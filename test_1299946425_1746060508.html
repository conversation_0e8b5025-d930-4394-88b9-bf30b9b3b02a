<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Smart Test Interface</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        .question-nav{position:fixed;top:70px;right:0;height:calc(100vh - 70px);width:320px;transition:all .3s;z-index:100;display:flex;flex-direction:column}
        .question-content{margin-right:0;transition:all .3s}
        .question-box{width:40px;height:40px;margin:5px;cursor:pointer}
        .attempted{background-color:#3498db!important;color:white}
        .correct{background-color:#2ecc71!important;color:white}
        .incorrect{background-color:#e74c3c!important;color:white}
        .current{border:3px solid #f39c12!important}
        #question-boxes{overflow-y:auto;max-height:calc(100vh - 150px);padding-bottom:15px}
        @media (max-width:768px){.question-nav{width:100%;right:-100%}.question-content{margin-right:0}}
    </style>
</head>
<body>
    <div class="container-fluid p-0">
        <!-- Header with controls -->
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary sticky-top">
            <div class="container-fluid">
                <a class="navbar-brand" href="#">
                    <i class="fas fa-graduation-cap me-2"></i>Smart Test
                </a>
                <!-- Section selector -->
                <div class="navbar-nav me-auto section-tabs">
                    <select class="form-select" id="section-selector" onchange="switchSection(this.value)">
                        <option value="all">All Sections</option>
                    </select>
                </div>
                <div class="d-flex align-items-center">
                    <!-- Timer -->
                    <div class="bg-light text-primary rounded px-3 py-2 me-2 fw-bold" id="timer">
                        <i class="far fa-clock me-2"></i><span id="timer-display">100:00</span>
                    </div>
                    <!-- Submit button -->
                    <button class="btn btn-success me-2" id="submit-btn" onclick="submitTest()">
                        <i class="fas fa-check me-1"></i> Submit
                    </button>
                    <!-- Language toggle -->
                    <button class="btn btn-light me-2" id="lang-btn" onclick="toggleLanguage()">
                        <i class="fas fa-language"></i>
                    </button>
                    <!-- Menu toggle for mobile -->
                    <button class="btn btn-light d-lg-none" id="menu-toggle" onclick="toggleMenu()">
                        <i class="fas fa-th"></i>
                    </button>
                </div>
            </div>
        </nav>
        <div class="row m-0">
            <!-- Main content area -->
            <div class="col p-0 question-content" id="question-content">
                <div class="container py-4">
                    <!-- Question navigation -->
                    <div class="d-flex justify-content-between mb-4">
                        <button class="btn btn-outline-primary" onclick="prevQuestion()">
                            <i class="fas fa-chevron-left me-1"></i> Previous
                        </button>
                        <div class="fw-bold" id="question-counter">Question 1 of 100</div>
                        <button class="btn btn-outline-primary" onclick="nextQuestion()">
                            Next <i class="fas fa-chevron-right ms-1"></i>
                        </button>
                    </div>
                    <!-- Questions will be displayed here -->
                    <div id="questions-container"></div>
                </div>
            </div>
            <!-- Question navigation sidebar -->
            <div class="bg-light question-nav" id="question-nav">
                <div class="p-3">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h5 class="m-0">Questions</h5>
                        <div>
                            <span class="badge bg-secondary me-1" id="attempted-count">0</span>
                            <span class="badge bg-secondary" id="total-count">100</span>
                            <button class="btn btn-sm btn-outline-secondary ms-2 d-lg-none" onclick="toggleMenu()">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>
                    <div class="d-flex flex-wrap" id="question-boxes"></div>
                </div>
            </div>
        </div>
        <!-- Results modal -->
        <div class="modal fade" id="results-modal" tabindex="-1" aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content">
                    <div class="modal-header bg-primary text-white">
                        <h5 class="modal-title">Test Results</h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row text-center">
                            <div class="col-6 mb-3">
                                <div class="p-3 bg-primary bg-opacity-10 rounded">
                                    <h3 id="score-value">0</h3>
                                    <div class="small text-muted">Score</div>
                                </div>
                            </div>
                            <div class="col-6 mb-3">
                                <div class="p-3 bg-success bg-opacity-10 rounded">
                                    <h3 id="correct-value">0</h3>
                                    <div class="small text-muted">Correct</div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="p-3 bg-danger bg-opacity-10 rounded">
                                    <h3 id="incorrect-value">0</h3>
                                    <div class="small text-muted">Incorrect</div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="p-3 bg-secondary bg-opacity-10 rounded">
                                    <h3 id="unattempted-value">0</h3>
                                    <div class="small text-muted">Unattempted</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-primary" onclick="reviewTest()">Review Test</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 100 * 60;
        let resultsModal;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            resultsModal = new bootstrap.Modal(document.getElementById('results-modal'));
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 768) {
                document.getElementById('question-content').style.marginRight = '320px';
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["6"] = {
                name: "Reasoning",
                start: 0,
                end: 23
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="6">Reasoning</option>`;
            sections["18"] = {
                name: "General Awareness",
                start: 24,
                end: 48
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="18">General Awareness</option>`;
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 49,
                end: 73
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["7"] = {
                name: "English",
                start: 74,
                end: 98
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="7">English</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 99,
                end: 99
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "6",
                    question_en: "<p>1. In a certain code language. &lsquo;JAMB&rsquo; is written as &lsquo;SBPA&rsquo; and &lsquo;BOAT&rsquo; is written as &lsquo;ANBI&rsquo;. How will &lsquo;JERK&rsquo; be written in that language ?</p>",
                    question_hi: "<p>1. एक निश्चित कूट भाषा में, \'JAMB\' को \'SBPA\' और \'BOAT\' को \'ANBI\' लिखा जाता है। उसी भाषा में \'JERK\' को किस प्रकार लिखा जाएगा ?</p>",
                    options_en: [
                        "<p>SXRK</p>",
                        "<p>SKRX</p>",
                        "<p>SRKX</p>",
                        "<p>SXKR</p>"
                    ],
                    options_hi: [
                        "<p>SXRK</p>",
                        "<p>SKRX</p>",
                        "<p>SRKX</p>",
                        "<p>SXKR</p>"
                    ],
                    solution_en: "<p>1.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742361731996.png\" alt=\"rId5\" width=\"78\" height=\"159\"><br>Similarly,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742361732108.png\" alt=\"rId6\" width=\"85\" height=\"166\"></p>",
                    solution_hi: "<p>1.(d)</p>\n<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742361731996.png\" alt=\"rId5\" width=\"78\" height=\"159\"><br>इसी प्रकार,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742361732108.png\" alt=\"rId6\" width=\"85\" height=\"166\"></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "6",
                    question_en: "<p>2. Select the correct mirror image of the given figure when the mirror is placed at MN.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742361732254.png\" alt=\"rId7\" width=\"120\" height=\"129\"></p>",
                    question_hi: "<p>2. दर्पण को MN पर रखे जाने पर दी गई आकृति का दर्पण में नि&zwj;र्मित सही प्रतिबिंब का चयन करें। <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742361732254.png\" alt=\"rId7\" width=\"120\" height=\"129\"></p>",
                    options_en: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742361732407.png\" alt=\"rId8\" width=\"74\" height=\"80\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742361732607.png\" alt=\"rId9\" width=\"75\" height=\"72\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742361732704.png\" alt=\"rId10\" width=\"76\" height=\"73\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742361732800.png\" alt=\"rId11\" width=\"75\" height=\"78\"></p>"
                    ],
                    options_hi: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742361732407.png\" alt=\"rId8\" width=\"73\" height=\"79\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742361732607.png\" alt=\"rId9\" width=\"76\" height=\"73\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742361732704.png\" alt=\"rId10\" width=\"76\" height=\"73\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742361732800.png\" alt=\"rId11\" width=\"75\" height=\"78\"></p>"
                    ],
                    solution_en: "<p>2.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742361732407.png\" alt=\"rId8\" width=\"76\" height=\"83\"></p>",
                    solution_hi: "<p>2.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742361732407.png\" alt=\"rId8\" width=\"76\" height=\"83\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "6",
                    question_en: "<p>3. Three of the following number-pairs are alike in some manner and hence form a group. Which number-pair does not belong to that group? <br>(NOTE : Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g. 13-Operations on 13 such as adding/subtracting/multiplying etc. to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed.)</p>",
                    question_hi: "<p>3. निम्नलिखित संख्या-युग्मों में से तीन किसी तरह से समान हैं और इसलिए एक समूह बनाते हैं। कौन-सा संख्या-युग्म उस समूह से संबंधित नहीं है? <br>(नोट : संख्याओं को उसके घटक अंकों में तोड़े बिना, पूर्ण संख्याओं पर गणितीय संक्रियाएं की जानी चाहिए। उदाहरण के लिए 13 - 13 पर संक्रियाएं जैसे कि 13 में जोड़ना/घटाना/गुणा करना आदि की जा सकती हैं। 13 को 1 और 3 में तोड़ने और फिर 1 और 3 पर गणितीय संक्रियाएँ करने की अनुमति नहीं है।)</p>",
                    options_en: [
                        "<p>12 - 225</p>",
                        "<p>8 - 100</p>",
                        "<p>9 - 121</p>",
                        "<p>6 - 64</p>"
                    ],
                    options_hi: [
                        "<p>12 - 225</p>",
                        "<p>8 - 100</p>",
                        "<p>9 - 121</p>",
                        "<p>6 - 64</p>"
                    ],
                    solution_en: "<p>3.(a)<br><strong>Logic:-&nbsp;</strong><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><msup><mn>1</mn><mrow><mi>s</mi><mi>t</mi></mrow></msup><mi>n</mi><mi>o</mi><mo>.</mo><mo>+</mo><mn>2</mn><mo>)</mo></mrow><mn>2</mn></msup><mo>&#160;</mo></math>= 3<sup>rd</sup> no.<br>(8 : 100):- (8 + 2<math display=\"inline\"><msup><mrow><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup></math> &rArr; (10)<sup>2 </sup>= 100<br>(9 : 121):- (9 + 2<math display=\"inline\"><msup><mrow><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup></math> &rArr; (11)<sup>2 </sup>= 121<br>(6 : 64):- (6 + 2<math display=\"inline\"><msup><mrow><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup></math> &rArr; (8)<sup>2 </sup>= 64<br>but<br>(12 : 225):- (12 + 2<math display=\"inline\"><msup><mrow><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup></math> &rArr; (14)<sup>2</sup>= 196 &ne; 225</p>",
                    solution_hi: "<p>3.(a)<br>तर्क :- ( पहली संख्या <math display=\"inline\"><mo>+</mo></math> 2)<sup>2</sup> = तीसरी संख्या<br>(8 : 100):- (8 + 2<math display=\"inline\"><msup><mrow><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup></math> &rArr; (10)<sup>2 </sup>= 100<br>(9 : 121):- (9 + 2<math display=\"inline\"><msup><mrow><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup></math> &rArr; (11)<sup>2 </sup>= 121<br>(6 : 64):- (6 + 2<math display=\"inline\"><msup><mrow><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup></math> &rArr; (8)<sup>2 </sup>= 64<br>लेकिन <br>(12 : 225):- (12 + 2<math display=\"inline\"><msup><mrow><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup></math> &rArr; (14)<sup>2</sup>= 196 &ne; 225</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "6",
                    question_en: "<p>4. In a certain code,&lsquo;curtain door window&rsquo; is coded as &lsquo;la ph ba&rsquo;,&lsquo;lock window key&rsquo; is coded as &lsquo;ep zm la&rsquo;,&lsquo;key curtain handle&rsquo; is coded as &lsquo;fd ba zm&rsquo;. What is the code for &lsquo;curtain&rsquo; in that language?(Note: All the codes are two-letter coded only.)</p>",
                    question_hi: "<p>4. एक निश्चित कूट में, \'curtain door window\' को \'la ph ba\' के रूप में कूटबद्ध किया जाता है, \'lock window key\' को \'ep zm la\' के रूप में कूटबद्ध किया जाता है, \'key curtain handle\' को \'fd ba zm\' के रूप में कूटबद्ध किया जाता है। उस भाषा में \'curtain\' के लिए कूट क्या है? (नोट: सभी कूट केवल दो-अक्षर वाले कूट हैं।)</p>",
                    options_en: [
                        "<p>zm</p>",
                        "<p>la</p>",
                        "<p>ep</p>",
                        "<p>ba</p>"
                    ],
                    options_hi: [
                        "<p>zm</p>",
                        "<p>la</p>",
                        "<p>ep</p>",
                        "<p>ba</p>"
                    ],
                    solution_en: "<p>4.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742361733039.png\" alt=\"rId12\" width=\"358\" height=\"186\"><br>The code of &lsquo;curtain&rsquo; = &lsquo;ba&rsquo;.</p>",
                    solution_hi: "<p>4.(d) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742361733039.png\" alt=\"rId12\" width=\"358\" height=\"186\"><br>\'curtain\' का कोड = \'ba\'.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "6",
                    question_en: "<p>5. Select the option that represents the letters that, when placed from left to right in the blanks below, will complete the letter-series.<br>a_mx_q_yc_o_</p>",
                    question_hi: "<p>5. उस विकल्प का चयन करें, जो उन अक्षरों को दर्शाता है, जिन्हें निम्&zwj;न रिक्त स्थानों में बाएँ से दाएँ रखने<br>पर अक्षर-शृंखला पूर्ण हो जाएगी।<br>a_mx_q_yc_o_</p>",
                    options_en: [
                        "<p>pnbrz</p>",
                        "<p>pbnrz</p>",
                        "<p>prnbx</p>",
                        "<p>nbprz</p>"
                    ],
                    options_hi: [
                        "<p>pnbrz</p>",
                        "<p>pbnrz</p>",
                        "<p>prnbx</p>",
                        "<p>nbprz</p>"
                    ],
                    solution_en: "<p>5.(b) a<span style=\"text-decoration: underline;\"><strong>p</strong></span>mx / <span style=\"text-decoration: underline;\"><strong>b</strong></span>q<span style=\"text-decoration: underline;\"><strong>n</strong></span>y / c<span style=\"text-decoration: underline;\"><strong>r</strong></span>o<span style=\"text-decoration: underline;\"><strong>z</strong></span><br><strong>Logic :-</strong> Letters are increasing by +1.</p>",
                    solution_hi: "<p>5.(b) a<span style=\"text-decoration: underline;\"><strong>p</strong></span>mx / <span style=\"text-decoration: underline;\"><strong>b</strong></span>q<span style=\"text-decoration: underline;\"><strong>n</strong></span>y / c<span style=\"text-decoration: underline;\"><strong>r</strong></span>o<span style=\"text-decoration: underline;\"><strong>z</strong></span><br><strong>तर्क :- </strong>अक्षर +1 से बढ़ रहे हैं।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "6",
                    question_en: "<p>6. How many rectangles are there in the following figure?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742361733180.png\" alt=\"rId13\" width=\"159\" height=\"109\"></p>",
                    question_hi: "<p>6. दी गई आकृति में कितने आयत हैं?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742361733180.png\" alt=\"rId13\" width=\"159\" height=\"109\"></p>",
                    options_en: [
                        "<p>9</p>",
                        "<p>11</p>",
                        "<p>10</p>",
                        "<p>8</p>"
                    ],
                    options_hi: [
                        "<p>9</p>",
                        "<p>11</p>",
                        "<p>10</p>",
                        "<p>8</p>"
                    ],
                    solution_en: "<p>6.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742361733458.png\" alt=\"rId15\" width=\"167\" height=\"112\"><br>There are 9 rectangle <br>ABCD, BEFC, AEFD, BMHG, MLIH, LKJI, BLIG, MKJH, BKJG</p>",
                    solution_hi: "<p>6.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742361733458.png\" alt=\"rId15\" width=\"167\" height=\"112\"><br>यहाँ 9 आयत हैं<br>ABCD, BEFC, AEFD, BMHG, MLIH, LKJI, BLIG, MKJH, BKJG</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "6",
                    question_en: "<p>7. Select the option that is related to the fifth letter-cluster in the same way as the second letter-cluster is related to the first letter-cluster and the fourth letter-cluster is related to the third letter-cluster.<br>WEL : DVO :: FOR : ULI :: SDX : ?</p>",
                    question_hi: "<p>7. उस विकल्प का चयन करें जो पाँचवें अक्षर-समूह से उसी प्रकार संबंधित है जिस प्रकार दूसरा अक्षर-समूह पहले अक्षर-समूह से संबंधित है और चौथा अक्षर-समूह तीसरे अक्षर-समूह से संबंधित है। <br>WEL : DVO :: FOR : ULI :: SDX : ?</p>",
                    options_en: [
                        "<p>HWC</p>",
                        "<p>GWC</p>",
                        "<p>HXC</p>",
                        "<p>GXC</p>"
                    ],
                    options_hi: [
                        "<p>HWC</p>",
                        "<p>GWC</p>",
                        "<p>HXC</p>",
                        "<p>GXC</p>"
                    ],
                    solution_en: "<p>7.(a) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742361733561.png\" alt=\"rId16\" width=\"135\" height=\"94\">, <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742361733691.png\" alt=\"rId17\" width=\"152\" height=\"93\"></p>\n<p>and<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742361733789.png\" alt=\"rId18\" width=\"156\" height=\"107\"></p>",
                    solution_hi: "<p>7.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742361733916.png\" alt=\"rId19\" width=\"116\" height=\"79\">, <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742361734067.png\" alt=\"rId20\" width=\"135\" height=\"76\"></p>\n<p>और<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742361734176.png\" alt=\"rId21\" width=\"148\" height=\"95\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "6",
                    question_en: "<p>8. In this question, three statements are given, followed by three conclusions numbered<br>I, II and III. Assuming the statements to be true, even if they seem to be at variance with commonly known facts, decide which of the conclusion(s) logically follows/follow from the statements.<br><strong>Statements :</strong><br>No copper is silver.<br>All gold is copper.<br>All aluminium is gold.<br><strong>Conclusions :</strong><br>I. No aluminium is silver.<br>II. Some gold is silvers<br>III. All aluminium is copper.</p>",
                    question_hi: "<p>8. इस प्रश्न में, तीन कथन और उसके बाद तीन निष्कर्ष I, II और III दिए गए हैं। कथनों को सत्य मानते हुए,<br>भले ही वे सर्वज्ञात तथ्यों से भिन्न प्रतीत होते हों, निर्णय करें कि कौन सा/से निष्कर्ष कथनों का तार्किक रूप से अनुसरण करता है/अनुसरण करते हैं।<br><strong>कथन :</strong><br>कोई तांबा, चांदी नहीं है।<br>सभी सोना, तांबा है।<br>सभी एल्युमिनियम, सोना है।<br><strong>निष्कर्ष :</strong><br>I. कोई एल्युमीनियम, चांदी नहीं है।<br>II. कुछ सोना, चांदी है।<br>III. सभी एल्युमीनियम, तांबा है।</p>",
                    options_en: [
                        "<p>Only conclusion II follows.</p>",
                        "<p>Only conclusion I follows.</p>",
                        "<p>Both conclusions I and III follow.</p>",
                        "<p>Both conclusions I and II follow.</p>"
                    ],
                    options_hi: [
                        "<p>केवल निष्कर्ष II अनुसरण करता है।</p>",
                        "<p>केवल निष्कर्ष I अनुसरण करता है।</p>",
                        "<p>निष्कर्ष I और III दोनों अनुसरण करते हैं।</p>",
                        "<p>निष्कर्ष I और II दोनों अनुसरण करते हैं।</p>"
                    ],
                    solution_en: "<p>8.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742361734476.png\" alt=\"rId22\" width=\"335\" height=\"105\"><br>Both conclusion I and III follows.</p>",
                    solution_hi: "<p>8.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742361734831.png\" alt=\"rId23\" width=\"306\" height=\"96\"><br>निष्कर्ष I और III दोनों अनुसरण करते हैं।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "6",
                    question_en: "<p>9. Two sets of numbers are given below. In each set of numbers, certain mathematical operation(s) on the first number result(s) in the second number. Similarly, certain mathematical operation(s) on the second number result(s) in the third number and so on. Which of the given options follows the same set of operations as in the given sets?<br>(NOTE: Operations should be performed on the whole numbers, without breaking down the numbers into their constituent digits. E.g. 13 - Operations on 13 such as adding/subtracting/multiplying to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed.)<br>8 - 33 - 66 - 81 ; 11 - 36 - 72 - 87</p>",
                    question_hi: "<p>9. संख्याओं के दो सेट नीचे दिए गए हैं। संख्याओं के प्रत्येक सेट में, पहले नंबर पर कुछ गणितीय संक्रिया(ओं) का परिणाम दूसरे नंबर पर होता है। इसी प्रकार, दूसरे नंबर पर कुछ गणितीय संक्रिया(ओं) का परिणाम तीसरे नंबर पर होता है, और इसी प्रकार आगे भी होता है। दिए गए विकल्पों में से कौन-सा विकल्प दिए गए सेटों की तरह समान संक्रियाओं वाले सेट का अनुसरण करता है?<br>(नोट: संख्याओं को उनके घटक अंकों में तोड़े बिना, ये संक्रियाएँ संपूर्ण संख्याओं पर की जानी चाहिए। उदाहरण के लिए 13 को लीजिए - 13 पर संक्रियाएँ जैसे कि 13 में जोड़ना/घटाना/गुणा करना, की जा सकती हैं। 13 को 1 और 3 में तोड़ना और फिर 1 और 3 पर गणितीय संक्रियाएँ करने की अनुमति नहीं है।)<br>8 - 33 - 66 - 81 ; 11 - 36 - 72 - 87</p>",
                    options_en: [
                        "<p>21 &ndash; 46 &ndash; 82 &ndash; 107</p>",
                        "<p>15 &ndash; 40 &ndash; 80 &ndash; 95</p>",
                        "<p>18 &ndash; 43 &ndash; 86 &ndash; 100</p>",
                        "<p>6 &ndash; 31 &ndash; 62 &ndash; 97</p>"
                    ],
                    options_hi: [
                        "<p>21 &ndash; 46 &ndash; 82 &ndash; 107</p>",
                        "<p>15 &ndash; 40 &ndash; 80 &ndash; 95</p>",
                        "<p>18 &ndash; 43 &ndash; 86 &ndash; 100</p>",
                        "<p>6 &ndash; 31 &ndash; 62 &ndash; 97</p>"
                    ],
                    solution_en: "<p>9.(b)<br>Logic;- <math display=\"inline\"><msup><mrow><mn>1</mn></mrow><mrow><mi mathvariant=\"bold-italic\">s</mi><mi mathvariant=\"bold-italic\">t</mi></mrow></msup><mi mathvariant=\"bold-italic\">n</mi><mi mathvariant=\"bold-italic\">o</mi><mo>.</mo></math> + <strong>25 </strong>= 2<sup>nd</sup> no. , 2<sup>nd</sup> no. &times; <strong>2</strong> = 3<sup>rd</sup> no., 3<sup>rd</sup> no. + <strong>15</strong> = 4thno.&nbsp;<br>(8 - 33 - 66 - 81) :- <strong>8</strong> + 25 = <strong>33,</strong> 33 <math display=\"inline\"><mo>&#215;</mo></math> 2 = <strong>66,</strong> 66 + 15 = <strong>81</strong><br>(11 - 36 - 72 - 87) :- <strong>11</strong> + 25 = <strong>36,</strong> 36 <math display=\"inline\"><mo>&#215;</mo></math> 2 = <strong>72,</strong> 72 + 15 = <strong>87</strong><br>Similarly<br>(15 - 40 - 80 - 95) :- <strong>15</strong> + 25 = <strong>40,</strong> 40 <math display=\"inline\"><mo>&#215;</mo></math> 2 = <strong>80,</strong> 80 + 15 = <strong>95</strong></p>",
                    solution_hi: "<p>9.(b)<br><strong>तर्क;- </strong>पहली संख्या + 25 = दूसरी संख्या , दूसरी संख्या <math display=\"inline\"><mo>&#215;</mo></math> 2 = तीसरी संख्या , तीसरी संख्या + 15 = चौथी संख्या <br>(8 - 33 - 66 - 81) :- <strong>8</strong> + 25 = <strong>33,</strong> 33 <math display=\"inline\"><mo>&#215;</mo></math> 2 = <strong>66,</strong> 66 + 15 = <strong>81</strong><br>(11 - 36 - 72 - 87) :- <strong>11</strong> + 25 = <strong>36,</strong> 36 <math display=\"inline\"><mo>&#215;</mo></math> 2 = <strong>72,</strong> 72 + 15 = <strong>87</strong><br>इसी प्रकार <br>(15 - 40 - 80 - 95) :- <strong>15</strong> + 25 = <strong>40,</strong> 40 <math display=\"inline\"><mo>&#215;</mo></math> 2 = <strong>80,</strong> 80 + 15 = <strong>95</strong></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "6",
                    question_en: "<p>10. In a certain code language,<br>A % B means &lsquo;A is the mother of B&rsquo;<br>A # B means &lsquo;A is the brother of B&rsquo;<br>A @ B means &lsquo;A is the wife of B&rsquo;<br>A &amp; B means &lsquo;A is the father of B&rsquo;<br>Based on the above, how is P related to T if &lsquo;P &amp; Q % R # S @ T&rsquo;?</p>",
                    question_hi: "<p>10. एक निश्चित कूट भाषा में<br>A % B का अर्थ A, B की मां है<br>A # B का अर्थ A, B का भाई है<br>A @ B का अर्थ A, B की पत्नी है<br>A &amp; B का अर्थ A, B का पिता है<br>उपरोक्त केआधार पर, यदि P &amp; Q % R # S @ T है, तो P का T से क्या संबंध है?</p>",
                    options_en: [
                        "<p>Wife&rsquo;s father</p>",
                        "<p>Wife&rsquo;s mother&rsquo;s father</p>",
                        "<p>Wife&rsquo;s mother&rsquo;s brother</p>",
                        "<p>Wife&rsquo;s brother</p>"
                    ],
                    options_hi: [
                        "<p>पत्नी के पिता</p>",
                        "<p>पत्नी की माँ के पिता</p>",
                        "<p>पत्नी की माँ का भाई</p>",
                        "<p>पत्नी का भाई</p>"
                    ],
                    solution_en: "<p>10.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742361735126.png\" alt=\"rId24\" width=\"227\" height=\"158\"><br>Hence, P is T&rsquo;s wife&rsquo;s mother&rsquo;s father.</p>",
                    solution_hi: "<p>10.(b) <br><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742361735126.png\" alt=\"rId24\" width=\"227\" height=\"158\"><br>अतः, P, T की पत्नी की माँ का पिता है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "6",
                    question_en: "<p>11. Select the Venn diagram that best illustrates the relationship between the following classes.<br>Potatoes, Vegetables, Grapes</p>",
                    question_hi: "<p>11. उस वेन-आरेख का चयन कीजिए, जो निम्नलिखित वर्गों के बीच के संबंध को सर्वोत्तम रूप से दर्शाता है।<br>आलू, सब्जियाँ, अंगूर</p>",
                    options_en: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742361735258.png\" alt=\"rId25\" width=\"84\" height=\"79\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742361735373.png\" alt=\"rId26\" width=\"137\" height=\"70\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742361735481.png\" alt=\"rId27\" width=\"166\" height=\"62\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742361735568.png\" alt=\"rId28\" width=\"103\" height=\"97\"></p>"
                    ],
                    options_hi: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742361735258.png\" alt=\"rId25\" width=\"104\" height=\"98\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742361735373.png\" alt=\"rId26\" width=\"150\" height=\"77\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742361735481.png\" alt=\"rId27\" width=\"174\" height=\"65\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742361735568.png\" alt=\"rId28\" width=\"108\" height=\"103\"></p>"
                    ],
                    solution_en: "<p>11.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742361735688.png\" alt=\"rId29\" width=\"235\" height=\"140\"></p>",
                    solution_hi: "<p>11.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742361735799.png\" alt=\"rId30\" width=\"218\" height=\"127\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "6",
                    question_en: "<p>12. Three of the following four are alike in a certain way and thus form a group. Which is&nbsp;the one that does NOT belong to that group?<br>(Note : The odd one out is not based on the number of consonants/vowels or their<br>position in the letter cluster.)</p>",
                    question_hi: "<p>12. निम्नलिखित चार अक्षर-समूहों में से तीन अक्षर-समूह एक निश्चित प्रकार से समान हैं और इस प्रकार एक&nbsp;समूह बनाते हैं। वह कौन-सा अक्षर-समूह है जो उस समूह से संबंधित नहीं है?<br>(ध्यान दें : असंगत अक्षर-समूह, उस अक्षर-समूह में व्यंजनों/स्वरों की संख्या या उनके स्थान पर आधारित<br>नहीं है।)</p>",
                    options_en: [
                        "<p>QWD</p>",
                        "<p>TZD</p>",
                        "<p>EKR</p>",
                        "<p>RXE</p>"
                    ],
                    options_hi: [
                        "<p>QWD</p>",
                        "<p>TZD</p>",
                        "<p>EKR</p>",
                        "<p>RXE</p>"
                    ],
                    solution_en: "<p>12.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742361736020.png\" alt=\"rId31\" width=\"162\" height=\"94\">,<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742361736165.png\" alt=\"rId32\" width=\"162\" height=\"95\">,<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742361736322.png\" alt=\"rId33\" width=\"160\" height=\"94\"><br>But, <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742361736507.png\" alt=\"rId34\" width=\"158\" height=\"92\"></p>",
                    solution_hi: "<p>12.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742361736020.png\" alt=\"rId31\" width=\"162\" height=\"94\">,<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742361736165.png\" alt=\"rId32\" width=\"162\" height=\"95\">,<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742361736322.png\" alt=\"rId33\" width=\"160\" height=\"94\"><br>लेकिन, <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742361736507.png\" alt=\"rId34\" width=\"158\" height=\"92\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "6",
                    question_en: "<p>13. Six words Hand, Leg, Eyes, Nose, Mouth and Ear are written on different faces of a dice. Three positions of this dice are shown in the figure. Find the word on the face opposite to Hand.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742361736722.png\" alt=\"rId35\" width=\"266\" height=\"85\"></p>",
                    question_hi: "<p>13. एक पासे के विभिन्न फलकों पर छ: शब्द हाथ (Hand), पैर (Leg), आँख (Eye), नाक (Nose), मुँह (Mouth) और कान (Ear) लिखे हुए हैं। नीचे चित्र में इस पासे की तीन स्थितियां दिखाई गई है। हाथ (Hand) शब्द के विपरीत फलक पर कौन-सा शब्द है?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742361736722.png\" alt=\"rId35\" width=\"266\" height=\"85\"></p>",
                    options_en: [
                        "<p>Leg</p>",
                        "<p>Nose</p>",
                        "<p>Mouth</p>",
                        "<p>Ear</p>"
                    ],
                    options_hi: [
                        "<p>पैर (Leg)</p>",
                        "<p>नाक (Nose)</p>",
                        "<p>मुँह (Mouth)</p>",
                        "<p>कान (Ear)</p>"
                    ],
                    solution_en: "<p>13.(c) from the dice (ii) and (iii), &lsquo;Nose&rsquo; and &lsquo;Leg&rsquo; is common.<br>So, the opposite face of &lsquo;Hand&rsquo; is &lsquo;Mouth&rsquo;.</p>",
                    solution_hi: "<p>13.(c) पासे (ii) और (iii) से, \'नाक\' और \'पैर\' उभयनिष्ठ है।<br>तो, \'हाथ\' का विपरीत फलक \'मुंह\' है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "6",
                    question_en: "<p>14. Select the set in which the numbers are related in the same way as are the numbers of the following sets.<br>(NOTE : Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g. 13- Operations on 13 such as adding /deleting /multiplying etc. to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed)<br>(288, 144, 24)<br>(432, 216, 36)</p>",
                    question_hi: "<p>14. उस समुच्चय का चयन करें जिसकी संख्याएं उसी तरह से संबंधित हैं जिस तरह निम्नलिखित समुच्चयों की संख्याएं संबंधित हैं।<br>(नोट : संख्याओं को उसके घटक अंकों में तोड़े बिना, पूर्ण संख्याओं पर संक्रियाएं की जानी चाहिए। उदाहरण के लिए संख्या 13 को लें - 13 पर संक्रियाएं, जैसे कि 13 में जोड़ना/घटाना/गुणा करना आदि, की जा सकती हैं। 13 को 1 और 3 में तोड़ने और फिर 1 और 3 पर गणितीय संक्रियाएं करने की अनुमति नहीं है)<br>(288, 144, 24)<br>(432, 216, 36)</p>",
                    options_en: [
                        "<p>(504, 252, 44)</p>",
                        "<p>(504, 250, 42)</p>",
                        "<p>(504, 252, 42)</p>",
                        "<p>(506, 252, 42)</p>"
                    ],
                    options_hi: [
                        "<p>(504, 252, 44)</p>",
                        "<p>(504, 250, 42)</p>",
                        "<p>(504, 252, 42)</p>",
                        "<p>(506, 252, 42)</p>"
                    ],
                    solution_en: "<p>14.(c) <strong>Logic :-</strong> (1st number - 2nd number) <math display=\"inline\"><mo>&#247;</mo></math> 6 = 3rd number<br>(288 , 144, 24) :- (288 - 144) <math display=\"inline\"><mo>&#247;</mo></math> 6 &rArr; (144) &divide; 6 = 24<br>(432, 216 , 36) :- (432 - 216) <math display=\"inline\"><mo>&#247;</mo></math> 6 &rArr; (216) &divide; 6 = 36<br>Similarly,<br>(504 , 252, 42) :- (504 - 252)<math display=\"inline\"><mo>&#247;</mo></math> 6 &rArr; (252) &divide; 6 = 42</p>",
                    solution_hi: "<p>14.(c)<strong> तर्क:-</strong> (पहली संख्या - दूसरी संख्या) <math display=\"inline\"><mo>&#247;</mo></math> 6 = तीसरी संख्या<br>(288 , 144, 24) :- (288 - 144) <math display=\"inline\"><mo>&#247;</mo></math> 6 &rArr; (144) &divide; 6 = 24<br>(432, 216 , 36) :- (432 - 216) <math display=\"inline\"><mo>&#247;</mo></math> 6 &rArr; (216) &divide; 6 = 36<br>इसी प्रकार,<br>(504 , 252, 42) :- (504 - 252)<math display=\"inline\"><mo>&#247;</mo></math> 6 &rArr; (252) &divide; 6 = 42</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "6",
                    question_en: "<p>15. I, J, K, L, M, N and O are sitting around a circular table facing the centre (but not necessarily in the same order). J sits fourth to the right of I. K sits to the immediate right of J. L sits to the immediate right of I. N sits fourth to the left of M. M is not an immediate neighbour of K. How many people sit between O and N when counted from the right of O ?</p>",
                    question_hi: "<p>15. I, J, K, L, M, N और O एक वृत्ताकार मेज के चारों ओर केंद्र की ओर मुख करके बैठे हैं (परंतु जरूरी नहीं कि वे इसी क्रम में बैठे हों)। J, I के दाएँ से चौथे स्थान पर बैठा है। K, J के ठीक दाएँ बैठा है। L, I के ठीक दाएँ बैठा है। N, M के बाएँ से चौथे स्थान पर बैठा है। M, K का निकटतम पड़ोसी नहीं है। O के दाएँ से गिनने पर O और N के बीच में कितने व्यक्ति बैठे हैं?</p>",
                    options_en: [
                        "<p>2</p>",
                        "<p>4</p>",
                        "<p>5</p>",
                        "<p>3</p>"
                    ],
                    options_hi: [
                        "<p>2</p>",
                        "<p>4</p>",
                        "<p>5</p>",
                        "<p>3</p>"
                    ],
                    solution_en: "<p>15.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742361736976.png\" alt=\"rId37\" width=\"126\" height=\"118\"><br>Three people sit between O and N when counted from the right of O.</p>",
                    solution_hi: "<p>15.(d) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742361736976.png\" alt=\"rId37\" width=\"126\" height=\"118\"><br>O के दाईं ओर से गिनती करने पर O और N के बीच तीन व्यक्ति बैठे हैं।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "6",
                    question_en: "<p>16. Anil starts from point A and drives 8 km towards North. He then takes a right turn, drives 2 km, turns right and drives 11 km. He then takes a right turn and drives 5 km. He takes a final right turn, drives 3 km and stops at point P. How far (shortest distance) and towards which direction should he drive in order to reach point A again? (All turns are 90 degrees turns only unless specified.)</p>",
                    question_hi: "<p>16. अनिल स्&zwj;थान A से आरंभ करके उत्तर की ओर 8 km ड्राइव करता है। फिर वह दाएं मुड़ता है, 2 km ड्राइव करता है, फिर दाएं मुड़ता है और 11 km ड्राइव करता है। फिर वह दाएं मुड़ता है और 5 km ड्राइव करता है। वह अंतिम बार दाएं मुड़ता है, 3 km ड्राइव करता है और स्&zwj;थान P पर रुकता है। स्&zwj;थान A पर फिर से पहुँचने के लिए उसे कितनी दूर (न्यूनतम दूरी) और किस दिशा की ओर ड्राइव करना चाहिए? (सभी मोड़ 90 डिग्री के मोड़ हैं जब तक कि निर्दिष्ट न किए गए हों।)</p>",
                    options_en: [
                        "<p>3 km towards North</p>",
                        "<p>3 km towards East</p>",
                        "<p>2 km towards West</p>",
                        "<p>2 km towards East</p>"
                    ],
                    options_hi: [
                        "<p>3 km उत्तर की ओर</p>",
                        "<p>3 km पूर्व की ओर</p>",
                        "<p>2 km पश्चिम की ओर</p>",
                        "<p>2 km पूर्व की ओर</p>"
                    ],
                    solution_en: "<p>16.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742361737124.png\" alt=\"rId38\" width=\"196\" height=\"161\"><br>He should drive 3km towards the east.</p>",
                    solution_hi: "<p>16.(b) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742361737124.png\" alt=\"rId38\" width=\"196\" height=\"161\"><br>उसे 3 km पूर्व की दिशा की ओर ड्राइव करना चाहिए</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "6",
                    question_en: "<p>17. What will come in the place of the question mark (?) in the following equation, if &lsquo;+&rsquo;and \'-\' are interchanged and &lsquo;&times;&rsquo; and &lsquo;<math display=\"inline\"><mo>&#247;</mo></math>&rsquo; are interchanged?<br>61 + 80 - 48 &times; 3 <math display=\"inline\"><mo>&#247;</mo></math> 5 = ?</p>",
                    question_hi: "<p>17. यदि \'+\' और &lsquo;-&rsquo; को आपस में बदल दिया जाए तथा &lsquo;&times;&rsquo; और &lsquo;<math display=\"inline\"><mo>&#247;</mo></math>&rsquo; को आपस में बदल दिया जाए तो निम्नलिखित समीकरण में प्रश्न चिन्ह (?) के स्थान पर क्या आएगा?<br>61 + 80 - 48 &times; 3 <math display=\"inline\"><mo>&#247;</mo></math> 5=?</p>",
                    options_en: [
                        "<p>61</p>",
                        "<p>63</p>",
                        "<p>60</p>",
                        "<p>62</p>"
                    ],
                    options_hi: [
                        "<p>61</p>",
                        "<p>63</p>",
                        "<p>60</p>",
                        "<p>62</p>"
                    ],
                    solution_en: "<p>17.(a) <strong>Given :- </strong>61 + 80 - 48 &times; 3 <math display=\"inline\"><mo>&#247;</mo></math> 5<br>As per given instruction after interchanging &lsquo;+&rsquo; and &lsquo;-&rsquo; and &lsquo;&times;&rsquo; and &lsquo;<math display=\"inline\"><mo>&#247;</mo></math>&rsquo; we get<br>61 - 80 + 48 <math display=\"inline\"><mo>&#247;</mo></math> 3 &times; 5<br>61 - 80 + 16&times;5<br>-19 + 80 = 61</p>",
                    solution_hi: "<p>17.(a) <strong>दिया गया :- </strong>61 + 80 - 48 &times; 3 <math display=\"inline\"><mo>&#247;</mo></math> 5<br>दिए गए निर्देश के अनुसार \'+\' और \'-\' तथा \'&times;\' और \'<math display=\"inline\"><mo>&#247;</mo></math>\' को आपस में बदलने पर हमें प्राप्त होता है<br>61 - 80 + 48 <math display=\"inline\"><mo>&#247;</mo></math> 3 &times; 5<br>61 - 80 + 16&times;5<br>-19 + 80 = 61</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "6",
                    question_en: "<p>18. Select the option in which the given figure is embedded (rotation is not allowed).<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742361737227.png\" alt=\"rId39\" width=\"83\" height=\"75\"></p>",
                    question_hi: "<p>18. उस विकल्प का चयन कीजिए जिसमें दी गई आकृति निहित है (घूर्णन की अनुमति नहीं है)।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742361737227.png\" alt=\"rId39\" width=\"75\" height=\"68\"></p>",
                    options_en: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742361737341.png\" alt=\"rId40\" width=\"75\" height=\"64\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742361737463.png\" alt=\"rId41\" width=\"76\" height=\"70\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742361737550.png\" alt=\"rId42\" width=\"77\" height=\"68\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742361737744.png\" alt=\"rId43\" width=\"76\" height=\"68\"></p>"
                    ],
                    options_hi: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742361737341.png\" alt=\"rId40\" width=\"82\" height=\"70\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742361737463.png\" alt=\"rId41\" width=\"75\" height=\"69\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742361737550.png\" alt=\"rId42\" width=\"78\" height=\"69\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742361737744.png\" alt=\"rId43\" width=\"79\" height=\"71\"></p>"
                    ],
                    solution_en: "<p>18.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742361737894.png\" alt=\"rId44\" width=\"75\" height=\"65\"></p>",
                    solution_hi: "<p>18.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742361737894.png\" alt=\"rId44\" width=\"74\" height=\"64\"></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "6",
                    question_en: "<p>19. What should come in place of the question mark (?) in the following series?<br>97, 88, 85, 76, 73, ?, 61</p>",
                    question_hi: "<p>19. दी गई श्रृंखला में प्रश्नवाचक चिह्न (?) के स्थान पर क्या आना चाहिए?<br>97, 88, 85, 76, 73, ?, 61</p>",
                    options_en: [
                        "<p>72</p>",
                        "<p>64</p>",
                        "<p>65</p>",
                        "<p>70</p>"
                    ],
                    options_hi: [
                        "<p>72</p>",
                        "<p>64</p>",
                        "<p>65</p>",
                        "<p>70</p>"
                    ],
                    solution_en: "<p>19.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742361738036.png\" alt=\"rId45\" width=\"347\" height=\"55\"></p>",
                    solution_hi: "<p>19.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742361738036.png\" alt=\"rId45\" width=\"347\" height=\"55\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "6",
                    question_en: "<p>20. All of the letters in the word \'FROWNED\' are arranged in alphabetical order. How many letters are there in the English alphabetical series between the letter which is fourth from the left end and the one which is second from the right end in the new letter-cluster thus formed?</p>",
                    question_hi: "<p>20. शब्द \'FROWNED\' के सभी अक्षर वर्णानुक्रम में व्यवस्थित किये जाते हैं। इस प्रकार बने नए अक्षर-समूह में बाएं छोर से चौथे और दाएं छोर से दूसरे अक्षर के बीच अंग्रेजी वर्णमाला क्रम में कितने अक्षर हैं?</p>",
                    options_en: [
                        "<p>Five</p>",
                        "<p>Two</p>",
                        "<p>Three</p>",
                        "<p>Four</p>"
                    ],
                    options_hi: [
                        "<p>पाँच</p>",
                        "<p>दो</p>",
                        "<p>तीन</p>",
                        "<p>चार</p>"
                    ],
                    solution_en: "<p>20.(c) <strong>Given:-</strong> FROWNED<br>As per given instruction after arranging the letter in alphabetical order we get<br>DEFNORW. Fourth from the left is N and second to the right is R. Number of letters between N and R is 3.</p>",
                    solution_hi: "<p>20.(c) <strong>दिया गया:-</strong> FROWNED<br>दिए गए निर्देश के अनुसार अक्षर को वर्णमाला क्रम में व्यवस्थित करने के बाद हमें प्राप्त होता है<br>DEFNORW। बाएं से चौथा N है और दाएं से दूसरा R है। N और R के बीच अक्षरों की संख्या 3 है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "6",
                    question_en: "<p>21. The sum of the present ages of a father and son is 50 years. If after 5 years, the father&rsquo;s age will be 5 times the age of the son, then what was the father&rsquo;s age 5 years ago?</p>",
                    question_hi: "<p>21. एक पिता और बेटे की वर्तमान आयु का योग 50 वर्ष है। यदि 5 वर्ष बाद पिता की आयु बेटे की आयु की 5 गुना होगी, तो 5 वर्ष पहले पिता की आयु क्या थी?</p>",
                    options_en: [
                        "<p>40</p>",
                        "<p>43</p>",
                        "<p>47</p>",
                        "<p>36</p>"
                    ],
                    options_hi: [
                        "<p>40</p>",
                        "<p>43</p>",
                        "<p>47</p>",
                        "<p>36</p>"
                    ],
                    solution_en: "<p>21.(a) <br>Let father&rsquo;s present age = <math display=\"inline\"><mi>x</mi></math> year<br>Then son&rsquo;s age = (50 - <math display=\"inline\"><mi>x</mi></math>) years<br>According to question, after 5 years<br>5 (50 - <math display=\"inline\"><mi>x</mi></math> + 5) = (x + 5)<br>275 - 5<math display=\"inline\"><mi>x</mi></math> = x + 5<br>6<math display=\"inline\"><mi>x</mi></math> = 270 &rArr; x = 45 years<br>Father&rsquo;s age 5 years ago = 45 - 5 = 40 years</p>",
                    solution_hi: "<p>21.(a) <br>माना पिता की वर्तमान आयु = <math display=\"inline\"><mi>x</mi></math> वर्ष<br>तो पुत्र की वर्तमान आयु = (50 - <math display=\"inline\"><mi>x</mi></math>) वर्ष<br>प्रश्न के अनुसार, 5 वर्ष बाद <br>5 (50 - <math display=\"inline\"><mi>x</mi></math> + 5) = (x + 5)<br>275 - 5<math display=\"inline\"><mi>x</mi></math> = x + 5<br>6<math display=\"inline\"><mi>x</mi></math> = 270 &rArr; x = 45 वर्ष<br>5 वर्ष पहले पिता की आयु = 45 - 5 = 40 वर्ष</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "6",
                    question_en: "<p>22. Which figure should replace the question mark (?) if the following series were to be continued?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742361738255.png\" alt=\"rId46\" width=\"393\" height=\"80\"></p>",
                    question_hi: "<p>22. निम्नलिखित श्रृंखला को जारी रखने के लिए प्रश्न चिह्न (?) के स्थान पर कौन-सी आकृति आनी चाहिए? <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742361738255.png\" alt=\"rId46\" width=\"393\" height=\"80\"></p>",
                    options_en: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742361738446.png\" alt=\"rId47\" width=\"75\" height=\"72\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742361738566.png\" alt=\"rId48\" width=\"76\" height=\"73\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742361738698.png\" alt=\"rId49\" width=\"76\" height=\"73\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742361738844.png\" alt=\"rId50\" width=\"75\" height=\"72\"></p>"
                    ],
                    options_hi: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742361738446.png\" alt=\"rId47\" width=\"72\" height=\"69\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742361738566.png\" alt=\"rId48\" width=\"76\" height=\"73\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742361738698.png\" alt=\"rId49\" width=\"74\" height=\"71\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742361738844.png\" alt=\"rId50\" width=\"76\" height=\"73\"></p>"
                    ],
                    solution_en: "<p>22.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742361738961.png\" alt=\"rId51\" width=\"77\" height=\"74\"></p>",
                    solution_hi: "<p>22.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742361738961.png\" alt=\"rId51\" width=\"77\" height=\"74\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "23",
                    section: "6",
                    question_en: "<p>23. What should come in place of ? in the given series based on the English alphabetical order ?<br>DLT ZHP VDL ? NVD</p>",
                    question_hi: "<p>23. अंग्रेजी वर्णमाला क्रम के आधार पर दी गई श्रृंखला में &lsquo;?&rsquo; के स्थान पर क्या आना चाहिए ?<br>DLT ZHP VDL ? NVD</p>",
                    options_en: [
                        "<p>BLV</p>",
                        "<p>DNX</p>",
                        "<p>CMW</p>",
                        "<p>RZH</p>"
                    ],
                    options_hi: [
                        "<p>BLV</p>",
                        "<p>DNX</p>",
                        "<p>CMW</p>",
                        "<p>RZH</p>"
                    ],
                    solution_en: "<p>23.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742361739133.png\" alt=\"rId52\" width=\"406\" height=\"130\"></p>",
                    solution_hi: "<p>23.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742361739133.png\" alt=\"rId52\" width=\"406\" height=\"130\"></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "24",
                    section: "6",
                    question_en: "<p>24. If 26 November 2013 was Tuesday, then what was the day of the week on 29 November 2018?</p>",
                    question_hi: "<p>24. यदि 26 नवंबर 2013 को मंगलवार था, तो 29 नवंबर 2018 को सप्ताह का कौन-सा दिन रहा होगा?</p>",
                    options_en: [
                        "<p>Saturday</p>",
                        "<p>Friday</p>",
                        "<p>Wednesday</p>",
                        "<p>Thursday</p>"
                    ],
                    options_hi: [
                        "<p>शनिवार</p>",
                        "<p>शुक्रवार</p>",
                        "<p>बुधवार</p>",
                        "<p>गुरुवार</p>"
                    ],
                    solution_en: "<p>24.(d) 26 November 2013 was Tuesday. On moving to 2018 the number of odd days = <br>+1 + 1 + 2 + 1 + 1 = 6. We have reached till 26 November, but we have to reach till 29 November. <br>The number of days in between = 3. <br>Total number of days = 6 + 3 = 9. <br>On dividing 9 by 7, the remainder is 2 <math display=\"inline\"><mo>&#8658;</mo></math> Tuesday + 2 = Thursday.</p>",
                    solution_hi: "<p>24.(d) 26 नवंबर 2013 को मंगलवार था. 2018 में जाने पर विषम दिनों की संख्या = <br>+1 + 1 + 2 + 1 + 1 = 6. हम 26 नवंबर तक पहुंच गए हैं, लेकिन हमें 29 नवंबर तक पहुंचना है. बीच में दिनों की संख्या = 3. <br>दिनों की कुल संख्या = 6 + 3 = 9, 9 को 7 से विभाजित करने पर शेषफल 2 आता है। <math display=\"inline\"><mo>&#8658;</mo></math> मंगलवार + 2 = गुरुवार।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "25",
                    section: "18",
                    question_en: "<p>25. \'Obstinate\' is related to \'Stubborn\' in the same way as \'Resolute\' is related to \'__________&rsquo; .<br>(The words must be considered as meaningful English words and must not be related to each other based on the number of letters/number of consonants/vowels in the word.)</p>",
                    question_hi: "<p>25. \'जिद्दी\' (Obstinate), \'हठी\' (Stubborn) से उसी प्रकार संबंधित है, जिस प्रकार \'दृढ़प्रतिज्ञ\' (Resolute)\' ________ \' से संबंधित है।<br>(शब्दों को सार्थक हिंदी शब्द माना जाना चाहिए और शब्दों को अक्षरों की संख्या/व्यंजनों/स्वरों की संख्या के आधार पर एक दूसरे से संबंधित नहीं किया जाना चाहिए।)</p>",
                    options_en: [
                        "<p>Determined</p>",
                        "<p>Wavering</p>",
                        "<p>Thoughtful</p>",
                        "<p>Dithering</p>"
                    ],
                    options_hi: [
                        "<p>कृतसंकल्प (Determined)</p>",
                        "<p>डगमगाना (Wavering)</p>",
                        "<p>विचारपूर्ण (Thoughtful)</p>",
                        "<p>थरथराना (Dithering)</p>"
                    ],
                    solution_en: "<p>25.(a)<br>As stubborn is the synonym of obstinate, similarly &lsquo;Determined&rsquo; is the synonym of Resolute.</p>",
                    solution_hi: "<p>25.(a)<br>जैसे \'हठी\' (Stubborn) \'जिद्दी\' (Obstinate) का पर्यायवाची है, वैसे ही कृतसंकल्प (Determined), \'दृढ़प्रतिज्ञ\' (Resolute)\' का पर्यायवाची है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "26",
                    section: "18",
                    question_en: "<p>26. The ____________ is a cricket shot in which the batsman swings his bat vertically and hits the ball along the ground. The ball is hit between cover and mid-off.</p>",
                    question_hi: "<p>26. _________ एक क्रिकेट शॉट है, जिसमें बल्लेबाज अपने बल्ले को लंबवत घुमाता है और गेंद को जमीन के अनुदिश मारता है। गेंद को कवर (cover) और मिड ऑफ (mid-off) के बीच मारा जाता है।</p>",
                    options_en: [
                        "<p>Front Foot Defense</p>",
                        "<p>Straight Drive</p>",
                        "<p>Off drive</p>",
                        "<p>On Drive</p>"
                    ],
                    options_hi: [
                        "<p>फ्रंट फुट डिफेंस (Front Foot Defense)</p>",
                        "<p>स्ट्रेट ड्राइव (Straight Drive)</p>",
                        "<p>ऑफ ड्राइव (Off drive)</p>",
                        "<p>ऑन ड्राइव (On Drive)</p>"
                    ],
                    solution_en: "<p>26.(c) <strong>Off drive.</strong> Other shots of Cricket : On Drive - It can be used to rotate the strike and hit occasional boundaries. Straight drive - It effectively combines all three methods to produce an aesthetically pleasing shot. Front Foot Defense - It is a deliberate shot that aims to prevent the ball from hitting the wicket or the player\'s pads.</p>",
                    solution_hi: "<p>26.(c) <strong>ऑफ ड्राइव (Off drive)।</strong> क्रिकेट के अन्य शॉट: ऑन ड्राइव - इसका उपयोग स्ट्राइक रोटेट करने और कभी-कभी बाउंड्री लगाने के लिए किया जाता है। स्ट्रेट ड्राइव - यह तीनों विधियों को प्रभावी ढंग से संयोजित करके क्रिकेट के सबसे बेहतरीन शॉट्स में से एक बनाता है। फ्रंट फुट डिफेंस - यह एक विचारपूर्वक शॉट है जिसका उद्देश्य गेंद को विकेट या खिलाड़ी के पैड से टकराने से रोकना होता है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "27",
                    section: "18",
                    question_en: "<p>27. If a bar magnet is hung from a string, in which direction does its north pole point?</p>",
                    question_hi: "<p>27. यदि एक छड़ चुंबक को एक डोरी से लटका दिया जाए, तो उसका उत्तरी ध्रुव किस दिशा में इंगित करेगा?</p>",
                    options_en: [
                        "<p>West</p>",
                        "<p>North</p>",
                        "<p>East</p>",
                        "<p>South</p>"
                    ],
                    options_hi: [
                        "<p>पश्चिम</p>",
                        "<p>उत्तर</p>",
                        "<p>पूर्व</p>",
                        "<p>दक्षिण</p>"
                    ],
                    solution_en: "<p>27.(b) <strong>North.</strong> When a bar magnet is hung freely from a string, its north pole aligns with the Earth\'s magnetic field and points toward the Earth\'s magnetic north pole. This happens because opposite magnetic poles attract; since the Earth\'s magnetic north pole is actually a magnetic south pole, it attracts the north pole of the magnet, causing it to point north.</p>",
                    solution_hi: "<p>27.(b) <strong>उत्तर।</strong> जब एक छड़ चुंबक को एक डोरी से स्वतंत्र रूप से लटकाया जाता है, तो इसका उत्तरी ध्रुव पृथ्वी के चुंबकीय क्षेत्र के साथ संरेखित हो जाता है और पृथ्वी के चुंबकीय उत्तरी ध्रुव की ओर इंगित करता है। ऐसा इसलिए होता है क्योंकि विपरीत चुंबकीय ध्रुव आकर्षित होते हैं; चूँकि पृथ्वी का चुंबकीय उत्तरी ध्रुव वास्तव में एक चुंबकीय दक्षिणी ध्रुव है, यह चुंबक के उत्तरी ध्रुव को आकर्षित करता है, जिससे वह उत्तर की ओर निर्देशित होता है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "28",
                    section: "18",
                    question_en: "<p>28. Who among the following was the founder of &lsquo;Vikramshila Vishwavidyalaya&rsquo;?</p>",
                    question_hi: "<p>28. निम्नलिखित में से कौन \'विक्रमशिला विश्वविद्यालय\' के संस्थापक थे ?</p>",
                    options_en: [
                        "<p>Mahipala I</p>",
                        "<p>Dharmapala</p>",
                        "<p>Ramapala</p>",
                        "<p>Govindapala</p>"
                    ],
                    options_hi: [
                        "<p>महिपाल प्रथम</p>",
                        "<p>धर्मपाल</p>",
                        "<p>रामपाल</p>",
                        "<p>गोविंदपाल</p>"
                    ],
                    solution_en: "<p>28.(b) <strong>Dharmapala.</strong> He was the son of Gopala (founder of Pala Dynasty). He revived Nalanda University. He constructed the Somapura Mahavihara at Paharpur. Art and Architecture of Palas - Vikramshila Vihar, Odantpuri Vihar, and Jagaddal Vihar. Vikramashila was one of the two most crucial centers of learning in India during the Pala Empire, along with Nalanda.</p>",
                    solution_hi: "<p>28.(b) <strong>धर्मपाल।</strong> वह गोपाल (पाल वंश के संस्थापक) के पुत्र थे। उन्होंने नालन्दा विश्वविद्यालय का पुनरुद्धार किया। उन्होंने पहाड़पुर में सोमपुरा महाविहार का निर्माण किया। पालों की कला और वास्तुकला - विक्रमशिला विहार, ओदंतपुरी विहार और जगद्दल विहार। पाल साम्राज्य के दौरान नालंदा के साथ विक्रमशिला भारत में शिक्षा के दो सबसे महत्वपूर्ण केंद्रों में से एक था।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "29",
                    section: "18",
                    question_en: "<p>29. Which of the following novels was written by George Orwell?</p>",
                    question_hi: "<p>29. निम्नलिखित में से कौन-सा उपन्यास जॉर्ज ऑरवेल द्वारा लिखा गया?</p>",
                    options_en: [
                        "<p>Brave New World</p>",
                        "<p>Fahrenheit 451</p>",
                        "<p>Animal Farm</p>",
                        "<p>Pride and Prejudice</p>"
                    ],
                    options_hi: [
                        "<p>ब्रेव न्यू वर्ल्ड</p>",
                        "<p>फारेनहाइट 451</p>",
                        "<p>एनिमल फार्म</p>",
                        "<p>प्राइड एंड प्रीजूडिस</p>"
                    ],
                    solution_en: "<p>29.(c) <strong>Animal Farm</strong>. George Orwell\'s other notable works : &ldquo;Burmese Days (1934)&rdquo;, &ldquo;Coming Up for Air (1939)&rdquo;, &ldquo;Down and Out in Paris and London (1933)&rdquo;. &lsquo;Brave New World&rdquo; is written by Aldous Huxley (1932), &ldquo;Fahrenheit 451&rdquo; is written by Ray Bradbury (1953), &ldquo;Pride and Prejudice&rdquo; is written by Jane Austen (1813).</p>",
                    solution_hi: "<p>29.(c) <strong>एनिमल फार्म।</strong> जॉर्ज ऑरवेल की अन्य उल्लेखनीय कृतियाँ: \"बर्मीज़ डेज़ (1934)\", \"कमिंग अप फ़ॉर एयर (1939)\", \"डाउन एंड आउट इन पेरिस एंड लंदन (1933)\"। \'ब्रेव न्यू वर्ल्ड\' एल्डस हक्सले (1932) द्वारा लिखी गई है, \"फ़ारेनहाइट 451\" रे ब्रैडबरी (1953) द्वारा लिखी गई है, \"प्राइड एंड प्रीज्यूडीस \" जेन ऑस्टेन (1813) द्वारा लिखी गई है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "30",
                    section: "18",
                    question_en: "<p>30. A javelin thrown by an athlete is in ________ motion.</p>",
                    question_hi: "<p>30. एक एथलीट द्वारा फेंका गया भाला ________ गति में होता है।</p>",
                    options_en: [
                        "<p>oscillatory</p>",
                        "<p>periodic</p>",
                        "<p>rectilinear</p>",
                        "<p>curvilinear</p>"
                    ],
                    options_hi: [
                        "<p>दोलनी</p>",
                        "<p>आवर्ती</p>",
                        "<p>ऋजु रेखीय</p>",
                        "<p>वक्रीय</p>"
                    ],
                    solution_en: "<p>30.(d) <strong>curvilinear.</strong> This is because the javelin follows a curved trajectory due to the influence of gravity and the initial angle of release. Curvilinear motion refers to the motion of a particle along a curved path. Oscillatory motion: Repeated back-and-forth motion (Example - A swinging pendulum). Periodic motion: Motion repeating in regular intervals (Example - Rotation of the earth around the sun). Rectilinear motion: Motion in a straight line (Example - An object falling straight down).</p>",
                    solution_hi: "<p>30.(d) <strong>वक्रीय।</strong> ऐसा इसलिए है क्योंकि भाला गुरुत्वाकर्षण के प्रभाव और छोड़ने के प्रारंभिक कोण के कारण एक वक्र प्रक्षेप पथ का अनुसरण करता है। वक्रीय गति से तात्पर्य एक कण की एक घुमावदार पथ पर गति करने से है। दोलन गति: बार-बार आगे-पीछे होने वाली गति (उदाहरण - एक झूलता हुआ पेंडुलम)। आवर्ती गति: नियमित अंतराल में दोहराई जाने वाली गति (उदाहरण - सूर्य के चारों ओर पृथ्वी का घूमना)। ऋजु रैखिक गति: एक सीधी रेखा में गति (उदाहरण - एक वस्तु का सीधे नीचे गिरना)।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "31",
                    section: "18",
                    question_en: "<p>31. Which military officer led the British forces in the Battle of Chinhat fought in 1857?</p>",
                    question_hi: "<p>31. 1857 में लड़े गए चिनहट (Chinhat) के युद्ध में ब्रिटिश सेना का नेतृत्व किस सैन्य अधिकारी ने किया था?</p>",
                    options_en: [
                        "<p>Robert Maclagan</p>",
                        "<p>Charles Ellice</p>",
                        "<p>Henry Lawrence</p>",
                        "<p>James George Smith</p>"
                    ],
                    options_hi: [
                        "<p>रॉबर्ट मैक्लेगन</p>",
                        "<p>चार्ल्स एलिस</p>",
                        "<p>हेनरी लॉरेंस</p>",
                        "<p>जेम्स जॉर्ज स्मिथ</p>"
                    ],
                    solution_en: "<p>31.(c) <strong>Henry Lawrence.</strong> The Battle of Chinhat took place on the morning of June 30, 1857, near the village of Chinhat in Awadh (now Uttar Pradesh), specifically at Ismailganj, where British forces clashed with Indian rebels during the Indian Rebellion of 1857.</p>",
                    solution_hi: "<p>31.(c) <strong>हेनरी लॉरेंस।</strong> चिनहट का युद्ध 30 जून, 1857 की सुबह अवध (अब उत्तर प्रदेश) के चिनहट गांव के पास, विशेष रूप से इस्माइलगंज में हुई, जहाँ 1857 के भारतीय विद्रोह के दौरान ब्रिटिश सेना और भारतीय विद्रोहियों के बीच संघर्ष हुआ था।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "32",
                    section: "18",
                    question_en: "<p>32. Which of the following decomposition reactions is NOT a redox reaction?</p>",
                    question_hi: "<p>32. निम्नलिखित में से कौन-सी अपघटन अभिक्रिया रेडॉक्स अभिक्रिया नहीं है?</p>",
                    options_en: [
                        "<p>Decomposition of dihydrogen monoxide</p>",
                        "<p>Decomposition of sodium hydride</p>",
                        "<p>Decomposition of potassium chlorate</p>",
                        "<p>Decomposition of calcium carbonate</p>"
                    ],
                    options_hi: [
                        "<p>डाइहाइड्रोजन मोनोऑक्साइड का अपघटन</p>",
                        "<p>सोडियम हाइड्राइड का अपघटन</p>",
                        "<p>पोटैशियम क्लोरेट का अपघटन</p>",
                        "<p>कैल्शियम कार्बोनेट का अपघटन</p>"
                    ],
                    solution_en: "<p>32.(d) <strong>Decomposition</strong> <strong>of calcium carbonate.</strong> A redox reaction is defined as a reaction in which oxidation and reduction take place simultaneously. The decomposition of calcium carbonate (<math xmlns=\"http://www.w3.org/1998/Math/MathML\" class=\"wrs_chemistry\"><msub><mi>CaCO</mi><mn>3</mn></msub></math>) into calcium oxide (CaO) and carbon dioxide (<math xmlns=\"http://www.w3.org/1998/Math/MathML\" class=\"wrs_chemistry\"><msub><mi>CO</mi><mn>2</mn></msub></math>) does not involve a change in the oxidation states of the elements involved, making it a non-redox reaction.</p>",
                    solution_hi: "<p>32.(d) <strong>कैल्शियम कार्बोनेट का अपघटन।</strong> रेडॉक्स अभिक्रिया को ऐसी अभिक्रिया के रूप में परिभाषित किया जाता है जिसमें ऑक्सीकरण और अपचयन एक साथ होता है। कैल्शियम कार्बोनेट (<math xmlns=\"http://www.w3.org/1998/Math/MathML\" class=\"wrs_chemistry\"><msub><mi>CaCO</mi><mn>3</mn></msub></math>) का कैल्शियम ऑक्साइड (CaO) और कार्बन डाइऑक्साइड (<math xmlns=\"http://www.w3.org/1998/Math/MathML\" class=\"wrs_chemistry\"><msub><mi>CO</mi><mn>2</mn></msub></math>) में अपघटन होने पर इसमें शामिल तत्वों की ऑक्सीकरण अवस्था में कोई परिवर्तन नहीं होता है, जिससे यह एक नॉन-रेडॉक्स अभिक्रिया बन जाती है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "33",
                    section: "18",
                    question_en: "<p>33. Who was the freedom fighter and member of the Mewar Praja Mandal who passed away at the age of 102?</p>",
                    question_hi: "<p>33. 102 वर्ष की आयु में दिवंगत स्वतंत्रता सेनानी और मेवाड़ प्रजा मंडल के सदस्य कौन थे?</p>",
                    options_en: [
                        "<p>Madan Mohan Somtiya</p>",
                        "<p>Bal Gangadhar Tilak</p>",
                        "<p>Motilal Nehru</p>",
                        "<p>Lala Lajpat Rai</p>"
                    ],
                    options_hi: [
                        "<p>मदन मोहन सोमटिया</p>",
                        "<p>बाल गंगाधर तिलक</p>",
                        "<p>मोतीलाल नेहरू</p>",
                        "<p>लाला लाजपत राय</p>"
                    ],
                    solution_en: "<p>33.(a)<strong> Madan Mohan Somtiya.</strong> A freedom fighter and member of the Mewar Praja Mandal, played a key role in India\'s independence movement, including the Quit India Movement. Known for his acts of defiance, he assisted in blackening Queen Victoria\'s statue in Udaipur as a protest.</p>",
                    solution_hi: "<p>33.(a) <strong>मदन मोहन सोमटिया</strong> स्वतंत्रता सेनानी और मेवाड़ प्रजा मंडल के सदस्य थे। उन्होंने भारत के स्वतंत्रता संग्राम, विशेषकर भारत छोड़ो आंदोलन में प्रमुख भूमिका निभाई। उन्होंने विरोधस्वरूप उदयपुर में क्वीन विक्टोरिया की मूर्ति को काला करने में सहायता की थी।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "34",
                    section: "18",
                    question_en: "<p>34. Which of the following is NOT an Indian tournament?</p>",
                    question_hi: "<p>34. निम्नलिखित में से कौन-सा भारतीय टूर्नामेंट नहीं है?</p>",
                    options_en: [
                        "<p>UEFA Champions League</p>",
                        "<p>Durand Cup</p>",
                        "<p>Santosh Trophy</p>",
                        "<p>Subroto Cup</p>"
                    ],
                    options_hi: [
                        "<p>यूईएफए चैंपियंस लीग</p>",
                        "<p>डूरंड कप</p>",
                        "<p>संतोष ट्रॉफी</p>",
                        "<p>सुब्रतो कप</p>"
                    ],
                    solution_en: "<p>34.(a) <strong>UEFA Champions League</strong>. It is an annual club football competition organized by the Union of European Football Associations (UEFA) and contested by top-division European clubs. It was founded in 1955. Some prominent football trophies in India include the Durand Cup, Santosh Trophy, Federation Cup, I-League Trophy, Indian Super League (ISL) Trophy, IFA Shield, Rovers Cup, Sait Nagjee Trophy, Subroto Cup, and Bordoloi Trophy.</p>",
                    solution_hi: "<p>34.(a) <strong>यूईएफए चैंपियंस लीग।</strong> यह यूनियन ऑफ यूरोपियन फुटबॉल एसोसिएशन (UEFA) द्वारा आयोजित एक वार्षिक क्लब फुटबॉल प्रतियोगिता है और इसमें शीर्ष-डिवीजन यूरोपीय क्लब भाग लेते हैं। इसकी स्थापना 1955 में हुई थी। भारत में कुछ प्रमुख फुटबॉल ट्रॉफियों में डूरंड कप, संतोष ट्रॉफी, फेडरेशन कप, आई-लीग ट्रॉफी, इंडियन सुपर लीग (ISL) ट्रॉफी, IFA शील्ड, रोवर्स कप, सैत नागजी ट्रॉफी, सुब्रतो कप और बोरदोलोई ट्रॉफी शामिल हैं।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "35",
                    section: "18",
                    question_en: "<p>35. Which plateaus are very fertile because they are rich in black soil that is very good for farming?</p>",
                    question_hi: "<p>35. कौन-से पठार बहुत उपजाऊ होते हैं क्योंकि वे काली मृदा में समृद्ध होते हैं जो खेती के लिए बहुत अच्छी है?</p>",
                    options_en: [
                        "<p>African plateau</p>",
                        "<p>Ethiopian plateau</p>",
                        "<p>Katanga plateau</p>",
                        "<p>Deccan lava plateau</p>"
                    ],
                    options_hi: [
                        "<p>अफ्रीकी पठार</p>",
                        "<p>इथियोपिया का पठार</p>",
                        "<p>कटंगा पठार</p>",
                        "<p>दक्कन लावा पठार</p>"
                    ],
                    solution_en: "<p>35.(d) <strong>Deccan lava plateau</strong> is a volcanic plateau in west-central India that was formed by the solidification of lava from deep within the Earth. It is a triangular landmass that lies to the south of the river Narmada. The African plateau is famous for gold and diamond mining. The Katanga Plateau in the Democratic Republic of the Congo is known for its rich deposits of copper and uranium.</p>",
                    solution_hi: "<p>35.(d) <strong>दक्कन लावा पठार</strong> पश्चिम-मध्य भारत में एक ज्वालामुखीय पठार है जिसका निर्माण पृथ्वी के भीतर लावा के जमने से हुआ था। यह एक त्रिकोणीय भूभाग है जो नर्मदा नदी के दक्षिण में स्थित है। अफ्रीकी पठार सोने और हीरे के खनन के लिए प्रसिद्ध है। कांगो लोकतांत्रिक गणराज्य में कटंगा पठार तांबे और यूरेनियम के समृद्ध भंडार के लिए जाना जाता है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "36",
                    section: "18",
                    question_en: "<p>36. What is the IUPAC name of tertiarybutyl alcohol?</p>",
                    question_hi: "<p>36. टर्शियरी ब्यूटाईल ऐल्कोहॉल (Tertiarybutyl alcohol) का IUPAC नाम क्या है?</p>",
                    options_en: [
                        "<p>2-Methylpropan-2-ol</p>",
                        "<p>1-propylpropan-3-ol</p>",
                        "<p>1-Methylpropan-3-ol</p>",
                        "<p>1-ethylpropan-3-ol</p>"
                    ],
                    options_hi: [
                        "<p>2-मेथाइलप्रोपेन-2-ol (2-Methylpropan-2-ol)</p>",
                        "<p>1-प्रोपाइलप्रोपेन-3-ol (1-propylpropan-3-ol)</p>",
                        "<p>1-मेथाइलप्रोपेन-3-ol (1-Methylpropan-3-ol)</p>",
                        "<p>1-एथाइलप्रोपेन-3-ol (1-ethylpropan-3-ol)</p>"
                    ],
                    solution_en: "<p>36.(a) <strong>2-Methylpropan-2-ol.</strong> Tertiary Butyl Alcohol, also Tert-butyl alcohol, is a tertiary alcohol used in fuels and fuel additives, intermediates and laboratory chemicals. It is a colorless oily liquid with a sharp alcohol odor.</p>",
                    solution_hi: "<p>36.(a) <strong>2-मेथाइलप्रोपेन-2-ol </strong>(2-Methylpropan-2-ol)। तृतीयक ब्यूटाइल अल्कोहल, जिसे टर्ट-ब्यूटाइल अल्कोहल भी कहा जाता है, ईंधन और ईंधन योजक, मध्यवर्ती और प्रयोगशाला रसायनों में इस्तेमाल किया जाने वाला तृतीयक अल्कोहल है। यह एक रंगहीन तैलीय तरल है जिसमें अल्कोहल की तीखी गंध होती है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "37",
                    section: "18",
                    question_en: "<p>37. Natya Sangeet is a form of musical play in classical music still prevalent in Maharashtra. Bal Gandharava is an exponent of this musical form. What is actual name of Balgandharva?</p>",
                    question_hi: "<p>37. शास्त्रीय संगीत में, नाट्य संगीत संगीत नाटक का एक रूप है, जो महाराष्ट्र में आज भी प्रचलित है। बाल गंधर्व इस संगीत रूप के प्रतिपादक हैं। उनका वास्तविक नाम क्या है?</p>",
                    options_en: [
                        "<p>Vishnu Narayan Bhatkhande</p>",
                        "<p>Narayan Shripad Rajhans</p>",
                        "<p>Mallikarjun Mansur</p>",
                        "<p>Vishnu Digambar Paluskar</p>"
                    ],
                    options_hi: [
                        "<p>विष्णु नारायण भातखंडे</p>",
                        "<p>नारायण श्रीपाद राजहंस</p>",
                        "<p>मल्लिकार्जुन मंसूर</p>",
                        "<p>विष्णु दिगंबर पलुस्कर</p>"
                    ],
                    solution_en: "<p>37.(b) <strong>Narayan Shripad Rajhans.</strong> He was a renowned Marathi singer and stage actor, famous for portraying female characters in Marathi plays at a time when women were not permitted to act on stage. Natya Sangeet is a form of drama that combines prose and poetry through songs to convey the story, akin to musicals. The term \"Dramatic Music\" refers to Natya Sangeet, one of the two popular forms of vocal arts in Maharashtra and neighboring states, the other being Bhavageet.</p>",
                    solution_hi: "<p>37.(b) <strong>नारायण श्रीपाद राजहंस</strong> एक प्रसिद्ध मराठी गायक और रंगमंच अभिनेता थे, जो उस समय मराठी नाटकों में महिला पात्रों को चित्रित करने के लिए प्रसिद्ध थे, जब महिलाओं को मंच पर अभिनय करने की अनुमति नहीं थी। नाट्य संगीत नाटक का एक रूप है जो संगीत के समान कहानी को व्यक्त करने के लिए गीतों के माध्यम से गद्य और पद्य को जोड़ता है। नाट्य संगीत महाराष्ट्र और आसपास के राज्यों में लोकप्रिय मुखर कलाओं में से एक है, जबकि दूसरी लोकप्रिय शैली भावगीत है। \"नाट्य संगीत\" को ही ड्रामेटिक म्यूज़िक कहा जाता है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "38",
                    section: "18",
                    question_en: "<p>38. Which of the following is the transboundary river between India and Pakistan?</p>",
                    question_hi: "<p>38. निम्नलिखित में से कौन भारत और पाकिस्तान के बीच सीमा पार (transboundary) नदी है?</p>",
                    options_en: [
                        "<p>Indus</p>",
                        "<p>Ravi</p>",
                        "<p>Beas</p>",
                        "<p>Jhelum</p>"
                    ],
                    options_hi: [
                        "<p>सिंधु</p>",
                        "<p>रावी</p>",
                        "<p>ब्यास</p>",
                        "<p>झेलम</p>"
                    ],
                    solution_en: "<p>38.(b) <strong>Ravi.</strong> This river flows through north-western India and eastern Pakistan. It, along with the Beas and Sutlej rivers, was allocated to India under the Indus Water Treaty. Indus River : This river originates in western Tibet, passes through Kashmir, and flows into Pakistan before falling into the Arabian Sea. It, along with the Chenab and Jhelum rivers, was allocated to Pakistan under the Indus Water Treaty of 1960.</p>",
                    solution_hi: "<p>38.(b) <strong>रावी।</strong> यह नदी उत्तर-पश्चिमी भारत और पूर्वी पाकिस्तान से होकर बहती है। इसे व्यास और सतलुज नदियों के साथ सिंधु जल संधि के तहत भारत को आवंटित किया गया था। सिंधु नदी: यह नदी पश्चिमी तिब्बत से निकलती है, और कश्मीर से होते हुए पाकिस्तान में बहती है तथा अरब सागर में मिल जाती है। इसे चिनाब और झेलम नदियों के साथ 1960 की सिंधु जल संधि के तहत पाकिस्तान को आवंटित किया गया था।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "39",
                    section: "18",
                    question_en: "<p>39. Who developed the technique of preserving the quality of food by raising its temperature to prevent microbial growth in the food?</p>",
                    question_hi: "<p>39. भोजन में सूक्ष्म जीवों की वृद्धि को रोकने के लिए उसका तापमान बढ़ाकर भोजन की गुणवत्ता को बनाए&nbsp;रखने की तकनीक किसने विकसित की थी?</p>",
                    options_en: [
                        "<p>Antonie van Leeuwenhoek</p>",
                        "<p>Joseph Lister</p>",
                        "<p>Robert Koch</p>",
                        "<p>Louis Pasteur</p>"
                    ],
                    options_hi: [
                        "<p>एंटोनी वैन ल्यूवेनहॉक (Antonie van Leeuwenhoek)</p>",
                        "<p>जोसेफ लिस्टर (Joseph Lister)</p>",
                        "<p>रॉबर्ट कोच (Robert Koch)</p>",
                        "<p>लुई पास्चर (Louis Pasteur)</p>"
                    ],
                    solution_en: "<p>39.(d) <strong>Louis Pasteur. </strong>He was a French biologist who is often regarded as the father of modern microbiology. Scientists and their discoveries: Antonie van Leeuwenhoek - Microbiology, Charles Darwin - Theory of evolution, Robert Koch - Tuberculosis bacterium, Barbara McClintock - Jumping genes, Watson and Crick - DNA structure.</p>",
                    solution_hi: "<p>39.(d) <strong>लुई पाश्चर</strong>। वे एक फ्रांसीसी जीव विज्ञानी थे जिन्हें प्रायः आधुनिक सूक्ष्म जीव विज्ञान का जनक माना जाता है। वैज्ञानिक और उनकी खोजें: एंटोनी वैन ल्यूवेनहॉक - सूक्ष्म जीव विज्ञान, चार्ल्स डार्विन - विकास का सिद्धांत, रॉबर्ट कोच - क्षय रोग या तपेदिक के जीवाणु, बारबरा मैकक्लिंटॉक - जंपिंग जीन, वॉटसन और क्रिक - DNA संरचना।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "40",
                    section: "18",
                    question_en: "<p>40. In which of the following states is the Behdienkhlam festival celebrated in the month of July ?</p>",
                    question_hi: "<p>40. निम्नलिखित में से किस राज्य में जुलाई महीने में बेहदीनखलम महोत्सव मनाया जाता है?</p>",
                    options_en: [
                        "<p>Meghalaya</p>",
                        "<p>Haryana</p>",
                        "<p>West Bengal</p>",
                        "<p>Bihar</p>"
                    ],
                    options_hi: [
                        "<p>मेघालय</p>",
                        "<p>हरियाणा</p>",
                        "<p>पश्चिम बंगाल</p>",
                        "<p>बिहार</p>"
                    ],
                    solution_en: "<p>40.(a) <strong>Meghalaya.</strong> Behdienkhlam (chasing away the Demon of Cholera) is celebrated annually in July after the sowing period, the most important dance festival of the Jaintia tribes. Other famous Festivals : Meghalaya - Wangala, Nongkrem, Shad Suk Mynsiem, Shad Sukra. Haryana - Guga Navmi, Gita Jayanti. West Bengal - Shiber Gajan, Disum sendra, Rohini Utsav. Bihar - Chhath Puja, Bihula, Karama.</p>",
                    solution_hi: "<p>40.(a) <strong>मेघालय।</strong> बेहदीनखलम (हैजा के दानव को भगाना) प्रत्येक वर्ष बुवाई के मौसम के बाद जुलाई में मनाया जाता है, यह जैंतिया जनजातियों का सबसे महत्वपूर्ण नृत्य उत्सव है। अन्य प्रसिद्ध त्योहार: मेघालय - वांगला, नोंग्क्रेम, शाद सुक म्यंसिएम, शाद सुकरा। हरियाणा - गुगा नवमी, गीता जयंती। पश्चिम बंगाल - शिबर गजान, दिसुम सेंद्रा, रोहिणी उत्सव। बिहार - छठ पूजा, बिहुला, करमा।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "41",
                    section: "18",
                    question_en: "<p>41. Dr. B. R. Ambedkar described Directive Principles of State Policy as __________of the Indian constitution.</p>",
                    question_hi: "<p>41. डॉ. बी.आर. अंबेडकर ने भारतीय संविधान के _____के रूप में राज्य के नीति निदेशक तत्&zwj;वों का वर्णन किया है।</p>",
                    options_en: [
                        "<p>soul of the state</p>",
                        "<p>identity card of the constitution</p>",
                        "<p>The most precious part of the constitution</p>",
                        "<p>a novel feature of the constitution</p>"
                    ],
                    options_hi: [
                        "<p>राज्य की आत्मा</p>",
                        "<p>संविधान के पहचान-पत्र</p>",
                        "<p>संविधान के सर्वाधिक बहुमूल्य भाग</p>",
                        "<p>संविधान की एक नवीन विशेषता</p>"
                    ],
                    solution_en: "<p>41.(d) <strong>a novel feature of the Indian constitution.</strong> The Directive Principles of State Policy (DPSP) are outlined in Part IV (Articles 36-51) of the Indian Constitution and are inspired by the Constitution of Ireland. They represent the ideals that the State should consider while formulating policies and enacting laws to promote social and economic justice.</p>",
                    solution_hi: "<p>41.(d) संविधान की एक नवीन विशेषता। राज्य के नीति निर्देशक सिद्धांत (DPSP) भारतीय संविधान के भाग IV (अनुच्छेद 36-51) में उल्लिखित हैं और आयरलैंड के संविधान से प्रेरित हैं। वे उन आदर्शों का प्रतिनिधित्व करते हैं, जिन्हें राज्य को सामाजिक एवं आर्थिक न्याय को बढ़ावा देने के लिए नीतियां बनाते व कानून का निर्माण करते समय ध्यान में रखना चाहिए।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "42",
                    section: "18",
                    question_en: "<p>42. Which of the following statements about eutrophication is correct?</p>",
                    question_hi: "<p>42. यूट्रोफिकेशन के बारे में निम्नलिखित में से कौन-सा कथन सही है?</p>",
                    options_en: [
                        "<p>It happens when too much nitrogen enriches the water, causing excessive growth of plants and <br>algae.</p>",
                        "<p>It happens when too much carbon enriches the water, causing excessive growth of bacteria and reduced growth of plants.</p>",
                        "<p>It happens when too less nitrogen is present in the water, causing reduced growth of plants and algae.</p>",
                        "<p>It is a beneficial process for environment restoration.</p>"
                    ],
                    options_hi: [
                        "<p>यह तब होता है जब बहुत अधिक नाइट्रोजन पानी को समृद्ध करती है, जिससे पौधों और शैवाल की<br>अत्यधिक वृद्धि होती है।</p>",
                        "<p>यह तब होता है जब बहुत अधिक कार्बन पानी को समृद्ध करता है, जिससे बैक्टीरिया का अत्यधिक विकास होता है और पौधों की बढ़त कम हो जाती है।</p>",
                        "<p>यह तब होता है जब पानी में बहुत कम नाइट्रोजन मौजूद होता है, जिससे पौधों और शैवाल की वृद्धि<br>कम हो जाती है।</p>",
                        "<p>यह पर्यावरण की बहाली के लिए एक लाभकारी प्रक्रिया है।</p>"
                    ],
                    solution_en: "<p>42.(a) <strong>Eutrophication.</strong> It is the process where excess nutrients, particularly nitrogen and phosphorus, lead&nbsp;to an overgrowth of algae and aquatic plants. This growth can deplete oxygen in the water, harming aquatic life.</p>",
                    solution_hi: "<p>42.(a) <strong>यूट्रोफिकेशन।</strong> यह वह प्रक्रिया है जिसमें अतिरिक्त पोषक तत्व, विशेष रूप से नाइट्रोजन और फास्फोरस, शैवाल और जलीय पौधों की अत्यधिक वृद्धि का कारण बनते हैं। इसकी वृद्धि से जल में ऑक्सीजन की कमी हो सकती है, जिसके कारण जलीय जीवन प्रभावित हो सकता है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "43",
                    section: "18",
                    question_en: "<p>43. Who authored the book &lsquo;Maa - Mother&rsquo;, launched on November 12, 2024?</p>",
                    question_hi: "<p>43. 12 नवंबर, 2024 को प्रकाशित की गई पुस्तक \'मां-मदर\' किसने लिखी है?</p>",
                    options_en: [
                        "<p>Dr. Davendra Kumar Dhodawat</p>",
                        "<p>Dr. Mrinmayee Bhushan</p>",
                        "<p>Air Marshal Vikram Singh (Retd)</p>",
                        "<p>Prof. Sreeram Chaulia</p>"
                    ],
                    options_hi: [
                        "<p>डॉ. दवेन्द्र कुमार धोडावत</p>",
                        "<p>डॉ. मृणमयी भूषण</p>",
                        "<p>एयर मार्शल विक्रम सिंह (सेवानिवृत्त)</p>",
                        "<p>प्रोफेसर श्रीराम चौलिया</p>"
                    ],
                    solution_en: "<p>43.(a) <strong>Dr. Davendra Kumar Dhodawat. </strong>The book, a collection of poems in Hindi and English, celebrates the essence of motherhood and was launched in New Delhi by Law and Justice Minister Arjun Ram Meghwal</p>",
                    solution_hi: "<p>.43.(a) <strong>डॉ. दवेंद्र कुमार धोडावत।</strong> हिंदी और अंग्रेजी में कविताओं का संग्रह, यह पुस्तक मातृत्व के सार का जश्न मनाती है और इसका विमोचन नई दिल्ली में कानून और न्याय मंत्री अर्जुन राम मेघवाल द्वारा किया गया।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "44",
                    section: "18",
                    question_en: "<p>44. Which Indian award was established in the year 1982, to promote and encourage scientific efforts in the field of environment and ecology ?</p>",
                    question_hi: "<p>44. पर्यावरण और पारिस्थितिकी के क्षेत्र में वैज्ञानिक प्रयासों को बढ़ावा देने और प्रोत्साहित करने के लिए वर्ष 1982 में कौन-सा भारतीय पुरस्कार आरंभ किया गया था ?</p>",
                    options_en: [
                        "<p>Pitambar Pant National Environment Fellowship Award</p>",
                        "<p>Sanjay Gandhi Award for Environment and Ecology</p>",
                        "<p>B P Pal National Environment Fellowship Award for Biodiversity</p>",
                        "<p>Indira Gandhi Paryavaran Puraskar</p>"
                    ],
                    options_hi: [
                        "<p>पीतांबर पंत राष्ट्रीय पर्यावरण फैलोशिप पुरस्कार</p>",
                        "<p>पर्यावरण और पारिस्थितिकी के लिए संजय गांधी पुरस्कार</p>",
                        "<p>जैव विविधता के लिए बी.पी. पाल राष्ट्रीय पर्यावरण फैलोशिप पुरस्कार</p>",
                        "<p>इंदिरा गांधी पर्यावरण पुरस्कार</p>"
                    ],
                    solution_en: "<p>44.(b) The Sanjay Gandhi Award for Environment and Ecology is awarded annually to an individual citizen of India engaged in scientific work in the field. It includes a cash prize of ₹100,000, with the announcement made in May or June of the following year. Eligibility is open to all Indian citizens.</p>",
                    solution_hi: "<p>44.(b) पर्यावरण और पारिस्थितिकी के लिए संजय गांधी पुरस्कार प्रत्येक वर्ष इस क्षेत्र में वैज्ञानिक कार्य करने वाले भारत के किसी नागरिक को दिया जाता है। इसमें ₹100,000 का नकद पुरस्कार शामिल है, जिसकी घोषणा आगामी वर्ष के मई या जून में की जाती है। सभी भारतीय नागरिक इसके लिए पात्र हैं।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "45",
                    section: "18",
                    question_en: "<p>45. Which of the following is mentioned in Article 1 of the Indian constitution?</p>",
                    question_hi: "<p>45. भारतीय संविधान के अनुच्छेद 1 में निम्नलिखित में से किसका उल्लेख है?</p>",
                    options_en: [
                        "<p>India that is Bharat as federation of states</p>",
                        "<p>India that is Bharat as parliamentary democracy</p>",
                        "<p>India that is Bharat shall be a Union of states</p>",
                        "<p>India that is Bharat as presidential form of democracy</p>"
                    ],
                    options_hi: [
                        "<p>इंडिया दैट इज भारत ऐज फेडरेशन ऑफ स्टेट्स</p>",
                        "<p>इंडिया दैट इज भारत ऐज पार्लियामेंट्री डेमोक्रेसी</p>",
                        "<p>इंडिया दैट इज भारत शैल बी अ यूनियन ऑफ स्टेट्स</p>",
                        "<p>इंडिया दैट इज भारत ऐज प्रेसिडेंशियल फॉर्म ऑफ डेमोक्रेसी</p>"
                    ],
                    solution_en: "<p>45.(c) Article 1 of the Indian Constitution states, \"India, that is Bharat, shall be a Union of States,\" signifying that India is a federation where states possess certain powers, but the central government holds ultimate authority. Although the Preamble describes India as a sovereign, socialist, secular, democratic republic, it does not explicitly refer to India as a Union of States.</p>",
                    solution_hi: "<p>45.(c) भारतीय संविधान के अनुच्छेद 1 में कहा गया है, \"इंडिया, अर्थात् भारत, राज्यों का संघ होगा,\" जिसका अर्थ है कि भारत एक संघ है जहाँ राज्यों के पास कुछ शक्तियाँ हैं, लेकिन केंद्र सरकार के पास अंतिम अधिकार है। यद्यपि प्रस्तावना भारत को एक संप्रभु, समाजवादी, धर्मनिरपेक्ष, लोकतांत्रिक गणराज्य के रूप में वर्णित करती है, लेकिन यह स्पष्ट रूप से भारत को राज्यों के संघ के रूप में संदर्भित नहीं करती है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "46",
                    section: "18",
                    question_en: "<p>46. The CPWD along with the Ministry of Road Transport and Highways works for public infrastructure. What is the full form of CPWD?</p>",
                    question_hi: "<p>46. सड़क परिवहन और राजमार्ग मंत्रालय के साथ CPWD, सार्वजनिक अवसंरचना के लिए कार्य करता है। CPWD का पूर्ण रूप क्या है?</p>",
                    options_en: [
                        "<p>Central Public Welfare Department</p>",
                        "<p>Central Public Welfare Design</p>",
                        "<p>Central Political Welfare Design</p>",
                        "<p>Central Public Works Department</p>"
                    ],
                    options_hi: [
                        "<p>Central Public Welfare Department (सेंट्रल पब्लिक वेलफेयर डिपार्टमेंट)</p>",
                        "<p>Central Public Welfare Design (सेंट्रल पब्लिक वेलफेयर डिजाइन)</p>",
                        "<p>Central Political Welfare Design (सेंट्रल पॉलिटिकल वेलफेयर डिजाइन)</p>",
                        "<p>Central Public Works Department (सेंट्रल पब्लिक वर्क्स डिपार्टमेंट)</p>"
                    ],
                    solution_en: "<p>46.(d) <strong>The Central Public Works Department</strong> (CPWD) operates under the Ministry of Housing and Urban Affairs of India. It acts as the builder, developer, and manager of Central government properties. Over time, its operations have expanded to include roadways and bridge engineering. Established in 1854, its headquarters is located in the Central Secretariat in New Delhi.</p>",
                    solution_hi: "<p>46.(d) <strong>सेंट्रल पब्लिक वर्क्स डिपार्टमेंट</strong> (Central Public Works Department) भारत के आवास और शहरी मामलों के मंत्रालय के अधीन कार्य करता है। यह केंद्र सरकार की संपत्तियों के निर्माता, डेवलपर और प्रबंधक के रूप में कार्य करता है। समय के साथ, इसके संचालन में सड़क और पुल इंजीनियरिंग को शामिल करने के लिए विस्तार किया गया है। 1854 में स्थापित, इसका मुख्यालय नई दिल्ली में केंद्रीय सचिवालय में स्थित है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "47",
                    section: "18",
                    question_en: "<p>47. What challenge does foreign investment often face in India?</p>",
                    question_hi: "<p>47. भारत में विदेशी निवेश को अधिकतर किस चुनौती का सामना करना पड़ता है?</p>",
                    options_en: [
                        "<p>Excessive foreign competition</p>",
                        "<p>Inconsistent regulatory environment</p>",
                        "<p>Lack of skilled labour</p>",
                        "<p>Lack of consumer base</p>"
                    ],
                    options_hi: [
                        "<p>अत्यधिक विदेशी प्रतिस्पर</p>",
                        "<p>अस्थिर विनियामक परिवेश</p>",
                        "<p>कुशल श्रमिक अभाव</p>",
                        "<p>उपभोक्ता आधार अभाव</p>"
                    ],
                    solution_en: "<p>47.(b) <strong>Inconsistent regulatory environment</strong> can lead to difficulties in compliance and planning for investors, ultimately affecting their confidence and willingness to invest in the country. Foreign investment refers to when an investor from one country purchases ownership of an asset or business in another country.</p>",
                    solution_hi: "<p>47.(b) <strong>अस्थिर विनियामक परिवेश</strong> निवेशकों के लिए अनुपालन और योजना बनाने में कठिनाइयों का कारण बन सकता है, जो अंततः उनके आत्मविश्वास और देश में निवेश करने की इच्छा को प्रभावित कर सकता है। विदेशी निवेश से तात्पर्य तब होता है जब एक देश का निवेशक किसी दूसरे देश में किसी परिसंपत्ति या व्यवसाय का स्वामित्व खरीदता है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "48",
                    section: "18",
                    question_en: "<p>48. Which geostationary weather satellite launched by the European Space Agency in 1977 that provides weather imaging of Earth at both visible light and infrared wavelengths?</p>",
                    question_hi: "<p>48. 1977 में यूरोपीय अंतरिक्ष एजेंसी द्वारा कौन-सा भूस्थिर मौसम उपग्रह प्रक्षेपित किया गया था, जो दृश्यमान प्रकाश और अवरक्त तरंगदैर्ध्य दोनों पर पृथ्वी की मौसम छवियां प्रदान करता है?</p>",
                    options_en: [
                        "<p>Himawari</p>",
                        "<p>Meteosat</p>",
                        "<p>QuikSCAT</p>",
                        "<p>Landsat</p>"
                    ],
                    options_hi: [
                        "<p>हिमावरी (Himawari)</p>",
                        "<p>मेटियोसैट (Meteosat)</p>",
                        "<p>क्विकस्कैट (QuikSCAT)</p>",
                        "<p>लैंडसेट (Landsat)</p>"
                    ],
                    solution_en: "<p>48.(b) <strong>Meteosat</strong> - The first European geostationary weather satellite, launched in November 1977 by the European Space Agency (ESA). Himawari is a Japanese weather satellite series. QuikSCAT was a NASA satellite that measured ocean winds. Landsat is a series of Earth observation satellites focused on land use, geology, and natural resources, not specifically weather imaging.</p>",
                    solution_hi: "<p>48.(b) <strong>मेटियोसैट (Meteosat)-</strong> पहला यूरोपीय भूस्थैतिक मौसम उपग्रह, नवंबर 1977 में यूरोपीय अंतरिक्ष एजेंसी (ESA) द्वारा लॉन्च किया गया था। हिमावारी एक जापानी मौसम उपग्रह श्रृंखला है। QuikSCAT NASA का एक उपग्रह था जो समुद्री हवाओं को मापता था। लैंडसैट पृथ्वी अवलोकन उपग्रहों की एक श्रृंखला है जो भूमि उपयोग, भूविज्ञान और प्राकृतिक संसाधनों पर केंद्रित है, न कि विशेष रूप से मौसम इमेजिंग पर।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "49",
                    section: "18",
                    question_en: "<p>49. What is the empirically fitted relationship between the rate of change of money, wage, and rate of unemployment known as ?</p>",
                    question_hi: "<p>49. धन, मजदूरी में परिवर्तन की दर और बेरोजगारी की दर के बीच अनुभवजन्य रूप से स्थापित संबंध को किस रूप में जाना जाता है?</p>",
                    options_en: [
                        "<p>Friedman&rsquo;s model</p>",
                        "<p>Keynesian model</p>",
                        "<p>Baumol hypothesis</p>",
                        "<p>Phillips curve</p>"
                    ],
                    options_hi: [
                        "<p>फ्रीडमैन मॉडल</p>",
                        "<p>केनेसियन मॉडल</p>",
                        "<p>बॉमोल परिकल्पना</p>",
                        "<p>फिलिप्स वक्र</p>"
                    ],
                    solution_en: "<p>49.(d) <strong>Phillips curve</strong> states that inflation and unemployment have an inverse relationship; higher inflation is associated with lower unemployment and vice versa. This concept was first proposed by Alban William Phillips. Friedman&rsquo;s model: Milton Friedman\'s monetary policy model, focusing on monetary supply and demand. Keynesian model: John Maynard Keynes\' macroeconomic model, emphasizing government intervention and aggregate demand. Baumol hypothesis: William Baumol\'s theory on the relationship between inflation and economic growth.</p>",
                    solution_hi: "<p>49.(d) <strong>फिलिप्स वक्र</strong> बताता है कि मुद्रास्फीति और बेरोजगारी में विपरीत संबंध है, उच्च मुद्रास्फीति निम्न बेरोजगारी से जुड़ी होती और निम्न मुद्रास्फीति उच्च बेरोजगारी से जुड़ी होती है। इस अवधारणा को सबसे पहले एल्बन विलियम फिलिप्स ने प्रस्तावित किया था। फ्राइडमैन का मॉडल: मिल्टन फ्राइडमैन का मौद्रिक नीति मॉडल, जो मौद्रिक आपूर्ति और मांग पर केंद्रित है। कीनेसियन मॉडल: जॉन मेनार्ड कीन्स का व्यापक आर्थिक मॉडल, जो सरकारी हस्तक्षेप और समग्र मांग पर जोर देता है। बॉमोल परिकल्पना: मुद्रास्फीति और आर्थिक विकास के बीच संबंधों पर विलियम बॉमोल का सिद्धांत।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "50",
                    section: "17",
                    question_en: "<p>50. What is the name of the multi-layer clothing system unveiled by DRDO in January 2025 ?</p>",
                    question_hi: "<p>50. जनवरी 2025 में DRDO द्वारा अनावरण की जाने वाली बहु-परत वस्त्र प्रणाली का नाम क्या है?</p>",
                    options_en: [
                        "<p>ARJUN</p>",
                        "<p>HIMKAVACH</p>",
                        "<p>SHAKTI</p>",
                        "<p>VAYU</p>"
                    ],
                    options_hi: [
                        "<p>अर्जुन</p>",
                        "<p>हिमकवच</p>",
                        "<p>शक्ति</p>",
                        "<p>वायु</p>"
                    ],
                    solution_en: "<p>50.(b) <strong>HIMKAVACH,</strong> a multi-layered clothing system designed to protect soldiers in extreme temperatures (-60&deg;C to +20&deg;C). It offers insulation, breathability, and moisture protection, with outer layers repelling water and wind, and inner layers trapping heat.</p>",
                    solution_hi: "<p>50.(b) <strong>हिमकवच,</strong> एक बहु-परत वस्त्र प्रणाली है जिसे अत्यधिक तापमान (-60&deg;C से +20&deg;C) में सैनिकों की सुरक्षा के लिए डिज़ाइन किया गया है। यह इन्सुलेशन, सांस लेने की क्षमता और नमी से सुरक्षा प्रदान करता है, बाहरी परतें पानी और हवा को रोकती हैं, और आंतरिक परतें गर्मी को रोकती हैं।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "51",
                    section: "17",
                    question_en: "<p>51. If 3c2933k is divisible by both 5 and 11, where c and k are single digit natural numbers,&nbsp;then c + k = ______.</p>",
                    question_hi: "<p>51. यदि 3c2933k, 5 और 11 दोनों से विभाज्य है जहां c और k एक अंक वाली प्राकृतिक संख्याएं हैं, तो c+k =______________ है।</p>",
                    options_en: [
                        "<p>6</p>",
                        "<p>8</p>",
                        "<p>5</p>",
                        "<p>7</p>"
                    ],
                    options_hi: [
                        "<p>6</p>",
                        "<p>8</p>",
                        "<p>5</p>",
                        "<p>7</p>"
                    ],
                    solution_en: "<p>51.(a)<br>Divisibility of 11: The difference of the sum of digits at odd position and sum of digits at even position in a number is 0 or divisible by 11.<br>Now, (3 + 2 + 3 + k) - (c + 9 + 3) = 0<br><math display=\"inline\"><mo>&#8658;</mo></math> (8 + k) = (c + 12) &hellip;&hellip;(i)<br>Divisibility of 5: If the last digit of the number should be 0 or 5 then that number will be divisible by 5.<br>Value of k = 5 (given we take only natural no.)<br>Put value of k in equation (i), we get<br><math display=\"inline\"><mo>&#8658;</mo></math> (8 + 5) = (c + 12) <br><math display=\"inline\"><mo>&#8658;</mo></math> c = 13 - 12 = 1<br>So,<br>c + k = 1 + 5 = 6</p>",
                    solution_hi: "<p>51.(a) <br>11 की विभाज्यता: किसी संख्या में विषम स्थान पर अंकों के योग और सम स्थान पर अंकों के योग का अंतर 0 हो या 11 से विभाज्य होना चाहिए <br>अब, (3 + 2 + 3 + k) - (c + 9 + 3) = 0<br><math display=\"inline\"><mo>&#8658;</mo></math> (8 + k) = (c + 12) &hellip;&hellip;(i)<br>5 से विभाज्यता: यदि किसी संख्या का अंतिम अंक 0 या 5 हो तो वह संख्या 5 से विभाज्य होगी।<br>K का मान = 5 (दिया है, हम केवल प्राकृतिक संख्या लेंगे)<br>समीकरण (i) में k का मान रखने पर, हमें मिलता है<br><math display=\"inline\"><mo>&#8658;</mo></math> (8 + 5) = (c + 12) <br><math display=\"inline\"><mo>&#8658;</mo></math> c = 13 - 12 = 1<br>इसलिए,<br>c + k = 1 + 5 = 6</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "52",
                    section: "17",
                    question_en: "<p>52. Tarun gives Hari a head-start of 60 seconds in a 2400 m race and still beats him by 40&nbsp;seconds. If the speed of Hari is 6 m/s, find the speed of Tarun.</p>",
                    question_hi: "<p>52. 2400 m की दौड़ में तरुण, हरि को 60 सेकंड की शुरुआती बढ़त देता है और फिर भी उसे 40 सेकंड से हरा देता है। यदि हरि की चाल 6 m/s है, तो तरुण की चाल ज्ञात कीजिए।</p>",
                    options_en: [
                        "<p>8 m/s</p>",
                        "<p>7 m/s</p>",
                        "<p>8.5 m/s</p>",
                        "<p>7.5 m/s</p>"
                    ],
                    options_hi: [
                        "<p>8 m/s</p>",
                        "<p>7 m/s</p>",
                        "<p>8.5 m/s</p>",
                        "<p>7.5 m/s</p>"
                    ],
                    solution_en: "<p>52.(a) Given , Speed of Hari = 6m/s<br>Distance travelled in 60 sec = 360m<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742361739446.png\" alt=\"rId53\" width=\"343\" height=\"50\"><br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; Tarun&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;Hari <br>Distance <math display=\"inline\"><mo>&#8594;</mo></math>&nbsp; 2400&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;2040<br>Time&nbsp; &nbsp; &nbsp; &nbsp;<math display=\"inline\"><mo>&#8594;</mo></math>&nbsp; &nbsp; &nbsp;T&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; T + 40<br>Speed = <math display=\"inline\"><mfrac><mrow><mi>D</mi><mi>i</mi><mi>s</mi><mi>t</mi><mi>a</mi><mi>n</mi><mi>c</mi><mi>e</mi><mi>&#160;</mi></mrow><mrow><mi>T</mi><mi>i</mi><mi>m</mi><mi>e</mi><mi>&#160;</mi></mrow></mfrac></math> &rArr; 6 =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2040</mn><mrow><mi>T</mi><mo>+</mo><mn>40</mn></mrow></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math> 6T = 2040 - 240 = 1800<br><math display=\"inline\"><mo>&#8658;</mo></math> T = 300 sec<br>&there4; Tarun&rsquo;s speed = <math display=\"inline\"><mfrac><mrow><mn>2400</mn></mrow><mrow><mn>300</mn></mrow></mfrac></math> = 8m/s</p>",
                    solution_hi: "<p>52.(a) दिया है , हरि की गति = 6m/s<br>60 सेकंड में तय की गई दूरी = 360m<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742361739446.png\" alt=\"rId53\" width=\"343\" height=\"50\"><br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; Tarun&nbsp; &nbsp; Hari <br>दूरी <math display=\"inline\"><mo>&#8594;</mo></math> 2400&nbsp; &nbsp; 2040<br>समय <math display=\"inline\"><mo>&#8594;</mo></math> T&nbsp; &nbsp; &nbsp; &nbsp;T + 40<br>गति = <math style=\"font-family: Verdana;\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>&#2342;&#2370;&#2352;&#2368;</mi><mi>&#2360;&#2350;&#2351;</mi></mfrac></math> &rArr; 6 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2040</mn><mrow><mi>T</mi><mo>+</mo><mn>40</mn></mrow></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math>6T = 2040 - 240 = 1800<br><math display=\"inline\"><mo>&#8658;</mo></math>T = 300 sec<br>&there4; तरूण की गति = <math display=\"inline\"><mfrac><mrow><mn>2400</mn></mrow><mrow><mn>300</mn></mrow></mfrac></math> = 8m/s</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "53",
                    section: "17",
                    question_en: "<p>53. The angle of elevation of a ladder leaning against a wall is <math display=\"inline\"><msup><mrow><mn>60</mn></mrow><mrow><mi>o</mi></mrow></msup></math> and the foot of the ladder is 15 m away from the wall . The length of the ladder is:</p>",
                    question_hi: "<p>53. एक दीवार के खिलाफ झुकी हुई सीढ़ी का उन्नयन कोण 60&deg; है और सीढ़ी का पैर दीवार से 15 मीटर दूर है। सीढ़ी की लंबाई है:</p>",
                    options_en: [
                        "<p>15 m</p>",
                        "<p>7.5 m</p>",
                        "<p>30 m</p>",
                        "<p>15<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math> m</p>"
                    ],
                    options_hi: [
                        "<p>15 m</p>",
                        "<p>7.5 m</p>",
                        "<p>30 m</p>",
                        "<p>15<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math> m</p>"
                    ],
                    solution_en: "<p>53.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742361739548.png\" alt=\"rId54\" width=\"106\" height=\"113\"><br>Cos 60&deg; = <math display=\"inline\"><mfrac><mrow><mn>15</mn></mrow><mrow><mi>A</mi><mi>C</mi></mrow></mfrac></math><br>&rArr;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>15</mn><mrow><mi>A</mi><mi>C</mi></mrow></mfrac></math><br>&rArr;&nbsp; AC = 30 m</p>",
                    solution_hi: "<p>53.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742361739548.png\" alt=\"rId54\" width=\"106\" height=\"113\"><br>Cos 60&deg; = <math display=\"inline\"><mfrac><mrow><mn>15</mn></mrow><mrow><mi>A</mi><mi>C</mi></mrow></mfrac></math><br>&rArr;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>15</mn><mrow><mi>A</mi><mi>C</mi></mrow></mfrac></math><br>&rArr;&nbsp; AC = 30 m</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "54",
                    section: "17",
                    question_en: "<p>54. Which composite number can divide the sum of the first 12 natural numbers?</p>",
                    question_hi: "<p>54. कौन-सी भाज्य संख्या प्रथम 12 प्राकृत संख्याओं के योग को विभाजित कर सकती है?</p>",
                    options_en: [
                        "<p>6</p>",
                        "<p>12</p>",
                        "<p>4</p>",
                        "<p>8</p>"
                    ],
                    options_hi: [
                        "<p>6</p>",
                        "<p>12</p>",
                        "<p>4</p>",
                        "<p>8</p>"
                    ],
                    solution_en: "<p>54.(a)<br>Sum of first n&nbsp;natural number =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>n</mi><mo>(</mo><mi>n</mi><mo>+</mo><mn>1</mn><mo>)</mo></mrow><mn>2</mn></mfrac></math><br>Sum of first 12 natural number = <math display=\"inline\"><mfrac><mrow><mn>12</mn><mo>(</mo><mn>12</mn><mo>+</mo><mn>1</mn><mo>)</mo></mrow><mrow><mn>2</mn></mrow></mfrac></math>= 6 &times; 13<br>Hence, 6 can divide the sum of first 12 natural numbers.</p>",
                    solution_hi: "<p>54.(a)<br>प्रथम n प्राकृत संख्या का योग = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>n</mi><mo>(</mo><mi>n</mi><mo>+</mo><mn>1</mn><mo>)</mo></mrow><mn>2</mn></mfrac></math><br>प्रथम 12 प्राकृत संख्याओं का योग = <math display=\"inline\"><mfrac><mrow><mn>12</mn><mo>(</mo><mn>12</mn><mo>+</mo><mn>1</mn><mo>)</mo></mrow><mrow><mn>2</mn></mrow></mfrac></math>= 6 &times; 13<br>अतः, प्रथम 12 प्राकृतिक संख्याओं के योग को 6 से विभाजित किया जा सकता है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "55",
                    section: "17",
                    question_en: "<p>55. The cost of painting a spherical vessel of diameter 14 cm is ₹21,560. What is the cost of painting (in ₹) per square centimetre ? (Use &pi; = <math display=\"inline\"><mfrac><mrow><mn>22</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math>)</p>",
                    question_hi: "<p>55. 14 cm व्यास वाले एक गोलाकार बर्तन को पेंट करने की लागत ₹21,560 है। प्रति वर्ग सेंटीमीटर पेंटिंग की लागत (₹ में) क्या है? ( &pi; = <math display=\"inline\"><mfrac><mrow><mn>22</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> उपयोग करें)</p>",
                    options_en: [
                        "<p>30</p>",
                        "<p>32</p>",
                        "<p>28</p>",
                        "<p>35</p>"
                    ],
                    options_hi: [
                        "<p>30</p>",
                        "<p>32</p>",
                        "<p>28</p>",
                        "<p>35</p>"
                    ],
                    solution_en: "<p>55.(d)<br>T.S.A. of spherical vessel = 4<math display=\"inline\"><mi>&#960;</mi><msup><mrow><mi>r</mi></mrow><mrow><mn>2</mn></mrow></msup></math> = 4 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mn>7</mn></mfrac></math>&times; 7 &times; 7 = 616&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>c</mi><msup><mi>m</mi><mn>2</mn></msup></math><br>Cost per square centimeter = <math display=\"inline\"><mfrac><mrow><mi>t</mi><mi>o</mi><mi>t</mi><mi>a</mi><mi>l</mi><mi>&#160;</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>t</mi></mrow><mrow><mi>T</mi><mo>.</mo><mi>S</mi><mo>.</mo><mi>A</mi></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>21</mn><mo>,</mo><mn>560</mn></mrow><mn>616</mn></mfrac></math> = 35 Rs.</p>",
                    solution_hi: "<p>55.(d)<br>गोलाकार बर्तन का कुल सतह क्षेत्रफल = 4<math display=\"inline\"><mi>&#960;</mi><msup><mrow><mi>r</mi></mrow><mrow><mn>2</mn></mrow></msup></math> = 4 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mn>7</mn></mfrac></math>&times; 7 &times; 7 = 616&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>c</mi><msup><mi>m</mi><mn>2</mn></msup></math><br>प्रति वर्ग सेंटीमीटर लागत = <math style=\"font-family:Verdana\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2325;&#2369;&#2354;</mi><mi>&#160;</mi><mi>&#2354;&#2366;&#2327;&#2340;</mi></mrow><mrow><mi>&#2325;&#2369;&#2354;</mi><mi>&#160;</mi><mi>&#2360;&#2340;&#2361;</mi><mi>&#160;</mi><mi>&#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;&#2347;&#2354;</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>21</mn><mo>,</mo><mn>560</mn></mrow><mn>616</mn></mfrac></math>= 35 Rs.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "56",
                    section: "17",
                    question_en: "<p>56. The Least Common Multiple of two prime numbers x and y, (x&lt;y) is 145. Then what is the value of (10x - y)?</p>\n<p>&nbsp;</p>",
                    question_hi: "<p>56.<strong> </strong>दो अभाज्य संख्याओं x और y, (x&lt;y) का लघुत्तम समापवर्त्य 145 है। तो (10x - y) का मान क्या है?<y) का=\"\" लघुत्तम=\"\" समापवर्त्य=\"\" 145=\"\" है।=\"\" तो=\"\" (10x=\"\" -=\"\" y)=\"\" मान=\"\" क्या=\"\" है?<=\"\" body=\"\"></y)></p>",
                    options_en: [
                        "<p>11</p>",
                        "<p>21</p>",
                        "<p>27</p>",
                        "<p>18</p>"
                    ],
                    options_hi: [
                        "<p>11</p>",
                        "<p>21</p>",
                        "<p>27</p>",
                        "<p>18</p>"
                    ],
                    solution_en: "<p>56.(b) According to question,<br><math display=\"inline\"><mi>x</mi></math> &times; y = 145 = 5 &times; 29 <br>(5 and 29 are prime no&rsquo;s)<br><math display=\"inline\"><mo>&#8658;</mo></math> x = 5, y = 29<br>10<math display=\"inline\"><mi>x</mi></math> - y = 10 &times; 5 - 29 = 21</p>",
                    solution_hi: "<p>56.(b) प्रश्न के अनुसार, <br><math display=\"inline\"><mi>x</mi></math> &times; y = 145 = 5 &times; 29 (5 और 29 अभाज्य संख्याएँ हैं)<br><math display=\"inline\"><mo>&#8658;</mo></math> x = 5, y = 29<br>10<math display=\"inline\"><mi>x</mi></math> - y = 10 &times; 5 - 29 = 21</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "57",
                    section: "17",
                    question_en: "<p>57. Simplify <br>[3<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math>&times; { 2<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>4</mn></mfrac></math> + 3.3 (5<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math> + 3<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math> - 2<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math>)}]</p>",
                    question_hi: "<p>57. निम्नलिखित को सरल कीजिए।<br>[3<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math>&times; { 2<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>4</mn></mfrac></math> + 3.3 (5<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math> + 3<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math> - 2<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math>)}]</p>",
                    options_en: [
                        "<p>80.41</p>",
                        "<p>46.29</p>",
                        "<p>53.25</p>",
                        "<p>70.25</p>"
                    ],
                    options_hi: [
                        "<p>80.41</p>",
                        "<p>46.29</p>",
                        "<p>53.25</p>",
                        "<p>70.25</p>"
                    ],
                    solution_en: "<p>57.(a)<br>[3<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math>&times; { 2<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>4</mn></mfrac></math> + 3.3 (5<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math> + 3<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math> - 2<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math>)}]<br>[<math display=\"inline\"><mfrac><mrow><mn>17</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math>&times; { <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>11</mn><mn>4</mn></mfrac></math>+ 3.3 (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>16</mn><mn>3</mn></mfrac></math>+ <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>10</mn><mn>3</mn></mfrac></math>- <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>3</mn></mfrac></math>)}]<br>[<math display=\"inline\"><mfrac><mrow><mn>17</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math>&times; { <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>11</mn><mn>4</mn></mfrac></math>+ 3.3 (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>19</mn><mn>3</mn></mfrac></math>)}]<br>[<math display=\"inline\"><mfrac><mrow><mn>17</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math>&times; { <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>11</mn><mn>4</mn></mfrac></math>+ 1.1 &times; 19)}]<br>[<math display=\"inline\"><mfrac><mrow><mn>17</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> &times; { 2.75 + 20.9)}]<br>[<math display=\"inline\"><mfrac><mrow><mn>17</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> &times; 23.65]<br>[17 &times; 4.73] = 80.41</p>",
                    solution_hi: "<p>57.(a)<br>[3<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math>&times; { 2<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>4</mn></mfrac></math> + 3.3 (5<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math> + 3<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math> - 2<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math>)}]<br>[<math display=\"inline\"><mfrac><mrow><mn>17</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math>&times; { <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>11</mn><mn>4</mn></mfrac></math>+ 3.3 (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>16</mn><mn>3</mn></mfrac></math>+ <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>10</mn><mn>3</mn></mfrac></math>- <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>3</mn></mfrac></math>)}]<br>[<math display=\"inline\"><mfrac><mrow><mn>17</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math>&times; { <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>11</mn><mn>4</mn></mfrac></math>+ 3.3 (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>19</mn><mn>3</mn></mfrac></math>)}]<br>[<math display=\"inline\"><mfrac><mrow><mn>17</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math>&times; { <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>11</mn><mn>4</mn></mfrac></math>+ 1.1 &times; 19)}]<br>[<math display=\"inline\"><mfrac><mrow><mn>17</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> &times; { 2.75 + 20.9)}]<br>[<math display=\"inline\"><mfrac><mrow><mn>17</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> &times; 23.65]<br>[17 &times; 4.73] = 80.41</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "58",
                    section: "17",
                    question_en: "<p>58. A cone of slant height 4.2 units has a lateral surface area 13.2 <math display=\"inline\"><msup><mrow><mi>u</mi><mi>n</mi><mi>i</mi><mi>t</mi><mi>s</mi></mrow><mrow><mn>2</mn></mrow></msup></math>. The radius of the base is:</p>",
                    question_hi: "<p>58. तिर्यक ऊंचाई 4.2 इकाई के एक शंकु का पार्श्व पृष्ठीय क्षेत्रफल 13.2 <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#160;</mo><mi>&#2311;&#2325;&#2366;&#2312;</mi><msup><mo>&#160;</mo><mn>2</mn></msup></math> है। आधार की त्रिज्या ज्ञात कीजिए</p>",
                    options_en: [
                        "<p>1 unit</p>",
                        "<p>1.5 units</p>",
                        "<p>2.5 units</p>",
                        "<p>2 units</p>"
                    ],
                    options_hi: [
                        "<p>1 इकाई</p>",
                        "<p>1.5 इकाई</p>",
                        "<p>2.5 इकाई</p>",
                        "<p>2 इकाई</p>"
                    ],
                    solution_en: "<p>58.(a)<br>Lateral surface area = <math display=\"inline\"><mi>&#960;</mi><mi>r</mi><mi>l</mi></math><br>13.2 = <math display=\"inline\"><mfrac><mrow><mn>22</mn></mrow><mrow><mn>7</mn></mrow></mfrac><mo>&#215;</mo><mi>&#160;</mi><mi>r</mi><mi>&#160;</mi><mo>&#215;</mo></math> 4.2 <br>13.2 =22<math display=\"inline\"><mo>&#215;</mo><mi>r</mi><mo>&#215;</mo></math>0.6<br><math display=\"inline\"><mi>r</mi></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>13</mn><mo>.</mo><mn>2</mn></mrow><mrow><mn>13</mn><mo>.</mo><mn>2</mn></mrow></mfrac></math> = 1 unit</p>",
                    solution_hi: "<p>58.(a)<br>पार्श्व सतह क्षेत्रफल = <math display=\"inline\"><mi>&#960;</mi><mi>r</mi><mi>l</mi></math><br>13.2 = <math display=\"inline\"><mfrac><mrow><mn>22</mn></mrow><mrow><mn>7</mn></mrow></mfrac><mo>&#215;</mo><mi>r</mi><mo>&#215;</mo></math>4.2 <br>13.2 =22<math display=\"inline\"><mo>&#215;</mo><mi>r</mi><mo>&#215;</mo></math>0.6<br><math display=\"inline\"><mi>r</mi></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>13</mn><mo>.</mo><mn>2</mn></mrow><mrow><mn>13</mn><mo>.</mo><mn>2</mn></mrow></mfrac></math>= 1 इकाई</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "59",
                    section: "17",
                    question_en: "<p>59. if tan <math display=\"inline\"><mi>&#952;</mi></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>8</mn></mfrac></math>, then find the value of <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>1</mn><mo>+</mo><mi>cos</mi><mi>&#952;</mi><mo>)</mo><mo>(</mo><mn>1</mn><mo>-</mo><mi>cos</mi><mi>&#952;</mi><mo>)</mo></mrow><mrow><mo>(</mo><mn>1</mn><mo>+</mo><mi>sin</mi><mi>&#952;</mi><mo>)</mo><mo>(</mo><mn>1</mn><mo>-</mo><mi>sin</mi><mi>&#952;</mi><mo>)</mo></mrow></mfrac></math>.</p>",
                    question_hi: "<p>59. यदि tan <math display=\"inline\"><mi>&#952;</mi></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>8</mn></mfrac></math>है, तो <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>1</mn><mo>+</mo><mi>cos</mi><mi>&#952;</mi><mo>)</mo><mo>(</mo><mn>1</mn><mo>-</mo><mi>cos</mi><mi>&#952;</mi><mo>)</mo></mrow><mrow><mo>(</mo><mn>1</mn><mo>+</mo><mi>sin</mi><mi>&#952;</mi><mo>)</mo><mo>(</mo><mn>1</mn><mo>-</mo><mi>sin</mi><mi>&#952;</mi><mo>)</mo></mrow></mfrac></math> का मान ज्ञात कीजिए।</p>",
                    options_en: [
                        "<p><math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>64</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>25</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>25</mn></mrow><mrow><mn>64</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>64</mn></mrow><mrow><mn>25</mn></mrow></mfrac></math></p>"
                    ],
                    options_hi: [
                        "<p><math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>64</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>25</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>25</mn></mrow><mrow><mn>64</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>64</mn></mrow><mrow><mn>25</mn></mrow></mfrac></math></p>"
                    ],
                    solution_en: "<p>59.(c) <strong>Given,</strong> tan <math display=\"inline\"><mi>&#952;</mi></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>8</mn></mfrac></math> <br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>1</mn><mo>+</mo><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#952;</mi><mo>)</mo><mo>(</mo><mn>1</mn><mo>-</mo><mi>cos</mi><mi>&#952;</mi><mo>)</mo></mrow><mrow><mo>(</mo><mn>1</mn><mo>+</mo><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#952;</mi><mo>)</mo><mo>(</mo><mn>1</mn><mo>-</mo><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#952;</mi><mo>)</mo></mrow></mfrac></math><br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mo>-</mo><msup><mrow><mi>c</mi><mi>o</mi><mi>s</mi></mrow><mn>2</mn></msup><mi>&#952;</mi></mrow><mrow><mn>1</mn><mo>-</mo><msup><mrow><mi>s</mi><mi>i</mi><mi>n</mi></mrow><mn>2</mn></msup><mi>&#952;</mi></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi>sin</mi><mn>2</mn></msup><mi>&#952;</mi></mrow><mrow><msup><mi>cos</mi><mn>2</mn></msup><mi>&#952;</mi></mrow></mfrac></math> = tan<sup>2</sup>&theta; =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mfrac><mn>5</mn><mn>8</mn></mfrac><mo>)</mo></mrow><mn>2</mn></msup></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>25</mn><mn>64</mn></mfrac></math></p>",
                    solution_hi: "<p>59.(c) <strong>दिया है ,</strong> tan <math display=\"inline\"><mi>&#952;</mi></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>8</mn></mfrac></math> <br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>1</mn><mo>+</mo><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#952;</mi><mo>)</mo><mo>(</mo><mn>1</mn><mo>-</mo><mi>cos</mi><mi>&#952;</mi><mo>)</mo></mrow><mrow><mo>(</mo><mn>1</mn><mo>+</mo><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#952;</mi><mo>)</mo><mo>(</mo><mn>1</mn><mo>-</mo><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#952;</mi><mo>)</mo></mrow></mfrac></math><br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mo>-</mo><msup><mrow><mi>c</mi><mi>o</mi><mi>s</mi></mrow><mn>2</mn></msup><mi>&#952;</mi></mrow><mrow><mn>1</mn><mo>-</mo><msup><mrow><mi>s</mi><mi>i</mi><mi>n</mi></mrow><mn>2</mn></msup><mi>&#952;</mi></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi>sin</mi><mn>2</mn></msup><mi>&#952;</mi></mrow><mrow><msup><mi>cos</mi><mn>2</mn></msup><mi>&#952;</mi></mrow></mfrac></math> = tan<sup>2</sup>&theta; =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mfrac><mn>5</mn><mn>8</mn></mfrac><mo>)</mo></mrow><mn>2</mn></msup></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>25</mn><mn>64</mn></mfrac></math></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "60",
                    section: "17",
                    question_en: "<p>60. R wishes to use a stick of length 6 units as common internal tangent to two circles of&nbsp;radius two units and three units. What can be the maximum distance (in units)&nbsp;between the centres of the circle?</p>",
                    question_hi: "<p>60. R, दो इकाई और तीन इकाई त्रिज्या वाले दो वृत्तों की उभयनिष्ठ आंतरिक स्पर्श रेखा के रूप में 6 इकाई&nbsp;लंबाई की एक छड़ी का उपयोग करना चाहता है। वृत्त के केन्द्रों के बीच अधिकतम दूरी कितनी हो सकती है?</p>",
                    options_en: [
                        "<p><math display=\"inline\"><msqrt><mn>67</mn></msqrt></math></p>",
                        "<p><math display=\"inline\"><msqrt><mn>33</mn></msqrt></math></p>",
                        "<p><math display=\"inline\"><msqrt><mn>37</mn></msqrt></math></p>",
                        "<p><math display=\"inline\"><msqrt><mn>61</mn></msqrt></math></p>"
                    ],
                    options_hi: [
                        "<p><math display=\"inline\"><msqrt><mn>67</mn></msqrt></math></p>",
                        "<p><math display=\"inline\"><msqrt><mn>33</mn></msqrt></math></p>",
                        "<p><math display=\"inline\"><msqrt><mn>37</mn></msqrt></math></p>",
                        "<p><math display=\"inline\"><msqrt><mn>61</mn></msqrt></math></p>"
                    ],
                    solution_en: "<p>60.(d) <br><strong>Concept used :</strong> <br>For maximum distance between the centres of the circle, we need to draw the transverse common tangent .<br>For the minimum distance between the centres of the circle , we need to draw the direct common tangent.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742361739657.png\" alt=\"rId55\" width=\"214\" height=\"122\"><br>Let the maximum distance between the centres be &lsquo;L&rsquo;<br>Transverse common tangent = <math display=\"inline\"><msqrt><msup><mrow><mi>L</mi></mrow><mrow><mn>2</mn></mrow></msup><mo>-</mo><mo>(</mo><mi>R</mi><mo>+</mo><mi>r</mi><msup><mrow><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup></msqrt></math><br>6 = <math display=\"inline\"><msqrt><msup><mrow><mi>L</mi></mrow><mrow><mn>2</mn></mrow></msup><mo>-</mo><mo>(</mo><mn>3</mn><mo>+</mo><mn>2</mn><msup><mrow><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup></msqrt></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mi>L</mi><mn>2</mn></msup><mo>-</mo><msup><mn>5</mn><mn>2</mn></msup></msqrt></math><br>36 = <math display=\"inline\"><msup><mrow><mi>L</mi></mrow><mrow><mn>2</mn></mrow></msup></math> - 25<br>61 = <math display=\"inline\"><msup><mrow><mi>L</mi></mrow><mrow><mn>2</mn></mrow></msup></math><br><math display=\"inline\"><mo>&#8658;</mo></math> L = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>61</mn></msqrt></math> Units</p>",
                    solution_hi: "<p>60.(d) <br><strong>प्रयुक्त अवधारणा:</strong> <br>वृत्त के केन्द्रों के बीच अधिकतम दूरी के लिए, हमें अनुप्रस्थ उभयनिष्ठ स्पर्श रेखा खींचनी होगी।<br>वृत्त के केन्द्रों के बीच न्यूनतम दूरी के लिए, हमें सीधी उभयनिष्ठ स्पर्शरेखा खींचनी होगी।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742361739657.png\" alt=\"rId55\" width=\"214\" height=\"122\"><br>माना केन्द्रों के बीच अधिकतम दूरी \'L\' है<br>अनुप्रस्थ उभयनिष्ठ स्पर्शरेखा = <math display=\"inline\"><msqrt><msup><mrow><mi>L</mi></mrow><mrow><mn>2</mn></mrow></msup><mo>-</mo><mo>(</mo><mi>R</mi><mo>+</mo><mi>r</mi><msup><mrow><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup></msqrt></math><br>6 = <math display=\"inline\"><msqrt><msup><mrow><mi>L</mi></mrow><mrow><mn>2</mn></mrow></msup><mo>-</mo><mo>(</mo><mn>3</mn><mo>+</mo><mn>2</mn><msup><mrow><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup></msqrt></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mi>L</mi><mn>2</mn></msup><mo>-</mo><msup><mn>5</mn><mn>2</mn></msup></msqrt></math><br>36 = <math display=\"inline\"><msup><mrow><mi>L</mi></mrow><mrow><mn>2</mn></mrow></msup></math> - 25<br>61 = <math display=\"inline\"><msup><mrow><mi>L</mi></mrow><mrow><mn>2</mn></mrow></msup></math><br><math display=\"inline\"><mo>&#8658;</mo></math> L = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>61</mn></msqrt></math> इकाई</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "61",
                    section: "17",
                    question_en: "<p>61. A retailer purchased a batch of goods for ₹5,000. Due to a manufacturing defect, 5% of the goods were damaged. If the retailer wants to make a profit of 20% on the remaining goods, at what price should the undamaged goods be sold?</p>",
                    question_hi: "<p>61. एक खुदरा विक्रेता ₹5,000 में सामान का एक बैच खरीदता है। विनिर्माण दोष के कारण 5% सामान क्षतिग्रस्त हो जाता है। यदि खुदरा विक्रेता शेष सामान पर 20% का लाभ अर्जित करना चाहता है, तो अक्षतिग्रस्त सामान को किस मूल्य पर बेचा जाना चाहिए?</p>",
                    options_en: [
                        "<p>₹5,800</p>",
                        "<p>₹5,700</p>",
                        "<p>₹5,900</p>",
                        "<p>₹6,000</p>"
                    ],
                    options_hi: [
                        "<p>₹5,800</p>",
                        "<p>₹5,700</p>",
                        "<p>₹5,900</p>",
                        "<p>₹6,000</p>"
                    ],
                    solution_en: "<p>61.(b)<br>Required price = 5000 &times; <math display=\"inline\"><mfrac><mrow><mn>19</mn></mrow><mrow><mn>20</mn></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>6</mn><mn>5</mn></mfrac></math>= ₹5,700</p>",
                    solution_hi: "<p>61.(b)<br>आवश्यक मूल्य = 5000 &times; <math display=\"inline\"><mfrac><mrow><mn>19</mn></mrow><mrow><mn>20</mn></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>6</mn><mn>5</mn></mfrac></math>= ₹5,700</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "62",
                    section: "17",
                    question_en: "<p>62. The value of <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mi>s</mi><mi>i</mi><mi>n</mi></mrow><mn>2</mn></msup></math>30&deg; - <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mi>s</mi><mi>i</mi><mi>n</mi></mrow><mn>2</mn></msup></math>40&deg; + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mi>s</mi><mi>i</mi><mi>n</mi></mrow><mn>2</mn></msup></math>45&deg; - <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mi>s</mi><mi>i</mi><mi>n</mi></mrow><mn>2</mn></msup></math>55&deg; - <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mi>s</mi><mi>i</mi><mi>n</mi></mrow><mn>2</mn></msup></math>35&deg; + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mi>s</mi><mi>i</mi><mi>n</mi></mrow><mn>2</mn></msup></math>45&deg; - <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mi>s</mi><mi>i</mi><mi>n</mi></mrow><mn>2</mn></msup></math>50&deg; + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mi>s</mi><mi>i</mi><mi>n</mi></mrow><mn>2</mn></msup></math>60&deg; is:</p>",
                    question_hi: "<p>62. <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mi>s</mi><mi>i</mi><mi>n</mi></mrow><mn>2</mn></msup></math>30&deg; - <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mi>s</mi><mi>i</mi><mi>n</mi></mrow><mn>2</mn></msup></math>40&deg; + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mi>s</mi><mi>i</mi><mi>n</mi></mrow><mn>2</mn></msup></math>45&deg; - <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mi>s</mi><mi>i</mi><mi>n</mi></mrow><mn>2</mn></msup></math>55&deg; - <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mi>s</mi><mi>i</mi><mi>n</mi></mrow><mn>2</mn></msup></math>35&deg; + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mi>s</mi><mi>i</mi><mi>n</mi></mrow><mn>2</mn></msup></math>45&deg; - <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mi>s</mi><mi>i</mi><mi>n</mi></mrow><mn>2</mn></msup></math>50&deg; + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mi>s</mi><mi>i</mi><mi>n</mi></mrow><mn>2</mn></msup></math>60&deg; का मान ज्ञात कीजिए।</p>",
                    options_en: [
                        "<p>1</p>",
                        "<p>2</p>",
                        "<p>4</p>",
                        "<p>0</p>"
                    ],
                    options_hi: [
                        "<p>1</p>",
                        "<p>2</p>",
                        "<p>4</p>",
                        "<p>0</p>"
                    ],
                    solution_en: "<p>62.(d) <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mi>s</mi><mi>i</mi><mi>n</mi></mrow><mn>2</mn></msup></math>30&deg; - <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mi>s</mi><mi>i</mi><mi>n</mi></mrow><mn>2</mn></msup></math>40&deg; + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mi>s</mi><mi>i</mi><mi>n</mi></mrow><mn>2</mn></msup></math>45&deg; - <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mi>s</mi><mi>i</mi><mi>n</mi></mrow><mn>2</mn></msup></math>55&deg; - <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mi>s</mi><mi>i</mi><mi>n</mi></mrow><mn>2</mn></msup></math>35&deg; + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mi>s</mi><mi>i</mi><mi>n</mi></mrow><mn>2</mn></msup></math>45&deg; - <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mi>s</mi><mi>i</mi><mi>n</mi></mrow><mn>2</mn></msup></math>50&deg; + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mi>s</mi><mi>i</mi><mi>n</mi></mrow><mn>2</mn></msup></math>60&deg;<br>= (<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mi>s</mi><mi>i</mi><mi>n</mi></mrow><mn>2</mn></msup></math>30&deg; + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mi>s</mi><mi>i</mi><mi>n</mi></mrow><mn>2</mn></msup></math>60&deg;) - (<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mi>s</mi><mi>i</mi><mi>n</mi></mrow><mn>2</mn></msup></math>40&deg; + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mi>s</mi><mi>i</mi><mi>n</mi></mrow><mn>2</mn></msup></math>50&deg;) + (<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mi>s</mi><mi>i</mi><mi>n</mi></mrow><mn>2</mn></msup></math>45&deg; + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mi>s</mi><mi>i</mi><mi>n</mi></mrow><mn>2</mn></msup></math>45&deg;) - (<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mi>s</mi><mi>i</mi><mi>n</mi></mrow><mn>2</mn></msup></math>55&deg;+ <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mi>s</mi><mi>i</mi><mi>n</mi></mrow><mn>2</mn></msup></math>35&deg;)<br>= 1 - 1 + 1 - 1 (∵ <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mi>s</mi><mi>i</mi><mi>n</mi></mrow><mn>2</mn></msup><mi>&#952;</mi></math> + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mi>s</mi><mi>i</mi><mi>n</mi></mrow><mn>2</mn></msup></math>(90&deg; - &theta;) = 1)<br>= 0</p>",
                    solution_hi: "<p>62.(d) <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mi>s</mi><mi>i</mi><mi>n</mi></mrow><mn>2</mn></msup></math>30&deg; - <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mi>s</mi><mi>i</mi><mi>n</mi></mrow><mn>2</mn></msup></math>40&deg; + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mi>s</mi><mi>i</mi><mi>n</mi></mrow><mn>2</mn></msup></math>45&deg; - <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mi>s</mi><mi>i</mi><mi>n</mi></mrow><mn>2</mn></msup></math>55&deg; - <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mi>s</mi><mi>i</mi><mi>n</mi></mrow><mn>2</mn></msup></math>35&deg; + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mi>s</mi><mi>i</mi><mi>n</mi></mrow><mn>2</mn></msup></math>45&deg; - <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mi>s</mi><mi>i</mi><mi>n</mi></mrow><mn>2</mn></msup></math>50&deg; + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mi>s</mi><mi>i</mi><mi>n</mi></mrow><mn>2</mn></msup></math>60&deg;<br>= (<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mi>s</mi><mi>i</mi><mi>n</mi></mrow><mn>2</mn></msup></math>30&deg; + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mi>s</mi><mi>i</mi><mi>n</mi></mrow><mn>2</mn></msup></math>60&deg;) - (<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mi>s</mi><mi>i</mi><mi>n</mi></mrow><mn>2</mn></msup></math>40&deg; + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mi>s</mi><mi>i</mi><mi>n</mi></mrow><mn>2</mn></msup></math>50&deg;) + (<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mi>s</mi><mi>i</mi><mi>n</mi></mrow><mn>2</mn></msup></math>45&deg; + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mi>s</mi><mi>i</mi><mi>n</mi></mrow><mn>2</mn></msup></math>45&deg;) - (<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mi>s</mi><mi>i</mi><mi>n</mi></mrow><mn>2</mn></msup></math>55&deg;+ <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mi>s</mi><mi>i</mi><mi>n</mi></mrow><mn>2</mn></msup></math>35&deg;)<br>= 1 - 1 + 1 - 1 (∵ <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mi>s</mi><mi>i</mi><mi>n</mi></mrow><mn>2</mn></msup><mi>&#952;</mi></math> + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mi>s</mi><mi>i</mi><mi>n</mi></mrow><mn>2</mn></msup></math>(90&deg; - &theta;) = 1)<br>= 0</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "63",
                    section: "17",
                    question_en: "<p>63. The difference between the compound interest (compounding half yearly) for 1 year and the simple interest for 1 year on a certain sum lent out at the annual rate of 30 percent is Rs. 337.5. What is the sum?</p>",
                    question_hi: "<p>63. 30 प्रतिशत की वार्षिक दर से ऋण पर दी गई एक निश्चित धनराशि पर 1 वर्ष के लिए चक्रवृद्धि ब्याज (अर्धवार्षिक रूप से संयोजित) और 1 वर्ष के लिए साधारण ब्याज के बीच का अंतर 337.5 रुपए है। वह धनराशि कितनी है?</p>",
                    options_en: [
                        "<p>Rs. 15000</p>",
                        "<p>Rs. 18500</p>",
                        "<p>Rs. 17000</p>",
                        "<p>Rs. 17700</p>"
                    ],
                    options_hi: [
                        "<p>15000 रुपए</p>",
                        "<p>18500 रुपए</p>",
                        "<p>17000 रुपए</p>",
                        "<p>17700 रुपए</p>"
                    ],
                    solution_en: "<p>63.(a)<br>Rate for <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math>year = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>30</mn><mn>2</mn></mfrac></math>= 15%<br>CI for 1 year (2 cycle) at 15% = (15+15 + <math display=\"inline\"><mfrac><mrow><mn>1</mn><msup><mrow><mn>5</mn></mrow><mrow><mn>2</mn></mrow></msup></mrow><mrow><mn>100</mn></mrow></mfrac></math>)% = 30+2.25 = 32.25%<br>SI for 1 year = 30%<br>(CI - SI) for 1 year = 32.25% - 30% = 2.25% of P<br>337.5 = <math display=\"inline\"><mfrac><mrow><mn>2</mn><mo>.</mo><mn>25</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math>P<br>P = <math display=\"inline\"><mfrac><mrow><mn>337</mn><mo>.</mo><mn>5</mn><mo>&#215;</mo><mn>100</mn></mrow><mrow><mn>2</mn><mo>.</mo><mn>25</mn></mrow></mfrac></math> = ₹15,000</p>",
                    solution_hi: "<p>63.(a)<br><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> वर्ष के लिए दर = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>30</mn><mn>2</mn></mfrac></math>= 15%<br>15% पर 1 वर्ष(2 चक्र) के लिए चक्रवृद्धि ब्याज = (15+15 + <math display=\"inline\"><mfrac><mrow><mn>1</mn><msup><mrow><mn>5</mn></mrow><mrow><mn>2</mn></mrow></msup></mrow><mrow><mn>100</mn></mrow></mfrac></math>)% = 30+2.25 = 32.25%<br>1 वर्ष के लिए साधारण ब्याज = 30%<br>(चक्रवृद्धि ब्याज - साधारण ब्याज) 1 वर्ष के लिए = 32.25% - 30% = मूलधन का 2.25% <br>337.5 = मूलधन &times; <math display=\"inline\"><mfrac><mrow><mn>2</mn><mo>.</mo><mn>25</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math><br>मूलधन = <math display=\"inline\"><mfrac><mrow><mn>337</mn><mo>.</mo><mn>5</mn><mo>&#215;</mo><mn>100</mn></mrow><mrow><mn>2</mn><mo>.</mo><mn>25</mn></mrow></mfrac></math> = ₹15,000</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "64",
                    section: "17",
                    question_en: "<p>64. In ∆PQR, S and T are the midpoints of the sides PQ and PR, respectively. The length of the side QR is 12 cm. If ST is parallel to QR, then find the length (in cm) of ST.</p>",
                    question_hi: "<p>64. ∆PQR में, S और T क्रमशः भुजाओं PQ और PR के मध्यबिंदु हैं। भुजा QR की लंबाई 12 cm है। यदि ST, QR के समांतर हो, तो ST की लंबाई (cm में) ज्ञात कीजिए।</p>",
                    options_en: [
                        "<p>4</p>",
                        "<p>10</p>",
                        "<p>6</p>",
                        "<p>8</p>"
                    ],
                    options_hi: [
                        "<p>4</p>",
                        "<p>10</p>",
                        "<p>6</p>",
                        "<p>8</p>"
                    ],
                    solution_en: "<p>64.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742361739783.png\" alt=\"rId56\" width=\"144\" height=\"144\"><br>S and T are the mid-point of PQ and PR respectively.<br>Hence, by mid-point theorem <br>ST = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> of QR<br>ST = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> &times; 12 = 6 cm</p>",
                    solution_hi: "<p>64.(c) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742361739783.png\" alt=\"rId56\" width=\"144\" height=\"144\"><br>S और T क्रमशः PQ और PR के मध्य-बिंदु हैं।<br>अतः, मध्य-बिंदु प्रमेय द्वारा <br>ST = QR का <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> <br>ST = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> &times; 12 = 6 cm</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "65",
                    section: "17",
                    question_en: "<p>65. In a class with a certain number of students, if one student weighing 30 kg is added, then the average weight of the class increases by 1 kg. If one more student weighing 30 kg is added, then the average weight of the class increases by 1.5 kg over the original average. What is the original average weight (in kg) of the class ?</p>",
                    question_hi: "<p>65. छात्रों की एक निश्चित संख्या वाली कक्षा में, यदि 30 kg भार वाले एक छात्र को शामिल कर लिया जाता है, तो कक्षा के औसत भार में 1 kg की वृद्धि हो जाती है। यदि 30 kg भार वाले एक और छात्र को शामिल कर लिया जाता है, तो कक्षा के औसत भार में मूल औसत से 1.5 kg की वृद्धि हो जाती है। कक्षा का मूल औसत भार (kg में) कितना है ?</p>",
                    options_en: [
                        "<p>26.5</p>",
                        "<p>25.5</p>",
                        "<p>27</p>",
                        "<p>24</p>"
                    ],
                    options_hi: [
                        "<p>26.5</p>",
                        "<p>25.5</p>",
                        "<p>27</p>",
                        "<p>24</p>"
                    ],
                    solution_en: "<p>65.(c)<br>Let number of student be n and their average weight be x kg<br>According to the question,<br>one student weighing 30 kg is added, then the average weight of the class increases by 1 kg<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>n</mi><mi>x</mi><mo>+</mo><mn>30</mn></mrow><mrow><mi>n</mi><mo>+</mo><mn>1</mn></mrow></mfrac></math> = x + 1<br><math display=\"inline\"><mi>n</mi><mi>x</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>30</mn></math> = nx + x + n+1<br><math display=\"inline\"><mi>x</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>n</mi></math> = 29<br><math display=\"inline\"><mi>n</mi></math> = 29 - x -----(i)<br>and, If one more student weighing 30 kg is added, then the average weight of the class increases by 1.5 kg over the original average<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>n</mi><mi>x</mi><mo>+</mo><mn>60</mn></mrow><mrow><mi>n</mi><mo>+</mo><mn>2</mn></mrow></mfrac></math> = x + 1.5<br><math display=\"inline\"><mi>n</mi><mi>x</mi></math> + 60 = nx + 2x + 1.5n + 3<br>1.5<math display=\"inline\"><mi>n</mi></math> + 2x = 57------(ii)<br>Putting the value of n from equation (i) to (ii) we get;<br>1.5(29 - <math display=\"inline\"><mi>x</mi></math>) + 2x = 57<br>43.5 + 0.5<math display=\"inline\"><mi>x</mi></math> = 57<br><math display=\"inline\"><mi>x</mi></math> = 27 kg<br>Hence, original average weight of class = 27 kg</p>",
                    solution_hi: "<p>65.(c)<br>माना छात्रों की संख्या <math display=\"inline\"><mi>n</mi></math> है और उनका औसत वजन x किलोग्राम है<br>प्रश्न के अनुसार,<br>30 किलोग्राम वजन वाला एक छात्र जोड़ा जाता है, तो कक्षा का औसत वजन 1 किलोग्राम बढ़ जाता है<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>n</mi><mi>x</mi><mo>+</mo><mn>30</mn></mrow><mrow><mi>n</mi><mo>+</mo><mn>1</mn></mrow></mfrac></math> = x + 1<br><math display=\"inline\"><mi>n</mi><mi>x</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>30</mn></math> = nx + x + n+1<br><math display=\"inline\"><mi>x</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>n</mi></math> = 29<br><math display=\"inline\"><mi>n</mi></math> = 29 - x -----(i)<br>और, यदि 30 किलोग्राम वजन वाला एक और छात्र जोड़ा जाता है, तो कक्षा का औसत वजन मूल औसत से 1.5 किलोग्राम बढ़ जाता है।<br><math display=\"inline\"><mfrac><mrow><mi>n</mi><mi>x</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>60</mn></mrow><mrow><mi>n</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>2</mn></mrow></mfrac></math> = x + 1.5<br><math display=\"inline\"><mi>n</mi><mi>x</mi></math> + 60 = nx + 2x + 1.5n + 3<br>1.5<math display=\"inline\"><mi>n</mi></math> + 2x = 57------(ii)<br>समीकरण (i) से (ii) में n का मान रखने पर हमें प्राप्त होता है;<br>1.5(29 - <math display=\"inline\"><mi>x</mi></math>) + 2x = 57<br>43.5 + 0.5<math display=\"inline\"><mi>x</mi></math> = 57<br><math display=\"inline\"><mi>x</mi></math> = 27 kg<br>अतः, कक्षा का मूल औसत वजन = 27 kg</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "66",
                    section: "17",
                    question_en: "<p>66. In an election, the winner was supported by 46% of all the voters in the list, and he got 410 votes more than his only rival.10% of the voters on the voters&rsquo; list did not cast their votes, and 60 voters cast their ballot papers blank. How many voters were on the list?</p>",
                    question_hi: "<p>66. एक चुनाव सूची में सभी मतदाताओं के 46% द्वारा विजेता का समर्थन किया गया था और उसे अपने एकमात्र प्रतिद्वंद्वी से 410 वोट अधिक मिले। मतदाता सूची के 10% मतदाताओं ने वोट नहीं डाले और 60 मतदाताओं ने अपने मतपत्र खाली डाल दिए। सूची में कितने मतदाता थे?</p>",
                    options_en: [
                        "<p>17445</p>",
                        "<p>16550</p>",
                        "<p>17500</p>",
                        "<p>15750</p>"
                    ],
                    options_hi: [
                        "<p>17445</p>",
                        "<p>16550</p>",
                        "<p>17500</p>",
                        "<p>15750</p>"
                    ],
                    solution_en: "<p>66.(c)<br>Let the no of voters on the list be 100<math display=\"inline\"><mi>x</mi></math><br>No of valid votes = 100<math display=\"inline\"><mi>x</mi></math> &times; 90% - 60 = (90x - 60)<br>Votes received by winner = 100<math display=\"inline\"><mi>x</mi></math> &times; 46% = 46x<br>Votes received by loser = (90<math display=\"inline\"><mi>x</mi></math> - 60) - 46x = (44x - 60)<br>According to question,<br>46<math display=\"inline\"><mi>x</mi></math> - (44x - 60) = 410<br>2<math display=\"inline\"><mi>x</mi></math> + 60 = 410<br>2<math display=\"inline\"><mi>x</mi></math> = 350<br><math display=\"inline\"><mi>x</mi></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>350</mn><mn>2</mn></mfrac></math>= 175<br>So, the no of voters on the list = 100<math display=\"inline\"><mi>x</mi></math> = 100 &times; 175 = 17500</p>",
                    solution_hi: "<p>66.(c)<br>माना मतदाताओं की संख्या 100<math display=\"inline\"><mi>x</mi></math> है<br>वैध मतों की संख्या = 100<math display=\"inline\"><mi>x</mi></math> &times; 90% - 60 = (90x - 60)<br>विजेता को प्राप्त वोट = 100<math display=\"inline\"><mi>x</mi></math> &times; 46% = 46x<br>हारने वाले को प्राप्त वोट = (90<math display=\"inline\"><mi>x</mi></math> - 60) - 46x = (44x - 60)<br>प्रश्न के अनुसार,<br>46<math display=\"inline\"><mi>x</mi></math> - (44x - 60) = 410<br>2<math display=\"inline\"><mi>x</mi></math> + 60 = 410<br>2<math display=\"inline\"><mi>x</mi></math> = 350<br><math display=\"inline\"><mi>x</mi></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>350</mn><mn>2</mn></mfrac></math>= 175<br>अतः, सूची में मतदाताओं की संख्या = 100<math display=\"inline\"><mi>x</mi></math> = 100 &times; 175 = 17500</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "67",
                    section: "17",
                    question_en: "<p>67. The given pie-chart shows the percentage distribution of the expenditure incurred in publishing a book.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742361739950.png\" alt=\"rId57\" width=\"220\" height=\"219\"> <br>What is the central angle of the sector corresponding to the expenditure incurred on binding?</p>",
                    question_hi: "<p>67. दिया गया पाई-चार्ट किसी पुस्तक के प्रकाशन पर हुए व्यय का प्रतिशत वितरण दर्शाता है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742361740086.png\" alt=\"rId58\" width=\"233\" height=\"232\"> <br>percentage distribution of expenditure- व्यय का प्रतिशत वितरण<br>Binding 23% - बाइंडिंग 23%<br>Printing cost 20% - मुद्रण लागत 20%<br>Paper cost 25% - कागज लागत 25%<br>Royalty 15% - रॉयल्टी 15%<br>Transport cost 17% - परिवहन लागत 17%<br>बाइंडिंग पर किए गए व्यय के संगत वृतखंड का केंद्रीय कोण क्या है?</p>",
                    options_en: [
                        "<p>78.8&deg;</p>",
                        "<p>61.8&deg;</p>",
                        "<p>82.8&deg;</p>",
                        "<p>48.8&deg;</p>"
                    ],
                    options_hi: [
                        "<p>78.8&deg;</p>",
                        "<p>61.8&deg;</p>",
                        "<p>82.8&deg;</p>",
                        "<p>48.8&deg;</p>"
                    ],
                    solution_en: "<p>67.(c) According to the question,<br><math display=\"inline\"><mo>&#8658;</mo></math> 100 % = 360&deg; <br><math display=\"inline\"><mo>&#8658;</mo></math> 23% =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>360</mn><mo>&#176;</mo></mrow><mn>100</mn></mfrac></math> &times; 23 = 82.8&deg;</p>",
                    solution_hi: "<p>67.(c) प्रश्न के अनुसार,<br><math display=\"inline\"><mo>&#8658;</mo></math> 100 % = 360&deg; <br><math display=\"inline\"><mo>&#8658;</mo></math> 23% = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>360</mn><mo>&#176;</mo></mrow><mn>100</mn></mfrac></math> &times; 23 = 82.8&deg;</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "68",
                    section: "17",
                    question_en: "<p>68. Two pipes can fill a tank in 12 hours and 18 hours, respectively, while a third pipe can empty it in 8 hours. How long (in hours) will it take to fill the empty tank if all three pipes are opened simultaneously?</p>",
                    question_hi: "<p>68. दो पाइप एक टैंक को क्रमशः 12 घंटे और 18 घंटे में भर सकते हैं, जबकि तीसरा पाइप इसे 8 घंटे में खाली कर सकता है। यदि तीनों पाइप एक साथ खोल दिए जाते हैं, तो खाली टैंक को भरने में कितना समय (घंटों में) लगेगा?</p>",
                    options_en: [
                        "<p>48</p>",
                        "<p>24</p>",
                        "<p>36</p>",
                        "<p>72</p>"
                    ],
                    options_hi: [
                        "<p>48</p>",
                        "<p>24</p>",
                        "<p>36</p>",
                        "<p>72</p>"
                    ],
                    solution_en: "<p>68.(d) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742361740435.png\" alt=\"rId59\" width=\"264\" height=\"150\"><br>Efficiency of A, B and C = 6 + 4 - 9 = 1 unit<br>Time taken to fill the empty tank = <math display=\"inline\"><mfrac><mrow><mn>72</mn></mrow><mrow><mn>1</mn></mrow></mfrac></math> = 72 hours</p>",
                    solution_hi: "<p>68.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742361740874.png\" alt=\"rId60\" width=\"200\" height=\"142\"><br>A, B और C की दक्षता = 6 + 4 - 9 = 1 इकाई<br>खाली टंकी को भरने में लगा समय = <math display=\"inline\"><mfrac><mrow><mn>72</mn></mrow><mrow><mn>1</mn></mrow></mfrac></math> = 72 घंटे</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "69",
                    section: "17",
                    question_en: "<p>69. The nominal marks obtained by students who appeared in a test are given below. Find the median marks of the students.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742361740975.png\" alt=\"rId61\"></p>",
                    question_hi: "<p>69. एक परीक्षा में भाग लेने वाले छात्रों के वास्तविक प्राप्तांक (nominal marks) नीचे दिए गए हैं। छात्रों के अंकों की माध्यिका ज्ञात कीजिए।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742361741082.png\" alt=\"rId62\"></p>",
                    options_en: [
                        "<p>13</p>",
                        "<p>8</p>",
                        "<p>13.5</p>",
                        "<p>10.5</p>"
                    ],
                    options_hi: [
                        "<p>13</p>",
                        "<p>8</p>",
                        "<p>13.5</p>",
                        "<p>10.5</p>"
                    ],
                    solution_en: "<p>69.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742361741182.png\" alt=\"rId63\" width=\"237\" height=\"234\"><br>Here , n = 50 Then <br>Median will be the average of <math display=\"inline\"><mfenced separators=\"|\"><mrow><mfrac><mrow><mi>n</mi></mrow><mrow><mn>2</mn></mrow></mfrac></mrow></mfenced><mi>t</mi><mi>h</mi></math> value and (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>n</mi><mn>2</mn></mfrac></math>+1)th value = 25th and 26th<br>25th student got 11 mark and 26th student got 16 mark<br>So , median = <math display=\"inline\"><mfrac><mrow><mn>11</mn><mo>+</mo><mn>16</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = 13.5 marks.</p>",
                    solution_hi: "<p>69.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742361741312.png\" alt=\"rId64\" width=\"178\" height=\"201\"><br>यहाँ, n = 50 <br>तब माध्यिका <math display=\"inline\"><mfenced separators=\"|\"><mrow><mfrac><mrow><mi>n</mi></mrow><mrow><mn>2</mn></mrow></mfrac></mrow></mfenced></math> वें और (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>n</mi><mn>2</mn></mfrac></math> + 1) वें पद के मान का औसत होगा = 25वां और 26वां<br>25 वें छात्र को 11 और 26 वें के छात्र को 16 अंक मिले हैं<br>तो, माध्यिका = <math display=\"inline\"><mfrac><mrow><mn>11</mn><mo>+</mo><mn>16</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = 13.5 अंक .</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "70",
                    section: "17",
                    question_en: "<p>70. For what value of k, the system of equations k<math display=\"inline\"><mi>x</mi></math> - 15y + 7 = 0 and 7x - 21y - 19 = 0 has NO solution?</p>",
                    question_hi: "<p>70. k, के किस मान के लिए, समीकरण निकाय k<math display=\"inline\"><mi>x</mi></math> - 15y + 7 = 0 और 7x - 21y - 19 = 0 का कोई हल नहीं है?</p>",
                    options_en: [
                        "<p>5</p>",
                        "<p>12</p>",
                        "<p>25</p>",
                        "<p>6</p>"
                    ],
                    options_hi: [
                        "<p>5</p>",
                        "<p>12</p>",
                        "<p>25</p>",
                        "<p>6</p>"
                    ],
                    solution_en: "<p>70.(a) Given equation, kx - 15y + 7 = 0 and 7x - 21y - 19 = 0<br>There is no solution = <math display=\"inline\"><mfrac><mrow><msub><mrow><mi>a</mi></mrow><mrow><mn>1</mn></mrow></msub></mrow><mrow><msub><mrow><mi>a</mi></mrow><mrow><mn>2</mn></mrow></msub></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msub><mi>b</mi><mn>1</mn></msub><mrow><msub><mi>b</mi><mn>2</mn></msub><mo>&#160;</mo></mrow></mfrac></math>&ne;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msub><mi>c</mi><mn>1</mn></msub><msub><mi>c</mi><mn>2</mn></msub></mfrac></math><br>Then,<br><math display=\"inline\"><mo>&#8658;</mo></math>&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>k</mi><mn>7</mn></mfrac></math>=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>-</mo><mn>15</mn></mrow><mrow><mo>-</mo><mn>21</mn></mrow></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math> k = 5</p>",
                    solution_hi: "<p>70.(a) दिया गया समीकरण , kx - 15y + 7 = 0 और 7x - 21y - 19 = 0<br>कोई हल नहीं = <math display=\"inline\"><mfrac><mrow><msub><mrow><mi>a</mi></mrow><mrow><mn>1</mn></mrow></msub></mrow><mrow><msub><mrow><mi>a</mi></mrow><mrow><mn>2</mn></mrow></msub></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msub><mi>b</mi><mn>1</mn></msub><mrow><msub><mi>b</mi><mn>2</mn></msub><mo>&#160;</mo></mrow></mfrac></math>&ne;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msub><mi>c</mi><mn>1</mn></msub><msub><mi>c</mi><mn>2</mn></msub></mfrac></math><br>इसलिए ,<br><math display=\"inline\"><mo>&#8658;</mo></math>&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>k</mi><mn>7</mn></mfrac></math>=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>-</mo><mn>15</mn></mrow><mrow><mo>-</mo><mn>21</mn></mrow></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math> k = 5</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "71",
                    section: "17",
                    question_en: "<p>71. A boat can travel 78 km upstream and back in a total of 32 hours. It can travel 15 km upstream and 52 km downstream in a total of 9 hours. How much distance will the boat cover in 12 hours in still water?</p>",
                    question_hi: "<p>71. एक नाव कुल 32 घंटों में धारा की विपरीत दिशा में 78 km की दूरी तय कर सकती है और वापस आ सकती है। यह कुल 9 घंटे में 15 km की दूरी धारा की विपरीत दिशा में और 52 km की दूरी धारा की दिशा में तय कर सकती है। यह नाव स्थिर जल में 12 घंटे में कितनी दूरी तय करेगी?</p>",
                    options_en: [
                        "<p>92 km</p>",
                        "<p>96 km</p>",
                        "<p>104 km</p>",
                        "<p>100 km</p>"
                    ],
                    options_hi: [
                        "<p>92 km</p>",
                        "<p>96 km</p>",
                        "<p>104 km</p>",
                        "<p>100 km</p>"
                    ],
                    solution_en: "<p>71.(b) Let the speed of a boat be <math display=\"inline\"><mi>x</mi></math> km/h and the speed of a stream be y km/h<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>78</mn><mrow><mi>x</mi><mo>+</mo><mi>y</mi></mrow></mfrac></math> +&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>78</mn><mrow><mi>x</mi><mo>-</mo><mi>y</mi></mrow></mfrac></math> = 32 &hellip;&hellip;.(i)<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>52</mn><mrow><mi>x</mi><mo>+</mo><mi>y</mi></mrow></mfrac></math> +&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>15</mn><mrow><mi>x</mi><mo>-</mo><mi>y</mi></mrow></mfrac></math> = 9 &hellip;&hellip;.(ii)<br>We will assume that (<math display=\"inline\"><mi>x</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>y</mi></math> = 13) and (x - y = 3) satisfies the equation (i) and (ii)<br>Then,<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>x</mi><mo>+</mo><mi>y</mi></math> = 13 &hellip;&hellip;.(iii)<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>x</mi><mo>-</mo><mi>y</mi></math> = 3 &hellip;&hellip;..(iv)<br>Add equation (iii) and (iv), we get<br>2<math display=\"inline\"><mi>x</mi></math> = 16<br><math display=\"inline\"><mi>x</mi></math> = 8 km/h<br>Now,<br>Distance covered by boat in still water = 12 &times; 8 = 96 km</p>",
                    solution_hi: "<p>71.(b) माना नाव की गति <math display=\"inline\"><mi>x</mi></math> km/h है और धारा की गति y km/h है,<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>78</mn><mrow><mi>x</mi><mo>+</mo><mi>y</mi></mrow></mfrac></math> +&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>78</mn><mrow><mi>x</mi><mo>-</mo><mi>y</mi></mrow></mfrac></math> = 32 &hellip;&hellip;.(i)<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>52</mn><mrow><mi>x</mi><mo>+</mo><mi>y</mi></mrow></mfrac></math> +&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>15</mn><mrow><mi>x</mi><mo>-</mo><mi>y</mi></mrow></mfrac></math> = 9 &hellip;&hellip;.(ii)<br>माना (<math display=\"inline\"><mi>x</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>y</mi></math> = 13) और (x - y = 3) समीकरण (i) और (ii) को संतुष्ट करते हैं,<br>तब,<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>x</mi><mo>+</mo><mi>y</mi></math> = 13 &hellip;&hellip;.(iii)<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>x</mi><mo>-</mo><mi>y</mi></math> = 3 &hellip;&hellip;..(iv)<br>समीकरण (iii) और (iv) जोड़ने पर <br>2<math display=\"inline\"><mi>x</mi></math> = 16<br><math display=\"inline\"><mi>x</mi></math> = 8 km/h<br>शांत पानी में नाव द्वारा तय की गई दूरी = 12 &times; 8 = 96 km</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "72",
                    section: "17",
                    question_en: "<p>72. What is the mean proportional between (6 - 2<math display=\"inline\"><msqrt><mn>2</mn></msqrt></math>) and (9 + 3<math display=\"inline\"><msqrt><mn>2</mn></msqrt></math>)?</p>",
                    question_hi: "<p>72. (6 - 2<math display=\"inline\"><msqrt><mn>2</mn></msqrt></math>) और (9 + 3<math display=\"inline\"><msqrt><mn>2</mn></msqrt></math>) के बीच माध्यानुपाती क्या है?</p>",
                    options_en: [
                        "<p><math display=\"inline\"><msqrt><mn>54</mn></msqrt></math></p>",
                        "<p><math display=\"inline\"><msqrt><mn>52</mn></msqrt></math></p>",
                        "<p><math display=\"inline\"><msqrt><mn>66</mn></msqrt></math></p>",
                        "<p><math display=\"inline\"><msqrt><mn>42</mn></msqrt></math></p>"
                    ],
                    options_hi: [
                        "<p><math display=\"inline\"><msqrt><mn>54</mn></msqrt></math></p>",
                        "<p><math display=\"inline\"><msqrt><mn>52</mn></msqrt></math></p>",
                        "<p><math display=\"inline\"><msqrt><mn>66</mn></msqrt></math></p>",
                        "<p><math display=\"inline\"><msqrt><mn>42</mn></msqrt></math></p>"
                    ],
                    solution_en: "<p>72.(d)<br>Mean proportion = <math display=\"inline\"><msqrt><mo>(</mo><mn>6</mn><mo>-</mo><mn>2</mn><msqrt><mn>2</mn></msqrt><mo>)</mo><mi>&#160;</mi><mo>(</mo><mn>9</mn><mo>+</mo><mn>3</mn><msqrt><mn>2</mn></msqrt><mo>)</mo></msqrt></math><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>54</mn><mo>+</mo><mn>18</mn><msqrt><mn>2</mn></msqrt><mo>-</mo><mn>18</mn><msqrt><mn>2</mn></msqrt><mo>-</mo><mn>12</mn></msqrt></math> <br>= <math display=\"inline\"><msqrt><mn>42</mn></msqrt></math></p>",
                    solution_hi: "<p>72.(d)<br>माध्य अनुपात = <math display=\"inline\"><msqrt><mo>(</mo><mn>6</mn><mo>-</mo><mn>2</mn><msqrt><mn>2</mn></msqrt><mo>)</mo><mi>&#160;</mi><mo>(</mo><mn>9</mn><mo>+</mo><mn>3</mn><msqrt><mn>2</mn></msqrt><mo>)</mo></msqrt></math><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>54</mn><mo>+</mo><mn>18</mn><msqrt><mn>2</mn></msqrt><mo>-</mo><mn>18</mn><msqrt><mn>2</mn></msqrt><mo>-</mo><mn>12</mn></msqrt></math><br>= <math display=\"inline\"><msqrt><mn>42</mn></msqrt></math></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "73",
                    section: "17",
                    question_en: "<p>73. The quantity of milk in a mixture of milk and water is <math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> of the volume of the mixture. After adding 1 liter of milk to 5 liters of the mixture, the percentage of water in the mixture is:</p>",
                    question_hi: "<p>73. दूध और जल के मिश्रण में दूध की मात्रा मिश्रण के आयतन का <math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> है। 5 लीटर मिश्रण में 1 लीटर दूध मिलाने के बाद, मिश्रण में जल का प्रतिशत कितना होगा?</p>",
                    options_en: [
                        "<p>17.5%</p>",
                        "<p>15%</p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>200</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math>%</p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>100</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math>%</p>"
                    ],
                    options_hi: [
                        "<p>17.5%</p>",
                        "<p>15%</p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>200</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math>%</p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>100</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math>%</p>"
                    ],
                    solution_en: "<p>73.(d)<br>Mixture <math display=\"inline\"><mo>&#8594;</mo></math>&nbsp; &nbsp;Milk&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; Water<br>5 liter&nbsp; &nbsp; <math display=\"inline\"><mo>&#8594;</mo></math>&nbsp; &nbsp;4 liter&nbsp; &nbsp;:&nbsp; &nbsp;1 liter<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; <math display=\"inline\"><mo>&#8595;</mo></math>+1 lit. :&nbsp; &darr;+0 lit.<br>Final <math display=\"inline\"><mo>&#8594;</mo></math>&nbsp; &nbsp; &nbsp; &nbsp;5 liter&nbsp; &nbsp; :&nbsp; 1 liter<br>Percentage of water in final mixture = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>6</mn></mrow></mfrac><mo>&#215;</mo><mn>100</mn></math>% = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>100</mn><mn>6</mn></mfrac></math>%</p>",
                    solution_hi: "<p>73.(d)<br>मिश्रण&nbsp; &nbsp; &nbsp;<math display=\"inline\"><mo>&#8594;</mo></math>&nbsp; दूध&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp; पानी<br>5 लीटर&nbsp; &nbsp;<math display=\"inline\"><mo>&#8594;</mo></math>&nbsp; 4 लीटर&nbsp; &nbsp;:&nbsp; &nbsp;1 लीटर <br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; <math display=\"inline\"><mo>&#8595;</mo></math>+1 ली. :&nbsp; &nbsp;&darr;+0 ली.<br>अंतिम&nbsp; &nbsp; <math display=\"inline\"><mo>&#8594;</mo></math>&nbsp; &nbsp;5 लीटर&nbsp; &nbsp;:&nbsp; &nbsp;1 लीटर <br>अंतिम मिश्रण में पानी का प्रतिशत = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>6</mn></mrow></mfrac><mo>&#215;</mo><mn>100</mn></math>% = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>100</mn><mn>6</mn></mfrac></math>%</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "74",
                    section: "17",
                    question_en: "<p>74. Sapna finishes a job in 12 hours working alone. If Sapna and Riya finish the same job in 8 hours working together at their respective constant rates, then, in how many hours can Riya do the job working alone?</p>",
                    question_hi: "<p>74. सपना अकेले काम करते हुए एक काम को 12 घंटे में पूरा करती है। यदि सपना और रिया अपनी-अपनी नियत चाल से साथ मिलकर काम करते हुए उसी काम को 8 घंटे में पूरा करती हैं. तो रिया अकेले काम करते हुए इसे कितने घंटों में पूरा कर सकती है?</p>",
                    options_en: [
                        "<p>18</p>",
                        "<p>20</p>",
                        "<p>16</p>",
                        "<p>24</p>"
                    ],
                    options_hi: [
                        "<p>18</p>",
                        "<p>20</p>",
                        "<p>16</p>",
                        "<p>24</p>"
                    ],
                    solution_en: "<p>74.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742361741419.png\" alt=\"rId65\" width=\"272\" height=\"202\"><br>Efficiency of Riya = 3 - 2 = 1 unit<br>Time taken by Riya = <math display=\"inline\"><mfrac><mrow><mn>24</mn></mrow><mrow><mn>1</mn></mrow></mfrac></math> = 24 hours</p>",
                    solution_hi: "<p>74.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742361741571.png\" alt=\"rId66\" width=\"245\" height=\"216\"><br>रिया की क्षमता = 3 - 2 = 1 इकाई<br>रिया द्वारा लिया गया समय = <math display=\"inline\"><mfrac><mrow><mn>24</mn></mrow><mrow><mn>1</mn></mrow></mfrac></math> = 24 घंटे</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "75",
                    section: "7",
                    question_en: "<p>75. A part of the journey is covered in 22.5 minutes at a speed of 68 km/h and the remaining part in 12 minutes at a speed of 72 km/h. The total distance of the journey is (to the nearest integer):</p>",
                    question_hi: "<p>75. यात्रा के एक हिस्से को 68 km/h की चाल से 22.5 मिनट में और शेष हिस्से को 72 km/h की चाल से 12 मिनट में तय किया जाता है। यात्रा की कुल दूरी कितनी है (निकटतम पूर्णांक तक)?</p>",
                    options_en: [
                        "<p>35 km</p>",
                        "<p>42 km</p>",
                        "<p>38 km</p>",
                        "<p>40 km</p>"
                    ],
                    options_hi: [
                        "<p>35 km</p>",
                        "<p>42 km</p>",
                        "<p>38 km</p>",
                        "<p>40 km</p>"
                    ],
                    solution_en: "<p>75.(d) <br>Total distance = 68 &times; <math display=\"inline\"><mfrac><mrow><mn>22</mn><mo>.</mo><mn>5</mn></mrow><mrow><mn>60</mn></mrow></mfrac></math> + 72 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>12</mn><mn>60</mn></mfrac></math>= 25.5 + 14.4 = 40 km (approx)</p>",
                    solution_hi: "<p>75.(d) <br>कुल दूरी = 68 &times; <math display=\"inline\"><mfrac><mrow><mn>22</mn><mo>.</mo><mn>5</mn></mrow><mrow><mn>60</mn></mrow></mfrac></math> + 72 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>12</mn><mn>60</mn></mfrac></math>= 25.5 + 14.4 = 40 किमी (लगभग)</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "76",
                    section: "7",
                    question_en: "<p>76. Parts of the following sentence have been given as options. Select the option that&nbsp;contains an error.<br>Ramayana is an ancient Sanskrit epic; it is believed to be written around 500 BC to 100&nbsp;BC.</p>",
                    question_hi: "<p>76. Parts of the following sentence have been given as options. Select the option that&nbsp;contains an error.<br>Ramayana is an ancient Sanskrit epic; it is believed to be written around 500 BC to 100 BC.</p>",
                    options_en: [
                        "<p>Ramayana is an ancient</p>",
                        "<p>around 500 BC to 100 BC.</p>",
                        "<p>believed to be written</p>",
                        "<p>Sanskrit epic; it is</p>"
                    ],
                    options_hi: [
                        "<p>Ramayana is an ancient</p>",
                        "<p>around 500 BC to 100 BC.</p>",
                        "<p>believed to be written</p>",
                        "<p>Sanskrit epic; it is</p>"
                    ],
                    solution_en: "<p>76.(a) Ramayana is an ancient<br>The definite article \'the\' is used before holy books. For example:- The Ramayana, The Mahabharata, The Quran, The Bible, etc. Hence, \'The Ramayana is an ancient\' is the most appropriate answer.</p>",
                    solution_hi: "<p>76.(a) Ramayana is an ancient<br>पवित्र ग्रंथों (holy books) से पहले Definite article \'the\' का प्रयोग किया जाता है। For example :- The Ramayana, The Mahabharata, The Quran, The Bible, आदि। अतः, \'The Ramayana is an ancient\' सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "77",
                    section: "7",
                    question_en: "<p>77. Choose the best alternative which best expresses the meaning of the idiom/ phrase given below as underlined in the sentence.<br>I <span style=\"text-decoration: underline;\"><strong>stirred the hornet&rsquo;s nest</strong></span> by my honest statement.</p>",
                    question_hi: "<p>77. Choose the best alternative which best expresses the meaning of the idiom/ phrase given below as underlined in the sentence.<br>I <span style=\"text-decoration: underline;\"><strong>stirred the hornet&rsquo;s nest</strong></span> by my honest statement.</p>",
                    options_en: [
                        "<p>responded to criticism angrily</p>",
                        "<p>caused anger in many people</p>",
                        "<p>stopped people criticizing someone</p>",
                        "<p>acted with energy and interest</p>"
                    ],
                    options_hi: [
                        "<p>responded to criticism angrily</p>",
                        "<p>caused anger in many people</p>",
                        "<p>stopped people criticizing someone</p>",
                        "<p>acted with energy and interest</p>"
                    ],
                    solution_en: "<p>77.(b) caused anger in many people.<br>Example- His remarks about lack of good women officers in the Army stirred up a hornet\'s nest.</p>",
                    solution_hi: "<p>77.(b) caused anger in many people./कई लोगों में गुस्सा पैदा करना।<br>Example- His remarks about lack of good women officers in the Army stirred up a hornet\'s nest.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "78",
                    section: "7",
                    question_en: "<p>78. Identify the best way to improve the underlined part of the given sentence. If there is no improvement required, select &lsquo;no improvement&rsquo;.<br>They <span style=\"text-decoration: underline;\"><strong>cooked a conspiracy</strong></span> to overthrow the monarch.</p>",
                    question_hi: "<p>78. Identify the best way to improve the underlined part of the given sentence. If there is no improvement required, select &lsquo;no improvement&rsquo;.<br>They <span style=\"text-decoration: underline;\"><strong>cooked a conspiracy</strong></span> to overthrow the monarch.</p>",
                    options_en: [
                        "<p>created conspiracy</p>",
                        "<p>fabricated conspiracy</p>",
                        "<p>hatched a conspiracy</p>",
                        "<p>No improvement</p>"
                    ],
                    options_hi: [
                        "<p>created conspiracy</p>",
                        "<p>fabricated conspiracy</p>",
                        "<p>hatched a conspiracy</p>",
                        "<p>No improvement</p>"
                    ],
                    solution_en: "<p>78.(c) hatched a conspiracy. <br>&lsquo;Hatched a conspiracy&rsquo; is an idiom that means to plan something secretly.</p>",
                    solution_hi: "<p>78.(c) hatched a conspiracy.<br>&lsquo;Hatched a conspiracy&rsquo; एक मुहावरा है जिसका अर्थ है गुप्त रूप से कोई योजना बनाना।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "79",
                    section: "7",
                    question_en: "<p>79. Choose the most appropriate option to change the narration (direct/indirect) of the given sentence:<br>He said, &ldquo;Get your coat, Mohan&rdquo;.</p>",
                    question_hi: "<p>79. Choose the most appropriate option to change the narration (direct/indirect) of the given sentence:<br>He said, &ldquo;Get your coat, Mohan&rdquo;.</p>",
                    options_en: [
                        "<p>He said to get his coat.</p>",
                        "<p>He asked Mohan for his coat.</p>",
                        "<p>He told Mohan get his coat.</p>",
                        "<p>He told Mohan to get his coat</p>"
                    ],
                    options_hi: [
                        "<p>He said to get his coat.</p>",
                        "<p>He asked Mohan for his coat.</p>",
                        "<p>He told Mohan get his coat.</p>",
                        "<p>He told Mohan to get his coat</p>"
                    ],
                    solution_en: "<p>79.(d) He told Mohan to get his coat. <br>(a) He said to get his coat. (Listener is missing)<br>(b) He <strong>asked</strong> Mohan for his coat. (Incorrect Reporting Verb)<br>(c) He told <strong>Mohan get</strong> his coat. (Incorrect joining word)</p>",
                    solution_hi: "<p>79.(d) He told Mohan to get his coat. <br>(a) He said to get his coat. (Listener गायब है )<br>(b) He <strong>asked</strong> Mohan for his coat. (गलत Reporting Verb)<br>(c) He told <strong>Mohan get</strong> his coat. (गलत joining word)</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "80",
                    section: "7",
                    question_en: "<p>80.Select the word which means the same as the group of words given<br>Cultivation of trees and vegetables</p>",
                    question_hi: "<p>80.Select the word which means the same as the group of words given<br>Cultivation of trees and vegetables</p>",
                    options_en: [
                        "<p>Pisciculture</p>",
                        "<p>Sericulture</p>",
                        "<p>Horticulture</p>",
                        "<p>Multiculture</p>"
                    ],
                    options_hi: [
                        "<p>Pisciculture</p>",
                        "<p>Sericulture</p>",
                        "<p>Horticulture</p>",
                        "<p>Multiculture</p>"
                    ],
                    solution_en: "<p>80.(c) <strong>Horticulture</strong><br>(a) <strong>Pisciculture</strong> - the controlled breeding and rearing of fish.<br>(b) <strong>Sericulture</strong> - the production of silk and the rearing of silkworms for this purpose.<br>(d) <strong>Multiculture</strong> - multiculture is something that incorporates ideas, beliefs or people from many different countries and cultural backgrounds.</p>",
                    solution_hi: "<p>80.(c) <strong>Horticulture</strong><br>(a) <strong>Pisciculture</strong> - मछलियों का नियंत्रित प्रजनन और पालन।<br>(b) <strong>Sericulture</strong> -रेशम का उत्पादन और रेशम के कीड़ों का पालन।<br>(d) <strong>Multiculture</strong> - बहुसंस्कृति एक ऐसी चीज है जो कई अलग-अलग देशों और सांस्कृतिक पृष्ठभूमि के विचारों, विश्वासों या लोगों को शामिल करती है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "81",
                    section: "7",
                    question_en: "<p>81. Select the most appropriate option that can substitute the underlined word in the given sentence. <br>The culprit had been shot <span style=\"text-decoration: underline;\">from</span> the back by the police inspector.</p>",
                    question_hi: "<p>81. Select the most appropriate option that can substitute the underlined word in the given sentence. <br>The culprit had been shot <span style=\"text-decoration: underline;\">from</span> the back by the police inspector.</p>",
                    options_en: [
                        "<p>by</p>",
                        "<p>off</p>",
                        "<p>in</p>",
                        "<p>to</p>"
                    ],
                    options_hi: [
                        "<p>by</p>",
                        "<p>off</p>",
                        "<p>in</p>",
                        "<p>to</p>"
                    ],
                    solution_en: "<p>81.(c) in<br>Here, &lsquo;in&rsquo; will be used to indicate that the bullet that was shot by the police inspector hit the back of the culprit. Hence, &lsquo;in&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>81.(c) in<br>यहाँ, &lsquo;in&rsquo; का प्रयोग यह दर्शाने के लिए किया जाएगा कि पुलिस इंस्पेक्टर (police inspector) द्वारा चलाई गई गोली अपराधी (culprit) की पीठ पर लगी थी। इसलिए, &lsquo;in&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "82",
                    section: "7",
                    question_en: "<p>82. May you be blessed with good health!</p>",
                    question_hi: "<p>82. May you be blessed with good health!</p>",
                    options_en: [
                        "<p>May God be blessing you with good health!</p>",
                        "<p>May God bless you with good health!</p>",
                        "<p>May God blessed you with good health!&nbsp;</p>",
                        "<p>May you bless God with good health!</p>"
                    ],
                    options_hi: [
                        "<p>May God be blessing you with good health!</p>",
                        "<p>May God bless you with good health!</p>",
                        "<p>May God blessed you with good health!&nbsp;</p>",
                        "<p>May you bless God with good health!</p>"
                    ],
                    solution_en: "<p>82.(b) May God bless you with good health! (Correct)<br>(a) May God <span style=\"text-decoration: underline;\">be blessing</span> you with good health! (Incorrect Tense)<br>(c) May God <span style=\"text-decoration: underline;\">blessed</span> you with good health!&nbsp;(Incorrect Verb) <br>(d) May <span style=\"text-decoration: underline;\">you bless God</span> with good health! (Incorrect Sentence <br>Structure)</p>",
                    solution_hi: "<p>82.(b) May God bless you with good health! (Correct)<br>(a) May God <span style=\"text-decoration: underline;\">be blessing</span> you with good health! (गलत verb (be blessing) का प्रयोग किया गया है। (blessed) का प्रयोग होगा। ) <br>(c) May God <span style=\"text-decoration: underline;\">blessed</span> you with good health!&nbsp;(गलत verb (blessed) का प्रयोग किया गया है। (bless) का प्रयोग होगा। <br>(d) May <span style=\"text-decoration: underline;\">you bless God</span> with good health! (Sentence Structure सही नहीं है)</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "83",
                    section: "7",
                    question_en: "<p>83. This unfair proposal is objected to by us.</p>",
                    question_hi: "<p>83. This unfair proposal is objected to by us.</p>",
                    options_en: [
                        "<p>We are objecting this unfair proposal.</p>",
                        "<p>We object to this unfair proposal.</p>",
                        "<p>We object this unfair proposal.</p>",
                        "<p>We have objected to this unfair proposal.</p>"
                    ],
                    options_hi: [
                        "<p>We are objecting this unfair proposal.</p>",
                        "<p>We object to this unfair proposal.</p>",
                        "<p>We object this unfair proposal.</p>",
                        "<p>We have objected to this unfair proposal.</p>"
                    ],
                    solution_en: "<p>83.(b) We object to this unfair proposal. (Correct)<br>(a) We <span style=\"text-decoration: underline;\">are objecting</span> this unfair proposal. (Incorrect Tense)<br>(c) We object this unfair proposal. (Preposition &lsquo;to&rsquo; is missing)<br>(d) We <span style=\"text-decoration: underline;\">have objected</span> to this unfair proposal. (Incorrect Tense)</p>",
                    solution_hi: "<p>83.(b) We object to this unfair proposal. (Correct)<br>(a) We <span style=\"text-decoration: underline;\">are objecting</span> this unfair proposal. (गलत verb (are objecting) का प्रयोग किया गया है। (object) का प्रयोग होगा । )<br>(c) We object this unfair proposal. (object के स्थान पर object to का प्रयोग होगा I )<br>(d) We <span style=\"text-decoration: underline;\">have objected</span> to this unfair proposal. (गलत tense (present perfect) का प्रयोग किया गया है । (object (present simple) का प्रयोग होगा )</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "84",
                    section: "7",
                    question_en: "<p>84. Select the most appropriate synonym of the given word.<br>Indigent</p>",
                    question_hi: "<p>84. Select the most appropriate synonym of the given word.<br>Indigent</p>",
                    options_en: [
                        "<p>Bust</p>",
                        "<p>Impoverished</p>",
                        "<p>Affluent</p>",
                        "<p>Low</p>"
                    ],
                    options_hi: [
                        "<p>Bust</p>",
                        "<p>Impoverished</p>",
                        "<p>Affluent</p>",
                        "<p>Low</p>"
                    ],
                    solution_en: "<p>84.(b) <strong>Impoverished-</strong> lacking financial resources.<br><strong>Indigent-</strong> extremely poor or needy.<br><strong>Bust-</strong> a failure or collapse, often financial.<br><strong>Affluent-</strong> wealthy or having an abundance of resources.<br><strong>Low-</strong> less than average in amount, extent, or quality.</p>",
                    solution_hi: "<p>84.(b) <strong>Impoverished</strong> (निर्धन) - lacking financial resources.<br><strong>Indigent</strong> (दरिद्र) - extremely poor or needy.<br><strong>Bust</strong> (वक्ष) - a failure or collapse, often financial.<br><strong>Affluent</strong> (संपन्न) - wealthy or having an abundance of resources.<br><strong>Low</strong> (निम्न) - less than average in amount, extent, or quality.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "85",
                    section: "7",
                    question_en: "<p>85. Given below are four jumbled sentences. Pick the option that gives their correct order.<br>(A) The term ecology denotes the web of physical and biological systems and processes of which humans are one element.<br>(B)Mountains and rivers, plains and oceans, and the flora and fauna that they support, are a part of ecology.<br>(C )The ecology of a place is also affected by the interaction between its geography and hydrology.<br>(D)All societies have an ecological basis.</p>",
                    question_hi: "<p>85. Given below are four jumbled sentences. Pick the option that gives their correct order.<br>(A) The term ecology denotes the web of physical and biological systems and processes of which humans are one element.<br>(B)Mountains and rivers, plains and oceans, and the flora and fauna that they support, are a part of ecology.<br>(C )The ecology of a place is also affected by the interaction between its geography and hydrology.<br>(D)All societies have an ecological basis.</p>",
                    options_en: [
                        "<p>DABC</p>",
                        "<p>DCBA</p>",
                        "<p>DABC</p>",
                        "<p>DACB</p>"
                    ],
                    options_hi: [
                        "<p>DABC</p>",
                        "<p>DCBA</p>",
                        "<p>DABC</p>",
                        "<p>DACB</p>"
                    ],
                    solution_en: "<p>85 (c) DABC<br>Sentence D will be the starting line as it contains the main idea of the parajumble i.e.All societies have an ecological basis. And, Sentence A states what the term ecology denotes. So, A will follow D. Further, Sentence B states the parts of ecology &amp; Sentence C states how the ecology of a place is affected. So, C will follow B. Going through the options, option (c) has the correct sequence.</p>",
                    solution_hi: "<p>85.(c) DABC<br>Sentence D starting line होगी क्योंकि इसमें parajumble का मुख्य विचार &lsquo;All societies have an ecological basis&rsquo; शामिल है। Sentence A बताता है कि पारिस्थितिकी शब्द क्या दर्शाता है। तो,D के बाद A आएगा । आगे, Sentence B पारिस्थितिकी के भागों को बताता है और Sentence C बताता है कि किसी स्थान की पारिस्थितिकी कैसे प्रभावित होती है। इसलिए, B के बाद C आएगा। options के माध्यम से जाने पर , option (c) में सही sequence है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "86",
                    section: "7",
                    question_en: "<p>86. Select the INCORRECTLY spelt word.</p>",
                    question_hi: "<p>86. Select the INCORRECTLY spelt word.</p>",
                    options_en: [
                        "<p>Amendment</p>",
                        "<p>Adulterous</p>",
                        "<p>Advertisment</p>",
                        "<p>Astounding</p>"
                    ],
                    options_hi: [
                        "<p>Amendment</p>",
                        "<p>Adulterous</p>",
                        "<p>Advertisment</p>",
                        "<p>Astounding</p>"
                    ],
                    solution_en: "<p>86.(c) Advertisment<br>&lsquo;Advertisement&rsquo; is the correct spelling.</p>",
                    solution_hi: "<p>86.(c) Advertisment<br>&lsquo;Advertisement&rsquo; सही spelling है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "87",
                    section: "7",
                    question_en: "<p>87. Select the most appropriate meaning of the given idiom. <br>A vicious cycle</p>",
                    question_hi: "<p>87. Select the most appropriate meaning of the given idiom. <br>A vicious cycle</p>",
                    options_en: [
                        "<p>a situation in which one problem creates another one, making the original problem easy to forget.</p>",
                        "<p>a situation in which several problems merge and become one extremely complex problem.</p>",
                        "<p>a situation in which one problem causes another one, making the first one easy to solve.</p>",
                        "<p>a situation in which one problem causes another one, making the original problem impossible to solve</p>"
                    ],
                    options_hi: [
                        "<p>a situation in which one problem creates another one, making the original problem easy to <br>forget.</p>",
                        "<p>a situation in which several problems merge and become one extremely complex problem.</p>",
                        "<p>a situation in which one problem causes another one, making the first one easy to solve.</p>",
                        "<p>a situation in which one problem causes another one, making the original problem impossible to solve</p>"
                    ],
                    solution_en: "<p>87.(d) <strong>A vicious cycle</strong> - a situation in which one problem causes another one, making the original problem impossible to solve.<br>E.g.- Stress can lead to lack of sleep, and lack of sleep causes more stress&mdash;a vicious cycle that\'s hard to break.</p>",
                    solution_hi: "<p>87.(d) <strong>A vicious cycle</strong> - a situation in which one problem causes another one, making the original problem impossible to solve./ऐसी स्थिति जिसमें एक समस्या दूसरी समस्या का कारण बनती है, जिससे मूल समस्या का समाधान असंभव हो जाता है।<br>E.g.- Stress can lead to lack of sleep, and lack of sleep causes more stress&mdash;a vicious cycle that\'s hard to break.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "88",
                    section: "7",
                    question_en: "<p>88. Given below are four jumbled sentences. Pick the option that gives their correct order.<br>(A) These were Indian girls, in stiff shoes and closely clinging dresses. <br>(B) A paleface woman, with white hair, came up after us.<br>(C ) We were placed in a line of girls who were marching into the dining room.<br>(D) The small girls wore sleeved aprons and shingled hair.</p>",
                    question_hi: "<p>88. Given below are four jumbled sentences. Pick the option that gives their correct order.<br>(A) These were Indian girls, in stiff shoes and closely clinging dresses. <br>(B) A paleface woman, with white hair, came up after us.<br>(C ) We were placed in a line of girls who were marching into the dining room.<br>(D) The small girls wore sleeved aprons and shingled hair.</p>",
                    options_en: [
                        "<p>DCBA</p>",
                        "<p>ABCD</p>",
                        "<p>BADC</p>",
                        "<p>BCAD</p>"
                    ],
                    options_hi: [
                        "<p>DCBA</p>",
                        "<p>ABCD</p>",
                        "<p>BADC</p>",
                        "<p>BCAD</p>"
                    ],
                    solution_en: "<p>88.(d) BCAD<br>Sentence B will be the starting line as it contains the main idea of the parajumble i.e. A paleface woman coming upto them. Sentence C states that they were placed in a line of girls who were marching into the dining room. So, C will follow B. Further, Sentence A states that these were Indian girls, in stiff shoes and closely clinging dresses and Sentence D states what the small girls wore .<br>So, D<br>will follow A. Going through the options, option (d) BCAD has the correct sequence.</p>",
                    solution_hi: "<p>88.(d) BCAD<br>Sentence B starting line होगी क्योंकि इसमें parajumble का मुख्य विचार &lsquo; A paleface woman coming up to them&rsquo; शामिल है। Sentence C में कहा गया है कि उन्हें उन लड़कियों की एक line में रखा गया था जो भोजन कक्ष में march कर रही थीं। तो, B के बाद C आएगा । आगे, Sentence A में कहा गया है कि ये भारतीय लड़कियां थीं, कड़े जूते और चिपकी हुई पोशाक में और Sentence D बताता है कि छोटी लड़कियों ने क्या पहना था। तो, A के बाद D<br>आएगा । options के माध्यम से जाने पर, option (d) BCAD में सही sequence है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "89",
                    section: "7",
                    question_en: "<p>89. Select the option that can be used as a one-word substitute for the given group of words.<br>Vivacious and enthusiastic</p>",
                    question_hi: "<p>89. Select the option that can be used as a one-word substitute for the given group of words.<br>Vivacious and enthusiastic</p>",
                    options_en: [
                        "<p>Introverted</p>",
                        "<p>Brazen</p>",
                        "<p>Rowdy</p>",
                        "<p>Effervescent</p>"
                    ],
                    options_hi: [
                        "<p>Introverted</p>",
                        "<p>Brazen</p>",
                        "<p>Rowdy</p>",
                        "<p>Effervescent</p>"
                    ],
                    solution_en: "<p>89.(d) <strong>Effervescent-</strong> vivacious and enthusiastic.<br><strong>Introverted-</strong> shy, quiet, and preferring to spend time alone.<br><strong>Brazen-</strong> obvious, without any attempt to be hidden<br><strong>Rowdy-</strong> noisy and likely to cause trouble</p>",
                    solution_hi: "<p>89.(d) <strong>Effervescent</strong> (चमकता हुआ) - vivacious and enthusiastic.<br><strong>Introverted</strong> (अंतर्मुखी) - shy, quiet, and preferring to spend time alone.<br><strong>Brazen</strong> (बेशर्म) - obvious, without any attempt to be hidden<br><strong>Rowdy</strong> (उपद्रवी) - noisy and likely to cause trouble</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "90",
                    section: "7",
                    question_en: "<p>90. Identify the segment in the sentence which contains grammatical error. Mark &lsquo;no error&rsquo; in case the sentence given is correct. <br>Reading is no longer popular among the youthful of today. As the influence of the internet has taken over a very important and active hobby.</p>",
                    question_hi: "<p>90. Identify the segment in the sentence which contains grammatical error. Mark &lsquo;no error&rsquo; in case the sentence given is correct.</p>",
                    options_en: [
                        "<p>among the youthful of today</p>",
                        "<p>very important and active hobby</p>",
                        "<p>influence of the internet</p>",
                        "<p>No Error</p>"
                    ],
                    options_hi: [
                        "<p>Reading is no longer popular among the youthful of today. As the influence of the internet has taken over a very important and active hobby. among the youthful of today</p>",
                        "<p>very important and active hobby</p>",
                        "<p>influence of the internet</p>",
                        "<p>No Error</p>"
                    ],
                    solution_en: "<p>90.(a) among the youthful of today<br>Here, among the youths (Noun) of today..........should be used here. Youthful {Adjective} - typical of young people.</p>",
                    solution_hi: "<p>90.(a) among the youthful of today<br>यहाँ among the youths (Noun) of today.......... .should का प्रयोग होना चाहिए। युवा [Adjective] - युवा लोगों का विशिष्ट।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "91",
                    section: "7",
                    question_en: "<p>91. Select the most appropriate option to fill in the blank.<br>The artist received a ___________ from the audience.</p>",
                    question_hi: "<p>91. Select the most appropriate option to fill in the blank.<br>The artist received a ___________ from the audience.</p>",
                    options_en: [
                        "<p>bunch of applause</p>",
                        "<p>plenty of applause</p>",
                        "<p>handful of applause</p>",
                        "<p>round of applause</p>"
                    ],
                    options_hi: [
                        "<p>bunch of applause</p>",
                        "<p>plenty of applause</p>",
                        "<p>handful of applause</p>",
                        "<p>round of applause</p>"
                    ],
                    solution_en: "<p>91.(d) round of applause <br>&lsquo;Round of applause&rsquo; is a common phrase used to describe the clapping or cheering given to someone by an audience. The given sentence states that the artist received a round of applause from the audience. Hence, &lsquo;round of applause&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>91.(d) round of applause <br>&lsquo;Round of applause&rsquo; एक common phrase है जिसका प्रयोग ताली बजाने या किसी व्यक्ति को दर्शकों द्वारा दी गई जयकार को describe करने के लिए किया जाता है। दिए गए sentence में कहा गया है कि कलाकार को दर्शकों से खूब तालियां मिलीं। अतः, &lsquo;round of applause&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "92",
                    section: "7",
                    question_en: "<p>92 Find a word that is the synonym of <br>ONUS</p>",
                    question_hi: "<p>92. Find a word that is the synonym of <br>ONUS</p>",
                    options_en: [
                        "<p>Sadness</p>",
                        "<p>Happiness</p>",
                        "<p>Responsibility</p>",
                        "<p>Criticism</p>"
                    ],
                    options_hi: [
                        "<p>Sadness</p>",
                        "<p>Happiness</p>",
                        "<p>Responsibility</p>",
                        "<p>Criticism</p>"
                    ],
                    solution_en: "<p>92.(c) <strong>Responsibility-</strong> a duty to deal with something so that it is your fault if something goes wrong. <br><strong>Onus</strong> - something that is one\'s duty or responsibility.<br><strong>Sadness</strong> - the feeling of being unhappy Happiness- a state of well-being &amp; contentment <br><strong>Criticism-</strong> what you think is bad about somebody/something</p>",
                    solution_hi: "<p>92.(c) <strong>Responsibility</strong> - किसी काम के प्रति जवाबदेही या ज़िम्&zwj;मेदारी।<br><strong>Onus</strong> - ऐसा कुछ जो किसी का कर्तव्य या जिम्मेदारी हो।<br><strong>Sadness</strong> - दुखी होने का भाव।<br><strong>Happiness</strong> - कल्याण और संतोष की स्थित<br><strong>Criticism</strong> - निंदा</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "93",
                    section: "7",
                    question_en: "<p>93. Select the most appropriate ANTONYM of the given word. <br>Languid</p>",
                    question_hi: "<p>93. Select the most appropriate ANTONYM of the given word. <br>Languid</p>",
                    options_en: [
                        "<p>Exceptional</p>",
                        "<p>Lazy</p>",
                        "<p>Loyal</p>",
                        "<p>Energetic</p>"
                    ],
                    options_hi: [
                        "<p>Exceptional</p>",
                        "<p>Lazy</p>",
                        "<p>Loyal</p>",
                        "<p>Energetic</p>"
                    ],
                    solution_en: "<p>93.(d) <strong>Energetic-</strong> showing or involving great activity or vitality.<br><strong>Languid-</strong> lacking energy or enthusiasm.<br><strong>Exceptional-</strong> unusual or extraordinary.<br><strong>Lazy-</strong> unwilling to work or use effort.<br><strong>Loyal-</strong> showing firm and constant support or allegiance.</p>",
                    solution_hi: "<p>93.(d) <strong>Energetic</strong> (शक्तिशाली) - showing or involving great activity or vitality.<br><strong>Languid</strong> (निस्तेज) - lacking energy or enthusiasm.<br><strong>Exceptional</strong> (असाधारण) - unusual or extraordinary.<br><strong>Lazy</strong> (आलसी) - unwilling to work or use effort.<br><strong>Loyal</strong> (निष्ठावान) - showing firm and constant support or allegiance.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "94",
                    section: "7",
                    question_en: "<p>94. Pick a word opposite in meaning to <br>SPURIOUS</p>",
                    question_hi: "<p>94. Pick a word opposite in meaning to <br>SPURIOUS</p>",
                    options_en: [
                        "<p>Truthful</p>",
                        "<p>Authentic</p>",
                        "<p>Credible</p>",
                        "<p>Original</p>"
                    ],
                    options_hi: [
                        "<p>Truthful</p>",
                        "<p>Authentic</p>",
                        "<p>Credible</p>",
                        "<p>Original</p>"
                    ],
                    solution_en: "<p>94.(b) <strong>Authentic</strong><br><strong>Spurious</strong> - not being what it purports to be; false or fake.</p>",
                    solution_hi: "<p>94.(b) <strong>Authentic</strong><br><strong>Spurious</strong> - झूठा या नकली।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "95",
                    section: "7",
                    question_en: "<p>95. Select the most appropriate word to fill in the blank.<br>Our new office is equipped ______ all the latest technology.</p>",
                    question_hi: "<p>95. Select the most appropriate word to fill in the blank.<br>Our new office is equipped ______ all the latest technology.</p>",
                    options_en: [
                        "<p>from</p>",
                        "<p>about</p>",
                        "<p>with</p>",
                        "<p>by</p>"
                    ],
                    options_hi: [
                        "<p>from</p>",
                        "<p>about</p>",
                        "<p>with</p>",
                        "<p>by</p>"
                    ],
                    solution_en: "<p>95.(c) with.<br>&lsquo;Equipped with&rsquo; means &ldquo;having all the necessary items.&rdquo; So &ldquo;with\'\' will be used.<br>Example - All the bedrooms are equipped with a colour TV in the hotel.</p>",
                    solution_hi: "<p>95.(c) with.<br>Equipped with का अर्थ \"सभी आवश्यक वस्तुओं का होना।\" है। अतः \"with\'\' का प्रयोग किया जाएगा।<br>उदाहरण - All the bedrooms are equipped with a colour TV in the hotel. / होटल के सभी बेडरूम रंगीन टीवी से सुसज्जित हैं।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "96",
                    section: "7",
                    question_en: "<p>96. <strong>Cloze Test</strong> <br>A series of ______ (96) of shell or ivory or thin metal clothed her left upper arm all the way down to her fingers. A necklace __________ (97) three pendants bunched together and a few bangles above the elbow and ________ (98) on the right hands display an almost modern art. She speaks of the ______ (99), ever hopeful human spirit. She reminds us that it is important to visit museums in our country to experience the _____ (100) that a work of art leaves on our senses, to find among all the riches one particular vision of beauty that speaks to us alone. <br>Select the most appropriate option to fill in blank number 96.</p>",
                    question_hi: "<p>96. <strong>Cloze Test</strong> <br>A series of ______ (96) of shell or ivory or thin metal clothed her left upper arm all the way down to her fingers. A necklace __________ (97) three pendants bunched together and a few bangles above the elbow and ________ (98) on the right hands display an almost modern art. She speaks of the ______ (99), ever hopeful human spirit. She reminds us that it is important to visit museums in our country to experience the _____ (100) that a work of art leaves on our senses, to find among all the riches one particular vision of beauty that speaks to us alone.<br>Select the most appropriate option to fill in blank number 96.</p>",
                    options_en: [
                        "<p>bangles</p>",
                        "<p>toe-rings</p>",
                        "<p>ear-rings</p>",
                        "<p>anklets</p>"
                    ],
                    options_hi: [
                        "<p>bangles</p>",
                        "<p>toe-rings</p>",
                        "<p>ear-rings</p>",
                        "<p>anklets</p>"
                    ],
                    solution_en: "<p>96.(a) bangles <br>&lsquo;Bangles&rsquo; means a ring of glass or metal worn around the wrist as an ornament. The given passage states that a series of bangles of shell or ivory or thin metal clothed her left upper arm all the way down to her fingers. Hence, &lsquo;bangles&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>96.(a) bangles <br>&lsquo;Bangles&rsquo; का अर्थ है glass या metal की ring जिसे ornament के रूप में कलाई में पहना जाता है। दिए गए passage में बताया गया है कि shell या ivory या thin metal की bangles की एक series ने उसके left upper arm से लेकर fingers तक उसे पहना हुआ था। इसलिए, &lsquo;bangles&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "97",
                    section: "7",
                    question_en: "<p>97. <strong>Cloze Test</strong> <br>A series of ______ (96) of shell or ivory or thin metal clothed her left upper arm all the way down to her fingers. A necklace __________ (97) three pendants bunched together and a few bangles above the elbow and ________ (98) on the right hands display an almost modern art. She speaks of the ______ (99), ever hopeful human spirit. She reminds us that it is important to visit museums in our country to experience the _____ (100) that a work of art leaves on our senses, to find among all the riches one particular vision of beauty that speaks to us alone.<br>Select the most appropriate option to fill in blank number 97.</p>",
                    question_hi: "<p>97. <strong>Cloze Test</strong> <br>A series of ______ (96) of shell or ivory or thin metal clothed her left upper arm all the way down to her fingers. A necklace __________ (97) three pendants bunched together and a few bangles above the elbow and ________ (98) on the right hands display an almost modern art. She speaks of the ______ (99), ever hopeful human spirit. She reminds us that it is important to visit museums in our country to experience the _____ (100) that a work of art leaves on our senses, to find among all the riches one particular vision of beauty that speaks to us alone.<br>Select the most appropriate option to fill in blank number 97.</p>",
                    options_en: [
                        "<p>in</p>",
                        "<p>with</p>",
                        "<p>upon</p>",
                        "<p>by</p>"
                    ],
                    options_hi: [
                        "<p>in</p>",
                        "<p>with</p>",
                        "<p>upon</p>",
                        "<p>by</p>"
                    ],
                    solution_en: "<p>97.(b) with <br>&lsquo;With&rsquo; means having or including something as a part. The given passage states that a necklace with three pendants punched together and a few bangles above the elbow and wrist on the right hands display an almost modern art. Hence, &lsquo;with&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>97.(b) with <br>&lsquo;With&rsquo; का अर्थ है किसी चीज़ को एक part के रूप में रखना या शामिल करना। दिए गए passage में कहा गया है कि एक साथ necklace तीन पेंडेंट वाला हार और right hands की elbow और wrist के ऊपर कुछ bangles लगभग आधुनिक कला को प्रदर्शित करती हैं। इसलिए, &lsquo;with&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "98",
                    section: "7",
                    question_en: "<p>98. <strong>Cloze Test</strong> <br>A series of ______ (96) of shell or ivory or thin metal clothed her left upper arm all the way down to her fingers. A necklace __________ (97) three pendants bunched together and a few bangles above the elbow and ________ (98) on the right hands display an almost modern art. She speaks of the ______ (99), ever hopeful human spirit. She reminds us that it is important to visit museums in our country to experience the _____ (100) that a work of art leaves on our senses, to find among all the riches one particular vision of beauty that speaks to us alone.<br>Select the most appropriate option to fill in blank number 98.</p>",
                    question_hi: "<p>98.<strong> Cloze Test</strong> <br>A series of ______ (96) of shell or ivory or thin metal clothed her left upper arm all the way down to her fingers. A necklace __________ (97) three pendants bunched together and a few bangles above the elbow and ________ (98) on the right hands display an almost modern art. She speaks of the ______ (99), ever hopeful human spirit. She reminds us that it is important to visit museums in our country to experience the _____ (100) that a work of art leaves on our senses, to find among all the riches one particular vision of beauty that speaks to us alone.<br>Select the most appropriate option to fill in blank number 98.</p>",
                    options_en: [
                        "<p>ankle</p>",
                        "<p>toe</p>",
                        "<p>waist</p>",
                        "<p>wrist</p>"
                    ],
                    options_hi: [
                        "<p>ankle</p>",
                        "<p>toe</p>",
                        "<p>waist</p>",
                        "<p>wrist</p>"
                    ],
                    solution_en: "<p>98.(d) wrist<br>&lsquo;Wrist&rsquo; is the joint between the hand and the arm. The given passage states that a necklace with three pendants punched together and a few bangles above the elbow and wrist on the right hands display an almost modern art. Hence, &lsquo;wrist&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>98.(d) wrist<br>&lsquo;Wrist&rsquo; hand और arm के बीच का जोड़ है। दिए गए passage में कहा गया है कि three pendants के साथ एक necklace और right hands पर elbow और wrist के ऊपर few bangles लगभग modern art को प्रदर्शित करती हैं। इसलिए, &lsquo;wrist&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "99",
                    section: "7",
                    question_en: "<p>99. <strong>Cloze Test</strong> <br>A series of ______ (96) of shell or ivory or thin metal clothed her left upper arm all the way down to her fingers. A necklace __________ (97) three pendants bunched together and a few bangles above the elbow and ________ (98) on the right hands display an almost modern art. She speaks of the ______ (99), ever hopeful human spirit. She reminds us that it is important to visit museums in our country to experience the _____ (100) that a work of art leaves on our senses, to find among all the riches one particular vision of beauty that speaks to us alone.<br>Select the most appropriate option to fill in blank number 99.</p>",
                    question_hi: "<p>99. <strong>Cloze Test</strong> <br>A series of ______ (96) of shell or ivory or thin metal clothed her left upper arm all the way down to her fingers. A necklace __________ (97) three pendants bunched together and a few bangles above the elbow and ________ (98) on the right hands display an almost modern art. She speaks of the ______ (99), ever hopeful human spirit. She reminds us that it is important to visit museums in our country to experience the _____ (100) that a work of art leaves on our senses, to find among all the riches one particular vision of beauty that speaks to us alone.<br>Select the most appropriate option to fill in blank number 99.</p>",
                    options_en: [
                        "<p>undaunted</p>",
                        "<p>ungrateful</p>",
                        "<p>unsafe</p>",
                        "<p>unbelievable</p>"
                    ],
                    options_hi: [
                        "<p>undaunted</p>",
                        "<p>ungrateful</p>",
                        "<p>unsafe</p>",
                        "<p>unbelievable</p>"
                    ],
                    solution_en: "<p>99.(a) undaunted <br>&lsquo;Undaunted&rsquo; means not frightened or discouraged by difficulty, danger, or disappointment. The given passage states that she speaks of the undaunted, ever hopeful human spirit. Hence, &lsquo;undaunted&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>99.(a) undaunted <br>&lsquo;Undaunted&rsquo; का अर्थ है difficulty, danger या disappointment से frightened या discouraged न होना। दिए गए passage में कहा गया है कि वह &lsquo;undaunted&rsquo; , हमेशा आशावान (hopeful) मानवीय भावना (human spirit) की बात करती है। इसलिए, &lsquo;undaunted&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "100",
                    section: "misc",
                    question_en: "<p>100. <strong>Cloze Test</strong> <br>A series of ______ (96) of shell or ivory or thin metal clothed her left upper arm all the way down to her fingers. A necklace __________ (97) three pendants bunched together and a few bangles above the elbow and ________ (98) on the right hands display an almost modern art. She speaks of the ______ (99), ever hopeful human spirit. She reminds us that it is important to visit museums in our country to experience the _____ (100) that a work of art leaves on our senses, to find among all the riches one particular vision of beauty that speaks to us alone.<br>Select the most appropriate option to fill in blank number 100.</p>",
                    question_hi: "<p>100. <strong>Cloze Test</strong> <br>A series of ______ (96) of shell or ivory or thin metal clothed her left upper arm all the way down to her fingers. A necklace __________ (97) three pendants bunched together and a few bangles above the elbow and ________ (98) on the right hands display an almost modern art. She speaks of the ______ (99), ever hopeful human spirit. She reminds us that it is important to visit museums in our country to experience the _____ (100) that a work of art leaves on our senses, to find among all the riches one particular vision of beauty that speaks to us alone.<br>Select the most appropriate option to fill in blank number 100.</p>",
                    options_en: [
                        "<p>impact</p>",
                        "<p>blow</p>",
                        "<p>affect</p>",
                        "<p>result</p>"
                    ],
                    options_hi: [
                        "<p>impact</p>",
                        "<p>blow</p>",
                        "<p>affect</p>",
                        "<p>result</p>"
                    ],
                    solution_en: "<p>100.(a) impact<br>&lsquo;Impact&rsquo; means a strong impression or effect that something has on a person. The given passage states that it is important to visit museums in our country to experience the impact that a work of art leaves on our senses. Hence, &lsquo;effect&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>100.(a) impact<br>&lsquo;Impact&rsquo; का अर्थ है किसी चीज़ (something) का किसी person पर गहरा impression या effect। दिए गए passage में कहा गया है कि कला के किसी काम का हमारी इंद्रियों (senses) पर क्या प्रभाव पड़ता है, इसका अनुभव करने के लिए हमारे देश (country) के संग्रहालयों (museums ) में जाना महत्वपूर्ण है। इसलिए, &lsquo;effect&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'question-box d-flex align-items-center justify-content-center border rounded';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.question-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            const html = `
                <div class="card shadow-sm mb-4">
                    <div class="card-body">
                        <div class="question-text mb-4">
                            <div class="en">${question.question_en}</div>
                            <div class="hi" style="display:none">${question.question_hi}</div>
                        </div>
                        <div class="options">
                            ${['a', 'b', 'c', 'd'].map((opt, i) => `
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="radio" name="q${index}" 
                                        id="opt-${index}-${opt}" value="${opt}" 
                                        ${answers[question.id] === opt ? 'checked' : ''}
                                        onclick="selectAnswer('${question.id}', '${opt}')">
                                    <label class="form-check-label w-100 py-2 px-3 rounded hover-bg-light" for="opt-${index}-${opt}">
                                        <div class="en">${question.options_en[i]}</div>
                                        <div class="hi" style="display:none">${question.options_hi[i]}</div>
                                    </label>
                                </div>
                            `).join('')}
                        </div>
                        ${submitted ? `
                            <div class="alert ${answers[question.id] === question.correct ? 'alert-success' : 'alert-danger'} mt-3">
                                ${answers[question.id] ? (answers[question.id] === question.correct ? 'Correct!' : 'Incorrect!') : 'Not attempted'}
                                <div class="mt-2">
                                    <strong>Solution:</strong>
                                    <div class="en">${question.solution_en}</div>
                                    <div class="hi" style="display:none">${question.solution_hi}</div>
                                </div>
                            </div>
                        ` : ''}
                    </div>
                </div>
            `;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // If in mobile view, close the menu
            if (window.innerWidth < 768) {
                toggleMenu(false);
            }
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                document.getElementById(`box-${index}`).classList.add('attempted');
            }
        }
        // Navigate to previous question
        function prevQuestion() {
            if (currentQuestion > 0) {
                showQuestion(currentQuestion - 1);
            }
        }
        // Navigate to next question
        function nextQuestion() {
            if (currentQuestion < questions.length - 1) {
                showQuestion(currentQuestion + 1);
            }
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) {
                showQuestion(section.start);
            }
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update button text
            document.getElementById('lang-btn').innerHTML = currentLang === 'en' ? 
                '<i class="fas fa-language"></i> हिंदी' : 
                '<i class="fas fa-language"></i> English';
        }
        // Toggle menu for mobile
        function toggleMenu(show) {
            const nav = document.getElementById('question-nav');
            const content = document.getElementById('question-content');
            if (show === undefined) {
                show = nav.style.right === '-100%' || nav.style.right === '';
            }
            if (show) {
                nav.style.right = '0';
                if (window.innerWidth >= 768) {
                    content.style.marginRight = '320px';
                }
            } else {
                nav.style.right = window.innerWidth < 768 ? '-100%' : '0';
                content.style.marginRight = window.innerWidth < 768 ? '0' : '320px';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
            document.getElementById('total-count').textContent = questions.length;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.remove('attempted');
                    box.classList.add('bg-secondary', 'text-white');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.remove('attempted');
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.remove('attempted');
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            resultsModal.show();
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check-circle me-1"></i> Submitted';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            resultsModal.hide();
            showQuestion(0);
        }
    </script>
</body>
</html>