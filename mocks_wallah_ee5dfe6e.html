<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #7209b7;
            --secondary: #4361ee;
            --accent: #f72585;
            --success: #06d6a0;
            --warning: #ffd166;
            --danger: #ef476f;
            --light: #f8f9fa;
            --dark: #212529;
        }
        body {
            background-color: #f0f2f5;
            font-size: 16px;
            padding-bottom: 60px; /* Space for fixed navigation */
        }
        .navbar {
            background: linear-gradient(135deg, var(--primary), var(--secondary)) !important;
            box-shadow: 0 2px 10px rgba(0,0,0,.1);
            padding: 0.5rem 1rem;
        }
        .navbar-brand {
            font-size: 1.1rem;
            margin-right: 0;
        }
        .navbar .container-fluid {
            justify-content: space-between;
        }
        .controls-group {
            display: flex;
            align-items: center;
            flex-wrap: nowrap;
        }
        .timer-display {
            background: linear-gradient(135deg, #4361ee, #3a0ca3);
            color: white;
            border-radius: 50px;
            box-shadow: 0 3px 8px rgba(0,0,0,.1);
            padding: 0.25rem 0.75rem;
            font-size: 0.875rem;
            white-space: nowrap;
        }
        .nav-btn {
            padding: 0.25rem 0.75rem;
            font-size: 0.875rem;
            margin-left: 0.25rem;
        }
        .question-nav {
            position: fixed;
            top: 56px;
            right: 0;
            height: calc(100vh - 56px);
            width: 280px;
            transform: translateX(280px);
            transition: transform .3s;
            z-index: 100;
            background: #fff;
            box-shadow: -5px 0 15px rgba(0,0,0,.1);
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .main-content {
            transition: margin .3s;
            min-height: calc(100vh - 116px);
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.5;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(32px, 1fr));
            gap: 6px;
        }
        .q-box {
            width: 35px;
            height: 35px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 5px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid #dee2e6;
            background: #fff;
            transition: all .2s;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: 0 3px 5px rgba(0,0,0,.1);
        }
        .q-box.attempted {
            background-color: var(--secondary);
            color: white;
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,.15);
        }
        .card {
            border-radius: 12px;
            overflow: hidden;
            border: none;
            box-shadow: 0 4px 12px rgba(0,0,0,.08);
        }
        .option-label {
            border-radius: 8px;
            transition: all .2s;
            border: 1px solid #dee2e6;
            padding: 0.75rem 1rem;
            font-size: 1rem;
        }
        .option-label:hover {
            background-color: #f8f9fa;
            transform: translateY(-2px);
            box-shadow: 0 3px 8px rgba(0,0,0,.05);
        }
        .form-check-input:checked + .option-label {
            border-color: var(--primary);
            background-color: rgba(114,9,183,.05);
        }
        .btn-primary {
            background-color: var(--primary);
            border-color: var(--primary);
        }
        .btn-outline-primary {
            color: var(--primary);
            border-color: var(--primary);
        }
        .btn-outline-primary:hover {
            background-color: var(--primary);
            color: white;
        }
        /* Bottom fixed navigation for mobile */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: white;
            box-shadow: 0 -2px 10px rgba(0,0,0,.1);
            padding: 10px 15px;
            display: flex;
            justify-content: space-between;
            z-index: 90;
        }
        @media (min-width: 992px) {
            .main-content.with-nav {
                margin-right: 280px;
            }
            .question-nav {
                transform: translateX(0);
            }
            .nav-toggle-btn {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(30px, 1fr));
            }
            .option-label {
                padding: 0.625rem 0.875rem;
                font-size: 0.95rem;
            }
            .question-text {
                font-size: 1rem;
            }
            .navbar-brand {
                font-size: 1rem;
            }
            .timer-display {
                font-size: 0.8125rem;
                padding: 0.2rem 0.6rem;
            }
            .nav-btn {
                padding: 0.2rem 0.6rem;
                font-size: 0.8125rem;
            }
            .container {
                padding-left: 10px;
                padding-right: 10px;
            }
        }
    </style>
</head>
<body>
    <!-- Header navbar -->
    <nav class="navbar navbar-dark sticky-top">
        <div class="container-fluid px-2">
            <a class="navbar-brand fw-bold" href="#"><i class="fas fa-graduation-cap me-1"></i></a>
            <div class="controls-group">
                <!-- Timer -->
                <div class="timer-display me-1 fw-bold" id="timer">
                    <i class="far fa-clock me-1"></i><span id="timer-display">8:00</span>
                </div>
                <!-- Submit button -->
                <button class="btn btn-light btn-sm nav-btn me-1" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Language toggle -->
                <button class="btn btn-light btn-sm nav-btn me-1" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="btn btn-light btn-sm nav-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </nav>
    <!-- Main content -->
    <div class="main-content" id="main-content">
        <div class="container py-3">
            <!-- Question counter -->
            <div class="fw-bold text-center mb-3" id="question-counter">Question 1 of 25</div>
            <!-- Questions will be displayed here -->
            <div id="questions-container"></div>
        </div>
    </div>
    <!-- Bottom navigation for mobile -->
    <div class="bottom-nav d-lg-none">
        <button class="btn btn-outline-primary px-3" onclick="prevQuestion()">
            <i class="fas fa-chevron-left me-1"></i> Previous
        </button>
        <button class="btn btn-outline-primary px-3" onclick="nextQuestion()">
            Next <i class="fas fa-chevron-right ms-1"></i>
        </button>
    </div>
    <!-- Question navigation sidebar -->
    <div class="question-nav" id="question-nav">
        <div class="p-3">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h5 class="m-0">Questions</h5>
                <div>
                    <span class="badge bg-secondary me-1" id="attempted-count">0</span>
                    <span class="badge bg-secondary" id="total-count">25</span>
                    <button class="btn btn-sm btn-outline-secondary ms-2 d-lg-none" onclick="toggleNav()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
            <!-- Section selector moved here -->
            <div class="mb-3">
                <label for="section-selector" class="form-label small text-muted">Jump to Section</label>
                <select class="form-select form-select-sm" id="section-selector" onchange="switchSection(this.value)">
                    <option value="all">All Sections</option>
                </select>
            </div>
            <div class="question-grid" id="question-boxes"></div>
        </div>
    </div>
    <!-- Results modal -->
    <div class="modal fade" id="results-modal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content border-0">
                <div class="modal-header border-0" style="background:linear-gradient(135deg,var(--primary),var(--secondary));color:white">
                    <h5 class="modal-title">Test Results</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body p-4">
                    <div class="row text-center g-2">
                        <div class="col-6">
                            <div class="p-3 rounded shadow-sm" style="background:linear-gradient(135deg,#4361ee,#3a0ca3);color:white">
                                <h3 id="score-value" class="mb-0 fw-bold">0</h3>
                                <div class="small">Score</div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="p-3 rounded shadow-sm" style="background:linear-gradient(135deg,#06d6a0,#1b9aaa);color:white">
                                <h3 id="correct-value" class="mb-0 fw-bold">0</h3>
                                <div class="small">Correct</div>
                            </div>
                        </div>
                        <div class="col-6 mt-2">
                            <div class="p-3 rounded shadow-sm" style="background:linear-gradient(135deg,#ef476f,#f72585);color:white">
                                <h3 id="incorrect-value" class="mb-0 fw-bold">0</h3>
                                <div class="small">Incorrect</div>
                            </div>
                        </div>
                        <div class="col-6 mt-2">
                            <div class="p-3 rounded shadow-sm" style="background:linear-gradient(135deg,#6c757d,#495057);color:white">
                                <h3 id="unattempted-value" class="mb-0 fw-bold">0</h3>
                                <div class="small">Unattempted</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer border-0">
                    <button type="button" class="btn btn-primary" onclick="reviewTest()">Review Test</button>
                </div>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 8 * 60;
        let resultsModal;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            resultsModal = new bootstrap.Modal(document.getElementById('results-modal'));
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["18"] = {
                name: "General Awareness",
                start: 0,
                end: 23
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="18">General Awareness</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 24,
                end: 24
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "18",
                    question_en: " <p>1. Who was the founder of Theosophical society?",
                    question_hi: "<p><span style=\"font-family: Baloo;\">1.थियोसोफिकल सोसाइटी की स्थापना किसने की थी ? </span></p>",
                    options_en: [" <p> Helena Roerich</span></p>", " <p> William Juan</span></p>", 
                                " <p> Annie Besant</span></p>", " <p> Madame Blavatsky </span></p>"],
                    options_hi: ["<p>हेलेना रोएरीच</p>", "<p>विलियम जुआन</p>",
                                "<p>एनी बेसेंट</p>", "<p>मैडम ब्लावात्सकी</p>"],
                    solution_en: " <p><span style=\"font-family:Times New Roman\">(d) Theosophical society was founded by Madame Blavatsky and Col. H.S. Olcott in New York in 1875.</span></p>",
                    solution_hi: "<p><span style=\"font-family: Times New Roman;\">(d) थियोसोफिकल सोसायटी की स्थापना मैडम ब्लावात्स्की और कर्नल एच.एस. 1875 में न्यूयॉर्क में ओल्कोट।</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "18",
                    question_en: " <p>2. Mahabaleshwar in Maharashtra is the source of which river?",
                    question_hi: "<p><span style=\"font-family: Baloo;\">2.महाराष्ट्र में महाबलेश्वर किस नदी का स्रोत है ? </span></p>",
                    options_en: [" <p> Godavari</span></p>", " <p> Krishna</span></p>", 
                                " <p> Kaveri</span></p>", " <p> Narmada</span></p>"],
                    options_hi: ["<p>गोदावरी</p>", "<p>कृष्णा</p>",
                                "<p>कावेरी</p>", "<p>नर्मदा</p>"],
                    solution_en: " <p><span style=\"font-family:Times New Roman\">(b) Krishna river’s source is at Mahabaleshwar near Jor village in the extreme North of Wai Taluka, Satara district, Maharashtra.</span></p>",
                    solution_hi: "<p><span style=\"font-family: Times New Roman;\">(b) कृष्णा नदी का स्रोत महाराष्ट्र के सतारा जिले के वाई तालुका के चरम उत्तर में जोर गांव के पास महाबलेश्वर में है।</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "18",
                    question_en: " <p>3. Google Doodle celebrated the birth anniversary of “India’s Satellite Man” on March 10. Who was he?",
                    question_hi: "<p><span style=\"font-family: Baloo;\">3.Google ने 10 मार्च को \"भारत के सैटेलाइट मैन\" की जयंती पर डूडल समर्पित किया है| वे कौन है?</span></p>",
                    options_en: [" <p> Satish Dhawan</span></p>", " <p> U R Rao</span></p>", 
                                " <p> K Kasturirangan</span></p>", " <p> A. S. Kiran Kumar</span></p>"],
                    options_hi: ["<p>सतीश धवन</p>", "<p>यू आर राव</p>",
                                "<p>के कस्तूरीरंगन</p>", "<p>ए.एस. किरण कुमार</p>"],
                    solution_en: " <p><span style=\"font-family:Times New Roman\">(b) Google celebrated the 89th birthday of renowned Indian professor and scientist Udupi Ramachandra Rao, remembered by many as “India’s Satellite Man.” Rao, who was an Indian space scientist and chairman of the Indian Space Research Organisation (ISRO), supervised the 1975 launch of India’s first satellite — “Aryabhata.”</span></p>",
                    solution_hi: "<p><span style=\"font-family: Times New Roman;\">(b) Google ने प्रसिद्ध भारतीय प्रोफेसर और वैज्ञानिक उडुपी रामचंद्र राव का 89 वां जन्मदिन मनाया, जिन्हें कई लोग \"भारत के सैटेलाइट मैन\" के रूप में याद करते हैं। राव, जो एक भारतीय अंतरिक्ष वैज्ञानिक और भारतीय अंतरिक्ष अनुसंधान संगठन (इसरो) के अध्यक्ष थे, ने भारत के पहले उपग्रह - \"आर्यभट्ट\" के 1975 के प्रक्षेपण की निगरानी की।</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "18",
                    question_en: " <p>4. India changed over to the decimal system of coinage in  ",
                    question_hi: "<p><span style=\"font-family: Baloo;\">4.भारत ने टंकण की दशमलव प्रणाली में कब परिवर्तन किया था ? </span></p>",
                    options_en: [" <p> April 1958</span></p>", " <p> April 1957</span></p>", 
                                " <p> April 1988</span></p>", " <p> April 1978</span></p>"],
                    options_hi: ["<p>अप्रैल 1958</p>", "<p>अप्रैल 1957</p>",
                                "<p>अप्रैल 1988<span style=\"font-family: Baloo;\"> </span></p>", "<p>अप्रैल 1978</p>"],
                    solution_en: " <p><span style=\"font-family:Times New Roman\">(b) </span><span style=\"font-family:Times New Roman\">In 1957, India shifted to the decimal system, but for a short period both decimal and non-decimal coins were in circulation. To distinguish between the two paisa, the coins minted between 1957 and 1964 have the legend \"Naya Paisa\".</span></p>",
                    solution_hi: "<p><span style=\"font-family: Times New Roman;\">(b)1957 में, भारत दशमलव प्रणाली में स्थानांतरित हो गया, लेकिन अल्प अवधि के लिए दशमलव और गैर-दशमलव दोनों सिक्के प्रचलन में थे। दो पैसे के बीच अंतर करने के लिए, 1957 और 1964 के बीच ढाले गए सिक्कों में \"नया पैसा\" की किंवदंती है।</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "18",
                    question_en: " <p>5 Accession tax is levied on which one of the following?  ",
                    question_hi: "<p><span style=\"font-family: Baloo;\">5. परिग्रहण कर निम्न में से किस पर लगाया जाता है ? </span></p>",
                    options_en: [" <p> purchased goods</span></p>", " <p>donor  </span></p>", 
                                " <p> recipient  </span><span style=\"font-family:Times New Roman\">             </span></p>", " <p>all of above  </span></p>"],
                    options_hi: ["<p>खरीदी गयी वस्तुएँ</p>", "<p>दाता</p>",
                                "<p>प्राप्तकर्ता&nbsp;</p>", "<p>उपरोक्त सभी</p>"],
                    solution_en: "<p><span style=\"font-family: Times New Roman;\">(c) Accession tax is levied on gifts and inherited property. This tax is not a liability on the donor. This tax is levied on the recipient.</span></p>",
                    solution_hi: "<p><span style=\"font-family: Times New Roman;\">(c) उपहार और विरासत में मिली संपत्ति पर परिग्रहण कर लगाया जाता है। यह कर दाता पर देयता नहीं है। यह टैक्स प्राप्तकर्ता पर लगाया जाता है।</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "18",
                    question_en: " <p>6. The title of Maharajadhiraja was first assumed by which Gupta ruler?  ",
                    question_hi: "<p><span style=\"font-family: Baloo;\">6. महाराजाधिराज की उपाधि सबसे पहले किस गुप्त शासक ने धारण की थी ? </span></p>",
                    options_en: [" <p> Samudragupta       </span></p>", " <p> Kumaragupta</span></p>", 
                                " <p> Vikramaditya       </span></p>", " <p> Chandragupta I</span></p>"],
                    options_hi: ["<p>समुद्रगुप्त</p>", "<p>कुमारगुप्त</p>",
                                "<p>विक्रमादित्य</p>", "<p>चंद्रगुप्त I</p>"],
                    solution_en: " <p><span style=\"font-family:Times New Roman\">(d) Chandragupta I was the first Gupta ruler to assume the title of Maharajadhiraja.</span></p>",
                    solution_hi: "<p><span style=\"font-family: Times New Roman;\">(d)चंद्रगुप्त प्रथम महाराजाधिराज की उपाधि धारण करने वाला पहला गुप्त शासक था।</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "18",
                    question_en: " <p>7. ________ is known as the Veiled Planet.  ",
                    question_hi: "<p><span style=\"font-family: Baloo;\">7. ______ &nbsp;को&nbsp;गुप्त ग्रह के नाम से जाना जाता है| </span></p>",
                    options_en: [" <p> Jupiter </span></p>", " <p> Venus </span></p>", 
                                " <p> Neptune </span></p>", " <p> Saturn </span></p>"],
                    options_hi: ["<p>बृहस्पति</p>", "<p>शुक्र</p>",
                                "<p>वरुण</p>", "<p>शनि</p>"],
                    solution_en: "<p><span style=\"font-family: Times New Roman;\">(b) As Venus is surrounded by a thick cloud cover, hence known as the &lsquo;veiled planet&rsquo;.</span></p>",
                    solution_hi: "<p><span style=\"font-family: Times New Roman;\">(b) चूंकि शुक्र घने बादलों से घिरा हुआ है, इसलिए इसे \'छिपे हुए ग्रह\' के रूप में जाना जाता है।</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "18",
                    question_en: "<p>8. The new President of The Associated Chambers of Commerce and Industry of India is-</p>",
                    question_hi: "<p><span style=\"font-family: Baloo;\">8. भारतीय वाणिज्य एवं उद्योग मंडल के नए अध्यक्ष हैं - </span></p>",
                    options_en: ["<p>Taranjit Sandhu</p>", "<p>Michael Debabrata Patra</p>", 
                                "<p>Niranjan Hiranandani</p>", "<p>Vineet Agarwal</p>"],
                    options_hi: ["<p>तरनजीत संधू</p>", "<p>माइकल देवब्रत पात्रा</p>",
                                "<p>निरंजन हीरानंदानी</p>", "<p>विनीत अग्रवाल</p>"],
                    solution_en: "<p><span style=\"font-family: Times New Roman;\">(d) Vineet Agarwal has been serving as the new president of Associated Chambers of commerce and Industry of India since december 2020. He replaced Niranjan Hiranandani , Co-Founder and MD of Hiranandani Group of companies.</span></p>",
                    solution_hi: "<p><span style=\"font-family: Times New Roman;\">(d)विनीत अग्रवाल दिसंबर 2020 से एसोसिएटेड चैंबर्स ऑफ कॉमर्स एंड इंडस्ट्री ऑफ इंडिया के नए अध्यक्ष के रूप में कार्यरत हैं। उन्होंने हीरानंदानी ग्रुप ऑफ कंपनीज के सह-संस्थापक और एमडी निरंजन हीरानंदानी की जगह ली।</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "18",
                    question_en: " <p>9.Which Union Minister has launched NanoSniffer, the world’s first Microsensor-based Explosive Trace Detector (ETD)?  ",
                    question_hi: "<p><span style=\"font-family: Baloo;\">9. किस केंद्रीय मंत्री ने दुनिया के पहले माइक्रोसेन्सर-आधारित Explosive Trace Detector (ETD) NanoSniffer लॉन्च किया है?</span></p>",
                    options_en: [" <p> Prakash Javadekarm  </span></p>", " <p> Ramesh Pokhriyal Nishank </span></p>", 
                                " <p> Ravi Shankar Prasad </span></p>", " <p> Dr. Harshvardhan </span></p>"],
                    options_hi: ["<p>प्रकाश जावड़ेकर</p>", "<p>रमेश पोखरियाल निशंक</p>",
                                "<p>रविशंकर प्रसाद</p>", "<p>डॉ हर्षवर्धन</p>"],
                    solution_en: " <p><span style=\"font-family:Times New Roman\">(b) Union Education Minister Ramesh Pokhriyal ‘Nishank’ has launched NanoSniffer, the world’s first Microsensor-based Explosive Trace Detector (ETD).The Nanosniffer has been developed by NanoSniff Technologies which is an IIT Bombay incubated startup.</span></p>",
                    solution_hi: "<p><span style=\"font-family: Times New Roman;\">(b) केंद्रीय शिक्षा मंत्री रमेश पोखरियाल \'निशंक\' ने दुनिया का पहला माइक्रोसेंसर-आधारित विस्फोटक ट्रेस डिटेक्टर (ETD) नैनोस्निफ़र लॉन्च किया है। नैनोस्निफ़र को नैनोस्निफ़ टेक्नोलॉजीज द्वारा विकसित किया गया है जो एक आईआईटी बॉम्बे इनक्यूबेटेड स्टार्टअप है।</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "18",
                    question_en: "<p>10. For pasteurization of milk as per LTH method, milk is boiled at 62.8 degree for how much time?</p>",
                    question_hi: "<p><span style=\"font-family: Baloo;\">10. LTH पद्धति के अनुसार दूध के पास्तुरिकरण के लिए, दूध को कितने समय तक 62.8 डिग्री तापमान पर उबाला जाता है ? </span></p>",
                    options_en: ["<p>15 second</p>", "<p>30 second</p>", 
                                "<p>15 minute</p>", "<p>30 minute</p>"],
                    options_hi: ["<p>15 second</p>", "<p>30 second</p>",
                                "<p>15 minute</p>", "<p>30 minute</p>"],
                    solution_en: "<p><span style=\"font-family: Times New Roman;\">(d) During pasteurization, milk is preserved using two methods:</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">(i) LTH- low temperature holding method- milk is boiled at 62.8 degree celsius for 30 minutes.</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">(ii) HTSt- High Temperature short time method- milk is boiled at 71.7 degree celsius for 15 second.</span></p>",
                    solution_hi: "<p><span style=\"font-family: Times New Roman;\">(d)&nbsp;</span></p>\r\n<p>पाश्चराइजेशन के दौरान दूध को दो तरीकों से परिरक्षित किया जाता है:</p>\r\n<p>(i) LTH- कम तापमान धारण करने की विधि- दूध को 62.8 डिग्री सेल्सियस पर 30 मिनट तक उबाला जाता है।</p>\r\n<p>(ii) HTSt- उच्च तापमान कम समय विधि- दूध को 71.7 डिग्री सेल्सियस पर 15 सेकंड के लिए उबाला जाता है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "18",
                    question_en: " <p>11. Tshogdu was the Parliament of -  ",
                    question_hi: "<p><span style=\"font-family: Baloo;\">11. शोगडु कहाँ की संसद थी ? </span></p>",
                    options_en: [" <p>Denmark</span></p>", " <p>Bhutan </span></p>", 
                                " <p>Israel </span></p>", " <p>Myanmar  </span></p>"],
                    options_hi: ["<p>डेनमार्क</p>", "<p>भूटान</p>",
                                "<p>इजराइल</p>", "<p>म्यांमार</p>"],
                    solution_en: " <p><span style=\"font-family:Times New Roman\">(b) </span><span style=\"font-family:Times New Roman\">The Tshogdu was the unicameral legislature of Bhutan until 31 July 2007.</span></p>",
                    solution_hi: "<p><span style=\"font-family: Times New Roman;\">(b) त्शोग्दू 31 जुलाई 2007 तक भूटान की एक सदनीय विधायिका थी।</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "18",
                    question_en: " <p>12. How many fundamental duties are there in the constitution of India?",
                    question_hi: "<p><span style=\"font-family: Baloo;\">12. भारत के संविधान में कितने मौलिक कर्त्तव्य हैं ? </span></p>",
                    options_en: [" <p>Nine   </span></p>", " <p>Ten      </span></p>", 
                                " <p>Eleven      </span></p>", " <p>Twelve </span></p>"],
                    options_hi: ["<p>नौ</p>", "<p>दस</p>",
                                "<p>ग्यारह</p>", "<p>बारह</p>"],
                    solution_en: " <p><span style=\"font-family:Times New Roman\">(c)</span><span style=\"font-family:Times New Roman\"> There are eleven fundamental duties in the constitution of India.</span></p>",
                    solution_hi: "<p><span style=\"font-family: Times New Roman;\">(c)</span><span style=\"font-family: Times New Roman;\"> भारत के संविधान में ग्यारह मौलिक कर्तव्य हैं।</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "18",
                    question_en: " <p>13. Which Indian-origin woman has become the first Sikh woman to be elected to Scottish parliament?  ",
                    question_hi: "<p><span style=\"font-family: Baloo;\">13. स्कॉटिश संसद के लिए चुनी जाने वाली पहली भारतीय मूल की सिख महिला कौन बन गई हैं?</span></p>",
                    options_en: [" <p> Palbinder Kaur  </span></p>", " <p> Pam Gosal </span></p>", 
                                " <p> Anmol Narang </span></p>", " <p> Sweety Kaur </span></p>"],
                    options_hi: ["<p>पलबिंदर कौर</p>", "<p>पैम गोसाल</p>",
                                "<p>अनमोल नारंग</p>", "<p>स्वीटी कौर</p>"],
                    solution_en: " <p><span style=\"font-family:Times New Roman\">(b) Pam Gosal has created history by becoming the first Indian woman to be elected to Scottish parliament. She is the Deputy Chairperson of the Conservative Women’s Organisation of Scotland.</span></p>",
                    solution_hi: "<p><span style=\"font-family: Times New Roman;\">(b) पाम गोसल ने स्कॉटिश संसद के लिए चुनी जाने वाली पहली भारतीय महिला बनकर इतिहास रच दिया है। वह स्कॉटलैंड के कंजर्वेटिव महिला संगठन की उपाध्यक्ष हैं।</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "18",
                    question_en: " <p>14. Keoladeo Bird sanctuary is located in _____  ",
                    question_hi: "<p><span style=\"font-family: Baloo;\">14. केवलादेव पक्षी </span><span style=\"font-family: Baloo;\">अभ्यारण्य</span><span style=\"font-family: Baloo;\"> _____ में स्थित है | </span></p>",
                    options_en: [" <p> Rajasthan </span></p>", " <p> Madhya Pradesh  </span></p>", 
                                " <p> Meghalaya </span></p>", " <p> Jammu and Kashmir  </span></p>"],
                    options_hi: ["<p>राजस्थान</p>", "<p>मध्य प्रदेश</p>",
                                "<p>मेघालय</p>", "<p>जम्मू और कश्मीर</p>"],
                    solution_en: " <p><span style=\"font-family:Times New Roman\">(a) Keoladeo Bird Sanctuary also known as Ghana Bird sanctuary is located in Bharatpur, Rajasthan.</span></p>",
                    solution_hi: "<p><span style=\"font-family: Times New Roman;\">(a) केवलादेव पक्षी अभयारण्य जिसे घाना पक्षी अभयारण्य भी कहा जाता है, भरतपुर, राजस्थान में स्थित है।</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "18",
                    question_en: "<p>15. What was the original name of Swami Sahajanand?</p>",
                    question_hi: "<p><span style=\"font-family: Baloo;\">15. स्वामी सहजानंद का वास्तविक नाम क्या था ? </span></p>",
                    options_en: ["<p>Mulri Manohar</p>", "<p>Ghanashyama</p>", 
                                "<p>Govinda</p>", "<p>Mula Shankar</p>"],
                    options_hi: ["<p>मुरली मनोहर</p>", "<p>घनश्याम</p>",
                                "<p>गोविंदा</p>", "<p>मूला शंकर</p>"],
                    solution_en: "<p><span style=\"font-family: Times New Roman;\">(b) Swami Sahajanand was the founder of Swaminarayan sect in Gujarat and was originally named as Ghanashyama.</span></p>",
                    solution_hi: "<p><span style=\"font-family: Times New Roman;\">(b) स्वामी सहजानंद गुजरात में स्वामीनारायण संप्रदाय के संस्थापक थे और मूल रूप से उनका नाम घनश्याम था।</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "18",
                    question_en: " <p>16. _________ is the science dealing with the effects of seasonal changes upon animal and plant life.  ",
                    question_hi: "<p><span style=\"font-family: Baloo;\">16. ______ वह विज्ञान है जिसका संबंध पशुओं एवं पौधों के जीवन पर मौसमी परिवर्तनों के प्रभाव से है | </span></p>",
                    options_en: [" <p> Phytogeography       </span></p>", " <p> Petrology </span></p>", 
                                " <p> Phenology                          </span></p>", " <p> Mestizo </span></p>"],
                    options_hi: ["<p>फायटोजियोग्राफी</p>", "<p>पेट्रोलॉजी</p>",
                                "<p>फेनोलॉजी</p>", "<p>मेस्तिजो</p>"],
                    solution_en: " <p><span style=\"font-family:Times New Roman\">(c) Phenology is the study of periodic plants and animal life cycle events and how these are influenced by seasonal and interannual variations in climate as well as habitat factors.</span></p>",
                    solution_hi: "<p><span style=\"font-family: Times New Roman;\">(c) फेनोलॉजी समय-समय पर पौधों और पशु जीवन चक्र की घटनाओं का अध्ययन है और ये कैसे जलवायु के साथ-साथ आवास कारकों में मौसमी और अंतर-वार्षिक बदलावों से प्रभावित होते हैं।</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "18",
                    question_en: " <p>17. As per Pitt’s India Act, trade and commerce related issues were under the purview of  ",
                    question_hi: "<p><span style=\"font-family: Baloo;\">17. पिट्स इंडिया अधिनियम के अनुसार, व्यापार एवं वाणिज्य से संबंधित मामले _____ के कार्यक्षेत्र में आते थे | </span></p>",
                    options_en: [" <p> Board of controllers </span></p>", " <p> Court of Directors</span></p>", 
                                " <p> both</span></p>", " <p> none of above</span></p>"],
                    options_hi: ["<p>बोर्ड ऑफ़ कंट्रोलर्स</p>", "<p>कोर्ट ऑफ़ डायरेक्टर्स</p>",
                                "<p>दोनों</p>", "<p>उपरोक्त में से कोई नहीं</p>"],
                    solution_en: " <p><span style=\"font-family:Times New Roman\">(b) As per Pitt’s India Act 1784,the Court of Directors managed commercial affairs and the Board of Control was to manage political affairs.Hence, this act established dual government.</span></p>",
                    solution_hi: "<p><span style=\"font-family: Times New Roman;\">(b)पिट्स इंडिया एक्ट 1784 के अनुसार, निदेशक मंडल वाणिज्यिक मामलों का प्रबंधन करता था और नियंत्रण बोर्ड को राजनीतिक मामलों का प्रबंधन करना था। इसलिए, इस अधिनियम ने दोहरी सरकार की स्थापना की।</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "18",
                    question_en: " <p>18. The Cabinet has approved a total Outlay of Rs. __________ for the Atmanirbhar Bharat Rozgar Yojana?   ",
                    question_hi: "<p><span style=\"font-family: Baloo;\">18. आत्मनिर्भर भारत रोज़गार योजना के लिए कैबिनेट ने कुल कितने रूपए के परिव्यय को मंजूरी दी है?</span></p>",
                    options_en: [" <p> ₹22,810 crore</span></p>", " <p> ₹22,600 crore</span></p>", 
                                " <p> ₹12,810 crore</span></p>", " <p>₹20,000 crore</span></p>"],
                    options_hi: ["<p>₹ 22,810 करोड़</p>", "<p>₹ 22,600 करोड़</p>",
                                "<p>₹ 12,810 करोड़</p>", "<p>₹ 20,000 करोड़</p>"],
                    solution_en: " <p><span style=\"font-family:Times New Roman\">(a) The Union Cabinet has approved an expenditure of ₹22,810 crore for a wage subsidy via employee’s provident fund organization (EPFO) as part of the Atmanirbhar Bharat Rojgar Yojana.</span></p>",
                    solution_hi: "<p><span style=\"font-family: Times New Roman;\">(a) केंद्रीय मंत्रिमंडल ने आत्मानबीर भारत रोजगार योजना के हिस्से के रूप में कर्मचारी भविष्य निधि संगठन (ईपीएफओ) के माध्यम से मजदूरी सब्सिडी के लिए 22,810 करोड़ रुपये के खर्च को मंजूरी दी है।</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "18",
                    question_en: " <p>19. Calcium is required in plants in the formation of which of the following?  ",
                    question_hi: "<p><span style=\"font-family: Baloo;\">19. निम्न में से किसके निर्माण के लिए पौधों में कैल्शियम की आवश्यकता होती है ? </span></p>",
                    options_en: [" <p> Ribosomes             </span></p>", " <p> Cell wall</span></p>", 
                                " <p> Cell membrane</span></p>", " <p> Chlorophyll</span></p>"],
                    options_hi: ["<p>राइबोसोम</p>", "<p>कोशिका भित्ति</p>",
                                "<p>कोशिका झिल्ली</p>", "<p>क्लोरोफिल</p>"],
                    solution_en: " <p><span style=\"font-family:Times New Roman\">(b) Calcium in the form of calcium pectate is responsible for holding the cell walls of plants together. Calcium deficiency leads to distorted growth of root tips, young leaves and shoot tips.</span></p>",
                    solution_hi: "<p><span style=\"font-family: Times New Roman;\">(b)कैल्शियम पेक्टेट के रूप में कैल्शियम पौधों की कोशिका भित्ति को एक साथ रखने के लिए जिम्मेदार होता है। कैल्शियम की कमी से जड़ के सिरे, नई पत्तियों और प्ररोह युक्तियों का विकृत विकास होता है।</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "18",
                    question_en: " <p>20. Hydrolysis of esters results in the formation of   ",
                    question_hi: "<p><span style=\"font-family: Baloo;\">20. इस्टरों के जलीय संलयन का परिणाम _____ के निर्माण के रूप में होता है | </span></p>",
                    options_en: [" <p> Gasohol</span></p>", " <p> Amino acid  </span></p>", 
                                " <p> Polymer </span></p>", " <p> Alcohol</span></p>"],
                    options_hi: ["<p>गैसोहोल</p>", "<p>एमिनो अम्ल</p>",
                                "<p>बहुलक</p>", "<p>एल्कोहल</p>"],
                    solution_en: " <p><span style=\"font-family:Times New Roman\">(d) Acidic hydrolysis of ester results in carboxylic acid and alcohol while basic hydrolysis of ester gives carboxylate salt and alcohol.</span></p>",
                    solution_hi: "<p><span style=\"font-family: Times New Roman;\">(d) एस्टर के अम्लीय हाइड्रोलिसिस से कार्बोक्जिलिक एसिड और अल्कोहल होता है जबकि एस्टर के मूल हाइड्रोलिसिस से कार्बोक्सिलेट नमक और अल्कोहल मिलता है।</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "18",
                    question_en: "<p>21. Which article of Indian constitution states that &ldquo;There shall be a council of Ministers with the Prime Minister as its head to aid and advise the President who shall, in exercise of his function act in accordance with such advice&rdquo;?</p>",
                    question_hi: "<p><span style=\"font-family: Baloo;\">21. भारतीय संविधान का कौन सा अनुच्छेद कहता है कि, &ldquo;राष्ट्रपति की सहायता करने एवं उन्हें सलाह देने के लिए प्रधानमंत्री की अध्यक्षता में एक मंत्रिपरिषद होगा जिसकी सलाह के अनुसार राष्ट्रपति अपने कर्तव्यों का निर्वहन करेगा |&rdquo; </span></p>",
                    options_en: ["<p>Article 39(1)</p>", "<p>Article 73(2)</p>", 
                                "<p>Article 75(1)</p>", "<p>Article 74(1)</p>"],
                    options_hi: ["<p>अनुच्छेद 39 ( 1 )</p>", "<p>अनुच्छेद 73 ( 2 )</p>",
                                "<p>अनुच्छेद 75 ( 1 )</p>", "<p>अनुच्छेद 74 ( 1 )</p>"],
                    solution_en: "<p><span style=\"font-family: Times New Roman;\">(d) Article 74(1) states that: There shall be a council of ministers with the Prime Minister as its head to aid and advise the President who shall in exercise of his function act in accordance with their advice.</span></p>",
                    solution_hi: "<p><span style=\"font-family: Times New Roman;\">(d) अनुच्छेद 74(1) में कहा गया है कि: राष्ट्रपति की सहायता और सलाह देने के लिए प्रधान मंत्री के साथ एक मंत्रिपरिषद होगी जो अपने कार्य के प्रयोग में उनकी सलाह के अनुसार कार्य करेगा।</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "18",
                    question_en: " <p>22. Who was the world’s oldest living first-class cricketer who passed away at the age of 100?  ",
                    question_hi: "<p><span style=\"font-family: Baloo;\">22. विश्व के सबसे उम्रदराज प्रथम श्रेणी के क्रिकेटर कौन थे जिनका हाल ही में 100 वर्ष की आयु में निधन हो गया?</span></p>",
                    options_en: [" <p> Michael Kindo     </span></p>", " <p> Alan Burgess</span></p>", 
                                " <p> Eric Freeman            </span></p>", " <p> Paulo Rossi</span></p>"],
                    options_hi: ["<p>माइकल किंडो</p>", "<p>एलन बर्गेस</p>",
                                "<p>एरिक फ्रीमैन</p>", "<p>पाउलो रोसी</p>"],
                    solution_en: " <p><span style=\"font-family:Times New Roman\">(b) Alan Burgess a right-hand batsman and slow left-arm bowler, Alan appeared in 11 first-class matches for Canterbury from 1940/41 to 1951/52 and also for New Zealand Services in England in 1945. He took 6-52 on debut against Otago at Lancaster Park in a match that began on Christmas Day 1940.</span></p>",
                    solution_hi: "<p><span style=\"font-family: Times New Roman;\">(b) एलन बर्गेस दाएं हाथ के बल्लेबाज और धीमे बाएं हाथ के गेंदबाज, एलन 1940/41 से 1951/52 तक कैंटरबरी के लिए 11 प्रथम श्रेणी मैचों में और 1945 में इंग्लैंड में न्यूजीलैंड सेवाओं के लिए भी दिखाई दिए। उन्होंने डेब्यू पर 6-52 लिया। क्रिसमस के दिन 1940 से शुरू हुए मैच में लैंकेस्टर पार्क में ओटागो के खिलाफ।</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "23",
                    section: "18",
                    question_en: "<p>23. The unit of gravitational constant does not depend on which of the following?</p>",
                    question_hi: "<p><span style=\"font-family: Baloo;\">23. गुरुत्वीय स्थिरांक की इकाई निम्न में से किस पर निर्भर नहीं करती है ? </span></p>",
                    options_en: ["<p>Mass of object</p>", "<p>force on material</p>", 
                                "<p>distance between objects</p>", "<p>Temperature of atmosphere</p>"],
                    options_hi: ["<p>वस्तु का द्रव्यमान</p>", "<p>वस्तु पर लगा बल</p>",
                                "<p>वस्तुओं के बीच की दूरी</p>", "<p>वायुमंडल का तापमान</p>"],
                    solution_en: "<p><span style=\"font-family: Times New Roman;\">(d) Gravitational constant, also known as G, depends on the following factors: Mass of object, distance between objects and Force on material. Its unit is N-</span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mi mathvariant=\"normal\">m</mi><mn>2</mn></msup><msup><mi>kg</mi><mn>2</mn></msup></mfrac></math></p>",
                    solution_hi: "<p><span style=\"font-family: Times New Roman;\">गुरुत्वाकर्षण स्थिरांक, जिसे G भी कहा जाता है, निम्नलिखित कारकों पर निर्भर करता है: वस्तु का द्रव्यमान, वस्तुओं के बीच की दूरी और सामग्री पर बल। इसकी इकाई N हैN-</span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mi mathvariant=\"normal\">m</mi><mn>2</mn></msup><msup><mi>kg</mi><mn>2</mn></msup></mfrac></math> <span style=\"font-family: Times New Roman;\">है ।&nbsp;</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "24",
                    section: "18",
                    question_en: " <p>24 Which acid is used in the Krebs cycle?  ",
                    question_hi: "<p><span style=\"font-family: Baloo;\">24. क्रेब्स चक्र में किस अम्ल का प्रयोग किया जाता है ? </span></p>",
                    options_en: [" <p> Phosphoenol        </span></p>", " <p> Pyruvic acid</span></p>", 
                                " <p> Malic acid        </span></p>", " <p> Glycogen </span></p>"],
                    options_hi: ["<p>फोस्फेनॉल</p>", "<p>पाइरुविक अम्ल</p>",
                                "<p>मैलिक अम्ल</p>", "<p>ग्लाइकोजन</p>"],
                    solution_en: " <p><span style=\"font-family:Times New Roman\">(b) </span><span style=\"font-family:Times New Roman\">Pyruvic acid supplies energy to living cells through the citric acid cycle (also known as the Krebs cycle ) when oxygen is present (aerobic respiration), it ferments to produce lactic acid when oxygen is lacking ( fermentation ). Pyruvate is the output of the anaerobic metabolism of glucose known as glycolysis.</span></p>",
                    solution_hi: "<p><span style=\"font-family: Times New Roman;\">(b) पाइरुविक एसिड साइट्रिक एसिड चक्र (जिसे क्रेब्स चक्र के रूप में भी जाना जाता है) के माध्यम से जीवित कोशिकाओं को ऊर्जा की आपूर्ति करता है जब ऑक्सीजन मौजूद होता है (एरोबिक श्वसन), यह ऑक्सीजन की कमी (किण्वन) होने पर लैक्टिक एसिड का उत्पादन करने के लिए किण्वित होता है। पाइरूवेट ग्लूकोज के अवायवीय चयापचय का उत्पादन है जिसे ग्लाइकोलाइसिस के रूप में जाना जाता है।</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "25",
                    section: "misc",
                    question_en: " <p>25. Which country has recently signed Chinese firm for hybrid wind and solar energy projects?   ",
                    question_hi: "<p><span style=\"font-family: Baloo;\">25. किस देश ने हाल ही में हाइब्रिड पवन और सौर ऊर्जा परियोजनाओं के लिए चीनी फर्म को अनुबंधित किया हैं?</span></p>",
                    options_en: [" <p> India  </span></p>", " <p> Sri Lanka </span></p>", 
                                " <p> Thailand </span></p>", " <p> Indonesia </span></p>"],
                    options_hi: ["<p>भारत</p>", "<p>श्रीलंका</p>",
                                "<p>थाईलैंड</p>", "<p>इंडोनेशिया</p>"],
                    solution_en: " <p><span style=\"font-family:Times New Roman\">(b) Sri Lanka has cleared a Chinese energy project in three islands off Jaffna peninsula that are barely 50 km from the Tamil Nadu coast.</span></p>",
                    solution_hi: "<p><span style=\"font-family: Times New Roman;\">(b) श्रीलंका ने जाफना प्रायद्वीप से तीन द्वीपों में एक चीनी ऊर्जा परियोजना को मंजूरी दे दी है जो तमिलनाडु तट से मुश्किल से 50 किमी दूर है।</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML - more compact
            let html = `
                <div class="card mb-3">
                    <div class="card-body">
                        <div class="question-text mb-3">
                            <div class="en">${question.question_en}</div>
                            <div class="hi" style="display:none">${question.question_hi}</div>
                        </div>
                        <div class="options">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = 'option-label';
                // When reviewing, highlight user's choice and correct answer
                if (submitted) {
                    if (opt === question.correct) {
                        optionClass += ' border-success bg-success bg-opacity-10';
                    }
                    if (answers[question.id] === opt && opt !== question.correct) {
                        optionClass += ' border-danger bg-danger bg-opacity-10';
                    }
                }
                html += `
                    <div class="form-check mb-2">
                        <input class="form-check-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${answers[question.id] === opt ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                        <label class="form-check-label w-100 ${optionClass}" for="opt-${index}-${opt}">
                            ${submitted && opt === question.correct ? '<i class="fas fa-check-circle text-success me-1"></i>' : ''}
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const alertClass = isCorrect ? 'alert-success' : 'alert-danger';
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="alert ${alertClass} mt-3 py-2">
                        <strong>${resultText}</strong>
                        <div class="mt-1 small">
                            <strong>Solution:</strong>
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                document.getElementById(`box-${index}`).classList.add('attempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { if (currentQuestion > 0) showQuestion(currentQuestion - 1); }
        function nextQuestion() { if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
            // Update button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const content = document.getElementById('main-content');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                if (window.innerWidth >= 992) {
                    content.classList.add('with-nav');
                }
            } else {
                nav.classList.remove('show');
                if (window.innerWidth >= 992) {
                    content.classList.remove('with-nav');
                }
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer - compact
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            if (confirm(`You have attempted ${attempted} of ${questions.length} questions.
${remaining} questions are unattempted.
Are you sure you want to submit?`)) {
                submitTest();
            }
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('bg-secondary', 'text-white');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            resultsModal.show();
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.classList.replace('btn-light', 'btn-success');
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            resultsModal.hide();
            showQuestion(0);
        }
        // Add event listener for window resize
        window.addEventListener('resize', function() {
            if (window.innerWidth >= 992) {
                if (document.getElementById('question-nav').classList.contains('show')) {
                    document.getElementById('main-content').classList.add('with-nav');
                }
            } else {
                document.getElementById('main-content').classList.remove('with-nav');
            }
        });
    </script>
</body>
</html>