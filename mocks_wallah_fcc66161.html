<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">8:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 15</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">15</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 8 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["18"] = {
                name: "General Awareness",
                start: 0,
                end: 13
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="18">General Awareness</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 14,
                end: 14
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "18",
                    question_en: "<p>1. In which state is the Kudankulam Nuclear Power Station located?</p>",
                    question_hi: "<p>1. कुडनकुलम परमाणु ऊर्जा केंद्र किस राज्य में स्थित है</p>",
                    options_en: ["<p>Tamil Nadu</p>", "<p>Gujarat</p>", 
                                "<p>Rajasthan</p>", "<p>Karnataka</p>"],
                    options_hi: ["<p>तमिलनाडू</p>", "<p>गुजरात</p>",
                                "<p>राजस्थान</p>", "<p>कर्नाटक</p>"],
                    solution_en: "<p>1.(a) <strong>Tamil Nadu.</strong> The Kudankulam Nuclear Power Plant (KNPP) is situated in Tirunelveli district.<strong> Major Nuclear Power Station</strong> - Tarapur Nuclear Reactor (Maharashtra), Kalpakkam Nuclear Power Plant (Tamil Nadu), Narora Nuclear Reactor (Uttar Pradesh), Kakrapar Atomic Power Plant (Gujarat), Rawatbhata Nuclear Power Plant(Rajasthan) , Kaiga Atomic Power Station (Karnataka).</p>",
                    solution_hi: "<p>1.(a) <strong>तमिलनाडु।</strong> कुडनकुलम परमाणु ऊर्जा संयंत्र (KNPP) तिरुनेलवेली जिले में स्थित है। <strong>प्रमुख परमाणु ऊर्जा स्टेशन</strong> - तारापुर परमाणु रिएक्टर (महाराष्ट्र), कलपक्कम परमाणु ऊर्जा संयंत्र (तमिलनाडु), नरोरा परमाणु रिएक्टर (उत्तर प्रदेश), काकरापार परमाणु ऊर्जा संयंत्र (गुजरात), रावतभाटा परमाणु ऊर्जा संयंत्र(राजस्थान ) , कैगा परमाणु ऊर्जा स्टेशन (कर्नाटक)।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "2",
                    section: "18",
                    question_en: "<p>2. India&rsquo;s 1st atomic power station was commissioned in</p>",
                    question_hi: "<p>2. भारत का पहला परमाणु ऊर्जा स्टेशन कब शुरू किया गया था ?</p>",
                    options_en: ["<p>1967</p>", "<p>1969</p>", 
                                "<p>1968</p>", "<p>1966</p>"],
                    options_hi: ["<p>1967</p>", "<p>1969</p>",
                                "<p>1968</p>", "<p>1966</p>"],
                    solution_en: "<p>2.(b) <strong>1969. India&rsquo;s 1st atomic power station</strong> - The Tarapur Atomic Power Station. Located - Boisar (Thane, Maharashtra). <strong>Other Nuclear power plants</strong> - Kudankulam Nuclear Power Plant (largest nuclear power station in India, Tamil Nadu), Rawatbhata Atomic Power Station (Rajasthan), Kakrapar Atomic Power Station (Gujarat), Narora Atomic Power Station (Uttar Pradesh), Kalpakkam Atomic Power Station (Tamil Nadu), Kaiga Atomic Energy Centre (Karnataka).</p>",
                    solution_hi: "<p>2.(b)<strong> 1969। भारत का पहला परमाणु ऊर्जा स्टेशन </strong>- तारापुर परमाणु ऊर्जा स्टेशन। स्थित - बोईसर (ठाणे, महाराष्ट्र)। <strong>अन्य परमाणु ऊर्जा संयंत्र</strong> - कुडनकुलम परमाणु ऊर्जा संयंत्र (भारत में सबसे बड़ा परमाणु ऊर्जा स्टेशन, तमिलनाडु ), रावतभाटा परमाणु ऊर्जा स्टेशन (राजस्थान), काकरापार परमाणु ऊर्जा स्टेशन (गुजरात), नरोरा परमाणु ऊर्जा स्टेशन (उत्तर प्रदेश), कलपक्कम परमाणु ऊर्जा स्टेशन (तमिलनाडु), कैगा परमाणु ऊर्जा केंद्र (कर्नाटक)।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "3",
                    section: "18",
                    question_en: "<p>3. Which is the first nuclear reactor made in India?</p>",
                    question_hi: "<p>3. भारत में बना पहला परमाणु रिएक्टर कौन सा है?</p>",
                    options_en: ["<p>Apsara</p>", "<p>CIRUS</p>", 
                                "<p>Dhruva</p>", "<p>KAMINI</p>"],
                    options_hi: ["<p>अप्सरा</p>", "<p>साइरस</p>",
                                "<p>ध्रुव</p>", "<p>कामिनी</p>"],
                    solution_en: "<p>3.(a)<strong> Apsara</strong>. The reactor was designed by the Bhabha Atomic Research Centre (BARC) in Mumbai. <strong>KAMINI</strong> (Kalpakkam Mini reactor) is a research reactor at <strong>Indira Gandhi Center for Atomic Research </strong>in Kalpakkam, India. <strong>CIRUS</strong> (Canada India Reactor Utility Services) was the second nuclear reactor to be built in India, Trombay (Mumbai). <strong>Dhruva reactor</strong> is the largest nuclear research reactor in India.</p>",
                    solution_hi: "<p>3.(a) <strong>अप्सरा</strong>। रिएक्टर को मुंबई में भाभा परमाणु अनुसंधान केंद्र (BARC) द्वारा तैयार किया गया था। <strong>कामिनी</strong> (कलपक्कम मिनी रिएक्टर) कलपक्कम, भारत में <strong>इंदिरा गांधी परमाणु अनुसंधान केंद्र</strong> में एक शोध रिएक्टर है।<strong> साइरस</strong> (कनाडा इंडिया रिएक्टर यूटिलिटी सर्विसेज) भारत, ट्रॉम्बे (मुंबई) में बनने वाला दूसरा परमाणु रिएक्टर था। <strong>ध्रुव रिएक्टर</strong> भारत का सबसे बड़ा परमाणु अनुसंधान रिएक्टर है।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "4",
                    section: "18",
                    question_en: "<p>4. Which is India\'s newest nuclear power plant?</p>",
                    question_hi: "<p>4. भारत का नवीनतम परमाणु ऊर्जा संयंत्र कौन सा है?</p>",
                    options_en: ["<p>Kudankulam</p>", "<p>Kaiga</p>", 
                                "<p>Kalpakkam</p>", "<p>Tarapur</p>"],
                    options_hi: ["<p>कुडनकुलम</p>", "<p>कैगा</p>",
                                "<p>कलपक्कम</p>", "<p>तारापुर</p>"],
                    solution_en: "<p>4.(a) <strong>Kudankulam</strong> (2013, Tirunelveli district, Tamil Nadu). <strong>Tarapur</strong> (first commercial nuclear power station - 1969, Maharashtra), <strong>Kaiga </strong>(2000, Karnataka), <strong>Kalpakkam</strong> (1984, Tamil Nadu). Father of India&rsquo;s nuclear program - <strong>Dr. Homi Jehangir Bhabha.</strong></p>",
                    solution_hi: "<p>4.(a) ​​<strong>कुडनकुलम </strong>(2013, तिरुनेलवेली जिला, तमिलनाडु)।<strong>तारापुर</strong> (प्रथम वाणिज्यिक परमाणु ऊर्जा स्टेशन - 1969, महाराष्ट्र), <strong>कैगा</strong> (2000, कर्नाटक), <strong>कलपक्कम</strong> (1984, तमिलनाडु)। भारत के परमाणु कार्यक्रम के जनक - <strong>डॉ. होमी जहांगीर भाभा।</strong></p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "5",
                    section: "18",
                    question_en: "<p>5. Where was the ﬁrst nuclear power plant set up in India?</p>",
                    question_hi: "<p>5. भारत में पहला परमाणु ऊर्जा संयंत्र कहाँ स्थापित किया गया था?</p>",
                    options_en: ["<p>Kakrapar</p>", "<p>Kaiga</p>", 
                                "<p>Tarapur</p>", "<p>Kalpakkam</p>"],
                    options_hi: ["<p>काकरापार</p>", "<p>कैगा</p>",
                                "<p>तारापुर</p>", "<p>कलपक्कम</p>"],
                    solution_en: "<p>5.(c) <strong>Tarapur</strong> (Maharashtra):- It was commissioned on 28th October 1969. On 8 May 1964, a contract between the Government of India and the United States began as India\'s first atomic power project. <strong>Other Nuclear power plants (Located):</strong> Kakrapar (Gujarat), Kudankulam (Tamil Nadu), Kaiga (Karnataka), Narora (Uttar Pradesh), Rawatbhata (Rajasthan), Kalpakkam (Tamil Nadu).</p>",
                    solution_hi: "<p>5.(c) तारापुर (महाराष्ट्र):- इसे 28 अक्टूबर 1969 को चालू किया गया था। 8 मई 1964 को भारत सरकार और संयुक्त राज्य अमेरिका के बीच भारत की पहली परमाणु ऊर्जा परियोजना के रूप में एक अनुबंध शुरू हुआ। <strong>अन्य परमाणु ऊर्जा संयंत्र (स्थिति):</strong> काकरापार (गुजरात), कुडनकुलम (तमिलनाडु), कैगा (कर्नाटक), नरोरा (उत्तर प्रदेश), रावतभाटा (राजस्थान), कलपक्कम (तमिलनाडु)।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "6",
                    section: "18",
                    question_en: "<p>6. In which year was India\'s first nuclear reactor formally inaugurated by Prime minister J.L. Nehru?</p>",
                    question_hi: "<p>6. प्रधान मंत्री जे.एल. नेहरू द्वारा औपचारिक रूप से भारत के पहले परमाणु रिएक्टर का उद्घाटन किस वर्ष किया गया था?</p>",
                    options_en: ["<p>1955</p>", "<p>1957</p>", 
                                "<p>1967</p>", "<p>1965</p>"],
                    options_hi: ["<p>1955</p>", "<p>1957</p>",
                                "<p>1967</p>", "<p>1965</p>"],
                    solution_en: "<p>6.(b)<strong> 1957</strong>. India\'s first nuclear reactor:- &lsquo;<strong>Apsara</strong>&rsquo;. Designed by :- Bhabha Atomic Research Centre (BARC) and was built with assistance from the United Kingdom. <strong>BARC Headquarters</strong> - Trombay (Mumbai), <strong>Founder</strong> - Homi J. Bhabha. <strong>Important Nuclear Power Plants in India</strong> - Kakrapar Atomic Power Station (Gujarat), Kaiga Nuclear Power Plant (Karnataka), Tarapur Atomic Power Station (Maharashtra), Kudankulam Nuclear Power Plant (Tamil Nadu).</p>",
                    solution_hi: "<p>6.(b) <strong>1957</strong> । भारत का प्रथम परमाणु रिएक्टर:- \'<strong>अप्सरा</strong>\'। भाभा परमाणु अनुसंधान केंद्र (BARC) और यूनाइटेड किंगडम की सहायता से बनाया गया था। <strong>BARC मुख्यालय</strong> - ट्रॉम्बे (मुंबई), संस्थापक - होमी जे. भाभा।<strong> भारत में महत्वपूर्ण परमाणु ऊर्जा संयंत्र</strong> - काकरापार परमाणु ऊर्जा स्टेशन (गुजरात), कैगा परमाणु ऊर्जा संयंत्र (कर्नाटक), तारापुर परमाणु ऊर्जा स्टेशन (महाराष्ट्र), कुडनकुलम परमाणु ऊर्जा संयंत्र (तमिलनाडु)।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "7",
                    section: "18",
                    question_en: "<p>7. Who founded India\'s three stage Nuclear Power Programme?</p>",
                    question_hi: "<p>7. भारत के त्रिस्तरीय परमाणु ऊर्जा कार्यक्रम की स्थापना किसने की?</p>",
                    options_en: ["<p>K Kasturirangan</p>", "<p>Dr. Homi Bhabha</p>", 
                                "<p>A P J Abdul Kalam</p>", "<p>Vikram Sarabhai</p>"],
                    options_hi: ["<p>के कस्तूरीरंगन</p>", "<p>डॉ. होमी भाभा</p>",
                                "<p>ए पी जे अब्दुल कलाम</p>", "<p>विक्रम साराभाई</p>"],
                    solution_en: "<p>7.(b) <strong>Dr. Homi Bhabha</strong>: Insti- He was an Indian nuclear physicist, founding director and professor of physics at the TIFR (Tatatute of Fundamental Research). Three Stage Nuclear Program: Pressurized Heavy Water Reactor (PHWR), Fast Breeder Reactor (FBR), Thorium based Reactor. India&rsquo;s <strong>first nuclear</strong> reactor, <strong>APSARA (Mumbai)</strong>.</p>",
                    solution_hi: "<p>7.(b) <strong>डॉ. होमी भाभा</strong>:- एक भारतीय परमाणु भौतिक विज्ञानी, TIFR (टाटा इंस्टीट्यूट ऑफ फंडामेंटल रिसर्च) में भौतिकी के संस्थापक निदेशक और प्रोफेसर थे। त्रिस्तरीय परमाणु कार्यक्रम: प्रेशराइज़्ड हैवी वाटर रिएक्टर (PHWR), फास्ट ब्रीडर रिएक्टर (FBR), थोरियम बेस्ड रिएक्टर। भारत का <strong>प्रथम परमाणु</strong> रिएक्टर, <strong>अप्सरा (मुंबई)</strong>।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "8",
                    section: "18",
                    question_en: "<p>8. At which place in Haryana is the Government of India developing a nuclear power plant?</p>",
                    question_hi: "<p>8. भारत सरकार हरियाणा में किस स्थान पर परमाणु ऊर्जा संयंत्र विकसित कर रही है?</p>",
                    options_en: ["<p>Gorakhpur</p>", "<p>Palwal</p>", 
                                "<p>Ballabgarh</p>", "<p>Kalka</p>"],
                    options_hi: ["<p>गोरखपुर</p>", "<p>पलवल</p>",
                                "<p>बल्लभगढ़</p>", "<p>कालका</p>"],
                    solution_en: "<p>8.(a) <strong>Gorakhpur</strong> (Fatehabad district). <strong>Other Nuclear power plants</strong> : Tarapur (oldest) - Maharashtra, Rawatbhata - Rajasthan, Kudankulam - Tamil Nadu, Kaiga - Karnataka, Kakrapar - Gujarat, Kalpakkam - Tamil Nadu, Narora - Uttar Pradesh. The largest nuclear power plant in India is at Kudankulam (Tamil Nadu).</p>",
                    solution_hi: "<p>8.(a) <strong>गोरखपुर</strong> (फतेहाबाद जिला)। <strong>अन्य परमाणु ऊर्जा संयंत्र</strong>: तारापुर (सबसे पुराना) - महाराष्ट्र, रावतभाटा - राजस्थान, कुडनकुलम - तमिलनाडु, कैगा - कर्नाटक, काकरापार - गुजरात, कलपक्कम - तमिलनाडु, नरोरा - उत्तर प्रदेश। भारत में सबसे बड़ा परमाणु ऊर्जा संयंत्र कुडनकुलम (तमिलनाडु) में है।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "9",
                    section: "18",
                    question_en: "<p>9. Which of the following is a nuclear research reactor operated by Bhabha Atomic Research Centre?</p>",
                    question_hi: "<p>9. भाभा परमाणु अनुसंधान केंद्र द्वारा संचालित निम्नलिखित में से कौन सा परमाणु अनुसंधान रिएक्टर है?</p>",
                    options_en: ["<p>Dhruva</p>", "<p>Shiva</p>", 
                                "<p>Vishnu</p>", "<p>Narayana</p>"],
                    options_hi: ["<p>ध्रुव</p>", "<p>शिव</p>",
                                "<p>विष्णु</p>", "<p>नारायण</p>"],
                    solution_en: "<p>9.(a) <strong>Dhruva</strong> (India\'s largest nuclear research reactor). It is India\'s primary generator of weapons-grade plutonium-bearing spent fuel for its nuclear weapons program.<strong> &ldquo;Apsara&rdquo;</strong>, was the first research reactor in Asia. Bhabha Atomic Research Centre (BARC) located in Mumbai. <strong>Founder</strong> - Homi J. Bhabha. Establishment - 1954.</p>",
                    solution_hi: "<p>9.(a) <strong>ध्रुव</strong> (भारत का सबसे बड़ा परमाणु अनुसंधान रिएक्टर)। यह भारत के परमाणु हथियार कार्यक्रम के लिए हथियार-ग्रेड प्लूटोनियम-युक्त ईंधन का प्राथमिक जनरेटर है। <strong>\"अप्सरा\"</strong>, एशिया का पहला अनुसंधान रिएक्टर था। भाभा परमाणु अनुसंधान केंद्र (BARC) मुंबई में स्थित है। <strong>संस्थापक</strong> - होमी जे भाभा। स्थापना - 1954 ।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "10",
                    section: "18",
                    question_en: "<p>10. What is the code name of India&rsquo;s first successful Nuclear test?</p>",
                    question_hi: "<p>10. भारत के पहले सफल परमाणु परीक्षण का कोड नाम क्या है?</p>",
                    options_en: ["<p>Operation Shakti</p>", "<p>Laughing Buddha</p>", 
                                "<p>Operation Vijay</p>", "<p>Smiling Buddha</p>"],
                    options_hi: ["<p>ऑपरेशन शक्ति</p>", "<p>लाफिंग बुद्धा</p>",
                                "<p>ऑपरेशन विजय</p>", "<p>स्माइलिंग बुद्धा</p>"],
                    solution_en: "<p>10.(d) <strong>Smiling Buddha.</strong> It was conducted in Pokhran on May 18, 1974. <strong>Operation Shakti</strong> was the code for a successful nuclear test on 11 May 1998. <strong>Operation Vijay (1961),</strong> the operation by the military of India that led to the capture of Goa, Daman and Diu and the Anjediva Islands. <strong>Operation Vijay (1999)</strong> was the Indian operation in the Kargil War. <strong>Laughing Buddha</strong> - Considered as a symbol of happiness, abundance, contentment and wellbeing.</p>",
                    solution_hi: "<p>10.(d) <strong>स्माइलिंग बुद्धा।</strong> यह 18 मई 1974 को पोखरण में संचालित किया गया था। <strong>ऑपरेशन शक्ति</strong>, 11 मई 1998 को एक सफल परमाणु परीक्षण के लिए कोड था। <strong>ऑपरेशन विजय (1961),</strong> भारत की सेना द्वारा किया गया ऑपरेशन है जिसके परिणामस्वरूप गोवा, दमन और दीव तथा अंजेदिवा द्वीप समूह पर कब्ज़ा किया गया। <strong>ऑपरेशन विजय (1999)</strong> कारगिल युद्ध में भारतीय ऑपरेशन था। <strong>लाफिंग बुद्धा</strong> - खुशी, प्रचुरता, संतुष्टि और कल्याण का प्रतीक माना जाता है।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "11",
                    section: "18",
                    question_en: "<p>11. In which year did India conduct its second nuclear test in Pokhran?</p>",
                    question_hi: "<p>11. भारत ने पोखरण में अपना दूसरा परमाणु परीक्षण किस वर्ष किया था?</p>",
                    options_en: ["<p>1996</p>", "<p>2003</p>", 
                                "<p>2001</p>", "<p>1998</p>"],
                    options_hi: ["<p>1996</p>", "<p>2003</p>",
                                "<p>2001</p>", "<p>1998</p>"],
                    solution_en: "<p>11.(d)<strong> 1998.</strong> It consisted of five detonations. The first detonation was a fusion bomb and the remaining four were detonations of fission bombs. The tests were assigned the code : <strong>Operation Shakti</strong>. The first test, code-named <strong>Smiling Buddha</strong>, was conducted in May 1974.</p>",
                    solution_hi: "<p>11.(d) <strong>1998।</strong> इसमें पाँच विस्फोट शामिल थे। जिसमे पहला विस्फोट संलयन बम था और बाकी चार विस्फोट विखंडन बम के थे। परीक्षणों को कोड दिया गया था - <strong>ऑपरेशन शक्ति</strong>। प्रथम परीक्षण, कोड-नेम <strong>स्माइलिंग बुद्धा</strong>, मई 1974 में आयोजित किया गया था।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "12",
                    section: "18",
                    question_en: "<p>12. In 1998 nuclear explosive devices were tested at __________ in India.</p>",
                    question_hi: "<p>12. 1998 में भारत में __________ में परमाणु विस्फोटक उपकरणों का परीक्षण किया गया था।</p>",
                    options_en: ["<p>Thumpa</p>", "<p>Sriharikota</p>", 
                                "<p>Bangalore</p>", "<p>Pokhran</p>"],
                    options_hi: ["<p>थम्पा</p>", "<p>श्रीहरिकोटा</p>",
                                "<p>बैंगलोर</p>", "<p>पोखरण</p>"],
                    solution_en: "<p>12.(d) <strong>Pokhran</strong> is a village located in the Jaisalmer district (Rajasthan). Nuclear Testing in India - India\'s first nuclear test conducted on 18 May 1974 (Code name - Smiling Buddha), Second Nuclear Test - Launched on 11 May 1998 (Codename - Operation Shakti). <strong>National Technology Day </strong>- It is celebrated on 11 May to commemorate the Nuclear test of 1998.</p>",
                    solution_hi: "<p>12.(d) <strong>पोखरण</strong> राजस्थान के जैसलमेर जिले में स्थित एक गाँव है। भारत में परमाणु परीक्षण - भारत का प्रथम परमाणु परीक्षण 18 मई 1974 (कोड नेम - स्माइलिंग बुद्धा) को किया गया , दूसरा परमाणु परीक्षण - 11 मई 1998 (कोड नेम - ऑपरेशन शक्ति) को किया गया। <strong>राष्ट्रीय प्रौद्योगिकी दिवस</strong> - यह 1998 के परमाणु परीक्षण की स्मृति में 11 मई को मनाया जाता है।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "13",
                    section: "18",
                    question_en: "<p>13. Which is the only operating nuclear reactor in the world using U 233 fuel?</p>",
                    question_hi: "<p>13. U 233 ईंधन का उपयोग करने वाला दुनिया का एकमात्र संचालित परमाणु रिएक्टर कौन सा है?</p>",
                    options_en: ["<p>Kamini</p>", "<p>Hanul</p>", 
                                "<p>Dhruva</p>", "<p>Apsara</p>"],
                    options_hi: ["<p>कामिनी</p>", "<p>हनुल</p>",
                                "<p>ध्रुव</p>", "<p>अप्सरा</p>"],
                    solution_en: "<p>13.(a) <strong>Kamini.</strong> The Kamini reactor was designed and built jointly by Bhabha Atomic Research Centre and Indira Gandhi Centre for Atomic Research in Kalpakkam, India. <strong>Apsara</strong> is the oldest Nuclear research reactor in India that was built in August 1956. <strong>Hanul</strong> is a nuclear power plant in South Korea. <strong>Dhruva</strong> is a nuclear research reactor in India.</p>",
                    solution_hi: "<p>13.(a) <strong>कामिनी।</strong> कामिनी रिएक्टर को भारत के कलपक्कम में भाभा परमाणु अनुसंधान केंद्र और इंदिरा गांधी परमाणु अनुसंधान केंद्र द्वारा संयुक्त रूप से डिजाइन और विकसित किया गया था। <strong>अप्सरा</strong> भारत का सबसे पुराना परमाणु अनुसंधान रिएक्टर है जिसे अगस्त 1956 में बनाया गया था। <strong>हनुल</strong> दक्षिण कोरिया का एक परमाणु ऊर्जा संयंत्र है। <strong>ध्रुव</strong> भारत में एक परमाणु अनुसंधान रिएक्टर है।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "14",
                    section: "18",
                    question_en: "<p>14. The nuclear power plant CHASHMA - III is located in:</p>",
                    question_hi: "<p>14. परमाणु ऊर्जा संयंत्र CHASHMA - III कहाँ स्थित है?</p>",
                    options_en: ["<p>Bangladesh</p>", "<p>Pakistan</p>", 
                                "<p>India</p>", "<p>Afghanistan</p>"],
                    options_hi: ["<p>बांग्लादेश</p>", "<p>पाकिस्तान</p>",
                                "<p>भारत</p>", "<p>अफ़ग़ानिस्तान</p>"],
                    solution_en: "<p>14.(b) <strong>Pakistan (Punjab).</strong> <strong>CHASHMA - III</strong> Nuclear Power Plant (CHASNUPP-3) Construction started in 2011 and commercial operation began in 2016. <strong>Operator</strong> - Pakistan Atomic energy commission. <strong>Gross capacity</strong> - 340 MWe. Another nuclear power plant in Pakistan - Karachi Nuclear Power Plant (<strong>KANUPP</strong>). Important Nuclear Power plant sites in India - Kaiga (Karnataka), Tarapur (Maharashtra), Kudankulam (TamilNadu), Kakrapar (Gujarat), Kalpakkam (Chennai), Narora (Uttar Pradesh).</p>",
                    solution_hi: "<p>14.(b) <strong>पाकिस्तान (पंजाब)।</strong> <strong>CHASHMA- III</strong> परमाणु ऊर्जा संयंत्र (CHASNUPP-3) का निर्माण 2011 में शुरू हुआ और वाणिज्यिक संचालन 2016 में शुरू हुआ। <strong>संचालक</strong> - पाकिस्तान परमाणु ऊर्जा आयोग। <strong>सकल क्षमता</strong> - 340 मेगावाट। पाकिस्तान में एक और परमाणु ऊर्जा संयंत्र - कराची परमाणु ऊर्जा संयंत्र (<strong>KANUPP</strong>)। भारत में महत्वपूर्ण परमाणु ऊर्जा संयंत्र स्थल - कैगा (कर्नाटक), तारापुर (महाराष्ट्र), कुडनकुलम (तमिलनाडु), काकरापार (गुजरात), कलपक्कम (चेन्नई), नरोरा (उत्तर प्रदेश)।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "15",
                    section: "misc",
                    question_en: "<p>15. The largest nuclear power station in India is in_______.</p>",
                    question_hi: "<p>15. भारत का सबसे बड़ा परमाणु ऊर्जा केंद्र___________ में है।</p>",
                    options_en: ["<p>Mahabaleshwar</p>", "<p>Tarapur</p>", 
                                "<p>Aurangabad</p>", "<p>Panchgani</p>"],
                    options_hi: ["<p>महाबलेश्वर</p>", "<p>तारापुर</p>",
                                "<p>औरंगाबाद</p>", "<p>पंचगनी</p>"],
                    solution_en: "<p>15.(b) <strong>Tarapur </strong>(Maharashtra). Tarapur Atomic Power Station was (the first nuclear power plant in India) established in 1969. <strong>Nuclear Power Plants In India</strong>: Kakrapar Atomic Power Station (Gujarat), Madras Atomic Power Station (Kalpakkam, Tamil Nadu), Narora Atomic Power Station (Uttar Pradesh), Kaiga Nuclear Power Plant (Karnataka), Tarapur Atomic Power Station (Maharashtra), Kudankulam Nuclear Power Plant (Tamil Nadu).</p>",
                    solution_hi: "<p>15.(b) <strong>तारापुर</strong> (महाराष्ट्र)। तारापुर परमाणु ऊर्जा स्टेशन (भारत में प्रथम परमाणु ऊर्जा संयंत्र) 1969 में स्थापित किया गया था। <strong>भारत में परमाणु ऊर्जा संयंत्र</strong>: काकरापार परमाणु ऊर्जा स्टेशन (गुजरात), मद्रास परमाणु ऊर्जा स्टेशन (कलपक्कम, तमिलनाडु), नरोरा परमाणु ऊर्जा स्टेशन (उत्तर) प्रदेश), कैगा परमाणु ऊर्जा संयंत्र (कर्नाटक), तारापुर परमाणु ऊर्जा स्टेशन (महाराष्ट्र), कुडनकुलम परमाणु ऊर्जा संयंत्र (तमिलनाडु)।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>