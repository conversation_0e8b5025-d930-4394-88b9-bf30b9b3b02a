<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">5:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 15</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">15</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 5 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["18"] = {
                name: "General Awareness",
                start: 0,
                end: 13
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="18">General Awareness</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 14,
                end: 14
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "18",
                    question_en: "1. Which of the following pairs is INCORRECT regarding the grade of organisation and its example?",
                    question_hi: "1. संगठन के ग्रेड और उसके उदाहरण के संबंध में निम्नलिखित में से कौन सी जोड़ी गलत है? ",
                    options_en: [" Cellular grade organisation - Sycon  ", " Protoplasmic grade organisation - Paramecium ", 
                                " Cell-tissue grade organisation - Jellyfish", " Tissue-organ grade organisation - Euplectella"],
                    options_hi: [" कोशिकीय ग्रेड संगठन - साइकोन", " प्रोटोप्लाज्मिक ग्रेड संगठन - पैरामीशियम  ",
                                " कोशिका-ऊतक ग्रेड संगठन - जेलीफ़िश", " ऊतक-अंग ग्रेड संगठन - यूप्लेक्टेला "],
                    solution_en: "1.(d) Euplectella, is a genus of glass sponges which includes the well-known Venus\' Flower Basket, exhibits a cellular grade of organization rather than a tissue-organ grade. A cellular grade of organisation is an aggregation of cells that are functionally differentiated. ",
                    solution_hi: "1.(d) यूप्लेक्टेला, ग्लास स्पंज की एक प्रजाति है जिसमें वीनस फ्लावर बास्केट शामिल है, जो ऊतक-अंग ग्रेड के बजाय सेलुलर ग्रेड का संगठन प्रदर्शित करता है। सेलुलर ग्रेड का संगठन कोशिकाओं का एक समूह है जो कार्यात्मक रूप से विभेदित होते हैं।",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "18",
                    question_en: "2. Which of the following statements best defines the monoecious? ",
                    question_hi: "2. निम्नलिखित में से कौन सा कथन उभयलिंगाश्रयी (monoecious) को सर्वोत्तम रूप से परिभाषित करता है?",
                    options_en: [" A flower with both androecium and gynoecium", " A flower with dithecous  ", 
                                " A flower with gynoecium only", " A flower with androecium only"],
                    options_hi: [" पुमंग और जायांग दोनों से युक्‍त फूल ", " द्विकोष्ठी फूल ",
                                " केवल जायांग वाला फूल ", " केवल पुमंग वाला फूल"],
                    solution_en: "2.(a) Monoecious plants bear both purely male and purely female flowers. Examples include castor, cucumber, and maize. In contrast, dioecious plants have male and female flowers on separate plants, such as papaya. ",
                    solution_hi: "2.(a) उभयलिंगाश्रयी पौधों में नर और मादा दोनों प्रकार के फूल होते हैं। इसके उदाहरणों में अरंडी, खीरा, और मक्का शामिल हैं। इसके विपरीत, द्विलिंगी पौधों में नर और मादा फूल अलग-अलग पौधों पर होते हैं, जैसे कि पपीता।",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "18",
                    question_en: "<p>3. Which condition, also known as icterus, causes a yellowing of your skin and the whites of your eyes?</p>",
                    question_hi: "<p>3. कौन सी स्थिति, जिसे पीलिया (इक्टेरस) के रूप में भी जाना जाता है, आपकी त्वचा के पीलेपन और आपकी आंखों के सफेद होने का कारण बनती है?</p>",
                    options_en: ["<p>Ichthyosis</p>", "<p>Jaundice</p>", 
                                "<p>Eczema</p>", "<p>Pemphigus</p>"],
                    options_hi: ["<p>इक्थियोसिस</p>", "<p>जॉन्डिस</p>",
                                "<p>एक्जिमा</p>", "<p>पेम्फीगस</p>"],
                    solution_en: "<p>3.(b) <strong>Jaundice.</strong> This yellowing occurs due to high levels of bilirubin in the blood. Bilirubin is a yellow pigment produced during the normal breakdown of red blood cells. Ichthyosis is a group of genetic skin disorders that cause dry, scaly, thickened skin. Eczema is a common skin condition that causes itchiness, rashes, dry patches, and infection. Pemphigus is a rare group of autoimmune diseases that cause blisters on the skin and mucous membranes.</p>",
                    solution_hi: "<p>3.(b) <strong>जॉन्डिस।</strong> यह पीलापन रक्त में बिलिरुबिन के उच्च स्तर के कारण होता है। बिलिरुबिन एक पीला वर्णक है जो लाल रक्त कोशिकाओं के सामान्य टूटने के दौरान उत्पन्न होता है। इक्थियोसिस आनुवंशिक त्वचा विकारों का एक समूह है जो शुष्क, परतदार, मोटी त्वचा का कारण बनता है। एक्जिमा एक सामान्य त्वचा की स्थिति है जो खुजली, दाने, सूखे धब्बे, और संक्रमण का कारण बनती है। पेम्फिगस ऑटोइम्यून बीमारियों का एक दुर्लभ समूह है जो त्वचा और श्लेष्म झिल्ली पर छाले उत्पन्न करता है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "18",
                    question_en: "<p>4. Which of the following is an inactivated (killed) polio vaccine developed in 1952?</p>",
                    question_hi: "<p>4. निम्नलिखित में से कौन-सी 1952 में विकसित एक निष्क्रिय (हत) पोलियो वैक्सीन है?</p>",
                    options_en: ["<p>Salk vaccine</p>", "<p>Imvanex vaccine</p>", 
                                "<p>HDCV vaccine</p>", "<p>TAB vaccine</p>"],
                    options_hi: ["<p>साल्क वैक्सीन</p>", "<p>इम्वेनेक्स वैक्सीन</p>",
                                "<p>एच.डी.सी.वी. (HDCV) वैक्सीन</p>", "<p>टैब (TAB) वैक्सीन</p>"],
                    solution_en: "<p>4.(a) <strong>Salk vaccine.</strong> The Inactivated Polio Vaccine (IPV) developed by Jonas Salk and the Oral Polio Vaccine (OPV) developed by Albert Sabin. Polio (poliomyelitis) is a highly infectious viral disease that enters the body through the mouth, multiplying in the intestine before invading the nervous system. Imvanex vaccine used to protect against smallpox. HDCV (Human diploid cell vaccine) is a rabies vaccine. TAB vaccine (Typhoid-paratyphoid A and B) is used for the treatment of typhoid.</p>",
                    solution_hi: "<p>4.(a) <strong>साल्क वैक्सीन। </strong>जोनास साल्क द्वारा विकसित निष्क्रिय पोलियो वैक्सीन (IPV) और अल्बर्ट सबिन द्वारा विकसित ओरल पोलियो वैक्सीन (OPV)। पोलियो (पोलियोमाइलाइटिस) एक अत्यधिक संक्रामक वायरल रोग है जो मुंह के माध्यम से शरीर में प्रवेश करती है, तंत्रिका तंत्र पर आक्रमण करने से पहले आंत में बढ़ता है। इम्वेनेक्स वैक्सीन का उपयोग चेचक से बचाव के लिए किया जाता है। HDCV (ह्यूमन डिप्लोइड सेल वैक्सीन) एक रेबीज वैक्सीन है। टैब (TAB) वैक्सीन (टाइफाइड-पैराटाइफाइड A और B) का उपयोग टाइफाइड के उपचार के लिए किया जाता है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "18",
                    question_en: "5. Which of the following Illustrations shows the mitochondrion’s structural details? <br /> <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1732011529552.png\" alt=\"rId5\" /> ",
                    question_hi: " 5. निम्नलिखित में से कौन-सा चित्रण माइटोकॉन्ड्रिया का संरचनात्मक विवरण दिखाता है?<br /> <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1732011529552.png\" alt=\"rId5\" /> ",
                    options_en: [" d  ", " b", 
                                " c", " a"],
                    options_hi: [" d  ", " b",
                                " c", " a"],
                    solution_en: "5.(b) The mitochondrion is a double-membraned, rod-shaped structure found in both plant and animal cells. Its size ranges from 0.5 to 1.0 micrometre in diameter. The structure comprises an outer membrane, an inner membrane, and a gel-like material called the matrix. ",
                    solution_hi: "5.(b) b. माइटोकॉन्ड्रिया एक दोहरी झिल्लीदार, छड़ के आकार की संरचना है जो पादप एवं जन्तु कोशिकाओं में पायी जाती है। इसका आकार 0.5 से 1.0 माइक्रोमीटर व्यास तक होता है। संरचना में एक बाहरी झिल्ली, एक आंतरिक झिल्ली तथा एक जेल जैसी पदार्थ होती है, जिसे मैट्रिक्स कहा जाता है।",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "18",
                    question_en: "<p>6. Hubbardia heptaneuron, which has become endangered, is a species of which of the<br>following?</p>",
                    question_hi: "<p>6. हब्बार्डिया हेप्टेन्यूरोन (Hubbardia heptaneuron), जो लुप्तप्राय हो गया है, निम्नलिखित में से किसकी प्रजाति है?</p>",
                    options_en: ["<p>Grass</p>", "<p>Bamboo</p>", 
                                "<p>Tiger</p>", "<p>Crane</p>"],
                    options_hi: ["<p>घास</p>", "<p>बाँस</p>",
                                "<p>बाघ</p>", "<p>सारस</p>"],
                    solution_en: "<p>6.(a) <strong>Grass.</strong> Hubbardia heptaneuron is nearing extinction due to its environmental insensitivity. Other endangered plant species include the Gran Canaria Drago Tree, Mandragora, Castela senticosa, Musli, and Assam Catkin. The IUCN\'s \"Red Data Books\" offer the most comprehensive global inventory of the conservation status of plant and animal species.</p>",
                    solution_hi: "<p>6.(a) <strong>घास।</strong> हब्बार्डिया हेप्टेन्यूरोन अपनी पर्यावरण असंवेदनशीलता के कारण विलुप्त होने के कगार पर है। अन्य लुप्तप्राय पौधों की प्रजातियों में ग्रैन कैनरिया ड्रैगो ट्री, मैनड्रैगोरा, कास्टेला सेंटिकोसा, मूसली और असम कैटकिन शामिल हैं। IUCN की \"रेड डेटा बुक्स\" पौधों तथा जानवरों की प्रजातियों की संरक्षण स्थिति की सबसे व्यापक वैश्विक सूची प्रदान करती है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "18",
                    question_en: "<p>7. Which of the following is a non-perishable food?</p>",
                    question_hi: "<p>7. निम्नलिखित में से कौन-सा अविकारीय भोजन है?</p>",
                    options_en: ["<p>Pulses</p>", "<p>Meat</p>", 
                                "<p>Milk</p>", "<p>Curds</p>"],
                    options_hi: ["<p>दाल</p>", "<p>मांस</p>",
                                "<p>दूध</p>", "<p>दही</p>"],
                    solution_en: "<p>7.(a)<strong> Pulses. </strong>Non-perishable foods are items that can be stored at room temperature for extended periods without spoiling. Examples include beans, coffee, honey, powdered milk, rice, and wheat. In contrast, frozen foods are not classified as non-perishable.</p>",
                    solution_hi: "<p>7.(a)<strong> दाल।</strong> अविकारीय खाद्य पदार्थ वे पदार्थ हैं जिन्हें बिना खराब हुए लंबे समय तक कमरे के तापमान पर रखा जा सकता है। उदाहरणों में बीन्स, कॉफी, शहद, पाउडर वाला दूध, चावल और गेहूं शामिल हैं। इसके विपरीत, फ्रोज़ेन खाद्य पदार्थ को अविकारीय के रूप में वर्गीकृत नहीं किया जाता है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "18",
                    question_en: "<p>8. Which organisms are classified as Aves?</p>",
                    question_hi: "<p>8. कौन से जीवों को एवीज़ (Aves) के रूप में वर्गीकृत किया गया है?</p>",
                    options_en: ["<p>Fishes</p>", "<p>Frogs</p>", 
                                "<p>Snakes</p>", "<p>Birds</p>"],
                    options_hi: ["<p>मछली</p>", "<p>मेंढक</p>",
                                "<p>साँप</p>", "<p>पक्षी</p>"],
                    solution_en: "<p>8.(d)<strong> Birds.</strong> Aves, commonly known as birds, are a class of endothermic vertebrates under the phylum Chordata. They are characterized by features such as toothless beaks and laying hard-shelled eggs. Fish belong to the Animalia kingdom, phylum Chordata, and subphylum Vertebrata, and are aquatic vertebrates equipped with gills and fins. Frogs are amphibians, a group that also includes toads, newts, salamanders, and caecilians. Snakes fall under the class Reptilia, order Squamata, and suborder Serpentes.</p>",
                    solution_hi: "<p>8.(d) <strong>पक्षी।</strong> एवीज़, जिन्हें आम तौर पर पक्षी के रूप में जाना जाता है, कॉर्डेटा संघ के अंतर्गत एंडोथर्मिक कशेरुकियों का एक वर्ग है। इनकी विशेषता दांत रहित चोंच और कठोर छिलके वाले अंडे देने जैसी विशेषताएं हैं। मछलियाँ एनिमेलिया जगत, कॉर्डेटा संघ और वर्टेब्रेटा उपसंघ से संबंधित हैं, तथा गलफड़ों और पंखों से सुसज्जित जलीय कशेरुकी हैं। मेंढक उभयचर हैं, जो एक ऐसा समूह जिसमें टोड, न्यूट, सैलामैंडर और सीसिलियन भी शामिल हैं। साँप रेप्टिलिया वर्ग, स्क्वामाटा गण, और सर्पेन्टेस उपगण के अंतर्गत आते हैं।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "18",
                    question_en: "<p>9. Which species is known as black lipped pearl oyster found in the Indo-Pacific, within tropical coral reefs?</p>",
                    question_hi: "<p>9. किस प्रजाति को ब्लैक-लिप्ड मोती सीप के रूप में जाना जाता है जो हिंद-प्रशांत क्षेत्र में उष्णकटिबंधीय प्रवाल भित्तियों के भीतर पाई जाती है?</p>",
                    options_en: ["<p>Aplysia dactylomela</p>", "<p>Dentalium neohexagonum</p>", 
                                "<p>Chaetopleura apiculata</p>", "<p>Pinctada margaritifera</p>"],
                    options_hi: ["<p>ऐप्लीसिया डैक्टाइलोमेला (Aplysia dactylomela)</p>", "<p>डेंटलियम नियोहेक्सागोनम (Dentalium neohexagonum)</p>",
                                "<p>कीटोप्ल्यूरा एपीकुलाटा (Chaetopleura apiculata)</p>", "<p>पिंकटाडा मार्गरिटिफेरा (Pinctada margaritifera)</p>"],
                    solution_en: "<p>9.(d)<strong> Pinctada margaritifera. </strong>This species is prized for its pearls and is widely cultivated for pearl production. Aplysia dactylomela : This is a black sea hare, a large sea slug found in tropical waters. Dentalium neohexagonum: This is a deep-sea tusk shell species native to the Indo-Pacific region. Chaetopleura apiculata: This is a type of chiton, a marine mollusk with an armored shell.</p>",
                    solution_hi: "<p>9.(d) <strong>पिंकटाडा मार्गरिटिफेरा</strong> (Pinctada margaritifera)। यह प्रजाति अपने मोतियों के लिए बेशकीमती है और मोती उत्पादन के लिए व्यापक रूप से इसकी खेती की जाती है। एप्लीसिया डैक्टाइलोमेला: यह एक ब्लैक सी हेयर है, जो उष्णकटिबंधीय जल में पाया जाने वाला एक बड़ा समुद्री स्लग है। डेंटलियम नियोहेक्सागोनम: यह एक गहरे समुद्र में रहने वाली टस्क शैल प्रजाति है जो भारत-प्रशांत क्षेत्र में पाई जाती है। कीटोप्ल्यूरा एपीकुलाटा: यह एक प्रकार का कैटन है, जो बख्तरबंद खोल वाला समुद्री मोलस्क है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "18",
                    question_en: "<p>10. Which of the following options is associated with the class of cold-blooded animals?</p>",
                    question_hi: "<p>10. निम्न में से कौन-सा विकल्प ठंडे खून वाले जंतुओं के वर्ग से संबंधित है?</p>",
                    options_en: ["<p>Chameleon</p>", "<p>Pavo</p>", 
                                "<p>Macropus</p>", "<p>Psittacula</p>"],
                    options_hi: ["<p>गिरगिट (Chameleon)</p>", "<p>पावो (Pavo)</p>",
                                "<p>मैक्रोपस (Macropus)</p>", "<p>सिटाकुला (Psittacula)</p>"],
                    solution_en: "<p>10.(a) <strong>Chameleons are reptiles. </strong>Cold-Blooded Animals : Cold-blooded animals, like reptiles and amphibians, have a body temperature that varies with their environment. Examples : Snakes, Lizards, Turtles, Frogs, Fish. Warm-blooded animals are also known as endothermic animals, can generate and maintain their own body heat. This category includes birds and mammals.</p>",
                    solution_hi: "<p>10.(a) <strong>गिरगिट </strong>(Chameleon) सरीसृप हैं। अनियततापी जन्तु : सरीसृप और उभयचरों जैसे अनियततापी जन्तुओं के शरीर का तापमान पर्यावरण के साथ बदलता रहता है। उदाहरण : साँप, छिपकली, कछुए, मेंढक, मछली। नियततापी जन्तु को एंडोथर्मिक जन्तु भी कहा जाता है, ये अपने शरीर की ऊष्मा स्वयं उत्पन्न कर सकते हैं और बनाए रख सकते हैं। इस श्रेणी में पक्षी और स्तनधारी शामिल हैं।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "18",
                    question_en: "11.  Which of the following characteristics is NOT of Aves ?",
                    question_hi: "11. निम्नलिखित में से कौन-सी विशेषता, एवीज़ (Aves) की नहीं है?",
                    options_en: [" They have a four-chambered heart.", " They give birth to live young ones with some exceptions that lay eggs.", 
                                "  These are warm-blooded animals.", " They breathe through the lungs"],
                    options_hi: [" इनका हृदय चार कक्षीय होता है।", " ये जीवित बच्चों को जन्म देते हैं, कुछ अपवादों को छोड़कर जो कि अंडे देते हैं। ",
                                "  ये गर्म खून वाले जीव होते हैं।", " ये फेफड़ों से सांस लेते हैं।"],
                    solution_en: "11.(b) The characteristic features of Aves (birds) are the presence of feathers and most of them can fly except flightless birds (e.g., Ostrich). The forelimbs are modified into wings. The hind limbs generally have scales and are modified for walking, swimming or clasping the tree branches. Endoskeleton is fully ossified (bony) and the long bones are hollow with air cavities (pneumatic). Examples- Corvus (Crow), Columba (Pigeon), Psittacula (Parrot), Struthio (Ostrich), Pavo (Peacock), Aptenodytes (Penguin), Neophron (Vulture).",
                    solution_hi: "11.(b) एवीज़ (पक्षियों) की विशिष्ट विशेषताएँ पंखों की उपस्थिति हैं और उड़ने में असमर्थ पक्षियों (उदाहरण के लिए, शुतुरमुर्ग) को छोड़कर उनमें से अधिकांश उड़ सकते हैं। अग्र पाद पंखों में रूपांतरित हो गए हैं। पिछले पैरों में आमतौर पर शल्क होते हैं और वे चलने, तैरने या पेड़ की शाखाओं को पकड़ने के लिए संशोधित होते हैं। अंतःकंकाल पूर्णतः अस्थिकृत (bony) होता है तथा लम्बी हड्डियां वायुगुहाओं (pneumatic) सहित खोखली होती हैं। उदाहरण - कोरवस (कौआ), कोलंबा (कबूतर), सिटाकुला (तोता), स्ट्रूथियो (शुतुरमुर्ग), पावो (मोर), एप्टेनोडाइट्स (पेंगुइन), नियोफ्रोन (गिद्ध)।",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "18",
                    question_en: "<p>12. Which cell organelle is defined as the small round organelle that undergoes oxidation reaction to produce hydrogen peroxide?</p>",
                    question_hi: "<p>12. किस कोशिका अंगक को छोटे गोलाकार कोशिकांग के रूप में परिभाषित किया गया है जो हाइड्रोजन पेरोक्साइड का उत्पादन करने के लिए ऑक्सीकरण अभिक्रिया से गुजरता है?</p>",
                    options_en: ["<p>Centrosome</p>", "<p>Vacuole</p>", 
                                "<p>Nucleus</p>", "<p>Peroxisomes</p>"],
                    options_hi: ["<p>सेंट्रोसोम</p>", "<p>वैकुओल</p>",
                                "<p>न्यूक्लियस</p>", "<p>पेरोक्सीसोम्स</p>"],
                    solution_en: "<p>12.(d) <strong>Peroxisomes.</strong> Peroxisomes have multiple functions like lipid metabolism, processing reactive oxygen species, oxidative processes, and catabolism of D-amino acids, polyamines, and bile acids, with enzymes like peroxidase and catalase converting harmful peroxides to water; in plants, peroxisomes facilitate photosynthesis, seed germination, and efficient carbon fixation.</p>",
                    solution_hi: "<p>12.(d) <strong>पेरोक्सीसोम्स ।</strong> पेरोक्सीसोम्स के कई कार्य हैं, जैसे लिपिड चयापचय, क्रियाशील ऑक्सीजन प्रजातियों का प्रसंस्करण, ऑक्सीडेटिव प्रक्रियाएं, और डी-अमीनो एसिड, पॉलीमाइन और पित्त एसिड का अपचय, पेरोक्सीडेज और कैटालेज जैसे एंजाइम हानिकारक पेरोक्साइड को जल में परिवर्तित करते हैं; तथा पौधों में, पेरोक्सीसोम्स प्रकाश संश्लेषण, बीज अंकुरण और कुशल कार्बन निर्धारण की सुविधा प्रदान करते हैं।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "18",
                    question_en: "<p>13. Which of the following categories does Gonyaulax belong to?</p>",
                    question_hi: "<p>13. निम्नलिखित में से कौन-सी श्रेणी गोनिओलेक्स (Gonyaulax) से संबंधित है?</p>",
                    options_en: ["<p>Euglenoids</p>", "<p>Chrysophytes</p>", 
                                "<p>Protozoans</p>", "<p>Dinoflagellates</p>"],
                    options_hi: ["<p>यूग्लीनोएड्स (Euglenoids)</p>", "<p>क्राइसोफाइट्स (Chrysophytes)</p>",
                                "<p>प्रोटोजोअन्&zwj;स (Protozoans)</p>", "<p>डाइनोफ्लैजलेट्स (Dinoflagellates)</p>"],
                    solution_en: "<p>13.(d) <strong>Dinoflagellates. </strong>These organisms are mainly marine, photosynthetic organisms that can appear in various colors such as yellow, green, brown, blue, or red, depending on their pigments. Euglenoids include Euglena, Chrysophytes encompass diatoms and golden algae, while Amoeba represents protozoans.</p>",
                    solution_hi: "<p>13.(d) <strong>डाइनोफ्लैजलेट्स </strong>(Dinoflagellates)। ये जीव मुख्य रूप से समुद्री, प्रकाश संश्लेषक जीव हैं जो अपने रंगद्रव्य के आधार पर पीले, हरे, भूरे, नीले या लाल जैसे विभिन्न रंगों में दिखाई दे सकते हैं। यूग्लेनोइड्स में यूग्लेना शामिल है, क्राइसोफाइट्स में डायटम और गोल्डन शैवाल शामिल हैं, जबकि अमीबा प्रोटोजोआ को दर्शाता है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "18",
                    question_en: "<p>14. Taxus wallichiana Zucc (Himalayan yew) is a medicinal plant found in which of the following states?</p>",
                    question_hi: "<p>14. टैक्सस वालिचियाना (Taxus wallichiana) ज़ुक्क (हिमालयी यू) एक औषधीय पौधा है जो निम्नलिखित में से किस राज्य में पाया जाता है?</p>",
                    options_en: ["<p>Goa</p>", "<p>Jharkhand</p>", 
                                "<p>Himachal Pradesh</p>", "<p>Bihar</p>"],
                    options_hi: ["<p>गोवा</p>", "<p>झारखंड</p>",
                                "<p>हिमाचल प्रदेश</p>", "<p>बिहार</p>"],
                    solution_en: "<p>14.(c) <strong>Himachal Pradesh. </strong>The Himalayan yew grows in the high-altitude forests of the Western Himalayas, especially in the states of Himachal Pradesh, Uttarakhand, and parts of Jammu &amp; Kashmir. The cool and moist environment of these regions provides the ideal conditions for this plant. This plant has traditionally been used to treat epilepsy, respiratory infections, colds, cough, asthma, and liver disorders.</p>",
                    solution_hi: "<p>14.(c) <strong>हिमाचल प्रदेश। </strong>हिमालयी यू पश्चिमी हिमालय के ऊंचे जंगलों में, खासकर हिमाचल प्रदेश, उत्तराखंड तथा जम्मू और कश्मीर के कुछ हिस्सों में उगता है। इन क्षेत्रों का ठंडा और आर्द्र वातावरण इस पौधे के लिए आदर्श परिस्थितियाँ प्रदान करता है। इस पौधे का उपयोग पारंपरिक रूप से मिर्गी, श्वसन संक्रमण, सर्दी, खांसी, अस्थमा एवं यकृत विकारों के इलाज के लिए किया जाता है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "misc",
                    question_en: "15. Which of the following pairs is INCORRECTLY matched?",
                    question_hi: "15. निम्नलिखित में से कौन-सा युग्म गलत सुमेलित है?",
                    options_en: [" Nucleus: Lipid metabolism", " Lysosomes: Suicidal bags", 
                                " Mitochondria: Power house of the cell", " Ribosomes: Protein factory"],
                    options_hi: [" नाभिक: लिपिड चयापचय", " लाइसोसोम: आत्मघाती थैली",
                                " माइटोकॉन्ड्रिया: सेल का पावर हाउस", " राइबोसोम: प्रोटीन का कारखाना"],
                    solution_en: "15.(a) The nucleus is primarily involved in storing genetic material and regulating gene expression. Lipids are required for the formation of membranes and contribute to many different processes such as cell signaling, energy supply, and cell death. Various organelles such as the endoplasmic reticulum, mitochondria, peroxisomes, and lipid droplets are involved in lipid metabolism. ",
                    solution_hi: "15.(a) नाभिक मुख्य रूप से आनुवंशिक पदार्थ को संग्रहीत करने और जीन अभिव्यक्ति को विनियमित करने में शामिल है। झिल्ली के निर्माण के लिए लिपिड की आवश्यकता होती है और यह कोशिका संकेतन, ऊर्जा आपूर्ति एवं कोशिका मृत जैसी कई अलग-अलग प्रक्रियाओं में योगदान देता है। एंडोप्लाज्मिक रेटिकुलम, माइटोकॉन्ड्रिया, पेरॉक्सिसोम और लिपिड ड्रॉपलेट्स जैसे विभिन्न अंग लिपिड चयापचय में शामिल होते हैं।",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>