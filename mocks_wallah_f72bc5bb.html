<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">25:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 25</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">25</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 25 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 0,
                end: 23
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 24,
                end: 24
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "17",
                    question_en: "<p>1. If sin A - cos A = <math display=\"inline\"><mfrac><mrow><msqrt><mn>3</mn></msqrt><mo>-</mo><msqrt><mn>2</mn></msqrt></mrow><mrow><mn>2</mn></mrow></mfrac></math>, then the value of sin A. cos A is:</p>",
                    question_hi: "<p>1. यदि sin A - cos A <math display=\"inline\"><mfrac><mrow><msqrt><mn>3</mn></msqrt><mo>-</mo><msqrt><mn>2</mn></msqrt></mrow><mrow><mn>2</mn></mrow></mfrac></math> हो, तो sin A. cos A का मान ज्ञात कीजिए।</p>",
                    options_en: ["<p><math display=\"inline\"><mfrac><mrow><mn>2</mn><msqrt><mn>3</mn></msqrt><mo>-</mo><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>2</mn><msqrt><mn>2</mn></msqrt><mo>-</mo><mn>1</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math></p>", 
                                "<p><math display=\"inline\"><mfrac><mrow><mn>2</mn><msqrt><mn>6</mn></msqrt><mo>-</mo><mn>1</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>3</mn><msqrt><mn>2</mn></msqrt><mo>-</mo><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math></p>"],
                    options_hi: ["<p><math display=\"inline\"><mfrac><mrow><mn>2</mn><msqrt><mn>3</mn></msqrt><mo>-</mo><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>2</mn><msqrt><mn>2</mn></msqrt><mo>-</mo><mn>1</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math></p>",
                                "<p><math display=\"inline\"><mfrac><mrow><mn>2</mn><msqrt><mn>6</mn></msqrt><mo>-</mo><mn>1</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>3</mn><msqrt><mn>2</mn></msqrt><mo>-</mo><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math></p>"],
                    solution_en: "<p>1.(c) sin A - cos A = <math display=\"inline\"><mfrac><mrow><msqrt><mn>3</mn></msqrt><mo>-</mo><msqrt><mn>2</mn></msqrt></mrow><mrow><mn>2</mn></mrow></mfrac></math><br>Taking square both side,<br><math display=\"inline\"><msup><mrow><mo>(</mo><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#160;</mi><mi>A</mi><mi>&#160;</mi><mo>-</mo><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#160;</mi><mi>A</mi><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mfrac><msqrt><mn>3</mn><mo>-</mo><msqrt><mn>2</mn></msqrt></msqrt><mn>2</mn></mfrac><mo>)</mo></mrow><mn>2</mn></msup></math><br><math display=\"inline\"><msup><mrow><mi>s</mi><mi>i</mi><mi>n</mi></mrow><mrow><mn>2</mn></mrow></msup></math>A + cos<sup>2</sup>A - 2sinA.cosA = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>2</mn><mo>&#160;</mo><mo>-</mo><msqrt><mn>6</mn></msqrt></mrow><mn>4</mn></mfrac></math><br>1 - 2sinA.cosA = <math display=\"inline\"><mfrac><mrow><mn>5</mn><mi>&#160;</mi><mo>-</mo><mn>2</mn><msqrt><mn>6</mn></msqrt></mrow><mrow><mn>4</mn></mrow></mfrac></math><br>2sinA.cosA = 1- <math display=\"inline\"><mfrac><mrow><mn>5</mn><mi>&#160;</mi><mo>-</mo><mn>2</mn><msqrt><mn>6</mn></msqrt></mrow><mrow><mn>4</mn></mrow></mfrac></math><br>2sinA.cosA = <math display=\"inline\"><mfrac><mrow><mn>4</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>5</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>2</mn><msqrt><mn>6</mn></msqrt></mrow><mrow><mn>4</mn></mrow></mfrac></math><br>sinA.cosA = <math display=\"inline\"><mfrac><mrow><mo>-</mo><mn>1</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>2</mn><msqrt><mn>6</mn></msqrt></mrow><mrow><mn>8</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><msqrt><mn>6</mn><mo>-</mo><mn>1</mn></msqrt></mrow><mn>8</mn></mfrac></math></p>",
                    solution_hi: "<p>1.(c) sin A - cos A = <math display=\"inline\"><mfrac><mrow><msqrt><mn>3</mn></msqrt><mo>-</mo><msqrt><mn>2</mn></msqrt></mrow><mrow><mn>2</mn></mrow></mfrac></math><br>दोनों तरफ वर्ग करने पर ,<br><math display=\"inline\"><msup><mrow><mo>(</mo><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#160;</mi><mi>A</mi><mi>&#160;</mi><mo>-</mo><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#160;</mi><mi>A</mi><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mfrac><msqrt><mn>3</mn><mo>-</mo><msqrt><mn>2</mn></msqrt></msqrt><mn>2</mn></mfrac><mo>)</mo></mrow><mn>2</mn></msup></math><br><math display=\"inline\"><msup><mrow><mi>s</mi><mi>i</mi><mi>n</mi></mrow><mrow><mn>2</mn></mrow></msup></math>A + cos<sup>2</sup>A - 2sinA.cosA = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>2</mn><mo>&#160;</mo><mo>-</mo><msqrt><mn>6</mn></msqrt></mrow><mn>4</mn></mfrac></math><br>1 - 2sinA.cosA = <math display=\"inline\"><mfrac><mrow><mn>5</mn><mi>&#160;</mi><mo>-</mo><mn>2</mn><msqrt><mn>6</mn></msqrt></mrow><mrow><mn>4</mn></mrow></mfrac></math><br>2sinA.cosA = 1- <math display=\"inline\"><mfrac><mrow><mn>5</mn><mi>&#160;</mi><mo>-</mo><mn>2</mn><msqrt><mn>6</mn></msqrt></mrow><mrow><mn>4</mn></mrow></mfrac></math><br>2sinA.cosA = <math display=\"inline\"><mfrac><mrow><mn>4</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>5</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>2</mn><msqrt><mn>6</mn></msqrt></mrow><mrow><mn>4</mn></mrow></mfrac></math><br>sinA.cosA = <math display=\"inline\"><mfrac><mrow><mo>-</mo><mn>1</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>2</mn><msqrt><mn>6</mn></msqrt></mrow><mrow><mn>8</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><msqrt><mn>6</mn><mo>-</mo><mn>1</mn></msqrt></mrow><mn>8</mn></mfrac></math></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "17",
                    question_en: "<p>2. The following bar chart shows the year-wise percentage hike in the fuel price with respect to the previous year<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731059464223.png\" alt=\"rId4\" width=\"297\" height=\"257\"> <br>In how many years did the fuel prices NOT rise?</p>",
                    question_hi: "<p>2. निम्न बार-चार्ट पिछले वर्ष के संदर्भ में ईंधन की कीमत में वर्ष-वार प्रतिशत वृद्धि दर्शाता है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731059464349.png\" alt=\"rId5\" width=\"323\" height=\"280\"> <br>Fuel price hike (in %) = ईंधन की कीमत में वृद्धि (% में) <br>कितने वर्षों में ईंधन की कीमतों में कोई वृद्धि नहीं हुई?</p>",
                    options_en: ["<p>0</p>", "<p>1</p>", 
                                "<p>2</p>", "<p>5</p>"],
                    options_hi: ["<p>0</p>", "<p>1</p>",
                                "<p>2</p>", "<p>5</p>"],
                    solution_en: "<p>2.(a)<br>From the bar chart we can see that every year there is a rise in fuel prices.</p>",
                    solution_hi: "<p>2.(a)<br>बार चार्ट से हम देख सकते हैं कि हर साल ईंधन की कीमतों में बढ़ोतरी हो रही है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "17",
                    question_en: "<p>3. The least number which must be subtracted from 7278745 so as to obtain a sum divisible by 11 is:</p>",
                    question_hi: "<p>3. 11 से विभाज्य संख्या प्राप्त करने के लिए 7278745 में से घटाई जाने वाली छोटी से छोटी संख्या कौन-सी है?</p>",
                    options_en: ["<p>3</p>", "<p>1</p>", 
                                "<p>5</p>", "<p>2</p>"],
                    options_hi: ["<p>3</p>", "<p>1</p>",
                                "<p>5</p>", "<p>2</p>"],
                    solution_en: "<p>3.(b) Divisible of 11 : if the difference between the sum of the digits at odd places and the sum of the digits at even places is 0 or multiple of 11<br>7278745<br><math display=\"inline\"><mo>&#8658;</mo></math> (7 + 7 + 7 + 5) - (2 + 8 + 4)&nbsp;<br><math display=\"inline\"><mo>&#8658;</mo></math> 26 - 14 = 12 <br>It is clear that if 1 is subtracted from this number (786452), then this number will become divisible by 11.</p>",
                    solution_hi: "<p>3.(b) 11 की विभाज्यता: यदि विषम स्थानों पर अंकों के योग और सम स्थानों पर अंकों के योग के बीच का अंतर 0 या 11 का गुणक है<br>7278745<br><math display=\"inline\"><mo>&#8658;</mo></math> (7 + 7 + 7 + 5) - (2 + 8 + 4)&nbsp;<br><math display=\"inline\"><mo>&#8658;</mo></math> 26 - 14 = 12 <br>स्पष्ट है कि यदि इस संख्या (786452) में से 1 घटा दिया जाये तो यह संख्या 11 से विभाज्य हो जायेगी।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "17",
                    question_en: "<p>4. A motorboat, whose speed is 15 km/h in still water, goes 50 km downstream and comes back in a total of 7 hours 30 minutes. The speed of the stream (in km/h) is:</p>",
                    question_hi: "<p>4. एक मोटरबोट, जिसकी स्थिर जल में चाल 15 km/h है, को धारा के अनुकूल 50 km जाने और वापस आने में 7 घंटे 30 मिनट का समय लगता है। धारा की चाल (km/h में) की गणना करें।</p>",
                    options_en: ["<p>9</p>", "<p>5</p>", 
                                "<p>11</p>", "<p>7</p>"],
                    options_hi: ["<p>9</p>", "<p>5</p>",
                                "<p>11</p>", "<p>7</p>"],
                    solution_en: "<p>4.(b) Let the speed of a stream be <math display=\"inline\"><mi>x</mi></math> km/h<br><math display=\"inline\"><mo>&#8658;</mo></math><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>50</mn><mrow><mn>15</mn><mo>-</mo><mi>x</mi></mrow></mfrac><mo>+</mo><mfrac><mn>50</mn><mrow><mn>15</mn><mo>+</mo><mi>x</mi></mrow></mfrac></math>&nbsp;= 7.5<br>In this type of question by using hit and trial method,<br>Put the value of <math display=\"inline\"><mi>x</mi></math> = 5, <br>LHS = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>50</mn><mrow><mn>15</mn><mo>-</mo><mi>x</mi></mrow></mfrac><mo>+</mo><mfrac><mn>50</mn><mrow><mn>15</mn><mo>+</mo><mi>x</mi></mrow></mfrac></math>&nbsp;<br>= <math display=\"inline\"><mfrac><mrow><mn>50</mn></mrow><mrow><mn>15</mn><mi>&#160;</mi><mo>-</mo><mn>5</mn></mrow></mfrac></math> + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>50</mn><mrow><mn>15</mn><mo>+</mo><mn>5</mn></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>50</mn><mn>10</mn></mfrac><mo>+</mo><mfrac><mn>50</mn><mn>20</mn></mfrac></math>&nbsp;= 5 + 2.5 = 7.5 = RHS<br>Value of <math display=\"inline\"><mi>x</mi></math> satisfies the equation,<br>So, the speed of a stream is 5 km/h</p>",
                    solution_hi: "<p>4.(b) माना धारा की गति x किमी/घंटा है<br><math display=\"inline\"><mo>&#8658;</mo></math><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>50</mn><mrow><mn>15</mn><mo>-</mo><mi>x</mi></mrow></mfrac><mo>+</mo><mfrac><mn>50</mn><mrow><mn>15</mn><mo>+</mo><mi>x</mi></mrow></mfrac></math> = 7.5<br>इस प्रकार के प्रश्न में हिट एण्ड ट्रायल विधि का प्रयोग करे,<br><math display=\"inline\"><mi>x</mi></math> = 5 रखने पर ,<br>LHS = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>50</mn><mrow><mn>15</mn><mo>-</mo><mi>x</mi></mrow></mfrac><mo>+</mo><mfrac><mn>50</mn><mrow><mn>15</mn><mo>+</mo><mi>x</mi></mrow></mfrac></math>&nbsp;<br>= <math display=\"inline\"><mfrac><mrow><mn>50</mn></mrow><mrow><mn>15</mn><mi>&#160;</mi><mo>-</mo><mn>5</mn></mrow></mfrac></math> + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>50</mn><mrow><mn>15</mn><mo>+</mo><mn>5</mn></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>50</mn><mn>10</mn></mfrac><mo>+</mo><mfrac><mn>50</mn><mn>20</mn></mfrac></math> = 5 + 2.5 = 7.5 = RHS<br>x का मान समीकरण को संतुष्ट करता है,<br>तो, धारा की गति 5 किमी/घंटा है</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "17",
                    question_en: "<p>5. The length, breadth and height of a room is 15 m, 9 m, and 5 m, respectively. From each can of paint 40 square metre of area is painted. How many cans of paint will be needed to paint only the walls of the room?</p>",
                    question_hi: "<p>5. एक कमरे की लंबाई, चौड़ाई और ऊँचाई क्रमशः 15 m, 9 m और 5 m है। पेंट के एक डिब्बे से 40 वर्ग-मीटर क्षेत्रफल को पेंट किया जा सकता है। केवल कमरे की दीवारों को पेंट करने के लिए पेंट के कितने डिब्बों की आवश्यकता होगी?</p>",
                    options_en: ["<p>12</p>", "<p>6</p>", 
                                "<p>4</p>", "<p>8</p>"],
                    options_hi: ["<p>12</p>", "<p>6</p>",
                                "<p>4</p>", "<p>8</p>"],
                    solution_en: "<p>5.(b) surface area of four walls = 2h(l + b)<br>= 2 &times; 5(15 + 9)<br>= 10(24) =&nbsp; 240 m<sup>2</sup><br>So,<br>The number of cans = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>240</mn><mn>40</mn></mfrac></math>= 6&nbsp;&nbsp;&nbsp;</p>\n<p>&nbsp;</p>",
                    solution_hi: "<p dir=\"ltr\">5.(b) चारों दीवारों का पृष्ठीय क्षेत्रफल = 2h(l + b)<br>= 2 &times; 5(15 + 9)<br>= 10(24) =&nbsp; 240 m<sup>2</sup><br>इसलिए , डिब्बों की संख्या <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>240</mn><mn>40</mn></mfrac></math>= 6 &nbsp;&nbsp;</p>\n<p>&nbsp;</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "17",
                    question_en: "<p>6. The distance between the longest chord and the centre of a circle is:</p>",
                    question_hi: "<p>6. सबसे लंबी जीवा और वृत्त के केंद्र के बीच की दूरी _____ होती है।</p>",
                    options_en: ["<p>Equal to zero unit</p>", "<p>Equal to the diameter</p>", 
                                "<p>Equal to half of the radius</p>", "<p>Equal to the radius</p>"],
                    options_hi: ["<p>शून्य इकाई के बराबर</p>", "<p>व्यास के बराबर</p>",
                                "<p>त्रिज्या के आधे के बराबर</p>", "<p>त्रिज्या के बराबर</p>"],
                    solution_en: "<p>6.(a) We know the longest chord of the circle is also called the diameter of the circle.<br>So, the distance between the longest chord and centre of the circle = 0 unit</p>",
                    solution_hi: "<p>6.(a) हम जानते हैं कि वृत्त की सबसे लंबी जीवा को वृत्त का व्यास भी कहा जाता है।<br>तो, सबसे लंबी जीवा और वृत्त के केंद्र के बीच की दूरी = 0 इकाई होगी</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "17",
                    question_en: "<p>7. The marked price of 55 items was equal to the cost price of 99 items. The selling price of 56 items was equal to the marked price of 35 items. Calculate the profit or loss percentage from the sale of each item.</p>",
                    question_hi: "<p>7. 55 वस्तुओं का अंकित मूल्य 99 वस्तुओं के क्रय मूल्य के बराबर था। 56 वस्तुओं का विक्रय मूल्य 35 वस्तुओं के अंकित मूल्य के बराबर था। प्रत्येक वस्तु की बिक्री से होने वाले लाभ या हानि प्रतिशत की गणना करें।</p>",
                    options_en: ["<p>15% profit</p>", "<p>12.25% profit</p>", 
                                "<p>12.5% profit</p>", "<p>12.5% loss</p>"],
                    options_hi: ["<p>15% लाभ</p>", "<p>12.25% लाभ</p>",
                                "<p>12.5% लाभ</p>", "<p>12.5% हानि</p>"],
                    solution_en: "<p>7.(c) MP of 55 items = CP of 99 items<br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>M</mi><mi>P</mi></mrow><mrow><mi>C</mi><mi>P</mi></mrow></mfrac><mo>=</mo><mfrac><mn>99</mn><mn>55</mn></mfrac><mo>=</mo><mfrac><mn>9</mn><mn>5</mn></mfrac></math><br>SP of 56 items = MP of 35 items<br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>S</mi><mi>P</mi></mrow><mrow><mi>M</mi><mi>P</mi></mrow></mfrac><mo>=</mo><mfrac><mn>35</mn><mn>56</mn></mfrac><mo>=</mo><mfrac><mn>5</mn><mn>8</mn></mfrac></math><br>Ratio <math display=\"inline\"><mo>&#8594;</mo></math> CP&nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; MP&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp; SP<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;5&nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; &nbsp;9&nbsp; &nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp; &nbsp;9<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;8&nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; &nbsp;8&nbsp; &nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp; &nbsp;5 <br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; ______________________<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;40&nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; &nbsp;72&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp; &nbsp;45<br>Required profit % = <math display=\"inline\"><mfrac><mrow><mn>45</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>40</mn></mrow><mrow><mn>40</mn></mrow></mfrac></math> &times; 100 = 12.5%</p>",
                    solution_hi: "<p>7.(c) 55 वस्तुओं का अंकित मूल्य = 99 वस्तुओं का CP<br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>M</mi><mi>P</mi></mrow><mrow><mi>C</mi><mi>P</mi></mrow></mfrac><mo>=</mo><mfrac><mn>99</mn><mn>55</mn></mfrac><mo>=</mo><mfrac><mn>9</mn><mn>5</mn></mfrac></math><br>56 वस्तुओं का विक्रय मूल्य = 35 वस्तुओं का अंकित मूल्य <br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>S</mi><mi>P</mi></mrow><mrow><mi>M</mi><mi>P</mi></mrow></mfrac><mo>=</mo><mfrac><mn>35</mn><mn>56</mn></mfrac><mo>=</mo><mfrac><mn>5</mn><mn>8</mn></mfrac></math><br>अनुपात <math display=\"inline\"><mo>&#8594;</mo></math> क्रय मूल्य&nbsp; &nbsp; : अंकित मूल्य : विक्रय मूल्य <br><br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;5&nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; &nbsp;9&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp; &nbsp;9<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;8&nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; &nbsp;8&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp; &nbsp;5 <br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;______________________<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 40&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp; 72&nbsp; &nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; &nbsp;45<br>आवश्यक लाभ % = <math display=\"inline\"><mfrac><mrow><mn>45</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>40</mn></mrow><mrow><mn>40</mn></mrow></mfrac></math> &times; 100 = 12.5%</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "17",
                    question_en: "<p>8. In an election between two candidates, P received 42% of the valid votes and Q won by 45,360 votes. 20% people did not cast their vote. If 10% of the votes cast were found invalid, what is the total number of votes registered in the poll booth?</p>",
                    question_hi: "<p>8. दो उम्मीदवारों के बीच हुए एक चुनाव में, P को कुल पड़े वैध मतों के 42% मत मिले और Q ने 45,360 मतों से जीत हासिल की। 20% लोगों ने मत नहीं डाला। यदि डाले गए 10% मत अवैध पाए गए, तो मतदान केंद्र में पंजीकृत मतों की कुल संख्या ज्ञात करें।</p>",
                    options_en: ["<p>3,93,750</p>", "<p>3,59,000</p>", 
                                "<p>3,93,600</p>", "<p>3,53,790</p>"],
                    options_hi: ["<p>3,93,750</p>", "<p>3,59,000</p>",
                                "<p>3,93,600</p>", "<p>3,53,790</p>"],
                    solution_en: "<p>8.(a) Let the total votes be 100%<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731059464534.png\" alt=\"rId6\" width=\"181\" height=\"212\"><br><math display=\"inline\"><mo>&#8658;</mo></math> (41.76% - 30.24%) = 11.52% = 45360<br><math display=\"inline\"><mo>&#8658;</mo></math> 100% = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>45360</mn><mrow><mn>11</mn><mo>.</mo><mn>52</mn></mrow></mfrac></math> &times; 100 = 393750</p>",
                    solution_hi: "<p>8.(a) माना कुल वोट = 100% <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731059464708.png\" alt=\"rId7\" width=\"162\" height=\"201\"><br><math display=\"inline\"><mo>&#8658;</mo></math> (41.76% - 30.24%) = 11.52% = 45360<br><math display=\"inline\"><mo>&#8658;</mo></math> 100% = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>45360</mn><mrow><mn>11</mn><mo>.</mo><mn>52</mn></mrow></mfrac></math> &times; 100 = 393750</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "17",
                    question_en: "<p>9. Hema is 20 years old and her sister Neha is 30 years old. How many years ago were their ages in the ratio of 3 : 5 ?</p>",
                    question_hi: "<p>9. हेमा 20 वर्ष की है और उनकी बहन नेहा 30 वर्ष की है। कितने वर्ष पहले उनकी आयु 3 : 5 के अनुपात में थी?</p>",
                    options_en: ["<p>3</p>", "<p>2</p>", 
                                "<p>7</p>", "<p>5</p>"],
                    options_hi: ["<p>3</p>", "<p>2</p>",
                                "<p>7</p>", "<p>5</p>"],
                    solution_en: "<p>9.(d) According to the question,<br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>20</mn><mo>-</mo><mi>X</mi></mrow><mrow><mn>30</mn><mo>-</mo><mi>X</mi></mrow></mfrac><mo>=</mo><mfrac><mn>3</mn><mn>5</mn></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math> 100 - 5x = 90 - 3x<br><math display=\"inline\"><mo>&#8658;</mo></math> 10 = 2x<br><math display=\"inline\"><mo>&#8658;</mo></math> x = 5 years</p>",
                    solution_hi: "<p>9.(d) प्रश्न के अनुसार,<br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>20</mn><mo>-</mo><mi>X</mi></mrow><mrow><mn>30</mn><mo>-</mo><mi>X</mi></mrow></mfrac><mo>=</mo><mfrac><mn>3</mn><mn>5</mn></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math> 100 - 5x = 90 - 3x<br><math display=\"inline\"><mo>&#8658;</mo></math> 10 = 2x<br><math display=\"inline\"><mo>&#8658;</mo></math> x = 5 वर्ष</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "17",
                    question_en: "<p>10. AB is the chord of the circle with centre O. A line segment DOC originating from a point D on the circumference of the circle in major segment meets AB produced at C such that BC = OD. If angle BCO = 30&deg;, then angle AOD is:</p>",
                    question_hi: "<p>10. AB केंद्र O वाले वृत्त की जीवा है। प्रमुख खंड में वृत्त की परिधि पर बिंदु D से निकलने वाला एक रेखा खंड DOC, C पर निर्मित AB से इस प्रकार मिलता है कि BC = OD है। यदि कोण BCO = 30&deg; है, तो कोण AOD ज्ञात करें।</p>",
                    options_en: ["<p>30&deg;</p>", "<p>90&deg;</p>", 
                                "<p>60&deg;</p>", "<p>80&deg;</p>"],
                    options_hi: ["<p>30&deg;</p>", "<p>90&deg;</p>",
                                "<p>60&deg;</p>", "<p>80&deg;</p>"],
                    solution_en: "<p>10.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731059464861.png\" alt=\"rId8\" width=\"238\" height=\"131\"><br>Given: BC = OD<br>then, OD = OB = OA = radii of the circle <br>In <math display=\"inline\"><mi>&#916;</mi></math>OBC,<br>&ang;OCB = &ang;COB = 30&deg;<br>&ang;OBC = 180&deg; - (30&deg; + 30&deg;) = 120&deg;<br>In <math display=\"inline\"><mi>&#916;</mi></math>OAB,<br>&ang;OBA = 180&deg; - 120&deg; = 60&deg; = &ang;OAB<br>&ang;AOB = 180&deg; - (60&deg; + 60&deg;) = 60&deg;<br>Now,<br>&ang;AOD = 180&deg; - 60&deg; - 30&deg; = 90&deg;</p>",
                    solution_hi: "<p>10.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731059464861.png\" alt=\"rId8\" width=\"238\" height=\"131\"><br>दिया गया है : BC = OD<br>तब, OD = OB = OA = वृत्त की त्रिज्या <br><math display=\"inline\"><mi>&#916;</mi></math>OBC में,<br>&ang;OCB = &ang;COB = 30&deg;<br>&ang;OBC = 180&deg; - (30&deg; + 30&deg;) = 120&deg;<br><math display=\"inline\"><mi>&#916;</mi></math>OABमें,<br>&ang;OBA = 180&deg; - 120&deg; = 60&deg; = &ang;OAB<br>&ang;AOB = 180&deg; - (60&deg; + 60&deg;) = 60&deg;<br>अब,<br>&ang;AOD = 180&deg; - 60&deg; - 30&deg; = 90&deg;</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "17",
                    question_en: "<p>11. The following pie chart shows the percentage distribution of the expenditure incurred in publishing a book. Study the pie chart and answer the question that follows.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731059464998.png\" alt=\"rId9\" width=\"258\" height=\"254\"> <br>If the expenditure on royalty in publishing certain number of books be <math display=\"inline\"><mi>&#8377;</mi></math>43,500, then what is the combined expenditure (in ₹) on transportation, binding and printing?</p>",
                    question_hi: "<p>11. निम्नलिखित पाई चार्ट किसी पुस्तक के प्रकाशन में किए गए व्यय का प्रतिशत वितरण दर्शाता है। पाई चार्ट का अध्ययन कीजिए और निम्नलिखित प्रश्न का उत्तर दीजिए।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731059465126.png\" alt=\"rId10\" width=\"262\" height=\"258\"> <br>Pie chart shows the percentage distribution of the expenditure<br>incutted in publishing a book: पाई चार्ट पुस्तक के प्रकाशन में हुए व्यय का प्रतिशत वितरण दर्शाता है<br>Printing cost - मुद्रण लागत Promotion cost- - प्रचार लागत<br>Binding - बाइंडिंग Paper cost - कागज की लागत<br>Transportation - परिवहन Royalty - रॉयल्टी<br>यदि निश्चित संख्या में पुस्तकों के प्रकाशन में रॉयल्टी पर व्यय रु. 43,500 है, तो परिवहन, बाइंडिंग और प्रिंटिंग पर संयुक्त व्यय (रुपये में) कितना है?</p>",
                    options_en: ["<p>1,97,900</p>", "<p>1,79,400</p>", 
                                "<p>1,79,900</p>", "<p>1,47,900</p>"],
                    options_hi: ["<p>1,97,900</p>", "<p>1,79,400</p>",
                                "<p>1,79,900</p>", "<p>1,47,900</p>"],
                    solution_en: "<p>11.(d) According to the question,<br>15 % = 43500<br>(10 + 16 + 25) % = 51% = <math display=\"inline\"><mfrac><mrow><mn>43500</mn></mrow><mrow><mn>15</mn></mrow></mfrac></math> &times; 51 = ₹147900</p>",
                    solution_hi: "<p>11.(d) प्रश्न के अनुसार,<br>15 % = 43500<br>(10 + 16 + 25) % = 51% = <math display=\"inline\"><mfrac><mrow><mn>43500</mn></mrow><mrow><mn>15</mn></mrow></mfrac></math> &times; 51 = ₹147900</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "17",
                    question_en: "<p>12. The given pie-chart shows the percentage distribution of the expenditure incurred in publishing a book.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731059465259.png\" alt=\"rId11\" width=\"306\" height=\"305\"> <br>What is the central angle of the sector corresponding to the expenditure incurred on binding?</p>",
                    question_hi: "<p>12. दिया गया पाई-चार्ट किसी पुस्तक के प्रकाशन पर हुए व्यय का प्रतिशत वितरण दर्शाता है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731059465401.png\" alt=\"rId12\" width=\"266\" height=\"265\"> <br>percentage distribution of expenditure- व्यय का प्रतिशत वितरण<br>Binding 23% - बाइंडिंग 23%<br>Printing cost 20% - मुद्रण लागत 20%<br>Paper cost 25% - कागज लागत 25%<br>Royalty 15% - रॉयल्टी 15%<br>Transport cost 17% - परिवहन लागत 17%<br>बाइंडिंग पर किए गए व्यय के संगत वृतखंड का केंद्रीय कोण क्या है?</p>",
                    options_en: ["<p>78.8&deg;</p>", "<p>61.8&deg;</p>", 
                                "<p>82.8&deg;</p>", "<p>48.8&deg;</p>"],
                    options_hi: ["<p>78.8&deg;</p>", "<p>61.8&deg;</p>",
                                "<p>82.8&deg;</p>", "<p>48.8&deg;</p>"],
                    solution_en: "<p>12.(c) According to the question,<br><math display=\"inline\"><mo>&#8658;</mo></math> 100 % = 360&deg; <br><math display=\"inline\"><mo>&#8658;</mo></math> 23% = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>360</mn><mo>&#176;</mo></mrow><mn>100</mn></mfrac></math>&times; 23 = 82.8&deg;</p>",
                    solution_hi: "<p>12.(c) प्रश्न के अनुसार,<br><math display=\"inline\"><mo>&#8658;</mo></math> 100 % = 360&deg; <br><math display=\"inline\"><mo>&#8658;</mo></math> 23% = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>360</mn><mo>&#176;</mo></mrow><mn>100</mn></mfrac></math>&times; 23 = 82.8&deg;</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "17",
                    question_en: "<p>13. Simplify:<br>25 - (18 - 4 <math display=\"inline\"><mo>&#215;</mo></math> 9 &divide; 3)</p>",
                    question_hi: "<p>13. निम्नलिखित को सरल कीजिए। <br>25 - (18 - 4 <math display=\"inline\"><mo>&#215;</mo></math> 9 &divide; 3)</p>",
                    options_en: ["<p>-31</p>", "<p>33</p>", 
                                "<p>-17</p>", "<p>19</p>"],
                    options_hi: ["<p>-31</p>", "<p>33</p>",
                                "<p>-17</p>", "<p>19</p>"],
                    solution_en: "<p>13.(d) 25 - (18 - 4 &times; 9 <math display=\"inline\"><mo>&#247;</mo></math> 3)<br>= 25 - (18 - 12)<br>= 25 - 6 = 19</p>",
                    solution_hi: "<p>13.(d) 25 - (18 - 4 &times; 9 <math display=\"inline\"><mo>&#247;</mo></math> 3)<br>= 25 - (18 - 12)<br>= 25 - 6 = 19</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "17",
                    question_en: "<p>14. XAB and XCD are two secants to a circle. If XA = 18 cm, AB = 22 cm and XC = 24 cm, then find the value of XD (in cm).</p>",
                    question_hi: "<p>14. XAB और XCD, एक वृत्त की दो छेदक रेखाएं हैं। यदि XA = 18 cm है, AB = 22 cm है और XC = 24 cm है, तो XD का मान (cm में) ज्ञात कीजिए।</p>",
                    options_en: ["<p>28</p>", "<p>21</p>", 
                                "<p>30</p>", "<p>34</p>"],
                    options_hi: ["<p>28</p>", "<p>21</p>",
                                "<p>30</p>", "<p>34</p>"],
                    solution_en: "<p>14.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731059465613.png\" alt=\"rId13\" width=\"253\" height=\"117\"><br>We know that,<br><math display=\"inline\"><mo>&#8658;</mo></math> XC &times; XD = XA &times; XB<br><math display=\"inline\"><mo>&#8658;</mo></math> 24 &times; XD = 18 &times; 40<br><math display=\"inline\"><mo>&#8658;</mo></math> XD = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>18</mn><mo>&#215;</mo><mn>40</mn></mrow><mn>24</mn></mfrac></math>= 30 cm</p>",
                    solution_hi: "<p>14.(c) <br><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731059465613.png\" alt=\"rId13\" width=\"253\" height=\"117\"><br>हम जानते हैं की ,<br><math display=\"inline\"><mo>&#8658;</mo></math> XC &times; XD = XA &times; XB<br><math display=\"inline\"><mo>&#8658;</mo></math> 24 &times; XD = 18 &times; 40<br><math display=\"inline\"><mo>&#8658;</mo></math> XD = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>18</mn><mo>&#215;</mo><mn>40</mn></mrow><mn>24</mn></mfrac></math>= 30 cm</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "17",
                    question_en: "<p>15. Study the below table and answer the question that follows.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731059465722.png\" alt=\"rId14\" width=\"214\" height=\"193\"> <br>If 300 calories are burned by jogging 6 km, then how many calories were burnt in the given week?</p>",
                    question_hi: "<p>15. निम्न तालिका का अध्ययन कीजिए और प्रश्न का उत्तर दीजिए।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731059465830.png\" alt=\"rId15\" width=\"222\" height=\"206\"> <br>यदि 6 km जॉगिंग करने से 300 कैलोरी बर्न होती है, तो दिए गए सप्ताह में कितनी कैलोरी बर्न हुई?</p>",
                    options_en: ["<p>1600</p>", "<p>2200</p>", 
                                "<p>1800</p>", "<p>2000</p>"],
                    options_hi: ["<p>1600</p>", "<p>2200</p>",
                                "<p>1800</p>", "<p>2000</p>"],
                    solution_en: "<p>15.(d) Total distance of the week = 6 + 5.5 + 5.5 + 7 + 5 + 3.5 + 7.5 = 40<br>According to the question,<br>6 km burn calories = 300 <br>40 km burn calories = <math display=\"inline\"><mfrac><mrow><mn>300</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math> &times; 40 = 2000</p>",
                    solution_hi: "<p>15.(d) पुरे सप्ताह में तय की गयी कुल दूरी = 6 + 5.5 + 5.5 + 7 + 5 + 3.5 + 7.5 = 40<br>प्रश्न के अनुसार,<br>6 किमी कैलोरी बर्न = 300 <br>40 किमी कैलोरी बर्न = <math display=\"inline\"><mfrac><mrow><mn>300</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math> &times; 40 = 2000</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "17",
                    question_en: "<p>16. If cos A + cos<sup>2</sup>A = 1 then sin<sup>2</sup>A + sin<sup>4</sup>A is equal to:</p>",
                    question_hi: "<p>16. यदि cos A + cos<sup>2</sup>A = 1 then sin<sup>2</sup>A + sin<sup>4</sup>A किसके बराबर है?</p>",
                    options_en: ["<p><math display=\"inline\"><mfrac><mrow><msup><mrow><mi>c</mi><mi>o</mi><mi>s</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>A</mi></mrow><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mi>A</mi></mrow></mfrac></math></p>", "<p>0</p>", 
                                "<p><math display=\"inline\"><mfrac><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mi>A</mi></mrow><mrow><msup><mrow><mi>c</mi><mi>o</mi><mi>s</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>A</mi></mrow></mfrac></math></p>", "<p>1</p>"],
                    options_hi: ["<p><math display=\"inline\"><mfrac><mrow><msup><mrow><mi>c</mi><mi>o</mi><mi>s</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>A</mi></mrow><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mi>A</mi></mrow></mfrac></math></p>", "<p>0</p>",
                                "<p><math display=\"inline\"><mfrac><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mi>A</mi></mrow><mrow><msup><mrow><mi>c</mi><mi>o</mi><mi>s</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>A</mi></mrow></mfrac></math></p>", "<p>1</p>"],
                    solution_en: "<p>16.(d) cos A + cos<sup>2</sup>A = 1<br><math display=\"inline\"><mo>&#8658;</mo></math> cos A = 1 - cos<sup>2</sup>A<br><math display=\"inline\"><mo>&#8658;</mo></math> cos A = sin<sup>2</sup>A<br>Then,<br>sin<sup>2</sup>A + sin<sup>4</sup>A = sin<sup>2</sup>A + cos<sup>2</sup>A = 1</p>",
                    solution_hi: "<p>16.(d) cos A + cos<sup>2</sup>A = 1<br><math display=\"inline\"><mo>&#8658;</mo></math> cos A = 1 - cos<sup>2</sup>A<br><math display=\"inline\"><mo>&#8658;</mo></math> cos A = sin<sup>2</sup>A<br>अब ,<br>sin<sup>2</sup>A + sin<sup>4</sup>A = sin<sup>2</sup>A + cos<sup>2</sup>A = 1</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "17",
                    question_en: "<p>17. If the medians of a triangle are equal, then the triangle will be:</p>",
                    question_hi: "<p>17. यदि किसी त्रिभुज की माध्यिकाएं बराबर हों, हों तो त्रिभुज _____ होगा।</p>",
                    options_en: ["<p>Equilateral triangle</p>", "<p>Obtuse-angle triangle</p>", 
                                "<p>Scalene triangle</p>", "<p>Right-angled triangle</p>"],
                    options_hi: ["<p>समबाहु त्रिभुज</p>", "<p>अधिक कोण त्रिभुज</p>",
                                "<p>विषमबाहु त्रिभुज</p>", "<p>समकोण त्रिभुज</p>"],
                    solution_en: "<p>17.(a) <br>If the medians of a triangle are equal, the triangle will be <strong>equilateral</strong>.<br>This is because in any triangle, the medians are proportional to the sides. If all three medians are of equal length, it implies that all three sides of the triangle must also be equal. Hence, the triangle must be equilateral.</p>",
                    solution_hi: "<p>17.(a) <br>यदि किसी त्रिभुज की माध्यिकाएँ बराबर हों तो त्रिभुज <strong>समबाहु </strong>होगा।<br>ऐसा इसलिए है क्योंकि किसी भी त्रिभुज में माध्यिकाएँ भुजाओं के समानुपाती होती हैं। यदि तीनों माध्यिकाएँ समान लंबाई की हैं, तो त्रिभुज की तीनों भुजाएँ भी समान होनी चाहिए। अत: त्रिभुज समबाहु त्रिभुज होना चाहिए।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "17",
                    question_en: "<p>18. Find the fourth proportional to 4, 9 and 16.</p>",
                    question_hi: "<p>18. 4, 9 और 16 का चतुर्थानुपाती ज्ञात करें।</p>",
                    options_en: ["<p>18</p>", "<p>27</p>", 
                                "<p>36</p>", "<p>16</p>"],
                    options_hi: ["<p>18</p>", "<p>27</p>",
                                "<p>36</p>", "<p>16</p>"],
                    solution_en: "<p>18.(c) Let the fourth proportional be <math display=\"inline\"><mi>x</mi></math><br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>9</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>16</mn><mi>X</mi></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math> x = 36</p>",
                    solution_hi: "<p>18.(c) माना चतुर्थानुपाती <math display=\"inline\"><mi>x</mi></math> है<br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>9</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>16</mn><mi>X</mi></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math> x = 36</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "17",
                    question_en: "<p>19. The value of <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mi>e</mi><mi>c</mi><mi>&#952;</mi><mo>-</mo><mi>c</mi><mi>o</mi><mi>t</mi><mi>&#952;</mi></mrow></mfrac></math> - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><mi>sin</mi><mi>&#952;</mi></mrow></mfrac></math> is equal to:</p>",
                    question_hi: "<p>19. f <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mi>e</mi><mi>c</mi><mi>&#952;</mi><mo>-</mo><mi>c</mi><mi>o</mi><mi>t</mi><mi>&#952;</mi></mrow></mfrac></math> - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><mi>sin</mi><mi>&#952;</mi></mrow></mfrac></math>&nbsp;का मान किसके बराबर है?</p>",
                    options_en: ["<p>tan<math display=\"inline\"><mi>&#952;</mi></math></p>", "<p>cosec<math display=\"inline\"><mi>&#952;</mi></math></p>", 
                                "<p>cot<math display=\"inline\"><mi>&#952;</mi></math></p>", "<p>sec<math display=\"inline\"><mi>&#952;</mi></math></p>"],
                    options_hi: ["<p>tan<math display=\"inline\"><mi>&#160;</mi><mi>&#952;</mi></math></p>", "<p>cosec <math display=\"inline\"><mi>&#952;</mi></math></p>",
                                "<p>cot <math display=\"inline\"><mi>&#952;</mi></math></p>", "<p>sec <math display=\"inline\"><mi>&#952;</mi></math></p>"],
                    solution_en: "<p>19.(c) <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mi>e</mi><mi>c</mi><mi>&#952;</mi><mo>-</mo><mi>c</mi><mi>o</mi><mi>t</mi><mi>&#952;</mi></mrow></mfrac></math> - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><mi>sin</mi><mi>&#952;</mi></mrow></mfrac></math><br>= <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mfrac><mrow><mn>1</mn></mrow><mrow><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#952;</mi></mrow></mfrac><mo>-</mo><mi>&#160;</mi><mfrac><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#952;</mi></mrow><mrow><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#952;</mi></mrow></mfrac></mrow></mfrac></math> -&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><mi>sin</mi><mi>&#952;</mi></mrow></mfrac></math><br>= <math display=\"inline\"><mfrac><mrow><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#952;</mi></mrow><mrow><mn>1</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#952;</mi></mrow></mfrac></math> -&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><mi>sin</mi><mi>&#952;</mi></mrow></mfrac></math><br>= <math display=\"inline\"><mfrac><mrow><msup><mrow><mi>s</mi><mi>i</mi><mi>n</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>&#952;</mi><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>1</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#952;</mi></mrow><mrow><mo>(</mo><mn>1</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#952;</mi><mo>)</mo><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#952;</mi></mrow></mfrac></math> <br>= <math display=\"inline\"><mfrac><mrow><mo>-</mo><mo>(</mo><msup><mrow><mn>1</mn><mo>-</mo><mi>s</mi><mi>i</mi><mi>n</mi></mrow><mrow><mn>2</mn></mrow></msup><mo>)</mo><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#952;</mi></mrow><mrow><mo>(</mo><mn>1</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#952;</mi><mo>)</mo><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#952;</mi></mrow></mfrac></math><br>= <math display=\"inline\"><mfrac><mrow><msup><mrow><mo>-</mo><mi>c</mi><mi>o</mi><mi>s</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>&#952;</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#952;</mi></mrow><mrow><mo>(</mo><mn>1</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#952;</mi><mo>)</mo><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#952;</mi></mrow></mfrac></math><br>= <math display=\"inline\"><mfrac><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#952;</mi></mrow><mrow><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#952;</mi></mrow></mfrac></math> <strong>= cot&theta;</strong></p>",
                    solution_hi: "<p>19.(c) <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mi>e</mi><mi>c</mi><mi>&#952;</mi><mo>-</mo><mi>c</mi><mi>o</mi><mi>t</mi><mi>&#952;</mi></mrow></mfrac></math> - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><mi>sin</mi><mi>&#952;</mi></mrow></mfrac></math><br>= <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mfrac><mrow><mn>1</mn></mrow><mrow><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#952;</mi></mrow></mfrac><mo>-</mo><mi>&#160;</mi><mfrac><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#952;</mi></mrow><mrow><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#952;</mi></mrow></mfrac></mrow></mfrac></math> -&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><mi>sin</mi><mi>&#952;</mi></mrow></mfrac></math><br>= <math display=\"inline\"><mfrac><mrow><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#952;</mi></mrow><mrow><mn>1</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#952;</mi></mrow></mfrac></math> -&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><mi>sin</mi><mi>&#952;</mi></mrow></mfrac></math><br>= <math display=\"inline\"><mfrac><mrow><msup><mrow><mi>s</mi><mi>i</mi><mi>n</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>&#952;</mi><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>1</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#952;</mi></mrow><mrow><mo>(</mo><mn>1</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#952;</mi><mo>)</mo><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#952;</mi></mrow></mfrac></math> <br>= <math display=\"inline\"><mfrac><mrow><mo>-</mo><mo>(</mo><msup><mrow><mn>1</mn><mo>-</mo><mi>s</mi><mi>i</mi><mi>n</mi></mrow><mrow><mn>2</mn></mrow></msup><mo>)</mo><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#952;</mi></mrow><mrow><mo>(</mo><mn>1</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#952;</mi><mo>)</mo><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#952;</mi></mrow></mfrac></math><br>= <math display=\"inline\"><mfrac><mrow><msup><mrow><mo>-</mo><mi>c</mi><mi>o</mi><mi>s</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>&#952;</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#952;</mi></mrow><mrow><mo>(</mo><mn>1</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#952;</mi><mo>)</mo><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#952;</mi></mrow></mfrac></math><br>= <math display=\"inline\"><mfrac><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#952;</mi></mrow><mrow><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#952;</mi></mrow></mfrac></math> <strong>= cot&theta;</strong></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "17",
                    question_en: "<p>20. Ten chairs and six tables together cost ₹5,140; three chairs and two tables together cost ₹1,635. The cost of 1 chair and 1 table is:</p>",
                    question_hi: "<p>20. दस कुर्सियों और छह मेजों का कुल मूल्य ₹5,140 है; तीन कुर्सियों और दो मेजों का कुल मूल्य ₹1,635 है। 1 कुर्सी और 1 मेज का मूल्य कितना है?</p>",
                    options_en: ["<p>₹900</p>", "<p>₹600</p>", 
                                "<p>₹800</p>", "<p>₹700</p>"],
                    options_hi: ["<p>₹900</p>", "<p>₹600</p>",
                                "<p>₹800</p>", "<p>₹700</p>"],
                    solution_en: "<p>20.(d) Let the price of one chairs and one tables be <math display=\"inline\"><mi>x</mi></math> and y respectively,<br><math display=\"inline\"><mo>&#8658;</mo></math> 10x + 6y = ₹5140 &hellip;&hellip;&hellip;.(i)<br><math display=\"inline\"><mo>&#8658;</mo></math> 3x + 2y = ₹1635 &hellip;&hellip;&hellip;.(ii)<br>On multiply by 3 in equation (ii) then subtract equation (i) from (ii)<br><math display=\"inline\"><mo>&#8658;</mo></math> x = 5140 - 4905 = ₹235<br>Put the value of <math display=\"inline\"><mi>x</mi></math> in equation (ii)<br><math display=\"inline\"><mo>&#8658;</mo></math> 3 &times; 235 + 2y = ₹1635<br><math display=\"inline\"><mo>&#8658;</mo></math> 2y = 1635 - 705<br><math display=\"inline\"><mo>&#8658;</mo></math> y = 9302 = ₹465<br>Now,<br>The value of 1 chair and 1 table = 235 + 465 = ₹700</p>",
                    solution_hi: "<p>20.(d) माना कि एक कुर्सी और एक मेज की कीमत क्रमशः <math display=\"inline\"><mi>x</mi></math>और y है,<br><math display=\"inline\"><mo>&#8658;</mo></math> 10x + 6y = ₹5140 &hellip;&hellip;&hellip;.(i)<br><math display=\"inline\"><mo>&#8658;</mo></math> 3x + 2y = ₹1635 &hellip;&hellip;&hellip;.(ii)<br>समीकरण (ii) में 3 से गुणा कर समीकरण (i) को (ii) से घटाने पर <br><math display=\"inline\"><mo>&#8658;</mo></math> x = 5140 - 4905 = ₹235<br>x का मान समीकरण (ii) में रखें<br><math display=\"inline\"><mo>&#8658;</mo></math> 3 &times; 235 + 2y = ₹1635<br><math display=\"inline\"><mo>&#8658;</mo></math> 2y = 1635 - 705<br><math display=\"inline\"><mo>&#8658;</mo></math> y = 9302 = ₹465<br>अब,<br>1 कुर्सी और 1 टेबल का मूल्य = 235 + 465 = ₹700 </p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "17",
                    question_en: "<p>21. If a + b + c = 0, then the value of (a<sup>2</sup> + b<sup>2</sup> + 2ab) is equal to:</p>",
                    question_hi: "<p>21. यदि a + b + c = 0 है, तो (a<sup>2</sup> + b<sup>2</sup> + 2ab) का मान क्या है ?</p>",
                    options_en: ["<p>c<sup>2</sup></p>", "<p>-c<sup>2</sup></p>", 
                                "<p>c</p>", "<p>-c</p>"],
                    options_hi: ["<p>c<sup>2</sup></p>", "<p>-c<sup>2</sup></p>",
                                "<p>c</p>", "<p>-c</p>"],
                    solution_en: "<p>21.(a) Given: a + b + c = 0<br><math display=\"inline\"><mi>a</mi></math> + b = - c<br>On squaring both side we get,<br><math display=\"inline\"><msup><mrow><mi>a</mi></mrow><mrow><mn>2</mn></mrow></msup></math> + b<sup>2</sup> + 2ab = (- c)<sup>2</sup><br><math display=\"inline\"><msup><mrow><mi>a</mi></mrow><mrow><mn>2</mn></mrow></msup></math> + b<sup>2</sup> + 2ab = c<sup>2</sup></p>",
                    solution_hi: "<p>21.(a) दिया गया है : a + b + c = 0<br><math display=\"inline\"><mi>a</mi></math> + b = -c<br>दोनों पक्षों का वर्ग करने पर हमें प्राप्त होता है,<br><math display=\"inline\"><msup><mrow><mi>a</mi></mrow><mrow><mn>2</mn></mrow></msup></math> + b<sup>2</sup> + 2ab = (- c)<sup>2</sup><br><math display=\"inline\"><msup><mrow><mi>a</mi></mrow><mrow><mn>2</mn></mrow></msup></math> + b<sup>2</sup> + 2ab = c<sup>2</sup></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "17",
                    question_en: "<p>22. The simple interest on a sum for 8 years is three-fifth of the sum. The rate of interest per annum is:</p>",
                    question_hi: "<p>22. किसी राशि पर 8 वर्षों का साधारण ब्याज उस राशि का तीन-पाँचवाँ भाग है। इसके लिए प्रति वर्ष ब्याज दर कितनी होगी?</p>",
                    options_en: ["<p>5<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math>%</p>", "<p>7<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math>%</p>", 
                                "<p>6%</p>", "<p>9%</p>"],
                    options_hi: ["<p>5<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math>%</p>", "<p>7<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math>%</p>",
                                "<p>6%</p>", "<p>9%</p>"],
                    solution_en: "<p>22.(b) According to the question,<br><math display=\"inline\"><mo>&#8658;</mo></math> SI = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>5</mn></mfrac></math>&nbsp;&times; Principal<br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>P</mi><mi>r</mi><mi>i</mi><mi>n</mi><mi>c</mi><mi>i</mi><mi>p</mi><mi>a</mi><mi>l</mi><mo>&#215;</mo><mi>r</mi><mi>a</mi><mi>t</mi><mi>e</mi><mo>&#215;</mo><mn>8</mn></mrow><mn>100</mn></mfrac></math>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>5</mn></mfrac></math> &times; Principal<br><math display=\"inline\"><mo>&#8658;</mo></math> rate =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mrow><mn>5</mn><mo>&#215;</mo><mn>8</mn></mrow></mfrac></math>&times; 100 = 7<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math>%</p>",
                    solution_hi: "<p>22.(b) प्रश्न के अनुसार,<br><math display=\"inline\"><mo>&#8658;</mo></math> साधारण ब्याज = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>5</mn></mfrac></math> &times; मूलधन<br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2350;&#2370;&#2354;&#2343;&#2344;</mi><mo>&#215;</mo><mi>&#2342;&#2352;</mi><mo>&#215;</mo><mn>8</mn></mrow><mn>100</mn></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>5</mn></mfrac></math>&times; मूलधन<br><math display=\"inline\"><mo>&#8658;</mo></math> दर = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mrow><mn>5</mn><mo>&#215;</mo><mn>8</mn></mrow></mfrac></math>&times; 100 = 7<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math>%</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "23",
                    section: "17",
                    question_en: "<p>23. The marked price of a bed sheet is ₹500. At the time of its sale, two successive discounts of 10% and 5% are available. Find the selling price (in ₹) of the bed sheet.</p>",
                    question_hi: "<p>23. एक बेड शीट का अंकित मूल्य ₹ 500 है। इसकी बिक्री के समय, इस पर 10% और 5% की दो क्रमिक छूट उपलब्ध हैं। बेड शीट का विक्रय मूल्य (₹ में) ज्ञात करें।</p>",
                    options_en: ["<p>432.50</p>", "<p>427.50</p>", 
                                "<p>445.00</p>", "<p>430.00</p>"],
                    options_hi: ["<p>432.50</p>", "<p>427.50</p>",
                                "<p>445.00</p>", "<p>430.00</p>"],
                    solution_en: "<p>23.(b) Selling price of the bed sheet = 500 &times; <math display=\"inline\"><mfrac><mrow><mn>9</mn></mrow><mrow><mn>10</mn></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>19</mn><mn>20</mn></mfrac></math> = ₹ 427.50</p>",
                    solution_hi: "<p>23.(b) बेड शीट का विक्रय मूल्य = 500 &times; <math display=\"inline\"><mfrac><mrow><mn>9</mn></mrow><mrow><mn>10</mn></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>19</mn><mn>20</mn></mfrac></math> = ₹ 427.50</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "24",
                    section: "17",
                    question_en: "<p>24. A mango juice is made by mixing water and mango concentrate in the ratio 9 : 7. If x litres of water and 3x litres of mango concentrate is mixed in 160 litres of mango juice, then the new ratio becomes 13 : 14. What is the quantity of the new mango juice (in litres)?</p>",
                    question_hi: "<p>24. पानी और आम के सांद्रण को 9 : 7 के अनुपात में मिलाकर आम का जूस बनाया जाता है। यदि 160 लीटर आम के जूस में x लीटर पानी और 3x लीटर आम के सांद्रण को मिलाया जाता है, तो नया अनुपात 13 : 14 हो जाता है। आम के नए जूस की मात्रा (लीटर में) क्या है?</p>",
                    options_en: ["<p>197</p>", "<p>212</p>", 
                                "<p>206</p>", "<p>216</p>"],
                    options_hi: ["<p>197</p>", "<p>212</p>",
                                "<p>206</p>", "<p>216</p>"],
                    solution_en: "<p>24.(d) Let the quantity of water and mango a in juice be 9a and 7a respectively,<br>According to the question,<br>16a = 160<br>a = 10<br>9a = 90 and 7a = 70<br>Then,<br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>90</mn><mo>+</mo><mi>x</mi></mrow><mrow><mn>70</mn><mo>+</mo><mn>3</mn><mi>x</mi></mrow></mfrac><mo>=</mo><mfrac><mn>13</mn><mn>14</mn></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math> 1260 + 14x = 910 + 39x<br><math display=\"inline\"><mo>&#8658;</mo></math> 350 = 25x<br><math display=\"inline\"><mo>&#8658;</mo></math> x = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>350</mn><mn>25</mn></mfrac></math> = 14<br>Quantity of the new mango juice = (90 + 14) + (70 + 3 &times; 14) = 216 litre</p>",
                    solution_hi: "<p>24.(d) माना जूस में पानी और आम की मात्रा क्रमशः 9a और 7a है,<br>प्रश्न के अनुसार,<br>16a = 160<br>a = 10<br>9a = 90 और 7a = 70<br>तब,<br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>90</mn><mo>+</mo><mi>x</mi></mrow><mrow><mn>70</mn><mo>+</mo><mn>3</mn><mi>x</mi></mrow></mfrac><mo>=</mo><mfrac><mn>13</mn><mn>14</mn></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math> 1260 + 14x = 910 + 39x<br><math display=\"inline\"><mo>&#8658;</mo></math> 350 = 25x<br><math display=\"inline\"><mo>&#8658;</mo></math> x = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>350</mn><mn>25</mn></mfrac></math> = 14<br>नये आम के जूस की मात्रा = (90 + 14) + (70 + 3 &times; 14) = 216 लीटर</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "25",
                    section: "misc",
                    question_en: "<p>25. Three pipes, P, Q and R, together take four hours to fill a tank. All the three pipes were opened at the same time. After three hours, P was closed, and Q and R filled the remaining tank in two hours. How many hours will P alone take to fill the tank?</p>",
                    question_hi: "<p>25. तीन पाइप, P, Q और R मिलकर एक टैंक को भरने में चार घंटे का समय लेते हैं। तीनों पाइपों को एक साथ खोला जाता है। तीन घंटे के बाद, P को बंद कर दिया जाता है, और Q तथा R शेष टैंक को दो घंटों में भर देते हैं। P अकेले टैंक को भरने में कितने घंटे का समय लेगा?</p>",
                    options_en: ["<p>8</p>", "<p>10</p>", 
                                "<p>12</p>", "<p>9</p>"],
                    options_hi: ["<p>8</p>", "<p>10</p>",
                                "<p>12</p>", "<p>9</p>"],
                    solution_en: "<p>25.(a) Part filled in 3 hours = <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math><br>Remaining part = 1 - <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math><br>(Q + R)&rsquo;s 1 hours work = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac><mo>=</mo><mfrac><mn>1</mn><mn>8</mn></mfrac></math><br>P&rsquo;s 1 hours work = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>8</mn></mfrac><mo>=</mo><mfrac><mrow><mn>2</mn><mo>-</mo><mn>1</mn></mrow><mn>8</mn></mfrac><mo>=</mo><mfrac><mn>1</mn><mn>8</mn></mfrac></math><br>So, P alone can fill the tank = 8 hours </p>",
                    solution_hi: "<p>25.(a) 3 घंटे में भरा भाग = <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math><br>शेष भाग = 1 - <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math><br>(Q + R) का 1 घंटे का काम = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac><mo>=</mo><mfrac><mn>1</mn><mn>8</mn></mfrac></math><br>P का 1 घंटे का काम = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>8</mn></mfrac><mo>=</mo><mfrac><mrow><mn>2</mn><mo>-</mo><mn>1</mn></mrow><mn>8</mn></mfrac><mo>=</mo><mfrac><mn>1</mn><mn>8</mn></mfrac></math><br>तो, P अकेले टंकी को भर सकता है = 8 hours</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>