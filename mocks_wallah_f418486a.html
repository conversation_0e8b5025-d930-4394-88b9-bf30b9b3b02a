<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">15:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 15</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">15</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 15 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 0,
                end: 13
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 14,
                end: 14
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "17",
                    question_en: "1. Abhishek marks the price of his laptop 50 percent above the cost price. He allows his customer a discount of 30 percent on the marked price. What is the profit percentage ?",
                    question_hi: "1. अभिषेक अपने लैपटॉप का मूल्य क्रय मूल्य से 50 प्रतिशत अधिक अंकित करता है। वह अपने ग्राहक को अंकित मूल्य पर 30 प्रतिशत की छूट देता है। लाभ प्रतिशत कितना है ?",
                    options_en: [" 5 percent", " 20 percent", 
                                " 8 percent", " 10 percent"],
                    options_hi: ["  5 प्रतिशत", " 20  प्रतिशत",
                                " 8 प्रतिशत", " 10 प्रतिशत"],
                    solution_en: "1.(a)<br />Net profit% = (50 - 30 - <math display=\"inline\"><mfrac><mrow><mn>50</mn><mo>×</mo><mn>30</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math>)% = 20 - 15 = 5%",
                    solution_hi: "1.(a)<br />शुद्ध लाभ%  = (50 - 30 - <math display=\"inline\"><mfrac><mrow><mn>50</mn><mo>×</mo><mn>30</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math>)% = 20 - 15 = 5%",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "2",
                    section: "17",
                    question_en: "<p>2. Selling price of a bag is Rs. 1056 and profit percentage is 20 percent. If selling price is Rs. 800, then what will be the loss percentage.</p>",
                    question_hi: "<p>2. एक बैग का विक्रय मूल्य 1056 रुपए और लाभ प्रतिशत 20 प्रतिशत है। यदि विक्रय मूल्य 800 रुपए है, तो हानि प्रतिशत कितना होगा ?</p>",
                    options_en: ["<p>4.25 percent</p>", "<p>9.09 percent</p>", 
                                "<p>11.11 percent</p>", "<p>8.08 percent</p>"],
                    options_hi: ["<p>4.25 प्रतिशत</p>", "<p>9.09 प्रतिशत</p>",
                                "<p>11.11 प्रतिशत</p>", "<p>8.08 प्रतिशत</p>"],
                    solution_en: "<p>2.(b)<br>20% = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math><br>CP : SP = 5 : 6<br>CP of a bag = <math display=\"inline\"><mfrac><mrow><mn>1056</mn></mrow><mrow><mn>6</mn></mrow></mfrac><mi>&#160;</mi></math>&times; 5 = ₹880<br>Now, SP of the same bag = ₹800<br>CP : SP = 880 : 800 = 11 : 10<br>loss% = <math display=\"inline\"><mfrac><mrow><mn>11</mn><mo>-</mo><mn>10</mn></mrow><mrow><mn>11</mn></mrow></mfrac></math> &times; 100 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>100</mn><mn>11</mn></mfrac><mo>&#160;</mo></math>= 9.09%</p>",
                    solution_hi: "<p>2.(b)<br>20% = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math><br>क्रय मूल्य : विक्रय मूल्य = 5 : 6<br>एक बैग का क्रय मूल्य = <math display=\"inline\"><mfrac><mrow><mn>1056</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math> &times; 5 = ₹880<br>अब, उसी बैग का SP = ₹800<br>क्रय मूल्य : विक्रय मूल्य = 880 : 800 = 11 : 10<br>हानि % = <math display=\"inline\"><mfrac><mrow><mn>11</mn><mo>-</mo><mn>10</mn></mrow><mrow><mn>11</mn></mrow></mfrac></math> &times; 100 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>100</mn><mn>11</mn></mfrac><mo>&#160;</mo></math>= 9.09%</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "3",
                    section: "17",
                    question_en: "<p>3. Mohan buys an old mobile for Rs. 4000 and spends Rs. 500 on its repairs. If he sells the mobile for Rs. 5000, then what is his profit percentage ?</p>",
                    question_hi: "<p>3. मोहन 4000 रुपए में एक पुराना मोबाइल खरीदता है और उसकी मरम्मत पर 500 रुपए खर्च करता है। यदि वह मोबाइल को 5000 रुपए में बेचता है, तो उसका लाभ प्रतिशत कितना है ?</p>",
                    options_en: ["<p>11.11 percent</p>", "<p>13.5 percent</p>", 
                                "<p>12.5 percent</p>", "<p>9.09 percent</p>"],
                    options_hi: ["<p>11.11 प्रतिशत</p>", "<p>13.5 प्रतिशत</p>",
                                "<p>12.5 प्रतिशत</p>", "<p>9.09 प्रतिशत</p>"],
                    solution_en: "<p>3.(a)<br>Total CP of old mobile = 4000 + 500 = ₹4500<br>SP of the old mobile =₹ 5000<br>Required profit% = <math display=\"inline\"><mfrac><mrow><mn>5000</mn><mo>-</mo><mn>4500</mn></mrow><mrow><mn>4500</mn></mrow></mfrac></math> &times; 100 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>100</mn><mn>9</mn></mfrac></math>&nbsp;= 11.11%</p>",
                    solution_hi: "<p>3.(a)<br>पुराने मोबाइल का कुल क्रय मूल्य = 4000 + 500 = ₹4500<br>पुराने मोबाइल का विक्रय मूल्य = ₹5000<br>आवश्यक लाभ% = <math display=\"inline\"><mfrac><mrow><mn>5000</mn><mo>-</mo><mn>4500</mn></mrow><mrow><mn>4500</mn></mrow></mfrac></math> &times; 100 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>100</mn><mn>9</mn></mfrac></math> = 11.11%</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "4",
                    section: "17",
                    question_en: "4. Riya sold a laptop for ₹35000, earning a profit of 25%. If she wanted to earn a profit of 30%, at what price should she have sold the laptop?",
                    question_hi: "4. रिया 25% का लाभ अर्जित करते हुए, ₹35000 में एक लेपटॉप बेचती है। यदि वह 30% का लाभ अर्जित करना चाहती थी, तो उसे लैपटॉप किस मूल्य पर बेचना चाहिए था?",
                    options_en: [" Rs. 37200", " Rs. 34600", 
                                " Rs. 36400", " Rs. 38400"],
                    options_hi: [" ₹ 37200", " ₹ 34600",
                                " ₹ 36400", " ₹38400"],
                    solution_en: "4.(c)<br />Let CP of a laptop be 100%<br />125% -------------  ₹35,000<br />130% ------------- <math display=\"inline\"><mfrac><mrow><mn>35</mn><mo>,</mo><mn>000</mn></mrow><mrow><mn>125</mn></mrow></mfrac></math>×130 = ₹36400",
                    solution_hi: "4.(c)<br />माना एक लैपटॉप का CP 100% है<br />125% -------------  ₹35,000<br />130% ------------- <math display=\"inline\"><mfrac><mrow><mn>35</mn><mo>,</mo><mn>000</mn></mrow><mrow><mn>125</mn></mrow></mfrac></math>×130 = ₹36400",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "5",
                    section: "17",
                    question_en: "5. A seller marks his goods at a price 40% higher than the purchase price. If he gives a discount of 30%, what is the loss percentage ?",
                    question_hi: "5. एक विक्रेता अपने माल को क्रय मूल्य से 40% अधिक मूल्य पर अंकित करता है। यदि वह 30% की छूट देता है, तो हानि प्रतिशत कितना है ?",
                    options_en: [" 2.5% ", " 2% ", 
                                " 3% ", " 1.5%"],
                    options_hi: [" 2.5% ", " 2% ",
                                " 3% ", " 1.5%"],
                    solution_en: "5.(b)<br />Effective rate = (+40 - 30 - <math display=\"inline\"><mfrac><mrow><mn>40</mn><mo>×</mo><mn>30</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math>)% = (10 - 12)% = -2% <br />(-) signs indicate loss so loss will be 2%. ",
                    solution_hi: "5.(b)<br />प्रभावी दर  = (+40 - 30 - <math display=\"inline\"><mfrac><mrow><mn>40</mn><mo>×</mo><mn>30</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math>)% = (10 - 12)% = -2% <br />(-) चिन्ह हानि का संकेत देते हैं इसलिए हानि 2% होगा। ",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "6",
                    section: "17",
                    question_en: "<p>6. The ratio of selling price and cost price is 7 : 11. What is the loss percent ?</p>",
                    question_hi: "<p>6. विक्रय मूल्य और क्रय मूल्य का अनुपात 7 : 11 है। हानि प्रतिशत कितना है ?</p>",
                    options_en: ["<p>33.33 percent</p>", "<p>36.36 percent</p>", 
                                "<p>35.08 percent</p>", "<p>27.58 percent</p>"],
                    options_hi: ["<p>33.33 प्रतिशत</p>", "<p>36.36 प्रतिशत</p>",
                                "<p>35.08 प्रतिशत</p>", "<p>27.58 प्रतिशत</p>"],
                    solution_en: "<p>6.(b)<br>loss% = <math display=\"inline\"><mfrac><mrow><mn>11</mn><mo>-</mo><mn>7</mn></mrow><mrow><mn>11</mn></mrow></mfrac></math> &times; 100 =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>400</mn><mn>11</mn></mfrac></math> = 36.36 percent</p>",
                    solution_hi: "<p>6.(b)<br>हानि% = <math display=\"inline\"><mfrac><mrow><mn>11</mn><mo>-</mo><mn>7</mn></mrow><mrow><mn>11</mn></mrow></mfrac></math> &times; 100 =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>400</mn><mn>11</mn></mfrac></math> = 36.36 प्रतिशत</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "7",
                    section: "17",
                    question_en: "7. Pankaj bought two bikes, the first bike for Rs 24000 and the second bike for Rs 20000. He sold the first bike at a profit of 20 percent and the second bike at a loss of 30 percent. What is the total profit or loss ?",
                    question_hi: "7. पंकज ने दो बाइक खरीदी, पहली बाइक 24000 रुपए में और दूसरी बाइक 20000 रुपए में। उसने पहली बाइक को 20 प्रतिशत के लाभ पर और दूसरी बाइक को 30 प्रतिशत की हानि पर बेचा। कुल लाभ या हानि क्या है?",
                    options_en: [" Profit = Rs. 1400", " Loss = Rs. 1400", 
                                " Loss = Rs. 1200", " Profit = Rs. 1200"],
                    options_hi: [" लाभ = 1400 रुपए  ", " हानि = 1400 रुपए  ",
                                " हानि = 1200 रुपए ", " लाभ = 1200 रुपए"],
                    solution_en: "7.(c)<br />Profit on  first bike = 24,000 × 20% = ₹4800<br />loss on second bike = 20,000 × 30% = ₹6000<br />Total loss = 6000 - 4800 = ₹1200",
                    solution_hi: "7.(c)<br />पहली बाइक पर लाभ  = 24,000 × 20% = ₹4800<br />दूसरी बाइक पर नुकसान = 20,000 × 30% = ₹6000<br />कुल हानि = 6000 - 4800 = ₹1200",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "8",
                    section: "17",
                    question_en: "<p>8. The ratio of the cost price and selling price of a bulb is 20 : 25. What is the profit percentage ?</p>",
                    question_hi: "<p>8. एक बल्ब के क्रय मूल्य और विक्रय मूल्य का अनुपात 20 : 25 है। लाभ प्रतिशत कितना है ?</p>",
                    options_en: ["<p>20 percent</p>", "<p>25 percent</p>", 
                                "<p>15 percent</p>", "<p>30 percent</p>"],
                    options_hi: ["<p>20 प्रतिशत</p>", "<p>25 प्रतिशत</p>",
                                "<p>15 प्रतिशत</p>", "<p>30 प्रतिशत</p>"],
                    solution_en: "<p>8.(b)<br>CP : SP = 20 : 25 = 4 : 5<br>Profit % = <math display=\"inline\"><mfrac><mrow><mn>5</mn><mo>-</mo><mn>4</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> &times; 100 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>100</mn><mn>4</mn></mfrac></math> = 25%</p>",
                    solution_hi: "<p>8.(b)<br>क्रय मूल्य : विक्रय मूल्य = 20 : 25 = 4 : 5<br>लाभ % = <math display=\"inline\"><mfrac><mrow><mn>5</mn><mo>-</mo><mn>4</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> &times; 100 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>100</mn><mn>4</mn></mfrac></math> = 25%</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "9",
                    section: "17",
                    question_en: "9. The marked price of an article is Rs. 1850. If a discount of 60 percent is given, then what will be the selling price of the article ?",
                    question_hi: "9. एक वस्तु का अंकित मूल्य 1850 रुपए है। यदि वस्तु पर 60 प्रतिशत की छूट दी जाती है, तो वस्तु का विक्रय मूल्य कितना होगा ?",
                    options_en: [" Rs. 750", " Rs. 840", 
                                " Rs. 860", " Rs. 740"],
                    options_hi: [" 750 रुपए", " 840 रुपए",
                                " 860 रुपए", " 740 रुपए"],
                    solution_en: "9.(d)<br />SP = 1850 × 40% = ₹740",
                    solution_hi: "9.(d)<br />विक्रय मूल्य = 1850 × 40% = ₹740",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "10",
                    section: "17",
                    question_en: "10. Anil sells a table at a loss of 20 percent. If he sells it for Rs. 160 more, then he earns a profit of 20 percent. What should be the selling price of the table to earn a profit of 25 percent ?",
                    question_hi: "10. अनिल एक मेज को 20 प्रतिशत की हानि पर बेचता है। यदि वह इसे 160 रुपए अधिक में बेचता है, तो वह 20 प्रतिशत का लाभ अर्जित करता है। 25 प्रतिशत का लाभ अर्जित करने के लिए मेज का विक्रय मूल्य कितना होना चाहिए ?",
                    options_en: [" Rs. 750 ", " Rs. 800", 
                                " Rs. 500", " Rs. 700"],
                    options_hi: [" 750 रुपए", " 800 रुपए",
                                " 500 रुपए", " 700 रुपए"],
                    solution_en: "10.(c)<br />Let CP be 100%<br />20-(-20) = 40%  --------------- ₹160<br />100% ------------- <math display=\"inline\"><mfrac><mrow><mn>160</mn></mrow><mrow><mn>40</mn></mrow></mfrac></math>×100 = ₹400<br />SP of the table = 400×125% = ₹500",
                    solution_hi: "10.(c)<br />माना क्रय मूल्य 100% है।<br />20-(-20) = 40%  --------------- ₹160<br />100% ------------- <math display=\"inline\"><mfrac><mrow><mn>160</mn></mrow><mrow><mn>40</mn></mrow></mfrac></math>×100 = ₹400<br />टेबल का विक्रय मुल्य = 400×125% = ₹500",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "11",
                    section: "17",
                    question_en: "<p>11. A shopkeeper sold a flower pot for Rs. 3500. If the cost price of the flower pot is Rs.2800, find the profit percent.</p>",
                    question_hi: "<p>11. एक दुकानदार एक फूलदान को 3500 रुपए में बेचता है। यदि फूलदान का क्रय मूल्य 2800 रुपए है, तो लाभ प्रतिशत ज्ञात कीजिए।</p>",
                    options_en: ["<p>25%</p>", "<p>15%</p>", 
                                "<p>30%</p>", "<p>20%</p>"],
                    options_hi: ["<p>25%</p>", "<p>15%</p>",
                                "<p>30%</p>", "<p>20%</p>"],
                    solution_en: "<p>11.(a)<br>CP : SP = 2800 : 3500 = 4 : 5<br>Profit% = <math display=\"inline\"><mfrac><mrow><mn>5</mn><mo>-</mo><mn>4</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> &times; 100 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>100</mn><mn>4</mn></mfrac></math>= 25%</p>",
                    solution_hi: "<p>11.(a)<br>क्रय मूल्य : विक्रय मूल्य = 2800 : 3500 = 4 : 5<br>लाभ% = <math display=\"inline\"><mfrac><mrow><mn>5</mn><mo>-</mo><mn>4</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> &times; 100 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>100</mn><mn>4</mn></mfrac></math>= 25%</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "12",
                    section: "17",
                    question_en: "12. The marked price is 28% higher than the cost price. A discount of 15% is given on the marked price. What is the gain or loss percentage ?",
                    question_hi: "12. अंकित मूल्य, क्रय मूल्य से 28% अधिक है। अंकित मूल्य पर 15% की छूट दी जाती है। लाभ या हानि प्रतिशत कितना है ?",
                    options_en: [" 4.4% loss", " 8.8% gain", 
                                " 5.2% loss", " 6.5% gain"],
                    options_hi: [" 4.4% हानि ", " 8.8% लाभ",
                                " 5.2% हानि", " 6.5% लाभ"],
                    solution_en: "12.(b) <br />Net profit/loss% = (+28 - 15 - <math display=\"inline\"><mfrac><mrow><mn>28</mn><mo>×</mo><mn>15</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math>)% = 13 - 4.2 = +8.8% or 8.8% profit",
                    solution_hi: "12.(b)<br />शुद्ध लाभ/हानि% = (+28 - 15 - <math display=\"inline\"><mfrac><mrow><mn>28</mn><mo>×</mo><mn>15</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math>)% = 13 - 4.2 = + 8.8% या 8.8% लाभ",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "13",
                    section: "17",
                    question_en: "13.  An article is sold for Rs. 4400 at a loss of 20 percent. What is the cost price of the article ?",
                    question_hi: "13.  एक वस्तु 4400 रुपए में, 20 प्रतिशत की हानि पर बेची जाती है। वस्तु का क्रय मूल्य कितना है ?",
                    options_en: ["  Rs. 5500", "  Rs. 5000", 
                                "  Rs. 5200", "  Rs. 6200"],
                    options_hi: [" 5500 रुपए", " 5000 रुपए",
                                " 5200 रुपए", " 6200 रुपए"],
                    solution_en: "13.(a)<br />According to question,<br />CP × 80% = 4400<br />CP = 4400 × <math display=\"inline\"><mfrac><mrow><mn>100</mn></mrow><mrow><mn>80</mn></mrow></mfrac></math> = ₹5500",
                    solution_hi: "13.(a)<br />प्रश्न के अनुसार, <br />क्रय मूल्य × 80% = 4400<br />क्रय मूल्य = 4400×<math display=\"inline\"><mfrac><mrow><mn>100</mn></mrow><mrow><mn>80</mn></mrow></mfrac></math> = ₹5500",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "14",
                    section: "17",
                    question_en: "<p>14. A person bought an article and sold it at a loss of 10 percent. If he had bought it for 30 percent less price and sold it for Rs. 20 more, then he would earn a profit of 40 percent. What is the cost price of the article ?</p>",
                    question_hi: "<p>14. एक व्यक्ति एक वस्तु खरीदता है और उसे 10 प्रतिशत की हानि पर बेच देता है। यदि वह उस वस्तु को 30 प्रतिशत कम मूल्य पर खरीदता है और उसे 20 रुपए अधिक मूल्य पर बेचता है, तो वह 40 प्रतिशत लाभ अर्जित करता है। वस्तु का क्रय मूल्य कितना है ?</p>",
                    options_en: ["<p>Rs. 250</p>", "<p>Rs. 200</p>", 
                                "<p>Rs. 300</p>", "<p>Rs. 350</p>"],
                    options_hi: ["<p>250 रुपए</p>", "<p>200 रुपए</p>",
                                "<p>300 रुपए</p>", "<p>350 रुपए</p>"],
                    solution_en: "<p>14.(a)<br>Let the original CP of an article be 100 unit<br>Then, original SP = 100 &times; 90% = 90 unit<br>New CP = 100 &times; 70% = 70 unit<br>New SP = 70 &times; 140% = 98 unit<br>ATQ, 98 - 90 = 8 unit ------------- ₹20<br>Then, 100 unit ------------- <math display=\"inline\"><mfrac><mrow><mn>20</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math> &times; 100 = ₹250</p>",
                    solution_hi: "<p>14.(a)<br>माना कि एक वस्तु का मूल क्रय मूल्य 100 इकाई है<br>फिर, मूल विक्रय मूल्य = 100 &times; 90% = 90 इकाई<br>नया क्रय मूल्य = 100 &times; 70% = 70 इकाई<br>नया विक्रय मूल्य = 70 &times; 140% = 98 इकाई<br>प्रश्न के अनुसार,<br>98 - 90 = 8 इकाई ------------- ₹20<br>फिर, 100 इकाई------------- <math display=\"inline\"><mfrac><mrow><mn>20</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math> &times; 100 = ₹250</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "15",
                    section: "misc",
                    question_en: "<p>15. The profit earned on selling an article for Rs. 4550 is 5 times of the loss incurred on selling the same article for Rs. 3290. What will be the selling price of the article to earn a profit of 20 percent ?</p>",
                    question_hi: "<p>15. एक वस्तु को 4550 रुपए में बेचने पर अर्जित लाभ, उसी वस्तु को 3290 रुपए में बेचने पर हुई हानि का 5 गुना है। 20 प्रतिशत का लाभ अर्जित करने के लिए वस्तु का विक्रय मूल्य कितना होगा ?</p>",
                    options_en: ["<p>Rs. 3900</p>", "<p>Rs. 4200</p>", 
                                "<p>Rs. 4000</p>", "<p>Rs. 4500</p>"],
                    options_hi: ["<p>3900 रुपए</p>", "<p>4200 रुपए</p>",
                                "<p>4000 रुपए</p>", "<p>4500 रुपए</p>"],
                    solution_en: "<p>15.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731564897337.png\" alt=\"rId4\"><br>5-(-1) = 6 unit ------------- 4550 <USER> <GROUP> = ₹1260<br>1 unit ------------- <math display=\"inline\"><mfrac><mrow><mn>1260</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math> = ₹210<br>CP of the article = 3290 + 210 = ₹3500<br>SP of the article = 3500 &times; 120% = ₹4,200</p>",
                    solution_hi: "<p>15.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731564897532.png\" alt=\"rId5\" width=\"333\" height=\"58\"><br>5-(-1) = 6 इकाई = 4550 - 3290 = ₹1260<br>1 इकाई = <math display=\"inline\"><mfrac><mrow><mn>1260</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math> = ₹210<br>वस्तु का क्रय मुल्य = 3290 + 210 = ₹3500<br>वस्तु का विक्रय मूल्य = 3500 &times; 120% = ₹4,200</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>