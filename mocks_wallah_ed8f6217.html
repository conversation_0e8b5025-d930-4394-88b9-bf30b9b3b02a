<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">25:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 25</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">25</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 25 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 0,
                end: 23
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 24,
                end: 24
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "17",
                    question_en: "<p>1. Find the fourth proportion to 1.25, 5.75 and 3.5</p>",
                    question_hi: "<p>1. 1.25, 5.75 और 3.5 का चतुर्थ समानुपाती ज्ञात कीजिए।</p>",
                    options_en: ["<p>4.5</p>", "<p>16.1</p>", 
                                "<p>12.5</p>", "<p>6.5</p>"],
                    options_hi: ["<p>4.5</p>", "<p>16.1</p>",
                                "<p>12.5</p>", "<p>6.5</p>"],
                    solution_en: "<p>1.(b) Let the fourth proportion be <math display=\"inline\"><mi>x</mi></math><br><math display=\"inline\"><mo>&#8658;</mo></math>&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mo>.</mo><mn>25</mn></mrow><mrow><mn>5</mn><mo>.</mo><mn>75</mn></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3</mn><mo>.</mo><mn>5</mn></mrow><mi mathvariant=\"normal\">x</mi></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math> x =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3</mn><mo>.</mo><mn>5</mn><mo>&#215;</mo><mn>5</mn><mo>.</mo><mn>75</mn></mrow><mrow><mn>1</mn><mo>.</mo><mn>25</mn></mrow></mfrac></math> = 16.1</p>",
                    solution_hi: "<p>1.(b) माना कि चौथा अनुपात <math display=\"inline\"><mi>x</mi></math> है <br><math display=\"inline\"><mo>&#8658;</mo></math>&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mo>.</mo><mn>25</mn></mrow><mrow><mn>5</mn><mo>.</mo><mn>75</mn></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3</mn><mo>.</mo><mn>5</mn></mrow><mi mathvariant=\"normal\">x</mi></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math> x =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3</mn><mo>.</mo><mn>5</mn><mo>&#215;</mo><mn>5</mn><mo>.</mo><mn>75</mn></mrow><mrow><mn>1</mn><mo>.</mo><mn>25</mn></mrow></mfrac></math> = 16.1</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "17",
                    question_en: "<p>2. The difference (in ₹) between a discount of 37% on ₹2,635 and two successive discounts of 23% and 7% on the same amount is (rounded off to 2 decimal places):</p>",
                    question_hi: "<p>2. ₹2,635 की धनराशि पर 37% की छूट और समान धनराशि पर 23% और 7% की दो क्रमागत छूटों के बीच का अंतर (₹ में) कितना है? (दशमलव के 2 स्थानों तक सन्त्रिकटित)</p>",
                    options_en: ["<p>226.87</p>", "<p>248.36</p>", 
                                "<p>242.25</p>", "<p>235.65</p>"],
                    options_hi: ["<p>226.87</p>", "<p>248.36</p>",
                                "<p>242.25</p>", "<p>235.65</p>"],
                    solution_en: "<p>2.(a) First discount = 37%<br>Second two successive discount (effective rate) = 23 + 7 - <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>23</mn><mo>&#215;</mo><mn>7</mn></mrow><mn>100</mn></mfrac></math> = 30 - 1.61 = 28.39%<br>Required amount = 2635 &times; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>37</mn><mo>-</mo><mn>28</mn><mo>.</mo><mn>39</mn><mo>)</mo></mrow><mn>100</mn></mfrac></math> = 2635 &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>8</mn><mo>.</mo><mn>61</mn></mrow><mn>100</mn></mfrac></math> = 226.87</p>",
                    solution_hi: "<p>2.(a) पहली छूट = 37%<br>दूसरी दो क्रमिक छूट (प्रभावी दर) = 23 + 7 - <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>23</mn><mo>&#215;</mo><mn>7</mn></mrow><mn>100</mn></mfrac></math>&nbsp;= 30 - 1.61 = 28.39%<br>आवश्यक राशि = 2635 &times; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>37</mn><mo>-</mo><mn>28</mn><mo>.</mo><mn>39</mn><mo>)</mo></mrow><mn>100</mn></mfrac></math> = 2635 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>8</mn><mo>.</mo><mn>61</mn></mrow><mn>100</mn></mfrac></math> = 226.87</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "17",
                    question_en: "<p>3. Two similar triangles have area 490 sq. cm. and 640 sq. cm. If the length of the one side of the first triangle is 21 cm, then what is the length of the corresponding side of the second triangle?</p>",
                    question_hi: "<p>3. दो समरूप त्रिभुजों का क्षेत्रफल 490 वर्ग cm और 640 वर्ग cm है। यदि पहले त्रिभुज की एक भुजा की लंबाई 21 cm है, तो दूसरे त्रिभुज की संगत भुजा की लंबाई क्या है?</p>",
                    options_en: ["<p>16 cm</p>", "<p>14 cm</p>", 
                                "<p>24 cm</p>", "<p>21 cm</p>"],
                    options_hi: ["<p>16 cm</p>", "<p>14 cm</p>",
                                "<p>24 cm</p>", "<p>21 cm</p>"],
                    solution_en: "<p>3.(c) According to question,<br><math display=\"inline\"><mo>&#8658;</mo></math>&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>Area</mi><mo>&#160;</mo><mi>of</mi><mo>&#160;</mo><mi>&#916;ABC</mi></mrow><mrow><mi>Area</mi><mo>&#160;</mo><mi>of</mi><mo>&#160;</mo><mi>&#916;PQR</mi></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mi>AB</mi><mn>2</mn></msup><msup><mi>PQ</mi><mn>2</mn></msup></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math>&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&#160;</mo><mn>490</mn></mrow><mn>640</mn></mfrac></math>=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mn>21</mn><mn>2</mn></msup><msup><mi>PQ</mi><mn>2</mn></msup></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math>&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&#160;</mo><mn>49</mn></mrow><mn>64</mn></mfrac></math>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mn>21</mn><mn>2</mn></msup><msup><mi>PQ</mi><mn>2</mn></msup></mfrac></math><br>Taking square root both side,<br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>8</mn></mfrac></math>=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>21</mn><mi>PQ</mi></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math> PQ = 24 cm</p>",
                    solution_hi: "<p>3.(c) प्रश्न के अनुसार,<br><math display=\"inline\"><mo>&#8658;</mo></math>&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#916;ABC</mi><mo>&#160;</mo><mi>&#2325;&#2366;</mi><mo>&#160;</mo><mi>&#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;&#2347;&#2354;</mi></mrow><mrow><mo>&#160;</mo><mi>&#916;PQR</mi><mo>&#160;</mo><mi>&#2325;&#2366;</mi><mo>&#160;</mo><mi>&#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;&#2347;&#2354;</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mi>AB</mi><mn>2</mn></msup><msup><mi>PQ</mi><mn>2</mn></msup></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math>&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&#160;</mo><mn>490</mn></mrow><mn>640</mn></mfrac></math>=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mn>21</mn><mn>2</mn></msup><msup><mi>PQ</mi><mn>2</mn></msup></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math>&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&#160;</mo><mn>49</mn></mrow><mn>64</mn></mfrac></math>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mn>21</mn><mn>2</mn></msup><msup><mi>PQ</mi><mn>2</mn></msup></mfrac></math><br>दोनों तरफ वर्गमूल लेने पर,<br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>8</mn></mfrac></math>=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>21</mn><mi>PQ</mi></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math> PQ = 24 cm</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "17",
                    question_en: "<p>4. The diameter of a roller is 35 cm and its length is 100 cm. It takes 200 complete revolutions to level a playground. Find the area of the playground in m<sup>2</sup></p>",
                    question_hi: "<p>4. एक रोलर का व्यास 35 cm और इसकी लंबाई 100 cm है। एक खेल के मैदान को समतल बनाने में इसे 200 पूर्ण परिक्रमण लगाने पड़ते हैं। m<sup>2</sup> में खेल के मैदान का क्षेत्रफल ज्ञात कीजिए।</p>",
                    options_en: ["<p>220</p>", "<p>2,20,00,000</p>", 
                                "<p>1,10,000</p>", "<p>110</p>"],
                    options_hi: ["<p>220</p>", "<p>2,20,00,000</p>",
                                "<p>1,10,000</p>", "<p>110</p>"],
                    solution_en: "<p>4.(a) <strong>Given:</strong> radius = <math display=\"inline\"><mfrac><mrow><mn>35</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> cm and length = 100 cm<br>Surface area of a roller = 2 &times; <math display=\"inline\"><mfrac><mrow><mn>22</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&#160;</mo><mn>35</mn></mrow><mn>2</mn></mfrac></math> &times; 100 = 11000 cm<sup>2</sup><br>1 revolution of roller = surface area of a roller <br>200 revolution = 11000 &times; 200 = 2200000 cm<sup>2</sup> = 220 m<sup>2</sup><br>Hence, Area of the playground = 220 m<sup>2</sup></p>",
                    solution_hi: "<p>4.(a) <strong>दिया गया है: </strong>त्रिज्या = <math display=\"inline\"><mfrac><mrow><mn>35</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> cm और लंबाई = 100 cm<br>रोलर का पृष्ठीय क्षेत्रफल = 2 &times; <math display=\"inline\"><mfrac><mrow><mn>22</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&#160;</mo><mn>35</mn></mrow><mn>2</mn></mfrac></math> &times; 100 = 11000 cm<sup>2</sup><br>रोलर की 1 परिक्रमा = रोलर का पृष्ठीय क्षेत्रफल <br>200 पूर्ण परिक्रमण = 11000 &times; 200 = 2200000 cm<sup>2</sup> = 220 m<sup>2</sup><br>अतः, खेल के मैदान का क्षेत्रफल = 220 m<sup>2</sup></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "17",
                    question_en: "<p>5. Find the amount to be paid at the end if ₹4,500 is invested for a period of 4 years at the rate of 8% simple interest per annum.</p>",
                    question_hi: "<p>5. यदि ₹4,500 की धनराशि का 8% वार्षिक साधारण ब्याज की दर से 4 वर्ष की अवधि के लिए निवेश किया जाता है, तो अवधि के अंत में भुगतान की जाने वाली धनराशि ज्ञात कीजिए।</p>",
                    options_en: ["<p>₹6,080</p>", "<p>₹5,940</p>", 
                                "<p>₹6,300</p>", "<p>₹5,490</p>"],
                    options_hi: ["<p>₹6,080</p>", "<p>₹5,940</p>",
                                "<p>₹6,300</p>", "<p>₹5,490</p>"],
                    solution_en: "<p>5.(b) let the principal = 100<br>Amount after 4 year with rate of 8% = 132<br>Now,<br>100 unit = ₹ 4500<br>132 unit = 45 &times; 132 = ₹ 5940</p>",
                    solution_hi: "<p>5.(b) माना मूलधन = 100<br>4 वर्ष के बाद 8% की दर से राशि = 132<br>अब,<br>100 इकाई = ₹ 4500<br>132 इकाई = 45 &times; 132 = ₹ 5940</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "17",
                    question_en: "<p>6. How many factors of 14,400 are divisible by 18 but not by 36?</p>",
                    question_hi: "<p>6. 14,400 के कितने गुणनखंड 18 से विभाज्य हैं, लेकिन 36 से नहीं ?</p>",
                    options_en: ["<p>3</p>", "<p>5</p>", 
                                "<p>4</p>", "<p>2</p>"],
                    options_hi: ["<p>3</p>", "<p>5</p>",
                                "<p>4</p>", "<p>2</p>"],
                    solution_en: "<p>6.(a)<br>Factor of 14400 = <math display=\"inline\"><msup><mrow><mn>2</mn></mrow><mrow><mn>6</mn></mrow></msup></math> &times; 3<sup>2</sup> &times; 5<sup>2</sup><br><math display=\"inline\"><mo>&#8658;</mo></math> <math display=\"inline\"><msup><mrow><mn>2</mn></mrow><mrow><mn>6</mn></mrow></msup></math> &times; 3<sup>2</sup> &times; 5<sup>2</sup> = 18 &times; (2<sup>5</sup> &times; 5<sup>2</sup>)<br>Divisible by 18 then remaining factor = 6 &times; 3 = 18<br><math display=\"inline\"><mo>&#8658;</mo></math> 2<sup>6</sup> &times; 3<sup>2</sup> &times; 5<sup>2</sup> = 36 &times; (2<sup>4</sup> &times; 5<sup>2</sup>)<br>Divisible by 36 then remaining factor = 5 &times; 3 = 15<br>Required factor divisible by 18 but not by 36 = 18 - 15 = 3</p>",
                    solution_hi: "<p>6.(a)<br>14400 का गुणनखंड = <math display=\"inline\"><msup><mrow><mn>2</mn></mrow><mrow><mn>6</mn></mrow></msup></math> &times; 3<sup>2</sup> &times; 5<sup>2</sup><br><math display=\"inline\"><mo>&#8658;</mo></math> <math display=\"inline\"><msup><mrow><mn>2</mn></mrow><mrow><mn>6</mn></mrow></msup></math> &times; 3<sup>2</sup> &times; 5<sup>2</sup> = 18 &times; (2<sup>5</sup> &times; 5<sup>2</sup>)<br>18 से विभाज्य तो शेष गुणनखंड = 6 &times; 3 = 18<br><math display=\"inline\"><mo>&#8658;</mo></math> 2<sup>6</sup> &times; 3<sup>2</sup> &times; 5<sup>2</sup> = 36 &times; (2<sup>4</sup> &times; 5<sup>2</sup>)<br>36 से विभाज्य तो शेष गुणनखंड = 5 &times; 3 = 15<br>आवश्यक गुणनखंड 18 से विभाज्य है लेकिन 36 से नहीं = 18 - 15 = 3</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "17",
                    question_en: "<p>7. The altitude of an equilateral triangle is 3<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math>&nbsp;cm. Find its area.</p>",
                    question_hi: "<p>7. एक समबाहु त्रिभुज की शीर्षलम्ब 3<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math>&nbsp;cm है। इसका क्षेत्रफल ज्ञात कीजिए।</p>",
                    options_en: ["<p>9 sq cm</p>", "<p>9<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math> sq cm</p>", 
                                "<p>27 sq cm</p>", "<p>3<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math>&nbsp;sq cm</p>"],
                    options_hi: ["<p>9 sq cm</p>", "<p>9<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math> sq cm</p>",
                                "<p>27 sq cm</p>", "<p>3<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math>&nbsp;sq cm</p>"],
                    solution_en: "<p>7.(b)<br>Height of an equilateral triangle = <math display=\"inline\"><mfrac><mrow><msqrt><mn>3</mn></msqrt></mrow><mrow><mn>2</mn></mrow></mfrac></math>a<br><math display=\"inline\"><mo>&#8658;</mo></math> <math display=\"inline\"><mfrac><mrow><msqrt><mn>3</mn></msqrt></mrow><mrow><mn>2</mn></mrow></mfrac></math>a = 3<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math><br><math display=\"inline\"><mo>&#8658;</mo></math> a = 6 cm<br>Area of an equilateral triangle = <math display=\"inline\"><mfrac><mrow><msqrt><mn>3</mn></msqrt></mrow><mrow><mn>4</mn></mrow></mfrac><msup><mrow><mi>a</mi></mrow><mrow><mn>2</mn></mrow></msup></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msqrt><mn>3</mn></msqrt><mn>4</mn></mfrac></math> &times; 36 = 9<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math> cm<sup>2</sup></p>",
                    solution_hi: "<p>7.(b)<br>एक समबाहु त्रिभुज की ऊँचाई = <math display=\"inline\"><mfrac><mrow><msqrt><mn>3</mn></msqrt></mrow><mrow><mn>2</mn></mrow></mfrac></math>a<br><math display=\"inline\"><mo>&#8658;</mo></math> <math display=\"inline\"><mfrac><mrow><msqrt><mn>3</mn></msqrt></mrow><mrow><mn>2</mn></mrow></mfrac></math>a = 3<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math><br><math display=\"inline\"><mo>&#8658;</mo></math> a = 6 cm<br>समबाहु त्रिभुज का क्षेत्रफल = <math display=\"inline\"><mfrac><mrow><msqrt><mn>3</mn></msqrt></mrow><mrow><mn>4</mn></mrow></mfrac><msup><mrow><mi>a</mi></mrow><mrow><mn>2</mn></mrow></msup></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msqrt><mn>3</mn></msqrt><mn>4</mn></mfrac></math> &times; 36 = 9<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math> cm<sup>2</sup></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "17",
                    question_en: "<p>8. The following pie chart represents the percentage domestic expenditure of Raman over his family. Study the pie chart carefully and answer the question given below.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733458700271.png\" alt=\"rId6\" width=\"342\" height=\"259\"> <br>After paying the House Rent and expending over Food and Children&rsquo;s Education, what is Raman\'s remaining monthly income (in ₹)?</p>",
                    question_hi: "<p>8. निम्नलिखित पाई-चार्ट में रमन के परिवार पर हुए घरेलू व्यय के प्रतिशत को दर्शाया गया है। पाई-चार्ट का ध्यानपूर्वक अध्ययन कीजिए और नीचे दिए गए प्रश्न का उत्तर दीजिए।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733458700410.png\" alt=\"rId7\" width=\"336\" height=\"254\"><br>मकान का किराया चुकाने, तथा भोजन और बच्चों की शिक्षा पर व्यय करने के बाद, रमन की शेष मासिक आय (₹ में) कितनी है?</p>",
                    options_en: ["<p>20,250</p>", "<p>29,250</p>", 
                                "<p>12,750</p>", "<p>15,750</p>"],
                    options_hi: ["<p>20,250</p>", "<p>29,250</p>",
                                "<p>12,750</p>", "<p>15,750</p>"],
                    solution_en: "<p>8.(d)<br>Raman&rsquo;s remaining income % = 100 - (25 + 20 + 20) = 35 %<br>Now,<br>Raman&rsquo;s remaining income = 45000 &times; <math display=\"inline\"><mfrac><mrow><mn>35</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = ₹ 15750</p>",
                    solution_hi: "<p>8.(d)<br>रमन की शेष आय % = 100 - (25 + 20 + 20) = 35 %<br>अब,<br>रमन की शेष आय = 45000 &times; <math display=\"inline\"><mfrac><mrow><mn>35</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = ₹ 15750</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "17",
                    question_en: "<p>9. Let ABC be a right-angled triangle such that &ang;C = 90&deg;. Let D be a point on AB such that&nbsp;CD is perpendicular to AB. If AC = 5 cm and BC = 12 cm, find the length of CD (in cm).</p>",
                    question_hi: "<p>9. माना ABC एक समकोण त्रिभुज इस प्रकार है कि &ang;C = 90&deg; है। मान लीजिए D, AB पर एक बिंदु इस प्रकार है कि CD, AB के लंबवत है। यदि AC = 5 cm और BC = 12 cm है, तो CD की लंबाई (cm में) ज्ञात कीजिए।</p>",
                    options_en: ["<p><math display=\"inline\"><mfrac><mrow><mn>40</mn></mrow><mrow><mn>23</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>50</mn></mrow><mrow><mn>23</mn></mrow></mfrac></math></p>", 
                                "<p><math display=\"inline\"><mfrac><mrow><mn>40</mn></mrow><mrow><mn>13</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>60</mn></mrow><mrow><mn>13</mn></mrow></mfrac></math></p>"],
                    options_hi: ["<p><math display=\"inline\"><mfrac><mrow><mn>40</mn></mrow><mrow><mn>23</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>50</mn></mrow><mrow><mn>23</mn></mrow></mfrac></math></p>",
                                "<p><math display=\"inline\"><mfrac><mrow><mn>40</mn></mrow><mrow><mn>13</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>60</mn></mrow><mrow><mn>13</mn></mrow></mfrac></math></p>"],
                    solution_en: "<p>9.(d)<br><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733458700562.png\" alt=\"rId8\" width=\"166\" height=\"133\"><br>By pythagorean triplet = (5, 12, 13)<br>So, AB = 13 cm<br>In <math display=\"inline\"><mi>&#916;</mi></math>ABC, <br><math display=\"inline\"><mo>&#8658;</mo></math> Required length (CD) =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>AC</mi><mo>&#215;</mo><mi>BC</mi></mrow><mi>AB</mi></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math> CD =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mo>&#215;</mo><mn>12</mn></mrow><mn>13</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>60</mn><mn>13</mn></mfrac></math> cm</p>",
                    solution_hi: "<p>9.(d)<br><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733458700562.png\" alt=\"rId8\" width=\"166\" height=\"133\"><br>पाइथागोरस ट्रिपलेट द्वारा = (5, 12, 13)<br>अत: AB = 13 सेमी<br><math display=\"inline\"><mi>&#916;</mi></math>ABC में, <br>आवश्यक लंबाई (CD) = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>AC</mi><mo>&#215;</mo><mi>BC</mi></mrow><mi>AB</mi></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math> CD =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mo>&#215;</mo><mn>12</mn></mrow><mn>13</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>60</mn><mn>13</mn></mfrac></math> cm</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "17",
                    question_en: "<p>10. A shopkeeper cheats 25% in weight while buying rice and cheats 25% while selling it. If he sells the rice at 12<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math>% profit, then find his net profit percentage.</p>",
                    question_hi: "<p>10. एक दुकानदार चावल खरीदते समय भार में 25% की बेईमानी करता है और बेचते समय 25% की बेईमानी करता है। यदि वह चावल को 12<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math>% लाभ पर बेचता है, तो उसका निवल लाभ प्रतिशत ज्ञात कीजिए।</p>",
                    options_en: ["<p>92.5%</p>", "<p>82.5%</p>", 
                                "<p>87.5%</p>", "<p>62.5%</p>"],
                    options_hi: ["<p>92.5%</p>", "<p>82.5%</p>",
                                "<p>87.5%</p>", "<p>62.5%</p>"],
                    solution_en: "<p>10.(c) According to question,<br>Ratio <math display=\"inline\"><mo>&#8594;</mo></math>&nbsp; &nbsp; &nbsp;C.P : S.P<br>Case 1&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 3 : 4<br>Case 2&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 4 : 5<br>Case 3&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 8 : 9<br>&mdash;---------------------------<br>Final ratio <math display=\"inline\"><mo>&#8594;</mo></math> 8 : 15<br>Required profit % = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>15</mn><mo>-</mo><mn>8</mn></mrow><mn>8</mn></mfrac></math> &times; 100 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>700</mn><mn>8</mn></mfrac></math> = 87.5 %</p>",
                    solution_hi: "<p>10.(c) प्रश्न के अनुसार,<br>अनुपात <math display=\"inline\"><mo>&#8594;</mo></math>&nbsp; &nbsp; &nbsp; &nbsp;C.P : S.P<br>स्तिथि 1&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;3 : 4<br>स्तिथि 2&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;4 : 5<br>स्तिथि 3&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;8 : 9<br>&mdash;---------------------------<br>अंतिम अनुपात <math display=\"inline\"><mo>&#8594;</mo></math> 8 : 15<br>आवाश्क अनुपात % = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>15</mn><mo>-</mo><mn>8</mn></mrow><mn>8</mn></mfrac></math> &times; 100 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>700</mn><mn>8</mn></mfrac></math> = 87.5 %</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "17",
                    question_en: "<p>11. X and Y can do a job in 18 days; Y and Z can do the same in 24 days. If all the three can finish the job in 12 days, in how many days can X and Z can complete the job?</p>",
                    question_hi: "<p>11. X और Y एक काम को 18 दिनों में पूरा कर सकते हैं; Y और Z उसी काम को 24 दिनों में पूरा कर सकते हैं। यदि वह तीनों उसी काम को 12 दिनों में पूरा कर सकते हैं, तो X और Z कितने दिनों में काम पूरा कर सकते हैं?</p>",
                    options_en: ["<p>14<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> days</p>", "<p>12<math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> days</p>", 
                                "<p>7 days</p>", "<p>5 days</p>"],
                    options_hi: ["<p>14<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> दिन</p>", "<p>12<math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> दिन</p>",
                                "<p>7 दिन</p>", "<p>5 दिन</p>"],
                    solution_en: "<p>11.(a) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733458700735.png\" alt=\"rId9\" width=\"237\" height=\"121\"><br>Efficiency of X = 6 - 3 = 3 units<br>Efficiency of Z = 6 - 4 = 2 units<br>Efficiency of (X + Z) = 5 units<br>So, Work done by (X + Z) = <math display=\"inline\"><mfrac><mrow><mn>72</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>14</mn><mfrac><mn>2</mn><mn>5</mn></mfrac></math> days</p>",
                    solution_hi: "<p>11.(a) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733458700932.png\" alt=\"rId10\" width=\"225\" height=\"126\"><br>X की दक्षता = 6 - 3 = 3 इकाई<br>Z की दक्षता = 6 - 4 = 2 इकाई<br>(X + Z) की दक्षता = 5 इकाई<br>इसलिए, (X + Z) द्वारा किया गया कार्य =&nbsp;<math display=\"inline\"><mfrac><mrow><mn>72</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>14</mn><mfrac><mn>2</mn><mn>5</mn></mfrac></math> दिन</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "17",
                    question_en: "<p>12. A person divides his total route of journey into three equal parts and decides to travel the three parts at the speeds of 80 km/h, 60 km/h and 30 km/h, respectively. What is the average speed during the journey?</p>",
                    question_hi: "<p>12. एक व्यक्ति अपनी यात्रा की कुल दूरी को तीन बराबर भागों में बांटता है और तीनों भागों को क्रमशः 80 km/h, 60 km/h और 30 km/h की चाल से तय करने का निर्णय लेता है। यात्रा के दौरान औसत चाल क्या है?</p>",
                    options_en: ["<p>49 km/h</p>", "<p>40 km/h</p>", 
                                "<p>45 km/h</p>", "<p>48 km/h</p>"],
                    options_hi: ["<p>49 km/h</p>", "<p>40 km/h</p>",
                                "<p>45 km/h</p>", "<p>48 km/h</p>"],
                    solution_en: "<p>12.(d) Let the total distance be 240 km<br>According to question,<br>Average speed = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>Total</mi><mi mathvariant=\"normal\">&#160;</mi><mi>distance</mi><mi mathvariant=\"normal\">&#160;</mi></mrow><mrow><mi>total</mi><mi mathvariant=\"normal\">&#160;</mi><mi>time</mi></mrow></mfrac></math> <br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>240</mn><mrow><mfrac><mn>80</mn><mn>80</mn></mfrac><mo>+</mo><mfrac><mn>80</mn><mn>60</mn></mfrac><mo>+</mo><mfrac><mn>80</mn><mn>30</mn></mfrac></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>240</mn><mrow><mn>1</mn><mo>+</mo><mstyle displaystyle=\"true\"><mfrac><mn>4</mn><mn>3</mn></mfrac></mstyle><mo>+</mo><mstyle displaystyle=\"true\"><mfrac><mn>8</mn><mn>3</mn></mfrac></mstyle></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>240</mn><mo>&#215;</mo><mn>3</mn></mrow><mrow><mn>3</mn><mo>+</mo><mn>4</mn><mo>+</mo><mn>8</mn></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>720</mn><mn>15</mn></mfrac></math>&nbsp;= 48 km/h</p>",
                    solution_hi: "<p>12.(d) माना कि कुल दूरी 240 किमी है<br>प्रश्न के अनुसार,<br>औसत गति = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2325;&#2369;&#2354;</mi><mo>&#160;</mo><mi>&#2342;&#2370;&#2352;&#2368;</mi><mi>&#160;</mi></mrow><mrow><mi>&#2325;&#2369;&#2354;</mi><mo>&#160;</mo><mi>&#2360;&#2350;&#2351;</mi></mrow></mfrac></math> <br>=&nbsp;<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>240</mn><mrow><mfrac><mn>80</mn><mn>80</mn></mfrac><mo>+</mo><mfrac><mn>80</mn><mn>60</mn></mfrac><mo>+</mo><mfrac><mn>80</mn><mn>30</mn></mfrac></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>240</mn><mrow><mn>1</mn><mo>+</mo><mstyle displaystyle=\"true\"><mfrac><mn>4</mn><mn>3</mn></mfrac></mstyle><mo>+</mo><mstyle displaystyle=\"true\"><mfrac><mn>8</mn><mn>3</mn></mfrac></mstyle></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>240</mn><mo>&#215;</mo><mn>3</mn></mrow><mrow><mn>3</mn><mo>+</mo><mn>4</mn><mo>+</mo><mn>8</mn></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>720</mn><mn>15</mn></mfrac></math> = 48 km/h</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "17",
                    question_en: "<p>13. A person travels from one place to another at 60 km/hr and returns at 75 km/hr. If the total time taken is 6 hours, then find the total distance travelled.</p>",
                    question_hi: "<p>13. एक व्यक्ति एक स्थान से दूसरे स्थान की यात्रा 60 km/hr की चाल से और वापसी की यात्रा 75 km/hr की चाल से तय करता है। यदि यात्रा में लिया गया कुल समय 6 घंटे है, तो तय की गई कुल दूरी ज्ञात कीजिए।</p>",
                    options_en: ["<p>250 km</p>", "<p>240 km</p>", 
                                "<p>400 km</p>", "<p>300 km</p>"],
                    options_hi: ["<p>250 km</p>", "<p>240 km</p>",
                                "<p>400 km</p>", "<p>300 km</p>"],
                    solution_en: "<p>13.(c) Let distance be x&nbsp;km <br>According to question,<br><math display=\"inline\"><mo>&#8658;</mo></math>&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">x</mi><mn>60</mn></mfrac></math>+&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">x</mi><mn>75</mn></mfrac></math> = 6<br><math display=\"inline\"><mo>&#8658;</mo></math>&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mi mathvariant=\"normal\">x</mi><mo>+</mo><mn>4</mn><mi mathvariant=\"normal\">x</mi></mrow><mn>300</mn></mfrac></math> = 6<br><math display=\"inline\"><mo>&#8658;</mo></math> 9x = 1800<br><math display=\"inline\"><mo>&#8658;</mo></math> x = 200 km<br>Hence, total distance travelled = 2 &times; 200 = 400 km</p>",
                    solution_hi: "<p>13.(c) माना दूरी x&nbsp;किमी है <br>प्रश्न के अनुसार,<br><math display=\"inline\"><mo>&#8658;</mo></math>&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">x</mi><mn>60</mn></mfrac></math>+&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">x</mi><mn>75</mn></mfrac></math> = 6<br><math display=\"inline\"><mo>&#8658;</mo></math>&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mi mathvariant=\"normal\">x</mi><mo>+</mo><mn>4</mn><mi mathvariant=\"normal\">x</mi></mrow><mn>300</mn></mfrac></math> = 6<br><math display=\"inline\"><mo>&#8658;</mo></math> 9x = 1800<br><math display=\"inline\"><mo>&#8658;</mo></math> x = 200 km<br>अतः तय की गई कुल दूरी = 2 &times; 200 = 400 km</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "17",
                    question_en: "<p>14. In &Delta;ABC, O is the point of intersection of the bisectors of angle B and angle A. If the angle BOC = 108&deg;, then angle BAO is:</p>",
                    question_hi: "<p>14. △ABC में, O, कोण B और कोण A के सम&zwnj;द्विभाजक का प्रतिच्छेदन बिंदु है। यदि कोण BOC = 108&deg; है, तो कोण BAO होगाः</p>",
                    options_en: ["<p>18&deg;</p>", "<p>26&deg;</p>", 
                                "<p>22&deg;</p>", "<p>16&deg;</p>"],
                    options_hi: ["<p>18&deg;</p>", "<p>26&deg;</p>",
                                "<p>22&deg;</p>", "<p>16&deg;</p>"],
                    solution_en: "<p>14.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733458701072.png\" alt=\"rId11\" width=\"200\"><br>In <math display=\"inline\"><mi>&#916;</mi></math>ABC,<br><math display=\"inline\"><mo>&#8658;</mo></math> &ang;BOC = 90&deg; +&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&#8736;</mo><mi>BAC</mi></mrow><mn>2</mn></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math> 108&deg; - 90&deg; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&#8736;</mo><mi>BAC</mi></mrow><mn>2</mn></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&#8736;</mo><mi>BAC</mi></mrow><mn>2</mn></mfrac></math> = 18&deg; = &ang;BAO<br>So, angle BAO will be 18&deg;.</p>",
                    solution_hi: "<p>14.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733458701072.png\" alt=\"rId11\" width=\"200\"><br><math display=\"inline\"><mi>&#916;</mi></math>ABC में,<br><math display=\"inline\"><mo>&#8658;</mo></math> &ang;BOC = 90&deg; +&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&#8736;</mo><mi>BAC</mi></mrow><mn>2</mn></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math> 108&deg; - 90&deg; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&#8736;</mo><mi>BAC</mi></mrow><mn>2</mn></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&#8736;</mo><mi>BAC</mi></mrow><mn>2</mn></mfrac></math> = 18&deg; = &ang;BAO<br>इसलिए, कोण BAO ,18&deg; होगा।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "17",
                    question_en: "<p>15. Let ABC be a triangle such that &ang;ABC = 70&deg; and &ang;ACB = 50&deg;. Let O be the incentre of the triangle. Find &ang;BOC.</p>",
                    question_hi: "<p>15. मान लीजिए ABC एक त्रिभुज इस प्रकार है कि &ang;ABC = 70&deg; और &ang;ACB = 50&deg; हैं। मान लीजिए O त्रिभुज का अंत: केंद्र है। &ang;BOC ज्ञात कीजिए।</p>",
                    options_en: ["<p>120&deg;</p>", "<p>130&deg;</p>", 
                                "<p>100&deg;</p>", "<p>60&deg;</p>"],
                    options_hi: ["<p>120&deg;</p>", "<p>130&deg;</p>",
                                "<p>100&deg;</p>", "<p>60&deg;</p>"],
                    solution_en: "<p>15.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733458701256.png\" alt=\"rId12\" width=\"200\"><br>In <math display=\"inline\"><mi>&#916;</mi></math>AMB, <br>&ang;BAM = 180&deg; - (90&deg; + 70&deg;) = 20&deg;<br>In <math display=\"inline\"><mi>&#916;</mi></math>AMC, <br>&ang;CAM = 180&deg; - (90&deg; + 50&deg;) = 40&deg;<br>So, &ang;BAC = &ang;BAM + &ang;CAM = 20&deg; + 40&deg; = 60&deg;<br>We know that,<br><math display=\"inline\"><mi>&#8736;</mi></math>BOC = 90&deg; +&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&#8736;</mo><mi>BAC</mi></mrow><mn>2</mn></mfrac></math> = 90&deg; +&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>60</mn><mo>&#176;</mo></mrow><mn>2</mn></mfrac></math> = 120&deg;</p>",
                    solution_hi: "<p>15.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733458701256.png\" alt=\"rId12\" width=\"200\"><br><math display=\"inline\"><mi>&#916;</mi></math>AMB में, <br>&ang;BAM = 180&deg; - (90&deg; + 70&deg;) = 20&deg;<br><math display=\"inline\"><mi>&#916;</mi></math>AMC में, <br>&ang;CAM = 180&deg; - (90&deg; + 50&deg;) = 40&deg;<br>इसलिए, &ang;BAC = &ang;BAM + &ang;CAM = 20&deg; + 40&deg; = 60&deg;<br>हम जानते है कि,<br><math display=\"inline\"><mi>&#8736;</mi></math>BOC = 90&deg; +&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&#8736;</mo><mi>BAC</mi></mrow><mn>2</mn></mfrac></math> = 90&deg; +&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>60</mn><mo>&#176;</mo></mrow><mn>2</mn></mfrac></math> = 120&deg;</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "17",
                    question_en: "<p>16. The following graph shows the data of the production of cloths (in lakh tonnes) by three different companies A, B and C over the years.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733458701448.png\" alt=\"rId13\" width=\"345\" height=\"270\"> <br>What is the ratio of the average production of Company A in the period 2012-2014 to the average production of Company B in the same period?</p>",
                    question_hi: "<p>16. निम्नलिखित ग्राफ कुछ वर्षों में तीन अलग-अलग कंपनियों A, B और द्वारा कपड़े के उत्पादन (लाख टन में) के आंकड़ों को दर्शाता है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733458701554.png\" alt=\"rId14\" width=\"381\" height=\"297\"> <br><strong>संदर्भ:</strong> Production of clothes by three companies over the years - कुछ वर्षों में तीन कंपनियों द्वारा कपड़ों का उत्पादन <br>2012-2014 की अवधि में कंपनी A के औसत उत्पादन का समान अवधि में कंपनी B के औसत उत्पादन से अनुपात कितना है?</p>",
                    options_en: ["<p>5 : 3</p>", "<p>3 : 5</p>", 
                                "<p>25 : 23</p>", "<p>23 : 25</p>"],
                    options_hi: ["<p>5 : 3</p>", "<p>3 : 5</p>",
                                "<p>25 : 23</p>", "<p>23 : 25</p>"],
                    solution_en: "<p>16.(d) <br>Average production of Company A = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>25</mn><mo>+</mo><mn>50</mn><mo>+</mo><mn>40</mn></mrow><mn>3</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>115</mn><mn>3</mn></mfrac></math><br>Average production of Company B = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>35</mn><mo>+</mo><mn>40</mn><mo>+</mo><mn>50</mn></mrow><mn>3</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>125</mn><mn>3</mn></mfrac></math><br>Required ratio <math display=\"inline\"><mo>&#8594;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>115</mn><mn>3</mn></mfrac></math> : <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>125</mn><mn>3</mn></mfrac></math> or 23 : 25</p>",
                    solution_hi: "<p>16.(d) <br>कंपनी A का औसत उत्पादन = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>25</mn><mo>+</mo><mn>50</mn><mo>+</mo><mn>40</mn></mrow><mn>3</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>115</mn><mn>3</mn></mfrac></math><br>कंपनी B का औसत उत्पादन = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>35</mn><mo>+</mo><mn>40</mn><mo>+</mo><mn>50</mn></mrow><mn>3</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>125</mn><mn>3</mn></mfrac></math><br>आवश्यक अनुपात <math display=\"inline\"><mo>&#8594;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>115</mn><mn>3</mn></mfrac></math> : <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>125</mn><mn>3</mn></mfrac></math> या 23 : 25</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "17",
                    question_en: "<p>17. K being any odd number greater than 1, k<sup>33</sup> &ndash; k is always divisible by:</p>",
                    question_hi: "<p>17. K 1 से बड़ी कोई भी विषम संख्या होने पर, k<sup>33 </sup>&ndash; k हमेशा ________ से विभाज्य होगा।</p>",
                    options_en: ["<p>5</p>", "<p>13</p>", 
                                "<p>24</p>", "<p>15</p>"],
                    options_hi: ["<p>5</p>", "<p>13</p>",
                                "<p>24</p>", "<p>15</p>"],
                    solution_en: "<p>17.(c) <strong>Given:</strong> k<sup>33</sup> &ndash; k<br>= k(k<sup>32</sup> - k)<br>= k(k<sup>16</sup> - 1)(k<sup>16</sup> + 1)<br>= k(k<sup>8</sup> - 1)(k<sup>8</sup> + 1)(k<sup>16</sup> + 1)<br>= k(k<sup>4</sup>-1)(k<sup>4</sup> + 1)(k<sup>8</sup> + 1)(k<sup>16</sup> + 1)<br>= k(k<sup>2</sup> -1)(k<sup>2</sup> + 1)(k<sup>4</sup> + 1)(k<sup>8</sup> + 1)(k<sup>16</sup> + 1)<br>= k(k - 1)(k + 1)(k<sup>2</sup> + 1)(k<sup>4</sup> + 1)(k<sup>8</sup> + 1)(k<sup>16</sup> + 1)<br>Put the value of k = 3, 5 ,7&hellip;..<br>= 3 &times; 2 &times; 4[(3<sup>2</sup> + 1)(3<sup>4</sup> + 1)(3<sup>8</sup> + 1)(3<sup>16</sup> + 1)]<br>= 24 &times; [(3<sup>2</sup> + 1)(3<sup>4</sup> + 1)(3<sup>8</sup> + 1)(3<sup>16</sup> + 1)]<br>From the above expression it is clear that k<sup>33</sup> - k will always be divisible by 24.</p>",
                    solution_hi: "<p>17.(c) <strong>दिया गया:</strong> k<sup>33</sup> &ndash; k<br>= k(k<sup>32</sup> - k)<br>= k(k<sup>16</sup> - 1)(k<sup>16</sup> + 1)<br>= k(k<sup>8</sup> - 1)(k<sup>8</sup> + 1)(k<sup>16</sup> + 1)<br>= k(k<sup>4</sup>-1)(k<sup>4</sup> + 1)(k<sup>8</sup> + 1)(k<sup>16</sup> + 1)<br>= k(k<sup>2</sup> -1)(k<sup>2</sup> + 1)(k<sup>4</sup> + 1)(k<sup>8</sup> + 1)(k<sup>16</sup> + 1)<br>= k(k - 1)(k + 1)(k<sup>2</sup> + 1)(k<sup>4</sup> + 1)(k<sup>8</sup> + 1)(k<sup>16</sup> + 1)<br>k का मान = 3, 5, 7 &hellip;.. रखने पर,<br>= 3 &times; 2 &times; 4[(3<sup>2</sup> + 1)(3<sup>4</sup> + 1)(3<sup>8</sup> + 1)(3<sup>16</sup> + 1)]<br>= 24 &times; [(3<sup>2</sup> + 1)(3<sup>4</sup> + 1)(3<sup>8</sup> + 1)(3<sup>16</sup> + 1)]<br>उपरोक्त अभिव्यक्ति से यह स्पष्ट है कि k<sup>33</sup> - k हमेशा 24 से विभाज्य होगा ।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "17",
                    question_en: "<p>18. The price of a motorcycle was $750 last year. The price increased by 20% this year. What is the price of the motorcycle this year ?</p>",
                    question_hi: "<p>18. एक मोटरसाइकिल की कीमत पिछले साल $750 थी। इस साल कीमत में 20% की वृद्धि हुई। इस साल मोटरसाइकिल की कीमत क्या है?</p>",
                    options_en: ["<p>$600</p>", "<p>$790</p>", 
                                "<p>$750</p>", "<p>$900</p>"],
                    options_hi: ["<p>$600</p>", "<p>$790</p>",
                                "<p>$750</p>", "<p>$900</p>"],
                    solution_en: "<p>18.(d) <br>Price of the motor cycle this year = $750 &times; <math display=\"inline\"><mfrac><mrow><mn>6</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> = $900</p>",
                    solution_hi: "<p>18.(d) <br>इस वर्ष मोटर साइकिल की कीमत = $750 &times; <math display=\"inline\"><mfrac><mrow><mn>6</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> = $900</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "17",
                    question_en: "<p>19. The number of subjects in which the students of a class have failed is given in the following table.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733458701682.png\" alt=\"rId15\" width=\"435\" height=\"43\"> <br>Find the total number of students who have failed in more than 3 subjects.</p>",
                    question_hi: "<p>19. एक कक्षा के विद्यार्थी जिन विषयों में अनुत्तीर्ण हुए हैं उनकी संख्या निम्न तालिका में दी गई है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733458701788.png\" alt=\"rId16\" width=\"384\" height=\"48\"> <br>3 से अधिक विषयों में अनुत्तीर्ण होने वाले विद्यार्थियों की कुल संख्या ज्ञात कीजिए।</p>",
                    options_en: ["<p>10</p>", "<p>17</p>", 
                                "<p>11</p>", "<p>12</p>"],
                    options_hi: ["<p>10</p>", "<p>17</p>",
                                "<p>11</p>", "<p>12</p>"],
                    solution_en: "<p>19.(c)<br>Number of students failed in more than 3 subjects = 7 + 4 = 11</p>",
                    solution_hi: "<p>19.(c)<br>3 से अधिक विषयों में अनुत्तीर्ण विद्यार्थियों की संख्या= 7 + 4 = 11</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "17",
                    question_en: "<p>20. If the graph of the equation 20x&nbsp;+ 21y = 420 cuts the coordinate axes at P and Q, then what is the measure of the length (in units) of PQ ?</p>",
                    question_hi: "<p>20. यदि समीकरण 20x&nbsp;+ 21y = 420 का ग्राफ निर्देशांक अक्षों को P और Q पर काटता है, तो PQ की लंबाई की माप (इकाइयों में) क्या है?</p>",
                    options_en: ["<p>29</p>", "<p>35</p>", 
                                "<p>25</p>", "<p>40</p>"],
                    options_hi: ["<p>29</p>", "<p>35</p>",
                                "<p>25</p>", "<p>40</p>"],
                    solution_en: "<p>20.(a) <strong>Given:</strong> 20x&nbsp;+ 21y = 420<br><math display=\"inline\"><mo>&#8658;</mo></math>&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">x</mi><mn>21</mn></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&#160;</mo><mi mathvariant=\"normal\">y</mi></mrow><mn>20</mn></mfrac></math> = 1<br>According to the question,<br>Coordinates of P and Q = (21, 0) and (0, 20)<br>Length of PQ = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mrow><mo>(</mo><msub><mi mathvariant=\"normal\">x</mi><mn>2</mn></msub><mo>-</mo><msub><mi mathvariant=\"normal\">x</mi><mn>1</mn></msub><mo>)</mo></mrow><mn>2</mn></msup><mo>+</mo><msup><mrow><mo>(</mo><msub><mi mathvariant=\"normal\">y</mi><mn>2</mn></msub><mo>-</mo><msub><mi mathvariant=\"normal\">y</mi><mn>1</mn></msub><mo>)</mo></mrow><mn>2</mn></msup></msqrt></math><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mrow><mo>(</mo><mn>0</mn><mo>-</mo><mn>21</mn><mo>)</mo></mrow><mn>2</mn></msup><mo>+</mo><msup><mrow><mo>(</mo><mn>20</mn><mo>-</mo><mn>0</mn><mo>)</mo></mrow><mn>2</mn></msup></msqrt></math><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>441</mn><mo>+</mo><mn>400</mn></msqrt></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mo>&#160;</mo><mn>841</mn></msqrt></math> = 29 units</p>",
                    solution_hi: "<p>20.(a) <strong>दिया गया है:</strong> 20x&nbsp;+ 21y = 420<br><math display=\"inline\"><mo>&#8658;</mo></math>&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">x</mi><mn>21</mn></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&#160;</mo><mi mathvariant=\"normal\">y</mi></mrow><mn>20</mn></mfrac></math> = 1<br>प्रश्न के अनुसार,<br>P और Q के निर्देशांक = (21, 0) and (0, 20)<br>PQ की लंबाई = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mrow><mo>(</mo><msub><mi mathvariant=\"normal\">x</mi><mn>2</mn></msub><mo>-</mo><msub><mi mathvariant=\"normal\">x</mi><mn>1</mn></msub><mo>)</mo></mrow><mn>2</mn></msup><mo>+</mo><msup><mrow><mo>(</mo><msub><mi mathvariant=\"normal\">y</mi><mn>2</mn></msub><mo>-</mo><msub><mi mathvariant=\"normal\">y</mi><mn>1</mn></msub><mo>)</mo></mrow><mn>2</mn></msup></msqrt></math><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mrow><mo>(</mo><mn>0</mn><mo>-</mo><mn>21</mn><mo>)</mo></mrow><mn>2</mn></msup><mo>+</mo><msup><mrow><mo>(</mo><mn>20</mn><mo>-</mo><mn>0</mn><mo>)</mo></mrow><mn>2</mn></msup></msqrt></math><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>441</mn><mo>+</mo><mn>400</mn></msqrt></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mo>&#160;</mo><mn>841</mn></msqrt></math> = 29 इकाई</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "17",
                    question_en: "<p>21. Study the given bar-graph carefully and answer the question that follows.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733458701879.png\" alt=\"rId17\" width=\"410\" height=\"310\"> <br>For how many years was the export above - average for the period, 2013 - 2019?</p>",
                    question_hi: "<p>21. दिए गए दंड-आलेख का ध्यानपूर्वक अध्ययन कीजिए और नीचे दिए गए प्रश्न का उत्तर दीजिए।&nbsp;दंड आलेख दर्शाता है?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733458701996.png\" alt=\"rId18\" width=\"370\" height=\"283\"> <br>2013-2019 की अवधि के लिए निर्यात कितने वर्षों के लिए औसत से अधिक था?</p>",
                    options_en: ["<p>3</p>", "<p>2</p>", 
                                "<p>4</p>", "<p>1</p>"],
                    options_hi: ["<p>3</p>", "<p>2</p>",
                                "<p>4</p>", "<p>1</p>"],
                    solution_en: "<p>21.(c) <br>Average of Export = <math display=\"inline\"><mfrac><mrow><mn>9</mn><mo>.</mo><mn>9</mn><mo>+</mo><mn>10</mn><mo>.</mo><mn>8</mn><mo>+</mo><mn>7</mn><mo>.</mo><mn>8</mn><mo>+</mo><mn>5</mn><mo>.</mo><mn>2</mn><mo>+</mo><mn>6</mn><mo>.</mo><mn>5</mn><mo>+</mo><mn>9</mn><mo>.</mo><mn>5</mn><mo>+</mo><mn>11</mn><mo>.</mo><mn>4</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> &asymp; 8.72<br>It is clear from the above expression 4 years the export above from the average of export.</p>",
                    solution_hi: "<p>21.(c) <br>निर्यात का औसत = <math display=\"inline\"><mfrac><mrow><mn>9</mn><mo>.</mo><mn>9</mn><mo>+</mo><mn>10</mn><mo>.</mo><mn>8</mn><mo>+</mo><mn>7</mn><mo>.</mo><mn>8</mn><mo>+</mo><mn>5</mn><mo>.</mo><mn>2</mn><mo>+</mo><mn>6</mn><mo>.</mo><mn>5</mn><mo>+</mo><mn>9</mn><mo>.</mo><mn>5</mn><mo>+</mo><mn>11</mn><mo>.</mo><mn>4</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> &asymp; 8.72<br>उपरोक्त अभिव्यक्ति से यह स्पष्ट है कि निर्यात के औसत से 4 वर्ष अधिक निर्यात हुआ।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "17",
                    question_en: "<p>22. A person spends 60% of his salary on family expenses, 10% on medical expenses, 5% on charity, and saves the remaining amount. If the amount on savings is ₹4,000, find his monthly salary.</p>",
                    question_hi: "<p>22. एक व्यक्ति अपने वेतन का 60% पारिवारिक व्यय पर, 10% चिकित्सा व्यय पर, 5% दान पर खर्च करता है, और शेष धनराशि बचाता है। यदि बचत राशि ₹4,000 है, तो उसका मासिक वेतन ज्ञात कीजिए।</p>",
                    options_en: ["<p>₹12,000</p>", "<p>₹15,000</p>", 
                                "<p>₹14,000</p>", "<p>₹16,000</p>"],
                    options_hi: ["<p>₹12,000</p>", "<p>₹15,000</p>",
                                "<p>₹14,000</p>", "<p>₹16,000</p>"],
                    solution_en: "<p>22.(d) Let the monthly salary be 100%<br>According to question,<br>Saving = 100 - (60 + 10 + 5) = 25%<br>Now,<br><math display=\"inline\"><mo>&#8658;</mo></math> 25% = 4000<br><math display=\"inline\"><mo>&#8658;</mo></math> 100 % = ₹ 16000</p>",
                    solution_hi: "<p>22.(d) मासिक वेतन 100% है। <br>प्रश्न के अनुसार,<br>बचत = 100 - (60 + 10 + 5) = 25%<br>अब, <br><math display=\"inline\"><mo>&#8658;</mo></math> 25% = 4000<br><math display=\"inline\"><mo>&#8658;</mo></math> 100 % = ₹ 16000</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "23",
                    section: "17",
                    question_en: "<p>23. In an election, voters have the option to vote for one of three candidates. They also have the option to select \'None Of The Above\' (NOTA) to reject all the candidates. All three candidates get 6 times the NOTA votes. The winner gets twice the votes of the second runner-up. The first runner-up gets 900 more votes than NOTA and defeats the second runner-up by 150 votes. Find the total number of votes cast.</p>",
                    question_hi: "<p>23. एक चुनाव में, मतदाताओं के पास तीन उम्मीदवारों में से एक उम्मीदवार को वोट देने का विकल्प है। उनके पास सभी उम्मीदवारों को अस्वीकार करने के लिए \'उपरोक्त में से कोई नहीं\' (None Of The Above - NOTA) के चयन का भी विकल्प है। तीनों उम्मीदवारों को नोटा (NOTA) के 6 गुना वोट मिलते हैं। विजेता को दूसरे उपविजेता के वोटों के दोगुने वोट मिलते हैं। पहले उपविजेता को नोटा (NOTA) से 900 वोट अधिक मिलते हैं और वह दूसरे उपविजेता को 150 वोट से हरा देता है। डाले गए मतों की कुल संख्या ज्ञात करें।</p>",
                    options_en: ["<p>15177</p>", "<p>11577</p>", 
                                "<p>11025</p>", "<p>17715</p>"],
                    options_hi: ["<p>15177</p>", "<p>11577</p>",
                                "<p>11025</p>", "<p>17715</p>"],
                    solution_en: "<p>23.(c)<br>Let winner = A ,1<sup>st</sup> runner up = B ,2<sup>nd</sup> runner up = C<br>and nota = N<br>According to question<br>A = 2C, B = N + 900 and C = N + 900 - 150 <br>Now,<br>A + B + C = 6N<br>2C + B + C = 6N<br>3C + B = 6N<br>3C + N + 900 = 6N<br>3(N + 900 - 150) + N + 900 = 6N<br>3N + 2250 + N + 900 = 6N<br>3150 = 2N<br>N = 1575<br>Hence, B = N + 900 = 2475<br>C = N + 900 - 150 <br>= 2325<br>A = 2C = 4650<br>Total votes = A + B + C + N<br>= 4650 + 2475 + 2325 + 1575 <br>= 11025</p>",
                    solution_hi: "<p>23.(c)<br>माना विजेता = A ,प्रथम उपविजेता = B ,द्वितीय उपविजेता = C<br>और नोटा = N<br>प्रश्न के अनुसार<br>A = 2C, B = N + 900 and C = N + 900 - 150 <br>अब,<br>A + B + C = 6N<br>2C + B + C = 6N<br>3C + B = 6N<br>3C + N + 900 = 6N<br>3(N + 900 - 150) + N + 900 = 6N<br>3N + 2250 + N + 900 = 6N<br>3150 = 2N<br>N = 1575<br>अत:, B = N + 900 = 2475<br>C = N + 900 - 150 <br>= 2325<br>A = 2C = 4650<br>कुल वोट = A + B + C + N<br>= 4650 + 2475 + 2325 + 1575 <br>= 11025</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "24",
                    section: "17",
                    question_en: "<p>24. [<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>cosec</mi><mi mathvariant=\"normal\">&#160;</mi><mn>31</mn><mo>&#176;</mo><mo>-</mo><mi>sec</mi><mi mathvariant=\"normal\">&#160;</mi><mn>59</mn><mo>&#176;</mo></mrow><mrow><mo>(</mo><mi>sin</mi><mi mathvariant=\"normal\">&#160;</mi><mn>18</mn><mo>&#176;</mo><mo>&#247;</mo><mi>cos</mi><mi mathvariant=\"normal\">&#160;</mi><mn>72</mn><mo>&#176;</mo><mo>)</mo></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>tan</mi><mo>&#160;</mo><mn>26</mn><mo>&#176;</mo></mrow><mrow><mi>cot</mi><mo>&#160;</mo><mn>64</mn><mo>&#176;</mo></mrow></mfrac></math>] + [<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>tan</mi><mo>&#160;</mo><mn>30</mn><mo>&#176;</mo><mo>+</mo><mi>tan</mi><mo>&#160;</mo><mn>15</mn><mo>&#176;</mo></mrow><mrow><mo>(</mo><mn>1</mn><mo>-</mo><mi>tan</mi><mo>&#160;</mo><mn>30</mn><mo>&#176;</mo><mo>&#160;</mo><mi>tan</mi><mo>&#160;</mo><mn>15</mn><mo>&#176;</mo><mo>)</mo></mrow></mfrac></math>]&nbsp; = __________________.</p>",
                    question_hi: "<p>24. [<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>cosec</mi><mi mathvariant=\"normal\">&#160;</mi><mn>31</mn><mo>&#176;</mo><mo>-</mo><mi>sec</mi><mi mathvariant=\"normal\">&#160;</mi><mn>59</mn><mo>&#176;</mo></mrow><mrow><mo>(</mo><mi>sin</mi><mi mathvariant=\"normal\">&#160;</mi><mn>18</mn><mo>&#176;</mo><mo>&#247;</mo><mi>cos</mi><mi mathvariant=\"normal\">&#160;</mi><mn>72</mn><mo>&#176;</mo><mo>)</mo></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>tan</mi><mo>&#160;</mo><mn>26</mn><mo>&#176;</mo></mrow><mrow><mi>cot</mi><mo>&#160;</mo><mn>64</mn><mo>&#176;</mo></mrow></mfrac></math>] + [<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>tan</mi><mo>&#160;</mo><mn>30</mn><mo>&#176;</mo><mo>+</mo><mi>tan</mi><mo>&#160;</mo><mn>15</mn><mo>&#176;</mo></mrow><mrow><mo>(</mo><mn>1</mn><mo>-</mo><mi>tan</mi><mo>&#160;</mo><mn>30</mn><mo>&#176;</mo><mo>&#160;</mo><mi>tan</mi><mo>&#160;</mo><mn>15</mn><mo>&#176;</mo><mo>)</mo></mrow></mfrac></math>]&nbsp; = __________________.</p>",
                    options_en: ["<p>-1</p>", "<p>0</p>", 
                                "<p>1</p>", "<p>2</p>"],
                    options_hi: ["<p>-1</p>", "<p>0</p>",
                                "<p>1</p>", "<p>2</p>"],
                    solution_en: "<p>24.(c) [<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>cosec</mi><mi mathvariant=\"normal\">&#160;</mi><mn>31</mn><mo>&#176;</mo><mo>-</mo><mi>sec</mi><mi mathvariant=\"normal\">&#160;</mi><mn>59</mn><mo>&#176;</mo></mrow><mrow><mo>(</mo><mi>sin</mi><mi mathvariant=\"normal\">&#160;</mi><mn>18</mn><mo>&#176;</mo><mo>&#247;</mo><mi>cos</mi><mi mathvariant=\"normal\">&#160;</mi><mn>72</mn><mo>&#176;</mo><mo>)</mo></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>tan</mi><mo>&#160;</mo><mn>26</mn><mo>&#176;</mo></mrow><mrow><mi>cot</mi><mo>&#160;</mo><mn>64</mn><mo>&#176;</mo></mrow></mfrac></math>] + [<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>tan</mi><mo>&#160;</mo><mn>30</mn><mo>&#176;</mo><mo>+</mo><mi>tan</mi><mo>&#160;</mo><mn>15</mn><mo>&#176;</mo></mrow><mrow><mo>(</mo><mn>1</mn><mo>-</mo><mi>tan</mi><mo>&#160;</mo><mn>30</mn><mo>&#176;</mo><mo>&#160;</mo><mi>tan</mi><mo>&#160;</mo><mn>15</mn><mo>&#176;</mo><mo>)</mo></mrow></mfrac></math>]<br>= [<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>cosec</mi><mi mathvariant=\"normal\">&#160;</mi><mn>31</mn><mo>&#176;</mo><mo>-</mo><mi>sec</mi><mo>(</mo><mn>90</mn><mo>&#176;</mo><mo>-</mo><mn>31</mn><mo>&#176;</mo><mo>)</mo></mrow><mrow><mo>(</mo><mi>sin</mi><mi mathvariant=\"normal\">&#160;</mi><mn>18</mn><mo>&#176;</mo><mo>&#247;</mo><mi>cos</mi><mi mathvariant=\"normal\">&#160;</mi><mn>72</mn><mo>&#176;</mo><mo>)</mo></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>tan</mi><mo>(</mo><mn>90</mn><mo>&#176;</mo><mo>-</mo><mn>64</mn><mo>&#176;</mo><mo>)</mo></mrow><mrow><mi>cot</mi><mo>&#160;</mo><mn>64</mn><mo>&#176;</mo></mrow></mfrac></math>] + [<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>tan</mi><mo>&#160;</mo><mn>30</mn><mo>&#176;</mo><mo>+</mo><mi>tan</mi><mo>&#160;</mo><mn>15</mn><mo>&#176;</mo></mrow><mrow><mo>(</mo><mn>1</mn><mo>-</mo><mi>tan</mi><mo>&#160;</mo><mn>30</mn><mo>&#176;</mo><mo>&#160;</mo><mi>tan</mi><mo>&#160;</mo><mn>15</mn><mo>&#176;</mo><mo>)</mo></mrow></mfrac></math>]<br>= [<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>cosec</mi><mi mathvariant=\"normal\">&#160;</mi><mn>31</mn><mo>&#176;</mo><mo>-</mo><mi>cosec</mi><mi mathvariant=\"normal\">&#160;</mi><mn>31</mn><mo>&#176;</mo></mrow><mrow><mo>(</mo><mi>sin</mi><mi mathvariant=\"normal\">&#160;</mi><mn>18</mn><mo>&#176;</mo><mo>&#247;</mo><mi>cos</mi><mi mathvariant=\"normal\">&#160;</mi><mn>72</mn><mo>&#176;</mo><mo>)</mo></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>cot</mi><mo>&#160;</mo><mn>64</mn><mo>&#176;</mo></mrow><mrow><mi>cot</mi><mo>&#160;</mo><mn>64</mn><mo>&#176;</mo></mrow></mfrac></math>] + tan (30&deg; + 15&deg;)<br>= [<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>0</mn><mrow><mo>(</mo><mi>sin</mi><mi mathvariant=\"normal\">&#160;</mi><mn>18</mn><mo>&#176;</mo><mo>&#247;</mo><mi>cos</mi><mi mathvariant=\"normal\">&#160;</mi><mn>72</mn><mo>&#176;</mo><mo>)</mo></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>cot</mi><mo>&#160;</mo><mn>64</mn><mo>&#176;</mo></mrow><mrow><mi>cot</mi><mo>&#160;</mo><mn>64</mn><mo>&#176;</mo></mrow></mfrac></math>] + tan 45&deg;<br>= 0 + 1 = 1</p>",
                    solution_hi: "<p>24.(c) [<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>cosec</mi><mi mathvariant=\"normal\">&#160;</mi><mn>31</mn><mo>&#176;</mo><mo>-</mo><mi>sec</mi><mi mathvariant=\"normal\">&#160;</mi><mn>59</mn><mo>&#176;</mo></mrow><mrow><mo>(</mo><mi>sin</mi><mi mathvariant=\"normal\">&#160;</mi><mn>18</mn><mo>&#176;</mo><mo>&#247;</mo><mi>cos</mi><mi mathvariant=\"normal\">&#160;</mi><mn>72</mn><mo>&#176;</mo><mo>)</mo></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>tan</mi><mo>&#160;</mo><mn>26</mn><mo>&#176;</mo></mrow><mrow><mi>cot</mi><mo>&#160;</mo><mn>64</mn><mo>&#176;</mo></mrow></mfrac></math>] + [<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>tan</mi><mo>&#160;</mo><mn>30</mn><mo>&#176;</mo><mo>+</mo><mi>tan</mi><mo>&#160;</mo><mn>15</mn><mo>&#176;</mo></mrow><mrow><mo>(</mo><mn>1</mn><mo>-</mo><mi>tan</mi><mo>&#160;</mo><mn>30</mn><mo>&#176;</mo><mo>&#160;</mo><mi>tan</mi><mo>&#160;</mo><mn>15</mn><mo>&#176;</mo><mo>)</mo></mrow></mfrac></math>]<br>= [<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>cosec</mi><mi mathvariant=\"normal\">&#160;</mi><mn>31</mn><mo>&#176;</mo><mo>-</mo><mi>sec</mi><mo>(</mo><mn>90</mn><mo>&#176;</mo><mo>-</mo><mn>31</mn><mo>&#176;</mo><mo>)</mo></mrow><mrow><mo>(</mo><mi>sin</mi><mi mathvariant=\"normal\">&#160;</mi><mn>18</mn><mo>&#176;</mo><mo>&#247;</mo><mi>cos</mi><mi mathvariant=\"normal\">&#160;</mi><mn>72</mn><mo>&#176;</mo><mo>)</mo></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>tan</mi><mo>(</mo><mn>90</mn><mo>&#176;</mo><mo>-</mo><mn>64</mn><mo>&#176;</mo><mo>)</mo></mrow><mrow><mi>cot</mi><mo>&#160;</mo><mn>64</mn><mo>&#176;</mo></mrow></mfrac></math>] + [<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>tan</mi><mo>&#160;</mo><mn>30</mn><mo>&#176;</mo><mo>+</mo><mi>tan</mi><mo>&#160;</mo><mn>15</mn><mo>&#176;</mo></mrow><mrow><mo>(</mo><mn>1</mn><mo>-</mo><mi>tan</mi><mo>&#160;</mo><mn>30</mn><mo>&#176;</mo><mo>&#160;</mo><mi>tan</mi><mo>&#160;</mo><mn>15</mn><mo>&#176;</mo><mo>)</mo></mrow></mfrac></math>]<br>= [<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>cosec</mi><mi mathvariant=\"normal\">&#160;</mi><mn>31</mn><mo>&#176;</mo><mo>-</mo><mi>cosec</mi><mi mathvariant=\"normal\">&#160;</mi><mn>31</mn><mo>&#176;</mo></mrow><mrow><mo>(</mo><mi>sin</mi><mi mathvariant=\"normal\">&#160;</mi><mn>18</mn><mo>&#176;</mo><mo>&#247;</mo><mi>cos</mi><mi mathvariant=\"normal\">&#160;</mi><mn>72</mn><mo>&#176;</mo><mo>)</mo></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>cot</mi><mo>&#160;</mo><mn>64</mn><mo>&#176;</mo></mrow><mrow><mi>cot</mi><mo>&#160;</mo><mn>64</mn><mo>&#176;</mo></mrow></mfrac></math>] + tan (30&deg; + 15&deg;)<br>= [<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>0</mn><mrow><mo>(</mo><mi>sin</mi><mi mathvariant=\"normal\">&#160;</mi><mn>18</mn><mo>&#176;</mo><mo>&#247;</mo><mi>cos</mi><mi mathvariant=\"normal\">&#160;</mi><mn>72</mn><mo>&#176;</mo><mo>)</mo></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>cot</mi><mo>&#160;</mo><mn>64</mn><mo>&#176;</mo></mrow><mrow><mi>cot</mi><mo>&#160;</mo><mn>64</mn><mo>&#176;</mo></mrow></mfrac></math>] + tan 45&deg;<br>= 0 + 1 = 1</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "25",
                    section: "misc",
                    question_en: "<p>25. The whole surface area of a cube is 294 cm<sup>2</sup>. The volume of the cube is:</p>",
                    question_hi: "<p>25. एक घन का संपूर्ण पृष्ठीय क्षेत्रफल 294 cm<sup>2</sup> है। घन का आयतन कितना है?</p>",
                    options_en: ["<p>243 cm<sup>3</sup></p>", "<p>343 cm<sup>3</sup></p>", 
                                "<p>216 cm<sup>3</sup></p>", "<p>512 cm<sup>3</sup></p>"],
                    options_hi: ["<p>243 cm<sup>3</sup></p>", "<p>343 cm<sup>3</sup></p>",
                                "<p>216 cm<sup>3</sup></p>", "<p>512 cm<sup>3</sup></p>"],
                    solution_en: "<p>25.(b) Total surface area of cube = 6<math display=\"inline\"><msup><mrow><mi>a</mi></mrow><mrow><mn>2</mn></mrow></msup></math><br><math display=\"inline\"><mo>&#8658;</mo></math> 294 = 6a<sup>2</sup><br><math display=\"inline\"><mo>&#8658;</mo></math> a<sup>2</sup> = 49 &rArr; a = 7 cm<br>Volume of cube = <math display=\"inline\"><msup><mrow><mi>a</mi></mrow><mrow><mn>3</mn></mrow></msup></math>= 7 &times; 7 &times; 7 = 343 cm<sup>3</sup></p>",
                    solution_hi: "<p>25.(b) घन का कुल पृष्ठीय क्षेत्रफल = 6<math display=\"inline\"><msup><mrow><mi>a</mi></mrow><mrow><mn>2</mn></mrow></msup></math><br><math display=\"inline\"><mo>&#8658;</mo></math> 294 = 6a<sup>2</sup><br><math display=\"inline\"><mo>&#8658;</mo></math> a<sup>2</sup> = 49 &rArr; a = 7 cm<br>घन का आयतन = <math display=\"inline\"><msup><mrow><mi>a</mi></mrow><mrow><mn>3</mn></mrow></msup></math>= 7 &times; 7 &times; 7 = 343 cm<sup>3</sup></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>