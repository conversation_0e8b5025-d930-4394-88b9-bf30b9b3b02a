<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Smart Test Interface</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        .question-nav{position:fixed;top:70px;right:0;height:calc(100vh - 70px);width:320px;transition:all .3s;z-index:100;display:flex;flex-direction:column}
        .question-content{margin-right:0;transition:all .3s}
        .question-box{width:40px;height:40px;margin:5px;cursor:pointer}
        .attempted{background-color:#3498db!important;color:white}
        .correct{background-color:#2ecc71!important;color:white}
        .incorrect{background-color:#e74c3c!important;color:white}
        .current{border:3px solid #f39c12!important}
        #question-boxes{overflow-y:auto;max-height:calc(100vh - 150px);padding-bottom:15px}
        @media (max-width:768px){.question-nav{width:100%;right:-100%}.question-content{margin-right:0}}
    </style>
</head>
<body>
    <div class="container-fluid p-0">
        <!-- Header with controls -->
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary sticky-top">
            <div class="container-fluid">
                <a class="navbar-brand" href="#">
                    <i class="fas fa-graduation-cap me-2"></i>Smart Test
                </a>
                <!-- Section selector -->
                <div class="navbar-nav me-auto section-tabs">
                    <select class="form-select" id="section-selector" onchange="switchSection(this.value)">
                        <option value="all">All Sections</option>
                    </select>
                </div>
                <div class="d-flex align-items-center">
                    <!-- Timer -->
                    <div class="bg-light text-primary rounded px-3 py-2 me-2 fw-bold" id="timer">
                        <i class="far fa-clock me-2"></i><span id="timer-display">100:00</span>
                    </div>
                    <!-- Submit button -->
                    <button class="btn btn-success me-2" id="submit-btn" onclick="submitTest()">
                        <i class="fas fa-check me-1"></i> Submit
                    </button>
                    <!-- Language toggle -->
                    <button class="btn btn-light me-2" id="lang-btn" onclick="toggleLanguage()">
                        <i class="fas fa-language"></i>
                    </button>
                    <!-- Menu toggle for mobile -->
                    <button class="btn btn-light d-lg-none" id="menu-toggle" onclick="toggleMenu()">
                        <i class="fas fa-th"></i>
                    </button>
                </div>
            </div>
        </nav>
        <div class="row m-0">
            <!-- Main content area -->
            <div class="col p-0 question-content" id="question-content">
                <div class="container py-4">
                    <!-- Question navigation -->
                    <div class="d-flex justify-content-between mb-4">
                        <button class="btn btn-outline-primary" onclick="prevQuestion()">
                            <i class="fas fa-chevron-left me-1"></i> Previous
                        </button>
                        <div class="fw-bold" id="question-counter">Question 1 of 100</div>
                        <button class="btn btn-outline-primary" onclick="nextQuestion()">
                            Next <i class="fas fa-chevron-right ms-1"></i>
                        </button>
                    </div>
                    <!-- Questions will be displayed here -->
                    <div id="questions-container"></div>
                </div>
            </div>
            <!-- Question navigation sidebar -->
            <div class="bg-light question-nav" id="question-nav">
                <div class="p-3">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h5 class="m-0">Questions</h5>
                        <div>
                            <span class="badge bg-secondary me-1" id="attempted-count">0</span>
                            <span class="badge bg-secondary" id="total-count">100</span>
                            <button class="btn btn-sm btn-outline-secondary ms-2 d-lg-none" onclick="toggleMenu()">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>
                    <div class="d-flex flex-wrap" id="question-boxes"></div>
                </div>
            </div>
        </div>
        <!-- Results modal -->
        <div class="modal fade" id="results-modal" tabindex="-1" aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content">
                    <div class="modal-header bg-primary text-white">
                        <h5 class="modal-title">Test Results</h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row text-center">
                            <div class="col-6 mb-3">
                                <div class="p-3 bg-primary bg-opacity-10 rounded">
                                    <h3 id="score-value">0</h3>
                                    <div class="small text-muted">Score</div>
                                </div>
                            </div>
                            <div class="col-6 mb-3">
                                <div class="p-3 bg-success bg-opacity-10 rounded">
                                    <h3 id="correct-value">0</h3>
                                    <div class="small text-muted">Correct</div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="p-3 bg-danger bg-opacity-10 rounded">
                                    <h3 id="incorrect-value">0</h3>
                                    <div class="small text-muted">Incorrect</div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="p-3 bg-secondary bg-opacity-10 rounded">
                                    <h3 id="unattempted-value">0</h3>
                                    <div class="small text-muted">Unattempted</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-primary" onclick="reviewTest()">Review Test</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 100 * 60;
        let resultsModal;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            resultsModal = new bootstrap.Modal(document.getElementById('results-modal'));
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 768) {
                document.getElementById('question-content').style.marginRight = '320px';
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["6"] = {
                name: "Reasoning",
                start: 0,
                end: 23
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="6">Reasoning</option>`;
            sections["18"] = {
                name: "General Awareness",
                start: 24,
                end: 48
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="18">General Awareness</option>`;
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 49,
                end: 73
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["7"] = {
                name: "English",
                start: 74,
                end: 98
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="7">English</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 99,
                end: 99
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "6",
                    question_en: "<p>1. In a certain code language, \'PBI\' is coded as \'27\' and \'ZEBRA\' is coded as \'125\'. What is the code for \'BIRD\' in the given code language?</p>",
                    question_hi: "<p>1. एक निश्चित कूट भाषा में, \'PBI\' को \'27\' लिखा जाता है और \'ZEBRA\' को \'125\' लिखा जाता है। उस कूट भाषा में \'BIRD\' को कैसे लिखा जाएगा?</p>",
                    options_en: [
                        "<p>37</p>",
                        "<p>64</p>",
                        "<p>25</p>",
                        "<p>49</p>"
                    ],
                    options_hi: [
                        "<p>37</p>",
                        "<p>64</p>",
                        "<p>25</p>",
                        "<p>49</p>"
                    ],
                    solution_en: "<p>1.(b) <strong>Logic :</strong>- (Number of letter)&sup3;<br>PBI :- (3)&sup3; = 27<br>ZEBRA :- (5)&sup3; = 125<br>Similarly,<br>BIRD :- (4)&sup3; = 64</p>",
                    solution_hi: "<p>1.(b) <strong>तर्क :-</strong> (अक्षरों की संख्या)&sup3;<br>PBI :- (3)&sup3; = 27<br>ZEBRA :- (5)&sup3; = 125<br>इसी प्रकार,<br>BIRD :- (4)&sup3; = 64</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "6",
                    question_en: "<p>2. Based on the alphabetical order, three of the following four letter-clusters are alike in a&nbsp;certain way and thus form a group. Which letter-cluster does not belong to that group?&nbsp;(Note: The odd one out is not based on the number of consonants/vowels or their&nbsp;position in the letter cluster.)</p>",
                    question_hi: "<p>2. वर्णमाला क्रम के आधार पर, निम्नलिखित चार अक्षर-समूहों में से तीन एक निश्चित प्रकार से समान हैं&nbsp;और इस प्रकार एक समूह बनाते हैं। कौन-सा अक्षर-समूह उस समूह से संबंधित नहीं है?&nbsp;(ध्यान दें: असंगत अक्षर-समूह, उस अक्षर-समूह मेें व्यंजन/स्वरों की संख्या या उनके स्थान पर आधारित नहीं है।)</p>",
                    options_en: [
                        " DGK",
                        " ILO",
                        " NQU",
                        " JMQ"
                    ],
                    options_hi: [
                        " DGK",
                        " ILO",
                        " NQU",
                        " JMQ"
                    ],
                    solution_en: "<p>2.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742268456341.png\" alt=\"rId4\" width=\"85\" height=\"50\">&nbsp; &nbsp;,&nbsp; &nbsp;<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742268456492.png\" alt=\"rId5\" width=\"85\" height=\"50\">&nbsp; &nbsp;,&nbsp; &nbsp;<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742268456613.png\" alt=\"rId6\" width=\"85\" height=\"50\"><br>but <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742268456735.png\" alt=\"rId7\" width=\"83\" height=\"50\"></p>",
                    solution_hi: "<p>2.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742268456341.png\" alt=\"rId4\" width=\"85\" height=\"50\">&nbsp; &nbsp;,&nbsp; &nbsp;<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742268456492.png\" alt=\"rId5\" width=\"85\" height=\"50\">&nbsp; &nbsp;,&nbsp; &nbsp;<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742268456613.png\" alt=\"rId6\" width=\"85\" height=\"50\"><br>लेकिन<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742268456735.png\" alt=\"rId7\" width=\"83\" height=\"50\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "6",
                    question_en: "<p>3. A square sheet of paper is folded along the dotted line successively along the directions shown and is then punched in the last. How would the paper look when unfolded ?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742268456967.png\" alt=\"rId8\" width=\"236\" height=\"70\"> </p>",
                    question_hi: "<p>3. कागज की एक वर्गाकार शीट को दर्शाई गई दिशाओ में बिंदीदार रेखा पर अनुक्रमशः मोड़ा जाता है और आखिर में उसमें छेद किया जाता है। खोलने पर यह कागज कैसा दिखाई देगा?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742268456967.png\" alt=\"rId8\" width=\"236\" height=\"70\"></p>",
                    options_en: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742268457174.png\" alt=\"rId9\" width=\"64\" height=\"60\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742268457300.png\" alt=\"rId10\" width=\"67\" height=\"60\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742268457446.png\" alt=\"rId11\" width=\"64\" height=\"60\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742268457592.png\" alt=\"rId12\" width=\"63\" height=\"60\"></p>"
                    ],
                    options_hi: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742268457174.png\" alt=\"rId9\" width=\"63\" height=\"60\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742268457300.png\" alt=\"rId10\" width=\"67\" height=\"60\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742268457446.png\" alt=\"rId11\" width=\"60\" height=\"60\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742268457592.png\" alt=\"rId12\" width=\"63\" height=\"60\"></p>"
                    ],
                    solution_en: "<p>3.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742268457733.png\" alt=\"rId13\" width=\"62\" height=\"60\"></p>",
                    solution_hi: "<p>3.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742268457733.png\" alt=\"rId13\" width=\"62\" height=\"60\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "6",
                    question_en: "<p>4. In a certain code language, \'served her people\' is written as \'vt yc df\', and \'dinner is served\' is coded as \'kp df hu\'. How will \'served\' be coded in that language?</p>",
                    question_hi: "<p>4. एक निश्चित कूट भाषा में \'served her people\' को \'vt yc df\' के रूप में कूटबद्ध किया जाता है और \'dinner is served\' को \'kp df hu\' के रूप में कूटबद्ध किया जाता है। उस भाषा में \'served\' को किस प्रकार कूटबद्ध किया जाएगा?</p>",
                    options_en: [
                        "<p>kp</p>",
                        "<p>df</p>",
                        "<p>ус</p>",
                        "<p>vt</p>"
                    ],
                    options_hi: [
                        "<p>kp</p>",
                        "<p>df</p>",
                        "<p>ус</p>",
                        "<p>vt</p>"
                    ],
                    solution_en: "<p>4.(b) served her people &rarr; vt yc df&hellip;&hellip; (i)<br>dinner is served &rarr; kp df hu&hellip;&hellip;. (ii)<br>From (i) and (ii) &lsquo;served&rsquo; and &lsquo;df&rsquo; are common. The code of &lsquo;served&rsquo; = &lsquo;df&rsquo;</p>",
                    solution_hi: "<p>4.(b) served her people &rarr; vt yc df&hellip;&hellip; (i)<br>dinner is served &rarr; kp df hu&hellip;&hellip;. (ii)<br>(i) और (ii) से \'served\' और \'df\' उभयनिष्ठ हैं। \'served\' का कोड = \'df\'</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "6",
                    question_en: "<p>5. Select the option that is related to the fifth letter-cluster in the same way as the second letter-cluster is related to the first letter-cluster and the fourth letter-cluster is related to the third letter-cluster.<br>ABUSE : ASUBE :: BEGIN : BIGEN :: OTHER : ?</p>",
                    question_hi: "<p>5. उस विकल्प का चयन कीजिए, जो पाँचवें अक्षर-समूह से उसी प्रकार संबंधित है, जिस प्रकार दूसरा अक्षर-समूह, पहले अक्षर-समूह से संबंधित है और चौथा अक्षर-समूह, तीसरे अक्षर-समूह से संबंधित है।<br>ABUSE : ASUBE :: BEGIN : BIGEN :: OTHER : ?</p>",
                    options_en: [
                        "<p>OTHRE</p>",
                        "<p>ORTHE</p>",
                        "<p>OHTER</p>",
                        "<p>OEHTR</p>"
                    ],
                    options_hi: [
                        "<p>OTHRE</p>",
                        "<p>ORTHE</p>",
                        "<p>OHTER</p>",
                        "<p>OEHTR</p>"
                    ],
                    solution_en: "<p>5.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742268457867.png\" alt=\"rId14\" width=\"87\" height=\"60\">&nbsp; &nbsp;,&nbsp; &nbsp;<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742268457979.png\" alt=\"rId15\" width=\"84\" height=\"60\"><br>Similarly,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742268458086.png\" alt=\"rId16\" width=\"84\" height=\"60\"></p>",
                    solution_hi: "<p>5.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742268457867.png\" alt=\"rId14\" width=\"87\" height=\"60\">&nbsp; &nbsp;,&nbsp; &nbsp;<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742268457979.png\" alt=\"rId15\" width=\"84\" height=\"60\"><br>इसी प्रकार,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742268458086.png\" alt=\"rId16\" width=\"84\" height=\"60\"></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "6",
                    question_en: "<p>6. What should come in place of the question mark (?) in the given series ?<br>23, 44, 65, 86, ?, 128</p>",
                    question_hi: "<p>6. दी गई श्रृंखला में प्रश्न चिह्न (?) के स्थान पर क्या आना चाहिए?<br>23, 44, 65, 86, ?, 128</p>",
                    options_en: [
                        "<p>107</p>",
                        "<p>109</p>",
                        "<p>103</p>",
                        "<p>105</p>"
                    ],
                    options_hi: [
                        "<p>107</p>",
                        "<p>109</p>",
                        "<p>103</p>",
                        "<p>105</p>"
                    ],
                    solution_en: "<p>6.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742268458233.png\" alt=\"rId17\" width=\"206\" height=\"40\"></p>",
                    solution_hi: "<p>6.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742268458233.png\" alt=\"rId17\" width=\"206\" height=\"40\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "6",
                    question_en: "<p>7. Identify the figure given in the options which when put in place of ? will logically complete the series.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742268458405.png\" alt=\"rId18\" width=\"243\" height=\"50\"> </p>",
                    question_hi: "<p>7. विकल्पों में दी गई उस आकृति की पहचान कीजिए जिसे ? के स्थान पर रखने पर श्रृंखला तर्कसंगत रूप से पूर्ण हो जाएगी।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742268458405.png\" alt=\"rId18\" width=\"243\" height=\"50\"></p>",
                    options_en: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742268458524.png\" alt=\"rId19\" width=\"50\" height=\"50\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742268458644.png\" alt=\"rId20\" width=\"58\" height=\"60\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742268458771.png\" alt=\"rId21\" width=\"60\" height=\"60\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742268458944.png\" alt=\"rId22\" width=\"59\" height=\"60\"></p>"
                    ],
                    options_hi: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742268458524.png\" alt=\"rId19\" width=\"59\" height=\"60\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742268458644.png\" alt=\"rId20\" width=\"59\" height=\"60\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742268458771.png\" alt=\"rId21\" width=\"59\" height=\"60\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742268458944.png\" alt=\"rId22\" width=\"59\" height=\"60\"></p>"
                    ],
                    solution_en: "<p>7.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742268458771.png\" alt=\"rId21\" width=\"68\" height=\"70\"></p>",
                    solution_hi: "<p>7.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742268458771.png\" alt=\"rId21\" width=\"68\" height=\"70\"></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "6",
                    question_en: "<p>8. Select the option that represents the letters that, when sequentially placed from left to right in the blanks, will complete the letter series.<br>_ R _ _ A R _ O _ _ G O A _ G O</p>",
                    question_hi: "<p>8. उस विकल्प का चयन करें, जो उन अक्षरों का प्रतिनिधित्व करता है, जो रिक्त स्थानों में क्रमिक रूप से बाएँ से दाएँ रखे जाने पर दी गई अक्षर शृंखला को पूरा करेंगे। <br>_ R _ _ A R _ O _ _ G O A _ G O</p>",
                    options_en: [
                        "<p>A G O G R A A</p>",
                        "<p>A G O G A R R</p>",
                        "<p>A A G O G R R</p>",
                        "<p>A G G O A R R</p>"
                    ],
                    options_hi: [
                        "<p>A G O G R A A</p>",
                        "<p>A G O G A R R</p>",
                        "<p>A A G O G R R</p>",
                        "<p>A G G O A R R</p>"
                    ],
                    solution_en: "<p>8.(b)<br><strong><span style=\"text-decoration: underline;\">A</span> </strong>R <strong><span style=\"text-decoration: underline;\">G O</span></strong> / A R <strong><span style=\"text-decoration: underline;\">G</span></strong> O / <strong><span style=\"text-decoration: underline;\">A R</span></strong> G O / A <strong><span style=\"text-decoration: underline;\">R</span></strong> G O</p>",
                    solution_hi: "<p>8.(b)<br><strong><span style=\"text-decoration: underline;\">A</span> </strong>R <strong><span style=\"text-decoration: underline;\">G O</span></strong> / A R <strong><span style=\"text-decoration: underline;\">G</span></strong> O / <strong><span style=\"text-decoration: underline;\">A R</span></strong> G O / A <strong><span style=\"text-decoration: underline;\">R</span></strong> G O</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "6",
                    question_en: "<p>9. Three statements are followed by conclusions numbered I, II. You have to consider these statements to be true, even if they seem to be at variance with commonly known facts. Decide which of the given conclusions logically follow/s from the given statement. <br><strong>Statements :</strong> <br>All jams are butter. <br>Some nibs are ink. <br>Some nibs are butter. <br><strong>Conclusion (I) :</strong> Some ink is butter. <br><strong>Conclusion (II) : </strong>All jams are nibs.</p>",
                    question_hi: "<p>9. तीन कथनों के बाद I, II क्रमांकित निष्कर्ष दिए गए हैं। आपको इन कथनों को सत्य मानना होगा, चाहे वे समान्यतः ज्ञात तथ्यों सेअलग प्रतीत होते हों। आपको निश्चय करना है कि कौन-सा/कौन-से निष्कर्ष तार्किक रूप से दिए गए कथन/कथनों के अनुसार है/हैं।&nbsp;<br><strong>कथन :</strong> <br>सभी जैम, मक्खन हैं। <br>कुछ निब, स्याही हैं। <br>कुछ निब, मक्खन हैं। <br><strong>निष्कर्ष (I) :</strong> कुछ स्याही, मक्खन है। <br><strong>निष्कर्ष (II) : </strong>सभी जैम, निब हैं</p>",
                    options_en: [
                        "<p>Only conclusion (II) follows.</p>",
                        "<p>Only conclusion (I) follows.</p>",
                        "<p>Neither conclusion (I) nor (II) follows.</p>",
                        "<p>Both conclusions (I) and (II) follow</p>"
                    ],
                    options_hi: [
                        "<p>केवल निष्कर्ष (II) कथनों के अनुसार है।</p>",
                        "<p>केवल निष्कर्ष (I) कथनों के अनुसार है।</p>",
                        "<p>न तो निष्कर्ष (I) और न ही (II) कथनों के अनुसार है</p>",
                        "<p>दोनों निष्कर्ष (I) और (II) कथनों के अनुसार हैं।</p>"
                    ],
                    solution_en: "<p>9.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742268459137.png\" alt=\"rId23\" width=\"227\" height=\"70\"><br>Neither conclusion I nor II follow.</p>",
                    solution_hi: "<p>9.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742268459427.png\" alt=\"rId24\" width=\"226\" height=\"70\"><br>न तो निष्कर्ष I और न ही II अनुसरण करता है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "6",
                    question_en: "<p>10. What should come in place of ? in the given series based on the English alphabetical order?<br>ZRJ, EUK, JXL, ?, TDN</p>",
                    question_hi: "<p>10. अंग्रेजी वर्णमाला क्रम के आधार पर दी गई श्रृंखला में &lsquo;?&rsquo; के स्थान पर क्या आना चाहिए?<br>ZRJ, EUK, JXL, ?, TDN</p>",
                    options_en: [
                        "<p>OAM</p>",
                        "<p>HST</p>",
                        "<p>WNE</p>",
                        "<p>QHR</p>"
                    ],
                    options_hi: [
                        "<p>OAM</p>",
                        "<p>HST</p>",
                        "<p>WNE</p>",
                        "<p>QHR</p>"
                    ],
                    solution_en: "<p>10.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742268459531.png\" alt=\"rId25\" width=\"287\" height=\"100\"></p>",
                    solution_hi: "<p>10.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742268459531.png\" alt=\"rId25\" width=\"287\" height=\"100\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "6",
                    question_en: "<p>11. In a certain code language, <br>&lsquo;A + B&rsquo; means &lsquo;A is the sister of B&rsquo;, <br>&lsquo;A &ndash; B&rsquo; means &lsquo;A is the brother of B&rsquo;, <br>&lsquo;A &times; B&rsquo; means &lsquo;A is the father of B&rsquo;, <br>&lsquo;A &divide; B&rsquo; means &lsquo;A is the daughter of B&rsquo;. <br>Based on the above, how is K related to P if &lsquo;J &divide; K + L &times; O &ndash; P&rsquo;?</p>",
                    question_hi: "<p>11. एक निश्चित कूट भाषा में, <br>&lsquo;A + B&rsquo; का अर्थ है, &lsquo;A, B की बहन है&rsquo;, <br>&lsquo;A &ndash; B&rsquo; का अर्थ है, &lsquo;A, B का भाई है&rsquo;, <br>&lsquo;A &times; B&rsquo; का अर्थ है, &lsquo;A , B के पिता है&rsquo;, <br>&lsquo;A &divide; B&rsquo; का अर्थ है, &lsquo;A, B की पुत्री है&rsquo;. <br>उपरोक्त के आधार पर, यदि&lsquo; J &divide; K + L &times; O &ndash; P&rsquo; है, तो K का P से क्या संबंध है?</p>",
                    options_en: [
                        "<p>Mother&rsquo;s sister</p>",
                        "<p>Mother</p>",
                        "<p>Sister</p>",
                        "<p>Father&rsquo;s sister</p>"
                    ],
                    options_hi: [
                        "<p>माता की बहन</p>",
                        "<p>माता</p>",
                        "<p>बहन</p>",
                        "<p>पिता की बहन</p>"
                    ],
                    solution_en: "<p>11.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742268459640.png\" alt=\"rId26\" width=\"197\" height=\"100\"><br>K is the sister of P&rsquo;s father.</p>",
                    solution_hi: "<p>11.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742268459640.png\" alt=\"rId26\" width=\"197\" height=\"100\"><br>K, P के पिता की बहन है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "6",
                    question_en: "<p>12. 765 is related to 876 following a certain logic. Following the same logic, 682 is related&nbsp;to 793. To which of the following is 587 related, following the same logic?&nbsp;(NOTE : Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g. 13 ; Operations on 13 such as adding/subtracting/multiplying etc. to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed.)</p>",
                    question_hi: "<p>12. एक निश्चित तर्क का अनुसरण करते हुए 765 , 876 से संबंधित है। उसी तर्क का अनुसरण करते हुए, 682, 793 से संबंधित है। उसी तर्क का अनुसरण करते हुए, 587 निम्नलिखित में से किससे संबंधित है?&nbsp;(ध्यान दें : संख्याओं को उनके घटक अंकों में अलग-अलग किए बिना पूर्ण संख्याओं पर संक्रियाएं की जानी चाहिए। उदाहरण के लिए संख्या 13 - संख्या 13 पर संक्रियाएं जैसे 13 को जोड़ना/घटाना/गुणा करना आदि किया जा सकता है। 13 को 1 और 3 में अलग-अलग करने की और फिर 1 और 3 पर गणितीय संक्रियाएं करने की अनुमति नहीं है।)</p>",
                    options_en: [
                        "<p>675</p>",
                        "<p>543</p>",
                        "<p>698</p>",
                        "<p>798</p>"
                    ],
                    options_hi: [
                        "<p>675</p>",
                        "<p>543</p>",
                        "<p>698</p>",
                        "<p>798</p>"
                    ],
                    solution_en: "<p>12.(c)<strong> Logic :-</strong> (1st number - 111) = 2nd number<br>(876 , 765) :- (876 - 111) = 765<br>(793, 682) :- (793 - 111) = 682<br>Similarly,<br>(698 ,?) :- (698 - 111) = 587</p>",
                    solution_hi: "<p>12.(c) <strong>तर्क :-</strong> (पहली संख्या - 111) = दूसरी संख्या<br>(876 , 765) :- (876 - 111) = 765<br>(793, 682) :- (793 - 111) = 682<br>इसी प्रकार,<br>(698 ,?) :- (698 - 111) = 587</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "6",
                    question_en: "<p>13. Two different positions of the same dice with faces T, O, B, L, Y and V are shown below. Select the letter that will be on the face opposite to the one having L.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742268459749.png\" alt=\"rId27\" width=\"133\" height=\"70\"></p>",
                    question_hi: "<p>13. T, O, B, L, Y और V फलकों वाले एक ही पासे की दो अलग-अलग स्थितियाँ नीचे दिखाई गई हैं। उस अक्षर का चयन कीजिए जो L अक्षर वाले फलक के विपरीत फलक पर होगा।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742268459749.png\" alt=\"rId27\" width=\"133\" height=\"70\"></p>",
                    options_en: [
                        "<p>Y</p>",
                        "<p>B</p>",
                        "<p>V</p>",
                        "<p>O</p>"
                    ],
                    options_hi: [
                        "<p>Y</p>",
                        "<p>B</p>",
                        "<p>V</p>",
                        "<p>O</p>"
                    ],
                    solution_en: "<p>13.(d) From both the dice the opposite face are <br>B &harr; T , O &harr; L</p>",
                    solution_hi: "<p>13.(d) दोनों पासों के विपरीत फलक हैं<br>B &harr; T , O &harr; L</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "6",
                    question_en: "<p>14. Select the option in which the given figure is embedded. (Rotation is NOT allowed.)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742268459869.png\" alt=\"rId28\" width=\"101\" height=\"55\"></p>",
                    question_hi: "<p>14. उस विकल्प का चयन कीजिए जिसमें दी गई आकृति निहित है। (घूर्णन की अनुमति नहीं है)।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742268459869.png\" alt=\"rId28\" width=\"101\" height=\"55\"></p>",
                    options_en: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742268459958.png\" alt=\"rId29\" width=\"65\" height=\"55\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742268460068.png\" alt=\"rId30\" width=\"85\" height=\"50\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742268460186.png\" alt=\"rId31\" width=\"63\" height=\"50\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742268460280.png\" alt=\"rId32\" width=\"46\" height=\"56\"></p>"
                    ],
                    options_hi: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742268459958.png\" alt=\"rId29\" width=\"64\" height=\"55\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742268460068.png\" alt=\"rId30\" width=\"85\" height=\"50\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742268460186.png\" alt=\"rId31\" width=\"63\" height=\"50\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742268460280.png\" alt=\"rId32\" width=\"46\" height=\"56\"></p>"
                    ],
                    solution_en: "<p>14.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742268460407.png\" alt=\"rId33\" width=\"66\" height=\"50\"></p>",
                    solution_hi: "<p>14.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742268460407.png\" alt=\"rId33\" width=\"66\" height=\"50\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "6",
                    question_en: "<p>15. Six people A, B, C, D, E and F are sitting around a circular table, facing the centre (but not necessarily in the same order). E is sitting second to the left of C. C is an immediate neighbour of both A and B. F is sitting third to the right of A.&nbsp;How many people are sitting between D and C when counted from the left of D?</p>",
                    question_hi: "<p>15. छह व्यक्ति A, B, C, D, E और F एक वृत्ताकार मेज के चारों ओर केंद्र की ओर मुख करके बैठे हैं (परंतु जरूरी नहीं कि वे इसी क्रम में बैठे हों)। E, C के बाएँ से दूसरे स्थान पर बैठा है। A और B दोनों का निकटतम पड़ोसी C है। F, A के दाएँ से तीसरे स्थान पर बैठा है। D के बाएँ से गिनती करने पर D और C के बीच में कितने व्यक्ति बैठे हैं?</p>",
                    options_en: [
                        "<p>3</p>",
                        "<p>4</p>",
                        "<p>2</p>",
                        "<p>1</p>"
                    ],
                    options_hi: [
                        "<p>3</p>",
                        "<p>4</p>",
                        "<p>2</p>",
                        "<p>1</p>"
                    ],
                    solution_en: "<p>15.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742268460524.png\" alt=\"rId34\" width=\"112\" height=\"120\"><br>Only 2 people sit between D and C.</p>",
                    solution_hi: "<p>15.(c) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742268460524.png\" alt=\"rId34\" width=\"112\" height=\"120\"><br>D और C के बीच केवल 2 व्यक्ति बैठे हैं।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "6",
                    question_en: "<p>16. What will come in the place of &lsquo;?&rsquo; in the following equation, if &lsquo;+&rsquo; and &lsquo;-&rsquo; are interchanged and &lsquo;&times;&rsquo; and &lsquo;&divide;&rsquo; are interchanged?<br>32 &times; 8 &divide; 4 - 6 + 15 = ?</p>",
                    question_hi: "<p>16. यदि &lsquo;+&rsquo; और &lsquo;-&rsquo; को आपस में बदल दिया जाए तथा \'&times;\' और \'&divide;\' को आपस में बदल दिया जाए तो निम्नलिखित समीकरण में (?) के स्थान पर क्या आएगा?<br>32 &times; 8 &divide; 4 - 6 + 15 = ?</p>",
                    options_en: [
                        "<p>5</p>",
                        "<p>15</p>",
                        "<p>13</p>",
                        "<p>7</p>"
                    ],
                    options_hi: [
                        "<p>5</p>",
                        "<p>15</p>",
                        "<p>13</p>",
                        "<p>7</p>"
                    ],
                    solution_en: "<p>16.(d) <strong>Given:-</strong> 32 &times; 8 <math display=\"inline\"><mo>&#247;</mo></math> 4 - 6 + 15<br>As per given instruction after interchanging the &lsquo;+&rsquo; and &lsquo;-&rsquo; and &lsquo;&times;&rsquo; and &lsquo;&divide;&rsquo; we get,<br>32 &divide;&nbsp;8 &times; 4 + 6 - 15<br>4 &times; 4 + 6 - 15 <br>22 - 15 = 7</p>",
                    solution_hi: "<p>16.(d) दिया गया है:- 32 &times; 8 &divide;&nbsp;4 - 6 + 15<br>दिए गए निर्देश के अनुसार \'+\' और \'-\' तथा \'&times;\' और \'&divide;\' को आपस में बदलने के बाद हमें प्राप्त होता है,<br>32 &divide;&nbsp;8 &times; 4 + 6 - 15<br>4 &times; 4 + 6 - 15 <br>22 - 15 = 7</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "6",
                    question_en: "<p>17. Three of the following four options are alike in a certain way and thus form a group. Which is the one that does NOT belong to that group?&nbsp;(NOTE : Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g. 13 &ndash; Operations on 13 such as adding/subtracting/multiplying etc. to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed.)</p>",
                    question_hi: "<p>17. निम्नलिखित चार विकल्पों में से तीन किसी न किसी रूप में समान हैं और इस प्रकार, एक समूह बनाते हैं। कौन-सा विकल्प उस समूह से संबंधित नहीं है?&nbsp;(ध्यान दें: संख्याओं को उनके घटक अंकों में तोड़े बिना, संक्रियाएं पूर्ण संख्याओं पर की जानी चाहिए। उदाहरण के लिए 13 को लें- संख्या 13 में की जाने वाली संक्रिया, जैसे कि जोड़ना/घटाना/गुणा करना केवल 13 में किया जा सकता है। 13 को अंक 1 और 3 में तोड़ना और फिर 1 और 3 के साथ गणितीय संक्रियाएं करने की अनुमति नहीं है।)</p>",
                    options_en: [
                        "<p>86 &ndash; 41 &ndash; 38</p>",
                        "<p>118 &ndash; 93 &ndash; 161</p>",
                        "<p>33 &ndash; 15 &ndash; 12</p>",
                        "<p>52 &ndash; 36 &ndash; 56</p>"
                    ],
                    options_hi: [
                        "<p>86 &ndash; 41 &ndash; 38</p>",
                        "<p>118 &ndash; 93 &ndash; 161</p>",
                        "<p>33 &ndash; 15 &ndash; 12</p>",
                        "<p>52 &ndash; 36 &ndash; 56</p>"
                    ],
                    solution_en: "<p>17.(a)<strong> logic:- </strong>(first number +third number) &divide; 3 = second number<br>(b) (118 + 161) &divide; 3 = 93<br>(c) (33 + 12) &divide; 3 = 15<br>(d) (52 + 56) &divide; 3 = 36<br>But,<br>(a) (86 + 38) &divide; 3 &ne; 41</p>",
                    solution_hi: "<p>17.(a) <strong>logic:- </strong>(पहली संख्या + तीसरी संख्या) &divide; 3 = दूसरी संख्या<br>(b) (118 + 161) &divide; 3 = 93<br>(c) (33 + 12) &divide; 3 = 15<br>(d) (52 + 56) &divide; 3 = 36<br>लेकिन,<br>(a) (86 + 38) &divide; 3 &ne; 41</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "6",
                    question_en: "<p>18. The position(s) of how many letters will remain unchanged if all the letters in the word &lsquo;ENTOMB&rsquo; are arranged in English alphabetical order?</p>",
                    question_hi: "<p>18. यदि &lsquo;ENTOMB&rsquo; शब्द के सभी अक्षरों को अंग्रेजी वर्णमाला क्रम में व्यवस्थित किया जाए, तो कितने अक्षरों की स्थिति में कोई बदलाव नहीं होगा?</p>",
                    options_en: [
                        "<p>None</p>",
                        "<p>Two</p>",
                        "<p>One</p>",
                        "<p>Three</p>"
                    ],
                    options_hi: [
                        "<p>कोई भी नहीं</p>",
                        "<p>दो</p>",
                        "<p>एक</p>",
                        "<p>तीन</p>"
                    ],
                    solution_en: "<p>18.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742268460664.png\" alt=\"rId35\" width=\"174\" height=\"90\"><br>None.</p>",
                    solution_hi: "<p>18.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742268460664.png\" alt=\"rId35\" width=\"174\" height=\"90\"><br>कोई भी नहीं</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "6",
                    question_en: "<p>19. In a certain code language, \'ALPHA\' is written as \'MBPBI\' and \'GAMMA\' is written as \'BHMBN\'. How will \'LASER\' be written as in that language ?</p>",
                    question_hi: "<p>19. एक निश्चित कूट भाषा में \'ALPHA\' को \'MBPBI\' लिखा जाता है और \'GAMMA\' को \'BHMBN\' लिखा जाता है। उस भाषा में \'LASER\' को कैसे लिखा जाएगा ?</p>",
                    options_en: [
                        "<p>BNSSF</p>",
                        "<p>BMSFS</p>",
                        "<p>BMSSF</p>",
                        "<p>BMFSS</p>"
                    ],
                    options_hi: [
                        "<p>BNSSF</p>",
                        "<p>BMSFS</p>",
                        "<p>BMSSF</p>",
                        "<p>BMFSS</p>"
                    ],
                    solution_en: "<p>19.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742268460806.png\" alt=\"rId36\" width=\"91\" height=\"70\">&nbsp; &nbsp;,&nbsp; &nbsp;<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742268460941.png\" alt=\"rId37\" width=\"92\" height=\"70\"><br>Similarly,<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742268461053.png\" alt=\"rId38\" width=\"91\" height=\"70\"></p>",
                    solution_hi: "<p>19.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742268461162.png\" alt=\"rId39\" width=\"71\" height=\"70\">&nbsp; &nbsp;,&nbsp; &nbsp;<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742268461256.png\" alt=\"rId40\" width=\"79\" height=\"71\"><br>इसी प्रकार,</p>\n<p><img src=\"data:image/png;base64,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\" width=\"113\" height=\"93\"></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "6",
                    question_en: "<p>20. Select the correct mirror image of the given figure when the mirror is placed at MN as shown below.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742268461503.png\" alt=\"rId42\" width=\"86\" height=\"90\"></p>",
                    question_hi: "<p>20. दी गई आकृति के उस सही दर्पण प्रतिबिंब को चुनिए, जो नीचे दर्शाए गए अनुसार दर्पण को MN पर रखने पर बनेगा।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742268461503.png\" alt=\"rId42\" width=\"77\" height=\"80\"></p>",
                    options_en: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742268461604.png\" alt=\"rId43\" width=\"74\" height=\"25\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742268461696.png\" alt=\"rId44\" width=\"72\" height=\"25\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742268461788.png\" alt=\"rId45\" width=\"78\" height=\"25\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742268461883.png\" alt=\"rId46\" width=\"82\" height=\"25\"></p>"
                    ],
                    options_hi: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742268461604.png\" alt=\"rId43\" width=\"74\" height=\"25\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742268461696.png\" alt=\"rId44\" width=\"72\" height=\"25\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742268461788.png\" alt=\"rId45\" width=\"78\" height=\"25\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742268461883.png\" alt=\"rId46\" width=\"82\" height=\"25\"></p>"
                    ],
                    solution_en: "<p>20.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742268461604.png\" alt=\"rId43\" width=\"74\" height=\"25\"></p>",
                    solution_hi: "<p>20.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742268461604.png\" alt=\"rId43\" width=\"74\" height=\"25\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "6",
                    question_en: "<p>21. The same operation(s) are followed in all the given number pairs except one. Find that odd number pair.&nbsp;(NOTE : Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g. 13 - Operations on 13 such as adding /deleting /multiplying etc., to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed.)</p>",
                    question_hi: "<p>21. दिए गए संख्या युग्मों में से एक को छोड़कर अन्य सभी में समान संक्रिया/संक्रियाओं का अनुसरण किया गया है। वह असंगत संख्या युग्म ज्ञात कीजिए।&nbsp;(नोट : संख्याओं को उसके संघटक अंकों में खंडित किए बिना संक्रियाएँ पूर्णांकों पर की जानी चाहिए। उदाहरण के लिए 13-13 पर की जाने वाली संक्रियाएं जैसे जोड़ना/घटाना/गुणा इत्यादि 13 पर ही की जानी चाहिए। 13 को 1 और 3 में खंडित करना और फिर 1 और 3 पर गणितीय संक्रियाएँ करना वर्जित है।)</p>",
                    options_en: [
                        "<p>40:52</p>",
                        "<p>45:54</p>",
                        "<p>35:42</p>",
                        "<p>55:66</p>"
                    ],
                    options_hi: [
                        "<p>40:52</p>",
                        "<p>45:54</p>",
                        "<p>35:42</p>",
                        "<p>55:66</p>"
                    ],
                    solution_en: "<p>21.(a) <strong>Logic:</strong> except option (a), all option have ratio ( 5 : 6)<br>45:54 <math display=\"inline\"><mo>&#8658;</mo></math>&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>45</mn><mn>9</mn></mfrac></math> :&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>54</mn><mn>9</mn></mfrac></math> = 5 : 6<br>35:42 <math display=\"inline\"><mo>&#8658;</mo></math>&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>35</mn><mn>7</mn></mfrac></math> :&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>42</mn><mn>7</mn></mfrac></math> = 5 : 6<br>55:66 <math display=\"inline\"><mo>&#8658;</mo></math><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>55</mn><mn>11</mn></mfrac></math> :&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>66</mn><mn>11</mn></mfrac></math> = 5 : 6<br>But<br>40:52 <math display=\"inline\"><mo>&#8658;</mo></math><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>40</mn><mn>4</mn></mfrac></math> :&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>52</mn><mn>4</mn></mfrac></math> = 10: 13</p>",
                    solution_hi: "<p>21.(a) <strong>तर्क :</strong> विकल्प (a) को छोड़कर, सभी विकल्पों का अनुपात (5:6) है । <br>45:54 <math display=\"inline\"><mo>&#8658;</mo></math><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>45</mn><mn>9</mn></mfrac></math> :&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>54</mn><mn>9</mn></mfrac></math> = 5 : 6<br>35:42 <math display=\"inline\"><mo>&#8658;</mo></math><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>35</mn><mn>7</mn></mfrac></math> :&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>42</mn><mn>7</mn></mfrac></math> = 5 : 6<br>55:66 <math display=\"inline\"><mo>&#8658;</mo></math><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>55</mn><mn>11</mn></mfrac></math> :&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>66</mn><mn>11</mn></mfrac></math> = 5 : 6<br>लेकिन,<br>40:52 <math display=\"inline\"><mo>&#8658;</mo></math><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>40</mn><mn>4</mn></mfrac></math> :&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>52</mn><mn>4</mn></mfrac></math> = 10: 13</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "6",
                    question_en: "<p>22. Ram is 20 years older than Rahul. &lsquo;n&rsquo; years ago Ram was thrice as old as Rahul. &lsquo;2n&rsquo;&nbsp;years from now the ratio of the ages of Ram and Rahul will be 15 : 11. What is the&nbsp;value of &lsquo;n&rsquo;?</p>",
                    question_hi: "<p>22. राम, राहुल से 20 वर्ष बड़ा है। \'n\' वर्ष पहले राम की आयु, राहुल की आयु से तीन गुना थी। अब से \'2n\'&nbsp;वर्ष बाद राम और राहुल की आयु का अनुपात 15 : 11 होगा। \'n\' का मान ज्ञात करें।</p>",
                    options_en: [
                        "<p>10</p>",
                        "<p>30</p>",
                        "<p>20</p>",
                        "<p>15</p>"
                    ],
                    options_hi: [
                        "<p>10</p>",
                        "<p>30</p>",
                        "<p>20</p>",
                        "<p>15</p>"
                    ],
                    solution_en: "<p>22.(d)<br>Let present age of Ram be <math display=\"inline\"><mi>x</mi></math> years then, <br>Present age Rahul be (<math display=\"inline\"><mi>x</mi></math> - 20) years<br>According to the question,<br>Ram is 20 years older than Rahul. &lsquo;n&rsquo; years ago Ram was thrice as old as Rahul<br>(<math display=\"inline\"><mi>x</mi></math> - n) = 3(x - 20 - n)<br>2<math display=\"inline\"><mi>x</mi></math> = 2n + 60 <br><math display=\"inline\"><mi>x</mi></math> = n + 30-------(i)<br>And, &lsquo;2n&rsquo; years from now the ratio of the ages of Ram and Rahul will be 15 : 11<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi mathvariant=\"bold-italic\">x</mi><mi mathvariant=\"bold-italic\">&#160;</mi><mo>+</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mn>2</mn><mi mathvariant=\"bold-italic\">n</mi></mrow><mrow><mi mathvariant=\"bold-italic\">x</mi><mi mathvariant=\"bold-italic\">&#160;</mi><mo>-</mo><mn>20</mn><mi mathvariant=\"bold-italic\">&#160;</mi><mo>+</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mn>2</mn><mi mathvariant=\"bold-italic\">n</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>15</mn><mn>11</mn></mfrac></mstyle></math>&nbsp;-------(ii)<br>Put <math display=\"inline\"><mi>x</mi></math> = n + 30 in equation (ii) we get;<br><math display=\"inline\"><mfrac><mrow><mi>n</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>30</mn><mi mathvariant=\"bold-italic\">&#160;</mi><mo>+</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mn>2</mn><mi mathvariant=\"bold-italic\">n</mi></mrow><mrow><mi>n</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>30</mn><mi mathvariant=\"bold-italic\">&#160;</mi><mo>-</mo><mn>20</mn><mi mathvariant=\"bold-italic\">&#160;</mi><mo>+</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mn>2</mn><mi mathvariant=\"bold-italic\">n</mi></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>15</mn><mn>11</mn></mfrac></mstyle></math><br><math display=\"inline\"><mfrac><mrow><mn>3</mn><mi>n</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>30</mn></mrow><mrow><mn>3</mn><mi>n</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>10</mn></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>15</mn><mn>11</mn></mfrac></mstyle></math><br>33n + 330 = 45n + 150<br>12n = 180<br>n = 15</p>",
                    solution_hi: "<p>22.(d)<br>माना राम की वर्तमान आयु x&nbsp;वर्ष है, <br>राहुल की वर्तमान आयु (x&nbsp;- 20) वर्ष है<br>प्रश्न के अनुसार,<br>राम, राहुल से 20 वर्ष बड़ा है। \'n\' वर्ष पहले राम की उम्र राहुल से तीन गुना थी<br>(x&nbsp;- n) = 3(x - 20 - n)<br>2x&nbsp;= 2n + 60 <br>x = n + 30-------(i)<br>और, अब से \'2n\' वर्ष बाद राम और राहुल की आयु का अनुपात 15:11 होगा<br><math display=\"inline\"><mfrac><mrow><mi mathvariant=\"bold-italic\">x</mi><mi mathvariant=\"bold-italic\">&#160;</mi><mo>+</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mn>2</mn><mi mathvariant=\"bold-italic\">n</mi></mrow><mrow><mi mathvariant=\"bold-italic\">x</mi><mi mathvariant=\"bold-italic\">&#160;</mi><mo>-</mo><mn>20</mn><mi mathvariant=\"bold-italic\">&#160;</mi><mo>+</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mn>2</mn><mi mathvariant=\"bold-italic\">n</mi></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>15</mn><mn>11</mn></mfrac></mstyle></math> -------(ii)<br>समीकरण (ii) में <math display=\"inline\"><mi>x</mi></math> = n + 30 रखने पर हमें प्राप्त होता है;<br><math display=\"inline\"><mfrac><mrow><mi>n</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>30</mn><mi mathvariant=\"bold-italic\">&#160;</mi><mo>+</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mn>2</mn><mi mathvariant=\"bold-italic\">n</mi></mrow><mrow><mi>n</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>30</mn><mi mathvariant=\"bold-italic\">&#160;</mi><mo>-</mo><mn>20</mn><mi mathvariant=\"bold-italic\">&#160;</mi><mo>+</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mn>2</mn><mi mathvariant=\"bold-italic\">n</mi></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>15</mn><mn>11</mn></mfrac></mstyle></math><br><math display=\"inline\"><mfrac><mrow><mn>3</mn><mi>n</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>30</mn></mrow><mrow><mn>3</mn><mi>n</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>10</mn></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>15</mn><mn>11</mn></mfrac></mstyle></math><br>33n + 330 = 45n + 150<br>12n = 180<br>n = 15</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "23",
                    section: "6",
                    question_en: "<p>23. If 23 January 2002 was Wednesday, then what was the day of the week on 26 January 2007 ?</p>",
                    question_hi: "<p>23. यदि 23 जनवरी 2002 को बुधवार था, तो 26 जनवरी 2007 को सप्ताह का कौन-सा दिन रहा होगा?</p>",
                    options_en: [
                        "<p>Saturday</p>",
                        "<p>Sunday</p>",
                        "<p>Friday</p>",
                        "<p>Monday</p>"
                    ],
                    options_hi: [
                        "<p>शनिवार</p>",
                        "<p>रविवार</p>",
                        "<p>शुक्रवार</p>",
                        "<p>सोमवार</p>"
                    ],
                    solution_en: "<p>23.(c) 23 January 2002 was Wednesday. On moving to 2007 the number of odd days =&nbsp;+1 + 2 + 1 + 1 + 1 = 6 , We have reached till 23 January 2007, but we have to go to 26 January, the number of days between = 3. Total number of days = 9. On dividing by 7 remainder = 2 <math display=\"inline\"><mo>&#8658;</mo></math> Wednesday + 2 = Friday.</p>",
                    solution_hi: "<p>23.(c) 23 जनवरी 2002 को बुधवार था। 2007 में जाने पर विषम दिनों की संख्या =&nbsp;+1 + 2 + 1 + 1 + 1 = 6 , हम 23 जनवरी 2007 तक पहुँच चुके हैं, लेकिन हमें 26 जनवरी तक जाना है, बीच के दिनों की संख्या = 3. <br>कुल दिनों की संख्या = 9, 7 से भाग देने पर शेषफल = 2 <math display=\"inline\"><mo>&#8658;</mo></math> बुधवार + 2 = शुक्रवार.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "24",
                    section: "6",
                    question_en: "<p>24. Select the figure from the given options that will replace the question mark (?) in the figure given below to complete the pattern.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742268462036.png\" alt=\"rId47\" width=\"106\" height=\"70\"></p>",
                    question_hi: "<p>24. दिए गए विकल्पों में से उस आकृति का चयन कीजिए जो पैटर्न को पूरा करने के लिए नीचे दी गई आकृति में प्रश्न चिह्न (?) के स्&zwj;थान पर आएगी।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742268462036.png\" alt=\"rId47\" width=\"106\" height=\"70\"></p>",
                    options_en: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742268462156.png\" alt=\"rId48\" width=\"67\" height=\"40\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742268462258.png\" alt=\"rId49\" width=\"62\" height=\"40\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742268462399.png\" alt=\"rId50\" width=\"62\" height=\"40\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742268462495.png\" alt=\"rId51\" width=\"62\" height=\"40\"></p>"
                    ],
                    options_hi: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742268462156.png\" alt=\"rId48\" width=\"68\" height=\"41\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742268462258.png\" alt=\"rId49\" width=\"67\" height=\"40\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742268462399.png\" alt=\"rId50\" width=\"62\" height=\"40\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742268462495.png\" alt=\"rId51\" width=\"61\" height=\"40\"></p>"
                    ],
                    solution_en: "<p>24.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742268462399.png\" alt=\"rId50\" width=\"78\" height=\"50\"></p>",
                    solution_hi: "<p>24.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742268462399.png\" alt=\"rId50\" width=\"78\" height=\"50\"></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "25",
                    section: "18",
                    question_en: "<p>25. Select the set in which the numbers are related in the same way as are the numbers of&nbsp;the following sets.&nbsp;(<strong>NOTE :</strong> Operations should be performed on the whole numbers, without breaking down the numbers into their constituent digits. E.g. 13 &ndash; Operations on 13 such as adding/subtracting/multiplying etc. to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed.)<br>(45, 15, 3)<br>(120, 40, 8)</p>",
                    question_hi: "<p>25. उस समुचय का चयन कीजिए जिसमें संख्याएं ठीक उसी प्रकार संबंधित हैं जिस प्रकार दिए गए समुचयों की संख्याएं संबंधित हैं।&nbsp;(ध्यान दें : संख्याओं को उनके घटक अंकों में अलग-अलग किए बिना पूर्ण संख्याओं पर संक्रियाएं की जानी चाहिए। उदाहरण के लिए संख्या 13 - संख्या 13 पर संक्रियाएं जैसे 13 को जोड़ना/घटाना/गुणा करना आदि किया जा सकता है। 13 को 1 और 3 में अलग-अलग करने की और फिर 1 और 3 पर गणितीय संक्रियाएं करने की अनुमति नहीं है।)<br>(45, 15, 3)<br>(120, 40, 8)</p>",
                    options_en: [
                        "<p>(175, 55, 11)</p>",
                        "<p>(165, 55, 11)</p>",
                        "<p>(165, 55, 9)</p>",
                        "<p>(165, 54, 11)</p>"
                    ],
                    options_hi: [
                        "<p>(175, 55, 11)</p>",
                        "<p>(165, 55, 11)</p>",
                        "<p>(165, 55, 9)</p>",
                        "<p>(165, 54, 11)</p>"
                    ],
                    solution_en: "<p>25.(b) <strong>Logic :- </strong>(1st number - 2nd number) <math display=\"inline\"><mo>&#247;</mo></math> 10 = 3rd number<br>(45, 15, 3) :- (45 - 15)<math display=\"inline\"><mo>&#247;</mo></math> 10 &rArr; (30) &divide; 10 = 3<br>(120 , 40, 8) :- (120 - 40) <math display=\"inline\"><mo>&#247;</mo></math> 10 &rArr; (80) &divide; 10 = 8<br>Similarly,<br>(165, 55, 11) :- (165 - 55)<math display=\"inline\"><mo>&#247;</mo></math> 10 &rArr; 110 &divide; 10 = 11</p>",
                    solution_hi: "<p>25.(b) <strong>तर्क:- </strong>(पहली संख्या - दूसरी संख्या) <math display=\"inline\"><mo>&#247;</mo></math> 10 = तीसरी संख्या<br>(45, 15, 3) :- (45 - 15)<math display=\"inline\"><mo>&#247;</mo></math> 10 &rArr; (30) &divide; 10 = 3<br>(120, 40, 8) :- (120 - 40) <math display=\"inline\"><mo>&#247;</mo></math> 10 &rArr; (80) &divide; 10 = 8<br>इसी प्रकार,<br>(165, 55, 11) :- (165 - 55)<math display=\"inline\"><mo>&#247;</mo></math> 10 &rArr; 110 &divide; 10 = 11</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "26",
                    section: "18",
                    question_en: "<p>26. Sandeep Das is famous for playing which of the following percussion instruments?</p>",
                    question_hi: "<p>26. संदीप दास निम्नलिखित में से किस ताल वाद्य-यंत्र को बजाने के लिए प्रसिद्ध हैं?</p>",
                    options_en: [
                        "<p>Pakhawaj</p>",
                        "<p>Tabla</p>",
                        "<p>Kanjira</p>",
                        "<p>Ghatam</p>"
                    ],
                    options_hi: [
                        "<p>पखावज</p>",
                        "<p>तबला</p>",
                        "<p>कंजीरा</p>",
                        "<p>घटम</p>"
                    ],
                    solution_en: "<p>26.(b) <strong>Tabla.</strong> Sandeep Das was born in Patna (Bihar). He received 59th Grammy Awards (2017). Other famous Indian Maestro: Tabla - Zakir Hussain, Allah Rakha. Pakhawaj - Totaram Sharma, Pt Ayodhya Prasad, Gopal Das. Kanjra - Pudukkotai Dakshinamurthi Pillai. Ghatam -TH Vinayakram, EM Subramaniam.</p>",
                    solution_hi: "<p>26.(b) <strong>तबला।</strong> संदीप दास का जन्म पटना (बिहार) में हुआ था। उन्हें 59वां ग्रैमी अवॉर्ड (2017) से सम्मानित किया गया था। अन्य प्रसिद्ध भारतीय वादक: तबला - जाकिर हुसैन, अल्लाह रक्खा। पखावज - तोताराम शर्मा, पं. अयोध्या प्रसाद, गोपाल दास। कंजीरा - पुदुक्कोट्टई दक्षिणमूर्ति पिल्लई। घटम - टी.एच. विनायकराम, ई.एम. सुब्रमण्यम।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "27",
                    section: "18",
                    question_en: "<p>27. Which country won both the men\'s and women\'s titles at the inaugural Kho Kho World Cup 2025 ?</p>",
                    question_hi: "<p>27. किस देश ने 2025 की पहली खो-खो विश्व कप में पुरुषों और महिलाओं दोनों के खिताब जीते ?</p>",
                    options_en: [
                        "<p>Nepal</p>",
                        "<p>Bangladesh</p>",
                        "<p>India</p>",
                        "<p>Sri Lanka</p>"
                    ],
                    options_hi: [
                        "<p>नेपाल</p>",
                        "<p>बांग्लादेश</p>",
                        "<p>भारत</p>",
                        "<p>श्रीलंका</p>"
                    ],
                    solution_en: "<p>27.(c) <strong>India.</strong> <br>India clinched both the men\'s and women\'s titles at the inaugural Kho Kho World Cup 2025, held from January 13 to 19 at the Indira Gandhi Arena in New Delhi. In the men\'s final, India secured a 54-36 victory over Nepal, while the women\'s team defeated Nepal 78-40</p>",
                    solution_hi: "<p>27. (c) <strong>भारत ।</strong><br>भारत ने नई दिल्ली के इंदिरा गांधी एरिना में 13 से 19 जनवरी तक आयोजित 2025 की पहली खो-खो विश्व कप में पुरुषों और महिलाओं दोनों के खिताब जीते। पुरुषों के फाइनल में, भारत ने नेपाल को 54-36 से हराया, जबकि महिला टीम ने नेपाल को 78-40 से पराजित किया।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "28",
                    section: "18",
                    question_en: "<p>28. Bhangra dance is commonly performed in which of these states?</p>",
                    question_hi: "<p>28. भांगड़ा नृत्य आमतौर पर निम्न में से किस राज्य में किया जाता है ?</p>",
                    options_en: [
                        "<p>Maharashtra</p>",
                        "<p>Uttar Pradesh</p>",
                        "<p>Chhattisgarh</p>",
                        "<p>Punjab</p>"
                    ],
                    options_hi: [
                        "<p>महाराष्ट्र</p>",
                        "<p>उत्तर प्रदेश</p>",
                        "<p>छत्तीसगढ़</p>",
                        "<p>पंजाब</p>"
                    ],
                    solution_en: "<p>28.(d) <strong>Punjab.</strong> Bhangra is performed during Baisakhi. Dances of Punjab: Giddha, Luddi, Julli, Dankara, Sammi, Jaago, Gadka and Kikli.</p>",
                    solution_hi: "<p>28.(d) <strong>पंजाब। </strong>भांगड़ा बैसाखी के दौरान किया जाता है। पंजाब के नृत्य: गिद्दा, लुड्डी, झुम्मर, जूली, डंकारा, सम्मी, जागो, गडका और किकली।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "29",
                    section: "18",
                    question_en: "<p>29. Who authored the book \"Khaki Mein Sthitapragya\", released on 21st September 2024 ?</p>",
                    question_hi: "<p>29. 21 सितंबर 2024 को प्रकाशित पुस्तक \"खाकी में स्थितप्रज्ञ\" के लेखक कौन हैं?</p>",
                    options_en: [
                        "<p>Anil Raturi</p>",
                        "<p>Bob Woodward</p>",
                        "<p>Dr. S. L. Bhyrappa</p>",
                        "<p>Raghuram Rajan</p>"
                    ],
                    options_hi: [
                        "<p>अनिल रतूड़ी</p>",
                        "<p>बॉब वुडवर्ड</p>",
                        "<p>डॉ. एस. एल. भैरप्पा</p>",
                        "<p>रघुराम राजन</p>"
                    ],
                    solution_en: "<p>29.(a) <strong>Anil Raturi.</strong> The memoir, released by Uttarakhand CM Pushkar Singh Dhami, explores Raturi&rsquo;s 35-year career as an IPS officer and the concept of &ldquo;Sthitapragya,&rdquo; maintaining wisdom and composure in adversity.</p>",
                    solution_hi: "<p>29.(a) <strong>अनिल रतूड़ी।</strong> उत्तराखंड के CM पुष्कर सिंह धामी द्वारा जारी संस्मरण में रतूड़ी के एक IPS अधिकारी के रूप में 35 वर्ष के करियर और प्रतिकूल परिस्थितियों में ज्ञान और संयम बनाए रखने की अवधारणा \"स्थितप्रज्ञ\" की खोज की अवधारणा का वर्णन किया गया है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "30",
                    section: "18",
                    question_en: "<p>30. Which of the following festival has \'Jalli Kattu\' as a feature?</p>",
                    question_hi: "<p>30. \'जल्ली कट्टू (Jallikattu)\' निम्नलिखित में से किस त्योहार की एक विशेषता है?</p>",
                    options_en: [
                        "<p>Onam</p>",
                        "<p>Vishu</p>",
                        "<p>Bihu</p>",
                        "<p>Pongal</p>"
                    ],
                    options_hi: [
                        "<p>ओणम</p>",
                        "<p>विशु</p>",
                        "<p>बिहु</p>",
                        "<p>पोंगल</p>"
                    ],
                    solution_en: "<p>30.(d)<strong> Pongal. </strong>It is a harvest festival celebrated by the Tamil community. Jallikattu is a traditional sport of Tamil Nadu that is celebrated on the third day of Pongal &ndash; Mattu Pongal Day. Other festivals of Tamil Nadu: Karthigai Deepam, Vinayaka Chathurthi, Puthandu.</p>",
                    solution_hi: "<p>30.(d) <strong>पोंगल।</strong> यह तमिल समुदाय द्वारा मनाया जाने वाला एक फसल उत्सव है। जल्लीकट्टू तमिलनाडु का एक पारंपरिक खेल है जो पोंगल के तीसरे दिन, &lsquo;मट्टू पोंगल दिवस&rsquo; पर मनाया जाता है। तमिलनाडु के अन्य त्योहार: कार्तिगई दीपम, विनायक चतुर्थी, पुथांडु।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "31",
                    section: "18",
                    question_en: "<p>31. Select the INCORRECT statement about Agar.</p>",
                    question_hi: "<p>31. आगर के बारे में गलत कथन का चयन कीजिए।</p>",
                    options_en: [
                        "<p>Agar is used to grow microbes.</p>",
                        "<p>Agar, one of the commercial products obtained from red algae, gelidium and gracilaria.</p>",
                        "<p>It is used as a nutrient and source of fat of bacterial culture.</p>",
                        "<p>It is used in preparations of ice-creams and jellies.</p>"
                    ],
                    options_hi: [
                        "<p>आगर का उपयोग रोगाणुओं को विकसित करने के लिए किया जाता है।</p>",
                        "<p>आगर लाल शैवाल, गेलिडियम और ग्रेसिलेरिया से प्राप्त वाणिज्यिक उत्पादों में से एक है।</p>",
                        "<p>इसका उपयोग पोषक तत्त्व और वसा के स्रोत के रूप में किया जाता है</p>",
                        "<p>इसका उपयोग आइसक्रीम और मुरब्बा बनाने में किया जाता है।</p>"
                    ],
                    solution_en: "<p>31.(c) Agar is not a source of fat for bacterial culture, but it is used as a solid growth surface to help feed and grow bacteria. Agar is a gelatinous heteropolysaccharide produced in the cell wall of marine red algae such as species of Gelidium, Gracilaria, Gigartina, etc. It is a mixture of sulphated heterpolysaccharides made up of D-galactose and L-galactose derivatives ether-linked between C3 and C6. Agarose is the agar component with very few charged groups (sulfates, pyruvates).</p>",
                    solution_hi: "<p>31.(c) आगर बैक्टीरियल कल्चर के लिए वसा का स्रोत नहीं है, लेकिन इसका उपयोग बैक्टीरिया के भोजन और बढ़ाने में मदद करने के लिए एक ठोस विकास सतह के रूप में किया जाता है। आगर समुद्री लाल शैवाल जैसे जेलिडियम, ग्रेसिलेरिया, गिगार्टिना आदि प्रजातियों की कोशिका भित्ति में उत्पादित एक जेलेटिनस हेटेरोपॉलिसैकेराइड है। यह D-गैलेक्टोज और L-गैलेक्टोज डेरिवेटिव्स का मिश्रण है जो C3 और C6 के बीच ईथर-लिंक्ड सल्फेटेड हेटरपोलिसैकेराइड्स से बना होता है। एगारोज बहुत कम चार्ज्ड समूहों (सल्फेट्स, पाइरुवेट्स) वाला अगर घटक है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "32",
                    section: "18",
                    question_en: "<p>32. Which Indian dance artist won the Sangeet Natak Akademi Award in 2018 for String puppetry ?</p>",
                    question_hi: "<p>32. किस भारतीय नृत्य कलाकार ने स्ट्रिंग कठपुतली (String puppetry) के लिए 2018 में संगीत नाटक अकादमी पुरस्कार जीता ?</p>",
                    options_en: [
                        "<p>Anupama Hoskere</p>",
                        "<p>Surupa Sen</p>",
                        "<p>Tapan Kumar Pattanayak</p>",
                        "<p>Dr. Aruna Mohanty</p>"
                    ],
                    options_hi: [
                        "<p>अनुपमा होस्कर (Anupama Hoskere)</p>",
                        "<p>सुरूपा सेन (Surupa Sen)</p>",
                        "<p>तपन कुमार पटनायक (Tapan Kumar Pattanayak)</p>",
                        "<p>डॉ. अरुणा मोहंती (Dr. Aruna Mohanty)</p>"
                    ],
                    solution_en: "<p>32.(a)<strong> Anupama Hoskere</strong>. Award : Padma Shri (2024). She founded the Dhaatu Puppet Theater, Bengaluru. Surupa Sen - Odissi dancer. Tapan Kumar Pattanayak - Chhau dancer, Sangeet Natak Akademi Award (2018). Dr. Aruna Mohanty - Odissi dancer.</p>",
                    solution_hi: "<p>32.(a) <strong>अनुपमा होस्केरे (Anupama Hoskere)। </strong>पुरस्कार: पद्म श्री (2024)। उन्होंने बेंगलुरु के धातू कठपुतली थिएटर की स्थापना की थी। सुरूपा सेन - ओडिसी नृत्यांगना। तपन कुमार पट्टानायक - छऊ नर्तक, संगीत नटक अकादमी अवार्ड (2018)। डॉ. अरुणा मोहंती - ओडिसी नृत्यांगना।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "33",
                    section: "18",
                    question_en: "<p>33. A proton bonds to the oxygen atom of a solvent water to give a __________ hydronium ion.</p>",
                    question_hi: "<p>33. एक प्रोटॉन, विलायक जल के ऑक्सीजन परमाणु से आबंध बनाकर एक _______________ हाइड्रोनियम आयन देता है।</p>",
                    options_en: [
                        "<p>tetrahedral</p>",
                        "<p>square planar</p>",
                        "<p>square pyramid</p>",
                        "<p>trigonal pyramidal</p>"
                    ],
                    options_hi: [
                        "<p>चतुष्फलकीय</p>",
                        "<p>वर्ग समतलीय</p>",
                        "<p>वर्ग पिरामिड</p>",
                        "<p>त्रिकोणीय पिरामिडीय</p>"
                    ],
                    solution_en: "<p>33.(d) <strong>Trigonal pyramidal.</strong> A trigonal pyramidal molecular geometry is a shape where one atom is at the top, or apex, and three atoms form a triangular base, similar to a tetrahedron. The bond angle of a trigonal pyramid is usually 109.5&deg;. A proton is a positively charged hydrogen ion (H+). When a proton bonds to a water molecule (H₂O), the resulting ion is called a hydronium ion (H₃O+).</p>",
                    solution_hi: "<p>33.(d) <strong>त्रिकोणीय पिरामिडीय।</strong> त्रिकोणीय पिरामिड आणविक ज्यामिति एक ऐसी आकृति है जिसमें एक परमाणु शीर्ष पर होता है, और तीन परमाणु एक त्रिकोणीय आधार बनाते हैं, जो टेट्राहेड्रोन के समान होता है। त्रिकोणीय पिरामिड का बंध कोण आमतौर पर 109.5&deg; होता है। प्रोटॉन एक धनावेशित हाइड्रोजन आयन (H+) होता है। जब एक प्रोटॉन जल के अणु (H₂O) से बंध बनाता है, तो परिणामी आयन को हाइड्रोनियम आयन (H₃O+) कहा जाता है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "34",
                    section: "18",
                    question_en: "<p>34. Who of the following was one of the stalwarts of a renaissance in Koodiyattam?</p>",
                    question_hi: "<p>34. निम्नलिखित में से कौन कुडियाट्टम (Koodiyattam) के पुनर्जागरण के दिग्गजों में से एक था?</p>",
                    options_en: [
                        "<p>Makar Dhwaja Darogha</p>",
                        "<p>P.K Kunju Kurup</p>",
                        "<p>Amubi Singh</p>",
                        "<p>Ammannur Madhava Chakyar</p>"
                    ],
                    options_hi: [
                        "<p>मकरध्वज दारोगा (Makar Dhwaja Darogha)</p>",
                        "<p>पी. के. कुंजू कुरुप (P.K Kunju Kurup)</p>",
                        "<p>अमुबी सिंह (Amubi Singh)</p>",
                        "<p>अम्मनूर माधव चाक्यार (Ammannur Madhava Chakyar)</p>"
                    ],
                    solution_en: "<p>34.(d) <strong>Ammannur Madhava Chakyar. </strong>He was one of the stalwarts of a renaissance in Koodiyattam which is a traditional form of Sanskrit theatre from Kerala. His Awards - Padma Bhushan (2003), Padma Shri (1982), Sangeet Natak Akademi Award (1979). Makar Dhwaja Darogha (Chhau): Awards - Padma Shri (2011). P.K Kunju Kurup (Kathakali): Awards - Sangeet Natak Akademi Award (1956). Maisnam Amubi Singh (Manipuri): Awards - Padma Shri (1970), Sangeet Natak Akademi Award (1956).</p>",
                    solution_hi: "<p>34.(d) <strong>अम्मनूर माधव चाक्यार (Ammannur Madhava Chakyar)।</strong> वे कुडियाट्टम में पुनर्जागरण के दिग्गजों में से एक थे, जो केरल के संस्कृत थिएटर का एक पारंपरिक रूप है। उनके पुरस्कार - पद्म भूषण (2003), पद्म श्री (1982), संगीत नाटक अकादमी पुरस्कार (1979)। मकरध्वज दारोगा (छऊ): पुरस्कार - पद्म श्री (2011)। पी.के कुंजू कुरुप (कथकली): पुरस्कार - संगीत नाटक अकादमी पुरस्कार (1956)। मैसनाम अमुबी सिंह (मणिपुरी): पुरस्कार - पद्म श्री (1970), संगीत नाटक अकादमी पुरस्कार (1956)।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "35",
                    section: "18",
                    question_en: "<p>35. Thaneswar where the Vardhana dynasty ruled around the present-day state of _______.</p>",
                    question_hi: "<p>35. थानेश्वर जहां वर्धन राजवंश ने शासन किया, वर्तमान में _________ राज्य है।</p>",
                    options_en: [
                        "<p>Madhya Pradesh</p>",
                        "<p>Haryana</p>",
                        "<p>Rajasthan</p>",
                        "<p>Gujarat</p>"
                    ],
                    options_hi: [
                        "<p>मध्य प्रदेश</p>",
                        "<p>हरियाणा</p>",
                        "<p>राजस्थान</p>",
                        "<p>गुजरात</p>"
                    ],
                    solution_en: "<p>35.(b) <strong>Haryana. </strong>The Pushyabhuti dynasty, also known as the Vardhana dynasty, ruled parts of northern India during the 6th and 7th centuries. The Vardhana dynasty emerged after the decline of the Gupta Empire (3rd century CE - 6th century CE) in northern India. The core area of their kingdom was in present-day Haryana, with the capital at Sthanishvara or Thaneshwar. The dynasty reached its zenith under its ruler, Harsha Vardhana, who ruled from 606 AD to 647 AD. The capital of Harshavardhana\'s Empire was Kannuaj.</p>",
                    solution_hi: "<p>35.(b) <strong>हरियाणा। </strong>पुष्यभूति राजवंश, जिसे वर्धन राजवंश के नाम से भी जाना जाता है, ने 6वीं और 7वीं शताब्दी के दौरान उत्तर भारत के कुछ हिस्सों पर शासन किया। वर्धन राजवंश उत्तर भारत में गुप्त साम्राज्य (तीसरी शताब्दी ई. - छठी शताब्दी ई.) के पतन के बाद उभरा। उनके राज्य का मुख्य क्षेत्र वर्तमान हरियाणा में था, जिसकी राजधानी स्थानेश्वर या थानेश्वर थी। राजवंश अपने शासक हर्षवर्धन के अधीन अपने चरम पर पहुंच गया, जिन्होंने 606 ई. से 647 ई. तक शासन किया। हर्षवर्धन साम्राज्य की राजधानी कन्नौज थी।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "36",
                    section: "18",
                    question_en: "<p>36. Identify the INCORRECT pair regarding motion and their examples?</p>",
                    question_hi: "<p>36. गति और उनके उदाहरणों के संबंध में गलत युग्म की पहचान करें?</p>",
                    options_en: [
                        "<p>Translatory motion &ndash; A ball falling from the cliff</p>",
                        "<p>Periodic motion &ndash; Hands of a clock</p>",
                        "<p>Oscillatory motion &ndash; Earth moving around the sun</p>",
                        "<p>Rotatory motion &ndash; blades of a fan</p>"
                    ],
                    options_hi: [
                        "<p>स्थानान्तरीय गति - चट्टान से गिर रही एक गेंद</p>",
                        "<p>आवर्त गति &ndash; घड़ी की सुइयां</p>",
                        "<p>दोलन गति - पृथ्वी का सूर्य के चारों और घूमना</p>",
                        "<p>घूर्णन गति - पंखे के ब्लेड्स</p>"
                    ],
                    solution_en: "<p>36.(c) Oscillatory motion involves repeated back-and-forth movement around a fixed point, like a pendulum. Other examples: Oscillatory motion - Pendulum, swing, vibrating string. Periodic motion - Wave motion, planetary orbits. Rotatory motion - Merry-go- round, wheel rotation. Translatory motion - Car moving on a straight road, projectile motion.</p>",
                    solution_hi: "<p>36.(c) दोलन गति में एक निश्चित बिंदु के चारों ओर बार-बार आगे-पीछे की गति शामिल होती है, जैसे कि एक पेंडुलम। अन्य उदाहरण: दोलन गति - पेंडुलम, झूला, कंपन करने वाली स्ट्रिंग। आवधिक गति - तरंग गति, ग्रहों की कक्षाएँ। घूर्णी गति - मेरी -गो-राउंड, पहिया घूमना। स्थानांतरण गति - सीधी सड़क पर चलती कार, प्रक्षेप्य गति।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "37",
                    section: "18",
                    question_en: "<p>37. According to the K&ouml;ppen classification, which group of climate is generally found at higher latitudes or higher altitudes?</p>",
                    question_hi: "<p>37. कोपेन वर्गीकरण के अनुसार जलवायु का कौन-सा समूह सामान्यतः उच्चतर अक्षांशों या अत्यधिक ऊँचाई वाले स्थानों पर पाया जाता है?</p>",
                    options_en: [
                        "<p>D group</p>",
                        "<p>E group</p>",
                        "<p>C group</p>",
                        "<p>A group</p>"
                    ],
                    options_hi: [
                        "<p>समूह D</p>",
                        "<p>समूह E</p>",
                        "<p>समूह C</p>",
                        "<p>समूह A</p>"
                    ],
                    solution_en: "<p>37.(b) <strong>E group.</strong> Koeppen recognised five major climatic groups, four of them are based on temperature and one on precipitation. Climatic Groups According to Koeppen: A - Tropical, B - Dry Climates, C - Warm Temperate, D - Cold Snow Forest Climates, E - Cold Climates, H - HighLand. The climatic groups are subdivided into types, designated by small letters, based on seasonality of precipitation and temperature characteristics. The seasons of dryness are indicated by the small letters : f, m, w and s.</p>",
                    solution_hi: "<p>37.(b) <strong>समूह E । </strong>कोप्पेन ने पांच प्रमुख जलवायु समूहों की पहचान की, उनमें से चार तापमान पर और एक वर्षा पर आधारित है। कोप्पेन के अनुसार जलवायु समूह: A - उष्णकटिबंधीय, B - शुष्क जलवायु, C - गर्म शीतोष्ण, D - ठंडी बर्फीली वन जलवायु, E - ठंडी जलवायु, H - उच्च भूमि। वर्षा की मौसमी प्रकृति और तापमान विशेषताओं के आधार पर जलवायु समूहों को छोटे अक्षरों द्वारा निर्दिष्ट प्रकारों में विभाजित किया गया है। शुष्कता के मौसम को छोटे अक्षरों से दर्शाया जाता है: f, m, w और s ।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "38",
                    section: "18",
                    question_en: "<p>38. On 12th of March 1930, Gandhiji with his 78 followers began a march from _______ up to Dandi coast.</p>",
                    question_hi: "<p>38. 12 मार्च, 1930 को, गांधीजी ने अपने 78 अनुयायियों के साथ _______ से दांडी तट तक मार्च शुरू किया।</p>",
                    options_en: [
                        "<p>Kutch</p>",
                        "<p>Tolstoy Farm</p>",
                        "<p>Surat</p>",
                        "<p>Sabarmati Ashram</p>"
                    ],
                    options_hi: [
                        "<p>कच्छ</p>",
                        "<p>टॉलस्टॉय फार्म</p>",
                        "<p>सूरत</p>",
                        "<p>साबरमती आश्रम</p>"
                    ],
                    solution_en: "<p>38.(d) <strong>Sabarmati Ashram.</strong> The Salt March, or Dandi March, was a crucial event in the Indian independence movement led by Mahatma Gandhi, starting on March 12, 1930, and lasting 24 days until April 6, 1930. Gandhi and his followers marched about 240 miles (386 kilometers) from Sabarmati Ashram in Ahmedabad to the coastal village of Dandi on the Arabian Sea. This march aimed to protest the British salt monopoly and mobilize civil disobedience across India.</p>",
                    solution_hi: "<p>38.(d)<strong> साबरमती आश्रम। </strong>नमक मार्च या दांडी मार्च, महात्मा गांधी के नेतृत्व में भारतीय स्वतंत्रता आंदोलन में एक महत्वपूर्ण घटना थी, जो 12 मार्च, 1930 को शुरू हुआ था और 6 अप्रैल, 1930 तक 24 दिनों तक चला। गांधी और उनके अनुयायियों ने अहमदाबाद के साबरमती आश्रम से अरब सागर के तटीय गांव दांडी तक लगभग 240 मील (386 किलोमीटर) की यात्रा की थी। इस मार्च का उद्देश्य ब्रिटिश नमक एकाधिकार का विरोध करना और सम्पूर्ण भारत में सविनय अवज्ञा को संगठित करना था।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "39",
                    section: "18",
                    question_en: "<p>39. The Indian Vice President is elected by an electoral college comprising members of ________.</p>",
                    question_hi: "<p>39. भारत के उपराष्ट्रपति का चुनाव एक निर्वाचक मंडल द्वारा किया जाता है, जिसमें _________ के सदस्य शामिल होते हैं।</p>",
                    options_en: [
                        "<p>Rajya Sabha, Lok Sabha and State Legislative Council</p>",
                        "<p>Lok Sabha and State Legislature</p>",
                        "<p>Rajya Sabha and State Legislative Assembly</p>",
                        "<p>both the houses of Parliament</p>"
                    ],
                    options_hi: [
                        "<p>राज्यसभा, लोकसभा और राज्य विधान परिषद</p>",
                        "<p>लोकसभा और राज्य विधानमंडल</p>",
                        "<p>राज्यसभा और राज्य विधान सभा</p>",
                        "<p>संसद के दोनों सदन</p>"
                    ],
                    solution_en: "<p>39.(d) <strong>Article 66</strong> - The vice president is elected indirectly, by an electoral college consisting of members (elected as well as nominated) of both Houses of Parliament (Lok Sabha &amp; Rajya Sabha), in accordance with the system of proportional representation by means of the single transferable vote and the voting at such election shall be by secret ballot. Tenure - 5 years. He is Ex-Officio chairman of the Council of States.</p>",
                    solution_hi: "<p>39.(d) <strong>अनुच्छेद 66 </strong>- उपराष्ट्रपति का चुनाव अप्रत्यक्ष रूप से एक निर्वाचक मंडल के द्वारा किया जाता है जिसमें संसद के दोनों सदनों (लोकसभा और राज्यसभा) के सदस्य (निर्वाचित और मनोनीत)) शामिल होते हैं, जो एकल संक्रमणीय मत के माध्यम से आनुपातिक प्रतिनिधित्व प्रणाली के अनुसार गुप्त मतदान के द्वारा होता है। कार्यकाल 5 वर्ष। उपराष्ट्रपति, राज्यसभा का पदेन सभापति होता है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "40",
                    section: "18",
                    question_en: "<p>40. Which of the following are major coal fields in India?</p>",
                    question_hi: "<p>40. निम्नलिखित में से कौन-से भारत के प्रमुख कोयला क्षेत्र हैं?</p>",
                    options_en: [
                        "<p>Jharia and Raniganj</p>",
                        "<p>Jhunjhunu and Alwar</p>",
                        "<p>Satara and Pune</p>",
                        "<p>Coimbatore and Madurai</p>"
                    ],
                    options_hi: [
                        "<p>झरिया और रानीगंज</p>",
                        "<p>झुंझुनू और अलवर</p>",
                        "<p>सतारा और पुणे</p>",
                        "<p>कोयम्बटूर और मदुरै</p>"
                    ],
                    solution_en: "<p>40.(a) <strong>Jharia and Raniganj. </strong>Jharkhand is home to several significant coalfields, including Jharia (the largest), Bokaro, Dhanbad, Giridih, Karanpura, Ramgarh, and Daltonganj. Major coal fields : West Bengal - Dalingkot (Darjeeling) Birbhum, Chinakuri. Madhya Pradesh - Singrauli, Suhagpur. Odisha - Talcher. Other Minerals in India : Copper - Found in Jhunjhunu and Alwar in Rajasthan. Limestone - Commonly found in Coimbatore and Madurai in Tamil Nadu.</p>",
                    solution_hi: "<p>40.(a) <strong>झरिया और रानीगंज। </strong>झारखंड कई महत्वपूर्ण कोयला क्षेत्रों का घर है, जिनमें झरिया (सबसे बड़ा), बोकारो, धनबाद, गिरिडीह, करनपुरा, रामगढ़ और डाल्टनगंज शामिल हैं। प्रमुख कोयला क्षेत्र : पश्चिम बंगाल - दलिंगकोट (दार्जिलिंग) बीरभूम, चिनाकुरी। मध्य प्रदेश - सिंगरौली, सुहागपुर। ओडिशा - तालचेर।<br>भारत में अन्य खनिज : तांबा - राजस्थान में झुंझुनू और अलवर में पाया जाता है। चूना पत्थर - आमतौर पर तमिलनाडु में कोयंबटूर और मदुरै में पाया जाता है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "41",
                    section: "18",
                    question_en: "<p>41. Which of the following types of unemployment arises from a mismatch between the jobs available in the market and the skills of the available workers in the market?</p>",
                    question_hi: "<p>41. निम्नलिखित में से किस प्रकार की बेरोजगारी, बाजार में उपलब्ध नौकरियों और बाजार में उपलब्ध श्रमिकों के कौशल के बीच बेमेलता होने से उत्पन्न होती है?</p>",
                    options_en: [
                        "<p>Frictional</p>",
                        "<p>Disguised</p>",
                        "<p>Structural</p>",
                        "<p>Seasonal</p>"
                    ],
                    options_hi: [
                        "<p>घर्षण</p>",
                        "<p>प्रच्छन्न</p>",
                        "<p>संरचनात्मक</p>",
                        "<p>मौसमी</p>"
                    ],
                    solution_en: "<p>41.(c)<strong> Structural. </strong>Frictional unemployment occurs when workers transition between jobs while searching for new opportunities. Seasonal unemployment happens when workers are jobless during certain seasons, especially in agriculture, tourism, and retail. Disguised unemployment occurs when more workers are employed than needed, leading to reduced productivity, commonly seen in agriculture and family businesses.</p>",
                    solution_hi: "<p>41.(c) <strong>संरचनात्मक।</strong> घर्षण बेरोजगारी तब होती है जब श्रमिक नए अवसरों की खोज करते हुए नौकरियों के बीच बदलाव करते हैं। मौसमी बेरोजगारी तब होती है जब कर्मचारी कुछ खास मौसमों के दौरान बेरोजगार होते हैं, खासकर कृषि, पर्यटन और रिटेल क्षेत्र में। प्रच्छन्न बेरोजगारी तब होती है जब आवश्यकता से अधिक श्रमिकों को रोजगार दिया जाता है, जिससे उत्पादकता कम हो जाती है, जो आमतौर पर कृषि और पारिवारिक व्यवसायों में देखी जाती है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "42",
                    section: "18",
                    question_en: "<p>42. Who among the following, while praising the amending feature of the Indian Constitution said that &lsquo;This variety in the amending process is wise but is rarely found&rsquo;?</p>",
                    question_hi: "<p>42. निम्नलिखित में से किसने भारतीय संविधान की संशोधित विशेषता की प्रशंसा करते हुए कहा था कि \'संशोधन प्रक्रिया में यह विविधता बुद्धिमानी है लेकिन बहुत कम पाई जाती है\'?</p>",
                    options_en: [
                        "<p>Granville Austin</p>",
                        "<p>Ivor Jennings</p>",
                        "<p>K C Wheare</p>",
                        "<p>HM Seervai</p>"
                    ],
                    options_hi: [
                        "<p>ग्रैनविले ऑस्टिन (Granville Austin)</p>",
                        "<p>इवोर जेनिंग्स (Ivor Jennings)</p>",
                        "<p>के. सी. व्हेयर (K C Wheare)</p>",
                        "<p>एचएम सीरवई (HM Seervai)</p>"
                    ],
                    solution_en: "<p>42.(c) <strong>K C Wheare</strong> was an Australian academic who was an expert on the constitutions of the British Commonwealth. He also described the Indian Constitution as Quasi federal. Granville Austin, a well-known scholar of the Indian Constitution, said, &ldquo;The amending process has proved itself as one of the most ably conceived aspects of the constitution&rdquo;. Ivor Jennings said that &lsquo;India is a federation with a strong centralising tendency&rsquo;.</p>",
                    solution_hi: "<p>42.(c) <strong>के. सी. व्हेयर (K C Wheare)</strong> एक ऑस्ट्रेलियाई शिक्षाविद थे जो ब्रिटिश राष्ट्रमंडल के संविधानों के विशेषज्ञ थे। उन्होंने भारतीय संविधान को अर्द्ध-संघीय बताया। भारतीय संविधान के सुप्रसिद्ध विद्वान ग्रैनविले ऑस्टिन ने कहा, \"संशोधन प्रक्रिया ने स्वयं को संविधान के सबसे सुविचारित पहलुओं में से एक साबित किया है\"। इवोर (आइवर) जेनिंग्स ने कहा था कि &lsquo;भारत एक मजबूत केंद्रीकरण प्रवृत्ति वाला संघ है।&rsquo;</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "43",
                    section: "18",
                    question_en: "<p>43. An argon positive ion fired towards east gets deflected towards south by a magnetic field. The direction of magnetic field is :</p>",
                    question_hi: "<p>43. पूर्व की ओर प्रज्वलित एक आर्गन धनात्मक आयन चुंबकीय क्षेत्र द्वारा दक्षिण की ओर विक्षेपित हो जाता है चुंबकीय क्षेत्र की दिशा है:</p>",
                    options_en: [
                        "<p>upward</p>",
                        "<p>towards north</p>",
                        "<p>downward</p>",
                        "<p>towards south</p>"
                    ],
                    options_hi: [
                        "<p>ऊपर की ओर</p>",
                        "<p>उत्तर की ओर</p>",
                        "<p>नीचे की ओर</p>",
                        "<p>दक्षिण की ओर</p>"
                    ],
                    solution_en: "<p>43.(a) <strong>upward.</strong> The proton is projected towards east and the direction of magnetic force on the proton is along the south. Using the right hand thumb rule, the direction of the magnetic field is vertically upwards. The induced current is found to be the highest when the direction of motion of the coil is at right angles to the magnetic field.</p>",
                    solution_hi: "<p>43.(a) <strong>ऊपर की ओर। </strong>प्रोटॉन पूर्व की ओर प्रक्षेपित है और प्रोटॉन पर चुंबकीय बल की दिशा दक्षिण की ओर है। दाएं हाथ के अंगूठे के नियम का उपयोग करते हुए, चुंबकीय क्षेत्र की दिशा ऊर्ध्वाधर ऊपर की ओर है। प्रेरित धारा सबसे अधिक तब पायी जाती है जब कुंडली की गति की दिशा चुंबकीय क्षेत्र के लम्बवत होती है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "44",
                    section: "18",
                    question_en: "<p>44. In India, statutory liquidity ratio is fixed by _____.</p>",
                    question_hi: "<p>44. भारत में वैधानिक तरलता अनुपात (statutory liquidity ratio) कौन निर्धारित करता है?</p>",
                    options_en: [
                        "<p>India Brand Equity Foundation</p>",
                        "<p>State government</p>",
                        "<p>Commercial banks</p>",
                        "<p>Reserve Bank of India</p>"
                    ],
                    options_hi: [
                        "<p>इंडिया ब्रांड इक्विटी फाउंडेशन</p>",
                        "<p>राज्य सरकार</p>",
                        "<p>वाणिज्यिक बैंक</p>",
                        "<p>भारतीय रिजर्व बैंक</p>"
                    ],
                    solution_en: "<p>44.(d) <strong>Reserve Bank of India (RBI).</strong> It is India\'s central bank and regulatory body responsible for regulation of the Indian banking system. Established - 1 April 1935. Headquarters - Mumbai. It was nationalised in 1949 and since then fully owned by the Ministry of Finance, Government of India. The Statutory Liquidity Ratio (SLR) is a regulation that requires commercial banks in India to maintain a minimum percentage of their deposits in liquid assets.</p>",
                    solution_hi: "<p>44.(d)<strong> भारतीय रिज़र्व बैंक (RBI)। </strong>यह भारत का केंद्रीय बैंक और नियामक निकाय है जो भारतीय बैंकिंग प्रणाली के विनियमन के लिए उत्तरदायी है। स्थापना - 1 अप्रैल 1935। मुख्यालय - मुंबई। 1949 में इसका राष्ट्रीयकरण किया गया और तब से यह भारत सरकार के वित्त मंत्रालय के पूर्ण स्वामित्व में है। वैधानिक तरलता अनुपात (SLR) एक विनियमन है जिसके तहत भारत में वाणिज्यिक बैंकों को अपनी जमा राशि का न्यूनतम प्रतिशत तरल परिसंपत्तियों में बनाए रखना आवश्यक है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "45",
                    section: "18",
                    question_en: "<p>45. Krishna Rao Shankar Pandit, Raja Bhaiya Poonchwale are associated with which Gharana?</p>",
                    question_hi: "<p>45. कृष्ण राव शंकर पंडित, राजा भैया पूंछवाले का संबंध किस घराने से है ?</p>",
                    options_en: [
                        "<p>Gwalior Gharana</p>",
                        "<p>Agra Gharana</p>",
                        "<p>Jaipur Atrauli Gharana</p>",
                        "<p>Lucknow Gharana</p>"
                    ],
                    options_hi: [
                        "<p>ग्वालियर घराना</p>",
                        "<p>आगरा घराना</p>",
                        "<p>जयपुर अतरौली घराना</p>",
                        "<p>लखनऊ घराना</p>"
                    ],
                    solution_en: "<p>45.(a) <strong>Gwalior Gharana.</strong> It belongs to the Khayal style of Hindustani classical music. It was founded by Ustad Nathan Pir Baksh and Ustad Nathu Khan. Gwalior, in Madhya Pradesh, the birthplace of the famed Indian classical singer Tansen, has been declared a &lsquo;Creative City of Music&rsquo; by the United Nations Educational, Scientific and Cultural Organization (UNESCO).</p>",
                    solution_hi: "<p>45.(a)<strong> ग्वालियर घराना। </strong>यह हिंदुस्तानी शास्त्रीय संगीत की ख्याल शैली से संबंधित है। इसकी स्थापना उस्ताद नाथन पीर बख्श और उस्ताद नाथू खान ने की थी। प्रसिद्ध भारतीय शास्त्रीय गायक तानसेन की जन्मस्थली मध्य प्रदेश के ग्वालियर को संयुक्त राष्ट्र शैक्षिक, वैज्ञानिक और सांस्कृतिक संगठन (UNESCO) द्वारा \'संगीत का रचनात्मक शहर\' घोषित किया गया है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "46",
                    section: "18",
                    question_en: "<p>46. Which of the following is the hardest mineral ?</p>",
                    question_hi: "<p>46. निम्न में से सबसे कठोर खनिज कौन-सा है?</p>",
                    options_en: [
                        "<p>Gypsum</p>",
                        "<p>Talc</p>",
                        "<p>Calcite</p>",
                        "<p>Diamond</p>"
                    ],
                    options_hi: [
                        "<p>जिप्सम</p>",
                        "<p>टेल्क</p>",
                        "<p>केल्साइट</p>",
                        "<p>हीरा</p>"
                    ],
                    solution_en: "<p>46.(d) <strong>Diamond :</strong> Hardest known natural mineral on earth. It is an allotrope of Carbon. In diamond, each carbon atom is bonded to four other carbon atoms forming a rigid three dimensional structure. Talc is the softest mineral on Earth.</p>",
                    solution_hi: "<p>46.(d)<strong> हीरा:</strong> पृथ्वी पर ज्ञात सबसे कठोर प्राकृतिक खनिज है। यह कार्बन का अपरूप है। हीरे में, प्रत्येक कार्बन परमाणु चार अन्य कार्बन परमाणुओं से मिलकर एक दृढ़ त्रि-आयामी संरचना बनाता है। टैल्क (साबुन​) सबसे नरम खनिज है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "47",
                    section: "18",
                    question_en: "<p>47. In which sport is \"puck\" used?</p>",
                    question_hi: "<p>47. \"पक (puck)\" का प्रयोग किस खेल में किया जाता है?</p>",
                    options_en: [
                        "<p>soccer</p>",
                        "<p>Snooker</p>",
                        "<p>table tennis</p>",
                        "<p>Ice hockey</p>"
                    ],
                    options_hi: [
                        "<p>सॉकर</p>",
                        "<p>स्नूकर</p>",
                        "<p>टेबल टेनिस</p>",
                        "<p>आइस हॉकी</p>"
                    ],
                    solution_en: "<p>47.(d) <strong>Ice hockey. </strong>Other terminologies related to sports : Ice hockey - Power play, Rink, Zamboni, Sweep check, Roughing. Table tennis - Blade, Crossover, Shakehand Grip. Snooker - Reflected angle, Ambidextrous, Air barrel. Soccer - Dead ball, Fifty-fifty balls, Kicker.</p>",
                    solution_hi: "<p>47.(d) <strong>आइस हॉकी।</strong> खेलों से संबंधित अन्य शब्दावलियाँ: आइस हॉकी - पावर प्ले, रिंक, ज़म्बोनी, स्वीप चेक, रफ़िंग। टेबल टेनिस - ब्लेड, क्रॉसओवर, शेकहैंड ग्रिप। स्नूकर - रिफ़्लेक्टेड एंगल, एम्बिडेक्स्ट्रस, एयर बैरल। सॉकर - डेड बॉल, फिफ़्टी-फिफ़्टी बॉल, किकर।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "48",
                    section: "18",
                    question_en: "<p>48. K-selected species are characterised by:</p>",
                    question_hi: "<p>48. K-चयनित प्रजातियों की विशेषताएं क्&zwj;या हैं?</p>",
                    options_en: [
                        "<p>species possessing relatively stable populations and which tend to produce relatively low numbers of offspring</p>",
                        "<p>species possessing relatively unstable populations and which tend to produce relatively large <br>numbers of offspring</p>",
                        "<p>species possessing relatively unstable populations and which tend to produce relatively low numbers of offspring</p>",
                        "<p>species possessing relatively stable populations and which tend to produce relatively large numbers of offspring</p>"
                    ],
                    options_hi: [
                        "<p>अपेक्षाकृत स्थिर आबादी रखने वाली प्रजातियां और जो अपेक्षाकृत कम संख्या में संतान पैदा करती हैं</p>",
                        "<p>अपेक्षाकृत अस्थिर आबादी वाली प्रजातियां और जो अपेक्षाकृत बड़ी संख्या में संतान पैदा करती हैं</p>",
                        "<p>अपेक्षाकृत अस्थिर आबादी वाली प्रजातियां और जो अपेक्षाकृत कम संख्या में संतान पैदा करती हैं</p>",
                        "<p>अपेक्षाकृत स्थिर आबादी रखने वाली प्रजातियां और जो अपेक्षाकृत बड़ी संख्या में संतान पैदा करती हैं</p>"
                    ],
                    solution_en: "<p>48.(a). K-selected species have stable populations and produce fewer offspring. Key characteristics include: Parental Care: They invest significant time and effort in raising their young. Population Growth: They grow slowly, exhibiting a logistic growth curve that levels off at the environment\'s carrying capacity. Lifespan: K-selected species generally have longer lifespans. Reproduction: They reproduce less frequently but may do so multiple times in their lives. Examples of K-selected species include humans, elephants, bison, certain trees, and some reptiles.</p>",
                    solution_hi: "<p>48.(a). K-चयनित प्रजातियों की आबादी स्थिर होती है और वे कम संतान पैदा करती हैं। मुख्य विशेषताओं में शामिल हैं: माता-पिता की देखभाल: वे अपने बच्चों को पालने में महत्वपूर्ण समय और प्रयास लगाते हैं। जनसंख्या वृद्धि: वे धीरे-धीरे बढ़ते हैं, एक तार्किक वृद्धि वक्र प्रदर्शित करते हैं जो पर्यावरण की वहन क्षमता पर स्थिर हो जाता है। जीवनकाल: K-चयनित प्रजातियों का जीवनकाल सामान्यतः लंबा होता है। प्रजनन: वे कम बार प्रजनन करते हैं लेकिन अपने जीवन में कई बार ऐसा कर सकते हैं। K-चयनित प्रजातियों के उदाहरणों में मनुष्य, हाथी, बाइसन, कुछ पेड़ और कुछ सरीसृप शामिल हैं।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "49",
                    section: "18",
                    question_en: "<p>49. When is World Mental Health Day observed annually?</p>",
                    question_hi: "<p>49. विश्व मानसिक स्वास्थ्य दिवस प्रतिवर्ष कब मनाया जाता है?</p>",
                    options_en: [
                        "<p>October 5</p>",
                        "<p>October 10</p>",
                        "<p>November 10</p>",
                        "<p>December 1</p>"
                    ],
                    options_hi: [
                        "<p>5 अक्टूबर</p>",
                        "<p>10 अक्टूबर</p>",
                        "<p>10 नवंबर</p>",
                        "<p>1 दिसंबर</p>"
                    ],
                    solution_en: "<p>49.(b) <strong>October 10.</strong> World Mental Health Day, first initiated in 1992 by the World Federation for Mental Health (WFMH), aims to raise awareness about mental health issues and promote better mental well-being globally. The theme for 2024, \"Mental Health at Work&rdquo;.</p>",
                    solution_hi: "<p>49.(b) <strong>10 अक्टूबर। </strong>विश्व मानसिक स्वास्थ्य दिवस, जिसकी शुरुआत 1992 में विश्व मानसिक स्वास्थ्य महासंघ (WFMH) द्वारा की गई थी, का उद्देश्य मानसिक स्वास्थ्य के मुद्दों के बारे में जागरूकता बढ़ाना और वैश्विक स्तर पर बेहतर मानसिक स्वास्थ्य को बढ़ावा देना है। 2024 का थीम, \"कार्यस्थल पर मानसिक स्वास्थ्य\"।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "50",
                    section: "17",
                    question_en: "<p>50. The languages ​​spoken in Jharkhand and parts of Central India mainly belong to which of the following language families?</p>",
                    question_hi: "<p>50. झारखंड और मध्य भारत के कुछ हिस्सों में बोली जाने वाली भाषाओं का संबंध मुख्य रूप से निम्नलिखित में से किस भाषा परिवार से हैं?</p>",
                    options_en: [
                        "<p>Tibeto-Burman family</p>",
                        "<p>Austro-Asian family</p>",
                        "<p>Dravidian family</p>",
                        "<p>Indo-European family</p>"
                    ],
                    options_hi: [
                        "<p>तिब्बती-बर्मन परिवार</p>",
                        "<p>ऑस्ट्रो-एशियाई परिवार</p>",
                        "<p>द्रविड़ परिवार</p>",
                        "<p>इंडो-यूरोपीय परिवार</p>"
                    ],
                    solution_en: "<p>50.(b) <strong>Austro-Asian family. </strong>There are mainly four families of languages in India : Indo-European family, Tibeto-Burman family, Austro-Asian family and Dravidian family. Indo-European family - This includes languages such as Sanskrit, Assamese, Gujarati, Hindi, Kashmiri, and Sindhi. Tibeto-Burman family - The languages spoken in the north-east of India belong to the Tibeto-Burman family. An example is Khasi. Dravidian family - This family includes languages like Tamil, Telugu, Kannada, and Malayalam.</p>",
                    solution_hi: "<p>50.(b) <strong>ऑस्ट्रो-एशियाई परिवार। </strong>भारत में मुख्य रूप से चार भाषा परिवार हैं: इंडो-यूरोपीय परिवार, तिब्बती-बर्मन परिवार, ऑस्ट्रो-एशियाई परिवार और द्रविड़ परिवार। इंडो-यूरोपीय परिवार - इसमें संस्कृत, असमिया, गुजराती, हिंदी, कश्मीरी एवं सिंधी जैसी भाषाएँ शामिल हैं। तिब्बती-बर्मन परिवार - भारत के उत्तर-पूर्व में बोली जाने वाली भाषाएँ तिब्बती-बर्मन परिवार से संबंधित हैं। इसका एक उदाहरण खासी है। द्रविड़ परिवार - इस परिवार में तमिल, तेलुगु, कन्नड़ तथा मलयालम जैसी भाषाएँ शामिल हैं।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "51",
                    section: "17",
                    question_en: "<p>51. Ram gives a six-digit number 468312 to Shyam to check the divisibility. Shyam tells Ram that the number is divisible by 57. Shyam asks Ram, &ldquo;If we rearrange the digits of this number in descending order, then by which number will it be always divisible ?&rdquo;</p>",
                    question_hi: "<p>51. राम, श्याम को छह अंकों वाली संख्या 468312 की विभाज्यता की जांच करने के लिए देता है। श्याम, राम से कहता है कि संख्या 57 से विभाज्य है। श्याम, राम से पूछता है, \"यदि हम इस संख्या के अंकों को अवरोही क्रम में पुनर्व्यवस्थित करें, तो यह किस संख्या से विभाज्य होगी ?\"</p>",
                    options_en: [
                        "<p>2</p>",
                        "<p>3</p>",
                        "<p>19</p>",
                        "<p>17</p>"
                    ],
                    options_hi: [
                        "<p>2</p>",
                        "<p>3</p>",
                        "<p>19</p>",
                        "<p>17</p>"
                    ],
                    solution_en: "<p>51.(b)<br><strong>Divisibility rule of 3 :</strong> <br>For any no to be divisible by 3, the sum of its digits should be divisible by 3.<br><strong>57 = 19 &times; 3</strong><br>Given that , 468312 is divisible by (19&times;3)<br>On arranging it in descending order , digits remain the same . so it will always be divisible by 3.</p>",
                    solution_hi: "<p>51.(b)<br><strong>3 की विभाज्यता नियम:</strong> <br>किसी भी संख्या के 3 से विभाज्य होने के लिए उसके अंकों का योग 3 से विभाज्य होना चाहिए।<br><strong>57 = 19 &times; 3</strong><br>यह देखते हुए, 468312 (19&times;3) से विभाज्य है<br>इसे अवरोही क्रम में व्यवस्थित करने पर अंक समान रहते हैं। अतः यह सदैव 3 से विभाज्य होगा।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "52",
                    section: "17",
                    question_en: "<p>52. A vegetable vendor bought 100 kg of potatoes at the rate of ₹19 per kg and spent ₹100 as cartage. He sold 60 kg of potatoes with a 50% profit and half of the remaining stock with a 40% profit. He sold half of the still remaining potatoes with a 25% profit. What profit percentage should he aim for when selling the ultimate remaining potatoes to achieve an overall profit of 42%?</p>",
                    question_hi: "<p>52. एक सब्जी विक्रेता ने ₹19 प्रति kg की दर से 100 kg आलू खरीदे और गाड़ी-भाड़े पर ₹100 खर्च किए। उसने 60 kg आलू 50% लाभ पर और शेष माल का आधा हिस्सा 40% लाभ पर बेचा। इसके बाद उसने बचे हुए आलुओं में से आधे आलू 25% लाभ पर बेच दिए। 42% का समग्र लाभ प्राप्त करने के लिए उसे शेष बचे आलुओं को बेचते समय कितने लाभ प्रतिशत का लक्ष्य रखना होगा?</p>",
                    options_en: [
                        "<p>0.16</p>",
                        "<p>0.1</p>",
                        "<p>0.12</p>",
                        "<p>0.15</p>"
                    ],
                    options_hi: [
                        "<p>0.16</p>",
                        "<p>0.1</p>",
                        "<p>0.12</p>",
                        "<p>0.15</p>"
                    ],
                    solution_en: "<p>52.(d) <br>Total cost of 100 kg of vegetable = 100 &times; 19 + 100 = ₹ 2000<br>Then, 1 kg vegetable cost = ₹ 20<br>Now, <br>SP of 60 kg vegetable after profit of 50% = 60 &times; 20 &times; <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = ₹ 1800<br>SP of 20 kg vegetable after profit of 40% = 20 &times; 20 &times; <math display=\"inline\"><mfrac><mrow><mn>7</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> = ₹ 560<br>SP of 10 kg vegetable after profit of 25% = 10 &times; 20 &times; <math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> = ₹ 250 <br>Total SP of 90 kg vegetable = 1800 + 560 + 250 = ₹ 2610<br>According to the question,<br>SP of 100 kg vegetable after overall profit = 2000 &times; <math display=\"inline\"><mfrac><mrow><mn>142</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = ₹ 2840<br>SP of 10 kg vegetable = 2840 - 2610 = ₹ 230<br>CP of 10 kg vegetable = 10 &times; 20 = ₹ 200<br>So, required percentage = <math display=\"inline\"><mfrac><mrow><mn>230</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>200</mn></mrow><mrow><mn>200</mn></mrow></mfrac></math> &times; 100 = 15% = 0.15</p>",
                    solution_hi: "<p>52.(d) <br>100 किलो सब्जी की कुल लागत = 100 &times; 19 + 100 = ₹ 2000<br>तो, 1 किलो सब्जी की कीमत = ₹ 20<br>अब, <br>50% लाभ के बाद 60 किलो सब्जी का विक्रय मूल्य = 60 &times; 20 &times; <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = ₹ 1800<br>40% लाभ के बाद 20 किलो सब्जी का विक्रय मूल्य = 20 &times; 20 &times; <math display=\"inline\"><mfrac><mrow><mn>7</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> = ₹ 560<br>25% लाभ के बाद 10 किलो सब्जी का विक्रय मूल्य = 10 &times; 20 &times; <math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> = ₹ 250 <br>90 किलो सब्जी का कुल विक्रय मूल्य = 1800 + 560 + 250 = ₹ 2610<br>प्रश्न के अनुसार,<br>कुल लाभ के बाद 100 किलो सब्जी का विक्रय मूल्य = 2000 &times; <math display=\"inline\"><mfrac><mrow><mn>142</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = ₹ 2840<br>10 किलो सब्जी का विक्रय मूल्य = 2840 - 2610 = ₹ 230<br>10 किलो सब्जी का क्रय मूल्य = 10 &times; 20 = ₹ 200<br>तो, आवश्यक प्रतिशत = <math display=\"inline\"><mfrac><mrow><mn>230</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>200</mn></mrow><mrow><mn>200</mn></mrow></mfrac></math> &times; 100 = 15% = 0.15</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "53",
                    section: "17",
                    question_en: "<p>53. The ratio of the length of a tree to its shadow is 1 : <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><msqrt><mn>3</mn></msqrt></mrow></mfrac></math>. The angle of the sun\'s elevation is:</p>",
                    question_hi: "<p>53. एक पेड़ की लंबाई और उसकी छाया का अनुपात 1 : <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><msqrt><mn>3</mn></msqrt></mrow></mfrac></math> है। सूर्य का उन्नयन कोण है:</p>",
                    options_en: [
                        "<p>75&deg;</p>",
                        "<p>30&deg;</p>",
                        "<p>60&deg;</p>",
                        "<p>45&deg;</p>"
                    ],
                    options_hi: [
                        "<p>75&deg;</p>",
                        "<p>30&deg;</p>",
                        "<p>60&deg;</p>",
                        "<p>45&deg;</p>"
                    ],
                    solution_en: "<p>53.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742268462603.png\" alt=\"rId52\" width=\"118\" height=\"120\"><br>tan <math display=\"inline\"><mi>&#952;</mi><mo>=</mo><mfrac><mrow><mn>1</mn></mrow><mrow><mfrac><mrow><mn>1</mn></mrow><mrow><msqrt><mn>3</mn></msqrt></mrow></mfrac></mrow></mfrac><mo>=</mo><msqrt><mn>3</mn></msqrt></math> = tan 60&deg;<br>&rArr; &theta; = 60&deg;</p>",
                    solution_hi: "<p>53.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742268462603.png\" alt=\"rId52\" width=\"118\" height=\"120\"><br>tan <math display=\"inline\"><mi>&#952;</mi><mo>=</mo><mfrac><mrow><mn>1</mn></mrow><mrow><mfrac><mrow><mn>1</mn></mrow><mrow><msqrt><mn>3</mn></msqrt></mrow></mfrac></mrow></mfrac><mo>=</mo><msqrt><mn>3</mn></msqrt></math> = tan 60&deg;<br>&rArr; &theta; = 60&deg;</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "54",
                    section: "17",
                    question_en: "<p>54. In an election between Sunita and Mamata, 85% of voters cast their votes but 2% of votes were invalid. If Sunita got 64,974 votes which is 78% of valid votes, then the total number of voters enrolled in the election was:</p>",
                    question_hi: "<p>54. सुनीता और ममता के बीच एक चुनाव में, 85% मतदाताओं ने अपना मत डाले लेकिन 2% मत अवैध थे। यदि सुनीता को 64,974 मत मिले जो वैध मतों का 78% है, तो चुनाव में नामांकित मतदाताओं की कुल संख्या कितनी थी?</p>",
                    options_en: [
                        "<p>1,25,000</p>",
                        "<p>1,00,000</p>",
                        "<p>1,65,000</p>",
                        "<p>1,80,000</p>"
                    ],
                    options_hi: [
                        "<p>1,25,000</p>",
                        "<p>1,00,000</p>",
                        "<p>1,65,000</p>",
                        "<p>1,80,000</p>"
                    ],
                    solution_en: "<p>54.(b)<br>Let the total number of voters enrolled = 100<math display=\"inline\"><mi>x</mi></math><br>Sunita got the votes = 100<math display=\"inline\"><mi>x</mi></math> &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>85</mn><mn>100</mn></mfrac></math> &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>98</mn><mn>100</mn></mfrac></math> &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>78</mn><mn>100</mn></mfrac></math> = 64974<br><math display=\"inline\"><mo>&#8658;</mo></math>x = 1000<br>And total votes enrolled = 100 <math display=\"inline\"><mo>&#215;</mo></math> 1000 = 1,00,000</p>",
                    solution_hi: "<p>54.(b)<br>माना , नामांकित मतदाताओं की कुल संख्या = 100<math display=\"inline\"><mi>x</mi></math><br>सुनीता को मिले वोट = 100<math display=\"inline\"><mi>x</mi></math> &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>85</mn><mn>100</mn></mfrac></math> &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>98</mn><mn>100</mn></mfrac></math> &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>78</mn><mn>100</mn></mfrac></math> = 64974<br><math display=\"inline\"><mo>&#8658;</mo></math>x = 1000<br>और कुल नामांकित वोट = 100 <math display=\"inline\"><mo>&#215;</mo></math> 1000 = 1,00,000</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "55",
                    section: "17",
                    question_en: "<p>55. A number whose fifth part increased by 5 is equal to its third part decreased by 7. Find half of the number.</p>",
                    question_hi: "<p>55. कोई संख्या जिसके पांचवें भाग में 5 की वृद्धि की जाती है, तो वह उसके तीसरे भाग में 7 की कमी के बराबर हो जाती है। संख्या का आधा भाग ज्ञात कीजिए।</p>",
                    options_en: [
                        "<p>45</p>",
                        "<p>150</p>",
                        "<p>80</p>",
                        "<p>60</p>"
                    ],
                    options_hi: [
                        "<p>45</p>",
                        "<p>150</p>",
                        "<p>80</p>",
                        "<p>60</p>"
                    ],
                    solution_en: "<p>55.(a)<br>Let the number is 15<math display=\"inline\"><mi>x</mi></math> (LCM of 5 and 3)<br>According to question<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>5</mn></mfrac></math> of 15x + 5 =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>1</mn><mn>3</mn></mfrac></mstyle></math> of 15x - 7<br>3x&nbsp;+ 5 = 5x - 7<br>2x&nbsp;= 12<br>x = 6<br>Hence, number = 15x&nbsp;= 15 &times; 6 = 90 <br>Half of number = 45</p>",
                    solution_hi: "<p>55.(a)<br>माना संख्या 15&times; है (5 और 3 का LCM)<br>प्रश्न के अनुसार,<br>15x&nbsp;का&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>1</mn><mn>5</mn></mfrac></mstyle></math> + 5 = 15x का&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>1</mn><mn>3</mn></mfrac></mstyle></math> - 7<br>3x&nbsp;+ 5 = 5x - 7<br>2x&nbsp;= 12<br>x = 6<br>अत: संख्या = 15x&nbsp;= 15 &times; 6 = 90 <br>संख्या का आधा = 45</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "56",
                    section: "17",
                    question_en: "<p>56. The traffic lights at three different crossings change after 30 seconds, 40 seconds, and 60 seconds respectively. Then in 30 min how many times will they change simultaneously ?</p>",
                    question_hi: "<p>56. तीन अलग-अलग क्रॉसिंगों पर ट्रैफिक लाइटें क्रमशः 30 सेकंड, 40 सेकंड और 60 सेकंड के बाद बदलती हैं। 30 मिनट में वे कितनी बार एक साथ बदलेंगी ?</p>",
                    options_en: [
                        "<p>15</p>",
                        "<p>13</p>",
                        "<p>18</p>",
                        "<p>19</p>"
                    ],
                    options_hi: [
                        "<p>15</p>",
                        "<p>13</p>",
                        "<p>18</p>",
                        "<p>19</p>"
                    ],
                    solution_en: "<p>56.(a)<br>LCM (30 , 40 , 60)sec. = 120 seconds<br>120 seconds = 2 minute<br>So, light change in 30 minutes = <math display=\"inline\"><mfrac><mrow><mn>30</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = 15 times</p>",
                    solution_hi: "<p>56.(a)<br>LCM (30 , 40 , 60)सेकंड = 120 सेकंड <br>120 सेकंड = 2 मिनट <br>अतः , 30 मिनट में लाइटें बदलेंगी = <math display=\"inline\"><mfrac><mrow><mn>30</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = 15 बार</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "57",
                    section: "17",
                    question_en: "<p>57. Mr Gogia invested an amount of ₹ 13,900 divided into two different schemes A and B at the simple interest rate of 14% pa and 11% pa, respectively. If the total amount of simple interest earned in 2 years is ₹3,508, what was the amount invested in scheme B?</p>",
                    question_hi: "<p>57. श्रीमान गोगिया ने ₹13,900 की राशि को विभाजित करके दो अलग-अलग योजनाओं A और B में क्रमशः 14% वार्षिक और 11% वार्षिक साधारण ब्याज दर पर निवेश किया। यदि 2 वर्षों मेंअर्जित साधारण ब्याज की कुल राशि ₹3,508 है, तो योजना B में निवेश की गई राशि कितनी थी?</p>",
                    options_en: [
                        "<p>₹7,200</p>",
                        "<p>₹6,500</p>",
                        "<p>₹6,400</p>",
                        "<p>₹7,500</p>"
                    ],
                    options_hi: [
                        "<p>₹7,200</p>",
                        "<p>₹6,500</p>",
                        "<p>₹6,400</p>",
                        "<p>₹7,500</p>"
                    ],
                    solution_en: "<p>57.(c)<br>Let amount invested in scheme A = <math display=\"inline\"><mi>x</mi></math><br>And amount invested in scheme B = 13,900 - <math display=\"inline\"><mi>x</mi></math><br>According to question,<br>&rArr;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mrow><mi>x</mi><mo>&#215;</mo><mn>14</mn><mo>&#215;</mo><mn>2</mn></mrow><mn>100</mn></mfrac></mstyle></math>+<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mrow><mo>(</mo><mn>13</mn><mo>,</mo><mn>900</mn><mo>-</mo><mi>x</mi><mo>)</mo><mo>&#215;</mo><mn>11</mn><mo>&#215;</mo><mn>2</mn></mrow><mn>100</mn></mfrac></mstyle></math> = 3508<br>&rArr; 3508 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mrow><mn>28</mn><mi>x</mi><mo>+</mo><mn>305800</mn><mo>-</mo><mn>22</mn><mo>&#160;</mo><mi>x</mi></mrow><mn>100</mn></mfrac></mstyle></math><br>&rArr; 3508 =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mrow><mn>6</mn><mi>&#160;</mi><mi>x</mi></mrow><mn>100</mn></mfrac></mstyle></math> + 3058<br>&rArr; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>6</mn><mi>&#160;</mi><mi>x</mi></mrow><mn>100</mn></mfrac></math> = 3508 - 3058 = 450<br>&rArr; x = ₹ 7500 <br>So, the amount invested in scheme B = 13900 - 7500 = ₹ 6400</p>",
                    solution_hi: "<p>57.(c)<br>माना योजना A में निवेश की गई राशि = <math display=\"inline\"><mi>x</mi></math><br>और योजना B में निवेश की गई राशि = 13,900 - <math display=\"inline\"><mi>x</mi></math><br>प्रश्न के अनुसार,<br>&rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mrow><mi>x</mi><mo>&#215;</mo><mn>14</mn><mo>&#215;</mo><mn>2</mn></mrow><mn>100</mn></mfrac></mstyle></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mrow><mo>(</mo><mn>13</mn><mo>,</mo><mn>900</mn><mo>-</mo><mi>x</mi><mo>)</mo><mo>&#215;</mo><mn>11</mn><mo>&#215;</mo><mn>2</mn></mrow><mn>100</mn></mfrac></mstyle></math> = 3508<br>&rArr; 3508 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mrow><mn>28</mn><mi>x</mi><mo>+</mo><mn>305800</mn><mo>-</mo><mn>22</mn><mi>x</mi></mrow><mn>100</mn></mfrac></mstyle></math><br>&rArr; 3508 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mrow><mn>6</mn><mi>&#160;</mi><mi>x</mi></mrow><mn>100</mn></mfrac></mstyle></math> + 3058<br>&rArr; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>6</mn><mi>&#160;</mi><mi>x</mi></mrow><mn>100</mn></mfrac></math> = 3508 - 3058 = 450<br>&rArr; x = ₹ 7500 <br>तो, योजना B में निवेश की गई राशि = 13900 - 7500 = ₹ 6400</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "58",
                    section: "17",
                    question_en: "<p>58. <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742268462780.png\" alt=\"rId53\" width=\"161\" height=\"160\"> <br>In the figure shown above, quadrilateral PQRS has its vertices on the circumference of the circle with centre O. If m&ang;QOS = 20x&deg; and m&ang;QRS = 26x&deg;, then what is the value of &lsquo;x&rsquo;?</p>",
                    question_hi: "<p>58. <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742268462916.png\" alt=\"rId54\" width=\"163\" height=\"160\"> <br>ऊपर दर्शाए गए चित्र में चतुर्भुज PQRS के शीर्ष O केंद्र वाले वृत्त की परिधि पर स्थित हैं। यदि m&ang;QOS = 20x&deg; और m&ang;QRS = 26x&deg; है, तो \'x\' का मान क्या है?</p>",
                    options_en: [
                        "<p>6</p>",
                        "<p>5</p>",
                        "<p>4</p>",
                        "<p>3</p>"
                    ],
                    options_hi: [
                        "<p>6</p>",
                        "<p>5</p>",
                        "<p>4</p>",
                        "<p>3</p>"
                    ],
                    solution_en: "<p>58.(b) <br>&ang;QPS = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac><mo>&#215;</mo></math>&ang;QOS = 10x&deg;<br>Now,<br>&ang;QPS + &ang;QRS = <math display=\"inline\"><msup><mrow><mn>180</mn></mrow><mrow><mo>&#176;</mo></mrow></msup></math> &hellip; (opposite angles of cyclic quadrilateral)<br>10<math display=\"inline\"><msup><mrow><mi>x</mi></mrow><mrow><mo>&#176;</mo></mrow></msup></math>+ 26x&deg; = 180&deg;<br><math display=\"inline\"><mi>x</mi></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mrow><mn>180</mn><mo>&#176;</mo></mrow><mrow><mn>36</mn><mo>&#176;</mo></mrow></mfrac></mstyle></math> = 5</p>",
                    solution_hi: "<p>58.(b) <br>&ang;QPS = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac><mo>&#215;</mo></math>&ang;QOS = 10x&deg;<br>अब,<br>&ang;QPS + &ang;QRS = <math display=\"inline\"><msup><mrow><mn>180</mn></mrow><mrow><mo>&#176;</mo></mrow></msup></math> &hellip; (चक्रीय चतुर्भुज के विपरीत कोण)<br>10<math display=\"inline\"><msup><mrow><mi>x</mi></mrow><mrow><mo>&#176;</mo></mrow></msup></math>+ 26x&deg; = 180&deg;<br><math display=\"inline\"><mi>x</mi></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mrow><mn>180</mn><mo>&#176;</mo></mrow><mrow><mn>36</mn><mo>&#176;</mo></mrow></mfrac></mstyle></math> = 5</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "59",
                    section: "17",
                    question_en: "<p>59 The ratio of the speed of a boat to that of the current water is 35 : 8. The boat goes along with the current in 5 hours 10 minutes. What will be the time taken by the boat to come back?</p>",
                    question_hi: "<p>59. एक नाव की गति और धारा की गति का अनुपात 35 : 8 है। नाव धारा के अनुकूल 5 घंटे 10 मिनट में जाती है। नाव को वापस आने में कितना समय लगेगा?</p>",
                    options_en: [
                        "<p>5 hours 15 minutes 58 seconds</p>",
                        "<p>6 hours 45 minutes 10 seconds</p>",
                        "<p>8 hours 13 minutes 42 seconds</p>",
                        "<p>9 hours 30 minutes 49 seconds</p>"
                    ],
                    options_hi: [
                        "<p>5 घंटे 15 मिनट 58 सेकंड</p>",
                        "<p>6 घंटे 45 मिनट 10 सेकंड</p>",
                        "<p>8 घंटे 13 मिनट 42 सेकंड</p>",
                        "<p>9 घंटे 30 मिनट 49 सेकंड</p>"
                    ],
                    solution_en: "<p>59.(c)<br>Ratio <math display=\"inline\"><mo>&#8594;</mo></math> Downstream : Upstream<br>Speed <math display=\"inline\"><mo>&#8594;</mo></math>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 43&nbsp; &nbsp; :&nbsp; &nbsp;27<br>Time <math display=\"inline\"><mo>&#8594;</mo></math>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;27&nbsp; &nbsp; :&nbsp; &nbsp;43<br>ATQ, <br>27 unit -------------- 5<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>6</mn></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>31</mn><mn>6</mn></mfrac></mstyle></math> hrs<br>Then, 43 unit -------------- <math display=\"inline\"><mfrac><mrow><mn>31</mn></mrow><mrow><mn>6</mn><mo>&#215;</mo><mn>27</mn></mrow></mfrac><mo>&#215;</mo></math> 43 =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>1333</mn><mn>162</mn></mfrac></mstyle></math> = 8 hours 13 minute 42 seconds</p>",
                    solution_hi: "<p>59.(c)<br>अनुपात <math display=\"inline\"><mo>&#8594;</mo></math> अनुप्रवाह&nbsp; :&nbsp; ऊर्ध्वप्रवाह<br>गति <math display=\"inline\"><mo>&#8594;</mo></math>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 43&nbsp; :&nbsp; &nbsp;27<br>समय <math display=\"inline\"><mo>&#8594;</mo></math>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 27&nbsp; :&nbsp; &nbsp;43<br>प्रश्न के अनुसार, <br>27 इकाई (अनुप्रवाह ) = 5<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math><br>1 इकाई = <math display=\"inline\"><mfrac><mrow><mn>31</mn></mrow><mrow><mn>6</mn><mo>&#215;</mo><mn>27</mn></mrow></mfrac></math><br>43 इकाई (ऊर्ध्वप्रवाह समय ) = <math display=\"inline\"><mfrac><mrow><mn>31</mn></mrow><mrow><mn>6</mn><mo>&#215;</mo><mn>27</mn></mrow></mfrac><mo>&#215;</mo></math> 43 =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>1333</mn><mn>162</mn></mfrac></mstyle></math> = 8 घंटे 13 मिनट 42 सेकंड</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "60",
                    section: "17",
                    question_en: "<p>60. The value of 66 &divide; [34 - (32 of 2 - 4) &divide; (4 &times; 15)] is equal to:</p>",
                    question_hi: "<p>60. 66 &divide; [34 - (32 of 2 - 4) &divide; (4 &times; 15)] का मान निम्न में से किसके बराबर होगा?</p>",
                    options_en: [
                        "<p>2</p>",
                        "<p>1</p>",
                        "<p>0</p>",
                        "<p>3</p>"
                    ],
                    options_hi: [
                        "<p>2</p>",
                        "<p>1</p>",
                        "<p>0</p>",
                        "<p>3</p>"
                    ],
                    solution_en: "<p>60.(a)<br>66 &divide; [34 - (32 of 2 - 4) &divide; (4 &times; 15)]<br>66 &divide; [34 - (64 - 4) &divide; (60)]<br>66 &divide; [34 - 60 &divide; 60]<br>66 &divide; [34 - 1]<br>66 &divide; 33 = 2</p>",
                    solution_hi: "<p>60.(a)<br>66 &divide; [34 - (32 of 2 - 4) &divide; (4 &times; 15)]<br>66 &divide; [34 - (64 - 4) &divide; (60)]<br>66 &divide; [34 - 60 &divide; 60]<br>66 &divide; [34 - 1]<br>66 &divide; 33 = 2</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "61",
                    section: "17",
                    question_en: "<p>61. In right-angled triangle ABC, &ang;B = 90&deg; and angle A and angle C are acute angles.<br>If cosecA = 2<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>2</mn></msqrt></math> , then find the value of sinA.cosC + cosA.sinC.</p>",
                    question_hi: "<p>61. समकोण त्रिभुज ABC में, &ang;B = 90&deg; तथा कोण A एवं कोण C न्यून कोण हैं।<br>यदि cosecA = 2<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>2</mn></msqrt></math>&nbsp;हो, तो sinA.cosC+ cosA.sinC का मान ज्ञात कीजिए।</p>",
                    options_en: [
                        "<p><math display=\"inline\"><msqrt><mn>2</mn></msqrt></math></p>",
                        "<p>1</p>",
                        "<p>0</p>",
                        "<p>2<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>2</mn></msqrt></math></p>"
                    ],
                    options_hi: [
                        "<p><math display=\"inline\"><msqrt><mn>2</mn></msqrt></math></p>",
                        "<p>1</p>",
                        "<p>0</p>",
                        "<p>2<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>2</mn></msqrt></math></p>"
                    ],
                    solution_en: "<p>61.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742268463041.png\" alt=\"rId55\" width=\"103\" height=\"110\"><br>cosecA = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>A</mi><mi>C</mi></mrow><mrow><mi>B</mi><mi>C</mi></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mrow><mn>2</mn><msqrt><mn>2</mn></msqrt></mrow><mn>1</mn></mfrac></mstyle></math><br>AB = <math display=\"inline\"><msqrt><msup><mrow><mo>(</mo><mn>2</mn><msqrt><mn>2</mn></msqrt><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup><mo>-</mo><msup><mrow><mo>(</mo><mn>1</mn><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup></msqrt></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>8</mn><mo>-</mo><mn>1</mn></msqrt></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>7</mn></msqrt></math><br>sinA.cosC + cosA.sinC = <math display=\"inline\"><mfrac><mrow><mi>B</mi><mi>C</mi></mrow><mrow><mi>A</mi><mi>C</mi></mrow></mfrac></math> &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mrow><mi>B</mi><mi>C</mi></mrow><mrow><mi>A</mi><mi>C</mi></mrow></mfrac></mstyle></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mrow><mi>A</mi><mi>B</mi></mrow><mrow><mi>A</mi><mi>C</mi></mrow></mfrac></mstyle></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mrow><mi>A</mi><mi>B</mi></mrow><mrow><mi>A</mi><mi>C</mi></mrow></mfrac></mstyle></math><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><mn>2</mn><msqrt><mn>2</mn></msqrt></mrow></mfrac></math> &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>1</mn><mrow><mn>2</mn><msqrt><mn>2</mn></msqrt></mrow></mfrac></mstyle></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><msqrt><mn>7</mn></msqrt><mrow><mn>2</mn><msqrt><mn>2</mn></msqrt></mrow></mfrac></mstyle></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><msqrt><mn>7</mn></msqrt><mrow><mn>2</mn><msqrt><mn>2</mn></msqrt></mrow></mfrac></mstyle></math><br>= <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math> +&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>7</mn><mn>8</mn></mfrac></mstyle></math><br>= 1</p>",
                    solution_hi: "<p>61.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742268463041.png\" alt=\"rId55\" width=\"123\" height=\"131\"><br>cosecA = <math display=\"inline\"><mfrac><mrow><mi>A</mi><mi>C</mi></mrow><mrow><mi>B</mi><mi>C</mi></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mrow><mn>2</mn><msqrt><mn>2</mn></msqrt></mrow><mn>1</mn></mfrac></mstyle></math><br>AB = <math display=\"inline\"><msqrt><msup><mrow><mo>(</mo><mn>2</mn><msqrt><mn>2</mn></msqrt><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup><mo>-</mo><msup><mrow><mo>(</mo><mn>1</mn><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup></msqrt></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>8</mn><mo>-</mo><mn>1</mn></msqrt></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>7</mn></msqrt></math><br>sinA.cosC + cosA.sinC = <math display=\"inline\"><mfrac><mrow><mi>B</mi><mi>C</mi></mrow><mrow><mi>A</mi><mi>C</mi></mrow></mfrac></math> &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mrow><mi>B</mi><mi>C</mi></mrow><mrow><mi>A</mi><mi>C</mi></mrow></mfrac></mstyle></math> +&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mrow><mi>A</mi><mi>B</mi></mrow><mrow><mi>A</mi><mi>C</mi></mrow></mfrac></mstyle></math> &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mrow><mi>A</mi><mi>B</mi></mrow><mrow><mi>A</mi><mi>C</mi></mrow></mfrac></mstyle></math><br>= <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn><msqrt><mn>2</mn></msqrt></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>1</mn><mrow><mn>2</mn><msqrt><mn>2</mn></msqrt></mrow></mfrac></mstyle></math> +&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><msqrt><mn>7</mn></msqrt><mrow><mn>2</mn><msqrt><mn>2</mn></msqrt></mrow></mfrac></mstyle></math> &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><msqrt><mn>7</mn></msqrt><mrow><mn>2</mn><msqrt><mn>2</mn></msqrt></mrow></mfrac></mstyle></math><br>= <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math> +&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>7</mn><mn>8</mn></mfrac></mstyle></math>= 1</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "62",
                    section: "17",
                    question_en: "<p>62. A dealer buys an article listed at ₹100 and get successive discounts of 10% and 20%. He spends 10% of the cost price on transportation. At what price should he sell the article to earn a profit of 15%?</p>",
                    question_hi: "<p>62. एक विक्रेता ₹100 अंकित मूल्य वाली एक वस्तु खरीदता है और 10% और 20% की क्रमिक छूट प्राप्त करता है। वह क्रय मूल्य का 10% परिवहन पर खर्च करता है। 15% का लाभ अर्जित करने के लिए उसे वस्तु को किस मूल्य पर बेचना चाहिए?</p>",
                    options_en: [
                        "<p>₹76.07</p>",
                        "<p>₹89.08</p>",
                        "<p>₹91.08</p>",
                        "<p>₹81.07</p>"
                    ],
                    options_hi: [
                        "<p>₹76.07</p>",
                        "<p>₹89.08</p>",
                        "<p>₹91.08</p>",
                        "<p>₹81.07</p>"
                    ],
                    solution_en: "<p>62.(c)<br>Marked price of the article = ₹100<br>After two successive discount of 10% and 20% <br>Cost price for dealer = 100 &times; <math display=\"inline\"><mfrac><mrow><mn>9</mn></mrow><mrow><mn>10</mn></mrow></mfrac></math> &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>4</mn><mn>5</mn></mfrac></mstyle></math> = ₹ 72<br>Now, he spends 10% of CP on transportation<br>= 72 &times; 10% = ₹ 7.2<br>Then, actual cost price = ₹72 + ₹7.2 = ₹79.2<br>Selling price of article after 15% profit = 79.2 &times; <math display=\"inline\"><mfrac><mrow><mn>23</mn></mrow><mrow><mn>20</mn></mrow></mfrac></math> = ₹91.08</p>",
                    solution_hi: "<p>62.(c)<br>वस्तु का अंकित मूल्य = ₹100<br>10% और 20% की दो क्रमिक छूट के बाद <br>डीलर के लिए क्रय मूल्य = 100 &times; <math display=\"inline\"><mfrac><mrow><mn>9</mn></mrow><mrow><mn>10</mn></mrow></mfrac></math> &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>4</mn><mn>5</mn></mfrac></mstyle></math> = ₹ 72<br>अब, वह क्रय मूल्य का 10% परिवहन पर खर्च करता है<br>= 72 &times; 10% = ₹ 7.2<br>फिर, वास्तविक लागत मूल्य = ₹72 + ₹7.2 = ₹79.2<br>15% लाभ के बाद वस्तु का विक्रय मूल्य = 79.2 &times; <math display=\"inline\"><mfrac><mrow><mn>23</mn></mrow><mrow><mn>20</mn></mrow></mfrac></math> = ₹91.08</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "63",
                    section: "17",
                    question_en: "<p>63. Sita takes a loan of Rs.35,000 at an interest rate of 10% compound interest, compounded annually. She agrees to pay two equal instalments in 2 years, one instalment at the end of each year. Find the value of each instalment.(Round off to the nearest integer)</p>",
                    question_hi: "<p>63. सीता Rs.35,000 का ऋण, वार्षिक रूप से चक्रवृद्धि होने वाली 10% वार्षिक चक्रवृद्धि ब्याज दर पर लेती है। वह 2 वर्षों में दो समान किश्तों में अर्थात् प्रत्येक वर्ष में अंत में एक किश्त का भुगतान करने के लिए सहमत है। प्रत्येक किश्त की राशि ज्ञात कीजिए। (उत्तर को निकटतम पूर्णांक तक पूर्णांकित करें)</p>",
                    options_en: [
                        "<p>₹20,167</p>",
                        "<p>₹40,167</p>",
                        "<p>₹10,167</p>",
                        "<p>₹30,167</p>"
                    ],
                    options_hi: [
                        "<p>₹20,167</p>",
                        "<p>₹40,167</p>",
                        "<p>₹10,167</p>",
                        "<p>₹30,167</p>"
                    ],
                    solution_en: "<p>63.(a) Rate = 10% = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>10</mn></mrow></mfrac></math><br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;Principal&nbsp; &nbsp;:&nbsp; &nbsp; Installment<br>1st yr <math display=\"inline\"><mo>&#8594;</mo></math>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 10&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; 11) <strong>&times; 11</strong><br>2nd yr <math display=\"inline\"><mo>&#8594;</mo></math>&nbsp; &nbsp; &nbsp; &nbsp; 102&nbsp; &nbsp; :&nbsp; &nbsp; 112 <br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;----------------------------<br>&nbsp;Total <math display=\"inline\"><mo>&#8594;</mo></math>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;210&nbsp; &nbsp; :&nbsp; &nbsp; &nbsp;242<br>Value of each installment = 121 units<br>According to the question,<br>210 units = Rs. 35,000<br>installment (121 units) = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>35000</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>121</mn></mrow><mn>210</mn></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>60500</mn><mn>3</mn></mfrac></mstyle></math> = 20,166.66 &asymp; ₹ 20,167 (nearest integer)</p>",
                    solution_hi: "<p>63.(a) दर = 10% =&nbsp;<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>10</mn></mrow></mfrac></math><br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; मूलधन&nbsp; &nbsp;:&nbsp; &nbsp; किस्त<br>प्रथम वर्ष <math display=\"inline\"><mo>&#8594;</mo></math>&nbsp; &nbsp;10&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; 11) &times; 11<br>द्वितीय वर्ष <math display=\"inline\"><mo>&#8594;</mo></math> 102&nbsp; &nbsp;:&nbsp; &nbsp; &nbsp;112 <br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; ----------------------------<br>&nbsp; &nbsp; कुल <math display=\"inline\"><mo>&#8594;</mo></math>&nbsp; &nbsp; &nbsp; 210&nbsp; :&nbsp; &nbsp; &nbsp;242<br>प्रत्येक किस्त का मूल्य = 121 इकाई <br>प्रश्न के अनुसार,<br>210 इकाई = Rs. 35,000<br>किस्त (121 इकाई ) = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>35000</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>121</mn></mrow><mn>210</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>60500</mn><mn>3</mn></mfrac></mstyle></math>= 20,166.66 &asymp; ₹ 20,167 (निकटतम पूर्णांक)</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "64",
                    section: "17",
                    question_en: "<p>64. The average of the areas of 2 similar triangles is 706.5 m&sup2; whose perimeters are in the ratio of 6 : 11. What is 20% of the difference (in m&sup2;) in areas of both triangles ?</p>",
                    question_hi: "<p>64. दो समरूप त्रिभुजों के क्षेत्रफलों का औसत 706.5 m&sup2; है, जिनके परिमाप 6 : 11 के अनुपात में हैं। दोनों&nbsp;त्रिभुजों के क्षेत्रफलों में अंतर (m&sup2; में) का 20% क्या होगा ?</p>",
                    options_en: [
                        "<p>164</p>",
                        "<p>149</p>",
                        "<p>153</p>",
                        "<p>157</p>"
                    ],
                    options_hi: [
                        "<p>164</p>",
                        "<p>149</p>",
                        "<p>153</p>",
                        "<p>157</p>"
                    ],
                    solution_en: "<p>64.(c)<br>Sum of the area of similar triangles = 706.5 &times; 2 = 1413 m&sup2;<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mi mathvariant=\"bold-italic\">s</mi><mi mathvariant=\"bold-italic\">t</mi><mi mathvariant=\"bold-italic\">&#160;</mi><mi mathvariant=\"bold-italic\">t</mi><mi mathvariant=\"bold-italic\">r</mi><mi mathvariant=\"bold-italic\">i</mi><mi mathvariant=\"bold-italic\">a</mi><mi mathvariant=\"bold-italic\">n</mi><mi mathvariant=\"bold-italic\">g</mi><mi mathvariant=\"bold-italic\">l</mi><mi mathvariant=\"bold-italic\">e</mi><mi mathvariant=\"bold-italic\">\'</mi><mi mathvariant=\"bold-italic\">s</mi><mi mathvariant=\"bold-italic\">&#160;</mi><mi mathvariant=\"bold-italic\">p</mi><mi mathvariant=\"bold-italic\">e</mi><mi mathvariant=\"bold-italic\">r</mi><mi mathvariant=\"bold-italic\">i</mi><mi mathvariant=\"bold-italic\">m</mi><mi mathvariant=\"bold-italic\">e</mi><mi mathvariant=\"bold-italic\">t</mi><mi mathvariant=\"bold-italic\">e</mi><mi mathvariant=\"bold-italic\">r</mi></mrow><mrow><mn>2</mn><mi mathvariant=\"bold-italic\">n</mi><mi mathvariant=\"bold-italic\">d</mi><mi mathvariant=\"bold-italic\">&#160;</mi><mi mathvariant=\"bold-italic\">t</mi><mi mathvariant=\"bold-italic\">r</mi><mi mathvariant=\"bold-italic\">i</mi><mi mathvariant=\"bold-italic\">a</mi><mi mathvariant=\"bold-italic\">n</mi><mi mathvariant=\"bold-italic\">g</mi><mi mathvariant=\"bold-italic\">l</mi><mi mathvariant=\"bold-italic\">e</mi><mi mathvariant=\"bold-italic\">\'</mi><mi mathvariant=\"bold-italic\">s</mi><mi mathvariant=\"bold-italic\">&#160;</mi><mi mathvariant=\"bold-italic\">p</mi><mi mathvariant=\"bold-italic\">e</mi><mi mathvariant=\"bold-italic\">r</mi><mi mathvariant=\"bold-italic\">i</mi><mi mathvariant=\"bold-italic\">m</mi><mi mathvariant=\"bold-italic\">e</mi><mi mathvariant=\"bold-italic\">t</mi><mi mathvariant=\"bold-italic\">e</mi><mi mathvariant=\"bold-italic\">r</mi></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mrow><mi>s</mi><mi>i</mi><mi>d</mi><mi>e</mi><mo>&#160;</mo><mi>o</mi><mi>f</mi><mo>&#160;</mo><mn>1</mn><mi>s</mi><mi>t</mi><mo>&#160;</mo><mi>t</mi><mi>r</mi><mi>i</mi><mi>a</mi><mi>n</mi><mi>g</mi><mi>l</mi><mi>e</mi></mrow><mrow><mi>s</mi><mi>i</mi><mi>d</mi><mi>e</mi><mo>&#160;</mo><mi>o</mi><mi>f</mi><mo>&#160;</mo><mn>2</mn><mi>n</mi><mi>d</mi><mo>&#160;</mo><mi>t</mi><mi>r</mi><mi>i</mi><mi>a</mi><mi>n</mi><mi>g</mi><mi>l</mi><mi>e</mi></mrow></mfrac></mstyle></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>6</mn><mn>11</mn></mfrac></mstyle></math><br><math display=\"inline\"><mfrac><mrow><mi mathvariant=\"bold-italic\">a</mi><mi mathvariant=\"bold-italic\">r</mi><mi mathvariant=\"bold-italic\">e</mi><mi mathvariant=\"bold-italic\">a</mi><mi mathvariant=\"bold-italic\">&#160;</mi><mi mathvariant=\"bold-italic\">o</mi><mi mathvariant=\"bold-italic\">f</mi><mi mathvariant=\"bold-italic\">&#160;</mi><mn>1</mn><mi mathvariant=\"bold-italic\">s</mi><mi mathvariant=\"bold-italic\">t</mi><mi mathvariant=\"bold-italic\">&#160;</mi><mi mathvariant=\"bold-italic\">t</mi><mi mathvariant=\"bold-italic\">r</mi><mi mathvariant=\"bold-italic\">i</mi><mi mathvariant=\"bold-italic\">a</mi><mi mathvariant=\"bold-italic\">n</mi><mi mathvariant=\"bold-italic\">g</mi><mi mathvariant=\"bold-italic\">l</mi><mi mathvariant=\"bold-italic\">e</mi></mrow><mrow><mi mathvariant=\"bold-italic\">a</mi><mi mathvariant=\"bold-italic\">r</mi><mi mathvariant=\"bold-italic\">e</mi><mi mathvariant=\"bold-italic\">a</mi><mi mathvariant=\"bold-italic\">&#160;</mi><mi mathvariant=\"bold-italic\">o</mi><mi mathvariant=\"bold-italic\">f</mi><mi mathvariant=\"bold-italic\">&#160;</mi><mn>2</mn><mi mathvariant=\"bold-italic\">n</mi><mi mathvariant=\"bold-italic\">d</mi><mi mathvariant=\"bold-italic\">&#160;</mi><mi mathvariant=\"bold-italic\">t</mi><mi mathvariant=\"bold-italic\">r</mi><mi mathvariant=\"bold-italic\">i</mi><mi mathvariant=\"bold-italic\">a</mi><mi mathvariant=\"bold-italic\">n</mi><mi mathvariant=\"bold-italic\">g</mi><mi mathvariant=\"bold-italic\">l</mi><mi mathvariant=\"bold-italic\">e</mi></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mstyle displaystyle=\"false\"><mfrac><mrow><mi>s</mi><mi>i</mi><mi>d</mi><mi>e</mi><mo>&#160;</mo><mi>o</mi><mi>f</mi><mo>&#160;</mo><mn>1</mn><mi>s</mi><mi>t</mi><mo>&#160;</mo><mi>t</mi><mi>r</mi><mi>i</mi><mi>a</mi><mi>n</mi><mi>g</mi><mi>l</mi><mi>e</mi></mrow><mrow><mi>s</mi><mi>i</mi><mi>d</mi><mi>e</mi><mo>&#160;</mo><mi>o</mi><mi>f</mi><mo>&#160;</mo><mn>2</mn><mi>n</mi><mi>d</mi><mo>&#160;</mo><mi>t</mi><mi>r</mi><mi>i</mi><mi>a</mi><mi>n</mi><mi>g</mi><mi>l</mi><mi>e</mi></mrow></mfrac></mstyle><mo>)</mo></mrow><mn>2</mn></msup></math><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mfrac><mn>6</mn><mn>11</mn></mfrac><mo>)</mo></mrow><mn>2</mn></msup></math>= <math display=\"inline\"><mfrac><mrow><mn>36</mn></mrow><mrow><mn>121</mn></mrow></mfrac></math><br>Sum of both triangles area = 36 + 121 = 157 units<br>157 units = 1413 m&sup2;<br>Area of 1st triangle (36 units) = <math display=\"inline\"><mfrac><mrow><mn>1413</mn></mrow><mrow><mn>157</mn></mrow></mfrac></math> &times; 36 = 324 m&sup2;<br>Area of 2nd triangle (121 units) = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1413</mn><mn>157</mn></mfrac></math> &times; 121 = 1089 m&sup2;<br>Required value = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><mn>1089</mn><mo>-</mo><mn>324</mn><mo>)</mo></math> &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>1</mn><mn>5</mn></mfrac></mstyle></math> = 153 m&sup2;</p>",
                    solution_hi: "<p>64.(c)<br>समरूप त्रिभुजों के क्षेत्रफल का योग = 706.5 &times; 2 = 1413 m&sup2;<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi mathvariant=\"bold\">&#2346;&#2361;&#2354;&#2375;</mi><mi mathvariant=\"bold\">&#160;</mi><mi mathvariant=\"bold\">&#2340;&#2381;&#2352;&#2367;&#2349;&#2369;&#2332;</mi><mi mathvariant=\"bold\">&#160;</mi><mi mathvariant=\"bold\">&#2325;&#2366;</mi><mi mathvariant=\"bold\">&#160;</mi><mi mathvariant=\"bold\">&#2346;&#2352;&#2367;&#2350;&#2366;&#2346;</mi><mo>,</mo></mrow><mrow><mi mathvariant=\"bold\">&#2342;&#2370;&#2360;&#2352;&#2375;</mi><mo mathvariant=\"bold\">&#160;&#160;</mo><mi mathvariant=\"bold\">&#2340;&#2381;&#2352;&#2367;&#2349;&#2369;&#2332;</mi><mi mathvariant=\"bold\">&#160;</mi><mi mathvariant=\"bold\">&#2325;&#2366;</mi><mi mathvariant=\"bold\">&#160;</mi><mi mathvariant=\"bold\">&#2346;&#2352;&#2367;&#2350;&#2366;&#2346;</mi><mo>,</mo></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mrow><mi>&#2346;&#2361;&#2354;&#2375;</mi><mo>&#160;</mo><mi>&#2340;&#2381;&#2352;&#2367;&#2349;&#2369;&#2332;</mi><mo>&#160;</mo><mi>&#2325;&#2368;</mi><mo>&#160;</mo><mi>&#2349;&#2369;&#2332;&#2366;</mi></mrow><mrow><mi>&#2342;&#2370;&#2360;&#2352;&#2375;</mi><mo>&#160;</mo><mi>&#2340;&#2381;&#2352;&#2367;&#2349;&#2369;&#2332;</mi><mo>&#160;</mo><mi>&#2325;&#2368;</mi><mo>&#160;</mo><mi>&#2349;&#2369;&#2332;&#2366;</mi></mrow></mfrac></mstyle><mo>=</mo><mstyle displaystyle=\"false\"><mfrac><mn>6</mn><mn>11</mn></mfrac></mstyle></math><br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi mathvariant=\"bold\">&#2346;&#2361;&#2354;&#2375;</mi><mi mathvariant=\"bold\">&#160;</mi><mi mathvariant=\"bold\">&#2340;&#2381;&#2352;&#2367;&#2349;&#2369;&#2332;</mi><mi mathvariant=\"bold\">&#160;</mi><mi mathvariant=\"bold\">&#2325;&#2366;</mi><mi mathvariant=\"bold\">&#160;</mi><mi mathvariant=\"bold\">&#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;&#2347;&#2354;</mi></mrow><mrow><mi mathvariant=\"bold\">&#2342;&#2370;&#2360;&#2352;&#2375;</mi><mi mathvariant=\"bold\">&#160;</mi><mi mathvariant=\"bold\">&#2340;&#2381;&#2352;&#2367;&#2349;&#2369;&#2332;</mi><mi mathvariant=\"bold\">&#160;</mi><mi mathvariant=\"bold\">&#2325;&#2366;</mi><mi mathvariant=\"bold\">&#160;</mi><mi mathvariant=\"bold\">&#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;&#2347;&#2354;</mi></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mstyle displaystyle=\"false\"><mfrac><mrow><mi>&#2346;&#2361;&#2354;&#2375;</mi><mo>&#160;</mo><mi>&#2340;&#2381;&#2352;&#2367;&#2349;&#2369;&#2332;</mi><mo>&#160;</mo><mi>&#2325;&#2368;</mi><mo>&#160;</mo><mi>&#2349;&#2369;&#2332;&#2366;</mi></mrow><mrow><mi>&#2342;&#2370;&#2360;&#2352;&#2375;</mi><mo>&#160;</mo><mi>&#2340;&#2381;&#2352;&#2367;&#2349;&#2369;&#2332;</mi><mo>&#160;</mo><mi>&#2325;&#2368;</mi><mo>&#160;</mo><mi>&#2349;&#2369;&#2332;&#2366;</mi></mrow></mfrac></mstyle><mo>)</mo></mrow><mn>2</mn></msup></math><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mfrac><mn>6</mn><mn>11</mn></mfrac><mo>)</mo></mrow><mn>2</mn></msup></math>=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>36</mn><mn>121</mn></mfrac></mstyle></math><br>दोनों त्रिभुजों के क्षेत्रफल का योग = 36 + 121 = 157 इकाई <br>157 इकाई = 1413 m&sup2;<br>पहले त्रिभुज का क्षेत्रफल (36 इकाई) = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1413</mn><mn>157</mn></mfrac></math> &times; 36 = 324 m&sup2;<br>दूसरे<strong id=\"docs-internal-guid-402069b5-7fff-7099-acd8-a8892ffeea4c\"> </strong>त्रिभुज का क्षेत्रफल(121 इकाई) = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>1413</mn><mn>157</mn></mfrac></mstyle></math> &times; 121 = 1089 m&sup2;<br>आवश्यक मान = (1089 - 324)&nbsp;&times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>1</mn><mn>5</mn></mfrac></mstyle></math> = 153 m&sup2;</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "65",
                    section: "17",
                    question_en: "<p>65. Study the given graph carefully and answer the question that follows. <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742268463182.png\" alt=\"rId56\" width=\"348\" height=\"200\"><br>On the basis of this multiple bar graph, What is the change in the number of Literature books from the year 2020 to the year 2023.</p>",
                    question_hi: "<p>65. दिए गए ग्राफ का ध्यानपूर्वक अध्ययन कीजिए और नीचे दिए गए प्रश्न का उत्तर दीजिए।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742268463413.png\" alt=\"rId57\" width=\"334\" height=\"201\"> <br>इस बहु दंड आलेख (multiple bar graph) के आधार पर, वर्ष 2020 से वर्ष 2023 तक साहित्य की पुस्तकों की संख्या में क्या परिवर्तन हुआ?</p>",
                    options_en: [
                        "<p>1000 more</p>",
                        "<p>2000 more</p>",
                        "<p>2000 less</p>",
                        "<p>No change</p>"
                    ],
                    options_hi: [
                        "<p>1000 अधिक</p>",
                        "<p>2000 अधिक</p>",
                        "<p>2000 कम</p>",
                        "<p>कोई परिवर्तन नहीं</p>"
                    ],
                    solution_en: "<p>65.(c)<br>Change in the number of literature book = 5000 - 3000 = 2000 less</p>",
                    solution_hi: "<p>65.(c)<br>साहित्य पुस्तक की संख्या में परिवर्तन = 5000 - 3000 = 2000 कम</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "66",
                    section: "17",
                    question_en: "<p>66. If m + n = 24, then (m -16)&sup3; + (n - 8)&sup3; is __________.</p>",
                    question_hi: "<p>66. यदि m + n = 24 है, तो तब&nbsp;(m -16)&sup3; + (n - 8)&sup3; का मान ________ है।</p>",
                    options_en: [
                        "<p>0</p>",
                        "<p>40</p>",
                        "<p>576</p>",
                        "<p>320</p>"
                    ],
                    options_hi: [
                        "<p>0</p>",
                        "<p>40</p>",
                        "<p>576</p>",
                        "<p>320</p>"
                    ],
                    solution_en: "<p>66.(a) <br>m + n = 24<br>Let m = 12<br>n = 12<br><math display=\"inline\"><mo>&#8658;</mo></math> (m -16)&sup3; + (n - 8)&sup3; = (12 -16)&sup3; + (12 - 8)&sup3;<br>= (-4)&sup3; + (4)&sup3; = 0</p>",
                    solution_hi: "<p>66.(a) <br>m + n = 24<br>माना , m = 12<br>n = 12<br><math display=\"inline\"><mo>&#8658;</mo></math>(m -16)&sup3; + (n - 8)&sup3; = (12 -16)&sup3; + (12 - 8)&sup3;<br>= (-4)&sup3; + (4)&sup3; = 0</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "67",
                    section: "17",
                    question_en: "<p>67.I forgot the last digit of a 7-digit telephone number. If one randomly dialed the final 3 digits after correctly dialing the first four, then what is the chance of dialing the correct number ?</p>",
                    question_hi: "<p>67. मैं 7 - अंकीय टेलीफोन नंबर का अंतिम अंक भूल गया। यदि एक व्यक्ति पहले चार को सही ढंग से डायल करने के बाद अंतिम 3 अंकों को यादृच्छिक रूप से डायल करता है, तो सही संख्या डायल करने की प्रायिकता क्या है?</p>",
                    options_en: [
                        "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>999</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>1001</mn></mrow></mfrac><mi>&#160;</mi></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>1000</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>1003</mn></mrow></mfrac></math></p>"
                    ],
                    options_hi: [
                        "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>999</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mi>&#160;</mi><mfrac><mrow><mn>1</mn></mrow><mrow><mn>1001</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>1000</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>1003</mn></mrow></mfrac></math></p>"
                    ],
                    solution_en: "<p>67.(c) It is given that the last three digits are randomly dialed, then each of the digits can be selected out of 10 digits(0, 1, 2, 3, 4, 5, 6, 7, 8, 9) in 10 ways.<br>Required probability = (<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>10</mn></mfrac></math>)&sup3; =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>1</mn><mn>1000</mn></mfrac></mstyle></math></p>",
                    solution_hi: "<p>67.(c) दिया गया है कि अंतिम तीन अंक यादृच्छिक रूप से डायल किए जाते हैं। तो प्रत्येक अंक को 10 अंकों में (0,1,2,3,4,5,6,7,8,9) से 10 तरीकों से चुना जा सकता है।<br>आवश्यक प्रायिकता = (<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>10</mn></mrow></mfrac></math>)&sup3; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>1</mn><mn>1000</mn></mfrac></mstyle></math></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "68",
                    section: "17",
                    question_en: "<p>68. If <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>s</mi><mi>e</mi><mi>c</mi><mi>&#160;</mi><mi>&#945;</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>t</mi><mi>a</mi><mi>n</mi><mi>&#160;</mi><mi>&#945;</mi></mrow><mrow><mi>s</mi><mi>e</mi><mi>c</mi><mi>&#160;</mi><mi>&#945;</mi><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mi>&#160;</mi><mi>t</mi><mi>a</mi><mi>n</mi><mi>&#160;</mi><mi>&#945;</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>7</mn><mn>4</mn></mfrac></mstyle></math>, then find the value of cosec &alpha;.</p>",
                    question_hi: "<p>68. यदि <math display=\"inline\"><mfrac><mrow><mi>s</mi><mi>e</mi><mi>c</mi><mi>&#160;</mi><mi>&#945;</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>t</mi><mi>a</mi><mi>n</mi><mi>&#160;</mi><mi>&#945;</mi></mrow><mrow><mi>s</mi><mi>e</mi><mi>c</mi><mi>&#160;</mi><mi>&#945;</mi><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mi>&#160;</mi><mi>t</mi><mi>a</mi><mi>n</mi><mi>&#160;</mi><mi>&#945;</mi></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>7</mn><mn>4</mn></mfrac></mstyle></math> है, तो cosec &alpha; का मान ज्ञात कीजिए।</p>",
                    options_en: [
                        "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>11</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>11</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>11</mn></mrow></mfrac></math></p>"
                    ],
                    options_hi: [
                        "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>11</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>11</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>11</mn></mrow></mfrac></math></p>"
                    ],
                    solution_en: "<p>68.(c)<br><math display=\"inline\"><mfrac><mrow><mi>s</mi><mi>e</mi><mi>c</mi><mi>&#160;</mi><mi>&#945;</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>t</mi><mi>a</mi><mi>n</mi><mi>&#160;</mi><mi>&#945;</mi></mrow><mrow><mi>s</mi><mi>e</mi><mi>c</mi><mi>&#160;</mi><mi>&#945;</mi><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mi>&#160;</mi><mi>t</mi><mi>a</mi><mi>n</mi><mi>&#160;</mi><mi>&#945;</mi></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>7</mn><mn>4</mn></mfrac></mstyle></math><br>By componendo and dividendo we get;<br><math display=\"inline\"><mfrac><mrow><mi>s</mi><mi>e</mi><mi>c</mi><mi>&#160;</mi><mi>&#945;</mi><mi>&#160;</mi></mrow><mrow><mi>t</mi><mi>a</mi><mi>n</mi><mi>&#160;</mi><mi>&#945;</mi></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>11</mn><mn>3</mn></mfrac></mstyle></math><br>sec<math display=\"inline\"><mi>&#945;</mi><mi>&#160;</mi></math>&times; cot&alpha; =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>11</mn><mn>3</mn></mfrac></mstyle></math><br><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#945;</mi><mi>&#160;</mi></mrow></mfrac></math> &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mrow><mi>cos</mi><mi>&#945;</mi></mrow><mrow><mi>sin</mi><mi>&#945;</mi></mrow></mfrac></mstyle></math> =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>11</mn><mn>3</mn></mfrac></mstyle></math><br><math display=\"inline\"><mfrac><mrow><mn>1</mn><mi>&#160;</mi></mrow><mrow><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#160;</mi><mi>&#160;</mi><mi>&#945;</mi></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>11</mn><mn>3</mn></mfrac></mstyle></math> &rArr; cosec&alpha; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>11</mn><mn>3</mn></mfrac></mstyle></math></p>",
                    solution_hi: "<p>68.(c)<br><math display=\"inline\"><mfrac><mrow><mi>s</mi><mi>e</mi><mi>c</mi><mi>&#160;</mi><mi>&#945;</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>t</mi><mi>a</mi><mi>n</mi><mi>&#160;</mi><mi>&#945;</mi></mrow><mrow><mi>s</mi><mi>e</mi><mi>c</mi><mi>&#160;</mi><mi>&#945;</mi><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mi>&#160;</mi><mi>t</mi><mi>a</mi><mi>n</mi><mi>&#160;</mi><mi>&#945;</mi></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>7</mn><mn>4</mn></mfrac></mstyle></math><br>योगांतरानुपात का उपयोग करने पर <br><math display=\"inline\"><mfrac><mrow><mi>s</mi><mi>e</mi><mi>c</mi><mi>&#160;</mi><mi>&#945;</mi><mi>&#160;</mi></mrow><mrow><mi>t</mi><mi>a</mi><mi>n</mi><mi>&#160;</mi><mi>&#945;</mi></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>11</mn><mn>3</mn></mfrac></mstyle></math><br>sec<math display=\"inline\"><mi>&#945;</mi><mi>&#160;</mi></math>&times; cot&alpha; =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>11</mn><mn>3</mn></mfrac></mstyle></math><br><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#945;</mi><mi>&#160;</mi></mrow></mfrac></math> &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mrow><mi>cos</mi><mi>&#945;</mi></mrow><mrow><mi>sin</mi><mi>&#945;</mi></mrow></mfrac></mstyle></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>11</mn><mn>3</mn></mfrac></mstyle></math><br><math display=\"inline\"><mfrac><mrow><mn>1</mn><mi>&#160;</mi></mrow><mrow><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#160;</mi><mi>&#160;</mi><mi>&#945;</mi></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>11</mn><mn>3</mn></mfrac></mstyle></math> &rArr; cosec&alpha; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>11</mn><mn>3</mn></mfrac></mstyle></math></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "69",
                    section: "17",
                    question_en: "<p>69. Find the fourth proportion to 1.25, 5.75 and 3.5</p>",
                    question_hi: "<p>69. 1.25, 5.75 और 3.5 का चतुर्थ समानुपाती ज्ञात कीजिए।</p>",
                    options_en: [
                        "<p>4.5</p>",
                        "<p>16.1</p>",
                        "<p>12.5</p>",
                        "<p>6.5</p>"
                    ],
                    options_hi: [
                        "<p>4.5</p>",
                        "<p>16.1</p>",
                        "<p>12.5</p>",
                        "<p>6.5</p>"
                    ],
                    solution_en: "<p>69.(b) Let the fourth proportion be x<br><math display=\"inline\"><mo>&#8658;</mo></math><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mrow><mn>1</mn><mo>.</mo><mn>25</mn></mrow><mrow><mn>5</mn><mo>.</mo><mn>75</mn></mrow></mfrac></mstyle></math>&nbsp;= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mrow><mn>3</mn><mo>.</mo><mn>5</mn></mrow><mi>x</mi></mfrac></mstyle></math><br><math display=\"inline\"><mo>&#8658;</mo></math>x =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mrow><mn>3</mn><mo>.</mo><mn>5</mn><mo>&#215;</mo><mn>5</mn><mo>.</mo><mn>75</mn></mrow><mrow><mn>1</mn><mo>.</mo><mn>25</mn></mrow></mfrac></mstyle></math> = 16.1</p>",
                    solution_hi: "<p>69.(b) माना कि चौथा अनुपात x&nbsp;है <br><math display=\"inline\"><mo>&#8658;</mo></math><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mrow><mn>1</mn><mo>.</mo><mn>25</mn></mrow><mrow><mn>5</mn><mo>.</mo><mn>75</mn></mrow></mfrac></mstyle></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mrow><mn>3</mn><mo>.</mo><mn>5</mn></mrow><mi>x</mi></mfrac></mstyle></math><br><math display=\"inline\"><mo>&#8658;</mo></math>x =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mrow><mn>3</mn><mo>.</mo><mn>5</mn><mo>&#215;</mo><mn>5</mn><mo>.</mo><mn>75</mn></mrow><mrow><mn>1</mn><mo>.</mo><mn>25</mn></mrow></mfrac></mstyle></math> = 16.1</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "70",
                    section: "17",
                    question_en: "<p>70. Two containers of equal capacity are full of mixture of milk and water. In the first, the ratio of milk to water is 3 : 7 and in the second it is 7 : 9, Now both the mixtures are mixed in a bigger container. What is the resulting ratio of milk to water ?</p>",
                    question_hi: "<p>70. समान धारिता के दो कंटेनर दूध और पानी के मिश्रण से भरे हुए हैं। पहले में, दूध और पानी का अनुपात 3 :7 है और दूसरे में 7: 9 है। । अब दोनों मिश्रणों को एक बड़े कंटेनर में मिलाया जाता है। दूध और पानी का परिणामी अनुपात क्या है?</p>",
                    options_en: [
                        "<p>59:101</p>",
                        "<p>57:107</p>",
                        "<p>61:97</p>",
                        "<p>58:103</p>"
                    ],
                    options_hi: [
                        "<p>59:101</p>",
                        "<p>57:107</p>",
                        "<p>61:97</p>",
                        "<p>58:103</p>"
                    ],
                    solution_en: "<p>70.(a)<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;Milk&nbsp; :&nbsp; Water&nbsp; &nbsp;:&nbsp; &nbsp;Total<br>First mixture &rarr;&nbsp; &nbsp; &nbsp; (3&nbsp; &nbsp; :&nbsp; &nbsp; &nbsp;7&nbsp; &nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp;10 ) &times; 8 <br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;24&nbsp; :&nbsp; &nbsp; 56&nbsp; &nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp;80<br>Second mixture &rarr; (7&nbsp; &nbsp;:&nbsp; &nbsp; &nbsp;9&nbsp; &nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp;16 ) &times; 5&nbsp;<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;35&nbsp; :&nbsp; &nbsp; 45&nbsp; &nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp;80<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; _____________________________<br>Final mixture &rarr;&nbsp; &nbsp; &nbsp;59&nbsp; :&nbsp; &nbsp; 101</p>",
                    solution_hi: "<p>70.(a)<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;दूध&nbsp; &nbsp; :&nbsp; &nbsp;पानी&nbsp; &nbsp;:&nbsp; &nbsp; कुल<br>पहला मिश्रण &rarr; ( 3&nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; 7&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; 10 ) &times; 8 <br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 24&nbsp; &nbsp;:&nbsp; &nbsp; &nbsp;56&nbsp; &nbsp; :&nbsp; &nbsp; &nbsp;80<br>दूसरा मिश्रण &rarr; (7&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp; 9&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; 16 ) &times; 5&nbsp;<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 35&nbsp; &nbsp;:&nbsp; &nbsp; &nbsp;45&nbsp; &nbsp; :&nbsp; &nbsp; &nbsp;80<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;_____________________________<br>अंतिम मिश्रण &rarr; 59&nbsp; &nbsp;:&nbsp; &nbsp; &nbsp;101</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "71",
                    section: "17",
                    question_en: "<p>71. Rohan takes 10 hours to mow a large lawn. He and Mohan together can mow it in 4 hours. How long will Mohan take to mow the lawn if he works alone?</p>",
                    question_hi: "<p>71. रोहन घास के एक बड़े मैदान को काटने में 10 घंटे लेता है। वह और मोहन मिलकर उसी को 4 घंटे में काट सकते हैं । यदि मोहन अकेले काम करता है तो उसे मैदान की घास काटने में कितना समय लगेगा?</p>",
                    options_en: [
                        "<p><math display=\"inline\"><mfrac><mrow><mn>40</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> hours</p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>10</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> hours</p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>20</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> hours</p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>16</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> hours</p>"
                    ],
                    options_hi: [
                        "<p><math display=\"inline\"><mfrac><mrow><mn>40</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> घंटे</p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>10</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> घंटे</p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>20</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> घंटे</p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>16</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> घंटे</p>"
                    ],
                    solution_en: "<p>71.(c)<br>Total work = LCM(10,4) = 20 unit<br>Efficiency of Rohan = <math display=\"inline\"><mfrac><mrow><mn>20</mn></mrow><mrow><mn>10</mn></mrow></mfrac></math> = 2 unit <br>Efficiency of (Mohan + Rohan) = <math display=\"inline\"><mfrac><mrow><mn>20</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> = 5 unit <br>Then, efficiency of Mohan = 5 - 2 = 3 unit<br>Time taken by Mohan to do whole work alone = <math display=\"inline\"><mfrac><mrow><mn>20</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> hours</p>",
                    solution_hi: "<p>71.(c)<br>कुल कार्य = LCM(10,4) = 20 इकाई<br>रोहन की दक्षता = <math display=\"inline\"><mfrac><mrow><mn>20</mn></mrow><mrow><mn>10</mn></mrow></mfrac></math> = 2 इकाई<br>(मोहन + रोहन) की दक्षता = <math display=\"inline\"><mfrac><mrow><mn>20</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> = 5 इकाई<br>तब, मोहन की दक्षता = 5 - 2 = 3 इकाई<br>मोहन द्वारा पूरा कार्य अकेले करने में लिया गया समय = <math display=\"inline\"><mfrac><mrow><mn>20</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> घंटे</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "72",
                    section: "17",
                    question_en: "<p>72. Two trains start at the same time from stations P and Q and travel towards each other at the speeds of 75 km/h and 100 km/h, respectively. When they meet, it is found that one train has travelled 50 km more than the other. The distance (in km) between two stations is:</p>",
                    question_hi: "<p>72. दो रेलगाड़ियाँ एक ही समय पर स्टेशन P और Q से चलना शुरू करती हैं और क्रमशः 75 km/h और 100 km/h की चाल से एक-दूसरे की ओर बढ़ती हैं। जब दोनों रेलगाड़ियाँ आपस में मिलती है, तो यह पता चलता है कि एक रेलगाड़ी दूसरी रेलगाड़ी से 50 km अधिक यात्रा कर चुकी है। दोनों स्टेशनों के बीच की दूरी (km में) क्या है?</p>",
                    options_en: [
                        "<p>300</p>",
                        "<p>350</p>",
                        "<p>375</p>",
                        "<p>325</p>"
                    ],
                    options_hi: [
                        "<p>300</p>",
                        "<p>350</p>",
                        "<p>375</p>",
                        "<p>325</p>"
                    ],
                    solution_en: "<p>72.(b)<br>Speed &prop; Distance (When time is constant)<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; P&nbsp; &nbsp;:&nbsp; &nbsp;Q<br>Speed <math display=\"inline\"><mo>&#8594;</mo></math>&nbsp; &nbsp; &nbsp;&nbsp; 75&nbsp; :&nbsp; &nbsp;100 = 3 : 4<br>Distance <math display=\"inline\"><mo>&#8594;</mo></math>&nbsp; &nbsp; 3&nbsp; &nbsp;:&nbsp; &nbsp; &nbsp;4<br>Now, (4 - 3) = 1 unit = 50 km<br>Then, (3 + 4) = 7 unit = 7 &times; 50 = 350 km<br>So, the distance between two stations = 350 km</p>",
                    solution_hi: "<p>72.(b)<br>गति &prop; दूरी (जब समय स्थिर हो)<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;P&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; Q<br>गति <math display=\"inline\"><mo>&#8594;</mo></math>&nbsp; &nbsp; &nbsp;75&nbsp; &nbsp;:&nbsp; &nbsp;100 = 3 : 4<br>दूरी <math display=\"inline\"><mo>&#8594;</mo></math>&nbsp; &nbsp; &nbsp; 3&nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; 4<br>अब , (4-3) = 1 इकाई = 50 km<br>तब , (3+4) = 7 इकाई = 7&times;50 = 350 km<br>अतः, दो स्टेशनों के बीच की दूरी = 350 km</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "73",
                    section: "17",
                    question_en: "<p>73. Ramana spends 75% of his income. His income is increased by 25% and he increases&nbsp;his expenditure by 10%. By what percentage are his savings increased?</p>",
                    question_hi: "<p>73. रमण अपनी आय का 75% खर्च करता है। उसकी आय में 25% की वृद्धि होती है और वह अपने व्यय में&nbsp;10% की वृद्धि करता है। उसकी बचत में कितने प्रतिशत की वृद्धि हुई है ?</p>",
                    options_en: [
                        "<p>50%</p>",
                        "<p>60%</p>",
                        "<p>40%</p>",
                        "<p>70%</p>"
                    ],
                    options_hi: [
                        "<p>50%</p>",
                        "<p>60%</p>",
                        "<p>40%</p>",
                        "<p>70%</p>"
                    ],
                    solution_en: "<p>73.(d) <br>Let the income of Ramana is 100 units.<br>Ratio -&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;before&nbsp; : after<br>Income -&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 100&nbsp; &nbsp; :&nbsp; 125 <br>Expenditure -&nbsp; &nbsp; &nbsp;75&nbsp; &nbsp;:&nbsp; &nbsp;82.5<br>&nbsp; &nbsp; &nbsp; &nbsp;-------------------------------------<br>Saving -&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;25&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; 42.5<br>increase % in saving = <math display=\"inline\"><mfrac><mrow><mn>42</mn><mo>.</mo><mn>5</mn><mo>-</mo><mn>25</mn></mrow><mrow><mn>25</mn></mrow></mfrac></math> &times; 100 = 17.5 &times; 4 = 70%</p>",
                    solution_hi: "<p>73.(d) <br>माना कि रमण की आय 100 इकाई है।<br>अनुपात -&nbsp; &nbsp;पहले&nbsp; &nbsp;:&nbsp; &nbsp;बाद में<br>आय -&nbsp; &nbsp; &nbsp; &nbsp;100&nbsp; &nbsp; :&nbsp; &nbsp; 125 <br>व्यय -&nbsp; &nbsp; &nbsp; &nbsp; 75&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp;82.5<br>-------------------------------------<br>बचत -&nbsp; &nbsp; &nbsp; &nbsp;25&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp;42.5<br>बचत में % वृद्धि = <math display=\"inline\"><mfrac><mrow><mn>42</mn><mo>.</mo><mn>5</mn><mo>-</mo><mn>25</mn></mrow><mrow><mn>25</mn></mrow></mfrac></math> &times; 100 = 70%</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "74",
                    section: "17",
                    question_en: "<p>74. Pipes A and B can fill a tank in 6 hours and 8 hours, respectively. Both pipes are opened together for 3 hours. After that pipe A is closed, and B continues to fill the tank. In how many hours will the tank be filled?</p>",
                    question_hi: "<p>74. पाइप A और B एक टैंक को क्रमशः 6 घंटे और 8 घंटे में पूरा भर सकते हैं। दोनों पाइपों को एक साथ 3 घंटे के लिए खोल दिया जाता है। उसके बाद पाइप A को बंद कर दिया जाता है, और पाइप B टैंक को भरना जारी रखता है। टैंक कितने घंटे में पूरा भर जायेगा?</p>",
                    options_en: [
                        "<p>2</p>",
                        "<p>4</p>",
                        "<p>3</p>",
                        "<p>6</p>"
                    ],
                    options_hi: [
                        "<p>2</p>",
                        "<p>4</p>",
                        "<p>3</p>",
                        "<p>6</p>"
                    ],
                    solution_en: "<p>74.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742268463584.png\" alt=\"rId58\" width=\"219\" height=\"150\"><br>Water filled by pipe A and B in 3 hours = (4 + 3) &times; 3 = 21 units<br>Remaining capacity of tank = 24 - 21 = 3 units<br>Water filled by pipe B alone = <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> = 1 hours<br>Total time to fill the tank = 3 + 1 = 4 hours</p>",
                    solution_hi: "<p>74.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742268463713.png\" alt=\"rId59\" width=\"211\" height=\"150\"><br>पाइप A और B द्वारा 3 घंटे में भरा गया पानी = (4 + 3) &times; 3 = 21 इकाई<br>टैंक की शेष क्षमता = 24 - 21 = 3 इकाई<br>अकेले पाइप B द्वारा भरा गया पानी = <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> = 1 घंटा<br>टंकी भरने में लगा कुल समय = 3 + 1 = 4 घंटा</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "75",
                    section: "7",
                    question_en: "<p>75. The average weight of 16 containers is 80 kg. If 8 new containers are added, the new average increases by 2.5 kg. The average weight of the 8 new containers is:</p>",
                    question_hi: "<p>75. 16 पात्रों का औसत भार 80 kg है। यदि 8 नए पात्र इसमें शामिल कर लिए जाते हैं, तो नए औसत में 2.5 kg की वृद्धि हो जाती है। 8 नए पात्रों का औसत भार कितना है?</p>",
                    options_en: [
                        "<p>87.5 kg</p>",
                        "<p>82.5 kg</p>",
                        "<p>87 kg</p>",
                        "<p>160 kg</p>"
                    ],
                    options_hi: [
                        "<p>87.5 kg</p>",
                        "<p>82.5 kg</p>",
                        "<p>87 kg</p>",
                        "<p>160 kg</p>"
                    ],
                    solution_en: "<p>75.(a)<br>16 containers : 8 containers = 2 : 1<br>Let average weight of 8 new containers be <math display=\"inline\"><mi>x</mi></math><br>According to the question,<br><math display=\"inline\"><mfrac><mrow><mn>2</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>80</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>1</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mi>x</mi></mrow><mrow><mn>3</mn></mrow></mfrac></math> = (80 + 2.5)<br>160 + <math display=\"inline\"><mi>x</mi></math> = 3 &times; 82.5<br><math display=\"inline\"><mi>x</mi></math> = 247.5 - 160 = 87.5<br>Hence, required average (<math display=\"inline\"><mi>x</mi></math>) = 87.5 kg</p>",
                    solution_hi: "<p>75.(a)<br>16 कन्टेनर : 8 कन्टेनर = 2 :1<br>माना 8 नए कंटेनरों का औसत वजन <math display=\"inline\"><mi>x</mi></math> है<br>प्रश्न के अनुसार,<br><math display=\"inline\"><mfrac><mrow><mn>2</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>80</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>1</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mi>x</mi></mrow><mrow><mn>3</mn></mrow></mfrac></math> = (80 + 2.5)<br>160 + x&nbsp;= 3 &times; 82.5<br>x = 247.5 - 160 = 87.5<br>अतः, आवश्यक औसत (<math display=\"inline\"><mi>x</mi></math>) = 87.5 किग्रा</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "76",
                    section: "7",
                    question_en: "<p>76. Identify the proper explanation of the error in the given sentence.<br>Mohan insisted to go there.</p>",
                    question_hi: "<p>76. Identify the proper explanation of the error in the given sentence.<br>Mohan insisted to go there.</p>",
                    options_en: [
                        "<p>Error is in the sentence formation. Preposition and gerund are required. The correct sentence should read like this: Mohan insisted on going there.</p>",
                        "<p>Error is with the verb. The correct sentence should read like this: Mohan insist to going there.</p>",
                        "<p>Error is with the tense. The correct sentence should read like this: Mohan insisting to go there.</p>",
                        "<p>Error is with the preposition. The correct sentence should read like this: Mohan insisted go there.</p>"
                    ],
                    options_hi: [
                        "<p>Error is in the sentence formation. Preposition and gerund are required. The correct sentence <br>should read like this: Mohan insisted on going there.</p>",
                        "<p>Error is with the verb. The correct sentence should read like this: Mohan insist to going there.</p>",
                        "<p>Error is with the tense. The correct sentence should read like this: Mohan insisting to go there.</p>",
                        "<p>Error is with the preposition. The correct sentence should read like this: Mohan insisted go there.</p>"
                    ],
                    solution_en: "<p>76.(a) Error is in the sentence formation. Preposition and gerund are required. The correct sentence should read like this: Mohan insisted on going there.</p>",
                    solution_hi: "<p>76.(a) इस sentence का formation गलत है। Preposition और gerund की आवश्यकता है। सही sentence है :- Mohan insisted on going there.</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "77",
                    section: "7",
                    question_en: "<p>77.Pick a word opposite in meaning to:<br>deterrent</p>",
                    question_hi: "<p>77.Pick a word opposite in meaning to:<br>deterrent</p>",
                    options_en: [
                        "<p>determinant</p>",
                        "<p>detriment</p>",
                        "<p>encouragement</p>",
                        "<p>enrichment</p>"
                    ],
                    options_hi: [
                        "<p>determinant</p>",
                        "<p>detriment</p>",
                        "<p>encouragement</p>",
                        "<p>enrichment</p>"
                    ],
                    solution_en: "<p>77.(c) <strong>encouragement</strong><br><strong>Deterrent</strong> - a thing that discourages or is intended to discourage someone from doing something.<br><strong>Detriment</strong> - the state of being harmed or damaged.</p>",
                    solution_hi: "<p>77.(c) <strong>encouragement</strong>/प्रोत्साहन<br><strong>Deterrent</strong> - एक ऐसी चीज जो किसी को कुछ करने से हतोत्साहित या हतोत्साहित करने का इरादा रखती है।<br><strong>Detriment</strong> - क्षतिग्रस्त या क्षतिग्रस्त होने की अवस्था या भाव।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "78",
                    section: "7",
                    question_en: "<p>78. Identify the segment in the sentence which contains grammatical error. Mark &lsquo;no error&rsquo; in case the sentence given is correct.<br>It has been said that history is the essence of innumerable biography.</p>",
                    question_hi: "<p>78. Identify the segment in the sentence which contains grammatical error. Mark &lsquo;no error&rsquo; in case the sentence given is correct.<br>It has been said that history is the essence of innumerable biography.</p>",
                    options_en: [
                        "<p>No Error</p>",
                        "<p>of innumerable biography</p>",
                        "<p>that history is the essence</p>",
                        "<p>It has been said</p>"
                    ],
                    options_hi: [
                        "<p>No Error</p>",
                        "<p>of innumerable biography</p>",
                        "<p>that history is the essence</p>",
                        "<p>It has been said</p>"
                    ],
                    solution_en: "<p>78.(b) of innumerable biography. <br>Here, innumerable biographies (plural) should be used.</p>",
                    solution_hi: "<p>78.(b) of innumerable biography. <br>यहां, innumerable biographies (plural) का प्रयोग किया जाना चाहिए।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "79",
                    section: "7",
                    question_en: "<p>79. Select the most appropriate option that can substitute the underlined segment in the given sentence.<br>My friends are on a trip <span style=\"text-decoration: underline;\">to the world</span>.</p>",
                    question_hi: "<p>79. Select the most appropriate option that can substitute the underlined segment in the given sentence.<br>My friends are on a trip <span style=\"text-decoration: underline;\">to the world</span>.</p>",
                    options_en: [
                        "<p>within the world</p>",
                        "<p>around the world</p>",
                        "<p>by the world</p>",
                        "<p>in the world</p>"
                    ],
                    options_hi: [
                        "<p>within the world</p>",
                        "<p>around the world</p>",
                        "<p>by the world</p>",
                        "<p>in the world</p>"
                    ],
                    solution_en: "<p>79.(b) around the world<br>&lsquo;To&rsquo; is used for a fixed or particular location. Whereas &lsquo;around&rsquo; is used with &lsquo;world&rsquo;. Hence, &lsquo;around the world&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>79.(b) around the world<br>&lsquo;To&rsquo; का प्रयोग किसी fixed या particular location के लिए किया जाता है। जबकि &lsquo;around&rsquo; का प्रयोग &lsquo;world&rsquo; के साथ किया जाता है। इसलिए, &lsquo;around the world&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "80",
                    section: "7",
                    question_en: "<p>80. Find the correctly spelt word.</p>",
                    question_hi: "<p>80. Find the correctly spelt word.</p>",
                    options_en: [
                        "<p>Nigardly</p>",
                        "<p>Emminent</p>",
                        "<p>Magnificent</p>",
                        "<p>Nuisence</p>"
                    ],
                    options_hi: [
                        "<p>Nigardly</p>",
                        "<p>Emminent</p>",
                        "<p>Magnificent</p>",
                        "<p>Nuisence</p>"
                    ],
                    solution_en: "<p>80.(c) Magnificent <br>Other words- Niggardly, Eminent, Nuisance</p>",
                    solution_hi: "<p>80.(c) Magnificent <br>Other words- Niggardly, Eminent, Nuisance </p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "81",
                    section: "7",
                    question_en: "<p>81. Select the most appropriate meaning of the idiom (in the context). <br>We visit the shopping mall <strong><span style=\"text-decoration: underline;\">off and on</span></strong>.</p>",
                    question_hi: "<p>81. Select the most appropriate meaning of the idiom (in the context). <br>We visit the shopping mall <strong><span style=\"text-decoration: underline;\">off and on</span></strong>.</p>",
                    options_en: [
                        "<p>up and about</p>",
                        "<p>often</p>",
                        "<p>really and truly</p>",
                        "<p>once upon a time</p>"
                    ],
                    options_hi: [
                        "<p>up and about</p>",
                        "<p>often</p>",
                        "<p>really and truly</p>",
                        "<p>once upon a time</p>"
                    ],
                    solution_en: "<p>81.(b) Often</p>",
                    solution_hi: "<p>81.(b) Often /अक्सर</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "82",
                    section: "7",
                    question_en: "<p>82. Choose the most appropriate option to change the narration (direct/indirect) of the given sentence:<br>He said, &ldquo;You needn&rsquo;t wait\".</p>",
                    question_hi: "<p>82. Choose the most appropriate option to change the narration (direct/indirect) of the given sentence:<br>He said, &ldquo;You needn&rsquo;t wait\".</p>",
                    options_en: [
                        "<p>He told that I needn&rsquo;t wait.</p>",
                        "<p>He told me that I needn&rsquo;t wait</p>",
                        "<p>He asked me not to wait.</p>",
                        "<p>He told me that he needn&rsquo;t wait.</p>"
                    ],
                    options_hi: [
                        "<p>He told that I needn&rsquo;t wait.</p>",
                        "<p>He told me that I needn&rsquo;t wait</p>",
                        "<p>He asked me not to wait.</p>",
                        "<p>He told me that he needn&rsquo;t wait.</p>"
                    ],
                    solution_en: "<p>82.(b) He told me that I needn&rsquo;t wait<br>(a) He told that I needn&rsquo;t wait. (Listener is missing)<br>(c) He <strong>asked</strong> me not <strong>to waiting</strong>. (after to first form of the verb is used)<br>(d) He told me that <strong>he</strong> needn&rsquo;t wait. (Incorrect change of pronoun)</p>",
                    solution_hi: "<p>82.(b) He told me that I needn&rsquo;t wait<br>(a) He told that I needn&rsquo;t wait. (Listener गायब है)<br>(c) He <strong>asked</strong> me not <strong>to waiting</strong>. (To के बाद verb का first form का प्रयोग किया जाता है )<br>(d) He told me that <strong>he</strong> needn&rsquo;t wait. (Pronoun का गलत परिवर्तन)</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "83",
                    section: "7",
                    question_en: "<p>83. How much will they pay you?</p>",
                    question_hi: "<p>83. How much will they pay you?</p>",
                    options_en: [
                        "<p>How much will they be paying?</p>",
                        "<p>How much will you be paid by them?</p>",
                        "<p>Payment will be done by them?</p>",
                        "<p>They will have paid you how much?</p>"
                    ],
                    options_hi: [
                        "<p>How much will they be paying?</p>",
                        "<p>How much will you be paid by them?</p>",
                        "<p>Payment will be done by them?</p>",
                        "<p>They will have paid you how much?</p>"
                    ],
                    solution_en: "<p>83.(b) How much will you be paid by them? (Correct)<br>(a) How much will they be <span style=\"text-decoration: underline;\">paying</span>? (Incorrect Tense)<br>(c) Payment will be done by them?(Incorrect Sentence Structure)<br>(d) They will have paid you how much? (Incorrect Sentence Structure)</p>",
                    solution_hi: "<p>83.(b) How much will you be paid by them? (Correct)<br>(a) How much will they be <span style=\"text-decoration: underline;\">paying</span>? (गलत verb (be <span style=\"text-decoration: underline;\">paying</span>) का प्रयोग किया गया है | (be paid का प्रयोग होगा ।)<br>(c) Payment will be done by them? (Sentence Structure सही नहीं है)<br>(d) They will have paid you how much? (Sentence Structure सही नहीं है)</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "84",
                    section: "7",
                    question_en: "<p>84. Select the most appropriate synonym of the given word.<br>Unhappiness</p>",
                    question_hi: "<p>84. Select the most appropriate synonym of the given word.<br>Unhappiness</p>",
                    options_en: [
                        "<p>Wicked</p>",
                        "<p>Woe</p>",
                        "<p>Willing</p>",
                        "<p>Wilful</p>"
                    ],
                    options_hi: [
                        "<p>Wicked</p>",
                        "<p>Woe</p>",
                        "<p>Willing</p>",
                        "<p>Wilful</p>"
                    ],
                    solution_en: "<p>84.(b) <strong>Woe- </strong>great sorrow or distress.<br><strong>Unhappiness</strong>- a state of discontent or sadness.<br><strong>Wicked-</strong> morally bad or evil.<br><strong>Willing- </strong>ready or eager to do something.<br><strong>Wilful-</strong> showing a stubborn or determined intention.</p>",
                    solution_hi: "<p>84.(b) <strong>Woe</strong> (दुख) - great sorrow or distress.<br><strong>Unhappiness</strong> (अप्रसन्नता) - a state of discontent or sadness.<br><strong>Wicked</strong> (दुष्ट) - morally bad or evil.<br><strong>Willing</strong> (उत्सुक) - ready or eager to do something.<br><strong>Wilful</strong> (स्वेच्छाचारी) - showing a stubborn or determined intention.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "85",
                    section: "7",
                    question_en: "<p>85. Given below are four jumbled sentences. Pick the option that gives their correct order.<br>(A) On coming out his skin would be shining like ebony, and he would squeal with pleasure.<br>(B) Every day I used to take him to the river in the morning for his bath.&nbsp;<br>(C) He would lie down on the sand bank while I rubbed him.<br>(D) After that he would lie in the water for a long time.</p>",
                    question_hi: "<p>85. Given below are four jumbled sentences. Pick the option that gives their correct order.<br>(A) On coming out his skin would be shining like ebony, and he would squeal with pleasure.<br>(B) Every day I used to take him to the river in the morning for his bath.&nbsp;<br>(C) He would lie down on the sand bank while I rubbed him.<br>(D) After that he would lie in the water for a long time.</p>",
                    options_en: [
                        "<p>ABCD</p>",
                        "<p>DABC</p>",
                        "<p>BDAC</p>",
                        "<p>BCDA</p>"
                    ],
                    options_hi: [
                        "<p>ABCD</p>",
                        "<p>DABC</p>",
                        "<p>BDAC</p>",
                        "<p>BCDA</p>"
                    ],
                    solution_en: "<p>85.(d) BCDA<br>Sentence B will be the first sentence as a usual habit is mentioned i.e. I take him to the river for his bath every morning . C will follow B as it tells he would lie down on the sand while I rubbed him. C will be followed by D as in D it is given after it he would lie in the water and it will be followed by A as in A it is given &ldquo;on coming out&hellip;&rdquo; . So option d (BCDA) is the suitable answer.</p>",
                    solution_hi: "<p>85.(d) BCDA<br>Sentence B first sentence होगा क्योंकि एक सामान्य आदत का उल्लेख किया गया है अर्थात मैं उसे रोज सुबह नहाने के लिए नदी पर ले जाता हूं। B के बाद C आएगा क्योंकि यह बताता है कि जब मैं उसे रगड़ रहा था तो वह रेत पर लेट गया था । C के बाद D होगा क्योंकि D में दिया गया है उसके बाद वह पानी में लेट जाएगा और उसके बाद A होगा क्योंकि A में &ldquo;on coming out&hellip;&rdquo; दिया गया है। अतः option d (BCDA) उपयुक्त उत्तर है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "86",
                    section: "7",
                    question_en: "86. Select the INCORRECTLY spelt word. ",
                    question_hi: "86. Select the INCORRECTLY spelt word. ",
                    options_en: [
                        " Immaculate",
                        " Innoculate",
                        " Irreverent",
                        " Immense"
                    ],
                    options_hi: [
                        " Immaculate",
                        " Innoculate",
                        " Irreverent",
                        " Immense"
                    ],
                    solution_en: "86.(b) Innoculate<br />‘Inoculate’ is the correct spelling.",
                    solution_hi: "86.(b) Innoculate<br />‘Inoculate’ सही spelling है। ",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "87",
                    section: "7",
                    question_en: "<p>87. The maid was dusting the hall when the door-bell rang.</p>",
                    question_hi: "<p>87. The maid was dusting the hall when the door-bell rang.</p>",
                    options_en: [
                        "<p>The door-bell rang when the hall was being dusted by the maid.</p>",
                        "<p>The door-bell rang by the maid when the hall was dusted.</p>",
                        "<p>The door-bell rang when the maid was dusting the hall.</p>",
                        "<p>The door-bell rang when the maid is dusting the hall.</p>"
                    ],
                    options_hi: [
                        "<p>The door-bell rang when the hall was being dusted by the maid.</p>",
                        "<p>The door-bell rang by the maid when the hall was dusted.</p>",
                        "<p>The door-bell rang when the maid was dusting the hall.</p>",
                        "<p>The door-bell rang when the maid is dusting the hall.</p>"
                    ],
                    solution_en: "<p>87.(a) The door-bell rang when the hall was being dusted by the maid. (Correct)<br>(b) The door-bell rang by the maid when the hall <span style=\"text-decoration: underline;\">was dusted</span>. (Incorrect Tense)<br>(c) The door-bell rang when the maid <span style=\"text-decoration: underline;\">was dusting</span> the hall. (Incorrect Tense)<br>(d) The door-bell rang when the maid <span style=\"text-decoration: underline;\">is dusting</span> the hall. (Incorrect Tense)</p>",
                    solution_hi: "<p>87.(a) The door-bell rang when the hall was being dusted by the maid. (Correct)<br>(b) The door-bell rang by the maid when the hall <span style=\"text-decoration: underline;\">was dusted</span>. (गलत verb (was dusted) का प्रयोग किया गया है। was being dusted) का प्रयोग होगा | )&nbsp;<br>(c) The door-bell rang when the maid <span style=\"text-decoration: underline;\">was dusting</span> the hall. (गलत tense (was dusting) का प्रयोग किया गया है। was being dusted) का प्रयोग होगा | )<br>(d) The door-bell rang when the maid <span style=\"text-decoration: underline;\">is dusting</span> the hall. (गलत tense (present continuous) का प्रयोग किया गया है । was being dusted (past continuous) का प्रयोग होगा | )&nbsp;</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "88",
                    section: "7",
                    question_en: "<p>88. Select the most appropriate meaning of the given idiom. <br>Make waves</p>",
                    question_hi: "<p>88. Select the most appropriate meaning of the given idiom. <br>Make waves</p>",
                    options_en: [
                        "<p>To have patience</p>",
                        "<p>To cause difficulty</p>",
                        "<p>To take it easy</p>",
                        "<p>To give up</p>"
                    ],
                    options_hi: [
                        "<p>To have bpatience</p>",
                        "<p>To cause difficulty</p>",
                        "<p>To take it easy</p>",
                        "<p>To give up</p>"
                    ],
                    solution_en: "<p>88.(b) <strong>Make waves</strong> - to cause difficulty.<br>E.g.- He didn&rsquo;t want to make waves at work, so he kept quiet about the issue.</p>",
                    solution_hi: "<p>88.(b) <strong>Make waves -</strong> to cause difficulty./कठिनाई उत्पन्न करना।<br>E.g.- He didn&rsquo;t want to make waves at work, so he kept quiet about the issue.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "89",
                    section: "7",
                    question_en: "<p>89. Given below are four jumbled sentences. Pick the option that gives their correct order.<br>(A) She was determined to lead a normal life and pursue her interest in music. <br>(B) It was then, she decided that she wanted to play it too.<br>(C) One day she noticed a girl playing a xylophone.<br>(D) But Evelyn was not going to give up.</p>",
                    question_hi: "<p>89. Given below are four jumbled sentences. Pick the option that gives their correct order.<br>(A) She was determined to lead a normal life and pursue her interest in music. <br>(B) It was then, she decided that she wanted to play it too.<br>(C) One day she noticed a girl playing a xylophone.<br>(D) But Evelyn was not going to give up.</p>",
                    options_en: [
                        "<p>DCAB</p>",
                        "<p>DACB</p>",
                        "<p>BCAD</p>",
                        "<p>ACBD</p>"
                    ],
                    options_hi: [
                        "<p>DCAB</p>",
                        "<p>DACB</p>",
                        "<p>BCAD</p>",
                        "<p>ACBD</p>"
                    ],
                    solution_en: "<p>89.(b) <strong>DACB</strong><br>Sentence D will be the starting line as the main idea of the parajumble is mentioned here &ldquo; Evelyn&rdquo;.The parajumble is about the determination of a girl Evelyn. Sentence A will follow sentence D as it states about her determination and interest in music. Sentence A will be followed by sentence C i.e. one day she noticed a girl playing a xylophone and it will be followed by sentence B as it explains that after seeing that girl she decided that she wanted to play the same instrument.So, option (B) is the correct choice.</p>",
                    solution_hi: "<p>89.(b) <strong>DACB</strong><br>Sentence D starting line होगी क्योंकि इसमे parajumble का मुख्य विचार &ldquo; Evelyn&rdquo; है। Parajumble एक लड़की Evelyn के दृढ़ संकल्प के बारे में है। Sentence D के बाद sentence A आएगा क्योंकि यह उसके दृढ़ संकल्प और संगीत में रुचि के बारे में बताता है। Sentence A के बाद sentence C होगा क्योंकि यह बताता है कि एक दिन उसने एक लड़की को जाइलोफोन(xylophone ) बजाते हुए देखा और उसके बाद sentence B आएगा क्योंकि यह बताता है कि उस लड़की को देखने के बाद उसने फैसला किया कि वह वही वाद्य यंत्र बजाना चाहती है। इसलिए, option (B) ) सही विकल्प है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "90",
                    section: "7",
                    question_en: "<p>90. Select the option that can be used as a one-word substitute for the given group of words.<br>A doubtful look or examination of one\'s motives.</p>",
                    question_hi: "<p>90. Select the option that can be used as a one-word substitute for the given group of words.<br>A doubtful look or examination of one\'s motives.</p>",
                    options_en: [
                        "<p>Introspect</p>",
                        "<p>Suspect</p>",
                        "<p>Circumspect</p>",
                        "<p>Retrospect</p>"
                    ],
                    options_hi: [
                        "<p>Introspect</p>",
                        "<p>Suspect</p>",
                        "<p>Circumspect</p>",
                        "<p>Retrospect</p>"
                    ],
                    solution_en: "<p>90.(b) <strong>Suspect-</strong> a doubtful look or examination of one\'s motives.<br><strong>Introspect-</strong> examine one\'s own thoughts or feelings.<br><strong>Circumspect-</strong> think carefully before doing or saying anything.<br><strong>Retrospect-</strong> a survey or review of a past course of events or period of time.</p>",
                    solution_hi: "<p>90.(b) <strong>Suspect </strong>(संदिग्ध व्यक्ति) - a doubtful look or examination of one\'s motives.<br><strong>Introspect</strong> (आत्&zwj;म-निरीक्षण करना) - examine one\'s own thoughts or feelings.<br><strong>Circumspect</strong> (सावधानी) - think carefully before doing or saying anything.<br><strong>Retrospect</strong> (पुनः निरीक्षण करें) - a survey or review of a past course of events or period of time.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "91",
                    section: "7",
                    question_en: "<p>91.Out of the four alternatives choose the one which can be substituted for the given words/sentences.<br>A medical condition in which somebody partly or completely loses their memory</p>",
                    question_hi: "<p>91.Out of the four alternatives choose the one which can be substituted for the given words/sentences.<br>A medical condition in which somebody partly or completely loses their memory</p>",
                    options_en: [
                        "<p>Amnesia</p>",
                        "<p>Asphyxia</p>",
                        "<p>Anaemia</p>",
                        "<p>Alopecia</p>"
                    ],
                    options_hi: [
                        "<p>Amnesia</p>",
                        "<p>Asphyxia</p>",
                        "<p>Anaemia</p>",
                        "<p>Alopecia</p>"
                    ],
                    solution_en: "<p>91.(a) <strong>Amnesia-</strong> a medical condition in which somebody partly or completely loses their memory <br><strong>Asphyxia- </strong>the state of being unable to breathe, which causes somebody to die or to become unconscious<br><strong>Anemia-</strong> a medical condition in which there are not enough red cells in the blood <br><strong>Alopecia-</strong> a medical condition of hair loss</p>",
                    solution_hi: "<p>91.(a) <strong>Amnesia </strong>(याददाश्त खो जाना)- a medical condition in which somebody partly or completely loses their memory <br><strong>Asphyxia </strong>(श्वासावरोध)- the state of being unable to breathe, which causes somebody to die or to become unconscious<br><strong>Anaemia </strong>(खून की कमी)- a medical condition in which there are not enough red cells in the blood <br><strong>Alopecia</strong>( गंजापन)- a medical condition of hair loss </p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "92",
                    section: "7",
                    question_en: "<p>92. Select the most appropriate option to fill in the blank.<br>Our life promises _________ pleasures and we must learn to enjoy it.</p>",
                    question_hi: "<p>92. Select the most appropriate option to fill in the blank.<br>Our life promises _________ pleasures and we must learn to enjoy it.</p>",
                    options_en: [
                        "<p>a lot of</p>",
                        "<p>a lot off</p>",
                        "<p>lots off</p>",
                        "<p>the lot of</p>"
                    ],
                    options_hi: [
                        "<p>a lot of</p>",
                        "<p>a lot off</p>",
                        "<p>lots off</p>",
                        "<p>the lot of</p>"
                    ],
                    solution_en: "<p>92.(a) <strong>a lot of</strong> , &ldquo;A lot of&rdquo; means a large number or amount of (things, people, etc.). It is a phrase.</p>",
                    solution_hi: "<p>92.(a) <strong>a lot of</strong> <br>&ldquo;A lot of&rdquo; का अर्थ है बड़ी संख्या या राशि (things, people, etc.), यह एक phrase है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "93",
                    section: "7",
                    question_en: "<p>93. Select the most appropriate ANTONYM of the underlined word in the given sentence.<br>Recession <span style=\"text-decoration: underline;\">fettered</span> the company from any further investments.</p>",
                    question_hi: "<p>93. Select the most appropriate ANTONYM of the underlined word in the given sentence.<br>Recession <span style=\"text-decoration: underline;\">fettered</span> the company from any further investments.</p>",
                    options_en: [
                        "<p>Liberated</p>",
                        "<p>Valuable</p>",
                        "<p>Depended</p>",
                        "<p>Concealed</p>"
                    ],
                    options_hi: [
                        "<p>Liberated</p>",
                        "<p>Valuable</p>",
                        "<p>Depended</p>",
                        "<p>Concealed</p>"
                    ],
                    solution_en: "<p>93.(a)<strong> Liberated</strong>- set free or released from restrictions.<br><strong>Fettered</strong>- restrained or restricted from progress or movement.<br><strong>Valuable</strong>- of great worth or importance.<br><strong>Depended</strong>- relied on or needed support.<br><strong>Concealed-</strong> kept hidden or secret.</p>",
                    solution_hi: "<p>93.(a)<strong> Liberated </strong>(मुक्त) - set free or released from restrictions.<br><strong>Fettered</strong> (बाधित) - restrained or restricted from progress or movement.<br><strong>Valuable</strong> (मूल्यवान) - of great worth or importance.<br><strong>Depended</strong> (आश्रित) - relied on or needed support.<br><strong>Concealed</strong> (गुप्त) - kept hidden or secret.</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "94",
                    section: "7",
                    question_en: "<p>94. Identify the best way to improve the underlined part of the given sentence. If there is no improvement required, select &lsquo;no improvement&rsquo;.<br>The teacher asked him to copy the material word <strong><span style=\"text-decoration: underline;\">for</span></strong> word.</p>",
                    question_hi: "<p>94. Identify the best way to improve the underlined part of the given sentence. If there is no improvement required, select &lsquo;no improvement&rsquo;.<br>The teacher asked him to copy the material word <span style=\"text-decoration: underline;\"><strong>for</strong></span> word.</p>",
                    options_en: [
                        "<p>by</p>",
                        "<p>No Improvement</p>",
                        "<p>before</p>",
                        "<p>after</p>"
                    ],
                    options_hi: [
                        "<p>by</p>",
                        "<p>No Improvement</p>",
                        "<p>before</p>",
                        "<p>after</p>"
                    ],
                    solution_en: "<p>94.(b) No Improvement<br>Word for word means - using exactly the same words:<br>Eg- She listened to everything that her lawyer advised and repeated it word for word before the judge.</p>",
                    solution_hi: "<p>94.(b) No Improvement<br>Word for word का अर्थ है - बिल्कुल समान शब्दों का प्रयोग करना। <br>Eg- She listened to everything that her lawyer advised and repeated it word for word before the judge./ उसने वह सब कुछ सुना जो उसके वकील ने सलाह दी और उसने जज के सामने ज्यों का त्यों दोहरा दिया । <br><br></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "95",
                    section: "7",
                    question_en: "<p>95. ______ 540,000-square-foot building now houses ______ enormous collection of artworks and historic archives.</p>",
                    question_hi: "<p>95. ______ 540,000-square-foot building now houses ______ enormous collection of artworks and historic archives.</p>",
                    options_en: [
                        "<p>No word required; the</p>",
                        "<p>The; no word required</p>",
                        "<p>The; a</p>",
                        "<p>A; an</p>"
                    ],
                    options_hi: [
                        "<p>No word required; the</p>",
                        "<p>The; no word required</p>",
                        "<p>The; a</p>",
                        "<p>A; an</p>"
                    ],
                    solution_en: "<p>95.(d) A; an <br>The &lsquo;building&rsquo; mentioned in the given sentence is non-specific and we generally use the indefinite article &lsquo;a&rsquo; before any non-specific or on-particular noun. However, the article &lsquo;an&rsquo; is used before words starting with a <span style=\"text-decoration: underline;\"><strong>vowel</strong></span>(A,E,I,O,U) for example, an educated person, an imminent danger, enormous collection(in the given sentence). Hence, &lsquo;A, an&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>95.(d) A; an <br>दिए गए वाक्य में उल्लिखित &lsquo;building&rsquo; non-specific है और हम आम तौर पर किसी non-specific या particular noun से पहले indefinite article &lsquo;a&rsquo; का उपयोग करते हैं। हालांकि, article &lsquo;an&rsquo; का प्रयोग <strong><span style=\"text-decoration: underline;\">vowel</span></strong>(A,E,I,O,U) से शुरू होने वाले शब्दों से पहले किया जाता है, उदाहरण के लिए, an educated person, an imminent danger, enormous collection (दिए गए वाक्य में)। इसलिए, &lsquo;A, an&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "96",
                    section: "7",
                    question_en: "<p>96.<strong>Cloze Test:</strong><br>A massive fire in Chennai destroyed 184 cars ____(96)____ to a private cab aggregator. _____(97)_____ blaze is suspected to have been triggered ______(98) ______ tall dried grass caught fire from a discarded _____(99)_____ cigarette butt, said a rescue services ______(100)_____ .<br>Select the appropriate option to fill in the blank No.96.</p>",
                    question_hi: "<p>96.<strong>Cloze Test:</strong><br>A massive fire in Chennai destroyed 184 cars ____(96)____ to a private cab aggregator. _____(97)_____ blaze is suspected to have been triggered ______(98) ______ tall dried grass caught fire from a discarded _____(99)_____ cigarette butt, said a rescue services ______(100)_____ .<br>Select the appropriate option to fill in the blank No.96</p>",
                    options_en: [
                        "<p>Were belonging</p>",
                        "<p>Belonging</p>",
                        "<p>Belongs</p>",
                        "<p>Belonged</p>"
                    ],
                    options_hi: [
                        "<p>Were belonging</p>",
                        "<p>Belonging</p>",
                        "<p>Belongs</p>",
                        "<p>Belonged</p>"
                    ],
                    solution_en: "<p>96.(b) Belonging<br>The given passage is in the present tense. However, the given sentence needs a gerund(V-ing) form of the verb &lsquo;belong&rsquo;. Hence, &lsquo;belonging&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>96.(b) Belonging<br>दिया गया passage present tense का है। हालाँकि, दिए गए वाक्य में &lsquo;belong&rsquo; verb के लिए gerund(V-ing) रूप की आवश्यकता होती है। इसलिए, &lsquo;belonging&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "97",
                    section: "7",
                    question_en: "<p>97.<strong>Cloze Test:</strong> <br>A massive fire in Chennai destroyed 184 cars ____(96)____ to a private cab aggregator. _____(97)_____ blaze is suspected to have been triggered ______(98) ______ tall dried grass caught fire from a discarded _____(99)_____ cigarette butt, said a rescue services ______(100)_____ .<br>Select the appropriate option to fill in the blank No.97</p>",
                    question_hi: "<p>97.<strong>Cloze Test:</strong> <br>A massive fire in Chennai destroyed 184 cars ____(96)____ to a private cab aggregator. _____(97)_____ blaze is suspected to have been triggered ______(98) ______ tall dried grass caught fire from a discarded _____(99)_____ cigarette butt, said a rescue services ______(100)_____ .<br>Select the appropriate option to fill in the blank No.97</p>",
                    options_en: [
                        "<p>A</p>",
                        "<p>The</p>",
                        "<p>Some</p>",
                        "<p>One</p>"
                    ],
                    options_hi: [
                        "<p>A</p>",
                        "<p>The</p>",
                        "<p>Some</p>",
                        "<p>One</p>"
                    ],
                    solution_en: "<p>97.(b) The<br>The definite article &lsquo;The&rsquo; must be used before &lsquo;blaze&rsquo; because it is a specific situation. Hence, &lsquo;The&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>97.(b) The<br>Definite article \'The\' का प्रयोग \'blaze\' से पहले किया जाना चाहिए क्योंकि यह एक specific situation है। इसलिए,&lsquo;The&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "98",
                    section: "7",
                    question_en: "<p>98.<strong>Cloze Test:</strong> <br>A massive fire in Chennai destroyed 184 cars ____(96)____ to a private cab aggregator. _____(97)_____ blaze is suspected to have been triggered ______(98) ______ tall dried grass caught fire from a discarded _____(99)_____ cigarette butt, said a rescue services ______(100)_____ .<br>Select the appropriate option to fill in the blank No.98</p>",
                    question_hi: "<p>98.<strong>Cloze Test:</strong> <br>A massive fire in Chennai destroyed 184 cars ____(96)____ to a private cab aggregator. _____(97)_____ blaze is suspected to have been triggered ______(98) ______ tall dried grass caught fire from a discarded _____(99)_____ cigarette butt, said a rescue services ______(100)_____ .<br>Select the appropriate option to fill in the blank No.98</p>",
                    options_en: [
                        "<p>Until</p>",
                        "<p>Of</p>",
                        "<p>After</p>",
                        "<p>By</p>"
                    ],
                    options_hi: [
                        "<p>Until</p>",
                        "<p>Of</p>",
                        "<p>After</p>",
                        "<p>By</p>"
                    ],
                    solution_en: "<p>98.(c) After<br>The preposition &lsquo;after&rsquo; will perfectly fit in the context of the sentence. The given passage states that the blaze is suspected to have been triggered after tall dried grass caught fire. Hence, &lsquo;after&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>98.(c) After<br>Preposition &lsquo;after&rsquo; वाक्य के संदर्भ में पूरी तरह से फिट होगा। दिए गए passage में कहा गया है कि आशंका जताई जा रही है कि लंबी सूखी घास में आग लगने के बाद आग लगी थी। इसलिए, &lsquo;after&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "99",
                    section: "7",
                    question_en: "<p>99. <strong>Cloze Test:&nbsp;</strong><br>A massive fire in Chennai destroyed 184 cars ____(96)____ to a private cab aggregator. _____(97)_____ blaze is suspected to have been triggered ______(98) ______ tall dried grass caught fire from a discarded _____(99)_____ cigarette butt, said a rescue services ______(100)_____ .<br>Select the appropriate option to fill in the blank No.99</p>",
                    question_hi: "<p>99.<strong>Cloze Test:</strong> <br>A massive fire in Chennai destroyed 184 cars ____(96)____ to a private cab aggregator. _____(97)_____ blaze is suspected to have been triggered ______(98) ______ tall dried grass caught fire from a discarded _____(99)_____ cigarette butt, said a rescue services ______(100)_____ .<br>Select the appropriate option to fill in the blank No.99</p>",
                    options_en: [
                        "<p>Lighting</p>",
                        "<p>Smouldering</p>",
                        "<p>Smoking</p>",
                        "<p>Firing</p>"
                    ],
                    options_hi: [
                        "<p>Lighting</p>",
                        "<p>Smouldering</p>",
                        "<p>Smoking</p>",
                        "<p>Firing</p>"
                    ],
                    solution_en: "<p>99.(b) Smouldering<br>&lsquo;Smouldering&rsquo; means the process of burning slowly with smoke but no flame. The given passage states that the dried grass caught the fire from a smouldering cigarette. Hence, &lsquo;smouldering&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>99.(b) Smouldering<br>&lsquo;Smouldering&rsquo; का अर्थ है धुएँ के साथ धीरे-धीरे जलने की प्रक्रिया। दिए गए passage में कहा गया है कि सूखे घास ने सुलगती सिगरेट से आग पकड़ ली। इसलिए, &lsquo;smouldering&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "100",
                    section: "misc",
                    question_en: "<p>100.<strong>Cloze Test:</strong><br>A massive fire in Chennai destroyed 184 cars ____(96)____ to a private cab aggregator. _____(97)_____ blaze is suspected to have been triggered ______(98) ______ tall dried grass caught fire from a discarded _____(99)_____ cigarette butt, said a rescue services ______(100)_____ .<br>Select the appropriate option to fill in the blank No.100</p>",
                    question_hi: "<p>100.<strong>Cloze Test:</strong> <br>A massive fire in Chennai destroyed 184 cars ____(96)____ to a private cab aggregator. _____(97)_____ blaze is suspected to have been triggered ______(98) ______ tall dried grass caught fire from a discarded _____(99)_____ cigarette butt, said a rescue services ______(100)_____ .<br>Select the appropriate option to fill in the blank No.100</p>",
                    options_en: [
                        "<p>Special</p>",
                        "<p>Official</p>",
                        "<p>Culprit</p>",
                        "<p>Victim</p>"
                    ],
                    options_hi: [
                        "<p>Special</p>",
                        "<p>Official</p>",
                        "<p>Culprit</p>",
                        "<p>Victim</p>"
                    ],
                    solution_en: "<p>100.(b) Official <br>&lsquo;Official&rsquo; means connected with the position of somebody in authority. The given passage states that the person was connected to the rescue services. Hence, &lsquo;official&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>100.(b) Official <br>&lsquo;Official&rsquo; का अर्थ सत्ता में किसी की स्थिति से जुड़ा हुआ है। दिया गया passage बताता है कि वह व्यक्ति बचाव सेवाओं से जुड़ा हुआ था। इसलिए, &lsquo;official&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'question-box d-flex align-items-center justify-content-center border rounded';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.question-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            const html = `
                <div class="card shadow-sm mb-4">
                    <div class="card-body">
                        <div class="question-text mb-4">
                            <div class="en">${question.question_en}</div>
                            <div class="hi" style="display:none">${question.question_hi}</div>
                        </div>
                        <div class="options">
                            ${['a', 'b', 'c', 'd'].map((opt, i) => `
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="radio" name="q${index}" 
                                        id="opt-${index}-${opt}" value="${opt}" 
                                        ${answers[question.id] === opt ? 'checked' : ''}
                                        onclick="selectAnswer('${question.id}', '${opt}')">
                                    <label class="form-check-label w-100 py-2 px-3 rounded hover-bg-light" for="opt-${index}-${opt}">
                                        <div class="en">${question.options_en[i]}</div>
                                        <div class="hi" style="display:none">${question.options_hi[i]}</div>
                                    </label>
                                </div>
                            `).join('')}
                        </div>
                        ${submitted ? `
                            <div class="alert ${answers[question.id] === question.correct ? 'alert-success' : 'alert-danger'} mt-3">
                                ${answers[question.id] ? (answers[question.id] === question.correct ? 'Correct!' : 'Incorrect!') : 'Not attempted'}
                                <div class="mt-2">
                                    <strong>Solution:</strong>
                                    <div class="en">${question.solution_en}</div>
                                    <div class="hi" style="display:none">${question.solution_hi}</div>
                                </div>
                            </div>
                        ` : ''}
                    </div>
                </div>
            `;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // If in mobile view, close the menu
            if (window.innerWidth < 768) {
                toggleMenu(false);
            }
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                document.getElementById(`box-${index}`).classList.add('attempted');
            }
        }
        // Navigate to previous question
        function prevQuestion() {
            if (currentQuestion > 0) {
                showQuestion(currentQuestion - 1);
            }
        }
        // Navigate to next question
        function nextQuestion() {
            if (currentQuestion < questions.length - 1) {
                showQuestion(currentQuestion + 1);
            }
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) {
                showQuestion(section.start);
            }
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update button text
            document.getElementById('lang-btn').innerHTML = currentLang === 'en' ? 
                '<i class="fas fa-language"></i> हिंदी' : 
                '<i class="fas fa-language"></i> English';
        }
        // Toggle menu for mobile
        function toggleMenu(show) {
            const nav = document.getElementById('question-nav');
            const content = document.getElementById('question-content');
            if (show === undefined) {
                show = nav.style.right === '-100%' || nav.style.right === '';
            }
            if (show) {
                nav.style.right = '0';
                if (window.innerWidth >= 768) {
                    content.style.marginRight = '320px';
                }
            } else {
                nav.style.right = window.innerWidth < 768 ? '-100%' : '0';
                content.style.marginRight = window.innerWidth < 768 ? '0' : '320px';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
            document.getElementById('total-count').textContent = questions.length;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.remove('attempted');
                    box.classList.add('bg-secondary', 'text-white');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.remove('attempted');
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.remove('attempted');
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            resultsModal.show();
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check-circle me-1"></i> Submitted';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            resultsModal.hide();
            showQuestion(0);
        }
    </script>
</body>
</html>