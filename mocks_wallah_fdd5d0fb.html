<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">20:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 15</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">15</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 20 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 0,
                end: 13
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 14,
                end: 14
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">1. </span><span style=\"font-family: Cambria Math;\">What is the value of (</span><span style=\"font-family: Cambria Math;\">a + b</span><span style=\"font-family: Cambria Math;\">)</span><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">&sup3;</span></span><span style=\"font-family: Cambria Math;\"> -&nbsp; a&sup3; + b</span><span style=\"font-family: Cambria Math;\">&sup3; ?</span></p>",
                    question_hi: "<p>1.<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">a + b</span><span style=\"font-family: Cambria Math;\">)</span><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">&sup3;</span></span><span style=\"font-family: Cambria Math;\"> -&nbsp; a&sup3; + b&sup3; </span><span style=\"font-family: Nirmala UI;\">का</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">मान</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">क्या</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">है</span><span style=\"font-family: Cambria Math;\">?</span></p>",
                    options_en: ["<p>- <span style=\"font-family: Cambria Math;\">3ab(a - b) + 2b</span><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">&sup3;</span></span></p>", "<p><span style=\"font-family: Cambria Math;\">3ab(a + b) + 2b</span><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">&sup3;</span></span></p>", 
                                "<p>3<span style=\"font-family: Cambria Math;\">ab(a - b) b&sup3;</span></p>", "<p>-3<span style=\"font-family: Cambria Math;\">ab(</span><span style=\"font-family: Cambria Math;\">a + b) +b</span><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">&sup3;</span></span></p>"],
                    options_hi: ["<p>- <span style=\"font-family: Cambria Math;\">3ab(a - b) + 2b&sup3;</span></p>", "<p><span style=\"font-family: Cambria Math;\">3ab(a + b) + 2b&sup3;</span></p>",
                                "<p>3<span style=\"font-family: Cambria Math;\">ab(a - b)b&sup3;</span></p>", "<p>- 3<span style=\"font-family: Cambria Math;\">ab(</span><span style=\"font-family: Cambria Math;\">a + b) + b&sup3;</span></p>"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">1.(</span><span style=\"font-family: Cambria Math;\">b)</span><br><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">(a + b</span><span style=\"font-weight: 400;\">)&sup3; </span><span style=\"font-weight: 400;\">-&nbsp; </span><span style=\"font-weight: 400;\">a</span><span style=\"font-weight: 400;\">&sup3; </span><span style=\"font-weight: 400;\">+ </span><span style=\"font-weight: 400;\">b</span><span style=\"font-weight: 400;\">&sup3; </span><span style=\"font-weight: 400;\">= </span><span style=\"font-weight: 400;\">a</span><span style=\"font-weight: 400;\">&sup3; </span><span style=\"font-weight: 400;\">+ </span><span style=\"font-weight: 400;\">b</span><span style=\"font-weight: 400;\">&sup3; </span><span style=\"font-weight: 400;\">+ 3ab(a + b) -</span><span style=\"font-weight: 400;\">a</span><span style=\"font-weight: 400;\">&sup3; </span><span style=\"font-weight: 400;\">+ </span><span style=\"font-weight: 400;\">b</span><span style=\"font-weight: 400;\">&sup3; </span><span style=\"font-weight: 400;\">= 2</span><span style=\"font-weight: 400;\">b</span><span style=\"font-weight: 400;\">&sup3; </span><span style=\"font-weight: 400;\">+ 3ab(a + b)</span></span></p>",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">1.(</span><span style=\"font-family: Cambria Math;\">b)</span><br><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">(a + b</span><span style=\"font-weight: 400;\">)&sup3; </span><span style=\"font-weight: 400;\">-&nbsp; </span><span style=\"font-weight: 400;\">a</span><span style=\"font-weight: 400;\">&sup3; </span><span style=\"font-weight: 400;\">+ </span><span style=\"font-weight: 400;\">b</span><span style=\"font-weight: 400;\">&sup3; </span><span style=\"font-weight: 400;\">= </span><span style=\"font-weight: 400;\">a</span><span style=\"font-weight: 400;\">&sup3; </span><span style=\"font-weight: 400;\">+ </span><span style=\"font-weight: 400;\">b</span><span style=\"font-weight: 400;\">&sup3; </span><span style=\"font-weight: 400;\">+ 3ab(a + b) -</span><span style=\"font-weight: 400;\">a</span><span style=\"font-weight: 400;\">&sup3; </span><span style=\"font-weight: 400;\">+ </span><span style=\"font-weight: 400;\">b</span><span style=\"font-weight: 400;\">&sup3; </span><span style=\"font-weight: 400;\">= 2</span><span style=\"font-weight: 400;\">b</span><span style=\"font-weight: 400;\">&sup3; </span><span style=\"font-weight: 400;\">+ 3ab(a + b)</span></span></p>\n<p>&nbsp;</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">2. </span><span style=\"font-family: Cambria Math;\"> If <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup><mo>+</mo><mfrac><mn>1</mn><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup></mfrac><mo>=</mo><mn>66</mn><mo>,</mo></math></span><span style=\"font-family: Cambria Math;\"> the value of <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">x</mi><mo>-</mo><mfrac><mn>1</mn><mi mathvariant=\"normal\">x</mi></mfrac></math></span><span style=\"font-family: Cambria Math;\">&nbsp;</span><span style=\"font-family: Cambria Math;\">is _______.&nbsp;</span></p>",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">2.</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">यदि <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup><mo>+</mo><mfrac><mn>1</mn><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup></mfrac><mo>=</mo><mn>66</mn></math></span><span style=\"font-family: Cambria Math;\">&nbsp;</span><span style=\"font-family: Nirmala UI;\">है</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">तो</span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">x</mi><mo>-</mo><mfrac><mn>1</mn><mi mathvariant=\"normal\">x</mi></mfrac></math></span><span style=\"font-family: Cambria Math;\">&nbsp;</span><span style=\"font-family: Nirmala UI;\">का</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">मान</span><span style=\"font-family: Cambria Math;\"> ________</span><span style=\"font-family: Nirmala UI;\">है</span><span style=\"font-family: Cambria Math;\"> </span></p>",
                    options_en: ["<p>10</p>", "<p>8</p>", 
                                "<p>9</p>", "<p>6</p>"],
                    options_hi: ["<p>10</p>", "<p>8</p>",
                                "<p>9</p>", "<p>6</p>"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">2.(</span><span style=\"font-family: Cambria Math;\">b)</span><br><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>If</mi><mo>&#160;</mo><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mfrac><mn>1</mn><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup></mfrac><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>66</mn></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mi mathvariant=\"normal\">x</mi><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mfrac><mn>1</mn><mi mathvariant=\"normal\">x</mi></mfrac><mo>)</mo></mrow><mn>2</mn></msup><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>66</mn><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mn>2</mn><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>64</mn></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">x</mi><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mfrac><mn>1</mn><mi mathvariant=\"normal\">x</mi></mfrac><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><msqrt><mn>64</mn></msqrt><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>8</mn></math></span></p>",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">2.(</span><span style=\"font-family: Cambria Math;\">b)</span><br><span style=\"font-family: Cambria Math;\">यदि&nbsp; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>x</mi><mn>2</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mfrac><mn>1</mn><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup></mfrac><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>66</mn></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mi mathvariant=\"normal\">x</mi><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mfrac><mn>1</mn><mi mathvariant=\"normal\">x</mi></mfrac><mo>)</mo></mrow><mn>2</mn></msup><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>66</mn><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mn>2</mn><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>64</mn></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">x</mi><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mfrac><mn>1</mn><mi mathvariant=\"normal\">x</mi></mfrac><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><msqrt><mn>64</mn></msqrt><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>8</mn></math></span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">3.</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">If <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">K</mi><mo>+</mo><mfrac><mn>1</mn><mi mathvariant=\"normal\">K</mi></mfrac><mo>=</mo><mn>4</mn></math></span><span style=\"font-family: Cambria Math;\">, then what is the value of&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi mathvariant=\"normal\">K</mi><mn>4</mn></msup><mo>+</mo><mfrac><mn>1</mn><msup><mi mathvariant=\"normal\">K</mi><mn>4</mn></msup></mfrac></math></span></p>",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">3.</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">यदि <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">K</mi><mo>+</mo><mfrac><mn>1</mn><mi mathvariant=\"normal\">K</mi></mfrac><mo>=</mo><mn>4</mn></math></span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">है</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">तो</span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi mathvariant=\"normal\">K</mi><mn>4</mn></msup><mo>+</mo><mfrac><mn>1</mn><msup><mi mathvariant=\"normal\">K</mi><mn>4</mn></msup></mfrac></math></span><span style=\"font-family: Nirmala UI;\">का</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">मान</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">क्या</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">है</span><span style=\"font-family: Cambria Math;\">?</span></p>",
                    options_en: ["<p>410</p>", "<p>192</p>", 
                                "<p>212</p>", "<p>194</p>"],
                    options_hi: ["<p>410</p>", "<p>192</p>",
                                "<p>212</p>", "<p>194</p>"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">3.(</span><span style=\"font-family: Cambria Math;\">d)</span><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi mathvariant=\"normal\">K</mi><mn>2</mn></msup><mo>&#160;</mo><mo>+</mo><mfrac><mn>1</mn><msup><mi mathvariant=\"normal\">K</mi><mn>2</mn></msup></mfrac><mo>&#160;</mo><mo>=</mo><msup><mn>4</mn><mn>2</mn></msup><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mn>2</mn><mo>&#160;</mo><mo>=</mo><mn>16</mn><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mn>2</mn><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>14</mn></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi mathvariant=\"normal\">K</mi><mn>4</mn></msup><mo>&#160;</mo><mo>+</mo><mfrac><mn>1</mn><msup><mi mathvariant=\"normal\">K</mi><mn>4</mn></msup></mfrac><mo>&#160;</mo><mo>=</mo><msup><mn>14</mn><mn>2</mn></msup><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mn>2</mn><mo>&#160;</mo><mo>=</mo><mn>196</mn><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mn>2</mn><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>194</mn></math></p>",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">3.(</span><span style=\"font-family: Cambria Math;\">d)</span><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi mathvariant=\"normal\">K</mi><mn>2</mn></msup><mo>&#160;</mo><mo>+</mo><mfrac><mn>1</mn><msup><mi mathvariant=\"normal\">K</mi><mn>2</mn></msup></mfrac><mo>&#160;</mo><mo>=</mo><msup><mn>4</mn><mn>2</mn></msup><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mn>2</mn><mo>&#160;</mo><mo>=</mo><mn>16</mn><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mn>2</mn><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>14</mn></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi mathvariant=\"normal\">K</mi><mn>4</mn></msup><mo>&#160;</mo><mo>+</mo><mfrac><mn>1</mn><msup><mi mathvariant=\"normal\">K</mi><mn>4</mn></msup></mfrac><mo>&#160;</mo><mo>=</mo><msup><mn>14</mn><mn>2</mn></msup><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mn>2</mn><mo>&#160;</mo><mo>=</mo><mn>196</mn><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mn>2</mn><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>194</mn></math></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">4.</span><span style=\"font-family: Cambria Math;\"> If p + q = 6 and </span><span style=\"font-family: Cambria Math;\">pq</span><span style=\"font-family: Cambria Math;\"> = 4, then what is the value of 3(p</span><span style=\"font-family: Cambria Math;\">&sup3;</span><span style=\"font-family: Cambria Math;\">+ q&sup3;</span><span style=\"font-family: Cambria Math;\">)?</span></p>",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">4.</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">यदि</span><span style=\"font-family: Cambria Math;\"> p + q = 6 </span><span style=\"font-family: Nirmala UI;\">तथा</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">pq</span><span style=\"font-family: Cambria Math;\"> = 4 </span><span style=\"font-family: Nirmala UI;\">है</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">तो</span><span style=\"font-family: Cambria Math;\"> 3(p&sup3;</span><span style=\"font-family: Cambria Math;\">+ q&sup3;</span><span style=\"font-family: Cambria Math;\">) </span><span style=\"font-family: Nirmala UI;\">का</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">मान</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">क्या</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">है</span><span style=\"font-family: Cambria Math;\">?</span></p>",
                    options_en: ["<p>512</p>", "<p>144</p>", 
                                "<p>288</p>", "<p>432</p>"],
                    options_hi: ["<p>512</p>", "<p>144</p>",
                                "<p>288</p>", "<p>432</p>"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">4.(</span><span style=\"font-family: Cambria Math;\">d)</span><br><span style=\"font-weight: 400;\">P</span><span style=\"font-weight: 400;\"><span style=\"font-family: Cambria Math;\">&sup3;</span></span><span style=\"font-weight: 400;\"> + </span><span style=\"font-weight: 400;\">q<span style=\"font-family: Cambria Math;\">&sup3;</span></span><span style=\"font-weight: 400;\"> = (p + q)[</span><span style=\"font-weight: 400;\">(p + q</span><span style=\"font-weight: 400;\">)&sup2; </span><span style=\"font-weight: 400;\">- 3pq]</span><span style=\"font-weight: 400;\">&nbsp;</span><br><span style=\"font-weight: 400;\">= 6[</span><span style=\"font-weight: 400;\">6&sup2; </span><span style=\"font-weight: 400;\">- 12]</span><span style=\"font-weight: 400;\"> = 6 &times; 24 = 144</span><br><span style=\"font-weight: 400;\">Now, </span><span style=\"font-weight: 400;\">3(p<span style=\"font-family: Cambria Math;\">&sup3;</span></span><span style=\"font-weight: 400;\">+ q<span style=\"font-family: Cambria Math;\">&sup3;</span></span><span style=\"font-weight: 400;\">) = 3 &times; 144 = 432</span></p>",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">4.(</span><span style=\"font-family: Cambria Math;\">d)</span><br><span style=\"font-weight: 400;\">P</span><span style=\"font-weight: 400;\"><span style=\"font-family: Cambria Math;\">&sup3;</span></span><span style=\"font-weight: 400;\"> + </span><span style=\"font-weight: 400;\">q<span style=\"font-family: Cambria Math;\">&sup3;</span></span><span style=\"font-weight: 400;\"> = (p + q)[</span><span style=\"font-weight: 400;\">(p + q</span><span style=\"font-weight: 400;\">)&sup2; </span><span style=\"font-weight: 400;\">- 3pq]</span><span style=\"font-weight: 400;\">&nbsp;</span><br><span style=\"font-weight: 400;\">= 6[</span><span style=\"font-weight: 400;\">6&sup2; </span><span style=\"font-weight: 400;\">- 12]</span><span style=\"font-weight: 400;\"> = 6 &times; 24 = 144</span><br><span style=\"font-weight: 400;\">अब<strong id=\"docs-internal-guid-4a1e1d40-7fff-8f47-f56f-e1d411bf9d71\">,</strong> </span><span style=\"font-weight: 400;\">3(p<span style=\"font-family: Cambria Math;\">&sup3;</span></span><span style=\"font-weight: 400;\">+ q<span style=\"font-family: Cambria Math;\">&sup3;</span></span><span style=\"font-weight: 400;\">) = 3 &times; 144 = 432</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "17",
                    question_en: "<p>5.<span style=\"font-family: Cambria Math;\"> If <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">P</mi><mo>-</mo><mfrac><mn>1</mn><mi mathvariant=\"normal\">P</mi></mfrac><mo>=</mo><mn>6</mn></math></span><span style=\"font-family: Cambria Math;\"> then what is the value of <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi mathvariant=\"normal\">P</mi><mn>4</mn></msup><mo>+</mo><mfrac><mn>1</mn><msup><mi mathvariant=\"normal\">P</mi><mn>4</mn></msup></mfrac></math></span><span style=\"font-family: Cambria Math;\">&nbsp;?</span></p>",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">5.</span><span style=\"font-family: Nirmala UI;\">यदि</span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">P</mi><mo>-</mo><mfrac><mn>1</mn><mi mathvariant=\"normal\">P</mi></mfrac><mo>=</mo><mn>6</mn></math></span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">है</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">तो</span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi mathvariant=\"normal\">P</mi><mn>4</mn></msup><mo>+</mo><mfrac><mn>1</mn><msup><mi mathvariant=\"normal\">P</mi><mn>4</mn></msup></mfrac></math></span><span style=\"font-family: Nirmala UI;\">का</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">मान</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">क्या</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">है</span><span style=\"font-family: Cambria Math;\">?</span></p>",
                    options_en: ["<p>1562</p>", "<p>1432</p>", 
                                "<p>1442</p>", "<p>1444</p>"],
                    options_hi: ["<p>1562</p>", "<p>1432</p>",
                                "<p>1442</p>", "<p>1444</p>"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">5.(</span><span style=\"font-family: Cambria Math;\">c)</span><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">P</mi><mo>-</mo><mfrac><mn>1</mn><mi mathvariant=\"normal\">P</mi></mfrac><mo>=</mo><mn>6</mn></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi mathvariant=\"normal\">P</mi><mn>2</mn></msup><mo>+</mo><mfrac><mn>1</mn><msup><mi mathvariant=\"normal\">P</mi><mn>2</mn></msup></mfrac><mo>=</mo><msup><mn>6</mn><mn>2</mn></msup><mo>+</mo><mn>2</mn><mo>=</mo><mn>38</mn></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi mathvariant=\"normal\">P</mi><mn>4</mn></msup><mo>+</mo><mfrac><mn>1</mn><msup><mi mathvariant=\"normal\">P</mi><mn>4</mn></msup></mfrac><mo>=</mo><msup><mn>38</mn><mn>2</mn></msup><mo>-</mo><mn>2</mn><mo>=</mo><mn>1444</mn><mo>-</mo><mn>2</mn><mo>=</mo><mn>1442</mn></math></p>",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">5.(</span><span style=\"font-family: Cambria Math;\">c)</span><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">P</mi><mo>-</mo><mfrac><mn>1</mn><mi mathvariant=\"normal\">P</mi></mfrac><mo>=</mo><mn>6</mn></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi mathvariant=\"normal\">P</mi><mn>2</mn></msup><mo>+</mo><mfrac><mn>1</mn><msup><mi mathvariant=\"normal\">P</mi><mn>2</mn></msup></mfrac><mo>=</mo><msup><mn>6</mn><mn>2</mn></msup><mo>+</mo><mn>2</mn><mo>=</mo><mn>38</mn></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi mathvariant=\"normal\">P</mi><mn>4</mn></msup><mo>+</mo><mfrac><mn>1</mn><msup><mi mathvariant=\"normal\">P</mi><mn>4</mn></msup></mfrac><mo>=</mo><msup><mn>38</mn><mn>2</mn></msup><mo>-</mo><mn>2</mn><mo>=</mo><mn>1444</mn><mo>-</mo><mn>2</mn><mo>=</mo><mn>1442</mn></math></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "17",
                    question_en: "<p>6. <span style=\"font-family: Cambria Math;\">Simplify x</span><sup><span style=\"font-family: Cambria Math;\">4 </span></sup><span style=\"font-family: Cambria Math;\">- 15x</span><span style=\"font-family: Cambria Math;\"><sup>3</sup>&nbsp;</span><span style=\"font-family: Cambria Math;\">+15x</span><span style=\"font-family: Cambria Math;\"><sup>2</sup> </span><span style=\"font-family: Cambria Math;\">- 15x + 40; given x = 14.</span></p>",
                    question_hi: "<p>6.<span style=\"font-family: Cambria Math;\"> x<sup>4 </sup>- 15x<sup>3 </sup>+15x<sup>2&nbsp; </sup>- 15x +40 </span><span style=\"font-family: Nirmala UI;\">का</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">मान</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">ज्ञात</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">कीजिए</span><span style=\"font-family: Nirmala UI;\">।</span><span style=\"font-family: Cambria Math;\"> x = 14 </span><span style=\"font-family: Nirmala UI;\">दिया</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">गया</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">है</span><span style=\"font-family: Nirmala UI;\">।</span></p>",
                    options_en: ["<p>0</p>", "<p>40</p>", 
                                "<p>14</p>", "<p>26</p>"],
                    options_hi: ["<p>0</p>", "<p>40</p>",
                                "<p>14</p>", "<p>26</p>"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">6.(</span><span style=\"font-family: Cambria Math;\">d)</span><br><span style=\"font-family: Cambria Math;\">x<sup>4 </sup>- 15x<sup>3</sup> + 15x<sup>2</sup> - 15x&nbsp; + 40; (given x = 14)</span><br><span style=\"font-family: Cambria Math;\">x<sup>4 </sup>- 14x<sup>3 </sup></span><span style=\"font-family: Cambria Math;\">-&nbsp; x<sup>3 </sup></span><span style=\"font-family: Cambria Math;\">+ 14x<sup>2 </sup></span><span style=\"font-family: Cambria Math;\">+ x<sup>2 </sup></span><span style=\"font-family: Cambria Math;\">- 14x - x + 14 + 26</span><br><span style=\"font-weight: 400;\">x</span><sup><span style=\"font-weight: 400;\">3</span></sup><span style=\"font-weight: 400;\">(x - 14) -</span><span style=\"font-weight: 400;\">x</span><sup><span style=\"font-weight: 400;\">2</span></sup><span style=\"font-weight: 400;\">(x - 14) + x(x - 14) - 1(x - 14) + 26</span><br><span style=\"font-family: Cambria Math;\">Putting x = 14, we get,</span><br><span style=\"font-family: Cambria Math;\">x<sup>4 </sup>- 15x<sup>3</sup> + 15x<sup>2</sup> - 15x + 40 = 26</span><span style=\"font-family: Cambria Math;\"> </span></p>",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">6.(</span><span style=\"font-family: Cambria Math;\">d)</span><br><span style=\"font-family: Cambria Math;\">x<sup>4 </sup>- 15x<sup>3</sup> + 15x<sup>2</sup> - 15x&nbsp; + 40; (दिया गया है<strong id=\"docs-internal-guid-66cf6074-7fff-774c-147f-5283c63f1703\"> </strong>x = 14)</span><br><span style=\"font-family: Cambria Math;\">x<sup>4 </sup>- 14x<sup>3 </sup></span><span style=\"font-family: Cambria Math;\">-&nbsp; x<sup>3 </sup></span><span style=\"font-family: Cambria Math;\">+ 14x<sup>2 </sup></span><span style=\"font-family: Cambria Math;\">+ x<sup>2 </sup></span><span style=\"font-family: Cambria Math;\">- 14x - x + 14 + 26</span><br><span style=\"font-weight: 400;\">x</span><sup><span style=\"font-weight: 400;\">3</span></sup><span style=\"font-weight: 400;\">(x - 14) -</span><span style=\"font-weight: 400;\">x</span><sup><span style=\"font-weight: 400;\">2</span></sup><span style=\"font-weight: 400;\">(x - 14) + x(x - 14) - 1(x - 14) + 26</span><br>x = 14 रखने पर, हम पाते हैं,<br><span style=\"font-family: Cambria Math;\">x<sup>4 </sup>- 15x<sup>3</sup> + 15x<sup>2</sup> - 15x + 40 = 26</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">7.</span><span style=\"font-family: Cambria Math;\"> If&nbsp; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">y</mi><mo>+</mo><mfrac><mn>1</mn><mi mathvariant=\"normal\">y</mi></mfrac><mo>=</mo><mn>11</mn></math></span><span style=\"font-family: Cambria Math;\">, then the value of </span><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi mathvariant=\"normal\">y</mi><mn>3</mn></msup><mo>-</mo><mfrac><mn>1</mn><msup><mi mathvariant=\"normal\">y</mi><mn>3</mn></msup></mfrac></math></span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">is:</span></p>",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">7. </span><span style=\"font-family: Nirmala UI;\">यदि</span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">y</mi><mo>+</mo><mfrac><mn>1</mn><mi mathvariant=\"normal\">y</mi></mfrac><mo>=</mo><mn>11</mn></math></span><span style=\"font-family: Cambria Math;\">&nbsp;</span><span style=\"font-family: Nirmala UI;\">है</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">तो</span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi mathvariant=\"normal\">y</mi><mn>3</mn></msup><mo>-</mo><mfrac><mn>1</mn><msup><mi mathvariant=\"normal\">y</mi><mn>3</mn></msup></mfrac></math></span><span style=\"font-family: Nirmala UI;\">का</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">मान</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">ज्ञात</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">करें</span><span style=\"font-family: Nirmala UI;\">।</span></p>",
                    options_en: ["<p>345<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>13</mn></msqrt></math></p>", "<p>360<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>13</mn></msqrt></math></p>", 
                                "<p>352<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>13</mn></msqrt></math></p>", "<p>368<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>13</mn></msqrt></math></p>"],
                    options_hi: ["<p>345<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>13</mn></msqrt></math><span style=\"font-family: Cambria Math;\"> </span></p>", "<p>360<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>13</mn></msqrt></math></p>",
                                "<p>352<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>13</mn></msqrt></math></p>", "<p>368<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>13</mn></msqrt></math></p>"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">7.(</span><span style=\"font-family: Cambria Math;\">b)</span><br><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>y</mi><mo>-</mo><mfrac><mn>1</mn><mi>y</mi></mfrac><mo>=</mo><msqrt><msup><mn>11</mn><mn>2</mn></msup><mo>-</mo><mn>4</mn></msqrt><mo>=</mo><msqrt><mn>121</mn><mo>-</mo><mn>4</mn></msqrt><mo>=</mo><msqrt><mn>117</mn></msqrt><mo>=</mo><mn>3</mn><msqrt><mn>13</mn></msqrt></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi mathvariant=\"normal\">y</mi><mn>3</mn></msup><mo>-</mo><mfrac><mn>1</mn><msup><mi mathvariant=\"normal\">y</mi><mn>3</mn></msup></mfrac><mo>=</mo><msup><mrow><mo>(</mo><mn>3</mn><msqrt><mn>13</mn></msqrt><mo>)</mo></mrow><mn>3</mn></msup><mo>+</mo><mn>3</mn><mo>&#215;</mo><mn>3</mn><msqrt><mn>13</mn></msqrt><mo>=</mo><mn>351</mn><msqrt><mn>13</mn></msqrt><mo>+</mo><mn>9</mn><msqrt><mn>13</mn><mo>&#160;</mo></msqrt><mo>=</mo><mn>360</mn><msqrt><mn>13</mn></msqrt></math></span></p>",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">7.(</span><span style=\"font-family: Cambria Math;\">b)</span><br><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>y</mi><mo>-</mo><mfrac><mn>1</mn><mi>y</mi></mfrac><mo>=</mo><msqrt><msup><mn>11</mn><mn>2</mn></msup><mo>-</mo><mn>4</mn></msqrt><mo>=</mo><msqrt><mn>121</mn><mo>-</mo><mn>4</mn></msqrt><mo>=</mo><msqrt><mn>117</mn></msqrt><mo>=</mo><mn>3</mn><msqrt><mn>13</mn></msqrt></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi mathvariant=\"normal\">y</mi><mn>3</mn></msup><mo>-</mo><mfrac><mn>1</mn><msup><mi mathvariant=\"normal\">y</mi><mn>3</mn></msup></mfrac><mo>=</mo><msup><mrow><mo>(</mo><mn>3</mn><msqrt><mn>13</mn></msqrt><mo>)</mo></mrow><mn>3</mn></msup><mo>+</mo><mn>3</mn><mo>&#215;</mo><mn>3</mn><msqrt><mn>13</mn></msqrt><mo>=</mo><mn>351</mn><msqrt><mn>13</mn></msqrt><mo>+</mo><mn>9</mn><msqrt><mn>13</mn><mo>&#160;</mo></msqrt><mo>=</mo><mn>360</mn><msqrt><mn>13</mn></msqrt></math></span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">8.</span><span style=\"font-family: Cambria Math;\"> If <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mi mathvariant=\"normal\">x</mi><mo>-</mo><mfrac><mn>1</mn><mi mathvariant=\"normal\">x</mi></mfrac><mo>)</mo></mrow><mn>2</mn></msup><mo>=</mo><mn>12</mn></math></span><span style=\"font-family: Cambria Math;\">&nbsp;,</span><span style=\"font-family: Cambria Math;\"> what is the value of <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup><mo>-</mo><mfrac><mn>1</mn><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup></mfrac><mo>)</mo></math> </span><span style=\"font-family: Cambria Math;\">given that x &gt; 0?</span></p>",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">8. </span><span style=\"font-family: Nirmala UI;\">यदि</span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mi mathvariant=\"normal\">x</mi><mo>-</mo><mfrac><mn>1</mn><mi mathvariant=\"normal\">x</mi></mfrac><mo>)</mo></mrow><mn>2</mn></msup><mo>=</mo><mn>12</mn></math></span><span style=\"font-family: Cambria Math;\">&nbsp;</span><span style=\"font-family: Nirmala UI;\">है</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">और</span><span style=\"font-family: Cambria Math;\"> x &gt; 0 </span><span style=\"font-family: Nirmala UI;\">दिया</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">गया</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">है</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">तो</span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mtext>(x</mtext><mn>2</mn></msup><mo>-</mo><mfrac><mn>1</mn><msup><mi>x</mi><mn>2</mn></msup></mfrac><mo>)</mo></math></span><span style=\"font-family: Cambria Math;\">&nbsp;</span><span style=\"font-family: Nirmala UI;\">का</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">मान</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">क्या</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">होगा</span><span style=\"font-family: Cambria Math;\">?,</span></p>",
                    options_en: ["<p>6<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>2</mn></msqrt></math></p>", "<p>8<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math></p>", 
                                "<p>6<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math></p>", "<p>8<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>2</mn></msqrt></math></p>"],
                    options_hi: ["<p>6<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>2</mn></msqrt></math></p>", "<p>8<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math></p>",
                                "<p>6<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math></p>", "<p>8<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>2</mn></msqrt></math></p>"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">8.(</span><span style=\"font-family: Cambria Math;\">b)</span><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mi mathvariant=\"normal\">x</mi><mo>-</mo><mfrac><mn>1</mn><mi mathvariant=\"normal\">x</mi></mfrac><mo>)</mo></mrow><mn>2</mn></msup><mo>=</mo><mn>12</mn></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">x</mi><mo>-</mo><mfrac><mn>1</mn><mi mathvariant=\"normal\">x</mi></mfrac><mo>=</mo><msqrt><mn>12</mn></msqrt><mo>=</mo><mo>&#160;</mo><mn>2</mn><msqrt><mn>3</mn></msqrt></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>Now</mi><mo>,</mo><mo>&#160;</mo><mi mathvariant=\"normal\">x</mi><mo>+</mo><mfrac><mn>1</mn><mi mathvariant=\"normal\">x</mi></mfrac><mo>=</mo><msqrt><msup><mrow><mo>(</mo><mn>2</mn><msqrt><mn>3</mn></msqrt><mo>)</mo></mrow><mn>2</mn></msup><mo>+</mo><mn>4</mn></msqrt><mo>=</mo><msqrt><mn>16</mn></msqrt><mo>=</mo><mn>4</mn></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>so</mi><mo>,</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup><mo>-</mo><mfrac><mn>1</mn><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup></mfrac><mo>=</mo><mn>4</mn><mo>&#215;</mo><mn>2</mn><msqrt><mn>3</mn></msqrt><mo>=</mo><mn>8</mn><msqrt><mn>3</mn></msqrt></math></p>",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">8.(</span><span style=\"font-family: Cambria Math;\">b)</span><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mi mathvariant=\"normal\">x</mi><mo>-</mo><mfrac><mn>1</mn><mi mathvariant=\"normal\">x</mi></mfrac><mo>)</mo></mrow><mn>2</mn></msup><mo>=</mo><mn>12</mn></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">x</mi><mo>-</mo><mfrac><mn>1</mn><mi mathvariant=\"normal\">x</mi></mfrac><mo>=</mo><msqrt><mn>12</mn></msqrt><mo>=</mo><mo>&#160;</mo><mn>2</mn><msqrt><mn>3</mn></msqrt></math><br>अब<strong id=\"docs-internal-guid-a6b658ec-7fff-64dc-ed36-f62ae5a01ee3\">, </strong><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>x</mi><mo>+</mo><mfrac><mn>1</mn><mi mathvariant=\"normal\">x</mi></mfrac><mo>=</mo><msqrt><msup><mrow><mo>(</mo><mn>2</mn><msqrt><mn>3</mn></msqrt><mo>)</mo></mrow><mn>2</mn></msup><mo>+</mo><mn>4</mn></msqrt><mo>=</mo><msqrt><mn>16</mn></msqrt><mo>=</mo><mn>4</mn></math><br>अतः <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>x</mi><mn>2</mn></msup><mo>-</mo><mfrac><mn>1</mn><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup></mfrac><mo>=</mo><mn>4</mn><mo>&#215;</mo><mn>2</mn><msqrt><mn>3</mn></msqrt><mo>=</mo><mn>8</mn><msqrt><mn>3</mn></msqrt></math></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">9.</span><span style=\"font-family: Cambria Math;\"> a</span><sup><span style=\"font-family: Cambria Math;\">3 </span></sup><span style=\"font-family: Cambria Math;\">+ b<sup>3 </sup></span><span style=\"font-family: Cambria Math;\">+ c<sup>3</sup></span><span style=\"font-family: Cambria Math;\">&nbsp;= 3abc if ______.</span></p>",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">9. a<sup>3 </sup>+ b<sup>3 </sup>+ c<sup>3</sup> = 3abc&nbsp;</span><span style=\"font-family: Nirmala UI;\">यदि</span><span style=\"font-family: Cambria Math;\">_________|</span></p>",
                    options_en: ["<p>a + b + c = - 3</p>", "<p>a<span style=\"font-family: Cambria Math;\"> + b + c = 0</span></p>", 
                                "<p>a<span style=\"font-family: Cambria Math;\"> + b + c = 1</span></p>", "<p>a + b + c = - 1</p>"],
                    options_hi: ["<p>a + b + c = - 3</p>", "<p>a + b + c = 0</p>",
                                "<p>a + b + c = 1</p>", "<p>a + b + c = - 1</p>"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">9.(</span><span style=\"font-family: Cambria Math;\">b)</span><br><span style=\"font-family: Cambria Math;\">a<sup>3 </sup>+ b<sup>3 </sup>+ c<sup>3</sup> = 3abc, either </span><span style=\"font-family: Cambria Math;\">a&nbsp; +&nbsp; b&nbsp; +&nbsp; c = 0 or a = b = c</span></p>",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">9.(</span><span style=\"font-family: Cambria Math;\">b)</span><br><span style=\"font-family: Cambria Math;\">a<sup>3 </sup>+ b<sup>3 </sup>+ c<sup>3</sup> = 3abc, either </span><span style=\"font-family: Cambria Math;\">a&nbsp; +&nbsp; b&nbsp; +&nbsp; c = 0 or a = b = c</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "17",
                    question_en: "<p>10.<span style=\"font-family: Cambria Math;\"> If&nbsp; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">m</mi><mo>+</mo><mfrac><mn>1</mn><mrow><mi mathvariant=\"normal\">m</mi><mo>-</mo><mn>2</mn></mrow></mfrac><mo>=</mo><mn>4</mn></math></span><span style=\"font-family: Cambria Math;\">, then find the value of (m &minus; </span><span style=\"font-family: Cambria Math;\">2)&sup2;</span><span style=\"font-family: Cambria Math;\"> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msup><mrow><mo>(</mo><mi mathvariant=\"normal\">m</mi><mo>-</mo><mn>2</mn><mo>)</mo></mrow><mn>2</mn></msup></mfrac></math> </span><span style=\"font-family: Cambria Math;\">.</span></p>",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">10. </span><span style=\"font-family: Nirmala UI;\">यदि</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">m + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><mi mathvariant=\"normal\">m</mi><mo>-</mo><mn>2</mn></mrow></mfrac></math> </span><span style=\"font-family: Cambria Math;\"> = 4, </span><span style=\"font-family: Nirmala UI;\">है</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">तो</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">(m &minus; 2)&sup2; + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msup><mrow><mo>(</mo><mi mathvariant=\"normal\">m</mi><mo>-</mo><mn>2</mn><mo>)</mo></mrow><mn>2</mn></msup></mfrac></math> </span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">का</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">मान</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">ज्ञात</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">कीजिए</span><span style=\"font-family: Nirmala UI;\">।</span></p>",
                    options_en: ["<p>- 2</p>", "<p>4</p>", 
                                "<p>0</p>", "<p>2</p>"],
                    options_hi: ["<p>- 2</p>", "<p>4</p>",
                                "<p>0</p>", "<p>2</p>"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">10.(</span><span style=\"font-family: Cambria Math;\">d)</span><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">m</mi><mo>+</mo><mfrac><mn>1</mn><mrow><mi mathvariant=\"normal\">m</mi><mo>-</mo><mn>2</mn></mrow></mfrac><mo>=</mo><mn>4</mn></math><br><span style=\"font-family: Cambria Math;\">Subtracting both side by 2. We </span><span style=\"font-family: Cambria Math;\">get :</span><br><span style=\"font-family: Cambria Math;\">m - 2 + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><mi mathvariant=\"normal\">m</mi><mo>-</mo><mn>2</mn></mrow></mfrac></math>&nbsp;</span><span style=\"font-family: Cambria Math;\">=&nbsp; 2</span><br><span style=\"font-family: Cambria Math;\">So, (m &minus; </span><span style=\"font-family: Cambria Math;\">2)&sup2;</span><span style=\"font-family: Cambria Math;\"> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msup><mrow><mo>(</mo><mi mathvariant=\"normal\">m</mi><mo>-</mo><mn>2</mn><mo>)</mo></mrow><mn>2</mn></msup></mfrac></math></span><span style=\"font-family: Cambria Math;\">= 2<sup>2</sup> </span><span style=\"font-family: Cambria Math;\">- 2 = 2</span></p>",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">10.(</span><span style=\"font-family: Cambria Math;\">d)</span><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">m</mi><mo>+</mo><mfrac><mn>1</mn><mrow><mi mathvariant=\"normal\">m</mi><mo>-</mo><mn>2</mn></mrow></mfrac><mo>=</mo><mn>4</mn></math><br>दोनों पक्षों में 2 घटाने पर, हम प्राप्त करते है :<br><span style=\"font-family: Cambria Math;\">m - 2 + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><mi mathvariant=\"normal\">m</mi><mo>-</mo><mn>2</mn></mrow></mfrac></math>&nbsp;</span><span style=\"font-family: Cambria Math;\">=&nbsp; 2</span><br><span style=\"font-family: Cambria Math;\">अतः <strong id=\"docs-internal-guid-d67f722f-7fff-0d73-b6b7-7ecdae5b9087\"></strong>(m &minus;&nbsp;</span><span style=\"font-family: Cambria Math;\">2)&sup2;</span><span style=\"font-family: Cambria Math;\"> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msup><mrow><mo>(</mo><mi mathvariant=\"normal\">m</mi><mo>-</mo><mn>2</mn><mo>)</mo></mrow><mn>2</mn></msup></mfrac></math></span><span style=\"font-family: Cambria Math;\">= 2<sup>2</sup> </span><span style=\"font-family: Cambria Math;\">- 2 = 2</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "17",
                    question_en: "<p>11.<span style=\"font-family: Cambria Math;\"> If 5x &minus; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mi mathvariant=\"normal\">x</mi></mfrac></math> </span><span style=\"font-family: Cambria Math;\"> + 6 = 0, then x&sup2; + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup></mfrac></math> </span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">is :</span><span style=\"font-family: Cambria Math;\"> </span></p>",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">11. </span><span style=\"font-family: Nirmala UI;\">यदि</span><span style=\"font-family: Cambria Math;\"> 5x &minus; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mi mathvariant=\"normal\">x</mi></mfrac></math></span><span style=\"font-family: Cambria Math;\"> + 6 = 0 </span><span style=\"font-family: Nirmala UI;\">है</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">तो</span><span style=\"font-family: Cambria Math;\"> x&sup2; + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup></mfrac></math></span><span style=\"font-family: Cambria Math;\"> = ?</span></p>",
                    options_en: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>86</mn><mn>25</mn></mfrac></math></p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>43</mn><mn>12</mn></mfrac></math></p>", 
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>81</mn><mn>10</mn></mfrac></math></p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>86</mn><mn>11</mn></mfrac></math></p>"],
                    options_hi: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>86</mn><mn>25</mn></mfrac></math></p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>43</mn><mn>12</mn></mfrac></math></p>",
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>81</mn><mn>10</mn></mfrac></math></p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>86</mn><mn>11</mn></mfrac></math></p>"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">11.(</span><span style=\"font-family: Cambria Math;\">a)</span><br><span style=\"font-family: Cambria Math;\">5x &minus; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mi mathvariant=\"normal\">x</mi></mfrac></math></span><span style=\"font-family: Cambria Math;\"> + 6 = 0</span><br><span style=\"font-family: Cambria Math;\">5x &minus; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mi mathvariant=\"normal\">x</mi></mfrac></math> </span><span style=\"font-family: Cambria Math;\">&nbsp;= -6</span><br><span style=\"font-family: Cambria Math;\">5(x - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi mathvariant=\"normal\">x</mi></mfrac></math></span><span style=\"font-family: Cambria Math;\">) = -6</span><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><mi mathvariant=\"normal\">x</mi><mo>-</mo><mfrac><mn>1</mn><mi mathvariant=\"normal\">x</mi></mfrac><mo>)</mo><mo>=</mo><mfrac><mrow><mo>-</mo><mn>6</mn></mrow><mn>5</mn></mfrac></math><br><span style=\"font-family: Cambria Math;\">x&sup2; + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup></mfrac></math></span><span style=\"font-family: Cambria Math;\"> = (</span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>-</mo><mn>6</mn></mrow><mn>5</mn></mfrac></math>)<sup>2 </sup>+ 2 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>86</mn><mn>25</mn></mfrac></math> </span></p>",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">11.(</span><span style=\"font-family: Cambria Math;\">a)</span><br><span style=\"font-family: Cambria Math;\">5x &minus; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mi mathvariant=\"normal\">x</mi></mfrac></math></span><span style=\"font-family: Cambria Math;\"> + 6 = 0</span><br><span style=\"font-family: Cambria Math;\">5x &minus; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mi mathvariant=\"normal\">x</mi></mfrac></math> </span><span style=\"font-family: Cambria Math;\">&nbsp;= -6</span><br><span style=\"font-family: Cambria Math;\">5(x - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi mathvariant=\"normal\">x</mi></mfrac></math></span><span style=\"font-family: Cambria Math;\">) = -6</span><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><mi mathvariant=\"normal\">x</mi><mo>-</mo><mfrac><mn>1</mn><mi mathvariant=\"normal\">x</mi></mfrac><mo>)</mo><mo>=</mo><mfrac><mrow><mo>-</mo><mn>6</mn></mrow><mn>5</mn></mfrac></math><br><span style=\"font-family: Cambria Math;\">x&sup2; + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup></mfrac></math></span><span style=\"font-family: Cambria Math;\"> = (</span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>-</mo><mn>6</mn></mrow><mn>5</mn></mfrac></math>)<sup>2 </sup>+ 2 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>86</mn><mn>25</mn></mfrac></math></span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "17",
                    question_en: "<p>12.<span style=\"font-family: Cambria Math;\"> If a = 26 and b = 22, then the value of <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi mathvariant=\"normal\">a</mi><mn>3</mn></msup><mo>-</mo><msup><mi mathvariant=\"normal\">b</mi><mn>3</mn></msup></mrow><mrow><msup><mi mathvariant=\"normal\">a</mi><mn>2</mn></msup><mo>-</mo><msup><mi mathvariant=\"normal\">b</mi><mn>2</mn></msup></mrow></mfrac><mo>-</mo><mfrac><mrow><mn>3</mn><mi>ab</mi></mrow><mrow><mi mathvariant=\"normal\">a</mi><mo>+</mo><mi mathvariant=\"normal\">b</mi></mrow></mfrac></math> </span><span style=\"font-family: Cambria Math;\">is _______</span></p>",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">12. </span><span style=\"font-family: Nirmala UI;\">यदि</span><span style=\"font-family: Cambria Math;\"> a = 26 </span><span style=\"font-family: Nirmala UI;\">और</span><span style=\"font-family: Cambria Math;\"> b = 22 </span><span style=\"font-family: Nirmala UI;\">है</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">तो <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi mathvariant=\"normal\">a</mi><mn>3</mn></msup><mo>-</mo><msup><mi mathvariant=\"normal\">b</mi><mn>3</mn></msup></mrow><mrow><msup><mi mathvariant=\"normal\">a</mi><mn>2</mn></msup><mo>-</mo><msup><mi mathvariant=\"normal\">b</mi><mn>2</mn></msup></mrow></mfrac><mo>-</mo><mfrac><mrow><mn>3</mn><mi>ab</mi></mrow><mrow><mi mathvariant=\"normal\">a</mi><mo>+</mo><mi mathvariant=\"normal\">b</mi></mrow></mfrac></math></span><span style=\"font-family: Cambria Math;\">&nbsp;</span><span style=\"font-family: Nirmala UI;\">का</span><span style=\"font-family: Cambria Math;\"> _______</span><span style=\"font-family: Nirmala UI;\">मान</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">है</span><span style=\"font-family: Nirmala UI;\">।</span></p>",
                    options_en: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>3</mn></mfrac></math></p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>13</mn><mn>11</mn></mfrac></math></p>", 
                                "<p><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math> </span></p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>11</mn><mn>13</mn></mfrac></math></p>"],
                    options_hi: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>3</mn></mfrac></math></p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>13</mn><mn>11</mn></mfrac></math></p>",
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math><span style=\"font-family: Cambria Math;\"> </span></p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>11</mn><mn>13</mn></mfrac></math></p>"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">12.(</span><span style=\"font-family: Cambria Math;\">c)</span><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi mathvariant=\"normal\">a</mi><mn>3</mn></msup><mo>-</mo><msup><mi mathvariant=\"normal\">b</mi><mn>3</mn></msup></mrow><mrow><msup><mi mathvariant=\"normal\">a</mi><mn>2</mn></msup><mo>-</mo><msup><mi mathvariant=\"normal\">b</mi><mn>2</mn></msup></mrow></mfrac><mo>-</mo><mfrac><mrow><mn>3</mn><mi>ab</mi></mrow><mrow><mi mathvariant=\"normal\">a</mi><mo>+</mo><mi mathvariant=\"normal\">b</mi></mrow></mfrac><mo>=</mo><mfrac><mrow><mo>(</mo><mi mathvariant=\"normal\">a</mi><mo>-</mo><mi mathvariant=\"normal\">b</mi><mo>)</mo><mo>(</mo><msup><mi mathvariant=\"normal\">a</mi><mn>2</mn></msup><mo>+</mo><msup><mi mathvariant=\"normal\">b</mi><mn>2</mn></msup><mo>+</mo><mi>ab</mi><mo>)</mo></mrow><mrow><mo>(</mo><mi mathvariant=\"normal\">a</mi><mo>-</mo><mi mathvariant=\"normal\">b</mi><mo>)</mo><mo>(</mo><mi mathvariant=\"normal\">a</mi><mo>+</mo><mi mathvariant=\"normal\">b</mi><mo>)</mo></mrow></mfrac><mo>-</mo><mfrac><mrow><mn>3</mn><mi>ab</mi></mrow><mrow><mi mathvariant=\"normal\">a</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mi mathvariant=\"normal\">b</mi><mo>&#160;</mo></mrow></mfrac></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>=</mo><mfrac><mrow><mo>(</mo><msup><mi mathvariant=\"normal\">a</mi><mn>2</mn></msup><mo>+</mo><msup><mi mathvariant=\"normal\">b</mi><mn>2</mn></msup><mo>+</mo><mi>ab</mi><mo>)</mo></mrow><mrow><mi mathvariant=\"normal\">a</mi><mo>+</mo><mi mathvariant=\"normal\">b</mi></mrow></mfrac><mo>-</mo><mfrac><mrow><mn>3</mn><mi>ab</mi></mrow><mrow><mo>&#160;</mo><mi mathvariant=\"normal\">a</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mi mathvariant=\"normal\">b</mi></mrow></mfrac><mo>=</mo><mfrac><mrow><msup><mi mathvariant=\"normal\">a</mi><mn>2</mn></msup><mo>+</mo><msup><mi mathvariant=\"normal\">b</mi><mn>2</mn></msup><mo>-</mo><mn>2</mn><mi>ab</mi></mrow><mrow><mi mathvariant=\"normal\">a</mi><mo>+</mo><mi mathvariant=\"normal\">b</mi><mo>&#160;</mo></mrow></mfrac></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>=</mo><mfrac><msup><mrow><mo>(</mo><mi mathvariant=\"normal\">a</mi><mo>-</mo><mi mathvariant=\"normal\">b</mi><mo>)</mo></mrow><mn>2</mn></msup><mrow><mi mathvariant=\"normal\">a</mi><mo>+</mo><mi mathvariant=\"normal\">b</mi></mrow></mfrac><mo>=</mo><mfrac><mrow><mo>&#160;</mo><msup><mrow><mo>(</mo><mn>26</mn><mo>-</mo><mn>22</mn><mo>)</mo></mrow><mn>2</mn></msup></mrow><mrow><mn>26</mn><mo>+</mo><mn>22</mn></mrow></mfrac><mo>=</mo><mfrac><mn>16</mn><mn>48</mn></mfrac><mo>=</mo><mfrac><mrow><mo>&#160;</mo><mn>1</mn></mrow><mn>3</mn></mfrac></math></p>",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">12.(</span><span style=\"font-family: Cambria Math;\">c)</span><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi mathvariant=\"normal\">a</mi><mn>3</mn></msup><mo>-</mo><msup><mi mathvariant=\"normal\">b</mi><mn>3</mn></msup></mrow><mrow><msup><mi mathvariant=\"normal\">a</mi><mn>2</mn></msup><mo>-</mo><msup><mi mathvariant=\"normal\">b</mi><mn>2</mn></msup></mrow></mfrac><mo>-</mo><mfrac><mrow><mn>3</mn><mi>ab</mi></mrow><mrow><mi mathvariant=\"normal\">a</mi><mo>+</mo><mi mathvariant=\"normal\">b</mi></mrow></mfrac><mo>=</mo><mfrac><mrow><mo>(</mo><mi mathvariant=\"normal\">a</mi><mo>-</mo><mi mathvariant=\"normal\">b</mi><mo>)</mo><mo>(</mo><msup><mi mathvariant=\"normal\">a</mi><mn>2</mn></msup><mo>+</mo><msup><mi mathvariant=\"normal\">b</mi><mn>2</mn></msup><mo>+</mo><mi>ab</mi><mo>)</mo></mrow><mrow><mo>(</mo><mi mathvariant=\"normal\">a</mi><mo>-</mo><mi mathvariant=\"normal\">b</mi><mo>)</mo><mo>(</mo><mi mathvariant=\"normal\">a</mi><mo>+</mo><mi mathvariant=\"normal\">b</mi><mo>)</mo></mrow></mfrac><mo>-</mo><mfrac><mrow><mn>3</mn><mi>ab</mi></mrow><mrow><mi mathvariant=\"normal\">a</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mi mathvariant=\"normal\">b</mi><mo>&#160;</mo></mrow></mfrac></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>=</mo><mfrac><mrow><mo>(</mo><msup><mi mathvariant=\"normal\">a</mi><mn>2</mn></msup><mo>+</mo><msup><mi mathvariant=\"normal\">b</mi><mn>2</mn></msup><mo>+</mo><mi>ab</mi><mo>)</mo></mrow><mrow><mi mathvariant=\"normal\">a</mi><mo>+</mo><mi mathvariant=\"normal\">b</mi></mrow></mfrac><mo>-</mo><mfrac><mrow><mn>3</mn><mi>ab</mi></mrow><mrow><mo>&#160;</mo><mi mathvariant=\"normal\">a</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mi mathvariant=\"normal\">b</mi></mrow></mfrac><mo>=</mo><mfrac><mrow><msup><mi mathvariant=\"normal\">a</mi><mn>2</mn></msup><mo>+</mo><msup><mi mathvariant=\"normal\">b</mi><mn>2</mn></msup><mo>-</mo><mn>2</mn><mi>ab</mi></mrow><mrow><mi mathvariant=\"normal\">a</mi><mo>+</mo><mi mathvariant=\"normal\">b</mi><mo>&#160;</mo></mrow></mfrac></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>=</mo><mfrac><msup><mrow><mo>(</mo><mi mathvariant=\"normal\">a</mi><mo>-</mo><mi mathvariant=\"normal\">b</mi><mo>)</mo></mrow><mn>2</mn></msup><mrow><mi mathvariant=\"normal\">a</mi><mo>+</mo><mi mathvariant=\"normal\">b</mi></mrow></mfrac><mo>=</mo><mfrac><mrow><mo>&#160;</mo><msup><mrow><mo>(</mo><mn>26</mn><mo>-</mo><mn>22</mn><mo>)</mo></mrow><mn>2</mn></msup></mrow><mrow><mn>26</mn><mo>+</mo><mn>22</mn></mrow></mfrac><mo>=</mo><mfrac><mn>16</mn><mn>48</mn></mfrac><mo>=</mo><mfrac><mrow><mo>&#160;</mo><mn>1</mn></mrow><mn>3</mn></mfrac></math></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">13.</span><span style=\"font-family: Cambria Math;\"> If a +<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi mathvariant=\"normal\">a</mi></mfrac></math> </span><span style=\"font-family: Cambria Math;\"> = </span><span style=\"font-family: Cambria Math;\">5, </span><span style=\"font-family: Cambria Math;\">then what is the value of a</span><sup><span style=\"font-family: Cambria Math;\">3</span></sup><span style=\"font-family: Cambria Math;\"><sup> </sup>+<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msup><mi mathvariant=\"normal\">a</mi><mn>3</mn></msup></mfrac></math> </span><span style=\"font-family: Cambria Math;\"> = ?</span></p>",
                    question_hi: "<p>13. <span style=\"font-family: Nirmala UI;\">यदि</span><span style=\"font-family: Cambria Math;\"> a</span><span style=\"font-family: Cambria Math;\"> +<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi mathvariant=\"normal\">a</mi></mfrac></math> </span><span style=\"font-family: Cambria Math;\"> = 5 </span><span style=\"font-family: Nirmala UI;\">है</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">तो</span><span style=\"font-family: Cambria Math;\"> a</span><sup><span style=\"font-family: Cambria Math;\">3</span></sup><span style=\"font-family: Cambria Math;\"> +<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msup><mi mathvariant=\"normal\">a</mi><mn>3</mn></msup></mfrac></math> </span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">का</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">मान</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">क्या</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">है</span><span style=\"font-family: Cambria Math;\">?</span></p>",
                    options_en: ["<p>110</p>", "<p>15</p>", 
                                "<p>105</p>", "<p>- 10</p>"],
                    options_hi: ["<p>110</p>", "<p>15</p>",
                                "<p>105</p>", "<p>- 10</p>"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">13.(</span><span style=\"font-family: Cambria Math;\">a) </span><span style=\"font-family: Cambria Math;\">a + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi mathvariant=\"normal\">a</mi></mfrac></math></span><span style=\"font-family: Cambria Math;\">= 5</span><br><span style=\"font-family: Cambria Math;\">a</span><sup><span style=\"font-family: Cambria Math;\">3</span></sup><span style=\"font-family: Cambria Math;\"> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msup><mi mathvariant=\"normal\">a</mi><mn>3</mn></msup></mfrac></math></span><span style=\"font-family: Cambria Math;\"> = 5<sup>3 </sup></span><span style=\"font-family: Cambria Math;\">- 3 &times; 5 = 125 - 15 = 110</span></p>",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">13.(</span><span style=\"font-family: Cambria Math;\">a) </span><span style=\"font-family: Cambria Math;\">a + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi mathvariant=\"normal\">a</mi></mfrac></math></span><span style=\"font-family: Cambria Math;\">= 5</span><br><span style=\"font-family: Cambria Math;\">a</span><sup><span style=\"font-family: Cambria Math;\">3</span></sup><span style=\"font-family: Cambria Math;\"> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msup><mi mathvariant=\"normal\">a</mi><mn>3</mn></msup></mfrac></math></span><span style=\"font-family: Cambria Math;\">= 5<sup>3 </sup></span><span style=\"font-family: Cambria Math;\">- 3 &times; 5 = 125 - 15 = 110</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">14. </span><span style=\"font-family: Cambria Math;\">What is the value of a<sup>2 </sup>+ b&sup2; +&nbsp; c&sup2; -&nbsp; 2ab - 2bc + 2ca ?</span></p>",
                    question_hi: "<p>14.<span style=\"font-family: Cambria Math;\"> a<sup>2 </sup></span><span style=\"font-family: Cambria Math;\">+ b&sup2; </span><span style=\"font-family: Cambria Math;\">+ c&sup2; </span><span style=\"font-family: Cambria Math;\">- 2ab - 2bc + 2</span><span style=\"font-family: Cambria Math;\">ca </span><span style=\"font-family: Nirmala UI;\">का</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">मान</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">क्या</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">है</span><span style=\"font-family: Cambria Math;\">?</span></p>",
                    options_en: ["<p>(<span style=\"font-family: Cambria Math;\">2a + b + c)&sup2;</span></p>", "<p>(<span style=\"font-family: Cambria Math;\">a - b + c)&sup2;</span></p>", 
                                "<p>(a- b- 2c)<span style=\"font-family: Cambria Math;\">&sup2;</span></p>", "<p>(a + 2b &ndash; c)<span style=\"font-family: Cambria Math;\">&sup2;</span></p>"],
                    options_hi: ["<p>(<span style=\"font-family: Cambria Math;\">2a + b + c)&sup2;</span></p>", "<p>(<span style=\"font-family: Cambria Math;\">a - b + c)&sup2;</span></p>",
                                "<p>(a - b - 2c)<span style=\"font-family: Cambria Math;\">&sup2;</span></p>", "<p>(a + 2b &ndash; c)<span style=\"font-family: Cambria Math;\">&sup2;</span></p>"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">14.(</span><span style=\"font-family: Cambria Math;\">b)</span><br><span style=\"font-family: Cambria Math;\">(a - b + c)&sup2; = a&sup2; + b&sup2; + c&sup2;&nbsp;</span><span style=\"font-family: Cambria Math;\">- 2ab&nbsp; - 2bc + 2ca</span></p>",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">14.(</span><span style=\"font-family: Cambria Math;\">b)</span><br><span style=\"font-family: Cambria Math;\">(a - b + c)&sup2; = a&sup2; + b&sup2; + c&sup2;&nbsp;</span><span style=\"font-family: Cambria Math;\">- 2ab&nbsp; - 2bc + 2ca</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "misc",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">15. </span><span style=\"font-family: Cambria Math;\"> If</span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">x</mi><mn>8</mn></mfrac><mo>+</mo><mfrac><mn>8</mn><mi mathvariant=\"normal\">x</mi></mfrac><mo>=</mo><mn>1</mn></math>, </span><span style=\"font-family: Cambria Math;\">then the value of x&sup3; is:</span></p>",
                    question_hi: "<p>15.<span style=\"font-family: Cambria Math;\"> &nbsp;</span><span style=\"font-family: \'Nirmala UI\';\">यदि&nbsp; </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">x</mi><mn>8</mn></mfrac><mo>+</mo><mfrac><mn>8</mn><mi mathvariant=\"normal\">x</mi></mfrac><mo>=</mo><mn>1</mn></math> <span style=\"font-family: Nirmala UI;\">है</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">तो</span><span style=\"font-family: Cambria Math;\"> x&sup3;</span><span style=\"font-family: Cambria Math;\">&nbsp;</span><span style=\"font-family: Nirmala UI;\">का</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">मान</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">ज्ञात</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">कीजिए</span><span style=\"font-family: Nirmala UI;\">।</span></p>",
                    options_en: ["<p>- <span style=\"font-family: Cambria Math;\">256</span></p>", "<p>- <span style=\"font-family: Cambria Math;\">512</span></p>", 
                                "<p>256</p>", "<p>512</p>"],
                    options_hi: ["<p>- <span style=\"font-family: Cambria Math;\">256</span></p>", "<p>- <span style=\"font-family: Cambria Math;\">512</span></p>",
                                "<p>256</p>", "<p>512</p>"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">15.(</span><span style=\"font-family: Cambria Math;\">b)</span><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">x</mi><mn>8</mn></mfrac><mo>+</mo><mfrac><mn>1</mn><mstyle displaystyle=\"true\"><mfrac><mi mathvariant=\"normal\">x</mi><mn>8</mn></mfrac></mstyle></mfrac><mo>=</mo><mn>1</mn></math><br><span style=\"font-family: Cambria Math;\">By applying </span><span style=\"font-family: Cambria Math;\">formula [</span><span style=\"font-family: Cambria Math;\"> a&sup3; + b&sup3; = (</span><span style=\"font-family: Cambria Math;\">a + b</span><span style=\"font-family: Cambria Math;\">){a&sup2; - ab + b&sup2;} ] we get,</span><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mfrac><mi mathvariant=\"normal\">x</mi><mn>8</mn></mfrac><mo>)</mo></mrow><mn>3</mn></msup><mo>+</mo><mfrac><mn>1</mn><msup><mrow><mo>(</mo><mstyle displaystyle=\"true\"><mfrac><mi mathvariant=\"normal\">x</mi><mn>8</mn></mfrac></mstyle><mo>)</mo></mrow><mn>3</mn></msup></mfrac></math> = 1 - 3 =&nbsp; - 2&nbsp; &nbsp;<br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">x</mi><mn>8</mn></mfrac></math>= - 1<strong id=\"docs-internal-guid-0dd24097-7fff-4ea1-5f8e-a002fc9efdf9\"></strong><br>x = - 8<br><span style=\"font-family: Cambria Math;\">So,&nbsp; x&sup3; </span><span style=\"font-family: Cambria Math;\">= (- 8)&sup3;</span><span style=\"font-family: Cambria Math;\">&nbsp;= - 512</span></p>",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">15.(</span><span style=\"font-family: Cambria Math;\">b)</span><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">x</mi><mn>8</mn></mfrac><mo>+</mo><mfrac><mn>1</mn><mstyle displaystyle=\"true\"><mfrac><mi mathvariant=\"normal\">x</mi><mn>8</mn></mfrac></mstyle></mfrac><mo>=</mo><mn>1</mn></math><br><span style=\"font-family: Cambria Math;\">[</span><span style=\"font-family: Cambria Math;\"> a&sup3; + b&sup3; = (</span><span style=\"font-family: Cambria Math;\">a + b</span><span style=\"font-family: Cambria Math;\">){a&sup2; - ab + b&sup2;} ] का उपयोग करने पर</span><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mfrac><mi mathvariant=\"normal\">x</mi><mn>8</mn></mfrac><mo>)</mo></mrow><mn>3</mn></msup><mo>+</mo><mfrac><mn>1</mn><msup><mrow><mo>(</mo><mstyle displaystyle=\"true\"><mfrac><mi mathvariant=\"normal\">x</mi><mn>8</mn></mfrac></mstyle><mo>)</mo></mrow><mn>3</mn></msup></mfrac></math> = 1 - 3 =&nbsp; - 2&nbsp; &nbsp;<br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">x</mi><mn>8</mn></mfrac></math> = - 1<strong id=\"docs-internal-guid-0dd24097-7fff-4ea1-5f8e-a002fc9efdf9\"></strong><br>x = - 8<br><span style=\"font-family: Cambria Math;\">इसलिए,&nbsp; x&sup3; </span><span style=\"font-family: Cambria Math;\">= (- 8)&sup3;</span><span style=\"font-family: Cambria Math;\">&nbsp;= - 512</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>