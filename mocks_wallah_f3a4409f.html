<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">15:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 15</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">15</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 15 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 0,
                end: 13
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 14,
                end: 14
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">1.</span><span style=\"font-family: Cambria Math;\"> The cost price of milk has increased to &#8377;62 from &#8377;60. The milkman sold milk at 10% profit earlier. What is his profit% now if he sells milk at the same rate?</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">1.</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2370;&#2343;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2381;&#2352;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#8377;</span><span style=\"font-family: Cambria Math;\">60 </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2338;&#2364;&#2366;&#2325;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#8377;</span><span style=\"font-family: Cambria Math;\">62 </span><span style=\"font-family: Cambria Math;\">&#2325;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2327;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2370;&#2343;&#2357;&#2366;&#2354;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2361;&#2354;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2370;&#2343;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> 10% </span><span style=\"font-family: Cambria Math;\">&#2354;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2375;&#2330;&#2366;</span><span style=\"font-family: Cambria Math;\">&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2361;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2313;&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2370;&#2343;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2375;&#2330;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2309;&#2348;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2313;&#2360;&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2354;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\"> % </span><span style=\"font-family: Cambria Math;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>5.46%</p>\n", "<p>6.54%</p>\n", 
                                "<p>6.45%</p>\n", "<p>5.64%</p>\n"],
                    options_hi: ["<p>5.46%</p>\n", "<p>6.54%</p>\n",
                                "<p>6.45%</p>\n", "<p>5.64%</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">1.(</span><span style=\"font-family: Cambria Math;\">c)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Earlier Selling </span><span style=\"font-family: Cambria Math;\">price =</span><span style=\"font-family: Cambria Math;\"> 60 &times; 110% = 66 Rs.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">New profit % = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>66</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>62</mn></mrow><mn>62</mn></mfrac></math> </span><span style=\"font-family: Cambria Math;\">&times;100 &asymp;&nbsp;</span><span style=\"font-family: Cambria Math;\"> 6.45% </span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">1.(</span><span style=\"font-family: Cambria Math;\">c)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&#2346;&#2361;&#2354;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2367;&#2325;&#2381;&#2352;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2370;&#2354;&#2381;</span><span style=\"font-family: Cambria Math;\">&#2351;</span><span style=\"font-family: Cambria Math;\"> =</span><span style=\"font-family: Cambria Math;\"> 60&nbsp; &times;110% = 66 </span><span style=\"font-family: Cambria Math;\">&#2352;&#2369;&#2346;&#2351;&#2375;</span><span style=\"font-family: Cambria Math;\">.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&#2344;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2354;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">% =</span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>66</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>62</mn></mrow><mn>62</mn></mfrac></math> </span><span style=\"font-family: Cambria Math;\">&times; 100 &asymp; </span><span style=\"font-family: Cambria Math;\">6.45% </span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">2.</span><span style=\"font-family: Cambria Math;\"> Ram sold two bags at &#8377;1,260 each. On one, he gained 20% and on the other, he lost 20%. Find his gain or loss percentage in the whole transaction.</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">2.</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2352;&#2366;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2376;&#2327;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2340;&#2381;&#2351;&#2375;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#8377;</span><span style=\"font-family: Cambria Math;\">1,260 </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2375;&#2330;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2313;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> 20% </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2354;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2369;&#2310;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2370;&#2360;&#2352;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2313;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> 20% </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2366;&#2344;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2369;&#2312;</span><span style=\"font-family: Cambria Math;\">&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2370;&#2352;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2354;&#2375;&#2344;</span><span style=\"font-family: Cambria Math;\">-</span><span style=\"font-family: Cambria Math;\">&#2342;&#2375;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2313;&#2360;&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2354;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2366;&#2344;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2381;&#2334;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2352;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\">&#2404;</span></p>\n",
                    options_en: ["<p>Gain 4%</p>\n", "<p>Gain 5%</p>\n", 
                                "<p>Loss 4%</p>\n", "<p>Loss 5%</p>\n"],
                    options_hi: ["<p>4% <span style=\"font-family: Cambria Math;\">&#2354;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p>5% <span style=\"font-family: Cambria Math;\">&#2354;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                                "<p>4% <span style=\"font-family: Cambria Math;\">&#2361;&#2366;&#2344;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p>5% <span style=\"font-family: Cambria Math;\">&#2361;&#2366;&#2344;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">2.(</span><span style=\"font-family: Cambria Math;\">c)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">If the S.P. of two articles is the </span><span style=\"font-family: Cambria Math;\">same and</span><span style=\"font-family: Cambria Math;\"> on one article he gained </span><span style=\"font-family: Cambria Math;\">% profit and on other </span><span style=\"font-family: Cambria Math;\">% loss .</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Then ,</span><span style=\"font-family: Cambria Math;\"> There will be always loss .</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Loss% = </span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mi>x</mi><mn>2</mn></msup><mn>100</mn></mfrac><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><msup><mn>20</mn><mn>2</mn></msup><mn>100</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> = 4%</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">2.(</span><span style=\"font-family: Cambria Math;\">c)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2360;&#2381;&#2340;&#2369;&#2323;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2367;&#2325;&#2381;&#2352;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2360;&#2381;&#2340;&#2369;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2313;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">% </span><span style=\"font-family: Cambria Math;\">&#2354;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2370;&#2360;&#2352;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">% </span><span style=\"font-family: Cambria Math;\">&#2361;&#2366;&#2344;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2366;&#2346;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2379;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">&#2404;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&#2347;&#2367;&#2352;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2361;&#2350;&#2375;&#2358;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2344;&#2369;&#2325;&#2360;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2379;&#2327;&#2366;</span><span style=\"font-family: Cambria Math;\"> I</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&#2361;&#2366;&#2344;&#2367;</span><span style=\"font-family: Cambria Math;\"> % = </span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mi>x</mi><mn>2</mn></msup><mn>100</mn></mfrac><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><msup><mn>20</mn><mn>2</mn></msup><mn>100</mn></mfrac></math> </span><span style=\"font-family: Cambria Math;\"> = 4%</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">3. </span><span style=\"font-family: Cambria Math;\">Sonam</span><span style=\"font-family: Cambria Math;\"> purchased a mobile phone for &#8377;27,000 and sold it at a loss of 19%. With that money she purchased a Tablet and sold it at a gain of 20%. What is </span><span style=\"font-family: Cambria Math;\">her overall gain or loss?</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">3. </span><span style=\"font-family: Cambria Math;\">&#2360;&#2379;&#2344;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#8377;</span><span style=\"font-family: Cambria Math;\">27,000 </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2379;&#2348;&#2366;&#2311;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2347;&#2379;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2326;&#2352;&#2368;&#2342;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2313;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> 19% </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2366;&#2344;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2375;&#2330;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\">&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2313;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2376;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2313;&#2360;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2335;&#2376;&#2348;&#2354;&#2375;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2326;&#2352;&#2368;&#2342;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2313;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> 20% </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2354;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2375;&#2330;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\">&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2313;&#2360;&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2369;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2354;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2366;&#2344;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2327;&#2339;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2352;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2404;</span></p>\n",
                    options_en: ["<p>gain of <span style=\"font-family: Cambria Math;\">&#8377;</span><span style=\"font-family: Cambria Math;\">870 </span></p>\n", "<p>loss of <span style=\"font-family: Cambria Math;\">&#8377;</span><span style=\"font-family: Cambria Math;\">756 </span></p>\n", 
                                "<p>loss of <span style=\"font-family: Cambria Math;\">&#8377;</span><span style=\"font-family: Cambria Math;\">856 </span></p>\n", "<p>gain of <span style=\"font-family: Cambria Math;\">&#8377;</span><span style=\"font-family: Cambria Math;\">756</span></p>\n"],
                    options_hi: ["<p><span style=\"font-family: Cambria Math;\">&#8377;</span><span style=\"font-family: Cambria Math;\">870 </span><span style=\"font-family: Cambria Math;\">&#2325;</span><span style=\"font-family: Cambria Math;\">&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2354;&#2366;&#2349;</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">&#8377;</span><span style=\"font-family: Cambria Math;\">756 </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2366;&#2344;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                                "<p><span style=\"font-family: Cambria Math;\">&#8377;</span><span style=\"font-family: Cambria Math;\">856 </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2366;&#2344;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><span style=\"font-family: Cambria Math;\">&#8377;</span><span style=\"font-family: Cambria Math;\">756 </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2354;&#2366;&#2349;</span></p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">3.(</span><span style=\"font-family: Cambria Math;\">b)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Overall Profit/Loss% = - </span><span style=\"font-family: Cambria Math;\">19% + 20% +&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>-</mo><mn>19</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>20</mn></mrow><mn>100</mn></mfrac></math></span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Loss% = - </span><span style=\"font-family: Cambria Math;\">2.8%</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Loss = 27000 &times; 2.8% = 756 Rs.</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">3.(</span><span style=\"font-family: Cambria Math;\">b)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&#2325;&#2369;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2354;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\">/</span><span style=\"font-family: Cambria Math;\">&#2361;&#2366;&#2344;&#2367;</span><span style=\"font-family: Cambria Math;\">% = - </span><span style=\"font-family: Cambria Math;\">19% + 20% +&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>-</mo><mn>19</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>20</mn></mrow><mn>100</mn></mfrac></math></span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&#2361;&#2366;&#2344;&#2367;</span><span style=\"font-family: Cambria Math;\">% = - </span><span style=\"font-family: Cambria Math;\">2.8%</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&#2361;&#2366;&#2344;&#2367;</span><span style=\"font-family: Cambria Math;\"> = 27000 &times; 2.8% = 756 </span><span style=\"font-family: Cambria Math;\">&#2352;&#2369;</span><span style=\"font-family: Cambria Math;\">.</span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">4.</span><span style=\"font-family: Cambria Math;\"> A shopkeeper is incurring a loss of cost price of 4 pens while selling 20 pens to Ajay. If the shopkeeper had purchased 20 pens at 10% less price and had sold 20 pens to Ajay at 25% more than the selling price, then how much percentage would he have gaine</span><span style=\"font-family: Cambria Math;\">d (rounded off to 1 decimal place)?</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">4.</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2309;&#2332;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> 20 </span><span style=\"font-family: Cambria Math;\">&#2346;&#2375;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2375;&#2330;&#2340;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2350;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2369;&#2325;&#2366;&#2344;&#2342;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> 4 </span><span style=\"font-family: Cambria Math;\">&#2346;&#2375;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2381;&#2352;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2366;&#2344;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2352;&#2361;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2369;&#2325;&#2366;&#2344;&#2342;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> 20 </span><span style=\"font-family: Cambria Math;\">&#2346;&#2375;&#2344;</span><span style=\"font-family: Cambria Math;\"> 10% </span><span style=\"font-family: Cambria Math;\">&#2325;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;&#2350;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2326;&#2352;&#2368;&#2342;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2379;&#2340;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> 20 </span><span style=\"font-family: Cambria Math;\">&#2346;&#2375;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2309;&#2332;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2367;&#2325;&#2381;&#2352;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> 25% </span><span style=\"font-family: Cambria Math;\">&#2309;&#2343;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2375;&#2330;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2379;&#2340;&#2375;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2313;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2367;&#2340;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2354;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2379;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">( </span><span style=\"font-family: Cambria Math;\">&#2342;&#2358;&#2350;&#2354;&#2357;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> 1 </span><span style=\"font-family: Cambria Math;\">&#2360;&#2381;&#2341;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2340;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2370;&#2352;&#2381;&#2339;&#2366;&#2306;&#2325;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> ) ?</span></p>\n",
                    options_en: ["<p>8.0</p>\n", "<p>11.1</p>\n", 
                                "<p>10.0</p>\n", "<p>12.5</p>\n"],
                    options_hi: ["<p>8.0</p>\n", "<p>11.1</p>\n",
                                "<p>10.0</p>\n", "<p>12.5</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">4.(</span><span style=\"font-family: Cambria Math;\">b)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Loss% = <span style=\"font-weight: 400;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>20</mn></mfrac></math></span><span style=\"font-weight: 400;\">&nbsp;&times; </span></span><span style=\"font-family: Cambria Math;\">100 =20%</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Let ,</span><span style=\"font-family: Cambria Math;\"> Cost price of 1 pen = 10 Rs.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Then Selling price = 10 &times; 80% = 8 Rs.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">New cost price = 10 &times; 90% = 9 Rs.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">C.P. of 20 pen = 180 Rs.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">New selling price = 8 &times; 125% = 10 Rs.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">S.P. of 20 pen = 200 Rs.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Required profit % = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>200</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>180</mn></mrow><mn>180</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> &times; 100 = 11.11%</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">4.(</span><span style=\"font-family: Cambria Math;\">b)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&#2361;&#2366;&#2344;&#2367;</span><span style=\"font-family: Cambria Math;\">% =</span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>20</mn></mfrac></math> &times; </span><span style=\"font-family: Cambria Math;\">100 =20%</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\">&#2366;</span><span style=\"font-family: Cambria Math;\"> ,</span><span style=\"font-family: Cambria Math;\"> 1 </span><span style=\"font-family: Cambria Math;\">&#2346;&#2375;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2354;&#2366;&#2327;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> = 10 </span><span style=\"font-family: Cambria Math;\">&#2352;&#2369;</span><span style=\"font-family: Cambria Math;\">.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2367;&#2325;&#2381;&#2352;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> = 10 &times; 8</span><span style=\"font-family: Cambria Math;\">0% = 8 </span><span style=\"font-family: Cambria Math;\">&#2352;&#2369;</span><span style=\"font-family: Cambria Math;\">.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&#2344;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2381;&#2352;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> = 10 &times; 90% = 9 </span><span style=\"font-family: Cambria Math;\">&#2352;&#2369;</span><span style=\"font-family: Cambria Math;\">.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">20 </span><span style=\"font-family: Cambria Math;\">&#2346;&#2375;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2381;&#2352;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\">= 180 </span><span style=\"font-family: Cambria Math;\">&#2352;&#2369;</span><span style=\"font-family: Cambria Math;\">.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&#2344;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2367;&#2325;&#2381;&#2352;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> = 8 &times; 125% = 10 </span><span style=\"font-family: Cambria Math;\">&#2352;&#2369;</span><span style=\"font-family: Cambria Math;\">.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">20 </span><span style=\"font-family: Cambria Math;\">&#2346;&#2375;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2367;&#2325;&#2381;&#2352;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2370;&#2354;&#2381;</span><span style=\"font-family: Cambria Math;\">&#2351;</span><span style=\"font-family: Cambria Math;\"> =</span><span style=\"font-family: Cambria Math;\"> 200 </span><span style=\"font-family: Cambria Math;\">&#2352;&#2369;</span><span style=\"font-family: Cambria Math;\">.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&#2310;&#2357;&#2358;&#2381;&#2351;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2354;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\"> % =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>200</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>180</mn></mrow><mn>180</mn></mfrac></math> &times; 100</span><span style=\"font-family: Cambria Math;\"> = 11.11%</span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">5.</span><span style=\"font-family: Cambria Math;\"> A piece of land was sold to a person through two middlemen, each gaining 25%. If the person purchased the land for &#8377;6</span><span style=\"font-family: Cambria Math;\">,25,000</span><span style=\"font-family: Cambria Math;\">, the original cost (in ) of the land was:</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">5.</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2367;&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2381;&#2351;&#2325;&#2381;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2367;&#2330;&#2380;</span><span style=\"font-family: Cambria Math;\">&#2354;&#2367;&#2351;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2366;&#2343;&#2381;&#2351;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2350;&#2368;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2335;&#2369;&#2325;&#2337;&#2364;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2375;&#2330;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2327;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2332;&#2367;&#2360;&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2340;&#2381;&#2351;&#2375;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2367;&#2330;&#2380;&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> 25% </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2354;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2366;&#2346;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2369;&#2310;</span><span style=\"font-family: Cambria Math;\">&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2381;&#2351;&#2325;&#2381;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#8377;</span><span style=\"font-family: Cambria Math;\">6</span><span style=\"font-family: Cambria Math;\">,25,000</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2350;&#2368;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2326;&#2352;&#2368;&#2342;&#2368;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2350;&#2368;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2370;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;&#2350;&#2340;</span><span style=\"font-family: Cambria Math;\"> (</span><span style=\"font-family: Cambria Math;\">&#8377;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\">) </span><span style=\"font-family: Cambria Math;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2341;&#2368;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>4<span style=\"font-family: Cambria Math;\">,25,000</span></p>\n", "<p>4<span style=\"font-family: Cambria Math;\">,00,000</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", 
                                "<p>3<span style=\"font-family: Cambria Math;\">,50,000</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p>3<span style=\"font-family: Cambria Math;\">,75,000</span></p>\n"],
                    options_hi: ["<p>4,25,000</p>\n", "<p>4,00,000</p>\n",
                                "<p>3,50,000</p>\n", "<p>3,75,000</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">5.(</span><span style=\"font-family: Cambria Math;\">b)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Let original cost of land be </span><span style=\"font-family: Cambria Math;\"> Rs</span><span style=\"font-family: Cambria Math;\">. ,</span><span style=\"font-family: Cambria Math;\"> then </span></p>\r\n<p><span style=\"font-weight: 400;\">x</span><span style=\"font-weight: 400;\"> &times; </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>4</mn></mfrac><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mfrac><mn>5</mn><mn>4</mn></mfrac></math><span style=\"font-weight: 400;\"> = 625000 Rs.</span></p>\r\n<p><span style=\"font-weight: 400;\">x</span><span style=\"font-weight: 400;\"> = </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>625000</mn><mo>&times;</mo><mn>16</mn></mrow><mn>25</mn></mfrac></math><span style=\"font-weight: 400;\"> = 400000 Rs.</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">5.(</span><span style=\"font-family: Cambria Math;\">b)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&#2350;&#2366;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2349;&#2370;&#2350;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2370;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2354;&#2366;&#2327;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2352;&#2369;&#2346;&#2351;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">&#2404;</span><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&#2311;&#2360;&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-weight: 400;\">x</span><span style=\"font-weight: 400;\"> &times; </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>4</mn></mfrac><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mfrac><mn>5</mn><mn>4</mn></mfrac></math><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> = 625000 </span><span style=\"font-family: Cambria Math;\">&#2352;&#2369;</span><span style=\"font-family: Cambria Math;\">.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">x</span><span style=\"font-weight: 400;\"> = </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>625000</mn><mo>&times;</mo><mn>16</mn></mrow><mn>25</mn></mfrac></math> </span><span style=\"font-family: Cambria Math;\">= 400000 </span><span style=\"font-family: Cambria Math;\">&#2352;&#2369;</span><span style=\"font-family: Cambria Math;\">..</span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">6.</span><span style=\"font-family: Cambria Math;\"> A shopkeeper bought articles A and B for a total &#8377;2,540. He sold A at a gain of 15% and sold B at a loss of 25%. The selling price of A was &#8377;109 more than that of B. His profit or loss percentage in the entire transaction was (rounded off to one decimal p</span><span style=\"font-family: Cambria Math;\">lace):</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">6.</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2369;&#2325;&#2366;&#2344;&#2342;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2369;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#8377;</span><span style=\"font-family: Cambria Math;\">2,540 </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2360;&#2381;&#2340;&#2369;</span><span style=\"font-family: Cambria Math;\"> A </span><span style=\"font-family: Cambria Math;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> B </span><span style=\"font-family: Cambria Math;\">&#2326;&#2352;&#2368;&#2342;&#2368;</span><span style=\"font-family: Cambria Math;\">&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2313;&#2360;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> A </span><span style=\"font-family: Cambria Math;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> 15% </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2354;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2375;&#2330;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> B </span><span style=\"font-family: Cambria Math;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> 25% </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2366;&#2344;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2375;&#2330;&#2366;</span><span style=\"font-family: Cambria Math;\">&#2404;</span><span style=\"font-family: Cambria Math;\"> A </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2367;&#2325;&#2381;&#2352;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> B </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2340;&#2369;&#2354;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#8377;</span><span style=\"font-family: Cambria Math;\">109 </span><span style=\"font-family: Cambria Math;\">&#2309;&#2343;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2341;&#2366;</span><span style=\"font-family: Cambria Math;\">&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2370;&#2352;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2354;&#2375;&#2344;&#2342;&#2375;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2313;</span><span style=\"font-family: Cambria Math;\">&#2360;&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2354;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2366;&#2344;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span><span style=\"font-family: Cambria Math;\"> (</span><span style=\"font-family: Cambria Math;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2358;&#2350;&#2354;&#2357;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2381;&#2341;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2370;&#2352;&#2381;&#2339;&#2366;&#2306;&#2325;&#2367;</span><span style=\"font-family: Cambria Math;\">&#2340;</span><span style=\"font-family: Cambria Math;\"> )</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2341;&#2366;</span><span style=\"font-family: Cambria Math;\">:</span></p>\n",
                    options_en: ["<p>Loss, 8.3%</p>\n", "<p>Profit, 8.3%</p>\n", 
                                "<p>Profit, 9.5%</p>\n", "<p>Loss, 9.5%</p>\n"],
                    options_hi: ["<p>8.3% <span style=\"font-family: Cambria Math;\">&#2361;&#2366;&#2344;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p>8.3% <span style=\"font-family: Cambria Math;\">&#2354;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                                "<p>9.5% <span style=\"font-family: Cambria Math;\">&#2354;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p>9.5% <span style=\"font-family: Cambria Math;\">&#2361;&#2366;&#2344;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">6.(</span><span style=\"font-family: Cambria Math;\">a)</span></p>\r\n<p><span style=\"font-weight: 400;\">According to the question ,&nbsp;</span></p>\r\n<p><span style=\"font-weight: 400;\">x </span><span style=\"font-weight: 400;\">&times; </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>23</mn><mn>20</mn></mfrac></math><span style=\"font-weight: 400;\"> </span><span style=\"font-weight: 400;\">-</span><span style=\"font-weight: 400;\"> (</span><span style=\"font-weight: 400;\">2540 - x ) </span><span style=\"font-weight: 400;\">&times; </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>4</mn></mfrac></math><span style=\"font-weight: 400;\">= 109</span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>38</mn><mi>x</mi></mrow><mn>20</mn></mfrac></math><span style=\"font-weight: 400;\">= 1905 + 109 = 2014</span></p>\r\n<p><span style=\"font-weight: 400;\">x</span><span style=\"font-weight: 400;\"> = 1060 Rs.&nbsp; &nbsp; and amount = 2540 </span><span style=\"font-weight: 400;\">-</span><span style=\"font-weight: 400;\">1060 = 1480 Rs.</span></p>\r\n<p><span style=\"font-weight: 400;\">Total S.P. = </span><span style=\"font-weight: 400;\">1060 </span><span style=\"font-weight: 400;\">&times; </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>23</mn><mn>20</mn></mfrac></math><span style=\"font-weight: 400;\">+ (</span><span style=\"font-weight: 400;\">1480) </span><span style=\"font-weight: 400;\">&times;</span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>4</mn></mfrac></math><span style=\"font-weight: 400;\">= 1219 + 1110 = 2329 Rs.</span></p>\r\n<p><span style=\"font-weight: 400;\">Required loss%&nbsp; = </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2540</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>2329</mn></mrow><mn>2540</mn></mfrac></math><span style=\"font-weight: 400;\">&times; 100</span><span style=\"font-weight: 400;\"> = 8.3% loss</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">6.(</span><span style=\"font-family: Cambria Math;\">a)</span></p>\r\n<p><span style=\"font-weight: 400;\">&#2346;&#2381;&#2352;&#2358;&#2381;&#2344; &#2325;&#2375; &#2309;&#2344;&#2369;&#2360;&#2366;&#2352;,</span></p>\r\n<p><span style=\"font-weight: 400;\">x </span><span style=\"font-weight: 400;\">&times; </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>23</mn><mn>20</mn></mfrac></math><span style=\"font-weight: 400;\">-</span><span style=\"font-weight: 400;\"> (</span><span style=\"font-weight: 400;\">2540 - x) </span><span style=\"font-weight: 400;\">&times; </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>4</mn></mfrac></math><span style=\"font-weight: 400;\">= 109</span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>38</mn><mi>x</mi></mrow><mn>20</mn></mfrac></math><span style=\"font-weight: 400;\"> = 1905 + 109 = 2014</span></p>\r\n<p><span style=\"font-weight: 400;\">x</span><span style=\"font-weight: 400;\"> = 1060 &#2352;&#2369;. &#2324;&#2352; &#2352;&#2366;&#2358;&#2367; = 2540 </span><span style=\"font-weight: 400;\">-</span><span style=\"font-weight: 400;\">1060 = 1480 &#2352;&#2369;.</span></p>\r\n<p><span style=\"font-weight: 400;\">&#2325;&#2369;&#2354; &#2357;&#2367;&#2325;&#2381;&#2352;&#2351; &#2350;&#2370;&#2354;&#2381;&#2351; = </span><span style=\"font-weight: 400;\">1060 </span><span style=\"font-weight: 400;\">&times; </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>23</mn><mn>20</mn></mfrac></math><span style=\"font-weight: 400;\">+ (</span><span style=\"font-weight: 400;\">1480) </span><span style=\"font-weight: 400;\">&times; </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>4</mn></mfrac></math><span style=\"font-weight: 400;\">&nbsp; = 1219 + 1110 = 2329 &#2352;&#2369;.</span></p>\r\n<p><span style=\"font-weight: 400;\">&#2310;&#2357;&#2358;&#2381;&#2351;&#2325; &#2361;&#2366;&#2344;&#2367;%&nbsp; = </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2540</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>2329</mn></mrow><mn>2540</mn></mfrac></math><span style=\"font-weight: 400;\">&times; 100 = 8.3% &#2361;&#2366;&#2344;&#2367;</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">7. </span><span style=\"font-family: Cambria Math;\">The cost price of 10 goods is equal to the selling price of 8 goods. Find the profit percentage.</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">7. </span><span style=\"font-family: Cambria Math;\">10 </span><span style=\"font-family: Cambria Math;\">&#2357;&#2360;&#2381;&#2340;&#2369;&#2323;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2381;&#2352;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> 8 </span><span style=\"font-family: Cambria Math;\">&#2357;&#2360;&#2381;&#2340;&#2369;&#2323;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2367;&#2325;&#2381;&#2352;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2352;&#2366;&#2348;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2354;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2381;&#2334;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;&#2332;&#2367;&#2319;</span></p>\n",
                    options_en: ["<p>25%</p>\n", "<p>15.2%</p>\n", 
                                "<p>11.11%</p>\n", "<p>13.3%</p>\n"],
                    options_hi: ["<p>25%</p>\n", "<p>15.2%</p>\n",
                                "<p>11.11%</p>\n", "<p>13.3%</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">7.(</span><span style=\"font-family: Cambria Math;\">a)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">10 &times; C.P. = 8 &times; S.P.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>C</mi><mo>.</mo><mi>P</mi><mo>.</mo></mrow><mrow><mi>S</mi><mo>.</mo><mi>P</mi><mo>.</mo></mrow></mfrac><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><mn>8</mn><mn>10</mn></mfrac></math> </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Required Profit % = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>10</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>8</mn></mrow><mn>8</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> &times; 100 = 25%</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">7.(</span><span style=\"font-family: Cambria Math;\">a)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">10 &times; </span><span style=\"font-family: Cambria Math;\">&#2325;&#2381;&#2352;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> = 8 &times; </span><span style=\"font-family: Cambria Math;\">&#2357;&#2367;&#2325;&#2381;&#2352;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\">.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> <math style=\"font-family: Verdana;\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2325;&#2381;&#2352;&#2351;</mi><mo>&nbsp;</mo><mi>&#2350;&#2370;&#2354;&#2381;&#2351;</mi></mrow><mrow><mi>&#2357;&#2367;&#2325;&#2381;&#2352;&#2351;</mi><mo>&nbsp;</mo><mi>&#2350;&#2370;&#2354;&#2381;&#2351;</mi></mrow></mfrac><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><mn>8</mn><mn>10</mn></mfrac></math> </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&#2310;&#2357;&#2358;&#2381;&#2351;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2354;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\"> % = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>10</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>8</mn></mrow><mn>8</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> &times; 100 = 25%</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">8.</span><span style=\"font-family: Cambria Math;\"> S sold his phone for &#8377;15,500, which he had bought for &#8377;12,000. What was his gain percentage profit?</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">8.</span><span style=\"font-family: Cambria Math;\"> S </span><span style=\"font-family: Cambria Math;\">&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2309;&#2346;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2347;&#2379;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#8377;</span><span style=\"font-family: Cambria Math;\">15,500 </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2375;&#2330;&#2366;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2332;&#2367;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2313;&#2360;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#8377;</span><span style=\"font-family: Cambria Math;\">12,000 </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2326;&#2352;&#2368;&#2342;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2341;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2340;&#2366;&#2311;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2313;&#2360;&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2354;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2341;&#2366;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>26.67%</p>\n", "<p>29.17%</p>\n", 
                                "<p>32.67%</p>\n", "<p>22.67%</p>\n"],
                    options_hi: ["<p>26.67%</p>\n", "<p>29.17%</p>\n",
                                "<p>32.67%</p>\n", "<p>22.67%</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">8.(</span><span style=\"font-family: Cambria Math;\">b)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Required profit </span><span style=\"font-family: Cambria Math;\">% <span style=\"font-weight: 400;\">= </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>15500</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>12000</mn></mrow><mn>12000</mn></mfrac></math><span style=\"font-weight: 400;\">&times; 100</span><span style=\"font-weight: 400;\"> </span></span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">= </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&nbsp;</mo><mn>350</mn></mrow><mrow><mn>12</mn><mo>&nbsp;</mo></mrow></mfrac></math></span><span style=\"font-family: Cambria Math;\">= 29.17%</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">8.(</span><span style=\"font-family: Cambria Math;\">b)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&#2310;&#2357;&#2358;&#2381;&#2351;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2354;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">% =</span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>15500</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>12000</mn></mrow><mn>12000</mn></mfrac></math><span style=\"font-weight: 400;\"> </span><span style=\"font-weight: 400;\">&times;100</span><span style=\"font-weight: 400;\"> </span></span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">= </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&nbsp;</mo><mn>350</mn></mrow><mrow><mn>12</mn><mo>&nbsp;</mo></mrow></mfrac></math><span style=\"font-weight: 400;\">= 29.17%</span></span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "17",
                    question_en: "<p>9. <span style=\"font-family: Cambria Math;\">Mohan purchases a table at an MRP of &#8377;800, at a discount of 10%. If he sells it at &#8377;900, then the profit percentage is:</span></p>\n",
                    question_hi: "<p>9. <span style=\"font-family: Cambria Math;\">&#2350;&#2379;&#2361;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2332;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#8377;</span><span style=\"font-family: Cambria Math;\">800 </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2309;&#2306;&#2325;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2370;&#2354;&#2381;</span><span style=\"font-family: Cambria Math;\">&#2351;</span><span style=\"font-family: Cambria Math;\"> (</span><span style=\"font-family: Cambria Math;\">MRP) </span><span style=\"font-family: Cambria Math;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> 10% </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2331;&#2370;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2326;&#2352;&#2368;&#2342;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2361;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2311;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#8377;</span><span style=\"font-family: Cambria Math;\">900 </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2375;&#2330;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2354;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span><span style=\"font-family: Cambria Math;\"> _____________ </span><span style=\"font-family: Cambria Math;\">&#2361;&#2379;&#2327;&#2366;</span><span style=\"font-family: Cambria Math;\">&#2404;</span></p>\n",
                    options_en: ["<p>20% profit</p>\n", "<p>25% profit</p>\n", 
                                "<p>15% profit</p>\n", "<p>30% profit</p>\n"],
                    options_hi: ["<p>20% <span style=\"font-family: Cambria Math;\">&#2354;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p>25% <span style=\"font-family: Cambria Math;\">&#2354;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                                "<p>15% <span style=\"font-family: Cambria Math;\">&#2354;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p>30% <span style=\"font-family: Cambria Math;\">&#2354;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">9.(</span><span style=\"font-family: Cambria Math;\">b)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">According to question,</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Cost price of table = 800 &times;</span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>90</mn><mn>100</mn></mfrac></math>= Rs. 720</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Selling price of table = Rs. 900</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">So, Profit % = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>900</mn><mo>-</mo><mn>720</mn></mrow><mn>720</mn></mfrac></math> &times; 100&nbsp;</span><span style=\"font-family: Cambria Math;\"> = 25%</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">9.(</span><span style=\"font-family: Cambria Math;\">b)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2358;&#2381;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2309;&#2344;&#2369;&#2360;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\">,</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&#2335;&#2375;&#2348;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2381;&#2352;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> = 800 &times;</span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>90</mn><mn>100</mn></mfrac></math>= </span><span style=\"font-family: Cambria Math;\">&#2352;&#2369;</span><span style=\"font-family: Cambria Math;\">. 720</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&#2335;&#2375;&#2348;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2367;&#2325;&#2381;&#2352;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> = </span><span style=\"font-family: Cambria Math;\">&#2352;&#2369;</span><span style=\"font-family: Cambria Math;\">. 900</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2354;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\"> % = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>900</mn><mo>-</mo><mn>720</mn></mrow><mn>720</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> &times; 100= 25%</span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "17",
                    question_en: "<p>10<span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\">A sells an article to B at a gain of 16%, B sells it to C at a loss of 15%, and C sells it to D at a gain of 20%. If the difference between the profits earned by C and A is &#8377;248, then the loss (in &#8377;) incurred by B is:</span></p>\n",
                    question_hi: "<p>10<span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\">A </span><span style=\"font-family: Cambria Math;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2360;&#2381;&#2340;&#2369;</span><span style=\"font-family: Cambria Math;\">, B </span><span style=\"font-family: Cambria Math;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> 16% </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2354;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2375;&#2330;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, B </span><span style=\"font-family: Cambria Math;\">&#2311;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> C </span><span style=\"font-family: Cambria Math;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> 15% </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2366;&#2344;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2375;&#2330;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> C </span><span style=\"font-family: Cambria Math;\">&#2311;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> D </span><span style=\"font-family: Cambria Math;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> 20% </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2354;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2375;&#2330;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> C </span><span style=\"font-family: Cambria Math;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> A </span><span style=\"font-family: Cambria Math;\">&#2342;&#2381;&#2357;&#2366;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2309;&#2352;&#2381;&#2332;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2354;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2368;&#2330;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2309;&#2306;&#2340;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#8377;</span><span style=\"font-family: Cambria Math;\">248 </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> B </span><span style=\"font-family: Cambria Math;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2369;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2366;&#2344;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2327;&#2339;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2352;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> (</span><span style=\"font-family: Cambria Math;\">&#8377;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\">) </span><span style=\"font-family: Cambria Math;\">&#2404;</span></p>\n",
                    options_en: ["<p>1250</p>\n", "<p>1075</p>\n", 
                                "<p>1160</p>\n", "<p>1<span style=\"font-family: Cambria Math;\">025</span></p>\n"],
                    options_hi: ["<p>1250</p>\n", "<p>1075</p>\n",
                                "<p>1160</p>\n", "<p>1025</p>\n"],
                    solution_en: "<p>10<span style=\"font-family: Cambria Math;\">.(c)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Let the cost price of article for A be 100 unit</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">According to question,</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">A sell the article to B = 116 unit</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">B sell the article to C = 116 &times;</span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>85</mn><mn>100</mn></mfrac></math>= 98.6 unit</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">C sell the article to D = 98.6 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&nbsp;</mo><mn>120</mn></mrow><mn>100</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> = 118.32 unit</span></p>\r\n<p><span style=\"font-weight: 400;\">(19.72 </span><span style=\"font-weight: 400;\">-</span><span style=\"font-weight: 400;\"> 16) </span>uni<span style=\"font-family: Cambria Math;\">t = Rs. 248</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Loss incurred by B (17.4) unit =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>248</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>17</mn><mo>.</mo><mn>4</mn></mrow><mrow><mn>3</mn><mo>.</mo><mn>72</mn></mrow></mfrac></math> </span><span style=\"font-family: Cambria Math;\"> = Rs. 1160</span></p>\n",
                    solution_hi: "<p>10<span style=\"font-family: Cambria Math;\">.(c)</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> A </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2360;&#2381;&#2340;&#2369;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2352;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> 100 </span><span style=\"font-family: Nirmala UI;\">&#2311;&#2325;&#2366;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2358;&#2381;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2344;&#2369;&#2360;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\">,</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">A </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2360;&#2381;&#2340;&#2369;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> B = 116 </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2370;&#2344;&#2367;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2375;&#2330;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">B </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2360;&#2381;&#2340;&#2369;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> C = 116 &times;</span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>85</mn><mn>100</mn></mfrac></math>= 98.6 </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2370;&#2344;&#2367;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2375;&#2330;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">C </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> D </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2360;&#2381;&#2340;&#2369;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2375;&#2330;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">= 98.6 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&nbsp;</mo><mn>120</mn></mrow><mn>100</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> = 118.32 </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2370;&#2344;&#2367;&#2335;</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\"><span style=\"font-weight: 400;\">(19.72 </span><span style=\"font-weight: 400;\">-</span><span style=\"font-weight: 400;\"> 16) </span>&#2351;&#2370;&#2344;&#2367;&#2335;</span><span style=\"font-family: Cambria Math;\"> = </span><span style=\"font-family: Nirmala UI;\">&#2352;&#2369;</span><span style=\"font-family: Cambria Math;\">. 248</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">B </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2381;&#2357;&#2366;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2369;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2366;&#2344;&#2367;</span><span style=\"font-family: Cambria Math;\"> (17.4) </span><span style=\"font-family: Nirmala UI;\">&#2311;&#2325;&#2366;&#2312;</span><span style=\"font-family: Cambria Math;\"> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>248</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>17</mn><mo>.</mo><mn>4</mn></mrow><mrow><mn>3</mn><mo>.</mo><mn>72</mn></mrow></mfrac></math></span><span style=\"font-family: Cambria Math;\"> = </span><span style=\"font-family: Nirmala UI;\">&#2352;&#2369;</span><span style=\"font-family: Cambria Math;\">. 1160</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "17",
                    question_en: "<p>11<span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\">A dealer sells rice at a profit of 15% and uses a weight that is 15% less than the actual weight. Find his percentage gain.</span></p>\n",
                    question_hi: "<p>11<span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2337;&#2368;&#2354;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2330;&#2366;&#2357;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> 15% </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2375;&#2330;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2366;&#2360;&#2381;&#2340;&#2357;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2332;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> 15% </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2332;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2346;&#2351;&#2379;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2360;&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2381;&#2334;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;&#2332;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2404;</span></p>\n",
                    options_en: ["<p>26%</p>\n", "<p>53.3%</p>\n", 
                                "<p>35.3%</p>\n", "<p>37.5%</p>\n"],
                    options_hi: ["<p>26%</p>\n", "<p>53.3%</p>\n",
                                "<p>35.3%</p>\n", "<p>37.5%</p>\n"],
                    solution_en: "<p>11<span style=\"font-family: Cambria Math;\">.(c)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Ratio&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &rarr;</span><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp;Cost </span><span style=\"font-family: Cambria Math;\">price :</span><span style=\"font-family: Cambria Math;\"> Selling</span><span style=\"font-family: Cambria Math;\"> price</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Profit % &nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&rarr;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;</span><span style=\"font-family: Cambria Math;\">20&nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; 23</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Faulty weight&nbsp; &nbsp;&rarr;&nbsp;&nbsp;</span><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; </span><span style=\"font-family: Cambria Math;\">17&nbsp; &nbsp;:</span><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp; &nbsp;20</span></p>\r\n<p><span style=\"text-decoration: underline;\">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; Overall = 17&nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; 23</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Overall profit </span><span style=\"font-family: Cambria Math;\">% =</span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>23</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>17</mn></mrow><mn>17</mn></mfrac></math> <span style=\"font-weight: 400;\">&times; 100 &asymp;</span></span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> 35.3 % </span></p>\n",
                    solution_hi: "<p>11<span style=\"font-family: Cambria Math;\">.(c)</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2309;&#2344;&#2369;&#2346;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&rarr;&nbsp; &nbsp; </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2366;&#2327;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2370;&#2354;&#2381;</span><span style=\"font-family: Nirmala UI;\">&#2351;</span><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp;:</span><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp; </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2325;&#2381;&#2352;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2354;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\"> %&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &rarr;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; </span><span style=\"font-family: Cambria Math;\">20&nbsp; &nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp;</span><span style=\"font-family: Cambria Math;\">23</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2342;&#2379;&#2359;&#2346;&#2370;&#2352;&#2381;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2332;&#2344;</span><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp; &rarr;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;</span><span style=\"font-family: Cambria Math;\">17&nbsp; &nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp; &nbsp;20</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><span style=\"text-decoration: underline;\">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp;</span></span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &#2325;&#2369;&#2354;</span><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp; =&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;17&nbsp; &nbsp; :&nbsp; &nbsp; &nbsp;23</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2325;&#2369;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">% =</span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>23</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>17</mn></mrow><mn>17</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> &times; 100 &asymp;</span><span style=\"font-family: Cambria Math;\"> 35.3 % </span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "17",
                    question_en: "<p>12<span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\">Muskan</span><span style=\"font-family: Cambria Math;\"> buys two kg of pulses for &#8377;400 and sells it for &#8377;440. She also uses the weight of 850 gm instead of 1 kg. What is </span><span style=\"font-family: Cambria Math;\">Muskan\'s</span><span style=\"font-family: Cambria Math;\"> actual profit percentage?</span></p>\n",
                    question_hi: "<p>12<span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2369;&#2360;&#2381;&#2325;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> &#8377;400 </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2379;</span><span style=\"font-family: Cambria Math;\"> kg </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2366;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2326;&#2352;&#2368;&#2342;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2311;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> &#8377;440 </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2375;&#2330;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Nirmala UI;\">&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2361;</span><span style=\"font-family: Cambria Math;\"> 1 kg </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2327;&#2361;</span><span style=\"font-family: Cambria Math;\"> 850 gm </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2366;&#2354;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2332;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2349;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2346;&#2351;&#2379;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2306;</span><span style=\"font-family: Nirmala UI;\">&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2369;&#2360;&#2381;&#2325;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2366;&#2360;&#2381;&#2340;&#2357;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2340;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>30%</p>\n", "<p>27%</p>\n", 
                                "<p><span style=\"font-weight: 400;\">25</span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>15</mn><mn>17</mn></mfrac></math><span style=\"font-weight: 400;\">%</span></p>\n", "<p><span style=\"font-weight: 400;\">29</span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>17</mn></mfrac></math><span style=\"font-weight: 400;\">%</span></p>\n"],
                    options_hi: ["<p>30%</p>\n", "<p>27%</p>\n",
                                "<p>25<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>15</mn><mn>17</mn></mfrac></math>%</p>\n", "<p><span style=\"font-weight: 400;\">29</span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>17</mn></mfrac></math><span style=\"font-weight: 400;\">%</span></p>\n"],
                    solution_en: "<p>12<span style=\"font-family: Cambria Math;\">.(d)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; C.P.</span><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp; &nbsp; </span><span style=\"font-family: Cambria Math;\">:&nbsp; &nbsp; &nbsp; S.P. </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Price&nbsp; &nbsp; &nbsp; &rarr;&nbsp; &nbsp; </span><span style=\"font-family: Cambria Math;\">400&nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp;440</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Weight &nbsp; &rarr;&nbsp; &nbsp;</span><span style=\"font-family: Cambria Math;\">850&nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; 1000</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">____________________________________</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Final ratio &nbsp;&rarr; </span><span style=\"font-family: Cambria Math;\">17&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp; 22 </span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Required profit % = <span style=\"font-weight: 400;\">&nbsp;</span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&nbsp;</mo><mn>22</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>17</mn><mo>&nbsp;</mo></mrow><mn>17</mn></mfrac></math><span style=\"font-weight: 400;\">&times; 100</span><span style=\"font-weight: 400;\"> = 29</span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>17</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\">%</span></p>\n",
                    solution_hi: "<p>12<span style=\"font-family: Cambria Math;\">.(d)</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&#2325;&#2381;&#2352;&#2351; &#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp; :&nbsp; &nbsp;</span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2325;&#2381;&#2352;&#2351; &#2350;&#2370;&#2354;&#2381;&#2351;</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&nbsp; &nbsp; &nbsp; &nbsp; &#2325;&#2368;&#2350;&#2340;</span><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp; &nbsp; &nbsp;&rarr;&nbsp;</span><span style=\"font-family: Cambria Math;\">&nbsp; 400&nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; 440</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&#2357;&#2332;&#2364;&#2344;</span><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&rarr;&nbsp; &nbsp;</span><span style=\"font-family: Cambria Math;\">850&nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; 1000</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&nbsp;<span style=\"text-decoration: underline;\">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp;</span></span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2309;&#2306;&#2340;&#2367;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2344;&#2369;&#2346;&#2366;&#2340; <span style=\"font-family: Cambria Math;\">&rarr;&nbsp;&nbsp;</span></span><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp; &nbsp;</span><span style=\"font-family: Cambria Math;\">17&nbsp; &nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp; &nbsp; 22 </span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2310;&#2357;&#2358;&#2381;&#2351;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">% = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&nbsp;</mo><mn>22</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>17</mn><mo>&nbsp;</mo></mrow><mn>17</mn></mfrac></math><span style=\"font-weight: 400;\">&times; 100</span><span style=\"font-weight: 400;\"> = 29</span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>17</mn></mfrac></math>%</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">13</span><span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\">A grocer sells pulses at a profit of 13% and uses weights which are 24% less than the market weight. The percentage of profit (correct to 2 decimal places) earned by him will be:</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">13.</span><span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2306;&#2360;&#2366;&#2352;</span><span style=\"font-family: Nirmala UI;\">&#2368;</span><span style=\"font-family: Cambria Math;\"> 13% </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2366;&#2354;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2375;&#2330;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2320;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2366;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2346;&#2351;&#2379;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2366;&#2332;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2366;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> 24% </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2306;</span><span style=\"font-family: Nirmala UI;\">&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2360;&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2381;&#2357;&#2366;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2352;&#2381;&#2332;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span><span style=\"font-family: Cambria Math;\"> (2 </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2358;&#2350;&#2354;&#2357;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2381;&#2341;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2361;&#2368;</span><span style=\"font-family: Cambria Math;\">)____________</span><span style=\"font-family: Nirmala UI;\">&#2361;&#2379;&#2327;&#2366;</span><span style=\"font-family: Nirmala UI;\">&#2404;</span></p>\n",
                    options_en: ["<p>45.56%</p>\n", "<p>37.75%</p>\n", 
                                "<p>48.68%</p>\n", "<p>42.35%</p>\n"],
                    options_hi: ["<p>45.56%</p>\n", "<p>37.75%</p>\n",
                                "<p>48.68%</p>\n", "<p>42.35%</p>\n"],
                    solution_en: "<p>13<span style=\"font-family: Cambria Math;\">.(c)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Ratio &nbsp; &nbsp;&rarr;&nbsp; &nbsp; &nbsp;</span><span style=\"font-family: Cambria Math;\">Cost </span><span style=\"font-family: Cambria Math;\">price&nbsp; &nbsp; :</span><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp; Selling price</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Profit &nbsp; &rarr;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; </span><span style=\"font-family: Cambria Math;\">100&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp;113</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Faulty weight &rarr;&nbsp; &nbsp;&nbsp;</span><span style=\"font-family: Cambria Math;\"> 76&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; 100</span></p>\r\n<p><span style=\"text-decoration: underline;\">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Overall&nbsp; &nbsp;=</span><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;76&nbsp; &nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp;113</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Required profit </span><span style=\"font-family: Cambria Math;\">% =</span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>113</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>76</mn></mrow><mn>76</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> &times; 100 = 48.68%</span></p>\n",
                    solution_hi: "<p>13<span style=\"font-family: Cambria Math;\">.(c)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">&#2309;&#2344;&#2369;&#2346;&#2366;&#2340; </span>&nbsp; &nbsp; &rarr; &nbsp; </span><span style=\"font-weight: 400;\">&#2354;&#2366;&#2327;&#2340; &#2350;&#2370;&#2354;&#2381;&#2351;&nbsp; :&nbsp; &nbsp;&#2357;&#2367;&#2325;&#2381;&#2352;&#2351; &#2350;&#2370;&#2354;&#2381;&#2351;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">&#2354;&#2366;&#2349;</span> &nbsp; &rarr;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;</span><span style=\"font-family: Cambria Math;\">100&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp;113</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">&#2342;&#2379;&#2359;&#2346;&#2370;&#2352;&#2381;&#2339; &#2357;&#2332;&#2344; </span>&rarr;&nbsp; &nbsp;&nbsp;</span><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp; 76&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; 100</span></p>\r\n<p><span style=\"text-decoration: underline;\">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">&#2325;&#2369;&#2354;</span> &nbsp; =</span><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;76&nbsp; &nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp;113</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">&#2310;&#2357;&#2358;&#2381;&#2351;&#2325; &#2354;&#2366;&#2349;</span> </span><span style=\"font-family: Cambria Math;\">% =</span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>113</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>76</mn></mrow><mn>76</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> &times; 100 = 48.68%</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">14</span><span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\">If the cost price is 66% of the selling price, then what is the profit percentage (rounded off t</span><span style=\"font-family: Cambria Math;\">o 2 decimal places)</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">14</span><span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2352;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2325;&#2381;&#2352;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> 66% </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> (2 </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2358;&#2350;&#2354;&#2357;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2381;&#2341;&#2366;&#2344;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2370;&#2352;&#2381;&#2339;&#2366;&#2306;&#2325;&#2367;</span><span style=\"font-family: Nirmala UI;\">&#2340;</span><span style=\"font-family: Cambria Math;\"> )</span><span style=\"font-family: Cambria Math;\"> ?</span></p>\n",
                    options_en: ["<p>51.52%</p>\n", "<p>50.52%</p>\n", 
                                "<p>60.25%</p>\n", "<p>55.62%</p>\n"],
                    options_hi: ["<p>51.52%</p>\n", "<p>50.52%</p>\n",
                                "<p>60.25%</p>\n", "<p>55.62%</p>\n"],
                    solution_en: "<p>14<span style=\"font-family: Cambria Math;\">.(a)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Let the </span><span style=\"font-family: Cambria Math;\">selling price be 100 Rs. Then Cost = 66 Rs.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Profit </span><span style=\"font-family: Cambria Math;\">% =</span><span style=\"font-family: Cambria Math;\">&nbsp;</span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>100</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>66</mn></mrow><mn>66</mn></mfrac></math><span style=\"font-weight: 400;\"> </span><span style=\"font-weight: 400;\">&times;100</span><span style=\"font-weight: 400;\">&nbsp;</span></p>\r\n<p><span style=\"font-weight: 400;\">= </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3400</mn><mn>66</mn></mfrac></math><span style=\"font-weight: 400;\">= 51.52%</span></p>\n",
                    solution_hi: "<p>14<span style=\"font-family: Cambria Math;\">.(a)</span></p>\r\n<p><span style=\"font-weight: 400;\">&#2350;&#2366;&#2344;&#2366; &#2357;&#2367;&#2325;&#2381;&#2352;&#2351; &#2350;&#2370;&#2354;&#2381;&#2351; 100 &#2352;&#2369;&#2346;&#2351;&#2375; &#2361;&#2376;, &#2340;&#2379; &#2354;&#2366;&#2327;&#2340; = 66 &#2352;&#2369;&#2346;&#2351;&#2375;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">&#2354;&#2366;&#2349;&nbsp; </span>% =</span><span style=\"font-family: Cambria Math;\">&nbsp;</span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>100</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>66</mn></mrow><mn>66</mn></mfrac></math><span style=\"font-weight: 400;\"> </span><span style=\"font-weight: 400;\">&times;100</span><span style=\"font-weight: 400;\">&nbsp;</span></p>\r\n<p><span style=\"font-weight: 400;\">= </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3400</mn><mn>66</mn></mfrac></math><span style=\"font-weight: 400;\">= 51.52%</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "misc",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">15</span><span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\">If the cost price is 94% of the selling price, then find the profit percentage.</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">15</span><span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2352;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2325;&#2381;&#2352;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> 94% </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2381;&#2334;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;&#2332;&#2367;&#2319;</span><span style=\"font-family: Nirmala UI;\">&#2404;</span></p>\n",
                    options_en: ["<p>6.5%</p>\n", "<p>4.6%</p>\n", 
                                "<p>6.4%</p>\n", "<p>5.6%</p>\n"],
                    options_hi: ["<p>6.5%</p>\n", "<p>4.6%</p>\n",
                                "<p>6.4%</p>\n", "<p>5.6%</p>\n"],
                    solution_en: "<p>15<span style=\"font-family: Cambria Math;\">.</span><span style=\"font-family: Cambria Math;\">(c)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Let the selling price be 100 Rs. Then Cost will be 94 Rs.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Profit </span><span style=\"font-family: Cambria Math;\">% <span style=\"font-weight: 400;\">= </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>100</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>94</mn></mrow><mn>94</mn></mfrac></math><span style=\"font-weight: 400;\"> </span><span style=\"font-weight: 400;\">&times; 100</span><span style=\"font-weight: 400;\"> </span></span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">= </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>600</mn><mn>94</mn></mfrac></math><span style=\"font-weight: 400;\"> &asymp;</span><span style=\"font-weight: 400;\"> 6.4%</span></span></p>\n",
                    solution_hi: "<p>15<span style=\"font-family: Cambria Math;\">.</span><span style=\"font-family: Cambria Math;\">(c)</span></p>\r\n<p><span style=\"font-weight: 400;\">&#2350;&#2366;&#2344;&#2366; &#2357;&#2367;&#2325;&#2381;&#2352;&#2351; &#2350;&#2370;&#2354;&#2381;&#2351; 100 &#2352;&#2369;&#2346;&#2351;&#2375; &#2361;&#2376; &#2340;&#2379; &#2354;&#2366;&#2327;&#2340; 94 &#2352;&#2369;&#2346;&#2351;&#2375; &#2361;&#2379;&#2327;&#2368;&#2404;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">&#2354;&#2366;&#2349; </span>% <span style=\"font-weight: 400;\">= </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>100</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>94</mn></mrow><mn>94</mn></mfrac></math><span style=\"font-weight: 400;\"> </span><span style=\"font-weight: 400;\">&times; 100</span><span style=\"font-weight: 400;\"> </span></span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">= </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>600</mn><mn>94</mn></mfrac></math><span style=\"font-weight: 400;\"> &asymp;</span><span style=\"font-weight: 400;\"> 6.4%</span></span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>