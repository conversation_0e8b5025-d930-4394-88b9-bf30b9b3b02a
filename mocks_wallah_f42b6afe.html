<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">5:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 15</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">15</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 5 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["18"] = {
                name: "General Awareness",
                start: 0,
                end: 13
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="18">General Awareness</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 14,
                end: 14
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "18",
                    question_en: "<p><span style=\"font-family: helvetica, arial, sans-serif;\">1. With reference to Sonepur Cattle fair, consider the following statements and select the correct option.</span><br><span style=\"font-family: helvetica, arial, sans-serif;\">a) It is associated with the Hariharnath temple. </span><br><span style=\"font-family: helvetica, arial, sans-serif;\">b) It begins on Purnima (full moon night) and Kartik month.</span><br><span style=\"font-family: helvetica, arial, sans-serif;\">c) It is celebrated in the Madhya Pradesh state.</span></p>",
                    question_hi: "<p><span style=\"font-family: helvetica, arial, sans-serif;\">1. सोनपुर पशु मेले के संदर्भ में निम्नलिखित कथनों पर विचार कीजिए और सही विकल्प को चुनिए।</span><br><span style=\"font-family: helvetica, arial, sans-serif;\">a) इसका संबंध हरिहरनाथ मंदिर से है।</span><br><span style=\"font-family: helvetica, arial, sans-serif;\">b) यह पूर्णिमा (पूर्णिमा की रात) और कार्तिक मास से शुरू होता है।</span><br><span style=\"font-family: helvetica, arial, sans-serif;\">c) यह मध्य प्रदेश राज्य में मनाया जाता है।</span></p>",
                    options_en: ["<p>Both a and b</p>", "<p>Both a and c</p>", 
                                "<p>Both b and c</p>", "<p>a, b, c</p>"],
                    options_hi: ["<p>a और b दोनों</p>", "<p>a और c दोनों</p>",
                                "<p>b और c दोनों</p>", "<p>a, b, c</p>"],
                    solution_en: "<p><span style=\"font-family: helvetica, arial, sans-serif;\">1.(a) <strong>Both a and b</strong>. Sonepur Cattle Fair is one of Asia\'s largest cattle fairs, organized at the confluence of two rivers, the Ganga and Gandak in Bihar. Nagaji Fair is celebrated in the tribal area of Madhya Pradesh. <strong>Other Important fairs </strong>- Pushkar (Rajasthan), Hemis Gompa (Ladakh), Surajkund (Haryana).</span></p>",
                    solution_hi: "<p><span style=\"font-family: helvetica, arial, sans-serif;\">1.(a)<strong> a और b दोनों।</strong> सोनपुर पशु मेला एशिया के सबसे बड़े पशु मेलों में से एक है, जो बिहार में दो नदियों, गंगा और गंडक के संगम पर आयोजित किया जाता है। नागाजी मेला मध्य प्रदेश के आदिवासी क्षेत्र में मनाया जाता है। <strong>अन्य महत्वपूर्ण मेले - </strong>पुष्कर (राजस्थान), हेमिस गोम्पा (लद्दाख), सूरजकुंड (हरियाणा)।</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "18",
                    question_en: "<p>2. In 2006, IIeana Citaristi, an Italian Odissi dancer, was awarded with the ______.</p>",
                    question_hi: "<p>2. 2006 में, इटैलियन ओडिसी नृत्यांगना इलियाना सिटारिस्टी (Ileana Citaristi) को ______ से सम्मानित किया गया था।</p>",
                    options_en: ["<p>Padma Bhushan</p>", "<p>Bharat Ratna</p>", 
                                "<p>Padma Shri</p>", "<p>Padma Vibhushan</p>"],
                    options_hi: ["<p>पद्म भूषण</p>", "<p>भारत रत्न</p>",
                                "<p>पद्म श्री</p>", "<p>पद्म विभूषण</p>"],
                    solution_en: "<p>2.(c) <strong>Padma Shri.</strong> Ileana Citaristi won the Best Choreography prize at the 43rd National Film Awards in 1995 for Yugant. <strong>Other Odissi Dancer </strong>- Kumkum Mohanty, Sonal Mansingh (Bharatanatyam as well), Kiran Segal, Pankaj Charan Das (Father of Odissi Dance), Mayadhar Raut, Gangadhar Pradhan.</p>",
                    solution_hi: "<p>2.(c) <strong>पद्म श्री</strong>। इलियाना सिटारिस्टी ने 1995 में युगांत के लिए 43वें राष्ट्रीय फिल्म पुरस्कार में सर्वश्रेष्ठ कोरियोग्राफी का पुरस्कार जीता। <strong>अन्य ओडिसी नर्तक</strong> - कुमकुम मोहंती, सोनल मानसिंह (भरतनाट्यम भी), किरण सहगल, पंकज चरण दास (ओडिसी नृत्य के जनक), मायाधर राउत, गंगाधर प्रधान।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "18",
                    question_en: "<p>3. The music legend Jagjit Singh is associated with:</p>",
                    question_hi: "<p>3. संगीत के दिग्गज जगजीत सिंह का संबंध किससे है?</p>",
                    options_en: ["<p>Ghazal</p>", "<p>Classical Music</p>", 
                                "<p>Western Music</p>", "<p>Instrument</p>"],
                    options_hi: ["<p>ग़ज़ल</p>", "<p>शास्त्रीय संगीत</p>",
                                "<p>पाश्चात्य संगीत</p>", "<p>वाद्य यंत्र</p>"],
                    solution_en: "<p>3.(a) <strong>Ghazal</strong> (a form of amatory poem or ode, originating in Arabic poetry). <strong>Awards</strong> - Padma Bhushan (2003), Rajasthan Ratna (2012), Lata Mangeshkar Samman (1998), Sahitya Academy Award (1998), Sahitya Kala Academy Award by Rajasthan government (1998). <strong>Other Ghazal Singers: </strong>Hariharan, Pankaj Udhas, Talat Aziz, Anup Jalota, Farida Khanum, Ustad Mehndi Hasan, Ghulam Ali.</p>",
                    solution_hi: "<p>3.(a) <strong>ग़ज़ल</strong> (शौकिया कविता या क़सीदे का एक रूप, जो अरबी कविता में उत्पन्न हुआ है)। <strong>पुरस्कार</strong> - पद्म भूषण (2003), राजस्थान रत्न (2012), लता मंगेशकर सम्मान (1998), साहित्य अकादमी पुरस्कार (1998), राजस्थान सरकार द्वारा साहित्य कला अकादमी पुरस्कार (1998)। <strong>अन्य ग़ज़ल गायक:</strong> हरिहरन, पंकज उधास, तलत अजीज, अनुप जलोटा, फरीदा खानम, उस्ताद मेहंदी हसन, गुलाम अली।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "18",
                    question_en: "<p>4. What is the classical dance of Assam?</p>",
                    question_hi: "<p>4. असम का शास्त्रीय नृत्य कौन-सा है?</p>",
                    options_en: ["<p>Sattriya Nritya</p>", "<p>Bharatnatyam</p>", 
                                "<p>Kathak</p>", "<p>Kuchipudi</p>"],
                    options_hi: ["<p>सत्त्रिया नृत्य</p>", "<p>भरतनाट्यम</p>",
                                "<p>कथक</p>", "<p>कुचिपुड़ी</p>"],
                    solution_en: "<p>4.(a) <strong>Sattriya Nritya</strong> originated in Sattra, monastery, as a part of neo-Vaishnavite movement started by Srimanta Sankardev in Assam, in the 15th Century. It was given the status of a classical dance in the year 2000 by the Sangeet Natak Akademi. <strong>Other classical dances of India</strong>: Bharatnatyam (Tamil Nadu), Kathakali (Kerala), Kuchipudi (Andhra Pradesh), Kathak (Uttar Pradesh), Mohiniyattam (Kerala), Manipuri (Manipur) and Odissi (Odisha).</p>",
                    solution_hi: "<p>4.(a) <strong>सत्त्रिया नृत्य</strong> की उत्पत्ति 15वीं शताब्दी में असम में श्रीमंत शंकरदेव द्वारा शुरू किए गए नव-वैष्णव आंदोलन के एक भाग के रूप में सत्रा, मठ में हुई थी। इसे संगीत नाटक अकादमी द्वारा वर्ष 2000 में शास्त्रीय नृत्य का दर्जा दिया गया था।<strong> भारत के अन्य शास्त्रीय नृत्य:</strong> भरतनाट्यम (तमिलनाडु), कथकली (केरल), कुचिपुड़ी (आंध्र प्रदेश), कथक (उत्तर प्रदेश), मोहिनीअट्टम (केरल), मणिपुरी (मणिपुर) और ओडिसी (ओडिशा)।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "18",
                    question_en: "<p>5. Which Indian state was the first to make it mandatory for all homes to have rooftop rainwater harvesting structures?</p>",
                    question_hi: "<p>5. भारत के कौन-से राज्य ने सर्वप्रथम सभी घरों की छतों पर वर्षा जल संचयन संरचनाओं के निर्माण को अनिवार्य किया था?</p>",
                    options_en: ["<p>Sikkim</p>", "<p>Tamil Nadu</p>", 
                                "<p>Odisha</p>", "<p>Haryana</p>"],
                    options_hi: ["<p>सिक्किम</p>", "<p>तमिलनाडु</p>",
                                "<p>ओडिशा</p>", "<p>हरियाणा</p>"],
                    solution_en: "<p>5.(b) <strong>Tamil Nadu. Rooftop rainwater harvesting</strong> is a technique used for the conservation of water. The rainwater that has fallen on the roof of houses or buildings is collected in storage or underground tanks through the help of pipes in this technique. This helps in recharging the groundwater levels. Rainwater harvesting is already a common practice in Mizoram.</p>",
                    solution_hi: "<p>5.(b) <strong>तमिलनाडु। छत पर वर्षा जल संचयन</strong> जल संरक्षण के लिए उपयोग की जाने वाली एक तकनीक है। इस तकनीक में घरों या इमारतों की छत पर गिरे बारिश के जल को पाइप की मदद से भंडारण या भूमिगत टैंकों में एकत्र किया जाता है। इससे भूजल स्तर को पुनर्भरण करने में मदद मिलती है। मिजोरम में वर्षा जल संचयन पहले से ही एक साधारण प्रक्रिया है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "18",
                    question_en: "<p>6. How many countries are part of SAARC?</p>",
                    question_hi: "<p>6. कितने देश SAARC का हिस्सा हैं?</p>",
                    options_en: ["<p>8</p>", "<p>2</p>", 
                                "<p>5</p>", "<p>7</p>"],
                    options_hi: ["<p>8</p>", "<p>2</p>",
                                "<p>5</p>", "<p>7</p>"],
                    solution_en: "<p>6.(a) <strong>8. South Asian Association for Regional Cooperation (SAARC) </strong>: Established in - 1985. Objective - Advance social improvement, economic growth, and cultural development throughout South Asia. Headquarters - Kathmandu, Nepal. Member Countries - Afghanistan, Bangladesh, Bhutan, India, Maldives, Nepal, Pakistan, and Sri Lanka. Afghanistan joined as the 8th member of SAARC in 2007.</p>",
                    solution_hi: "<p>6.(a) <strong>8 । दक्षिण एशियाई क्षेत्रीय सहयोग संघ (SAARC)</strong>: स्थापना - 1985। उद्देश्य - सम्पूर्ण दक्षिण एशिया में सामाजिक सुधार, आर्थिक विकास और सांस्कृतिक विकास को आगे बढ़ाना। मुख्यालय - काठमांडू, नेपाल। <strong>सदस्य देश -</strong> अफगानिस्तान, बांग्लादेश, भूटान, भारत, मालदीव, नेपाल, पाकिस्तान और श्रीलंका। अफगानिस्तान 2007 में SAARC के 8वें सदस्य के रूप में शामिल हुआ।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "18",
                    question_en: "<p>7. Chaitrual and Phulaich are the festival of which of the following states ?</p>",
                    question_hi: "<p>7. चैत्रुअल (Chaitrual) और फुलाइच (Phulaich) निम्नलिखित में से किस राज्य का त्योहार है ?</p>",
                    options_en: ["<p>Himachal Pradesh</p>", "<p>Gujarat</p>", 
                                "<p>Uttarakhand</p>", "<p>Haryana</p>"],
                    options_hi: ["<p>हिमाचल प्रदेश</p>", "<p>गुजरात</p>",
                                "<p>उत्तराखंड</p>", "<p>हरियाणा</p>"],
                    solution_en: "<p>7.(a) <strong>Himachal Pradesh.</strong> Chaitrual (festival of pictures) - celebrated in the month of Chaitra (March - April) in Sirmaur area. Phulaich (festival of flowers)- celebrated on the 16th day of the Hindu month of Bhadrapada (August - September). <strong>States and their festivals</strong> : Himachal Pradesh - Pori Festival, Minjar Fair, Halda Festival, Losar Festival. Haryana - Teej, Guga Navmi, Gangore Festival. Uttarakhand - Basant Panchami, Bhitauli, Harela, Phooldei, Batsavitri, Ganga Dussehra, Dikar Puja. Gujarat - Navratri, Uttarayan, Rann Utsav, Shamlaji Melo.</p>",
                    solution_hi: "<p>7.(a) <strong>हिमाचल प्रदेश।</strong> चैत्रुअल (चित्रों का त्योहार) - सिरमौर क्षेत्र में चैत्र (मार्च-अप्रैल) महीने में मनाया जाता है। फुलाइच (फूलों का त्योहार)- हिंदू महीने भाद्रपद (अगस्त-सितंबर) के 16वें दिन मनाया जाता है। <strong>राज्य और उनके त्यौहार:</strong> हिमाचल प्रदेश - पोरी महोत्सव, मिंजर मेला, हलदा महोत्सव, लोसर महोत्सव। हरियाणा - तीज, गुगा नवमी, गंगोर महोत्सव। उत्तराखंड - बसंत पंचमी, भिटौली, हरेला, फूलदेई, बटसावित्री, गंगा दशहरा, डिकर पूजा। गुजरात-नवरात्रि, उत्तरायण, रण उत्सव, शामलाजी मेलो।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "18",
                    question_en: "<p>8. Which of the following erstwhile Princely states was primarily associated with Kathak?</p>",
                    question_hi: "<p>8. निम्नलिखित में से किस तत्कालीन रियासत का संबंध मुख्य रूप से कथक से था?</p>",
                    options_en: ["<p>Tripura</p>", "<p>Avadh</p>", 
                                "<p>Baroda</p>", "<p>Vijaynagar</p>"],
                    options_hi: ["<p>त्रिपुरा</p>", "<p>अवध</p>",
                                "<p>वडोदरा</p>", "<p>विजयनगर</p>"],
                    solution_en: "<p>8.(b) <strong>Avadh.</strong> Kathak (Uttar Pradesh) - It was performed under the Mughal emperors. Exponents - Pandit Birju Maharaj, Shovna Narayan, Shambhu Maharaj, Lachhu Maharaj. Other dances of Uttar Pradesh: Charkula, Khyal, Raslila, Nautanki Dance, Kajri. Tripura - Hojagiri, Garia, Jhum, Bijhu, Sangrai, Lebang boomani.</p>",
                    solution_hi: "<p>8.(b) <strong>अवध</strong> । कथक (उत्तर प्रदेश) - यह मुगल सम्राटों के अधीन किया जाता था। प्रतिपादक - पंडित बिरजू महाराज, शोवना नारायण, शंभु महाराज, लच्छू महाराज। उत्तर प्रदेश के अन्य नृत्य: चरकुला, ख्याल, रासलीला, नौटंकी नृत्य, कजरी। त्रिपुरा - होजागिरी, गरिया, झूम, बिझू, संगराई, लेबांग बूमानी।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "18",
                    question_en: "<p>9.Which of the following components is NOT related to the New India Literacy Programme?</p>",
                    question_hi: "<p>9. निम्नलिखित में से कौन-सा घटक नव भारत साक्षरता कार्यक्रम (New India Literacy Programme) से संबंधित नहीं है?</p>",
                    options_en: ["<p>Business and Enhancing skill Development</p>", "<p>Foundational Literacy and Numeracy</p>", 
                                "<p>Critical Life Skills</p>", "<p>Basic Education</p>"],
                    options_hi: ["<p>व्यावसायिक कौशल विकास</p>", "<p>मूलभूत साक्षरता और संख्यात्मक ज्ञान</p>",
                                "<p>महत्वपूर्ण जीवन कौशल</p>", "<p>बुनियादी शिक्षा</p>"],
                    solution_en: "<p>9.(a) Implementation during five years from the FYs 2022-23 to 2026-27. <strong>Aim</strong> - Aims to cover a target of 5.00 crore non-literates in the age group of 15 years and above. The scheme has five components - Foundational Literacy and Numeracy, Critical Life Skills,Vocational Skills Development, Basic Education and Continuing Education, Vocational Skills Development, Continuing Education.</p>",
                    solution_hi: "<p>9.(a) <strong>नव भारत साक्षरता कार्यक्रम (NILP): </strong>वित्त वर्ष 2022-23 से 2026-27 तक पाँच वर्षों के दौरान कार्यान्वयन। <strong>उद्देश्य</strong> - 15 वर्ष और उससे अधिक आयु वर्ग के 5.00 करोड़ गैर-साक्षरों के लक्ष्य को कवर करना है। इस योजना के पाँच घटक हैं - मूलभूत साक्षरता और संख्यात्मकता, महत्वपूर्ण जीवन कौशल, व्यावसायिक कौशल विकास, बुनियादी शिक्षा और सतत शिक्षा। व्यावसायिक कौशल विकास, सतत शिक्षा।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "18",
                    question_en: "<p>10.Ranjumoni Saikia was awarded the Sangeet Natak Akademi Award 2019 for her contribution to which classical dance?</p>",
                    question_hi: "<p>10. रंजुमोनी सैकिया (Ranjumoni Saikia) को किस शास्त्रीय नृत्य में उनके योगदान के लिए संगीत नाटक अकादमी पुरस्कार 2019 से सम्मानित किया गया?</p>",
                    options_en: ["<p>Kuchipudi</p>", "<p>Mohiniyattam</p>", 
                                "<p>Sattriya</p>", "<p>Chhau</p>"],
                    options_hi: ["<p>कुचिपुड़ी</p>", "<p>मोहिनीअट्टम</p>",
                                "<p>सत्त्रिया</p>", "<p>छऊ</p>"],
                    solution_en: "<p>10.(c) <strong>Sattriya</strong> (Classical dance of Assam). Exponents - Guru Jatin Goswami, Manik Barbayan, Ghanakanta Bora. <strong>Other dances and their exponents </strong>: Kuchipudi (Andhra Pradesh) - Indrani Bajpai, Yamini Krishnamurty, Raja Radha Reddy, Swapna Sundari, and Mallika Sarabhai. Mohiniyattam (Kerala) - Sunanda Nair, Kalamandalam Kalyanikutty Amma, Radha Dutta, Vijayalakshmi, Gopika Varma. Chhau (West Bengal) - Gambhir Singh Mura.</p>",
                    solution_hi: "<p>10.(c) <strong>सत्त्रिया</strong> (असम का शास्त्रीय नृत्य)। नर्तक - गुरु जतिन गोस्वामी, माणिक बारबयान, घनकांत बोरा। <strong>अन्य नृत्य और उनके नर्तक:</strong> कुचिपुड़ी (आंध्र प्रदेश) - इंद्राणी बाजपेयी, यामिनी कृष्णमूर्ति, राजा राधा रेड्डी, स्वप्न सुंदरी, और मल्लिका साराभाई। मोहिनीअट्टम (केरल) - सुनंदा नायर, कलामंडलम कल्याणिकुट्टी अम्मा, राधा दत्ता, विजयलक्ष्मी, गोपिका वर्मा। छाऊ (पश्चिम बंगाल) - गंभीर सिंह मुरा।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "18",
                    question_en: "<p>11. When was Kapil Dev awarded with Padma Shri Award?</p>",
                    question_hi: "<p>11. कपिल देव को पद्म श्री पुरस्कार से कब सम्मानित किया गया था?</p>",
                    options_en: ["<p>1981</p>", "<p>1980</p>", 
                                "<p>1982</p>", "<p>1983</p>"],
                    options_hi: ["<p>1981</p>", "<p>1980</p>",
                                "<p>1982</p>", "<p>1983</p>"],
                    solution_en: "<p>11.(c) <strong>1982.</strong> Kapil Dev (The Haryana Hurricane) : He was the captain of the Indian cricket team when India lifted a World Cup trophy (1983). <strong>Awards</strong> - Padma Bhushan (1991), Arjuna Award for Cricket (1979), C. K. Nayudu Lifetime Achievement Award (2013).</p>",
                    solution_hi: "<p>11.(c) <strong>1982</strong> । कपिल देव (हरियाणा हरिकेन): जब भारत ने विश्व कप ट्रॉफी (1983) जीती थी तब वह भारतीय क्रिकेट टीम के कप्तान थे। <strong>पुरस्कार</strong> - पद्म भूषण (1991), क्रिकेट के लिए अर्जुन पुरस्कार (1979), सी.के. नायडू लाइफटाइम अचीवमेंट अवार्ड (2013)।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "18",
                    question_en: "<p>12.Who is credited with creating the popular Raga Miya Ki Malhar?</p>",
                    question_hi: "<p>12. लोकप्रिय राग मिया की मल्हार की रचना का श्रेय किसे दिया जाता है?</p>",
                    options_en: ["<p>Tansen</p>", "<p>Amir Khusrau</p>", 
                                "<p>Meera Bai</p>", "<p>Zakir Hussain</p>"],
                    options_hi: ["<p>तानसेन</p>", "<p>अमीर खुसरो</p>",
                                "<p>मीरा बाई</p>", "<p>जाकिर हुसैन</p>"],
                    solution_en: "<p>12.(a) <strong>Tansen.</strong> Compositions - Darbari Kanhra (night raga), Mian Ki Todi (morning raga), Mian ki Sarang (mid-day raga). Miya ki Malhar (seasonal raga). <strong>Zakir Hussain</strong> - An Indian tabla player, composer, percussionist and music producer. <strong>Amir Khusrau</strong> (founder of qawwali). He was a famous musician in the court of Alauddin Khalji. <strong>Meera Bai</strong> (Devotee of Krishna) - Bhajans and Raga composed by her - Raag Govind, Govind Tika, Raag Soratha, Meera Ki Malhar, Mira Padavali, Narsi ji Ka Mayara.</p>",
                    solution_hi: "<p>12.(a) <strong>तानसेन।</strong> रचनाएँ - दरबारी कान्हड़ा (रात्रि राग), मियाँ की तोड़ी (सुबह का राग), मियाँ की सारंग (मध्याह्न राग)। मिया की मल्हार (मौसमी राग)। <strong>ज़ाकिर हुसैन</strong> - एक भारतीय तबला वादक, संगीतकार, तालवादक और संगीत निर्माता। <strong>अमीर खुसरो</strong> (कव्वाली के संस्थापक)। वह अलाउद्दीन खिलजी के दरबार में एक प्रसिद्ध संगीतकार थे। <strong>मीरा बाई</strong> (कृष्ण भक्त) - उनके द्वारा रचित भजन और राग - राग गोविंद, गोविंद टीका, राग सोरठा, मीरा की मल्हार, मीरा पदावली, नरसी जी का मायरा।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "18",
                    question_en: "<p>13. Bhimsen Joshi belongs to which of the following classical music traditions?</p>",
                    question_hi: "<p>13. भीमसेन जोशी का संबंध निम्नलिखित में से किस शास्त्रीय संगीत परंपरा से है?</p>",
                    options_en: ["<p>Delhi Gharana</p>", "<p>Banaras Gharana</p>", 
                                "<p>Kirana Gharana</p>", "<p>Jaipur Gharana</p>"],
                    options_hi: ["<p>दिल्ली घराना</p>", "<p>बनारस घराना</p>",
                                "<p>किराना घराना</p>", "<p>जयपुर घराना</p>"],
                    solution_en: "<p>13.(c) <strong>Kirana gharana : Founder </strong>- Ustad Abdul Karim Khan. <strong>Famous artists</strong> - Suresh Babu Mane, Hira Bai Badodekar and Roshanara Begum. <strong>Bhimsen Joshi Awards</strong> - Bharat Ratna (2009), Padma Vibhusan (1999), Padma Bhusan (1985), Padma Shri (1972). <strong>Gharana and Founder:</strong> Banaras - Ram Sahai. Jaipur Gharana - Bhanuji.</p>",
                    solution_hi: "<p>13.(c) <strong>किराना घराना : संस्थापक -</strong> उस्ताद अब्दुल करीम खान। <strong>प्रसिद्ध कलाकार -</strong> सुरेश बाबू माने, हीराबाई बडोडेकर और रोशनआरा बेगम।<strong> भीमसेन जोशी पुरस्कार</strong> - भारत रत्न (2009), पद्म विभूषण (1999), पद्म भूषण (1985), पद्म श्री (1972)। <strong>घराना और संस्थापक: </strong>बनारस - राम सहाय। जयपुर घराना - भानुजी.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "18",
                    question_en: "<p>14. Lotia is a regional music form of which state ?</p>",
                    question_hi: "<p>14. लोटिया किस राज्य की क्षेत्रीय संगीत शैली है?</p>",
                    options_en: ["<p>Odisha</p>", "<p>Rajasthan</p>", 
                                "<p>Assam</p>", "<p>Bihar</p>"],
                    options_hi: ["<p>ओडिशा</p>", "<p>राजस्थान</p>",
                                "<p>असम</p>", "<p>बिहार</p>"],
                    solution_en: "<p>14.(b) <strong>Rajasthan. Lotia</strong> - It is sung by the peasants while working in the fields on the occasion of Lotia festival in the Chaitra month according to the Hindu calendar. Women bring lotas (a vessel to fill water) and kalash (a vessel considered to be auspicious to fill water during worship) filled with water from ponds and wells. <strong>Traditional Folk Music of Rajasthan </strong>- Pabuji Ki Phach, Maand, Panihari.</p>",
                    solution_hi: "<p>14.(b) <strong>राजस्थान। लोटिया -</strong> यह हिंदू कैलेंडर के अनुसार चैत्र माह में लोटिया त्योहार के अवसर पर खेतों में काम करते समय किसानों द्वारा गाया जाता है। महिलाएं तालाबों और कुओं से पानी भरकर लोटा (पानी भरने का बर्तन) और कलश (पूजा के दौरान पानी भरने के लिए शुभ माना जाने वाला बर्तन) लाती हैं। <strong>राजस्थान का पारंपरिक लोक संगीत </strong>- पाबूजी की फाच, मांड, पणिहारी।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "misc",
                    question_en: "<p>15. Which of the following Indian classical dancers was the recipient of the Padma Vibhushan award of 2003?</p>",
                    question_hi: "<p>15. निम्नलिखित में से कौन-सी/सा भारतीय शास्त्रीय नर्तकी / नर्तक 2003 के पद्म विभूषण पुरस्कार की/के प्राप्तकर्ता थी/थे?</p>",
                    options_en: ["<p>Saswati Sen</p>", "<p>Elam Endira Devi</p>", 
                                "<p>Birju Maharaj</p>", "<p>Sonal mansingh</p>"],
                    options_hi: ["<p>सास्वती सेन (Saswati Sen)</p>", "<p>एलम इंदिरा देवी (Elam Endira Devi)</p>",
                                "<p>बिरजू महाराज (Birju Maharaj)</p>", "<p>सोनल मानसिंह (Sonal Mansingh)</p>"],
                    solution_en: "<p>15.(d) <strong>Sonal Mansingh</strong> : Indian classical dancer in Bharatanatyam and Odissi dancing style. <strong>Award</strong> - Padma Bhushan (youngest recipient) (1992), Lifetime Achievement Award (2019). <strong>Other dancers and awards : Saswati Sen</strong> (Kathak dancer) - Sangeet Natak Akademi Award (2004-05). <strong>Pandit Birju Maharaj</strong> ( Kathak dancer) - Padma Vibhushan (1986), Sangeet Natak Akademi Award (1964), Kalidas Samman (1987), Lata Mangeshkar Puruskar (2002). <strong>Elam Endira Devi </strong>(dance form of Manipuri) - Padma Shri (2014).</p>",
                    solution_hi: "<p>15.(d) <strong>सोनल मानसिंह (Sonal Mansingh)</strong>। भरतनाट्यम और ओडिसी नृत्य शैली में भारतीय शास्त्रीय नर्तक। पुरस्कार - संगीत नाटक अकादमी पुरस्कार (1987) और पद्म भूषण (1992)। <strong>अन्य नर्तक और पुरस्कार: शाश्वती सेन </strong>(कथक नर्तक) - संगीत नाटक अकादमी पुरस्कार (2004-05)। <strong>पंडित बिरजू महाराज</strong> (कथक नर्तक) - संगीत नाटक अकादमी पुरस्कार (1964), पद्म विभूषण (1986), कालिदास सम्मान (1987), लता मंगेशकर पुरस्कार (2002)। <strong>एलम एंडिरा (इंदिरा) देवी</strong> (मणिपुरी नृत्य शैली) - पद्म श्री (2014)।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>