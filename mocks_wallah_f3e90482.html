<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">25:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 40</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">40</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 25 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["18"] = {
                name: "General Awareness",
                start: 0,
                end: 38
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="18">General Awareness</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 39,
                end: 39
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "18",
                    question_en: "<p>1. When was the first Indian Cricket Club-the Calcutta Cricket Club established ?</p>",
                    question_hi: "<p>1. पहला भारतीय क्रिकेट क्लब- कलकत्ता क्रिकेट क्लब कब स्थापित किया गया था ?</p>",
                    options_en: ["<p>1792</p>", "<p>1790</p>", 
                                "<p>1791</p>", "<p>1793</p>"],
                    options_hi: ["<p>1792</p>", "<p>1790</p>",
                                "<p>1791</p>", "<p>1793</p>"],
                    solution_en: "<p>1.(a) The establishment of the Calcutta Cricket Club in 1792, In fact, it is the second-oldest cricket club in the world, after the MCC (1787). The Oriental cricket club founded in 1848, was the first Indian cricket club established by the Parsi community of Mumbai.</p>",
                    solution_hi: "<p>1.(a) 1792 में कलकत्ता क्रिकेट क्लब की स्थापना, वास्तव में, यह MCC (1787) के बाद दुनिया का दूसरा सबसे पुराना क्रिकेट क्लब है। 1848 में स्थापित ओरिएंटल क्रिकेट क्लब, मुंबई के पारसी समुदाय द्वारा स्थापित पहला भारतीय क्रिकेट क्लब था।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "2",
                    section: "18",
                    question_en: "2. Name the creation of Devaki Nandan Khatri which is considered to be the first authentic work of prose in Hindi ?",
                    question_hi: "2. देवकी नंदन खत्री की रचना का नाम बताइए जिसे हिंदी में गद्य की  प्रथम प्रामाणिक कृति माना जाता है ?",
                    options_en: [" Ratnavali  ", " Chandrakanta ", 
                                " Gitanjali ", " Gita Govinda "],
                    options_hi: [" रत्नावली", " चंद्रकांता",
                                " गीतांजलि", " गीता गोविंदा"],
                    solution_en: "2.(b) Chandrakanta is the creation of Devaki Nandan Khatri. Ratnavali is a famous Sanskrit drama composed in four Acts by Sri Harsha. Gitanjali is a collection of poems by the Bengali poet Rabindranath Tagore. The Gita Govinda is a work composed by Jayadeva.",
                    solution_hi: "2.(b) चंद्रकांता देवकी नंदन खत्री की रचना है। रत्नावली एक प्रसिद्ध संस्कृत नाटक है जिसकी रचना श्री हर्ष द्वारा चार अधिनियमों में की गई है। गीतांजलि बंगाली कवि रवींद्रनाथ टैगोर की कविताओं का संग्रह है। गीता गोविंदा जयदेव द्वारा रचित एक कृति है।",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "3",
                    section: "18",
                    question_en: "<p>3. The International Criminal Police Commission (ICPC) predecessor to INTERPOL&nbsp;Was founded at _____ in 1923.</p>",
                    question_hi: "<p>3. इंटरपोल के पूर्ववर्ती अंतर्राष्ट्रीय आपराधिक पुलिस आयोग (ICPC) की स्थापना 1923 में _____ में हुई थी</p>",
                    options_en: ["<p>Washington</p>", "<p>Geneva</p>", 
                                "<p>New York</p>", "<p>Vienna</p>"],
                    options_hi: ["<p>वाशिंगटन</p>", "<p>जिनेवा</p>",
                                "<p>न्यूयॉर्क</p>", "<p>वियना</p>"],
                    solution_en: "<p>3.(d) The International Criminal Police Commission (ICPC) predecessor to INTERPOL was founded in Vienna, Austria in 1923. Interpol aims to promote the widest-possible mutual assistance between criminal police forces and to establish and develop institutions likely to contribute to the prevention and suppression of international crime. Headquarter is in Lyon, France,</p>",
                    solution_hi: "<p>3.(d) इंटरपोल के पूर्ववर्ती अंतर्राष्ट्रीय आपराधिक पुलिस आयोग (ICPC) 1923 में ऑस्ट्रिया के विएना में स्थापित किया गया था। इंटरपोल (INTERPOL) का उद्देश्य आपराधिक पुलिस बलों के बीच व्यापक-संभव पारस्परिक सहायता को बढ़ावा देना और अंतरराष्ट्रीय अपराध की रोकथाम और दमन में योगदान करने वाले संस्थानों की स्थापना और विकास करना है। मुख्यालय ल्यों, फ्रांस में है।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "4",
                    section: "18",
                    question_en: "4. The first Pressurised Heavy Water Reactor (PHWR) of India in 1964 was a collaborative venture between Atomic Energy in ____ Ltd. and NPCIL of India.  ",
                    question_hi: "4. 1964 में भारत का पहला दबावयुक्त भारी पानी प्रतिघातक (PHWR) _____लिमिटेड में परमाणु ऊर्जा और भारत के NPCIL के बीच एक सहयोगी उद्यम था।",
                    options_en: [" France ", " USSR ", 
                                " Israel ", " Canada "],
                    options_hi: [" फ्रांस", " सोवियत संघ",
                                " इजराइल", " कनाडा "],
                    solution_en: "4.(d) The first Pressurised Heavy Water Reactor (PHWR) of India in 1964 was a collaborative venture between Atomic Energy in Canada Ltd. and NPCIL of India.c",
                    solution_hi: "4.(d) 1964 में भारत का पहला प्रेशराइज्ड हेवी वाटर रिएक्टर (PHWR) कनाडा लिमिटेड में परमाणु ऊर्जा और भारत के NPCIL के बीच एक सहयोगी उद्यम था।",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "5",
                    section: "18",
                    question_en: "<p>5. Which of the following special trains can be taken to travel to Lumbini, Bodhgaya, Sarnath and Kushinagar ?</p>",
                    question_hi: "<p>5. लुंबिनी, बोधगया, सारनाथ और कुशीनगर जाने के लिए निम्नलिखित में से कौन सी विशेष ट्रेन ली जा सकती है ?</p>",
                    options_en: ["<p>Buddhist Train</p>", "<p>Buddhist Tourist Train</p>", 
                                "<p>Buddhist Circuit Tourist Train</p>", "<p>Buddhist Express Special Tourist Train</p>"],
                    options_hi: ["<p>बौद्ध ट्रेन</p>", "<p>बौद्ध पर्यटक ट्रेन</p>",
                                "<p>बौद्ध सर्किट पर्यटक ट्रेन</p>", "<p>बौद्ध एक्सप्रेस विशेष पर्यटक ट्रेन</p>"],
                    solution_en: "<p>5.(c) Buddhist Circuit Tourist Trains are special trains that can be taken to travel to Lumbini, Bodhgaya, Sarnath, and Kushinagar. The Mahaparinirvan Express is a tourist train that was launched by the Indian Railway Catering and Tourism Corporation (IRCTC) on 28 March 2007, to attract Buddhist pilgrims.</p>",
                    solution_hi: "<p>5.(c) बौद्ध सर्किट पर्यटक ट्रेनें विशेष ट्रेनें हैं जिन्हें लुंबिनी, बोधगया, सारनाथ और कुशीनगर की यात्रा के लिए ले जाया जा सकता है। महापरिनिर्वाण एक्सप्रेस एक पर्यटक ट्रेन है जिसे भारतीय रेलवे खानपान और पर्यटन निगम (आईआरसीटीसी) द्वारा 28 मार्च 2007 को बौद्ध तीर्थयात्रियों को आकर्षित करने के लिए शुरू किया गया था।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "6",
                    section: "18",
                    question_en: "<p>6. Name the British Chemist who presented the atomic theory in 1808, on conservation of mass and law of definite proportions, which was a turning point in the study of motion.</p>",
                    question_hi: "<p>6. उस ब्रिटिश रसायनज्ञ का नाम बताइए जिसने 1808 में द्रव्यमान के संरक्षण और निश्चित अनुपात के नियम पर परमाणु सिद्धांत प्रस्तुत किया, जो गति के अध्ययन में एक महत्वपूर्ण मोड़ था।</p>",
                    options_en: ["<p>John Dalton</p>", "<p>Lavoisier</p>", 
                                "<p>Ernest Rutherford</p>", "<p>Proust</p>"],
                    options_hi: ["<p>जॉन डाल्टन</p>", "<p>लावोइसियर</p>",
                                "<p>अर्नेस्ट रदरफोर्ड</p>", "<p>प्रॉस्ट</p>"],
                    solution_en: "<p>6.(a) John Dalton the British Chemist who presented the atomic theory in 1808, on conservation of mass and law of definite proportions, which was a turning point in the study of motion. Ernest Rutherford postulated the nuclear structure of the atom, discovered alpha and beta rays and proposed the laws of radioactive decay. Proust is a social novelist. Antoine Lavoisier is called the &ldquo;father of modern chemistry&rdquo;.</p>",
                    solution_hi: "<p>6.(a) जॉन डाल्टन ब्रिटिश केमिस्ट जिन्होंने 1808 में द्रव्यमान के संरक्षण और निश्चित अनुपात के कानून पर परमाणु सिद्धांत प्रस्तुत किया, जो गति के अध्ययन में एक महत्वपूर्ण मोड़ था। अर्नेस्ट रदरफोर्ड ने परमाणु की परमाणु संरचना की परिकल्पना की, अल्फा और बीटा किरणों की खोज की और रेडियोधर्मी क्षय के नियमों का प्रस्ताव रखा। प्राउस्ट एक सामाजिक उपन्यासकार हैं। एंटोनी लावोज़ियर को \"आधुनिक रसायन विज्ञान का जनक\" कहा जाता है।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "7",
                    section: "18",
                    question_en: "<p>7. Government of India has set ambitious target of building ____Kms of highways in FY21</p>",
                    question_hi: "<p>7. भारत सरकार ने वित्त वर्ष 2021 में _____ किलोमीटर राजमार्ग बनाने का महत्वाकांक्षी लक्ष्य निर्धारित किया है</p>",
                    options_en: ["<p>15000</p>", "<p>10000</p>", 
                                "<p>5000</p>", "<p>1000</p>"],
                    options_hi: ["<p>15000</p>", "<p>10000</p>",
                                "<p>5000</p>", "<p>1000</p>"],
                    solution_en: "<p>7.(a) The government of India has set an ambitious target of building 15000 Kms of highways in FY21. National Highways Authority of India (NHAI) was constituted by an Act of Parliament in 1988.</p>",
                    solution_hi: "<p>7.(a) भारत सरकार ने वित्त वर्ष 2021 में 15,000 किलोमीटर राजमार्ग बनाने का महत्वाकांक्षी लक्ष्य रखा है। भारतीय राष्ट्रीय राजमार्ग प्राधिकरण (NHAI) का गठन 1988 में संसद के एक अधिनियम द्वारा किया गया था।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "8",
                    section: "18",
                    question_en: "8. Name the book written by Mahatma Gandhi in 1909 that suggested British rule would come to an end if Indians didn’t cooperate with them ?",
                    question_hi: "8. 1909 में महात्मा गांधी द्वारा लिखी गई उस पुस्तक का नाम बताइए जिसमें सुझाव दिया गया था कि यदि भारतीयों ने ब्रिटिश शासन का सहयोग नहीं किया तो वह समाप्त हो जाएगा ?",
                    options_en: [" Hind Swaraj ", " Constructive Programme-Its meaning and place ", 
                                " Village Swaraj ", " India of my dreams "],
                    options_hi: [" हिंद स्वराज", " रचनात्मक कार्यक्रम-इसका अर्थ और स्थान",
                                " ग्राम स्वराज", " मेरे सपनों का भारत"],
                    solution_en: "8.(a) Hind Swaraj was written by Mahatma Gandhi in 1909 that suggested British rule would come to an end if Indians didn’t cooperate with them. Hind Swaraj is the most seminal work of Mahatma Gandhi and the most original contribution to political theory after Kautilya\'s Arthashastra.",
                    solution_hi: "8.(a) 1909 में महात्मा गांधी द्वारा हिंद स्वराज लिखा गया था कि यदि भारतीयों ने उनके साथ सहयोग नहीं किया तो ब्रिटिश शासन समाप्त हो जाएगा। कौटिल्य के अर्थशास्त्र के बाद हिंद स्वराज महात्मा गांधी की सबसे महत्वपूर्ण कृति और राजनीतिक सिद्धांत में सबसे मौलिक योगदान है।",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "9",
                    section: "18",
                    question_en: "<p>9. Which among the following is <strong>NOT </strong>an extension for a video file ?</p>",
                    question_hi: "<p>9. निम्नलिखित में से कौन एक वीडियो फ़ाइल का एक्सटेंशन नहीं है ?</p>",
                    options_en: ["<p>.avi</p>", "<p>.mov</p>", 
                                "<p>.jpeg</p>", "<p>.mp4</p>"],
                    options_hi: ["<p>.avi</p>", "<p>.mov</p>",
                                "<p>.jpeg</p>", "<p>.mp4</p>"],
                    solution_en: "<p>9.(c) JPEG stands for &ldquo;Joint Photographic Experts Group&rdquo;. It is an extension of the image file. .avi is Audio Video Interleave.Mov is a file extension of Apple QuickTime movie files. MP4 is a digital multimedia container format most commonly used to store video and audio.</p>",
                    solution_hi: "<p>9.(c) JPEG का अर्थ \"संयुक्त फोटोग्राफिक विशेषज्ञ समूह\" है। यह इमेज फाइल का एक्सटेंशन है। .avi ऑडियो वीडियो इंटरलीव है।मोव ऐप्पल क्विकटाइम मूवी फाइलों का एक फाइल एक्सटेंशन है। MP4 एक डिजिटल मल्टीमीडिया कंटेनर प्रारूप है जिसका उपयोग आमतौर पर वीडियो और ऑडियो को स्टोर करने के लिए किया जाता है।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "10",
                    section: "18",
                    question_en: "10. The Ministry of Human Resource Development has designed a one stop education portal which caters to the needs of students, starting from elementary students to research scholars, teachers and life-lonf learners. What is the name of that portal ?",
                    question_hi: "10. मानव संसाधन विकास मंत्रालय ने एक वन स्टॉप शिक्षा पोर्टल तैयार किया है जो छात्रों की जरूरतों को पूरा करता है, प्रारंभिक छात्रों से लेकर शोध करने वाले विद्वानों, शिक्षकों और जीवन भर सीखने वालों तक। उस पोर्टल का नाम क्या है ?",
                    options_en: [" PRASHIKSHAK ", " SAKSHAT ", 
                                " PADHAI ", " DIKSHA "],
                    options_hi: [" प्रशिक्षक", " साक्षात",
                                " पढाई", " दीक्षा"],
                    solution_en: "10.(b) The Ministry of Human Resource Development has designed a one-stop education portal that caters to the needs of students, starting from elementary students to research scholars, teachers and life-lonf learners is Sakshat. The pilot project SAKSHAT: A One-Stop Education Portal launched on October 30, 2006 .",
                    solution_hi: "10.(b) मानव संसाधन विकास मंत्रालय ने एक वन-स्टॉप शिक्षा पोर्टल तैयार किया है जो छात्रों की जरूरतों को पूरा करता है, प्रारंभिक छात्रों से लेकर शोध करने वाले विद्वानों, शिक्षकों और आजीवन शिक्षार्थियों तक साक्षात है। पायलट प्रोजेक्ट साक्षत: एक वन-स्टॉप एजुकेशन पोर्टल 30 अक्टूबर, 2006 को लॉन्च किया गया।",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "11",
                    section: "18",
                    question_en: "<p>11. Name the writ under which the court orders that the arrested person should be presented before it or can order to set free an arrested person if the manner or grounds of arrest are NOT lawful or satisfactory.</p>",
                    question_hi: "<p>11. उस रिट का नाम बताइए जिसके तहत अदालत आदेश देती है कि गिरफ्तार व्यक्ति को उसके सामने पेश किया जाए या गिरफ्तार व्यक्ति को मुक्त करने का आदेश दे सकता है यदि गिरफ्तारी का तरीका या आधार वैध या संतोषजनक नहीं है</p>",
                    options_en: ["<p>Quo Warranto</p>", "<p>Habeas corpus</p>", 
                                "<p>Mandamus</p>", "<p>Certiorari</p>"],
                    options_hi: ["<p>अधिकार-पृच्छा</p>", "<p>बन्दी प्रत्यक्षीकरण</p>",
                                "<p>परमादेश</p>", "<p>उत्प्रेषण-लेख</p>"],
                    solution_en: "<p>11.(b) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728883443311.png\" alt=\"rId4\" width=\"584\" height=\"225\"></p>",
                    solution_hi: "<p>11.(b) <br><strong id=\"docs-internal-guid-68b17610-7fff-f1c0-b52b-5942cdd63f3e\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcMA2anUG0q2eg6q6xKr0AwHDJuwdDydqg8qtW9nm-lxv-slPd0D66o_zV6Otx8oVHY_UgsADDBUdLZGq_uRPWei9l1EmdR414ENoZSqwomnuQwQt7Bw7ZqpxqifDlF06PDvKLyKyN5ulefztEc5wLmBGYM?key=AyKYg_U9C7PqB4v7PQWAUw\" width=\"717\" height=\"248\"></strong></p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "12",
                    section: "18",
                    question_en: "<p>12. Who among the following Nobel Prize winners is the founder of a grassroot movement to combat deforestation ?</p>",
                    question_hi: "<p>12. निम्नलिखित नोबेल पुरस्कार विजेताओं में से कौन वनों की कटाई से निपटने के लिए जमीनी स्तर पर आंदोलन के संस्थापक हैं ?</p>",
                    options_en: ["<p>Francoise Barre</p>", "<p>Linda Buck</p>", 
                                "<p>Wangari Mathai</p>", "<p>May-Britt Moser</p>"],
                    options_hi: ["<p>फ्रेंकोइस बैरे</p>", "<p>लिंडा बक</p>",
                                "<p>वंगारी मथाई</p>", "<p>मे-ब्रिट मोसेर</p>"],
                    solution_en: "<p>12.(c) Wangari Mathai (Nobel Prize Winner) is the founder of a grassroots movement to combat deforestation. Wangari Maathai is internationally recognized for her persistent struggle for democracy, human rights, and environmental conservation.</p>",
                    solution_hi: "<p>12.(c) वंगारी मथाई (नोबेल पुरस्कार विजेता) वनों की कटाई से निपटने के लिए जमीनी स्तर पर आंदोलन के संस्थापक हैं। वंगारी मथाई को लोकतंत्र, मानवाधिकारों और पर्यावरण संरक्षण के लिए उनके लगातार संघर्ष के लिए अंतरराष्ट्रीय स्तर पर पहचान मिली है।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "13",
                    section: "18",
                    question_en: "13. Which of the following river’s sections was declared as National Waterways-2 in 1988 ?",
                    question_hi: "13. निम्नलिखित में से किस नदी के खंड को 1988 में राष्ट्रीय जलमार्ग-2 घोषित किया गया था ?",
                    options_en: [" Krishna ", " Narmada ", 
                                " Brahmaputra ", " Ganga "],
                    options_hi: [" कृष्णा", " नर्मदा",
                                " ब्रह्मपुत्र", " गंगा"],
                    solution_en: "13.(c) Brahmaputra river’s section was declared as National Waterways-2 in 1988.Brahmaputra River, Bengali (Jamuna), Tibetan (Tsangpo), Chinese (Pinyin).",
                    solution_hi: "13.(c) 1988 में ब्रह्मपुत्र नदी के खंड को राष्ट्रीय जलमार्ग -2 घोषित किया गया था। ब्रह्मपुत्र नदी, बंगाली (जमुना), तिब्बती (त्संगपो), चीनी (पिनयिन)।",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "14",
                    section: "18",
                    question_en: "14. Who along with Motilal Nehru formed the Swaraj Party within the Congress to argue for a return to council politics ? ",
                    question_hi: "14. परिषद राजनीति में वापसी के लिए बहस करने के लिए मोतीलाल नेहरू के साथ किसने कांग्रेस के भीतर स्वराज पार्टी का गठन किया ?",
                    options_en: [" CR Das ", " Jawaharlal Nehru ", 
                                " BR Ambedkar ", " Subhas Chandra Bose "],
                    options_hi: [" सीआर दास", " जवाहर लाल नेहरू",
                                " बीआर अम्बेडकर", " सुभाष चंद्र बोस"],
                    solution_en: "14.(a) In December 1922, Chittaranjan Das, Narasimha Chintaman Kelkar and Motilal Nehru formed the Congress-Khilafat Swarajya Party with Das as the president and Nehru as one of the secretaries.",
                    solution_hi: "14.(a) दिसंबर 1922 में, चित्तरंजन दास, नरसिम्हा चिंतामन केलकर और मोतीलाल नेहरू ने कांग्रेस-खिलाफत स्वराज्य पार्टी का गठन किया, जिसमें दास अध्यक्ष और नेहरू सचिवों में से एक थे।",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "15",
                    section: "18",
                    question_en: "15. What is the scale for measuring a hydrogen ion concentration in a solution ?",
                    question_hi: "15. किसी विलयन में हाइड्रोजन आयन सांद्रता मापने का पैमाना क्या है ?",
                    options_en: [" pH scale  ", " OH scale  ", 
                                " Hydrogen scale ", " dB scale "],
                    options_hi: [" pH स्केल", " OH स्केल",
                                " हाइड्रोजन स्केल", " dB स्केल"],
                    solution_en: "15.(a) pH scale is the scale for measuring a hydrogen ion concentration is a measure of how acidic/basic water is. The range goes from 0 - 14, with 7 being neutral. pHs of less than 7 indicate acidity, whereas a pH of greater than 7 indicates a base in a solution.",
                    solution_hi: "15.(a) pH पैमाना हाइड्रोजन आयन सांद्रता को मापने का पैमाना है जो इस बात का माप है कि पानी कितना अम्लीय/क्षारीय है। सीमा 0 - 14 से जाती है, जिसमें 7 तटस्थ होते हैं। 7 से कम का पीएच अम्लता को दर्शाता है, जबकि 7 से अधिक का पीएच एक घोल में एक आधार को दर्शाता है।",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "16",
                    section: "18",
                    question_en: "<p>16. Tendu, Amaltas, Bel are common trees found in which type of forests in India ?</p>",
                    question_hi: "<p>16. तेंदू, अमलतास, बेल भारत में किस प्रकार के वनों में पाए जाने वाले सामान्य वृक्ष हैं ?</p>",
                    options_en: ["<p>Tropical thorny forests</p>", "<p>Dry deciduous forests</p>", 
                                "<p>Moist deciduous forests</p>", "<p>Montane forests</p>"],
                    options_hi: ["<p>उष्णकटिबंधीय कांटेदार वन</p>", "<p>शुष्क पर्णपाती वन</p>",
                                "<p>आर्द्र पर्णपाती वन</p>", "<p>पर्वतीय वन</p>"],
                    solution_en: "<p>16.(b) Tendu, Amaltas, Bel are common trees found in Dry deciduous forests type of forests in India. Tropical dry deciduous forests (TDFs) can be found in severe and extremely variable climates characterized by low annual rainfall, 5-6 dry months within the annual cycle, and nutrient-poor soil.</p>",
                    solution_hi: "<p>16.(b) तेंदू, अमलतास, बेल भारत में शुष्क पर्णपाती वनों के प्रकार के जंगलों में पाए जाने वाले सामान्य पेड़ हैं। उष्णकटिबंधीय शुष्क पर्णपाती वन (TDF) कम वार्षिक वर्षा, वार्षिक चक्र के भीतर 5-6 शुष्क महीनों और पोषक तत्वों की कमी वाली मिट्टी की विशेषता वाले गंभीर और अत्यंत परिवर्तनशील जलवायु में पाए जा सकते हैं।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "17",
                    section: "18",
                    question_en: "17. Which Mughal monument was designed by Ustad Ahmed Lahori and declared a UNESCO World Heritage Site in 1983 ? ",
                    question_hi: "17. किस मुगल स्मारक को उस्ताद अहमद लाहौरी द्वारा डिजाइन किया गया था और 1983 में यूनेस्को की विश्व धरोहर स्थल घोषित किया गया था ?",
                    options_en: [" Red Fort ", " Taj Mahal ", 
                                " Agra Fort ", " Humayun’s Tomb  "],
                    options_hi: [" लाल किला", " ताजमहल",
                                " आगरा किला ", " हुमायूँ का मकबरा"],
                    solution_en: "17.(b) Taj Mahal Was designed by Ustad Ahmad Lahori and declared a UNESCO World Heritage Site in 1983. The Red Fort Complex was built as the palace fort of Shahjahanabad – the new capital of the fifth Mughal Emperor of India, Shah Jahan. Agra fort  built by Mughal emperor Akbar in 1565 AD. Humayun\'s Tomb was built in the 1560s, with the patronage of Humayun\'s son, the great Emperor Akbar. ",
                    solution_hi: "17.(b) ताजमहल, उस्ताद अहमद लाहौरी द्वारा डिजाइन किया गया था और 1983 में UNESCO की विश्व धरोहर स्थल घोषित किया गया था। लाल किला परिसर शाहजहानाबाद के महल किले के रूप में बनाया गया था - भारत के पांचवें मुगल सम्राट शाहजहाँ की नई राजधानी। आगरा का किला मुगल बादशाह अकबर ने 1565 ई. में बनवाया था। हुमायूँ का मकबरा 1560 के दशक में हुमायूँ के बेटे, महान सम्राट अकबर के संरक्षण में बनाया गया था।",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "18",
                    section: "18",
                    question_en: "<p>18. Yakshagana is a folk performance of which Indian state ?</p>",
                    question_hi: "<p>18. यक्षगण किस भारतीय राज्य का लोक प्रदर्शन है ?</p>",
                    options_en: ["<p>Gujarat</p>", "<p>Karnataka</p>", 
                                "<p>Maharashtra</p>", "<p>Assam</p>"],
                    options_hi: ["<p>गुजरात</p>", "<p>कर्नाटक</p>",
                                "<p>महाराष्ट्र</p>", "<p>असम</p>"],
                    solution_en: "<p>18.(b) Yakshagana is a folk performance of Karnataka. Garba, folk dance of Gujarat, India. There are six popular folk dances of Maharashtra. These are Lavani, Dhangari Gaja, Lezim, Koli, Gondhal and Tamasha dances. Folk dances of Assam include the Bihu and the Bagurumba (both danced during festivals held in the spring).</p>",
                    solution_hi: "<p>18.(b) यक्षगान कर्नाटक का लोक प्रदर्शन है। गरबा, गुजरात, भारत का लोक नृत्य। महाराष्ट्र के छह लोकप्रिय लोक नृत्य हैं। ये हैं लावणी, धंगारी गाजा, लेज़िम, कोली, गोंधल और तमाशा नृत्य। असम के लोक नृत्यों में बिहू और बागुरुम्बा (दोनों वसंत ऋतु में आयोजित त्योहारों के दौरान नृत्य किए गए) शामिल हैं।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "19",
                    section: "18",
                    question_en: "<p>19. What is another name of calcium oxide ?</p>",
                    question_hi: "<p>19. कैल्शियम ऑक्साइड का दूसरा नाम क्या है ?</p>",
                    options_en: ["<p>Lime Soda</p>", "<p>Baking Soda</p>", 
                                "<p>Quick Lime</p>", "<p>Cement</p>"],
                    options_hi: ["<p>सोडा लाइम</p>", "<p>खाने का सोडा</p>",
                                "<p>कलीचूना</p>", "<p>सीमेंट</p>"],
                    solution_en: "<p>19.(c) Another name of calcium oxide(CaO) is quick lime. Baking soda, also known as sodium bicarbonate(NaHCO₃), is widely used in baking. Cement formula is 4CaO&middot;Al<sub>2</sub>O₃&middot;Fe2O₃ = calcium alumino ferrite.</p>",
                    solution_hi: "<p>19.(c) कैल्शियम ऑक्साइड (CaO) का दूसरा नाम क्विक लाइम है। बेकिंग सोडा, जिसे सोडियम बाइकार्बोनेट (NaHCO₃) के रूप में भी जाना जाता है, का व्यापक रूप से बेकिंग में उपयोग किया जाता है। सीमेंट का फॉर्मूला 4CaO&middot;Al<sub>2</sub>O₃&middot;Fe2O₃ = कैल्शियम एल्युमिनो फेराइट है।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "20",
                    section: "18",
                    question_en: "<p>20. Which of the following is <strong>NOT </strong>a part of the National Social Assistance Programme ?</p>",
                    question_hi: "<p>20. निम्नलिखित में से कौन राष्ट्रीय सामाजिक सहायता कार्यक्रम का हिस्सा नहीं है ?</p>",
                    options_en: ["<p>Indira Gandhi National Disability Pension Scheme</p>", "<p>Indira Gandhi National Widow Pension Scheme</p>", 
                                "<p>Annapurna</p>", "<p>AYUSH</p>"],
                    options_hi: ["<p>इंदिरा गांधी राष्ट्रीय विकलांगता पेंशन योजना</p>", "<p>इंदिरा गांधी राष्ट्रीय विधवा पेंशन योजना</p>",
                                "<p>अन्नपूर्णा</p>", "<p>आयुष</p>"],
                    solution_en: "<p>20.(d) AYUSH is an acronym for Ayurveda, Yoga, and Naturopathy, Unani, Siddha, and Homeopathy.AYUSH sector forms the part of health sector planning.AYUSH was formed on 9th November 2014.</p>",
                    solution_hi: "<p>20.(d) आयुष आयुर्वेद, योग और प्राकृतिक चिकित्सा, यूनानी, सिद्ध और होम्योपैथी के लिए एक संक्षिप्त शब्द है। आयुष क्षेत्र स्वास्थ्य क्षेत्र की योजना का हिस्सा है। आयुष का गठन 9 नवंबर 2014 को किया गया था।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "21",
                    section: "18",
                    question_en: "21. Who was one of the founders of the American computer company Sun Microsystems, later acquired by oracle ?",
                    question_hi: "21. अमेरिकी कंप्यूटर कंपनी सन माइक्रोसिस्टम्स के संस्थापकों में से एक कौन था, जिसे बाद में ओरेकल द्वारा अधिग्रहित कर लिया गया था ?",
                    options_en: [" Vinod Khosla ", " Satya Nadella ", 
                                " Sunder Pichai  ", " Sabeer Bhatia "],
                    options_hi: [" विनोद खोसला", " सत्या नडेला",
                                " सुंदर पिचाई", " सबीर भाटिया"],
                    solution_en: "21.(a) Scott McNealy, Vinod Khosla, Andy Bechtolsheim Bill Joy are the founders of American computer company Sun Microsystems, later acquired by oracle founded on February 24, 1982.",
                    solution_hi: "21.(a) स्कॉट मैकनेली, विनोद खोसला, एंडी बेचटोल्शिम बिल जॉय अमेरिकी कंप्यूटर कंपनी सन माइक्रोसिस्टम्स के संस्थापक हैं, जिन्हें बाद में 24 फरवरी, 1982 को स्थापित ओरेकल द्वारा अधिग्रहित किया गया।",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "22",
                    section: "18",
                    question_en: "22. In which of the following states in India the ‘rat hole mining’ is practised ?",
                    question_hi: "22. भारत में निम्नलिखित में से किस राज्य में \'रैट होल माइनिंग\' की जाती है ?",
                    options_en: [" Gujarat ", " Meghalaya ", 
                                " Maharashtra ", " Jharkhand "],
                    options_hi: [" गुजरात", " मेघालय",
                                " महाराष्ट्र", " झारखंड "],
                    solution_en: "22.(b) The ‘rat-hole mining’ is practiced in Meghalaya. A rat-hole mine involves digging of very small tunnels, usually only 3-4 feet deep, in which workers, more often children, enter and extract coal. ",
                    solution_hi: "22.(b) मेघालय में \'रैट-होल माइनिंग\' प्रचलित है। रैट-होल खदान में बहुत छोटी सुरंग खोदना शामिल है, आमतौर पर केवल 3-4 फीट गहरी, जिसमें श्रमिक, अधिक बार बच्चे, प्रवेश करते हैं और कोयला निकालते हैं।",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "23",
                    section: "18",
                    question_en: "<p>23. Name the drainage pattern where the river originates from a hill and flows in all directions ?</p>",
                    question_hi: "<p>23. उस जल निकासी पैटर्न का नाम बताइए जहां नदी एक पहाड़ी से निकलती है और सभी दिशाओं में बहती है ?</p>",
                    options_en: ["<p>Dendritic</p>", "<p>Centripetal</p>", 
                                "<p>Trellis</p>", "<p>Radial</p>"],
                    options_hi: ["<p>द्रुमाकृतिक</p>", "<p>अभिकेन्द्री</p>",
                                "<p>जाल</p>", "<p>त्रिज्य</p>"],
                    solution_en: "<p>23.(d) Radial is the drainage pattern where the river originates from a hill and flows in all directions. The Dendritic Drainage Pattern was a tree-like trace of the streams on a map that resulted from uniform surface control of the development of stream channels.The centripetal drainage pattern is formed when rivers discharge their waters from all directions into a lake or a depression. For example, Loktak lake in Manipur. Trellis drainage pattern is formed when the primary tributaries of main rivers flow parallel to each other and secondary tributaries join them at right angles.</p>",
                    solution_hi: "<p>23.(d) रेडियल जल निकासी पैटर्न है जहां नदी एक पहाड़ी से निकलती है और सभी दिशाओं में बहती है। डेंड्रिटिक ड्रेनेज पैटर्न एक नक्शे पर धाराओं का एक पेड़ जैसा निशान था जो धारा चैनलों के विकास के समान सतह नियंत्रण के परिणामस्वरूप होता है। सेंट्रिपेटल ड्रेनेज पैटर्न तब बनता है जब नदियाँ अपने पानी को सभी दिशाओं से एक झील या अवसाद में बहा देती हैं। उदाहरण के लिए, मणिपुर में लोकतक झील। ट्रेलिस जल निकासी पैटर्न तब बनता है जब मुख्य नदियों की प्राथमिक सहायक नदियाँ एक दूसरे के समानांतर बहती हैं और द्वितीयक सहायक नदियाँ समकोण पर उनसे जुड़ती हैं।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "24",
                    section: "18",
                    question_en: "24. Which flagship programme under the Ministry of Rural Development aims to organise the rural poor women into their own institutions like self-help groups and their federations, producers, collectives etc and also ensure their financial inclusion and livelihood support ?",
                    question_hi: "24. ग्रामीण विकास मंत्रालय के तहत किस प्रमुख कार्यक्रम का उद्देश्य ग्रामीण गरीब महिलाओं को स्वयं सहायता समूहों और उनके संघों, उत्पादकों, सामूहिक आदि जैसे अपने स्वयं के संस्थानों में संगठित करना और उनके वित्तीय समावेशन और आजीविका सहायता को सुनिश्चित करना है ?",
                    options_en: [" The National Social Assistance Programme(NSAP) ", " Rashtriya Krishi Vikas Yojana ", 
                                " Mahatma Gandhi National Rural Employment Guarantee Programme(MGNREGA) ", " Deendayal Antyodaya Yojana-National Rural Livelihood Mission(DAY-NRLM) "],
                    options_hi: [" राष्ट्रीय सामाजिक सहायता कार्यक्रम (NSAP)", " राष्ट्रीय कृषि विकास योजना ",
                                " महात्मा गांधी राष्ट्रीय ग्रामीण रोजगार गारंटी कार्यक्रम (MGNREGA)", " दीनदयाल अंत्योदय योजना-राष्ट्रीय ग्रामीण आजीविका मिशन (DAY-NRLM) "],
                    solution_en: "24.(d) Deendayal Antyodaya Yojana-National Rural Livelihood Mission (DAY-NRLM) Aajeevika - National Rural Livelihoods Mission (NRLM) was launched by the Ministry of Rural Development (MoRD), Government of India in June 2011. Mahatma Gandhi Employment Guarantee Act 2005, is Indian labor law and social security measure that aims to guarantee the \'right to work\'.  NSAP was launched on 15th August 1995. The RKVY scheme was initiated in 2007 as an umbrella scheme for ensuring the holistic development of agriculture and allied sectors.",
                    solution_hi: "24.(d) दीनदयाल अंत्योदय योजना-राष्ट्रीय ग्रामीण आजीविका मिशन (DAY-NRLM) आजीविका - राष्ट्रीय ग्रामीण आजीविका मिशन (NRLM) ग्रामीण विकास मंत्रालय (MoRD), भारत सरकार द्वारा जून 2011 में शुरू किया गया था। महात्मा गांधी रोजगार गारंटी अधिनियम 2005, भारतीय है श्रम कानून और सामाजिक सुरक्षा उपाय जिसका उद्देश्य \'काम के अधिकार\' की गारंटी देना है। एनएसएपी 15 अगस्त 1995 को शुरू किया गया था। आरकेवीवाई योजना 2007 में कृषि और संबद्ध क्षेत्रों के समग्र विकास को सुनिश्चित करने के लिए एक छत्र योजना के रूप में शुरू की गई थी।",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "25",
                    section: "18",
                    question_en: "<p>25. In February 2019, which communication satellite was launched by ISRO to help in bulk data transfer to telecommunication applications ?</p>",
                    question_hi: "<p>25. फरवरी 2019 में, ISRO द्वारा दूरसंचार अनुप्रयोगों में बल्क डेटा स्थानांतरण में मदद के लिए कौन सा संचार उपग्रह लॉन्च किया गया था ?</p>",
                    options_en: ["<p>GSAT-31</p>", "<p>GSAT-32</p>", 
                                "<p>GSAT-13</p>", "<p>GSAT-30</p>"],
                    options_hi: ["<p>GSAT-31</p>", "<p>GSAT-32</p>",
                                "<p>GSAT-13</p>", "<p>GSAT-30</p>"],
                    solution_en: "<p>25.(a) GSAT 31. The satellite provides advanced telecommunication to the Indian subcontinent. It is used for VSAT networks, television uplinks, digital signage, new gathering, DTH services, and other communication systems.</p>",
                    solution_hi: "<p>25.(a) GSAT 31-उपग्रह भारतीय उपमहाद्वीप को उन्नत दूरसंचार प्रदान करता है। इसका उपयोग VSAT नेटवर्क, टेलीविजन अपलिंक, डिजिटल साइनेज, नई सभा, DTH सेवाओं और अन्य संचार प्रणालियों के लिए किया जाता है।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "26",
                    section: "18",
                    question_en: "26. Name the Shiva temple near Taliparamba in Kerala, where women are allowed to enter only after 8 p.m. <br />A unique feature of this temple is the absence of a flagstaff. ",
                    question_hi: "26. केरल में तालीपरम्बा के पास उस शिव मंदिर का नाम बताइए, जहां महिलाओं को रात 8 बजे के बाद ही प्रवेश करने की अनुमति है। इस मंदिर की एक अनूठी विशेषता एक ध्वजवाहक की अनुपस्थिति है।",
                    options_en: [" Gokarnnatheshwara Temple ", " Rajarajeshwara Temple ", 
                                " Mallikarjun Temple ", " Rameshwaram Mahadev Temple "],
                    options_hi: [" गोकर्णनाथेश्वर मंदिर", " राजराजेश्वर मंदिर",
                                " मल्लिकार्जुन मंदिर", " रामेश्वरम महादेव मंदिर <br /> "],
                    solution_en: "26.(b) Rajarajeshwara Temple is the Shiva temple near Taliparamba in Kerala. The Gokarnanatheshwara Temple is in the Kudroli area of Mangalore in Karnataka, India. Mallikarjuna Temple (also simply known as Srisailam Temple) is a Hindu temple dedicated to the deity Shiva, located at Srisailam in the Indian state of Andhra Pradesh.",
                    solution_hi: "26.(b) राजराजेश्वर मंदिर केरल में तालीपरम्बा के पास शिव मंदिर है। गोकर्णनाथेश्वर मंदिर भारत के कर्नाटक में मैंगलोर के कुद्रोली क्षेत्र में है। मल्लिकार्जुन मंदिर (श्रीशैलम मंदिर के रूप में भी जाना जाता है) एक हिंदू मंदिर है जो भारतीय राज्य आंध्र प्रदेश में श्रीशैलम में स्थित देवता शिव को समर्पित है।",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "27",
                    section: "18",
                    question_en: "<p>27. Who among the following is <strong>NOT </strong>a Cabinet Minister ?</p>",
                    question_hi: "<p>27. निम्नलिखित में से कौन कैबिनेट मंत्री नहीं है ?</p>",
                    options_en: ["<p>Minister of Home Affairs</p>", "<p>Minister of State in the Ministry of Defence</p>", 
                                "<p>Minister of External Affairs</p>", "<p>Minister of Law and Justice</p>"],
                    options_hi: ["<p>गृह मंत्री</p>", "<p>रक्षा मंत्रालय में राज्य मंत्री</p>",
                                "<p>विदेश मंत्री</p>", "<p>कानून और न्याय मंत्री</p>"],
                    solution_en: "<p>27.(b) The Minister of State in the Ministry of Defence is not a cabinet Minister. The Home Ministry is headed by the Union Minister of Home Affairs Amit Shah. The current Minister of External Affairs is Subrahmanyam Jaishankar. Kiren Rijiju is the current Law and Justice Minister of India.</p>",
                    solution_hi: "<p>27.(b) रक्षा मंत्रालय में राज्य मंत्री कैबिनेट मंत्री नहीं है। गृह मंत्रालय का नेतृत्व केंद्रीय गृह मंत्री अमित शाह करते हैं। वर्तमान विदेश मंत्री सुब्रह्मण्यम जयशंकर हैं। किरेन रिजिजू भारत के वर्तमान कानून और न्याय मंत्री हैं।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "28",
                    section: "18",
                    question_en: "28. What is the language spoken by a majority of the people in Lakshadweep ?",
                    question_hi: "28. लक्षद्वीप में अधिकांश लोगों द्वारा बोली जाने वाली भाषा क्या है ?",
                    options_en: [" Malayalam ", " Marathi ", 
                                " Konkani  ", " Kannada  "],
                    options_hi: [" मलयालम", " मराठी",
                                " कोंकणी", " कन्नड़"],
                    solution_en: "28.(a) Malayalam is the language spoken by a majority of the people in Lakshadweep. Marathi (Maharashtra), Konkani(Goa), Kannada (Karnataka).",
                    solution_hi: "28.(a) मलयालम लक्षद्वीप में अधिकांश लोगों द्वारा बोली जाने वाली भाषा है। मराठी (महाराष्ट्र), कोंकणी (गोवा), कन्नड़ (कर्नाटक)।",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "29",
                    section: "18",
                    question_en: "<p>29. At which university did Mahatma Gandhi make his first public appearance in 1916 after returning from South Africa to India ?</p>",
                    question_hi: "<p>29. 1916 में दक्षिण अफ्रीका से भारत लौटने के बाद महात्मा गांधी ने किस विश्वविद्यालय में अपनी पहली सार्वजनिक उपस्थिति दर्ज की ?</p>",
                    options_en: ["<p>Banaras Hindu University</p>", "<p>Aligarh Muslim University</p>", 
                                "<p>Allahabad University</p>", "<p>University of Mumbai</p>"],
                    options_hi: ["<p>बनारस हिंदू विश्वविद्यालय</p>", "<p>अलीगढ़ मुस्लिम विश्वविद्यालय</p>",
                                "<p>इलाहाबाद विश्वविद्यालय</p>", "<p>मुंबई विश्वविद्यालय</p>"],
                    solution_en: "<p>29.(a) At Banaras Hindu University Mahatma Gandhi made his first public appearance in 1916 after returning from South Africa to India. Aligarh Muslim University(1875) was founded by Sayed Ahmad Khan. BHU was founded by Madan Mohan Malaviya, Annie Besant, Sunder Lal.</p>",
                    solution_hi: "<p>29.(a) बनारस हिंदू विश्वविद्यालय में महात्मा गांधी ने दक्षिण अफ्रीका से भारत लौटने के बाद 1916 में अपनी पहली सार्वजनिक उपस्थिति दर्ज की। अलीगढ़ मुस्लिम विश्वविद्यालय (1875) की स्थापना सैयद अहमद खान ने की थी। बीएचयू की स्थापना मदन मोहन मालवीय, एनी बेसेंट, सुंदर लाल ने की थी।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "30",
                    section: "18",
                    question_en: "30. Name the German Chemist who grouped elements into triads in 1817 ?",
                    question_hi: "30. उस जर्मन रसायनज्ञ का नाम बताइए जिसने 1817 में तत्वों को त्रय में समूहित किया था ?",
                    options_en: [" Dmitri Ivanovich Mendeleev ", " John Newlands ", 
                                " Johann Wolfganag Dobereiner ", " Henry Moseley "],
                    options_hi: [" दिमित्री इवानोविच मेंडेलीव", " जॉन न्यूलैंड्स",
                                " जोहान वोल्फगनाग डोबेराइनर", " हेनरी मोसले"],
                    solution_en: "30.(c) Johann Wolfgang Dobereiner the German Chemist who grouped elements into triads in 1817. Henry Moseley, in 1913 he used self-built equipment to prove that every element\'s identity is uniquely determined by the number of protons it has. John Newlands found that every eight elements had similar properties and called this the law of octaves. Dmitri Mendeleev is often referred to as the Father of the Periodic Table. ",
                    solution_hi: "30.(c) जर्मन रसायनज्ञ जोहान वोल्फगैंग डोबेराइनर, जिन्होंने 1817 में तत्वों को त्रय में समूहित किया। हेनरी मोसले ने 1913 में यह साबित करने के लिए स्व-निर्मित उपकरणों का उपयोग किया कि प्रत्येक तत्व की पहचान विशिष्ट रूप से उसके पास मौजूद प्रोटॉन की संख्या से निर्धारित होती है। जॉन न्यूलैंड्स ने पाया कि प्रत्येक आठ तत्वों में समान गुण होते हैं और इसे सप्तक का नियम कहते हैं। दिमित्री मेंडेलीव को अक्सर आवर्त सारणी के पिता के रूप में जाना जाता है।",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "31",
                    section: "18",
                    question_en: "<p>31. In 1930, who organized the Dalits into the Depressed Classes Association and demanded separate electorates for them ?</p>",
                    question_hi: "<p>31. 1930 में दलितों को दलित वर्ग संघ में किसने संगठित किया और उनके लिए पृथक निर्वाचक मंडल की मांग की ?</p>",
                    options_en: ["<p>Abdul Gaffar Khan</p>", "<p>Jawahar Lal Nehru</p>", 
                                "<p>Mahatma Gandhi</p>", "<p>BR Ambedkar</p>"],
                    options_hi: ["<p>अब्दुल गफ्फार खान</p>", "<p>जवाहर लाल नेहरू</p>",
                                "<p>महात्मा गांधी</p>", "<p>बी.आर. अम्बेडकर</p>"],
                    solution_en: "<p>31.(d) In 1930, BR Ambedkar Organised the Dalits into the Depressed Classes Association and demanded separate electorates for them. B.R Ambedkar is recognized as the Father of the Constitution of India.</p>",
                    solution_hi: "<p>31.(d) 1930 में, बी.आर. अंबेडकर ने दलितों को दलित वर्ग संघ में संगठित किया और उनके लिए अलग निर्वाचक मंडल की मांग की। बी.आर. अंबेडकर को भारत के संविधान के पिता के रूप में मान्यता प्राप्त है।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "32",
                    section: "18",
                    question_en: "32. Which Indian state has declared Malkhamb as its state sport ?",
                    question_hi: "32. किस भारतीय राज्य ने मल्लखंब को अपना राज्य खेल घोषित किया है ?",
                    options_en: [" Madhya Pradesh ", " Uttar Pradesh  ", 
                                " Haryana ", " Uttarakhand  "],
                    options_hi: [" मध्य प्रदेश", " उत्तर प्रदेश",
                                " हरियाणा", " उत्तराखंड"],
                    solution_en: "32.(a) The name Mallakhamb derived from the term Malla, meaning wrestler, and Khamb, which means a pole. On April 9, 2013, the Indian state of Madhya Pradesh declared Mallakhamba the state sport.",
                    solution_hi: "32.(a) मल्लखंब नाम मल्ल शब्द से लिया गया है, जिसका अर्थ है पहलवान, और खंब, जिसका अर्थ है एक पोल। 9 अप्रैल, 2013 को, भारतीय राज्य मध्य प्रदेश ने मल्लखंबा को राज्य का खेल घोषित किया।",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "33",
                    section: "18",
                    question_en: "<p>33. Which of the following is a property of an ionic compound ?</p>",
                    question_hi: "<p>33. निम्नलिखित में से कौन एक आयनिक यौगिक का गुण है ?</p>",
                    options_en: ["<p>It is hard and does not break easily.</p>", "<p>It has a high melting point and boiling point.</p>", 
                                "<p>It is soluble in solvents such as kerosene and petrol</p>", "<p>It conducts electricity in the solid-state</p>"],
                    options_hi: ["<p>यह कठिन है और आसानी से नहीं टूटता</p>", "<p>इसका गलनांक और क्वथनांक उच्च होता है।</p>",
                                "<p>यह केरोसिन और पेट्रोल जैसे विलायक में घुलनशील है</p>", "<p>यह ठोस अवस्था में विद्युत का संचालन करता है</p>"],
                    solution_en: "<p>33.(b) Properties of an ionic compound, are usually crystalline solids. They have high melting points and high boiling points. They are usually soluble in water but insoluble in organic solvents. They conduct electricity when dissolved in water or when melted.</p>",
                    solution_hi: "<p>33.(b) एक आयनिक यौगिक के गुण, आमतौर पर क्रिस्टलीय ठोस होते हैं। उनके उच्च गलनांक और उच्च क्वथनांक होते हैं। वे आमतौर पर पानी में घुलनशील होते हैं लेकिन कार्बनिक सॉल्वैंट्स में अघुलनशील होते हैं। वे पानी में घुलने पर या पिघलने पर बिजली का संचालन करते हैं।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "34",
                    section: "18",
                    question_en: "34. From where did India’s Polar Satellite Launch Vehicle (PSLV-C45) successfully launch EMISAT and 28 international other customer satellites on 1st April 2019 ?<br /> ",
                    question_hi: "34. 1 अप्रैल, 2019 को भारत के ध्रुवीय उपग्रह प्रक्षेपण यान (PSLV-C45) ने EMISAT और 28 अन्य अंतर्राष्ट्रीय ग्राहक उपग्रहों को सफलतापूर्वक कहाँ से लॉन्च किया ?",
                    options_en: [" Spaceport in French Guiana ", " Satish Dhawan Space Centre ", 
                                " UR Rao Satellite Centre ", " Centre Spatial Guyanais, Kourou "],
                    options_hi: [" फ्रेंच गयाना में स्पेसपोर्ट", " सतीश धवन अंतरिक्ष केंद्र",
                                " यूआर राव सैटेलाइट सेंटर", " केंद्र स्थानिक गुयानाइस, कौरौस "],
                    solution_en: "34.(b) From Satish Dhawan Space Centre India’s Polar Satellite Launch Vehicle (PSLV-C45) successfully launched EMISAT and 28 international customer satellites on 1st April 2019. EMISAT (Electromagnetic Intelligence-gathering Satellite) EMISAT is a minisatellite with a mass of 436 kg designed and developed at ISRO (Indian Space Research Organization) for India\'s DRDO (Defence Research and Development Organization).",
                    solution_hi: "34.(b) सतीश धवन अंतरिक्ष केंद्र से भारत के ध्रुवीय उपग्रह प्रक्षेपण यान (PSLV-C45) ने 1 अप्रैल 2019 को EMISAT और 28 अंतर्राष्ट्रीय ग्राहक उपग्रहों को सफलतापूर्वक लॉन्च किया। EMISAT (इलेक्ट्रोमैग्नेटिक इंटेलिजेंस-गैदरिंग सैटेलाइट) EMISAT 436 किलोग्राम के द्रव्यमान वाला एक छोटा उपग्रह है जिसे डिजाइन और विकसित किया गया है। भारत के DRDO (रक्षा अनुसंधान और विकास संगठन) के लिए ISRO (भारतीय अंतरिक्ष अनुसंधान संगठन)।",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "35",
                    section: "18",
                    question_en: "<p>35. The Nice Guy Who finished First is the biography of which famous sportsperson ?</p>",
                    question_hi: "<p>35. द गुड गाइ हू फिनिश्ड फर्स्ट (The nice Guy Who finished First) किस प्रसिद्ध खिलाड़ी की जीवनी है ?</p>",
                    options_en: ["<p>Rahul Dravid</p>", "<p>David Beckham</p>", 
                                "<p>Michael Phelps</p>", "<p>Tiger Woods</p>"],
                    options_hi: ["<p>राहुल द्रविड़</p>", "<p>डेविड बेकहम</p>",
                                "<p>माइकल फेल्प्स</p>", "<p>टाइगर वुड्स</p>"],
                    solution_en: "<p>35.(a) The Nice Guy Who Finished First is the remarkable story of Rahul Sharad Dravid. David Beckham: My Side is the biography of David Beckham. Michael Phelps most Olympic gold medal record.No Limits: The Will to Succeed is an autobiography written by Michael Phelps with Alan Abrahamson. Tiger Woods is a 2018 biography of professional golfer Tiger Woods written by Jeff Benedict and Armen Keteyian.</p>",
                    solution_hi: "<p>35.(a) द नाइस गाइ हू फिनिश्ड फर्स्ट राहुल शरद द्रविड़ की उल्लेखनीय कहानी है<br>डेविड बेकहम: माई साइड डेविड बेकहम की जीवनी है। माइकल फेल्प्स ने सर्वाधिक ओलंपिक स्वर्ण पदक रिकॉर्ड बनाया। कोई सीमा नहीं: सफल होने की इच्छा माइकल फेल्प्स द्वारा एलन अब्राहमसन के साथ लिखी गई एक आत्मकथा है। टाइगर वुड्स पेशेवर गोल्फर टाइगर वुड्स की 2018 की जीवनी है, जिसे जेफ बेनेडिक्ट और आर्मेन केतियन ने लिखा है।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "36",
                    section: "18",
                    question_en: "<p>36. From which constitution has the Fundamental Rights in the Indian Constitution drawn ?</p>",
                    question_hi: "<p>36. भारतीय संविधान में मौलिक अधिकार किस संविधान से लिया गया है ?</p>",
                    options_en: ["<p>Britain</p>", "<p>Switzerland</p>", 
                                "<p>United States</p>", "<p>Soviet Union</p>"],
                    options_hi: ["<p>ब्रिटेन</p>", "<p>स्विट्ज़रलैंड</p>",
                                "<p>संयुक्त राज्य अमेरिका</p>", "<p>सोवियत संघ</p>"],
                    solution_en: "<p>36.(c) Features borrowed from: <br><strong>USA- </strong>Impeachment of the president, Functions of president and vice-president, Removal of Supreme Court and High court judges, Fundamental Rights, Judicial review, Independence of judiciary, Preamble of the constitution. <br><strong>Britain- </strong>1. Parliamentary government 2. Rule of Law 3. Legislative procedure 4. Single citizenship 5. Cabinet system 6. Prerogative writs 7. Parliamentary privileges 8. Bicameralism.</p>\n<p><strong>USSR</strong>-Fundamental duties Ideals of justice (social, economic, and political) in the Preamble.</p>",
                    solution_hi: "<p>36.(c) उधार ली गई विशेषताएं:<br><strong>संयुक्त राज्य अमेरिका-</strong> राष्ट्रपति का महाभियोग, राष्ट्रपति और उपाध्यक्ष के कार्य, सर्वोच्च न्यायालय और उच्च न्यायालय के न्यायाधीशों को हटाना, मौलिक अधिकार, न्यायिक समीक्षा, न्यायपालिका की स्वतंत्रता, संविधान की प्रस्तावना।<br><strong>ब्रिटेन- </strong>1. संसदीय सरकार 2. कानून का शासन 3. विधायी प्रक्रिया 4. एकल नागरिकता 5. कैबिनेट प्रणाली 6. विशेषाधिकार रिट 7. संसदीय विशेषाधिकार 8. द्विसदनीयता। <br><strong>USSR </strong>- मौलिक कर्तव्य प्रस्तावना में न्याय के आदर्श (सामाजिक, आर्थिक और राजनीतिक)।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "37",
                    section: "18",
                    question_en: "37. On heating gypsum at 373 k, it loses water molecules and becomes calcium sulphate hemihydrate. This substance is used to make toys, materials for decoration, and smooth surfaces. <br />What is the substance commonly known as ?",
                    question_hi: "37. जिप्सम को 373 k पर गर्म करने पर यह पानी के अणुओं को अवमुक्त करता  है और कैल्शियम सल्फेट हेमीहाइड्रेट बन जाता है। इस पदार्थ का उपयोग खिलौने, सजावट के लिए सामग्री और चिकनी सतहों को बनाने के लिए किया जाता है। पदार्थ को सामान्यतः किस नाम से जाना जाता है ?",
                    options_en: [" Cement ", " Alabaster ", 
                                " Plaster of Paris ", " Clay "],
                    options_hi: [" सीमेंट", " अलाबस्टर",
                                " प्लास्टर ऑफ पेरिस", " मिट्टी"],
                    solution_en: "37.(c) Plaster of Paris is prepared by heating calcium sulfate dihydrate or gypsum. Plaster of Paris is used to make sculptures and metal castings used as decorative in buildings. It is also used in buildings to avoid fire hazards as Plaster of Paris is fire resistant. It is used as a coating on wood and metal structures to avoid any fire accidents.",
                    solution_hi: "37.(c) कैल्शियम सल्फेट डाइहाइड्रेट या जिप्सम को गर्म करके प्लास्टर ऑफ पेरिस तैयार किया जाता है। प्लास्टर ऑफ पेरिस का उपयोग इमारतों में सजावटी के रूप में उपयोग की जाने वाली मूर्तियां और धातु की ढलाई बनाने के लिए किया जाता है। इसका उपयोग इमारतों में आग के खतरों से बचने के लिए भी किया जाता है क्योंकि प्लास्टर ऑफ पेरिस आग प्रतिरोधी है। यह किसी भी आग दुर्घटना से बचने के लिए लकड़ी और धातु संरचनाओं पर एक कोटिंग के रूप में प्रयोग किया जाता है।",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "38",
                    section: "18",
                    question_en: "38. The National TB Programme (NTP) was launched by the Government of India in year____in the form of a district TB Centre Model involved with BCG Vaccination and TB Treatment. ",
                    question_hi: "38. राष्ट्रीय TB कार्यक्रम (NTP) भारत सरकार द्वारा वर्ष 1962 में शुरू किया गया था। BCG टीकाकरण और TB उपचार से जुड़े एक जिला टीबी केंद्र मॉडल के रूप में।",
                    options_en: [" 1962", " 1963", 
                                " 1961", " 1960"],
                    options_hi: [" 1962", " 1963",
                                " 1961", " 1960"],
                    solution_en: "38.(a) The main strategy of the NTP is the Directly Observed Short Course (DOTS).Bacille Calmette-Guérin (BCG) is a vaccine for tuberculosis (TB) disease.The National TB Programme (NTP) was launched by the Government of India in the year 1962.",
                    solution_hi: "38.(a) NTP की मुख्य रणनीति डायरेक्टली ऑब्जर्व्ड शॉर्ट कोर्स (DOTS) है। बेसिल कैलमेट-गुएरिन (BCG) तपेदिक (TB) रोग के लिए एक टीका है। राष्ट्रीय TB कार्यक्रम (NTP) भारत सरकार द्वारा वर्ष 1962 में शुरू किया गया था।  ",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "39",
                    section: "18",
                    question_en: "<p>39. Which place in India was known as &lsquo;kala pani&rsquo; ?</p>",
                    question_hi: "<p>39. भारत में किस स्थान को \'काला पानी\' के नाम से जाना जाता था ?</p>",
                    options_en: ["<p>Gulf of Kutch</p>", "<p>Kerala&rsquo;s backwaters</p>", 
                                "<p>Lakshadweep</p>", "<p>Andaman Islands</p>"],
                    options_hi: ["<p>कच्छ की खाड़ी</p>", "<p>केरल अनूपझीलें</p>",
                                "<p>लक्षद्वीप</p>", "<p>अंडमान द्वीप समूह</p>"],
                    solution_en: "<p>39.(d) Andaman Islands in India was known as &lsquo;Kala pani&rsquo;. The Cellular Jail, also known as Kala Pani, was a colonial prison in the Andaman and Nicobar Islands, India.</p>",
                    solution_hi: "<p>39.(d) भारत में अंडमान द्वीप समूह को \'काला पानी\' के नाम से जाना जाता था। सेलुलर जेल, जिसे काला पानी के नाम से भी जाना जाता है, भारत के अंडमान और निकोबार द्वीप समूह में एक औपनिवेशिक जेल थी।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "40",
                    section: "misc",
                    question_en: "40. Where is the office of the United Nation Environment Programme (UNEP) located in India ?",
                    question_hi: "40. भारत में संयुक्त राष्ट्र पर्यावरण कार्यक्रम (UNEP) का कार्यालय कहाँ स्थित है ?",
                    options_en: [" Chennai ", " Banglore ", 
                                " Mumbai", " New Delhi  "],
                    options_hi: [" चेन्नई", " बैंगलोर",
                                " मुंबई ", " नई दिल्ली<br /> "],
                    solution_en: "40.(d) The office of the United Nation Environment Programme (UNEP) is located in India in New Delhi. UNEP Headquarter is in Nairobi, Kenya. It was founded on 5 June 1972.",
                    solution_hi: "40.(d) संयुक्त राष्ट्र पर्यावरण कार्यक्रम (UNEP) का कार्यालय भारत में नई दिल्ली में स्थित है। UNEP का मुख्यालय नैरोबी, केन्या में है। इसकी स्थापना 5 जून 1972 को हुई थी।",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>