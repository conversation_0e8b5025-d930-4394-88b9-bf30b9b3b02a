<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">20:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 15</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">15</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 20 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 0,
                end: 13
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 14,
                end: 14
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "17",
                    question_en: "<p>1. <span style=\"font-family: Cambria Math;\">8 girls and 12 boys can complete a piece of work in 4 days, while 10 girls and 6 boys can complete it in 6 days. How long will 5 girls and 3 boys take to complete this same piece of work?</span></p>\n",
                    question_hi: "<p>1. <span style=\"font-family: Cambria Math;\">8 </span><span style=\"font-family: Kokila;\">&#2354;&#2337;&#2364;&#2325;&#2367;&#2351;&#2366;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> 12 </span><span style=\"font-family: Kokila;\">&#2354;&#2337;&#2364;&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2366;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> 4 </span><span style=\"font-family: Kokila;\">&#2342;&#2367;&#2344;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2370;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2325;&#2340;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;&#2306;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Kokila;\">&#2332;&#2348;&#2325;&#2367;</span><span style=\"font-family: Cambria Math;\"> 10 </span><span style=\"font-family: Kokila;\">&#2354;&#2337;&#2364;&#2325;&#2367;&#2351;&#2366;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> 6 </span><span style=\"font-family: Kokila;\">&#2354;&#2337;&#2364;&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2311;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> 6 </span><span style=\"font-family: Kokila;\">&#2342;&#2367;&#2344;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2370;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2325;&#2340;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;&#2306;</span><span style=\"font-family: Cambria Math;\">. 5 </span><span style=\"font-family: Kokila;\">&#2354;&#2337;&#2364;&#2325;&#2367;&#2351;&#2366;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> 3 </span><span style=\"font-family: Kokila;\">&#2354;&#2337;&#2364;&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2311;&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2366;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2370;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2352;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2367;&#2340;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2350;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2354;&#2375;&#2306;&#2327;&#2375;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>12 days</p>\n", "<p><span style=\"font-family: Cambria Math;\">14 days</span></p>\n", 
                                "<p><span style=\"font-family: Cambria Math;\">11 days</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">13 days</span></p>\n"],
                    options_hi: ["<p>12 <span style=\"font-family: Kokila;\">&#2342;&#2367;&#2344;</span><span style=\"font-family: Cambria Math;\">&nbsp;</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">14 </span><span style=\"font-family: Kokila;\">&#2342;&#2367;&#2344;</span></p>\n",
                                "<p><span style=\"font-family: Cambria Math;\"> 11 </span><span style=\"font-family: Kokila;\">&#2342;&#2367;&#2344;</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">13 </span><span style=\"font-family: Kokila;\">&#2342;&#2367;&#2344;</span></p>\n"],
                    solution_en: "<p>1.(a)</p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>A</mi><mi>c</mi><mi>c</mi><mi>o</mi><mi>r</mi><mi>d</mi><mi>i</mi><mi>n</mi><mi>g</mi><mo>&nbsp;</mo><mi>t</mi><mi>o</mi><mo>&nbsp;</mo><mi>t</mi><mi>h</mi><mi>e</mi><mo>&nbsp;</mo><mi>q</mi><mi>u</mi><mi>e</mi><mi>s</mi><mi>t</mi><mi>i</mi><mi>o</mi><mi>n</mi><mo>,</mo><mspace linebreak=\"newline\"></mspace><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>(</mo><mn>8</mn><mi>G</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>12</mn><mi>B</mi><mo>)</mo><mo>&times;</mo><mn>4</mn><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mo>(</mo><mn>10</mn><mi>G</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>6</mn><mi>B</mi><mo>)</mo><mo>&times;</mo><mn>6</mn><mspace linebreak=\"newline\"></mspace><mo>&rArr;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mn>32</mn><mi>G</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>48</mn><mi>B</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>60</mn><mi>G</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>36</mn><mi>B</mi><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mo>&rArr;</mo><mn>48</mn><mi>B</mi><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>36</mn><mi>B</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>60</mn><mi>G</mi><mo>&nbsp;</mo><mo>-</mo><mn>32</mn><mi>G</mi><mspace linebreak=\"newline\"></mspace><mo>&rArr;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mn>12</mn><mi>B</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>28</mn><mi>G</mi><mo>&nbsp;</mo><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mo>&nbsp;</mo><mo>&nbsp;</mo><mfrac><mrow><mo>&nbsp;</mo><mi>B</mi></mrow><mi>G</mi></mfrac><mo>&nbsp;</mo><mo>=</mo><mfrac><mrow><mo>&nbsp;</mo><mn>7</mn></mrow><mn>3</mn></mfrac><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mi>S</mi><mi>o</mi><mo>,</mo><mo>&nbsp;</mo><mn>5</mn><mo>&nbsp;</mo><mi>g</mi><mi>i</mi><mi>r</mi><mi>l</mi><mi>s</mi><mo>&nbsp;</mo><mi>a</mi><mi>n</mi><mi>d</mi><mo>&nbsp;</mo><mn>3</mn><mo>&nbsp;</mo><mi>b</mi><mi>o</mi><mi>y</mi><mi>s</mi><mo>&nbsp;</mo><mi>w</mi><mi>i</mi><mi>l</mi><mi>l</mi><mo>&nbsp;</mo><mi>c</mi><mi>o</mi><mi>m</mi><mi>p</mi><mi>l</mi><mi>e</mi><mi>t</mi><mi>e</mi><mo>&nbsp;</mo><mi>w</mi><mi>o</mi><mi>r</mi><mi>k</mi><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&rArr;</mo><mo>&nbsp;</mo><mfrac><mrow><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>(</mo><mn>8</mn><mo>&times;</mo><mn>3</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>12</mn><mo>&times;</mo><mn>7</mn><mo>)</mo><mo>&times;</mo><mn>4</mn></mrow><mrow><mn>5</mn><mo>&times;</mo><mn>3</mn><mo>+</mo><mn>3</mn><mo>&times;</mo><mn>7</mn></mrow></mfrac><mo>&nbsp;</mo><mo>=</mo><mfrac><mrow><mn>108</mn><mo>&times;</mo><mn>4</mn></mrow><mn>36</mn></mfrac><mo>&nbsp;</mo><mo>=</mo><mn>12</mn><mo>&nbsp;</mo><mi>d</mi><mi>a</mi><mi>y</mi><mi>s</mi></math></p>\n",
                    solution_hi: "<p>1.(a)</p>\r\n<p><math style=\"font-family: Verdana;\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#2346;&#2381;&#2352;&#2358;&#2381;&#2344;</mi><mo>&nbsp;</mo><mi>&#2325;&#2375;</mi><mo>&nbsp;</mo><mi>&#2309;&#2344;&#2369;&#2360;&#2366;&#2352;</mi><mo>,</mo><mspace linebreak=\"newline\"></mspace><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>(</mo><mn>8</mn><mi>G</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>12</mn><mi>B</mi><mo>)</mo><mo>&times;</mo><mn>4</mn><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mo>(</mo><mn>10</mn><mi>G</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>6</mn><mi>B</mi><mo>)</mo><mo>&times;</mo><mn>6</mn><mspace linebreak=\"newline\"></mspace><mo>&rArr;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mn>32</mn><mi>G</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>48</mn><mi>B</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>60</mn><mi>G</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>36</mn><mi>B</mi><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mo>&rArr;</mo><mn>48</mn><mi>B</mi><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>36</mn><mi>B</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>60</mn><mi>G</mi><mo>&nbsp;</mo><mo>-</mo><mn>32</mn><mi>G</mi><mspace linebreak=\"newline\"></mspace><mo>&rArr;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mn>12</mn><mi>B</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>28</mn><mi>G</mi><mo>&nbsp;</mo><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mo>&nbsp;</mo><mo>&nbsp;</mo><mfrac><mrow><mo>&nbsp;</mo><mi>B</mi></mrow><mi>G</mi></mfrac><mo>&nbsp;</mo><mo>=</mo><mfrac><mrow><mo>&nbsp;</mo><mn>7</mn></mrow><mn>3</mn></mfrac><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mi>&#2311;&#2360;&#2354;&#2367;&#2319;</mi><mo>,</mo><mo>&nbsp;</mo><mn>5</mn><mo>&nbsp;</mo><mi>&#2354;&#2337;&#2364;&#2325;&#2367;&#2351;&#2366;&#2306;</mi><mo>&nbsp;</mo><mi>&#2324;&#2352;</mi><mo>&nbsp;</mo><mn>3</mn><mo>&nbsp;</mo><mi>&#2354;&#2337;&#2364;&#2325;&#2375;</mi><mo>&nbsp;</mo><mi>&#2325;&#2366;&#2350;</mi><mo>&nbsp;</mo><mi>&#2346;&#2370;&#2352;&#2366;</mi><mo>&nbsp;</mo><mi>&#2325;&#2352;&#2375;&#2306;&#2327;&#2375;</mi><mspace linebreak=\"newline\"></mspace><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&rArr;</mo><mo>&nbsp;</mo><mfrac><mrow><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>(</mo><mn>8</mn><mo>&times;</mo><mn>3</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>12</mn><mo>&times;</mo><mn>7</mn><mo>)</mo><mo>&times;</mo><mn>4</mn></mrow><mrow><mn>5</mn><mo>&times;</mo><mn>3</mn><mo>+</mo><mn>3</mn><mo>&times;</mo><mn>7</mn></mrow></mfrac><mo>&nbsp;</mo><mo>=</mo><mfrac><mrow><mn>108</mn><mo>&times;</mo><mn>4</mn></mrow><mn>36</mn></mfrac><mo>&nbsp;</mo><mo>=</mo><mn>12</mn><mo>&nbsp;</mo><mi>d</mi><mi>a</mi><mi>y</mi><mi>s</mi></math></p>\r\n<p>&nbsp;</p>\n",
                    correct: "a",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "2",
                    section: "17",
                    question_en: "<p>2. <span style=\"font-family: Cambria Math;\">A camp of 250 soldiers has food for 600 days. After 60 days, 50 soldiers leave the camp. For how many days will the remaining food last from the time that these 50 soldiers left?</span></p>\n",
                    question_hi: "<p>2. <span style=\"font-family: Cambria Math;\">250 </span><span style=\"font-family: Kokila;\">&#2360;&#2376;&#2344;&#2367;&#2325;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2376;&#2306;&#2346;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> 600 </span><span style=\"font-family: Kokila;\">&#2342;&#2367;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2326;&#2366;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2379;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> 60 </span><span style=\"font-family: Kokila;\">&#2342;&#2367;&#2344;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2348;&#2366;&#2342;</span><span style=\"font-family: Cambria Math;\">, 50 </span><span style=\"font-family: Kokila;\">&#2360;&#2376;&#2344;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2358;&#2367;&#2357;&#2367;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2331;&#2379;&#2337;&#2364;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2342;&#2375;&#2340;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;&#2306;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2311;&#2344;</span><span style=\"font-family: Cambria Math;\"> 50 </span><span style=\"font-family: Kokila;\">&#2360;&#2376;&#2344;&#2367;&#2325;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2330;&#2354;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2332;&#2366;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2348;&#2366;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2348;&#2330;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2369;&#2310;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2349;&#2379;&#2332;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2367;&#2340;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2342;&#2367;&#2344;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2340;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2330;&#2354;&#2375;&#2327;&#2366;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>725 days<span style=\"font-family: Cambria Math;\">&nbsp;</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">750 days</span></p>\n", 
                                "<p><span style=\"font-family: Cambria Math;\">675 days</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">650 days</span></p>\n"],
                    options_hi: ["<p>725 <span style=\"font-family: Kokila;\">&#2342;&#2367;&#2344;</span></p>\n", "<p>750 <span style=\"font-family: Kokila;\">&#2342;&#2367;&#2344;</span></p>\n",
                                "<p>675 <span style=\"font-family: Kokila;\">&#2342;&#2367;&#2344;</span></p>\n", "<p>650 <span style=\"font-family: Kokila;\">&#2342;&#2367;&#2344;</span></p>\n"],
                    solution_en: "<p>2.(c)<span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>A</mi><mi>c</mi><mi>c</mi><mi>o</mi><mi>r</mi><mi>d</mi><mi>i</mi><mi>n</mi><mi>g</mi><mo>&nbsp;</mo><mi>t</mi><mi>o</mi><mo>&nbsp;</mo><mi>t</mi><mi>h</mi><mi>e</mi><mo>&nbsp;</mo><mi>q</mi><mi>u</mi><mi>e</mi><mi>s</mi><mi>t</mi><mi>i</mi><mi>o</mi><mi>n</mi><mo>,</mo><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mo>&nbsp;</mo><mi>F</mi><mi>o</mi><mi>o</mi><mi>d</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>250</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>600</mn><mo>&nbsp;</mo><mo>=</mo><mn>150000</mn><mo>&nbsp;</mo><mi>u</mi><mi>n</mi><mi>i</mi><mi>t</mi><mspace linebreak=\"newline\"></mspace><mo>&nbsp;</mo><mi>A</mi><mi>f</mi><mi>t</mi><mi>e</mi><mi>r</mi><mo>&nbsp;</mo><mn>60</mn><mo>&nbsp;</mo><mi>d</mi><mi>a</mi><mi>y</mi><mi>s</mi><mo>,</mo><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mi>R</mi><mi>e</mi><mi>m</mi><mi>a</mi><mi>i</mi><mi>n</mi><mi>i</mi><mi>n</mi><mi>g</mi><mo>&nbsp;</mo><mi>f</mi><mi>o</mi><mi>o</mi><mi>d</mi><mo>,</mo><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mn>150000</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>250</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>60</mn><mo>&nbsp;</mo><mo>=</mo><mn>150000</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>15000</mn><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&rArr;</mo><mo>&nbsp;</mo><mn>135000</mn><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mo>&nbsp;</mo><mo>&nbsp;</mo><mi>D</mi><mi>a</mi><mi>y</mi><mi>s</mi><mo>&nbsp;</mo><mi>l</mi><mi>e</mi><mi>f</mi><mi>t</mi><mo>&nbsp;</mo><mi>f</mi><mi>o</mi><mi>r</mi><mo>&nbsp;</mo><mi>f</mi><mi>o</mi><mi>o</mi><mi>d</mi><mo>&nbsp;</mo><mi>c</mi><mi>o</mi><mi>n</mi><mi>s</mi><mi>u</mi><mi>m</mi><mi>p</mi><mi>t</mi><mi>i</mi><mi>o</mi><mi>n</mi><mo>,</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mo>&rArr;</mo><mo>&nbsp;</mo><mfrac><mrow><mo>&nbsp;</mo><mo>&nbsp;</mo><mn>135000</mn></mrow><mn>200</mn></mfrac><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>675</mn><mo>&nbsp;</mo><mi>d</mi><mi>a</mi><mi>y</mi><mi>s</mi></math></p>\n",
                    solution_hi: "<p>2.(c)</p>\r\n<p><math style=\"font-family: Verdana;\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#2346;&#2381;&#2352;&#2358;&#2381;&#2344;</mi><mo>&nbsp;</mo><mi>&#2325;&#2375;</mi><mo>&nbsp;</mo><mi>&#2309;&#2344;&#2369;&#2360;&#2366;&#2352;</mi><mo>,</mo><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mo>&nbsp;</mo><mi>&#2349;&#2379;&#2332;&#2344;</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>250</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>600</mn><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>150000</mn><mo>&nbsp;</mo><mi>&#2311;&#2325;&#2366;&#2312;</mi><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mn>60</mn><mo>&nbsp;</mo><mi>&#2342;&#2367;&#2344;&#2379;&#2306;</mi><mo>&nbsp;</mo><mi>&#2325;&#2375;</mi><mo>&nbsp;</mo><mi>&#2348;&#2366;&#2342;</mi><mo>,</mo><mo>&nbsp;</mo><mi>&#2348;&#2330;&#2366;</mi><mo>&nbsp;</mo><mi>&#2361;&#2369;&#2310;</mi><mo>&nbsp;</mo><mi>&#2326;&#2366;&#2344;&#2366;</mi><mo>,</mo><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mn>150000</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>250</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>60</mn><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>150000</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>15000</mn><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>=</mo><mn>135000</mn><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mi>&#2349;&#2379;&#2332;&#2344;</mi><mo>&nbsp;</mo><mi>&#2325;&#2375;</mi><mo>&nbsp;</mo><mi>&#2326;&#2346;&#2340;</mi><mo>&nbsp;</mo><mi>&#2361;&#2379;&#2344;&#2375;</mi><mo>&nbsp;</mo><mi>&#2325;&#2375;</mi><mo>&nbsp;</mo><mi>&#2358;&#2375;&#2359;</mi><mo>&nbsp;</mo><mi>&#2342;&#2367;&#2344;</mi><mo>,</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mo>&nbsp;</mo><mfrac><mrow><mo>&nbsp;</mo><mo>&nbsp;</mo><mn>135000</mn></mrow><mn>200</mn></mfrac><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>675</mn><mo>&nbsp;</mo><mi>&#2342;&#2367;&#2344;</mi></math></p>\r\n<p>&nbsp;</p>\n",
                    correct: "c",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "3",
                    section: "17",
                    question_en: "<p>3.<span style=\"font-family: Cambria Math;\"> A work can be completed by N men in 15 days. 5 men leave the Work after 3 days. The remaining work was completed in 18 days. What is the value of N?</span></p>\n",
                    question_hi: "<p>3. <span style=\"font-family: Kokila;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2366;&#2352;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> N </span><span style=\"font-family: Kokila;\">&#2346;&#2369;&#2352;&#2369;&#2359;</span><span style=\"font-family: Cambria Math;\"> 15 </span><span style=\"font-family: Kokila;\">&#2342;&#2367;&#2344;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2370;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2325;&#2340;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;&#2306;&#2404;</span><span style=\"font-family: Cambria Math;\"> 5 </span><span style=\"font-family: Kokila;\">&#2310;&#2342;&#2350;&#2368;</span><span style=\"font-family: Cambria Math;\"> 3 </span><span style=\"font-family: Kokila;\">&#2342;&#2367;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2348;&#2366;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2366;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2331;&#2379;&#2337;&#2364;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2342;&#2375;&#2340;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;&#2306;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2358;&#2375;&#2359;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2366;&#2352;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> 18 </span><span style=\"font-family: Kokila;\">&#2342;&#2367;&#2344;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2370;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2327;&#2351;&#2366;&#2404;</span><span style=\"font-family: Cambria Math;\"> N </span><span style=\"font-family: Kokila;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>18</p>\n", "<p><span style=\"font-family: Cambria Math;\">21</span></p>\n", 
                                "<p><span style=\"font-family: Cambria Math;\">15</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">12</span></p>\n"],
                    options_hi: ["<p>18</p>\n", "<p><span style=\"font-family: Cambria Math;\">21</span></p>\n",
                                "<p><span style=\"font-family: Cambria Math;\">15</span></p>\n", "<p><span style=\"font-family: Cambria Math;\"> 12</span></p>\n"],
                    solution_en: "<p>3.(c)</p>\r\n<p><span style=\"font-family: Cambria Math;\">According to the question,</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> N &times;15 = N&times;3 +(N - 5)&times;18</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">15N = 3N +18N -90</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> 21N - 15N = 90</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> 6N = 90 </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">So, </span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> N = 15</span></p>\n",
                    solution_hi: "<p>3.(c)</p>\r\n<p><span style=\"font-family: Kokila;\">&#2346;&#2381;&#2352;&#2358;&#2381;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2309;&#2344;&#2369;&#2360;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\">,</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> N &times;15 = N&times;3 +(N - 5)&times;18</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">15N = 3N +18N -90</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> 21N - 15N = 90</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> 6N = 90 </span></p>\r\n<p><span style=\"font-family: Kokila;\">&#2311;&#2360;&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\">,</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> N = 15</span></p>\n",
                    correct: "c",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "4",
                    section: "17",
                    question_en: "<p>4. <span style=\"font-family: Cambria Math;\">S alone can do a piece of work in 14 days. T alone can do the same work in 21 days. U alone can do the same work in 28 days. Working together, in how many days will they complete the work?</span></p>\n",
                    question_hi: "<p>4. <span style=\"font-family: Cambria Math;\">S </span><span style=\"font-family: Kokila;\">&#2309;&#2325;&#2375;&#2354;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2366;&#2352;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> 14 </span><span style=\"font-family: Kokila;\">&#2342;&#2367;&#2344;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2325;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> T </span><span style=\"font-family: Kokila;\">&#2309;&#2325;&#2375;&#2354;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2313;&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2366;&#2352;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> 21 </span><span style=\"font-family: Kokila;\">&#2342;&#2367;&#2344;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2325;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> U </span><span style=\"font-family: Kokila;\">&#2309;&#2325;&#2375;&#2354;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2313;&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2366;&#2352;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> 28 </span><span style=\"font-family: Kokila;\">&#2342;&#2367;&#2344;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2325;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2366;&#2341;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2366;&#2352;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2352;&#2340;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2369;&#2319;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Kokila;\">&#2357;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2366;&#2352;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2367;&#2340;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2342;&#2367;&#2344;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2370;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2352;&#2375;&#2306;&#2327;&#2375;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>84</mn><mn>13</mn></mfrac></math></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>77</mn><mn>13</mn></mfrac></math></p>\n", 
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>71</mn><mn>13</mn></mfrac></math></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>87</mn><mn>13</mn></mfrac></math></p>\n"],
                    options_hi: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>84</mn><mn>13</mn></mfrac></math></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>77</mn><mn>13</mn></mfrac></math></p>\n",
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>71</mn><mn>13</mn></mfrac></math></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>87</mn><mn>13</mn></mfrac></math></p>\n"],
                    solution_en: "<p>4.(a)</p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1674893178/word/media/image1.png\" width=\"227\" height=\"99\"></p>\r\n<p><span style=\"font-family: Cambria Math;\">So,</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&rArr;</mo><mfrac><mn>84</mn><mrow><mn>6</mn><mo>+</mo><mn>4</mn><mo>+</mo><mn>3</mn></mrow></mfrac><mo>=</mo><mo>&nbsp;</mo><mfrac><mn>84</mn><mn>13</mn></mfrac></math> days</span></p>\n",
                    solution_hi: "<p>4.(a)</p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1674893178/word/media/image2.png\" width=\"239\" height=\"97\"></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2311;&#2360;&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\">,</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&rArr;</mo><mfrac><mn>84</mn><mrow><mn>6</mn><mo>+</mo><mn>4</mn><mo>+</mo><mn>3</mn></mrow></mfrac><mo>=</mo><mo>&nbsp;</mo><mfrac><mn>84</mn><mn>13</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\">= </span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2342;&#2367;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                    correct: "a",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "5",
                    section: "17",
                    question_en: "<p>5.<span style=\"font-family: Cambria Math;\"> A camp of soldiers has food for 400 days, After 40 days, 150 soldiers leave the camp. As a result, the food can last for 480 days from then onwards. What was the initial number of soldiers?</span></p>\n",
                    question_hi: "<p>5. <span style=\"font-family: Kokila;\">&#2360;&#2376;&#2344;&#2367;&#2325;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2358;&#2367;&#2357;&#2367;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> 400 </span><span style=\"font-family: Kokila;\">&#2342;&#2367;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2349;&#2379;&#2332;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2379;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, 40 </span><span style=\"font-family: Kokila;\">&#2342;&#2367;&#2344;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2348;&#2366;&#2342;</span><span style=\"font-family: Cambria Math;\"> 150 </span><span style=\"font-family: Kokila;\">&#2360;&#2376;&#2344;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2358;&#2367;&#2357;&#2367;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2330;&#2354;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2332;&#2366;&#2340;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;&#2306;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2344;&#2340;&#2368;&#2332;&#2340;&#2344;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Kokila;\">&#2349;&#2379;&#2332;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2313;&#2360;&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2348;&#2366;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> 480 </span><span style=\"font-family: Kokila;\">&#2342;&#2367;&#2344;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2340;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2330;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2325;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2376;&#2344;&#2367;&#2325;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2381;&#2352;&#2366;&#2352;&#2306;&#2349;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2341;&#2368;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>750</p>\n", "<p>600</p>\n", 
                                "<p>450</p>\n", "<p>900</p>\n"],
                    options_hi: ["<p>750</p>\n", "<p>600</p>\n",
                                "<p>450</p>\n", "<p>900</p>\n"],
                    solution_en: "<p>5.(b)</p>\r\n<p><span style=\"font-family: Cambria Math;\">Let the initial number of soldier = X</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> Food = 400 &times; X = 400X unit</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">After 40 days remaining food </span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> 400X - 40X = 360X </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">According to the question,</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> 360X = 480( X- 150)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> 480X -360X = 480 &times; 150</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> 120X = 480 &times; 150</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Therefore,</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">X = 600</span></p>\n",
                    solution_hi: "<p>5.(b)</p>\r\n<p><span style=\"font-family: Kokila;\">&#2350;&#2366;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2376;&#2344;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2381;&#2352;&#2366;&#2352;&#2306;&#2349;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> = X</span></p>\r\n<p><span style=\"font-family: Kokila;\">&#2349;&#2379;&#2332;&#2344;</span><span style=\"font-family: Cambria Math;\"> = 400 &times; X = 400X </span><span style=\"font-family: Kokila;\">&#2311;&#2325;&#2366;&#2312;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">40 </span><span style=\"font-family: Kokila;\">&#2342;&#2367;&#2344;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2348;&#2366;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2348;&#2330;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2369;&#2310;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2326;&#2366;&#2344;&#2366;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">400X - 40X = 360X</span></p>\r\n<p><span style=\"font-family: Kokila;\">&#2346;&#2381;&#2352;&#2358;&#2381;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2309;&#2344;&#2369;&#2360;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\">,</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">360X = 480 (X- 150)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">480X -360X = 480 &times; 150</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">120X = 480 &times; 150</span></p>\r\n<p><span style=\"font-family: Kokila;\">&#2311;&#2360;&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\">, x = 600</span></p>\n",
                    correct: "b",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "6",
                    section: "17",
                    question_en: "<p>6.<span style=\"font-family: Cambria Math;\"> A work could be completed in 100 days by some workers. Due to the absence of 16 workers, the work was completed in 108 days. Find the original number of workers.</span></p>\n",
                    question_hi: "<p>6. <span style=\"font-family: Kokila;\">&#2325;&#2369;&#2331;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2358;&#2381;&#2352;&#2350;&#2367;&#2325;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2342;&#2381;&#2357;&#2366;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2366;&#2352;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> 100 </span><span style=\"font-family: Kokila;\">&#2342;&#2367;&#2344;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2370;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2332;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2325;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> 16 </span><span style=\"font-family: Kokila;\">&#2350;&#2332;&#2342;&#2370;&#2352;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2344;&#2361;&#2368;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2352;&#2361;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> 108 </span><span style=\"font-family: Kokila;\">&#2342;&#2367;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2366;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2370;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2379;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2358;&#2381;&#2352;&#2350;&#2367;&#2325;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2370;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2332;&#2381;&#2334;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2368;&#2332;&#2367;&#2319;&#2404;</span></p>\n",
                    options_en: ["<p>204</p>\n", "<p>254</p>\n", 
                                "<p>226</p>\n", "<p>216</p>\n"],
                    options_hi: ["<p>204</p>\n", "<p>254</p>\n",
                                "<p>226</p>\n", "<p>216</p>\n"],
                    solution_en: "<p>6.(d)</p>\r\n<p><span style=\"font-family: Cambria Math;\">Let the number of initial worker = X</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">According to the question ,</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">100 &times; X = (X - 16) &times; 108</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> 108X -100X = 108 &times; 16</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> 8X = 108 &times; 16 </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Therefore ,</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> X = 216 </span></p>\n",
                    solution_hi: "<p>6.(d)</p>\r\n<p><span style=\"font-family: Kokila;\">&#2350;&#2366;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2381;&#2352;&#2366;&#2352;&#2306;&#2349;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2366;&#2352;&#2381;&#2351;&#2325;&#2352;&#2381;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> = X</span></p>\r\n<p><span style=\"font-family: Kokila;\">&#2346;&#2381;&#2352;&#2358;&#2381;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2309;&#2344;&#2369;&#2360;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\">,</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> 100 &times; x</span><span style=\"font-family: Cambria Math;\"> = (x</span><span style=\"font-family: Cambria Math;\">&nbsp;- 16) &times; 108</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> 108X -100X = 108 &times; 16</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> 8X = 108 &times; 16</span></p>\r\n<p><span style=\"font-family: Kokila;\">&#2311;&#2360;&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> ,</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> x = 216</span></p>\n",
                    correct: "d",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "7",
                    section: "17",
                    question_en: "<p>7. <span style=\"font-family: Cambria Math;\">A can do a piece of work alone in 15 days, while B completes the same piece of work alone in 12 days. If they work on it together for 5 days, then the fraction of the work that will be left is:</span></p>\n",
                    question_hi: "<p>7. <span style=\"font-family: Cambria Math;\">A </span><span style=\"font-family: Kokila;\">&#2325;&#2367;&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2366;&#2352;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2309;&#2325;&#2375;&#2354;&#2375;</span><span style=\"font-family: Cambria Math;\"> 15 </span><span style=\"font-family: Kokila;\">&#2342;&#2367;&#2344;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2325;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Kokila;\">&#2332;&#2348;&#2325;&#2367;</span><span style=\"font-family: Cambria Math;\"> B </span><span style=\"font-family: Kokila;\">&#2313;&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2366;&#2352;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2309;&#2325;&#2375;&#2354;&#2375;</span><span style=\"font-family: Cambria Math;\"> 12 </span><span style=\"font-family: Kokila;\">&#2342;&#2367;&#2344;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2370;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2325;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2357;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2313;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> 5 </span><span style=\"font-family: Kokila;\">&#2342;&#2367;&#2344;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2340;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2366;&#2341;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2366;&#2352;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2352;&#2340;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;&#2306;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Kokila;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2358;&#2375;&#2359;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2366;&#2352;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2309;&#2306;&#2358;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2379;&#2327;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2404;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                    options_en: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>4</mn></mfrac></math></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>8</mn></mfrac></math></p>\n", 
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>4</mn></mfrac></math><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>7</mn></mfrac></math><span style=\"font-family: Cambria Math;\"> </span></p>\n"],
                    options_hi: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>4</mn></mfrac></math></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>8</mn></mfrac></math></p>\n",
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>4</mn></mfrac></math><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>7</mn></mfrac></math><span style=\"font-family: Cambria Math;\"> </span></p>\n"],
                    solution_en: "<p>7.(c)</p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1674893178/word/media/image3.png\"></p>\r\n<p><span style=\"font-weight: 400;\">(A+B)</span>&rsquo;s 5 days work=9&times;5=45 work</p>\r\n<p><span style=\"font-family: Cambria Math;\">Left work=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>60</mn><mo>-</mo><mn>45</mn></mrow><mn>60</mn></mfrac><mo>=</mo><mfrac><mn>15</mn><mn>60</mn></mfrac><mo>=</mo><mfrac><mn>1</mn><mn>4</mn></mfrac></math></span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                    solution_hi: "<p>7.(c)</p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1674893178/word/media/image3.png\"></p>\r\n<p><span style=\"font-family: Kokila;\"><span style=\"font-weight: 400;\">(A+B) </span>&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> 5 </span><span style=\"font-family: Kokila;\">&#2342;&#2367;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2366;&#2350;</span><span style=\"font-family: Cambria Math;\">=9&times;5=45 </span><span style=\"font-family: Kokila;\">&#2325;&#2366;&#2352;&#2381;&#2351;</span></p>\r\n<p><span style=\"font-family: Kokila;\">&#2358;&#2375;&#2359;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2366;&#2352;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>60</mn><mo>-</mo><mn>45</mn></mrow><mn>60</mn></mfrac><mo>=</mo><mfrac><mn>15</mn><mn>60</mn></mfrac><mo>=</mo><mfrac><mn>1</mn><mn>4</mn></mfrac></math></span></p>\n",
                    correct: "c",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "8",
                    section: "17",
                    question_en: "<p>8. <span style=\"font-family: Cambria Math;\">Arshi can do a piece of work in 30 days, while Ahan can complete it in 20 days. Both together labour for 4 days and afterwards Ahan leaves. How long will Arshi to complete the remaining work?</span></p>\n",
                    question_hi: "<p>8. <span style=\"font-family: Kokila;\">&#2309;&#2352;&#2381;&#2358;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2366;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> 30 </span><span style=\"font-family: Kokila;\">&#2342;&#2367;&#2344;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2325;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Kokila;\">&#2332;&#2348;&#2325;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2309;&#2361;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2313;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> 20 </span><span style=\"font-family: Kokila;\">&#2342;&#2367;&#2344;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2370;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2325;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2342;&#2379;&#2344;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2366;&#2341;</span><span style=\"font-family: Cambria Math;\"> 4 </span><span style=\"font-family: Kokila;\">&#2342;&#2367;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2340;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2366;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2352;&#2340;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2313;&#2360;&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2348;&#2366;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2309;&#2361;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2344;&#2367;&#2325;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2332;&#2366;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2358;&#2375;&#2359;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2366;&#2352;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2370;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2352;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2309;&#2352;&#2381;&#2358;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2367;&#2340;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2350;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2354;&#2327;&#2375;&#2327;&#2366;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>10 days</p>\n", "<p>15 days</p>\n", 
                                "<p>25 days</p>\n", "<p>20 days</p>\n"],
                    options_hi: ["<p>10 <span style=\"font-family: Kokila;\">&#2342;&#2367;&#2344;</span></p>\n", "<p>15 <span style=\"font-family: Kokila;\">&#2342;&#2367;&#2344;</span></p>\n",
                                "<p>25 <span style=\"font-family: Kokila;\">&#2342;&#2367;&#2344;</span></p>\n", "<p>20 <span style=\"font-family: Kokila;\">&#2342;&#2367;&#2344;</span></p>\n"],
                    solution_en: "<p>8.(d)</p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1674893178/word/media/image4.png\"></p>\r\n<p><span style=\"font-weight: 400;\">(Arshi+Ahan)</span>&rsquo;s 4 day work=5&times;4=20</p>\r\n<p><span style=\"font-family: Cambria Math;\">Arshi will do the remaining work in =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>60</mn><mo>-</mo><mn>20</mn></mrow><mn>2</mn></mfrac><mo>=</mo><mfrac><mn>40</mn><mn>2</mn></mfrac><mo>=</mo><mn>20</mn><mi>d</mi><mi>a</mi><mi>y</mi><mi>s</mi></math></span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                    solution_hi: "<p>8.(d)</p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1674893178/word/media/image4.png\"></p>\r\n<p><span style=\"font-family: Kokila;\"><span style=\"font-weight: 400;\">(&#2309;&#2352;&#2381;&#2358;&#2368; +&#2309;&#2361;&#2366;&#2344;) </span>&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> 4 </span><span style=\"font-family: Kokila;\">&#2342;&#2367;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2366;&#2350;</span><span style=\"font-family: Cambria Math;\">=5&times;4=20</span></p>\r\n<p><span style=\"font-family: Kokila;\">&#2309;&#2352;&#2381;&#2358;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2358;&#2375;&#2359;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2366;&#2350;</span><span style=\"font-family: Cambria Math;\"> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>60</mn><mo>-</mo><mn>20</mn></mrow><mn>2</mn></mfrac><mo>=</mo><mfrac><mn>40</mn><mn>2</mn></mfrac><mo>=</mo><mn>20</mn></math> &nbsp;</span><span style=\"font-family: Kokila;\">&#2342;&#2367;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2352;&#2375;&#2327;&#2368;</span></p>\n",
                    correct: "d",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "9",
                    section: "17",
                    question_en: "<p>9. <span style=\"font-family: Cambria Math;\">Kavita can complete a piece of work alone in 12 hours, while Komal can complete the same work alone in 18 hours. Kavita works alone for the first 4 hours and leaves. After that, Komal comes and completes the remaining work by herself. How much time did Komal take to finish the remaining work?</span></p>\n",
                    question_hi: "<p>9. <span style=\"font-family: Kokila;\">&#2325;&#2357;&#2367;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2309;&#2325;&#2375;&#2354;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2366;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> 12 </span><span style=\"font-family: Kokila;\">&#2328;&#2306;&#2335;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2370;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2325;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Kokila;\">&#2332;&#2348;&#2325;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2379;&#2350;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2309;&#2325;&#2375;&#2354;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2313;&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2366;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> 18 </span><span style=\"font-family: Kokila;\">&#2328;&#2306;&#2335;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2370;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2325;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2357;&#2367;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2361;&#2354;&#2375;</span><span style=\"font-family: Cambria Math;\"> 4 </span><span style=\"font-family: Kokila;\">&#2328;&#2306;&#2335;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2309;&#2325;&#2375;&#2354;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2366;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2352;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2330;&#2354;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2332;&#2366;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2313;&#2360;&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2348;&#2366;&#2342;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Kokila;\">&#2325;&#2379;&#2350;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2310;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2348;&#2366;&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2366;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2326;&#2369;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2370;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2352;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2379;&#2350;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2358;&#2375;&#2359;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2366;&#2352;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2370;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2352;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2367;&#2340;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2350;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2354;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>15 hours</p>\n", "<p>10 hours</p>\n", 
                                "<p>16 hours</p>\n", "<p>12 hours</p>\n"],
                    options_hi: ["<p>15 <span style=\"font-family: Kokila;\">&#2328;&#2306;&#2335;&#2375;</span></p>\n", "<p>10 <span style=\"font-family: Kokila;\">&#2328;&#2306;&#2335;&#2375;</span></p>\n",
                                "<p>16 <span style=\"font-family: Kokila;\">&#2328;&#2306;&#2335;&#2375;</span></p>\n", "<p>12 <span style=\"font-family: Kokila;\">&#2328;&#2306;&#2335;&#2375;</span></p>\n"],
                    solution_en: "<p>9.(d)</p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1674893178/word/media/image5.png\" width=\"219\" height=\"134\"></p>\r\n<p><span style=\"font-family: Cambria Math;\">After working kavita 4 days remaining work = 36 - 12 =24 unit</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Komal will finish remaining work =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>24</mn><mn>2</mn></mfrac></math> </span><span style=\"font-family: Cambria Math;\"> = 12 days</span></p>\n",
                    solution_hi: "<p>9.(d)</p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1674893178/word/media/image6.png\" width=\"240\" height=\"123\"></p>\r\n<p><span style=\"font-family: Kokila;\">&#2325;&#2357;&#2367;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2366;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2352;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> 4 </span><span style=\"font-family: Kokila;\">&#2342;&#2367;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2348;&#2366;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2366;&#2350;</span><span style=\"font-family: Cambria Math;\"> = 36 - 12 = 24 </span><span style=\"font-family: Kokila;\">&#2351;&#2370;&#2344;&#2367;&#2335;</span></p>\r\n<p><span style=\"font-family: Kokila;\">&#2325;&#2379;&#2350;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2358;&#2375;&#2359;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2366;&#2352;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2370;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2352;&#2375;&#2327;&#2368;</span><span style=\"font-family: Cambria Math;\"> =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>24</mn><mn>2</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> = 12 </span><span style=\"font-family: Kokila;\">&#2342;&#2367;&#2344;</span></p>\n",
                    correct: "d",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "10",
                    section: "17",
                    question_en: "<p>10. <span style=\"font-family: Cambria Math;\">A,B and C together can complete a work in 10 days. A and B together can do the same work in 12 days. If A, B and C received 9000 for doing the work together, then what was the share of C from the above amount?</span></p>\n",
                    question_hi: "<p>10. <span style=\"font-family: Cambria Math;\">A,B </span><span style=\"font-family: Kokila;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> C </span><span style=\"font-family: Kokila;\">&#2350;&#2367;&#2354;&#2325;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2366;&#2352;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> 10 </span><span style=\"font-family: Kokila;\">&#2342;&#2367;&#2344;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2370;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2325;&#2340;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;&#2306;&#2404;</span><span style=\"font-family: Cambria Math;\"> A </span><span style=\"font-family: Kokila;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> B </span><span style=\"font-family: Kokila;\">&#2350;&#2367;&#2354;&#2325;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2313;&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2366;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> 12 </span><span style=\"font-family: Kokila;\">&#2342;&#2367;&#2344;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2325;&#2340;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;&#2306;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> A, B </span><span style=\"font-family: Kokila;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> C </span><span style=\"font-family: Kokila;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2366;&#2341;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2366;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2352;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> 9000 </span><span style=\"font-family: Kokila;\">&#2352;&#2369;&#2346;&#2351;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2367;&#2354;&#2340;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;&#2306;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Kokila;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2313;&#2346;&#2352;&#2379;&#2325;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2352;&#2366;&#2358;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> C </span><span style=\"font-family: Kokila;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2367;&#2360;&#2381;&#2360;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2367;&#2340;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2341;&#2366;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>Rs 3000</p>\n", "<p>Rs 3750</p>\n", 
                                "<p>Rs 1500</p>\n", "<p>Rs 2250</p>\n"],
                    options_hi: ["<p>3000 <span style=\"font-family: Kokila;\">&#2352;&#2370;&#2346;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p>3750 <span style=\"font-family: Kokila;\">&#2352;&#2370;&#2346;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                                "<p>1500 <span style=\"font-family: Kokila;\">&#2352;&#2370;&#2346;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p>2250 <span style=\"font-family: Kokila;\">&#2352;&#2370;&#2346;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n"],
                    solution_en: "<p>10.(c)</p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1674893178/word/media/image7.png\"></p>\r\n<p><span style=\"font-family: Cambria Math;\">C&rsquo;s share =&nbsp; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>6</mn></mfrac><mo>&times;</mo><mn>9000</mn></math>&nbsp;=</span><span style=\"font-family: Cambria Math;\">1,500</span></p>\n",
                    solution_hi: "<p>10.(c)</p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1674893178/word/media/image7.png\"></p>\r\n<p><span style=\"font-family: Cambria Math;\">C </span><span style=\"font-family: Kokila;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2367;&#2360;&#2381;&#2360;&#2366; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>6</mn></mfrac><mo>&times;</mo><mn>9000</mn></math></span><span style=\"font-family: Cambria Math;\">=</span><span style=\"font-family: Cambria Math;\">1,500</span></p>\n",
                    correct: "c",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "11",
                    section: "17",
                    question_en: "<p>11. <span style=\"font-family: Cambria Math;\">5 men can complete a work in 12 days, and 6 women can complete the same work in 20 days. In how many days can 1 man and 1 woman together complete this work?</span></p>\n",
                    question_hi: "<p>11. <span style=\"font-family: Cambria Math;\">5 </span><span style=\"font-family: Kokila;\">&#2346;&#2369;&#2352;&#2369;&#2359;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2366;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> 12 </span><span style=\"font-family: Kokila;\">&#2342;&#2367;&#2344;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2370;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2325;&#2340;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;&#2306;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Kokila;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> 6 </span><span style=\"font-family: Kokila;\">&#2350;&#2361;&#2367;&#2354;&#2366;&#2319;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2313;&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2366;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> 20 </span><span style=\"font-family: Kokila;\">&#2342;&#2367;&#2344;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2370;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2325;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;&#2306;&#2404;</span><span style=\"font-family: Cambria Math;\"> 1 </span><span style=\"font-family: Kokila;\">&#2346;&#2369;&#2352;&#2369;&#2359;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> 1 </span><span style=\"font-family: Kokila;\">&#2350;&#2361;&#2367;&#2354;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2367;&#2354;&#2325;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2311;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2366;&#2352;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2367;&#2340;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2342;&#2367;&#2344;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2370;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2325;&#2340;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;&#2306;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>120 days</p>\n", "<p>32 days</p>\n", 
                                "<p>40 days</p>\n", "<p>60 days</p>\n"],
                    options_hi: ["<p>120 <span style=\"font-family: Kokila;\">&#2342;&#2367;&#2344;</span></p>\n", "<p>32 <span style=\"font-family: Kokila;\">&#2342;&#2367;&#2344;</span></p>\n",
                                "<p>40 <span style=\"font-family: Kokila;\">&#2342;&#2367;&#2344;</span></p>\n", "<p>60 <span style=\"font-family: Kokila;\">&#2342;&#2367;&#2344;</span></p>\n"],
                    solution_en: "<p>11.(c)<span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">5m &times; 12 = 6w &times; 20</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">M : W = 2 : 1</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">5(2) &times; 12 = {(1(2) + 1(1)} &times; d</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">120 = 3 &times; d</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">d = 40</span></p>\n",
                    solution_hi: "<p>11.(c)<span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">5m &times; 12 = 6w &times; 20</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">M : W = 2 : 1</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">5(2) &times; 12 = {(1(2) + 1(1)} &times; d</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">120 = 3 &times; d</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">d = 40</span></p>\n",
                    correct: "c",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "12",
                    section: "17",
                    question_en: "<p>12. <span style=\"font-family: Cambria Math;\">A and B together can finish a work in 20 days. If they worked together for 8 days, then what was the share of work that was yet to be completed?</span></p>\n",
                    question_hi: "<p>12. <span style=\"font-family: Cambria Math;\"> A </span><span style=\"font-family: Kokila;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> B </span><span style=\"font-family: Kokila;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2366;&#2341;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2366;&#2352;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> 20 </span><span style=\"font-family: Kokila;\">&#2342;&#2367;&#2344;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2350;&#2366;&#2346;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2325;&#2340;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;&#2306;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2313;&#2344;&#2381;&#2361;&#2379;&#2306;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2366;&#2341;</span><span style=\"font-family: Cambria Math;\"> 8 </span><span style=\"font-family: Kokila;\">&#2342;&#2367;&#2344;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2340;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2366;&#2352;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Kokila;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2313;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2366;&#2352;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2367;&#2340;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2349;&#2366;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2309;&#2349;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2370;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2332;&#2366;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2348;&#2366;&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>5</mn></mfrac></math></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>5</mn></mfrac></math></p>\n", 
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>5</mn></mfrac></math></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>5</mn></mfrac></math></p>\n"],
                    options_hi: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>5</mn></mfrac></math></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>5</mn></mfrac></math></p>\n",
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>5</mn></mfrac></math></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>5</mn></mfrac></math></p>\n"],
                    solution_en: "<p>12.(a)</p>\r\n<p><span style=\"font-family: Cambria Math;\">Let the total work = 20 units</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Efficiency of A + B </span><span style=\"font-family: Cambria Math;\"> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>20</mn><mn>20</mn></mfrac></math>=1</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Work done in 8 days = 8 unit</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Share to be completed = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>20</mn><mo>-</mo><mn>8</mn></mrow><mn>20</mn></mfrac><mo>=</mo><mfrac><mn>3</mn><mn>5</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\">&nbsp;</span></p>\n",
                    solution_hi: "<p>12.(a)</p>\r\n<p><span style=\"font-family: Kokila;\">&#2350;&#2366;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2369;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2366;&#2352;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> = 20 </span><span style=\"font-family: Kokila;\">&#2311;&#2325;&#2366;&#2312;</span></p>\r\n<p><span style=\"font-family: Kokila;\"><span style=\"font-weight: 400;\">(A + B) </span>&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2342;&#2325;&#2381;&#2359;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>20</mn><mn>20</mn></mfrac></math>=1</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">8 </span><span style=\"font-family: Kokila;\">&#2342;&#2367;&#2344;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2327;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2366;&#2352;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> = 8 </span><span style=\"font-family: Kokila;\">&#2311;&#2325;&#2366;&#2312;</span></p>\r\n<p><span style=\"font-family: Kokila;\">&#2346;&#2370;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2332;&#2366;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2357;&#2366;&#2354;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2367;&#2360;&#2381;&#2360;&#2366;</span><span style=\"font-family: Cambria Math;\"> =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>20</mn><mo>-</mo><mn>8</mn></mrow><mn>20</mn></mfrac><mo>&nbsp;</mo><mo>=</mo><mfrac><mrow><mo>&nbsp;</mo><mn>3</mn></mrow><mn>5</mn></mfrac></math></span></p>\n",
                    correct: "a",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "13",
                    section: "17",
                    question_en: "<p>13.<span style=\"font-family: Cambria Math;\"> If A and B together can do a piece of work in 5 days, and A alone can do the same work in 10 days, then in how many days can B alone do the piece of work?</span></p>\n",
                    question_hi: "<p>13. <span style=\"font-family: Kokila;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> A </span><span style=\"font-family: Kokila;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> B </span><span style=\"font-family: Kokila;\">&#2350;&#2367;&#2354;&#2325;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2367;&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2366;&#2352;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> 5 </span><span style=\"font-family: Kokila;\">&#2342;&#2367;&#2344;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2325;&#2340;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;&#2306;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Kokila;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> A </span><span style=\"font-family: Kokila;\">&#2309;&#2325;&#2375;&#2354;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2313;&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2366;&#2352;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> 10 </span><span style=\"font-family: Kokila;\">&#2342;&#2367;&#2344;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2325;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Kokila;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> B </span><span style=\"font-family: Kokila;\">&#2309;&#2325;&#2375;&#2354;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2313;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2366;&#2352;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2367;&#2340;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2342;&#2367;&#2344;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2325;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>10 days</p>\n", "<p>11 days</p>\n", 
                                "<p>12 days</p>\n", "<p>13 days</p>\n"],
                    options_hi: ["<p>10 <span style=\"font-family: Kokila;\">&#2342;&#2367;&#2344;</span></p>\n", "<p>11 <span style=\"font-family: Kokila;\">&#2342;&#2367;&#2344;</span></p>\n",
                                "<p>12 <span style=\"font-family: Kokila;\">&#2342;&#2367;&#2344;</span></p>\n", "<p>13 <span style=\"font-family: Kokila;\">&#2342;&#2367;&#2344;</span></p>\n"],
                    solution_en: "<p>13.(a)<span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>L</mi><mi>e</mi><mi>t</mi><mo>&nbsp;</mo><mi>t</mi><mi>h</mi><mi>e</mi><mo>&nbsp;</mo><mi>t</mi><mi>o</mi><mi>t</mi><mi>a</mi><mi>l</mi><mo>&nbsp;</mo><mi>w</mi><mi>o</mi><mi>r</mi><mi>k</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>10</mn><mo>&nbsp;</mo><mi>u</mi><mi>n</mi><mi>i</mi><mi>t</mi><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mi>E</mi><mi>f</mi><mi>f</mi><mi>i</mi><mi>c</mi><mi>i</mi><mi>e</mi><mi>n</mi><mi>c</mi><mi>y</mi><mo>&nbsp;</mo><mi>o</mi><mi>f</mi><mo>&nbsp;</mo><mi>A</mi><mo>&nbsp;</mo><mo>=</mo><mfrac><mrow><mo>&nbsp;</mo><mn>10</mn></mrow><mn>10</mn></mfrac><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>1</mn><mspace linebreak=\"newline\"></mspace><mo>&nbsp;</mo><mi>E</mi><mi>f</mi><mi>f</mi><mi>i</mi><mi>c</mi><mi>i</mi><mi>e</mi><mi>n</mi><mi>c</mi><mi>y</mi><mo>&nbsp;</mo><mi>o</mi><mi>f</mi><mo>&nbsp;</mo><mi>A</mi><mo>&nbsp;</mo><mi>a</mi><mi>n</mi><mi>d</mi><mo>&nbsp;</mo><mi>B</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><mn>10</mn><mn>5</mn></mfrac><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>2</mn><mspace linebreak=\"newline\"></mspace><mo>&nbsp;</mo><mi>E</mi><mi>f</mi><mi>f</mi><mi>i</mi><mi>c</mi><mi>i</mi><mi>e</mi><mi>n</mi><mi>c</mi><mi>y</mi><mo>&nbsp;</mo><mi>o</mi><mi>f</mi><mo>&nbsp;</mo><mi>B</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>2</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>1</mn><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>1</mn><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mi>T</mi><mi>i</mi><mi>m</mi><mi>e</mi><mo>&nbsp;</mo><mi>t</mi><mi>a</mi><mi>k</mi><mi>e</mi><mi>n</mi><mo>&nbsp;</mo><mi>b</mi><mi>y</mi><mo>&nbsp;</mo><mi>B</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><mn>10</mn><mn>1</mn></mfrac><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>10</mn><mo>&nbsp;</mo><mi>d</mi><mi>a</mi><mi>y</mi><mi>s</mi></math></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                    solution_hi: "<p>13.(a)</p>\r\n<p><math style=\"font-family: Verdana;\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#2350;&#2366;&#2344;&#2366;</mi><mo>&nbsp;</mo><mi>&#2325;&#2369;&#2354;</mi><mo>&nbsp;</mo><mi>&#2325;&#2366;&#2352;&#2381;&#2351;</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>10</mn><mo>&nbsp;</mo><mi>u</mi><mi>n</mi><mi>i</mi><mi>t</mi><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mi>A</mi><mo>&nbsp;</mo><mi>&#2325;&#2368;</mi><mo>&nbsp;</mo><mi>&#2342;&#2325;&#2381;&#2359;&#2340;&#2366;</mi><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>=</mo><mfrac><mrow><mo>&nbsp;</mo><mn>10</mn></mrow><mn>10</mn></mfrac><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>1</mn><mspace linebreak=\"newline\"></mspace><mo>&nbsp;</mo><mi>A</mi><mo>&nbsp;</mo><mi>&#2324;&#2352;</mi><mo>&nbsp;</mo><mi>B</mi><mo>&nbsp;</mo><mi>&#2325;&#2368;</mi><mo>&nbsp;</mo><mi>&#2342;&#2325;&#2381;&#2359;&#2340;&#2366;</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><mn>10</mn><mn>5</mn></mfrac><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>2</mn><mspace linebreak=\"newline\"></mspace><mo>&nbsp;</mo><mi>B</mi><mo>&nbsp;</mo><mi>&#2325;&#2368;</mi><mo>&nbsp;</mo><mi>&#2342;&#2325;&#2381;&#2359;&#2340;&#2366;</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>2</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>1</mn><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>1</mn><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mi>B</mi><mo>&nbsp;</mo><mi>&#2342;&#2381;&#2357;&#2366;&#2352;&#2366;</mi><mo>&nbsp;</mo><mi>&#2354;&#2367;&#2351;&#2366;</mi><mo>&nbsp;</mo><mi>&#2327;&#2351;&#2366;</mi><mo>&nbsp;</mo><mi>&#2360;&#2350;&#2351;</mi><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><mn>10</mn><mn>1</mn></mfrac><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>10</mn><mo>&nbsp;</mo><mo>&nbsp;</mo><mi>&#2342;&#2367;&#2344;</mi><mo>&nbsp;</mo></math></p>\n",
                    correct: "a",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "14",
                    section: "17",
                    question_en: "<p>14. <span style=\"font-family: Cambria Math;\">12 women can make 15 toys in 6 days if they work for 8 hours a day. If 20 toys are to be made by 10 women. working 12 hours a day, then how many days will be required?</span></p>\n",
                    question_hi: "<p>14. <span style=\"font-family: Cambria Math;\">12 </span><span style=\"font-family: Kokila;\">&#2350;&#2361;&#2367;&#2354;&#2366;&#2319;&#2306;</span><span style=\"font-family: Cambria Math;\"> 6 </span><span style=\"font-family: Kokila;\">&#2342;&#2367;&#2344;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> 15 </span><span style=\"font-family: Kokila;\">&#2326;&#2367;&#2354;&#2380;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2348;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2325;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2357;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2342;&#2367;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> 8 </span><span style=\"font-family: Kokila;\">&#2328;&#2306;&#2335;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2366;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2352;&#2375;&#2306;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> 20 </span><span style=\"font-family: Kokila;\">&#2326;&#2367;&#2354;&#2380;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> 10 </span><span style=\"font-family: Kokila;\">&#2350;&#2361;&#2367;&#2354;&#2366;&#2323;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2342;&#2381;&#2357;&#2366;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2348;&#2344;&#2366;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2332;&#2366;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;&#2306;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Kokila;\">&#2342;&#2367;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> 12 </span><span style=\"font-family: Kokila;\">&#2328;&#2306;&#2335;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2366;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2352;&#2340;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;&#2306;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Kokila;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2367;&#2340;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2342;&#2367;&#2344;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2310;&#2357;&#2358;&#2381;&#2351;&#2325;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2379;&#2327;&#2368;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>6.8 days</p>\n", "<p>7.8 days</p>\n", 
                                "<p>6.4 days</p>\n", "<p>5.4 days</p>\n"],
                    options_hi: ["<p>6.8 <span style=\"font-family: Kokila;\">&#2342;&#2367;&#2344;</span></p>\n", "<p>7.8 <span style=\"font-family: Kokila;\">&#2342;&#2367;&#2344;</span></p>\n",
                                "<p>6.4 <span style=\"font-family: Kokila;\">&#2342;&#2367;&#2344;</span></p>\n", "<p>5.4 <span style=\"font-family: Kokila;\">&#2342;&#2367;&#2344;</span></p>\n"],
                    solution_en: "<p>14.(c)<span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>A</mi><mi>s</mi><mo>&nbsp;</mo><mi>p</mi><mi>e</mi><mi>r</mi><mo>&nbsp;</mo><mi>q</mi><mi>u</mi><mi>e</mi><mi>s</mi><mi>t</mi><mi>i</mi><mi>o</mi><mi>n</mi><mo>,</mo><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mfrac><mrow><mn>12</mn><mo>&times;</mo><mn>6</mn><mo>&times;</mo><mn>8</mn></mrow><mn>15</mn></mfrac><mo>=</mo><mfrac><mrow><mn>10</mn><mo>&times;</mo><mi>d</mi><mo>&times;</mo><mn>12</mn></mrow><mn>20</mn></mfrac><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mi>d</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>6</mn><mo>.</mo><mn>4</mn><mo>&nbsp;</mo><mi>d</mi><mi>a</mi><mi>y</mi><mi>s</mi><mo>.</mo></math></p>\n",
                    solution_hi: "<p>14.(c)</p>\r\n<p><math style=\"font-family: Verdana;\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#2346;&#2381;&#2352;&#2358;&#2381;&#2344;</mi><mo>&nbsp;</mo><mi>&#2325;&#2375;</mi><mo>&nbsp;</mo><mi>&#2309;&#2344;&#2369;&#2360;&#2366;&#2352;</mi><mo>,</mo><mspace linebreak=\"newline\"></mspace><mfrac><mrow><mn>12</mn><mo>&times;</mo><mn>6</mn><mo>&times;</mo><mn>8</mn></mrow><mn>15</mn></mfrac><mo>=</mo><mfrac><mrow><mn>10</mn><mo>&times;</mo><mi>d</mi><mo>&times;</mo><mn>12</mn></mrow><mn>20</mn></mfrac><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mi>d</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>6</mn><mo>.</mo><mn>4</mn><mo>&nbsp;</mo><mi>&#2342;&#2367;&#2344;</mi></math></p>\r\n<p>&nbsp;</p>\n",
                    correct: "c",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "15",
                    section: "misc",
                    question_en: "<p>15. <span style=\"font-family: Cambria Math;\">A can complete 25% of a work in 10 days. B can complete 40% of the same work in 16 days. In how many days will A and B together complete twice the work mentioned above?</span></p>\n",
                    question_hi: "<p>15. <span style=\"font-family: Cambria Math;\"> A </span><span style=\"font-family: Kokila;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2366;&#2352;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> 25% 10 </span><span style=\"font-family: Kokila;\">&#2342;&#2367;&#2344;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2370;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2325;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> B </span><span style=\"font-family: Kokila;\">&#2313;&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2366;&#2352;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> 40% 16 </span><span style=\"font-family: Kokila;\">&#2342;&#2367;&#2344;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2370;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2325;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> A </span><span style=\"font-family: Kokila;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> B </span><span style=\"font-family: Kokila;\">&#2350;&#2367;&#2354;&#2325;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2314;&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2348;&#2340;&#2366;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2327;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2366;&#2352;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2342;&#2379;&#2327;&#2369;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2367;&#2340;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2342;&#2367;&#2344;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2370;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2352;&#2375;&#2306;&#2327;&#2375;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>20</p>\n", "<p>40</p>\n", 
                                "<p>60</p>\n", "<p>50</p>\n"],
                    options_hi: ["<p>20</p>\n", "<p>40</p>\n",
                                "<p>60</p>\n", "<p>50</p>\n"],
                    solution_en: "<p>15.(b)</p>\r\n<p><span style=\"font-family: Cambria Math;\">Time taken by A to finish the whole work = 40 days</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Time taken by B to finish the whole work = 40 days</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Let the total work = 40 unit</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Efficiency Ratio of A ;: B = 1 : 1</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Time taken to complete double the work = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>80</mn><mrow><mn>1</mn><mo>+</mo><mn>1</mn></mrow></mfrac></math></span><span style=\"font-family: Cambria Math;\"> = 40 days</span></p>\n",
                    solution_hi: "<p>15.(b)</p>\r\n<p><span style=\"font-family: Cambria Math;\">A </span><span style=\"font-family: Kokila;\">&#2342;&#2381;&#2357;&#2366;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2370;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2366;&#2352;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2350;&#2366;&#2346;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2352;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2354;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2327;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2350;&#2351;</span><span style=\"font-family: Cambria Math;\"> = 40 </span><span style=\"font-family: Kokila;\">&#2342;&#2367;&#2344;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">B </span><span style=\"font-family: Kokila;\">&#2342;&#2381;&#2357;&#2366;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2370;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2366;&#2352;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2350;&#2366;&#2346;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2352;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2354;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2327;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2350;&#2351;</span><span style=\"font-family: Cambria Math;\"> = 40 </span><span style=\"font-family: Kokila;\">&#2342;&#2367;&#2344;</span></p>\r\n<p><span style=\"font-family: Kokila;\">&#2350;&#2366;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2369;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2366;&#2352;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> = 40 </span><span style=\"font-family: Kokila;\">&#2351;&#2370;&#2344;&#2367;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">A </span><span style=\"font-family: Kokila;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> B </span><span style=\"font-family: Kokila;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2342;&#2325;&#2381;&#2359;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2309;&#2344;&#2369;&#2346;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\">, A</span><span style=\"font-family: Cambria Math;\">:</span><span style=\"font-family: Cambria Math;\">B = 1</span><span style=\"font-family: Cambria Math;\">:</span><span style=\"font-family: Cambria Math;\">1</span></p>\r\n<p><span style=\"font-family: Kokila;\">&#2342;&#2369;&#2327;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2366;&#2352;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2370;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2352;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2354;&#2327;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2350;&#2351;</span><span style=\"font-family: Cambria Math;\"> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>80</mn><mrow><mn>1</mn><mo>+</mo><mn>1</mn></mrow></mfrac></math></span><span style=\"font-family: Cambria Math;\"> = 40 </span><span style=\"font-family: Kokila;\">&#2342;&#2367;&#2344;</span></p>\n",
                    correct: "b",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>