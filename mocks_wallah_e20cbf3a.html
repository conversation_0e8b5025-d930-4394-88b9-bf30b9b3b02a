<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">15:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 15</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">15</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 15 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 0,
                end: 13
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 14,
                end: 14
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "17",
                    question_en: "<p>1. Study the given table and answer the question that follows.<br>Percentage of marks obtained by 5 students in different subjects<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1737368849654.png\" alt=\"rId4\" width=\"348\" height=\"138\"> <br>The marks obtained by Sohan in maths are _______ than that obtained by Shyam in maths.</p>",
                    question_hi: "<p>1. दी गई तालिका का अध्ययन कीजिए और दिए गए प्रश्न का उत्तर दीजिए। <br>5 विद्यार्थियों द्वारा विभिन्न विषयों में प्राप्त अंकों का प्रतिशत<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1737368849837.png\" alt=\"rId5\" width=\"300\" height=\"153\"> <br>सोहन द्वारा गणित में प्राप्त किया गया अंक, श्याम द्वारा गणित में प्राप्त किए गए अंकों से _____है।</p>",
                    options_en: ["<p>40 more</p>", "<p>40 less</p>", 
                                "<p>30 less</p>", "<p>30 more</p>"],
                    options_hi: ["<p>40 अधिक</p>", "<p>40 कम</p>",
                                "<p>30 कम</p>", "<p>30 अधिक</p>"],
                    solution_en: "<p>1.(a)<br>Marks obtained by Sohan in maths = <math display=\"inline\"><mfrac><mrow><mn>90</mn></mrow><mrow><mn>100</mn></mrow></mfrac><mo>&#215;</mo></math> 200 = 180<br>Marks obtained by Shyam in maths = <math display=\"inline\"><mfrac><mrow><mn>70</mn></mrow><mrow><mn>100</mn></mrow></mfrac><mo>&#215;</mo></math> 200 = 140<br>required difference = 180 - 140 = 40 (more)</p>",
                    solution_hi: "<p>1.(a)<br>सोहन द्वारा गणित में प्राप्त अंक = <math display=\"inline\"><mfrac><mrow><mn>90</mn></mrow><mrow><mn>100</mn></mrow></mfrac><mo>&#215;</mo></math> 200 = 180<br>श्याम द्वारा गणित में प्राप्त अंक = <math display=\"inline\"><mfrac><mrow><mn>70</mn></mrow><mrow><mn>100</mn></mrow></mfrac><mo>&#215;</mo></math> 200 = 140<br>आवश्यक अंतर = 180 - 140 = 40(अधिक)</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "2",
                    section: "17",
                    question_en: "<p>2. In the following table, the achievement of 10 boys and 10 girls in Math has been given. Study the table and select the correct statement.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1737368849984.png\" alt=\"rId6\" width=\"87\" height=\"198\"></p>",
                    question_hi: "<p>2. निम्नलिखित तालिका में गणित में 10 लड़के और 10 लड़कियों की उपलब्धि दी गई है। तालिका का अध्ययन कीजिए और सही कथन का चयन कीजिए।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1737368850094.png\" alt=\"rId7\" width=\"107\" height=\"221\"></p>",
                    options_en: ["<p>The average achievement of the boys and girls is the same.</p>", "<p>The average achievement of the girls is more than that of the boys.</p>", 
                                "<p>The given data is insufficient to draw any meaningful conclusion.</p>", "<p>The average achievement of the boys is more than that of the girls.</p>"],
                    options_hi: ["<p>लड़कों और लड़कियों की औसत उपलब्धि समान है।</p>", "<p>लड़कियों की औसत उपलब्धि लड़कों की तुलना में अधिक है।</p>",
                                "<p>दिया गया डेटा कोई सार्थक निष्कर्ष निकालने के लिए अपर्याप्त है।</p>", "<p>लड़कों की औसत उपलब्धि लड़कियों की तुलना में अधिक है।</p>"],
                    solution_en: "<p>2.(b)<br>Average achievement of Boys = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>56</mn><mo>+</mo><mn>75</mn><mo>+</mo><mn>34</mn><mo>+</mo><mn>49</mn><mo>+</mo><mn>89</mn><mo>+</mo><mn>99</mn><mo>+</mo><mn>94</mn><mo>+</mo><mn>59</mn><mo>+</mo><mn>82</mn><mo>+</mo><mn>79</mn></mrow><mn>10</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>716</mn><mn>10</mn></mfrac></math>= 71.6<br>Average achievement of Girls = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>35</mn><mo>+</mo><mn>88</mn><mo>+</mo><mn>95</mn><mo>+</mo><mn>99</mn><mo>+</mo><mn>67</mn><mo>+</mo><mn>82</mn><mo>+</mo><mn>97</mn><mo>+</mo><mn>63</mn><mo>+</mo><mn>79</mn><mo>+</mo><mn>81</mn></mrow><mn>10</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>786</mn><mn>10</mn></mfrac></math> = 78.6<br>Hence, the average achievement of girls is more than that of the boys.</p>",
                    solution_hi: "<p>2.(b)<br>लड़कों की औसत उपलब्धि = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>56</mn><mo>+</mo><mn>75</mn><mo>+</mo><mn>34</mn><mo>+</mo><mn>49</mn><mo>+</mo><mn>89</mn><mo>+</mo><mn>99</mn><mo>+</mo><mn>94</mn><mo>+</mo><mn>59</mn><mo>+</mo><mn>82</mn><mo>+</mo><mn>79</mn></mrow><mn>10</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>716</mn><mn>10</mn></mfrac></math> = 71.6<br>लड़कियों की औसत उपलब्धि = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>35</mn><mo>+</mo><mn>88</mn><mo>+</mo><mn>95</mn><mo>+</mo><mn>99</mn><mo>+</mo><mn>67</mn><mo>+</mo><mn>82</mn><mo>+</mo><mn>97</mn><mo>+</mo><mn>63</mn><mo>+</mo><mn>79</mn><mo>+</mo><mn>81</mn></mrow><mn>10</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>786</mn><mn>10</mn></mfrac></math> = 78.6<br>अतः लड़कियों की औसत उपलब्धि लड़कों की तुलना में अधिक है।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "3",
                    section: "17",
                    question_en: "<p>3. The given bar graph shows the strength of employees (in lakhs) of a company during four different years.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1737368850193.png\" alt=\"rId8\" width=\"431\" height=\"259\"> <br>What was the percentage increase in employees in 2020 over 2019 ?</p>",
                    question_hi: "<p>3. दिया गया बार ग्राफ़ चार अलग-अलग वर्षों के दौरान एक कंपनी के कर्मचारियों की संख्या (लाख में) दर्शाता है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1737368850293.png\" alt=\"rId9\" width=\"421\" height=\"253\"> <br>चार अलग-अलग वर्षों के दौरान एक कंपनी में कर्मचारियों की संख्या 2019 की तुलना में 2020 में कर्मचारियों की संख्या में कितने प्रतिशत की वृद्धि हुई ?</p>",
                    options_en: ["<p>5<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math>%</p>", "<p>6<math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math>%</p>", 
                                "<p>6<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math>%</p>", "<p>4<math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math>%</p>"],
                    options_hi: ["<p>5<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math>%</p>", "<p>6<math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math>%</p>",
                                "<p>6<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math>%</p>", "<p>4<math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math>%</p>"],
                    solution_en: "<p>3.(c)<br>Required % = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>34</mn><mo>-</mo><mn>32</mn></mrow><mn>32</mn></mfrac></math> &times; 100 = 6<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>4</mn></mfrac></math>%</p>",
                    solution_hi: "<p>3.(c)<br>आवश्यक% = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>34</mn><mo>-</mo><mn>32</mn></mrow><mn>32</mn></mfrac></math> &times; 100 = 6<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>4</mn></mfrac></math>%</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "4",
                    section: "17",
                    question_en: "<p>4. The following table shows the marks obtained out of 100, by four students in four different subjects.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1737368850400.png\" alt=\"rId10\" width=\"276\" height=\"105\"> <br>The total marks scored by Ronak in all the subjects is what percentage of the total marks scored by Shivam, Sonu, Arnav together in all the subjects ?</p>",
                    question_hi: "<p>4. निम्न तालिका चार विद्यार्थियों द्वारा चार अलग-अलग विषयों में 100 में से प्राप्त अंकों को दर्शाती है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1737368850527.png\" alt=\"rId11\" width=\"254\" height=\"119\"> <br>सभी विषयों में रौनक द्वारा प्राप्त किए गए कुल अंक, शिवम, सोनू, अर्नव द्वारा सभी विषयों में प्राप्त किए गए कुल अंकों का कितना प्रतिशत हैं ?</p>",
                    options_en: ["<p>40.63%</p>", "<p>44.46%</p>", 
                                "<p>34.63%</p>", "<p>30.46%</p>"],
                    options_hi: ["<p>40.63%</p>", "<p>44.46%</p>",
                                "<p>34.63%</p>", "<p>30.46%</p>"],
                    solution_en: "<p>4.(d)<br>Marks obtained by Ronak in all subject = 75 + 89 + 79 + 82 = 325<br>Marks obtained by shivam, sonu, Arnav in all subject = (88 + 95 + 90 + 87) + (92 + 85 + 90 + 88) + (83 + 87 + 90 + 92) = 1067<br>Required % = <math display=\"inline\"><mfrac><mrow><mn>325</mn></mrow><mrow><mn>1067</mn></mrow></mfrac></math> &times; 100 = 30.46%</p>",
                    solution_hi: "<p>4.(d)<br>रोनक द्वारा सभी विषयों में प्राप्त अंक = 75 + 89 + 79 + 82 = 325<br>सभी विषयों में शिवम, सोनू, अर्नव द्वारा प्राप्त अंक = (88 + 95 + 90 + 87) + (92 + 85 + 90 + 88) + (83 + 87 + 90 + 92) = 1067<br>आवश्यक % = <math display=\"inline\"><mfrac><mrow><mn>325</mn></mrow><mrow><mn>1067</mn></mrow></mfrac></math> &times; 100 = 30.46%</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "5",
                    section: "17",
                    question_en: "<p>5. Study the given table and answer the question that follows.<br>The table shows the yearly production (in thousands) of scooters in five different factories (P, Q, R, S and T) from 1985 to 1989.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1737368850641.png\" alt=\"rId12\" width=\"288\" height=\"145\"> <br>The ratio of the production of scooters by factory P to that by factory T in 1985 is:</p>",
                    question_hi: "<p>5. दी गई तालिका का अध्ययन करें और निम्नलिखित प्रश्न का उत्तर दें। <br>तालिका में 1985 से 1989 तक पांच अलग-अलग कारखानों (P, Q, R, S और T) में स्कूटरों का वार्षिक उत्पादन (हजार में) दर्शाया गया है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1737368850753.png\" alt=\"rId13\" width=\"274\" height=\"147\"> <br>1985 में फैक्ट्री P और फैक्ट्री T द्वारा स्कूटरों के उत्पादन का अनुपात क्या है ?</p>",
                    options_en: ["<p>3 : 2</p>", "<p>2 : 3</p>", 
                                "<p>1 : 2</p>", "<p>2 : 1</p>"],
                    options_hi: ["<p>3 : 2</p>", "<p>2 : 3</p>",
                                "<p>1 : 2</p>", "<p>2 : 1</p>"],
                    solution_en: "<p>5.(c)<br>Required ratio = 20 : 40 = 1 : 2</p>",
                    solution_hi: "<p>5.(c)<br>आवश्यक अनुपात = 20 : 40 = 1 : 2</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "6",
                    section: "17",
                    question_en: "<p>6. The following table shows the Gross Turnover (in Crores) of a company over five years:<br>The Gross Turnover for 2017 is what percent of the Gross Turnover for 2019?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1737368850910.png\" alt=\"rId14\" width=\"171\" height=\"124\"></p>",
                    question_hi: "<p>6. निम्न तालिका पाँच वर्षों में एक कंपनी का सकल टर्नओवर (करोड़ों में) दर्शाती है:<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1737368851016.png\" alt=\"rId15\" width=\"155\" height=\"133\"> <br>2017 का सकल टर्नओवर. 2019 के सकल टर्नओवर का कितना प्रतिशत है ?</p>",
                    options_en: ["<p>6</p>", "<p>60</p>", 
                                "<p>0.06</p>", "<p>16</p>"],
                    options_hi: ["<p>6</p>", "<p>60</p>",
                                "<p>0.06</p>", "<p>16</p>"],
                    solution_en: "<p>6.(b)<br>Required % = <math display=\"inline\"><mfrac><mrow><mn>15</mn></mrow><mrow><mn>25</mn></mrow></mfrac></math> &times; 100 = 60 %</p>",
                    solution_hi: "<p>6.(b)<br>आवश्यक % = <math display=\"inline\"><mfrac><mrow><mn>15</mn></mrow><mrow><mn>25</mn></mrow></mfrac></math> &times; 100 = 60 %</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "7",
                    section: "17",
                    question_en: "<p>7. Study the given table and answer the question that follows.<br>The table shows the number of voters at three different centres A, B, C.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1737368851207.png\" alt=\"rId16\" width=\"466\" height=\"102\"> <br>The number of people who did not vote at centre B is what percentage more than those who did not vote at centre C ?</p>",
                    question_hi: "<p>7. दी गई तालिका का अध्ययन कीजिए और नीचे दिए गए प्रश्न का उत्तर दीजिए।<br>तालिका तीन अलग-अलग केंद्रों A, B, C पर मतदाताओं की संख्या दर्शाती है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1737368851327.png\" alt=\"rId17\" width=\"415\" height=\"111\"> <br>केंद्र B पर मतदान नहीं करने वालों की संख्या, केंद्र C पर मतदान नहीं करने वालों की संख्या से कितने प्रतिशत अधिक है?</p>",
                    options_en: ["<p>66<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>13</mn></mrow></mfrac></math>%</p>", "<p>66<math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>13</mn></mrow></mfrac></math>%</p>", 
                                "<p>66<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>13</mn></mrow></mfrac></math>%</p>", "<p>66<math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>13</mn></mrow></mfrac></math>%</p>"],
                    options_hi: ["<p>66<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>13</mn></mrow></mfrac></math>%</p>", "<p>66<math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>13</mn></mrow></mfrac></math>%</p>",
                                "<p>66<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>13</mn></mrow></mfrac></math>%</p>", "<p>66<math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>13</mn></mrow></mfrac></math>%</p>"],
                    solution_en: "<p>7.(a)<br>Total voter who did not vote at centre B = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>30</mn><mn>100</mn></mfrac></math>&times; 5400 = 1620<br>Total voter who did not vote at centre C = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>15</mn><mn>100</mn></mfrac></math>&times; 6500 = 975<br>Required % = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>1620</mn><mo>-</mo><mn>975</mn><mo>)</mo></mrow><mn>975</mn></mfrac></math> &times; 100<br>=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>645</mn><mn>975</mn></mfrac></math> &times; 100 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>860</mn><mn>13</mn></mfrac></math> = 66<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>13</mn></mfrac></math> %</p>",
                    solution_hi: "<p>7.(a)<br>केंद्र B पर मतदान न करने वाले कुल मतदाता = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>30</mn><mn>100</mn></mfrac></math>&times; 5400 = 1620<br>केंद्र C पर मतदान न करने वाले कुल मतदाता = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>15</mn><mn>100</mn></mfrac></math>&times; 6500 = 975<br>आवश्यक % = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>1620</mn><mo>-</mo><mn>975</mn><mo>)</mo></mrow><mn>975</mn></mfrac></math> &times; 100<br>=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>645</mn><mn>975</mn></mfrac></math> &times; 100 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>860</mn><mn>13</mn></mfrac></math> = 66<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>13</mn></mfrac></math> %</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "8",
                    section: "17",
                    question_en: "<p>8. The following table shows the shares traded on Mumbai, Rajasthan, Uttar Pradesh and Uttarakhand stock exchanges:<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1737368851478.png\" alt=\"rId18\" width=\"496\" height=\"102\"> <br>For Jmbuja cement, the ratio of the high rate of share to the low rate is maximum in the stock exchange at:</p>",
                    question_hi: "<p>8. निम्नलिखित तालिका मुंबई, राजस्थान, उत्तर प्रदेश और उत्तराखंड स्टॉक एक्सचेंजों में क्रय-विक्रय (ट्रेड) किए गए शेयरों को दर्शाती है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1737368851826.png\" alt=\"rId19\" width=\"462\" height=\"111\"> <br>जंबुजा सीमेंट के लिए, शेयर की उच्च दर और निम्न दर का अनुपात किस स्टॉक एक्सचेंज में अधिकतम है ?</p>",
                    options_en: ["<p>Mumbai</p>", "<p>Rajasthan</p>", 
                                "<p>Uttarakhand</p>", "<p>Uttar Pradesh</p>"],
                    options_hi: ["<p>मुंबई</p>", "<p>राजस्थान</p>",
                                "<p>उत्तराखंड</p>", "<p>उत्तर प्रदेश</p>"],
                    solution_en: "<p>8.(b) <br>For Jmbuja cement,<br>Ratio of high to low in mumbai = <math display=\"inline\"><mfrac><mrow><mn>150</mn></mrow><mrow><mn>155</mn></mrow></mfrac></math> = 0.96<br>Ratio of high to low in Uttarakhand = <math display=\"inline\"><mfrac><mrow><mn>120</mn></mrow><mrow><mn>125</mn></mrow></mfrac></math> = 0.96<br>Ratio of high to low in Rajasthan = <math display=\"inline\"><mfrac><mrow><mn>160</mn><mi>&#160;</mi></mrow><mrow><mn>135</mn></mrow></mfrac></math> = 1.185<br>Ratio of high to low in uttar pradesh = <math display=\"inline\"><mfrac><mrow><mn>145</mn></mrow><mrow><mn>170</mn></mrow></mfrac></math> = 0.85</p>",
                    solution_hi: "<p>8.(b) <br>जंबुजा सीमेंट के लिए,<br>मुंबई में उच्च से निम्न का अनुपात = <math display=\"inline\"><mfrac><mrow><mn>150</mn></mrow><mrow><mn>155</mn></mrow></mfrac></math> = 0.96<br>उत्तराखंड में उच्च से निम्न का अनुपात = <math display=\"inline\"><mfrac><mrow><mn>120</mn></mrow><mrow><mn>125</mn></mrow></mfrac></math> = 0.96<br>राजस्थान में उच्च से निम्न का अनुपात = <math display=\"inline\"><mfrac><mrow><mn>160</mn><mi>&#160;</mi></mrow><mrow><mn>135</mn></mrow></mfrac></math> = 1.185<br>उत्तर प्रदेश में उच्च से निम्न का अनुपात = <math display=\"inline\"><mfrac><mrow><mn>145</mn></mrow><mrow><mn>170</mn></mrow></mfrac></math> = 0.85</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "9",
                    section: "17",
                    question_en: "<p>9. Study the given pie-graph and answer the question that follows.<br>In a certain company, allocations to various sectors of the yearly budget per ₹7,200 crores are represented by this pie-diagram.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1737368852055.png\" alt=\"rId20\" width=\"269\" height=\"253\"> <br>The expenditure (in ₹) on Infrastructure is:</p>",
                    question_hi: "<p>9. दिए गए पाई-ग्राफ का अध्ययन कीजिए और निम्नलिखित प्रश्न का उत्तर दीजिए।<br>इस पाई-आरेख में किसी निश्चित कंपनी में, वार्षिक बजट के विभिन्न क्षेत्रों के लिए ₹7,200 करोड़ का आवंटन दर्शाया गया है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1737368852166.png\" alt=\"rId21\" width=\"268\" height=\"240\"> <br>इंफ्रास्ट्रक्चर पर व्यय (₹ में) _____है।</p>",
                    options_en: ["<p>₹1,694 crores</p>", "<p>₹6,089 crores</p>", 
                                "<p>₹6,098 crores</p>", "<p>₹1,649 crores</p>"],
                    options_hi: ["<p>₹1,694 करोड़</p>", "<p>₹6,089 करोड़</p>",
                                "<p>₹6,098 करोड़</p>", "<p>₹1,649 करोड़</p>"],
                    solution_en: "<p>9.(a)<br>Expenditure on Infrastructure = <math display=\"inline\"><mfrac><mrow><mn>84</mn><mo>.</mo><msup><mrow><mn>7</mn></mrow><mrow><mi>o</mi></mrow></msup></mrow><mrow><mn>36</mn><msup><mrow><mn>0</mn></mrow><mrow><mi>o</mi></mrow></msup></mrow></mfrac></math> &times; 72,00 = ₹1694 crore</p>",
                    solution_hi: "<p>9.(a)<br>इंफ्रास्ट्रक्चर पर खर्च = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>84</mn><mo>.</mo><msup><mn>7</mn><mi>o</mi></msup></mrow><mrow><mn>36</mn><msup><mn>0</mn><mi>o</mi></msup></mrow></mfrac></math> &times; 72,00 = ₹1694 करोड़</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "10",
                    section: "17",
                    question_en: "<p>10. Study the given table and answer the question that follows.<br>The table shows the production of five different types of cars by a company from 1989 to 1994.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1737368852332.png\" alt=\"rId22\" width=\"374\" height=\"159\"> <br>Which type of car constituted 25% of the total production of all types of cars in 1993 ?</p>",
                    question_hi: "<p>10. दी गई तालिका का अध्ययन करें और निम्नलिखित प्रश्न का उत्तर दें। <br>तालिका में 1989 से 1994 तक एक कंपनी द्वारा पांच विभिन्न प्रकार की कारों के उत्पादन को दर्शाया गया है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1737368852440.png\" alt=\"rId23\" width=\"347\" height=\"178\"> <br>1993 में सभी प्रकार की कारों के कुल उत्पादन का 25% भाग किस प्रकार की कार का था ?</p>",
                    options_en: ["<p>R</p>", "<p>P</p>", 
                                "<p>Q</p>", "<p>S</p>"],
                    options_hi: ["<p>R</p>", "<p>P</p>",
                                "<p>Q</p>", "<p>S</p>"],
                    solution_en: "<p>10.(d)<br>25% of all type of car produce in 1993 = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> &times; 80 = 20 cars<br>We can see that in company S car production is 25% of total cars produced of all companies in 1993.</p>",
                    solution_hi: "<p>10.(d)<br>1993 में सभी प्रकार की कारों के उत्पादन का 25% = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> &times; 80 = 20 कारें<br>हम देख सकते हैं कि कंपनी S में कार का उत्पादन 1993 में सभी कंपनियों द्वारा उत्पादित कुल कारों का 25% है।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "11",
                    section: "17",
                    question_en: "<p>11. The following table gives the number of males, females, educated males and educated females in a village over the years 2016 - 2020. Study the table carefully and answer the question.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1737368852543.png\" alt=\"rId24\" width=\"358\" height=\"119\"> <br>Find the percentage of the total number of educated females to the total number of females over all the years (up to 2 decimal places).</p>",
                    question_hi: "<p>11. निम्नलिखित तालिका वर्ष 2016-2020 के दौरान एक गाँव में पुरुषों, महिलाओं, शिक्षित पुरुषों और शिक्षित महिलाओं की संख्या प्रदर्शित करती है। तालिका का ध्यानपूर्वक अध्ययन करें और प्रश्न का उत्तर दें।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1737368852687.png\" alt=\"rId25\" width=\"310\" height=\"138\"> <br>सभी वर्षों में शिक्षित महिलाओं की कुल संख्या का कुल महिलाओं की संख्या से प्रतिशत ज्ञात कीजिए। (2 दशमलव स्थानों तक)</p>",
                    options_en: ["<p>0.9233</p>", "<p>0.8333</p>", 
                                "<p>0.8923</p>", "<p>0.8623</p>"],
                    options_hi: ["<p>0.9233</p>", "<p>0.8333</p>",
                                "<p>0.8923</p>", "<p>0.8623</p>"],
                    solution_en: "<p>11.(b)<br>Total educated females = 600 + 820 + 950 + 980 + 1000 = 4350<br>Total females = 900 + 1000 + 1020 + 1100 + 1200 = 5220<br>Required % = <math display=\"inline\"><mfrac><mrow><mn>4350</mn></mrow><mrow><mn>5220</mn></mrow></mfrac></math> = 0.8333</p>",
                    solution_hi: "<p>11.(b)<br>कुल शिक्षित महिलाएँ = 600 + 820 + 950 + 980 + 1000 = 4350<br>कुल महिलाएँ = 900 + 1000 + 1020 + 1100 + 1200 = 5220<br>आवश्यक % = <math display=\"inline\"><mfrac><mrow><mn>4350</mn></mrow><mrow><mn>5220</mn></mrow></mfrac></math> = 0.8333</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "12",
                    section: "17",
                    question_en: "<p>12. The table below shows the number of mobile phones that were sold by the show rooms in the years from 2016-2020.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1737368852901.png\" alt=\"rId26\" width=\"347\" height=\"148\"> <br>What will be the average number of mobile phones sold by all the show rooms in the year 2020 ?</p>",
                    question_hi: "<p>12. नीचे दी गई तालिका में वर्ष 2016-2020 के दौरान शोरूमों द्वारा बेचे गए मोबाइल फोन की संख्या दर्शाई गई है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1737368852999.png\" alt=\"rId27\" width=\"281\" height=\"154\"> <br>वर्ष 2020 में सभी शोरूमों द्वारा बेचे गए मोबाइल फोन की औसत संख्या कितनी होगी ?</p>",
                    options_en: ["<p>405</p>", "<p>420</p>", 
                                "<p>410</p>", "<p>415</p>"],
                    options_hi: ["<p>405</p>", "<p>420</p>",
                                "<p>410</p>", "<p>415</p>"],
                    solution_en: "<p>12.(b)<br>Average number of mobile phones sold by all showroom in 2020 = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>Total</mi><mi mathvariant=\"normal\">&#160;</mi><mi>mobile</mi><mi mathvariant=\"normal\">&#160;</mi><mi>phones</mi><mi mathvariant=\"normal\">&#160;</mi><mi>sold</mi><mi mathvariant=\"normal\">&#160;</mi><mi>in</mi><mi mathvariant=\"normal\">&#160;</mi><mn>2020</mn></mrow><mn>6</mn></mfrac></math><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>420</mn><mo>+</mo><mn>430</mn><mo>+</mo><mn>410</mn><mo>+</mo><mn>440</mn><mo>+</mo><mn>400</mn><mo>+</mo><mn>420</mn></mrow><mn>6</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2520</mn><mn>6</mn></mfrac></math> = 420</p>",
                    solution_hi: "<p>12.(b)<br>2020 में सभी शोरूम द्वारा बेचे गए मोबाइल फोन की औसत संख्या = <math style=\"font-family:Verdana\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2020</mn><mo>&#160;</mo><mi>&#2350;&#2375;&#2306;</mi><mo>&#160;</mo><mi>&#2348;&#2375;&#2330;&#2375;</mi><mo>&#160;</mo><mi>&#2327;&#2319;</mi><mo>&#160;</mo><mi>&#2325;&#2369;&#2354;</mi><mo>&#160;</mo><mi>&#2350;&#2379;&#2348;&#2366;&#2311;&#2354;</mi><mo>&#160;</mo><mi>&#2347;&#2379;&#2344;</mi></mrow><mn>6</mn></mfrac></math><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>420</mn><mo>+</mo><mn>430</mn><mo>+</mo><mn>410</mn><mo>+</mo><mn>440</mn><mo>+</mo><mn>400</mn><mo>+</mo><mn>420</mn></mrow><mn>6</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2520</mn><mn>6</mn></mfrac></math> = 420</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "13",
                    section: "17",
                    question_en: "<p>13. The following table gives information of Panchayat elections held in four villages P, Q, R and S.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1737368853153.png\" alt=\"rId28\" width=\"349\" height=\"111\"> <br>Find the ratio of invalid votes of village P to that of village S.</p>",
                    question_hi: "<p>13. निम्नलिखित तालिका में चार गाँवों P, Q, R और S में हुए पंचायत के चुनावों की जानकारी दी गई है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1737368853315.png\" alt=\"rId29\" width=\"351\" height=\"105\"> <br>गाँव P के अवैध मतों और गाँव S के अवैध मतों का अनुपात ज्ञात कीजिए।</p>",
                    options_en: ["<p>3 ∶ 4</p>", "<p>1 ∶ 2</p>", 
                                "<p>1 ∶ 3</p>", "<p>2 ∶ 3</p>"],
                    options_hi: ["<p>3 ∶ 4</p>", "<p>1 ∶ 2</p>",
                                "<p>1 ∶ 3</p>", "<p>2 ∶ 3</p>"],
                    solution_en: "<p>13.(c)<br>Invalid votes of village P = 40 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>80</mn><mrow><mn>100</mn><mo>&#160;</mo></mrow></mfrac><mo>&#215;</mo><mfrac><mn>10</mn><mn>100</mn></mfrac></math><br>Invalid votes of village S = 80 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>80</mn><mn>100</mn></mfrac><mo>&#215;</mo><mfrac><mn>15</mn><mn>100</mn></mfrac></math><br>Ratio =&nbsp;<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>40</mn><mo>&#215;</mo><mfrac><mn>80</mn><mn>100</mn></mfrac><mo>&#215;</mo><mfrac><mn>10</mn><mn>100</mn></mfrac></mrow><mrow><mi>&#160;</mi><mn>80</mn><mo>&#215;</mo><mfrac><mn>80</mn><mn>100</mn></mfrac><mo>&#215;</mo><mfrac><mn>15</mn><mn>100</mn></mfrac></mrow></mfrac></math> = 1 : 3</p>",
                    solution_hi: "<p>13.(c)<br>ग्राम P के अवैध वोट = 40 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>80</mn><mrow><mn>100</mn><mo>&#160;</mo></mrow></mfrac><mo>&#215;</mo><mfrac><mn>10</mn><mn>100</mn></mfrac></math><br>ग्राम S के अवैध वोट = 80 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>80</mn><mn>100</mn></mfrac><mo>&#215;</mo><mfrac><mn>15</mn><mn>100</mn></mfrac></math><br>अनुपात = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>40</mn><mo>&#215;</mo><mfrac><mn>80</mn><mn>100</mn></mfrac><mo>&#215;</mo><mfrac><mn>10</mn><mn>100</mn></mfrac></mrow><mrow><mi>&#160;</mi><mn>80</mn><mo>&#215;</mo><mfrac><mn>80</mn><mn>100</mn></mfrac><mo>&#215;</mo><mfrac><mn>15</mn><mn>100</mn></mfrac></mrow></mfrac></math> = 1 : 3</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "14",
                    section: "17",
                    question_en: "<p>14. Find the mean from the following table (rounded off to two decimal places).<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1737368853465.png\" alt=\"rId30\" width=\"139\" height=\"153\"></p>",
                    question_hi: "<p>14. निम्न तालिका से माध्य ज्ञात कीजिए (दशमलव के दो स्थानों तक पूर्णांकित)।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1737368853630.png\" alt=\"rId31\" width=\"116\" height=\"163\"></p>",
                    options_en: ["<p>18.44</p>", "<p>17.56</p>", 
                                "<p>20.82</p>", "<p>19.78</p>"],
                    options_hi: ["<p>18.44</p>", "<p>17.56</p>",
                                "<p>20.82</p>", "<p>19.78</p>"],
                    solution_en: "<p>14.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1737368853801.png\" alt=\"rId32\" width=\"267\" height=\"189\"><br>Mean = <math display=\"inline\"><mfrac><mrow><mi>&#931;</mi><msub><mrow><mi>x</mi></mrow><mrow><mi>i</mi></mrow></msub><msub><mrow><mi>f</mi></mrow><mrow><mi>i</mi></mrow></msub></mrow><mrow><msub><mrow><mi>&#931;</mi><mi>x</mi></mrow><mrow><mi>i</mi></mrow></msub></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>356</mn><mn>18</mn></mfrac></math> = 19.78</p>",
                    solution_hi: "<p>14.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1737368853931.png\" alt=\"rId33\" width=\"227\" height=\"189\"><br>माध्य = <math display=\"inline\"><mfrac><mrow><mi>&#931;</mi><msub><mrow><mi>x</mi></mrow><mrow><mi>i</mi></mrow></msub><msub><mrow><mi>f</mi></mrow><mrow><mi>i</mi></mrow></msub></mrow><mrow><msub><mrow><mi>&#931;</mi><mi>x</mi></mrow><mrow><mi>i</mi></mrow></msub></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>356</mn><mn>18</mn></mfrac></math> = 19.78</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "15",
                    section: "misc",
                    question_en: "<p>15. Study the given table and answer the question that follows.<br>The table shows the production of five different types of cars by a company from 1989 to 1994.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1737368854061.png\" alt=\"rId34\" width=\"324\" height=\"146\"> <br>In which year was the production of cars of all types taken together approximately equal to the average of the total production during the period ?</p>",
                    question_hi: "<p>15. दी गई तालिका का अध्ययन करें और निम्नलिखित प्रश्न का उत्तर दें।<br>तालिका में 1989 से 1994 तक एक कंपनी द्वारा पांच विभिन्न प्रकार की कारों के उत्पादन को दर्शाया गया है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1737368854199.png\" alt=\"rId35\" width=\"354\" height=\"159\"> <br>किस वर्ष में सभी प्रकार की कारों का उत्पादन उस अवधि के दौरान हुए कुल उत्पादन के औसत के लगभग बराबर था ?</p>",
                    options_en: [" 1990", " 1992", 
                                " 1991", " 1993"],
                    options_hi: [" 1990", " 1992",
                                " 1991", " 1993"],
                    solution_en: "15.(d)<br />Average of total production = <math display=\"inline\"><mfrac><mrow><mn>476</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math> = 79.5<br />Hence, total production in 1993 is approximately equal to the average of total production.",
                    solution_hi: "15.(d)<br />कुल उत्पादन का औसत = <math display=\"inline\"><mfrac><mrow><mn>476</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math> = 79.5<br />अतः, 1993 में कुल उत्पादन लगभग कुल उत्पादन के औसत के बराबर है।",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>