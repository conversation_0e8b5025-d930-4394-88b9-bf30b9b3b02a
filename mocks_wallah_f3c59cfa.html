<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">60:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 70</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">70</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 60 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["7"] = {
                name: "English",
                start: 0,
                end: 43
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="7">English</option>`;
            sections["18"] = {
                name: "General Awareness",
                start: 44,
                end: 68
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="18">General Awareness</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 69,
                end: 69
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "7",
                    question_en: "<p>1. <strong>Comprehension :</strong><br>India was one of the first places where human civilization started. Around 5000 years BC this civilization was located around the Indus River. Hence, it is known as Indus Valley civilization. It is also known as Harappan Civilization because records show one of the city\'s names was Harappa. It was 1 of 1500 cities in the Indus River Valley, another well-known city being Mohenjo-Daro. It wasn\'t until 1920 that the civilization was discovered at all. There were four known major cities, Harappa, Mohenjo-Daro, Mehrgarh, and Lothal.<br>Mohenjo-daro was not only the largest city of the Indus Valley Civilization but also one of the world&rsquo;s earliest major urban centers. Much of the cities were made from clay bricks baked in a furnace known as a kiln. The cities were advanced for their time, having their own wells and water drainage systems as well as having grid-style roads in the cities.They were also noted for clusters of large, nonresidential buildings. But most people did not live in the urban areas and instead lived in the local farming villages.<br>Though little is known about the Indus Valley people, we do know a little about their farming habits. They had domesticated peas, wheat, melons, dates, sesame seeds, as well as cotton.<br>They had advanced knowledge of the process of drying wheat, and a large granary was discovered, a building whose sole purpose is to hold and dry large amounts of wheat. They also created some large structures, one of the largest being a public pool known as the Great Bath that was 40 feet long, 10 feet deep, and 20 feet wide.<br>Which of the following statements shows that the cities of Indus Valley were well planned?</p>",
                    question_hi: "<p>1. <strong>Comprehension :</strong><br>India was one of the first places where human civilization started. Around 5000 years BC this civilization was located around the Indus River. Hence, it is known as Indus Valley civilization. It is also known as Harappan Civilization because records show one of the city\'s names was Harappa. It was 1 of 1500 cities in the Indus River Valley, another well-known city being Mohenjo-Daro. It wasn\'t until 1920 that the civilization was discovered at all. There were four known major cities, Harappa, Mohenjo-Daro, Mehrgarh, and Lothal.<br>Mohenjo-daro was not only the largest city of the Indus Valley Civilization but also one of the world&rsquo;s earliest major urban centers. Much of the cities were made from clay bricks baked in a furnace known as a kiln. The cities were advanced for their time, having their own wells and water drainage systems as well as having grid-style roads in the cities.They were also noted for clusters of large, nonresidential buildings. But most people did not live in the urban areas and instead lived in the local farming villages.<br>Though little is known about the Indus Valley people, we do know a little about their farming habits. They had domesticated peas, wheat, melons, dates, sesame seeds, as well as cotton.<br>They had advanced knowledge of the process of drying wheat, and a large granary was discovered, a building whose sole purpose is to hold and dry large amounts of wheat. They also created some large structures, one of the largest being a public pool known as the Great Bath that was 40 feet long, 10 feet deep, and 20 feet wide.<br>Which of the following statements shows that the cities of Indus Valley were well planned?</p>",
                    options_en: ["<p>The structures were made of baked bricks.</p>", "<p>The cities had grid-style roads and water drainage system.</p>", 
                                "<p>There were clusters of large non residential buildings.</p>", "<p>There were wells for water in the Cities.</p>"],
                    options_hi: ["<p>The structures were made of baked bricks.</p>", "<p>The cities had grid-style roads and water drainage system.</p>",
                                "<p>There were clusters of large non residential buildings.</p>", "<p>There were wells for water in the Cities.</p>"],
                    solution_en: "<p>1.(b) The cities had grid-style roads and water drainage system.</p>",
                    solution_hi: "<p>1.(b)&nbsp;<br>The cities had grid-style roads and water drainage system./शहरों में ग्रिड-शैली की सड़कें और पानी की जल निकासी प्रणाली थी I</p>\n<p>&nbsp;</p>",
                    correct: "b",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "2",
                    section: "7",
                    question_en: "<p>2. <strong>Comprehension</strong> <strong>:</strong><br>India was one of the first places where human civilization started. Around 5000 years BC this civilization was located around the Indus River. Hence, it is known as Indus Valley civilization. It is also known as Harappan Civilization because records show one of the city\'s names was Harappa. It was 1 of 1500 cities in the Indus River Valley, another well-known city being Mohenjo-Daro. It wasn\'t until 1920 that the civilization was discovered at all. There were four known major cities, Harappa, Mohenjo-Daro, Mehrgarh, and Lothal.<br>Mohenjo-daro was not only the largest city of the Indus Valley Civilization but also one of the world&rsquo;s earliest major urban centers. Much of the cities were made from clay bricks baked in a furnace known as a kiln. The cities were advanced for their time, having their own wells and water drainage systems as well as having grid-style roads in the cities.They were also noted for clusters of large, nonresidential buildings. But most people did not live in the urban areas and instead lived in the local farming villages.<br>Though little is known about the Indus Valley people, we do know a little about their farming habits. They had domesticated peas, wheat, melons, dates, sesame seeds, as well as cotton.<br>They had advanced knowledge of the process of drying wheat, and a large granary was discovered, a building whose sole purpose is to hold and dry large amounts of wheat. They also created some large structures, one of the largest being a public pool known as the Great Bath that was 40 feet long, 10 feet deep, and 20 feet wide.<br>Which was the largest city of the Indus Valley civilization?</p>",
                    question_hi: "<p>2. <strong>Comprehension :</strong><br>India was one of the first places where human civilization started. Around 5000 years BC this civilization was located around the Indus River. Hence, it is known as Indus Valley civilization. It is also known as Harappan Civilization because records show one of the city\'s names was Harappa. It was 1 of 1500 cities in the Indus River Valley, another well-known city being Mohenjo-Daro. It wasn\'t until 1920 that the civilization was discovered at all. There were four known major cities, Harappa, Mohenjo-Daro, Mehrgarh, and Lothal.<br>Mohenjo-daro was not only the largest city of the Indus Valley Civilization but also one of the world&rsquo;s earliest major urban centers. Much of the cities were made from clay bricks baked in a furnace known as a kiln. The cities were advanced for their time, having their own wells and water drainage systems as well as having grid-style roads in the cities.They were also noted for clusters of large, nonresidential buildings. But most people did not live in the urban areas and instead lived in the local farming villages.<br>Though little is known about the Indus Valley people, we do know a little about their farming habits. They had domesticated peas, wheat, melons, dates, sesame seeds, as well as cotton.<br>They had advanced knowledge of the process of drying wheat, and a large granary was discovered, a building whose sole purpose is to hold and dry large amounts of wheat. They also created some large structures, one of the largest being a public pool known as the Great Bath that was 40 feet long, 10 feet deep, and 20 feet wide.<br>Which was the largest city of the Indus Valley civilization?</p>",
                    options_en: ["<p>Lothal</p>", "<p>Harappa</p>", 
                                "<p>Mehrgarh</p>", "<p>Mohenjo-Daro</p>"],
                    options_hi: ["<p>Lothal</p>", "<p>Harappa</p>",
                                "<p>Mehrgarh</p>", "<p>Mohenjo-Daro</p>"],
                    solution_en: "<p>2.(d) &ldquo;Mohenjo-Daro&rdquo;<br>It is given in the second paragraph of the passage that Mohenjo-Daro was the largest city of the Indus Valley civilization.</p>",
                    solution_hi: "<p>2.(d) \"Mohenjo-Daro\"<br>Passage के दूसरे paragraph में बताया गया है कि मोहनजोदड़ो, सिंधु घाटी सभ्यता का सबसे बड़ा शहर था।</p>",
                    correct: "d",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "3",
                    section: "7",
                    question_en: "<p>3. <strong>Comprehension :</strong><br>India was one of the first places where human civilization started. Around 5000 years BC this civilization was located around the Indus River. Hence, it is known as Indus Valley civilization. It is also known as Harappan Civilization because records show one of the city\'s names was Harappa. It was 1 of 1500 cities in the Indus River Valley, another well-known city being Mohenjo-Daro. It wasn\'t until 1920 that the civilization was discovered at all. There were four known major cities, Harappa, Mohenjo-Daro, Mehrgarh, and Lothal.<br>Mohenjo-daro was not only the largest city of the Indus Valley Civilization but also one of the world&rsquo;s earliest major urban centers. Much of the cities were made from clay bricks baked in a furnace known as a kiln. The cities were advanced for their time, having their own wells and water drainage systems as well as having grid-style roads in the cities.They were also noted for clusters of large, nonresidential buildings. But most people did not live in the urban areas and instead lived in the local farming villages.<br>Though little is known about the Indus Valley people, we do know a little about their farming habits. They had domesticated peas, wheat, melons, dates, sesame seeds, as well as cotton.<br>They had advanced knowledge of the process of drying wheat, and a large granary was discovered, a building whose sole purpose is to hold and dry large amounts of wheat. They also created some large structures, one of the largest being a public pool known as the Great Bath that was 40 feet long, 10 feet deep, and 20 feet wide.<br>The passage belongs to the subject area of</p>",
                    question_hi: "<p>3. <strong>Comprehension :</strong><br>India was one of the first places where human civilization started. Around 5000 years BC this civilization was located around the Indus River. Hence, it is known as Indus Valley civilization. It is also known as Harappan Civilization because records show one of the city\'s names was Harappa. It was 1 of 1500 cities in the Indus River Valley, another well-known city being Mohenjo-Daro. It wasn\'t until 1920 that the civilization was discovered at all. There were four known major cities, Harappa, Mohenjo-Daro, Mehrgarh, and Lothal.<br>Mohenjo-daro was not only the largest city of the Indus Valley Civilization but also one of the world&rsquo;s earliest major urban centers. Much of the cities were made from clay bricks baked in a furnace known as a kiln. The cities were advanced for their time, having their own wells and water drainage systems as well as having grid-style roads in the cities.They were also noted for clusters of large, nonresidential buildings. But most people did not live in the urban areas and instead lived in the local farming villages.<br>Though little is known about the Indus Valley people, we do know a little about their farming habits. They had domesticated peas, wheat, melons, dates, sesame seeds, as well as cotton.<br>They had advanced knowledge of the process of drying wheat, and a large granary was discovered, a building whose sole purpose is to hold and dry large amounts of wheat. They also created some large structures, one of the largest being a public pool known as the Great Bath that was 40 feet long, 10 feet deep, and 20 feet wide.<br>The passage belongs to the subject area of</p>",
                    options_en: ["<p>geography</p>", "<p>history</p>", 
                                "<p>civics</p>", "<p>architecture</p>"],
                    options_hi: ["<p>geography</p>", "<p>history</p>",
                                "<p>civics</p>", "<p>architecture</p>"],
                    solution_en: "<p>3.(b) history<br>The passage belongs to the subject area of history.</p>",
                    solution_hi: "<p>3.(b) history<br>यह Passage &lsquo;इतिहास के विषय क्षेत्र&rsquo; से संबंधित है।</p>",
                    correct: "b",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "4",
                    section: "7",
                    question_en: "<p>4. <strong>Comprehension</strong> :<br>India was one of the first places where human civilization started. Around 5000 years BC this civilization was located around the Indus River. Hence, it is known as Indus Valley civilization. It is also known as Harappan Civilization because records show one of the city\'s names was Harappa. It was 1 of 1500 cities in the Indus River Valley, another well-known city being Mohenjo-Daro. It wasn\'t until 1920 that the civilization was discovered at all. There were four known major cities, Harappa, Mohenjo-Daro, Mehrgarh, and Lothal.<br>Mohenjo-daro was not only the largest city of the Indus Valley Civilization but also one of the world&rsquo;s earliest major urban centers. Much of the cities were made from clay bricks baked in a furnace known as a kiln. The cities were advanced for their time, having their own wells and water drainage systems as well as having grid-style roads in the cities.They were also noted for clusters of large, nonresidential buildings. But most people did not live in the urban areas and instead lived in the local farming villages.<br>Though little is known about the Indus Valley people, we do know a little about their farming habits. They had domesticated peas, wheat, melons, dates, sesame seeds, as well as cotton.<br>They had advanced knowledge of the process of drying wheat, and a large granary was discovered, a building whose sole purpose is to hold and dry large amounts of wheat. They also created some large structures, one of the largest being a public pool known as the Great Bath that was 40 feet long, 10 feet deep, and 20 feet wide.Which discovery indicated that people of Indus valley stored large amount of grain?</p>",
                    question_hi: "<p>4. <strong>Comprehension</strong> :<br>India was one of the first places where human civilization started. Around 5000 years BC this civilization was located around the Indus River. Hence, it is known as Indus Valley civilization. It is also known as Harappan Civilization because records show one of the city\'s names was Harappa. It was 1 of 1500 cities in the Indus River Valley, another well-known city being Mohenjo-Daro. It wasn\'t until 1920 that the civilization was discovered at all. There were four known major cities, Harappa, Mohenjo-Daro, Mehrgarh, and Lothal.<br>Mohenjo-daro was not only the largest city of the Indus Valley Civilization but also one of the world&rsquo;s earliest major urban centers. Much of the cities were made from clay bricks baked in a furnace known as a kiln. The cities were advanced for their time, having their own wells and water drainage systems as well as having grid-style roads in the cities.They were also noted for clusters of large, nonresidential buildings. But most people did not live in the urban areas and instead lived in the local farming villages.<br>Though little is known about the Indus Valley people, we do know a little about their farming habits. They had domesticated peas, wheat, melons, dates, sesame seeds, as well as cotton.<br>They had advanced knowledge of the process of drying wheat, and a large granary was discovered, a building whose sole purpose is to hold and dry large amounts of wheat. They also created some large structures, one of the largest being a public pool known as the Great Bath that was 40 feet long, 10 feet deep, and 20 feet wide.Which discovery indicated that people of Indus valley stored large amount of grain?</p>",
                    options_en: [" the Great Bath", " brick houses", 
                                " the granary", " clusters of non residential buildings"],
                    options_hi: [" the Great Bath", " brick houses",
                                " the granary", " clusters of non residential buildings\\"],
                    solution_en: "<p>4.(c) the granary<br><strong>(Line/s from the passage- </strong>They had advanced knowledge of the process of drying wheat, and a large granary was discovered, a building whose sole purpose is to hold and dry large amounts of wheat.)</p>",
                    solution_hi: "<p>4.(c) the granary<br><strong>(Passage से ली गई line -</strong> They had advanced knowledge of the process of drying wheat, and a large granary was discovered, a building whose sole purpose is to hold and dry large amounts of wheat/उन्हें गेहूं सुखाने की प्रक्रिया का अच्छा ज्ञान था,और एक बड़े अन्न भंडार की खोज की गयी थी जिसका एकमात्र उद्देश्य बड़ी मात्रा में गेहूं रखना और सुखाना था।)</p>",
                    correct: "c",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "5",
                    section: "7",
                    question_en: "<p>5. <strong>Comprehension</strong> :<br>India was one of the first places where human civilization started. Around 5000 years BC this civilization was located around the Indus River. Hence, it is known as Indus Valley civilization. It is also known as Harappan Civilization because records show one of the city\'s names was Harappa. It was 1 of 1500 cities in the Indus River Valley, another well-known city being Mohenjo-Daro. It wasn\'t until 1920 that the civilization was discovered at all. There were four known major cities, Harappa, Mohenjo-Daro, Mehrgarh, and Lothal.<br>Mohenjo-daro was not only the largest city of the Indus Valley Civilization but also one of the world&rsquo;s earliest major urban centers. Much of the cities were made from clay bricks baked in a furnace known as a kiln. The cities were advanced for their time, having their own wells and water drainage systems as well as having grid-style roads in the cities.They were also noted for clusters of large, nonresidential buildings. But most people did not live in the urban areas and instead lived in the local farming villages.<br>Though little is known about the Indus Valley people, we do know a little about their farming habits. They had domesticated peas, wheat, melons, dates, sesame seeds, as well as cotton.<br>They had advanced knowledge of the process of drying wheat, and a large granary was discovered, a building whose sole purpose is to hold and dry large amounts of wheat. They also created some large structures, one of the largest being a public pool known as the Great Bath that was 40 feet long, 10 feet deep, and 20 feet wide. Which of the following crops were NOT grown by people of Indus Valley?</p>",
                    question_hi: "<p>5. <strong>Comprehension</strong> :<br>India was one of the first places where human civilization started. Around 5000 years BC this civilization was located around the Indus River. Hence, it is known as Indus Valley civilization. It is also known as Harappan Civilization because records show one of the city\'s names was Harappa. It was 1 of 1500 cities in the Indus River Valley, another well-known city being Mohenjo-Daro. It wasn\'t until 1920 that the civilization was discovered at all. There were four known major cities, Harappa, Mohenjo-Daro, Mehrgarh, and Lothal.<br>Mohenjo-daro was not only the largest city of the Indus Valley Civilization but also one of the world&rsquo;s earliest major urban centers. Much of the cities were made from clay bricks baked in a furnace known as a kiln. The cities were advanced for their time, having their own wells and water drainage systems as well as having grid-style roads in the cities.They were also noted for clusters of large, nonresidential buildings. But most people did not live in the urban areas and instead lived in the local farming villages.<br>Though little is known about the Indus Valley people, we do know a little about their farming habits. They had domesticated peas, wheat, melons, dates, sesame seeds, as well as cotton.<br>They had advanced knowledge of the process of drying wheat, and a large granary was discovered, a building whose sole purpose is to hold and dry large amounts of wheat. They also created some large structures, one of the largest being a public pool known as the Great Bath that was 40 feet long, 10 feet deep, and 20 feet wide. Which of the following crops were NOT grown by people of Indus Valley?</p>",
                    options_en: [" cotton", " peas", 
                                " wheat", " sugarcane"],
                    options_hi: [" cotton", " peas",
                                " wheat", " sugarcane"],
                    solution_en: "5.(d) “sugarcane”<br />It can be inferred from the last paragraph of the passage that Sugarcane was the only crop that was not grown by the people of Indus Valley.",
                    solution_hi: "5.(d) “sugarcane”<br />Passage के last paragraph से यह अनुमान लगाया जा सकता है कि गन्ना एकमात्र ऐसी फसल थी जिसे सिंधु घाटी के लोग नहीं उगाते थे।",
                    correct: "d",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "6",
                    section: "7",
                    question_en: "<p>6. <strong>Comprehension</strong> :<br>The small Texas school that I went to had a tradition carried out every year during the eighth- grade graduation: a beautiful gold and green jacket (the school colours) was awarded to the student who had maintained the grade for eight years.<br>The scholarship jacket &mdash; a beautiful gold and green jacket had a big gold S on the left front side and your name written in gold letters on the pocket. My oldest sister, Rosie, had won the jacket a few years back, and I fully expected to also. I had been a straight &lsquo;A&rsquo; student since the first grade and in the eighth grade had looked forward very much to owning that jacket.<br>My father was a farm labourer who couldn&rsquo;t earn enough money to feed eight children, so When I was six I was given to my grandparents to raise. We couldn&rsquo;t participate in sports at school because there were registration fees, uniform costs, and trips out of town; so, even though our family was quite agile and athletic there would never be a school sports jacket for us. This one, the scholarship jacket, was our only chance.<br>Another hour of sweating in basketball was coming up and I was walking up to the gym.<br>Then I remembered my P.E. shorts and I had to walk all the way back and get them. I was almost back at my classroom door when I heard voices raised in anger as if in some sort of argument. I recognised the voices: Mr. Schmidt, my history teacher, and Mr. Boone, my math teacher, seemed to be arguing about me. I heard them say &lsquo;Martha&rsquo; and I couldn&rsquo;t believe it. &ldquo;Martha has a straight A-plus average and you know it.&rdquo; That was Mr. Schmidt and he sounded very angry. &ldquo;I refuse to do it! I don&rsquo;t care who Joann&rsquo;s father is, her grades don&rsquo;t even begin to compare to Martha&rsquo;s,&rdquo; he continued. My heart sank.<br>The narrator says, &ldquo;I looked forward very much to owning that jacket,&rdquo; because:</p>",
                    question_hi: "<p>6. <strong>Comprehension</strong> :<br>The small Texas school that I went to had a tradition carried out every year during the eighth- grade graduation: a beautiful gold and green jacket (the school colours) was awarded to the student who had maintained the grade for eight years.<br>The scholarship jacket &mdash; a beautiful gold and green jacket had a big gold S on the left front side and your name written in gold letters on the pocket. My oldest sister, Rosie, had won the jacket a few years back, and I fully expected to also. I had been a straight &lsquo;A&rsquo; student since the first grade and in the eighth grade had looked forward very much to owning that jacket.<br>My father was a farm labourer who couldn&rsquo;t earn enough money to feed eight children, so When I was six I was given to my grandparents to raise. We couldn&rsquo;t participate in sports at school because there were registration fees, uniform costs, and trips out of town; so, even though our family was quite agile and athletic there would never be a school sports jacket for us. This one, the scholarship jacket, was our only chance.<br>Another hour of sweating in basketball was coming up and I was walking up to the gym.<br>Then I remembered my P.E. shorts and I had to walk all the way back and get them. I was almost back at my classroom door when I heard voices raised in anger as if in some sort of argument. I recognised the voices: Mr. Schmidt, my history teacher, and Mr. Boone, my math teacher, seemed to be arguing about me. I heard them say &lsquo;Martha&rsquo; and I couldn&rsquo;t believe it. &ldquo;Martha has a straight A-plus average and you know it.&rdquo; That was Mr. Schmidt and he sounded very angry. &ldquo;I refuse to do it! I don&rsquo;t care who Joann&rsquo;s father is, her grades don&rsquo;t even begin to compare to Martha&rsquo;s,&rdquo; he continued. My heart sank.<br>The narrator says, &ldquo;I looked forward very much to owning that jacket,&rdquo; because:</p>",
                    options_en: ["<p>she wanted to prove to her teachers that she was good</p>", "<p>she had been an A grade student for 8 years</p>", 
                                "<p>the jacket had the school name on it</p>", "<p>it was a beautiful jacket</p>"],
                    options_hi: ["<p>she wanted to prove to her teachers that she was good</p>", "<p>she had been an A grade student for 8 years</p>",
                                "<p>the jacket had the school name on it</p>", "<p>it was a beautiful jacket</p>"],
                    solution_en: "<p>6.(b) she had been an A grade student for 8 years<br>It is given in the first paragraph of the passage that the narrator says, &ldquo;I looked forward very much to owning that jacket,&rdquo; because she had been an A grade student for 8 years.</p>",
                    solution_hi: "<p>6.(b) she had been an A grade student for 8 years<br>इस passage के पहले paragraph में वक्ता कहता है कि, &ldquo;I looked forward very much to owning that jacket,&rdquo; क्योंकि वह 8 साल से A grade की छात्रा थी।</p>",
                    correct: "b",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "7",
                    section: "7",
                    question_en: "<p>7. <strong>Comprehension</strong> :<br>The small Texas school that I went to had a tradition carried out every year during the eighth- grade graduation: a beautiful gold and green jacket (the school colours) was awarded to the student who had maintained the grade for eight years.<br>The scholarship jacket &mdash; a beautiful gold and green jacket had a big gold S on the left front side and your name written in gold letters on the pocket. My oldest sister, Rosie, had won the jacket a few years back, and I fully expected to also. I had been a straight &lsquo;A&rsquo; student since the first grade and in the eighth grade had looked forward very much to owning that jacket.<br>My father was a farm labourer who couldn&rsquo;t earn enough money to feed eight children, so When I was six I was given to my grandparents to raise. We couldn&rsquo;t participate in sports at school because there were registration fees, uniform costs, and trips out of town; so, even though our family was quite agile and athletic there would never be a school sports jacket for us. This one, the scholarship jacket, was our only chance.<br>Another hour of sweating in basketball was coming up and I was walking up to the gym.<br>Then I remembered my P.E. shorts and I had to walk all the way back and get them. I was almost back at my classroom door when I heard voices raised in anger as if in some sort of argument. I recognised the voices: Mr. Schmidt, my history teacher, and Mr. Boone, my math teacher, seemed to be arguing about me. I heard them say &lsquo;Martha&rsquo; and I couldn&rsquo;t believe it. &ldquo;Martha has a straight A-plus average and you know it.&rdquo; That was Mr. Schmidt and he sounded very angry. &ldquo;I refuse to do it! I don&rsquo;t care who Joann&rsquo;s father is, her grades don&rsquo;t even begin to compare to Martha&rsquo;s,&rdquo; he continued. My heart sank.<br>Complete the following sentence.<br>The narrator was brought up by her:</p>",
                    question_hi: "<p>7. <strong>Comprehension</strong> :<br>The small Texas school that I went to had a tradition carried out every year during the eighth- grade graduation: a beautiful gold and green jacket (the school colours) was awarded to the student who had maintained the grade for eight years.<br>The scholarship jacket &mdash; a beautiful gold and green jacket had a big gold S on the left front side and your name written in gold letters on the pocket. My oldest sister, Rosie, had won the jacket a few years back, and I fully expected to also. I had been a straight &lsquo;A&rsquo; student since the first grade and in the eighth grade had looked forward very much to owning that jacket.<br>My father was a farm labourer who couldn&rsquo;t earn enough money to feed eight children, so When I was six I was given to my grandparents to raise. We couldn&rsquo;t participate in sports at school because there were registration fees, uniform costs, and trips out of town; so, even though our family was quite agile and athletic there would never be a school sports jacket for us. This one, the scholarship jacket, was our only chance.<br>Another hour of sweating in basketball was coming up and I was walking up to the gym.<br>Then I remembered my P.E. shorts and I had to walk all the way back and get them. I was almost back at my classroom door when I heard voices raised in anger as if in some sort of argument. I recognised the voices: Mr. Schmidt, my history teacher, and Mr. Boone, my math teacher, seemed to be arguing about me. I heard them say &lsquo;Martha&rsquo; and I couldn&rsquo;t believe it. &ldquo;Martha has a straight A-plus average and you know it.&rdquo; That was Mr. Schmidt and he sounded very angry. &ldquo;I refuse to do it! I don&rsquo;t care who Joann&rsquo;s father is, her grades don&rsquo;t even begin to compare to Martha&rsquo;s,&rdquo; he continued. My heart sank.<br>Complete the following sentence.<br>The narrator was brought up by her:</p>",
                    options_en: ["<p>sister</p>", "<p>grandparents</p>", 
                                "<p>teachers</p>", "<p>parents</p>"],
                    options_hi: ["<p>sister</p>", "<p>grandparents</p>",
                                "<p>teachers</p>", "<p>parents</p>"],
                    solution_en: "<p>7.(b) grandparents<br><strong>(Line/s from the passage -&nbsp;</strong>My father was a farm labourer who couldn&rsquo;t earn enough money to feed eight children, so When I was six I was given to my grandparents to raise.</p>",
                    solution_hi: "<p>7.(b) grandparents<br><strong>(Passage से ली गई lines - </strong>My father was a farm labourer who couldn&rsquo;t earn enough money to feed eight children, so When I was six I was given to my grandparents to raise./ मेरे पिता खेतो के एक मजदूर थे, जो आठ बच्चों को खिलाने के लिए पर्याप्त पैसा नहीं कमा सकते थे, इसलिए जब मैं छह साल का था तभी मुझको grandparents के पास पालने के लिए दे दिया गया था।</p>",
                    correct: "b",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "8",
                    section: "7",
                    question_en: "<p>8. <strong>Comprehension</strong> :<br>The small Texas school that I went to had a tradition carried out every year during the eighth- grade graduation: a beautiful gold and green jacket (the school colours) was awarded to the student who had maintained the grade for eight years.<br>The scholarship jacket &mdash; a beautiful gold and green jacket had a big gold S on the left front side and your name written in gold letters on the pocket. My oldest sister, Rosie, had won the jacket a few years back, and I fully expected to also. I had been a straight &lsquo;A&rsquo; student since the first grade and in the eighth grade had looked forward very much to owning that jacket.<br>My father was a farm labourer who couldn&rsquo;t earn enough money to feed eight children, so When I was six I was given to my grandparents to raise. We couldn&rsquo;t participate in sports at school because there were registration fees, uniform costs, and trips out of town; so, even though our family was quite agile and athletic there would never be a school sports jacket for us. This one, the scholarship jacket, was our only chance.<br>Another hour of sweating in basketball was coming up and I was walking up to the gym.<br>Then I remembered my P.E. shorts and I had to walk all the way back and get them. I was almost back at my classroom door when I heard voices raised in anger as if in some sort of argument. I recognised the voices: Mr. Schmidt, my history teacher, and Mr. Boone, my math teacher, seemed to be arguing about me. I heard them say &lsquo;Martha&rsquo; and I couldn&rsquo;t believe it. &ldquo;Martha has a straight A-plus average and you know it.&rdquo; That was Mr. Schmidt and he sounded very angry. &ldquo;I refuse to do it! I don&rsquo;t care who Joann&rsquo;s father is, her grades don&rsquo;t even begin to compare to Martha&rsquo;s,&rdquo; he continued. My heart sank.<br>Which of the following statements are true?<br>a) Grandfather was a farm labourer<br>b) Father couldn&rsquo;t earn enough money to feed eight<br>c) The family could not pay for sports activities<br>d) The family was small<br>e) Martha was a bright student</p>",
                    question_hi: "<p>8. <strong>Comprehension</strong> :<br>The small Texas school that I went to had a tradition carried out every year during the eighth- grade graduation: a beautiful gold and green jacket (the school colours) was awarded to the student who had maintained the grade for eight years.<br>The scholarship jacket &mdash; a beautiful gold and green jacket had a big gold S on the left front side and your name written in gold letters on the pocket. My oldest sister, Rosie, had won the jacket a few years back, and I fully expected to also. I had been a straight &lsquo;A&rsquo; student since the first grade and in the eighth grade had looked forward very much to owning that jacket.<br>My father was a farm labourer who couldn&rsquo;t earn enough money to feed eight children, so When I was six I was given to my grandparents to raise. We couldn&rsquo;t participate in sports at school because there were registration fees, uniform costs, and trips out of town; so, even though our family was quite agile and athletic there would never be a school sports jacket for us. This one, the scholarship jacket, was our only chance.<br>Another hour of sweating in basketball was coming up and I was walking up to the gym.<br>Then I remembered my P.E. shorts and I had to walk all the way back and get them. I was almost back at my classroom door when I heard voices raised in anger as if in some sort of argument. I recognised the voices: Mr. Schmidt, my history teacher, and Mr. Boone, my math teacher, seemed to be arguing about me. I heard them say &lsquo;Martha&rsquo; and I couldn&rsquo;t believe it. &ldquo;Martha has a straight A-plus average and you know it.&rdquo; That was Mr. Schmidt and he sounded very angry. &ldquo;I refuse to do it! I don&rsquo;t care who Joann&rsquo;s father is, her grades don&rsquo;t even begin to compare to Martha&rsquo;s,&rdquo; he continued. My heart sank.<br>Which of the following statements are true?<br>a) Grandfather was a farm labourer<br>b) Father couldn&rsquo;t earn enough money to feed eight<br>c) The family could not pay for sports activities<br>d) The family was small<br>e) Martha was a bright student</p>",
                    options_en: ["<p>b, c and e</p>", "<p>a, b and d</p>", 
                                "<p>a, c and d</p>", "<p>b, d and e</p>"],
                    options_hi: ["<p>b, c and e</p>", "<p>a, b and d</p>",
                                "<p>a, c and d</p>", "<p>b, d and e</p>"],
                    solution_en: "<p>8.(a) b, c and e<br>It can be inferred from the passage that statements written in option &lsquo;a&rsquo; are true.</p>",
                    solution_hi: "<p>8.(a) b, c and e<br>Passage से यह अनुमान लगाया जा सकता है कि option &lsquo;a&rsquo; में लिखे गए कथन सत्य हैं।</p>",
                    correct: "a",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "9",
                    section: "7",
                    question_en: "<p>9. <strong>Comprehension</strong> :<br>The small Texas school that I went to had a tradition carried out every year during the eighth- grade graduation: a beautiful gold and green jacket (the school colours) was awarded to the student who had maintained the grade for eight years.<br>The scholarship jacket &mdash; a beautiful gold and green jacket had a big gold S on the left front side and your name written in gold letters on the pocket. My oldest sister, Rosie, had won the jacket a few years back, and I fully expected to also. I had been a straight &lsquo;A&rsquo; student since the first grade and in the eighth grade had looked forward very much to owning that jacket.<br>My father was a farm labourer who couldn&rsquo;t earn enough money to feed eight children, so When I was six I was given to my grandparents to raise. We couldn&rsquo;t participate in sports at school because there were registration fees, uniform costs, and trips out of town; so, even though our family was quite agile and athletic there would never be a school sports jacket for us. This one, the scholarship jacket, was our only chance.<br>Another hour of sweating in basketball was coming up and I was walking up to the gym.<br>Then I remembered my P.E. shorts and I had to walk all the way back and get them. I was almost back at my classroom door when I heard voices raised in anger as if in some sort of argument. I recognised the voices: Mr. Schmidt, my history teacher, and Mr. Boone, my math teacher, seemed to be arguing about me. I heard them say &lsquo;Martha&rsquo; and I couldn&rsquo;t believe it. &ldquo;Martha has a straight A-plus average and you know it.&rdquo; That was Mr. Schmidt and he sounded very angry. &ldquo;I refuse to do it! I don&rsquo;t care who Joann&rsquo;s father is, her grades don&rsquo;t even begin to compare to Martha&rsquo;s,&rdquo; he continued. My heart sank.<br>The narrator&rsquo;s &lsquo;heart sank&rsquo;. She was worried because:</p>",
                    question_hi: "<p>9. <strong>Comprehension</strong> :<br>The small Texas school that I went to had a tradition carried out every year during the eighth- grade graduation: a beautiful gold and green jacket (the school colours) was awarded to the student who had maintained the grade for eight years.<br>The scholarship jacket &mdash; a beautiful gold and green jacket had a big gold S on the left front side and your name written in gold letters on the pocket. My oldest sister, Rosie, had won the jacket a few years back, and I fully expected to also. I had been a straight &lsquo;A&rsquo; student since the first grade and in the eighth grade had looked forward very much to owning that jacket.<br>My father was a farm labourer who couldn&rsquo;t earn enough money to feed eight children, so When I was six I was given to my grandparents to raise. We couldn&rsquo;t participate in sports at school because there were registration fees, uniform costs, and trips out of town; so, even though our family was quite agile and athletic there would never be a school sports jacket for us. This one, the scholarship jacket, was our only chance.<br>Another hour of sweating in basketball was coming up and I was walking up to the gym.<br>Then I remembered my P.E. shorts and I had to walk all the way back and get them. I was almost back at my classroom door when I heard voices raised in anger as if in some sort of argument. I recognised the voices: Mr. Schmidt, my history teacher, and Mr. Boone, my math teacher, seemed to be arguing about me. I heard them say &lsquo;Martha&rsquo; and I couldn&rsquo;t believe it. &ldquo;Martha has a straight A-plus average and you know it.&rdquo; That was Mr. Schmidt and he sounded very angry. &ldquo;I refuse to do it! I don&rsquo;t care who Joann&rsquo;s father is, her grades don&rsquo;t even begin to compare to Martha&rsquo;s,&rdquo; he continued. My heart sank.<br>The narrator&rsquo;s &lsquo;heart sank&rsquo;. She was worried because:</p>",
                    options_en: ["<p>Mr. Boone, the math teacher was arguing in her favour</p>", "<p>they were both arguing about her performance in sports</p>", 
                                "<p>she heard Mr. Boone say that her grades were not good</p>", "<p>Mr. Schmidt, the history teacher, was having to defend her though she was the best student.</p>"],
                    options_hi: ["<p>Mr. Boone, the math teacher was arguing in her favour</p>", "<p>they were both arguing about her performance in sports</p>",
                                "<p>she heard Mr. Boone say that her grades were not good</p>", "<p>Mr. Schmidt, the history teacher, was having to defend her though she was the best student.</p>"],
                    solution_en: "<p>9.(d) &ldquo;Mr. Schmidt, the history teacher, was having to defend her though she was the best student.&rdquo;<br>It can be inferred from the last paragraph of the passage that the narrator&rsquo;s &lsquo;heart sank&rsquo;. She was worried because Mr. Schmidt, the history teacher, was having to defend her though she was the best student.</p>",
                    solution_hi: "<p>9.(d) &ldquo;Mr. Schmidt, the history teacher, was having to defend her though she was the best student.&rdquo;/ Passage के अंतिम paragraph से यह अनुमान लगाया जा सकता है कि वक्ता का \'दिल टूट गया था\'। क्योंकि वह सबसे अच्छी छात्रा थी फिर भी इतिहास के शिक्षक Mr. Schmidt को उसका बचाव करना पड़ रहा था।</p>",
                    correct: "d",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "10",
                    section: "7",
                    question_en: "<p>10. <strong>Comprehension</strong> :<br>The small Texas school that I went to had a tradition carried out every year during the eighth- grade graduation: a beautiful gold and green jacket (the school colours) was awarded to the student who had maintained the grade for eight years.<br>The scholarship jacket &mdash; a beautiful gold and green jacket had a big gold S on the left front side and your name written in gold letters on the pocket. My oldest sister, Rosie, had won the jacket a few years back, and I fully expected to also. I had been a straight &lsquo;A&rsquo; student since the first grade and in the eighth grade had looked forward very much to owning that jacket.<br>My father was a farm labourer who couldn&rsquo;t earn enough money to feed eight children, so When I was six I was given to my grandparents to raise. We couldn&rsquo;t participate in sports at school because there were registration fees, uniform costs, and trips out of town; so, even though our family was quite agile and athletic there would never be a school sports jacket for us. This one, the scholarship jacket, was our only chance.<br>Another hour of sweating in basketball was coming up and I was walking up to the gym.<br>Then I remembered my P.E. shorts and I had to walk all the way back and get them. I was almost back at my classroom door when I heard voices raised in anger as if in some sort of argument. I recognised the voices: Mr. Schmidt, my history teacher, and Mr. Boone, my math teacher, seemed to be arguing about me. I heard them say &lsquo;Martha&rsquo; and I couldn&rsquo;t believe it. &ldquo;Martha has a straight A-plus average and you know it.&rdquo; That was Mr. Schmidt and he sounded very angry. &ldquo;I refuse to do it! I don&rsquo;t care who Joann&rsquo;s father is, her grades don&rsquo;t even begin to compare to Martha&rsquo;s,&rdquo; he continued. My heart sank.<br>Complete the following sentence.<br>The school honoured the best student in eighth grade with:</p>",
                    question_hi: "<p>10. <strong>Comprehension</strong> :<br>The small Texas school that I went to had a tradition carried out every year during the eighth- grade graduation: a beautiful gold and green jacket (the school colours) was awarded to the student who had maintained the grade for eight years.<br>The scholarship jacket &mdash; a beautiful gold and green jacket had a big gold S on the left front side and your name written in gold letters on the pocket. My oldest sister, Rosie, had won the jacket a few years back, and I fully expected to also. I had been a straight &lsquo;A&rsquo; student since the first grade and in the eighth grade had looked forward very much to owning that jacket.<br>My father was a farm labourer who couldn&rsquo;t earn enough money to feed eight children, so When I was six I was given to my grandparents to raise. We couldn&rsquo;t participate in sports at school because there were registration fees, uniform costs, and trips out of town; so, even though our family was quite agile and athletic there would never be a school sports jacket for us. This one, the scholarship jacket, was our only chance.<br>Another hour of sweating in basketball was coming up and I was walking up to the gym.<br>Then I remembered my P.E. shorts and I had to walk all the way back and get them. I was almost back at my classroom door when I heard voices raised in anger as if in some sort of argument. I recognised the voices: Mr. Schmidt, my history teacher, and Mr. Boone, my math teacher, seemed to be arguing about me. I heard them say &lsquo;Martha&rsquo; and I couldn&rsquo;t believe it. &ldquo;Martha has a straight A-plus average and you know it.&rdquo; That was Mr. Schmidt and he sounded very angry. &ldquo;I refuse to do it! I don&rsquo;t care who Joann&rsquo;s father is, her grades don&rsquo;t even begin to compare to Martha&rsquo;s,&rdquo; he continued. My heart sank.<br>Complete the following sentence.<br>The school honoured the best student in eighth grade with:</p>",
                    options_en: ["<p>a green jacket with the name in green</p>", "<p>a golden jacket with an S in green</p>", 
                                "<p>a green jacket with a gold pocket</p>", "<p>a gold and green jacket with a gold S</p>"],
                    options_hi: ["<p>a green jacket with the name in green</p>", "<p>a golden jacket with an S in green</p>",
                                "<p>a green jacket with a gold pocket</p>", "<p>a gold and green jacket with a gold S</p>"],
                    solution_en: "<p>10.(d) &ldquo;a gold and green jacket with a gold S&rdquo;<br>It can be inferred from the first paragraph of the passage that the school honoured the best student in eighth grade with a gold and green jacket with a gold S.</p>",
                    solution_hi: "<p>10.(d) &ldquo;a gold and green jacket with a gold S&rdquo;<br>Passage के पहले paragraph से यह अनुमान लगाया जा सकता है कि आठवीं कक्षा के सर्वश्रेष्ठ छात्र को gold और green रंग की jacket से सम्मानित किया, जिस पर सोने का S बना हुआ था ।</p>",
                    correct: "d",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "11",
                    section: "7",
                    question_en: "<p>11. <strong>Comprehension</strong> : <br>At a number of places in the Kashmir Valley, security forces have put coils of razor wire on roads to enforce restrictions on movement. Concertina wire or razor wire fences are used along territorial borders and in theatres of conflict around the world, to keep out combatants, terrorists, or refugees.<br>The expandable spools of barbed or razor wire get their name from concertina, a hand-held musical instrument similar to the accordion, with bellows that expand and contract. Concertina wire coils were an improvisation on the barbed wire obstacles used during World War I. The flat, collapsible coils with <span style=\"text-decoration: underline;\">intermittent</span> barbs or blades were designed to be carried along by infantry, and deployed on battlefields to prevent or slow down enemy movement.<br>The Englishman Richard Newton is credited with creating the first barbed wire around 1845; the first patent for &ldquo;a double wire clipped with diamond shaped barbs&rdquo; was given to Louis Fran&ccedil;ois Janin of France. In the United States, the first patent was registered by Lucien B Smith on June 25, 1867, for a prairie fence made of fireproof iron wire. Design innovations and more patents followed; Michael Kelly twisted razor wires together to form a cable of wires.<br>The American businessman Joseph F Glidden is considered to be the father of the modern barbed wire. His design of two strands of <span style=\"text-decoration: underline;\">intertwined </span>wire held by sharp prongs at regular intervals.<br>Barbed wire was initially an agrarian fencing invention intended to confine cattle and sheep, which unlike lumber, was largely resistant to fire and bad weather. An advertorial published in the US in 1885 under the title &lsquo;Why Barb Fencing Is Better Than Any Other&rsquo;, argued that &ldquo;it does not decay; boys cannot crawl through or over it; nor dogs; nor cats; nor any other animal; it watches with <span style=\"text-decoration: underline;\">argus eyes</span> the inside and outside, up, down and lengthwise; it prevents the &lsquo;ins&rsquo; from being &lsquo;outs&rsquo;, and the &lsquo;outs&rsquo; from being &lsquo;ins&rsquo;, watches at day-break, at noontide, at sunset and all night long&hellip;&rdquo;<br>Barbed wire was put to military use in the Siege of Santiago in 1898 during the Spanish-American War, and by the British in the Second Boer War of 1899-1902 to confine the families of the Afrikaans-speaking Boer fighters.<br>World War I saw extensive use of barbed wire &mdash; and German military engineers are credited with improvising the earliest concertina coils on the battlefield.They spun the barbed wire into circles and simply spread it on the battlefield.Without using any support infrastructure like poles etc. this was more effective against the infantry charged by Allied soldiers.<br>The fence erected by India along the Line of Control to keep out terrorist infiltrators consists of rows of concertina wire coils held by iron angles. Concertina coils have long been deployed during curfews in the Valley. They are now commonly seen elsewhere in India too, and are used to secure private properties as well.<br>What is the main theme of the above passage?</p>",
                    question_hi: "<p>11. <strong>Comprehension</strong> : <br>At a number of places in the Kashmir Valley, security forces have put coils of razor wire on roads to enforce restrictions on movement. Concertina wire or razor wire fences are used along territorial borders and in theatres of conflict around the world, to keep out combatants, terrorists, or refugees.<br>The expandable spools of barbed or razor wire get their name from concertina, a hand-held musical instrument similar to the accordion, with bellows that expand and contract. Concertina wire coils were an improvisation on the barbed wire obstacles used during World War I. The flat, collapsible coils with <span style=\"text-decoration: underline;\">intermittent</span> barbs or blades were designed to be carried along by infantry, and deployed on battlefields to prevent or slow down enemy movement.<br>The Englishman Richard Newton is credited with creating the first barbed wire around 1845; the first patent for &ldquo;a double wire clipped with diamond shaped barbs&rdquo; was given to Louis Fran&ccedil;ois Janin of France. In the United States, the first patent was registered by Lucien B Smith on June 25, 1867, for a prairie fence made of fireproof iron wire. Design innovations and more patents followed; Michael Kelly twisted razor wires together to form a cable of wires.<br>The American businessman Joseph F Glidden is considered to be the father of the modern barbed wire. His design of two strands of <span style=\"text-decoration: underline;\">intertwined </span>wire held by sharp prongs at regular intervals.<br>Barbed wire was initially an agrarian fencing invention intended to confine cattle and sheep, which unlike lumber, was largely resistant to fire and bad weather. An advertorial published in the US in 1885 under the title &lsquo;Why Barb Fencing Is Better Than Any Other&rsquo;, argued that &ldquo;it does not decay; boys cannot crawl through or over it; nor dogs; nor cats; nor any other animal; it watches with <span style=\"text-decoration: underline;\">argus eyes</span> the inside and outside, up, down and lengthwise; it prevents the &lsquo;ins&rsquo; from being &lsquo;outs&rsquo;, and the &lsquo;outs&rsquo; from being &lsquo;ins&rsquo;, watches at day-break, at noontide, at sunset and all night long&hellip;&rdquo;<br>Barbed wire was put to military use in the Siege of Santiago in 1898 during the Spanish-American War, and by the British in the Second Boer War of 1899-1902 to confine the families of the Afrikaans-speaking Boer fighters.<br>World War I saw extensive use of barbed wire &mdash; and German military engineers are credited with improvising the earliest concertina coils on the battlefield.They spun the barbed wire into circles and simply spread it on the battlefield.Without using any support infrastructure like poles etc. this was more effective against the infantry charged by Allied soldiers.<br>The fence erected by India along the Line of Control to keep out terrorist infiltrators consists of rows of concertina wire coils held by iron angles. Concertina coils have long been deployed during curfews in the Valley. They are now commonly seen elsewhere in India too, and are used to secure private properties as well.<br>What is the main theme of the above passage?</p>",
                    options_en: ["<p>How barbed wire was patented</p>", "<p>The use of barbed wire in Jammu and Kashmir</p>", 
                                "<p>The use of barbed wire in agriculture</p>", "<p>The evolution and use of barbed wire</p>"],
                    options_hi: ["<p>How barbed wire was patented</p>", "<p>The use of barbed wire in Jammu and Kashmir</p>",
                                "<p>The use of barbed wire in agriculture</p>", "<p>The evolution and use of barbed wire</p>"],
                    solution_en: "<p>11.(d) The evolution and use of barbed wire</p>",
                    solution_hi: "<p>11.(d) The evolution and use of barbed wire/ कांटेदार तार का विकास और उपयोग।</p>",
                    correct: "d",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "12",
                    section: "7",
                    question_en: "<p>12. <strong>Comprehension</strong> : <br>At a number of places in the Kashmir Valley, security forces have put coils of razor wire on roads to enforce restrictions on movement. Concertina wire or razor wire fences are used along territorial borders and in theatres of conflict around the world, to keep out combatants, terrorists, or refugees.<br>The expandable spools of barbed or razor wire get their name from concertina, a hand-held musical instrument similar to the accordion, with bellows that expand and contract. Concertina wire coils were an improvisation on the barbed wire obstacles used during World War I. The flat, collapsible coils with <span style=\"text-decoration: underline;\">intermittent</span> barbs or blades were designed to be carried along by infantry, and deployed on battlefields to prevent or slow down enemy movement.<br>The Englishman Richard Newton is credited with creating the first barbed wire around 1845; the first patent for &ldquo;a double wire clipped with diamond shaped barbs&rdquo; was given to Louis Fran&ccedil;ois Janin of France. In the United States, the first patent was registered by Lucien B Smith on June 25, 1867, for a prairie fence made of fireproof iron wire. Design innovations and more patents followed; Michael Kelly twisted razor wires together to form a cable of wires.<br>The American businessman Joseph F Glidden is considered to be the father of the modern barbed wire. His design of two strands of <span style=\"text-decoration: underline;\">intertwined </span>wire held by sharp prongs at regular intervals.<br>Barbed wire was initially an agrarian fencing invention intended to confine cattle and sheep, which unlike lumber, was largely resistant to fire and bad weather. An advertorial published in the US in 1885 under the title &lsquo;Why Barb Fencing Is Better Than Any Other&rsquo;, argued that &ldquo;it does not decay; boys cannot crawl through or over it; nor dogs; nor cats; nor any other animal; it watches with <span style=\"text-decoration: underline;\">argus eyes</span> the inside and outside, up, down and lengthwise; it prevents the &lsquo;ins&rsquo; from being &lsquo;outs&rsquo;, and the &lsquo;outs&rsquo; from being &lsquo;ins&rsquo;, watches at day-break, at noontide, at sunset and all night long&hellip;&rdquo;<br>Barbed wire was put to military use in the Siege of Santiago in 1898 during the Spanish-American War, and by the British in the Second Boer War of 1899-1902 to confine the families of the Afrikaans-speaking Boer fighters.<br>World War I saw extensive use of barbed wire &mdash; and German military engineers are credited with improvising the earliest concertina coils on the battlefield.They spun the barbed wire into circles and simply spread it on the battlefield.Without using any support infrastructure like poles etc. this was more effective against the infantry charged by Allied soldiers.<br>The fence erected by India along the Line of Control to keep out terrorist infiltrators consists of rows of concertina wire coils held by iron angles. Concertina coils have long been deployed during curfews in the Valley. They are now commonly seen elsewhere in India too, and are used to secure private properties as well.<br>Who is credited with creating the modern barbed wire?</p>",
                    question_hi: "<p>12. <strong>Comprehension</strong> : <br>At a number of places in the Kashmir Valley, security forces have put coils of razor wire on roads to enforce restrictions on movement. Concertina wire or razor wire fences are used along territorial borders and in theatres of conflict around the world, to keep out combatants, terrorists, or refugees.<br>The expandable spools of barbed or razor wire get their name from concertina, a hand-held musical instrument similar to the accordion, with bellows that expand and contract. Concertina wire coils were an improvisation on the barbed wire obstacles used during World War I. The flat, collapsible coils with <span style=\"text-decoration: underline;\">intermittent</span> barbs or blades were designed to be carried along by infantry, and deployed on battlefields to prevent or slow down enemy movement.<br>The Englishman Richard Newton is credited with creating the first barbed wire around 1845; the first patent for &ldquo;a double wire clipped with diamond shaped barbs&rdquo; was given to Louis Fran&ccedil;ois Janin of France. In the United States, the first patent was registered by Lucien B Smith on June 25, 1867, for a prairie fence made of fireproof iron wire. Design innovations and more patents followed; Michael Kelly twisted razor wires together to form a cable of wires.<br>The American businessman Joseph F Glidden is considered to be the father of the modern barbed wire. His design of two strands of <span style=\"text-decoration: underline;\">intertwined </span>wire held by sharp prongs at regular intervals.<br>Barbed wire was initially an agrarian fencing invention intended to confine cattle and sheep, which unlike lumber, was largely resistant to fire and bad weather. An advertorial published in the US in 1885 under the title &lsquo;Why Barb Fencing Is Better Than Any Other&rsquo;, argued that &ldquo;it does not decay; boys cannot crawl through or over it; nor dogs; nor cats; nor any other animal; it watches with <span style=\"text-decoration: underline;\">argus eyes</span> the inside and outside, up, down and lengthwise; it prevents the &lsquo;ins&rsquo; from being &lsquo;outs&rsquo;, and the &lsquo;outs&rsquo; from being &lsquo;ins&rsquo;, watches at day-break, at noontide, at sunset and all night long&hellip;&rdquo;<br>Barbed wire was put to military use in the Siege of Santiago in 1898 during the Spanish-American War, and by the British in the Second Boer War of 1899-1902 to confine the families of the Afrikaans-speaking Boer fighters.<br>World War I saw extensive use of barbed wire &mdash; and German military engineers are credited with improvising the earliest concertina coils on the battlefield.They spun the barbed wire into circles and simply spread it on the battlefield.Without using any support infrastructure like poles etc. this was more effective against the infantry charged by Allied soldiers.<br>The fence erected by India along the Line of Control to keep out terrorist infiltrators consists of rows of concertina wire coils held by iron angles. Concertina coils have long been deployed during curfews in the Valley. They are now commonly seen elsewhere in India too, and are used to secure private properties as well.<br>Who is credited with creating the modern barbed wire?</p>",
                    options_en: ["<p>Louise Francoise Janin</p>", "<p>Joseph F Glidden</p>", 
                                "<p>Lucien B Smith</p>", "<p>Richard Newton</p>"],
                    options_hi: ["<p>Louise Francoise Janin</p>", "<p>Joseph F Glidden</p>",
                                "<p>Lucien B Smith</p>", "<p>Richard Newton</p>"],
                    solution_en: "<p>12.(b) Joseph F Glidden <br><strong><span style=\"text-decoration: underline;\">Line/s from the passage</span> -&nbsp;</strong>The American businessman <span style=\"text-decoration: underline;\">Joseph F Glidden</span> is considered to be the <span style=\"text-decoration: underline;\">father of the modern barbed wire</span>.</p>",
                    solution_hi: "<p>12.(b) Joseph F Glidden <br><strong><span style=\"text-decoration: underline;\">Passage से ली गई lines -</span> </strong>The American businessman Joseph F Glidden is considered to be the father of the modern barbed wire./ अमेरिकी व्यवसायी <span style=\"text-decoration: underline;\">Joseph F Glidden</span> को <span style=\"text-decoration: underline;\">आधुनिक कांटेदार तार का जनक माना जाता है।</span></p>",
                    correct: "b",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "13",
                    section: "7",
                    question_en: "<p>13. <strong>Comprehension</strong> : <br>At a number of places in the Kashmir Valley, security forces have put coils of razor wire on roads to enforce restrictions on movement. Concertina wire or razor wire fences are used along territorial borders and in theatres of conflict around the world, to keep out combatants, terrorists, or refugees.<br>The expandable spools of barbed or razor wire get their name from concertina, a hand-held musical instrument similar to the accordion, with bellows that expand and contract. Concertina wire coils were an improvisation on the barbed wire obstacles used during World War I. The flat, collapsible coils with <span style=\"text-decoration: underline;\">intermittent</span> barbs or blades were designed to be carried along by infantry, and deployed on battlefields to prevent or slow down enemy movement.<br>The Englishman Richard Newton is credited with creating the first barbed wire around 1845; the first patent for &ldquo;a double wire clipped with diamond shaped barbs&rdquo; was given to Louis Fran&ccedil;ois Janin of France. In the United States, the first patent was registered by Lucien B Smith on June 25, 1867, for a prairie fence made of fireproof iron wire. Design innovations and more patents followed; Michael Kelly twisted razor wires together to form a cable of wires.<br>The American businessman Joseph F Glidden is considered to be the father of the modern barbed wire. His design of two strands of <span style=\"text-decoration: underline;\">intertwined </span>wire held by sharp prongs at regular intervals.<br>Barbed wire was initially an agrarian fencing invention intended to confine cattle and sheep, which unlike lumber, was largely resistant to fire and bad weather. An advertorial published in the US in 1885 under the title &lsquo;Why Barb Fencing Is Better Than Any Other&rsquo;, argued that &ldquo;it does not decay; boys cannot crawl through or over it; nor dogs; nor cats; nor any other animal; it watches with <span style=\"text-decoration: underline;\">argus eyes</span> the inside and outside, up, down and lengthwise; it prevents the &lsquo;ins&rsquo; from being &lsquo;outs&rsquo;, and the &lsquo;outs&rsquo; from being &lsquo;ins&rsquo;, watches at day-break, at noontide, at sunset and all night long&hellip;&rdquo;<br>Barbed wire was put to military use in the Siege of Santiago in 1898 during the Spanish-American War, and by the British in the Second Boer War of 1899-1902 to confine the families of the Afrikaans-speaking Boer fighters.<br>World War I saw extensive use of barbed wire &mdash; and German military engineers are credited with improvising the earliest concertina coils on the battlefield.They spun the barbed wire into circles and simply spread it on the battlefield.Without using any support infrastructure like poles etc. this was more effective against the infantry charged by Allied soldiers.<br>The fence erected by India along the Line of Control to keep out terrorist infiltrators consists of rows of concertina wire coils held by iron angles. Concertina coils have long been deployed during curfews in the Valley. They are now commonly seen elsewhere in India too, and are used to secure private properties as well.<br>What was the initial purpose of inventing the barbed wire?</p>",
                    question_hi: "<p>13. <strong>Comprehension</strong> : <br>At a number of places in the Kashmir Valley, security forces have put coils of razor wire on roads to enforce restrictions on movement. Concertina wire or razor wire fences are used along territorial borders and in theatres of conflict around the world, to keep out combatants, terrorists, or refugees.<br>The expandable spools of barbed or razor wire get their name from concertina, a hand-held musical instrument similar to the accordion, with bellows that expand and contract. Concertina wire coils were an improvisation on the barbed wire obstacles used during World War I. The flat, collapsible coils with <span style=\"text-decoration: underline;\">intermittent</span> barbs or blades were designed to be carried along by infantry, and deployed on battlefields to prevent or slow down enemy movement.<br>The Englishman Richard Newton is credited with creating the first barbed wire around 1845; the first patent for &ldquo;a double wire clipped with diamond shaped barbs&rdquo; was given to Louis Fran&ccedil;ois Janin of France. In the United States, the first patent was registered by Lucien B Smith on June 25, 1867, for a prairie fence made of fireproof iron wire. Design innovations and more patents followed; Michael Kelly twisted razor wires together to form a cable of wires.<br>The American businessman Joseph F Glidden is considered to be the father of the modern barbed wire. His design of two strands of <span style=\"text-decoration: underline;\">intertwined </span>wire held by sharp prongs at regular intervals.<br>Barbed wire was initially an agrarian fencing invention intended to confine cattle and sheep, which unlike lumber, was largely resistant to fire and bad weather. An advertorial published in the US in 1885 under the title &lsquo;Why Barb Fencing Is Better Than Any Other&rsquo;, argued that &ldquo;it does not decay; boys cannot crawl through or over it; nor dogs; nor cats; nor any other animal; it watches with <span style=\"text-decoration: underline;\">argus eyes</span> the inside and outside, up, down and lengthwise; it prevents the &lsquo;ins&rsquo; from being &lsquo;outs&rsquo;, and the &lsquo;outs&rsquo; from being &lsquo;ins&rsquo;, watches at day-break, at noontide, at sunset and all night long&hellip;&rdquo;<br>Barbed wire was put to military use in the Siege of Santiago in 1898 during the Spanish-American War, and by the British in the Second Boer War of 1899-1902 to confine the families of the Afrikaans-speaking Boer fighters.<br>World War I saw extensive use of barbed wire &mdash; and German military engineers are credited with improvising the earliest concertina coils on the battlefield.They spun the barbed wire into circles and simply spread it on the battlefield.Without using any support infrastructure like poles etc. this was more effective against the infantry charged by Allied soldiers.<br>The fence erected by India along the Line of Control to keep out terrorist infiltrators consists of rows of concertina wire coils held by iron angles. Concertina coils have long been deployed during curfews in the Valley. They are now commonly seen elsewhere in India too, and are used to secure private properties as well.<br>What was the initial purpose of inventing the barbed wire?</p>",
                    options_en: ["<p>to secure the borders of a country</p>", "<p>to keep the dogs and boys out of gardens</p>", 
                                "<p>to restrict the movement of trouble makers</p>", "<p>to confine cattle and sheep within an Area</p>"],
                    options_hi: ["<p>to secure the borders of a country</p>", "<p>to keep the dogs and boys out of gardens</p>",
                                "<p>to restrict the movement of trouble makers</p>", "<p>to confine cattle and sheep within an Area</p>"],
                    solution_en: "<p>13.(d) to confine cattle and sheep within an area<br><span style=\"text-decoration: underline;\"><strong>Line/s from the passage- </strong></span>Barbed wire was initially an agrarian fencing invention intended <span style=\"text-decoration: underline;\">to confine cattle and sheep</span>, which unlike lumber, was largely resistant to fire and bad weather.</p>",
                    solution_hi: "<p>13.(d) to confine cattle and sheep within an area<br><strong><span style=\"text-decoration: underline;\">Passage से ली गई line</span> - </strong>Barbed wire was initially an agrarian fencing invention intended<span style=\"text-decoration: underline;\"> to confine cattle and sheep</span>, which unlike lumber, was largely resistant to fire and bad weather./कंटीले तार का आविष्कार शुरू में एक कृषि बाड़ लगाने के लिए था। कंटीले तार का उद्देश्य मवेशियों और भेड़ों को एक ही स्थान पर रखना था। तार काफी हद तक आग और खराब मौसम के लिए प्रतिरोधी थी ।</p>",
                    correct: "d",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "14",
                    section: "7",
                    question_en: "<p>14. <strong>Comprehension</strong> : <br>At a number of places in the Kashmir Valley, security forces have put coils of razor wire on roads to enforce restrictions on movement. Concertina wire or razor wire fences are used along territorial borders and in theatres of conflict around the world, to keep out combatants, terrorists, or refugees.<br>The expandable spools of barbed or razor wire get their name from concertina, a hand-held musical instrument similar to the accordion, with bellows that expand and contract. Concertina wire coils were an improvisation on the barbed wire obstacles used during World War I. The flat, collapsible coils with <span style=\"text-decoration: underline;\">intermittent</span> barbs or blades were designed to be carried along by infantry, and deployed on battlefields to prevent or slow down enemy movement.<br>The Englishman Richard Newton is credited with creating the first barbed wire around 1845; the first patent for &ldquo;a double wire clipped with diamond shaped barbs&rdquo; was given to Louis Fran&ccedil;ois Janin of France. In the United States, the first patent was registered by Lucien B Smith on June 25, 1867, for a prairie fence made of fireproof iron wire. Design innovations and more patents followed; Michael Kelly twisted razor wires together to form a cable of wires.<br>The American businessman Joseph F Glidden is considered to be the father of the modern barbed wire. His design of two strands of <span style=\"text-decoration: underline;\">intertwined </span>wire held by sharp prongs at regular intervals.<br>Barbed wire was initially an agrarian fencing invention intended to confine cattle and sheep, which unlike lumber, was largely resistant to fire and bad weather. An advertorial published in the US in 1885 under the title &lsquo;Why Barb Fencing Is Better Than Any Other&rsquo;, argued that &ldquo;it does not decay; boys cannot crawl through or over it; nor dogs; nor cats; nor any other animal; it watches with <span style=\"text-decoration: underline;\">argus eyes</span> the inside and outside, up, down and lengthwise; it prevents the &lsquo;ins&rsquo; from being &lsquo;outs&rsquo;, and the &lsquo;outs&rsquo; from being &lsquo;ins&rsquo;, watches at day-break, at noontide, at sunset and all night long&hellip;&rdquo;<br>Barbed wire was put to military use in the Siege of Santiago in 1898 during the Spanish-American War, and by the British in the Second Boer War of 1899-1902 to confine the families of the Afrikaans-speaking Boer fighters.<br>World War I saw extensive use of barbed wire &mdash; and German military engineers are credited with improvising the earliest concertina coils on the battlefield.They spun the barbed wire into circles and simply spread it on the battlefield.Without using any support infrastructure like poles etc. this was more effective against the infantry charged by Allied soldiers.<br>The fence erected by India along the Line of Control to keep out terrorist infiltrators consists of rows of concertina wire coils held by iron angles. Concertina coils have long been deployed during curfews in the Valley. They are now commonly seen elsewhere in India too, and are used to secure private properties as well.<br>Who first spread the barbed wires on the field without using the poles or any other support system.</p>",
                    question_hi: "<p>14. <strong>Comprehension</strong> : <br>At a number of places in the Kashmir Valley, security forces have put coils of razor wire on roads to enforce restrictions on movement. Concertina wire or razor wire fences are used along territorial borders and in theatres of conflict around the world, to keep out combatants, terrorists, or refugees.<br>The expandable spools of barbed or razor wire get their name from concertina, a hand-held musical instrument similar to the accordion, with bellows that expand and contract. Concertina wire coils were an improvisation on the barbed wire obstacles used during World War I. The flat, collapsible coils with <span style=\"text-decoration: underline;\">intermittent</span> barbs or blades were designed to be carried along by infantry, and deployed on battlefields to prevent or slow down enemy movement.<br>The Englishman Richard Newton is credited with creating the first barbed wire around 1845; the first patent for &ldquo;a double wire clipped with diamond shaped barbs&rdquo; was given to Louis Fran&ccedil;ois Janin of France. In the United States, the first patent was registered by Lucien B Smith on June 25, 1867, for a prairie fence made of fireproof iron wire. Design innovations and more patents followed; Michael Kelly twisted razor wires together to form a cable of wires.<br>The American businessman Joseph F Glidden is considered to be the father of the modern barbed wire. His design of two strands of <span style=\"text-decoration: underline;\">intertwined </span>wire held by sharp prongs at regular intervals.<br>Barbed wire was initially an agrarian fencing invention intended to confine cattle and sheep, which unlike lumber, was largely resistant to fire and bad weather. An advertorial published in the US in 1885 under the title &lsquo;Why Barb Fencing Is Better Than Any Other&rsquo;, argued that &ldquo;it does not decay; boys cannot crawl through or over it; nor dogs; nor cats; nor any other animal; it watches with <span style=\"text-decoration: underline;\">argus eyes</span> the inside and outside, up, down and lengthwise; it prevents the &lsquo;ins&rsquo; from being &lsquo;outs&rsquo;, and the &lsquo;outs&rsquo; from being &lsquo;ins&rsquo;, watches at day-break, at noontide, at sunset and all night long&hellip;&rdquo;<br>Barbed wire was put to military use in the Siege of Santiago in 1898 during the Spanish-American War, and by the British in the Second Boer War of 1899-1902 to confine the families of the Afrikaans-speaking Boer fighters.<br>World War I saw extensive use of barbed wire &mdash; and German military engineers are credited with improvising the earliest concertina coils on the battlefield.They spun the barbed wire into circles and simply spread it on the battlefield.Without using any support infrastructure like poles etc. this was more effective against the infantry charged by Allied soldiers.<br>The fence erected by India along the Line of Control to keep out terrorist infiltrators consists of rows of concertina wire coils held by iron angles. Concertina coils have long been deployed during curfews in the Valley. They are now commonly seen elsewhere in India too, and are used to secure private properties as well.<br>Who first spread the barbed wires on the field without using the poles or any other support system.</p>",
                    options_en: ["<p>German military engineers</p>", "<p>British army</p>", 
                                "<p>American military</p>", "<p>allied soldiers</p>"],
                    options_hi: ["<p>German military engineers</p>", "<p>British army</p>",
                                "<p>American military</p>", "<p>allied soldiers</p>"],
                    solution_en: "<p>14.(a) German military engineers<br><strong><span style=\"text-decoration: underline;\">Line/s from the passage</span></strong>-World War I saw extensive use of barbed wire &mdash; <span style=\"text-decoration: underline;\">and German military engineers</span> are credited with improvising the earliest concertina coils on the battlefield.</p>",
                    solution_hi: "<p>14.(a) German military engineers<br><strong><span style=\"text-decoration: underline;\">Passage से ली गई line</span> </strong>- World War 1 saw extensive use of barbed wire &mdash; <span style=\"text-decoration: underline;\">and German military engineers</span> are credited with improvising the earliest concertina coils on the battlefield./ प्रथम विश्व युद्ध में कांटेदार तार का व्यापक उपयोग देखा गया - और <span style=\"text-decoration: underline;\">German military engineers</span> को युद्ध के मैदान पर सबसे पहले concertina coils को सुधारने का श्रेय दिया जाता है।</p>",
                    correct: "a",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "15",
                    section: "7",
                    question_en: "<p>15. <strong>Comprehension</strong> : <br>At a number of places in the Kashmir Valley, security forces have put coils of razor wire on roads to enforce restrictions on movement. Concertina wire or razor wire fences are used along territorial borders and in theatres of conflict around the world, to keep out combatants, terrorists, or refugees.<br>The expandable spools of barbed or razor wire get their name from concertina, a hand-held musical instrument similar to the accordion, with bellows that expand and contract. Concertina wire coils were an improvisation on the barbed wire obstacles used during World War I. The flat, collapsible coils with <span style=\"text-decoration: underline;\">intermittent</span> barbs or blades were designed to be carried along by infantry, and deployed on battlefields to prevent or slow down enemy movement.<br>The Englishman Richard Newton is credited with creating the first barbed wire around 1845; the first patent for &ldquo;a double wire clipped with diamond shaped barbs&rdquo; was given to Louis Fran&ccedil;ois Janin of France. In the United States, the first patent was registered by Lucien B Smith on June 25, 1867, for a prairie fence made of fireproof iron wire. Design innovations and more patents followed; Michael Kelly twisted razor wires together to form a cable of wires.<br>The American businessman Joseph F Glidden is considered to be the father of the modern barbed wire. His design of two strands of <span style=\"text-decoration: underline;\">intertwined </span>wire held by sharp prongs at regular intervals.<br>Barbed wire was initially an agrarian fencing invention intended to confine cattle and sheep, which unlike lumber, was largely resistant to fire and bad weather. An advertorial published in the US in 1885 under the title &lsquo;Why Barb Fencing Is Better Than Any Other&rsquo;, argued that &ldquo;it does not decay; boys cannot crawl through or over it; nor dogs; nor cats; nor any other animal; it watches with <span style=\"text-decoration: underline;\">argus eyes</span> the inside and outside, up, down and lengthwise; it prevents the &lsquo;ins&rsquo; from being &lsquo;outs&rsquo;, and the &lsquo;outs&rsquo; from being &lsquo;ins&rsquo;, watches at day-break, at noontide, at sunset and all night long&hellip;&rdquo;<br>Barbed wire was put to military use in the Siege of Santiago in 1898 during the Spanish-American War, and by the British in the Second Boer War of 1899-1902 to confine the families of the Afrikaans-speaking Boer fighters.<br>World War I saw extensive use of barbed wire &mdash; and German military engineers are credited with improvising the earliest concertina coils on the battlefield.They spun the barbed wire into circles and simply spread it on the battlefield.Without using any support infrastructure like poles etc. this was more effective against the infantry charged by Allied soldiers.<br>The fence erected by India along the Line of Control to keep out terrorist infiltrators consists of rows of concertina wire coils held by iron angles. Concertina coils have long been deployed during curfews in the Valley. They are now commonly seen elsewhere in India too, and are used to secure private properties as well.<br>Which statement is NOT true according to the passage?</p>",
                    question_hi: "<p>15. <strong>Comprehension</strong> : <br>At a number of places in the Kashmir Valley, security forces have put coils of razor wire on roads to enforce restrictions on movement. Concertina wire or razor wire fences are used along territorial borders and in theatres of conflict around the world, to keep out combatants, terrorists, or refugees.<br>The expandable spools of barbed or razor wire get their name from concertina, a hand-held musical instrument similar to the accordion, with bellows that expand and contract. Concertina wire coils were an improvisation on the barbed wire obstacles used during World War I. The flat, collapsible coils with <span style=\"text-decoration: underline;\">intermittent</span> barbs or blades were designed to be carried along by infantry, and deployed on battlefields to prevent or slow down enemy movement.<br>The Englishman Richard Newton is credited with creating the first barbed wire around 1845; the first patent for &ldquo;a double wire clipped with diamond shaped barbs&rdquo; was given to Louis Fran&ccedil;ois Janin of France. In the United States, the first patent was registered by Lucien B Smith on June 25, 1867, for a prairie fence made of fireproof iron wire. Design innovations and more patents followed; Michael Kelly twisted razor wires together to form a cable of wires.<br>The American businessman Joseph F Glidden is considered to be the father of the modern barbed wire. His design of two strands of <span style=\"text-decoration: underline;\">intertwined </span>wire held by sharp prongs at regular intervals.<br>Barbed wire was initially an agrarian fencing invention intended to confine cattle and sheep, which unlike lumber, was largely resistant to fire and bad weather. An advertorial published in the US in 1885 under the title &lsquo;Why Barb Fencing Is Better Than Any Other&rsquo;, argued that &ldquo;it does not decay; boys cannot crawl through or over it; nor dogs; nor cats; nor any other animal; it watches with <span style=\"text-decoration: underline;\">argus eyes</span> the inside and outside, up, down and lengthwise; it prevents the &lsquo;ins&rsquo; from being &lsquo;outs&rsquo;, and the &lsquo;outs&rsquo; from being &lsquo;ins&rsquo;, watches at day-break, at noontide, at sunset and all night long&hellip;&rdquo;<br>Barbed wire was put to military use in the Siege of Santiago in 1898 during the Spanish-American War, and by the British in the Second Boer War of 1899-1902 to confine the families of the Afrikaans-speaking Boer fighters.<br>World War I saw extensive use of barbed wire &mdash; and German military engineers are credited with improvising the earliest concertina coils on the battlefield.They spun the barbed wire into circles and simply spread it on the battlefield.Without using any support infrastructure like poles etc. this was more effective against the infantry charged by Allied soldiers.<br>The fence erected by India along the Line of Control to keep out terrorist infiltrators consists of rows of concertina wire coils held by iron angles. Concertina coils have long been deployed during curfews in the Valley. They are now commonly seen elsewhere in India too, and are used to secure private properties as well.<br>Which statement is NOT true according to the passage?</p>",
                    options_en: ["<p>The fence along the Indian Line of Control consists of rows of concertina wire coils held by iron angles.</p>", "<p>In the United State, the first patent was registered by LouiseFrancoise Janin.</p>", 
                                "<p>It was Richard Newton, an Englishman who invented the barbed wire around 1945.</p>", "<p>Barbed wire was put to military use in the Siege of Santiago in 1898 during the SpanishAmerican-War.</p>"],
                    options_hi: ["<p>The fence along the Indian Line of Control consists of rows of concertina wire coils held by iron angles.</p>", "<p>In the United State, the first patent was registered by LouiseFrancoise Janin.</p>",
                                "<p>It was Richard Newton, an Englishman who invented the barbed wire around 1945.</p>", "<p>Barbed wire was put to military use in the Siege of Santiago in 1898 during the SpanishAmerican-War.</p>"],
                    solution_en: "<p>15.(b) In the United State, the first patent was registered by Louise Francoise Janin.<br><span style=\"text-decoration: underline;\"><strong>Line/s from the passage- </strong>The first patent </span>for &ldquo;a double wire clipped with diamond shaped barbs&rdquo; was given to <span style=\"text-decoration: underline;\">Louis Fran&ccedil;ois Janin of France.</span></p>",
                    solution_hi: "<p>15.(b) In the United State, the first patent was registered by Louise Francoise Janin.<br><strong><span style=\"text-decoration: underline;\">Passage से ली गई line-</span> </strong><span style=\"text-decoration: underline;\">The first patent</span> for &ldquo;a double wire clipped with diamond shaped barbs&rdquo; was given to <span style=\"text-decoration: underline;\">Louis</span> Fran&ccedil;ois Janin of France./ \"A double wire clipped with diamond shaped barbs\" का पहला पेटेंट France के <span style=\"text-decoration: underline;\">Louis</span> Fran&ccedil;ois Janin को दिया गया था।</p>",
                    correct: "b",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "16",
                    section: "7",
                    question_en: "<p>16. <strong>Comprehension</strong> :<br>The ocean will not look the same color in the future.Essentially, climate change will make the blues of the ocean bluer and the greens greener. Scientists from MIT, Boston, figured this out by creating a global model that <span style=\"text-decoration: underline;\">simulates</span> the growth of a tiny creature that lives in the oceans and affects the color we see. <br>The ocean looks blue or green to us because of a combination of how sunlight interacts with water molecules and with whatever else lives in that water.<br>The molecules in water absorb all but the blue part of the spectrum of sunlight, and the water reflects that blue color back. That\'s the color we see.<br>The water looks greener when it has more phytoplankton, tiny, microscopic organisms that, like plants, can use chlorophyll to capture mostly the blue portions of the spectrum of sunlight. They then use photosynthesis to create the chemical energy they need to live. When there are more of these creatures in the water absorbing sunlight, they make the water look greener. <span style=\"text-decoration: underline;\">Conversely</span>, if there are fewer phytoplankton, the water looks bluer.<br>The creatures\' growth is dependent on how much sunlight, carbon dioxide and nutrients are around. Climate change is altering the ocean currents, meaning there will be fewer nutrients for phytoplankton to feed on in some areas, so there will be a decline in their number in those regions.<br>Since the 1990s, satellites have taken regular measurements of how much chlorophyll is in the ocean. Those levels can change because of weather events or because of climate change.<br>The study predicts that the blues will intensify, most likely in subtropical regions where phytoplankton will decrease. These are areas near the equator like Bermuda and the Bahamas that are already quite low in phytoplankton.<br>Regions where there are a lot of nutrients, like in the Southern Ocean or parts of the North Atlantic, will see even faster-growing phytoplankton because those waters are warming with climate change. Those waters will look greener.<br>Climate change will bring a color change to half of the world\'s oceans by the end of the 21st century, the study says. That\'s bad for climate change on several levels: For one, phytoplankton remove about as much carbon dioxide from the air as plants and help regulate our climate, research shows. They are also key to other animals\' survival. \"Phytoplankton are at the base, and if the base changes, it endangers everything else along the food web, going far enough to the polar bears or tuna or just about anything that you want to eat or love to see in pictures.\"said Stephanie Dutkiewicz, a principal research scientist in MIT.<br>The passage is mainly about:</p>",
                    question_hi: "<p>16. <strong>Comprehension</strong> :<br>The ocean will not look the same color in the future.Essentially, climate change will make the blues of the ocean bluer and the greens greener. Scientists from MIT, Boston, figured this out by creating a global model that <span style=\"text-decoration: underline;\">simulates</span> the growth of a tiny creature that lives in the oceans and affects the color we see. <br>The ocean looks blue or green to us because of a combination of how sunlight interacts with water molecules and with whatever else lives in that water.<br>The molecules in water absorb all but the blue part of the spectrum of sunlight, and the water reflects that blue color back. That\'s the color we see.<br>The water looks greener when it has more phytoplankton, tiny, microscopic organisms that, like plants, can use chlorophyll to capture mostly the blue portions of the spectrum of sunlight. They then use photosynthesis to create the chemical energy they need to live. When there are more of these creatures in the water absorbing sunlight, they make the water look greener. <span style=\"text-decoration: underline;\">Conversely</span>, if there are fewer phytoplankton, the water looks bluer.<br>The creatures\' growth is dependent on how much sunlight, carbon dioxide and nutrients are around. Climate change is altering the ocean currents, meaning there will be fewer nutrients for phytoplankton to feed on in some areas, so there will be a decline in their number in those regions.<br>Since the 1990s, satellites have taken regular measurements of how much chlorophyll is in the ocean. Those levels can change because of weather events or because of climate change.<br>The study predicts that the blues will intensify, most likely in subtropical regions where phytoplankton will decrease. These are areas near the equator like Bermuda and the Bahamas that are already quite low in phytoplankton.<br>Regions where there are a lot of nutrients, like in the Southern Ocean or parts of the North Atlantic, will see even faster-growing phytoplankton because those waters are warming with climate change. Those waters will look greener.<br>Climate change will bring a color change to half of the world\'s oceans by the end of the 21st century, the study says. That\'s bad for climate change on several levels: For one, phytoplankton remove about as much carbon dioxide from the air as plants and help regulate our climate, research shows. They are also key to other animals\' survival. \"Phytoplankton are at the base, and if the base changes, it endangers everything else along the food web, going far enough to the polar bears or tuna or just about anything that you want to eat or love to see in pictures.\"said Stephanie Dutkiewicz, a principal research scientist in MIT.<br>The passage is mainly about:</p>",
                    options_en: ["<p>The growth of phytoplankton in the ocean.</p>", "<p>How the growth of phytoplankton will impact the food web</p>", 
                                "<p>The change of the ocean color due to climate change</p>", "<p>Why ocean water looks blue.</p>"],
                    options_hi: ["<p>The growth of phytoplankton in the ocean.</p>", "<p>How the growth of phytoplankton will impact the food web</p>",
                                "<p>The change of the ocean color due to climate change</p>", "<p>Why ocean water looks blue.</p>"],
                    solution_en: "<p>16.(c) The change of the ocean color due to climate change</p>",
                    solution_hi: "<p>16.(c) The change of the ocean color due to climate change/जलवायु परिवर्तन के कारण समुद्र के रंग में परिवर्तन।</p>",
                    correct: "c",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "17",
                    section: "7",
                    question_en: "<p>17. <strong>Comprehension</strong> :<br>The ocean will not look the same color in the future.Essentially, climate change will make the blues of the ocean bluer and the greens greener. Scientists from MIT, Boston, figured this out by creating a global model that <span style=\"text-decoration: underline;\">simulates</span> the growth of a tiny creature that lives in the oceans and affects the color we see. <br>The ocean looks blue or green to us because of a combination of how sunlight interacts with water molecules and with whatever else lives in that water.<br>The molecules in water absorb all but the blue part of the spectrum of sunlight, and the water reflects that blue color back. That\'s the color we see.<br>The water looks greener when it has more phytoplankton, tiny, microscopic organisms that, like plants, can use chlorophyll to capture mostly the blue portions of the spectrum of sunlight. They then use photosynthesis to create the chemical energy they need to live. When there are more of these creatures in the water absorbing sunlight, they make the water look greener. <span style=\"text-decoration: underline;\">Conversely</span>, if there are fewer phytoplankton, the water looks bluer.<br>The creatures\' growth is dependent on how much sunlight, carbon dioxide and nutrients are around. Climate change is altering the ocean currents, meaning there will be fewer nutrients for phytoplankton to feed on in some areas, so there will be a decline in their number in those regions.<br>Since the 1990s, satellites have taken regular measurements of how much chlorophyll is in the ocean. Those levels can change because of weather events or because of climate change.<br>The study predicts that the blues will intensify, most likely in subtropical regions where phytoplankton will decrease. These are areas near the equator like Bermuda and the Bahamas that are already quite low in phytoplankton.<br>Regions where there are a lot of nutrients, like in the Southern Ocean or parts of the North Atlantic, will see even faster-growing phytoplankton because those waters are warming with climate change. Those waters will look greener.<br>Climate change will bring a color change to half of the world\'s oceans by the end of the 21st century, the study says. That\'s bad for climate change on several levels: For one, phytoplankton remove about as much carbon dioxide from the air as plants and help regulate our climate, research shows. They are also key to other animals\' survival. \"Phytoplankton are at the base, and if the base changes, it endangers everything else along the food web, going far enough to the polar bears or tuna or just about anything that you want to eat or love to see in pictures.\"said Stephanie Dutkiewicz, a principal research scientist in MIT.<br>The colour of the ocean depends on:</p>",
                    question_hi: "<p>17. <strong>Comprehension</strong> :<br>The ocean will not look the same color in the future.Essentially, climate change will make the blues of the ocean bluer and the greens greener. Scientists from MIT, Boston, figured this out by creating a global model that <span style=\"text-decoration: underline;\">simulates</span> the growth of a tiny creature that lives in the oceans and affects the color we see. <br>The ocean looks blue or green to us because of a combination of how sunlight interacts with water molecules and with whatever else lives in that water.<br>The molecules in water absorb all but the blue part of the spectrum of sunlight, and the water reflects that blue color back. That\'s the color we see.<br>The water looks greener when it has more phytoplankton, tiny, microscopic organisms that, like plants, can use chlorophyll to capture mostly the blue portions of the spectrum of sunlight. They then use photosynthesis to create the chemical energy they need to live. When there are more of these creatures in the water absorbing sunlight, they make the water look greener. <span style=\"text-decoration: underline;\">Conversely</span>, if there are fewer phytoplankton, the water looks bluer.<br>The creatures\' growth is dependent on how much sunlight, carbon dioxide and nutrients are around. Climate change is altering the ocean currents, meaning there will be fewer nutrients for phytoplankton to feed on in some areas, so there will be a decline in their number in those regions.<br>Since the 1990s, satellites have taken regular measurements of how much chlorophyll is in the ocean. Those levels can change because of weather events or because of climate change.<br>The study predicts that the blues will intensify, most likely in subtropical regions where phytoplankton will decrease. These are areas near the equator like Bermuda and the Bahamas that are already quite low in phytoplankton.<br>Regions where there are a lot of nutrients, like in the Southern Ocean or parts of the North Atlantic, will see even faster-growing phytoplankton because those waters are warming with climate change. Those waters will look greener.<br>Climate change will bring a color change to half of the world\'s oceans by the end of the 21st century, the study says. That\'s bad for climate change on several levels: For one, phytoplankton remove about as much carbon dioxide from the air as plants and help regulate our climate, research shows. They are also key to other animals\' survival. \"Phytoplankton are at the base, and if the base changes, it endangers everything else along the food web, going far enough to the polar bears or tuna or just about anything that you want to eat or love to see in pictures.\"said Stephanie Dutkiewicz, a principal research scientist in MIT.<br>The colour of the ocean depends on:</p>",
                    options_en: ["<p>the growth of phytoplankton in the ocean.</p>", "<p>the sunlight&rsquo;s interaction with the water molecules and sea organisms.</p>", 
                                "<p>water molecules absorbing the blue of the sunlight.</p>", "<p>the sunlight&rsquo;s absorption by the sea organisms.</p>"],
                    options_hi: ["<p>the growth of phytoplankton in the ocean.</p>", "<p>the sunlight&rsquo;s interaction with the water molecules and sea organisms.</p>",
                                "<p>water molecules absorbing the blue of the sunlight.</p>", "<p>the sunlight&rsquo;s absorption by the sea organisms.</p>"],
                    solution_en: "<p>17.(b) The sunlight&rsquo;s interaction with the water molecules and sea organisms.<br><span style=\"text-decoration: underline;\"><strong>Line/s from the passage</strong></span> -The ocean looks blue or green to us because of a combination of <span style=\"text-decoration: underline;\">how sunlight interacts with water molecules</span> and with whatever else lives in that water.</p>",
                    solution_hi: "<p>17.(b) The sunlight&rsquo;s interaction with the water molecules and sea organisms.<br><strong><span style=\"text-decoration: underline;\">Passage से ली गई line </span></strong>-The ocean looks blue or green to us because of a combination of <span style=\"text-decoration: underline;\">how sunlight interacts with water molecules</span> and with whatever else lives in that water./समुद्र हमें नीला या हरा दिखता है क्योकि सूरज की रोशनी पानी के अणुओं के साथ और उस पानी में जो कुछ भी है उसके साथ संयोजन करती है।</p>",
                    correct: "b",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "18",
                    section: "7",
                    question_en: "<p>18. <strong>Comprehension</strong> :<br>The ocean will not look the same color in the future.Essentially, climate change will make the blues of the ocean bluer and the greens greener. Scientists from MIT, Boston, figured this out by creating a global model that <span style=\"text-decoration: underline;\">simulates</span> the growth of a tiny creature that lives in the oceans and affects the color we see. <br>The ocean looks blue or green to us because of a combination of how sunlight interacts with water molecules and with whatever else lives in that water.<br>The molecules in water absorb all but the blue part of the spectrum of sunlight, and the water reflects that blue color back. That\'s the color we see.<br>The water looks greener when it has more phytoplankton, tiny, microscopic organisms that, like plants, can use chlorophyll to capture mostly the blue portions of the spectrum of sunlight. They then use photosynthesis to create the chemical energy they need to live. When there are more of these creatures in the water absorbing sunlight, they make the water look greener. <span style=\"text-decoration: underline;\">Conversely</span>, if there are fewer phytoplankton, the water looks bluer.<br>The creatures\' growth is dependent on how much sunlight, carbon dioxide and nutrients are around. Climate change is altering the ocean currents, meaning there will be fewer nutrients for phytoplankton to feed on in some areas, so there will be a decline in their number in those regions.<br>Since the 1990s, satellites have taken regular measurements of how much chlorophyll is in the ocean. Those levels can change because of weather events or because of climate change.<br>The study predicts that the blues will intensify, most likely in subtropical regions where phytoplankton will decrease. These are areas near the equator like Bermuda and the Bahamas that are already quite low in phytoplankton.<br>Regions where there are a lot of nutrients, like in the Southern Ocean or parts of the North Atlantic, will see even faster-growing phytoplankton because those waters are warming with climate change. Those waters will look greener.<br>Climate change will bring a color change to half of the world\'s oceans by the end of the 21st century, the study says. That\'s bad for climate change on several levels: For one, phytoplankton remove about as much carbon dioxide from the air as plants and help regulate our climate, research shows. They are also key to other animals\' survival. \"Phytoplankton are at the base, and if the base changes, it endangers everything else along the food web, going far enough to the polar bears or tuna or just about anything that you want to eat or love to see in pictures.\"said Stephanie Dutkiewicz, a principal research scientist in MIT.<br>In which areas will the ocean look bluer?</p>",
                    question_hi: "<p>18. <strong>Comprehension</strong> :<br>The ocean will not look the same color in the future.Essentially, climate change will make the blues of the ocean bluer and the greens greener. Scientists from MIT, Boston, figured this out by creating a global model that <span style=\"text-decoration: underline;\">simulates</span> the growth of a tiny creature that lives in the oceans and affects the color we see. <br>The ocean looks blue or green to us because of a combination of how sunlight interacts with water molecules and with whatever else lives in that water.<br>The molecules in water absorb all but the blue part of the spectrum of sunlight, and the water reflects that blue color back. That\'s the color we see.<br>The water looks greener when it has more phytoplankton, tiny, microscopic organisms that, like plants, can use chlorophyll to capture mostly the blue portions of the spectrum of sunlight. They then use photosynthesis to create the chemical energy they need to live. When there are more of these creatures in the water absorbing sunlight, they make the water look greener. <span style=\"text-decoration: underline;\">Conversely</span>, if there are fewer phytoplankton, the water looks bluer.<br>The creatures\' growth is dependent on how much sunlight, carbon dioxide and nutrients are around. Climate change is altering the ocean currents, meaning there will be fewer nutrients for phytoplankton to feed on in some areas, so there will be a decline in their number in those regions.<br>Since the 1990s, satellites have taken regular measurements of how much chlorophyll is in the ocean. Those levels can change because of weather events or because of climate change.<br>The study predicts that the blues will intensify, most likely in subtropical regions where phytoplankton will decrease. These are areas near the equator like Bermuda and the Bahamas that are already quite low in phytoplankton.<br>Regions where there are a lot of nutrients, like in the Southern Ocean or parts of the North Atlantic, will see even faster-growing phytoplankton because those waters are warming with climate change. Those waters will look greener.<br>Climate change will bring a color change to half of the world\'s oceans by the end of the 21st century, the study says. That\'s bad for climate change on several levels: For one, phytoplankton remove about as much carbon dioxide from the air as plants and help regulate our climate, research shows. They are also key to other animals\' survival. \"Phytoplankton are at the base, and if the base changes, it endangers everything else along the food web, going far enough to the polar bears or tuna or just about anything that you want to eat or love to see in pictures.\"said Stephanie Dutkiewicz, a principal research scientist in MIT.<br>In which areas will the ocean look bluer?</p>",
                    options_en: ["<p>Southern ocean</p>", "<p>Areas near poles</p>", 
                                "<p>Areas near equator</p>", "<p>North Atlantic ocean</p>"],
                    options_hi: ["<p>Southern ocean</p>", "<p>Areas near poles</p>",
                                "<p>Areas near equator</p>", "<p>North Atlantic ocean</p>"],
                    solution_en: "<p>18.(c) Areas near equator<br><span style=\"text-decoration: underline;\"><strong>Line/s from the passage</strong></span> The study predicts that <span style=\"text-decoration: underline;\">the blues will intensify,</span> most likely in subtropical regions where phytoplankton will decrease. <span style=\"text-decoration: underline;\">These are areas near the equator</span> like Bermuda and the Bahamas that are already quite low in phytoplankton.&rdquo;</p>",
                    solution_hi: "<p>18.(c) Areas near equator<br><span style=\"text-decoration: underline;\"><strong>Passage से ली गई line</strong></span>- The study predicts that<span style=\"text-decoration: underline;\"> the blues will intensify,</span> most likely in subtropical regions where phytoplankton will decrease. <span style=\"text-decoration: underline;\">These are areas near the equator</span> like Bermuda and the Bahamas that are already quite low in phytoplankton.&rdquo;/ यह अध्ययन यह भविष्यवाणी करता है कि समुंदर और जयादा नीला हो जायेगा , सबसे अधिक संभावना उपोष्णकटिबंधीय क्षेत्रों (subtropical regions) में हैं जहां phytoplankton कम हो जाएगा। ये Bermuda और Bahamas जैसे भूमध्य रेखा के पास के क्षेत्र हैं जो पहले से ही phytoplankton में काफी कम हैं।</p>",
                    correct: "c",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "19",
                    section: "7",
                    question_en: "<p>19. <strong>Comprehension</strong> :<br>The ocean will not look the same color in the future.Essentially, climate change will make the blues of the ocean bluer and the greens greener. Scientists from MIT, Boston, figured this out by creating a global model that <span style=\"text-decoration: underline;\">simulates</span> the growth of a tiny creature that lives in the oceans and affects the color we see. <br>The ocean looks blue or green to us because of a combination of how sunlight interacts with water molecules and with whatever else lives in that water.<br>The molecules in water absorb all but the blue part of the spectrum of sunlight, and the water reflects that blue color back. That\'s the color we see.<br>The water looks greener when it has more phytoplankton, tiny, microscopic organisms that, like plants, can use chlorophyll to capture mostly the blue portions of the spectrum of sunlight. They then use photosynthesis to create the chemical energy they need to live. When there are more of these creatures in the water absorbing sunlight, they make the water look greener. <span style=\"text-decoration: underline;\">Conversely</span>, if there are fewer phytoplankton, the water looks bluer.<br>The creatures\' growth is dependent on how much sunlight, carbon dioxide and nutrients are around. Climate change is altering the ocean currents, meaning there will be fewer nutrients for phytoplankton to feed on in some areas, so there will be a decline in their number in those regions.<br>Since the 1990s, satellites have taken regular measurements of how much chlorophyll is in the ocean. Those levels can change because of weather events or because of climate change.<br>The study predicts that the blues will intensify, most likely in subtropical regions where phytoplankton will decrease. These are areas near the equator like Bermuda and the Bahamas that are already quite low in phytoplankton.<br>Regions where there are a lot of nutrients, like in the Southern Ocean or parts of the North Atlantic, will see even faster-growing phytoplankton because those waters are warming with climate change. Those waters will look greener.<br>Climate change will bring a color change to half of the world\'s oceans by the end of the 21st century, the study says. That\'s bad for climate change on several levels: For one, phytoplankton remove about as much carbon dioxide from the air as plants and help regulate our climate, research shows. They are also key to other animals\' survival. \"Phytoplankton are at the base, and if the base changes, it endangers everything else along the food web, going far enough to the polar bears or tuna or just about anything that you want to eat or love to see in pictures.\"said Stephanie Dutkiewicz, a principal research scientist in MIT.<br>Where do phytoplankton grow profusely?</p>",
                    question_hi: "<p>19. <strong>Comprehension</strong> :<br>The ocean will not look the same color in the future.Essentially, climate change will make the blues of the ocean bluer and the greens greener. Scientists from MIT, Boston, figured this out by creating a global model that <span style=\"text-decoration: underline;\">simulates</span> the growth of a tiny creature that lives in the oceans and affects the color we see. <br>The ocean looks blue or green to us because of a combination of how sunlight interacts with water molecules and with whatever else lives in that water.<br>The molecules in water absorb all but the blue part of the spectrum of sunlight, and the water reflects that blue color back. That\'s the color we see.<br>The water looks greener when it has more phytoplankton, tiny, microscopic organisms that, like plants, can use chlorophyll to capture mostly the blue portions of the spectrum of sunlight. They then use photosynthesis to create the chemical energy they need to live. When there are more of these creatures in the water absorbing sunlight, they make the water look greener. <span style=\"text-decoration: underline;\">Conversely</span>, if there are fewer phytoplankton, the water looks bluer.<br>The creatures\' growth is dependent on how much sunlight, carbon dioxide and nutrients are around. Climate change is altering the ocean currents, meaning there will be fewer nutrients for phytoplankton to feed on in some areas, so there will be a decline in their number in those regions.<br>Since the 1990s, satellites have taken regular measurements of how much chlorophyll is in the ocean. Those levels can change because of weather events or because of climate change.<br>The study predicts that the blues will intensify, most likely in subtropical regions where phytoplankton will decrease. These are areas near the equator like Bermuda and the Bahamas that are already quite low in phytoplankton.<br>Regions where there are a lot of nutrients, like in the Southern Ocean or parts of the North Atlantic, will see even faster-growing phytoplankton because those waters are warming with climate change. Those waters will look greener.<br>Climate change will bring a color change to half of the world\'s oceans by the end of the 21st century, the study says. That\'s bad for climate change on several levels: For one, phytoplankton remove about as much carbon dioxide from the air as plants and help regulate our climate, research shows. They are also key to other animals\' survival. \"Phytoplankton are at the base, and if the base changes, it endangers everything else along the food web, going far enough to the polar bears or tuna or just about anything that you want to eat or love to see in pictures.\"said Stephanie Dutkiewicz, a principal research scientist in MIT.<br>Where do phytoplankton grow profusely?</p>",
                    options_en: ["<p>Where the sun rays are absorbed by ocean water</p>", "<p>Where the ocean water are hot as at equator</p>", 
                                "<p>Where there are a lot of nutrients in the ocean water</p>", "<p>Where the ocean currents are cold</p>"],
                    options_hi: ["<p>Where the sun rays are absorbed by ocean water</p>", "<p>Where the ocean water are hot as at equator</p>",
                                "<p>Where there are a lot of nutrients in the ocean water</p>", "<p>Where the ocean currents are cold</p>"],
                    solution_en: "<p>19.(c) Where there are a lot of nutrients in the ocean water<br><span style=\"text-decoration: underline;\"><strong>Line/s from the passage</strong></span> Regions where <span style=\"text-decoration: underline;\">there are a lot of nutrients</span>, like in the Southern Ocean or parts of the North Atlantic, will see <span style=\"text-decoration: underline;\">even faster-growing phytoplankton</span> because those waters are warming with climate change.&rdquo;</p>",
                    solution_hi: "<p>19.(c) Where there are a lot of nutrients in the ocean water<br><span style=\"text-decoration: underline;\"><strong>Passage से ली गई line/s</strong></span>- Regions where <span style=\"text-decoration: underline;\">there are a lot of nutrients,</span> like in the Southern Ocean or parts of the North Atlantic, will see<span style=\"text-decoration: underline;\"> even faster-growing phytoplankton</span> because those waters are warming with climate change.&rdquo;/जिन क्षेत्रों में बहुत सारे पोषक तत्व हैं जैसे कि दक्षिणी महासागर या उत्तरी अटलांटिक के कुछ हिस्से। वँहा तेजी से बढ़ने वाले <span style=\"text-decoration: underline;\">phytoplankton</span> भी दिखाई देंगे क्योंकि वे पानी ,जलवायु परिवर्तन के साथ गर्म हो रहे हैं।</p>",
                    correct: "c",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "20",
                    section: "7",
                    question_en: "<p>20. <strong>Comprehension</strong> :<br>The ocean will not look the same color in the future.Essentially, climate change will make the blues of the ocean bluer and the greens greener. Scientists from MIT, Boston, figured this out by creating a global model that <span style=\"text-decoration: underline;\">simulates</span> the growth of a tiny creature that lives in the oceans and affects the color we see. <br>The ocean looks blue or green to us because of a combination of how sunlight interacts with water molecules and with whatever else lives in that water.<br>The molecules in water absorb all but the blue part of the spectrum of sunlight, and the water reflects that blue color back. That\'s the color we see.<br>The water looks greener when it has more phytoplankton, tiny, microscopic organisms that, like plants, can use chlorophyll to capture mostly the blue portions of the spectrum of sunlight. They then use photosynthesis to create the chemical energy they need to live. When there are more of these creatures in the water absorbing sunlight, they make the water look greener. <span style=\"text-decoration: underline;\">Conversely</span>, if there are fewer phytoplankton, the water looks bluer.<br>The creatures\' growth is dependent on how much sunlight, carbon dioxide and nutrients are around. Climate change is altering the ocean currents, meaning there will be fewer nutrients for phytoplankton to feed on in some areas, so there will be a decline in their number in those regions.<br>Since the 1990s, satellites have taken regular measurements of how much chlorophyll is in the ocean. Those levels can change because of weather events or because of climate change.<br>The study predicts that the blues will intensify, most likely in subtropical regions where phytoplankton will decrease. These are areas near the equator like Bermuda and the Bahamas that are already quite low in phytoplankton.<br>Regions where there are a lot of nutrients, like in the Southern Ocean or parts of the North Atlantic, will see even faster-growing phytoplankton because those waters are warming with climate change. Those waters will look greener.<br>Climate change will bring a color change to half of the world\'s oceans by the end of the 21st century, the study says. That\'s bad for climate change on several levels: For one, phytoplankton remove about as much carbon dioxide from the air as plants and help regulate our climate, research shows. They are also key to other animals\' survival. \"Phytoplankton are at the base, and if the base changes, it endangers everything else along the food web, going far enough to the polar bears or tuna or just about anything that you want to eat or love to see in pictures.\"said Stephanie Dutkiewicz, a principal research scientist in MIT.<br>Which statement is not true according to the passage?</p>",
                    question_hi: "<p>20. <strong>Comprehension</strong> :<br>The ocean will not look the same color in the future.Essentially, climate change will make the blues of the ocean bluer and the greens greener. Scientists from MIT, Boston, figured this out by creating a global model that <span style=\"text-decoration: underline;\">simulates</span> the growth of a tiny creature that lives in the oceans and affects the color we see. <br>The ocean looks blue or green to us because of a combination of how sunlight interacts with water molecules and with whatever else lives in that water.<br>The molecules in water absorb all but the blue part of the spectrum of sunlight, and the water reflects that blue color back. That\'s the color we see.<br>The water looks greener when it has more phytoplankton, tiny, microscopic organisms that, like plants, can use chlorophyll to capture mostly the blue portions of the spectrum of sunlight. They then use photosynthesis to create the chemical energy they need to live. When there are more of these creatures in the water absorbing sunlight, they make the water look greener. <span style=\"text-decoration: underline;\">Conversely</span>, if there are fewer phytoplankton, the water looks bluer.<br>The creatures\' growth is dependent on how much sunlight, carbon dioxide and nutrients are around. Climate change is altering the ocean currents, meaning there will be fewer nutrients for phytoplankton to feed on in some areas, so there will be a decline in their number in those regions.<br>Since the 1990s, satellites have taken regular measurements of how much chlorophyll is in the ocean. Those levels can change because of weather events or because of climate change.<br>The study predicts that the blues will intensify, most likely in subtropical regions where phytoplankton will decrease. These are areas near the equator like Bermuda and the Bahamas that are already quite low in phytoplankton.<br>Regions where there are a lot of nutrients, like in the Southern Ocean or parts of the North Atlantic, will see even faster-growing phytoplankton because those waters are warming with climate change. Those waters will look greener.<br>Climate change will bring a color change to half of the world\'s oceans by the end of the 21st century, the study says. That\'s bad for climate change on several levels: For one, phytoplankton remove about as much carbon dioxide from the air as plants and help regulate our climate, research shows. They are also key to other animals\' survival. \"Phytoplankton are at the base, and if the base changes, it endangers everything else along the food web, going far enough to the polar bears or tuna or just about anything that you want to eat or love to see in pictures.\"said Stephanie Dutkiewicz, a principal research scientist in MIT.<br>Which statement is not true according to the passage?</p>",
                    options_en: ["<p>By the end of the 21st century the colour of ocean waters will change.</p>", "<p>In subtropical regions phytoplankton will decrease.</p>", 
                                "<p>More blue colour in the ocean will affect climate change.</p>", "<p>Phytoplankton has a key role in the survival of other animals.</p>"],
                    options_hi: ["<p>By the end of the 21st century the colour of ocean waters will change.</p>", "<p>In subtropical regions phytoplankton will decrease.</p>",
                                "<p>More blue colour in the ocean will affect climate change.</p>", "<p>Phytoplankton has a key role in the survival of other animals.</p>"],
                    solution_en: "<p>20.(c) More blue colour in the ocean will affect climate change.<br>All other options are true according to the passage.</p>",
                    solution_hi: "<p>20.(c) More blue colour in the ocean will affect climate change.<br>Passage के अनुसार अन्य सभी विकल्प सही हैं। .</p>",
                    correct: "c",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "21",
                    section: "7",
                    question_en: "<p>21. <strong>Cloze Test :-</strong><br>The Bhopal gas tragedy has been described as the world&rsquo;s (21)____ industrial disaster. Forty two tonnes of methyl isocyanate (22)____ from the steel containers (23)___ the Union Carbide factory and released a cloud of (24)___ gas. It left a legacy of instant and (25)____ death<br>Select the most appropriate option to fill in the blank no 21</p>",
                    question_hi: "<p>21. <strong>Cloze Test :-</strong><br>The Bhopal gas tragedy has been described as the world&rsquo;s (21)____ industrial disaster. Forty two tonnes of methyl isocyanate (22)____ from the steel containers (23)___ the Union Carbide factory and released a cloud of (24)___ gas. It left a legacy of instant and (25)____ death<br>Select the most appropriate option to fill in the blank no 21</p>",
                    options_en: ["<p>bad</p>", "<p>worst</p>", 
                                "<p>worse</p>", "<p>best</p>"],
                    options_hi: ["<p>bad</p>", "<p>worst</p>",
                                "<p>worse</p>", "<p>best</p>"],
                    solution_en: "<p>21.(b) worst <br>&ldquo;The&rdquo; should be followed by a superlative degree. In this sentence, the superlative degree of bad; (worst) should be used.</p>",
                    solution_hi: "<p>21.(b) worst <br>&ldquo;The&rdquo; के बाद एक superlative degree होनी चाहिए। इस वाक्य में bad की superlative degree (worst) का इस्तेमाल किया जाना चाहिए।</p>",
                    correct: "b",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "22",
                    section: "7",
                    question_en: "<p>22. <strong>Cloze Test:-</strong><br>The Bhopal gas tragedy has been described as the world&rsquo;s (21)____ industrial disaster. Forty two tonnes of methyl isocyanate (22)____ from the steel containers (23)___ the Union Carbide factory and released a cloud of (24)___ gas. It left a legacy of instant and (25)____ death<br>Select the most appropriate option to fill in the blank no 22</p>",
                    question_hi: "<p>22. <strong>Cloze Test:-</strong><br>The Bhopal gas tragedy has been described as the world&rsquo;s (21)____ industrial disaster. Forty two tonnes of methyl isocyanate (22)____ from the steel containers (23)___ the Union Carbide factory and released a cloud of (24)___ gas. It left a legacy of instant and (25)____ death<br>Select the most appropriate option to fill in the blank no 22</p>",
                    options_en: [" flowed", " released", 
                                " emitted ", " leaked"],
                    options_hi: [" flowed", " released",
                                " emitted ", " leaked"],
                    solution_en: "<p>22.(d) leaked<br><strong>Leaked-</strong>be accidentally lost or admitted through a hole or crack in a container or covering.<br><strong>Released</strong>-allow or enable to escape from confinement; SET free.<br><strong>Emitted-</strong>produce and discharge <br>So, option (d) fit to the context of the sentence.</p>",
                    solution_hi: "<p>22.(d) leaked<br><strong>Leaked</strong> - रिसाव ।<br><strong>Released</strong> - कारावास से निकलने की अनुमति देना; आजाद करना।<br><strong>Emitted</strong> - उत्पादन और प्रसारित करना<br>इसलिए , विकल्प (d) वाक्य के संदर्भ में ठीक बैठ रहा है।</p>",
                    correct: "d",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "23",
                    section: "7",
                    question_en: "<p>23. <strong>Cloze Test:-</strong><br>The Bhopal gas tragedy has been described as the world&rsquo;s (21)____ industrial disaster. Forty two tonnes of methyl isocyanate (22)____ from the steel containers (23)___ the Union Carbide factory and released a cloud of (24)___ gas. It left a legacy of instant and (25)____ death<br>Select the most appropriate option to fill in the blank no 23</p>",
                    question_hi: "<p>23. <strong>Cloze Test:-</strong><br>The Bhopal gas tragedy has been described as the world&rsquo;s (21)____ industrial disaster. Forty two tonnes of methyl isocyanate (22)____ from the steel containers (23)___ the Union Carbide factory and released a cloud of (24)___ gas. It left a legacy of instant and (25)____ death<br>Select the most appropriate option to fill in the blank no 23</p>",
                    options_en: [" across  ", " at  ", 
                                " beside ", " along"],
                    options_hi: [" across  ", " at  ",
                                " beside ", " along"],
                    solution_en: "23.(b) at<br />Preposition ‘at’ is used to express location or arrival in a particular place or position.",
                    solution_hi: "23.(b) at<br />Preposition ‘at’ का प्रयोग किसी विशेष स्थिति या स्थान के आगमन को व्यक्त करने के लिए किया जाता है।",
                    correct: "b",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "24",
                    section: "7",
                    question_en: "<p>24. <strong>Cloze Test:-</strong><br>The Bhopal gas tragedy has been described as the world&rsquo;s (21)____ industrial disaster. Forty two tonnes of methyl isocyanate (22)____ from the steel containers (23)___ the Union Carbide factory and released a cloud of (24)___ gas. It left a legacy of instant and (25)____ death<br>Select the most appropriate option to fill in the blank no 24</p>",
                    question_hi: "<p>24. <strong>Cloze Test:-</strong><br>The Bhopal gas tragedy has been described as the world&rsquo;s (21)____ industrial disaster. Forty two tonnes of methyl isocyanate (22)____ from the steel containers (23)___ the Union Carbide factory and released a cloud of (24)___ gas. It left a legacy of instant and (25)____ death<br>Select the most appropriate option to fill in the blank no 24</p>",
                    options_en: ["<p>merciless</p>", "<p>deathly</p>", 
                                "<p>incurable</p>", "<p>abominable</p>"],
                    options_hi: ["<p>merciless</p>", "<p>deathly</p>",
                                "<p>incurable</p>", "<p>abominable</p>"],
                    solution_en: "<p>24.(b) deathly<br><strong>Deathly</strong> -resembling or suggestive of death<br><strong>Merciless</strong> -showing no mercy.<br><strong>Incurable</strong> -not able to be cured.<br><strong>Abominable</strong> -very bad; terrible.<br>Because a lot of people died from the gas so, option (d) is the answer</p>",
                    solution_hi: "<p>24.(b) deathly<br><strong>Deathly</strong> - मृत्यु के समान। <br><strong>Merciless</strong> - कोई दया नहीं दिखाना ।<br><strong>Incurable</strong> - इलाज नहीं हो पाना । <br><strong>Abominable-</strong> बहुत बुरा; भयानक।<br>क्योंकि बहुत सारे लोग गैस से मर गए थे, इसलिए विकल्प (d) उत्तर सही है।</p>",
                    correct: "b",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "25",
                    section: "7",
                    question_en: "<p>25. <strong>Cloze Test:-</strong><br>The Bhopal gas tragedy has been described as the world&rsquo;s (21)____ industrial disaster. Forty two tonnes of methyl isocyanate (22)____ from the steel containers (23)___ the Union Carbide factory and released a cloud of (24)___ gas. It left a legacy of instant and (25)____ death<br>Select the most appropriate option to fill in the blank no 25</p>",
                    question_hi: "<p>25. <strong>Cloze Test:-</strong><br>The Bhopal gas tragedy has been described as the world&rsquo;s (21)____ industrial disaster. Forty two tonnes of methyl isocyanate (22)____ from the steel containers (23)___ the Union Carbide factory and released a cloud of (24)___ gas. It left a legacy of instant and (25)____ death<br>Select the most appropriate option to fill in the blank no 25</p>",
                    options_en: ["<p>averted</p>", "<p>detained</p>", 
                                "<p>momentary</p>", "<p>deferred</p>"],
                    options_hi: ["<p>averted</p>", "<p>detained</p>",
                                "<p>momentary</p>", "<p>deferred</p>"],
                    solution_en: "<p>25.(d) deferred<br><strong>Deferred</strong>-put off to a later time. <br><strong>Averted</strong>-prevent or ward off<br><strong>Detained</strong>-keep (someone) in official custody, typically for questioning about a crime or in a politically sensitive situation.<br><strong>Momentary</strong>-lasting for a very short time<br>Option (d) is fit to the context of the sentence.</p>",
                    solution_hi: "<p>25.(d) deferred<br><strong>Deferred</strong>-बाद के समय के लिए टाल देना <br><strong>Averted</strong> - रोकना या बचाना। <br><strong>Detained</strong> - (किसी को) आधिकारिक हिरासत में रखना, आमतौर पर किसी अपराध के बारे में या राजनीतिक रूप से संवेदनशील स्थिति में पूछताछ के लिए।<br><strong>Momentary</strong> - बहुत कम समय तक चलने वाला<br>विकल्प (d) वाक्य के संदर्भ में उपयुक्त है।</p>",
                    correct: "d",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "26",
                    section: "7",
                    question_en: "<p>26.&nbsp;Choose the best alternative which best expresses the meaning of the idiom/ phrase used in the given sentence.<br>Rahul fought <span style=\"text-decoration: underline;\"><strong>tooth and nail</strong></span> to save his company.</p>",
                    question_hi: "<p>26.&nbsp;Choose the best alternative which best expresses the meaning of the idiom/ phrase used in the given sentence.<br>Rahul fought <span style=\"text-decoration: underline;\"><strong>tooth and nail</strong></span> to save his company.</p>",
                    options_en: ["<p>with weapons</p>", "<p>as best as he could</p>", 
                                "<p>using unfair means</p>", "<p>with strength and fury</p>"],
                    options_hi: ["<p>with weapons</p>", "<p>as best as he could</p>",
                                "<p>using unfair means</p>", "<p>with strength and fury</p>"],
                    solution_en: "<p>26.(b) as best as he could<br>Example- We fought tooth and nail to get the error in the bill rectified.</p>",
                    solution_hi: "<p>26.(b) as best as he could/ जितना अच्छा वह कर सकता था<br>उदाहरण - We fought tooth and nail to get the error in the bill rectified./ हम विधेयक (bill) में त्रुटि को सुधारने के लिए जितना जोर लगा सकते था उतना लगाया ।</p>",
                    correct: "b",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "27",
                    section: "7",
                    question_en: "<p>27.&nbsp;Out of the four alternatives choose the one which can be substituted for the given words/sentences.<br>A person who shows people their seats</p>",
                    question_hi: "<p>27.&nbsp;Out of the four alternatives choose the one which can be substituted for the given words/sentences.<br>A person who shows people their seats</p>",
                    options_en: ["<p>usher</p>", "<p>guard</p>", 
                                "<p>watchman</p>", "<p>cameraman</p>"],
                    options_hi: ["<p>usher</p>", "<p>guard</p>",
                                "<p>watchman</p>", "<p>cameraman</p>"],
                    solution_en: "<p>27.(a) <strong>Usher-</strong> a person who shows people their seats <br><strong>Guard-</strong> the state of being ready to prevent attack or danger<br><strong>Watchman-</strong> a man whose job is to guard a building<br><strong>Cameraman-</strong> a person whose job is to operate a camera for a film or a television company</p>",
                    solution_hi: "<p>27.(a) <strong>Usher( स्थान दिखाने वाला )</strong>- a person who shows people their seats <br><strong>Guard(रक्षक)-</strong> the state of being ready to prevent attack or danger<br><strong>Watchman(चौकीदार)-</strong> a man whose job is to guard a building<br><strong>Cameraman</strong>( <strong>कैमराचालक)-</strong> a person whose job is to operate a camera for a film or a television company</p>",
                    correct: "a",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "28",
                    section: "7",
                    question_en: "<p>28. Given below are four jumbled sentences. Pick the option that gives their correct order.<br>P: No partner is expected to air the views of a particular group in public. <br>Q: The only requirement is that the coalition partners have to stick to a code of conduct. <br>R: Every coalition party has to own the responsibility for all government policies or actions. <br>S: Experience has now shown that a coalition government can run as smoothly as any single party government.</p>",
                    question_hi: "<p>28. Given below are four jumbled sentences. Pick the option that gives their correct order.<br>P: No partner is expected to air the views of a particular group in public. <br>Q: The only requirement is that the coalition partners have to stick to a code of conduct. <br>R: Every coalition party has to own the responsibility for all government policies or actions. <br>S: Experience has now shown that a coalition government can run as smoothly as any single party government.</p>",
                    options_en: ["<p>QRPS</p>", "<p>RQPS</p>", 
                                "<p>SQRP</p>", "<p>PQSR</p>"],
                    options_hi: ["<p>QRPS</p>", "<p>RQPS</p>",
                                "<p>SQRP</p>", "<p>PQSR</p>"],
                    solution_en: "<p>28.(c) SQRP <br>Sentence S is the starting line of the parajumble because it tells the subject of the parajumble that is about the coalition government. Then, Sentence Q tells the requirements for a coalition government. So, Q follows S. Going through the options, only option (c) shows Q follows S so Option (c) is the correct answer.</p>",
                    solution_hi: "<p>28.(c) SQRP <br>वाक्य S parajumble की शुरुआती line है क्योंकि यह parajumble के विषय बहुदलीय सरकार के बारे में जानकारी दे रहा है । फिर, वाक्य Q बहुदलीय सरकार की आवश्यकताओं को बताता है। इसलिए S के बाद Q आएगा। विकल्पों के माध्यम से जाने पर, केवल विकल्प (c) दिख रहा है कि S के बाद Q आ रहा है इसलिए विकल्प (c) सही उत्तर है।</p>",
                    correct: "c",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "29",
                    section: "7",
                    question_en: "<p>29. Choose the most appropriate option to change the voice (active/passive) form of the given sentence: <br>Who asked you to follow the instructions blindly?</p>",
                    question_hi: "<p>29. Choose the most appropriate option to change the voice (active/passive) form of the given sentence: <br>Who asked you to follow the instructions blindly?</p>",
                    options_en: ["<p>By who you are asked to follow the instructions blindly?</p>", "<p>By who have you been asked to follow the instructions blindly.</p>", 
                                "<p>By whom were you asked to follow the instructions blindly?</p>", "<p>By whom you were asked to followthe instructions blindly?</p>"],
                    options_hi: ["<p>By who you are asked to follow the instructions blindly?</p>", "<p>By who have you been asked to follow the instructions blindly.</p>",
                                "<p>By whom were you asked to follow the instructions blindly?</p>", "<p>By whom you were asked to followthe instructions blindly?</p>"],
                    solution_en: "<p>29.(c) By whom were you asked to follow the instructions blindly?<br>(a) <strong>By who</strong> you are asked to follow the instructions blindly? (Incorrect words)<br>(b) <strong>By who</strong> have you been asked to follow the instructions blindly. (Incorrect words)<br>(d) By whom <strong>you were asked</strong> to follow the instructions blindly?(Interrogative pattern not followed)</p>",
                    solution_hi: "<p>29.(c) By whom were you asked to follow the instructions blindly?<br>(a) <strong>By who</strong> you are asked to follow the instructions blindly? (गलत words)<br>(b) <strong>By who</strong> have you been asked to follow the instructions blindly. ( गलत words)<br>(d) By whom <strong>you were asked</strong> to follow the instructions blindly?(Interrogative pattern का पालन नहीं किया गया)</p>",
                    correct: "c",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "30",
                    section: "7",
                    question_en: "<p>30.&nbsp;Choose the most appropriate option to change the narration (direct/indirect) of the given sentence: . <br>The maid said, &ldquo;Madam,I will do the dusting tomorrow as I have to take my daughter to the hospital today.&rdquo;</p>",
                    question_hi: "<p>30.&nbsp;Choose the most appropriate option to change the narration (direct/indirect) of the given sentence: . <br>The maid said, &ldquo;Madam,I will do the dusting tomorrow as I have to take my daughter to the hospital today.&rdquo;</p>",
                    options_en: ["<p>The maid said respectfully that she would do the dusting the previous day as she had to take her daughter to the hospital that day</p>", "<p>The maid says respectfully that she would do the dusting the next day as she had to take her daughter to the hospital that day</p>", 
                                "<p>The maid said respectfully that she would do the dusting the next day as she had to take her daughter to the hospital that day</p>", "<p>The maid said respectfully madam that she would do the dusting the next day as she had to take her daughter to the hospital that day.</p>"],
                    options_hi: ["<p>The maid said respectfully that she would do the dusting the previous day as she had to take her daughter to the hospital that day</p>", "<p>The maid says respectfully that she would do the dusting the next day as she had to take her daughter to the hospital that day</p>",
                                "<p>The maid said respectfully that she would do the dusting the next day as she had to take her daughter to the hospital that day</p>", "<p>The maid said respectfully madam that she would do the dusting the next day as she had to take her daughter to the hospital that day.</p>"],
                    solution_en: "<p>30.(c) The maid said respectfully that she would do the dusting the next day as she had to take her daughter to the hospital that day.<br>(a) The maid said respectfully that she would do the dusting <strong>the previous day</strong> as she had to take her daughter to the hospital that day (Incorrect word)<br>(b) The maid <strong>says</strong> respectfully that she would do the dusting the next day as she had to take her daughter to the hospital that day (Incorrect Reporting Verb)<br>(d) The maid said respectfully <strong>madam</strong> that she would do the dusting the next day as she had to take her daughter to the hospital that day. (Incorrect word)</p>",
                    solution_hi: "<p>30.(c) The maid said respectfully that she would do the dusting the next day as she had to take her daughter to the hospital that day.<br>(a) The maid said respectfully that she would do the dusting<strong> the previous day</strong> as she had to take her daughter to the hospital that day (गलत word)<br>(b) The maid <strong>says</strong> respectfully that she would do the dusting the next day as she had to take her daughter to the hospital that day (गलत Reporting Verb)<br>(d) The maid said respectfully <strong>madam </strong>that she would do the dusting the next day as she had to take her daughter to the hospital that day. (गलत word)</p>",
                    correct: "c",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "31",
                    section: "7",
                    question_en: "<p>31. Find a word that is the synonym of the word given<br>Relinquished</p>",
                    question_hi: "<p>31. Find a word that is the synonym of the word given<br>Relinquished</p>",
                    options_en: ["<p>give up</p>", "<p>abdicate</p>", 
                                "<p>leave</p>", "<p>renounce</p>"],
                    options_hi: ["<p>give up</p>", "<p>abdicate</p>",
                                "<p>leave</p>", "<p>renounce</p>"],
                    solution_en: "<p>31.(b) abdicate<br><strong>Abdicate</strong> - fail to fulfil or undertake<br><strong>Renounce</strong> - formally declare one\'s abandonment of (a claim, right, or possession).</p>",
                    solution_hi: "<p>31.(b) abdicate<br>(b) <strong>Abdicate</strong> -पूरा करने में विफल। <br>(d) <strong>Renounce</strong> -औपचारिक रूप से किसी के परित्याग की घोषणा करना (दावा या अधिकार)</p>",
                    correct: "b",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "32",
                    section: "7",
                    question_en: "<p>32.&nbsp;Pick a word opposite in meaning to the word given.<br>Expedited</p>",
                    question_hi: "<p>32.&nbsp;Pick a word opposite in meaning to the word given.<br>Expedited</p>",
                    options_en: ["<p>deferred</p>", "<p>dissolved</p>", 
                                "<p>postponed</p>", "<p>put off</p>"],
                    options_hi: ["<p>deferred</p>", "<p>dissolved</p>",
                                "<p>postponed</p>", "<p>put off</p>"],
                    solution_en: "<p>32.(a) deferred<br><strong>Deferred</strong> - put off (an action or event) to a later time; postpone.<br><strong>Expedited</strong> - make (an action or process) happen sooner or be accomplished more quickly.</p>",
                    solution_hi: "<p>32.(a) deferred<br><strong>Deferred</strong> - बाद में करने के लिए किसी कार्य को रोकना या स्थगित करना।<br><strong>Expedited</strong> - (एक क्रिया या प्रक्रिया) का जल्दी होना या अधिक तेज़ी से पूरा होना। .</p>",
                    correct: "a",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "33",
                    section: "7",
                    question_en: "<p>33.&nbsp; Given below are four jumbled sentences. Pick the option that gives their correct order.<br>P: Biographies of great men can also help us in learning good manners. <br>Q: Courtesy and politeness is the key to good manners. <br>R: No doubt these are little words but if they are spoken at the right moment and in a soft and sweet voice, they are bound to work wonders.<br>S: The use of polite words like \"Sorry\", \"Please\", \"Thank you\", etc. creates a healthy impact on the minds of others.</p>",
                    question_hi: "<p>33.&nbsp; Given below are four jumbled sentences. Pick the option that gives their correct order.<br>P: Biographies of great men can also help us in learning good manners. <br>Q: Courtesy and politeness is the key to good manners. <br>R: No doubt these are little words but if they are spoken at the right moment and in a soft and sweet voice, they are bound to work wonders.<br>S: The use of polite words like \"Sorry\", \"Please\", \"Thank you\", etc. creates a healthy impact on the minds of others.</p>",
                    options_en: ["<p>PRSQ</p>", "<p>RPSQ</p>", 
                                "<p>QPSR</p>", "<p>SPQR</p>"],
                    options_hi: ["<p>PRSQ</p>", "<p>RPSQ</p>",
                                "<p>QPSR</p>", "<p>SPQR</p>"],
                    solution_en: "<p>33.(c) QPSR <br>Sentence Q is the starting line of the parajumble because it tells the subject of the parajumble that is about the key to good manners . Then, Sentence P tells that Biographies of great men help to learn good manners. So, P follows Q, Going through the options, only option (c) shows P follows Q so Option (c) is the correct answer.</p>",
                    solution_hi: "<p>33.(c) QPSR <br>वाक्य Q, parajumble की शुरुआती line है क्योंकि यह parajumble के विषय को बताती है जो अच्छे शिष्टाचार के बारे में है। फिर, Sentence P बताता है कि महापुरुषों की जीवनी अच्छे शिष्टाचार को सीखने में मदद करती है। इसलिए P, Q के बाद आएगा। विकल्पों को देखने पर केवल विकल्प (c) दिखाता है कि Q के बाद P आ रहा है, इसलिए विकल्प (c) सही उत्तर है।</p>",
                    correct: "c",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "34",
                    section: "7",
                    question_en: "<p>34.&nbsp;Identify the segment in the sentence that contains grammatical error from the given options. <br>As soon as I shall reach New Delhi I shall send you the file you have asked for.</p>",
                    question_hi: "<p>34.&nbsp;Identify the segment in the sentence that contains grammatical error from the given options. <br>As soon as I shall reach New Delhi I shall send you the file you have asked for.</p>",
                    options_en: ["<p>No error</p>", "<p>As soon as I shall reach New Delhi</p>", 
                                "<p>I shall send you the</p>", "<p>file you have asked for.</p>"],
                    options_hi: ["<p>No error</p>", "<p>As soon as I shall reach New Delhi</p>",
                                "<p>I shall send you the</p>", "<p>file you have asked for.</p>"],
                    solution_en: "<p>34.(b) As soon as I shall reach New Delhi <br>With &ldquo;as soon as&rdquo; the simple present tense is used. eliminate &ldquo;shall&rdquo;. So the correct phrase will be- As soon as I reach New Delhi.</p>",
                    solution_hi: "<p>34.(b) As soon as I shall reach New Delhi <br>&ldquo;As soon as&rdquo; के साथ simple present tense का उपयोग किया जाता है इसलिए shall हटाना होगा। अतः सही phrase &lsquo; As soon as I reach New Delhi&rsquo; होगा ।</p>",
                    correct: "b",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "35",
                    section: "7",
                    question_en: "<p>35. Select the most appropriate option to substitute the underlined segment in the given sentence. If there is no need to substitute it, select No improvement.<br><span style=\"text-decoration: underline;\">Mutual shakes of hands was exchanged.</span></p>",
                    question_hi: "<p>35. Select the most appropriate option to substitute the underlined segment in the given sentence. If there is no need to substitute it, select No improvement.<br><span style=\"text-decoration: underline;\">Mutual shakes of hands was exchanged.</span></p>",
                    options_en: ["<p>Both shakes of hands was carried.</p>", "<p>The hand shakes were exchanged.</p>", 
                                "<p>They shook hands with each other.</p>", "<p>No improvement.</p>"],
                    options_hi: ["<p>Both shakes of hands was carried.</p>", "<p>The hand shakes were exchanged.</p>",
                                "<p>They shook hands with each other.</p>", "<p>No improvement.</p>"],
                    solution_en: "<p>35.(c) They shook hands with each other.</p>",
                    solution_hi: "<p>35.(c) They shook hands with each other./ उन्होंने एक दूसरे से हाथ मिलाया।</p>",
                    correct: "c",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "36",
                    section: "7",
                    question_en: "<p>36. Choose the most appropriate option to change the voice (active/passive) form of the given sentence: <br>Don\'t subject the animals to experiments.</p>",
                    question_hi: "<p>36. Choose the most appropriate option to change the voice (active/passive) form of the given sentence: <br>Don\'t subject the animals to experiments.</p>",
                    options_en: ["<p>The animals are not to be subjected to experiments.</p>", "<p>The animals shall not be subjected to experiments.</p>", 
                                "<p>The animals will not be subjected to experiments.</p>", "<p>The animals should not be subjected to experiments.</p>"],
                    options_hi: ["<p>The animals are not to be subjected to experiments.</p>", "<p>The animals shall not be subjected to experiments.</p>",
                                "<p>The animals will not be subjected to experiments.</p>", "<p>The animals should not be subjected to experiments.</p>"],
                    solution_en: "<p>36.(a) The animals are not to be subjected to experiments.<br>(b) The animals <strong>shall</strong> not be subjected to experiments. (Tense has changed)<br>(c) The animals <strong>will</strong> not be subjected to experiments. (Tense has changed)<br>(d) The animals <strong>should not be subjected</strong> to experiments. (Tone of the sentence is changed)</p>",
                    solution_hi: "<p>36.(a) The animals are not to be subjected to experiments.<br>(b) The animals <strong>shall</strong> not be subjected to experiments. (Tense बदल गया )<br>(c) The animals <strong>will</strong> not be subjected to experiments. (Tense बदल गया )<br>(d) The animals <strong>should not be subjected</strong> to experiments. (Sentence का Tone बदल गया)</p>",
                    correct: "a",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "37",
                    section: "7",
                    question_en: "<p>37. Choose the most appropriate option to change the narration (direct/indirect) of the given sentence: . <br>The PM said to the citizens &ldquo;We are all responsible for the kind of country we live in. For after all we ourselves create or destroy our nation \"</p>",
                    question_hi: "<p>37. Choose the most appropriate option to change the narration (direct/indirect) of the given sentence: . <br>The PM said to the citizens &ldquo;We are all responsible for the kind of country we live in. For after all we ourselves create or destroy our nation \"</p>",
                    options_en: ["<p>The PM said to the citizens that we are all responsible for the kind of country we live in. For after all we ourselves create or destroy our nation.</p>", "<p>The PM said to the citizens that they are all responsible for the kind of country we live in. For after all we ourselves create or destroy our nation</p>", 
                                "<p>The PM said to the citizens that we are all responsibly for the kind of country we live in. For after all we ourselves create or destroy our nation</p>", "<p>The PM said to the citizens that we are all responsible for the kind of country we lives in. For after all we ourselves create or destroy our nation</p>"],
                    options_hi: ["<p>The PM said to the citizens that we are all responsible for the kind of country we live in. For after all we ourselves create or destroy our nation.</p>", "<p>The PM said to the citizens that they are all responsible for the kind of country we live in. For after all we ourselves create or destroy our nation</p>",
                                "<p>The PM said to the citizens that we are all responsibly for the kind of country we live in. For after all we ourselves create or destroy our nation</p>", "<p>The PM said to the citizens that we are all responsible for the kind of country we lives in. For after all we ourselves create or destroy our nation</p>"],
                    solution_en: "<p>37.(a)The PM said to the citizens that we are all responsible for the kind of country we live in. For after all we ourselves create or destroy our nation.<br>(b) The PM said to the citizens that <strong>they</strong> are all responsible for the kind of country we live in. For after all we ourselves create or destroy our nation (Incorrect word)<br>(c) The PM said to the citizens that we are all <strong>responsibly</strong> for the kind of country we live in. For after all we ourselves create or destroy our nation (Incorrect word)<br>(d) The PM said to the citizens that we are all responsible for the kind of country we <strong>lives</strong> in. For after all we ourselves create or destroy our nation (Incorrect word)</p>",
                    solution_hi: "<p>37.(a)The PM said to the citizens that we are all responsible for the kind of country we live in. For after all we ourselves create or destroy our nation.<br>(b) The PM said to the citizens that <strong>they</strong> are all responsible for the kind of country we live in. For after all we ourselves create or destroy our nation (गलत word)<br>(c) The PM said to the citizens that we are all <strong>responsibly</strong> for the kind of country we live in. For after all we ourselves create or destroy our nation (गलत word)<br>(d) The PM said to the citizens that we are all responsible for the kind of country we <strong>lives</strong> in. For after all we ourselves create or destroy our nation (गलत word)</p>",
                    correct: "a",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "38",
                    section: "7",
                    question_en: "<p>38. Select the most appropriate option to fill in the blank.<br>Like any other country India has its ______ share of superstitions.</p>",
                    question_hi: "<p>38. Select the most appropriate option to fill in the blank.<br>Like any other country India has its ______ share of superstitions.</p>",
                    options_en: ["<p>peculiar</p>", "<p>fair</p>", 
                                "<p>proper</p>", "<p>abundant</p>"],
                    options_hi: ["<p>peculiar</p>", "<p>fair</p>",
                                "<p>proper</p>", "<p>abundant</p>"],
                    solution_en: "<p>38.(b) fair<br>&lsquo;Fair&rsquo; means appropriate and acceptable in a particular situation. The given sentence states that like any other country India has its fair(appropriate) share of superstitions. Hence, &lsquo;fair&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>38.(b) fair<br>&lsquo;Fair&rsquo;\' का अर्थ है- किसी विशेष स्थिति में उचित और स्वीकार्य। दिए गए वाक्य में कहा गया है कि किसी भी अन्य देश की तरह भारत में भी अंधविश्वास का अपना उचित (appropriate) स्थान है। इसलिए, &lsquo;Fair&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "39",
                    section: "7",
                    question_en: "<p>39. Given below are four jumbled sentences. Pick the option that gives their correct order.<br>P: All cursed the stone and blamed the Government.<br>Q: Then the king had the stone removed.<br>R: Next day people passed by and went round it.<br>S: The king was distressed because his people were lazy so he had a big stone put in the middle of the road one night.</p>",
                    question_hi: "<p>39. Given below are four jumbled sentences. Pick the option that gives their correct order.<br>P: All cursed the stone and blamed the Government.<br>Q: Then the king had the stone removed.<br>R: Next day people passed by and went round it.<br>S: The king was distressed because his people were lazy so he had a big stone put in the middle of the road one night.</p>",
                    options_en: ["<p>PSRQ</p>", "<p>SRPQ</p>", 
                                "<p>QPRS</p>", "<p>PQRS</p>"],
                    options_hi: ["<p>PSRQ</p>", "<p>SRPQ</p>",
                                "<p>QPRS</p>", "<p>PQRS</p>"],
                    solution_en: "<p>39.(b) SRPQ<br>Sentence S is the starting line of the parajumble because it tells the subject of the parajumble - a big stone.. Then, Sentence R tells the next day scenario. So, R follows S. Going through the options, only option (c) shows R follows S so Option (c) is the correct answer.</p>",
                    solution_hi: "<p>39.(b) SRPQ<br>Sentence S, parajumble की शुरुआती line है क्योंकि यह parajumble के विषय को बताता है - एक बड़ा पत्थर । फिर, Sentence R अगले दिन का परिदृश्य बताता है। तो S के बाद R आएगा। विकल्पों के माध्यम से जाने पर, केवल विकल्प (c) में दिखता है कि S के बाद R आ रहा है, इसलिए विकल्प (c) सही उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "40",
                    section: "7",
                    question_en: "<p>40. Choose the alternative which best expresses the meaning of the idiom/phrase.<br>At the helm of</p>",
                    question_hi: "<p>40. Choose the alternative which best expresses the meaning of the idiom/phrase.<br>At the helm of</p>",
                    options_en: ["<p>at the helm of the ship</p>", "<p>at the main situation</p>", 
                                "<p>in the centre of the ship</p>", "<p>in the centre of a storm</p>"],
                    options_hi: ["<p>at the helm of the ship</p>", "<p>at the main situation</p>",
                                "<p>in the centre of the ship</p>", "<p>in the centre of a storm</p>"],
                    solution_en: "<p>40.(b) at the main situation<br>Example- The pilot of the plane was at the helm during the bad weather.</p>",
                    solution_hi: "<p>40.(b) at the main situation/मुख्य स्थिति में। <br>उदाहरण - The pilot of the plane was at the helm during the bad weather./खराब मौसम के दौरान विमान का पायलट मुख्य स्थिति में था ।</p>",
                    correct: "b",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "41",
                    section: "7",
                    question_en: "<p>41. Select the word which means the same as the group of words given.<br>Violation of something holy or sacred</p>",
                    question_hi: "<p>41. Select the word which means the same as the group of words given.<br>Violation of something holy or sacred</p>",
                    options_en: ["<p>Profanity</p>", "<p>Sedition</p>", 
                                "<p>Sacrilege</p>", "<p>Slander</p>"],
                    options_hi: ["<p>Profanity</p>", "<p>Sedition</p>",
                                "<p>Sacrilege</p>", "<p>Slander</p>"],
                    solution_en: "<p>41.(c) Sacrilege<br>(a) <strong>Profanity-</strong>showing no respect for a god or a religion, especially through language<br>(b) <strong>Sedition-</strong> conduct or speech inciting people to rebel against the authority of a state or monarch.<br>(c) <strong>Sacrilege</strong> - violation or misuse of what is regarded as sacred.<br>(d) <strong>Slander-</strong> The action or crime of making a false spoken statement damaging to a person\'s reputation.</p>",
                    solution_hi: "<p>41.(c) Sacrilege<br>(a) <strong>Profanity</strong> -किसी ईश्वर या धर्म के प्रति सम्मान ना दिखाना। <br>(b) <strong>Sedition</strong> -किसी राज्य या सम्राट के खिलाफ लोगों को विद्रोह करने के लिए उकसाने वाला आचरण या भाषण।<br>(c) <strong>Sacrilege</strong> - पवित्र वस्&zwj;तु या स्&zwj;थान का अपमान करना।<br>(d) <strong>Slander</strong> - किसी व्यक्ति की प्रतिष्ठा को नुकसान पहुँचाने वाला, झूठा बयान।</p>",
                    correct: "c",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "42",
                    section: "7",
                    question_en: "<p>42. Identify the segment in the sentence, which contains the grammatical error.<br>The feelings of frustration and dejection are momentous when one is contemplating suicide.</p>",
                    question_hi: "<p>42. Identify the segment in the sentence, which contains the grammatical error.<br>The feelings of frustration and dejection are momentous when one is contemplating suicide.</p>",
                    options_en: ["<p>The feelings of frustration</p>", "<p>and dejection are momentous</p>", 
                                "<p>when one is contemplating suicide.</p>", "<p>No error</p>"],
                    options_hi: ["<p>The feelings of frustration</p>", "<p>and dejection are momentous</p>",
                                "<p>when one is contemplating suicide.</p>", "<p>No error</p>"],
                    solution_en: "<p>42.(b) and dejection are momentous<br>&lsquo;Momentous&rsquo; which means something important is an incorrect word in the context of the sentence because the sentence is talking about feelings that last for a while(momentary). Hence, &lsquo;Momentary&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>42.(b) and dejection are momentous<br>&lsquo;Momentous&rsquo; जिसका अर्थ है- कुछ महत्वपूर्ण, जो वाक्य के संदर्भ में गलत शब्द है क्योंकि वाक्य उन भावनाओं के बारे में बात कर रहा है जो थोड़ी देर(momentary) के लिए होती है। इसलिए, &lsquo;Momentary&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "43",
                    section: "7",
                    question_en: "<p>43. Identify the best way to improve the underlined part of the given sentence. If there is no improvement required, select &lsquo;no improvement&rsquo;.<br>The reason <span style=\"text-decoration: underline;\"><strong>why he wrote the letter was because</strong></span> he could not contact him over the phone.</p>",
                    question_hi: "<p>43. Identify the best way to improve the underlined part of the given sentence. If there is no improvement required, select &lsquo;no improvement&rsquo;.<br>The reason <span style=\"text-decoration: underline;\"><strong>why he wrote the letter was because</strong></span> he could not contact him over the phone.</p>",
                    options_en: ["<p>why he wrote the letter was since</p>", "<p>for which he wrote the letter because</p>", 
                                "<p>why he wrote the letter was that</p>", "<p>no improvement</p>"],
                    options_hi: ["<p>why he wrote the letter was since</p>", "<p>for which he wrote the letter because</p>",
                                "<p>why he wrote the letter was that</p>", "<p>no improvement</p>"],
                    solution_en: "<p>43.(c) why he wrote the letter was that<br>We cannot write &lsquo;the reason why&rsquo; and &lsquo;because&rsquo; together in a sentence because it becomes the case of superfluousness (not necessary). Hence, &lsquo;why he wrote the letter was that&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>43.(c) why he wrote the letter was that<br>हम एक वाक्य में &lsquo;the reason why&rsquo; और &lsquo;because&rsquo;एक साथ नहीं लिख सकते क्योंकि यह superfluousness अतिश्योक्ति (आवश्यक नहीं) की स्थिति बन जाती है। इसलिए,&lsquo;why he wrote the letter was that&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "c",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "44",
                    section: "7",
                    question_en: "44. Select the wrongly spelt word.",
                    question_hi: "44. Select the wrongly spelt word.",
                    options_en: ["  fastidious        ", "  facsimile", 
                                "  fasinated           ", "  fashion"],
                    options_hi: ["  fastidious        ", "  facsimile",
                                "  fasinated           ", "  fashion"],
                    solution_en: "44.(c) fasinated<br />Fascinated is correct spelt word.",
                    solution_hi: "44.(c)  fasinated<br />Fascinated सही शब्द है।",
                    correct: "c",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "45",
                    section: "18",
                    question_en: "45. Select the most appropriate option to fill in the blank. <br />He believes that he has the right to  ________ while others should work hard.",
                    question_hi: "45. Select the most appropriate option to fill in the blank. <br />He believes that he has the right to  ________ while others should work hard.",
                    options_en: [" sit back", " sit up", 
                                " sit down           ", " sit and sit"],
                    options_hi: [" sit back", " sit up",
                                " sit down           ", " sit and sit"],
                    solution_en: "45. (a) sit back<br />Sit back is a phrasal verb which means - To sit back and relax and take no action.",
                    solution_hi: "45.(a) sit back.<br />Sit Back एक Phrasal verb है जिसका अर्थ है - आराम से बैठना और आराम करना और किसी विषय पर कोई कार्रवाई न करना।",
                    correct: "a",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "46",
                    section: "18",
                    question_en: "46. Where is the fair of Pir Budhan held every year in India?",
                    question_hi: "46. भारत में प्रतिवर्ष पीर बुधन का मेला कहाँ लगता है ?",
                    options_en: [" Uttar Pradesh ", " Haryana", 
                                " Uttarakhand      ", " Madhya Pradesh"],
                    options_hi: [" उत्तर प्रदेश ", " हरियाणा",
                                " उत्तराखंड ", " मध्य प्रदेश"],
                    solution_en: "46.(d) The fair of Pir Budhan is held every year in the month of August- September in sawnra area of the Shivpuri district, Madhya Pradesh. Other important fairs of Madhya Pradesh are Fair of Nagaji, Fair of kanababa, Fair of Mahamritunjay, Tejaji, Singaji, Mandhata, Dhamoni Ursa, Garibnath baba Ka mela, kaluji Maharaj, Madai ka mela, Gotmar mela, etc. ",
                    solution_hi: "46.(d) पीर बुधन का मेला हर साल अगस्त-सितंबर के महीने में मध्य प्रदेश के शिवपुरी जिले के सावनरा क्षेत्र में आयोजित किया जाता है। मध्य प्रदेश के अन्य महत्वपूर्ण मेले नागाजी का मेला, कनबाबा का मेला, महामृत्युंजय का मेला, तेजाजी, सिंगाजी, मांधाता, धमोनी उर्स, गरीबनाथ बाबा का मेला, कालूजी महाराज, मड़ई का मेला, गोटमार मेला आदि हैं।",
                    correct: "d",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "47",
                    section: "18",
                    question_en: "47. Which among the following terms denotes the hilly parts of the Karnataka plateau?",
                    question_hi: "47. निम्नलिखित में से कौन सा शब्द कर्नाटक पठार के पहाड़ी भागों को दर्शाता है?",
                    options_en: [" Bugyals", " Terai", 
                                " Malnad", " Bets"],
                    options_hi: [" बुग्याल ", " तराई",
                                " मलनाड ", " बेट्स"],
                    solution_en: "<p>47.(c) <strong>\'Malnad\'</strong> means hilly region in Kannada language. Malnad region of Karnataka is famous for its coffee plantations. <strong>Bugyals</strong> are alpine pasture lands, or meadows in the Indian state of Uttarakhand. <strong>Terai</strong> is a low-lying area that is composed of marshy land in Nepal and Northern India. The flood plains which are formed due to repeated deposition of new alluvium during each flood is known as Bet.</p>",
                    solution_hi: "<p>47.(c) <strong>\'मलनाड\'</strong> का अर्थ कन्नड़ भाषा में पहाड़ी क्षेत्र है। कर्नाटक का मलनाड क्षेत्र अपने कॉफी बागानों के लिए प्रसिद्ध है। <strong>बुग्याल</strong> भारतीय राज्य उत्तराखंड में अल्पाइन चारागाह भूमि या घास के मैदान हैं। <strong>तराई</strong> एक निचला इलाका है जो नेपाल और उत्तरी भारत में दलदली भूमि से बना है। बाढ़ के मैदान जो प्रत्येक बाढ़ के दौरान नए जलोढ़ के बार-बार जमा होने के कारण बनते हैं, उन्हें बेट के रूप में जाना जाता है।</p>",
                    correct: "c",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "48",
                    section: "18",
                    question_en: "<p>48. Hurkiya Baul is a folk dance form associated with which Indian state?</p>",
                    question_hi: "<p>48. हुरकिया बाउल किस भारतीय राज्य से जुड़ा एक लोक नृत्य है?</p>",
                    options_en: ["<p>Uttarakhand</p>", "<p>Goa</p>", 
                                "<p>Haryana</p>", "<p>Maharashtra</p>"],
                    options_hi: ["<p>उत्तराखंड</p>", "<p>गोवा</p>",
                                "<p>हरियाणा</p>", "<p>महाराष्ट्र</p>"],
                    solution_en: "<p>48.(a) <strong>Uttarakhand</strong> &rarr; Mandan, Thadiya, Chauphula, Cholia, Jhumailo. <strong>Haryana</strong> &rarr; Kheda dance, Ghoomar dance, Dhamaal dance,&nbsp;Phag dance. <strong>Maharashtra</strong> &rarr; Dindi, Koli, Lavani, Powada.&nbsp;<strong>Goa</strong> &rarr; Fugdi, Dekhni, Shigmo, Ghode, Jagor, Gonf, Tonya Mel.</p>",
                    solution_hi: "<p>48.(a) <strong>उत्तराखंड</strong> &rarr; मंडाण ,थडिया, चौंफुला, छोलिया, झुमैलो। <strong>हरियाणा</strong> &rarr; खेड़ा नृत्य, घूमर नृत्य, धमाल नृत्य,फाग नृत्य। <strong>महाराष्ट्र</strong> &rarr; डिंडी, कोली, लावणी,पोवाड़ा। <strong>गोवा</strong> &rarr; फुगड़ी,देक्खनी, शिग्मो, घोडे, जगोर, गोंफ, टोन्या मेल।</p>",
                    correct: "a",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "49",
                    section: "18",
                    question_en: "<p>49. \'The Emperor\'s New Clothes\' is a children\'s story by ______.</p>",
                    question_hi: "<p>49. \'द एम्परर्स न्यू क्लोथ्स\' ______ द्वारा बच्चों की कहानी है।</p>",
                    options_en: ["<p>Enid Blyton</p>", "<p>Hans Christian Andersen</p>", 
                                "<p>Roald Dahl</p>", "<p>Oscar Wilde</p>"],
                    options_hi: ["<p>एनीड ब्लिटन</p>", "<p>हैंस क्रिश्चियन एंडरसन</p>",
                                "<p>रोल्ड डाहल</p>", "<p>ऑस्कर वाइल्ड</p>"],
                    solution_en: "<p>49.(b) Books by Hans Christian Andersen: &lsquo;The Emperor\'s New Clothes&rsquo;, &lsquo;Fairy Tales&rsquo;, &lsquo;Fairy Tales of Hans Christian Andersen&rsquo;, &lsquo;The Illustrated Ugly Duckling&rsquo;, &lsquo;Den Lille Pige med Svovlstikkerne&rsquo;.</p>",
                    solution_hi: "<p>49.(b) हैंस क्रिश्चियन एंडरसन की पुस्तकें: \'द एम्परर्स न्यू क्लॉथ्स\', \'फेयरी टेल्स\', \'फेयरी टेल्स ऑफ हैंस क्रिश्चियन एंडरसन\', \'द इलस्ट्रेटेड अग्ली डकलिंग\', \'डेन लिले पिगे मेड स्वोव्लस्टिक्केर्न\'।</p>",
                    correct: "b",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "50",
                    section: "18",
                    question_en: "<p>50. Which nation has hosted the Commonwealth Games five times?</p>",
                    question_hi: "<p>50. किस देश ने पांच बार राष्ट्रमंडल खेलों की मेजबानी की है?</p>",
                    options_en: ["<p>Australia</p>", "<p>England</p>", 
                                "<p>Canada</p>", "<p>New Zealand</p>"],
                    options_hi: ["<p>ऑस्ट्रेलिया</p>", "<p>इंगलैंड</p>",
                                "<p>कनाडा</p>", "<p>न्यूजीलैंड</p>"],
                    solution_en: "<p>50.(a) <strong>Australia</strong> (Firstly hosted in 1938.) England hosted &rarr; two occasions (1934, 2000). Canada&rarr; Four occasions. New Zealand&rarr; three occasions.The 23rd season of the Commonwealth Games will be held in five cities in the Australian state of Victoria in March 2026.</p>",
                    solution_hi: "<p>50.(a) <strong>ऑस्ट्रेलिया</strong> ( सबसे पहले 1938 में मेजबानी )। इंग्लैंड ने मेजबानी की &rarr;दो बार (1934, 2000)। कनाडा &rarr; चार बार । न्यूज़ीलैंड &rarr; तीन बार। राष्ट्रमंडल खेलों का 23वां सीजन मार्च 2026 में ऑस्ट्रेलिया के विक्टोरिया राज्य के पांच शहरों में आयोजित किया जाएगा।</p>",
                    correct: "a",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "51",
                    section: "18",
                    question_en: "<p>51. Which of the following countries is a member of SAARC?</p>",
                    question_hi: "<p>51. निम्नलिखित में से कौन सा देश SAARC का सदस्य है?</p>",
                    options_en: ["<p>Myanmar</p>", "<p>Mauritius</p>", 
                                "<p>China</p>", "<p>Afghanistan</p>"],
                    options_hi: ["<p>म्यांमार</p>", "<p>मॉरीशस</p>",
                                "<p>चीन</p>", "<p>अफ़ग़ानिस्तान</p>"],
                    solution_en: "<p>51.(d) <strong>Afghanistan.</strong> The South Asian Association for Regional Cooperation member states are Afghanistan, Bangladesh, Bhutan, India, Maldives, Nepal, Pakistan, and Sri Lanka. SAARC founded in Dhaka on 8 December 1985</p>",
                    solution_hi: "<p>51.(d) <strong>अफगानिस्तान</strong> । दक्षिण एशियाई क्षेत्रीय सहयोग संघ के सदस्य देश अफगानिस्तान, बांग्लादेश, भूटान, भारत, मालदीव, नेपाल, पाकिस्तान और श्रीलंका हैं। सार्क की स्थापना 8 दिसंबर 1985 को ढाका में हुई थी |</p>",
                    correct: "d",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "52",
                    section: "18",
                    question_en: "52. The ______ is a single string musical instrument made using bamboo and a gourd, mostly used by traditional folk singers in Maharashtra.",
                    question_hi: "52. _____ एक एकल तार वाला संगीत वाद्य यंत्र है जो बांस और तुम्बी का प्रयोग करके बनाया जाता है तथा अधिकांशतः महाराष्ट्र के पारंपरिक लोक गायकों के द्वारा इस्तेमाल किया जाता है।  ",
                    options_en: [" Edakka", " Ekkalam", 
                                " Esraj", " Ektara"],
                    options_hi: [" एडाक्का  ", " एक्कलम  ",
                                " इसराज", " इकतारा"],
                    solution_en: "<p>52.(d) <strong>Ektara</strong> is a drone flute consisting of a gourd resonator covered with skin, through which a bamboo neck is inserted. Ektara means &ldquo;one string . <strong>Ekkalam</strong> is an aerophone instrument mainly used in Tamil Nadu, India. It consists of a large brass tube with one end having a bell. <strong>Edakka,</strong> is an hourglass-shaped drum . <strong>Esraj</strong> is an Indian stringed instrument .</p>",
                    solution_hi: "<p>52.(d) <strong>एकतारा</strong> | <br><strong>एकतारा</strong> एक ड्रोन बांसुरी है जिसमें त्वचा से ढकी एक लौकी गुंजयमान यंत्र होता है, जिसके माध्यम से एक बांस की गर्दन डाली जाती है। एकतारा का अर्थ है \"एक तार। <strong>एक्कलम</strong> मुख्य रूप से तमिलनाडु, भारत में इस्तेमाल किया जाने वाला एक एरोफोन उपकरण है। इसमें एक बड़ी पीतल की नली होती है जिसके एक सिरे पर घंटी होती है। <strong>एडाक्का</strong> , एक घंटे(hourglass) के आकार का ड्रम है। <strong>इसराज</strong> एक भारतीय तार वाला वाद्य यंत्र है।</p>",
                    correct: "d",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "53",
                    section: "18",
                    question_en: "<p>53. Which of the following books did the famous cricketer Sunil Gavaskar author?</p>",
                    question_hi: "<p>53. प्रसिद्ध क्रिकेटर सुनील गावस्कर ने निम्नलिखित में से किस पुस्तक के लेखक थे?</p>",
                    options_en: ["<p>Democracy&rsquo;s XI: The Great Indian Cricket Story</p>", "<p>Runs \'n Ruins</p>", 
                                "<p>Playing It My Way</p>", "<p>Captain Cool</p>"],
                    options_hi: ["<p>डेमोक्रेसी इलेवन: द ग्रेट इंडियन क्रिकेट स्टोरी</p>", "<p>रन एंड रुइन्स</p>",
                                "<p>प्लेइंग इट माई वे</p>", "<p>कैप्टन कूल</p>"],
                    solution_en: "<p>53.(b) <strong>Playing It My Way</strong> autobiography of Sachin Tendulkar. <strong>Runs\' and Ruins</strong>. Democracy&rsquo;s XI: The Great Indian Cricket Story (Rajdeep Sardesai). <strong>Captain Cool</strong> by Gulu Ezekiel (story of MS Dhoni).</p>",
                    solution_hi: "<p>53.(b) <strong>रन एंड रुइन्स।</strong> डेमोक्रेसी इलेवन: द ग्रेट इंडियन क्रिकेट स्टोरी (राजदीप सरदेसाई)। <strong>प्लेइंग इट माई</strong> वे सचिन तेंदुलकर की आत्मकथा। गुलु ईजेकील द्वारा <strong>कैप्टन कूल</strong> (MS धोनी की कहानी)।</p>",
                    correct: "b",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "54",
                    section: "18",
                    question_en: "<p>54. Geneva is the headquarters of _____.</p>",
                    question_hi: "<p>54. जिनेवा _____ का मुख्यालय है।</p>",
                    options_en: ["<p>WTO</p>", "<p>UNICEF</p>", 
                                "<p>IMF</p>", "<p>UNESCO</p>"],
                    options_hi: ["<p>WTO</p>", "<p>UNICEF</p>",
                                "<p>IMF</p>", "<p>UNESCO</p>"],
                    solution_en: "<p>54.(a) <strong>World Trade Organisation</strong> (Formed: 1 January 1995; headquarters : Geneva, Switzerland; Director-General: Ngozi Okonjo-Iweala (As on November 2022). <strong>United Nations International Children\'s Emergency Fund</strong> (11 December 1946; New York, United States). <strong>International Monetary Fund</strong> (July 1944; Washington DC, US). <strong>United Nations Educational, Scientific and Cultural Organization</strong> (16 November 1945; Paris, France).</p>",
                    solution_hi: "<p>54.(a) <strong>WTO; विश्व व्यापार संगठन</strong> (गठित: 1 जनवरी 1995; मुख्यालय: जिनेवा, स्विट्जरलैंड; महानिदेशक: न्गोजी ओकोन्जो-इवेला (नवंबर 2022 तक)। <strong>UNICEF;</strong> संयुक्त राष्ट्र अंतर्राष्ट्रीय बाल आपातकालीन कोष (11 दिसंबर 1946; न्यूयॉर्क, संयुक्त राज्य अमेरिका)। <strong>IMF;</strong> अंतर्राष्ट्रीय मुद्रा कोष (जुलाई 1944; वाशिंगटन DC, US) <strong>UNESCO;</strong> संयुक्त राष्ट्र शैक्षिक, वैज्ञानिक और सांस्कृतिक संगठन (16 नवंबर 1945; पेरिस, फ्रांस)।</p>",
                    correct: "a",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "55",
                    section: "18",
                    question_en: "<p>55. Which of the following states has the largest catchment area of Godavari Basin?</p>",
                    question_hi: "<p>55. निम्नलिखित में से किस राज्य में गोदावरी बेसिन का सबसे बड़ा जलग्रहण क्षेत्र है?</p>",
                    options_en: ["<p>Karnataka</p>", "<p>Maharashtra</p>", 
                                "<p>Madhya Pradesh</p>", "<p>Telangana</p>"],
                    options_hi: ["<p>कर्नाटक</p>", "<p>महाराष्ट्र</p>",
                                "<p>मध्य प्रदेश</p>", "<p>तेलंगाना</p>"],
                    solution_en: "<p>55.(b) <strong>Maharashtra.</strong> Banganga, Kadva, Shivana, Purna, Kadam, Pranahita, Indravati, Taliperu, Sabari, Nasardi, Pravara, Sindphana, Manjira, Manair, Kinnerasani are <strong>tributaries</strong> of Godavari river. Godavari river is called Dakshina Ganga. <strong>Origin</strong> &rarr; Tryambak Hill, Nashik District, Maharashtra.</p>",
                    solution_hi: "<p>55.(b) <strong>महाराष्ट्र</strong> । गोदावरी नदी की सहायक नदियाँ बाणगंगा, कदवा, शिवना, पूर्णा, कदम, प्राणहिता, इंद्रावती, तालीपेरु, सबरी, नसरदी, प्रवर, सिंधफाना, मंजीरा, मनैर, किन्नरसानी हैं। गोदावरी नदी को दक्षिणा गंगा (दक्षिण की गंगा) कहा जाता है। <strong>उत्पत्ति &rarr; त्रयंबक पहाड़ी</strong> ,नासिक जिला ,महाराष्ट्र |</p>",
                    correct: "b",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "56",
                    section: "18",
                    question_en: "<p>56. How many protons are there in a lithium nucleus?</p>",
                    question_hi: "<p>56. लिथियम नाभिक में कितने प्रोटॉन होते हैं?</p>",
                    options_en: ["<p>2</p>", "<p>4</p>", 
                                "<p>5</p>", "<p>3</p>"],
                    options_hi: ["<p>2</p>", "<p>4</p>",
                                "<p>5</p>", "<p>3</p>"],
                    solution_en: "<p>56.(d) <strong>Lithium</strong> nucleus has 3 protons (which gives the nucleus a charge of +3 ) and 4 neutrons (giving it a total mass number of 7). lithium Uses in rechargeable batteries for mobile phones, laptops, digital cameras and electric vehicles. Although it is a metal, it is soft enough to cut with a knife. It is so light it can float on water.</p>",
                    solution_hi: "<p>56.(d) <strong>लिथियम</strong> के नाभिक में 3 प्रोटॉन (जो नाभिक को +3 का चार्ज देता है,) और 4 न्यूट्रॉन (इसे कुल द्रव्यमान संख्या 7 देता है) होते हैं। लिथियम उपयोग &rarr; मोबाइल फोन, लैपटॉप, डिजिटल कैमरा और इलेक्ट्रिक वाहनों के लिए रिचार्जेबल बैटरी | हालांकि यह एक धातु है, यह काफी नरम होता है जिससे इसे चाकू से काटा जा सकता है यह इतना हल्का होता है कि पानी में भी तैर सकता है।</p>",
                    correct: "d",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "57",
                    section: "18",
                    question_en: "<p>57. What is the freezing point of water on the Kelvin scale?</p>",
                    question_hi: "<p>57. केल्विन पैमाने पर जल का हिमांक कितना होता है?</p>",
                    options_en: ["<p>173.15 K</p>", "<p>473.15 K</p>", 
                                "<p>373.15 K</p>", "<p>273.15 K</p>"],
                    options_hi: ["<p>173.15 K</p>", "<p>473.15 K</p>",
                                "<p>373.15 K</p>", "<p>273.15 K</p>"],
                    solution_en: "<p>57.(d) The freezing point of water is <strong>273.15 Kelvin (K)</strong> or 0&deg; Centigrade or 32&deg; Fahrenheit.</p>",
                    solution_hi: "<p>57(d) पानी का हिमांक<strong> 273.15 केल्विन (k)</strong> या 0&deg; सेंटीग्रेड या 32&deg; फ़ारेनहाइट होता है।</p>",
                    correct: "d",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "58",
                    section: "18",
                    question_en: "58.As per the Natya Shastra, idiophones are called ______.",
                    question_hi: "58. नाट्य शास्त्र के अनुसार, इडियोफोन्स को ______ कहा जाता है।",
                    options_en: [" Ghan Vadya        ", " Sushir Vadya", 
                                " Avanaddha Vadya  ", " Tat Vadya"],
                    options_hi: [" घन वाद्य  ", " सुशिर वाद्य",
                                " अवनद्ध वाद्य ", " तत् वद्य"],
                    solution_en: "58.(a) Natya Shastra by Bharat Muni (composed between 200 BC and 200 AD) clubbed musical instruments into four groups: Avanaddha Vadya (membranophones or percussion instruments), Ghana Vadya (idiophones or solid instruments), Sushir Vadya (aerophones or wind instruments), and Tat Vadya (chordophones or stringed instruments). Later, Greek labels were assigned to the four classifications - Chordophones for Tat Vadya, Membranophones for Avanaddha Vadya, Aerophones for Sushir Vadya, and Autophones for Ghan Vadya. Thus, the western system of classification is based on the ancient Indian Natya Shastra. ",
                    solution_hi: "58.(a) भरत मुनि द्वारा नाट्य शास्त्र (200 ईसा पूर्व और 200 ईस्वी के बीच रचित) ने संगीत वाद्ययंत्रों को चार समूहों में बांटा: अवनद्ध वाद्य (मेम्ब्रानोफ़ोन या पर्क्यूशन इंस्ट्रूमेंट्स), घन वाद्य (इडियोफ़ोन या सॉलिड इंस्ट्रूमेंट्स), सुशिर वाद्य (एरोफ़ोन या विंड इंस्ट्रूमेंट्स), और टाट वाद्य (कॉर्डोफ़ोन या तार वाले वाद्य यंत्र)। बाद में, ग्रीक लेबल को चार वर्गीकरणों के लिए सौंपा गया - टाट वाद्य के लिए कॉर्डोफ़ोन, अवनद्ध वाद्य के लिए मेम्ब्रानोफ़ोन, सुशिर वाद्य के लिए एरोफ़ोन और घन वाद्य के लिए ऑटोफ़ोन। इस प्रकार, वर्गीकरण की पश्चिमी प्रणाली प्राचीन भारतीय नाट्य शास्त्र पर आधारित है।",
                    correct: "a",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "59",
                    section: "18",
                    question_en: "<p>59. Around which year did the construction of Taj Mahal Complex begin?</p>",
                    question_hi: "<p>59. ताजमहल परिसर का निर्माण किस वर्ष के आसपास शुरू हुआ था?</p>",
                    options_en: ["<p>1641 AD</p>", "<p>1651 AD</p>", 
                                "<p>1632 AD</p>", "<p>1621 AD</p>"],
                    options_hi: ["<p>1641 AD</p>", "<p>1651 AD</p>",
                                "<p>1632 AD</p>", "<p>1621 AD</p>"],
                    solution_en: "<p>59.(c) Around <strong>1632</strong> AD. Built by Mughal emperor Shah Jahan in memory of his wife Mumtaz Mahal and it was completed in 1648 AD. The Taj Mahal was designated as a UNESCO World Heritage Site in 1983, Architect - Ustad Ahmed Lahori.</p>",
                    solution_hi: "<p>59.(c) <strong>1632</strong> ई. के आसपास | मुगल बादशाह शाहजहां ने अपनी पत्नी मुमताज महल की याद में बनवाया था और यह 1648 ई. में बनकर तैयार हुआ था। ताजमहल को 1983 में यूनेस्को की विश्व विरासत स्थल के रूप में नामित किया गया था, वास्तुकार - उस्ताद अहमद लाहौरी।</p>",
                    correct: "c",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "60",
                    section: "18",
                    question_en: "<p>60. Cobalamin is the scientific name of Vitamin _____.</p>",
                    question_hi: "<p>60. कोबालामिन विटामिन ____ का वैज्ञानिक नाम है।</p>",
                    options_en: ["<p>B1</p>", "<p>C</p>", 
                                "<p>B12</p>", "<p>E</p>"],
                    options_hi: ["<p>B1</p>", "<p>C</p>",
                                "<p>B12</p>", "<p>E</p>"],
                    solution_en: "<p>60.(c) <strong>Vitamin B-12</strong> &rarr; Cobalamin. C &rarr; Ascorbic acid, B1 &rarr; <strong>Thiamine.,</strong> E &rarr; tocopherol. Water soluble vitamins (vitamins B, C, H).</p>",
                    solution_hi: "<p>60.(c) <strong>विटामिन B-12 </strong>&rarr; कोबालामिन | विटामिन C &rarr; एस्कार्बिक एसिड , विटामिन B1&rarr; <strong>थायमिन</strong> , विटामिन E&rarr; टेकोफेराॅल | पानी में घुलनशील विटामिन (विटामिन B, C, H) |</p>",
                    correct: "c",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "61",
                    section: "18",
                    question_en: "<p>61. Where was the Second Buddhist Council held in 383 BCE?</p>",
                    question_hi: "<p>61. 383 ईसा पूर्व में द्वितीय बौद्ध संगीति कहाँ आयोजित की गई थी?</p>",
                    options_en: ["<p>Kannauj</p>", "<p>Lumbini</p>", 
                                "<p>Vaishali</p>", "<p>Nalanda</p>"],
                    options_hi: ["<p>कन्नौज</p>", "<p>लुम्बिनी</p>",
                                "<p>वैशाली</p>", "<p>नालंदा</p>"],
                    solution_en: "<p>61.(c) <strong>Buddhist Council (1st) - </strong>Ajatshatru (King) - Rajgriha (Venue) - Mahakashyapa (Chairman)- 483 BC (Year).<strong> Buddhist Council (2nd)</strong> - Kalashoka (King) - Vaishali (Venue) - Sabakami (Chairman)- 383 BC (Year). <strong>Buddhist Council (3rd)</strong> - Ashoka (King) - Patliputra (Venue) - Moggaliputta (Chairman)- 250 BC (Year). <strong>Buddhist Council (4th)</strong> - Kanishka (King) - Kundalvan, Kashmir (Venue)- Vasumitra (Chairman)- 72 BC (Year).</p>",
                    solution_hi: "<p>61.(c) <strong>बौद्ध परिषद (प्रथम) - </strong>अजातशत्रु (राजा) - राजगृह (स्थल) - महाकश्यप (अध्यक्ष) - 483 ईसा पूर्व (वर्ष)। <strong>बौद्ध परिषद (द्वितीय) </strong>- कालाशोक (राजा) - वैशाली (स्थल) - सबकामी (अध्यक्ष) - 383 ईसा पूर्व (वर्ष)। <strong>बौद्ध परिषद (तृतीय) -</strong> अशोक (राजा) - पाटलिपुत्र (स्थल) - मोग्गलिपुत्त (अध्यक्ष) - 250 ईसा पूर्व (वर्ष)। <strong>बौद्ध परिषद (चौथा)</strong> - कनिष्क (राजा) - कुंडलवन, कश्मीर (स्थल) - वसुमित्र (अध्यक्ष) - 72 ईसा पूर्व (वर्ष)।</p>",
                    correct: "c",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "62",
                    section: "18",
                    question_en: "<p>62. Where did Mahatma Gandhi preside over the annual sessions of the Indian National Congress for the first and the last time?</p>",
                    question_hi: "<p>62. महात्मा गांधी ने पहली बार और आखिरी बार भारतीय राष्ट्रीय कांग्रेस के वार्षिक अधिवेशन की अध्यक्षता कहाँ की थी ?</p>",
                    options_en: ["<p>Surat</p>", "<p>Nagpur</p>", 
                                "<p>Tripura</p>", "<p>Belgaum</p>"],
                    options_hi: ["<p>सूरत</p>", "<p>नागपुर</p>",
                                "<p>त्रिपुरा</p>", "<p>बेलगाम</p>"],
                    solution_en: "<p>62.(d) <strong>Belgaum</strong> (karnataka), 39th Session(1924). <strong>1st Session</strong> (Bombay- Dec. 28-30, 1885; Womesh Chandra Bonnerjee). <strong>2nd Session</strong> (Calcutta; 1886; Shri Dadabhai Naoroji). <strong>3rd Session</strong> (Madras 1887; Badruddin Tyabji( First Muslim). <strong>4th Session</strong> (Allahabad ; 1888; George Yule).</p>",
                    solution_hi: "<p>62.(d) <strong>बेलगाम</strong> (कर्नाटक), 39वां सत्र (1924)। <strong>पहला सत्र </strong>(बॉम्बे - 28 - 30 दिसंबर, 1885; व्योमेश चंद्र बनर्जी)। <strong>दूसरा सत्र</strong> (कलकत्ता; 1886; श्री दादाभाई नौरोजी)।<strong> तीसरा सत्र</strong> (मद्रास 1887; बदरुद्दीन तैयबजी (प्रथम मुस्लिम)। <strong>चौथा सत्र</strong> (इलाहाबाद; 1888; जॉर्ज यूल) |</p>",
                    correct: "d",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "63",
                    section: "18",
                    question_en: "<p>63. Kargil Vijay Diwas is celebrated on _____ every year.</p>",
                    question_hi: "<p>63. कारगिल विजय दिवस हर साल _____ को मनाया जाता है।</p>",
                    options_en: ["<p>6th December</p>", "<p>26th July</p>", 
                                "<p>24th August</p>", "<p>7th May</p>"],
                    options_hi: ["<p>6 दिसंबर</p>", "<p>26 जुलाई</p>",
                                "<p>24 अगस्त</p>", "<p>7 मई</p>"],
                    solution_en: "<p>63.(b) <strong>Kargil Vijay Diwas</strong> is observed on <strong>26 July</strong> to commemorate the victory of the Indian soldiers over the infiltrating Pakistani troops. &rdquo;Operation Safed Sagar(operation of IAF)&rdquo; and Tiger hill is related to the Kargil War.</p>",
                    solution_hi: "<p>63.(b) <strong>कारगिल विजय दिवस 26 जुलाई</strong> को घुसपैठ करने वाले पाकिस्तानी सैनिकों पर भारतीय सैनिकों की जीत के उपलक्ष्य में मनाया जाता है। \"ऑपरेशन सफ़ेद सागर (IAF का ऑपरेशन)\" और टाइगर हिल कारगिल युद्ध से संबंधित है।</p>",
                    correct: "b",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "64",
                    section: "18",
                    question_en: "<p>64. The 74th Amendment Act of 1992 added a new Part____ to the Constitution of India.</p>",
                    question_hi: "<p>64. 1992 के 74वें संशोधन अधिनियम ने भारत के संविधान में एक नया भाग_____ जोड़ा।</p>",
                    options_en: ["<p>X</p>", "<p>IX-A</p>", 
                                "<p>IX</p>", "<p>VIII</p>"],
                    options_hi: ["<p>X</p>", "<p>IX-A</p>",
                                "<p>IX</p>", "<p>VIII</p>"],
                    solution_en: "<p>64.(b) The <strong>74th Amendment Act of 1992</strong> local Self Government under <strong>Part IX-A-</strong> The Municipalities ( Article 243P &ndash; 243ZG). <strong>Part X-</strong> The Scheduled and Tribal Areas (Articles:244&ndash;244A), <strong>Part IX-</strong> Panchayats (243-243O), <strong>Part VIII</strong> &ndash; The Union Territories (Articles 239 &ndash; 242).</p>",
                    solution_hi: "<p>64.(b) <strong>1992</strong> का <strong>74वाँ संशोधन अधिनियम भाग IX-A</strong> के तहत स्थानीय स्वशासन - नगर पालिकाएँ (अनुच्छेद 243P - 243ZG)। <strong>भाग X- </strong>अनुसूचित और जनजातीय क्षेत्र (अनुच्छेद 244 - 244A), <strong>भाग IX-</strong> पंचायत (243-243O), <strong>भाग VIII - </strong>केंद्र शासित प्रदेश (अनुच्छेद 239 - 242 )।</p>",
                    correct: "b",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "65",
                    section: "18",
                    question_en: "<p>65. Which Article of the Constitution of India states the directive principles of state policy on &lsquo;promotion of international peace and security&rsquo;?</p>",
                    question_hi: "<p>65. भारतीय संविधान का कौन सा अनुच्छेद &lsquo;अंतर्राष्ट्रीय शांति और सुरक्षा को प्रोत्साहन&rsquo; को लेकर राज्य नीति के निर्देशक सिद्धांतों को बताता है?</p>",
                    options_en: ["<p>Article 49</p>", "<p>Article 51</p>", 
                                "<p>Article 69</p>", "<p>Article 62</p>"],
                    options_hi: ["<p>अनुच्छेद 49</p>", "<p>अनुच्छेद 51</p>",
                                "<p>अनुच्छेद 69</p>", "<p>अनुच्छेद 62</p>"],
                    solution_en: "<p>65.(b) <strong>Article 51. Article 49:</strong> Protection of monuments and places and objects of national importance. <strong>Article 62: </strong>Time of holding the election to fill the vacancy in the office of President. <strong>Article 69:</strong> Oath or affirmation by the Vice-President.</p>",
                    solution_hi: "<p>65.(b) <strong>अनुच्छेद 51</strong> I <strong>अनुच्छेद 49:-&nbsp;</strong>राष्ट्रीय महत्व के स्मारकों और स्थानों और वस्तुओं का संरक्षण। <strong>अनुच्छेद 62:</strong>- राष्ट्रपति के पद की रिक्ति को भरने के लिए चुनाव कराने का समय। <strong>अनुच्छेद 69:-</strong> उपराष्ट्रपति द्वारा शपथ या प्रतिज्ञान।</p>",
                    correct: "b",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "66",
                    section: "18",
                    question_en: "<p>66. Alladi Krishnaswami Ayyar was the chairman of the _____ of the Constituent Assembly of India.</p>",
                    question_hi: "<p>66. अल्लादी कृष्णस्वामी अय्यार भारत की संविधान सभा के ____ के अध्यक्ष थे।</p>",
                    options_en: ["<p>Credential Committee</p>", "<p>Union Powers Committee</p>", 
                                "<p>Order of Business Committee</p>", "<p>Fundamental Rights Sub-Committee</p>"],
                    options_hi: ["<p>प्रत्यय पत्र समिति</p>", "<p>संघ शक्ति समिति</p>",
                                "<p>कार्य - सूचि समिति</p>", "<p>मौलिक अधिकार उप-समिति</p>"],
                    solution_en: "<p>66.(a) <strong>Alladi Krishnaswami Ayyar </strong>was the chairman of the Credential Committee of the Constituent Assembly of India. Union Powers Committee - <strong>Jawaharlal Nehru, </strong>Order of Business Committee- <strong>K.M. Munshi,</strong> Fundamental Rights Sub-Committee - <strong>J.B. Kripalani.</strong></p>",
                    solution_hi: "<p>66.(a) <strong>अल्लादी कृष्णस्वामी अय्यर </strong>भारत की संविधान सभा की साख समिति के अध्यक्ष थे। संघ शक्ति समिति - <strong>जवाहरलाल नेहरू, </strong>कार्य - सूचि समिति - <strong>के.एम. मुंशी,</strong> मौलिक अधिकार उप-समिति - <strong>जे.बी. कृपलानी</strong> थे।</p>",
                    correct: "a",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "67",
                    section: "18",
                    question_en: "<p>67. Which state has launched AAYU app to address chronic diseases through yoga and meditation?</p>",
                    question_hi: "<p>67. योग और ध्यान के माध्यम से पुरानी बीमारियों को दूर करने के लिए किस राज्य ने AAYU ऐप लॉन्च किया है?</p>",
                    options_en: ["<p>Odisha</p>", "<p>Kerala</p>", 
                                "<p>Karnataka</p>", "<p>Telangana</p>"],
                    options_hi: ["<p>ओडिशा</p>", "<p>केरल</p>",
                                "<p>कर्नाटक</p>", "<p>तेलंगाना</p>"],
                    solution_en: "<p>67.(c) <strong>Karnataka</strong> Chief Minister Basavaraj Bommai launched a new health and wellness app AAYU. The app has been developed by Swami Vivekananda Yoga Anusandhana Samsthana (S-VYASA) in partnership with RESET TECH, an AI-driven integrated health-tech platform.</p>",
                    solution_hi: "<p>67.(c) <strong>कर्नाटक.</strong> कर्नाटक के मुख्यमंत्री बसवराज बोम्मई ने स्वास्थ्य और कल्याण ऐप AAYU लॉन्च किया है। ऐप को स्वामी विवेकानंद योग अनुसंधान संस्थान (S-VYASA) द्वारा RESET TECH, AI- संचालित एकीकृत स्वास्थ्य-तकनीक प्लेटफॉर्म के साथ साझेदारी में विकसित किया गया है।</p>",
                    correct: "c",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "68",
                    section: "18",
                    question_en: "<p>68. Identify the disease caused by a monkey bite.</p>",
                    question_hi: "<p>68. बंदर के काटने से होने वाले रोग की पहचान करें।</p>",
                    options_en: ["<p>Malaria</p>", "<p>Typhoid</p>", 
                                "<p>Influenza</p>", "<p>Rabies</p>"],
                    options_hi: ["<p>मलेरिया</p>", "<p>टाइफाइड</p>",
                                "<p>इन्फ्लुएंजा</p>", "<p>रेबीज</p>"],
                    solution_en: "<p>68.(d) Serious wound infections include the <strong>herpes B virus</strong> and <strong>rabies.</strong> Malaria is a carrier-borne infectious disease transmitted by <strong>protozoan</strong> parasites. The carrier of the malaria parasite is the <strong>female Anopheles </strong>mosquito. Typhoid is caused by a bacterium called <strong>Salmonella typhi.</strong> Influenza is also known as the flu caused by RNA virus.</p>",
                    solution_hi: "<p>68.(d) गंभीर घाव संक्रमण, <strong>हर्पीस B वायरस</strong> और <strong>रेबीज</strong> शामिल हैं। <strong>मलेरिया</strong> एक वाहक-जनित संक्रामक रोग है जो प्रोटोज़ोआ परजीवी द्वारा फैलता है। मलेरिया के परजीवी का वाहक <strong>मादा एनोफ़िलेज़</strong> (Anopheles) मच्छर है। <strong>आंत्र ज्वर</strong> (टाइफायड) सलमोनेल्ला टायफी (Salmonella typhi) नामक जीवाणु (बैक्टीरिया) से होता है। इन्फ्लुएंजा को RNA वायरस के कारण होने वाले फ्लू के रूप में भी जाना जाता है।</p>",
                    correct: "d",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "69",
                    section: "18",
                    question_en: "<p>69. When is World Press Freedom Day observed?</p>",
                    question_hi: "<p>69. विश्व प्रेस स्वतंत्रता दिवस कब मनाया जाता है ?</p>",
                    options_en: ["<p>May 2</p>", "<p>May 4</p>", 
                                "<p>May 1</p>", "<p>May 3</p>"],
                    options_hi: ["<p>2 मई</p>", "<p>4 मई</p>",
                                "<p>1 मई</p>", "<p>3 मई</p>"],
                    solution_en: "<p>69.(d) <strong>May 3. </strong>Theme &rarr; \'Journalism under digital siege\'. On 2-5 May 2022, UNESCO and the Republic of Uruguay will host the annual World Press Freedom Day global conference in a hybrid format. According to the RSF 2022 World Press Freedom Index, India has slipped to 150th position from last year\'s 142nd position.</p>",
                    solution_hi: "<p>69.(d) <strong>3 मई </strong>थीम &rarr; \'Journalism under digital siege\' <br>2-5 मई 2022 को, यूनेस्को और उरुग्वे गणराज्य हाइब्रिड प्रारूप में वार्षिक विश्व प्रेस स्वतंत्रता दिवस वैश्विक सम्मेलन की मेजबानी करेंगे। RSF 2022 वर्ल्ड प्रेस फ्रीडम इंडेक्स के अनुसार, भारत पिछले साल के 142वें स्थान से 150वें स्थान पर आ गया है।</p>",
                    correct: "d",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "70",
                    section: "misc",
                    question_en: "<p>70. The Global Skill Park in Bhopal will be named after which mystic saint?</p>",
                    question_hi: "<p>70. भोपाल में ग्लोबल स्किल पार्क का नाम&nbsp;किस संत के नाम पर रखा जाएगा?</p>",
                    options_en: ["<p>Sant Surdas</p>", "<p>Kabir Das</p>", 
                                "<p>Sant Tulsidas</p>", "<p>Sant Ravidas</p>"],
                    options_hi: ["<p>संत सूरदास</p>", "<p>कबीर दास</p>",
                                "<p>संत तुलसीदास</p>", "<p>संत रविदास</p>"],
                    solution_en: "<p>70.(d) <strong>Saint Ravidas. </strong>&ldquo;<strong>Sant Ravidas Swarojgar Yojana </strong>\'\' &rarr; SC/ST youth will be provided loan assistance ranging from one lakh to 50 lakhs to set up a manufacturing unit. In addition to \'Damayanti\' and \'Byhalo\', \'Dashamskand Tika\', \'Naglila\', \'Bhagwat\', \'Goverdhan Leela\', \'Soorpachisi\', \'Sursagar Saar\', \'Pranpyari\' etc. are included. Kabir Das &rarr; Sakhi, Bijak. Saint Tulsidas &rarr; Shri Ramcharitmanas, Kavitavali, Jankimangal, Vinaya Patrika, Geetavali, Hanuman Chalisa, Barvai Ramayana.</p>",
                    solution_hi: "<p>70.(d) <strong>संत रविदास।</strong> <br><strong>\"संत रविदास स्वरोजगार योजना\" </strong>&rarr; अनुसूचित जाति/अनुसूचित जनजाति के युवाओं को निर्माण इकाई स्थापित करने के लिए एक लाख से 50 लाख तक की ऋण सहायता प्रदान की जाएगी। \'दमयंती\' और \'व्याहलो\' के अतिरिक्त \'दशमस्कंद टीका\', \'नगलीला\', \'भागवत\', \'गोवर्धन लीला\', \'सूरपच्ची\', \'सूरसागर सार\', \'प्राणप्यारी\' आदि शामिल हैं। कबीर दास &rarr; सखी, बीजक। संत तुलसीदास &rarr; श्री रामचरितमानस, कवितावली, जानकीमंगल, विनय पत्रिका, गीतावली, हनुमान चालीसा, बरवै रामायण।</p>",
                    correct: "d",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>