<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">8:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 15</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">15</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 8 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["18"] = {
                name: "General Awareness",
                start: 0,
                end: 13
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="18">General Awareness</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 14,
                end: 14
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "18",
                    question_en: "<p>1. Which of the following is the tallest statue in the world?</p>",
                    question_hi: "<p>1. निम्नलिखित में से कौन विश्व की सबसे ऊंची प्रतिमा है?</p>",
                    options_en: ["<p>&lsquo;Spring Temple Buddha&rsquo; in China</p>", "<p>&lsquo;Great Buddha of Thailand&rsquo; in Thailand</p>", 
                                "<p>&lsquo;Statue of Liberty&rsquo; in USA</p>", "<p>&lsquo;Statue of Unity&rsquo; in India</p>"],
                    options_hi: ["<p>\'स्प्रिंग टेम्पल बुद्धा\' चीन में</p>", "<p>\'ग्रेट बुद्धा ऑफ\' थाईलैंड थाईलैंड में</p>",
                                "<p>\'स्टेच्यू ऑफ लिबर्टी\' अमेरिका में</p>", "<p>\'स्टैच्यू ऑफ यूनिटी\' भारत में</p>"],
                    solution_en: "<p>1.(d) <strong>&lsquo;Statue of Unity&rsquo; in India. </strong>The 182-meter statue is dedicated to Sardar Vallabhbhai Patel (Iron man of India). It is located on Sadhu Bet island on the Narmada river in Kevadia (Gujarat). It was inaugurated on 31st October, 2018. Designed by - (Sculptor Ram V Sutar). Other Important statues in India: Statue of Belief (Shiva statue) Rajasthan. Dr. B.R Ambedkar Statue - Telangana. Dhyana Buddha Statue - Andhra Pradesh.</p>",
                    solution_hi: "<p>1.(d) <strong>भारत में \'स्टैच्यू ऑफ यूनिटी\' ।</strong> 182 मीटर की यह प्रतिमा सरदार वल्लभभाई पटेल (भारत के लौह पुरुष) को समर्पित है। यह केवडिया (गुजरात) में नर्मदा नदी पर साधु बेट द्वीप पर स्थित है। इसका उद्घाटन 31 अक्टूबर, 2018 को हुआ था। डिज़ाइन किया गया - (मूर्तिकार राम वी सुतार)। भारत में अन्य महत्वपूर्ण मूर्तियाँ: स्टैच्यू ऑफ बिलीफ (शिव प्रतिमा) राजस्थान। डॉ. बी.आर. अम्बेडकर प्रतिमा - तेलंगाना। ध्यान बुद्ध प्रतिमा - आंध्र प्रदेश।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "2",
                    section: "18",
                    question_en: "<p>2. Which of the following is NOT a correct pair of a country and its capital?</p>",
                    question_hi: "<p>2. निम्नलिखित में से कौन सा देश और उसकी राजधानी का सही युग्म नहीं है?</p>",
                    options_en: ["<p>Libya - Tripoli</p>", "<p>Bahamas - Nassau</p>", 
                                "<p>Kazakhstan - Bishkek</p>", "<p>Belgium - Brussels</p>"],
                    options_hi: ["<p>लीबिया - त्रिपोली</p>", "<p>बहामास - नासाउ</p>",
                                "<p>कज़ाखस्तान - बिश्केकी</p>", "<p>बेल्जियम - ब्रुसेल्स</p>"],
                    solution_en: "<p>2.(c) <strong>Kazakhstan - Bishkek.</strong> Kazakhstan: Capital - Nur Sultan; Currency - Kazakhstani Tenge; Location- central asia.<strong> Important countries and their capital - </strong>China - Beijing, Bangladesh - Dhaka, USA - Washington DC, France - Paris, Germany - Berlin, Pakistan - Islamabad.</p>",
                    solution_hi: "<p>2.(c) <strong>कजाकिस्तान - बिश्केक। </strong>कजाकिस्तान: राजधानी - नूर सुल्तान; मुद्रा - कजाकिस्तान तेंगे; स्थान- मध्य एशिया। <strong>महत्वपूर्ण देश और उनकी राजधानी - </strong>चीन - बीजिंग, बांग्लादेश - ढाका, अमेरिका - वाशिंगटन डीसी, फ्रांस - पेरिस, जर्मनी - बर्लिन, पाकिस्तान - इस्लामाबाद।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "3",
                    section: "18",
                    question_en: "<p>3. Gurdwara Panja Sahib is located in ________</p>",
                    question_hi: "<p>3. गुरुद्वारा पंजा साहिब स्थित है</p>",
                    options_en: ["<p>Sri Lanka</p>", "<p>Pakistan</p>", 
                                "<p>India</p>", "<p>Canada</p>"],
                    options_hi: ["<p>श्रीलंका</p>", "<p>पाकिस्तान</p>",
                                "<p>भारत</p>", "<p>कनाडा</p>"],
                    solution_en: "<p>3.(b) <strong>Pakistan </strong>(at Hasan Abdal). <strong>Gurdwara Panja Sahib</strong> is considered to be the most valuable as the handprint of Guru Nanak (The founder of Sikhism) is believed to be imprinted on a rock at the Gurdwara. <strong>The Kartarpur Gurdwara</strong> (Kartarpur Sahib) is located in Narowal district of Punjab province of Pakistan near the Indian border and is revered by Sikhs due to the belief that Guru Nanak died there.</p>",
                    solution_hi: "<p>3.(b) <strong>पाकिस्तान</strong> (हसन अब्दाल में)। <strong>गुरुद्वारा पंजा साहिब</strong> को विशेष रूप से महत्वपूर्ण माना जाता है क्योंकि माना जाता है कि गुरु नानक (सिख धर्म के संस्थापक) के हाथ के निशान गुरुद्वारे में एक चट्टान पर अंकित है।<strong> करतारपुर गुरुद्वारा</strong> (करतारपुर साहिब) भारतीय सीमा के पास पाकिस्तान के पंजाब प्रांत के नारोवाल जिले में स्थित है तथा सिखों द्वारा इस मान्यता के कारण यह पूजनीय है कि गुरु नानक की मृत्यु वहीं हुई थी।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "4",
                    section: "18",
                    question_en: "<p>4. Which country is known as &lsquo;Hermit Kingdom&rsquo;?</p>",
                    question_hi: "<p>4. किस देश को \'हर्मिट किंगडम\' के नाम से जाना जाता है?</p>",
                    options_en: ["<p>North Korea</p>", "<p>Japan</p>", 
                                "<p>Thailand</p>", "<p>Australia</p>"],
                    options_hi: ["<p>उत्तर कोरिया</p>", "<p>जापान</p>",
                                "<p>थाईलैंड</p>", "<p>ऑस्ट्रेलिया</p>"],
                    solution_en: "<p>4.(a) <strong>North Korea. </strong>It is the most isolated country in the world. Capital of North Korea is Pyongyang and currency is North Korean Won. Japan: Land of rising sun, Capital - Tokyo, Currency - Yen. Thailand: Capital - Bangkok, Currency - Baht. Australia: Capital - Canberra, Currency - Dollar.</p>",
                    solution_hi: "<p>4.(a) <strong>उत्तर कोरिया</strong>। यह दुनिया का सबसे पृथक देश है। उत्तर कोरिया की राजधानी प्योंगयांग है और मुद्रा उत्तर कोरियाई वॉन है। जापान: उगते सूरज की भूमि, राजधानी - टोक्यो, मुद्रा - येन। थाईलैंड: राजधानी - बैंकॉक, मुद्रा - बहत। ऑस्ट्रेलिया: राजधानी - कैनबरा, मुद्रा - डॉलर।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "5",
                    section: "18",
                    question_en: "<p>5. Where will you find the &lsquo;Forbidden City&rsquo;?</p>",
                    question_hi: "<p>5. \'फॉरबिडन सिटी (Forbidden City)\' कहां स्थित है?</p>",
                    options_en: ["<p>Myanmar</p>", "<p>China</p>", 
                                "<p>Vietnam</p>", "<p>Greece</p>"],
                    options_hi: ["<p>म्यांमार</p>", "<p>चीन</p>",
                                "<p>वियतनाम</p>", "<p>ग्रीस</p>"],
                    solution_en: "<p>5.(b) <strong>China. The Forbidden City :</strong> It is situated in Beijing (China). It served as the imperial palace during the Ming and Qing dynasties. It is a UNESCO World Heritage site (1987). Some famous Cities with their nicknames: The City of Light - Paris, France. The Eternal City - Rome (Italy). The Lion City - Singapore. Paris of the Middle East - Beirut (Lebanon).</p>",
                    solution_hi: "<p>5.(b) <strong>चीन। फॉरबिडन सिटी:</strong> यह बीजिंग (चीन) में स्थित है। यह मिंग और किंग राजवंशों के समय शाही महल के रूप में कार्य करता था। यह यूनेस्को विश्व धरोहर (1987) स्थल है । कुछ प्रसिद्ध शहर और उनके उपनाम: रोशनी का शहर - पेरिस, फ़्रांस। शाश्वत शहर - रोम (इटली)। द लायन सिटी - सिंगापुर। मध्य पूर्व का पेरिस - बेरूत (लेबनान)</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "6",
                    section: "18",
                    question_en: "<p>6. Eleanor Roosevelt, who was the chairperson of UDHR, belonged to which country?</p>",
                    question_hi: "<p>6. एलेनोर रूजवेल्ट, जो UDHR के अध्यक्ष थे, किस देश के थे?</p>",
                    options_en: ["<p>Australia</p>", "<p>New Zealand</p>", 
                                "<p>UK</p>", "<p>USA</p>"],
                    options_hi: ["<p>ऑस्ट्रेलिया</p>", "<p>न्यूजीलैंड</p>",
                                "<p>UK</p>", "<p>USA</p>"],
                    solution_en: "<p>6.(d) <strong>USA</strong>. Mrs. Eleanor Roosevelt was appointed as chairperson of the Universal Declaration of Human Rights (UDHR) in 1946.<strong> List of Organizations and their Headquarters - </strong>UN Women (New York, USA, in 2010), International Monetary Fund and World Bank (Washington, DC, USA, 1944), International Peace Bureau (Geneva, Switzerland, 1891), World Wide Fund for Nature (Gland, Switzerland, 1961).</p>",
                    solution_hi: "<p>6.(d)<strong> USA ।</strong>श्रीमती एलेनोर रूजवेल्ट को 1946 में सर्वराष्ट्रीय मानव अधिकार घोषणापत्र (UDHR) के अध्यक्ष के रूप में नियुक्त किया गया था।<strong> संगठनों और उनके मुख्यालयों की सूची -</strong> संयुक्त राष्ट्र महिला (न्यूयॉर्क, USA, 2010 में), अंतर्राष्ट्रीय मुद्रा कोष और विश्व बैंक (वाशिंगटन, डीसी) , USA, 1944), अंतर्राष्ट्रीय शांति ब्यूरो (जिनेवा, स्विट्जरलैंड, 1891), वर्ल्ड वाइड फंड फॉर नेचर (ग्लैंड, स्विट्जरलैंड, 1961)।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "7",
                    section: "18",
                    question_en: "<p>7. When was the currency Euro (common European currency) adopted in Europe?</p>",
                    question_hi: "<p>7. यूरोप में मुद्रा यूरो (सामान्य यूरोपीय मुद्रा) को कब अपनाया गया था?</p>",
                    options_en: ["<p>Jan, 2002</p>", "<p>Jan, 2003</p>", 
                                "<p>Jan , 2005</p>", "<p>Jan 1999</p>"],
                    options_hi: ["<p>जनवरी, 2002</p>", "<p>जनवरी, 2003</p>",
                                "<p>जनवरी, 2005</p>", "<p>जनवरी 1999</p>"],
                    solution_en: "<p>7.(d) <strong>Jan 1999. </strong>Greece became the 12th member state to adopt the euro on January 1, 2001. On January 1, 2002, these 12 countries officially introduced the euro banknotes and coins as legal tender. <strong>Croatia </strong>became the 20<sup>th</sup> member state to adopt the Euro on January 1, 2023.</p>",
                    solution_hi: "<p>7.(d)<strong> जनवरी 1999। </strong>1 जनवरी 2001 को ग्रीस यूरो अपनाने वाला 12वां सदस्य देश बन गया। 1 जनवरी 2002 को, इन 12 देशों ने आधिकारिक तौर पर यूरो बैंकनोट और सिक्कों को कानूनी निविदा के रूप में पेश किया। <strong>क्रोएशिया </strong>1 जनवरी, 2023 को यूरो अपनाने वाला 20वां सदस्य देश बन गया।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "8",
                    section: "18",
                    question_en: "<p>8. Which of the following Nations was the member of &ldquo;Allied Powers&rdquo; group in World War II?</p>",
                    question_hi: "<p>8. निम्नलिखित में से कौन सा राष्ट्र द्वितीय विश्व युद्ध में \"मित्र राष्ट्र\" समूह का सदस्य था?</p>",
                    options_en: ["<p>USA</p>", "<p>Italy</p>", 
                                "<p>Germany</p>", "<p>Japan</p>"],
                    options_hi: ["<p>अमेरिका</p>", "<p>इटली</p>",
                                "<p>जर्मनी</p>", "<p>जापान</p>"],
                    solution_en: "<p>8.(a) <strong>USA. World War II </strong>(1939 - 1945): Allied Forces - The USA, Soviet Union, Britain and France defeated the Axis Powers (Germany, Italy and Japan). <strong>World War I</strong> (1914 -1918): Fought between the Allied Powers (France, Russia, and Britain) and the Central Powers (Germany, Austria-Hungary, the Ottoman Empire, and Bulgaria). Allied Powers defeated Central Powers.</p>",
                    solution_hi: "<p>8.(a) <strong>अमेरीका। द्वितीय विश्व युद्ध</strong> (1939 - 1945): मित्र देशों की सेनाओं - संयुक्त राज्य अमेरिका, सोवियत संघ, ब्रिटेन और फ्रांस ने धुरी शक्तियों (जर्मनी, इटली और जापान) को हराया । <strong>प्रथम विश्व युद्ध</strong> (1914 -1918): मित्र शक्तियों (फ्रांस, रूस और ब्रिटेन) और केंद्रीय शक्तियों (जर्मनी, ऑस्ट्रिया-हंगरी, ओटोमन साम्राज्य और बुल्गारिया) के बीच लड़ा गया। मित्र शक्तियों ने केन्द्रीय शक्तियों को पराजित किया।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "9",
                    section: "18",
                    question_en: "<p>9. &lsquo;Pataca&rsquo; is the currency of which country?</p>",
                    question_hi: "<p>9. \'पटाका (Pataca)&rsquo; किस देश की मुद्रा है ?</p>",
                    options_en: ["<p>Laos</p>", "<p>Mexico</p>", 
                                "<p>Macau</p>", "<p>Panama</p>"],
                    options_hi: ["<p>लाओस</p>", "<p>मेक्सिको</p>",
                                "<p>मकाउ</p>", "<p>पनामा</p>"],
                    solution_en: "<p>9.(c) <strong>Macau</strong>. Some other countries and their currencies: Laos - Laotian Kip, Mexico - Peso, Panama - Balboa, Albania - Lek, Denmark - Krone, Philippine - peso, Algeria - Dinar, Armenia - Dram, Azerbaijan - Manat, Bangladesh - Taka.</p>",
                    solution_hi: "<p>9.(c) <strong>मकाउ</strong>। कुछ अन्य देश और उनकी मुद्राएँ: लाओस - लाओटियन किप, मेक्सिको - पेसो, पनामा - बाल्बोआ, अल्बानिया - लेक, डेनमार्क - क्रोन, फिलीपीन - पेसो, अल्जीरिया - दीनार, आर्मेनिया - ड्राम, अजरबैजान - मनात, बांग्लादेश - टका।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "10",
                    section: "18",
                    question_en: "<p>10. In which country are the Inca ruins of Machu Picchu located?</p>",
                    question_hi: "<p>10. माचू पिच्चू के इंका खंडहर किस देश में स्थित हैं?</p>",
                    options_en: ["<p>Peru</p>", "<p>Guatemala</p>", 
                                "<p>Mexico</p>", "<p>Greece</p>"],
                    options_hi: ["<p>पेरू</p>", "<p>ग्वाटेमाला</p>",
                                "<p>मेक्सिको</p>", "<p>ग्रीस</p>"],
                    solution_en: "<p>10.(a) <strong>Peru. Machu Picchu - </strong>It is referred to as the \"Lost City of the Incas\" Because the city was never discovered by the Spaniards after they conquered the Inca in the 1500s. It was rediscovered in 1911 by the American explorer Hiram Bingham. It was designated as a UNESCO World Heritage site in 1983. <strong>Guatemala - </strong>The most populous city in Central America. <strong>Mexico - Capital:</strong> Mexico City, <strong>Currency</strong>: Mexican Peso. <strong>Greece </strong>(Cradle of Western Civilization) - <strong>Capital</strong>: Athens, <strong>Currency -</strong> Euro.</p>",
                    solution_hi: "<p>10.(a) <strong>पेरू। माचू पिच्चू -</strong> इसे \"इन्कास का खोया हुआ शहर\" कहा जाता है क्योंकि 1500 के दशक में इंका पर विजय प्राप्त करने के बाद स्पेनियों ने इस शहर की खोज कभी नहीं की थी। इसे 1911 में अमेरिकी खोजकर्ता हीराम बिंघम द्वारा पुनः खोजा गया था। इसे 1983 में यूनेस्को विश्व धरोहर स्थल के रूप में शामिल किया गया । <strong>ग्वाटेमाला </strong>- मध्य अमेरिका का सबसे अधिक आबादी वाला शहर। <strong>मेक्सिको </strong>- <strong>राजधानी</strong>: मेक्सिको सिटी, <strong>मुद्रा</strong>: मैक्सिकन पेसो। ग्रीस (पश्चिमी सभ्यता का उद्गम स्थल) - <strong>राजधानी</strong>: एथेंस, <strong>मुद्रा </strong>- यूरो।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "11",
                    section: "18",
                    question_en: "<p>11. Kurt Waldheim, the fourth UN secretary General was the former president of _______.</p>",
                    question_hi: "<p>11. संयुक्त राष्ट्र (UN) के चौथे महासचिव, कर्ट वल्डहैम (Kurt Waldheim) किस देश के भूतपूर्व राष्ट्रपति थे?</p>",
                    options_en: ["<p>Argentina</p>", "<p>Mexico</p>", 
                                "<p>Austria</p>", "<p>Spain</p>"],
                    options_hi: ["<p>अर्जेंटीना</p>", "<p>मेक्सिको</p>",
                                "<p>ऑस्ट्रिया</p>", "<p>स्पेन</p>"],
                    solution_en: "<p>11.(c) <strong>Austria</strong>. Kurt Josef Waldheim was an Austrian politician and diplomat. He was the Secretary-General of the United Nations from 1972 to 1981 and President of Austria from 1986 to 1992. United Nations Organization (UNO): Formed 24 October 1945 (San Francisco, USA), Headquarters - New York.</p>",
                    solution_hi: "<p>11.(c) <strong>ऑस्ट्रिया</strong>। कर्ट जोसेफ वाल्डहेम एक ऑस्ट्रियाई राजनीतिज्ञ और कूटनीतिज्ञ थे। वह 1972 से 1981 तक संयुक्त राष्ट्र के महासचिव और 1986 से 1992 तक ऑस्ट्रिया के राष्ट्रपति रहे। संयुक्त राष्ट्र संगठन (UNO): स्थापना: 24 अक्टूबर 1945 (सैन फ्रांसिस्को, USA), मुख्यालय - न्यूयॉर्क।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "12",
                    section: "18",
                    question_en: "<p>12. Headquarters of United Nations University is situated in _______.</p>",
                    question_hi: "<p>12. संयुक्त राष्ट्र विश्वविद्यालय का मुख्यालय _______ में स्थित है।</p>",
                    options_en: ["<p>USA</p>", "<p>France</p>", 
                                "<p>Germany</p>", "<p>Japan</p>"],
                    options_hi: ["<p>USA</p>", "<p>फ्रांस</p>",
                                "<p>जर्मनी</p>", "<p>जापान</p>"],
                    solution_en: "<p>12.(d) <strong>Japan. United Nations University - </strong>Mission - To contribute, through collaborative research and education, to efforts to resolve the pressing global problems of human survival, development, and welfare that are the concern of the United Nations, its Peoples, and Member States. In 1969, UN Secretary-General U Thant proposed a report to the United Nations (UN) to create UNU. Established in 1972.</p>",
                    solution_hi: "<p>12.(d) <strong>जापान। संयुक्त राष्ट्र विश्वविद्यालय - </strong>मिशन - सहयोगात्मक अनुसंधान और शिक्षा के माध्यम से, मानव अस्तित्व, विकास और कल्याण की गंभीर वैश्विक समस्याओं को हल करने के प्रयासों में योगदान देना, जो संयुक्त राष्ट्र, इसके लोगों और सदस्य राज्यों की चिंता का विषय हैं। 1969 में, संयुक्त राष्ट्र महासचिव यू थांट ने UNU बनाने के लिए संयुक्त राष्ट्र (UN) को एक रिपोर्ट का प्रस्ताव दिया।स्थापना- 1972 ।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "13",
                    section: "18",
                    question_en: "<p>13. The Pittsburgh industrial region is located in :</p>",
                    question_hi: "<p>13. पिट्सबर्ग औद्योगिक क्षेत्र अवस्थित है :</p>",
                    options_en: ["<p>Japan</p>", "<p>Germany</p>", 
                                "<p>England</p>", "<p>the United States of America</p>"],
                    options_hi: ["<p>जापान</p>", "<p>जर्मनी</p>",
                                "<p>इंग्लैंड</p>", "<p>संयुक्त राज्य अमेरिका</p>"],
                    solution_en: "<p>13.(d)<strong> the United States of America. </strong>The Pittsburgh industrial region is famous for the &ldquo;Steel industry &rdquo;.<strong>Jamshedpur </strong>is known as the Pittsburgh of India due to the largest iron and steel industry. The first and one of the largest manufacturing plants in Asia is Tata Iron and Steel Company (TISCO), It was founded by Jamsetji Tata in 1907 at Sakchi.</p>",
                    solution_hi: "<p>13.(d) <strong>संयुक्त राज्य अमेरिका।</strong> पिट्सबर्ग औद्योगिक क्षेत्र \"इस्पात उद्योग\" के लिए प्रसिद्ध है। सबसे बड़े लौह और इस्पात उद्योग के कारण <strong>जमशेदपुर </strong>को भारत के पिट्सबर्ग के रूप में जाना जाता है। एशिया में पहला और सबसे बड़े विनिर्माण संयंत्रों में से एक टाटा आयरन एंड स्टील कंपनी (TISCO) है, इसकी स्थापना जमशेदजी टाटा ने 1907 में साकची में की थी।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "14",
                    section: "18",
                    question_en: "<p>14. Who is known as the \'Father of Bangladesh\' from the following options ?</p>",
                    question_hi: "<p>14. निम्नलिखित में से किसे \'बांग्लादेश का जनक\' कहा जाता है ?</p>",
                    options_en: ["<p>Sheikh Mujibur Rahman</p>", "<p>Ataur Rahman Khan</p>", 
                                "<p>Muhammad Habibur Rahman</p>", "<p>Sheikh Hasina</p>"],
                    options_hi: ["<p>शेख मुजीबुर्रहमान</p>", "<p>अताउर रहमान खान</p>",
                                "<p>मोहम्मद हबीबुर्रहमान</p>", "<p>शेख हसीना</p>"],
                    solution_en: "<p>14.(a) <strong>Sheikh Mujibur Rahman</strong> (First President of Bangladesh). Bangladesh: First Prime Minister - Tajuddin Ahmad. First female prime minister - Khaleda Zia. Personalities and their nicknames - Grand Old man of India (Dadabhai Naoroji), Indian Einstein (Nagarjuna), Man of Peace (Lal Bahadur Shastri), Morning Star of India Renaissance (Raja Ram Mohan Roy), Machiavelli of India (Chanakya), Bengal Kesari (Ashutosh Mukherji).</p>",
                    solution_hi: "<p>14.(a)<strong> शेख मुजीबुर्रहमान</strong> (बांग्लादेश के प्रथम राष्ट्रपति)। बांग्लादेश: प्रथम प्रधान मंत्री - ताजुद्दीन अहमद। प्रथम महिला प्रधान मंत्री - खालिदा जिया। व्यक्ति और उनके उपनाम - भारत के ग्रैंड ओल्ड मैन (दादाभाई नौरोजी), भारतीय का आइंस्टीन (नागार्जुन), शांति पुरुष (लाल बहादुर शास्त्री), भारत पुनर्जागरण के प्रभात तारा (राजा राम मोहन रॉय), भारत का मैकियावेली (चाणक्य), बंगाल केसरी (आशुतोष मुखर्जी)।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "15",
                    section: "misc",
                    question_en: "<p>15. Soparrkar and the logo of his worldwide initiative \'Dance for a Cause\' are being printed on an official postage stamp issued by the __________ government.</p>",
                    question_hi: "<p>15. सोपारकर और उनकी विश्वव्यापी पहल \'डांस फॉर ए कॉज\' का लोगो ___________ सरकार द्वारा जारी एक आधिकारिक डाक टिकट पर मुद्रित किया जा रहा है।</p>",
                    options_en: ["<p>Nepal</p>", "<p>Bangladesh</p>", 
                                "<p>Sri Lanka</p>", "<p>Bhutan</p>"],
                    options_hi: ["<p>नेपाल</p>", "<p>बांग्लादेश</p>",
                                "<p>श्रीलंका</p>", "<p>भूटान</p>"],
                    solution_en: "<p>15.(d) <strong>Bhutan</strong>. In 2008 Sandip Soparrkar had launched his Initiative Dance for a Cause at the World Economic forum in Davos, Switzerland. The First Stamp of Independent India was issued on 21 November 1947. It depicts the Indian Flag with the patriots\' slogan, Jai Hind. The first postal stamp in India was introduced on 1 July 1852 under the Governor General Lord Dalhousie.</p>",
                    solution_hi: "<p>15.(d) <strong>भूटान </strong>। 2008 में संदीप सोपारकर ने स्विट्जरलैंड के दावोस में वर्ल्ड इकोनॉमिक फोरम में अपना इनिशिएटिव डांस फॉर ए कॉज लॉन्च किया था। स्वतंत्र भारत का प्रथम डाक टिकट 21 नवंबर 1947 को जारी किया गया था, इसमें देशभक्तों के नारे जय हिंद के साथ भारतीय ध्वज को दर्शाया गया है। भारत में पहला डाक टिकट 1 जुलाई 1852 को गवर्नर जनरल लॉर्ड डलहौजी के अधीन जारी किया गया था।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>