<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">15:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 15</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">15</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 15 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 0,
                end: 13
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 14,
                end: 14
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "17",
                    question_en: "<p>1. <span style=\"font-family: Cambria Math;\">What is the maturity amount &#8377;40,000 is invested for 2 years, the rate of compound interest (compounded annually) being 8% for the first year and 10% for the second year?</span></p>\n",
                    question_hi: "<p>1. <span style=\"font-family: Cambria Math;\">&#2325;&#2369;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2367;&#2358;&#2381;&#2352;&#2343;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2332;&#2348;</span><span style=\"font-family: Cambria Math;\"> &#8377;40,000 </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2344;&#2367;&#2357;&#2375;&#2358;</span><span style=\"font-family: Cambria Math;\"> 2 </span><span style=\"font-family: Cambria Math;\">&#2360;&#2366;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2366;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2330;&#2325;&#2381;&#2352;&#2357;&#2371;&#2342;&#2381;&#2343;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2381;&#2351;&#2366;&#2332;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2352;</span><span style=\"font-family: Cambria Math;\"> (</span><span style=\"font-family: Cambria Math;\">&#2357;&#2366;&#2352;&#2381;&#2359;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2352;&#2370;&#2346;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2306;&#2351;&#2379;&#2332;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\">) </span><span style=\"font-family: Cambria Math;\">&#2346;&#2361;&#2354;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2352;&#2381;&#2359;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> 8% </span><span style=\"font-family: Cambria Math;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2370;&#2360;&#2352;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2352;&#2381;&#2359;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> 10% </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>49,550</p>\n", "<p>48,400</p>\n", 
                                "<p>45,270</p>\n", "<p>47,520</p>\n"],
                    options_hi: ["<p>49,550</p>\n", "<p>48,400</p>\n",
                                "<p>45,270</p>\n", "<p>47,520</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">1.(</span><span style=\"font-family: Cambria Math;\">d)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">8% = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>25</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> and 1</span><span style=\"font-family: Cambria Math;\">0% =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>10</mn></mfrac></math></span></p>\r\n<p><span style=\"font-weight: 400;\">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;Principal &nbsp; : &nbsp; Amount</span></p>\r\n<p><span style=\"font-weight: 400;\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span style=\"font-weight: 400;\"> </span><span style=\"font-weight: 400;\"> &nbsp; &nbsp; 25&nbsp; &nbsp; &nbsp; &nbsp; : &nbsp; &nbsp; &nbsp; 27</span></p>\r\n<p><span style=\"font-weight: 400;\"> </span><span style=\"font-weight: 400;\">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;10&nbsp; &nbsp; &nbsp; &nbsp; : &nbsp; &nbsp; &nbsp; 11</span></p>\r\n<p><span style=\"font-weight: 400;\">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; _________________</span></p>\r\n<p><strong>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </strong><span style=\"font-weight: 400;\">250&nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; &nbsp; 297</span></p>\r\n<p><span style=\"font-weight: 400;\">Clearly, Principal = 250 unit = 40,000</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">1 unit = 160</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">So maturity amount = 297 unit = </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">297 &times; 160 = 4</span><span style=\"font-family: Cambria Math;\">7,520</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">1.(</span><span style=\"font-family: Cambria Math;\">d)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">8% = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>25</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> <span style=\"font-weight: 400;\">&#2324;&#2352; </span>1</span><span style=\"font-family: Cambria Math;\">0% =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>10</mn></mfrac></math></span></p>\r\n<p><span style=\"font-weight: 400;\">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&#2350;&#2370;&#2354;&#2343;&#2344;&nbsp; &nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp;&#2352;&#2366;&#2358;&#2367; </span></p>\r\n<p><span style=\"font-weight: 400;\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span style=\"font-weight: 400;\"> </span><span style=\"font-weight: 400;\"> &nbsp; &nbsp; 25&nbsp; &nbsp; &nbsp; &nbsp; : &nbsp; &nbsp; &nbsp; 27</span></p>\r\n<p><span style=\"font-weight: 400;\"> </span><span style=\"font-weight: 400;\">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;10&nbsp; &nbsp; &nbsp; &nbsp; : &nbsp; &nbsp; &nbsp; 11</span></p>\r\n<p><span style=\"font-weight: 400;\">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; _________________</span></p>\r\n<p><strong>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </strong><span style=\"font-weight: 400;\">250&nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; &nbsp; 297</span></p>\r\n<p><span style=\"font-weight: 400;\">&#2360;&#2381;&#2346;&#2359;&#2381;&#2335; &#2352;&#2370;&#2346; &#2360;&#2375;, &#2350;&#2370;&#2354;&#2343;&#2344;&nbsp; = 250 &#2311;&#2325;&#2366;&#2312; = 40,000</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">1 <span style=\"font-weight: 400;\">&#2311;&#2325;&#2366;&#2312; </span>= 160</span></p>\r\n<p><span style=\"font-weight: 400;\">&#2340;&#2379; &#2350;&#2367;&#2358;&#2381;&#2352;&#2343;&#2344; = 297 &#2311;&#2325;&#2366;&#2312; = 297 &times; &#2311;&#2325;&#2366;&#2312;&nbsp; =</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">297 &times; 160 = 4</span><span style=\"font-family: Cambria Math;\">7,520</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "17",
                    question_en: "<p>2<span style=\"font-family: Cambria Math;\">. The simple interest on a sum of money at 15 % per annum for 2 years is &#8377; 2700. Compounded annually, what would be the compound interest (in </span><span style=\"font-family: Cambria Math;\">&#8377; )</span><span style=\"font-family: Cambria Math;\"> for the same period and at the same rate of int</span><span style=\"font-family: Cambria Math;\">erest ?</span></p>\n",
                    question_hi: "<p>2<span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\">&#2325;&#2367;&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2343;&#2344;&#2352;&#2366;&#2358;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> 15% </span><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2357;&#2352;&#2381;&#2359;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> 2 </span><span style=\"font-family: Cambria Math;\">&#2357;&#2352;&#2381;&#2359;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2366;&#2343;&#2366;&#2352;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2381;&#2351;&#2366;&#2332;</span><span style=\"font-family: Cambria Math;\"> &#8377; 2700 </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2366;&#2352;&#2381;&#2359;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2352;&#2370;&#2346;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2306;&#2351;&#2379;&#2332;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2360;&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2309;&#2357;&#2343;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2381;&#2351;&#2366;&#2332;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2330;&#2325;&#2381;&#2352;&#2357;&#2371;&#2342;&#2381;&#2343;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2381;&#2351;&#2366;&#2332;</span><span style=\"font-family: Cambria Math;\"> (&#8377; </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\">) </span><span style=\"font-family: Cambria Math;\">&#2325;&#2367;&#2340;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2379;&#2327;&#2366;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>2950.50</p>\n", "<p>2290.50</p>\n", 
                                "<p>2209.50</p>\n", "<p>2902.50</p>\n"],
                    options_hi: ["<p>2950.50</p>\n", "<p>229<span style=\"font-family: Cambria Math;\">0.50</span></p>\n",
                                "<p>2209.50</p>\n", "<p>2902.50</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">2.(</span><span style=\"font-family: Cambria Math;\">d) </span><span style=\"font-family: Cambria Math;\">Effective </span><span style=\"font-family: Cambria Math;\">interest earned</span><span style=\"font-family: Cambria Math;\"> in 2 years at 15% rate = 2 &times; 15 = 30%</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">ATQ, 30% &mdash; 2700</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">100% &mdash; 9000</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Now, effective CI = 15 + 15 + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>15</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>15</mn></mrow><mn>100</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> = 32.25%</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Required CI = 32.25% of 9000 </span><span style=\"font-family: Cambria Math;\">= &#8377;</span><span style=\"font-family: Cambria Math;\"> 2902.50</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">2.(</span><span style=\"font-family: Cambria Math;\">d) </span><span style=\"font-family: Cambria Math;\">2 </span><span style=\"font-family: Cambria Math;\">&#2357;&#2352;&#2381;&#2359;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> 15% </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2309;&#2352;&#2381;&#2332;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2349;&#2366;&#2357;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2381;&#2351;&#2366;&#2332;</span><span style=\"font-family: Cambria Math;\"> = 2 &times; 15 = 30%</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2358;&#2381;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2309;&#2344;&#2369;&#2360;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\">, </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&rArr; 30% = 2700</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&rArr; 100% = 9000</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&#2309;&#2348;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2349;&#2366;&#2357;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2330;&#2325;&#2381;&#2352;&#2357;&#2371;&#2342;&#2381;&#2343;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2381;&#2351;&#2366;&#2332;</span><span style=\"font-family: Cambria Math;\">= </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">15 + 15 + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>15</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>15</mn></mrow><mn>100</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> = 32.25 %</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&#2310;&#2357;&#2358;&#2381;&#2351;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2330;&#2325;&#2381;&#2352;&#2357;&#2371;&#2342;&#2381;&#2343;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2381;&#2351;&#2366;&#2332;</span><span style=\"font-family: Cambria Math;\"> = 32.25% of 9000 = &#8377;2902.50</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "17",
                    question_en: "<p>3. <span style=\"font-family: Cambria Math;\">What will be the amount (i</span><span style=\"font-family: Cambria Math;\">n &#8377;) of a sum of &#8377;6000 in <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>2</mn><mfrac><mn>1</mn><mn>4</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> years at 13<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\">% </span><span style=\"font-family: Cambria Math;\">p.a.</span><span style=\"font-family: Cambria Math;\"> If the interest is compounded 9 </span><span style=\"font-family: Cambria Math;\">monthly ?</span></p>\n",
                    question_hi: "<p>3. <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>2</mn><mfrac><mn>1</mn><mn>4</mn></mfrac></math><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2352;&#2381;&#2359;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>13</mn><mfrac><mn>1</mn><mn>3</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\">% </span><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2352;&#2381;&#2359;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> &#8377; 6000 </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2352;&#2366;&#2358;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2352;&#2366;&#2358;&#2367;</span><span style=\"font-family: Cambria Math;\"> (&#8377; </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\">) </span><span style=\"font-family: Cambria Math;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2379;&#2327;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2381;&#2351;&#2366;&#2332;</span><span style=\"font-family: Cambria Math;\"> 9 </span><span style=\"font-family: Cambria Math;\">&#2350;&#2366;&#2360;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2352;&#2370;&#2346;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2306;&#2351;&#2379;&#2332;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2366;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>7986</p>\n", "<p>8429</p>\n", 
                                "<p>7869</p>\n", "<p>7896</p>\n"],
                    options_hi: ["<p>7986<span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p>8429<span style=\"font-family: Cambria Math;\"> </span></p>\n",
                                "<p>7869</p>\n", "<p>7896</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">3.(</span><span style=\"font-family: Cambria Math;\">a)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>13</mn><mfrac><mn>1</mn><mn>3</mn></mfrac><mo>%</mo><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><mn>40</mn><mn>3</mn></mfrac><mo>%</mo></math></p>\r\n<p><span style=\"font-family: Cambria Math;\">Effective rate of interest = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>40</mn><mn>3</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\">&times;</span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>9</mn><mn>12</mn></mfrac></math>= 10%</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">2</span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>4</mn></mfrac></math>years = 27 months</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Number of compounding cycles =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>27</mn><mn>9</mn></mfrac></math> = 3</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; Principal&nbsp; &nbsp; :&nbsp; &nbsp; Amount</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 10&nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp;11</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 10&nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp;11</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 10&nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; 11</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;_______________</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 1000 : 1331</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Required amount = 6000 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1331</mn><mn>1000</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">= &#8377;</span><span style=\"font-family: Cambria Math;\"> 7986</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">3.(</span><span style=\"font-family: Cambria Math;\">a)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>13</mn><mfrac><mn>1</mn><mn>3</mn></mfrac><mo>%</mo><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><mn>40</mn><mn>3</mn></mfrac><mo>%</mo></math></p>\r\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">&#2346;&#2381;&#2352;&#2349;&#2366;&#2357;&#2368; &#2348;&#2381;&#2351;&#2366;&#2332; &#2342;&#2352;</span> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>40</mn><mn>3</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\">&times;</span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>9</mn><mn>12</mn></mfrac></math>= 10%</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">2</span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>4</mn></mfrac></math><span style=\"font-weight: 400;\">&#2360;&#2366;&#2354; </span>= 27 <span style=\"font-weight: 400;\">&#2350;&#2361;&#2368;&#2344;&#2375;</span></span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">&#2330;&#2325;&#2381;&#2352;&#2357;&#2371;&#2342;&#2381;&#2343;&#2367; &#2330;&#2325;&#2381;&#2352;&#2379;&#2306; &#2325;&#2368; &#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>27</mn><mn>9</mn></mfrac></math> = 3</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<span style=\"font-weight: 400;\">&#2350;&#2370;&#2354;&#2343;&#2344;&nbsp; &nbsp; &nbsp; </span>: &nbsp; &nbsp;<span style=\"font-weight: 400;\">&#2352;&#2366;&#2358;&#2367;</span></span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 10&nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp;11</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 10&nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp;11</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 10&nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; 11</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;_______________</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 1000 : 1331</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">&#2310;&#2357;&#2358;&#2381;&#2351;&#2325; &#2352;&#2366;&#2358;&#2367; </span>= 6000 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1331</mn><mn>1000</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">= &#8377;</span><span style=\"font-family: Cambria Math;\"> 7986</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "17",
                    question_en: "<p>4.<span style=\"font-family: Cambria Math;\"> A b</span><span style=\"font-family: Cambria Math;\">orrowed &#8377; 58000 from B at 8 % per annum simple interest </span><span style=\"font-family: Cambria Math;\">for 2 </span><span style=\"font-family: Cambria Math;\">years.He</span><span style=\"font-family: Cambria Math;\"> lent the same sum to C at 10 % per annum compound interest annually for 2 years. How much did he </span><span style=\"font-family: Cambria Math;\">earn</span><span style=\"font-family: Cambria Math;\"> (in &#8377;) in the transaction at the end of 2 </span><span style=\"font-family: Cambria Math;\">years ?</span></p>\n",
                    question_hi: "<p>4.<span style=\"font-family: Cambria Math;\"> A </span><span style=\"font-family: Cambria Math;\">&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> B </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> 2 </span><span style=\"font-family: Cambria Math;\">&#2357;&#2352;&#2381;&#2359;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> 8% </span><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2352;&#2381;&#2359;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2366;&#2343;&#2366;&#2352;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2381;&#2351;&#2366;&#2332;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> &#8377;58000 </span><span style=\"font-family: Cambria Math;\">&#2313;&#2343;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2354;&#2367;&#2351;&#2366;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2313;&#2360;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2313;&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2352;&#2366;&#2358;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> C </span><span style=\"font-family: Cambria Math;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> 2 </span><span style=\"font-family: Cambria Math;\">&#2357;&#2352;&#2381;&#2359;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2366;&#2352;&#2381;&#2359;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2352;&#2370;&#2346;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> 10% </span><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2352;&#2381;&#2359;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2330;&#2325;&#2381;&#2352;&#2357;&#2371;&#2342;&#2381;&#2343;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2381;&#2351;&#2366;&#2332;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2313;&#2343;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2367;&#2351;&#2366;&#2404;</span><span style=\"font-family: Cambria Math;\"> 2 </span><span style=\"font-family: Cambria Math;\">&#2357;&#2352;&#2381;&#2359;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2309;&#2306;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2313;&#2360;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2354;&#2375;&#2344;</span><span style=\"font-family: Cambria Math;\">-</span><span style=\"font-family: Cambria Math;\">&#2342;&#2375;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2367;&#2340;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> (&#8377; </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\">) </span><span style=\"font-family: Cambria Math;\">&#2309;&#2352;&#2381;&#2332;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>3000</p>\n", "<p>2800</p>\n", 
                                "<p>2900</p>\n", "<p>2750</p>\n"],
                    options_hi: ["<p>3000<span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p>2800</p>\n",
                                "<p>2900</p>\n", "<p>2750</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">4.(</span><span style=\"font-family: Cambria Math;\">c) </span><span style=\"font-family: Cambria Math;\">Interest percent for 2 years at 8% = 16%</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Effective CI percent = 10 + 10 + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>10</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>10</mn></mrow><mn>100</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> = 21%</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Profit earned (in percent) = (21 - 16</span><span style=\"font-family: Cambria Math;\">)%</span><span style=\"font-family: Cambria Math;\"> = 5%</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Profit earned = 5% of 58000 = </span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>20</mn></mfrac></math> &times; 58000 = &#8377; 2900</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">4.(</span><span style=\"font-family: Cambria Math;\">c) </span><span style=\"font-family: Cambria Math;\">2 </span><span style=\"font-family: Cambria Math;\">&#2360;&#2366;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> 8% </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2381;&#2351;&#2366;&#2332;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span><span style=\"font-family: Cambria Math;\"> = 16%</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">&#2346;&#2381;&#2352;&#2349;&#2366;&#2357;&#2368; &#2330;&#2325;&#2381;&#2352;&#2357;&#2371;&#2342;&#2381;&#2343;&#2367; &#2348;&#2381;&#2351;&#2366;&#2332; &#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340; =</span> 10 + 10 + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>10</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>10</mn></mrow><mn>100</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> = 21%</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">&#2309;&#2352;&#2381;&#2332;&#2367;&#2340; &#2354;&#2366;&#2349; (&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340; &#2350;&#2375;&#2306;)</span> = (21 - 16</span><span style=\"font-family: Cambria Math;\">)%</span><span style=\"font-family: Cambria Math;\"> = 5%</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">&#2309;&#2352;&#2381;&#2332;&#2367;&#2340; &#2354;&#2366;&#2349; </span>&nbsp;= 5% of 58000 = </span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>20</mn></mfrac></math> &times; 58000 = &#8377; 2900</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "17",
                    question_en: "<p>5.<span style=\"font-family: Cambria Math;\"> What is the difference between the c</span><span style=\"font-family: Cambria Math;\">ompound interest on a sum of &#8377; 10000 at 12 % p.a. for 1<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>4</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> years when the interest is compounded annually and when the interest is compounded 5 </span><span style=\"font-family: Cambria Math;\">monthly ?</span></p>\n",
                    question_hi: "<p>5. <span style=\"font-family: Cambria Math;\">&#2348;&#2381;&#2351;&#2366;&#2332;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2327;&#2339;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2381;&#2352;&#2350;&#2358;&#2307;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2366;&#2352;&#2381;&#2359;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2352;&#2370;&#2346;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2366;&#2306;&#2330;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2366;&#2361;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2379;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> 10,000 </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2352;&#2366;&#2358;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> 12% </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2366;&#2352;&#2381;&#2359;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>1</mn><mfrac><mn>1</mn><mn>4</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2352;&#2381;&#2359;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2330;&#2325;&#2381;&#2352;&#2357;&#2371;&#2342;&#2381;&#2343;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2381;&#2351;&#2366;&#2332;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2309;&#2306;&#2340;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2367;&#2340;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2379;&#2327;&#2366;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>35</p>\n", "<p>40</p>\n", 
                                "<p>36</p>\n", "<p>42</p>\n"],
                    options_hi: ["<p>35<span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p>40</p>\n",
                                "<p>36</p>\n", "<p>42</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">5.(</span><span style=\"font-family: Cambria Math;\">b) </span><span style=\"font-family: Cambria Math;\">Effective CI percent when compoun</span><span style=\"font-family: Cambria Math;\">ded annually = 12 + 3 + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>12</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>3</mn></mrow><mn>100</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> = 15.36%</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">CI earned = 15.36% &times; 10000 = &#8377;1536 </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Effective rate for compounded 5-monthly = 12 &times;</span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>12</mn></mfrac></math>= 5%</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">5% </span><span style=\"font-family: Cambria Math;\">= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>20</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\">, <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>1</mn><mfrac><mn>1</mn><mn>4</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> years = 15 months,</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Compounding cycles = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>15</mn><mn>5</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> = 3.</span></p>\r\n<p><span style=\"font-weight: 400;\">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; Principal &nbsp; : &nbsp; Amount</span></p>\r\n<p><span style=\"font-weight: 400;\"> </span><span style=\"font-weight: 400;\">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 20&nbsp; &nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; 21</span></p>\r\n<p><span style=\"font-weight: 400;\"> </span><span style=\"font-weight: 400;\">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 20&nbsp; &nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; 21</span></p>\r\n<p><span style=\"font-weight: 400;\"> </span><span style=\"font-weight: 400;\">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 20&nbsp; &nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; 21</span></p>\r\n<p><span style=\"font-weight: 400;\">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; _______________</span></p>\r\n<p><span style=\"font-weight: 400;\"> </span><span style=\"font-weight: 400;\">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 8000&nbsp; &nbsp; &nbsp; : &nbsp; &nbsp; 9261</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">CI earned = 9261 - 8000 = 1261 units</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">CI = 10000 &times;</span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1261</mn><mn>8000</mn></mfrac></math>= &#8377; 1576.25</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Required difference = 1576.25 - 1536 = &#8377;40</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">5.(</span><span style=\"font-family: Cambria Math;\">b) </span><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">&#2346;&#2381;&#2352;&#2349;&#2366;&#2357;&#2368; &#2330;&#2325;&#2381;&#2352;&#2357;&#2371;&#2342;&#2381;&#2343;&#2367; &#2348;&#2381;&#2351;&#2366;&#2332; &#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340; &#2332;&#2348; &#2357;&#2366;&#2352;&#2381;&#2359;&#2367;&#2325; &#2352;&#2370;&#2346; &#2360;&#2375; &#2360;&#2306;&#2351;&#2379;&#2332;&#2367;&#2340; &#2361;&#2379;&#2340;&#2366; &#2361;&#2376;</span> = 12 + 3 + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>12</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>3</mn></mrow><mn>100</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> = 15.36%</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">&#2309;&#2352;&#2381;&#2332;&#2367;&#2340; &#2330;&#2325;&#2381;&#2352;&#2357;&#2371;&#2342;&#2381;&#2343;&#2367; &#2348;&#2381;&#2351;&#2366;&#2332;</span> = 15.36% &times; 10000 = &#8377;1536 </span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">5-&#2350;&#2366;&#2360;&#2367;&#2325; &#2330;&#2325;&#2381;&#2352;&#2357;&#2371;&#2342;&#2381;&#2343;&#2367; &#2325;&#2375; &#2354;&#2367;&#2319; &#2346;&#2381;&#2352;&#2349;&#2366;&#2357;&#2368; &#2342;&#2352; = </span>&nbsp;12 &times;</span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>12</mn></mfrac></math>= 5%</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">5% </span><span style=\"font-family: Cambria Math;\">= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>20</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\">, <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>1</mn><mfrac><mn>1</mn><mn>4</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> <span style=\"font-weight: 400;\">&#2360;&#2366;&#2354; </span>= 15 <span style=\"font-weight: 400;\">&#2350;&#2361;&#2368;&#2344;&#2375;</span>,</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">&#2330;&#2325;&#2381;&#2352;&#2357;&#2371;&#2342;&#2381;&#2343;&#2367; &#2330;&#2325;&#2381;&#2352; </span>&nbsp;= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>15</mn><mn>5</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> = 3.</span></p>\r\n<p><span style=\"font-weight: 400;\">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &#2350;&#2370;&#2354;&nbsp; &nbsp; &nbsp; &nbsp; : &nbsp; &#2352;&#2366;&#2358;&#2367;</span></p>\r\n<p><span style=\"font-weight: 400;\"> </span><span style=\"font-weight: 400;\">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 20&nbsp; &nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; 21</span></p>\r\n<p><span style=\"font-weight: 400;\"> </span><span style=\"font-weight: 400;\">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 20&nbsp; &nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; 21</span></p>\r\n<p><span style=\"font-weight: 400;\"> </span><span style=\"font-weight: 400;\">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 20&nbsp; &nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; 21</span></p>\r\n<p><span style=\"font-weight: 400;\">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; _______________</span></p>\r\n<p><span style=\"font-weight: 400;\"> </span><span style=\"font-weight: 400;\">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 8000&nbsp; &nbsp; &nbsp; : &nbsp; &nbsp; 9261</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">&#2309;&#2352;&#2381;&#2332;&#2367;&#2340; &#2330;&#2325;&#2381;&#2352;&#2357;&#2371;&#2342;&#2381;&#2343;&#2367; &#2348;&#2381;&#2351;&#2366;&#2332;</span>= 9261 - 8000 = 1261 <span style=\"font-weight: 400;\">&#2311;&#2325;&#2366;&#2312; </span></span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">&#2330;&#2325;&#2381;&#2352;&#2357;&#2371;&#2342;&#2381;&#2343;&#2367; &#2348;&#2381;&#2351;&#2366;&#2332; </span>= 10000 &times;</span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1261</mn><mn>8000</mn></mfrac></math>= &#8377; 1576.25</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">&#2310;&#2357;&#2358;&#2381;&#2351;&#2325; &#2309;&#2306;&#2340;&#2352;</span> = 1576.25 - 1536 = &#8377;40</span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "17",
                    question_en: "<p>6.<span style=\"font-family: Cambria Math;\"> The difference between the compound interest and simple interest on &#8377; x at rate of 8.5 % per annum for 2 years is &#8377;260.10. What is the value of </span><span style=\"font-family: Cambria Math;\">x ?</span></p>\n",
                    question_hi: "<p>6.<span style=\"font-family: Cambria Math;\"> &#8377; x </span><span style=\"font-family: Cambria Math;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> 8.5% </span><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2352;&#2381;&#2359;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> 2 </span><span style=\"font-family: Cambria Math;\">&#2357;&#2352;&#2381;&#2359;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2330;&#2325;&#2381;&#2352;&#2357;&#2371;&#2342;&#2381;&#2343;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2381;&#2351;&#2366;&#2332;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2366;&#2343;&#2366;&#2352;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2381;&#2351;&#2366;&#2332;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2368;&#2330;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2309;&#2306;&#2340;&#2352;</span><span style=\"font-family: Cambria Math;\"> &#8377;260.10 </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> x </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;</span><span style=\"font-family: Cambria Math;\">&#2376;</span><span style=\"font-family: Cambria Math;\"> ?</span></p>\n",
                    options_en: ["<p>38000</p>\n", "<p>36000</p>\n", 
                                "<p>34000</p>\n", "<p>35000</p>\n"],
                    options_hi: ["<p>38000</p>\n", "<p>36000</p>\n",
                                "<p>34000</p>\n", "<p>35000</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">6.(</span><span style=\"font-family: Cambria Math;\">b</span><span style=\"font-family: Cambria Math;\">)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1702118913/word/media/image1.png\" width=\"162\" height=\"101\"></p>\r\n<p><span style=\"font-family: Cambria Math;\">Let x = 100 units, now SI earned per year = 8.5 units.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Difference between CI and SI for 2 years = 8.5% of 8.5 units = 0.7225</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">ATQ, 0.7225 units = &#8377; 260.10</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">1 unit = 360</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">100 units = &#8377; 36000</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">6.(</span><span style=\"font-family: Cambria Math;\">b)</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1702118913/word/media/image2.png\" width=\"197\" height=\"123\"></p>\r\n<p><span style=\"font-family: Cambria Math;\">&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2354;&#2368;&#2332;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> x = 100 </span><span style=\"font-family: Cambria Math;\">&#2311;&#2325;&#2366;&#2312;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2309;&#2348;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2352;&#2381;&#2359;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2309;&#2352;&#2381;&#2332;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2366;&#2343;&#2366;&#2352;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2381;&#2351;&#2366;&#2332;</span><span style=\"font-family: Cambria Math;\"> = 8.5 </span><span style=\"font-family: Cambria Math;\">&#2311;&#2325;&#2366;&#2312;&#2404;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">2 </span><span style=\"font-family: Cambria Math;\">&#2357;&#2352;&#2381;&#2359;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2330;&#2325;&#2381;&#2352;&#2357;&#2371;&#2342;&#2381;&#2343;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2381;&#2351;&#2366;&#2332;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2366;&#2343;&#2366;&#2352;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2381;&#2351;&#2366;&#2332;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2368;&#2330;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2309;&#2306;&#2340;&#2352;</span><span style=\"font-family: Cambria Math;\"> = 8.5 </span><span style=\"font-family: Cambria Math;\">&#2311;&#2325;&#2366;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> 8.5% = 0.7225</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2358;&#2381;&#2344;&#2366;&#2344;&#2369;&#2360;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> 0.7225 </span><span style=\"font-family: Cambria Math;\">&#2311;&#2325;&#2366;&#2312;</span><span style=\"font-family: Cambria Math;\"> = &#8377;260.10</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">1 </span><span style=\"font-family: Cambria Math;\">&#2311;&#2325;&#2366;&#2312;</span><span style=\"font-family: Cambria Math;\"> = 360</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">100 </span><span style=\"font-family: Cambria Math;\">&#2351;&#2370;&#2344;&#2367;&#2335;</span><span style=\"font-family: Cambria Math;\"> = &#8377; 36000</span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "17",
                    question_en: "<p>7.<span style=\"font-family: Cambria Math;\"> A man borrowed money for 2 years and paid back in two equal annual installments of &#8377; 2260 at</span><span style=\"font-family: Cambria Math;\"> 5 % compound </span><span style=\"font-family: Cambria Math;\">interest</span><span style=\"font-family: Cambria Math;\">,compounded</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">annually.what</span><span style=\"font-family: Cambria Math;\"> was the sum borrowed ? </span></p>\n",
                    question_hi: "<p>7.<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2310;&#2342;&#2350;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> 2 </span><span style=\"font-family: Cambria Math;\">&#2360;&#2366;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2376;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2313;&#2343;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> 2260 </span><span style=\"font-family: Cambria Math;\">&#2352;&#2369;&#2346;&#2351;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2366;&#2352;&#2381;&#2359;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2367;&#2358;&#2381;&#2340;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> 5% </span><span style=\"font-family: Cambria Math;\">&#2357;&#2366;&#2352;&#2381;&#2359;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2330;&#2325;&#2381;&#2352;&#2357;&#2371;&#2342;&#2381;&#2343;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2381;&#2351;&#2366;&#2332;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2366;&#2346;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2349;&#2369;&#2327;&#2340;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2367;&#2351;&#2366;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2313;&#2343;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2354;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2327;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2352;&#2366;&#2358;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2341;&#2368;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>&#8377; 4420</p>\n", "<p>&#8377; 4520</p>\n", 
                                "<p>&#8377; 4300</p>\n", "<p>&#8377; 4200</p>\n"],
                    options_hi: ["<p>&#8377; 4420</p>\n", "<p>&#8377; 4520</p>\n",
                                "<p>&#8377; 4300</p>\n", "<p>&#8377; 4200</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">7.(</span><span style=\"font-family: Cambria Math;\">d)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">5% </span><span style=\"font-family: Cambria Math;\">= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>20</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\">,</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; Principal&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp;Installment</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;20 &times; 21&nbsp; &nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; 21 &times; 21</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 20&sup2; &nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;21&sup2;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; _____________________</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 820 </span><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">At 441 units of annual installment, </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">820 units of principal amount </span><span style=\"font-family: Cambria Math;\">is</span><span style=\"font-family: Cambria Math;\"> paid.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">441 units &mdash; 2260</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">1unit &mdash; 5.12</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">820 units &mdash; 4198 &asymp; 4200</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">7.(</span><span style=\"font-family: Cambria Math;\">d)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">5% </span><span style=\"font-family: Cambria Math;\">= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>20</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\">,</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&#2350;&#2370;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2352;&#2366;&#2358;&#2367;&nbsp; &nbsp; &nbsp;</span><span style=\"font-family: Cambria Math;\"> :&nbsp; &nbsp; &nbsp; </span><span style=\"font-family: Cambria Math;\">&#2325;&#2367;&#2360;&#2381;&#2340;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;20 &times; 21&nbsp; &nbsp; :&nbsp; &nbsp; &nbsp;21 &times; 21</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;20&sup2; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; &nbsp;21&sup2; </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;_____________________</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 820 </span><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&#2357;&#2366;&#2352;&#2381;&#2359;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2367;&#2360;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> 441 </span><span style=\"font-family: Cambria Math;\">&#2311;&#2325;&#2366;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2370;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2352;&#2366;&#2358;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> 820 </span><span style=\"font-family: Cambria Math;\">&#2311;&#2325;&#2366;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2349;&#2369;&#2327;&#2340;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2366;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;&#2404;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">441 </span><span style=\"font-family: Cambria Math;\">&#2311;&#2325;&#2366;&#2312;</span><span style=\"font-family: Cambria Math;\"> &mdash; 2260</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">1 </span><span style=\"font-family: Cambria Math;\">&#2311;&#2325;&#2366;&#2312;</span><span style=\"font-family: Cambria Math;\"> &mdash; 5.12</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">820 </span><span style=\"font-family: Cambria Math;\">&#2311;&#2325;&#2366;&#2312;</span><span style=\"font-family: Cambria Math;\"> &mdash; 4198 &asymp; 4200</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "17",
                    question_en: "<p>8. <span style=\"font-family: Cambria Math;\">What is the difference between the compound interest on &#8377;8,000 at 15% per annum for 2 years when the interest is compounded yearly and when compounded 8-monthly?</span></p>\n",
                    question_hi: "<p>8. <span style=\"font-family: Cambria Math;\">&#8377;</span><span style=\"font-family: Cambria Math;\">8,000 </span><span style=\"font-family: Cambria Math;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> 2 </span><span style=\"font-family: Cambria Math;\">&#2357;&#2352;&#2381;&#2359;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2352;&#2381;&#2359;</span><span style=\"font-family: Cambria Math;\"> 15% </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2330;&#2325;&#2381;&#2352;&#2357;&#2371;&#2342;&#2381;&#2343;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2381;&#2351;&#2366;&#2332;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2368;&#2330;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2309;&#2306;&#2340;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2332;&#2348;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2381;&#2351;&#2366;&#2332;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2366;&#2352;&#2381;&#2359;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2352;&#2370;&#2346;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2330;&#2325;&#2381;&#2352;&#2357;&#2371;&#2342;&#2381;&#2343;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2379;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2348;</span><span style=\"font-family: Cambria Math;\"> 8-</span><span style=\"font-family: Cambria Math;\">&#2350;&#2366;&#2360;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2352;&#2370;&#2346;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2330;&#2325;&#2381;&#2352;&#2357;&#2371;&#2342;&#2381;&#2343;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2379;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;</span><span style=\"font-family: Cambria Math;\">&#2376;</span><span style=\"font-family: Cambria Math;\"> ?</span></p>\n",
                    options_en: ["<p>&#8377;70</p>\n", "<p>&#8377;72</p>\n", 
                                "<p>&#8377;75</p>\n", "<p>&#8377;68</p>\n"],
                    options_hi: ["<p>&#8377;70<span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p>&#8377;72</p>\n",
                                "<p>&#8377;75</p>\n", "<p>&#8377;68</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">8.(</span><span style=\"font-family: Cambria Math;\">d) </span><span style=\"font-family: Cambria Math;\">Effective CI rate for 2 years (interest compounded annually) </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">= 15 + 15 + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>15</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>15</mn></mrow><mn>100</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> = 30 + 2.25 = 32.25%</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Now for interest compounded 8-</span><span style=\"font-family: Cambria Math;\">monthly :</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">For 12 months, rate of interest is 15%</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">so</span><span style=\"font-family: Cambria Math;\">, for 8 months, rate of interest = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>15</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>8</mn></mrow><mn>12</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> = 10%</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Effective CI rate for 2 years (interest compounded 8-monthly) </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">= a</span><span style=\"font-family: Cambria Math;\">+ a</span><span style=\"font-family: Cambria Math;\">+ a</span><span style=\"font-family: Cambria Math;\">+ </span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mi>a</mi><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mi>a</mi><mo>)</mo><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mo>(</mo><mi>a</mi><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mi>a</mi><mo>)</mo><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mo>(</mo><mi>a</mi><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mi>a</mi><mo>)</mo></mrow><mn>100</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\">&nbsp; + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>a</mi><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mi>a</mi><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mi>a</mi><mo>&nbsp;</mo></mrow><mn>10000</mn></mfrac></math>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; (where, a </span><span style=\"font-family: Cambria Math;\">= rate of interest)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">= 10 + 10 + 10 +&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>10</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>10</mn><mo>)</mo><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mo>(</mo><mn>10</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>10</mn><mo>)</mo><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mo>(</mo><mn>10</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>10</mn><mo>)</mo></mrow><mrow><mn>100</mn><mo>&nbsp;</mo></mrow></mfrac><mo>+</mo><mfrac><mrow><mn>10</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>10</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>10</mn><mo>&nbsp;</mo><mo>&nbsp;</mo></mrow><mn>10000</mn></mfrac></math></span></p>\r\n<p><span style=\"font-family: Cambria Math;\">= 30 + </span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>300</mn><mn>100</mn></mfrac><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mfrac><mn>1000</mn><mn>10000</mn></mfrac></math> </span><span style=\"font-family: Cambria Math;\"> = 30 + 3 + 0.1 = 33.1%</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">So, required amount = (33.1 - 32.25</span><span style=\"font-family: Cambria Math;\">)%</span><span style=\"font-family: Cambria Math;\"> of 8000 = (0.85)% of 8000 = </span><span style=\"font-family: Cambria Math;\">&#8377;</span><span style=\"font-family: Cambria Math;\">68</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">8.(</span><span style=\"font-family: Cambria Math;\">d) </span><span style=\"font-family: Cambria Math;\">2 </span><span style=\"font-family: Cambria Math;\">&#2357;&#2352;&#2381;&#2359;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2349;&#2366;&#2357;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2330;&#2325;&#2381;&#2352;&#2357;&#2371;&#2342;&#2381;&#2343;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2381;&#2351;&#2366;&#2332;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2352;</span><span style=\"font-family: Cambria Math;\"> (</span><span style=\"font-family: Cambria Math;\">&#2357;&#2366;&#2352;&#2381;&#2359;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2352;&#2370;&#2346;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2330;&#2325;&#2381;&#2352;&#2357;&#2371;&#2342;&#2381;&#2343;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2381;&#2351;&#2366;&#2332;</span><span style=\"font-family: Cambria Math;\">)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">= 15 + 15 + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>15</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>15</mn></mrow><mn>100</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> = 30 + 2.25 = 32.25%</span></p>\r\n<p><span style=\"font-weight: 400;\">&#2309;&#2348; &#2348;&#2381;&#2351;&#2366;&#2332; &#2325;&#2375; &#2354;&#2367;&#2319; 8-&#2350;&#2366;&#2360;&#2367;&#2325; &#2330;&#2325;&#2381;&#2352;&#2357;&#2371;&#2342;&#2381;&#2343;&#2367; :</span></p>\r\n<p><span style=\"font-weight: 400;\">12 &#2350;&#2361;&#2368;&#2344;&#2379;&#2306; &#2325;&#2375; &#2354;&#2367;&#2319; &#2348;&#2381;&#2351;&#2366;&#2332; &#2342;&#2352; 15% &#2361;&#2376;&#2404;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">&#2309;&#2340;: 8 &#2350;&#2361;&#2368;&#2344;&#2379;&#2306; &#2325;&#2375; &#2354;&#2367;&#2319; &#2348;&#2381;&#2351;&#2366;&#2332; &#2342;&#2352;</span> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>15</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>8</mn></mrow><mn>12</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> = 10%</span></p>\r\n<p><span style=\"font-weight: 400;\">2 &#2357;&#2352;&#2381;&#2359;&#2379;&#2306; &#2325;&#2375; &#2354;&#2367;&#2319; &#2346;&#2381;&#2352;&#2349;&#2366;&#2357;&#2368; &#2330;&#2325;&#2381;&#2352;&#2357;&#2371;&#2342;&#2381;&#2343;&#2367; &#2348;&#2381;&#2351;&#2366;&#2332; &#2342;&#2352; (8 &#2350;&#2366;&#2360;&#2367;&#2325; &#2330;&#2325;&#2381;&#2352;&#2357;&#2371;&#2342;&#2381;&#2343;&#2367; &#2348;&#2381;&#2351;&#2366;&#2332;)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">= a </span><span style=\"font-family: Cambria Math;\">+ a </span><span style=\"font-family: Cambria Math;\">+ a </span><span style=\"font-family: Cambria Math;\">+ </span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mi>a</mi><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mi>a</mi><mo>)</mo><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mo>(</mo><mi>a</mi><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mi>a</mi><mo>)</mo><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mo>(</mo><mi>a</mi><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mi>a</mi><mo>)</mo></mrow><mn>100</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\">&nbsp; + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>a</mi><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mi>a</mi><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mi>a</mi><mo>&nbsp;</mo></mrow><mn>10000</mn></mfrac></math>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; </span><span style=\"font-weight: 400;\">&nbsp;(&#2332;&#2361;&#2366;&#2305;, a = &#2348;&#2381;&#2351;&#2366;&#2332; &#2342;&#2352;)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">= 10 + 10 + 10 +&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>10</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>10</mn><mo>)</mo><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mo>(</mo><mn>10</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>10</mn><mo>)</mo><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mo>(</mo><mn>10</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>10</mn><mo>)</mo></mrow><mrow><mn>100</mn><mo>&nbsp;</mo></mrow></mfrac><mo>+</mo><mfrac><mrow><mn>10</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>10</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>10</mn><mo>&nbsp;</mo><mo>&nbsp;</mo></mrow><mn>10000</mn></mfrac></math></span></p>\r\n<p><span style=\"font-family: Cambria Math;\">= 30 + </span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>300</mn><mn>100</mn></mfrac><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mfrac><mn>1000</mn><mn>10000</mn></mfrac></math> </span><span style=\"font-family: Cambria Math;\"> = 30 + 3 + 0.1 = 33.1%</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">&#2340;&#2379;, &#2309;&#2349;&#2368;&#2359;&#2381;&#2335; &#2352;&#2366;&#2358;&#2367; = 8000 &#2325;&#2366; (33.1 - 32.25)% = 8000 &#2325;&#2366; (0.85)% = &#8377;68</span> </span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "17",
                    question_en: "<p>9. <span style=\"font-family: Cambria Math;\">What is the difference </span><span style=\"font-family: Cambria Math;\">( in</span><span style=\"font-family: Cambria Math;\"> &#8377;, to the nea</span><span style=\"font-family: Cambria Math;\">rest rupee) between the simple interest and compound interest on &#8377;24,500 in two years at the rate of 8% per annum? The compound interest is compounded annually.</span></p>\n",
                    question_hi: "<p>9. <span style=\"font-family: Cambria Math;\">&#2342;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2352;&#2381;&#2359;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> 8% </span><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2352;&#2381;&#2359;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> &#8377; 24,500 </span><span style=\"font-family: Cambria Math;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2366;&#2343;&#2366;&#2352;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2381;&#2351;&#2366;&#2332;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2330;&#2325;&#2381;&#2352;&#2357;&#2371;&#2342;&#2381;&#2343;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2381;&#2351;&#2366;&#2332;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2368;&#2330;</span><span style=\"font-family: Cambria Math;\"> (&#8377; </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\">) </span><span style=\"font-family: Cambria Math;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2309;&#2306;&#2340;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">? </span></p>\n",
                    options_en: ["<p>157<span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p>175</p>\n", 
                                "<p>257</p>\n", "<p>225</p>\n"],
                    options_hi: ["<p>&#8377; 157</p>\n", "<p>&#8377; 175</p>\n",
                                "<p>&#8377; 257</p>\n", "<p>&#8377; 225</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">9.(</span><span style=\"font-family: Cambria Math;\">a) </span><span style=\"font-family: Cambria Math;\">Effective SI rate for 2 years = 2 &times; </span><span style=\"font-family: Cambria Math;\">8 = 16%</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Effective CI rate for 2 years = 8 + 8 + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>8</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>8</mn></mrow><mn>100</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> = 16.64%</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">So, difference (in %) = 16.64% - 16% = 0.64%</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Required difference = 0.64% of 24500 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>64</mn><mo>&nbsp;</mo></mrow><mn>10000</mn></mfrac></math>&times; </span><span style=\"font-family: Cambria Math;\">24500 = 156.8 &asymp;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#8377;</span><span style=\"font-family: Cambria Math;\">157</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">9.(</span><span style=\"font-family: Cambria Math;\">a) </span><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">2 &#2357;&#2352;&#2381;&#2359;&#2379;&#2306; &#2325;&#2375; &#2354;&#2367;&#2319; &#2346;&#2381;&#2352;&#2349;&#2366;&#2357;&#2368; &#2360;&#2366;&#2343;&#2366;&#2352;&#2339; &#2348;&#2381;&#2351;&#2366;&#2332; &#2342;&#2352;</span> = 2 &times; </span><span style=\"font-family: Cambria Math;\">8 = 16%</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">2 &#2357;&#2352;&#2381;&#2359;&#2379;&#2306; &#2325;&#2375; &#2354;&#2367;&#2319; &#2346;&#2381;&#2352;&#2349;&#2366;&#2357;&#2368; &#2330;&#2325;&#2381;&#2352;&#2357;&#2371;&#2342;&#2381;&#2343;&#2367; &#2348;&#2381;&#2351;&#2366;&#2332; &#2342;&#2352;</span>= 8 + 8 + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>8</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>8</mn></mrow><mn>100</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> = 16.64%</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">&#2309;&#2340;: &#2309;&#2306;&#2340;&#2352; (% &#2350;&#2375;&#2306;) </span>= 16.64% - 16% = 0.64%</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">&#2310;&#2357;&#2358;&#2381;&#2351;&#2325; &#2309;&#2306;&#2340;&#2352; </span>&nbsp;= 0.64% of 24500 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>64</mn><mo>&nbsp;</mo></mrow><mn>10000</mn></mfrac></math>&times; </span><span style=\"font-family: Cambria Math;\">24500 = 156.8 &asymp;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#8377;</span><span style=\"font-family: Cambria Math;\">157</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "17",
                    question_en: "<p>10<span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\">A sum of money lent on interest, compounded semi-annually amounts t</span><span style=\"font-family: Cambria Math;\">o &#8377;54,000 in one year and to &#8377;65,340 in two years. What is the rate of interest per </span><span style=\"font-family: Cambria Math;\">Annum?</span></p>\n",
                    question_hi: "<p>10<span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\">&#2348;&#2381;&#2351;&#2366;&#2332;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2313;&#2343;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2327;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2352;&#2366;&#2358;&#2367;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2309;&#2352;&#2381;&#2343;</span><span style=\"font-family: Cambria Math;\">-</span><span style=\"font-family: Cambria Math;\">&#2357;&#2366;&#2352;&#2381;&#2359;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2330;&#2325;&#2381;&#2352;&#2357;&#2371;&#2342;&#2381;&#2343;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2352;&#2381;&#2359;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> &#8377;54,000 </span><span style=\"font-family: Cambria Math;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2352;&#2381;&#2359;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> &#8377;65,340 </span><span style=\"font-family: Cambria Math;\">&#2361;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2366;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2352;&#2381;</span><span style=\"font-family: Cambria Math;\">&#2359;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2381;&#2351;&#2366;&#2332;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>20%</p>\n", "<p>10%</p>\n", 
                                "<p>16%</p>\n", "<p>12%</p>\n"],
                    options_hi: ["<p>20%</p>\n", "<p>10%</p>\n",
                                "<p>16%</p>\n", "<p>12%</p>\n"],
                    solution_en: "<p>10<span style=\"font-family: Cambria Math;\">.(a) </span><span style=\"font-family: Cambria Math;\">Let rate of interest = R</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">According to the question,</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>65340</mn><mo>&nbsp;</mo></mrow><mn>54000</mn></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>P</mi><msup><mfenced><mrow><mn>1</mn><mo>&nbsp;</mo><mo>+</mo><mfrac><mrow><mi>R</mi><mo>/</mo><mn>2</mn></mrow><mn>100</mn></mfrac></mrow></mfenced><mrow><mn>2</mn><mo>&times;</mo><mn>2</mn></mrow></msup></mrow><mrow><mi>P</mi><msup><mfenced><mrow><mn>1</mn><mo>&nbsp;</mo><mo>+</mo><mfrac><mrow><mi>R</mi><mo>/</mo><mn>2</mn></mrow><mn>100</mn></mfrac></mrow></mfenced><mrow><mn>2</mn><mo>&times;</mo><mn>1</mn></mrow></msup></mrow></mfrac></math></span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>65340</mn><mo>&nbsp;</mo></mrow><mn>54000</mn></mfrac></math> =&nbsp;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>P</mi><msup><mfenced><mrow><mn>1</mn><mo>&nbsp;</mo><mo>+</mo><mfrac><mrow><mi>R</mi><mo>/</mo><mn>2</mn></mrow><mn>100</mn></mfrac></mrow></mfenced><mn>4</mn></msup></mrow><mrow><mi>P</mi><msup><mfenced><mrow><mn>1</mn><mo>&nbsp;</mo><mo>+</mo><mfrac><mrow><mi>R</mi><mo>/</mo><mn>2</mn></mrow><mn>100</mn></mfrac></mrow></mfenced><mn>2</mn></msup></mrow></mfrac></math></span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>121</mn><mo>&nbsp;</mo></mrow><mn>100</mn></mfrac></math>=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mfenced><mrow><mn>1</mn><mo>+</mo><mstyle displaystyle=\"true\"><mfrac><mrow><mi>R</mi><mo>/</mo><mn>2</mn></mrow><mn>100</mn></mfrac></mstyle></mrow></mfenced><mn>2</mn></msup><mn>1</mn></mfrac></math></span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mfenced><mfrac><mn>11</mn><mn>10</mn></mfrac></mfenced><mn>2</mn></msup></math>=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mfenced><mrow><mn>1</mn><mo>+</mo><mfrac><mi>R</mi><mn>200</mn></mfrac></mrow></mfenced><mn>2</mn></msup></math></span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>11</mn><mn>10</mn></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>1</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mfrac><mi>R</mi><mn>200</mn></mfrac></math></span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>R</mi><mn>200</mn></mfrac></math>=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>10</mn></mfrac></math></span></p>\r\n<p><span style=\"font-family: Cambria Math;\">R = 20%</span></p>\n",
                    solution_hi: "<p>10<span style=\"font-family: Cambria Math;\">.(a) </span><span style=\"font-family: Cambria Math;\">&#2350;&#2366;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2348;&#2381;&#2351;&#2366;&#2332;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2352;</span><span style=\"font-family: Cambria Math;\"> = R</span></p>\r\n<p><span style=\"font-weight: 400;\">&#2346;&#2381;&#2352;&#2358;&#2381;&#2344; &#2325;&#2375; &#2309;&#2344;&#2369;&#2360;&#2366;&#2352;,</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>65340</mn><mo>&nbsp;</mo></mrow><mn>54000</mn></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>P</mi><msup><mfenced><mrow><mn>1</mn><mo>&nbsp;</mo><mo>+</mo><mfrac><mrow><mi>R</mi><mo>/</mo><mn>2</mn></mrow><mn>100</mn></mfrac></mrow></mfenced><mrow><mn>2</mn><mo>&times;</mo><mn>2</mn></mrow></msup></mrow><mrow><mi>P</mi><msup><mfenced><mrow><mn>1</mn><mo>&nbsp;</mo><mo>+</mo><mfrac><mrow><mi>R</mi><mo>/</mo><mn>2</mn></mrow><mn>100</mn></mfrac></mrow></mfenced><mrow><mn>2</mn><mo>&times;</mo><mn>1</mn></mrow></msup></mrow></mfrac></math></span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>65340</mn><mo>&nbsp;</mo></mrow><mn>54000</mn></mfrac></math> =&nbsp;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>P</mi><msup><mfenced><mrow><mn>1</mn><mo>&nbsp;</mo><mo>+</mo><mfrac><mrow><mi>R</mi><mo>/</mo><mn>2</mn></mrow><mn>100</mn></mfrac></mrow></mfenced><mn>4</mn></msup></mrow><mrow><mi>P</mi><msup><mfenced><mrow><mn>1</mn><mo>&nbsp;</mo><mo>+</mo><mfrac><mrow><mi>R</mi><mo>/</mo><mn>2</mn></mrow><mn>100</mn></mfrac></mrow></mfenced><mn>2</mn></msup></mrow></mfrac></math></span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>121</mn><mo>&nbsp;</mo></mrow><mn>100</mn></mfrac></math>=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mfenced><mrow><mn>1</mn><mo>+</mo><mstyle displaystyle=\"true\"><mfrac><mrow><mi>R</mi><mo>/</mo><mn>2</mn></mrow><mn>100</mn></mfrac></mstyle></mrow></mfenced><mn>2</mn></msup><mn>1</mn></mfrac></math></span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mfenced><mfrac><mn>11</mn><mn>10</mn></mfrac></mfenced><mn>2</mn></msup></math>=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mfenced><mrow><mn>1</mn><mo>+</mo><mfrac><mi>R</mi><mn>200</mn></mfrac></mrow></mfenced><mn>2</mn></msup></math></span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>11</mn><mn>10</mn></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>1</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mfrac><mi>R</mi><mn>200</mn></mfrac></math></span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>R</mi><mn>200</mn></mfrac></math>=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>10</mn></mfrac></math></span></p>\r\n<p><span style=\"font-family: Cambria Math;\">R = 20%</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "17",
                    question_en: "<p>11<span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\">What is the difference between the compound interest and simple interest (in Rs. to the nearest integer) on Rs.12</span><span style=\"font-family: Cambria Math;\">,000</span><span style=\"font-family: Cambria Math;\"> in 2 years at 8% per annum, compounded annually?</span></p>\n",
                    question_hi: "<p>11<span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\">2 </span><span style=\"font-family: Cambria Math;\">&#2357;&#2352;&#2381;&#2359;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> 8% </span><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2357;&#2352;&#2381;&#2359;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> 12,000 </span><span style=\"font-family: Cambria Math;\">&#2352;&#2369;&#2346;&#2351;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2330;&#2325;&#2381;&#2352;&#2357;&#2371;&#2342;&#2381;&#2343;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2381;&#2351;&#2366;&#2332;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2366;&#2343;&#2366;&#2352;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2381;&#2351;&#2366;&#2332;</span><span style=\"font-family: Cambria Math;\"> (</span><span style=\"font-family: Cambria Math;\">&#2344;&#2367;&#2325;&#2335;&#2340;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2370;&#2352;&#2381;&#2339;&#2366;&#2306;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2352;&#2369;&#2346;&#2351;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\">) </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2368;&#2330;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2309;&#2306;&#2340;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">Metric level </span><span style=\"font-family: Cambria Math;\">(ha) 77</span></p>\n",
                    options_en: ["<p>77</p>\n", "<p>76</p>\n", 
                                "<p>98</p>\n", "<p>97</p>\n"],
                    options_hi: ["<p>77</p>\n", "<p>76</p>\n",
                                "<p>98</p>\n", "<p>97</p>\n"],
                    solution_en: "<p>11<span style=\"font-family: Cambria Math;\">.(a) </span><span style=\"font-family: Cambria Math;\">Effective SI rate for 2 yea</span><span style=\"font-family: Cambria Math;\">rs = 2 &times; </span><span style=\"font-family: Cambria Math;\">8 = 16%</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Effective CI rate for 2 years = 8 + 8 + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>8</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>8</mn></mrow><mn>100</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> = 16.64%</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">So, difference (in %) = 16.64% - 16% = 0.64%</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Required difference = 0.64% of 12000 =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>64</mn><mo>&nbsp;</mo></mrow><mn>10000</mn></mfrac></math> &times; </span><span style=\"font-family: Cambria Math;\">12000 = 76.8 &asymp;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#8377;</span><span style=\"font-family: Cambria Math;\">77</span></p>\n",
                    solution_hi: "<p>11<span style=\"font-family: Cambria Math;\">.(a) </span><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">2 &#2357;&#2352;&#2381;&#2359;&#2379;&#2306; &#2325;&#2375; &#2354;&#2367;&#2319; &#2346;&#2381;&#2352;&#2349;&#2366;&#2357;&#2368; &#2360;&#2366;&#2343;&#2366;&#2352;&#2339; &#2348;&#2381;&#2351;&#2366;&#2332; &#2342;&#2352; </span>&nbsp;= 2 &times; </span><span style=\"font-family: Cambria Math;\">8 = 16%</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">2 &#2357;&#2352;&#2381;&#2359;&#2379;&#2306; &#2325;&#2375; &#2354;&#2367;&#2319; &#2346;&#2381;&#2352;&#2349;&#2366;&#2357;&#2368; &#2330;&#2325;&#2381;&#2352;&#2357;&#2371;&#2342;&#2381;&#2343;&#2367; &#2348;&#2381;&#2351;&#2366;&#2332; &#2342;&#2352;</span> = 8 + 8 + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>8</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>8</mn></mrow><mn>100</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> = 16.64%</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">&#2309;&#2340;: &#2309;&#2306;&#2340;&#2352; (% &#2350;&#2375;&#2306;) </span>= 16.64% - 16% = 0.64%</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">&#2310;&#2357;&#2358;&#2381;&#2351;&#2325; &#2309;&#2306;&#2340;&#2352; = 12000 &#2325;&#2366; 0.64% </span>=<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>64</mn><mo>&nbsp;</mo></mrow><mn>10000</mn></mfrac></math> &times; </span><span style=\"font-family: Cambria Math;\">12000 = 76.8 &asymp;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#8377;</span><span style=\"font-family: Cambria Math;\">77</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "17",
                    question_en: "<p>12<span style=\"font-family: Cambria Math;\">.</span><span style=\"font-family: Cambria Math;\"> A sum of money amounts to Rs.16</span><span style=\"font-family: Cambria Math;\">,800</span><span style=\"font-family: Cambria Math;\"> in one year and to Rs.17,472 in one and a half years on the basis of interest compounded semi-annually. What is the rate of interest per annum?</span></p>\n",
                    question_hi: "<p>12<span style=\"font-family: Cambria Math;\">.</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2309;&#2352;&#2381;&#2343;</span><span style=\"font-family: Cambria Math;\">-</span><span style=\"font-family: Cambria Math;\">&#2357;&#2366;&#2352;&#2381;&#2359;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2330;&#2325;&#2381;&#2352;&#2357;&#2371;&#2342;&#2381;&#2343;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2381;&#2351;&#2366;&#2332;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2310;&#2343;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2352;&#2366;&#2358;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2352;&#2381;&#2359;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> 16,800 </span><span style=\"font-family: Cambria Math;\">&#2352;&#2369;&#2346;&#2351;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2337;&#2375;&#2338;&#2364;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2366;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> 17,472 </span><span style=\"font-family: Cambria Math;\">&#2352;&#2369;&#2346;&#2351;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2366;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2352;&#2381;&#2359;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2381;&#2351;&#2366;&#2332;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>16%</p>\n", "<p>12%</p>\n", 
                                "<p>8%</p>\n", "<p>4%</p>\n"],
                    options_hi: ["<p>16%</p>\n", "<p>12%</p>\n",
                                "<p>8%</p>\n", "<p>4%</p>\n"],
                    solution_en: "<p>12<span style=\"font-family: Cambria Math;\">.(c) </span><span style=\"font-family: Cambria Math;\">Let rate of interest = R</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">According to the question,</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>17472</mn><mo>&nbsp;</mo></mrow><mn>16800</mn></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>P</mi><mo>&nbsp;</mo><msup><mfenced><mrow><mn>1</mn><mo>+</mo><mfrac><mstyle displaystyle=\"true\"><mfrac bevelled=\"true\"><mi>R</mi><mn>2</mn></mfrac></mstyle><mn>100</mn></mfrac></mrow></mfenced><mrow><mn>2</mn><mo>&times;</mo><mn>1</mn><mo>.</mo><mn>5</mn></mrow></msup></mrow><mrow><mi>P</mi><mo>&nbsp;</mo><mo>&nbsp;</mo><msup><mfenced><mrow><mn>1</mn><mo>+</mo><mfrac><mstyle displaystyle=\"true\"><mfrac bevelled=\"true\"><mi>R</mi><mn>2</mn></mfrac></mstyle><mn>100</mn></mfrac></mrow></mfenced><mrow><mn>2</mn><mo>&times;</mo><mn>1</mn></mrow></msup></mrow></mfrac></math></span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2184</mn><mo>&nbsp;</mo></mrow><mn>2100</mn></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mfenced><mrow><mn>1</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mstyle displaystyle=\"true\"><mfrac><mstyle displaystyle=\"true\"><mfrac bevelled=\"true\"><mi>R</mi><mn>2</mn></mfrac></mstyle><mn>100</mn></mfrac></mstyle></mrow></mfenced><mn>3</mn></msup><msup><mfenced><mrow><mn>1</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mstyle displaystyle=\"true\"><mfrac><mstyle displaystyle=\"true\"><mfrac bevelled=\"true\"><mi>R</mi><mn>2</mn></mfrac></mstyle><mn>100</mn></mfrac></mstyle></mrow></mfenced><mn>2</mn></msup></mfrac></math></span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>546</mn><mo>&nbsp;</mo></mrow><mn>525</mn></mfrac></math>=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfenced><mrow><mn>1</mn><mo>+</mo><mfrac><mi>R</mi><mn>200</mn></mfrac></mrow></mfenced></math></span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>R</mi><mo>&nbsp;</mo></mrow><mn>200</mn></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>21</mn><mn>525</mn></mfrac></math></span></p>\r\n<p><span style=\"font-family: Cambria Math;\">R = 8%</span></p>\n",
                    solution_hi: "<p>12<span style=\"font-family: Cambria Math;\">.(c) </span><span style=\"font-family: Cambria Math;\">&#2350;&#2366;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2381;&#2351;&#2366;&#2332;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2352;</span><span style=\"font-family: Cambria Math;\"> = R</span></p>\r\n<p><span style=\"font-weight: 400;\">&#2346;&#2381;&#2352;&#2358;&#2381;&#2344; &#2325;&#2375; &#2309;&#2344;&#2369;&#2360;&#2366;&#2352;,</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>17472</mn><mo>&nbsp;</mo></mrow><mn>16800</mn></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>P</mi><mo>&nbsp;</mo><msup><mfenced><mrow><mn>1</mn><mo>+</mo><mfrac><mstyle displaystyle=\"true\"><mfrac bevelled=\"true\"><mi>R</mi><mn>2</mn></mfrac></mstyle><mn>100</mn></mfrac></mrow></mfenced><mrow><mn>2</mn><mo>&times;</mo><mn>1</mn><mo>.</mo><mn>5</mn></mrow></msup></mrow><mrow><mi>P</mi><mo>&nbsp;</mo><mo>&nbsp;</mo><msup><mfenced><mrow><mn>1</mn><mo>+</mo><mfrac><mstyle displaystyle=\"true\"><mfrac bevelled=\"true\"><mi>R</mi><mn>2</mn></mfrac></mstyle><mn>100</mn></mfrac></mrow></mfenced><mrow><mn>2</mn><mo>&times;</mo><mn>1</mn></mrow></msup></mrow></mfrac></math></span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2184</mn><mo>&nbsp;</mo></mrow><mn>2100</mn></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mfenced><mrow><mn>1</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mstyle displaystyle=\"true\"><mfrac><mstyle displaystyle=\"true\"><mfrac bevelled=\"true\"><mi>R</mi><mn>2</mn></mfrac></mstyle><mn>100</mn></mfrac></mstyle></mrow></mfenced><mn>3</mn></msup><msup><mfenced><mrow><mn>1</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mstyle displaystyle=\"true\"><mfrac><mstyle displaystyle=\"true\"><mfrac bevelled=\"true\"><mi>R</mi><mn>2</mn></mfrac></mstyle><mn>100</mn></mfrac></mstyle></mrow></mfenced><mn>2</mn></msup></mfrac></math></span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>546</mn><mo>&nbsp;</mo></mrow><mn>525</mn></mfrac></math>=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfenced><mrow><mn>1</mn><mo>+</mo><mfrac><mi>R</mi><mn>200</mn></mfrac></mrow></mfenced></math></span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>R</mi><mo>&nbsp;</mo></mrow><mn>200</mn></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>21</mn><mn>525</mn></mfrac></math></span></p>\r\n<p><span style=\"font-family: Cambria Math;\">R = 8%</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "17",
                    question_en: "<p>13<span style=\"font-family: Cambria Math;\">.</span><span style=\"font-family: Cambria Math;\"> What will be the compound interest on a sum of Rs.3</span><span style=\"font-family: Cambria Math;\">,750</span><span style=\"font-family: Cambria Math;\"> when the interest is compounded semi-annually at 16% per annum for one year?</span></p>\n",
                    question_hi: "<p>13<span style=\"font-family: Cambria Math;\">.</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">3,750 </span><span style=\"font-family: Cambria Math;\">&#2352;&#2369;&#2346;&#2351;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2352;&#2366;&#2358;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2330;&#2325;&#2381;&#2352;&#2357;&#2371;&#2342;&#2381;&#2343;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2381;&#2351;&#2366;&#2332;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2379;&#2327;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2348;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2381;&#2351;&#2366;&#2332;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2352;&#2381;&#2359;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> 16% </span><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2352;&#2381;&#2359;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2309;&#2352;&#2381;&#2343;</span><span style=\"font-family: Cambria Math;\">-</span><span style=\"font-family: Cambria Math;\">&#2357;&#2366;&#2352;&#2381;&#2359;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2352;&#2370;&#2346;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2306;&#2351;&#2379;&#2332;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2366;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>Rs.600</p>\n", "<p>Rs.650</p>\n", 
                                "<p>Rs.640</p>\n", "<p>Rs.624</p>\n"],
                    options_hi: ["<p>Rs.600</p>\n", "<p>Rs.650</p>\n",
                                "<p>Rs.640</p>\n", "<p>Rs.624</p>\n"],
                    solution_en: "<p>13<span style=\"font-family: Cambria Math;\">.(d) </span><span style=\"font-family: Cambria Math;\">According</span><span style=\"font-family: Cambria Math;\"> to the question,</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">CI =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">P</mi><mo>&nbsp;</mo><mo>[</mo><mo>&nbsp;</mo><msup><mfenced><mrow><mn>1</mn><mo>+</mo><mfrac><mstyle displaystyle=\"true\"><mfrac bevelled=\"true\"><mi mathvariant=\"normal\">R</mi><mn>2</mn></mfrac></mstyle><mn>100</mn></mfrac></mrow></mfenced><mrow><mn>2</mn><mi mathvariant=\"normal\">n</mi></mrow></msup><mo>-</mo><mn>1</mn><mo>]</mo></math></span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>3750</mn><mo>&nbsp;</mo><mo>[</mo><msup><mfenced><mrow><mo>&nbsp;</mo><mn>1</mn><mo>+</mo><mfrac><mstyle displaystyle=\"true\"><mfrac bevelled=\"true\"><mn>16</mn><mn>2</mn></mfrac></mstyle><mn>100</mn></mfrac></mrow></mfenced><mrow><mn>2</mn><mfenced><mn>1</mn></mfenced></mrow></msup><mo>-</mo><mn>1</mn><mo>]</mo></math></span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>3750</mn><mo>&nbsp;</mo><mo>[</mo><msup><mfenced><mrow><mn>1</mn><mo>+</mo><mfrac><mn>8</mn><mn>100</mn></mfrac></mrow></mfenced><mn>2</mn></msup><mo>-</mo><mn>1</mn><mo>]</mo></math></span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>3750</mn><mo>&nbsp;</mo><mo>[</mo><msup><mfenced><mfrac><mn>27</mn><mn>25</mn></mfrac></mfenced><mn>2</mn></msup><mo>-</mo><mn>1</mn><mo>]</mo></math></span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>3750</mn><mo>&nbsp;</mo><mo>[</mo><mfrac><mrow><mn>104</mn><mo>&nbsp;</mo></mrow><mn>625</mn></mfrac><mo>]</mo></math></span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> = </span><span style=\"font-family: Cambria Math;\">&#8377;</span><span style=\"font-family: Cambria Math;\">624</span></p>\n",
                    solution_hi: "<p>13<span style=\"font-family: Cambria Math;\">.(d) </span><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2358;&#2381;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2309;&#2344;&#2369;&#2360;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\">,</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&#2330;&#2325;&#2381;&#2352;&#2357;&#2371;&#2342;&#2381;&#2343;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2381;&#2351;&#2366;&#2332;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&nbsp;= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">P</mi><mo>&nbsp;</mo><mo>[</mo><mo>&nbsp;</mo><msup><mfenced><mrow><mn>1</mn><mo>+</mo><mfrac><mstyle displaystyle=\"true\"><mfrac bevelled=\"true\"><mi mathvariant=\"normal\">R</mi><mn>2</mn></mfrac></mstyle><mn>100</mn></mfrac></mrow></mfenced><mrow><mn>2</mn><mi mathvariant=\"normal\">n</mi></mrow></msup><mo>-</mo><mn>1</mn><mo>]</mo></math></span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>3750</mn><mo>&nbsp;</mo><mo>[</mo><msup><mfenced><mrow><mo>&nbsp;</mo><mn>1</mn><mo>+</mo><mfrac><mstyle displaystyle=\"true\"><mfrac bevelled=\"true\"><mn>16</mn><mn>2</mn></mfrac></mstyle><mn>100</mn></mfrac></mrow></mfenced><mrow><mn>2</mn><mfenced><mn>1</mn></mfenced></mrow></msup><mo>-</mo><mn>1</mn><mo>]</mo></math></span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>3750</mn><mo>&nbsp;</mo><mo>[</mo><msup><mfenced><mrow><mn>1</mn><mo>+</mo><mfrac><mn>8</mn><mn>100</mn></mfrac></mrow></mfenced><mn>2</mn></msup><mo>-</mo><mn>1</mn><mo>]</mo></math></span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>3750</mn><mo>&nbsp;</mo><mo>[</mo><msup><mfenced><mfrac><mn>27</mn><mn>25</mn></mfrac></mfenced><mn>2</mn></msup><mo>-</mo><mn>1</mn><mo>]</mo></math></span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>3750</mn><mo>&nbsp;</mo><mo>[</mo><mfrac><mrow><mn>104</mn><mo>&nbsp;</mo></mrow><mn>625</mn></mfrac><mo>]</mo></math></span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> = </span><span style=\"font-family: Cambria Math;\">&#8377;</span><span style=\"font-family: Cambria Math;\">624</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "17",
                    question_en: "<p>14<span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\">The simple interest on a sum of money at 10% per annum for 2 years is Rs.8</span><span style=\"font-family: Cambria Math;\">,000</span><span style=\"font-family: Cambria Math;\">. What </span><span style=\"font-family: Cambria Math;\">wiil</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">be</span><span style=\"font-family: Cambria Math;\"> the compound interest (in Rs.) on the same sum for the same period at the same rate compounded annually?</span></p>\n",
                    question_hi: "<p>14<span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\">&#2325;&#2367;&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2343;&#2344;&#2352;&#2366;&#2358;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> 10% </span><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2357;&#2352;&#2381;&#2359;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> 2 </span><span style=\"font-family: Cambria Math;\">&#2357;&#2352;&#2381;&#2359;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2366;&#2343;&#2366;&#2352;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2381;&#2351;&#2366;&#2332;</span><span style=\"font-family: Cambria Math;\"> 8,000 </span><span style=\"font-family: Cambria Math;\">&#2352;&#2369;&#2346;&#2351;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2352;&#2366;&#2358;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2309;&#2357;&#2343;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2366;&#2352;&#2381;&#2359;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2330;&#2325;&#2381;&#2352;&#2357;&#2371;&#2342;&#2381;&#2343;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2381;&#2351;&#2366;&#2332;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2367;&#2340;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2379;&#2327;&#2366;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>8,500</p>\n", "<p>8,400</p>\n", 
                                "<p>8,600</p>\n", "<p>8,200</p>\n"],
                    options_hi: ["<p>8,500</p>\n", "<p>8,400</p>\n",
                                "<p>8,600</p>\n", "<p>8,200</p>\n"],
                    solution_en: "<p>14<span style=\"font-family: Cambria Math;\">.(b) </span><span style=\"font-family: Cambria Math;\">Let principal = P</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">According to</span><span style=\"font-family: Cambria Math;\"> the question,</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">8000 =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>p</mi><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>10</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>2</mn><mo>&nbsp;</mo></mrow><mn>100</mn></mfrac></math></span></p>\r\n<p><span style=\"font-family: Cambria Math;\">P = </span><span style=\"font-family: Cambria Math;\">&#8377;</span><span style=\"font-family: Cambria Math;\">40,000</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Required CI = 40000 </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>[</mo><msup><mrow><mo>(</mo><mn>1</mn><mo>+</mo><mfrac><mn>10</mn><mn>100</mn></mfrac><mo>)</mo></mrow><mn>2</mn></msup><mo>-</mo><mn>1</mn><mo>]</mo></math></p>\r\n<p><span style=\"font-family: Cambria Math;\"> = 40000 [<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mfenced><mfrac><mn>11</mn><mn>10</mn></mfrac></mfenced><mn>2</mn></msup></math></span><span style=\"font-family: Cambria Math;\"> - 1]</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> = 40000 [<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>21</mn><mn>100</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\">]</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> = </span><span style=\"font-family: Cambria Math;\">&#8377;</span><span style=\"font-family: Cambria Math;\">8,400</span></p>\n",
                    solution_hi: "<p>14<span style=\"font-family: Cambria Math;\">.(b) <span style=\"font-weight: 400;\">&#2350;&#2366;&#2344;&#2366; &#2350;&#2370;&#2354;&#2343;&#2344;</span></span><span style=\"font-family: Cambria Math;\">&nbsp;= P</span></p>\r\n<p><span style=\"font-weight: 400;\">&#2346;&#2381;&#2352;&#2358;&#2381;&#2344; &#2325;&#2375; &#2309;&#2344;&#2369;&#2360;&#2366;&#2352;,</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">8000 =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>p</mi><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>10</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>2</mn><mo>&nbsp;</mo></mrow><mn>100</mn></mfrac></math></span></p>\r\n<p><span style=\"font-family: Cambria Math;\">P = </span><span style=\"font-family: Cambria Math;\">&#8377;</span><span style=\"font-family: Cambria Math;\">40,000</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">&#2310;&#2357;&#2358;&#2381;&#2351;&#2325; &#2330;&#2325;&#2381;&#2352;&#2357;&#2371;&#2342;&#2381;&#2343;&#2367; &#2348;&#2381;&#2351;&#2366;&#2332; </span>&nbsp;= 40000 </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>[</mo><msup><mrow><mo>(</mo><mn>1</mn><mo>+</mo><mfrac><mn>10</mn><mn>100</mn></mfrac><mo>)</mo></mrow><mn>2</mn></msup><mo>-</mo><mn>1</mn><mo>]</mo></math></p>\r\n<p><span style=\"font-family: Cambria Math;\"> = 40000 [<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mfenced><mfrac><mn>11</mn><mn>10</mn></mfrac></mfenced><mn>2</mn></msup></math></span><span style=\"font-family: Cambria Math;\"> - 1]</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> = 40000 [<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>21</mn><mn>100</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\">]</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> = </span><span style=\"font-family: Cambria Math;\">&#8377;</span><span style=\"font-family: Cambria Math;\">8,400</span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "misc",
                    question_en: "<p>15<span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\">The difference between the interest compounded annually and the simple interest on a sum of Rs.31</span><span style=\"font-family: Cambria Math;\">,250</span><span style=\"font-family: Cambria Math;\"> for 2 years at 8 % per annum is :</span></p>\n",
                    question_hi: "<p>15<span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\">&#2352;&#2369;</span><span style=\"font-family: Cambria Math;\">.31,250 </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2352;&#2366;&#2358;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> 2 </span><span style=\"font-family: Cambria Math;\">&#2357;&#2352;&#2381;&#2359;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> 8% </span><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2352;&#2381;&#2359;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2366;&#2352;&#2381;&#2359;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2352;&#2370;&#2346;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2306;&#2351;&#2379;&#2332;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2330;&#2325;&#2381;&#2352;&#2357;&#2371;&#2342;&#2381;&#2343;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2381;&#2351;&#2366;&#2332;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2366;&#2343;&#2366;&#2352;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2381;&#2351;&#2366;&#2332;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2368;&#2330;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2309;&#2306;&#2340;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">:</span></p>\n",
                    options_en: ["<p>Rs.200</p>\n", "<p>Rs.220</p>\n", 
                                "<p>Rs.225</p>\n", "<p>Rs.175</p>\n"],
                    options_hi: ["<p>Rs.200</p>\n", "<p>Rs.220</p>\n",
                                "<p>Rs.225</p>\n", "<p>Rs.175</p>\n"],
                    solution_en: "<p>15<span style=\"font-family: Cambria Math;\">.(a) </span><span style=\"font-family: Cambria Math;\">Effective SI rate for 2 years = 2 &times; </span><span style=\"font-family: Cambria Math;\">8 =</span><span style=\"font-family: Cambria Math;\"> 16%</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Effectiv</span><span style=\"font-family: Cambria Math;\">e CI rate for 2 years = 8 + 8 + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>8</mn><mo>&times;</mo><mn>8</mn></mrow><mn>100</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> = 16.64%</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Difference (in %) = 16.64% - 16% = 0.64%</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">So, required difference (in </span><span style=\"font-family: Cambria Math;\">&#8377;</span><span style=\"font-family: Cambria Math;\">) = 0.64% of 31250 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>64</mn><mo>&nbsp;</mo></mrow><mn>10000</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> &times; </span><span style=\"font-family: Cambria Math;\">31250 = </span><span style=\"font-family: Cambria Math;\">&#8377;200</span></p>\n",
                    solution_hi: "<p>15<span style=\"font-family: Cambria Math;\">.(a) </span><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">2 &#2357;&#2352;&#2381;&#2359;&#2379;&#2306; &#2325;&#2375; &#2354;&#2367;&#2319; &#2346;&#2381;&#2352;&#2349;&#2366;&#2357;&#2368; &#2360;&#2366;&#2343;&#2366;&#2352;&#2339; &#2348;&#2381;&#2351;&#2366;&#2332; &#2342;&#2352;&nbsp;</span> = 2 &times; </span><span style=\"font-family: Cambria Math;\">8 =</span><span style=\"font-family: Cambria Math;\"> 16%</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">2 &#2357;&#2352;&#2381;&#2359;&#2379;&#2306; &#2325;&#2375; &#2354;&#2367;&#2319; &#2346;&#2381;&#2352;&#2349;&#2366;&#2357;&#2368; &#2330;&#2325;&#2381;&#2352;&#2357;&#2371;&#2342;&#2381;&#2343;&#2367; &#2348;&#2381;&#2351;&#2366;&#2332; &#2342;&#2352;</span> = 8 + 8 + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>8</mn><mo>&times;</mo><mn>8</mn></mrow><mn>100</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> = 16.64%</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">&#2309;&#2306;&#2340;&#2352; (% &#2350;&#2375;&#2306;)&nbsp; </span>= 16.64% - 16% = 0.64%</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">&#2311;&#2360;&#2354;&#2367;&#2319;, &#2310;&#2357;&#2358;&#2381;&#2351;&#2325; &#2309;&#2306;&#2340;&#2352; (&#8377; &#2350;&#2375;&#2306;) = 31250 &#2325;&#2366; 0.64% </span>&nbsp;= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>64</mn><mo>&nbsp;</mo></mrow><mn>10000</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> &times; </span><span style=\"font-family: Cambria Math;\">31250 = </span><span style=\"font-family: Cambria Math;\">&#8377;200</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>