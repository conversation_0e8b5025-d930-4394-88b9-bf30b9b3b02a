<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">12:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 25</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">25</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 12 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["7"] = {
                name: "English",
                start: 0,
                end: 23
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="7">English</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 24,
                end: 24
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "7",
                    question_en: "1. The given sentence is divided into four segments. Select the option that has the segment with a grammatical error. <br />Jerry driving / home late / when the incident / took place.",
                    question_hi: "1. The given sentence is divided into four segments. Select the option that has the segment with a grammatical error. <br />Jerry driving / home late / when the incident / took place.",
                    options_en: [" took place.", " when the incident", 
                                " Jerry driving", " home late"],
                    options_hi: [" took place.", " when the incident",
                                " Jerry driving", " home late"],
                    solution_en: "1.(c) Jerry driving<br />Helping verb is missing in this sentence. The given sentence is in the past tense, so it must have the helping ‘was’ denoting past continuous tense. Hence, ‘Jerry was driving’ is the most appropriate answer.",
                    solution_hi: "1.(c) Jerry driving<br />इस sentence में helping verb, missing है। दिया गया sentence, past tense में है, इसलिए इसमें helping verb ‘was’ होगी, जो past continuous tense को denote करती है। अतः, ‘Jerry was driving’ सबसे उपयुक्त उत्तर है।",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "7",
                    question_en: "2. Select the INCORRECTLY spelt word.",
                    question_hi: "2. Select the INCORRECTLY spelt word.",
                    options_en: [" Possession", " Unfourseen", 
                                " Referred", " Weather"],
                    options_hi: [" Possession", " Unfourseen",
                                " Referred", " Weather"],
                    solution_en: "2.(b) Unfourseen<br />\'Unforeseen\' is the correct spelling.",
                    solution_hi: "2.(b) Unfourseen<br />\'Unforeseen\' सही spelling है।      ",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "7",
                    question_en: "<p>3. Select the most appropriate synonym of the given word. <br>Evoke</p>",
                    question_hi: "<p>3. Select the most appropriate synonym of the given word. <br>Evoke</p>",
                    options_en: ["<p>Induce</p>", "<p>Abandon</p>", 
                                "<p>Divert</p>", "<p>Reduce</p>"],
                    options_hi: ["<p>Induce</p>", "<p>Abandon</p>",
                                "<p>Divert</p>", "<p>Reduce</p>"],
                    solution_en: "<p>3.(a) <strong>Induce</strong>- to cause something to happen.<br><strong>Evoke</strong>- to bring a feeling, memory, or image to mind.<br><strong>Abandon</strong>- to leave something or someone behind permanently.<br><strong>Divert</strong>- to change the direction or focus of something.<br><strong>Reduce</strong>- to make something smaller or less in amount.</p>",
                    solution_hi: "<p>3.(a) <strong>Induce </strong>(प्रेरित करना)- to cause something to happen.<br><strong>Evoke </strong>(आह्वान करना)- to bring a feeling, memory, or image to mind.<br><strong>Abandon </strong>(परित्याग करना)- to leave something or someone behind permanently.<br><strong>Divert </strong>(ध्यान हटाना)- to change the direction or focus of something.<br><strong>Reduce </strong>(संकुचित करना)- to make something smaller or less in amount.</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "7",
                    question_en: "<p>4. Select the most appropriate synonym of the word in brackets to fill in the blanks.<br>The soldiers spent long period ________ (convalescing).</p>",
                    question_hi: "<p>4. Select the most appropriate synonym of the word in brackets to fill in the blanks.<br>The soldiers spent long period ________ (convalescing).</p>",
                    options_en: ["<p>attacking</p>", "<p>travelling</p>", 
                                "<p>recovering</p>", "<p>ailing</p>"],
                    options_hi: ["<p>attacking</p>", "<p>travelling</p>",
                                "<p>recovering</p>", "<p>ailing</p>"],
                    solution_en: "<p>4.(c) <strong>Recovering</strong>- returning to a normal state of health, mind, or strength.<br><strong>Convalescing</strong>- recovering health and strength after illness.<br><strong>Attacking</strong>- launching an aggressive action against something or someone.<br><strong>Travelling</strong>- going from one place to another, often over a distance.<br><strong>Ailing</strong>- experiencing ill health or discomfort.</p>",
                    solution_hi: "<p>4.(c) <strong>Recovering </strong>(पुनर्प्राप्ति)- returning to a normal state of health, mind, or strength.<br><strong>Convalescing </strong>(स्वास्थ्य ठीक होना)- recovering health and strength after illness.<br><strong>Attacking </strong>(हमला करना)- launching an aggressive action against something or someone.<br><strong>Travelling </strong>(यात्रा करना)- going from one place to another, often over a distance.<br><strong>Ailing </strong>(अस्वस्थ/रुग्ण)- experiencing ill health or discomfort.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "7",
                    question_en: "5. Select the correctly spelled sentence.",
                    question_hi: "5. Select the correctly spelled sentence.",
                    options_en: [" The restaurant serves delicious meals.", " The resturaunt serves delisious meals.", 
                                " The resturant serves deliscious meals.", " The resteraunt serves delicious meals."],
                    options_hi: [" The restaurant serves delicious meals.", " The resturaunt serves delisious meals.",
                                " The resturant serves deliscious meals.", " The resteraunt serves delicious meals."],
                    solution_en: "5.(a) The restaurant serves delicious meals.",
                    solution_hi: "5.(a) The restaurant serves delicious meals.",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "7",
                    question_en: "<p>6. Select the most appropriate ANTONYM of the given word.<br>Intelligible</p>",
                    question_hi: "<p>6. Select the most appropriate ANTONYM of the given word.<br>Intelligible</p>",
                    options_en: ["<p>Scriptable</p>", "<p>Embezzle</p>", 
                                "<p>Incoherent</p>", "<p>Deliberate</p>"],
                    options_hi: ["<p>Scriptable</p>", "<p>Embezzle</p>",
                                "<p>Incoherent</p>", "<p>Deliberate</p>"],
                    solution_en: "<p>6.(c) <strong>Incoherent</strong>- unclear or difficult to understand.<br><strong>Intelligible</strong>- able to be understood.<br><strong>Scriptable</strong>- able to be written or expressed in a script or code.<br><strong>Embezzle</strong>- to secretly take money that belongs to an organization or business you work for.<br><strong>Deliberate</strong>- done consciously and intentionally.</p>",
                    solution_hi: "<p>6.(c) <strong>Incoherent </strong>(असंगत)- unclear or difficult to understand.<br><strong>Intelligible </strong>(बोधगम्य)- able to be understood.<br><strong>Scriptable </strong>(लिपिबद्ध करने योग्य)- able to be written or expressed in a script or code.<br><strong>Embezzle </strong>(गबन करना)- to secretly take money that belongs to an organization or business you work for.<br><strong>Deliberate </strong>(सोचा-समझा)- done consciously and intentionally.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "7",
                    question_en: "7. The following sentence has been divided into four segments. Identify the segment that contains a grammatical error. <br />The lions became violently / when the visitors / started throwing / stones at them.",
                    question_hi: "7. The following sentence has been divided into four segments. Identify the segment that contains a grammatical error. <br />The lions became violently / when the visitors / started throwing / stones at them.",
                    options_en: [" stones at them.", " The lions became violently", 
                                " started throwing", " when the visitors"],
                    options_hi: [" stones at them.", " The lions became violently",
                                " started throwing", " when the visitors"],
                    solution_en: "7.(b) The lions became violently<br />‘Became’ is a stative verb and we generally use an adjective after a stative verb. Therefore, \'violently\' must be replaced with \'violent\'. Hence, \'The lions became violent\' is the most appropriate answer.",
                    solution_hi: "7.(b) The lions became violently<br />‘Became’ एक stative verb  है और हम generally, stative verb के बाद adjective का प्रयोग करते हैं। इसलिए, \'violently\' के स्थान पर \'violent\' का प्रयोग होगा। अतः, \'The lions became violent\' सबसे उपयुक्त उत्तर है।",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "7",
                    question_en: "<p>8. Select the word which means the same as the group of words given.<br>Essence or main point of any passage, lecture or book</p>",
                    question_hi: "<p>8. Select the word which means the same as the group of words given.<br>Essence or main point of any passage, lecture or book</p>",
                    options_en: ["<p>Gist</p>", "<p>Significance</p>", 
                                "<p>Meaning</p>", "<p>Heart</p>"],
                    options_hi: ["<p>Gist</p>", "<p>Significance</p>",
                                "<p>Meaning</p>", "<p>Heart</p>"],
                    solution_en: "<p>8.(a) <strong>Gist</strong>- essence or main point of any passage, lecture or book.</p>",
                    solution_hi: "<p>8.(a) <strong>Gist </strong>(सारांश/भावार्थ)- essence or main point of any passage, lecture or book.</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "7",
                    question_en: "<p>9. Select the most appropriate synonym of the given word.<br>Grotesque</p>",
                    question_hi: "<p>9. Select the most appropriate synonym of the given word.<br>Grotesque</p>",
                    options_en: ["<p>Shameful</p>", "<p>Comely</p>", 
                                "<p>Ugly</p>", "<p>Spontaneous</p>"],
                    options_hi: ["<p>Shameful</p>", "<p>Comely</p>",
                                "<p>Ugly</p>", "<p>Spontaneous</p>"],
                    solution_en: "<p>9.(c) <strong>Ugly</strong><br><strong>Grotesque</strong>- comically or repulsively ugly or distorted.<br><strong>Shameful</strong>- deserving or causing shame or disgrace.<br><strong>Comely</strong>- attractive.<br><strong>Spontaneous</strong>- occurring naturally or without external cause.</p>",
                    solution_hi: "<p>9.(c) <strong>Ugly</strong><br><strong>Grotesque</strong> (विकृत) - comically or repulsively ugly or distorted.<br><strong>Shameful </strong>(शर्मनाक) - deserving or causing shame or disgrace.<br><strong>Comely </strong>(आकर्षक) - attractive.<br><strong>Spontaneous </strong>(स्वाभाविक) - occurring naturally or without external cause.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "7",
                    question_en: "<p>10. Select the most appropriate option that can substitute the underlined segment in the given sentence.<br>Arushya purchased <span style=\"text-decoration: underline;\">the most good</span> of all the suits kept in the shop.</p>",
                    question_hi: "<p>10. Select the most appropriate option that can substitute the underlined segment in the given sentence.<br>Arushya purchased <span style=\"text-decoration: underline;\">the most good</span> of all the suits kept in the shop.</p>",
                    options_en: ["<p>the goods</p>", "<p>the best</p>", 
                                "<p>the more good</p>", "<p>the good</p>"],
                    options_hi: ["<p>the goods</p>", "<p>the best</p>",
                                "<p>the more good</p>", "<p>the good</p>"],
                    solution_en: "<p>10.(b) the best<br>&lsquo;Best&rsquo; is the correct superlative degree of &lsquo;good&rsquo;. Hence, &lsquo;the best&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>10.(b) the best<br>&lsquo;Good&rsquo; की सही superlative degree &lsquo;best&rsquo; है। अतः, &lsquo;the best&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "7",
                    question_en: "<p>11. Select the most appropriate synonym of the given word.<br>Loving</p>",
                    question_hi: "<p>11. Select the most appropriate synonym of the given word.<br>Loving</p>",
                    options_en: ["<p>Subordinate</p>", "<p>Affectionate</p>", 
                                "<p>Rejuvenate</p>", "<p>Offish</p>"],
                    options_hi: ["<p>Subordinate</p>", "<p>Affectionate</p>",
                                "<p>Rejuvenate</p>", "<p>Offish</p>"],
                    solution_en: "<p>11.(b) <strong>Affectionate</strong>- showing fondness or tenderness.<br><strong>Loving</strong>- feeling or showing deep affection or care.<br><strong>Subordinate</strong>- lower in rank or position.<br><strong>Rejuvenate</strong>- to make someone or something look or feel younger, fresher, or more lively.<br><strong>Offish</strong>- distant or unfriendly in manner.</p>",
                    solution_hi: "<p>11.(b) <strong>Affectionate </strong>(स्नेही)- showing fondness or tenderness.<br><strong>Loving </strong>( प्रेमपूर्वक)- feeling or showing deep affection or care.<br><strong>Subordinate </strong>(अधीन)- lower in rank or position.<br><strong>Rejuvenate </strong>(पुनः जीवंत करना)- to make someone or something look or feel younger, fresher, or more lively.<br><strong>Offish </strong>(असामाजिक)- distant or unfriendly in manner.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "7",
                    question_en: "<p>12. Select the option that can be used as a one-word substitute for the underlined group of words.<br>We, as human beings, have a tendency of believing in the <span style=\"text-decoration: underline;\">things that can be seen and touched.</span></p>",
                    question_hi: "<p>12. Select the option that can be used as a one-word substitute for the underlined group of words.<br>We, as human beings, have a tendency of believing in the <span style=\"text-decoration: underline;\">things that can be seen and touched.</span></p>",
                    options_en: ["<p>impalpable</p>", "<p>spiritual</p>", 
                                "<p>inexpressible</p>", "<p>tangible</p>"],
                    options_hi: ["<p>impalpable</p>", "<p>spiritual</p>",
                                "<p>inexpressible</p>", "<p>tangible</p>"],
                    solution_en: "<p>12.(d) <strong>Tangible</strong>- things that can be seen and touched.<br><strong>Impalpable</strong>- unable to be felt by touch.<br><strong>Spiritual</strong>- relating to or affecting the human spirit or soul.<br><strong>Inexpressible</strong>- (of a feeling) too strong to be described or conveyed in words.</p>",
                    solution_hi: "<p>12.(d) <strong>Tangible </strong>(मूर्त)- things that can be seen and touched.<br><strong>Impalpable </strong>(अदृश्य)- unable to be felt by touch.<br><strong>Spiritual </strong>(आध्यात्मिक)- relating to or affecting the human spirit or soul.<br><strong>Inexpressible </strong>(अवर्णनीय)- (of a feeling) too strong to be described or conveyed in words.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "7",
                    question_en: "<p>13. Select the most appropriate idiom to fill in the blank.<br>After months of dedication and effort, I finally completed the marathon. I can really _______________ for this<br>accomplishment.</p>",
                    question_hi: "<p>13. Select the most appropriate idiom to fill in the blank.<br>After months of dedication and effort, I finally completed the marathon. I can really _______________ for this<br>accomplishment.</p>",
                    options_en: ["<p>eat like a horse</p>", "<p>up a creek without a paddle</p>", 
                                "<p>blow hot and cold</p>", "<p>pat myself on the back</p>"],
                    options_hi: ["<p>eat like a horse</p>", "<p>up a creek without a paddle</p>",
                                "<p>blow hot and cold</p>", "<p>pat myself on the back</p>"],
                    solution_en: "<p>13.(d) <strong>Pat myself on the back</strong>- to praise myself for doing something good.</p>",
                    solution_hi: "<p>13.(d) <strong>Pat myself on the back</strong>- to praise myself for doing something good./कुछ अच्छा करने पर स्वयं की प्रशंसा करना।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "7",
                    question_en: "<p>14. Select the most appropriate meaning of the underlined idiom in the following sentence.<br>The leader of the Legislative Assembly won the hearts of the members through his maiden speech during the session in state council.</p>",
                    question_hi: "<p>14. Select the most appropriate meaning of the underlined idiom in the following sentence.<br>The leader of the Legislative Assembly won the hearts of the members through his maiden speech during the session in state council.</p>",
                    options_en: ["<p>final speech</p>", "<p>closure speech</p>", 
                                "<p>first speech</p>", "<p>logical speech</p>"],
                    options_hi: ["<p>final speech</p>", "<p>closure speech</p>",
                                "<p>first speech</p>", "<p>logical speech</p>"],
                    solution_en: "<p>14.(c) <strong>Maiden speech</strong>- first speech.</p>",
                    solution_hi: "<p>14.(c) <strong>Maiden speech</strong> - first speech./प्रथम भाषण।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "7",
                    question_en: "<p>15. Select the most appropriate option that can substitute the underlined segment in the given sentence.<br>Even if you&rsquo;ve never heard his name, you&rsquo;ve probably met someone with his words <span style=\"text-decoration: underline;\">tattooed in their skin</span> or heard them sung at a music concert.</p>",
                    question_hi: "<p>15. Select the most appropriate option that can substitute the underlined segment in the given sentence.<br>Even if you&rsquo;ve never heard his name, you&rsquo;ve probably met someone with his words <span style=\"text-decoration: underline;\">tattooed in their skin</span> or heard them sung at a music concert.</p>",
                    options_en: ["<p>tattooed through their skin</p>", "<p>tattooed until their skin</p>", 
                                "<p>tattooed overdoing their skin</p>", "<p>tattooed on their skin</p>"],
                    options_hi: ["<p>tattooed through their skin</p>", "<p>tattooed until their skin</p>",
                                "<p>tattooed overdoing their skin</p>", "<p>tattooed on their skin</p>"],
                    solution_en: "<p>15.(d) tattooed on their skin<br>&lsquo;On&rsquo; is used to indicate the surface in contact with the object. Similarly, the tattoo lies on the surface of the skin. Hence, &lsquo;tattooed on their skin&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>15.(d) tattooed on their skin<br>&lsquo;On&rsquo; का प्रयोग वस्तु के संपर्क में आने वाली surface को indicate करने के लिए किया जाता है। इसी प्रकार, tattoo त्वचा की सतह पर होता है। अतः, &lsquo;tattooed on their skin&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "7",
                    question_en: "16. Parts of a sentence are given below in jumbled order. Arrange the parts in the correct order to form a meaningful sentence.<br />A. at the same time giving happiness to the poor<br />B. by providing them with food, clothes, money or grain<br />C. festivals in India are celebrated to share happiness<br />D. by being in the presence of the members of the family and",
                    question_hi: "16. Parts of a sentence are given below in jumbled order. Arrange the parts in the correct order to form a meaningful sentence.<br />A. at the same time giving happiness to the poor<br />B. by providing them with food, clothes, money or grain<br />C. festivals in India are celebrated to share happiness<br />D. by being in the presence of the members of the family and",
                    options_en: [" ACBD", " CBAD", 
                                " CABD", " CDAB"],
                    options_hi: [" ACBD", " CBAD",
                                " CABD", " CDAB"],
                    solution_en: "16.(d) CDAB<br />The given sentence starts with Part C as it introduces the main idea of the sentence, i.e. ‘Festivals in India are celebrated to share happiness’. Part C will be followed by Part D as it states that festivals are celebrated in the presence of the members of the family. Further, Part A talks about simultaneously giving happiness to the poor & Part B talks about providing the poor with food, clothes, money or grain. So, B will follow A. Going through the options, option ‘d’ has the correct sequence.",
                    solution_hi: "16.(d) CDAB<br />दिया गया sentence, Part C से प्रारम्भ होगा क्योंकि यह sentence के मुख्य विचार ‘Festivals in India are celebrated to share happiness’ का परिचय देता है। Part C के बाद Part D आएगा क्योंकि यह बताता है कि festivals परिवार के सदस्यों की उपस्थिति में मनाए जाते हैं। इसके अलावा, Part A गरीबों को एक साथ खुशियाँ देने की बात करता है और Part B गरीबों को food, clothes, money या grain उपलब्ध कराने की बात करता है। इसलिए, A के बाद B आएगा। अतः options के माध्यम से जाने पर option ‘d’  में सही sequence है।",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "7",
                    question_en: "<p>17. Select the option that expresses the given sentence in passive voice.<br>The chef prepares the meal with great care.</p>",
                    question_hi: "<p>17. Select the option that expresses the given sentence in passive voice.<br>The chef prepares the meal with great care.</p>",
                    options_en: ["<p>The meal was prepared with great care by the chef.</p>", "<p>The meal will be prepared with great care by the chef.</p>", 
                                "<p>The meal is prepared with great care by the chef.</p>", "<p>The meal has been prepared with great care by the chef.</p>"],
                    options_hi: ["<p>The meal was prepared with great care by the chef.</p>", "<p>The meal will be prepared with great care by the chef.</p>",
                                "<p>The meal is prepared with great care by the chef.</p>", "<p>The meal has been prepared with great care by the chef.</p>"],
                    solution_en: "<p>17.(c) The meal is prepared with great care by the chef. (Correct)<br>(a) The meal <span style=\"text-decoration: underline;\">was prepared</span> with great care by the chef. (Incorrect Tense)<br>(b) The meal <span style=\"text-decoration: underline;\">will be prepared</span> with great care by the chef. (Incorrect Tense)<br>(d) The meal <span style=\"text-decoration: underline;\">has been</span> prepared with great care by the chef. (Incorrect Helping Verb)</p>",
                    solution_hi: "<p>17.(c) The meal is prepared with great care by the chef. (Correct)<br>(a) The meal <span style=\"text-decoration: underline;\">was prepared</span> with great care by the chef. (गलत Tense)<br>(b) The meal <span style=\"text-decoration: underline;\">will be prepared</span> with great care by the chef. (गलत Tense)<br>(d) The meal <span style=\"text-decoration: underline;\">has been</span> prepared with great care by the chef. (गलत Helping Verb)</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "7",
                    question_en: "<p>18. Select the most appropriate synonym of the given word.<br>Necessary</p>",
                    question_hi: "<p>18. Select the most appropriate synonym of the given word.<br>Necessary</p>",
                    options_en: ["<p>Potential</p>", "<p>Essential</p>", 
                                "<p>Existential</p>", "<p>Spatial</p>"],
                    options_hi: ["<p>Potential</p>", "<p>Essential</p>",
                                "<p>Existential</p>", "<p>Spatial</p>"],
                    solution_en: "<p>18.(b) <strong>Essential</strong>- absolutely necessary or extremely important.<br><strong>Necessary</strong>- required or needed for a particular purpose.<br><strong>Potential</strong>- having the capacity to develop into something in the future.<br><strong>Existential</strong>- relating to existence or the nature of being.<br><strong>Spatial</strong>- relating to space and the position, size, or shape of things in it .</p>",
                    solution_hi: "<p>18.(b) <strong>Essential </strong>(अनिवार्य)- absolutely necessary or extremely important.<br><strong>Necessary </strong>(जरूरी)- required or needed for a particular purpose.<br><strong>Potential </strong>(संभावित)- having the capacity to develop into something in the future.<br><strong>Existential </strong>(अस्तित्ववान)- relating to existence or the nature of being.<br><strong>Spatial </strong>(स्थानिक)- relating to space and the position, size, or shape of things in it .</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "7",
                    question_en: "<p>19. Select the option that expresses the following sentence in active voice.<br>The novels of Dickens are being read by Mayank.</p>",
                    question_hi: "<p>19. Select the option that expresses the following sentence in active voice.<br>The novels of Dickens are being read by Mayank.</p>",
                    options_en: ["<p>Mayank is reading the novels of Dickens.</p>", "<p>Mayank has been reading the novels of Dickens.</p>", 
                                "<p>Mayank was reading the novels of Dickens.</p>", "<p>Mayank had been reading the novels of Dickens.</p>"],
                    options_hi: ["<p>Mayank is reading the novels of Dickens.</p>", "<p>Mayank has been reading the novels of Dickens.</p>",
                                "<p>Mayank was reading the novels of Dickens.</p>", "<p>Mayank had been reading the novels of Dickens.</p>"],
                    solution_en: "<p>19.(a) Mayank is reading the novels of Dickens. (Correct)<br>(b) Mayank <span style=\"text-decoration: underline;\">has been</span> reading the novels of Dickens. (Incorrect Helping Verb)<br>(c) Mayank <span style=\"text-decoration: underline;\">was reading</span> the novels of Dickens. (Incorrect Tense)<br>(d) Mayank <span style=\"text-decoration: underline;\">had been reading</span> the novels of Dickens. (Incorrect Tense)</p>",
                    solution_hi: "<p>19.(a) Mayank is reading the novels of Dickens. (Correct)<br>(b) Mayank <span style=\"text-decoration: underline;\">has been</span> reading the novels of Dickens. (गलत Helping Verb)<br>(c) Mayank <span style=\"text-decoration: underline;\">was reading</span> the novels of Dickens. (गलत Tense)<br>(d) Mayank <span style=\"text-decoration: underline;\">had been reading</span> the novels of Dickens. (गलत Tense)</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "7",
                    question_en: "<p>20. Select the option that can be used as a one-word substitute for the given group of words.<br>Fear of snakes</p>",
                    question_hi: "<p>20. Select the option that can be used as a one-word substitute for the given group of words.<br>Fear of snakes</p>",
                    options_en: ["<p>Claustrophobia</p>", "<p>Syngenesophobia</p>", 
                                "<p>Xenophobia</p>", "<p>Ophidiophobia</p>"],
                    options_hi: ["<p>Claustrophobia</p>", "<p>Syngenesophobia</p>",
                                "<p>Xenophobia</p>", "<p>Ophidiophobia</p>"],
                    solution_en: "<p>20.(d) <strong>Ophidiophobia</strong>- fear of snakes.<br><strong>Claustrophobia</strong>- fear of closed spaces.<br><strong>Syngenesophobia</strong>- fear of relatives.<br><strong>Xenophobia</strong>- fear of strangers or foreigners.</p>",
                    solution_hi: "<p>20.(d) <strong>Ophidiophobia </strong>(सर्प-भय) - fear of snakes.<br><strong>Claustrophobia </strong>(संकीर्ण स्थानों का भय) - fear of closed spaces.<br><strong>Syngenesophobia </strong>(संबंधियों से भय) - fear of relatives.<br><strong>Xenophobia </strong>(विदेशियों से भय) - fear of strangers or foreigners.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "7",
                    question_en: "<p>21.<strong>Cloze test:</strong><br>In the following passage, some words have been deleted. Read the passage carefully and select the most appropriate option, in the<br>questions that follow, to fill in each blank.<br>Listening starts with hearing but goes (21)___________. Hearing, in other words, is necessary, but is not a sufficient (22)___________ for listening. Listening involves hearing with attention. Listening is a process that (23)___________ concentration. While listening, one should also be observant. In other words, listening has to do with the ears, (24)____________ with the eyes and the mind. Listening is to be understood as the total process that involves hearing with attention, being observant and making interpretations. Good communication is essentially a/an (25)___________ process.<br>Select the most appropriate option to fill in blank number 21.</p>",
                    question_hi: "<p>21. <strong>Cloze test:</strong><br>In the following passage, some words have been deleted. Read the passage carefully and select the most appropriate option, in the<br>questions that follow, to fill in each blank.<br>Listening starts with hearing but goes (21)___________. Hearing, in other words, is necessary, but is not a sufficient (22)___________ for listening. Listening involves hearing with attention. Listening is a process that (23)___________ concentration. While listening, one should also be observant. In other words, listening has to do with the ears, (24)____________ with the eyes and the mind. Listening is to be understood as the total process that involves hearing with attention, being observant and making interpretations. Good communication is essentially a/an (25)___________ process.<br>Select the most appropriate option to fill in blank number 21.</p>",
                    options_en: ["<p>towards</p>", "<p>across</p>", 
                                "<p>beyond</p>", "<p>throughout</p>"],
                    options_hi: ["<p>towards</p>", "<p>across</p>",
                                "<p>beyond</p>", "<p>throughout</p>"],
                    solution_en: "<p>21.(c) beyond<br>The phrase &lsquo;go beyond&rsquo; means to be more than something. The given passage states that listening starts with hearing but goes beyond. Hence, \'beyond\' is the most appropriate answer.</p>",
                    solution_hi: "<p>21.(c) beyond<br>Phrase &lsquo;go beyond&rsquo; का अर्थ है किसी चीज़ से अधिक होना। दिए गए passage में कहा गया है कि listening, की hearing से शुरू होती है, लेकिन इससे परे जाती है। अतः, \'beyond\' सबसे उपयुक्त उत्तर है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "7",
                    question_en: "<p>22. <strong>Cloze test:</strong><br>In the following passage, some words have been deleted. Read the passage carefully and select the most appropriate option, in the<br>questions that follow, to fill in each blank.<br>Listening starts with hearing but goes (21)___________. Hearing, in other words, is necessary, but is not a sufficient (22)___________ for listening. Listening involves hearing with attention. Listening is a process that (23)___________ concentration. While listening, one should also be observant. In other words, listening has to do with the ears, (24)____________ with the eyes and the mind. Listening is to be understood as the total process that involves hearing with attention, being observant and making interpretations. Good communication is essentially a/an (25)___________ process.<br>Select the most appropriate option to fill in blank number 22.</p>",
                    question_hi: "<p>22. <strong>Cloze test:</strong><br>In the following passage, some words have been deleted. Read the passage carefully and select the most appropriate option, in the<br>questions that follow, to fill in each blank.<br>Listening starts with hearing but goes (21)___________. Hearing, in other words, is necessary, but is not a sufficient (22)___________ for listening. Listening involves hearing with attention. Listening is a process that (23)___________ concentration. While listening, one should also be observant. In other words, listening has to do with the ears, (24)____________ with the eyes and the mind. Listening is to be understood as the total process that involves hearing with attention, being observant and making interpretations. Good communication is essentially a/an (25)___________ process.<br>Select the most appropriate option to fill in blank number 22.</p>",
                    options_en: ["<p>place</p>", "<p>decorum</p>", 
                                "<p>condition</p>", "<p>scenario</p>"],
                    options_hi: ["<p>place</p>", "<p>decorum</p>",
                                "<p>condition</p>", "<p>scenario</p>"],
                    solution_en: "<p>22.(c) condition<br>&lsquo;Condition&rsquo; means something that must exist before something else can happen. The given passage states that hearing, in other words, is necessary, but not a sufficient condition for listening. Hence, \'condition\' is the most appropriate answer.</p>",
                    solution_hi: "<p>22.(c) condition<br>&lsquo;Condition&rsquo; का अर्थ है किसी अन्य चीज़ के घटित होने से पहले उसका होना आवश्यक। दिए गए passage में कहा गया है कि hearing, दूसरे शब्दों में necessary है, लेकिन listening के लिए sufficient condition नहीं है। अतः, \'condition\' सबसे उपयुक्त उत्तर है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "23",
                    section: "7",
                    question_en: "<p>23. <strong>Cloze test:</strong><br>In the following passage, some words have been deleted. Read the passage carefully and select the most appropriate option, in the<br>questions that follow, to fill in each blank.<br>Listening starts with hearing but goes (21)___________. Hearing, in other words, is necessary, but is not a sufficient (22)___________ for listening. Listening involves hearing with attention. Listening is a process that (23)___________ concentration. While listening, one should also be observant. In other words, listening has to do with the ears, (24)____________ with the eyes and the mind. Listening is to be understood as the total process that involves hearing with attention, being observant and making interpretations. Good communication is essentially a/an (25)___________ process.<br>Select the most appropriate option to fill in blank number 23.</p>",
                    question_hi: "<p>23. <strong>Cloze test:</strong><br>In the following passage, some words have been deleted. Read the passage carefully and select the most appropriate option, in the<br>questions that follow, to fill in each blank.<br>Listening starts with hearing but goes (21)___________. Hearing, in other words, is necessary, but is not a sufficient (22)___________ for listening. Listening involves hearing with attention. Listening is a process that (23)___________ concentration. While listening, one should also be observant. In other words, listening has to do with the ears, (24)____________ with the eyes and the mind. Listening is to be understood as the total process that involves hearing with attention, being observant and making interpretations. Good communication is essentially a/an (25)___________ process.<br>Select the most appropriate option to fill in blank number 23.</p>",
                    options_en: ["<p>calls for</p>", "<p>disposes of</p>", 
                                "<p>takes for</p>", "<p>brings up</p>"],
                    options_hi: ["<p>calls for</p>", "<p>disposes of</p>",
                                "<p>takes for</p>", "<p>brings up</p>"],
                    solution_en: "<p>23.(a) calls for<br>&lsquo;Call for&rsquo; means to demand something. The given passage states that listening is a process that demands concentration. Hence, \'calls for\' is the most appropriate answer.</p>",
                    solution_hi: "<p>23.(a) calls for<br>&lsquo;Call for&rsquo; का अर्थ है किसी चीज़ की माँग करना। दिए गए passage में कहा गया है कि listening एक ऐसी प्रक्रिया है जिसमें concentration की आवश्यकता होती है। अतः, \'calls for\' सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "24",
                    section: "7",
                    question_en: "<p>24. <strong>Cloze test:</strong><br>In the following passage, some words have been deleted. Read the passage carefully and select the most appropriate option, in the<br>questions that follow, to fill in each blank.<br>Listening starts with hearing but goes (21)___________. Hearing, in other words, is necessary, but is not a sufficient (22)___________ for listening. Listening involves hearing with attention. Listening is a process that (23)___________ concentration. While listening, one should also be observant. In other words, listening has to do with the ears, (24)____________ with the eyes and the mind. Listening is to be understood as the total process that involves hearing with attention, being observant and making interpretations. Good communication is essentially a/an (25)___________ process.<br>Select the most appropriate option to fill in blank number 24.</p>",
                    question_hi: "<p>24. <strong>Cloze test:</strong><br>In the following passage, some words have been deleted. Read the passage carefully and select the most appropriate option, in the<br>questions that follow, to fill in each blank.<br>Listening starts with hearing but goes (21)___________. Hearing, in other words, is necessary, but is not a sufficient (22)___________ for listening. Listening involves hearing with attention. Listening is a process that (23)___________ concentration. While listening, one should also be observant. In other words, listening has to do with the ears, (24)____________ with the eyes and the mind. Listening is to be understood as the total process that involves hearing with attention, being observant and making interpretations. Good communication is essentially a/an (25)___________ process.<br>Select the most appropriate option to fill in blank number 24.</p>",
                    options_en: ["<p>although</p>", "<p>rather</p>", 
                                "<p>as well as</p>", "<p>yet</p>"],
                    options_hi: ["<p>although</p>", "<p>rather</p>",
                                "<p>as well as</p>", "<p>yet</p>"],
                    solution_en: "<p>24.(c) as well as<br>&lsquo;As well as&rsquo; is used to mention another item connected with the subject we are discussing. The given passage states that listening has to do with ears, and is also connected with the eyes and the mind. Hence, \'as well as\' is the most appropriate answer.</p>",
                    solution_hi: "<p>24.(c) as well as<br>&lsquo;As well as&rsquo; का प्रयोग किसी subject से जुड़ी अन्य चीज का उल्लेख करने के लिए किया जाता है जिस पर हम चर्चा कर रहे हैं। दिए गए passage में कहा गया है कि listening का संबंध ears से है, और यह eyes और mind से भी जुड़ा हुई है। अतः, \'as well as\' सबसे उपयुक्त उत्तर है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "25",
                    section: "misc",
                    question_en: "<p>25. <strong>Cloze test:</strong><br>In the following passage, some words have been deleted. Read the passage carefully and select the most appropriate option, in the<br>questions that follow, to fill in each blank.<br>Listening starts with hearing but goes (21)___________. Hearing, in other words, is necessary, but is not a sufficient (22)___________ for listening. Listening involves hearing with attention. Listening is a process that (23)___________ concentration. While listening, one should also be observant. In other words, listening has to do with the ears, (24)____________ with the eyes and the mind. Listening is to be understood as the total process that involves hearing with attention, being observant and making interpretations. Good communication is essentially a/an (25)___________ process.<br>Select the most appropriate option to fill in blank number 25.</p>",
                    question_hi: "<p>25. <strong>Cloze test:</strong><br>In the following passage, some words have been deleted. Read the passage carefully and select the most appropriate option, in the<br>questions that follow, to fill in each blank.<br>Listening starts with hearing but goes (21)___________. Hearing, in other words, is necessary, but is not a sufficient (22)___________ for listening. Listening involves hearing with attention. Listening is a process that (23)___________ concentration. While listening, one should also be observant. In other words, listening has to do with the ears, (24)____________ with the eyes and the mind. Listening is to be understood as the total process that involves hearing with attention, being observant and making interpretations. Good communication is essentially a/an (25)___________ process.<br>Select the most appropriate option to fill in blank number 25.</p>",
                    options_en: ["<p>onerous</p>", "<p>obnoxious</p>", 
                                "<p>interactive</p>", "<p>pecuniary</p>"],
                    options_hi: ["<p>onerous</p>", "<p>obnoxious</p>",
                                "<p>interactive</p>", "<p>pecuniary</p>"],
                    solution_en: "<p>25.(c) interactive<br>&lsquo;Interactive&rsquo; means &lsquo;involving communication between people&rsquo;. The given passage states that good communication is essentially an interactive process. Hence, \'interactive\' is the most appropriate answer.</p>",
                    solution_hi: "<p>25.(c) interactive<br>&lsquo;Interactive&rsquo; का अर्थ है लोगों के बीच संचार। दिए गए passage में कहा गया है कि good communication अनिवार्य रूप से एक संवादात्मक (interactive) प्रक्रिया है। अतः, \'interactive\' सबसे उपयुक्त उत्तर है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>