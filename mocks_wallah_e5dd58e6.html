<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">15:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 15</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">15</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 15 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 0,
                end: 13
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 14,
                end: 14
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "17",
                    question_en: "<p>1. If the income of P is 40% less than Q, then the combined income of P and Q is how much percent more than the income of P?</p>",
                    question_hi: "<p>1. यदि P की आय Q से 40% कम है, तो P और Q की संयुक्त आय, P की आय से कितने प्रतिशत अधिक है?</p>",
                    options_en: ["<p>166<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>", "<p>166<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>", 
                                "<p>164<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>", "<p>164<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>"],
                    options_hi: ["<p>166<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>", "<p>166<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>",
                                "<p>164<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>", "<p>164<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>"],
                    solution_en: "<p>1.(b) <br>40% = <math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math><br>Income - P : Q = 3 : 5<br>Hence, required % = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>3</mn><mo>+</mo><mn>5</mn><mo>)</mo><mo>-</mo><mn>3</mn></mrow><mn>3</mn></mfrac></math> &times; 100 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>500</mn><mn>3</mn></mfrac></math> = 166<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>3</mn></mfrac></math>%</p>",
                    solution_hi: "<p>1.(b) <br>40% = <math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math><br>आय - P : Q = 3 : 5<br>अतः, आवश्यक % = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>3</mn><mo>+</mo><mn>5</mn><mo>)</mo><mo>-</mo><mn>3</mn></mrow><mn>3</mn></mfrac></math> &times; 100 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>500</mn><mn>3</mn></mfrac></math> = 166<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>3</mn></mfrac></math>%</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "17",
                    question_en: "<p>2. 12.75% is equivalent to _________.</p>",
                    question_hi: "<p>2. 12.75% _________ के समतुल्य है।</p>",
                    options_en: ["<p><math display=\"inline\"><mfrac><mrow><mn>41</mn></mrow><mrow><mn>400</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>17</mn></mrow><mrow><mn>200</mn></mrow></mfrac></math></p>", 
                                "<p><math display=\"inline\"><mfrac><mrow><mn>51</mn></mrow><mrow><mn>400</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>37</mn></mrow><mrow><mn>400</mn></mrow></mfrac></math></p>"],
                    options_hi: ["<p><math display=\"inline\"><mfrac><mrow><mn>41</mn></mrow><mrow><mn>400</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>17</mn></mrow><mrow><mn>200</mn></mrow></mfrac></math></p>",
                                "<p><math display=\"inline\"><mfrac><mrow><mn>51</mn></mrow><mrow><mn>400</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>37</mn></mrow><mrow><mn>400</mn></mrow></mfrac></math></p>"],
                    solution_en: "<p>2.(c) <br>12.75% = <math display=\"inline\"><mfrac><mrow><mn>1275</mn></mrow><mrow><mn>10000</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>51</mn><mn>400</mn></mfrac></math></p>",
                    solution_hi: "<p>2.(c) <br>12.75% = <math display=\"inline\"><mfrac><mrow><mn>1275</mn></mrow><mrow><mn>10000</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>51</mn><mn>400</mn></mfrac></math></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "17",
                    question_en: "<p>3. In an election between two candidates, one got 60% of the total valid votes and 15% of the votes were invalid. If the total number of votes were 8400, the number of valid votes that the other candidate got were:</p>",
                    question_hi: "<p>3. दो उम्मीदवारों के बीच एक चुनाव में, एक को कुल वैध मतों का 60% प्राप्त हुआ और 15% मत अवैध थे। यदि मतों की कुल संख्या 8400 थी, तो दूसरे उम्मीदवार को मिले वैध मतों की संख्या कितनी थी?</p>",
                    options_en: ["<p>3213</p>", "<p>2856</p>", 
                                "<p>3117</p>", "<p>2998</p>"],
                    options_hi: ["<p>3213</p>", "<p>2856</p>",
                                "<p>3117</p>", "<p>2998</p>"],
                    solution_en: "<p>3.(b)<br>Let total valid votes be x<br>According to the question,<br>x = 8400 &times; <math display=\"inline\"><mfrac><mrow><mn>85</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 7140<br>Now, valid votes got by other candidate = 7140 &times; <math display=\"inline\"><mfrac><mrow><mn>40</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 2856</p>",
                    solution_hi: "<p>3.(b)<br>माना कि कुल वैध वोट x हैं<br>प्रश्न के अनुसार,<br>x = 8400 &times; <math display=\"inline\"><mfrac><mrow><mn>85</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 7140<br>अब, दूसरे उम्मीदवार को मिले वैध वोट = 7140 &times; <math display=\"inline\"><mfrac><mrow><mn>40</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 2856</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "17",
                    question_en: "<p>4. In an engineering college, there are four branches namely Computer Science (CS), Electronics (EC), Civil (CE), and Mechanical (ME). The respective ratio of the number of students in these branches is 11 : 5 : 6 : 7. What is the percentage of students belonging to CS and ME branches in the college (correct up to two decimal places)?</p>",
                    question_hi: "<p>4. एक इंजीनियरिंग कॉलेज में, कंप्यूटर साइंस (CS), इलेक्ट्रॉनिक्स (EC), सिविल (CE) और मैकेनिकल (ME) नाम की चार शाखाएँ हैं। इन शाखाओं में छात्रों की संख्या का क्रमशः अनुपात 11 : 5 : 6 : 7 है। कॉलेज में CS और ME शाखाओं के छात्रों का प्रतिशत क्या है (दशमलव के दो स्थानों तक सही) ?</p>",
                    options_en: ["<p>62.37%</p>", "<p>62.07%</p>", 
                                "<p>61.37%</p>", "<p>61.07%</p>"],
                    options_hi: ["<p>62.37%</p>", "<p>62.07%</p>",
                                "<p>61.37%</p>", "<p>61.07%</p>"],
                    solution_en: "<p>4.(b)<br>Total students in all branches = (11x&nbsp;+ 5x + 6x + 7x) = 29x<br>Number of students in CS and ME branches = (11x&nbsp;+ 7x) = 18x<br>Hence, required % = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>18</mn><mi mathvariant=\"normal\">x</mi></mrow><mrow><mn>29</mn><mi mathvariant=\"normal\">x</mi></mrow></mfrac></math> &times; 100 = 62.07%</p>",
                    solution_hi: "<p>4.(b)<br>सभी शाखाओं में कुल छात्र = (11x&nbsp;+ 5x + 6x + 7x) = 29x<br>कंप्यूटर साइंस (CS) और मैकेनिकल (ME) शाखाओं में छात्रों की संख्या = (11x&nbsp;+ 7x) = 18x<br>अतः, आवश्यक % = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>18</mn><mi mathvariant=\"normal\">x</mi></mrow><mrow><mn>29</mn><mi mathvariant=\"normal\">x</mi></mrow></mfrac></math>&nbsp;&times; 100 = 62.07%</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "17",
                    question_en: "<p>5. A person spends 10% of his income on groceries, 10% on medicines, 20% on children&rsquo;s education, 15% on house rent, and he saves the remaining amount. If his monthly income is ₹30,000, find his savings.</p>",
                    question_hi: "<p>5. एक व्यक्ति अपनी आय का 10% किराने के सामान पर, 10% दवाओं पर, 20% बच्चों की शिक्षा पर, 15% घर के किराए पर खर्च करता है, और वह शेष राशि बचाता है। यदि उसकी मासिक आय ₹30,000 है, तो उसकी बचत ज्ञात कीजिए।</p>",
                    options_en: ["<p>₹13,500</p>", "<p>₹16,500</p>", 
                                "<p>₹12,500</p>", "<p>₹15,500</p>"],
                    options_hi: ["<p>₹13,500</p>", "<p>₹16,500</p>",
                                "<p>₹12,500</p>", "<p>₹15,500</p>"],
                    solution_en: "<p>5.(a)<br>Expenditure of the man = 10% + 10% + 20% + 15% = 55%<br>Saving of the man = 100% - 55% = 45%<br>(income) 100% = 30000<br>(saving) 45% = <math display=\"inline\"><mfrac><mrow><mn>30000</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> &times; 45 = 13,500</p>",
                    solution_hi: "<p>5.(a)<br>आदमी का व्यय = 10% + 10% + 20% + 15% = 55%<br>आदमी की बचत = 100% - 55% = 45%<br>(आय) 100% = 30000<br>(बचत) 45% = <math display=\"inline\"><mfrac><mrow><mn>30000</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> &times; 45 = 13,500</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "17",
                    question_en: "<p>6. In a state election between two parties, 83% of the voters cast their votes, out of which 4% of the votes were declared invalid. Party A got 156611 votes which were 83% of the total valid votes. Find the total number of votes enrolled in that election. (Consider integer part only)</p>",
                    question_hi: "<p>6. दो दलों के बीच एक राज्य के चुनाव में, 83% मतदाताओं ने अपने मत डाले, जिनमें से 4% मतों को अवैध घोषित कर दिया गया। दल A को 156611 मत मिले जो कुल वैध मतों का 83% था। उस चुनाव में नामांकित मतों की कुल संख्या ज्ञात कीजिए (केवल पूर्णांक भाग पर विचार कीजिए)</p>",
                    options_en: ["<p>23,46,807</p>", "<p>2,36,807</p>", 
                                "<p>2,34,888</p>", "<p>2,58,233</p>"],
                    options_hi: ["<p>23,46,807</p>", "<p>2,36,807</p>",
                                "<p>2,34,888</p>", "<p>2,58,233</p>"],
                    solution_en: "<p>6.(b)<br>Total number of enrolled votes = 100x<br>Number of cast votes = 100x&nbsp;&times; 83% = 83x<br>Total number of valid votes = 83x&nbsp;&times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>96</mn><mn>100</mn></mfrac></math><br>Number of votes got by party A = 83x &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>96</mn><mn>100</mn></mfrac></math>&times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>83</mn><mn>100</mn></mfrac></math><br>83x &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>96</mn><mn>100</mn></mfrac></math>&times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>83</mn><mn>100</mn></mfrac></math> = 156611<br>Number of enrolled votes (100<math display=\"inline\"><mi>x</mi></math>) = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>156611</mn><mo>&#215;</mo><mn>100</mn><mo>&#215;</mo><mn>100</mn></mrow><mrow><mn>83</mn><mo>&#215;</mo><mn>96</mn><mo>&#215;</mo><mn>83</mn></mrow></mfrac></math> = 2,36,807 (approx)</p>",
                    solution_hi: "<p>6.(b)<br>नामांकित वोटों की कुल संख्या = 100x<br>डाले गए वोटों की संख्या = 100<math display=\"inline\"><mi>x</mi></math> &times; 83% = 83x<br>वैध मतों की कुल संख्या = 83x&nbsp;&times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>96</mn><mn>100</mn></mfrac></math><br>पार्टी A को मिले वोटों की संख्या = 83x &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>96</mn><mn>100</mn></mfrac></math>&times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>83</mn><mn>100</mn></mfrac></math><br>83x &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>96</mn><mn>100</mn></mfrac></math>&times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>83</mn><mn>100</mn></mfrac></math> = 156611<br>नामांकित मतों की संख्या (100x) = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>156611</mn><mo>&#215;</mo><mn>100</mn><mo>&#215;</mo><mn>100</mn></mrow><mrow><mn>83</mn><mo>&#215;</mo><mn>96</mn><mo>&#215;</mo><mn>83</mn></mrow></mfrac></math> = 2,36,807 (लगभग)</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "17",
                    question_en: "<p>7. Rahul spends 70% of his income. His income increased by 15% and his expenditure increased by 7.5%. What is the percentage increase in his savings?</p>",
                    question_hi: "<p>7. राहुल अपनी आय का 70% खर्च करता है। उसकी आय में 15% की वृद्धि हुई और उसके व्यय में 7.5% की वृद्धि हुई। उसकी बचत में प्रतिशत वृद्धि कितनी है?</p>",
                    options_en: ["<p>32.5%</p>", "<p>25.5%</p>", 
                                "<p>50%</p>", "<p>30.5%</p>"],
                    options_hi: ["<p>32.5%</p>", "<p>25.5%</p>",
                                "<p>50%</p>", "<p>30.5%</p>"],
                    solution_en: "<p>7.(a) <br>Ratio -&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; before&nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; after<br>Income -&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;100&nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; 115<br>Expenditure -&nbsp; &nbsp; &nbsp; &nbsp; 70&nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; 75.25 <br>Saving -&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 30&nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; 39.75<br>required% = <math display=\"inline\"><mfrac><mrow><mn>39</mn><mo>.</mo><mn>75</mn><mo>-</mo><mn>30</mn></mrow><mrow><mn>30</mn></mrow></mfrac></math> &times; 100 = 32.5%</p>",
                    solution_hi: "<p>7.(a) <br>अनुपात -&nbsp; &nbsp; &nbsp; &nbsp;पहले&nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; बाद में<br>आय -&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 100&nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; 115<br>व्यय -&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 70&nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; 75.25 <br>बचत -&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;30&nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; 39.75<br>आवश्यक% = <math display=\"inline\"><mfrac><mrow><mn>39</mn><mo>.</mo><mn>75</mn><mo>-</mo><mn>30</mn></mrow><mrow><mn>30</mn></mrow></mfrac></math> &times; 100 = 32.5%</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "17",
                    question_en: "<p>8. Last year, Ranjan&rsquo;s monthly salary was ₹34,500 and this year his monthly salary is ₹38,640. What is the percentage increase in Ranjan&rsquo;s monthly salary this year over his monthly salary last year?</p>",
                    question_hi: "<p>8. पिछले वर्ष, रंजन का मासिक वेतन ₹34,500 था और इस वर्ष उसका मासिक वेतन ₹38,640 है। इस वर्ष रंजन के मासिक वेतन में, पिछले वर्ष के मासिक वेतन की तुलना में कितने प्रतिशत की वृद्धि हुई है?</p>",
                    options_en: ["<p>15%</p>", "<p>13%</p>", 
                                "<p>12%</p>", "<p>20%</p>"],
                    options_hi: ["<p>15%</p>", "<p>13%</p>",
                                "<p>12%</p>", "<p>20%</p>"],
                    solution_en: "<p>8.(c)<br>Increase % = <math display=\"inline\"><mfrac><mrow><mn>38640</mn><mo>-</mo><mn>34500</mn></mrow><mrow><mn>34500</mn></mrow></mfrac></math> &times; 100 = 12%</p>",
                    solution_hi: "<p>8.(c)<br>वृद्धि % = <math display=\"inline\"><mfrac><mrow><mn>38640</mn><mo>-</mo><mn>34500</mn></mrow><mrow><mn>34500</mn></mrow></mfrac></math> &times; 100 = 12%</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "17",
                    question_en: "<p>9. The cost of rice is increased by 25% but its consumption is decreased by 30%. Find the percentage increase or decrease in the expenditure of money.</p>",
                    question_hi: "<p>9. चावल के मूल्य में 25% की वृद्धि की जाती है लेकिन इसकी खपत में 30% की कमी हो जाती है। धन के व्यय में प्रतिशत वृद्धि या कमी ज्ञात कीजिए।</p>",
                    options_en: ["<p>Decrease 12<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math>%</p>", "<p>Decrease 13<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math>%</p>", 
                                "<p>Increase 12<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math>%</p>", "<p>Increase 13<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math>%</p>"],
                    options_hi: ["<p>12<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math>% कमी</p>", "<p>13<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math>% कमी</p>",
                                "<p>12<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math>% वृद्धि</p>", "<p>13<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math>% वृद्धि</p>"],
                    solution_en: "<p>9.(a) <br>increase/decrease% = 25 - 30 + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>25</mn><mo>&#215;</mo><mo>(</mo><mo>-</mo><mn>30</mn><mo>)</mo></mrow><mn>100</mn></mfrac></math> = -12.5% (-ve sine denote decrease)<br>Decrease % = 12<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math>% Decrease</p>",
                    solution_hi: "<p>9.(a) <br>वृद्धि/कमी% = 25 - 30 + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>25</mn><mo>&#215;</mo><mo>(</mo><mo>-</mo><mn>30</mn><mo>)</mo></mrow><mn>100</mn></mfrac></math>&nbsp;= -12.5% (- चिह्न कमी को दर्शाता है)<br>कमी % = 12<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math>%</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "17",
                    question_en: "<p>10. Express the following as decimals, respectively. <br>20%, 0.5%, 0.03%</p>",
                    question_hi: "<p>10. निम्नलिखित को क्रमशः दशमलव रूप में व्यक्त कीजिए।<br>20%, 0.5%, 0.03%</p>",
                    options_en: ["<p>0.02,0.005,0.0003</p>", "<p>0.02,0.005,0.003</p>", 
                                "<p>0.2,0.0005,0.0003</p>", "<p>0.2,0.005,0.0003</p>"],
                    options_hi: ["<p>0.02,0.005,0.0003</p>", "<p>0.02,0.005,0.003</p>",
                                "<p>0.2,0.0005,0.0003</p>", "<p>0.2,0.005,0.0003</p>"],
                    solution_en: "<p>10.(d)<br>20% = <math display=\"inline\"><mfrac><mrow><mn>20</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 0.2 <br>0.5% = <math display=\"inline\"><mfrac><mrow><mn>0</mn><mo>.</mo><mn>5</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 0.005<br>0.03% = <math display=\"inline\"><mfrac><mrow><mn>0</mn><mo>.</mo><mn>03</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 0.0003</p>",
                    solution_hi: "<p>10.(d)<br>20% = <math display=\"inline\"><mfrac><mrow><mn>20</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 0.2 <br>0.5% = <math display=\"inline\"><mfrac><mrow><mn>0</mn><mo>.</mo><mn>5</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 0.005<br>0.03% = <math display=\"inline\"><mfrac><mrow><mn>0</mn><mo>.</mo><mn>03</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 0.0003</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "17",
                    question_en: "<p>11. Last year, Geeta\'s monthly salary was ₹12,000 and Seeta\'s monthly salary was ₹10,000. This year, Geeta\'s monthly salary is ₹14,400, while Seeta\'s monthly salary is ₹12,500. If the percentage increase in Seeta\'s monthly salary this year over her monthly salary last year is denoted by x%, and the percentage increase in Geeta\'s monthly salary this year over her monthly salary last year is denoted by y%, then what is the value of (<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi mathvariant=\"normal\">x</mi><mo>-</mo><mi mathvariant=\"normal\">y</mi></mrow><mi mathvariant=\"normal\">y</mi></mfrac></math> &times; 100)%?</p>",
                    question_hi: "<p>11. गत वर्ष, गीता का मासिक वेतन ₹12,000 था और सीता का मासिक वेतन ₹10,000 था। इस वर्ष, गीता का मासिक वेतन ₹14,400 है, जबकि सीता का मासिक वेतन ₹12,500 है। यदि इस वर्ष सीता के मासिक वेतन में पिछले वर्ष के मासिक वेतन की तुलना में प्रतिशत वृद्धि को x% से निरुपित किया जाता है, और इस वर्ष गीता के मासिक वेतन में पिछले वर्ष के मासिक वेतन की तुलना में प्रतिशत वृद्धि को y% से निरुपित किया जाता है, तो (<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi mathvariant=\"normal\">x</mi><mo>-</mo><mi mathvariant=\"normal\">y</mi></mrow><mi mathvariant=\"normal\">y</mi></mfrac></math>&nbsp;&times; 100)% का मान क्या है?</p>",
                    options_en: ["<p>22</p>", "<p>20</p>", 
                                "<p>24</p>", "<p>25</p>"],
                    options_hi: ["<p>22</p>", "<p>20</p>",
                                "<p>24</p>", "<p>25</p>"],
                    solution_en: "<p>1.(d) <br>According to question,<br>y % = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>14400</mn><mo>-</mo><mn>12000</mn></mrow><mn>12000</mn></mfrac></math> &times; 100 = 20 %<br>x % = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>12500</mn><mo>-</mo><mn>10000</mn></mrow><mn>10000</mn></mfrac></math> &times; 100 = 25 %<br>So, (<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi mathvariant=\"normal\">x</mi><mo>-</mo><mi mathvariant=\"normal\">y</mi></mrow><mi mathvariant=\"normal\">y</mi></mfrac></math> &times; 100)% = ( <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>25</mn><mo>-</mo><mn>20</mn></mrow><mn>20</mn></mfrac></math> &times; 100)% = 25%</p>",
                    solution_hi: "<p>11.(d) <br>प्रश्न के अनुसार,<br>y % = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>14400</mn><mo>-</mo><mn>12000</mn></mrow><mn>12000</mn></mfrac></math> &times; 100 = 20 %<br>x % = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>12500</mn><mo>-</mo><mn>10000</mn></mrow><mn>10000</mn></mfrac></math> &times; 100 = 25 %<br>तो,&nbsp;(<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi mathvariant=\"normal\">x</mi><mo>-</mo><mi mathvariant=\"normal\">y</mi></mrow><mi mathvariant=\"normal\">y</mi></mfrac></math> &times; 100)% = ( <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>25</mn><mo>-</mo><mn>20</mn></mrow><mn>20</mn></mfrac></math> &times; 100)% = 25%</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "17",
                    question_en: "<p>12. The price of pulses has increased by 45%. By what percentage (rounded off to the nearest integer) the increased price of the pulses should be reduced so that the price of the pulses remains unaltered?</p>",
                    question_hi: "<p>12. दालों के मूल्य में 45% की बढ़ोतरी हुई है। दालों के बढ़े हुए मूल्य में कितने प्रतिशत (निकटतम पूर्णांक तक सन्निकटित) की कमी की जानी चाहिए ताकि दालों का मूल्य अपरिवर्तित रहे?</p>",
                    options_en: ["<p>35</p>", "<p>41</p>", 
                                "<p>45</p>", "<p>31</p>"],
                    options_hi: ["<p>35</p>", "<p>41</p>",
                                "<p>45</p>", "<p>31</p>"],
                    solution_en: "<p>12.(d)<br>Ratio -&nbsp; &nbsp; &nbsp; &nbsp; initial&nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; after <br>Pulses price - 20&nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; 29<br>Required % = <math display=\"inline\"><mfrac><mrow><mn>9</mn></mrow><mrow><mn>29</mn></mrow></mfrac></math> &times; 100 = 31.03 % or 31%</p>",
                    solution_hi: "<p>12.(d)<br>अनुपात - प्रारंभिक&nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; पश्चात <br>दालों का भाव - 20&nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; 29<br>आवश्यक % = <math display=\"inline\"><mfrac><mrow><mn>9</mn></mrow><mrow><mn>29</mn></mrow></mfrac></math> &times; 100 = 31.03 % या 31 %</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "17",
                    question_en: "<p>13. If the price of tea is increased by 40%, by how much percentage must the consumption of tea be decreased so as not to increase the expenditure ?</p>",
                    question_hi: "<p>13. यदि चाय की कीमतों में 40% की वृद्धि होती है, तो चाय की खपत को कितने प्रतिशत कम किया जाना चाहिए ताकि व्यय में कोई वृद्धि न हो?</p>",
                    options_en: ["<p>35<math display=\"inline\"><mfrac><mrow><mn>9</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math>%</p>", "<p>28<math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math>%</p>", 
                                "<p>31<math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math>%</p>", "<p>15<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>9</mn></mrow></mfrac></math>%</p>"],
                    options_hi: ["<p>35<math display=\"inline\"><mfrac><mrow><mn>9</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math>%</p>", "<p>28<math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math>%</p>",
                                "<p>31<math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math>%</p>", "<p>15<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>9</mn></mrow></mfrac></math>%</p>"],
                    solution_en: "<p>13.(b)&nbsp;According to the question,<br>Ratio -&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;initial&nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; final<br>Price -&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 100&nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; 140<br>Consumption - 140&nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; 100<br>decrease% = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>140</mn><mo>-</mo><mn>100</mn></mrow><mn>140</mn></mfrac></math> &times; 100 = 28<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>7</mn></mfrac></math>%</p>",
                    solution_hi: "<p>13.(b)&nbsp;प्रश्न के अनुसार,<br>अनुपात - प्रारंभिक&nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; अंतिम<br>कीमत -&nbsp; &nbsp; &nbsp; &nbsp; 100&nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; 140<br>खपत -&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 140&nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; 100<br>कमी% = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>140</mn><mo>-</mo><mn>100</mn></mrow><mn>140</mn></mfrac></math> &times; 100 = 28<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>7</mn></mfrac></math>%</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "17",
                    question_en: "<p>14. Ramu used to spend 72% of his income. His income has increased by 12%, and he increases his expenditure by 5%. If Ramu earlier saved y and after the increases he now saves x, then what is the value of (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi mathvariant=\"normal\">x</mi><mo>-</mo><mi mathvariant=\"normal\">y</mi></mrow><mi mathvariant=\"normal\">y</mi></mfrac></math> &times; 100)% ?</p>",
                    question_hi: "<p>14. रामू अपनी आय का 72% खर्च करता है। उसकी आय में 12% की वृद्धि होती है, और वह अपने खर्च में 5% की वृद्धि करता है। यदि रामू पहले \'y बचत करता था और वृद्धि के बाद अब वह \'<math display=\"inline\"><mi>x</mi></math> बचत करता है, तो (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi mathvariant=\"normal\">x</mi><mo>-</mo><mi mathvariant=\"normal\">y</mi></mrow><mi mathvariant=\"normal\">y</mi></mfrac></math> &times; 100)% का मान क्या है?</p>",
                    options_en: ["<p>25%</p>", "<p>22%</p>", 
                                "<p>27%</p>", "<p>30%</p>"],
                    options_hi: ["<p>25%</p>", "<p>22%</p>",
                                "<p>27%</p>", "<p>30%</p>"],
                    solution_en: "<p>14.(d)&nbsp;Let the income of the Ramu is 100 units<br>According to the question,<br>Ratio -&nbsp; &nbsp; &nbsp; before&nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; after<br>Income -&nbsp; &nbsp; &nbsp; 100&nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; 112<br>Expenditure - 72&nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; 75.6<br>Saving -&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;28&nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; 36.4<br>Required value = (<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi mathvariant=\"normal\">x</mi><mo>-</mo><mi mathvariant=\"normal\">y</mi></mrow><mi mathvariant=\"normal\">y</mi></mfrac></math> &times; 100)% = (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>36</mn><mo>.</mo><mn>4</mn><mo>-</mo><mn>28</mn></mrow><mn>28</mn></mfrac></math> &times; 100)% = 30%</p>",
                    solution_hi: "<p>14.(d)&nbsp;माना रामू की आय 100 इकाई है<br>प्रश्न के अनुसार,<br>अनुपात - पहले&nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; बाद में<br>आय-&nbsp; &nbsp; &nbsp; &nbsp;100&nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; 112<br>व्यय -&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;72&nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; 75.6<br>बचत-&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;28&nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; 36.4<br>आवश्यक मान = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi mathvariant=\"normal\">x</mi><mo>-</mo><mi mathvariant=\"normal\">y</mi></mrow><mi mathvariant=\"normal\">y</mi></mfrac></math> &times; 100)% = (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>36</mn><mo>.</mo><mn>4</mn><mo>-</mo><mn>28</mn></mrow><mn>28</mn></mfrac></math> &times; 100)% = 30%</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "misc",
                    question_en: "<p>15. If the income of Neha is 5% more than that of Monica, then the income of Monica is what percentage less than that of Neha?</p>",
                    question_hi: "<p>15. यदि नेहा की आय मोनिका की आय से 5% अधिक है, तो मोनिका की आय नेहा की आय से कितने प्रतिशत कम है ?</p>",
                    options_en: ["<p>4<math display=\"inline\"><mfrac><mrow><mn>16</mn></mrow><mrow><mn>21</mn></mrow></mfrac></math>%</p>", "<p>4<math display=\"inline\"><mfrac><mrow><mn>11</mn></mrow><mrow><mn>21</mn></mrow></mfrac></math>%</p>", 
                                "<p>4<math display=\"inline\"><mfrac><mrow><mn>13</mn></mrow><mrow><mn>21</mn></mrow></mfrac></math>%</p>", "<p>4<math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>21</mn></mrow></mfrac></math>%</p>"],
                    options_hi: ["<p>4<math display=\"inline\"><mfrac><mrow><mn>16</mn></mrow><mrow><mn>21</mn></mrow></mfrac></math>%</p>", "<p>4<math display=\"inline\"><mfrac><mrow><mn>11</mn></mrow><mrow><mn>21</mn></mrow></mfrac></math>%</p>",
                                "<p>4<math display=\"inline\"><mfrac><mrow><mn>13</mn></mrow><mrow><mn>21</mn></mrow></mfrac></math>%</p>", "<p>4<math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>21</mn></mrow></mfrac></math>%</p>"],
                    solution_en: "<p>15.(a)<br>Ratio -&nbsp; &nbsp; &nbsp; Neha&nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; Monica<br>Income -&nbsp; &nbsp; &nbsp; 105&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp; 100<br>required% = <math display=\"inline\"><mfrac><mrow><mn>105</mn><mo>-</mo><mn>100</mn></mrow><mrow><mn>105</mn></mrow></mfrac></math> &times; 100 = 4<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>16</mn><mn>21</mn></mfrac></math>%</p>",
                    solution_hi: "<p>15.(a)<br>अनुपात - नेहा&nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; मोनिका<br>आय -&nbsp; &nbsp; &nbsp;105&nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; 100<br>आवश्यक% = <math display=\"inline\"><mfrac><mrow><mn>105</mn><mo>-</mo><mn>100</mn></mrow><mrow><mn>105</mn></mrow></mfrac></math> &times; 100 = 4<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>16</mn><mn>21</mn></mfrac></math>%</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>