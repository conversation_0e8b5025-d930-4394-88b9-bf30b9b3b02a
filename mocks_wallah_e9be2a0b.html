<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">60:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 100</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">100</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 60 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["6"] = {
                name: "Reasoning",
                start: 0,
                end: 23
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="6">Reasoning</option>`;
            sections["18"] = {
                name: "General Awareness",
                start: 24,
                end: 48
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="18">General Awareness</option>`;
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 49,
                end: 73
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["7"] = {
                name: "English",
                start: 74,
                end: 98
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="7">English</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 99,
                end: 99
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "6",
                    question_en: "<p>1. Identify the figure given in the options which when put in place of \'?\' will logically complete the series.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731132142680.png\" alt=\"rId6\" width=\"374\" height=\"66\"></p>",
                    question_hi: "<p>1. दिए गए विकल्पों में से उस आकृति की पहचान कीजिए, जिसे\'?\' के स्थान पर रखने पर शृंखला तार्किक रूप से पूर्ण हो जाएगी।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731132142680.png\" alt=\"rId6\" width=\"374\" height=\"66\"></p>",
                    options_en: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731132142886.png\" alt=\"rId7\" width=\"90\" height=\"91\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731132143521.png\" alt=\"rId8\" width=\"90\" height=\"90\"></p>", 
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731132143693.png\" alt=\"rId9\" width=\"89\" height=\"90\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731132143863.png\" alt=\"rId10\" width=\"90\" height=\"91\"></p>"],
                    options_hi: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731132142886.png\" alt=\"rId7\" width=\"91\" height=\"92\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731132143521.png\" alt=\"rId8\" width=\"90\" height=\"90\"></p>",
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731132143693.png\" alt=\"rId9\" width=\"91\" height=\"91\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731132143863.png\" alt=\"rId10\" width=\"90\" height=\"90\"></p>"],
                    solution_en: "<p>1.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731132143863.png\" alt=\"rId10\" width=\"90\" height=\"91\"></p>",
                    solution_hi: "<p>1.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731132143863.png\" alt=\"rId10\" width=\"90\" height=\"91\"></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "6",
                    question_en: "<p>2. 34 is related to 191 by certain logic. Following the same logic, 56 is related to 301. To which of the following is 63 related, following the same logic?<br>(<strong>NOTE :</strong> Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g. 13- Operations on 13 such as adding /deleting /multiplying etc., to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed.)</p>",
                    question_hi: "<p>2. 34 एक निश्चित तर्क का अनुसरण करते हुए 191 से संबंधित है। इसी तर्क का अनुसरण करते हुए 56, 301 से संबंधित है। समान तर्क का अनुसरण करते हुए, 63 निम्नलिखित में से किससे संबंधित है?<br>(<strong>नोट:</strong> संख्याओं को उनके घटक अंकों में विभक्त किए बिना पूर्ण संख्याओं पर संक्रियाएं की जानी चाहिए। उदाहरण के लिए 13 को लें-13 पर संक्रियाएं, जैसे कि जोड़ना/घटाना/गुणा आदि 13 में की जा सकती हैं। 13 को 1 और 3 में विभक्त करना और फिर 1 और 3 पर गणितीय संक्रियाएं करने की अनुमति नहीं है।)</p>",
                    options_en: ["<p>340</p>", "<p>386</p>", 
                                "<p>336</p>", "<p>319</p>"],
                    options_hi: ["<p>340</p>", "<p>386</p>",
                                "<p>336</p>", "<p>319</p>"],
                    solution_en: "<p>2.(c) <strong>Logic:</strong> (1st number &times; 5) + 21 = 2nd number<br>(34 : 191) :- (34 &times; 5) + 21 = 191 <br>(56 : 301) :- (56 &times; 5) + 21 = 301 <br>Similarly<br>(63 : x) :- (63 &times; 5) + 21 = 336</p>",
                    solution_hi: "<p>2.(c) <strong>तर्क: </strong>(पहली संख्या &times; 5) + 21 = दूसरी संख्या<br>(34 : 191) :- (34 &times; 5) + 21 = 191 <br>(56 : 301) :- (56 &times; 5) + 21 = 301 <br>इसी प्रकार<br>(63 : x) :- (63 &times; 5) + 21 = 336</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "6",
                    question_en: "<p>3. In a certain code language,<br>\'A + B\' means \'A is the mother of B\';<br>\'A- B\' means \'A is the brother of B\';<br>\'A &times; B\' means \'A is the wife of B\' and<br>\'A &divide; B\' means \'A is the father of B\'.<br>Based on the above, how is 5 related to 2 if \'5 &divide; 1 &times; 3 &divide; 4 - 2\'?</p>",
                    question_hi: "<p>3. एक निश्चित कूट भाषा में,<br>\'A + B\' का अर्थ है \'A, B की माता है\';<br>\'A - B\' का अर्थ है \'A, B का भाई है;<br>\'A &times; B\' का अर्थ है \'A, B की पत्नी है और<br>\'A &divide; B\' का अर्थ है \'A, B का पिता है।<br>उपर्युक्त के आधार पर, यदि \'5 &divide; 1 &times; 3 &divide; 4 - 2\' है, तो 5, 2 से किस प्रकार संबंधित है?</p>",
                    options_en: ["<p>Father\'s mother</p>", "<p>Father\'s father</p>", 
                                "<p>Mother\'s mother</p>", "<p>Mother\'s father</p>"],
                    options_hi: ["<p>पिता की माता</p>", "<p>पिता का पिता</p>",
                                "<p>माता की माता</p>", "<p>माता का पिता</p>"],
                    solution_en: "<p>3.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731132143984.png\" alt=\"rId11\" width=\"109\" height=\"152\"><br>&lsquo;5&rsquo; is the Mother\'s father of &lsquo;2&rsquo;</p>",
                    solution_hi: "<p>3.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731132143984.png\" alt=\"rId11\" width=\"101\" height=\"141\"><br>\'5\', \'2\' की माता का पिता है</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "6",
                    question_en: "<p>4. 27 is related to 119 following a certain logic. Following the same logic, 33 is related to 143. To which of the following is 42 related, following the same logic? (<strong>NOTE </strong>: Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g. 13 - Operations on 13 such as adding /deleting /multiplying etc., to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed.)</p>",
                    question_hi: "<p>4. एक निश्चित तर्क के अनुसार 27 का संबंध 119 से है। उसी तर्क का अनुसरण करते हुए, 33 का संबंध 143 से है। उसी तर्क का अनुसरण करते हुए, 42 का संबंध निम्नलिखित में से किससे है? <br><strong>नोट : </strong>संख्याओं को उनके घटक अंकों में विभक्त किए बिना पूर्ण संख्याओं पर संक्रियाएं की जानी चाहिए। उदाहरण के लिए 13 को लें-13 पर संक्रियाएं, जैसे कि जोड़ना/घटाना/गुणा आदि 13 में की जा सकती हैं। 13 को 1 और 3 में विभक्त करना और फिर 1 और 3 पर गणितीय संक्रियाएं करने की अनुमति नहीं है।</p>",
                    options_en: ["<p>153</p>", "<p>188</p>", 
                                "<p>179</p>", "<p>162</p>"],
                    options_hi: ["<p>153</p>", "<p>188</p>",
                                "<p>179</p>", "<p>162</p>"],
                    solution_en: "<p>4.(c) <strong>Logic:</strong> (1st number &times; 4) + 11 = 2nd number.<br>(27 : 119) :- 27 &times; 4 + 11 = 119 <br>(33 : 143) :- 33 &times; 4 + 11 = 143<br>Similarly<br>(42 : x) :- 42 &times; 4 + 11 = 179</p>",
                    solution_hi: "<p>4.(c) <strong>तर्क: </strong>(पहली संख्या &times; 4 )+ 11 = दूसरी संख्या<br>(27 : 119) :- 27 &times; 4 + 11 = 119 <br>(33 : 143) :- 33 &times; 4 + 11 = 143<br>इसी प्रकार<br>(42 : x) :- 42 &times; 4 + 11 = 179</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "6",
                    question_en: "<p>5. Four letter-clusters have been given, out of which three are alike in some manner and one is different. Select the letter-cluster that is different.<br><strong>Note :</strong> The odd one out is not based on the number of consonants/vowels or their position in the letter cluster.</p>",
                    question_hi: "<p>5. चार अक्षर-समूह दिए गए हैं, जिनमें से तीन किसी न किसी रूप में एक जैसे हैं और एक भिन्न है। उस अक्षर-समूह का चयन करें जो भिन्न हो।<br><strong>नोट :</strong> अक्षर समूह में, भिन्न व्यंजनों/स्वरों की संख्या या उनकी स्थिति पर आधारित नहीं है।.</p>",
                    options_en: ["<p>HLJ</p>", "<p>GIK</p>", 
                                "<p>LPN</p>", "<p>BFD</p>"],
                    options_hi: ["<p>HLJ</p>", "<p>GIK</p>",
                                "<p>LPN</p>", "<p>BFD</p>"],
                    solution_en: "<p>5.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731132144086.png\" alt=\"rId12\" width=\"108\" height=\"56\">,<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731132144201.png\" alt=\"rId13\" width=\"152\" height=\"56\">,<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731132144313.png\" alt=\"rId14\" width=\"126\" height=\"55\"><br>But<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731132144417.png\" alt=\"rId15\" width=\"107\" height=\"56\"></p>",
                    solution_hi: "<p>5.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731132144086.png\" alt=\"rId12\" width=\"108\" height=\"56\">,<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731132144201.png\" alt=\"rId13\" width=\"152\" height=\"56\">,<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731132144313.png\" alt=\"rId14\" width=\"126\" height=\"55\"><br>लेकिन<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731132144417.png\" alt=\"rId15\" width=\"107\" height=\"56\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "6",
                    question_en: "<p>6. What will come in place of \'?\' in the following equation, if \'+\' and \'-\' are interchanged and \'&times;\' and \'&divide;\' are interchanged?<br>63 &times; 9 + 21 - 4 &divide; 5 = ?</p>",
                    question_hi: "<p>6. यदि \'+\' और \'-\' को परस्पर बदल दिया जाए और \'&times;\' और \'&divide;\' को परस्पर बदल दिया जाए, तो निम्नलिखित समीकरण में \'?\' के स्थान पर कितना मान आएगा?<br>63 &times; 9 + 21 - 4 &divide; 5 = ?</p>",
                    options_en: ["<p>4</p>", "<p>7</p>", 
                                "<p>6</p>", "<p>5</p>"],
                    options_hi: ["<p>4</p>", "<p>7</p>",
                                "<p>6</p>", "<p>5</p>"],
                    solution_en: "<p>6.(c) <strong>Given: </strong>63 &times; 9 + 21 - 4 &divide; 5 = ?<br>As per the instruction given in the question, after interchanging the symbol \'+\' and \'-\' and \'&times;\' and \'&divide;\' we get&nbsp;<br>63 &divide; 9 - 21 + 4 &times; 5 = ?<br>7 - 21 + 4 &times; 5 <br>7 - 21 + 20 <br>27 - 21 = 6</p>",
                    solution_hi: "<p>6.(c) दिया गया है: 63 &times; 9 + 21 - 4 &divide; 5 = ?<br>प्रश्न में दिए गए निर्देश के अनुसार, प्रतीक \'+\' और \'-\' तथा \'&times;\' और \'&divide;\' को आपस में बदलने पर हमें प्राप्त होता है<br>63 &divide; 9 - 21 + 4 &times; 5 = ?<br>7 - 21 + 4 &times; 5 <br>7 - 21 + 20 <br>27 - 21 = 6</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "6",
                    question_en: "<p>7. Identify the figure given in the options which when put in place of ? will logically complete the series.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731132144574.png\" alt=\"rId16\" width=\"370\" height=\"74\"></p>",
                    question_hi: "<p>7. विकल्पों में दी गई उस आकृति की पहचान कीजिए जिसे ? के स्थान पर रखने पर श्रृंखला तर्कसंगत रूप से पूर्ण हो जाएगी।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731132144574.png\" alt=\"rId16\" width=\"370\" height=\"74\"></p>",
                    options_en: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731132144777.png\" alt=\"rId17\" width=\"90\" height=\"91\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731132144979.png\" alt=\"rId18\" width=\"90\" height=\"91\"></p>", 
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731132145169.png\" alt=\"rId19\" width=\"91\" height=\"92\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731132145337.png\" alt=\"rId20\" width=\"89\" height=\"90\"></p>"],
                    options_hi: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731132144777.png\" alt=\"rId17\" width=\"90\" height=\"91\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731132144979.png\" alt=\"rId18\" width=\"90\" height=\"91\"></p>",
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731132145169.png\" alt=\"rId19\" width=\"92\" height=\"93\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731132145337.png\" alt=\"rId20\" width=\"91\" height=\"92\"></p>"],
                    solution_en: "<p>7.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731132145337.png\" alt=\"rId20\" width=\"91\" height=\"92\"></p>",
                    solution_hi: "<p>7.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731132145337.png\" alt=\"rId20\" width=\"91\" height=\"92\"></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "6",
                    question_en: "<p>8. Select the number from among the given options that can replace the question mark (?) in the following series.<br>949, 1006, 1066, 1129, 1195, ?</p>",
                    question_hi: "<p>8. दिए गए विकल्पों में से उस संख्या का चयन कीजिए, जो निम्नलिखित श्रृंखला में प्रश्न-चिह्न (?) को प्रतिस्थापित कर सकती है।<br>949, 1006, 1066, 1129, 1195, ?</p>",
                    options_en: ["<p>1233</p>", "<p>1264</p>", 
                                "<p>1244</p>", "<p>1254</p>"],
                    options_hi: ["<p>1233</p>", "<p>1264</p>",
                                "<p>1244</p>", "<p>1254</p>"],
                    solution_en: "<p>8.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731132145471.png\" alt=\"rId21\" width=\"240\" height=\"73\"></p>",
                    solution_hi: "<p>8.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731132145471.png\" alt=\"rId21\" width=\"240\" height=\"73\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "6",
                    question_en: "<p>9. In this question, three statements are given, followed by three conclusions numbered I, Il and III. Assuming the statements to be true, even if they seem to be at variance with commonly known facts, decide which of the conclusions logically follow(s) from the statements.<br><strong>Statements :</strong><br>All computers are printers.<br>Some printers are laptops.<br>All laptops are mobiles.<br><strong>Conclusions :</strong><br>I. Some printers are computers.<br>II. Some laptops are printers.<br>III. Some mobiles are laptops.</p>",
                    question_hi: "<p>9. इस प्रश्न में, तीन कथन और उसके बाद तीन निष्कर्ष क्रमांक।, ॥ और II। दिए गए हैं। कथनों को सत्य मानते हुए, भले ही वे सामान्य रूप से ज्ञात तथ्यों से भिन्न प्रतीत होते हों, तय कीजिए कि कौन सा/से निष्कर्ष कथनों का तार्किक रूप से अनुसरण करता/करते है/ हैं।<br><strong>कथन :</strong><br>सभी कंप्यूटर, प्रिंटर हैं।<br>कुछ प्रिंटर, लैपटॉप हैं।<br>सभी लैपटॉप, मोबाइल हैं।<br><strong>निष्कर्ष :</strong><br>I. कुछ प्रिंटर, कंप्यूटर हैं।<br>II. कुछ लैपटॉप, प्रिंटर हैं।<br>III. कुछ मोबाइल, लैपटॉप हैं।</p>",
                    options_en: ["<p>Only conclusion III follows.</p>", "<p>Only conclusions II and III follow.</p>", 
                                "<p>All the conclusions follow.</p>", "<p>Only conclusions I and III follow.</p>"],
                    options_hi: ["<p>केवल निष्कर्ष III अनुसरण करता है।</p>", "<p>केवल निष्कर्ष II और III अनुसरण करते हैं।</p>",
                                "<p>सभी निष्कर्ष अनुसरण करते हैं।</p>", "<p>केवल निष्कर्ष। और III अनुसरण करते हैं।</p>"],
                    solution_en: "<p>9.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731132145632.png\" alt=\"rId22\" width=\"180\" height=\"84\"><br>All the conclusions follow.</p>",
                    solution_hi: "<p>9.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731132145739.png\" alt=\"rId23\" width=\"192\" height=\"87\"><br>सभी निष्कर्ष अनुसरण करते हैं।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "6",
                    question_en: "<p>10. \'ACKO\' is related to \'ZXPL\' in a certain way based on the English alphabetical order. In the same way, \'HIND\' is related to \'SRMW\'. To which of the following is \'PUKE\' related, following the same logic?</p>",
                    question_hi: "<p>10. अँग्रेजी वर्णमाला-क्रम के आधार पर \'ACKO\' एक निश्चित तरीके से \'ZXPL\' से संबंधित है। उसी तरह \'HIND\' का संबंध \'SRMW\' से है। उसी तर्क के अनुसार \'PUKE\' का संबंध निम्नलिखित में से किससे है?</p>",
                    options_en: ["<p>JGQW</p>", "<p>JGPW</p>", 
                                "<p>LFQV</p>", "<p>KFPV</p>"],
                    options_hi: ["<p>JGQW</p>", "<p>JGPW</p>",
                                "<p>LFQV</p>", "<p>KFPV</p>"],
                    solution_en: "<p>10.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731132145863.png\" alt=\"rId24\" width=\"111\" height=\"103\">,<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731132145988.png\" alt=\"rId25\" width=\"107\" height=\"104\"><br>Similarly<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731132146119.png\" alt=\"rId26\" width=\"100\" height=\"93\"></p>",
                    solution_hi: "<p>10.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731132146243.png\" alt=\"rId27\" width=\"114\" height=\"107\">,&nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731132146362.png\" alt=\"rId28\" width=\"108\" height=\"106\"><br>इसी प्रकार <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731132146504.png\" alt=\"rId29\" width=\"105\" height=\"100\"></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "6",
                    question_en: "<p>11. In a certain code language, \'What you think you become\' is written as \'Fe Mo Su Ta Za\' and \'What you think you achieve\' is written as \'Mo Su Te Fe Za\'. How will \'achieve\' be written in that language?</p>",
                    question_hi: "<p>11. एक निश्चित कूट भाषा में, \'What you think you become\' को \'Fe Mo Su Ta Za\' के रूप में लिखा जाता है और \'What you think you achieve\' को \'Mo Su Te Fe Za\' के रूप में लिखा जाता है। उसी भाषा में \'achieve\' को किस प्रकार लिखा जाएगा?</p>",
                    options_en: ["<p>Za</p>", "<p>Mo</p>", 
                                "<p>Te</p>", "<p>Fe</p>"],
                    options_hi: ["<p>Za</p>", "<p>Mo</p>",
                                "<p>Te</p>", "<p>Fe</p>"],
                    solution_en: "<p>11.(c) \'What you think you become\' &rarr;&nbsp;\'Fe Mo Su Ta Za\' &hellip;&hellip;&hellip;.. (i)<br>\'What you think you achieve\' &rarr; \'Mo Su Te Fe Za &hellip;&hellip;&hellip;.(ii)<br>From (i) and (ii) \'What you think you&rsquo; is common <br>So, the code of &lsquo;achieve&rsquo; is &lsquo;Te&rsquo;</p>",
                    solution_hi: "<p>11.(c) \'What you think you become\' &rarr; \'Fe Mo Su Ta Za\' &hellip;&hellip;&hellip;.. (i)<br>\'What you think you achieve\' &rarr; \'Mo Su Te Fe Za &hellip;&hellip;&hellip;.(ii)<br>(i) और (ii) से \'What you think you&rsquo; उभयनिष्ट है<br>तो, \'achieve\' का कोड \'te\' है</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "6",
                    question_en: "<p>12. \'Noise\' is related to \'Silence\' in the same way as \'Light\' is related to \' _________ .</p>",
                    question_hi: "<p>12. \'शोर\' का संबंध \'मौन\' से उसी प्रकार है, जैसे \'प्रकाश\' का संबंध \' ____________ &lsquo; से है।</p>",
                    options_en: ["<p>Radiant</p>", "<p>Warmth</p>", 
                                "<p>Brightness</p>", "<p>Darkness</p>"],
                    options_hi: ["<p>दीप्तिमान</p>", "<p>गरमाहट</p>",
                                "<p>चमक</p>", "<p>अंधकार</p>"],
                    solution_en: "<p>12.(d)<br>&lsquo;Noise&rsquo; and &lsquo;Silence&rsquo; are opposites. Similarly, &lsquo;Light&rsquo; and &lsquo;Darkness&rsquo; are opposites.</p>",
                    solution_hi: "<p>12.(d)<br>\'शोर\' और \'मौन\' विपरीत हैं। इसी प्रकार, \'प्रकाश\' और \'अंधकार\' विपरीत हैं।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "6",
                    question_en: "<p>13. Which of the following terms will replace the question mark (?) in the given series?<br>EQBY, GPDX, ?, KNHV, MMJU</p>",
                    question_hi: "<p>13. निम्नलिखित में से कौन-सा पद दी गई शृंखला में प्रश्न-चिह्न (?) का स्थान लेगा?<br>EQBY, GPDX, ?, KNHV, MMJU</p>",
                    options_en: ["<p>IOFW</p>", "<p>IPFW</p>", 
                                "<p>HOFW</p>", "<p>IOEW</p>"],
                    options_hi: ["<p>IOFW</p>", "<p>IPFW</p>",
                                "<p>HOFW</p>", "<p>IOEW</p>"],
                    solution_en: "<p>13.(a).<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731132146649.png\" alt=\"rId30\" width=\"241\" height=\"81\"></p>",
                    solution_hi: "<p>13.(a).<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731132146649.png\" alt=\"rId30\" width=\"241\" height=\"81\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "6",
                    question_en: "<p>14. Select the number that will come in the place of the question mark(?), if \'+\' and \' -\' are interchanged and \'&times;\' and \'&divide;\' are interchanged<br>31 &divide; 3 - 186 &times; 6 + 17 = ?</p>",
                    question_hi: "<p>14. यदि निम्नलिखित समीकरण में \'+\' और &lsquo;-&rsquo; को आपस में बदल दिया जाए और \'&times;\' और \'&divide;\' को आपस में बदल दिया जाए, तो प्रश्न चिह्न (?) के स्थान पर कौन-सी संख्या आएगी ?<br>31 &divide; 3 - 186 &times; 6 + 17 = ?</p>",
                    options_en: ["<p>103</p>", "<p>96</p>", 
                                "<p>118</p>", "<p>107</p>"],
                    options_hi: ["<p>103</p>", "<p>96</p>",
                                "<p>118</p>", "<p>107</p>"],
                    solution_en: "<p>14.(d) <strong>Given:</strong> 31 &divide; 3 - 186 &times; 6 + 17 = ?<br>As per instruction given in question, after interchanging the symbol \'+\' and \' -\' and \'&times;\' and \'&divide;\' we get<br>31 &times; 3 + 186 &divide; 6 - 17 = ?<br>31 &times; 3 + 31 - 17<br>93 + 31 - 17<br>124 - 17 = 107</p>",
                    solution_hi: "<p>14.(d) <strong>दिया गया है: </strong>31 &divide; 3 - 186 &times; 6 + 17 = ?<br>प्रश्न में दिए गए निर्देश के अनुसार, प्रतीक \'+\' और \'-\' और \'&times;\' और \'&divide;\' को आपस में बदलने पर हमें प्राप्त होता है<br>31 &times; 3 + 186 &divide; 6 - 17 = ?<br>31 &times; 3 + 31 - 17<br>93 + 31 - 17<br>124 - 17 = 107</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "6",
                    question_en: "<p>15. Read the given statements and conclusions carefully. You have to take the given statements to be true even if they seem to be at variance from commonly known facts. You have to decide which conclusion/s logically follow/s from the given statements.<br><strong>Statements :</strong><br>Some telephones are curtains.<br>All curtains are lamps.<br>No lamps is books.<br><strong>Conclusions :</strong><br>(I) All lamps are telephones.<br>(II) No books is lamps.</p>",
                    question_hi: "<p>15. दिए गए कथनों और निष्कर्षों को ध्यानपूर्वक पढ़ें। आपको दिए गए कथनों को सत्य मानना है भले ही वे सामान्य रूप से ज्ञात तथ्यों से भिन्न प्रतीत होते हों। आपको यह तय करना है कि कौन सा/से निष्कर्ष दिए गए कथनों का तार्किक रूप से अनुसरण करता/करते है/ हैं।<br><strong>कथन :</strong><br>कुछ टेलीफोन, पर्दे हैं।<br>सभी पर्दे, लैम्प हैं।<br>कोई भी लैम्प, पुस्तक नहीं हैं।<br><strong>निष्कर्ष :</strong><br>(I) सभी लैम्प, टेलीफोन हैं।<br>(II) कोई भी पुस्तक, लैम्प नहीं हैं।</p>",
                    options_en: ["<p>Only conclusion (I) is follow</p>", "<p>Only conclusion (II) is follow</p>", 
                                "<p>Neither conclusion (I) nor (II) is follow</p>", "<p>Both conclusions (I) and (II) are follow</p>"],
                    options_hi: ["<p>केवल निष्कर्ष (I) अनुसरण करता है।</p>", "<p>केवल निष्कर्ष (II) अनुसरण करता है।</p>",
                                "<p>न तो निष्कर्ष (I) और न ही (II) अनुसरण करता है।</p>", "<p>निष्कर्ष (I) और (II), दोनों अनुसरण करते हैं।</p>"],
                    solution_en: "<p>15.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731132146762.png\" alt=\"rId31\" width=\"223\" height=\"58\"><br>Only conclusion (II) is follow</p>",
                    solution_hi: "<p>15.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731132146901.png\" alt=\"rId32\" width=\"275\" height=\"73\"><br>केवल निष्कर्ष (II) अनुसरण करता है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "6",
                    question_en: "<p>16. How many squares are there in the given figure?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731132147107.png\" alt=\"rId33\" width=\"166\" height=\"178\"></p>",
                    question_hi: "<p>16. दी गई आकृति में कितने वर्ग हैं?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731132147107.png\" alt=\"rId33\" width=\"166\" height=\"178\"></p>",
                    options_en: ["<p>17</p>", "<p>20</p>", 
                                "<p>18</p>", "<p>19</p>"],
                    options_hi: ["<p>17</p>", "<p>20</p>",
                                "<p>18</p>", "<p>19</p>"],
                    solution_en: "<p>16.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731132147277.png\" alt=\"rId34\" width=\"157\" height=\"166\"><br>WXYV, ABYU, ACZT, AD59, EFY4, G23Y, GHIY, IYKJ, Y3LK , I1QJ, H2LJ, 1YNP, Y45N, LMNK, KJON, QJOP , QRSP, RJ78, JL67, I3MO.<br>There are 20 squares.</p>",
                    solution_hi: "<p>16.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731132147277.png\" alt=\"rId34\" width=\"157\" height=\"166\"><br>WXYV, ABYU, ACZT, AD59, EFY4, G23Y, GHIY, IYKJ, Y3LK , I1QJ, H2LJ, 1YNP, Y45N, LMNK, KJON, QJOP , QRSP, RJ78, JL67, I3MO.<br>20 वर्ग हैं.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "6",
                    question_en: "<p>17. The same operation(s) are followed in all the given number pairs except one. Find that odd number pair.<br>(<strong>NOTE :</strong> Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g. 13 - Operations on 13 such as adding /deleting /multiplying etc., to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed.)</p>",
                    question_hi: "<p>17. दिए गए संख्या युग्मों में से एक को छोड़कर अन्य सभी में समान संक्रिया/संक्रियाओं का अनुसरण किया गया है। वह असंगत संख्या युग्म ज्ञात कीजिए।<br>(<strong>नोट :</strong> संख्याओं को उसके संघटक अंकों में खंडित किए बिना संक्रियाएँ पूर्णांकों पर की जानी चाहिए। उदाहरण के लिए 13-13 पर की जाने वाली संक्रियाएं जैसे जोड़ना/घटाना/गुणा इत्यादि 13 पर ही की जानी चाहिए। 13 को 1 और 3 में खंडित करना और फिर 1 और 3 पर गणितीय संक्रियाएँ करना वर्जित है।)</p>",
                    options_en: ["<p>40 : 52</p>", "<p>45 : 54</p>", 
                                "<p>35 : 42</p>", "<p>55 : 66</p>"],
                    options_hi: ["<p>40 : 52</p>", "<p>45 : 54</p>",
                                "<p>35 : 42</p>", "<p>55 : 66</p>"],
                    solution_en: "<p>17.(a) <strong>Logic: </strong>except option (a), all option have ratio ( 5 : 6)<br>45 : 54 &rArr;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>45</mn><mn>9</mn></mfrac></math> : <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>54</mn><mn>9</mn></mfrac></math> = 5 : 6<br>35 : 42 &rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>35</mn><mn>7</mn></mfrac></math> : <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>42</mn><mn>7</mn></mfrac></math> = 5 : 6<br>55 : 66 &rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>55</mn><mn>11</mn></mfrac></math> : <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>66</mn><mn>11</mn></mfrac></math> = 5 : 6<br>But<br>40 : 52 &rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>40</mn><mn>4</mn></mfrac></math> : <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>52</mn><mn>4</mn></mfrac></math> = 10: 13</p>",
                    solution_hi: "<p>17.(a) <strong>तर्क : </strong>विकल्प (a) को छोड़कर, सभी विकल्पों का अनुपात (5 : 6) है ।&nbsp;<br>45 : 54 &rArr;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>45</mn><mn>9</mn></mfrac></math> : <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>54</mn><mn>9</mn></mfrac></math> = 5 : 6<br>35 : 42 &rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>35</mn><mn>7</mn></mfrac></math> : <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>42</mn><mn>7</mn></mfrac></math> = 5 : 6<br>55 : 66 &rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>55</mn><mn>11</mn></mfrac></math> : <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>66</mn><mn>11</mn></mfrac></math> = 5 : 6<br>लेकिन<br>40 : 52 &rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>40</mn><mn>4</mn></mfrac></math> : <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>52</mn><mn>4</mn></mfrac></math> = 10: 13</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "6",
                    question_en: "<p>18. A paper is folded and cut as shown below. How will it appear when unfolded?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731132147473.png\" alt=\"rId35\" width=\"338\" height=\"80\"></p>",
                    question_hi: "<p>18. एक कागज को निम्नानुसार मोड़ा और काटा जाता है। खोलने पर यह कैसा दिखाई देगा?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731132147473.png\" alt=\"rId35\" width=\"338\" height=\"80\"></p>",
                    options_en: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731132147690.png\" alt=\"rId36\" width=\"90\" height=\"85\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731132147880.png\" alt=\"rId37\" width=\"91\" height=\"86\"></p>", 
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731132148015.png\" alt=\"rId38\" width=\"91\" height=\"86\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731132148211.png\" alt=\"rId39\" width=\"91\" height=\"86\"></p>"],
                    options_hi: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731132147690.png\" alt=\"rId36\" width=\"90\" height=\"85\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731132147880.png\" alt=\"rId37\" width=\"90\" height=\"85\"></p>",
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731132148015.png\" alt=\"rId38\" width=\"90\" height=\"85\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731132148211.png\" alt=\"rId39\" width=\"90\" height=\"85\"></p>"],
                    solution_en: "<p>18.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731132148211.png\" alt=\"rId39\" width=\"90\" height=\"85\"></p>",
                    solution_hi: "<p>18.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731132148211.png\" alt=\"rId39\" width=\"90\" height=\"85\"></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "6",
                    question_en: "<p>19. In a certain code language, \'TIDE\' is coded as \'9457\' and \'DENT\' is coded as \'7593\'. What is the code for \'N\' in that language?</p>",
                    question_hi: "<p>19. एक निश्चित कूट भाषा में \'TIDE\' को \'9457\' के रूप में कूटबद्ध किया जाता है और \'DENT\' को \'7593\' के रूप में कूटबद्ध किया जाता है। उस भाषा में \'N\' के लिए कूट क्या है?</p>",
                    options_en: ["<p>5</p>", "<p>7</p>", 
                                "<p>3</p>", "<p>9</p>"],
                    options_hi: ["<p>5</p>", "<p>7</p>",
                                "<p>3</p>", "<p>9</p>"],
                    solution_en: "<p>19.(c) TIDE\' &rarr; \'9457\' &hellip;&hellip; (i)<br>\'DENT\' &rarr; \'7593 &hellip;&hellip;.. (ii)<br>From (i) and (ii) (T , D, E) is common<br>So, the code of &lsquo;N&rsquo; is &lsquo;3&rsquo;</p>",
                    solution_hi: "<p>19.(c) TIDE\' &rarr; \'9457\' &hellip;&hellip; (i)<br>\'DENT\' &rarr; \'7593 &hellip;&hellip;.. (ii)<br>(i) और (ii) से, (T, D, E) उभयनिष्ठ है.<br>तो, \'N\' का कोड \'3\' है</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "6",
                    question_en: "<p>20. Each of the letters in the word \'LASTING\' is arranged in the English alphabetical order. How many letters are there in the English alphabetical series between the letter which is second from the left end and the one which is fourth from the right end in the new letter-cluster thus formed?</p>",
                    question_hi: "<p>20. शब्द \'LASTING\' के प्रत्येक अक्षर को अँग्रेजी वर्णमाला क्रम में व्यवस्थित किया जाता है। इस प्रकार बने नए अक्षर-समूह में बाएँ छोर से दूसरे अक्षर और दाएँ छोर से चौथे अक्षर के बीच अँग्रेजी वर्णमाला शृंखला में कितने अक्षर हैं?</p>",
                    options_en: ["<p>Five</p>", "<p>Six</p>", 
                                "<p>Three</p>", "<p>Four</p>"],
                    options_hi: ["<p>पाँच</p>", "<p>छः</p>",
                                "<p>तीन</p>", "<p>चार</p>"],
                    solution_en: "<p>20.(d) <strong>Given:</strong> LASTING<br>After arranging in english alphabetical order - AGILNST<br>Letter second from the left is &lsquo;G&rsquo; and fourth from the right end is &lsquo;L&rsquo;<br>Letter between &lsquo;G&rsquo; and &lsquo;L&rsquo; in english alphabet = 4(H,I,J,K)</p>",
                    solution_hi: "<p>20.(d) <strong>Given:</strong> LASTING<br>अंग्रेजी वर्णमाला क्रम में व्यवस्थित करने के बाद - AGILNST<br>बायीं ओर से दूसरा अक्षर \'G\' है और दायें छोर से चौथा अक्षर \'L\' है<br>अंग्रेजी वर्णमाला में \'G\' और \'L\' के बीच का अक्षर = 4(H,I,J,K)</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "6",
                    question_en: "<p>21. A transparent sheet with a pattern is given in the following figure. Identify from amongst the options as to how the pattern would appear when the transparent sheet is folded at the middle horizontal line.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731132148383.png\" alt=\"rId40\" width=\"113\" height=\"82\"></p>",
                    question_hi: "<p>21. निम्नलिखित आकृति में पैटर्न वाली एक पारदर्शी शीट दी गई है। विकल्पों में से पहचानें कि मध्य क्षैतिज रेखा पर पारदर्शी शीट को मोड़ने पर पैटर्न कैसा दिखाई देगा।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731132148383.png\" alt=\"rId40\" width=\"113\" height=\"82\"></p>",
                    options_en: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731132148561.png\" alt=\"rId41\" width=\"101\" height=\"71\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731132148739.png\" alt=\"rId42\" width=\"100\" height=\"71\"></p>", 
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731132148929.png\" alt=\"rId43\" width=\"103\" height=\"73\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731132149121.png\" alt=\"rId44\" width=\"100\" height=\"71\"></p>"],
                    options_hi: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731132148561.png\" alt=\"rId41\" width=\"100\" height=\"71\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731132148739.png\" alt=\"rId42\" width=\"101\" height=\"72\"></p>",
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731132148929.png\" alt=\"rId43\" width=\"102\" height=\"72\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731132149121.png\" alt=\"rId44\" width=\"101\" height=\"72\"></p>"],
                    solution_en: "<p>21.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731132148929.png\" alt=\"rId43\" width=\"110\" height=\"78\"></p>",
                    solution_hi: "<p>21.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731132148929.png\" alt=\"rId43\" width=\"110\" height=\"78\"></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "6",
                    question_en: "<p>22. Select the option figure in which the given figure (X) is embedded (rotation is NOT allowed).<br><img src=\"data:image/png;base64,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\" width=\"104\" height=\"117\"></p>",
                    question_hi: "<p>22. उस विकल्प आकृति का चयन करें जिसमें दी गई आकृति (X) सन्निहित है (घुमाने की अनुमति नहीं है)।<br><img src=\"data:image/png;base64,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\" width=\"104\" height=\"117\"></p>",
                    options_en: ["<p><img src=\"data:image/png;base64,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\" width=\"89\" height=\"84\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731132149733.png\" alt=\"rId47\" width=\"91\" height=\"89\"></p>", 
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731132149905.png\" alt=\"rId48\" width=\"91\" height=\"89\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731132150172.png\" alt=\"rId49\" width=\"91\" height=\"89\"></p>"],
                    options_hi: ["<p><img src=\"data:image/png;base64,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\" width=\"89\" height=\"84\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731132149733.png\" alt=\"rId47\" width=\"91\" height=\"89\"></p>",
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731132149905.png\" alt=\"rId48\" width=\"91\" height=\"89\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731132150172.png\" alt=\"rId49\" width=\"91\" height=\"89\"></p>"],
                    solution_en: "<p>22.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731132150332.png\" alt=\"rId50\" width=\"97\" height=\"100\"></p>",
                    solution_hi: "<p>22.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731132150332.png\" alt=\"rId50\" width=\"97\" height=\"100\"></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "23",
                    section: "6",
                    question_en: "<p>23. Select the option that represents the letters that when placed from left to right in the blanks below will complete the given letter series.<br>MN_OJKM_QOJK_NQ_JKMNQO_KMN</p>",
                    question_hi: "<p>23. उस विकल्प का चयन करें जो उन अक्षरों को निरूपित करता है जिन्हें नीचे दिए गए रिक्त स्थानों में बाएँ से दाएँ रखे जाने पर दी गई अक्षर श्रृंखला पूरी हो जाएगी।<br>MN_OJKM_QOJK_NQ_JKMNQO_KMN</p>",
                    options_en: ["<p>QMNJO</p>", "<p>QNJMO</p>", 
                                "<p>QNMJO</p>", "<p>QNMOJ</p>"],
                    options_hi: ["<p>QMNJO</p>", "<p>QNJMO</p>",
                                "<p>QNMJO</p>", "<p>QNMOJ</p>"],
                    solution_en: "<p>23.(d)<br>MN<span style=\"text-decoration: underline;\"><strong>Q</strong></span>OJK/M<span style=\"text-decoration: underline;\"><strong>N</strong></span>QOJK/<span style=\"text-decoration: underline;\"><strong>M</strong></span>NQ<span style=\"text-decoration: underline;\"><strong>O</strong></span>JK/MNQO<span style=\"text-decoration: underline;\"><strong>J</strong></span>K/MN</p>",
                    solution_hi: "<p>23.(d)<br>MN<span style=\"text-decoration: underline;\"><strong>Q</strong></span>OJK/M<span style=\"text-decoration: underline;\"><strong>N</strong></span>QOJK/<span style=\"text-decoration: underline;\"><strong>M</strong></span>NQ<span style=\"text-decoration: underline;\"><strong>O</strong></span>JK/MNQO<span style=\"text-decoration: underline;\"><strong>J</strong></span>K/MN</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "24",
                    section: "6",
                    question_en: "<p>24. Select the option in which the numbers share the same relationship as that shared by the given pair of numbers.<br>(<strong>NOTE : </strong>Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g. 13 - Operations on 13 such as adding/deleting/multiplying etc. to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed.)<br>8 : 79<br>6 : 51</p>",
                    question_hi: "<p>24. उस विकल्प का चयन कीजिए जिसमें संख्याओं के मध्य वही संबंध है जो नीचे दिए गए युग्म की संख्याओं के मध्य है।<br>(<strong>ध्यान दें </strong>: संख्याओं को उनके घटक अंकों में विभाजित किए बिना, पूर्ण संख्याओं पर संक्रियाएं की जानी चाहिए। उदाहरण के लिए 13 को लें-13 पर की जाने वाली संक्रियाएं जैसे जोड़ना/घटाना/गुणा करना आदि केवल 13 पर की जा सकती हैं। 13 को 1 और 3 में तोड़ना और फिर 1 और 3 पर गणितीय संक्रियाएं करने की अनुमति नहीं है।)<br>8 : 79<br>6 : 51</p>",
                    options_en: ["<p>9 : 96</p>", "<p>5 : 39</p>", 
                                "<p>4 : 32</p>", "<p>7 : 62</p>"],
                    options_hi: ["<p>9 : 96</p>", "<p>5 : 39</p>",
                                "<p>4 : 32</p>", "<p>7 : 62</p>"],
                    solution_en: "<p>24.(a) <strong>Logic :</strong> (1st number)<sup>2</sup> + 15 = 2nd number<br>8 : 79 :- 8<sup>2</sup> + 15 &rArr; 64 + 15 = 79<br>6 : 51 :- 6<sup>2</sup> + 15 &rArr; 36 + 15 = 51<br>Similarly<br>9 : 96 :- 9<sup>2</sup> + 15 &rArr; 81 + 15 = 96</p>",
                    solution_hi: "<p>24.(a)<strong> तर्क :</strong> (पहली संख्या)<sup>2</sup> + 15 = दूसरी संख्या<br>8 : 79 :- 8<sup>2</sup> + 15 &rArr; 64 + 15 = 79<br>6 : 51 :- 6<sup>2</sup> + 15 &rArr; 36 + 15 = 51<br>इसी प्रकार<br>9 : 96 :- 9<sup>2</sup> + 15 &rArr; 81 + 15 = 96</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "25",
                    section: "18",
                    question_en: "<p>25. Select the option figure in which the given figure (X) is embedded (rotation is NOT allowed).<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731132150564.png\" alt=\"rId51\" width=\"106\" height=\"122\"></p>",
                    question_hi: "<p>25. उस विकल्प आकृति का चयन करें जिसमें दी गई आकृति (X) सन्निहित है (घुमाने की अनुमति नहीं है)।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731132150564.png\" alt=\"rId51\" width=\"106\" height=\"122\"></p>",
                    options_en: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731132150739.png\" alt=\"rId52\" width=\"90\" height=\"90\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731132150905.png\" alt=\"rId53\" width=\"90\" height=\"90\"></p>", 
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731132151075.png\" alt=\"rId54\" width=\"90\" height=\"90\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731132151225.png\" alt=\"rId55\" width=\"90\" height=\"90\"></p>"],
                    options_hi: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731132150739.png\" alt=\"rId52\" width=\"91\" height=\"91\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731132150905.png\" alt=\"rId53\" width=\"90\" height=\"90\"></p>",
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731132151075.png\" alt=\"rId54\" width=\"91\" height=\"91\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731132151225.png\" alt=\"rId55\" width=\"92\" height=\"92\"></p>"],
                    solution_en: "<p>25.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731132151351.png\" alt=\"rId56\" width=\"91\" height=\"93\"></p>",
                    solution_hi: "<p>25.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731132151351.png\" alt=\"rId56\" width=\"91\" height=\"93\"></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "26",
                    section: "18",
                    question_en: "<p>26. Which of the following countries has the highest Hindu population after India?</p>",
                    question_hi: "<p>26. भारत के बाद निम्नलिखित में से किस देश में सबसे अधिक हिंदू आबादी है?</p>",
                    options_en: ["<p>Bhutan</p>", "<p>Sri Lanka</p>", 
                                "<p>Myanmar</p>", "<p>Nepal</p>"],
                    options_hi: ["<p>भूटान</p>", "<p>श्रीलंका</p>",
                                "<p>म्यांमार</p>", "<p>नेपाल</p>"],
                    solution_en: "<p>26.(d) <strong>Nepal. </strong>After India, the next nine countries with the largest Hindu populations are, in decreasing order : Nepal, Bangladesh, Indonesia, Pakistan, Sri Lanka, the United States, Malaysia, the United Arab Emirates and the United Kingdom.</p>",
                    solution_hi: "<p>26.(d) <strong>नेपाल।</strong> भारत के बाद, सबसे अधिक हिंदू आबादी वाले अगले नौ देश, घटते क्रम में हैं : नेपाल, बांग्लादेश, इंडोनेशिया, पाकिस्तान, श्रीलंका, संयुक्त राज्य अमेरिका, मलेशिया, संयुक्त अरब अमीरात तथा यूनाइटेड किंगडम।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "27",
                    section: "18",
                    question_en: "<p>27. Which of the following statements about the aims of National Mission for Sustainable Agriculture (NMSA) of India is/are correct?<br>1.To make agriculture more productive.<br>2.To make agriculture more sustainable.<br>3.To promote organic farming.</p>",
                    question_hi: "<p>27. भारत के राष्ट्रीय सतत कृषि मिशन (NMSA) के बारे में निम्नलिखित में से कौन सा/से कथन सही है/हैं?<br>1.कृषि को अधिक उत्पादक बनाना।<br>2.कृषि को अधिक सतत बनाना।<br>3.जैविक खेती को बढ़ावा देना।</p>",
                    options_en: ["<p>Only 1 is correct.</p>", "<p>Only 2 and 3 are correct.</p>", 
                                "<p>1, 2 and 3 are correct.</p>", "<p>Only 1 and 2 are correct.</p>"],
                    options_hi: ["<p>केवल 1 सही है।</p>", "<p>केवल 2 और 3 सही हैं।</p>",
                                "<p>1, 2 और 3 सही हैं।</p>", "<p>केवल 1 और 2 सही हैं।</p>"],
                    solution_en: "<p>27.(c) <strong>1, 2 and 3 are correct.</strong> National Mission for Sustainable Agriculture (NMSA) has been made operational from the year 2014-15 which aims at making agriculture more productive, sustainable, remunerative and climate resilient by promoting location specific integrated /composite farming systems; soil and moisture conservation measures; comprehensive soil health management; eﬃcient water management practices and mainstreaming rainfed technologies.</p>",
                    solution_hi: "<p>27.(c) <strong>1, 2 और 3 सही हैं।</strong> राष्ट्रीय सतत कृषि मिशन (NMSA) को वर्ष 2014-15 से कार्यान्वित किया गया है, जिसका उद्देश्य स्थान-विशिष्ट एकीकृत/संयुक्त कृषि प्रणालियों को बढ़ावा देकर, मृदा एवं नमी संरक्षण उपायों, व्यापक मृदा स्वास्थ्य प्रबंधन, कुशल जल प्रबंधन प्रथाओं एवं वर्षा आधारित प्रौद्योगिकियों को मुख्यधारा में लाकर कृषि को अधिक उत्पादक, टिकाऊ, लाभकारी और जलवायु अनुकूल बनाना है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "28",
                    section: "18",
                    question_en: "<p>28. Who among the following sports persons is designated as brand ambassador for Nikshay Mitra, an initiative under Pradhan Mantri TB Mukt Bharat Abhiyaan by Ministry of Health and Family welfare of GOI in November 2022?</p>",
                    question_hi: "<p>28. नवंबर 2022 में, भारत सरकार (GOI) के स्वास्थ्य एवं परिवार कल्याण मंत्रालय ने प्रधानमंत्री टीबी मुक्त भारत अभियान के तहत शुरू की गई एक पहल, निक्षय मित्र के लिए निम्नलिखित खिलाड़ियों में से किसे ब्रांड एंबेसडर के रूप में नामित किया है?</p>",
                    options_en: ["<p>Mary Kom</p>", "<p>Abhinav Bindra</p>", 
                                "<p>Pullela Gopichand</p>", "<p>Deepa Malik</p>"],
                    options_hi: ["<p>मैरी कॉम</p>", "<p>अभिनव बिंद्रा</p>",
                                "<p>पुल्लेला गोपीचंद</p>", "<p>दीपा मलिक</p>"],
                    solution_en: "<p>28.(d) <strong>Deepa Malik. </strong>The Nikshay Mitra initiative was launched in September, 2022 as part of the Pradhan Mantri TB Mukt Bharat Abhiyaan. The initiative was first proposed by the Prime Minister in 2018. Deepa Malik is the first Indian woman to win a medal in the Paralympic Games. She won a silver medal at the 2016 Summer Paralympics in shot put.</p>",
                    solution_hi: "<p>28.(d) <strong>दीपा मलिक।</strong> निक्षय मित्र पहल सितंबर, 2022 को प्रधानमंत्री टीबी मुक्त भारत अभियान के हिस्से के रूप में शुरू की गई थी। इस पहल का प्रस्ताव पहली बार 2018 में प्रधानमंत्री द्वारा दिया गया था। दीपा मलिक पैरालंपिक खेलों में पदक जीतने वाली पहली भारतीय महिला हैं। उन्होंने 2016 के ग्रीष्मकालीन पैरालंपिक में शॉट पुट में रजत पदक जीता था।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "29",
                    section: "18",
                    question_en: "<p>29. What is subtracted from GDP to arrive at NDP?</p>",
                    question_hi: "<p>29. एनडीपी (NDP) निकालने के लिए जीडीपी (GDP) में से क्या घटाया जाता है?</p>",
                    options_en: ["<p>Subsidies</p>", "<p>Depreciation</p>", 
                                "<p>Net indirect taxes</p>", "<p>Net factor income from abroad</p>"],
                    options_hi: ["<p>अनुदान</p>", "<p>मूल्यह्रास</p>",
                                "<p>शुद्ध अप्रत्यक्ष कर</p>", "<p>विदेश से शुद्ध कारक आय</p>"],
                    solution_en: "<p>29.(b) <strong>Depreciation. </strong>Net Domestic Product at factor cost (NDP at FC) is the income earned by the factors in the form of wages, profits, rent, interest etc. NDP (Net Domestic Product) at FC (Factor Cost) = GDP (Gross Domestic product) at FC minus Depreciation. Gross Domestic Product - Final monetary value of the goods and services produced within the country during a specific period of time. It is released by the National Statistical Office (NSO).</p>",
                    solution_hi: "<p>29.(b) <strong>मूल्यह्रास। </strong>कारक लागत पर शुद्ध घरेलू उत्पाद (FC पर NDP) मजदूरी, लाभ, किराया, ब्याज आदि के रूप में कारकों द्वारा अर्जित आय है। NDP (शुद्ध घरेलू उत्पाद) पर FC (कारक लागत) = GDP (सकल घरेलू उत्पाद) पर FC घटा मूल्यह्रास। सकल घरेलू उत्पाद - किसी निश्चित अवधि के दौरान देश के भीतर उत्पादित वस्तुओं एवं सेवाओं का अंतिम मौद्रिक मूल्य है। इसे राष्ट्रीय सांख्यिकी कार्यालय (NSO) द्वारा जारी किया जाता है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "30",
                    section: "18",
                    question_en: "<p>30. Indira PP Bora is a Padma Shri Awardee 2020 for her contribution to which Indian classical dance?</p>",
                    question_hi: "<p>30. इंदिरा पी.पी. बोरा को किस भारतीय शास्त्रीय नृत्य में उनके योगदान के लिए वर्ष 2020 में पद्म श्री पुरस्कार से सम्मानित किया गया?</p>",
                    options_en: ["<p>Sattriya</p>", "<p>Kathak</p>", 
                                "<p>Kuchipudi</p>", "<p>Bharatanatyam</p>"],
                    options_hi: ["<p>सत्रीया</p>", "<p>कथक</p>",
                                "<p>कुचिपुड़ी</p>", "<p>भरतनाट्यम</p>"],
                    solution_en: "<p>30.(a) <strong>Sattriya. </strong>Indira P. P. Bora has won Sangeet Natak Akademi Award in 1996. Dance and their exponents : Sattriya - Sharodi Saikia, Guru Jatin Goswami, Guru Ghanakanta Bora, Manik Barbayan, Pushpa Bhuyan. Kathak - Pandit Birju Maharaj, Ishwari Prasad, Kumudini Lakhia, etc. Kuchipudi - Raja and Radha Reddy, Lakshmi Narayan Shastri, Swapna Sundari, etc. Bharatanatyam - Rukmini Devi Arundale, Meenakshi Sundaram Pillai, T Bala Saraswati, etc.</p>",
                    solution_hi: "<p>30.(a) <strong>सत्रीया। </strong>इंदिरा पी. पी. बोरा ने 1996 में संगीत नाटक अकादमी पुरस्कार जीता है। नृत्य एवं उनके प्रतिपादक: सत्त्रिया - शारोदी सैकिया, गुरु जतिन गोस्वामी, गुरु घनकांत बोरा, माणिक बारबायन, पुष्पा भुइयां। कथक - पंडित बिरजू महाराज, ईश्वरी प्रसाद, कुमुदिनी लाखिया आदि। कुचिपुड़ी - राजा और राधा रेड्डी, लक्ष्मी नारायण शास्त्री, स्वप्न सुंदरी, आदि। भरतनाट्यम - रुक्मिणी देवी अरुंडेल, मीनाक्षी सुंदरम पिल्लई, टी बाला सरस्वती आदि।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "31",
                    section: "18",
                    question_en: "<p>31. Follow On is related to which of the following cricket formats?</p>",
                    question_hi: "<p>31. फॉलो ऑन (Follow On) निम्नलिखित में से किस क्रिकेट प्रारूप से संबंधित है?</p>",
                    options_en: ["<p>Only T20 matches</p>", "<p>Only Test Matches</p>", 
                                "<p>Only One Day matches</p>", "<p>Both test and One-day matches</p>"],
                    options_hi: ["<p>केवल टी-20 मैच</p>", "<p>केवल टेस्ट मैच</p>",
                                "<p>केवल एक दिवसीय मैच</p>", "<p>टेस्ट और एकदिवसीय मैच दोनों</p>"],
                    solution_en: "<p>31.(b) <strong>Only Test Matches.</strong> Follow-on: The follow on forces the team batting second to bat again just after the original innings has finished. In a match of five days or more, a side which bats first and leads by at least 200 runs has the option of requiring the other side to follow-on.</p>",
                    solution_hi: "<p>31.(b) <strong>केवल टेस्ट मैच।</strong> फॉलो-ऑन: फॉलोऑन के तहत दूसरी पारी में बल्लेबाजी करने वाली टीम को मूल पारी समाप्त होने के तुरंत बाद दोबारा बल्लेबाजी करने के लिए बाध्य होना पड़ता है। पांच दिन या उससे अधिक समय तक चलने वाले मैच में, जो टीम पहले बल्लेबाजी करती है और कम से कम 200 रन की बढ़त बनाता है, उसके पास दूसरी टीम को फॉलो-ऑन करने के लिए कहने का विकल्प होता है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "32",
                    section: "18",
                    question_en: "<p>32. Which of the following is NOT a halogen gas?</p>",
                    question_hi: "<p>32. निम्नलिखित में से कौन-सी हैलोजन गैस नहीं है?</p>",
                    options_en: ["<p>F</p>", "<p>He</p>", 
                                "<p>Cl</p>", "<p>Br</p>"],
                    options_hi: ["<p>F</p>", "<p>He</p>",
                                "<p>Cl</p>", "<p>Br</p>"],
                    solution_en: "<p>32.(b) Helium (He) is a chemical element with atomic number 2. It is a colorless, odorless, tasteless, non-toxic, inert gas and the first in the Noble gas group in the periodic table. The Halogens are non-Metallic elements found in group 17 (second from the right) of the Modern periodic table. The halogen elements are Fluorine (F), Chlorine (Cl), Bromine (Br), Iodine (I), Astatine (At), and Tennessine (Ts).</p>",
                    solution_hi: "<p>32.(b) हीलियम (He) एक रासायनिक तत्व है, जिसका परमाणु क्रमांक 2 है। यह एक रंगहीन, गंधहीन, स्वादहीन, गैर विषैली, निष्क्रिय गैस है तथा आवर्त सारणी में नोबल गैस समूह में प्रथम है। हैलोजन आधुनिक आवर्त सारणी के समूह 17 (दाएं से दूसरे) में पाया जाने वाला गैर-धात्विक तत्व है। हैलोजन तत्व फ्लोरीन (F), क्लोरीन (Cl), ब्रोमीन (Br), आयोडीन (I), एस्टेटिन (At), और टेनेसीन (Ts) हैं।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "33",
                    section: "18",
                    question_en: "<p>33. Which of the following articles from Directive Principles of state policy mentions about organization of Village Panchayats?</p>",
                    question_hi: "<p>33. राज्य के नीति निदेशक तत्वों के निम्नलिखित में से किस अनुच्छेद में ग्राम पंचायतों के संगठन के बारे में उल्लेख है?</p>",
                    options_en: ["<p>Article 38</p>", "<p>Article 42</p>", 
                                "<p>Article 40</p>", "<p>Article 36</p>"],
                    options_hi: ["<p>अनुच्छेद 38</p>", "<p>अनुच्छेद 42</p>",
                                "<p>अनुच्छेद 40</p>", "<p>अनुच्छेद 36</p>"],
                    solution_en: "<p>33.(c) <strong>Article 40. </strong>Other Important Articles: Article 36 - Definition. - In this Part, unless the context otherwise requires, &ldquo;the State&rdquo; has the same meaning as in Part III. Article 38 - State to secure a social order for the promotion of welfare of the people. Article 39 - Certain principles of policy to be followed by the State. Article 41 - Right to work, to education and to public assistance in certain cases. Article 42 - Provision for just and humane conditions of work and maternity relief.</p>",
                    solution_hi: "<p>33.(c) <strong>अनुच्छेद 40. </strong>अन्य महत्त्वपूर्ण अनुच्छेद: अनुच्छेद 36 - परिभाषा। इस भाग में, जब तक कि संदर्भ से अन्यथा अपेक्षित न हो, \"राज्य\" का वही अर्थ है जो भाग III में है। अनुच्छेद 38 - राज्य लोगों के कल्याण को बढ़ावा देने के लिए एक सामाजिक व्यवस्था सुनिश्चित करेगा। अनुच्छेद 39 - राज्य द्वारा अपनाई जाने वाली नीति के कुछ सिद्धांत। अनुच्छेद 41 - कार्य करने, शिक्षा पाने और कुछ मामलों में सार्वजनिक सहायता पाने का अधिकार। अनुच्छेद 42 - काम की न्यायसंगत और मानवीय परिस्थितियों एवं मातृत्व सहायता के लिए प्रावधान।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "34",
                    section: "18",
                    question_en: "<p>34. Who among the following is a Sarod player popularly known as \'Sarod Samrat\'?</p>",
                    question_hi: "<p>34. निम्नलिखित में से कौन ऐसा सरोद वादक है जिसे \'सरोद सम्राट\' के नाम से जाना जाता है?</p>",
                    options_en: ["<p>Pandit Kishan Maharaj</p>", "<p>Pandit Ravi Shankar</p>", 
                                "<p>Ustad Amjad Ali Khan Bangash</p>", "<p>Pandit Shivkumar Sharma</p>"],
                    options_hi: ["<p>पंडित किशन महाराज</p>", "<p>पंडित रविशंकर</p>",
                                "<p>उस्ताद अमजद अली खान बंगश</p>", "<p>पंडित शिवकुमार शर्मा</p>"],
                    solution_en: "<p>34.(c) <strong>Ustad Amjad Ali Khan Bangash: </strong>Awards - Padma Shri (1975), Padma Bhushan (1991), Padma Vibhushan (2001). Other Sarod Players: Allauddin Khan, Hafiz Ali Khan, Ali Akbar Khan, Radhika Mohan Maitra, Sharan Rani Backliwal, Zarin Sharma, Buddhadev Das Gupta, Rajeev Taranath, Aashish Khan, Tejendra Majumdar etc.</p>",
                    solution_hi: "<p>34.(c) <strong>उस्ताद अमजद अली खान बंगश:</strong> पुरस्कार - पद्म श्री (1975), पद्म भूषण (1991), पद्म विभूषण (2001)। अन्य सरोद वादक: अलाउद्दीन खान, हाफिज अली खान, अली अकबर खान, राधिका मोहन मैत्रा, शरण रानी बैकलीवाल, जरीन शर्मा, बुद्धदेव दास गुप्ता, राजीव तारानाथ, आशीष खान, तेजेंद्र मजूमदार आदि।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "35",
                    section: "18",
                    question_en: "<p>35. Who among the following rulers built five astronomical observatories at different places in north India?</p>",
                    question_hi: "<p>35. निम्नलिखित में से किस शासक ने उत्तरी भारत में विभिन्न स्थानों पर पाँच खगोलीय वेधशालाएँ बनवाईं?</p>",
                    options_en: ["<p>Sawai Mansingh</p>", "<p>Maharana Pratap</p>", 
                                "<p>Sawai Jaisingh II</p>", "<p>Madho Singh</p>"],
                    options_hi: ["<p>सवाई मानसिंह</p>", "<p>महाराणा प्रताप</p>",
                                "<p>सवाई जयसिंह द्वितीय</p>", "<p>माधो सिंह</p>"],
                    solution_en: "<p>35.(c) <strong>Sawai Jaisingh II</strong> founded the city of Jaipur on 18 November 1727 AD. He was appointed the Subedar (governor) of Agra in 1722. He established five Jantar Mantar observatories, located in Delhi, Jaipur, Ujjain, Mathura, and Varanasi. Maharaja Sawai Madho Singh II was the Maharaja of Jaipur from 1880 to 1922, being the adopted son of Maharaja Ram Singh II. The Hawa Mahal was built by Maharaja Sawai Pratap Singh, the grandson of Sawai Jai Singh II, in 1799. Maharaja Man Singh II was the last ruling Maharaja of Jaipur.</p>",
                    solution_hi: "<p>35.(c) <strong>सवाई जयसिंह द्वितीय </strong>ने 18 नवंबर 1727 ई. को जयपुर शहर की स्थापना की । उन्हें 1722 में आगरा का सूबेदार (राज्यपाल) नियुक्त किया गया। उन्होंने दिल्ली, जयपुर, उज्जैन, मथुरा एवं वाराणसी में स्थित पांच जंतर-मंतर वेधशालाएं स्थापित की। महाराजा सवाई माधो सिंह द्वितीय 1880 से 1922 तक जयपुर के महाराजा थे, जो महाराजा राम सिंह द्वितीय के दत्तक पुत्र थे। हवा महल का निर्माण सवाई जय सिंह द्वितीय के पोते महाराजा सवाई प्रताप सिंह ने 1799 में करवाया था। महाराजा मान सिंह द्वितीय जयपुर रियासत के अंतिम महाराजा (शासक) थे।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "36",
                    section: "18",
                    question_en: "<p>36. Which of the following festivals is celebrated in Madhya Pradesh?</p>",
                    question_hi: "<p>36. मध्य प्रदेश में निम्नलिखित में से कौन-सा महोत्सव मनाया जाता है?</p>",
                    options_en: ["<p>Thaipusam Festival</p>", "<p>Jaisalmer Desert Festival</p>", 
                                "<p>Bihu Festival</p>", "<p>Khajuraho Dance Festival</p>"],
                    options_hi: ["<p>थाईपुसम महोत्सव</p>", "<p>जैसलमेर डेजर्ट फेस्टिवल</p>",
                                "<p>बिहू महोत्सव</p>", "<p>खजुराहो नृत्य महोत्सव</p>"],
                    solution_en: "<p>36.(d) <strong>Khajuraho Dance Festival </strong>- Organized by the Kala Parishad of the Madhya Pradesh government. It is a seven-day long festival which usually takes place from the 20th of February to the 26th of February. It is held at Khajuraho temples. It was started in 1975. Some festivals which have been organized in Madhya Pradesh are Jal Mahotsav, Mandu Mahotsav, Orchha Mahotsav, Satpura Cycle Safari, Heritage Run.</p>",
                    solution_hi: "<p>36.(d) <strong>खजुराहो नृत्य महोत्सव </strong>मध्य प्रदेश सरकार की कला परिषद द्वारा आयोजित किया जाता है। यह सात दिवसीय उत्सव है जो आमतौर पर 20 फरवरी से 26 फरवरी तक चलता है। यह खजुराहो के मंदिरों में आयोजित किया जाता है। इसकी शुरुआत 1975 में हुई थी। मध्य प्रदेश में आयोजित किए जाने वाले कुछ त्यौहार हैं जल महोत्सव, मांडू महोत्सव, ओरछा महोत्सव, सतपुड़ा साइकिल सफारी, हेरिटेज रन।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "37",
                    section: "18",
                    question_en: "<p>37. Deimachus who was a Greek ambassador came to India during the reign of the Mauryan king, _________ .</p>",
                    question_hi: "<p>37. डिमेकस जो एक यूनानी राजदूत था, मौर्य राजा ____________ के शासनकाल के दौरान भारत आया था।</p>",
                    options_en: ["<p>Shatadhanvan</p>", "<p>Bindusara</p>", 
                                "<p>Ashoka</p>", "<p>Chandragupta Maurya</p>"],
                    options_hi: ["<p>शतधन्वन</p>", "<p>बिंदुसार</p>",
                                "<p>अशोक</p>", "<p>चन्द्रगुप्त मौर्य</p>"],
                    solution_en: "<p>37.(b) <strong>Bindusara</strong> also known as \"Amitrochates\" or \"Amitragata\" by the Greeks, was the son of Chandragupta Maurya. Bindusara patronized the Ajivikas and was taught by the Ajivika Brahmin Pingalavatsa. Chandragupta Maurya : At age 25, he captured Pataliputra from the last Nanda ruler, with the help of Kautilya (Chanakya or Vishnugupta). Ashoka : The son of Bindusara, was the first ruler to communicate with people through inscriptions.</p>",
                    solution_hi: "<p>37.(b) <strong>बिन्दुसार </strong>को यूनानियों द्वारा \"अमित्रोचेट्स\" या \"अमित्रगता\" के नाम से भी जाना जाता था, वह चंद्रगुप्त मौर्य के पुत्र थे। बिन्दुसार ने आजीविकों को संरक्षण दिया और उन्हें आजीविक ब्राह्मण पिंगलवत्स द्वारा शिक्षा दी गई। चंद्रगुप्त मौर्य: 25 वर्ष की आयु में कौटिल्य (चाणक्य या विष्णुगुप्त) की सहायता से अंतिम नंद शासक से पाटलिपुत्र पर अधिकार कर लिया। अशोक: बिन्दुसार के पुत्र, शिलालेखों के माध्यम से लोगों से संवाद (वार्तालाप) करने वाले पहले शासक थे।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "38",
                    section: "18",
                    question_en: "<p>38. Which of the following does NOT belong to the category of Porifera?</p>",
                    question_hi: "<p>38. निम्नलिखित में से कौन-सा पोरिफ़ेरा (Porifera) की श्रेणी से संबंधित नहीं है?</p>",
                    options_en: ["<p>Hydra</p>", "<p>Sycon</p>", 
                                "<p>Spongilla</p>", "<p>Euplectella</p>"],
                    options_hi: ["<p>हाइड्रा (Hydra)</p>", "<p>साइकॉन (Sycon)</p>",
                                "<p>स्पोंजिला (Spongilla)</p>", "<p>यूप्लेक्टेला (Euplectella)</p>"],
                    solution_en: "<p>38.(a) <strong>Hydra - </strong>belongs to the phylum Cnidaria. Example of phylum Cnidaria - jellyfish, corals, sea anemones, etc. Phylum Porifera : Members of this phylum are commonly known as sponges. They are generally marine and mostly asymmetrical animals. Sponges have a water transport or canal system.</p>",
                    solution_hi: "<p>38.(a) <strong>हाइड्रा (Hydra)</strong> - निडेरिया संघ से संबंधित है। निडेरिया संघ के उदाहरण - जेलीफ़िश, कोरल, समुद्री एनीमोन, आदि। पोरिफ़ेरा संघ: इस संघ के सदस्य को सामान्यतः स्पंज कहा जाता है। ये प्रायः समुद्री होते हैं और अधिकतर असममित जानवर होते हैं। स्पंज में जल परिवहन या नहर प्रणाली होती है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "39",
                    section: "18",
                    question_en: "<p>39. Which is a serious contagious bacterial infection that usually affects the mucous membranes of the nose and throat?</p>",
                    question_hi: "<p>39. कौन-सा एक गंभीर संक्रामक जीवाणु से फैलने वाला संक्रमण है जो आमतौर पर नाक और गले की श्लेष्मा झिल्ली को प्रभावित करता है?</p>",
                    options_en: ["<p>Meningococcal</p>", "<p>Diphtheria</p>", 
                                "<p>Shigellosis</p>", "<p>Chlamydia</p>"],
                    options_hi: ["<p>मेनिंगोकॉकल (Meningococcal)</p>", "<p>डिप्थीरिया (Diphtheria)</p>",
                                "<p>शिगेलॉसिस (Shigellosis)</p>", "<p>क्लेमाइडिया (Chlamydia)</p>"],
                    solution_en: "<p>39.(b) <strong>Diphtheria.</strong> Meningococcal disease is a serious bacterial illness that can lead to severe swelling of the tissues surrounding the brain and spinal cord or infection of the bloodstream. Shigellosis is an infection of the intestines caused by Shigella bacteria. Chlamydia is a common sexually transmitted infection (STI) caused by the bacteria Chlamydia trachomatis.</p>",
                    solution_hi: "<p>39.(b) <strong>डिप्थीरिया</strong> (Diphtheria)। मेनिंगोकॉकल (Meningococcal) रोग एक गंभीर जीवाणुजनित संक्रमण है जो मस्तिष्क और रीढ़ की हड्डी के आस-पास के ऊतकों में गंभीर सूजन या रक्तप्रवाह के संक्रमण का कारण बन सकती है। शिगेलॉसिस (Shigellosis) आंतों का एक संक्रमण है जो शिगेला बैक्टीरिया के कारण होता है। क्लेमाइडिया (Chlamydia) एक सामान्य यौन संचारित संक्रमण (STI) है जो क्लैमाइडिया ट्रैकोमैटिस बैक्टीरिया के कारण होता है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "40",
                    section: "18",
                    question_en: "<p>40. The Maharashtra Bhushan was conferred upon __________ in the year 2002.</p>",
                    question_hi: "<p>40. वर्ष 2002 में ____________ को महाराष्ट्र भूषण प्रदान किया गया था।</p>",
                    options_en: ["<p>Pandit Ravi Shankar</p>", "<p>Pandit Bhimsen Joshi</p>", 
                                "<p>Pandit Jasraj</p>", "<p>Pandit Hari Prasad Chaurasia</p>"],
                    options_hi: ["<p>पंडित रवि शंकर</p>", "<p>पंडित भीमसेन जोशी</p>",
                                "<p>पंडित जसराज</p>", "<p>पंडित हरि प्रसाद चौरसिया</p>"],
                    solution_en: "<p>40.(b) <strong>Pandit Bhimsen Joshi</strong>. He belongs to the Kirana gharana tradition of Hindustani Classical Music. Awards - Bharat Ratna (2009), Padma Vibhushan (1999), Padma Bhushan (1985), Padma Shri (1972), Sangeet Natak Akademi Award for Hindustani vocal music (1975), Aditya Vikram Birla Kalashikhar Puraskar (2000).</p>",
                    solution_hi: "<p>40.(b)<strong> पंडित भीमसेन जोशी</strong> हिंदुस्तानी शास्त्रीय संगीत के किराना घराना से संबंधित हैं। पुरस्कार - भारत रत्न (2009), पद्म विभूषण (1999), पद्म भूषण (1985), पद्म श्री (1972), हिंदुस्तानी गायन के लिए संगीत नाटक अकादमी पुरस्कार (1975), आदित्य विक्रम बिड़ला कलाशिखर पुरस्कार (2000)।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "41",
                    section: "18",
                    question_en: "<p>41. Which article of the Constitution of India lays down that it shall be the duty of the Advocate-General to give advice to the Government of the State upon legal matters?</p>",
                    question_hi: "<p>41. भारत के संविधान के किस अनुच्छेद में कहा गया है कि कानूनी मामलों पर राज्य सरकार को सलाह देना महाधिवक्ता का कर्तव्य होगा?</p>",
                    options_en: ["<p>Article 168</p>", "<p>Article 165</p>", 
                                "<p>Article 167</p>", "<p>Article 166</p>"],
                    options_hi: ["<p>अनुच्छेद 168</p>", "<p>अनुच्छेद 165</p>",
                                "<p>अनुच्छेद 167</p>", "<p>अनुच्छेद 166</p>"],
                    solution_en: "<p>41.(b) <strong>Article 165.</strong> Other Important Articles: Article 166 - Conduct of business of the Government of a State. Article 167 - Duties of Chief Minister as respects the furnishing of information to the Governor, etc. Article 168 - Constitution of Legislatures in States.</p>",
                    solution_hi: "<p>41.(b) <strong>अनुच्छेद 165.</strong> अन्य महत्त्वपूर्ण अनुच्छेद: अनुच्छेद 166 - राज्य सरकार का कार्य संचालन। अनुच्छेद 167 - राज्यपाल को सूचना देने आदि के संबंध में मुख्यमंत्री के कर्तव्य। अनुच्छेद 168 - राज्यों में विधानमंडलों का गठन।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "42",
                    section: "18",
                    question_en: "<p>42. What is the tenure of office for the Chief Election Commissioner?</p>",
                    question_hi: "<p>42. मुख्य चुनाव आयुक्त पद का कार्यकाल कितना होता है?</p>",
                    options_en: ["<p>6 years or up to the age of 65 years</p>", "<p>5 years or up to the age of 65 years</p>", 
                                "<p>5 years or up to the age of 60 years</p>", "<p>6 years or up to the age of 60 years</p>"],
                    options_hi: ["<p>6 वर्ष या 65 वर्ष की आयु तक</p>", "<p>5 वर्ष या 65 वर्ष की आयु तक</p>",
                                "<p>5 वर्ष या 60 वर्ष की आयु तक</p>", "<p>6 वर्ष या 60 वर्ष की आयु तक</p>"],
                    solution_en: "<p>42.(a) <strong>6 years or up to the age of 65 years.</strong> Article 324 - Superintendence, direction and control of elections to be vested in an Election Commission : (2) The Election Commission shall consist of the Chief Election Commissioner and such number of other Election Commissioners, if any, as the President may from time to time fix and the appointment of the Chief Election Commissioner and other Election Commissioners shall, subject to the provisions of any law made in that behalf by Parliament, be made by the President.</p>",
                    solution_hi: "<p>42.(a) <strong>6 वर्ष या 65 वर्ष की आयु तक। </strong>अनुच्छेद 324 - चुनावों का अधीक्षण, निर्देशन और नियंत्रण चुनाव आयोग में निहित होना: (2) निर्वाचन आयोग मुख्य निर्वाचन आयुक्त और उतनी संख्या में अन्य निर्वाचन आयुक्तों से मिलकर बनेगा, यदि कोई हों, जितने राष्ट्रपति समय-समय पर नियत करें एवं मुख्य निर्वाचन आयुक्त तथा अन्य निर्वाचन आयुक्तों की नियुक्ति, संसद द्वारा इस निमित्त बनाई गई किसी विधि के उपबंधों के अधीन रहते हुए, राष्ट्रपति द्वारा की जाएगी।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "43",
                    section: "18",
                    question_en: "<p>43. What was a significant negative environmental impact of the Green Revolution?</p>",
                    question_hi: "<p>43. हरित क्रांति का पर्यावरण पर महत्वपूर्ण नकारात्मक प्रभाव क्या था?</p>",
                    options_en: ["<p>Deforestation</p>", "<p>Soil degradation</p>", 
                                "<p>Increase in air pollution</p>", "<p>Ozone layer depletion</p>"],
                    options_hi: ["<p>वनों की कटाई</p>", "<p>मृदा क्षरण</p>",
                                "<p>वायु प्रदूषण में वृद्धि</p>", "<p>ओजोन परत का क्षरण</p>"],
                    solution_en: "<p>43.(b) <strong>Soil degradation. </strong>The Green Revolution caused soil fertility loss through the use of chemical fertilizers. Chemical fertilizers also kill bacteria and other microorganisms in the soil. Green Revolution also led to groundwater depletion due to extensive tubewell irrigation, impacting long-term environmental resources.</p>",
                    solution_hi: "<p>43.(b) <strong>मृदा क्षरण।</strong> हरित क्रांति के कारण रासायनिक उर्वरकों के उपयोग से मिट्टी की उर्वरता में कमी आयी है। रासायनिक उर्वरक मिट्टी में बैक्टीरिया और अन्य सूक्ष्मजीवों को भी मारते हैं। हरित क्रांति से व्यापक नलकूप सिंचाई के कारण भूजल में कमी आई, जिससे दीर्घकालिक पर्यावरणीय संसाधनों पर प्रभाव पड़ा।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "44",
                    section: "18",
                    question_en: "<p>44. Which of the following peninsular rivers falls into the Arabian Sea?</p>",
                    question_hi: "<p>44. निम्नलिखित में से कौन-सी प्रायद्वीपीय नदी अरब सागर में गिरती है?</p>",
                    options_en: ["<p>Mahanadi river</p>", "<p>Krishna river</p>", 
                                "<p>Sabarmati river</p>", "<p>Godavari river</p>"],
                    options_hi: ["<p>महानदी नदी</p>", "<p>कृष्णा नदी</p>",
                                "<p>साबरमती नदी</p>", "<p>गोदावरी नदी</p>"],
                    solution_en: "<p>44.(c) <strong>Sabarmati river.</strong> It is one of the major west-flowing rivers in India. It originates from Aravalli hills. Mahanadi river: Origin - Sihawa (South of Amarkantak) near Raipur (Chhattisgarh). Flows through Chhattisgarh and Odisha and drains into the Bay of Bengal. Godavari River (Dakshin Ganga) : Origin - Trimbakeshwar near Nashik in Maharashtra. It has Largest Peninsular River system and falls into the Bay of Bengal.</p>",
                    solution_hi: "<p>44.(c) <strong>साबरमती नदी।</strong> यह भारत में पश्चिम की ओर बहने वाली प्रमुख नदियों में से एक है। इसका उद्गम अरावली पहाड़ियों से होता है। महानदी नदी: उद्गम - रायपुर (छत्तीसगढ़) के पास सिहावा (अमरकंटक के दक्षिण में)। छत्तीसगढ़ और ओडिशा से होकर बहती है तथा बंगाल की खाड़ी में गिरती है। गोदावरी नदी (दक्षिण गंगा): उद्गम - महाराष्ट्र में नासिक के पास त्र्यंबकेश्वर। यह सबसे बड़ी प्रायद्वीपीय नदी प्रणाली है और बंगाल की खाड़ी में गिरती है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "45",
                    section: "18",
                    question_en: "<p>45. In the ancient temple of Mahadev at Mahabaleshwar, the spout emerging from the mouth of a cow statue is the source of which river basin?</p>",
                    question_hi: "<p>45. महाबलेश्वर में महादेव के प्राचीन मंदिर में गाय की मूर्ति के मुख से निकलने वाली धारा किस नदी द्रोणी का स्रोत है?</p>",
                    options_en: ["<p>Kaveri</p>", "<p>Krishna</p>", 
                                "<p>Godavari</p>", "<p>Narmada</p>"],
                    options_hi: ["<p>कावेरी</p>", "<p>कृष्णा</p>",
                                "<p>गोदावरी</p>", "<p>नर्मदा</p>"],
                    solution_en: "<p>45.(b) <strong>Krishna</strong>. It is the second largest east flowing Peninsular river which rises near Mahabaleshwar in Sahyadri. Origin of rivers : Kaveri - originates at Talakaveri, which is located on the Brahmagiri range in the Kodagu (Coorg) district of Karnataka. Godavari - rises from Trimbakeshwar in the Nashik district of Maharashtra. Narmada - rises from Maikala range near Amarkantak in Madhya Pradesh.</p>",
                    solution_hi: "<p>45.(b) <strong>कृष्णा।</strong> यह दूसरी सबसे बड़ी पूर्व की ओर बहने वाली प्रायद्वीपीय नदी है जो सह्याद्रि में महाबलेश्वर के पास से निकलती है। नदियों का उद्गम: कावेरी - तालकावेरी से निकलती है, जो कर्नाटक के कोडागु (कूर्ग) जिले में ब्रह्मगिरि पर्वतमाला पर स्थित है। गोदावरी - महाराष्ट्र के नासिक जिले में त्र्यंबकेश्वर से निकलती है। नर्मदा - मध्य प्रदेश में अमरकंटक के पास मैकाल पर्वतमाला से निकलती है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "46",
                    section: "18",
                    question_en: "<p>46. Which of the following animals was the emblem of the Chola dynasty?</p>",
                    question_hi: "<p>46. निम्नलिखित में से कौन-सा जानवर चोल राजवंश का प्रतीक था?</p>",
                    options_en: ["<p>Wolf</p>", "<p>Horse</p>", 
                                "<p>Elephant</p>", "<p>Tiger</p>"],
                    options_hi: ["<p>भेड़िया</p>", "<p>घोड़ा</p>",
                                "<p>हाथी</p>", "<p>बाघ</p>"],
                    solution_en: "<p>46.(d) <strong>Tiger. </strong>Three dynasties ruled during the Sangam Age. Cheras: Modern-Day City (Kerala), Ancient Capital (Vanji), Important Ports (Musiri, Tondi), Emblem (Bow and Arrow). Cholas: Modern-Day City (Tamil Nadu), Ancient Capital (Uraiyur, Puhar), Important Ports (Kaveripattinam). Pandyas: Modern-Day City (Tamil Nadu), Ancient Capital (Madurai), Important Ports {Muziris (Muchiri), Korkai, Kaveri}, Emblem (Carp).</p>",
                    solution_hi: "<p>46.(d) <strong>बाघ।</strong> संगम युग के दौरान तीन राजवंशों ने शासन किया। चेर: आधुनिक शहर (केरल), प्राचीन राजधानी (वज्जि), महत्त्वपूर्ण बंदरगाह (मुसिरी, तोंडी), प्रतीक (धनुष और बाण)। चोल: आधुनिक शहर (तमिलनाडु), प्राचीन राजधानी (उरैयूर, पुहार), महत्त्वपूर्ण बंदरगाह (कावेरीपत्तनम)। पांड्य: आधुनिक शहर (तमिलनाडु), प्राचीन राजधानी (मदुरै), महत्त्वपूर्ण बंदरगाह {मुज़िरिस (मुचिरी), कोरकई, कावेरी}, प्रतीक (मछली)।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "47",
                    section: "18",
                    question_en: "<p>47. Samata Sainik Dal was formed in 1927 by whom among the following personalities?</p>",
                    question_hi: "<p>47. 1927 में समता सैनिक दल की स्थापना निम्नलिखित में से किस व्यक्ति के द्वारा की गई थी?</p>",
                    options_en: ["<p>Savitribai Phule</p>", "<p>BR Ambedkar</p>", 
                                "<p>Mahatma Gandhi</p>", "<p>Narayana Guru</p>"],
                    options_hi: ["<p>सावित्रीबाई फुले</p>", "<p>बी.आर. अंबेडकर</p>",
                                "<p>महात्मा गांधी</p>", "<p>नारायण गुरु</p>"],
                    solution_en: "<p>47.(b) <strong>BR Ambedkar</strong>. He founded &lsquo;Bahishkrit Hitakarini Sabha\' in 1924 to promote and uplift the education of untouchables. In 1936, he founded an &ldquo;Independent Labour Party&rdquo;, which participated in the Elections of Bombay held in 1937. Mahatma Gandhi founded - Harijan Sevak Sangh in 1932. Narayana Guru founded the Sree Narayana Dharma Paripalana Yogam (SNDP) in 1903.</p>",
                    solution_hi: "<p>47.(b)<strong> बी.आर. अंबेडकर। </strong>उन्होंने अछूतों की शिक्षा को बढ़ावा देने और उनके उत्थान के लिए 1924 में \'बहिष्कृत हितकारिणी सभा\' ​​की स्थापना की। 1936 में, उन्होंने एक \"स्वतंत्र लेबर पार्टी\" की स्थापना की, जिसने 1937 में आयोजित बॉम्बे के चुनावों में भाग लिया। महात्मा गांधी ने 1932 में &lsquo;हरिजन सेवक संघ&rsquo; की स्थापना की। नारायण गुरु ने 1903 में श्री नारायण धर्म परिपालन योगम (SNDP) की स्थापना की थी।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "48",
                    section: "18",
                    question_en: "<p>48. On 29<sup>th</sup> October 2020, the Ministry of Science and Technology launched the SERB-POWER Scheme. What is the objective of the scheme?</p>",
                    question_hi: "<p>48. 29 अक्टूबर, 2020 को विज्ञान एवं प्रौद्योगिकी मंत्रालय ने SERB-POWER योजना का शुभारंभ किया। उस योजना का लक्ष्य क्या है?</p>",
                    options_en: ["<p>To allow reservation for women in the Parliament and Judiciary</p>", "<p>To strengthen the solar programme of India with France</p>", 
                                "<p>To mitigate gender disparity in science and engineering research funding in various academic programmes and laboratories</p>", "<p>To allow reservation for women in the Ministry of Science and Technology</p>"],
                    options_hi: ["<p>संसद और न्यायपालिका में महिला-आरक्षण की अनुमति देना</p>", "<p>फ्रांस के साथ भारतीय सौर कार्यक्रम को सुदृढ़ करना</p>",
                                "<p>विभिन्न अकादमिक कार्यक्रमों और प्रयोगशालाओं में विज्ञान और प्रौद्योगिकी अनुसंधानार्थ वित्तपोषण में लैंगिक असमानता को न्यूनतम करना</p>", "<p>विज्ञान एवं प्रौद्योगिकी मंत्रालय में महिलाओं के लिए आरक्षण की अनुमति देना</p>"],
                    solution_en: "<p>48.(c) SERB-POWER (Promoting Opportunities for Women in Exploratory Research) program is a scheme by the Ministry of Science and Technology in India to reduce gender disparity in science and engineering research funding. The program offers two types of support : SERB-POWER Fellowship and SERB-POWER Research Grants.</p>",
                    solution_hi: "<p>48.(c) SERB-POWER (प्रमोटिंग अपॉर्चुनिटीज फ़ॉर वूमेन इन एक्सप्लोरेटरी रिसर्च) कार्यक्रम विज्ञान और इंजीनियरिंग अनुसंधान निधि में लैंगिक असमानता को कम करने के लिए भारत में विज्ञान और प्रौद्योगिकी मंत्रालय की एक योजना है। कार्यक्रम दो प्रकार की सहायता प्रदान करता है: SERB-POWER फ़ेलोशिप और SERB-POWER अनुसंधान अनुदान।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "49",
                    section: "18",
                    question_en: "<p>49. Which of the following was the mascot of the Khelo India University Games 2022?</p>",
                    question_hi: "<p>49. निम्नलिखित में से कौन खेलो इंडिया यूनिवर्सिटी गेम्स 2022 का शुभंकर था?</p>",
                    options_en: ["<p>Bheema</p>", "<p>Moga</p>", 
                                "<p>Jitu</p>", "<p>Saavaj</p>"],
                    options_hi: ["<p>भीमा</p>", "<p>मोगा</p>",
                                "<p>जीतू</p>", "<p>सावज</p>"],
                    solution_en: "<p>49.(c) <strong>Jitu.</strong> Maskot of KIUG 2023 - Ashtalakshmi (a butterfly). Mascot of KIUG 2021 - Veera (an elephant). Khelo India University Games (KIUG) : First event - 2020, in Bhubaneswar, Odisha. It occurs annually. KIUG 2024: Winner : Chandigarh University, Host : seven north-eastern states .</p>",
                    solution_hi: "<p>49.(c)<strong> जीतू। </strong>KIUG 2023 का शुभंकर - अष्टलक्ष्मी (तितली)। KIUG 2021 का शुभंकर - वीरा (हाथी)। खेलो इंडिया यूनिवर्सिटी गेम्स (KIUG): पहला आयोजन - 2020, भुवनेश्वर, ओडिशा में। यह प्रत्येक वर्ष होता है। KIUG 2024: विजेता: चंडीगढ़ विश्वविद्यालय, मेज़बान: सात पूर्वोत्तर राज्य।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "50",
                    section: "17",
                    question_en: "<p>50. The chemical formula of glucose is C<sub>6</sub>H<sub>12</sub>O<sub>6</sub>. The weight % of carbon in glucose is:</p>",
                    question_hi: "<p>50. ग्लूकोज का रासायनिक सूत्र C<sub>6</sub>H<sub>12</sub>O<sub>6</sub> है। ग्लूकोज में कार्बन का भार प्रतिशत (%) कितना होता है।</p>",
                    options_en: ["<p>40</p>", "<p>72</p>", 
                                "<p>53</p>", "<p>25</p>"],
                    options_hi: ["<p>40</p>", "<p>72</p>",
                                "<p>53</p>", "<p>25</p>"],
                    solution_en: "<p>50.(a)&nbsp;<strong>40.</strong> Calculate the molar mass of glucose (C<sub>6</sub>H<sub>12</sub>O<sub>6</sub>)</p>\n<p>= (6 &times; 12) + (12 &times; 1) + (6 &times; 16)</p>\n<p>= 72 + 12 + 96 = 180 g/mol.&nbsp;<br>Total mass of carbon (C) = 6 &times; 12 = 72 g.<br>We Know that,<br>Percentage by Weight = <math display=\"inline\"><mfrac><mrow><mi>W</mi><mi>e</mi><mi>i</mi><mi>g</mi><mi>h</mi><mi>t</mi><mi>&#160;</mi><mi>o</mi><mi>f</mi><mi>&#160;</mi><mi>c</mi><mi>a</mi><mi>r</mi><mi>b</mi><mi>o</mi><mi>n</mi></mrow><mrow><mi>W</mi><mi>e</mi><mi>i</mi><mi>g</mi><mi>h</mi><mi>t</mi><mi>&#160;</mi><mi>o</mi><mi>f</mi><mi>&#160;</mi><mi>G</mi><mi>l</mi><mi>u</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>e</mi></mrow></mfrac></math> &times; 100 <br>&rArr; <math display=\"inline\"><mfrac><mrow><mn>72</mn></mrow><mrow><mn>180</mn></mrow></mfrac></math> &times; 100 = 40 %.</p>",
                    solution_hi: "<p>50.(a) <strong>40.</strong> ग्लूकोज़ (C<sub>6</sub>H<sub>12</sub>O<sub>6</sub>) का मोलर द्रव्यमान की गणना&nbsp;</p>\n<p>= (6 &times; 12) + (12 &times; 1) + (6 &times; 16)</p>\n<p>= 72 + 12 + 96 = 180 ग्राम/मोल</p>\n<p>कार्बन का कुल द्रव्यमान (C) = 6 &times; 12 = 72 g.<br>हम जानते हैं कि,<br>भार प्रतिशत = <math style=\"font-family: Verdana;\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2325;&#2366;&#2352;&#2381;&#2348;&#2344;</mi><mo>&#160;</mo><mi>&#2325;&#2366;</mi><mo>&#160;</mo><mi>&#2349;&#2366;&#2352;</mi></mrow><mrow><mi>&#2327;&#2381;&#2354;&#2370;&#2325;&#2379;&#2332;</mi><mo>&#160;</mo><mi>&#2325;&#2366;</mi><mo>&#160;</mo><mi>&#2349;&#2366;&#2352;</mi></mrow></mfrac></math> &times; 100 <br>&rArr; <math display=\"inline\"><mfrac><mrow><mn>72</mn></mrow><mrow><mn>180</mn></mrow></mfrac></math> &times; 100 = 40 %.</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "51",
                    section: "17",
                    question_en: "<p>51. Study the following table and answer the question that follows.<br>Expenditures of a Company (in Lakh Rupees) Per Annum over Different Heads in the&nbsp;<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731132151516.png\" alt=\"rId57\" width=\"368\" height=\"119\"> <br>The total amount of bonus paid by the company during the given period is approximately what percentage of the total amount of salary paid during this period? (Rounded off to the nearest integer.)</p>",
                    question_hi: "<p>51. निम्नलिखित तालिका का अध्ययन कीजिए और उसके बाद आने वाले प्रश्न का उत्तर दीजिए।<br>दिए गए वर्षों में विभिन्न मदों में प्रति वर्ष कंपनी का व्यय (लाख रुपये में)।<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731132151679.png\" alt=\"rId58\" width=\"310\" height=\"167\"> <br>दी गई अवधि के दौरान कंपनी द्वारा भुगतान की गई बोनस की कुल राशि, इस अवधि के दौरान भुगतान की गई वेतन की कुल राशि का लगभग कितने प्रतिशत है? (निकटतम पूर्णांक तक पूर्णांकित।)</p>",
                    options_en: ["<p>2%</p>", "<p>1.5%</p>", 
                                "<p>2.5%</p>", "<p>1%</p>"],
                    options_hi: ["<p>2%</p>", "<p>1.5%</p>",
                                "<p>2.5%</p>", "<p>1%</p>"],
                    solution_en: "<p>51.(d)Total bonus paid by the company during the given period</p>\n<p>= 3.84 + 3.68 + 3.00 + 2.52 + 3.96 = 17<br>Total amount of salary paid during this period</p>\n<p>= 310 + 324 + 338 + 346 + 410 = 1728<br>Required percentage = <math display=\"inline\"><mfrac><mrow><mn>17</mn><mi>&#160;</mi></mrow><mrow><mn>1728</mn><mi>&#160;</mi></mrow></mfrac></math> &times; 100 = 0.98 ⋍ 1%</p>",
                    solution_hi: "<p>51.(d) दी गई अवधि के दौरान कंपनी द्वारा भुगतान किया गया कुल बोनस</p>\n<p>= 3.84 + 3.68 + 3.00 + 2.52 + 3.96&nbsp; = 17<br>इस अवधि के दौरान भुगतान की गई वेतन की कुल राशि</p>\n<p>= 310 + 324 + 338 + 346 + 410 = 1728<br>आवश्यक प्रतिशत = <math display=\"inline\"><mfrac><mrow><mn>17</mn><mi>&#160;</mi></mrow><mrow><mn>1728</mn><mi>&#160;</mi></mrow></mfrac></math> &times; 100 = 0.98 ⋍ 1%</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "52",
                    section: "17",
                    question_en: "<p>52. Two candidates contested in an election. 80% of the voters cast their votes, out of which 5% of the polled votes were invalid. The winning candidate got 70% of valid votes and won by a majority of 36,480 votes. Find the total number of voters.</p>",
                    question_hi: "<p>52. एक चुनाव में दो उम्मीदवारों ने चुनाव लड़ा। 80% मतदाताओं ने अपने मत डाले, जिनमें से 5% मत अवैध पाए गए। विजयी उम्मीदवार को 70% वैध मत मिले और वह 36,480 मतों से चुनाव जीत गया। मतदाताओं की कुल संख्या की गणना करें।</p>",
                    options_en: ["<p>1,15,000</p>", "<p>1,20,000</p>", 
                                "<p>1,25,000</p>", "<p>1,18,000</p>"],
                    options_hi: ["<p>1,15,000</p>", "<p>1,20,000</p>",
                                "<p>1,25,000</p>", "<p>1,18,000</p>"],
                    solution_en: "<p>52.(b) Let total number of voters = 100 unit <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731132151841.png\" alt=\"rId59\" width=\"180\" height=\"176\"><br>Now , according to question <br>&rArr; 30.4 unit = 36,480<br>&rArr; 1 unit = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>36</mn><mo>,</mo><mn>480</mn></mrow><mrow><mn>30</mn><mo>.</mo><mn>4</mn></mrow></mfrac></math><br>&rArr; 100 unit = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>36</mn><mo>,</mo><mn>480</mn></mrow><mrow><mn>30</mn><mo>.</mo><mn>4</mn></mrow></mfrac></math> &times; 100 = 1,20, 000</p>",
                    solution_hi: "<p>52.(b) माना मतदाताओं की कुल संख्या = 100 इकाई <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731132152008.png\" alt=\"rId60\" width=\"159\" height=\"154\"><br>अब, प्रश्न के अनुसार <br>&rArr; 30.4 इकाई = 36,480<br>&rArr; 1 इकाई =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>36</mn><mo>,</mo><mn>480</mn></mrow><mrow><mn>30</mn><mo>.</mo><mn>4</mn></mrow></mfrac></math><br>&rArr; 100 इकाई =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>36</mn><mo>,</mo><mn>480</mn></mrow><mrow><mn>30</mn><mo>.</mo><mn>4</mn></mrow></mfrac></math> &times; 100 = 1,20, 000</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "53",
                    section: "17",
                    question_en: "<p>53. The given bar graph shows the actual production and targeted production of electric cars from 2019 to 2022. Study the given bar graph and answer the question that follows.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731132152213.png\" alt=\"rId61\" width=\"231\" height=\"201\"> <br>What is the ratio of the total targeted production of cars in 2021 and 2022 to the actual production of cars from 2019 to 2022 taken together?</p>",
                    question_hi: "<p>53. दिया गया बार ग्राफ 2019 से 2022 तक विद्युत् कारों के वास्तविक उत्पादन और लक्षित उत्पादन को दर्शाता है। दिए गए बार ग्राफ का अध्ययन कीजिए और निम्नलिखित प्रश्न का उत्तर दीजिए।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731132152440.png\" alt=\"rId62\" width=\"245\" height=\"213\"> <br>2021 तथा 2022 में कारों के कुल लक्षित उत्पादन और 2019 से 2022 तक कारों के कुल वास्तविक उत्पादन का अनुपात क्या&nbsp;है?<br>Bar Graph showing the actual production and targeted production of electric cars from 2019 to 2022 = बार<br>ग्राफ 2019 से 2022 तक विद्युत् कारों के वास्तविक उत्पादन और लक्षित उत्पादन को दर्शाता है।<br>Number of Cars = कारों की संख्या, Years = वर्ष, Actual= वास्तविक, Targeted = लक्षित</p>",
                    options_en: ["<p>32 : 25</p>", "<p>25 : 36</p>", 
                                "<p>36 : 25</p>", "<p>25 : 32</p>"],
                    options_hi: ["<p>32 : 25</p>", "<p>25 : 36</p>",
                                "<p>36 : 25</p>", "<p>25 : 32</p>"],
                    solution_en: "<p>53.(d) Total targeted production of cars in 2021 and 2022 = 70 + 80 = 150<br>The actual production of cars from 2019 to 2022 taken together = 36 + 32 + 59 + 65 = 192<br>Desired ratio = 150 : 192 <br>= 25 : 32</p>",
                    solution_hi: "<p>53.(d) 2021 और 2022 में कारों का कुल लक्षित उत्पादन = 70 + 80 = 150<br>2019 से 2022 तक कारों का कुल वास्तविक उत्पादन = 36 + 32 + 59 + 65 =192<br>वांछित अनुपात = 150 : 192 <br>= 25 : 32</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "54",
                    section: "17",
                    question_en: "<p>54. M and N are, respectively, the centres of two circles of radii 12 cm and 8 cm. QR is the common tangent to the circles. If MN = 16 cm, then what will be the length of QR?</p>",
                    question_hi: "<p>54. M और N क्रमशः 12 cm और 8 cm त्रिज्या वाले दो वृत्तों के केंद्र हैं। QR वृत्तों की उभयनिष्ठ स्पशरिखा है। यदि MN =16 cm है, तो QR की लंबाई कितनी होगी?</p>",
                    options_en: ["<p>5<math display=\"inline\"><msqrt><mn>17</mn></msqrt></math> cm</p>", "<p>4<math display=\"inline\"><msqrt><mn>15</mn></msqrt></math> cm</p>", 
                                "<p>3<math display=\"inline\"><msqrt><mn>19</mn></msqrt></math> cm</p>", "<p>6<math display=\"inline\"><msqrt><mn>8</mn></msqrt></math> cm</p>"],
                    options_hi: ["<p>5<math display=\"inline\"><msqrt><mn>17</mn></msqrt></math> cm</p>", "<p>4<math display=\"inline\"><msqrt><mn>15</mn></msqrt></math> cm</p>",
                                "<p>3<math display=\"inline\"><msqrt><mn>19</mn></msqrt></math> cm</p>", "<p>6<math display=\"inline\"><msqrt><mn>8</mn></msqrt></math> cm</p>"],
                    solution_en: "<p>54.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731132152549.png\" alt=\"rId63\" width=\"186\" height=\"109\"><br>Length of QR = <math display=\"inline\"><msqrt><msup><mrow><mi mathvariant=\"bold-italic\">d</mi></mrow><mrow><mn>2</mn></mrow></msup><mi mathvariant=\"bold-italic\">&#160;</mi><mo>-</mo><msup><mrow><mi mathvariant=\"bold-italic\">&#160;</mi><mo>(</mo><mn>12</mn><mi mathvariant=\"bold-italic\">&#160;</mi><mo>-</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mn>8</mn><mi mathvariant=\"bold-italic\">&#160;</mi><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup></msqrt></math><br>= <math display=\"inline\"><msqrt><mn>256</mn><mi mathvariant=\"bold-italic\">&#160;</mi><mo>-</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mn>16</mn></msqrt></math><br>= <math display=\"inline\"><msqrt><mn>240</mn></msqrt></math> = 4<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>15</mn></msqrt></math> cm</p>",
                    solution_hi: "<p>54.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731132152549.png\" alt=\"rId63\" width=\"203\" height=\"119\"><br>QR की लंबाई = <math display=\"inline\"><msqrt><msup><mrow><mi mathvariant=\"bold-italic\">d</mi></mrow><mrow><mn>2</mn></mrow></msup><mi mathvariant=\"bold-italic\">&#160;</mi><mo>-</mo><msup><mrow><mi mathvariant=\"bold-italic\">&#160;</mi><mo>(</mo><mn>12</mn><mi mathvariant=\"bold-italic\">&#160;</mi><mo>-</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mn>8</mn><mi mathvariant=\"bold-italic\">&#160;</mi><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup></msqrt></math><br>= <math display=\"inline\"><msqrt><mn>256</mn><mi mathvariant=\"bold-italic\">&#160;</mi><mo>-</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mn>16</mn></msqrt></math><br>= <math display=\"inline\"><msqrt><mn>240</mn></msqrt></math> = 4<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>15</mn></msqrt></math> cm</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "55",
                    section: "17",
                    question_en: "<p>55. Simplify the following.<br>(5 - <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>15</mn><mn>16</mn></mfrac></math>) &times; 1<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>12</mn><mn>13</mn></mfrac></math></p>",
                    question_hi: "<p>55. निम्नलिखित को सरल कीजिए।<br>(5 - <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>15</mn><mn>16</mn></mfrac></math>) &times; 1<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>12</mn><mn>13</mn></mfrac></math></p>",
                    options_en: ["<p>7<math display=\"inline\"><mfrac><mrow><mn>13</mn><mi>&#160;</mi></mrow><mrow><mn>16</mn></mrow></mfrac></math></p>", "<p>7<math display=\"inline\"><mfrac><mrow><mn>11</mn><mi>&#160;</mi></mrow><mrow><mn>16</mn></mrow></mfrac></math></p>", 
                                "<p>5<math display=\"inline\"><mfrac><mrow><mn>13</mn><mi>&#160;</mi></mrow><mrow><mn>16</mn></mrow></mfrac></math></p>", "<p>5<math display=\"inline\"><mfrac><mrow><mn>11</mn><mi>&#160;</mi></mrow><mrow><mn>16</mn></mrow></mfrac></math></p>"],
                    options_hi: ["<p>7<math display=\"inline\"><mfrac><mrow><mn>13</mn><mi>&#160;</mi></mrow><mrow><mn>16</mn></mrow></mfrac></math></p>", "<p>7<math display=\"inline\"><mfrac><mrow><mn>11</mn><mi>&#160;</mi></mrow><mrow><mn>16</mn></mrow></mfrac></math></p>",
                                "<p>5<math display=\"inline\"><mfrac><mrow><mn>13</mn><mi>&#160;</mi></mrow><mrow><mn>16</mn></mrow></mfrac></math></p>", "<p>5<math display=\"inline\"><mfrac><mrow><mn>11</mn><mi>&#160;</mi></mrow><mrow><mn>16</mn></mrow></mfrac></math></p>"],
                    solution_en: "<p>55.(a) (5 - <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>15</mn><mn>16</mn></mfrac></math>) &times; 1<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>12</mn><mn>13</mn></mfrac></math><br>= <math display=\"inline\"><mfrac><mrow><mn>80</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>15</mn><mi>&#160;</mi></mrow><mrow><mn>16</mn></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>25</mn><mn>13</mn></mfrac></math><br>= <math display=\"inline\"><mfrac><mrow><mn>65</mn></mrow><mrow><mn>16</mn></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>25</mn><mn>13</mn></mfrac></math></p>\n<p>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>125</mn><mn>16</mn></mfrac></math> = 7<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>13</mn><mn>16</mn></mfrac></math></p>",
                    solution_hi: "<p>55.(a) (5 - <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>15</mn><mn>16</mn></mfrac></math>) &times; 1<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>12</mn><mn>13</mn></mfrac></math><br>= <math display=\"inline\"><mfrac><mrow><mn>80</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>15</mn><mi>&#160;</mi></mrow><mrow><mn>16</mn></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>25</mn><mn>13</mn></mfrac></math><br>= <math display=\"inline\"><mfrac><mrow><mn>65</mn></mrow><mrow><mn>16</mn></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>25</mn><mn>13</mn></mfrac></math></p>\n<p>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>125</mn><mn>16</mn></mfrac></math> = 7<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>13</mn><mn>16</mn></mfrac></math></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "56",
                    section: "17",
                    question_en: "<p>56. A group of 30 students appeared in a test. The average score of 12 students is 62, and that of the rest is 76. What is the average score of the group?</p>",
                    question_hi: "<p>56. 30 छात्रों के एक समूह ने एक टेस्ट दिया। 12 छात्रों का औसत स्कोर 62 है और बाकी छात्रों का औसत स्कोर 76 है। समूह&nbsp;का औसत स्कोर ज्ञात करें।</p>",
                    options_en: ["<p>71.4</p>", "<p>68.4</p>", 
                                "<p>69.4</p>", "<p>70.4</p>"],
                    options_hi: ["<p>71.4</p>", "<p>68.4</p>",
                                "<p>69.4</p>", "<p>70.4</p>"],
                    solution_en: "<p>56.(d)Total number of students = 30<br>The total score of 12 students = 12 &times; 62 = 744 <br>The total score of 18 students = 18 &times; 76 = 1368<br>the average score of the group = <math display=\"inline\"><mfrac><mrow><mn>744</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>1368</mn></mrow><mrow><mn>30</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2112</mn><mn>30</mn></mfrac></math> = 70.4</p>",
                    solution_hi: "<p>56.(d) छात्रों की कुल संख्या = 30<br>12 विद्यार्थियों के कुल अंक = 12 &times; 62 = 744 <br>18 विद्यार्थियों के कुल अंक= 18 &times; 76 = 1368<br>समूह का औसत स्कोर = <math display=\"inline\"><mfrac><mrow><mn>744</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>1368</mn></mrow><mrow><mn>30</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2112</mn><mn>30</mn></mfrac></math> = 70.4</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "57",
                    section: "17",
                    question_en: "<p>57. AB and CD are two parallel chords drawn in a circle with centre O. The distance between the two chords is 21 cm. If the lengths of AB and CD are 24 cm and 18 cm, respectively, then the radius of the circle is equal to:</p>",
                    question_hi: "<p>57. AB और CD केंद्र O वाले एक वृत्त में खींची गई दो समानांतर जीवाएँ हैं। दोनों जीवाओं के बीच की दूरी 21 cm है। यदि AB और CD की लंबाई क्रमशः 24 cm और 18 cm है, तो वृत्त की त्रिज्या किसके बराबर है?</p>",
                    options_en: ["<p>15 cm</p>", "<p>20 cm</p>", 
                                "<p>18 cm</p>", "<p>24 cm</p>"],
                    options_hi: ["<p>15 cm</p>", "<p>20 cm</p>",
                                "<p>18 cm</p>", "<p>24 cm</p>"],
                    solution_en: "<p>57.(a) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731132152724.png\" alt=\"rId64\" width=\"160\" height=\"115\"><br>Here , OB = OD = radius <br>&rArr; 12<sup>2</sup> + (21 - x)<sup>2</sup> = 9<sup>2</sup> + x<sup>2</sup><br>&rArr; 144 + 441 + x<sup>2</sup> - 42x = 81 + x<sup>2</sup><br>&rArr; 144 + 441 - 81 = 42x <br>&rArr; 504 = 42x <br>&rArr; x = 12<br>Now in &Delta;ODQ<br>OD = <math display=\"inline\"><msqrt><mn>81</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>144</mn><mi>&#160;</mi></msqrt></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>225</mn></msqrt></math> = 15<br>So, radius of the circle = 15 cm</p>",
                    solution_hi: "<p>57.(a) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731132152724.png\" alt=\"rId64\" width=\"160\" height=\"115\"><br>यहाँ, OB = OD = त्रिज्या <br>&rArr; 12<sup>2</sup> + (21 - x)<sup>2</sup> = 9<sup>2</sup> + x<sup>2</sup><br>&rArr; 144 + 441 + x<sup>2</sup> - 42x = 81 + x<sup>2</sup><br>&rArr; 144 + 441 - 81 = 42x <br>&rArr; 504 = 42x <br>&rArr; x = 12<br>अब &Delta;ODQ में&nbsp;<br>OD = <math display=\"inline\"><msqrt><mn>81</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>144</mn><mi>&#160;</mi></msqrt></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>225</mn></msqrt></math> = 15<br>अत: वृत्त की त्रिज्या = 15 cm</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "58",
                    section: "17",
                    question_en: "<p>58. A cistern can be filled by two pipes in 8 minutes and 10 minutes, separately. Both the pipes are opened together for a certain time, but due to an obstruction, only <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>8</mn></mfrac></math> of the full quantity of water flowed through the former pipe and <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>5</mn></mfrac></math> through the latter pipe. However, the obstruction was suddenly removed, and the cistern was filled in 3 minutes from that moment. How long did it take before the full flow began?</p>",
                    question_hi: "<p>58. एक टंकी दो पाइपों द्वारा स्वतंत्र रूप से 8 मिनट और 10 मिनट में भरी जा सकती है। दोनों पाइपों को एक साथ कुछ समय के लिए खोला जाता है, लेकिन किसी अवरोध के कारण, पहले पाइप से जल की पूरी मात्रा का केवल <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>8</mn></mfrac></math> भाग और दूसरे पाइप से <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>5</mn></mfrac></math> भाग प्रवाहित होता है। हालांकि, अवरोध अचानक निकल जाता है, और उस क्षण से 3 मिनट में टंकी भर जाती है। पूर्ण प्रवाह शुरू होने से पहले कितना समय लगा?</p>",
                    options_en: ["<p>3<math display=\"inline\"><mfrac><mrow><mn>1</mn><mi>&#160;</mi></mrow><mrow><mn>16</mn></mrow></mfrac></math>minutes</p>", "<p>2<math display=\"inline\"><mfrac><mrow><mn>6</mn><mi>&#160;</mi></mrow><mrow><mn>17</mn></mrow></mfrac></math>minutes</p>", 
                                "<p>9<math display=\"inline\"><mfrac><mrow><mn>6</mn><mi>&#160;</mi></mrow><mrow><mn>7</mn></mrow></mfrac></math>minutes</p>", "<p>2<math display=\"inline\"><mfrac><mrow><mn>30</mn><mi>&#160;</mi></mrow><mrow><mn>37</mn></mrow></mfrac></math>minutes</p>"],
                    options_hi: ["<p>3<math display=\"inline\"><mfrac><mrow><mn>1</mn><mi>&#160;</mi></mrow><mrow><mn>16</mn></mrow></mfrac></math> मिनट</p>", "<p>2<math display=\"inline\"><mfrac><mrow><mn>6</mn><mi>&#160;</mi></mrow><mrow><mn>17</mn></mrow></mfrac></math> मिनट</p>",
                                "<p>9<math display=\"inline\"><mfrac><mrow><mn>6</mn><mi>&#160;</mi></mrow><mrow><mn>7</mn></mrow></mfrac></math> मिनट</p>", "<p>2<math display=\"inline\"><mfrac><mrow><mn>30</mn><mi>&#160;</mi></mrow><mrow><mn>37</mn></mrow></mfrac></math> मिनट</p>"],
                    solution_en: "<p>58.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731132152899.png\" alt=\"rId65\" width=\"165\" height=\"155\"><br>Water filled by Pipe A in 3 min. = (5 + 4) &times; 3 = 27 unit<br>Remaining water to be filled = 40 - 27 = 13 unit<br>Due to blockage- <br>Efficiency of Pipe A = (5 &times; <math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math>) = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>25</mn><mn>8</mn></mfrac></math>unit&nbsp;<br>Efficiency of Pipe B = (4 &times; <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math>) = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>12</mn><mn>5</mn></mfrac></math> unit <br>Time taken by to fill remaining water when there was blockage</p>\n<p>=&nbsp;<math display=\"inline\"><mfrac><mrow><mn>13</mn></mrow><mrow><mo>(</mo><mfrac><mrow><mn>25</mn></mrow><mrow><mn>8</mn></mrow></mfrac><mi>&#160;</mi><mo>+</mo><mfrac><mrow><mn>12</mn></mrow><mrow><mn>5</mn></mrow></mfrac><mo>)</mo></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>13</mn><mo>&#215;</mo><mn>40</mn></mrow><mrow><mn>125</mn><mo>+</mo><mn>96</mn><mo>&#160;</mo></mrow></mfrac></math></p>\n<p>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>520</mn><mn>221</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>40</mn><mn>17</mn></mfrac></math> = 2<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>6</mn><mn>17</mn></mfrac></math> minutes</p>",
                    solution_hi: "<p>58.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731132153058.png\" alt=\"rId66\" width=\"163\" height=\"167\"><br>पाइप A द्वारा 3 मिनट में भरा गया पानी = (5 + 4) &times; 3 = 27 इकाई<br>भरा जाने वाला शेष जल = 40 - 27 = 13 इकाई<br>अवरोध के कारण- <br>पाइप A की दक्षता = (5 &times; <math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math> ) = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>25</mn><mn>8</mn></mfrac></math> इकाई <br>पाइप B की दक्षता = (4 &times; <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math>) = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>12</mn><mn>5</mn></mfrac></math> इकाई <br>अवरोध के कारण बचा हुआ पानी भरने में लगा समय</p>\n<p>=&nbsp;<math display=\"inline\"><mfrac><mrow><mn>13</mn></mrow><mrow><mo>(</mo><mfrac><mrow><mn>25</mn></mrow><mrow><mn>8</mn></mrow></mfrac><mi>&#160;</mi><mo>+</mo><mfrac><mrow><mn>12</mn></mrow><mrow><mn>5</mn></mrow></mfrac><mo>)</mo></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>13</mn><mo>&#215;</mo><mn>40</mn></mrow><mrow><mn>125</mn><mo>+</mo><mn>96</mn><mo>&#160;</mo></mrow></mfrac></math></p>\n<p>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>520</mn><mn>221</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>40</mn><mn>17</mn></mfrac></math> = 2<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>6</mn><mn>17</mn></mfrac></math>&nbsp;मिनट</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "59",
                    section: "17",
                    question_en: "<p>59. If a = xcos&theta; + ysin&theta; and b = xsin&theta; - ycos&theta; , then a<sup>2</sup> + b<sup>2</sup> is equal to:</p>",
                    question_hi: "<p>59. यदि a = xcos&theta; + ysin&theta; और b = xsin&theta; - ycos&theta; हैं, तो a<sup>2</sup> + b<sup>2</sup> का मान कितना होगा?</p>",
                    options_en: ["<p>x + y</p>", "<p>x<sup>2</sup> - y<sup>2</sup></p>", 
                                "<p>x<sup>2</sup> + y<sup>2</sup></p>", "<p>x - y</p>"],
                    options_hi: ["<p>x + y</p>", "<p>x<sup>2</sup> - y<sup>2</sup></p>",
                                "<p>x<sup>2</sup> + y<sup>2</sup></p>", "<p>x - y</p>"],
                    solution_en: "<p>59.(c) <strong>if </strong>a =&nbsp;<math display=\"inline\"><mi>x</mi></math>cos&theta; + ysin&theta; and b = xsin&theta; - ycos&theta;&nbsp;<br>Then a<sup>2</sup> + b<sup>2</sup> = x<sup>2 </sup>+ y<sup>2</sup> (by using direct result )</p>",
                    solution_hi: "<p>59.(c) <strong>यदि </strong>a = xcos&theta; + ysin&theta; and b = xsin&theta; - ycos&theta;&nbsp;<br>फिर a<sup>2</sup> + b<sup>2</sup> = x<sup>2 </sup>+ y<sup>2</sup>&nbsp;(प्रत्यक्ष परिणाम का उपयोग करके )</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "60",
                    section: "17",
                    question_en: "<p>60. For what value of k, the system of equations 4x&nbsp;+ 12y + 36 = 0 and 5x + ky + 45 = 0 has an infinite number of solutions?</p>",
                    question_hi: "<p>60. k, के किस मान के लिए समीकरण निकाय 4x + 12y + 36 = 0और 5x + ky + 45 = 0 में अनंत संख्या में हल हैं?</p>",
                    options_en: ["<p>20</p>", "<p>25</p>", 
                                "<p>22</p>", "<p>15</p>"],
                    options_hi: ["<p>20</p>", "<p>25</p>",
                                "<p>22</p>", "<p>15</p>"],
                    solution_en: "<p>60.(d) Given equations , 4x&nbsp;+ 12y + 36 = 0 and 5x + ky + 45 = 0<br>For infinite number of solution , <math display=\"inline\"><mfrac><mrow><msub><mrow><mi>a</mi></mrow><mrow><mn>1</mn></mrow></msub></mrow><mrow><msub><mrow><mi>a</mi></mrow><mrow><mn>2</mn></mrow></msub></mrow></mfrac></math> = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msub><mi>b</mi><mn>1</mn></msub><msub><mi>b</mi><mn>2</mn></msub></mfrac></math> = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msub><mi>c</mi><mn>1</mn></msub><msub><mi>c</mi><mn>2</mn></msub></mfrac></math><br>Here , a<sub>1</sub> = 4 , a<sub>2</sub> = 5 , b<sub>1</sub> = 12 , b<sub>2</sub> = k , c<sub>1</sub> = 36 , c<sub>2</sub> = 45<br>By equating the equation , we get<br>&rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>5</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>12</mn><mi>k</mi></mfrac></math><br>&there4; k = 15</p>",
                    solution_hi: "<p>60.(d) दिए गया समीकरण , 4x&nbsp;+ 12y + 36 = 0 और 5x + ky + 45 = 0<br>अनंत हल के लिए , <math display=\"inline\"><mfrac><mrow><msub><mrow><mi>a</mi></mrow><mrow><mn>1</mn></mrow></msub></mrow><mrow><msub><mrow><mi>a</mi></mrow><mrow><mn>2</mn></mrow></msub></mrow></mfrac></math> = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msub><mi>b</mi><mn>1</mn></msub><msub><mi>b</mi><mn>2</mn></msub></mfrac></math> = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msub><mi>c</mi><mn>1</mn></msub><msub><mi>c</mi><mn>2</mn></msub></mfrac></math><br>यहाँ, a<sub>1</sub> = 4 , a<sub>2</sub> = 5 , b<sub>1</sub> = 12 , b<sub>2</sub> = k , c<sub>1</sub> = 36 , c<sub>2</sub> = 45<br>समीकरण को बराबर करने पर, हमे प्राप्त होता है <br>&rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>5</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>12</mn><mi>k</mi></mfrac></math><br>&there4; k = 15</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "61",
                    section: "17",
                    question_en: "<p>61. A motorboat takes 18 hours to go downstream, and it takes 36 hours to return the same distance. Find the ratio of the speed of the boat in still water to the speed of the stream.</p>",
                    question_hi: "<p>61. एक मोटरबोट को धारा के अनुकूल जाने में 18 घंटे लगते हैं, और समान दूरी तक वापस आने में 36 घंटे लगते हैं। नाव की, स्थिर जल में चाल और धारा की दिशा में चाल का अनुपात ज्ञात कीजिए।</p>",
                    options_en: ["<p>3 : 1</p>", "<p>2 : 3</p>", 
                                "<p>2 : 1</p>", "<p>3 : 2</p>"],
                    options_hi: ["<p>3 : 1</p>", "<p>2 : 3</p>",
                                "<p>2 : 1</p>", "<p>3 : 2</p>"],
                    solution_en: "<p>61.(a) Let speed of boat in still water = xkm/h <br>Speed of stream = y km/h <br>According to question , <br>&rArr; 18 &times; (x + y) = 36 &times; (x - y)<br>&rArr;<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mi>x</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>y</mi><mo>)</mo></mrow><mrow><mo>(</mo><mi>x</mi><mi>&#160;</mi><mo>-</mo><mi>y</mi><mi>&#160;</mi><mo>)</mo></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>1</mn></mfrac></math><br>&there4; x&nbsp;= 1.5 km/h , y = 0.5km/h<br>Ratio of the speed of the boat in still water to the speed of the stream = 1.5 : 0.5 <br>= 3 : 1</p>",
                    solution_hi: "<p>61.(a) माना शांत पानी में नाव की गति = x&nbsp;km/h <br>धारा की गति = y km/h <br>प्रश्न के अनुसार, <br>&rArr; 18 &times; (x + y) = 36 &times; (x - y)<br>&rArr;<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mi>x</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>y</mi><mo>)</mo></mrow><mrow><mo>(</mo><mi>x</mi><mi>&#160;</mi><mo>-</mo><mi>y</mi><mi>&#160;</mi><mo>)</mo></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>1</mn></mfrac></math><br>&there4; x&nbsp;= 1.5 किमी/घंटा , y = 0.5 किमी/घंटा<br>शांत पानी में नाव की गति और धारा की गति का अनुपात = 1.5 : 0.5 = 3 : 1</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "62",
                    section: "17",
                    question_en: "<p>62. In right-angled triangle ABC, &ang;C = 90, CM is perpendicular on AB. If AB = 18 cm and BM = 6 cm, then find the length of CM.</p>",
                    question_hi: "<p>62. समकोण त्रिभुज ABC में, &ang;C = 90 है, CM, AB पर लंब है। यदि AB = 18 cm और BM = 6 cm है, तो CM की लंबाई ज्ञात कीजिए।</p>",
                    options_en: ["<p>6 <math display=\"inline\"><msqrt><mn>2</mn></msqrt></math> cm</p>", "<p>4<math display=\"inline\"><msqrt><mn>2</mn></msqrt></math> cm</p>", 
                                "<p>7<math display=\"inline\"><msqrt><mn>2</mn></msqrt></math> cm</p>", "<p>2<math display=\"inline\"><msqrt><mn>2</mn></msqrt></math> cm</p>"],
                    options_hi: ["<p>6 <math display=\"inline\"><msqrt><mn>2</mn></msqrt></math> cm</p>", "<p>4<math display=\"inline\"><msqrt><mn>2</mn></msqrt></math> cm</p>",
                                "<p>7<math display=\"inline\"><msqrt><mn>2</mn></msqrt></math> cm</p>", "<p>2<math display=\"inline\"><msqrt><mn>2</mn></msqrt></math> cm</p>"],
                    solution_en: "<p>62.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731132153160.png\" alt=\"rId67\" width=\"137\" height=\"134\"><br>Length of CM = <math display=\"inline\"><msqrt><mn>12</mn><mi mathvariant=\"bold-italic\">&#160;</mi><mo>&#215;</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mn>6</mn></msqrt></math> = 6<math display=\"inline\"><msqrt><mn>2</mn></msqrt></math>cm</p>",
                    solution_hi: "<p>62.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731132153160.png\" alt=\"rId67\" width=\"159\" height=\"156\"><br>CM की लंबाई = <math display=\"inline\"><msqrt><mn>12</mn><mi mathvariant=\"bold-italic\">&#160;</mi><mo>&#215;</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mn>6</mn></msqrt></math> = 6<math display=\"inline\"><msqrt><mn>2</mn></msqrt></math>cm</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "63",
                    section: "17",
                    question_en: "<p>63. Find the least value of x&nbsp;for which 57x716 is divisible by 9.</p>",
                    question_hi: "<p>63. x&nbsp;का वह न्यूनतम मान ज्ञात करें, जिसके लिए 57x716, 9 से विभाज्य है।</p>",
                    options_en: ["<p>1</p>", "<p>9</p>", 
                                "<p>0</p>", "<p>8</p>"],
                    options_hi: ["<p>1</p>", "<p>9</p>",
                                "<p>0</p>", "<p>8</p>"],
                    solution_en: "<p>63.(a) Divisibility rule of 9 : Sum of the digit of the number should be divisible by 9 <br>Given , number =57x716 <br>Sum of digit : 5 + 7 + x + 7 + 1 + 6 = 26 + x <br>Put the value of x&nbsp;= 1 so sum of digit is divisible by 9 &there4; x = 1</p>",
                    solution_hi: "<p>63.(a) 9 की विभाज्यता नियम: संख्या के अंकों का योग 9 से विभाज्य होना चाहिए <br>दिया गया है, संख्या =57x716 <br>अंकों का योग: 5 + 7 + x + 7 + 1 + 6 = 26 + x <br>x = 1 मान रखें ताकि अंकों का योग 9 &there4; x = 1 से विभाज्य हो ।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "64",
                    section: "17",
                    question_en: "<p>64. In a rhombus PQRS, O is any interior point such that OP = OR. What is the degree measure of &ang;SOQ?</p>",
                    question_hi: "<p>64. एक समचतुर्भुज PQRS में, O एक आंतरिक बिंदु इस प्रकार है कि OP= OR है। &ang;SOQ की अंशीय माप कितनी है?</p>",
                    options_en: ["<p>120&deg;</p>", "<p>180&deg;</p>", 
                                "<p>240&deg;</p>", "<p>200&deg;</p>"],
                    options_hi: ["<p>120&deg;</p>", "<p>180&deg;</p>",
                                "<p>240&deg;</p>", "<p>200&deg;</p>"],
                    solution_en: "<p>64.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731132153281.png\" alt=\"rId68\" width=\"149\" height=\"134\"><br>As OP = OR , So with the help of diagram we can see that SOQ is a straight line hence &ang;SOQ = 180&deg;</p>",
                    solution_hi: "<p>64.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731132153281.png\" alt=\"rId68\" width=\"151\" height=\"136\"><br>चूंकि OP = OR, इसलिए इसलिए आरेख की सहायता से हम देख सकते हैं कि SOQ एक सीधी रेखा है , इसलिए &ang;SOQ = 180&deg;</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "65",
                    section: "17",
                    question_en: "<p>65. A shopkeeper allows 40% discount on his goods. He further gives 20% discount. What is the single discount equivalent to these two successive discounts?</p>",
                    question_hi: "<p>65. एक दुकानदार अपने माल पर 40% की छूट देता है। वह 20% की अतिरिक्त छूट भी देता है। इन दो क्रमिक छूटों के बराबर एकल छूट कितनी है?</p>",
                    options_en: ["<p>62%</p>", "<p>52%</p>", 
                                "<p>60%</p>", "<p>50%</p>"],
                    options_hi: ["<p>62%</p>", "<p>52%</p>",
                                "<p>60%</p>", "<p>50%</p>"],
                    solution_en: "<p>65.(b) Single equivalent discount = (40 + 20 - <math display=\"inline\"><mfrac><mrow><mn>40</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>20</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math>)%<br>= 60 - 8 = 52%</p>",
                    solution_hi: "<p>65.(b) एकल समकक्ष छूट = (40 + 20 - <math display=\"inline\"><mfrac><mrow><mn>40</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>20</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math>)%<br>= 60 - 8 = 52%</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "66",
                    section: "17",
                    question_en: "<p>66. The following table shows the categorisation of 100 students based on their marks in Physics and Chemistry in an examination.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731132153474.png\" alt=\"rId69\" width=\"496\" height=\"78\"> <br>If a minimum of 40% marks in Chemistry is required to pursue higher studies in Chemistry, how many students will be eligible to pursue higher studies in Chemistry?</p>",
                    question_hi: "<p>66. निम्नलिखित तालिका किसी परीक्षा में भौतिकी और रसायन विज्ञान में 100 विद्यार्थियों के वर्गीकरण को उनके अंकों के आधार पर दिखाती है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731132153631.png\" alt=\"rId70\" width=\"417\" height=\"112\"> <br>यदि रसायन विज्ञान में उच्च अध्ययन करने के लिए रसायन विज्ञान में न्यूनतम 40% अंक आवश्यक हैं, तो कितने विद्यार्थी रसायन विज्ञान में उच्च अध्ययन करने के लिए पात्र होंगे?</p>",
                    options_en: ["<p>66</p>", "<p>25</p>", 
                                "<p>45</p>", "<p>21</p>"],
                    options_hi: ["<p>66</p>", "<p>25</p>",
                                "<p>45</p>", "<p>21</p>"],
                    solution_en: "<p>66.(a) Total marks = 50<br>Required marks in chemistry to pursue higher studied in chemistry = 50 &times; <math display=\"inline\"><mfrac><mrow><mn>40</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 20<br>So , No of students that are eligible to pursue higher studies in Chemistry = 66</p>",
                    solution_hi: "<p>66.(a) कुल अंक = 50<br>रसायन विज्ञान में उच्च अध्ययन करने के लिए रसायन विज्ञान में आवश्यक अंक = 50 &times; <math display=\"inline\"><mfrac><mrow><mn>40</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 20<br>तो, रसायन विज्ञान में उच्च अध्ययन करने के लिए पात्र छात्रों की संख्या = 66</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "67",
                    section: "17",
                    question_en: "<p>67. If 8a<sup>3</sup> + b<sup>3</sup> + 27c<sup>3</sup> = 18 abc, then the relation among a, b and c is:</p>",
                    question_hi: "<p>67. यदि 8a<sup>3</sup> + b<sup>3</sup> +27c<sup>3</sup> = 18 abc है, तो a, b और c के बीच संबंध ज्ञात कीजिए।</p>",
                    options_en: ["<p>b + 3c = 2a</p>", "<p>2a + b = 3c</p>", 
                                "<p>2a + 3c = b</p>", "<p>b + 3c =- 2a</p>"],
                    options_hi: ["<p>b +3c = 2a</p>", "<p>2a + b = 3c</p>",
                                "<p>2a + 3c = b</p>", "<p>b + 3c =- 2a</p>"],
                    solution_en: "<p>67.(d) 8a<sup>3</sup> + b<sup>3</sup> +27c<sup>3</sup> = 18abc <br>After rearranging , we can write this <br>(2a)<sup>3</sup> + b<sup>3</sup> +(3c)<sup>3</sup> = 3 &times; 2a &times; b &times; 3c<br>Formula : a<sup>3</sup> + b<sup>3</sup> + c<sup>3</sup> = 3abc , when a + b + c = 0<br>&rArr; 2a + b + 3c = 0 <br>b + 3c = - 2a</p>",
                    solution_hi: "<p>67.(d) 8a<sup>3</sup> + b<sup>3</sup> +27c<sup>3</sup> = 18abc<br>पुनः व्यवस्थित करने के बाद, <br>(2a)<sup>3</sup> + b<sup>3</sup> +(3c)<sup>3</sup> = 3 &times; 2a &times; b &times; 3c<br>सूत्र: a<sup>3</sup> + b<sup>3</sup> + c<sup>3</sup> = 3abc, जब a + b + c = 0<br>&rArr; 2a + b + 3c = 0 <br>b + 3c = - 2a</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "68",
                    section: "17",
                    question_en: "<p>68. An article passing through two hands is sold at a total profit of 40% of the original cost price. If the first dealer makes a profit of 30%, then the profit percentage made by the second dealer is:</p>",
                    question_hi: "<p>68. दो हाथों से गुज़रने वाली एक वस्तु को मूल लागत मूल्य के 40% के कुल लाभ पर बेचा जाता है। यदि पहला व्यापारी 30% का लाभ कमाता है, तो दूसरे व्यापारी द्वारा अर्जित लाभ प्रतिशत कितना होगा?</p>",
                    options_en: ["<p>8<math display=\"inline\"><mfrac><mrow><mn>6</mn><mi>&#160;</mi></mrow><mrow><mn>13</mn></mrow></mfrac></math>%</p>", "<p>6<math display=\"inline\"><mfrac><mrow><mn>3</mn><mi>&#160;</mi></mrow><mrow><mn>13</mn></mrow></mfrac></math>%</p>", 
                                "<p>7<math display=\"inline\"><mfrac><mrow><mn>1</mn><mi>&#160;</mi></mrow><mrow><mn>13</mn></mrow></mfrac></math>%</p>", "<p>7<math display=\"inline\"><mfrac><mrow><mn>9</mn><mi>&#160;</mi></mrow><mrow><mn>13</mn></mrow></mfrac></math>%</p>"],
                    options_hi: ["<p>8<math display=\"inline\"><mfrac><mrow><mn>6</mn><mi>&#160;</mi></mrow><mrow><mn>13</mn></mrow></mfrac></math>%</p>", "<p>6<math display=\"inline\"><mfrac><mrow><mn>3</mn><mi>&#160;</mi></mrow><mrow><mn>13</mn></mrow></mfrac></math>%</p>",
                                "<p>7<math display=\"inline\"><mfrac><mrow><mn>1</mn><mi>&#160;</mi></mrow><mrow><mn>13</mn></mrow></mfrac></math>%</p>", "<p>7<math display=\"inline\"><mfrac><mrow><mn>9</mn><mi>&#160;</mi></mrow><mrow><mn>13</mn></mrow></mfrac></math>%</p>"],
                    solution_en: "<p>68.(d) Let the initial cost price = 100 rs<br>Final selling price = 140rs<br>Selling price of first dealer(30%&uarr;) = 130 rs<br>Cost price for second dealer = 130rs<br>Profit percentage made by the second dealer = <math display=\"inline\"><mfrac><mrow><mn>140</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>130</mn><mi>&#160;</mi></mrow><mrow><mn>130</mn></mrow></mfrac></math> &times; 100 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>100</mn><mn>13</mn></mfrac></math><br>= 7<math display=\"inline\"><mfrac><mrow><mn>9</mn><mi>&#160;</mi></mrow><mrow><mn>13</mn></mrow></mfrac></math>%</p>",
                    solution_hi: "<p>68.(d) माना आरंभिक लागत मूल्य = 100 rs<br>अंतिम विक्रय मूल्य = 140rs<br>पहले डीलर का विक्रय मूल्य (30%&uarr;) = 130 rs<br>दूसरे डीलर के लिए क्रय मूल्य = 130rs<br>दूसरे डीलर द्वारा अर्जित लाभ प्रतिशत = <math display=\"inline\"><mfrac><mrow><mn>140</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>130</mn><mi>&#160;</mi></mrow><mrow><mn>130</mn></mrow></mfrac></math> &times; 100 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>100</mn><mn>13</mn></mfrac></math><br>= 7<math display=\"inline\"><mfrac><mrow><mn>9</mn><mi>&#160;</mi></mrow><mrow><mn>13</mn></mrow></mfrac></math>%</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "69",
                    section: "17",
                    question_en: "<p>69. In a company, the ratio of men and women is 5 : 3, respectively. When 150 additional women are employed in the company, the ratio becomes 5 : 4. How many men are employed in the company?</p>",
                    question_hi: "<p>69. एक कंपनी में, पुरुषों और महिलाओं का अनुपात क्रमशः 5 : 3 है। जब कंपनी में 150 अतिरिक्त महिलाओं को नियोजित किया जाता है, तो अनुपात 5 : 4 हो जाता है। कंपनी में कितने पुरुष कार्यरत हैं?</p>",
                    options_en: ["<p>850</p>", "<p>750</p>", 
                                "<p>550</p>", "<p>950</p>"],
                    options_hi: ["<p>850</p>", "<p>750</p>",
                                "<p>550</p>", "<p>950</p>"],
                    solution_en: "<p>69.(b) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731132153782.png\" alt=\"rId71\" width=\"174\" height=\"88\"><br>According to question <br>&rArr; 1 unit = 150<br>No of men employee in company (5 unit ) = 5 &times; 150 = 750</p>",
                    solution_hi: "<p>69.(b) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731132153914.png\" alt=\"rId72\" width=\"212\" height=\"101\"><br>प्रश्न के अनुसार <br>&rArr; 1 इकाई = 150<br>कंपनी में पुरुष कर्मचारियों की संख्या (5 इकाई) = 5 &times; 150 = 750</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "70",
                    section: "17",
                    question_en: "<p>70. If sinA + cosA = <math display=\"inline\"><mfrac><mrow><mi>&#160;</mi><mn>1</mn></mrow><mrow><mn>2</mn><msqrt><mn>2</mn></msqrt></mrow></mfrac></math>, then find the value of sin<sup>4</sup>A + cos<sup>4</sup>A.</p>",
                    question_hi: "<p>70. यदि sinA + cosA = <math display=\"inline\"><mfrac><mrow><mi>&#160;</mi><mn>1</mn></mrow><mrow><mn>2</mn><msqrt><mn>2</mn></msqrt></mrow></mfrac></math>है, तो sin<sup>4</sup>A + cos<sup>4</sup>A का मान ज्ञात करें।</p>",
                    options_en: ["<p><math display=\"inline\"><mfrac><mrow><mn>79</mn><mi>&#160;</mi></mrow><mrow><mn>128</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mi>&#160;</mi><mn>7</mn></mrow><mrow><mn>13</mn></mrow></mfrac></math></p>", 
                                "<p><math display=\"inline\"><mfrac><mrow><mi>&#160;</mi><mn>79</mn></mrow><mrow><mn>126</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mi>&#160;</mi><mn>5</mn></mrow><mrow><mn>13</mn></mrow></mfrac></math></p>"],
                    options_hi: ["<p><math display=\"inline\"><mfrac><mrow><mn>79</mn><mi>&#160;</mi></mrow><mrow><mn>128</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mi>&#160;</mi><mn>7</mn></mrow><mrow><mn>13</mn></mrow></mfrac></math></p>",
                                "<p><math display=\"inline\"><mfrac><mrow><mi>&#160;</mi><mn>79</mn></mrow><mrow><mn>126</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mi>&#160;</mi><mn>5</mn></mrow><mrow><mn>13</mn></mrow></mfrac></math></p>"],
                    solution_en: "<p>70.(a) sinA + cosA = <math display=\"inline\"><mfrac><mrow><mi>&#160;</mi><mn>1</mn></mrow><mrow><mn>2</mn><msqrt><mn>2</mn></msqrt></mrow></mfrac></math> <br>Squaring both side , we get <br>&rArr; Sin<sup>2</sup>A + Cos<sup>2</sup>A + 2 SinA Cos A =&nbsp;<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>8</mn></mfrac></math><br>&rArr; 2 SinA Cos A = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>8</mn></mfrac></math> - 1 = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>-</mo><mn>7</mn></mrow><mn>8</mn></mfrac></math><br>&rArr; SinA Cos A = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>-</mo><mn>7</mn></mrow><mn>16</mn></mfrac></math><br>Now , we know that <br>Sin<sup>2</sup>A + Cos<sup>2</sup>A = 1<br>Again Squaring both side , we get <br>&rArr; sin<sup>4</sup>A + cos<sup>4</sup>A + 2 Sin<sup>2</sup>A Cos<sup>2</sup>A = 1<br>&rArr; sin<sup>4</sup>A + cos<sup>4</sup>A + 2 &times; (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>49</mn><mn>256</mn></mfrac></math>) = 1<br>&rArr; sin<sup>4</sup>A + cos<sup>4</sup>A = 1 - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>49</mn><mn>128</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>79</mn><mn>128</mn></mfrac></math></p>",
                    solution_hi: "<p>70.(a) sinA + cosA = <math display=\"inline\"><mfrac><mrow><mi>&#160;</mi><mn>1</mn></mrow><mrow><mn>2</mn><msqrt><mn>2</mn></msqrt></mrow></mfrac></math> <br>दोनों पक्षों का वर्ग करने पर, हमें प्राप्त होता है <br>&rArr; Sin<sup>2</sup>A + Cos<sup>2</sup>A + 2 SinA Cos A =&nbsp;<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>8</mn></mfrac></math><br>&rArr; 2 SinA Cos A = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>8</mn></mfrac></math> - 1 = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>-</mo><mn>7</mn></mrow><mn>8</mn></mfrac></math><br>&rArr; SinA Cos A = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>-</mo><mn>7</mn></mrow><mn>16</mn></mfrac></math><br>अब, हम यह जानते हैं <br>Sin<sup>2</sup>A + Cos<sup>2</sup>A = 1<br>पुनः दोनों पक्षों का वर्ग करने पर, हमें प्राप्त होता है <br>&rArr; sin<sup>4</sup>A + cos<sup>4</sup>A + 2 Sin<sup>2</sup>A Cos<sup>2</sup>A = 1<br>&rArr; sin<sup>4</sup>A + cos<sup>4</sup>A + 2 &times; (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>49</mn><mn>256</mn></mfrac></math>) = 1<br>&rArr; sin<sup>4</sup>A + cos<sup>4</sup>A = 1 - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>49</mn><mn>128</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>79</mn><mn>128</mn></mfrac></math></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "71",
                    section: "17",
                    question_en: "<p>71. Find the value of [<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>4</mn><mi>&#160;</mi><mi>c</mi><mi>o</mi><mi>s</mi><mo>(</mo><mn>90</mn><mo>-</mo><mi>&#952;</mi><mo>)</mo><mi>&#160;</mi><msup><mrow><mi>s</mi><mi>i</mi><mi>n</mi></mrow><mn>3</mn></msup><mo>(</mo><mn>90</mn><mo>+</mo><mi>&#952;</mi><mo>)</mo><mi>&#160;</mi><mo>-</mo><mn>4</mn><mi>&#160;</mi><mi>s</mi><mi>i</mi><mi>n</mi><mo>(</mo><mn>90</mn><mo>+</mo><mi>&#952;</mi><mo>)</mo><mi>&#160;</mi><msup><mrow><mi>c</mi><mi>o</mi><mi>s</mi></mrow><mn>3</mn></msup><mo>(</mo><mn>90</mn><mo>-</mo><mi>&#952;</mi><mo>)</mo></mrow><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mo>(</mo><mfrac><mrow><mi>&#160;</mi><mn>180</mn><mo>+</mo><mn>8</mn><mi>&#952;</mi></mrow><mn>2</mn></mfrac><mo>)</mo></mrow></mfrac></math>]</p>",
                    question_hi: "<p>71. [<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>4</mn><mi>&#160;</mi><mi>c</mi><mi>o</mi><mi>s</mi><mo>(</mo><mn>90</mn><mo>-</mo><mi>&#952;</mi><mo>)</mo><mi>&#160;</mi><msup><mrow><mi>s</mi><mi>i</mi><mi>n</mi></mrow><mn>3</mn></msup><mo>(</mo><mn>90</mn><mo>+</mo><mi>&#952;</mi><mo>)</mo><mo>-</mo><mn>4</mn><mi>&#160;</mi><mi>s</mi><mi>i</mi><mi>n</mi><mo>(</mo><mn>90</mn><mo>+</mo><mi>&#952;</mi><mo>)</mo><mi>&#160;</mi><msup><mrow><mi>c</mi><mi>o</mi><mi>s</mi></mrow><mn>3</mn></msup><mo>(</mo><mn>90</mn><mo>-</mo><mi>&#952;</mi><mo>)</mo><mi>&#160;</mi></mrow><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mo>(</mo><mfrac><mrow><mi>&#160;</mi><mn>180</mn><mo>+</mo><mn>8</mn><mi>&#952;</mi></mrow><mn>2</mn></mfrac><mo>)</mo></mrow></mfrac></math>] का मान ज्ञात कीजिए।</p>",
                    options_en: ["<p>-1</p>", "<p>1</p>", 
                                "<p>2</p>", "<p>0</p>"],
                    options_hi: ["<p>-1</p>", "<p>1</p>",
                                "<p>2</p>", "<p>0</p>"],
                    solution_en: "<p>71.(a) [<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>4</mn><mi>&#160;</mi><mi>c</mi><mi>o</mi><mi>s</mi><mo>(</mo><mn>90</mn><mo>-</mo><mi>&#952;</mi><mo>)</mo><mi>&#160;</mi><msup><mrow><mi>s</mi><mi>i</mi><mi>n</mi></mrow><mn>3</mn></msup><mo>(</mo><mn>90</mn><mo>+</mo><mi>&#952;</mi><mo>)</mo><mo>-</mo><mn>4</mn><mi>&#160;</mi><mi>s</mi><mi>i</mi><mi>n</mi><mo>(</mo><mn>90</mn><mo>+</mo><mi>&#952;</mi><mo>)</mo><mi>&#160;</mi><msup><mrow><mi>c</mi><mi>o</mi><mi>s</mi></mrow><mn>3</mn></msup><mo>(</mo><mn>90</mn><mo>-</mo><mi>&#952;</mi><mo>)</mo><mi>&#160;</mi></mrow><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mo>(</mo><mfrac><mrow><mi>&#160;</mi><mn>180</mn><mo>+</mo><mn>8</mn><mi>&#952;</mi></mrow><mn>2</mn></mfrac><mo>)</mo></mrow></mfrac></math>]<br>= <math display=\"inline\"><mfrac><mrow><mn>4</mn><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#952;</mi><mi>&#160;</mi><msup><mrow><mo>&#215;</mo><mi>&#160;</mi><mi>c</mi><mi>o</mi><mi>s</mi></mrow><mrow><mn>3</mn></mrow></msup><mi>&#952;</mi><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>4</mn><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#952;</mi><mi>&#160;</mi><mo>&#215;</mo><msup><mrow><mi>s</mi><mi>i</mi><mi>n</mi></mrow><mrow><mn>3</mn></mrow></msup><mi>&#952;</mi></mrow><mrow><mo>-</mo><mi>&#160;</mi><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#160;</mi><mn>4</mn><mi>&#952;</mi></mrow></mfrac></math> <br>= <math display=\"inline\"><mfrac><mrow><mn>4</mn><mi>S</mi><mi>i</mi><mi>n</mi><mi>&#952;</mi><mi>C</mi><mi>o</mi><mi>s</mi><mi>&#952;</mi><mi>&#160;</mi><mo>(</mo><msup><mrow><mi>c</mi><mi>o</mi><mi>s</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>&#952;</mi><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><msup><mrow><mi>s</mi><mi>i</mi><mi>n</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>&#952;</mi><mo>)</mo></mrow><mrow><mo>-</mo><mi>&#160;</mi><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#160;</mi><mn>2</mn><mo>&#215;</mo><mo>(</mo><mn>2</mn><mi>&#952;</mi><mo>)</mo></mrow></mfrac></math><br>= <math display=\"inline\"><mfrac><mrow><mn>4</mn><mi>S</mi><mi>i</mi><mi>n</mi><mi>&#952;</mi><mi>C</mi><mi>o</mi><mi>s</mi><mi>&#952;</mi><mi>&#160;</mi><mo>(</mo><msup><mrow><mi>c</mi><mi>o</mi><mi>s</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>&#952;</mi><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><msup><mrow><mi>s</mi><mi>i</mi><mi>n</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>&#952;</mi><mo>)</mo></mrow><mrow><mo>-</mo><mi>&#160;</mi><mn>2</mn><mi>&#160;</mi><mi>S</mi><mi>i</mi><mi>n</mi><mn>2</mn><mi>&#952;</mi><mi>C</mi><mi>o</mi><mi>s</mi><mn>2</mn><mi>&#952;</mi></mrow></mfrac></math> (∵Cos2&theta; = cos<sup>2</sup>&theta; - sin<sup>2</sup>&theta;)<br>=<math display=\"inline\"><mfrac><mrow><mn>2</mn><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#952;</mi><mi>C</mi><mi>o</mi><mi>s</mi><mi>&#952;</mi></mrow><mrow><mo>-</mo><mi>S</mi><mi>i</mi><mi>n</mi><mn>2</mn><mi>&#952;</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mi>sin</mi><mi>&#952;</mi><mi>C</mi><mi>o</mi><mi>s</mi><mi>&#952;</mi></mrow><mrow><mo>-</mo><mn>2</mn><mi>sin</mi><mi>&#952;</mi><mi>C</mi><mi>o</mi><mi>s</mi><mi>&#952;</mi></mrow></mfrac></math> = -1 (∵Sin2&theta; = 2Sin&theta;Cos&theta;)</p>",
                    solution_hi: "<p>71.(a) [<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>4</mn><mi>&#160;</mi><mi>c</mi><mi>o</mi><mi>s</mi><mo>(</mo><mn>90</mn><mo>-</mo><mi>&#952;</mi><mo>)</mo><mi>&#160;</mi><msup><mrow><mi>s</mi><mi>i</mi><mi>n</mi></mrow><mn>3</mn></msup><mo>(</mo><mn>90</mn><mo>+</mo><mi>&#952;</mi><mo>)</mo><mo>-</mo><mn>4</mn><mi>&#160;</mi><mi>s</mi><mi>i</mi><mi>n</mi><mo>(</mo><mn>90</mn><mo>+</mo><mi>&#952;</mi><mo>)</mo><mi>&#160;</mi><msup><mrow><mi>c</mi><mi>o</mi><mi>s</mi></mrow><mn>3</mn></msup><mo>(</mo><mn>90</mn><mo>-</mo><mi>&#952;</mi><mo>)</mo><mi>&#160;</mi></mrow><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mo>(</mo><mfrac><mrow><mi>&#160;</mi><mn>180</mn><mo>+</mo><mn>8</mn><mi>&#952;</mi></mrow><mn>2</mn></mfrac><mo>)</mo></mrow></mfrac></math>]<br>= <math display=\"inline\"><mfrac><mrow><mn>4</mn><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#952;</mi><mi>&#160;</mi><msup><mrow><mo>&#215;</mo><mi>&#160;</mi><mi>c</mi><mi>o</mi><mi>s</mi></mrow><mrow><mn>3</mn></mrow></msup><mi>&#952;</mi><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>4</mn><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#952;</mi><mi>&#160;</mi><mo>&#215;</mo><msup><mrow><mi>s</mi><mi>i</mi><mi>n</mi></mrow><mrow><mn>3</mn></mrow></msup><mi>&#952;</mi></mrow><mrow><mo>-</mo><mi>&#160;</mi><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#160;</mi><mn>4</mn><mi>&#952;</mi></mrow></mfrac></math> <br>= <math display=\"inline\"><mfrac><mrow><mn>4</mn><mi>S</mi><mi>i</mi><mi>n</mi><mi>&#952;</mi><mi>C</mi><mi>o</mi><mi>s</mi><mi>&#952;</mi><mi>&#160;</mi><mo>(</mo><msup><mrow><mi>c</mi><mi>o</mi><mi>s</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>&#952;</mi><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><msup><mrow><mi>s</mi><mi>i</mi><mi>n</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>&#952;</mi><mo>)</mo></mrow><mrow><mo>-</mo><mi>&#160;</mi><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#160;</mi><mn>2</mn><mo>&#215;</mo><mo>(</mo><mn>2</mn><mi>&#952;</mi><mo>)</mo></mrow></mfrac></math><br>= <math display=\"inline\"><mfrac><mrow><mn>4</mn><mi>S</mi><mi>i</mi><mi>n</mi><mi>&#952;</mi><mi>C</mi><mi>o</mi><mi>s</mi><mi>&#952;</mi><mi>&#160;</mi><mo>(</mo><msup><mrow><mi>c</mi><mi>o</mi><mi>s</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>&#952;</mi><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><msup><mrow><mi>s</mi><mi>i</mi><mi>n</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>&#952;</mi><mo>)</mo></mrow><mrow><mo>-</mo><mi>&#160;</mi><mn>2</mn><mi>&#160;</mi><mi>S</mi><mi>i</mi><mi>n</mi><mn>2</mn><mi>&#952;</mi><mi>C</mi><mi>o</mi><mi>s</mi><mn>2</mn><mi>&#952;</mi></mrow></mfrac></math> (∵Cos2&theta; = cos<sup>2</sup>&theta; - sin<sup>2</sup>&theta;)<br>=<math display=\"inline\"><mfrac><mrow><mn>2</mn><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#952;</mi><mi>C</mi><mi>o</mi><mi>s</mi><mi>&#952;</mi></mrow><mrow><mo>-</mo><mi>S</mi><mi>i</mi><mi>n</mi><mn>2</mn><mi>&#952;</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mi>sin</mi><mi>&#952;</mi><mi>C</mi><mi>o</mi><mi>s</mi><mi>&#952;</mi></mrow><mrow><mo>-</mo><mn>2</mn><mi>sin</mi><mi>&#952;</mi><mi>C</mi><mi>o</mi><mi>s</mi><mi>&#952;</mi></mrow></mfrac></math> = -1 (∵Sin2&theta; = 2Sin&theta;Cos&theta;)</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "72",
                    section: "17",
                    question_en: "<p>72. If a<sup>3</sup> + 3a<sup>2</sup> + 3a = 7, then the value of a<sup>2</sup> + 2a is:</p>",
                    question_hi: "<p>72. यदि a<sup>3</sup> + 3a<sup>2</sup> + 3a = 7 है, तो a<sup>2</sup> + 2a का मान ज्ञात कीजिए।</p>",
                    options_en: ["<p>3</p>", "<p>1</p>", 
                                "<p>2</p>", "<p>4</p>"],
                    options_hi: ["<p>3</p>", "<p>1</p>",
                                "<p>2</p>", "<p>4</p>"],
                    solution_en: "<p>72.(a) <strong>Given, </strong>a<sup>3</sup> + 3a<sup>2</sup> + 3a = 7<br>By hit and trial , put a = 1 , which satisfy the condition <br>&there4; a<sup>2</sup> + 2a = 1<sup>2 </sup>+ 2 (1) = 3</p>",
                    solution_hi: "<p>72.(a) <strong>दिया गया है,</strong> a<sup>3</sup> + 3a<sup>2</sup> + 3a = 7<br>हिट एंड ट्रायल द्वारा, a = 1 रखने पर ,शर्त पूरी होती है,<br>&there4; a<sup>2</sup> + 2a = 1<sup>2 </sup>+ 2 (1) = 3</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "73",
                    section: "17",
                    question_en: "<p>73. What is the rate of interest per annum for simple interest at which ₹1,040 amounts&nbsp;to ₹1,230 in 1<math display=\"inline\"><mfrac><mrow><mn>1</mn><mi>&#160;</mi></mrow><mrow><mn>2</mn></mrow></mfrac></math> years?</p>",
                    question_hi: "<p>73. साधारण ब्याज पर प्रति वर्ष ब्याज की दर कितनी है, जिस पर 1<math display=\"inline\"><mfrac><mrow><mn>1</mn><mi>&#160;</mi></mrow><mrow><mn>2</mn></mrow></mfrac></math> वर्षों में ₹1,040 का मूलधन ₹1,230 हो जाता है?</p>",
                    options_en: ["<p>11<math display=\"inline\"><mfrac><mrow><mi>&#160;</mi><mn>7</mn></mrow><mrow><mn>39</mn></mrow></mfrac></math>%</p>", "<p>10<math display=\"inline\"><mfrac><mrow><mi>&#160;</mi><mn>7</mn></mrow><mrow><mn>39</mn></mrow></mfrac></math>%</p>", 
                                "<p>12<math display=\"inline\"><mfrac><mrow><mi>&#160;</mi><mn>7</mn></mrow><mrow><mn>39</mn></mrow></mfrac></math>%</p>", "<p>13<math display=\"inline\"><mfrac><mrow><mi>&#160;</mi><mn>7</mn></mrow><mrow><mn>39</mn></mrow></mfrac></math>%</p>"],
                    options_hi: ["<p>11<math display=\"inline\"><mfrac><mrow><mi>&#160;</mi><mn>7</mn></mrow><mrow><mn>39</mn></mrow></mfrac></math>%</p>", "<p>10<math display=\"inline\"><mfrac><mrow><mi>&#160;</mi><mn>7</mn></mrow><mrow><mn>39</mn></mrow></mfrac></math>%</p>",
                                "<p>12<math display=\"inline\"><mfrac><mrow><mi>&#160;</mi><mn>7</mn></mrow><mrow><mn>39</mn></mrow></mfrac></math>%</p>", "<p>13<math display=\"inline\"><mfrac><mrow><mi>&#160;</mi><mn>7</mn></mrow><mrow><mn>39</mn></mrow></mfrac></math>%</p>"],
                    solution_en: "<p>73.(c) Interest = 1230 - 1040 = ₹190 <br>Rate = <math display=\"inline\"><mfrac><mrow><mi>S</mi><mi>I</mi><mi>&#160;</mi><mo>&#215;</mo><mn>100</mn></mrow><mrow><mi>P</mi><mi>r</mi><mi>i</mi><mi>n</mi><mi>c</mi><mi>i</mi><mi>p</mi><mi>a</mi><mi>l</mi><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mi>t</mi><mi>i</mi><mi>m</mi><mi>e</mi><mi>&#160;</mi></mrow></mfrac><mi>&#160;</mi></math><br>Rate = <math display=\"inline\"><mfrac><mrow><mn>190</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>100</mn></mrow><mrow><mn>1040</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mfrac><mrow><mn>3</mn></mrow><mrow><mn>2</mn></mrow></mfrac></mrow></mfrac></math><br>Rate = <math display=\"inline\"><mfrac><mrow><mn>475</mn></mrow><mrow><mn>39</mn></mrow></mfrac></math> = 12<math display=\"inline\"><mfrac><mrow><mi>&#160;</mi><mn>7</mn></mrow><mrow><mn>39</mn></mrow></mfrac></math>%</p>",
                    solution_hi: "<p>73.(c) ब्याज = 1230 - 1040 = ₹190 <br>दर = <math style=\"font-family:Verdana\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>S</mi><mi>I</mi><mi>&#160;</mi><mo>&#215;</mo><mn>100</mn></mrow><mrow><mi>&#2350;&#2370;&#2354;&#2343;&#2344;</mi><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mi>&#2360;</mi><mi>&#2350;</mi><mi>&#2351;</mi><mi>&#160;</mi><mi>&#160;</mi></mrow></mfrac></math><br>दर = <math display=\"inline\"><mfrac><mrow><mn>190</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>100</mn></mrow><mrow><mn>1040</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mfrac><mrow><mn>3</mn></mrow><mrow><mn>2</mn></mrow></mfrac></mrow></mfrac></math><br>दर = <math display=\"inline\"><mfrac><mrow><mn>475</mn></mrow><mrow><mn>39</mn></mrow></mfrac></math> = 12<math display=\"inline\"><mfrac><mrow><mi>&#160;</mi><mn>7</mn></mrow><mrow><mn>39</mn></mrow></mfrac></math>%</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "74",
                    section: "17",
                    question_en: "<p>74. The table shows the production of different types of bikes (in thousands).<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731132154022.png\" alt=\"rId73\" width=\"241\" height=\"139\"> <br>The number of years, in which the production of bikes of type C is less than the average production of type A bikes over the years is</p>",
                    question_hi: "<p>74. तालिका विभिन्न प्रकार की बाइकों के उत्पादन (हजार में) को दर्शाती है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731132154189.png\" alt=\"rId74\" width=\"201\" height=\"161\"> <br>उन वर्षों की संख्या ज्ञात करें, जिनमें C प्रकार की बाइक का उत्पादन, विगत वर्षों में A प्रकार की बाइक के औसत उत्पादन से कम है?</p>",
                    options_en: ["<p>4</p>", "<p>3</p>", 
                                "<p>1</p>", "<p>2</p>"],
                    options_hi: ["<p>4</p>", "<p>3</p>",
                                "<p>1</p>", "<p>2</p>"],
                    solution_en: "<p>74.(d) Average production of type A bike = <math display=\"inline\"><mfrac><mrow><mn>157</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>194</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>271</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>290</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> = 228<br>In 2 years (2016 , 2017 ) the production of bikes of type C is less than the average production of type A bikes .</p>",
                    solution_hi: "<p>74.(d) टाइप A बाइक का औसत उत्पादन = <math display=\"inline\"><mfrac><mrow><mn>157</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>194</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>271</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>290</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> = 228<br>2 वर्षों (2016, 2017) में टाइप C बाइक का उत्पादन टाइप A बाइक के औसत उत्पादन से कम है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "75",
                    section: "7",
                    question_en: "<p>75. If the height and diameter of a cylinder are 12 cm and 28 cm, respectively, then find the ratio of the total surface area to the curved surface area.</p>",
                    question_hi: "<p>75. यदि एक बेलन की ऊँचाई और व्यास क्रमशः 12 cm और 28 cm है, तो कुल पृष्ठीय क्षेत्रफल और वक्र पृष्ठीय क्षेत्रफल का अनुपात ज्ञात कीजिए।</p>",
                    options_en: ["<p>13 : 5</p>", "<p>10 : 3</p>", 
                                "<p>13 : 6</p>", "<p>11 : 6</p>"],
                    options_hi: ["<p>13 : 5</p>", "<p>10 : 3</p>",
                                "<p>13 : 6</p>", "<p>11 : 6</p>"],
                    solution_en: "<p>75.(c) Height and diameter of a cylinder are 12 cm and 28cm <br>Total surface area of cylinder = 22&pi;r(r + h)<br>= 2 &times; <math display=\"inline\"><mfrac><mrow><mn>22</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> &times; 14 (14 + 12)<br>= 88 &times; 26<br>Curved Surface area of cylinder = 2&pi;rh&nbsp;= 2 &times; <math display=\"inline\"><mfrac><mrow><mn>22</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> &times; 14 &times; 12<br>= 88 &times; 12<br>The ratio of the total surface area to the curved surface area = 88 &times; 26 : 88 &times; 12<br>= 13 : 6</p>",
                    solution_hi: "<p>75.(c) एक बेलन की ऊंचाई और व्यास 12 सेमी और 28 सेमी है <br>बेलन का कुल पृष्ठीय क्षेत्रफल = 22&pi;r(r + h)<br>= 2 &times; <math display=\"inline\"><mfrac><mrow><mn>22</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> &times; 14 (14 + 12)<br>= 88 &times; 26<br>बेलन का वक्र पृष्ठीय क्षेत्रफल = 2&pi;rh&nbsp;= 2 &times; <math display=\"inline\"><mfrac><mrow><mn>22</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> &times; 14 &times; 12<br>= 88 &times; 12<br>कुल सतह क्षेत्रफल का वक्र सतह क्षेत्रफल से अनुपात = 88 &times; 26 : 88 &times; 12<br>= 13 : 6</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "76",
                    section: "7",
                    question_en: "<p>76. Select the most appropriate meaning of the following idiom.<br>To bring to light</p>",
                    question_hi: "<p>76. Select the most appropriate meaning of the following idiom.<br>To bring to light</p>",
                    options_en: ["<p>To engage in conversation</p>", "<p>To lighten</p>", 
                                "<p>To disclose</p>", "<p>To disengage</p>"],
                    options_hi: ["<p>To engage in conversation</p>", "<p>To lighten</p>",
                                "<p>To disclose</p>", "<p>To disengage</p>"],
                    solution_en: "<p>76.(c) <strong>To bring to light-</strong> to disclose.<br>E.g.- The investigation brought to light several previously unknown facts about the case.</p>",
                    solution_hi: "<p>76.(c) <strong>To bring to light- </strong>to disclose./ उजागर करना।<br>E.g.- The investigation brought to light several previously unknown facts about the case.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "77",
                    section: "7",
                    question_en: "<p>77. Parts of a sentence are given below in jumbled order. Arrange the parts in the correct order to form a meaningful sentence.<br>(a) false news websites<br>(b) spreading propaganda about politics<br>(c) thrive because the advertisements and politicians<br>(d) and social networking sites<br>(e) make a lot of money</p>",
                    question_hi: "<p>77. Parts of a sentence are given below in jumbled order. Arrange the parts in the correct order to form a meaningful sentence.<br>(a) false news websites<br>(b) spreading propaganda about politics<br>(c) thrive because the advertisements and politicians<br>(d) and social networking sites<br>(e) make a lot of money</p>",
                    options_en: ["<p>aecbd</p>", "<p>adcbe</p>", 
                                "<p>adecb</p>", "<p>aedcb</p>"],
                    options_hi: ["<p>aecbd</p>", "<p>adcbe</p>",
                                "<p>adecb</p>", "<p>aedcb</p>"],
                    solution_en: "<p>77.(b) <strong>adcbe</strong><br>The given sentence starts with Part (a) as it introduces the main subject of the sentence, i.e. false news websites. Part (d) contains the other part of the subject joined by conjunction &lsquo;and&rsquo; &amp; Part (c) has the main verb of the sentence. So, Part (c) will follow Part (d). Further, Part (b) states that advertisements and politicians spread propaganda about politics &amp; Part (e) states that such advertisements and politicians make a lot of money. So, Part (e) will follow Part (b). Going through the options, option &lsquo;b&rsquo; has the correct sequence.</p>",
                    solution_hi: "<p>77.(b) <strong>adcbe</strong><br>दिया गया sentence Part (a) से प्रारंभ होता है क्योंकि इसमें sentence का main subject, &lsquo;false news websites&rsquo; शामिल है। Part (d) में conjunction &lsquo;and&rsquo; से जुड़ा हुआ subject का दूसरा भाग शामिल है और Part (c) में sentence की main verb है। इसलिए, Part (d) के बाद Part (c) आएगा। इसके अलावा, Part (b) में कहा गया है कि advertisements और politicians राजनीति के बारे में प्रचार करते हैं और Part (e) में कहा गया है कि ऐसे advertisements और politicians बहुत पैसा कमाते हैं। इसलिए, Part (b) के बाद Part (e) आएगा। अतः options के माध्यम से जाने पर, option &lsquo;b&rsquo; में सही sequence है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "78",
                    section: "7",
                    question_en: "<p>78. Parts of a sentence are given below in jumbled order. Arrange the parts in the correct order to form a meaningful sentence.<br>P) willing to go<br>Q) is not<br>R) my son<br>S) camping</p>",
                    question_hi: "<p>78. Parts of a sentence are given below in jumbled order. Arrange the parts in the correct order to form a meaningful sentence.<br>P) willing to go<br>Q) is not<br>R) my son<br>S) camping</p>",
                    options_en: ["<p>RQPS</p>", "<p>SPRQ</p>", 
                                "<p>PQRS</p>", "<p>QRSP</p>"],
                    options_hi: ["<p>RQPS</p>", "<p>SPRQ</p>",
                                "<p>PQRS</p>", "<p>QRSP</p>"],
                    solution_en: "<p>78.(a) <strong>RQPS</strong><br>The correct sentence is &ldquo;My son is not willing to go camping.&rdquo;</p>",
                    solution_hi: "<p>78.(a) <strong>RQPS</strong><br>&ldquo;My son is not willing to go camping.&rdquo; सही sentence है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "79",
                    section: "7",
                    question_en: "79. Select the most appropriate homophone to fill in the blank.<br />The ointment will help to ___________ the wound.",
                    question_hi: "79. Select the most appropriate homophone to fill in the blank.<br />The ointment will help to ___________ the wound.",
                    options_en: ["  he\'ll", "  heel", 
                                "  heal", " kneel"],
                    options_hi: ["  he\'ll", "  heel",
                                "  heal", " kneel"],
                    solution_en: "79.(c) heal<br />‘Heal’ means to cause (a wound, injury, or person) to become sound or healthy again. The given sentence states that the ointment will help to heal the wound. Hence, ‘heal’ is the most appropriate answer.",
                    solution_hi: "79.(c) heal<br />‘Heal’ का अर्थ है फिर से स्वस्थ या ठीक करना (घाव, चोट या व्यक्ति को)। दिए गए sentence में कहा गया है कि ointment, घाव को ठीक करने में मदद करेगा। अतः, ‘heal’ सबसे उपयुक्त उत्तर है।",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "80",
                    section: "7",
                    question_en: "<p>80. Select the most appropriate ANTONYM of the given word.<br>Attack</p>",
                    question_hi: "<p>80. Select the most appropriate ANTONYM of the given word.<br>Attack</p>",
                    options_en: ["<p>Ambush</p>", "<p>Defence</p>", 
                                "<p>Production</p>", "<p>Engagement</p>"],
                    options_hi: ["<p>Ambush</p>", "<p>Defence</p>",
                                "<p>Production</p>", "<p>Engagement</p>"],
                    solution_en: "<p>80.(b) <strong>Defence-</strong> protection against harm or attack.<br><strong>Attack- </strong>aggressive action against something or someone.<br><strong>Ambush-</strong> a surprise attack.<br><strong>Production- </strong>the process of creating or manufacturing something.<br><strong>Engagement- </strong>involvement or participation in an activity or event.</p>",
                    solution_hi: "<p>80.(b) <strong>Defence</strong> (सुरक्षा/रक्षा)- protection against harm or attack.<br><strong>Attack</strong> (आक्रमण)- aggressive action against something or someone.<br><strong>Ambush</strong> (घात लगाना)- a surprise attack.<br><strong>Production </strong>(उत्पादन/निर्माण)- the process of creating or manufacturing something.<br><strong>Engagement</strong> (भागीदारी/शामिल होना)- involvement or participation in an activity or event.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "81",
                    section: "7",
                    question_en: "81. Sentences of a paragraph are given below in jumbled order. Arrange the sentences in the correct order to form a meaningful and coherent paragraph.<br />A. It was as simple as announcing what you have in your store or the services you offer in your premises.<br />B. Over the years, advertising has evolved into a major industry that goes beyond informing to persuading and influencing.<br />C. Advertising was initially meant to make people aware of the goods available in the market.<br />D. It is a form of brainwashing consumers.",
                    question_hi: "81. Sentences of a paragraph are given below in jumbled order. Arrange the sentences in the correct order to form a meaningful and coherent paragraph.<br />A. It was as simple as announcing what you have in your store or the services you offer in your premises.<br />B. Over the years, advertising has evolved into a major industry that goes beyond informing to persuading and influencing.<br />C. Advertising was initially meant to make people aware of the goods available in the market.<br />D. It is a form of brainwashing consumers.",
                    options_en: [" BADC ", "  ADBC", 
                                " DABC ", " CABD"],
                    options_hi: [" BADC ", "  ADBC",
                                " DABC ", " CABD"],
                    solution_en: "81.(d) CABD<br />Sentence C will be the starting line as it introduces the main idea of the parajumble, i.e. advertising was initially meant to make people aware of the goods available in the market. And, Sentence A talks about the simplicity of advertising. So, A will follow C. Further, Sentence B states that advertising has evolved over the years & Sentence D states that now it is a form of brainwashing consumers. So, D will follow B. Going through the options, option ‘d’ has the correct sequence.",
                    solution_hi: "81.(d) CABD<br />Sentence C प्रारंभिक line होगी क्योंकि यह parajumble के मुख्य विचार ‘advertising was initially meant to make people aware of the goods available in the market’ का परिचय देता है। और, Sentence A, advertising की simplicity के बारे में बात करता है। इसलिए, C के बाद A आएगा। इसके अलावा, Sentence B बताता है कि advertising पिछले कुछ वर्षों में विकसित हुआ है और Sentence D बताता है कि अब यह उपभोक्ताओं को गुमराह (brainwashing) का एक तरीका है। इसलिए, B के बाद  D आएगा। अतः options के माध्यम से जाने पर,  option ‘d’ में सही sequence है।",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "82",
                    section: "7",
                    question_en: "<p>82. Select the most appropriate ANTONYM of the underlined word.<br>We should plan our actions <span style=\"text-decoration: underline;\">judiciously</span> before going to war.</p>",
                    question_hi: "<p>82. Select the most appropriate ANTONYM of the underlined word.<br>We should plan our actions <span style=\"text-decoration: underline;\">judiciously</span> before going to war.</p>",
                    options_en: ["<p>Irreverent</p>", "<p>Irrational</p>", 
                                "<p>Irrelevant</p>", "<p>Irritatingly</p>"],
                    options_hi: ["<p>Irreverent</p>", "<p>Irrational</p>",
                                "<p>Irrelevant</p>", "<p>Irritatingly</p>"],
                    solution_en: "<p>82.(b) <strong>Irrational-</strong> lacking reason or logic.<br><strong>Judiciously-</strong> with good judgment or sense.<br><strong>Irreverent- </strong>showing a lack of respect for things usually taken seriously.<br><strong>Irrelevant-</strong> not related to the matter at hand.<br><strong>Irritatingly- </strong>in a way that causes annoyance or irritation.</p>",
                    solution_hi: "<p>82.(b) <strong>Irrational</strong> (अतार्किक)- lacking reason or logic.<br><strong>Judiciously</strong> (विवेकपूर्वक)- with good judgment or sense.<br><strong>Irreverent </strong>(अप्रासंगिक)- showing a lack of respect for things usually taken seriously.<br><strong>Irrelevant</strong> (असंगत)- not related to the matter at hand.<br><strong>Irritatingly</strong> (चिड़चिड़ाहट से)- in a way that causes annoyance or irritation.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "83",
                    section: "7",
                    question_en: "83. Select the sentence that has a grammatical error.",
                    question_hi: "83. Select the sentence that has a grammatical error.",
                    options_en: [" It is said that Joseph was not ready to go to war. ", " Karl Marx was a renowned social scientist. ", 
                                " Indian force are known for their loyalty and integrity. ", " It is impossible to wake Christine up in the morning."],
                    options_hi: [" It is said that Joseph was not ready to go to war. ", " Karl Marx was a renowned social scientist. ",
                                " Indian force are known for their loyalty and integrity. ", " It is impossible to wake Christine up in the morning."],
                    solution_en: "83.(c) Indian force are known for their loyalty and integrity.<br />‘Forces’ will be used instead of ‘force’ according to the plural verb ‘are’ and the plural adjective ‘their’. Hence, the correct sentence is: “Indian forces are known for their loyalty and integrity.”",
                    solution_hi: "83.(c) Indian force are known for their loyalty and integrity.<br />Plural verb ‘are’ और  plural adjective ‘their’ के अनुसार ‘force’ के स्थान पर ‘forces’ का use किया जाएगा। अतः, “Indian forces are known for their loyalty and integrity” सही sentence है। ",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "84",
                    section: "7",
                    question_en: "<p>84. Select the correct meaning of the underlined idiom in the following sentence.<br>Rina <span style=\"text-decoration: underline;\">pulled a long face</span> since her husband had not bought her a diamond necklace on their anniversary.</p>",
                    question_hi: "<p>84. Select the correct meaning of the underlined idiom in the following sentence.<br>Rina <span style=\"text-decoration: underline;\">pulled a long face</span> since her husband had not bought her a diamond necklace on their anniversary.</p>",
                    options_en: ["<p>To be euphoric</p>", "<p>To be ecstatic</p>", 
                                "<p>To look saddened</p>", "<p>To be electrified</p>"],
                    options_hi: ["<p>To be euphoric</p>", "<p>To be ecstatic</p>",
                                "<p>To look saddened</p>", "<p>To be electrified</p>"],
                    solution_en: "<p>84.(c) <strong>Pulled a long face-</strong> to look saddened.</p>",
                    solution_hi: "<p>84.(c) <strong>Pulled a long face-</strong> to look saddened./उदास या दुःखी दिखना।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "85",
                    section: "7",
                    question_en: "85. Select the correct spelling to fill in the blank.<br />The thought of eating half-cooked food under compulsion fills me with _________ .",
                    question_hi: "85. Select the correct spelling to fill in the blank.<br />The thought of eating half-cooked food under compulsion fills me with _________ .",
                    options_en: ["  ripugnance", " repegnance ", 
                                " repugnance ", " repugnence"],
                    options_hi: ["  ripugnance", " repegnance ",
                                " repugnance ", " repugnence"],
                    solution_en: "85.(c) repugnance<br />\'Repugnance\' is the correct spelling.",
                    solution_hi: "85.(c) repugnance<br />\'Repugnance\' सही spelling है। ",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "86",
                    section: "7",
                    question_en: "86. Select the INCORRECTLY spelt word from the given sentence.<br />The resturant offers a wide variety of dishes.",
                    question_hi: "86. Select the INCORRECTLY spelt word from the given sentence.<br />The resturant offers a wide variety of dishes.",
                    options_en: [" variety", "  resturant", 
                                "  dishes", " offers"],
                    options_hi: [" variety", "  resturant",
                                "  dishes", " offers"],
                    solution_en: "86.(b) resturant<br />\'Restaurant\' is the correct spelling.",
                    solution_hi: "86.(b) resturant<br />\'Restaurant\' सही spelling है। ",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "87",
                    section: "7",
                    question_en: "<p>87. Select the option that expresses the given sentence in passive voice.<br>They are building a new hospital in the city.</p>",
                    question_hi: "<p>87. Select the option that expresses the given sentence in passive voice.<br>They are building a new hospital in the city.</p>",
                    options_en: ["<p>A new hospital was built in the city by them.</p>", "<p>A new hospital has been built in the city by them.</p>", 
                                "<p>A new hospital will be built in the city by them.</p>", "<p>A new hospital is being built in the city by them.</p>"],
                    options_hi: ["<p>A new hospital was built in the city by them.</p>", "<p>A new hospital has been built in the city by them.</p>",
                                "<p>A new hospital will be built in the city by them.</p>", "<p>A new hospital is being built in the city by them.</p>"],
                    solution_en: "<p>87.(d) A new hospital is being built in the city by them. (Correct)<br>(a) A new hospital <span style=\"text-decoration: underline;\">was built</span> in the city by them. (Incorrect Tense)<br>(b) A new hospital <span style=\"text-decoration: underline;\">has been built</span> in the city by them. (Incorrect Tense)<br>(c) A new hospital <span style=\"text-decoration: underline;\">will be built</span> in the city by them. (Incorrect Tense)</p>",
                    solution_hi: "<p>87.(d) A new hospital is being built in the city by them. (Correct)<br>(a) A new hospital <span style=\"text-decoration: underline;\">was built</span> in the city by them. (गलत Tense)<br>(b) A new hospital <span style=\"text-decoration: underline;\">has been built</span> in the city by them. (गलत Tense)<br>(c) A new hospital <span style=\"text-decoration: underline;\">will be built</span> in the city by them. (गलत Tense)</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "88",
                    section: "7",
                    question_en: "<p>88. Select the most appropriate option to substitute the underlined part of the given sentence. If there is no need to substitute it, select \'No substitution\'.<br>As the rulers of the planet, humans like to think that <span style=\"text-decoration: underline;\">it is the largest creatures</span> that will emerge victorious from the struggle for survival.</p>",
                    question_hi: "<p>88. Select the most appropriate option to substitute the underlined part of the given sentence. If there is no need to substitute it, select \'No substitution\'.<br>As the rulers of the planet, humans like to think that <span style=\"text-decoration: underline;\">it is the largest creatures</span> that will emerge victorious from the struggle for survival.</p>",
                    options_en: ["<p>No substitution</p>", "<p>it is the most large creatures</p>", 
                                "<p>they are a largest creature</p>", "<p>they are the largest creatures</p>"],
                    options_hi: ["<p>No substitution</p>", "<p>it is the most large creatures</p>",
                                "<p>they are a largest creature</p>", "<p>they are the largest creatures</p>"],
                    solution_en: "<p>88.(d) they are the largest creatures<br>&lsquo;Humans&rsquo; is a plural noun that will take the plural pronoun &lsquo;they&rsquo; and the plural verb &lsquo;are&rsquo;. Hence, &lsquo;they are the largest creatures&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>88.(d) they are the largest creatures<br>&lsquo;Humans&rsquo;&lsquo; एक plural noun है जिसके साथ plural pronoun &lsquo;they&rsquo; और plural verb &lsquo;are&rsquo; का प्रयोग होगा। अतः, &lsquo;they are the largest creatures&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "89",
                    section: "7",
                    question_en: "<p>89. Select the most appropriate option to substitute the underlined segment in the given sentence.<br>Experts acknowledged that some <span style=\"text-decoration: underline;\">counterfeiting of document</span> techniques are virtually impossible to detect.</p>",
                    question_hi: "<p>89. Select the most appropriate option to substitute the underlined segment in the given sentence.<br>Experts acknowledged that some <span style=\"text-decoration: underline;\">counterfeiting of document</span> techniques are virtually impossible to detect.</p>",
                    options_en: ["<p>fiasco</p>", "<p>forgery</p>", 
                                "<p>impromptu</p>", "<p>frenzy</p>"],
                    options_hi: ["<p>fiasco</p>", "<p>forgery</p>",
                                "<p>impromptu</p>", "<p>frenzy</p>"],
                    solution_en: "<p>89.(b) forgery<br>&lsquo;Forgery&rsquo; means the crime of illegally copying something in order to deceive someone. The given sentence states that experts acknowledged that some forgery techniques are virtually impossible to detect. Hence, &lsquo;forgery&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>89.(b) forgery<br>&lsquo;Forgery&rsquo; का अर्थ है किसी को धोखा देने के लिए अवैध रूप से किसी चीज की नकल करना। दिए गए sentence में कहा गया है कि विशेषज्ञों(experts) ने स्वीकार किया है कि कुछ जालसाजी(forgery) तकनीकों का पता लगाना लगभग असंभव है। अतः, &lsquo;forgery&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "90",
                    section: "7",
                    question_en: "<p>90. Select the option that expresses the given sentence in passive voice.<br>They have built a new bridge across the river.</p>",
                    question_hi: "<p>90. Select the option that expresses the given sentence in passive voice.<br>They have built a new bridge across the river.</p>",
                    options_en: ["<p>A new bridge has been built across the river by them.</p>", "<p>A new bridge by them has been built across the river.</p>", 
                                "<p>A new bridge has built across the river by them.</p>", "<p>The river has been built a new bridge by them.</p>"],
                    options_hi: ["<p>A new bridge has been built across the river by them.</p>", "<p>A new bridge by them has been built across the river.</p>",
                                "<p>A new bridge has built across the river by them.</p>", "<p>The river has been built a new bridge by them.</p>"],
                    solution_en: "<p>90.(a) A new bridge has been built across the river by them. (correct)<br>(b) A new bridge by them has been built across the river. (Incorrect Sentence Structure)<br>(c) A new bridge <span style=\"text-decoration: underline;\">has built</span> across the river by them. (&lsquo;Been&rsquo; is missing)<br>(d) The river has been built a new bridge by them. (Incorrect Sentence Structure)</p>",
                    solution_hi: "<p>90.(a) A new bridge has been built across the river by them.(correct)<br>(b) A new bridge by them has been built across the river. (गलत Sentence Structure)<br>(c) A new bridge <span style=\"text-decoration: underline;\">has built</span> across the river by them. (&lsquo;Been&rsquo; missing है)<br>(d) The river has been built a new bridge by them. (गलत Sentence Structure)</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "91",
                    section: "7",
                    question_en: "<p>91. Select the most appropriate ANTONYM of the word in the brackets to fill in the blank.<br>The chef requires ___________ vegetables for the salad. [stale]</p>",
                    question_hi: "<p>91. Select the most appropriate ANTONYM of the word in the brackets to fill in the blank.<br>The chef requires ___________ vegetables for the salad. [stale]</p>",
                    options_en: ["<p>green</p>", "<p>fresh</p>", 
                                "<p>cut</p>", "<p>leafy</p>"],
                    options_hi: ["<p>green</p>", "<p>fresh</p>",
                                "<p>cut</p>", "<p>leafy</p>"],
                    solution_en: "<p>91.(b) <strong>Fresh -</strong> recently made or obtained.<br><strong>Stale -</strong> no longer fresh and pleasant.<br><strong>Leafy -</strong> full of leaves.</p>",
                    solution_hi: "<p>91.(b) <strong>Fresh </strong>(ताजा)- recently made or obtained.<br><strong>Stale </strong>(बासी)- no longer fresh and pleasant.<br><strong>Leafy </strong>(पत्तेदार)- full of leaves.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "92",
                    section: "7",
                    question_en: "<p>92. Select the most appropriate ANTONYM of the underlined word in the sentence given below.<br>I <span style=\"text-decoration: underline;\">refuse</span> to let anything come in the way of my dreams.</p>",
                    question_hi: "<p>92. Select the most appropriate ANTONYM of the underlined word in the sentence given below.<br>I <span style=\"text-decoration: underline;\">refuse</span> to let anything come in the way of my dreams.</p>",
                    options_en: ["<p>repress</p>", "<p>balk</p>", 
                                "<p>accord</p>", "<p>permit</p>"],
                    options_hi: ["<p>repress</p>", "<p>balk</p>",
                                "<p>accord</p>", "<p>permit</p>"],
                    solution_en: "<p>92.(d) <strong>Permit-</strong> to allow or give permission.<br><strong>Refuse-</strong> to indicate or show unwillingness to accept.<br><strong>Repress-</strong> to restrain or hold back.<br><strong>Balk-</strong> to hesitate or be unwilling to accept an idea or undertaking.<br><strong>Accord- </strong>to agree or be in harmony.</p>",
                    solution_hi: "<p>92.(d) <strong>Permit (</strong>अनुमति देना)- to allow or give permission.<br><strong>Refuse</strong> (अस्वीकार करना)- to indicate or show unwillingness to accept.<br><strong>Repress </strong>(रोकना)- to restrain or hold back.<br><strong>Balk</strong> (हिचकिचाना)- to hesitate or be unwilling to accept an idea or undertaking.<br><strong>Accord</strong> (सहमत होना)- to agree or be in harmony.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "93",
                    section: "7",
                    question_en: "<p>93. The following sentence has been split into four segments. Identify the segment that contains an error.<br>The principal gave up / the prizes to / the winners on / Republic Day.</p>",
                    question_hi: "<p>93. The following sentence has been split into four segments. Identify the segment that contains an error.<br>The principal gave up / the prizes to / the winners on / Republic Day.</p>",
                    options_en: ["<p>The principal gave up</p>", "<p>the winners on</p>", 
                                "<p>the prizes to</p>", "<p>Republic Day</p>"],
                    options_hi: ["<p>The principal gave up</p>", "<p>the winners on</p>",
                                "<p>the prizes to</p>", "<p>Republic Day</p>"],
                    solution_en: "<p>93.(a) The principal gave up<br>&lsquo;Gave out&rsquo; is the correct phrasal verb to use here. &lsquo;Give out&rsquo; means distributing something. The given sentence talks about the distribution of the prizes. Hence, &lsquo;The principal gave out&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>93.(a) The principal gave up<br>यहाँ प्रयोग करने के लिए सही phrasal verb \'gave out\' है। Give out&rsquo; का अर्थ है कुछ वितरित करना। दिया गया sentence पुरस्कारों के वितरण के बारे में बात करता है। अतः, &lsquo;The principal gave out&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "94",
                    section: "7",
                    question_en: "<p>94. Select the option that can be used as a one-word substitute for the underlined group of words in the following sentence.<br>Almost immediately, they heard the <span style=\"text-decoration: underline;\">high shrill piercing cry</span> of tires on the side road.</p>",
                    question_hi: "<p>94. Select the option that can be used as a one-word substitute for the underlined group of words in the following sentence.<br>Almost immediately, they heard the <span style=\"text-decoration: underline;\">high shrill piercing cry</span> of tires on the side road.</p>",
                    options_en: ["<p>beseech</p>", "<p>creche</p>", 
                                "<p>screech</p>", "<p>bleach</p>"],
                    options_hi: ["<p>beseech</p>", "<p>creche</p>",
                                "<p>screech</p>", "<p>bleach</p>"],
                    solution_en: "<p>94.(c) <strong>Screech-</strong> high shrill piercing cry.<br><strong>Beseech- </strong>to ask for something in a way that shows you need it very much.<br><strong>Creche-</strong> a nursery where babies and young children are cared for during the working day.<br><strong>Bleach-</strong> to remove color from by means of chemicals or by exposure to the sun\'s rays.</p>",
                    solution_hi: "<p>94.(c) <strong>Screech </strong>(चीख़)- high shrill piercing cry.<br><strong>Beseech</strong> (विनती करना)- to ask for something in a way that shows you need it very much.<br><strong>Creche </strong>(शिशुगृह)- a nursery where babies and young children are cared for during the working day.<br><strong>Bleach</strong> (विरंजित करना)- to remove color from by means of chemicals or by exposure to the sun\'s rays.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "95",
                    section: "7",
                    question_en: "<p>95. Select the most appropriate ANTONYM of the given word.<br>Synthetic</p>",
                    question_hi: "<p>95. Select the most appropriate ANTONYM of the given word.<br>Synthetic</p>",
                    options_en: ["<p>Artificial</p>", "<p>Imitation</p>", 
                                "<p>Processed</p>", "<p>Natural</p>"],
                    options_hi: ["<p>Artificial</p>", "<p>Imitation</p>",
                                "<p>Processed</p>", "<p>Natural</p>"],
                    solution_en: "<p>95.(d) <strong>Natural- </strong>existing in or produced by nature.<br><strong>Synthetic- </strong>made by chemical synthesis, especially to imitate a natural product.<br><strong>Artificial-</strong> made or produced by human beings rather than occurring naturally.<br><strong>Imitation-</strong> a copy or reproduction of something, often to mimic its characteristics.<br><strong>Processed-</strong> treated or prepared by a series of mechanical or chemical operations for use or sale.</p>",
                    solution_hi: "<p>95.(d) <strong>Natural</strong> (प्राकृतिक)- existing in or produced by nature.<br><strong>Synthetic </strong>(संश्लेषित)- made by chemical synthesis, especially to imitate a natural product.<br><strong>Artificial </strong>(कृत्रिम)- made or produced by human beings rather than occurring naturally.<br><strong>Imitation</strong> (प्रतिरूपता)- a copy or reproduction of something, often to mimic its characteristics.<br><strong>Processed </strong>(प्रसंस्कृत)- treated or prepared by a series of mechanical or chemical operations for use or sale.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "96",
                    section: "7",
                    question_en: "<p>96. <strong>Cloze Test:</strong><br>The adverse (96)______ of climate change and environmental degradation are increasingly driving human mobility the world over, particularly in countries with high exposure and low (97) ________ capacity. While most climate-related mobility currently occurs within countries, desperation and deteriorating environments can also compel people to seek a livelihood elsewhere through (98) _______ migration. While climate change negatively impacts everyone, everywhere, those already in vulnerable situations due to geography, poverty, gender, age, disability, origin, or other status, including migrant women who (99) _______ climate-sensitive livelihoods, and children who are less able to survive extreme weather events, are at the greatest risk of suffering harm. It is (100) _______ to recognise this reality and take meaningful action to protect the human rights of those most affected by climate change, including migrants.<br>Select the most appropriate option to fill in blank number 96.</p>",
                    question_hi: "<p>96. <strong>Cloze Test:</strong><br>The adverse (96)______ of climate change and environmental degradation are increasingly driving human mobility the world over, particularly in countries with high exposure and low (97) ________ capacity. While most climate-related mobility currently occurs within countries, desperation and deteriorating environments can also compel people to seek a livelihood elsewhere through (98) _______ migration. While climate change negatively impacts everyone, everywhere, those already in vulnerable situations due to geography, poverty, gender, age, disability, origin, or other status, including migrant women who (99) _______ climate-sensitive livelihoods, and children who are less able to survive extreme weather events, are at the greatest risk of suffering harm. It is (100) _______ to recognise this reality and take meaningful action to protect the human rights of those most affected by climate change, including migrants.<br>Select the most appropriate option to fill in blank number 96.</p>",
                    options_en: ["<p>renounce</p>", "<p>affects</p>", 
                                "<p>effects</p>", "<p>practice</p>"],
                    options_hi: ["<p>renounce</p>", "<p>affects</p>",
                                "<p>effects</p>", "<p>practice</p>"],
                    solution_en: "<p>96.(c) <strong>effects</strong><br>&lsquo;Effect&rsquo; means the result of a particular action. The given passage states that the adverse effects of climate change and environmental degradation are increasingly driving human mobility the world over. Hence, &lsquo;effects&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>96.(c) <strong>effects</strong><br>&lsquo;Effect&rsquo; का अर्थ है किसी विशेष action का परिणाम। दिए गए passage में कहा गया है कि जलवायु परिवर्तन(climate change) और पर्यावरणीय क्षरण(degradation) के प्रतिकूल प्रभाव दुनिया भर में मानव गतिशीलता(mobility) को तेजी से बढ़ा रहे हैं। अतः, &lsquo;effects&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "97",
                    section: "7",
                    question_en: "<p>97. <strong>Cloze Test:</strong><br>The adverse (96)______ of climate change and environmental degradation are increasingly driving human mobility the world over, particularly in countries with high exposure and low (97) ________ capacity. While most climate-related mobility currently occurs within countries, desperation and deteriorating environments can also compel people to seek a livelihood elsewhere through (98) _______ migration. While climate change negatively impacts everyone, everywhere, those already in vulnerable situations due to geography, poverty, gender, age, disability, origin, or other status, including migrant women who (99) _______ climate-sensitive livelihoods, and children who are less able to survive extreme weather events, are at the greatest risk of suffering harm. It is (100) _______ to recognise this reality and take meaningful action to protect the human rights of those most affected by climate change, including migrants.<br>Select the most appropriate option to fill in blank number 97.</p>",
                    question_hi: "<p>97. <strong>Cloze Test:</strong><br>The adverse (96)______ of climate change and environmental degradation are increasingly driving human mobility the world over, particularly in countries with high exposure and low (97) ________ capacity. While most climate-related mobility currently occurs within countries, desperation and deteriorating environments can also compel people to seek a livelihood elsewhere through (98) _______ migration. While climate change negatively impacts everyone, everywhere, those already in vulnerable situations due to geography, poverty, gender, age, disability, origin, or other status, including migrant women who (99) _______ climate-sensitive livelihoods, and children who are less able to survive extreme weather events, are at the greatest risk of suffering harm. It is (100) _______ to recognise this reality and take meaningful action to protect the human rights of those most affected by climate change, including migrants.<br>Select the most appropriate option to fill in blank number 97.</p>",
                    options_en: ["<p>adaptive</p>", "<p>adoptive</p>", 
                                "<p>minimum</p>", "<p>affective</p>"],
                    options_hi: ["<p>adaptive</p>", "<p>adoptive</p>",
                                "<p>minimum</p>", "<p>affective</p>"],
                    solution_en: "<p>97.(a) <strong>adaptive</strong><br>&lsquo;Adaptive&rsquo; means having the ability or tendency to adapt to different situations. The given passage states that the adverse effects of climate change and environmental degradation are increasingly driving human mobility the world over, particularly in countries with high exposure and low adaptive capacity. Hence, &lsquo;adaptive&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>97.(a) <strong>adaptive</strong><br>&lsquo;Adaptive&rsquo; का अर्थ है विभिन्न परिस्थितियों के अनुकूल होने की क्षमता या प्रवृत्ति होना। दिए गए passage में कहा गया है कि जलवायु परिवर्तन और पर्यावरणीय क्षरण के प्रतिकूल प्रभाव दुनिया भर में मानव गतिशीलता को तेजी से बढ़ा रहे हैं, विशेषकर उन देशों में जहां उच्च अवशोषण (high exposure) और कम अनुकूलन (low adaptive) क्षमता है। अतः, &lsquo;adaptive&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "98",
                    section: "7",
                    question_en: "<p>98. <strong>Cloze Test:</strong><br>The adverse (96)______ of climate change and environmental degradation are increasingly driving human mobility the world over, particularly in countries with high exposure and low (97) ________ capacity. While most climate-related mobility currently occurs within countries, desperation and deteriorating environments can also compel people to seek a livelihood elsewhere through (98) _______ migration. While climate change negatively impacts everyone, everywhere, those already in vulnerable situations due to geography, poverty, gender, age, disability, origin, or other status, including migrant women who (99) _______ climate-sensitive livelihoods, and children who are less able to survive extreme weather events, are at the greatest risk of suffering harm. It is (100) _______ to recognise this reality and take meaningful action to protect the human rights of those most affected by climate change, including migrants.<br>Select the most appropriate option to fill in blank number 98.</p>",
                    question_hi: "<p>98. <strong>Cloze Test:</strong><br>The adverse (96)______ of climate change and environmental degradation are increasingly driving human mobility the world over, particularly in countries with high exposure and low (97) ________ capacity. While most climate-related mobility currently occurs within countries, desperation and deteriorating environments can also compel people to seek a livelihood elsewhere through (98) _______ migration. While climate change negatively impacts everyone, everywhere, those already in vulnerable situations due to geography, poverty, gender, age, disability, origin, or other status, including migrant women who (99) _______ climate-sensitive livelihoods, and children who are less able to survive extreme weather events, are at the greatest risk of suffering harm. It is (100) _______ to recognise this reality and take meaningful action to protect the human rights of those most affected by climate change, including migrants.<br>Select the most appropriate option to fill in blank number 98.</p>",
                    options_en: ["<p>regard</p>", "<p>fast</p>", 
                                "<p>fixate</p>", "<p>irregular</p>"],
                    options_hi: ["<p>regard</p>", "<p>fast</p>",
                                "<p>fixate</p>", "<p>irregular</p>"],
                    solution_en: "<p>98.(d)<strong> irregular</strong><br>&lsquo;Irregular&rsquo; means not even or balanced in arrangement. The given passage states that desperation and deteriorating environments can also compel people to seek a livelihood elsewhere through irregular migration.<br>Hence, &lsquo;irregular&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>98.(d)<strong> irregular</strong><br>&lsquo;Irregular&rsquo; का अर्थ है व्यवस्था में संतुलन न होना। दिए गए passage में कहा गया है कि हताशा (desperation) और दूषित पर्यावरण (deteriorating environments) भी अनियमित migration के माध्यम से अन्यत्र आजीविका की तलाश करने के लिए मजबूर कर सकते हैं। अतः, &lsquo;irregular&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "99",
                    section: "7",
                    question_en: "<p>99. <strong>Cloze Test:</strong><br>The adverse (96)______ of climate change and environmental degradation are increasingly driving human mobility the world over, particularly in countries with high exposure and low (97) ________ capacity. While most climate-related mobility currently occurs within countries, desperation and deteriorating environments can also compel people to seek a livelihood elsewhere through (98) _______ migration. While climate change negatively impacts everyone, everywhere, those already in vulnerable situations due to geography, poverty, gender, age, disability, origin, or other status, including migrant women who (99) _______ climate-sensitive livelihoods, and children who are less able to survive extreme weather events, are at the greatest risk of suffering harm. It is (100) _______ to recognise this reality and take meaningful action to protect the human rights of those most affected by climate change, including migrants.<br>Select the most appropriate option to fill in blank number 99.</p>",
                    question_hi: "<p>99. <strong>Cloze Test:</strong><br>The adverse (96)______ of climate change and environmental degradation are increasingly driving human mobility the world over, particularly in countries with high exposure and low (97) ________ capacity. While most climate-related mobility currently occurs within countries, desperation and deteriorating environments can also compel people to seek a livelihood elsewhere through (98) _______ migration. While climate change negatively impacts everyone, everywhere, those already in vulnerable situations due to geography, poverty, gender, age, disability, origin, or other status, including migrant women who (99) _______ climate-sensitive livelihoods, and children who are less able to survive extreme weather events, are at the greatest risk of suffering harm. It is (100) _______ to recognise this reality and take meaningful action to protect the human rights of those most affected by climate change, including migrants.<br>Select the most appropriate option to fill in blank number 99.</p>",
                    options_en: ["<p>depend from</p>", "<p>depend at</p>", 
                                "<p>depend on</p>", "<p>depend in</p>"],
                    options_hi: ["<p>depend from</p>", "<p>depend at</p>",
                                "<p>depend on</p>", "<p>depend in</p>"],
                    solution_en: "<p>99.(c) <strong>depend on</strong><br>&lsquo;On&rsquo; is a fixed preposition used after &lsquo;depend&rsquo;. Hence, &lsquo;depend on&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>99.(c) <strong>depend on</strong><br>&lsquo;On&rsquo; एक fixed preposition है जिसका प्रयोग &lsquo;depend&rsquo; के बाद किया जाता है। अतः, &lsquo;depend on&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "100",
                    section: "misc",
                    question_en: "<p>100. <strong>Cloze Test:</strong><br>The adverse (96)______ of climate change and environmental degradation are increasingly driving human mobility the world over, particularly in countries with high exposure and low (97) ________ capacity. While most climate-related mobility currently occurs within countries, desperation and deteriorating environments can also compel people to seek a livelihood elsewhere through (98) _______ migration. While climate change negatively impacts everyone, everywhere, those already in vulnerable situations due to geography, poverty, gender, age, disability, origin, or other status, including migrant women who (99) _______ climate-sensitive livelihoods, and children who are less able to survive extreme weather events, are at the greatest risk of suffering harm. It is (100) _______ to recognise this reality and take meaningful action to protect the human rights of those most affected by climate change, including migrants.<br>Select the most appropriate option to fill in blank number 100.</p>",
                    question_hi: "<p>100. <strong>Cloze Test:</strong><br>The adverse (96)______ of climate change and environmental degradation are increasingly driving human mobility the world over, particularly in countries with high exposure and low (97) ________ capacity. While most climate-related mobility currently occurs within countries, desperation and deteriorating environments can also compel people to seek a livelihood elsewhere through (98) _______ migration. While climate change negatively impacts everyone, everywhere, those already in vulnerable situations due to geography, poverty, gender, age, disability, origin, or other status, including migrant women who (99) _______ climate-sensitive livelihoods, and children who are less able to survive extreme weather events, are at the greatest risk of suffering harm. It is (100) _______ to recognise this reality and take meaningful action to protect the human rights of those most affected by climate change, including migrants.<br>Select the most appropriate option to fill in blank number 100.</p>",
                    options_en: ["<p>optional</p>", "<p>secondary</p>", 
                                "<p>resistant</p>", "<p>imperative</p>"],
                    options_hi: ["<p>optional</p>", "<p>secondary</p>",
                                "<p>resistant</p>", "<p>imperative</p>"],
                    solution_en: "<p>100.(d) <strong>imperative</strong><br>&lsquo;Imperative&rsquo; means extremely important or urgent. The given passage states that it is imperative to recognise this reality. Hence, &lsquo;imperative&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>100.(d) <strong>imperative</strong><br>&lsquo;Imperative&rsquo; का अर्थ है अत्यंत महत्वपूर्ण। दिए गए passage में कहा गया है कि इस वास्तविकता(reality) को पहचानना अनिवार्य(imperative) है। अतः, &lsquo;imperative&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>