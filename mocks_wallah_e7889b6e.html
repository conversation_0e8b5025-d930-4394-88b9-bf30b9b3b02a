<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #7209b7;
            --secondary: #4361ee;
            --accent: #f72585;
            --success: #06d6a0;
            --warning: #ffd166;
            --danger: #ef476f;
            --light: #f8f9fa;
            --dark: #212529;
        }
        body {
            background-color: #f0f2f5;
            font-size: 16px;
            padding-bottom: 60px; /* Space for fixed navigation */
        }
        .navbar {
            background: linear-gradient(135deg, var(--primary), var(--secondary)) !important;
            box-shadow: 0 2px 10px rgba(0,0,0,.1);
            padding: 0.5rem 1rem;
        }
        .navbar-brand {
            font-size: 1.1rem;
            margin-right: 0;
        }
        .navbar .container-fluid {
            justify-content: space-between;
        }
        .controls-group {
            display: flex;
            align-items: center;
            flex-wrap: nowrap;
        }
        .timer-display {
            background: linear-gradient(135deg, #4361ee, #3a0ca3);
            color: white;
            border-radius: 50px;
            box-shadow: 0 3px 8px rgba(0,0,0,.1);
            padding: 0.25rem 0.75rem;
            font-size: 0.875rem;
            white-space: nowrap;
        }
        .nav-btn {
            padding: 0.25rem 0.75rem;
            font-size: 0.875rem;
            margin-left: 0.25rem;
        }
        .question-nav {
            position: fixed;
            top: 56px;
            right: 0;
            height: calc(100vh - 56px);
            width: 280px;
            transform: translateX(280px);
            transition: transform .3s;
            z-index: 100;
            background: #fff;
            box-shadow: -5px 0 15px rgba(0,0,0,.1);
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .main-content {
            transition: margin .3s;
            min-height: calc(100vh - 116px);
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.5;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(32px, 1fr));
            gap: 6px;
        }
        .q-box {
            width: 35px;
            height: 35px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 5px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid #dee2e6;
            background: #fff;
            transition: all .2s;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: 0 3px 5px rgba(0,0,0,.1);
        }
        .q-box.attempted {
            background-color: var(--secondary);
            color: white;
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,.15);
        }
        .card {
            border-radius: 12px;
            overflow: hidden;
            border: none;
            box-shadow: 0 4px 12px rgba(0,0,0,.08);
        }
        .option-label {
            border-radius: 8px;
            transition: all .2s;
            border: 1px solid #dee2e6;
            padding: 0.75rem 1rem;
            font-size: 1rem;
        }
        .option-label:hover {
            background-color: #f8f9fa;
            transform: translateY(-2px);
            box-shadow: 0 3px 8px rgba(0,0,0,.05);
        }
        .form-check-input:checked + .option-label {
            border-color: var(--primary);
            background-color: rgba(114,9,183,.05);
        }
        .btn-primary {
            background-color: var(--primary);
            border-color: var(--primary);
        }
        .btn-outline-primary {
            color: var(--primary);
            border-color: var(--primary);
        }
        .btn-outline-primary:hover {
            background-color: var(--primary);
            color: white;
        }
        /* Bottom fixed navigation for mobile */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: white;
            box-shadow: 0 -2px 10px rgba(0,0,0,.1);
            padding: 10px 15px;
            display: flex;
            justify-content: space-between;
            z-index: 90;
        }
        @media (min-width: 992px) {
            .main-content.with-nav {
                margin-right: 280px;
            }
            .question-nav {
                transform: translateX(0);
            }
            .nav-toggle-btn {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(30px, 1fr));
            }
            .option-label {
                padding: 0.625rem 0.875rem;
                font-size: 0.95rem;
            }
            .question-text {
                font-size: 1rem;
            }
            .navbar-brand {
                font-size: 1rem;
            }
            .timer-display {
                font-size: 0.8125rem;
                padding: 0.2rem 0.6rem;
            }
            .nav-btn {
                padding: 0.2rem 0.6rem;
                font-size: 0.8125rem;
            }
            .container {
                padding-left: 10px;
                padding-right: 10px;
            }
        }
    </style>
</head>
<body>
    <!-- Header navbar -->
    <nav class="navbar navbar-dark sticky-top">
        <div class="container-fluid px-2">
            <a class="navbar-brand fw-bold" href="#"><i class="fas fa-graduation-cap me-1"></i></a>
            <div class="controls-group">
                <!-- Timer -->
                <div class="timer-display me-1 fw-bold" id="timer">
                    <i class="far fa-clock me-1"></i><span id="timer-display">90:00</span>
                </div>
                <!-- Submit button -->
                <button class="btn btn-light btn-sm nav-btn me-1" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Language toggle -->
                <button class="btn btn-light btn-sm nav-btn me-1" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="btn btn-light btn-sm nav-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </nav>
    <!-- Main content -->
    <div class="main-content" id="main-content">
        <div class="container py-3">
            <!-- Question counter -->
            <div class="fw-bold text-center mb-3" id="question-counter">Question 1 of 25</div>
            <!-- Questions will be displayed here -->
            <div id="questions-container"></div>
        </div>
    </div>
    <!-- Bottom navigation for mobile -->
    <div class="bottom-nav d-lg-none">
        <button class="btn btn-outline-primary px-3" onclick="prevQuestion()">
            <i class="fas fa-chevron-left me-1"></i> Previous
        </button>
        <button class="btn btn-outline-primary px-3" onclick="nextQuestion()">
            Next <i class="fas fa-chevron-right ms-1"></i>
        </button>
    </div>
    <!-- Question navigation sidebar -->
    <div class="question-nav" id="question-nav">
        <div class="p-3">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h5 class="m-0">Questions</h5>
                <div>
                    <span class="badge bg-secondary me-1" id="attempted-count">0</span>
                    <span class="badge bg-secondary" id="total-count">25</span>
                    <button class="btn btn-sm btn-outline-secondary ms-2 d-lg-none" onclick="toggleNav()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
            <!-- Section selector moved here -->
            <div class="mb-3">
                <label for="section-selector" class="form-label small text-muted">Jump to Section</label>
                <select class="form-select form-select-sm" id="section-selector" onchange="switchSection(this.value)">
                    <option value="all">All Sections</option>
                </select>
            </div>
            <div class="question-grid" id="question-boxes"></div>
        </div>
    </div>
    <!-- Results modal -->
    <div class="modal fade" id="results-modal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content border-0">
                <div class="modal-header border-0" style="background:linear-gradient(135deg,var(--primary),var(--secondary));color:white">
                    <h5 class="modal-title">Test Results</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body p-4">
                    <div class="row text-center g-2">
                        <div class="col-6">
                            <div class="p-3 rounded shadow-sm" style="background:linear-gradient(135deg,#4361ee,#3a0ca3);color:white">
                                <h3 id="score-value" class="mb-0 fw-bold">0</h3>
                                <div class="small">Score</div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="p-3 rounded shadow-sm" style="background:linear-gradient(135deg,#06d6a0,#1b9aaa);color:white">
                                <h3 id="correct-value" class="mb-0 fw-bold">0</h3>
                                <div class="small">Correct</div>
                            </div>
                        </div>
                        <div class="col-6 mt-2">
                            <div class="p-3 rounded shadow-sm" style="background:linear-gradient(135deg,#ef476f,#f72585);color:white">
                                <h3 id="incorrect-value" class="mb-0 fw-bold">0</h3>
                                <div class="small">Incorrect</div>
                            </div>
                        </div>
                        <div class="col-6 mt-2">
                            <div class="p-3 rounded shadow-sm" style="background:linear-gradient(135deg,#6c757d,#495057);color:white">
                                <h3 id="unattempted-value" class="mb-0 fw-bold">0</h3>
                                <div class="small">Unattempted</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer border-0">
                    <button type="button" class="btn btn-primary" onclick="reviewTest()">Review Test</button>
                </div>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 90 * 60;
        let resultsModal;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            resultsModal = new bootstrap.Modal(document.getElementById('results-modal'));
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["6"] = {
                name: "Reasoning",
                start: 0,
                end: 23
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="6">Reasoning</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 24,
                end: 24
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "6",
                    question_en: "<p><span style=\"font-family: Times New Roman;\">1. Select the number from among the given options that can replace the question mark (?) in the following series. </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">10, 13, 19, 31, ?, 103 </span></p>\n",
                    question_hi: "<p><span style=\"font-family: Baloo;\">1. &#2342;&#2367;&#2319; &#2327;&#2319; &#2357;&#2367;&#2325;&#2354;&#2381;&#2346;&#2379;&#2306; &#2350;&#2375;&#2306; &#2360;&#2375; &#2357;&#2361; &#2360;&#2306;&#2326;&#2381;&#2351;&#2366; &#2330;&#2369;&#2344;&#2367;&#2319; &#2332;&#2379; &#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340; &#2358;&#2381;&#2352;&#2371;&#2306;&#2326;&#2354;&#2366; &#2350;&#2375;&#2306; &#2346;&#2381;&#2352;&#2358;&#2381;&#2344;&#2357;&#2366;&#2330;&#2325; &#2330;&#2367;&#2344;&#2381;&#2361; (?) &#2325;&#2379; &#2346;&#2381;&#2352;&#2340;&#2367;&#2360;&#2381;&#2341;&#2366;&#2346;&#2367;&#2340; &#2325;&#2352; &#2360;&#2325;&#2375;&#2404; </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">10, 13, 19, 31, ?, 103 </span></p>\n",
                    options_en: ["<p>73</p>\n", "<p>61</p>\n", 
                                "<p>49</p>\n", "<p>55</p>\n"],
                    options_hi: ["<p>73</p>\n", "<p>61</p>\n",
                                "<p>49</p>\n", "<p>55</p>\n"],
                    solution_en: "<p>1.<span style=\"font-family: Times New Roman;\">(d)</span></p>\r\n<p><span style=\"font-family: Times New Roman;\"><strong>Logic:-</strong> Difference is double of previous difference. i.e. 3, 6, 12, 24&hellip;. And so on.</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">31 + 24 = 55 and 55 + 48 = 103.</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Roboto;\">1.</span><span style=\"font-family: Baloo;\">(d) </span></p>\r\n<p><span style=\"font-family: Baloo;\"><strong>Logic:-</strong> &#2309;&#2306;&#2340;&#2352; &#2346;&#2367;&#2331;&#2354;&#2375; &#2309;&#2306;&#2340;&#2352; &#2325;&#2366; &#2342;&#2379;&#2327;&#2369;&#2344;&#2366; &#2361;&#2376; . &#2313;&#2342;&#2366;&#2361;&#2352;&#2339;- 3, 6, 12, 24&hellip;. &#2324;&#2352; &#2311;&#2360;&#2368; &#2340;&#2352;&#2361;&#2404;</span></p>\r\n<p><span style=\"font-family: Baloo;\">31 + 24 = 55 &#2324;&#2352; 55 + 48 = 103.</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "6",
                    question_en: " <p>2. Two different positions of the same dice are shown. Select the number that will be at the top if “1” is at the bottom.</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646739326/word/media/image11.png\"/></p>",
                    question_hi: " <p><span style=\"font-family:Baloo\">2. एक ही पासे की दो अलग-अलग स्थितियों को दिखाया गया है। वह संख्या चुनें जो सबसे ऊपर होगी यदि \"1\" सबसे नीचे है।</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646739326/word/media/image11.png\"/></p>",
                    options_en: [" <p> 5</span></p>", " <p> 4</span></p>", 
                                " <p> 6</span></p>", " <p> 2</span></p>"],
                    options_hi: [" <p> 5</span></p>", " <p> 4</span></p>",
                                " <p> 6</span></p>", " <p> 2</span></p>"],
                    solution_en: " <p>2.</span><span style=\"font-family:Times New Roman\">(d) As only 3 is common in both the figures so moving anticlockwise from 3 we get 2 and 1 so 2 is opposite to 1.</span></p>",
                    solution_hi: " <p><span style=\"font-family:Roboto\">2.</span><span style=\"font-family:Baloo\">(d) चूँकि दोनों आकृतियों में केवल 3 ही उभयनिष्ठ है, इसलिए 3 से वामावर्त घूमने पर हमें 2 और 1 प्राप्त होता है, इसलिए 2, 1 के विपरीत है।</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "6",
                    question_en: " <p>3. 85 apples are to be distributed in a class consisting of boys and girls. If each boy gets 5 apples, then each girl will get 7 apples, and if each boy gets 4 apples, then each girl will get 9 apples. How many students are there in the class? </span></p>",
                    question_hi: " <p><span style=\"font-family:Baloo\">3. लड़कों और लड़कियों की एक कक्षा में 85 सेब बांटे जाने हैं। यदि प्रत्येक लड़के को 5 सेब मिलते हैं, तो प्रत्येक लड़की को 7 सेब मिलेंगे, और यदि प्रत्येक लड़के को 4 सेब मिलेंगे, तो प्रत्येक लड़की को 9 सेब मिलेंगे। कक्षा में कितने छात्र हैं? </span></p>",
                    options_en: [" <p> 15 </span></p>", " <p> 14 </span></p>", 
                                " <p> 17 </span></p>", " <p> 19 </span></p>"],
                    options_hi: [" <p> 15 </span></p>", " <p> 14 </span></p>",
                                " <p> 17 </span></p>", " <p> 19 </span></p>"],
                    solution_en: " <p>3.</span><span style=\"font-family:Times New Roman\">(a) Let the number of boys = x, number of girls = y.</span></p> <p><span style=\"font-family:Times New Roman\">So 5x + 7y = 85</span></p> <p><span style=\"font-family:Times New Roman\">And 4x + 9y = 85</span></p> <p><span style=\"font-family:Times New Roman\">5x + 7y = 4x + 9y </span></p> <p><span style=\"font-family:Times New Roman\">x = 2y</span></p> <p><span style=\"font-family:Times New Roman\">Putting in the equation </span></p> <p><span style=\"font-family:Times New Roman\">10y + 7y = 85</span></p> <p><span style=\"font-family:Times New Roman\">17y = 85 </span></p> <p><span style=\"font-family:Times New Roman\">y = 5  and x = 10 total student = 15</span></p>",
                    solution_hi: " <p><span style=\"font-family:Roboto\">3.</span><span style=\"font-family:Baloo\">(a) माना लड़कों की संख्या = x, लड़कियों की संख्या = y</span></p> <p><span style=\"font-family:Times New Roman\">5x + 7y = 85</span></p> <p><span style=\"font-family:Times New Roman\">4x + 9y = 85</span></p> <p><span style=\"font-family:Times New Roman\">5x + 7y = 4x + 9y </span></p> <p><span style=\"font-family:Times New Roman\">x = 2y</span></p> <p><span style=\"font-family:Baloo\">समीकरण में x का मान रखने पर, </span></p> <p><span style=\"font-family:Times New Roman\">10y + 7y = 85</span></p> <p><span style=\"font-family:Times New Roman\">17y = 85 </span></p> <p><span style=\"font-family:Times New Roman\">y = 5  and x = 10 </span></p> <p><span style=\"font-family:Baloo\">कुल छात्र = 15</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "6",
                    question_en: " <p>4. Four numbers have been given, out of which three are alike in some manner and one is different. Select the number that is different. </span></p>",
                    question_hi: " <p><span style=\"font-family:Baloo\">4. चार अंक दिए गए हैं, जिनमें से तीन किसी न किसी रूप में एक जैसे हैं और एक भिन्न है। उस संख्या का चयन करें जो भिन्न है। </span></p>",
                    options_en: [" <p> 315 </span></p>", " <p> 385 </span></p>", 
                                " <p> 59 </span></p>", " <p> 245 </span></p>"],
                    options_hi: [" <p> 315 </span></p>", " <p> 385 </span></p>",
                                " <p> 59 </span></p>", " <p> 245 </span></p>"],
                    solution_en: " <p>4.</span><span style=\"font-family:Times New Roman\">(c) Except option (c) all others are the multiples of 35 while 59 is a prime number.</span></p>",
                    solution_hi: " <p><span style=\"font-family:Roboto\">4.</span><span style=\"font-family:Baloo\">(c) विकल्प (c) को छोड़कर अन्य सभी 35 के गुणज हैं जबकि 59 एक अभाज्य संख्या है।</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "6",
                    question_en: " <p>5. Select the correct option that indicates the arrangement of the given words in a logical and meaningful order. </span></p> <p><span style=\"font-family:Times New Roman\">1. Journey </span><span style=\"font-family:Times New Roman\">       </span></p> <p><span style=\"font-family:Times New Roman\">2. Return </span></p> <p><span style=\"font-family:Times New Roman\">3. Destination  </span><span style=\"font-family:Times New Roman\">       </span></p> <p><span style=\"font-family:Times New Roman\">4. Reservation </span></p> <p><span style=\"font-family:Times New Roman\">5. Guided tour </span></p>",
                    question_hi: " <p><span style=\"font-family:Baloo\">5. उस सही विकल्प का चयन करें जो दिए गए शब्दों को तार्किक और सार्थक क्रम में व्यवस्थित करता है।</span></p> <p><span style=\"font-family:Times New Roman\">1. Journey </span><span style=\"font-family:Times New Roman\">       </span></p> <p><span style=\"font-family:Times New Roman\">2. Return </span></p> <p><span style=\"font-family:Times New Roman\">3. Destination  </span><span style=\"font-family:Times New Roman\">       </span></p> <p><span style=\"font-family:Times New Roman\">4. Reservation </span></p> <p><span style=\"font-family:Times New Roman\">5. Guided tour </span></p>",
                    options_en: [" <p> 3, 5, 4, 2, 1 </span><span style=\"font-family:Times New Roman\">     </span></p>", " <p> 3, 4, 2, 1, 5 </span></p>", 
                                " <p> 4, 5, 3, 1, 2 </span><span style=\"font-family:Times New Roman\">    </span></p>", " <p> 3, 4, 1, 5, 2 </span></p>"],
                    options_hi: [" <p> 3, 5, 4, 2, 1 </span><span style=\"font-family:Times New Roman\">     </span></p>", " <p> 3, 4, 2, 1, 5 </span></p>",
                                " <p> 4, 5, 3, 1, 2 </span><span style=\"font-family:Times New Roman\">     </span></p>", " <p> 3, 4, 1, 5, 2 </span></p>"],
                    solution_en: " <p>5.</span><span style=\"font-family:Times New Roman\">(d) Correct order is 3, 4, 1, 5, 2</span></p> <p><span style=\"font-family:Cardo\">Destination→Reservation→Journey→Guided tour→Return </span></p>",
                    solution_hi: " <p><span style=\"font-family:Roboto\">5.</span><span style=\"font-family:Baloo\">(d) सही क्रम है:- 3, 4, 1, 5, 2</span></p> <p><span style=\"font-family:Cardo\">Destination→Reservation→Journey→Guided tour→Return </span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "6",
                    question_en: " <p>6. Which two signs should be interchanged to make the following equation correct? </span></p> <p><span style=\"font-family:Times New Roman\">8 + 12 ÷ 6 × 8 – 9 = 8 </span></p>",
                    question_hi: " <p><span style=\"font-family:Baloo\">6. निम्नलिखित समीकरण को सही बनाने के लिए किन दो चिह्नों को आपस में बदला जाना चाहिए? </span></p> <p><span style=\"font-family:Times New Roman\">8 + 12 ÷ 6 × 8 – 9 = 8 </span></p>",
                    options_en: [" <p> × and ÷ </span></p>", " <p> + and × </span></p>", 
                                " <p> × and − </span></p>", " <p> ÷ and + </span></p>"],
                    options_hi: [" <p> × तथा ÷ </span></p>", " <p> + तथा × </span></p>",
                                " <p> × तथा − </span></p>", " <p> ÷ तथा + </span></p>"],
                    solution_en: " <p>6.</span><span style=\"font-family:Times New Roman\">(a) Applying hit and trial and changing the × and ÷ sign we get the equation as </span></p> <p><span style=\"font-family:Times New Roman\">8 + 12 × 6 ÷ 8 – 9 = 8</span></p> <p><span style=\"font-family:Times New Roman\">8 + 9 -9 = 8</span></p> <p><span style=\"font-family:Times New Roman\">Hence Verified</span></p>",
                    solution_hi: " <p><span style=\"font-family:Roboto\">6.</span><span style=\"font-family:Baloo\">(a) हिट और ट्रायल को लागू करने और × और ÷ चिह्न को बदलने से हमें समीकरण मिलता है:</span></p> <p><span style=\"font-family:Times New Roman\">8 + 12 × 6 ÷ 8 – 9 = 8</span></p> <p><span style=\"font-family:Times New Roman\">8 + 9 -9 = 8</span></p> <p><span style=\"font-family:Times New Roman\">8 = 8</span></p> <p><span style=\"font-family:Times New Roman\"> </span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "6",
                    question_en: "<p><span style=\"font-family: Times New Roman;\">7. The sequence of folding a piece of paper and the manner in which the folded paper has been cut is shown in the following figures. How would this paper look when unfolded?</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/images/mceu_27449944811675913429095.png\" width=\"299\" height=\"86\"></p>\n",
                    question_hi: "<p><span style=\"font-family: Baloo;\">7. &#2325;&#2366;&#2327;&#2332; &#2325;&#2375; &#2319;&#2325; &#2335;&#2369;&#2325;&#2337;&#2364;&#2375; &#2325;&#2379; &#2350;&#2379;&#2337;&#2364;&#2344;&#2375; &#2325;&#2366; &#2325;&#2381;&#2352;&#2350; &#2324;&#2352; &#2332;&#2367;&#2360; &#2340;&#2352;&#2368;&#2325;&#2375; &#2360;&#2375; &#2350;&#2369;&#2337;&#2364;&#2375; &#2361;&#2369;&#2319; &#2325;&#2366;&#2327;&#2332; &#2325;&#2379; &#2325;&#2366;&#2335;&#2366; &#2327;&#2351;&#2366; &#2361;&#2376;, &#2313;&#2360;&#2375; &#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340; &#2310;&#2325;&#2371;&#2340;&#2367;&#2351;&#2379;&#2306; &#2350;&#2375;&#2306; &#2342;&#2367;&#2326;&#2366;&#2351;&#2366; &#2327;&#2351;&#2366; &#2361;&#2376;&#2404; &#2326;&#2379;&#2354;&#2344;&#2375; &#2346;&#2352; &#2351;&#2361; &#2346;&#2375;&#2346;&#2352; &#2325;&#2376;&#2360;&#2366; &#2342;&#2367;&#2326;&#2375;&#2327;&#2366;?</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/images/mceu_57641433411675913532844.png\" width=\"301\" height=\"87\"></p>\n",
                    options_en: ["<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646739326/word/media/image10.png\"></p>\n", "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646739326/word/media/image5.png\"></p>\n", 
                                "<p><img src=\"https://ssccglpinnacle.com/images/mceu_30216541821675913464897.png\" width=\"100\" height=\"95\"></p>\n", "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646739326/word/media/image8.png\"></p>\n"],
                    options_hi: ["<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646739326/word/media/image10.png\"></p>\n", "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646739326/word/media/image5.png\"></p>\n",
                                "<p><img src=\"https://ssccglpinnacle.com/images/mceu_45004094721675913549733.png\" width=\"100\" height=\"95\"></p>\n", "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646739326/word/media/image8.png\"></p>\n"],
                    solution_en: "<p>7.<span style=\"font-family: Times New Roman;\">(c)</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/images/mceu_73163563831675913501665.png\" width=\"100\" height=\"95\"></p>\n",
                    solution_hi: "<p><span style=\"font-family: Roboto;\">7.</span><span style=\"font-family: Times New Roman;\">(c)</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/images/mceu_61240055031675913568961.png\" width=\"100\" height=\"95\"></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "6",
                    question_en: "<p>8. Four letter-clusters have been given, out of which three are alike in some manner and one is different. Select the letter-cluster that is different.</p>\n",
                    question_hi: "<p><span style=\"font-family: Baloo;\">8. &#2330;&#2366;&#2352; &#2309;&#2325;&#2381;&#2359;&#2352;-&#2360;&#2350;&#2370;&#2361; &#2342;&#2367;&#2319; &#2327;&#2319; &#2361;&#2376;&#2306;, &#2332;&#2367;&#2344;&#2350;&#2375;&#2306; &#2360;&#2375; &#2340;&#2368;&#2344; &#2325;&#2367;&#2360;&#2368; &#2344; &#2325;&#2367;&#2360;&#2368; &#2352;&#2370;&#2346; &#2350;&#2375;&#2306; &#2319;&#2325; &#2332;&#2376;&#2360;&#2375; &#2361;&#2376;&#2306; &#2324;&#2352; &#2319;&#2325; &#2349;&#2367;&#2344;&#2381;&#2344; &#2361;&#2376;&#2404; &#2313;&#2360; &#2309;&#2325;&#2381;&#2359;&#2352;-&#2360;&#2350;&#2370;&#2361; &#2325;&#2366; &#2330;&#2351;&#2344; &#2325;&#2352;&#2375;&#2306; &#2332;&#2379; &#2349;&#2367;&#2344;&#2381;&#2344; &#2361;&#2379;&#2404; </span></p>\n",
                    options_en: ["<p>MOVE <span style=\"font-family: Times New Roman;\"> </span></p>\n", "<p>TRAM</p>\n", 
                                "<p>SPIT <span style=\"font-family: Times New Roman;\"> </span></p>\n", "<p>BASK</p>\n"],
                    options_hi: ["<p>MOVE <span style=\"font-family: Times New Roman;\"> </span></p>\n", "<p>TRAM</p>\n",
                                "<p>SPIT <span style=\"font-family: Times New Roman;\"> </span></p>\n", "<p>BASK</p>\n"],
                    solution_en: "<p>8.<span style=\"font-family: Times New Roman;\">(a) <strong>Logic:-</strong> Except MOVE all others have one vowel while MOVE has 2 vowels.</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Roboto;\">8.</span><span style=\"font-family: Times New Roman;\">(a)</span><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Baloo;\"><strong>Logic:-</strong> MOVE &#2325;&#2379; &#2331;&#2379;&#2337;&#2364;&#2325;&#2352; &#2309;&#2344;&#2381;&#2351; &#2360;&#2349;&#2368; &#2350;&#2375;&#2306; &#2319;&#2325; &#2360;&#2381;&#2357;&#2352; &#2361;&#2376; &#2332;&#2348;&#2325;&#2367; MOVE &#2350;&#2375;&#2306; 2 &#2360;&#2381;&#2357;&#2352; &#2361;&#2376;&#2306;&#2404;</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "6",
                    question_en: "<p>9. In a certain code language, INTERACT is written as TNIRETCA. How will SATURDAY be written in that language?</p>\n",
                    question_hi: "<p><span style=\"font-family: Baloo;\">9. &#2319;&#2325; &#2344;&#2367;&#2358;&#2381;&#2330;&#2367;&#2340; &#2325;&#2370;&#2335; &#2349;&#2366;&#2359;&#2366; &#2350;&#2375;&#2306; INTERACT &#2325;&#2379; TNIRETCA &#2354;&#2367;&#2326;&#2366; &#2332;&#2366;&#2340;&#2366; &#2361;&#2376;&#2404; SATURDAY &#2325;&#2379; &#2313;&#2360;&#2368; &#2349;&#2366;&#2359;&#2366; &#2350;&#2375;&#2306; &#2325;&#2376;&#2360;&#2375; &#2354;&#2367;&#2326;&#2366; &#2332;&#2366;&#2319;&#2327;&#2366;?</span></p>\n",
                    options_en: ["<p>ASUTDRYA <span style=\"font-family: Times New Roman;\"> </span></p>\n", "<p>TASRUYAD</p>\n", 
                                "<p>UTASYADR <span style=\"font-family: Times New Roman;\"> </span></p>\n", "<p>TASURYDA</p>\n"],
                    options_hi: ["<p>ASUTDRYA <span style=\"font-family: Times New Roman;\"> </span></p>\n", "<p>TASRUYAD</p>\n",
                                "<p>UTASYADR <span style=\"font-family: Times New Roman;\"> </span></p>\n", "<p>TASURYDA</p>\n"],
                    solution_en: "<p>9.<span style=\"font-family: Times New Roman;\">(b) <strong>Logic:-</strong> First divide the number into three parts of 3-2-3 letters and then reverse each group</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">SATURDAY is SAT-UR-DAY is TAS-RU-YAD.</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Roboto;\">9.</span><span style=\"font-family: Times New Roman;\">(b)</span><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Baloo;\"><strong>Logic:- </strong>&#2346;&#2361;&#2354;&#2375; &#2360;&#2306;&#2326;&#2381;&#2351;&#2366; &#2325;&#2379; 3-2-3 &#2309;&#2325;&#2381;&#2359;&#2352;&#2379;&#2306; &#2325;&#2375; &#2340;&#2368;&#2344; &#2349;&#2366;&#2327;&#2379;&#2306; &#2350;&#2375;&#2306; &#2357;&#2367;&#2349;&#2366;&#2332;&#2367;&#2340; &#2325;&#2352;&#2375;&#2306; &#2324;&#2352; &#2347;&#2367;&#2352; &#2346;&#2381;&#2352;&#2340;&#2381;&#2351;&#2375;&#2325; &#2360;&#2350;&#2370;&#2361; &#2325;&#2379; &#2313;&#2354;&#2335; &#2342;&#2375;&#2306; SATURDAY = SAT-UR-DAY &#2351;&#2366;&#2344;&#2368; TAS-RU-YAD &#2361;&#2376;&#2404;</span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "6",
                    question_en: " <p>10. ‘Musician’ is related to ‘Piano’ in the same way as ‘Farmer’ is related to ‘_______’. </span></p>",
                    question_hi: " <p><span style=\"font-family:Baloo\">10. \'संगीतकार\' का संबंध \'पियानो\' से उसी प्रकार है जैसे \'किसान\' का संबंध \'____\' से है। </span></p>",
                    options_en: [" <p> Field </span></p>", " <p> Plough </span></p>", 
                                " <p> Bullock </span></p>", " <p> Cultivation</span></p>"],
                    options_hi: [" <p> फील्ड</span></p>", " <p> हल</span></p>",
                                " <p> बैल</span></p>", " <p> खेती</span></p>"],
                    solution_en: " <p>10.</span><span style=\"font-family:Times New Roman\">(b) Piano is one of the instruments played by the musician similarly plough is one of the tools used by the farmers.</span></p>",
                    solution_hi: " <p><span style=\"font-family:Roboto\">10.</span><span style=\"font-family:Times New Roman\">(b)</span><span style=\"font-family:Times New Roman\"> </span><span style=\"font-family:Baloo\">पियानो संगीतकार द्वारा बजाए जाने वाले उपकरणों में से एक है, वैसे ही हल किसानों द्वारा उपयोग किए जाने वाले उपकरणों में से एक है।</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "6",
                    question_en: "<p>11. Select the option that is embedded in the given figure (rotation is NOT allowed).</p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646739326/word/media/image2.png\" width=\"129\" height=\"83\"></p>\n",
                    question_hi: "<p><span style=\"font-family: Baloo;\">11. &#2313;&#2360; &#2357;&#2367;&#2325;&#2354;&#2381;&#2346; &#2325;&#2366; &#2330;&#2351;&#2344; &#2325;&#2352;&#2375;&#2306; &#2332;&#2379; &#2342;&#2368; &#2327;&#2312; &#2310;&#2325;&#2371;&#2340;&#2367; &#2350;&#2375;&#2306; &#2360;&#2344;&#2381;&#2344;&#2367;&#2361;&#2367;&#2340; &#2361;&#2376; (&#2352;&#2379;&#2335;&#2375;&#2358;&#2344; &#2325;&#2368; &#2309;&#2344;&#2369;&#2350;&#2340;&#2367; &#2344;&#2361;&#2368;&#2306; &#2361;&#2376;)&#2404;</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646739326/word/media/image2.png\" width=\"131\" height=\"84\"></p>\n",
                    options_en: ["<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646739326/word/media/image16.png\"></p>\n", "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646739326/word/media/image25.png\"></p>\n", 
                                "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646739326/word/media/image20.png\" width=\"130\" height=\"72\"></p>\n", "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646739326/word/media/image7.png\"></p>\n"],
                    options_hi: ["<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646739326/word/media/image16.png\"></p>\n", "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646739326/word/media/image25.png\"></p>\n",
                                "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646739326/word/media/image20.png\" width=\"131\" height=\"73\"></p>\r\n<p><span style=\"font-family: Times New Roman;\"> </span></p>\n", "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646739326/word/media/image7.png\"></p>\n"],
                    solution_en: "<p>11.<span style=\"font-family: Times New Roman;\">(c) </span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646739326/word/media/image31.png\" width=\"130\" height=\"79\"></p>\n",
                    solution_hi: "<p><span style=\"font-family: Roboto;\">11.</span><span style=\"font-family: Times New Roman;\">(c)</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646739326/word/media/image31.png\" width=\"131\" height=\"80\"></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "6",
                    question_en: " <p>12. Select the correct mirror image of the given figure when the mirror is placed at the right side of the figure.</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646739326/word/media/image12.png\"/></p>",
                    question_hi: " <p><span style=\"font-family:Baloo\">12. दी गई आकृति की सही दर्पण छवि का चयन करें जब दर्पण को आकृति के दाईं ओर रखा जाए।</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646739326/word/media/image12.png\"/></p>",
                    options_en: [" <p> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646739326/word/media/image15.png\"/></p>", " <p> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646739326/word/media/image26.png\"/></p>", 
                                " <p> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646739326/word/media/image24.png\"/></p>", " <p> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646739326/word/media/image1.png\"/></p>"],
                    options_hi: [" <p> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646739326/word/media/image15.png\"/></p>", " <p> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646739326/word/media/image26.png\"/></p>",
                                " <p> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646739326/word/media/image24.png\"/></p>", " <p> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646739326/word/media/image1.png\"/></p>"],
                    solution_en: " <p>12.</span><span style=\"font-family:Times New Roman\">(c)</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646739326/word/media/image9.png\"/></p>",
                    solution_hi: " <p><span style=\"font-family:Roboto\">12.</span><span style=\"font-family:Times New Roman\">(c)</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646739326/word/media/image9.png\"/></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "6",
                    question_en: "<p>13. Select the option in which the numbers are related in the same way as are the numbers of the following set.</p>\r\n<p><span style=\"font-family: Times New Roman;\">{8, 17, 15} </span></p>\n",
                    question_hi: "<p><span style=\"font-family: Baloo;\">13. &#2313;&#2360; &#2357;&#2367;&#2325;&#2354;&#2381;&#2346; &#2325;&#2366; &#2330;&#2351;&#2344; &#2325;&#2352;&#2375;&#2306; &#2332;&#2367;&#2360;&#2350;&#2375;&#2306; &#2360;&#2306;&#2326;&#2381;&#2351;&#2366;&#2319;&#2305; &#2313;&#2360;&#2368; &#2346;&#2381;&#2352;&#2325;&#2366;&#2352; &#2360;&#2306;&#2348;&#2306;&#2343;&#2367;&#2340; &#2361;&#2376;&#2306; &#2332;&#2376;&#2360;&#2375; &#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340; &#2360;&#2375;&#2335; &#2325;&#2368; &#2360;&#2306;&#2326;&#2381;&#2351;&#2366;&#2319;&#2305; &#2361;&#2376;&#2306;&#2404; </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">{8, 17, 15} </span></p>\n",
                    options_en: ["<p>{7, 25, 24} <span style=\"font-family: Times New Roman;\"> </span></p>\n", "<p>{6, 10, 9}</p>\n", 
                                "<p>{9, 15, 11} <span style=\"font-family: Times New Roman;\"> </span></p>\n", "<p>{3, 8, 5}</p>\n"],
                    options_hi: ["<p>{7, 25, 24} <span style=\"font-family: Times New Roman;\"> </span></p>\n", "<p>{6, 10, 9}</p>\n",
                                "<p>{9, 15, 11} <span style=\"font-family: Times New Roman;\"> </span></p>\n", "<p>{3, 8, 5}</p>\n"],
                    solution_en: "<p>13.<span style=\"font-family: Times New Roman;\">(a) <strong>Logic:-</strong> pythagorean triplet</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Both 8, 15, 17 and 7, 24, 25 is a pythagorean triplet.</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Roboto;\">13.</span><span style=\"font-family: Times New Roman;\">(a)</span><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Baloo;\"><strong>Logic:-</strong> &#2346;&#2366;&#2351;&#2341;&#2366;&#2327;&#2377;&#2352;&#2367;&#2351;&#2344; &#2335;&#2381;&#2352;&#2367;&#2346;&#2354;&#2375;&#2335; &#2361;&#2376;&#2404; </span></p>\r\n<p><span style=\"font-family: Baloo;\">8, 15, 17 &#2324;&#2352; 7, 24, 25 &#2342;&#2379;&#2344;&#2379;&#2306; &#2319;&#2325; &#2346;&#2366;&#2311;&#2341;&#2366;&#2327;&#2379;&#2352;&#2360; &#2340;&#2381;&#2352;&#2367;&#2325; &#2361;&#2376;&#2404;</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "6",
                    question_en: " <p>14. In the given diagram the circle stands for ‘farmers’, the rectangle stands for ‘’rural’, and the triangle stands for ‘rich’. The number given in the different segments represents the number of persons in that category.</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646739326/word/media/image29.png\"/></p> <p><span style=\"font-family:Times New Roman\">How many rural people are either farmers or rich but NOT both.</span></p>",
                    question_hi: " <p><span style=\"font-family:Baloo\">14. दिए गए आरेख में वृत्त \'किसान\' के लिए है, आयत \'ग्रामीण\' के लिए है, और त्रिभुज \'अमीर\' के लिए है। विभिन्न खंडों में दी गई संख्या उस श्रेणी के व्यक्तियों की संख्या को दर्शाती है।</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646739326/word/media/image29.png\"/></p> <p><span style=\"font-family:Baloo\">कितने ग्रामीण लोग या तो किसान हैं या अमीर लेकिन दोनों नहीं?</span></p>",
                    options_en: [" <p> 25</span></p>", " <p> 47</span></p>", 
                                " <p> 28</span></p>", " <p> 36</span></p>"],
                    options_hi: [" <p> 25</span></p>", " <p> 47</span></p>",
                                " <p> 28</span></p>", " <p> 36</span></p>"],
                    solution_en: " <p>14.</span><span style=\"font-family:Times New Roman\">(d) Rural people who are farmer =  22 </span></p> <p><span style=\"font-family:Times New Roman\">Rural people who are rich = 14</span></p> <p><span style=\"font-family:Times New Roman\">Total rural people who are rither farmer or rich : 22 + 14 = 36 </span></p>",
                    solution_hi: " <p><span style=\"font-family:Roboto\">14.</span><span style=\"font-family:Baloo\">(d) ग्रामीण लोग जो किसान हैं = 22</span></p> <p><span style=\"font-family:Baloo\">ग्रामीण लोग जो अमीर हैं = 14</span></p> <p><span style=\"font-family:Baloo\">कुल ग्रामीण लोग जो किसान या अमीर हैं : 22 +14 = 36</span></p> <p><span style=\"font-family:Times New Roman\"> </span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "6",
                    question_en: "<p>15. Vineet is the husband of Antima. Dev is the brother of Kusha. Shalini is the mother of Kusha\'s mother. Dev is the son of Vineet. How is Antima related to Shalini?</p>\n",
                    question_hi: "<p><span style=\"font-family: Baloo;\">15. &#2357;&#2367;&#2344;&#2368;&#2340; &#2309;&#2306;&#2340;&#2367;&#2350;&#2366; &#2325;&#2366; &#2346;&#2340;&#2367; &#2361;&#2376;&#2404; &#2342;&#2375;&#2357; &#2325;&#2369;&#2358; &#2325;&#2366; &#2349;&#2366;&#2312; &#2361;&#2376;&#2404; &#2358;&#2366;&#2354;&#2367;&#2344;&#2368; &#2325;&#2369;&#2358; &#2325;&#2368; &#2350;&#2366;&#2340;&#2366; &#2325;&#2368; &#2350;&#2366;&#2340;&#2366; &#2361;&#2376;&#2404; &#2342;&#2375;&#2357;, &#2357;&#2367;&#2344;&#2368;&#2340; &#2325;&#2366; &#2346;&#2369;&#2340;&#2381;&#2352; &#2361;&#2376;&#2404; &#2309;&#2306;&#2340;&#2367;&#2350;&#2366; &#2358;&#2366;&#2354;&#2367;&#2344;&#2368; &#2360;&#2375; &#2325;&#2367;&#2360; &#2346;&#2381;&#2352;&#2325;&#2366;&#2352; &#2360;&#2306;&#2348;&#2306;&#2343;&#2367;&#2340; &#2361;&#2376;?</span></p>\n",
                    options_en: ["<p>Maternal aunt <span style=\"font-family: Times New Roman;\"> </span></p>\n", "<p>Daughter</p>\n", 
                                "<p>Mother <span style=\"font-family: Times New Roman;\"> </span></p>\n", "<p>Sister</p>\n"],
                    options_hi: ["<p>&#2350;&#2380;&#2360;&#2368;</p>\n", "<p>&#2348;&#2375;&#2335;&#2368;</p>\n",
                                "<p>&#2350;&#2366;&#2306;</p>\n", "<p>&#2348;&#2361;&#2344;</p>\n"],
                    solution_en: "<p>15.<span style=\"font-family: Times New Roman;\">(b) &lsquo;</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">According to the following family diagram, Antima is the daughter of Shalini.</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646739326/word/media/image28.png\"></p>\n",
                    solution_hi: "<p><span style=\"font-family: Roboto;\">15.</span><span style=\"font-family: Times New Roman;\">(b) &lsquo;</span><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Baloo;\">&#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340; &#2346;&#2352;&#2367;&#2357;&#2366;&#2352; &#2310;&#2352;&#2375;&#2326; &#2325;&#2375; &#2309;&#2344;&#2369;&#2360;&#2366;&#2352;, &#2309;&#2306;&#2340;&#2367;&#2350;&#2366; &#2358;&#2366;&#2354;&#2367;&#2344;&#2368; &#2325;&#2368; &#2346;&#2369;&#2340;&#2381;&#2352;&#2368; &#2361;&#2376;&#2404;</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646739326/word/media/image28.png\"></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "6",
                    question_en: "<p>16. How many Triangles are there in the following figure?</p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646739326/word/media/image3.png\"></p>\n",
                    question_hi: "<p><span style=\"font-family: Baloo;\">16. &#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340; &#2310;&#2325;&#2371;&#2340;&#2367; &#2350;&#2375;&#2306; &#2325;&#2367;&#2340;&#2344;&#2375; &#2340;&#2381;&#2352;&#2367;&#2349;&#2369;&#2332; &#2361;&#2376;&#2306;?</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646739326/word/media/image3.png\"></p>\n",
                    options_en: ["<p>20</p>\n", "<p>31</p>\n", 
                                "<p>24</p>\n", "<p>28</p>\n"],
                    options_hi: ["<p>20</p>\n", "<p>31</p>\n",
                                "<p>24</p>\n", "<p>28</p>\n"],
                    solution_en: "<p>16.<span style=\"font-family: Times New Roman;\">(b)</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646739326/word/media/image13.png\"></p>\r\n<p><span style=\"font-family: Times New Roman;\">There are 14 individual triangles numbered from 1 to 14. Other 17 are the combination of (3 &amp; 5), (2 &amp; 4), (1, 2 &amp; 4), (2, 4 &amp; 6), (3, 5 &amp; 1), (3, 5 &amp; 6), (8 &amp; 7), (8 &amp; 10), (9 &amp; 7), (9 &amp; 10), (3, 5, 6, 7 &amp; 8), (2, 4, 6, 7 &amp; 9), (11 &amp; 12), (11 &amp; 14), (13 &amp; 12) , (13 &amp; 14) and (12,11 &amp; 4)</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Roboto;\">16.</span><span style=\"font-family: Times New Roman;\">(b)</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646739326/word/media/image13.png\"></p>\r\n<p><span style=\"font-family: Baloo;\">1 &#2360;&#2375; 14 &#2340;&#2325; &#2327;&#2367;&#2344;&#2375; &#2327;&#2319; 14 &#2357;&#2381;&#2351;&#2325;&#2381;&#2340;&#2367;&#2327;&#2340; &#2340;&#2381;&#2352;&#2367;&#2349;&#2369;&#2332; &#2361;&#2376;&#2306;&#2404; &#2309;&#2344;&#2381;&#2351; 17 - (3 &amp; 5), (2 &amp; 4), (1, 2 &amp; 4), (2, 4 &amp; 6), (3, 5 &amp; 1), (3, 5 &amp; 6), (8 &amp; 7), (8 &amp; 10), (9 &amp; 7), (9 &amp; 10), (3, 5, 6, 7 &amp; 8), (2, 4, 6, 7 &amp; 9), (11 &amp; 12), (11 &amp; 14), (13 &amp; 12) , (13 &amp; 14) &#2340;&#2341;&#2366; (4,11 &amp; 12) &#2325;&#2366; &#2360;&#2306;&#2351;&#2379;&#2332;&#2344; &#2361;&#2376;&#2404; </span></p>\r\n<p><span style=\"font-family: Times New Roman;\"> </span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "6",
                    question_en: " <p>17. Select the option in which the words share the same relationship as that shared by the given pair of words. </span></p> <p><span style=\"font-family:Times New Roman\">Trust : Faith </span></p>",
                    question_hi: " <p><span style=\"font-family:Baloo\">17. उस विकल्प का चयन करें जिसमें शब्द वही संबंध साझा करते हैं जो दिए गए शब्दों के जोड़े द्वारा साझा किए गए हैं।</span></p> <p><span style=\"font-family:Times New Roman\">Trust : Faith </span></p>",
                    options_en: [" <p> Disgust : Distaste </span></p>", " <p> Fair : Wholesome </span></p>", 
                                " <p> Salary : Employment </span></p>", " <p> Joy : Success </span></p>"],
                    options_hi: [" <p> Disgust : Distaste </span></p>", " <p> Fair : Wholesome </span></p>",
                                " <p> Salary : Employment </span></p>", " <p> Joy : Success </span></p>"],
                    solution_en: " <p>17.</span><span style=\"font-family:Times New Roman\">(a) As trust and Faith both are synonyms of each other similarly Disgust and Distaste are synonyms of each other.</span></p>",
                    solution_hi: " <p>17.</span><span style=\"font-family:Times New Roman\">(a)</span><span style=\"font-family:Times New Roman\"> </span><span style=\"font-family:Baloo\">जिस प्रकार trust और Faith दोनों एक दूसरे के पर्यायवाची हैं उसी प्रकार Disgust और Distaste एक दूसरे के पर्यायवाची हैं।</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "6",
                    question_en: "<p>18. Select the letter-cluster that can replace the question mark (?) in the following series.</p>\r\n<p><span style=\"font-family: Times New Roman;\">REST, SEST, SFST, ? , SFTU </span></p>",
                    question_hi: "<p><span style=\"font-family: Baloo;\">18. उस अक्षर-समूह का चयन करें जो निम्नलिखित श्रृंखला में प्रश्न चिह्न (?) को प्रतिस्थापित कर सकता है।</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">REST, SEST, SFST, ? , SFTU </span></p>",
                    options_en: ["<p>SFRT</p>", "<p>SGTT</p>", 
                                "<p>SFUT</p>", "<p>SFTT</p>"],
                    options_hi: ["<p>SFRT</p>", "<p>SGTT</p>",
                                "<p>SFUT</p>", "<p>SFTT</p>"],
                    solution_en: "<p>18.<span style=\"font-family: Times New Roman;\">(d)</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">R E S T </span></p>\r\n<p><span style=\"font-family: Cardo;\">&darr;(+1)</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">S E S T</span></p>\r\n<p><span style=\"font-family: Cardo;\">&nbsp; &nbsp; &darr;(+1)</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">S F S T</span></p>\r\n<p><span style=\"font-family: Cardo;\">&nbsp; &nbsp; &nbsp; &nbsp;&darr;(+1)</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">S F T T</span></p>\r\n<p><span style=\"font-family: Cardo;\">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&darr;(+1)</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">S F T U</span></p>",
                    solution_hi: "<p><span style=\"font-family: Roboto;\">18.</span><span style=\"font-family: Times New Roman;\">(d)</span><span style=\"font-family: Times New Roman;\"> </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">R E S T </span></p>\r\n<p><span style=\"font-family: Cardo;\">&darr;(+1)</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">S E S T</span></p>\r\n<p><span style=\"font-family: Cardo;\">&nbsp; &nbsp; &darr;(+1)</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">S F S T</span></p>\r\n<p><span style=\"font-family: Cardo;\">&nbsp; &nbsp; &nbsp; &darr;(+1)</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">S F T T</span></p>\r\n<p><span style=\"font-family: Cardo;\">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&darr;(+1)</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">S F T U</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "6",
                    question_en: "<p>19. Select the option in which the numbers share the same relationship as that shared by the given pair of numbers.</p>\r\n<p><span style=\"font-family: Times New Roman;\">6 : 54 </span></p>\n",
                    question_hi: "<p><span style=\"font-family: Baloo;\">19. &#2313;&#2360; &#2357;&#2367;&#2325;&#2354;&#2381;&#2346; &#2325;&#2366; &#2330;&#2351;&#2344; &#2325;&#2352;&#2375;&#2306; &#2332;&#2367;&#2360;&#2350;&#2375;&#2306; &#2360;&#2306;&#2326;&#2381;&#2351;&#2366;&#2319;&#2306; &#2357;&#2361;&#2368; &#2360;&#2306;&#2348;&#2306;&#2343; &#2360;&#2366;&#2333;&#2366; &#2325;&#2352;&#2340;&#2368; &#2361;&#2376;&#2306; &#2332;&#2379; &#2342;&#2367;&#2319; &#2327;&#2319; &#2360;&#2306;&#2326;&#2381;&#2351;&#2366;&#2323;&#2306; &#2325;&#2375; &#2351;&#2369;&#2327;&#2381;&#2350; &#2342;&#2381;&#2357;&#2366;&#2352;&#2366; &#2360;&#2366;&#2333;&#2366; &#2325;&#2367;&#2351;&#2366; &#2332;&#2366;&#2340;&#2366; &#2361;&#2376;&#2404; </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">6 : 54 </span></p>\n",
                    options_en: ["<p>2 : 9</p>\n", "<p>4 : 16</p>\n", 
                                "<p>10 : 108</p>\n", "<p>8 : 76</p>\n"],
                    options_hi: ["<p>2 : 9</p>\n", "<p>4 : 16</p>\n",
                                "<p>10 : 108</p>\n", "<p>8 : 76</p>\n"],
                    solution_en: "<p>19.<span style=\"font-family: Times New Roman;\">(b) <strong>Logic:-</strong> the second number is completely divisible by the first number.</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">In option b 16 is completely divisible by 4 so it is the correct option.</span></p>\n",
                    solution_hi: "<p>19.(b) <span style=\"font-family: Baloo;\"><strong>Logic:-</strong> &#2342;&#2370;&#2360;&#2352;&#2368; &#2360;&#2306;&#2326;&#2381;&#2351;&#2366; &#2346;&#2361;&#2354;&#2368; &#2360;&#2306;&#2326;&#2381;&#2351;&#2366; &#2360;&#2375; &#2346;&#2370;&#2352;&#2381;&#2339;&#2340;&#2307; &#2357;&#2367;&#2349;&#2366;&#2332;&#2367;&#2340; &#2361;&#2379;&#2340;&#2368; &#2361;&#2376;&#2404;</span></p>\r\n<p><span style=\"font-family: Baloo;\">&#2357;&#2367;&#2325;&#2354;&#2381;&#2346; b &#2350;&#2375;&#2306; 16, 4 &#2360;&#2375; &#2346;&#2370;&#2352;&#2381;&#2339; &#2352;&#2370;&#2346; &#2360;&#2375; &#2357;&#2367;&#2349;&#2366;&#2332;&#2381;&#2351; &#2361;&#2376; &#2309;&#2340;&#2307; &#2351;&#2361; &#2360;&#2361;&#2368; &#2357;&#2367;&#2325;&#2354;&#2381;&#2346; &#2361;&#2376;&#2404;</span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "6",
                    question_en: "<p>20. In a certain code language, SEMANTIC is coded as 14485293.How will PRACTICE be coded in that language?</p>\n",
                    question_hi: "<p><span style=\"font-family: Baloo;\">20. &#2319;&#2325; &#2344;&#2367;&#2358;&#2381;&#2330;&#2367;&#2340; &#2325;&#2379;&#2337; &#2349;&#2366;&#2359;&#2366; &#2350;&#2375;&#2306;, SEMANTIC &#2325;&#2379; 14485293 &#2325;&#2375; &#2352;&#2370;&#2346; &#2350;&#2375;&#2306; &#2325;&#2379;&#2337;&#2367;&#2340; &#2325;&#2367;&#2351;&#2366; &#2332;&#2366;&#2340;&#2366; &#2361;&#2376;&#2404; &#2313;&#2360; &#2349;&#2366;&#2359;&#2366; &#2350;&#2375;&#2306; PRACTICE &#2325;&#2379; &#2325;&#2376;&#2360;&#2375; &#2325;&#2379;&#2337;&#2367;&#2340; &#2325;&#2367;&#2351;&#2366; &#2332;&#2366;&#2319;&#2327;&#2366;?</span></p>\n",
                    options_en: ["<p>29137934 <span style=\"font-family: Times New Roman;\"> </span></p>\n", "<p>79832934</p>\n", 
                                "<p>79132935 <span style=\"font-family: Times New Roman;\"> </span></p>\n", "<p>81943145</p>\n"],
                    options_hi: ["<p>29137934 <span style=\"font-family: Times New Roman;\"> </span></p>\n", "<p>79832934</p>\n",
                                "<p>79132935 <span style=\"font-family: Times New Roman;\"> </span></p>\n", "<p>81943145</p>\n"],
                    solution_en: "<p>20.<span style=\"font-family: Times New Roman;\">(b)</span></p>\r\n<p><span style=\"font-family: Times New Roman;\"><strong>Logic:-</strong> Sum of the alphabetical positioning of the consonant and the reverse alphabetical positioning of the vowels.</span></p>\r\n<p><span style=\"font-family: Cardo;\">So, PRACTICE &rarr; 16-18-26-3-20-18-3-22</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">On the sum of their digits 7-9-8-3-2-9-3-4</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Roboto;\">20.</span><span style=\"font-family: Times New Roman;\">(b)</span><span style=\"font-family: Times New Roman;\"> </span></p>\r\n<p><span style=\"font-family: Baloo;\"><strong>Logic:- </strong>&#2357;&#2381;&#2351;&#2306;&#2332;&#2344; &#2325;&#2368; &#2357;&#2352;&#2381;&#2339;&#2366;&#2344;&#2369;&#2325;&#2381;&#2352;&#2350;&#2367;&#2325; &#2360;&#2381;&#2341;&#2367;&#2340;&#2367; &#2324;&#2352; &#2360;&#2381;&#2357;&#2352;&#2379;&#2306; &#2325;&#2368; &#2357;&#2367;&#2346;&#2352;&#2368;&#2340; &#2357;&#2352;&#2381;&#2339;&#2366;&#2344;&#2369;&#2325;&#2381;&#2352;&#2350; &#2360;&#2381;&#2341;&#2367;&#2340;&#2367; &#2325;&#2366; &#2351;&#2379;&#2327;&#2404;</span></p>\r\n<p><span style=\"font-family: Arial Unicode MS;\">&#2340;&#2379;, PRACTICE &rarr; </span><span style=\"font-family: Times New Roman;\">16- 18-26-3-20-18-3-22</span></p>\r\n<p><span style=\"font-family: Baloo;\">&#2313;&#2344;&#2325;&#2375; &#2309;&#2306;&#2325;&#2379;&#2306; &#2325;&#2375; &#2351;&#2379;&#2327; &#2346;&#2352; 7-9-8-3-2-9-3-4</span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "6",
                    question_en: "<p>21. Select the option that is related to the third letter-cluster in the same way as the second letter-cluster is related to the first letter-cluster.</p>\r\n<p><span style=\"font-family: Times New Roman;\">DJHF : JPNL :: KTQN : ? </span></p>\n",
                    question_hi: "<p><span style=\"font-family: Baloo;\">21. &#2313;&#2360; &#2357;&#2367;&#2325;&#2354;&#2381;&#2346; &#2325;&#2366; &#2330;&#2351;&#2344; &#2325;&#2352;&#2375;&#2306; &#2332;&#2379; &#2340;&#2368;&#2360;&#2352;&#2375; &#2309;&#2325;&#2381;&#2359;&#2352;-&#2360;&#2350;&#2370;&#2361; &#2360;&#2375; &#2313;&#2360;&#2368; &#2346;&#2381;&#2352;&#2325;&#2366;&#2352; &#2360;&#2306;&#2348;&#2306;&#2343;&#2367;&#2340; &#2361;&#2376; &#2332;&#2376;&#2360;&#2375; &#2342;&#2370;&#2360;&#2352;&#2366; &#2309;&#2325;&#2381;&#2359;&#2352;-&#2360;&#2350;&#2370;&#2361; &#2346;&#2361;&#2354;&#2375; &#2309;&#2325;&#2381;&#2359;&#2352;-&#2360;&#2350;&#2370;&#2361; &#2360;&#2375; &#2360;&#2306;&#2348;&#2306;&#2343;&#2367;&#2340; &#2361;&#2376;&#2404; </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">DJHF : JPNL :: KTQN : ?</span></p>\n",
                    options_en: ["<p>PZWS</p>\n", "<p>QWUS</p>\n", 
                                "<p>QZWT</p>\n", "<p>QHKN</p>\n"],
                    options_hi: ["<p>PZWS</p>\n", "<p>QWUS</p>\n",
                                "<p>QZWT</p>\n", "<p>QHKN</p>\n"],
                    solution_en: "<p>21.<span style=\"font-family: Times New Roman;\">(c)</span></p>\r\n<p><span style=\"font-family: Times New Roman;\"><strong>Logic:-</strong> +6 to the corresponding letters </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">So K +6 = Q, T +6 = Z,</span></p>\r\n<p><span style=\"font-family: Times New Roman;\"> Q +6 = W and N +6 = T</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">So, </span><span style=\"font-family: Times New Roman;\">QZWT</span><span style=\"font-family: Times New Roman;\"> is the correct </span><span style=\"font-family: Times New Roman;\">answer.</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Roboto;\">21.</span><span style=\"font-family: Times New Roman;\">(c)</span><span style=\"font-family: Times New Roman;\"> </span></p>\r\n<p><span style=\"font-family: Baloo;\"><strong>Logic:-</strong> &#2360;&#2306;&#2348;&#2306;&#2343;&#2367;&#2340; &#2309;&#2325;&#2381;&#2359;&#2352;&#2379;&#2306; &#2325;&#2379; +6 </span></p>\r\n<p><span style=\"font-family: Baloo;\">&#2340;&#2379; K +6 = Q, T +6 = Z, </span></p>\r\n<p><span style=\"font-family: Baloo;\">Q +6 = W &#2324;&#2352; N +6 = T</span></p>\r\n<p><span style=\"font-family: Baloo;\">&#2340;&#2379;, QZWT &#2360;&#2361;&#2368; &#2313;&#2340;&#2381;&#2340;&#2352; &#2361;&#2376;&#2404;</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "6",
                    question_en: " <p>22. Four words have been given, out of which three are alike in some manner and one is different. Select the word that is different. </span></p>",
                    question_hi: " <p><span style=\"font-family:Baloo\">22. चार शब्द दिए गए हैं, जिनमें से तीन किसी तरह से एक जैसे हैं और एक अलग है। उस शब्द का चयन करें जो भिन्न है।</span></p>",
                    options_en: [" <p> Progression </span><span style=\"font-family:Times New Roman\">       </span></p>", " <p> Valediction </span></p>", 
                                " <p> Termination </span><span style=\"font-family:Times New Roman\">       </span></p>", " <p> Conclusion </span></p>"],
                    options_hi: [" <p> Progression </span><span style=\"font-family:Times New Roman\">      </span></p>", " <p> Valediction </span></p>",
                                " <p> Termination </span><span style=\"font-family:Times New Roman\">      </span></p>", " <p> Conclusion </span></p>"],
                    solution_en: " <p>22.</span><span style=\"font-family:Times New Roman\">(a) Except for progression, all others are at the end of any session or program.</span></p>",
                    solution_hi: " <p><span style=\"font-family:Roboto\">22.</span><span style=\"font-family:Times New Roman\">(a)</span><span style=\"font-family:Times New Roman\"> </span><span style=\"font-family:Baloo\">Progression को छोड़कर, अन्य सभी किसी सत्र या कार्यक्रम के अंत में हैं।</span><span style=\"font-family:Times New Roman\"> </span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "23",
                    section: "6",
                    question_en: "<p>23. Read the given statements and conclusions carefully. Assuming that the information given in the statements is true, even if it appears to be at variance with commonly known facts, decide which of the given conclusions logically follow(s) from the statements.</p>\r\n<p><strong><span style=\"font-family: Times New Roman;\">Statements: </span></strong></p>\r\n<p><span style=\"font-family: Times New Roman;\">Some vehicles are cars. </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">All cars are four-wheelers. </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Some four-wheelers are trucks.</span></p>\r\n<p><strong><span style=\"font-family: Times New Roman;\">Conclusions: </span></strong></p>\r\n<p><span style=\"font-family: Times New Roman;\">I. Some vehicles are trucks. </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">II. Some four-wheelers are vehicles. </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">III. Some trucks are cars. </span></p>\n",
                    question_hi: "<p><span style=\"font-family: Baloo;\">23. &#2342;&#2367;&#2319; &#2327;&#2319; &#2325;&#2341;&#2344;&#2379;&#2306; &#2324;&#2352; &#2344;&#2367;&#2359;&#2381;&#2325;&#2352;&#2381;&#2359;&#2379;&#2306; &#2325;&#2379; &#2343;&#2381;&#2351;&#2366;&#2344;&#2346;&#2370;&#2352;&#2381;&#2357;&#2325; &#2346;&#2338;&#2364;&#2367;&#2319;&#2404; &#2351;&#2361; &#2350;&#2366;&#2344;&#2340;&#2375; &#2361;&#2369;&#2319; &#2325;&#2367; &#2325;&#2341;&#2344;&#2379;&#2306; &#2350;&#2375;&#2306; &#2342;&#2368; &#2327;&#2312; &#2332;&#2366;&#2344;&#2325;&#2366;&#2352;&#2368; &#2360;&#2340;&#2381;&#2351; &#2361;&#2376;, &#2349;&#2354;&#2375; &#2361;&#2368; &#2357;&#2361; &#2360;&#2366;&#2350;&#2366;&#2344;&#2381;&#2351; &#2352;&#2370;&#2346; &#2360;&#2375; &#2332;&#2381;&#2334;&#2366;&#2340; &#2340;&#2341;&#2381;&#2351;&#2379;&#2306; &#2360;&#2375; &#2349;&#2367;&#2344;&#2381;&#2344; &#2346;&#2381;&#2352;&#2340;&#2368;&#2340; &#2361;&#2379;&#2340;&#2368; &#2361;&#2379;, &#2340;&#2351; &#2325;&#2352;&#2375;&#2306; &#2325;&#2367; &#2342;&#2367;&#2319; &#2327;&#2319; &#2344;&#2367;&#2359;&#2381;&#2325;&#2352;&#2381;&#2359;&#2379;&#2306; &#2350;&#2375;&#2306; &#2360;&#2375; &#2325;&#2380;&#2344; &#2360;&#2366; &#2325;&#2341;&#2344;&#2379;&#2306; &#2325;&#2366; &#2340;&#2366;&#2352;&#2381;&#2325;&#2367;&#2325; &#2352;&#2370;&#2346; &#2360;&#2375; &#2309;&#2344;&#2369;&#2360;&#2352;&#2339; &#2325;&#2352;&#2340;&#2366; &#2361;&#2376;&#2404;</span></p>\r\n<p><strong><span style=\"font-family: Baloo;\">&#2325;&#2341;&#2344;:</span></strong></p>\r\n<p><span style=\"font-family: Baloo;\">&#2325;&#2369;&#2331; &#2357;&#2366;&#2361;&#2344; &#2325;&#2366;&#2352; &#2361;&#2376;&#2306;&#2404;</span></p>\r\n<p><span style=\"font-family: Baloo;\">&#2360;&#2349;&#2368; &#2325;&#2366;&#2352;&#2375;&#2306; &#2330;&#2380;&#2346;&#2361;&#2367;&#2351;&#2366; &#2361;&#2376;&#2306;&#2404;</span></p>\r\n<p><span style=\"font-family: Baloo;\">&#2325;&#2369;&#2331; &#2330;&#2380;&#2346;&#2361;&#2367;&#2351;&#2366; &#2335;&#2381;&#2352;&#2325; &#2361;&#2376;&#2306;&#2404;</span></p>\r\n<p><strong><span style=\"font-family: Baloo;\">&#2344;&#2367;&#2359;&#2381;&#2325;&#2352;&#2381;&#2359;:</span></strong></p>\r\n<p><span style=\"font-family: Baloo;\">I. &#2325;&#2369;&#2331; &#2357;&#2366;&#2361;&#2344; &#2335;&#2381;&#2352;&#2325; &#2361;&#2376;&#2306;&#2404;</span></p>\r\n<p><span style=\"font-family: Baloo;\">II. &#2325;&#2369;&#2331; &#2330;&#2380;&#2346;&#2361;&#2367;&#2351;&#2366; &#2357;&#2366;&#2361;&#2344; &#2361;&#2376;&#2306;&#2404;</span></p>\r\n<p><span style=\"font-family: Baloo;\">III. &#2325;&#2369;&#2331; &#2335;&#2381;&#2352;&#2325; &#2325;&#2366;&#2352; &#2361;&#2376;&#2306;&#2404;</span></p>\n",
                    options_en: ["<p>None of the conclusions follow.</p>\n", "<p>Only conclusion I <span style=\"font-family: Times New Roman;\">follows</span><span style=\"font-family: Times New Roman;\">. </span></p>\n", 
                                "<p>Only conclusion II follows.</p>\n", "<p>Only conclusions II and III follow.</p>\n"],
                    options_hi: ["<p>&#2325;&#2379;&#2312; &#2349;&#2368; &#2344;&#2367;&#2359;&#2381;&#2325;&#2352;&#2381;&#2359; &#2309;&#2344;&#2369;&#2360;&#2352;&#2339; &#2344;&#2361;&#2368;&#2306; &#2325;&#2352;&#2340;&#2366; &#2361;&#2376;&#2404;</p>\n", "<p>&#2325;&#2375;&#2357;&#2354; &#2344;&#2367;&#2359;&#2381;&#2325;&#2352;&#2381;&#2359; I &#2309;&#2344;&#2369;&#2360;&#2352;&#2339; &#2325;&#2352;&#2340;&#2366; &#2361;&#2376;&#2404;</p>\n",
                                "<p>&#2325;&#2375;&#2357;&#2354; &#2344;&#2367;&#2359;&#2381;&#2325;&#2352;&#2381;&#2359; II &#2309;&#2344;&#2369;&#2360;&#2352;&#2339; &#2325;&#2352;&#2340;&#2366; &#2361;&#2376;&#2404;</p>\n", "<p>&#2325;&#2375;&#2357;&#2354; &#2344;&#2367;&#2359;&#2381;&#2325;&#2352;&#2381;&#2359; II &#2324;&#2352; III &#2309;&#2344;&#2369;&#2360;&#2352;&#2339; &#2325;&#2352;&#2340;&#2375; &#2361;&#2376;&#2306;&#2404;</p>\n"],
                    solution_en: "<p>23.<span style=\"font-family: Times New Roman;\">(c) According to the following Venn diagram, Only conclusion II follows.</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646739326/word/media/image30.png\"></p>\n",
                    solution_hi: "<p><span style=\"font-family: Roboto;\">23.</span><span style=\"font-family: Baloo;\">(c) &#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340; &#2357;&#2375;&#2344; &#2310;&#2352;&#2375;&#2326; &#2325;&#2375; &#2309;&#2344;&#2369;&#2360;&#2366;&#2352;, &#2325;&#2375;&#2357;&#2354; &#2344;&#2367;&#2359;&#2381;&#2325;&#2352;&#2381;&#2359; II &#2309;&#2344;&#2369;&#2360;&#2352;&#2339; &#2325;&#2352;&#2340;&#2366; &#2361;&#2376;&#2404;</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646739326/word/media/image30.png\"></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "24",
                    section: "6",
                    question_en: "<p>24. Select the figure that will come next in the following figure series.</p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646739326/word/media/image6.png\"></p>\n",
                    question_hi: " <p><span style=\"font-family:Baloo\">24. उस आकृति का चयन करें जो निम्नलिखित आकृति श्रृंखला में आगे आएगी। </span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646739326/word/media/image6.png\"/></p>",
                    options_en: ["<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646739326/word/media/image23.png\"></p>\n", "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646739326/word/media/image21.png\"></p>\n", 
                                "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646739326/word/media/image4.png\"></p>\n", "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646739326/word/media/image27.png\" width=\"101\" height=\"83\"></p>\n"],
                    options_hi: [" <p> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646739326/word/media/image23.png\"/></p>", " <p> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646739326/word/media/image21.png\"/></p>",
                                " <p> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646739326/word/media/image4.png\"/></p>", " <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646739326/word/media/image27.png\"/></p>"],
                    solution_en: "<p>24.<span style=\"font-family: Times New Roman;\">(a)</span></p>\r\n<p><span style=\"font-family: Times New Roman;\"><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646739326/word/media/image17.png\"></span></p>\n",
                    solution_hi: " <p>24.(a)</span><span style=\"font-family:Times New Roman\"> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646739326/word/media/image17.png\"/></p> <p><span style=\"font-family:Times New Roman\"> </span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "25",
                    section: "misc",
                    question_en: "<p>25. Select the option in which the numbers are related in the same way as are the numbers of the following set.</p>\r\n<p><span style=\"font-family: Times New Roman;\">{14, 44, 8} </span></p>\n",
                    question_hi: "<p><span style=\"font-family: Baloo;\">25. &#2313;&#2360; &#2357;&#2367;&#2325;&#2354;&#2381;&#2346; &#2325;&#2366; &#2330;&#2351;&#2344; &#2325;&#2352;&#2375;&#2306; &#2332;&#2367;&#2360;&#2350;&#2375;&#2306; &#2360;&#2306;&#2326;&#2381;&#2351;&#2366;&#2319;&#2305; &#2313;&#2360;&#2368; &#2346;&#2381;&#2352;&#2325;&#2366;&#2352; &#2360;&#2306;&#2348;&#2306;&#2343;&#2367;&#2340; &#2361;&#2376;&#2306; &#2332;&#2376;&#2360;&#2375; &#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340; &#2360;&#2375;&#2335; &#2325;&#2368; &#2360;&#2306;&#2326;&#2381;&#2351;&#2366;&#2319;&#2305; &#2361;&#2376;&#2306;&#2404;</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">{14, 44, 8} </span></p>\n",
                    options_en: ["<p>{18, 54, 13} <span style=\"font-family: Times New Roman;\"> </span></p>\n", "<p>{12, 36, 9}</p>\n", 
                                "<p>{5, 32, 11} <span style=\"font-family: Times New Roman;\"> </span></p>\n", "<p>{9, 38, 12}</p>\n"],
                    options_hi: ["<p>{18, 54, 13} <span style=\"font-family: Times New Roman;\"> </span></p>\n", "<p>{12, 36, 9}</p>\n",
                                "<p>{5, 32, 11} <span style=\"font-family: Times New Roman;\"> </span></p>\n", "<p>{9, 38, 12}</p>\n"],
                    solution_en: "<p>25.<span style=\"font-family: Times New Roman;\">(c) <strong>Logic:-</strong> {a, 2a + 2b, b}</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">10 + 22 = 32 </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">So, c is the correct option. </span></p>\n",
                    solution_hi: "<p>25.(c) <span style=\"font-family: Times New Roman;\"><strong>Logic:-</strong> {a, 2a + 2b, b}</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">10 + 22 = 32 </span></p>\r\n<p><span style=\"font-family: Baloo;\">&#2309;&#2340;: c &#2360;&#2361;&#2368; &#2357;&#2367;&#2325;&#2354;&#2381;&#2346; &#2361;&#2376;&#2404;</span><span style=\"font-family: Times New Roman;\"> </span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML - more compact
            let html = `
                <div class="card mb-3">
                    <div class="card-body">
                        <div class="question-text mb-3">
                            <div class="en">${question.question_en}</div>
                            <div class="hi" style="display:none">${question.question_hi}</div>
                        </div>
                        <div class="options">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = 'option-label';
                // When reviewing, highlight user's choice and correct answer
                if (submitted) {
                    if (opt === question.correct) {
                        optionClass += ' border-success bg-success bg-opacity-10';
                    }
                    if (answers[question.id] === opt && opt !== question.correct) {
                        optionClass += ' border-danger bg-danger bg-opacity-10';
                    }
                }
                html += `
                    <div class="form-check mb-2">
                        <input class="form-check-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${answers[question.id] === opt ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                        <label class="form-check-label w-100 ${optionClass}" for="opt-${index}-${opt}">
                            ${submitted && opt === question.correct ? '<i class="fas fa-check-circle text-success me-1"></i>' : ''}
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const alertClass = isCorrect ? 'alert-success' : 'alert-danger';
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="alert ${alertClass} mt-3 py-2">
                        <strong>${resultText}</strong>
                        <div class="mt-1 small">
                            <strong>Solution:</strong>
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                document.getElementById(`box-${index}`).classList.add('attempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { if (currentQuestion > 0) showQuestion(currentQuestion - 1); }
        function nextQuestion() { if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
            // Update button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const content = document.getElementById('main-content');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                if (window.innerWidth >= 992) {
                    content.classList.add('with-nav');
                }
            } else {
                nav.classList.remove('show');
                if (window.innerWidth >= 992) {
                    content.classList.remove('with-nav');
                }
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer - compact
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            if (confirm(`You have attempted ${attempted} of ${questions.length} questions.
${remaining} questions are unattempted.
Are you sure you want to submit?`)) {
                submitTest();
            }
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('bg-secondary', 'text-white');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            resultsModal.show();
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.classList.replace('btn-light', 'btn-success');
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            resultsModal.hide();
            showQuestion(0);
        }
        // Add event listener for window resize
        window.addEventListener('resize', function() {
            if (window.innerWidth >= 992) {
                if (document.getElementById('question-nav').classList.contains('show')) {
                    document.getElementById('main-content').classList.add('with-nav');
                }
            } else {
                document.getElementById('main-content').classList.remove('with-nav');
            }
        });
    </script>
</body>
</html>