<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">20:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 18</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">18</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 20 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 0,
                end: 16
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 17,
                end: 17
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "17",
                    question_en: "<p>1. The LCM of the two numbers is 56 times their HCF, with the sum of their HCF and LCM being 1710. If one of the two numbers is 240, then what is the other number ?</p>",
                    question_hi: "<p>1. <span style=\"font-family: Palanquin Dark;\">दो संख्याओं का LCM उनके HCF का 56 गुना है, उनके HCF और LCM का योग 1710 है। यदि दो संख्याओं में से एक 240 है, तो दूसरी संख्या क्या है ?</span></p>",
                    options_en: ["<p>57</p>", "<p>171</p>", 
                                "<p>1680</p>", "<p>210</p>"],
                    options_hi: ["<p>57</p>", "<p>171</p>",
                                "<p>1680</p>", "<p>210</p>"],
                    solution_en: "<p>1.(d) <span style=\"font-family: Palanquin Dark;\">Let LCM = a and HCF = b<br></span><span style=\"font-family: Palanquin Dark;\">According to the question,<br></span><span style=\"font-family: Palanquin Dark;\">a = 56b and a + b = 1710 <br></span><span style=\"font-family: Palanquin Dark;\">So, we get 56b + b = 1710 <br></span><span style=\"font-family: Palanquin Dark;\">57b = 1710&nbsp;<br></span><span style=\"font-family: Palanquin Dark;\">b = 30<br></span><span style=\"font-family: Palanquin Dark;\">a = 56b = 56(30) = 1680<br></span><span style=\"font-family: Palanquin Dark;\">Let second number = y<br></span><span style=\"font-family: Palanquin Dark;\">As we know that,&nbsp;<br></span><span style=\"font-family: Palanquin Dark;\">First number &times;</span><span style=\"font-family: Palanquin Dark;\">&nbsp;Second number = LCM &times;</span><span style=\"font-family: Palanquin Dark;\">&nbsp;HCF<br></span><span style=\"font-family: Palanquin Dark;\">240 &times; </span><span style=\"font-family: Palanquin Dark;\">y = 1680 &times;</span><span style=\"font-family: Palanquin Dark;\">&nbsp;30<br></span><span style=\"font-family: Palanquin Dark;\">y = 210 </span></p>",
                    solution_hi: "<p>1.(d) <span style=\"font-family: Palanquin Dark;\">माना LCM = a और HCF = b<br></span><span style=\"font-family: Palanquin Dark;\">प्रश्न के अनुसार,<br></span><span style=\"font-family: Palanquin Dark;\">a = 56b और a + b = 1710<br></span><span style=\"font-family: Palanquin Dark;\">अतः, हमें प्राप्त होता है 56b + b = 1710<br></span><span style=\"font-family: Palanquin Dark;\">57b = 1710<br></span><span style=\"font-family: Palanquin Dark;\">b = 30<br></span><span style=\"font-family: Palanquin Dark;\">a = 56b = 56 (30) = 1680<br></span><span style=\"font-family: Palanquin Dark;\">माना दूसरी संख्या = y<br></span><span style=\"font-family: Palanquin Dark;\">जैसा कि हम जानते हैं कि,<br></span><span style=\"font-family: Palanquin Dark;\">पहली संख्या &times;</span><span style=\"font-family: Palanquin Dark;\"> दूसरी संख्या = LCM &times;</span><span style=\"font-family: Palanquin Dark;\"> HCF<br></span><span style=\"font-family: Palanquin Dark;\">240 &times;</span><span style=\"font-family: Palanquin Dark;\"> y = 1680 &times;</span><span style=\"font-family: Palanquin Dark;\"> 30<br></span><span style=\"font-family: Palanquin Dark;\">y = 210</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "17",
                    question_en: "<p>2.<span style=\"font-family: Palanquin Dark;\"> Find the greatest number which divides 108, 124, and 156, leaving the same remainder.</span></p>",
                    question_hi: "<p>2.<span style=\"font-family: Palanquin Dark;\"> वह बड़ी से बड़ी संख्या ज्ञात कीजिए जो 108, 124 और 156 को विभाजित करने पर वही शेषफल प्रदान करती है।</span></p>",
                    options_en: ["<p>18</p>", "<p>10</p>", 
                                "<p>12</p>", "<p>16</p>"],
                    options_hi: ["<p>18</p>", "<p>10</p>",
                                "<p>12</p>", "<p>16</p>"],
                    solution_en: "<p>2.(d) <span style=\"font-family: Palanquin Dark;\">For finding the required greatest number,&nbsp;<br></span><span style=\"font-family: Palanquin Dark;\">we need to find HCF of difference between all three given numbers.<br></span><span style=\"font-family: Palanquin Dark;\">124 &ndash;108 = 16, 156 &ndash; 124 = 32 and 156 &ndash; 108 = 48<br></span><span style=\"font-family: Palanquin Dark;\">Required greatest number = HCF (16, 32, 48) = 16</span></p>",
                    solution_hi: "<p>2.(d) <span style=\"font-family: Palanquin Dark;\">सबसे बड़ी अभीष्ट संख्या ज्ञात करने के लिए,<br></span><span style=\"font-family: Palanquin Dark;\">हमें दी गई तीनों संख्याओं के बीच अंतर का HCF ज्ञात करना पड़ेगा ।<br></span><span style=\"font-family: Palanquin Dark;\">124 &ndash; 108 = 16, 156 &ndash; 124 = 32 और 156 &ndash; 108 = 48<br></span><span style=\"font-family: \'Palanquin Dark\';\">सबसे बड़ी अभीष्ट संख्या = HCF (16, 32, 48) = 16</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "17",
                    question_en: "<p>3. <span style=\"font-family: Palanquin Dark;\">What is the greater number by which when 156, 181 and 331 are divided, the remainder is 6 in each case?</span></p>",
                    question_hi: "<p>3. <span style=\"font-family: Palanquin Dark;\">वह कौन सी बड़ी संख्या है जिससे 156, 181 और 331 को विभाजित करने पर प्रत्येक स्थिति में शेषफल 6 आता है?</span></p>",
                    options_en: ["<p>26</p>", "<p>17</p>", 
                                "<p>25</p>", "<p>13</p>"],
                    options_hi: ["<p>26</p>", "<p>17</p>",
                                "<p>25</p>", "<p>13</p>"],
                    solution_en: "<p>3.(c) <span style=\"font-family: Palanquin Dark;\">For finding the required greatest number we need to deduct remainder from numbers given in question and then take HCF of numbers so obtained.<br></span><span style=\"font-family: Palanquin Dark;\">156 &ndash; 6 = 150&nbsp;<br></span><span style=\"font-family: Palanquin Dark;\">181 &ndash; 6 = 175<br></span><span style=\"font-family: Palanquin Dark;\">331 &ndash; 6 = 325<br></span><span style=\"font-family: Palanquin Dark;\">Required greatest number = HCF (150, 175, 325) = 25</span></p>",
                    solution_hi: "<p>3.(c) <span style=\"font-family: Palanquin Dark;\">सबसे बड़ी अपेक्षित संख्या ज्ञात करने के लिए हमें प्रश्न में दी गई संख्याओं में से शेष को घटाना होगा और फिर प्राप्त संख्याओं का HCF लेना होगा।<br></span><span style=\"font-family: Palanquin Dark;\">156 &ndash; 6 = 150&nbsp;<br></span><span style=\"font-family: Palanquin Dark;\">181 &ndash; 6 = 175<br></span><span style=\"font-family: Palanquin Dark;\">331 &ndash; 6 = 325<br></span><span style=\"font-family: Palanquin Dark;\">सबसे बड़ी अभीष्ट संख्या = HCF (150, 175, 325) = 25</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "17",
                    question_en: "<p>4. <span style=\"font-family: Palanquin Dark;\">Which is the smallest multiple of 7, which leaves 5 as a remainder in each case, when </span><span style=\"font-family: Palanquin Dark;\">divided by 8, 9, 12 and 15?</span></p>",
                    question_hi: "<p>4. <span style=\"font-family: Palanquin Dark;\">7 का सबसे छोटा गुणज कौन सा है, जिसे 8, 9, 12 और 15 से विभाजित करने पर प्रत्येक स्थिति में 5 शेष बचता है?</span></p>",
                    options_en: ["<p>365</p>", "<p>1085</p>", 
                                "<p>2525</p>", "<p>725</p>"],
                    options_hi: ["<p>365</p>", "<p>1085</p>",
                                "<p>2525</p>", "<p>725</p>"],
                    solution_en: "<p>4.(b)&nbsp; <span style=\"font-family: Palanquin Dark;\">LCM (8, 9, 12, 15) = 360<br></span><span style=\"font-family: Palanquin Dark;\">360 &times;</span><span style=\"font-family: Palanquin Dark;\"> 3 = 1080<br></span><span style=\"font-family: Palanquin Dark;\">Required number = 1080 + 5 = 1085, which is also the smallest multiple of 7.</span></p>",
                    solution_hi: "<p>4.(b)<span style=\"font-family: Palanquin Dark;\">LCM (8, 9, 12, 15) = 360<br></span><span style=\"font-family: Palanquin Dark;\">360 &times;</span><span style=\"font-family: Palanquin Dark;\"> 3 = 1080<br></span><span style=\"font-family: Palanquin Dark;\">अभीष्ट संख्या = 1080 + 5 = 1085, जो 7 का सबसे छोटा गुणज भी है।</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "17",
                    question_en: "<p>5.<span style=\"font-family: Palanquin Dark;\"> Six bells begin to toll together and toll, respectively, at intervals 3,4,6,7,8 and 12 seconds. After how many seconds, will they toll together again?</span></p>",
                    question_hi: "<p>5.<span style=\"font-family: Palanquin Dark;\"> छह घंटियां क्रमशः 3,4,6,7,8 और 12 सेकंड के अंतराल पर एक साथ बजना शुरू करती हैं। कितने सेकंड के बाद, वे फिर से एक साथ बजेंगी ?</span></p>",
                    options_en: ["<p>167</p>", "<p>168</p>", 
                                "<p>176</p>", "<p>186</p>"],
                    options_hi: ["<p>167</p>", "<p>168</p>",
                                "<p>176</p>", "<p>186</p>"],
                    solution_en: "<p>5.(b) <span style=\"font-family: Palanquin Dark;\">Number of seconds after which six bells will toll together again&nbsp;<br></span><span style=\"font-family: Palanquin Dark;\">= LCM (3, 4, 6, 7, 8, 12) = 168 seconds</span></p>",
                    solution_hi: "<p>5.(b) <span style=\"font-family: Palanquin Dark;\">जितने सेकंड के बाद, वे फिर से घंटियां एक साथ बजेंगी = LCM (3, 4, 6, 7, 8, 12) = 168 सेकंड</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "17",
                    question_en: "<p>6.<span style=\"font-family: Palanquin Dark;\"> A and B are two prime numbers such that A&gt;B and their LCM is 209. The value of A<sup>2 </sup></span><span style=\"font-family: Palanquin Dark;\">- B is:</span></p>",
                    question_hi: "<p>6.<span style=\"font-family: Palanquin Dark;\"> A और B ऐसी दो अभाज्य संख्याएँ हैं कि A &gt; B और उनका LCM 209 है। A&sup2;- B का मान है:</span></p>",
                    options_en: ["<p>350</p>", "<p>372</p>", 
                                "<p>361</p>", "<p>339</p>"],
                    options_hi: ["<p>350</p>", "<p>372</p>",
                                "<p>361</p>", "<p>339</p>"],
                    solution_en: "<p>6.(a) <span style=\"font-family: Palanquin Dark;\">In case of prime numbers, product of prime numbers = LCM of prime numbers and HCF = 1<br></span><span style=\"font-family: Palanquin Dark;\">According to the question,<br></span><span style=\"font-family: Palanquin Dark;\">A &times; </span><span style=\"font-family: Palanquin Dark;\">B = LCM &times;&nbsp;</span><span style=\"font-family: Palanquin Dark;\"> HCF = 209 &times;</span><span style=\"font-family: Palanquin Dark;\"> 1 = 209<br></span><span style=\"font-family: Palanquin Dark;\">A &times;</span><span style=\"font-family: Palanquin Dark;\"> B = 19 &times;</span><span style=\"font-family: Palanquin Dark;\"> 11<br></span><span style=\"font-family: Palanquin Dark;\">So, A = 19 and B = 11 &hellip;&hellip;(A &gt; B)<br></span><span style=\"font-family: Palanquin Dark;\">Hence, A<sup>2 </sup>- B</span><span style=\"font-family: Palanquin Dark;\"> = 19<sup>2</sup> - 11</span><span style=\"font-family: Palanquin Dark;\">&nbsp;= 361 &ndash; 11 = 350</span></p>",
                    solution_hi: "<p>6.(a) <span style=\"font-family: Palanquin Dark;\">अभाज्य संख्याओं की स्थिति में,&nbsp;<br></span><span style=\"font-family: Palanquin Dark;\">अभाज्य संख्याओं का गुणनफल = अभाज्य संख्याओं का LCM और HCF = 1<br></span><span style=\"font-family: Palanquin Dark;\">प्रश्न के अनुसार,<br></span><span style=\"font-family: Palanquin Dark;\">A &times; </span><span style=\"font-family: Palanquin Dark;\">B = LCM &times;</span><span style=\"font-family: Palanquin Dark;\"> HCF = 209 &times;</span><span style=\"font-family: Palanquin Dark;\"> 1 = 209<br></span><span style=\"font-family: Palanquin Dark;\">A &times;</span><span style=\"font-family: Palanquin Dark;\"> B = 19 &times;</span><span style=\"font-family: Palanquin Dark;\"> 11<br></span><span style=\"font-family: Palanquin Dark;\">इसलिए, A = 19 और B = 11 &hellip;&hellip;(A &gt; B)<br></span><span style=\"font-family: Palanquin Dark;\">अत: A<sup>2 </sup>- B = 19<sup>2</sup> - 11 = 361 &ndash; 11 = 350</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "17",
                    question_en: "<p>7. <span style=\"font-family: Palanquin Dark;\">13, a, b, c are four distinct numbers and the HCF of each pair of numbers (13,a) : (13,b): </span><span style=\"font-family: Palanquin Dark;\">(13,c) is 13,where a,b,c&nbsp;are each less than 60 and a &lt; b &lt; c. What is the value of&nbsp; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi mathvariant=\"normal\">a</mi><mo>+</mo><mi mathvariant=\"normal\">c</mi></mrow><mi mathvariant=\"normal\">b</mi></mfrac></math></span><span style=\"font-family: Palanquin Dark;\">?</span></p>",
                    question_hi: "<p>7. <span style=\"font-family: Palanquin Dark;\">13, a, b, c चार अलग-अलग संख्याएं हैं और संख्याओं की प्रत्येक युग्म का HCF (13,a) : (13,b): </span>13 है, जहां a, b, c प्रत्येक&nbsp;60 से कम है और a &lt; b &lt; c है। <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi mathvariant=\"normal\">a</mi><mo>+</mo><mi mathvariant=\"normal\">c</mi></mrow><mi mathvariant=\"normal\">b</mi></mfrac></math>&nbsp;<span style=\"font-family: Palanquin Dark;\"> का मान क्या है?</span></p>",
                    options_en: ["<p>3.5</p>", "<p>2</p>", 
                                "<p>5</p>", "<p>4.5</p>"],
                    options_hi: ["<p>3.5</p>", "<p>2</p>",
                                "<p>5</p>", "<p>4.5</p>"],
                    solution_en: "<p>7.(b) <span style=\"font-family: Palanquin Dark;\">As, HCF of (13, a), (13, b) and (13, c) is 13, a, b and c must be multiples of 13.<br></span><span style=\"font-family: Palanquin Dark;\">Only possible value for a, b and c such that a &lt; b &lt; c &lt; 60 is 26, 39 and 52.<br></span><span style=\"font-family: Palanquin Dark;\">So, a = 26, b = 39 and c = 52.<br></span><span style=\"font-family: Palanquin Dark;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi mathvariant=\"normal\">a</mi><mo>+</mo><mi mathvariant=\"normal\">c</mi></mrow><mi mathvariant=\"normal\">b</mi></mfrac><mo>=</mo><mfrac><mrow><mn>26</mn><mo>+</mo><mn>52</mn></mrow><mn>39</mn></mfrac></math> =</span><span style=\"font-family: Palanquin Dark;\">&nbsp;2.</span></p>",
                    solution_hi: "<p>7.(b) <span style=\"font-family: Palanquin Dark;\">चूँकि, (13, a), (13, b) और (13, c) का HCF 13 है, a, b और c का HCF 13 का गुणक होना चाहिए।<br></span><span style=\"font-family: Palanquin Dark;\">a, b और c के लिए केवल संभव मान इस प्रकार है कि a &lt; b &lt; c &lt; 60 26, 39 और 52 है।<br></span><span style=\"font-family: Palanquin Dark;\">इसलिए, a = 26, b = 39 और c = 52 है।<br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi mathvariant=\"normal\">a</mi><mo>+</mo><mi mathvariant=\"normal\">c</mi></mrow><mi mathvariant=\"normal\">b</mi></mfrac><mo>=</mo><mfrac><mrow><mn>26</mn><mo>+</mo><mn>52</mn></mrow><mn>39</mn></mfrac></math> = 2.<br></span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "17",
                    question_en: "<p>8. <span style=\"font-family: Palanquin Dark;\">A and B are two prime numbers such that A &gt; B and their LCM is 209. The value of&nbsp; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi mathvariant=\"normal\">B</mi><mn>2</mn></msup><mo>-</mo><mi mathvariant=\"normal\">A</mi></math></span><span style=\"font-family: Palanquin Dark;\">is:</span></p>",
                    question_hi: "<p>8. <span style=\"font-family: Palanquin Dark;\">A और B ऐसी दो अभाज्य संख्याएँ हैं कि A &gt; B और उनका LCM 209 है। तो <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi mathvariant=\"normal\">B</mi><mn>2</mn></msup><mo>-</mo><mi mathvariant=\"normal\">A</mi></math> </span><span style=\"font-family: Palanquin Dark;\">का मान क्या होगा ?</span></p>",
                    options_en: ["<p>111</p>", "<p>102</p>", 
                                "<p>121</p>", "<p>109</p>"],
                    options_hi: ["<p>111</p>", "<p>102</p>",
                                "<p>121</p>", "<p>109</p>"],
                    solution_en: "<p>8.(b) <span style=\"font-family: Palanquin Dark;\">209 = 19 &times;11<br></span><span style=\"font-family: Palanquin Dark;\">Clearly, A = 19 and B = 11<br></span><span style=\"font-family: Palanquin Dark;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi mathvariant=\"normal\">B</mi><mn>2</mn></msup><mo>-</mo><mi mathvariant=\"normal\">A</mi><mo>=</mo><msup><mn>11</mn><mn>2</mn></msup><mo>-</mo><mn>19</mn></math> = </span><span style=\"font-family: Palanquin Dark;\">121 &ndash; 19 = 102</span></p>",
                    solution_hi: "<p>8.(b) <span style=\"font-family: Palanquin Dark;\">209 = 19 &times;11<br></span><span style=\"font-family: Palanquin Dark;\">स्पष्टत:, A = 19 और B = 11<br></span><span style=\"font-family: Palanquin Dark;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi mathvariant=\"normal\">B</mi><mn>2</mn></msup><mo>-</mo><mi mathvariant=\"normal\">A</mi><mo>=</mo><msup><mn>11</mn><mn>2</mn></msup><mo>-</mo><mn>19</mn></math> = </span><span style=\"font-family: Palanquin Dark;\">121 &ndash; 19 = 102</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "17",
                    question_en: "<p>9. <span style=\"font-family: Palanquin Dark;\">What is the least number which when decreased by 7 is divisible by 15, 24, 28 and 32?</span></p>",
                    question_hi: "<p>9. <span style=\"font-family: Palanquin Dark;\">वह छोटी से छोटी संख्या कौन सी है जिसे 7 से कम करने पर 15, 24, 28 और 32 से विभाजित हो जाती है?</span></p>",
                    options_en: ["<p>10097</p>", "<p>10087</p>", 
                                "<p>10067</p>", "<p>10077</p>"],
                    options_hi: ["<p>10097</p>", "<p>10087</p>",
                                "<p>10067</p>", "<p>10077</p>"],
                    solution_en: "<p>9.(b) <span style=\"font-family: Palanquin Dark;\">LCM (15, 24, 28, 32) = 3360<br></span><span style=\"font-family: Palanquin Dark;\">3360 &times; </span><span style=\"font-family: Palanquin Dark;\">3 = 10080<br></span><span style=\"font-family: Palanquin Dark;\">Required number = 10080 + 7 = 10087</span></p>",
                    solution_hi: "<p>9.(b) <span style=\"font-family: Palanquin Dark;\">LCM (15, 24, 28, 32) = 3360<br></span><span style=\"font-family: Palanquin Dark;\">3360&nbsp;&times; </span><span style=\"font-family: Palanquin Dark;\">3 = 10080<br></span><span style=\"font-family: Palanquin Dark;\">अभीष्ट संख्या = 10080 + 7 = 10087</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "17",
                    question_en: "<p>10.<span style=\"font-family: Palanquin Dark;\"> The greatest number that divides 126, 224 and 608 leaving remainders 2, 7 and 19, respectively, is:</span></p>",
                    question_hi: "<p>10. <span style=\"font-family: Palanquin Dark;\">&nbsp;वह सबसे बड़ी संख्या जिससे 126, 224 और 608 को विभाजित करने पर क्रमशः 2, 7 और 19 शेषफल प्राप्त होते हैं:</span></p>",
                    options_en: ["<p>27</p>", "<p>31</p>", 
                                "<p>21</p>", "<p>37</p>"],
                    options_hi: ["<p>27</p>", "<p>31</p>",
                                "<p>21</p>", "<p>37</p>"],
                    solution_en: "<p>10.(b) <span style=\"font-family: Palanquin Dark;\">For finding the required greatest number, we need to deduct the remainder from the respective number and then find HCF of </span><span style=\"font-family: Palanquin Dark;\">numbers so obtained</span><span style=\"font-family: Palanquin Dark;\">.<br></span><span style=\"font-family: Palanquin Dark;\">126 &ndash; 2 = 124<br></span><span style=\"font-family: Palanquin Dark;\">224 &ndash; 7 = 217<br></span><span style=\"font-family: Palanquin Dark;\">608 &ndash; 19 = 589<br></span><span style=\"font-family: Palanquin Dark;\">Required greatest number = HCF (124, 217, 589) = 31</span></p>",
                    solution_hi: "<p>10.(b)<span style=\"font-family: Palanquin Dark;\">सबसे बड़ी आवश्यक संख्या ज्ञात करने के लिए, हमें शेष को संबंधित संख्या से घटाना होगा और फिर प्राप्त संख्याओं का HCF ज्ञात करना होगा।<br></span><span style=\"font-family: Palanquin Dark;\">126 &ndash; 2 = 124<br></span><span style=\"font-family: Palanquin Dark;\">224 &ndash; 7 = 217<br></span><span style=\"font-family: Palanquin Dark;\">608 &ndash; 19 = 589<br></span><span style=\"font-family: Palanquin Dark;\">अभीष्ट सबसे बड़ी संख्या = HCF (124, 217, 589) = 31</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "17",
                    question_en: "<p>11. <span style=\"font-family: Palanquin Dark;\">&nbsp;What is the greatest four-digit number which on being divided by 6,7 and 8 leaves 4,5 and 6 as remainders,respectively?</span></p>",
                    question_hi: "<p>11.<span style=\"font-family: Palanquin Dark;\"> चार अंकों की वह सबसे बड़ी संख्या कौन सी है जिसे 6,7 और 8 से विभाजित करने पर क्रमशः 4,5 और 6 शेष बचे हैं?</span></p>",
                    options_en: ["<p>9910</p>", "<p>9920</p>", 
                                "<p>9921</p>", "<p>9912</p>"],
                    options_hi: ["<p>9910</p>", "<p>9920</p>",
                                "<p>9921</p>", "<p>9912</p>"],
                    solution_en: "<p>11.(a) <span style=\"font-family: Palanquin Dark;\">LCM (6,7 and 8) = 168<br></span><span style=\"font-weight: 400;\">(6 &ndash; 4) = (7 &ndash; 5) = (8 &ndash; 6) = 2<br></span><span style=\"font-family: Palanquin Dark;\">Required number = 168k &ndash; 2<br></span><span style=\"font-family: Palanquin Dark;\">For k = 59 we have required number = 9910</span></p>",
                    solution_hi: "<p>11.(a) <span style=\"font-family: Palanquin Dark;\">LCM (6, 7 और 8) = 168<br></span><span style=\"font-weight: 400;\">(6 &ndash; 4) = (7 &ndash; 5) = (8 &ndash; 6) = 2<br></span><span style=\"font-family: Palanquin Dark;\">आवश्यक संख्या = 168k &ndash; 2<br></span><span style=\"font-family: Palanquin Dark;\">k = 59 के लिए हमारे पास अपेक्षित संख्या है = 9910</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "17",
                    question_en: "<p>12. <span style=\"font-family: Palanquin Dark;\">&nbsp;LCM of two numbers is 22 times their HCF. If one of the numbers is 132 and the sum of LCM and HCF is 276, then what is the other number?</span></p>",
                    question_hi: "<p>12.<span style=\"font-family: Palanquin Dark;\"> दो संख्याओं का LCM उनके HCF का 22 गुना है। यदि संख्याओं में से एक 132 है और LCM और HCF का योग 276 है, तो दूसरी संख्या क्या है?</span></p>",
                    options_en: ["<p>24</p>", "<p>30</p>", 
                                "<p>25</p>", "<p>20</p>"],
                    options_hi: ["<p>24</p>", "<p>30</p>",
                                "<p>25</p>", "<p>20</p>"],
                    solution_en: "<p>12.(a) <span style=\"font-family: Palanquin Dark;\">Let second number = b, LCM = x and HCF = y<br></span><span style=\"font-family: Palanquin Dark;\">According to the question,<br></span><span style=\"font-family: Palanquin Dark;\">x = 22y<br></span><span style=\"font-family: Palanquin Dark;\">x + y = 276<br></span><span style=\"font-family: Palanquin Dark;\">So, 22y + y = 276&nbsp;<br></span><span style=\"font-family: Palanquin Dark;\">23y = 276<br></span><span style=\"font-family: Palanquin Dark;\">y = 12<br></span><span style=\"font-family: Palanquin Dark;\">So, x = 22y = 22(12) = 264<br></span><span style=\"font-family: Palanquin Dark;\">Now, product of two numbers = LCM &times; </span><span style=\"font-family: Palanquin Dark;\">HCF<br></span><span style=\"font-family: Palanquin Dark;\">132&nbsp;&times;&nbsp;</span><span style=\"font-family: Palanquin Dark;\">b = 264 &times; </span><span style=\"font-family: Palanquin Dark;\">12<br></span><span style=\"font-family: Palanquin Dark;\">b = 24</span></p>",
                    solution_hi: "<p>12.(a) <span style=\"font-family: Palanquin Dark;\">माना दूसरी संख्या = b, LCM = x और HCF = y<br></span><span style=\"font-family: Palanquin Dark;\">प्रश्न के अनुसार,<br></span><span style=\"font-family: Palanquin Dark;\">x = 22y<br></span><span style=\"font-family: Palanquin Dark;\">x + y = 276<br></span><span style=\"font-family: Palanquin Dark;\">इसलिए, 22y + y = 276&nbsp;<br></span><span style=\"font-family: Palanquin Dark;\">23y = 276<br></span><span style=\"font-family: Palanquin Dark;\">y = 12<br></span><span style=\"font-family: Palanquin Dark;\">अत: x = 22y = 22(12) = 264<br></span><span style=\"font-family: Palanquin Dark;\">अब, दो संख्याओं का गुणनफल = LCM&nbsp;&times; </span><span style=\"font-family: Palanquin Dark;\">HCF<br></span><span style=\"font-family: Palanquin Dark;\">132&nbsp;&times;&nbsp;</span><span style=\"font-family: Palanquin Dark;\">b = 264 &times; </span><span style=\"font-family: Palanquin Dark;\">12<br></span><span style=\"font-family: Palanquin Dark;\">b = 24</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "17",
                    question_en: "<p>13. <span style=\"font-family: Palanquin Dark;\"> Three </span><span style=\"font-family: Palanquin Dark;\">number</span><span style=\"font-family: Palanquin Dark;\"> are in the proportion of 3 : 8 : 15 and their LCM is 8280. What is their HCF?</span></p>",
                    question_hi: "<p>13. <span style=\"font-family: Palanquin Dark;\">&nbsp;तीन संख्याएँ 3 : 8 : 15 के अनुपात में हैं और उनका LCM 8280 है। उनका HCF क्या है?</span></p>",
                    options_en: ["<p>60</p>", "<p>69</p>", 
                                "<p>75</p>", "<p>57</p>"],
                    options_hi: ["<p>60</p>", "<p>69</p>",
                                "<p>75</p>", "<p>57</p>"],
                    solution_en: "<p>13.(b) Let three numbers<br><span style=\"font-family: \'Palanquin Dark\';\">3x : 8x : 15x<br></span><span style=\"font-family: Palanquin Dark;\">LCM of (3x,8x,15x) = 120x<br></span><span style=\"font-family: Palanquin Dark;\">HCF of (3x,8x,15x) = x<br></span><span style=\"font-family: Palanquin Dark;\">According to the question<br></span><span style=\"font-family: Palanquin Dark;\">LCM = 120x = 8280<br></span><span style=\"font-family: Palanquin Dark;\">x = 69<br></span><span style=\"font-family: Palanquin Dark;\">Now ,HCF = x = 69</span></p>",
                    solution_hi: "<p>13.(b) माना तीन संख्याएं हैं<br><span style=\"font-family: \'Palanquin Dark\';\">3x : 8x : 15x<br></span><span style=\"font-weight: 400;\">(3x,8x,15x) </span>का LCM = 120x<br><span style=\"font-weight: 400;\">(3x,8x,15x) </span>का LCM = x<br><span style=\"font-family: Palanquin Dark;\">प्रश्न के अनुसार,<br></span><span style=\"font-family: Palanquin Dark;\">LCM = 120x = 8280<br></span><span style=\"font-family: Palanquin Dark;\">x = 69<br></span><span style=\"font-family: Palanquin Dark;\">अब, HCF = x = 69</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "17",
                    question_en: "<p>14.<span style=\"font-family: Palanquin Dark;\"> What is the LCM of 3.6, 1.8 and 0.144?</span></p>",
                    question_hi: "<p>14. <span style=\"font-family: Palanquin Dark;\">3.6, 1.8 और 0.144 का लघुतम समापवर्त्य क्या है?</span></p>",
                    options_en: ["<p>3.6</p>", "<p>36</p>", 
                                "<p>3600</p>", "<p>360</p>"],
                    options_hi: ["<p>3.6</p>", "<p>36</p>",
                                "<p>3600</p>", "<p>360</p>"],
                    solution_en: "<p>14.(a) <span style=\"font-family: Palanquin Dark;\">LCM of 3.6, 1.8 and 0.144<br></span><span style=\"font-family: Palanquin Dark;\">3.6 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>36</mn><mn>10</mn></mfrac><mo>=</mo><mfrac><mn>18</mn><mn>5</mn></mfrac></math></span><span style=\"font-family: Palanquin Dark;\">&nbsp;</span><span style=\"font-family: Palanquin Dark;\">,&nbsp;<br></span><span style=\"font-family: Palanquin Dark;\">1.8</span><span style=\"font-family: Palanquin Dark;\"> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>18</mn><mn>10</mn></mfrac><mo>=</mo><mfrac><mn>9</mn><mn>5</mn></mfrac></math></span><span style=\"font-family: Palanquin Dark;\"> </span><span style=\"font-family: Palanquin Dark;\">and <br></span><span style=\"font-family: Palanquin Dark;\">0.144</span><span style=\"font-family: Palanquin Dark;\"> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>144</mn><mn>1000</mn></mfrac><mo>=</mo><mfrac><mn>18</mn><mn>125</mn></mfrac></math></span><span style=\"font-family: Palanquin Dark;\"> <br></span><span style=\"font-family: Palanquin Dark;\">Now, LCM of&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>18</mn><mn>5</mn></mfrac><mo>,</mo><mo>&#160;</mo><mfrac><mn>9</mn><mn>5</mn></mfrac><mo>,</mo><mo>&#160;</mo><mi>and</mi><mo>&#160;</mo><mfrac><mn>18</mn><mn>125</mn></mfrac><mo>=</mo><mfrac><mrow><mi>LCMof</mi><mo>(</mo><mn>18</mn><mo>,</mo><mn>9</mn><mo>,</mo><mn>18</mn><mo>)</mo></mrow><mrow><mi>HCFof</mi><mo>(</mo><mn>5</mn><mo>,</mo><mn>5</mn><mo>,</mo><mn>125</mn><mo>)</mo></mrow></mfrac><mfrac><mn>18</mn><mn>5</mn></mfrac><mo>=</mo><mn>3</mn><mo>.</mo><mn>6</mn></math></span></p>",
                    solution_hi: "<p>14.(a) <span style=\"font-family: Palanquin Dark;\">3.6, 1.8 और 0.144 का LCM<br></span><span style=\"font-family: Palanquin Dark;\">3.6 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>36</mn><mn>10</mn></mfrac><mo>=</mo><mfrac><mn>18</mn><mn>5</mn></mfrac></math></span><span style=\"font-family: Palanquin Dark;\">&nbsp;</span><span style=\"font-family: Palanquin Dark;\">,&nbsp;<br></span><span style=\"font-family: Palanquin Dark;\">1.8</span><span style=\"font-family: Palanquin Dark;\"> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>18</mn><mn>10</mn></mfrac><mo>=</mo><mfrac><mn>9</mn><mn>5</mn></mfrac></math></span><span style=\"font-family: Palanquin Dark;\"> और</span><span style=\"font-family: Palanquin Dark;\"><br></span><span style=\"font-family: Palanquin Dark;\">0.144</span><span style=\"font-family: Palanquin Dark;\"> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>144</mn><mn>1000</mn></mfrac><mo>=</mo><mfrac><mn>18</mn><mn>125</mn></mfrac></math></span></p>\n<p><span style=\"font-family: Palanquin Dark;\">अब,<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>18</mn><mn>5</mn></mfrac></math></span><span style=\"font-weight: 400;\">, <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>9</mn><mn>5</mn></mfrac></math></span><span style=\"font-weight: 400;\">और <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>18</mn><mn>125</mn></mfrac></math></span><span style=\"font-weight: 400;\">&nbsp;का LCM = </span><span style=\"font-weight: 400;\"> </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&#160;</mo><mo>&#160;</mo><mn>18</mn><mo>,</mo><mo>&#160;</mo><mn>9</mn><mo>,</mo><mo>&#160;</mo><mn>18</mn><mo>&#160;</mo><mi>&#2325;&#2366;</mi><mo>&#160;</mo><mi>LCM</mi><mo>&#160;</mo></mrow><mrow><mn>5</mn><mo>,</mo><mo>&#160;</mo><mn>5</mn><mo>,</mo><mo>&#160;</mo><mn>125</mn><mo>&#160;</mo><mi>&#2325;&#2366;</mi><mo>&#160;</mo><mi>HCF</mi></mrow></mfrac></math><span style=\"font-weight: 400;\">= </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>18</mn><mn>5</mn></mfrac></math><span style=\"font-weight: 400;\"> </span><span style=\"font-weight: 400;\">= 3.6</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "17",
                    question_en: "<p>15.<span style=\"font-family: Palanquin Dark;\"> Find the greatest 3-digit number which when divided by 3, 4, 5, and 8 leaves remainder 2 in each case</span></p>",
                    question_hi: "<p>15. <span style=\"font-family: Palanquin Dark;\">तीन अंकों की सबसे बड़ी संख्या ज्ञात कीजिए जिसे 3, 4, 5 और 8 से विभाजित करने पर प्रत्येक स्थिति में शेषफल 2 बचे |</span></p>",
                    options_en: ["<p>962</p>", "<p>122</p>", 
                                "<p>958</p>", "<p>482</p>"],
                    options_hi: ["<p>962</p>", "<p>122</p>",
                                "<p>958</p>", "<p>482</p>"],
                    solution_en: "<p>15.(a) <span style=\"font-family: Palanquin Dark;\">Greatest 3 - digit number which when divided by 3, 4, 5, and 8 leaves remainder 2 in each case is </span><span style=\"font-family: Palanquin Dark;\"><strong>[LCM of (3,4,5,8)] &times; k + 2 ] &rArr; </strong></span><span style=\"font-family: Palanquin Dark;\"><strong>120k + 2</strong>&nbsp;&rArr;</span><span style=\"font-family: Palanquin Dark;\">&nbsp;</span><span style=\"font-family: Palanquin Dark;\">for greatest 3 digit number value of k must be greatest&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>[</mo><mn>120</mn><mo>&#215;</mo><mn>8</mn><mo>+</mo><mn>2</mn><mo>]</mo><mo>=</mo><mn>962</mn></math></span></p>",
                    solution_hi: "<p>15.(a) <span style=\"font-family: Palanquin Dark;\">सबसे बड़ी 3-अंकीय संख्या, जिसे 3, 4, 5, और 8 से विभाजित करने पर प्रत्येक स्थिति में 2 शेष बचता है, </span><strong><span style=\"font-family: Palanquin Dark;\">[(3,4,5,8)का LCM] &times; k + 2 होगी</span></strong><span style=\"font-family: Palanquin Dark;\"><strong> &rArr; 120k + 2</strong> &rArr;</span><span style=\"font-family: Palanquin Dark;\">&nbsp;</span><span style=\"font-family: Palanquin Dark;\">सबसे बड़ी 3-अंकीय संख्या के लिए k का मान 8 होगा&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>[</mo><mn>120</mn><mo>&#215;</mo><mn>8</mn><mo>+</mo><mn>2</mn><mo>]</mo><mo>=</mo><mn>962</mn></math></span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Times New Roman;\">16. </span><span style=\"font-family: Times New Roman;\">The least number which should be added to 3627 so that the sum is exactly divisible by 4,5,6 and 8 is&nbsp;</span></p>",
                    question_hi: "<p><span style=\"font-family: Times New Roman;\">16. </span><span style=\"font-family: Baloo;\">वह छोटी से छोटी संख्या जिसे 3627 में जोड़ा जाना चाहिए ताकि योग 4,5,6 और 8 से पूर्णतः विभाजित हो जाए:</span></p>",
                    options_en: ["<p>93</p>", "<p>39</p>", 
                                "<p>27</p>", "<p>72</p>"],
                    options_hi: ["<p>93</p>", "<p>39</p>",
                                "<p>27</p>", "<p>72</p>"],
                    solution_en: "<p>16.(a) <span style=\"font-family: Times New Roman;\">LCM of 4,5,6,8 is 120<br></span><span style=\"font-family: Times New Roman;\">Now remainder = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&#160;</mo><mn>3627</mn></mrow><mn>120</mn></mfrac></math></span><span style=\"font-family: Times New Roman;\"> = 27 so if we add (120 - 27) = 93&nbsp;<br></span><span style=\"font-family: Times New Roman;\">then it will be completely divisible by 120 </span></p>",
                    solution_hi: "<p>16.(a) <span style=\"font-family: Baloo;\">4,5,6,8 का LCM 120 है।<br></span><span style=\"font-family: Baloo;\">अब शेषफल = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&#160;</mo><mn>3627</mn></mrow><mn>120</mn></mfrac></math></span><span style=\"font-family: Baloo;\">= 27 इसलिए, यदि हम जोड़ दें (120 - 27) = 93<br></span><span style=\"font-family: Baloo;\">तो यह 120 से पूर्णतः विभाज्य हो जाएगा</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Baloo;\">17. </span><span style=\"font-family: Baloo;\">Find the smallest number which should be added to the smaller number divisible by 6, 9 and 15 to make it perfect square</span></p>",
                    question_hi: "<p>17. <span style=\"font-family: Palanquin Dark;\">वह छोटी से छोटी संख्या ज्ञात कीजिए जो 6, 9 और 15 से विभाज्य छोटी संख्या में जोड़ने पर पूर्ण वर्ग बन जाए</span></p>",
                    options_en: ["<p>10</p>", "<p>9</p>", 
                                "<p>19</p>", "<p>21</p>"],
                    options_hi: ["<p>10</p>", "<p>9</p>",
                                "<p>19</p>", "<p>21</p>"],
                    solution_en: "<p>17.(a) <span style=\"font-family: Times New Roman;\">LCM of (6,9,15) is 90<br></span><span style=\"font-family: Times New Roman;\">Next perfect square is 100&nbsp;<br></span><span style=\"font-family: Times New Roman;\">90 + x = 100<br></span><span style=\"font-family: Times New Roman;\">x = 10</span></p>",
                    solution_hi: "<p>17.(a) <span style=\"font-family: Baloo;\">(6,9,15) का LCM = 90&nbsp;<br></span><span style=\"font-family: Baloo;\">अगला पूर्ण वर्ग = 100&nbsp;<br></span><span style=\"font-family: Times New Roman;\">90 + x = 100<br></span><span style=\"font-family: Times New Roman;\">x = 10</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "misc",
                    question_en: "<p>18. <span style=\"font-family: Times New Roman;\">&nbsp;LCM and HCF of two numbers are 90 and 15, respectively. If the sum of the two numbers is 75, find the greater number.</span></p>",
                    question_hi: "<p>18. <span style=\"font-family: Palanquin Dark;\">&nbsp;दो संख्याओं का LCM और HCF क्रमशः 90 और 15 है। यदि दो संख्याओं का योग 75 है, तो बड़ी संख्या ज्ञात कीजिए।</span></p>",
                    options_en: ["<p>45</p>", "<p>90</p>", 
                                "<p>75</p>", "<p>60</p>"],
                    options_hi: ["<p>45</p>", "<p>90</p>",
                                "<p>75</p>", "<p>60</p>"],
                    solution_en: "<p>18.(a) <span style=\"font-family: Palanquin Dark;\">Let, the two numbers a and b<br></span><span style=\"font-family: Palanquin Dark;\">Now, HCF &times; LCM = a &times; b<br></span><span style=\"font-family: \'Palanquin Dark\';\">a &times; b = 90 &times; 15<br></span><span style=\"font-family: Palanquin Dark;\">a + b = 75 &hellip;&hellip;(1)<br>(a - b)<sup>2</sup> = (a + b)<sup>2</sup> - 4ab<br>(a - b)<sup>2</sup> = (75)<sup>2</sup> - 4(1350)<br>(a - b)<sup>2</sup> &nbsp;= 225<br>a - b = 15 </span><span style=\"font-family: Palanquin Dark;\">&hellip;&hellip;(2)<br></span><span style=\"font-family: Palanquin Dark;\">Solving Equ. (1) and (2)&nbsp;<br></span><span style=\"font-family: Palanquin Dark;\">a = 30 , b = 45<br></span><span style=\"font-family: Palanquin Dark;\">Greater number is 45 hence, it is required answer. <br></span><strong><span style=\"font-family: Palanquin Dark;\">Alternate :&nbsp;<br></span></strong><span style=\"font-family: Palanquin Dark;\">By hit and trial take 45 as it is also multiple of HCF 15, so another no is 75 - 45 = 30<br></span><span style=\"font-family: Palanquin Dark;\">Now HCF &times; LCM = a &times; b<br></span><span style=\"font-family: Palanquin Dark;\">90 &times; 15 = 45 &times; 30 satisfies so 45 is correct answer</span></p>",
                    solution_hi: "<p>18.(a) <span style=\"font-family: Palanquin Dark;\">माना, दो संख्याएँ a और b<br></span><span style=\"font-family: Palanquin Dark;\">अब, HCF &times; LCM = a &times; b<br></span><span style=\"font-family: \'Palanquin Dark\';\">a &times; b = 90 &times; 15<br></span><span style=\"font-family: Palanquin Dark;\">a + b = 75 &hellip;&hellip;(1)<br>(a - b)<sup>2</sup> = (a + b)<sup>2</sup> - 4ab<br>(a - b)<sup>2</sup> = (75)<sup>2</sup> - 4(1350)<br>(a - b)<sup>2</sup> &nbsp;= 225<br>a - b = 15 </span><span style=\"font-family: Palanquin Dark;\">&hellip;&hellip;(2)<br></span><span style=\"font-family: Palanquin Dark;\">समीकरण (1) और (2) को हल करने पर:<br></span><span style=\"font-family: Palanquin Dark;\">a = 30 , b = 45<br></span><span style=\"font-family: Palanquin Dark;\">बड़ी संख्या 45 इसलिए, इसका उत्तर 45 आवश्यक है।<br></span><strong><span style=\"font-family: Palanquin Dark;\">वैकल्पिक :<br></span></strong><span style=\"font-family: Palanquin Dark;\">हिट एंड ट्रायल द्वारा 45 लें क्योंकि यह HCF 15 का भी गुणक है, इसलिए दूसरी संख्या 75 - 45 = 30 है।<br></span><span style=\"font-family: Palanquin Dark;\">अब HCF &times; LCM = a &times; b<br></span><span style=\"font-family: Palanquin Dark;\">90 &times; 15 = 45 &times; 30 संतुष्ट करता है, इसलिए 45 सही उत्तर है।</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>