<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">15:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 25</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">25</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 15 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["6"] = {
                name: "Reasoning",
                start: 0,
                end: 23
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="6">Reasoning</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 24,
                end: 24
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "6",
                    question_en: "<p>1. <span style=\"font-family: Cambria Math;\">Which number can replace the question mark (?) in the following series ? </span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">1, 10, 28, 55, 91, 136, ?</span></p>\\n",
                    question_hi: "<p>1. <span style=\"font-family: Nirmala UI;\">&#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2358;&#2381;&#2352;&#2306;&#2326;&#2354;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2358;&#2381;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2330;&#2367;&#2344;&#2381;&#2361;</span><span style=\"font-family: Cambria Math;\"> (?) </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2381;&#2341;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2380;&#2344;</span><span style=\"font-family: Cambria Math;\">-</span><span style=\"font-family: Nirmala UI;\">&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2325;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> ?</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">1, 10, 28, 55, 91, 136, ?</span></p>\\n",
                    options_en: ["<p>190</p>\\n", "<p>191</p>\\n", 
                                "<p>192</p>\\n", "<p>195</p>\\n"],
                    options_hi: ["<p>190</p>\\n", "<p>191</p>\\n",
                                "<p>192</p>\\n", "<p>195</p>\\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">1.(a)</span></p>\\r\\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1701509074/word/media/image1.png\" width=\"300\" height=\"64\"></p>\\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">1.(a)</span></p>\\r\\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1701509074/word/media/image1.png\" width=\"304\" height=\"65\"></p>\\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "6",
                    question_en: "<p>2. <span style=\"font-family: Cambria Math;\">Select the set in which the numbers are related in the same way as are the numbers of the following sets.</span></p>\\r\\n<p><span style=\"font-weight: 400;\">(NOTE: Operations should be performed on whole numbers, without breaking down the numbers into their constituent digits. E.g. 13-Operations on 13 such as adding/subtracting/multiplying, etc. to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed.)</span></p>\\r\\n<p><span style=\"font-weight: 400;\">(162, 9, 18)</span></p>\\r\\n<p><span style=\"font-weight: 400;\">(231, 11, 21)</span></p>\\n",
                    question_hi: "<p>2. <span style=\"font-family: Nirmala UI;\">&#2313;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2350;&#2369;&#2330;&#2381;&#2330;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2330;&#2369;&#2344;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2367;&#2360;&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;&#2319;&#2305;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2336;&#2368;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2325;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2348;&#2306;&#2343;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2367;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2325;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2350;&#2369;&#2330;&#2381;&#2330;&#2351;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;&#2319;&#2305;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2348;&#2306;&#2343;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span></p>\\r\\n<p><span style=\"font-weight: 400;\">(&#2343;&#2381;&#2351;&#2366;&#2344; &#2342;&#2375;&#2306;: &#2360;&#2306;&#2326;&#2381;&#2351;&#2366;&#2323;&#2306; &#2325;&#2379; &#2313;&#2344;&#2325;&#2375; &#2328;&#2335;&#2325; &#2309;&#2306;&#2325;&#2379;&#2306; &#2350;&#2375;&#2306; &#2309;&#2354;&#2327;-&#2309;&#2354;&#2327; &#2325;&#2367;&#2319; &#2348;&#2367;&#2344;&#2366;, &#2346;&#2370;&#2352;&#2381;&#2339; &#2360;&#2306;&#2326;&#2381;&#2351;&#2366;&#2323;&#2306; &#2346;&#2352; &#2360;&#2325;&#2381;&#2352;&#2367;&#2351;&#2366;&#2319;&#2305; &#2325;&#2368; &#2332;&#2366;&#2344;&#2368; &#2330;&#2366;&#2361;&#2367;&#2319;&#2404; &#2313;&#2342;&#2366;. 13 - &#2360;&#2306;&#2326;&#2381;&#2351;&#2366; 13 &#2346;&#2352; &#2360;&#2306;&#2325;&#2381;&#2352;&#2367;&#2351;&#2366;&#2319;&#2305; &#2332;&#2376;&#2360;&#2375; 13 &#2325;&#2379; &#2332;&#2379;&#2337;&#2364;&#2344;&#2366; / &#2328;&#2335;&#2366;&#2344;&#2366; / &#2327;&#2369;&#2339;&#2366; &#2325;&#2352;&#2344;&#2366; &#2310;&#2342;&#2367; &#2325;&#2367;&#2351;&#2366; &#2332;&#2366; &#2360;&#2325;&#2340;&#2366; &#2361;&#2376;&#2404; 13 &#2325;&#2379; 1 &#2324;&#2352; 3 &#2350;&#2375;&#2306; &#2309;&#2354;&#2327;-&#2309;&#2354;&#2327; &#2325;&#2352;&#2344;&#2375; &#2325;&#2368; &#2323;&#2352; &#2347;&#2367;&#2352; 1 &#2324;&#2352; 3 &#2346;&#2352; &#2327;&#2339;&#2367;&#2340;&#2368;&#2351; &#2360;&#2306;&#2325;&#2381;&#2352;&#2367;&#2351;&#2366;&#2319;&#2305; &#2325;&#2352;&#2344;&#2375; &#2325;&#2368; &#2309;&#2344;&#2369;&#2350;&#2340;&#2367; &#2344;&#2361;&#2368;&#2306; &#2361;&#2376;&#2404;)</span></p>\\r\\n<p><span style=\"font-weight: 400;\">(162, 9, 18)</span></p>\\r\\n<p><span style=\"font-weight: 400;\">(231, 11, 21)</span></p>\\n",
                    options_en: ["<p><span style=\"font-weight: 400;\">(336, 28, 12)</span></p>\\n", "<p><span style=\"font-weight: 400;\">(270, 12, 23)</span></p>\\n", 
                                "<p><span style=\"font-weight: 400;\">&nbsp;(314, 19, 16)</span></p>\\n", "<p><span style=\"font-weight: 400;\">(296, 21, 14)</span></p>\\n"],
                    options_hi: ["<p><span style=\"font-weight: 400;\">(336, 28, 12)</span></p>\\n", "<p><span style=\"font-weight: 400;\">(270, 12, 23)</span></p>\\n",
                                "<p><span style=\"font-weight: 400;\">(314, 19, 16)</span></p>\\n", "<p><span style=\"font-weight: 400;\">(296, 21, 14)</span></p>\\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">2.(a)</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Logic :- </span><span style=\"font-family: Cambria Math;\">(2nd number &times; 3rd number) = 1st number</span></p>\\r\\n<p><span style=\"font-weight: 400;\">(162 , 9, 18)&nbsp; </span>:- (9 &times; 18) = 162</p>\\r\\n<p><span style=\"font-weight: 400;\">(231 , 11 ,21)&nbsp; </span>:- (11 &times; 21) = 231</p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Similarly,</span></p>\\r\\n<p><span style=\"font-weight: 400;\">(336, 28, 12)&nbsp; </span>:- (28 &times; 12) = 336</p>\\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">2.(a)</span></p>\\r\\n<p><span style=\"font-family: Nirmala UI;\">&#2340;&#2352;&#2381;&#2325;</span><span style=\"font-family: Cambria Math;\">:-</span><span style=\"font-family: Cambria Math;\"> (</span><span style=\"font-family: Nirmala UI;\">&#2342;&#2370;&#2360;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> &times; </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2368;&#2360;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\">) = </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2361;&#2354;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span></p>\\r\\n<p><span style=\"font-weight: 400;\">(162 , 9, 18) </span>:- (9 &times; 18) = 162</p>\\r\\n<p><span style=\"font-weight: 400;\">(231 , 11 ,21) </span>:- (11 &times; 21) = 231</p>\\r\\n<p><span style=\"font-family: Nirmala UI;\">&#2311;&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2325;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\">,</span></p>\\r\\n<p><span style=\"font-weight: 400;\">(336, 28, 12)&nbsp; </span>:- (28 &times; 12) = 336</p>\\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "6",
                    question_en: "<p>3. <span style=\"font-family: Cambria Math;\">Select the correct mirror image of the given figure when the mirror is placed at MN as shown below.</span></p>\\r\\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1701509074/word/media/image2.png\" width=\"152\" height=\"175\"></p>\\n",
                    question_hi: "<p>3. <span style=\"font-family: Nirmala UI;\">&#2342;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2325;&#2371;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2361;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2352;&#2381;&#2346;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2348;&#2367;&#2306;&#2348;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2330;&#2369;&#2344;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2368;&#2330;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2352;&#2381;&#2358;&#2366;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2344;&#2369;&#2360;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2352;&#2381;&#2346;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> MN </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2352;&#2326;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2344;&#2375;&#2327;&#2366;</span><span style=\"font-family: Cambria Math;\"> I</span></p>\\r\\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1701509074/word/media/image2.png\" width=\"150\" height=\"173\"></p>\\n",
                    options_en: ["<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1701509074/word/media/image3.png\" width=\"133\" height=\"34\"></p>\\n", "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1701509074/word/media/image4.png\" width=\"134\" height=\"40\"></p>\\n", 
                                "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1701509074/word/media/image5.png\" width=\"140\" height=\"43\"></p>\\n", "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1701509074/word/media/image6.png\" width=\"142\" height=\"42\"></p>\\n"],
                    options_hi: ["<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1701509074/word/media/image3.png\" width=\"133\" height=\"34\"></p>\\n", "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1701509074/word/media/image4.png\" width=\"134\" height=\"40\"></p>\\n",
                                "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1701509074/word/media/image5.png\" width=\"140\" height=\"43\"></p>\\n", "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1701509074/word/media/image6.png\" width=\"142\" height=\"42\"></p>\\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">3.(a)</span></p>\\r\\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1701509074/word/media/image3.png\" width=\"148\" height=\"38\"></p>\\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">3.(a)</span></p>\\r\\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1701509074/word/media/image3.png\" width=\"148\" height=\"38\"></p>\\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "6",
                    question_en: "<p>4. <span style=\"font-family: Cambria Math;\">125 is related to 50 in a certain way. Following the same logic, 230 is related to 92. To which of the following is 315 related, following the same logic ?</span></p>\\n",
                    question_hi: "<p>4. <span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2367;&#2358;&#2381;&#2330;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2352;&#2381;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2344;&#2369;&#2360;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> 125, 50 </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2348;&#2306;&#2343;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2352;&#2381;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2344;&#2369;&#2360;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\">, 230, 92 </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2348;&#2306;&#2343;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2352;&#2381;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2344;&#2369;&#2360;&#2352;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2340;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2369;&#2319;</span><span style=\"font-family: Cambria Math;\"> 315 </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2360;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2348;&#2306;&#2343;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\\n",
                    options_en: ["<p>145</p>\\n", "<p>135</p>\\n", 
                                "<p>120</p>\\n", "<p>126</p>\\n"],
                    options_hi: ["<p>145</p>\\n", "<p>135</p>\\n",
                                "<p>120</p>\\n", "<p>126</p>\\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">4.(d) Logic :- </span><span style=\"font-family: Cambria Math;\">(2nd number) &times; 2 + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mi>n</mi><mi>d</mi><mo>&nbsp;</mo><mi>n</mi><mi>u</mi><mi>m</mi><mi>b</mi><mi>e</mi><mi>r</mi></mrow><mn>2</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> = 1st number</span><span style=\"font-family: Cambria Math;\"> </span></p>\\r\\n<p><span style=\"font-weight: 400;\">(125 , 50)</span>:- (50) &times; 2 +<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>50</mn><mn>2</mn></mfrac></math> <span style=\"font-family: Cambria Math;\"> &rArr; 100 + 25 = 125</span></p>\\r\\n<p><span style=\"font-weight: 400;\">(230 , 92) </span>:- (92) &times; 2 + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>92</mn><mn>2</mn></mfrac></math><span style=\"font-family: Cambria Math;\"> &rArr; (184) + 46 = 230</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Similarly,</span></p>\\r\\n<p><span style=\"font-weight: 400;\">(315 , 126) </span>:- (126) &times; 2 + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>126</mn><mn>2</mn></mfrac></math><span style=\"font-family: Cambria Math;\"> &rArr; (252) + 63 = 315</span></p>\\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">4.(d)</span></p>\\r\\n<p><span style=\"font-family: Nirmala UI;\">&#2340;&#2352;&#2381;&#2325;</span><span style=\"font-family: Cambria Math;\"> :-</span><span style=\"font-family: Cambria Math;\"> (</span><span style=\"font-family: Nirmala UI;\">&#2342;&#2370;&#2360;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\">) &times; 2 + <math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2342;&#2370;&#2360;&#2352;&#2368;</mi><mo>&nbsp;</mo><mi>&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</mi></mrow><mn>2</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\">= </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2361;&#2354;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;</span><span style=\"font-family: Nirmala UI;\">&#2326;&#2381;&#2351;&#2366;</span></p>\\r\\n<p><span style=\"font-weight: 400;\">(125 , 50) </span>:- (50) &times; 2 + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>50</mn><mn>2</mn></mfrac></math><span style=\"font-family: Cambria Math;\"> &rArr; 100 + 25 = 125</span></p>\\r\\n<p><span style=\"font-weight: 400;\">(230 , 92) </span>:- (92) &times; 2 + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>92</mn><mn>2</mn></mfrac></math><span style=\"font-family: Cambria Math;\"> &rArr; (184) + 46 = 230</span></p>\\r\\n<p><span style=\"font-family: Nirmala UI;\">&#2311;&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2325;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\">,</span></p>\\r\\n<p><span style=\"font-weight: 400;\">(315 , 126)&nbsp; </span>:- (126) &times; 2 + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>126</mn><mn>2</mn></mfrac></math><span style=\"font-family: Cambria Math;\"> &rArr; (252) + 63 = 315</span></p>\\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "6",
                    question_en: "<p>5. <span style=\"font-family: Cambria Math;\">What should come in place of X in the given series?</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">4 11 25 46 74 X</span></p>\\n",
                    question_hi: "<p>5. <span style=\"font-family: Nirmala UI;\">&#2342;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2358;&#2381;&#2352;&#2371;&#2306;&#2326;&#2354;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> \'x\' </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2381;&#2341;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2330;&#2366;&#2361;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> ?</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">4 11 25 46 74 X</span></p>\\n",
                    options_en: ["<p>105</p>\\n", "<p>109</p>\\n", 
                                "<p>111</p>\\n", "<p>107</p>\\n"],
                    options_hi: ["<p>105</p>\\n", "<p>109</p>\\n",
                                "<p>111</p>\\n", "<p>107</p>\\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">5.(b)</span></p>\\r\\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1701509074/word/media/image7.png\" width=\"304\" height=\"79\"></p>\\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">5.(b)</span></p>\\r\\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1701509074/word/media/image7.png\" width=\"300\" height=\"78\"></p>\\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "6",
                    question_en: "<p>6. <span style=\"font-family: Cambria Math;\">In a certain code language,</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">A + B means \'A is the son of B\',</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">A &minus;</span><span style=\"font-family: Cambria Math;\"> B means \'A is the sister of B\',</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">A &times;</span><span style=\"font-family: Cambria Math;\"> B means \'A is the mother of B\',</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">A &divide;</span><span style=\"font-family: Cambria Math;\"> B means \'A is the brother of B\'.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Based on the above, how is P related to S if </span><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">\'P &times; </span><span style=\"font-weight: 400;\">Q &divide; </span><span style=\"font-weight: 400;\">R - T + S\'</span><span style=\"font-weight: 400;\"> ?</span></span></p>\\n",
                    question_hi: "<p>6. <span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2367;&#2358;&#2381;&#2330;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2370;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2349;&#2366;&#2359;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\">,</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">A + B </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2352;&#2381;&#2341;</span><span style=\"font-family: Cambria Math;\"> \'A, B </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2375;&#2335;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">\',</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">A - </span><span style=\"font-family: Cambria Math;\">B </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2352;&#2381;&#2341;</span><span style=\"font-family: Cambria Math;\"> \'A, B </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2361;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">\',</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">A &times; B </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2352;&#2381;&#2341;</span><span style=\"font-family: Cambria Math;\"> \'A, B </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">\',</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">A &divide; B </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2352;&#2381;&#2341;</span><span style=\"font-family: Cambria Math;\"> \'A, B </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2349;&#2366;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span></p>\\r\\n<p><span style=\"font-family: Nirmala UI;\">&#2313;&#2346;&#2352;&#2381;&#2351;&#2369;&#2325;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2343;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> \'<span style=\"font-weight: 400;\">P &times; </span><span style=\"font-weight: 400;\">Q &divide; </span><span style=\"font-weight: 400;\">R - T + S\'</span></span><span style=\"font-family: Cambria Math;\">&nbsp;</span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> P </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> S </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2348;&#2306;&#2343;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> <span style=\"font-weight: 400;\">?</span></span></p>\\n",
                    options_en: ["<p>Father\'s mother</p>\\n", "<p>Sister</p>\\n", 
                                "<p>Wife</p>\\n", "<p>Mother</p>\\n"],
                    options_hi: ["<p><span style=\"font-family: Nirmala UI;\">&#2346;&#2367;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span></p>\\n", "<p><span style=\"font-family: Nirmala UI;\">&#2348;&#2361;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span></p>\\n",
                                "<p><span style=\"font-family: Nirmala UI;\">&#2346;&#2340;&#2381;&#2344;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span></p>\\n", "<p><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2306;</span></p>\\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">6.(c)</span></p>\\r\\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1701509074/word/media/image8.png\" width=\"170\" height=\"99\"></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">P is the wife of S.</span></p>\\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">6.(c)</span></p>\\r\\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1701509074/word/media/image8.png\" width=\"164\" height=\"96\"></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">P, S </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2340;&#2381;&#2344;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> |</span></p>\\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "6",
                    question_en: "<p>7. <span style=\"font-family: Cambria Math;\">What will come in the place of &ldquo;?\" in the following equation, if \'+\' and &ldquo;-</span><span style=\"font-family: Cambria Math;\">&rdquo; are interchanged with &ldquo;&times;</span><span style=\"font-family: Cambria Math;\">&rdquo; and &ldquo;&divide;</span><span style=\"font-family: Cambria Math;\">&rdquo;, respectively ?</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">18 &times;</span><span style=\"font-weight: 400;\"> 17 + 4 &divide;</span><span style=\"font-weight: 400;\"> 22 - 2 = ?</span></span></p>\\n",
                    question_hi: "<p>7. <span style=\"font-family: Nirmala UI;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> \'+\' </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> &ldquo;-</span><span style=\"font-family: Cambria Math;\">&rdquo; </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2352;&#2350;&#2358;&#2307;</span><span style=\"font-family: Cambria Math;\"> &ldquo;&times;</span><span style=\"font-family: Cambria Math;\">&rdquo; </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> &ldquo;&divide;</span><span style=\"font-family: Cambria Math;\">&rdquo; </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2342;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2366;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2350;&#2368;&#2325;&#2352;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> (?) </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2381;&#2341;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2319;&#2327;&#2366;</span><span style=\"font-family: Cambria Math;\"> ?</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">18 &times;</span><span style=\"font-weight: 400;\"> 17 + 4 &divide;</span><span style=\"font-weight: 400;\"> 22 - 2 = ?</span></span></p>\\n",
                    options_en: ["<p>81</p>\\n", "<p>75</p>\\n", 
                                "<p>70</p>\\n", "<p>64</p>\\n"],
                    options_hi: ["<p>81</p>\\n", "<p>75</p>\\n",
                                "<p>70</p>\\n", "<p>64</p>\\n"],
                    solution_en: "<p>7.(b)</p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Given :- </span><span style=\"font-family: Cambria Math;\">18 &times; 17 + 4 &divide;</span><span style=\"font-family: Cambria Math;\"> 22 - 2 </span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">As per given instruction after interchanging &lsquo;+&rsquo; and &lsquo;-&rsquo; into &lsquo;&times;&rsquo; and &lsquo;&divide;</span><span style=\"font-family: Cambria Math;\">&rsquo; </span><span style=\"font-family: Cambria Math;\">respectively </span><span style=\"font-family: Cambria Math;\">we get,</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">18 + 17 &times; 4 - </span><span style=\"font-family: Cambria Math;\">22 &divide;</span><span style=\"font-family: Cambria Math;\"> 2</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">= 18 + 68 - 11 </span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">= 75</span></p>\\n",
                    solution_hi: "<p>7.(b)</p>\\r\\n<p><span style=\"font-family: Nirmala UI;\">&#2342;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">:-</span><span style=\"font-family: Cambria Math;\"> <span style=\"font-weight: 400;\">18 &times;</span><span style=\"font-weight: 400;\"> 17 + 4 &divide;</span><span style=\"font-weight: 400;\"> 22 - 2 = ?</span></span></p>\\r\\n<p><span style=\"font-family: Nirmala UI;\">&#2342;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2367;&#2352;&#2381;&#2342;&#2375;&#2358;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2344;&#2369;&#2360;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> \'+\' </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> \'-\' </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> \'&times;\' </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> \'<span style=\"font-weight: 400;\">&divide;</span></span><span style=\"font-family: Cambria Math;\">\' </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2342;&#2354;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2366;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2367;&#2354;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">,</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">18 + 17 &times; 4 - </span><span style=\"font-family: Cambria Math;\">22 <span style=\"font-weight: 400;\">&divide;</span></span><span style=\"font-family: Cambria Math;\"> 2</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">= 18 + 68 - 11 </span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">= 75</span></p>\\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "6",
                    question_en: "<p>8. <span style=\"font-family: Cambria Math;\">Select the term from among the given options that can replace the question mark (?) in the following series based on the English alphabetical order.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">ADE, CFH, EHK, GJN,?</span></p>\\n",
                    question_hi: "<p>8. <span style=\"font-family: Nirmala UI;\">&#2342;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2325;&#2354;&#2381;&#2346;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2330;&#2351;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;&#2332;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2306;&#2327;&#2381;&#2352;&#2375;&#2332;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2352;&#2381;&#2339;&#2350;&#2366;&#2354;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2352;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2343;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2358;&#2381;&#2352;&#2371;&#2306;&#2326;&#2354;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2358;&#2381;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2330;&#2367;&#2361;&#2381;&#2344;</span><span style=\"font-family: Cambria Math;\"> (?) </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2381;&#2341;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2325;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">ADE, CFH, EHK, GJN,?</span></p>\\n",
                    options_en: ["<p>IJK</p>\\n", "<p>HKQ</p>\\n", 
                                "<p>ILK</p>\\n", "<p>ILQ</p>\\n"],
                    options_hi: ["<p>IJK</p>\\n", "<p>HKQ</p>\\n",
                                "<p>ILK</p>\\n", "<p>ILQ</p>\\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">8.(d)</span></p>\\r\\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1701509074/word/media/image9.png\" width=\"303\" height=\"74\"></p>\\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">8.(d)</span></p>\\r\\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1701509074/word/media/image9.png\" width=\"302\" height=\"74\"></p>\\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "6",
                    question_en: "<p>9. <span style=\"font-family: Cambria Math;\">In a certain code language, \'FLORENCE\' is coded as \'86\' and \'GRATITUDE\' is coded as \'114&rsquo;. How is \'FOREIGN\' coded in the given language?</span></p>\\n",
                    question_hi: "<p>9. <span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2367;&#2358;&#2381;&#2330;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2370;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2349;&#2366;&#2359;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\">, FLORENCE\' </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> \'86\' </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2367;&#2326;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2366;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> \'GRATITUDE\' </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> \'114\' </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2367;&#2326;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2366;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2370;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2349;&#2366;&#2359;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> \'FOREIGN \' </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2376;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2367;&#2326;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2366;&#2319;&#2327;&#2366;</span><span style=\"font-family: Cambria Math;\">?</span></p>\\n",
                    options_en: ["<p>74</p>\\n", "<p>77</p>\\n", 
                                "<p>81</p>\\n", "<p>79</p>\\n"],
                    options_hi: ["<p>74</p>\\n", "<p>77</p>\\n",
                                "<p>81</p>\\n", "<p>79</p>\\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">9.(c)</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Logic :-</span><span style=\"font-family: Cambria Math;\"> (Place value of letters) + (Number of letters in words</span><span style=\"font-family: Cambria Math;\">)</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">FLORENCE :- (6 + 12 + 15 + 18 + 5 + 14 + 3 + 5) = (78) + (8) = 86</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">GRATITUDE :- (7 + 18 + 1 + 20 + 9 + 20 + 21 + 4 + 5) = (105) + (9) = 114</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Similarly,</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">FOREIGN :- (6 + 15 + 18 + 5 + 9 + 7 + 14) = (74) + (7) = 81</span></p>\\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">9.(c)</span></p>\\r\\n<p><span style=\"font-family: Nirmala UI;\">&#2340;&#2352;&#2381;&#2325;</span><span style=\"font-family: Cambria Math;\"> :-</span><span style=\"font-family: Cambria Math;\"> (</span><span style=\"font-family: Nirmala UI;\">&#2309;&#2325;&#2381;&#2359;&#2352;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2381;&#2341;&#2366;&#2344;&#2368;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\">) + (</span><span style=\"font-family: Nirmala UI;\">&#2358;&#2348;&#2381;&#2342;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2325;&#2381;&#2359;&#2352;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\">)</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">FLORENCE :- (6 + 12 + 15 + 18 + 5 + 14 + 3 + 5) = (78) + (8) = 86</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">GRATITUDE :- (7 + 18 + 1 + 20 + 9 + 20 + 21 + 4 + 5) = (105) + (9) = 114</span></p>\\r\\n<p><span style=\"font-family: Nirmala UI;\">&#2311;&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2325;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\">,</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">FOREIGN :- (6 + 15 + 18 + 5 + 9 + 7 + 14) = (74) + (7) = 81</span></p>\\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "6",
                    question_en: "<p>10. <span style=\"font-family: Cambria Math;\">What should come in place of ? in the given series based on the English alphabetical order ?</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">MXU QCA UHG YMM ?</span></p>\\n",
                    question_hi: "<p>10. <span style=\"font-family: Nirmala UI;\">&#2309;&#2306;&#2327;&#2381;&#2352;&#2375;&#2332;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2352;&#2381;&#2339;&#2350;&#2366;&#2354;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2352;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2343;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2358;&#2381;&#2352;&#2371;&#2306;&#2326;&#2354;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2358;&#2381;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2330;&#2367;&#2361;&#2381;&#2344;</span><span style=\"font-family: Cambria Math;\"> (?) </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2381;&#2341;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2330;&#2366;&#2361;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> ? </span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">MXU QCA UHG YMM ?</span></p>\\n",
                    options_en: ["<p>BQT</p>\\n", "<p>CRS</p>\\n", 
                                "<p>BRT</p>\\n", "<p>DUO</p>\\n"],
                    options_hi: ["<p>BQT</p>\\n", "<p>CRS</p>\\n",
                                "<p>BRT</p>\\n", "<p>DUO</p>\\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">10</span><span style=\"font-family: Cambria Math;\">.(b)</span></p>\\r\\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1701509074/word/media/image10.png\" width=\"301\" height=\"73\"></p>\\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">10</span><span style=\"font-family: Cambria Math;\">.(b)</span></p>\\r\\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1701509074/word/media/image10.png\" width=\"301\" height=\"73\"></p>\\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "6",
                    question_en: "<p>11. <span style=\"font-family: Cambria Math;\">What will come in the place of \"?\" in the following equation, if &ldquo;+&rdquo; and &ldquo;-</span><span style=\"font-family: Cambria Math;\">&rdquo; are interchanged and &ldquo;&times;</span><span style=\"font-family: Cambria Math;\">&rdquo; and &ldquo;&divide;</span><span style=\"font-family: Cambria Math;\">&rdquo; are interchanged?</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">15 &divide; </span><span style=\"font-weight: 400;\">5 + 12 &times; </span><span style=\"font-weight: 400;\">3 - 5= ?</span></span></p>\\n",
                    question_hi: "<p>11. <span style=\"font-family: Nirmala UI;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> &ldquo;+&rdquo; </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> &ldquo;-</span><span style=\"font-family: Cambria Math;\">&rdquo; </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2346;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2342;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2366;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> &ldquo;<span style=\"font-weight: 400;\">&times;</span></span><span style=\"font-family: Cambria Math;\">&rdquo; </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> &ldquo;<span style=\"font-weight: 400;\">&divide;</span></span><span style=\"font-family: Cambria Math;\">&rdquo; </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2346;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2342;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2366;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2350;&#2368;&#2325;&#2352;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> (?) </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2381;&#2341;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2319;&#2327;&#2366;</span><span style=\"font-family: Cambria Math;\">?</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">15 &divide; </span><span style=\"font-weight: 400;\">5 + 12 &times; </span><span style=\"font-weight: 400;\">3 - 5= ?</span></span></p>\\n",
                    options_en: ["<p>96</p>\\n", "<p>66</p>\\n", 
                                "<p>76</p>\\n", "<p>86</p>\\n"],
                    options_hi: ["<p>96</p>\\n", "<p>66</p>\\n",
                                "<p>76</p>\\n", "<p>86</p>\\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">11.(c)</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Given :- <span style=\"font-weight: 400;\">15 &divide; </span><span style=\"font-weight: 400;\">5 + 12 &times; </span><span style=\"font-weight: 400;\">3 - 5</span></span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">According to given instruction after interchanging &lsquo;+&rsquo; and &lsquo;-&rsquo; and &lsquo;&times;&rsquo; and &lsquo;<span style=\"font-weight: 400;\">&divide;</span></span><span style=\"font-family: Cambria Math;\">&rsquo; we get</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">15 &times; 5 - 12 <span style=\"font-weight: 400;\">&divide;</span></span><span style=\"font-family: Cambria Math;\"> 3 + 5</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">= 75 - 4 + 5 </span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">= 76</span></p>\\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">11.(c)</span></p>\\r\\n<p><span style=\"font-family: Nirmala UI;\">&#2342;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">:- <span style=\"font-weight: 400;\">15 &divide; </span><span style=\"font-weight: 400;\">5 + 12 &times; </span><span style=\"font-weight: 400;\">3 - 5</span></span></p>\\r\\n<p><span style=\"font-family: Nirmala UI;\">&#2342;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2367;&#2352;&#2381;&#2342;&#2375;&#2358;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2344;&#2369;&#2360;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> \'+\' </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> \'-\' </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2341;&#2366;</span><span style=\"font-family: Cambria Math;\"> \'&times;\' </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> \'<span style=\"font-weight: 400;\">&divide;</span></span><span style=\"font-family: Cambria Math;\">\' </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2346;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2342;&#2354;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2366;&#2346;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2379;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">15 &times; 5 - 12 <span style=\"font-weight: 400;\">&divide;</span></span><span style=\"font-family: Cambria Math;\"> 3 + 5</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">= 75 - 4 + 5 </span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">= 76</span></p>\\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "6",
                    question_en: "<p>12. <span style=\"font-family: Cambria Math;\">In a certain code language,</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">A + </span><span style=\"font-family: Cambria Math;\">B means \'A is the wife of B\',</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">A - </span><span style=\"font-family: Cambria Math;\">B means \'A is the brother of B\',</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">A &times;</span><span style=\"font-family: Cambria Math;\"> B means \'A is the sister of B\' and</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">A &divide;</span><span style=\"font-family: Cambria Math;\"> B means \'A is the father of B\'.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Based on the above, how is P related to N if \'</span><span style=\"font-weight: 400;\">P - B <span style=\"font-family: Cambria Math;\">&times;</span></span><span style=\"font-weight: 400;\"> V <span style=\"font-family: Cambria Math;\">&divide; </span></span><span style=\"font-weight: 400;\">N + E\' ?</span></p>\\n",
                    question_hi: "<p>12. <span style=\"font-weight: 400;\">&#2319;&#2325; &#2344;&#2367;&#2358;&#2381;&#2330;&#2367;&#2340; &#2325;&#2370;&#2335; &#2349;&#2366;&#2359;&#2366; &#2350;&#2375;&#2306;,</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">A + B </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2352;&#2381;&#2341;</span><span style=\"font-family: Cambria Math;\"> \'A, B </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2340;&#2381;&#2344;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">\',</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">A - </span><span style=\"font-family: Cambria Math;\">B </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2352;&#2381;&#2341;</span><span style=\"font-family: Cambria Math;\"> \'A, B </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2349;&#2366;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">\',</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">A &times; B </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2352;&#2381;&#2341;</span><span style=\"font-family: Cambria Math;\"> \'A, B </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2361;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Nirmala UI;\"><span style=\"font-family: Cambria Math;\">\'</span></span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">A &divide; B </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2352;&#2381;&#2341;</span><span style=\"font-family: Cambria Math;\"> \'A, B </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2367;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Nirmala UI;\"><span style=\"font-family: Cambria Math;\">\'</span></span></p>\\r\\n<p><span style=\"font-family: Nirmala UI;\">&#2313;&#2346;&#2352;&#2379;&#2325;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2343;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> \'<span style=\"font-weight: 400;\">P - B &times;</span><span style=\"font-weight: 400;\"> V &divide; </span><span style=\"font-weight: 400;\">N + E\'</span></span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> P </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> N </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2348;&#2306;&#2343;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\\n",
                    options_en: ["<p>Husband\'s son</p>\\n", "<p>Mother\'s brother</p>\\n", 
                                "<p>Father\'s brother</p>\\n", "<p>Husband\'s brother</p>\\n"],
                    options_hi: ["<p><span style=\"font-family: Nirmala UI;\">&#2346;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2375;&#2335;&#2366;</span></p>\\n", "<p><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2349;&#2366;&#2312;</span></p>\\n",
                                "<p><span style=\"font-family: Nirmala UI;\">&#2346;&#2367;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2349;&#2366;&#2312;</span></p>\\n", "<p><span style=\"font-family: Nirmala UI;\">&#2346;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2349;&#2366;&#2312;</span></p>\\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">12.(c)</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\"> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1701509074/word/media/image11.png\" width=\"203\" height=\"100\"></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">P is </span><span style=\"font-family: Cambria Math;\">Father\'s brother of N</span><span style=\"font-family: Cambria Math;\">.</span></p>\\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">12.(c)</span></p>\\r\\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1701509074/word/media/image11.png\" width=\"201\" height=\"99\"></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">P, N </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2367;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2349;&#2366;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span></p>\\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "6",
                    question_en: "<p>13. <span style=\"font-family: Cambria Math;\">Select the option that represents the combination of letters that, when sequentially placed from left to right in the blanks below, will complete the letter-series.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">_cdd_dd_ddc_</span></p>\\n",
                    question_hi: "<p>13. <span style=\"font-family: Nirmala UI;\">&#2313;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2325;&#2354;&#2381;&#2346;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2330;&#2369;&#2344;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2325;&#2381;&#2359;&#2352;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2351;&#2379;&#2332;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2352;&#2381;&#2358;&#2366;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2367;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2368;&#2330;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2352;&#2367;&#2325;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2381;&#2341;&#2366;&#2344;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2366;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2366;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2323;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2352;&#2350;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2352;&#2370;&#2346;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2352;&#2326;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2325;&#2381;&#2359;&#2352;</span><span style=\"font-family: Cambria Math;\">-</span><span style=\"font-family: Nirmala UI;\">&#2358;&#2381;&#2352;&#2371;&#2306;&#2326;&#2354;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2370;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2366;&#2319;&#2327;&#2368;&#2404;</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">_cdd_dd_ddc_</span></p>\\n",
                    options_en: ["<p>dccd</p>\\n", "<p>cddc</p>\\n", 
                                "<p>cdcd</p>\\n", "<p>dcdc</p>\\n"],
                    options_hi: ["<p>dccd</p>\\n", "<p>cddc</p>\\n",
                                "<p>cdcd</p>\\n", "<p>dcdc</p>\\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">13.(a)</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">The correct order is</span></p>\\r\\n<p><span style=\"text-decoration: underline;\"><strong><span style=\"font-family: Cambria Math;\">d</span></strong></span><span style=\"font-family: Cambria Math;\">cd / d</span><span style=\"text-decoration: underline;\"><strong><span style=\"font-family: Cambria Math;\">c</span></strong></span><span style=\"font-family: Cambria Math;\">d / d</span><span style=\"text-decoration: underline;\"><strong><span style=\"font-family: Cambria Math;\">c</span></strong></span><span style=\"font-family: Cambria Math;\">d / dc</span><span style=\"text-decoration: underline;\"><strong><span style=\"font-family: Cambria Math;\">d</span></strong></span></p>\\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">13.(a)</span></p>\\r\\n<p><span style=\"font-family: Nirmala UI;\">&#2360;&#2361;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2352;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span></p>\\r\\n<p><span style=\"text-decoration: underline;\"><strong><span style=\"font-family: Cambria Math;\">d</span></strong></span><span style=\"font-family: Cambria Math;\">cd / d</span><span style=\"text-decoration: underline;\"><strong><span style=\"font-family: Cambria Math;\">c</span></strong></span><span style=\"font-family: Cambria Math;\">d / d</span><span style=\"text-decoration: underline;\"><strong><span style=\"font-family: Cambria Math;\">c</span></strong></span><span style=\"font-family: Cambria Math;\">d / dc</span><span style=\"text-decoration: underline;\"><strong><span style=\"font-family: Cambria Math;\">d</span></strong></span></p>\\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "6",
                    question_en: "<p>14. <span style=\"font-family: Cambria Math;\">Identify the figure given in the options which when put in place of the question mark (?) will logically complete the series.</span></p>\\r\\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1701509074/word/media/image12.png\" width=\"357\" height=\"80\"></p>\\n",
                    question_hi: "<p>14. <span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2325;&#2354;&#2381;&#2346;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2325;&#2371;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2361;&#2330;&#2366;&#2344;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2367;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2358;&#2381;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2330;&#2367;&#2361;&#2381;&#2344;</span><span style=\"font-family: Cambria Math;\"> (?) </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2381;&#2341;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2352;&#2326;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2358;&#2381;&#2352;&#2371;&#2306;&#2326;&#2354;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2366;&#2352;&#2381;&#2325;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2352;&#2370;&#2346;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2370;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2366;&#2319;&#2327;&#2368;&#2404;</span></p>\\r\\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1701509074/word/media/image12.png\" width=\"357\" height=\"80\"></p>\\n",
                    options_en: ["<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1701509074/word/media/image13.png\" width=\"101\" height=\"101\"></p>\\n", "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1701509074/word/media/image14.png\" width=\"100\" height=\"105\"></p>\\n", 
                                "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1701509074/word/media/image15.png\" width=\"100\" height=\"98\"></p>\\n", "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1701509074/word/media/image16.png\" width=\"101\" height=\"96\"></p>\\n"],
                    options_hi: ["<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1701509074/word/media/image13.png\" width=\"101\" height=\"101\"></p>\\n", "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1701509074/word/media/image14.png\" width=\"100\" height=\"105\"></p>\\n",
                                "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1701509074/word/media/image15.png\" width=\"100\" height=\"98\"></p>\\n", "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1701509074/word/media/image16.png\" width=\"101\" height=\"96\"></p>\\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">14.(a)</span></p>\\r\\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1701509074/word/media/image13.png\" width=\"101\" height=\"101\"></p>\\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">14.(a)</span></p>\\r\\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1701509074/word/media/image13.png\" width=\"101\" height=\"101\"></p>\\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "6",
                    question_en: "<p>15. <span style=\"font-family: Cambria Math;\">In a certain code language, \'all kings are beautiful\' is coded as \'42 63 mo lue\' and \'all queens are beautiful&rsquo; is coded as \'mo lue 63 36&rsquo;. How is \'kings\' coded in the given language ?</span></p>\\n",
                    question_hi: "<p>15. <span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2367;&#2358;&#2381;&#2330;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2370;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2349;&#2366;&#2359;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\">, &lsquo;all </span><span style=\"font-family: Cambria Math;\">kings are beautiful\' </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> \'42 63 mo lue\' </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2367;&#2326;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2366;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> \'all queens are beautiful\' </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> \'</span><span style=\"font-family: Cambria Math;\">mo lue</span><span style=\"font-family: Cambria Math;\"> 63 36\' </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2367;&#2326;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2366;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2370;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2349;&#2366;&#2359;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> \'kings\' </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2376;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2367;&#2326;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2366;&#2319;&#2327;&#2366;</span><span style=\"font-family: Cambria Math;\"> ?</span></p>\\n",
                    options_en: ["<p>63</p>\\n", "<p>24</p>\\n", 
                                "<p>lue</p>\\n", "<p>42</p>\\n"],
                    options_hi: ["<p>63</p>\\n", "<p>24</p>\\n",
                                "<p>lue</p>\\n", "<p>42</p>\\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">15.(d)</span></p>\\r\\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1701509074/word/media/image17.png\" width=\"350\" height=\"85\"><span style=\"font-family: Cambria Math;\"> </span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">The code of kings = 42.</span></p>\\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">15.(d)</span></p>\\r\\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1701509074/word/media/image17.png\" width=\"350\" height=\"85\"></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">kings</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;&#2337;</span><span style=\"font-family: Cambria Math;\"> = 42.</span></p>\\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "6",
                    question_en: "<p>16. <span style=\"font-family: Cambria Math;\">Read the given statements and conclusions carefully. Assuming that the information given in the statements is true, even if it appears to be at variance with commonly known facts, decide which of the given conclusions logically f</span><span style=\"font-family: Cambria Math;\">ollow(s) from the statements.</span></p>\\r\\n<p><strong><span style=\"font-family: Cambria Math;\">Statements:</span></strong></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Some keys are scales. </span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">All books are papers. </span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">All scales are papers.</span></p>\\r\\n<p><strong><span style=\"font-family: Cambria Math;\">Conclusions:</span></strong></p>\\r\\n<p><span style=\"font-weight: 400;\">(I) No books are keys.</span></p>\\r\\n<p><span style=\"font-weight: 400;\">(II) Some papers are keys.</span></p>\\n",
                    question_hi: "<p>16. <span style=\"font-family: Nirmala UI;\">&#2344;&#2368;&#2330;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2341;&#2344;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2367;&#2359;&#2381;&#2325;&#2352;&#2381;&#2359;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2343;&#2381;&#2351;&#2366;&#2344;&#2346;&#2370;&#2352;&#2381;&#2357;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2397;&#2367;&#2319;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2346;&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2344;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2341;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2340;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2330;&#2366;&#2361;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2350;&#2366;&#2344;&#2381;&#2351;&#2340;&#2307;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2381;&#2334;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2341;&#2381;&#2351;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2354;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2340;&#2368;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2379;&#2340;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2379;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2346;&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2367;&#2358;&#2381;&#2330;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2380;&#2344;</span><span style=\"font-family: Cambria Math;\">-</span><span style=\"font-family: Nirmala UI;\">&#2360;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2380;&#2344;</span><span style=\"font-family: Cambria Math;\">-</span><span style=\"font-family: Nirmala UI;\">&#2360;</span><span style=\"font-family: Nirmala UI;\">&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2367;&#2359;&#2381;&#2325;&#2352;&#2381;&#2359;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2366;&#2352;&#2381;&#2325;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2352;&#2370;&#2346;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2341;&#2344;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2344;&#2369;&#2360;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">/</span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2306;&#2404;</span></p>\\r\\n<p><strong><span style=\"font-family: Nirmala UI;\">&#2325;&#2341;&#2344;</span><span style=\"font-family: Cambria Math;\">:</span></strong></p>\\r\\n<p><span style=\"font-family: Nirmala UI;\">&#2325;&#2369;&#2331;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2330;&#2366;&#2348;&#2367;&#2351;&#2366;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2381;&#2325;&#2375;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span></p>\\r\\n<p><span style=\"font-family: Nirmala UI;\">&#2360;&#2349;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2340;&#2366;&#2348;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2375;&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2306;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span></p>\\r\\n<p><span style=\"font-family: Nirmala UI;\">&#2360;&#2349;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2381;&#2325;&#2375;&#2354;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2375;&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span></p>\\r\\n<p><strong><span style=\"font-family: Nirmala UI;\">&#2344;&#2367;&#2359;&#2381;&#2325;&#2352;&#2381;&#2359;</span><span style=\"font-family: Cambria Math;\">:</span></strong></p>\\r\\n<p><span style=\"font-weight: 400;\">(I) &#2325;&#2379;&#2312; &#2325;&#2367;&#2340;&#2366;&#2348;&#2375;&#2306;, &#2330;&#2366;&#2348;&#2367;&#2351;&#2366;&#2306; &#2344;&#2361;&#2368;&#2306; &#2361;&#2376;&#2404;&nbsp;</span></p>\\r\\n<p><span style=\"font-weight: 400;\">(II) &#2325;&#2369;&#2331; &#2346;&#2375;&#2346;&#2352;, &#2330;&#2366;&#2348;&#2367;&#2351;&#2366;&#2306; &#2361;&#2376;&#2306;&#2404;</span></p>\\n",
                    options_en: ["<p>Both conclusions (I) and (II) follow</p>\\n", "<p>Only conclusion (I) follows</p>\\n", 
                                "<p>On<span style=\"font-family: Cambria Math;\">ly conclusion (II) follows</span></p>\\n", "<p>Neither conclusion(I) nor (II) follows</p>\\n"],
                    options_hi: ["<p><span style=\"font-family: Nirmala UI;\">&#2342;&#2379;&#2344;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2367;&#2359;&#2381;&#2325;&#2352;&#2381;&#2359;</span><span style=\"font-family: Cambria Math;\"> (I) </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> (II) </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2341;&#2344;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2344;&#2369;&#2360;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2306;</span><span style=\"font-family: Cambria Math;\">I</span></p>\\n", "<p><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;&#2357;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2367;&#2359;&#2381;&#2325;&#2352;&#2381;&#2359;</span><span style=\"font-family: Cambria Math;\"> (I) </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2341;&#2344;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2344;&#2369;&#2360;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">I</span></p>\\n",
                                "<p><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;&#2357;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2367;&#2359;&#2381;&#2325;&#2352;&#2381;&#2359;</span><span style=\"font-family: Cambria Math;\"> (II) </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2341;&#2344;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2344;&#2369;&#2360;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">I</span></p>\\n", "<p><span style=\"font-family: Nirmala UI;\">&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2367;&#2359;&#2381;&#2325;&#2352;&#2381;&#2359;</span><span style=\"font-family: Cambria Math;\"> (I) </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2368;</span><span style=\"font-family: Cambria Math;\"> (II) </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2341;&#2344;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2344;&#2369;&#2360;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> I</span></p>\\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">16.(c)</span></p>\\r\\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1701509074/word/media/image18.png\" width=\"203\" height=\"81\"></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Only conclusion II follows.</span></p>\\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">16.(c)</span></p>\\r\\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1701509074/word/media/image19.png\" width=\"226\" height=\"78\"></p>\\r\\n<p><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;&#2357;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2367;&#2359;&#2381;&#2325;&#2352;&#2381;&#2359;</span><span style=\"font-family: Cambria Math;\"> II </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2344;&#2369;&#2360;&#2352;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span></p>\\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "6",
                    question_en: "<p>17. <span style=\"font-family: Cambria Math;\">Which t</span><span style=\"font-family: Cambria Math;\">erm from among the given options can replace the question mark (?) in the following series to make it logically complete ?</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">BV 101, ?, DX 109, EY 113, FZ 117</span></p>\\n",
                    question_hi: "<p>17. <span style=\"font-family: Nirmala UI;\">&#2342;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2325;&#2354;&#2381;&#2346;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2380;&#2344;</span><span style=\"font-family: Cambria Math;\">-</span><span style=\"font-family: Nirmala UI;\">&#2360;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2342;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2358;&#2381;&#2352;&#2371;&#2306;&#2326;&#2354;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2366;&#2352;&#2381;&#2325;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2352;&#2370;&#2346;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2370;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2358;&#2381;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2330;&#2367;&#2361;&#2381;&#2344;</span><span style=\"font-family: Cambria Math;\"> (?) </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2381;&#2341;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2319;&#2327;&#2366;</span><span style=\"font-family: Cambria Math;\"> ?</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">BV 101, ?, DX 109, EY 113, FZ 117</span></p>\\n",
                    options_en: ["<p>CW 105</p>\\n", "<p>CV 104</p>\\n", 
                                "<p>CW 106</p>\\n", "<p>CV 107</p>\\n"],
                    options_hi: ["<p>CW 105</p>\\n", "<p>CV 104</p>\\n",
                                "<p>CW 106</p>\\n", "<p>CV 107</p>\\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">17.(a)</span></p>\\r\\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1701509074/word/media/image20.png\" width=\"350\" height=\"61\"></p>\\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">17.(a)</span></p>\\r\\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1701509074/word/media/image20.png\" width=\"350\" height=\"61\"></p>\\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "6",
                    question_en: "<p>18. <span style=\"font-family: Cambria Math;\">Select the option in which the numbers do NOT share the same relationship as that shared by the given pairs of numbers.</span></p>\\r\\n<p><span style=\"font-weight: 400;\">(NOTE: Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g. 13- Operations on 13 such as adding/deleting/multiplying etc. to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed.)&nbsp;</span></p>\\r\\n<p><span style=\"font-weight: 400;\">4 : 75 and 2 :19&nbsp;</span></p>\\n",
                    question_hi: "<p>18<span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2325;&#2354;&#2381;&#2346;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2330;&#2369;&#2344;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2367;&#2360;&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;&#2319;&#2305;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2361;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2348;&#2306;&#2343;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2366;&#2333;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2361;&#2368;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;&#2323;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2369;&#2327;&#2381;&#2350;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2366;&#2333;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2366;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span></p>\\r\\n<p><span style=\"font-weight: 400;\">(&#2343;&#2381;&#2351;&#2366;&#2344; &#2342;&#2375;&#2306;: &#2360;&#2306;&#2326;&#2381;&#2351;&#2366;&#2323;&#2306; &#2325;&#2379; &#2313;&#2344;&#2325;&#2375; &#2328;&#2335;&#2325; &#2309;&#2306;&#2325;&#2379;&#2306; &#2350;&#2375;&#2306; &#2309;&#2354;&#2327;-&#2309;&#2354;&#2327; &#2325;&#2367;&#2319; &#2348;&#2367;&#2344;&#2366;, &#2346;&#2370;&#2352;&#2381;&#2339; &#2360;&#2306;&#2326;&#2381;&#2351;&#2366;&#2323;&#2306; &#2346;&#2352; &#2360;&#2325;&#2381;&#2352;&#2367;&#2351;&#2366;&#2319;&#2305; &#2325;&#2368; &#2332;&#2366;&#2344;&#2368; &#2330;&#2366;&#2361;&#2367;&#2319;&#2404; &#2313;&#2342;&#2366;. 13- &#2360;&#2306;&#2326;&#2381;&#2351;&#2366; 13 &#2346;&#2352; &#2360;&#2306;&#2325;&#2381;&#2352;&#2367;&#2351;&#2366;&#2319;&#2305; &#2332;&#2376;&#2360;&#2375; 13 &#2325;&#2379; &#2332;&#2379;&#2337;&#2364;&#2344;&#2366;/&#2328;&#2335;&#2366;&#2344;&#2366;/&#2327;&#2369;&#2339;&#2366; &#2325;&#2352;&#2344;&#2366; &#2310;&#2342;&#2367; &#2325;&#2367;&#2351;&#2366; &#2332;&#2366; &#2360;&#2325;&#2340;&#2366; &#2361;&#2376;&#2404; 13 &#2325;&#2379; 1 &#2324;&#2352; 3 &#2350;&#2375;&#2306; &#2309;&#2354;&#2327;-&#2309;&#2354;&#2327; &#2325;&#2352;&#2344;&#2375; &#2325;&#2368; &#2324;&#2352; &#2347;&#2367;&#2352; &#2324;&#2352; 3 &#2346;&#2352; &#2327;&#2339;&#2367;&#2340;&#2368;&#2351; &#2360;&#2306;&#2325;&#2381;&#2352;&#2367;&#2351;&#2366;&#2319;&#2305; &#2325;&#2352;&#2344;&#2375; &#2325;&#2368; &#2309;&#2344;&#2369;&#2350;&#2340;&#2367; &#2344;&#2361;&#2368;&#2306; &#2361;&#2376;&#2404;)</span></p>\\r\\n<p><span style=\"font-weight: 400;\">4 : 75 &#2324;&#2352; 2 :19</span></p>\\n",
                    options_en: ["<p>3 : 38</p>\\n", "<p>7 : 354</p>\\n", 
                                "<p>6 : 212</p>\\n", "<p>5 : 136</p>\\n"],
                    options_hi: ["<p>3 : 38</p>\\n", "<p>7 : 354</p>\\n",
                                "<p>6 : 212</p>\\n", "<p>5 : 136</p>\\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">18.(c)</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Logic :-</span><span style=\"font-family: Cambria Math;\"> (1st number)&sup3;</span><span style=\"font-family: Cambria Math;\">&nbsp;+ 11 = 2nd number</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">4 : 75 :- (4)&sup3;</span><span style=\"font-family: Cambria Math;\">&nbsp;+ 11&rArr; 64 + 11 = 75</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">2 : 19 :- (2)&sup3;</span><span style=\"font-family: Cambria Math;\">&nbsp;+ 11&rArr; 8 + 11 = 19</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">But,</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">6 : 212 :- (6)&sup3;</span><span style=\"font-family: Cambria Math;\">&nbsp;+ 11&rArr; 216 + 11 = 227 (Not 212)</span></p>\\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">18.(c)</span></p>\\r\\n<p><span style=\"font-family: Nirmala UI;\">&#2340;&#2352;&#2381;&#2325;</span><span style=\"font-family: Cambria Math;\"> :- </span><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Nirmala UI;\">&#2346;&#2361;&#2354;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\">)&sup3;</span><span style=\"font-family: Cambria Math;\">&nbsp;+ 11 =</span><span style=\"font-family: Nirmala UI;\">&#2342;&#2370;&#2360;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">4 : 75 :- (4)&sup3;</span><span style=\"font-family: Cambria Math;\">&nbsp;+ 11&rArr; 64 + 11 = 75</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">2 : 19 :- (2)&sup3;</span><span style=\"font-family: Cambria Math;\">+ 11&rArr; 8 + 11 = 19</span></p>\\r\\n<p><span style=\"font-family: Nirmala UI;\">&#2354;&#2375;&#2325;&#2367;&#2344;</span><span style=\"font-family: Cambria Math;\">,</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">6 : 212 :- (6)&sup3;</span><span style=\"font-family: Cambria Math;\">&nbsp;+ 11 &rArr; 216 + 11 = 227 (212 </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2361;&#2368;&#2306;</span><span style=\"font-family: Cambria Math;\">)</span></p>\\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "6",
                    question_en: "<p>19. <span style=\"font-family: Cambria Math;\">Which of the following letter-clusters will replace the question mark (?) in the given series to make it logically complete?</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">MY 27, KZ 64, ?, GB 216, EC 343</span></p>\\n",
                    question_hi: "<p>19. <span style=\"font-family: Nirmala UI;\">&#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2380;&#2344;</span><span style=\"font-family: Cambria Math;\">-</span><span style=\"font-family: Nirmala UI;\">&#2360;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2325;&#2381;&#2359;&#2352;</span><span style=\"font-family: Cambria Math;\">-</span><span style=\"font-family: Nirmala UI;\">&#2360;&#2350;&#2370;&#2361;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2358;&#2381;&#2352;</span><span style=\"font-family: Nirmala UI;\">&#2371;&#2306;&#2326;&#2354;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2366;&#2352;&#2381;&#2325;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2352;&#2370;&#2346;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2370;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2358;&#2381;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2330;&#2367;&#2361;&#2381;&#2344;</span><span style=\"font-family: Cambria Math;\"> (?) </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2381;&#2341;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2319;&#2327;&#2366;</span><span style=\"font-family: Cambria Math;\"> ?</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">MY 27, KZ 64, ?, GB 216, EC 343</span></p>\\n",
                    options_en: ["<p>JB 81</p>\\n", "<p>JA 81</p>\\n", 
                                "<p>IA 125</p>\\n", "<p>HB 100</p>\\n"],
                    options_hi: ["<p>JB 81</p>\\n", "<p>JA 81</p>\\n",
                                "<p>IA 125</p>\\n", "<p>HB 100</p>\\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">19.(c)</span></p>\\r\\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1701509074/word/media/image21.png\" width=\"309\" height=\"68\"></p>\\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">19.(c)</span></p>\\r\\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1701509074/word/media/image21.png\" width=\"301\" height=\"66\"></p>\\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "6",
                    question_en: "<p>20. <span style=\"font-family: Cambria Math;\">If 6 November 1925 is Friday, then what will be the day of the week on 16 December 1933?</span></p>\\n",
                    question_hi: "<p>20. <span style=\"font-family: Nirmala UI;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> 6 </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2357;&#2306;&#2348;&#2352;</span><span style=\"font-family: Cambria Math;\"> 1925 </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2358;&#2369;&#2325;&#2381;&#2352;&#2357;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> 16 </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2367;&#2360;&#2306;&#2348;&#2352;</span><span style=\"font-family: Cambria Math;\"> 1933 </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2346;&#2381;&#2340;&#2366;&#2361;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2380;&#2344;</span><span style=\"font-family: Cambria Math;\"> - </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2367;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2379;&#2327;&#2366;</span><span style=\"font-family: Cambria Math;\">?</span></p>\\n",
                    options_en: ["<p>Monday</p>\\n", "<p>Friday</p>\\n", 
                                "<p>Thursday</p>\\n", "<p>Saturday</p>\\n"],
                    options_hi: ["<p><span style=\"font-family: Nirmala UI;\">&#2360;&#2379;&#2350;&#2357;&#2366;&#2352;</span></p>\\n", "<p><span style=\"font-family: Nirmala UI;\">&#2358;&#2369;&#2325;&#2381;&#2352;&#2357;&#2366;</span><span style=\"font-family: Nirmala UI;\">&#2352;</span></p>\\n",
                                "<p><span style=\"font-family: Nirmala UI;\">&#2327;&#2369;&#2352;&#2369;&#2357;&#2366;&#2352;</span></p>\\n", "<p><span style=\"font-family: Nirmala UI;\">&#2358;&#2344;&#2367;&#2357;&#2366;&#2352;</span></p>\\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">20</span><span style=\"font-family: Cambria Math;\">.(d)</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Total number of odd days From 6 November 1925 to 6 November 1933 = </span><span style=\"font-family: Cambria Math;\">1 + 1 + 2 + 1 + 1 + 1 + 2 + 1 = 10 odd days</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Number of odd days From 6 November 1933 to 16 december 1933 =</span><span style=\"font-family: Cambria Math;\">24 + 16 = 40 odd days </span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Total numb</span><span style=\"font-family: Cambria Math;\">er of odd days = 10 + 40 = 50 odd days (50 &divide; 7) = 1 odd days</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">&rArr;Friday + 1 = Saturday</span></p>\\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">20</span><span style=\"font-family: Cambria Math;\">.(d)</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">6 </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2357;&#2306;&#2348;&#2352;</span><span style=\"font-family: Cambria Math;\"> 1925 </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> 6 </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2357;&#2306;&#2348;&#2352;</span><span style=\"font-family: Cambria Math;\"> 1933 </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2369;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2359;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2367;&#2344;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> = </span><span style=\"font-family: Cambria Math;\">1 + 1 + 2 + 1 + 1 + 1 + 2 + 1 = 10 </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2359;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2367;&#2344;</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">6 </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2357;&#2350;&#2381;&#2348;&#2352;</span><span style=\"font-family: Cambria Math;\"> 1933 </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> 16 </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2367;&#2360;&#2350;&#2381;&#2348;&#2352;</span><span style=\"font-family: Cambria Math;\"> 1933 </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2359;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2367;&#2344;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> = </span><span style=\"font-family: Cambria Math;\">24 + 16 = 40 </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2359;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2367;&#2344;</span></p>\\r\\n<p><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2359;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2367;&#2344;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2369;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> = 10 + 40 = 50 </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2359;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2367;&#2344;</span><span style=\"font-family: Cambria Math;\"> (50 &divide; 7) = 1 </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2359;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2367;&#2344;</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">&rArr; </span><span style=\"font-family: Nirmala UI;\">&#2358;&#2369;&#2325;&#2381;&#2352;&#2357;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> + 1 = </span><span style=\"font-family: Nirmala UI;\">&#2358;&#2344;&#2367;&#2357;&#2366;&#2352;</span></p>\\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "6",
                    question_en: "<p>21. <span style=\"font-family: Cambria Math;\">UOKG is related to VPLH in a certain way based on the English alphabe</span><span style=\"font-family: Cambria Math;\">tical order. In the same way, KQWY is related to LRXZ. To which of the following is AMSW related to following the same logic ?</span></p>\\n",
                    question_hi: "<p>21. <span style=\"font-family: Nirmala UI;\">&#2309;&#2306;&#2327;&#2381;&#2352;&#2375;&#2332;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2352;&#2381;&#2339;&#2350;&#2366;&#2354;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2352;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2325;&#2381;&#2359;&#2352;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2368;&#2330;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2367;&#2358;&#2381;&#2330;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2348;&#2306;&#2343;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2343;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> UOKG</span><span style=\"font-family: Cambria Math;\">, VPLH </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2367;&#2358;&#2381;&#2330;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2325;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2348;&#2306;&#2343;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2325;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> KQWY, LRXZ </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2348;&#2306;&#2343;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2352;&#2381;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2344;&#2369;&#2360;&#2352;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2340;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2369;&#2319;</span><span style=\"font-family: Cambria Math;\">, AMSW </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2360;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2348;&#2306;&#2343;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> ?</span></p>\\n",
                    options_en: ["<p>BNTX</p>\\n", "<p>WKTC</p>\\n", 
                                "<p>BOTY</p>\\n", "<p>ZMRW</p>\\n"],
                    options_hi: ["<p>BNTX</p>\\n", "<p>WKTC</p>\\n",
                                "<p>BOTY</p>\\n", "<p>ZMRW</p>\\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">21</span><span style=\"font-family: Cambria Math;\">.(a)</span></p>\\r\\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1701509074/word/media/image22.png\" width=\"112\" height=\"100\"><span style=\"font-family: Cambria Math;\"> , </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1701509074/word/media/image23.png\" width=\"112\" height=\"101\"><span style=\"font-family: Cambria Math;\"> </span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Similarly, </span></p>\\r\\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1701509074/word/media/image24.png\" width=\"108\" height=\"96\"></p>\\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">21</span><span style=\"font-family: Cambria Math;\">.(a)</span></p>\\r\\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1701509074/word/media/image22.png\" width=\"112\" height=\"100\"><span style=\"font-family: Cambria Math;\"> , </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1701509074/word/media/image23.png\" width=\"112\" height=\"101\"><span style=\"font-family: Cambria Math;\"> </span></p>\\r\\n<p><span style=\"font-family: Nirmala UI;\">&#2311;&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2325;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\">,&nbsp;</span></p>\\r\\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1701509074/word/media/image24.png\" width=\"108\" height=\"96\"></p>\\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "6",
                    question_en: "<p>22. <span style=\"font-family: Cambria Math;\">Four number pairs have been given, out of which three are alike in some manner and one is different. Select the one that is different.</span></p>\\n",
                    question_hi: "<p>22. <span style=\"font-family: Nirmala UI;\">&#2344;&#2368;&#2330;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2330;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> -</span><span style=\"font-family: Nirmala UI;\">&#2351;&#2369;&#2327;&#2381;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2306;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2367;&#2344;&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2368;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2325;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2349;&#2367;&#2344;&#2381;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\">-</span><span style=\"font-family: Nirmala UI;\">&#2351;&#2369;&#2327;&#2381;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2330;&#2369;&#2344;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2349;&#2367;&#2344;&#2381;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span></p>\\n",
                    options_en: ["<p>15-225</p>\\n", "<p>19-361</p>\\n", 
                                "<p>9-81</p>\\n", "<p>4-64</p>\\n"],
                    options_hi: ["<p>15-225</p>\\n", "<p>19-361</p>\\n",
                                "<p>9-81</p>\\n", "<p>4-64</p>\\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">22</span><span style=\"font-family: Cambria Math;\">.(d)</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Logic :-</span><span style=\"font-family: Cambria Math;\"> (1st number)&sup2;</span><span style=\"font-family: Cambria Math;\">&nbsp;= (2nd number)</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">15: 225 :- (15)&sup2;</span><span style=\"font-family: Cambria Math;\">&nbsp;= 225</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">19 : 361 :- (19)&sup2;</span><span style=\"font-family: Cambria Math;\">&nbsp;= 361</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">9 : 81 :- (9)&sup2;</span><span style=\"font-family: Cambria Math;\">&nbsp;= 81</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">But,</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">4 : 64 :- (4)&sup3;</span><span style=\"font-family: Cambria Math;\">&nbsp;= 64</span></p>\\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">22</span><span style=\"font-family: Cambria Math;\">.(d)</span></p>\\r\\n<p><span style=\"font-family: Nirmala UI;\">&#2340;&#2352;&#2381;&#2325;</span><span style=\"font-family: Cambria Math;\"> :-</span><span style=\"font-family: Cambria Math;\"> (</span><span style=\"font-family: Nirmala UI;\">&#2346;&#2361;&#2354;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\">)&sup2;</span><span style=\"font-family: Cambria Math;\">&nbsp;= (</span><span style=\"font-family: Nirmala UI;\">&#2342;&#2370;&#2360;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\">)</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">15: 225 :- (15)&sup2;</span><span style=\"font-family: Cambria Math;\">&nbsp;= 225</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">19 : 361 :- (19)&sup2;</span><span style=\"font-family: Cambria Math;\">= 361</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">9 : 81 :- (9)&sup2;</span><span style=\"font-family: Cambria Math;\">&nbsp;= 81</span></p>\\r\\n<p><span style=\"font-family: Nirmala UI;\">&#2354;&#2375;&#2325;&#2367;&#2344;</span><span style=\"font-family: Cambria Math;\">,</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">4 : 64 :- (4)&sup3;</span><span style=\"font-family: Cambria Math;\">&nbsp;= 64</span></p>\\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "23",
                    section: "6",
                    question_en: "<p>23. <span style=\"font-family: Cambria Math;\">Select the correct mirror image of the given figure when the mirror is placed at MN as shown .</span></p>\\r\\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1701509074/word/media/image25.png\" width=\"101\" height=\"126\"></p>\\n",
                    question_hi: "<p>23. <span style=\"font-family: Nirmala UI;\">&#2342;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2325;&#2371;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2361;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2352;&#2381;&#2346;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2348;&#2367;&#2306;&#2348;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2330;&#2369;&#2344;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2368;&#2330;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2352;&#2381;&#2358;&#2366;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2344;&#2369;&#2360;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2352;&#2381;&#2346;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> MN </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2352;&#2326;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2344;&#2375;&#2327;&#2366;&#2404;</span></p>\\r\\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1701509074/word/media/image25.png\" width=\"101\" height=\"126\"></p>\\n",
                    options_en: ["<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1701509074/word/media/image26.png\" width=\"102\" height=\"111\"></p>\\n", "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1701509074/word/media/image27.png\" width=\"100\" height=\"114\"></p>\\n", 
                                "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1701509074/word/media/image28.png\" width=\"101\" height=\"127\"></p>\\n", "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1701509074/word/media/image29.png\" width=\"100\" height=\"124\"></p>\\n"],
                    options_hi: ["<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1701509074/word/media/image26.png\" width=\"102\" height=\"111\"></p>\\n", "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1701509074/word/media/image27.png\" width=\"100\" height=\"114\"></p>\\n",
                                "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1701509074/word/media/image28.png\" width=\"101\" height=\"127\"></p>\\n", "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1701509074/word/media/image29.png\" width=\"100\" height=\"124\"></p>\\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">23</span><span style=\"font-family: Cambria Math;\">.(d)</span></p>\\r\\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1701509074/word/media/image29.png\" width=\"102\" height=\"127\"></p>\\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">23.</span><span style=\"font-family: Cambria Math;\">(d)</span></p>\\r\\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1701509074/word/media/image29.png\" width=\"100\" height=\"124\"></p>\\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "24",
                    section: "6",
                    question_en: "<p>24. <span style=\"font-family: Cambria Math;\">Read the given statements and conclusions carefully. Assuming that the information given in the statements is true, even if it appears to be at variance with commonly known facts, decide which of </span><span style=\"font-family: Cambria Math;\">the given conclusions logically follow(s) from the statements </span></p>\\r\\n<p><strong><span style=\"font-family: Cambria Math;\">Statements:</span></strong></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Some pencils are cartoons</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">All cartoons are rivers.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">All rivers are shadows.</span></p>\\r\\n<p><strong><span style=\"font-family: Cambria Math;\">Conclusions:</span></strong></p>\\r\\n<p><span style=\"font-weight: 400;\">(I) All pencils are shadows.&nbsp;</span></p>\\r\\n<p><span style=\"font-weight: 400;\">(II) All cartoons are shadows.&nbsp;</span></p>\\r\\n<p><span style=\"font-weight: 400;\">(III) Some shadows are pencils.</span></p>\\n",
                    question_hi: "<p>24. <span style=\"font-family: Nirmala UI;\">&#2344;&#2368;&#2330;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2341;&#2344;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2323;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2367;&#2359;&#2381;&#2325;&#2352;&#2381;&#2359;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2343;&#2381;&#2351;&#2366;&#2344;&#2346;&#2370;&#2352;&#2381;&#2357;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2397;&#2367;&#2319;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2346;&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2344;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2341;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2340;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2330;&#2366;&#2361;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2350;&#2366;&#2344;&#2381;&#2351;&#2340;&#2307;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2381;&#2334;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2341;&#2381;&#2351;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2354;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2340;&#2368;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2379;&#2340;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2379;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2346;&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2367;&#2358;&#2381;&#2330;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2380;&#2344;</span><span style=\"font-family: Cambria Math;\">-</span><span style=\"font-family: Nirmala UI;\">&#2360;&#2366;</span><span style=\"font-family: Cambria Math;\"> / </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2380;&#2344;</span><span style=\"font-family: Cambria Math;\">-</span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2367;&#2359;&#2381;&#2325;&#2352;&#2381;&#2359;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2366;&#2352;&#2381;&#2325;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2352;&#2370;&#2346;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2341;&#2344;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2344;&#2369;&#2360;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">/</span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2306;&#2404;</span></p>\\r\\n<p><strong><span style=\"font-family: Nirmala UI;\">&#2325;&#2341;&#2344;</span><span style=\"font-family: Cambria Math;\">:</span></strong></p>\\r\\n<p><span style=\"font-family: Nirmala UI;\">&#2325;&#2369;&#2331;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2375;&#2306;&#2360;&#2367;&#2354;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Nirmala UI;\">&#2352;&#2381;&#2335;&#2370;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span></p>\\r\\n<p><span style=\"font-family: Nirmala UI;\">&#2360;&#2349;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;&#2352;&#2381;&#2335;&#2370;&#2344;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2342;&#2367;&#2351;&#2366;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2306;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span></p>\\r\\n<p><span style=\"font-family: Nirmala UI;\">&#2360;&#2349;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2342;&#2367;&#2351;&#2366;&#2306;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2331;&#2366;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2306;&#2404;</span></p>\\r\\n<p><strong><span style=\"font-family: Nirmala UI;\">&#2344;&#2367;&#2359;&#2381;&#2325;&#2352;&#2381;&#2359;</span><span style=\"font-family: Cambria Math;\">:</span></strong></p>\\r\\n<p><span style=\"font-weight: 400;\">(I) &#2360;&#2349;&#2368; &#2346;&#2375;&#2306;&#2360;&#2367;&#2354;, &#2331;&#2366;&#2351;&#2366; &#2361;&#2376;&#2404;&nbsp;</span></p>\\r\\n<p><span style=\"font-weight: 400;\">(II) &#2360;&#2349;&#2368; &#2325;&#2366;&#2352;&#2381;&#2335;&#2370;&#2344;, &#2331;&#2366;&#2351;&#2366; &#2361;&#2376;&#2404;&nbsp;</span></p>\\r\\n<p><span style=\"font-weight: 400;\">(III) &#2325;&#2369;&#2331; &#2331;&#2366;&#2351;&#2366;, &#2346;&#2375;&#2306;&#2360;&#2367;&#2354; &#2361;&#2376;&#2404;</span></p>\\n",
                    options_en: ["<p>Both conclusions II and III follow</p>\\n", "<p>Both conclusions I and II follow</p>\\n", 
                                "<p>Only conclusion III follows</p>\\n", "<p>Only conclusion II follows</p>\\n"],
                    options_hi: ["<p><span style=\"font-family: Nirmala UI;\">&#2342;&#2379;&#2344;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2367;&#2359;&#2381;&#2325;&#2352;&#2381;&#2359;</span><span style=\"font-family: Cambria Math;\"> II </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> III </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2341;&#2344;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2344;&#2369;&#2360;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2306;&#2404;</span></p>\\n", "<p><span style=\"font-family: Nirmala UI;\">&#2342;&#2379;&#2344;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2367;&#2359;&#2381;&#2325;&#2352;&#2381;&#2359;</span><span style=\"font-family: Cambria Math;\"> I </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> II </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2341;&#2344;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2344;&#2369;&#2360;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2306;&#2404;</span></p>\\n",
                                "<p><span style=\"font-family: Nirmala UI;\">&#2325;</span><span style=\"font-family: Nirmala UI;\">&#2375;&#2357;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2367;&#2359;&#2381;&#2325;&#2352;&#2381;&#2359;</span><span style=\"font-family: Cambria Math;\"> III </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2341;&#2344;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2344;&#2369;&#2360;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span></p>\\n", "<p><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;&#2357;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2367;&#2359;&#2381;&#2325;&#2352;&#2381;&#2359;</span><span style=\"font-family: Cambria Math;\"> II </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2341;&#2344;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2344;&#2369;&#2360;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span></p>\\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">24</span><span style=\"font-family: Cambria Math;\">.(a)</span></p>\\r\\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1701509074/word/media/image30.png\" width=\"200\" height=\"81\"></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Both conclusion II and III follows.</span></p>\\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">24</span><span style=\"font-family: Cambria Math;\">.(a)</span></p>\\r\\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1701509074/word/media/image31.png\" width=\"200\" height=\"80\"></p>\\r\\n<p><span style=\"font-family: Nirmala UI;\">&#2342;&#2379;&#2344;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2367;&#2359;&#2381;&#2325;&#2352;&#2381;&#2359;</span><span style=\"font-family: Cambria Math;\"> II </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> III </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2344;&#2369;&#2360;&#2352;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2340;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2306;&#2404;</span></p>\\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "25",
                    section: "misc",
                    question_en: "<p>25. <span style=\"font-family: Cambria Math;\">Identify the number of triangles in the given figure.</span></p>\\r\\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1701509074/word/media/image32.png\" width=\"152\" height=\"178\"></p>\\n",
                    question_hi: "<p>25. <span style=\"font-family: Nirmala UI;\">&#2342;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2325;&#2371;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2381;&#2352;&#2367;&#2349;&#2369;&#2332;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2381;&#2334;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;&#2332;&#2367;&#2319;&#2404;</span></p>\\r\\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1701509074/word/media/image32.png\" width=\"109\" height=\"128\"></p>\\n",
                    options_en: ["<p>22</p>\\n", "<p>30</p>\\n", 
                                "<p>26</p>\\n", "<p>28</p>\\n"],
                    options_hi: ["<p>22</p>\\n", "<p>30</p>\\n",
                                "<p>26</p>\\n", "<p>28</p>\\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">25.(b)</span></p>\\r\\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1701509074/word/media/image33.png\" width=\"156\" height=\"166\"></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Number of triangles in one figure = 1 + 2 + 3 + 4 = 10</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Since there are 3 figures so total no of triangles = 10 &times;</span><span style=\"font-family: Cambria Math;\"> 3 = 30</span></p>\\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">25.(b)</span></p>\\r\\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1701509074/word/media/image33.png\" width=\"156\" height=\"166\"></p>\\r\\n<p><span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2325;&#2371;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2381;&#2352;&#2367;&#2349;&#2369;&#2332;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> = 1 + 2 + 3 + 4 = 10</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2330;&#2370;&#2305;&#2325;&#2367;</span><span style=\"font-family: Cambria Math;\"> 3 </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2325;&#2371;&#2340;&#2367;&#2351;&#2366;&#2305;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2311;&#2360;&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2381;&#2352;&#2367;&#2349;&#2369;&#2332;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2369;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> =10 &times;</span><span style=\"font-family: Cambria Math;\"> 3 = 30</span></p>\\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>