<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">8:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 25</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">25</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 8 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["18"] = {
                name: "General Awareness",
                start: 0,
                end: 23
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="18">General Awareness</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 24,
                end: 24
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "18",
                    question_en: "<p>1. On which river in India was the Somasila Dam built?</p>",
                    question_hi: "<p>1. सोमसिला बांध (Somasila Dam) भारत में किस नदी पर बनाया गया है?</p>",
                    options_en: ["<p>Krishna river</p>", "<p>Tapti river</p>", 
                                "<p>Pennar river</p>", "<p>Sabarmati river</p>"],
                    options_hi: ["<p>कृष्णा नदी</p>", "<p>ताप्ती नदी</p>",
                                "<p>पेन्नार नदी</p>", "<p>साबरमती नदी</p>"],
                    solution_en: "<p>1.(c) <strong>Pennar river.</strong> It originates in the Nandi Hills, Karnataka, and flows through Karnataka and Andhra Pradesh before emptying into the Bay of Bengal. The Somasila dam is situated in the Nellore district (Andhra Pradesh). Famous dams and the rivers they are built on: Hirakud Dam (Mahanadi River), Sardar Sarovar Dam (Narmada River), Tehri Dam (Bhagirathi River), Bhakra Nangal Dam (Sutlej River), Koyna Dam (Koyna River), Idukki Dam (Periyar River).</p>",
                    solution_hi: "<p>1.(c) <strong>पेन्नार नदी।</strong> यह नदी कर्नाटक के नंदी हिल्स से निकलती है और बंगाल की खाड़ी में गिरने से पहले कर्नाटक और आंध्र प्रदेश से होकर बहती है। सोमासिला बांध नेल्लोर जिले (आंध्र प्रदेश) में स्थित है। प्रसिद्ध बांध और वे नदियाँ जिन पर वे निर्मित हैं: हीराकुंड बांध (महानदी नदी), सरदार सरोवर बांध (नर्मदा नदी), टिहरी बांध (भागीरथी नदी), भाखड़ा नांगल बांध (सतलुज नदी), कोयना बांध (कोयना नदी), इडुक्की बांध (पेरियार नदी)।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "18",
                    question_en: "<p>2. What is the name of the centrally sponsored scheme related with literacy that was launched by the Government of India in March 2023?</p>",
                    question_hi: "<p>2. मार्च 2023 में भारत सरकार द्वारा शुरू की गई साक्षरता से संबंधित केंद्र प्रायोजित योजना का नाम क्या है?</p>",
                    options_en: ["<p>New India Literacy Programme</p>", "<p>India Literacy Programme</p>", 
                                "<p>National Literacy Programme</p>", "<p>Bharat Padhega</p>"],
                    options_hi: ["<p>नव भारत साक्षरता कार्यक्रम</p>", "<p>भारत साक्षरता कार्यक्रम</p>",
                                "<p>राष्ट्रीय साक्षरता कार्यक्रम</p>", "<p>भारत पढ़ेगा</p>"],
                    solution_en: "<p>2.(a) <strong>New India Literacy Programme. </strong>It has been approved by the Government of India with Rs.1037.90 crore for FYs 2022-23 to 2026-27. This scheme targets non-literates aged 15 and above, aligning with NEP 2020. Other famous literacy programs: Pradhan Mantri Gramin Digital Saksharta Abhiyan (2017), Sarva Shiksha Abhiyan (2001-2002), Samagra Shiksha Abhiyan (2018).</p>",
                    solution_hi: "<p>2.(a) <strong>नव भारत साक्षरता कार्यक्रम।</strong> इसे भारत सरकार द्वारा वित्त वर्ष 2022-23 से 2026-27 के लिए 1037.90 करोड़ रुपये के साथ अनुमोदित किया गया है। यह योजना NEP 2020 के साथ संरेखित, 15 वर्ष और उससे अधिक आयु के गैर-साक्षरों को लक्षित करती है। अन्य प्रसिद्ध साक्षरता कार्यक्रम: प्रधानमंत्री ग्रामीण डिजिटल साक्षरता अभियान (2017), सर्व शिक्षा अभियान (2001-2002), समग्र शिक्षा अभियान (2018)।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "18",
                    question_en: "<p>3. Which kind of computer security requires verifying the genuineness of the individuals or organisations that want to access a system?</p>",
                    question_hi: "<p>3. किस प्रकार की कंप्यूटर सुरक्षा में किसी सिस्टम को एक्सैस (access) करने के इच्छुक व्यक्तियों या संगठनों की वास्तविकता को सत्यापित करने की आवश्यकता होती है?</p>",
                    options_en: ["<p>Authentication</p>", "<p>Non-repudiation</p>", 
                                "<p>Availability</p>", "<p>Confidentiality</p>"],
                    options_hi: ["<p>अधिप्रमाणन</p>", "<p>गैर- अस्वीकरण</p>",
                                "<p>गोपनीयता</p>", "<p>उपलब्धता</p>"],
                    solution_en: "<p>3.(a) <strong>Authentication. Computer security</strong> (Cybersecurity), is the protection of computer systems and information from harm, theft, and unauthorized use. Some examples of computer security: Firewalls, Strong passwords, Anti-virus and anti-malware software, Endpoint security, Controlling access, Regular updates, Monitoring for intrusion, Raising awareness.</p>",
                    solution_hi: "<p>3.(a) <strong>अधिप्रमाणन। </strong>कंप्यूटर सुरक्षा (साइबर सुरक्षा), कंप्यूटर सिस्टम और सूचना को नुकसान, चोरी और अनधिकृत उपयोग से बचाना है। कंप्यूटर सुरक्षा के कुछ उदाहरण: फ़ायरवॉल, मज़बूत पासवर्ड, एंटी-वायरस और एंटी-मैलवेयर सॉफ़्टवेयर, एंडपॉइंट सुरक्षा, पहुँच को नियंत्रित करना, नियमित अपडेट, घुसपैठ की निगरानी, ​​जागरूकता बढ़ाना।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "18",
                    question_en: "<p>4. Which Clause of Article 20 incorporates the doctrine of double jeopardy?</p>",
                    question_hi: "<p>4. अनुच्छेद 20 का कौन सा खंड दोहरे दंड के सिद्धांत (doctrine of double jeopardy) को शामिल करता है?</p>",
                    options_en: ["<p>3</p>", "<p>2</p>", 
                                "<p>1</p>", "<p>4</p>"],
                    options_hi: ["<p>3</p>", "<p>2</p>",
                                "<p>1</p>", "<p>4</p>"],
                    solution_en: "<p>4.(b) <strong>2. </strong>Double Jeopardy is a legal term and it means that a person can not be punished for the same offense more than once. Article 20(2) of the Constitution of India and Section 300 of the Criminal Procedure Code say that no person shall be prosecuted and punished for the same offense more than once. Article 20 grants protection against arbitrary and excessive punishment to an accused person, whether citizen or foreigner or legal person like a company or a corporation. Three provisions: No ex-post-facto law, No double jeopardy, No self-incrimination.</p>",
                    solution_hi: "<p>4.(b)<strong> 2.</strong> दोहरा दंड एक कानूनी शर्त है और इसका अर्थ है कि किसी व्यक्ति को एक ही अपराध के लिए एक से अधिक बार दंडित नहीं किया जा सकता है। भारतीय संविधान के अनुच्छेद 20(2) और दंड प्रक्रिया संहिता की धारा 300 में कहा गया है कि किसी भी व्यक्ति पर एक ही अपराध के लिए एक से अधिक बार मुकदमा नहीं चलाया जाएगा और उसे दंडित नहीं किया जाएगा। अनुच्छेद 20 - यह अनुच्छेद किसी भी अभियुक्त या दोषी करार व्यक्ति, चाहे वह नागरिक हो या विदेशी या कंपनी व परिषद का कानूनी व्यक्ति हो, उसके विरुद्ध मनमाने और अतिरिक्त दण्ड से संरक्षण प्रदान करता है। तीन प्रावधान: कोई पूर्वव्यापी कानून नहीं, कोई दोहरा दंड नहीं, कोई आत्म-दोष नहीं।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "18",
                    question_en: "<p>5. _________ is a significant festival of the Parsi people who practice Zoroastrianism as it marks the birth anniversary of Zoroaster.</p>",
                    question_hi: "<p>5. _________ पारसी लोगों का एक महत्वपूर्ण त्योहार है जो पारसी धर्म का पालन करते हैं क्योंकि यह जोरोस्टर की जयंती का प्रतीक है।</p>",
                    options_en: ["<p>Khordad Sal</p>", "<p>Gahambars</p>", 
                                "<p>Pateti</p>", "<p>Jamshed-e-Navroz </p>"],
                    options_hi: ["<p>खोरदाद साल (Khordad Sal)</p>", "<p>गहम्बर (Gahambars)</p>",
                                "<p>पतेती (Pateti)</p>", "<p>जमशेद-ए-नवरोज़ (Jamshed-e-Navroz)</p>"],
                    solution_en: "<p>5.(a) <strong>Khordad Sal.</strong> The Gahambars were established as a means of celebrating the six primordial elements of creation: sky, water, earth, plants, animals, and humans. Papeti (Navroz or Parsi New Year) is a festival celebrated by the Parsi community. It marks the start of the Iranian calendar and is an auspicious occasion for the Zoroastrian community. Jamshed-e-Navroz is a festival celebrated by the Parsis, a Zoroastrian community, to commemorate the coronation of King Jamshed.</p>",
                    solution_hi: "<p>5.(a) <strong>खोरदाद साल।</strong> गहंबरों की स्थापना सृष्टि के छह मूल तत्वों का उत्सव मनाने के साधन के रूप में की गई थी: आकाश, जल, पृथ्वी, पौधे, पशु और मनुष्य। पापेटी (नवरोज या पारसी नव वर्ष) पारसी समुदाय द्वारा मनाया जाने वाला एक त्योहार है, यह ईरानी कैलेंडर की शुरुआत का प्रतीक है और पारसी समुदाय के लिए एक शुभ अवसर है। जमशेद-ए-नवरोज़ पारसियों द्वारा मनाया जाने वाला एक त्यौहार है, पारसी समुदाय, जो राजा जमशेद के राज्याभिषेक की याद में मनाते है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "18",
                    question_en: "<p>6. By which ministry of Government of India was the Deendayal Antyodaya Yojana started?</p>",
                    question_hi: "<p>6. दीनदयाल अंत्योदय योजना भारत सरकार के किस मंत्रालय द्वारा शुरू की गई थी?</p>",
                    options_en: ["<p>Ministry of Corporate affairs</p>", "<p>Ministry of Rural Development</p>", 
                                "<p>Ministry of Home affairs</p>", "<p>Ministry of Labour and Employment</p>"],
                    options_hi: ["<p>कॉर्पोरेट कार्य मंत्रालय</p>", "<p>ग्रामीण विकास मंत्रालय</p>",
                                "<p>गृह मंत्रालय</p>", "<p>श्रम और रोजगार मंत्रालय</p>"],
                    solution_en: "<p>6.(b) <strong>Ministry of Rural Development.</strong> Deen Dayal Antyodaya Yojana - National Rural Livelihoods Mission (DAY - NRLM) was launched as &lsquo;Aajeevika - National Rural Livelihoods Mission (NRLM)\' in the year 2011. It was renamed as DAY-NRLM in 2015. It aims at creating effective and efficient institutional platforms to enable the rural poor to increase their household income by means of sustainable livelihood enhancements and better access to financial services.</p>",
                    solution_hi: "<p>6.(b) <strong>ग्रामीण विकास मंत्रालय।</strong> दीन दयाल अंत्योदय योजना - राष्ट्रीय ग्रामीण आजीविका मिशन (DAY - NRLM) को वर्ष 2011 में \'आजीविका - राष्ट्रीय ग्रामीण आजीविका मिशन (NRLM)\' के रूप में शुरू किया गया था। 2015 में इसका नाम बदलकर DAY - NRLM कर दिया गया। इसका उद्देश्य ग्रामीण गरीबों को स्थायी आजीविका संवर्द्धन और वित्तीय सेवाओं तक बेहतर पहुंच के माध्यम से अपनी घरेलू आय बढ़ाने में सक्षम बनाने के लिए प्रभावी और कुशल संस्थागत प्लेटफॉर्म बनाना है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "18",
                    question_en: "<p>7. According to the Census of India-2011, which state has the largest gap between male and female literacy rates?</p>",
                    question_hi: "<p>7. भारत की जनगणना-2011 के अनुसार, किस राज्य में पुरुष और महिला साक्षरता दर के बीच सबसे अधिक अंतर है?</p>",
                    options_en: ["<p>Andhra Pradesh</p>", "<p>Assam</p>", 
                                "<p>Rajasthan</p>", "<p>Karnataka</p>"],
                    options_hi: ["<p>आंध्र प्रदेश</p>", "<p>असम</p>",
                                "<p>राजस्थान</p>", "<p>कर्नाटक</p>"],
                    solution_en: "<p>7.(c) <strong>Rajasthan</strong>. The literacy rate is the total percentage of the population of an area at a particular time aged 7 years or above who can read and write with understanding. The literacy rate in the country is 74.04 per cent, 82.14 for males and 65.46 for females. <br>Kerala retained its position by being on top with a 93.91 per cent literacy rate, closely followed by Lakshadweep (92.28 per cent) and Mizoram (91.58 per cent). Bihar is the least literate state in India with a literacy rate of 63.82%.</p>",
                    solution_hi: "<p>7.(c) <strong>राजस्थान</strong>। साक्षरता दर किसी क्षेत्र की 7 वर्ष या उससे अधिक आयु की आबादी का कुल प्रतिशत है जो समझ के साथ पढ़ और लिख सकती है। देश में साक्षरता दर 74.04 प्रतिशत है, जिसमें पुरुषों के लिए 82.14 और महिलाओं के लिए 65.46 प्रतिशत है। केरल 93.91 प्रतिशत साक्षरता दर के साथ शीर्ष स्थान पर है, उसके बाद लक्षद्वीप (92.28 प्रतिशत) और मिजोरम (91.58 प्रतिशत) का स्थान है। बिहार 63.82% साक्षरता दर के साथ भारत का सबसे कम साक्षरता वाला राज्य है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "18",
                    question_en: "<p>8. Who among the following was appointed, to the UN tax committee in July 2021, as a member for the 2021 to 2025 term?</p>",
                    question_hi: "<p>8. निम्नलिखित में से किसे 2021 से 2025 की अवधि के लिए सदस्य के रूप में जुलाई 2021 में संयुक्त राष्ट्र कर समिति में नियुक्त किया गया था?</p>",
                    options_en: ["<p>Rasmi Ranjan Das</p>", "<p>Vivek Singh</p>", 
                                "<p>Vipul Bansal</p>", "<p>TV Somanathan</p>"],
                    options_hi: ["<p>रश्मि रंजन दास</p>", "<p>विवेक सिंह</p>",
                                "<p>विपुल बंसल</p>", "<p>टी.वी.सोमनाथन</p>"],
                    solution_en: "<p>8.(a) <strong>Rasmi Ranjan Das.</strong> The United Nations Committee of Experts on International Cooperation in Tax Matters, also known as the UN Tax Committee, is responsible for strengthening international cooperation on tax matters. The committee\'s work includes: Policy and guidance, Sustainable development, Capacity building, Convention and treaty updates, Focus on developing countries. The committee\'s agenda includes: Digitalized and globalized economy, Environmental taxes, Extractive industries taxes, Health taxes, Wealth taxes, Transfer pricing, Tax treaties.</p>",
                    solution_hi: "<p>8.(a) <strong>रश्मि रंजन दास।</strong> कर मामलों में अन्तर्राष्ट्रीय सहयोग पर संयुक्त राष्ट्र विशेषज्ञों की समिति, जिसे संयुक्त राष्ट्र कर समिति के रूप में भी जाना जाता है, कर मामलों पर अन्तर्राष्ट्रीय सहयोग को मजबूत करने के लिए जिम्मेदार है। समिति के कार्यों में शामिल हैं: नीति और मार्गदर्शन, सतत विकास, क्षमता निर्माण, सम्मेलन और संधि अद्यतन, विकासशील देशों पर ध्यान केंद्रित करना। समिति के एजेंडे में शामिल हैं: डिजिटल और वैश्वीकृत अर्थव्यवस्था, पर्यावरण कर, निष्कर्षण उद्योग कर, स्वास्थ्य कर, संपत्ति कर, हस्तांतरण मूल्य निर्धारण, कर संधियाँ।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "18",
                    question_en: "<p>9. The loss of water in the form of water droplets from leaves of plants is called ____.</p>",
                    question_hi: "<p>9. पौधों की पत्तियों में से जल वाष्प के रूप में जल का क्षय (loss) होना ________ कहलाता है।</p>",
                    options_en: ["<p>Translocation</p>", "<p>Plasmolysis</p>", 
                                "<p>Pressure gradient</p>", "<p>Guttation</p>"],
                    options_hi: ["<p>स्थानांतरण (Translocation)</p>", "<p>जीवद्रव्य कुंचन (Plasmolysis)</p>",
                                "<p>दाब प्रवणता (Pressure gradient)</p>", "<p>बिंदुस्राव (Guttation)</p>"],
                    solution_en: "<p>9.(d) <strong>Guttation</strong>. Translocation is the movement of materials from leaves to other tissues throughout the plant. Plasmolysis is defined as the process of contraction or shrinkage of the protoplasm of a plant cell and is caused due to the loss of water in the cell. Pressure gradient refers to the difference in air pressure between two points in the atmosphere or on the surface of the earth.</p>",
                    solution_hi: "<p>9.(d) <strong>बिंदुस्राव</strong>। स्थानांतरण पूरे पौधे में पत्तियों से दूसरे ऊतकों तक पदार्थों का संचरण है। जीवद्रव्य कुंचन को पौधे की कोशिका के जीवद्रव्य के संकुचन की प्रक्रिया के रूप में परिभाषित किया जाता है और यह कोशिका में जल की कमी के कारण होता है। दाब प्रवणता वायुमंडल में या पृथ्वी की सतह पर दो बिंदुओं के बीच वायु दाब में अंतर को संदर्भित करती है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "18",
                    question_en: "<p>10. As per Olympic rules, in the third game, badminton players change ends when a side scores _____ points.</p>",
                    question_hi: "<p>10. ओलंपिक के नियमों के अनुसार, मैच की तीसरी पारी में, जब कोई पक्ष _____ पॉइंट बनाता है तो बैडमिंटन खिलाड़ी कोर्ट की साइड (ends) बदल लेते हैं।</p>",
                    options_en: ["<p>15</p>", "<p>11</p>", 
                                "<p>12</p>", "<p>13</p>"],
                    options_hi: ["<p>15</p>", "<p>11</p>",
                                "<p>12</p>", "<p>13</p>"],
                    solution_en: "<p>10.(b) <strong>11.</strong> The International Olympic Committee is the guardian of the Olympic Games and the leader of the Olympic Movement. A truly global organisation. Badminton had its debut as an official event at the 1992 Summer Olympics and has been contested in eight Olympiads. Saina Nehwal became India\'s first female world no. 1 in April 2015 and the first Indian to win an Olympic badminton medal.</p>",
                    solution_hi: "<p>10.(b) <strong>11.</strong>अंतर्राष्ट्रीय ओलंपिक समिति, ओलंपिक खेलों की संरक्षक और ओलंपिक कार्यान्वयन का नेतृत्व करती है। यह वास्तव में एक वैश्विक संगठन है। बैडमिंटन ने 1992 के ग्रीष्मकालीन ओलंपिक में आधिकारिक आयोजन के रूप में अपनी शुरुआत की थी और आठ ओलंपियाड में भाग लिया। साइना नेहवाल अप्रैल 2015 में भारत की पहली महिला विश्व नंबर 1 खिलाड़ी और ओलंपिक बैडमिंटन पदक जीतने वाली पहली भारतीय बनीं।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "18",
                    question_en: "<p>11. How many times has India won the South Asian Football Federation (SAFF) Championship as of December 2023?</p>",
                    question_hi: "<p>11. दिसंबर 2023 तक भारत ने कितनी बार दक्षिण एशियाई फूटबॉल संघ (South Asian Football Federation - SAFF) चैम्पियनशिप जीती?</p>",
                    options_en: ["<p>5 times</p>", "<p>7 times</p>", 
                                "<p>6 times</p>", "<p>9 times</p>"],
                    options_hi: ["<p>5 बार</p>", "<p>7 बार</p>",
                                "<p>6 बार</p>", "<p>9 बार</p>"],
                    solution_en: "<p>11.(d) <strong>9 times. </strong>South Asian Football Federation (SAFF) is an association of the football playing nations in South Asia which is a regional subsidiary of Asian Football Confederation, incorporated in 1997. The members of the association are Bangladesh, Bhutan, India, Maldives, Nepal, Pakistan and Sri Lanka. Formation - 1997. Headquarters - Banani, Dhaka (Bangladesh). Official language - English.</p>",
                    solution_hi: "<p>11.(d) <strong>9 बार।</strong> दक्षिण एशियाई फुटबॉल महासंघ (SAFF) दक्षिण एशिया में फुटबॉल खेलने वाले देशों का एक संघ है जो एशियाई फुटबॉल परिसंघ की एक क्षेत्रीय सहायक संस्था है, जिसे 1997 में शामिल किया गया था। संघ के सदस्य बांग्लादेश, भूटान, भारत, मालदीव, नेपाल, पाकिस्तान और श्रीलंका हैं। गठन - 1997 । मुख्यालय - बनानी, ढाका (बांग्लादेश)। आधिकारिक भाषा - अंग्रेजी।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "18",
                    question_en: "<p>12. In MS Word, which feature allows you to create different headers and footers for odd and even pages in a document?</p>",
                    question_hi: "<p>12. एमएस वर्ड (MS Word) में, कौन-सा फीचर आपको किसी डॉक्यूमेंट में विषम और सम पृष्ठों के लिए अलग-अलग हेडर और फ़ुटर बनाने में सहायता करता है?</p>",
                    options_en: ["<p>Different Odd &amp; Even Pages</p>", "<p>Page Breaks</p>", 
                                "<p>Section Breaks</p>", "<p>Different First Page</p>"],
                    options_hi: ["<p>डिफ़रेंट ऑड ऐंड इवन पेजेस (Different Odd &amp; Even Pages)</p>", "<p>पेज ब्रेक्स (Page Breaks)</p>",
                                "<p>सेक्शन ब्रेक्स (Section Breaks)</p>", "<p>डिफ़रेंट फ़र्स्ट पेज (Different First Page)</p>"],
                    solution_en: "<p>12.(a)<strong> Different Odd &amp; Even Pages. </strong>Microsoft Word is a word processor program developed by Microsoft. It was first released on October 25, 1983, under the name Multi-Tool Word for Xenix systems. Initial release - October 25, 1983.</p>",
                    solution_hi: "<p>12.(a) <strong>डिफ़रेंट ऑड ऐंड इवन पेजेस। </strong>माइक्रोसॉफ्ट वर्ड माइक्रोसॉफ्ट द्वारा विकसित एक वर्ड प्रोसेसर प्रोग्राम है। इसे पहली बार 25 अक्टूबर, 1983 को जेनिक्स सिस्टम के लिए मल्टी-टूल वर्ड नाम से रिलीज़ किया गया था। प्रारंभिक उद्घाटन - 25 अक्टूबर, 1983।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "18",
                    question_en: "<p>13. Which theory describes the collective effects of changes in Earth\'s movements on its climate over thousands of years?</p>",
                    question_hi: "<p>13. कौन सा सिद्धांत हजारों वर्षों में पृथ्वी की गति में परिवर्तन के कारण उसकी जलवायु पर पड़ने वाले सामूहिक प्रभावों का वर्णन करता है?</p>",
                    options_en: ["<p>Milankovitch theory</p>", "<p>Copernicus\' heliocentric theory</p>", 
                                "<p>Plate tectonics theory</p>", "<p>Continental drift theory</p>"],
                    options_hi: ["<p>मिलनकोविच सिद्धांत (Milankovitch theory)</p>", "<p>कॉपरनिकस का हेलियोसेंट्रिक सिद्धांत (Copernicus\' heliocentric theory)</p>",
                                "<p>प्लेट विवर्तनिकी सिद्धांत (Plate tectonics theory)</p>", "<p>महाद्वीपीय बहाव सिद्धांत (Continental drift theory)</p>"],
                    solution_en: "<p>13.(a) <strong>Milankovitch theory.</strong> Copernican heliocentrism, proposed by Copernicus in 1543, positions the Sun at the center, with Earth and other planets orbiting it. Plate tectonics is a scientific theory that explains how major landforms are created as a result of Earth\'s subterranean movements.</p>",
                    solution_hi: "<p>13.(a) <strong>मिलनकोविच सिद्धांत। </strong>कॉपरनिकस द्वारा 1543 में प्रस्तावित कॉपरनिकस हेलियोसेंट्रिज्म के अनुसार सूर्य केंद्र में होता है, जबकि पृथ्वी और अन्य ग्रह इसकी परिक्रमा करते हैं। प्लेट विवर्तनिकी एक वैज्ञानिक सिद्धांत है जो बताता है कि पृथ्वी की भूमिगत गतिविधियों के परिणामस्वरूप प्रमुख भू-आकृतियों का निर्माण किस प्रकार होता है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "18",
                    question_en: "<p>14. Who is the founder of modern micro finance ?</p>",
                    question_hi: "<p>14. आधुनिक सूक्ष्म वित्त (modern micro finance) के जनक कौन हैं?</p>",
                    options_en: ["<p>Muhammad Yunus</p>", "<p>Rangarajan</p>", 
                                "<p>YV Reddy</p>", "<p>VKV Rao</p>"],
                    options_hi: ["<p>मुहम्मद यूनुस</p>", "<p>रंगराजन</p>",
                                "<p>वाई.वी. रेड्डी</p>", "<p>वी.के.वी. राव</p>"],
                    solution_en: "<p>14.(a) <strong>Muhammad Yunus</strong>. Microfinance is a banking service that is provided to low-income individuals who have no other means of gaining financial services. Muhammad Yunus is a Bangladeshi economist, was awarded the Nobel Peace Prize in 2006 for founding the Grameen Bank (Founded - 1976, Dhaka, Bangladesh) and pioneering the concepts of microcredit and microfinance.</p>",
                    solution_hi: "<p>14.(a) <strong>मुहम्मद यूनुस। </strong>सूक्ष्म वित्त एक बैंकिंग सेवा है जो कम आय वाले व्यक्तियों को प्रदान की जाती है जिनके पास वित्तीय सेवाएँ प्राप्त करने का कोई अन्य साधन नहीं है। मुहम्मद यूनुस एक बांग्लादेशी अर्थशास्त्री हैं, जिन्हें ग्रामीण बैंक (स्थापना - 1976, ढाका, बांग्लादेश) की स्थापना और माइक्रो क्रेडिट तथा माइक्रो फाइनेंस की अवधारणाओं को आगे बढ़ाने के लिए 2006 में नोबेल शांति पुरस्कार से सम्मानित किया गया था।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "18",
                    question_en: "<p>15. In which year was tennis included in the Olympic games?</p>",
                    question_hi: "<p>15. टेनिस को ओलंपिक खेलों में किस वर्ष शामिल किया गया था?</p>",
                    options_en: ["<p>1927</p>", "<p>1896</p>", 
                                "<p>1935</p>", "<p>1948</p>"],
                    options_hi: ["<p>1927</p>", "<p>1896</p>",
                                "<p>1935</p>", "<p>1948</p>"],
                    solution_en: "<p>15.(b) <strong>1896</strong>. The first Olympic Games of the modern era took place in Athens, in the country where the original Games took place in Antiquity, in April 1896. Leander Paes is still the only Indian to win an Olympics tennis medal, bagging a men\'s singles bronze at Atlanta 1996.</p>",
                    solution_hi: "<p>15.(b) <strong>1896</strong>. आधुनिक युग के पहले ओलंपिक खेल अप्रैल 1896 में एथेंस में हुए थे, जहाँ मूल खेल प्राचीन काल में आयोजित हुए थे। लिएंडर पेस ओलंपिक टेनिस पदक जीतने वाले एकमात्र भारतीय हैं, जिन्होंने अटलांटा 1996 में पुरुष एकल कांस्य पदक जीता था।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "18",
                    question_en: "<p>16. How many types of writs can be issued by the Supreme Court to protect the Fundamental Rights of Indian citizens?</p>",
                    question_hi: "<p>16. भारतीय नागरिकों के मौलिक अधिकारों की रक्षा के लिए सर्वोच्च न्यायालय (Supreme Court) द्वारा कितने प्रकार की रिट (writs) जारी की जा सकती हैं?</p>",
                    options_en: ["<p>Four</p>", "<p>Seven</p>", 
                                "<p>Five</p>", "<p>Six</p>"],
                    options_hi: ["<p>चार</p>", "<p>सात</p>",
                                "<p>पाँच</p>", "<p>छः</p>"],
                    solution_en: "<p>16.(c) <strong>Five</strong>. Article 32 - Remedies for enforcement of rights conferred by this Part. Article 226 - Power of High Courts to issue certain writs. The Supreme Court or the High Court can issue five different types of writs/orders to enforce the fundamental rights of Indian citizens. They are called Habeas Corpus, Mandamus, Quo-Warranto, Prohibition, and Certiorari.</p>",
                    solution_hi: "<p>16.(c) <strong>पाँच</strong>। अनुच्छेद 32 - इस भाग द्वारा प्रदत्त अधिकारों को लागू करने के उपाय। अनुच्छेद 226 - कुछ रिट जारी करने की उच्च न्यायालयों की शक्ति। भारतीय नागरिकों के मौलिक अधिकारों को लागू करने के लिए सर्वोच्च न्यायालय या उच्च न्यायालय पाँच अलग-अलग प्रकार की रिट/आदेश जारी कर सकता है। उन्हें बंदी प्रत्यक्षीकरण, मैंडामस, क्वो-वारंटो, निषेध और सर्टिओरारी कहा जाता है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "18",
                    question_en: "<p>17. Which of the following was the last dynasty of the Vijayanagara Empire?</p>",
                    question_hi: "<p>17. निम्नलिखित में से कौन विजयनगर साम्राज्य का अंतिम राजवंश था?</p>",
                    options_en: ["<p>Tuluva dynasty</p>", "<p>Saluva dynasty</p>", 
                                "<p>Sangam dynasty</p>", "<p>Aravidu dynasty</p>"],
                    options_hi: ["<p>तुलुव राजवंश (Tuluva dynasty)</p>", "<p>सलुव राजवंश (Saluva dynasty)</p>",
                                "<p>संगम राजवंश (Sangam dynasty)</p>", "<p>अराविडु राजवंश (Aravidu dynasty)</p>"],
                    solution_en: "<p>17.(d) <strong>Aravidu dynasty.</strong> The Vijayanagara Empire was a late medieval Hindu empire that ruled much of southern India. It was established in 1336 by the brothers Harihara I and Bukka Raya I of the Sangama dynasty. The Vijayanagara Empire had four dynasties: Sangama dynasty (1336-1485 CE), Saluva dynasty (1485-1505 CE), Tuluva dynasty (1505-1570 CE), Aravidu dynasty (1570-1646 CE).</p>",
                    solution_hi: "<p>17.(d) <strong>अराविडु राजवंश।</strong> विजयनगर साम्राज्य एक मध्यकालीन हिंदू साम्राज्य था जिसने दक्षिण भारत के अधिकांश भाग पर शासन किया था। इसकी स्थापना 1336 में संगम राजवंश के भाइयों हरिहर प्रथम और बुक्का राय प्रथम ने की थी। विजयनगर साम्राज्य में चार राजवंश थे: संगम राजवंश (1336-1485 ई.), सलुव राजवंश (1485-1505 ई.), तुलुव राजवंश (1505-1570 ई.), अराविडु राजवंश (1570-1646 ई.)।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "18",
                    question_en: "<p>18. Match the following musical instruments with the maestros who play them.<br>a. Santoor&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; i. Bismillah Khan<br>b. Sitar&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;ii. Ustad Binda Khan<br>c. Shehnai&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; iii. Pt Shiv Kumar Sharma<br>d. Sarangi&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;iv. Anushka Shankar</p>",
                    question_hi: "<p>18. निम्नलिखित संगीत वाद्ययंत्रों का मिलान उन्हें बजाने वाले उस्तादों के साथ करें।<br>&nbsp; a. संतूर&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; i. बिस्मिल्लाह खान<br>&nbsp; b. सितार&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; ii. उस्ताद बिंदा खान<br>&nbsp; c. शहनाई&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; iii. पंडित शिव कुमार शर्मा<br>&nbsp; d. सारंगी&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; iv. अनुष्का शंकर</p>",
                    options_en: ["<p>a-ii, b-iii, c-i, d-iv</p>", "<p>a-iv, b-iii, c-ii, d-i</p>", 
                                "<p>a-iii, b-iv, c-i, d-ii</p>", "<p>a-i, b-ii, c-iii, d-iv</p>"],
                    options_hi: ["<p>a-ii, b-iii, c-i, d-iv</p>", "<p>a-iv, b-iii, c-ii, d-i</p>",
                                "<p>a-iii, b-iv, c-i, d-ii</p>", "<p>a-i, b-ii, c-iii, d-iv</p>"],
                    solution_en: "<p>18.(c) <strong>a-iii, b-iv, c-i, d-ii. </strong>Famous musical instruments and their exponents: Santoor - Bhajan Sopori, Pt Tarun Bhattacharya.<br>Sitar - Ustad Vilayat Khan, Pt Ravi Shankar, Shujaat Hussain Khan, Shahid Parvez Khan, Anushka Shankar, Nikhil Banerjee, Mustaq Ali Khan, Budhaditya Mukherjee. Shehnai - Daya Shankar,Ali Ahmad Hussain. Sarangi: Abdul Latif Khan, Ramesh Mishra, Sultan Khan, Pt Ram Narayan, Shakoor Khan.</p>",
                    solution_hi: "<p>18.(c) <strong>a-iii, b-iv, c-i, d-ii. </strong>प्रसिद्ध संगीत वाद्ययंत्र और उनके प्रतिपादक: संतूर - भजन सोपोरी, पं. तरूण भट्टाचार्य। सितार - उस्ताद विलायत खान, पं. रविशंकर, शुजात हुसैन खान, शाहिद परवेज खान, अनुष्का शंकर, निखिल बनर्जी, मुस्ताक अली खान, बुधादित्य मुखर्जी। शहनाई - दया शंकर, अली अहमद हुसैन। सारंगी: अब्दुल लतीफ खान, रमेश मिश्रा, सुल्तान खान, पं. राम नारायण, शकूर खान।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "18",
                    question_en: "<p>19. Who among the following was primarily an integral part of the Indian National Army (INA)?</p>",
                    question_hi: "<p>19. निम्नलिखित में से कौन मुख्य रूप से भारतीय राष्ट्रीय सेना (INA) के अभिन्न अंग थे?</p>",
                    options_en: ["<p>Rabindranath Tagore</p>", "<p>Subhas Chandra Bose</p>", 
                                "<p>Chittaranjan Das</p>", "<p>Abanindranath Tagore</p>"],
                    options_hi: ["<p>रवीन्द्र नाथ टैगोर</p>", "<p>सुभाष चंद्र बोस</p>",
                                "<p>चितरंजन दास</p>", "<p>अवनींद्रनाथ टैगोर</p>"],
                    solution_en: "<p>19.(b) <strong>Subhas Chandra Bose. </strong>Indian National Army (INA) was a collaborationist armed unit of Indian collaborators that fought under the command of the Japanese Empire. It was founded by Mohan Singh in September 1942 in Southeast Asia during World War II. Some notable people associated: Mohan Singh, Habib ur Rahman, Lt. Col Hori Lal Verma.</p>",
                    solution_hi: "<p>19.(b) <strong>सुभाष चंद्र बोस।</strong> इंडियन नेशनल आर्मी (INA) भारतीय सहयोगियों की एक सहयोगी सशस्त्र यूनिट थी जो जापानी साम्राज्य की कमान के तहत लड़ी थी। इसकी स्थापना मोहन सिंह ने सितंबर 1942 में द्वितीय विश्व युद्ध के दौरान दक्षिण पूर्व एशिया में की थी। इससे जुड़े कुछ प्रसिद्ध व्यक्तित्व: मोहन सिंह, हबीब उर रहमान, लेफ्टिनेंट कर्नल होरी लाल वर्मा।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "18",
                    question_en: "<p>20. PC Mahalanobis is remembered as a major contributor of India&rsquo;s developmental path because he ______.<br>(A)set up Indian Institutes of Management located in different parts of India<br>(B)formulated the Second Five-Year Plan<br>(C)was the first Minister of Statistics and Programme Implementation</p>",
                    question_hi: "<p>20. पी.सी.महालनोबिस (PC Mahalanobis) को भारत के विकास पथ में एक प्रमुख योगदानकर्ता के रूप में याद किया जाता है क्योंकि उन्होंने/वे __________ ।<br>(A) भारत के विभिन्न हिस्सों में स्थित भारतीय प्रबंधन संस्थानों की स्थापना की<br>(B) दूसरी पंचवर्षीय योजना तैयार की थी<br>(C) सांख्यिकी और कार्यक्रम कार्यान्वयन के पहले मंत्री थे</p>",
                    options_en: ["<p>Only (A) and (C) are true</p>", "<p>Only (B) is true</p>", 
                                "<p>Only (A) and (B) are true</p>", "<p>Only (A) is true</p>"],
                    options_hi: ["<p>केवल (A) और (C) सत्य हैं</p>", "<p>केवल (B) सत्य है</p>",
                                "<p>केवल (A) और (B) सत्य हैं</p>", "<p>केवल (A) सत्य है</p>"],
                    solution_en: "<p>20.(b) <strong>Only (B) is true. </strong>The Second five-year plan was based on the Mahalanobis model, an economic development model developed by the Indian statistician Prasanta Chandra Mahalanobis in 1953. PC Mahalanobis established the Indian Statistical Institute (ISI) in Calcutta in 1931 and for the same reason, he is known as the father of Indian Statistics. He was a member of the first Planning Commission of free India.</p>",
                    solution_hi: "<p>20.(b) <strong>केवल (B) सत्य है।</strong> दूसरी पंचवर्षीय योजना महालनोबिस मॉडल पर आधारित थी, जो 1953 में भारतीय सांख्यिकीविद् प्रशांत चंद्र महालनोबिस द्वारा विकसित एक आर्थिक विकास मॉडल था। पी.सी. महालनोबिस ने 1931 में कलकत्ता में भारतीय सांख्यिकी संस्थान (ISI) की स्थापना की और इसी कारण से उन्हें भारतीय सांख्यिकी के जनक के रूप में जाना जाता है। वे स्वतंत्र भारत के पहले योजना आयोग के सदस्य थे।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "18",
                    question_en: "<p>21. A _________ is a cloud of dust and gas inside a galaxy.</p>",
                    question_hi: "<p>21. _________ किसी आकाशगंगा के अंदर धूल और गैस का एक बादल होता है।</p>",
                    options_en: ["<p>black hole</p>", "<p>photosphere</p>", 
                                "<p>nebula</p>", "<p>chromosphere</p>"],
                    options_hi: ["<p>ब्लैक होल</p>", "<p>फ़ोटोस्फ़ेयर</p>",
                                "<p>नेबुला</p>", "<p>वर्णमण्डल</p>"],
                    solution_en: "<p>21.(c) <strong>nebula</strong>. A nebula is an interstellar cloud of dust, hydrogen, helium and other ionized gases. Originally, nebula was a name for any diffuse astronomical object, including galaxies beyond the Milky Way. Black holes grow by the accretion of matter nearby that is pulled in by their immense gravity. The photosphere is a star\'s outer shell from which light is radiated. A chromosphere is the second layer of a star\'s atmosphere, located above the photosphere and below the solar transition region and corona.</p>",
                    solution_hi: "<p>21.(c) <strong>नेबुला</strong>। नेबुला धूल, हाइड्रोजन, हीलियम और अन्य आयनित गैसों का एक अंतरतारकीय बादल है। मूल रूप से, नेबुला किसी भी बिखरे हुए खगोलीय पिंड का नाम था, जिसमें मिल्की वे से परे आकाशगंगाएँ भी शामिल थीं। ब्लैक होल आस-पास के पदार्थ के संचय से बढ़ते हैं जो उनके विशाल गुरुत्वाकर्षण द्वारा खींचे जाते हैं। फोटोस्फीयर एक तारे का बाहरी आवरण है जहाँ से प्रकाश निकलता है। क्रोमोस्फीयर एक तारे के वायुमंडल की दूसरी परत है, जो फोटोस्फीयर के ऊपर और सौर संक्रमण क्षेत्र और कोरोना के नीचे स्थित है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "18",
                    question_en: "<p>22. In 2023, Gulab Chand Kataria became the Governor of which of the following states?</p>",
                    question_hi: "<p>22. 2023 में, गुलाब चंद कटारिया निम्नलिखित में से किस राज्य के राज्यपाल बने?</p>",
                    options_en: ["<p>Himachal Pradesh</p>", "<p>Assam</p>", 
                                "<p>Tamil Nadu</p>", "<p>Odisha</p>"],
                    options_hi: ["<p>हिमाचल प्रदेश</p>", "<p>असम</p>",
                                "<p>तमिलनाडु</p>", "<p>ओडिशा</p>"],
                    solution_en: "<p>22.(b) <strong>Assam</strong>. Gulab Chand Kataria is an Indian politician who is serving as the Governor of Punjab and Administrator of Chandigarh since 31 July 2024. Lakshman Prasad Acharya is the 32nd Governor of Assam (As of September 2024). Article 153 - Governors of States. Article 154 - Executive power of State. Other state Governor: Himachal Pradesh - Shiv Pratap Shukla, Tamil Nadu - R. N. Ravi, Odisha - Raghubar Das.</p>",
                    solution_hi: "<p>22.(b) <strong>असम</strong>। गुलाब चंद कटारिया एक भारतीय राजनीतिज्ञ हैं जो 31 जुलाई 2024 से पंजाब के राज्यपाल और चंडीगढ़ के प्रशासक के रूप में कार्यरत हैं। लक्ष्मण प्रसाद आचार्य असम के 32वें राज्यपाल हैं (सितम्बर 2024 तक)। अनुच्छेद 153 - राज्यों के राज्यपाल. अनुच्छेद 154 - राज्य की कार्यपालिका शक्ति. अन्य राज्य के राज्यपाल: हिमाचल प्रदेश - शिव प्रताप शुक्ला, तमिलनाडु - आर.एन. रवि, ओडिशा - रघुबर दास।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "23",
                    section: "18",
                    question_en: "<p>23. Who among the following is a famous classical dancer?</p>",
                    question_hi: "<p>23. निम्नलिखित में से कौन एक प्रसिद्ध शास्त्रीय नृत्यांगना हैं?</p>",
                    options_en: ["<p>S Janaki</p>", "<p>Sonal Mansingh</p>", 
                                "<p>Asha Bhosle</p>", "<p>Pratibha Ray</p>"],
                    options_hi: ["<p>एस. जानकी</p>", "<p>सोनल मानसिंह</p>",
                                "<p>आशा भोसले</p>", "<p>प्रतिभा राय</p>"],
                    solution_en: "<p>23.(b) <strong>Sonal Mansingh.</strong> She is an Indian classical dancer and Guru in Bharatanatyam and Odissi dancing style. Sistla Janaki is an Indian playback singer and occasional music composer from Andhra Pradesh. Asha Bhosle is an Indian singer who records songs for movies. Pratibha Ray is an Indian academic and writer of Odia-language novels and stories.</p>",
                    solution_hi: "<p>23.(b) <strong>सोनल मानसिंह।</strong> वह एक भारतीय शास्त्रीय नर्तकी और भरतनाट्यम और ओडिसी नृत्य शैली की गुरु हैं। सिस्टला जानकी आंध्र प्रदेश की एक भारतीय पार्श्व गायिका और सामयिक संगीत रचनाकार हैं। आशा भोसले एक भारतीय गायिका हैं जो फिल्मों के लिए गाने रिकॉर्ड करती हैं। प्रतिभा रे एक भारतीय शिक्षाविद और ओडिया भाषा के उपन्यासों और कहानियों की लेखिका हैं।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "24",
                    section: "18",
                    question_en: "<p>24. Which of the following types of crops are sown after the end of the monsoon season?</p>",
                    question_hi: "<p>24. निम्नलिखित में से किस प्रकार की फसलें मानसून के मौसम के अंत के बाद बोई जाती हैं?</p>",
                    options_en: ["<p>Kharif crops</p>", "<p>Zaid crops</p>", 
                                "<p>Rabi crops</p>", "<p>Vital crops</p>"],
                    options_hi: ["<p>खरीफ की फसलें</p>", "<p>जायद की फसलें</p>",
                                "<p>रबी की फसलें</p>", "<p>महत्वपूर्ण फसलें</p>"],
                    solution_en: "<p>24.(c) <strong>Rabi crops.</strong> It is an agricultural crop that is sown in the winter and harvested in the spring. Kharif crops are crops that are sown in June or July and harvested in September. Zaid crops are seasonal fruits and vegetables grown in the Indian subcontinent during the summer season, from March to June.</p>",
                    solution_hi: "<p>24.(c)<strong> रबी की फसलें। </strong>यह एक कृषि फसल है जिसे सर्दियों में बोया जाता है और वसंत में काटा जाता है। खरीफ की फसलें ऐसी फसलें हैं जिन्हें जून या जुलाई में बोया जाता है और सितंबर में काटा जाता है। जायद की फसलें भारतीय उपमहाद्वीप में गर्मियों के मौसम में, मार्च से जून तक उगाई जाने वाली मौसमी फल और सब्जियाँ हैं।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "25",
                    section: "misc",
                    question_en: "<p>25. Match List-I (Name of the composition) with List-II (Writer).<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1732866205036.png\" alt=\"rId7\" width=\"445\" height=\"98\"></p>",
                    question_hi: "<p>25. सूची-I (रचना का नाम) को सूची-II (लेखक) के साथ सुमेलित करें।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1732866205200.png\" alt=\"rId8\" width=\"468\" height=\"152\"></p>",
                    options_en: ["<p>1-C, 2-A, 3-B</p>", "<p>1-B, 2-C, 3-A</p>", 
                                "<p>1-C, 2-B, 3-A</p>", "<p>1-A, 2-B, 3-C</p>"],
                    options_hi: ["<p>1-C, 2-A, 3-B</p>", "<p>1-B, 2-C, 3-A</p>",
                                "<p>1-C, 2-B, 3-A</p>", "<p>1-A, 2-B, 3-C</p>"],
                    solution_en: "<p>25.(b) <strong>1-B, 2-C, 3-A.</strong> \"Aye Mere Watan Ke Logo\" is a patriotic song written in Hindi by Kavi Pradeep, composed by C. Ramchandra, and sung by singer Lata Mangeshkar. Jana Gana Mana is the national anthem of the Republic of India. It was originally composed as \"Bharoto Bhagyo Bidhata\" in Bengali by polymath Rabindranath Tagore on 11 December 1911. \"Vande Mataram\" is a Bengali poem by Bankim Chandra Chatterjee.</p>",
                    solution_hi: "<p>25.(b) <strong>1-B, 2-C, 3-A.</strong> \"ऐ मेरे वतन के लोगो\" कवि प्रदीप द्वारा हिंदी में लिखा गया एक देशभक्ति गीत है, जिसे सी. रामचंद्र ने संगीतबद्ध किया है और गायिका लता मंगेशकर ने गाया है। जन गण मन भारत गणराज्य का राष्ट्रगान है। इसे मूल रूप से 11 दिसंबर 1911 को बहुज्ञ रवींद्रनाथ टैगोर द्वारा बंगाली में \"भारतो भाग्यो बिधाता\" के रूप में रचा गया था। \"वंदे मातरम\" बंकिम चंद्र चटर्जी द्वारा रचित एक बंगाली कविता है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>