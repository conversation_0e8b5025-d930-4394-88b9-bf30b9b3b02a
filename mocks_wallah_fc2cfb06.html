<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">90:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 100</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">100</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 90 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["33"] = {
                name: "CBT",
                start: 0,
                end: 98
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="33">CBT</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 99,
                end: 99
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "33",
                    question_en: "<p>1. The arithmetic mean and geometric mean of of two numbers are 7 and 2<math display=\"inline\"><msqrt><mn>10</mn></msqrt></math> respectively, then find the numbers.</p>",
                    question_hi: "<p>1. दो संख्याओं का समान्तर माध्य और गुणोत्तर माध्य क्रमश: 7 और 2<math display=\"inline\"><msqrt><mn>10</mn></msqrt></math> है तो संख्याएं ज्ञात कीजिए।</p>",
                    options_en: ["<p>5, 4</p>", "<p>2, 20</p>", 
                                "<p>4, 10</p>", "<p>8, 5</p>"],
                    options_hi: ["<p>5, 4</p>", "<p>2, 20</p>",
                                "<p>4, 10</p>", "<p>8, 5</p>"],
                    solution_en: "<p>1.(c) <br>Arithmetic mean (AM) = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi mathvariant=\"normal\">a</mi><mo>+</mo><mi mathvariant=\"normal\">b</mi></mrow><mn>2</mn></mfrac></math><br>Geometric mean (GM) =&nbsp;<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mi>ab</mi></msqrt></math><br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi mathvariant=\"normal\">a</mi><mo>+</mo><mi mathvariant=\"normal\">b</mi></mrow><mn>2</mn></mfrac></math> = 7&nbsp;&rArr; a + b = 14........(1)<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mi>ab</mi></msqrt></math> = 2<math display=\"inline\"><msqrt><mn>10</mn></msqrt></math> &rArr; ab = 40.........(2)<br>By solving eq .(1) and (2) we get, <br>a = 4 and b = 10</p>",
                    solution_hi: "<p>1.(c) <br>समान्तर माध्य (AM) = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi mathvariant=\"normal\">a</mi><mo>+</mo><mi mathvariant=\"normal\">b</mi></mrow><mn>2</mn></mfrac></math><br>गुणोत्तर माध्य (GM) =&nbsp;<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mi>ab</mi></msqrt></math><br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi mathvariant=\"normal\">a</mi><mo>+</mo><mi mathvariant=\"normal\">b</mi></mrow><mn>2</mn></mfrac></math> = 7&nbsp;&rArr; a + b = 14........(1)<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mi>ab</mi></msqrt></math> = 2<math display=\"inline\"><msqrt><mn>10</mn></msqrt></math> &rArr; ab = 40.........(2)<br>समीकरण (1) और (2) को हल करने पर हमें <br>a = 4 और b = 10 प्राप्त होता है।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "2",
                    section: "33",
                    question_en: "<p>2. In the given letter-cluster pairs, the first letter-cluster is related to the second letter-cluster following a certain logic. Study the given pairs carefully, and from the given options, select the pair&nbsp;that follows the same logic.<br>GKV : IMX<br>PLR : RNT</p>",
                    question_hi: "<p>2. दिए गए अक्षर-समूह युग्मों में, पहला अक्षर-समूह एक निश्चित तर्क के अनुसार दूसरे अक्षर-समूह से संबंधित है। दिए गए युग्मों का ध्यानपूर्वक अध्ययन कीजिए और दिए गए विकल्पों में से उस युग्म को चुनिए जो समान तर्क का अनुसरण करता हो।<br>GKV : IMX<br>PLR : RNT</p>",
                    options_en: ["<p>UKM : TPJ</p>", "<p>EMH : VXR</p>", 
                                "<p>WDZ : JAP</p>", "<p>JWO : LYQ</p>"],
                    options_hi: ["<p>UKM : TPJ</p>", "<p>EMH : VXR</p>",
                                "<p>WDZ : JAP</p>", "<p>JWO : LYQ</p>"],
                    solution_en: "<p>2.(d) <strong>GKV : IMX</strong><br>G + 2 = I, K + 2 = M, V + 2 = X<br>PLR : RNT<br>P + 2 = R, L + 2 = N, R + 2 = T<br>Similarly,<br>JWO : LYQ<br>J + 2 = L, W + 2 = Y, O + 2 = Q</p>",
                    solution_hi: "<p>2.(d) <strong>GKV : IMX</strong><br>G + 2 = I, K + 2 = M, V + 2 = X<br>PLR : RNT<br>P + 2 = R, L + 2 = N, R + 2 = T<br>इसी प्रकार,<br>JWO : LYQ<br>J + 2 = L, W + 2 = Y, O + 2 = Q</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "3",
                    section: "33",
                    question_en: "<p>3. Which of the following is NOT a classical dance form of South India?</p>",
                    question_hi: "<p>3. निम्नलिखित में से कौन दक्षिण भारत का शास्त्रीय नृत्य नहीं है?</p>",
                    options_en: ["<p>Sattriya</p>", "<p>Mohiniyattam</p>", 
                                "<p>Kathakali</p>", "<p>Bharatanatyam</p>"],
                    options_hi: ["<p>सत्त्रिया</p>", "<p>मोहिनीअट्टम</p>",
                                "<p>कथकली</p>", "<p>भरतनाट्यम</p>"],
                    solution_en: "<p>3.(a) <strong>Sattriya.</strong> Classical dance forms of India - Bharatanatyam (Tamil Nadu), Kathakali (Kerala), Mohiniattam (Kerala), Kuchipudi (Andhra Pradesh), Manipuri Dance (Manipur), Kathak (Uttar Pradesh), Odissi (Odisha), Sattriya Dance (Assam). Chhau dance is the 9th form of classical dance in India according to the Ministry of Culture.</p>",
                    solution_hi: "<p>3.(a) <strong>सत्त्रिया। </strong>भारत के शास्त्रीय नृत्य रूप - भरतनाट्यम (तमिलनाडु), कथकली (केरल), मोहिनीअट्टम (केरल), कुचिपुड़ी (आंध्र प्रदेश), मणिपुरी नृत्य (मणिपुर), कथक (उत्तर प्रदेश), ओडिसी (ओडिशा), सत्त्रिया नृत्य (असम) । संस्कृति मंत्रालय के अनुसार छाऊ नृत्य भारत में शास्त्रीय नृत्य का 9वां रूप है।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "4",
                    section: "33",
                    question_en: "<p>4. A student focuses a sharp image of sun using a spherical mirror on a sheet of paper, which starts to burn after some time. Which of the following statements / statements about the mirror is/are correct?<br>(A) It is concave spherical mirror<br>(B) It has positive focal length<br>(C) It is a converging mirror</p>",
                    question_hi: "<p>4. एक छात्र किसी गोलीय दर्पण का उपयोग करके एक कागज पर सूर्य का एक स्पष्ट प्रतिबिम्ब फोकस करता है, जो कुछ समय बाद जलने लगता है। दर्पण के बारे में इनमे में से कौन से कथन सही हैं?<br>(A) यह अवतल गोलीय दर्पण है<br>(B) इसकी फोकस दूरी धनात्मक है<br>(C) यह एक अभिसारी दर्पण है</p>",
                    options_en: ["<p>Both (A) and (C)</p>", "<p>Both (A) and (B)</p>", 
                                "<p>(A), (B) and (C)</p>", "<p>Both (B) and (C)</p>"],
                    options_hi: ["<p>(A) और (C) दोनों</p>", "<p>(A) और (B) दोनों</p>",
                                "<p>(A), (B) और (C)</p>", "<p>(B) और (C) दोनों</p>"],
                    solution_en: "<p>4.(a) <strong>Both (A) and (C).</strong> A Concave mirror is a Converging mirror on which when parallel rays fall then all the rays get converged at a point which is known as the focus of the Concave mirror and as all the rays coincide at one point and therefore the intensity at that point will increase and that is why when a paper is kept at the focal point of the Concave mirror exposed to sunlight for some time the paper catches fire. The concave mirror forms a virtual and magnified image. The focal length of a Concave mirror is negative because the Focus of a Concave mirror is in front of the mirror.</p>",
                    solution_hi: "<p>4.(a) <strong>(A) और (C) दोनों I</strong> अवतल दर्पण एक अभिसारी दर्पण होता है जिस पर जब समानांतर किरणें पड़ती हैं तो सभी किरणें एक बिंदु पर अभिसरित हो जाती हैं जिसे अवतल दर्पण का फोकस कहा जाता है और चूंकि सभी किरणें एक बिंदु पर मिलती हैं तो उस बिंदु पर तीव्रता बढ़ जाती है और इसीलिए जब किसी कागज़ को अवतल दर्पण से गुजरने वाले सूर्य के प्रकाश के संपर्क में रखा जाता है तो कागज में आग लग जाती है। अवतल दर्पण एक आभासी और आवर्धित प्रतिबिंब बनाता है। अवतल दर्पण की फोकस दूरी ऋणात्मक होती है क्योंकि अवतल दर्पण का फोकस, दर्पण के सामने होता है।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "5",
                    section: "33",
                    question_en: "<p>5. Find the value of 21<sup>2</sup> + 22<sup>2</sup> + 23<sup>2</sup> +... .....+ 30<sup>2</sup></p>",
                    question_hi: "<p>5. 21<sup>2</sup> + 22<sup>2</sup> + 23<sup>2</sup> +... .....+ 30<sup>2</sup>&nbsp;का मान ज्ञात करे</p>",
                    options_en: ["<p>6855</p>", "<p>6585</p>", 
                                "<p>5865</p>", "<p>8565</p>"],
                    options_hi: ["<p>6855</p>", "<p>6585</p>",
                                "<p>5865</p>", "<p>8565</p>"],
                    solution_en: "<p>5.(b) Sum of squares of n natural numbers = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>n</mi><mo>&#215;</mo><mo>(</mo><mi>n</mi><mo>+</mo><mn>1</mn><mo>)</mo><mo>&#215;</mo><mo>(</mo><mn>2</mn><mi>n</mi><mo>+</mo><mn>1</mn><mo>)</mo></mrow><mrow><mn>6</mn><mi>&#160;</mi></mrow></mfrac></math><br>21<sup>2</sup> + 22<sup>2</sup> + 23<sup>2</sup> +... .....+ 30<sup>2</sup><br>= (Sum of square of 1 to 30 ) - (Sum of square of 1 to 20) Sum of square of 1 to 30<br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>30</mn><mo>&#215;</mo><mo>(</mo><mn>30</mn><mo>+</mo><mn>1</mn><mo>)</mo><mo>&#215;</mo><mo>(</mo><mn>2</mn><mo>&#215;</mo><mn>30</mn><mo>+</mo><mn>1</mn><mo>)</mo></mrow><mrow><mn>6</mn><mi>&#160;</mi></mrow></mfrac></math> <br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>930</mn><mo>&#215;</mo><mn>61</mn></mrow><mrow><mn>6</mn><mi>&#160;</mi></mrow></mfrac></math> = 9455<br>Sum of square of 1 to 20 <br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>20</mn><mo>&#215;</mo><mo>(</mo><mn>20</mn><mo>+</mo><mn>1</mn><mo>)</mo><mo>&#215;</mo><mo>(</mo><mn>2</mn><mo>&#215;</mo><mn>20</mn><mo>+</mo><mn>1</mn><mo>)</mo></mrow><mrow><mn>6</mn><mi>&#160;</mi></mrow></mfrac></math> <br>= <math display=\"inline\"><mfrac><mrow><mn>17220</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math> = 2870 <br>Hence ,21<sup>2</sup> + 22<sup>2</sup> + 23<sup>2</sup> +... .....+ 30<sup>2</sup><br>= 9455 - 2870 <br>= 6585</p>",
                    solution_hi: "<p>5.(b) n प्राकृत संख्याओं के वर्गों का योग = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>n</mi><mo>&#215;</mo><mo>(</mo><mi>n</mi><mo>+</mo><mn>1</mn><mo>)</mo><mo>&#215;</mo><mo>(</mo><mn>2</mn><mi>n</mi><mo>+</mo><mn>1</mn><mo>)</mo></mrow><mrow><mn>6</mn><mi>&#160;</mi></mrow></mfrac></math><br>21<sup>2</sup> + 22<sup>2</sup> + 23<sup>2</sup> +... .....+ 30<sup>2</sup><br>= (1 से 30 तक के वर्ग का योग) - (1 से 20 तक के वर्ग का योग) 1 से 30 तक के वर्ग का योग <br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>30</mn><mo>&#215;</mo><mo>(</mo><mn>30</mn><mo>+</mo><mn>1</mn><mo>)</mo><mo>&#215;</mo><mo>(</mo><mn>2</mn><mo>&#215;</mo><mn>30</mn><mo>+</mo><mn>1</mn><mo>)</mo></mrow><mrow><mn>6</mn><mi>&#160;</mi></mrow></mfrac></math> <br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>930</mn><mo>&#215;</mo><mn>61</mn></mrow><mrow><mn>6</mn><mi>&#160;</mi></mrow></mfrac></math> = 9455<br>1 से 20 के वर्ग का योग <br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>20</mn><mo>&#215;</mo><mo>(</mo><mn>20</mn><mo>+</mo><mn>1</mn><mo>)</mo><mo>&#215;</mo><mo>(</mo><mn>2</mn><mo>&#215;</mo><mn>20</mn><mo>+</mo><mn>1</mn><mo>)</mo></mrow><mrow><mn>6</mn><mi>&#160;</mi></mrow></mfrac></math> <br>= <math display=\"inline\"><mfrac><mrow><mn>17220</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math> = 2870<br>अत:,&nbsp;21<sup>2</sup> + 22<sup>2</sup> + 23<sup>2</sup> +... .....+ 30<sup>2</sup><br>= 9455 - 2870 <br>= 6585</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "6",
                    section: "33",
                    question_en: "<p>6. Select the set in which the numbers are related in the same way as are the numbers of the following sets.<br>(NOTE: Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits.)<br>(3, 261, 29)<br>(5, 360, 24)</p>",
                    question_hi: "<p>6. उस समुच्चय का चयन करें जिसमें संख्याएँ उसी तरह से संबंधित हैं जैसे निम्नलिखित सेटों की संख्याएँ हैं। (ध्यान दें: संख्याओं को उनके घटक अंकों में विभाजित किए बिना, पूर्ण संख्याओं पर संचालन किया जाना चाहिए।)<br>(3, 261, 29)<br>(5, 360, 24)</p>",
                    options_en: ["<p>(8, 218, 13)</p>", "<p>(3, 188, 28)</p>", 
                                "<p>(7, 231, 11)</p>", "<p>(4, 218, 26)</p>"],
                    options_hi: ["<p>(8, 218, 13)</p>", "<p>(3, 188, 28)</p>",
                                "<p>(7, 231, 11)</p>", "<p>(4, 218, 26)</p>"],
                    solution_en: "<p>6.(c)<br>(3, 261, 29) = (3 &times; 29) &times; 3 = 261<br>(5, 360, 24) = (5 &times; 24) &times; 3 = 360<br>Similarly,<br>(7, 231, 11) = (7 &times; 11) &times; 3 = 231</p>",
                    solution_hi: "<p>6.(c)<br>(3, 261, 29) = (3 &times; 29) &times; 3 = 261<br>(5, 360, 24) = (5 &times; 24) &times; 3 = 360<br>इसी प्रकार,<br>(7, 231, 11) = (7 &times; 11) &times; 3 = 231</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "7",
                    section: "33",
                    question_en: "<p>7. Tarana Singer Pandit Rattan Mohan Sharma belongs to the _______ gharana.</p>",
                    question_hi: "<p>7. तराना गायक, पंडित रतन मोहन शर्मा _______ घराने से संबंधित हैं।</p>",
                    options_en: ["<p>Mewati</p>", "<p>Agra</p>", 
                                "<p>Indore</p>", "<p>Dilli</p>"],
                    options_hi: ["<p>मेवाती</p>", "<p>आगरा</p>",
                                "<p>इंदौर</p>", "<p>दिल्ली</p>"],
                    solution_en: "<p>7.(a) <strong>Mewati.</strong> Rattan Mohan Sharma is an Indian classical vocalist. He performs classical music forms such as Khayal and Tarana as well as light classical forms such as Haveli Sangeet, Tappa and Bhajan as well as Rajasthani Folk. Famous singers of: Mewati Gharana - Pandit Jasraj, Sanjeev and Shobha Abhyankar, Bande Ali Khan, Jaiwant Singhji Vaghela; Agra Gharana - Hajusujan Khan (Founder), Ustad Faiyaz Khan, Sujan Khan Deepak Jyoti, Dayam Khan Sur Gyan; Delhi Gharana - Ustad Aman Ali Khan, Shashikala Koratar, Anjanibai Malpekar.</p>",
                    solution_hi: "<p>7.(a) <strong>मेवाती। </strong>रतन मोहन शर्मा एक भारतीय शास्त्रीय गायक हैं। वह खयाल और तराना जैसे शास्त्रीय संगीत रूपों के साथ-साथ हवेली संगीत, टप्पा और भजन जैसे हल्के शास्त्रीय रूपों के साथ-साथ राजस्थानी लोक भी प्रस्तुत करते हैं। मेवाती घराने के प्रसिद्ध गायक - पंडित जसराज, संजीव और शोभा अभ्यंकर, बंदे अली खान, जयवंत सिंहजी वाघेला; आगरा घराना - हजुसुजान खान (संस्थापक), उस्ताद फैयाज खान, सुजान खान दीपक ज्योति, दायम खान सूर ज्ञान; दिल्ली घराना - उस्ताद अमान अली खान, शशिकला कोराटर, अंजनीबाई मालपेकर।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "8",
                    section: "33",
                    question_en: "<p>8. The friction in liquids is called:</p>",
                    question_hi: "<p>8. द्रवों में घर्षण को क्या कहा जाता है :</p>",
                    options_en: ["<p>Morbidity</p>", "<p>Viscosity</p>", 
                                "<p>Rancidity</p>", "<p>Rigidity</p>"],
                    options_hi: ["<p>विकृति (Morbidity)</p>", "<p>श्यानता (Viscosity)</p>",
                                "<p>विकृतगंधता (Rancidity)</p>", "<p>कठोरता (Rigidity)</p>"],
                    solution_en: "<p>8.(b)<strong> Viscosity</strong>: Measure of a fluid&rsquo;s resistance to flow. SI unit of viscosity is N-s/m<sup>2 </sup>or pascal-second (Pa - s). Example - Honey, Molasses, Melted chocolate. Rigidity - The inability of a solid to change its shape. Example - Table, chair, fan.</p>",
                    solution_hi: "<p>8.(b)<strong> श्यानता (Viscosity): </strong>किसी द्रव पदार्थ के प्रवाह के प्रतिरोध की माप है । श्यानता की SI इकाई N-s/m<sup>2</sup> या पास्कल-सेकंड (Pa-s) है। उदाहरण - शहद, गुड़, पिघली हुई चॉकलेट। कठोरता (Rigidity) - किसी ठोस का अपना आकार बदलने में असमर्थता। उदाहरण - मेज, कुर्सी, पंखा।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "9",
                    section: "33",
                    question_en: "<p>9. The HCF of any set of 10 co-prime numbers is always</p>",
                    question_hi: "<p>9. 10 सह-अभाज्य संख्याओं के किसी भी सेट का HCF हमेशा होता है</p>",
                    options_en: ["<p>1</p>", "<p>10</p>", 
                                "<p>0</p>", "<p>2</p>"],
                    options_hi: ["<p>1</p>", "<p>10</p>",
                                "<p>0</p>", "<p>2</p>"],
                    solution_en: "<p>9.(a) The H.C.F of any two co-prime numbers always = 1.</p>",
                    solution_hi: "<p>9.(a) हमेशा किन्हीं दो सह-अभाज्य संख्याओं का H.C.F = 1</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "10",
                    section: "33",
                    question_en: "<p>10. Pick the odd one out.<br>225,441,1225,961,343</p>",
                    question_hi: "<p>10. विजातीय को चुनिए <br>225,441,1225,961,343</p>",
                    options_en: ["<p>343</p>", "<p>961</p>", 
                                "<p>1225</p>", "<p>225</p>"],
                    options_hi: ["<p>343</p>", "<p>961</p>",
                                "<p>1225</p>", "<p>225</p>"],
                    solution_en: "<p>10.(a) All are squares of a number but 343 is cube.</p>",
                    solution_hi: "<p>10.(a) सभी एक संख्या के वर्ग हैं लेकिन 343 घन है।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "11",
                    section: "33",
                    question_en: "<p>11. In Natya Shastra, Bharat Muni has clubbed musical instruments into how many groups?</p>",
                    question_hi: "<p>11. नाट्य शास्त्र में भरत मुनि ने वाद्ययंत्रों को कितने समूहों में बांटा है?</p>",
                    options_en: ["<p>7</p>", "<p>6</p>", 
                                "<p>4</p>", "<p>3</p>"],
                    options_hi: ["<p>7</p>", "<p>6</p>",
                                "<p>4</p>", "<p>3</p>"],
                    solution_en: "<p>11.(c) <strong>4</strong>. Natya Shastra by Bharat Muni (composed between 200 BC and 200 AD) clubbed musical instruments into four groups: Avanaddha Vadya (membranophones or percussion instruments), Ghan Vadya (idiophones or solid instruments), Sushir Vadya (aerophones or wind instruments), and Tat Vadya (chordophones or stringed instruments).</p>",
                    solution_hi: "<p>11.(c) <strong>4 </strong>। भरत मुनि द्वारा रचित नाट्य शास्त्र (200 ईसा पूर्व और 200 ईस्वी के बीच रचित) ने संगीत वाद्ययंत्रों को चार समूहों में बांटा: अवनद्ध वाद्य (झिल्लीदार वाद्य या ताल वाद्य), घन वाद्य (इडियोफोन या ठोस वाद्ययंत्र), सुशीर वाद्य (एयरोफोन या वायु वाद्ययंत्र), और तत् वाद्य (कॉर्डोफोन या तार वाले वाद्ययंत्र)।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "12",
                    section: "33",
                    question_en: "<p>12. A metallic wire having resistivity &rho; is cut into four equal parts. The resistivity of each part is:</p>",
                    question_hi: "<p>12. &rho; प्रतिरोधकता वाले धातु के तार को चार बराबर भागों में काटा जाता है। प्रत्येक भाग की प्रतिरोधकता क्या है?</p>",
                    options_en: ["<p><math display=\"inline\"><mfrac><mrow><mi>&#961;</mi></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mi>&#961;</mi></mrow><mrow><mn>4</mn></mrow></mfrac></math></p>", 
                                "<p>&rho;</p>", "<p>4&rho;</p>"],
                    options_hi: ["<p><math display=\"inline\"><mfrac><mrow><mi>&#961;</mi></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mi>&#961;</mi></mrow><mrow><mn>4</mn></mrow></mfrac></math></p>",
                                "<p>&rho;</p>", "<p>4&rho;</p>"],
                    solution_en: "<p>12.(c) <strong>&rho;.</strong> Resistivity: Resistance offered by an object per unit length and per unit cross sectional area at a specified temperature. As resistivity depends upon the material of the wire and temperature. If we cut the wire into 4 equal parts then the length of the wire will be changed but the resistivity of the wire will remain the same i.e.(⍴). SI unit of Resistivity is ohm m.</p>",
                    solution_hi: "<p>12.(c) <strong>&rho;. </strong>प्रतिरोधकता (resistivity): एक विशिष्ट तापमान पर प्रति इकाई लंबाई और प्रति इकाई अनुप्रस्थ काट के क्षेत्रफल द्वारा प्रतिरोध की पेशकश की जाती है। चूंकि प्रतिरोधकता तार के पदार्थ और तापमान निर्भर करती है। यदि हम तार को 4 बराबर भागों में काट दें तो तार की लंबाई परिवर्तित हो जाएगी लेकिन तार की प्रतिरोधकता समान रहेगी । प्रतिरोधकता (⍴) की SI इकाई ओम मीटर है।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "13",
                    section: "33",
                    question_en: "<p>13. If <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msqrt><mn>5</mn></msqrt><mo>-</mo><mn>1</mn></mrow><mrow><msqrt><mn>5</mn></msqrt><mo>+</mo><mn>1</mn></mrow></mfrac></math> - <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msqrt><mn>5</mn></msqrt><mo>+</mo><mn>1</mn></mrow><mrow><msqrt><mn>5</mn></msqrt><mo>-</mo><mn>1</mn></mrow></mfrac></math> = a + b<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>5</mn></msqrt></math>, then the value of (a + b) (a - b) &divide; a<sup>2</sup> + b<sup>2</sup> is:</p>",
                    question_hi: "<p>13. यदि <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msqrt><mn>5</mn></msqrt><mo>-</mo><mn>1</mn></mrow><mrow><msqrt><mn>5</mn></msqrt><mo>+</mo><mn>1</mn></mrow></mfrac></math> - <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msqrt><mn>5</mn></msqrt><mo>+</mo><mn>1</mn></mrow><mrow><msqrt><mn>5</mn></msqrt><mo>-</mo><mn>1</mn></mrow></mfrac></math> = a + b<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>5</mn></msqrt></math>, तो (a + b) (a - b) &divide; a<sup>2</sup> + b<sup>2</sup> का मान है:</p>",
                    options_en: ["<p>-2</p>", "<p>0</p>", 
                                "<p>-1</p>", "<p>3</p>"],
                    options_hi: ["<p>-2</p>", "<p>0</p>",
                                "<p>-1</p>", "<p>3</p>"],
                    solution_en: "<p>13.(c) <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msqrt><mn>5</mn></msqrt><mo>-</mo><mn>1</mn></mrow><mrow><msqrt><mn>5</mn></msqrt><mo>+</mo><mn>1</mn></mrow></mfrac></math> - <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msqrt><mn>5</mn></msqrt><mo>+</mo><mn>1</mn></mrow><mrow><msqrt><mn>5</mn></msqrt><mo>-</mo><mn>1</mn></mrow></mfrac></math> = a + b<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>5</mn></msqrt></math><br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><msqrt><mn>5</mn></msqrt><mo>-</mo><mn>1</mn><mo>)</mo><mo>(</mo><msqrt><mn>5</mn></msqrt><mo>-</mo><mn>1</mn><mo>)</mo><mo>-</mo><mo>(</mo><msqrt><mn>5</mn></msqrt><mo>+</mo><mn>1</mn><mo>)</mo><mo>(</mo><msqrt><mn>5</mn></msqrt><mo>+</mo><mn>1</mn><mo>)</mo></mrow><mrow><mo>(</mo><msqrt><mn>5</mn></msqrt><mo>-</mo><mn>1</mn><mo>)</mo><mo>(</mo><msqrt><mn>5</mn></msqrt><mo>+</mo><mn>1</mn><mo>)</mo></mrow></mfrac></math> = a + b<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>5</mn></msqrt></math><br>&rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mo>-</mo><mn>2</mn><msqrt><mn>5</mn></msqrt><mo>+</mo><mn>1</mn><mo>-</mo><mn>5</mn><mo>-</mo><mn>2</mn><msqrt><mn>5</mn></msqrt><mo>-</mo><mn>1</mn></mrow><mrow><mo>(</mo><msqrt><mn>5</mn></msqrt><mo>-</mo><mn>1</mn><mo>)</mo><mo>(</mo><msqrt><mn>5</mn></msqrt><mo>+</mo><mn>1</mn><mo>)</mo></mrow></mfrac></math> = a + b<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>5</mn></msqrt></math><br>&rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>-</mo><mn>4</mn><msqrt><mn>5</mn></msqrt></mrow><mn>4</mn></mfrac></math> = a + b<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>5</mn></msqrt></math> - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>5</mn></msqrt></math> = a + b<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>5</mn></msqrt></math><br>a = 0, b = -1<br>So, (a + b) (a - b) &divide;&nbsp;(a<sup>2</sup> + b<sup>2</sup>)&nbsp;<br>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi mathvariant=\"normal\">a</mi><mn>2</mn></msup><mo>-</mo><msup><mi mathvariant=\"normal\">b</mi><mn>2</mn></msup></mrow><mrow><msup><mi mathvariant=\"normal\">a</mi><mn>2</mn></msup><mo>+</mo><msup><mi mathvariant=\"normal\">b</mi><mn>2</mn></msup></mrow></mfrac></math> = -1</p>",
                    solution_hi: "<p>13.(c) <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msqrt><mn>5</mn></msqrt><mo>-</mo><mn>1</mn></mrow><mrow><msqrt><mn>5</mn></msqrt><mo>+</mo><mn>1</mn></mrow></mfrac></math> - <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msqrt><mn>5</mn></msqrt><mo>+</mo><mn>1</mn></mrow><mrow><msqrt><mn>5</mn></msqrt><mo>-</mo><mn>1</mn></mrow></mfrac></math> = a + b<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>5</mn></msqrt></math><br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><msqrt><mn>5</mn></msqrt><mo>-</mo><mn>1</mn><mo>)</mo><mo>(</mo><msqrt><mn>5</mn></msqrt><mo>-</mo><mn>1</mn><mo>)</mo><mo>-</mo><mo>(</mo><msqrt><mn>5</mn></msqrt><mo>+</mo><mn>1</mn><mo>)</mo><mo>(</mo><msqrt><mn>5</mn></msqrt><mo>+</mo><mn>1</mn><mo>)</mo></mrow><mrow><mo>(</mo><msqrt><mn>5</mn></msqrt><mo>-</mo><mn>1</mn><mo>)</mo><mo>(</mo><msqrt><mn>5</mn></msqrt><mo>+</mo><mn>1</mn><mo>)</mo></mrow></mfrac></math> = a + b<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>5</mn></msqrt></math><br>&rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mo>-</mo><mn>2</mn><msqrt><mn>5</mn></msqrt><mo>+</mo><mn>1</mn><mo>-</mo><mn>5</mn><mo>-</mo><mn>2</mn><msqrt><mn>5</mn></msqrt><mo>-</mo><mn>1</mn></mrow><mrow><mo>(</mo><msqrt><mn>5</mn></msqrt><mo>-</mo><mn>1</mn><mo>)</mo><mo>(</mo><msqrt><mn>5</mn></msqrt><mo>+</mo><mn>1</mn><mo>)</mo></mrow></mfrac></math> = a + b<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>5</mn></msqrt></math><br>&rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>-</mo><mn>4</mn><msqrt><mn>5</mn></msqrt></mrow><mn>4</mn></mfrac></math> = a + b<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>5</mn></msqrt></math> - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>5</mn></msqrt></math> = a + b<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>5</mn></msqrt></math><br>a = 0, b = -1<br>So, (a + b) (a - b) &divide;&nbsp;(a<sup>2</sup> + b<sup>2</sup>)&nbsp;<br>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi mathvariant=\"normal\">a</mi><mn>2</mn></msup><mo>-</mo><msup><mi mathvariant=\"normal\">b</mi><mn>2</mn></msup></mrow><mrow><msup><mi mathvariant=\"normal\">a</mi><mn>2</mn></msup><mo>+</mo><msup><mi mathvariant=\"normal\">b</mi><mn>2</mn></msup></mrow></mfrac></math> = -1</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "14",
                    section: "33",
                    question_en: "<p>14. In a certain code language: &lsquo;V O C A M&rsquo; is written as &lsquo;S&amp; , U* , (# , !* , O = &rsquo; &lsquo;E R A S E&rsquo; is written as &lsquo;g@ , + S , g@ , DD , S&amp;&rsquo;. Which of the following can be the way in which &lsquo;CAMERA&rsquo; is written in that code language?</p>",
                    question_hi: "<p>14. एक निश्चित कोड भाषा में &lsquo;V O C A M&rsquo; को &lsquo;S&amp;, U*, (#, !*, O=&rsquo; के रूप में लिखा जाता है. \'ERASE\' को \'g@,+S, g@,DD,S&amp;p\' लिखा जाता है निम्नलिखित में से कौन सा तरीका हो सकता है जिस तरह से उस कोड भाषा में \'CAMERA\' लिखा जाता है?</p>",
                    options_en: ["<p>DD , g@ , U8 , (# , O = , *%)</p>", "<p>1*, O = , $&amp; , + S, g@ , DD</p>", 
                                "<p>(# , g@ , DD , g@ , $&amp; , U*</p>", "<p>U* , !* , DD , $&amp; , $&amp; , g@</p>"],
                    options_hi: ["<p>DD, g@,U8, (#,O=,*%)</p>", "<p>1*, O=,$&amp;, + S, g@, DD</p>",
                                "<p>(#, g@, DD, g@,$&amp;, U*</p>", "<p>U*,!*,DD, $&amp;,$&amp;,g@</p>"],
                    solution_en: "<p>14.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1738229057861.png\" alt=\"rId4\" width=\"303\" height=\"57\"><br>By the given code , A is coded as &lsquo;S&amp;&rsquo;&nbsp;and E is coded as &lsquo;g@&rsquo;<br>In CAMERA alphabet A is repeated twice so &lsquo;S&amp;&rsquo; should be there twice and only in option (D) &lsquo;S&amp;&rsquo; repeated twice.<br>So, CAMERA is written as U*,!*,DD, $&amp;,$&amp;,g@</p>",
                    solution_hi: "<p>14.(d)<br><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1738229057861.png\" alt=\"rId4\" width=\"303\" height=\"57\"><br>दिए गए कोड से, By the given code , A को &lsquo;S&amp;&rsquo; के रूप में कोडित किया गया है। और&nbsp;E को &lsquo;g@&rsquo; के रूप में कोडित किया गया है। <br>CAMERA में अक्षर A दो बार दोहराया गया है तो &lsquo;S&amp;&rsquo; दो बार होना चाहिए और केवल विकल्प (D) में &lsquo;S&amp;&rsquo; दो बार है <br>इसलिए CAMERA को U*,!*,DD, $&amp;,$&amp;,g@ लिखा जाएगा ।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "15",
                    section: "33",
                    question_en: "<p>15. Sindhu Darshan festival is celebrated in which part of India?</p>",
                    question_hi: "<p>15. सिंधु दर्शन उत्सव भारत के किस भाग में मनाया जाता है?</p>",
                    options_en: ["<p>Punjab</p>", "<p>Ladakh</p>", 
                                "<p>Sikkim</p>", "<p>Uttar Pradesh</p>"],
                    options_hi: ["<p>पंजाब</p>", "<p>लद्दाख</p>",
                                "<p>सिक्किम</p>", "<p>उत्तर प्रदेश</p>"],
                    solution_en: "<p>15.(b) <strong>Ladakh (Leh).</strong> This festival is held every year in June on the full moon day of Guru Purnima. Festivals of Ladakh - Hemis, Losar, Phyang Tsedup, Dosmoche, Saka Dawa. Punjab - Lohri, Baisakhi, Hola Mohalla, Gurupurab and Teeyan. Uttar Pradesh - Buddha Purnima, Muharram, Ganga Dussehra, Janmashtami. Sikkim - Losoong, Sonam Lhochhar, Losar, Bhumchu, Chaite Dashain, Saga Dawa.</p>",
                    solution_hi: "<p>15.(b) <strong>लद्दाख (लेह)।</strong> यह त्यौहार हर वर्ष जून में गुरु पूर्णिमा की पूर्णिमा के दिन आयोजित किया जाता है। लद्दाख के महोत्सव - हेमिस, लोसर, फ्यांग टेडुप, दोसमोचे, साका दावा। पंजाब - लोहड़ी, बैसाखी, होला मोहल्ला, गुरुपुरब और तीयां। उत्तर प्रदेश - बुद्ध पूर्णिमा, मुहर्रम, गंगा दशहरा, जन्माष्टमी। सिक्किम - लोसांग, सोनम ल्होचर, लोसर, भूमचू, चैते दशाईं, सागा दावा।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "16",
                    section: "33",
                    question_en: "<p>16. Three bulbs A, B and C rated 40 W, 60 W and 100 W respectively are connected in parallel to a voltage source of 220 V. the bulb that glows with maximum brightness is:</p>",
                    question_hi: "<p>16. क्रमशः 40 W, 60 W और 100 W की रेटिंग वाले तीन बल्ब A, B और C 220 V के वोल्टेज स्रोत के साथ समानांतर क्रम में संयोजित हैं। अधिकतम दीप्ति के साथ चमकने वाला बल्ब कौन सा है?</p>",
                    options_en: ["<p>all bulbs will glow with equal brightness</p>", "<p>bulb C</p>", 
                                "<p>bulb A</p>", "<p>bulb B</p>"],
                    options_hi: ["<p>सभी बल्ब समान दीप्ति से चमकेंगे</p>", "<p>बल्ब C</p>",
                                "<p>बल्ब A</p>", "<p>बल्ब B</p>"],
                    solution_en: "<p>16.(b) <strong>bulb C.</strong><br>Three bulbs are connected in parallel&nbsp;P = <math display=\"inline\"><mfrac><mrow><msup><mrow><mi mathvariant=\"bold-italic\">V</mi></mrow><mrow><mn>2</mn></mrow></msup></mrow><mrow><mi mathvariant=\"bold-italic\">R</mi></mrow></mfrac></math><br>When voltage is constant then we can say that P &prop; <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mi>R</mi></mrow></mfrac></math>.<br>So when R increases, power decreases and vice versa.<br>Bulb A: P = <math display=\"inline\"><mfrac><mrow><msup><mrow><mi>V</mi></mrow><mrow><mn>2</mn></mrow></msup></mrow><mrow><mi>R</mi></mrow></mfrac></math> &rArr; R<sub>A</sub> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mi mathvariant=\"normal\">V</mi><mn>2</mn></msup><mi mathvariant=\"normal\">P</mi></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mn>220</mn><mn>2</mn></msup><mn>40</mn></mfrac></math>= 1210 &Omega; <br>Bulb B:P = <math display=\"inline\"><mfrac><mrow><msup><mrow><mi>V</mi></mrow><mrow><mn>2</mn></mrow></msup></mrow><mrow><mi>R</mi></mrow></mfrac></math> &rArr; R<sub>B</sub> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mi mathvariant=\"normal\">V</mi><mn>2</mn></msup><mi mathvariant=\"normal\">P</mi></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mn>220</mn><mn>2</mn></msup><mn>60</mn></mfrac></math> = 806 &Omega; <br>Bulb C : P = <math display=\"inline\"><mfrac><mrow><msup><mrow><mi>V</mi></mrow><mrow><mn>2</mn></mrow></msup></mrow><mrow><mi>R</mi></mrow></mfrac></math> &rArr; R<sub>C</sub> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mi mathvariant=\"normal\">V</mi><mn>2</mn></msup><mi mathvariant=\"normal\">P</mi></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mn>220</mn><mn>2</mn></msup><mn>100</mn></mfrac></math>= 484 &Omega;&nbsp;<br>So, R<sub>C</sub> &lt; R<sub>B</sub> &lt; R<sub>A</sub> &rArr; P<sub>C</sub> &gt; P<sub>B</sub> &gt; P<sub>A</sub><br>&rArr; Bulb C will glow brightest.</p>",
                    solution_hi: "<p>16.(b) <strong>बल्ब C।</strong><br>तीन बल्ब समानांतर में जुड़े हुए हैं&nbsp;P = <math display=\"inline\"><mfrac><mrow><msup><mrow><mi mathvariant=\"bold-italic\">V</mi></mrow><mrow><mn>2</mn></mrow></msup></mrow><mrow><mi mathvariant=\"bold-italic\">R</mi></mrow></mfrac></math><br>जब वोल्टेज स्थिर होता है तब हम ऐसा कह सकते हैं P &prop; <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mi>R</mi></mrow></mfrac></math>.<br>इसलिए जब R बढ़ता है, शक्ति कम हो जाती है, और जब R घटता है , शक्ति बढ़ जाती है । <br>बल्ब A: P =&nbsp;<math display=\"inline\"><mfrac><mrow><msup><mrow><mi>V</mi></mrow><mrow><mn>2</mn></mrow></msup></mrow><mrow><mi>R</mi></mrow></mfrac></math> &rArr; R<sub>A</sub> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mi mathvariant=\"normal\">V</mi><mn>2</mn></msup><mi mathvariant=\"normal\">P</mi></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mn>220</mn><mn>2</mn></msup><mn>40</mn></mfrac></math>= 1210 &Omega; <br>बल्ब B:P =&nbsp;<math display=\"inline\"><mfrac><mrow><msup><mrow><mi>V</mi></mrow><mrow><mn>2</mn></mrow></msup></mrow><mrow><mi>R</mi></mrow></mfrac></math> &rArr; R<sub>B</sub> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mi mathvariant=\"normal\">V</mi><mn>2</mn></msup><mi mathvariant=\"normal\">P</mi></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mn>220</mn><mn>2</mn></msup><mn>60</mn></mfrac></math> = 806 &Omega; <br>बल्ब C : P =&nbsp;<math display=\"inline\"><mfrac><mrow><msup><mrow><mi>V</mi></mrow><mrow><mn>2</mn></mrow></msup></mrow><mrow><mi>R</mi></mrow></mfrac></math> &rArr; R<sub>C</sub> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mi mathvariant=\"normal\">V</mi><mn>2</mn></msup><mi mathvariant=\"normal\">P</mi></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mn>220</mn><mn>2</mn></msup><mn>100</mn></mfrac></math>= 484 &Omega;&nbsp;<br>इसलिए, R<sub>C</sub> &lt; R<sub>B</sub> &lt; R<sub>A</sub> &rArr; P<sub>C</sub> &gt; P<sub>B</sub> &gt; P<sub>A</sub><br>बल्ब C सबसे ज्यादा चमकेगा।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "17",
                    section: "33",
                    question_en: "<p>17. The value of [x<sup>m(n-p)</sup>.x<sup>n(p-m)</sup>.x<sup>p(m-n)</sup>] [<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mroot><mn>625</mn><mn>4</mn></mroot><mo>-</mo><msqrt><mn>25</mn></msqrt></math>]&nbsp;is _______.</p>",
                    question_hi: "<p>17. [x<sup>m(n-p)</sup>.x<sup>n(p-m)</sup>.x<sup>p(m-n)</sup>] [<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mroot><mn>625</mn><mn>4</mn></mroot><mo>-</mo><msqrt><mn>25</mn></msqrt></math>] का मान _______ है।</p>",
                    options_en: ["<p>1</p>", "<p>2</p>", 
                                "<p>5</p>", "<p>0</p>"],
                    options_hi: ["<p>1</p>", "<p>2</p>",
                                "<p>5</p>", "<p>0</p>"],
                    solution_en: "<p>17.(d)<br>[x<sup>m(n-p)</sup>.x<sup>n(p-m)</sup>.x<sup>p(m-n)</sup>] [<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mroot><mn>625</mn><mn>4</mn></mroot><mo>-</mo><msqrt><mn>25</mn></msqrt></math>]<br>= [x<sup>mn-mp</sup>.x<sup>np-nm</sup>. x<sup>pm-pn</sup>] [<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mroot><mn>625</mn><mn>4</mn></mroot><mo>-</mo><msqrt><mn>25</mn></msqrt></math>]<br>= [x<sup>0</sup>] &times; (5 - 5) <br>= 0</p>",
                    solution_hi: "<p>17.(d)<br>[x<sup>m(n-p)</sup>.x<sup>n(p-m)</sup>.x<sup>p(m-n)</sup>] [<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mroot><mn>625</mn><mn>4</mn></mroot><mo>-</mo><msqrt><mn>25</mn></msqrt></math>]<br>= [x<sup>mn-mp</sup>.x<sup>np-nm</sup>. x<sup>pm-pn</sup>] [<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mroot><mn>625</mn><mn>4</mn></mroot><mo>-</mo><msqrt><mn>25</mn></msqrt></math>]<br>= [x<sup>0</sup>] &times; (5 - 5) <br>= 0</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "18",
                    section: "33",
                    question_en: "<p>18. If MOANA is written as 132 in a particular code language, how will CROOD be written as in that language?</p>",
                    question_hi: "<p>18. किसी विशेष कूट भाषा में MOANA को 132 लिखा जाता है, उसी भाषा में CROOD को किस प्रकार लिखा जाएगा?</p>",
                    options_en: ["<p>55</p>", "<p>158</p>", 
                                "<p>173</p>", "<p>165</p>"],
                    options_hi: ["<p>55</p>", "<p>158</p>",
                                "<p>173</p>", "<p>165</p>"],
                    solution_en: "<p>18.(d) <strong>Logic:-</strong><br>M + O + A + N + A = 13 + 15 + 1 + 14 + 1 = 44 then 44 &times; 3 = 132<br>Similarly , C + R + O + O + D <br>= 3 + 18 + 15 + 15 + 4 = 55 , <br>then 55&nbsp;&times; 3 = 165</p>",
                    solution_hi: "<p>18.(d) <strong>तर्क -</strong><br>M + O + A + N + A = 13 + 15 + 1 + 14 + 1 = 44 फिर 44 &times; 3 = 132<br>इसी तरह, C + R + O + O + D <br>= 3 + 18 + 15 + 15 + 4 = 55 , <br>फिर 55 &times; 3 = 165</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "19",
                    section: "33",
                    question_en: "<p>19. Maru Sri (or Maru Shri) is a desert pageant held at the _______.</p>",
                    question_hi: "<p>19. मरु श्री, _______के दौरान आयोजित की जाने वाली एक रेगिस्तानी शोभायात्रा है।</p>",
                    options_en: ["<p>Pushkar Fair</p>", "<p>Hornbill Festival</p>", 
                                "<p>Jaisalmer Desert Festival</p>", "<p>Goa Carnival</p>"],
                    options_hi: ["<p>पुष्कर मेला</p>", "<p>हॉर्नबिल महोत्सव</p>",
                                "<p>जैसलमेर डेजर्ट फेस्टिवल</p>", "<p>गोवा कार्निवल</p>"],
                    solution_en: "<p>19.(c) <strong>Jaisalmer Desert Festival </strong>(annual event that takes place in Thar Desert in February, dedicated to Lord Krishna). There is a unique competition for men by the name Maru Sri or Mr. Desert Pageant. Famous festivals of Rajasthan - Pushkar festival, Kumbhalgarh festival, Winter Festival (Mount Abu), Camel Fair (Bikaner), Kite Festival (Jaipur), Nagaur Fair, Mewar Festival (Udaipur), Abhaneri Festival (Dausa), Ranakpur Mahotsav (Pali).</p>",
                    solution_hi: "<p>19.(c) <strong>जैसलमेर रेगिस्तान महोत्सव</strong> (फरवरी में थार रेगिस्तान में होने वाला वार्षिक कार्यक्रम, भगवान कृष्ण को समर्पित)। यहां पुरुषों के लिए मारू श्री या मिस्टर डेजर्ट पेजेंट नाम से एक अनोखी प्रतियोगिता होती है। राजस्थान के प्रसिद्ध त्यौहार - पुष्कर महोत्सव, कुम्भलगढ़ महोत्सव, विंटर महोत्सव (माउंट आबू), ऊँट मेला (बीकानेर), पतंग महोत्सव (जयपुर), नागौर मेला, मेवाड़ महोत्सव (उदयपुर), आभानेरी महोत्सव (दौसा), रणकपुर महोत्सव (पाली)।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "20",
                    section: "33",
                    question_en: "<p>20. A spring balance is a device commonly used for measuring the _____ acting on an object.</p>",
                    question_hi: "<p>20. स्प्रिंग बैलेंस एक उपकरण है जिसका उपयोग आमतौर पर किसी वस्तु पर कार्य करने वाले _____ को मापने के लिए किया जाता है।</p>",
                    options_en: ["<p>force</p>", "<p>momentum</p>", 
                                "<p>velocity</p>", "<p>mass</p>"],
                    options_hi: ["<p>बल</p>", "<p>गति</p>",
                                "<p>वेग</p>", "<p>द्रव्यमान</p>"],
                    solution_en: "<p>20.(a)<strong> force (F)</strong> - SI unit - Newton, F = m (mass) &times; a (acceleration), a vector quantity, Spring balance:- Hooke\'s Law, which states that the force needed to extend a spring is proportional to the distance that spring is extended from its rest position. Momentum (p):- SI unit: (kg&sdot;m/s), p = m (mass) &times; v (velocity), a vector quantity. Velocity (V):- It is the rate of change of distance. SI unit:- m/s. Mass (m) :- SI unit :- kg, Mass = &rho; (density) &times; v (volume), a scalar quantity.</p>",
                    solution_hi: "<p>20.(a) <strong>बल (F) </strong>- SI मात्रक - न्यूटन, F = m (द्रव्यमान) &times; a (त्वरण), एक वेक्टर मात्रा, स्प्रिंग बैलेंस: - हुक का नियम, जो बताता है कि स्प्रिंग को फैलाने के लिए आवश्यक बल स्प्रिंग की दूरी के समानुपाती होता है इसकी विश्राम स्थिति से बढ़ाया गया है। संवेग (p):- SI मात्रक : (kg&sdot;m/s), p = m (द्रव्यमान) &times; v (वेग), सदिश राशि है। वेग (v):- यह दूरी के परिवर्तन की दर है। SI मात्रक :- मीटर/सेकंड। द्रव्यमान (m):- SI मात्रक :- किग्रा, द्रव्यमान = &rho; (घनत्व) &times; v (आयतन), अदिश राशि है।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "21",
                    section: "33",
                    question_en: "<p>21. If a &oplus;&nbsp;b = a - b + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msqrt><mi>ab</mi></msqrt></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mo>(</mo><mfrac><mi>a</mi><mi>b</mi></mfrac><mo>)</mo></msqrt></math> , then 0.9 &oplus; 0.1 = ?</p>",
                    question_hi: "<p>21. यदि a &oplus; b = a - b + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msqrt><mi>ab</mi></msqrt></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mo>(</mo><mfrac><mi>a</mi><mi>b</mi></mfrac><mo>)</mo></msqrt></math> , तो 0.9 &oplus; 0.1 = ?&nbsp;</p>",
                    options_en: ["<p><math display=\"inline\"><mfrac><mrow><mn>30</mn></mrow><mrow><mn>212</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>214</mn></mrow><mrow><mn>30</mn></mrow></mfrac></math></p>", 
                                "<p><math display=\"inline\"><mfrac><mrow><mn>212</mn></mrow><mrow><mn>30</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>30</mn></mrow><mrow><mn>214</mn></mrow></mfrac></math></p>"],
                    options_hi: ["<p><math display=\"inline\"><mfrac><mrow><mn>30</mn></mrow><mrow><mn>212</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>214</mn></mrow><mrow><mn>30</mn></mrow></mfrac></math></p>",
                                "<p><math display=\"inline\"><mfrac><mrow><mn>212</mn></mrow><mrow><mn>30</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>30</mn></mrow><mrow><mn>214</mn></mrow></mfrac></math></p>"],
                    solution_en: "<p>21.(b) 0.9 &oplus; 0.1<br>= 0.9 - 0.1 +&nbsp;<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msqrt><mn>0</mn><mo>.</mo><mn>9</mn><mo>&#215;</mo><mn>0</mn><mo>.</mo><mn>1</mn></msqrt></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mfrac><mrow><mn>0</mn><mo>.</mo><mn>9</mn></mrow><mrow><mn>0</mn><mo>.</mo><mn>1</mn></mrow></mfrac></msqrt></math><br>= 0.8 + <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>0</mn><mo>.</mo><mn>3</mn></mrow></mfrac></math> + 3 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>8</mn><mn>10</mn></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>10</mn><mn>3</mn></mfrac></math> + 3<br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>24</mn><mo>+</mo><mn>100</mn><mo>+</mo><mn>90</mn></mrow><mn>30</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>214</mn><mn>30</mn></mfrac></math></p>",
                    solution_hi: "<p>21.(b) 0.9 &oplus; 0.1<br>= 0.9 - 0.1 +&nbsp;<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msqrt><mn>0</mn><mo>.</mo><mn>9</mn><mo>&#215;</mo><mn>0</mn><mo>.</mo><mn>1</mn></msqrt></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mfrac><mrow><mn>0</mn><mo>.</mo><mn>9</mn></mrow><mrow><mn>0</mn><mo>.</mo><mn>1</mn></mrow></mfrac></msqrt></math><br>= 0.8 + <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>0</mn><mo>.</mo><mn>3</mn></mrow></mfrac></math> + 3 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>8</mn><mn>10</mn></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>10</mn><mn>3</mn></mfrac></math> + 3<br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>24</mn><mo>+</mo><mn>100</mn><mo>+</mo><mn>90</mn></mrow><mn>30</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>214</mn><mn>30</mn></mfrac></math></p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "22",
                    section: "33",
                    question_en: "<p>22. What should come in place of \'?\' in the given series?</p>\n<p>13, 14, 32, 123, ?</p>",
                    question_hi: "<p>22. दी गई श्रृंखला में \'?\' के स्थान पर क्या आना चाहिए?</p>\n<p>13, 14, 32, 123, ?</p>",
                    options_en: ["<p>748</p>", "<p>682</p>", 
                                "<p>635</p>", "<p>712</p>"],
                    options_hi: ["<p>748</p>", "<p>682</p>",
                                "<p>635</p>", "<p>712</p>"],
                    solution_en: "<p>22.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1738229058122.png\" alt=\"rId5\" width=\"282\" height=\"61\"></p>",
                    solution_hi: "<p>22.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1738229058122.png\" alt=\"rId5\" width=\"282\" height=\"61\"></p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "23",
                    section: "33",
                    question_en: "<p>23. The Jewish festival of lights is called:</p>",
                    question_hi: "<p>23. यहूदीओं के कौन से त्योहार को रोशनी का त्योहार कहा जाता है?</p>",
                    options_en: ["<p>Anthurium</p>", "<p>Hanukkah</p>", 
                                "<p>Hornbill</p>", "<p>Nauroj</p>"],
                    options_hi: ["<p>एंथुरियम (Anthurium)</p>", "<p>हनुक्का (Hanukkah)</p>",
                                "<p>हॉर्नबिल (Hornbill)</p>", "<p>नौरोज (Nauroj)</p>"],
                    solution_en: "<p>23.(b) <strong>Hanukkah</strong> (Chanukah) (Festival of Lights) is the Jewish eight-day, wintertime festival of lights, celebrated with a nightly menorah lighting, special prayers and fried foods. Some of the major festivals and celebrations of Jews are Days of Awe, Jewish New Year (Rosh Hashanah), Day of Atonement (Yom Kippur), Festival of Thanksgiving (Sukkot), Merry Making Festival (Purim), Festival of Freedom (Pessah or Passover) and Festival of Weeks (Shavuot).</p>",
                    solution_hi: "<p>23.(b) <strong>हनुक्का </strong>(चनुकाह) (प्रकाश का त्योहार) यहूदी आठ दिवसीय, सर्दियों में प्रकाश का त्योहार है, रात में मेनोराह प्रकाश, विशेष प्रार्थना और तले हुए खाद्य पदार्थों के साथ मनाया जाता है। यहूदियों के कुछ प्रमुख त्योहार और उत्सव हैं विस्मय का दिन, यहूदी नव वर्ष (रोश हसनाह), प्रायश्चित का दिन (योम किप्पुर), धन्यवाद का त्योहार (सुकोट), मेरी मेकिंग फेस्टिवल (पुरिम), स्वतंत्रता का त्योहार (पेसा या फसह) और सप्ताहों का त्योहार (शावोत)।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "24",
                    section: "33",
                    question_en: "<p>24. A constant force acts on an object of mass 10 kg for a duration of 2 seconds. It increases the object&rsquo;s velocity from 5 m/s to 10 m/s. Find the magnitude of the applied force. Now, if the force is applied for a duration of 5 seconds what would be the final velocity of the object?</p>",
                    question_hi: "<p>24. एक स्थिर बल 10 किग्रा द्रव्यमान की वस्तु पर 2 सेकंड की अवधि के लिए कार्य करता है। यह वस्तु के वेग को 5 मीटर/सेकंड से बढ़ाकर 10 मीटर/सेकंड कर देता है। लागू बल का परिमाण ज्ञात कीजिए। अब, यदि बल 5 सेकंड की अवधि के लिए लगाया जाता है, तो वस्तु का अंतिम वेग क्या होगा?</p>",
                    options_en: ["<p>Applied force = 20 N, Final Velocity = 7.5 m/s</p>", "<p>Applied Force = 25 N, Final Velocity = 7.5 m/s</p>", 
                                "<p>Applied Force = 20 N, Final Velocity = 17.5 m/s</p>", "<p>Applied Force = 25 N, Final Velocity = 17.5 m/s</p>"],
                    options_hi: ["<p>लगाया गया बल = 20 N, अंतिम वेग = 7.5 मीटर/सेकंड</p>", "<p>लगाया गया बल = 25 N, अंतिम वेग = 7.5 मीटर/सेकंड</p>",
                                "<p>लगाया गया बल = 20 N, अंतिम वेग = 17.5 मीटर/सेकंड</p>", "<p>लगाया गया बल = 25 N, अंतिम वेग = 17.5 मीटर/सेकंड</p>"],
                    solution_en: "<p>24.(d) <strong>Applied Force = 25 N, Final Velocity = 17.5 m/s.</strong> <br>Given, Mass = 10 kg, t<sub>1</sub>= 2s, Initial velocity u = 5 m/s, Final Velocity v = 10 m/s, t<sub>2</sub> = 5s.<br>So, Let the force and acceleration be &lsquo;F&rsquo; and &lsquo;a&rsquo; respectively.<br>So, a = <math display=\"inline\"><mfrac><mrow><mo>(</mo><mi>v</mi><mo>-</mo><mi>u</mi><mo>)</mo></mrow><mrow><mi>t</mi></mrow></mfrac><mi>&#160;</mi></math>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>10</mn><mo>-</mo><mn>5</mn><mo>)</mo></mrow><mn>2</mn></mfrac></math> = 2.5 m/s<sup>2</sup>.<br>So, the magnitude of applied force is Mass &times; Acceleration = 10 &times; 2.5 = 25 N<br>And now final velocity after 5s be v,<br>v = u + at = 5 + 2.5 &times; 5 = 17.5 m/s<br>The final velocity after 5s is 17.5 m/s.</p>",
                    solution_hi: "<p>24.(d) <strong>लगाया गया बल = 25 N, अंतिम वेग = 17.5 मीटर/सेकंड।</strong><br>दिया गया है, द्रव्यमान = 10 किग्रा, t<sub>1</sub>= 2s, प्रारंभिक वेग u = 5 m/s, अंतिम वेग v = 10 m/s, t<sub>2</sub> = 5s।<br>तो, मान लीजिए बल और त्वरण क्रमशः \'F\' और \'a\' है।<br>तो, a = <math display=\"inline\"><mfrac><mrow><mo>(</mo><mi>v</mi><mo>-</mo><mi>u</mi><mo>)</mo></mrow><mrow><mi>t</mi></mrow></mfrac><mi>&#160;</mi></math>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>10</mn><mo>-</mo><mn>5</mn><mo>)</mo></mrow><mn>2</mn></mfrac></math> = 2.5 m/s<sup>2</sup>.।&nbsp;<br>तो, लगाए गए बल का परिमाण द्रव्यमान &times; त्वरण = 10 &times; 2.5 = 25 N है<br>और अब 5s के बाद अंतिम वेग v होगा,<br>v = u + at = 5 + 2.5 &times; 5 = 17.5 मी/से<br>5s के बाद अंतिम वेग 17.5 m/s है।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "25",
                    section: "33",
                    question_en: "<p>25. Find the value of (1 + tan 20&deg; + sec 20&deg;) (1+ cot 20&deg; - cosec 20&deg;).</p>",
                    question_hi: "<p>25. (1 + tan 20&deg; + sec 20&deg;) (1 + cot 20&deg; - cosec 20&deg;) का मन ज्ञात कीजिये।</p>",
                    options_en: ["<p>1</p>", "<p>-1</p>", 
                                "<p>0</p>", "<p>2</p>"],
                    options_hi: ["<p>1</p>", "<p>-1</p>",
                                "<p>0</p>", "<p>2</p>"],
                    solution_en: "<p>25.(d) (1 + tan 20&deg; + sec 20&deg;)(1 + cot 20&deg; - cosec 20&deg;)<br>&rArr; (1 + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>sin</mi><mn>20</mn><mo>&#176;</mo></mrow><mrow><mi>cos</mi><mn>20</mn><mo>&#176;</mo></mrow></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><mi>cos</mi><mn>20</mn><mo>&#176;</mo></mrow></mfrac></math>)(1 + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>cos</mi><mn>20</mn><mo>&#176;</mo></mrow><mrow><mi>sin</mi><mn>20</mn><mo>&#176;</mo></mrow></mfrac></math> - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><mi>sin</mi><mn>20</mn><mo>&#176;</mo></mrow></mfrac></math>)<br>&rArr; (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>cos</mi><mn>20</mn><mo>&#176;</mo><mo>+</mo><mi>sin</mi><mn>20</mn><mo>&#176;</mo><mo>+</mo><mn>1</mn></mrow><mrow><mi>cos</mi><mn>20</mn><mo>&#176;</mo></mrow></mfrac></math>)&nbsp;(<math display=\"inline\"><mfrac><mrow><mi>s</mi><mi>i</mi><mi>n</mi><mn>20</mn><mo>&#176;</mo><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>c</mi><mi>o</mi><mi>s</mi><mn>20</mn><mo>&#176;</mo><mo>-</mo><mi>&#160;</mi><mn>1</mn></mrow><mrow><mi>s</mi><mi>i</mi><mi>n</mi><mn>20</mn><mo>&#176;</mo></mrow></mfrac></math>)<br>&rArr; (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mrow><mo>(</mo><mi>cos</mi><mn>20</mn><mo>&#176;</mo><mo>+</mo><mi>sin</mi><mn>20</mn><mo>&#176;</mo><mo>)</mo></mrow><mn>2</mn></msup><mo>-</mo><mn>1</mn></mrow><mrow><mi>cos</mi><mn>20</mn><mo>&#176;</mo><mo>&#215;</mo><mi>sin</mi><mn>20</mn><mo>&#176;</mo></mrow></mfrac></math> ) <br>&rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>cos</mi><mo>&#178;</mo><mn>20</mn><mo>&#176;</mo><mo>+</mo><mi>sin</mi><mo>&#178;</mo><mn>20</mn><mo>&#176;</mo><mo>+</mo><mn>2</mn><mo>&#215;</mo><mi>cos</mi><mo>&#160;</mo><mn>20</mn><mo>&#176;</mo><mo>&#215;</mo><mi>sin</mi><mo>&#160;</mo><mn>20</mn><mo>&#176;</mo><mo>-</mo><mn>1</mn></mrow><mrow><mi>cos</mi><mn>20</mn><mo>&#176;</mo><mo>&#215;</mo><mi>sin</mi><mn>20</mn><mo>&#176;</mo></mrow></mfrac></math> <br>&rArr; (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mo>+</mo><mn>2</mn><mo>&#215;</mo><mi>cos</mi><mn>20</mn><mo>&#176;</mo><mo>&#215;</mo><mi>sin</mi><mn>20</mn><mo>&#176;</mo><mo>-</mo><mn>1</mn></mrow><mrow><mi>cos</mi><mn>20</mn><mo>&#176;</mo><mo>&#215;</mo><mi>sin</mi><mn>20</mn><mo>&#176;</mo></mrow></mfrac></math>)<br>&rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mo>&#215;</mo><mi>cos</mi><mo>&#160;</mo><mn>20</mn><mo>&#176;</mo><mo>&#215;</mo><mi>sin</mi><mo>&#160;</mo><mn>20</mn><mo>&#176;</mo></mrow><mrow><mi>cos</mi><mn>20</mn><mo>&#176;</mo><mo>&#215;</mo><mi>sin</mi><mn>20</mn><mo>&#176;</mo></mrow></mfrac></math> = 2</p>",
                    solution_hi: "<p>25.(d) (1 + tan 20&deg; + sec 20&deg;)(1 + cot 20&deg; - cosec 20&deg;)<br>&rArr; (1 + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>sin</mi><mn>20</mn><mo>&#176;</mo></mrow><mrow><mi>cos</mi><mn>20</mn><mo>&#176;</mo></mrow></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><mi>cos</mi><mn>20</mn><mo>&#176;</mo></mrow></mfrac></math>)(1 + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>cos</mi><mn>20</mn><mo>&#176;</mo></mrow><mrow><mi>sin</mi><mn>20</mn><mo>&#176;</mo></mrow></mfrac></math> - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><mi>sin</mi><mn>20</mn><mo>&#176;</mo></mrow></mfrac></math>)<br>&rArr; (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>cos</mi><mn>20</mn><mo>&#176;</mo><mo>+</mo><mi>sin</mi><mn>20</mn><mo>&#176;</mo><mo>+</mo><mn>1</mn></mrow><mrow><mi>cos</mi><mn>20</mn><mo>&#176;</mo></mrow></mfrac></math>)&nbsp;(<math display=\"inline\"><mfrac><mrow><mi>s</mi><mi>i</mi><mi>n</mi><mn>20</mn><mo>&#176;</mo><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>c</mi><mi>o</mi><mi>s</mi><mn>20</mn><mo>&#176;</mo><mo>-</mo><mi>&#160;</mi><mn>1</mn></mrow><mrow><mi>s</mi><mi>i</mi><mi>n</mi><mn>20</mn><mo>&#176;</mo></mrow></mfrac></math>)<br>&rArr; (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mrow><mo>(</mo><mi>cos</mi><mn>20</mn><mo>&#176;</mo><mo>+</mo><mi>sin</mi><mn>20</mn><mo>&#176;</mo><mo>)</mo></mrow><mn>2</mn></msup><mo>-</mo><mn>1</mn></mrow><mrow><mi>cos</mi><mn>20</mn><mo>&#176;</mo><mo>&#215;</mo><mi>sin</mi><mn>20</mn><mo>&#176;</mo></mrow></mfrac></math> ) <br>&rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>cos</mi><mo>&#178;</mo><mn>20</mn><mo>&#176;</mo><mo>+</mo><mi>sin</mi><mo>&#178;</mo><mn>20</mn><mo>&#176;</mo><mo>+</mo><mn>2</mn><mo>&#215;</mo><mi>cos</mi><mo>&#160;</mo><mn>20</mn><mo>&#176;</mo><mo>&#215;</mo><mi>sin</mi><mo>&#160;</mo><mn>20</mn><mo>&#176;</mo><mo>-</mo><mn>1</mn></mrow><mrow><mi>cos</mi><mn>20</mn><mo>&#176;</mo><mo>&#215;</mo><mi>sin</mi><mn>20</mn><mo>&#176;</mo></mrow></mfrac></math> <br>&rArr; (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mo>+</mo><mn>2</mn><mo>&#215;</mo><mi>cos</mi><mn>20</mn><mo>&#176;</mo><mo>&#215;</mo><mi>sin</mi><mn>20</mn><mo>&#176;</mo><mo>-</mo><mn>1</mn></mrow><mrow><mi>cos</mi><mn>20</mn><mo>&#176;</mo><mo>&#215;</mo><mi>sin</mi><mn>20</mn><mo>&#176;</mo></mrow></mfrac></math>)<br>&rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mo>&#215;</mo><mi>cos</mi><mo>&#160;</mo><mn>20</mn><mo>&#176;</mo><mo>&#215;</mo><mi>sin</mi><mo>&#160;</mo><mn>20</mn><mo>&#176;</mo></mrow><mrow><mi>cos</mi><mn>20</mn><mo>&#176;</mo><mo>&#215;</mo><mi>sin</mi><mn>20</mn><mo>&#176;</mo></mrow></mfrac></math> = 2</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "26",
                    section: "33",
                    question_en: "<p>26. What should come in place of \'?\' in the given series?</p>\n<p>- 3.4, - 2.7, - 2, - 1.3, - 0.6, ?</p>",
                    question_hi: "<p>26. दी गई श्रृंखला में \'?\' के स्थान पर क्या आना चाहिए?</p>\n<p>- 3.4, - 2.7, - 2, - 1.3, - 0.6, ?</p>",
                    options_en: ["<p>0.2</p>", "<p>0.1</p>", 
                                "<p>0.3</p>", "<p>0</p>"],
                    options_hi: ["<p>0.2</p>", "<p>0.1</p>",
                                "<p>0.3</p>", "<p>0</p>"],
                    solution_en: "<p>26.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1738229058327.png\" alt=\"rId6\" width=\"340\" height=\"70\"></p>",
                    solution_hi: "<p>26.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1738229058327.png\" alt=\"rId6\" width=\"340\" height=\"70\"></p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "27",
                    section: "33",
                    question_en: "<p>27. Maulana Abul Kalam Azad Trophy (MAKA) was instituted in the year _______ and awarded to the overall top performing Indian University in the Sports.</p>",
                    question_hi: "<p>27. मौलाना अबुल कलाम आज़ाद ट्रॉफी (MAKA) वर्ष _______ में स्थापित की गई थी और खेल में समग्र रूप से शीर्ष प्रदर्शन करने वाले भारतीय विश्वविद्यालय को प्रदान की गई थी।</p>",
                    options_en: ["<p>1956 - 57</p>", "<p>1958 - 59</p>", 
                                "<p>1954 - 55</p>", "<p>1952- 53</p>"],
                    options_hi: ["<p>1956 - 57</p>", "<p>1958 - 59</p>",
                                "<p>1954 - 55</p>", "<p>1952- 53</p>"],
                    solution_en: "<p>27.(a) <strong>1956 - 57. </strong>Maulana Abul Kalam Azad Trophy: It is awarded annually by the Ministry of Youth Affairs and Sports. Sponsored by - Government of India. First winner - Bombay University. Maulana Abul Kalam Azad - He was the first Minister of Education of India. Trophies and their related major sports - Deodhar Trophy (Cricket), Durand Cup (Football), Santosh Trophy (Football), Ranji Trophy (Cricket), Subroto Cup (Football).</p>",
                    solution_hi: "<p>27.(a) <strong>1956 - 57</strong> । मौलाना अबुल कलाम आज़ाद ट्रॉफी: यह युवा मामले और खेल मंत्रालय द्वारा प्रतिवर्ष प्रदान की जाती है। प्रायोजित - भारत सरकार। प्रथम विजेता - बॉम्बे यूनिवर्सिटी। मौलाना अबुल कलाम आज़ाद - वे भारत के प्रथम शिक्षा मंत्री थे। ट्रॉफियां और उनसे संबंधित प्रमुख खेल - देवधर ट्रॉफी (क्रिकेट), डूरंड कप (फुटबॉल), संतोष ट्रॉफी (फुटबॉल), रणजी ट्रॉफी (क्रिकेट), सुब्रतो कप (फुटबॉल)।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "28",
                    section: "33",
                    question_en: "<p>28. <strong id=\"docs-internal-guid-5cebe77c-7fff-a890-1e60-ef971bafc7a1\"></strong>In this question, a statement is given followed by three conclusions. Choose the conclusion(s) which best fit(s) logically.</p>\n<p><strong>Statement:</strong><br>E <math display=\"inline\"><mo>&#8805;</mo></math> R = S &le; T &lt; N &gt; O = Z<br><strong>Conclusions:</strong><br>1. E&nbsp;<math display=\"inline\"><mo>&#8805;</mo></math> S <br>2. &Tau; <math display=\"inline\"><mo>&#8805;</mo></math> &Epsilon;<br>3. R &gt; N <br>4. T &lt; Z</p>",
                    question_hi: "<p>28. इस प्रश्न में, एक कथन के बाद तीन निष्कर्ष दिए गए हैं।&nbsp; तार्किक रूप से सबसे योग्य निष्कर्ष को चुनें।</p>\n<p><strong>कथन:</strong> <br>E <math display=\"inline\"><mo>&#8805;</mo></math> R = S &le; T &lt; N &gt; O = Z<br><strong>निष्कर्ष:</strong> <br>1. E&nbsp;<math display=\"inline\"><mo>&#8805;</mo></math> S <br>2. &Tau; &ge; &Epsilon;<br>3. R &gt; N <br>4. T &lt; Z</p>",
                    options_en: ["<p>2</p>", "<p>3</p>", 
                                "<p>4</p>", "<p>1</p>"],
                    options_hi: ["<p>2</p>", "<p>3</p>",
                                "<p>4</p>", "<p>1</p>"],
                    solution_en: "<p>28.(d) <strong>Given statement:</strong><br>E <math display=\"inline\"><mo>&#8805;</mo></math> R = S &le; T &lt; N &gt; O = Z<br>From conclusion,<br>(i) E &ge; S = true ( as per E &ge; R = S) <br>(ii) &Tau; &ge; &Epsilon; = false (as per E &ge; R = S &le; T ) because there is no relation between T and E.<br>(iii) R &gt; N = false ( as per R = S &le; T &lt; N ) because N is greater than R.<br>(iv) T &lt; Z = false (as per T &lt; N &gt; O = Z) because there is no relation between Z and T. <br>So we can clearly see that Only conclusion I follows.</p>",
                    solution_hi: "<p>28.(d) <strong>दिया गया कथन</strong> <br>E &ge;&nbsp;R = S &le; T &lt; N &gt; O = Z<br>निष्कर्ष से,<br>(i) E &ge; S = सत्य (E &ge; R = S के अनुसार)<br>(ii) &Tau; &ge; &Epsilon; = असत्य (E &ge; R = S &le; T के अनुसार) क्योंकि T और E के बीच कोई संबंध नहीं है।<br>(iii) R &gt; N = असत्य (R = S &le; T &lt; N के अनुसार) क्योंकि N, R से बड़ा है।<br>(iv) T &lt; Z = असत्य ( T &lt; N &gt; O = Z के अनुसार) क्योंकि Z और T के बीच कोई संबंध नहीं है।<br>अतः हम स्पष्ट रूप से देख सकते हैं कि केवल&nbsp;निष्कर्ष I अनुसरण करता है।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "29",
                    section: "33",
                    question_en: "<p>29. Fill in the blank with the most appropriate option.&nbsp;The Universal Constant of Gravitation is ______.</p>",
                    question_hi: "<p>29. रिक्त स्थान में सबसे उपयुक्त विकल्प भरें। गुरुत्वाकर्षण का सार्वभौमिक नियतांक है _______</p>",
                    options_en: ["<p>9.8 Nm<sup>2</sup>/kg<sup>2</sup></p>", "<p>6.76 &times; 10<sup>-10&nbsp;</sup>Nm<sup>2</sup>/kg<sup>2</sup></p>", 
                                "<p>6.67 &times; 10<sup>-11</sup> Nm<sup>2</sup>/kg<sup>2</sup></p>", "<p>6.67 &times; 10<sup>10 </sup>Nm<sup>2</sup>/kg<sup>2</sup></p>"],
                    options_hi: ["<p>9.8 Nm<sup>2</sup>/kg<sup>2</sup></p>", "<p>6.76 &times; 10<sup>-10</sup> Nm<sup>2</sup>/kg<sup>2</sup></p>",
                                "<p>6.67 &times; 10<sup>-11 </sup>Nm<sup>2</sup>/kg<sup>2</sup></p>", "<p>6.67 &times; 10<sup>10</sup>Nm<sup>2</sup>/kg<sup>2</sup></p>"],
                    solution_en: "<p>29.(c) <strong>6.67 &times; 10<sup>-11</sup> Nm<sup>2</sup>/kg<sup>2</sup>.</strong> Accordingly to Newton\'s Law of Universal Gravitation, The gravitational constant is the proportionality constant which is denoted by G. Newton\'s law of universal gravitation: The attractive force between two objects (F) is equal to G times the product of their masses (M<sub>1</sub>, M<sub>2</sub>) divided by the square of the distance between them (r<sup>2</sup>). F = G&nbsp;<math display=\"inline\"><mfrac><mrow><msub><mrow><mi>M</mi></mrow><mrow><mn>1</mn></mrow></msub><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><msub><mrow><mi>M</mi></mrow><mrow><mn>2</mn></mrow></msub></mrow><mrow><msup><mrow><mi>r</mi></mrow><mrow><mn>2</mn></mrow></msup></mrow></mfrac></math> .</p>",
                    solution_hi: "<p>29.(c) <strong>6.67 &times; 10<sup>-11</sup> Nm<sup>2</sup>/kg<sup>2</sup>.</strong>। न्यूटन के सार्वभौमिक गुरुत्वाकर्षण के नियम के अनुसार, गुरुत्वाकर्षण नियतांक, समानुपातता नियतांक है जिसे G द्वारा दर्शाया जाता है। न्यूटन के सार्वत्रिक गुरुत्वाकर्षण के नियम में, दो वस्तुओं (F) के बीच आकर्षण बल उनके द्रव्यमान (M<sub>1</sub>, M<sub>2</sub>) के गुणनफल के G गुणा के बराबर होता है, जो उनके बीच की दूरी (r<sup>2</sup>) के वर्ग से विभाजित होता है। F = G&nbsp;<math display=\"inline\"><mfrac><mrow><msub><mrow><mi>M</mi></mrow><mrow><mn>1</mn></mrow></msub><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><msub><mrow><mi>M</mi></mrow><mrow><mn>2</mn></mrow></msub></mrow><mrow><msup><mrow><mi>r</mi></mrow><mrow><mn>2</mn></mrow></msup></mrow></mfrac></math> ।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "30",
                    section: "33",
                    question_en: "<p>30. The volumes of two spheres are in the ratio of 27 : 8. The ratio of the surface areas of these spheres, in the order in which they are mentioned here, is ______.</p>",
                    question_hi: "<p>30. दो गोलों के आयतनों का अनुपात 27 : 8 है। जिस क्रम में उनका यहां उल्लेख हुआ है, उसी क्रम में इन गोलों के पृष्ठीय क्षेत्रफलों का अनुपात ज्ञात कीजिए।</p>",
                    options_en: ["<p>4 : 9</p>", "<p>9 : 4</p>", 
                                "<p>3 : 2</p>", "<p>2 : 3</p>"],
                    options_hi: ["<p>4 : 9</p>", "<p>9 : 4</p>",
                                "<p>3 : 2</p>", "<p>2 : 3</p>"],
                    solution_en: "<p>30.(b) Ratio of volume, <br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mfrac><mn>4</mn><mn>3</mn></mfrac><mi>&#960;</mi><msup><mi>R</mi><mn>3</mn></msup></mrow><mrow><mfrac><mn>4</mn><mn>3</mn></mfrac><mi>&#960;</mi><msup><mi>r</mi><mn>3</mn></msup></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>27</mn><mn>8</mn></mfrac></math> <br><img src=\"data:image/png;base64,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\" width=\"110\" height=\"31\"><br>Now, ratio of surface area <br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>4</mn><mi>&#960;</mi><msup><mi>R</mi><mn>2</mn></msup></mrow><mrow><mn>4</mn><mi>&#960;</mi><msup><mi>r</mi><mn>2</mn></msup></mrow></mfrac></math> = (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>2</mn></mfrac></math>)<sup>2</sup> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>9</mn><mn>4</mn></mfrac></math> or 9 : 4</p>",
                    solution_hi: "<p>30.(b) आयतन का अनुपात <br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mfrac><mn>4</mn><mn>3</mn></mfrac><mi>&#960;</mi><msup><mi>R</mi><mn>3</mn></msup></mrow><mrow><mfrac><mn>4</mn><mn>3</mn></mfrac><mi>&#960;</mi><msup><mi>r</mi><mn>3</mn></msup></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>27</mn><mn>8</mn></mfrac></math> <br><img src=\"data:image/png;base64,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\" width=\"110\" height=\"31\"><br>अब, सतह क्षेत्रफल का अनुपात <br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>4</mn><mi>&#960;</mi><msup><mi>R</mi><mn>2</mn></msup></mrow><mrow><mn>4</mn><mi>&#960;</mi><msup><mi>r</mi><mn>2</mn></msup></mrow></mfrac></math> = (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>2</mn></mfrac></math>)<sup>2</sup> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>9</mn><mn>4</mn></mfrac></math> or 9 : 4</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "31",
                    section: "33",
                    question_en: "<p>31. Four of the following five are somehow similar and therefore create a group. What is the one who does not come in this group?<br>B, N, R, Z, E</p>",
                    question_hi: "<p>31. निम्नलिखित पाँच में से चार किसी न किसी तरह समान हैं और इसलिए एक समूह बनाते हैं। उसे चुने जो इस समूह में नहीं आता है ?<br>B, N, R, Z, E</p>",
                    options_en: ["<p>Z</p>", "<p>E</p>", 
                                "<p>R</p>", "<p>N</p>"],
                    options_hi: ["<p>Z</p>", "<p>E</p>",
                                "<p>R</p>", "<p>N</p>"],
                    solution_en: "<p>31.(b) Except E all are consonants. So the odd one is E.</p>",
                    solution_hi: "<p>31.(b) E को छोड़कर सभी व्यंजन है। तो विषम E है।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "32",
                    section: "33",
                    question_en: "<p>32. Which of the following sports types is associated with the term \"LIBERO\"?</p>",
                    question_hi: "<p>32. निम्नलिखित में से कौन सा खेल प्रकार \"LIBERO\" शब्द से संबंधित है?</p>",
                    options_en: ["<p>Tennis</p>", "<p>Volleyball</p>", 
                                "<p>Archery</p>", "<p>Badminton</p>"],
                    options_hi: ["<p>टेनिस</p>", "<p>वॉलीबॉल</p>",
                                "<p>तीरंदाजी</p>", "<p>बैडमिंटन</p>"],
                    solution_en: "<p>32.(b) <strong>Volleyball</strong>. Libero is a defensive specialist position in indoor volleyball. Sports and their terminologies:- Volleyball - Beach dig, Bump, Block, Bump pass, Decoy, Flare. Tennis - Ace, Crosscourt, Backhand, Volley. Archery - Recurve bow, Limb, Fletching, Quiver, Nock, Riser. Badminton - Birdie, Fault, Lob, Short serve, Wood shot.</p>",
                    solution_hi: "<p>32.(b) <strong>वॉलीबॉल |</strong> लिबरो (Libero) इनडोर वॉलीबॉल में एक रक्षात्मक विशेषज्ञ स्थिति है। खेल और उनकी शब्दावली:- वॉलीबॉल - बीच डिग, बम्प, ब्लॉक, बम्प पास, डिकॉय, फ्लेयर। टेनिस - ऐस, क्रॉसकोर्ट, बैकहैंड, वॉली। तीरंदाजी - रिकर्व बो, लिम्ब , फ्लेचिंग, क्विवर, नॉक, राइसर। बैडमिंटन - बर्डी, फॉल्ट, लोब, शॉर्ट सर्व, वुड शॉट।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "33",
                    section: "33",
                    question_en: "<p>33. What happens when sodium metal reacts with water?</p>",
                    question_hi: "<p>33. सोडियम धातु के जल के साथ अभिक्रिया करने पर क्या होता है?</p>",
                    options_en: ["<p>Hydrogen gas and sodium hydroxide solution are produced.</p>", "<p>Chlorine gas and sodium hydroxide solution are produced.</p>", 
                                "<p>No reaction takes place.</p>", "<p>Only sodium hydroxide solution is produced.</p>"],
                    options_hi: ["<p>हाइड्रोजन गैस और सोडियम हाइड्रोक्साइड विलयन बनते है।</p>", "<p>क्लोरीन गैस तथा सोडियम हाइड्रॉक्साइड विलयन बनता हैं।</p>",
                                "<p>कोई अभिक्रिया नहीं होती है।</p>", "<p>केवल सोडियम हाइड्रॉक्साइड विलयन बनते है।</p>"],
                    solution_en: "<p>33.(a) It is an exothermic reaction. 2Na (s) + 2H<sub>2</sub>O &rarr; 2NaOH (aq) + H<sub>2</sub>(g). The resultant is non-colored and basic in nature. Sodium hydroxide solution (pH-14): used to produce soaps, rayon, paper, products that explode, dyes, and petroleum products.</p>",
                    solution_hi: "<p>33.(a) यह एक उष्माक्षेपी अभिक्रिया है। 2Na (s) + 2H<sub>2</sub>O &rarr; 2NaOH (aq) + H<sub>2</sub>(g).। उत्पाद रंगहीन और क्षारीय प्रकृति का होता है। सोडियम हाइड्रॉक्साइड विलयन (pH-14): साबुन, रेयॉन, कागज़, विस्फोट करने वाले उत्पाद, रंजक और पेट्रोलियम उत्पाद बनाने के लिए प्रयोग किया जाता है।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "34",
                    section: "33",
                    question_en: "<p>34. The circumference of a circle is given as 308 m. What is the area of the circle? [Use &pi;&nbsp;= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mn>7</mn></mfrac></math>]</p>",
                    question_hi: "<p>34. एक वृत्त की परिधि 308 m दी गई है। वृत्त का क्षेत्रफल क्या है ? [ Use &pi; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mn>7</mn></mfrac></math>]</p>",
                    options_en: ["<p>7646 m<sup>2</sup></p>", "<p>7546 m<sup>2</sup></p>", 
                                "<p>7556 m<sup>2</sup></p>", "<p>7446 m<sup>2</sup></p>"],
                    options_hi: ["<p>7646 m<sup>2</sup></p>", "<p>7546 m<sup>2</sup></p>",
                                "<p>7556 m<sup>2</sup></p>", "<p>7446 m<sup>2</sup></p>"],
                    solution_en: "<p>34.(b) According to the question,<br>Circumference = 308<br>2&pi;r = 308<br>2 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mn>7</mn></mfrac></math> &times; r = 308 &rArr; r = 49 m<br>Area of circle = &pi;r<sup>2</sup> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mn>7</mn></mfrac></math> &times; (49)<sup>2</sup> = 7546 m<sup>2</sup>&nbsp;<br>Short Tricks :- As we know that ratio of side , circumference and area of circle.<br>Side : circumference :&nbsp; Area <br>&nbsp; 7k&nbsp; &nbsp;:&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;44 k&nbsp; &nbsp; &nbsp; &nbsp; :&nbsp; 154k<sup>2</sup><br>Here , 44k = 308m&rArr;&nbsp;k = 7<br>Area = 154k<sup>2</sup> = 154 &times; 7<sup>2</sup> <br>= 7546 m<sup>2</sup></p>",
                    solution_hi: "<p>34.(b) प्रश्न के अनुसार,<br>परिधि = 308<br>2&pi;r = 308<br>2 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mn>7</mn></mfrac></math> &times; r = 308 &rArr; r = 49 m<br>वृत्त का क्षेत्रफल = &pi;r<sup>2</sup> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mn>7</mn></mfrac></math> &times; (49)<sup>2</sup> = 7546 m<sup>2</sup>&nbsp;<br>शार्ट ट्रिक्स: जैसा कि हम जानते हैं कि वृत्त की भुजा, परिधि और क्षेत्रफल का अनुपात।<br>भुजा :&nbsp; परिधि : क्षेत्रफल<br>7k&nbsp; &nbsp; : 44 k&nbsp; &nbsp;:&nbsp; 154k<sup>2</sup><br>यहाँ, 44k = 308m &rArr; k = 7<br>क्षेत्रफल = 154k<sup>2</sup> = 154 &times; 7<sup>2</sup> <br>= 7546 m<sup>2</sup></p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "35",
                    section: "33",
                    question_en: "<p>35. Find out missing number in the following:<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1738229058623.png\" alt=\"rId7\" width=\"267\" height=\"99\"></p>",
                    question_hi: "<p>35. निम्नलिखित में लुप्त संख्या ज्ञात कीजिए?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1738229058623.png\" alt=\"rId7\" width=\"267\" height=\"99\"></p>",
                    options_en: ["<p>3</p>", "<p>2</p>", 
                                "<p>4</p>", "<p>1</p>"],
                    options_hi: ["<p>3</p>", "<p>2</p>",
                                "<p>4</p>", "<p>1</p>"],
                    solution_en: "<p>35.(c) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1738229058871.png\" alt=\"rId8\" width=\"416\" height=\"109\"></p>",
                    solution_hi: "<p>35.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1738229058871.png\" alt=\"rId8\" width=\"416\" height=\"109\"></p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "36",
                    section: "33",
                    question_en: "<p>36. Who wrote the book, &lsquo;The Little Balance (La Balancitta) in 1586?</p>",
                    question_hi: "<p>36. 1586 में \'द लिटिल बैलेंस (ला बालनसिट्टा)\' पुस्तक किसने लिखी थी?</p>",
                    options_en: ["<p>Galileo Galilei</p>", "<p>Carolus Linnaeus</p>", 
                                "<p>Archimedes</p>", "<p>James Prescott</p>"],
                    options_hi: ["<p>गैलीलियो गैलीली</p>", "<p>कैरोल लिनियस</p>",
                                "<p>आर्किमिडीज</p>", "<p>जेम्स प्रेस्कॉट</p>"],
                    solution_en: "<p>36.(a) <strong>Galileo Galilei.</strong> He was the first to report telescopic observations of the mountains on the moon, the moons of Jupiter, the phases of Venus, and the rings of Saturn. &lsquo;Species Plantarum&rsquo; and &lsquo;Systema Naturae&rsquo; were written by Carolus Linnaeus (father of modern Taxonomy). Archimedes (famous mathematician and inventor) wrote &lsquo;Archimedes Palimpsest&rsquo;. &lsquo;Das Mechanische Warmeaquivalent&rsquo; was written by James Prescott.</p>",
                    solution_hi: "<p>36.(a) <strong>गैलीलियो गैलीली।</strong> वह चंद्रमा पर पहाड़ों, बृहस्पति के उपग्रहों, शुक्र के चरण और शनि के वलयों का दूरबीन से अवलोकन की स्थिति पता करने वाले पहले व्यक्ति थे। \'स्पीशीज प्लान्टेरम\' और \'सिस्टेमा नेचुरे\' कैरोलस लिनियस (आधुनिक वर्गीकरण के जनक) द्वारा लिखे गए थे। आर्किमिडीज़ (प्रसिद्ध गणितज्ञ और आविष्कारक) ने \'आर्किमिडीज़ पालिम्प्सेस्ट\' लिखा था। \'दास मैकेनिश वार्मिएक्विवेलेंट\' जेम्स प्रेस्कॉट द्वारा लिखा गया था।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "37",
                    section: "33",
                    question_en: "<p>37. Carbonisation is a term used to describe the process of transforming dead plants into _____ over a long period of time.</p>",
                    question_hi: "<p>37. लंबी अवधि के दौरान मृत पौधों के _________ में परिवर्तित होने की प्रक्रिया कार्बनीकरण कहलाती है।</p>",
                    options_en: ["<p>petrol</p>", "<p>fossil</p>", 
                                "<p>coal</p>", "<p>diesel</p>"],
                    options_hi: ["<p>पेट्रोल</p>", "<p>जीवाश्म</p>",
                                "<p>कोयला</p>", "<p>डीजल</p>"],
                    solution_en: "<p>37.(c) <strong>Coal.</strong> Carbonisation: conversion of organic matters (plants and dead animal remains) into coal through destructive distillation under high pressure and temperature. Coal is a complex mixture of carbon, hydrogen and oxygen compounds, found in coal mines deep under the surface of earth. The classification of coal depends on the carbon content of coal. Higher the temperature and pressure of the Earth and the longer time the coal has been buried under the Earth, the more is the carbon content in it. Peat (30-40%) &lt; Lignite (50%) &lt; Bituminous (60%) &lt; Anthracite ( &gt; 80%).</p>",
                    solution_hi: "<p>37.(c) <strong>कोयला।</strong> कार्बनीकरण - उच्च दबाव और उच्च तापमान के तहत विनाशकारी आसवन के माध्यम से पौधों और मृत जानवरों के अवशेषों जैसे कार्बनिक पदार्थों का कोयले में रूपांतरण । कोयला कार्बन, हाइड्रोजन और ऑक्सीजन यौगिकों का एक जटिल मिश्रण है जो पृथ्वी की सतह के नीचे गहरे कोयले की खानों में पाया जाता है। कोयले का वर्गीकरण, कोयले में उपस्थित कार्बन पदार्थ पर निर्भर करता है। पृथ्वी का तापमान और दबाव जितना अधिक होगा और जितना अधिक समय तक कोयला पृथ्वी के नीचे दबा रहेगा, उसमें कार्बन की मात्रा उतनी ही अधिक होगी। पीट (30 - 40%) &lt; लिग्नाइट (50%) &lt; बिटुमिनस (60%) &lt; एन्थ्रेसाइट ( &gt; 80%)।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "38",
                    section: "33",
                    question_en: "<p>38. The lengths of two sides of a triangle are 7 cm and 8 cm respectively and the measure of the angles included between these two sided is 60&deg; .The length (in cm) of the third si de of the triangle is:</p>",
                    question_hi: "<p>38. एक त्रिभुज की दो भुजाओं की लंबाई क्रमशः 7cm और 8cm है और इन दोनों भुजाओं के बीच के कोणों की माप 60&deg; है। त्रिभुज की तीसरी भुजा की लंबाई (cm में) कितनी है?</p>",
                    options_en: ["<p><math display=\"inline\"><mn>2</mn><msqrt><mn>14</mn></msqrt></math></p>", "<p>7.5</p>", 
                                "<p>9</p>", "<p><math display=\"inline\"><msqrt><mn>57</mn></msqrt></math></p>"],
                    options_hi: ["<p><math display=\"inline\"><mn>2</mn><msqrt><mn>14</mn></msqrt></math></p>", "<p>7.5</p>",
                                "<p>9</p>", "<p><math display=\"inline\"><msqrt><mn>57</mn></msqrt></math></p>"],
                    solution_en: "<p>38.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1738229059157.png\" alt=\"rId9\" width=\"119\" height=\"123\"><br>Applying cosine rule in the given &Delta;<br>cos&theta; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mrow><mo>(</mo><mn>7</mn><mo>)</mo></mrow><mn>2</mn></msup><mo>+</mo><msup><mrow><mo>(</mo><mn>8</mn><mo>)</mo></mrow><mn>2</mn></msup><mo>-</mo><msup><mi>a</mi><mn>2</mn></msup></mrow><mrow><mn>2</mn><mo>&#215;</mo><mn>7</mn><mo>&#215;</mo><mn>8</mn></mrow></mfrac></math><br>Cos 60&deg; = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>49</mn><mo>+</mo><mn>64</mn><mo>-</mo><msup><mi>a</mi><mn>2</mn></msup></mrow><mn>112</mn></mfrac></math> <br>&rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>113</mn><mo>-</mo><msup><mi>a</mi><mn>2</mn></msup></mrow><mn>112</mn></mfrac></math><br>112 = 226 -&nbsp;2a<sup>2</sup> <br>&rArr; a = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>57</mn></msqrt></math></p>",
                    solution_hi: "<p>38.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1738229059157.png\" alt=\"rId9\" width=\"119\" height=\"123\"><br>दिए गए त्रिभुज में Cosine नियम लगाने पर, <br>cos&theta; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mrow><mo>(</mo><mn>7</mn><mo>)</mo></mrow><mn>2</mn></msup><mo>+</mo><msup><mrow><mo>(</mo><mn>8</mn><mo>)</mo></mrow><mn>2</mn></msup><mo>-</mo><msup><mi>a</mi><mn>2</mn></msup></mrow><mrow><mn>2</mn><mo>&#215;</mo><mn>7</mn><mo>&#215;</mo><mn>8</mn></mrow></mfrac></math><br>Cos 60&deg; = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>49</mn><mo>+</mo><mn>64</mn><mo>-</mo><msup><mi>a</mi><mn>2</mn></msup></mrow><mn>112</mn></mfrac></math> <br>&rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>113</mn><mo>-</mo><msup><mi>a</mi><mn>2</mn></msup></mrow><mn>112</mn></mfrac></math><br>112 = 226 -&nbsp;2a<sup>2</sup> <br>&rArr; a = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>57</mn></msqrt></math></p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "39",
                    section: "33",
                    question_en: "<p>39. A statement is given followed by two assumptions numbered I and II. You have to assume everything in the statement to be true and decide which of the assumptions is/are implicit in the statement<br><strong>Statement:</strong><br>Increase in the tax rates will hurt the pockets of the salaried middle class <br><strong>Assumptions:</strong><br>I. Increase in the prices of goods will hurt the poor too.<br>II. The middle class will reduce their extra expenditure to save money</p>",
                    question_hi: "<p>39. एक कथन और उसके बाद दो धारणाएं | और II दी गई हैं। आपको कथन में दी गई संपूर्ण जानकारी को सत्य मानते यह तय करना है दी गई धारणाओं में से कौन-सी कथन में निहित है/हैं<br><strong>कथन:</strong><br>कर की दरों में वृद्धि से वेतनभोगी मध्यम वर्ग की जेब पर असर पड़ेगा। <br><strong>धारणाएं:</strong><br>I. वस्तुओं की कीमतों में वृद्धि से गरीबों को भी नुकसान होगा।<br>II. मध्यम वर्ग पैसे बचाने के लिए अपने अतिरिक्त खर्च को कम करेगा।</p>",
                    options_en: ["<p>Only assumption I is implicit</p>", "<p>Neither assumption I nor II is implicit</p>", 
                                "<p>Both assumptions I and II are implicit</p>", "<p>Only assumption II is implicit</p>"],
                    options_hi: ["<p>केवल धारणा I निहित है</p>", "<p>न तो धारणा । और न ही ॥ निहित है</p>",
                                "<p>I और II दोनों धारणाएं निहित हैं</p>", "<p>केवल धारणा ॥ निहित है</p>"],
                    solution_en: "<p>39.(d) <strong>Statement :- </strong>Increase in the tax rates will hurt the pockets of the salaried middle class.<br>According to the statement, it can be concluded that an increase in tax rates will lead to reduction in the savings of salaried middle class. In order to keep the savings same, salaried middle class will try to reduce their extra expenditure.Only assumption (II) is implicit.</p>",
                    solution_hi: "<p>39.(d) <strong>कथन :-</strong> कर की दरों में वृद्धि से वेतनभोगी मध्यम वर्ग की जेब पर असर पड़ेगा।<br>कथन के अनुसार, यह निष्कर्ष निकाला जा सकता है कि कर दरों में वृद्धि से वेतनभोगी मध्यम वर्ग की बचत में कमी आएगी। बचत को समान रखने के लिए, वेतनभोगी मध्यम वर्ग अपने अतिरिक्त व्यय को कम करने का प्रयास करेगा। केवल धारणा (II) निहित है</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "40",
                    section: "33",
                    question_en: "<p>40. Goa became India\'s 25th state on:</p>",
                    question_hi: "<p>40. गोवा भारत का 25वां राज्य कब बना ?</p>",
                    options_en: ["<p>30 May 1990</p>", "<p>30 May 1986</p>", 
                                "<p>30 May 1987</p>", "<p>30 May 1988</p>"],
                    options_hi: ["<p>30 मई 1990</p>", "<p>30 मई 1986</p>",
                                "<p>30 मई 1987</p>", "<p>30 मई 1988</p>"],
                    solution_en: "<p>40.(c) <strong>30 May 1987. </strong>Goa was part of the Union Territory of Goa, Daman and Diu from 19 December 1961 to 30 May 1987. Under 12th Constitutional Amendment Act 1962, Goa, Daman and Diu were constituted as a union territory. Under the 56th Constitutional Amendment Act 1987, Goa was given the status of separate state. Telangana was officially formed on 2 June 2014.</p>",
                    solution_hi: "<p>40.(c) <strong>30 मई 1987</strong> । गोवा 19 दिसंबर 1961 से 30 मई 1987 तक केंद्र शासित प्रदेश गोवा, दमन और दीव का हिस्सा था। 12वें संवैधानिक संशोधन अधिनियम 1962 के तहत, गोवा, दमन और दीव को केंद्र शासित प्रदेश के रूप में गठित किया गया था। 56वें संवैधानिक संशोधन अधिनियम 1987 के तहत गोवा को अलग राज्य का दर्जा दिया गया। तेलंगाना का आधिकारिक तौर पर गठन 2 जून 2014 को हुआ था।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "41",
                    section: "33",
                    question_en: "<p>41. Which of the following is the correct structure of a benzene molecule?</p>",
                    question_hi: "<p>41. निम्नलिखित में से कौन सी बेंजीन अणु की सही संरचना है ?</p>",
                    options_en: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1738229059302.png\" alt=\"rId10\" width=\"90\" height=\"104\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1738229059420.png\" alt=\"rId11\" width=\"87\" height=\"101\"></p>", 
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1738229059536.png\" alt=\"rId12\" width=\"91\" height=\"105\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1738229059686.png\" alt=\"rId13\" width=\"89\" height=\"103\"></p>"],
                    options_hi: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1738229059302.png\" alt=\"rId10\" width=\"90\" height=\"104\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1738229059420.png\" alt=\"rId11\" width=\"87\" height=\"101\"></p>",
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1738229059536.png\" alt=\"rId12\" width=\"91\" height=\"105\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1738229059686.png\" alt=\"rId13\" width=\"89\" height=\"103\"></p>"],
                    solution_en: "<p>41.(c) Benzene (C<sub>6</sub>H<sub>6</sub>) - average mass 78.112 g/mol. The structure has a six-carbon ring which is represented by a hexagon and it includes 3-double bonds. The carbon atoms are represented by a corner that is bonded to other atoms. It was discovered by Michal Faraday in 1825. Uses - Making plastics, synthetic fibers, rubber lubricants, dyes, resins, detergents, drugs and more. Derivatives of Benzene :- Phenol (C<sub>6</sub>H<sub>6</sub>O), Toulene (C<sub>6</sub>H<sub>5</sub>CH<sub>3</sub>), Aniline (C<sub>6</sub>H<sub>5</sub>NH<sub>2</sub>), Anisole (C<sub>7</sub>H<sub>8</sub>O), Benzaldehyde (C<sub>7</sub>H<sub>6</sub>O), Acetophenone (C<sub>8</sub>H<sub>8</sub>O), Benzoic Acid (C<sub>7</sub>H<sub>6</sub>O<sub>2</sub>), Benzenesulphonic Acid (C<sub>6</sub>H<sub>6</sub>O<sub>3</sub>S), Styrene (C<sub>8</sub>H<sub>8</sub>).</p>",
                    solution_hi: "<p>41.(c) बेंजीन (C<sub>6</sub>H<sub>6</sub>) - औसत द्रव्यमान 78.112 g/mol। संरचना में एक छह-कार्बन वलय होता है जिसे एक षट्भुज द्वारा दर्शाया जाता है और इसमें 3-डबल बॉन्ड शामिल होते हैं। कार्बन परमाणुओं को एक कोने द्वारा दर्शाया जाता है जो अन्य परमाणुओं से बंधा होता है। इसकी खोज 1825 में माइकल फैराडे ने की थी। उपयोग - प्लास्टिक, सिंथेटिक फाइबर, रबर स्नेहक, रंजक, रेजिन, डिटर्जेंट, ड्रग्स और बहुत कुछ बनाना। बेंजीन के डेरिवेटिव: {फिनोल (C<sub>6</sub>H<sub>6</sub>O), टॉलीन (C<sub>6</sub>H<sub>5</sub>CH<sub>3</sub>), अनिलिन (C<sub>6</sub>H<sub>5</sub>NH<sub>2</sub>), ऐनिसोल (C<sub>7</sub>H<sub>8</sub>O), बेंजाल्डिहाइड (C<sub>7</sub>H<sub>6</sub>O), एसिटोफेनोन (C<sub>8</sub>H<sub>8</sub>O), बेंजोइक एसिड (C<sub>7</sub>H<sub>6</sub>O<sub>2</sub>), बेंजीन सल्फोनिक एसिड (C<sub>6</sub>H<sub>6</sub>O<sub>3</sub>S), स्टाइरीन (C<sub>8</sub>H<sub>8</sub>)}|</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "42",
                    section: "33",
                    question_en: "<p>42. The sum of two angles is 155&deg; and their difference is <math display=\"inline\"><mfrac><mrow><mi>&#960;</mi></mrow><mrow><mn>2</mn></mrow></mfrac></math>, the value of the greater angle (in radians) is..</p>",
                    question_hi: "<p>42. दो कोणों का योग 155&deg; है और उनका अंतर <math display=\"inline\"><mfrac><mrow><mi>&#960;</mi></mrow><mrow><mn>2</mn></mrow></mfrac></math> है, बड़े कोण का मान (रेडियन में) कितना है ?</p>",
                    options_en: ["<p><math display=\"inline\"><mfrac><mrow><mn>53</mn><mi>&#960;</mi></mrow><mrow><mn>72</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>49</mn><mi>&#960;</mi></mrow><mrow><mn>72</mn></mrow></mfrac></math></p>", 
                                "<p><math display=\"inline\"><mfrac><mrow><mn>47</mn><mi>&#960;</mi></mrow><mrow><mn>72</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>51</mn><mi>&#960;</mi></mrow><mrow><mn>72</mn></mrow></mfrac></math></p>"],
                    options_hi: ["<p><math display=\"inline\"><mfrac><mrow><mn>53</mn><mi>&#960;</mi></mrow><mrow><mn>72</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>49</mn><mi>&#960;</mi></mrow><mrow><mn>72</mn></mrow></mfrac></math></p>",
                                "<p><math display=\"inline\"><mfrac><mrow><mn>47</mn><mi>&#960;</mi></mrow><mrow><mn>72</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>51</mn><mi>&#960;</mi></mrow><mrow><mn>72</mn></mrow></mfrac></math></p>"],
                    solution_en: "<p>42.(b) Let the angles are x and y<br>x + y = 155&deg;, <br>x - y = 90&deg;<br>On solving both equations we get, <br>x = 122.5&deg; = <math display=\"inline\"><mfrac><mrow><mn>122</mn><mo>.</mo><mn>5</mn><mi>&#960;</mi></mrow><mrow><mn>180</mn></mrow></mfrac></math> = <math display=\"inline\"><mfrac><mrow><mn>49</mn><mi>&#960;</mi></mrow><mrow><mn>72</mn></mrow></mfrac></math></p>",
                    solution_hi: "<p>42.(b) माना की कोण x और y हैं<br>x + y = 155&deg;<br>x - y = 90&deg;<br>दोनों समीकरणों को हल करने पर हम पाते हैं,<br>x = 122.5&deg; = <math display=\"inline\"><mfrac><mrow><mn>122</mn><mo>.</mo><mn>5</mn><mi>&#960;</mi></mrow><mrow><mn>180</mn></mrow></mfrac></math> = <math display=\"inline\"><mfrac><mrow><mn>49</mn><mi>&#960;</mi></mrow><mrow><mn>72</mn></mrow></mfrac></math></p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "43",
                    section: "33",
                    question_en: "<p>43. A statement followed by two assumptions I and II is given. You have to decide which of the given assumptions is/are implicit in the statement assuming all the information given in the statement to be true?<br><strong>Statement:</strong><br>Reading books will make you a morally good person.<br><strong>Assumptions:</strong><br>I. Reading books helps in acquiring knowledge.<br>II, All successful people read books.</p>",
                    question_hi: "<p>43. एक कथन और उसके बाद दो धारणाएं I और II दी गई हैं। आपको कथन में दी गई संपूर्ण जानकारी को सत्य मानते हुए, यह तय करना है दी गई धारणाओं में से कौन-सी कथन में निहित हैं?<br><strong>कथन:</strong><br>किताबें पढ़ना आपको नैतिक रूप से अच्छा इंसान बनाएगा।<br><strong>धारणाएं:</strong><br>I. किताबें पढ़ने से ज्ञान प्राप्त करने में मदद मिलती है।<br>॥. सभी सफल लोग किताबें पढ़ते हैं।</p>",
                    options_en: ["<p>Both assumptions I and II are implicit</p>", "<p>Only assumption II Is implicit.</p>", 
                                "<p>Only assumption I Is implicit.</p>", "<p>Neither assumption I nor II is implicit.</p>"],
                    options_hi: ["<p>I और II दोनों धारणाएं निहित हैं</p>", "<p>केवल धारणा II निहित है।</p>",
                                "<p>केवल धारणा । निहित है</p>", "<p>न तो धारणा । और न ही ॥ निहित है</p>"],
                    solution_en: "<p>43.(d) Since, Both the conclusions don\'t have any relation with Morality.<br>Hence, Neither assumption I nor II is implicit.</p>",
                    solution_hi: "<p>43.(d) चूंकि, दोनों निष्कर्ष का नैतिकता से कोई संबंध नहीं है।<br>इसलिए, न तो धारणाएं I और न ही II निहित है।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "44",
                    section: "33",
                    question_en: "<p>44. Vardhamana Mahavira was the __________Tirthankara of Jainism.</p>",
                    question_hi: "<p>44. वर्धमान महावीर जैन धर्म के _______ तीर्थंकर थे।</p>",
                    options_en: ["<p>20<sup>th</sup></p>", "<p>2<sup>nd</sup></p>", 
                                "<p>24<sup>th</sup></p>", "<p>5<sup>th</sup></p>"],
                    options_hi: ["<p>20 वें</p>", "<p>दूसरे</p>",
                                "<p>24 वें</p>", "<p>5वें</p>"],
                    solution_en: "<p>44.(c) <strong>24<sup>th</sup></strong>. Tirthankara of Jainism: 1<sup>st</sup> - Rishabhanatha, 2<sup>nd</sup> - Ajitanath, 5<sup>th</sup> - Sumatinatha, 23<sup>rd</sup> - Parshvanatha. Vardhamana Mahavira: Birth place - Kundagrama (Vaishali), Birth Name - Vardhamana Mother\'s name -Trishala, Father\'s name - Siddhartha, Clan - Jnatrika, Enlightenment - Beneath Sala tree, First sermon - Pava. The <strong>five principles</strong> of Jainism: Non-violence (Ahimsa), Do not speak a lie (Satya), Non-stealing (Asteya), Observe continence (Brahmacharya) and Do not acquire property (Aparigraha).</p>",
                    solution_hi: "<p>44.(c)<strong> 24वें।</strong> जैन धर्म के तीर्थंकर: 1<sup>st</sup>- ऋषभनाथ, 2<sup>nd</sup> - अजितनाथ, 5वें - सुमतिनाथ, 23वें - पार्श्वनाथ । वर्धमान महावीर: जन्म स्थान - कुंडग्राम (वैशाली), जन्म नाम - वर्धमान माता का नाम - त्रिशला, पिता का नाम - सिद्धार्थ, गोत्र - ज्ञात्रिक, ज्ञानोदय - शाल वृक्ष के नीचे, प्रथम उपदेश - पावा। जैन धर्म के पाँच सिद्धांत: अहिंसा (अहिंसा), झूठ मत बोलो (सत्य), चोरी न करना (अस्तेय), संयम का पालन करना (ब्रह्मचर्य) और संपत्ति अर्जित न करना (अपरिग्रह)।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "45",
                    section: "33",
                    question_en: "<p>45. There are four elements P, Q, R and S and their atomic numbers are 2, 6, 13 and 18 respectively. Which of the following pairs has elements having similar valency?</p>",
                    question_hi: "<p>45. चार तत्व P, Q, R और S हैं और उनकी परमाणु संख्याएँ क्रमशः 2, 6, 13 और 18 हैं। निम्न में से किस युग्म में समान संयोजकता वाले तत्व हैं?</p>",
                    options_en: ["<p>R and S</p>", "<p>P and Q</p>", 
                                "<p>P and S</p>", "<p>O and S</p>"],
                    options_hi: ["<p>R और S</p>", "<p>P और Q</p>",
                                "<p>P और S</p>", "<p>O और S</p>"],
                    solution_en: "<p>45.(c)<strong> P and S</strong>. P = 2 &ndash; It is a Noble gas. Hence, Valency = 0<br>Q = 6 &ndash; 2, 4 - It needs 4 electrons to complete the octet. Hence, valency = 4<br>R = 13 - 2, 8, 3 &ndash; It needs to lose the last 3 electrons to complete the octet.&nbsp;<br>Hence, valency = 3&nbsp;<br>S = 18 &ndash; It is a Noble gas. Hence, Valency = 0<br>So, P and S have the same valency.</p>",
                    solution_hi: "<p>45.(c) <strong>P और S.</strong> P = 2 &ndash; यह एक नोबल गैस है। इसलिए, संयोजकता = 0<br>Q = 6 &ndash; 2, 4 &ndash; इसे अष्टक पूरा करने के लिए 4 इलेक्ट्रॉनों की आवश्यकता होती है। अत: संयोजकता = 4<br>R = 13 &ndash; 2, 8, 3 &ndash; अष्टक पूरा करने के लिए इसे अंतिम 3 इलेक्ट्रॉनों को खोने की जरूरत है। <br>अत: संयोजकता = 3<br>S = 18 &ndash; यह एक नोबल गैस है। इसलिए, संयोजकता = 0<br>अतः P और S की संयोजकता समान है।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "46",
                    section: "33",
                    question_en: "<p>46. If x + y = 1 , then find the value of x<sup>3</sup> + y<sup>3</sup> + 3xy.</p>",
                    question_hi: "<p>46. यदि x + y = 1 , तो x<sup>3</sup> + y<sup>3</sup> + 3xy का मान ज्ञात कीजिए।</p>",
                    options_en: ["<p>6</p>", "<p>0</p>", 
                                "<p>1</p>", "<p>2</p>"],
                    options_hi: ["<p>6</p>", "<p>0</p>",
                                "<p>1</p>", "<p>2</p>"],
                    solution_en: "<p>46.(c) x + y = 1<br>(x + y)<sup>3</sup> = x<sup>3</sup> + y<sup>3</sup> + 3xy(x+y)<br>&rArr; 1<sup>3</sup> = x<sup>3</sup> + y<sup>3</sup> + 3xy &times; 1<br>x<sup>3</sup> + y<sup>3</sup> + 3xy = 1</p>",
                    solution_hi: "<p>46.(c) x + y = 1<br>(x + y)<sup>3</sup> = x<sup>3</sup> + y<sup>3</sup> + 3xy(x+y)<br>&rArr; 1<sup>3</sup> = x<sup>3</sup> + y<sup>3</sup> + 3xy &times; 1<br>x<sup>3</sup> + y<sup>3</sup> + 3xy = 1</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "47",
                    section: "33",
                    question_en: "<p>47. Given below is a statement followed by two possible conclusions I and II. Read the information carefully and select the correct option.<br><strong>Statement:</strong><br>Following the recession in country F, many smaller companies have decided to lay off all their employees who were on probation period of six months. As a result, a little over 48% of the employees of company M were laid off. Which of the following can be concluded from the given information?<br>I. Company M had almost doubled in employee strength recently.<br>II. The layoffs due to recession are not performance based.</p>",
                    question_hi: "<p>47. नीचे एक कथन और उसके दो संभावित निष्कर्ष । और ।I दिए गए हैं। जानकारी को ध्यानपूर्वक पढ़कर सही विकल्प चुनें।<br><strong>कथन:</strong><br>देश F में मंदी आने के बाद, कई छोटी कंपनियों ने उन सभी कर्मचारियों की छंटनी करने का फैसला किया है, जो छह महीने की परिवीक्षा अवधि पर थे। इसके फलस्वरूप, कंपनी M से 48% से कुछ अधिक कर्मचारी निकाल दिए गए।<br>दी गई जानकारी के आधार पर निम्नलिखित में से कौन सा निष्कर्ष निकाला जा सकता है?<br>I. कंपनी M में हाल ही में कर्मचारियों की संख्या लगभग दोगुनी हो गई। <br>II. मंदी के कारण की गई छंटनी ( कर्मचारियों के) कार्य प्रदर्शन पर आधारित नहीं है।</p>",
                    options_en: ["<p>Only I can be concluded from the given information.</p>", "<p>Only II can be concluded from the given information.</p>", 
                                "<p>Both I and II can be concluded from the given information.</p>", "<p>Neither I nor II can be concluded from the given information</p>"],
                    options_hi: ["<p>दी गई जानकारी से केवल । निष्कर्ष निकाला जा सकता है।</p>", "<p>दी गई जानकारी से केवल || निष्कर्ष निकाला जा सकता है।</p>",
                                "<p>दी गई जानकारी से | और || दोनों निष्कर्ष निकाले जा सकते हैं।</p>", "<p>दी गई जानकारी से न तो । और न ही ॥ निष्कर्ष निकाले जा सकते हैं।</p>"],
                    solution_en: "<p>47.(c) As per the statement, nearly 50 percent employees have been laid off by smaller companies owing to recession. Employee strength has been reduced to half, in other words employee strength in the recent past was twice the present. Therefore, conclusion (I) is true. Since the layoffs were due to recession, but not the poor performance of employees under probation period. Therefore, conclusion (ii) is also true.</p>",
                    solution_hi: "<p>47.(c) कथन के अनुसार, मंदी के कारण छोटी कंपनियों द्वारा लगभग 50 प्रतिशत कर्मचारियों की छंटनी कर दी गयी है। कर्मचारियों की संख्या घटकर आधी हो गई है, दूसरे शब्दों में हाल के दिनों में कर्मचारियों की संख्या वर्तमान से दोगुनी थी। इसलिए, निष्कर्ष (I) सत्य है।चूंकि छंटनी मंदी के कारण थी, लेकिन परिवीक्षा अवधि पर कर्मचारियों के खराब प्रदर्शन के कारण नहीं। इसलिए, निष्कर्ष (ii) भी सत्य है।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "48",
                    section: "33",
                    question_en: "<p>48. Where was Gautama Buddha born?</p>",
                    question_hi: "<p>48. गौतम बुद्ध का जन्म कहाँ हुआ था?</p>",
                    options_en: ["<p>Vaishali</p>", "<p>Magadha</p>", 
                                "<p>Ayodhya</p>", "<p>Lumbini</p>"],
                    options_hi: ["<p>वैशाली</p>", "<p>मगध</p>",
                                "<p>अयोध्या</p>", "<p>लुम्बिनी</p>"],
                    solution_en: "<p>48.(d) <strong>Lumbini</strong> (Kapilvastu, Nepal). Gautama Buddha (Founder of Buddhism) was born in 563 B.C. His teachings have been inscribed in the Tripitaka (Pali language). Symbols related to Buddha: Lotus and Bull (Birth of Buddha), Horse (leaving his home - Mahabhinishkramana), Bodhi Tree (Enlightenment - Nirvana), Wheel (The first Sermon of Buddha - Dharma chakra Pravartana), Stupa (Death - Mahaparinirvana).</p>",
                    solution_hi: "<p>48.(d) <strong>लुम्बिनी</strong> (कपिलवस्तु, नेपाल)। गौतम बुद्ध (बौद्ध धर्म के संस्थापक) का जन्म 563 ईसा पूर्व में हुआ था। उनकी शिक्षाओं को त्रिपिटक (पाली भाषा) में अंकित किया गया है। बुद्ध से संबंधित प्रतीक: कमल और बैल (बुद्ध का जन्म), घोड़ा (गृह त्यागना - महाभिनिष्क्रमण), बोधि वृक्ष (ज्ञानोदय - निर्वाण), चक्र(बुद्ध का पहला उपदेश - धर्म चक्र प्रवर्तन), स्तूप (मृत्यु - महापरिनिर्वाण)।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "49",
                    section: "33",
                    question_en: "<p>49 The horizontal row of the periodic table is called the-</p>",
                    question_hi: "<p>49. आवर्त सारणी की क्षैतिज पंक्ति को क्या कहा जाता है?</p>",
                    options_en: ["<p>Period</p>", "<p>Table</p>", 
                                "<p>Column</p>", "<p>Group</p>"],
                    options_hi: ["<p>आवर्त</p>", "<p>सारिणी</p>",
                                "<p>स्तंभ</p>", "<p>समूह</p>"],
                    solution_en: "<p>49.(a) <strong>Period. </strong>The Modern Periodic Table contains 7 horizontal rows called periods and 18 vertical columns called Groups. Two rows are separately placed at the bottom of the periodic table for Lanthanides and Actinides. The whole periodic table is divided into four blocks : s-block (alkali and alkaline earth metals); p-block (metals, non-metals, and metalloids); d-block (transition metals) and f-block (Lanthanides and Actinides series).</p>",
                    solution_hi: "<p>49.(a) <strong>आवर्त।</strong> आधुनिक आवर्त सारणी में 7 क्षैतिज पंक्तियाँ हैं जिन्हें आवर्त कहा जाता है और 18 ऊर्ध्वाधर स्तंभ हैं जिन्हें समूह कहा जाता है। लैंथेनाइड्स और एक्टिनाइड्स के लिए आवर्त सारणी के नीचे दो पंक्तियाँ अलग-अलग रखी गई हैं। संपूर्ण आवर्त सारणी को चार ब्लॉकों में विभाजित किया गया है: s-ब्लॉक (क्षार और क्षारीय मृदा धातुएं); p-ब्लॉक (धातुएं, गैर-धातुएं और मेटलॉइड्स); d-ब्लॉक (संक्रमण धातुएं) और f-ब्लॉक (लैंथेनाइड और एक्टिनाइड श्रृंखला)।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "50",
                    section: "33",
                    question_en: "<p>50. If 2<sup>x</sup> = 4<sup>y+1</sup> and 3<sup>y</sup> = 3<sup>x-9</sup>, then the values of x and y , respectively , are:</p>",
                    question_hi: "<p>50. यदि 2<sup>x</sup> = 4<sup>y+1</sup> और 3<sup>y</sup> = 3<sup>x-9</sup>&nbsp;तो क्रमशः x और y के मान हैं:</p>",
                    options_en: ["<p>16 ,7</p>", "<p>-16 ,7</p>", 
                                "<p>-16 ,</p>", "<p>16 ,-7</p>"],
                    options_hi: ["<p>16 ,7</p>", "<p>-16 ,7</p>",
                                "<p>-16 ,</p>", "<p>16 ,-7</p>"],
                    solution_en: "<p>50.(a) <br>2<sup>x</sup> = 4<sup>y+1</sup> &rArr; x = 2y + 2<br>3<sup>y</sup> = 3<sup>x-9</sup>&nbsp; &rArr; y = x - 9<br>Then solving these two equations we get x = 16, y = 7 ;</p>",
                    solution_hi: "<p>50.(a) <br>2<sup>x</sup> = 4<sup>y+1</sup> &rArr; x = 2y + 2<br>3<sup>y</sup> = 3<sup>x-9</sup>&nbsp; &rArr; y = x - 9<br>फिर इन दो समीकरणों को हल करने पर हमें x = 16, y = 7 मिलता है;</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "51",
                    section: "33",
                    question_en: "<p>51. Read the given statements and conclusions carefully and decide which of the given conclusions logically follows (s) from the statements.</p>\n<p><strong>Statements:</strong><br>(1) Some Mumbai is Delhi.<br>(2) Some Kolkata is Mumbai.<br>(3) Some Chennai is Delhi.<br><strong>Conclusions:</strong><br>I. All Kolkata is Chennai.<br>II. No Chennai is Kolkata.</p>",
                    question_hi: "<p>51. दिए गए कथनों और निष्कर्षों का ध्यानपूर्वक अध्ययन करें, और तय करें कि कौन से निष्कर्ष तार्किक रूप से कथनों का पालन करते हैं?</p>\n<p><strong>कथन:</strong> <br>1.कुछ मुम्बई दिल्ली हैं।<br>2. कुछ कोलकाता मुम्बई हैं।<br>3. कुछ चेन्नई दिल्ली हैं।<br><strong>निष्कर्ष:</strong><br>I. सभी कोलकाता चेन्नई हैं।<br>II. कोई चेन्नई कोलकाता नहीं है।</p>",
                    options_en: ["<p>Both conclusions I and II follow</p>", "<p>Neither conclusion I nor II follows</p>", 
                                "<p>Only conclusion II follows</p>", "<p>Only conclusion I follows</p>"],
                    options_hi: ["<p>निष्कर्ष I और II दोनों ही उपयुक्त हैं।</p>", "<p>न निष्कर्ष I न II उपयुक्त है।</p>",
                                "<p>केवल निष्कर्ष II उपयुक्त है।</p>", "<p>केवल निष्कर्ष । उपयुक्त है।</p>"],
                    solution_en: "<p>51.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1738229059905.png\" alt=\"rId14\" width=\"431\" height=\"60\"><br>From the above diagram we can clearly see that neither conclusion I nor II follows.</p>",
                    solution_hi: "<p>51.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1738229060140.png\" alt=\"rId15\" width=\"327\" height=\"52\"><br>उपरोक्त आरेख से हम स्पष्ट रूप से देख सकते हैं कि न तो निष्कर्ष I और न ही II अनुसरण करता है।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "52",
                    section: "33",
                    question_en: "<p>52. Under the provisions of which Article of the Indian Constitution is the President empowered to constitute a Finance Commission at the expiration of every fifth year or at such earlier time as the President considers necessary?</p>",
                    question_hi: "<p>52. भारतीय संविधान के किस अनुच्छेद के प्रावधानों के तहत, राष्ट्रपति को प्रत्येक पांचवें वर्ष की समाप्ति पर या ऐसे पूर्वतर समय पर, जिसे राष्ट्रपति आवश्यक समझे, वित्त आयोग का गठन करने का अधिकार है?</p>",
                    options_en: ["<p>Article 282</p>", "<p>Article 280</p>", 
                                "<p>Article 283</p>", "<p>Article 281</p>"],
                    options_hi: ["<p>अनुच्छेद 282</p>", "<p>अनुच्छेद 280</p>",
                                "<p>अनुच्छेद 283</p>", "<p>अनुच्छेद 281</p>"],
                    solution_en: "<p>52.(b) <strong>Article 280.</strong> Finance Commission- Formed in 1951. It was created to define the financial relations between the Centre and the states. The President of India constitutes the Finance Commission. Composition- Chairman and Four Members. 1st Finance Commission Chairman- K.C. Neogy. Article 281 - Recommendations of the Finance Commission. Article 282 - Expenditure defrayable by the Union or a State out of its revenues. Article 283 - Custody, etc., of Consolidated Funds, Contingency Funds and money credited to the public accounts.</p>",
                    solution_hi: "<p>52.(b) <strong>अनुच्छेद 280 ।</strong> वित्त आयोग- 1951 में गठित । यह केंद्र और राज्यों के बीच वित्तीय संबंधों को निर्धारित करने के लिए बनाया गया था। भारत के राष्ट्रपति वित्त आयोग का गठन करते हैं। संरचना- अध्यक्ष और चार सदस्य। प्रथम वित्त आयोग के अध्यक्ष- के.सी. नियोगी। अनुच्छेद 281 - वित्त आयोग की सिफारिशें। अनुच्छेद 282 - संघ या राज्य द्वारा अपने राजस्व में से भुगतान योग्य व्यय। अनुच्छेद 283 - संचित निधियों, आकस्मिक निधियों और लोक खातों में जमा धन की अभिरक्षा आदि।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "53",
                    section: "33",
                    question_en: "<p>53. What is the role of chlorine gas in the preparation of bleaching powder?</p>",
                    question_hi: "<p>53. ब्लीचिंग पाउडर बनाने में क्लोरीन गैस की क्या भूमिका है?</p>",
                    options_en: ["<p>It is used as a catalyst for the reaction.</p>", "<p>It reacts with dry slaked lime to form bleaching powder.<strong id=\"docs-internal-guid-76978b07-7fff-6cd8-80b7-a97253652043\"><br></strong></p>", 
                                "<p>It is used to neutralise calcium hydroxide.</p>", "<p>It reacts with water to produce hydrogen peroxide.</p>"],
                    options_hi: ["<p>इसका उपयोग प्रतिक्रिया के लिए उत्प्रेरक के रूप में किया जाता है।</p>", "<p>यह ब्लीचिंग पाउडर बनाने के लिए सूखे बुझे चूने के साथ प्रतिक्रिया करता है।</p>",
                                "<p>इसका उपयोग कैल्शियम हाइड्रॉक्साइड को बेअसर करने के लिए किया जाता है।</p>", "<p>यह हाइड्रोजन पेरोक्साइड बनाने के लिए पानी के साथ प्रतिक्रिया करता है।</p>"],
                    solution_en: "<p>53. (b) Chlorine gas plays a key role in the preparation of bleaching powder. In the process, chlorine gas reacts with dry slaked lime (calcium hydroxide, Ca(OH)<sub>2</sub>​) to form bleaching powder (calcium oxychloride, Ca(OCl)<sub>2</sub>). The reaction is as follows: Cl<sub>2</sub> ​+ Ca(OH)<sub>2​</sub> &rarr; Ca(OCl)<sub>2​</sub> + H<sub>2​</sub>​O.</p>",
                    solution_hi: "<p>53. (b) ब्लीचिंग पाउडर बनाने में क्लोरीन गैस एक महत्वपूर्ण भूमिका निभाती है। इस प्रक्रिया में, क्लोरीन गैस सूखे बुझे चूने (कैल्शियम हाइड्रॉक्साइड, Ca(OH)<sub>2</sub>​) के साथ प्रतिक्रिया करके ब्लीचिंग पाउडर (कैल्शियम ऑक्सीक्लोराइड, Ca(OCl)<sub>2</sub>) बनाती है। प्रतिक्रिया इस प्रकार है: Cl<sub>2</sub> ​+ Ca(OH)<sub>2​</sub> &rarr; Ca(OCl)<sub>2​</sub> + H<sub>2​</sub>​O.</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "54",
                    section: "33",
                    question_en: "<p>54. If<strong> </strong>x<sup>2</sup> + 25y<sup>2</sup> = 10xy, then x : y = ?</p>",
                    question_hi: "<p>54. यदि x<sup>2</sup> + 25y<sup>2</sup> = 10xy, तो x : y = ?</p>",
                    options_en: ["<p>2 : 3</p>", "<p>1 : 5</p>", 
                                "<p>5 : 1</p>", "<p>3 : 5</p>"],
                    options_hi: ["<p>2 : 3</p>", "<p>1 : 5</p>",
                                "<p>5 : 1</p>", "<p>3 : 5</p>"],
                    solution_en: "<p>54.(c) <br>x<sup>2</sup> + 25y<sup>2</sup> = 10xy<br>x<sup>2</sup> + 25y<sup>2</sup> - 10xy = 0<br>(x -5y)<sup>2</sup><strong id=\"docs-internal-guid-92fc136d-7fff-6437-de37-8e00e442ee64\">&nbsp;</strong>= 0 &rArr; X = 5y<br><math display=\"inline\"><mfrac><mrow><mi>X</mi></mrow><mrow><mi>Y</mi></mrow></mfrac><mi>&#160;</mi></math>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>1</mn></mfrac></math></p>",
                    solution_hi: "<p>54.(c) <br>x<sup>2</sup> + 25y<sup>2</sup> = 10xy<br>x<sup>2</sup> + 25y<sup>2</sup> - 10xy = 0<br>(x -5y)<sup>2</sup><strong id=\"docs-internal-guid-92fc136d-7fff-6437-de37-8e00e442ee64\">&nbsp;</strong>= 0 &rArr; X = 5y<br><math display=\"inline\"><mfrac><mrow><mi>X</mi></mrow><mrow><mi>Y</mi></mrow></mfrac><mi>&#160;</mi></math>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>1</mn></mfrac></math></p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "55",
                    section: "33",
                    question_en: "<p>55. Study the given diagram carefully and answer the question. The numbers in different sections indicate the number of persons.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1738229060437.png\" alt=\"rId16\" width=\"272\" height=\"146\"> <br>How many such doctors are there who are also joggers but NOT NRIs ?</p>",
                    question_hi: "<p>55. दिए गए आरेख का ध्यानपूर्वक अध्ययन करें और प्रश्न का उत्तर दें। विभिन्न वर्गों व्यक्तियों की संख्या दर्शाती है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1738229060606.png\" alt=\"rId17\" width=\"217\" height=\"121\"> <br>ऐसे कितने डॉक्टर हैं जो जॉगर्स भी हैं लेकिन एनआरआई(NRI) नहीं हैं?</p>",
                    options_en: ["<p>28</p>", "<p>75</p>", 
                                "<p>45</p>", "<p>56</p>"],
                    options_hi: ["<p>28</p>", "<p>75</p>",
                                "<p>45</p>", "<p>56</p>"],
                    solution_en: "<p>55.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1738229060437.png\" alt=\"rId16\" width=\"246\" height=\"133\"><br>Doctors who are also joggers but not NRI&rsquo;s = 45.</p>",
                    solution_hi: "<p>55.(c) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1738229060606.png\" alt=\"rId17\" width=\"239\" height=\"133\"><br>डॉक्टर जो जॉगर्स भी हैं लेकिन एनआरआई नहीं = 45</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "56",
                    section: "33",
                    question_en: "<p>56. Which Article of the India Constitution is related with powers, authority and responsibilities of Panchayats?</p>",
                    question_hi: "<p>56. भारतीय संविधान का कौन सा अनुच्छेद पंचायत की शक्तियों, प्राधिकार और उत्तरदायित्वों से संबंधित है ?</p>",
                    options_en: ["<p>243G</p>", "<p>243F</p>", 
                                "<p>243E</p>", "<p>243H</p>"],
                    options_hi: ["<p>243G</p>", "<p>243F</p>",
                                "<p>243E</p>", "<p>243H</p>"],
                    solution_en: "<p>56.(a) <strong>243G. </strong>Part IX of the Indian Constitution deals with the Panchayats (Articles 243 to 243O). Article 243E - Duration of Panchayats. Article 243F - Disqualifications for membership. Article 243H - Powers to impose taxes by, and Funds of, the Panchayats.</p>",
                    solution_hi: "<p>56.(a) <strong>243G।</strong> भारतीय संविधान का भाग IX पंचायतों से संबंधित है (अनुच्छेद 243 से 243O)। अनुच्छेद 243E- पंचायतों की अवधि। अनुच्छेद 243F - सदस्यता के लिए अयोग्यताएँ। अनुच्छेद 243H - पंचायतों द्वारा और उनकी निधियों पर कर लगाने की शक्तियाँ।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "57",
                    section: "33",
                    question_en: "<p>57. A homologous series is a group of organic compounds that have:</p>",
                    question_hi: "<p>57. समजातीय श्रृंखला कार्बनिक यौगिकों का एक समूह है जिसमें:</p>",
                    options_en: ["<p>the same structural formula<strong id=\"docs-internal-guid-9a243b59-7fff-961c-a3fa-12b030ceb79f\"><br></strong></p>", "<p>the same functional group but differ by a CH<sub>2</sub> group<strong id=\"docs-internal-guid-796a40a7-7fff-c10f-3cf8-2af3437ed20f\"><br></strong></p>", 
                                "<p>the same molecular formula</p>", "<p>no relation to each other</p>"],
                    options_hi: ["<p>समान संरचनात्मक सूत्र</p>", "<p>समान कार्यात्मक समूह लेकिन CH<sub>2</sub> समूह द्वारा भिन्न</p>",
                                "<p>समान आणविक सूत्र</p>", "<p>एक दूसरे से कोई संबंध नहीं</p>"],
                    solution_en: "<p>57. (b) <strong>the same functional group but differ by a CH<sub>2</sub> group.</strong> A homologous series is a group of organic compounds that share the same functional group and exhibit similar chemical properties but differ from each other by a CH<sub>2</sub> group (a methylene group) in their structure. This series follows a regular pattern, where each successive compound has one more CH<sub>2</sub> unit than the previous one. For example, the alkane series (methane (CH<sub>4</sub>), ethane (C<sub>2</sub>H<sub>6</sub>), propane (C<sub>3</sub>H<sub>8</sub>), etc.) is a homologous series where each compound differs by one CH<sub>2</sub> group.</p>",
                    solution_hi: "<p>57. (b) <strong>समान कार्यात्मक समूह लेकिन CH2 समूह द्वारा भिन्न।</strong> समजातीय श्रृंखला कार्बनिक यौगिकों का एक समूह है जो समान कार्यात्मक समूह साझा करते हैं और समान रासायनिक गुण प्रदर्शित करते हैं लेकिन उनकी संरचना में CH<sub>2</sub> समूह (एक मेथिलीन समूह) द्वारा एक दूसरे से भिन्न होते हैं। यह श्रृंखला एक नियमित पैटर्न का अनुसरण करती है, जहाँ प्रत्येक क्रमिक यौगिक में पिछले एक की तुलना में एक अधिक CH<sub>2</sub> इकाई होती है। उदाहरण के लिए, एल्केन श्रृंखला (मीथेन (CH<sub>4</sub>), ईथेन (C<sub>2</sub>H<sub>6</sub>), प्रोपेन (C<sub>3</sub>H<sub>8</sub>), आदि) एक समजातीय श्रृंखला है जहाँ प्रत्येक यौगिक एक CH<sub>2 </sub>समूह द्वारा भिन्न होता है।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "58",
                    section: "33",
                    question_en: "<p>58. If <math display=\"inline\"><mfrac><mrow><mi>x</mi></mrow><mrow><mi>y</mi><mi>&#160;</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>5</mn></mfrac></math>, then <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mi>x</mi><mo>+</mo><mn>7</mn><mi>y</mi></mrow><mrow><mn>5</mn><mi>x</mi><mo>-</mo><mn>7</mn><mi>y</mi></mrow></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>6</mn><mi>x</mi><mo>+</mo><mn>4</mn><mi>y</mi></mrow><mrow><mn>7</mn><mi>x</mi><mo>-</mo><mn>8</mn><mi>y</mi></mrow></mfrac></math> is equal to :</p>",
                    question_hi: "<p>58. यदि <math display=\"inline\"><mfrac><mrow><mi>x</mi></mrow><mrow><mi>y</mi><mi>&#160;</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>5</mn></mfrac></math>, तब <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mi>x</mi><mo>+</mo><mn>7</mn><mi>y</mi></mrow><mrow><mn>5</mn><mi>x</mi><mo>-</mo><mn>7</mn><mi>y</mi></mrow></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>6</mn><mi>x</mi><mo>+</mo><mn>4</mn><mi>y</mi></mrow><mrow><mn>7</mn><mi>x</mi><mo>-</mo><mn>8</mn><mi>y</mi></mrow></mfrac></math> किसके समतुल्य है ?</p>",
                    options_en: ["<p><math display=\"inline\"><mfrac><mrow><mn>11</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>", "<p>- <math display=\"inline\"><mfrac><mrow><mn>22</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>", 
                                "<p><math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>", "<p>- <math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math></p>"],
                    options_hi: ["<p><math display=\"inline\"><mfrac><mrow><mn>11</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>", "<p>- <math display=\"inline\"><mfrac><mrow><mn>22</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>",
                                "<p><math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>", "<p>- <math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math></p>"],
                    solution_en: "<p>58.(b) Here 5x = 4y&nbsp;,<br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mi>x</mi><mo>+</mo><mn>7</mn><mi>y</mi></mrow><mrow><mn>5</mn><mi>x</mi><mo>-</mo><mn>7</mn><mi>y</mi></mrow></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>6</mn><mi>x</mi><mo>+</mo><mn>4</mn><mi>y</mi></mrow><mrow><mn>7</mn><mi>x</mi><mo>-</mo><mn>8</mn><mi>y</mi></mrow></mfrac></math><br>= <math display=\"inline\"><mfrac><mrow><mn>4</mn><mi>y</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>7</mn><mi>y</mi></mrow><mrow><mn>4</mn><mi>y</mi><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>7</mn><mi>y</mi></mrow></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>6</mn><mi>x</mi><mo>+</mo><mn>5</mn><mi>x</mi></mrow><mrow><mn>7</mn><mi>x</mi><mo>-</mo><mn>10</mn><mi>x</mi></mrow></mfrac></math><br>= <math display=\"inline\"><mfrac><mrow><mn>11</mn></mrow><mrow><mo>-</mo><mi>&#160;</mi><mn>3</mn></mrow></mfrac></math> + <math display=\"inline\"><mfrac><mrow><mn>11</mn></mrow><mrow><mo>-</mo><mi>&#160;</mi><mn>3</mn></mrow></mfrac></math><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mrow><mo>-</mo><mi>&#160;</mi><mn>3</mn></mrow></mfrac></math></p>",
                    solution_hi: "<p>58.(b) Here 5x = 4y&nbsp;,<br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mi>x</mi><mo>+</mo><mn>7</mn><mi>y</mi></mrow><mrow><mn>5</mn><mi>x</mi><mo>-</mo><mn>7</mn><mi>y</mi></mrow></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>6</mn><mi>x</mi><mo>+</mo><mn>4</mn><mi>y</mi></mrow><mrow><mn>7</mn><mi>x</mi><mo>-</mo><mn>8</mn><mi>y</mi></mrow></mfrac></math><br>= <math display=\"inline\"><mfrac><mrow><mn>4</mn><mi>y</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>7</mn><mi>y</mi></mrow><mrow><mn>4</mn><mi>y</mi><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>7</mn><mi>y</mi></mrow></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>6</mn><mi>x</mi><mo>+</mo><mn>5</mn><mi>x</mi></mrow><mrow><mn>7</mn><mi>x</mi><mo>-</mo><mn>10</mn><mi>x</mi></mrow></mfrac></math><br>= <math display=\"inline\"><mfrac><mrow><mn>11</mn></mrow><mrow><mo>-</mo><mi>&#160;</mi><mn>3</mn></mrow></mfrac></math> + <math display=\"inline\"><mfrac><mrow><mn>11</mn></mrow><mrow><mo>-</mo><mi>&#160;</mi><mn>3</mn></mrow></mfrac></math><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mrow><mo>-</mo><mi>&#160;</mi><mn>3</mn></mrow></mfrac></math></p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "59",
                    section: "33",
                    question_en: "<p>59. Seven persons N, O, P, Q, R, S, and T are sitting in a row facing North. Only three persons are sitting between P and T. N is sitting at one end. P is sitting somewhere to the left of N. O is to the immediate right of P and immediate left of R. Exactly two people are sitting to the right of T who is sitting to the immediate right of Q . Who is sitting in the middle?</p>",
                    question_hi: "<p>59. व्यक्ति N, O, P, Q, R, S और T एक पंक्ति में उत्तर की ओर मुख करके बैठे हैं। P और T के बीच केवल तीन व्यक्ति बैठे हैं। N किसी एक सिरे पर बैठा है। P, N के बाईं ओर किसी स्थान पर बैठा है। O, P के दाईं ओर ठीक बगल में, और R के बाईं ओर ठीक बगल में बैठा है। T, जो Q के दाईं ओर ठीक बगल में बैठा है, के दाईं ओर केवल दो व्यक्ति बैठे हैं। पंक्ति के ठीक मध्य में कौन बैठा है?</p>",
                    options_en: ["<p>S</p>", "<p>Q</p>", 
                                "<p>O</p>", "<p>T</p>"],
                    options_hi: ["<p>S</p>", "<p>Q</p>",
                                "<p>O</p>", "<p>T</p>"],
                    solution_en: "<p>59.(b) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1738229060834.png\" alt=\"rId18\" width=\"225\" height=\"77\"><br>Q is sitting in the middle of the row.</p>",
                    solution_hi: "<p>59.(b) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1738229060834.png\" alt=\"rId18\" width=\"225\" height=\"77\"><br>Q पंक्ति के मध्य में बैठा है।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "60",
                    section: "33",
                    question_en: "<p>60. Which river originates at Mahabaleshwar in Maharashtra, passes through Sangli and meets the sea in the Bay of Bengal at Hamasaladeevi in Andhra Pradesh?</p>",
                    question_hi: "<p>60. कौन सी नदी महाराष्ट्र में महाबलेश्वर से निकलती है, और सांगली से गुजरते हुए आंध्र प्रदेश के हमसलादीवी (Hamasaladeevi) में बंगाल की खाड़ी में समुद्र में मिल जाती है?</p>",
                    options_en: ["<p>Krishna</p>", "<p>Godavari</p>", 
                                "<p>Narmada</p>", "<p>Tapti</p>"],
                    options_hi: ["<p>कृष्ण</p>", "<p>गोदावरी</p>",
                                "<p>नर्मदा</p>", "<p>ताप्ती</p>"],
                    solution_en: "<p>60.(a) <strong>Krishna River.</strong> Godawari/Dakshina Ganga - 2nd longest river in India. Origin - Trimbakeshwar, Nashik, Maharashtra. Tributaries - Pravara, Purna, Manjra, Penganga, Wardha, Wainganga, Indravati, Maner and the Sabri. Narmada / Reva River Source - Amarkantak range of Madhya Pradesh. Tributaries - Burhner, Halon, Heran, Banjar, Dudhi, Shakkar, Tawa, Barna, Kolar, Ganjal, Beda and Orsang. Tapi River Origin - Multai, Madhya Pradesh.</p>",
                    solution_hi: "<p>60.(a) <strong>कृष्णा नदी।</strong> गोदावरी/दक्षिणा गंगा - भारत की दूसरी सबसे लंबी नदी। उत्पत्ति - त्र्यंबकेश्वर, नासिक, महाराष्ट्र। सहायक नदियाँ - प्रवरा, पूर्णा, मंजरा, पेंगंगा, वर्धा, वैनगंगा, इंद्रावती, मनेर और सबरी। नर्मदा/रेवा नदी का उद्गम - मध्य प्रदेश की अमरकंटक श्रेणी। सहायक नदियाँ - बुरहनेर, हालोन, हेरान, बंजार, दुधी, शक्कर, तवा, बरना, कोलार, गंजाल, बेदा और ओरसंग। तापी नदी का उद्गम - मुलताई, मध्य प्रदेश।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "61",
                    section: "33",
                    question_en: "<p>61. Which of the following factors is primarily responsible for the survival of species?</p>",
                    question_hi: "<p>61. निम्नलिखित में से कौन सा कारक मुख्य रूप से प्रजातियों के अस्तित्व के लिए जिम्मेदार है?</p>",
                    options_en: ["<p>Variations</p>", "<p>Temperature<strong id=\"docs-internal-guid-4429d970-7fff-0bea-0d21-e93de33c1296\"><br></strong></p>", 
                                "<p>Niche<strong id=\"docs-internal-guid-01323918-7fff-3bc6-ce0c-3e00c86774fb\"><br></strong></p>", "<p>Mutation</p>"],
                    options_hi: ["<p>विविधताएँ</p>", "<p>तापमान</p>",
                                "<p>आला</p>", "<p>उत्परिवर्तन</p>"],
                    solution_en: "<p>61. (a) <strong>Variations</strong> within a species are primarily responsible for its survival because they provide the genetic diversity needed for natural selection. These variations allow some individuals to possess traits that are better suited for surviving in a given environment, leading to higher reproductive success and the passing on of favorable traits to future generations. This process is a key factor in evolution and ensures that species can adapt to changing environments over time.<strong> Temperature:</strong> While temperature can affect survival, it is not the primary factor driving survival. Species adapt to various temperatures, but it is the variations within species that determine their long-term survival. <strong>Niche:</strong> A niche defines a species\' role and habitat, but it is the ability of individuals to adapt through variation that affects survival.Mutation: <strong>Mutations</strong> contribute to variation, but they are not as primary as the variations themselves, which are the result of both genetic and environmental factors.</p>",
                    solution_hi: "<p>61. (a) किसी प्रजाति के भीतर विविधताएँ उसके अस्तित्व के लिए मुख्य रूप से जिम्मेदार होती हैं क्योंकि वे प्राकृतिक चयन के लिए आवश्यक आनुवंशिक विविधता प्रदान करती हैं। ये विविधताएँ कुछ व्यक्तियों को ऐसे लक्षण रखने की अनुमति देती हैं जो किसी दिए गए वातावरण में जीवित रहने के लिए बेहतर अनुकूल होते हैं, जिससे उच्च प्रजनन सफलता और भविष्य की पीढ़ियों को अनुकूल लक्षण प्राप्त होते हैं। यह प्रक्रिया विकास में एक महत्वपूर्ण कारक है और यह सुनिश्चित करती है कि प्रजातियाँ समय के साथ बदलते वातावरण के अनुकूल हो सकें। <strong>तापमान: </strong>जबकि तापमान अस्तित्व को प्रभावित कर सकता है, यह अस्तित्व को चलाने वाला प्राथमिक कारक नहीं है। प्रजातियाँ विभिन्न तापमानों के अनुकूल हो जाती हैं, लेकिन यह प्रजातियों के भीतर भिन्नताएँ हैं जो उनके दीर्घकालिक अस्तित्व को निर्धारित करती हैं। <strong>आला:</strong> एक आला एक प्रजाति की भूमिका और निवास स्थान को परिभाषित करता है, लेकिन यह व्यक्तियों की विविधता के माध्यम से अनुकूलन करने की क्षमता है जो अस्तित्व को प्रभावित करती है।<strong> उत्परिवर्तन:</strong> उत्परिवर्तन भिन्नता में योगदान करते हैं, लेकिन वे स्वयं भिन्नताओं की तरह प्राथमिक नहीं हैं, जो आनुवंशिक और पर्यावरणीय दोनों कारकों का परिणाम हैं।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "62",
                    section: "33",
                    question_en: "<p>62. The mean score of class A of 40 students in a mathematics test of 30 marks is 23. The mean score of class B of 45 students in the same test is 22. What is the ratio of mean scores of both the class to that of class A.</p>",
                    question_hi: "<p>62. 30 अंकों की गणित की परीक्षा में 40 छात्रों के कक्षा A के औसत प्राप्त अंक 23 है। इसी परीक्षा में 45 छात्रों के कक्षा B का औसत प्राप्त अंक 22 है। दोनों कक्षाओं के औसत अंकों का कक्षा A के औसत अंकों से अनुपात कितना है ?</p>",
                    options_en: ["<p>382 : 391</p>", "<p>380 : 391</p>", 
                                "<p>390 : 382</p>", "<p>391 : 382</p>"],
                    options_hi: ["<p>382 : 391</p>", "<p>380 : 391</p>",
                                "<p>390 : 382</p>", "<p>391 : 382</p>"],
                    solution_en: "<p>62.(a) Mean of class A + Mean of class B = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>40</mn><mo>&#215;</mo><mn>23</mn><mo>+</mo><mn>45</mn><mo>&#215;</mo><mn>22</mn></mrow><mn>85</mn></mfrac></math> <br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>920</mn><mo>+</mo><mn>990</mn></mrow><mn>85</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1910</mn><mn>85</mn></mfrac></math><br>Required ratio = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1910</mn><mrow><mn>85</mn><mo>&#215;</mo><mn>23</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>382</mn><mn>391</mn></mfrac></math></p>",
                    solution_hi: "<p>62.(a) कक्षा A का माध्य + कक्षा B का माध्य = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>40</mn><mo>&#215;</mo><mn>23</mn><mo>+</mo><mn>45</mn><mo>&#215;</mo><mn>22</mn></mrow><mn>85</mn></mfrac></math> <br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>920</mn><mo>+</mo><mn>990</mn></mrow><mn>85</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1910</mn><mn>85</mn></mfrac></math><br>आवश्यक अनुपात = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1910</mn><mrow><mn>85</mn><mo>&#215;</mo><mn>23</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>382</mn><mn>391</mn></mfrac></math></p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "63",
                    section: "33",
                    question_en: "<p>63. Six girls, P, Q, R, S, T and V, are sitting in a circle, facing the centre of the circle. P sits to the immediate right of Q . S is 3rd to the right of P, and to the immediate left of R. V and T are immediate neighbours. T is second to the left of R. Who is to the immediate left of V ?</p>",
                    question_hi: "<p>63. छह लड़कियां, P, Q, R, S, T और V एक वृत्ताकार व्यवस्था में वृत्त के केंद्र की ओर मुख करके बैठी हुई हैं। P, Q के दाईं ओर ठीक बगल में बैठी है। S, P के दाईं ओर तीसरे स्थान पर और R के बाईं ओर ठीक बगल में बैठी है। V और T दोनों अगल-बगल बैठी हुई हैं। T, R के बाईं ओर दूसरे स्थान पर बैठी है। V के बाईं ओर ठीक बगल में कौन सी लड़की बैठी हुई है?</p>",
                    options_en: ["<p>S</p>", "<p>Q</p>", 
                                "<p>P</p>", "<p>T</p>"],
                    options_hi: ["<p>S</p>", "<p>Q</p>",
                                "<p>P</p>", "<p>T</p>"],
                    solution_en: "<p>63.(c) In the following sitting arrangement, the immediate left of V is P. <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1738229061048.png\" alt=\"rId19\" width=\"125\" height=\"159\"></p>",
                    solution_hi: "<p>63.(c) निम्नलिखित बैठने की व्यवस्था में, V के ठीक बायें ओर P है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1738229061048.png\" alt=\"rId19\" width=\"120\" height=\"151\"></p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "64",
                    section: "33",
                    question_en: "<p>64. Which of the following lakes is a brackish water lake?</p>",
                    question_hi: "<p>64. निम्नलिखित में से कौन सी झील खारे पानी की झील है?</p>",
                    options_en: ["<p>Pulicat Lake</p>", "<p>Dal Lake</p>", 
                                "<p>Loktak Lake</p>", "<p>Wular Lake</p>"],
                    options_hi: ["<p>पुलिकट झील</p>", "<p>डल झील</p>",
                                "<p>लोकटक झील</p>", "<p>वुलर झील</p>"],
                    solution_en: "<p>64.(a) <strong>Pulicat </strong>(Andhra Pradesh and Tamil Nadu) - The second largest brackish (saltier than fresh water) water lagoon in India, (after Chilika Lake). Dal Lake (Srinagar), Wular Lake - The largest freshwater lake in India, located in Bandipora district of Jammu and Kashmir, India.</p>",
                    solution_hi: "<p>64.(a) <strong>पुलिकट</strong>। पुलिकट (आंध्र प्रदेश और तमिलनाडु) - भारत में दूसरा सबसे बड़ा खारा (ताजे पानी से अधिक खारा) पानी का लैगून, (चिल्का झील के बाद)। डल झील (श्रीनगर), वुलर झील - भारत की सबसे बड़ी मीठे पानी की झील, भारत के जम्मू और कश्मीर के बांदीपोरा जिले में स्थित है।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "65",
                    section: "33",
                    question_en: "<p>65. Which of the following is NOT a method of pollination?</p>",
                    question_hi: "<p>65. निम्नलिखित में से कौन परागण की विधि नहीं है?</p>",
                    options_en: ["<p>Self-pollination</p>", "<p>Wind pollination</p>", 
                                "<p>Water pollination<strong id=\"docs-internal-guid-bbe5e929-7fff-7ae9-5c87-b73809eac614\"><br></strong></p>", "<p>Asexual pollination</p>"],
                    options_hi: ["<p>स्व-परागण</p>", "<p>वायु परागण</p>",
                                "<p>जल परागण</p>", "<p>अलैंगिक परागण</p>"],
                    solution_en: "<p>65. (d) <strong>Asexual pollination</strong>. Pollination refers to the transfer of pollen from the male part (anther) to the female part (stigma) of a flower, enabling fertilization to occur. There are several methods of pollination: Self-pollination: This occurs when pollen from the same flower or plant fertilizes the ovules of the same plant. Wind pollination: In this method, pollen is carried by the wind from one flower to another, commonly seen in plants like grasses and trees. Water pollination: Some aquatic plants use water as a medium to carry pollen to other plants, allowing fertilization. However, Asexual pollination is not a valid term in the context of pollination, as asexual reproduction involves the production of offspring without the involvement of pollen or fertilization. Therefore, asexual pollination is not a recognized method of pollination.</p>",
                    solution_hi: "<p>65. (d) <strong>अलैंगिक परागण।</strong> परागण से तात्पर्य फूल के नर भाग (परागकोश) से मादा भाग (कलंक) में पराग के स्थानांतरण से है, जिससे निषेचन संभव होता है। परागण की कई विधियाँ हैं: स्व-परागण: यह तब होता है जब एक ही फूल या पौधे से पराग उसी पौधे के बीजांड को निषेचित करता है। वायु परागण: इस विधि में, पराग हवा द्वारा एक फूल से दूसरे फूल तक पहुँचाया जाता है, जो आमतौर पर घास और पेड़ों जैसे पौधों में देखा जाता है। जल परागण: कुछ जलीय पौधे पराग को दूसरे पौधों तक ले जाने के लिए पानी का उपयोग करते हैं, जिससे निषेचन संभव होता है। हालाँकि, परागण के संदर्भ में अलैंगिक परागण एक मान्य शब्द नहीं है, क्योंकि अलैंगिक प्रजनन में पराग या निषेचन की भागीदारी के बिना संतानों का उत्पादन शामिल होता है। इसलिए, अलैंगिक परागण परागण की मान्यता प्राप्त विधि नहीं है।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "66",
                    section: "33",
                    question_en: "<p>66. A can do a piece of work in 12 days. B can do the same work in 15 days. C can do the same work in 9 days. The time taken by them to finish the same work if they work together is:</p>",
                    question_hi: "<p>66. A किसी काम को 12 दिनों में कर सकता है। B उसी काम को 15 दिनों में कर सकता है। C उसी काम को 9 दिनों में कर सकता है। यदि वे एक साथ कार्य करते हैं तो उसी कार्य को पूरा करने में उनके द्वारा लिया गया समय ज्ञात कीजिए।</p>",
                    options_en: ["<p><math display=\"inline\"><mn>4</mn><mfrac><mrow><mn>39</mn></mrow><mrow><mn>47</mn></mrow></mfrac></math> days</p>", "<p><math display=\"inline\"><mn>2</mn><mfrac><mrow><mn>39</mn></mrow><mrow><mn>47</mn></mrow></mfrac></math> days</p>", 
                                "<p><math display=\"inline\"><mi>&#160;</mi><mn>5</mn><mfrac><mrow><mn>39</mn></mrow><mrow><mn>47</mn></mrow></mfrac></math> days</p>", "<p><math display=\"inline\"><mi>&#160;</mi><mn>3</mn><mfrac><mrow><mn>39</mn></mrow><mrow><mn>47</mn></mrow></mfrac></math> days</p>"],
                    options_hi: ["<p><math display=\"inline\"><mn>4</mn><mfrac><mrow><mn>39</mn></mrow><mrow><mn>47</mn></mrow></mfrac></math> दिन</p>", "<p><math display=\"inline\"><mn>2</mn><mfrac><mrow><mn>39</mn></mrow><mrow><mn>47</mn></mrow></mfrac></math> दिन</p>",
                                "<p><math display=\"inline\"><mi>&#160;</mi><mn>5</mn><mfrac><mrow><mn>39</mn></mrow><mrow><mn>47</mn></mrow></mfrac></math> दिन</p>", "<p><math display=\"inline\"><mi>&#160;</mi><mn>3</mn><mfrac><mrow><mn>39</mn></mrow><mrow><mn>47</mn></mrow></mfrac></math> दिन</p>"],
                    solution_en: "<p>66.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1738229061289.png\" alt=\"rId20\" width=\"231\" height=\"141\"><br>So required day = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>180</mn><mrow><mn>15</mn><mo>+</mo><mn>12</mn><mo>+</mo><mn>20</mn></mrow></mfrac></math> <br>&rArr; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>180</mn><mn>47</mn></mfrac></math> = 3<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>39</mn><mn>47</mn></mfrac></math> days</p>",
                    solution_hi: "<p>66.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1738229061456.png\" alt=\"rId21\" width=\"196\" height=\"124\"><br>अतः अभीष्ट दिन =&nbsp;<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>180</mn><mrow><mn>15</mn><mo>+</mo><mn>12</mn><mo>+</mo><mn>20</mn></mrow></mfrac></math> <br>&rArr; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>180</mn><mn>47</mn></mfrac></math> = 3<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>39</mn><mn>47</mn></mfrac></math>दिन</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "67",
                    section: "33",
                    question_en: "<p>67. There are eight persons P. Q, R, S, T, U. V and W, sitting around a rectangular table in such a way that four of them sit at four corners of the table while the other four sit in the middle of each of the four sides. All the eight persons face toward the centre. T sits third to the right of V, but T is not the immediate neighbour of U. R sits second to the right of Q . S sits second to the right of U. P sits third to the right Q . Who among the following is sitting 4th to the right of P ?</p>",
                    question_hi: "<p>67. आठ व्यक्ति P. Q, R, S, T, U. V और W एक आयताकार मेज के चारों ओर इस प्रकार बैठे हैं कि उनमें से चार मेज के चारों कोनों पर बैठते हैं जबकि अन्य चार मेज के चारों ओर बैठते हैं। चारों भुजाओं में से प्रत्येक के मध्य में। सभी आठ व्यक्तियों का मुख केंद्र की ओर है। T, V के दायें से तीसरे स्थान पर बैठा है, लेकिन T, U का निकटतम पड़ोसी नहीं है। R, Q के दायें से दूसरे स्थान पर बैठा है। S, U के दायें से दूसरे स्थान पर बैठा है। P, Q के दायें से तीसरे स्थान पर बैठा है। निम्नलिखित में से कौन P के दायें से चौथे स्थान पर बैठा है?</p>",
                    options_en: ["<p>W</p>", "<p>R</p>", 
                                "<p>U</p>", "<p>S</p>"],
                    options_hi: ["<p>W</p>", "<p>R</p>",
                                "<p>U</p>", "<p>S</p>"],
                    solution_en: "<p>67.(d) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1738229061605.png\" alt=\"rId22\" width=\"140\" height=\"144\"><br>Clearly , S is sitting 4th to the right of P.</p>",
                    solution_hi: "<p>67.(d) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1738229061605.png\" alt=\"rId22\" width=\"136\" height=\"139\"><br>स्पष्ट रूप से, S, P के दायें से चौथे स्थान पर बैठा है।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "68",
                    section: "33",
                    question_en: "<p>68. In which state did the Indian Air Force (IAF) launch &lsquo;Gagan Shakti-2024,&rsquo; a 10-day war exercise in April 2024?</p>",
                    question_hi: "<p>68. भारतीय वायु सेना (IAF) ने अप्रैल 2024 में किस राज्य में 10 दिवसीय युद्ध अभ्यास &lsquo;गगन शक्ति-2024&rsquo; शुरू किया?</p>",
                    options_en: ["<p>Gujarat</p>", "<p>Maharashtra<strong id=\"docs-internal-guid-e381b21a-7fff-240d-7317-e6d5fb95565e\"><br></strong></p>", 
                                "<p>Rajasthan<strong id=\"docs-internal-guid-01821626-7fff-51a0-c4c0-ad88d32d5f83\"><br></strong></p>", "<p>Arunachal Pradesh</p>"],
                    options_hi: ["<p>गुजरात</p>", "<p>महाराष्ट्र</p>",
                                "<p>राजस्थान</p>", "<p>अरुणाचल प्रदेश</p>"],
                    solution_en: "<p>68. (c)<strong> Rajasthan.</strong> The Indian Air Force (IAF) has launched a 10-day war exercise named &lsquo;Gagan Shakti-2024&rsquo; from April 1 to April 10, 2024, to display its prowess in executing high-intensity operations across India. The Pokhran Field Firing Range in Jaisalmer, Rajasthan; Bhuj in Gujarat; Ladakh, Arunachal Pradesh; and other locations nationwide will host the exercise.</p>",
                    solution_hi: "<p>68. (c)<strong> राजस्थान।</strong> भारतीय वायु सेना (IAF) ने भारत भर में उच्च तीव्रता वाले अभियानों को अंजाम देने में अपनी क्षमता दिखाने के लिए 1 अप्रैल से 10 अप्रैल, 2024 तक &lsquo;गगन शक्ति-2024&rsquo; नामक 10 दिवसीय युद्ध अभ्यास शुरू किया है। जैसलमेर, राजस्थान में पोखरण फील्ड फायरिंग रेंज; गुजरात में भुज; लद्दाख, अरुणाचल प्रदेश; और देश भर के अन्य स्थानों पर अभ्यास की मेजबानी की जाएगी।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "69",
                    section: "33",
                    question_en: "<p>69. Of the food items listed below, which is the richest source of vitamin C?</p>",
                    question_hi: "<p>69. नीचे सूचीबद्ध खाद्य पदार्थों में से विटामिन C का सबसे प्रचुर स्रोत कौन सा है?</p>",
                    options_en: ["<p>Eggs</p>", "<p>Citrus fruits</p>", 
                                "<p>Pulses</p>", "<p>Dates</p>"],
                    options_hi: ["<p>अंडे</p>", "<p>खट्टे फल</p>",
                                "<p>दालें</p>", "<p>खजूर</p>"],
                    solution_en: "<p>69.(b) <strong>Citrus fruits. </strong>Sources of Vitamin C - Kiwifruit, Orange, Broccoli, Strawberry, Mango, Lemon, Papaya, Tomatoes, Potato, Pineapple. Eggs are a good source of vitamin A, D, E, B<sub>6</sub>, B<sub>12</sub> and vitamin K. Dates {Contain vitamins B<sub>1</sub> (thiamine), B<sub>2</sub> (riboflavin), Nicotinic acid (niacin) and vitamin A} - Good source of antioxidants, mainly carotenoids and phenolics.</p>",
                    solution_hi: "<p>69.(b) <strong>खट्टे फल। </strong>विटामिन C के स्रोत - कीवीफ्रूट, संतरा, ब्रोकोली, स्ट्रॉबेरी, आम, नींबू, पपीता, टमाटर, आलू, अनानास। अंडे विटामिन A, D, E, B<sub>6</sub>, B<sub>12</sub> और विटामिन K का अच्छा स्रोत हैं। खजूर {इसमें विटामिन B<sub>1</sub> (थायमिन), B<sub>2</sub> (राइबोफ्लेविन), निकोटिनिक अम्ल (नियासिन) और विटामिन A होता है) - एंटीऑक्सिडेंट, मुख्य रूप से कैरोटीनॉयड और फेनोलिक्स का अच्छा स्रोत है।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "70",
                    section: "33",
                    question_en: "<p>70. The speed of a bus increases by 4 km/h after every two hours. If the bus covers a distance of 80 km in the first two hours, then the total distance covered by the bus in 10 hours will be:</p>",
                    question_hi: "<p>70. एक बस की चाल हर दो घंटे के बाद 4 km/h बढ़ जाती है। यदि बस पहले दो घंटे में 80 km की दूरी तय करती है, तो बस द्वारा 10 घंटे में तय की गई कुल दूरी ज्ञात कीजिए।</p>",
                    options_en: ["<p>440 km</p>", "<p>480 km</p>", 
                                "<p>460 km</p>", "<p>470 km</p>"],
                    options_hi: ["<p>440 km</p>", "<p>480 km</p>",
                                "<p>460 km</p>", "<p>470 km</p>"],
                    solution_en: "<p>70.(b) <br>Let the speed&nbsp;= a, a + 4, a + 8, a + 12, a + 16<br>According to the question,<br>a + a = 80 = a = 40<br>So, distance for first two hours&nbsp;= 40 + 40 = 80 km<br>Distance for next two hours&nbsp;= 44 + 44 = 88 km<br>Distance for next two hours&nbsp;= 48 + 48 = 96 km<br>Distance for next two hours&nbsp;= 52 + 52 = 104 km<br>Distance for next two hours&nbsp;= 56 + 56 = 112 km <br>Total distance = 80 + 88 + 96 + 104 + 112 <br>= 480 km</p>",
                    solution_hi: "<p>70.(b)<br>माना गति = a, a + 4, a + 8, a + 12, a + 16<br>प्रश्न के अनुसार,<br>a + a = 80 =&nbsp;a = 40<br>अत: पहले दो घंटे की दूरी = 40 + 40 = 80 km<br>अगले दो घंटे की दूरी = 44 + 44 = 88 km<br>अगले दो घंटे की दूरी = 48 + 48 = 96 km<br>अगले दो घंटे की दूरी = 52 + 52 = 104 km<br>अगले दो घंटे की दूरी = 56 + 56 = 112 km<br>कुल दूरी = 80 + 88 + 96 + 104 + 112 <br>= 480 km</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "71",
                    section: "33",
                    question_en: "<p>71. Pole E is to the east of pole R. Pole G is to the west of pole R and north of pole A. Pole T is to the south of pole A. What is the position of pole R with respect to pole A?</p>",
                    question_hi: "<p>71. स्तंभ E, स्तंभ R के पूर्व में है। स्तंभ G, स्तंभ R के पश्चिम में और स्तंभ A के उत्तर में है। स्तंभ T, स्तंभ A के दक्षिण में है। स्तंभ A के सापेक्ष में स्तंभ R की स्थिति क्या है?</p>",
                    options_en: ["<p>North - east</p>", "<p>South - east</p>", 
                                "<p>North</p>", "<p>North - west</p>"],
                    options_hi: ["<p>उत्तर - पूर्व</p>", "<p>दक्षिण - पूर्व</p>",
                                "<p>उत्तर</p>", "<p>उत्तर - पश्चिम</p>"],
                    solution_en: "<p>71.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1738229061803.png\" alt=\"rId23\" width=\"142\" height=\"140\"><br>R is at <strong>North-east</strong> with respect to Pole A.</p>",
                    solution_hi: "<p>71.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1738229061803.png\" alt=\"rId23\" width=\"132\" height=\"130\"><br>R, स्तंभ A के संबंध में <strong>उत्तर-पूर्व</strong> में है।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "72",
                    section: "33",
                    question_en: "<p>72. Which state was recognized as the &lsquo;petro capital&rsquo; of India in January 2024?</p>",
                    question_hi: "<p>72. जनवरी 2024 में किस राज्य को भारत की \'पेट्रो राजधानी\' के रूप में मान्यता दी गई?</p>",
                    options_en: ["<p>Gujarat</p>", "<p>Karnataka<strong id=\"docs-internal-guid-78c8193f-7fff-afc9-3401-7c602c5e82a8\"><br></strong></p>", 
                                "<p>Maharashtra<strong id=\"docs-internal-guid-3d3e361b-7fff-d1c5-2c7f-735bd58be024\"><br></strong></p>", "<p>Odisha</p>"],
                    options_hi: ["<p>गुजरात</p>", "<p>कर्नाटक</p>",
                                "<p>महाराष्ट्र</p>", "<p>ओडिशा</p>"],
                    solution_en: "<p>72. (a) <strong>Gujarat </strong>has been recognised as the &lsquo;petro capital&rsquo; of India, due to the presence of the Reliance Industry Limited&rsquo;s Jamnagar refinery and ONGC Petro Additions Limited (OPaL)&rsquo;s state-of-the-art petrochemical complex at Dahej in Bharuch district. RIL - Jamnagar refinery complex is the largest and most complex single-site refinery in the world.</p>",
                    solution_hi: "<p>72. (a) <strong>गुजरात</strong> को भारत की \'पेट्रो राजधानी\' के रूप में मान्यता दी गई है, क्योंकि यहाँ रिलायंस इंडस्ट्री लिमिटेड की जामनगर रिफाइनरी और भरूच जिले के दाहेज में ONGC पेट्रो एडिशन लिमिटेड (OPaL) का अत्याधुनिक पेट्रोकेमिकल कॉम्प्लेक्स मौजूद है। RIL-जामनगर रिफाइनरी कॉम्प्लेक्स दुनिया की सबसे बड़ी और सबसे जटिल सिंगल-साइट रिफाइनरी है।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "73",
                    section: "33",
                    question_en: "<p>73. Which figure is the correct representation of the movement of food in phloem?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1738229062030.png\" alt=\"rId24\" width=\"274\" height=\"104\"></p>",
                    question_hi: "<p>73. फ्लोएम में भोजन की गति का सही प्रतिनिधित्व कौन-सा चित्र करता है? <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1738229062030.png\" alt=\"rId24\" width=\"274\" height=\"104\"></p>",
                    options_en: ["<p>C</p>", "<p>B</p>", 
                                "<p>D</p>", "<p>A</p>"],
                    options_hi: ["<p>C</p>", "<p>B</p>",
                                "<p>D</p>", "<p>A</p>"],
                    solution_en: "<p>73.(a)<strong> Phloem </strong>is a living vascular tissue that is found in vascular plants and is responsible for transporting food prepared in the leaves (during photosynthesis) to all the parts of the plant. This whole process of transportation is known as phloem translocation.</p>",
                    solution_hi: "<p>73.(a) <strong>फ्लोएम</strong> एक जीवित संवहनी ऊतक है जो संवहनी पौधों में पाया जाता है और पत्तियों में तैयार भोजन (प्रकाश संश्लेषण के दौरान) को पौधे के सभी भागों तक पहुंचाने के लिए जिम्मेदार होता है। परिवहन की इस पूरी प्रक्रिया को phloem translocation के रूप में जाना जाता है।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "74",
                    section: "33",
                    question_en: "<p>74. The population of a town is 2,24,375. If it increases at the rate of 4% per annum, what will be its population 2 years hence?</p>",
                    question_hi: "<p>74. एक कस्बे की जनसंख्या 2,24,375 है। यदि यह 4% प्रति वर्ष की दर से बढ़ती है, तो 2 वर्ष बाद इसकी जनसंख्या कितनी होगी?</p>",
                    options_en: ["<p>2,42,684</p>", "<p>2,40,468</p>", 
                                "<p>2,36,864</p>", "<p>2,32,846</p>"],
                    options_hi: ["<p>2,42,684</p>", "<p>2,40,468</p>",
                                "<p>2,36,864</p>", "<p>2,32,846</p>"],
                    solution_en: "<p>74.(a) The population of town after 2<br>years = 224375 &times; <math display=\"inline\"><mfrac><mrow><mn>104</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> &times; <math display=\"inline\"><mfrac><mrow><mn>104</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 242684</p>",
                    solution_hi: "<p>74.(a) 2 वर्ष बाद शहर की जनसंख्या <br>= 224375 &times; <math display=\"inline\"><mfrac><mrow><mn>104</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> &times; <math display=\"inline\"><mfrac><mrow><mn>104</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 242684</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "75",
                    section: "33",
                    question_en: "<p>75. Muneer walked from his school, 85 m west, 45 m north, 25 m east and then, 45 m south. He stopped at this point to issue books from the library. How far and in which direction is Muneer&rsquo;s school from the library?</p>",
                    question_hi: "<p>75. मुनीर अपने विद्यालय से 85 मीटर पश्चिम, 45 मीटर उत्तर, 25 मीटर पूर्व और फिर 45 मीटर दक्षिण में चला। पुस्तकालय से पुस्तकें जारी करने के लिए वह यहीं रुक गया। मुनीर का विद्यालय पुस्तकालय से कितनी दूर और किस दिशा में है?</p>",
                    options_en: ["<p>60 m, east</p>", "<p>60 m, south</p>", 
                                "<p>60 m, West</p>", "<p>55 m, West</p>"],
                    options_hi: ["<p>60 मीटर, पूर्व</p>", "<p>60 मीटर, दक्षिण</p>",
                                "<p>60 मीटर, पश्चिम</p>", "<p>55 मीटर, पश्चिम</p>"],
                    solution_en: "<p>75.(a) From the given information we can draw the following diagram :<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1738229062244.png\" alt=\"rId25\" width=\"206\" height=\"120\"><br>Muneer\'s school is 60 m to the East direction from the Library.</p>",
                    solution_hi: "<p>75.(a) दी गई जानकारी से हम निम्नलिखित आरेख बना सकते हैं:<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1738229062430.png\" alt=\"rId26\" width=\"202\" height=\"124\"><br>मुनीर का विद्यालय पुस्तकालय से पूर्व दिशा में 60 मीटर दूर है।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "76",
                    section: "33",
                    question_en: "<p>76. Which state has recently (in September &lsquo;24) decided to celebrate Praja Palana Day on September 17?</p>",
                    question_hi: "<p>76. हाल ही में (सितंबर 2024 में) किस राज्य ने 17 सितंबर को प्रजा पालन दिवस मनाने का फैसला किया है?</p>",
                    options_en: ["<p>Kerala<strong id=\"docs-internal-guid-4b264393-7fff-ac8f-a4e8-dc03f00f7887\"><br></strong></p>", "<p>Karnataka<strong id=\"docs-internal-guid-a5a18a3d-7fff-c5b6-fd80-8babd6144dfd\"><br></strong></p>", 
                                "<p>Assam<strong id=\"docs-internal-guid-4e7940d8-7fff-fa78-1a72-b9d1127d5c70\"><br></strong></p>", "<p>Telangana</p>"],
                    options_hi: ["<p>केरल</p>", "<p>कर्नाटक</p>",
                                "<p>असम</p>", "<p>तेलंगाना</p>"],
                    solution_en: "<p>76. (d) <strong>Telangana.</strong> September 17 is the day on which Hyderabad merged with the Indian Union in 1948, following a police action under Operation Polo. Previously, the Government of India (GoI) had decided to celebrate September 17 as Hyderabad Liberation Day.</p>",
                    solution_hi: "<p>76. (d) <strong>तेलंगाना। </strong>17 सितंबर वह दिन है जिस दिन 1948 में ऑपरेशन पोलो के तहत पुलिस कार्रवाई के बाद हैदराबाद का भारतीय संघ में विलय हुआ था। इससे पहले, भारत सरकार (GoI) ने 17 सितंबर को हैदराबाद मुक्ति दिवस के रूप में मनाने का फैसला किया था।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "77",
                    section: "33",
                    question_en: "<p>77. Tetanus is a serious disease of the nervous system caused by a toxin-producing:</p>",
                    question_hi: "<p>77. टेटनस तंत्रिका तंत्र की एक गंभीर बीमारी है जो किस विष-उत्पादक के कारण होती है?</p>",
                    options_en: ["<p>Protist</p>", "<p>Fungus</p>", 
                                "<p>Parasite</p>", "<p>Bacterium</p>"],
                    options_hi: ["<p>प्रजीव (Protist)</p>", "<p>कवकों (Fungus)</p>",
                                "<p>परजीवी (parasite)</p>", "<p>जीवाणुओं (Bacterium)</p>"],
                    solution_en: "<p>77.(d) <strong>Bacterium.</strong> Tetanus is an infection caused by a bacterium called Clostridium tetani. The disease causes muscle contractions, particularly of your jaw and neck muscles. Tetanus is commonly known as lockjaw. Diseases caused by Bacteria - cholera, tuberculosis, anthrax (Bacillus anthracis), Pseudomonas infection.</p>",
                    solution_hi: "<p>77.(d) <strong>जीवाणु।</strong> टिटनेस एक संक्रमण है जो क्लोस्ट्रीडियम टेटानी नामक जीवाणु के कारण होता है। यह रोग विशेषकर आपके जबड़े और गर्दन की मांसपेशियों में संकुचन का कारण बनता है । टिटनेस को आमतौर पर लॉकजॉ के नाम से जाना जाता है। बैक्टीरिया से होने वाले रोग - हैजा, तपेदिक, एंथ्रेक्स (बैसिलस एन्थ्रेसीस जीवाणु), स्यूडोमोनास संक्रमण।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "78",
                    section: "33",
                    question_en: "<p>78. A man purchased 20 dozen mangoes for Rs. 1,000 . Out of these, 40 mangoes were rotten and could not be sold. At what rate per dozen should he sell the remaining mangoes to make a profit of 30%?</p>",
                    question_hi: "<p>78. एक आदमी ने 20 दर्जन आम 1,000 रुपये में खरीदे। इनमें से 40 आम सड़े हुए थे और बेचे नहीं जा सकते थे। 30% का लाभ अर्जित करने के लिए उसे शेष आमों को प्रति दर्जन किस दर पर बेचना चाहिए?</p>",
                    options_en: ["<p>Rs. 80</p>", "<p>Rs. 78</p>", 
                                "<p>Rs. 70</p>", "<p>Rs. 72</p>"],
                    options_hi: ["<p>Rs. 80</p>", "<p>Rs. 78</p>",
                                "<p>Rs. 70</p>", "<p>Rs. 72</p>"],
                    solution_en: "<p>78.(b) Rotten = 40 mangoes <br>Good mangoes = 20 &times; 12 - 40&nbsp;= 200 mangoes<br>Total SP = 1000 + 30% of 1000 <br>= 1300 Rs.<br>SP of 1 dozen mango = <math display=\"inline\"><mfrac><mrow><mn>1300</mn></mrow><mrow><mn>200</mn></mrow></mfrac></math>&times; 12 = 78</p>",
                    solution_hi: "<p>78.(b) सड़े हुए = 40 आम<br>अच्छे आम = 20 &times; 12 - 40 = 200 आम<br>कुल विक्रय मूल्य = 1000 + 30% of 1000 <br>= 1300 रुपये।<br>1 दर्जन आम का विक्रय मूल्य = <math display=\"inline\"><mfrac><mrow><mn>1300</mn></mrow><mrow><mn>200</mn></mrow></mfrac></math> &times; 12 = 78</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "79",
                    section: "33",
                    question_en: "<p>79. If 2 is added to each odd digit of the number 34135278, and 1 is subtracted from each even digit, then what will be the sum of the third digits from the left and the third from the right?</p>",
                    question_hi: "<p>79. यदि संख्या 34135278 के प्रत्येक विषम अंक में 2 जोड़ा जाता है, और प्रत्येक सम अंक से 1 घटाया जाता है, तो बाएं से तीसरे और दाएं से तीसरे अंकों का योग क्या होगा?</p>",
                    options_en: ["<p>7</p>", "<p>1</p>", 
                                "<p>3</p>", "<p>4</p>"],
                    options_hi: ["<p>7</p>", "<p>1</p>",
                                "<p>3</p>", "<p>4</p>"],
                    solution_en: "<p>79.(d) <strong>Given number</strong> = 34135278<br>After adding 2 to each odd digit and subtracting 1 from each even digit,<br>the resultant number = 53357197<br>The sum of third digit from the left and third digit from the right = 3 + 1 = 4</p>",
                    solution_hi: "<p>79.(d) <strong>दिया गया नंबर </strong>= 34135278<br>प्रत्येक विषम अंक में 2 जोड़ने और प्रत्येक सम अंक में से 1 घटाने के बाद<br>परिणामी संख्या = 53357197<br>बाएं से तीसरे अंक और दाएं से तीसरे अंक का योग = 3 + 1 = 4</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "80",
                    section: "33",
                    question_en: "<p>80. What was India&rsquo;s rank in the 2024 World Press Freedom Index published by Reporters Without Borders (RSF)?</p>",
                    question_hi: "<p>80. रिपोर्टर्स विदाउट बॉर्डर्स (आरएसएफ) द्वारा प्रकाशित 2024 विश्व प्रेस स्वतंत्रता सूचकांक में भारत की रैंक क्या थी?</p>",
                    options_en: ["<p>93rd</p>", "<p>113th<strong id=\"docs-internal-guid-4bbeab1d-7fff-0dcc-20ee-a816542168b1\"><br></strong></p>", 
                                "<p>159th<strong id=\"docs-internal-guid-361a9e1c-7fff-3e9c-e6a4-0b81ae4de3e3\"><br></strong></p>", "<p>49th</p>"],
                    options_hi: ["<p>93वां</p>", "<p>113वां</p>",
                                "<p>159वां</p>", "<p>49वां</p>"],
                    solution_en: "<p>80. (c) <strong>159th.</strong> Norway tops the 2024 index for the 8th consecutive year followed by Denmark (2nd) and Sweden (3rd). The last 3 places are occupied by: Eritrea (180th); Syria (179th), and Afghanistan (178th). The index ranks 180 countries on the ability of journalists to work and report freely and independently by covering five categories viz. Political Context, Legal Framework, Economic Context, Social Context and Security.</p>",
                    solution_hi: "<p>80. (c) <strong>159वां।</strong> नॉर्वे लगातार 8वें वर्ष 2024 सूचकांक में शीर्ष पर है, उसके बाद डेनमार्क (दूसरा) और स्वीडन (तीसरा) हैं। अंतिम 3 स्थानों पर हैं: इरिट्रिया (180वां); सीरिया (179वां), और अफगानिस्तान (178वां)। सूचकांक पत्रकारों की स्वतंत्र रूप से काम करने और रिपोर्ट करने की क्षमता के आधार पर 180 देशों को पांच श्रेणियों को कवर करके रैंक करता है। राजनीतिक संदर्भ, कानूनी ढांचा, आर्थिक संदर्भ, सामाजिक संदर्भ और सुरक्षा।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "81",
                    section: "33",
                    question_en: "<p>81. Which of the following DOES NOT constitute a part of pubertal development?</p>",
                    question_hi: "<p>81. निम्नलिखित में से कौन सा यौवन विकास का हिस्सा नहीं है?</p>",
                    options_en: ["<p>Obesity</p>", "<p>Getting pimples</p>", 
                                "<p>Menstruation</p>", "<p>Voice change</p>"],
                    options_hi: ["<p>मोटापा</p>", "<p>मुहांसे होना</p>",
                                "<p>मासिक धर्म</p>", "<p>आवाज परिवर्तन</p>"],
                    solution_en: "<p>81.(a) <strong>Obesity</strong> is a complex disease involving an excessive amount of body fat. Growth of nails also does not constitute a part of pubertal development. Puberty - Process of physical changes (happens between ages 10 and 14 for girls and ages 12 and 16 for boys) through which a child\'s body matures into an adult body capable of sexual reproduction. It is initiated by hormonal signals from the brain to the gonads - the ovaries in a female, the testes in a male, associated with emotional and hormonal changes as well as physical changes such as breast development in females (thelarche), genital changes in males, voice changes, an increase in height, and the onset of menstruation (menarche).</p>",
                    solution_hi: "<p>81.(a) <strong>मोटापा</strong> (Obesity) एक जटिल बीमारी है जिसमें शरीर में अत्यधिक मात्रा में वसा शामिल होती है। नाखूनों की वृद्धि भी यौवन विकास का हिस्सा नहीं है। यौवन (puberty) - शारीरिक परिवर्तनों की प्रक्रिया है (आमतौर पर लड़कियों के लिए 10 से 14 वर्ष और लड़कों के लिए 12 से 16 वर्ष के बीच होती है) जिसके माध्यम से एक बच्चे का शरीर यौन प्रजनन के लिए सक्षम वयस्क शरीर में परिपक्व होता है। यह मस्तिष्क से जननांग तक हार्मोनल संकेतों द्वारा शुरू किया जाता है - एक महिला में अंडाशय, एक पुरुष में वृषण; भावनात्मक और हार्मोनल परिवर्तनों के साथ-साथ शारीरिक परिवर्तन जैसे कि महिलाओं में स्तन विकास (thelarche), पुरुषों में जननांग परिवर्तन , आवाज में बदलाव, ऊंचाई में वृद्धि, और मासिक धर्म की शुरुआत (menarche)।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "82",
                    section: "33",
                    question_en: "<p>82. Two successive discounts of 15% and X% on the sale of an item is equivalent to a single discount of 23.5%. What is the value of X% ?</p>",
                    question_hi: "<p>82. किसी वस्तु की बिक्री पर 15% और X% की दो क्रमिक छूट 23.5% की एकल छूट के बराबर है। X% का मूल्य ज्ञात कीजिए।</p>",
                    options_en: ["<p>11%</p>", "<p>12%</p>", 
                                "<p>10%</p>", "<p>9%</p>"],
                    options_hi: ["<p>11%</p>", "<p>12%</p>",
                                "<p>10%</p>", "<p>9%</p>"],
                    solution_en: "<p>82.(c) <br>Let the Marked price = 100 unit <br>According to the question,<br>100 &times; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>100</mn><mo>-</mo><mn>15</mn></mrow><mn>100</mn></mfrac></math> &times; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>100</mn><mo>-</mo><mi>x</mi></mrow><mn>100</mn></mfrac></math><br>&rArr; 100 &times; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>100</mn><mo>-</mo><mn>23</mn><mo>.</mo><mn>5</mn></mrow><mn>100</mn></mfrac></math><br>&rArr; 85 &times; (100 - x) = 7650 <br>&rArr; 8500 - 85x = 7650 <br>&rArr; 85x = 850<br>Therefore, required percentage (x) = 10%</p>",
                    solution_hi: "<p>82.(c) माना अंकित कीमत = 100 यूनिट<br>प्रश्न के अनुसार,<br>100 &times; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>100</mn><mo>-</mo><mn>15</mn></mrow><mn>100</mn></mfrac></math> &times; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>100</mn><mo>-</mo><mi>x</mi></mrow><mn>100</mn></mfrac></math><br>&rArr; 100 &times; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>100</mn><mo>-</mo><mn>23</mn><mo>.</mo><mn>5</mn></mrow><mn>100</mn></mfrac></math><br>&rArr; 85 &times; (100 - x) = 7650 <br>&rArr; 8500 - 85x = 7650 <br>&rArr; 85x = 850<br>इसलिए, आवश्यक प्रतिशत (x) = 10%</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "83",
                    section: "33",
                    question_en: "<p>83. If L denotes &divide;, M denotes &times;, P denotes + and Q denotes - , then which of the following statements is true out of the options given below?</p>",
                    question_hi: "<p>83. यदि L, &divide; को निरूपित करता है; M, &times; निरूपित करता है, P, + निरूपित करता है, और Q, - को निरूपित करता है, तो नीचे दिए गए विकल्पों में से कौन सा समीकरण सही होगा?</p>",
                    options_en: ["<p>17 Q 22 M 18 P 13 L 7 = 473</p>", "<p>13 M 64 P 24 L 18 Q 6 = 36</p>", 
                                "<p>12 Q 13 L 15 M 19 P 23 = 96</p>", "<p>11 L 34 M 17 P 8 Q 3 = 42/4</p>"],
                    options_hi: ["<p>17 Q 22 M 18 P 13 L 7 = 473</p>", "<p>13 M 64 P 24 L 18 Q 6 = 36</p>",
                                "<p>12 Q 13 L 15 M 19 P 23 = 96</p>", "<p>11 L 34 M 17 P 8 Q 3 = 42/4</p>"],
                    solution_en: "<p>83.(d) The letters of all the alternatives are replaced by mathematical symbols respectively, then the statement of option D is true<br>11 L 34 M 17 P 8 Q 3 = <math display=\"inline\"><mfrac><mrow><mn>42</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> <br>11 &divide; 34 &times; 17 + 8 - 3 = <math display=\"inline\"><mfrac><mrow><mn>42</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math><br>&rArr; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>11</mn><mn>34</mn></mfrac></math> &times; 17 + 8 - 3 = <math display=\"inline\"><mfrac><mrow><mn>42</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math><br>&rArr; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>11</mn><mn>2</mn></mfrac></math> + 8 - 3 = <math display=\"inline\"><mfrac><mrow><mn>42</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math><br>&rArr; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>27</mn><mn>2</mn></mfrac></math> - 3 = <math display=\"inline\"><mfrac><mrow><mn>42</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math><br>&rArr; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>21</mn><mn>2</mn></mfrac></math> = <math display=\"inline\"><mfrac><mrow><mn>42</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math><br>Multiplying the numerator and denominator of <math display=\"inline\"><mfrac><mrow><mn>21</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> by 2<br><math display=\"inline\"><mfrac><mrow><mn>42</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> = <math display=\"inline\"><mfrac><mrow><mn>42</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> (satisfied)</p>",
                    solution_hi: "<p>83.(d) सभी विकल्पों के अक्षरों को क्रमशः गणितीय प्रतीकों से बदल दिया जाता है, तो विकल्प D का कथन सत्य है|<br>11 L 34 M 17 P 8 Q 3 = <math display=\"inline\"><mfrac><mrow><mn>42</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> <br>11 &divide; 34 &times; 17 + 8 - 3 = <math display=\"inline\"><mfrac><mrow><mn>42</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math><br>&rArr; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>11</mn><mn>34</mn></mfrac></math> &times; 17 + 8 - 3 = <math display=\"inline\"><mfrac><mrow><mn>42</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math><br>&rArr; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>11</mn><mn>2</mn></mfrac></math> + 8 - 3 = <math display=\"inline\"><mfrac><mrow><mn>42</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math><br>&rArr; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>27</mn><mn>2</mn></mfrac></math> - 3 = <math display=\"inline\"><mfrac><mrow><mn>42</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math><br>&rArr; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>21</mn><mn>2</mn></mfrac></math> = <math display=\"inline\"><mfrac><mrow><mn>42</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math><br><math display=\"inline\"><mfrac><mrow><mn>21</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> के अंश और हर को 2 से गुणा करने पर<br>&rArr; <math display=\"inline\"><mfrac><mrow><mn>42</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> = <math display=\"inline\"><mfrac><mrow><mn>42</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math>(संतुष्ट)</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "84",
                    section: "33",
                    question_en: "<p>84. &lsquo;Double Circulation\' CANNOT be observed in ______ .</p>",
                    question_hi: "<p>84. ____ में \'दोहरा परिसंचरण\' नहीं होता है।</p>",
                    options_en: ["<p>eagle</p>", "<p>snake</p>", 
                                "<p>fish</p>", "<p>frog</p>"],
                    options_hi: ["<p>गरुड़ो</p>", "<p>सर्पों</p>",
                                "<p>मछलियों</p>", "<p>मेंढक</p>"],
                    solution_en: "<p>84.(d) <strong>Frog.</strong> Amphibians and reptiles have an incomplete double circulation. Double circulation is the sort of circulation where the blood goes to the heart twice. Some animals have complete double circulation: Fish, Alligator, Frogs, lungfish, Prawns, Egyptian plover (Crocodile birds), mammals.</p>",
                    solution_hi: "<p>84.(d) <strong>मेंढक</strong> । उभयचरों और सरीसृपों में अधूरा दोहरा परिसंचरण होता है। दोहरा परिसंचरण ऐसा परिसंचरण है जहां रक्त दो बार हृदय में जाता है। कुछ जानवरों में पूर्ण दोहरा परिसंचरण होता है, जैसे मछली, मगरमच्छ, मेंढक, फुफ्फुस मछली (lungfish), झींगे, मिस्री प्लोवर (मगरमच्छ पक्षी), स्तनधारी ।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "85",
                    section: "33",
                    question_en: "<p>85. The average weight of 11 students is 50 kg. When the teacher\'s weight of 70 kg is added to the total, then the new average weight will be:</p>",
                    question_hi: "<p>85. 11 विद्यार्थियों का औसत वजन 50 kg है। यदि शिक्षक के 70 kg वजन को कुल वजन में जोड़ा जाता है, तो नया औसत ज्ञात कीजिए।</p>",
                    options_en: ["<p>50.66</p>", "<p>53.66</p>", 
                                "<p>51.36</p>", "<p>51.66</p>"],
                    options_hi: ["<p>50.66</p>", "<p>53.66</p>",
                                "<p>51.36</p>", "<p>51.66</p>"],
                    solution_en: "<p>85.(d)<br>Average weight of 11 students = 50 kg <br>Total sum of the weight of 11 students <br>= 11 &times; 50 = 550 kg<br>Now, new average after teacher&rsquo;s weight is added <br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>550</mn><mo>+</mo><mn>70</mn></mrow><mn>12</mn></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>620</mn><mn>12</mn></mfrac></mstyle></math> = 51.66</p>",
                    solution_hi: "<p>85.(d) 11 छात्रों का औसत वजन = 50 kg<br>11 छात्रों की वजन का कुल योग <br>= 11 &times; 50 = 550 kg<br>अब, शिक्षक का वजन जोड़ने के बाद नया औसत <br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>550</mn><mo>+</mo><mn>70</mn></mrow><mn>12</mn></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>620</mn><mn>12</mn></mfrac></mstyle></math> = 51.66</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "86",
                    section: "33",
                    question_en: "<p>86. Select the combination of numbers which, when arranged, will form a meaningful English word from the jumbled letters given below : <br>O H R E M T&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;1 2 3 4 5 6</p>",
                    question_hi: "<p>86. संख्याओं के उस संयोजन का चयन करें, जिसे व्यवस्थित करने पर, नीचे दिए गए अव्यवस्थित अक्षरों से एक अर्थपूर्ण अंग्रेजी शब्द बन जाएगा :<br>O H R E M T&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 1 2 3 4 5 6</p>",
                    options_en: ["<p>3, 4, 6, 1, 2, 5</p>", "<p>2, 4, 3, 6, 1, 5</p>", 
                                "<p>6, 1, 3, 5, 4, 2</p>", "<p>5, 1, 6, 2, 4, 3</p>"],
                    options_hi: ["<p>3, 4, 6, 1, 2, 5</p>", "<p>2, 4, 3, 6, 1, 5</p>",
                                "<p>6, 1, 3, 5, 4, 2</p>", "<p>5, 1, 6, 2, 4, 3</p>"],
                    solution_en: "<p>86.(d) OHREMT = MOTHER<br>Therefore, MOTHER = 5,1,6,2,4,3</p>",
                    solution_hi: "<p>86.(d) OHREMT = MOTHER<br>Therefore, MOTHER = 5,1,6,2,4,3</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "87",
                    section: "33",
                    question_en: "<p>87. Which is called the brain of any computer system ?</p>",
                    question_hi: "<p>87. किसी भी कंप्यूटर सिस्टम (computer system) का मस्तिष्क (brain ) किसे कहा जाता है ?</p>",
                    options_en: ["<p>Monitor</p>", "<p>ALU</p>", 
                                "<p>CPU</p>", "<p>UPS</p>"],
                    options_hi: ["<p>मॉनिटर (Monitor)</p>", "<p>ALU</p>",
                                "<p>CPU</p>", "<p>UPS</p>"],
                    solution_en: "<p>87.(c) <strong>CPU </strong>(Central Processing Unit) is the \"brain\" of the computer because it is responsible for executing instructions and performing calculations. The primary use of a monitor is to display images, text, video, and graphics information generated by the computer via a computer\'s video card. Arithmetic Logic Unit serves as a combinational digital circuit that performs arithmetic and bitwise operations on binary numbers. An uninterruptible power supply (UPS) is a device that allows a computer to keep running for at least a short time when incoming power is interrupted.</p>",
                    solution_hi: "<p>87.(c) <strong>CPU </strong>(Central Processing Unit) कंप्यूटर का \"मस्तिष्क\" है क्योंकि यह निर्देशों को निष्पादित करने और गणना करने के लिए जिम्मेदार है। मॉनिटर का प्राथमिक उपयोग कंप्यूटर द्वारा उत्पन्न छवियों, टेक्स्ट, वीडियो और ग्राफिक्स जानकारी को कंप्यूटर के वीडियो कार्ड के माध्यम से प्रदर्शित करना है। अंकगणितीय तर्क इकाई (Arithmetic Logic Unit) एक संयोजन डिजिटल सर्किट के रूप में कार्य करती है जो बाइनरी संख्याओं पर अंकगणितीय और बिटवाइज़ संचालन करती है। एक Uninterruptible Power Supply (UPS) एक ऐसा उपकरण है जो कंप्यूटर को आने वाली बिजली बाधित होने पर कम से कम थोड़े समय के लिए चालू रखने की अनुमति देता है।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "88",
                    section: "33",
                    question_en: "<p>88. Find the arithmetic mean of 5, 15, 23, 26 and 29.</p>",
                    question_hi: "<p>88. 5, 15, 23, 26 और 29 का समांतर माध्य ज्ञात कीजिए ।</p>",
                    options_en: ["<p>20.6</p>", "<p>18.6</p>", 
                                "<p>19.6</p>", "<p>17.6</p>"],
                    options_hi: ["<p>20.6</p>", "<p>18.6</p>",
                                "<p>19.6</p>", "<p>17.6</p>"],
                    solution_en: "<p>88.(c) Arithmetic Mean <br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mo>+</mo><mn>15</mn><mo>+</mo><mn>23</mn><mo>+</mo><mn>26</mn><mo>+</mo><mn>29</mn></mrow><mn>5</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>98</mn><mn>5</mn></mfrac></math> = 19.6</p>",
                    solution_hi: "<p>88.(c) समांतरमाध्य<br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mo>+</mo><mn>15</mn><mo>+</mo><mn>23</mn><mo>+</mo><mn>26</mn><mo>+</mo><mn>29</mn></mrow><mn>5</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>98</mn><mn>5</mn></mfrac></math> = 19.6</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "89",
                    section: "33",
                    question_en: "<p>89. All 29 students of a class are standing in a straight row facing north. Suresh is 24th from the right end, while Bhaskar is 16th from the left end. How many students stand between Suresh and Bhaskar?</p>",
                    question_hi: "<p>89. एक कक्षा के सभी 29 विद्यार्थी उत्तर की ओर मुख करके एक सीधी पंक्ति में खड़े हैं। सुरेश दाएं सिरे से 24वें स्थान पर खड़ा है, जबकि भास्कर बाएं सिरे से 16वें स्थान पर खड़ा है। सुरेश और भास्कर के बीच कितने विद्यार्थी खड़े हैं?</p>",
                    options_en: ["<p>9</p>", "<p>6</p>", 
                                "<p>12</p>", "<p>14</p>"],
                    options_hi: ["<p>9</p>", "<p>6</p>",
                                "<p>12</p>", "<p>14</p>"],
                    solution_en: "<p>89.(a) Total students = 29<br>Suresh&rsquo;s position from the right end = 24th<br>Bhaskar&rsquo;s position from the left end = 16th<br>Number of students between Suresh and Bhaskar <br>= 24 - 16 + 1 = 9</p>",
                    solution_hi: "<p>89.(a) कुल छात्र = 29<br>दायें छोर से सुरेश का स्थान = 24वां<br>भास्कर का बाएं छोर से स्थान = 16वां<br>सुरेश और भास्कर के बीच छात्रों की संख्या<br>= 24 - 16 + 1 = 9</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "90",
                    section: "33",
                    question_en: "<p>90. In the following figure, deer occupy _________.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1738229062658.png\" alt=\"rId27\" width=\"187\" height=\"131\"></p>",
                    question_hi: "<p>90. निम्न चित्र में हिरण किस स्तर (Level) पर आएंगे ?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1738229062888.png\" alt=\"rId28\" width=\"181\" height=\"127\"></p>",
                    options_en: ["<p>Level 3</p>", "<p>Level 2</p>", 
                                "<p>Level 1</p>", "<p>Level 4</p>"],
                    options_hi: ["<p>स्तर 3</p>", "<p>स्तर 2</p>",
                                "<p>स्तर 1</p>", "<p>स्तर 4</p>"],
                    solution_en: "<p>90.(b) <strong>Level 2</strong> (Primary Consumers / Second trophic level / Herbivores) - Zooplankton, Grasshopper, cow. Level 1 (Producer / First trophic level / Plants) - Phytoplankton, grass, Tree. Level 3 (Secondary Consumers / third trophic level / Carnivore) - Birds, fishes, wolves. Level 4 (Tertiary Consumers / Fourth trophic level / Top carnivores) - Man, Lion. Trophic level is the relative position of an entity in the food chain.</p>",
                    solution_hi: "<p>90.(b) <strong>स्तर 2</strong> (प्राथमिक उपभोक्ता/द्वितीय पोषी स्तर/शाकाहारी) - प्राणिप्लवक, टिड्डा, गाय। स्तर 1 (उत्पादक/पहला पोषी स्तर/पौधे) - पादपप्लवक, घास, वृक्ष। स्तर 3 (द्वितीयक उपभोक्ता/तृतीय पोषण स्तर/मांसाहारी) - पक्षी, मछलियाँ, भेड़िये। स्तर 4 (तृतीयक उपभोक्ता / चौथा पोषण स्तर / शीर्ष मांसाहारी) - मनुष्य, शेर। पोषी स्तर, खाद्य श्रृंखला में अस्तित्व की सापेक्ष स्थिति है।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "91",
                    section: "33",
                    question_en: "<p>91. The difference between the compound interest (when interest is compounded annually) and the simple interest if ₹10,000 is deposited at 5% rate of interest per annum for 2 years is :</p>",
                    question_hi: "<p>91. ₹10,000 की राशि पर 5% वार्षिक ब्याज दर से दो वर्ष में प्राप्त होने वाले चक्रवृद्धि ब्याज (ब्याज की गणना चक्रवृद्धि आधार पर होने पर) और साधारण ब्याज के बीच का अंतर ज्ञात कीजिए ।</p>",
                    options_en: ["<p>₹25</p>", "<p>₹15</p>", 
                                "<p>₹50</p>", "<p>₹35</p>"],
                    options_hi: ["<p>₹25</p>", "<p>₹15</p>",
                                "<p>₹50</p>", "<p>₹35</p>"],
                    solution_en: "<p>91.(a) <strong>Given, <br></strong>Principal = ₹10,000 , <br>rate = 5 % <br>and time 2 years<br>(C.I - S.I) for 2 years <br>&rarr;&nbsp;P &times; (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>R</mi><mn>100</mn></mfrac></math>)<sup>2</sup> <br>= 10000 &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>25</mn><mn>10000</mn></mfrac></math> <br>= ₹25</p>",
                    solution_hi: "<p>91.(a) <strong>दिया गया है,</strong> <br>मूलधन (P)&nbsp;= ₹10,000, <br>दर (R) = 5% <br>और समय (T) = 2 वर्ष<br>(चक्रवृद्धि ब्याज -&nbsp; साधारण ब्याज)<sub>2साल&nbsp;</sub><br>&rarr;&nbsp;P &times; (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>R</mi><mn>100</mn></mfrac></math>)<sup>2</sup> <br>= 10000 &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>25</mn><mn>10000</mn></mfrac></math> <br>= ₹25</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "92",
                    section: "33",
                    question_en: "<p>92. Four friends Ramniwas, Ramesh, Ramsingh and Raman received their PhD degrees in consecutive months of the same calendar year. Ramniwas received his degree exactly one month prior to Ramsingh. Raman received his degree exactly one month after Ramesh. Ramniwas received his degree in September and Raman did not obtain his degree before Ramniwas. In which month did Ramesh receive his degree?</p>",
                    question_hi: "<p>92. चार मित्र - रामनिवास, रमेश, रामसिंह और रमन ने एक ही कैलेंडर वर्ष के लगातार महीनों में PhD की डिग्री प्राप्त की। रामनिवास ने अपनी डिग्री रामसिंह से ठीक एक महीने पहले प्राप्त की। रमेश के ठीक एक महीने बाद रमन ने अपनी डिग्री प्राप्त की । रामनिवास ने सितंबर में अपनी डिग्री प्राप्त की और रमन ने अपनी डिग्री रामनिवास से पहले प्राप्त नहीं की । रमेश ने अपनी डिग्री किस महीने में प्राप्त की ?</p>",
                    options_en: ["<p>August</p>", "<p>October</p>", 
                                "<p>November</p>", "<p>December</p>"],
                    options_hi: ["<p>अगस्त</p>", "<p>अक्टूबर</p>",
                                "<p>नवंबर</p>", "<p>दिसंबर</p>"],
                    solution_en: "<p>92.(c) Since Raman did not get his degree before Ramnivas, the correct sequence of people who got degree would be <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1738229062993.png\" alt=\"rId29\" width=\"203\" height=\"103\"></p>",
                    solution_hi: "<p>92.(c) चूंकि रमन ने रामनिवास से पहले अपनी डिग्री प्राप्त नहीं की थी इसलिए डिग्री प्राप्त करने करने वाले लोगों का सही क्रम होगा<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1738229063085.png\" alt=\"rId30\" width=\"197\" height=\"123\"></p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "93",
                    section: "33",
                    question_en: "<p>93. The Rio Declaration on Environment and Development was approved by the United Nation in the year ____.</p>",
                    question_hi: "<p>93. पर्यावरण और विकास पर रियो घोषणा को संयुक्त राष्ट्र द्वारा वर्ष ____ में अनुमोदित किया गया था।</p>",
                    options_en: ["<p>1992</p>", "<p>2012</p>", 
                                "<p>2018</p>", "<p>2002</p>"],
                    options_hi: ["<p>1992</p>", "<p>2012</p>",
                                "<p>2018</p>", "<p>2002</p>"],
                    solution_en: "<p>93.(a) <strong>1992.</strong> The United Nations Conference on Environment and Development (UNCED): It is called the Earth summit, was held in Rio de Janeiro (Brazil) in June 1992. Important Outcomes: Rio Declaration, Agenda 21, United Nations Framework Convention on Climate Change, United Nations Convention to Combat Desertification. The first world conference on the environment - Stockholm (Sweden) in 1972, Outcome - Creation of United Nations Environment Programme (UNEP).</p>",
                    solution_hi: "<p>93.(a)<strong> 1992 </strong>। पर्यावरण और विकास पर संयुक्त राष्ट्र सम्मेलन (UNCED): इसे पृथ्वी शिखर सम्मेलन कहा जाता है, जून 1992 में रियो डी जनेरियो (ब्राजील) में आयोजित किया गया था। महत्वपूर्ण परिणाम: रियो घोषणा, एजेंडा 21, जलवायु परिवर्तन पर संयुक्त राष्ट्र फ्रेमवर्क कन्वेंशन , मरुस्थलीकरण से निपटने के लिए संयुक्त राष्ट्र कन्वेंशन। पर्यावरण पर प्रथम विश्व सम्मेलन - स्टॉकहोम (स्वीडन) 1972 में, परिणाम - संयुक्त राष्ट्र पर्यावरण कार्यक्रम (UNEP) का गठन ।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "94",
                    section: "33",
                    question_en: "<p>94. The area (in square units) of the quadrilateral ABCD, formed by the vertices A (0 , - 2), B (2 , 1) , C (0 , 4), and D (- 2 , 1) is</p>",
                    question_hi: "<p>94. शीर्ष A (0 , - 2), B (2 , 1), C (0 , 4), और D (- 2 , 1) द्वारा गठित चतुर्भुज ABCD का क्षेत्रफल (वर्ग इकाइयों में) ज्ञात कीजिये।</p>",
                    options_en: ["<p>15</p>", "<p>14</p>", 
                                "<p>13</p>", "<p>12</p>"],
                    options_hi: ["<p>15</p>", "<p>14</p>",
                                "<p>13</p>", "<p>12</p>"],
                    solution_en: "<p>94.(d) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1738229063224.png\" alt=\"rId31\" width=\"177\" height=\"115\"><br>A(0 , - 2) , B(2 , 1) , C(0 , 4) , D(- 2 , 1)<br>Area of triangle ABD <br>= <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> [x<sub>1</sub>(y<sub>2</sub> - y<sub>3</sub>)&nbsp;+ x<sub>2</sub>(y<sub>3</sub> - y<sub>1</sub>)&nbsp; + x<sub>3</sub>(y<sub>1</sub> - y<sub>2</sub>)]<br>= <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math>[0 + 6 + 6]<br>= <math display=\"inline\"><mfrac><mrow><mn>1</mn><mi>&#160;</mi></mrow><mrow><mn>2</mn></mrow></mfrac></math> &times; 12 = 6 sq . units <br>Area of triangle BCD <br>= <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> [x<sub>1</sub>(y<sub>2</sub> - y<sub>3</sub>)&nbsp;+ x<sub>2</sub>(y<sub>3</sub> - y<sub>1</sub>)&nbsp; + x<sub>3</sub>(y<sub>1</sub> - y<sub>2</sub>)]<br>= <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math>[6 + 0 + 6]<br>= <math display=\"inline\"><mfrac><mrow><mn>1</mn><mi>&#160;</mi></mrow><mrow><mn>2</mn></mrow></mfrac></math> &times; 12 = 6 sq . units<br>Area of quadrilateral ABCD <br>= Area of triangle ABD + Area of triangle BCD<br>= 6 + 6 = 12 sq .units</p>",
                    solution_hi: "<p>94.(d) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1738229063224.png\" alt=\"rId31\" width=\"179\" height=\"116\"><br>A(0 , - 2) , B(2 , 1) , C(0 , 4) , D(- 2 , 1)<br>&Delta;ABD का क्षेत्रफल&nbsp;<br>= <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> [x<sub>1</sub>(y<sub>2</sub> - y<sub>3</sub>)&nbsp;+ x<sub>2</sub>(y<sub>3</sub> - y<sub>1</sub>)&nbsp; + x<sub>3</sub>(y<sub>1</sub> - y<sub>2</sub>)]<br>= <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math>[0 + 6 + 6]<br>= <math display=\"inline\"><mfrac><mrow><mn>1</mn><mi>&#160;</mi></mrow><mrow><mn>2</mn></mrow></mfrac></math> &times; 12 = 6 वर्ग इकाई&nbsp;<br>त्रिभुज BCD का क्षेत्रफल <br>= <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> [x<sub>1</sub>(y<sub>2</sub> - y<sub>3</sub>)&nbsp;+ x<sub>2</sub>(y<sub>3</sub> - y<sub>1</sub>)&nbsp; + x<sub>3</sub>(y<sub>1</sub> - y<sub>2</sub>)]<br>= <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math>[6 + 0 + 6]<br>= <math display=\"inline\"><mfrac><mrow><mn>1</mn><mi>&#160;</mi></mrow><mrow><mn>2</mn></mrow></mfrac></math> &times; 12 = 6 वर्ग इकाई&nbsp;<br>चतुर्भुज ABCD का क्षेत्रफल <br>= त्रिभुज ABD का क्षेत्रफल + त्रिभुज BCD का क्षेत्रफल<br>= 6 + 6 = 12 वर्ग इकाई</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "95",
                    section: "33",
                    question_en: "<p>95. Each of W, X, Y, Z, A, B and C has a wedding to attend on a different day of a week starting from Monday to Sunday of the same week. C has to attend wedding immediately after A. Y has to attend a wedding on one of the days before B and W. Only Z has to attend a wedding before A. X has to attend a wedding on Friday. B does not have to attend a wedding on Sunday. On which day of the week does Y have to attend a wedding?</p>",
                    question_hi: "<p>95. W, X, Y, Z, A , B और C में से प्रत्येक सोमवार से शुरू होकर उसी सप्ताह के अलग-अलग दिनों में शादियों में शामिल हुए । C को A के ठीक बाद शादी में शामिल होना है। Y को B और W से पहले के किसी दिन शादी में शामिल होना है। केवल Z को A से पहले शादी में शामिल होना है। X को शुक्रवार को शादी में शामिल होना है। B को रविवार को शादी में शामिल नहीं होना है। सप्ताह के किस दिन Y को शादी में शामिल होना है?</p>",
                    options_en: ["<p>Saturday</p>", "<p>Sunday</p>", 
                                "<p>Wednesday</p>", "<p>Thursday</p>"],
                    options_hi: ["<p>शनिवार</p>", "<p>रविवार</p>",
                                "<p>बुधवार</p>", "<p>गुरुवार</p>"],
                    solution_en: "<p>95.(d) We can arrange all 7 people in the following way.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1738229063363.png\" alt=\"rId32\" width=\"170\" height=\"158\"><br>Hence we can see in the above table Y attend a wedding on Thursday.</p>",
                    solution_hi: "<p>95.(d) हम निम्नलिखित तरीके से सभी 7 लोगों को व्यवस्थित कर सकते हैं।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1738229063506.png\" alt=\"rId33\" width=\"139\" height=\"163\"></p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "96",
                    section: "33",
                    question_en: "<p>96. Where was the first experimental satellite telecommunication earth station set up in 1967 in India?</p>",
                    question_hi: "<p>96. 1967 में भारत में पहला प्रायोगिक उपग्रह दूरसंचार पृथ्वी स्टेशन कहाँ स्थापित किया गय था?</p>",
                    options_en: ["<p>Aurangabad</p>", "<p>Ahmednagar</p>", 
                                "<p>Allahabad</p>", "<p>Ahmedabad</p>"],
                    options_hi: ["<p>औरंगाबाद</p>", "<p>अहमदनगर</p>",
                                "<p>इलाहाबाद</p>", "<p>अहमदाबाद</p>"],
                    solution_en: "<p>96.(d) <strong>Ahmedabad. </strong>Indian space research organisation (ISRO): Headquarter - Bangalore. Established - 15 August 1969. Important space centres - Vikram Sarabhai Space Centre (Thiruvananthapuram), ISRO Propulsion Complex (Mahendragiri, Tamil Nadu), National Remote Sensing Centre (Hyderabad), Satish Dhawan Space Centre (SDSC) - Sriharikota (Andhra Pradesh).</p>",
                    solution_hi: "<p>96.(d) <strong>अहमदाबाद</strong> । भारतीय अंतरिक्ष अनुसंधान संगठन (ISRO): मुख्यालय - बैंगलोर, स्थापना - 15 अगस्त 1969। महत्वपूर्ण अंतरिक्ष केंद्र - विक्रम साराभाई अंतरिक्ष केंद्र (तिरुवनंतपुरम), ISRO प्रोपल्शन कॉम्प्लेक्स (महेंद्रगिरि, तमिलनाडु), राष्ट्रीय रिमोट सेंसिंग सेंटर (हैदराबाद), सतीश धवन अंतरिक्ष केंद्र (SDSC) - श्रीहरिकोटा (आंध्र प्रदेश)।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "97",
                    section: "33",
                    question_en: "<p>97. Three pipes A, B and C together can fill a tank in 8 hours. Three pipes were opened for 2 hours, after that C was closed. Later A and B fill the remaining part in 9 hours. The number of hours taken by C alone to fill the tank is:</p>",
                    question_hi: "<p>97. तीन पाइप A, B और C मिलकर एक टंकी को 8 घंटे में भर सकते हैं। तीन पाइपों को 2 घंटे के लिए खोला गया, उसके बाद C को बंद कर दिया गया। बाद में A और B शेष भाग को 9 घंटे में भरते हैं। पाइप C अकेले टंकी को कितने घंटे में भरेगा ?</p>",
                    options_en: ["<p>20</p>", "<p>12</p>", 
                                "<p>24</p>", "<p>13</p>"],
                    options_hi: ["<p>20</p>", "<p>12</p>",
                                "<p>24</p>", "<p>13</p>"],
                    solution_en: "<p>97.(c) For remaining part of the tank &rarr;&nbsp;A , B and C can complete in 8 - 2 = 6 hours And A and B completed the remaining part in 6 hour<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1738229063700.png\" alt=\"rId34\" width=\"195\" height=\"130\"><br>Efficiency of C = 3 - 2 = 1<br>Total capacity of tank = 8 &times; 3 (because A , B and C can fill the tank in 8 hour) <br>So, required time = <math display=\"inline\"><mfrac><mrow><mn>24</mn></mrow><mrow><mn>1</mn></mrow></mfrac></math> = 24 hours</p>",
                    solution_hi: "<p>97.(c)<br>टैंक के शेष भाग के लिए &rarr; A , B और C द्वारा <br>8 - 2 = 6 घंटे में पूरा कर सकते हैं और A और B ने शेष भाग को 6 घंटे में पूरा किया<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1738229063700.png\" alt=\"rId34\" width=\"201\" height=\"134\"><br>C की क्षमता = 3 - 2 = 1<br>टैंक की कुल क्षमता = 8 &times; 3 (क्योंकि A , B और C टैंक को 8 घंटे में भर सकते हैं)<br>अत: अभीष्ट समय = <math display=\"inline\"><mfrac><mrow><mn>24</mn></mrow><mrow><mn>1</mn></mrow></mfrac></math> = 24 घंटे</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "98",
                    section: "33",
                    question_en: "<p>98. Study the following arrangement carefully to answer these questions ?</p>\n<p>(Left) T Y A N E C M K E W A F H E Q A P M N B E D H E K U W S D A N M A W E (Right)&nbsp;<br>How many such consonants are there in the series which are immediately preceded by a vowel and also immediately followed by a vowel?</p>",
                    question_hi: "<p>98. इन प्रश्नों का उत्तर देने के लिए निम्नलिखित व्यवस्था का ध्यानपूर्वक अध्ययन करें?</p>\n<p>(बाएं) T Y A N E C M K E W A F H E Q A P M N B E D H E K U W S D A N M A W E (दाएं) श्रंखला में ऐसे कितने व्यंजन हैं जिनके ठीक पहले एक स्वर है और ठीक बाद में एक स्वर है?</p>",
                    options_en: ["<p>Five</p>", "<p>Four</p>", 
                                "<p>Three</p>", "<p>Six</p>"],
                    options_hi: ["<p>पांच</p>", "<p>चार</p>",
                                "<p>तीन</p>", "<p>छह</p>"],
                    solution_en: "<p>98.(a) <strong>Given series:</strong> <br>(Left) T Y A N E C M K E W A F H E Q A P M N B E D H E K U W S D A N M A W E (Right)<br>There are 5 such consonants which are immediately preceded by a vowel and also immediately followed by a vowel <br>i.e. (A N E), (E W A), (E Q A), (E K U) and (A W E).</p>",
                    solution_hi: "<p>98.(a) <strong>दी गई श्रृंखला:</strong><br>(बाएं)T Y A N E C M K E W A F H E Q A P M N B E D H E K U W S D A N M A W E(दाएं). ऐसे 5 व्यंजन हैं जिनके ठीक पहले एक स्वर है और ठीक बाद में भीएक स्वर है, जोकि है <br>(A N E), (E W A), (E Q A), (E K U) और (A W E)</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "99",
                    section: "33",
                    question_en: "<p>99. Find the value of<br>777<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> + 777<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>5</mn></mfrac></math> + 777<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>5</mn></mfrac></math> + 777<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>5</mn></mfrac></math>.</p>",
                    question_hi: "<p>99. 777<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> + 777<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>5</mn></mfrac></math> + 777<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>5</mn></mfrac></math> + 777<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>5</mn></mfrac></math> का मान ज्ञात कीजिये ?</p>",
                    options_en: ["<p>3110</p>", "<p>3018</p>", 
                                "<p>3000</p>", "<p>3108</p>"],
                    options_hi: ["<p>3110</p>", "<p>3018</p>",
                                "<p>3000</p>", "<p>3108</p>"],
                    solution_en: "<p>99.(a) <br>777<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> + 777<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>5</mn></mfrac></math> + 777<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>5</mn></mfrac></math> + 777<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>5</mn></mfrac></math>.<br>= 777 &times; 4 + (<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>5</mn></mfrac></math> + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>5</mn></mfrac></math> = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>5</mn></mfrac></math>)<br>= 3108 + 2 <br>= 3110</p>",
                    solution_hi: "<p>99.(a)<br>777<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> + 777<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>5</mn></mfrac></math> + 777<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>5</mn></mfrac></math> + 777<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>5</mn></mfrac></math>.<br>= 777 &times; 4 + (<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>5</mn></mfrac></math> + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>5</mn></mfrac></math> = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>5</mn></mfrac></math>)<br>= 3108 + 2 <br>= 3110</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "100",
                    section: "misc",
                    question_en: "<p>100. Refer to the following letter, number, symbol series and answer the question.</p>\n<p>(Left) K L % Y &amp; 4 E 2 8 * 3 M &amp; 7 S W # 8 2 H * L (Right)<br>If all the numbers are dropped from the series, which of the following will be eighth from the right?</p>",
                    question_hi: "<p>100. निम्नलिखित अक्षर, संख्या, प्रतीक श्रृंखला देखिए और प्रश्न का उत्तर दीजिए।</p>\n<p>(बाएँ) K L % Y &amp; 4 E 2 8 * 3 M &amp; 7 S W # 8 2 H * L (दाएँ).<br>यदि श्रृंखला से सभी संख्याओं को हटा दिया जाता है, तो निम्नलिखित में से कौन सा दाएं से आठवां होगा?</p>",
                    options_en: ["<p>M</p>", "<p>&amp;</p>", 
                                "<p>*</p>", "<p>E</p>"],
                    options_hi: ["<p>M</p>", "<p>&amp;</p>",
                                "<p>*</p>", "<p>E</p>"],
                    solution_en: "<p>100.(a) We count from right leaving all numbers.<br>(Left) K L % Y &amp; 4 E 2 8 * 3 M &amp; 7 S W # 8 2 H * L (Right)<br>(Left) K L % Y &amp; E * M &amp; S W # H * L (Right) <br>Clearly, M is at the 8th position.</p>",
                    solution_hi: "<p>100.(a) हम सभी संख्याओं को छोड़कर दाईं ओर से गिनते हैं।<br>(बाएँ) K L % Y &amp; 4 E 2 8 * 3 M &amp; 7 S W # 8 2 H * L (दाएँ)<br>(बाएँ) K L % Y &amp; E * M &amp; S W # H * L (दाएँ) <br>स्पष्ट रूप से, &lsquo;M&rsquo; 8वें स्थान पर है।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>