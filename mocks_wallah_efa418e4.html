<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">18:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 18</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">18</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 18 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 0,
                end: 16
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 17,
                end: 17
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "17",
                    question_en: "<p>1. A person sells an article at 10% below its cost price. Had he sold it for ₹332 more, he would have made a profit of 20%. What is the original selling price (in ₹) of the article?</p>",
                    question_hi: "<p>1. एक व्यक्ति किसी वस्तु को इसके क्रय मूल्य से 10% कम पर बेचता है । यदि उसने इसे 332 रुपये अधिक में बेचा होता, तो उसे 20% का लाभ हुआ होता । इस वस्तु का आरंभिक विक्रय मूल्य ( रुपये में ) कितना था ?</p>",
                    options_en: ["<p>896</p>", "<p>1328</p>", 
                                "<p>1028</p>", "<p>996</p>"],
                    options_hi: ["<p>896</p>", "<p>1328</p>",
                                "<p>1028</p>", "<p>996</p>"],
                    solution_en: "<p>1.(d) Let the original cost price of article is 100 units<br>At 10% loss on CP, SP is 90 units<br>If article is sold at ₹332 more, there is 20% profit, ie. SP = 120 units<br>120-90 = 30 units = ₹ 332<br>1 unit = <math display=\"inline\"><mfrac><mrow><mn>332</mn></mrow><mrow><mn>30</mn></mrow></mfrac></math><br>Original SP = 90 units <br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>332</mn><mn>30</mn></mfrac><mo>&#215;</mo><mn>90</mn><mo>=</mo><mo>&#8377;</mo><mn>996</mn></math></p>",
                    solution_hi: "<p>1.(d)<br>माना वस्तु का मूल लागत मूल्य 100 इकाई है<br>क्रय मूल्य पर 10% की हानि पर, विक्रय मूल्य 90 इकाई है<br>यदि वस्तु ₹332 अधिक पर बेची जाती है, तो 20% लाभ होता है, अर्थात। विक्रय मूल्य = 120 इकाई<br>120-90 = 30 इकाई = ₹ 332<br>1 इकाई = <math display=\"inline\"><mfrac><mrow><mn>332</mn></mrow><mrow><mn>30</mn></mrow></mfrac></math><br>वास्तविक विक्रय मूल्य = 90 इकाई <br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>332</mn><mn>30</mn></mfrac><mo>&#215;</mo><mn>90</mn><mo>=</mo><mo>&#8377;</mo><mn>996</mn></math></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "17",
                    question_en: "<p>2. Anu fixes the selling price of an article at 25% above its cost of production. If the cost of production goes up by 20% and she raises the selling price by 10%, then her percentage profit is (correct to one decimal place):</p>",
                    question_hi: "<p>2. अनु किसी वस्तु का विक्रय मूल्य इसके उत्पादन लागत से 25% अधिक निर्धारित करती है । यदि उत्पादन की लागत 20% बढ़ जाती है एवं वह विक्रय मूल्य 10% बढ़ा देती है, तो उसका प्रतिशत लाभ होगा :( दशमलव के एक स्थान तक)</p>",
                    options_en: ["<p>16.4%</p>", "<p>14.6%</p>", 
                                "<p>13.8%</p>", "<p>15.2%</p>"],
                    options_hi: ["<p>16.4%</p>", "<p>14.6%</p>",
                                "<p>13.8%</p>", "<p>15.2%</p>"],
                    solution_en: "<p>2.(b) Let cost price of article be ₹100<br>Then SP after 25% increment = ₹125<br>If CP raise by 20% = ₹120<br>And SP raise by 10% = ₹ 137.50<br>% profit =&nbsp;<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>137</mn><mo>.</mo><mn>5</mn><mo>-</mo><mn>120</mn></mrow><mn>120</mn></mfrac><mo>&#215;</mo><mn>100</mn></math> = 14.58%</p>",
                    solution_hi: "<p>2.(b) <br>माना वस्तु का क्रय मूल्य ₹100 है<br>फिर 25% वेतन वृद्धि के बाद विक्रय मूल्य = ₹125<br>यदि क्रय मूल्य में 20% की वृद्धि होती है = ₹120<br>और विक्रय मूल्य 10% बढ़ा = ₹ 137.50<br>लाभ % = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>137</mn><mo>.</mo><mn>5</mn><mo>-</mo><mn>120</mn></mrow><mn>120</mn></mfrac><mo>&#215;</mo><mn>100</mn></math>&nbsp;= 14.58%</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "17",
                    question_en: "<p>3. A dealer sold 6 sewing machines for ₹63,000 with a profit of 5%. For how much should he sell 8 machines if he intends to earn 15% profit?</p>",
                    question_hi: "<p>3. एक विक्रेता ने 6 सिलाई मशीन 63000 रुपये में बेची तथा उसे 5% का लाभ हुआ । यदि वह 15% लाभ कमाना चाहता है, तो उसे 8 मशीनें किस कीमत पर बेचनी चाहिए ?</p>",
                    options_en: ["<p>₹88,200</p>", "<p>₹92,000</p>", 
                                "<p>₹69,300</p>", "<p>₹92,400</p>"],
                    options_hi: ["<p>₹88,200</p>", "<p>₹92,000</p>",
                                "<p>₹69,300</p>", "<p>₹92,400</p>"],
                    solution_en: "<p>3.(b) Let CP of machine be x<br>At 5% profit, on 6 sewing machines<br>6<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#215;</mo><mo>(</mo><mfrac><mn>105</mn><mn>100</mn></mfrac></math>of x) = ₹63000<br>X = ₹ 10000<br>Cost price for 8 machines<br>= 8<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#215;</mo></math>₹10000 = ₹80,000<br>To earn 15% profit,<br>Selling price of 8 machines <br>= <math display=\"inline\"><mfrac><mrow><mn>115</mn></mrow><mrow><mn>100</mn></mrow></mfrac><mo>&#215;</mo></math>80,000 = ₹ 92,000</p>",
                    solution_hi: "<p>3.(b) माना मशीन का क्रय मूल्य x है<br>5% लाभ पर, 6 सिलाई मशीनों = 6<math display=\"inline\"><mo>&#215;</mo><mo>(</mo><mfrac><mrow><mn>105</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math>of x) = ₹63000<br>X = ₹ 10000<br>8 मशीनों के लिए क्रय मूल्य = 8<math display=\"inline\"><mo>&#215;</mo></math>₹10000 = ₹80,000<br>15% लाभ कमाने के लिए, 8 मशीनों का विक्रय मूल्य = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>115</mn><mn>100</mn></mfrac><mo>&#215;</mo></math>80,000 = ₹ 92,000</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "17",
                    question_en: "<p>4. A person buys 5 tables and 9 chairs for ₹15,400. He sells the tables at 10% profit and chairs at 20% profit. If his total profit on selling all the tables and chairs is ₹2,080, what is the cost price of 3 chairs?</p>",
                    question_hi: "<p>4. एक व्यक्ति 5 मेज और 9 कुर्सियों को 15,400 रुपये में ख़रीदा । उसने मेजों को 10% लाभ पर तथा कुर्सियों को 20% लाभ पर बेच दिया । यदि सभी मेज एवं कुर्सियों को बेचकर उसका कुल लाभ 2,080 रुपये का था, तो 3 कुर्सियों का क्रय मूल्य कितना है ?</p>",
                    options_en: ["<p>₹1,890</p>", "<p>₹1,740</p>", 
                                "<p>₹1,800</p>", "<p>₹1,860</p>"],
                    options_hi: ["<p>₹1,890</p>", "<p>₹1,740</p>",
                                "<p>₹1,800</p>", "<p>₹1,860</p>"],
                    solution_en: "<p>4.(c) Let cost price of 1 table be &lsquo;t&rsquo; and cost price of 1 chair be &lsquo;c&rsquo;<br>5t + 9c = ₹ 15,400<br>As 5 tables are sold at 10% profit and 9 chairs at 20% profit, total profit is ₹ 2080<br>If all the articles would have been sold at 10% profit, actual profit would have been ₹ 1,540. But the extra ₹ 540 profit is due to 10% more profit on 9 chairs. <br>For 9 chairs, 1% of profit = ₹ 54<br>For 1 chairs, 1% of profit = ₹6<br>Thus, cost price of 1 chair = ₹ 600<br>Cost price of 3 chairs = ₹ 1,800</p>",
                    solution_hi: "<p>4.(c) माना 1 टेबल का क्रय मूल्य \'t\' है और 1 कुर्सी का क्रय मूल्य \'c\' है।<br>5t + 9c = ₹. 15,400<br>चूँकि 5 मेजें 10% लाभ पर और 9 कुर्सियाँ 20% लाभ पर बेची जाती हैं, कुल लाभ ₹ 2080 है<br>यदि सभी वस्तुएँ 10% लाभ पर बेची जातीं, तो वास्तविक लाभ ₹ 1,540 होता। लेकिन ₹540 का अतिरिक्त लाभ 9 कुर्सियों पर 10% अधिक लाभ के कारण है।<br>9 कुर्सियों के लिए 1% लाभ = ₹ 54<br>1 कुर्सियों के लिए, लाभ का 1% = ₹6<br>अत: 1 कुर्सी का क्रय मूल्य = ₹ 600<br>3 कुर्सियों का क्रय मूल्य = ₹ 1,800</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "17",
                    question_en: "<p>5. One-third of the goods are sold at a 15% profit, 25% of the goods are sold at a 20% profit and rest at a 20% loss. If the total profit of ₹138.50 is earned on the whole transaction, then the value (in ₹) of the goods is:</p>",
                    question_hi: "<p>5. एक-तिहाई वस्तुएँ 15% लाभ पर बेची जाती हैं, 25% वस्तुएँ 20% लाभ पर तथा शेष वस्तुएँ 20% हानि पर बेची जाती हैं । यदि पूरे लेनदेन पर कुल 138.50 रुपये का लाभ होता है, तो वस्तुओं का मूल्य ( रुपये में ) है :</p>",
                    options_en: ["<p>₹8,587</p>", "<p>₹8,030</p>", 
                                "<p>₹7,756</p>", "<p>₹8,310</p>"],
                    options_hi: ["<p>₹8,587</p>", "<p>₹8,030</p>",
                                "<p>₹7,756</p>", "<p>₹8,310</p>"],
                    solution_en: "<p>5.(d) <br>Let there be 12 goods of 100 unit each<br>Total CP = 1200 unit<br>One third of goods= 4, which are sold at 15% profit i.e. for 115 unit each<br>SP<sub>1</sub> = 460unit<br>25% of goods = 3 which are sold at 20% profit i.e for 120 unit each<br>SP<sub>2</sub> = 360unit<br>Remaining goods = 5 are sold at 20% loss i.e. for 80unit each<br>SP<sub>3</sub> = 400unit<br>Total Selling price = 1220units<br>Total profit = 20unit<br>As per condition given in question:<br>20 unit = ₹ 138.50<br>1 unit = ₹ 6.925<br>1200 units = ₹ 8,310</p>",
                    solution_hi: "<p>5.(d) मान लीजिए कि 12 वस्तुएँ प्रत्येक 100 इकाई के सामान हैं<br>कुल क्रय मूल्य = 1200 यूनिट<br>एक-तिहाई वस्तुएँ = 4, जो 15% लाभ पर बेचा जाता है अर्थात प्रत्येक 115 इकाई के लिए<br>SP1 = 460 इकाई (SP<sub>1</sub> = पहला विक्रय मूल्य)<br>वस्तु का 25% = 3 जो 20% लाभ पर बेचा जाता है अर्थात प्रत्येक 120 यूनिट के लिए<br>SP<sub>2</sub> = 360 इकाई<br>शेष माल = 5 को 20% हानि पर बेचा जाता है अर्थात प्रत्येक 80इकाई के लिए<br>SP<sub>3</sub> = 400 इकाई<br>कुल विक्रय मूल्य = 1220 इकाइयाँ<br>कुल लाभ = 20 यूनिट<br>प्रश्न में दी गई शर्त के अनुसार:<br>20 इकाई = ₹ 138.50<br>1 इकाई = ₹ 6.925<br>1200 यूनिट = ₹ 8,310</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "17",
                    question_en: "<p>6. A sells an article to B at a loss of 20%. B sells it to C at a profit of 12.5% and C sells it to D at a loss of 8%. If D buys it for ₹248.40, then what is the difference between the loss incurred by A and C?</p>",
                    question_hi: "<p>6. A ने एक वस्तु B को 20% की हानि पर बेची । B ने उस वस्तु को C को 12.5% लाभ पर बेच दिया तथा C ने वह वस्तु D को 8% की हानि पर बेची । यदि D ने उसे 248.40 रुपये में ख़रीदा, तो A और C को हुई हानि के बीच क्या अंतर है ?</p>",
                    options_en: ["<p>₹36.80</p>", "<p>₹38.40</p>", 
                                "<p>₹42.60</p>", "<p>₹39.20</p>"],
                    options_hi: ["<p>₹36.80</p>", "<p>₹38.40</p>",
                                "<p>₹42.60</p>", "<p>₹39.20</p>"],
                    solution_en: "<p>6.(b) Let cost price of A = 100 units<br>According to question:<br>At 20% loss, CP for B = 80 units <br>At 12.5% profit, CP for C = 90 unit<br>At 8% loss, CP for D = 82.80 unit<br>82.80 units = ₹ 248.40<br>1 units = ₹ 3<br>Now, loss incurred by A = 20 units; and loss incurred by C = 7.20 units<br>Required difference = 12.80 units = ₹38.40</p>",
                    solution_hi: "<p>6.(b) माना A का क्रय मूल्य = 100 इकाई<br>प्रश्न के अनुसार:<br>20% हानि पर, B के लिए क्रय मूल्य = 80 इकाई<br>12.5% ​​लाभ पर, C के लिए क्रय मूल्य = 90 इकाई<br>8% हानि पर, D के लिए क्रय मूल्य = 82.80 इकाई<br>82.80 यूनिट = ₹ 248.40<br>1 इकाई = ₹ 3<br>अब, A द्वारा हुई हानि = 20 इकाई; और C द्वारा हुई हानि = 7.20 इकाई<br>अभीष्ट अंतर = 12.80 इकाई = ₹38.40</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "17",
                    question_en: "<p>7. Sudha sold an article to Renu for ₹576 at a loss of 20%. Renu spent a sum of ₹224 on its transportation and sold it to Raghu at a price which would have given Sudha a profit of 24%. The percentage of gain for Renu is:</p>",
                    question_hi: "<p>7. सुधा ने 20% हानि पर रेणु को एक वस्तु 576 रुपये में बेच दी । रेणु ने इसके परिवहन पर 224 रुपये खर्च किये तथा रघु को उस कीमत पर बेच दिया जिस कीमत पर सुधा को 24% का लाभ होता । रेणु के लिए प्रतिशत लाभ है :</p>",
                    options_en: ["<p>10.5%</p>", "<p>11.6%</p>", 
                                "<p>12.9%</p>", "<p>13.2%</p>"],
                    options_hi: ["<p>10.5%</p>", "<p>11.6%</p>",
                                "<p>12.9%</p>", "<p>13.2%</p>"],
                    solution_en: "<p>7.(b) <br>C.P for Sudha = ₹ 576 <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#215;</mo><mfrac><mn>100</mn><mn>80</mn></mfrac></math> = ₹ 720<br>C.P. for Renu = ₹ (576 + 224) = ₹ 800<br>C.P for Raghu = ₹ <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>124</mn><mn>100</mn></mfrac><mo>&#215;</mo><mn>720</mn></math> = ₹ 892.80<br>Renu profit % <br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>892</mn><mo>.</mo><mn>80</mn><mo>-</mo><mn>800</mn></mrow><mn>800</mn></mfrac><mo>&#215;</mo><mn>100</mn></math> = 11.6%</p>",
                    solution_hi: "<p>7.(b) <br>सुधा के लिए क्रय मूल्य = ₹ 576 <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#215;</mo><mfrac><mn>100</mn><mn>80</mn></mfrac></math> = ₹ 720<br>रेणु के लिए क्रय मूल्य = ₹ (576 + 224) = ₹ 800<br>रघु के लिए क्रय मूल्य = ₹ <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>124</mn><mn>100</mn></mfrac><mo>&#215;</mo><mn>720</mn></math> = ₹ 892.80<br>रेणु का लाभ %= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>892</mn><mo>.</mo><mn>80</mn><mo>-</mo><mn>800</mn></mrow><mn>800</mn></mfrac><mo>&#215;</mo><mn>100</mn></math>&nbsp;= 11.6%</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "17",
                    question_en: "<p>8. Reena sold 48 articles for ₹2,160 and suffered a loss of 10%. How many articles should she sell for ₹2,016 to earn a profit of 12%?</p>",
                    question_hi: "<p>8. रीना ने 48 वस्तुओं को 2,160 रुपये में बेचा तथा उसे 10% की हानि हुई । 12% का लाभ कमाने के लिए उसे 2016 रुपये में कितनी वस्तुओं को बेचना चाहिए ?</p>",
                    options_en: ["<p>36</p>", "<p>40</p>", 
                                "<p>32</p>", "<p>28</p>"],
                    options_hi: ["<p>36</p>", "<p>40</p>",
                                "<p>32</p>", "<p>28</p>"],
                    solution_en: "<p>8.(a) Cost price of 48 articles <br>= <math display=\"inline\"><mfrac><mrow><mn>2160</mn></mrow><mrow><mn>90</mn></mrow></mfrac><mo>&#215;</mo><mn>100</mn></math> = ₹ 2400<br>CP of 1 article = ₹ 50<br>Number of articles in ₹ 2016 = <math display=\"inline\"><mfrac><mrow><mn>2016</mn></mrow><mrow><mn>50</mn></mrow></mfrac></math><br>Profit% = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>CostQuantitative</mi><mo>-</mo><mi>SellingQuantitative</mi></mrow><mi>sellingQuantitative</mi></mfrac><mo>&#215;</mo><mn>100</mn></math><br><math display=\"inline\"><mo>&#8658;</mo><mi>&#160;</mi><mfrac><mrow><mn>12</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mstyle displaystyle=\"true\"><mfrac><mn>2016</mn><mn>50</mn></mfrac></mstyle><mo>-</mo><mi mathvariant=\"normal\">S</mi></mrow><mi mathvariant=\"normal\">S</mi></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo><mi>&#160;</mi><mfrac><mrow><mn>6</mn></mrow><mrow><mn>50</mn></mrow></mfrac><mi>&#160;</mi></math>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mstyle displaystyle=\"true\"><mfrac><mn>2016</mn><mn>50</mn></mfrac></mstyle><mo>-</mo><mi mathvariant=\"normal\">S</mi></mrow><mi mathvariant=\"normal\">S</mi></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math> S = 36 articles</p>",
                    solution_hi: "<p>8.(a) <br>48 वस्तुओं का क्रय मूल्य = <math display=\"inline\"><mfrac><mrow><mn>2160</mn></mrow><mrow><mn>90</mn></mrow></mfrac><mi>&#160;</mi><mo>&#215;</mo><mn>100</mn></math> = ₹ 2400<br>1 वस्तु का क्रय मूल्य = ₹ 50<br>&nbsp;₹ 2016 में वस्तुओं की संख्या = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2016</mn><mn>56</mn></mfrac></math><br>लाभ % =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2354;&#2366;&#2327;&#2340;</mi><mo>&#160;</mo><mi>&#2350;&#2366;&#2340;&#2381;&#2352;&#2366;&#2340;&#2381;&#2350;&#2325;</mi><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mi>&#2348;&#2367;&#2325;&#2381;&#2352;&#2368;</mi><mo>&#160;</mo><mi>&#2350;&#2366;&#2340;&#2381;&#2352;&#2366;&#2340;&#2381;&#2350;&#2325;</mi></mrow><mrow><mi>&#2348;&#2367;&#2325;&#2381;&#2352;&#2368;</mi><mo>&#160;</mo><mo>&#160;</mo><mi>&#2350;&#2366;&#2340;&#2381;&#2352;&#2366;&#2340;&#2381;&#2350;&#2325;</mi></mrow></mfrac></math> &times; 100<br><math display=\"inline\"><mo>&#8658;</mo><mi>&#160;</mi><mfrac><mrow><mn>12</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mstyle displaystyle=\"true\"><mfrac><mn>2016</mn><mn>50</mn></mfrac></mstyle><mo>-</mo><mi mathvariant=\"normal\">S</mi></mrow><mi mathvariant=\"normal\">S</mi></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo><mi>&#160;</mi><mfrac><mrow><mn>6</mn></mrow><mrow><mn>50</mn></mrow></mfrac><mi>&#160;</mi></math>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mstyle displaystyle=\"true\"><mfrac><mn>2016</mn><mn>50</mn></mfrac></mstyle><mo>-</mo><mi mathvariant=\"normal\">S</mi></mrow><mi mathvariant=\"normal\">S</mi></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math> S = 36 वस्तु</p>\n<p>&nbsp;</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "17",
                    question_en: "<p>9. Sudhir purchased a laptop for ₹42,000 and a scanner-cum-printer for ₹8,000. He sold the laptop for a 10% profit and scanner-cum-printer for a 5% profit. What is his profit percentage?</p>",
                    question_hi: "<p>9. सुधीर ने 42,000 रुपये में एक लैपटॉप तथा 8,000 रुपये में स्कैनर सह प्रिंटर ख़रीदा । उसने लैपटॉप को 10% लाभ पर तथा स्कैनर सह प्रिंटर को 5% लाभ पर बेच दिया । लाभ का प्रतिशत कितना है ?</p>",
                    options_en: ["<p>7<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math>%</p>", "<p>9<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math>%</p>", 
                                "<p>15%</p>", "<p>9<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math>%</p>"],
                    options_hi: ["<p>7<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math>%</p>", "<p>9<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math>%</p>",
                                "<p>15%</p>", "<p>9<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math>%</p>"],
                    solution_en: "<p>9.(d) CP of laptop = ₹ 42,000<br>CP of scanner - cum - printer = ₹ 8,000<br>Total CP = ₹ 50,000<br>SP of laptop = ₹ 46,200<br>SP of scanner - cum - printer = ₹ 8,400<br>Total SP = ₹ 54,600<br>Total Profit = ₹ 4,600<br>Profit % = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4600</mn><mn>50000</mn></mfrac><mo>&#215;</mo><mn>100</mn></math> = 9<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>5</mn></mfrac></math>%</p>",
                    solution_hi: "<p>9.(d) <br>लैपटॉप का क्रय मूल्य = ₹ 42,000<br>स्कैनर-सह-प्रिंटर का क्रय मूल्य = ₹ 8,000<br>कुल क्रय मूल्य = ₹ 50,000<br>लैपटॉप का विक्रय मूल्य = ₹ 46,200<br>स्कैनर-सह-प्रिंटर का SP = ₹ 8,400<br>कुल विक्रय मूल्य = ₹ 54,600<br>कुल लाभ = ₹ 4,600<br>लाभ % = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4600</mn><mn>50000</mn></mfrac><mo>&#215;</mo><mn>100</mn></math> = 9<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>5</mn></mfrac></math>%</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "17",
                    question_en: "<p>10. An article was sold at a gain of 18%. If it had been sold for ₹ 49 more, then the gain would have been 25%. The cost price of the article is:</p>",
                    question_hi: "<p>10. एक वस्तु 18% के लाभ पर बेची गयी । यदि उसे 49 रुपये अधिक में बेचा गया होता, तो लाभ 25% होता । इस वस्तु का क्रय मूल्य कितना है ?</p>",
                    options_en: ["<p>₹ 570</p>", "<p>₹ 890</p>", 
                                "<p>₹ 700</p>", "<p>₹ 650</p>"],
                    options_hi: ["<p>₹ 570</p>", "<p>₹ 890</p>",
                                "<p>₹ 700</p>", "<p>₹650</p>"],
                    solution_en: "<p>10.(c) Let CP of article = 100 %<br><math display=\"inline\"><mi>&#8377;</mi><mi>&#160;</mi><mn>49</mn></math>change occur due to 7% change in price<br>1% = <math display=\"inline\"><mi>&#8377;</mi></math> 7. 100% = ₹ 700</p>",
                    solution_hi: "<p>10.(c)<br>माना वस्तु का क्रय मूल्य = 100%<br>कीमत में 7% बदलाव के कारण ₹49 का परिवर्तन होता है<br>1% = ₹ 7 , 100% = ₹ 700</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "17",
                    question_en: "<p>11. A manufacturer sells cooking gas stoves to shopkeepers at 10% profit, and in turn they sell the cooking gas stoves to customers to earn 15% profit. If a customer gets a cooking gas stove for ₹ 7,590, then what is its manufacturing cost?</p>",
                    question_hi: "<p>11. एक विनिर्माता कुकिंग गैस स्टोव को दुकानदार को 10% लाभ पर बेचता है । तथा फिर दुकानदार इस कुकिंग गैस स्टोव को ग्राहकों को 15% लाभ पर बेच देते हैं । यदि एक ग्राहक इस कुकिंग गैस स्टोव को 7590 रुपये में खरीदता है, तो विनिर्माण की लागत कितनी है ?</p>",
                    options_en: ["<p>₹ 6,500</p>", "<p>₹ 6,000</p>", 
                                "<p>₹ 5,000</p>", "<p>₹ 5,090</p>"],
                    options_hi: ["<p>₹ 6,500</p>", "<p>₹ 6,000</p>",
                                "<p>₹ 5,000</p>", "<p>₹ 5,090</p>"],
                    solution_en: "<p>11.(b) Let the manufacturing cost = ₹ x<br><math display=\"inline\"><mo>&#8658;</mo></math> x &times;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>110</mn><mn>100</mn></mfrac></math>&times;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>115</mn><mn>100</mn></mfrac></math> = ₹ 7590<br><math display=\"inline\"><mo>&#8658;</mo></math> x = ₹ 6000</p>",
                    solution_hi: "<p>11.(b) माना विनिर्माण की लागत = ₹ x<br><math display=\"inline\"><mo>&#8658;</mo></math> x &times;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>110</mn><mn>100</mn></mfrac></math>&times;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>115</mn><mn>100</mn></mfrac></math> = ₹ 7590<br><math display=\"inline\"><mo>&#8658;</mo></math> x = ₹ 6000</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "17",
                    question_en: "<p>12. An article was sold at a gain of 16%. If it had been sold for ₹36 more, the gain would have been 20%. The cost price of the article is:</p>",
                    question_hi: "<p>12. एक वस्तु 16% के लाभ पर बेची गयी । यदि उसे 36 रुपये अधिक में बेचा गया होता, तो लाभ 20% का होता । इस वस्तु का क्रय मूल्य कितना है ?</p>",
                    options_en: ["<p>₹862</p>", "<p>₹720</p>", 
                                "<p>₹810</p>", "<p>₹900</p>"],
                    options_hi: ["<p>₹862</p>", "<p>₹720</p>",
                                "<p>₹810</p>", "<p>₹900</p>"],
                    solution_en: "<p>12.(d) Due to 4% change in profit%, there is a difference of ₹36.<br>1% of CP = ₹ 9<br>CP = ₹ 900</p>",
                    solution_hi: "<p>12.(d) <br>लाभ% में 4% परिवर्तन के कारण ₹36 का अंतर है।<br>क्रय मूल्य का 1% = ₹ 9 , क्रय मूल्य = ₹ 900</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "17",
                    question_en: "<p>13. After selling 18 table fans for Rs 11,664 a person makes a loss of 10%. How many fans should he sell for ₹ 17,424 to earn 10% profit?</p>",
                    question_hi: "<p>13. 18 टेबल पंखों को 11,664 रुपये में बेचने के बाद एक व्यक्ति को 10% की हानि होती है । 10% का लाभ कमाने के लिए उसे 17,424 रुपये में कितने पंखे बेचने चाहिए ?</p>",
                    options_en: ["<p>23</p>", "<p>18</p>", 
                                "<p>20</p>", "<p>22</p>"],
                    options_hi: ["<p>23</p>", "<p>18</p>",
                                "<p>20</p>", "<p>22</p>"],
                    solution_en: "<p>13.(d) CP of 18 tables&nbsp;= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>11664</mn><mn>90</mn></mfrac><mo>&#215;</mo><mn>100</mn><mo>=</mo><mi mathvariant=\"normal\">&#8377;</mi><mn>12960</mn></math><br>CP of 1 table = ₹720<br>Number of tables for ₹17,424 <br>= <math display=\"inline\"><mfrac><mrow><mn>17424</mn></mrow><mrow><mn>720</mn></mrow></mfrac></math> = 24.2<br>To earn 10% profit, number of table that can be sold for ₹17,424 &rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>10</mn><mn>100</mn></mfrac></math><br>= <math display=\"inline\"><mfrac><mrow><mn>24</mn><mo>.</mo><mn>2</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mi>S</mi></mrow><mrow><mi>S</mi></mrow></mfrac></math><br>S = 22 tables</p>",
                    solution_hi: "<p>13.(d) <br>18 टेबल का क्रय मूल्य&nbsp;= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>11664</mn><mn>90</mn></mfrac><mo>&#215;</mo><mn>100</mn><mo>=</mo><mi mathvariant=\"normal\">&#8377;</mi><mn>12960</mn></math><br>1 टेबल का क्रय मूल्य = ₹720<br>10% लाभ अर्जित करने के लिए 1 टेबल का विक्रय मूल्य =<br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>720</mn><mn>100</mn></mfrac></math> &times; 110 = 792<br>₹17,424 में टेबलों की संख्या = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>17424</mn><mn>792</mn></mfrac></math> = 22<br><br></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "17",
                    question_en: "<p>14. A man buys two watches &lsquo;A&rsquo; and &lsquo;B&rsquo; at a total cost of <math display=\"inline\"><mi>&#8377;</mi><mn>800</mn><mo>.</mo></math>He sells both watches at the same selling price, and earns a profit of 18% on watch &lsquo;A&rsquo; and incurs a loss of 22% on watch &lsquo;B&rsquo;. What are the cost prices of the two watches? (two places after decimal).</p>",
                    question_hi: "<p>14. एक व्यक्ति दो घड़ी &lsquo;A&rsquo; और &lsquo;B&rsquo; को कुल 800 रुपये की लागत में क्रय करता है । वह दोनों घड़ियों को समान विक्रय मूल्य पर बेच देता है तथा घड़ी A पर 18% का लाभ कमाता है जबकि घड़ी B पर उसे 22% की हानि होती है । दोनों घड़ियों का क्रय मूल्य कितना है ?</p>",
                    options_en: ["<p>A = ₹350.32 and B = ₹450.68</p>", "<p>A = ₹318.37 and B = ₹481.63</p>", 
                                "<p>A = ₹220 and B = ₹580</p>", "<p>A = ₹317 and B = ₹483</p>"],
                    options_hi: ["<p>A = ₹350.32 and B = ₹450.68</p>", "<p>A = ₹318.37 and B = ₹481.63</p>",
                                "<p>A = ₹220 and B = ₹580</p>", "<p>A = ₹317 and B = ₹483</p>"],
                    solution_en: "<p>14.(b) <br>Let the cost price of two watches be ₹ A and ₹ B respectively. <br>118% of A = 78% of B <br>Therefore, <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">A</mi><mi mathvariant=\"normal\">B</mi></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>78</mn><mn>118</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>39</mn><mn>59</mn></mfrac></math><br>A = 39 units and B = 59 units<br>A + B = ₹ 800<br>98 units = ₹ 800<br>1 units = ₹ 8.163<br>A = ₹ 39 &times; 8.163 = ₹ 318.37<br>B = ₹ 59 &times; 8.163 = ₹ 481.63</p>",
                    solution_hi: "<p>14.(b) <br>माना दो घड़ियों का क्रय मूल्य क्रमशः ₹A और ₹B है।<br>A का 118% = B का 78%<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">A</mi><mi mathvariant=\"normal\">B</mi></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>78</mn><mn>118</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>39</mn><mn>59</mn></mfrac></math><br>A = 39 इकाई और B = 59 इकाई<br>A + B = ₹800<br>98 इकाई = ₹ 800 , 1 इकाई = ₹ 8.163<br>A = ₹ 39 &times; 8.163 = ₹ 318.37<br>B = ₹ 59 &times; 8.163 = ₹ 481.63</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "17",
                    question_en: "<p>15. A man sold two gifts at ₹30 each. On one gift he gained 18% and on the other he lost 18%. What is his overall gain/loss (in ₹)?</p>",
                    question_hi: "<p>15. एक व्यक्ति ने दो उपहारों में से प्रत्येक को 30 रुपये मे बेचा । पहले उपहार पर उसे 18% का लाभ हुआ तथा दूसरे उपहार पर उसे 18% की हानि हुई । उसका कुल लाभ/हानि ( रुपये में ) क्या है ?</p>",
                    options_en: ["<p>Gain of ₹1.75</p>", "<p>Gain of ₹2.00</p>", 
                                "<p>Loss of ₹2.00</p>", "<p>Loss of ₹2.50</p>"],
                    options_hi: ["<p>लाभ ₹1.75</p>", "<p>लाभ ₹2.00</p>",
                                "<p>हानि ₹2.00</p>", "<p>हानि ₹2.50</p>"],
                    solution_en: "<p>15.(c) SP<sub>1</sub> = SP<sub>2</sub><br>Overall gain/loss% = 18 - 18 -<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">&#160;</mi><mfrac><mrow><mn>18</mn><mo>&#215;</mo><mn>18</mn></mrow><mn>100</mn></mfrac></math><br>= 3.24 loss<br>If total CP = 100, loss = 3.24 and SP <br>= 96.76<br>But total SP = 60, then, Loss <br>= <math display=\"inline\"><mfrac><mrow><mn>60</mn></mrow><mrow><mn>96</mn><mo>.</mo><mn>76</mn></mrow></mfrac><mo>&#215;</mo><mn>3</mn><mo>.</mo><mn>24</mn><mo>&#8776;</mo></math> ₹ 2 (approx.)</p>",
                    solution_hi: "<p>15.(c) विक्रय मूल्य<sub>1</sub> = विक्रय मूल्य<sub>2</sub><br>कुल लाभ/हानि% = 18 - 18 - <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">&#160;</mi><mfrac><mrow><mn>18</mn><mo>&#215;</mo><mn>18</mn></mrow><mn>100</mn></mfrac></math><br>= 3.24 हानि<br>यदि कुल क्रय मूल्य = 100, हानि = 3.24 और विक्रय मूल्य = 96.76<br>लेकिन कुल विक्रय मूल्य = 60, तो, हानि = <math display=\"inline\"><mfrac><mrow><mn>60</mn></mrow><mrow><mn>96</mn><mo>.</mo><mn>76</mn></mrow></mfrac></math> &times; 3.24&asymp; ₹ 2 (approx.)</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "17",
                    question_en: "<p>16. Ram makes a profit of 30% by selling an article. What would be the profit percent if it were calculated on the selling price instead of the cost price? (Correct to one decimal place.)</p>",
                    question_hi: "<p>16. राम किसी वस्तु को बेचकर 30% लाभ कमाता है । यदि लाभ की गणना क्रय मूल्य के बजाय विक्रय मूल्य पर की जाए, तो लाभ का प्रतिशत क्या होगा ? (दशमलव के एक स्थान तक)</p>",
                    options_en: ["<p>22.4%</p>", "<p>23.1%</p>", 
                                "<p>20.1%</p>", "<p>24.2%</p>"],
                    options_hi: ["<p>22.4%</p>", "<p>23.1%</p>",
                                "<p>20.1%</p>", "<p>24.2%</p>"],
                    solution_en: "<p>16.(b) Let CP = 10 units, then SP <br>= 13 units<br>profit% at SP<br>= <math display=\"inline\"><mfrac><mrow><mn>13</mn><mo>-</mo><mn>10</mn></mrow><mrow><mn>13</mn></mrow></mfrac><mo>&#215;</mo><mn>100</mn><mi>&#160;</mi><mo>=</mo><mi>&#160;</mi><mn>23</mn><mo>.</mo><mn>1</mn><mi>%</mi></math></p>",
                    solution_hi: "<p>16.(b) माना कि क्रय मूल्य = 10 इकाई, विक्रय मूल्य = 13 इकाई<br>विक्रय मूल्य पर लाभ% = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>13</mn><mi mathvariant=\"normal\">&#160;</mi><mo>-</mo><mi mathvariant=\"normal\">&#160;</mi><mn>10</mn></mrow><mn>13</mn></mfrac><mo>&#215;</mo><mn>100</mn><mo>=</mo><mn>23</mn><mo>.</mo><mn>1</mn><mi mathvariant=\"normal\">%</mi></math></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "17",
                    question_en: "<p>17. A shopkeeper buys two books for ₹300. He sells the first book at a profit of 20% and the second book at a loss of 10%. What is the selling price of the first book, if in the whole transaction there is no profit no loss?</p>",
                    question_hi: "<p>17. एक दुकानदार ने दो पुस्तकों को 300 रुपये में ख़रीदा । उसने पहली पुस्तक 20% के लाभ पर तथा दूसरी पुस्तक 10% की हानि पर बेच दी । पहली पुस्तक का विक्रय मूल्य कितना है, यदि पूरे लेनदेन में ना तो लाभ ना ही हानि हुई है ?</p>",
                    options_en: ["<p>₹125</p>", "<p>₹115</p>", 
                                "<p>₹110</p>", "<p>₹120</p>"],
                    options_hi: ["<p>₹125</p>", "<p>₹115</p>",
                                "<p>₹110</p>", "<p>₹120</p>"],
                    solution_en: "<p>17.(d) Let the cost price of the first book = ₹x. Then, cost price of second book = ₹(300 - x)<br>120% of x + 90% of (300 - x) = 300<br>30% x + 270 = 300<br>x = ₹100<br>Selling price of first book = 120% of x <br>= ₹120</p>",
                    solution_hi: "<p>17.(d) माना प्रथम पुस्तक का क्रय मूल्य = ₹x. तो, दूसरी पुस्तक का क्रय मूल्य = ₹(300 - x)<br>x का 120% + (300 - x) का 90% = 300<br>30% x + 270 = 300 <math display=\"inline\"><mo>&#8658;</mo></math> x = ₹100<br>पहली पुस्तक का वि. मू.= x का 120% = ₹120</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "misc",
                    question_en: "<p>18. A car dealer purchased an old car for ₹1,08,500 and spent some amount on its maintenance. He sold it for ₹1,56,250, thereby earning a profit of 25%. How much money did he spend on the maintenance of the car?</p>",
                    question_hi: "18. एक कार विक्रेता ने 1,08,500 रुपये में एक पुरानी कार खरीदी तथा कुछ राशि उसकी मरम्मत पर खर्च की । उसने उस कार को   ₹1,56,250 में बेचा, जिससे उसे 25% का लाभ हुआ । उसने कार की मरम्मत पर कितनी राशि खर्च की है ? ",
                    options_en: [" ₹16,500 ", " ₹20,625", 
                                " ₹8,687.5            ", " ₹47,750"],
                    options_hi: [" ₹16,500 ", " ₹20,625",
                                " ₹8,687.5     ", " ₹47,750"],
                    solution_en: "<p>18.(a) Cost price of car = ₹1,08,500<br>Let maintenance cost = ₹ x<br>Selling price = ₹ 1,56,250<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>125</mn><mn>100</mn></mfrac><mi mathvariant=\"normal\">&#160;</mi><mo>&#215;</mo><mo>(</mo><mn>108500</mn><mo>+</mo><mi mathvariant=\"normal\">x</mi><mo>)</mo></math> = 156250<br><math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> &times; (108500 + x) = 156250<br>x = ₹ 16,500</p>",
                    solution_hi: "<p>18.(a) कार का लागत मूल्य = ₹1,08,500<br>माना मरम्मत खर्च = ₹ x<br>विक्रय मूल्य = ₹ 1,56,250<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>125</mn><mn>100</mn></mfrac><mi mathvariant=\"normal\">&#160;</mi><mo>&#215;</mo><mo>(</mo><mn>108500</mn><mo>+</mo><mi mathvariant=\"normal\">x</mi><mo>)</mo></math> = 156250<br><math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> &times; (108500 + x) = 156250<br>x = ₹ 16,500</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>