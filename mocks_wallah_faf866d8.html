<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">60:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 100</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">100</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 60 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["6"] = {
                name: "Reasoning",
                start: 0,
                end: 23
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="6">Reasoning</option>`;
            sections["18"] = {
                name: "General Awareness",
                start: 24,
                end: 48
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="18">General Awareness</option>`;
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 49,
                end: 73
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["7"] = {
                name: "English",
                start: 74,
                end: 98
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="7">English</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 99,
                end: 99
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "6",
                    question_en: "<p>1. Each of the letters in the word &lsquo;REDUCTION&rsquo; are arranged in the English alphabetical order. How many letters are there in the English alphabetical order between the letter which is the third from the left end and the fourth from the left end in the group of letters thus formed ?</p>",
                    question_hi: "<p>1. शब्द \'REDUCTION\' के प्रत्येक अक्षर को अंग्रेजी वर्णानुक्रम में व्यवस्थित किया गया है। इस प्रकार बने अक्षरों के समूह में बाएं से तीसरे और बाएं से चौथे अक्षर के बीच अंग्रेजी वर्णानुक्रम में कितने अक्षर हैं ?</p>",
                    options_en: ["<p>Two</p>", "<p>One</p>", 
                                "<p>Three</p>", "<p>Four</p>"],
                    options_hi: ["<p>दो</p>", "<p>एक</p>",
                                "<p>तीन</p>", "<p>चार</p>"],
                    solution_en: "<p>1.(c) <strong>Given: </strong>REDUCTION<br>Arrange in english alphabetical order - CDEINORTU<br>The letter third from the left is &lsquo;E&rsquo; and fourth from the left end is &lsquo;I&rsquo;<br>Letter between &lsquo;E&rsquo; and &lsquo;I&rsquo; in the English alphabet is three.</p>",
                    solution_hi: "<p>1.(c) <strong>दिया गया:</strong> REDUCTION<br>अंग्रेजी वर्णमाला क्रम में व्यवस्थित करने पर - CDEINORTU<br>बाएं छोर से तीसरा अक्षर \'E\' है और बाएं छोर से चौथा अक्षर \'I\' है<br>अंग्रेजी वर्णमाला में \'E\' और \'I\' के बीच अक्षर तीन है।.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "6",
                    question_en: "<p>2. In a certain code language, <br>\'A ? B\' means &lsquo;A is the mother of B&rsquo;, <br>\'A ! B\' means &lsquo;A is the wife of B&rsquo;, <br>\'A &amp; B\' means &lsquo;A is the brother of B&rsquo; and <br>\'A x B\' means &lsquo;A is the father of B&rsquo;. <br>Which of the following means that Z is the wife\'s father\'s mother of L ?</p>",
                    question_hi: "<p>2. एक निश्चित कूट भाषा में,<br>\'A ? B\' का अर्थ है कि &lsquo;A, B की माता है&rsquo;,<br>\'A ! B\' का अर्थ है कि &lsquo;A, B की पत्नी है&rsquo;,<br>\'A &amp; B\' का अर्थ है कि &lsquo;A, B का भाई है&rsquo; और<br>\'A x B\' का अर्थ है कि &lsquo;A, B का पिता है&rsquo;।<br>निम्नलिखित में से किसका अर्थ यह है कि Z, L की परसास (पत्नी के पिता की माता) है?</p>",
                    options_en: ["<p>Z &amp; B ! T x J ? L</p>", "<p>Z ! B x T ? J &amp; L</p>", 
                                "<p>Z ? B x T &amp; J ! L</p>", "<p>Z ? B ! T &amp; J x L</p>"],
                    options_hi: ["<p>Z &amp; B ! T x J ? L</p>", "<p>Z ! B x T ? J &amp; L</p>",
                                "<p>Z ? B x T &amp; J ! L</p>", "<p>Z ? B ! T &amp; J x L</p>"],
                    solution_en: "<p>2.(c)<br>On checking all the options, only option (c) is satisfied.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730889855368.png\" alt=\"rId4\" width=\"258\" height=\"208\"></p>",
                    solution_hi: "<p>2.(c)<br>सभी विकल्पों की जाँच करने पर, केवल विकल्प (c) संतुष्ट होता है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730889855368.png\" alt=\"rId4\" width=\"258\" height=\"208\"></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "6",
                    question_en: "<p>3. Six letters B, F, D, X, N and A are written on different faces of a dice. Two positions of this dice are shown in the figures below. Find the letter on the face opposite to D.</p>\n<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730889855553.png\" alt=\"rId5\" width=\"223\" height=\"116\"></p>",
                    question_hi: "<p>3. एक पासे के विभिन्न फलकों पर छह अक्षर B, F, D, X, N और A लिखे गए हैं। इस पासे की दो स्थितियाँ नीचे चित्र में दर्शाई गई हैं। D के विपरीत वाले फलक पर आने वाला अक्षर ज्ञात कीजिए। <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730889855553.png\" alt=\"rId5\" width=\"223\" height=\"116\"></p>",
                    options_en: ["<p>A</p>", "<p>N</p>", 
                                "<p>B</p>", "<p>F</p>"],
                    options_hi: ["<p>A</p>", "<p>N</p>",
                                "<p>B</p>", "<p>F</p>"],
                    solution_en: "<p>3.(a) from the both dices opposite faces are<br>F <math display=\"inline\"><mo>&#8596;</mo></math> N, B &harr; X, D &harr; A</p>",
                    solution_hi: "<p>3.(a) दोनों पासों के विपरीत फलक हैं<br>F <math display=\"inline\"><mo>&#8596;</mo></math> N, B &harr; X, D &harr; A</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "6",
                    question_en: "4. The following contains two pairs of words which are related to each other in a certain way. Three of the following four word-pairs are alike as these have the same relationship and thus form a group. Which word-pair does NOT belong to that group? <br />(The words must be considered as meaningful English words and must not be grouped based on the number of letters/number of consonants/vowels in the word)",
                    question_hi: "4. यहाँ दो शब्दों के युग्म दिए गए हैं जो एक निश्चित तरीके से एक दूसरे से संबंधित हैं। निम्नलिखित चार शब्द-युग्मों में से तीन शब्द-युग्म एक समान हैं क्योंकि इनके बीच में समान संबंध है और इस प्रकार एक समूह बनाते हैं। निम्नलिखित में से कौन-सा शब्द-युग्म इस समूह से संबंधित नहीं है? <br />(शब्दों को सार्थक हिंदी शब्दों के रूप में माना जाना चाहिए और शब्द में अक्षरों की संख्या/व्यंजनों/स्वरों की संख्या के आधार पर समूहीकृत नहीं किया जाना चाहिए)",
                    options_en: [" Joy - Peace ", " Misery - Happiness ", 
                                " Problem - Concern ", " Worry - Fear "],
                    options_hi: [" आनंद - शांति ", " कष्ट - ख़ुशी ",
                                " समस्या - चिंता", " चिंता - भय"],
                    solution_en: "4.(b)<br />(Joy - Peace, Problem - Concern, Worry - Fear) represent words that are closely related or synonymous in meaning. However, Misery and Happiness are opposites.",
                    solution_hi: "4.(b)<br />(आनंद - शांति, समस्या - चिंता, चिंता - भय) उन शब्दों का प्रतिनिधित्व करते हैं जो अर्थ में निकट रूप से संबंधित या पर्यायवाची हैं। हालाँकि, कष्ट और खुशी विपरीत हैं।",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "6",
                    question_en: "<p>5. Select the correct mirror image of the given figure when the mirror is placed at MN as shown below. <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730889855688.png\" alt=\"rId6\" width=\"176\" height=\"157\"></p>",
                    question_hi: "<p>5. जब दर्पण को नीचे दर्शाए गए अनुसार MN पर रखा जाता है तो दी गई आकृति के सही दर्पण प्रतिबिंब का चयन कीजिए। <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730889855688.png\" alt=\"rId6\" width=\"176\" height=\"157\"></p>",
                    options_en: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730889855783.png\" alt=\"rId7\" width=\"175\" height=\"49\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730889855878.png\" alt=\"rId8\" width=\"181\" height=\"64\"></p>", 
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730889855984.png\" alt=\"rId9\" width=\"179\" height=\"61\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730889856076.png\" alt=\"rId10\" width=\"180\" height=\"62\"></p>"],
                    options_hi: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730889855783.png\" alt=\"rId7\" width=\"175\" height=\"49\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730889855878.png\" alt=\"rId8\" width=\"181\" height=\"64\"></p>",
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730889855984.png\" alt=\"rId9\" width=\"179\" height=\"61\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730889856076.png\" alt=\"rId10\" width=\"180\" height=\"62\"></p>"],
                    solution_en: "<p>5.(d) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730889856076.png\" alt=\"rId10\" width=\"180\" height=\"62\"></p>",
                    solution_hi: "<p>5.(d) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730889856076.png\" alt=\"rId10\" width=\"180\" height=\"62\"></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "6",
                    question_en: "<p>6. Which of the following terms will replace the question mark (?) in the given series? <br>BHVC, ZLTG, XPRK, ? , TXNS</p>",
                    question_hi: "<p>6. निम्नलिखित में से कौन-सा पद दी गई श्रृंखला में प्रश्न चिह्न (?) को प्रतिस्थापित करेगा?<br>BHVC, ZLTG, XPRK, ? , TXNS</p>",
                    options_en: ["<p>VTPO</p>", "<p>VTPN</p>", 
                                "<p>VPTN</p>", "<p>VPTO</p>"],
                    options_hi: ["<p>VTPO</p>", "<p>VTPN</p>",
                                "<p>VPTN</p>", "<p>VPTO</p>"],
                    solution_en: "<p>6.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730889856198.png\" alt=\"rId11\" width=\"433\" height=\"158\"></p>",
                    solution_hi: "<p>6.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730889856198.png\" alt=\"rId11\" width=\"433\" height=\"158\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "6",
                    question_en: "<p>7. Which figure should replace the question mark (?) if the following series were to be continued?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730889856327.png\" alt=\"rId12\" width=\"463\" height=\"122\"></p>",
                    question_hi: "<p>7. निम्नलिखित श्रृंखला को जारी रखने के लिए प्रश्न चिह्न (?) के स्थान पर कौन-सी आकृति आनी चाहिए?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730889856327.png\" alt=\"rId12\" width=\"463\" height=\"122\"></p>",
                    options_en: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730889856438.png\" alt=\"rId13\" width=\"102\" height=\"101\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730889856545.png\" alt=\"rId14\" width=\"101\" height=\"101\"></p>", 
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730889856654.png\" alt=\"rId15\" width=\"99\" height=\"98\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730889856779.png\" alt=\"rId16\" width=\"96\" height=\"93\"></p>"],
                    options_hi: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730889856438.png\" alt=\"rId13\" width=\"102\" height=\"101\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730889856545.png\" alt=\"rId14\" width=\"101\" height=\"101\"></p>",
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730889856654.png\" alt=\"rId15\" width=\"99\" height=\"98\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730889856779.png\" alt=\"rId16\" width=\"96\" height=\"93\"></p>"],
                    solution_en: "<p>7.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730889856545.png\" alt=\"rId14\" width=\"101\" height=\"101\"></p>",
                    solution_hi: "<p>7.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730889856545.png\" alt=\"rId14\" width=\"101\" height=\"101\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "6",
                    question_en: "<p>8. Two sets of numbers are given below. In each set of numbers, certain mathematical operation(s) on the first number result(s) in the second number. Similarly, certain mathematical operation(s) on the second number result(s) in the third number and so on. Which of the given options follows the same set of operations as in the given sets? <br>(<strong>NOTE: </strong>Operations should be performed on the whole numbers, without breaking down the numbers into their constituent digits. E.g. 13 &ndash; Operations on 13 such as adding/subtracting/multiplying to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed.) <br>9 &ndash; 27 &ndash; 31 &ndash; 36; 10 &ndash; 30 &ndash; 34 &ndash; 39</p>",
                    question_hi: "<p>8. नीचे संख्याओं के दो समुच्चय दिए गए हैं। संख्याओं के प्रत्येक समुच्चय में, पहली संख्या पर कुछ निश्चित गणितीय संक्रिया करने पर दूसरी संख्या प्राप्त होती है। इसी प्रकार, दूसरी संख्या पर कुछ निश्चित गणितीय संक्रिया करने पर तीसरी संख्या प्राप्त होती है और इसी तरह आगे भी होती है। दिए गए विकल्पों में से कौन-सा विकल्प दिए गए समुच्चयों की तरह संक्रियाओं के समान समुच्चय का अनुसरण करता है? <br>(<strong>नोट: </strong>संख्याओं को उनके घटक अंकों में तोड़े बिना, पूर्ण संख्याओं पर संक्रियाएँ की जानी चाहिए। उदाहरण के लिए 13 लीजिए - 13 पर की जाने वाली संक्रियाएँ जैसे कि 13 में जोड़ना/घटाना/गुणा करना आदि केवल 13 पर किया जा सकता है। 13 को 1 और 3 में तोड़ना, तथा 1 और 3 पर गणितीय संक्रिया करने की अनुमति नहीं है।)<br>9 &ndash; 27 &ndash; 31 &ndash; 36; 10 &ndash; 30 &ndash; 34 &ndash; 39</p>",
                    options_en: ["<p>8 &ndash; 24 &ndash; 28 &ndash; 35</p>", "<p>15 &ndash; 45 &ndash; 50 &ndash; 55</p>", 
                                "<p>11 &ndash; 33 &ndash; 38 &ndash; 44</p>", "<p>5 &ndash; 15 &ndash; 19 &ndash; 24</p>"],
                    options_hi: ["<p>8 &ndash; 24 &ndash; 28 &ndash; 35</p>", "<p>15 &ndash; 45 &ndash; 50 &ndash; 55</p>",
                                "<p>11 &ndash; 33 &ndash; 38 &ndash; 44</p>", "<p>5 &ndash; 15 &ndash; 19 &ndash; 24</p>"],
                    solution_en: "<p>8.(d) <strong>Logic:- </strong>1st number &times; 3 = 2nd number, 2nd number + 4 = 3rd number, <br>3rd number + 5 = 4th number.<br>9 &ndash; 27 &ndash; 31 &ndash; 36 :- 9 &times; 3 = 27 , 27 + 4 = 31, 31 +5 = 36<br>10 &ndash; 30 &ndash; 34 &ndash; 39 :- 10 &times; 3 = 30, 30 + 4 = 34, 34 + 5= 39<br>Similarly<br>5 &ndash; 15 &ndash; 19 &ndash; 24 :- 5 &times; 3 = 15, 15 + 4= 19, 19 + 5 = 24</p>",
                    solution_hi: "<p>8.(d) <strong>तर्क:-</strong> पहली संख्या &times; 3 = दूसरी संख्या, दूसरी संख्या + 4 = तीसरी संख्या, <br>तीसरी संख्या + 5 = चौथी संख्या.<br>9 &ndash; 27 &ndash; 31 &ndash; 36 :- 9 &times; 3 = 27 , 27 + 4 = 31, 31 +5 = 36<br>10 &ndash; 30 &ndash; 34 &ndash; 39 :- 10 &times; 3 = 30, 30 + 4 = 34, 34 + 5= 39<br>इसी प्रकार <br>5 &ndash; 15 &ndash; 19 &ndash; 24 :- 5 &times; 3 = 15, 15 + 4= 19, 19 + 5 = 24</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "6",
                    question_en: "<p>9. How many squares are there in the figure shown below?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730889856897.png\" alt=\"rId17\" width=\"165\" height=\"159\"></p>",
                    question_hi: "<p>9. नीचे दर्शायी गयी आकृति में कितने वर्ग हैं?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730889856897.png\" alt=\"rId17\" width=\"165\" height=\"159\"></p>",
                    options_en: ["<p>11</p>", "<p>13</p>", 
                                "<p>9</p>", "<p>10</p>"],
                    options_hi: ["<p>11</p>", "<p>13</p>",
                                "<p>9</p>", "<p>10</p>"],
                    solution_en: "<p>9.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730889857032.png\" alt=\"rId18\" width=\"211\" height=\"208\"><br>ABSR, ACMQ, GFKI, HYJI, YEKJ, EKLD, YOMD, TYOP, KWVU, JKNO, KLMN<br>There are 11 squares.</p>",
                    solution_hi: "<p>9.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730889857032.png\" alt=\"rId18\" width=\"211\" height=\"208\"><br>ABSR, ACMQ, GFKI, HYJI, YEKJ, EKLD, YOMD, TYOP, KWVU, JKNO, KLMN<br>11 वर्ग हैं.</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "6",
                    question_en: "<p>10. Select the option in which the numbers share the same relationship as that shared by the given pair of numbers. (NOTE: Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g. 13 &ndash; Operations on 13 such as adding/deleting/multiplying etc. to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed.) <br>63 : 441 <br>51 : 357</p>",
                    question_hi: "<p>10. उस विकल्&zwj;प का चयन कीजिए जिसमें संख्&zwj;याओं के मध्&zwj;य वही संबंध है जो नीचे दिए गए युग्&zwj;म की संख्&zwj;याओं के मध्&zwj;य है। <br>(ध्यान दें: संख्याओं को उनके घटक अंकों में विभाजित किए बिना, पूर्ण संख्याओं पर संक्रियाएं की जानी चाहिए। उदाहरण के लिए 13 को लें- 13 पर की जाने वाली संक्रियाएं जैसे जोड़ना/ घटाना/ गुणा करना आदि केवल 13 पर की जा सकती हैं। 13 को 1 और 3 में तोड़ना और फिर 1 और 3 पर गणितीय संक्रियाएं करने की अनुमति नहीं है।)<br>63 : 441 <br>51 : 357</p>",
                    options_en: ["<p>68 : 340</p>", "<p>54 : 324</p>", 
                                "<p>39 : 273</p>", "<p>89 : 96</p>"],
                    options_hi: ["<p>68 : 340</p>", "<p>54 : 324</p>",
                                "<p>39 : 273</p>", "<p>89 : 96</p>"],
                    solution_en: "<p>10.(c) <strong>Logic:</strong> 1st number &times; 7 = 2nd number<br>63 : 441 :- 63 &times; 7 = 441 <br>51 : 357 :- 51 &times; 7 = 357<br>Similarly<br>39 : 273 :- 39 &times; 7 = 273</p>",
                    solution_hi: "<p>10.(c) <strong>तर्क: </strong>पहली संख्या &times; 7 = दूसरी संख्या<br>63 : 441 :- 63 &times; 7 = 441 <br>51 : 357 :- 51 &times; 7 = 357<br>इसी प्रकार<br>39 : 273 :- 39 &times; 7 = 273</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "6",
                    question_en: "11. Select the word-pair that best represents a similar relationship to the one expressed in the pair of words given below. <br />(The words must be considered as meaningful English words and must NOT be related to each other based on the number of letters/number of consonants/vowels in the word) <br />Kaziranga : Rhinoceros",
                    question_hi: "11. उस शब्द-युग्म का चयन करें, जो नीचे दिए गए शब्दों के युग्म में व्यक्त किए गए संबंध के समान संबंधों का सबसे बेहतर ढंग से प्रतिनिधित्व करता है।<br />(शब्दों को अर्थपूर्ण हिंदी शब्द माना जाना चाहिए और शब्द में अक्षरों की संख्या/व्यंजनों/स्वरों की संख्या के आधार पर एक दूसरे से संबंधित नहीं होना चाहिए)<br />Kaziranga : Rhinoceros (काजीरंगा : गैंडा)",
                    options_en: [" Gahirmatha : Lions  ", " Periyar : Elephants  ", 
                                " Ranthambore : Peacocks ", " Gir : Leopards"],
                    options_hi: [" Gahirmatha : Lions (गहिरमाथा : सिंह)", " Periyar : Elephants (पेरियार : हाथी) ",
                                " Ranthambore : Peacocks (रणथंभौर : मोर) ", " Gir : Leopards (गिर : तेंदुआ)"],
                    solution_en: "11.(b) As Kaziranga<br /> is famous for Rhinoceros, just as Periyar is famous for Elephants.",
                    solution_hi: "11.(b) जैसे काजीरंगा गैंडे के लिए प्रसिद्ध है, वैसे ही पेरियार हाथियों के लिए प्रसिद्ध है।",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "6",
                    question_en: "12. Select the option that is related to the third word in the same way as the second word is related to the first word. (The words must be considered as meaningful English words and must NOT be related to each other based on the number of letters/number of consonants/vowels in the word) <br />Magician : Magic :: Dancer : ?",
                    question_hi: "12. उस विकल्प का चयन करें जो तीसरे शब्द से उसी प्रकार संबंधित है जिस प्रकार दूसरा शब्द पहले शब्द से संबंधित है। (शब्दों को अर्थपूर्ण अंग्रेजी/ हिन्दी शब्दों के रूप में माना जाना चाहिए और इन्हें शब्द में अक्षरों की संख्या/व्यंजनों की संख्या/स्वरों की संख्या के आधार पर एक दूसरे से संबद्ध नहीं किया जाना चाहिए।) <br />जादूगर : जादू :: नर्तक : ?",
                    options_en: [" Dance", " Theatre ", 
                                " Movie", " Song"],
                    options_hi: [" नृत्य", " थिएटर ",
                                " चलचित्र ", " गीत"],
                    solution_en: "12.(a) Just as a magician performs magic, a dancer performs dance. ",
                    solution_hi: "12.(a) जैसे जादूगर जादू दिखाता है वैसे ही नर्तक नृत्य करता है ।   ",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "6",
                    question_en: "<p>13. Select the option that represents the letters that when sequentially placed from left to right in the blanks below will complete the letter series.<br>_ O _ O _ _ O _ P _ O _</p>",
                    question_hi: "<p>13. उस विकल्प का चयन कीजिए जो उन अक्षरों को प्रदर्शित करता है जिन्हें नीचे दिए गए रिक्&zwj;त स्&zwj;थानों में बाएं से दाएं क्रमिक रूप से रखने पर अक्षर श्रृंखला पूर्ण हो जाएगी।<br>_ O _ O _ _ O _ P _ O _</p>",
                    options_en: ["<p>PPOOPOP</p>", "<p>OOPPPOO</p>", 
                                "<p>OPOPOOP</p>", "<p>PPOOOPO</p>"],
                    options_hi: ["<p>PPOOPOP</p>", "<p>OOPPPOO</p>",
                                "<p>OPOPOOP</p>", "<p>PPOOOPO</p>"],
                    solution_en: "<p>13.(c)<br><span style=\"text-decoration: underline;\"><strong>O</strong></span> O <span style=\"text-decoration: underline;\"><strong>P</strong></span> /O <span style=\"text-decoration: underline;\"><strong>O</strong></span> <span style=\"text-decoration: underline;\"><strong>P</strong></span> /O <span style=\"text-decoration: underline;\"><strong>O</strong></span> P /<span style=\"text-decoration: underline;\"><strong>O</strong></span> O <span style=\"text-decoration: underline;\"><strong>P</strong></span></p>",
                    solution_hi: "<p>13.(c)<br><span style=\"text-decoration: underline;\"><strong>O</strong></span> O <span style=\"text-decoration: underline;\"><strong>P</strong></span> /O <span style=\"text-decoration: underline;\"><strong>O</strong></span> <span style=\"text-decoration: underline;\"><strong>P</strong></span> /O <span style=\"text-decoration: underline;\"><strong>O</strong></span> P /<span style=\"text-decoration: underline;\"><strong>O</strong></span> O <span style=\"text-decoration: underline;\"><strong>P</strong></span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "6",
                    question_en: "<p>14. Six letters G, H, I, J, K and L are written on different faces of a dice. Two positions of this dice are shown in the figure below. Find the letter on the face opposite to G.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730889857138.png\" alt=\"rId19\" width=\"220\" height=\"91\"></p>",
                    question_hi: "<p>14. एक पासे के विभिन्न फलकों पर छ: अक्षर G, H, I, J, K और L लिखे गए हैं। नीचे दिए गए चित्र में इस पासे की दो स्थितियों को दर्शाया गया है। दिए गए विकल्पों में से G के विपरीत फलक पर लिखा अक्षर ज्ञात कीजिए। <br><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730889857138.png\" alt=\"rId19\" width=\"220\" height=\"91\"></p>",
                    options_en: ["<p>K</p>", "<p>I</p>", 
                                "<p>L</p>", "<p>J</p>"],
                    options_hi: ["<p>K</p>", "<p>I</p>",
                                "<p>L</p>", "<p>J</p>"],
                    solution_en: "<p>14.(c) from both dices opposite faces are <br>L <math display=\"inline\"><mo>&#8596;</mo></math> G, J &harr; I, K &harr; H</p>",
                    solution_hi: "<p>14.(c) दोनों पासों के विपरीत फलक हैं <br>L <math display=\"inline\"><mo>&#8596;</mo></math> G, J &harr; I, K &harr; H</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "6",
                    question_en: "<p>15. Study the given Venn diagram and answer the question that follows.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730889857262.png\" alt=\"rId20\" width=\"265\" height=\"261\"><br>How many women are either smart or brave or both?</p>",
                    question_hi: "<p>15. दिए गए वेन आरेख का अध्ययन करें और नीचे दिए गए प्रश्न का उत्तर दें।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730889857364.png\" alt=\"rId21\" width=\"253\" height=\"252\"><br>कितनी महिलाएं या तो चतुर है या बहादुर हैं या दोनों हैं?</p>",
                    options_en: ["<p>27</p>", "<p>23</p>", 
                                "<p>30</p>", "<p>26</p>"],
                    options_hi: ["<p>27</p>", "<p>23</p>",
                                "<p>30</p>", "<p>26</p>"],
                    solution_en: "<p>15.(c) women are either smart or brave or both = 13 + 14 + 3 = 30</p>",
                    solution_hi: "<p>15.(c) महिलाएं या तो चतुर या बहादुर या दोनों हैं = 13 + 14 + 3 = 30</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "6",
                    question_en: "<p>16. Identify the figure given in the options which when put in place of the question mark (?) will logically complete the series?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730889857450.png\" alt=\"rId22\" width=\"455\" height=\"103\"></p>",
                    question_hi: "<p>16. विकल्पों में दी गई उस आकृति की पहचान कीजिए जिसे प्रश्न चिह्न (?) के स्थान पर रखने पर शृंखला तार्किक रूप से पूरी हो जाएगी।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730889857450.png\" alt=\"rId22\" width=\"455\" height=\"103\"></p>",
                    options_en: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730889857555.png\" alt=\"rId23\" width=\"110\" height=\"111\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730889857675.png\" alt=\"rId24\" width=\"111\" height=\"107\"></p>", 
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730889857750.png\" alt=\"rId25\" width=\"110\" height=\"112\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730889857856.png\" alt=\"rId26\" width=\"110\" height=\"102\"></p>"],
                    options_hi: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730889857555.png\" alt=\"rId23\" width=\"110\" height=\"111\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730889857675.png\" alt=\"rId24\" width=\"111\" height=\"107\"></p>",
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730889857750.png\" alt=\"rId25\" width=\"110\" height=\"112\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730889857856.png\" alt=\"rId26\" width=\"110\" height=\"102\"></p>"],
                    solution_en: "<p>16.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730889857555.png\" alt=\"rId23\" width=\"110\" height=\"111\"></p>",
                    solution_hi: "<p>16.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730889857555.png\" alt=\"rId23\" width=\"110\" height=\"111\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "6",
                    question_en: "<p>17. What will come in place of the question mark (?) in the following equation if &lsquo;+&rsquo; and &lsquo;&times;&lsquo; are interchanged and &lsquo;&minus;&rsquo; and &lsquo;&divide;&rsquo; are interchanged ? <br>27 &minus; 6 + 4 &times; 8 &divide; 6 = ?</p>",
                    question_hi: "<p>17. निम्नलिखित समीकरण में यदि \'+\' और \'&times;\' को आपस में बदल दिया जाए और \'-\' और \'&divide;\' को आपस में बदल दिया जाए तो प्रश्न चिह्न (?) के स्थान पर कौन सी संख्&zwj;या आएगी?<br>27 &minus; 6 + 4 &times; 8 &divide; 6 = ?</p>",
                    options_en: ["<p>22</p>", "<p>18</p>", 
                                "<p>16</p>", "<p>20</p>"],
                    options_hi: ["<p>22</p>", "<p>18</p>",
                                "<p>16</p>", "<p>20</p>"],
                    solution_en: "<p>17.(d) <strong>Given: </strong>27 &minus; 6 + 4 &times; 8 &divide; 6 = ?<br>As per the instruction given in question, after interchanging the symbol &lsquo;+&rsquo; and &lsquo;&times;&lsquo; and &lsquo;&minus;&rsquo; and &lsquo;&divide;&rsquo;<br>We get<br>27 &minus; 6 + 4 &times; 8 &divide; 6 = ? <br>27 &divide; 6 &times; 4 + 8 - 6 <br><math display=\"inline\"><mfrac><mrow><mn>9</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> &times; 4 + 8 - 6<br>18 + 8 - 6 <br>26 - 6 = 20</p>",
                    solution_hi: "<p>17.(d) <strong>दिया गया है: </strong>27 &minus; 6 +4 &times; 8 &divide; 6 = ?<br>प्रश्न में दिए गए निर्देश के अनुसार, प्रतीक \'+\' और \'&times;\' और \'-\' और \'&divide;\' को आपस में बदलने के बाद<br>हम पाते हैं<br>27 &minus; 6 + 4 &times; 8 &divide; 6 = ? <br>27 &divide; 6 &times; 4 + 8 - 6 <br><math display=\"inline\"><mfrac><mrow><mn>9</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> &times; 4 + 8 - 6<br>18 + 8 - 6 <br>26 - 6 = 20</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "6",
                    question_en: "<p>18. 13 is related to 117 following a certain logic. Following the same logic, 23 is related to 207. To which of the following is 51 related, following the same logic ? <br>(NOTE: Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g. 13 &ndash; Operations on 13 such as adding /deleting /multiplying etc. to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed)</p>",
                    question_hi: "<p>18. एक निश्चित तर्क का अनुसरण करते हुए 13, 117 से संबंधित है। उसी तर्क का अनुसरण करते हुए, 23, 207 से संबंधित है। उसी तर्क का अनुसरण करते हुए, 51 निम्नलिखित में से किससे संबंधित है? <br>(नोट: संख्याओं को उसके घटक अंकों में तोड़े बिना, पूर्ण संख्याओं पर संक्रियाएं की जानी चाहिए। उदाहरण के लिए संख्या 13 को लें - 13 पर संक्रियाएं, जैसे कि 13 में जोड़ना/घटाना/गुणा करना आदि, की जा सकती हैं। 13 को 1 और 3 में तोड़ने और फिर 1 और 3 पर गणितीय संक्रियाएं करने की अनुमति नहीं है)</p>",
                    options_en: ["<p>350</p>", "<p>450</p>", 
                                "<p>359</p>", "<p>459</p>"],
                    options_hi: ["<p>350</p>", "<p>450</p>",
                                "<p>359</p>", "<p>459</p>"],
                    solution_en: "<p>18.(d) <strong>Logic: </strong>1st number &times; 9 = 2nd number<br>(13 : 117) :- 13 &times; 9 = 117<br>(23 : 207) : - 23 &times; 9 = 207<br>Similarly<br>(51 : x) :- 51 &times; 9 = 459</p>",
                    solution_hi: "<p>18.(d) <strong>तर्क: </strong>पहली संख्या &times; 9 = दूसरी संख्या<br>(13 : 117) :- 13 &times; 9 = 117<br>(23 : 207) : - 23 &times; 9 = 207<br>उसी प्रकार<br>(51 : x) :- 51 &times; 9 = 459</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "6",
                    question_en: "<p>19. What will come in place of the question mark (?) in the following equation, if &lsquo;+&rsquo; and &lsquo;&ndash;&rsquo; are interchanged and &lsquo;&times;&rsquo; and &lsquo;&divide;&rsquo; are interchanged? <br>2864 &times; 4 &ndash; 168 &divide; 2 + 69 = ?</p>",
                    question_hi: "<p>19. यदि &lsquo;+&rsquo; और &lsquo;&ndash;&rsquo; को आपस में बदल दिया जाए और &lsquo;&times;&rsquo; और &lsquo;&divide;&rsquo; को आपस में बदल दिया जाए, तो निम्नलिखित समीकरण में प्रश्न चिह्न (?) के स्थान पर क्या आएगा? <br>2864 &times; 4 &ndash; 168 &divide; 2 + 69 = ?</p>",
                    options_en: ["<p>389</p>", "<p>983</p>", 
                                "<p>839</p>", "<p>938</p>"],
                    options_hi: ["<p>389</p>", "<p>983</p>",
                                "<p>839</p>", "<p>938</p>"],
                    solution_en: "<p>19.(b) <strong>Given:</strong> 2864 &times; 4 &ndash; 168 &divide; 2 + 69 = ? <br>As per the instructions given in the question, after interchanging the symbols &lsquo;+&rsquo; and &lsquo;&ndash;&rsquo; and &lsquo;&times;&rsquo; and &lsquo;&divide;&rsquo; we get.<br>2864 &divide; 4 + 168 &times; 2 - 69 = ? <br>716 +168 &times; 2 - 69<br>716 + 336 - 69<br>1052 - 69 = 983</p>",
                    solution_hi: "<p>19.(b) <strong>दिया गया है:</strong> 2864 &times; 4 &ndash; 168 &divide; 2+ 69 = ?&nbsp;<br>प्रश्न में दिए गए निर्देशों के अनुसार, प्रतीकों \'+\' और \'-\' तथा \'&times;\' और \'&divide;\' को आपस में बदलने पर हमें प्राप्त होता है।<br>2864 &divide; 4 + 168 &times; 2 - 69 = ? <br>716 +168 &times; 2 - 69<br>716 + 336 - 69<br>1052 - 69 = 983</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "6",
                    question_en: "<p>20. Which of the following numbers will replace the question mark (?) in the given series? <br>22, 23, 27, 54, 70, 195, ?</p>",
                    question_hi: "<p>20. निम्नलिखित में से कौन सी संख्&zwj;या दी गई श्रृंखला में प्रश्&zwj;न चिह्न (?) का स्&zwj;थान लेगी?<br>22, 23, 27, 54, 70, 195, ?</p>",
                    options_en: ["<p>196</p>", "<p>230</p>", 
                                "<p>200</p>", "<p>231</p>"],
                    options_hi: ["<p>196</p>", "<p>230</p>",
                                "<p>200</p>", "<p>231</p>"],
                    solution_en: "<p>20.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730889857963.png\" alt=\"rId27\" width=\"379\" height=\"77\"></p>",
                    solution_hi: "<p>20.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730889857963.png\" alt=\"rId27\" width=\"379\" height=\"77\"></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "6",
                    question_en: "<p>21. In a certain code language, &lsquo;VAT&rsquo; is coded as &lsquo;43&rsquo; and &lsquo;AWW&rsquo; is coded as &lsquo;47&rsquo;. How will &lsquo;ZHU&rsquo; be coded in that language?</p>",
                    question_hi: "<p>21. एक निश्चित कूट भाषा में, \'VAT\' को \'43\' के रूप में कूटबद्ध किया जाता है और \'AWW\' को \'47\' के रूप में कूटबद्ध किया जाता है। उसी भाषा में \'ZHU\' को किस प्रकार कूटबद्ध किया जाएगा?</p>",
                    options_en: ["<p>63</p>", "<p>48</p>", 
                                "<p>55</p>", "<p>62</p>"],
                    options_hi: ["<p>63</p>", "<p>48</p>",
                                "<p>55</p>", "<p>62</p>"],
                    solution_en: "<p>21.(c)<br>&lsquo;VAT&rsquo; <math display=\"inline\"><mo>&#8594;</mo></math> place value &rarr; 22 + 1 + 20 = 43<br>&lsquo;AWW&rsquo; <math display=\"inline\"><mo>&#8594;</mo></math> place value &rarr; 1 + 23 +23 = 47<br>similarly<br>&lsquo;ZHU&rsquo; <math display=\"inline\"><mo>&#8594;</mo></math> place value &rarr; 26 + 8 + 21 = 55</p>",
                    solution_hi: "<p>21.(c)<br>&lsquo;VAT&rsquo; <math display=\"inline\"><mo>&#8594;</mo></math>स्थानीय मान &rarr; 22 + 1 + 20 = 43<br>&lsquo;AWW&rsquo; <math display=\"inline\"><mo>&#8594;</mo></math> स्थानीय मान &rarr; 1 + 23 +23 = 47<br>उसी प्रकार<br>&lsquo;ZHU&rsquo; <math display=\"inline\"><mo>&#8594;</mo></math> स्थानीय मान &rarr; 26 + 8 + 21 = 55</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "6",
                    question_en: "<p>22. Three of the following four options are alike in a certain way and thus form a group. Which is the one that does NOT belong to that group? <br>(NOTE: Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g. 13 &ndash; Operations on 13 such as adding/subtracting/multiplying etc. to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed.)</p>",
                    question_hi: "<p>22. निम्नलिखित चार विकल्पों में से तीन किसी न किसी रूप में समान हैं और इस प्रकार, एक समूह बनाते हैं। कौन-सा विकल्प उस समूह से संबंधित नहीं है? <br>(ध्यान दें: संख्याओं को उनके घटक अंकों में तोड़े बिना, संक्रियाएं पूर्ण संख्याओं पर की जानी चाहिए। उदाहरण के लिए 13 को लें- संख्या 13 में की जाने वाली संक्रिया, जैसे कि जोड़ना/घटाना/गुणा करना केवल 13 में किया जा सकता है। 13 को अंक 1 और 3 में तोड़ना और फिर 1 और 3 के साथ गणितीय संक्रियाएं करने की अनुमति नहीं है।)</p>",
                    options_en: ["<p>360 &ndash; 12 &ndash; 6</p>", "<p>82 &ndash; 8 &ndash; 2</p>", 
                                "<p>63 &ndash; 6 &ndash; 3</p>", "<p>164 &ndash; 10 &ndash; 4</p>"],
                    options_hi: ["<p>360 &ndash; 12 &ndash; 6</p>", "<p>82 &ndash; 8 &ndash; 2</p>",
                                "<p>63 &ndash; 6 &ndash; 3</p>", "<p>164 &ndash; 10 &ndash; 4</p>"],
                    solution_en: "<p>22.(b)<strong> Logic:</strong> (2nd number)<sup>2</sup> + (3rd number)<sup>3</sup> = 1st number<br>360 &ndash; 12 &ndash; 6 :- 12<sup>2</sup> + 6<sup>3</sup> = 144 + 216 = 360<br>63 &ndash; 6 &ndash; 3 :- 6<sup>2</sup> + 3<sup>3</sup> = 36 + 27 = 63<br>164 &ndash; 10 &ndash; 4 :- 10<sup>2</sup> + 4<sup>3</sup> = 100 + 64 = 164<br>But<br>82 &ndash; 8 &ndash; 2 :- 8<sup>2</sup> + 2<sup>3</sup> = 64 +8 = 72 (<math display=\"inline\"><mo>&#8800;</mo></math>82)</p>",
                    solution_hi: "<p>22.(b) <strong>तर्क:</strong> (दूसरी संख्या)<sup>2</sup> + (तीसरी संख्या)<sup>3</sup> = पहली संख्या<br>360 &ndash; 12 &ndash; 6 :- 12<sup>2</sup> + 6<sup>3</sup> = 144 + 216 = 360<br>63 &ndash; 6 &ndash; 3 :- 6<sup>2</sup> + 3<sup>3</sup> = 36 + 27 = 63<br>164 &ndash; 10 &ndash; 4 :- 10<sup>2</sup> + 4<sup>3</sup> = 100 + 64 = 164<br>लेकिन<br>82 &ndash; 8 &ndash; 2 :- 8<sup>2</sup> + 2<sup>3</sup> = 64 +8 = 72 (<math display=\"inline\"><mo>&#8800;</mo></math>82)</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "23",
                    section: "6",
                    question_en: "<p>23. Select the option figure that will replace the question mark (?) in the figure given below to complete the pattern. <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730889858082.png\" alt=\"rId28\" width=\"177\" height=\"157\"></p>",
                    question_hi: "<p>23. उस विकल्प आकृति का चयन कीजिए, जो पैटर्न को पूरा करने के लिए नीचे दी गई आकृति में प्रश्न चिह्न (?) को प्रतिस्थापित करेगी।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730889858082.png\" alt=\"rId28\" width=\"177\" height=\"157\"></p>",
                    options_en: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730889858210.png\" alt=\"rId29\" width=\"102\" height=\"91\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730889858325.png\" alt=\"rId30\" width=\"101\" height=\"92\"></p>", 
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730889858440.png\" alt=\"rId31\" width=\"101\" height=\"87\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730889858532.png\" alt=\"rId32\" width=\"98\" height=\"87\"></p>"],
                    options_hi: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730889858210.png\" alt=\"rId29\" width=\"102\" height=\"91\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730889858325.png\" alt=\"rId30\" width=\"101\" height=\"92\"></p>",
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730889858440.png\" alt=\"rId31\" width=\"101\" height=\"87\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730889858532.png\" alt=\"rId32\" width=\"98\" height=\"87\"></p>"],
                    solution_en: "<p>23.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730889858210.png\" alt=\"rId29\" width=\"102\" height=\"91\"></p>",
                    solution_hi: "<p>23.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730889858210.png\" alt=\"rId29\" width=\"102\" height=\"91\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "24",
                    section: "6",
                    question_en: "<p>24. In a certain code language, \'ESTATE\' is written as \'80\', \'FAMOUS\' is written as \'85\', how will \'FOREST\' be written in that language?</p>",
                    question_hi: "<p>24. एक निश्चित कूट भाषा में, \'ESTATE\' को \'80\' लिखा जाता है, \'FAMOUS\' को \'85\' लिखा जाता है, उसी कूट भाषा में \'FOREST\' को कैसे लिखा जाएगा?</p>",
                    options_en: ["<p>93</p>", "<p>88</p>", 
                                "<p>83</p>", "<p>90</p>"],
                    options_hi: ["<p>93</p>", "<p>88</p>",
                                "<p>83</p>", "<p>90</p>"],
                    solution_en: "<p>24.(a) <strong>Logic: </strong>sum of place value + 10 <br>&lsquo;ESTATE&rsquo; <math display=\"inline\"><mo>&#8594;</mo></math> (5 + 19 + 20 + 1 + 20 + 5) + 10 = 80<br>\'FAMOUS\' <math display=\"inline\"><mo>&#8594;</mo></math> (6 + 1 + 13 + 15 + 21 + 19) + 10 = 85<br>Similarly<br>\'FOREST\' <math display=\"inline\"><mo>&#8594;</mo></math> (6 + 15 + 18 + 5 + 19 + 20) + 10 = 93.</p>",
                    solution_hi: "<p>24.(a) <strong>तर्क: </strong>स्थानीय मान का योग + 10<br>&lsquo;ESTATE&rsquo; <math display=\"inline\"><mo>&#8594;</mo></math> (5 + 19 + 20 + 1 + 20 + 5) + 10 = 80<br>\'FAMOUS\' <math display=\"inline\"><mo>&#8594;</mo></math> (6 + 1 + 13 + 15 + 21 + 19) + 10 = 85<br>इसी प्रकार<br>\'FOREST\' <math display=\"inline\"><mo>&#8594;</mo></math> (6 + 15 + 18 + 5 + 19 + 20) + 10 = 93.</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "25",
                    section: "18",
                    question_en: "<p>25. How many people play either only volleyball or only chess as per the given Venn diagram?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730889858642.png\" alt=\"rId33\" width=\"280\" height=\"221\"></p>",
                    question_hi: "<p>25. दिए गए वेन आरेख के अनुसार ऐसे कितने लोग हैं जो या तो केवल वॉलीबॉल या केवल शतरंज खेलते हैं?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730889858760.png\" alt=\"rId34\" width=\"287\" height=\"219\"></p>",
                    options_en: ["<p>81</p>", "<p>88</p>", 
                                "<p>15</p>", "<p>66</p>"],
                    options_hi: ["<p>81</p>", "<p>88</p>",
                                "<p>15</p>", "<p>66</p>"],
                    solution_en: "<p>25.(d) people who play either only volleyball or only chess <br>23 + 43 = 66</p>",
                    solution_hi: "<p>25.(d) जो लोग या तो केवल वॉलीबॉल या केवल शतरंज खेलते हैं <br>23 + 43 = 66</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "26",
                    section: "18",
                    question_en: "<p>26. Which of the following is NOT a component of a flower?</p>",
                    question_hi: "<p>26. निम्नलिखित में से कौन-सा फूल का घटक नहीं है?</p>",
                    options_en: ["<p>Androecium</p>", "<p>Corolla</p>", 
                                "<p>Spines</p>", "<p>Calyx</p>"],
                    options_hi: ["<p>पुमंग</p>", "<p>दलपुंज</p>",
                                "<p>काँटा</p>", "<p>कर्णिका</p>"],
                    solution_en: "<p>26.(c) <strong>Spines. </strong>They are modified leaves or stems that serve as a defense mechanism in plants, particularly in cacti and succulents. Androecium : The male reproductive part of the flower, consisting of stamens (filaments and anthers). Corolla : The collective term for the petals of a flower. Calyx : The collective term for the sepals (green, leaf-like structures) that protect the flower bud.</p>",
                    solution_hi: "<p>26.(c) <strong>काँटा।</strong> ये संशोधित पत्तियाँ या तने हैं जो पौधों में, विशेष रूप से कैक्टस और रसीले पौधों में रक्षा तंत्र के रूप में काम करते हैं। पुमंग : फूल का नर प्रजनन भाग, जिसमें पुंकेसर (तंतु और परागकोष) होते हैं। दलपुंज : फूल की पंखुड़ियों के लिए सामूहिक शब्द। कर्णिका : बाह्यदलपुंज (हरे, पत्ती जैसी संरचनाएँ) के लिए सामूहिक शब्द जो फूल की कली की रक्षा करते हैं।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "27",
                    section: "18",
                    question_en: "<p>27. Where was the Hindu College established in the year 1791?</p>",
                    question_hi: "<p>27. वर्ष 1791 में हिंदू कॉलेज की स्थापना कहाँ हुई थी?</p>",
                    options_en: ["<p>Mathura</p>", "<p>Kolkata</p>", 
                                "<p>Benaras</p>", "<p>Patna</p>"],
                    options_hi: ["<p>मथुरा</p>", "<p>कोलकाता</p>",
                                "<p>बनारस</p>", "<p>पटना</p>"],
                    solution_en: "<p>27.(c) <strong>Benaras.</strong> The Hindu College was founded by Jonathan Duncan, a British officer, and Raja Rameshwar Singh, the Maharaja of Banaras. The college was originally known as the Sanskrit College, and it was dedicated to the study of Sanskrit language and literature. In 1958, the Sanskrit College became a university and was renamed Sampurnanand Sanskrit University in 1974. Raja Ram Mohan Roy established the Hindu College in Calcutta in 1817, which is now known as Presidency University, Kolkata.</p>",
                    solution_hi: "<p>27.(c) <strong>बनारस।</strong> हिंदू कॉलेज की स्थापना ब्रिटिश अधिकारी जोनाथन डंकन और बनारस के महाराजा राजा रामेश्वर सिंह द्वारा की गई थी। कॉलेज को मूल रूप से संस्कृत कॉलेज के नाम से जाना जाता था, और यह संस्कृत भाषा एवं साहित्य के अध्ययन के लिए समर्पित था। 1958 में, संस्कृत कॉलेज एक विश्वविद्यालय बन गया तथा 1974 में इसका नाम बदलकर संपूर्णानंद संस्कृत विश्वविद्यालय कर दिया गया। राजा राम मोहन राय ने 1817 में कलकत्ता में हिंदू कॉलेज की स्थापना की, जिसे अब प्रेसीडेंसी यूनिवर्सिटी, कोलकाता के नाम से जाना जाता है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "28",
                    section: "18",
                    question_en: "28. Where is the Gulf of Mannar located?",
                    question_hi: "28. मन्नार की खाड़ी कहाँ स्थित है?",
                    options_en: [" Between the south-eastern tip of India and the eastern coast of Sri Lanka", " Between the south-eastern tip of India and the western coast of Sri Lanka ", 
                                " Between the south-eastern tip of India and the north-western coast of Sri Lanka ", " Between the south-eastern tip of India and the southern coast of Sri Lanka"],
                    options_hi: [" भारत के दक्षिण-पूर्वी सिरे और श्रीलंका के पूर्वी तट के बीच ", " भारत के दक्षिण-पूर्वी सिरे और श्रीलंका के पश्चिमी तट के बीच ",
                                " भारत के दक्षिण-पूर्वी सिरे और श्रीलंका के उत्तर-पश्चिमी तट के बीच", " भारत के दक्षिण-पूर्वी सिरे और श्रीलंका के दक्षिणी तट के बीच"],
                    solution_en: "28.(b) Gulf of Mannar: It is located in the Coromandel Coast region. The Gulf of Mannar is separated from the Palk Bay by a chain of islands and coral reefs known as Adam\'s Bridge or Ram Setu. It is bounded to the northeast by Rameswaram (island), Adam’s (Rama’s) Bridge (a chain of shoals), and Mannar Island.",
                    solution_hi: "28.(b) मन्नार की खाड़ी: यह कोरोमंडल तट क्षेत्र में स्थित है। मन्नार की खाड़ी को पाक खाड़ी से द्वीपों और प्रवाल भित्तियों की एक श्रृंखला द्वारा अलग किया जाता है जिसे एडम ब्रिज या राम सेतु के नाम से जाना जाता है। यह उत्तर-पूर्व में रामेश्वरम (द्वीप), एडम्स (राम) ब्रिज (तटीय नदियों की एक श्रृंखला) और मन्नार द्वीप से घिरा हुआ है।",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "29",
                    section: "18",
                    question_en: "<p>29. Who among the following won a Gold Medal at the 2021 World Cadet Wrestling Championship held in Budapest, Hungary?</p>",
                    question_hi: "<p>29. निम्नलिखित में से किसने बुडापेस्ट, हंगरी में आयोजित 2021 विश्व कैडेट कुश्ती चैम्पियनशिप में स्वर्ण पदक जीता?</p>",
                    options_en: ["<p>Geeta Phogat</p>", "<p>Vinesh Phogat</p>", 
                                "<p>Pooja Dhanda</p>", "<p>Priya Malik</p>"],
                    options_hi: ["<p>गीता फोगाट</p>", "<p>विनेश फोगाट</p>",
                                "<p>पूजा ढांडा</p>", "<p>प्रिया मलिक</p>"],
                    solution_en: "<p>29.(d) <strong>Priya Malik. </strong>2021 World Cadet Wrestling Championship : She won Gold in the 73 kg weight category by defeating Kseniya Patapovich of Belarus with a score of 5-0. This victory was part of India\'s remarkable performance at the championships, where Indian wrestlers won a total of 13 medals, including five gold medals. Indian Wrestlers : Geeta Phogat, Bajrang Punia, KD Jadhav, Vinesh Phogat, Ravi Kumar Dahiya.</p>",
                    solution_hi: "<p>29.(d) <strong>प्रिया मलिक। </strong>2021 विश्व कैडेट कुश्ती चैंपियनशिप: उन्होंने 73 किलोग्राम भार वर्ग में बेलारूस की केसिया पटापोविच को 5-0 के स्कोर से हराकर स्वर्ण पदक जीता। यह जीत चैंपियनशिप में भारत के उल्लेखनीय प्रदर्शन का हिस्सा थी, जहाँ भारतीय पहलवानों ने पाँच स्वर्ण पदक सहित कुल 13 पदक जीते। भारतीय पहलवान: गीता फोगाट, बजरंग पुनिया, के.डी. जाधव, विनेश फोगाट, रवि कुमार दहिया।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "30",
                    section: "18",
                    question_en: "<p>30. Sangeet Kala Acharya Award is given by which organisation every year since 1993?</p>",
                    question_hi: "<p>30. वर्ष 1993 से प्रत्येक वर्ष किस संस्था द्वारा संगीत कला आचार्य पुरस्कार दिया जाता है?</p>",
                    options_en: ["<p>Madras Music Academy</p>", "<p>Calcutta School of Music</p>", 
                                "<p>Shankar Mahadevan Academy</p>", "<p>Subramaniam Academy of Performing Arts</p>"],
                    options_hi: ["<p>मद्रास संगीत अकादमी</p>", "<p>कलकत्ता संगीत विद्यालय</p>",
                                "<p>शंकर महादेवन अकादमी</p>", "<p>सुब्रमण्यम एकेडमी ऑफ परफॉर्मिंग आर्ट्स</p>"],
                    solution_en: "<p>30.(a) <strong>Madras Music Academy :</strong> It was established as an offshoot of the All India Congress Session in December 1927 and inaugurated on August 18, 1928, by Sir CP Ramaswami Aiyar at the YMCA Auditorium, Esplanade. Its mission was to set the standard for Carnatic music. In 1929, it initiated annual music conferences, leading to the December Music Festival of Madras, one of the world\'s largest cultural events. Sangita Kala Acharya : This award was instituted in 1993 and is given to those who have contributed by bringing several disciples to the concert platform.</p>",
                    solution_hi: "<p>30.(a) <strong>मद्रास संगीत अकादमी: </strong>इसकी स्थापना दिसंबर 1927 में अखिल भारतीय कांग्रेस अधिवेशन की एक शाखा के रूप में की गई थी एवं इसका उद्घाटन 18 अगस्त, 1928 को सर सी.पी. रामास्वामी अय्यर ने वाई.एम.सी.ए. ऑडिटोरियम, एस्प्लेनेड में किया था। इसका उद्देश्य कर्नाटक संगीत के लिए मानक स्थापित करना था। 1929 में, इसने वार्षिक संगीत सम्मेलनों की शुरुआत की, जिसके परिणामस्वरूप मद्रास का दिसंबर संगीत समारोह आयोजित किया गया, जो दुनिया के सबसे बड़े सांस्कृतिक कार्यक्रमों में से एक है। संगीत कला आचार्य: यह पुरस्कार 1993 में स्थापित किया गया था तथा यह उन लोगों को दिया जाता है, जिन्होंने कई शिष्यों को संगीत मंच पर लाकर योगदान दिया है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "31",
                    section: "18",
                    question_en: "<p>31. Which of the following is an example of a non-tax revenue source for the government?</p>",
                    question_hi: "<p>31. निम्नलिखित में से कौन-सा सरकार के लिए गैर-कर राजस्व स्रोत का उदाहरण है?</p>",
                    options_en: ["<p>Fees and fines</p>", "<p>Personal income tax</p>", 
                                "<p>Corporate income tax</p>", "<p>Value-added tax</p>"],
                    options_hi: ["<p>शुल्क और जुर्माना</p>", "<p>व्यक्तिगत आय कर</p>",
                                "<p>कॉर्पोरेट आय कर</p>", "<p>मूल्य वर्धित कर</p>"],
                    solution_en: "<p>31.(a) <strong>Fees and fines.</strong> Non-tax revenue refers to income generated by the government through sources other than taxes. Examples of non-tax revenue include : Fees - License fees, registration fees, tuition fees, etc. Fines - Penalties imposed for violating laws or regulations. Interest receipts - Interest earned on government investments or loans. Dividends - Earnings from government-owned companies. Grants - Receipts from international organizations or foreign governments.</p>",
                    solution_hi: "<p>31.(a)<strong> शुल्क और जुर्माना। </strong>गैर-कर राजस्व से तात्पर्य सरकार द्वारा करों के अलावा अन्य स्रोतों से अर्जित आय से है। गैर-कर राजस्व के उदाहरणों में शामिल हैं: शुल्क - लाइसेंस शुल्क, पंजीकरण शुल्क, ट्यूशन शुल्क, आदि। जुर्माना - कानून या नियमों का उल्लंघन करने पर लगाया गया दंड। ब्याज प्राप्तियां - सरकारी निवेश या ऋण पर अर्जित ब्याज। लाभांश - सरकारी स्वामित्व वाली कंपनियों से आय। अनुदान - अंतर्राष्ट्रीय संगठनों या विदेशी सरकारों से प्राप्तियां।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "32",
                    section: "18",
                    question_en: "<p>32. Sangeet Natak Akademy Awardee Sulochana Chavan was a famous ________ singer.</p>",
                    question_hi: "<p>32. संगीत नाटक अकादमी पुरस्कार से सम्मानित सुलोचना चव्हाण एक प्रसिद्ध ________ गायिका थीं।</p>",
                    options_en: ["<p>Giddha</p>", "<p>Jhumair</p>", 
                                "<p>Ghumar</p>", "<p>Lavani</p>"],
                    options_hi: ["<p>गिद्धा</p>", "<p>झूमर</p>",
                                "<p>घूमर</p>", "<p>लावणी</p>"],
                    solution_en: "<p>32.(d) <strong>Lavani. </strong>Sulochana Chavan : Her Awards - Padma Shri (in 2022), Lata Mangeshkar Award (in 2010), Sangeet Natak Akademi Award (in 2012). Other Lavani Singers : Bela Shende, Asha Bhosle, Usha Mangeshkar, Vaishali Samant.</p>",
                    solution_hi: "<p>32.(d) <strong>लावणी।</strong> सुलोचना चव्हाण: उनके पुरस्कार - पद्म श्री (2022 में), लता मंगेशकर पुरस्कार (2010 में), संगीत नाटक अकादमी पुरस्कार (2012 में)। अन्य लावणी गायिका: बेला शेंडे, आशा भोसले, उषा मंगेशकर, वैशाली सामंत।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "33",
                    section: "18",
                    question_en: "<p>33. Which of the following plants is used to cure cold and cough?</p>",
                    question_hi: "<p>33. निम्नलिखित में से किस पौधे का उपयोग सर्दी और खांसी को ठीक करने के लिए किया जाता है?</p>",
                    options_en: ["<p>Tulsi</p>", "<p>Babool</p>", 
                                "<p>Jamun</p>", "<p>Arjun</p>"],
                    options_hi: ["<p>तुलसी</p>", "<p>बबूल</p>",
                                "<p>जामुन</p>", "<p>अर्जुन</p>"],
                    solution_en: "<p>33.(a) <strong>Tulsi </strong>(Ocimum sanctum). It is an herb commonly used in Ayurvedic medicine to treat various ailments, including respiratory issues (bronchitis, asthma), fever, sore throat, and infections. Babool (Acacia nilotica) is utilized for skin conditions (wounds, eczema), digestive issues, and oral health. Jamun (Syzygium cumini) is beneficial for diabetes management, digestive issues, and possesses antioxidant properties. Arjun (Terminalia arjuna) is used to promote cardiovascular health, address heart conditions, and provide anti-inflammatory benefits.</p>",
                    solution_hi: "<p>33.(a) <strong>तुलसी</strong> (ओसीमम सैंक्टम)। यह एक जड़ी बूटी है जिसका उपयोग आयुर्वेदिक चिकित्सा में श्वसन संबंधी समस्याओं (ब्रोंकाइटिस, अस्थमा), बुखार, गले में खराश और संक्रमण सहित विभिन्न बीमारियों के इलाज के लिए किया जाता है। बबूल (अकेशिया निलोटिका) का उपयोग त्वचा की स्थितियों (घाव, एक्जिमा), पाचन संबंधी समस्याओं और मुख स्वास्थ्य के लिए किया जाता है। जामुन (सिजीजियम क्यूमिनी) मधुमेह प्रबंधन, पाचन संबंधी समस्याओं के लिए फायदेमंद है और इसमें एंटीऑक्सीडेंट गुण होते हैं। अर्जुन (टर्मिनलिया अर्जुन) का उपयोग हृदय स्वास्थ्य को बढ़ावा देने, हृदय की स्थितियों को ठीक करने व सूजन-रोधी लाभ प्रदान करने के लिए किया जाता है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "34",
                    section: "18",
                    question_en: "<p>34. Which is the colloquial term to describe the occurrence of pre-monsoon rains in India especially in parts of Kerala, Karnataka and Tamil Nadu?</p>",
                    question_hi: "<p>34. भारत में विशेष रूप से केरल, कर्नाटक और तमिलनाडु के कुछ हिस्सों में पूर्व-मानसून वर्षा की घटना का वर्णन करने के लिए आम बोलचाल में इस्तेमाल होने वाला शब्द कौन-सा है?</p>",
                    options_en: ["<p>Mango showers</p>", "<p>Kalbaisakhi</p>", 
                                "<p>Nor Westers</p>", "<p>Orange showers</p>"],
                    options_hi: ["<p>आम की वर्षा (मैंगो शॉवर्स)</p>", "<p>कालबैसाखी</p>",
                                "<p>नोर वेस्टर्स</p>", "<p>संतरे की वर्षा (ऑरेंज शॉवर्स)</p>"],
                    solution_en: "<p>34.(a) <strong>Mango showers. </strong>These are pre-monsoon rains that occur at the end of the summer season, helping the mangoes ripen; These are also called April rains. Tea rains : These are the pre-monsoon rains necessary for tea plantations, known as \"tea rains\" in Assam and \"Kalbaisakhi\" in West Bengal. Blossom shower, also known as cherry blossom or espresso shower, occurs in windy areas and signals the beginning of monsoon in India.</p>",
                    solution_hi: "<p>34.(a) <strong>आम की वर्षा (मैंगो शॉवर्स)।</strong> ये ग्रीष्म ऋतु के अंत में होने वाली पूर्व-मानसून वर्षा हैं, जो आमों को पकने में मदद करती हैं; इन्हें अप्रैल वर्षा भी कहा जाता है। टी रेन्स: ये चाय बागानों के लिए आवश्यक पूर्व-मानसून वर्षा हैं, जिन्हें असम में \"चाय वर्षा\" और पश्चिम बंगाल में \"कालबैसाखी\" के नाम से जाना जाता है। ब्लॉसम शावर, जिसे चेरी ब्लॉसम या एस्प्रेसो शावर के नाम से भी जाना जाता है, हवा वाले क्षेत्रों में होता है और भारत में मानसून की शुरुआत का संकेत देता है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "35",
                    section: "18",
                    question_en: "<p>35. The name &lsquo;Piyadassi&rsquo; is associated with whom among the following Mauryan kings?</p>",
                    question_hi: "<p>35. निम्नलिखित मौर्य राजाओं में से \'पियदस्सी\' नाम किससे संबंधित है?</p>",
                    options_en: ["<p>Bindusara</p>", "<p>Brihadratha</p>", 
                                "<p>Ashoka</p>", "<p>Chandragupta Maurya</p>"],
                    options_hi: ["<p>बिंदुसार</p>", "<p>बृहद्रथ</p>",
                                "<p>अशोक</p>", "<p>चन्द्रगुप्त मौर्य</p>"],
                    solution_en: "<p>35.(c) <strong>Ashoka. </strong>He belonged to the Maurya dynasty, which is documented in the Arthashastra by Chanakya and Indica by Megasthenes. He significantly contributed to the spread of Buddhism across Asia. The capital of his dynasty was Pataliputra, with provincial capitals at Taxila and Ujjain. James Prinsep was the first to decipher Ashoka\'s edicts, the earliest tangible evidence of Buddhism. \"Piyadasi\" is an honorific epithet that means \"He who regards others with kindness\", \"Humane\", or \"He who glances amiably\".</p>",
                    solution_hi: "<p>35.(c) <strong>अशोक। </strong>वह मौर्य वंश से संबंधित थे, जिसका विवरण चाणक्य द्वारा लिखित अर्थशास्त्र और मेगस्थनीज द्वारा लिखित इंडिका में मिलता है। उन्होंने पूरे एशिया में बौद्ध धर्म के प्रसार में महत्त्वपूर्ण योगदान दिया। उनके राजवंश की राजधानी पाटलिपुत्र थी, तथा तक्षशिला एवं उज्जैन में प्रांतीय राजधानियाँ थीं। जेम्स प्रिंसेप अशोक के शिलालेखों को समझने वाले पहले व्यक्ति थे, जो बौद्ध धर्म का सबसे प्रारंभिक ठोस प्रमाण हैं। \"पियदसि\" एक सम्मानजनक उपाधि है जिसका अर्थ है \"वह जो दूसरों के साथ दयालुता से व्यवहार करता है\", \"मानवीय\", या \"वह जो दयालुता से देखता है\"।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "36",
                    section: "18",
                    question_en: "<p>36. The festival of &lsquo;Rath Yatra&rsquo; of Puri in Odisha is dedicated to which deity?</p>",
                    question_hi: "<p>36. ओडिशा में पुरी की \'रथ यात्रा\' का त्योहार किस देवता को समर्पित है?</p>",
                    options_en: ["<p>Lord Jagannath</p>", "<p>Lord Ganesha</p>", 
                                "<p>Lord Shiva</p>", "<p>Lord Hanuman</p>"],
                    options_hi: ["<p>भगवान जगन्नाथ</p>", "<p>भगवान गणेश</p>",
                                "<p>भगवान शिव</p>", "<p>भगवान हनुमान</p>"],
                    solution_en: "<p>36.(a) <strong>Lord Jagannath. </strong>Rath Yatra Festival : Celebrated on the second day of the bright fortnight in the month of Ashadha, this festival features a public procession with three large chariots carrying the deities Jagannath, Balabhadra, Subhadra. The chariots are pulled towards the Gundicha Temple, located about 3 kilometers away, where the deities stay for seven days before being returned to the main temple. Other festivals in Odisha : Raja Parba, Magha Saptmi, Nuakhai, Chatar Jatra.</p>",
                    solution_hi: "<p>36.(a) <strong>भगवान जगन्नाथ।</strong> रथ यात्रा उत्सव: आषाढ़ माह के शुक्ल पक्ष की द्वितीया तिथि को मनाया जाने वाला यह उत्सव तीन बड़े रथों पर भगवान जगन्नाथ, बलभद्र, सुभद्रा को लेकर सार्वजनिक जुलूस निकाला जाता है। रथों को गुंडिचा मंदिर की ओर खींचा जाता है, जो लगभग 3 किलोमीटर दूर स्थित है, जहाँ देवता मुख्य मंदिर में वापस आने से पहले सात दिनों तक रहते हैं। ओडिशा के अन्य त्योहार: राजा परबा, माघ सप्तमी, नुआखाई, छतर यात्रा।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "37",
                    section: "18",
                    question_en: "<p>37. How many members are nominated by the President to the Rajya Sabha?</p>",
                    question_hi: "<p>37. राष्ट्रपति द्वारा राज्यसभा के लिए कितने सदस्यों को मनोनीत किया जाता है?</p>",
                    options_en: ["<p>12</p>", "<p>10</p>", 
                                "<p>20</p>", "<p>15</p>"],
                    options_hi: ["<p>12</p>", "<p>10</p>",
                                "<p>20</p>", "<p>15</p>"],
                    solution_en: "<p>37.(a) <strong>12 </strong>members are nominated to the Rajya Sabha by the President of India for six-year terms in recognition of their contributions to arts, literature, sciences, and social services. This right is granted to the President under the Fourth Schedule (Articles 4(1) and 80(2)) of the Constitution of India. Every two years, one-third of the members retire, and at the beginning of every third year, vacancies are filled through fresh elections and presidential nominations.</p>",
                    solution_hi: "<p>37.(a) <strong>12</strong> सदस्यों को राज्यसभा में कला, साहित्य, विज्ञान एवं सामाजिक सेवाओं में उनके योगदान के लिए भारत के राष्ट्रपति द्वारा छह वर्ष के कार्यकाल के लिए नामित किया जाता है। यह अधिकार राष्ट्रपति को भारत के संविधान की चौथी अनुसूची (अनुच्छेद 4(1) व 80(2)) के तहत प्रदान किया गया है। प्रत्येक दो वर्ष में एक तिहाई सदस्य सेवानिवृत्त हो जाते हैं तथा प्रत्येक तीसरे वर्ष की शुरुआत में नए चुनावों एवं राष्ट्रपति के नामांकन के माध्यम से रिक्तियों को भरा जाता है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "38",
                    section: "18",
                    question_en: "38. Which of the following is NOT the socio-economic objective of Industrial Policy Statement 1980? ",
                    question_hi: "38. निम्नलिखित में से कौन-सा औद्योगिक नीति वक्तव्य 1980 का सामाजिक-आर्थिक उद्देश्य नहीं है? ",
                    options_en: [" Higher employment generation ", " Consumer protection against high prices and bad quality ", 
                                " Promotion of export-oriented industries ", " Allowing the maximum foreign direct investment "],
                    options_hi: [" उच्च रोजगार सृजन ", " ऊंची कीमतों और खराब गुणवत्ता के विरुद्ध उपभोक्ता संरक्षण ",
                                " निर्यातोन्मुख उद्योगों को बढ़ावा देना ", " अधिकतम प्रत्यक्ष विदेशी निवेश की अनुमति"],
                    solution_en: "38.(d)  Industrial Policy Resolution of 1956 : It aimed to accelerate economic growth and promote industrialization for a socialist society, with comprehensive revisions made after the Constitution\'s adoption.  To address new challenges, it was further modified through statements in 1973, 1977, and 1980. Industrial Policy Statement of 1980 : It  aimed for higher employment generation, consumer protection, promotion of export-oriented industries, encouragement of small-scale and cottage industries, regional balance and decentralization, and self-reliance through import substitution.",
                    solution_hi: "38.(d) औद्योगिक नीति वक्तव्य 1956: इसका उद्देश्य समाजवादी समाज के लिए आर्थिक विकास में तेजी लाना और औद्योगीकरण को बढ़ावा देना था, जिसमें संविधान को अपनाने के बाद व्यापक संशोधन किए गए। नई चुनौतियों का समाधान करने के लिए, इसे 1973, 1977 व 1980 में वक्तव्यों के माध्यम से और संशोधित किया गया। औद्योगिक नीति वक्तव्य 1980 : इसका उद्देश्य उच्च रोजगार सृजन, उपभोक्ता संरक्षण, निर्यातोन्मुखी उद्योगों को बढ़ावा देना, लघु और कुटीर उद्योगों को प्रोत्साहन, क्षेत्रीय संतुलन और विकेंद्रीकरण और आयात प्रतिस्थापन के माध्यम से आत्मनिर्भरता प्राप्त करना था।",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "39",
                    section: "18",
                    question_en: "<p>39. In which of the following years will India host the World Badminton Championship?</p>",
                    question_hi: "<p>39. निम्नलिखित में से किस वर्ष में भारत विश्व बैडमिंटन चैम्पियनशिप की मेजबानी करेगा?</p>",
                    options_en: ["<p>2025</p>", "<p>2028</p>", 
                                "<p>2026</p>", "<p>2027</p>"],
                    options_hi: ["<p>2025 में</p>", "<p>2028 में</p>",
                                "<p>2026 में</p>", "<p>2027 में</p>"],
                    solution_en: "<p>39.(c) <strong>2026. </strong>BWF World Championships : It is also known as the World Badminton Championships, is a prestigious badminton tournament organized by the Badminton World Federation. It offers the highest ranking points, alongside the Summer Olympics badminton events introduced in 1992. First held in 1977, the world badminton championships were held every three years until 1983. After then, it became a biennial event till 2005. Since then, it was held every year except in an Olympic year (except 2021 edition).</p>",
                    solution_hi: "<p>39.(c)<strong> 2026 में। </strong>BWF विश्व चैंपियनशिप: इसे विश्व बैडमिंटन चैंपियनशिप के नाम से भी जाना जाता है, यह बैडमिंटन विश्व महासंघ द्वारा आयोजित एक प्रतिष्ठित बैडमिंटन टूर्नामेंट है। यह 1992 में शुरू किए गए ग्रीष्मकालीन ओलंपिक बैडमिंटन स्पर्धाओं के साथ-साथ उच्चतम रैंकिंग अंक प्रदान करता है। विश्व बैडमिंटन चैंपियनशिप पहली बार 1977 में आयोजित की गई थी, जिसे 1983 तक हर तीन साल में आयोजित किया जाता था। उसके बाद, यह 2005 तक एक द्विवार्षिक आयोजन बन गया। तब से, यह ओलंपिक वर्ष (2021 संस्करण को छोड़कर) को छोड़कर हर साल आयोजित किया जाता है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "40",
                    section: "18",
                    question_en: "<p>40. Grignard reagent is represented as:</p>",
                    question_hi: "<p>40. ग्रिग्नार्ड अभिकर्मक को निम्न रूप में दर्शाया जाता है:</p>",
                    options_en: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>CH</mi><mn>3</mn></msub><mo>-</mo><mi>Ca</mi><mo>-</mo><mi mathvariant=\"normal\">F</mi></math></p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>CH</mi><mn>3</mn></msub><mo>-</mo><mi>Be</mi><mo>-</mo><mi mathvariant=\"normal\">F</mi></math></p>", 
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>CH</mi><mn>3</mn></msub><mo>-</mo><mi>Mg</mi><mo>-</mo><mi>Cl</mi></math></p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">H</mi><mo>-</mo><mi>Mg</mi><mo>-</mo><mi mathvariant=\"normal\">H</mi></math></p>"],
                    options_hi: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>CH</mi><mn>3</mn></msub><mo>-</mo><mi>Ca</mi><mo>-</mo><mi mathvariant=\"normal\">F</mi></math></p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>CH</mi><mn>3</mn></msub><mo>-</mo><mi>Be</mi><mo>-</mo><mi mathvariant=\"normal\">F</mi></math></p>",
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>CH</mi><mn>3</mn></msub><mo>-</mo><mi>Mg</mi><mo>-</mo><mi>Cl</mi></math></p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">H</mi><mo>-</mo><mi>Mg</mi><mo>-</mo><mi mathvariant=\"normal\">H</mi></math></p>"],
                    solution_en: "<p>40.(c) <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi mathvariant=\"bold\">CH</mi><mn mathvariant=\"bold\">3</mn></msub><mo mathvariant=\"bold\">-</mo><mi mathvariant=\"bold\">Mg</mi><mo mathvariant=\"bold\">-</mo><mi mathvariant=\"bold\">Cl</mi></math><strong>.</strong> Grignard reagent : It is an organometallic compound with the general formula RMgX, where R is an alkyl or aryl group, Mg represents magnesium, and X is a halogen (Cl, Br, I). Grignard reagents are used in organic synthesis (addition, substitution); React with carbonyl compounds, acids, and esters; Useful for forming carbon-carbon bonds.</p>",
                    solution_hi: "<p>40.(c) <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi mathvariant=\"bold\">CH</mi><mn mathvariant=\"bold\">3</mn></msub><mo mathvariant=\"bold\">-</mo><mi mathvariant=\"bold\">Mg</mi><mo mathvariant=\"bold\">-</mo><mi mathvariant=\"bold\">Cl</mi></math><strong>.</strong> ग्रिग्नार्ड अभिकर्मक: यह एक कार्बनिक धातु यौगिक है जिसका सामान्य सूत्र RMgX है, जहाँ R एक एल्काइल या एरिल समूह है, Mg मैग्नीशियम को दर्शाता है, और X एक हैलोजन (Cl, Br, I) है। ग्रिग्नार्ड अभिकर्मकों का उपयोग कार्बनिक संश्लेषण (योग, प्रतिस्थापन) में किया जाता है; कार्बोनिल यौगिकों, अम्लों तथा एस्टर के साथ अभिक्रिया करते हैं; कार्बन-कार्बन बंध बनाने के लिए उपयोगी होते हैं।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "41",
                    section: "18",
                    question_en: "<p>41. Which of the following crops is NOT grown during the Zaid season in India?</p>",
                    question_hi: "<p>41. निम्नलिखित में से कौन-सी फसल भारत में जायद के मौसम में नहीं उगाई जाती है?</p>",
                    options_en: ["<p>Rice</p>", "<p>Cucumber</p>", 
                                "<p>Watermelon</p>", "<p>Muskmelon</p>"],
                    options_hi: ["<p>चावल</p>", "<p>खीरा</p>",
                                "<p>तरबूज</p>", "<p>खरबूजा</p>"],
                    solution_en: "<p>41.(a) <strong>Rice.</strong> Kharif Crops, also known as monsoon crops, are sown at the beginning of the rainy season. Examples include rice, maize, bajra, ragi, sorghum, soybean, groundnut, and cotton. Zaid Crops : Grown in the short season between Kharif and Rabi, examples include Pumpkin, Cucumber, Watermelon, and Bitter gourd. Rabi Crops : Sown at the end of the monsoon or beginning of winter, they are known as winter crops. Examples include Wheat, Mustard, Pulses, and Barley.</p>",
                    solution_hi: "<p>41.(a) <strong>चावल।</strong> खरीफ फसलें: इन्हें मानसून फसलें भी कहा जाता है,और इन्हें वर्षा ऋतु की शुरुआत में बोया जाता है। उदाहरणों में चावल, मक्का, बाजरा, रागी, ज्वार, सोयाबीन, मूंगफली और कपास शामिल हैं। जायद फसलें: खरीफ और रबी के बीच के छोटे मौसम में उगाई जाती हैं, उदाहरणों में कद्दू, खीरा, तरबूज और करेला शामिल हैं। रबी फसलें: इन्हें मानसून के अंत या सर्दियों की शुरुआत में बोया जाता है, इन्हें सर्दियों की फसल के रूप में जाना जाता है। उदाहरणों में गेहूं, सरसों, दालें और जौ शामिल हैं।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "42",
                    section: "18",
                    question_en: "<p>42. Who was the player of the match in the semi-final of the T20 Women\'s World Cup 2023 played between India and Australia?</p>",
                    question_hi: "<p>42. भारत और ऑस्ट्रेलिया के बीच खेले गए T20 महिला विश्व कप 2023 के सेमीफाइनल में प्लेयर ऑफ द मैच कौन रही थी?</p>",
                    options_en: ["<p>Deepti Sharma</p>", "<p>Ashleigh Gardner</p>", 
                                "<p>Meg Lanning</p>", "<p>Harmanpreet kaur</p>"],
                    options_hi: ["<p>दीप्ति शर्मा (Deepti Sharma)</p>", "<p>एशले गार्डनर (Ashleigh Gardner)</p>",
                                "<p>मेग लैनिंग (Meg Lanning)</p>", "<p>हरमनप्रीत कौर (Harmanpreet kaur)</p>"],
                    solution_en: "<p>42.(b)<strong> Ashleigh Gardner.</strong> The 8th ICC Women\'s T20 World Cup was held in South Africa, concluding in Cape Town. In the semi-finals, Australia defeated India by 5 runs. Australia then claimed their sixth overall and third consecutive title by defeating South Africa by 19 runs in the final.</p>",
                    solution_hi: "<p>42.(b) <strong>एशले गार्डनर।</strong> 8वां ICC महिला T20 वर्ल्ड कप दक्षिण अफ्रीका में आयोजित किया गया, जिसका समापन केपटाउन में हुआ। सेमीफाइनल में ऑस्ट्रेलिया ने भारत को 5 रन से हराया। इसके बाद ऑस्ट्रेलिया ने फाइनल में दक्षिण अफ्रीका को 19 रन से हराकर अपना छठा और लगातार तीसरा खिताब जीता।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "43",
                    section: "18",
                    question_en: "<p>43. As per article 123, the ordinance making power is the most important legislative power of ____________.</p>",
                    question_hi: "<p>43. अनुच्छेद 123 के अनुसार, अध्यादेश बनाने की शक्ति ______ की सबसे महत्वपूर्ण विधाय शक्ति है।</p>",
                    options_en: ["<p>President</p>", "<p>Rajya Sabha</p>", 
                                "<p>Legislative council</p>", "<p>Chief Justice of India</p>"],
                    options_hi: ["<p>राष्ट्रपति</p>", "<p>राज्य सभा</p>",
                                "<p>विधान परिषद</p>", "<p>भारत के मुख्य न्यायाधीश</p>"],
                    solution_en: "<p>43.(a)<strong> President. </strong>Article 123 of the Indian Constitution allows the President to issue ordinances when Parliament is not in session, with approval required within six weeks of reassembly. The maximum life of an ordinance is six months and six weeks. Fakhruddin Ali Ahmed issued the most ordinances in India. Article 213 : Power of Governor to promulgate Ordinances during recess of Legislature.</p>",
                    solution_hi: "<p>43.(a) <strong>राष्ट्रपति। </strong>भारतीय संविधान का अनुच्छेद 123 राष्ट्रपति को, संसद सत्र में न होने पर अध्यादेश जारी करने का अधिकार देता है, जिसे पुनः सत्रावसान के छह सप्ताह के भीतर अनुमोदन प्राप्त करना आवश्यक होता है। अध्यादेश की अधिकतम अवधि छह महीने और छह सप्ताह होती है। फखरुद्दीन अली अहमद ने भारत में सबसे अधिक अध्यादेश जारी किए थे। अनुच्छेद 213: विधानमंडल के अवकाश के दौरान अध्यादेश जारी करने की राज्यपाल की शक्ति।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "44",
                    section: "18",
                    question_en: "<p>44. In which year was the Gandhi-Irwin Pact signed ?</p>",
                    question_hi: "<p>44. गांधी-इरविन समझौते पर किस वर्ष हस्ताक्षर किए गए थे?</p>",
                    options_en: ["<p>1935</p>", "<p>1929</p>", 
                                "<p>1931</p>", "<p>1941</p>"],
                    options_hi: ["<p>1935</p>", "<p>1929</p>",
                                "<p>1931</p>", "<p>1941</p>"],
                    solution_en: "<p>44.(c) <strong>1931.</strong> The Gandhi-Irwin Pact was signed on March 5, 1931, between Mahatma Gandhi and Lord Irwin, the Viceroy of India (1926-1931). The pact ended the Civil Disobedience Movement, initiated by Gandhi with the Salt March, and was signed before the Second Round Table Conference in London.</p>",
                    solution_hi: "<p>44.(c) <strong>1931.</strong> गांधी-इरविन समझौता 5 मार्च, 1931 को महात्मा गांधी और भारत के वायसराय लॉर्ड इरविन (1926-1931) के बीच हुआ था। इस समझौते ने सविनय अवज्ञा आंदोलन को समाप्त कर दिया, जिसकी शुरुआत गांधीजी ने नमक मार्च के साथ की थी, तथा इस पर लंदन में द्वितीय गोलमेज सम्मेलन से पहले हस्ताक्षर किये गये थे।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "45",
                    section: "18",
                    question_en: "<p>45. Which of the following schemes was launched for the welfare of the unorganised sector workers and labourers on 26 August 2021 ?</p>",
                    question_hi: "<p>45. 26 अगस्त 2021 को असंगठित क्षेत्र के कामगारों और मजदूरों के कल्याण के लिए निम्नलिखित में से कौन-सी योजना शुरू की गई थी?</p>",
                    options_en: ["<p>e-Shram Portal</p>", "<p>PM Mentoring Yuva Scheme</p>", 
                                "<p>PM Daksh Yojana</p>", "<p>Gram Ujala Scheme</p>"],
                    options_hi: ["<p>ई-श्रम पोर्टल</p>", "<p>पीएम मेंटरिंग युवा स्कीम</p>",
                                "<p>पीएम दक्ष योजना</p>", "<p>ग्राम उजाला योजना</p>"],
                    solution_en: "<p>45.(a) <strong>e-Shram Portal :</strong> An initiative by the Ministry of Labour and Employment to create a National Database of Unorganized Workers (NDUW) based on Aadhaar. PM-Daksh Portal and Mobile Application : Launched on August 7, 2021, by Dr. Virendra Kumar to provide access to skill development programs for targeted beneficiaries. Unnat Jyoti by Affordable LEDs for All (UJALA) : Launched in 2015, this scheme aims to replace 77 crore incandescent lamps with LED bulbs, promoting efficient lighting, reducing electricity bills, and preserving the environment.</p>",
                    solution_hi: "<p>45.(a) <strong>ई-श्रम पोर्टल :</strong> श्रम और रोजगार मंत्रालय द्वारा आधार (Aadhaar) पर आधारित असंगठित श्रमिकों का राष्ट्रीय डेटाबेस (NDUW) बनाने की एक पहल है। पीएम-दक्ष पोर्टल और मोबाइल एप्लिकेशन : लक्षित लाभार्थियों को कौशल विकास कार्यक्रमों तक पहुँच प्रदान करने के लिए 7 अगस्त, 2021 को डॉ. वीरेंद्र कुमार द्वारा शुभारंभ किया गया। उन्नत ज्योति अफोर्डेबल LED फॉर ऑल (UJALA): 2015 में शुरू की गई इस योजना का उद्देश्य 77 करोड़ तापदीप्त लैंपों को LED बल्बों से बदलना, कुशल प्रकाश व्यवस्था को बढ़ावा देना, बिजली के बिलों को कम करना और पर्यावरण को संरक्षित करना है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "46",
                    section: "18",
                    question_en: "<p>46. What is the SI unit of current?</p>",
                    question_hi: "<p>46. धारा का SI मात्रक क्या है?</p>",
                    options_en: ["<p>Ohm</p>", "<p>Ampere</p>", 
                                "<p>Metre</p>", "<p>Volt</p>"],
                    options_hi: ["<p>ओम</p>", "<p>एम्पियर</p>",
                                "<p>मीटर</p>", "<p>वोल्ट</p>"],
                    solution_en: "<p>46.(b)<strong> Ampere. </strong>SI unit : Length - Meter (m), Mass - Kilogram (kg), Time - Second (s), Thermodynamic temperature - Kelvin (K), Amount of substance - Mole (mol), Luminous intensity - Candela (cd).</p>",
                    solution_hi: "<p>46.(b) <strong>एम्पियर। </strong>SI मात्रक: लंबाई - मीटर (m), द्रव्यमान - किलोग्राम (kg), समय - सेकंड (s), ऊष्मागतिक तापमान - केल्विन (K), पदार्थ की मात्रा - मोल (mol), ज्योति तीव्रता - कैंडेला (cd)।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "47",
                    section: "18",
                    question_en: "<p>47. Name a common electrolyte disorder that occurs when the amount of sodium in your blood becomes abnormally low.</p>",
                    question_hi: "<p>47. एक सामान्य इलेक्ट्रो लाइट विकार का नाम बताइए जो तब घटित होता है जब आपके रक्त में सोडियम की मात्रा असामान्य रूप से कम हो जाती है।</p>",
                    options_en: ["<p>Hyperkalemia</p>", "<p>Hypokalemia</p>", 
                                "<p>Hyponatremia</p>", "<p>Hypernatremia</p>"],
                    options_hi: ["<p>अतिपोटैशियम रक्तता (Hyperkalemia)</p>", "<p>अवपोटैशियम रक्तता (hypokalemia)</p>",
                                "<p>अवसोडियम रक्तता (Hyponatremia)</p>", "<p>अतिसोडियम रक्तता (hypernatremia) </p>"],
                    solution_en: "<p>47.(c) <strong>Hyponatremia: </strong>It is a common electrolyte disorder characterized by an abnormally low concentration of sodium ions (Na+) in the blood, typically less than 135 mmol/L. Hyperkalemia : Elevated potassium levels in the blood. Hypokalemia: Low potassium levels in the blood. Hypernatremia: Elevated sodium levels in the blood.</p>",
                    solution_hi: "<p>47.(c) <strong>अवसोडियम रक्तता</strong> (Hyponatremia) : यह एक सामान्य इलेक्ट्रोलाइट विकार है, जिसमें रक्त में सोडियम आयनों (Na+) की सांद्रता असामान्य रूप से कम होती है, जो आमतौर पर 135 mmol/L से कम होती है। अतिपोटैशियम रक्तता (Hyperkalemia) : रक्त में पोटेशियम का स्तर बढ़ जाना। अवपोटैशियम रक्तता (hypokalemia) : रक्त में पोटेशियम का कम स्तर। अतिसोडियम रक्तता (hypernatremia) : रक्त में सोडियम का स्तर बढ़ जाना।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "48",
                    section: "18",
                    question_en: "<p>48. Dr. B. R. Ambedkar described Directive Principles of State Policy as ________of the Indian constitution.&nbsp;</p>",
                    question_hi: "<p>48. डॉ. बी.आर. अंबेडकर ने भारतीय संविधान के _____के रूप में राज्य के नीति निदेशक तत्&zwj;वों का वर्णन किया है।</p>",
                    options_en: ["<p>soul of the state</p>", "<p>identity card of the constitution</p>", 
                                "<p>The most precious part of the constitution</p>", "<p>a novel feature of the constitution</p>"],
                    options_hi: ["<p>राज्य की आत्मा</p>", "<p>संविधान के पहचान-पत्र</p>",
                                "<p>संविधान के सर्वाधिक बहुमूल्य भाग</p>", "<p>संविधान की एक नवीन विशेषता</p>"],
                    solution_en: "<p>48.(d) <strong>a novel feature of the Indian constitution.</strong> The Directive Principles of State Policy (DPSP) are outlined in Part IV (Articles 36-51) of the Indian Constitution and are inspired by the Constitution of Ireland. They represent the ideals that the State should consider while formulating policies and enacting laws to promote social and economic justice.</p>",
                    solution_hi: "<p>48.(d) <strong>संविधान की एक नवीन विशेषता।</strong> राज्य के नीति निर्देशक सिद्धांत (DPSP) भारतीय संविधान के भाग IV (अनुच्छेद 36-51) में उल्लिखित हैं और आयरलैंड के संविधान से प्रेरित हैं। वे उन आदर्शों का प्रतिनिधित्व करते हैं, जिन्हें राज्य को सामाजिक एवं आर्थिक न्याय को बढ़ावा देने के लिए नीतियां बनाते व कानून का निर्माण करते समय ध्यान में रखना चाहिए।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "49",
                    section: "18",
                    question_en: "<p>49. Tera Tali folk dance is primarily associated with which of the following states?</p>",
                    question_hi: "<p>49. तेरह ताली लोक नृत्य मुख्य रूप से निम्नलिखित में से किस राज्य से संबंधित है?</p>",
                    options_en: ["<p>Rajasthan</p>", "<p>Assam</p>", 
                                "<p>Madhya Pradesh</p>", "<p>Haryana</p>"],
                    options_hi: ["<p>राजस्थान</p>", "<p>असम</p>",
                                "<p>मध्य प्रदेश</p>", "<p>हरियाणा</p>"],
                    solution_en: "<p>49.(a) <strong>Rajasthan. </strong>Terah Tali Dance : This dance is performed in praise of the folk god Ramdevji, primarily by the Kamar tribe. During the performance, women sit on the ground and use thirteen cymbals on various body parts, synchronizing the swinging Manjeeras with the rhythm of the background music. Rajasthani folk dances : Chari, Ghoomar, Kalbelia, Kathputli dance and Gair folk dance.</p>",
                    solution_hi: "<p>49.(a) <strong>राजस्थान।</strong> तेरह ताली नृत्य: यह नृत्य मुख्य रूप से कमार जनजाति द्वारा लोक देवता रामदेवजी की स्तुति में किया जाता है। प्रदर्शन के दौरान, महिलाएँ ज़मीन पर बैठती हैं और शरीर के विभिन्न अंगों पर तेरह झांझों का उपयोग करती हैं, जो झूलते हुए मंजीरों को पृष्ठभूमि संगीत की लय के साथ तालबद्ध करती हैं। राजस्थानी लोक नृत्य: चरी, घूमर, कालबेलिया, कठपुतली नृत्य और गैर लोक नृत्य।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "50",
                    section: "17",
                    question_en: "<p>50. Who among the following was the Vakil under the reign of Mughal Emperor Akbar, who received the title of Khan-i-Khanan ?</p>",
                    question_hi: "<p>50. निम्नलिखित में से कौन मुगल सम्राट अकबर के शासनकाल में वकील था, जिसे खान-ए-खाना की उपाधि प्राप्त थी?</p>",
                    options_en: ["<p>Mulla do-Piyaza</p>", "<p>Birbal</p>", 
                                "<p>Raja Todar Mal</p>", "<p>Bairam Khan</p>"],
                    options_hi: ["<p>मुल्ला दो-प्याजा</p>", "<p>बीरबल</p>",
                                "<p>राजा टोडरमल</p>", "<p>बैरम खान</p>"],
                    solution_en: "<p>50.(d) <strong>Bairam Khan. </strong>A prominent Mughal noble and military commander who served as Akbar\'s regent and chief minister during the early years of his reign. Akbar : The third Mughal emperor (1556-1605) and son of Humayun, ascended the throne at 13 and introduced the Mansabdari system to organize the empire\'s military and administration. Mulla do-Piyaza : A scholar and advisor. Birbal : A poet, musician, and close friend of Akbar. Raja Todar Mal : The finance minister.</p>",
                    solution_hi: "<p>50.(d) <strong>बैरम खान</strong> प्रमुख मुग़ल सरदार और सैन्य कमांडर थे, जिन्होंने अकबर के प्रारंभिक शासन के दौरान उनके संरक्षक और प्रधान मंत्री के रूप में सेवा की। अकबर: तीसरे मुगल सम्राट (1556-1605) एवं हुमायूं का पुत्र, 13 वर्ष की आयु में सिंहासन पर आसीन हुआ और साम्राज्य की सेना एवं प्रशासन को व्यवस्थित करने के लिए मनसबदारी प्रणाली की शुरुआत की। मुल्ला दो-प्याजा : एक विद्वान व सलाहकार। बीरबल: एक कवि, संगीतकार और अकबर के घनिष्ठ मित्र । राजा टोडरमल: वित्त मंत्री।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "51",
                    section: "17",
                    question_en: "<p>51. A boatman rows to a place 30 km distant and back in 14 hours. He finds that he can row 10 km with the stream at the same time as 4 km against the stream. Find the speed (in km/h) of the stream.</p>",
                    question_hi: "<p>51. एक नाविक को नाव से 30 km की दूरी तक जाने और वापस आने में 14 घंटे का समय लगता है। वह पाता है कि वह जितने समय में धारा की दिशा में 10 km नाव चला सकता है, उतने ही समय में धारा के विपरीत दिशा में 4 km नाव चला सकता है। धारा की चाल (km/h में) ज्ञात कीजिए।</p>",
                    options_en: ["<p><math display=\"inline\"><mfrac><mrow><mn>9</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>9</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math></p>", 
                                "<p><math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>9</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>9</mn></mrow></mfrac></math></p>"],
                    options_hi: ["<p><math display=\"inline\"><mfrac><mrow><mn>9</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>9</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math></p>",
                                "<p><math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>9</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>9</mn></mrow></mfrac></math></p>"],
                    solution_en: "<p>51.(b) Let the speed of boat in still water = x km/h<br>Speed of stream = y km/h<br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>10</mn><mrow><mi mathvariant=\"normal\">x</mi><mo>+</mo><mi mathvariant=\"normal\">y</mi></mrow></mfrac><mo>=</mo><mfrac><mn>4</mn><mrow><mi mathvariant=\"normal\">x</mi><mo>-</mo><mi mathvariant=\"normal\">y</mi></mrow></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi mathvariant=\"normal\">x</mi><mo>-</mo><mi mathvariant=\"normal\">y</mi></mrow><mrow><mi mathvariant=\"normal\">x</mi><mo>+</mo><mi mathvariant=\"normal\">y</mi></mrow></mfrac><mo>=</mo><mfrac><mrow><mn>2</mn><mi mathvariant=\"normal\">k</mi></mrow><mrow><mn>5</mn><mi mathvariant=\"normal\">k</mi></mrow></mfrac></math><br>x = 3.5k and y = 1.5k<br><strong id=\"docs-internal-guid-973b6780-7fff-507f-92b5-b271ad76fcd3\"></strong>According to question ,&nbsp;<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>30</mn><mrow><mi mathvariant=\"normal\">x</mi><mi mathvariant=\"normal\">&#160;</mi><mo>+</mo><mi mathvariant=\"normal\">&#160;</mi><mi mathvariant=\"normal\">y</mi><mi mathvariant=\"normal\">&#160;</mi></mrow></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>30</mn><mrow><mi mathvariant=\"normal\">x</mi><mo>-</mo><mi mathvariant=\"normal\">y</mi></mrow></mfrac></math> = 14<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>30</mn><mrow><mi mathvariant=\"normal\">&#160;</mi><mn>5</mn><mi mathvariant=\"normal\">k</mi></mrow></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>30</mn><mrow><mn>2</mn><mi mathvariant=\"normal\">k</mi><mo>&#160;</mo></mrow></mfrac></math> = 14<br><math display=\"inline\"><mo>&#8658;</mo></math> k = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>21</mn><mn>14</mn></mfrac></math><br>&there4; Speed of stream = 1.5k = 1.5 &times; <math display=\"inline\"><mfrac><mrow><mn>21</mn></mrow><mrow><mn>14</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>9</mn><mn>4</mn></mfrac></math> km/h</p>",
                    solution_hi: "<p>51.(b) माना , शांत पानी में नाव की गति = x&nbsp;किमी/घंटा<br>धारा की गति =y किमी/घंटा<br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>10</mn><mrow><mi mathvariant=\"normal\">x</mi><mo>+</mo><mi mathvariant=\"normal\">y</mi></mrow></mfrac><mo>=</mo><mfrac><mn>4</mn><mrow><mi mathvariant=\"normal\">x</mi><mo>-</mo><mi mathvariant=\"normal\">y</mi></mrow></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi mathvariant=\"normal\">x</mi><mo>-</mo><mi mathvariant=\"normal\">y</mi></mrow><mrow><mi mathvariant=\"normal\">x</mi><mo>+</mo><mi mathvariant=\"normal\">y</mi></mrow></mfrac><mo>=</mo><mfrac><mrow><mn>2</mn><mi mathvariant=\"normal\">k</mi></mrow><mrow><mn>5</mn><mi mathvariant=\"normal\">k</mi></mrow></mfrac></math><br>x = 3.5k और y = 1.5k<br>प्रश्न के अनुसार , <br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>30</mn><mrow><mi mathvariant=\"normal\">x</mi><mi mathvariant=\"normal\">&#160;</mi><mo>+</mo><mi mathvariant=\"normal\">&#160;</mi><mi mathvariant=\"normal\">y</mi><mi mathvariant=\"normal\">&#160;</mi></mrow></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>30</mn><mrow><mi mathvariant=\"normal\">x</mi><mo>-</mo><mi mathvariant=\"normal\">y</mi></mrow></mfrac></math> = 14<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>30</mn><mrow><mi mathvariant=\"normal\">&#160;</mi><mn>5</mn><mi mathvariant=\"normal\">k</mi></mrow></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>30</mn><mrow><mn>2</mn><mi mathvariant=\"normal\">k</mi><mo>&#160;</mo></mrow></mfrac></math> = 14<br><math display=\"inline\"><mo>&#8658;</mo></math> k = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>21</mn><mn>14</mn></mfrac></math><br>&there4; धारा की गति = 1.5k = 1.5 &times; <math display=\"inline\"><mfrac><mrow><mn>21</mn></mrow><mrow><mn>14</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>9</mn><mn>4</mn></mfrac></math> किमी/घंटा</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "52",
                    section: "17",
                    question_en: "<p>52. Two circles, centred at P and Q intersect at two points C and D. AB is tangent to the two circles at A and B. If &ang;ADB = 68&deg;, then &ang;ACB = __________.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730952541820.png\" alt=\"rId4\" width=\"276\" height=\"171\"></p>",
                    question_hi: "<p>52. P और Q केंद्र वाले दो वृत दो बिंदुओं C और D पर प्रतिच्छेदित करते हैं। AB, बिन्दु A और बिन्दु B पर खींची गई दोनों वृत्तों की स्पर्श रेखा है। यदि &ang;ADB = 68&deg; है, तो &ang;ACB = ______होगा |<br><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730952541820.png\" alt=\"rId4\" width=\"276\" height=\"171\"></p>",
                    options_en: ["<p>132&deg;</p>", "<p>112&deg;</p>", 
                                "<p>124&deg;</p>", "<p>102&deg;</p>"],
                    options_hi: ["<p>132&deg;</p>", "<p>112&deg;</p>",
                                "<p>124&deg;</p>", "<p>102&deg;</p>"],
                    solution_en: "<p>52.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730952541972.png\" alt=\"rId5\" width=\"265\" height=\"170\"><br>In <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#916;ACD</mi></math><br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">&#8736;</mi><mi>BAC</mi></math> = &ang;ADC &hellip; (i)&hellip; (alternate segment theorem)<br>In <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#916;BCD</mi></math><br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">&#8736;</mi><mi>ABC</mi></math> = &ang;BDC &hellip; (ii)&hellip; (alternate segment theorem)<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">&#8736;</mi><mi>BAC</mi></math> + &ang;ABC = 180&deg; - &ang;ACB &hellip; (iii)<br>On adding (i) and (ii) we get,<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">&#8736;</mi><mi>BAC</mi></math> + &ang;ABC = &ang;ADC + &ang;BDC <br><math display=\"inline\"><msup><mrow><mn>180</mn></mrow><mrow><mo>&#176;</mo></mrow></msup></math> - &ang;ACB = 68&deg; &hellip; [from(iii)]<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">&#8736;</mi><mi>ACB</mi></math> = 180&deg; - 68&deg; = 112&deg;</p>",
                    solution_hi: "<p>52.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730952541972.png\" alt=\"rId5\" width=\"265\" height=\"170\"><br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#916;ACD</mi></math> में<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">&#8736;</mi><mi>BAC</mi></math> = &ang;ADC &hellip; (i)&hellip; (वैकल्पिक खंड प्रमेय)<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#916;BCD</mi></math> में<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">&#8736;</mi><mi>ABC</mi></math> = &ang;BDC &hellip; (ii)&hellip; (वैकल्पिक खंड प्रमेय)<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">&#8736;</mi><mi>BAC</mi></math> + &ang;ABC = 180&deg; - &ang;ACB &hellip; (iii)<br>(i) और (ii) जोड़ने पर हमें मिलता है,<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">&#8736;</mi><mi>BAC</mi></math> + &ang;ABC = &ang;ADC + &ang;BDC <br><math display=\"inline\"><msup><mrow><mn>180</mn></mrow><mrow><mo>&#176;</mo></mrow></msup></math> - &ang;ACB = 68&deg; &hellip;[(iii) से]<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">&#8736;</mi><mi>ACB</mi></math> = 180&deg; - 68&deg; = 112&deg;</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "53",
                    section: "17",
                    question_en: "<p>53. Find the remainder: (17<sup>13</sup> &minus; 21) &divide; 18</p>",
                    question_hi: "<p>53. (17<sup>13</sup> &minus; 21) &divide; 18 का शेषफल ज्ञात करें।</p>",
                    options_en: ["<p>15</p>", "<p>17</p>", 
                                "<p>14</p>", "<p>21</p>"],
                    options_hi: ["<p>15</p>", "<p>17</p>",
                                "<p>14</p>", "<p>21</p>"],
                    solution_en: "<p>53.(c) (17<sup>13</sup> &minus; 21) &divide; 18<br>Concept : Rem {<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mrow><mo>(</mo><mi mathvariant=\"normal\">a</mi><mo>-</mo><mn>1</mn><mo>)</mo></mrow><mi mathvariant=\"normal\">n</mi></msup><mi mathvariant=\"normal\">a</mi></mfrac></math>} &rarr; (a -1) or -1 ; when n is odd <br><math display=\"inline\"><mfrac><mrow><msup><mrow><mo>(</mo><mn>18</mn><mi>&#160;</mi><mo>-</mo><mn>1</mn><mo>)</mo></mrow><mrow><mn>13</mn></mrow></msup></mrow><mrow><mn>18</mn></mrow></mfrac></math> - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>21</mn><mn>18</mn></mfrac></math><br>17 - 3 = 14 (rem)</p>",
                    solution_hi: "<p>53.(c) (17<sup>13</sup> &minus; 21) &divide; 18<br>अवधारणा : शेषफल {<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mrow><mo>(</mo><mi mathvariant=\"normal\">a</mi><mo>-</mo><mn>1</mn><mo>)</mo></mrow><mi mathvariant=\"normal\">n</mi></msup><mi mathvariant=\"normal\">a</mi></mfrac></math>} &rarr; (a -1) or -1 ; जब n = विषम <br><math display=\"inline\"><mfrac><mrow><msup><mrow><mo>(</mo><mn>18</mn><mi>&#160;</mi><mo>-</mo><mn>1</mn><mo>)</mo></mrow><mrow><mn>13</mn></mrow></msup></mrow><mrow><mn>18</mn></mrow></mfrac></math> - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>21</mn><mn>18</mn></mfrac></math> <br>17 - 3 = 14 (शेषफल)</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "54",
                    section: "17",
                    question_en: "54. Simplify the following. <br />16 ÷ 2− 7 × 15 ÷ 3 + 5 × 5 + 4 × 3 − 10",
                    question_hi: "54. निम्नलिखित का सरलीकरण करें।<br />16 ÷ 2− 7 × 15 ÷ 3 + 5 × 5 + 4 × 3 − 10",
                    options_en: [" 1", " 2 ", 
                                " 0", " -1"],
                    options_hi: [" 1", " 2 ",
                                " 0", " -1"],
                    solution_en: "54.(c) 16 ÷ 2− 7 × 15 ÷ 3 + 5 × 5 + 4 × 3 − 10<br />8 - 35 + 25 + 2<br />35 - 35 = 0",
                    solution_hi: "54.(c) 16 ÷ 2− 7 × 15 ÷ 3 + 5 × 5 + 4 × 3 − 10<br />8 - 35 + 25 + 2<br />35 - 35 = 0",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "55",
                    section: "17",
                    question_en: "<p>55. Study the given graph carefully and answer the question that follows.<br>The given graph shows the number of male and female volunteers for helping handicapped people from four different districts A, B, C and D.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730952542090.png\" alt=\"rId6\" width=\"380\" height=\"297\"> <br>Find the number of volunteers altogether.</p>",
                    question_hi: "<p>55. दिए गए ग्राफ़ का ध्यानपूर्वक अध्ययन करें और उसके बाद पूछे गए प्रश्न का उत्तर दें।<br>दिया गया ग्राफ़ विकलांग लोगों की मदद के लिए चार विभिन्न जिलों A, B, C और D से पुरुष और महिला स्वयंसेवकों की संख्या को दर्शाता है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730952542253.png\" alt=\"rId7\" width=\"367\" height=\"289\"> <br>स्वयंसेवकों की कुल संख्या ज्ञात कीजिए।</p>",
                    options_en: ["<p>230</p>", "<p>250</p>", 
                                "<p>240</p>", "<p>260</p>"],
                    options_hi: ["<p>230</p>", "<p>250</p>",
                                "<p>240</p>", "<p>260</p>"],
                    solution_en: "<p>55.(b) Number of volunteers altogether = 40+ 30 + 25 + 35 + 20 + 30 + 45 + 25 = 250</p>",
                    solution_hi: "<p>55.(b) स्वयंसेवकों की कुल संख्या = 40+ 30 + 25 + 35 + 20 + 30 + 45 + 25 = 250</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "56",
                    section: "17",
                    question_en: "<p>56. If the curved surface area of a cylinder is 880 cm&sup2; , then the product of its height and radius is:</p>",
                    question_hi: "<p>56. यदि एक बेलन का वक्र पृष्ठीय क्षेत्रफल 880 cm&sup2; है, तो उसकी ऊँचाई और त्रिज्या का गुणनफल क्&zwj;या होगा?</p>",
                    options_en: ["<p>90 cm&sup2;</p>", "<p>120 cm&sup2;</p>", 
                                "<p>220 cm&sup2;</p>", "<p>140 cm&sup2;</p>"],
                    options_hi: ["<p>90 cm&sup2;</p>", "<p>120 cm&sup2;</p>",
                                "<p>220 cm&sup2;</p>", "<p>140 cm&sup2;</p>"],
                    solution_en: "<p>56.(d) Given , curved surface area of a cylinder = 880 cm&sup2;<br>&there4; 2<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">&#960;</mi></math>rh = 880<br>rh = <math display=\"inline\"><mfrac><mrow><mn>880</mn><mi>&#160;</mi><mo>&#215;</mo><mn>7</mn></mrow><mrow><mn>2</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>22</mn></mrow></mfrac></math> = 20 &times; 7 = 140 cm&sup2;</p>",
                    solution_hi: "<p>56.(d) दिया गया है, बेलन का वक्र पृष्ठीय क्षेत्रफल = 880 cm&sup2;<br>&there4; 2<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">&#960;</mi></math>rh = 880<br>rh = <math display=\"inline\"><mfrac><mrow><mn>880</mn><mi>&#160;</mi><mo>&#215;</mo><mn>7</mn></mrow><mrow><mn>2</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>22</mn></mrow></mfrac></math> = 20 &times; 7 = 140 cm&sup2;</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "57",
                    section: "17",
                    question_en: "<p>57. The angle turned by minutes hand in 25 minutes in a clock is:</p>",
                    question_hi: "<p>57. किसी घड़ी में मिनट की सुई द्वारा 25 मिनट में बनाया गया कोण कितना है?</p>",
                    options_en: ["<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">&#960;</mi></math></p>", "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">&#960;</mi><mn>2</mn></mfrac></math></p>", 
                                "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mi mathvariant=\"normal\">&#960;</mi></mrow><mn>3</mn></mfrac></math></p>", "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mi mathvariant=\"normal\">&#960;</mi></mrow><mn>6</mn></mfrac></math></p>"],
                    options_hi: ["<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">&#960;</mi></math></p>", "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">&#960;</mi><mn>2</mn></mfrac></math></p>",
                                "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mi mathvariant=\"normal\">&#960;</mi></mrow><mn>3</mn></mfrac></math></p>", "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mi mathvariant=\"normal\">&#960;</mi></mrow><mn>6</mn></mfrac></math></p>"],
                    solution_en: "<p>57.(d) For minute hand <br><math display=\"inline\"><mo>&#8658;</mo></math> 60 minute = 2&pi;<br><math display=\"inline\"><mo>&#8658;</mo></math>1 minute = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mi mathvariant=\"normal\">&#960;</mi></mrow><mn>60</mn></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math> 25 minute = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mi mathvariant=\"normal\">&#960;</mi></mrow><mn>60</mn></mfrac></math> &times; 25 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mi mathvariant=\"normal\">&#960;</mi></mrow><mn>6</mn></mfrac></math></p>",
                    solution_hi: "<p>57.(d) मिनट की सुई के लिए <br><math display=\"inline\"><mo>&#8658;</mo></math> 60 मिनट = 2&pi;<br><math display=\"inline\"><mo>&#8658;</mo></math> 1 मिनट = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mi mathvariant=\"normal\">&#960;</mi></mrow><mn>60</mn></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math> 25 मिनट = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mi mathvariant=\"normal\">&#960;</mi></mrow><mn>60</mn></mfrac></math> &times; 25 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mi mathvariant=\"normal\">&#960;</mi></mrow><mn>6</mn></mfrac></math></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "58",
                    section: "17",
                    question_en: "<p>58. A class of 30 students appeared in a test. The average score of 12 students is 62, and that of the rest is 72. What is the average score of the class?</p>",
                    question_hi: "<p>58. 30 छात्रों वाली एक कक्षा के सभी छात्रों ने एक टेस्ट दिया। 12 छात्रों का औसत स्कोर 62 है और बाकी छात्रों का औसत स्कोर 72 है। कक्षा का औसत स्कोर ज्ञात करें।</p>",
                    options_en: ["<p>66</p>", "<p>68</p>", 
                                "<p>67</p>", "<p>69</p>"],
                    options_hi: ["<p>66</p>", "<p>68</p>",
                                "<p>67</p>", "<p>69</p>"],
                    solution_en: "<p>58.(b) Total score of 12 students = 12 &times; 62 = 744<br>Total score of 18 students = 18 &times; 72 = 1296<br>the average score of the class = <math display=\"inline\"><mfrac><mrow><mn>1296</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>744</mn></mrow><mrow><mn>30</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2040</mn><mrow><mn>30</mn><mo>&#160;</mo></mrow></mfrac></math> = 68</p>",
                    solution_hi: "<p>58.(b) 12 छात्रों का कुल स्कोर = 12 &times; 62 = 744<br>18 छात्रों का कुल स्कोर = 18 &times; 72 = 1296<br>कक्षा का औसत अंक = <math display=\"inline\"><mfrac><mrow><mn>1296</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>744</mn></mrow><mrow><mn>30</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2040</mn><mrow><mn>30</mn><mo>&#160;</mo></mrow></mfrac></math> = 68</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "59",
                    section: "17",
                    question_en: "<p>59. If m = asecA and y = btanA then find the value of b<sup>2</sup>m<sup>2</sup> - a<sup>2</sup>y<sup>2</sup> + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi mathvariant=\"normal\">a</mi><mn>2</mn></msup><msup><mi mathvariant=\"normal\">y</mi><mn>2</mn></msup></mrow><mrow><msup><mi mathvariant=\"normal\">b</mi><mn>2</mn></msup><msup><mi mathvariant=\"normal\">m</mi><mn>2</mn></msup></mrow></mfrac></math> + cos<sup>2</sup>A.</p>",
                    question_hi: "<p>59. यदि m = asecA और y = btanA है, तो b<sup>2</sup>m<sup>2</sup> - a<sup>2</sup>y<sup>2</sup> + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi mathvariant=\"normal\">a</mi><mn>2</mn></msup><msup><mi mathvariant=\"normal\">y</mi><mn>2</mn></msup></mrow><mrow><msup><mi mathvariant=\"normal\">b</mi><mn>2</mn></msup><msup><mi mathvariant=\"normal\">m</mi><mn>2</mn></msup></mrow></mfrac></math> + cos<sup>2</sup>A का मान ज्ञात कीजिए।</p>",
                    options_en: ["<p>a&sup2;b&sup2;</p>", "<p>1 - a&sup2;b&sup2;</p>", 
                                "<p>a&sup2;b&sup2; + 2</p>", "<p>a&sup2;b&sup2; + 1</p>"],
                    options_hi: ["<p>a&sup2;b&sup2;</p>", "<p>1 - a&sup2;b&sup2;</p>",
                                "<p>a&sup2;b&sup2; + 2</p>", "<p>a&sup2;b&sup2; + 1</p>"],
                    solution_en: "<p>59.(d) <br><strong>Given:</strong> m = asecA and y = btan A<br>b<sup>2</sup>m<sup>2</sup> - a<sup>2</sup>y<sup>2</sup> + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi mathvariant=\"normal\">a</mi><mn>2</mn></msup><msup><mi mathvariant=\"normal\">y</mi><mn>2</mn></msup></mrow><mrow><msup><mi mathvariant=\"normal\">b</mi><mn>2</mn></msup><msup><mi mathvariant=\"normal\">m</mi><mn>2</mn></msup></mrow></mfrac></math> + cos<sup>2</sup>A<br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi mathvariant=\"normal\">b</mi><mn>2</mn></msup><msup><mi mathvariant=\"normal\">a</mi><mn>2</mn></msup><msup><mi>sec</mi><mn>2</mn></msup><mi mathvariant=\"normal\">A</mi></math> - a<sup>2</sup>b<sup>2</sup>tan<sup>2</sup>A + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi mathvariant=\"normal\">a</mi><mn>2</mn></msup><msup><mi mathvariant=\"normal\">b</mi><mn>2</mn></msup><msup><mi>tan</mi><mn>2</mn></msup><mi mathvariant=\"normal\">A</mi></mrow><mrow><msup><mi mathvariant=\"normal\">b</mi><mn>2</mn></msup><msup><mi mathvariant=\"normal\">a</mi><mn>2</mn></msup><msup><mi>sec</mi><mn>2</mn></msup><mi mathvariant=\"normal\">A</mi></mrow></mfrac><mo>&#160;</mo></math>+ cos<sup>2</sup>A<br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi mathvariant=\"normal\">a</mi><mn>2</mn></msup><msup><mi mathvariant=\"normal\">b</mi><mn>2</mn></msup></math> (sec<sup>2</sup>A - tan<sup>2</sup>A ) + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&#160;</mo><msup><mi>tan</mi><mn>2</mn></msup><mi mathvariant=\"normal\">A</mi></mrow><mrow><msup><mi>sec</mi><mn>2</mn></msup><mi mathvariant=\"normal\">A</mi><mo>&#160;</mo></mrow></mfrac></math> + cos<sup>2</sup> A<br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi mathvariant=\"normal\">a</mi><mn>2</mn></msup><msup><mi mathvariant=\"normal\">b</mi><mn>2</mn></msup></math>(1) + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi>Sin</mi><mn>2</mn></msup><mi mathvariant=\"normal\">A</mi><mo>&#215;</mo><msup><mi>cos</mi><mn>2</mn></msup><mi mathvariant=\"normal\">A</mi></mrow><mrow><msup><mi>cos</mi><mn>2</mn></msup><mi mathvariant=\"normal\">A</mi></mrow></mfrac></math> + cos<sup>2</sup> A<br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi mathvariant=\"normal\">a</mi><mn>2</mn></msup><msup><mi mathvariant=\"normal\">b</mi><mn>2</mn></msup></math> + 1</p>",
                    solution_hi: "<p>59.(d)<br><strong>दिया गया: </strong>m = asecA और y = btanA<br>b<sup>2</sup>m<sup>2</sup> - a<sup>2</sup>y<sup>2</sup> + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi mathvariant=\"normal\">a</mi><mn>2</mn></msup><msup><mi mathvariant=\"normal\">y</mi><mn>2</mn></msup></mrow><mrow><msup><mi mathvariant=\"normal\">b</mi><mn>2</mn></msup><msup><mi mathvariant=\"normal\">m</mi><mn>2</mn></msup></mrow></mfrac></math> + cos<sup>2</sup>A<br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi mathvariant=\"normal\">b</mi><mn>2</mn></msup><msup><mi mathvariant=\"normal\">a</mi><mn>2</mn></msup><msup><mi>sec</mi><mn>2</mn></msup><mi mathvariant=\"normal\">A</mi></math> - a<sup>2</sup>b<sup>2</sup>tan<sup>2</sup>A + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi mathvariant=\"normal\">a</mi><mn>2</mn></msup><msup><mi mathvariant=\"normal\">b</mi><mn>2</mn></msup><msup><mi>tan</mi><mn>2</mn></msup><mi mathvariant=\"normal\">A</mi></mrow><mrow><msup><mi mathvariant=\"normal\">b</mi><mn>2</mn></msup><msup><mi mathvariant=\"normal\">a</mi><mn>2</mn></msup><msup><mi>sec</mi><mn>2</mn></msup><mi mathvariant=\"normal\">A</mi></mrow></mfrac><mo>&#160;</mo></math>+ cos<sup>2</sup>A<br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi mathvariant=\"normal\">a</mi><mn>2</mn></msup><msup><mi mathvariant=\"normal\">b</mi><mn>2</mn></msup></math> (sec<sup>2</sup>A - tan<sup>2</sup>A ) + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&#160;</mo><msup><mi>tan</mi><mn>2</mn></msup><mi mathvariant=\"normal\">A</mi></mrow><mrow><msup><mi>sec</mi><mn>2</mn></msup><mi mathvariant=\"normal\">A</mi><mo>&#160;</mo></mrow></mfrac></math> + cos<sup>2</sup> A<br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi mathvariant=\"normal\">a</mi><mn>2</mn></msup><msup><mi mathvariant=\"normal\">b</mi><mn>2</mn></msup></math>(1) + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi>Sin</mi><mn>2</mn></msup><mi mathvariant=\"normal\">A</mi><mo>&#215;</mo><msup><mi>cos</mi><mn>2</mn></msup><mi mathvariant=\"normal\">A</mi></mrow><mrow><msup><mi>cos</mi><mn>2</mn></msup><mi mathvariant=\"normal\">A</mi></mrow></mfrac></math> + cos<sup>2</sup> A<br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi mathvariant=\"normal\">a</mi><mn>2</mn></msup><msup><mi mathvariant=\"normal\">b</mi><mn>2</mn></msup></math> + 1</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "60",
                    section: "17",
                    question_en: "<p>60. Two pipes P and Q can fill a tank in 36 minutes and 45 minutes, respectively. If both pipes are opened together, the time taken to fill the tank is:</p>",
                    question_hi: "<p>60. दो पाइप P और Q एक टंकी को क्रमशः 36 मिनट और 45 मिनट में भर सकते हैं। यदि दोनों पाइपों को एक साथ खोल दिया जाए, तो टंकी को भरने में कितना समय लगेगा?</p>",
                    options_en: ["<p>81 minutes</p>", "<p>20 minutes</p>", 
                                "<p>40.5 minutes</p>", "<p>10 minutes</p>"],
                    options_hi: ["<p>81 मिनट</p>", "<p>20 मिनट</p>",
                                "<p>40.5 मिनट</p>", "<p>10 मिनट</p>"],
                    solution_en: "<p>60.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730952542412.png\" alt=\"rId8\" width=\"222\" height=\"199\"><br>When both pipes are open , then time to fill the tank = <math display=\"inline\"><mfrac><mrow><mn>180</mn></mrow><mrow><mn>9</mn></mrow></mfrac></math> = 20 min</p>",
                    solution_hi: "<p>60.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730952542530.png\" alt=\"rId9\" width=\"221\" height=\"198\"><br>जब दोनों पाइप खुले हों, तो टैंक भरने मे लगा समय = <math display=\"inline\"><mfrac><mrow><mn>180</mn></mrow><mrow><mn>9</mn></mrow></mfrac></math> = 20 मिनट</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "61",
                    section: "17",
                    question_en: "<p>61. The table shows the percentage distribution of the population (only male and female) according to Gender and Literacy.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730952542688.png\" alt=\"rId10\" width=\"323\" height=\"139\"> <br>If the literate male population of state A is 5,00,000, then the total population of the state is:</p>",
                    question_hi: "<p>61. दी गई तालिका लिंग और साक्षरता के अनुसार जनसंख्या (केवल पुरुष और महिला) का प्रतिशत बंटन दर्शाती है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730952542798.png\" alt=\"rId11\" width=\"324\" height=\"156\"> <br>यदि राज्य A की साक्षर पुरुष जनसंख्या 5,00,000 है, तो राज्य की कुल जनसंख्या ज्ञात करें।</p>",
                    options_en: ["<p>30,00,000</p>", "<p>40,00,000</p>", 
                                "<p>16,00,000</p>", "<p>20,00,000</p>"],
                    options_hi: ["<p>30,00,000</p>", "<p>40,00,000</p>",
                                "<p>16,00,000</p>", "<p>20,00,000</p>"],
                    solution_en: "<p>61.(d) According to the question,<br><math display=\"inline\"><mo>&#8658;</mo></math> 5 units = 500000<br><math display=\"inline\"><mo>&#8658;</mo></math> 1 units = 100000<br><math display=\"inline\"><mo>&#8658;</mo></math> (5 + 6 + 4 + 9) units = 20 units = ₹ 20,000,00</p>",
                    solution_hi: "<p>61.(d) प्रश्न के अनुसार,<br><math display=\"inline\"><mo>&#8658;</mo></math> 5 इकाई = 500000<br><math display=\"inline\"><mo>&#8658;</mo></math> 1 इकाई = 100000<br><math display=\"inline\"><mo>&#8658;</mo></math> (5 + 6 + 4 + 9) इकाई = 20 इकाई = ₹ 20,000,00</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "62",
                    section: "17",
                    question_en: "<p>62. Study the given table and answer the question.<br>The table displays the number of cars produced (in lakhs) by six different brands between 2018 and 2023<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730952542938.png\" alt=\"rId12\" width=\"448\" height=\"156\"> <br>Which brand production is the highest, and in which year ?</p>",
                    question_hi: "<p>62. दी गई सारणी का अध्ययन कीजिए और प्रश्न का उत्तर दीजिए।&nbsp;<br>सारणी 2018 और 2023 के बीच छ: अलग-अलग ब्रांड द्वारा उत्पादित कारों की संख्या (लाखों में) को दर्शाती है। <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730952543048.png\" alt=\"rId13\" width=\"389\" height=\"168\"> <br>किस ब्रांड का उत्पादन सबसे अधिक और किस वर्ष में है ?</p>",
                    options_en: ["<p>E and 2019</p>", "<p>E and 2018</p>", 
                                "<p>C and 2022</p>", "<p>F and 2020</p>"],
                    options_hi: ["<p>E और 2019</p>", "<p>E और 2018</p>",
                                "<p>C और 2022</p>", "<p>F और 2020</p>"],
                    solution_en: "<p>62.(b) In 2018 , Brand E production is highest.</p>",
                    solution_hi: "<p>62.(b) 2018 में, ब्रांड E का उत्पादन सबसे अधिक है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "63",
                    section: "17",
                    question_en: "63. A manufacturer announces a trade discount of 30% on his products for his dealers. One of the dealers purchased 6 smart phones manufactured by the company at the rate of Rs.25,000 each. What is the amount of trade discount? ",
                    question_hi: "63. एक निर्माता अपने डीलरों को अपने उत्पाद पर 30% की व्यापार छूट प्रदान करता है। एक डीलर Rs.25,000 प्रत्येक की दर से कंपनी द्वारा निर्मित 6 स्मार्ट फ़ोन खरीदता है। व्यापार छूट की राशि क्या होगी? ",
                    options_en: [" Rs.50,000", " Rs.55,000", 
                                " Rs.1,05,000", " Rs.45,000"],
                    options_hi: [" Rs.50,000", " Rs.55,000",
                                " Rs.1,05,000", " Rs.45,000"],
                    solution_en: "63.(d) <br />Amount of trade discount = 25000 × 6 × <math display=\"inline\"><mfrac><mrow><mn>30</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = ₹ 45000 ",
                    solution_hi: "63.(d) <br />व्यापार छूट की राशि = 25000 × 6 × <math display=\"inline\"><mfrac><mrow><mn>30</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = ₹ 45000 ",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "64",
                    section: "17",
                    question_en: "64. Find the mean proportional of 12.96 and 0.16.",
                    question_hi: "64. 12.96 और 0.16 का माध्‍यानुपाती ज्ञात करें।",
                    options_en: [" 1.21  ", " 1.96 ", 
                                " 1.44 ", " 1.69"],
                    options_hi: [" 1.21  ", " 1.96 ",
                                " 1.44 ", " 1.69"],
                    solution_en: "64.(c) Mean proportional of 12.96 and 0.16 = <math display=\"inline\"><msqrt><mn>12</mn><mo>.</mo><mn>96</mn><mi>&nbsp;</mi><mo>×</mo><mi>&nbsp;</mi><mn>0</mn><mo>.</mo><mn>16</mn></msqrt></math><br />= <math display=\"inline\"><msqrt><mn>3</mn><mo>.</mo><mn>6</mn><mi>&nbsp;</mi><mo>×</mo><mn>3</mn><mo>.</mo><mn>6</mn><mi>&nbsp;</mi><mo>×</mo><mi>&nbsp;</mi><mn>0</mn><mo>.</mo><mn>4</mn><mi>&nbsp;</mi><mo>×</mo><mi>&nbsp;</mi><mn>0</mn><mo>.</mo><mn>4</mn></msqrt></math><br />=  3.6 × 0.4 = 1.44",
                    solution_hi: "64.(c) 12.96 और 0.16 का माध्य आनुपातिक = <math display=\"inline\"><msqrt><mn>12</mn><mo>.</mo><mn>96</mn><mi>&nbsp;</mi><mo>×</mo><mi>&nbsp;</mi><mn>0</mn><mo>.</mo><mn>16</mn></msqrt></math><br />= <math display=\"inline\"><msqrt><mn>3</mn><mo>.</mo><mn>6</mn><mi>&nbsp;</mi><mo>×</mo><mn>3</mn><mo>.</mo><mn>6</mn><mi>&nbsp;</mi><mo>×</mo><mi>&nbsp;</mi><mn>0</mn><mo>.</mo><mn>4</mn><mi>&nbsp;</mi><mo>×</mo><mi>&nbsp;</mi><mn>0</mn><mo>.</mo><mn>4</mn></msqrt></math><br />=  3.6 × 0.4 = 1.44",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "65",
                    section: "17",
                    question_en: "<p>65. The perimeters of two similar triangles &Delta;RST and &Delta;ABC are 26 cm and 39 cm, respectively. If AB = 24 cm, then RS is ___________.</p>",
                    question_hi: "<p>65. दो समरूप त्रिभुजों &Delta;RST जों और &Delta;ABC का परिमाप क्रमशः 26 cm और 39 cm है। यदि AB = 24 cm है, तो RS _________ है।&nbsp;</p>",
                    options_en: ["<p>16 cm</p>", "<p>36 cm</p>", 
                                "<p>18 cm</p>", "<p>24 cm</p>"],
                    options_hi: ["<p>16 cm</p>", "<p>36 cm</p>",
                                "<p>18 cm</p>", "<p>24 cm</p>"],
                    solution_en: "<p>65.(a) We know that, perimeters of two similar triangle are equal to their corresponding sides<br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>perimeter</mi><mo>&#160;</mo><mi>of</mi><mo>&#160;</mo><mi>&#916;RST</mi><mo>&#160;</mo><mo>&#160;</mo></mrow><mrow><mi>perimeter</mi><mo>&#160;</mo><mi>of</mi><mo>&#160;</mo><mi>&#916;ABC</mi></mrow></mfrac><mo>=</mo><mfrac><mi>RS</mi><mi>AB</mi></mfrac></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>26</mn><mn>39</mn></mfrac><mo>=</mo><mfrac><mi>RS</mi><mn>24</mn></mfrac></math> &rArr; RS =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>26</mn><mo>&#215;</mo><mn>24</mn></mrow><mn>39</mn></mfrac></math> = 16cm</p>",
                    solution_hi: "<p>65.(a) हम जानते हैं , दो समरूप त्रिभुजों की परिधि उनकी संगत भुजाओं के बराबर होती है।<br><math style=\"font-family: Verdana;\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#916;RST</mi><mo>&#160;</mo><mi>&#2325;&#2366;</mi><mo>&#160;</mo><mi>&#2346;&#2352;&#2367;&#2350;&#2366;&#2346;</mi><mo>&#160;</mo></mrow><mrow><mi>&#916;ABC</mi><mo>&#160;</mo><mi>&#2325;&#2366;</mi><mo>&#160;</mo><mi>&#2346;&#2352;&#2367;&#2350;&#2366;&#2346;</mi></mrow></mfrac><mo>=</mo><mfrac><mi>RS</mi><mi>AB</mi></mfrac></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>26</mn><mn>39</mn></mfrac><mo>=</mo><mfrac><mi>RS</mi><mn>24</mn></mfrac></math> &rArr; RS =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>26</mn><mo>&#215;</mo><mn>24</mn></mrow><mn>39</mn></mfrac></math> = 16 सेमी</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "66",
                    section: "17",
                    question_en: "66. In the following sets, which set represents the angles of a triangle?",
                    question_hi: "66. निम्नलिखित समुच्‍चयों में से कौन सा समुच्‍चय एक त्रिभुज के कोणों को दर्शाता है? ",
                    options_en: [" {30°,60°,90°} ", " {80°,50°,90°} ", 
                                " {30°,50°,110°} ", " {30°,50°,90°}"],
                    options_hi: [" {30°,60°,90°} ", " {80°,50°,90°} ",
                                " {30°,50°,110°} ", " {30°,50°,90°}"],
                    solution_en: "66.(a) We know that , sum of the angle of triangle = 180°<br />By checking all options one by one, only option (a) satisfy i.e. {30°,60°,90°}  ",
                    solution_hi: "66.(a) हम जानते हैं कि त्रिभुज के कोणों का योग = 180°<br />एक एक करके विकल्पों की जांच करने पर केवल विकल्प (a) संतुष्ट होता है अर्थात {30°,60°,90°}",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "67",
                    section: "17",
                    question_en: "<p>67. Simplify the following expression. <br>(g - h)&sup2; - (g + h)&sup2; + 4gh</p>",
                    question_hi: "<p>67. निम्नलिखित व्यंजक को हल कीजिए।<br>(g - h)&sup2; - (g + h)&sup2; + 4gh</p>",
                    options_en: ["<p>4gh</p>", "<p>&minus;4gh</p>", 
                                "<p>4</p>", "<p>0</p>"],
                    options_hi: ["<p>4gh</p>", "<p>&minus;4gh</p>",
                                "<p>4</p>", "<p>0</p>"],
                    solution_en: "<p>67.(d)&nbsp;<br>(g - h)&sup2; - (g + h)&sup2; + 4gh<br><strong>Identity used :</strong> <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi mathvariant=\"normal\">a</mi><mn>2</mn></msup></math> - b&sup2; = (a + b) (a - b)<br>= (g - h - g - h ) (g - h + g + h) + 4gh <br>= (- 2h) (2g) + 4gh<br>= - 4gh + 4gh = 0</p>",
                    solution_hi: "<p>67.(d) <br>(g - h)&sup2; - (g + h)&sup2; + 4gh<br>प्रयुक्त सूत्र : <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi mathvariant=\"normal\">a</mi><mn>2</mn></msup></math> - b&sup2; = (a + b) (a - b)<br>= (g - h - g - h ) (g - h + g + h) + 4gh <br>= (- 2h ) (2g) + 4gh<br>= - 4gh + 4gh = 0</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "68",
                    section: "17",
                    question_en: "<p>68. The following table shows the percentage population of four states below poverty line, and the male to female ratios both above and below the poverty line.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730952543233.png\" alt=\"rId14\" width=\"480\" height=\"163\"> <br>If the population of states Q and S is 14,700 each, then what is the total number of males below poverty line in these states ?</p>",
                    question_hi: "<p>68. निम्न तालिका में चार राज्यों में गरीबी रेखा से नीचे रहने वाली जनसंख्या का प्रतिशत तथा गरीबी रेखा से ऊपर एवं गरीबी रेखा से नीचे रहने वाले पुरुष-महिला के अनुपात को दर्शाया गया है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730952543386.png\" alt=\"rId15\" width=\"450\" height=\"163\"> <br>यदि Q और S प्रत्येक राज्य की जनसंख्या 14,700 है, तो इन राज्यों में गरीबी रेखा से नीचे रहने वाले पुरुषों की कुल संख्या कितनी है ?</p>",
                    options_en: ["<p>1276</p>", "<p>1479</p>", 
                                "<p>1113</p>", "<p>1364</p>"],
                    options_hi: ["<p>1276</p>", "<p>1479</p>",
                                "<p>1113</p>", "<p>1364</p>"],
                    solution_en: "<p>68.(c) Total number of males below poverty line in Q = 14700 &times; <math display=\"inline\"><mfrac><mrow><mn>19</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>7</mn></mfrac></math> = 798<br>Total number of males below poverty line in S = 14700 &times; <math display=\"inline\"><mfrac><mrow><mn>15</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>7</mn></mfrac></math> = 315<br>Required population = 798 + 315 = 1113</p>",
                    solution_hi: "<p>68.(c) Q में गरीबी रेखा से नीचे पुरुषों की कुल संख्या = 14700 &times; <math display=\"inline\"><mfrac><mrow><mn>19</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>7</mn></mfrac></math> = 798<br>S में गरीबी रेखा से नीचे पुरुषों की कुल संख्या = 14700 &times; <math display=\"inline\"><mfrac><mrow><mn>15</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>7</mn></mfrac></math> = 315<br>आवश्यक जनसंख्या = 798 + 315 = 1113</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "69",
                    section: "17",
                    question_en: "69. A sum of ₹10,000 is taken as a loan by Rajesh at a rate of 15% p.a. simple interest for 2 years. But Rajesh could not repay it at the agreed time and asked for an extension of two more years. So, the lender included the interest amount for the period as principal for the next two years at the same rate of interest. The total amount paid by Rajesh at the end of 4 years is: ",
                    question_hi: "69. राजेश द्वारा ₹10,000 की राशि 2 वर्षों के लिए 15% साधारण ब्याज की दर पर ऋण के रूप में ली जाती है। लेकिन राजेश इसे तय समय पर नहीं चुका सका और उसने दो और वर्ष का समय मांगा। इसलिए, ऋणदाता ने उस अवधि की ब्याज राशि को उसी ब्याज दर पर अगले दो वर्षों के लिए मूलधन के रूप में शामिल किया। 4 वर्ष के अंत में राजेश द्वारा भुगतान की गई कुल राशि ज्ञात करें। ",
                    options_en: [" ₹18,590 ", " ₹16,900 ", 
                                " ₹17,650 ", " ₹15,630"],
                    options_hi: [" ₹18,590 ", " ₹16,900 ",
                                " ₹17,650 ", " ₹15,630"],
                    solution_en: "69.(b) For first two years <br />Principal = ₹10,000 , rate = 15% , <br />SI = <math display=\"inline\"><mfrac><mrow><mn>10000</mn><mi>&nbsp;</mi><mo>×</mo><mi>&nbsp;</mi><mn>15</mn><mi>&nbsp;</mi><mo>×</mo><mi>&nbsp;</mi><mn>2</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = ₹3000<br />For next 2 years,<br />Principal = 13000 , Rate = 15% <br />SI = <math display=\"inline\"><mfrac><mrow><mn>13000</mn><mi>&nbsp;</mi><mo>×</mo><mi>&nbsp;</mi><mn>15</mn><mi>&nbsp;</mi><mo>×</mo><mi>&nbsp;</mi><mn>2</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = ₹3900<br />So , total amount paid by Rajesh at the end of 4 years = 13000 + 3900 =₹16900",
                    solution_hi: "69.(b) पहले दो वर्षों के लिए <br />मूलधन = ₹10,000 , दर = 15% , <br />साधारण ब्याज = <math display=\"inline\"><mfrac><mrow><mn>10000</mn><mi>&nbsp;</mi><mo>×</mo><mi>&nbsp;</mi><mn>15</mn><mi>&nbsp;</mi><mo>×</mo><mi>&nbsp;</mi><mn>2</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = ₹3000<br />अगले 2 वर्षों के लिए,<br />मूलधन = 13000 , दर = 15% <br />साधारण ब्याज = <math display=\"inline\"><mfrac><mrow><mn>13000</mn><mi>&nbsp;</mi><mo>×</mo><mi>&nbsp;</mi><mn>15</mn><mi>&nbsp;</mi><mo>×</mo><mi>&nbsp;</mi><mn>2</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = ₹3900<br />तो, 4 साल के अंत में राजेश द्वारा भुगतान की गई कुल राशि  = 13000 + 3900 =₹16900",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "70",
                    section: "17",
                    question_en: "<p>70. The area of a sector of a circle of radius 12 cm, formed by an arc of length 7 cm is:</p>",
                    question_hi: "<p>70. 12 cm त्रिज्या वाले एक वृत्त के एक त्रिज्यखंड का क्षेत्रफल कितना होगा, जो 7 cm लंबे चाप द्वारा निर्मित हुआ है?</p>",
                    options_en: ["<p>42 cm&sup2;</p>", "<p>84 cm&sup2;</p>", 
                                "<p>21 cm&sup2;</p>", "<p>28 cm&sup2;</p>"],
                    options_hi: ["<p>42 cm&sup2;</p>", "<p>84 cm&sup2;</p>",
                                "<p>21 cm&sup2;</p>", "<p>28 cm&sup2;</p>"],
                    solution_en: "<p>70.(a) Given radius = 12cm , arc of length = 7 cm<br>Area of sector = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> &times; r &times; l<br>= <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> &times; 12 &times; 7<br>= 42 cm&sup2;</p>",
                    solution_hi: "<p>70.(a) दिया गया , त्रिज्या = 12 सेमी , चाप की लंबाई = 7 सेमी<br>त्रिज्यखंड का क्षेत्रफल = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> &times; r &times; l<br>= <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> &times; 12 &times; 7<br>= 42 cm&sup2;</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "71",
                    section: "17",
                    question_en: "<p>71. Kalyan spends 80% of his income. His income increases by 15% and his expenditure also increases by 5%. The percentage of increase in his savings is:</p>",
                    question_hi: "<p>71. कल्याण अपनी आय का 80% खर्च करता है। उसकी आय 15% बढ़ जाती है और उसका व्यय भी 5% बढ़ जाता है। उसकी बचत में वृद्धि का प्रतिशत क्या है?</p>",
                    options_en: ["<p>50%</p>", "<p>20%</p>", 
                                "<p>55%</p>", "<p>40%</p>"],
                    options_hi: ["<p>50%</p>", "<p>20%</p>",
                                "<p>55%</p>", "<p>40%</p>"],
                    solution_en: "<p>71.(c) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730952543507.png\" alt=\"rId16\" width=\"273\" height=\"152\"><br>percentage of increase in his savings = <math display=\"inline\"><mfrac><mrow><mn>31</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>20</mn><mi>&#160;</mi></mrow><mrow><mn>20</mn></mrow></mfrac></math> &times; 100 = 55%</p>",
                    solution_hi: "<p>71.(c) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730952543640.png\" alt=\"rId17\" width=\"288\" height=\"175\"><br>उसकी बचत में वृद्धि का प्रतिशत = <math display=\"inline\"><mfrac><mrow><mn>31</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>20</mn><mi>&#160;</mi></mrow><mrow><mn>20</mn></mrow></mfrac></math> &times; 100 = 55%</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "72",
                    section: "17",
                    question_en: "<p>72. A dishonest fruit-seller sells fruits at 20% profit but he uses 950 gm weight in place of 1 kg. What is the profit per cent?</p>",
                    question_hi: "<p>72. एक बेईमान फल-विक्रेता 20% लाभ पर फल बेचता है लेकिन वह 1 kg के स्थान पर 950 gm बाट(weight) का उपयोग करता है। लाभ प्रतिशत क्या है?</p>",
                    options_en: ["<p><math display=\"inline\"><mfrac><mrow><mn>500</mn></mrow><mrow><mn>17</mn></mrow></mfrac></math>%</p>", "<p><math display=\"inline\"><mfrac><mrow><mn>500</mn></mrow><mrow><mn>19</mn></mrow></mfrac></math>%</p>", 
                                "<p><math display=\"inline\"><mfrac><mrow><mn>500</mn></mrow><mrow><mn>32</mn></mrow></mfrac></math>%</p>", "<p><math display=\"inline\"><mfrac><mrow><mn>700</mn></mrow><mrow><mn>19</mn></mrow></mfrac></math>%</p>"],
                    options_hi: ["<p><math display=\"inline\"><mfrac><mrow><mn>500</mn></mrow><mrow><mn>17</mn></mrow></mfrac></math>%</p>", "<p><math display=\"inline\"><mfrac><mrow><mn>500</mn></mrow><mrow><mn>19</mn></mrow></mfrac></math>%</p>",
                                "<p><math display=\"inline\"><mfrac><mrow><mn>500</mn></mrow><mrow><mn>32</mn></mrow></mfrac></math>%</p>", "<p><math display=\"inline\"><mfrac><mrow><mn>700</mn></mrow><mrow><mn>19</mn></mrow></mfrac></math>%</p>"],
                    solution_en: "<p>72.(b) 20% = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math><br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>SP</mi><mi mathvariant=\"normal\">&#160;</mi></mrow><mrow><mi>CP</mi><mi mathvariant=\"normal\">&#160;</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>6</mn><mn>5</mn></mfrac><mo>&#215;</mo><mfrac><mn>1000</mn><mrow><mn>950</mn><mo>&#160;</mo></mrow></mfrac><mo>=</mo><mfrac><mn>600</mn><mn>475</mn></mfrac></math><br>profit percent = <math display=\"inline\"><mfrac><mrow><mn>600</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>475</mn><mi>&#160;</mi></mrow><mrow><mn>475</mn></mrow></mfrac></math> &times; 100<br>= <math display=\"inline\"><mfrac><mrow><mn>125</mn></mrow><mrow><mn>475</mn></mrow></mfrac></math> &times; 100 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>500</mn><mn>19</mn></mfrac></math>%</p>",
                    solution_hi: "<p>72.(b) 20% = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math><br><math style=\"font-family: Verdana;\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2357;&#2367;&#2325;&#2381;&#2352;&#2351;</mi><mo>&#160;</mo><mi>&#2350;&#2370;&#2354;&#2381;&#2351;</mi></mrow><mrow><mi>&#2325;&#2381;&#2352;&#2351;</mi><mo>&#160;</mo><mi>&#2350;&#2370;&#2354;&#2381;&#2351;</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>6</mn><mn>5</mn></mfrac><mo>&#215;</mo><mfrac><mn>1000</mn><mrow><mn>950</mn><mo>&#160;</mo></mrow></mfrac><mo>=</mo><mfrac><mn>600</mn><mn>475</mn></mfrac></math><br>लाभ प्रतिशत = <math display=\"inline\"><mfrac><mrow><mn>600</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>475</mn><mi>&#160;</mi></mrow><mrow><mn>475</mn></mrow></mfrac></math> &times; 100<br>= <math display=\"inline\"><mfrac><mrow><mn>125</mn></mrow><mrow><mn>475</mn></mrow></mfrac></math> &times; 100 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>500</mn><mn>19</mn></mfrac></math>%</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "73",
                    section: "17",
                    question_en: "<p>73. Simplify <math display=\"inline\"><mfrac><mrow><mn>1</mn><mo>.</mo><mn>5</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>1</mn><mo>.</mo><mn>5</mn><mi>&#160;</mi><mo>+</mo><mn>2</mn><mo>.</mo><mn>5</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>2</mn><mo>.</mo><mn>5</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>3</mn><mo>.</mo><mn>5</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>3</mn><mo>.</mo><mn>5</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>2</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>1</mn><mo>.</mo><mn>5</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>2</mn><mo>.</mo><mn>5</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>2</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>2</mn><mo>.</mo><mn>5</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>3</mn><mo>.</mo><mn>5</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>2</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>1</mn><mo>.</mo><mn>5</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>3</mn><mo>.</mo><mn>5</mn></mrow><mrow><mn>1</mn><mo>.</mo><mn>5</mn><mo>+</mo><mn>2</mn><mo>.</mo><mn>5</mn><mi>&#160;</mi><mo>+</mo><mn>3</mn><mo>.</mo><mn>5</mn></mrow></mfrac></math></p>",
                    question_hi: "<p>73. <math display=\"inline\"><mfrac><mrow><mn>1</mn><mo>.</mo><mn>5</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>1</mn><mo>.</mo><mn>5</mn><mi>&#160;</mi><mo>+</mo><mn>2</mn><mo>.</mo><mn>5</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>2</mn><mo>.</mo><mn>5</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>3</mn><mo>.</mo><mn>5</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>3</mn><mo>.</mo><mn>5</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>2</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>1</mn><mo>.</mo><mn>5</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>2</mn><mo>.</mo><mn>5</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>2</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>2</mn><mo>.</mo><mn>5</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>3</mn><mo>.</mo><mn>5</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>2</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>1</mn><mo>.</mo><mn>5</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>3</mn><mo>.</mo><mn>5</mn></mrow><mrow><mn>1</mn><mo>.</mo><mn>5</mn><mo>+</mo><mn>2</mn><mo>.</mo><mn>5</mn><mi>&#160;</mi><mo>+</mo><mn>3</mn><mo>.</mo><mn>5</mn></mrow></mfrac></math> को सरल कीजिए।</p>",
                    options_en: ["<p>9.5</p>", "<p>6.5</p>", 
                                "<p>8.5</p>", "<p>7.5</p>"],
                    options_hi: ["<p>9.5</p>", "<p>6.5</p>",
                                "<p>8.5</p>", "<p>7.5</p>"],
                    solution_en: "<p>73.(d)<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mo>.</mo><mn>5</mn><mo>&#215;</mo><mn>1</mn><mo>.</mo><mn>5</mn><mo>+</mo><mn>2</mn><mo>.</mo><mn>5</mn><mo>&#215;</mo><mn>2</mn><mo>.</mo><mn>5</mn><mo>+</mo><mn>3</mn><mo>.</mo><mn>5</mn><mo>&#215;</mo><mn>3</mn><mo>.</mo><mn>5</mn><mo>+</mo><mn>2</mn><mo>&#215;</mo><mn>1</mn><mo>.</mo><mn>5</mn><mo>&#215;</mo><mn>2</mn><mo>.</mo><mn>5</mn><mo>+</mo><mn>2</mn><mo>&#215;</mo><mn>2</mn><mo>.</mo><mn>5</mn><mo>&#215;</mo><mn>3</mn><mo>.</mo><mn>5</mn><mo>+</mo><mn>2</mn><mo>&#215;</mo><mn>1</mn><mo>.</mo><mn>5</mn><mo>&#215;</mo><mn>3</mn><mo>.</mo><mn>5</mn></mrow><mrow><mn>1</mn><mo>.</mo><mn>5</mn><mo>+</mo><mn>2</mn><mo>.</mo><mn>5</mn><mo>+</mo><mn>3</mn><mo>.</mo><mn>5</mn></mrow></mfrac></math><br>We know that <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mi mathvariant=\"normal\">a</mi><mo>+</mo><mi mathvariant=\"normal\">b</mi><mo>+</mo><mi mathvariant=\"normal\">c</mi><mo>)</mo></mrow><mn>2</mn></msup></math> = a<sup>2</sup> + b<sup>2</sup> + c<sup>2</sup> + 2ab + 2bc + 2ca<br><math display=\"inline\"><mfrac><mrow><msup><mrow><mo>(</mo><mn>1</mn><mo>.</mo><mn>5</mn><mo>+</mo><mn>2</mn><mo>.</mo><mn>5</mn><mi>&#160;</mi><mo>+</mo><mn>3</mn><mo>.</mo><mn>5</mn><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup></mrow><mrow><mn>1</mn><mo>.</mo><mn>5</mn><mo>+</mo><mn>2</mn><mo>.</mo><mn>5</mn><mi>&#160;</mi><mo>+</mo><mn>3</mn><mo>.</mo><mn>5</mn></mrow></mfrac></math> = 1.5 + 2.5 + 3.5 = 7.5</p>",
                    solution_hi: "<p>73.(d)&nbsp;<br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mrow><mn>1</mn><mo>.</mo><mn>5</mn><mo>&#215;</mo><mn>1</mn><mo>.</mo><mn>5</mn><mo>+</mo><mn>2</mn><mo>.</mo><mn>5</mn><mo>&#215;</mo><mn>2</mn><mo>.</mo><mn>5</mn><mo>+</mo><mn>3</mn><mo>.</mo><mn>5</mn><mo>&#215;</mo><mn>3</mn><mo>.</mo><mn>5</mn><mo>+</mo><mn>2</mn><mo>&#215;</mo><mn>1</mn><mo>.</mo><mn>5</mn><mo>&#215;</mo><mn>2</mn><mo>.</mo><mn>5</mn><mo>+</mo><mn>2</mn><mo>&#215;</mo><mn>2</mn><mo>.</mo><mn>5</mn><mo>&#215;</mo><mn>3</mn><mo>.</mo><mn>5</mn><mo>+</mo><mn>2</mn><mo>&#215;</mo><mn>1</mn><mo>.</mo><mn>5</mn><mo>&#215;</mo><mn>3</mn><mo>.</mo><mn>5</mn></mrow><mrow><mn>1</mn><mo>.</mo><mn>5</mn><mo>+</mo><mn>2</mn><mo>.</mo><mn>5</mn><mo>+</mo><mn>3</mn><mo>.</mo><mn>5</mn></mrow></mfrac></mstyle></math><br>जैसा की हम जानते है , <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mi mathvariant=\"normal\">a</mi><mo>+</mo><mi mathvariant=\"normal\">b</mi><mo>+</mo><mi mathvariant=\"normal\">c</mi><mo>)</mo></mrow><mn>2</mn></msup></math> = a<sup>2</sup> + b<sup>2</sup> + c<sup>2</sup> + 2ab + 2bc + 2ca<br><math display=\"inline\"><mfrac><mrow><msup><mrow><mo>(</mo><mn>1</mn><mo>.</mo><mn>5</mn><mo>+</mo><mn>2</mn><mo>.</mo><mn>5</mn><mi>&#160;</mi><mo>+</mo><mn>3</mn><mo>.</mo><mn>5</mn><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup></mrow><mrow><mn>1</mn><mo>.</mo><mn>5</mn><mo>+</mo><mn>2</mn><mo>.</mo><mn>5</mn><mi>&#160;</mi><mo>+</mo><mn>3</mn><mo>.</mo><mn>5</mn></mrow></mfrac></math> = 1.5 + 2.5 + 3.5 = 7.5</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "74",
                    section: "17",
                    question_en: "<p>74. The value of (<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>cosecA</mi><mrow><mi>cosecA</mi><mo>-</mo><mn>1</mn><mi mathvariant=\"normal\">&#160;</mi></mrow></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>cosecA</mi><mrow><mi>cosecA</mi><mo>+</mo><mn>1</mn></mrow></mfrac></math>) (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>secA</mi><mrow><mi>secA</mi><mo>-</mo><mn>1</mn></mrow></mfrac><mo>+</mo><mfrac><mi>secA</mi><mrow><mi>secA</mi><mo>+</mo><mn>1</mn></mrow></mfrac></math>)<sup>-1</sup> , when A = 60&deg; is:</p>",
                    question_hi: "<p>74. A = 60&deg; होने पर (<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>cosecA</mi><mrow><mi>cosecA</mi><mo>-</mo><mn>1</mn><mi mathvariant=\"normal\">&#160;</mi></mrow></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>cosecA</mi><mrow><mi>cosecA</mi><mo>+</mo><mn>1</mn></mrow></mfrac></math>) (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>secA</mi><mrow><mi>secA</mi><mo>-</mo><mn>1</mn></mrow></mfrac><mo>+</mo><mfrac><mi>secA</mi><mrow><mi>secA</mi><mo>+</mo><mn>1</mn></mrow></mfrac></math>)<sup>-1</sup> का मान क्या होगा ?</p>",
                    options_en: ["<p>4</p>", "<p>3</p>", 
                                "<p>1</p>", "<p>2</p>"],
                    options_hi: ["<p>4</p>", "<p>3</p>",
                                "<p>1</p>", "<p>2</p>"],
                    solution_en: "<p>74.(b)&nbsp;<br>(<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>cosecA</mi><mrow><mi>cosecA</mi><mo>-</mo><mn>1</mn><mi mathvariant=\"normal\">&#160;</mi></mrow></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>cosecA</mi><mrow><mi>cosecA</mi><mo>+</mo><mn>1</mn></mrow></mfrac></math>) (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>secA</mi><mrow><mi>secA</mi><mo>-</mo><mn>1</mn></mrow></mfrac><mo>+</mo><mfrac><mi>secA</mi><mrow><mi>secA</mi><mo>+</mo><mn>1</mn></mrow></mfrac></math>)<sup>-1</sup><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mfrac><mrow><msup><mi>cosec</mi><mn>2</mn></msup><mi mathvariant=\"normal\">A</mi><mi mathvariant=\"normal\">&#160;</mi><mo>+</mo><mi mathvariant=\"normal\">&#160;</mi><mi>cosecA</mi><mo>&#160;&#160;</mo><mo>+</mo><msup><mi>cosec</mi><mn>2</mn></msup><mi mathvariant=\"normal\">A</mi><mi mathvariant=\"normal\">&#160;</mi><mo>-</mo><mi mathvariant=\"normal\">&#160;</mi><mi>cosecA</mi><mi mathvariant=\"normal\">&#160;</mi></mrow><mrow><msup><mi>cosec</mi><mn>2</mn></msup><mi mathvariant=\"normal\">A</mi><mi mathvariant=\"normal\">&#160;</mi><mo>-</mo><mi mathvariant=\"normal\">&#160;</mi><msup><mrow><mi mathvariant=\"normal\">&#160;</mi><mn>1</mn></mrow><mn>2</mn></msup></mrow></mfrac><mfrac><mrow><msup><mi>sec</mi><mn>2</mn></msup><mi mathvariant=\"normal\">A</mi><mi mathvariant=\"normal\">&#160;</mi><mo>+</mo><mi mathvariant=\"normal\">&#160;</mi><mi>secA</mi><mi mathvariant=\"normal\">&#160;</mi><mo>+</mo><mi mathvariant=\"normal\">&#160;</mi><msup><mi>sec</mi><mn>2</mn></msup><mi mathvariant=\"normal\">A</mi><mi mathvariant=\"normal\">&#160;</mi><mo>-</mo><mi mathvariant=\"normal\">&#160;</mi><mi>secA</mi><mo>&#160;&#160;</mo><mi mathvariant=\"normal\">&#160;</mi></mrow><mrow><msup><mi>sec</mi><mn>2</mn></msup><mi mathvariant=\"normal\">A</mi><mi mathvariant=\"normal\">&#160;</mi><mo>-</mo><mi mathvariant=\"normal\">&#160;</mi><msup><mrow><mi mathvariant=\"normal\">&#160;</mi><mn>1</mn></mrow><mn>2</mn></msup><mi mathvariant=\"normal\">&#160;</mi></mrow></mfrac></mfrac></math><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><msup><mi>cosec</mi><mn>2</mn></msup><mi mathvariant=\"normal\">A</mi></mrow><mrow><msup><mi>cosec</mi><mn>2</mn></msup><mi mathvariant=\"normal\">A</mi><mi mathvariant=\"normal\">&#160;</mi><mo>-</mo><mi mathvariant=\"normal\">&#160;</mi><mrow><mi mathvariant=\"normal\">&#160;</mi><mn>1</mn></mrow></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi>sec</mi><mn>2</mn></msup><mi mathvariant=\"normal\">A</mi><mo>-</mo><mn>1</mn></mrow><mrow><mn>2</mn><msup><mi>sec</mi><mn>2</mn></msup><mi mathvariant=\"normal\">A</mi></mrow></mfrac></math><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi>cosec</mi><mn>2</mn></msup><mi mathvariant=\"normal\">A</mi></mrow><mrow><msup><mi>cot</mi><mn>2</mn></msup><mi mathvariant=\"normal\">A</mi></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi>tan</mi><mn>2</mn></msup><mi mathvariant=\"normal\">A</mi></mrow><mrow><msup><mi>sec</mi><mn>2</mn></msup><mi mathvariant=\"normal\">A</mi></mrow></mfrac></math><br>Now , A = 60&deg;<br>= <math display=\"inline\"><mfrac><mrow><mfrac><mrow><mn>4</mn></mrow><mrow><mn>3</mn></mrow></mfrac></mrow><mrow><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>4</mn></mfrac></math> = 4 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>4</mn></mfrac></math> = 3</p>",
                    solution_hi: "<p>74.(b)&nbsp;<br>(<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>cosecA</mi><mrow><mi>cosecA</mi><mo>-</mo><mn>1</mn><mi mathvariant=\"normal\">&#160;</mi></mrow></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>cosecA</mi><mrow><mi>cosecA</mi><mo>+</mo><mn>1</mn></mrow></mfrac></math>) (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>secA</mi><mrow><mi>secA</mi><mo>-</mo><mn>1</mn></mrow></mfrac><mo>+</mo><mfrac><mi>secA</mi><mrow><mi>secA</mi><mo>+</mo><mn>1</mn></mrow></mfrac></math>)<sup>-1</sup><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mfrac><mrow><msup><mi>cosec</mi><mn>2</mn></msup><mi mathvariant=\"normal\">A</mi><mi mathvariant=\"normal\">&#160;</mi><mo>+</mo><mi mathvariant=\"normal\">&#160;</mi><mi>cosecA</mi><mo>&#160;&#160;</mo><mo>+</mo><msup><mi>cosec</mi><mn>2</mn></msup><mi mathvariant=\"normal\">A</mi><mi mathvariant=\"normal\">&#160;</mi><mo>-</mo><mi mathvariant=\"normal\">&#160;</mi><mi>cosecA</mi><mi mathvariant=\"normal\">&#160;</mi></mrow><mrow><msup><mi>cosec</mi><mn>2</mn></msup><mi mathvariant=\"normal\">A</mi><mi mathvariant=\"normal\">&#160;</mi><mo>-</mo><mi mathvariant=\"normal\">&#160;</mi><msup><mrow><mi mathvariant=\"normal\">&#160;</mi><mn>1</mn></mrow><mn>2</mn></msup></mrow></mfrac><mfrac><mrow><msup><mi>sec</mi><mn>2</mn></msup><mi mathvariant=\"normal\">A</mi><mi mathvariant=\"normal\">&#160;</mi><mo>+</mo><mi mathvariant=\"normal\">&#160;</mi><mi>secA</mi><mi mathvariant=\"normal\">&#160;</mi><mo>+</mo><mi mathvariant=\"normal\">&#160;</mi><msup><mi>sec</mi><mn>2</mn></msup><mi mathvariant=\"normal\">A</mi><mi mathvariant=\"normal\">&#160;</mi><mo>-</mo><mi mathvariant=\"normal\">&#160;</mi><mi>secA</mi><mo>&#160;&#160;</mo><mi mathvariant=\"normal\">&#160;</mi></mrow><mrow><msup><mi>sec</mi><mn>2</mn></msup><mi mathvariant=\"normal\">A</mi><mi mathvariant=\"normal\">&#160;</mi><mo>-</mo><mi mathvariant=\"normal\">&#160;</mi><msup><mrow><mi mathvariant=\"normal\">&#160;</mi><mn>1</mn></mrow><mn>2</mn></msup><mi mathvariant=\"normal\">&#160;</mi></mrow></mfrac></mfrac></math><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><msup><mi>cosec</mi><mn>2</mn></msup><mi mathvariant=\"normal\">A</mi></mrow><mrow><msup><mi>cosec</mi><mn>2</mn></msup><mi mathvariant=\"normal\">A</mi><mi mathvariant=\"normal\">&#160;</mi><mo>-</mo><mi mathvariant=\"normal\">&#160;</mi><msup><mrow><mi mathvariant=\"normal\">&#160;</mi><mn>1</mn></mrow><mrow/></msup></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi>sec</mi><mn>2</mn></msup><mi mathvariant=\"normal\">A</mi><mo>-</mo><mn>1</mn></mrow><mrow><mn>2</mn><msup><mi>sec</mi><mn>2</mn></msup><mi mathvariant=\"normal\">A</mi></mrow></mfrac></math><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi>cosec</mi><mn>2</mn></msup><mi mathvariant=\"normal\">A</mi></mrow><mrow><msup><mi>cot</mi><mn>2</mn></msup><mi mathvariant=\"normal\">A</mi></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi>tan</mi><mn>2</mn></msup><mi mathvariant=\"normal\">A</mi></mrow><mrow><msup><mi>sec</mi><mn>2</mn></msup><mi mathvariant=\"normal\">A</mi></mrow></mfrac></math><br>अब , A = 60&deg;<br>= <math display=\"inline\"><mfrac><mrow><mfrac><mrow><mn>4</mn></mrow><mrow><mn>3</mn></mrow></mfrac></mrow><mrow><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>4</mn></mfrac></math> = 4 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>4</mn></mfrac></math> = 3</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "75",
                    section: "7",
                    question_en: "<p>75. 2 biscuits and 1 chocolate cost ₹69. 2 chocolates and 3 cups of coffee cost ₹127. 3 biscuits, 4 chocolates and 2 cups of coffee cost ₹229. Find the total cost (in ₹) of 5 biscuits, 5 chocolates and 5 cups of coffee.</p>",
                    question_hi: "<p>75. 2 बिस्कुट और 1 चॉकलेट की कीमत ₹69 है। 2 चॉकलेट और 3 कप कॉफी की कीमत ₹127 है। 3 बिस्कुट, 4 चॉकलेट और 2 कप कॉफी की कीमत ₹229 है। 5 बिस्कुट, 5 चॉकलेट और 5 कप कॉफी की कुल कीमत (₹ में) ज्ञात करें।</p>",
                    options_en: ["<p>304</p>", "<p>375</p>", 
                                "<p>345</p>", "<p>355</p>"],
                    options_hi: ["<p>304</p>", "<p>375</p>",
                                "<p>345</p>", "<p>355</p>"],
                    solution_en: "<p>75.(d) <br>Let cost of 1 biscuit, 1 chocolate, 1 cup coffee be <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">x</mi><mo>,</mo><mi mathvariant=\"normal\">&#160;</mi><mi mathvariant=\"normal\">y</mi><mo>,</mo><mi mathvariant=\"normal\">&#160;</mi><mi mathvariant=\"normal\">z</mi></math> respectively.<br>According to question,<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>2</mn><mi mathvariant=\"normal\">x</mi></math> + y = 69 &hellip; (i)<br>2y&nbsp;+ 3z = 127&hellip; (ii)<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>3</mn><mi mathvariant=\"normal\">x</mi></math> + 4y + 2z = 229&hellip; (iii)<br>Adding (i) and (iii) <br>5x&nbsp;+ 5y + 2z = 69 + 229<br>5x&nbsp;+ 5y + 2z = 298&hellip; (iv)<br>now,<br>Multiply eq. (i) by 2 and then subtracting eq. (ii) we get<br>4x&nbsp;- 3z = 138 - 127 <br>4x&nbsp;- 3z = 11 &hellip; (v)<br>Multiply eq. (ii) by 2 and then subtracting eq. (iii) we get<br>- 3x&nbsp;+ 4z = 254 - 229<br>- 3x&nbsp;+ 4z = 25 &hellip; (vi)<br>By solving eq. (v) and (vi) we get <br>z = 19<br>From eq. (iv) adding 3<math display=\"inline\"><mi>z</mi></math> we get<br>5x&nbsp;+ 5y + 5z = 298 + 19 &times; 3 = 298 + 57 = 355</p>",
                    solution_hi: "<p>75.(d) <br>माना 1 बिस्किट, 1 चॉकलेट, 1 कप कॉफी की लागत क्रमशः <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">x</mi><mo>,</mo><mi mathvariant=\"normal\">&#160;</mi><mi mathvariant=\"normal\">y</mi><mo>,</mo><mi mathvariant=\"normal\">&#160;</mi><mi mathvariant=\"normal\">z</mi></math> है।<br>प्रश्न के अनुसार,<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>2</mn><mi mathvariant=\"normal\">x</mi></math> + y = 69 &hellip; (i)<br>2y&nbsp;+ 3z = 127&hellip; (ii)<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>3</mn><mi mathvariant=\"normal\">x</mi></math> + 4y + 2z = 229&hellip; (iii)<br>(i) और (iii) जोड़ने पर <br>5<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">x</mi></math> + 5y + 2z = 69 + 229<br>5<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">x</mi></math> + 5y + 2z = 298&hellip; (iv)<br>अब,<br>समीकरण (i) को 2 से गुणा करें और फिर समीकरण (ii) को घटाने पर हमें प्राप्त होता है। <br>4x&nbsp;- 3z = 138 - 127 <br>4x&nbsp;- 3z = 11 &hellip; (v)<br>समीकरण (ii) को 2 से गुणा करें और फिर समीकरण (iii) को घटाने पर हमें प्राप्त होता है। <br>- 3x&nbsp;+ 4z = 254 - 229<br>- 3x&nbsp;+ 4z = 25 &hellip; (vi)<br>समीकरण (v) और (vi) को हल करने पर हमें प्राप्त होता है <br>z = 19<br>समीकरण (iv) में 3z जोड़ने पर हमें प्राप्त होता है<br>5x&nbsp;+ 5y + 5z = 298 + 19 &times; 3 = 298 + 57 = 355</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "76",
                    section: "7",
                    question_en: "<p>76. Choose the most appropriate ANTONYM for the underlined word in the given sentence. <br>The war was followed by a long period of <span style=\"text-decoration: underline;\">prosperity</span>.</p>",
                    question_hi: "<p>76. Choose the most appropriate ANTONYM for the underlined word in the given sentence. <br>The war was followed by a long period of <span style=\"text-decoration: underline;\">prosperity</span>.</p>",
                    options_en: ["<p>Security</p>", "<p>Secure</p>", 
                                "<p>Affluence</p>", "<p>Failure</p>"],
                    options_hi: ["<p>Security</p>", "<p>Secure</p>",
                                "<p>Affluence</p>", "<p>Failure</p>"],
                    solution_en: "<p>76.(d) <strong>Failure-</strong> lack of success.<br><strong>Prosperity- </strong>the state of being successful or flourishing.<br><strong>Security-</strong> the state of being free from danger or threat.<br><strong>Secure- </strong>to make safe or protect.<br><strong>Affluence-</strong> the state of having a great deal of wealth or abundance.</p>",
                    solution_hi: "<p>76.(d) <strong>Failure</strong> (असफलता)- lack of success.<br><strong>Prosperity</strong> (समृद्धि)- the state of being successful or flourishing.<br><strong>Security</strong> (सुरक्षा)- the state of being free from danger or threat.<br><strong>Secure </strong>(सुरक्षित)- to make safe or protect.<br><strong>Affluence</strong> (संपन्नता)- the state of having a great deal of wealth or abundance.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "77",
                    section: "7",
                    question_en: "<p>77. Select the option that can be used as a one-word substitute for the given phrase. <br>An important person who represents his/her country in a foreign country</p>",
                    question_hi: "<p>77. Select the option that can be used as a one-word substitute for the given phrase. <br>An important person who represents his/her country in a foreign country</p>",
                    options_en: ["<p>Amateur</p>", "<p>Ambassador</p>", 
                                "<p>Altruist</p>", "<p>Anonymous</p>"],
                    options_hi: ["<p>Amateur</p>", "<p>Ambassador</p>",
                                "<p>Altruist</p>", "<p>Anonymous</p>"],
                    solution_en: "<p>77.(b) <strong>Ambassador- </strong>an important person who represents his/her country in a foreign country.<br><strong>Amateur-</strong> a person who takes part in an activity for pleasure, not as a job.<br><strong>Altruist- </strong>a person who cares about others and helps them despite not gaining anything by doing this.<br><strong>Anonymous- </strong>of unknown authorship or origin.</p>",
                    solution_hi: "<p>77.(b) <strong>Ambassador</strong> (राजदूत)- an important person who represents his/her country in a foreign country.<br><strong>Amateur</strong> (शौकिया/शौकीन)- a person who takes part in an activity for pleasure, not as a job.<br><strong>Altruist</strong> (परोपकारी)- a person who cares about others and helps them despite not gaining anything by doing this.<br><strong>Anonymous</strong> (अज्ञात/गुमनाम)- of unknown authorship or origin.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "78",
                    section: "7",
                    question_en: "<p>78. Select the option that expresses the given sentence in passive voice. <br>Martha made pancakes yesterday.</p>",
                    question_hi: "<p>78. Select the option that expresses the given sentence in passive voice. <br>Martha made pancakes yesterday.</p>",
                    options_en: ["<p>Pancakes had been made by Martha yesterday.</p>", "<p>Pancakes were being made by Martha yesterday.</p>", 
                                "<p>Pancakes were made by Martha yesterday.</p>", "<p>Pancakes was made by Martha yesterday.</p>"],
                    options_hi: ["<p>Pancakes had been made by Martha yesterday.</p>", "<p>Pancakes were being made by Martha yesterday.</p>",
                                "<p>Pancakes were made by Martha yesterday.</p>", "<p>Pancakes was made by Martha yesterday.</p>"],
                    solution_en: "<p>78.(c) Pancakes were made by Martha yesterday. (Correct)<br>(a) Pancakes <span style=\"text-decoration: underline;\">had been made</span> by Martha yesterday. (Incorrect Tense)<br>(b) Pancakes <span style=\"text-decoration: underline;\">were being made</span> by Martha yesterday. (Incorrect Tense)<br>(d) Pancakes <span style=\"text-decoration: underline;\">was</span> made by Martha yesterday. (Incorrect Helping Verb)</p>",
                    solution_hi: "<p>78.(c) Pancakes were made by Martha yesterday. (Correct)<br>(a) Pancakes <span style=\"text-decoration: underline;\">had been made</span> by Martha yesterday. (गलत Tense)<br>(b) Pancakes <span style=\"text-decoration: underline;\">were being made</span> by Martha yesterday. (गलत Tense)<br>(d) Pancakes <span style=\"text-decoration: underline;\">was</span> made by Martha yesterday. (गलत Helping Verb)</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "79",
                    section: "7",
                    question_en: "79. In the given question, a statement divided into different segments is given. Rearrange all the segments to form a coherent statement. <br />O: also benefit from knowing <br />P: there are several reasons <br />Q: why even parents would <br />R: more about how and why <br />S: children do the things that they do",
                    question_hi: "79. In the given question, a statement divided into different segments is given. Rearrange all the segments to form a coherent statement. <br />O: also benefit from knowing <br />P: there are several reasons <br />Q: why even parents would <br />R: more about how and why <br />S: children do the things that they do",
                    options_en: [" RQOPS ", " OPQSR  ", 
                                " PQORS ", " PQROS"],
                    options_hi: [" RQOPS ", " OPQSR  ",
                                " PQORS ", " PQROS"],
                    solution_en: "79.(c) PQORS<br />The given sentence starts with Part P as it introduces the main idea of the sentence, i.e. ‘There are several reasons’. Part Q starts the question related to parents and Part O continues the question by asking about the benefit from knowing something. So, O will follow Q. Further, Part R introduces another question by asking how and why & Part S continues the question by enquiring about the actions of children. So, S will follow R. Going through the options, option ‘c’ has the correct sequence.",
                    solution_hi: "79.(c) PQORS<br />दिया गया sentence, Part P से प्रारंभ होता है क्योंकि यह sentence के मुख्य विचार ‘There are several reasons’ का परिचय देता है। Part Q माता-पिता (parents) से संबंधित प्रश्न शुरू करता है और Part O कुछ जानने से होने वाले लाभ के बारे में पूछकर प्रश्न को जारी रखता है। इसलिए, Q के बाद O आएगा। इसके अलावा, Part R, how और why पूछकर एक और प्रश्न प्रस्तुत करता है और Part S बच्चों के actions के बारे में पूछताछ करके प्रश्न को जारी रखता है। अतः options के माध्यम से जाने पर,  option ‘c’ में सही sequence है।",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "80",
                    section: "7",
                    question_en: "<p>80. Select the most appropriate meaning of the given idiom. <br>Radhika stood by Monica&rsquo;s side <span style=\"text-decoration: underline;\">through thick and thin</span>.</p>",
                    question_hi: "<p>80. Select the most appropriate meaning of the given idiom. <br>Radhika stood by Monica&rsquo;s side <span style=\"text-decoration: underline;\">through thick and thin</span>.</p>",
                    options_en: ["<p>Under easy situations wherein she could ensure Monica&rsquo;s safety and security</p>", "<p>In all the happy moments, personally spending most of her time</p>", 
                                "<p>Under all conditions, no matter how challenging or difficult</p>", "<p>In the moments when Monica couldn&rsquo;t complete some simple tasks </p>"],
                    options_hi: ["<p>Under easy situations wherein she could ensure Monica&rsquo;s safety and security</p>", "<p>In all the happy moments, personally spending most of her time</p>",
                                "<p>Under all conditions, no matter how challenging or difficult</p>", "<p>In the moments when Monica couldn&rsquo;t complete some simple tasks</p>"],
                    solution_en: "<p>80.(c) <strong>Through thick and thin-</strong> under all conditions, no matter how challenging or difficult.</p>",
                    solution_hi: "<p>80.(c) <strong>Through thick and thin- </strong>under all conditions, no matter how challenging or difficult./सभी परिस्थितियों में, चाहे कितनी भी चुनौतीपूर्ण हो।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "81",
                    section: "7",
                    question_en: "<p>81. Give one word substitution for : <br>A person who is unsure about God&rsquo;s existence</p>",
                    question_hi: "<p>81. Give one word substitution for : <br>A person who is unsure about God&rsquo;s existence</p>",
                    options_en: ["<p>Ignorant</p>", "<p>Atheist</p>", 
                                "<p>Agnostic</p>", "<p>Theist</p>"],
                    options_hi: ["<p>Ignorant</p>", "<p>Atheist</p>",
                                "<p>Agnostic</p>", "<p>Theist</p>"],
                    solution_en: "<p>81.(c) <strong>Agnostic- </strong>a person who is unsure about God&rsquo;s existence.<br><strong>Ignorant-</strong> having no knowledge or awareness of something or of things in general.<br><strong>Atheist- </strong>one who does not believe in the existence of god.<br><strong>Theist-</strong> one who believes in the existence of god.</p>",
                    solution_hi: "<p>81.(c) <strong>Agnostic</strong> (अज्ञेयवादी/संशयवादी)- a person who is unsure about God&rsquo;s existence.<br><strong>Ignorant</strong> (अज्ञानी)- having no knowledge or awareness of something or of things in general.<br><strong>Atheist</strong> (नास्तिक)- one who does not believe in the existence of god.<br><strong>Theist </strong>(आस्तिक)- one who believes in the existence of god.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "82",
                    section: "7",
                    question_en: "<p>82. Select the most appropriate synonym of the underlined word in the given sentence. <br>His approach was filled with such <span style=\"text-decoration: underline;\">perseverance</span> that it was difficult to overlook his merit.</p>",
                    question_hi: "<p>82. Select the most appropriate synonym of the underlined word in the given sentence. <br>His approach was filled with such <span style=\"text-decoration: underline;\">perseverance</span> that it was difficult to overlook his merit.</p>",
                    options_en: ["<p>Ignorance</p>", "<p>Doggedness</p>", 
                                "<p>Reluctance</p>", "<p>Monotonous</p>"],
                    options_hi: ["<p>Ignorance</p>", "<p>Doggedness</p>",
                                "<p>Reluctance</p>", "<p>Monotonous</p>"],
                    solution_en: "<p>82.(b) <strong>Doggedness-</strong> persistence and determination.<br><strong>Perseverance-</strong> continued effort to achieve something despite difficulties.<br><strong>Ignorance-</strong> lack of knowledge or awareness.<br><strong>Reluctance-</strong> unwillingness or hesitation.<br><strong>Monotonous-</strong> dull, repetitive, and lacking variety.</p>",
                    solution_hi: "<p>82.(b) <strong>Doggedness</strong> (दृढ़ संकल्प)- persistence and determination.<br><strong>Perseverance</strong> (दृढता/अटलता)- continued effort to achieve something despite difficulties.<br><strong>Ignorance</strong> (अज्ञानता)- lack of knowledge or awareness.<br><strong>Reluctance</strong> (अनिच्छा)- unwillingness or hesitation.<br><strong>Monotonous</strong> (नीरस)- dull, repetitive, and lacking variety.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "83",
                    section: "7",
                    question_en: "<p>83. Select the most appropriate option that can substitute the underlined part in the following sentence. Everyone called the reporter <span style=\"text-decoration: underline;\">a daredevil</span> after he came back safely from the war zone.</p>",
                    question_hi: "<p>83. Select the most appropriate option that can substitute the underlined part in the following sentence. Everyone called the reporter <span style=\"text-decoration: underline;\">a daredevil</span> after he came back safely from the war zone.</p>",
                    options_en: ["<p>a common man</p>", "<p>a fearless person</p>", 
                                "<p>an insignificant person</p>", "<p>a hypocrite</p>"],
                    options_hi: ["<p>a common man</p>", "<p>a fearless person</p>",
                                "<p>an insignificant person</p>", "<p>a hypocrite</p>"],
                    solution_en: "<p>83.(b) A daredevil- a fearless person.</p>",
                    solution_hi: "<p>83.(b) A daredevil- a fearless person./ निडर व्यक्ति।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "84",
                    section: "7",
                    question_en: "<p>84. Parts of the given sentence have been underlined. Identify the part that has an error. Select \'No error\' if there is no error. <br>Although the team had <span style=\"text-decoration: underline;\">trained diligently for months</span>, they <span style=\"text-decoration: underline;\">had still overwhelmed</span> by the opponent\'s unexpected tactics.</p>",
                    question_hi: "<p>84. Parts of the given sentence have been underlined. Identify the part that has an error. Select \'No error\' if there is no error. <br>Although the team had <span style=\"text-decoration: underline;\">trained diligently for months</span>, they <span style=\"text-decoration: underline;\">had still overwhelmed</span> by the opponent\'s unexpected tactics.</p>",
                    options_en: ["<p>No error</p>", "<p>had still overwhelmed</p>", 
                                "<p>trained diligently</p>", "<p>for months</p>"],
                    options_hi: ["<p>No error</p>", "<p>had still overwhelmed</p>",
                                "<p>trained diligently</p>", "<p>for months</p>"],
                    solution_en: "<p>84.(b) had still overwhelmed<br>&lsquo;Had&rsquo; must be replaced with &lsquo;were&rsquo; as the given sentence is an example of a passive voice of simple past tense and &lsquo;Plural subject (they) + were + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi mathvariant=\"normal\">V</mi><mn>3</mn></msub></math>&rsquo; is the correct grammatical structure for it. Hence, &lsquo;were still overwhelmed(<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi mathvariant=\"normal\">V</mi><mn>3</mn></msub></math>)&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>84.(b) had still overwhelmed<br>&lsquo;Had&rsquo; के स्थान पर &lsquo;were&rsquo; का प्रयोग होगा, क्योंकि दिया गया sentence, simple past tense के passive voice का example है और इसके लिए सही grammatical structure, &lsquo;Plural subject (they) + were + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi mathvariant=\"normal\">V</mi><mn>3</mn></msub></math>&rsquo; है। अतः, &lsquo;were still overwhelmed(<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi mathvariant=\"normal\">V</mi><mn>3</mn></msub></math>)&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "85",
                    section: "7",
                    question_en: "<p>85. Select the most appropriate ANTONYM of the underlined word. <br>If someone <span style=\"text-decoration: underline;\">babbles</span>, it means that they talk in a confused or excited way.</p>",
                    question_hi: "<p>85. Select the most appropriate ANTONYM of the underlined word. <br>If someone <span style=\"text-decoration: underline;\">babbles</span>, it means that they talk in a confused or excited way.</p>",
                    options_en: ["<p>Gossips</p>", "<p>Quiet</p>", 
                                "<p>Talkative</p>", "<p>Rants</p>"],
                    options_hi: ["<p>Gossips</p>", "<p>Quiet</p>",
                                "<p>Talkative</p>", "<p>Rants</p>"],
                    solution_en: "<p>85.(b) <strong>Quiet- </strong>making little or no noise.<br><strong>Babbles- </strong>the sound of people talking simultaneously.<br><strong>Gossips- l</strong>ight informal conversation for social occasions..<br><strong>Talkative-</strong> inclined to talk a great deal.<br><strong>Rants- </strong>speaks or shouts at length in a wild, impassioned way.</p>",
                    solution_hi: "<p>85.(b) <strong>Quiet</strong> (शांत)- making little or no noise.<br><strong>Babbles</strong> (बड़बड़ाहट)- the sound of people talking simultaneously.<br><strong>Gossips</strong> (गपशप)- light informal conversation for social occasions..<br><strong>Talkative</strong> (वाचाल)- inclined to talk a great deal.<br><strong>Rants</strong> (बड़बड़ाना)- speaks or shouts at length in a wild, impassioned way.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "86",
                    section: "7",
                    question_en: "<p>86. Select the most appropriate ANTONYM of the word in the brackets to fill in the blank.<br>Akash went to ______ his school uniform. [sell]</p>",
                    question_hi: "<p>86. Select the most appropriate ANTONYM of the word in the brackets to fill in the blank.<br>Akash went to ______ his school uniform. [sell]</p>",
                    options_en: ["<p>loan</p>", "<p>rent</p>", 
                                "<p>buy</p>", "<p>make</p>"],
                    options_hi: ["<p>loan</p>", "<p>rent</p>",
                                "<p>buy</p>", "<p>make</p>"],
                    solution_en: "<p>86.(c) Buy<br><strong>Loan- </strong>a thing that is borrowed, especially a sum of money that is expected to be paid back with interest.<br><strong>Rent-</strong> a regular payment made by a tenant to an owner for the use of land, a building, or other property.</p>",
                    solution_hi: "<p>86.(c) Buy<br><strong>Loan</strong> (ऋण/कर्ज)- a thing that is borrowed, especially a sum of money that is expected to be paid back with interest.<br><strong>Rent</strong> (किराया)- a regular payment made by a tenant to an owner for the use of land, a building, or other property.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "87",
                    section: "7",
                    question_en: "<p>87. Select the most appropriate option to substitute the underlined word segment in the given sentence. Carnatic music is usually performed by a <span style=\"text-decoration: underline;\">small troupe of actors</span>, consisting of a principal performer, a melodic accompaniment, and a rhythm accompaniment.</p>",
                    question_hi: "<p>87. Select the most appropriate option to substitute the underlined word segment in the given sentence. Carnatic music is usually performed by a <span style=\"text-decoration: underline;\">small troupe of actors</span>, consisting of a principal performer, a melodic accompaniment, and a rhythm accompaniment.</p>",
                    options_en: ["<p>board of directors</p>", "<p>ensemble of musicians</p>", 
                                "<p>panel of judges</p>", "<p>band of singers</p>"],
                    options_hi: ["<p>board of directors</p>", "<p>ensemble of musicians</p>",
                                "<p>panel of judges</p>", "<p>band of singers</p>"],
                    solution_en: "<p>87.(b) ensemble of musicians<br>&lsquo;Ensemble&rsquo; means a group of musicians, dancers, or actors who perform together. The given sentence talks about carnatic music, so the correct phrase to use is &lsquo;ensemble of musicians&rsquo;. Hence, &lsquo;ensemble of musicians&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>87.(b) ensemble of musicians<br>&lsquo;Ensemble&rsquo; का अर्थ है musicians, dancers या actors का समूह जो एक साथ perform करते हैं। दिया गया sentence कर्नाटक संगीत के बारे में बात करता है, इसलिए उपयोग करने के लिए सही phrase, &lsquo;ensemble of musicians&rsquo; है। अतः, &lsquo;ensemble of musicians&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "88",
                    section: "7",
                    question_en: "<p>88. Select the option that can be used as a one-word substitute for the given group of words. <br>One who speaks less in a forum.</p>",
                    question_hi: "<p>88. Select the option that can be used as a one-word substitute for the given group of words. <br>One who speaks less in a forum.</p>",
                    options_en: ["<p>Eccentric</p>", "<p>Loquacious</p>", 
                                "<p>Gabby</p>", "<p>Reticent</p>"],
                    options_hi: ["<p>Eccentric</p>", "<p>Loquacious</p>",
                                "<p>Gabby</p>", "<p>Reticent</p>"],
                    solution_en: "<p>88.(d)<strong> Reticent-</strong> one who speaks less in a forum.<br><strong>Eccentric- </strong>a person of unconventional and slightly strange views or behaviour.<br><strong>Loquacious- </strong>full of excessive talk.<br><strong>Gabby-</strong> excessively or annoyingly talkative.</p>",
                    solution_hi: "<p>88.(d) <strong>Reticent </strong>(मितभाषी)- one who speaks less in a forum.<br><strong>Eccentric</strong> (विलक्षण/सनकी व्यक्ति)- a person of unconventional and slightly strange views or behaviour.<br><strong>Loquacious</strong> (वाचाल)- full of excessive talk.<br><strong>Gabby</strong> (बातूनी)- excessively or annoyingly talkative.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "89",
                    section: "7",
                    question_en: "89. Parts of a sentence are given below in jumbled order. Select the option that gives their correct logical sequence and forms a meaningful sentence. <br />In November, Lakshman fell (P) / with fever (Q) / seriously ill (R) and pneumonia (S). ",
                    question_hi: "89. Parts of a sentence are given below in jumbled order. Select the option that gives their correct logical sequence and forms a meaningful sentence. <br />In November, Lakshman fell (P) / with fever (Q) / seriously ill (R) and pneumonia (S). ",
                    options_en: [" P, Q, S, R ", " P, R, Q, S ", 
                                " P, Q, R, S ", " R, Q, S, P"],
                    options_hi: [" P, Q, S, R ", " P, R, Q, S ",
                                " P, Q, R, S ", " R, Q, S, P"],
                    solution_en: "89.(b) P, R, Q, S<br />The correct sentence is “In November, Lakshman fell seriously ill with fever and pneumonia.”",
                    solution_hi: "89.(b) P, R, Q, S<br /> “In November, Lakshman fell seriously ill with fever and pneumonia.” सही sentence है। ",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "90",
                    section: "7",
                    question_en: "<p>90. In the following sentence, four words are underlined out of which one word is misspelt. Identify the INCORRECTLY spelt word. <br>I felt <span style=\"text-decoration: underline;\">absolutely</span> (A) fit at the summit. My mind was absolutely <span style=\"text-decoration: underline;\">clear</span>.(B) I didn&rsquo;t feel tired, I felt <span style=\"text-decoration: underline;\">exhelarated</span>. (C)It was a very clear <span style=\"text-decoration: underline;\">sensation</span>.(D)</p>",
                    question_hi: "<p>90. In the following sentence, four words are underlined out of which one word is misspelt. Identify the INCORRECTLY spelt word. <br>I felt <span style=\"text-decoration: underline;\">absolutely</span> (A) fit at the summit. My mind was absolutely <span style=\"text-decoration: underline;\">clear</span>.(B) I didn&rsquo;t feel tired, I felt <span style=\"text-decoration: underline;\">exhelarated</span>. (C)It was a very clear <span style=\"text-decoration: underline;\">sensation</span>.(D)</p>",
                    options_en: ["<p>C</p>", "<p>D</p>", 
                                "<p>B</p>", "<p>A</p>"],
                    options_hi: ["<p>C</p>", "<p>D</p>",
                                "<p>B</p>", "<p>A</p>"],
                    solution_en: "<p>90.(a) C <span style=\"text-decoration: underline;\">exhelarated</span><br>\'Exhilarated\' is the correct spelling.</p>",
                    solution_hi: "<p>90.(a) C <span style=\"text-decoration: underline;\">exhelarated</span><br>\'Exhilarated\' सही spelling है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "91",
                    section: "7",
                    question_en: "<p>91. The following sentence has been divided into five segments. The first segment has no error. Select the option that has the segment with the grammatical error. <br>Due to the pandemic, / the prices of / medicines increased / every day since / last December.</p>",
                    question_hi: "<p>91. The following sentence has been divided into five segments. The first segment has no error. Select the option that has the segment with the grammatical error. <br>Due to the pandemic, / the prices of / medicines increased / every day since / last December.</p>",
                    options_en: ["<p>every day since</p>", "<p>medicines increased</p>", 
                                "<p>last December.</p>", "<p>the prices of</p>"],
                    options_hi: ["<p>every day since</p>", "<p>medicines increased</p>",
                                "<p>last December.</p>", "<p>the prices of</p>"],
                    solution_en: "<p>91.(b) medicines increased<br>We use present continuous tense with &lsquo;for/since&rsquo; to show that something has been happening for a period of time. &lsquo;Plural subject (prices) + have been + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi mathvariant=\"normal\">V</mi><mi>ing</mi></msub></math>&rsquo; is the correct grammatical structure for it. Hence, &lsquo;medicines have been increasing&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>91.(b) medicines increased<br>\'For/since\' के साथ present continuous tense का प्रयोग यह दर्शाने के लिए किया जाता हैं कि कोई चीज कुछ समय से घटित हो रही है। इसके लिए सही grammatical structure, &lsquo;Plural subject (prices) + have been + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi mathvariant=\"normal\">V</mi><mi>ing</mi></msub></math>&rsquo; है। अतः, &lsquo;medicines have been increasing&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "92",
                    section: "7",
                    question_en: "<p>92. Select the option that expresses the given sentence in active voice. <br>I will be gifted a fancy doll by them the next day.</p>",
                    question_hi: "<p>92. Select the option that expresses the given sentence in active voice. <br>I will be gifted a fancy doll by them the next day.</p>",
                    options_en: ["<p>They gifted me a fancy doll yesterday.</p>", "<p>They are gifting me a fancy doll the next day.</p>", 
                                "<p>They gift me a fancy doll the previous day.</p>", "<p>They will gift me a fancy doll tomorrow.</p>"],
                    options_hi: ["<p>They gifted me a fancy doll yesterday.</p>", "<p>They are gifting me a fancy doll the next day.</p>",
                                "<p>They gift me a fancy doll the previous day.</p>", "<p>They will gift me a fancy doll tomorrow.</p>"],
                    solution_en: "<p>92.(d) They will gift me a fancy doll tomorrow. (Correct)<br>(a) They <span style=\"text-decoration: underline;\">gifted</span> me a fancy doll <span style=\"text-decoration: underline;\">yesterday</span>. (Incorrect Tense &amp; Adverb)<br>(b) They <span style=\"text-decoration: underline;\">are gifting</span> me a fancy doll <span style=\"text-decoration: underline;\">the next day</span>. (Incorrect Tense &amp; Adverb)<br>(c) They <span style=\"text-decoration: underline;\">gift</span> me a fancy doll <span style=\"text-decoration: underline;\">the previous day</span>. (Incorrect Tense &amp; Adverb)</p>",
                    solution_hi: "<p>92.(d) They will gift me a fancy doll tomorrow. (Correct)<br>(a) They <span style=\"text-decoration: underline;\">gifted</span> me a fancy doll <span style=\"text-decoration: underline;\">yesterday</span>. (गलत Tense एवं Adverb)<br>(b) They <span style=\"text-decoration: underline;\">are gifting</span> me a fancy doll <span style=\"text-decoration: underline;\">the next day</span>. (गलत Tense एवं Adverb)<br>(c) They <span style=\"text-decoration: underline;\">gift</span> me a fancy doll <span style=\"text-decoration: underline;\">the previous day</span>. (गलत Tense एवं Adverb)</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "93",
                    section: "7",
                    question_en: "93. Select the most appropriate option that can rectify the incorrectly spelt word in the following sentence. The government has ordered for the maintainance of the roads. ",
                    question_hi: "93. Select the most appropriate option that can rectify the incorrectly spelt word in the following sentence. The government has ordered for the maintainance of the roads. ",
                    options_en: [" maintenance", " maintainence ", 
                                " maintainiance", " maintennance"],
                    options_hi: [" maintenance", " maintainence ",
                                " maintainiance", " maintennance"],
                    solution_en: "93.(a) maintenance<br />\'Maintenance\' is the correct spelling.",
                    solution_hi: "93.(a) maintenance<br />\'Maintenance\' सही spelling है। ",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "94",
                    section: "7",
                    question_en: "<p>94. Select the most appropriate meaning of the given word. <br>Illiterate</p>",
                    question_hi: "<p>94. Select the most appropriate meaning of the given word. <br>Illiterate</p>",
                    options_en: ["<p>A person who cannot speak</p>", "<p>One who is well-educated</p>", 
                                "<p>A person who cannot read and write</p>", "<p>Highly rated thing</p>"],
                    options_hi: ["<p>A person who cannot speak</p>", "<p>One who is well-educated</p>",
                                "<p>A person who cannot read and write</p>", "<p>Highly rated thing</p>"],
                    solution_en: "<p>94.(c) <strong>Illiterate- </strong>a person who cannot read and write.</p>",
                    solution_hi: "<p>94.(c)<strong> Illiterate-</strong> a person who cannot read and write./अनपढ़ ।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "95",
                    section: "7",
                    question_en: "<p>95. Select the most appropriate ANTONYM of the given word. <br>Affirmative</p>",
                    question_hi: "<p>95. Select the most appropriate ANTONYM of the given word. <br>Affirmative</p>",
                    options_en: ["<p>Sanction</p>", "<p>Negative</p>", 
                                "<p>Acquiescence</p>", "<p>Accession</p>"],
                    options_hi: ["<p>Sanction</p>", "<p>Negative</p>",
                                "<p>Acquiescence</p>", "<p>Accession</p>"],
                    solution_en: "<p>95.(b) <strong>Negative</strong><br><strong>Affirmative- </strong>positive or showing agreement.<br><strong>Sanction- </strong>official permission or approval for an action.<br><strong>Acquiescence- </strong>passive acceptance or submission without protest.<br><strong>Accession- </strong>the act of agreeing or consenting to a demand, request, or treaty.</p>",
                    solution_hi: "<p>95.(b) <strong>Negative</strong><br><strong>Affirmative</strong> (सकारात्मक)- positive or showing agreement.<br><strong>Sanction</strong> (स्वीकृति)- official permission or approval for an action.<br><strong>Acquiescence </strong>(सहमति)- passive acceptance or submission without protest.<br><strong>Accession </strong>(परिग्रहण-)- the act of agreeing or consenting to a demand, request, or treaty.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "96",
                    section: "7",
                    question_en: "<p>96. <strong>Cloze Test:&nbsp;</strong><br>(96) ______a letter came to inform them that Raka&rsquo;s mother Tara had another breakdown and was in a nursing home in Geneva and that Raka&rsquo;s grandmother Asha, (97) ______seen another grandchild safely into the world, was flying to Switzerland to be with her, Nanda Kaul (98) _______ her lips, (99) _______ the blue sheets of paper with that distasteful sprawl (100) ______them, and hid them in her desk.&nbsp;<br>Select the most appropriate option to fill in blank number 96.</p>",
                    question_hi: "<p>96. <strong>Cloze Test:&nbsp;</strong><br>(96) ______a letter came to inform them that Raka&rsquo;s mother Tara had another breakdown and was in a nursing home in Geneva and that Raka&rsquo;s grandmother Asha, (97) ______seen another grandchild safely into the world, was flying to Switzerland to be with her, Nanda Kaul (98) _______ her lips, (99) _______ the blue sheets of paper with that distasteful sprawl (100) ______them, and hid them in her desk.&nbsp;<br>Select the most appropriate option to fill in blank number 96.</p>",
                    options_en: ["<p>When</p>", "<p>Sooner</p>", 
                                "<p>For</p>", "<p>Unless</p>"],
                    options_hi: ["<p>When</p>", "<p>Sooner</p>",
                                "<p>For</p>", "<p>Unless</p>"],
                    solution_en: "<p>96.(a) When<br>&lsquo;When&rsquo; is a relative adverb used to indicate the time of an action or event. Similarly, in the given passage, it tells us the time of the event &lsquo;Nanda kaul pursing her lips&rsquo;. Hence, &lsquo;When&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>96.(a) When<br>&lsquo;When&rsquo; एक relative adverb है जिसका use किसी action) या event के समय को इंगित करने के लिए किया जाता है। इसी तरह, दिए गए passage में, यह Nanda kaul के होंठ सिकुड़ने के action का समय बताता है। अतः, &lsquo;When&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "97",
                    section: "7",
                    question_en: "<p>97. <strong>Cloze Test:&nbsp;</strong><br>(96) ______a letter came to inform them that Raka&rsquo;s mother Tara had another breakdown and was in a nursing home in Geneva and that Raka&rsquo;s grandmother Asha, (97) ______seen another grandchild safely into the world, was flying to Switzerland to be with her, Nanda Kaul (98) _______ her lips, (99) _______ the blue sheets of paper with that distasteful sprawl (100) ______them, and hid them in her desk.&nbsp;<br>Select the most appropriate option to fill in blank number 97.</p>",
                    question_hi: "<p>97. <strong>Cloze Test:&nbsp;</strong><br>(96) ______a letter came to inform them that Raka&rsquo;s mother Tara had another breakdown and was in a nursing home in Geneva and that Raka&rsquo;s grandmother Asha, (97) ______seen another grandchild safely into the world, was flying to Switzerland to be with her, Nanda Kaul (98) _______ her lips, (99) _______ the blue sheets of paper with that distasteful sprawl (100) ______them, and hid them in her desk.&nbsp;<br>Select the most appropriate option to fill in blank number 97.</p>",
                    options_en: ["<p>have</p>", "<p>has</p>", 
                                "<p>had</p>", "<p>having</p>"],
                    options_hi: ["<p>have</p>", "<p>has</p>",
                                "<p>had</p>", "<p>having</p>"],
                    solution_en: "<p>97.(d) having<br>&lsquo;Having + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi mathvariant=\"normal\">V</mi><mn>3</mn></msub></math>&rsquo; is a perfect participle used to denote an action happening immediately after it. Similarly, in the given passage, the action &ldquo;flying to Switzerland&rdquo; happens immediately after the action &ldquo;seeing another grandchild&rdquo;. Hence, &lsquo;having&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>97.(d) having<br>&lsquo;Having + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi mathvariant=\"normal\">V</mi><mn>3</mn></msub></math>&rsquo; एक perfect participle है जिसका प्रयोग इसके तुरंत बाद होने वाले action को दर्शाने के लिए किया जाता है। इसी प्रकार, दिए गए passage में, action &ldquo;seeing another grandchild&rdquo; के तुरंत बाद action &ldquo;flying to Switzerland&rdquo; होता है। अतः, &lsquo;having&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "98",
                    section: "7",
                    question_en: "<p>98.<strong> Cloze Test:</strong>&nbsp;<br>(96) ______a letter came to inform them that Raka&rsquo;s mother Tara had another breakdown and was in a nursing home in Geneva and that Raka&rsquo;s grandmother Asha, (97) ______seen another grandchild safely into the world, was flying to Switzerland to be with her, Nanda Kaul (98) _______ her lips, (99) _______ the blue sheets of paper with that distasteful sprawl (100) ______them, and hid them in her desk.&nbsp;<br>Select the most appropriate option to fill in blank number 98.</p>",
                    question_hi: "<p>98.<strong> Cloze Test:</strong>&nbsp;<br>(96) ______a letter came to inform them that Raka&rsquo;s mother Tara had another breakdown and was in a nursing home in Geneva and that Raka&rsquo;s grandmother Asha, (97) ______seen another grandchild safely into the world, was flying to Switzerland to be with her, Nanda Kaul (98) _______ her lips, (99) _______ the blue sheets of paper with that distasteful sprawl (100) ______them, and hid them in her desk.&nbsp;<br>Select the most appropriate option to fill in blank number 98.</p>",
                    options_en: ["<p>stuck</p>", "<p>dirt</p>", 
                                "<p>dusted</p>", "<p>pursed</p>"],
                    options_hi: ["<p>stuck</p>", "<p>dirt</p>",
                                "<p>dusted</p>", "<p>pursed</p>"],
                    solution_en: "<p>98.(d) pursed<br>&lsquo;Purse&rsquo; means to bring your lips tightly together so that they form a rounded shape, usually as an expression of disapproval. The given passage states that Nana Kaul pursed her lips. Hence, &lsquo;pursed&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>98.(d) pursed<br>&lsquo;Purse&rsquo; का अर्थ है गोल आकार में होठ को सिकोड़ना, जो आमतौर पर अस्वीकृति (disapproval) का expression होता है। दिए गए passage में कहा गया है कि Nana Kaul ने अपने होठों को सिकोड़ा। अतः, &lsquo;pursed&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "99",
                    section: "7",
                    question_en: "<p>99.<strong> Cloze Test:</strong>&nbsp;<br>(96) ______a letter came to inform them that Raka&rsquo;s mother Tara had another breakdown and was in a nursing home in Geneva and that Raka&rsquo;s grandmother Asha, (97) ______seen another grandchild safely into the world, was flying to Switzerland to be with her, Nanda Kaul (98) _______ her lips, (99) _______ the blue sheets of paper with that distasteful sprawl (100) ______them, and hid them in her desk.&nbsp;<br>Select the most appropriate option to fill in blank number 99.</p>",
                    question_hi: "<p>99.<strong> Cloze Test:</strong>&nbsp;<br>(96) ______a letter came to inform them that Raka&rsquo;s mother Tara had another breakdown and was in a nursing home in Geneva and that Raka&rsquo;s grandmother Asha, (97) ______seen another grandchild safely into the world, was flying to Switzerland to be with her, Nanda Kaul (98) _______ her lips, (99) _______ the blue sheets of paper with that distasteful sprawl (100) ______them, and hid them in her desk.&nbsp;<br>Select the most appropriate option to fill in blank number 99.</p>",
                    options_en: ["<p>folded up</p>", "<p>cleared up</p>", 
                                "<p>hid away</p>", "<p>tucked away</p>"],
                    options_hi: ["<p>folded up</p>", "<p>cleared up</p>",
                                "<p>hid away</p>", "<p>tucked away</p>"],
                    solution_en: "<p>99.(a) folded up<br>The given passage states that Nanda Kaul folded up the blue sheets of paper with that distasteful sprawl across them. Hence, &lsquo;folded up&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>99.(a) folded up<br>दिए गए passage में बताया गया है कि Nanda Kaul ने कागज़ की blue sheets को उस अप्रिय फैलाव के साथ मोड़ दिया। अतः, &lsquo;folded up&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "100",
                    section: "misc",
                    question_en: "<p>100.<strong> Cloze Test:</strong>&nbsp;<br>(96) ______a letter came to inform them that Raka&rsquo;s mother Tara had another breakdown and was in a nursing home in Geneva and that Raka&rsquo;s grandmother Asha, (97) ______seen another grandchild safely into the world, was flying to Switzerland to be with her, Nanda Kaul (98) _______ her lips, (99) _______ the blue sheets of paper with that distasteful sprawl (100) ______them, and hid them in her desk.&nbsp;<br>Select the most appropriate option to fill in blank number 100.</p>",
                    question_hi: "<p>100.<strong> Cloze Test:</strong>&nbsp;<br>(96) ______a letter came to inform them that Raka&rsquo;s mother Tara had another breakdown and was in a nursing home in Geneva and that Raka&rsquo;s grandmother Asha, (97) ______seen another grandchild safely into the world, was flying to Switzerland to be with her, Nanda Kaul (98) _______ her lips, (99) _______ the blue sheets of paper with that distasteful sprawl (100) ______them, and hid them in her desk.&nbsp;<br>Select the most appropriate option to fill in blank number 100.</p>",
                    options_en: ["<p>across</p>", "<p>at</p>", 
                                "<p>in</p>", "<p>by</p>"],
                    options_hi: ["<p>across</p>", "<p>at</p>",
                                "<p>in</p>", "<p>by</p>"],
                    solution_en: "<p>100.(a) across<br>&lsquo;Across&rsquo; means from one side to the other. The given passage states that Nana Kaul pursed her lips, folded up the blue sheets of paper with that distasteful sprawl across them, and hid them in her desk. Hence, &lsquo;across&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>100.(a) across<br>&lsquo;Across&rsquo; का अर्थ है एक तरफ से दूसरी तरफ। दिए गए passage में बताया गया है कि Nana Kaul ने अपने होठों को सिकोड़ा, कागज़ की blue sheets को उस अप्रिय फैलाव के साथ मोड़ा और उन्हें अपनी मेज़ में छिपा दिया। अतः, &lsquo;across&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>