<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">60:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 100</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">100</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 60 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["6"] = {
                name: "Reasoning",
                start: 0,
                end: 23
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="6">Reasoning</option>`;
            sections["18"] = {
                name: "General Awareness",
                start: 24,
                end: 48
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="18">General Awareness</option>`;
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 49,
                end: 73
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["7"] = {
                name: "English",
                start: 74,
                end: 98
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="7">English</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 99,
                end: 99
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "6",
                    question_en: "<p>1. Select the correct mirror image of the given figure when a mirror is placed on the right side of the figure.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744023354107.png\" alt=\"rId4\" width=\"91\" height=\"88\"></p>",
                    question_hi: "<p>1. दी गयी आकृति के सही दर्पण प्रतिबिंब का चयन करें, जब दर्पण को आकृति के दायीं ओर रखा जाता है ।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744023354107.png\" alt=\"rId4\" width=\"91\" height=\"88\"></p>",
                    options_en: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744023354901.png\" alt=\"rId5\" width=\"80\" height=\"77\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744023355099.png\" alt=\"rId6\" width=\"80\" height=\"77\"></p>", 
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744023355696.png\" alt=\"rId7\" width=\"81\" height=\"78\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744023356027.png\" alt=\"rId8\" width=\"80\" height=\"78\"></p>"],
                    options_hi: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744023354901.png\" alt=\"rId5\" width=\"80\" height=\"77\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744023355099.png\" alt=\"rId6\" width=\"80\" height=\"77\"></p>",
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744023355696.png\" alt=\"rId7\" width=\"81\" height=\"78\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744023356027.png\" alt=\"rId8\" width=\"80\" height=\"78\"></p>"],
                    solution_en: "<p>1.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744023356633.png\" alt=\"rId9\" width=\"164\" height=\"115\"></p>",
                    solution_hi: "<p>1.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744023356633.png\" alt=\"rId9\" width=\"164\" height=\"115\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "6",
                    question_en: "<p>2. In a certain language &lsquo;EARTH&rsquo; is coded as &lsquo;8201815&rsquo;. How will &lsquo;ORBIT&rsquo; be coded in that language ?</p>",
                    question_hi: "<p>2. किसी निश्चित कूट भाषा में, EARTH का कूट &lsquo;8201815&rsquo; है। इसी भाषा में ORBIT का कूट क्या होगा ?</p>",
                    options_en: ["<p>18172372</p>", "<p>20921815</p>", 
                                "<p>37681026</p>", "<p>17113912</p>"],
                    options_hi: ["<p>18172372</p>", "<p>20921815</p>",
                                "<p>37681026</p>", "<p>17113912</p>"],
                    solution_en: "<p>2.(b) In the given question, the alphabetical positions of each alphabet are written in reverse order.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744023360687.png\" alt=\"rId10\" width=\"132\" height=\"133\"><br>Similarly, &lsquo;ORBIT&rsquo; will be coded as 20921815.</p>",
                    solution_hi: "<p>2.(b) दिए गए प्रश्न में, प्रत्येक वर्णमाला की वर्णानुक्रमिक स्थिति उल्टे क्रम में लिखी गई है ।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744023360687.png\" alt=\"rId10\" width=\"132\" height=\"133\"><br>इसी तरह, ORBIT\' को 20921815 के रूप में कोडित किया जाएगा।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "6",
                    question_en: "<p>3. Select the option that is related to the third term in the same way as the second term is related to the first term and the sixth term is related to the fifth term. <br>72 : 14 :: 87 : ? :: 96 : 54</p>",
                    question_hi: "<p>3. उस विकल्प का चयन करें जो तीसरी संख्या से ठीक उसी प्रकार संबंधित है जिस प्रकार दूसरी संख्या पहली संख्या से तथा छठी संख्या पाँचवीं संख्या से संबंधित है। <br>72 : 14 :: 87 : ? :: 96 : 54</p>",
                    options_en: ["<p>56</p>", "<p>52</p>", 
                                "<p>29</p>", "<p>15</p>"],
                    options_hi: ["<p>56</p>", "<p>52</p>",
                                "<p>29</p>", "<p>15</p>"],
                    solution_en: "<p>3.(a) The pattern being followed is: Product of the digits of first term = Second term<br>7 &times; 2 = 14, 9 &times; 6 = 54&nbsp;<br>Similarly,<br>8 &times; 7 = 56</p>",
                    solution_hi: "<p>3.(a) अनुसरण किया जा रहा पैटर्न है: पहले पद के अंकों का गुणनफल = दूसरा पद<br>7 &times; 2 = 14, 9 &times; 6 = 54&nbsp;<br>इसी तरह,<br>8 &times; 7 = 56</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "6",
                    question_en: "<p>4. Select the option that depicts how the given transparent sheet of paper would appear if it is folded at the dotted line.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744023361410.png\" alt=\"rId11\" width=\"95\" height=\"86\"></p>",
                    question_hi: "<p>4. उस विकल्प का चयन करें जो दर्शाता है कि दिए गए पारदर्शी कागज़ को यदि बिंदु-रेखा पर से मोड़ा जाता है, तो यह कैसा दिखाई देगा ? <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744023361410.png\" alt=\"rId11\" width=\"95\" height=\"86\"></p>",
                    options_en: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744023362328.png\" alt=\"rId12\" width=\"80\" height=\"73\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744023362681.png\" alt=\"rId13\" width=\"80\" height=\"71\"></p>", 
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744023362863.png\" alt=\"rId14\" width=\"80\" height=\"76\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744023363423.png\" alt=\"rId15\" width=\"80\" height=\"75\"></p>"],
                    options_hi: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744023362328.png\" alt=\"rId12\" width=\"80\" height=\"73\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744023362681.png\" alt=\"rId13\" width=\"80\" height=\"71\"></p>",
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744023362863.png\" alt=\"rId14\" width=\"80\" height=\"76\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744023363423.png\" alt=\"rId15\" width=\"81\" height=\"76\"></p>"],
                    solution_en: "<p>4.(c) The figure given below shows how the transparent paper will appear when folded along the dotted lines.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744023362863.png\" alt=\"rId14\" width=\"82\" height=\"77\"></p>",
                    solution_hi: "<p>4.(c) नीचे दिया गया चित्र दिखाता है कि बिंदीदार रेखाओं के साथ मोड़ने पर पारदर्शी कागज कैसा दिखाई देगा।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744023362863.png\" alt=\"rId14\" width=\"82\" height=\"77\"></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "6",
                    question_en: "<p>5. How many rectangles are there in the given figure ?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744023363675.png\" alt=\"rId16\" width=\"131\" height=\"103\"></p>",
                    question_hi: "<p>5. दी गयी आकृति में कितने आयत हैं ?&nbsp;<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744023363675.png\" alt=\"rId16\" width=\"131\" height=\"103\"></p>",
                    options_en: ["<p>15</p>", "<p>17</p>", 
                                "<p>11</p>", "<p>13</p>"],
                    options_hi: ["<p>15</p>", "<p>17</p>",
                                "<p>11</p>", "<p>13</p>"],
                    solution_en: "<p>5.(b) There are 17 rectangles in the given figure.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744023364224.png\" alt=\"rId17\" width=\"150\" height=\"131\"><br>Rectangles: AIHB, ATCB, AMPB, AJGB, TMPC, MJGP, TJGC, JIHG, MIHP, TIHC, LKFE, LKJM, LKGP, PGFE, MJFE, CPXD, TMXD</p>",
                    solution_hi: "<p>5.(b) दी गई आकृति में 17 आयत हैं।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744023364224.png\" alt=\"rId17\" width=\"150\" height=\"131\"><br>आयत: AIHB, ATCB, AMPB, AJGB, TMPC, MJGP, TJGC, JIHG, MIHP, TIHC, LKFE, LKJM, LKGP, PGFE, MJFE, CPXD,TMXD</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "6",
                    question_en: "<p>6. In the given Venn diagram, the &lsquo;rectangle&rsquo; represents &lsquo;engineers&rsquo;, the &lsquo;circle&rsquo; represents &lsquo;managers&rsquo;, and the &lsquo;triangle&rsquo; represents &lsquo;married&rsquo;. The numbers given the diagram represent the number of persons in that particular category.&nbsp;<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744023364697.png\" alt=\"rId18\" width=\"168\" height=\"135\"> <br>How many people are married but NOT engineers ?</p>",
                    question_hi: "<p>6. दिए गए वेन आरेख में, आयत &lsquo;इंजीनियरों&rsquo; को दर्शाता है, वृत्त &lsquo;प्रबंधकों&rsquo; को दर्शाता है तथा त्रिभुज &lsquo;विवाहित लोगों&rsquo; को दर्शाता है। आरेख में दी गयी संख्याएँ उस विशेष श्रेणी में मौजूद लोगों की संख्या बताती हैं।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744023364697.png\" alt=\"rId18\" width=\"168\" height=\"135\"><br>कितने लोग विवाहित हैं लेकिन इंजीनियर नहीं हैं ?</p>",
                    options_en: ["<p>29</p>", "<p>16</p>", 
                                "<p>70</p>", "<p>13</p>"],
                    options_hi: ["<p>29</p>", "<p>16</p>",
                                "<p>70</p>", "<p>13</p>"],
                    solution_en: "<p>6.(a) The triangle represents married people and the rectangle represents engineers but we want married people and not engineers. Hence,<br>13 + 16 = 29 (Married but not Engineers) is the answer.</p>",
                    solution_hi: "<p>6.(a) त्रिभुज विवाहित लोगों का प्रतिनिधित्व करता है और आयत इंजीनियरों का प्रतिनिधित्व करता है लेकिन हम विवाहित लोगों को चाहते हैं न कि इंजीनियर। इसलिये,<br>13 +16 = 29 (विवाहित लेकिन इंजीनियर नहीं) इसका उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "6",
                    question_en: "<p>7. In a certain code language, &lsquo;JUPITER&rsquo; is written as &lsquo;JVOJSFR&rsquo;. How will &lsquo;NEPTUNE &lsquo; be written as in that language ?</p>",
                    question_hi: "<p>7. किसी निश्चित कूट भाषा में, JUPITER को &lsquo;JVOJSFR&rsquo; लिखा जाता है। इसी भाषा में &lsquo;NEPTUNE&rsquo; को क्या लिखा जाएगा ?</p>",
                    options_en: ["<p>NDPSVME</p>", "<p>NGOUTOE</p>", 
                                "<p>NFOUTOE</p>", "<p>NFOSTOE</p>"],
                    options_hi: ["<p>NDPSVME</p>", "<p>NGOUTOE</p>",
                                "<p>NFOUTOE</p>", "<p>NFOSTOE</p>"],
                    solution_en: "<p>7.(c) The required pattern is:1st &amp; last alphabet <math display=\"inline\"><mo>&#8594;</mo></math>no change, then +1, -1 rule is used.</p>\n<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744023365870.png\" alt=\"rId19\" width=\"191\" height=\"109\"></p>\n<p>Following the same pattern, &lsquo;NEPTUNE&rsquo; will be coded as &lsquo;NFOUTOE&rsquo;</p>",
                    solution_hi: "<p>7.(c) आवश्यक पैटर्न है: पहला और अंतिम अक्षर<math display=\"inline\"><mo>&#8594;</mo></math> कोई परिवर्तन नहीं, फिर +1, -1 नियम का उपयोग किया जाता है।</p>\n<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744023366194.png\" alt=\"rId20\" width=\"185\" height=\"108\"></p>\n<p>उसी पैटर्न का अनुसरण करते हुए, \'NEPTUNE\' को \'NFOUTOE\' के रूप में कोडित किया जाएगा।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "6",
                    question_en: "<p>8. A product was marked for sale with a label price, which is at a 20% discount on the printed price. At the time of sale, the shopkeeper gave an additional 10% discount on the label price. If a customer bought the product at Rs 468 , then what is the printed price of the product ?</p>",
                    question_hi: "<p>8. एक वस्तु को कीमत पर्ची के साथ बिक्री के लिए रखा गया जो मुद्रित मूल्य से 20% की छूट पर है। बिक्री के समय, दुकानदार ने कीमत पर्ची मूल्य पर 10% की अतिरिक्त छूट दी। यदि एक ग्राहक ने इस वस्तु को 468 रुपये में क्रय किया, तो इस वस्तु का मुद्रित मूल्य कितना है ?</p>",
                    options_en: ["<p>Rs 700</p>", "<p>Rs 650</p>", 
                                "<p>Rs 520</p>", "<p>Rs 600</p>"],
                    options_hi: ["<p>Rs 700</p>", "<p>Rs 650</p>",
                                "<p>Rs 520</p>", "<p>Rs 600</p>"],
                    solution_en: "<p>8.(b) Printed Price <br>= (468 &times; 10 &times; 5)/(9 &times; 4) = 650</p>",
                    solution_hi: "<p>8.(b) मुद्रित मूल्य = (468 &times; 10 &times; 5)/(9 &times; 4) = 650</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "6",
                    question_en: "<p>9. A + B means &lsquo;A is the wife of B.<br>A - B means &lsquo;B is the daughter of A.<br>A &times; B means &lsquo;B is the brother of A.<br>A &divide; B means &lsquo;A is the father of B. <br>If, P + R &times; T - Q + S &divide; U, then how is P related to the mother of U ?</p>",
                    question_hi: "<p>9. A + B का अर्थ है \'A, B की पत्नी है<br>A - B का अर्थ है, &lsquo;B, A की बेटी है।<br>A &times; B का अर्थ है, B, A का भाई है।<br>A &divide; B का अर्थ है, &lsquo;A, B का पिता है।<br>यदि P + R &times; T - Q + S &divide; U है, तो P, U की माँ से किस प्रकार संबंधित है ?</p>",
                    options_en: ["<p>Maternal grandmother</p>", "<p>Mother</p>", 
                                "<p>Paternal grandmother</p>", "<p>Aunt</p>"],
                    options_hi: ["<p>नानी</p>", "<p>माँ</p>",
                                "<p>दादी</p>", "<p>चाची</p>"],
                    solution_en: "<p>9.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744023366379.png\" alt=\"rId21\" width=\"195\" height=\"130\"><br>P is the aunt of Q, is mother of U.</p>",
                    solution_hi: "<p>9.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744023366379.png\" alt=\"rId21\" width=\"195\" height=\"130\"><br>P, Q की आंटी है जो U की माता है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "6",
                    question_en: "<p>10. Arrange the following words in a logical and meaningful order (according to population)&nbsp;<br>1. Andhra Pradesh <br>2. Madhya Pradesh<br>3. Arunachal Pradesh <br>4. Uttar Pradesh <br>5. Himachal Pradesh</p>",
                    question_hi: "<p>10. निम्नलिखित शब्दों को (आबादी के अनुसार) एक तर्कपूर्ण तथा अर्थपूर्ण क्रम में व्यवस्थित करें।<br>1. आंध्र प्रदेश <br>2. मध्य प्रदेश <br>3. अरुणाचल प्रदेश <br>4. उत्तर प्रदेश <br>5. हिमाचल प्रदेश</p>",
                    options_en: ["<p>4-1-2-3-5</p>", "<p>4-2-1-5-3</p>", 
                                "<p>4-1-2-5-3</p>", "<p>4-2-1-3-5</p>"],
                    options_hi: ["<p>4-1-2-3-5</p>", "<p>4-2-1-5-3</p>",
                                "<p>4-1-2-5-3</p>", "<p>4-2-1-3-5</p>"],
                    solution_en: "<p>10.(b) The logical and meaningful order according to population is:<br>Uttar Pradesh &gt; Madhya Pradesh &gt; Andhra Pradesh &gt; Himachal Pradesh &gt; Arunachal Pradesh.</p>",
                    solution_hi: "<p>10.(b) जनसंख्या के अनुसार तार्किक और सार्थक क्रम है: उत्तर प्रदेश &gt; मध्य प्रदेश &gt; आंध्र प्रदेश &gt; हिमाचल प्रदेश &gt; अरुणाचल प्रदेश।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "6",
                    question_en: "<p>11. Select the option in which the words share the same relationship as that shared by the given pair of words. <br>Dentist : Doctor</p>",
                    question_hi: "<p>11. उस विकल्प का चयन करें जिसमें शब्दों के बीच ठीक वही संबंध है जो संबंध शब्दों के दिए गए युग्म में है।<br>\'डेंटिस्ट\' : \'डॉक्टर\'</p>",
                    options_en: ["<p>Line : Circle</p>", "<p>Algebra : Geometry</p>", 
                                "<p>Chemistry : Science</p>", "<p>Biology : Astrology</p>"],
                    options_hi: ["<p>रेखा : वृत्त</p>", "<p>बीजगणित : ज्यामिति</p>",
                                "<p>रसायन शास्त्र : विज्ञान</p>", "<p>जीव विज्ञान : ज्योतिष शास्त्र</p>"],
                    solution_en: "<p>11.(c) &lsquo;Dentist&rsquo; comes under the category of &lsquo;Doctors&rsquo; in the same way &lsquo;Chemistry&rsquo; comes under the category of &lsquo; Science&rsquo;.</p>",
                    solution_hi: "<p>11.(c) \'\'डेंटिस्ट\' \'डॉक्टर\' की श्रेणी में आता है उसी तरह रसायन शास्त्र \'विज्ञान\' की श्रेणी में आता है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "6",
                    question_en: "<p>12. Which two signs should be interchanged to make the given equation correct ?<br>121 + 11 - 42 &times;&nbsp;6 &divide; 7 = 83</p>",
                    question_hi: "<p>12. दिए गए समीकरण को सही बनाने के लिए किन दो शब्दों को आपस में बदलने की आवश्यकता है ? <br>121 + 11 - 42 &times; 6 &divide; 7 = 83</p>",
                    options_en: ["<p>- and &divide;</p>", "<p>+ and &divide;</p>", 
                                "<p>&divide; and &times;</p>", "<p>&times; and -</p>"],
                    options_hi: ["<p>- और &divide;</p>", "<p>+ और &divide;</p>",
                                "<p>&divide; और &times;</p>", "<p>&times; और -</p>"],
                    solution_en: "<p>12.(c) 121 + 11 - 42 &times; 6 &divide; 7 = 83<br>By observing all the options, we find that the option(c) gives the desired results.<br>Now interchanging &lsquo;&divide; and &times;&rsquo;,<br>LHS<br>121 + 11 - 42 &divide; 6 &times; 7<br>= 121 + 11- 7 &times; 7<br>= 121 + 11 - 49<br>&rArr; 132 - 49<br>&rArr; 83 = RHS</p>",
                    solution_hi: "<p>12.(c) 121 + 11 - 42 &times; 6 &divide; 7 = 83<br>सभी विकल्पों को देखने पर, हम पाते हैं कि विकल्प (c) वांछित परिणाम देता है।<br>अब &divide; और &times; को आपस में बदलने पर ,<br>LHS<br>121 + 11 - 42 &divide; 6 &times; 7<br>= 121 + 11- 7 &times; 7<br>= 121 + 11 - 49<br>&rArr; 132 - 49<br>&rArr; 83 = RHS</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "6",
                    question_en: "<p>13. Select the option in which the given figure is embedded. (rotation is NOT allowed)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744023366668.png\" alt=\"rId22\" width=\"96\" height=\"87\"></p>",
                    question_hi: "<p>13. उस विकल्प का चयन करें जिसमें दी गयी आकृति अंतर्निहित है।(घुमाने की अनुमति नहीं है)&nbsp;<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744023366668.png\" alt=\"rId22\" width=\"96\" height=\"87\"></p>",
                    options_en: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744023366797.png\" alt=\"rId23\" width=\"81\" height=\"72\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744023366990.png\" alt=\"rId24\" width=\"80\" height=\"72\"></p>", 
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744023367390.png\" alt=\"rId25\" width=\"80\" height=\"72\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744023367546.png\" alt=\"rId26\" width=\"80\" height=\"74\"></p>"],
                    options_hi: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744023366797.png\" alt=\"rId23\" width=\"81\" height=\"72\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744023366990.png\" alt=\"rId24\" width=\"80\" height=\"72\"></p>",
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744023367390.png\" alt=\"rId25\" width=\"81\" height=\"73\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744023367546.png\" alt=\"rId26\" width=\"81\" height=\"75\"></p>"],
                    solution_en: "<p>13.(a) The figure given in the question is embedded in the image given below.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744023367775.png\" alt=\"rId28\" width=\"98\" height=\"90\"></p>",
                    solution_hi: "<p>13.(a) प्रश्न में दी गई आकृति नीचे दी गई छवि में सन्निहित है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744023367775.png\" alt=\"rId28\" width=\"98\" height=\"90\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "6",
                    question_en: "<p>14. Select the figure which can come in place of question mark (?) in the following series ?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744023368074.png\" alt=\"rId29\" width=\"326\" height=\"67\"></p>",
                    question_hi: "<p>14. उस आकृति का चयन करें जो नीचे दी गयी श्रृंखला में प्रश्नचिन्ह (?) के स्थान पर आ सकती है ?&nbsp;<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744023368074.png\" alt=\"rId29\" width=\"326\" height=\"67\"></p>",
                    options_en: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744023368256.png\" alt=\"rId30\" width=\"80\" height=\"80\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744023368637.png\" alt=\"rId31\" width=\"80\" height=\"80\"></p>", 
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744023368904.png\" alt=\"rId32\" width=\"80\" height=\"80\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744023369008.png\" alt=\"rId33\" width=\"81\" height=\"81\"></p>"],
                    options_hi: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744023368256.png\" alt=\"rId30\" width=\"80\" height=\"80\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744023368637.png\" alt=\"rId31\" width=\"80\" height=\"80\"></p>",
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744023368904.png\" alt=\"rId32\" width=\"80\" height=\"80\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744023369008.png\" alt=\"rId33\" width=\"80\" height=\"80\"></p>"],
                    solution_en: "<p>14.(c) If the position of <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744023369120.png\" alt=\"rId34\" width=\"23\" height=\"43\"> is only focused then it is clear that in the successive figures it takes a clockwise direction position along with getting inverted&nbsp;&nbsp;<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744023369828.png\" alt=\"rId35\" width=\"22\" height=\"39\"> alternately and also the pentagon symbol follows the same.</p>",
                    solution_hi: "<p>14.(c) यदि <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744023369120.png\" alt=\"rId34\" width=\"23\" height=\"43\"> की स्थिति केवल केंद्रित है तो यह स्पष्ट है कि क्रमिक आंकड़ों में यह बारी-बारी से उलटे होने के साथ-साथ दक्षिणावर्त दिशा <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744023369828.png\" alt=\"rId35\" width=\"22\" height=\"39\"> की स्थिति लेता है और पंचकोण का प्रतीक भी उसी का अनुसरण करता है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "6",
                    question_en: "<p>15. Select the number-pair that is different</p>",
                    question_hi: "<p>15. अलग संख्या-युग्म का चयन करें।</p>",
                    options_en: ["<p>10 : 123</p>", "<p>17 : 290</p>", 
                                "<p>14 : 227</p>", "<p>11 : 146</p>"],
                    options_hi: ["<p>10 : 123</p>", "<p>17 : 290</p>",
                                "<p>14 : 227</p>", "<p>11 : 146</p>"],
                    solution_en: "<p>15.(b) The pattern followed here is:<br>(1st number + 1)<sup>2</sup> +2<br>10 + 1 = 11, 11<sup>2 </sup>+ 2 = 121 + 2 = 123<br>14 + 1 = 15, 15<sup>2</sup> + 2 = 225 + 2 = 227<br>11 + 1 = 12, 12<sup>2</sup> + 2 = 144 + 2 = 146<br>But, this pattern is not followed in option (b)<br>i.e, 17 + 1 = 18, 17<sup>2</sup> = 28 9 + 1 = 290.</p>",
                    solution_hi: "<p>15.(b) यहां अनुसरण किया गया पैटर्न है: (पहली संख्या + 1)<sup>2</sup> + 2<br>10 + 1 = 11, 11<sup>2 </sup>+ 2 = 121 + 2 = 123<br>14 + 1 = 15, 15<sup>2</sup> + 2 = 225 + 2 = 227<br>11 + 1 = 12, 12<sup>2</sup> + 2 = 144 + 2 = 146<br>लेकिन, विकल्प (b) में इस पैटर्न का पालन नहीं किया जाता है, <br>अर्थात, 17 + 1 = 18, 17<sup>2</sup> = 289 + 1 = 290.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "6",
                    question_en: "<p>16. Select the option in which the number set shares the same relationship as that shared by the given number set. <br>(17, 24, 45)</p>",
                    question_hi: "<p>16. उस विकल्प का चयन करें जिसमें संख्याओं के समूह में ठीक वही संबंध है जो संबंध संख्याओं के दिए गए समूह में है। <br>(17, 24, 45)</p>",
                    options_en: ["<p>(18, 23, 46)</p>", "<p>(19, 26, 34)</p>", 
                                "<p>(15, 20, 43)</p>", "<p>(12, 19, 40)</p>"],
                    options_hi: ["<p>(18, 23, 46)</p>", "<p>(19, 26, 34)</p>",
                                "<p>(15, 20, 43)</p>", "<p>(12, 19, 40)</p>"],
                    solution_en: "<p>16.(d) The pattern:<br>17 + &lsquo;7&rsquo; = 24<br>24 + &lsquo;21&rsquo; = 45<br>By observing, we find that only (12, 19, 40) satisfies the above pattern.<br>12 + &lsquo;7&rsquo; = 19<br>19 + &lsquo;21&rsquo; = 40</p>",
                    solution_hi: "<p>16.(d) पैटर्न है:<br>17 + &lsquo;7&rsquo; = 24<br>24 + &lsquo;21&rsquo; = 45<br>अवलोकन करने पर, हम पाते हैं कि केवल (12, 19, 40) उपरोक्त पैटर्न को संतुष्ट करता है,<br>12 + &lsquo;7&rsquo; = 19<br>19 + &lsquo;21&rsquo; = 40</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "6",
                    question_en: "<p>17. Read the given statement(s) and conclusions carefully. Assuming that the information given in the statements is true, even if it appears to be at variance with commonly known facts, decide which of the given conclusions logically follow(s) from the statement(s).<br><strong>Statement:</strong><br>All Singers are dancers <br>No dancer is a plumber <br><strong>Conclusions:</strong><br>I. No plumber is a singer <br>II. Some singers are plumbers.<br>III. Some dancers are singers .</p>",
                    question_hi: "<p>17. दिए गए कथनों और निष्कर्षों का ध्यानपूर्वक अध्ययन करें । यह मानते हुए कि दिए गए कथनों में दी गयी जानकारी सही है, भले ही यह सामान्य रूप से स्थापित तथ्यों से भिन्न प्रतीत होती है, यह तय करें कि इन कथनों से तर्कपूर्ण धन से निम्न में से कौन सा निष्कर्ष निकाला जा सकता है।<br><strong>कथन :</strong><br>सभी गायक नर्तक हैं |<br>कोई भी नर्तक प्लंबर नहीं है |<br><strong>निष्कर्ष :</strong><br>I.&nbsp;कोई भी प्लंबर गायक नहीं है |<br>II.&nbsp;कुछ गायक प्लंबर हैं |<br>III.&nbsp;कुछ नर्तक गायक हैं |</p>",
                    options_en: ["<p>Only conclusion I follow.</p>", "<p>Only conclusions II and III follow .</p>", 
                                "<p>only conclusions I and III follow .</p>", "<p>Only Conclusion II follows.</p>"],
                    options_hi: ["<p>केवल निष्कर्ष I सही है।</p>", "<p>केवल निष्कर्ष II और III सही हैं।</p>",
                                "<p>केवल निष्कर्ष I और III सही है।</p>", "<p>केवल निष्कर्ष II सही है।</p>"],
                    solution_en: "<p>17.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744023370101.png\" alt=\"rId36\" width=\"210\" height=\"83\"><br>Clearly, only I and III follow.</p>",
                    solution_hi: "<p>17.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744023370390.png\" alt=\"rId37\" width=\"218\" height=\"86\"><br>स्पष्ट रूप से, केवल I और III अनुसरण करते हैं।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "6",
                    question_en: "<p>18. Select the number that can replace the question mark (?) in the following series.&nbsp;<br>1537, 1539 , 1543 , ? , 1557 , 1567</p>",
                    question_hi: "<p>18. उस संख्या का चयन करें जो नीचे दी गयी श्रृंखला में प्रश्नचिन्ह (?) के स्थान पर आ सकती है।<br>1537, 1539 , 1543 , ? , 1557 , 1567</p>",
                    options_en: ["<p>1553</p>", "<p>1550</p>", 
                                "<p>1546</p>", "<p>1549</p>"],
                    options_hi: ["<p>1553</p>", "<p>1550</p>",
                                "<p>1546</p>", "<p>1549</p>"],
                    solution_en: "<p>18.(d) The required pattern here is: +2, +4, +6, +8, +10<br>1537 + 2 = 1539, 1539 + 4 = 1543<br>1543 + 6 = <span style=\"text-decoration: underline;\">1549</span>, 1549 + 8 = 1557,&nbsp;<br>1557 + 10 = 1567</p>",
                    solution_hi: "<p>18.(d) यहां आवश्यक पैटर्न है : +2, +4 ,+6 ,+8, +10<br>1537 + 2 = 1539, 1539 + 4 = 1543<br>1543 + 6 = <span style=\"text-decoration: underline;\">1549</span>, 1549 + 8 = 1557,&nbsp;<br>1557 + 10 = 1567</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "6",
                    question_en: "<p>19. Select the odd word.</p>",
                    question_hi: "<p>19. अलग शब्द का चयन करें।</p>",
                    options_en: ["<p>Hyderabad</p>", "<p>Aurangabad</p>", 
                                "<p>Chennai</p>", "<p>Gangtok</p>"],
                    options_hi: ["<p>हैदराबाद</p>", "<p>औरंगाबाद</p>",
                                "<p>चेन्नई</p>", "<p>गंगटोक</p>"],
                    solution_en: "<p>19.(b) Except &lsquo;Aurangabad&rsquo; all the rest are capital cities. Hyderabad is the capital of Telangana, Chennai is the capital of Tamil Nadu &amp; Gangtok is the capital of Sikkim.</p>",
                    solution_hi: "<p>19.(b) \'औरंगाबाद\' को छोड़कर बाकी सभी राजधानी शहर हैं। हैदराबाद तेलंगाना की राजधानी है, चेन्नई तमिलनाडु की राजधानी है। गंगटोक सिक्किम की राजधानी है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "6",
                    question_en: "<p>20. Select the letter-cluster that can replace the question mark (?) in the following series.&nbsp;<br>DAG, FCI, ? , JGM , LIO</p>",
                    question_hi: "<p>20. उस अक्षर समूह का चयन करें जो नीचे दी गयी श्रृंखला में प्रश्न चिन्ह (?) के स्थान पर आ सकता है ।&nbsp;<br>DAG , FCI , ? , JGM , LIO</p>",
                    options_en: ["<p>GDJ</p>", "<p>IFL</p>", 
                                "<p>HKE</p>", "<p>HEK</p>"],
                    options_hi: ["<p>GDJ</p>", "<p>IFL</p>",
                                "<p>HKE</p>", "<p>HEK</p>"],
                    solution_en: "<p>20.(d) The required pattern is:+2 rule is used.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744023370557.png\" alt=\"rId38\" width=\"245\" height=\"80\"></p>",
                    solution_hi: "<p>20.(d) आवश्यक पैटर्न है: +2 नियम का उपयोग किया जाता है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744023370557.png\" alt=\"rId38\" width=\"245\" height=\"80\"></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "6",
                    question_en: "<p>21. Wheat&rsquo; is related to &lsquo;Bread&rsquo; in the same way as &lsquo;Sugarcane&rsquo; is related to &lsquo;_____&rsquo;</p>",
                    question_hi: "<p>21. &lsquo;गेहूँ&rsquo; का ब्रेड से ठीक वही संबंध है जो संबंध &lsquo;गन्ना&rsquo; का ______से है।</p>",
                    options_en: ["<p>Jaggery</p>", "<p>Mayonnaise</p>", 
                                "<p>Grass</p>", "<p>Ketchup</p>"],
                    options_hi: ["<p>गुड़</p>", "<p>मेयोनीज</p>",
                                "<p>घास</p>", "<p>केचअप</p>"],
                    solution_en: "<p>21.(a) &lsquo;Wheat&rsquo; is used to prepare &lsquo;Bread&rsquo; similarly &lsquo;Sugarcane&rsquo; is used to make &lsquo;Jaggery&rsquo;</p>",
                    solution_hi: "<p>21.(a) \'रोटी\' बनाने के लिए \'गेहूं\' का उपयोग किया जाता है उसी तरह \'गन्ना\' का उपयोग \'गुड़\' बनाने के लिए किया जाता है ।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "6",
                    question_en: "<p>22. Select the odd letter-Cluster.</p>",
                    question_hi: "<p>22. अलग अक्षर-समूह का चयन करें।</p>",
                    options_en: ["<p>FGL</p>", "<p>ABF</p>", 
                                "<p>IJN</p>", "<p>MNR</p>"],
                    options_hi: ["<p>FGL</p>", "<p>ABF</p>",
                                "<p>IJN</p>", "<p>MNR</p>"],
                    solution_en: "<p>22.(a) <strong>The pattern:</strong> +1, +4 rule is used.</p>\n<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744023370685.png\" alt=\"rId39\" width=\"106\" height=\"53\">&nbsp; ,&nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744023370803.png\" alt=\"rId40\" width=\"99\" height=\"52\">&nbsp; , <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744023370933.png\" alt=\"rId41\" width=\"111\" height=\"54\"></p>\n<p>The above-fixed pattern is not being followed by &lsquo;FGL&rsquo;</p>",
                    solution_hi: "<p>22.(a)<strong> पैटर्न:</strong> +1, +4 नियम का उपयोग किया जाता है।</p>\n<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744023370685.png\" alt=\"rId39\" width=\"106\" height=\"53\">&nbsp; ,&nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744023370803.png\" alt=\"rId40\" width=\"99\" height=\"52\">&nbsp; , <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744023370933.png\" alt=\"rId41\" width=\"111\" height=\"54\"></p>\n<p>उपरोक्त निश्चित पैटर्न का \'FGL\' द्वारा पालन नहीं किया जा रहा है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "23",
                    section: "6",
                    question_en: "<p>23. Select the letter that can replace the question mark (?) in the following series<br>A , E , J , ? , W</p>",
                    question_hi: "<p>23. उस अक्षर का चयन करें जो नीचे दी गयी श्रृंखला में प्रश्न चिन्ह (?) के स्थान पर आ सकता है।<br>A , E , J , ? , W</p>",
                    options_en: ["<p>R</p>", "<p>P</p>", 
                                "<p>S</p>", "<p>Q</p>"],
                    options_hi: ["<p>R</p>", "<p>P</p>",
                                "<p>S</p>", "<p>Q</p>"],
                    solution_en: "<p>23.(b) The required pattern here is ; +4, +5, +6, +7<br>A + 4 = E<br>E + 5 = J<br>J + 6 = <strong>P</strong><br>P + 7 = W</p>",
                    solution_hi: "<p>23.(b) यहां आवश्यक पैटर्न है:+4, +5, +6, +7<br>A + 4 = E<br>E + 5 = J<br>J + 6 = <strong>P</strong><br>P + 7 = W</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "24",
                    section: "6",
                    question_en: "<p>24. Three different positions of the same dice are shown. Select the symbol that will be on the face opposite to the one showing &lsquo;=&rsquo;. <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744023371201.png\" alt=\"rId42\" width=\"239\" height=\"65\"></p>",
                    question_hi: "<p>24. एक ही पासे की तीन अलग-अलग अवस्थाओं को दिखाया गया है। उस प्रतीक का चयन करें &lsquo;=&rsquo; को दर्शाने वाली फलक के पीछे है। <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744023371201.png\" alt=\"rId42\" width=\"239\" height=\"65\"></p>",
                    options_en: ["<p>$</p>", "<p>#</p>", 
                                "<p>%</p>", "<p>*</p>"],
                    options_hi: ["<p>$</p>", "<p>#</p>",
                                "<p>%</p>", "<p>*</p>"],
                    solution_en: "<p>24.(a) By observing the three positions of the dices, we find that<br>&lsquo;#&rsquo; <math display=\"inline\"><mo>&#8658;</mo></math> %<br><strong>&lsquo;=&rsquo; <math display=\"inline\"><mo>&#8658;</mo></math> $</strong><br><math display=\"inline\"><mi>\'</mi><mo>&#8902;</mo><mi>\'</mi><mo>&#8658;</mo></math> +&nbsp;<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744023371312.png\" alt=\"rId43\" width=\"215\" height=\"84\"></p>",
                    solution_hi: "<p>24.(a) पासों की तीन स्थितियों को देखने पर हम पाते हैं कि<br>&lsquo;#&rsquo; <math display=\"inline\"><mo>&#8658;</mo></math> %<br><strong>&lsquo;=&rsquo; <math display=\"inline\"><mo>&#8658;</mo></math> $</strong><br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>\'</mi><mo>&#8902;</mo><mi>\'</mi><mo>&#8658;</mo></math> +<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744023371312.png\" alt=\"rId43\" width=\"215\" height=\"84\"></p>",
                    correct: " a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "25",
                    section: "18",
                    question_en: "<p>25. Study the given pattern carefully and select the number that can replace the question mark (?) in it.&nbsp;<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744023371513.png\" alt=\"rId44\" width=\"134\" height=\"126\"></p>",
                    question_hi: "<p>25. दिए गए प्रारूप का ध्यानपूर्वक अध्ययन करें तथा उस संख्या का चयन करें जो इस में प्रश्न चिन्ह (?) के स्थान पर आ सकती है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744023371513.png\" alt=\"rId44\" width=\"134\" height=\"126\"></p>",
                    options_en: ["<p>64</p>", "<p>96</p>", 
                                "<p>82</p>", "<p>74</p>"],
                    options_hi: ["<p>64</p>", "<p>96</p>",
                                "<p>82</p>", "<p>74</p>"],
                    solution_en: "<p>25.(d) The pattern: +6, +10, 14, +18&hellip;&amp; so on starting from 4.<br>Thus, the gap between them is 4. For eg.4 = (10 - 6) &amp; next (14 - 10)..so on<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744023371934.png\" alt=\"rId45\" width=\"138\" height=\"294\"></p>",
                    solution_hi: "<p>25.(d) पैटर्न: +6, +10, 14, +18&hellip;&amp; इसी तरह 4 से शुरू होता है। इस प्रकार, उनके बीच का अंतर 4 है। उदाहरण के लिए 4 = (10 - 6) और अगला (14 - 10)..इसी तरह<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744023371934.png\" alt=\"rId45\" width=\"138\" height=\"294\"></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "26",
                    section: "18",
                    question_en: "<p>26. When does the entire earth experience equal days and nights ?</p>",
                    question_hi: "<p>26. संपूर्ण पृथ्वी पर कब दिन और रात बराबर होते है ?</p>",
                    options_en: ["<p>Day of winter solstice</p>", "<p>Day of summer solstice</p>", 
                                "<p>At orbital plane</p>", "<p>Day of equinox</p>"],
                    options_hi: ["<p>शीतकालीन संक्रांति का दिन</p>", "<p>ग्रीष्म संक्रांति का दिन</p>",
                                "<p>कक्षीय समतल पर</p>", "<p>विषुव का दिन</p>"],
                    solution_en: "<p>26.(d) Equinox is the day when day and night are equal. This occurs two times every year on 20 march and 23 September. <br>Summer Solstice is on 21 June. <br>Winter Solstice is on 22 December.</p>",
                    solution_hi: "<p>26.(d) विषुव वह दिन है जब दिन और रात बराबर होते हैं। यह हर साल दो बार 20 मार्च और 23 सितंबर को होता है।<br>ग्रीष्म संक्रांति 21 जून को है।<br>22 दिसंबर को शीतकालीन संक्रांति है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "27",
                    section: "18",
                    question_en: "<p>27. Which of these is the energy conversion that happens in the process called photosynthesis ?</p>",
                    question_hi: "<p>27. इनमें से कौन सा ऊर्जा रुपांतरण है जो प्रकाश संश्लेषण नामक प्रक्रिया में होता है ?</p>",
                    options_en: ["<p>Potential Energy to Chemical Energy</p>", "<p>Light Energy to Chemical Energy</p>", 
                                "<p>Heat Energy to Chemical Energy</p>", "<p>Heat Energy to Light Energy</p>"],
                    options_hi: ["<p>पोटेंशियल एनर्जी टू केमिकल एनर्जी</p>", "<p>लाइट एनर्जी टू केमिकल एनर्जी</p>",
                                "<p>हीट एनर्जी टू केमिकल एनर्जी</p>", "<p>हीट एनर्जी टू लाइट एनर्जी</p>"],
                    solution_en: "<p>27.(b) Photosynthesis is a process by which green plants and certain other organisms make their own food by transforming the light energy into chemical energy</p>",
                    solution_hi: "<p>27.(b) प्रकाश ऊर्जा से रासायनिक ऊर्जा&nbsp;प्रकाश संश्लेषण एक ऐसी प्रक्रिया है जिसके द्वारा हरे पौधे और कुछ अन्य जीव प्रकाश ऊर्जा को रासायनिक ऊर्जा में परिवर्तित करके अपना भोजन स्वयं बनाते हैं।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "28",
                    section: "18",
                    question_en: "<p>28. Which of the following is an INCORRECT sequence of Mughal-rulers ?</p>",
                    question_hi: "<p>28. मुग़ल शासकों का निम्न में से कौन सा क्रम गलत है ?</p>",
                    options_en: ["<p>Jahangir, Shah Jahan, Aurangzeb</p>", "<p>Akbar, Shah Jahan, Jahangir</p>", 
                                "<p>Babur, Humayun, Akbar</p>", "<p>Akbar, Jahangir, Shah Jahan</p>"],
                    options_hi: ["<p>जहाँगीर, शाहजहाँ, औरंगजेब</p>", "<p>अकबर, शाहजहाँ, जहाँगीर</p>",
                                "<p>बाबर, हुमायूँ, अकबर</p>", "<p>अकबर, जहाँगीर, शाहजहाँ</p>"],
                    solution_en: "<p>28.(b) Correct order of Mughals Rulers are as follows -&nbsp;Babar, Humayun, Akbar, Jahangir, Shah Jahan, Aurangeb<br>So option B is wrong</p>",
                    solution_hi: "<p>28.(b) मुगल शासकों का सही क्रम इस प्रकार है- बाबर, हुमायूँ, अकबर, जहाँगीर, शाहजहाँ, औरंगजेब <br>तो विकल्प बी गलत है । </p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "29",
                    section: "18",
                    question_en: "<p>29. Who was the then Governor-General of British India when \'Sati Pratha\' became illegal and punishable ?</p>",
                    question_hi: "<p>29. ब्रिटिश भारत के तत्कालीन गवर्नर-जनरल कौन थे जब \'सती प्रथा\' अवैध और दंडनीय बन गई थी ?</p>",
                    options_en: ["<p>Lord Cornwallis</p>", "<p>Lord Wellesley</p>", 
                                "<p>Lord William Bentick</p>", "<p>Warren Hastings</p>"],
                    options_hi: ["<p>लॉर्ड कॉर्नवालिस</p>", "<p>लॉर्ड वेलेजली</p>",
                                "<p>लॉर्ड विलियम बेंटिक</p>", "<p>वारेन हेस्टिंग्स</p>"],
                    solution_en: "<p>29.(c) Lord William Bentick was the Governor General of British India When sati pratha became Illegal and punishable. William Bentick alongwith Raja Ram Mohan Roy is given the credit for abolishment of Sati pratha and declaring it&rsquo;s as a crime. It was abolished in 1829.</p>",
                    solution_hi: "<p>29.(c) लॉर्ड विलियम बेंटिक ब्रिटिश भारत के गवर्नर-जनरल थे जब सती प्रथा अवैध और दंडनीय हो गई थी। राजा राम मोहन राय के साथ विलियम बेंटिक को सती प्रथा को समाप्त करने और इसे अपराध घोषित करने का श्रेय दिया जाता है। इसे 1829 ई. में समाप्त कर दिया गया था।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "30",
                    section: "18",
                    question_en: "<p>30. Who among the following is known as the \'father of Muslim renaissance\' in Bengal ?</p>",
                    question_hi: "<p>30. निम्नलिखित में से किसे बंगाल में \'मुस्लिम पुनर्जागरण के पिता\' के रूप में जाना जाता है ?</p>",
                    options_en: ["<p>Sir Syed Ahmed Khan</p>", "<p>Ameer Ali</p>", 
                                "<p>Nawab Abdul Latif Khan</p>", "<p>Nawab Salimullah Khan</p>"],
                    options_hi: ["<p>सर सय्यद अहमद खान</p>", "<p>अमीर अली</p>",
                                "<p>नवाब अब्दुल लतीफ खान</p>", "<p>नवाब सलीमुल्लाह खान</p>"],
                    solution_en: "<p>30.(c) Father of Muslin Renaissance in Bengal is Nawab Abdul Latif Khan. His title, Nawab was awarded by the British in 1880. He was one of the first Muslim in nineteenth centuary India to embrace the idea of modernization.</p>",
                    solution_hi: "<p>30.(c) बंगाल में मुस्लिम पुनर्जागरण के जनक नवाब अब्दुल लतीफ खान हैं। उनकी उपाधि, नवाब को 1880 में अंग्रेजों ने प्रदान किया था। वह उन्नीसवीं सदी के भारत में आधुनिकीकरण के विचार को अपनाने वाले पहले मुसलमानों में से एक थे।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "31",
                    section: "18",
                    question_en: "<p>31. What is the name of the phenomena (derived by scattering of the light) in which mountain tops acquire a rosy or orange hue around sunrise and sunset ?</p>",
                    question_hi: "<p>31. उस घटना का क्या नाम है (प्रकाश के प्रकीर्णन से) जिसमे किसी पर्वत के शीर्ष पर सूर्योदय और सूर्यास्त के दौरान एक हल्का लाल या नारंगी रंग होता है ?</p>",
                    options_en: ["<p>Brillouin scattering</p>", "<p>Circle of confusion</p>", 
                                "<p>Alpenglow</p>", "<p>Barrel distortion</p>"],
                    options_hi: ["<p>ब्रिलोइन स्कैटरिंग</p>", "<p>सर्किल ऑफ़ कन्फूज़न</p>",
                                "<p>अल्पेन्ग्लो</p>", "<p>बैरल डिस्टॉर्शन</p>"],
                    solution_en: "<p>31.(c) Alpenglow is the phenomenon by which mountain tops acquire a rosy or orange hue around sunrise and sunset. It is either the indirect sunlight reflection off of clouds after sunset or before sunrise, or to direct sunlight that occurs near sunset or sunrise.</p>",
                    solution_hi: "<p>31.(c) एल्पेंग्लो वह घटना है जिसके द्वारा पर्वत शिखर सूर्योदय और सूर्यास्त के आसपास गुलाबी या नारंगी रंग का हो जाता है। यह या तो सूर्यास्त के बाद या सूर्योदय से पहले बादलों से परोक्ष सूर्य के प्रकाश का परावर्तन है, या सूर्यास्त या सूर्योदय के निकट होने वाली सीधी धूप है। प्रकाश का प्रकीर्णन वह परिघटना है जिसमें प्रकाश किरणें धूल या गैस के अणु, जलवाष्प आदि किसी बाधा से टकराने पर अपने सीधे पथ से विचलित हो जाती हैं। प्रकाश का प्रकीर्णन टिंडल प्रभाव और \"सूर्योदय और सूर्यास्त के लाल रंग\" जैसी कई शानदार घटनाओं को जन्म देता है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "32",
                    section: "18",
                    question_en: "<p>32. The American scientist Edwin Hubble\'s name is associated with which of these theories ?</p>",
                    question_hi: "<p>32. अमेरिकी वैज्ञानिक एडविन हबल का नाम निम्न में से किस सिद्धांत से जुड़ा हुआ है ?</p>",
                    options_en: ["<p>Lattice Gauge Theory</p>", "<p>Quantum Chromodynamics Theory</p>", 
                                "<p>Partial Coherence of Light Theory</p>", "<p>The Big Bang Theory</p>"],
                    options_hi: ["<p>लैटिस गेज सिद्धांत</p>", "<p>क्वांटम क्रोमोडायनामिक्स सिद्धांत</p>",
                                "<p>अंशतः सम्बद्ध प्रकाश का सिद्धांत</p>", "<p>बिग बैंग सिद्धांत</p>"],
                    solution_en: "<p>32.(d) Edwin Hubble is associated with the Big Bang theory. He is an American Astronomer and played a crucial role in establishing the field of extragalactic astronomy and observational cosmology.</p>",
                    solution_hi: "<p>32.(d) एडविन हबल बिग बैंग सिद्धांत से जुड़े हैं। वह एक अमेरिकी खगोलविद हैं और उन्होंने एक्सट्रैगैलेक्टिक खगोल विज्ञान और अवलोकन संबंधी ब्रह्मांड विज्ञान के क्षेत्र की स्थापना में महत्वपूर्ण भूमिका निभाई है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "33",
                    section: "18",
                    question_en: "<p>33. What is the name of the phenomena in physics and astronomy which involves the splitting of a spectral line into two or more components of slightly different frequency when the light source is placed in a magnetic field ?</p>",
                    question_hi: "<p>33. भौतिकी और खगोल विज्ञान में घटना का नाम क्या है जिसमें एक चुंबकीय क्षेत्र में प्रकाश स्रोत को रखा जाने पर वर्णक्रमीय रेखा के दो या दो से अधिक घटकों को थोड़ा अलग आवृत्ति में विभाजित करना शामिल है ?</p>",
                    options_en: ["<p>Lumen Effect</p>", "<p>Alpenglow Effect</p>", 
                                "<p>Zeeman Effect</p>", "<p>Raman Effect</p>"],
                    options_hi: ["<p>लुमेन प्रभाव</p>", "<p>अल्पेन्ग्लो प्रभाव</p>",
                                "<p>ज़िम्मन प्रभाव</p>", "<p>रमन प्रभाव</p>"],
                    solution_en: "<p>33.(c) Zeeman Effect is a phenomena in physics and astronomy which involves the splitting of a spectral line into two or more components of slightly different frequency when the light source is placed in a magnetic field.</p>",
                    solution_hi: "<p>33.(c) ज़िम्मन प्रभाव भौतिकी और खगोल विज्ञान में एक घटना है जिसमें प्रकाश स्रोत को चुंबकीय क्षेत्र में रखने पर वर्णक्रमीय रेखा को दो या दो से अधिक अलग-अलग आवृत्तियों के घटकों में विभाजित करना शामिल है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "34",
                    section: "18",
                    question_en: "<p>34. Who is the only scientist in the world to have won the Nobel Prize in Chemistry twice ?</p>",
                    question_hi: "<p>34. रसायन शास्त्र में दो बार नोबेल पुरस्कार जीतने वाला विश्व का एकमात्र वैज्ञानिक कौन है ?</p>",
                    options_en: ["<p>Linus Carl Pauling</p>", "<p>Roger D.Kornberg</p>", 
                                "<p>Madame Curie</p>", "<p>Fraderick Sanger</p>"],
                    options_hi: ["<p>लिनस कार्ल पॉलिंग</p>", "<p>रोजर डी कोर्नबर्ग</p>",
                                "<p>मैडम क्यूरी</p>", "<p>फ्रेडरिक सैंगर</p>"],
                    solution_en: "<p>34.(d) Fredrick Sanger is the Scientist to win two Nobel prizes in Chemistry. He won his first Nobel prize in1963 and second in 1980.</p>",
                    solution_hi: "<p>34.(d) फ्रेडरिक सेंगर रसायन विज्ञान में दो नोबेल पुरस्कार जीतने वाले वैज्ञानिक हैं। उन्होंने अपना पहला नोबेल पुरस्कार 1963 में और दूसरा 1980 में जीता।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "35",
                    section: "18",
                    question_en: "<p>35. Bhavai and Kalbelia as traditional dance forms , owe their genesis to which Indian state ?</p>",
                    question_hi: "<p>35. भवाई तथा कलबेलिया की पारंपरिक नृत्य शैलियों के रूप में किस भारतीय राज्य में उत्पत्ति हुई थी ?</p>",
                    options_en: ["<p>Punjab</p>", "<p>Rajasthan</p>", 
                                "<p>Assam</p>", "<p>Odisha</p>"],
                    options_hi: ["<p>पंजाब</p>", "<p>राजस्थान</p>",
                                "<p>असम</p>", "<p>ओडिशा</p>"],
                    solution_en: "<p>35.(b) Bhavai and Kalbelia are traditional dance forms of Rajasthan. In Bhavai Women wear colourful dresses such as Ghaghra cholis and dupattas and they balance a number of earthen pots on their head and dance. Kalbelia dance is performed by a tribe of people known by the same name, Kalbelia.</p>",
                    solution_hi: "<p>35.(b) भवई और कालबेलिया राजस्थान के पारंपरिक नृत्य रूप हैं. भवई में महिलाएं घाघरा चोली और दुपट्टे जैसे रंगीन कपड़े पहनती हैं और वे अपने सिर पर कई मिट्टी के बर्तनों को संतुलित करती हैं और नृत्य करती हैं। कालबेलिया नृत्य इसी नाम से जाने जाने वाले लोगों की एक जनजाति, कालबेलिया द्वारा किया जाता है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "36",
                    section: "18",
                    question_en: "<p>36. Only one Indian batsman has scored a triple century in test cricket other than Virender Sehwag. Name this batsman.</p>",
                    question_hi: "<p>36. टेस्ट क्रिकेट में वीरेंद्र सहवाग के अलावा केवल एक ही भारतीय बल्लेबाज ने तिहरा शतक लगाया है | उस बल्लेबाज का नाम क्या है ?</p>",
                    options_en: ["<p>Ajinkya Rahane</p>", "<p>Rohit Sharma</p>", 
                                "<p>Shikhar Dhawan</p>", "<p>Karun Nai</p>"],
                    options_hi: ["<p>अजिंक्य रहाणे</p>", "<p>रोहित शर्मा</p>",
                                "<p>शिखर धवन</p>", "<p>करुण नायर</p>"],
                    solution_en: "<p>36.(d) Karun Nair is the only indian cricket other than Virendra Sehwag to score triple hundred in the test match. He scored 303 runs against England batting at number 5.</p>",
                    solution_hi: "<p>36.(d) टेस्ट मैच में तिहरा शतक लगाने वाले वीरेंद्र सहवाग के अलावा करुण नायर एकमात्र भारतीय क्रिकेट खिलाड़ी हैं। उन्होंने पांचवें नंबर पर बल्लेबाजी करते हुए इंग्लैंड के खिलाफ 303 रन बनाए।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "37",
                    section: "18",
                    question_en: "<p>37. Which of the following is an Indian Research Station in the Antarctica Region ?</p>",
                    question_hi: "<p>37. निम्नलिखित में से कौन सा अंटार्कटिका क्षेत्र में एक भारतीय अनुसंधान केंद्र है ?</p>",
                    options_en: ["<p>Orcadas</p>", "<p>Maitri</p>", 
                                "<p>Hope Bay</p>", "<p>Mawson</p>"],
                    options_hi: ["<p>ओरकाडस</p>", "<p>मैत्री</p>",
                                "<p>होप बे</p>", "<p>मेसन</p>"],
                    solution_en: "<p>37.(b) India has 3 Indian Research stations in Antarctica named Dakshin Gangotri (1983), Maitri (1989) and Bharti (2015).</p>",
                    solution_hi: "<p>37.(b) भारत के अंटार्कटिका में दक्षिण गंगोत्री (1983), मैत्री (1989) और भारती (2015) नामक 3 भारतीय अनुसंधान केंद्र हैं।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "38",
                    section: "18",
                    question_en: "<p>38. The 3rd Khelo India Youth Games 2020 is hosted by which Indian state ?</p>",
                    question_hi: "<p>38. तीसरा खेलो इंडिया यूथ गेम्स 2020 किस भारतीय राज्य द्वारा आयोजित किया गया है ?</p>",
                    options_en: ["<p>Assam</p>", "<p>Kerala</p>", 
                                "<p>Karnataka</p>", "<p>Punjab</p>"],
                    options_hi: ["<p>असम</p>", "<p>केरल</p>",
                                "<p>कर्नाटक</p>", "<p>पंजाब</p>"],
                    solution_en: "<p>38.(a) The 3<sup>rd</sup>. Khelo India Youth Games 2020 were held at Assam. 4<sup>th</sup> edition of Khelo India Youth Games is to be held in Haryana. 1<sup>st</sup> edition in New Delhi and 2<sup>nd</sup> edition in Pune.</p>",
                    solution_hi: "<p>38.(a) 3rd खेलो इंडिया यूथ गेम्स 2020 का आयोजन असम में किया गया। खेलो इंडिया यूथ गेम्स का चौथा संस्करण हरियाणा में आयोजित किया जाना है,&nbsp;पहला संस्करण नई दिल्ली में और दूसरा संस्करण पुणे में।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "39",
                    section: "18",
                    question_en: "<p>39. How many members did the International Monetary Fund (IMF) have as of January 2020 ?</p>",
                    question_hi: "<p>39. जनवरी 2020 तक अंतर्राष्ट्रीय मुद्रा कोष (IMF) में कितने सदस्य थे ?</p>",
                    options_en: ["<p>164</p>", "<p>174</p>", 
                                "<p>189</p>", "<p>182</p>"],
                    options_hi: ["<p>164</p>", "<p>174</p>",
                                "<p>189</p>", "<p>182</p>"],
                    solution_en: "<p>39.(c) IMF has 189 members as of January 2020 but Andorra became the 190th member on 16 October 2020. Nauru joined the IMF as the 189th country on April 16, 2016.</p>",
                    solution_hi: "<p>39.(c) जनवरी 2020 तक IMF के 189 सदस्य हैं लेकिन अंडोरा 16 अक्टूबर 2020 को 190वां सदस्य बन गया। नाउरू 16 अप्रैल, 2016 को 189वें देश के रूप में IMF में शामिल हो गया।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "40",
                    section: "18",
                    question_en: "<p>40. Name the company that has acquired an online travel portal Yatra Online inc., for an enterprise value of $337.8 million, in an-all stock transaction.</p>",
                    question_hi: "<p>40. उस कंपनी का नाम बताएं जिसने एक ऑनलाइन यात्रा पोर्टल &lsquo;यात्रा ऑनलाइन इंक&rsquo; का सभी अंशो सहित 337.8 मिलियन डॉलर उद्यम मूल्य पर अधिग्रहण किया है |</p>",
                    options_en: ["<p>Make my trip.com</p>", "<p>Trivago</p>", 
                                "<p>Ebix Inc</p>", "<p>Trip Advisor</p>"],
                    options_hi: ["<p>मेक माई ट्रिप.कॉम</p>", "<p>ट्रिवागो</p>",
                                "<p>एबिक्स इंक</p>", "<p>ट्रिप एडवाइजर</p>"],
                    solution_en: "<p>40.(c) Ebix Inc has acquired an online travel portal yatra online Inc., for an enterprise value of $337.8 million, in an all-stock transaction. Ebix Inc. is a supplier of on-demand software and e-commerce service more focused on the Business to Business Segment whereas Yatra Online is more focused on the Business to Customer segment.</p>",
                    solution_hi: "<p>40.(c) Ebix Inc ने सभी स्टॉक लेनदेन में $337.8 मिलियन के उद्यम मूल्य के लिए एक ऑनलाइन ट्रैवल पोर्टल yatra online Inc. का अधिग्रहण किया है। Ebix Inc. ऑन-डिमांड सॉफ़्टवेयर और ई-कॉमर्स सेवा का आपूर्तिकर्ता है जो व्यवसाय से व्यवसाय खंड पर अधिक केंद्रित है जबकि यात्रा ऑनलाइन व्यवसाय से ग्राहक खंड पर अधिक केंद्रित है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "41",
                    section: "18",
                    question_en: "<p>41. Irianin Major General Qasim Soleimani was recently (2020) assassinated by the US military in _______.</p>",
                    question_hi: "<p>41. इरानी मेजर जनरल कासिम सुलेमानी की हाल ही (2020) में अमेरिकी सेना के द्वारा ____में हत्या कर दी गयी थी |</p>",
                    options_en: ["<p>United Arab Emirates</p>", "<p>Iraq</p>", 
                                "<p>Saudi Arabia</p>", "<p>Pakistan</p>"],
                    options_hi: ["<p>संयुक्त अरब अमीरात</p>", "<p>इराक</p>",
                                "<p>सऊदी अरब</p>", "<p>पाकिस्तान</p>"],
                    solution_en: "<p>41.(b) Iranian Major General Qasim Soleimani was recently assassinated by the US military in Iraq. He was killed in a US airstrike ordered by President Donald Trump at Baghdad International Airport.</p>",
                    solution_hi: "<p>41.(b) ईरानी मेजर जनरल कासिम सुलेमानी की हाल ही में इराक में अमेरिकी सेना ने हत्या कर दी थी। वह बगदाद अंतरराष्ट्रीय हवाई अड्डे पर राष्ट्रपति डोनाल्ड ट्रम्प द्वारा आदेशित अमेरिकी हवाई हमले में मारा गया था।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "42",
                    section: "18",
                    question_en: "<p>42. Name the two Indian actors who shared the best National Best Actor Award (Male) in the 66th National Film Awards 2019 ?</p>",
                    question_hi: "<p>42. उन दो भारतीय अभिनेताओं का नाम बताइए जिन्होंने 66 वें राष्ट्रीय फिल्म पुरस्कार 2019 में सर्वश्रेष्ठ राष्ट्रीय अभिनेता पुरस्कार (पुरुष) साझा किया ?</p>",
                    options_en: ["<p>Ayushmann Khurrana and Vicky Kaushal</p>", "<p>Ranbir Kapoor and Akshay Kumar</p>", 
                                "<p>Nana Patekar and Mohanlal</p>", "<p>Amitabh Bachchan and Ranbir Kapoor</p>"],
                    options_hi: ["<p>आयुष्मान खुराना और विक्की कौशल</p>", "<p>रणबीर कपूर और अक्षय कुमार</p>",
                                "<p>नाना पाटेकर और मोहनलाल</p>", "<p>अमिताभ बच्चन और रणबीर कपूर</p>"],
                    solution_en: "<p>42.(a) Ayushmann Khurrana for Andhadhundh and Vicky Kaushal for Uri :The surgical strike won the National Best Actor Award (Male) in the 66th National Film Award 2019.</p>",
                    solution_hi: "<p>42.(c) अंधाधुंध के लिए आयुष्मान खुराना और उरी के लिए विक्की कौशल: सर्जिकल स्ट्राइक ने 66 वें राष्ट्रीय फिल्म पुरस्कार 2019 में राष्ट्रीय सर्वश्रेष्ठ अभिनेता का पुरस्कार (पुरुष) जीता।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "43",
                    section: "18",
                    question_en: "<p>43. Former union minister and former Karnataka CM, D.V. Sadananda Gowda is a cabinet minister in the current central cabinet (February 2020). What is his portfolio ?</p>",
                    question_hi: "<p>43. पूर्व केंद्रीय मंत्री और कर्नाटक के पूर्व सीएम, डी.वी. सदानंद गौड़ा वर्तमान केंद्रीय कैबिनेट (फरवरी 2020) में एक कैबिनेट मंत्री हैं। उनका पोर्टफोलियो क्या है ?</p>",
                    options_en: ["<p>Law and Justice</p>", "<p>Chemicals and Fertilizers</p>", 
                                "<p>Public Distribution</p>", "<p>Tribal Affairs</p>"],
                    options_hi: ["<p>कानून और न्याय</p>", "<p>रसायन और उर्वरक</p>",
                                "<p>सार्वजनिक वितरण</p>", "<p>जनजातीय मामले</p>"],
                    solution_en: "<p>43.(b) Minister of Chemicals and Fertilizers- D.V. Sadananda Gowda<br>Minister of Law and Justice - Ravi Shankar Prasad<br>Minister of Tribal Affairs &ndash; Arjun Munda<br>Public Distribution Minister - Ram Vilas Paswan</p>",
                    solution_hi: "<p>43.(b) रसायन और उर्वरक मंत्री- डी.वी. सदानंद गौड़ा<br>कानून और न्याय मंत्री - रविशंकर प्रसाद<br>जनजातीय मामलों के मंत्री&ndash; अर्जुन मुंडा<br>सार्वजनिक वितरण मंत्री - रामविलास पासवान।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "44",
                    section: "18",
                    question_en: "<p>44. Name the author of the 2019 released book- \'The Scent of God\'.</p>",
                    question_hi: "<p>44. 2019 में प्रकाशित \'The Scent of God\' के लेखक कौन है ?</p>",
                    options_en: ["<p>Nayantara Sehgal</p>", "<p>Githa Hariharan</p>", 
                                "<p>Saikat Majumdar</p>", "<p>Himanjali Sankar</p>"],
                    options_hi: ["<p>नयनतारा सहगल</p>", "<p>गीता हरिहरन</p>",
                                "<p>सैकत मजूमदार</p>", "<p>हिमांजलि शंकर</p>"],
                    solution_en: "<p>44.(c) The author of the book &lsquo;The Scent of God\' is Saikat Majumdar. This book raises the question about some uncomfortable question about life in celibate, monastic orders.</p>",
                    solution_hi: "<p>44.(c) \'द सेंट ऑफ गॉड\' पुस्तक के लेखक सैकत मजूमदार हैं। यह पुस्तक ब्रह्मचारी, मठवासी व्यवस्था में जीवन के बारे में कुछ असहज प्रश्न के बारे में प्रश्न उठाती है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "45",
                    section: "18",
                    question_en: "<p>45. From which of the following states does 2019 Padma Vibhushan Winner, Teejan Bai hail ?</p>",
                    question_hi: "<p>45. 2019 पद्म विभूषण विजेता, तीजन बाई जय निम्नलिखित में से किस राज्य से हैं ?</p>",
                    options_en: ["<p>Chhattisgarh</p>", "<p>Gujarat</p>", 
                                "<p>Odisha</p>", "<p>Telangana</p>"],
                    options_hi: ["<p>छत्तीसगढ़</p>", "<p>गुजरात</p>",
                                "<p>ओडिशा</p>", "<p>तेलंगाना</p>"],
                    solution_en: "<p>45.(a) 2019 Padma vibhushan winner Teejan Bai is from Chhattisgarh. She has been awarded the&nbsp;Padma Shri in 1987, Padma Bhushan in 2003, and Padma Vibhushan in 2019 by the Government of India.</p>",
                    solution_hi: "<p>45.(a) 2019 पद्म विभूषण विजेता तीजन बाई छत्तीसगढ़ से हैं। उन्हें भारत सरकार द्वारा 1987 में पद्म श्री, 2003 में पद्म भूषण और 2019 में पद्म विभूषण से सम्मानित किया जा चुका है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "46",
                    section: "18",
                    question_en: "<p>46. Who is the author of the delightful and anecdotal history of Indian cricket titled- \'A Corner of a foreign field: The Indian history of a British sport\' ?</p>",
                    question_hi: "<p>46. भारतीय क्रिकेट के रमणीय और अनोखे इतिहास शीर्षक- \'A Corner of a foreign field: The Indian history of a British sport\' का लेखक कौन है ?</p>",
                    options_en: ["<p>Sanjay Singh</p>", "<p>Ramchandra Guha</p>", 
                                "<p>Bipin Chandra</p>", "<p>Romila Thapar</p>"],
                    options_hi: ["<p>संजय सिंह</p>", "<p>रामचंद्र गुहा</p>",
                                "<p>बिपिन चंद्र</p>", "<p>रोमिला थापर</p>"],
                    solution_en: "<p>46.(b) A Corner of a Foreign field: The Indian history of a British sport is written by Ramchandran Guha.</p>",
                    solution_hi: "<p>46.(b) A Corner of a foreign field: The Indian history of a British sport&rsquo; रामचंद्रन गुहा द्वारा लिखा गया है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "47",
                    section: "18",
                    question_en: "<p>47. For multi-storied illegal constructions - Jain Coral Cove, H20 Holy Faith, Alfa Serene and Golden Kayaloram - were razed to the ground in January 2020 following Supreme Court instructions. In which state did this happen ?</p>",
                    question_hi: "<p>47. चार बहुमंज़िला अवैध निर्माणों - जैन कोरल कोव, H20 होली फेथ, अल्फा सेरेन तथा गोल्डन कायालोरम - को सर्वोच्च न्यायालय के निर्देशानुसार जनवरी 2020 में ध्वस्त कर दिया गया था | यह घटना किस राज्य में हुई ?</p>",
                    options_en: ["<p>Karnataka</p>", "<p>Goa</p>", 
                                "<p>Kerala</p>", "<p>Tamil Nadu</p>"],
                    options_hi: ["<p>कर्नाटक</p>", "<p>गोवा</p>",
                                "<p>केरल</p>", "<p>तमिलनाडु</p>"],
                    solution_en: "<p>47.(c) Four multi-storied illegal constructions - Jain Coral Cove, H20 Holy Faith, Alfa Serene, and Golden Kayaloram - were razed to the ground in January 2020 following Supreme Court instructions. This incident happened in kerala.</p>",
                    solution_hi: "<p>47.(c) सुप्रीम कोर्ट के निर्देशों के बाद जनवरी 2020 में चार बहु-मंजिला अवैध निर्माण - जैन कोरल कोव, एच 20 होली फेथ, अल्फा सेरेन और गोल्डन कयालोरम को धराशायी कर दिया गया था। ये घटना केरल की है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "48",
                    section: "18",
                    question_en: "<p>48. The recently revoked \'Article 370\' is associated with which of these states of India ?</p>",
                    question_hi: "<p>48. हाल ही में निरस्त किये गए अनुच्छेद 370 का संबंध भारत के निम्न में से किस राज्य से था ?</p>",
                    options_en: ["<p>Assam</p>", "<p>Sikkim</p>", 
                                "<p>Nagaland</p>", "<p>Kashmir</p>"],
                    options_hi: ["<p>असम</p>", "<p>सिक्किम</p>",
                                "<p>नागालैंड</p>", "<p>कश्मीर</p>"],
                    solution_en: "<p>48.(d) Article 370 was a constitutional provision giving special status to Jammu and Kashmir. The Government of India removed this status on 31 October 2019 making Jammu and Kashmir and Ladakh two separate Union Territories.</p>",
                    solution_hi: "<p>48.(d) अनुच्छेद 370 जम्मू-कश्मीर को विशेष दर्जा देने वाला एक संवैधानिक प्रावधान था। भारत सरकार ने 31 अक्टूबर 2019 को जम्मू-कश्मीर और लद्दाख को दो अलग केंद्र शासित प्रदेश बनाते हुए इस दर्जे को हटा दिया।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "49",
                    section: "18",
                    question_en: "<p>49. Who among is the current (January 2020) Governor of Kerala ?</p>",
                    question_hi: "<p>49. केरल के वर्तमान (जनवरी 2020) राज्यपाल कौन हैं ?</p>",
                    options_en: ["<p>Arif Mohammad Khan</p>", "<p>Satya Pal Malik</p>", 
                                "<p>Lalji Tandon</p>", "<p>B.D. Sharma</p>"],
                    options_hi: ["<p>आरिफ मोहम्मद खान</p>", "<p>सत्य पाल मलिक</p>",
                                "<p>लालजी टंडन</p>", "<p>बी.डी. शर्मा</p>"],
                    solution_en: "<p>49.(a) Arif Mohamad Khan is the 22<sup>nd</sup> governor of Kerala (January 2020). He was appointed as&nbsp;governor on 6 September 2019.</p>",
                    solution_hi: "<p>49.(a) आरिफ मोहम्मद खान केरल के 22वें राज्यपाल हैं (जनवरी 2020)। उन्हें 6 सितंबर 2019 को राज्यपाल के रूप में नियुक्त किया गया था।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "50",
                    section: "17",
                    question_en: "<p>50. The city of Vijayawada lies on the banks of which of these rivers ?</p>",
                    question_hi: "<p>50. विजयवाड़ा शहर किस नदी के तट पर स्थित है ?</p>",
                    options_en: ["<p>Mahanadi</p>", "<p>Tapti</p>", 
                                "<p>Krishna</p>", "<p>Godavari</p>"],
                    options_hi: ["<p>महानदी</p>", "<p>ताप्ति</p>",
                                "<p>कृष्णा</p>", "<p>गोदावरी</p>"],
                    solution_en: "<p>50.(c)<br>Vijayawada is on the bank of the river Krishna. The Krishna River is the fourth-biggest river in terms of water inflows and river basin area in India, after the Ganga, Godavari and Brahmaputra. The Krishna river originates in the Western Ghats near Mahabaleshwar in Maharashtra.</p>",
                    solution_hi: "<p>50.(c) विजयवाड़ा कृष्णा नदी के तट पर है। गंगा, गोदावरी और ब्रह्मपुत्र के बाद कृष्णा नदी भारत में जल प्रवाह और नदी बेसिन क्षेत्र के मामले में चौथी सबसे बड़ी नदी है। कृष्णा नदी महाराष्ट्र में महाबलेश्वर के पास पश्चिमी घाट से निकलती है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "51",
                    section: "17",
                    question_en: "<p>51. The given table represents the production of different types of motorcycles (in thousands) over a period of six years. Study the table carefully and answer the questions that follows:<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744023372101.png\" alt=\"rId46\" width=\"379\" height=\"218\"> <br>During 2015, the production of which type of motorcycle was more than 25% of the total production of all types of motorcycles in 2017 ?</p>",
                    question_hi: "<p>51. दी गयी तालिका छः वर्षों की अवधि के दौरान अलग-अलग प्रकार की मोटर साइकिलों के उत्पादन (हज़ार में) को दर्शाती है | इस तालिका का ध्यानपूर्वक अध्ययन करें तथा फिर पूछे गए प्रश्नों का उत्तर दीजिए |&nbsp;<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744023373424.png\" alt=\"rId47\" width=\"351\" height=\"207\"> <br>2015 के दौरान, किस प्रकार की मोटरसाइकिल का उत्पादन 2017 में सभी प्रकार की मोटर साइकिलों के कुल उत्पादन के 25% से अधिक रहा है ?</p>",
                    options_en: ["<p>A</p>", "<p>B</p>", 
                                "<p>C</p>", "<p>D</p>"],
                    options_hi: ["<p>A</p>", "<p>B</p>",
                                "<p>C</p>", "<p>D</p>"],
                    solution_en: "<p>51.(d) <br>Total production in 2017 = 80 + 96 + 100 + 104 = 380<br>25% of 380 = 95<br>In 2015, D had more than 25% of total production in 2017.</p>",
                    solution_hi: "<p>51.(d) 2017 में कुल उत्पादन&nbsp;= 80 + 96 + 100 + 104 = 380<br>380 का 25% = 95<br>2015 में, D का 2017 में कुल उत्पादन का 25% से अधिक था।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "52",
                    section: "17",
                    question_en: "<p>52. The average age of a number of persons in a group was calculated as 35 years, which was 2.5 years more than the correct average as there was an error in recording the age of two persons as 38.5 years and 40 years instead of 29 years and 22 years respectively. The number of persons in the group was:</p>",
                    question_hi: "<p>52. एक समूह में कुछ लोगों की औसत उम्र की गणना 35 वर्ष की गयी, जो वास्तविक औसत से 2.5 वर्ष अधिक थी, क्योंकि दो व्यक्तियों की उम्र 29 वर्ष तथा 22 वर्ष के स्थान पर भूलवश 38.5 वर्ष तथा 40 वर्ष दर्ज कर ली गयी थी | इस समूह में लोगों की संख्या कितनी थी ?</p>",
                    options_en: ["<p>11</p>", "<p>12</p>", 
                                "<p>15</p>", "<p>13</p>"],
                    options_hi: ["<p>11</p>", "<p>12</p>",
                                "<p>15</p>", "<p>13</p>"],
                    solution_en: "<p>52.(a)<br>Let the number of persons in a group is &lsquo;x&rsquo;<br>35 &times; x = S<sub>x-2 </sub>+ 38.5 + 40 &hellip; (1)<br>Correct average = 35-2.5 = 32.5<br>32.5 &times; x = S<sub>x-2 </sub>+ 29 + 22 &hellip; (2)<br>(1)-(2) &rArr; 2.5*x = 27.5<br>x = 11 person</p>",
                    solution_hi: "<p>52.(a) <br>मान लीजिए एक समूह में \'x\' व्यक्ति हैं।<br>35 &times; x = S<sub>x-2 </sub>+ 38.5 + 40 &hellip; (1)<br>सही औसत = 35-2.5 = 32.5<br>32.5 &times; x = S<sub>x-2 </sub>+ 29 + 22 &hellip; (2)<br>(1)-(2) &rArr; 2.5*x = 27.5<br>x = 11 व्यक्ति</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "53",
                    section: "17",
                    question_en: "<p>53. If sec&theta; + tan&theta; = p, 0&deg; &lt; &theta; &lt; 90&deg;, then <math display=\"inline\"><mfrac><mrow><msup><mrow><mi>p</mi></mrow><mrow><mn>2</mn></mrow></msup><mo>-</mo><mn>1</mn></mrow><mrow><msup><mrow><mi>p</mi></mrow><mrow><mn>2</mn></mrow></msup><mo>+</mo><mn>1</mn></mrow></mfrac></math> is equal to:</p>",
                    question_hi: "<p>53. यदि sec&theta; + tan&theta; = p है, जहाँ 0&deg; &lt; &theta; &lt; 90&deg; है, तो <math display=\"inline\"><mfrac><mrow><msup><mrow><mi>p</mi></mrow><mrow><mn>2</mn></mrow></msup><mo>-</mo><mn>1</mn></mrow><mrow><msup><mrow><mi>p</mi></mrow><mrow><mn>2</mn></mrow></msup><mo>+</mo><mn>1</mn></mrow></mfrac></math> का मान किसके बराबर होगा ?</p>",
                    options_en: ["<p>cosec&theta;</p>", "<p>sin&theta;</p>", 
                                "<p>cos&theta;</p>", "<p>2cosec&theta;</p>"],
                    options_hi: ["<p>cosec&theta;</p>", "<p>sin&theta;</p>",
                                "<p>cos&theta;</p>", "<p>2cosec&theta;</p>"],
                    solution_en: "<p>53.(b) <br>sec&theta; + tan&theta; = p&nbsp;<br>Then, sec&theta; - tan&theta; =<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mi>p</mi></mrow></mfrac></math> (sec<sup>2</sup>&theta; - tan<sup>2</sup>&theta; = 1)<br>p + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>p</mi></mfrac></math> = 2 sec&theta;<br>p - <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>p</mi></mfrac></math> = 2 tan&theta;<br><math display=\"inline\"><mfrac><mrow><msup><mrow><mi>p</mi></mrow><mrow><mn>2</mn></mrow></msup><mo>-</mo><mn>1</mn></mrow><mrow><msup><mrow><mi>p</mi></mrow><mrow><mn>2</mn></mrow></msup><mo>+</mo><mn>1</mn></mrow></mfrac></math>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>p</mi><mo>-</mo><mstyle displaystyle=\"true\"><mfrac><mn>1</mn><mi>p</mi></mfrac></mstyle></mrow><mrow><mi>p</mi><mo>+</mo><mstyle displaystyle=\"true\"><mfrac><mn>1</mn><mi>p</mi></mfrac></mstyle></mrow></mfrac></math> = sin&theta;</p>",
                    solution_hi: "<p>53.(b) <br>sec&theta; + tan&theta; = p&nbsp;<br>तो, sec&theta; - tan&theta; =<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mi>p</mi></mrow></mfrac></math> (sec<sup>2</sup>&theta; - tan<sup>2</sup>&theta; = 1)<br>p + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>p</mi></mfrac></math> = 2 sec&theta;<br>p - <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>p</mi></mfrac></math> = 2 tan&theta;<br><math display=\"inline\"><mfrac><mrow><msup><mrow><mi>p</mi></mrow><mrow><mn>2</mn></mrow></msup><mo>-</mo><mn>1</mn></mrow><mrow><msup><mrow><mi>p</mi></mrow><mrow><mn>2</mn></mrow></msup><mo>+</mo><mn>1</mn></mrow></mfrac></math>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>p</mi><mo>-</mo><mstyle displaystyle=\"true\"><mfrac><mn>1</mn><mi>p</mi></mfrac></mstyle></mrow><mrow><mi>p</mi><mo>+</mo><mstyle displaystyle=\"true\"><mfrac><mn>1</mn><mi>p</mi></mfrac></mstyle></mrow></mfrac></math> = sin&theta;</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "54",
                    section: "17",
                    question_en: "<p>54. The chords AB and CD of a circle intersect at E. If AE = 12cm, BE = 20.25 cm and CE = 3DE, then the length (in cm) of CE is:</p>",
                    question_hi: "<p>54. एक वृत्त की दो जीवाएँ AB तथा CD एक-दूसरे को E पर काटती हैं | यदि AE = 12 सेमी, BE = 20.25 सेमी तथा CE = 3DE है, तो CE की लंबाई (सेमी में) कितनी होगी ?</p>",
                    options_en: ["<p>27</p>", "<p>25.5</p>", 
                                "<p>18</p>", "<p>28.5</p>"],
                    options_hi: ["<p>27</p>", "<p>25.5</p>",
                                "<p>18</p>", "<p>28.5</p>"],
                    solution_en: "<p>54.(a)<br>AB and CD are two chords in a circle.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744023373788.png\" alt=\"rId48\" width=\"149\" height=\"138\"><br>&rArr; AE &times; BE = CE &times; DE<br>&rArr; 12 &times; 20.25 = 3x<sup>2</sup><br>&rArr; 81 = x<sup>2</sup><br>x = 9 cm<br>CE = 27 cm</p>",
                    solution_hi: "<p>54.(a)<br>AB और CD एक वृत्त की दो जीवाएँ हैं।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744023373788.png\" alt=\"rId48\" width=\"149\" height=\"138\"><br>&rArr; AE &times; BE = CE &times; DE<br>&rArr; 12 &times; 20.25 = 3x<sup>2</sup><br>&rArr; 81 = x<sup>2</sup><br>x = 9 cm<br>CE = 27 cm</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "55",
                    section: "17",
                    question_en: "<p>55. If x + y + z = 3 and x<sup>2 </sup>+ y<sup>2 </sup>+ z<sup>2</sup> = 101, then what is the value of <math display=\"inline\"><msqrt><msup><mrow><mi>x</mi></mrow><mrow><mn>3</mn></mrow></msup><mo>+</mo><msup><mrow><mi>y</mi></mrow><mrow><mn>3</mn></mrow></msup><mo>+</mo><msup><mrow><mi>z</mi></mrow><mrow><mn>3</mn></mrow></msup><mo>-</mo><mn>3</mn><mi>x</mi><mi>y</mi><mi>z</mi></msqrt></math> ?</p>",
                    question_hi: "<p>55. यदि x + y + z = 3 तथा x<sup>2 </sup>+ y<sup>2 </sup>+ z<sup>2</sup> = 101 है, तो <math display=\"inline\"><msqrt><msup><mrow><mi>x</mi></mrow><mrow><mn>3</mn></mrow></msup><mo>+</mo><msup><mrow><mi>y</mi></mrow><mrow><mn>3</mn></mrow></msup><mo>+</mo><msup><mrow><mi>z</mi></mrow><mrow><mn>3</mn></mrow></msup><mo>-</mo><mn>3</mn><mi>x</mi><mi>y</mi><mi>z</mi></msqrt></math> का मान क्या होगा ?</p>",
                    options_en: ["<p>19</p>", "<p>21</p>", 
                                "<p>24</p>", "<p>28</p>"],
                    options_hi: ["<p>19</p>", "<p>21</p>",
                                "<p>24</p>", "<p>28</p>"],
                    solution_en: "<p>55.(b) <br>It is given that x + y + z = 3...(i) and x<sup>2 </sup>+ y<sup>2 </sup>+ z<sup>2</sup> = 101&hellip;(ii)<br>On squaring (i), we get:<br>x<sup>2 </sup>+ y<sup>2 </sup>+ z<sup>2 </sup>+ 2(xy + yz + zx) = 9<br>101 + 2(xy + yz + zx) = 9<br>xy + yz + zx = - 46<br><math display=\"inline\"><msqrt><msup><mrow><mi>x</mi></mrow><mrow><mn>3</mn></mrow></msup><mo>+</mo><msup><mrow><mi>y</mi></mrow><mrow><mn>3</mn></mrow></msup><mo>+</mo><msup><mrow><mi>z</mi></mrow><mrow><mn>3</mn></mrow></msup><mo>-</mo><mn>3</mn><mi>x</mi><mi>y</mi><mi>z</mi></msqrt></math>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mo>(</mo><mi>x</mi><mo>+</mo><mi>y</mi><mo>+</mo><mi>z</mi><mo>)</mo><mo>(</mo><msup><mi>x</mi><mn>2</mn></msup><mo>+</mo><msup><mi>y</mi><mn>2</mn></msup><mo>+</mo><msup><mi>z</mi><mn>2</mn></msup><mo>-</mo><mo>(</mo><mi>x</mi><mi>y</mi><mo>+</mo><mi>y</mi><mi>z</mi><mo>+</mo><mi>z</mi><mi>x</mi><mo>)</mo><mo>)</mo></msqrt></math><br>= <math display=\"inline\"><msqrt><mo>(</mo><mn>3</mn><mo>)</mo><mo>(</mo><mn>101</mn><mo>-</mo><mo>(</mo><mo>-</mo><mn>46</mn><mo>)</mo><mo>)</mo></msqrt></math>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mo>(</mo><mn>3</mn><mo>)</mo><mo>(</mo><mn>147</mn><mo>)</mo></msqrt></math> = 21</p>",
                    solution_hi: "<p>55.(b) दिया गया है की,<br>x + y + z = 3...(i) और x<sup>2 </sup>+ y<sup>2 </sup>+ z<sup>2</sup> = 101&hellip;(ii)<br>समीकरण (i) का वर्ग करने पर, हम प्राप्त करतेहैं,<br>x<sup>2 </sup>+ y<sup>2 </sup>+ z<sup>2 </sup>+ 2(xy + yz + zx) = 9<br>101 + 2(xy + yz + zx) = 9<br>xy + yz + zx = - 46<br><math display=\"inline\"><msqrt><msup><mrow><mi>x</mi></mrow><mrow><mn>3</mn></mrow></msup><mo>+</mo><msup><mrow><mi>y</mi></mrow><mrow><mn>3</mn></mrow></msup><mo>+</mo><msup><mrow><mi>z</mi></mrow><mrow><mn>3</mn></mrow></msup><mo>-</mo><mn>3</mn><mi>x</mi><mi>y</mi><mi>z</mi></msqrt></math>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mo>(</mo><mi>x</mi><mo>+</mo><mi>y</mi><mo>+</mo><mi>z</mi><mo>)</mo><mo>(</mo><msup><mi>x</mi><mn>2</mn></msup><mo>+</mo><msup><mi>y</mi><mn>2</mn></msup><mo>+</mo><msup><mi>z</mi><mn>2</mn></msup><mo>-</mo><mo>(</mo><mi>x</mi><mi>y</mi><mo>+</mo><mi>y</mi><mi>z</mi><mo>+</mo><mi>z</mi><mi>x</mi><mo>)</mo><mo>)</mo></msqrt></math><br>= <math display=\"inline\"><msqrt><mo>(</mo><mn>3</mn><mo>)</mo><mo>(</mo><mn>101</mn><mo>-</mo><mo>(</mo><mo>-</mo><mn>46</mn><mo>)</mo><mo>)</mo></msqrt></math>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mo>(</mo><mn>3</mn><mo>)</mo><mo>(</mo><mn>147</mn><mo>)</mo></msqrt></math> = 21</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "56",
                    section: "17",
                    question_en: "<p>56. If 12x<sup>2</sup> - 21x + 1 = 0, then what is the value of 9x<sup>2</sup> + (16x<sup>2</sup>)<sup>-1</sup> ?</p>",
                    question_hi: "<p>56. यदि 12x<sup>2 </sup>- 21x + 1 = 0 है, तो 9x<sup>2 </sup>+ (16x<sup>2</sup>)<sup>-1</sup> का मान क्या होगा ?</p>",
                    options_en: ["<p><math display=\"inline\"><mfrac><mrow><mn>429</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>465</mn></mrow><mrow><mn>16</mn></mrow></mfrac></math></p>", 
                                "<p><math display=\"inline\"><mfrac><mrow><mn>417</mn></mrow><mrow><mn>16</mn></mrow></mfrac><mi>&#160;</mi></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>453</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math></p>"],
                    options_hi: ["<p><math display=\"inline\"><mi>&#160;</mi><mfrac><mrow><mn>429</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mi>&#160;</mi><mfrac><mrow><mn>465</mn></mrow><mrow><mn>16</mn></mrow></mfrac></math></p>",
                                "<p><math display=\"inline\"><mi>&#160;</mi><mfrac><mrow><mn>417</mn></mrow><mrow><mn>16</mn></mrow></mfrac><mi>&#160;</mi></math></p>", "<p><math display=\"inline\"><mi>&#160;</mi><mfrac><mrow><mn>453</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math></p>"],
                    solution_en: "<p>56.(c) <br>12x<sup>2 </sup>- 21x + 1 = 0&nbsp;<br>&rArr; 12x<sup>2 </sup>+ 1 = 21x<br>&rArr; 4x + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&#160;</mo><mn>1</mn><mo>&#160;</mo></mrow><mrow><mn>3</mn><mi>x</mi></mrow></mfrac></math> = 7<br>On multiplying both sides by (<math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math>) and then squaring both sides, we get:<br>&rArr; 9x<sup>2 </sup>+ <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><mn>16</mn><msup><mi>x</mi><mn>2</mn></msup></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>417</mn><mn>16</mn></mfrac></math></p>",
                    solution_hi: "<p>56.(c)&nbsp;<br>12x<sup>2 </sup>- 21x + 1 = 0&nbsp;<br>&rArr; 12x<sup>2 </sup>+ 1 = 21x<br>&rArr; 4x + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&#160;</mo><mn>1</mn><mo>&#160;</mo></mrow><mrow><mn>3</mn><mi>x</mi></mrow></mfrac></math> = 7<br>दोनों पक्षों को (<math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math>) से गुणा करने पर और फिर दोनों पक्षों का वर्ग करने पर, हम प्राप्त करते हैं |<br>&rArr; 9x<sup>2 </sup>+ <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><mn>16</mn><msup><mi>x</mi><mn>2</mn></msup></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>417</mn><mn>16</mn></mfrac></math></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "57",
                    section: "17",
                    question_en: "<p>&nbsp;57. A sum of ₹x was divided between A, B, C and D in the ratio <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math> :&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>1</mn><mn>5</mn></mfrac></mstyle></math> : <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>1</mn><mn>6</mn></mfrac></mstyle></math> : <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>1</mn><mn>9</mn></mfrac></mstyle></math>. If the difference between the shares of B and D is ₹ 832, then the value of x is:</p>",
                    question_hi: "<p>57. X रुपये की राशि का विभाजन A, B, C और D के बीच <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math> :&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>1</mn><mn>5</mn></mfrac></mstyle></math> : <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>1</mn><mn>6</mn></mfrac></mstyle></math> : <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>1</mn><mn>9</mn></mfrac></mstyle></math>&nbsp;के अनुपात में किया गया | यदि B और D के हिस्सों में 832 रुपये का अंतर है, तो x का मान ज्ञात कीजिए</p>",
                    options_en: ["<p>₹7,592</p>", "<p>₹7,384</p>", 
                                "<p>₹7,696</p>", "<p>₹7,488</p>"],
                    options_hi: ["<p>₹7,592</p>", "<p>₹7,384</p>",
                                "<p>₹7,696</p>", "<p>₹7,488</p>"],
                    solution_en: "<p>57.(a) <br>A : B : C : D = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math> :&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>1</mn><mn>5</mn></mfrac></mstyle></math> : <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>1</mn><mn>6</mn></mfrac></mstyle></math> : <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>1</mn><mn>9</mn></mfrac></mstyle></math><br>&rArr; A : B : C : D = 30 : 18 : 15 : 10 = 73 units<br>&rArr; B - D = 8 units = ₹832<br>&rArr; 1 unit = 104<br>73 units = 73 &times; 104 = ₹ 7,592</p>",
                    solution_hi: "<p>57.(a) <br>A : B : C : D = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math> :&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>1</mn><mn>5</mn></mfrac></mstyle></math> : <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>1</mn><mn>6</mn></mfrac></mstyle></math> : <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>1</mn><mn>9</mn></mfrac></mstyle></math><br>&rArr; A : B : C : D = 30 : 18 : 15 : 10 = 73 इकाइयां<br>&rArr; B - D = 8 इकाइयां = ₹832<br>&rArr; 1 इकाई = 104<br>73 इकाइयां = 73 &times; 104 = ₹ 7,592</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "58",
                    section: "17",
                    question_en: "<p>58. The area of field in shape of a regular hexagon is 2400<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math>m<sup>2</sup>. The cost of fencing the field at ₹16.80 metre is:</p>",
                    question_hi: "<p>58. सम षट्भुज के आकार वाले एक मैदान का क्षेत्रफल 2400<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math> वर्ग मीटर है | 16.80 रुपये प्रति मीटर की दर से इस मैदान पर बाड़ लगाने की लागत ज्ञात कीजिए |</p>",
                    options_en: ["<p>₹4,536</p>", "<p>₹3,024</p>", 
                                "<p>₹4,032</p>", "<p>₹3,528</p>"],
                    options_hi: ["<p>₹4,536</p>", "<p>₹3,024</p>",
                                "<p>₹4,032</p>", "<p>₹3,528</p>"],
                    solution_en: "<p>58.(c) <br>Area of regular hexagon = 6 &times; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msqrt><mn>3</mn></msqrt><mn>4</mn></mfrac></math> &times; side<sup>2</sup><br>= 2400<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math><br>side = 40 m<br>Perimeter of hexagon = 6 &times; side = 240 m<br>Cost of fencing = 240 &times; 16.80 = ₹4,032</p>",
                    solution_hi: "<p>58.(c)<br>नियमित षट्भुज का क्षेत्रफल = 6 &times; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msqrt><mn>3</mn></msqrt><mn>4</mn></mfrac></math> &times; भुजा<sup>2</sup><br>= 2400<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math><br>भुजा = 40 m<br>षट्भुज का परिमाप = 6 &times; भुजा = 240 m<br>बाड़ लगाने की लागत = 240 &times; 16.80 = ₹4,032</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "59",
                    section: "17",
                    question_en: "<p>59. Places A and B are 144 km apart. Two cars start simultaneously, one from A and the other from B. If they move in the same direction, they meet after 12 hours, but if they move towards each other they meet after <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>9</mn><mn>8</mn></mfrac></math>hours. The speed(in km/h) of the car moving at a faster speed, is:</p>",
                    question_hi: "<p>59. स्थान A और B 144 किमी दूर हैं | दो कारें एक ही समय चलना शुरू करती हैं, पहली कार A से तथा दूसरी कार B से | ये वे समान दिशा में चलती हैं, तो वे 12 घंटों के बाद मिलती हैं, लेकिन यदि वे एक-दूसरे की ओर चलती हैं, तो <math display=\"inline\"><mfrac><mrow><mn>9</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math> घंटे के बाद मिलती हैं| तीव्र गति से चलने वाली कार की चाल (किमी/घंटा में) कितनी है ?</p>",
                    options_en: ["<p>70</p>", "<p>72</p>", 
                                "<p>60</p>", "<p>64</p>"],
                    options_hi: ["<p>70</p>", "<p>72</p>",
                                "<p>60</p>", "<p>64</p>"],
                    solution_en: "<p>59.(a) <br>Distance between A and B = 144 km <br>Let speed of A = a km/h<br>Speed of B = b km/h<br>For same direction, meeting time = 12 hour<br>&rArr; 12 &times; (a - b) = 144 &hellip;(i)<br>For opposite direction, meeting time <br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>9</mn><mn>8</mn></mfrac></math>hr<br>&rArr; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>9</mn><mn>8</mn></mfrac></math> &times; (a + b) = 144 &hellip;(ii)<br>We get: 12 &times; (a - b) = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>9</mn><mn>8</mn></mfrac></math> &times; (a + b)<br>a : b = 35 : 29<br>a = 35x<br>b = 29x<br>Put values in (i): 12 &times; (6x) = 144<br>x = 2<br>a = 70 km/h</p>",
                    solution_hi: "<p>59.(a) <br>A और B के बीच की दूरी = 144 किमी<br>माना A की गति = a किमी/घंटा<br>B की गति = b किमी/घंटा<br>उसी दिशा के लिए, मिलने का समय = 12 घंटा<br>&rArr; 12 &times; (a - b) = 144 &hellip;(i)<br>विपरीत दिशा के लिए, मिलने का समय = <math display=\"inline\"><mfrac><mrow><mn>9</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math>घंटा<br>&rArr; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>9</mn><mn>8</mn></mfrac></math> &times; (a + b) = 144 &hellip;(ii)<br>12 &times; (a - b) =&nbsp;<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>9</mn><mn>8</mn></mfrac></math> &times; (a + b)<br>a : b = 35 : 29<br>a = 35x<br>b = 29x<br>मानों को (i) में रखें: 12 &times; (6x) = 144<br>x = 2<br>a = 70 किमी/घंटा</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "60",
                    section: "17",
                    question_en: "<p>60. A, B and C donate 8%,7% and 9%, of their salaries respectively to a charitable trust. The salaries of A and B are the same and the difference between their donations is ₹259. The total donation of A and B is ₹1,185 more than that of C. The total donation of A and C is what percentage of the total salaries of A,B and C ? (Correct to one decimal place)</p>",
                    question_hi: "<p>60. A, B तथा C अपने वेतन का क्रमशः 8%, 7% तथा 9% भाग एक पुण्यार्थ ट्रस्ट में दान कर देते हैं | A और B के वेतन समान हैं तथा उनके दान में 259 रुपये का अंतर है | A और B का कुल दान C के दान से 1,185 रुपये अधिक है | A और C का कुल दान A, B तथा C के कुल वेतन का कितना प्रतिशत है ?</p>",
                    options_en: ["<p>6.2%</p>", "<p>5.8%</p>", 
                                "<p>6.4%</p>", "<p>7.1%</p>"],
                    options_hi: ["<p>6.2%</p>", "<p>5.8%</p>",
                                "<p>6.4%</p>", "<p>7.1%</p>"],
                    solution_en: "<p>60.(b) <br>Let salaries of A and B = ₹ x<br>As A donated 8% salary and B donated 7% salary and difference between their donation is ₹ 259<br>1% x = ₹ 259<br>Salary of each of A and B = ₹ 25900<br>Total donation of A and B = 15% of ₹ 25900 = ₹ 3885<br>C&rsquo;s donation = ₹ (3885 - 1185) = ₹ 2700<br>C&rsquo;s salary = ₹ 30000<br>A&rsquo;s donation = ₹ 2072<br>B&rsquo;s donation = ₹ 1813<br>Total salary of A,B and C = ₹ 81,800<br>Donation of A and C = ₹ 4772<br>Required % = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4772</mn><mn>81800</mn></mfrac></math> &times; 100 = 5.8%</p>",
                    solution_hi: "<p>60.(b)<br>माना A और B का वेतन = ₹ x<br>चूंकि A ने वेतन का 8% दान दिया और B ने वेतन का 7% दान दिया और उनके दान के बीच का अंतर ₹ 259 है। <br>x का 1% = ₹ 259<br>A और B के प्रत्येक का वेतन = ₹ 25900<br>A और B का कुल दान = 15% of ₹ 25900 = ₹ 3885<br>C का दान = ₹ (3885 - 1185) = ₹ 2700<br>C का वेतन = ₹ 30000<br>A का दान = ₹ 2072<br>B का दान = ₹ 1813<br>A,B और C का कुल वेतन = ₹ 81,800<br>A और C का कुल वेतन = ₹ 4772<br>आवश्यक % = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4772</mn><mn>81800</mn></mfrac></math> &times; 100 = 5.8%</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "61",
                    section: "17",
                    question_en: "<p>61. Four men and 6 women can complete a certain piece of work in 5 days whereas three men and 4 women can complete it in 7 days. How many should assist 25 women to complete 2<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math>times the same work in 5 days ?</p>",
                    question_hi: "<p>61. 4 पुरुष और 6 महिलाएं एक निश्चित काम को 5 दिनों में पूरा कर सकते हैं, जबकि तीन पुरुष और 4 महिलाएं इसे 7 दिनों में पूरा कर सकती हैं। समान कार्य के 2<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math>गुना कार्य को 5 दिनों में पूरा करने के लिए कितनी और पुरुष को 25 महिलाओं की सहायता करनी होगी ?</p>",
                    options_en: ["<p>8</p>", "<p>10</p>", 
                                "<p>4</p>", "<p>5</p>"],
                    options_hi: ["<p>8</p>", "<p>10</p>",
                                "<p>4</p>", "<p>5</p>"],
                    solution_en: "<p>61.(d) <br>Let efficiency of 1men = m units and efficiency of 1 woman = w units<br>According to question:<br>5(4m + 6w) = 7(3m + 4w)<br>&rArr; Efficiency of 1 men = 2women<br>Total work = 5[4(2w) + 6w] = 70w units.<br>Let x men assist 25 women to complete <math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math>times 70w units of work in 5 days.<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>2</mn></mfrac></math> &times; 70w = 5[25w + x(2w)]<br>&rArr; 175w = 125w + 10wx<br>&rArr; 50 = 10x<br>&rArr; x = 5</p>",
                    solution_hi: "<p>61.(d) <br>माना 1 पुरुष की क्षमता = m इकाई और 1 महिला की दक्षता = w इकाई <br>प्रश्न के अनुसार:<br>5(4m + 6w) = 7(3m + 4w)<br>1 पुरुषों की क्षमता = 2 महिलाओं की क्षमता <br>कुल कार्य = 5[4(2w) + 6w] = 70w इकाइयाँ।<br>माना x पुरुष 25 महिलाओं को 5 दिनों में 70w इकाइयों का <math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> गुना काम पूरा करने में सहायता करते हैं। <br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>2</mn></mfrac></math> &times; 70w = 5[25w + x(2w)]<br>&rArr; 175w = 125w + 10wx<br>&rArr; 50 = 10x<br>&rArr; x = 5</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "62",
                    section: "17",
                    question_en: "<p>62. In &Delta;ABC, &ang;B = 72&deg;<strong id=\"docs-internal-guid-da612ef0-7fff-2db6-7824-890ddcc1f81a\"> </strong>and &ang;C = 44&deg;. Side BC is produced to D. The bisectors of &ang;B and &ang;ACD meet at E. What is the measure of &ang;BEC ?</p>",
                    question_hi: "<p>62. त्रिभुज ABC में, &ang;B = 72&deg; तथा &ang;C = 44&deg; है | भुजा BC को D तक बढ़ाया जाता है | &ang;B तथा &ang;ACD के समद्विभाजक E पर मिलते हैं | कोण BEC का मान क्या होगा ?</p>",
                    options_en: ["<p>58<math display=\"inline\"><mo>&#176;</mo></math></p>", "<p>46<math display=\"inline\"><mo>&#176;</mo><mi>&#160;</mi><mi>&#160;</mi><mi>&#160;</mi></math></p>", 
                                "<p>32<math display=\"inline\"><mo>&#176;</mo><mi>&#160;</mi></math></p>", "<p>36<math display=\"inline\"><mo>&#176;</mo></math></p>"],
                    options_hi: ["<p>58<math display=\"inline\"><mo>&#176;</mo></math></p>", "<p>46<math display=\"inline\"><mo>&#176;</mo><mi>&#160;</mi><mi>&#160;</mi><mi>&#160;</mi></math></p>",
                                "<p>32<math display=\"inline\"><mo>&#176;</mo><mi>&#160;</mi></math></p>", "<p>36<math display=\"inline\"><mo>&#176;</mo></math></p>"],
                    solution_en: "<p>62.(c) <br>In &Delta;ABC, &ang;B = 72&deg; and &ang;C = 44&deg;&nbsp;<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744023374046.png\" alt=\"rId49\" width=\"168\" height=\"116\"><br>&ang;A = 64&deg;<br>Bisectors of &ang;B and &ang;ACD meet at E. Thus, &ang;BEC is half &ang;BAC<br>Thus, &ang;E = 32&deg;.</p>",
                    solution_hi: "<p>62.(c) <br>&Delta;ABC, &ang;B = 72&deg; तथा &ang;C= 44&deg;<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744023374046.png\" alt=\"rId49\" width=\"168\" height=\"116\"><br>&ang;A = 64&deg;<br>&ang;B और &ang;ACD के समद्विभाजक E पर मिलते हैं। इस प्रकार, &ang;BEC, &ang;BAC का आधा है।<br>इस प्रकार, &ang;E = 32&deg;.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "63",
                    section: "17",
                    question_en: "<p>63. In &Delta;ABC, AC = 8.4 cm, BC = 14 cm. P is a point on AB such that CP = 11.2 cm and &ang;ACP = &ang;B. What is the length (in cm) of BP ?&nbsp;</p>",
                    question_hi: "<p>63. &Delta;ABC में, AC = 8.4 सेमी, BC = 14 सेमी है | P, AB पर स्थित एक ऐसा बिंदु है कि CP = 11.2 सेमी तथा &ang;ACP = &ang;B है | BP की लंबाई (सेमी में) कितनी है ?</p>",
                    options_en: ["<p>4.12</p>", "<p>2.8</p>", 
                                "<p>3.78</p>", "<p>3.6</p>"],
                    options_hi: ["<p>4.12</p>", "<p>2.8</p>",
                                "<p>3.78</p>", "<p>3.6</p>"],
                    solution_en: "<p>63.(c) <br>In &Delta;ABC, &ang;ACP = &ang;B<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744023374269.png\" alt=\"rId50\" width=\"134\" height=\"98\"><br>In &Delta;ABC and &Delta;ACP:<br>&ang;ACP = &ang;B, <br>&ang;A and side AC is common<br>Hence, <math display=\"inline\"><mfrac><mrow><mi>A</mi><mi>B</mi></mrow><mrow><mi>A</mi><mi>C</mi></mrow></mfrac></math>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>B</mi><mi>C</mi></mrow><mrow><mi>P</mi><mi>C</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>A</mi><mi>C</mi></mrow><mrow><mi>A</mi><mi>P</mi></mrow></mfrac></math><br>&rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>B</mi><mi>C</mi></mrow><mrow><mi>P</mi><mi>C</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>A</mi><mi>C</mi></mrow><mrow><mi>A</mi><mi>P</mi></mrow></mfrac></math><br>&rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>14</mn><mrow><mn>11</mn><mo>.</mo><mn>2</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>8</mn><mo>.</mo><mn>4</mn></mrow><mrow><mi>A</mi><mi>P</mi></mrow></mfrac></math><br>&rArr; AP = 6.72<br>Similarly, in &Delta;ABC and &Delta;CBP.<br>AB &times; PC = AC &times; BC<br>AB &times; 11.2 = 8.4 &times; 14<br>AB = 10.5<br>BP = 10.5 - 6.72 = 3.78 cm</p>",
                    solution_hi: "<p>63.(c) <br>&Delta;ABC में, &ang;ACP = &ang;B<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744023374269.png\" alt=\"rId50\" width=\"134\" height=\"98\"><br>&Delta;ABC तथा &Delta;ACPमें,:<br>&ang;ACP = &ang;B, <br>&ang;A और भुजा AC उभयनिष्ठ है<br>अत:, <math display=\"inline\"><mfrac><mrow><mi>A</mi><mi>B</mi></mrow><mrow><mi>A</mi><mi>C</mi></mrow></mfrac></math>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>B</mi><mi>C</mi></mrow><mrow><mi>P</mi><mi>C</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>A</mi><mi>C</mi></mrow><mrow><mi>A</mi><mi>P</mi></mrow></mfrac></math><br>&rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>B</mi><mi>C</mi></mrow><mrow><mi>P</mi><mi>C</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>A</mi><mi>C</mi></mrow><mrow><mi>A</mi><mi>P</mi></mrow></mfrac></math><br>&rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>14</mn><mrow><mn>11</mn><mo>.</mo><mn>2</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>8</mn><mo>.</mo><mn>4</mn></mrow><mrow><mi>A</mi><mi>P</mi></mrow></mfrac></math><br>&rArr; AP = 6.72<br>इसी तरह, &Delta;ABC और &Delta;CBP.<br>AB &times; PC = AC &times; BC<br>AB &times; 11.2 = 8.4 &times; 14<br>AB = 10.5<br>BP = 10.5 - 6.72 = 3.78 cm</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "64",
                    section: "17",
                    question_en: "<p>64. If 2x<sup>2 </sup>+ y<sup>2 </sup>+ 8z<sup>2 </sup>- 2<math display=\"inline\"><msqrt><mn>2</mn></msqrt><mi>x</mi><mi>y</mi></math> + 4<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>2</mn></msqrt></math>yz - 8zx = (Ax + y + Bz)<sup>2</sup>, then the value of (A<sup>2 </sup>+ B<sup>2 </sup>- AB) is:</p>",
                    question_hi: "<p>64. यदि 2x<sup>2 </sup>+ y<sup>2 </sup>+ 8z<sup>2 </sup>- 2<math display=\"inline\"><msqrt><mn>2</mn></msqrt><mi>x</mi><mi>y</mi></math> + 4<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>2</mn></msqrt></math>yz - 8zx = (Ax + y + Bz)<sup>2</sup> है, तो (A<sup>2 </sup>+ B<sup>2 </sup>- AB) का मान होगा</p>",
                    options_en: ["<p>16</p>", "<p>14</p>", 
                                "<p>6</p>", "<p>18</p>"],
                    options_hi: ["<p>16 </p>", "<p>14</p>",
                                "<p>6</p>", "<p>18</p>"],
                    solution_en: "<p>64.(b) <br>2x<sup>2 </sup>+ y<sup>2 </sup>+ 8z<sup>2 </sup>- 2<math display=\"inline\"><msqrt><mn>2</mn></msqrt><mi>x</mi><mi>y</mi></math> + 4<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>2</mn></msqrt></math>yz - 8zx = (Ax + y + Bz)<sup>2</sup><br>&rArr; (-<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>2</mn></msqrt></math>x)<sup>2 </sup>+ y<sup>2 </sup>+ (2<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>2</mn></msqrt></math>z)<sup>2 </sup>- 2<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>2</mn></msqrt></math>xy + 4<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>2</mn></msqrt></math>yz - 8zx = (Ax + y + Bz)<sup>2</sup><br>&rArr; A = -<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>2</mn></msqrt></math> and B = 2<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>2</mn></msqrt></math><br>Then, A<sup>2</sup> + B<sup>2 </sup>- AB = 2 + 8 + 4 = 14</p>",
                    solution_hi: "<p>64.(b)<br>2x<sup>2 </sup>+ y<sup>2 </sup>+ 8z<sup>2 </sup>- 2<math display=\"inline\"><msqrt><mn>2</mn></msqrt><mi>x</mi><mi>y</mi></math> + 4<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>2</mn></msqrt></math>yz - 8zx = (Ax + y + Bz)<sup>2</sup><br>&rArr; (-<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>2</mn></msqrt></math>x)<sup>2 </sup>+ y<sup>2 </sup>+ (2<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>2</mn></msqrt></math>z)<sup>2 </sup>- 2<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>2</mn></msqrt></math>xy + 4<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>2</mn></msqrt></math>yz - 8zx = (Ax + y + Bz)<sup>2</sup><br>&rArr; A = -<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>2</mn></msqrt></math> और B = 2<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>2</mn></msqrt></math><br>तो, A<sup>2</sup> + B<sup>2 </sup>- AB = 2 + 8 + 4 = 14</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "65",
                    section: "17",
                    question_en: "<p>65. How many numbers are there from 200 to 800 which are neither divisible by 5 nor by 7 ?</p>",
                    question_hi: "<p>65. 200 से 800 तक ऐसी कितनी संख्याएँ हैं जो ना तो 5 से ना ही 7 से विभाज्य हैं ?</p>",
                    options_en: ["<p>407</p>", "<p>410</p>", 
                                "<p>413</p>", "<p>411</p>"],
                    options_hi: ["<p>407</p>", "<p>410</p>",
                                "<p>413</p>", "<p>411</p>"],
                    solution_en: "<p>65.(d) <br>From 200 to 800, numbers which are divisible by <br>(i) 5 : <math display=\"inline\"><mfrac><mrow><mn>800</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>200</mn><mn>5</mn></mfrac></math> = 120 + 1<br>(ii) 7 : <math display=\"inline\"><mfrac><mrow><mn>800</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>200</mn><mn>7</mn></mfrac></math> = 114 - 28 = 86<br>(iii) 35 : <math display=\"inline\"><mfrac><mrow><mn>800</mn></mrow><mrow><mn>35</mn></mrow></mfrac></math> - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>200</mn><mn>35</mn></mfrac></math> = 22 - 5 = 17<br>Total number = 121 + 86 - 17 = 190<br>Numbers from 200 to 800 which are neither divisible by 5 nor by 7<br>= (800 - 199) - 190 = 411</p>",
                    solution_hi: "<p>65.(d) 200 से 800 तक, संख्याएँ जो से विभाज्य हैं<br>(i) 5 : <math display=\"inline\"><mfrac><mrow><mn>800</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>200</mn><mn>5</mn></mfrac></math> = 120 + 1<br>(ii) 7 : <math display=\"inline\"><mfrac><mrow><mn>800</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>200</mn><mn>7</mn></mfrac></math> = 114 - 28 = 86<br>(iii) 35 : <math display=\"inline\"><mfrac><mrow><mn>800</mn></mrow><mrow><mn>35</mn></mrow></mfrac></math> - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>200</mn><mn>35</mn></mfrac></math> = 22 - 5 = 17<br>कुल संख्याएँ = 121 + 86 - 17 = 190<br>200 से 800 तक की संख्याएँ जो न तो 5 से और न ही 7 से विभाज्य हैं = (800 - 199) - 190 = 411</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "66",
                    section: "17",
                    question_en: "<p>66. The value of <math display=\"inline\"><mfrac><mrow><mn>8</mn><mo>&#247;</mo><mo>[</mo><mo>(</mo><mn>8</mn><mo>-</mo><mn>3</mn><mo>)</mo><mo>&#247;</mo><mo>{</mo><mo>(</mo><mn>4</mn><mo>&#247;</mo><mn>4</mn><mi>&#160;</mi><mi>o</mi><mi>f</mi><mi>&#160;</mi><mn>8</mn><mo>)</mo><mo>+</mo><mn>4</mn><mo>-</mo><mn>4</mn><mo>&#215;</mo><mn>4</mn><mo>&#247;</mo><mn>8</mn><mo>}</mo><mo>-</mo><mn>2</mn><mo>]</mo></mrow><mrow><mn>8</mn><mo>&#215;</mo><mn>8</mn><mo>&#247;</mo><mn>4</mn><mo>-</mo><mn>8</mn><mo>&#247;</mo><mn>8</mn><mi>&#160;</mi><mi>o</mi><mi>f</mi><mi>&#160;</mi><mn>2</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>7</mn></mrow></mfrac></math> is:</p>",
                    question_hi: "<p>66. <math display=\"inline\"><mfrac><mrow><mn>8</mn><mo>&#247;</mo><mo>[</mo><mo>(</mo><mn>8</mn><mo>-</mo><mn>3</mn><mo>)</mo><mo>&#247;</mo><mo>{</mo><mo>(</mo><mn>4</mn><mo>&#247;</mo><mn>4</mn><mi>&#160;</mi><mi>o</mi><mi>f</mi><mi>&#160;</mi><mn>8</mn><mo>)</mo><mo>+</mo><mn>4</mn><mo>-</mo><mn>4</mn><mo>&#215;</mo><mn>4</mn><mo>&#247;</mo><mn>8</mn><mo>}</mo><mo>-</mo><mn>2</mn><mo>]</mo></mrow><mrow><mn>8</mn><mo>&#215;</mo><mn>8</mn><mo>&#247;</mo><mn>4</mn><mo>-</mo><mn>8</mn><mo>&#247;</mo><mn>8</mn><mi>&#160;</mi><mi>o</mi><mi>f</mi><mi>&#160;</mi><mn>2</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>7</mn></mrow></mfrac></math> का मान है :</p>",
                    options_en: ["<p><math display=\"inline\"><mi>&#160;</mi><mfrac><mrow><mn>17</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mi>&#160;</mi><mfrac><mrow><mi>&#160;</mi><mn>8</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>", 
                                "<p><math display=\"inline\"><mi>&#160;</mi><mfrac><mrow><mn>16</mn></mrow><mrow><mn>170</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mi>&#160;</mi><mfrac><mrow><mn>2</mn></mrow><mrow><mn>17</mn></mrow></mfrac></math></p>"],
                    options_hi: ["<p><math display=\"inline\"><mfrac><mrow><mn>17</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>8</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>",
                                "<p><math display=\"inline\"><mfrac><mrow><mn>16</mn></mrow><mrow><mn>170</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>17</mn></mrow></mfrac></math></p>"],
                    solution_en: "<p>66.(b) <br>On applying BODMAS rule <br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>8</mn><mo>&#247;</mo><mo>[</mo><mo>(</mo><mn>8</mn><mo>-</mo><mn>3</mn><mo>)</mo><mo>&#247;</mo><mo>{</mo><mo>(</mo><mn>4</mn><mo>&#247;</mo><mn>4</mn><mi>&#160;</mi><mi>o</mi><mi>f</mi><mi>&#160;</mi><mn>8</mn><mo>)</mo><mo>+</mo><mn>4</mn><mo>-</mo><mn>4</mn><mo>&#215;</mo><mn>4</mn><mo>&#247;</mo><mn>8</mn><mo>}</mo><mo>-</mo><mn>2</mn><mo>]</mo></mrow><mrow><mn>8</mn><mo>&#215;</mo><mn>8</mn><mo>&#247;</mo><mn>4</mn><mo>-</mo><mn>8</mn><mo>&#247;</mo><mn>8</mn><mi>&#160;</mi><mi>o</mi><mi>f</mi><mi>&#160;</mi><mn>2</mn><mo>-</mo><mn>7</mn></mrow></mfrac></math><br>&rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>8</mn><mo>&#247;</mo><mo>[</mo><mo>(</mo><mn>5</mn><mo>)</mo><mo>&#247;</mo><mo>{</mo><mo>(</mo><mfrac><mn>1</mn><mn>8</mn></mfrac><mo>)</mo><mo>+</mo><mn>4</mn><mo>-</mo><mn>2</mn><mo>}</mo><mo>-</mo><mn>2</mn><mo>]</mo></mrow><mrow><mn>8</mn><mo>&#215;</mo><mn>2</mn><mo>-</mo><mfrac><mn>1</mn><mn>2</mn></mfrac><mo>-</mo><mn>7</mn></mrow></mfrac></math><br>&rArr; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>8</mn><mo>&#247;</mo><mo>[</mo><mn>5</mn><mo>&#247;</mo><mfrac><mn>17</mn><mn>8</mn></mfrac><mo>-</mo><mn>2</mn><mo>]</mo></mrow><mrow><mn>8</mn><mo>&#215;</mo><mn>2</mn><mo>-</mo><mfrac><mn>1</mn><mn>2</mn></mfrac><mo>-</mo><mn>7</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>8</mn><mn>3</mn></mfrac></math></p>",
                    solution_hi: "<p>66.(b) <strong id=\"docs-internal-guid-8c34c162-7fff-9bdc-c3d4-8302b89cf720\">BODMAS </strong>नियम को लागू करने पर&nbsp;<br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>8</mn><mo>&#247;</mo><mo>[</mo><mo>(</mo><mn>8</mn><mo>-</mo><mn>3</mn><mo>)</mo><mo>&#247;</mo><mo>{</mo><mo>(</mo><mn>4</mn><mo>&#247;</mo><mn>4</mn><mi>&#160;</mi><mi>o</mi><mi>f</mi><mi>&#160;</mi><mn>8</mn><mo>)</mo><mo>+</mo><mn>4</mn><mo>-</mo><mn>4</mn><mo>&#215;</mo><mn>4</mn><mo>&#247;</mo><mn>8</mn><mo>}</mo><mo>-</mo><mn>2</mn><mo>]</mo></mrow><mrow><mn>8</mn><mo>&#215;</mo><mn>8</mn><mo>&#247;</mo><mn>4</mn><mo>-</mo><mn>8</mn><mo>&#247;</mo><mn>8</mn><mi>&#160;</mi><mi>o</mi><mi>f</mi><mi>&#160;</mi><mn>2</mn><mo>-</mo><mn>7</mn></mrow></mfrac></math><br>&rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>8</mn><mo>&#247;</mo><mo>[</mo><mo>(</mo><mn>5</mn><mo>)</mo><mo>&#247;</mo><mo>{</mo><mo>(</mo><mfrac><mn>1</mn><mn>8</mn></mfrac><mo>)</mo><mo>+</mo><mn>4</mn><mo>-</mo><mn>2</mn><mo>}</mo><mo>-</mo><mn>2</mn><mo>]</mo></mrow><mrow><mn>8</mn><mo>&#215;</mo><mn>2</mn><mo>-</mo><mfrac><mn>1</mn><mn>2</mn></mfrac><mo>-</mo><mn>7</mn></mrow></mfrac></math><br>&rArr; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>8</mn><mo>&#247;</mo><mo>[</mo><mn>5</mn><mo>&#247;</mo><mfrac><mn>17</mn><mn>8</mn></mfrac><mo>-</mo><mn>2</mn><mo>]</mo></mrow><mrow><mn>8</mn><mo>&#215;</mo><mn>2</mn><mo>-</mo><mfrac><mn>1</mn><mn>2</mn></mfrac><mo>-</mo><mn>7</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>8</mn><mn>3</mn></mfrac></math></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "67",
                    section: "17",
                    question_en: "<p>67. The given table represents the production of different types of motorcycles (in thousands) over a period of six years. Study the table carefully and answer the questions that follows:<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744023372101.png\" alt=\"rId46\" width=\"360\" height=\"207\"> <br>What is the percentage increase in the total production of all types of motorcycles from 2014to 2018 ?</p>",
                    question_hi: "<p>67. दी गयी तालिका छः वर्षों की अवधि के दौरान अलग-अलग प्रकार की मोटर साइकिलों के उत्पादन (हज़ार में) को दर्शाती है | इस तालिका का ध्यानपूर्वक अध्ययन करें तथा फिर पूछे गए प्रश्नों का उत्तर दीजिए |&nbsp;<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744023373424.png\" alt=\"rId47\" width=\"377\" height=\"222\"> <br>2014 से 2018 तक सभी प्रकार की मोटरसाइकिल के कुल उत्पादन में कितने प्रतिशत की वृद्धि हुई है ?</p>",
                    options_en: ["<p>14<math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math></p>", "<p>17<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>", 
                                "<p>16<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>", "<p>14<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math></p>"],
                    options_hi: ["<p>14<math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math></p>", "<p>17<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>",
                                "<p>16<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>", "<p>14<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math></p>"],
                    solution_en: "<p>67.(c) <br>Total production of motorcycles in 2014 = 84 + 87 + 89 + 100 = 360<br>Total production of motorcycles in 2018 = 98 + 92 + 110 + 120 = 420<br>Required % = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>60</mn><mn>360</mn></mfrac></math> &times; 100 = 16<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math>%</p>",
                    solution_hi: "<p>67.(c) 2014 में मोटरसाइकिलों का कुल उत्पादन = 84 + 87 + 89 + 100 = 360<br>2018 में मोटरसाइकिलों का कुल उत्पादन = 98 + 92 + 110 + 120 = 420<br>आवश्यक % = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>60</mn><mn>360</mn></mfrac></math> &times; 100 = 16<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math>%</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "68",
                    section: "17",
                    question_en: "<p>68. The given table represents the production of different types of motorcycles (in thousands) over a period of six years. Study the table carefully and answer the questions that follows:<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744023372101.png\" alt=\"rId46\" width=\"366\" height=\"211\"> <br>By what percentage is the total production of type A motorcycles over six years, less than the total production of all types of motorcycles in 2013 and 2016 ?</p>",
                    question_hi: "<p>68. दी गयी तालिका छः वर्षों की अवधि के दौरान अलग-अलग प्रकार की मोटर साइकिलों के उत्पादन ( हज़ार में ) को दर्शाती है | इस तालिका का ध्यानपूर्वक अध्ययन करें तथा फिर पूछे गए प्रश्नों का उत्तर दीजिए | <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744023373424.png\" alt=\"rId47\" width=\"363\" height=\"214\"> <br>इन छः वर्षों में A प्रकार की मोटरसाइकिल का कुल उत्पादन 2013 तथा 2016 में सभी प्रकार की मोटर साइकिलों के कुल उत्पादन से कितना प्रतिशत कम रहा है ?</p>",
                    options_en: ["<p>32.2</p>", "<p>32.8</p>", 
                                "<p>31.6</p>", "<p>30.5</p>"],
                    options_hi: ["<p>32.2</p>", "<p>32.8</p>",
                                "<p>31.6</p>", "<p>30.5</p>"],
                    solution_en: "<p>68.(d) <br>Total production of type A motorcycles over six years = 95 + 84 + 85 + 89 + 80 + 98 = 531<br>Total production of all motorcycles in 2013 and 2016 = (95 + 98 + 104 + 103) + (89 + 88 + 92 + 95) = 764<br>Required % =<math display=\"inline\"><mi>&#160;</mi><mfrac><mrow><mn>764</mn><mo>-</mo><mn>531</mn></mrow><mrow><mn>764</mn></mrow></mfrac></math> &times; 100&nbsp;<br>=<math display=\"inline\"><mi>&#160;</mi><mfrac><mrow><mn>233</mn></mrow><mrow><mn>764</mn></mrow></mfrac></math> &times; 100 = 30.5%</p>",
                    solution_hi: "<p>68.(d) छह वर्षों में टाइप A मोटरसाइकिल का कुल उत्पादन = 95 + 84 + 85 + 89 + 80 + 98 = 531<br>2013 और 2016 में सभी मोटरसाइकिलों का कुल उत्पादन<br>= (95 + 98 + 104 + 103) + (89 + 88 + 92 + 95) = 764<br>आवश्यक % =<math display=\"inline\"><mi>&#160;</mi><mfrac><mrow><mn>764</mn><mo>-</mo><mn>531</mn></mrow><mrow><mn>764</mn></mrow></mfrac></math> &times; 100&nbsp;<br>=<math display=\"inline\"><mi>&#160;</mi><mfrac><mrow><mn>233</mn></mrow><mrow><mn>764</mn></mrow></mfrac></math> &times; 100 = 30.5%</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "69",
                    section: "17",
                    question_en: "<p>69. Two circles of radii 7cm and 5 cm intersect each other at A and B, the distance between their centres is 10 cm. The length (in cm) of the common chord AB is:</p>",
                    question_hi: "<p>69. दो वृत्त, जिनकी त्रिज्याएँ 7 सेमी तथा 5 सेमी हैं, एक-दूसरे को A और B पर प्रतिच्छेद करते हैं | उनके केंद्रों के बीच की दूरी 10 सेमी है | उभयनिष्ठ जीवा AB की लंबाई (सेमी में) कितनी है ?</p>",
                    options_en: ["<p><math display=\"inline\"><mfrac><mrow><mn>3</mn><msqrt><mn>66</mn></msqrt></mrow><mrow><mn>5</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>4</mn><msqrt><mn>66</mn></msqrt></mrow><mrow><mn>5</mn></mrow></mfrac><mi>&#160;</mi></math></p>", 
                                "<p><math display=\"inline\"><mfrac><mrow><mn>2</mn><msqrt><mn>74</mn></msqrt></mrow><mrow><mn>5</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>3</mn><msqrt><mn>74</mn></msqrt></mrow><mrow><mn>5</mn></mrow></mfrac></math></p>"],
                    options_hi: ["<p><math display=\"inline\"><mfrac><mrow><mn>3</mn><msqrt><mn>66</mn></msqrt></mrow><mrow><mn>5</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>4</mn><msqrt><mn>66</mn></msqrt></mrow><mrow><mn>5</mn></mrow></mfrac></math></p>",
                                "<p><math display=\"inline\"><mfrac><mrow><mn>2</mn><msqrt><mn>74</mn></msqrt></mrow><mrow><mn>5</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>3</mn><msqrt><mn>74</mn></msqrt></mrow><mrow><mn>5</mn></mrow></mfrac></math></p>"],
                    solution_en: "<p>69.(b) <br>As the radius of two circles be 7 cm and 5 cm and distance between their centres is 10 cm. Let AB and centre of two circles(c<sub>1</sub> and c<sub>2</sub>) intersect of O. AB = 2x and and c<sub>1</sub>O = a then c<sub>2</sub>O = (10-a).&nbsp;<br>AO is common in &Delta;AOc<sub>1 </sub>and &Delta;AOc<sub>2</sub>. Applying pythagoras theorem in both triangles, AO&nbsp;<br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mn>7</mn><mn>2</mn></msup><mo>-</mo><msup><mi>a</mi><mn>2</mn></msup></msqrt></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><msqrt><mrow><msup><mn>5</mn><mn>2</mn></msup><mo>-</mo><msup><mrow><mo>(</mo><mn>10</mn><mo>-</mo><mi>a</mi><mo>)</mo></mrow><mn>2</mn></msup></mrow></msqrt></mstyle></math><br>a = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>31</mn><mn>5</mn></mfrac></math><br>AO = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mn>7</mn><mn>2</mn></msup><mo>-</mo><msup><mfrac><mn>31</mn><mn>5</mn></mfrac><mn>2</mn></msup></msqrt></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><msqrt><mn>66</mn></msqrt></mrow><mn>5</mn></mfrac></math><br>AB = <math display=\"inline\"><mfrac><mrow><mn>4</mn><msqrt><mn>66</mn></msqrt></mrow><mrow><mn>5</mn></mrow></mfrac></math></p>",
                    solution_hi: "<p>69.(b) <br>चूंकि दो वृत्तों की त्रिज्या 7 सेमी और 5 सेमी है और उनके केंद्रों के बीच की दूरी 10 सेमी है। माना AB और दो वृत्तों का केंद्र (c<sub>1</sub> और c<sub>2</sub>) O को प्रतिच्छेद करता है। AB = 2x और c<sub>1</sub>O = a फिर c<sub>2</sub>O = (10 - a).&nbsp;<br>AO &Delta;AOc<sub>1 </sub>तथा &Delta;AOc<sub>2 </sub>में उभयनिष्ठ है&nbsp;<br>पाइथागोरस प्रमेय को दोनों त्रिभुजों में लागू करने पर,<br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mn>7</mn><mn>2</mn></msup><mo>-</mo><msup><mi>a</mi><mn>2</mn></msup></msqrt></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><msqrt><mrow><msup><mn>5</mn><mn>2</mn></msup><mo>-</mo><msup><mrow><mo>(</mo><mn>10</mn><mo>-</mo><mi>a</mi><mo>)</mo></mrow><mn>2</mn></msup></mrow></msqrt></mstyle></math><br>a = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>31</mn><mn>5</mn></mfrac></math><br>AO = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mn>7</mn><mn>2</mn></msup><mo>-</mo><msup><mfrac><mn>31</mn><mn>5</mn></mfrac><mn>2</mn></msup></msqrt></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><msqrt><mn>66</mn></msqrt></mrow><mn>5</mn></mfrac></math><br>AB = <math display=\"inline\"><mfrac><mrow><mn>4</mn><msqrt><mn>66</mn></msqrt></mrow><mrow><mn>5</mn></mrow></mfrac></math></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "70",
                    section: "17",
                    question_en: "<p>70. If the selling price of an article is 8% more than the cost price and the discount offered is 10% on the marked price of the article, then what is the ratio of cost price to the marked price ?</p>",
                    question_hi: "<p>70. यदि एक वस्तु का विक्रय मूल्य क्रय मूल्य से 8% अधिक है तथा वस्तु के अंकित मूल्य पर 10% की छूट दी जाती है, तो क्रय मूल्य तथा अंकित मूल्य के बीच क्या अनुपात है ?</p>",
                    options_en: ["<p>5 : 6</p>", "<p>8 : 9</p>", 
                                "<p>3 : 4</p>", "<p>4 : 5</p>"],
                    options_hi: ["<p>5 : 6</p>", "<p>8 : 9</p>",
                                "<p>3 : 4</p>", "<p>4 : 5</p>"],
                    solution_en: "<p>70.(a) <br>According to question:<br>SP = 108% of CP = 90% of MP<br>Thus, <math display=\"inline\"><mfrac><mrow><mi>C</mi><mi>P</mi></mrow><mrow><mi>M</mi><mi>P</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>90</mn><mn>108</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>6</mn></mfrac></math></p>",
                    solution_hi: "<p>70.(a)<br>प्रश्न के अनुसार:<br>विक्रय मूल्य = क्रय मूल्य का 108% = 90% का अंकित मूल्य<br>इस प्रकार, (<math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2354;&#2366;&#2327;&#2340;</mi><mo>&#160;</mo><mi>&#2350;&#2370;&#2354;&#2381;&#2351;</mi></mrow><mrow><mi>&#2309;&#2306;&#2325;&#2367;&#2340;</mi><mo>&#160;</mo><mi>&#2350;&#2370;&#2354;&#2381;&#2351;</mi></mrow></mfrac></math>)&nbsp;= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>90</mn><mn>108</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>6</mn></mfrac></math></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "71",
                    section: "17",
                    question_en: "<p>71. The given table represents the production of different types of motorcycles (in thousands) over a period of six years. Study the table carefully and answer the questions that follows:<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744023372101.png\" alt=\"rId46\" width=\"360\" height=\"207\"> <br>What is the ratio of the total number of motorcycles of type B produced in 2016 and 2018 to the total number of motorcycles of type D produced in 2013, 2015 and 2016 ?</p>",
                    question_hi: "<p>71. दी गयी तालिका छः वर्षों की अवधि के दौरान अलग-अलग प्रकार की मोटर साइकिलों के उत्पादन (हज़ार में) को दर्शाती है | इस तालिका का ध्यानपूर्वक अध्ययन करें तथा फिर पूछे गए प्रश्नों का उत्तर दीजिए |&nbsp;<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744023373424.png\" alt=\"rId47\" width=\"365\" height=\"215\"> <br>2016 तथा 2018 में उत्पादित की गयी B प्रकार की मोटरसाइकिलों की कुल संख्या का 2013, 2015 और 2016 में उत्पादित D प्रकार की मोटरसाइकिलों की कुल संख्या के साथ अनुपात ज्ञात कीजिए |</p>",
                    options_en: ["<p>1 : 2</p>", "<p>3 : 4</p>", 
                                "<p>3 : 5</p>", "<p>2 : 3</p>"],
                    options_hi: ["<p>1 : 2</p>", "<p>3 : 4</p>",
                                "<p>3 : 5</p>", "<p>2 : 3</p>"],
                    solution_en: "<p>71.(c) <br>Total number of motorcycle of type B in 2016 and 2018 = 88 + 92 = 180<br>Type D motorcycles produced in 2013, 2015 and 2016 = 103 + 102 + 95 = 300&nbsp;<br>Required ratio = 180 : 300 = 3 : 5</p>",
                    solution_hi: "<p>71.(c) 2016 और 2018 में B प्रकार की मोटरसाइकिलों की कुल संख्या = 88 + 92 = 180<br>2013, 2015 और 2016 में उत्पादित टाइप D मोटरसाइकिलें = 103 + 102 + 95 = 300&nbsp;<br>आवश्यक अनुपात = 180 : 300 = 3 : 5</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "72",
                    section: "17",
                    question_en: "<p>72. A sum of ₹ 8,000 invested at 10% pa amounts to ₹ 9,261 in a certain time, interest compounded half-yearly. What will be the compound interest (in₹) on the same sum for the same time at double the earlier rate of interest, when interest is compounded annually ?</p>",
                    question_hi: "<p>72. 10% प्रति वर्ष की दर से निवेश की गयी 8,000 रुपये की एक राशि अर्धवार्षिक चक्रवृद्धि ब्याज की दर से किसी निश्चित समय में 9,261 रुपये हो जाती है | इसी राशि पर इतने ही समय के लिए पूर्व की तुलना में दोगुने दर से चक्रवृद्धि ब्याज ज्ञात कीजिए, यदि ब्याज का संयोजन वार्षिक है |</p>",
                    options_en: ["<p>₹ 2,520</p>", "<p>₹ 2,480</p>", 
                                "<p>₹ 2,560</p>", "<p>₹ 2,500</p>"],
                    options_hi: ["<p>₹ 2,520</p>", "<p>₹ 2,480</p>",
                                "<p>₹ 2,560</p>", "<p>₹ 2,500</p>"],
                    solution_en: "<p>72.(c) <br>9261 = 8000(1 + <math display=\"inline\"><mfrac><mrow><mn>10</mn></mrow><mrow><mn>2</mn><mo>&#215;</mo><mn>100</mn></mrow></mfrac></math>)<sup>2t</sup><br>&rArr; t = 1.5 years<br>Now at 20% rate, CI for 1.5 years :<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744023375206.png\" alt=\"rId52\" width=\"110\" height=\"125\"><br>Total compound interest = 1600 + 960&nbsp;<br>= 2560</p>",
                    solution_hi: "<p>72.(c) <br>9261 = 8000(1 + <math display=\"inline\"><mfrac><mrow><mn>10</mn></mrow><mrow><mn>2</mn><mo>&#215;</mo><mn>100</mn></mrow></mfrac></math>)<sup>2t</sup><br>&rArr; t = 1.5 वर्षो<br>20% दर पर , 1.5 वर्षो का चक्रवृद्धि ब्याज :<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744023375206.png\" alt=\"rId52\" width=\"110\" height=\"125\"><br>कुल चक्रवृद्धि ब्याज = 1600 + 960 = 2560</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "73",
                    section: "17",
                    question_en: "<p>73. A sells an article to B at a loss of 20%. B sells it to C at a profit of 12.5% and C sells it to D at a loss of 8%. If D buys it for ₹248.40, then what is the difference between the loss incurred by A and C ?</p>",
                    question_hi: "<p>73. A ने एक वस्तु B को 20% की हानि पर बेची | B ने उस वस्तु को C को 12.5% लाभ पर बेच दिया तथा C ने वह वस्तु D को 8% की हानि पर बेची | यदि D ने उसे 248.40 रुपये में ख़रीदा, तो A और C को हुई हानि के बीच क्या अंतर है ?</p>",
                    options_en: ["<p>₹36.80</p>", "<p>₹38.40</p>", 
                                "<p>₹42.60</p>", "<p>₹39.20</p>"],
                    options_hi: ["<p>₹36.80</p>", "<p>₹38.40</p>",
                                "<p>₹42.60</p>", "<p>₹39.20</p>"],
                    solution_en: "<p>73.(b) <br>Let cost price of A = 100 units<br>According to question:<br>At 20% loss, CP for B = 80 units <br>At 12.5% profit, CP for C = 90 unit<br>At 8% loss, CP for D = 82.80 unit<br>82.80 units = ₹ 248.40<br>1 units = ₹ 3<br>Now, loss incurred by A = 20 units; and loss incurred by C = 7.20 units<br>Required difference = 12.80 units = ₹38.40</p>",
                    solution_hi: "<p>73.(b) <br>माना A का क्रय मूल्य = 100 इकाई<br>प्रश्न के अनुसार:<br>20% हानि पर, B के लिए क्रय मूल्य = 80 इकाई<br>12.5% ​​लाभ पर, C के लिए क्रय मूल्य = 90 इकाई<br>8% हानि पर, D के लिए क्रय मूल्य = 82.80 इकाई<br>82.80 यूनिट = ₹ 248.40<br>1 इकाई = ₹ 3<br>अब, A द्वारा हुई हानि = 20 इकाई; और C द्वारा हुई हानि = 7.20 इकाई<br>अभीष्ट अंतर = 12.80 इकाई = ₹38.40</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "74",
                    section: "17",
                    question_en: "<p>74. If 7cos<sup>2</sup>&theta; + 3sin<sup>2</sup>&theta; = 6, 0&deg; &lt; &theta; &lt; 90&deg;, then the value of <math display=\"inline\"><mfrac><mrow><msup><mrow><mi>c</mi><mi>o</mi><mi>t</mi></mrow><mrow><mn>2</mn></mrow></msup><mn>2</mn><mi>&#952;</mi><mo>+</mo><msup><mrow><mi>s</mi><mi>e</mi><mi>c</mi></mrow><mrow><mn>2</mn></mrow></msup><mn>2</mn><mi>&#952;</mi></mrow><mrow><msup><mrow><mi>t</mi><mi>a</mi><mi>n</mi></mrow><mrow><mn>2</mn></mrow></msup><mn>2</mn><mi>&#952;</mi><mo>-</mo><msup><mrow><mi>s</mi><mi>i</mi><mi>n</mi></mrow><mrow><mn>2</mn></mrow></msup><mn>2</mn><mi>&#952;</mi></mrow></mfrac></math> is:</p>",
                    question_hi: "<p>74. यदि 7cos<sup>2</sup>&theta; + 3sin<sup>2</sup>&theta; = 6, 0&deg; &lt; &theta; &lt; 90&deg; है, तो <math display=\"inline\"><mfrac><mrow><msup><mrow><mi>c</mi><mi>o</mi><mi>t</mi></mrow><mrow><mn>2</mn></mrow></msup><mn>2</mn><mi>&#952;</mi><mo>+</mo><msup><mrow><mi>s</mi><mi>e</mi><mi>c</mi></mrow><mrow><mn>2</mn></mrow></msup><mn>2</mn><mi>&#952;</mi></mrow><mrow><msup><mrow><mi>t</mi><mi>a</mi><mi>n</mi></mrow><mrow><mn>2</mn></mrow></msup><mn>2</mn><mi>&#952;</mi><mo>-</mo><msup><mrow><mi>s</mi><mi>i</mi><mi>n</mi></mrow><mrow><mn>2</mn></mrow></msup><mn>2</mn><mi>&#952;</mi></mrow></mfrac></math> का मान ज्ञात कीजिए |</p>",
                    options_en: ["<p><math display=\"inline\"><mfrac><mrow><mn>52</mn></mrow><mrow><mn>27</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>26</mn></mrow><mrow><mn>15</mn></mrow></mfrac></math></p>", 
                                "<p><math display=\"inline\"><mfrac><mrow><mn>28</mn></mrow><mrow><mn>27</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>49</mn></mrow><mrow><mn>45</mn></mrow></mfrac></math></p>"],
                    options_hi: ["<p><math display=\"inline\"><mfrac><mrow><mn>52</mn></mrow><mrow><mn>27</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>26</mn></mrow><mrow><mn>15</mn></mrow></mfrac></math></p>",
                                "<p><math display=\"inline\"><mfrac><mrow><mn>28</mn></mrow><mrow><mn>27</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>49</mn></mrow><mrow><mn>45</mn></mrow></mfrac></math></p>"],
                    solution_en: "<p>74.(a) <br>7cos<sup>2</sup>&theta; + 3sin<sup>2 </sup>&theta; = 6, 0&deg; &lt; &theta; &lt; 90&deg;<br>sin<sup>2</sup>&theta; = 1 - cos<sup>2</sup>&theta;<br>4cos<sup>2</sup>&theta; = 3<br>Cos&theta; = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msqrt><mn>3</mn></msqrt><mn>2</mn></mfrac></math> &rArr; &theta; = 30&deg;<br><math display=\"inline\"><mfrac><mrow><msup><mrow><mi>c</mi><mi>o</mi><mi>t</mi></mrow><mrow><mn>2</mn></mrow></msup><mn>2</mn><mi>&#952;</mi><mo>+</mo><msup><mrow><mi>s</mi><mi>e</mi><mi>c</mi></mrow><mrow><mn>2</mn></mrow></msup><mn>2</mn><mi>&#952;</mi></mrow><mrow><msup><mrow><mi>t</mi><mi>a</mi><mi>n</mi></mrow><mrow><mn>2</mn></mrow></msup><mn>2</mn><mi>&#952;</mi><mo>-</mo><msup><mrow><mi>s</mi><mi>i</mi><mi>n</mi></mrow><mrow><mn>2</mn></mrow></msup><mn>2</mn><mi>&#952;</mi></mrow></mfrac><mi>&#160;</mi></math>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>c</mi><mi>o</mi><msup><mi>t</mi><mn>2</mn></msup><mn>60</mn><mo>+</mo><mi>s</mi><mi>e</mi><msup><mi>c</mi><mn>2</mn></msup><mn>60</mn></mrow><mrow><msup><mi>tan</mi><mn>2</mn></msup><mn>60</mn><mo>-</mo><msup><mi>sin</mi><mn>2</mn></msup><mn>60</mn></mrow></mfrac></math><br>= <math display=\"inline\"><mfrac><mrow><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac><mo>+</mo><mn>4</mn></mrow><mrow><mn>3</mn><mo>-</mo><mfrac><mrow><mn>3</mn></mrow><mrow><mn>4</mn></mrow></mfrac></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>52</mn><mn>27</mn></mfrac></math></p>",
                    solution_hi: "<p>74.(a) <br>7cos<sup>2</sup>&theta; + 3sin<sup>2 </sup>&theta; = 6, 0&deg; &lt; &theta; &lt; 90&deg;<br>sin<sup>2</sup>&theta; = 1 - cos<sup>2</sup>&theta;<br>4cos<sup>2</sup>&theta; = 3<br>Cos&theta; = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msqrt><mn>3</mn></msqrt><mn>2</mn></mfrac></math> &rArr; &theta; = 30&deg;<br><math display=\"inline\"><mfrac><mrow><msup><mrow><mi>c</mi><mi>o</mi><mi>t</mi></mrow><mrow><mn>2</mn></mrow></msup><mn>2</mn><mi>&#952;</mi><mo>+</mo><msup><mrow><mi>s</mi><mi>e</mi><mi>c</mi></mrow><mrow><mn>2</mn></mrow></msup><mn>2</mn><mi>&#952;</mi></mrow><mrow><msup><mrow><mi>t</mi><mi>a</mi><mi>n</mi></mrow><mrow><mn>2</mn></mrow></msup><mn>2</mn><mi>&#952;</mi><mo>-</mo><msup><mrow><mi>s</mi><mi>i</mi><mi>n</mi></mrow><mrow><mn>2</mn></mrow></msup><mn>2</mn><mi>&#952;</mi></mrow></mfrac><mi>&#160;</mi></math>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>c</mi><mi>o</mi><msup><mi>t</mi><mn>2</mn></msup><mn>60</mn><mo>+</mo><mi>s</mi><mi>e</mi><msup><mi>c</mi><mn>2</mn></msup><mn>60</mn></mrow><mrow><msup><mi>tan</mi><mn>2</mn></msup><mn>60</mn><mo>-</mo><msup><mi>sin</mi><mn>2</mn></msup><mn>60</mn></mrow></mfrac></math><br>= <math display=\"inline\"><mfrac><mrow><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac><mo>+</mo><mn>4</mn></mrow><mrow><mn>3</mn><mo>-</mo><mfrac><mrow><mn>3</mn></mrow><mrow><mn>4</mn></mrow></mfrac></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>52</mn><mn>27</mn></mfrac></math></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "75",
                    section: "7",
                    question_en: "<p>75. The value of <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#160;</mi><mn>9</mn><mo>&#176;</mo><mo>+</mo><mi>&#160;</mi><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#160;</mi><mn>81</mn><mo>&#176;</mo><mo>)</mo><mo>(</mo><mi>s</mi><mi>e</mi><mi>c</mi><mi>&#160;</mi><mn>9</mn><mo>&#176;</mo><mo>+</mo><mo>&#160;</mo><mi>c</mi><mi>o</mi><mi>s</mi><mi>e</mi><mi>c</mi><mo>&#160;</mo><mn>81</mn><mo>&#176;</mo><mo>)</mo></mrow><mrow><mn>2</mn><msup><mrow><mi>s</mi><mi>i</mi><mi>n</mi></mrow><mn>2</mn></msup><mo>&#160;</mo><mn>63</mn><mo>&#176;</mo><mo>+</mo><mn>1</mn><mo>+</mo><mn>2</mn><msup><mrow><mi>s</mi><mi>i</mi><mi>n</mi></mrow><mn>2</mn></msup><mo>&#160;</mo><mn>27</mn><mo>&#176;</mo></mrow></mfrac></math> is:</p>",
                    question_hi: "<p>75. <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#160;</mi><mn>9</mn><mo>&#176;</mo><mo>+</mo><mi>&#160;</mi><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#160;</mi><mn>81</mn><mo>&#176;</mo><mo>)</mo><mo>(</mo><mi>s</mi><mi>e</mi><mi>c</mi><mi>&#160;</mi><mn>9</mn><mo>&#176;</mo><mo>+</mo><mo>&#160;</mo><mi>c</mi><mi>o</mi><mi>s</mi><mi>e</mi><mi>c</mi><mo>&#160;</mo><mn>81</mn><mo>&#176;</mo><mo>)</mo></mrow><mrow><mn>2</mn><msup><mrow><mi>s</mi><mi>i</mi><mi>n</mi></mrow><mn>2</mn></msup><mo>&#160;</mo><mn>63</mn><mo>&#176;</mo><mo>+</mo><mn>1</mn><mo>+</mo><mn>2</mn><msup><mrow><mi>s</mi><mi>i</mi><mi>n</mi></mrow><mn>2</mn></msup><mo>&#160;</mo><mn>27</mn><mo>&#176;</mo></mrow></mfrac></math>&nbsp;का मान होगा :</p>",
                    options_en: ["<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>", 
                                "<p>2</p>", "<p>1</p>"],
                    options_hi: ["<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>",
                                "<p>2</p>", "<p>1</p>"],
                    solution_en: "<p>75.(b)<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#160;</mi><mn>9</mn><mo>&#176;</mo><mo>+</mo><mi>&#160;</mi><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#160;</mi><mn>81</mn><mo>&#176;</mo><mo>)</mo><mo>(</mo><mi>s</mi><mi>e</mi><mi>c</mi><mi>&#160;</mi><mn>9</mn><mo>&#176;</mo><mo>+</mo><mo>&#160;</mo><mi>c</mi><mi>o</mi><mi>s</mi><mi>e</mi><mi>c</mi><mo>&#160;</mo><mn>81</mn><mo>&#176;</mo><mo>)</mo></mrow><mrow><mn>2</mn><msup><mrow><mi>s</mi><mi>i</mi><mi>n</mi></mrow><mn>2</mn></msup><mo>&#160;</mo><mn>63</mn><mo>&#176;</mo><mo>+</mo><mn>1</mn><mo>+</mo><mn>2</mn><msup><mrow><mi>s</mi><mi>i</mi><mi>n</mi></mrow><mn>2</mn></msup><mo>&#160;</mo><mn>27</mn><mo>&#176;</mo></mrow></mfrac></math><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mi>c</mi><mi>o</mi><mi>s</mi><mo>&#160;</mo><mn>9</mn><mo>&#176;</mo><mo>+</mo><mi>&#160;</mi><mi>c</mi><mi>o</mi><mi>s</mi><mo>&#160;</mo><mn>9</mn><mo>&#176;</mo><mo>)</mo><mo>(</mo><mi>s</mi><mi>e</mi><mi>c</mi><mo>&#160;</mo><mn>9</mn><mo>&#176;</mo><mo>+</mo><mi>s</mi><mi>e</mi><mi>c</mi><mo>&#160;</mo><mn>9</mn><mo>&#176;</mo><mo>)</mo></mrow><mrow><mn>2</mn><msup><mrow><mi>s</mi><mi>i</mi><mi>n</mi></mrow><mrow><mn>2</mn><mo>&#160;</mo></mrow></msup><mn>63</mn><mo>&#176;</mo><mo>+</mo><mn>1</mn><mo>+</mo><mn>2</mn><msup><mrow><mi>c</mi><mi>o</mi><mi>s</mi></mrow><mn>2</mn></msup><mo>&#160;</mo><mn>63</mn><mo>&#176;</mo></mrow></mfrac></math><br>=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>2</mn><mi>cos</mi><mo>&#160;</mo><mn>9</mn><mo>&#176;</mo><mo>)</mo><mo>(</mo><mn>2</mn><mi>s</mi><mi>e</mi><mi>c</mi><mo>&#160;</mo><mn>9</mn><mo>&#176;</mo><mo>)</mo></mrow><mrow><mn>2</mn><mo>(</mo><msup><mi>sin</mi><mn>2</mn></msup><mo>&#160;</mo><mn>63</mn><mo>&#176;</mo><mo>+</mo><msup><mi>cos</mi><mn>2</mn></msup><mo>&#160;</mo><mn>63</mn><mo>&#176;</mo><mo>)</mo><mo>+</mo><mn>1</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>3</mn></mfrac></math></p>",
                    solution_hi: "<p>75.(b)<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#160;</mi><mn>9</mn><mo>&#176;</mo><mo>+</mo><mi>&#160;</mi><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#160;</mi><mn>81</mn><mo>&#176;</mo><mo>)</mo><mo>(</mo><mi>s</mi><mi>e</mi><mi>c</mi><mi>&#160;</mi><mn>9</mn><mo>&#176;</mo><mo>+</mo><mo>&#160;</mo><mi>c</mi><mi>o</mi><mi>s</mi><mi>e</mi><mi>c</mi><mo>&#160;</mo><mn>81</mn><mo>&#176;</mo><mo>)</mo></mrow><mrow><mn>2</mn><msup><mrow><mi>s</mi><mi>i</mi><mi>n</mi></mrow><mn>2</mn></msup><mo>&#160;</mo><mn>63</mn><mo>&#176;</mo><mo>+</mo><mn>1</mn><mo>+</mo><mn>2</mn><msup><mrow><mi>s</mi><mi>i</mi><mi>n</mi></mrow><mn>2</mn></msup><mo>&#160;</mo><mn>27</mn><mo>&#176;</mo></mrow></mfrac></math><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mi>c</mi><mi>o</mi><mi>s</mi><mo>&#160;</mo><mn>9</mn><mo>&#176;</mo><mo>+</mo><mi>&#160;</mi><mi>c</mi><mi>o</mi><mi>s</mi><mo>&#160;</mo><mn>9</mn><mo>&#176;</mo><mo>)</mo><mo>(</mo><mi>s</mi><mi>e</mi><mi>c</mi><mo>&#160;</mo><mn>9</mn><mo>&#176;</mo><mo>+</mo><mi>s</mi><mi>e</mi><mi>c</mi><mo>&#160;</mo><mn>9</mn><mo>&#176;</mo><mo>)</mo></mrow><mrow><mn>2</mn><msup><mrow><mi>s</mi><mi>i</mi><mi>n</mi></mrow><mrow><mn>2</mn><mo>&#160;</mo></mrow></msup><mn>63</mn><mo>&#176;</mo><mo>+</mo><mn>1</mn><mo>+</mo><mn>2</mn><msup><mrow><mi>c</mi><mi>o</mi><mi>s</mi></mrow><mn>2</mn></msup><mo>&#160;</mo><mn>63</mn><mo>&#176;</mo></mrow></mfrac></math><br>=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>2</mn><mi>cos</mi><mo>&#160;</mo><mn>9</mn><mo>&#176;</mo><mo>)</mo><mo>(</mo><mn>2</mn><mi>s</mi><mi>e</mi><mi>c</mi><mo>&#160;</mo><mn>9</mn><mo>&#176;</mo><mo>)</mo></mrow><mrow><mn>2</mn><mo>(</mo><msup><mi>sin</mi><mn>2</mn></msup><mo>&#160;</mo><mn>63</mn><mo>&#176;</mo><mo>+</mo><msup><mi>cos</mi><mn>2</mn></msup><mo>&#160;</mo><mn>63</mn><mo>&#176;</mo><mo>)</mo><mo>+</mo><mn>1</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>3</mn></mfrac></math></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "76",
                    section: "7",
                    question_en: "<p>76. Select the most appropriate option to fill in the blank.<br>Being a millionaire, he is leading a _______ life.</p>",
                    question_hi: "<p>76. Select the most appropriate option to fill in the blank.<br>Being a millionaire, he is leading a _______ life.</p>",
                    options_en: ["<p>expensive</p>", "<p>conducive</p>", 
                                "<p>destitute</p>", "<p>luxurious</p>"],
                    options_hi: ["<p>expensive</p>", "<p>conducive</p>",
                                "<p>destitute</p>", "<p>luxurious</p>"],
                    solution_en: "<p>76.(d) <br><strong>luxurious </strong>- very comfortable<br><strong>expensive-</strong> costing a lot of money<br><strong>conducive</strong> - helping or making something happen<br><strong>destitute</strong> - without any money, food or a home</p>",
                    solution_hi: "<p>76.(d) <br><strong>luxurious</strong> (विलासितापूर्ण)- very comfortable<br><strong>expensive</strong> (मूल्यवान)- costing a lot of money<br><strong>conducive</strong> (अनुकूल) - helping or making something happen<br><strong>destitute</strong> (निराश्रित) - without any money, food or a home</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "77",
                    section: "7",
                    question_en: "<p>77. Select the appropriate antonym of the given word.<br>Sparse</p>",
                    question_hi: "<p>77. Select the appropriate antonym of the given word.<br>Sparse</p>",
                    options_en: ["<p>Costly</p>", "<p>Tranquil</p>", 
                                "<p>Scanty</p>", "<p>Abundant</p>"],
                    options_hi: ["<p>Costly</p>", "<p>Tranquil</p>",
                                "<p>Scanty</p>", "<p>Abundant</p>"],
                    solution_en: "<p>77.(d) <br><strong>Abundant</strong> - existing or available in large quantities; plentiful. <br><strong>Sparse</strong> - thinly dispersed or scattered.<br><strong>Costly</strong> - expensive.<br><strong>Tranquil</strong> - free from disturbance; calm.<br><strong>Scanty-</strong> small or insufficient in quantity or amount.</p>",
                    solution_hi: "<p>77.(d) <br><strong>Abundant</strong> (प्रचुर)- existing or available in large quantities; plentiful. <br><strong>Sparse</strong> (विरल)- thinly dispersed or scattered.<br><strong>Costly</strong> (कीमती) - expensive.<br><strong>Tranquil</strong> (शांत) -free from disturbance; calm.<br><strong>Scanty</strong> (अल्प) - small or insufficient in quantity or amount.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "78",
                    section: "7",
                    question_en: "<p>78. Select the correct passive form of the given sentence.<br>Did the problems you had to face discourage you ?</p>",
                    question_hi: "<p>78. Select the correct passive form of the given sentence.<br>Did the problems you had to face discourage you ?</p>",
                    options_en: ["<p>Are you being discouraged by the problems you had to face?</p>", "<p>Have you been discouraged by the problems you have to face?</p>", 
                                "<p>Are you discouraged by the problems you had to face?</p>", "<p>Were you discouraged by the problems you had to face?</p>"],
                    options_hi: ["<p>Are you being discouraged by the problems you had to face?</p>", "<p>Have you been discouraged by the problems you have to face?</p>",
                                "<p>Are you discouraged by the problems you had to face?</p>", "<p>Were you discouraged by the problems you had to face?</p>"],
                    solution_en: "<p>78.(d) Were you discouraged by the problems you had to face? (Correct) <br>(a) <span style=\"text-decoration: underline;\">Are you being discouraged</span> by the problems you had to face?(Incorrect Tense) <br>(b) <span style=\"text-decoration: underline;\">Have you been discouraged</span> by the problems you <span style=\"text-decoration: underline;\">have</span> to face?(Incorrect Tense) <br>(c) <span style=\"text-decoration: underline;\">Are you discouraged</span> by the problems you had to face?(Incorrect Tense)</p>",
                    solution_hi: "<p>78.(d) Were you discouraged by the problems you had to face? (Correct) <br>(a) <span style=\"text-decoration: underline;\">Are you being discouraged</span> by the problems you had to face?(गलत Tense) <br>(b) <span style=\"text-decoration: underline;\">Have you been discouraged</span> by the problems you <span style=\"text-decoration: underline;\">have</span> to face?(गलत Tense) <br>(c) <span style=\"text-decoration: underline;\">Are you discouraged</span> by the problems you had to face?(गलत Tense)</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "79",
                    section: "7",
                    question_en: "<p>79. Select the words which means the same as group of words given.<br>One who is preoccupied with his own interests.</p>",
                    question_hi: "<p>79. Select the words which means the same as group of words given.<br>One who is preoccupied with his own interests.</p>",
                    options_en: ["<p>Egoist</p>", "<p>Pessimist</p>", 
                                "<p>Atheist</p>", "<p>Optimist</p>"],
                    options_hi: ["<p>Egoist</p>", "<p>Pessimist</p>",
                                "<p>Atheist</p>", "<p>Optimist</p>"],
                    solution_en: "<p>79.(a) <br><strong>Egoist</strong> - One who is preoccupied with his own interests.<br><strong>Pessimist</strong> - a person who tends to see the worst aspect of things or believe that the worst will happen.<br><strong>Atheist</strong> - a person who disbelieves or lacks belief in the existence of God or gods.<br><strong>Optimist</strong> - a person who tends to be hopeful and confident about the future or the success of something.</p>",
                    solution_hi: "<p>79.(a) <br><strong>Egoist</strong> (अहंवादी) - One who is preoccupied with his own interests.<br><strong>Pessimist</strong> (निराशावादी) - a person who tends to see the worst aspect of things or believe that the worst will happen.<br><strong>Atheist</strong> (नास्तिक) - a person who disbelieves or lacks belief in the existence of God or gods.<br><strong>Optimist</strong> (आशावादी) - a person who tends to be hopeful and confident about the future or the success of something.</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "80",
                    section: "7",
                    question_en: "<p>80. Select the most appropriate sequence from the given options to make a meaningful paragraph from jumbled sentences.<br>A. Upon returning from his travels, he was forced to live in a house where his large family lived.<br>B. Vaikom Muhammad Basheer was a Malayalam fiction writer from Vaikom in Kerela.<br>C. The household was always noisy and full of chaos, no place for a writer surely!<br>D. Apart from family members, myriads of domestic animals also treated the house as their own.</p>",
                    question_hi: "<p>80. Select the most appropriate sequence from the given options to make a meaningful paragraph from jumbled sentences.<br>A. Upon returning from his travels, he was forced to live in a house where his large family lived.<br>B. Vaikom Muhammad Basheer was a Malayalam fiction writer from Vaikom in Kerela.<br>C. The household was always noisy and full of chaos, no place for a writer surely!<br>D. Apart from family members, myriads of domestic animals also treated the house as their own.</p>",
                    options_en: ["<p>BCAD</p>", "<p>BCDA</p>", 
                                "<p>BADC</p>", "<p>BDCA</p>"],
                    options_hi: ["<p>BCAD</p>", "<p>BCDA</p>",
                                "<p>BADC</p>", "<p>BDCA</p>"],
                    solution_en: "<p>80.(c) BADC<br>Sentence B says that Vaikom Muhammad Basheer was a Malayalam fiction writer and Sentence A states that he was forced to live in a house where his large family lived. A will follow B. <br>Sentence D says that apart from family members, myriads of domestic animals also treated the house as their own. D will follow A. So, Option (c) is the correct sequence.</p>",
                    solution_hi: "<p>80.(c) BADC<br>Sentence B कहता है कि Vaikom Muhammad Basheer एक मलयालम fiction writer थे और Sentence A कहता है कि उन्हें एक ऐसे घर में रहने के लिए forced किया गया था जहाँ उनका बड़ा परिवार रहता था। इसलिए, A, B के बाद आएगा। Sentence D कहता है कि family members के अलावा, असंख्य पालतू जानवर भी घर को अपना मानते थे। इसलिए, D, A के बाद आएगा। अतः Option (c) में सही sequence है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "81",
                    section: "7",
                    question_en: "<p>81. Select the incorrectly spelt word.</p>",
                    question_hi: "<p>81. Select the incorrectly spelt word.</p>",
                    options_en: ["<p>Ignorance</p>", "<p>Influence</p>", 
                                "<p>Itinerent</p>", "<p>Impeach</p>"],
                    options_hi: ["<p>Ignorance</p>", "<p>Influence</p>",
                                "<p>Itinerent</p>", "<p>Impeach</p>"],
                    solution_en: "<p>81.(c) Itinerent. <br>Itinerant is the correct spelling.</p>",
                    solution_hi: "<p>81.(c) Itinerent. <br>Itinerant सही spelling है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "82",
                    section: "7",
                    question_en: "<p>82. <strong>Cloze Test:</strong><br>Milton Hershey returned to the village where he had been born, in the heart of dairy country. He opened his chocolate manufacturing plant (82) ______ 1905. With access to all the fresh milk he (83) ______, he began producing the finest milk chocolate. The plant (84) _______ opened in a small Pennsylvania village is today the (85) ______ chocolate factory in the world. The confections created here are (86) _______ around the world.<br>Select the most appropriate option for blank no.82</p>",
                    question_hi: "<p>82. <strong>Cloze Test:</strong><br>Milton Hershey returned to the village where he had been born, in the heart of dairy country. He opened his chocolate manufacturing plant (82) ______ 1905. With access to all the fresh milk he (83) ______, he began producing the finest milk chocolate. The plant (84) _______ opened in a small Pennsylvania village is today the (85) ______ chocolate factory in the world. The confections created here are (86) _______ around the world.<br>Select the most appropriate option for blank no.82</p>",
                    options_en: ["<p>to</p>", "<p>at</p>", 
                                "<p>in</p>", "<p>on</p>"],
                    options_hi: ["<p>to</p>", "<p>at</p>",
                                "<p>in</p>", "<p>on</p>"],
                    solution_en: "<p>82.(c) in.<br>With Year, Months, Seasons, Parts of the day, point of time in the future, duration(within a maximum period) preposition &lsquo;in&rsquo; is used.</p>",
                    solution_hi: "<p>82.(c) in.<br>Year, Months, Seasons, Parts of the day, point of time in the future, duration (अधिकतम अवधि में) के साथ preposition &lsquo;in&rsquo; का प्रयोग किया जाता है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "83",
                    section: "7",
                    question_en: "<p>83. <strong>Cloze Test:</strong><br>Milton Hershey returned to the village where he had been born, in the heart of dairy country. He opened his chocolate manufacturing plant (82) ______ 1905. With access to all the fresh milk he (83) ______, he began producing the finest milk chocolate. The plant (84) _______ opened in a small Pennsylvania village is today the (85) ______ chocolate factory in the world. The confections created here are (86) _______ around the world.<br>Select the most appropriate option for blank no.83</p>",
                    question_hi: "<p>83. <strong>Cloze Test:</strong><br>Milton Hershey returned to the village where he had been born, in the heart of dairy country. He opened his chocolate manufacturing plant (82) ______ 1905. With access to all the fresh milk he (83) ______, he began producing the finest milk chocolate. The plant (84) _______ opened in a small Pennsylvania village is today the (85) ______ chocolate factory in the world. The confections created here are (86) _______ around the world.<br>Select the most appropriate option for blank no.83</p>",
                    options_en: ["<p>considered</p>", "<p>kneaded</p>", 
                                "<p>distributed</p>", "<p>needed</p>"],
                    options_hi: ["<p>considered</p>", "<p>kneaded</p>",
                                "<p>distributed</p>", "<p>needed</p>"],
                    solution_en: "<p>83.(d) needed.</p>",
                    solution_hi: "<p>83.(d) needed.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "84",
                    section: "7",
                    question_en: "<p>84. <strong>Cloze Test:</strong><br>Milton Hershey returned to the village where he had been born, in the heart of dairy country. He opened his chocolate manufacturing plant (82) ______ 1905. With access to all the fresh milk he (83) ______, he began producing the finest milk chocolate. The plant (84) _______ opened in a small Pennsylvania village is today the (85) ______ chocolate factory in the world. The confections created here are (86) _______ around the world.<br>Select the most appropriate option for blank no 84.</p>",
                    question_hi: "<p>84. <strong>Cloze Test:</strong><br>Milton Hershey returned to the village where he had been born, in the heart of dairy country. He opened his chocolate manufacturing plant (82) ______ 1905. With access to all the fresh milk he (83) ______, he began producing the finest milk chocolate. The plant (84) _______ opened in a small Pennsylvania village is today the (85) ______ chocolate factory in the world. The confections created here are (86) _______ around the world.<br>Select the most appropriate option for blank no 84.</p>",
                    options_en: ["<p>that</p>", "<p>when</p>", 
                                "<p>who</p>", "<p>what</p>"],
                    options_hi: ["<p>that</p>", "<p>when</p>",
                                "<p>who</p>", "<p>what</p>"],
                    solution_en: "<p>84.(a) that.<br>Here the comparison between the past and present situation of the plant is being talked about therefore &lsquo;that&rsquo; should be used.</p>",
                    solution_hi: "<p>84.(a) that. <br>यहाँ plant के past एवं present स्थिति के मध्य comparison की बात की जा रही है, इसलिए &lsquo;that&rsquo; का प्रयोग किया जाना चाहिए।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "85",
                    section: "7",
                    question_en: "<p>85. <strong>Cloze Test:</strong><br>Milton Hershey returned to the village where he had been born, in the heart of dairy country. He opened his chocolate manufacturing plant (82) ______ 1905. With access to all the fresh milk he (83) ______, he began producing the finest milk chocolate. The plant (84) _______ opened in a small Pennsylvania village is today the (85) ______ chocolate factory in the world. The confections created here are (86) _______ around the world.<br>Select the most appropriate option for blank no.85</p>",
                    question_hi: "<p>85. <strong>Cloze Test:</strong><br>Milton Hershey returned to the village where he had been born, in the heart of dairy country. He opened his chocolate manufacturing plant (82) ______ 1905. With access to all the fresh milk he (83) ______, he began producing the finest milk chocolate. The plant (84) _______ opened in a small Pennsylvania village is today the (85) ______ chocolate factory in the world. The confections created here are (86) _______ around the world. <br>Select the most appropriate option for blank no.85</p>",
                    options_en: ["<p>large</p>", "<p>most largest</p>", 
                                "<p>largest</p>", "<p>larger</p>"],
                    options_hi: ["<p>large</p>", "<p>most largest</p>",
                                "<p>largest</p>", "<p>larger</p>"],
                    solution_en: "<p>85.(c) largest <br>After &lsquo;the&rsquo; superlative degree should be used because we use a superlative to say that a thing or person is the most of a group. When we use a superlative adjective (\'the tallest student\') before the noun, we generally use it with \'the\'. This is because there\'s only one (or one group) of the thing we are talking about.</p>",
                    solution_hi: "<p>85.(c) largest <br>&lsquo;the&rsquo; के बाद superlative degree का प्रयोग किया जाना चाहिए, क्योंकि हम किसी superlative degree का प्रयोग यह कहने के लिए करते हैं कि कोई thing या person किसी group में सबसे अधिक है। जब हम noun से पहले superlative adjective (\'the tallest student\') का प्रयोग करते हैं, तो हम सामान्यतः इसका प्रयोग &lsquo;the&rsquo; के साथ करते हैं। ऐसा इसलिए है क्योंकि हम जिस वस्तु के बारे में बात कर रहे हैं, वह केवल एक (या एक समूह) है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "86",
                    section: "7",
                    question_en: "<p>86. <strong>Cloze Test:</strong><br>Milton Hershey returned to the village where he had been born, in the heart of dairy country. He opened his chocolate manufacturing plant (82) ______ 1905. With access to all the fresh milk he (83) ______, he began producing the finest milk chocolate. The plant (84) _______ opened in a small Pennsylvania village is today the (85) ______ chocolate factory in the world. The confections created here are (86) _______ around the world.<br>Select the most appropriate option for blank no.86</p>",
                    question_hi: "<p>86.<strong> Cloze Test:</strong><br>Milton Hershey returned to the village where he had been born, in the heart of dairy country. He opened his chocolate manufacturing plant (82) ______ 1905. With access to all the fresh milk he (83) ______, he began producing the finest milk chocolate. The plant (84) _______ opened in a small Pennsylvania village is today the (85) ______ chocolate factory in the world. The confections created here are (86) _______ around the world.<br>Select the most appropriate option for blank no.86</p>",
                    options_en: ["<p>choices</p>", "<p>favourites</p>", 
                                "<p>collections</p>", "<p>selections</p>"],
                    options_hi: ["<p>choices</p>", "<p>favourites</p>",
                                "<p>collections</p>", "<p>selections</p>"],
                    solution_en: "<p>86.(b) favourites.</p>",
                    solution_hi: "<p>86.(b) favourites.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "87",
                    section: "7",
                    question_en: "<p>87. Select the most appropriate word to fill in the blank.<br>Children under five years are ______ from passport biometrics.</p>",
                    question_hi: "<p>87. Select the most appropriate word to fill in the blank.<br>Children under five years are ______ from passport biometrics.</p>",
                    options_en: ["<p>acquitted</p>", "<p>exempted</p>", 
                                "<p>escaped</p>", "<p>allowed</p>"],
                    options_hi: ["<p>acquitted</p>", "<p>exempted</p>",
                                "<p>escaped</p>", "<p>allowed</p>"],
                    solution_en: "<p>87.(b) <br><strong>Exempted </strong>- free (a person or organization) from an obligation or liability imposed on others.<br><strong>Acquitted</strong> - free (someone) from a criminal charge by a verdict of not guilty.<br><strong>Escaped</strong> - having broken free from confinement or control.<br><strong>Allowed</strong> - let (someone) have or do something.</p>",
                    solution_hi: "<p>87.(b) <br><strong>Exempted</strong> (मुक्त करना) - free (a person or organization) from an obligation or liability imposed on others.<br><strong>Acquitted</strong> (दोषमुक्त करना) - free (someone) from a criminal charge by a verdict of not guilty.<br><strong>Escaped</strong> (सुरक्षित निकल जाना) - having broken free from confinement or control.<br><strong>Allowed</strong> (अनुमति देना) - let (someone) have or do something.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "88",
                    section: "7",
                    question_en: "<p>88. Select the words which the same as group of words given.<br>A geometrical figure with eight sides.</p>",
                    question_hi: "<p>88. Select the words which the same as group of words given.<br>A geometrical figure with eight sides.</p>",
                    options_en: ["<p>Octagon</p>", "<p>Pentagon</p>", 
                                "<p>Hexagon</p>", "<p>Heptagon</p>"],
                    options_hi: ["<p>Octagon</p>", "<p>Pentagon</p>",
                                "<p>Hexagon</p>", "<p>Heptagon</p>"],
                    solution_en: "<p>88.(a) <br><strong>Octagon</strong> - A geometrical figure with eight sides.<br><strong>Pentagon</strong>- five sides<br><strong>Hexagon </strong>- six sides<br><strong>Heptagon</strong>- seven sides</p>",
                    solution_hi: "<p>88.(a) <br><strong>Octagon </strong>(अष्टभुज) - A geometrical figure with eight sides.<br><strong>Pentagon </strong>(पंचभुज) - five sides<br><strong>Hexagon</strong> (षट्भुज) - six sides<br><strong>Heptagon </strong>(सप्तभुज) - seven sides</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "89",
                    section: "7",
                    question_en: "<p>89. Select the most appropriate meaning of the given idiom.<br>Bring to light</p>",
                    question_hi: "<p>89. Select the most appropriate meaning of the given idiom.<br>Bring to light</p>",
                    options_en: ["<p>Reveal clearly</p>", "<p>Cheer someone</p>", 
                                "<p>Praise in public</p>", "<p>Brighten up</p>"],
                    options_hi: ["<p>Reveal clearly</p>", "<p>Cheer someone</p>",
                                "<p>Praise in public</p>", "<p>Brighten up</p>"],
                    solution_en: "<p>89.(a) Bring to light - Reveal clearly</p>",
                    solution_hi: "<p>89.(a) Bring to light - Reveal clearly/खुलासा करना।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "90",
                    section: "7",
                    question_en: "<p>90. Select the appropriate synonym of the given word.<br>Fury</p>",
                    question_hi: "<p>90. Select the appropriate synonym of the given word.<br>Fury</p>",
                    options_en: ["<p>Sin</p>", "<p>Risk</p>", 
                                "<p>anger</p>", "<p>crime</p>"],
                    options_hi: ["<p>Sin</p>", "<p>Risk</p>",
                                "<p>anger</p>", "<p>crime</p>"],
                    solution_en: "<p>90.(c) <br><strong>Anger </strong>- a strong feeling of annoyance, displeasure, or hostility.<br><strong>Fury </strong>- wild or violent anger.<br><strong>Sin</strong> - an immoral act considered to be a transgression against divine law.<br><strong>Risk</strong> - a situation involving exposure to danger.<br><strong>crime</strong> - an action or omission which constitutes an offence and is punishable by law.</p>",
                    solution_hi: "<p>90.(c) <br><strong>Anger</strong> (क्रोध) - a strong feeling of annoyance, displeasure, or hostility.<br><strong>Fury</strong> (उग्र) - wild or violent anger.<br><strong>Sin</strong> (पाप) - an immoral act considered to be a transgression against divine law.<br><strong>Risk</strong> (जोखिम) - a situation involving exposure to danger.<br><strong>Crime</strong> (अपराध) - an action or omission which constitutes an offence and is punishable by law.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "91",
                    section: "7",
                    question_en: "<p>91. Select the most appropriate meaning of the given idiom.<br>Hold water</p>",
                    question_hi: "<p>91. Select the most appropriate meaning of the given idiom.<br>Hold water</p>",
                    options_en: ["<p>to be fickle</p>", "<p>to be busy</p>", 
                                "<p>to be valid</p>", "<p>to be deep</p>"],
                    options_hi: ["<p>to be fickle</p>", "<p>to be busy</p>",
                                "<p>to be valid</p>", "<p>to be deep</p>"],
                    solution_en: "<p>91.(c) Hold water - to be valid</p>",
                    solution_hi: "<p>91.(c) Hold water - to be valid/ तर्कसंगत होना।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "92",
                    section: "7",
                    question_en: "<p>92. Select the most appropriate option to substitute the underlined segment in the given sentence. If there is no need to substitute it, select \'No substitution\'<br>Your advice will <span style=\"text-decoration: underline;\">benefit to me</span>.</p>",
                    question_hi: "<p>92. Select the most appropriate option to substitute the underlined segment in the given sentence. If there is no need to substitute it, select \'No substitution\'<br>Your advice will <span style=\"text-decoration: underline;\">benefit to me</span>.</p>",
                    options_en: ["<p>benefit from me</p>", "<p>benefit me</p>", 
                                "<p>No substitution</p>", "<p>benefit for me</p>"],
                    options_hi: ["<p>benefit from me</p>", "<p>benefit me</p>",
                                "<p>No substitution</p>", "<p>benefit for me</p>"],
                    solution_en: "<p>92.(b) benefit me.<br>&ldquo;To&rdquo; should be avoided. &lsquo;Benefit&rsquo; doesn&rsquo;t take preposition &lsquo;to&rsquo; after it .</p>",
                    solution_hi: "<p>92.(b) benefit me.<br>&ldquo;To&rdquo; को avoid किया जाना चाहिए। &lsquo;Benefit&rsquo; के बाद preposition &lsquo;to&rsquo; का प्रयोग नहीं होता है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "93",
                    section: "7",
                    question_en: "<p>93. Select the most appropriate option to substitute the underlined segment in the given sentence. If there is no need to substitute it, select \'No substitution\'<br><span style=\"text-decoration: underline;\">Where you left</span> your bag yesterday ?</p>",
                    question_hi: "<p>93. Select the most appropriate option to substitute the underlined segment in the given sentence. If there is no need to substitute it, select \'No substitution\'<br><span style=\"text-decoration: underline;\">Where you left</span> your bag yesterday ?</p>",
                    options_en: ["<p>No substitution</p>", "<p>Where did you left</p>", 
                                "<p>Where were you leaving</p>", "<p>Where did you leave</p>"],
                    options_hi: ["<p>No substitution</p>", "<p>Where did you left</p>",
                                "<p>Where were you leaving</p>", "<p>Where did you leave</p>"],
                    solution_en: "<p>93.(d) Where did you leave.<br>&ldquo;Where you left&rdquo; should be replaced with &ldquo;Where did you leave&rdquo;. The auxiliary verb &lsquo;did&rsquo; should be used after &lsquo;where&rsquo; and with &lsquo;did&rsquo; first form of verb is to be used i.e. leave</p>",
                    solution_hi: "<p>93.(d) Where did you leave<br>&ldquo;Where you left&rdquo; के स्थान पर &ldquo;Where did you leave&rdquo; का प्रयोग किया जाना चाहिए। Where के बाद Auxiliary verb &lsquo;did&rsquo; का प्रयोग किया जाना चाहिए तथा &lsquo;did&rsquo; के साथ verb की first form &lsquo;leave&rsquo; का प्रयोग किया जाना चाहिए।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "94",
                    section: "7",
                    question_en: "<p>94. Select the appropriate antonym of the given word.<br>Friendship</p>",
                    question_hi: "<p>94. Select the appropriate antonym of the given word.<br>Friendship</p>",
                    options_en: ["<p>Enmity</p>", "<p>Amity</p>", 
                                "<p>Slavery</p>", "<p>Arrogance</p>"],
                    options_hi: ["<p>Enmity</p>", "<p>Amity</p>",
                                "<p>Slavery</p>", "<p>Arrogance</p>"],
                    solution_en: "<p>94.(a) <br><strong>Enmity </strong>- a state or feeling of active opposition or hostility.<br><strong>Amity </strong>- friendly relations.<br><strong>Slavery </strong>- the state of being a slave.<br><strong>Arrogance</strong> - the quality of being arrogant.</p>",
                    solution_hi: "<p>94.(a) <br><strong>Enmity </strong>(शत्रुता) - a state or feeling of active opposition or hostility.<br><strong>Amity</strong> (मित्रता) - friendly relations.<br><strong>Slavery </strong>(गुलामी) - the state of being a slave.<br><strong>Arrogance </strong>(अहंकार) - the quality of being arrogant.</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "95",
                    section: "7",
                    question_en: "<p>95. Select the correct indirect form of the given sentence.<br>The lawyer said to me,\"There is no proof of your involvement in this case.\"</p>",
                    question_hi: "<p>95. Select the correct indirect form of the given sentence.<br>The lawyer said to me,\"There is no proof of your involvement in this case.\"</p>",
                    options_en: ["<p>The lawyer told that there is no proof of my involvement in that case.</p>", "<p>The lawyer told me that there was no proof of my involvement in that case.</p>", 
                                "<p>The lawyer told me that there is no proof of your involvement in that case.</p>", "<p>The lawyer said me that there was no proof of my involvement in this case.</p>"],
                    options_hi: ["<p>The lawyer told that there is no proof of my involvement in that case.</p>", "<p>The lawyer told me that there was no proof of my involvement in that case.</p>",
                                "<p>The lawyer told me that there is no proof of your involvement in that case.</p>", "<p>The lawyer said me that there was no proof of my involvement in this case.</p>"],
                    solution_en: "<p>95.(b) The lawyer told me that there was no proof of my involvement in that case. (Correct) <br>(a) The lawyer <span style=\"text-decoration: underline;\">told</span> that there <span style=\"text-decoration: underline;\">is</span> no proof of my involvement in that case.(Incorrect Tense)<br>(c) The lawyer told me that there <span style=\"text-decoration: underline;\">is no</span> proof of your involvement in that case.(Incorrect Tense, Reported Verb)<br>(d) The lawyer <span style=\"text-decoration: underline;\">said me</span> that there was no proof of my involvement in this case. (Incorrect Tense, Reported Verb)</p>",
                    solution_hi: "<p>95.(b) The lawyer told me that there was no proof of my involvement in that case. (Correct) <br>(a) The lawyer <span style=\"text-decoration: underline;\">told</span> that there <span style=\"text-decoration: underline;\">is</span> no proof of my involvement in that case.(गलत Tense)<br>(c) The lawyer told me that there <span style=\"text-decoration: underline;\">is no</span> proof of your involvement in that case.(गलत Tense, Reported Verb)<br>(d) The lawyer <span style=\"text-decoration: underline;\">said me</span> that there was no proof of my involvement in this case. (गलत Tense, Reported Verb)</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "96",
                    section: "7",
                    question_en: "<p>96. Identify the segment in the sentence which contains the grammatical error from the given options.<br>I am believing you have submitted all the necessary documents.</p>",
                    question_hi: "<p>96. Identify the segment in the sentence which contains the grammatical error from the given options.<br>I am believing you have submitted all the necessary documents.</p>",
                    options_en: ["<p>I am believing</p>", "<p>necessary documents</p>", 
                                "<p>you have submitted</p>", "<p>all the</p>"],
                    options_hi: ["<p>I am believing</p>", "<p>necessary documents</p>",
                                "<p>you have submitted</p>", "<p>all the</p>"],
                    solution_en: "<p>96.(a) I am believing. <br>&ldquo;I am believing&rdquo; should be replaced with &lsquo;I believe&rsquo;</p>",
                    solution_hi: "<p>96.(a) I am believing. <br>&ldquo;I am believing&rdquo; के स्थान पर &lsquo;I believe&rsquo; का प्रयोग किया जाना चाहिए।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "97",
                    section: "7",
                    question_en: "<p>97. Identify the segment in the sentence which contains the grammatical error from the given options.<br>No sooner did we receive your message when we heaved a sigh of relief.</p>",
                    question_hi: "<p>97. Identify the segment in the sentence which contains the grammatical error from the given options.<br>No sooner did we receive your message when we heaved a sigh of relief.</p>",
                    options_en: ["<p>did we receive</p>", "<p>a sigh of relief</p>", 
                                "<p>your message</p>", "<p>when we heaved</p>"],
                    options_hi: ["<p>did we receive</p>", "<p>a sigh of relief</p>",
                                "<p>your message</p>", "<p>when we heaved</p>"],
                    solution_en: "<p>97.(d) when we heaved. <br>&ldquo;then&rdquo; should be used in place of &ldquo;when&rdquo;. The conjunction \'No sooner ----- than\' is used in Present and Past tenses. It can be used only in a sentence in which two actions take place. \'No sooner\' should never be followed by the word \'when\'.</p>",
                    solution_hi: "<p>97.(d) when we heaved. <br>&lsquo;When&rsquo; के स्थान पर &ldquo;then&rdquo; का प्रयोग किया जाना चाहिए। Conjunction \'\'No sooner ----- than\' का प्रयोग Present एवं Past tense में किया जाता है। इसका प्रयोग केवल उसी sentence में किया जा सकता है जिसमें दो action हुए हों। \'No sooner\' के बाद कभी भी \'when&rsquo; word का प्रयोग नहीं करना चाहिए।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "98",
                    section: "7",
                    question_en: "<p>98. Select the most appropriate sequence from the given options to make a meaningful paragraph from jumbled sentences.<br>A. Even the most ill-equipped laboratory would have been better than their shed.<br>B. Thus, they did not allow any difficulties to come in their way.<br>C. But their mind was set upon the discovery of radium.<br>D. The curies had to work in extreme poverty.</p>",
                    question_hi: "<p>98. Select the most appropriate sequence from the given options to make a meaningful paragraph from jumbled sentences.<br>A. Even the most ill-equipped laboratory would have been better than their shed.<br>B. Thus, they did not allow any difficulties to come in their way.<br>C. But their mind was set upon the discovery of radium.<br>D. The curies had to work in extreme poverty.</p>",
                    options_en: ["<p>DACB</p>", "<p>ABCD</p>", 
                                "<p>ADBC</p>", "<p>DBCA</p>"],
                    options_hi: ["<p>DACB</p>", "<p>ABCD</p>",
                                "<p>ADBC</p>", "<p>DBCA</p>"],
                    solution_en: "<p>98.(a) DACB. <br>Sentence D says that the curies had to work in extreme poverty. Sentence A states that even the most ill-equipped laboratory would have been better than their shed.<br>Sentence C says that their mind was set upon the discovery of radium. C will follow A. Option (a) DACB is the correct sequence.</p>",
                    solution_hi: "<p>98.(a) DACB. <br>Sentence D कहता है कि curies को अत्यधिक गरीबी में काम करना पड़ा। Sentence A कहता है कि सबसे बेकार equipped laboratory भी उनके शेड से बेहतर होती। Sentence C कहता है कि उनका मन radium की खोज पर केंद्रित था। इसलिए, C, A के बाद आएगा। अतः Option (a) DACB में सही sequence है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "99",
                    section: "7",
                    question_en: "<p>99. Select the appropriate synonym of the given word.<br>Solitary</p>",
                    question_hi: "<p>99. Select the appropriate synonym of the given word.<br>Solitary</p>",
                    options_en: ["<p>Singular</p>", "<p>Stubborn</p>", 
                                "<p>Splendid</p>", "<p>Sensible</p>"],
                    options_hi: ["<p>Singular</p>", "<p>Stubborn</p>",
                                "<p>Splendid</p>", "<p>Sensible</p>"],
                    solution_en: "<p>99.(a)<br><strong>Singular </strong>- (of a word or form) denoting or referring to just one person or thing.<br><strong>Solitary </strong>- done or existing alone<br><strong>Stubborn </strong>- having or showing dogged determination not to change one\'s attitude or position on something, especially in spite of good arguments or reasons to do so.<br><strong>Splendid </strong>- magnificent; very impressive.<br><strong>Sensible </strong>- done or chosen in accordance with wisdom or prudence; likely to be of benefit.</p>",
                    solution_hi: "<p>99.(a)<br><strong>Singular </strong>(एकवचन) - (of a word or form) denoting or referring to just one person or thing.<br><strong>Solitary </strong>(एकाकी/अकेला) - done or existing alone<br><strong>Stubborn</strong> (जिद्दी) - having or showing dogged determination not to change one\'s attitude or position on something, especially in spite of good arguments or reasons to do so.<br><strong>Splendid</strong> (शानदार) - magnificent; very impressive.<br><strong>Sensible</strong> (समझदार) - done or chosen in accordance with wisdom or prudence; likely to be of benefit.</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "100",
                    section: "misc",
                    question_en: "<p>100. Select the INCORRECTLY spelt word.</p>",
                    question_hi: "<p>100. Select the INCORRECTLY spelt word.</p>",
                    options_en: ["<p>Diliver</p>", "<p>Denial</p>", 
                                "<p>Decision</p>", "<p>Deter</p>"],
                    options_hi: ["<p>Diliver</p>", "<p>Denial</p>",
                                "<p>Decision</p>", "<p>Deter</p>"],
                    solution_en: "<p>100.(a) Diliver<br>&lsquo;Deliver&rsquo; is the correct spelling .</p>",
                    solution_hi: "<p>100.(a) Diliver<br>&lsquo;Deliver&rsquo; सही spelling है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>