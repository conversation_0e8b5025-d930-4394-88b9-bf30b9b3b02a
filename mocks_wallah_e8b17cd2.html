<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">60:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 100</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">100</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 60 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["6"] = {
                name: "Reasoning",
                start: 0,
                end: 23
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="6">Reasoning</option>`;
            sections["18"] = {
                name: "General Awareness",
                start: 24,
                end: 48
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="18">General Awareness</option>`;
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 49,
                end: 73
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["7"] = {
                name: "English",
                start: 74,
                end: 98
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="7">English</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 99,
                end: 99
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "6",
                    question_en: "<p>1. HFLI is related to OMSP in a certain way based on the English alphabetical order. In the same way, KIOL is related to RPVS. To which of the following is MKQN related, following the same logic?</p>",
                    question_hi: "<p>1. अंग्रेजी वर्णमाला क्रम के आधार पर HFLI एक निश्चित प्रकार से OMSP से संबंधित है। ठीक उसी प्रकार KIOL, RPVS से संबंधित है। समान तर्क का अनुसरण करते हुए MKQN निम्नलिखित में से किससे संबंधित है?</p>",
                    options_en: ["<p>TRUX</p>", "<p>RTXU</p>", 
                                "<p>RTUX</p>", "<p>TRXU</p>"],
                    options_hi: ["<p>TRUX</p>", "<p>RTXU</p>",
                                "<p>RTUX</p>", "<p>TRXU</p>"],
                    solution_en: "<p>1.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773735578.png\" alt=\"rId4\" width=\"158\" height=\"99\">&nbsp; &nbsp;,&nbsp; &nbsp;<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773735684.png\" alt=\"rId5\" width=\"170\" height=\"100\"><br>Similarly,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773735783.png\" alt=\"rId6\" width=\"166\" height=\"100\"></p>",
                    solution_hi: "<p>1.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773735578.png\" alt=\"rId4\" width=\"158\" height=\"99\">&nbsp; &nbsp;,&nbsp; &nbsp;<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773735684.png\" alt=\"rId5\" width=\"170\" height=\"100\"><br>इसी प्रकार,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773735783.png\" alt=\"rId6\" width=\"166\" height=\"100\"></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "6",
                    question_en: "<p>2. Which of the following terms will replace the question mark (?) in the given series? <br>MFT, NIS, PLQ, SON, WRJ, ?</p>",
                    question_hi: "<p>2. निम्नलिखित में से कौन-सा पद दी गई श्रृंखला में प्रश्&zwj;न चिह्न (?) का स्थान लेगा ?<br>MFT, NIS, PLQ, SON, WRJ, ?</p>",
                    options_en: ["<p>RUE</p>", "<p>BUE</p>", 
                                "<p>BVD</p>", "<p>BUP</p>"],
                    options_hi: ["<p>RUE</p>", "<p>BUE</p>",
                                "<p>BVD</p>", "<p>BUP</p>"],
                    solution_en: "<p>2.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773735934.png\" alt=\"rId7\" width=\"353\" height=\"120\"></p>",
                    solution_hi: "<p>2.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773735934.png\" alt=\"rId7\" width=\"353\" height=\"120\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "6",
                    question_en: "<p>3. In a certain code language,<br>A + B means &lsquo;A is the wife of B&rsquo;.<br>A &minus; B means &lsquo;A is the son of B&rsquo;.<br>A &times; B means &lsquo;A is the husband of B&rsquo;.<br>A &divide; B means &lsquo;A is the father of B&rsquo;.<br>Based on the above, how is P related to T if &lsquo;P &minus; Q + R &divide; S &times; T&rsquo;?</p>",
                    question_hi: "<p>3. एक निश्चित कूट भाषा में,<br>A + B का अर्थ है कि \'A, B की पत्नी है\'।<br>A &minus; B का अर्थ है कि \'A, B का पुत्र है\'।<br>A &times; B का अर्थ है कि \'A, B का पति है\'।<br>A &divide; B का अर्थ है कि \'A, B का पिता है\'।<br>उपर्युक्त के आधार पर, यदि \'P &minus; Q + R &divide; S &times; T\' है, तो P का T से क्&zwj;या संबंध है?</p>",
                    options_en: ["<p>Son&rsquo;s son</p>", "<p>Father</p>", 
                                "<p>Husband&rsquo;s brother</p>", "<p>Father-in-law</p>"],
                    options_hi: ["<p>पौत्र</p>", "<p>पिता</p>",
                                "<p>देवर</p>", "<p>ससुर</p>"],
                    solution_en: "<p>3.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773736140.png\" alt=\"rId8\" width=\"139\" height=\"83\"><br>P is the brother of T&rsquo;s husband.</p>",
                    solution_hi: "<p>3.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773736140.png\" alt=\"rId8\" width=\"139\" height=\"83\"><br>P, T के पति का भाई (देवर) है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "6",
                    question_en: "<p>4. In a certain code language, &lsquo;FACED&rsquo; is written as &lsquo;GZDDE&rsquo; and &lsquo;VACAY&rsquo; is written as &lsquo;WZDZZ&rsquo;. How will &lsquo;LABOR&rsquo; be written in that language?</p>",
                    question_hi: "<p>4. एक निश्चित कोड कूट में, \'FACED\' को \'GZDDE\' के रूप में लिखा जाता है और \'VACAY\' को \'WZDZZ\' के रूप में लिखा जाता है। उसी भाषा में &lsquo;LABOR&rsquo; को किस प्रकार लिखा जाएगा?</p>",
                    options_en: ["<p>MZCNS</p>", "<p>MBCPS</p>", 
                                "<p>MBDPS</p>", "<p>MADMS</p>"],
                    options_hi: ["<p>MZCNS</p>", "<p>MBCPS</p>",
                                "<p>MBDPS</p>", "<p>MADMS</p>"],
                    solution_en: "<p>4.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773736278.png\" alt=\"rId9\" width=\"172\" height=\"105\">&nbsp; &nbsp;,&nbsp; &nbsp;<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773736392.png\" alt=\"rId10\" width=\"184\" height=\"101\"><br>Similarly,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773736529.png\" alt=\"rId11\" width=\"175\" height=\"92\"></p>",
                    solution_hi: "<p>4.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773736278.png\" alt=\"rId9\" width=\"172\" height=\"105\">&nbsp; &nbsp;,&nbsp; &nbsp;<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773736392.png\" alt=\"rId10\" width=\"184\" height=\"101\"><br>इसी प्रकार,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773736529.png\" alt=\"rId11\" width=\"175\" height=\"92\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "6",
                    question_en: "<p>5. Based on the position in the English alphabetical order, three of the following letter-clusters are alike in some manner and one is different. Select the odd letter-cluster.&nbsp;Note: The odd one out is not based on the number of consonants/vowels or their position in the letter cluster.</p>",
                    question_hi: "<p>5. अंग्रेजी वर्णमाला क्रम में स्थिति के आधार पर, निम्नलिखित में से तीन अक्षर-समूह किसी न किसी रूप में संगत हैं और एक असंगत है। असंगत अक्षर- समूह का चयन करें।&nbsp;(नोटः असंगत, अक्षर-समूह में व्यंजनों/स्वरों की संख्या या उनकी स्थिति पर आधारित नहीं है।)</p>",
                    options_en: ["<p>SUZY</p>", "<p>MPUT</p>", 
                                "<p>QSXW</p>", "<p>PRWV</p>"],
                    options_hi: ["<p>SUZY</p>", "<p>MPUT</p>",
                                "<p>QSXW</p>", "<p>PRWV</p>"],
                    solution_en: "<p>5.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773736686.png\" alt=\"rId12\" width=\"219\" height=\"51\">&nbsp; &nbsp;,&nbsp; &nbsp;<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773736819.png\" alt=\"rId13\" width=\"218\" height=\"50\">&nbsp; &nbsp;, <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773736931.png\" alt=\"rId14\" width=\"215\" height=\"50\"><br>but, <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773737063.png\" alt=\"rId15\" width=\"214\" height=\"50\"></p>",
                    solution_hi: "<p>5.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773736686.png\" alt=\"rId12\" width=\"219\" height=\"51\">&nbsp; &nbsp;,&nbsp; &nbsp;<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773736819.png\" alt=\"rId13\" width=\"218\" height=\"50\">&nbsp; &nbsp;, <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773736931.png\" alt=\"rId14\" width=\"215\" height=\"50\"><br>लेकिन,&nbsp;<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773737063.png\" alt=\"rId15\" width=\"214\" height=\"50\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "6",
                    question_en: "<p>6. Six students - Aman, Bobby, Chintu, Gita, Pranay and Ram - are sitting around a circular table, facing the centre.Ram is sitting second to the right of Gita.Pranay is the immediate neighbour of Gita and Bobby.Aman is sitting third to the right of Gita. Who among the following is the immediate neighbour of both Ram and Bobby?</p>",
                    question_hi: "<p>6. छह विद्यार्थी- अमन, बॉबी, चिंटू, गीता, प्रणय और राम - एक गोल मेज के परितः केंद्र की ओर अभिमुख होकर बैठे हैं।राम, गीता के दायें से दूसरे स्थान पर बैठा है।गीता और बॉबी का निकटतम पड़ोसी प्रणय है।अमन, गीता के दायें से तीसरे स्थान पर बैठा है।निम्नलिखित में से कौन राम और बॉबी दोनों का निकटतम पड़ोसी है?</p>",
                    options_en: ["<p>Pranay</p>", "<p>Aman</p>", 
                                "<p>Gita</p>", "<p>Chintu</p>"],
                    options_hi: ["<p>प्रणय</p>", "<p>अमन</p>",
                                "<p>गीता</p>", "<p>चिंटू</p>"],
                    solution_en: "<p>6.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773737208.png\" alt=\"rId16\" width=\"178\" height=\"104\"><br>Aman is the neighbour of Bobby and Ram.</p>",
                    solution_hi: "<p>6.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773737300.png\" alt=\"rId17\" width=\"157\" height=\"107\"><br>अमन, बॉबी और राम का पड़ोसी है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "6",
                    question_en: "<p>7. Based on the English alphabetical order, three of the following four letter-clusters are alike in a certain way and thus form a group. Which is the one that DOES NOT belong to that group?&nbsp;(Note : The odd man out is not based on the number of consonants/vowels or their position in the letter-cluster.)</p>",
                    question_hi: "<p>7. अंग्रेजी वर्णमाला क्रम के आधार पर, निम्नलिखित चार अक्षर-समूहों में से तीन किसी निश्चित तरीके से एकसमान हैं और इस प्रकार एक समूह बनाते हैं। कौन-सा अक्षर-समूह उस समूह से संबंधित नहीं है?&nbsp;(नोट : असंगत अक्षर-समूह, व्यंजनों/स्वरों की संख्या या अक्षर-समूह में इनके स्थान पर आधारित नहीं है।)</p>",
                    options_en: ["<p>KJE</p>", "<p>JHD</p>", 
                                "<p>FDZ</p>", "<p>AYU</p>"],
                    options_hi: ["<p>KJE</p>", "<p>JHD</p>",
                                "<p>FDZ</p>", "<p>AYU</p>"],
                    solution_en: "<p>7.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773737407.png\" alt=\"rId18\" width=\"123\" height=\"60\">&nbsp; &nbsp;,&nbsp; &nbsp;<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773737511.png\" alt=\"rId19\" width=\"136\" height=\"60\">&nbsp; &nbsp;,&nbsp; &nbsp;<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773737604.png\" alt=\"rId20\" width=\"140\" height=\"60\"><br>But,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773737720.png\" alt=\"rId21\" width=\"124\" height=\"60\"></p>",
                    solution_hi: "<p>7.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773737407.png\" alt=\"rId18\" width=\"123\" height=\"60\">&nbsp; &nbsp;,&nbsp; &nbsp;<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773737511.png\" alt=\"rId19\" width=\"136\" height=\"60\">&nbsp; &nbsp;,&nbsp; &nbsp;<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773737604.png\" alt=\"rId20\" width=\"140\" height=\"60\"><br>लेकिन,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773737720.png\" alt=\"rId21\" width=\"124\" height=\"60\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "6",
                    question_en: "<p>8. Three statements are given followed by three conclusions numbered I, II and III. Assuming the statements to be true, even if they seem to be at variance with commonly known facts, decide which of the conclusions logically follow(s) from the statements.<br><strong>Statements :</strong> <br>Some tears are drops. <br>Some drops are streams. <br>All streams are rivers.<br><strong>Conclusions :</strong> <br>I. All tears can never be rivers. <br>II. Some drops are rivers. <br>III. All tears being streams is a possibility.</p>",
                    question_hi: "<p>8. तीन कथन और उसके बाद तीन निष्कर्ष I, II और III दिए गए हैं। कथनों को सत्य मानते हुए, भले ही वे सामान्य रूप से ज्ञात तथ्यों से भिन्न प्रतीत होते हों, तय कीजिए कि कौन-सा/से निष्कर्ष इन कथनों का तार्किक रूप से अनुसरण करता है/करते हैं। <br><strong>कथन :</strong> <br>कुछ आंसू, बूंदें हैं। <br>कुछ बूंदें, धाराएं हैं। <br>सभी धाराएं, नदियां हैं। <br><strong>निष्कर्ष :</strong> <br>I. सभी आंसू कभी भी नदियां नहीं हो सकते हैं। <br>II. कुछ बूंदें, नदियां हैं। <br>III. सभी आंसुओं के धाराएं होने की संभावना है।</p>",
                    options_en: ["<p>Both I and II conclusion follow</p>", "<p>Only conclusion I follows</p>", 
                                "<p>Only conclusion III follows</p>", "<p>Both II and III conclusion follow</p>"],
                    options_hi: ["<p>निष्&zwj;कर्ष I और II दोनों अनुसरण करते है</p>", "<p>केवल निष्&zwj;कर्ष I अनुसरण करता है</p>",
                                "<p>केवल निष्&zwj;कर्ष III अनुसरण करता<strong> </strong>है</p>", "<p>निष्&zwj;कर्ष II और III दोनों अनुसरण करते है</p>"],
                    solution_en: "<p>8.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773737824.png\" alt=\"rId22\" width=\"245\" height=\"64\"><br>Both II and III conclusion follow.</p>",
                    solution_hi: "<p>8.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773737958.png\" alt=\"rId23\" width=\"232\" height=\"64\"><br>निष्कर्ष II और III दोनों अनुसरण करते हैं।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "6",
                    question_en: "<p>9. &lsquo;Blossom&rsquo; is related to &lsquo;Wither&rsquo; in the same way as &lsquo;Stagnate&rsquo; is related to &lsquo;________&rsquo;.<br>(The words must be considered as meaningful English words and must not be related to each other based on the number of letters/number of consonants/vowels in the word.)</p>",
                    question_hi: "<p>9. \'खिलना\' (Blossom),\'मुरझाना\' (Wither) से उसी प्रकार संबंधित है, जिस प्रकार \'स्थिर होना\' (Stagnate) \'________\' से संबंधित है।&nbsp;<br>(शब्दों को सार्थक हिंदी शब्द माना जाना चाहिए और शब्&zwj;दों को अक्षरों की संख्या/व्यंजनों/नोंस्वरों की संख्या के आधार पर एक दूसरे से संबंधित नहीं किया जाना चाहिए।</p>",
                    options_en: ["<p>Stand</p>", "<p>Rest</p>", 
                                "<p>Languish</p>", "<p>Flow</p>"],
                    options_hi: ["<p>खड़े रहना (Stand)</p>", "<p>विराम (Rest)</p>",
                                "<p>मंद पड़ना (Languish)</p>", "<p>बहना (Flow)</p>"],
                    solution_en: "<p>9.(d) As &lsquo;Blossom&rsquo; and &lsquo;Wither&rsquo; are opposite of each other similarly, &lsquo;Stagnate&rsquo; and &lsquo;Flow&rsquo; are opposite of each other.</p>",
                    solution_hi: "<p>9.(d) जैसे \'खिलना\' और \'मुरझाना\' एक दूसरे के विपरीत हैं, उसी प्रकार \'स्थिर\' और \'बहना\' एक दूसरे के विपरीत हैं।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "6",
                    question_en: "<p>10. In a code language, \'BASIC\' is coded as 101 and \'MAGIC\' is coded as 102. How will \'LIGHT\' be coded in the same language?</p>",
                    question_hi: "<p>10. एक कूट भाषा में, \'BASIC\' को 101 के रूप में कूटबद्ध किया जाता है और \'MAGIC\' को 102 के रूप में कूटबद्ध किया जाता है। उसी भाषा में \'LIGHT\' को किस प्रकार कूटबद्ध किया जाएगा?</p>",
                    options_en: ["<p>85</p>", "<p>79</p>", 
                                "<p>82</p>", "<p>86</p>"],
                    options_hi: ["<p>85</p>", "<p>79</p>",
                                "<p>82</p>", "<p>86</p>"],
                    solution_en: "<p>10.(b) <strong>Logic :-</strong> (Sum of the place value of opposite letter)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773738090.png\" alt=\"rId24\" width=\"124\" height=\"115\">&nbsp; &nbsp;,&nbsp; &nbsp;<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773738181.png\" alt=\"rId25\" width=\"132\" height=\"115\"><br>Similarly,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773738276.png\" alt=\"rId26\" width=\"130\" height=\"115\"></p>",
                    solution_hi: "<p>10.(b) <strong>तर्क :-</strong> (विपरीत अक्षर के स्थानीय मान का योग)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773738366.png\" alt=\"rId27\" width=\"128\" height=\"115\">&nbsp; &nbsp;<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773738463.png\" alt=\"rId28\" width=\"130\" height=\"115\"><br>इसी प्रकार,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773738592.png\" alt=\"rId29\" width=\"125\" height=\"115\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "6",
                    question_en: "<p>11. Select the triad in which the numbers are related in the same way as are the numbers of the given triads.<br>(16, 33, 67), (19, 39, 79)&nbsp;(NOTE : Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g. 13 &ndash; Operations on 13 such as adding /deleting /multiplying etc. to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed)</p>",
                    question_hi: "<p>11. उस त्रिक का चयन कीजिए, जिसमें संख्याएँ एक-दूसरे से उसी तरह संबंधित हैं, जैसे दिए गए त्रिकों की संख्याएँ हैं।<br>(16, 33, 67), (19, 39, 79)&nbsp;(ध्यान दीजिए : संख्याओं को उनके घटक अंकों में विभाजित किए बिना, पूर्ण संख्याओं पर संक्रियाएँ की जानी चाहिए। उदाहरण के लिए 13 - 13 पर संक्रिया जैसे जोड़ना / घटाना / गुणा करना आदि 13 पर किया जा सकता है। 13 को 1 और 3 में तोड़ना और फिर 1 और 3 पर गणितीय संक्रियाएँ करने की अनुमति नहीं है)</p>",
                    options_en: ["<p>(22, 45, 91)</p>", "<p>(21, 45, 90)</p>", 
                                "<p>(18, 36, 80)</p>", "<p>(14, 27, 53)</p>"],
                    options_hi: ["<p>(22, 45, 91)</p>", "<p>(21, 45, 90)</p>",
                                "<p>(18, 36, 80)</p>", "<p>(14, 27, 53)</p>"],
                    solution_en: "<p>11.(a) <strong>Logic:- </strong>(3rd number - 2nd number)<math display=\"inline\"><mo>&#247;</mo></math> 2 - 1 = 1st number<br>(16, 33, 67):- (67 -33)<math display=\"inline\"><mo>&#247;</mo></math>2 -1 &rArr; (34) &divide; 2 - 1 &rArr; (17) - 1 = 16&nbsp;<br>(19, 39, 79):- (79 - 39)<math display=\"inline\"><mo>&#247;</mo></math>2 -1 &rArr; (40) &divide; 2 -1 &rArr; (20) - 1 = 19<br>Similarly,<br>(22, 45, 91):- (91 - 45)<math display=\"inline\"><mo>&#247;</mo></math>2 -1 &rArr; (46) &divide; 2 -1 &rArr; (23) - 1 = 22</p>",
                    solution_hi: "<p>11.(a) <strong>तर्क: </strong>(तीसरी संख्या - दूसरी संख्या) <math display=\"inline\"><mo>&#247;</mo></math> 2 - 1 = पहली संख्या<br>(16, 33, 67):- (67 -33)<math display=\"inline\"><mo>&#247;</mo></math>2 -1 &rArr; (34) &divide; 2 - 1 &rArr; (17) - 1 = 16&nbsp;<br>(19, 39, 79):- (79 - 39)<math display=\"inline\"><mo>&#247;</mo></math>2 -1 &rArr; (40) &divide; 2 -1 &rArr; (20) - 1 = 19<br>इसी प्रकार,<br>(22, 45, 91):- (91 - 45)<math display=\"inline\"><mo>&#247;</mo></math>2 -1 &rArr; (46) &divide; 2 -1 &rArr; (23) - 1 = 22</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "6",
                    question_en: "<p>12. The sequence of folding a paper and the manner in which the folded paper is cut is shown in the following figures. How would this paper look when unfolded?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773738747.png\" alt=\"rId30\" width=\"216\" height=\"113\"></p>",
                    question_hi: "<p>12. निम्नलिखित आकृतियों में एक कागज़ को मोड़ने का क्रम और मुड़े हुए कागज़ को काटने का तरीका दर्शाया गया है। खोले जाने पर यह कागज़ कैसा दिखाई देगा?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773738747.png\" alt=\"rId30\" width=\"216\" height=\"113\"></p>",
                    options_en: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773738862.png\" alt=\"rId31\" width=\"93\" height=\"130\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773739002.png\" alt=\"rId32\" width=\"82\" height=\"133\"></p>", 
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773739098.png\" alt=\"rId33\" width=\"81\" height=\"130\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773739231.png\" alt=\"rId34\" width=\"81\" height=\"129\"></p>"],
                    options_hi: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773738862.png\" alt=\"rId31\" width=\"94\" height=\"131\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773739002.png\" alt=\"rId32\" width=\"82\" height=\"133\"></p>",
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773739098.png\" alt=\"rId33\" width=\"81\" height=\"130\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773739231.png\" alt=\"rId34\" width=\"83\" height=\"132\"></p>"],
                    solution_en: "<p>12.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773739231.png\" alt=\"rId34\" width=\"82\" height=\"131\"></p>",
                    solution_hi: "<p>12.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773739231.png\" alt=\"rId34\" width=\"82\" height=\"131\"></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "6",
                    question_en: "<p>13. In a certain code language,<br>M &amp; N means &lsquo;M is the husband of N&rsquo;,<br>M @ N means &lsquo;M is the brother of N&rsquo;,<br>M $ N means &lsquo;M is the father of N&rsquo;,<br>M # N means &lsquo;M is the mother of N&rsquo;.<br>Based on the above, how is F related to J if &lsquo;F &amp; G # H @ I $ J&rsquo; ?</p>",
                    question_hi: "<p>13. एक निश्चित कूट भाषा में,<br>M &amp; N का अर्थ है &lsquo;M, N का पति है&rsquo;,<br>M @ N का अर्थ है &lsquo;M, N का भाई है&rsquo;,<br>M $ N का अर्थ है &lsquo;M, N का पिता है&rsquo;<br>M # N का अर्थ है \' M, N की माता है&rsquo;।<br>उपरोक्त के आधार पर, यदि &lsquo;F &amp; G # H @ I $ J&rsquo; है तो F, J से किस प्रकार संबंधित है?</p>",
                    options_en: ["<p>Mother&rsquo;s father</p>", "<p>Father&rsquo;s father</p>", 
                                "<p>Mother</p>", "<p>Sister</p>"],
                    options_hi: ["<p>नाना</p>", "<p>दादा</p>",
                                "<p>माता</p>", "<p>बहन</p>"],
                    solution_en: "<p>13.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773739329.png\" alt=\"rId35\" width=\"144\" height=\"170\"><br>F is the father&rsquo;s father of J.</p>",
                    solution_hi: "<p>13.(b) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773739329.png\" alt=\"rId35\" width=\"144\" height=\"170\"><br>F, J के दादा है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "6",
                    question_en: "<p>14. In a code language, &lsquo;CRAB&rsquo; is written as &lsquo;YTWD&rsquo;, and &lsquo;CALM&rsquo; is written as &lsquo;YCHO&rsquo;. How will &lsquo;ZOOM&rsquo; be written in that language?</p>",
                    question_hi: "<p>14. एक कूट भाषा में &lsquo;CRAB&rsquo; को &lsquo;YTWD&rsquo; के रूप में लिखा जाता है और &lsquo;CALM&rsquo; को &lsquo;YCHO&rsquo; के रूप में लिखा जाता है। इसी कूट भाषा में &lsquo;ZOOM&rsquo; को कैसे लिखा जाएगा?</p>",
                    options_en: ["<p>KOVQ</p>", "<p>VQKO</p>", 
                                "<p>VKOQ</p>", "<p>KVOQ</p>"],
                    options_hi: ["<p>KOVQ</p>", "<p>VQKO</p>",
                                "<p>VKOQ</p>", "<p>KVOQ</p>"],
                    solution_en: "<p>14.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773739483.png\" alt=\"rId36\" width=\"88\" height=\"69\">&nbsp; &nbsp;,&nbsp; &nbsp;<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773739605.png\" alt=\"rId37\" width=\"91\" height=\"70\"><br>Similarly, <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773739708.png\" alt=\"rId38\" width=\"93\" height=\"72\"></p>",
                    solution_hi: "<p>14.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773739483.png\" alt=\"rId36\" width=\"88\" height=\"69\">&nbsp; &nbsp;,&nbsp; &nbsp;<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773739605.png\" alt=\"rId37\" width=\"91\" height=\"70\"><br>इसी प्रकार,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773739708.png\" alt=\"rId38\" width=\"93\" height=\"72\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "6",
                    question_en: "<p>15. 104 is related to 13 following a certain logic. Following the same logic, 136 is related to 17. To which of the following is 232 related, following the same logic? (NOTE : Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g. 13 - Operations on 13 such as adding /deleting /multiplying etc. to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed)</p>",
                    question_hi: "<p>15. एक निश्चित तर्क का अनुसरण करते हुए 104, 13 से संबंधित है। उसी तर्क का अनुसरण करते हुए, 136, 17 से संबंधित है। उसी तर्क का अनुसरण करते हुए, 232 निम्नलिखित में से किससे संबंधित है?&nbsp;(नोट : संख्याओं को उसके घटक अंकों में तोड़े बिना, पूर्ण संख्याओं पर संक्रियाएं की जानी चाहिए। उदाहरण के लिए संख्या 13 को लें - 13 पर संक्रियाएं, जैसे कि 13 में जोड़ना/घटाना/गुणा करना आदि, की जा सकती हैं। 13 को 1 और 3 में तोड़ने और फिर 1 और 3 पर गणितीय संक्रियाएं करने की अनुमति नहीं है)</p>",
                    options_en: ["<p>29</p>", "<p>31</p>", 
                                "<p>33</p>", "<p>27</p>"],
                    options_hi: ["<p>29</p>", "<p>31</p>",
                                "<p>33</p>", "<p>27</p>"],
                    solution_en: "<p>15.(a) <strong>Logic:</strong> small number &times; 8 = large number<br>13 &times; 8 = 104<br>17 &times; 8 = 136<br>Similarly,<br><math display=\"inline\"><mi>x</mi></math> &times; 8 = 232 &rArr; x = 29</p>",
                    solution_hi: "<p>15.(a) <strong>तर्क: </strong>छोटी संख्या &times; 8 = बड़ी संख्या<br>13 &times; 8 = 104<br>17 &times; 8 = 136<br>उसीप्रकार,<br><math display=\"inline\"><mi>x</mi></math> &times; 8 = 232 &rArr; x = 29</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "6",
                    question_en: "<p>16. Select the combination of letters that when sequentially placed in the blanks of the given series will logically complete the series.<br>BYN_J_YNF_X_NFJVY_FJ</p>",
                    question_hi: "<p>16. अक्षरों के उस संयोजन का चयन कीजिए, जिसे दी गई श्रृंखला के रिक्त स्थानों में क्रमिक रूप से रखे जाने पर श्रृंखला तार्किक रूप से पूरी हो जाएगी।<br>BYN_J_YNF_X_NFJVY_FJ</p>",
                    options_en: ["<p>FZJYN</p>", "<p>FNJYV</p>", 
                                "<p>FZYVJ</p>", "<p>FJZNY</p>"],
                    options_hi: ["<p>FZJYN</p>", "<p>FNJYV</p>",
                                "<p>FZYVJ</p>", "<p>FJZNY</p>"],
                    solution_en: "<p>16.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773739851.png\" alt=\"rId39\" width=\"257\" height=\"36\"></p>",
                    solution_hi: "<p>16.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773739851.png\" alt=\"rId39\" width=\"257\" height=\"36\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "6",
                    question_en: "<p>17. Which figure should replace the question mark (?) if the following series were to be continued?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773739960.png\" alt=\"rId40\" width=\"299\" height=\"70\"></p>",
                    question_hi: "<p>17. निम्नलिखित श्रृंखला को जारी रखने के लिए प्रश्न चिह्न (?) के स्थान पर कौन-सी आकृति आनी चाहिए?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773739960.png\" alt=\"rId40\" width=\"299\" height=\"70\"></p>",
                    options_en: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773740090.png\" alt=\"rId41\" width=\"74\" height=\"70\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773740203.png\" alt=\"rId42\" width=\"75\" height=\"70\"></p>", 
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773740284.png\" alt=\"rId43\" width=\"71\" height=\"68\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773740387.png\" alt=\"rId44\" width=\"76\" height=\"72\"></p>"],
                    options_hi: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773740090.png\" alt=\"rId41\" width=\"74\" height=\"70\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773740203.png\" alt=\"rId42\" width=\"75\" height=\"70\"></p>",
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773740284.png\" alt=\"rId43\" width=\"73\" height=\"70\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773740387.png\" alt=\"rId44\" width=\"74\" height=\"70\"></p>"],
                    solution_en: "<p>17.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773740203.png\" alt=\"rId42\" width=\"80\" height=\"75\"></p>",
                    solution_hi: "<p>17.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773740203.png\" alt=\"rId42\" width=\"80\" height=\"75\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "6",
                    question_en: "<p>18. Three statements are given, followed by three conclusions numbered I, II and III. Assuming the statements to be true, even if they seem to be at variance with commonly known facts, decide which of the conclusions logically follow(s) from the statements.<br><strong>Statements :</strong><br>All bottles are jugs.<br>Some bottles are flasks.<br>All flasks are bins.<br><strong>Conclusions :</strong><br>I. Some bottles are bins.<br>II. Some jugs are bins.<br>III. Some flasks are jugs.</p>",
                    question_hi: "<p>18. तीन कथन दिए गए हैं, जिसके बाद तीन निष्कर्ष I, II और III दिए गए हैं। कथनों को सत्य मानते हुए, भले ही वे सामान्य रूप से ज्ञात तथ्यों से भिन्न प्रतीत होते हों, निर्धारित करें कि कौन सा/से निष्कर्ष कथनों का तार्किक रूप से अनुसरण करता/करते है/हैं।<br><strong>कथन :</strong><br>सभी बोतलें, जग हैं।<br>कुछ बोतलें, फ्लास्&zwj;क हैं।<br>सभी फ्लास्&zwj;क, डब्बे (Bins) हैं।<br><strong>निष्&zwj;कर्ष :</strong><br>I. कुछ बोतलें, डब्बे (Bins) हैं।<br>II. कुछ जग, डब्बे (Bins) हैं।<br>III. कुछ फ्लास्&zwj;क, जग हैं।</p>",
                    options_en: ["<p>All conclusions I, II and III follow</p>", "<p>Either conclusion I or II follows</p>", 
                                "<p>Only conclusions II and III follow</p>", "<p>Only conclusions I and II follow</p>"],
                    options_hi: ["<p>निष्कर्ष I, II और III सभी अनुसरण करते हैं</p>", "<p>या तो निष्कर्ष I अनुसरण करता है या II अनुसरण करता है</p>",
                                "<p>केवल निष्कर्ष II और III अनुसरण करते हैं</p>", "<p>केवल निष्कर्ष I और II अनुसरण करते हैं</p>"],
                    solution_en: "<p>18.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773740460.png\" alt=\"rId45\" width=\"226\" height=\"100\"><br>All conclusions I, II and III follow.</p>",
                    solution_hi: "<p>18.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773740634.png\" alt=\"rId46\" width=\"212\" height=\"100\"><br>निष्कर्ष I, II और III सभी अनुसरण करते हैं.</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "6",
                    question_en: "<p>19. Which of the four options will replace the question mark (?) in the following series? <br>UE 88, VG 84, WI 80, XK76, ?</p>",
                    question_hi: "<p>19. चार विकल्पों में से कौन सा विकल्प निम्नलिखित श्रृंखला में प्रश्न चिह्न (?) के स्&zwj;थान पर आएगा?<br>UE 88, VG 84, WI 80, XK76, ?</p>",
                    options_en: ["<p>YN73</p>", "<p>YM72</p>", 
                                "<p>ZN71</p>", "<p>ZM72</p>"],
                    options_hi: ["<p>YN73</p>", "<p>YM72</p>",
                                "<p>ZN71</p>", "<p>ZM72</p>"],
                    solution_en: "<p>19.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773740746.png\" alt=\"rId47\" width=\"300\" height=\"90\"></p>",
                    solution_hi: "<p>19.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773740746.png\" alt=\"rId47\" width=\"300\" height=\"90\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "6",
                    question_en: "<p>20. In a code language, \'KIRAT\' is coded as 50-40-85-0-95 and \'WASIM\' is coded as 110-0-90-40-60. How will \'MRINAL\' be coded in the same language?</p>",
                    question_hi: "<p>20. एक कूट भाषा में, \'KIRAT\' को 50-40-85-0-95 के रूप में कूटबद्ध किया जाता है और \'WASIM\' को 110-0-90-40-60 के रूप में कूटबद्ध किया जाता है। उसी भाषा में \'MRINAL\' को किस प्रकार कूटबद्ध किया जाएगा?</p>",
                    options_en: ["<p>85-40-50-75-0-65</p>", "<p>75-50-60-55-0-65</p>", 
                                "<p>60-85-40-65-0-55</p>", "<p>55-60-45-65-0-85</p>"],
                    options_hi: ["<p>85-40-50-75-0-65</p>", "<p>75-50-60-55-0-65</p>",
                                "<p>60-85-40-65-0-55</p>", "<p>55-60-45-65-0-85</p>"],
                    solution_en: "<p>20.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773740917.png\" alt=\"rId48\" width=\"172\" height=\"100\">&nbsp; &nbsp;,&nbsp; &nbsp;<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773741020.png\" alt=\"rId49\" width=\"172\" height=\"100\"><br>Similarly,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773741134.png\" alt=\"rId50\" width=\"208\" height=\"100\"></p>",
                    solution_hi: "<p>20.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773740917.png\" alt=\"rId48\" width=\"172\" height=\"100\">&nbsp; &nbsp;,&nbsp; &nbsp;<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773741020.png\" alt=\"rId49\" width=\"172\" height=\"100\"><br>इसी प्रकार,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773741134.png\" alt=\"rId50\" width=\"208\" height=\"100\"></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "6",
                    question_en: "<p>21. Select the correct mirror image of the given figure when the mirror is placed at MN as shown below.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773741229.png\" alt=\"rId51\" width=\"119\" height=\"120\"></p>",
                    question_hi: "<p>21. जब दर्पण को नीचे दर्शाए गए अनुसार MN पर रखा जाता है तो दी गई आकृति के सही दर्पण प्रतिबिंब का चयन कीजिए। <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773741229.png\" alt=\"rId51\" width=\"119\" height=\"120\"></p>",
                    options_en: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773741351.png\" alt=\"rId52\" width=\"105\" height=\"30\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773741438.png\" alt=\"rId53\" width=\"99\" height=\"30\"></p>", 
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773741524.png\" alt=\"rId54\" width=\"99\" height=\"30\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773741651.png\" alt=\"rId55\" width=\"102\" height=\"30\"></p>"],
                    options_hi: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773741351.png\" alt=\"rId52\" width=\"105\" height=\"30\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773741438.png\" alt=\"rId53\" width=\"99\" height=\"30\"></p>",
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773741524.png\" alt=\"rId54\" width=\"99\" height=\"30\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773741651.png\" alt=\"rId55\" width=\"102\" height=\"30\"></p>"],
                    solution_en: "<p>21.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773741651.png\" alt=\"rId55\" width=\"102\" height=\"30\"></p>",
                    solution_hi: "<p>21.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773741651.png\" alt=\"rId55\" width=\"102\" height=\"30\"></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "6",
                    question_en: "<p>22. Which of the following numbers will replace the question marks (?) in the given series?<br>4, ?, ?, 49, 121, 169, 289</p>",
                    question_hi: "<p>22. निम्नलिखित में से कौन-सी संख्याएँ दी गई शृंखला में प्रश्न-चिह्न(?) का स्थान लेंगी?<br>4, ?, ?, 49, 121, 169, 289</p>",
                    options_en: ["<p>9, 16</p>", "<p>9, 25</p>", 
                                "<p>3, 9</p>", "<p>16, 25</p>"],
                    options_hi: ["<p>9, 16</p>", "<p>9, 25</p>",
                                "<p>3, 9</p>", "<p>16, 25</p>"],
                    solution_en: "<p>22.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773741747.png\" alt=\"rId56\" width=\"348\" height=\"80\"></p>",
                    solution_hi: "<p>22.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773741747.png\" alt=\"rId56\" width=\"348\" height=\"80\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "23",
                    section: "6",
                    question_en: "<p>23. Three statements are given followed by three conclusions numbered I, II and III. Assuming the statements to be true, even if they seem to be at variance with commonly known facts, decide which of the conclusions logically follow(s) from the statements.<br><strong>Statements :</strong><br>All cards are postcards.<br>Some postcards are books.<br>All books are novels.<br><strong>Conclusions :</strong><br>I. Some postcards are novels.<br>II. No card is a book.<br>III. All cards are novels.</p>",
                    question_hi: "<p>23. तीन कथन और उसके बाद तीन निष्कर्ष I, II और III दिए गए हैं। कथनों को सत्य मानते हुए, भले ही वे सामान्य रूप से ज्ञात तथ्यों से भिन्न प्रतीत होते हों, तय कीजिए कि कौन-सा/से निष्कर्ष इन कथनों का तार्किक रूप से अनुसरण करता है/करते हैं।<br><strong>कथन :</strong><br>सभी कार्ड, पोस्टकार्ड हैं।<br>कुछ पोस्टकार्ड, किताबें हैं।<br>सभी किताबें, उपन्यास हैं।<br><strong>निष्कर्ष :</strong><br>I. कुछ पोस्टकार्ड, उपन्यास हैं।<br>II. कोई भी कार्ड, किताब नहीं है।<br>III. सभी कार्ड, उपन्यास हैं।</p>",
                    options_en: ["<p>Only II and III conclusion follow</p>", "<p>Only conclusion III follows</p>", 
                                "<p>Only I and II conclusion follow</p>", "<p>Only conclusion I follows</p>"],
                    options_hi: ["<p>केवल II और III निष्कर्ष अनुसरण करते हैं</p>", "<p>केवल निष्कर्ष III अनुसरण करता है</p>",
                                "<p>केवल I और II निष्कर्ष अनुसरण करते हैं</p>", "<p>केवल निष्कर्ष I अनुसरण करता है</p>"],
                    solution_en: "<p>23.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773741908.png\" alt=\"rId57\" width=\"233\" height=\"90\"><br>Only conclusion I follows</p>",
                    solution_hi: "<p>23.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773742133.png\" alt=\"rId58\" width=\"232\" height=\"90\"><br>केवल निष्कर्ष I अनुसरण करता है</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "24",
                    section: "6",
                    question_en: "<p>24. Select the option figure in which the given figure (X) is embedded (rotation is NOT allowed).<br><img src=\"data:image/png;base64,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\" width=\"92\" height=\"99\"></p>",
                    question_hi: "<p>24. उस विकल्प आकृति का चयन करें जिसमें दी गई आकृति (X) सन्निहित है (घुमाने की अनुमति नहीं है)।<br><img src=\"data:image/png;base64,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\" width=\"92\" height=\"99\"></p>",
                    options_en: ["<p><img src=\"data:image/png;base64,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\" width=\"104\" height=\"101\"></p>", "<p><img src=\"data:image/png;base64,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\" width=\"104\" height=\"100\"></p>", 
                                "<p><img src=\"data:image/png;base64,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\" width=\"105\" height=\"100\"></p>", "<p><img src=\"data:image/png;base64,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\" width=\"105\" height=\"100\"></p>"],
                    options_hi: ["<p><img src=\"data:image/png;base64,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\" width=\"104\" height=\"101\"></p>", "<p><img src=\"data:image/png;base64,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\" width=\"104\" height=\"100\"></p>",
                                "<p><img src=\"data:image/png;base64,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\" width=\"105\" height=\"100\"></p>", "<p><img src=\"data:image/png;base64,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\" width=\"105\" height=\"100\"></p>"],
                    solution_en: "<p>24.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773743039.png\" alt=\"rId64\" width=\"97\" height=\"100\"></p>",
                    solution_hi: "<p>24.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773743039.png\" alt=\"rId64\" width=\"97\" height=\"100\"></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "25",
                    section: "18",
                    question_en: "<p>25. What will come in place of the question mark (?) in the following equation, if &lsquo;+&rsquo; and &lsquo;&ndash;&rsquo; are interchanged and &lsquo;&times;&rsquo; and &lsquo;&divide;&rsquo; are interchanged? <br>4515 &times; 5 &ndash; 431 &divide; 3 + 821 = ?</p>",
                    question_hi: "<p>25. यदि &lsquo;+&rsquo; और &lsquo;&ndash;&rsquo; को आपस में बदल दिया जाए और &lsquo;&times;&rsquo; और &lsquo;&divide;&rsquo; को आपस में बदल दिया जाए, तो निम्नलिखित समीकरण में प्रश्न चिह्न (?) के स्थान पर क्या आएगा? <br>4515 &times; 5 &ndash; 431 &divide; 3 + 821 = ?</p>",
                    options_en: ["<p>1575</p>", "<p>1335</p>", 
                                "<p>1375</p>", "<p>1775</p>"],
                    options_hi: ["<p>1575</p>", "<p>1335</p>",
                                "<p>1375</p>", "<p>1775</p>"],
                    solution_en: "<p>25.(c) <strong>Given:-</strong> 4515 &times; 5 - 431 <math display=\"inline\"><mo>&#247;</mo></math> 3 + 821<br>As per given instruction after interchanging &lsquo;+&rsquo; and &lsquo;-&rsquo; and &lsquo;&times;&rsquo; and &lsquo;<math display=\"inline\"><mo>&#247;</mo></math>&rsquo; we get,<br>4515 <math display=\"inline\"><mo>&#247;</mo></math> 5 + 431 &times; 3 - 821<br>903 + 1293 - 821<br>2196 - 821 = 1375</p>",
                    solution_hi: "<p>25.(c) <strong>दिया गया है:- </strong>4515 &times; 5 - 431<math display=\"inline\"><mo>&#247;</mo></math> 3 + 821<br>दिए गए निर्देश के अनुसार \'+\' और \'-\' तथा \'&times;\' और \'<math display=\"inline\"><mo>&#247;</mo></math>\' को आपस में बदलने के बाद हमें प्राप्त होता है,<br>4515 <math display=\"inline\"><mo>&#247;</mo></math> 5 + 431 &times; 3 - 821<br>903 + 1293 - 821<br>2196 - 821 = 1375</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "26",
                    section: "18",
                    question_en: "<p>26. Who was appointed as the Brand Ambassador and Honorary Tourism Advisor for Thailand in November 2024?</p>",
                    question_hi: "<p>26. नवंबर 2024 में थाईलैंड के लिए ब्रांड एंबेसडर और मानद पर्यटन सलाहकार के रूप में किसे नियुक्त किया गया था ?</p>",
                    options_en: ["<p>Akshay Kumar</p>", "<p>Salman Khan</p>", 
                                "<p>Sonu Sood</p>", "<p>Amitabh Bachchan</p>"],
                    options_hi: ["<p>अक्षय कुमार</p>", "<p>सलमान खान</p>",
                                "<p>सोनू सूद</p>", "<p>अमिताभ बच्चन</p>"],
                    solution_en: "<p>26.(c) <strong>Sonu Sood.</strong><br>Sonu Sood, renowned actor and philanthropist. Thailand is a Southeast Asian country with Bangkok as its capital. Known for its tropical beaches, vibrant culture, and delicious cuisine like Pad Thai and Tom Yum, it is a constitutional monarchy under King Maha Vajiralongkorn (Rama X).</p>",
                    solution_hi: "<p>26.(c) <strong>सोनू सूद।</strong><br>सोनू सूद, प्रसिद्ध अभिनेता और परोपकारी। थाईलैंड एक दक्षिण पूर्व एशियाई देश है जिसकी राजधानी बैंकॉक है। अपने उष्णकटिबंधीय समुद्र तटों, जीवंत संस्कृति और पैड थाई और टॉम यम जैसे स्वादिष्ट व्यंजनों के लिए जाना जाता है, यह राजा महा वजीरालोंगकोर्न (राम X) के अधीन एक संवैधानिक राजतंत्र है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "27",
                    section: "18",
                    question_en: "<p>27. The Vice-President of India is elected by the:</p>",
                    question_hi: "<p>27. भारत के उपराष्ट्रपति का चुनाव किसके द्वारा किया जाता है:</p>",
                    options_en: ["<p>members of the Lok Sabha.</p>", "<p>members of both houses of Parliament.</p>", 
                                "<p>members of the Rajya Sabha.</p>", "<p>member of the houses of parliament and state legislative assemblies.</p>"],
                    options_hi: ["<p>लोकसभा के सदस्य</p>", "<p>संसद के दोनों सदनों के सदस्य</p>",
                                "<p>राज्यसभा के सदस्य</p>", "<p>संसद और राज्य विधानसभाओं के सदनों के सदस्य</p>"],
                    solution_en: "<p>27.(b) The Vice-President of India is elected by the members of both houses of parliament in accordance with the system of proportional representation by means of single transferable vote. Article 63 of Indian Constitution states that \"There shall be a vice president of India.\" Jagdeep Dhankhar is the 14th Vice President of India.</p>",
                    solution_hi: "<p>27.(b) भारत के उपराष्ट्रपति का चुनाव संसद के दोनों सदनों के सदस्यों द्वारा आनुपातिक प्रतिनिधित्व प्रणाली के अनुसार एकल संक्रमणीय मत के माध्यम से किया जाता है। भारतीय संविधान के अनुच्छेद 63 में कहा गया है कि \"भारत का एक उपराष्ट्रपति होगा।\" जगदीप धनखड़ भारत के 14वें उपराष्ट्रपति हैं।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "28",
                    section: "18",
                    question_en: "<p>28. In optics, the refractive index of a substance is described by the formula&nbsp;ղ = c/v, where c is:</p>",
                    question_hi: "<p>28. प्रकाशिकी में, किसी पदार्थ का अपवर्तनांक सूचकांक, ղ = c/v से दर्शाया जाता है, जहाँ c निम्न है:</p>",
                    options_en: ["<p>the speed of light in medium</p>", "<p>the center of curvature</p>", 
                                "<p>the radius of the sphere</p>", "<p>the speed of light in vacuum</p>"],
                    options_hi: ["<p>माध्यम में प्रकाश की गति</p>", "<p>वक्रता केंद्र</p>",
                                "<p>गोले की त्रिज्या</p>", "<p>निर्वात में प्रकाश की गति</p>"],
                    solution_en: "<p>28.(d) In optics, the refractive index of a substance is described by the formula n = c/v, where c is <strong>the speed of light in vacuum (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn mathvariant=\"bold\">3</mn><mo mathvariant=\"bold\">&#215;</mo><msup><mn mathvariant=\"bold\">10</mn><mn mathvariant=\"bold\">8</mn></msup><mi mathvariant=\"bold\">m</mi><mo mathvariant=\"bold\">/</mo><mi mathvariant=\"bold\">s</mi></math>)</strong>. v is the phase velocity of light. Refractive Index- Air- 1.0003, Water-1.333, Diamond- 2.417, Ice- 1.31, Ethyl Alcohol- 1.36, Vacuum -1.</p>",
                    solution_hi: "<p>28.(d) प्रकाशिकी (optics) में, किसी पदार्थ का अपवर्तनांक सूचकांक (refractive index) ղ = c/v द्वारा वर्णित किया जाता है, जहाँ c निर्वात में प्रकाश की गति (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn mathvariant=\"bold\">3</mn><mo mathvariant=\"bold\">&#215;</mo><msup><mn mathvariant=\"bold\">10</mn><mn mathvariant=\"bold\">8</mn></msup><mi mathvariant=\"bold\">m</mi><mo mathvariant=\"bold\">/</mo><mi mathvariant=\"bold\">s</mi></math>) है। v प्रकाश का कला वेग (phase velocity) है। अपवर्तनांक - वायु - 1.0003, जल-1.333, हीरा- 2.417, बर्फ- 1.31, इथाइल अल्कोहल- 1.36, निर्वात -1</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "29",
                    section: "18",
                    question_en: "<p>29. Which of the following is the Galilean moon that was observed in 1610 by the Italian astronomer Galileo Galilei using a homemade telescope?</p>",
                    question_hi: "<p>29. निम्नलिखित में से कौन सा गैलिलियन चंद्रमा है जिसे 1610 में इतालवी खगोलशास्त्री गैलीलियो गैलीली ने एक घर का बना (homemade) टेलीस्कोप का उपयोग करके देखा था?</p>",
                    options_en: ["<p>Namaka</p>", "<p>Phobos</p>", 
                                "<p>Titan</p>", "<p>Ganymede</p>"],
                    options_hi: ["<p>फोबोस</p>", "<p>नमका</p>",
                                "<p>टाइटन</p>", "<p>गेनीमेड</p>"],
                    solution_en: "<p>29.(d) Ganymede, a satellite of Jupiter, is the largest and most massive of the Solar System\'s moons. The ninth-largest object in the Solar System. The next planned mission to the Jovian system (system of moons of Jupiter) is the European Space Agency\'s Jupiter Icy Moon Explorer (JUICE), due to launch in 2023. Namaka (Neptune); Phobos (Mars); Titan (Saturn).</p>",
                    solution_hi: "<p>29.(d) गेनीमेड, बृहस्पति का एक उपग्रह, सौर मंडल के चंद्रमाओं में सबसे बड़ा और सबसे विशाल है। सौर मंडल में नौवीं सबसे बड़ी वस्तु (object ) है । जोवियन सिस्टम (बृहस्पति के चंद्रमाओं की प्रणाली) का अगला नियोजित मिशन यूरोपीय अंतरिक्ष एजेंसी का जुपिटर आइसी मून एक्सप्लोरर (JUICE) है, जो 2023 में लॉन्च होने वाला है। नमका (नेप्च्यून); फोबोस (मंगल); टाइटन (शनि)।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "30",
                    section: "18",
                    question_en: "<p>30. In the context of the spread of germs by faecal matter, which of the following does NOT figure in the F-Diagram?</p>",
                    question_hi: "<p>30. मल पदार्थ द्वारा रोगाणुओं (germs )के प्रसार के संदर्भ में, निम्नलिखित में से कौन F-डायग्राम में नहीं आता है?</p>",
                    options_en: ["<p>Fingers</p>", "<p>Fluids</p>", 
                                "<p>Flies</p>", "<p>Fungi</p>"],
                    options_hi: ["<p>उंगलियां (Fingers)</p>", "<p>तरल पदार्थ (Fluids)</p>",
                                "<p>मक्खियों (Flies)</p>", "<p>कवक (Fungi)</p>"],
                    solution_en: "<p>30.(d) F-Diagram - The F-Diagram is one of the many tools in the Participatory Hygiene and Transformation (PHAST) methodology&nbsp;<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773743168.png\" alt=\"rId65\" width=\"311\" height=\"240\"><br>which seeks to vividly describe in detail the fecal oral transmission route.</p>",
                    solution_hi: "<p>30.(d) F-आरेख &ndash; F-आरेख, सहभागी स्वच्छता और स्वच्छता परिवर्तन (PHAST) पद्धति के कई उपकरणों में से एक है, जो फेकल ओरल ट्रांसमिशन रूट का विस्तार से वर्णन करने का प्रयास करता है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773743168.png\" alt=\"rId65\" width=\"311\" height=\"240\"></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "31",
                    section: "18",
                    question_en: "<p>31. Who won the Kamaladevi Chattopadhyay NIF Book Prize 2024?</p>",
                    question_hi: "<p>31. 2024 का कमलादेवी चट्टोपाध्याय NIF पुस्तक पुरस्कार किसने जीता?</p>",
                    options_en: ["<p>Ramachandra Guha</p>", "<p>Ashok Gopal</p>", 
                                "<p>Shashi Tharoor</p>", "<p>Arvind Subramanian</p>"],
                    options_hi: ["<p>रामचंद्र गुहा</p>", "<p>अशोक गोपाल</p>",
                                "<p>शशि थरूर</p>", "<p>अरविंद सुब्रमणियन</p>"],
                    solution_en: "<p>31.(b) <strong>Ashok Gopal.</strong> He won the Kamaladevi Chattopadhyay NIF Book Prize 2024 for his biography \"A Part Apart: The Life and Thought of B.R. Ambedkar.\"</p>",
                    solution_hi: "<p>31.(b) <strong>अशोक गोपाल। </strong>अशोक गोपाल ने अपनी जीवनी &ldquo;ए पार्ट अपार्ट: द लाइफ एंड थॉट ऑफ बी.आर. अंबेडकर\" के लिए 2024 का कमलादेवी चट्टोपाध्याय NIF पुस्तक पुरस्कार जीता।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "32",
                    section: "18",
                    question_en: "<p>32. Which longitude has been selected as the Standard Meridian of India?</p>",
                    question_hi: "<p>32. किस देशांतर को भारत की मानक याम्योत्तर के रूप में चुना गया है?</p>",
                    options_en: ["<p>82&deg;30\'E</p>", "<p>81&deg;30\'E</p>", 
                                "<p>82&deg;32\'E</p>", "<p>82&deg;31\'E</p>"],
                    options_hi: ["<p>82&deg;30\'E</p>", "<p>81&deg;30\'E</p>",
                                "<p>82&deg;32\'E</p>", "<p>82&deg;31\'E</p>"],
                    solution_en: "<p>32.(a) 82&deg;30\'E longitude has been selected as the Standard Meridian of India. It passes through five Indian states- Uttar Pradesh (Mirzapur), Madhya Pradesh, Chattisgarh, Orissa, and Andhra Pradesh.</p>",
                    solution_hi: "<p>32.(a) 82&deg;30\'E देशांतर को भारत की मानक याम्योत्तर के रूप में चुना गया है। यह पांच भारतीय राज्यों- उत्तर प्रदेश (मिर्जापुर), मध्य प्रदेश, छत्तीसगढ़, उड़ीसा और आंध्र प्रदेश से होकर गुजरती है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "33",
                    section: "18",
                    question_en: "<p>33. Where is the headquarters of SAARC Development Fund (SDF) established by SAARC nations ?</p>",
                    question_hi: "<p>33. सार्क देशों द्वारा स्थापित सार्क विकास कोष (SDF) का मुख्यालय कहाँ है?</p>",
                    options_en: ["<p>Kathmandu, Nepal</p>", "<p>Dhaka, Bangladesh</p>", 
                                "<p>Thimphu, Bhutan</p>", "<p>Mumbai, India</p>"],
                    options_hi: ["<p>काठमांडू, नेपाल</p>", "<p>ढाका, बांग्लादेश</p>",
                                "<p>थिम्पू, भूटान</p>", "<p>मुंबई, भारत</p>"],
                    solution_en: "<p>33.(c) Thimphu, Bhutan is the headquarters of SAARC Development Fund (SDF) established by SAARC nations. SAARC was established on 8th December 1985. Headquarter (Kathmandu, Nepal). SAARC has eight member countries (Afghanistan, Bangladesh, Bhutan, India, Maldives, Nepal, Pakistan and Sri-Lanka).</p>",
                    solution_hi: "<p>33.(c) <strong>थिम्फू, भूटान </strong>सार्क देशों द्वारा स्थापित सार्क विकास निधि (एसडीएफ) का मुख्यालय है। <strong>सार्क की स्थापना</strong> 8 दिसंबर 1985 को हुई थी। मुख्यालय (काठमांडू, नेपाल)। सार्क के आठ सदस्य देश (अफगानिस्तान, बांग्लादेश, भूटान, भारत, मालदीव, नेपाल, पाकिस्तान और श्रीलंका) हैं।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "34",
                    section: "18",
                    question_en: "<p>34. What was the name of King Ashoka&rsquo;s daughter whom he appointed to carry out the duties of a Buddhist missionary?</p>",
                    question_hi: "<p>34. राजा अशोक की बेटी का नाम क्या था जिसे उन्होंने बौद्ध मिशनरी के कर्तव्यों को निभाने के लिए नियुक्त किया था?</p>",
                    options_en: ["<p>Padmavati</p>", "<p>Charumati</p>", 
                                "<p>Asandhimitra</p>", "<p>Sanghamitra</p>"],
                    options_hi: ["<p>पद्मावती</p>", "<p>चारुमती</p>",
                                "<p>असंधिमित्र</p>", "<p>संघमित्रा</p>"],
                    solution_en: "<p>34.(d)<strong> Sanghamitra</strong> was the name of King Ashoka&rsquo;s daughter whom he appointed to carry out the duties of a Buddhist missionary. Mahendra, his son also was on the duties of a Buddhist missionary. They both went to Lakshadweep and Sri Lanka. <strong>Asandhamitra</strong> was a queen and chief consort of the Mauryan emperor Ashoka.</p>",
                    solution_hi: "<p>34.(d) <strong>संघमित्रा </strong>राजा अशोक की बेटी का नाम था जिसे उन्होंने बौद्ध मिशनरी के कर्तव्यों को पूरा करने के लिए नियुक्त किया था। महेंद्र उनका पुत्र भी एक बौद्ध मिशनरी की ड्यूटी पर था। वे दोनों लक्षद्वीप और श्रीलंका गए। <strong>असंधमित्र</strong> मौर्य सम्राट अशोक की एक रानी और प्रमुख पत्नी थीं।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "35",
                    section: "18",
                    question_en: "<p>35. Who said , &ldquo;Political freedom is the life-breath of a nation.&rdquo;</p>",
                    question_hi: "<p>35. किसने कहा, \"राजनीतिक स्वतंत्रता एक राष्ट्र की प्राणवायु है।\"</p>",
                    options_en: ["<p>Rabindranath Tagore</p>", "<p>Gopal Krishna Gokhale</p>", 
                                "<p>Mahatma Gandhi</p>", "<p>Aurobindo Ghosh</p>"],
                    options_hi: ["<p>रवींद्रनाथ टैगोर</p>", "<p>गोपाल कृष्ण गोखले</p>",
                                "<p>महात्मा गांधी</p>", "<p>अरबिंदो घोष</p>"],
                    solution_en: "<p>35.(d) Aurobindo Ghose said , &ldquo;Political freedom is the life-breath of a nation.&rdquo; Sri Aurobindo was an Indian nationalist but is best known for his philosophy on human evolution and Integral Yoga. <strong>Rabindranath Tagore</strong> (First Non European) was awarded the Nobel Prize in 1913 Literature for his work Gitanjali. <strong>Gopal Krishna Gokhale</strong> (Political guru of Mahatma Gandhi).</p>",
                    solution_hi: "<p>35.(d) अरबिंदो घोष ने कहा, \"राजनीतिक स्वतंत्रता एक राष्ट्र की जीवन-श्वास है।\" श्री अरबिंदो एक भारतीय राष्ट्रवादी थे, लेकिन मानव विकास और एकात्म योग पर उनके दर्शन के लिए जाने जाते हैं। <strong>रवींद्रनाथ टैगोर</strong> (प्रथम गैर यूरोपीय) को उनकी रचना गीतांजलि के लिए 1913 साहित्य में नोबेल पुरस्कार से सम्मानित किया गया था। <strong>गोपाल कृष्ण गोखले</strong> (महात्मा गांधी के राजनीतिक गुरु)।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "36",
                    section: "18",
                    question_en: "<p>36. Which of the following acid is the constituent of eyewash?</p>",
                    question_hi: "<p>36. निम्नलिखित में से कौन सा अम्ल नेत्र धोने का घटक है</p>",
                    options_en: ["<p>Sulphuric acid</p>", "<p>Boric acid</p>", 
                                "<p>Acetic acid</p>", "<p>Hydrochloric acid</p>"],
                    options_hi: ["<p>सल्फ्यूरिक अम्ल</p>", "<p>बोरिक अम्ल</p>",
                                "<p>एसीटिक अम्ल</p>", "<p>हाइड्रोक्लोरिक अम्ल</p>"],
                    solution_en: "<p>36.(b) <strong>Boric acid (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi mathvariant=\"bold\">H</mi><mn mathvariant=\"bold\">3</mn></msub><msub><mi mathvariant=\"bold\">BO</mi><mn mathvariant=\"bold\">3</mn></msub></math>)</strong> is the&nbsp;constituent of eyewash. Boric acid is also used as a fungicide and as an insecticide powder. Boric Acid is a weakly acidic hydrate of boric oxide with mild antiseptic, antifungal, and antiviral properties. <strong>Hydrochloric acid</strong>, also known as muriatic acid, It is a component of the gastric acid in the digestive systems of most animal species. <strong>Acetic acid</strong> (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>CH</mi><mn>3</mn></msub><mi>COOH</mi></math>) is also known as ethanoic acid, ethylic acid, vinegar acid, and methane carboxylic acid.Sulphuric acid(<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi mathvariant=\"normal\">H</mi><mn>2</mn></msub><mi>SO</mi><mn>4</mn></math>)</p>",
                    solution_hi: "<p>36.(b) <strong>बोरिक एसिड (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi mathvariant=\"bold\">H</mi><mn mathvariant=\"bold\">3</mn></msub><msub><mi mathvariant=\"bold\">BO</mi><mn mathvariant=\"bold\">3</mn></msub></math>)</strong> आईवॉश का घटक है। बोरिक एसिड का उपयोग कवकनाशी और कीटनाशक पाउडर के रूप में भी किया जाता है। बोरिक एसिड हल्के एंटीसेप्टिक, एंटिफंगल और एंटीवायरल गुणों के साथ बोरिक ऑक्साइड का एक कमजोर अम्लीय हाइड्रेट है। <strong>हाइड्रोक्लोरिक एसिड</strong>, जिसे म्यूरिएटिक एसिड भी कहा जाता है, यह अधिकांश जानवरों की प्रजातियों के पाचन तंत्र में गैस्ट्रिक एसिड का एक घटक है। <strong>एसिटिक एसिड</strong> (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>CH</mi><mn>3</mn></msub><mi>COOH</mi></math>) को एथेनोइक एसिड, एथिलिक एसिड, सिरका एसिड और मीथेन कार्बोक्जिलिक एसिड के रूप में भी जाना जाता है।सल्फ्यूरिक अम्ल(<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi mathvariant=\"normal\">H</mi><mn>2</mn></msub><mi>SO</mi><mn>4</mn></math>)</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "37",
                    section: "18",
                    question_en: "<p>37. Where was the PWR DUPR India Masters Pickleball Championship 2024 held?</p>",
                    question_hi: "<p>37. PWR DUPR इंडिया मास्टर्स पिकलबॉल चैंपियनशिप 2024 कहाँ आयोजित हुई?</p>",
                    options_en: ["<p>Mumbai</p>", "<p>New Delhi</p>", 
                                "<p>Bengaluru</p>", "<p>Chennai</p>"],
                    options_hi: ["<p>मुंबई</p>", "<p>नई दिल्ली</p>",
                                "<p>बेंगलुरु</p>", "<p>चेन्नई</p>"],
                    solution_en: "<p>37.(b) <strong>New Delhi. </strong>The PWR DUPR India Masters Pickleball Championship 2024 was held at the Delhi Lawn Tennis Association (DLTA) Stadium, New Delhi, from 24 to 27 October 2024. This event featured 750 players. Men&rsquo;s Doubles Title : Winners : Armaan Bhatia and Harsh Mehta.</p>",
                    solution_hi: "<p>37.(b) <strong>नई दिल्ली। </strong>PWR DUPR इंडिया मास्टर्स पिकलबॉल चैंपियनशिप 2024 नई दिल्ली के दिल्ली लॉन टेनिस एसोसिएशन (DLTA) स्टेडियम में 24 से 27 अक्टूबर 2024 तक आयोजित हुई। इस आयोजन में 750 खिलाड़ियों ने भाग लिया।&nbsp;मेंस डबल्स खिताब: विजेता: अरमान भाटिया और हर्ष मेहता।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "38",
                    section: "18",
                    question_en: "<p>38. How many microns are there in a metre?</p>",
                    question_hi: "<p>38. एक मीटर में कितने माइक्रोन होते हैं?</p>",
                    options_en: ["<p>10000</p>", "<p>1000</p>", 
                                "<p>1000000</p>", "<p>100000</p>"],
                    options_hi: ["<p>10000</p>", "<p>1000</p>",
                                "<p>1000000</p>", "<p>100000</p>"],
                    solution_en: "<p>38.(c) There are 1000000 microns in a meter. A micron is a unit of measure in the metric system. It equals one-millionth of a meter and one-thousandth of a millimeter.</p>",
                    solution_hi: "<p>38.(c) एक मीटर में 1000000 माइक्रोन होते हैं। एक माइक्रोन मीट्रिक प्रणाली में माप की एक इकाई है। यह एक मीटर के दस लाखवें हिस्से और एक मिलीमीटर के हज़ारवें हिस्से के बराबर होता है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "39",
                    section: "18",
                    question_en: "<p>39. The novel &lsquo;Tamas&rsquo; has been written by:</p>",
                    question_hi: "<p>39. उपन्यास \'तमस\' किसके द्वारा लिखा गया है:</p>",
                    options_en: ["<p>Khushwant Singh</p>", "<p>Amrita Pritam</p>", 
                                "<p>RK Narayan</p>", "<p>Bhisham Sahani</p>"],
                    options_hi: ["<p>खुशवंत सिंह</p>", "<p>अमृता प्रीतम</p>",
                                "<p>आरके नारायण</p>", "<p>भीष्म साहनी</p>"],
                    solution_en: "<p>39.(d) Tamas has been written by <strong>Bhisham Sahani</strong>. Six other Hindi novels by him include &lsquo;Jharokhe&rsquo;, &lsquo;Kadian&rsquo;, &lsquo;Basanti&rsquo;, &lsquo;Mayyadas Ki Madi&rsquo;, &lsquo;Kunto&rsquo;, and &lsquo;Neeloo&rsquo;. <strong>Khushwant Singh</strong> &rarr;&rsquo;Train to Pakistan&rsquo;, &lsquo;Karma&rsquo;, &lsquo;Delhi&rsquo;. <strong>Amrita Pritam</strong> (Pinjar). <strong>RK Narayan</strong> (Malgudi Days).</p>",
                    solution_hi: "<p>39.(d) तमस की रचना <strong>भीष्म साहनी</strong> ने की है। उनके द्वारा छह अन्य हिंदी उपन्यासों में झरोखे, कादियान, बसंती, मायादास की मदी, कुंतो और नीलू शामिल हैं। <strong>खुशवंत सिंह</strong> &rarr; ट्रैन टू पाकिस्तान , <strong>अमृता प्रीतम</strong>- पिंजर ,<strong>आरके नारायण</strong> &rarr; मालगुडी डेज़</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "40",
                    section: "18",
                    question_en: "<p>40. Sankranti, the harvest festival, is celebrated in the month of Magh of the Hindu calendar and is known by different names in different parts of India. What is it called in Gujarat?</p>",
                    question_hi: "<p>40. संक्रांति, फसल त्योहार, हिंदू कैलेंडर के माघ महीने में मनाया जाता है और भारत के विभिन्न हिस्सों में अलग-अलग नामों से जाना जाता है। गुजरात में इसे क्या कहा जाता है ?</p>",
                    options_en: ["<p>Uttarayan</p>", "<p>Pana Sankranti</p>", 
                                "<p>Lohri</p>", "<p>Maghi</p>"],
                    options_hi: ["<p>उत्तरायण</p>", "<p>पाना संक्रांति</p>",
                                "<p>लोहड़ी</p>", "<p>मगही</p>"],
                    solution_en: "<p>40.(a) Sankranti is one of the major Indian harvest festivals celebrated on 14th or 15th of January every year. It is known by various names in different states: Haryana&rarr;Sakrat, Punjab&rarr;Maghi, Gujarat&rarr; Uttarayan, Himachal Pradesh&rarr; Magha Saaji.</p>",
                    solution_hi: "<p>40.(a) संक्रांति हर वर्ष 14 या 15 जनवरी को मनाए जाने वाले प्रमुख भारतीय फसल त्योहारों में से एक है। इसे विभिन्न राज्यों में विभिन्न नामों से जाना जाता है:हरियाणा &rarr;सकरात, पंजाब &rarr;माघी, गुजरात&rarr; उत्तरायण ,हिमाचल प्रदेश &rarr;माघ साजी</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "41",
                    section: "18",
                    question_en: "<p>41. Who took over as the Chairman of the Staff Selection Commission (SSC) in October 2024 ?</p>",
                    question_hi: "<p>41. अक्टूबर 2024 में कर्मचारी चयन आयोग (SSC) के अध्यक्ष का पद किसने संभाला ?</p>",
                    options_en: ["<p>S. Gopalakrishnan</p>", "<p>Rakesh Ranjan</p>", 
                                "<p>Ashok Chandra</p>", "<p>Himanshu Pathak</p>"],
                    options_hi: ["<p>एस गोपालकृष्णन</p>", "<p>राकेश रंजन</p>",
                                "<p>अशोक चंद्र</p>", "<p>हिमांशु पाठक</p>"],
                    solution_en: "<p>41.(a) <strong>S. Gopalakrishnan.</strong> The Staff Selection Commission (SSC) was established on 4th November 1975 under the Department of Personnel and Training (DoPT), Government of India. Its headquarters is located in New Delhi, India.</p>",
                    solution_hi: "<p>41.(a) <strong>एस गोपालकृष्णन।</strong> कर्मचारी चयन आयोग (SSC) की स्थापना 4 नवंबर 1975 को भारत सरकार के कार्मिक और प्रशिक्षण विभाग (DoPT) के तहत की गई थी। इसका मुख्यालय नई दिल्ली, भारत में स्थित है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "42",
                    section: "18",
                    question_en: "<p>42. Chakri\' is a type of traditional music that is popular in the Indian state of ______.</p>",
                    question_hi: "<p>42. चकरी \'एक प्रकार का पारंपरिक संगीत है जो भारतीय राज्य ______ में लोकप्रिय है।</p>",
                    options_en: ["<p>Rajasthan</p>", "<p>Madhya Pradesh</p>", 
                                "<p>Jammu and Kashmir</p>", "<p>Punjab</p>"],
                    options_hi: ["<p>राजस्थान</p>", "<p>मध्य प्रदेश</p>",
                                "<p>जम्मू और कश्मीर</p>", "<p>पंजाब</p>"],
                    solution_en: "<p>42.(c) Chakri is one of the most popular types of traditional music played in Jammu &amp; Kashmir. <br><strong>Rajasthan</strong> &rarr; Panihari and Maand. <strong>Madhya Pradesh</strong>&rarr; Thumri, Dadra, Chaiti, Kajari. <strong>Punjab</strong>&rarr; Jugni, Mahia, Tappe, Jindua, Dhola, Kafian.</p>",
                    solution_hi: "<p>42.(c) चकरी जम्मू में बजाए जाने वाले पारंपरिक संगीत के सबसे लोकप्रिय प्रकारों में से एक है । <strong>राजस्थान</strong>&rarr; पनिहारी और मांड।<br><strong>मध्य प्रदेश</strong>&rarr;ठुमरी, दादरा, चैती, कजरी। <strong>पंजाब</strong>&rarr; जुगनी, माहिया, टप्पे, जिंदुआ, ढोला, काफियां ।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "43",
                    section: "18",
                    question_en: "<p>43. Who invented vaccine for smallpox?</p>",
                    question_hi: "<p>43. चेचक के लिए टीके का आविष्कार किसने किया?</p>",
                    options_en: ["<p>James Chadwick</p>", "<p>Edward Jenner</p>", 
                                "<p>D Rutherford</p>", "<p>Louis Pasteur</p>"],
                    options_hi: ["<p>जेम्स चैडविक</p>", "<p>एडवर्ड जेनर</p>",
                                "<p>डी रदरफोर्ड</p>", "<p>लुई पास्चर</p>"],
                    solution_en: "<p>43.(b) Edward Jenner discovered a vaccine for Smallpox. The vaccine is made from a virus called vaccinia, which is a poxvirus similar to smallpox, but less harmful. <strong>James Chadwick</strong> &rarr; Neutron, <strong>D Rutherford</strong> &rarr; Nitrogen. <strong>Louis Pasteur</strong>&rarr; Originated the process of pasteurization and developed vaccines against anthrax and rabies.</p>",
                    solution_hi: "<p>43.(b) एडवर्ड जेनर ने चेचक के टीके की खोज की। वैक्सीन वैक्सीनिया नामक वायरस से बना है, जो चेचक के समान एक पॉक्सवायरस है, लेकिन कम हानिकारक है। <strong>जेम्स चैडविक</strong> &rarr; न्यूट्रॉन, <strong>डी रदरफोर्ड</strong> &rarr; नाइट्रोजन। <strong>लुई पाश्चर</strong> &rarr; ने पास्चराइजेशन की प्रक्रिया की शुरुआत की और एंथ्रेक्स और रेबीज के खिलाफ टीके विकसित किए।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "44",
                    section: "18",
                    question_en: "<p>44. \'Mamita\' is a popular dance of the Kaloi community from the state of ______.</p>",
                    question_hi: "<p>44. ममीता, कालोई समुदाय का लोकप्रिय नृत्य ______ राज्य से है?</p>",
                    options_en: ["<p>Chattisgarh</p>", "<p>Odisha</p>", 
                                "<p>Tripura</p>", "<p>Assam</p>"],
                    options_hi: ["<p>छत्तीसगढ़</p>", "<p>ओडिशा</p>",
                                "<p>त्रिपुरा</p>", "<p>असम</p>"],
                    solution_en: "<p>44.(c) The main folk dances of Tripura are &ndash; Mamita, Goria, Hai-hak, Hojagiri, Jhum, Lebang Boomani, Mosak Sumani, Owa, Sangrai.</p>",
                    solution_hi: "<p>44.(c) त्रिपुरा के प्रमुख लोकनृत्य हैं- ममीता , गोरिया, है-हक, होजागिरी, झूम, लेबांग बूमनी, मोसक सुमानी, ओवा, संगराई।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "45",
                    section: "18",
                    question_en: "<p>45. Kapaleeswarar Temple in Tamil Nadu is dedicated to which Indian God?</p>",
                    question_hi: "<p>45. तमिलनाडु का कपालीश्वरर मंदिर किस भारतीय देवता को समर्पित है ?</p>",
                    options_en: ["<p>Vishnu</p>", "<p>Durga</p>", 
                                "<p>Brahma</p>", "<p>Shiva</p>"],
                    options_hi: ["<p>विष्णु</p>", "<p>दुर्गा</p>",
                                "<p>ब्रह्मा</p>", "<p>शिव</p>"],
                    solution_en: "<p>45.(d) Kapaleeshwarar Temple is a Hindu temple dedicated to lord Shiva located in Mylapore, Chennai in the Indian state of Tamil Nadu. The form of Shiva\'s consort Parvati worshiped at this temple is called Karpagambal is from Tamil (\"Goddess of the Wish-Yielding Tree\").</p>",
                    solution_hi: "<p>45.(d) कपालीश्वरर मंदिर एक हिंदू मंदिर है जो भारत के तमिलनाडु राज्य में चेन्नई के मायलापुर में स्थित भगवान शिव को समर्पित है। इस मंदिर में पूजा की जाने वाली शिव की पत्नी पार्वती के रूप को करपगंबल कहा जाता है जो तमिल (\"इच्छा देने वाले पेड़ की देवी\") से है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "46",
                    section: "18",
                    question_en: "<p>46. Which of the following is the correct chronology of these rulers?<br>A. Timur <br>B. Mahmud of Ghazni <br>C. Genghis Khan <br>D. Muhammad Ghori</p>",
                    question_hi: "<p>46. इन शासकों का सही कालक्रम निम्नलिखित में से कौन सा है?<br>A. तैमूर<br>B. गजनी का महमूद<br>C. चंगेज खान<br>D. मुहम्मद गौरी</p>",
                    options_en: ["<p>B, D, A, C</p>", "<p>B, D, C, A</p>", 
                                "<p>B, C, D, A</p>", "<p>D, B, C, A</p>"],
                    options_hi: ["<p>B, D, A, C</p>", "<p>B, D, C, A</p>",
                                "<p>B, C, D, A</p>", "<p>D, B, C, A</p>"],
                    solution_en: "<p>46.(b) Mahmud of Ghazni (998 AD), Muhammad Ghori (1173 AD), Genghis Khan (1206 AD), Timur(1370 AD).</p>",
                    solution_hi: "<p>46.(b) गजनी का महमूद (998 ई.), मुहम्मद गोरी (1173 ई.), चंगेज खान (1206 ई.), तैमूर (1370 ई.)</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "47",
                    section: "18",
                    question_en: "<p>47. Which word was named Collins Dictionary\'s Word of the Year 2024?</p>",
                    question_hi: "<p>47. 2024 के लिए कॉलिन्स डिक्शनरी का वर्ड ऑफ द ईयर कौन सा शब्द था?</p>",
                    options_en: ["<p>Brat</p>", "<p>Metaverse</p>", 
                                "<p>Climate Crisis</p>", "<p>Resilience</p>"],
                    options_hi: ["<p>ब्रैट</p>", "<p>मेटावर्स</p>",
                                "<p>जलवायु संकट</p>", "<p>लचीलापन</p>"],
                    solution_en: "<p>47.(a) <strong>Brat.</strong> This reflects its growing&nbsp;usage in cultural and social contexts, symbolizing the impact of youth behavior and attitudes often seen in media and societal discussions.</p>",
                    solution_hi: "<p>47.(a) <strong>ब्रैट।</strong> यह शब्द सांस्कृतिक और सामाजिक संदर्भों में इसके बढ़ते उपयोग को दर्शाता है, जो युवा व्यवहार और दृष्टिकोण के प्रभाव को प्रतीकित करता है, जैसा कि अक्सर मीडिया और सामाजिक चर्चाओं में देखा जाता है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "48",
                    section: "18",
                    question_en: "<p>48. Which organization hosted the National Geoscience Awards 2023 to honor excellence in Earth Sciences?</p>",
                    question_hi: "<p>48. राष्ट्रीय भूविज्ञान पुरस्कार 2023 का आयोजन किस संगठन ने पृथ्वी विज्ञानों में उत्कृष्टता को सम्मानित करने के लिए किया?</p>",
                    options_en: ["<p>Geological Survey of India (GSI)</p>", "<p>Ministry of Earth Sciences</p>", 
                                "<p>Ministry of Mines</p>", "<p>Indian National Science Academy</p>"],
                    options_hi: ["<p>भारतीय भूवैज्ञानिक सर्वेक्षण (GSI)</p>", "<p>पृथ्वी विज्ञान मंत्रालय</p>",
                                "<p>खनिज मंत्रालय</p>", "<p>भारतीय राष्ट्रीय विज्ञान अकादमी</p>"],
                    solution_en: "<p>48.(c) <strong>Ministry of Mines.</strong> The awards aim to encourage geoscientific research and innovation in India. President Droupadi Murmu presided over a distinguished ceremony at the Rashtrapati Bhavan in New Delhi. In a separate but related development, Dr. Jitendra Singh was honored with the prestigious &ldquo;Distinguished Mentor of the Year&rdquo; award by the American College of Physicians. The award was presented during the 9th Annual Conference of the ACP India Chapter in Lucknow.</p>",
                    solution_hi: "<p>48.(c) <strong>खनिज मंत्रालय। </strong>इन पुरस्कारों का उद्देश्य भारत में भूविज्ञान संबंधी अनुसंधान और नवाचार को प्रोत्साहित करना है। राष्ट्रपति द्रौपदी मुर्मू ने नई दिल्ली में राष्ट्रपति भवन में एक विशिष्ट समारोह की अध्यक्षता की। एक अलग लेकिन संबंधित घटनाक्रम में, डॉ. जितेंद्र सिंह को अमेरिकी चिकित्सक कॉलेज द्वारा \"डिस्टिंग्विश्ड मेंटर ऑफ द ईयर\" पुरस्कार से सम्मानित किया गया। यह पुरस्कार लखनऊ में ACP इंडिया चैप्टर के 9वें वार्षिक सम्मेलन के दौरान प्रस्तुत किया गया।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "49",
                    section: "18",
                    question_en: "<p>49. Urea, a commonly used nitrogen-based fertilizer, is prepared by the reaction between ammonia and ______.</p>",
                    question_hi: "<p>49. यूरिया, आमतौर पर इस्तेमाल किया जाने वाला नाइट्रोजन आधारित उर्वरक, के बीच प्रतिक्रिया द्वारा तैयार किया जाता है अमोनिया और ______।</p>",
                    options_en: ["<p>carbon dioxide</p>", "<p>hydrogen</p>", 
                                "<p>oxygen</p>", "<p>sulphur</p>"],
                    options_hi: ["<p>कार्बन डाइऑक्साइड</p>", "<p>हाइड्रोजन</p>",
                                "<p>ऑक्सीजन</p>", "<p>सल्फर</p>"],
                    solution_en: "<p>49.(a) Urea(<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>CH</mi><mn>4</mn></msub><msub><mi mathvariant=\"normal\">N</mi><mn>2</mn></msub><mi mathvariant=\"normal\">O</mi></math>) , a commonly used nitrogen-based fertilizer, is prepared by the reaction between ammonia and Carbon Dioxide. Urea is widely used as a fertilizer, a feed supplement, and a starting material in the manufacture of drugs and plastics. It is used as a nitrogen-release fertilizer .</p>",
                    solution_hi: "<p>49.(a) यूरिया,(CH₄N₂O) आमतौर पर इस्तेमाल किया जाने वाला नाइट्रोजन आधारित उर्वरक, अमोनिया और कार्बन डाइऑक्साइड के बीच प्रतिक्रिया से तैयार किया जाता है। यूरिया का व्यापक रूप से एक उर्वरक, एक फ़ीड पूरक, और दवाओं और प्लास्टिक के निर्माण में एक प्रारंभिक सामग्री के रूप में उपयोग किया जाता है। इसका उपयोग नाइट्रोजन-मुक्त उर्वरक के रूप में किया जाता है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "50",
                    section: "17",
                    question_en: "<p>50. \'Income and employment theory\' is the other name for which branch of Economics ?</p>",
                    question_hi: "<p>50.\'आय और रोजगार सिद्धांत\' अर्थशास्त्र की किस शाखा का दूसरा नाम है ?</p>",
                    options_en: ["<p>International economics</p>", "<p>Public finance</p>", 
                                "<p>Microeconomics</p>", "<p>Macroeconomics</p>"],
                    options_hi: ["<p>अंतर्राष्ट्रीय अर्थशास्त्र</p>", "<p>सार्वजनिक वित्त</p>",
                                "<p>सूक्ष्म अर्थशास्त्र</p>", "<p>समष्टि अर्थशास्त्र</p>"],
                    solution_en: "<p>50.(d) Macroeconomics is the branch of economics that studies the behavior and performance of an economy as a whole. It focuses on the aggregate changes in the economy such as unemployment, growth rate, gross domestic product, and inflation. <strong>Microeconomics</strong> is a part of economics concerned with single factors and the effects of individual decisions. <strong>Public finance</strong> is the study of the role of the government in the economy. <strong>International Economics</strong> is the study of economic interactions between countries.</p>",
                    solution_hi: "<p>50.(d) समष्टि अर्थशास्त्र (मैक्रोइकॉनॉमिक्स) अर्थशास्त्र की वह शाखा है जो समग्र रूप से अर्थव्यवस्था के व्यवहार और प्रदर्शन का अध्ययन करती है। यह अर्थव्यवस्था में कुल परिवर्तन जैसे बेरोजगारी, विकास दर, सकल घरेलू उत्पाद और मुद्रास्फीति पर केंद्रित है।<strong> सूक्ष्मअर्थशास्त्र</strong> एकल कारकों और व्यक्तिगत निर्णयों के प्रभावों से संबंधित अर्थशास्त्र का एक हिस्सा है। <strong>सार्वजनिक वित्त</strong> अर्थव्यवस्था में सरकार की भूमिका का अध्ययन है। <strong>अंतर्राष्ट्रीय अर्थशास्त्र</strong> देशों के बीच आर्थिक संबंधों का अध्ययन है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "51",
                    section: "17",
                    question_en: "<p>51. In a circle, two chords MN and PQ intersect at O. If MO = 9 cm, ON = 5 cm and OQ = 6 cm, then the value of OP (in cm) is:</p>",
                    question_hi: "<p>51. एक वृत्त में, दो जीवाएँ MN और PQ हैं जो O पर प्रतिच्छेद करती हैं। यदि MO = 9 cm, ON = 5 cm और OQ = 6 cm है, तो OP का मान (cm में) कितना है?</p>",
                    options_en: ["<p>7.5</p>", "<p>6.5</p>", 
                                "<p>6</p>", "<p>7</p>"],
                    options_hi: ["<p>7.5</p>", "<p>6.5</p>",
                                "<p>6</p>", "<p>7</p>"],
                    solution_en: "<p>51.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773743392.png\" alt=\"rId66\" width=\"176\" height=\"150\"><br>From the figure, <br>MO &times; ON = OQ &times; OP<br>9 &times; 5 = 6 &times; x<br>x = 7.5 cm</p>",
                    solution_hi: "<p>51.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773743392.png\" alt=\"rId66\" width=\"176\" height=\"150\"><br>MO &times; ON = OQ &times; OP<br>9 &times; 5 = 6 &times; x<br>x = 7.5 सेमी</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "52",
                    section: "17",
                    question_en: "<p>52. A person, while trading the shares of a particular company, observes that its price has&nbsp;gone down by 10% from the previous day. In anticipation of increment on the next day, he holds it for the next day. But the share price further falls down by another 8% of the previous day. Then he sells his shares and gets ₹12,420. How much could he have saved had he sold it the previous day?</p>",
                    question_hi: "<p>52. एक व्यक्ति किसी विशेष कंपनी के शेयरों का क्रय-विक्रय (ट्रेडिंग) करते समय देखता है कि इसका मूल्य पिछले दिन से 10% कम हो गया है। अगले दिन वृद्धि की प्रत्याशा में, वह इसे अगले दिन के लिए रखता&nbsp;है। लेकिन शेयर का मूल्य पिछले दिन की तुलना में 8% और कम हो जाता है। फिर वह अपने शेयर बेचता है और उसे ₹12,420 प्राप्त होते हैं। यदि उसने इसे पिछले दिन बेचा होता तो वह कितना बचा सकता था?</p>",
                    options_en: ["<p>₹1,080</p>", "<p>₹1,220</p>", 
                                "<p>₹1,140</p>", "<p>₹1,260</p>"],
                    options_hi: ["<p>₹1,080</p>", "<p>₹1,220</p>",
                                "<p>₹1,140</p>", "<p>₹1,260</p>"],
                    solution_en: "<p>52.(a)<br>Let original price of the share = 100 units<br>Next day price = 100 <math display=\"inline\"><mo>&#215;</mo></math>&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>90</mn><mn>100</mn></mfrac></math> = 90 units<br>Final price = 90 <math display=\"inline\"><mo>&#215;</mo></math>&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>92</mn><mn>100</mn></mfrac></math> = 82.8 units<br>Given final price = ₹ 12,420<br>82.8 units = ₹ 12,420<br>1 units = ₹ 150<br>So, (90 - 82.8) = 7.2 units = 150 &times; 7.2 = ₹ 1080<br>Hence, required money = ₹ 1080</p>",
                    solution_hi: "<p>52.(a)<br>माना शेयर की मूल कीमत = 100 इकाई<br>अगले दिन की कीमत = 100 <math display=\"inline\"><mo>&#215;</mo></math>&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>90</mn><mn>100</mn></mfrac></math> = 90 इकाई<br>अंतिम कीमत = 90 <math display=\"inline\"><mo>&#215;</mo></math>&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>92</mn><mn>100</mn></mfrac></math> = 82.8 इकाई<br>दी गई अंतिम कीमत = ₹ 12,420<br>82.8 इकाई = ₹ 12,420<br>1 इकाई = ₹ 150 <br>तो, (90 - 82.8) = 7.2 इकाई = 150 &times; 7.2 = ₹ 1080<br>अतः, आवश्यक धनराशि = ₹ 1080</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "53",
                    section: "17",
                    question_en: "<p>53. The mean of marks secured by 3 students in grade A of class XI is 83, that of 4 students of grade B is 76&nbsp;and that of 5 students of grade C is 85. What will be the mean of marks of the students of three grades of Class XI?</p>",
                    question_hi: "<p>53. कक्षा XI के ग्रेड A में 3 विद्यार्थियों द्वारा प्राप्त अंकों का माध्य 83 है, ग्रेड B में 4 विद्यार्थियों द्वारा प्राप्त अंकों का माध्य 76 है और ग्रेड C में 5 विद्यार्थियों द्वारा प्राप्त अंकों का माध्य 85 है। कक्षा XI के तीनों ग्रेड के विद्यार्थियों के अंकों का माध्य कितना होगा?</p>",
                    options_en: ["<p>80.5</p>", "<p>81</p>", 
                                "<p>80</p>", "<p>81.5</p>"],
                    options_hi: ["<p>80.5</p>", "<p>81</p>",
                                "<p>80</p>", "<p>81.5</p>"],
                    solution_en: "<p>53.(d) Sum of the marks grade A of class XI = 83 &times; 3 = 249<br>Sum of the marks grade B of class XI = 76 &times; 4 = 304<br>Sum of the marks grade C of class XI = 85 &times; 5 = 425<br>Mean of marks of the three grade = <math display=\"inline\"><mfrac><mrow><mn>249</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>304</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>425</mn></mrow><mrow><mn>12</mn></mrow></mfrac></math> = 81.5</p>",
                    solution_hi: "<p>53.(d) कक्षा XI के ग्रेड A के अंकों का योग = 83 &times; 3 = 249<br>कक्षा XI के ग्रेड B के अंकों का योग = 76 &times; 4 = 304<br>कक्षा XI के ग्रेड C के अंकों का योग = 85 &times; 5 = 425<br>तीनों ग्रेड के अंकों का माध्य = <math display=\"inline\"><mfrac><mrow><mn>249</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>304</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>425</mn></mrow><mrow><mn>12</mn></mrow></mfrac></math> = 81.5</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "54",
                    section: "17",
                    question_en: "<p>54. The area of a regular hexagon is 2048<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math> cm&sup2;. What is the length (in cm) of each side of the hexagon?</p>",
                    question_hi: "<p>54. एक सम षट्भुज का क्षेत्रफल 2048<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math> cm&sup2; है। षट्&zwnj;भुज की प्रत्येक भुजा की लंबाई (cm में) ज्ञात करें।</p>",
                    options_en: ["<p>32<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>64</mn><msqrt><mn>3</mn></msqrt></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>", 
                                "<p><math display=\"inline\"><mfrac><mrow><mn>32</mn><msqrt><mn>3</mn></msqrt></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>", "<p>64<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math></p>"],
                    options_hi: ["<p>32<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>64</mn><msqrt><mn>3</mn></msqrt></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>",
                                "<p><math display=\"inline\"><mfrac><mrow><mn>32</mn><msqrt><mn>3</mn></msqrt></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>", "<p>64<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math></p>"],
                    solution_en: "<p>54.(b)<br>Area of regular hexagon = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3</mn><msqrt><mn>3</mn></msqrt></mrow><mn>2</mn></mfrac><msup><mi mathvariant=\"normal\">a</mi><mn>2</mn></msup></math><br>According to the question,<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3</mn><msqrt><mn>3</mn></msqrt></mrow><mn>2</mn></mfrac><msup><mi mathvariant=\"normal\">a</mi><mn>2</mn></msup></math> = 2048<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math><br>a&sup2; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>4096</mn><mn>3</mn></mfrac></mstyle></math><br>a = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mstyle displaystyle=\"false\"><mfrac><mn>4096</mn><mn>3</mn></mfrac></mstyle></msqrt></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>64</mn><msqrt><mn>3</mn></msqrt></mfrac></mstyle></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>64</mn><msqrt><mn>3</mn></msqrt></mfrac></mstyle></math> &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><msqrt><mn>3</mn></msqrt><msqrt><mn>3</mn></msqrt></mfrac></mstyle></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mrow><mn>64</mn><msqrt><mn>3</mn></msqrt></mrow><mn>3</mn></mfrac></mstyle></math></p>",
                    solution_hi: "<p>54.(b)<br>नियमित षट्भुज का क्षेत्रफल = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3</mn><msqrt><mn>3</mn></msqrt></mrow><mn>2</mn></mfrac><msup><mi mathvariant=\"normal\">a</mi><mn>2</mn></msup></math><br>प्रश्न के अनुसार,<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3</mn><msqrt><mn>3</mn></msqrt></mrow><mn>2</mn></mfrac><msup><mi mathvariant=\"normal\">a</mi><mn>2</mn></msup></math> = 2048<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math><br>a&sup2; =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>4096</mn><mn>3</mn></mfrac></mstyle></math><br>a =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mstyle displaystyle=\"false\"><mfrac><mn>4096</mn><mn>3</mn></mfrac></mstyle></msqrt></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>64</mn><msqrt><mn>3</mn></msqrt></mfrac></mstyle></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>64</mn><msqrt><mn>3</mn></msqrt></mfrac></mstyle></math> &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><msqrt><mn>3</mn></msqrt><msqrt><mn>3</mn></msqrt></mfrac></mstyle></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mrow><mn>64</mn><msqrt><mn>3</mn></msqrt></mrow><mn>3</mn></mfrac></mstyle></math></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "55",
                    section: "17",
                    question_en: "<p>55. A dealer advertises that he sales his goods at cost price. But he uses 950 gm for 1 kg weight. Find his gain or loss percentage.</p>",
                    question_hi: "<p>55. एक डीलर विज्ञापन देता है कि वह अपना माल क्रय मूल्य पर बेचता है। लेकिन वह 1 kg वाले भार के लिए 950 gm का उपयोग करता है। उसका लाभ या हानि प्रतिशत ज्ञात कीजिए।</p>",
                    options_en: ["<p>Loss<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>46</mn><mi mathvariant=\"normal\">&#160;</mi></mrow><mn>19</mn></mfrac></math>%</p>", "<p>Gain<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi mathvariant=\"normal\">&#160;</mi><mn>46</mn></mrow><mn>19</mn></mfrac></math>%</p>", 
                                "<p>Loss<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi mathvariant=\"normal\">&#160;</mi><mn>100</mn></mrow><mn>19</mn></mfrac></math>%</p>", "<p>Gain<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi mathvariant=\"normal\">&#160;</mi><mn>100</mn></mrow><mn>19</mn></mfrac></math>%</p>"],
                    options_hi: ["<p>हानि<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>46</mn><mi mathvariant=\"normal\">&#160;</mi></mrow><mn>19</mn></mfrac></math>%</p>", "<p>लाभ<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi mathvariant=\"normal\">&#160;</mi><mn>46</mn></mrow><mn>19</mn></mfrac></math>%</p>",
                                "<p>हानि<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi mathvariant=\"normal\">&#160;</mi><mn>100</mn></mrow><mn>19</mn></mfrac></math>%</p>", "<p>लाभ<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi mathvariant=\"normal\">&#160;</mi><mn>100</mn></mrow><mn>19</mn></mfrac></math>%</p>"],
                    solution_en: "<p>55.(d)<br>Given<br>Ratio -&nbsp; &nbsp;CP&nbsp; &nbsp;:&nbsp; &nbsp;SP<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;950&nbsp; &nbsp;:&nbsp; 1000<br>Gain % = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>SP</mi><mi mathvariant=\"normal\">&#160;</mi><mo>-</mo><mi mathvariant=\"normal\">&#160;</mi><mi>CP</mi></mrow><mi>CP</mi></mfrac></math> &times; 100<br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1000</mn><mi mathvariant=\"normal\">&#160;</mi><mo>-</mo><mi mathvariant=\"normal\">&#160;</mi><mn>950</mn></mrow><mn>950</mn></mfrac></math> &times; 100<br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>50</mn><mn>950</mn></mfrac></math> &times; 100 =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>100</mn><mn>19</mn></mfrac></mstyle></math>% gain</p>",
                    solution_hi: "<p>55.(d)<br>दिया गया<br>अनुपात -&nbsp; CP&nbsp; &nbsp;:&nbsp; &nbsp;SP<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;950&nbsp; : 1000<br>लाभ% = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>SP</mi><mi mathvariant=\"normal\">&#160;</mi><mo>-</mo><mi mathvariant=\"normal\">&#160;</mi><mi>CP</mi></mrow><mi>CP</mi></mfrac></math> &times; 100<br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1000</mn><mi mathvariant=\"normal\">&#160;</mi><mo>-</mo><mi mathvariant=\"normal\">&#160;</mi><mn>950</mn></mrow><mn>950</mn></mfrac></math> &times; 100<br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>50</mn><mn>950</mn></mfrac></math> &times; 100 =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>100</mn><mn>19</mn></mfrac></mstyle></math>% लाभ</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "56",
                    section: "17",
                    question_en: "<p>56. Which of the following numbers are divisible by 11?<br>(i) 29435417<br>(ii) 57463828<br>(iii) 57463824<br>(iv) 29435416</p>",
                    question_hi: "<p>56. निम्नलिखित में से कौन-सी संख्याएं, 11 से विभाज्य हैं?<br>(i) 29435417<br>(ii) 57463828<br>(iii 57463824<br>(iv) 29435416</p>",
                    options_en: ["<p>(iii) and (iv)</p>", "<p>(ii) and (iii)</p>", 
                                "<p>(i) and (ii)</p>", "<p>(i) and (iii)</p>"],
                    options_hi: ["<p>(iii) और (iv)</p>", "<p>(ii) और (iii)</p>",
                                "<p>(i) और (ii)</p>", "<p>(i) और (iii)</p>"],
                    solution_en: "<p>56.(d)<br>Divisibility rule of 11 : The difference between sum of digits placed at odd places and at even places is either zero or divisible by 11.<br>Now, <br>(i) 29435417<br><math display=\"inline\"><mo>&#8658;</mo></math> (2 + 4 + 5 + 1) - (9 + 3 + 4 + 7) = 12 - 23 = -11 (Divisible by 11)<br>(ii) 57463828<br><math display=\"inline\"><mo>&#8658;</mo></math> ( 5 + 4 + 3 + 2) - (7 + 6 + 8 + 8) = 14 - 29 = -15 (Not divisible by 11)<br>(iii) 57463824<br><math display=\"inline\"><mo>&#8658;</mo></math> ( 5 + 4 + 3 + 2) - (7 + 6 + 8 + 4) = 14 - 25 = -11 (Divisible by 11)<br>(iv) 29435416<br><math display=\"inline\"><mo>&#8658;</mo></math> (2 + 4 + 5 + 1) - (9 + 3 + 4 + 6) = 12 - 22 = -10 (not divisible by 11)<br>(i) and (iii) are divisible by 11.<br>Hence, option (d) is the correct answer.</p>",
                    solution_hi: "<p>56.(d)<br>11 का विभाज्यता नियम:- विषम स्थानों के अंकों का योग और सम स्थानों के अंकों के योग के बीच का अंतर या तो शून्य होता है या 11 से विभाज्य होता है।<br>अब, <br>(i) 29435417<br><math display=\"inline\"><mo>&#8658;</mo></math> (2 + 4 + 5 + 1) - (9 + 3 + 4 + 7) = 12 - 23 = -11 (11 से विभाज्य)<br>(ii) 57463828<br><math display=\"inline\"><mo>&#8658;</mo></math> ( 5 + 4 + 3 + 2) - (7 + 6 + 8 + 8) = 14 - 29 = -15 (11 से विभाज्य नहीं)<br>(iii) 57463824<br><math display=\"inline\"><mo>&#8658;</mo></math> ( 5 + 4 + 3 + 2) - (7 + 6 + 8 + 4) = 14 - 25 = -11 (11 से विभाज्य)<br>(iv) 29435416<br><math display=\"inline\"><mo>&#8658;</mo></math> (2 + 4 + 5 + 1) - (9 + 3 + 4 + 6) = 12 - 22 = -10 (11 से विभाज्य नहीं)<br>(i) और (iii) 11 से विभाज्य हैं।<br>अतः, विकल्प (d) सही उत्तर है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "57",
                    section: "17",
                    question_en: "<p>57. Vinod and Sanjay were fighting against each other for the post of a village Pradhan. Sanjay got 47.5% votes and lost the election by 270 votes. How many votes did Vinod get (assuming that all the villagers participated in the voting)?</p>",
                    question_hi: "<p>57. विनोद और संजय ग्राम प्रधान पद के लिए एक दूसरे के विरुद्ध चुनाव लड़ रहे थे। संजय को 47.5% मत मिले और 270 मतों से चुनाव हार गया। विनोद को कितने मत मिले (यह मानते हुए कि सभी ग्रामवासियों ने मतदान में भाग लिया)?</p>",
                    options_en: ["<p>2655</p>", "<p>2835</p>", 
                                "<p>2385</p>", "<p>2565</p>"],
                    options_hi: ["<p>2655</p>", "<p>2835</p>",
                                "<p>2385</p>", "<p>2565</p>"],
                    solution_en: "<p>57.(b)<br>Let the total no of votes be 100%<br>No of votes received by Sanjay = 47.5%<br>No of votes received by Vinod = (100 - 47.5)% = 52.5%<br>ATQ,<br>(52.5 - 47.5)% = 270<br>5% = 270<br>1% = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>270</mn><mn>5</mn></mfrac></math> = 54<br>So, the no of votes received by Vinod = 54 &times; 52.5 = 2835</p>",
                    solution_hi: "<p>57.(b)<br>माना कि वोटों की कुल संख्या 100% है<br>संजय को प्राप्त मतों की संख्या = 47.5%<br>विनोद को प्राप्त वोटों की संख्या = (100 - 47.5)% = 52.5%<br>प्रश्न के अनुसार,<br>(52.5 - 47.5)% = 270<br>5% = 270<br>1% = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>270</mn><mn>5</mn></mfrac></math> = 54<br>तो, विनोद को प्राप्त वोटों की संख्या = 54 &times; 52.5 = 2835</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "58",
                    section: "17",
                    question_en: "<p>58. In ∆ABC, two medians AD and BE intersect at G at right angles. If AD = 12 cm and BE = 9 cm, then the length of AB is equal to:</p>",
                    question_hi: "<p>58. ∆ABC में, दो माध्यिकाएँ AD और BE, बिंदु G पर समकोण पर प्रतिच्छेदित करती हैं। यदि AD = 12 cm और BE = 9 cm हैं, तो AB की लंबाई कितनी है?</p>",
                    options_en: ["<p>14 cm</p>", "<p>16 cm</p>", 
                                "<p>12 cm</p>", "<p>10 cm</p>"],
                    options_hi: ["<p>14 cm</p>", "<p>16 cm</p>",
                                "<p>12 cm</p>", "<p>10 cm</p>"],
                    solution_en: "<p>58.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773743555.png\" alt=\"rId67\" width=\"159\" height=\"124\"><br>As we know, centroid (G) divides the median in the ratio 2 : 1. So, we have ;<br>AG = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>3</mn></mfrac></math>AD =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>2</mn><mn>3</mn></mfrac></mstyle></math> &times; 12 = 8 cm<br>BG = <math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math>BE =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>2</mn><mn>3</mn></mfrac></mstyle></math> &times; 9 = 6 cm<br>Now, In <math display=\"inline\"><mo>&#9651;</mo></math>BGA, AB =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mn>8</mn><mn>2</mn></msup><mo>+</mo><msup><mn>6</mn><mn>2</mn></msup></msqrt></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>100</mn></msqrt></math> = 10 cm</p>",
                    solution_hi: "<p>58.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773743555.png\" alt=\"rId67\" width=\"159\" height=\"124\"><br>केन्द्रक (G) मध्यिका को 2 : 1 के अनुपात मे विभाजित करती हैं । <br>AG = <math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math>AD =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>2</mn><mn>3</mn></mfrac></mstyle></math> &times; 12 = 8 cm<br>BG = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>3</mn></mfrac></math>BE =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>2</mn><mn>3</mn></mfrac></mstyle></math> &times; 9 = 6 cm<br>अब, <math display=\"inline\"><mo>&#9651;</mo></math>BGA मे, AB =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mn>8</mn><mn>2</mn></msup><mo>+</mo><msup><mn>6</mn><mn>2</mn></msup></msqrt></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>100</mn></msqrt></math> = 10 cm</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "59",
                    section: "17",
                    question_en: "<p>59. The number of toys manufactured by a machine in 2018 was 25,000, which increased by 20% in 2019.In 2020, the production was hindered by strike and it fell by 15%. How many total toys were manufactured in three years by the machine?</p>",
                    question_hi: "<p>59. 2018 में एक मशीन द्वारा निर्मित खिलौनों की संख्या 25,000 थी, जो 2019 में 20% बढ़ गई। 2020 में उत्पादन हड़ताल से बाधित हुआ और इसमें 15% की गिरावट आई। मशीन द्वारा तीन वर्षों में कुल कितने खिलौने बनाए गए?</p>",
                    options_en: ["<p>80000</p>", "<p>89500</p>", 
                                "<p>25500</p>", "<p>80500</p>"],
                    options_hi: ["<p>80000</p>", "<p>89500</p>",
                                "<p>25500</p>", "<p>80500</p>"],
                    solution_en: "<p>59.(d)<br>No of toys manufactured in 2018 = 25,000<br>No of toys manufactured in 2019 = 25,000 &times; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>6</mn><mn>5</mn></mfrac></math> = 30,000<br>No of toys manufactured in 2020 = 30,000 &times; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>17</mn><mn>20</mn></mfrac></math> =25,500<br>Total no of toys manufactured in 3 yrs = 25,000+30,000+25,500 = 80500</p>",
                    solution_hi: "<p>59.(d)<br>2018 में निर्मित खिलौनों की संख्या = 25,000<br>2019 में निर्मित खिलौनों की संख्या = 25,000 &times; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>6</mn><mn>5</mn></mfrac></math> = 30,000<br>2020 में निर्मित खिलौनों की संख्या = 30,000 &times; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>17</mn><mn>20</mn></mfrac></math> =25,500<br>3 वर्षों में निर्मित खिलौनों की कुल संख्या = 25,000+30,000+25,500 = 80500</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "60",
                    section: "17",
                    question_en: "<p>60. If sin&theta; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mrow><mn>2</mn><mi>xy</mi></mrow><mrow><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup><mo>+</mo><msup><mi mathvariant=\"normal\">y</mi><mn>2</mn></msup></mrow></mfrac></mstyle></math>, then the value of tan&theta; is:</p>",
                    question_hi: "<p>60. यदि sin&theta; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mrow><mn>2</mn><mi>xy</mi></mrow><mrow><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup><mo>+</mo><msup><mi mathvariant=\"normal\">y</mi><mn>2</mn></msup></mrow></mfrac></mstyle></math>, तो tan&theta; का मान हैं :</p>",
                    options_en: ["<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mi>xy</mi></mrow><mrow><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup><mo>-</mo><msup><mi mathvariant=\"normal\">y</mi><mn>2</mn></msup></mrow></mfrac></math></p>", "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup><mo>-</mo><msup><mi mathvariant=\"normal\">y</mi><mn>2</mn></msup></mrow><mrow><mn>2</mn><mi>xy</mi></mrow></mfrac></math></p>", 
                                "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup><mo>-</mo><msup><mi mathvariant=\"normal\">y</mi><mn>2</mn></msup></mrow><mrow><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup><mo>+</mo><msup><mi mathvariant=\"normal\">y</mi><mn>2</mn></msup></mrow></mfrac></math></p>", "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup><mo>+</mo><msup><mi mathvariant=\"normal\">y</mi><mn>2</mn></msup></mrow><mrow><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup><mo>-</mo><msup><mi mathvariant=\"normal\">y</mi><mn>2</mn></msup></mrow></mfrac></math></p>"],
                    options_hi: ["<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mi>xy</mi></mrow><mrow><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup><mo>-</mo><msup><mi mathvariant=\"normal\">y</mi><mn>2</mn></msup></mrow></mfrac></math></p>", "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup><mo>-</mo><msup><mi mathvariant=\"normal\">y</mi><mn>2</mn></msup></mrow><mrow><mn>2</mn><mi>xy</mi></mrow></mfrac></math></p>",
                                "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup><mo>-</mo><msup><mi mathvariant=\"normal\">y</mi><mn>2</mn></msup></mrow><mrow><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup><mo>+</mo><msup><mi mathvariant=\"normal\">y</mi><mn>2</mn></msup></mrow></mfrac></math></p>", "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup><mo>+</mo><msup><mi mathvariant=\"normal\">y</mi><mn>2</mn></msup></mrow><mrow><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup><mo>-</mo><msup><mi mathvariant=\"normal\">y</mi><mn>2</mn></msup></mrow></mfrac></math></p>"],
                    solution_en: "<p>60.(a) <br>(<math display=\"inline\"><mn>2</mn><mi>x</mi><mi>y</mi></math> : x&sup2;+ y&sup2; : x&sup2; - y&sup2;) are pythagorean triplet&nbsp;<br>sin&theta; =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mi>Perpendicular</mi><mi>Hypotenuse</mi></mfrac></mstyle></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mrow><mn>2</mn><mi>xy</mi></mrow><mrow><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup><mo>+</mo><msup><mi mathvariant=\"normal\">y</mi><mn>2</mn></msup></mrow></mfrac></mstyle></math><br>So, tan&theta; =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mi>Perpendicular</mi><mi>Base</mi></mfrac></mstyle></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mrow><mn>2</mn><mi>xy</mi></mrow><mrow><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup><mo>-</mo><msup><mi mathvariant=\"normal\">y</mi><mn>2</mn></msup></mrow></mfrac></mstyle></math></p>",
                    solution_hi: "<p>60.(a) <br>(<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>2</mn><mi>xy</mi></math> : x&sup2;+ y&sup2; : x&sup2; - y&sup2;) पायथागॉरियन त्रिक हैं&nbsp;<br>sin&theta; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mi>&#2354;&#2306;&#2348;</mi><mi>&#2325;&#2352;&#2381;&#2339;</mi></mfrac></mstyle></math>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mrow><mn>2</mn><mi>xy</mi></mrow><mrow><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup><mo>+</mo><msup><mi mathvariant=\"normal\">y</mi><mn>2</mn></msup></mrow></mfrac></mstyle></math><br>तो , tan&theta;&nbsp;=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mi>&#2354;&#2306;&#2348;</mi><mi>&#2310;&#2343;&#2366;&#2352;</mi></mfrac></mstyle></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mrow><mn>2</mn><mi>xy</mi></mrow><mrow><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup><mo>-</mo><msup><mi mathvariant=\"normal\">y</mi><mn>2</mn></msup></mrow></mfrac></mstyle></math></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "61",
                    section: "17",
                    question_en: "<p>61. Any amount invested at compound interest (compounded annually) becomes 3.24 times itself in 2 years. What is the annual interest rate ?</p>",
                    question_hi: "<p>61. चक्रवृद्धि ब्याज (वार्षिक रूप से संयोजित) पर निवेश करने पर कोई धनराशि 2 वर्षों में स्वयं की 3.24 गुना हो जाती है। वार्षिक ब्याज दर कितनी है?</p>",
                    options_en: ["<p>60 percent</p>", "<p>75 percent</p>", 
                                "<p>90 percent</p>", "<p>80 percent</p>"],
                    options_hi: ["<p>60 प्रतिशत</p>", "<p>75 प्रतिशत</p>",
                                "<p>90 प्रतिशत</p>", "<p>80 प्रतिशत</p>"],
                    solution_en: "<p>61.(d) Let the amount be A and principal amount be P<br>According to question,<br><math display=\"inline\"><mo>&#8658;</mo></math>A = 3.24P<br><math display=\"inline\"><mo>&#8658;</mo></math><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mi>A</mi><mi mathvariant=\"normal\">P</mi></mfrac></mstyle></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mrow><mn>3</mn><mo>.</mo><mn>24</mn></mrow><mn>1</mn></mfrac></mstyle></math><br>So, <br><math display=\"inline\"><mo>&#8658;</mo></math>A = P<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mn>1</mn><mo>+</mo><mstyle displaystyle=\"false\"><mfrac><mi mathvariant=\"normal\">R</mi><mn>100</mn></mfrac></mstyle><mo>)</mo></mrow><mi mathvariant=\"normal\">n</mi></msup></math><br><math display=\"inline\"><mo>&#8658;</mo></math><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mrow><mn>3</mn><mo>.</mo><mn>24</mn></mrow><mn>1</mn></mfrac></mstyle></math>&nbsp;= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mn>1</mn><mo>+</mo><mstyle displaystyle=\"false\"><mfrac><mi mathvariant=\"normal\">R</mi><mn>100</mn></mfrac></mstyle><mo>)</mo></mrow><mn>2</mn></msup></math><br><math display=\"inline\"><mo>&#8658;</mo></math><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>324</mn><mn>100</mn></mfrac></mstyle></math>&nbsp;= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mn>1</mn><mo>+</mo><mstyle displaystyle=\"false\"><mfrac><mi mathvariant=\"normal\">R</mi><mn>100</mn></mfrac></mstyle><mo>)</mo></mrow><mn>2</mn></msup></math><br><math display=\"inline\"><mo>&#8658;</mo></math>1 +&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mi>R</mi><mn>100</mn></mfrac></mstyle><mo>=</mo><mstyle displaystyle=\"false\"><mfrac><mn>18</mn><mn>10</mn></mfrac></mstyle></math><br><math display=\"inline\"><mo>&#8658;</mo></math><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mi mathvariant=\"normal\">R</mi><mn>100</mn></mfrac></mstyle></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>8</mn><mn>10</mn></mfrac></mstyle></math> &rArr; R = 80%</p>",
                    solution_hi: "<p>61.(d) माना मिश्रधन A है और <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#2350;&#2370;&#2354;&#2343;&#2344;</mi></math> P है<br>प्रश्न के अनुसार,<br><math display=\"inline\"><mo>&#8658;</mo></math>मिश्रधन = 3.24 &times; मूलधन<br><math display=\"inline\"><mo>&#8658;</mo></math><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mi mathvariant=\"normal\">A</mi><mi mathvariant=\"normal\">P</mi></mfrac></mstyle></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mrow><mn>3</mn><mo>.</mo><mn>24</mn></mrow><mn>1</mn></mfrac></mstyle></math><br>इसलिए, <br><math display=\"inline\"><mo>&#8658;</mo></math>मिश्रधन = मूलधन &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mn>1</mn><mo>+</mo><mstyle displaystyle=\"false\"><mfrac><mi>&#2342;&#2352;</mi><mn>100</mn></mfrac></mstyle><mo>)</mo></mrow><mi mathvariant=\"normal\">n</mi></msup></math><br><math display=\"inline\"><mo>&#8658;</mo></math><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mrow><mn>3</mn><mo>.</mo><mn>24</mn></mrow><mn>1</mn></mfrac></mstyle></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mn>1</mn><mo>+</mo><mstyle displaystyle=\"false\"><mfrac><mi mathvariant=\"normal\">R</mi><mn>100</mn></mfrac></mstyle><mo>)</mo></mrow><mn>2</mn></msup></math><br><math display=\"inline\"><mo>&#8658;</mo></math><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>324</mn><mn>100</mn></mfrac></mstyle></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mn>1</mn><mo>+</mo><mstyle displaystyle=\"false\"><mfrac><mi mathvariant=\"normal\">R</mi><mn>100</mn></mfrac></mstyle><mo>)</mo></mrow><mn>2</mn></msup></math><br><math display=\"inline\"><mo>&#8658;</mo></math>1 +&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mi>&#2342;&#2352;</mi><mn>100</mn></mfrac></mstyle></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>18</mn><mn>10</mn></mfrac></mstyle></math><br><math display=\"inline\"><mo>&#8658;</mo></math><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mi>&#2342;&#2352;</mi><mn>100</mn></mfrac></mstyle></math>&nbsp;= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>8</mn><mn>10</mn></mfrac></mstyle></math> &rArr; दर = 80%</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "62",
                    section: "17",
                    question_en: "<p>62. The least common multiple of three different numbers is 120. Which of the following cannot be the greatest common factor of those numbers ?</p>",
                    question_hi: "<p>62. तीन अलग-अलग संख्याओं का लघुत्तम समापवर्त्य 120 है। निम्नलिखित में से कौन-सा उन संख्याओं का महत्तम समापवर्तक नहीं हो सकता है ?</p>",
                    options_en: ["<p>40</p>", "<p>20</p>", 
                                "<p>16</p>", "<p>15</p>"],
                    options_hi: ["<p>40</p>", "<p>20</p>",
                                "<p>16</p>", "<p>15</p>"],
                    solution_en: "<p>62.(c)&nbsp;Checking all the options one by one, we get 16 is the only number which does not divide the given no.<br>So, 16 can&rsquo;t be the greatest common factor of 120.</p>",
                    solution_hi: "<p>62.(c)&nbsp;एक-एक करके सभी विकल्पों की जांच करने पर, हमें 16 एकमात्र संख्या मिलती है जो दी गई संख्या को विभाजित नहीं करती है।&nbsp;इसलिए, 16, 120 का सबसे बड़ा सामान्य गुणनखंड नहीं हो सकता।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "63",
                    section: "17",
                    question_en: "<p>63. If a&sup3; + 3a&sup2; + 3a = 7, then the value of a&sup2; + 2a is:</p>",
                    question_hi: "<p>63. यदि a&sup3; + 3a&sup2; + 3a = 7 है, तो a&sup2; + 2a का मान ज्ञात कीजिए।</p>",
                    options_en: ["<p>3</p>", "<p>1</p>", 
                                "<p>2</p>", "<p>4</p>"],
                    options_hi: ["<p>3</p>", "<p>1</p>",
                                "<p>2</p>", "<p>4</p>"],
                    solution_en: "<p>63.(a) <strong>Given,</strong> a&sup3; + 3a&sup2; + 3a = 7 <br>By hit and trial , put a = 1 , which satisfy the condition <br>&there4; a&sup2; + 2a = <math display=\"inline\"><msup><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></msup></math>+ 2 (1) = 3</p>",
                    solution_hi: "<p>63.(a) <strong>दिया गया है, </strong>a&sup3; + 3a&sup2; + 3a = 7 <br>हिट एंड ट्रायल द्वारा, a = 1 रखने पर ,शर्त पूरी होती है,<br>&there4; a&sup2; + 2a = <math display=\"inline\"><msup><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></msup></math>+ 2 (1) = 3</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "64",
                    section: "17",
                    question_en: "<p>64. The cost price of 20 articles is the same as the selling price of 15 articles. The profit percentage in the transaction is:</p>",
                    question_hi: "<p>64. 20 वस्तुओं का क्रय मूल्य 15 वस्तुओं के विक्रय मूल्य के समान है। लेन-देन में लाभ प्रतिशत कितना है?</p>",
                    options_en: ["<p>33<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math>%</p>", "<p>25%</p>", 
                                "<p>50%</p>", "<p>30%</p>"],
                    options_hi: ["<p>33<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> %</p>", "<p>25%</p>",
                                "<p>50%</p>", "<p>30%</p>"],
                    solution_en: "<p>64.(a) <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"bold\">CP</mi><mi mathvariant=\"bold\">SP</mi></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>15</mn><mn>20</mn></mfrac></mstyle></math><br>Profit percentage = <math display=\"inline\"><mfrac><mrow><mn>20</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>15</mn><mi>&#160;</mi></mrow><mrow><mn>15</mn></mrow></mfrac></math> &times; 100 =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>100</mn><mn>3</mn></mfrac></mstyle></math> = 33<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>1</mn><mn>3</mn></mfrac></mstyle></math>%</p>",
                    solution_hi: "<p>64.(a) <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"bold\">CP</mi><mi mathvariant=\"bold\">SP</mi></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>15</mn><mn>20</mn></mfrac></mstyle></math><br>लाभ प्रतिशत = <math display=\"inline\"><mfrac><mrow><mn>20</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>15</mn><mi>&#160;</mi></mrow><mrow><mn>15</mn></mrow></mfrac></math> &times; 100 =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>100</mn><mn>3</mn></mfrac></mstyle></math> = 33<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>1</mn><mn>3</mn></mfrac></mstyle></math>%</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "65",
                    section: "17",
                    question_en: "<p>65. The curved surface area of a cone is 2200 cm&sup2; and its radius is 28 cm, what is the slant height (in cm) of the cone? (Use &pi; = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mn>7</mn></mfrac></math>)</p>",
                    question_hi: "<p>65. एक शंकु का वक्र पृष्ठीय क्षेत्रफल 2200 cm&sup2; है और इसकी त्रिज्या 28 cm है। इस शंकु की तिर्यक ऊंचाई (cm में) क्या है?&nbsp;(&pi; = <math display=\"inline\"><mfrac><mrow><mn>22</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> का प्रयोग करें)।</p>",
                    options_en: ["<p>22</p>", "<p>23</p>", 
                                "<p>24</p>", "<p>25</p>"],
                    options_hi: ["<p>22</p>", "<p>23</p>",
                                "<p>24</p>", "<p>25</p>"],
                    solution_en: "<p>65.(d) <br>Curved surface area of the cone = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#960;rl</mi></math><br>2200 = <math display=\"inline\"><mfrac><mrow><mn>22</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> &times; 28 &times; l<br>(slant height) l = 25 cm</p>",
                    solution_hi: "<p>65.(d) <br>शंकु का वक्र पृष्ठीय क्षेत्रफल = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#960;rl</mi></math><br>2200 = <math display=\"inline\"><mfrac><mrow><mn>22</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> &times; 28 &times; l<br>(तिर्यक ऊंचाई) l = 25 cm</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "66",
                    section: "17",
                    question_en: "<p>66. Rakesh rented out an amount to Samarth for 6 years at simple interest rate. At the end of the 6th year, Samarth paid <math display=\"inline\"><mfrac><mrow><mn>8</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math>of the amount to Rakesh to clear out the amount. The rate of simple interest per annum was:</p>",
                    question_hi: "<p>66. राकेश ने समर्थ को 6 वर्ष के लिए साधारण ब्याज दर पर एक राशि ब्याज पर दी। छठे वर्ष के अंत में, समर्थ ने राशि चुकाने के लिए राकेश को राशि के <math display=\"inline\"><mfrac><mrow><mn>8</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> भाग का भुगतान किया। वार्षिक साधारण ब्याज की दर कितनी थी?</p>",
                    options_en: ["<p><math display=\"inline\"><mn>2</mn><mfrac><mrow><mn>7</mn></mrow><mrow><mn>21</mn></mrow></mfrac></math>%</p>", "<p>2<math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>20</mn></mrow></mfrac></math>%</p>", 
                                "<p>2<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>12</mn></mrow></mfrac></math>%</p>", "<p>2<math display=\"inline\"><mfrac><mrow><mn>8</mn></mrow><mrow><mn>21</mn></mrow></mfrac></math>%</p>"],
                    options_hi: ["<p><math display=\"inline\"><mn>2</mn><mfrac><mrow><mn>7</mn></mrow><mrow><mn>21</mn></mrow></mfrac></math>%</p>", "<p>2<math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>20</mn></mrow></mfrac></math>%</p>",
                                "<p>2<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>12</mn></mrow></mfrac></math>%</p>", "<p>2<math display=\"inline\"><mfrac><mrow><mn>8</mn></mrow><mrow><mn>21</mn></mrow></mfrac></math>%</p>"],
                    solution_en: "<p>66.(d) Let Principal = 7 unit <br>At the end of the 6th year, Samarth paid <math display=\"inline\"><mfrac><mrow><mn>8</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math>of the amount to Rakesh to clear out the amount<br>Amount at the end of 6 year will be 8 unit <br>&there4; Simple Interest = 8 - 7 = 1 unit <br><math display=\"inline\"><mo>&#8658;</mo></math>1 =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mrow><mn>7</mn><mo>&#215;</mo><mi mathvariant=\"normal\">R</mi><mo>&#215;</mo><mn>6</mn></mrow><mn>100</mn></mfrac></mstyle></math><br><math display=\"inline\"><mo>&#8658;</mo></math>R =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>100</mn><mn>42</mn></mfrac></mstyle></math> = 2<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>8</mn><mn>21</mn></mfrac></mstyle></math>%</p>",
                    solution_hi: "<p>66.(d) माना , मूलधन = 7 इकाई <br>छठे वर्ष के अंत में, समर्थ ने राशि चुकाने के लिए राकेश को राशि में से <math display=\"inline\"><mfrac><mrow><mn>8</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> का भुगतान किया<br>6 वर्ष के अंत में राशि = 8 इकाई <br>&there4; साधारण ब्याज = 8 - 7 = 1 इकाई <br><math display=\"inline\"><mo>&#8658;</mo></math>1 =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mrow><mn>7</mn><mo>&#215;</mo><mi mathvariant=\"normal\">R</mi><mo>&#215;</mo><mn>6</mn></mrow><mn>100</mn></mfrac></mstyle></math><br><math display=\"inline\"><mo>&#8658;</mo></math>R =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>100</mn><mn>42</mn></mfrac></mstyle></math> = 2<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>8</mn><mn>21</mn></mfrac></mstyle></math>%</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "67",
                    section: "17",
                    question_en: "<p>67. Given is a circle with centre at C. A, B and D are the points on the circumference. Find&nbsp;&ang;ABC.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773743809.png\" alt=\"rId68\" width=\"161\" height=\"151\"></p>",
                    question_hi: "<p>67. केंद्र C पर एक वृत्त दिया गया है। A, B और D परिधि पर बिंदु हैं। &ang;ABC ज्ञात कीजिए।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773743809.png\" alt=\"rId68\" width=\"161\" height=\"151\"></p>",
                    options_en: ["<p>37&deg;</p>", "<p>33&deg;</p>", 
                                "<p>35&deg;</p>", "<p>30&deg;</p>"],
                    options_hi: ["<p>37&deg;</p>", "<p>33&deg;</p>",
                                "<p>35&deg;</p>", "<p>30&deg;</p>"],
                    solution_en: "<p>67.(d) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773743910.png\" alt=\"rId69\" width=\"164\" height=\"152\"><br><strong>Construction :</strong> join A and C<br>(∵ The angle subtended by an arc at the center is twice the angle subtended at the circumference of the circle)<br>So, &ang;BCD = 2&ang;BAD <br>126&deg; = 2&ang;BAD <math display=\"inline\"><mo>&#8658;</mo></math> &ang;BAD = 63&deg;<br>in <math display=\"inline\"><mi>&#916;</mi></math> ACD <br>&ang;CAD = &ang;CDA = 33&deg; &hellip; {angle opposite to equal side}<br>Now, &ang;BAC = &ang;BAD - &ang;CAD <br>&ang;BAC = 63&deg; - 33&deg; = 30&deg;<br>in <math display=\"inline\"><mi>&#916;</mi></math> ACB<br>&ang;CAB = &ang;ABC = 30&deg; &hellip; {angle opposite to equal side}<br>Hence, &ang;ABC = 30&deg;</p>",
                    solution_hi: "<p>67.(d) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773743910.png\" alt=\"rId69\" width=\"164\" height=\"152\"><br>(∵ एक चाप द्वारा केंद्र पर बनाया गया कोण वृत्त की परिधि पर बनाए गए कोण का दोगुना होता है।)<br>तो, &ang;BCD = 2&ang;BAD <br>126&deg; = 2&ang;BAD <math display=\"inline\"><mo>&#8658;</mo></math> &ang;BAD = 63&deg;<br><math display=\"inline\"><mi>&#916;</mi></math> ACD में<br>&ang;CAD = &ang;CDA = 33&deg; {सामान भुजाओं के विपरीत कोण}<br>अब , &ang;BAC = &ang;BAD - &ang;CAD <br>&ang;BAC = 63&deg; - 33&deg; = 30&deg;<br><math display=\"inline\"><mi>&#916;</mi></math> ACB में<br>&ang;CAB = &ang;ABC = 30&deg; {सामान भुजाओं के विपरीत कोण}<br>अत: , &ang;ABC = 30&deg;</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "68",
                    section: "17",
                    question_en: "<p>68. In an amusement park, all rides are equally priced, and the entry ticket for the park costs an additional ₹180. Rohan had 15 rides and paid a total of ₹1,380 for his visit to the park. How much (in ₹) in all would Rohan have to pay for his visit to the amusement park if he had opted for 20 rides ?</p>",
                    question_hi: "<p>68. एक मनोरंजन उद्यान में, सभी राइड का किराया एक समान है, और उद्यान के लिए प्रवेश टिकट पर ₹180 का अतिरिक्त खर्च आता है। रोहन ने 15 राइड ली और उसने उद्यान में अपनी यात्रा के लिए कुल ₹1,380 का भुगतान किया। यदि रोहन 20 राइड का विकल्प चुनता है, तो उसे मनोरंजन उद्यान में अपनी यात्रा के लिए कुल कितना (₹ में) भुगतान करना होगा ?</p>",
                    options_en: ["<p>1840</p>", "<p>1760</p>", 
                                "<p>1800</p>", "<p>1780</p>"],
                    options_hi: ["<p>1840</p>", "<p>1760</p>",
                                "<p>1800</p>", "<p>1780</p>"],
                    solution_en: "<p>68.(d) According to question,<br>Cost of the 15 rides = 1380 - 180 = ₹ 1200<br>Cost of the 1 ride = <math display=\"inline\"><mfrac><mrow><mn>1200</mn></mrow><mrow><mn>15</mn></mrow></mfrac></math> = ₹ 80<br>Now,<br>Cost of the 20 rides = 20 &times; 80 + 180 = ₹ 1780</p>",
                    solution_hi: "<p>68.(d) प्रश्न के अनुसार,<br>15 राइड की लागत = 1380 - 180 = ₹ 1200<br>1 राइड की लागत = <math display=\"inline\"><mfrac><mrow><mn>1200</mn></mrow><mrow><mn>15</mn></mrow></mfrac></math> = ₹ 80<br>अब,<br>20 राइड की लागत = 20 &times; 80 + 180 = ₹ 1780</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "69",
                    section: "17",
                    question_en: "<p>69. The price of a book is first increased by 10% and then again by 20%. If the price of the book after both increments is ₹33, then the original price (in ₹) of the book is:</p>",
                    question_hi: "<p>69. एक किताब की कीमत में पहले 10% और फिर 20% की वृद्धि की जाती है। यदि दोनों वृद्धियों के बाद पुस्तक का मूल्य ₹33 है, तो पुस्तक का मूल मूल्य (₹ में) कितना है ?</p>",
                    options_en: ["<p>30</p>", "<p>32</p>", 
                                "<p>33</p>", "<p>25</p>"],
                    options_hi: ["<p>30</p>", "<p>32</p>",
                                "<p>33</p>", "<p>25</p>"],
                    solution_en: "<p>69.(d) Let original price of book = ₹x<br><math display=\"inline\"><mo>&#8658;</mo></math>x &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>11</mn><mn>10</mn></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>6</mn><mn>5</mn></mfrac></math> = 33<br><math display=\"inline\"><mo>&#8658;</mo></math>x = ₹25<br>&there4; original price of book = ₹25</p>",
                    solution_hi: "<p>69.(d) माना पुस्तक का मूल मूल्य = ₹x<br><math display=\"inline\"><mo>&#8658;</mo></math>x &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>11</mn><mn>10</mn></mfrac></math> &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>6</mn><mn>5</mn></mfrac></math> = 33<br><math display=\"inline\"><mo>&#8658;</mo></math>x = ₹25<br>&there4; पुस्तक का मूल मूल्य = ₹25</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "70",
                    section: "17",
                    question_en: "<p>70. The given bar graph shows the number of students enrolled in Institutes A and B during 5 years (2018 to 2022).<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773744008.png\" alt=\"rId70\" width=\"290\" height=\"220\"> <br>What is the ratio of the total students enrolled in Institute B in 2019, 2020 and 2022 to that of the total students enrolled in Institute A in 2018, 2020 and 2021?</p>",
                    question_hi: "<p>70. दिया गया बार ग्राफ 5 वर्षों (2018 से 2022) के दौरान संस्थान A और B में नामांकित छात्रों की संख्या को दर्शाता है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773744103.png\" alt=\"rId71\" width=\"290\" height=\"220\"> <br>2019, 2020 और 2022 में संस्थान B में नामांकित छात्रों की कुल संख्या और 2018, 2020 और 2021 में संस्थान A में नामांकित छात्रों की कुल संख्या का अनुपात ज्ञात करें।</p>",
                    options_en: ["<p>37 : 28</p>", "<p>11 : 28</p>", 
                                "<p>28 : 11</p>", "<p>28 : 37</p>"],
                    options_hi: ["<p>37 : 28</p>", "<p>11 : 28</p>",
                                "<p>28 : 11</p>", "<p>28 : 37</p>"],
                    solution_en: "<p>70.(a)<br>Students enrolled in institute B in 2019 , 2020 and 2022 = 350 + 375 + 200 = 925<br>Students enrolled in institute A in 2018 , 2020 and 2021 = 150 + 300 + 250 = 700<br>So, required ratio = 925 : 700 = 37 : 28</p>",
                    solution_hi: "<p>70.(a)<br>2019, 2020 और 2022 में संस्थान B में नामांकित छात्र = 350 + 375 + 200 = 925<br>2018, 2020 और 2021 में संस्थान A में नामांकित छात्र = 150 + 300 + 250 = 700<br>तो, आवश्यक अनुपात = 925 : 700 = 37 : 28</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "71",
                    section: "17",
                    question_en: "<p>71. Express the following as a vulgar fraction.<br>0.<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><menclose notation=\"top\"><mn>54</mn></menclose></math>+ 0.2<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><menclose notation=\"top\"><mn>3</mn></menclose></math></p>",
                    question_hi: "<p>71. निम्नलिखित को साधारण भिन्न के रूप में व्यक्त कीजिए।<br>0.<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><menclose notation=\"top\"><mn>54</mn></menclose></math>+ 0.2<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><menclose notation=\"top\"><mn>3</mn></menclose></math></p>",
                    options_en: ["<p><math display=\"inline\"><mfrac><mrow><mn>771</mn></mrow><mrow><mn>990</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>771</mn></mrow><mrow><mn>900</mn></mrow></mfrac></math></p>", 
                                "<p><math display=\"inline\"><mfrac><mrow><mn>771</mn></mrow><mrow><mn>99</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>771</mn></mrow><mrow><mn>999</mn></mrow></mfrac></math></p>"],
                    options_hi: ["<p><math display=\"inline\"><mfrac><mrow><mn>771</mn></mrow><mrow><mn>990</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>771</mn></mrow><mrow><mn>900</mn></mrow></mfrac></math></p>",
                                "<p><math display=\"inline\"><mfrac><mrow><mn>771</mn></mrow><mrow><mn>99</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>771</mn></mrow><mrow><mn>999</mn></mrow></mfrac></math></p>"],
                    solution_en: "<p>71.(a)<br>0.<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><menclose notation=\"top\"><mn>54</mn></menclose></math>+ 0.2<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><menclose notation=\"top\"><mn>3</mn></menclose></math><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>54</mn><mi>&#160;</mi></mrow><mn>99</mn></mfrac></math> +&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mrow><mn>23</mn><mo>-</mo><mn>2</mn><mi>&#160;</mi></mrow><mn>90</mn></mfrac></mstyle></math><br>= <math display=\"inline\"><mfrac><mrow><mn>54</mn><mi>&#160;</mi></mrow><mrow><mn>99</mn></mrow></mfrac></math> +&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>21</mn><mn>90</mn></mfrac></mstyle></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mrow><mn>540</mn><mo>+</mo><mn>231</mn><mi>&#160;</mi></mrow><mn>990</mn></mfrac></mstyle></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>771</mn><mn>990</mn></mfrac></mstyle></math></p>",
                    solution_hi: "<p>71.(a)<br>0.<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><menclose notation=\"top\"><mn>54</mn></menclose></math>+ 0.2<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><menclose notation=\"top\"><mn>3</mn></menclose></math><br>= <math display=\"inline\"><mfrac><mrow><mn>54</mn><mi>&#160;</mi></mrow><mrow><mn>99</mn></mrow></mfrac></math> +&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mrow><mn>23</mn><mo>-</mo><mn>2</mn><mi>&#160;</mi></mrow><mn>90</mn></mfrac></mstyle></math><br>= <math display=\"inline\"><mfrac><mrow><mn>54</mn><mi>&#160;</mi></mrow><mrow><mn>99</mn></mrow></mfrac></math> +&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>21</mn><mn>90</mn></mfrac></mstyle></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mrow><mn>540</mn><mo>+</mo><mn>231</mn></mrow><mn>990</mn></mfrac></mstyle></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>771</mn><mn>990</mn></mfrac></mstyle></math></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "72",
                    section: "17",
                    question_en: "<p>72. The following table shows the total candidates appeared and number of candidates present, in different exam centres &ndash; P, Q and R. Study the table and answer the question that follows.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773744218.png\" alt=\"rId72\" width=\"394\" height=\"160\"> <br>&lsquo;Total&rsquo; denotes total candidates applied for the centre,&lsquo;Present&rsquo; denotes the candidates appeared. In which year was the number of absentees the second highest in total of all centres?</p>",
                    question_hi: "<p>72. निम्नलिखित तालिका विभिन्न परीक्षा केंद्रों P, Q और R में कुल उपस्थित उम्मीदवारों और उपस्थित उम्मीदवारों की संख्या को दर्शाती है। तालिका का अध्ययन कीजिए और निम्नलिखित प्रश्न का उत्तर दीजिए।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773744343.png\" alt=\"rId73\" width=\"421\" height=\"170\"> <br>\'कुल\' केंद्र के लिए आवेदन करने वाले कुल उम्मीदवारों को दर्शाता है, \'उपस्थित\' उपस्थित उम्मीदवारों को दर्शाता है।<br>किस वर्ष में सभी केन्द्रों में अनुपस्थित रहने वालों की संख्या दूसरी सबसे अधिक थी?</p>",
                    options_en: ["<p>2018</p>", "<p>2017</p>", 
                                "<p>2020</p>", "<p>2019</p>"],
                    options_hi: ["<p>2018</p>", "<p>2017</p>",
                                "<p>2020</p>", "<p>2019</p>"],
                    solution_en: "<p>72.(d)<br>The number of absentees in 2018 = (64 + 65 + 65) - (60 + 45 + 55) = 34<br>The number of absentees in 2017 = (50 + 75 + 45) - (45 + 55 + 40) = 30 <br>The number of absentees in 2020 = (55 + 80 + 90) - (44 + 66 + 85) = 30<br>The number of absentees in 2019 = (80 + 84 + 70) - (69 + 72 + 62) = 31<br>It is clear from the above expression that in 2019 the number of absentees is the second highest in total of all centres.</p>",
                    solution_hi: "<p>72.(d)<br>2018 में अनुपस्थितों की संख्या = (64 + 65 + 65) - (60 + 45 + 55) = 34<br>2017 में अनुपस्थितों की संख्या = (50 + 75 + 45) - (45 + 55 + 40) = 30 <br>2020 में अनुपस्थितों की संख्या= (55 + 80 + 90) - (44 + 66 + 85) = 30<br>2019 में अनुपस्थितों की संख्या= (80 + 84 + 70) - (69 + 72 + 62) = 31<br>उपरोक्त अभिव्यक्ति से यह स्पष्ट है कि 2019 में अनुपस्थितों की संख्या सभी केंद्रों के कुल योग में दूसरी सबसे अधिक है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "73",
                    section: "17",
                    question_en: "<p>73. If cos A + cos&sup2;A = 1 then sin&sup2;A +&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>sin</mi><mn>4</mn></msup><mi mathvariant=\"normal\">A</mi></math> is equal to:</p>",
                    question_hi: "<p>73. यदि cos A + cos&sup2;A = 1 है, तो sin&sup2;A +&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>sin</mi><mn>4</mn></msup><mi mathvariant=\"normal\">A</mi></math> किसके बराबर है?</p>",
                    options_en: ["<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi>cos</mi><mn>2</mn></msup><mi mathvariant=\"normal\">A</mi></mrow><mi>cosA</mi></mfrac></math></p>", "<p>0</p>", 
                                "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>cosA</mi><mrow><msup><mi>cos</mi><mn>2</mn></msup><mi mathvariant=\"normal\">A</mi></mrow></mfrac></math></p>", "<p>1</p>"],
                    options_hi: ["<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi>cos</mi><mn>2</mn></msup><mi mathvariant=\"normal\">A</mi></mrow><mi>cosA</mi></mfrac></math></p>", "<p>0</p>",
                                "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>cosA</mi><mrow><msup><mi>cos</mi><mn>2</mn></msup><mi mathvariant=\"normal\">A</mi></mrow></mfrac></math></p>", "<p>1</p>"],
                    solution_en: "<p>73.(d) cos A + cos&sup2;A = 1<br><math display=\"inline\"><mo>&#8658;</mo></math>cos A = 1 - cos&sup2;A<br><math display=\"inline\"><mo>&#8658;</mo></math>cos A = sin&sup2;A<br>Then,<br>sin&sup2;A +&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>sin</mi><mn>4</mn></msup><mi mathvariant=\"normal\">A</mi></math> = sin&sup2;A + cos&sup2;A = 1</p>",
                    solution_hi: "<p>73.(d) cos A + cos&sup2;A = 1<br><math display=\"inline\"><mo>&#8658;</mo></math>cos A = 1 - cos&sup2;A<br><math display=\"inline\"><mo>&#8658;</mo></math>cos A = sin&sup2;A<br>अब ,<br>sin&sup2;A + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>sin</mi><mn>4</mn></msup><mi mathvariant=\"normal\">A</mi><mo>&#160;</mo></math>= sin&sup2;A + cos&sup2;A = 1</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "74",
                    section: "17",
                    question_en: "<p>74. A and B can complete a work alone in 20 days and 60 days respectively. They began the work together but A left the work after some days and B completed the remaining work in 12 days. After how many days from the beginning A left the work?</p>",
                    question_hi: "<p>74. A और B अकेले-अकेले तौर पर एक काम को क्रमशः 20 दिन और 60 दिन में पूरा कर सकते हैं। उन्होंने एक साथ काम शुरू किया लेकिन A ने कुछ दिनों के बाद काम छोड़ दिया और B ने शेष काम 12 दिनों में पूरा किया। काम की शुरुआत से कितने दिनों के बाद A ने काम छोड़ दिया?</p>",
                    options_en: ["<p>15 days</p>", "<p>10 days</p>", 
                                "<p>9 days</p>", "<p>12 days</p>"],
                    options_hi: ["<p>15 दिन</p>", "<p>10 दिन</p>",
                                "<p>9 दिन</p>", "<p>12 दिन</p>"],
                    solution_en: "<p>74.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773744656.png\" alt=\"rId74\" width=\"136\" height=\"136\"><br>Work done by B in 12 days = 12&times;1 = 12 unit<br>Remaining work = 60 - 12 = 48 unit<br>time taken by (A+B) to complete the work = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>48</mn><mrow><mn>3</mn><mo>+</mo><mn>1</mn></mrow></mfrac></math> = 12 days<br>So, 12 days from the beginning A left the work.</p>",
                    solution_hi: "<p>74.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773744806.png\" alt=\"rId75\" width=\"133\" height=\"137\"><br>B द्वारा 12 दिनों में किया गया कार्य = 12&times;1 = 12 इकाई <br>शेष कार्य = 60-12 = 48 इकाई <br>कार्य पूरा करने में (A+ B) द्वारा लिया गया समय = <math display=\"inline\"><mfrac><mrow><mn>48</mn></mrow><mrow><mn>3</mn><mo>+</mo><mn>1</mn></mrow></mfrac></math> = 12 दिन <br>तो, शुरुआत से 12 दिन बाद A ने काम छोड़ दिया।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "75",
                    section: "7",
                    question_en: "<p>75. Find the values of \'a\' and \'b\' for which the system of equations 3x + y = 3 and (a - b)x + (a + b)y = 3a + b - 3 has infinite solutions.</p>",
                    question_hi: "<p>75. a\' और \'b\' के मान ज्ञात कीजिए जिनके लिए समीकरणों के निकाय 3x + y = 3 तथा (a - b)x + (a + b)y = 3a + b - 3 के अनंत हल हैं।</p>",
                    options_en: ["<p>a = 3, b = - <math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>", "<p>a = - <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math>, b = 2</p>", 
                                "<p>a = 3, b = - <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>", "<p>a = 2, b = - <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>"],
                    options_hi: ["<p>a = 3, b = - <math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>", "<p>a = - <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math>, b = 2</p>",
                                "<p>a = 3, b = - <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>", "<p>a = 2, b = - <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>"],
                    solution_en: "<p>75.(c) <br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi mathvariant=\"normal\">a</mi><mn>1</mn></msub><mo>=</mo><mn>3</mn><mo>,</mo><mo>&#160;</mo><msub><mi mathvariant=\"normal\">b</mi><mn>1</mn></msub><mo>=</mo><mn>1</mn><mo>,</mo><mo>&#160;</mo><msub><mi mathvariant=\"normal\">c</mi><mn>1</mn></msub><mo>=</mo><mo>-</mo><mn>3</mn><mo>&#160;</mo></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#160;</mo><msub><mi mathvariant=\"normal\">a</mi><mn>2</mn></msub><mo>=</mo><mi mathvariant=\"normal\">a</mi><mo>-</mo><mi mathvariant=\"normal\">b</mi><mo>,</mo><mo>&#160;</mo><msub><mi mathvariant=\"normal\">b</mi><mn>2</mn></msub><mo>=</mo><mi mathvariant=\"normal\">a</mi><mo>+</mo><mi mathvariant=\"normal\">b</mi><mo>,</mo><mo>&#160;</mo><msub><mi mathvariant=\"normal\">c</mi><mn>1</mn></msub><mo>&#160;</mo></math>= -3a - b + 3<br>For infinite solution<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msub><mi mathvariant=\"normal\">a</mi><mn>1</mn></msub><msub><mi mathvariant=\"normal\">a</mi><mn>2</mn></msub></mfrac><mo>=</mo><mfrac><msub><mi mathvariant=\"normal\">b</mi><mn>1</mn></msub><msub><mi mathvariant=\"normal\">b</mi><mn>2</mn></msub></mfrac><mo>=</mo><mfrac><msub><mi mathvariant=\"normal\">c</mi><mn>1</mn></msub><msub><mi mathvariant=\"normal\">c</mi><mn>2</mn></msub></mfrac></math><br>Hence, <br><math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mi>a</mi><mo>-</mo><mi>b</mi></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>1</mn><mrow><mi>a</mi><mo>+</mo><mi>b</mi></mrow></mfrac></mstyle></math>=<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mrow><mo>-</mo><mn>3</mn></mrow><mrow><mo>-</mo><mn>3</mn><mi>a</mi><mo>-</mo><mi>b</mi><mo>+</mo><mn>3</mn></mrow></mfrac></mstyle></math><br><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mi>a</mi><mo>+</mo><mi>b</mi></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mrow><mo>-</mo><mn>3</mn></mrow><mrow><mo>-</mo><mn>3</mn><mi>a</mi><mo>-</mo><mi>b</mi><mo>+</mo><mn>3</mn></mrow></mfrac></mstyle></math><br>-3a - b + 3 = -3a - 3b <br>2<math display=\"inline\"><mi>b</mi></math> = -3 or b = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mrow><mo>-</mo><mn>3</mn></mrow><mn>2</mn></mfrac></mstyle></math>&hellip; (i)<br>Now,<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mrow><mi mathvariant=\"normal\">a</mi><mo>-</mo><mi mathvariant=\"normal\">b</mi></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>1</mn><mrow><mi mathvariant=\"normal\">a</mi><mo>+</mo><mi mathvariant=\"normal\">b</mi></mrow></mfrac></mstyle></math><br>3<math display=\"inline\"><mi>a</mi></math> + 3b = a - b <br>2<math display=\"inline\"><mi>a</mi></math> + 4b = 0<br>2<math display=\"inline\"><mi>a</mi></math> + 4 &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mrow><mo>-</mo><mn>3</mn></mrow><mn>2</mn></mfrac></mstyle></math> = 0<br>2<math display=\"inline\"><mi>a</mi></math> = 6 &rArr; a = 3<br>hence, <math display=\"inline\"><mi>a</mi></math> = 3 and b =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mrow><mo>-</mo><mn>3</mn></mrow><mn>2</mn></mfrac></mstyle></math></p>",
                    solution_hi: "<p>75.(c) <br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi mathvariant=\"normal\">a</mi><mn>1</mn></msub><mo>=</mo><mn>3</mn><mo>,</mo><mo>&#160;</mo><msub><mi mathvariant=\"normal\">b</mi><mn>1</mn></msub><mo>=</mo><mn>1</mn><mo>,</mo><mo>&#160;</mo><msub><mi mathvariant=\"normal\">c</mi><mn>1</mn></msub><mo>=</mo><mo>-</mo><mn>3</mn></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi mathvariant=\"normal\">a</mi><mn>2</mn></msub><mo>=</mo><mi mathvariant=\"normal\">a</mi><mo>-</mo><mi mathvariant=\"normal\">b</mi><mo>,</mo><mo>&#160;</mo><msub><mi mathvariant=\"normal\">b</mi><mn>2</mn></msub><mo>=</mo><mi mathvariant=\"normal\">a</mi><mo>+</mo><mi mathvariant=\"normal\">b</mi><mo>,</mo><mo>&#160;</mo><msub><mi mathvariant=\"normal\">c</mi><mn>1</mn></msub><mo>&#160;</mo></math>= -3a - b + 3<br>अनंत समाधान के लिए<br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><msub><mi mathvariant=\"normal\">a</mi><mn>1</mn></msub><msub><mi mathvariant=\"normal\">a</mi><mn>2</mn></msub></mfrac><mo>=</mo><mfrac><msub><mi mathvariant=\"normal\">b</mi><mn>1</mn></msub><msub><mi mathvariant=\"normal\">b</mi><mn>2</mn></msub></mfrac><mo>=</mo><mfrac><msub><mi mathvariant=\"normal\">c</mi><mn>1</mn></msub><msub><mi mathvariant=\"normal\">c</mi><mn>2</mn></msub></mfrac></mstyle></math><br>इस तरह, <br><math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mi>a</mi><mo>-</mo><mi>b</mi></mrow></mfrac></math> =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>1</mn><mrow><mi>a</mi><mo>+</mo><mi>b</mi></mrow></mfrac></mstyle></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mrow><mo>-</mo><mn>3</mn></mrow><mrow><mo>-</mo><mn>3</mn><mi>a</mi><mo>-</mo><mi>b</mi><mo>+</mo><mn>3</mn></mrow></mfrac></mstyle></math><br><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mi>a</mi><mo>+</mo><mi>b</mi></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mrow><mo>-</mo><mn>3</mn></mrow><mrow><mo>-</mo><mn>3</mn><mi>a</mi><mo>-</mo><mi>b</mi><mo>+</mo><mn>3</mn></mrow></mfrac></mstyle></math><br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>-</mo><mn>3</mn><mi mathvariant=\"normal\">a</mi><mo>-</mo><mi mathvariant=\"normal\">b</mi><mo>+</mo><mn>3</mn></math> = -3a - 3b <br>2b = -3 or b =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mrow><mo>-</mo><mn>3</mn></mrow><mn>2</mn></mfrac></mstyle></math> &hellip; (i)<br>अब,<br><math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mi>a</mi><mo>-</mo><mi>b</mi></mrow></mfrac></math> =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>1</mn><mrow><mi>a</mi><mo>+</mo><mi>b</mi></mrow></mfrac></mstyle></math><br>3<math display=\"inline\"><mi>a</mi></math> + 3b = a - b <br>2<math display=\"inline\"><mi>a</mi></math> + 4b = 0<br>2a&nbsp;+ 4 &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mrow><mo>-</mo><mn>3</mn></mrow><mn>2</mn></mfrac></mstyle></math> = 0<br>2a&nbsp;= 6 &rArr; a = 3<br>इसलिए, <math display=\"inline\"><mi>a</mi></math> = 3 और b =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mrow><mo>-</mo><mn>3</mn></mrow><mn>2</mn></mfrac></mstyle></math></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "76",
                    section: "7",
                    question_en: "<p>76. Parts of the following sentence have been given as options. Select the option that contains an error. <br>For the past / three years, / my uncle is /away from home</p>",
                    question_hi: "<p>76. Parts of the following sentence have been given as options. Select the option that contains an error. <br>For the past / three years, / my uncle is /away from home</p>",
                    options_en: ["<p>three years,</p>", "<p>away from home.</p>", 
                                "<p>For the past</p>", "<p>my uncle is</p>"],
                    options_hi: ["<p>three years,</p>", "<p>away from home.</p>",
                                "<p>For the past</p>", "<p>my uncle is</p>"],
                    solution_en: "<p>76.(d) my uncle is<br>&lsquo;My uncle is&rsquo; must be replaced with &lsquo;my uncle has been&rsquo;. Present perfect tense (Singular Sub. + has + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi mathvariant=\"normal\">V</mi><mn>3</mn></msub></math>) is used to indicate a state that has existed for some time. Hence, &lsquo;my uncle has been(<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi mathvariant=\"normal\">V</mi><mn>3</mn></msub></math>)&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>76.(d) my uncle is<br>&lsquo;My uncle is&rsquo; के स्थान पर &lsquo;my uncle has been&rsquo; का प्रयोग होना चाहिए। Present perfect tense ​​(Singular Sub. + has + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi mathvariant=\"normal\">V</mi><mn>3</mn></msub></math>) का प्रयोग किसी ऐसी स्थिति को इंगित करने के लिए किया जाता है जो कुछ समय से अस्तित्व में है। इसलिए, &lsquo;my uncle has been(<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi mathvariant=\"normal\">V</mi><mn>3</mn></msub></math>)&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "77",
                    section: "7",
                    question_en: "<p>77. Select the most appropriate segment to substitute the underlined words in the given sentence.<br><span style=\"text-decoration: underline;\">You have tears</span>, prepare to shed them now.</p>",
                    question_hi: "<p>77. Select the most appropriate segment to substitute the underlined words in the given sentence.<br><span style=\"text-decoration: underline;\">You have tears</span>, prepare to shed them now.</p>",
                    options_en: ["<p>Until you have tears</p>", "<p>Yet you have tears,</p>", 
                                "<p>Unless you have tears,</p>", "<p>If you have tears,</p>"],
                    options_hi: ["<p>Until you have tears</p>", "<p>Yet you have tears,</p>",
                                "<p>Unless you have tears,</p>", "<p>If you have tears,</p>"],
                    solution_en: "<p>77.(d) If you have tears<br>&lsquo;If&rsquo; is used to provide the condition under which something happens. Similarly, in the given sentence, having tears is the condition for shedding tears. Hence, &lsquo;If you have tears&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>77.(d) If you have tears<br>&lsquo;If&rsquo; का प्रयोग उस स्थिति को बताने के लिए किया जाता है जिसके तहत कुछ होता है। इसी तरह, दिए गए sentence में, having tears आँसू बहाने (shedding tears) की स्थिति है। इसलिए, &lsquo;If you have tears&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "78",
                    section: "7",
                    question_en: "<p>78. Select the most appropriate meaning of the underlined idiom in the given sentence.<br>When I got an opportunity to start a new business, Ravikanth <span style=\"text-decoration: underline;\">queered my pitch</span>.</p>",
                    question_hi: "<p>78. Select the most appropriate meaning of the underlined idiom in the given sentence.<br>When I got an opportunity to start a new business, Ravikanth <span style=\"text-decoration: underline;\">queered my pitch</span>.</p>",
                    options_en: ["<p>Ruined my plan</p>", "<p>Advised me</p>", 
                                "<p>Helped me by standing beside me</p>", "<p>Supported me with money</p>"],
                    options_hi: ["<p>Ruined my plan</p>", "<p>Advised me</p>",
                                "<p>Helped me by standing beside me</p>", "<p>Supported me with money</p>"],
                    solution_en: "<p>78.(a) <strong>Queered my pitch</strong>- ruined my plan.</p>",
                    solution_hi: "<p>78.(a) <strong>Queered my pitch- </strong>ruined my plan./योजना बर्बाद करना।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "79",
                    section: "7",
                    question_en: "<p>79. Select the most appropriate synonym of the underlined word in the given sentence.<br>He is just <span style=\"text-decoration: underline;\">jealous</span> that he didn&rsquo;t get picked for the job.</p>",
                    question_hi: "<p>79. Select the most appropriate synonym of the underlined word in the given sentence.<br>He is just <span style=\"text-decoration: underline;\">jealous</span> that he didn&rsquo;t get picked for the job.</p>",
                    options_en: ["<p>envious</p>", "<p>suspicious</p>", 
                                "<p>spiteful</p>", "<p>greedy</p>"],
                    options_hi: ["<p>envious</p>", "<p>suspicious</p>",
                                "<p>spiteful</p>", "<p>greedy</p>"],
                    solution_en: "<p>79.(a) <strong>Envious</strong>- desirous of what others have.<br><strong>Jealous-</strong> to feel threatened or insecure of something you already have.<br><strong>Suspicious</strong>- feeling that somebody has done something wrong, dishonest or illegal.<br><strong>Spiteful-</strong> having or showing a desire to hurt or annoy someone.<br><strong>Greedy-</strong> having or showing a selfish desire for wealth and possessions.</p>",
                    solution_hi: "<p>79.(a) <strong>Envious</strong> (ईर्ष्यालु) - desirous of what others have.<br><strong>Jealous</strong> (ईर्ष्यालु) - to feel threatened or insecure of something you already have.<br><strong>Suspicious</strong> (संदिग्ध) - feeling that somebody has done something wrong, dishonest or illegal.<br><strong>Spiteful</strong> (द्वेषपूर्ण) - having or showing a desire to hurt or annoy someone.<br><strong>Greedy</strong> (लालची) - having or showing a selfish desire for wealth and possessions.</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "80",
                    section: "7",
                    question_en: "<p>80. Choose the most appropriate option to change the voice (active / passive) form of the given sentence.<br>That too many cooks spoil the broth is known to all.</p>",
                    question_hi: "<p>80. Choose the most appropriate option to change the voice (active / passive) form of the given sentence.<br>That too many cooks spoil the broth is known to all.</p>",
                    options_en: ["<p>All know that too many cooks spoil the broth.</p>", "<p>All knew that too many cooks spoilt the broth.</p>", 
                                "<p>All know that the broth is spoilt by too many cooks.</p>", "<p>All knows that the broth is spoilt by too many cooks.</p>"],
                    options_hi: ["<p>All know that too many cooks spoil the broth.</p>", "<p>All knew that too many cooks spoilt the broth.</p>",
                                "<p>All know that the broth is spoilt by too many cooks.</p>", "<p>All knows that the broth is spoilt by too many cooks.</p>"],
                    solution_en: "<p>80.(a) All know that too many cooks spoil the broth.<br>(b). All <strong>knew</strong> that too many cooks spoilt the broth.(Incorrect word)<br>(c). All know that <strong>the broth is spoilt by too many cooks. (Incorrect structure)</strong><br>(d). All <strong>knows </strong>that the broth is spoilt by too many cooks.(Incorrect word)</p>",
                    solution_hi: "<p>80.(a) All know that too many cooks spoil the broth.<br>(b). All <strong>knew</strong> that too many cooks spoilt the broth.(गलत word)<br>(c). All know that <strong>the broth is spoilt by too many cooks</strong>. (गलत वाक्य संरचना)<br>(d). All <strong>knows</strong> that the broth is spoilt by too many cooks.(गलत word)</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "81",
                    section: "7",
                    question_en: "<p>81. Given below are four jumbled sentences. Pick the option that gives their correct order.<br>P: The reward for the watching eye and the listening ear is great. <br>Q: In zoos, as in nature, the best times to visit are sunrise and sunset as that is when most animals come to life.&nbsp;<br>R: I spent many hours witnessing the manifold expressions of life that grace our planet.<br>S: They stir, leave their shelter, show their raiment\'s and sing their songs.</p>",
                    question_hi: "<p>81. Given below are four jumbled sentences. Pick the option that gives their correct order.<br>P: The reward for the watching eye and the listening ear is great. <br>Q: In zoos, as in nature, the best times to visit are sunrise and sunset as that is when most animals come to life. <br>R: I spent many hours witnessing the manifold expressions of life that grace our planet.<br>S: They stir, leave their shelter, show their raiment\'s and sing their songs.</p>",
                    options_en: ["<p>PSQR</p>", "<p>RQPS</p>", 
                                "<p>PQRS</p>", "<p>QSPR</p>"],
                    options_hi: ["<p>PSQR</p>", "<p>RQPS</p>",
                                "<p>PQRS</p>", "<p>QSPR</p>"],
                    solution_en: "<p>81.(d) QSPR<br>Q will be the first part as it tells us the subject of the parajumble that is -what is the best time to visit the zoo. It will be followed by S as S gives the reason why it is the best time to visit . \'That\' refers to the time of sunrise and sunset and tells why it is the best time. Next will be P followed by R. The views then were so mesmerizing that the speaker spent hours watching them.The answer is (d) QSPR</p>",
                    solution_hi: "<p>81.(d) QSPR<br>Q पहला भाग होगा क्योंकि यह हमें parajumble का विषय बताता है - चिड़ियाघर जाने का सबसे अच्छा समय क्या है। इसके बाद S होगा क्योंकि S इसका कारण बताता है कि यह घूमने का सबसे अच्छा समय क्यों है।Q पहला भाग होगा जिसके तुरंत बाद S होगा। \'That\' सूर्योदय और सूर्यास्त के समय को संदर्भित करता है और बताता है कि यह सबसे अच्छा समय क्यों है। इसके बाद P और उसके बाद R होगा। उस समय के नज़ारे इतने मंत्रमुग्ध कर देने वाले थे कि speaker घंटों उन्हें देखते रहे। Option (d) QSPR उत्तर है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "82",
                    section: "7",
                    question_en: "<p>82. Select the INCORRECTLY spelt word.</p>",
                    question_hi: "<p>82. Select the INCORRECTLY spelt word.</p>",
                    options_en: ["<p>Ommision</p>", "<p>License</p>", 
                                "<p>Aspirant</p>", "<p>Autonomous</p>"],
                    options_hi: ["<p>Ommision</p>", "<p>License</p>",
                                "<p>Aspirant</p>", "<p>Autonomous</p>"],
                    solution_en: "<p>82.(a) Ommision<br>&lsquo;Omission&rsquo; is the correct spelling.</p>",
                    solution_hi: "<p>82.(a) Ommision<br>&lsquo;Omission&rsquo; सही spelling है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "83",
                    section: "7",
                    question_en: "<p>83. Select the most appropriate meaning of the given idiom.<br>Spill the beans</p>",
                    question_hi: "<p>83. Select the most appropriate meaning of the given idiom.<br>Spill the beans</p>",
                    options_en: ["<p>The act of loving beans</p>", "<p>To help someone extraordinarily</p>", 
                                "<p>To leak a secret</p>", "<p>Cook the beans carefully</p>"],
                    options_hi: ["<p>The act of loving beans</p>", "<p>To help someone extraordinarily</p>",
                                "<p>To leak a secret</p>", "<p>Cook the beans carefully</p>"],
                    solution_en: "<p>83.(c) <strong>Spill the beans- </strong>to leak a secret.<br>E.g.- Please don\'t spill the beans about the new product launch until the official announcement.</p>",
                    solution_hi: "<p>83.(c) <strong>Spill the beans -</strong> to leak a secret./किसी रहस्य का खुलासा करना।<br>E.g.- Please don\'t spill the beans about the new product launch until the official announcement.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "84",
                    section: "7",
                    question_en: "<p>84. Choose the most appropriate option to change the narration (direct / indirect) of the given sentence.<br>The teacher said to him, &ldquo;Do not go out.&rdquo;</p>",
                    question_hi: "<p>84. Choose the most appropriate option to change the narration (direct / indirect) of the given sentence.<br>The teacher said to him, &ldquo;Do not go out.&rdquo;</p>",
                    options_en: ["<p>The teacher commanded him that he should not go out.</p>", "<p>The teacher told him do not go out.</p>", 
                                "<p>The teacher ordered him to not to go out.</p>", "<p>The teacher advised him not to go out.</p>"],
                    options_hi: ["<p>The teacher commanded him that he should not go out.</p>", "<p>The teacher told him do not go out.</p>",
                                "<p>The teacher ordered him to not to go out.</p>", "<p>The teacher advised him not to go out.</p>"],
                    solution_en: "<p>84.(d) The teacher advised him not to go out.<br>(a) The teacher commanded him<strong> <span style=\"text-decoration: underline;\">that</span> </strong>he should not go out. <strong>(Incorrect connecting word)</strong><br>(b) The teacher told him <strong><span style=\"text-decoration: underline;\">do not</span></strong> go out. <strong>(Incorrect word)</strong><br>(c) The teacher ordered him <strong><span style=\"text-decoration: underline;\">to not to</span></strong> go out. <strong>(Incorrect structure)</strong></p>",
                    solution_hi: "<p>84.(d) The teacher advised him not to go out.<br>(a) The teacher commanded him <strong><span style=\"text-decoration: underline;\">that</span></strong> he should not go out. <strong>(गलत connecting word)</strong><br>(b) The teacher told him <span style=\"text-decoration: underline;\"><strong>do not</strong></span> go out. <strong>(गलत word)</strong><br>(c) The teacher ordered him <span style=\"text-decoration: underline;\"><strong>to not to</strong></span> go out. <strong>(गलत वाक्य संरचना )</strong></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "85",
                    section: "7",
                    question_en: "<p>85. Select the most appropriate ANTONYM of the underlined word in the following sentence.</p>\n<p>There is also an increase in&nbsp;<span style=\"text-decoration: underline;\">juvenile</span> delinquency.</p>",
                    question_hi: "<p>85. Select the most appropriate ANTONYM of the underlined word in the following sentence.</p>\n<p>There is also an increase in <span style=\"text-decoration: underline;\">juvenile</span> delinquency.</p>",
                    options_en: ["<p>Minor</p>", "<p>Teenage</p>", 
                                "<p>Puerile</p>", "<p>Adult</p>"],
                    options_hi: ["<p>Minor</p>", "<p>Teenage</p>",
                                "<p>Puerile</p>", "<p>Adult</p>"],
                    solution_en: "<p>85.(d) <strong>Adult-</strong> a person who is fully grown or developed.<br><strong>Juvenile-</strong> for or relating to young people.<br><strong>Minor- </strong>lesser in importance or seriousness. <br><strong>Teenage-</strong> aged between 13 and 19.<br><strong>Puerile- </strong>behaving in a silly way like a child.</p>",
                    solution_hi: "<p>85.(d) <strong>Adult</strong> (वयस्क) - a person who is fully grown or developed.<br><strong>Juvenile</strong> (किशोर) - for or relating to young people.<br><strong>Minor</strong> (नाबालिग) - lesser in importance or seriousness. <br><strong>Teenage </strong>(किशोर) - aged between 13 and 19.<br><strong>Puerile </strong>(बालिग) - behaving in a silly way like a child.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "86",
                    section: "7",
                    question_en: "<p>86. Select the option that can be used as a one-word substitute for the given group of words. <br>A person who is inclined to see the worst aspect of things</p>",
                    question_hi: "<p>86. Select the option that can be used as a one-word substitute for the given group of words. <br>A person who is inclined to see the worst aspect of things</p>",
                    options_en: ["<p>Pessimist</p>", "<p>Philanthropist</p>", 
                                "<p>Optimist</p>", "<p>Altruist</p>"],
                    options_hi: ["<p>Pessimist</p>", "<p>Philanthropist</p>",
                                "<p>Optimist</p>", "<p>Altruist</p>"],
                    solution_en: "<p>86.(a) <strong>Pessimist-</strong> a person who is inclined to see the worst aspect of things.<br><strong>Philanthropist</strong>- a person who seeks to promote the welfare of others, especially by the generous donation of money to good causes.<br><strong>Optimist- </strong>a person who is inclined to be hopeful and to expect good outcomes.<br><strong>Altruist-</strong> a person who cares about others and helps them despite not gaining anything by doing this.</p>",
                    solution_hi: "<p>86.(a) <strong>Pessimist</strong> (निराशावादी)- a person who is inclined to see the worst aspect of things.<br><strong>Philanthropist</strong> (परोपकारी)- a person who seeks to promote the welfare of others, especially by the generous donation of money to good causes.<br><strong>Optimist</strong> (आशावादी)- a person who is inclined to be hopeful and to expect good outcomes.<br><strong>Altruist </strong>(परोपकारी)- a person who cares about others and helps them despite not gaining anything by doing this.</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "87",
                    section: "7",
                    question_en: "<p>87. Select the option that can be used as a one-word substitute for the given group of words. <br>Loud enough to be heard.</p>",
                    question_hi: "<p>87. Select the option that can be used as a one-word substitute for the given group of words. <br>Loud enough to be heard.</p>",
                    options_en: ["<p>Audible</p>", "<p>Audit</p>", 
                                "<p>Audacious</p>", "<p>Audacity</p>"],
                    options_hi: ["<p>Audible</p>", "<p>Audit</p>",
                                "<p>Audacious</p>", "<p>Audacity</p>"],
                    solution_en: "<p>87.(a) <strong>Audible-</strong> loud enough to be heard.<br><strong>Audit-</strong> an official inspection of an organization\'s accounts, typically by an independent body.<br><strong>Audacious-</strong> showing a willingness to take surprisingly bold risks.<br><strong>Audacity-</strong> a willingness to take bold risks.</p>",
                    solution_hi: "<p>87.(a) <strong>Audible </strong>(सुनाई देने योग्य)- loud enough to be heard.<br><strong>Audit</strong> (लेखा परीक्षा)- an official inspection of an organization\'s accounts, typically by an independent body.<br><strong>Audacious</strong> (साहसी)- showing a willingness to take surprisingly bold risks.<br><strong>Audacity</strong> (साहसिकता)- a willingness to take bold risks.</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "88",
                    section: "7",
                    question_en: "<p>88. Select the most appropriate synonym of the given word.<br>Erudite</p>",
                    question_hi: "<p>88. Select the most appropriate synonym of the given word.<br>Erudite</p>",
                    options_en: ["<p>Honest</p>", "<p>Humble</p>", 
                                "<p>Strong</p>", "<p>Educated</p>"],
                    options_hi: ["<p>Honest</p>", "<p>Humble</p>",
                                "<p>Strong</p>", "<p>Educated</p>"],
                    solution_en: "<p>88.(d) <strong>Educated- </strong>having received a good level of schooling or training.<br><strong>Erudite</strong>- having or showing great knowledge.<br><strong>Honest</strong>- truthful and sincere.<br><strong>Humble-</strong> having a modest view of one\'s importance.<br><strong>Strong-</strong> having great physical power or strength.</p>",
                    solution_hi: "<p>88.(d)<strong> Educated </strong>(शिक्षित) - having received a good level of schooling or training.<br><strong>Erudite</strong> (विद्वान) - having or showing great knowledge.<br><strong>Honest </strong>(ईमानदार) - truthful and sincere.<br><strong>Humble</strong> (विनम्र) - having a modest view of one\'s importance.<br><strong>Strong</strong> (बलवान) - having great physical power or strength.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "89",
                    section: "7",
                    question_en: "<p>89. Select the most appropriate ANTONYM of the given word.<br>Merit</p>",
                    question_hi: "<p>89. Select the most appropriate ANTONYM of the given word.<br>Merit</p>",
                    options_en: ["<p>Occult</p>", "<p>Tumult</p>", 
                                "<p>Fault</p>", "<p>Result</p>"],
                    options_hi: ["<p>Occult</p>", "<p>Tumult</p>",
                                "<p>Fault</p>", "<p>Result</p>"],
                    solution_en: "<p>89.(c) <strong>Fault- </strong>mistake.<br><strong>Merit-</strong> the quality of being particularly good.<br><strong>Occult- </strong>related to hidden or mysterious knowledge.<br><strong>Tumult-</strong> a loud, confused noise, especially one caused by a large mass of people.</p>",
                    solution_hi: "<p>89.(c) <strong>Fault</strong> (दोष) - mistake.<br><strong>Merit</strong> (योग्यता) - the quality of being particularly good.<br><strong>Occult</strong> (रहस्यमय) - related to hidden or mysterious knowledge.<br><strong>Tumult</strong> (शोरगुल) - a loud, confused noise, especially one caused by a large mass of people.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "90",
                    section: "7",
                    question_en: "<p>90. Select the most appropriate option to fill in the blank.<br>He _________ a long distance.</p>",
                    question_hi: "<p>90. Select the most appropriate option to fill in the blank.<br>He _________ a long distance.</p>",
                    options_en: ["<p>running</p>", "<p>run</p>", 
                                "<p>ran</p>", "<p>runned</p>"],
                    options_hi: ["<p>running</p>", "<p>run</p>",
                                "<p>ran</p>", "<p>runned</p>"],
                    solution_en: "<p>90.(c) ran<br>&lsquo;Ran&rsquo; is the correct verb to use here. &lsquo;Run&rsquo; is the past form of &lsquo;run&rsquo;, indicating simple past tense. Hence, &lsquo;ran&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>90.(c) ran<br>दिए गए sentence के लिए &lsquo;Ran&rsquo;, correct verb है। &lsquo;Ran&rsquo;, &lsquo;run&rsquo; का past form है, जो कि simple past tense को दर्शाता है। अतः, &lsquo;ran&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "91",
                    section: "7",
                    question_en: "<p>91.<strong>Cloze Test</strong><br>Each child deserves love and care. The child who is _____(91) for what he is even if he is slow or ______(92) , will grow with confidence in himself. He will ______(93) the ability to make the best use of the _____(94) which come his way and make light of any______ (95) that maypose difficulties in his path.<br>Select the most appropriate option to fill in the blank no.91</p>",
                    question_hi: "<p>91. <strong>Cloze Test</strong><br>Each child deserves love and care. The child who is _____(91) for what he is even if he is slow or ______(92) , will grow with confidence in himself. He will ______(93) the ability to make the best use of the _____(94) which come his way and make light of any______ (95) that maypose difficulties in his path.<br>Select the most appropriate option to fill in the blank no.91</p>",
                    options_en: ["<p>appreciated</p>", "<p>thanked</p>", 
                                "<p>rebuked</p>", "<p>jeered</p>"],
                    options_hi: ["<p>appreciated</p>", "<p>thanked</p>",
                                "<p>rebuked</p>", "<p>jeered</p>"],
                    solution_en: "<p>91.(a) Appreciated <br><strong>Appreciated</strong> - to understand the value of somebody/something<br><strong>Thanked </strong>- to tell somebody that you are grateful<br><strong>Rebuked</strong> - to speak angrily to somebody because he/she has done something wrong<br><strong>Jeered</strong> - make rude and mocking remarks, typically in a loud voice.<br>Option (a) is fit to the context of the sentence.</p>",
                    solution_hi: "<p>91.(a) Appreciated <br><strong>Appreciated</strong> - किसी की सराहना करना । <br><strong>Thanked -</strong> शुक्रिया अदा करना। <br><strong>Rebuked</strong> - कुछ गलत करने पर उससे गुस्से में बात करना । <br><strong>Jeered </strong>- आमतौर पर तेज आवाज में उपहासपूर्ण टिप्पणी करना।<br>विकल्प (a) वाक्य के संदर्भ के लिए उपयुक्त है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "92",
                    section: "7",
                    question_en: "<p>92. <strong>Cloze Test:</strong><br>Each child deserves love and care. The child who is _____(91) for what he is even if he is slow or ______(92) , will grow with confidence in himself. He will ______(93) the ability to make the best use of the _____(94) which come his way and make light of any______ (95) that maypose difficulties in his path.<br>Select the most appropriate option to fill in the blank no.92</p>",
                    question_hi: "<p>92. <strong>Cloze Test:</strong><br>Each child deserves love and care. The child who is _____(91) for what he is even if he is slow or ______(92) , will grow with confidence in himself. He will ______(93) the ability to make the best use of the _____(94) which come his way and make light of any______ (95) that maypose difficulties in his path.<br>Select the most appropriate option to fill in the blank no.92</p>",
                    options_en: ["<p>clumsy</p>", "<p>capable</p>", 
                                "<p>clever</p>", "<p>courteous</p>"],
                    options_hi: ["<p>clumsy</p>", "<p>capable</p>",
                                "<p>clever</p>", "<p>courteous</p>"],
                    solution_en: "<p>92.(a) Clumsy<br><strong>Clumsy</strong> - awkward in movement or in handling things.<br><strong>Capable</strong> -having the ability, fitness, or quality necessary to do or achieve a specified thing.<br><strong>Clever </strong>-quick to understand, learn, and devise or apply ideas<br><strong>Courteous </strong>- polite, respectful, or considerate in manner.</p>",
                    solution_hi: "<p>92.(a) <strong>Clumsy</strong><br><strong>Clumsy</strong> - संचार में या चीजों को संभालने में अजीब लगना <br><strong>Capable</strong> -किसी निर्दिष्ट चीज़ को करने या हासिल करने के लिए आवश्यक क्षमता, स्वास्थ्य या गुणवत्ता होना।<br><strong>Clever</strong> - तीव्रता से विचारों को समझने, सीखने और लागू करना । <br><strong>Courteous </strong>- विनम्र या सम्मानजनक।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "93",
                    section: "7",
                    question_en: "<p>93. <strong>Cloze Test:</strong><br>Each child deserves love and care. The child who is _____(91) for what he is even if he is slow or ______(92) , will grow with confidence in himself. He will ______(93) the ability to make the best use of the _____(94) which come his way and make light of any______ (95) that maypose difficulties in his path.<br>Select the most appropriate option to fill in the blank no.93</p>",
                    question_hi: "<p>93. <strong>Cloze Test:</strong><br>Each child deserves love and care. The child who is _____(91) for what he is even if he is slow or ______(92) , will grow with confidence in himself. He will ______(93) the ability to make the best use of the _____(94) which come his way and make light of any______ (95) that maypose difficulties in his path.<br>Select the most appropriate option to fill in the blank no.93</p>",
                    options_en: ["<p>maintain</p>", "<p>develop</p>", 
                                "<p>conceal</p>", "<p>consider</p>"],
                    options_hi: ["<p>maintain</p>", "<p>develop</p>",
                                "<p>conceal</p>", "<p>consider </p>"],
                    solution_en: "<p>93.(b) Develop <br><strong>Develop </strong>- grow or cause to grow and become more mature, advanced, or elaborate.<br><strong>Maintain</strong> - cause or enable (a condition or situation) to continue.<br>Conceal - not allow to be seen<br>Consider - Think carefully about (something or someone), usually before making a decision.<br>According to the context of the sentence, option (b) is the answer.</p>",
                    solution_hi: "<p>93.(b) Develop <br><strong>Develop </strong>- परिपक्व या विस्तृत हो जाना।<br><strong>Maintain </strong>- जारी रखने के लिए कारण या सक्षम (एक शर्त या स्थिति)<br>Conceal - देखने की अनुमति नहीं<br>Consider - आमतौर पर निर्णय लेने से पहले (कोई चीज़ या कोई बात ) ध्यान से सोचें।<br>वाक्य के संदर्भ के अनुसार विकल्प (b) उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "94",
                    section: "7",
                    question_en: "<p>94.<strong> Cloze Test:</strong><br>Each child deserves love and care. The child who is _____(91) for what he is even if he is slow or ______(92) , will grow with confidence in himself. He will ______(93) the ability to make the best use of the _____(94) which come his way and make light of any______ (95) that maypose difficulties in his path.<br>Select the most appropriate option to fill in the blank no.94</p>",
                    question_hi: "<p>94. <strong>Cloze Test:</strong><br>Each child deserves love and care. The child who is _____(91) for what he is even if he is slow or ______(92) , will grow with confidence in himself. He will ______(93) the ability to make the best use of the _____(94) which come his way and make light of any______ (95) that maypose difficulties in his path.<br>Select the most appropriate option to fill in the blank no.94</p>",
                    options_en: ["<p>opportunities</p>", "<p>dreams</p>", 
                                "<p>favours</p>", "<p>threats</p>"],
                    options_hi: ["<p>opportunities</p>", "<p>dreams</p>",
                                "<p>favours</p>", "<p>threats</p>"],
                    solution_en: "<p>94.(a) Opportunities <br><strong>Opportunities</strong> - a time or SET of circumstances that makes it possible to do something.<br><strong>Dreams </strong>-a series of thoughts, images, and sensations occurring in a person\'s mind during sleep.<br><strong>Favours</strong> -approval, support, or liking for someone or something.<br>Threats - a person or thing likely to cause damage or danger.<br>Option (a) is fit to the context of the passage.</p>",
                    solution_hi: "<p>94.(a) Opportunities <br><strong>Opportunities</strong> - परिस्थितियों का एक समय या समूह जो कुछ करना संभव बनाता है।<br><strong>Dreams -</strong> नींद के दौरान किसी व्यक्ति के दिमाग में होने वाले विचारों, छवियों और संवेदनाओं की एक श्रृंखला।<br><strong>Favours - </strong>किसी चीज़ के लिए अनुमोदन, समर्थन या पसंद करना।<br><strong>Threats -</strong>किसी व्यक्ति या वस्तु को नुकसान या खतरा होने की संभावना।<br>विकल्प (a) passage के संदर्भ में उपयुक्त है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "95",
                    section: "7",
                    question_en: "<p>95. <strong>Cloze Test:</strong><br>Each child deserves love and care. The child who is _____(91) for what he is even if he is slow or ______(92) , will grow with confidence in himself. He will ______(93) the ability to make the best use of the _____(94) which come his way and make light of any______ (95) that maypose difficulties in his path.<br>Select the most appropriate option to fill in the blank no.95</p>",
                    question_hi: "<p>95. <strong>Cloze Test:</strong><br>Each child deserves love and care. The child who is _____(91) for what he is even if he is slow or ______(92) , will grow with confidence in himself. He will ______(93) the ability to make the best use of the _____(94) which come his way and make light of any______ (95) that maypose difficulties in his path.<br>Select the most appropriate option to fill in the blank no.95</p>",
                    options_en: ["<p>consequences</p>", "<p>intrusions</p>", 
                                "<p>prohibition</p>", "<p>handicaps</p>"],
                    options_hi: ["<p>consequences</p>", "<p>intrusions</p>",
                                "<p>prohibition</p>", "<p>handicaps</p>"],
                    solution_en: "<p>95.(d) handicaps<br>Handicaps - a circumstance that makes progress or success difficult.<br>Consequences - a result or effect, typically one that is unwelcome or unpleasant<br>Intrusions - the action of intruding.<br>Prohibition - the action of forbidding something, especially by law. <br>So , option (d) is the most logical answer.</p>",
                    solution_hi: "<p>95.(d) handicaps<br>Handicaps -ऐसी परिस्थिति जो प्रगति या सफलता को कठिन बनाती है।<br>Consequences - एक परिणाम या प्रभाव, आमतौर पर जो अप्रिय होता है।<br>Intrusions - घुसपैठ की क्रिया। <br>Prohibition - विशेष रूप से कानून द्वारा किसी चीज को मना करने की क्रिया। <br>विकल्प (d) सबसे तार्किक उत्तर है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "96",
                    section: "7",
                    question_en: "<p>96. <strong>Comprehension :-</strong><br>Indian rivers play an integral part in enriching the value and heavenly beauty of India. They are our greatest natural assets. There are hundreds of them watering our land and making it green and fertile; they even flow as natural highways linking the towns with the villages. The most eye-catching variety of these rivers is that there are navigable rivers, especially in Bengal and Kerala. Even raging torrents which leap from the mountains down to the deep gorges. Some huge rivers sometimes burst their banks and flood the countryside. And in tribute to their strength and beauty, we have always held them sacred. To love India is to love her rivers, big and small.<br>Select the most appropriate option to fill in the blank.<br>I found it __________ that some people hate rivers.</p>",
                    question_hi: "<p>96. <strong>Comprehension :-</strong><br>Indian rivers play an integral part in enriching the value and heavenly beauty of India. They are our greatest natural assets. There are hundreds of them watering our land and making it green and fertile; they even flow as natural highways linking the towns with the villages. The most eye-catching variety of these rivers is that there are navigable rivers, especially in Bengal and Kerala. Even raging torrents which leap from the mountains down to the deep gorges. Some huge rivers sometimes burst their banks and flood the countryside. And in tribute to their strength and beauty, we have always held them sacred. To love India is to love her rivers, big and small.<br>Select the most appropriate option to fill in the blank.<br>I found it __________ that some people hate rivers.</p>",
                    options_en: ["<p>peculiar</p>", "<p>sacred</p>", 
                                "<p>gorgeous</p>", "<p>tribute</p>"],
                    options_hi: ["<p>peculiar</p>", "<p>sacred</p>",
                                "<p>gorgeous</p>", "<p>tribute</p>"],
                    solution_en: "<p>96.(a) peculiar<br>The given passage talks about the importance of Indian rivers, describing them as natural assets that provide water, fertility, and beauty to the land. Therefore, I found it peculiar that some people hate rivers.</p>",
                    solution_hi: "<p>96.(a) peculiar<br>दिए गए passage में Indian rivers के importance के बारे में बताया गया है, उन्हें प्राकृतिक संपत्ति(natural assets) के रूप में describe किया गया है जो भूमि को जल, उर्वरता(fertility) एवं सुंदरता प्रदान करती हैं। इसलिए, मुझे यह अजीब(peculiar) लगा कि कुछ लोग नदियों से नफरत करते हैं।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "97",
                    section: "7",
                    question_en: "<p>97. <strong>Comprehension :-</strong><br>Indian rivers play an integral part in enriching the value and heavenly beauty of India. They are our greatest natural assets. There are hundreds of them watering our land and making it green and fertile; they even flow as natural highways linking the towns with the villages. The most eye-catching variety of these rivers is that there are navigable rivers, especially in Bengal and Kerala. Even raging torrents which leap from the mountains down to the deep gorges. Some huge rivers sometimes burst their banks and flood the countryside. And in tribute to their strength and beauty, we have always held them sacred. To love India is to love her rivers, big and small.<br>What are the two words highlighted in the passage that describe the vital role of rivers in the development of the agriculture sector ?</p>",
                    question_hi: "<p>97. <strong>Comprehension :-</strong><br>Indian rivers play an integral part in enriching the value and heavenly beauty of India. They are our greatest natural assets. There are hundreds of them watering our land and making it green and fertile; they even flow as natural highways linking the towns with the villages. The most eye-catching variety of these rivers is that there are navigable rivers, especially in Bengal and Kerala. Even raging torrents which leap from the mountains down to the deep gorges. Some huge rivers sometimes burst their banks and flood the countryside. And in tribute to their strength and beauty, we have always held them sacred. To love India is to love her rivers, big and small.<br>What are the two words highlighted in the passage that describe the vital role of rivers in the development of the agriculture sector ?</p>",
                    options_en: ["<p>Burst and flood</p>", "<p>Watering and fertile</p>", 
                                "<p>Leaping torrents and navigable rivers</p>", "<p>Countryside and villages</p>"],
                    options_hi: ["<p>Burst and flood</p>", "<p>Watering and fertile</p>",
                                "<p>Leaping torrents and navigable rivers</p>", "<p>Countryside and villages</p>"],
                    solution_en: "<p>97.(b) Watering and fertile<br>(Line/s from the passage- There are hundreds of them watering our land and making it green and fertile;)</p>",
                    solution_hi: "<p>97.(b) Watering and fertile<br>(Passage से ली गई line/s - There are hundreds of them watering our land and making it green and fertile;/इनमें से सैकड़ों हमारी भूमि को सींच रहे हैं और उसे हरा-भरा एवं उपजाऊ बना रहे हैं)</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "98",
                    section: "7",
                    question_en: "<p>98. <strong>Comprehension :-</strong><br>Indian rivers play an integral part in enriching the value and heavenly beauty of India. They are our greatest natural assets. There are hundreds of them watering our land and making it green and fertile; they even flow as natural highways linking the towns with the villages. The most eye-catching variety of these rivers is that there are navigable rivers, especially in Bengal and Kerala. Even raging torrents which leap from the mountains down to the deep gorges. Some huge rivers sometimes burst their banks and flood the countryside. And in tribute to their strength and beauty, we have always held them sacred. To love India is to love her rivers, big and small.<br>Why are the rivers called natural highways ?</p>",
                    question_hi: "<p>98. <strong>Comprehension :-</strong><br>Indian rivers play an integral part in enriching the value and heavenly beauty of India. They are our greatest natural assets. There are hundreds of them watering our land and making it green and fertile; they even flow as natural highways linking the towns with the villages. The most eye-catching variety of these rivers is that there are navigable rivers, especially in Bengal and Kerala. Even raging torrents which leap from the mountains down to the deep gorges. Some huge rivers sometimes burst their banks and flood the countryside. And in tribute to their strength and beauty, we have always held them sacred. To love India is to love her rivers, big and small.<br>Why are the rivers called natural highways ?</p>",
                    options_en: ["<p>Because they link the towns and villages</p>", "<p>Because they avoid overflow and flood</p>", 
                                "<p>Because national highways are built alongside their banks</p>", "<p>Because they enrich the beauty of Bengal and Kerala</p>"],
                    options_hi: ["<p>Because they link the towns and villages</p>", "<p>Because they avoid overflow and flood</p>",
                                "<p>Because national highways are built alongside their banks</p>", "<p>Because they enrich the beauty of Bengal and Kerala</p>"],
                    solution_en: "<p>98.(a) Because they link the towns and villages<br>(Line/s from the passage- they even flow as natural highways linking the towns with the villages.)</p>",
                    solution_hi: "<p>98.(a) Because they link the towns and villages<br>(Passage से ली गई line/s - they even flow as natural highways linking the towns with the villages./यहां तक कि वे कस्बों को गांवों से जोड़ने वाले प्राकृतिक राजमार्गों के रूप में भी बहते हैं।)</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "99",
                    section: "7",
                    question_en: "<p>99. <strong>Comprehension :-</strong><br>Indian rivers play an integral part in enriching the value and heavenly beauty of India. They are our greatest natural assets. There are hundreds of them watering our land and making it green and fertile; they even flow as natural highways linking the towns with the villages. The most eye-catching variety of these rivers is that there are navigable rivers, especially in Bengal and Kerala. Even raging torrents which leap from the mountains down to the deep gorges. Some huge rivers sometimes burst their banks and flood the countryside. And in tribute to their strength and beauty, we have always held them sacred. To love India is to love her rivers, big and small.<br>What are the characteristics of rivers in India ?</p>",
                    question_hi: "<p>99. <strong>Comprehension :-</strong><br>Indian rivers play an integral part in enriching the value and heavenly beauty of India. They are our greatest natural assets. There are hundreds of them watering our land and making it green and fertile; they even flow as natural highways linking the towns with the villages. The most eye-catching variety of these rivers is that there are navigable rivers, especially in Bengal and Kerala. Even raging torrents which leap from the mountains down to the deep gorges. Some huge rivers sometimes burst their banks and flood the countryside. And in tribute to their strength and beauty, we have always held them sacred. To love India is to love her rivers, big and small.<br>What are the characteristics of rivers in India ?</p>",
                    options_en: ["<p>Watering, navigable, sacred, huge, and small</p>", "<p>Destroyable, tiny, pond-sized</p>", 
                                "<p>Storable, impure, negligible</p>", "<p>Reverse flowing, unflawed, useless</p>"],
                    options_hi: ["<p>Watering, navigable, sacred, huge, and small</p>", "<p>Destroyable, tiny, pond-sized</p>",
                                "<p>Storable, impure, negligible</p>", "<p>Reverse flowing, unflawed, useless</p>"],
                    solution_en: "<p>99.(a) Watering, navigable, sacred, huge, and small.<br>It can be inferred from the passage that option (a) gives the characteristics of rivers in India.</p>",
                    solution_hi: "<p>99.(a) Watering, navigable, sacred, huge, and small.<br>दिए गए Passage से यह अनुमान लगाया जा सकता है कि option (a) भारत में नदियों की विशेषताओं को बताता है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "100",
                    section: "misc",
                    question_en: "<p>100.<strong> Comprehension :-</strong><br>Indian rivers play an integral part in enriching the value and heavenly beauty of India. They are our greatest natural assets. There are hundreds of them watering our land and making it green and fertile; they even flow as natural highways linking the towns with the villages. The most eye-catching variety of these rivers is that there are navigable rivers, especially in Bengal and Kerala. Even raging torrents which leap from the mountains down to the deep gorges. Some huge rivers sometimes burst their banks and flood the countryside. And in tribute to their strength and beauty, we have always held them sacred. To love India is to love her rivers, big and small.<br>Select the most appropriate word from the passage to fill in the blank.<br>Why do Indians hold their rivers ________?</p>",
                    question_hi: "<p>100. <strong>Comprehension :-</strong><br>Indian rivers play an integral part in enriching the value and heavenly beauty of India. They are our greatest natural assets. There are hundreds of them watering our land and making it green and fertile; they even flow as natural highways linking the towns with the villages. The most eye-catching variety of these rivers is that there are navigable rivers, especially in Bengal and Kerala. Even raging torrents which leap from the mountains down to the deep gorges. Some huge rivers sometimes burst their banks and flood the countryside. And in tribute to their strength and beauty, we have always held them sacred. To love India is to love her rivers, big and small.<br>Select the most appropriate word from the passage to fill in the blank.<br>Why do Indians hold their rivers ________?</p>",
                    options_en: ["<p>tribute</p>", "<p>sacred</p>", 
                                "<p>navigable</p>", "<p>variety</p>"],
                    options_hi: ["<p>tribute</p>", "<p>sacred</p>",
                                "<p>navigable</p>", "<p>variety</p>"],
                    solution_en: "<p>100.(b) sacred<br>This question comes from the line &lsquo;And in tribute to their strength and beauty, we have always held them sacred.&rsquo;</p>",
                    solution_hi: "<p>100.(b) sacred<br>दिया गया question इस line &lsquo;And in tribute to their strength and beauty, we have always held them sacred.&rsquo; से लिया गया है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>