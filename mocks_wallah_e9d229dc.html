<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">30:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 26</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">26</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 30 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["6"] = {
                name: "Reasoning",
                start: 0,
                end: 24
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="6">Reasoning</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 25,
                end: 25
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "6",
                    question_en: "<p>1. Four friends, Ankita, Shruti, Payal and Mahak, are sitting at a rectangular table, each at one corner. Ankita is sitting to the right of Shruti, who is sitting diagonally opposite to Payal. Where is Ankita sitting with respect to Mahak ?</p>",
                    question_hi: "<p>1. चार मित्र अंकिता, श्रुति, पायल और महक प्रत्येक एक कोने पर एक आयताकार मेज पर बैठे हैं। अंकिता श्रुति के दायें बैठी है, जो पायल के तिर्यक रूप से सामने बैठी है। अंकिता महक के सन्दर्भ में कहाँ बैठी है ?</p>",
                    options_en: ["<p>Directly opposite</p>", "<p>Diagonally opposite</p>", 
                                "<p>Immediate right</p>", "<p>Immediate left</p>"],
                    options_hi: ["<p>सीधे सामने</p>", "<p>विकर्ण रूप से सामने</p>",
                                "<p>तत्काल दायें</p>", "<p>तत्काल बाएं</p>"],
                    solution_en: "<p>1.(b)</p>\n<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728968379063.png\" alt=\"rId5\" width=\"103\" height=\"104\"></p>",
                    solution_hi: "<p>1.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728968379231.png\" alt=\"rId6\" width=\"102\" height=\"113\"></p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "2",
                    section: "6",
                    question_en: "<p>2. Four words have been given, out of which three are alike in some manner and one is different, Select the odd one out:<br>Plateau, Glacier, Valley,Hill</p>",
                    question_hi: "<p>2. चार शब्द दिए गए हैं, जिनमें से तीन किसी तरह से एक जैसे हैं और एक अलग है, विजातीय को चुनिए<br>घाटी, पठार, ग्लेशियर, पहाड़ी</p>",
                    options_en: ["<p>Plateau</p>", "<p>Glacier</p>", 
                                "<p>Valley</p>", "<p>Hill</p>"],
                    options_hi: ["<p>पठार</p>", "<p>हिमनद</p>",
                                "<p>घाटी</p>", "<p>पहाड़ी</p>"],
                    solution_en: "<p>2.(b)<br>Glaciers are Found in the ocean and all the rest are found in the land.</p>",
                    solution_hi: "<p>2.(b)<br>ग्लेशियर समुद्र में पाए जाते हैं और बाकी सभी भूमि में पाए जाते हैं।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "3",
                    section: "6",
                    question_en: "<p>3. A series is given with one term missing. Select the correct alternative from the given options that will complete the series. <br>EF27, GI34, IL41, KO48, ?</p>",
                    question_hi: "<p>3. एक श्रंखला दी गई है जिसमें एक पद लुप्त है। दिए गए विकल्पों में से वह सही विकल्प चुनिए जो श्रृंखला को पूरा करे<br>EF27, GI34, IL41, KO48, ?</p>",
                    options_en: ["<p>NS51</p>", "<p>MR55</p>", 
                                "<p>PQ53</p>", "<p>LT57</p>"],
                    options_hi: ["<p>NS51</p>", "<p>MR55</p>",
                                "<p>PQ53</p>", "<p>LT57</p>"],
                    solution_en: "<p>3.(b)<br>Logic : - Number is increasing by 7.<br>1s letter + 2 = 1st letter of next term<br>2nd letter + 3 = 2nd letter of the next term<br>Next term = K + 2 = M, O + 3 = R, 48 + 7 = 55 <math display=\"inline\"><mo>&#8658;</mo><mi>M</mi><mi>R</mi><mn>55</mn></math></p>",
                    solution_hi: "<p>3.(b)<br>तर्क :- संख्या 7 से बढ़ रही है।<br>1 अक्षर + 2 = अगले पद का पहला अक्षर<br>दूसरा अक्षर + 3 = अगले पद का दूसरा अक्षर<br>अगला पद = K + 2 = M, O + 3 = R, 48 + 7 = 55 <math display=\"inline\"><mo>&#8658;</mo><mi>M</mi><mi>R</mi><mn>55</mn></math></p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "4",
                    section: "6",
                    question_en: "<p>4. Read the given statements and decide which of the conclusions given in the options logically follow from the statements. <br><strong>Statements :&nbsp;</strong><br>1. All kitchens are bedrooms.&nbsp;<br>2. No bedroom is a hall.</p>",
                    question_hi: "<p>4. दिए गए कथनों को पढ़ें और बताइये कि विकल्पों में से कौन सा निष्कर्ष कथनों का तार्किक रूप से अनुसरण करता है<br><strong>कथन :</strong><br>1. सभी रसोई बेडरूम हैं<br>2. कोई बेडरूम हॉल नहीं है</p>",
                    options_en: ["<p>Some Kitchens are halls.</p>", "<p>All halls are kitchens.</p>", 
                                "<p>No kitchen is a bedroom.</p>", "<p>Some bedrooms are kitchens</p>"],
                    options_hi: ["<p>कुछ रसोई हॉल हैं</p>", "<p>सभी हॉल रसोई हैं</p>",
                                "<p>कोई रसोई बेडरूम नहीं है।</p>", "<p>कुछ बेडरूम रसोई हैं</p>"],
                    solution_en: "<p>4.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728968379336.png\" alt=\"rId7\" width=\"115\" height=\"113\"><br>From the above diagram it is clear that Some bedrooms are kitchen.</p>",
                    solution_hi: "<p>4.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728968379571.png\" alt=\"rId8\" width=\"114\" height=\"112\"><br>ऊपर दिए गए आरेख से यह स्पष्ट है कि कुछ बेडरूम किचन हैं।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "5",
                    section: "6",
                    question_en: "5. Select the letter from among the given options that can replace the question mark (?) in the following series.<br />H3K9N15Q21?",
                    question_hi: "5. दिए गए विकल्पों में से उस अक्षर का चयन कीजिये जो निम्नलिखित श्रृंखला में प्रश्नवाचक चिन्ह (?) को प्रतिस्थापित कर सकता है।<br />H3K9N15Q21?",
                    options_en: [" T", " V ", 
                                " U", " S"],
                    options_hi: [" T", " V ",
                                " U", " S"],
                    solution_en: "<p>5.(a)<br>H + 3 = K, K + 3 = N, N + 3 = Q, Q + 3 = T</p>",
                    solution_hi: "<p>5.(a)<br>H + 3 = K, K + 3 = N, N + 3 = Q, Q + 3 = T</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "6",
                    section: "6",
                    question_en: "<p>6. From among the given options, select the sequence that best arranges the following words in a meaningful order.<br>1. Family&nbsp;<br>2. World&nbsp;<br>3. Individual&nbsp;<br>4. Community&nbsp;<br>5. Country</p>",
                    question_hi: "<p>6. दिए गए विकल्पों में से उस अनुक्रम का चयन करें जो निम्नलिखित शब्दों को सार्थक क्रम में व्यवस्थित करता है।<br>1. परिवार<br>2. दुनिया<br>3. व्यक्ति<br>4. समुदाय&nbsp;<br>5. देश</p>",
                    options_en: ["<p>45321</p>", "<p>31452</p>", 
                                "<p>31542</p>", "<p>51432</p>"],
                    options_hi: ["<p>45321</p>", "<p>31452</p>",
                                "<p>31542</p>", "<p>51432</p>"],
                    solution_en: "<p>6.(b)<br>Individual, family, community, country, world</p>",
                    solution_hi: "<p>6.(b)<br>व्यक्ति, परिवार, समुदाय, देश, विश्व</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "7",
                    section: "6",
                    question_en: "<p>7. Study the given grid carefully and select the options from among the given options that can replace the question mark (?)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728968379689.png\" alt=\"rId9\" width=\"174\" height=\"72\"></p>",
                    question_hi: "<p>7. दिए गए ग्रिड का ध्यानपूर्वक अध्ययन कीजिये और दिए गए विकल्पों में से उन विकल्पों का चयन करें जो प्रश्न चिह्न (?) के स्थान पर आएगा <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728968379689.png\" alt=\"rId9\" width=\"174\" height=\"72\"></p>",
                    options_en: [" <math display=\"inline\"><msub><mrow><mi>Q</mi></mrow><mrow><mn>21</mn></mrow></msub></math>", " <math display=\"inline\"><msub><mrow><mi>R</mi></mrow><mrow><mn>12</mn></mrow></msub></math>", 
                                " <math display=\"inline\"><msub><mrow><mi>Q</mi></mrow><mrow><mn>17</mn></mrow></msub></math>", " <math display=\"inline\"><msub><mrow><mi>P</mi></mrow><mrow><mn>12</mn></mrow></msub></math>"],
                    options_hi: [" <math display=\"inline\"><msub><mrow><mi>Q</mi></mrow><mrow><mn>21</mn></mrow></msub></math>", " <math display=\"inline\"><msub><mrow><mi>R</mi></mrow><mrow><mn>12</mn></mrow></msub></math>",
                                " <math display=\"inline\"><msub><mrow><mi>Q</mi></mrow><mrow><mn>17</mn></mrow></msub></math>", " <math display=\"inline\"><msub><mrow><mi>P</mi></mrow><mrow><mn>12</mn></mrow></msub></math>"],
                    solution_en: "<p>7.(c)<br>Logic :- previous alphabet + 3 = next alphabet<br>11 - 3 = 8, 13 - 4 = 9, 24 - 7 = 17<br>N + 3 = Q, 24 - 7 = 17<br><math display=\"block\"><msub><mrow><mi>Q</mi></mrow><mrow><mn>17</mn></mrow></msub></math></p>",
                    solution_hi: "<p>7.(c)<br>तर्क :- पिछला अक्षर + 3 = अगला अक्षर<br>11 - 3 = 8, 13 - 4 = 9, 24 - 7 = 17<br>N + 3 = Q, 24 - 7 = 17<br><math display=\"block\"><msub><mrow><mi>Q</mi></mrow><mrow><mn>17</mn></mrow></msub></math></p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "8",
                    section: "6",
                    question_en: "<p>8. M1, M2, M3, M4 and M5 are five men sitting in a line facing towards the South. L1, L2, L3, L4 and L5 are five women sitting in a second line, parallel to the first line, facing towards the north. <br>M2, who is to the immediate left of M4, is opposite to L5. <br>M3 and L2 are diagonally opposite each other <br>M5 is opposite L3, who is to the immediate right of L1 <br>L4, who is to the immediate right of L3, is opposite M4. <br>L1 is at one end of the line. <br>Which of the following pairs are diagonally opposite each other ?</p>",
                    question_hi: "<p>8. M1, M2, M3, M4 और M5 पांच व्यक्ति एक पंक्ति में दक्षिण की ओर मुख करके बैठे हैं। L1, L2, L3, L4 और L5 पांच महिलाएं हैं, जो पहली पंक्ति के समानांतर दूसरी पंक्ति में उत्तर की ओर मुख करके बैठी हैं।<br>M2, जो M4 के ठीक बाईं ओर है, L5 के विपरीत है।<br>M3 और L2 एक दूसरे के तिर्यक रूप से सामने हैं।<br>M5, L3 के सामने है, जो L1 के ठीक दायें है।<br>L4, जो L3 के ठीक दायें है, M4 के सामने है<br>L1 पंक्ति के एक छोर पर है। <br>निम्नलिखित में से कौन-सा युग्म एक दूसरे के विकर्णत: सम्मुख है ?</p>",
                    options_en: [" M5 and L3", " M1 and L2  ", 
                                " M3 and L1  ", " M1 and L1 "],
                    options_hi: ["  M5 और L3", "  M1 और L2",
                                "  M3 और L1", "  M1 और L1"],
                    solution_en: "<p>8.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728968379792.png\" alt=\"rId10\" width=\"155\" height=\"98\"></p>",
                    solution_hi: "<p>8.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728968379792.png\" alt=\"rId10\" width=\"155\" height=\"98\"></p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "9",
                    section: "6",
                    question_en: "<p>9.Read the given statements and decide which of the conclusions given in the option logically follows from the statements.<br><strong>Statements :</strong><br>1.All guitars are instruments. <br>2. All instruments are bagpipes.</p>",
                    question_hi: "<p>9. दिए गए कथनों को पढ़िए और बताइये कि दिए गए विकल्पों में से कौन सा निष्कर्ष कथनों का तार्किक रूप से अनुसरण करता है<br><strong>कथन :</strong><br>1. सभी गिटार यंत्र हैं<br>2. सभी उपकरण बैगपाइप हैं</p>",
                    options_en: ["<p>All bagpipes are instruments.</p>", "<p>All instruments are guitars.</p>", 
                                "<p>All guitars are bagpipes.</p>", "<p>No guitar is a bagpipe.</p>"],
                    options_hi: ["<p>सभी बैगपाइप यंत्र हैं</p>", "<p>सभी यंत्र गिटार हैं</p>",
                                "<p>सभी गिटार बैगपाइप हैं</p>", "<p>कोई गिटार बैगपाइप नहीं है</p>"],
                    solution_en: "<p>9.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728968379937.png\" alt=\"rId11\" width=\"141\" height=\"80\"><br>From the above diagram we can observe that all Guitars are Bagpipers.</p>",
                    solution_hi: "<p>9.(c)<br><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728968380045.png\" alt=\"rId12\" width=\"148\" height=\"84\"><br>उपरोक्त आरेख से हम देख सकते हैं कि सभी गिटार बैगपाइपर हैं।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "10",
                    section: "6",
                    question_en: "<p>10. Four words have been given, out of which three are alike in some manner and one is different. Select the odd one.<br>Sword , Dagger , Spade , Knife</p>",
                    question_hi: "<p>10. चार शब्द दिए गए हैं, जिनमें से तीन किसी तरह से एक जैसे हैं और एक अलग है। विजातीय का चयन कीजिये ।<br>तलवार, कटार, कुदाल, चाकू</p>",
                    options_en: ["<p>Sword</p>", "<p>Knife</p>", 
                                "<p>Dagger</p>", "<p>Spade</p>"],
                    options_hi: ["<p>तलवार</p>", "<p>चाकू</p>",
                                "<p>कटार</p>", "<p>कुदाल</p>"],
                    solution_en: "<p>10.(d)<br>All others except Spade are used for cutting.</p>",
                    solution_hi: "<p>10.(d)<br>कुदाल को छोड़कर अन्य सभी का उपयोग काटने के लिए किया जाता है।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "11",
                    section: "6",
                    question_en: "<p>11. Four words have been given, out of which three are alike in some manner and one is different. Select the odd one. <br>Jaipur , Tinsukia , Dehradun , Kohima</p>",
                    question_hi: "<p>11. चार शब्द दिए गए हैं, जिनमें से तीन किसी तरह से एक जैसे हैं और एक अलग है। विजातीय का चयन कीजिये <br>जयपुर, तिनसुकिया, देहरादून, कोहिमा</p>",
                    options_en: ["<p>Dehradun</p>", "<p>Kohima</p>", 
                                "<p>Tinsukia</p>", "<p>Jaipur</p>"],
                    options_hi: ["<p>देहरादून</p>", "<p>कोहिमा</p>",
                                "<p>तिनसुकिया</p>", "<p>जयपुर</p>"],
                    solution_en: "<p>11.(c)<br>All are capital of states except Tinsukia.</p>",
                    solution_hi: "<p>11.(c)<br>तिनसुकिया को छोड़कर सभी राज्यों की राजधानी हैं।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "12",
                    section: "6",
                    question_en: "<p>12. Akshay&rsquo;s son-in-law is married to Anisha whose brother Vijay is married to Twinkle. How is Twinkle related to Akshay?</p>",
                    question_hi: "<p>12.अक्षय के दामाद की शादी अनीशा से हुई है जिसके भाई विजय की शादी ट्विंकल से हुई है। ट्विंकल का अक्षय से क्या संबंध है?</p>",
                    options_en: ["<p>Sister</p>", "<p>Daughter</p>", 
                                "<p>Brother&rsquo;s wife</p>", "<p>Daughter-in-law</p>"],
                    options_hi: ["<p>बहन</p>", "<p>पुत्री</p>",
                                "<p>भाई की पत्नी</p>", "<p>बहू</p>"],
                    solution_en: "<p>12.(d)<br><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728968380227.png\" alt=\"rId13\" width=\"254\" height=\"91\"></p>",
                    solution_hi: "<p>12.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728968380369.png\" alt=\"rId14\" width=\"277\" height=\"100\"></p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "13",
                    section: "6",
                    question_en: "<p>13. &rsquo;Mo &gt; No&rsquo; means that Mo is the daughter of No. <br>&lsquo;Mo # No&rsquo; means Mo is the father of No. <br>&lsquo;Mo $ No means Mo is the mother of No. <br>&lsquo;Mo / No&rsquo; means Mo is the brother of No. <br>Based on the above, how is Po related to Ko in the given expression?<br>Po $ Qo # Ro / To &gt; Ko</p>",
                    question_hi: "<p>13. Mo &gt; No का अर्थ Mo, No की पुत्री है<br>Mo # No&rsquo; का अर्थ Mo, No का पिता है <br>Mo $ No का अर्थ Mo, No की माता है&nbsp;<br>Mo / No&rsquo; का अर्थ Mo, No का भाई है&nbsp;<br>उपरोक्त के आधार पर, दिए गए व्यंजक में Po, Ko से किस प्रकार संबंधित है<br>Po $ Qo # Ro / To &gt; Ko</p>",
                    options_en: ["<p>Husband&rsquo;s sister</p>", "<p>Son&rsquo;s wife</p>", 
                                "<p>Father&rsquo;s sister</p>", "<p>Husband&rsquo;s mother</p>"],
                    options_hi: ["<p>पत्नी की बहन</p>", "<p>पुत्र की पत्नी</p>",
                                "<p>पिता की बहन</p>", "<p>पति की माता</p>"],
                    solution_en: "<p>13.(a)<br>Po $ Qo # Ro / To &gt; Ko<br>Po is mother of Qo, Qo is father of Ro, Ro is mother of To, To is daughter of Ko<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728968380495.png\" alt=\"rId15\" width=\"94\" height=\"141\"></p>",
                    solution_hi: "<p>13.(a)<br>Po $ Qo # Ro / To &gt; Ko<br>Po, Qo की मां है, Qo Ro का पिता है, Ro,To की मां है, To Ko की बेटी है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728968380495.png\" alt=\"rId15\" width=\"94\" height=\"141\"></p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "14",
                    section: "6",
                    question_en: "<p>14. Four different positions of the same dice are shown. Select the letter that will be on the face opposite to the one having the letter F. <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728968380591.png\" alt=\"rId16\" width=\"243\" height=\"51\"></p>",
                    question_hi: "<p>14. एक ही पासे की चार अलग-अलग स्थितियों को दिखाया गया है। उस अक्षर का चयन करें जो F अक्षर वाले के विपरीत फलक पर होगा।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728968380591.png\" alt=\"rId16\" width=\"243\" height=\"51\"></p>",
                    options_en: ["<p>C</p>", "<p>A</p>", 
                                "<p>E</p>", "<p>D</p>"],
                    options_hi: ["<p>C</p>", "<p>A</p>",
                                "<p>E</p>", "<p>D</p>"],
                    solution_en: "<p>14.(a)<br>Opposite of A = D<br>Opposite of C = F<br>Opposite of D = E</p>",
                    solution_hi: "<p>14.(a)<br>A के विपरीत = D<br>C के विपरीत = F<br>D के विपरीत = E</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "15",
                    section: "6",
                    question_en: "<p>15. A wristwatch reads 7:30 am. If the hour hand points south-west, then in what direction will the minute hand point after 15 minutes ?</p>",
                    question_hi: "<p>15. एक कलाई घड़ी में 7:30 am बजे है। यदि घंटे की सुई दक्षिण-पश्चिम की ओर इंगित करती है, तो 15 मिनट के बाद मिनट की सुई किस दिशा में इंगित करेगी ?</p>",
                    options_en: ["<p>South</p>", "<p>South-west</p>", 
                                "<p>West</p>", "<p>North</p>"],
                    options_hi: ["<p>दक्षिण</p>", "<p>दक्षिण पश्चिम</p>",
                                "<p>पश्चिम</p>", "<p>उत्तर</p>"],
                    solution_en: "<p>15.(c)<br><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728968380714.png\" alt=\"rId17\" width=\"192\" height=\"117\"></p>",
                    solution_hi: "<p>15.(c)<br><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728968380817.png\" alt=\"rId18\" width=\"193\" height=\"105\"></p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "16",
                    section: "6",
                    question_en: "<p>16. Read the given information and answer the questions that follow.<br>Six students A, B, C, D, E and F appeared in several tests. Either C or F scored the highest. Whenever C scored the highest, E scored the least. Whenever F scored the highest, B scored the least. In all the tests they got different marks. D scored higher than A but they were close competitors. A scored higher than B. C scored higher than A. E and F were close competitors. If C got the first rank, what was the rank of A ?</p>",
                    question_hi: "<p>16. दी गई जानकारी को पढ़िए और नीचे दिए गए प्रश्नों के उत्तर दीजिए।<br>छह छात्र A, B, C, D, E और F कई परीक्षाओं में शामिल हुए। या तो C या F ने सबसे अधिक अंक प्राप्त किए। जब भी C ने सबसे अधिक अंक प्राप्त किए, E ने सबसे कम अंक प्राप्त किए। जब भी F ने सबसे अधिक अंक प्राप्त किए, तो B ने सबसे कम अंक प्राप्त किए। सभी परीक्षणों में उन्हें अलग-अलग अंक मिले। D ने A से अधिक अंक प्राप्त किए लेकिन वे करीबी प्रतिस्पर्धी थे। A ने B से अधिक अंक प्राप्त किए। C ने A से अधिक अंक प्राप्त किए। E और F करीबी प्रतिस्पर्धी थे। यदि C को प्रथम स्थान प्राप्त हुआ, तो A का स्थान क्या था ?</p>",
                    options_en: ["<p>Third</p>", "<p>First</p>", 
                                "<p>Fourth</p>", "<p>Second</p>"],
                    options_hi: ["<p>तीसरा</p>", "<p>प्रथम</p>",
                                "<p>चौथी</p>", "<p>दूसरा</p>"],
                    solution_en: "<p>16.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728968380914.png\" alt=\"rId19\" width=\"144\" height=\"119\"></p>",
                    solution_hi: "<p>16.(A)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728968380914.png\" alt=\"rId19\" width=\"145\" height=\"120\"></p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "17",
                    section: "6",
                    question_en: "<p>17. Study the given pattern carefully and select the number from among the given that can replace the question mark (?).<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728968381019.png\" alt=\"rId20\" width=\"115\" height=\"118\"></p>",
                    question_hi: "<p>17. दिए गए पैटर्न का ध्यानपूर्वक अध्ययन कीजिये और दिए गए प्रश्नों में से उस संख्या का चयन करें जो प्रश्नवाचक चिन्ह (?) के स्थान पर आएगी <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728968381019.png\" alt=\"rId20\" width=\"115\" height=\"118\"></p>",
                    options_en: ["<p>20</p>", "<p>6</p>", 
                                "<p>14</p>", "<p>7</p>"],
                    options_hi: ["<p>20</p>", "<p>6</p>",
                                "<p>14</p>", "<p>7</p>"],
                    solution_en: "<p>17.(d)<br>T = 20 - 12 = 8<br>S = 19 - 12 = 7</p>",
                    solution_hi: "<p>17.(d)<br>T = 20 - 12 = 8<br>S = 19 - 12 = 7</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "18",
                    section: "6",
                    question_en: "<p>18. Vimal and Kamal are good at piano and guitar. Rima and Sima are good at hockey and guitar. Rima, Komal and Rina are good at hockey and chess. Rina and Rima are good at piano and badminton. Komal and Sobha are good at chess and piano. Who is good at hockey, chess and badminton but NOT at guitar ?</p>",
                    question_hi: "<p>18. विमल और कमल पियानो और गिटार में अच्छे हैं। रीमा और सीमा हॉकी और गिटार में अच्छी हैं। रीमा, कोमल और रीना हॉकी और शतरंज में अच्छी हैं। रीना और रीमा पियानो और बैडमिंटन में अच्छी हैं। कोमल और शोभा शतरंज और पियानो में अच्छी हैं। हॉकी, शतरंज और बैडमिंटन में कौन अच्छा है लेकिन गिटार में नहीं ?</p>",
                    options_en: ["<p>Rima</p>", "<p>Komal</p>", 
                                "<p>Sima</p>", "<p>Rina</p>"],
                    options_hi: ["<p>रीमा</p>", "<p>कोमल</p>",
                                "<p>सिमा</p>", "<p>रीना</p>"],
                    solution_en: "<p>18.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728968381206.png\" alt=\"rId21\" width=\"306\" height=\"130\"></p>",
                    solution_hi: "<p>18.(d)<br><strong id=\"docs-internal-guid-c4c4a9f7-7fff-62a0-1a2e-1cd8b85874eb\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdd05zK3Wbi1wfUfG8EI4sJ1GkJoDVhgvQRiWOEintpMwlwNr1DM_oZxxUsheUbFMjvrRiT5VXO6MNHPud8oLlWhAzd9_XL1tLYAW0rXFHgBQPN0tHyPzuTSRrnE0O4q5PL8hFz06pawIevGomWZxpwqJBi?key=itrIIMltIrSzfuxlys72ZA\" width=\"300\" height=\"193\"></strong></p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "19",
                    section: "6",
                    question_en: "<p>19. Select the option that is related to the third term in the same way as the second term is related to the first term.<br>Law : order :: Crime : ?</p>",
                    question_hi: "<p>19. विकल्प का चयन कीजिये जो तीसरे पद से उसी प्रकार संबंधित है जैसे दूसरा पद पहले पद से संबंधित है<br>कानून : अनुशासन :: अपराध : ?</p>",
                    options_en: ["<p>Punishment</p>", "<p>War</p>", 
                                "<p>Criminal</p>", "<p>Peace</p>"],
                    options_hi: ["<p>सज़ा</p>", "<p>युद्ध</p>",
                                "<p>अपराधी</p>", "<p>शांति</p>"],
                    solution_en: "<p>19.(a)<br>As Law related to order, in the same way Crime is related to Punishment</p>",
                    solution_hi: "<p>19.(a)<br>जिस प्रकार कानून व्यवस्था से संबंधित है, उसी प्रकार अपराध का संबंध सजा से है।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "20",
                    section: "6",
                    question_en: "<p>20. There is a family of five persons A, B, C, D and E. D is the father of B. Who is the wife of A. C is the sister of D, and E is the wife of D. How is E related to A ?</p>",
                    question_hi: "<p>20. पांच व्यक्तियों A, B, C, D और E का एक परिवार है। D, B का पिता है। A की पत्नी कौन है। C, D की बहन है, और E, D की पत्नी है। E , A कैसे संबंधित है ?</p>",
                    options_en: ["<p>Son - in - law</p>", "<p>Son</p>", 
                                "<p>Mother</p>", "<p>Mother - in - law</p>"],
                    options_hi: ["<p>दामाद</p>", "<p>पुत्र</p>",
                                "<p>माता</p>", "<p>सास</p>"],
                    solution_en: "<p>20.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728968381519.png\" alt=\"rId23\" width=\"168\" height=\"95\"></p>",
                    solution_hi: "<p>20.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728968381519.png\" alt=\"rId23\" width=\"168\" height=\"95\"></p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "21",
                    section: "6",
                    question_en: "<p>21. Consider the given statements and decide which of the following assumptions is/are implicit in statements.<br><strong>Statement :</strong><br>An advertisement states that XYZ paints should be used to decorate houses. <br><strong>Assumptions :</strong><br>1. People want to decorate their house.&nbsp;<br>2. Only XYZ paints can make the house decorative.</p>",
                    question_hi: "<p>21. दिए गए कथनों पर विचार कीजिये और बताइये कि निम्नलिखित में से कौन सी धारणा कथनों में निहित हैं<br><strong>कथन :&nbsp;</strong><br>एक विज्ञापन में कहा गया है कि घरों को सजाने के लिए XYZ पेंट का इस्तेमाल किया जाना चाहिए<br><strong>धारणा :</strong><br>1. लोग अपने घर को सजाना चाहते हैं&nbsp;<br>2. केवल XYZ पेंट ही घर को सजावटी बना सकते हैं</p>",
                    options_en: ["<p>Only assumption 1 is implicit.</p>", "<p>Only assumption 2 is implicit.</p>", 
                                "<p>Neither assumption 1 nor 2 is implicit.</p>", "<p>Either assumption 1 or 2 is implicit</p>"],
                    options_hi: ["<p>केवल धारणा 1 निहित है।</p>", "<p>केवल धारणा 2 निहित है।</p>",
                                "<p>न तो धारणा 1 और न ही 2 निहित है।</p>", "<p>या तो धारणा 1 या 2 निहित है</p>"],
                    solution_en: "<p>21.(a)<br>From the given statement it is clear that People wants to decorate their houses.</p>",
                    solution_hi: "<p>21.(a)<br>दिए गए कथन से यह स्पष्ट है कि लोग अपने घरों को सजाना चाहते हैं।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "22",
                    section: "6",
                    question_en: "<p>22. In a certain code language, LAUGHTER is written as MBVHIUFS, How will &lsquo;DEPRESSION&rsquo; be written in that language?</p>",
                    question_hi: "<p>22. एक निश्चित कूट भाषा में LAUGHTER को MBVHIUFS लिखा जाता है, उस भाषा में \'DEPRESSION\' कैसे लिखा जाएगा?</p>",
                    options_en: ["<p>EFQSFTTJPO</p>", "<p>EFSFTQTJPO</p>", 
                                "<p>EFQSFTJPTO</p>", "<p>EFQSJPOFTT</p>"],
                    options_hi: ["<p>EFQSFTTJPO</p>", "<p>EFSFTQTJPO</p>",
                                "<p>EFQSFTJPTO</p>", "<p>EFQSJPOFTT</p>"],
                    solution_en: "<p>22.(a)<br>Next letter of each letter is written.<br>Code for DEPRESSION = EFQSFTTJPO</p>",
                    solution_hi: "<p>22.(a)<br>प्रत्येक अक्षर का अगला अक्षर लिखा होता है।<br>DEPRESSION के लिए कोड = EFQSFTTJPO</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "23",
                    section: "6",
                    question_en: "<p>23. Select the number from among the given options that can replace the question mark (?) in the following series.<br>5,18,39,68,105,?</p>",
                    question_hi: "<p>23. दिए गए विकल्पों में से उस संख्या का चयन कीजिये जो निम्नलिखित श्रृंखला में प्रश्नवाचक चिह्न (?) को प्रतिस्थापित कर सके<br>5,18,39,68,105,?</p>",
                    options_en: ["<p>140</p>", "<p>150</p>", 
                                "<p>138</p>", "<p>118</p>"],
                    options_hi: ["<p>140</p>", "<p>150</p>",
                                "<p>138</p>", "<p>118</p>"],
                    solution_en: "<p>23.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728968381629.png\" alt=\"rId24\" width=\"214\" height=\"85\"></p>",
                    solution_hi: "<p>23.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728968381629.png\" alt=\"rId24\" width=\"214\" height=\"85\"></p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "24",
                    section: "6",
                    question_en: "<p>24. A statement is given followed by two arguments. Decide which of the arguments is/are strong with respect to the statement.<br><strong>Statement :</strong><br>Officers in the administrative departments should be transferred.<br><strong>Arguments :</strong><br>1. Yes, they get friendly with people and can be manipulated by them.<br>2. No, officers do not like to get transferred frequently.</p>",
                    question_hi: "<p>24. एक कथन के बाद दो तर्क दिए गए हैं। बताइये कि कथन के संबंध में कौन-सा तर्क पुष्ट हैं।<br><strong>कथन :&nbsp;</strong><br>प्रशासनिक विभागों के अधिकारियों का तबादला किया जाए<br><strong>तर्क :&nbsp;</strong><br>1. हां, वे लोगों के साथ मित्रवत हो जाते हैं और उनके साथ छेड़छाड़ की जा सकती है<br>2. नहीं, अधिकारियों का बार-बार तबादला होना पसंद नहीं है</p>",
                    options_en: ["<p>Only argument 2 is strong.</p>", "<p>Neither argument 1 nor 2 is strong.</p>", 
                                "<p>Both arguments 1 and 2 are strong.</p>", "<p>Only argument 1 is strong.</p>"],
                    options_hi: ["<p>केवल तर्क 2 पुष्ट है।</p>", "<p>न तो तर्क 1 और न ही 2 पुष्ट है</p>",
                                "<p>तर्क 1 और 2 दोनों पुष्ट हैं</p>", "<p>केवल तर्क 1 पुष्ट है</p>"],
                    solution_en: "<p>24.(d)<br>From the given statement it is clear that only argument 1 is strong.</p>",
                    solution_hi: "<p>24.(d)<br>दिए गए कथन से यह स्पष्ट है कि केवल तर्क 1 ही प्रबल है।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "25",
                    section: "6",
                    question_en: "<p>25. Four pairs of words are listed below, out of which three pairs are alike and one is different. Find the odd pair. <br>Low : High <br>Night : Day <br>Soft : Delicate <br>Sweet : Sour</p>",
                    question_hi: "<p>25. शब्दों के चार जोड़े नीचे सूचीबद्ध हैं, जिनमें से तीन जोड़े एक जैसे हैं और एक अलग है। विजातीय युग्म ज्ञात कीजिए।<br>निम्न : उच्च <br>रात : दिन <br>मृदु : नाज़ुक<br>मीठा : खट्टा</p>",
                    options_en: ["<p>Sweet : Sour</p>", "<p>Low : High</p>", 
                                "<p>Soft : Delicate</p>", "<p>Night : Day</p>"],
                    options_hi: ["<p>मीठा : खट्टा</p>", "<p>निम्न : उच्च</p>",
                                "<p>मृदु : नाज़ुक</p>", "<p>रात : दिन</p>"],
                    solution_en: "<p>25.(c)<br>The 2nd word is the opposite of the first.</p>",
                    solution_hi: "<p>25.(c)<br>दूसरा शब्द पहले के विपरीत है।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "26",
                    section: "misc",
                    question_en: "<p>26. In the following diagram, A contains all the people who like chocolate, B contains all the people who like ice-cream and C contains all the people who like candy. What does the shaded area represent ?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728968381845.png\" alt=\"rId25\" width=\"111\" height=\"98\"></p>",
                    question_hi: "<p>26. निम्नलिखित आरेख में, A में वे सभी लोग शामिल हैं जिन्हें चॉकलेट पसंद है, B में वे सभी लोग हैं जिन्हें आइसक्रीम पसंद है और C में कैंडी पसंद करने वाले सभी लोग शामिल हैं। छायांकित क्षेत्र क्या निरूपित करता है ?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728968381845.png\" alt=\"rId25\" width=\"111\" height=\"98\"></p>",
                    options_en: ["<p>People who like chocolate, candy and ice-cream.</p>", "<p>People who only like candy and ice-cream</p>", 
                                "<p>People who only like chocolate and ice-cream.</p>", "<p>People who only like chocolate and candy.</p>"],
                    options_hi: ["<p>चॉकलेट, कैंडी और आइसक्रीम पसंद करने वाले लोग</p>", "<p>जो लोग केवल कैंडी और आइसक्रीम पसंद करते हैं।</p>",
                                "<p>जो लोग सिर्फ चॉकलेट और आइसक्रीम पसंद करते हैं।</p>", "<p>जो लोग केवल चॉकलेट और कैंडी पसंद करते हैं।</p>"],
                    solution_en: "<p>26.(a)<br>The shaded area represents people who like chocolate, candy and ice-cream.</p>",
                    solution_hi: "<p>26.(a)<br>छायांकित क्षेत्र उन लोगों को निरूपित करता है जो चॉकलेट, कैंडी और आइसक्रीम पसंद करते हैं।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>