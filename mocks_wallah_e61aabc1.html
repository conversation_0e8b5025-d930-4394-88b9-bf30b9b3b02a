<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">8:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 15</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">15</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 8 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["18"] = {
                name: "General Awareness",
                start: 0,
                end: 13
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="18">General Awareness</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 14,
                end: 14
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "18",
                    question_en: "<p>1. According to which law, at constant temperature, the volume of a gas is inversely proportional to pressure ?</p>",
                    question_hi: "<p>1. किस नियम के अनुसार,स्थिर ताप पर, गैस का आयतन दाब के व्युत्क्रमानुपाती होता है?</p>",
                    options_en: ["<p>Graham&rsquo;s Law</p>", "<p>Charles\'s Law</p>", 
                                "<p>Boyle&rsquo;s Law</p>", "<p>Gay-Lussac&rsquo;s Law</p>"],
                    options_hi: ["<p>ग्राहम का नियम</p>", "<p>चार्ल्स का नियम</p>",
                                "<p>बॉयल का नियम</p>", "<p>गे-लुसाक का नियम</p>"],
                    solution_en: "<p>1.(c) <strong>Boyle\'s law. Graham\'s Gas Diffusion Law -</strong> The rate of diffusion of a gas at constant pressure is inversely proportional to the square root of its molar mass.<strong> Charles\'s law - </strong>The volume of an ideal gas is directly proportional to the absolute temperature at constant pressure. <strong>Gay-Lussac\'s law -</strong> The pressure of a given mass of gas varies directly with the absolute temperature of the gas when the volume is kept constant.</p>",
                    solution_hi: "<p>1.(c)<strong> बॉयल का नियम। ग्राहम का गैस प्रसार नियम -</strong> स्थिर दाब पर गैस के प्रसार की दर उसके मोलर द्रव्यमान के वर्गमूल के व्युत्क्रमानुपाती होती है। <strong>चार्ल्स का नियम -</strong> एक आदर्श गैस का आयतन स्थिर दबाव पर परम तापमान के समानुपाती होता है। <strong>गे-लुसाक का नियम - </strong>स्थिर द्रव्यमान और आयतन पर गैस द्वारा लगाया गया दाब तापमान के समानुपाती होता है।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "2",
                    section: "18",
                    question_en: "<p>2. What is the other name of Newton\'s ﬁrst law of motion?</p>",
                    question_hi: "<p>2. न्यूटन के गति के प्रथम नियम का दूसरा नाम क्या है?</p>",
                    options_en: ["<p>Law of inertia</p>", "<p>Law of movement</p>", 
                                "<p>Law of momentum</p>", "<p>Law of displacement</p>"],
                    options_hi: ["<p>जड़त्व का नियम</p>", "<p>दोलन का नियम</p>",
                                "<p>संवेग का नियम</p>", "<p>विस्थापन का नियम</p>"],
                    solution_en: "<p>2.(a) <strong>law of inertia. </strong>According to this law, if a body is at rest, it will remain at rest. Or if a body is moving in a straight line with a uniform velocity, then it will remain in motion. Unless an external force is applied to that body.</p>",
                    solution_hi: "<p>2.(a)<strong> जड़त्व का नियम।</strong> इस नियम के अनुसार यदि कोई पिंड विरामावस्था में है तो वह विरामावस्था में ही रहेगी। अथवा कोई पिंड एकसमान वेग से एक सीधी सरल रेखा में गतिमान अवस्था में है तो वह गतिमान अवस्था में ही रहेगी। जब तक उस पिंड पर कोई बाह्य बल न लगाया जाए।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "3",
                    section: "18",
                    question_en: "<p>3. The three laws of motion were proposed by</p>",
                    question_hi: "<p>3. गति के तीन नियम किसके द्वारा प्रस्तावित किए गए थे?</p>",
                    options_en: ["<p>Newton</p>", "<p>Galileo</p>", 
                                "<p>Edison</p>", "<p>Aristotle</p>"],
                    options_hi: ["<p>न्यूटन</p>", "<p>गैलीलियो</p>",
                                "<p>एडीसन</p>", "<p>अरस्तू</p>"],
                    solution_en: "<p>3.(a) <strong>Newton </strong>- In 1687 in his book &lsquo;Philosophiae Naturalis Principia Mathematica&rsquo;.<strong> First Law of Motion</strong> (Law of Inertia) - Every object will remain at rest or in uniform motion in a straight line unless compelled to change its state by the action of an external force. <strong>Second Law of Motion -</strong> Acceleration of an object depends on the mass of the object and the amount of force applied (F = ma). <strong>Third law of motion - </strong>Whenever one object exerts a force on another object, the second object exerts an equal and opposite on the first. <strong>Galileo </strong>was the first one who used the telescope for astronomy. <strong>Edison </strong>invented the electric bulb.</p>",
                    solution_hi: "<p>3.(a) <strong>न्यूटन </strong>- 1687 में अपनी पुस्तक \'फिलोसोफी नेचुरेलिस प्रिंसिपिया मैथमैटिका\' में लिखी है। <strong>गति का प्रथम नियम</strong> (जड़त्व का नियम) - प्रत्येक वस्तु तब तक स्थिर या एक सीधी रेखा में एक समान गति में रहेगी और अपनी स्थिति मे परिवर्तन नहीं करेगी जब तक कि उसपर कोई बाह्य बल न लगाया जाता है। <strong>गति का द्वितीय नियम - </strong>किसी वस्तु का त्वरण वस्तु के द्रव्यमान और लगाए गए बल की मात्रा (F = ma) पर निर्भर करता है। <strong>गति का तृतीय नियम -</strong> जब भी एक वस्तु दूसरी वस्तु पर बल लगाती है तो दूसरी वस्तु पहली वस्तु पर बराबर और विपरीत दिशा में बल लगाती है। <strong>गैलीलियो </strong>पहले व्यक्ति थे जिन्होंने खगोल विज्ञान के लिए दूरबीन का उपयोग किया था। <strong>एडिसन </strong>ने विद्युत बल्ब का आविष्कार किया था।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "4",
                    section: "18",
                    question_en: "<p>4. What is the equation for Newton&rsquo;s second law of motion?</p>",
                    question_hi: "<p>4. न्यूटन के गति के दूसरे नियम का समीकरण क्या है?</p>",
                    options_en: ["<p>F= <math display=\"inline\"><msup><mrow><mi>m</mi><mi>c</mi></mrow><mrow><mn>2</mn></mrow></msup></math></p>", "<p>F= AP</p>", 
                                "<p>F = ma</p>", "<p>F=<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac><msup><mrow><mi>m</mi><mi>v</mi></mrow><mrow><mn>2</mn></mrow></msup></math></p>"],
                    options_hi: ["<p>F = <math display=\"inline\"><msup><mrow><mi>m</mi><mi>c</mi></mrow><mrow><mn>2</mn></mrow></msup></math></p>", "<p>F = AP</p>",
                                "<p>F = ma</p>", "<p>F = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac><msup><mrow><mi>m</mi><mi>v</mi></mrow><mrow><mn>2</mn></mrow></msup></math></p>"],
                    solution_en: "<p>4.(c) <strong>F = ma. Newton\'s Second law of Motion - </strong>The rate of change of momentum of a body is directly proportional to the applied force and takes place in the direction in which the force acts. <strong>Newton\'s First Law of Motion </strong>- An object remains at rest, or moves at a constant speed in a straight line, unless a force is applied to it. <strong>Newton\'s Third Law of Motion -</strong> For every action, there is an equal and opposite reaction.</p>",
                    solution_hi: "<p>4.(c) <strong>F = ma । न्यूटन के गति का द्वितीय नियम -</strong> किसी पिंड के संवेग में परिवर्तन की दर लगाए गए बल के सीधे समानुपाती होती है और उस दिशा में होती है जिस दिशा में बल कार्य करता है। <strong>न्यूटन के गति का प्रथम नियम</strong> - कोई पिंड तब तक स्थिर अवस्था में रहता है, या एक सीधी रेखा में स्थिर गति से गति करता है, जब तक कि उस पर कोई बाहरी बल न लगाया जाए। <strong>न्यूटन के गति का तृतीय नियम -</strong> प्रत्येक क्रिया की सदैव समान एवं विपरीत दिशा में प्रतिक्रिया होती है।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "5",
                    section: "18",
                    question_en: "<p>5. The rate of change of momentum of an object is proportional to the applied unbalanced force in the direction of the force. This rule is known as:</p>",
                    question_hi: "<p>5. किसी वस्तु के संवेग परिवर्तन की दर बल की दिशा में लगाए गए असंतुलित बल के समानुपाती होती है। इस नियम को किस रूप में जाना जाता है।</p>",
                    options_en: ["<p>Newton&rsquo;s Second Law of Motion</p>", "<p>Newton&rsquo;s Fourth Law of Motion</p>", 
                                "<p>Newton&rsquo;s First Law of Motion</p>", "<p>Newton&rsquo;s Third Law of Motion</p>"],
                    options_hi: ["<p>न्यूटन के गति का द्वितीय नियम</p>", "<p>न्यूटन के गति का चौथा नियम</p>",
                                "<p>न्यूटन के गति का प्रथम नियम</p>", "<p>न्यूटन के गति का तृतीय नियम</p>"],
                    solution_en: "<p>5.(a) <strong>Newton&rsquo;s Second Law of Motion. Newton&rsquo;s Third Law of motion:</strong> For every action, there is an equal and opposite reaction. <strong>Newton First Law of motion: </strong>Every object will remain at rest or in uniform motion in a straight line unless an external or an unbalanced force acts upon it.</p>",
                    solution_hi: "<p>5.(a) <strong>न्यूटन के गति का द्वितीय नियम। न्यूटन के गति का तृतीय नियम:</strong> प्रत्येक क्रिया के लिए एक समान और विपरीत प्रतिक्रिया होती है। <strong>न्यूटन के गति का प्रथम नियम:</strong> प्रत्येक वस्तु तब तक स्थिर या एक सीधी रेखा में एकसमान गति में रहेगी जब तक उस पर कोई बाह्य या असंतुलित बल न लगाया जाये ।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "6",
                    section: "18",
                    question_en: "<p>6. A spring balance is a device commonly used for measuring the _____ acting on an object.</p>",
                    question_hi: "<p>6. स्प्रिंग बैलेंस एक उपकरण है जिसका उपयोग आमतौर पर किसी वस्तु पर कार्य करने वाले _____ को मापने के लिए किया जाता है।</p>",
                    options_en: ["<p>force</p>", "<p>momentum</p>", 
                                "<p>velocity</p>", "<p>mass</p>"],
                    options_hi: ["<p>बल</p>", "<p>गति</p>",
                                "<p>वेग</p>", "<p>द्रव्यमान</p>"],
                    solution_en: "<p>6.(a) <strong>force (F) - </strong>SI unit - Newton, F = m (mass) &times; a (acceleration), a vector quantity, <strong>Spring balance:-</strong> Hooke\'s Law, which states that the force needed to extend a spring is proportional to the distance that spring is extended from its rest position.<strong> Momentum (p):-</strong> SI unit: (kg&sdot;m/s), p = m (mass) &times; v (velocity), a vector quantity. <strong>Velocity (V):-</strong> It is the rate of change of distance. SI unit:- m/s.<strong> Mass (m):- </strong>Dimensionless quantity representing the amount of matter in a particle. SI unit:- kg, Mass = &rho; (density) &times; v (volume), a scalar quantity.</p>",
                    solution_hi: "<p>6.(a) <strong>बल (F) -</strong> SI मात्रक - न्यूटन, F = m (द्रव्यमान) &times; a (त्वरण), एक वेक्टर मात्रा, स्प्रिंग बैलेंस: - हुक का नियम, जो बताता है कि स्प्रिंग को फैलाने के लिए आवश्यक बल स्प्रिंग की दूरी के समानुपाती होता है इसकी विश्राम स्थिति से बढ़ाया गया है।<strong> संवेग (p):-</strong> SI मात्रक : (kg&sdot;m/s), p = m (द्रव्यमान) &times; v (वेग), सदिश राशि है। <strong>वेग (V):-</strong> यह दूरी के परिवर्तन की दर है। SI मात्रक :- मीटर/सेकंड। <strong>द्रव्यमान (m):- </strong>किसी कण में पदार्थ की मात्रा को दर्शाने वाली आयामहीन मात्रा (Dimensionless quantity) है। SI मात्रक :- किग्रा, द्रव्यमान = &rho; (घनत्व) &times; v (आयतन), अदिश राशि है।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "7",
                    section: "18",
                    question_en: "<p>7. Which is the least frictional force of the following?</p>",
                    question_hi: "<p>7. निम्नलिखित में से सबसे कम घर्षण बल कौन सा है?</p>",
                    options_en: ["<p>Sliding</p>", "<p>Fluid</p>", 
                                "<p>Static</p>", "<p>Rolling</p>"],
                    options_hi: ["<p>सर्पी</p>", "<p>द्रव</p>",
                                "<p>स्थैतिक</p>", "<p>बेल्लन</p>"],
                    solution_en: "<p>7.(d) <strong>Rolling friction</strong> is a type of kinetic friction; it comes into play when one body actually rolls on the surface of another body. Static &gt; Sliding &gt; Rolling. <strong>Static frictional</strong> force is the strongest form among all.<strong> Fluid friction</strong> is friction that acts on objects that are moving through a fluid. <strong>Friction </strong>is the force that resists motion when the surface of one object comes in contact with the surface of another.</p>",
                    solution_hi: "<p>7.(d) <strong>बेल्लन घर्षण </strong>एक प्रकार का गतिज घर्षण है; यह तब क्रियान्वित होता है जब एक पिंड वास्तव में दूसरे पिंड की सतह पर लुढ़कता है। स्थैतिक &gt; सर्पी &gt; बेल्लन।<strong> स्थैतिक घर्षण </strong>बल सभी में सबसे शक्तिशाली रूप है। <strong>द्रव घर्षण </strong>वह घर्षण है जो तरल पदार्थ के माध्यम से गति करने वाली वस्तुओं पर कार्य करता है। <strong>घर्षण </strong>वह बल है जो गति का विरोध करता है जब एक वस्तु की सतह दूसरी वस्तु की सतह के संपर्क में आती है।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "8",
                    section: "18",
                    question_en: "<p>8. A constant force acts on an object of mass 10 kg for a duration of 2 seconds. It increases the object&rsquo;s velocity from 5 metres/second to 10 metre/second. Find the magnitude of the applied force. Now, if the force is applied for a duration of 5 seconds what would be the final velocity of the object?</p>",
                    question_hi: "<p>8. एक स्थिर बल 10 किग्रा द्रव्यमान की वस्तु पर 2 सेकंड की अवधि के लिए कार्य करता है। यह वस्तु के वेग को 5 मीटर/सेकंड से बढ़ाकर 10 मीटर/सेकंड कर देता है। लागू बल का परिमाण ज्ञात कीजिए। अब, यदि बल 5 सेकंड की अवधि के लिए लगाया जाता है, तो वस्तु का अंतिम वेग क्या होगा?</p>",
                    options_en: ["<p>Applied force = 20 N, Final Velocity = 7.5 metres/second</p>", "<p>Applied Force = 25 N, Final Velocity = 7.5 metres/second</p>", 
                                "<p>Applied Force = 20 N, Final Velocity = 17.5 metres/second</p>", "<p>Applied Force = 25 N, Final Velocity = 17.5 metres/second</p>"],
                    options_hi: ["<p>लगाया गया बल = 20 N, अंतिम वेग = 7.5 मीटर/सेकंड</p>", "<p>लगाया गया बल = 25 N, अंतिम वेग = 7.5 मीटर/सेकंड</p>",
                                "<p>लगाया गया बल = 20 N, अंतिम वेग = 17.5 मीटर/सेकंड</p>", "<p>लगाया गया बल = 25 N, अंतिम वेग = 17.5 मीटर/सेकंड</p>"],
                    solution_en: "<p>8.(d) <strong>Applied Force = 25 N, Final Velocity = 17.5 metres/second.</strong> <br>Given, Mass = 10 kg, t<sub>1</sub>= 2s, Initial velocity u = 5 m/s, Final Velocity v = 10 m/s, t<sub>2</sub> = 5s.<br>So, Let the force and acceleration be &lsquo;F&rsquo; and &lsquo;a&rsquo; respectively.<br>So, a = <math display=\"inline\"><mfrac><mrow><mo>(</mo><mi>v</mi><mo>-</mo><mi>u</mi><mo>)</mo></mrow><mrow><mi>t</mi></mrow></mfrac><mi>&#160;</mi></math>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>10</mn><mo>-</mo><mn>5</mn><mo>)</mo></mrow><mn>2</mn></mfrac></math>&nbsp;= 2.5 m/s<sup>2</sup>.<br>So, the magnitude of applied force is Mass x Acceleration = 10 &times; 2.5 = 25 N<br>And now final velocity after 5s be v,<br>v = u + at = 5 + 2.5 &times; 5 = 17.5 m/s<br>The final velocity after 5s is 17.5 m/s.</p>",
                    solution_hi: "<p>8.(d) <strong>लगाया गया बल = 25 N, अंतिम वेग = 17.5 मीटर/सेकंड।</strong><br>दिया गया है, द्रव्यमान = 10 किग्रा, t<sub>1</sub>= 2s, प्रारंभिक वेग u = 5 m/s, अंतिम वेग v = 10 m/s, t<sub>2</sub> = 5s।<br>तो, मान लीजिए बल और त्वरण क्रमशः \'F\' और \'a\' है।<br>तो, a = <math display=\"inline\"><mfrac><mrow><mo>(</mo><mi>v</mi><mo>-</mo><mi>u</mi><mo>)</mo></mrow><mrow><mi>t</mi></mrow></mfrac><mi>&#160;</mi></math>=<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>10</mn><mo>-</mo><mn>5</mn><mo>)</mo></mrow><mn>2</mn></mfrac></math> = 2.5 m/s<sup>2</sup>। <br>तो, लगाए गए बल का परिमाण द्रव्यमान x त्वरण = 10 &times; 2.5 = 25 N है<br>और अब 5s के बाद अंतिम वेग v होगा,<br>v = u + at = 5 + 2.5 &times; 5 = 17.5 मी/से<br>5s के बाद अंतिम वेग 17.5 m/s है।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "9",
                    section: "18",
                    question_en: "<p>9. Which one of the following is NOT a scalar quantity?</p>",
                    question_hi: "<p>9. निम्नलिखित में से कौन एक अदिश राशि नहीं है?</p>",
                    options_en: ["<p>Time</p>", "<p>Speed</p>", 
                                "<p>Force</p>", "<p>Distance</p>"],
                    options_hi: ["<p>समय</p>", "<p>गति</p>",
                                "<p>बल</p>", "<p>दूरी</p>"],
                    solution_en: "<p>9.(c)<strong> Force. Scalar quantities </strong>are described only by a magnitude. <strong>Example </strong>- Distance, Speed, Mass, Temperature, Energy, Work, Volume, Area. <strong>Vector quantities </strong>are defined as the physical quantity that has both directions as well as magnitude. <strong>Example </strong>- Torque, displacement, velocity.</p>",
                    solution_hi: "<p>9.(c) <strong>बल । अदिश राशियों</strong> का वर्णन केवल परिमाण द्वारा किया जाता है। <strong>उदाहरण </strong>- दूरी, गति, द्रव्यमान, तापमान, ऊर्जा, कार्य, आयतन, क्षेत्रफल। <strong>सदिश राशियों</strong> को उस भौतिक राशि के रूप में परिभाषित किया जाता है जिसमें दिशा और परिमाण दोनों होते हैं। <strong>उदाहरण </strong>- बलाघूर्ण, विस्थापन, वेग।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "10",
                    section: "18",
                    question_en: "<p>10. Among the following, the weakest force is :</p>",
                    question_hi: "<p>10. निम्नलिखित में से, सबसे कमजोर बल है:</p>",
                    options_en: ["<p>Electric force</p>", "<p>Gravitational force</p>", 
                                "<p>Buoyant force</p>", "<p>Nuclear force</p>"],
                    options_hi: ["<p>विद्युत बल</p>", "<p>गुरुत्वाकर्षण बल</p>",
                                "<p>उत्प्लावक बल</p>", "<p>परमाणु बल</p>"],
                    solution_en: "<p>10.(b) <strong>Gravitational force </strong>- It is the force of attraction between all masses in the universe. An <strong>electric force</strong> is the interaction of either attractive force or repulsive force between two charged bodies. <strong>Buoyant force</strong> is a force acting on an object opposite to gravity by fluid which is being submerged partially or completely in fluid. <strong>Nuclear forces</strong> are the forces that act between two or more nucleons.</p>",
                    solution_hi: "<p>10.(b) <strong>गुरुत्वाकर्षण बल -</strong> यह विश्व में सभी द्रव्यमानों के बीच आकर्षण बल है। <strong>विद्युत बल </strong>दो आवेशित पिंडों के बीच आकर्षण बल या प्रतिकारक बल की परस्पर क्रिया है। उत्प्लावक बल <strong>किसी </strong>तरल पदार्थ में आंशिक रूप से या पूरी तरह से डूबा हुआ गुरुत्वाकर्षण के विपरीत किसी वस्तु पर लगने वाला बल है ।<strong> परमाणु बल</strong> वे बल हैं जो दो या दो से अधिक न्यूक्लियॉन के बीच कार्य करते हैं।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "11",
                    section: "18",
                    question_en: "<p>11. How many laws of motion were given by Isaac Newton?</p>",
                    question_hi: "<p>11. आइजैक न्यूटन ने गति के कितने नियम दिए थे?</p>",
                    options_en: ["<p>Four</p>", "<p>Two</p>", 
                                "<p>Three</p>", "<p>Five</p>"],
                    options_hi: ["<p>चार</p>", "<p>दो</p>",
                                "<p>तीन</p>", "<p>पांच</p>"],
                    solution_en: "<p>11.(c) <strong>Three. First law -</strong> Every object will remain at rest or in uniform motion in a straight line unless compelled to change its state by the action of an external force. <strong>Second law </strong>- The acceleration of an object depends on the mass of the object and the amount of force applied. <strong>Third law -</strong> Whenever one object exerts a force on a second object, the second object exerts an equal and opposite force on the first.</p>",
                    solution_hi: "<p>11.(c)<strong> तीन। प्रथम नियम - </strong>प्रत्येक वस्तु विराम अवस्था में या एक सीधी रेखा में एकसमान गति में रहेगी जब तक कि उस पर कोई बाह्य बल न लगाया जाए ।<strong> दूसरा नियम -</strong> किसी वस्तु का त्वरण वस्तु के द्रव्यमान और लगाए गए बल की मात्रा पर निर्भर करता है। <strong>तीसरा नियम -</strong> जब भी एक वस्तु दूसरी वस्तु पर बल लगाती है तो दूसरी वस्तु पहली वस्तु पर समान और विपरीत बल लगाती है।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "12",
                    section: "18",
                    question_en: "<p>12. The following statement applies to which law from the given options?<br>&lsquo;The line that joins any planet to the sun sweeps equal areas in equal intervals of time.&rsquo;</p>",
                    question_hi: "<p>12. निम्नलिखित कथन दिए गए विकल्पों में से किस नियम पर लागू होता है?<br>\'\'किसी भी ग्रह को सूर्य से मिलाने वाली रेखा समय के समान अंतराल में समान क्षेत्र तय करती है।\'</p>",
                    options_en: ["<p>Law of Periods</p>", "<p>Law of Areas</p>", 
                                "<p>Law of Motion</p>", "<p>Law of Orbits</p>"],
                    options_hi: ["<p>परिक्रमण काल का नियम (Law of Periods)</p>", "<p>क्षेत्रफलों का नियम (Law of Areas)</p>",
                                "<p>गति का नियम (Law of Motion)</p>", "<p>कक्षाओं का नियम (Law of Orbits)</p>"],
                    solution_en: "<p>12.(b) <strong>Law of Areas. Kepler\'s laws of planetary motion</strong> are three laws that describe the motion of planets around the Sun. <strong>Law of Orbits - </strong>All planets move in elliptical orbits, with the Sun at one focus.<strong> Law of Areas - </strong>A line segment joining a planet and the Sun sweeps out equal areas during equal intervals of time. <strong>Law of Periods -</strong> The square of the period of any planet is proportional to the cube of the semimajor axis of its orbit.</p>",
                    solution_hi: "<p>12.(b) <strong>क्षेत्रफलों का नियम। ग्रहों की गति के अनुसार केप्लर के तीन नियम</strong> हैं जो सूर्य के चारों ओर ग्रहों की गति का वर्णन करते हैं।<strong> कक्षाओं का नियम -</strong> सभी ग्रह दीर्घवृत्ताकार कक्षाओं में घूमते हैं, जिसमें सूर्य एक केंद्र पर स्थित होता है। <strong>क्षेत्रफलों का नियम -\'</strong> किसी भी ग्रह को सूर्य से मिलाने वाली रेखा समान समय अंतराल में समान क्षेत्र तय करती है।<strong> परिक्रमण काल का नियम -</strong> किसी भी ग्रह के परिक्रमण काल का वर्ग उसकी कक्षा के अर्धप्रमुख अक्ष के घन के समानुपाती होता है।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "13",
                    section: "18",
                    question_en: "<p>13. A bus moving on a straight road at a speed of 10 km/h increases its speed to 70 km/h in 2 minutes. Find its average acceleration.</p>",
                    question_hi: "<p>13. सीधी सड़क पर 10 km/h की चाल से गति करती हुई एक बस 2 मिनट में अपनी चाल 70 km/h तक बढ़ा देती है। बस का औसत त्वरण (acceleration) ज्ञात कीजिए।</p>",
                    options_en: ["<p>1 km/minute<sup>2</sup></p>", "<p>0.5 km/minute<sup>2</sup></p>", 
                                "<p>2 km/minute<sup>2</sup></p>", "<p>30 km/minute<sup>2</sup></p>"],
                    options_hi: ["<p>1 किमी/ वर्ग मिनट</p>", "<p>0.5 किमी/वर्ग मिनट</p>",
                                "<p>2 किमी/वर्ग मिनट</p>", "<p>30 किमी/वर्ग मिनट</p>"],
                    solution_en: "<p>13.(b) <strong>0.5 km/minute<sup>2</sup>. </strong>Given : Final velocity, v = 70 km/h =&nbsp;<math display=\"inline\"><mfrac><mrow><mn>7</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math> km/min. Initial velocity, u = 10 km/h =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>6</mn></mfrac></math> km/min. Time period, t = 2 min. We know that, Average acceleration, a = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>v</mi><mo>-</mo><mi>u</mi></mrow><mi>t</mi></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mstyle displaystyle=\"true\"><mfrac><mn>7</mn><mn>6</mn></mfrac></mstyle><mo>-</mo><mstyle displaystyle=\"true\"><mfrac><mn>1</mn><mn>6</mn></mfrac></mstyle></mrow><mn>2</mn></mfrac></math> &nbsp;<br>= <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = 0.5 km/min<sup>2</sup>.</p>",
                    solution_hi: "<p>13.(b) <strong>0.5 किमी/वर्ग मिनट।</strong> दिया गया है: अंतिम वेग, v = 70 किमी/घंटा = <math display=\"inline\"><mfrac><mrow><mn>7</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math> किमी / मिनट। प्रारंभिक वेग, u = 10 किमी/घंटा = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>6</mn></mfrac></math> किमी/मिनट। समयावधि, t = 2 मिनट। हम जानते हैं कि, औसत त्वरण, a =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>v</mi><mo>-</mo><mi>u</mi></mrow><mi>t</mi></mfrac></math>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mstyle displaystyle=\"true\"><mfrac><mn>7</mn><mn>6</mn></mfrac></mstyle><mo>-</mo><mstyle displaystyle=\"true\"><mfrac><mn>1</mn><mn>6</mn></mfrac></mstyle></mrow><mn>2</mn></mfrac></math> = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math>= 0.5 किमी/वर्ग मिनट।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "14",
                    section: "18",
                    question_en: "<p>14. What is the metric unit of pressure that is equal to 0.986923 atm?</p>",
                    question_hi: "<p>14. दाब की मीट्रिक इकाई क्या है, जो 0.986923 atm के बराबर होती है?</p>",
                    options_en: ["<p>Yard</p>", "<p>Quart</p>", 
                                "<p>Bar</p>", "<p>Gallon</p>"],
                    options_hi: ["<p>यार्ड (Yard)</p>", "<p>क्वार्ट (Quart)</p>",
                                "<p>बार (Bar)</p>", "<p>गैलन (Gallion)</p>"],
                    solution_en: "<p>14.(c) <strong>Bar</strong>. The &lsquo;atm&rsquo; is mostly used for measuring and describing atmospheric pressure. &lsquo;bar&rsquo; is also used for atmospheric pressure but is also used to measure the pressure inside vessels. 1 bar = 0.986923 atm. 1 atm = 1.01325 bar. Other units: 1 Quart = 32 fluid ounces or 4 cups, 1 Yard = 3 feet or 36 inches (0.9144 meters), 1 Gallon = 3.785 liters.</p>",
                    solution_hi: "<p>14.(c) <strong>बार</strong> (bar)। &lsquo;atm&rsquo; का उपयोग अधिकतर वायुमंडलीय दाब को मापने और उसका वर्णन करने के लिए किया जाता है। <strong>bar </strong>का उपयोग वायुमंडलीय दाब के लिए भी किया जाता है, लेकिन इसका उपयोग जहाजों के अंदर दाब को मापने के लिए भी किया जाता है। 1 बार = 0.986923 Atm । 1 atm = 1.01325 बार। अन्य मात्रक : 1 क्वार्ट = 32 द्रव औंस या 4 कप, 1 यार्ड = 3 फीट या 36 इंच (0.9144 मीटर), 1 गैलन = 3.785 लीटर।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "15",
                    section: "misc",
                    question_en: "<p>15. By observing the motion of objects on an inclined plane __________ deduced that objects move with a constant speed when no force acts on them.</p>",
                    question_hi: "<p>15. एक आनत तल पर वस्तुओं की गति का अवलोकन करके _________________ ने यह निष्कर्ष निकाला कि जब वस्तुओं पर कोई बल कार्य नहीं करता है, तो वस्तुएं नियत गति से गतिमान रहती हैं।</p>",
                    options_en: ["<p>Archimedes</p>", "<p>Johannes Kepler</p>", 
                                "<p>Galileo</p>", "<p>Michael Faraday</p>"],
                    options_hi: ["<p>आर्किमिडीज</p>", "<p>जोहान्स केप्लर</p>",
                                "<p>गैलीलियो</p>", "<p>माइकल फैराडे</p>"],
                    solution_en: "<p>15.(c) <strong>Galileo</strong>. The law of inertia was inferred by Galileo from observations of motion of a ball on a double inclined plane. Galileo concluded that an object moving on a frictionless horizontal plane must neither have acceleration nor retardation, i.e. it should move with constant velocity.</p>",
                    solution_hi: "<p>15.(c) <strong>गैलीलियो</strong>। जड़त्व के नियम का निष्कर्ष गैलीलियो ने दोहरे झुकाव वाले आनत तल पर एक गेंद की गति के अवलोकन से लगाया था। गैलीलियो ने निष्कर्ष निकाला कि घर्षण रहित क्षैतिज तल पर चलने वाली किसी वस्तु में न तो त्वरण होना चाहिए और न ही गतिरोध, अर्थात उसे स्थिर वेग से चलना चाहिए।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>