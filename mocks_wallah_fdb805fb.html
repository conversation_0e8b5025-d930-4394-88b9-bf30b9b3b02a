<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">25:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 40</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">40</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 25 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["18"] = {
                name: "General Awareness",
                start: 0,
                end: 38
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="18">General Awareness</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 39,
                end: 39
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "18",
                    question_en: "1. In which year did Gandhiji begin a defiant march to the sea to protest against the British monopoly on salt?",
                    question_hi: "1. नमक पर ब्रिटिश एकाधिकार का विरोध करने के लिए गांधीजी ने किस वर्ष समुद्र की ओर एक अवज्ञापूर्ण मार्च शुरू किया था?",
                    options_en: [" 1929", " 1927", 
                                " 1930", " 1928"],
                    options_hi: [" 1929", " 1927",
                                " 1930", " 1928"],
                    solution_en: "1.(c) On March 12,1930, Indian independence leader Mohandas Gandhi began a defiant march to the sea in protest of the British monopoly on salt, his boldest act of civil disobedience yet against British rule in India.",
                    solution_hi: "1.(c) 12 मार्च, 1930 को, भारतीय स्वतंत्रता नेता मोहनदास गांधी ने नमक पर ब्रिटिश एकाधिकार के विरोध में समुद्र की ओर एक उद्दंड मार्च शुरू किया, जो भारत में ब्रिटिश शासन के खिलाफ सविनय अवज्ञा का उनका सबसे साहसिक कार्य था।",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "2",
                    section: "18",
                    question_en: "2. The full form of LPG is:",
                    question_hi: "2. LPG का पूर्ण रूप क्या है?",
                    options_en: [" Liquidated Petrol Gas", " Liquefied Petroleum Gas", 
                                " Liquid Petrol Gas", " Liquid Petroleum Gas"],
                    options_hi: [" Liquidated Petrol Gas", " Liquefied Petroleum Gas",
                                " Liquid Petrol Gas", " Liquid Petroleum Gas"],
                    solution_en: "2.(b) The acronym LPG stands for liquefied petroleum gas and in common parlance describes gases that remain liquid at room temperature under relatively low pressure, such as propane, butane, and their mixtures.",
                    solution_hi: "2.(b) संक्षिप्त नाम एलपीजी (LPG)  तरलीकृत पेट्रोलियम गैस है, और आम बोलचाल में गैसों का वर्णन करता है जो अपेक्षाकृत कम दबाव, जैसे प्रोपेन, ब्यूटेन और उनके मिश्रण के तहत कमरे के तापमान पर तरल रहते हैं।",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "3",
                    section: "18",
                    question_en: "3. What is correct about the Khilafat Movement in India?",
                    question_hi: "3. भारत में खिलाफत आंदोलन के बारे में क्या सही है?",
                    options_en: [" It was a movement to restore the Mughal Royal family in India ", " It was a movement against the massacre of Jallianwala Bagh ", 
                                " It was a movement in demand of Pakistan ", " It was a movement in support of the Turkish sultan "],
                    options_hi: [" यह भारत में मुगल शाही परिवार को बहाल करने के लिए एक आंदोलन था ", " यह जलियांवाला बाग हत्याकांड के खिलाफ एक आंदोलन था",
                                " यह पाकिस्तान की मांग में एक आंदोलन था", " यह तुर्की सुल्तान के समर्थन में एक आंदोलन था"],
                    solution_en: " 3.(d) The Khilafat movement (1919-1924) was an agitation by Indian Muslims allied with Indian nationalism in the years following World War I. Its purpose was to pressure the British government to preserve the authority of the Ottoman Sultan as Caliph of Islam following the breakup of the Ottoman Empire at the end of the war. It was a movement in support of the Turkish sultan.",
                    solution_hi: "3.(d) खिलाफत आंदोलन (1919-1924) प्रथम विश्व युद्ध के बाद के वर्षों में भारतीय मुसलमानों द्वारा भारतीय राष्ट्रवाद से संबद्ध एक आंदोलन था। इसका उद्देश्य ब्रिटिश सरकार पर इस्लाम के खलीफा के रूप में तुर्क सुल्तान के अधिकार को बनाए रखने के लिए दबाव बनाना था। युद्ध के अंत में तुर्क साम्राज्य। यह तुर्की सुल्तान के समर्थन में एक आंदोलन था।",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "4",
                    section: "18",
                    question_en: "4. The District and Sessions Judge works directly under the control of the:",
                    question_hi: "4. जिला एवं सत्र न्यायाधीश सीधे के नियंत्रण में कार्य करता है",
                    options_en: [" district collector ", " law minister of the state ", 
                                " high court of the state ", " governor of the state "],
                    options_hi: [" जिला कलेक्टर", " राज्य के कानून मंत्री",
                                " राज्य के उच्च न्यायालय", " राज्य के राज्यपाल"],
                    solution_en: "4.(c)The District and Sessions Judge works directly under the control of the High Court of the state. The District and Sessions Judge is the Controlling and appointing authority of the ministerial staff of all the Courts of his Civil and Sessions Division.",
                    solution_hi: "4.(c) जिला एवं सत्र न्यायाधीश सीधे राज्य के उच्च न्यायालय के नियंत्रण में कार्य करते हैं। जिला और सत्र न्यायाधीश अपने सिविल और सत्र प्रभाग के सभी न्यायालयों के मंत्रिस्तरीय कर्मचारियों का नियंत्रण और नियुक्ति प्राधिकारी है।",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "5",
                    section: "18",
                    question_en: "5. The Dilwara temple is situated at___.",
                    question_hi: "5. दिलवाड़ा मंदिर कहाँ स्थित है?",
                    options_en: [" Aurangabad ", " Bhubaneshwar ", 
                                " Mount Abu", " Khajuraho "],
                    options_hi: [" औरंगाबाद", " भुवनेश्वर",
                                " माउंट आबू", " खजुराहो"],
                    solution_en: "5.(c) The Dilwara Temples of Dilwara Temples are a group of Svetambara Jain temples located in Mount Abu, in Sirohi District, Rajasthan\'s only hill station. This temple was constructed by Bhima Shah, a minister of Sultan Begada of Ahmedabad between 1316 and 1432 A.D. The main attraction of this temple is a huge metal statue of the first Tirthankara, Rishabha Dev or Adinath.",
                    solution_hi: "5.(c) दिलवाड़ा मंदिरों के दिलवाड़ा मंदिर राजस्थान के एकमात्र हिल स्टेशन सिरोही जिले के माउंट आबू में स्थित श्वेतांबर जैन मंदिरों का एक समूह है। इस मंदिर का निर्माण अहमदाबाद के सुल्तान बेगड़ा के मंत्री भीम शाह ने 1316 और 1432 ईस्वी के बीच करवाया था। इस मंदिर का मुख्य आकर्षण पहले तीर्थंकर, ऋषभ देव या आदिनाथ की विशाल धातु की मूर्ति है।",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "6",
                    section: "18",
                    question_en: "6. The pre-monsoon showers which are a common phenomenon in Kerala and the coastal areas of Karnataka are locally known as____.",
                    question_hi: "6. प्री-मानसून वर्षा जो केरल और कर्नाटक के तटीय क्षेत्रों में एक सामान्य घटना है, को स्थानीय रूप से किस नाम से जाना जाता है?",
                    options_en: [" mango showers ", " winter showers ", 
                                " Nor Westers ", " blossom showers"],
                    options_hi: [" आम की बारिश", " सर्दियों की बारिश",
                                " नोर वेस्टर ", " फूलों की बारिश"],
                    solution_en: "6.(a) Mango showers are the name of the pre-monsoon showers in Karnataka, Kerala, Konkan, and Goa that help in the ripening of mangoes. They are also known as April rains or summer showers.",
                    solution_hi: "6.(a) मैंगो शावर कर्नाटक, केरल, कोंकण और गोवा में प्री-मानसून वर्षा का नाम है जो आम के पकने में मदद करता है। उन्हें अप्रैल की बारिश या गर्मियों की बारिश के रूप में भी जाना जाता है।",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "7",
                    section: "18",
                    question_en: "7.  Which of the following vitamins is rich in yeast?",
                    question_hi: "7. निम्नलिखित में से कौन सा विटामिन खमीर में समृद्ध है",
                    options_en: [" Vitamin K ", " Vitamin C ", 
                                " Vitamin A ", " Vitamin B "],
                    options_hi: [" विटामिन K", " विटामिन C",
                                " विटामिन A", " विटामिन B"],
                    solution_en: "7.(d) Yeast is an important source of Vitamin B. It is also known as riboflavin and invertase. It is best for body growth, helps in making the body stronger from inside, and is important for the digestive functioning of the body.",
                    solution_hi: "7.(d) यीस्ट विटामिन बी का एक महत्वपूर्ण स्रोत है। इसे राइबोफ्लेविन और इनवर्टेज के नाम से भी जाना जाता है। यह शरीर के विकास के लिए सबसे अच्छा है, शरीर को अंदर से मजबूत बनाने में मदद करता है, और शरीर के पाचन क्रिया के लिए महत्वपूर्ण है।",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "8",
                    section: "18",
                    question_en: "8. Who was the runner-up in the men\'s singles Australian Open final in 2020?",
                    question_hi: "8. 2020 में पुरुष एकल ऑस्ट्रेलियन ओपन के फाइनल में उपविजेता कौन था?",
                    options_en: [" Frances Tiafoe ", " Rafael Nadal ", 
                                " Dominic Thiem ", " Novak Djokovic "],
                    options_hi: [" फ्रांसिस टियाफो", " राफेल नडाल",
                                " डोमिनिक थिएम", " नोवाक जोकोविच"],
                    solution_en: "8.(c)  Dominic Thiem was the runner-up in the men\'s singles Australian Open final in 2020. Novak Djokovic defeated No. 4 Daniil Medvedev  to win the Australian open in 2021.",
                    solution_hi: "8.(c) डोमिनिक थिएम 2020 में पुरुष एकल ऑस्ट्रेलियन ओपन के फाइनल में उपविजेता रहे। नोवाक जोकोविच ने नंबर 4 डेनियल मेदवेदेव को हराकर 2021 में ऑस्ट्रेलियन ओपन जीता।",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "9",
                    section: "18",
                    question_en: "9. Who among the following created history by becoming the first girl student of the Indian Institute of Technology- Madras (IIT-M) to win the President of India Prize 2019?",
                    question_hi: "9. निम्नलिखित में से किसने भारतीय प्रौद्योगिकी संस्थान- मद्रास (IIT-M) की पहली छात्रा बनकर भारत का राष्ट्रपति पुरस्कार 2019 जीता?",
                    options_en: [" Payal Jangid ", " Kavitha Gopal ", 
                                " Kalli Purie ", " Kavitha Swaminathan "],
                    options_hi: [" पायल जांगिड़", " कविता गोपाल",
                                " कल्ली पुरी", " कविता स्वामीनाथन"],
                    solution_en: "9.(b) Kavita Gopal created history by becoming the first girl student of the Indian Institute of Technology- Madras (IIT-M) to win the President of India Prize 2019.",
                    solution_hi: "9.(b)  कविता गोपाल ने भारतीय प्रौद्योगिकी संस्थान- मद्रास (IIT-M) की पहली छात्रा बनकर इतिहास रच दिया, जिसने भारत का राष्ट्रपति पुरस्कार 2019 जीता।",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "10",
                    section: "18",
                    question_en: "10. Penicillium is a/an___.",
                    question_hi: "10. पेनिसिलियम एक________ है",
                    options_en: [" bacteria ", " fungi ", 
                                " virus ", " algae "],
                    options_hi: [" बैक्टीरिया", " कवक",
                                " वायरस", " शैवाल"],
                    solution_en: "10.(b) Penicillium is a genus of ascomycetous fungi that is part of the mycobiome of many species and is of major importance in the natural environment. Alexander Fleming discovered penicillin. This discovery led to the introduction of antibiotics that greatly reduced the number of deaths from infection.",
                    solution_hi: "10.(b) पेनिसिलियम असममित कवक का एक जीनस है जो कई प्रजातियों के माइकोबायोम का हिस्सा है और प्राकृतिक वातावरण में प्रमुख महत्व रखता है। अलेक्जेंडर फ्लेमिंग ने पेनिसिलिन की खोज की। इस खोज ने एंटीबायोटिक दवाओं की शुरूआत की जिससे संक्रमण से होने वाली मौतों की संख्या में काफी कमी आई।",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "11",
                    section: "18",
                    question_en: "11. In India, National Income is computed by:",
                    question_hi: "11. भारत में, राष्ट्रीय आय की गणना किसके द्वारा की जाती है?",
                    options_en: [" Central Statistical Organisation ", " NITI Aayog ", 
                                " Ministry of Finance ", " National Sample Survey Office "],
                    options_hi: [" केंद्रीय सांख्यिकी संगठन", " नीति आयोग",
                                " वित्त मंत्रित्व", " राष्ट्रीय नमूना सर्वेक्षण कार्यालय"],
                    solution_en: "11.(a) In India, Central Statistical Organisation (1949) now renamed as Central Statistical Office (CSO) has been formulating National Income. The national income of the country is measured by the Product method, Income method, and expenditure method.",
                    solution_hi: "11.(a) भारत में, केंद्रीय सांख्यिकी संगठन (1949) का नाम बदलकर अब केंद्रीय सांख्यिकी कार्यालय (CSO) कर दिया गया है, जो राष्ट्रीय आय तैयार कर रहा है। देश की राष्ट्रीय आय को उत्पाद विधि, आय विधि और व्यय विधि द्वारा मापा जाता है।",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "12",
                    section: "18",
                    question_en: "12. In India, the impeachment process is NOT applicable to___ for removal from office.",
                    question_hi: "12. भारत में, महाभियोग प्रक्रिया कार्यालय से हटाने के लिए ___ पर लागू नहीं होती है",
                    options_en: [" Chief Justice of India ", " Chief Election Commissioner ", 
                                " The Prime Minister ", " President of India "],
                    options_hi: [" भारत के मुख्य न्यायाधीश", " मुख्य चुनाव आयुक्त",
                                " प्रधानमंत्री", " भारत के राष्ट्रपति"],
                    solution_en: "12.(c) In India, the impeachment process is NOT applicable to the Prime Minister for removal from office. The prime minister serves on \'the pleasure of the president\', hence, a prime minister may remain in office indefinitely, so long as the president has confidence in him/her. However, a prime minister must have the confidence of the Lok Sabha, the lower house of the Parliament of India.",
                    solution_hi: "12.(c)  भारत में, महाभियोग की प्रक्रिया प्रधान मंत्री को पद से हटाने के लिए लागू नहीं होती है। प्रधान मंत्री \'राष्ट्रपति की खुशी\' पर कार्य करता है, इसलिए, एक प्रधान मंत्री अनिश्चित काल तक पद पर रह सकता है, जब तक कि राष्ट्रपति को उस पर भरोसा है। हालाँकि, एक प्रधान मंत्री को भारत की संसद के निचले सदन, लोकसभा का विश्वास होना चाहिए।",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "13",
                    section: "18",
                    question_en: "13. A light-year is a unit of____",
                    question_hi: "13. एक प्रकाश वर्ष ________ की एक इकाई है",
                    options_en: [" Distance ", " Intensity of light ", 
                                " Time ", " Mass "],
                    options_hi: [" दूरी", " प्रकाश की तीव्रता",
                                " समय ", " द्र्व्यमान "],
                    solution_en: "13.(a) A light-year is the distance a beam of light travels in a single Earth year, or 6 trillion miles (9.7 trillion kilometers). Light moves at a velocity of about 300,000 kilometers (km) each second.",
                    solution_hi: "13.(a) एक प्रकाश वर्ष वह दूरी है जो प्रकाश की किरण एक पृथ्वी वर्ष में यात्रा करती है, या 6 ट्रिलियन मील (9.7 ट्रिलियन किलोमीटर) है। प्रकाश प्रति सेकंड लगभग 300,000 किलोमीटर (किमी) के वेग से चलता है।",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "14",
                    section: "18",
                    question_en: "14. In which of the following cities is the Charminar situated?",
                    question_hi: "14. निम्नलिखित में से किस शहर में चारमीनार स्थित है?",
                    options_en: [" Kanpu ", " Hyderabad ", 
                                " New Delhi ", " Bangalore "],
                    options_hi: [" कानपुर", " हैदराबाद",
                                " नई दिल्ली", " बैंगलोर"],
                    solution_en: "14.(b) The Charminar constructed in 1591, is a monument and mosque located in Hyderabad, Telangana, India. The monument was built in 1591 by Muḥammad Quli Quṭb Shah, the fifth king of the Quṭb Shahi dynasty. ",
                    solution_hi: "14.(b) 1591 में निर्मित चारमीनार, हैदराबाद, तेलंगाना, भारत में स्थित एक स्मारक और मस्जिद है। स्मारक 1591 में क़ुब शाही वंश के पांचवें राजा मुहम्मद कुली क़ुब शाह द्वारा बनवाया गया था।",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "15",
                    section: "18",
                    question_en: "15. Which of the following is India\'s largest oil company?",
                    question_hi: "15. निम्नलिखित में से कौन भारत की सबसे बड़ी तेल कंपनी है?",
                    options_en: [" Indian Oil Corporation Limited ", " Bharat Petroleum ", 
                                " Hindustan Petroleum ", " Oil and Natural Gas Company "],
                    options_hi: [" इंडियन ऑयल कॉर्पोरेशन लिमिटेड", " भारत पेट्रोलियम",
                                " हिंदुस्तान पेट्रोलियम", " तेल और प्राकृतिक गैस कंपनी"],
                    solution_en: "15.(a) Indian Oil Corporation (Maharatna)is India’s largest oil company, it was founded on 30 June 1959. Chairperson- Shrikant Madhav Vaidya and headquarter is in Mumbai.",
                    solution_hi: "15.(a) इंडियन ऑयल कॉर्पोरेशन (महारत्न) भारत की सबसे बड़ी तेल कंपनी है, इसकी स्थापना 30 जून 1959 को हुई थी। अध्यक्ष- श्रीकांत माधव वैद्य और मुख्यालय मुंबई में है।",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "16",
                    section: "18",
                    question_en: "16. World Red Cross day is celebrated on:",
                    question_hi: "16. विश्व रेड क्रॉस दिवस कब मनाया जाता है?",
                    options_en: [" 8th May ", " 15th June ", 
                                " 8th July ", " 18th May "],
                    options_hi: [" 8 मई", " 15 जून",
                                " 8 जुलाई", " 18 मई"],
                    solution_en: "16.(a) World Red Cross day is celebrated on 8th May. World Red Cross Day is celebrated to commemorate the principles of the International Red Cross and Red Crescent Movement. ",
                    solution_hi: "16.(a) विश्व रेड क्रॉस दिवस 8 मई को मनाया जाता है। विश्व रेड क्रॉस दिवस अंतर्राष्ट्रीय रेड क्रॉस और रेड क्रिसेंट आंदोलन के सिद्धांतों को मनाने के लिए मनाया जाता है।",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "17",
                    section: "18",
                    question_en: "17. Who is the first women Indian Police Service officer to reach the South pole?",
                    question_hi: "17. दक्षिणी ध्रुव पर पहुंचने वाली पहली महिला भारतीय पुलिस सेवा अधिकारी कौन है?",
                    options_en: [" Aparna Kumar ", " Kiran Bedi ", 
                                " Raisina Dialogue ", " Kavita Desai "],
                    options_hi: [" अपर्णा कुमार", " किरण बेदी",
                                " रायसीना डायलॉग", " कविता देसाई"],
                    solution_en: "17.(a) The first women Indian Police Service officer to reach the South pole is Deputy Inspector General (DIG) Aparna Kumar, a 2002-batch Indian Police Service officer of the Uttar Pradesh cadre and a mother of two children, is the senior-most field officer of ITBP on the ground.",
                    solution_hi: "17.(a) दक्षिण ध्रुव पर पहुंचने वाली पहली महिला भारतीय पुलिस सेवा अधिकारी उप महानिरीक्षक (डीआईजी) अपर्णा कुमार हैं, जो उत्तर प्रदेश कैडर की 2002-बैच की भारतीय पुलिस सेवा की अधिकारी हैं और दो बच्चों की मां हैं, जो कि सबसे वरिष्ठ क्षेत्रीय आईटीबीपी (ITBP) अधिकारी हैं।  ",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "18",
                    section: "18",
                    question_en: "18. The Directive Principles of State Policy are mentioned in which of the following parts of the constitution of India?",
                    question_hi: "18. राज्य के नीति निदेशक तत्वों का उल्लेख भारत के संविधान के निम्नलिखित में से किस भाग में किया गया है?",
                    options_en: [" Part 4 ", " Part 5 ", 
                                " Part 3 ", " Part 6 "],
                    options_hi: [" भाग 4", " भाग 5",
                                " भाग 3", " भाग 6"],
                    solution_en: "18.(a) Part 4 of the Constitution of India (Article 36–51) contains the Directive Principles of State Policy (DPSP). Part 5 deals with Union. Article 12 to 35 contained in Part III of the Constitution deals with Fundamental Rights. Part 6 of the Indian constitution contains Article 152 to Article 237. Part 6 mainly focuses on the state government.",
                    solution_hi: "18.(a) भारत के संविधान के भाग 4 (अनुच्छेद 36-51) में राज्य के नीति निर्देशक सिद्धांत (DPSP) शामिल हैं। भाग 5 संघ से संबंधित है। संविधान के भाग III में निहित अनुच्छेद 12 से 35 मौलिक अधिकारों से संबंधित है। भारतीय संविधान के भाग 6 में अनुच्छेद 152 से अनुच्छेद 237 शामिल हैं। भाग 6 मुख्य रूप से राज्य सरकार पर केंद्रित है।",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "19",
                    section: "18",
                    question_en: "19. Which of the following is a renewable energy resource?",
                    question_hi: "19. निम्नलिखित में से कौन सा अक्षय ऊर्जा संसाधन है?",
                    options_en: [" Petroleum ", " Syngas ", 
                                " Coal ", " Natural gas "],
                    options_hi: [" पेट्रोलियम", " सिनगैस",
                                " कोयला", " प्राकृतिक गैस"],
                    solution_en: "19.(b) Syngas is a renewable energy resource. Syngas, or synthesis gas, is a fuel gas mixture consisting primarily of hydrogen, carbon monoxide, and very often some carbon dioxide. Renewable energy is derived from natural processes that are replenished constantly.",
                    solution_hi: "19.(b) Syngas एक अक्षय ऊर्जा संसाधन है। Syngas, या संश्लेषण गैस, एक ईंधन गैस मिश्रण है जिसमें मुख्य रूप से हाइड्रोजन, कार्बन मोनोऑक्साइड और अक्सर कुछ कार्बन डाइऑक्साइड होता है। अक्षय ऊर्जा प्राकृतिक प्रक्रियाओं से प्राप्त होती है जो लगातार भर जाती हैं।",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "20",
                    section: "18",
                    question_en: "20. Which country in the world has the highest natural gas reserves?",
                    question_hi: "20. विश्व के किस देश में प्राकृतिक गैस का सर्वाधिक भंडार है?",
                    options_en: [" Iraq ", " Russia ", 
                                " Iran ", " UAE "],
                    options_hi: [" इराक", " रूस",
                                " ईरान", " संयुक्त अरब अमीरात"],
                    solution_en: "20.(b) Russia in the world has the highest natural gas reserves. 2nd is Iran and 3rd is Qatar. Natural gas is a mixture of gases that are rich in hydrocarbons. All these gases (methane, nitrogen, carbon dioxide, etc) are naturally found in the atmosphere. ",
                    solution_hi: "20.(b) दुनिया में रूस के पास सबसे ज्यादा प्राकृतिक गैस का भंडार है। दूसरा ईरान और तीसरा कतर है। प्राकृतिक गैस हाइड्रोकार्बन से भरपूर गैसों का मिश्रण है। ये सभी गैसें (मीथेन, नाइट्रोजन, कार्बन डाइऑक्साइड, आदि) प्राकृतिक रूप से वातावरण में पाई जाती हैं।",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "21",
                    section: "18",
                    question_en: "21. What form of art is used to decorate the floor in front of the house?",
                    question_hi: "21. घर के सामने फर्श को सजाने के लिए किस प्रकार की कला का प्रयोग किया जाता है?",
                    options_en: [" Mehandi ", " Worli ", 
                                " Madhubani ", " Kolam "],
                    options_hi: [" मेहँदी", " वर्ली",
                                " मधुबनी", " कोलम"],
                    solution_en: "21.(d) Kolam is the name given to the art of Rangoli in the southern parts of India, mainly in the states of Tamil Nadu and Kerala. Warli painting is a form of tribal art mostly created by the tribal people from the North Sahyadri Range in Maharashtra, India. The folk art of Bihar, commonly known as Madhubani, refers to the market town of the same name. The antiquity of this land and its religious histories have made the people of Bihar deeply religious.",
                    solution_hi: "21.(d) कोलम भारत के दक्षिणी हिस्सों में मुख्य रूप से तमिलनाडु और केरल राज्यों में रंगोली की कला को दिया गया नाम है। वार्ली पेंटिंग आदिवासी कला का एक रूप है जो ज्यादातर भारत के महाराष्ट्र में उत्तरी सह्याद्री रेंज के आदिवासी लोगों द्वारा बनाई गई है। बिहार की लोक कला, जिसे आमतौर पर मधुबनी के नाम से जाना जाता है, उसी नाम के बाजार शहर को संदर्भित करता है। इस भूमि की प्राचीनता और इसके धार्मिक इतिहास ने बिहार के लोगों को गहरा धार्मिक बना दिया है।",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "22",
                    section: "18",
                    question_en: "22. The first Lokpal Bill was introduced in the Indian Parliament in:",
                    question_hi: "22. पहला लोकपाल बिल भारतीय संसद में पेश किया गया था",
                    options_en: [" 1968", " 1975", 
                                " 1965", " 1972"],
                    options_hi: [" 1968", " 1975",
                                " 1965", " 1972"],
                    solution_en: "22.(a) The first Jan Lokpal Bill was proposed by Adv Shanti Bhushan in 1968 and passed in the 4th Lok Sabha in 1969, but did not pass through the Rajya Sabha. The First Chairperson of the Lokpal is Shri Justice Pinaki Chandra Ghose. The Jan Lokpal Bill is also referred to as the Citizen\'s Ombudsman Bill.",
                    solution_hi: "22.(a)  पहला जन लोकपाल विधेयक 1968 में अधिवक्ता शांति भूषण द्वारा प्रस्तावित किया गया था और 1969 में चौथी लोकसभा में पारित किया गया था, लेकिन राज्यसभा से पारित नहीं हुआ था। लोकपाल के पहले अध्यक्ष श्री न्यायमूर्ति पिनाकी चंद्र घोष हैं। जन लोकपाल विधेयक को नागरिक लोकपाल विधेयक भी कहा जाता है।",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "23",
                    section: "18",
                    question_en: "23. Which of the following is NOT a UN Agenda for Sustainable Development 2030?",
                    question_hi: "23. निम्नलिखित में से कौन सतत विकास 2030 के लिए संयुक्त राष्ट्र एजेंडा नहीं है?",
                    options_en: [" End poverty in all its forms everywhere ", " Reduce inequality within and among nations ", 
                                " Reduce the total population of the world by <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math>rd ", " Achieve food security by 2030 "],
                    options_hi: [" हर जगह गरीबी को उसके सभी रूपों में समाप्त करना ", " राष्ट्रों के भीतर और उनके बीच असमानता को कम करना ",
                                " विश्व की कुल जनसंख्या में  <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math>rd कम करना ", " 2030 तक खाद्य सुरक्षा प्राप्त करना "],
                    solution_en: "23.(c) The Global Goals and the 2030 Agenda for Sustainable Development seek to end poverty and hunger, realize the human rights of all, achieve gender equality and the empowerment of all women and girls, and ensure the lasting protection of the planet and its natural resources.",
                    solution_hi: "23.(c) सतत विकास के लिए वैश्विक लक्ष्य और 2030 एजेंडा गरीबी और भूख को समाप्त करने, सभी के मानवाधिकारों का एहसास करने, लैंगिक समानता और सभी महिलाओं और लड़कियों के सशक्तिकरण को प्राप्त करने और ग्रह और उसके प्राकृतिक संसाधनों की स्थायी सुरक्षा सुनिश्चित करने का प्रयास करते हैं।",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "24",
                    section: "18",
                    question_en: "24.The Poona Pact was signed between:",
                    question_hi: "24. पूना समझौता किसके बीच हुआ था?",
                    options_en: [" M K Gandhi and S C Bose ", " M K Gandhi and B R Ambedkar ", 
                                " M K Gandhi and Lord Irwin ", " M K Gandhi and M A Jinnah "],
                    options_hi: [" एम के गांधी और एस सी बोस", " एम के गांधी और बी आर अंबेडकर",
                                " एम के गांधी और लॉर्ड इरविन", " एम के गांधी और एम ए जिन्ना"],
                    solution_en: "24.(b) The Poona Pact was signed on September 24 by 23 people. Madan Mohan Malaviya signed it on behalf of Hindus and Gandhi, and Ambedkar on behalf of depressed classes.",
                    solution_hi: "24.(b) 24 सितंबर को 23 लोगों ने पूना पैक्ट पर दस्तखत किए थे। मदन मोहन मालवीय ने हिंदुओं और गांधी की ओर से और अम्बेडकर ने दलित वर्गों की ओर से इस पर हस्ताक्षर किए।",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "25",
                    section: "18",
                    question_en: "25. Who is considered to be the Father of Modern Computers?",
                    question_hi: "25. आधुनिक कंप्यूटर का जनक किसे माना जाता है?",
                    options_en: [" Charles Babbage ", " Alan Turing ", 
                                " James Gosling ", " Gordon E Moore "],
                    options_hi: [" चार्ल्स बैबेज", " एलन ट्यूरिंग",
                                " जेम्स गोस्लिंग", " गॉर्डन ई मूर"],
                    solution_en: "25.(b) Alan Turing is considered to be the Father of Modern Computers. Charles Babbage is regarded as the father of computing because of his research into machines that could calculate. James Gosling created Java. Gordon E Moore is the co-founder and Chairman Emeritus of Intel Corporation.",
                    solution_hi: "25.(b) एलन ट्यूरिंग को आधुनिक कंप्यूटर का जनक माना जाता है। चार्ल्स बैबेज को गणना करने वाली मशीनों में अपने शोध के कारण कंप्यूटिंग का जनक माना जाता है। जेम्स गोस्लिंग ने जावा बनाया। गॉर्डन ई मूर इंटेल कॉर्पोरेशन के सह-संस्थापक और अध्यक्ष एमेरिटस हैं।",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "26",
                    section: "18",
                    question_en: "26. If untreated, HIV can lead to____.",
                    question_hi: "26. यदि अनुपचारित किया जाता है, तो HIV _________ को जन्म दे सकता है।",
                    options_en: [" cancer ", " hepatitis ", 
                                " plague ", " AIDS "],
                    options_hi: [" कैंसर", " हेपेटाइटिस",
                                " प्लेग", " एड्स"],
                    solution_en: "26.(d) If left untreated, HIV can lead to the disease AIDS (acquired immunodeficiency syndrome). Hepatitis is inflammation of the liver. A plague is a disease that affects humans and other mammals. It is caused by the bacterium, Yersinia pestis.",
                    solution_hi: "26.(d) यदि अनुपचारित छोड़ दिया जाता है, तो एचआईवी एड्स (एक्वायर्ड इम्युनोडेफिशिएंसी सिंड्रोम) रोग का कारण बन सकता है। हेपेटाइटिस यकृत की सूजन है। प्लेग एक ऐसी बीमारी है जो मनुष्यों और अन्य स्तनधारियों को प्रभावित करती है। यह जीवाणु यर्सिनिया पेस्टिस के कारण होता है।",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "27",
                    section: "18",
                    question_en: "27. Who was the first Indian woman president of the Indian National Congress?",
                    question_hi: "27. भारतीय राष्ट्रीय कांग्रेस की पहली भारतीय महिला अध्यक्ष कौन थी?",
                    options_en: [" Sarojini Naidu ", " Indira Gandhi ", 
                                " Annie Besant ", " Vijay Laxmi Pandit  "],
                    options_hi: [" सरोजिनी नायडू", " इंदिरा गांधी",
                                " एनी बेसेंट", " विजय लक्ष्मी पंडित"],
                    solution_en: "27.(a) Sarojini Naidu was appointed as the President of the Indian National Congress in 1925 and later became the Governor of the United Provinces in 1947, becoming the first woman to hold the office of Governor in the Dominion of India.",
                    solution_hi: "27.(a) सरोजिनी नायडू को 1925 में भारतीय राष्ट्रीय कांग्रेस के अध्यक्ष के रूप में नियुक्त किया गया था और बाद में 1947 में संयुक्त प्रांत की राज्यपाल बनीं, जो भारत के डोमिनियन में राज्यपाल का पद संभालने वाली पहली महिला बनीं।",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "28",
                    section: "18",
                    question_en: "28.Which of the following is the birthplace of Lord Mahavira in the early 6th century BC?",
                    question_hi: "28. निम्नलिखित में से कौन सी छठी शताब्दी ईसा पूर्व में भगवान महावीर का जन्मस्थान है?",
                    options_en: [" Magadha", " Vaishali ", 
                                " Sarnath ", " Patliputra "],
                    options_hi: [" मगध", " वैशाली",
                                " सारनाथ", " पाटलिपुत्र"],
                    solution_en: "28.(b) Lord Mahavira was born Prince Vardhamana to King Siddhartha and Queen Trishala of the Ikshvaku Dynasty. He was born in 599 B.C. on the thirteenth day of the rising moon during the month of Chaitra in the Vira Nirvana Samvat calendar.",
                    solution_hi: "28.(b) भगवान महावीर का जन्म इक्ष्वाकु राजवंश के राजा सिद्धार्थ और रानी त्रिशला के राजकुमार वर्धमान से हुआ था। उनका जन्म 599 ईसा पूर्व में हुआ था। वीर निर्वाण संवत कैलेंडर में चैत्र महीने के दौरान उगते चंद्रमा के तेरहवें दिन।",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "29",
                    section: "18",
                    question_en: "29. As of October 2020, how many Public Sector banks are there in India?",
                    question_hi: "29. अक्टूबर 2020 तक, भारत में कितने सार्वजनिक क्षेत्र के बैंक हैं?",
                    options_en: [" 12", " 20", 
                                " 22", " 21"],
                    options_hi: [" 12", " 20",
                                " 22", " 21"],
                    solution_en: "29.(a)  As of October 2020 after the recent mergers of government banks, there are a total of 12 nationalized banks in India. Oriental Bank of Commerce and United Bank of India were merged with Punjab National Bank. Then Allahabad Bank was merged with Indian Bank. Syndicate Bank was merged with Canara Bank. Andhra Bank and Corporation Bank were merged with Union Bank of India.",
                    solution_hi: "29.(a) अक्टूबर 2020 तक सरकारी बैंकों के हालिया विलय के बाद, भारत में कुल 12 राष्ट्रीयकृत बैंक हैं। ओरिएंटल बैंक ऑफ कॉमर्स और यूनाइटेड बैंक ऑफ इंडिया का पंजाब नेशनल बैंक में विलय कर दिया गया। फिर इलाहाबाद बैंक का इंडियन बैंक में विलय कर दिया गया। सिंडिकेट बैंक का केनरा बैंक में विलय कर दिया गया था। आंध्रा बैंक और कॉर्पोरेशन बैंक का यूनियन बैंक ऑफ इंडिया में विलय कर दिया गया।",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "30",
                    section: "18",
                    question_en: "30. Which of the following Book authored by Jean Pierre Harrison is based on the biography of Kalpana Chawla?",
                    question_hi: "30. जीन पियरे हैरिसन द्वारा लिखित निम्नलिखित में से कौन सी पुस्तक कल्पना चावला की जीवनी पर आधारित है?",
                    options_en: [" The Edge of time ", " Sleeping on Jupiter ", 
                                " The sense of an ending ", " The luminaries "],
                    options_hi: [" द एज ऑफ़ टाइम ", " स्लीपिंग ओन जुपिटर ",
                                " द सेंस ऑफ़ एन एंडिंग ", " द लुमिनिरिज "],
                    solution_en: "30.(a) The Edge of Time- The Authoritative Biography of Kalpana Chawla by Jean-Pierre Harrison. Sleeping on Jupiter is a novel by Anuradha Roy. The Sense of an Ending is a 2011 novel written by British author Julian Barnes. The Luminaries is a 2013 novel by Eleanor Catton.",
                    solution_hi: "30.(a)  द एज ऑफ़ टाइम- कल्पना चावला की आधिकारिक जीवनी जीन-पियरे हैरिसन द्वारा। स्लीपिंग ऑन जुपिटर अनुराधा रॉय का एक उपन्यास है। द सेंस ऑफ एन एंडिंग ब्रिटिश लेखक जूलियन बार्न्स द्वारा लिखित 2011 का एक उपन्यास है। द ल्यूमिनरीज़ एलेनोर कैटन का 2013 का उपन्यास है।",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "31",
                    section: "18",
                    question_en: "31. Who inspired the Young Bengal movement?",
                    question_hi: "31. यंग बंगाल आंदोलन को किसने प्रेरित किया?",
                    options_en: [" Henry Vivian Derozio ", " Krishna Mohan Banerjee ", 
                                " Madhusudan Dutt ", " Ram Gopal Ghosh "],
                    options_hi: [" हेनरी विवियन डेरोजियो", " कृष्ण मोहन बनर्जी",
                                " मधुसूदन दत्त", " राम गोपाल घोष"],
                    solution_en: "31.(a) Young Bengal Movement was launched by Henry Louis Vivian Derozio (1809-1831), who had come to Calcutta in 1826. The main aim of the Young Bengal Movement was to promote radical ideas through teachings and by organizing debates and discussions on Literature, History, Philosophy, and Science.",
                    solution_hi: "31.(a) यंग बंगाल मूवमेंट हेनरी लुइस विवियन डेरोजियो (1809-1831) द्वारा शुरू किया गया था, जो 1826 में कलकत्ता आए थे। यंग बंगाल मूवमेंट का मुख्य उद्देश्य शिक्षाओं के माध्यम से कट्टरपंथी विचारों को बढ़ावा देना और साहित्य, इतिहास पर बहस और चर्चा का आयोजन करना था। दर्शनशास्त्र, और विज्ञान।",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "32",
                    section: "18",
                    question_en: "32. Internet was started in India on:",
                    question_hi: "32. भारत में इंटरनेट की शुरुआत कब हुई:",
                    options_en: [" 9th August, 1996 ", " 8th August, 1994 ", 
                                " 15th August, 1995 ", " 11th August, 1995 "],
                    options_hi: [" 9 अगस्त, 1996", " 8 अगस्त, 1994",
                                " 15 अगस्त, 1995", " 11 अगस्त, 1995"],
                    solution_en: "32.(c) Internet services were launched in India on 15th August 1995 by Videsh Sanchar Nigam Limited.",
                    solution_hi: "32.(c) भारत में इंटरनेट सेवाओं की शुरुआत 15 अगस्त 1995 को विदेश संचार निगम लिमिटेड द्वारा की गई थी।",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "33",
                    section: "18",
                    question_en: "33. Which one among the following is the Asia\'s first stock exchange?",
                    question_hi: "33. निम्नलिखित में से कौन एशिया का पहला स्टॉक एक्सचेंज है?",
                    options_en: [" Shanghai Stock Exchange ", " National Stock Exchange ", 
                                " Bombay Stock Exchange ", " Tokyo Stock Exchange "],
                    options_hi: [" शंघाई स्टॉक एक्सचेंज", " नेशनल स्टॉक एक्सचेंज",
                                " बॉम्बे स्टॉक एक्सचेंज", " टोक्यो स्टॉक एक्सचेंज"],
                    solution_en: "33.(c) Bombay Stock Exchange (BSE), the first-ever stock exchange in Asia established in 1875 and the first in the country to be granted permanent recognition under the Securities Contract Regulation Act, 1956.",
                    solution_hi: "33.(c) बॉम्बे स्टॉक एक्सचेंज (बीएसई), 1875 में स्थापित एशिया का पहला स्टॉक एक्सचेंज और सिक्योरिटीज कॉन्ट्रैक्ट रेगुलेशन एक्ट, 1956 के तहत स्थायी मान्यता प्रदान करने वाला देश का पहला स्टॉक एक्सचेंज है।",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "34",
                    section: "18",
                    question_en: "34. Diphu pass is situated in:",
                    question_hi: "34. दीफू दर्रा अवस्थित कहाँ है?",
                    options_en: [" Arunachal Himalayas ", " Pir Panjal range in Himalayas ", 
                                " Darjeeling and Sikkim Himalayas ", "  Kashmir or North western Himalayas "],
                    options_hi: [" अरुणाचल हिमालय", " हिमालय में पीर पंजाल रेंज",
                                " दार्जिलिंग और सिक्किम हिमालय", " कश्मीर या उत्तर पश्चिमी हिमालय"],
                    solution_en: "34.(a) Diphu Pass is also a strategic approach to eastern Arunachal Pradesh in India. It lies on the McMahon Line. Passes in Arunachal Pradesh are Bom Di La, Dihang Pass, Yong Yap Pass,Kumajwang pass, Hapungan Pass, Chankan Pass.",
                    solution_hi: "34.(a) दीफू दर्रा भारत में पूर्वी अरुणाचल प्रदेश के लिए एक रणनीतिक दृष्टिकोण भी है। यह मैकमोहन रेखा पर स्थित है। अरुणाचल प्रदेश में पास बोम दी ला, दिहांग दर्रा, योंग याप दर्रा, कुमाजवांग दर्रा, हापुंगन दर्रा, चंकन दर्रा हैं।",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "35",
                    section: "18",
                    question_en: "35. Which of the following in polluted air causes Hay fever?",
                    question_hi: "35. प्रदूषित हवा में निम्नलिखित में से कौन हे फीवर का कारण बनता है?",
                    options_en: [" Ozone ", " Particulate matter ", 
                                " Pollen ", " Carbon monoxide "],
                    options_hi: [" ओजोन", " कणिका तत्व",
                                " पराग", " कार्बन मोनोआक्साइड"],
                    solution_en: "35.(c) Hay fever is caused by an allergic response to outdoor or indoor allergens, such as pollen, dust mites, or tiny flecks of skin and saliva shed by cats, dogs, and other animals with fur or feathers.",
                    solution_hi: "35.(c) हे फीवर बाहरी या इनडोर एलर्जेंस से एलर्जी की प्रतिक्रिया के कारण होता है, जैसे कि पराग, धूल के कण, या त्वचा के छोटे-छोटे टुकड़े और बिल्लियों, कुत्तों और फर या पंखों वाले अन्य जानवरों द्वारा बहाए गए लार।",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "36",
                    section: "18",
                    question_en: "36. Which city is popularly called the “Detroit of Asia” and the “Automobile Capital” of India?",
                    question_hi: "36. किस शहर को लोकप्रिय रूप से \"एशिया का डेट्रॉइट\" और भारत की \"ऑटोमोबाइल राजधानी\" कहा जाता है?",
                    options_en: [" Coimbatore ", " Hyderabad ", 
                                " Jaipur ", " Chennai "],
                    options_hi: [" कोयंबटूर", " हैदराबाद",
                                " जयपुर", " चेन्नई"],
                    solution_en: "36.(d) Chennai city is popularly called the “Detroit of Asia” and the “Automobile Capital” of India. Chennai is the base of 30% of India\'s automobile industry • Detroit is known as the world\'s \"Automobile Capital\" and \"Motown\" (for \"Motor Town\"), the city where Henry Ford pioneered the automotive assembly line, with the world\'s first mass-produced car, the Model T.",
                    solution_hi: "36.(d) चेन्नई शहर को लोकप्रिय रूप से \"एशिया का डेट्रॉइट\" और भारत की \"ऑटोमोबाइल राजधानी\" कहा जाता है। चेन्नई भारत के ऑटोमोबाइल उद्योग के 30% का आधार है • डेट्रॉइट को दुनिया की \"ऑटोमोबाइल कैपिटल\" और \"मोटाउन\" (\"मोटर टाउन\" के लिए) के रूप में जाना जाता है, वह शहर जहां हेनरी फोर्ड ने दुनिया के पहले बड़े पैमाने के साथ ऑटोमोटिव असेंबली लाइन का बीड़ा उठाया था। -निर्मित कार, मॉडल टी.",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "37",
                    section: "18",
                    question_en: "37. The first nuclear bomb experiment test in India was carried out in the year:",
                    question_hi: "37. भारत में पहला परमाणु बम प्रयोग परीक्षण वर्ष में किया गया था ?",
                    options_en: [" 1999", " 1973", 
                                " 1974", " 1976"],
                    options_hi: [" 1999", " 1973",
                                " 1974", " 1976"],
                    solution_en: "37.(c) Operation Smiling Buddha (Pokhran-I) was the assigned code name of India\'s first successful nuclear bomb test on 18 May 1974. The bomb was detonated on the army base Pokhran Test Range (PTR), in Rajasthan, by the Indian Army under the supervision of several key Indian generals.",
                    solution_hi: "37.(c) ऑपरेशन स्माइलिंग बुद्धा (पोखरण- I) 18 मई 1974 को भारत के पहले सफल परमाणु बम परीक्षण का निर्दिष्ट कोड नाम था। बम को राजस्थान में सेना के आधार पोखरण टेस्ट रेंज (PTR) पर, भारतीय सेना की देखरेख में विस्फोट किया गया था। कई प्रमुख भारतीय जनरलों की।",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "38",
                    section: "18",
                    question_en: "38. The ephedra plant is grouped under:",
                    question_hi: "38. एफेड्रा के पौधे को _______ अधीन समूहीकृत किया गया है",
                    options_en: [" bryophytes ", " gymnosperm ", 
                                " pteridophytes ", " angiosperm "],
                    options_hi: [" ब्रायोफाइट्स", " अनावृतबीजी",
                                " टेरिडोफाइट", " आवृतबीजी"],
                    solution_en: "38.(b) Ephedra is a genus of gymnosperm shrubs. Ephedra, a genus of 65 species of gymnosperm shrubs of the family Ephedraceae. Bryophytes are a proposed taxonomic division containing three groups of non-vascular land plants: the liverworts, hornworts, and mosses. Pteridophytes are cryptogams that have well-developed vascular tissue. Flowering plants are members of the clade Angiospermae, commonly called angiosperms.",
                    solution_hi: "38.(b) एफेड्रा जिम्नोस्पर्म झाड़ियों की एक प्रजाति है। एफेड्रा, परिवार इफेड्रेसी के जिम्नोस्पर्म झाड़ियों की 65 प्रजातियों की एक प्रजाति। ब्रायोफाइट्स एक प्रस्तावित टैक्सोनोमिक डिवीजन है जिसमें गैर-संवहनी भूमि पौधों के तीन समूह शामिल हैं: लिवरवॉर्ट्स, हॉर्नवॉर्ट्स और मॉस। टेरिडोफाइट्स क्रिप्टोगैम हैं जिनमें अच्छी तरह से विकसित संवहनी ऊतक होते हैं। फूल वाले पौधे क्लैड एंजियोस्पर्म के सदस्य होते हैं, जिन्हें आमतौर पर एंजियोस्पर्म कहा जाता है।",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "39",
                    section: "18",
                    question_en: "39. The Jallianwala Bagh massacre took place on ______.",
                    question_hi: "39. जलियांवाला बाग हत्याकांड ______ को हुआ था।",
                    options_en: [" 20 December 1919 ", " 25 May 1919 ", 
                                " 13 April 1919 ", " 15 August 1919 "],
                    options_hi: [" 20 दिसंबर 1919", " 25 मई 1919",
                                " 13 अप्रैल 1919", " 15 अगस्त 1919"],
                    solution_en: "39.(c) The Jallianwala Bagh massacre, also known as the Amritsar massacre, took place on 13 April 1919 when the acting Brigadier-General Reginald Dyer ordered British troops to open fire on unarmed Indian civilians.",
                    solution_hi: "39.(c)  जलियांवाला बाग हत्याकांड, जिसे अमृतसर नरसंहार के रूप में भी जाना जाता है, 13 अप्रैल 1919 को हुआ जब कार्यवाहक ब्रिगेडियर-जनरल रेजिनाल्ड डायर ने ब्रिटिश सैनिकों को निहत्थे भारतीय नागरिकों पर गोलियां चलाने का आदेश दिया।",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "40",
                    section: "misc",
                    question_en: "40.  In which year was the World Happiness Report published for the first time?",
                    question_hi: "40. वर्ल्ड हैप्पीनेस रिपोर्ट पहली बार किस वर्ष प्रकाशित हुई थी?",
                    options_en: [" 2012 ", " 2015", 
                                " 2010", " 2017"],
                    options_hi: [" 2012 ", " 2015",
                                " 2010", " 2017<br />   "],
                    solution_en: "40.(a) The first World Happiness Report was released on April 1, 2012, as a foundational text for the UN High-Level Meeting: Well-being and Happiness: Defining a New Economic Paradigm, drawing international attention. India’s rank in the World Happiness Report is 139 for 2021 and the top is Finland.",
                    solution_hi: "40.(a) पहली विश्व प्रसन्नता रिपोर्ट 1 अप्रैल, 2012 को संयुक्त राष्ट्र की उच्च स्तरीय बैठक के आधारभूत पाठ के रूप में जारी की गई थी: कल्याण और खुशी: एक नए आर्थिक प्रतिमान को परिभाषित करना, अंतर्राष्ट्रीय ध्यान आकर्षित करना। वर्ल्ड हैप्पीनेस रिपोर्ट में भारत की रैंक 2021 के लिए 139 है और शीर्ष पर फिनलैंड है।  ",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>