<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Smart Test Interface</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        .question-nav{position:fixed;top:70px;right:0;height:calc(100vh - 70px);width:320px;transition:all .3s;z-index:100;display:flex;flex-direction:column}
        .question-content{margin-right:0;transition:all .3s}
        .question-box{width:40px;height:40px;margin:5px;cursor:pointer}
        .attempted{background-color:#3498db!important;color:white}
        .correct{background-color:#2ecc71!important;color:white}
        .incorrect{background-color:#e74c3c!important;color:white}
        .current{border:3px solid #f39c12!important}
        #question-boxes{overflow-y:auto;max-height:calc(100vh - 150px);padding-bottom:15px}
        @media (max-width:768px){.question-nav{width:100%;right:-100%}.question-content{margin-right:0}}
    </style>
</head>
<body>
    <div class="container-fluid p-0">
        <!-- Header with controls -->
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary sticky-top">
            <div class="container-fluid">
                <a class="navbar-brand" href="#">
                    <i class="fas fa-graduation-cap me-2"></i>Smart Test
                </a>
                <!-- Section selector -->
                <div class="navbar-nav me-auto section-tabs">
                    <select class="form-select" id="section-selector" onchange="switchSection(this.value)">
                        <option value="all">All Sections</option>
                    </select>
                </div>
                <div class="d-flex align-items-center">
                    <!-- Timer -->
                    <div class="bg-light text-primary rounded px-3 py-2 me-2 fw-bold" id="timer">
                        <i class="far fa-clock me-2"></i><span id="timer-display">100:00</span>
                    </div>
                    <!-- Submit button -->
                    <button class="btn btn-success me-2" id="submit-btn" onclick="submitTest()">
                        <i class="fas fa-check me-1"></i> Submit
                    </button>
                    <!-- Language toggle -->
                    <button class="btn btn-light me-2" id="lang-btn" onclick="toggleLanguage()">
                        <i class="fas fa-language"></i>
                    </button>
                    <!-- Menu toggle for mobile -->
                    <button class="btn btn-light d-lg-none" id="menu-toggle" onclick="toggleMenu()">
                        <i class="fas fa-th"></i>
                    </button>
                </div>
            </div>
        </nav>
        <div class="row m-0">
            <!-- Main content area -->
            <div class="col p-0 question-content" id="question-content">
                <div class="container py-4">
                    <!-- Question navigation -->
                    <div class="d-flex justify-content-between mb-4">
                        <button class="btn btn-outline-primary" onclick="prevQuestion()">
                            <i class="fas fa-chevron-left me-1"></i> Previous
                        </button>
                        <div class="fw-bold" id="question-counter">Question 1 of 100</div>
                        <button class="btn btn-outline-primary" onclick="nextQuestion()">
                            Next <i class="fas fa-chevron-right ms-1"></i>
                        </button>
                    </div>
                    <!-- Questions will be displayed here -->
                    <div id="questions-container"></div>
                </div>
            </div>
            <!-- Question navigation sidebar -->
            <div class="bg-light question-nav" id="question-nav">
                <div class="p-3">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h5 class="m-0">Questions</h5>
                        <div>
                            <span class="badge bg-secondary me-1" id="attempted-count">0</span>
                            <span class="badge bg-secondary" id="total-count">100</span>
                            <button class="btn btn-sm btn-outline-secondary ms-2 d-lg-none" onclick="toggleMenu()">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>
                    <div class="d-flex flex-wrap" id="question-boxes"></div>
                </div>
            </div>
        </div>
        <!-- Results modal -->
        <div class="modal fade" id="results-modal" tabindex="-1" aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content">
                    <div class="modal-header bg-primary text-white">
                        <h5 class="modal-title">Test Results</h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row text-center">
                            <div class="col-6 mb-3">
                                <div class="p-3 bg-primary bg-opacity-10 rounded">
                                    <h3 id="score-value">0</h3>
                                    <div class="small text-muted">Score</div>
                                </div>
                            </div>
                            <div class="col-6 mb-3">
                                <div class="p-3 bg-success bg-opacity-10 rounded">
                                    <h3 id="correct-value">0</h3>
                                    <div class="small text-muted">Correct</div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="p-3 bg-danger bg-opacity-10 rounded">
                                    <h3 id="incorrect-value">0</h3>
                                    <div class="small text-muted">Incorrect</div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="p-3 bg-secondary bg-opacity-10 rounded">
                                    <h3 id="unattempted-value">0</h3>
                                    <div class="small text-muted">Unattempted</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-primary" onclick="reviewTest()">Review Test</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 100 * 60;
        let resultsModal;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            resultsModal = new bootstrap.Modal(document.getElementById('results-modal'));
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 768) {
                document.getElementById('question-content').style.marginRight = '320px';
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["6"] = {
                name: "Reasoning",
                start: 0,
                end: 23
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="6">Reasoning</option>`;
            sections["18"] = {
                name: "General Awareness",
                start: 24,
                end: 48
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="18">General Awareness</option>`;
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 49,
                end: 73
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["7"] = {
                name: "English",
                start: 74,
                end: 98
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="7">English</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 99,
                end: 99
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "6",
                    question_en: "<p>1. Select the combination of letters that when sequentially placed in the blanks of the given series will complete the series. <br>P _ A A _ _ Z A _V _ _ A _ _</p>",
                    question_hi: "<p>1. अक्षरों के उस संयोजन का चयन कीजिए, जिसके अक्षरों को दी गई शृंखला के रिक्त स्थानों में क्रमिक रूप से रखने पर शृंखला पूर्ण हो जाएगी।<br>P _ A A _ _ Z A _V _ _ A _ _</p>",
                    options_en: [
                        "<p>VPZZPVAP</p>",
                        "<p>ZPVVAPVZ</p>",
                        "<p>ZVPAPZAV</p>",
                        "<p>VZAAPVPZ</p>"
                    ],
                    options_hi: [
                        "<p>VPZZPVAP</p>",
                        "<p>ZPVVAPVZ</p>",
                        "<p>ZVPAPZAV</p>",
                        "<p>VZAAPVPZ</p>"
                    ],
                    solution_en: "<p>1.(c) P<span style=\"text-decoration: underline;\"><strong>Z</strong></span> A A<span style=\"text-decoration: underline;\"><strong>V</strong></span> / <span style=\"text-decoration: underline;\"><strong>P</strong></span> Z A <span style=\"text-decoration: underline;\"><strong>A</strong></span>V / <span style=\"text-decoration: underline;\"><strong>PZ</strong></span> A <span style=\"text-decoration: underline;\"><strong>A V</strong></span></p>",
                    solution_hi: "<p>1.(c) P<span style=\"text-decoration: underline;\"><strong>Z</strong></span> A A<span style=\"text-decoration: underline;\"><strong>V</strong></span> / <span style=\"text-decoration: underline;\"><strong>P</strong></span> Z A <span style=\"text-decoration: underline;\"><strong>A</strong></span>V / <span style=\"text-decoration: underline;\"><strong>PZ</strong></span> A <span style=\"text-decoration: underline;\"><strong>A V</strong></span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "6",
                    question_en: "<p>2. Identify the figure given in the options which when put in place of the question mark (?) will logically complete the series.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744687582968.png\" alt=\"rId5\" width=\"361\" height=\"83\"></p>",
                    question_hi: "<p>2. विकल्पों में से उस आकृति की पहचान करें, जिसे प्रश्न-चिह्न (?) के स्थान पर रखे जाने पर शृंखला तार्किक रूप से पूर्ण हो जाएगी।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744687582968.png\" alt=\"rId5\" width=\"361\" height=\"83\"></p>",
                    options_en: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744687586683.png\" alt=\"rId6\" width=\"85\" height=\"82\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744687586804.png\" alt=\"rId7\" width=\"87\" height=\"85\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744687586933.png\" alt=\"rId8\" width=\"89\" height=\"84\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744687587031.png\" alt=\"rId9\" width=\"86\" height=\"89\"></p>"
                    ],
                    options_hi: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744687586683.png\" alt=\"rId6\" width=\"88\" height=\"85\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744687586804.png\" alt=\"rId7\" width=\"87\" height=\"85\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744687586933.png\" alt=\"rId8\" width=\"88\" height=\"83\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744687587031.png\" alt=\"rId9\" width=\"88\" height=\"91\"></p>"
                    ],
                    solution_en: "<p>2.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744687586683.png\" alt=\"rId6\" width=\"88\" height=\"85\"></p>",
                    solution_hi: "<p>2.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744687586683.png\" alt=\"rId6\" width=\"88\" height=\"85\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "6",
                    question_en: "<p>3. 325 is related to 30 following a certain logic . Following the same logic , 424 is related to 32. Which of the following numbers is related to 16 using the same logic ?</p>",
                    question_hi: "<p>3. एक निश्चित तर्क का अनुसरण करते हुए 325, 30 से संबंधित है। उसी तर्क का अनुसरण करते हुए, 424, 32 से संबंधित है। उसी तर्क का उपयोग करते हुए निम्नलिखित में से कौन सी संख्या 16 से संबंधित है ?</p>",
                    options_en: [
                        "<p>178</p>",
                        "<p>182</p>",
                        "<p>180</p>",
                        "<p>184</p>"
                    ],
                    options_hi: [
                        "<p>178</p>",
                        "<p>182</p>",
                        "<p>180</p>",
                        "<p>184</p>"
                    ],
                    solution_en: "<p>3.(b)<strong> Logic :- </strong>(Multiplication of digit of 1st number) = 2nd number<br>(325, 30) :- (3 &times; 2 &times; 5) = 30<br>(424, 32) :- (4 &times; 2 &times; 4) = 32<br>Similarly,<br>(?, 16) :- (1 &times; 8 &times; 2) = 16</p>",
                    solution_hi: "<p>3.(b) <strong>तर्क:- </strong>(पहली संख्या के अंक का गुणन) = दूसरी संख्या<br>(325, 30) :- (3 &times; 2 &times; 5) = 30<br>(424, 32) :- (4 &times; 2 &times; 4) = 32<br>इसी प्रकार,<br>(?, 16) :- (1 &times; 8 &times; 2) = 16</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "6",
                    question_en: "<p>4. Select the correct mirror image of the given figure when the mirror is placed at MN.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744687587210.png\" alt=\"rId10\" width=\"113\" height=\"123\"></p>",
                    question_hi: "<p>4. जब दर्पण को MN पर रखा जाता हो तो दी गई आकृति के सही दर्पण प्रतिबिंब का चयन कीजिए।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744687587210.png\" alt=\"rId10\" width=\"113\" height=\"123\"></p>",
                    options_en: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744687587310.png\" alt=\"rId11\" width=\"122\" height=\"19\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744687587459.png\" alt=\"rId12\" width=\"126\" height=\"19\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744687587872.png\" alt=\"rId13\" width=\"120\" height=\"18\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744687589042.png\" alt=\"rId14\" width=\"120\" height=\"18\"></p>"
                    ],
                    options_hi: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744687587310.png\" alt=\"rId11\" width=\"122\" height=\"19\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744687587459.png\" alt=\"rId12\" width=\"126\" height=\"19\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744687587872.png\" alt=\"rId13\" width=\"126\" height=\"19\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744687589042.png\" alt=\"rId14\" width=\"126\" height=\"19\"></p>"
                    ],
                    solution_en: "<p>4.(c) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744687587872.png\" alt=\"rId13\" width=\"126\" height=\"19\"></p>",
                    solution_hi: "<p>4.(c) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744687587872.png\" alt=\"rId13\" width=\"126\" height=\"19\"></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "6",
                    question_en: "<p>5. Three statements are given, followed by three conclusions numbered I, II and III. Assuming the statements to be true, even if they seem to be at variance with commonly known facts, decide which of the conclusions logically follow(s) from the statements.<br><strong>Statements :</strong><br>No lotus is a daisy.<br>All lotuses are tulips. <br>All tulips are sunflowers.<br><strong>Conclusions :</strong><br>I. All daisies are tulips. <br>II. Some sunflowers are daisies. <br>III. No sunflower is a daisy.</p>",
                    question_hi: "<p>5. तीन कथन दिए गए हैं, जिसके बाद तीन निष्कर्ष ।, ॥ और III दिए गए हैं। कथनों को सत्य मानते हुए, भले ही वे सामान्य रूप से ज्ञात तथ्यों से भिन्न प्रतीत होते हों, निर्धारित करें कि कौन सा/से निष्कर्ष कथनों का तार्किक रूप से अनुसरण करता /करते है/हैं।<br><strong>कथन :</strong><br>कोई भी कमल, डेज़ी नहीं है।<br>सभी कमल, ट्यूलिप हैं।<br>सभी ट्यूलिप, सूरजमुखी हैं।<br><strong>निष्कर्ष :</strong><br>I. सभी डेज़ी, ट्यूलिप हैं।<br>II. कुछ सूरजमुखी, डेज़ी हैं। <br>III. कोई सूरजमुखी, डेज़ी नहीं है।</p>",
                    options_en: [
                        "<p>Only conclusion II follows</p>",
                        "<p>Either conclusion II or III follows</p>",
                        "<p>Only conclusion I follows</p>",
                        "<p>Only conclusion III follows</p>"
                    ],
                    options_hi: [
                        "<p>केवल निष्कर्ष ॥ अनुसरण करता है</p>",
                        "<p>या तो निष्कर्ष ॥ अनुसरण करता है या III अनुसरण करता है</p>",
                        "<p>केवल निष्कर्ष । अनुसरण करता है</p>",
                        "<p>केवल निष्कर्ष III अनुसरण करता है</p>"
                    ],
                    solution_en: "<p>5.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744687589709.png\" alt=\"rId15\" width=\"309\" height=\"91\"><br>Either conclusion II or III follow.</p>",
                    solution_hi: "<p>5.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744687592640.png\" alt=\"rId16\" width=\"313\" height=\"93\"><br>या तो निष्कर्ष II या III अनुसरण करता है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "6",
                    question_en: "<p>6. In a code language, \'HOPE\' is coded as &lsquo;FRRL&rsquo; and \'TAIL\' is coded as &lsquo;MKDX&rsquo;. How will \'STORY\' be coded in the same language ?</p>",
                    question_hi: "<p>6. एक कूट भाषा में, \'HOPE\' को &lsquo;FRRL&rsquo; और \'TAIL\' को &lsquo;MKDX&rsquo; के रूप में कूटबद्ध किया जाता है। उसी भाषा में \'STORY\' को कैसे कूटबद्ध किया जाएगा ?</p>",
                    options_en: [
                        "<p>ZTRYY</p>",
                        "<p>ZTRXX</p>",
                        "<p>ZRTYY</p>",
                        "<p>ZRTXX</p>"
                    ],
                    options_hi: [
                        "<p>ZTRYY</p>",
                        "<p>ZTRXX</p>",
                        "<p>ZRTYY</p>",
                        "<p>ZRTXX</p>"
                    ],
                    solution_en: "<p>6.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744687594349.png\" alt=\"rId17\" width=\"89\" height=\"97\">&nbsp; ,&nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744687595125.png\" alt=\"rId18\" width=\"89\" height=\"96\"></p>\n<p>Similarly,</p>\n<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744687595679.png\" alt=\"rId19\" width=\"133\" height=\"97\"></p>",
                    solution_hi: "<p>6.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744687594349.png\" alt=\"rId17\" width=\"89\" height=\"97\">&nbsp; ,&nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744687595125.png\" alt=\"rId18\" width=\"89\" height=\"96\"></p>\n<p>इसी प्रकार,</p>\n<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744687595679.png\" alt=\"rId19\" width=\"133\" height=\"97\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "6",
                    question_en: "<p>7. Four letter-clusters have been given, out of which three are alike in some manner and one is different. Select the one that is different. The odd one out is not based on the number of consonants/vowels or their position in the letter cluster</p>",
                    question_hi: "<p>7. चार अक्षर समूह दिए गए हैं, जिनमें से तीन किसी न किसी रूप में एक समान हैं। उस असमान विकल्प को चुनिए। अक्षर समूह में, असमान विकल्प व्यंजनों/स्वरों की संख्या या उनकी स्थिति पर आधारित नहीं है।</p>",
                    options_en: [
                        "<p>CBE</p>",
                        "<p>NMP</p>",
                        "<p>UTW</p>",
                        "<p>LKM</p>"
                    ],
                    options_hi: [
                        "<p>CBE</p>",
                        "<p>NMP</p>",
                        "<p>UTW</p>",
                        "<p>LKM</p>"
                    ],
                    solution_en: "<p>7.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744687596558.png\" alt=\"rId20\" width=\"125\" height=\"48\"> ,&nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744687597573.png\" alt=\"rId21\" width=\"126\" height=\"47\"> ,&nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744687597855.png\" alt=\"rId22\" width=\"124\" height=\"48\"><br>but</p>\n<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744687600685.png\" alt=\"rId23\" width=\"121\" height=\"49\"></p>",
                    solution_hi: "<p>7.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744687596558.png\" alt=\"rId20\" width=\"125\" height=\"48\"> ,&nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744687597573.png\" alt=\"rId21\" width=\"126\" height=\"47\"> ,&nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744687597855.png\" alt=\"rId22\" width=\"124\" height=\"48\"></p>\n<p>लेकिन</p>\n<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744687600685.png\" alt=\"rId23\" width=\"121\" height=\"49\"></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "6",
                    question_en: "<p>8. Which of the following terms will replace the question mark (?) in the given series ?<br>AWSO, KGCY, UQMI, EAWS, ?</p>",
                    question_hi: "<p>8. निम्नलिखित में से कौन-सा पद दी गई श्रृंखला में प्रश्न चिह्न (?) को प्रतिस्थापित करेगा ?<br>AWSO, KGCY, UQMI, EAWS, ?</p>",
                    options_en: [
                        "<p>OKGC</p>",
                        "<p>WAEI</p>",
                        "<p>ZWTQ</p>",
                        "<p>OGKH</p>"
                    ],
                    options_hi: [
                        "<p>OKGC</p>",
                        "<p>WAEI</p>",
                        "<p>ZWTQ</p>",
                        "<p>OGKH</p>"
                    ],
                    solution_en: "<p>8.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744687602323.png\" alt=\"rId24\" width=\"314\" height=\"116\"></p>",
                    solution_hi: "<p>8.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744687602323.png\" alt=\"rId24\" width=\"314\" height=\"116\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "6",
                    question_en: "<p>9. Which two signs should be interchanged to make the given equation correct ?<br>11 &times; 7 &minus; 48 + 6 &divide; 37 = 106</p>",
                    question_hi: "<p>9. दिए गए समीकरण को सही करने के लिए किन दो चिह्नों को आपस में बदला जाना चाहिए ?<br>11 &times; 7 &minus; 48 + 6 &divide; 37 = 106</p>",
                    options_en: [
                        "<p>&minus; and &times;</p>",
                        "<p>&divide; and +</p>",
                        "<p>+ and &times;</p>",
                        "<p>&minus; and +</p>"
                    ],
                    options_hi: [
                        "<p>&minus; और &times;</p>",
                        "<p>&divide; और +</p>",
                        "<p>+ और &times;</p>",
                        "<p>&minus; और +</p>"
                    ],
                    solution_en: "<p>9.(b) <strong>Given:</strong> 11 &times; 7 &minus; 48 + 6 &divide; 37 = 106<br>By checking all the options one by one, option (b) is satisfied, after interchanging the sign we get.<br>L.H.S: 11 &times; 7 &minus; 48 &divide; 6 + 37<br>11 &times; 7 &minus; 8 + 37<br>77 - 8 + 37<br>114 - 8 = 106<br>L.H.S = R.H.S</p>",
                    solution_hi: "<p>9.(b) <strong>दिया गया है:</strong> 11 &times; 7 &minus; 48 + 6 &divide; 37 = 106<br>एक एक करके सभी विकल्पों की जाँच करने पर विकल्प (b) संतुष्ट होता है, चिन्ह बदलने के बाद प्राप्त होता है <br>L.H.S: 11 &times; 7 &minus; 48 &divide; 6 + 37<br>11 &times; 7 &minus; 8 + 37<br>77 - 8 + 37<br>114 - 8 = 106<br>L.H.S = R.H.S</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "6",
                    question_en: "<p>10. Each of the letters in the word &lsquo;DARKEN&rsquo; is arranged in the English alphabetical order. How many letters are there in the English alphabetical series between the letter which is second from the left end and the one which is third from the right end in the new letter-cluster thus formed ?</p>",
                    question_hi: "<p>10. शब्द \'DARKEN\' के प्रत्येक अक्षर को अँग्रेजी वर्णमाला क्रम में व्यवस्थित किया जाता है। इस प्रकार बने नए अक्षर-समूह में बाएँ छोर से दूसरे अक्षर और दाएँ छोर से तीसरे अक्षर के बीच अँग्रेजी वर्णमाला श्रृंखला में कितने अक्षर हैं ?</p>",
                    options_en: [
                        "<p>Six</p>",
                        "<p>Eight</p>",
                        "<p>Five</p>",
                        "<p>Seven</p>"
                    ],
                    options_hi: [
                        "<p>छ:</p>",
                        "<p>आठ</p>",
                        "<p>पाँच</p>",
                        "<p>सात</p>"
                    ],
                    solution_en: "<p>10.(a)<br><strong>Given :-</strong> D A R K E N&nbsp;<br>After arranging alphabetically :- A D E K N R<br>Second alphabet from left end = D<br>Second alphabet from right end = K<br>No. of alphabet between D and K in alphabetical order = 6</p>",
                    solution_hi: "<p>10.(a)<br><strong>दिया गया है:-</strong> D A R K E N <br>वर्णानुक्रम से व्यवस्थित करने के बाद :- A D E K N R<br>बाएं छोर से दूसरा अक्षर = D<br>दाएं छोर से दूसरा अक्षर = K<br>वर्णमाला क्रम में D और K के बीच अक्षरों की संख्या = 6</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "6",
                    question_en: "<p>11. Identify the option figure that can replace the question mark (?) in the following series to logically complete the series ?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744687602653.png\" alt=\"rId25\" width=\"328\" height=\"78\"></p>",
                    question_hi: "<p>11. उस विकल्प आकृति का चयन कीजिए जो शृंखला को तर्कसंगत रूप से पूर्ण करने के लिए निम्नलिखित शृंखला में प्रश्न-चिह्न (?) को प्रतिस्थापित कर सकती है ?&nbsp;<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744687602653.png\" alt=\"rId25\" width=\"328\" height=\"78\"></p>",
                    options_en: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744687603118.png\" alt=\"rId26\" width=\"86\" height=\"93\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744687603302.png\" alt=\"rId27\" width=\"91\" height=\"85\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744687603403.png\" alt=\"rId28\" width=\"87\" height=\"87\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744687603551.png\" alt=\"rId29\" width=\"87\" height=\"89\"></p>"
                    ],
                    options_hi: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744687603118.png\" alt=\"rId26\" width=\"86\" height=\"94\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744687603302.png\" alt=\"rId27\" width=\"86\" height=\"80\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744687603403.png\" alt=\"rId28\" width=\"87\" height=\"87\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744687603551.png\" alt=\"rId29\" width=\"88\" height=\"90\"></p>"
                    ],
                    solution_en: "<p>11.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744687603551.png\" alt=\"rId29\" width=\"89\" height=\"91\"></p>",
                    solution_hi: "<p>11.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744687603551.png\" alt=\"rId29\" width=\"89\" height=\"91\"></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "6",
                    question_en: "<p>12. Select the correct mirror image of the given figure when the mirror is placed at MN as shown below.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744687603856.png\" alt=\"rId30\" width=\"111\" height=\"128\"></p>",
                    question_hi: "<p>12. जब दर्पण को नीचे दर्शाए गए अनुसार MN रेखा पर रखा जाता है तो दी गई आकृति के सही दर्पण प्रतिबिंब का चयन कीजिए।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744687603856.png\" alt=\"rId30\" width=\"111\" height=\"128\"></p>",
                    options_en: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744687604112.png\" alt=\"rId31\" width=\"87\" height=\"80\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744687604314.png\" alt=\"rId32\" width=\"84\" height=\"85\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744687604498.png\" alt=\"rId33\" width=\"87\" height=\"83\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744687604764.png\" alt=\"rId34\" width=\"87\" height=\"91\"></p>"
                    ],
                    options_hi: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744687604112.png\" alt=\"rId31\" width=\"89\" height=\"82\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744687604314.png\" alt=\"rId32\" width=\"86\" height=\"87\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744687604498.png\" alt=\"rId33\" width=\"90\" height=\"86\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744687604764.png\" alt=\"rId34\" width=\"88\" height=\"92\"></p>"
                    ],
                    solution_en: "<p>12.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744687604764.png\" alt=\"rId34\" width=\"89\" height=\"93\"></p>",
                    solution_hi: "<p>12.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744687604764.png\" alt=\"rId34\" width=\"89\" height=\"93\"></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "6",
                    question_en: "<p>13. Select the option that is related to the fifth letter-cluster in the same way as the second letter-cluster is related to the first letter-cluster and the fourth letter-cluster is related to the third letter-cluster.<br>BOIL : DRMQ :: SCAR : UFEW :: MAIL : ?</p>",
                    question_hi: "<p>13. उस विकल्प का चयन कीजिए, जो पाँचवें अक्षर-समूह से उसी प्रकार संबंधित है, जिस प्रकार दूसरा अक्षर-समूह पहले अक्षर-समूह से संबंधित है और चौथा अक्षर-समूह तीसरे अक्षर-समूह से संबंधित है।<br>BOIL : DRMQ :: SCAR : UFEW :: MAIL : ?</p>",
                    options_en: [
                        "<p>ODNR</p>",
                        "<p>ODMQ</p>",
                        "<p>ODLR</p>",
                        "<p>ODMS</p>"
                    ],
                    options_hi: [
                        "<p>ODNR</p>",
                        "<p>ODMQ</p>",
                        "<p>ODLR</p>",
                        "<p>ODMS</p>"
                    ],
                    solution_en: "<p>13.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744687604990.png\" alt=\"rId35\" width=\"151\" height=\"89\">&nbsp; ,&nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744687607405.png\" alt=\"rId36\" width=\"153\" height=\"89\"></p>\n<p>Similarly,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744687607597.png\" alt=\"rId37\" width=\"145\" height=\"86\"></p>",
                    solution_hi: "<p>13.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744687604990.png\" alt=\"rId35\" width=\"151\" height=\"89\">&nbsp; ,&nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744687607405.png\" alt=\"rId36\" width=\"153\" height=\"89\"></p>\n<p>इसी तरह,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744687607597.png\" alt=\"rId37\" width=\"145\" height=\"86\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "6",
                    question_en: "<p>14. In a certain code language, &lsquo;TURF&rsquo; is coded as &lsquo;5691&rsquo; and &lsquo;RAFT&rsquo; is coded as &lsquo;6935&rsquo;. What is the code for &lsquo;A&rsquo; in the given code language ?</p>",
                    question_hi: "<p>14. किसी निश्चित कूट भाषा में, &lsquo;TURF&rsquo; को &lsquo;5691&rsquo; के रूप में कूटबद्ध किया जाता है और &lsquo;RAFT&rsquo; को &lsquo;6935&rsquo; के रूप में कूटबद्ध किया जाता है। दी गई कूट भाषा में \'A\' के लिए कूट क्या है ?</p>",
                    options_en: [
                        "<p>3</p>",
                        "<p>9</p>",
                        "<p>6</p>",
                        "<p>5</p>"
                    ],
                    options_hi: [
                        "<p>3</p>",
                        "<p>9</p>",
                        "<p>6</p>",
                        "<p>5</p>"
                    ],
                    solution_en: "<p>14.(a) TURF :- 5691&hellip;&hellip;(i)<br>RAFT :- 6935&hellip;&hellip;(ii)<br>From (i) and (ii) &lsquo;RFT&rsquo; and &lsquo;965&rsquo; are common. The code of &lsquo;A&rsquo; = &lsquo;3&rsquo;</p>",
                    solution_hi: "<p>14.(a) TURF :- 5691&hellip;&hellip;(i)<br>RAFT :- 6935&hellip;&hellip;(ii)<br>(i) और (ii) से &lsquo;RFT&rsquo; और &lsquo;965&rsquo; उभयनिष्ठ हैं। &lsquo;A&rsquo; का कोड = &lsquo;3&rsquo;</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "6",
                    question_en: "<p>15. Four figure pairs have been given, out of which three are alike in a certain way and thus form a group. Which is the one that does not belong to that group ?</p>",
                    question_hi: "<p>15. चार आकृति युग्म दिए गए हैं, जिनमें से तीन किसी निश्चित प्रकार से समान हैं और इस प्रकार एक समूह बनाते हैं। वह आकृति युग्म कौन-सा है, जो उस समूह से संबंधित नहीं है ?</p>",
                    options_en: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744687607747.png\" alt=\"rId38\" width=\"159\" height=\"71\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744687609078.png\" alt=\"rId39\" width=\"159\" height=\"70\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744687609390.png\" alt=\"rId40\" width=\"163\" height=\"72\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744687609514.png\" alt=\"rId41\" width=\"159\" height=\"71\"></p>"
                    ],
                    options_hi: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744687609618.png\" alt=\"rId42\" width=\"156\" height=\"70\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744687609744.png\" alt=\"rId43\" width=\"159\" height=\"71\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744687609836.png\" alt=\"rId44\" width=\"161\" height=\"71\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744687609975.png\" alt=\"rId45\" width=\"161\" height=\"72\"></p>"
                    ],
                    solution_en: "<p>15.(a) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744687610072.png\" alt=\"rId46\" width=\"163\" height=\"73\"></p>",
                    solution_hi: "<p>15.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744687610072.png\" alt=\"rId46\" width=\"163\" height=\"73\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "6",
                    question_en: "<p>16. Read the given statements and conclusions carefully. Assuming that the information given in the statements is true, even if it appears to be at variance with commonly known facts, decide which of the given conclusion(s) logically follow(s) from the statements.<br><strong>Statements:</strong><br>All pastries are chocolates.<br>Some chocolates are cookies.<br>All cookies are cakes.<br><strong>Conclusions:</strong><br>(I) No pastry is a cake.<br>(II) Some cakes are chocolates.</p>",
                    question_hi: "<p>16. दिए गए कथनों और निष्कर्षों का ध्यानपूर्वक अध्ययन कीजिए। कथनों में दी गई जानकारी को सत्य मानते हुए, भले ही यह सामान्य रूप से ज्ञात तथ्यों से भिन्न प्रतीत होती हो, तय कीजिए कि दिए गए निष्कर्ष(षों) षों में से कौन-सा/से निष्कर्ष, कथनों का तर्कसंगत रूप से अनुसरण करता है/करते हैं।<br><strong>कथन:</strong><br>सभी पेस्ट्री , चॉकलेट हैं।<br>कुछ चॉकलेट, कुकीज़ हैं।<br>सभी कुकीज़, केक हैं।<br><strong>निष्कर्ष:</strong><br>(I) कोई भी पेस्ट्री , केक नहीं है।<br>(II) कुछ केक, चॉकलेट हैं।</p>",
                    options_en: [
                        "<p>Only conclusion I follows.</p>",
                        "<p>Both conclusions I and II follow.</p>",
                        "<p>Neither conclusion I nor II follows.</p>",
                        "<p>Only conclusion II follows.</p>"
                    ],
                    options_hi: [
                        "<p>केवल निष्कर्ष (I) अनुसरण करता है।</p>",
                        "<p>निष्कर्ष (I) और (II) दोनों अनुसरण करते हैं।</p>",
                        "<p>न तो निष्कर्ष (I) और न ही निष्कर्ष (II) अनुसरण करता है।</p>",
                        "<p>केवल निष्कर्ष (II) अनुसरण करता है।</p>"
                    ],
                    solution_en: "<p>16.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744687610354.png\" alt=\"rId48\" width=\"273\" height=\"140\"><br>Hence, conclusion II follows.</p>",
                    solution_hi: "<p>16.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744687610512.png\" alt=\"rId49\" width=\"202\" height=\"84\"><br>अतः, निष्कर्ष II अनुसरण करता है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "6",
                    question_en: "<p>17. Select the number from among the given options that can replace the question mark (?) in the following series.<br>1225, 1184, 1147, 1116, 1087, ?</p>",
                    question_hi: "<p>17. दिए गए विकल्पों में से उस संख्या का चयन कीजिए जो निम्नलिखित श्रृंखला में प्रश्न चिह्न (?) के स्&zwj;थान पर आ सकती है।<br>1225, 1184, 1147, 1116, 1087, ?</p>",
                    options_en: [
                        "<p>1054</p>",
                        "<p>1078</p>",
                        "<p>1072</p>",
                        "<p>1064</p>"
                    ],
                    options_hi: [
                        "<p>1054</p>",
                        "<p>1078</p>",
                        "<p>1072</p>",
                        "<p>1064</p>"
                    ],
                    solution_en: "<p>17.(d)<strong> logic:- </strong>subtracting consecutive prime numbers </p>\n<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744687610622.png\" alt=\"rId50\" width=\"250\" height=\"45\"></p>",
                    solution_hi: "<p>17.(d) <strong>तर्क:- </strong>लगातार अभाज्य संख्याओं को घटाना</p>\n<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744687610622.png\" alt=\"rId50\" width=\"250\" height=\"45\"></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "6",
                    question_en: "<p>18. Select the pair which is related to each other in the same way as the following-<br>9 : 69<br>11 : 109<br>(<strong>NOTE:</strong> Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g. 13- Operations on 13 such as adding/subtracting/multiplying etc. to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed.)</p>",
                    question_hi: "<p>18. उस युग्म का चयन कीजिए जो एक दूसरे से उसी प्रकार संबंधित है जिस प्रकार नीचे दिए गए युग्म संबंधित हैं I<br>9 : 69<br>11 : 109<br>(<strong>नोट:</strong> संख्याओं को उनके घटक अंकों में तोड़े बिना संक्रियाएँ केवल पूर्ण संख्याओं पर की जानी चाहिए। उदाहरण के लिए 13 को लीजिए - 13 पर की जाने वाली संक्रियाएँ, जैसे 13 में जोड़ना / घटाना / गुणा करना इत्यादि केवल 13 पर की जा सकती हैं। 13 को 1 और 3 में तोड़ना और फिर 1 और 3 पर गणितीय संक्रियाएँ करने की अनुमति नहीं है।)</p>",
                    options_en: [
                        "<p>16 : 244</p>",
                        "<p>8 : 76</p>",
                        "<p>14 : 209</p>",
                        "<p>24 : 576</p>"
                    ],
                    options_hi: [
                        "<p>16 : 244</p>",
                        "<p>8 : 76</p>",
                        "<p>14 : 209</p>",
                        "<p>24 : 576</p>"
                    ],
                    solution_en: "<p>18.(a) <strong>Logic:- </strong>(<math display=\"inline\"><msup><mrow><mn>1</mn></mrow><mrow><mi>s</mi><mi>t</mi></mrow></msup></math>no.)<sup>2</sup> - 12 = 2<sup>nd</sup>no.<br>(9 : 69):- (9)<sup>2</sup> - 12 &rarr; 81 - 12 = 69<br>(11 : 109):- (11)<sup>2 </sup>- 12 &rarr; 121 - 12 = 109<strong id=\"docs-internal-guid-9327140a-7fff-58dd-54e3-2106607b3422\"> </strong><br>Similarly,<br>(16 : 244):- (16)<sup>2</sup> - 12 &rarr; 256 - 12 = 144</p>",
                    solution_hi: "<p>18.(a) <strong>तर्क :-</strong> (पहली संख्या)<sup>2</sup> - 12 = दूसरी संख्या<br>(9 : 69):- (9)<sup>2</sup> - 12 &rarr; 81 - 12 = 69<br>(11 : 109):- (11)<sup>2 </sup>- 12 &rarr; 121 - 12 = 109<br>इसी प्रकार,<br>(16 : 244):- (16)<sup>2</sup> - 12 &rarr; 256 - 12 = 144</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "6",
                    question_en: "<p>19. In a certain code language,<br>X + Y means &lsquo;X is the sister of Y&rsquo;,<br>X &minus; Y means &lsquo;X is the son of Y&rsquo;,<br>X &times; Y means &lsquo;X is the husband of Y&rsquo;,<br>X &divide; Y means &lsquo;X is the mother of Y&rsquo;.<br>Based on the above, how is T related to X if &lsquo;T + U &minus; V &times; W &divide; X&rsquo; ?</p>",
                    question_hi: "<p>19. एक निश्चित कूट भाषा में,<br>X + Y का अर्थ है \'X, Y की बहन है\',<br>X &minus; Y का अर्थ है \'X, Y का पुत्र है\',<br>X &times; Y का अर्थ है \'X, Y का पति है\',<br>X &divide; Y का अर्थ है \'X, Y की माता है\'।<br>उपर्युक्त के आधार पर, यदि \'T + U &minus; V &times; W &divide; X\' है, तो T, X से किस प्रकार संबंधित है ?</p>",
                    options_en: [
                        "<p>Father&rsquo;s mother</p>",
                        "<p>Sister</p>",
                        "<p>Father&rsquo;s sister</p>",
                        "<p>Mother&rsquo;s sister</p>"
                    ],
                    options_hi: [
                        "<p>पिता की माता</p>",
                        "<p>बहन</p>",
                        "<p>पिता की बहन</p>",
                        "<p>माता की बहन</p>"
                    ],
                    solution_en: "<p>19.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744687610980.png\" alt=\"rId52\" width=\"170\" height=\"96\"><br>Hence, &lsquo;T&rsquo; is the sister of &lsquo;X&rsquo;</p>",
                    solution_hi: "<p>19.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744687610980.png\" alt=\"rId52\" width=\"170\" height=\"96\"><br>अतः, \'T\', \'X\' की बहन है ।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "6",
                    question_en: "<p>20. In a certain language MET is coded as 32, and MOW is coded as 45. How will RUN be coded in the same language ?</p>",
                    question_hi: "<p>20. एक निश्चित भाषा में MET को 32 और MOW को 45 के रूप में कूटबद्ध किया जाता है। उस भाषा में RUN को किस प्रकार कूटबद्ध किया जाएगा ?</p>",
                    options_en: [
                        "<p>30</p>",
                        "<p>50</p>",
                        "<p>47</p>",
                        "<p>53</p>"
                    ],
                    options_hi: [
                        "<p>30</p>",
                        "<p>50</p>",
                        "<p>47</p>",
                        "<p>53</p>"
                    ],
                    solution_en: "<p>20.(c)<strong> Logic :-</strong> (Sum of the place value of letters) - 6<br>(MET) :- (13 + 5 + 20) - (6) &rArr; (38) - (6) = 32<br>(MOW) :- (13 + 15 + 23) - (6) &rArr; (51) - (6) = 45 <br>Similarly,<br>(RUN) :- (18 + 21 + 14) - (6) &rArr; (53) - (6) = 47</p>",
                    solution_hi: "<p>20.(c) <strong>तर्क :- </strong>(अक्षरों के स्थानीय मान का योग) - 6<br>(MET) :- (13 + 5 + 20) - (6) &rArr; (38) - (6) = 32<br>(MOW) :- (13 + 15 + 23) - (6) &rArr; (51) - (6) = 45 <br>इसी प्रकार,<br>(RUN) :- (18 + 21 + 14) - (6) &rArr; (53) - (6) = 47</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "6",
                    question_en: "<p>21. Select the option that is related to the third number in the same way as the second number is related to the first number and the sixth number is related to the fifth number.<br>125 : 625 :: 343 : ? :: 221 : 721<br>(<strong>NOTE: </strong>Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g. 13- Operations on 13 such as adding/subtracting/multiplying etc. to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed.)</p>",
                    question_hi: "<p>21. उस विकल्प का चयन कीजिए जो तीसरी संख्या से उसी प्रकार संबंधित है जिस प्रकार दूसरी संख्या पहली संख्या से संबंधित है और छठी संख्या पांचवीं संख्या से संबंधित है।<br>125 : 625 :: 343 : ? :: 221 : 721<br>(<strong>नोटः</strong> संख्याओं को उनके घटक अंकों में तोड़े बिना संक्रियाएँ केवल पूर्ण संख्याओं पर की जानी चाहिए। उदाहरण के लिए 13 को लीजिए 13 पर की जाने वाली संक्रियाएँ, जैसे 13 में जोड़ना / घटाना / गुणा करना इत्यादि केवल 13 पर की जा सकती हैं। 13 को 1 और 3 में तोड़ना और फिर 1 और 3 पर गणितीय संक्रियाएँ करने की अनुमति नहीं है।)</p>",
                    options_en: [
                        "<p>1296</p>",
                        "<p>512</p>",
                        "<p>1126</p>",
                        "<p>843</p>"
                    ],
                    options_hi: [
                        "<p>1296</p>",
                        "<p>512</p>",
                        "<p>1126</p>",
                        "<p>843</p>"
                    ],
                    solution_en: "<p>21.(d)<br><strong>Logic:-</strong> <math display=\"inline\"><msup><mrow><mn>1</mn></mrow><mrow><mi>s</mi><mi>t</mi></mrow></msup></math>no. + 500 = 2<sup>nd</sup>no.<br>(125 : 625):- 125 + 500 = 625<br>(221 : 721):- 221 + 500 = 721<br>Similarly<br>(343 : ?):- 343 + 500 = 843</p>",
                    solution_hi: "<p>21.(d)<br><strong>तर्क :- </strong>पहली संख्या + 500 = दूसरी संख्या <br>(125 : 625):- 125 + 500 = 625<br>(221 : 721):- 221 + 500 = 721<br>इसी प्रकार <br>(343 : ?) :- 343 + 500 = 843</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "6",
                    question_en: "<p>22. Which of the following numbers will replace the question mark (?) in the given series ?<br>3, 17, 83, 329, ?, 1961</p>",
                    question_hi: "<p>22. निम्नलिखित में से कौन सी संख्&zwj;या दी गई श्रृंखला में प्रश्&zwj;न चिह्न (?) का स्&zwj;थान लेगी ?<br>3, 17, 83, 329, ?, 1961</p>",
                    options_en: [
                        "<p>983</p>",
                        "<p>883</p>",
                        "<p>880</p>",
                        "<p>980</p>"
                    ],
                    options_hi: [
                        "<p>983</p>",
                        "<p>883</p>",
                        "<p>880</p>",
                        "<p>980</p>"
                    ],
                    solution_en: "<p>22.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744687611091.png\" alt=\"rId53\" width=\"258\" height=\"54\"></p>",
                    solution_hi: "<p>22.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744687611091.png\" alt=\"rId53\" width=\"258\" height=\"54\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "23",
                    section: "6",
                    question_en: "<p>23. Ajay starts from Point A and drives 10 km towards North. He then takes a right turn, drives 3 km, turns right and drives 15 km. He then takes a right turn and drives 8 km. He takes a final right turn, drives 5 km and stops at Point P. How far (shortest distance) and towards which direction should he now drive to reach Point A again ? (All turns are 90&deg; turns only.)</p>",
                    question_hi: "<p>23. अजय बिंदु A से गाड़ी चलाना शुरू करता है और उत्तर की ओर 10 km गाड़ी चलाता है। फिर वह दाएँ मुड़ता है, 3 km गाड़ी चलाता है, दाएँ मुड़ता है और 15 km गाड़ी चलाता है। फिर वह दाएँ मुड़ता है और 8 km गाड़ी चलाता है। वह अंतिम बार दाएँ मुड़ता है, 5 km गाड़ी चलाता है और बिंदु P पर रुकता है। बिंदु A पर फिर से पहुँचने के लिए उसे कितनी दूर (न्यूनतम दूरी) और किस दिशा में गाड़ी चलानी चाहिए ? (सभी मोड़ केवल 90&deg; वाले मोड़ हैं।)</p>",
                    options_en: [
                        "<p>3 km towards East</p>",
                        "<p>5 km towards West</p>",
                        "<p>5 km towards East</p>",
                        "<p>3 km towards West</p>"
                    ],
                    options_hi: [
                        "<p>पूर्व की ओर 3 km</p>",
                        "<p>पश्चिम की ओर 5 km</p>",
                        "<p>पूर्व की ओर 5 km</p>",
                        "<p>पश्चिम की ओर 3 km</p>"
                    ],
                    solution_en: "<p>23.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744687611186.png\" alt=\"rId54\" width=\"234\" height=\"220\"><br>He should drive 5 km towards east to reach point A.</p>",
                    solution_hi: "<p>23.(c) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744687611186.png\" alt=\"rId54\" width=\"234\" height=\"220\"><br>बिंदु A तक पहुँचने के लिए उसे पूर्व की ओर 5 किमी गाड़ी चलानी चाहिए।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "24",
                    section: "6",
                    question_en: "<p>24. What will come in place of the question mark (?) in the following equation, if &lsquo;+&rsquo; and &lsquo;&ndash;&rsquo; are interchanged and &lsquo;&times;&rsquo; and &lsquo;&divide;&rsquo; are interchanged ?&nbsp;<br>107 + 2028 &times; 6 &ndash; 427 &divide; 7 = ?</p>",
                    question_hi: "<p>24. यदि &lsquo;+&rsquo; और &lsquo;&ndash;&rsquo; को आपस में बदल दिया जाए और &lsquo;&times;&rsquo; और &lsquo;&divide;&rsquo; को आपस में बदल दिया जाए, तो निम्नलिखित समीकरण में प्रश्न चिह्न (?) के स्थान पर क्या आएगा ?<br>107 + 2028 &times; 6 &ndash; 427 &divide; 7 = ?</p>",
                    options_en: [
                        "<p>2758</p>",
                        "<p>2268</p>",
                        "<p>2785</p>",
                        "<p>2578</p>"
                    ],
                    options_hi: [
                        "<p>2758</p>",
                        "<p>2268</p>",
                        "<p>2785</p>",
                        "<p>2578</p>"
                    ],
                    solution_en: "<p>24.(a) <strong>Given: </strong>107 + 2028 &times; 6 &ndash; 427 &divide; 7 = ?<br>As per the instructions after interchanging the symbol &lsquo;&times;&rsquo; and &lsquo;&divide;&rsquo; and &lsquo;+&rsquo; and &lsquo;&ndash;&rsquo; we get.<br>107 - 2028 &divide; 6 + 427 &times; 7 = ?<br>107 - 338 + 2989<br>3096 - 338 = 2758</p>",
                    solution_hi: "<p>24.(a) <strong>दिया गया है:</strong> 107 + 2028 &times; 6 &ndash; 427 &divide; 7 = ?<br>निर्देशों के अनुसार प्रतीक \'&times;\' और \'&divide;\' तथा \'+\' और \'-\' को आपस में बदलने पर हमें प्राप्त होता है।<br>107 - 2028 &divide; 6 + 427 &times; 7 = ?<br>107 - 338 + 2989<br>3096 - 338 = 2758</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "25",
                    section: "18",
                    question_en: "<p>25. All the six members of a family, 1, 2, 3, 4, 5 and 6, live together. 2 is the daughter of 3, but 3 is not the mother of 2. 1 and 3 are a married couple. 5 is the sister of 3. 4 is the daughter of 1. 6 is the brother of 2. Who is the mother of 2 ?</p>",
                    question_hi: "<p>25. एक परिवार के सभी छ: सदस्य 1, 2, 3, 4, 5 और 6 एक साथ रहते हैं। 2, 3 की पुत्री है, लेकिन 3, 2 की माता नहीं है। 1 और 3 विवाहित युगल हैं। 5, 3 की बहन है। 4, 1 की पुत्री है। 6, 2 का भाई है। 2 की माता कौन है ?</p>",
                    options_en: [
                        "<p>4</p>",
                        "<p>5</p>",
                        "<p>1</p>",
                        "<p>2</p>"
                    ],
                    options_hi: [
                        "<p>4</p>",
                        "<p>5</p>",
                        "<p>1</p>",
                        "<p>2</p>"
                    ],
                    solution_en: "<p>25.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744687611305.png\" alt=\"rId55\" width=\"202\" height=\"105\"><br><strong>We can see that 1 is the mother of 2.</strong></p>",
                    solution_hi: "<p>25.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744687611305.png\" alt=\"rId55\" width=\"202\" height=\"105\"><br><strong>हम देख सकते हैं कि 1, 2 की माँ है।</strong></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "26",
                    section: "18",
                    question_en: "<p>26. What are the thin filamentous extensions that the motile bacterial cells have from their cell wall called ?</p>",
                    question_hi: "<p>26. गतिशील जीवाणु कोशिकाओं की कोशिका भित्ति में पाए जाने वाले पतले तंतुमय विस्तार (filamentous extensions) क्या कहलाते हैं ?</p>",
                    options_en: [
                        "<p>Fimbriae</p>",
                        "<p>Flagella</p>",
                        "<p>Lamellae</p>",
                        "<p>Pili</p>"
                    ],
                    options_hi: [
                        "<p>फिम्ब्री (Fimbriae)</p>",
                        "<p>कशाभिका (Flagella)</p>",
                        "<p>लामेली (Lamellae)</p>",
                        "<p>पिली (Pili)</p>"
                    ],
                    solution_en: "<p>26.(b) <strong>Flagella.</strong> The bacterial flagella are long (3 to 12 &micro;m), filamentous surface appendages about 12 to 30 nm in diameter. The protein subunits of a flagellum are assembled to form a cylindrical structure with a hollow core. A flagellum consists of three parts: Filament, Hook, and Basal body. Pili are short and thin thread-like structures projecting out from the cell wall in some bacteria.</p>",
                    solution_hi: "<p>26.(b) <strong>कशाभिका </strong>(Flagella)। जीवाणुओं के कशाभिका लंबे (3 से 12 &micro;m) तथा तंतुमय सतही उपांग लगभग 12 से 30 nm व्यास के होते हैं। कशाभिका की प्रोटीन उपइकाइयां एकत्रित होकर एक खोखले कोर वाली बेलनाकार संरचना बनाती हैं। एक कशाभिका में तीन भाग होते हैं : फिलामेंट, हुक और बेसल बॉडी। पिली कुछ जीवाणुओं में कोशिका भित्ति से बाहर निकली हुई छोटी और पतली धागे जैसी संरचनाएँ होती हैं।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "27",
                    section: "18",
                    question_en: "<p>27. Under the LIC\'s Bima Sakhi Yojana, what is the primary aim of the initiative ?</p>",
                    question_hi: "<p>27. LIC की बीमा सखी योजना के तहत, इस पहल का प्राथमिक उद्देश्य क्या है ?</p>",
                    options_en: [
                        "<p>Promote women&rsquo;s entrepreneurship</p>",
                        "<p>Train women as LIC agents</p>",
                        "<p>Provide women-specific insurance schemes</p>",
                        "<p>Financial literacy for women</p>"
                    ],
                    options_hi: [
                        "<p>महिलाओं का उद्यमिता को बढ़ावा देना</p>",
                        "<p>महिलाओं को LIC एजेंट के रूप में प्रशिक्षित करना</p>",
                        "<p>महिलाओं के लिए विशेष बीमा योजनाएं प्रदान करना</p>",
                        "<p>महिलाओं के लिए वित्तीय साक्षरता बढ़ाना</p>"
                    ],
                    solution_en: "<p>27.(b) <strong>Train women as LIC agents.</strong> LIC\'s Bima Sakhi Yojana aims to empower women by training them as LIC agents, providing stipends, and enhancing financial independence. LIC is headquartered in Mumbai, and its Chairman is Siddhartha Mohanty.</p>",
                    solution_hi: "<p>27.(b) <strong>महिलाओं को LIC एजेंट के रूप में प्रशिक्षित करना। </strong>LIC की बीमा सखी योजना का उद्देश्य महिलाओं को LIC एजेंट के रूप में प्रशिक्षित करना है, उन्हें भत्ते प्रदान करना और वित्तीय स्वतंत्रता बढ़ाना है। LIC का मुख्यालय मुंबई में है और इसके चेयरमैन सिद्धार्थ मोहन्टी हैं।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "28",
                    section: "18",
                    question_en: "<p>28. In which year was \'Self Help Group - Bank Linkage Programme\' (SHG-BLP) introduced in India ?</p>",
                    question_hi: "<p>28. भारत में स्वयं सहायता समूह - बैंक सहबद्धता कार्यक्रम (SHG-BLP) किस वर्ष शुरू किया गया था ?</p>",
                    options_en: [
                        "<p>1992</p>",
                        "<p>1984</p>",
                        "<p>2000</p>",
                        "<p>1996</p>"
                    ],
                    options_hi: [
                        "<p>1992 में</p>",
                        "<p>1984 में</p>",
                        "<p>2000 में</p>",
                        "<p>1996 में</p>"
                    ],
                    solution_en: "<p>28.(a) <strong>1992. </strong>The Self-Help Group - Bank Linkage Programme was launched by NABARD in 1992-93 to provide financial assistance to self-help groups (SHGs) in rural areas. NABARD (National Bank for Agriculture and Rural Development): Founded in 1982, is a development bank focused on promoting rural development and agriculture in India.</p>",
                    solution_hi: "<p>28.(a) <strong>1992. </strong>ग्रामीण क्षेत्रों में स्वयं सहायता समूहों (SHG) को वित्तीय सहायता प्रदान करने के लिए NABARD द्वारा 1992-93 में स्वयं सहायता समूह - बैंक सहबद्धता कार्यक्रम शुरू किया गया था। NABARD (नेशनल बैंक फ़ॉर एग्रीकल्चर एंड रूरल डेवलपमेंट): 1982 में स्थापित, यह एक विकास बैंक है जो भारत में ग्रामीण विकास और कृषि को बढ़ावा देने पर केंद्रित है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "29",
                    section: "18",
                    question_en: "<p>29. Vasco da Gama, who discovered the sea route from Europe to India, belonged to which nation ?</p>",
                    question_hi: "<p>29. वास्को डी गामा, जिसने यूरोप से भारत तक के समुद्री मार्ग की खोज की, किस देश के थे ?</p>",
                    options_en: [
                        "<p>England</p>",
                        "<p>Portugal</p>",
                        "<p>Spain</p>",
                        "<p>Germany</p>"
                    ],
                    options_hi: [
                        "<p>इंग्लैंड</p>",
                        "<p>पुर्तगाल</p>",
                        "<p>स्पेन</p>",
                        "<p>जर्मनी</p>"
                    ],
                    solution_en: "<p>29.(b) <strong>Portugal. </strong>Vasco da Gama arrived in India at Calicut (Kozhikode) in 1498 AD during the reign of the Zamorin, a Hindu ruler. This event marked the discovery of the sea route from Europe to India. He reached India by sea via the \"Cape of Good Hope\". Francisco de Almeida was appointed as the first Portuguese governor in India. His policy is known for implementing the \"Policy of Blue Water,\" which aimed to establish Portuguese dominance in maritime trade.</p>",
                    solution_hi: "<p>29.(b) <strong>पुर्तगाल।</strong> वास्को डी गामा 1498 ई. में हिंदू शासक ज़मोरिन के शासनकाल के दौरान कालीकट (कोझिकोड) में भारत पहुंचे थे। इस घटना ने यूरोप से भारत तक समुद्री मार्ग की खोज को चिह्नित किया। वह \"केप ऑफ़ गुड होप\" के माध्यम से समुद्र के रास्ते भारत पहुंचे थे। फ्रांसिस्को डी अल्मेडा को भारत में पहला पुर्तगाली गवर्नर नियुक्त किया गया था। उनकी नीति \"ब्लू वॉटर की नीति\" को लागू करने के लिए जानी जाती है, जिसका उद्देश्य समुद्री व्यापार में पुर्तगाली प्रभुत्व स्थापित करना था।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "30",
                    section: "18",
                    question_en: "<p>30. Where was the 27th International Table Tennis Federation (ITTF)-Asian Table Tennis Championships 2024 held from 7th to 13th October 2024 ?</p>",
                    question_hi: "<p>30. 7 से 13 अक्टूबर 2024 तक आयोजित 27वीं अंतर्राष्ट्रीय टेबल टेनिस महासंघ (ITTF)-एशियाई टेबल टेनिस चैंपियनशिप 2024 कहां आयोजित हुई ?</p>",
                    options_en: [
                        "<p>Munich, Germany</p>",
                        "<p>Bridgetown, Barbados</p>",
                        "<p>Lausanne, Switzerland</p>",
                        "<p>Astana, Kazakhstan</p>"
                    ],
                    options_hi: [
                        "<p>म्यूनिख, जर्मनी</p>",
                        "<p>ब्रिजटाउन, बारबाडोस</p>",
                        "<p>लॉज़ेन, स्विट्जरलैंड</p>",
                        "<p>अस्ताना, कजाखस्तान</p>"
                    ],
                    solution_en: "<p>30.(d) <strong>Astana, Kazakhstan.</strong> Indian table tennis players won 3 bronze medals and finished 7th on the medal tally. Japan topped the medal tally with 8 medals (3 Gold, 2 Silver, 3 Bronze).</p>",
                    solution_hi: "<p>30.(d) <strong>अस्ताना, कजाखस्तान।</strong><br>भारतीय टेबल टेनिस खिलाड़ियों ने 3 कांस्य पदक जीते और पदक तालिका में 7वें स्थान पर रहे। जापान ने 8 पदकों (3 स्वर्ण, 2 रजत, 3 कांस्य) के साथ पदक तालिका में शीर्ष स्थान प्राप्त किया।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "31",
                    section: "18",
                    question_en: "<p>31. In which Article does the Finance Commission of India find a mention ?</p>",
                    question_hi: "<p>31. भारत के वित्त आयोग का उल्लेख किस अनुच्छेद में दिया गया है ?</p>",
                    options_en: [
                        "<p>Article 292</p>",
                        "<p>Article 375</p>",
                        "<p>Article 280</p>",
                        "<p>Article 370</p>"
                    ],
                    options_hi: [
                        "<p>अनुच्छेद 292</p>",
                        "<p>अनुच्छेद 375</p>",
                        "<p>अनुच्छेद 280</p>",
                        "<p>अनुच्छेद 370</p>"
                    ],
                    solution_en: "<p>31.(c) <strong>Article 280. </strong>The Finance Commission is a constitutional body established to recommend the distribution of the net proceeds of taxes between the Central Government and the states, as well as among the states themselves. It is constituted by the President of India every five years. The Commission consists of five members, including a chairperson. Article 292: Borrowing by the Government of India. Article 375: Courts, authorities and officers continue to function subject to the provisions of the Constitution. Article 370: Temporary provisions with respect to the State of Jammu and Kashmir.</p>",
                    solution_hi: "<p>31.(c) <strong>अनुच्छेद 280. </strong>वित्त आयोग एक संवैधानिक निकाय है जिसकी स्थापना केन्द्र सरकार और राज्यों के बीच तथा राज्यों के बीच करों की शुद्ध आय के वितरण की सिफारिश करने के लिए की गई है। इसका गठन भारत के राष्ट्रपति द्वारा प्रत्येक पाँच वर्ष में किया जाता है। आयोग में एक अध्यक्ष सहित पाँच सदस्य होते हैं। अनुच्छेद 292: भारत सरकार द्वारा उधार लेना। अनुच्छेद 375: न्यायालय, प्राधिकरण और अधिकारी संविधान के प्रावधानों के अधीन कार्य करना जारी रखते हैं। अनुच्छेद 370: जम्मू और कश्मीर राज्य के संबंध में अस्थायी प्रावधान।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "32",
                    section: "18",
                    question_en: "<p>32. Identify the number of electrons found in the outermost shell of halogen.</p>",
                    question_hi: "<p>32. हैलोजन के बाह्यतम कोश में पाए जाने वाले इलेक्ट्रॉनों की संख्या की पहचान कीजिए।</p>",
                    options_en: [
                        "<p>Two</p>",
                        "<p>One</p>",
                        "<p>Five</p>",
                        "<p>Seven</p>"
                    ],
                    options_hi: [
                        "<p>दो</p>",
                        "<p>एक</p>",
                        "<p>पाँच</p>",
                        "<p>सात</p>"
                    ],
                    solution_en: "<p>32.(d) <strong>Seven. </strong>Group 17 of the Periodic Table consists of the following elements : Fluorine (F), Chlorine (Cl), Bromine (Br), Iodine (I), Astatine (At). These elements are extremely reactive. The common oxidation state of these elements is -1. However, the highest oxidation state can be +7. They form oxides, hydrogen halides, interhalogen compounds, and oxoacids.</p>",
                    solution_hi: "<p>32.(d) <strong>सात।</strong> आवर्त सारणी के समूह 17 में निम्नलिखित तत्व शामिल हैं : फ्लोरीन (F), क्लोरीन (Cl), ब्रोमीन (Br), आयोडीन (I), एस्टेटिन (At)। ये तत्व अत्यधिक अभिक्रियाशील होते हैं। इन तत्वों की सामान्य ऑक्सीकरण अवस्था -1 होती है। हालाँकि, अधिकतम ऑक्सीकरण अवस्था +7 हो सकती है। ये ऑक्साइड, हाइड्रोजन हैलाइड, इंटरहेलोजन यौगिक और ऑक्सोएसिड बनाते हैं।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "33",
                    section: "18",
                    question_en: "<p>33. Which of the following statements are correct regarding conditions required to recognise as a political party in India ?<br>A. If a political party is treated as a recognised political party in four or more States, it shall be known as a &lsquo;National Party&rsquo; throughout the whole of India.<br>B. If a political party is treated as a recognised political party in less than four States, it shall be known as a &lsquo;State Party&rsquo; in the State or States in which it is so recognised.<br>C. National Parties need only one proposer for filing the nomination and are also entitled for two sets of electoral rolls free of cost.</p>",
                    question_hi: "<p>33. भारत में एक राजनीतिक दल के रूप में मान्यता प्राप्त करने के लिए आवश्यक शर्तों के संबंध में निम्नलिखित में से कौन-सा/से कथन सही है/हैं ?<br>A. यदि किसी राजनीतिक दल को चार या अधिक राज्यों में एक मान्यता प्राप्त राजनीतिक दल के रूप में माना जाता है, तो इसे पूरे भारत में एक राष्ट्रीय दल के रूप में जाना जाएगा।<br>B. यदि किसी राजनीतिक दल को चार से कम राज्यों में एक मान्यता प्राप्त राजनीतिक दल के रूप में माना जाता है, तो इसे उस राज्य या राज्यों में एक राज्य दल के रूप में जाना जाएगा, जिसमें/जिनमें इसे मान्यता प्राप्त है।<br>C. राष्ट्रीय दलों को नामांकन दाखिल करने के लिए केवल एक प्रस्तावक की आवश्यकता होती है और वे मतदाता सूची के दो सेट मुफ्त पाने के भी हकदार होते हैं।</p>",
                    options_en: [
                        "<p>A and C only</p>",
                        "<p>A, B and C</p>",
                        "<p>B and C only</p>",
                        "<p>A and B Only</p>"
                    ],
                    options_hi: [
                        "<p>केवल A और C</p>",
                        "<p>A, B और C</p>",
                        "<p>केवल B और C</p>",
                        "<p>केवल A और B</p>"
                    ],
                    solution_en: "<p>33.(b) <strong>A, B and C. </strong>Criteria for a Party to be Recognised as a National Party : It is &lsquo;recognised&rsquo; in four or more states as a state party; or if its candidates polled at least 6% of total valid votes in any four or more states in the last Lok Sabha or Assembly elections and has at least four MPs in the last Lok Sabha polls; or if it has won at least 2% of the total seats in the Lok Sabha from not less than three states. There are 6 national parties in India (As of September 2024).</p>",
                    solution_hi: "<p>33.(b) <strong>A, B और C ।</strong> किसी पार्टी को राष्ट्रीय पार्टी का दर्जा प्राप्त करने के लिए कुछ विशिष्ट मानदंड - यदि किसी पार्टी को चार या उससे अधिक राज्यों में राज्य स्तरीय पार्टी के रूप में मान्यता प्राप्त है; या फिर उस पार्टी के उम्मीदवारों ने पिछले लोकसभा या विधानसभा चुनावों में किसी भी चार या उससे अधिक राज्यों में कुल वैध मतों का कम से कम 6% वोट प्राप्त किया हो और साथ ही पिछले लोकसभा चुनावों में उसके कम से कम चार सांसद हों।; या फिर उस पार्टी ने कम से कम तीन राज्यों से लोकसभा की कुल सीटों में से कम से कम 2% सीटें जीती हो। वर्तमान में भारत में 6 राष्ट्रीय दल हैं (सितंबर 2024 तक)।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "34",
                    section: "18",
                    question_en: "<p>34. What is pro-vitamin A or the anti-cancer compound present in carrots called ?</p>",
                    question_hi: "<p>34. गाजर में मौजूद प्रो-विटामिन A या कैंसर रोधी यौगिक क्या कहलाता है ?</p>",
                    options_en: [
                        "<p>Beta-carotene</p>",
                        "<p>Alpha-resins</p>",
                        "<p>Delta-terpenes</p>",
                        "<p>Alpha-terpenoid</p>"
                    ],
                    options_hi: [
                        "<p>बीटा-कैरोटीन</p>",
                        "<p>अल्फा-रेजिन</p>",
                        "<p>डेल्टा-टरपीन्स</p>",
                        "<p>अल्फा-टरपीनॉयड</p>"
                    ],
                    solution_en: "<p>34.(a) <strong>Beta-carotene.</strong> It is a yellow-orange pigment found in fruits and vegetables. It converts to Vitamin A in the body, supporting healthy vision, immune function, and skin. As an antioxidant, it protects cells from damage, reducing cancer and heart disease risk. Food sources include carrots, sweet potatoes, dark leafy greens, and squash.</p>",
                    solution_hi: "<p>34.(a) <strong>बीटा-कैरोटीन। </strong>यह फलों और सब्जियों में पाया जाने वाला एक पीला-नारंगी रंगद्रव्य है। यह शरीर में विटामिन A में परिवर्तित हो जाता है, जिससे स्वस्थ दृष्टि, प्रतिरक्षा प्रणाली और त्वचा को सहायता प्रदान करता है। एक एंटीऑक्सीडेंट के रूप में, यह कोशिकाओं को क्षति से बचाता है, जिससे कैंसर और हृदय रोग का जोखिम कम होता है। इसके खाद्य स्रोतों में गाजर, शकरकंद, हरे पत्तेदार सब्जियाँ और स्क्वैश शामिल हैं।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "35",
                    section: "18",
                    question_en: "<p>35. Which of the following statements is/are true about private goods ?<br>I. Rivalrous in consumption<br>II. Non-excludability<br>III. Negative marginal cost</p>",
                    question_hi: "<p>35. निम्नलिखित में से कौन सा/से कथन निजी वस्तुओं के संबंध में सत्य है/हैं ?<br>I. खपत में प्रतिद्वंद्विता<br>II. गैर-बहिष्कृतता<br>III. ऋणात्मक सीमांत लागत</p>",
                    options_en: [
                        "<p>Only III</p>",
                        "<p>Only I</p>",
                        "<p>Both II and III</p>",
                        "<p>Only II</p>"
                    ],
                    options_hi: [
                        "<p>केवल III</p>",
                        "<p>केवल I</p>",
                        "<p>II और III दोनों</p>",
                        "<p>केवल II</p>"
                    ],
                    solution_en: "<p>35.(b) <strong>Only I. </strong>One person or household consumes a private good and is both excludable and rivalrous. This means that one person&rsquo;s consumption of the good prevents others from consuming it, and the provider can exclude others from using it. Examples include food, clothing, and cars. Public goods : Nonexcludability, and Nonrivalrous consumption. Examples - National defense, Street lights, Public Parks, etc.</p>",
                    solution_hi: "<p>35.(b) <strong>केवल I. </strong>एक व्यक्ति या परिवार एक निजी वस्तु का उपभोग करता है और वह बहिष्कृत और प्रतिद्वंद्वितापूर्ण दोनों है। इसका मतलब है कि एक व्यक्ति द्वारा वस्तु का उपभोग दूसरों को उसका उपभोग करने से रोकता है, और प्रदाता दूसरों को इसका उपयोग करने से वर्जित कर सकता है। उदाहरणों में भोजन, कपड़े और कार शामिल हैं। सार्वजनिक वस्तुएँ: गैर-बहिष्करणीयता, और गैर-प्रतिद्वंद्वितापूर्ण उपभोग। उदाहरण - राष्ट्रीय रक्षा, स्ट्रीट लाइट, सार्वजनिक पार्क, आदि।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "36",
                    section: "18",
                    question_en: "<p>36. Which sector promotes modernisation and stable employment in the economy ?</p>",
                    question_hi: "<p>36. कौन-सा क्षेत्र अर्थव्यवस्था में आधुनिकीकरण और स्थिर रोजगार को बढ़ावा देता है ?</p>",
                    options_en: [
                        "<p>Service sector</p>",
                        "<p>Agriculture sector</p>",
                        "<p>Energy sector</p>",
                        "<p>Industrial sector</p>"
                    ],
                    options_hi: [
                        "<p>सेवा क्षेत्र</p>",
                        "<p>कृषि क्षेत्र</p>",
                        "<p>ऊर्जा क्षेत्र</p>",
                        "<p>औद्योगिक क्षेत्र</p>"
                    ],
                    solution_en: "<p>36.(d) <strong>Industrial sector </strong>(The secondary sector) covers activities in which natural products are changed into other forms through ways of manufacturing that we associate with industrial activity. Examples of Secondary Sector - Industrial production, cotton fabric, sugar cane production etc.</p>",
                    solution_hi: "<p>36.(d) <strong>औद्योगिक क्षेत्र </strong>(द्वितीयक क्षेत्र) में वे गतिविधियाँ शामिल हैं जिनमें प्राकृतिक उत्पादों को विनिर्माण के तरीकों के माध्यम से अन्य रूपों में परिवर्तित किया जाता है जिन्हें हम औद्योगिक गतिविधि से जोड़ते हैं। द्वितीयक क्षेत्र के उदाहरण - औद्योगिक उत्पादन, सूती कपड़ा, गन्ना उत्पादन आदि।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "37",
                    section: "18",
                    question_en: "<p>37. Which of the following statement/statements is/are true regarding the light year ?<br>Light year is a unit of distance.<br>2. Light is a unit of time.<br>3. A light year is the distance that light travels in one Earth year.<br>4. Light year is the measurement of light intensity.</p>",
                    question_hi: "<p>37. निम्नलिखित में से कौन-सा/से कथन प्रकाश वर्ष के संदर्भ में सत्य है/हैं ?<br>1. प्रकाश वर्ष, दूरी की इकाई है।<br>2. प्रकाश, समय की इकाई है।<br>3. प्रकाश वर्ष वह दूरी है, जो प्रकाश, एक पृथ्वी वर्ष में तय करता है।<br>4. प्रकाश वर्ष प्रकाश की तीव्रता का माप है।</p>",
                    options_en: [
                        "<p>Only 1 is correct</p>",
                        "<p>Only 1 and 3 are correct</p>",
                        "<p>Only 2 is correct</p>",
                        "<p>Only 1 and 2 are correct</p>"
                    ],
                    options_hi: [
                        "<p>केवल 1 सही है</p>",
                        "<p>केवल 1 और 3 सही हैं</p>",
                        "<p>केवल 2 सही है</p>",
                        "<p>केवल 1 और 2 सही हैं</p>"
                    ],
                    solution_en: "<p>37.(b) <strong>Only 1 and 3 are correct. </strong>1 light year = 9.46 &times; 10<sup>15</sup> m = 9.46 &times; 10<sup>12</sup> km = 9.46 &times; 10<sup>17</sup> cm. Relative Speed of light: Air &gt; Water &gt; Glass &gt; Diamond. In air (3 &times; 10<sup>8</sup> m/s), in water (2.26 &times; 10<sup>8</sup> m/s), in glass (2 &times; 10<sup>8</sup> m/s), in diamond (1.25 &times;10<sup>8</sup> m/s). Light radiation travels at a speed of approximately 299,792,458 (m/s) or 3 &times; 10<sup>8</sup> m/s in a vacuum. Relative Speed of light : Solid ＜ Liquid ＜ Gas ＜Vaccum.</p>",
                    solution_hi: "<p>37.(b) <strong>केवल 1 और 3 सही हैं।</strong> 1 प्रकाश वर्ष = 9.46 &times; 10<sup>15 </sup>मीटर = 9.46 &times; 10<sup>12</sup> किमी = 9.46 &times; 10<sup>17</sup> सेमी। प्रकाश की सापेक्ष गति: वायु &gt; जल &gt; कांच &gt; हीरा। वायु में (3 &times; 10<sup>8</sup> मीटर/सेकेंड), जल में (2.26 &times; 10<sup>8</sup> मीटर/सेकेंड), कांच में (2 &times; 10<sup>8</sup> मीटर/सेकेंड), हीरे में (1.25 &times; 10<sup>8</sup> मीटर/सेकेंड)। प्रकाश विकिरण निर्वात में लगभग 299,792,458 (मी/सेकेंड) या 3 &times; 10<sup>8</sup> मीटर/सेकेंड की गति से गमन करता है। प्रकाश की सापेक्ष गति: ठोस ＜ द्रव ＜ गैस ＜ निर्वात।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "38",
                    section: "18",
                    question_en: "<p>38. With progressive delicensing of industries as part of liberalisation, which of the following is the industry that is removed from the requirement of license ?</p>",
                    question_hi: "<p>38. उदारीकरण के भाग के रूप में उद्योगों के प्रगतिशील डिलाइसेंसिंग के साथ निम्नलिखित में से किस उद्योग को लाइसेंस की आवश्यकता से हटा दिया गया है ?</p>",
                    options_en: [
                        "<p>Cigars and cigarettes of tobacco, and manufactured tobacco substitutes</p>",
                        "<p>Industrial explosives</p>",
                        "<p>Hazardous chemicals</p>",
                        "<p>Coal and lignite</p>"
                    ],
                    options_hi: [
                        "<p>तंबाकू के सिगार और सिगरेट, और निर्मित तंबाकू के विकल्प</p>",
                        "<p>औद्योगिक विस्फोटक</p>",
                        "<p>खतरनाक रसायन</p>",
                        "<p>कोयला और लिग्नाइट</p>"
                    ],
                    solution_en: "<p>38.(d) <strong>Coal and lignite. </strong>Mineral Laws (Amendment) Bill, 2020 provides that prior approval of the central government will not be required by the state government in granting licenses for coal and lignite. Industries subject to compulsory licensing in India: Tobacco items, Defense aerospace and warships, Hazardous chemicals, Industrial explosives.</p>",
                    solution_hi: "<p>38.(d) <strong>कोयला और लिग्नाइट ।&nbsp;</strong>खनिज कानून (संशोधन) विधेयक, 2020 में प्रावधान है कि कोयला और लिग्नाइट के लिए लाइसेंस देने में राज्य सरकार को केंद्र सरकार की पूर्व स्वीकृति की आवश्यकता नहीं होगी। भारत में अनिवार्य लाइसेंसिंग के अधीन उद्योग: तम्बाकू उत्पाद, रक्षा एयरोस्पेस और युद्धपोत, खतरनाक रसायन, औद्योगिक विस्फोटक आदि।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "39",
                    section: "18",
                    question_en: "<p>39. In the context of local storms of hot weather season, Nor\'Westers are:</p>",
                    question_hi: "<p>39. गर्मी के मौसम में आने वाले स्थानीय तूफानों के संदर्भ में नोर वेस्टर्स (Nor\'Westers) क्या हैं:</p>",
                    options_en: [
                        "<p>the pre-monsoon showers that are a common phenomenon in Kerala and coastal areas of Karnataka</p>",
                        "<p>the dreaded evening thunderstorms in Bengal and Assam</p>",
                        "<p>the hot, dry and oppressing winds blowing in the Northern plains from Punjab to Bihar with higher intensity between Delhi and Patna</p>",
                        "<p>a nearby wind that blows coffee flowers in Kerala</p>"
                    ],
                    options_hi: [
                        "<p>मानसून से पहले की बारिश, जो केरल और कर्नाटक के तटीय क्षेत्रों में होने वाली एक सामान्य घटना है</p>",
                        "<p>बंगाल और असम में शाम के समय चलने वाली भयानक आँधियाँ</p>",
                        "<p>दिल्ली और पटना के बीच उच्च तीव्रता के साथ पंजाब से बिहार तक उत्तरी मैदानी इलाकों में चलने वाली गर्म, शुष्क और दमनकारी हवाएँ</p>",
                        "<p>केरल में कॉफी के फूलों को ब्लो करने (खिलाने) वाली नजदीकी हवा</p>"
                    ],
                    solution_en: "<p>39.(b) Nor Westers: Their notorious nature can be understood from the local nomenclature of &lsquo;Kalbaisakhi&rsquo;, a calamity of the month of Baisakh. These showers are useful for tea, jute and rice cultivation. In Assam, these storms are known as &ldquo;Bardoli Chheerha&rdquo;. Some Famous Local Storms: Mango Shower : Pre-monsoon showers in Kerala and Karnataka. It helps in the early ripening of mangoes. Blossom Shower: Useful for coffee flowers blooming in Kerala. Loo : Hot, dry and oppressing winds blowing in the Northern plains.</p>",
                    solution_hi: "<p>39.(b) नॉर वेस्टर्स: उनकी कुख्यात प्रकृति को बैसाख माह की एक आपदा \'कालबैसाखी\' के स्थानीय नाम से जाना जाता है। ये वर्षा चाय, जूट और चावल की खेती के लिए उपयोगी होती है। असम में, इन तूफानों को &ldquo;बारडोली छेड़ा&rdquo; के नाम से जाना जाता है। कुछ प्रसिद्ध स्थानीय तूफान : मैंगो शावर - केरल और कर्नाटक में प्री-मानसून वर्षा। यह आमों को जल्दी पकने में मदद करता है। ब्लॉसम शावर - केरल में खिलने वाले कॉफी के फूलों के लिए उपयोगी है। लू - उत्तरी मैदानों में चलने वाली गर्म, शुष्क और दमनकारी पवने।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "40",
                    section: "18",
                    question_en: "<p>40. Good Friday is related to:</p>",
                    question_hi: "<p>40. गुड फ्राइडे किससे संबंधित है ?</p>",
                    options_en: [
                        "<p>Jains</p>",
                        "<p>Hindus</p>",
                        "<p>Sikhs</p>",
                        "<p>Christians</p>"
                    ],
                    options_hi: [
                        "<p>जैन</p>",
                        "<p>हिंदुओं</p>",
                        "<p>सिखों</p>",
                        "<p>ईसाइयों</p>"
                    ],
                    solution_en: "<p>40.(d)<strong> Christians. </strong>Good Friday is a Christian holy day observing the crucifixion of Jesus and his death at Calvary. Other festivals and religions: Hindus - Diwali, Holi, Rakshabandhan. Christians - Christmas, Easter. Jains - Mahavir Jayanti, Paryushana, Mahamastakabhisheka festival. Sikhs - Hola Mohalla, Vaisakhi, Guru Nanak Gurpurab.</p>",
                    solution_hi: "<p>40.(d) <strong>ईसाइयों। </strong>गुड फ्राइडे ईसाइयों का पवित्र दिन है, जो ईसा मसीह के सूली पर चढ़ने और कैल्वरी में उनकी मृत्यु का स्मरण करता है। अन्य धर्म तथा त्योहार : हिंदू - दिवाली, होली, रक्षाबंधन। ईसाई - क्रिसमस, ईस्टर। जैन - महावीर जयंती, पर्यूषण, महामस्तकाभिषेक उत्सव। सिख - होला मोहल्ला, वैसाखी, गुरु नानक गुरुपर्व।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "41",
                    section: "18",
                    question_en: "<p>41. Which of the following statements are correct regarding the jurisdiction of the Supreme Court of India ?<br>A. Original jurisdiction extends to any dispute between the Government of India and one or more States or between the Government of India and any State or States on one side and one or more States on the other or between two or more States.<br>B. Appellate Jurisdiction extends when the Union plus some States are one side and some other States on the other side of the dispute.<br>C. Under Articles 129 and 142 of the Constitution, the Supreme Court has been vested with the power to punish for contempt of court.<br>D. As of now, the International Commercial Arbitration cannot be initiated in the Supreme Court.</p>",
                    question_hi: "<p>41. भारत के सर्वोच्च न्यायालय के क्षेत्राधिकार के संबंध में निम्नलिखित में से कौन सा कथन सही है ?<br>A. मूल क्षेत्राधिकार भारत सरकार और एक या अधिक राज्यों के बीच या भारत सरकार और किसी राज्य या राज्यों और दूसरी तरफ एक या अधिक राज्यों के बीच या दो या अधिक राज्यों के बीच किसी भी विवाद तक विस्तृत है।<br>B. अपीलीय अधिकार क्षेत्र का विस्तार तब होता है जब संघ और कुछ राज्य विवाद के एक तरफ होते हैं और कुछ अन्य राज्य दूसरी तरफ होते हैं।&nbsp;<br>C. संविधान के अनुच्छेद 129 और 142 के तहत, सर्वोच्च न्यायालय में अदालत की अवमानना के लिए दंडित करने की शक्ति निहित है।<br>D. अभी तक, सर्वोच्च न्यायालय में अंतर्राष्ट्रीय वाणिज्यिक मध्यस्थता शुरू नहीं की जा सकती है।</p>",
                    options_en: [
                        "<p>B and D only</p>",
                        "<p>A and C only</p>",
                        "<p>A, B, C and D</p>",
                        "<p>A and B only</p>"
                    ],
                    options_hi: [
                        "<p>B और D केवल</p>",
                        "<p>A और C केवल</p>",
                        "<p>A,B, C और D</p>",
                        "<p>A और B केवल</p>"
                    ],
                    solution_en: "<p>41.(b) <strong>A and C only. </strong>Advisory Jurisdiction (Article 143) - The President to seek the opinion of the Supreme Court. The appellate jurisdiction of the Supreme Court can be invoked by a certificate granted by the High Court concerned under Article 132(1), 133(1) or 134 of the Constitution. The Supreme Court (Article 124) of India is the apex judicial body under the Constitution of India. Inaugurated - On January, 28, 1950.</p>",
                    solution_hi: "<p>41.(b) <strong>A और C केवल। </strong>सलाहकार क्षेत्राधिकार (अनुच्छेद 143) - सर्वोच्च न्यायालय से परामर्श करने की राष्ट्रपति की शक्ति। सर्वोच्च न्यायालय के अपीलीय क्षेत्राधिकार को संविधान के अनुच्छेद 132(1), 133(1) या 134 के तहत संबंधित उच्च न्यायालय द्वारा दिए गए प्रमाण पत्र द्वारा लागू किया जा सकता है। भारत का सर्वोच्च न्यायालय (अनुच्छेद 124) भारत के संविधान के तहत सर्वोच्च न्यायिक निकाय है। उद्घाटन - 28 जनवरी, 1950 को।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "42",
                    section: "18",
                    question_en: "<p>42. Which of the following organic compounds is a benzenoid ?</p>",
                    question_hi: "<p>42. निम्नलिखित में से कौन सा कार्बनिक यौगिक एक बेन्जीनॉइड है ?</p>",
                    options_en: [
                        "<p>Aniline</p>",
                        "<p>Hexane</p>",
                        "<p>Propane</p>",
                        "<p>Acetaldehyde</p>"
                    ],
                    options_hi: [
                        "<p>ऐनिलीन</p>",
                        "<p>हेक्सेन</p>",
                        "<p>प्रोपेन</p>",
                        "<p>ऐसिटैल्डिहाइड</p>"
                    ],
                    solution_en: "<p>42.(a) <strong>Aniline. </strong>Aromatic compounds are special types of compounds. These include benzene and other related ring compounds (benzenoid). Benzenoid aromatic compounds: Benzene, Naphthalene. Like alicyclic compounds, aromatic compounds may also have hetero atoms in the ring. Such compounds are called heterocyclic aromatic compounds. Example -Furan, Thiophene, Pyridine.</p>",
                    solution_hi: "<p>42.(a) <strong>ऐनिलीन। </strong>एरोमेटिक (चक्रीय) यौगिक विशेष प्रकार के यौगिक होते हैं। इनमें बेंजीन और अन्य संबंधित रिंग यौगिक (बेंजेनॉइड) शामिल हैं। बेंजीनॉइड एरोमेटिक यौगिक: बेंजीन, नेफ़थलीन। ऐलीसाइक्लिक यौगिकों की तरह, एरोमेटिक यौगिकों में भी रिंग में विषम परमाणु हो सकते हैं। ऐसे यौगिकों को हेटरोसाइक्लिक एरोमेटिक यौगिक कहा जाता है। उदाहरण -फ़्यूरान, थायोफीन, पाइरीडीन।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "43",
                    section: "18",
                    question_en: "<p>43. When did the second urbanisation in Ganga Valley take place ?</p>",
                    question_hi: "<p>43. गंगा घाटी में द्वितीय नगरीकरण का आरंभ कब हुआ था ?</p>",
                    options_en: [
                        "<p>5<sup>th</sup> Century BCE</p>",
                        "<p>3<sup>rd</sup> Century BCE</p>",
                        "<p>6<sup>th</sup> Century BCE</p>",
                        "<p>2<sup>nd</sup> Century BCE</p>"
                    ],
                    options_hi: [
                        "<p>पाँचवीं शताब्दी ईसा पूर्व</p>",
                        "<p>तीसरी शताब्दी ईसा पूर्व</p>",
                        "<p>छठी शताब्दी ईसा पूर्व</p>",
                        "<p>दूसरी शताब्दी ईसा पूर्व</p>"
                    ],
                    solution_en: "<p>43.(c) <strong>6th Century BCE. </strong>The second urbanization in the Ganga Valley occurred between the 6th and 3rd centuries BCE, marked by the rise of large-scale towns and states. This phase saw the emergence of cities in the middle Gangetic basin, with Magadha gaining prominence and forming the core of the Mauryan Empire. The First urbanisation in India took place during the Harappan civilization.</p>",
                    solution_hi: "<p>43.(c) <strong>छठी शताब्दी ईसा पूर्व। </strong>गंगा घाटी में दूसरा शहरीकरण छठी से तीसरी शताब्दी ई.पू. के बीच हुआ था, जिसकी पहचान बड़े पैमाने पर कस्बों और राज्यों के उदय से हुई। इस चरण में मध्य गंगा बेसिन में शहरों का उदय हुआ, जिसमें मगध को प्रमुखता मिली और मौर्य साम्राज्य का केंद्र बना। भारत में पहला शहरीकरण हड़प्पा सभ्यता के दौरान हुआ था।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "44",
                    section: "18",
                    question_en: "<p>44. What are bases that are soluble in water called ?</p>",
                    question_hi: "<p>44. जल में घुलनशील क्षार क्या कहलाते हैं ?</p>",
                    options_en: [
                        "<p>Alkenes</p>",
                        "<p>Acids</p>",
                        "<p>Alkalis</p>",
                        "<p>Alkanes</p>"
                    ],
                    options_hi: [
                        "<p>एल्कीन</p>",
                        "<p>अम्ल</p>",
                        "<p>अल्कली</p>",
                        "<p>ऐल्केन</p>"
                    ],
                    solution_en: "<p>44.(c) <strong>Alkalis. </strong>When a base dissolves in water, it forms hydroxide ions (OH⁻), which makes the solution basic with a pH greater than 7. Not all bases are soluble, but those that are, like sodium hydroxide (NaOH) and potassium hydroxide (KOH), are called alkalis.</p>",
                    solution_hi: "<p>44.(c) <strong>अल्कली। </strong>जब कोई क्षार जल में घुलता है, तो वह हाइड्रॉक्साइड आयन (OH⁻) बनाता है, जो विलयन को क्षारीय बनाता है जिसका pH मान 7 से अधिक होता है। सभी क्षार घुलनशील नहीं होते, लेकिन जो घुलनशील होते हैं, जैसे सोडियम हाइड्रॉक्साइड (NaOH) और पोटेशियम हाइड्रॉक्साइड (KOH), उन्हें अल्कली कहते हैं।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "45",
                    section: "18",
                    question_en: "<p>45. In which of the following schedules of the Wildlife (Protection) Act, 1972 is the most common wild cat in India \'Felis chaus\' listed ?</p>",
                    question_hi: "<p>45. वन्यजीव (संरक्षण) अधिनियम, 1972 की निम्नलिखित में से किस अनुसूची में भारत की सबसे आम जंगली बिल्ली फीलिस चाउस सूचीबद्ध है ?</p>",
                    options_en: [
                        "<p>Schedule I</p>",
                        "<p>Schedule III</p>",
                        "<p>Schedule IV</p>",
                        "<p>Schedule II</p>"
                    ],
                    options_hi: [
                        "<p>अनुसूची I</p>",
                        "<p>अनुसूची III</p>",
                        "<p>अनुसूची IV</p>",
                        "<p>अनुसूची II</p>"
                    ],
                    solution_en: "<p>45.(d) <strong>Schedule II. </strong>Under this schedule, the species receives protection, and poaching or capturing it without proper authorization is prohibited. The Wild Life (Protection) Act, 1972 is an Indian law enacted to protect plant and animal species.</p>",
                    solution_hi: "<p>45.(d) <strong>अनुसूची II.</strong> इस अनुसूची के तहत, प्रजातियों को संरक्षण प्राप्त है, और उचित प्राधिकरण के बिना उनका अवैध शिकार या उन्हें पकड़ना प्रतिबंधित है। वन्य जीवन (संरक्षण) अधिनियम, 1972 एक भारतीय कानून है जिसे पौधों और जानवरों की प्रजातियों की रक्षा के लिए बनाया गया है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "46",
                    section: "18",
                    question_en: "<p>46. In which year was the Foreign Investment Promotion Board established ?</p>",
                    question_hi: "<p>46. विदेशी निवेश संवर्धन बोर्ड की स्थापना किस वर्ष हुई थी ?</p>",
                    options_en: [
                        "<p>1960</p>",
                        "<p>1968</p>",
                        "<p>1965</p>",
                        "<p>1991</p>"
                    ],
                    options_hi: [
                        "<p>1960</p>",
                        "<p>1968</p>",
                        "<p>1965</p>",
                        "<p>1991</p>"
                    ],
                    solution_en: "<p>46.(b) <strong>1968. </strong>The Foreign Investment Promotion Board (FIPB), housed in the Department of Economic Affairs, Ministry of Finance, is an inter-ministerial body, responsible for processing of FDI proposals and making recommendations for Government approval. In 1996, it was transferred to the Department for Promotion of Industry and Internal Trade (DPIIT) in the Ministry of Commerce and Industry. The Foreign Investment Promotion Board (FIPB) was replaced by the Foreign Investment Facilitation Portal (FIFP) in May 2017.</p>",
                    solution_hi: "<p>46.(b) <strong>1968. </strong>वित्त मंत्रालय के आर्थिक मामलों के विभाग में स्थित विदेशी निवेश संवर्धन बोर्ड (FIPB) एक अंतर-मंत्रालयी निकाय है, जो FDI प्रस्तावों के प्रसंस्करण और सरकारी अनुमोदन के लिए सिफारिशें करने के लिए उत्तरदायी है। 1996 में, इसे वाणिज्य और उद्योग मंत्रालय में उद्योग और आंतरिक व्यापार संवर्धन विभाग (DPIIT) को हस्तांतरित कर दिया गया था। मई 2017 में विदेशी निवेश संवर्धन बोर्ड (FIPB) को विदेशी निवेश सुविधा पोर्टल (FIFP) द्वारा प्रतिस्थापित किया गया था।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "47",
                    section: "18",
                    question_en: "<p>47. Identify a mobile source of food.</p>",
                    question_hi: "<p>47. भोजन के एक गतिशील स्रोत की पहचान कीजिए।</p>",
                    options_en: [
                        "<p>Dead goat</p>",
                        "<p>Deer</p>",
                        "<p>Shrubs</p>",
                        "<p>Grass</p>"
                    ],
                    options_hi: [
                        "<p>मरी हुई बकरी</p>",
                        "<p>हिरन</p>",
                        "<p>झाड़ी</p>",
                        "<p>घास</p>"
                    ],
                    solution_en: "<p>47.(b) <strong>Deer. </strong>A mobile source of food refers to organisms that can move from one location to another to obtain sustenance. In this context, deer are herbivorous animals that actively roam and forage for plants, leaves, and grass. Their mobility enables them to search for food across various areas, especially when resources become scarce in a particular location.</p>",
                    solution_hi: "<p>47.(b) <strong>हिरण। </strong>भोजन का एक गतिशील स्रोत उन जीवों को संदर्भित करता है जो जीविका प्राप्त करने के लिए एक स्थान से दूसरे स्थान पर जा सकते हैं। इस संदर्भ में, हिरण शाकाहारी जीव हैं जो सक्रिय रूप से घूमते हैं और पौधों, पत्तियों तथा घास की तलाश करते हैं। उनकी गतिशीलता उन्हें विभिन्न क्षेत्रों में भोजन की खोज करने में सक्षम बनाती है, खासकर जब किसी विशेष स्थान पर संसाधन दुर्लभ हो जाते हैं।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "48",
                    section: "18",
                    question_en: "<p>48. Which festival is celebrated after the month of Ramzan ?</p>",
                    question_hi: "<p>48. रमज़ान के महीने के बाद कौन-सा त्&zwj;योहार मनाया जाता है ?</p>",
                    options_en: [
                        "<p>Eid ul Fitr</p>",
                        "<p>Muharram</p>",
                        "<p>Milad un Nabi</p>",
                        "<p>Jammat Ul Vida</p>"
                    ],
                    options_hi: [
                        "<p>ईद उल फितर</p>",
                        "<p>मुहर्रम</p>",
                        "<p>मिलाद उन नबी</p>",
                        "<p>जमात उल विदा</p>"
                    ],
                    solution_en: "<p>48.(a) <strong>Eid ul Fitr. </strong>It is a religious festival celebrated by Muslims at the end of the holy month of Ramzan. It is celebrated on the first day of Shawwal, the tenth month of the Islamic calendar. Everyone celebrates this festival together and prays to God for happiness, peace, and prosperity.</p>",
                    solution_hi: "<p>48.(a)<strong> ईद उल फ़ितर। </strong>यह मुसलमानों द्वारा रमज़ान के पवित्र महीने के अंत में मनाया जाने वाला एक धार्मिक त्योहार है। यह इस्लामी कैलेंडर के दसवें महीने शव्वाल के पहले दिन मनाया जाता है। इस त्यौहार को सभी लोग मिलजुलकर मनाते हैं और ईश्वर से सुख, शांति और समृद्धि की प्रार्थना करते हैं।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "49",
                    section: "18",
                    question_en: "<p>49. The signs of charges of electron, proton and neutron are _____, respectively.</p>",
                    question_hi: "<p>49. इलेक्ट्रॉन, प्रोटॉन और न्यूट्रॉन के आवेश के चिह्न क्रमशः ______होते हैं।</p>",
                    options_en: [
                        "<p>negative, neutral and positive,</p>",
                        "<p>negative, positive and neutral,</p>",
                        "<p>neutral, positive and negative,</p>",
                        "<p>positive, negative and neutral,</p>"
                    ],
                    options_hi: [
                        "<p>ऋणात्मक, उदासीन और धनात्मक</p>",
                        "<p>ऋणात्मक, धनात्मक और उदासीन</p>",
                        "<p>उदासीन, धनात्मक और ऋणात्मक</p>",
                        "<p>धनात्मक, ऋणात्मक और उदासीन</p>"
                    ],
                    solution_en: "<p>49.(b) <strong>negative, positive and neutral. </strong>Protons and neutrons are collectively called nucleons. For an atom\'s electronic stability, the number of electrons must equal the number of protons, as protons have a +1 charge and electrons a &ndash;1 charge. Neutrons, in contrast, are neutral and carry no charge.</p>",
                    solution_hi: "<p>49.(b)<strong> ऋणात्मक, धनात्मक और उदासीन।</strong> प्रोटॉन और न्यूट्रॉन को सामूहिक रूप से न्यूक्लिऑन कहा जाता है। किसी परमाणु की इलेक्ट्रॉनिक स्थिरता के लिए, इलेक्ट्रॉनों की संख्या प्रोटॉन की संख्या के बराबर होनी चाहिए, क्योंकि प्रोटॉन में +1 आवेश होता है और इलेक्ट्रॉन में -1 आवेश होता है। इसके विपरीत, न्यूट्रॉन उदासीन होते हैं और इस पर कोई आवेश नहीं होता है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "50",
                    section: "17",
                    question_en: "<p>50. Which AI company launched Grok 3, claimed to be the smartest AI on Earth ?</p>",
                    question_hi: "<p>50. किस एआई (AI) कंपनी ने ग्रोक 3 (Grok 3) लॉन्च किया, जिसे अब तक का सबसे बुद्धिमान एआई बताया गया ?</p>",
                    options_en: [
                        "<p>OpenAI</p>",
                        "<p>Google DeepMind</p>",
                        "<p>xAI</p>",
                        "<p>Anthropic</p>"
                    ],
                    options_hi: [
                        "<p>ओपनएआई (OpenAI)</p>",
                        "<p>गूगल डीपमाइंड (Google DeepMind)</p>",
                        "<p>xAI</p>",
                        "<p>एंथ्रोपिक (Anthropic)</p>"
                    ],
                    solution_en: "<p>50.(c) <strong>xAI. </strong>Elon Musk&rsquo;s AI company xAI unveiled Grok 3, an advanced AI chatbot designed to be the most intelligent AI ever developed.</p>",
                    solution_hi: "<p>50.(c) <strong>xAI. </strong>एलन मस्क (Elon Musk) की एआई कंपनी xAI ने Grok 3 लॉन्च किया, जिसे अब तक का सबसे बुद्धिमान एआई चैटबॉट बताया गया है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "51",
                    section: "17",
                    question_en: "<p>51. If the price of rice is reduced by 24%, it enables Alok to buy 10 kg more rice for ₹2,500. The reduced rate of rice per kg is:</p>",
                    question_hi: "<p>51. यदि चावल की कीमत में 24% की कमी होती है, तो इससे आलोक ₹2,500 में 10 kg चावल अधिक खरीद पाता है। चावल की प्रति kg घटी हुई दर क्या है ?</p>",
                    options_en: [
                        "<p>₹25</p>",
                        "<p>₹60</p>",
                        "<p>₹50</p>",
                        "<p>₹75</p>"
                    ],
                    options_hi: [
                        "<p>₹25</p>",
                        "<p>₹60</p>",
                        "<p>₹50</p>",
                        "<p>₹75</p>"
                    ],
                    solution_en: "<p>51.(b) <br>Expenditure = price &times; quantity<br>Ratio&nbsp; -&nbsp; &nbsp;old&nbsp; :&nbsp; new<br>Price&nbsp; -&nbsp; &nbsp; 25&nbsp; :&nbsp; 19<br>Quantity - 19 : 25<br>Difference = 25 - 19 = 6 units<br>6 units = 10 kg<br>(New) 25 units = <math display=\"inline\"><mfrac><mrow><mn>10</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math> &times; 25 kg<br>New price per kg = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2500</mn><mo>&#215;</mo><mn>6</mn></mrow><mrow><mn>10</mn><mo>&#215;</mo><mn>25</mn></mrow></mfrac></math>&nbsp;= ₹ 60 per kg</p>",
                    solution_hi: "<p>51.(b) <br>व्यय = कीमत &times; मात्रा<br>अनुपात - पुराना : नया<br>मूल्य&nbsp; &nbsp; &nbsp;-&nbsp; &nbsp;25&nbsp; &nbsp;: 19<br>मात्रा&nbsp; &nbsp; &nbsp;-&nbsp; &nbsp;19&nbsp; &nbsp;: 25<br>अंतर = 25 - 19 = 6 इकाई <br>6 इकाई = 10 kg<br>(नया) 25 इकाई = <math display=\"inline\"><mfrac><mrow><mn>10</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math> &times; 25 kg<br>नई कीमत प्रति किलो =&nbsp;<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2500</mn><mo>&#215;</mo><mn>6</mn></mrow><mrow><mn>10</mn><mo>&#215;</mo><mn>25</mn></mrow></mfrac></math> = ₹ 60 per kg</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "52",
                    section: "17",
                    question_en: "52. Two quantities are in the ratio 3 : 5. If the first quantity is 155 kg. Find the other quantity (approximately).",
                    question_hi: "52. दो राशियाँ 3 : 5 के अनुपात में हैं। यदि पहली राशि 155 kg है, तो दूसरी राशि (लगभग) ज्ञात कीजिए।",
                    options_en: [
                        " 150.5 kg ",
                        " 200.4 kg ",
                        " 240.6 kg ",
                        " 258.3 kg"
                    ],
                    options_hi: [
                        " 150.5 kg ",
                        " 200.4 kg ",
                        " 240.6 kg ",
                        " 258.3 kg"
                    ],
                    solution_en: "<p>52.(d)<br>Let the first and the 2nd number be 3x and 5x<br>3<math display=\"inline\"><mi>x</mi></math> = 155 kg <br>5x&nbsp;=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>155</mn><mn>3</mn></mfrac></math> &times; 5 = 258.3 kg</p>",
                    solution_hi: "<p>52.(d)<br>माना कि पहली और दूसरी संख्या 3x&nbsp;और 5x है<br>3<math display=\"inline\"><mi>x</mi></math> = 155 kg <br>5x&nbsp;=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>155</mn><mn>3</mn></mfrac></math> &times; 5 = 258.3 kg</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "53",
                    section: "17",
                    question_en: "<p>53. Three solid iron cubes of edges 8 cm, 10 cm and 12 cm are melted together to make a new cube. It is observed that 496 cm&sup3; of the melted material is lost due to improper handling. The area (in cm&sup2;) of the whole surface of the newly formed cube is :</p>",
                    question_hi: "<p>53. 8 cm, 10 cm और 12 cm किनारों वाले लोहे के तीन ठोस घनों को एक साथ पिघलाकर एक नया घन बनाया जाता है। यह देखा गया है कि अनुचित संचालन के कारण पिघली हुई सामग्री का 496 cm<sup>3</sup> नष्ट हो जाता है। नए बने घन के संपूर्णपृष्ठ का क्षेत्रफल कितना (cm<sup>2</sup> में) है?</p>",
                    options_en: [
                        "<p>1331</p>",
                        "<p>1176</p>",
                        "<p>2197</p>",
                        "<p>3375</p>"
                    ],
                    options_hi: [
                        "<p>1331</p>",
                        "<p>1176</p>",
                        "<p>2197</p>",
                        "<p>3375</p>"
                    ],
                    solution_en: "<p>53.(b)<br>Volume of cubes = 8<sup>3 </sup>+ 10<sup>3 </sup>+ 12<sup>3</sup> = 512 + 1000 + 1728 = 3240 cm<sup>3</sup><br>Remaining volume of cube = 3240 - 496 = 2744 cm<sup>3</sup><br>a<sup>3</sup> = 2744 &rArr; a = 14 cm<br>Area of whole surface of the newly formed cube (6a<sup>2</sup>) = 6 &times; 14 &times; 14 = 1176 cm<sup>2</sup></p>",
                    solution_hi: "<p>53.(b)<br>घनों का आयतन = 8<sup>3 </sup>+ 10<sup>3 </sup>+ 12<sup>3</sup> = 512 + 1000 + 1728 = 3240 cm<sup>3</sup><br>घन का शेष आयतन = 3240 - 496 = 2744 cm<sup>3</sup><br>a<sup>3</sup> = 2744 &rArr; a = 14 cm<br>नए बने घन के संपूर्णपृष्ठ का क्षेत्रफल (6a<sup>2</sup>) = 6 &times; 14 &times; 14 = 1176 cm<sup>2</sup></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "54",
                    section: "17",
                    question_en: "<p>54. Vipul spends 30% of his monthly income on rent, 60% of the remaining on groceries, and 40% of the remaining on others. If he saves ₹ 7056 in a month, then how much (in ₹) does he spend on groceries ?</p>",
                    question_hi: "<p>54. विपुल अपनी मासिक आय का 30% किराए पर, शेष का 60% किराने के सामान पर और शेष का 40% अन्य पर खर्च करता है। यदि वह एक महीने में ₹ 7,056 बचाता है, तो वह किराने के सामान पर कितना (₹ में) खर्च करता है ?</p>",
                    options_en: [
                        "<p>18323</p>",
                        "<p>15230</p>",
                        "<p>17640</p>",
                        "<p>22386</p>"
                    ],
                    options_hi: [
                        "<p>18323</p>",
                        "<p>15230</p>",
                        "<p>17640</p>",
                        "<p>22386</p>"
                    ],
                    solution_en: "<p>54.(c) Let total income = 100 unit <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744687611408.png\" alt=\"rId56\" width=\"190\" height=\"234\"><br>Now according to the question,<br>16.8 unit = 7056 <br>&rArr; 1 Unit = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7056</mn><mrow><mn>16</mn><mo>.</mo><mn>8</mn></mrow></mfrac></math><br>&rArr; 42 Unit = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7056</mn><mrow><mn>16</mn><mo>.</mo><mn>8</mn></mrow></mfrac></math> &times; 42 = ₹17640</p>",
                    solution_hi: "<p>54.(c) माना कुल आय = 100 इकाई <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744687611855.png\" alt=\"rId57\" width=\"156\" height=\"218\"><br>अब प्रश्न के अनुसार,<br>16.8 इकाई = 7056 <br>&rArr; 1 इकाई = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7056</mn><mrow><mn>16</mn><mo>.</mo><mn>8</mn></mrow></mfrac></math><br>&rArr; 42 इकाई = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7056</mn><mrow><mn>16</mn><mo>.</mo><mn>8</mn></mrow></mfrac></math> &times; 42 = ₹17640</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "55",
                    section: "17",
                    question_en: "<p>55. A six-digit number is divisible by 33. If 21 is added to the number, then the new number which formed is also divisible by:</p>",
                    question_hi: "<p>55. छह अंकों की एक संख्या 33 से विभाज्य है। यदि संख्या में 21 जोड़ा जाता है, तो इस प्रकार प्राप्त नई संख्या ______से भी विभाज्य है।</p>",
                    options_en: [
                        "<p>5</p>",
                        "<p>4</p>",
                        "<p>2</p>",
                        "<p>3</p>"
                    ],
                    options_hi: [
                        "<p>5</p>",
                        "<p>4</p>",
                        "<p>2</p>",
                        "<p>3</p>"
                    ],
                    solution_en: "<p>55.(d)<br>Let six digit number = 33 &times;&nbsp;x <br>New number = 33 &times; x + 21 <br>= 3{(11 &times; x) + 7}<br>We can say that new number is divisible by 3</p>",
                    solution_hi: "<p>55.(d)<br>छह अंकों की संख्या = 33 &times; x <br>नई संख्या = 33 &times; x + 21 <br>= 3{(11 &times; x) + 7}<br>हम कह सकते हैं कि नई संख्या 3 से विभाज्य है |</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "56",
                    section: "17",
                    question_en: "<p>56. SinASinB = _______.</p>",
                    question_hi: "<p>56. SinASinB = _______.</p>",
                    options_en: [
                        "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math>{(sin(A + B) + sin(A - B)}</p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math>{(sin(A + B) - sin(A - B)}</p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math>{(cos(A + B) - cos(A - B)}</p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math>{(cos(A - B) - cos(A + B)}</p>"
                    ],
                    options_hi: [
                        "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math>{sin(A + B) + sin(A - B)}</p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math>{sin(A + B) - sin(A - B)}</p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math>{cos(A + B) - cos(A - B)}</p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math>{cos(A - B) - cos(A + B)}</p>"
                    ],
                    solution_en: "<p>56.(d)<br><strong>By using identity:</strong><br>SinASinB = - <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math>{(cos(A + B) - cos(A - B)}<br>hence,<br>SinASinB = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math>{(cos(A - B) - cos(A + B)}</p>",
                    solution_hi: "<p>56.(d)<br><strong>सूत्र का प्रयोग करने पर :</strong><br>SinASinB = - <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math>{(cos(A + B) - cos(A - B)}<br>अतः ,<br>SinASinB = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math>{(cos(A - B) - cos(A + B)}</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "57",
                    section: "17",
                    question_en: "<p>57. ∆DEF and ∆GHI are two similar triangles. If DE = 64 cm, GH = 24 cm and the perimeter of ∆GHI is 72 cm, then what is the sum of the lengths (in cm) of the sides EF and FD of the ∆DEF ?</p>",
                    question_hi: "<p>57. ∆DEF और ∆GHI दो समरूप त्रिभुज हैं। यदि DE = 64 cm, GH = 24 cm और ∆GHI का परिमाप 72 cm हो, तो ∆DEF की भुजाओं EF और FD की लंबाइयों (सेमी में) का योग कितना होगा ?</p>",
                    options_en: [
                        "<p>128</p>",
                        "<p>96</p>",
                        "<p>192</p>",
                        "<p>82</p>"
                    ],
                    options_hi: [
                        "<p>128</p>",
                        "<p>96</p>",
                        "<p>192</p>",
                        "<p>82</p>"
                    ],
                    solution_en: "<p>57.(a) <br>Ratio of perimeter of similar triangles are equal to their corresponding sides <br>According to question,<br><math display=\"inline\"><mfrac><mrow><mi>D</mi><mi>E</mi></mrow><mrow><mi>G</mi><mi>H</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>p</mi><mi>e</mi><mi>r</mi><mi>i</mi><mi>m</mi><mi>e</mi><mi>t</mi><mi>e</mi><mi>r</mi><mo>&#160;</mo><mi>o</mi><mi>f</mi><mo>&#8710;</mo><mi>D</mi><mi>E</mi><mi>F</mi></mrow><mrow><mi>p</mi><mi>e</mi><mi>r</mi><mi>i</mi><mi>m</mi><mi>e</mi><mi>t</mi><mi>e</mi><mi>r</mi><mo>&#160;</mo><mi>o</mi><mi>f</mi><mo>&#8710;</mo><mi>G</mi><mi>H</mi><mi>I</mi></mrow></mfrac></math> <br><math display=\"inline\"><mfrac><mrow><mn>64</mn></mrow><mrow><mn>24</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>X</mi><mn>72</mn></mfrac></math> &rArr; X = 192 cm<br>Hence, required sum (EF + FD) = 192 - 64 = 128 cm</p>",
                    solution_hi: "<p>57.(a) <br>समरूप त्रिभुजों के परिमाप का अनुपात उनकी संगत भुजाओं के बराबर होता है <br>प्रश्न के अनुसार,<br><math display=\"inline\"><mfrac><mrow><mi>D</mi><mi>E</mi></mrow><mrow><mi>G</mi><mi>H</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&#8710;</mo><mi>D</mi><mi>E</mi><mi>F</mi><mo>&#160;</mo><mi>&#2325;&#2366;</mi><mo>&#160;</mo><mi>&#2346;&#2352;&#2367;&#2350;&#2366;&#2346;</mi></mrow><mrow><mo>&#8710;</mo><mi>G</mi><mi>H</mi><mi>I</mi><mo>&#160;</mo><mi>&#2325;&#2366;</mi><mo>&#160;</mo><mi>&#2346;&#2352;&#2367;&#2350;&#2366;&#2346;</mi></mrow></mfrac></math> <br><math display=\"inline\"><mfrac><mrow><mn>64</mn></mrow><mrow><mn>24</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>X</mi><mn>72</mn></mfrac></math> &rArr; X = 192 सेमी<br>अतः, अभीष्ट योग (EF + FD) = 192 - 64 = 128 सेमी</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "58",
                    section: "17",
                    question_en: "<p>58. For the equations ax + (a<sup>2</sup> + 1)y = 4 and 4x + ay = a<sup>2</sup> , which of the following statements is TRUE ?</p>",
                    question_hi: "<p>58. समीकरण ax + (a<sup>2</sup> + 1)y = 4 and 4x + ay = a<sup>2</sup> ,के लिए, निम्नलिखित में से कौन-सा कथन सत्य है ?</p>",
                    options_en: [
                        "<p>If a = 6, then x = <math display=\"inline\"><mfrac><mrow><mn>325</mn></mrow><mrow><mn>28</mn></mrow></mfrac></math> , y = - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>25</mn><mn>28</mn></mfrac></math> are the solutions.</p>",
                        "<p>If a = 6, then x = 48, y = 4 are the solutions.</p>",
                        "<p>If a = -12, then x = <math display=\"inline\"><mfrac><mrow><mn>325</mn></mrow><mrow><mn>28</mn></mrow></mfrac></math>, y = - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>25</mn><mn>28</mn></mfrac></math> are the solutions.</p>",
                        "<p>If a = -12, then x = 48, y = 4 are the solutions</p>"
                    ],
                    options_hi: [
                        "<p>यदि a = 6 है , तो x = <math display=\"inline\"><mfrac><mrow><mn>325</mn></mrow><mrow><mn>28</mn></mrow></mfrac></math> , y = - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>25</mn><mn>28</mn></mfrac></math> हल हैं।</p>",
                        "<p>यदि a = 6 है , तो x = 48, y = 4 हल हैं।</p>",
                        "<p>यदि a = -12 है , तो x = <math display=\"inline\"><mfrac><mrow><mn>325</mn></mrow><mrow><mn>28</mn></mrow></mfrac></math>, y = - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>25</mn><mn>28</mn></mfrac></math> हल हैं।</p>",
                        "<p>यदि a = -12 है , तो x = 48, y = 4 हल हैं।</p>"
                    ],
                    solution_en: "<p>58.(d) given, ax + (a<sup>2</sup> + 1)y = 4 and 4x + ay = a<sup>2</sup><br>After checking all options one by one, only option (d) satisfies,<br>a = -12, then x = 48, y = 4<br>&rArr; ax + (a<sup>2</sup> + 1)y = 4<br>LHS = ax + (a<sup>2</sup> + 1)y<br>= -12 &times; 48 + [(-12)<sup>2</sup> + 1]4<br>= - 576 + 145 &times; 4 <br>= - 576 + 580 = 4 = RHS<br>&rArr; 4x + ay = a<sup>2</sup><br>LHS = 4x + ay<br>= 4 &times; 48 - 12 &times; 4 = 192 - 48 = 144<br>RHS = a<sup>2</sup> = (-12)<sup>2</sup> = 144<br>LHS = RHS</p>",
                    solution_hi: "<p>58.(d) दिया है, ax + (a<sup>2</sup> + 1)y = 4 and 4x + ay = a<sup>2</sup><br>सभी विकल्पों को एक-एक करके जांचने के बाद, केवल विकल्प (d) संतुष्ट करता है,<br>a = -12, तो x = 48, y = 4<br>&rArr; ax + (a<sup>2</sup> + 1)y = 4<br>LHS = ax + (a<sup>2</sup> + 1)y<br>= -12 &times; 48 + [(-12)<sup>2</sup> + 1]4<br>= - 576 + 145 &times; 4 <br>= - 576 + 580 = 4 = RHS<br>&rArr; 4x + ay = a<sup>2</sup><br>LHS = 4x + ay<br>= 4 &times; 48 - 12 &times; 4 = 192 - 48 = 144<br>RHS = a<sup>2</sup> = (-12)<sup>2</sup> = 144<br>LHS = RHS</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "59",
                    section: "17",
                    question_en: "<p>59. The average weight of 5 boys is increased by 9 kg when one of them, whose weight is 39 kg, is replaced by another boy. What is the weight (in kg) of the new boy ?</p>",
                    question_hi: "<p>59. 5 लड़कों का औसत वजन 9 kg बढ़ जाता है, जब उनमें से एक, जिसका वजन 39 kg है, को दूसरे लड़के से बदल दिया जाता है। नए लड़के का वजन (kg में) कितना है ?</p>",
                    options_en: [
                        " 69",
                        " 65",
                        " 70",
                        " 84"
                    ],
                    options_hi: [
                        " 69",
                        " 65",
                        " 70",
                        " 84"
                    ],
                    solution_en: "<p>59.(d)<br>Total number of boys = 5<br>Let average weight of 5 boys = A<br>&rArr; Total weight = 5A<br>Let the weight of new boy = x years<br>Given<br>5 (A + 9) = 5A - 39 + x<br>&rArr; 5A + 45 = 5A - 39 + x<br>&rArr; x = 84 years</p>",
                    solution_hi: "<p>59.(d)<br>लड़कों की कुल संख्या = 5<br>माना , 5 लड़कों का औसत वजन = A<br>&rArr; कुल वजन = 5A<br>माना , नये लड़के का वजन= <math display=\"inline\"><mi>x</mi></math> वर्ष<br>प्रश्न अनुसार <br>5 (A + 9) = 5A - 39 + x<br>&rArr; 5 A + 45 = 5A - 39 + x<br>&rArr; x = 84 वर्ष</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "60",
                    section: "17",
                    question_en: "<p>60. A thief is spotted by a policeman from a distance of 100 m. The thief starts running and the policeman chases him. If the speed of thief and policeman are 21 km/h and 23 km/h, respectively, then how far will the thief have to run before he is over taken ?</p>",
                    question_hi: "<p>60. एक पुलिसकर्मी 100 m की दूरी से एक चोर को देखता है। चोर भागने लगता है और पुलिसकर्मी उसका पीछा करता है। यदि चोर और पुलिसकर्मी की चाल क्रमशः 21 km/h और 23 km/h है, तो पकड़े जाने से पहले चोर कितनी दूर तक भाग पाएगा ?</p>",
                    options_en: [
                        "<p>1020 m</p>",
                        "<p>1050 m</p>",
                        "<p>1090 m</p>",
                        "<p>1080 m</p>"
                    ],
                    options_hi: [
                        "<p>1020 m</p>",
                        "<p>1050 m</p>",
                        "<p>1090 m</p>",
                        "<p>1080 m</p>"
                    ],
                    solution_en: "<p>60.(b)<br>Let time taken by policeman to catch the thief = t hr<br>Now , Distance travel by thief = 21t <br>Distance travel by policemen = 23t<br>According to question , <br>Distance travel by policeman = distance traveled by thief + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>100</mn><mn>1000</mn></mfrac></math><br>&rArr; 23t = 21t + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>10</mn></mfrac></math><br>&rArr; t = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>20</mn></mfrac></math>hr <br>Distance travel by thief = 21 &times; <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>20</mn></mrow></mfrac></math> &times; 1000 = 1050 m</p>",
                    solution_hi: "<p>60.(b)<br>माना चोर को पकड़ने में पुलिसकर्मी द्वारा लिया गया समय = t घंटा<br>अब, चोर द्वारा तय की गई दूरी = 21t <br>पुलिसकर्मियों द्वारा दूरी यात्रा = 23t<br>प्रश्न के अनुसार, <br>पुलिसकर्मी द्वारा तय की गई दूरी = चोर द्वारा तय की गई दूरी + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>100</mn><mn>1000</mn></mfrac></math><br>&rArr; 23t = 21t + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>10</mn></mfrac></math><br>&rArr; t = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>20</mn></mfrac></math>hr<br>चोर द्वारा तय की गई दूरी = 21 &times; <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>20</mn></mrow></mfrac></math> &times; 1000 = 1050 m</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "61",
                    section: "17",
                    question_en: "<p>61. Pipe X can fill a tank in 60 hours while pipe Y can fill the tank in 72 hours. Both pipes are opened together for 20 hours. How much of the tank is left empty ?</p>",
                    question_hi: "<p>61. पाइप X एक टंकी को 60 घंटे में भर सकती है जबकि पाइप Y उस टंकी को 72 घंटे में भर सकता है। दोनों पाइपों को एक साथ 20 घंटे के लिए खोला जाता है। टंकी का कितना भाग खाली बचेगा ?</p>",
                    options_en: [
                        "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>243</mn></mrow><mrow><mn>360</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>7</mn></mrow><mrow><mn>18</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math></p>"
                    ],
                    options_hi: [
                        "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>243</mn></mrow><mrow><mn>360</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>7</mn></mrow><mrow><mn>18</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math></p>"
                    ],
                    solution_en: "<p>61.(c) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744687617921.png\" alt=\"rId58\" width=\"177\" height=\"132\"><br>Tank fill in 20hr by both the pipe = (6 + 5) &times; 20 = 220<br>Part of tank is left empty = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>360</mn><mo>-</mo><mn>220</mn></mrow><mn>360</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>140</mn><mn>360</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>18</mn></mfrac></math></p>",
                    solution_hi: "<p>61.(c) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744687623489.png\" alt=\"rId59\" width=\"166\" height=\"140\"><br>दोनों पाइपों द्वारा 20 घंटे में टंकी भरना = (6 + 5) &times; 20 = 220<br>जितनी टंकी का भाग खाली छोड़ा गया = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>360</mn><mo>-</mo><mn>220</mn></mrow><mn>360</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>140</mn><mn>360</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>18</mn></mfrac></math></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "62",
                    section: "17",
                    question_en: "<p>62. As part of the partial work from home process, Amita went to office every 2nd day, Amrita went to office every 3rd day, and Tosita went to office every 6th day, respectively. All three went to office on 31 October 2021. On how many days did all three attend office on the same date during the remaining two months of the year ?</p>",
                    question_hi: "<p>62. आंशिक रूप से वर्क फ्रॉम होम प्रक्रिया के एक भाग के रूप में, क्रमशः अमिता हर दूसरे दिन ऑफिस जाती थी, अमृता हर तीसरे दिन ऑफिस जाती थी, और तोसिता हर छठे दिन ऑफिस जाती थी। 31 अक्टूबर 2021 को तीनों ऑफिस गईं। वर्ष के शेष दो महीनों के दौरान ऐसे कितने दिन थे जिनमें तीनों एक ही तिथि को ऑफिस में उपस्थित हुईं ?</p>",
                    options_en: [
                        "<p>9</p>",
                        "<p>10</p>",
                        "<p>11</p>",
                        "<p>12</p>"
                    ],
                    options_hi: [
                        "<p>9</p>",
                        "<p>10</p>",
                        "<p>11</p>",
                        "<p>12</p>"
                    ],
                    solution_en: "<p>62.(b)<br>Amita , Amrita and Tosita went office on 2nd , 3rd and 6th day respectively<br>LCM (2 , 3 , 6) = 6<br>So, on every 6th day all three attend the office<br>Now, total days in last two months = 30 + 31 = 61 days<br>Hence, no. of times they attend together the office in last two months = <math display=\"inline\"><mfrac><mrow><mn>61</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math> = 10 times<br>(only integer value considered)</p>",
                    solution_hi: "<p>62.(b)<br>अमिता, अमृता और तोसिता क्रमशः दूसरे, तीसरे और छठे दिन कार्यालय जाती है <br>LCM (2 , 3 , 6) = 6<br>अतः, प्रत्येक छठे दिन तीनों कार्यालय में उपस्थित होते हैं<br>अब, बचे दो महीनों में कुल दिन = 30 + 31 = 61 दिन <br>इसलिए, पिछले दो महीनों में उनके कार्यालय में एक साथ उपस्थित होने की संख्या = <math display=\"inline\"><mfrac><mrow><mn>61</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math> = 10बार<br>(केवल पूर्णांक मान पर विचार करें)</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "63",
                    section: "17",
                    question_en: "<p>63. Simplify :<br>[(7<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>4</mn></mfrac></math>)<sup>2</sup> - (5<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>5</mn></mfrac></math>)<sup>2</sup>] &divide; 5<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>5</mn></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>16</mn><mn>9</mn></mfrac></math></p>",
                    question_hi: "<p>63. निम्नलिखित को सरल कीजिए :<br>[(7<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>4</mn></mfrac></math>)<sup>2</sup> - (5<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>5</mn></mfrac></math>)<sup>2</sup>] &divide; 5<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>5</mn></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>16</mn><mn>9</mn></mfrac></math></p>",
                    options_en: [
                        "<p>9.3</p>",
                        "<p>3.1</p>",
                        "<p>5.8</p>",
                        "<p>7.4</p>"
                    ],
                    options_hi: [
                        "<p>9.3</p>",
                        "<p>3.1</p>",
                        "<p>5.8</p>",
                        "<p>7.4</p>"
                    ],
                    solution_en: "<p>63.(c)<br>[(7<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>4</mn></mfrac></math>)<sup>2</sup> - (5<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>5</mn></mfrac></math>)<sup>2</sup>] &divide; 5<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>5</mn></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>16</mn><mn>9</mn></mfrac></math></p>\n<p>= [(<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>29</mn><mn>4</mn></mfrac></math>)<sup>2</sup> - (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>29</mn><mn>5</mn></mfrac></math>)<sup>2</sup>] &divide; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>29</mn><mn>5</mn></mfrac></math>&nbsp;&times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>16</mn><mn>9</mn></mfrac></math></p>\n<p>= [(20)<sup>2 </sup>(<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>16</mn></mfrac></math> -&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>25</mn></mfrac></math>)] &divide; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>29</mn><mn>5</mn></mfrac></math>&nbsp;&times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>16</mn><mn>9</mn></mfrac></math></p>\n<p>= [(29)<sup>2 </sup>(0.0625 - 0.04)] &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>29</mn></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>16</mn><mn>9</mn></mfrac></math></p>\n<p>= [(29)<sup>2 </sup>(0.0225)] &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>29</mn></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>16</mn><mn>9</mn></mfrac></math></p>\n<p>= [(29) &times; <sup>&nbsp;</sup>(0.0225)] &times; 5 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>16</mn><mn>9</mn></mfrac></math></p>\n<p>= 29 &times; (0.0025) &times; 5 &times; 16<strong id=\"docs-internal-guid-5b2c17ac-7fff-a0aa-26d6-8dd090177885\">&nbsp; </strong><br>= 29 &times; 0.2 = 5.8</p>",
                    solution_hi: "<p>63.(c)<br>[(7<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>4</mn></mfrac></math>)<sup>2</sup> - (5<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>5</mn></mfrac></math>)<sup>2</sup>] &divide; 5<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>5</mn></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>16</mn><mn>9</mn></mfrac></math></p>\n<p>= [(<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>29</mn><mn>4</mn></mfrac></math>)<sup>2</sup> - (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>29</mn><mn>5</mn></mfrac></math>)<sup>2</sup>] &divide; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>29</mn><mn>5</mn></mfrac></math>&nbsp;&times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>16</mn><mn>9</mn></mfrac></math></p>\n<p>= [(20)<sup>2 </sup>(<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>16</mn></mfrac></math> -&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>25</mn></mfrac></math>)] &divide; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>29</mn><mn>5</mn></mfrac></math>&nbsp;&times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>16</mn><mn>9</mn></mfrac></math></p>\n<p>= [(29)<sup>2 </sup>(0.0625 - 0.04)] &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>29</mn></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>16</mn><mn>9</mn></mfrac></math></p>\n<p>= [(29)<sup>2 </sup>(0.0225)] &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>29</mn></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>16</mn><mn>9</mn></mfrac></math></p>\n<p>= [(29) &times; <sup>&nbsp;</sup>(0.0225)] &times; 5 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>16</mn><mn>9</mn></mfrac></math></p>\n<p>= 29 &times; (0.0025) &times; 5 &times; 16<strong id=\"docs-internal-guid-5b2c17ac-7fff-a0aa-26d6-8dd090177885\">&nbsp; </strong><br>= 29 &times; 0.2 = 5.8</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "64",
                    section: "17",
                    question_en: "<p>64. How many years will it take for ₹8,400 to amount to ₹11,928 at a simple interest rate of 7% per annum ?</p>",
                    question_hi: "<p>64. ₹8,400 की राशि को साधारण ब्याज की 7% वार्षिक दर पर ₹11,928 होने में कितने वर्ष लगेंगे ?</p>",
                    options_en: [
                        "<p>8</p>",
                        "<p>5</p>",
                        "<p>2</p>",
                        "<p>6</p>"
                    ],
                    options_hi: [
                        "<p>8</p>",
                        "<p>5</p>",
                        "<p>2</p>",
                        "<p>6</p>"
                    ],
                    solution_en: "<p>64.(d) <br>SI = 11928 - 8400 = ₹3528<br>SI = P &times; T &times; R%<br>3528 = 8400 &times; T &times; 7%<br>3528 = 588T<br>T = <math display=\"inline\"><mfrac><mrow><mn>3528</mn></mrow><mrow><mn>588</mn></mrow></mfrac></math> = 6 yrs</p>",
                    solution_hi: "<p>64.(d) <br>साधारण ब्याज = 11928 - 8400 = ₹3528<br>साधारण ब्याज = P &times; T &times; R%<br>3528 = 8400 &times; T &times; 7%<br>3528 = 588T<br>T = <math display=\"inline\"><mfrac><mrow><mn>3528</mn></mrow><mrow><mn>588</mn></mrow></mfrac></math> = 6 वर्ष</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "65",
                    section: "17",
                    question_en: "<p>65. A trader buys 500 kg cotton for ₹8,000. If 10% of this cotton is spoiled due to rain, at what rate (₹/kg) should he sell the rest to earn a 20% profit ?</p>",
                    question_hi: "<p>65. एक व्यापारी ₹8,000 में 500 kg कपास खरीदता है। यदि बारिश के कारण इस कपास का 10% खराब हो जाता है, तो 20% लाभ अर्जित करने के लिए उसे शेष कपास को किस दर (₹/kg) पर बेचना चाहिए ?</p>",
                    options_en: [
                        "<p>21<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>",
                        "<p>23<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>",
                        "<p>22<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>",
                        "<p>25<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>"
                    ],
                    options_hi: [
                        "<p>21<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>",
                        "<p>23<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>",
                        "<p>22<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>",
                        "<p>25<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>"
                    ],
                    solution_en: "<p>65.(a)<br>According to the question,<br>10% of cotton spoiled due to rain so,<br>Remaining cotton to sell = 500 &times; <math display=\"inline\"><mfrac><mrow><mn>9</mn></mrow><mrow><mn>10</mn></mrow></mfrac></math> = 450 kg<br>For profit of 20%<br>Selling price of cotton = 8000 &times; 120% = Rs. 9600<br>Selling price per kg = <math display=\"inline\"><mfrac><mrow><mn>9600</mn></mrow><mrow><mn>450</mn></mrow></mfrac></math> = Rs. 21<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> /kg</p>",
                    solution_hi: "<p>65.(a)<br>प्रश्न के अनुसार,<br>बारिश के कारण 10 फीसदी कपास खराब हो गई तो,<br>बेचने के लिए शेष कपास = 500 &times; <math display=\"inline\"><mfrac><mrow><mn>9</mn></mrow><mrow><mn>10</mn></mrow></mfrac></math> = 450 किग्रा<br>20% के लाभ के लिए<br>कपास का विक्रय मूल्य = 8000 &times; 120% = Rs. 9600<br>प्रति किलो विक्रय मूल्य = <math display=\"inline\"><mfrac><mrow><mn>9600</mn></mrow><mrow><mn>450</mn></mrow></mfrac></math> = Rs. 21<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> /किग्रा</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "66",
                    section: "17",
                    question_en: "<p>66. Consider a triangle PQR, right angled at R, in which PQ = 29 units, QR = 21 units and &ang;PQR = &theta;. Find the value of cos<sup>2</sup>&theta; - sin<sup>2</sup>&theta;.</p>",
                    question_hi: "<p>66. उस त्रिभुज PQR पर विचार कीजिए, जो R पर समकोण है, जिसमें PQ = 29 इकाई है, QR = 21 इकाई और &ang;PQR = &theta; है। cos<sup>2</sup>&theta; - sin<sup>2</sup>&theta; का मान ज्ञात कीजिए।</p>",
                    options_en: [
                        "<p><math display=\"inline\"><mfrac><mrow><mn>21</mn></mrow><mrow><mn>841</mn></mrow></mfrac></math></p>",
                        "<p>1</p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>20</mn></mrow><mrow><mn>841</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>41</mn></mrow><mrow><mn>841</mn></mrow></mfrac></math></p>"
                    ],
                    options_hi: [
                        "<p><math display=\"inline\"><mfrac><mrow><mn>21</mn></mrow><mrow><mn>841</mn></mrow></mfrac></math></p>",
                        "<p>1</p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>20</mn></mrow><mrow><mn>841</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>41</mn></mrow><mrow><mn>841</mn></mrow></mfrac></math></p>"
                    ],
                    solution_en: "<p>66.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744687624617.png\" alt=\"rId60\" width=\"166\" height=\"161\"><br>PR = <math display=\"inline\"><msqrt><mn>2</mn><msup><mrow><mn>9</mn></mrow><mrow><mn>2</mn></mrow></msup><mo>-</mo><msup><mrow><mn>21</mn></mrow><mrow><mn>2</mn></mrow></msup></msqrt></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>400</mn></msqrt></math> = 20 units<br>cos&theta; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>21</mn><mn>29</mn></mfrac></math> , sin&theta; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>20</mn><mn>29</mn></mfrac></math><br>Then, cos<sup>2</sup>&theta; - sin<sup>2</sup>&theta; <br>= (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>21</mn><mn>29</mn></mfrac></math>)<sup>2</sup> - (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>20</mn><mn>29</mn></mfrac></math>)<sup>2</sup> <br>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>441</mn><mo>-</mo><mn>400</mn></mrow><mn>841</mn></mfrac></math> = <math display=\"inline\"><mfrac><mrow><mn>41</mn></mrow><mrow><mn>841</mn></mrow></mfrac></math></p>",
                    solution_hi: "<p>66.(d) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744687624617.png\" alt=\"rId60\" width=\"166\" height=\"161\"><br>PR = <math display=\"inline\"><msqrt><mn>2</mn><msup><mrow><mn>9</mn></mrow><mrow><mn>2</mn></mrow></msup><mo>-</mo><msup><mrow><mn>21</mn></mrow><mrow><mn>2</mn></mrow></msup></msqrt></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>400</mn></msqrt></math> = 20 इकाई<br>cos&theta; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>21</mn><mn>29</mn></mfrac></math> , sin&theta; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>20</mn><mn>29</mn></mfrac></math><br>तब, cos<sup>2</sup>&theta; - sin<sup>2</sup>&theta; <br>= (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>21</mn><mn>29</mn></mfrac></math>)<sup>2</sup> - (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>20</mn><mn>29</mn></mfrac></math>)<sup>2</sup> <br>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>441</mn><mo>-</mo><mn>400</mn></mrow><mn>841</mn></mfrac></math> = <math display=\"inline\"><mfrac><mrow><mn>41</mn></mrow><mrow><mn>841</mn></mrow></mfrac></math></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "67",
                    section: "17",
                    question_en: "<p>67. The length, breadth and height of a room is 15 m, 9 m, and 5 m, respectively. From each can of paint 40 square metre of area is painted. How many cans of paint will be needed to paint only the walls of the room ?</p>",
                    question_hi: "<p>67. एक कमरे की लंबाई, चौड़ाई और ऊँचाई क्रमशः 15 m, 9 m और 5 m है। पेंट के एक डिब्बे से 40 वर्ग-मीटर क्षेत्रफल को पेंट किया जा सकता है। केवल कमरे की दीवारों को पेंट करने के लिए पेंट के कितने डिब्बों की आवश्यकता होगी ?</p>",
                    options_en: [
                        "<p>12</p>",
                        "<p>6</p>",
                        "<p>4</p>",
                        "<p>8</p>"
                    ],
                    options_hi: [
                        "<p>12</p>",
                        "<p>6</p>",
                        "<p>4</p>",
                        "<p>8</p>"
                    ],
                    solution_en: "<p dir=\"ltr\">67.(b) Surface area of four walls = 2h(l + b)<br>= 2 &times; 5(15 + 9)<br>= 10(24) =&nbsp; 240 m<sup>2</sup><br>So,<br>The number of cans = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>240</mn><mn>40</mn></mfrac></math> = 6 </p>",
                    solution_hi: "<p dir=\"ltr\">67.(b) चारों दीवारों का पृष्ठीय क्षेत्रफल = 2h(l + b)<br>= 2 &times; 5(15 + 9)<br>= 10(24) =&nbsp; 240 m<sup>2</sup><br>इसलिए , डिब्बों की संख्या = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>240</mn><mn>40</mn></mfrac></math> = 6</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "68",
                    section: "17",
                    question_en: "<p>68. Study the given bar-graph and answer the question that follows. <br>The bar-graph shows the sales of books (in thousands) from six branches (B1, B2, B3, B4, B5 and B6) of a publishing company during two consecutive years 2000 and 2001.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744687626046.png\" alt=\"rId62\" width=\"346\" height=\"268\"> <br>The total sales of branch B6 for both the years is what percentage of the total sales of branch B3 for both the years (correct up to two decimal places) ?</p>",
                    question_hi: "<p>68. दिए गए दंड-आलेख का अध्ययन कीजिए और नीचे दिए गए प्रश्न का उत्तर दीजिए।<br>दंड-आलेख दो क्रमिक वर्षों 2000 और 2001 के दौरान एक प्रकाशन कंपनी की छह शाखाओं (B1, B2, B3, B4, B5 और B6) से पुस्तकों की बिक्री (हजार में) दर्शाता है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744687626287.png\" alt=\"rId63\" width=\"354\" height=\"274\"> <br>दोनों वर्षों के लिए शाखा B6 की कुल बिक्री, दोनों वर्षों के लिए शाखा B3 की कुल बिक्री का कितने प्रतिशत है (दो दशमलव स्थान तक पूर्णांकित) ?</p>",
                    options_en: [
                        "<p>70.69%</p>",
                        "<p>73.17%</p>",
                        "<p>69.25%</p>",
                        "<p>82.26%</p>"
                    ],
                    options_hi: [
                        "<p>70.69%</p>",
                        "<p>73.17%</p>",
                        "<p>69.25%</p>",
                        "<p>82.26%</p>"
                    ],
                    solution_en: "<p>68.(b)<br>Total sales of branch B6 for both years = 70 + 80 = 150<br>Total sales of branch B3 for both years = 95 + 110 = 205<br>Required percentage = <math display=\"inline\"><mfrac><mrow><mn>150</mn></mrow><mrow><mn>205</mn></mrow></mfrac></math> &times; 100 = 73.17%</p>",
                    solution_hi: "<p>68.(b)<br>दोनो वर्षों के लिए शाखा B6 की कुल बिक्री = 70 + 80 = 150<br>दोनो वर्षों के लिए शाखा B3 की कुल बिक्री = 95 + 110 = 205<br>आवश्यक प्रतिशत = <math display=\"inline\"><mfrac><mrow><mn>150</mn></mrow><mrow><mn>205</mn></mrow></mfrac></math> &times; 100 = 73.17%</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "69",
                    section: "17",
                    question_en: "<p>69. A car with a price of ₹6,50,000 is bought by making some down payment. On the balance, a simple interest of 10% is charged in lump sum and the money is to be paid in 20 equal annual installments of ₹25,000. How much is the down payment ?</p>",
                    question_hi: "<p>69. ₹6,50,000 की कीमत वाली कार कुछ डाउन पेमेंट (तत्काल भुगतान) करके खरीदी जाती है। शेष राशि पर, एकमुश्त 10% का साधारण ब्याज लगाया जाता है और राशि का भुगतान ₹25,000 की 20 समान वार्षिक किस्तों में किया जाना है। डाउन पेमेंट (तत्काल भुगतान) की राशि कितनी है ?</p>",
                    options_en: [
                        "<p>₹ 1,55,945</p>",
                        "<p>₹ 1,95,455</p>",
                        "<p>₹ 1,94,555</p>",
                        "<p>₹ 1,45,955</p>"
                    ],
                    options_hi: [
                        "<p>₹ 1,55,945</p>",
                        "<p>₹ 1,95,455</p>",
                        "<p>₹ 1,94,555</p>",
                        "<p>₹ 1,45,955</p>"
                    ],
                    solution_en: "<p>69.(b) Sum of all installment <br>= 25000 &times; 20 = Rs. 500000 <br>According to question ,<br>Rate = 10% <br>&rArr;&nbsp;initial : final <br>&nbsp; &nbsp; &nbsp; 10&nbsp; &nbsp;:&nbsp; 11<br>According to question,<br>11 units = Rs. 500000 <br>Then , 10 units <math display=\"inline\"><mi>&#65374;</mi></math> Rs. 454545 <br>Value of Down payment <br>= 650000 -&nbsp;454545 = Rs. 195455</p>",
                    solution_hi: "<p>69.(b) सभी किस्तों का योग <br>= 25000 &times; 20 = 500000 रूपये <br>प्रश्न के अनुसार, दर = 10% <br>प्रारंभिक : अंतिम <br>&nbsp; &nbsp;10&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; 11<br>प्रश्न के अनुसार, 11 इकाई = 500000 रूपये <br>फिर , 10 इकाई <math display=\"inline\"><mi>&#65374;</mi></math> 454545 रूपये <br>डाउन पेमेंट (तत्काल भुगतान) की राशि <br>= 650000 -&nbsp;454545 = 195455 रूपये</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "70",
                    section: "17",
                    question_en: "<p>70. Let x = r cos (t), y = r sin (t) cos (u), z = r sin (t)sin (u). Then the value of x<sup>2</sup> + y<sup>2</sup> + z<sup>2</sup> is ______.</p>",
                    question_hi: "<p>70. मान लें कि x = r cos (t), y = r sin (t) cos (u), z = r sin (t)sin (u) है। तो x<sup>2</sup> + y<sup>2</sup> + z<sup>2</sup> का मान ज्ञात करें।</p>",
                    options_en: [
                        "<p>r<sup>2</sup> cos (u)</p>",
                        "<p>r<sup>2</sup></p>",
                        "<p>2r</p>",
                        "<p>r<sup>2</sup> sin (t)</p>"
                    ],
                    options_hi: [
                        "<p>r<sup>2</sup> cos (u)</p>",
                        "<p>r<sup>2</sup></p>",
                        "<p>2r</p>",
                        "<p>r<sup>2</sup> sin (t)</p>"
                    ],
                    solution_en: "<p>70.(b) According to question,<br>x<sup>2</sup> + y<sup>2</sup> + z<sup>2</sup><br>= (r cos t)<sup>2</sup> + (r sin t.cos u)<sup>2</sup> + (r sin t.sin u)<sup>2</sup><br>= r<sup>2</sup>cos<sup>2</sup>t + r<sup>2</sup>cos<sup>2</sup>u.sin<sup>2</sup>t + r<sup>2</sup>sin<sup>2</sup>t.sin<sup>2</sup>u&nbsp;<br>= r<sup>2</sup>cos<sup>2</sup>t + r<sup>2</sup>sin<sup>2</sup>t(cos<sup>2</sup>u + sin<sup>2</sup>u)<br>= r2(cos<sup>2</sup>t + sin<sup>2</sup>t) <br>= r<sup>2</sup></p>",
                    solution_hi: "<p>70.(b) प्रश्न के अनुसार,<br>x<sup>2</sup> + y<sup>2</sup> + z<sup>2</sup><br>= (r cos t)<sup>2</sup> + (r sin t.cos u)<sup>2</sup> + (r sin t.sin u)<sup>2</sup><br>= r<sup>2</sup>cos<sup>2</sup>t + r<sup>2</sup>cos<sup>2</sup>u.sin<sup>2</sup>t + r<sup>2</sup>sin<sup>2</sup>t.sin<sup>2</sup>u&nbsp;<br>= r<sup>2</sup>cos<sup>2</sup>t + r<sup>2</sup>sin<sup>2</sup>t(cos<sup>2</sup>u + sin<sup>2</sup>u)<br>= r2(cos<sup>2</sup>t + sin<sup>2</sup>t) <br>= r<sup>2</sup></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "71",
                    section: "17",
                    question_en: "<p>71. A and B together can complete a piece of work in 25 days, B and C together can complete the same piece of work in 36 days, while C and A together can complete it in 30 days. If A, B, C, and D together can complete this piece of work in 18 days, then in how many days can D alone complete this piece of work ?</p>",
                    question_hi: "<p>71. A और B मिलकर किसी कार्य को 25 दिनों में पूरा कर सकते हैं, B और C मिलकर उसी कार्य को 36 दिनों में पूरा कर सकते हैं, जबकि C और A मिलकर उसी कार्य को 30 दिनों में पूरा कर सकते हैं। यदि A, B, C और D मिलकर इस कार्य को 18 दिनों में पूरा कर सकते हैं, तो D अकेला इस कार्य को कितने दिनों में पूरा कर सकता है ?</p>",
                    options_en: [
                        "<p>225</p>",
                        "<p>210</p>",
                        "<p>200</p>",
                        "<p>180</p>"
                    ],
                    options_hi: [
                        "<p>225</p>",
                        "<p>210</p>",
                        "<p>200</p>",
                        "<p>180</p>"
                    ],
                    solution_en: "<p>71.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744687627125.png\" alt=\"rId64\" width=\"294\" height=\"120\"><br>Efficiency of A + B + C = <math display=\"inline\"><mfrac><mrow><mn>91</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = 45.5 unit<br>Efficiency of A + B + C + D = 50<br>Efficiency of D = 50 - 45.5 = 4.5 unit<br>Time taken by D alone = <math display=\"inline\"><mfrac><mrow><mn>900</mn></mrow><mrow><mn>4</mn><mo>.</mo><mn>5</mn></mrow></mfrac></math> = 200 days</p>",
                    solution_hi: "<p>71.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744687629480.png\" alt=\"rId65\" width=\"283\" height=\"124\"><br>A + B + C की दक्षता = <math display=\"inline\"><mfrac><mrow><mn>91</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = 45.5 इकाई<br>A + B + C + D की दक्षता = 50<br>D की दक्षता = 50 - 45.5 = 4.5 इकाई<br>अकेले D द्वारा लिया गया समय = <math display=\"inline\"><mfrac><mrow><mn>900</mn></mrow><mrow><mn>4</mn><mo>.</mo><mn>5</mn></mrow></mfrac></math> = 200 दिन</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "72",
                    section: "17",
                    question_en: "<p>72. A person purchased a table and a sofa for ₹28,000. He sold the sofa at a profit of 10% and the table at a profit 15.25%. If his total profit was 13%, then the difference between the cost price of the sofa and the table (in ₹) is:</p>",
                    question_hi: "<p>72. एक व्यक्ति ने ₹28,000 में एक मेज और एक सोफा खरीदा। उसने सोफे को 10% के लाभ पर और मेज को 15.25% के लाभ पर बेचा। यदि उसका कुल लाभ 13% था, तो सोफे और मेज के क्रय मूल्य के बीच का अंतर (₹ में) ज्ञात करें।</p>",
                    options_en: [
                        "<p>1500</p>",
                        "<p>4000</p>",
                        "<p>5000</p>",
                        "<p>2500</p>"
                    ],
                    options_hi: [
                        "<p>1500</p>",
                        "<p>4000</p>",
                        "<p>5000</p>",
                        "<p>2500</p>"
                    ],
                    solution_en: "<p>72.(b) <br>By using Alligation method <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744687629665.png\" alt=\"rId66\" width=\"193\" height=\"156\"><br>According to question,<br>(0.75 + 1) unit = ₹28,000<br>1 unit = <math display=\"inline\"><mfrac><mrow><mn>28000</mn></mrow><mrow><mn>1</mn><mo>.</mo><mn>75</mn></mrow></mfrac></math><br>Then required difference,<br>0.25unit = <math display=\"inline\"><mfrac><mrow><mn>28000</mn></mrow><mrow><mn>1</mn><mo>.</mo><mn>75</mn></mrow></mfrac></math> &times; 0.25 = ₹4000</p>",
                    solution_hi: "<p>72.(b) <br>एलीगेशन विधि का प्रयोग करके <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744687629885.png\" alt=\"rId67\" width=\"196\" height=\"158\"><br>प्रश्न के अनुसार <br>(0.75 + 1) इकाई = ₹28,000<br>1 इकाई = <math display=\"inline\"><mfrac><mrow><mn>28000</mn></mrow><mrow><mn>1</mn><mo>.</mo><mn>75</mn></mrow></mfrac></math><br>तब अभीष्ट अंतर <br>0.25 इकाई =&nbsp;<math display=\"inline\"><mfrac><mrow><mn>28000</mn></mrow><mrow><mn>1</mn><mo>.</mo><mn>75</mn></mrow></mfrac></math> &times; 0.25 = ₹4000</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "73",
                    section: "17",
                    question_en: "<p>73. An item costs Rs 15,000. A customer can choose either a 30% discount or buy two get one free offer. Jeevan chose the first offer and Rakshit chose the second offer. Find the difference in their payment for a single item.</p>",
                    question_hi: "<p>73. एक वस्तु की कीमत 15,000 रुपये है। ग्राहक या तो 30% छूट चुन सकता है या दो खरीदने पर एक मुफ्त ऑफर चुन सकता है। जीवन ने पहला ऑफर चुना और रक्षित ने दूसरा ऑफर चुना। किसी एक वस्तु के लिए उनके भुगतान में अंतर ज्ञात कीजिए।</p>",
                    options_en: [
                        "<p>Rs.500</p>",
                        "<p>Rs.10,500</p>",
                        "<p>Rs.750</p>",
                        "<p>Rs.10,000</p>"
                    ],
                    options_hi: [
                        "<p>₹ 500</p>",
                        "<p>₹ 10,500</p>",
                        "<p>₹ 750</p>",
                        "<p>₹ 10,000</p>"
                    ],
                    solution_en: "<p>73.(d) <br>Amount of trade discount = 25000 &times; 6 &times; <math display=\"inline\"><mfrac><mrow><mn>30</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = ₹ 45000</p>",
                    solution_hi: "<p>73.(d) <br>व्यापार छूट की राशि = 25000 &times; 6 &times; <math display=\"inline\"><mfrac><mrow><mn>30</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = ₹ 45000</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "74",
                    section: "17",
                    question_en: "<p>74. Find the average of natural numbers from 1 to 69. (both included)</p>",
                    question_hi: "<p>74. 1 से 69 तक की प्राकृत संख्याओं का औसत ज्ञात कीजिए। (दोनों का मिलाकर)</p>",
                    options_en: [
                        "<p>37</p>",
                        "<p>35</p>",
                        "<p>33</p>",
                        "<p>31</p>"
                    ],
                    options_hi: [
                        "<p>37</p>",
                        "<p>35</p>",
                        "<p>33</p>",
                        "<p>31</p>"
                    ],
                    solution_en: "<p>74.(b)<br>Average of n natural number = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mi>n</mi><mo>+</mo><mn>1</mn><mo>)</mo></mrow><mn>2</mn></mfrac></math><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>69</mn><mo>+</mo><mn>1</mn><mo>)</mo></mrow><mrow><mn>2</mn><mi>&#160;</mi></mrow></mfrac></math> &rArr; <math display=\"inline\"><mfrac><mrow><mi>&#160;</mi><mn>70</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = 35</p>",
                    solution_hi: "<p>74.(b)<br>n प्राकृत संख्याओं का औसत = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mi>n</mi><mo>+</mo><mn>1</mn><mo>)</mo></mrow><mn>2</mn></mfrac></math><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>69</mn><mo>+</mo><mn>1</mn><mo>)</mo></mrow><mrow><mn>2</mn><mi>&#160;</mi></mrow></mfrac></math> &rArr; <math display=\"inline\"><mfrac><mrow><mi>&#160;</mi><mn>70</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = 35</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "75",
                    section: "7",
                    question_en: "<p>75. Ravi walks a circular track of 1680m in circumference. If the speed of Ravi is 140m/min, then the time to cover one round is equal to:</p>",
                    question_hi: "<p>75. रवि 1680 मीटर परिधि वाले एक वृत्ताकार ट्रैक पर चलता है। यदि रवि की चाल 140 मीटर/मिनट है, तो उसे एक चक्कर पूरा करने में कितना समय लगेगा ?</p>",
                    options_en: [
                        "<p>8 min</p>",
                        "<p>14 min</p>",
                        "<p>10 min</p>",
                        "<p>12 min</p>"
                    ],
                    options_hi: [
                        "<p>8 मिनट</p>",
                        "<p>14 मिनट</p>",
                        "<p>10 मिनट</p>",
                        "<p>12 मिनट</p>"
                    ],
                    solution_en: "<p>75.(d)<br>Required time taken = <math display=\"inline\"><mfrac><mrow><mn>1680</mn></mrow><mrow><mn>140</mn></mrow></mfrac></math> = 12 min.</p>",
                    solution_hi: "<p>75.(d)<br>आवश्यक लिया गया समय = <math display=\"inline\"><mfrac><mrow><mn>1680</mn></mrow><mrow><mn>140</mn></mrow></mfrac></math> = 12 मिनट</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "76",
                    section: "7",
                    question_en: "<p>76. Identify the segment in the sentence, which contains the grammatical error.<br>Water borne diseases are reducing life expectancy in the country in an average of six months.</p>",
                    question_hi: "<p>76. Identify the segment in the sentence, which contains the grammatical error.<br>Water borne diseases are reducing life expectancy in the country in an average of six months.</p>",
                    options_en: [
                        "<p>Water borne diseases</p>",
                        "<p>are reducing</p>",
                        "<p>in an average of six months.</p>",
                        "<p>life expectancy in the country</p>"
                    ],
                    options_hi: [
                        "<p>Water borne diseases</p>",
                        "<p>are reducing</p>",
                        "<p>in an average of six months.</p>",
                        "<p>life expectancy in the country</p>"
                    ],
                    solution_en: "<p>76.(c) In an average of six months<br>There is a prepositional error in the given sentence. The preposition &lsquo;in&rsquo; must be replaced with &lsquo;at&rsquo;. Hence, &lsquo;at an average&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>76.(c) In an average of six months<br>दिए गए वाक्य में prepositional त्रुटि है। preposition \'in\' को \'at\' से बदला जाना चाहिए। इसलिए, \'at an average\' सबसे उपयुक्त उत्तर है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "77",
                    section: "7",
                    question_en: "<p>77. Find a word that is synonym of :<br>Concurrence</p>",
                    question_hi: "<p>77. Find a word that is synonym of :<br>Concurrence</p>",
                    options_en: [
                        "<p>occurrence</p>",
                        "<p>conquest</p>",
                        "<p>currency</p>",
                        "<p>agreement</p>"
                    ],
                    options_hi: [
                        "<p>occurrence</p>",
                        "<p>conquest</p>",
                        "<p>currency</p>",
                        "<p>agreement</p>"
                    ],
                    solution_en: "<p>77.(d) Agreement<br>The word &lsquo;Concurrence&rsquo; means &lsquo;agreement&rsquo;</p>",
                    solution_hi: "<p>77.(d) Agreement<br>&lsquo;Concurrence&rsquo; शब्द का अर्थ है &lsquo;agreement&rsquo;</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "78",
                    section: "7",
                    question_en: "<p>78. Identify the best way to improve the underlined part of the given sentence. If there is no improvement required, select &lsquo;no improvement&rsquo;.<br>The transport workers have organized a strike <strong><span style=\"text-decoration: underline;\">asking</span> </strong>for more salary.</p>",
                    question_hi: "<p>78. Identify the best way to improve the underlined part of the given sentence. If there is no improvement required, select &lsquo;no improvement&rsquo;.<br>The transport workers have organized a strike <strong><span style=\"text-decoration: underline;\">asking</span></strong> for more salary.</p>",
                    options_en: [
                        "<p>demanding</p>",
                        "<p>begging</p>",
                        "<p>requesting</p>",
                        "<p>No improvement</p>"
                    ],
                    options_hi: [
                        "<p>demanding</p>",
                        "<p>begging</p>",
                        "<p>requesting</p>",
                        "<p>No improvement</p>"
                    ],
                    solution_en: "<p>78.(a) Demanding<br>&lsquo;Demanding&rsquo; means to call for something in an authoritative way. The given sentence states that The transport workers have organized a strike demanding for more salary. Hence, &lsquo;demanding&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>78.(a) Demanding<br>\'Demanding\' का अर्थ है आधिकारिक रूप से किसी चीज की मांग करना। दिए गए sentence में कहा गया है कि परिवहन कर्मचारियों ने अधिक वेतन की मांग को लेकर हड़ताल की है। इसलिए, \'demanding\' सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "79",
                    section: "7",
                    question_en: "<p>79. Choose the most appropriate option to change the narration (direct/indirect) of the given sentence:<br>He said, &ldquo;I must go at once&rdquo;.</p>",
                    question_hi: "<p>79. Choose the most appropriate option to change the narration (direct/indirect) of the given sentence:<br>He said, &ldquo;I must go at once&rdquo;.</p>",
                    options_en: [
                        "<p>He said that he had gone at once.</p>",
                        "<p>He said that he had to go at once.</p>",
                        "<p>He told if he had to go at once.</p>",
                        "<p>He asked to go at once.</p>"
                    ],
                    options_hi: [
                        "<p>He said that he had gone at once.</p>",
                        "<p>He said that he had to go at once.</p>",
                        "<p>He told if he had to go at once.</p>",
                        "<p>He asked to go at once.</p>"
                    ],
                    solution_en: "<p>79.(b) He said that he had to go at once.<br>(a) He said that he <strong>had gone</strong> at once. (Incorrect change of tense)<br>(c) He told <strong>if he had to go at once. </strong>(Meaning of sentence changed)<br>(d) He <strong>asked </strong>to go at once. (Incorrect Reporting Verb)</p>",
                    solution_hi: "<p>79.(b) He said that he had to go at once.<br>(a) He said that he <strong>had gone</strong> at once. (Tense का गलत परिवर्तन)<br>(c) He told <strong>if he had to go at once. </strong>(Sentence का अर्थ बदल गया)<br>(d) He <strong>asked </strong>to go at once. (ग़लत Reporting Verb)</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "80",
                    section: "7",
                    question_en: "<p>80. In the following questions, out of the four alternatives, choose the one which can be substituted for the given words/sentence. <br>Government by the wealthy.</p>",
                    question_hi: "<p>80. In the following questions, out of the four alternatives, choose the one which can be substituted for the given words/sentence. <br>Government by the wealthy.</p>",
                    options_en: [
                        "<p>Theocracy</p>",
                        "<p>Plutocracy</p>",
                        "<p>Bureaucracy</p>",
                        "<p>Aristocracy</p>"
                    ],
                    options_hi: [
                        "<p>Theocracy</p>",
                        "<p>Plutocracy</p>",
                        "<p>Bureaucracy</p>",
                        "<p>Aristocracy</p>"
                    ],
                    solution_en: "<p>80.(b) <strong>plutocracy.</strong><br><strong>Theocracy</strong> - a system of government in which priests rule in the name of God.<br><strong>Plutocracy</strong> - government by the wealthy.<br><strong>Bureaucracy</strong> - a system of government in which most of the important decisions are taken by state officials rather than by elected representatives.<br><strong>Aristocracy</strong> - the highest class in certain societies, typically comprising people of noble birth holding hereditary titles and offices.</p>",
                    solution_hi: "<p>80.(b) <strong>plutocracy</strong><br><strong>Theocracy-</strong> सरकार की एक प्रणाली जिसमें पुजारी भगवान के नाम पर शासन करते हैं।<br><strong>Plutocracy</strong> - अमीरों द्वारा संचालित सरकार।<br><strong>Bureaucracy</strong> - सरकार की एक प्रणाली जिसमें अधिकांश महत्वपूर्ण निर्णय राज्य के चुने गए प्रतिनिधियों के बजाय अधिकारियों द्वारा लिए जाते हैं।<br><strong>Aristocracy-</strong> कुछ समाजों में उच्चतम वर्ग, आम तौर पर कुलीन वर्ग में जन्में और वंशानुगत उपाधियों और अधिकारों को रखने वाले आते हैं।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "81",
                    section: "7",
                    question_en: "<p>81. Choose the most appropriate option to change the voice (active/passive) form of the given sentence.<br>A letter has been written by me.</p>",
                    question_hi: "<p>81. Choose the most appropriate option to change the voice (active/passive) form of the given sentence.<br>A letter has been written by me.</p>",
                    options_en: [
                        "<p>I have been writing a letter.</p>",
                        "<p>I has written a letter.</p>",
                        "<p>I had written a letter.</p>",
                        "<p>I have written a letter.</p>"
                    ],
                    options_hi: [
                        "<p>I have been writing a letter.</p>",
                        "<p>I has written a letter.</p>",
                        "<p>I had written a letter.</p>",
                        "<p>I have written a letter.</p>"
                    ],
                    solution_en: "<p>81.(d) I have written a letter.<br>(a) <strong>I have been writing</strong> a letter. (Tense has changed)&nbsp;<br>(b)<strong> I has</strong> written a letter.(Incorrect helping verb) <br>(c) <strong>I had</strong> written a letter.(Incorrect tense)</p>",
                    solution_hi: "<p>81.(d) I have written a letter.<br>(a) <strong>I have been writing </strong>a letter. (Tense बदल गया)<br>(b) <strong>I has</strong> written a letter.(गलत helping verb)<br>(c)<strong> I had</strong> written a letter.(गलत tense)</p>",
                    correct: "d ",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "82",
                    section: "7",
                    question_en: "<p>82. Choose the most appropriate option to change the voice (active/passive) form of the given sentence:<br>You have to do this immediately.</p>",
                    question_hi: "<p>82. Choose the most appropriate option to change the voice (active/passive) form of the given sentence:<br>You have to do this immediately.</p>",
                    options_en: [
                        "<p>This has been done by you immediately.</p>",
                        "<p>This is being done by you immediately.</p>",
                        "<p>This as being done by you immediately.</p>",
                        "<p>This has to be done by you immediately.</p>"
                    ],
                    options_hi: [
                        "<p>This has been done by you immediately.</p>",
                        "<p>This is being done by you immediately.</p>",
                        "<p>This as being done by you immediately.</p>",
                        "<p>This has to be done by you immediately.</p>"
                    ],
                    solution_en: "<p>82.(d) This has to be done by you immediately. <br>a. This has been done by you immediately. (Meaning changed)<br>b. This is being done by you immediately. (Tense changed)<br>c. This as being done by you immediately. (Incorrect structure)</p>",
                    solution_hi: "<p>82.(d) This has to be done by you immediately. <br>a. This has been done by you immediately. (Meaning बदला गया है)<br>b. This is being done by you immediately. (Tense बदला गया है)<br>c. This as being done by you immediately. (ग़लत structure)</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "83",
                    section: "7",
                    question_en: "<p>83. Identify the segment in the sentence, which contains the grammatical error.<br>Fifteen people were apprehended in the border who were trying to enter India.</p>",
                    question_hi: "<p>83. Identify the segment in the sentence, which contains the grammatical error.<br>Fifteen people were apprehended in the border who were trying to enter India.</p>",
                    options_en: [
                        "<p>fifteen people were apprehended</p>",
                        "<p>in the border</p>",
                        "<p>who were</p>",
                        "<p>trying to enter India</p>"
                    ],
                    options_hi: [
                        "<p>fifteen people were apprehended</p>",
                        "<p>in the border</p>",
                        "<p>who were</p>",
                        "<p>trying to enter India</p>"
                    ],
                    solution_en: "<p>83.(b) In the border<br>&lsquo;Border&rsquo; is not a closed area so we can&rsquo;t use the preposition &lsquo;in&rsquo; with it. We use &lsquo;in&rsquo; with&nbsp;closed areas and we use <strong>&ldquo;at&rdquo;</strong> to describe locations including firms, companies, workplaces and educational institutions. So, &lsquo;in&rsquo; will be replaced with &lsquo;at&rsquo; Hence, &lsquo;at the border&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>83.(b) In the border<br>&lsquo;Border&rsquo; एक बंद क्षेत्र नहीं है इसलिए हम इसके साथ preposition &lsquo;in&rsquo; का उपयोग नहीं कर सकते। हम बंद क्षेत्रों के साथ &lsquo;in&rsquo; का उपयोग करते हैं और हम कंपनियों, कार्यस्थलों और शैक्षणिक संस्थानों सहित स्थानों का वर्णन करने के लिए <strong>&lsquo;at&rsquo; </strong>का उपयोग करते हैं। इसलिए, \'in\' को \'at\' से बदल दिया जाएगा, इसलिए, &lsquo;at the border&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "84",
                    section: "7",
                    question_en: "<p>84. Select the most appropriate ANTONYM of the given word.<br>Shame</p>",
                    question_hi: "<p>84. Select the most appropriate ANTONYM of the given word.<br>Shame</p>",
                    options_en: [
                        "<p>Fear</p>",
                        "<p>Defamation</p>",
                        "<p>Pride</p>",
                        "<p>Disgrace</p>"
                    ],
                    options_hi: [
                        "<p>Fear</p>",
                        "<p>Defamation</p>",
                        "<p>Pride</p>",
                        "<p>Disgrace</p>"
                    ],
                    solution_en: "<p>84.(c) <strong>Pride-</strong> positive feeling of self-respect and self-worth<br><strong>Shame-</strong> a painful feeling that\'s a mix of regret, self-hate, and dishonor<br><strong>Fear-</strong> an unpleasant emotion caused by the threat of danger, pain, or harm<br><strong>Defamation-</strong> the action of damaging the good reputation of someone; slander or libel.<br><strong>Disgrace-</strong> loss of reputation or respect</p>",
                    solution_hi: "<p>84.(c) <strong>Pride</strong> (गर्व) - positive feeling of self-respect and self-worth<br><strong>Shame</strong> (शर्म) - a painful feeling that\'s a mix of regret, self-hate, and dishonor<br><strong>Fear</strong> (डर/भय) - an unpleasant emotion caused by the threat of danger, pain, or harm<br><strong>Defamation</strong> (मानहानि) - the action of damaging the good reputation of someone; slander or libel.<br><strong>Disgrace</strong> (अपमान) - loss of reputation or respect</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "85",
                    section: "7",
                    question_en: "<p>85. Parts of a sentence are given below in jumbled order. Select the option that arranges the parts in the correct sequence to form a meaningful sentence.<br>(P) technology has revolutionised<br>(Q) and it has transformed the way<br>(O) every aspect of our lives<br>(R) we communicate and connect</p>",
                    question_hi: "<p>85. Parts of a sentence are given below in jumbled order. Select the option that arranges the parts in the correct sequence to form a meaningful sentence.<br>(P) technology has revolutionised<br>(Q) and it has transformed the way<br>(O) every aspect of our lives<br>(R) we communicate and connect</p>",
                    options_en: [
                        "<p>RPQO</p>",
                        "<p>RPOQ</p>",
                        "<p>POQR</p>",
                        "<p>ORQP</p>"
                    ],
                    options_hi: [
                        "<p>RPQO</p>",
                        "<p>RPOQ</p>",
                        "<p>POQR</p>",
                        "<p>ORQP</p>"
                    ],
                    solution_en: "<p>85.(c) <strong>POQR</strong><br>The given sentence starts with Part P as it introduces the main idea of the sentence, i.e. &lsquo;technology has revolutionised&rsquo;. Part P will be followed by Part O as it contains the object of the verb(revolutionised), i.e. every aspect of our lives. Further, Part Q talks about transformation by technology and Part R states that it has transformed the way we communicate and connect. So, R will follow Q . Going through the options, option &lsquo;c&rsquo; has the correct sequence.</p>",
                    solution_hi: "<p>85.(c) <strong>POQR</strong><br>दिया गया sentence, Part P से प्रारंभ होता है क्योंकि यह sentence के मुख्य विचार, &lsquo;technology has revolutionised&rsquo; का परिचय देता है। Part P के बाद Part O आएगा क्योंकि इसमें verb (revolutionised) का object, &lsquo;every aspect of our lives&rsquo; शामिल है। इसके अलावा, Part Q, technology द्वारा परिवर्तन के बारे में बात करता है और Part R बताता है कि इसने हमारे communicate करने और connect होने के तरीके को बदल दिया है। इसलिए, Q के बाद R आएगा। अतः options के माध्यम से जाने पर, option &lsquo;c\' में sequence है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "86",
                    section: "7",
                    question_en: "<p>86. Find the correctly spelt word from the given options.</p>",
                    question_hi: "<p>86. Find the correctly spelt word from the given options.</p>",
                    options_en: [
                        "<p>sponsorship</p>",
                        "<p>soveriegn</p>",
                        "<p>subserveint</p>",
                        "<p>seplchure</p>"
                    ],
                    options_hi: [
                        "<p>sponsorship</p>",
                        "<p>soveriegn</p>",
                        "<p>subserveint</p>",
                        "<p>seplchure</p>"
                    ],
                    solution_en: "<p>86.(a) Sponsorship<br>&lsquo;Sponsorship&rsquo; is the correct spelling.</p>",
                    solution_hi: "<p>86.(a) Sponsorship<br>\'Sponsorship\' सही spelling है।</p>",
                    correct: " a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "87",
                    section: "7",
                    question_en: "<p>87. Given below are four jumbled sentences. Select the option that gives their correct logical sequence.<br>A. These can turn around to track stars with the rotation of Earth.<br>B. Observatories are places to study place.<br>C. They are mostly built on the mountain tops.<br>D. Telescopes are placed in the dome-shaped roof of the building.</p>",
                    question_hi: "<p>87. Given below are four jumbled sentences. Select the option that gives their correct logical sequence.<br>A. These can turn around to track stars with the rotation of Earth.<br>B. Observatories are places to study place.<br>C. They are mostly built on the mountain tops.<br>D. Telescopes are placed in the dome-shaped roof of the building.</p>",
                    options_en: [
                        "<p>BCDA</p>",
                        "<p>DCBA</p>",
                        "<p>ACDB</p>",
                        "<p>CBDA</p>"
                    ],
                    options_hi: [
                        "<p>BCDA</p>",
                        "<p>DCBA</p>",
                        "<p>ACDB</p>",
                        "<p>CBDA</p>"
                    ],
                    solution_en: "<p>87.(a) <strong>BCDA</strong><br>Sentence B will be the starting line as it contains the main idea of the parajumble i.e. &lsquo;Observatories are places to study place.&rsquo; And, Sentence C states that they are mostly built on the mountain tops. So, C will follow B. Further, Sentence D states that telescopes are placed in the dome-shaped roof of the building &amp; Sentence A gives the additional information about the telescopes. So, A will follow D. Going through the options, option &lsquo;a&rsquo; has the correct sequence.</p>",
                    solution_hi: "<p>87.(a) <strong>BCDA</strong><br>Sentence B प्रारंभिक line होगी क्योंकि इसमें parajumble का मुख्य विचार &lsquo;Observatories are places to study place&rsquo; शामिल है। और, Sentence C में कहा गया है कि वे ज़्यादातर पहाड़ की चोटियों पर बनाई जाती हैं। इसलिए, B के बाद C आएगा। इसके अलावा, Sentence D में कहा गया है कि दूरबीनों(telescopes) को इमारत की गुंबद के आकार की छत में रखा जाता है और Sentence A दूरबीनों के बारे में अतिरिक्त जानकारी देता है। इसलिए, D के बाद A आएगा। Options के मध्यम से जाने पर option &lsquo;a&rsquo; में सही sequence है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "88",
                    section: "7",
                    question_en: "<p>88. Select the sentence that uses the given idiom correctly.<br>Have your head in the clouds</p>",
                    question_hi: "<p>88. Select the sentence that uses the given idiom correctly.<br>Have your head in the clouds</p>",
                    options_en: [
                        "<p>They had their heads in the clouds by looking up at the sky during a meteor shower.</p>",
                        "<p>The pilot had his head in the clouds while flying a plane.</p>",
                        "<p>He had his head in the clouds during an important meeting.</p>",
                        "<p>She had her head in the clouds by wearing a cloud-shaped hat.</p>"
                    ],
                    options_hi: [
                        "<p>They had their heads in the clouds by looking up at the sky during a meteor shower.</p>",
                        "<p>The pilot had his head in the clouds while flying a plane.</p>",
                        "<p>He had his head in the clouds during an important meeting.</p>",
                        "<p>She had her head in the clouds by wearing a cloud-shaped hat.</p>"
                    ],
                    solution_en: "<p>88.(c) He had his head in the clouds during an important meeting.<br>&lsquo;Have your head in the clouds&rsquo; is an idiom which means someone is not paying attention. Going through the options, the sentence given in option (c) correctly uses the idiom. Hence, option (c) is the most appropriate answer.</p>",
                    solution_hi: "<p>88.(c) He had his head in the clouds during an important meeting.<br>&lsquo;Have your head in the clouds&rsquo; एक idiom है जिसका अर्थ है कि कोई ध्यान (attention) नहीं दे रहा है। options के माध्यम से जाने पर, option (c) में दिया गया sentence idiom का सही प्रयोग करता है। इसलिए, option (c) सबसे उपयुक्त उत्तर है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "89",
                    section: "7",
                    question_en: "<p>89. Select the most appropriate option that can substitute the underlined word in the given idiom. <br>At the <span style=\"text-decoration: underline;\">last</span> hour</p>",
                    question_hi: "<p>89. Select the most appropriate option that can substitute the underlined word in the given idiom. <br>At the <span style=\"text-decoration: underline;\">last</span> hour</p>",
                    options_en: [
                        "<p>twelfth</p>",
                        "<p>eleventh</p>",
                        "<p>sixth</p>",
                        "<p>half</p>"
                    ],
                    options_hi: [
                        "<p>twelfth</p>",
                        "<p>eleventh</p>",
                        "<p>sixth</p>",
                        "<p>half</p>"
                    ],
                    solution_en: "<p>89.(b) <strong>At the eleventh hour -</strong> in the last moment.<br>E.g.- He finished his homework at the eleventh hour, just before the deadline.</p>",
                    solution_hi: "<p>89.(b) <strong>At the eleventh hour - </strong>in the last moment./अंतिम क्षण में।<br>E.g.- He finished his homework at the eleventh hour, just before the deadline.<br><br></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "90",
                    section: "7",
                    question_en: "<p>90. In the following questions, out of the four alternatives, choose the one which can be substituted for the given words/sentence. <br>One thing that can be divided</p>",
                    question_hi: "<p>90. In the following questions, out of the four alternatives, choose the one which can be substituted for the given words/sentence. <br>One thing that can be divided</p>",
                    options_en: [
                        "<p>Remainder</p>",
                        "<p>Quotient</p>",
                        "<p>Dividend</p>",
                        "<p>Divisible</p>"
                    ],
                    options_hi: [
                        "<p>Remainder</p>",
                        "<p>Quotient</p>",
                        "<p>Dividend</p>",
                        "<p>Divisible</p>"
                    ],
                    solution_en: "<p>90.(d) <strong>Divisible-</strong> one thing that can be divided<br><strong>Remainder-</strong> the people, things, etc. that are left after the others have gone away<br><strong>Quotient-</strong> a number which is the result when one number is divided by another<br><strong>Dividend-</strong> a part of a company&rsquo;s profits that is paid to the people who own shares in it</p>",
                    solution_hi: "<p>90.(d) <strong>Divisible(भाज्य)-</strong> one thing that can be divided<br><strong>Remainder(शेष)-</strong> the people, things, etc. that are left after the others have gone away<br><strong>Quotient(</strong> भागफल)- a number which is the result when one number is divided by another<br><strong>Dividend(लाभांश)-</strong> a part of a company&rsquo;s profits that is paid to the people who own shares in it</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "91",
                    section: "7",
                    question_en: "<p>91. Identify the best way to improve the underlined part of the given sentence. If there is no improvement required, select &lsquo;no improvement&rsquo;.<br>Delhi - Gurgaon section of the metro rail <strong><span style=\"text-decoration: underline;\">were hit</span></strong> on Wednesday morning due to an electric failure causing inconvenience to daily commuters.</p>",
                    question_hi: "<p>91. Identify the best way to improve the underlined part of the given sentence. If there is no improvement required, select &lsquo;no improvement&rsquo;.<br>Delhi - Gurgaon section of the metro rail <span style=\"text-decoration: underline;\"><strong>were hit</strong></span> on Wednesday morning due to an electric failure causing inconvenience to daily commuters.</p>",
                    options_en: [
                        "<p>Are hit</p>",
                        "<p>Is hit</p>",
                        "<p>Was hit</p>",
                        "<p>No improvement</p>"
                    ],
                    options_hi: [
                        "<p>Are hit</p>",
                        "<p>Is hit</p>",
                        "<p>Was hit</p>",
                        "<p>No improvement</p>"
                    ],
                    solution_en: "<p>91.(c) Was hit<br>According to the <span style=\"text-decoration: underline;\">&ldquo;Subject-Verb Agreement Rule&rdquo;</span>, a singular subject always takes a singular verb and a plural subject always takes a plural verb. In the given sentence, &lsquo;Delhi - Gurgaon section of the metro&rsquo; is a singular subject that will take &lsquo;was&rsquo; as a singular verb. Hence, &lsquo;was hit&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>91.(c) Was hit<br><span style=\"text-decoration: underline;\">\"Subject-Verb Agreement Rule\"</span> के अनुसार, singular subject हमेशा singular verb लेता है और plural subject हमेशा plural verb लेता है। दिए गए sentence में \'Delhi - Gurgaon section of the metro\' एक singular subject है जो \'was\' को singular verb के रूप में लेगा। इसलिए, \'was hit\' सबसे उपयुक्त उत्तर है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "92",
                    section: "7",
                    question_en: "<p>92. Find a word that is the synonym of<br>Sinuous</p>",
                    question_hi: "<p>92. Find a word that is the synonym of<br>Sinuous</p>",
                    options_en: [
                        "<p>Serene</p>",
                        "<p>Straight</p>",
                        "<p>Serpentine</p>",
                        "<p>Transparent</p>"
                    ],
                    options_hi: [
                        "<p>Serene</p>",
                        "<p>Straight</p>",
                        "<p>Serpentine</p>",
                        "<p>Transparent</p>"
                    ],
                    solution_en: "<p>92.(c) serpentine<br>Sinuous means of a serpentine or wavy form.</p>",
                    solution_hi: "<p>92.(c) serpentine<br>Sinuous का अर्थ टेढ़ा या लहरदार रूप ।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "93",
                    section: "7",
                    question_en: "<p>93. Select the most appropriate pair of words to fill in the blanks.<br>Shyam has reserved a ______ in the train for his wife as she will be giving_____to his child soon.</p>",
                    question_hi: "<p>93. Select the most appropriate pair of words to fill in the blanks.<br>Shyam has reserved a ______ in the train for his wife as she will be giving_____to his child soon.</p>",
                    options_en: [
                        "<p>birth; berth</p>",
                        "<p>berth; birth</p>",
                        "<p>berth; berth</p>",
                        "<p>birth; birth</p>"
                    ],
                    options_hi: [
                        "<p>birth; berth</p>",
                        "<p>berth; birth</p>",
                        "<p>berth; berth</p>",
                        "<p>birth; birth</p>"
                    ],
                    solution_en: "<p>93.(b) berth; birth<br>&lsquo;Berth&rsquo; means a place to sleep on a train or ship &amp; &lsquo;birth&rsquo; means a baby born. The given sentence states that Shyam has reserved a berth in the train for his wife as she will be giving birth to his child soon. Hence, &lsquo;berth; birth&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>93.(b) berth; birth<br>&lsquo;Berth&rsquo; का अर्थ है ट्रेन या जहाज़ पर सोने की जगह तथा &lsquo;birth&rsquo; का अर्थ है बच्चे का जन्म। दिए गए sentence में कहा गया है कि श्याम ने अपनी पत्नी के लिए ट्रेन में सोने की जगह (berth) आरक्षित कर रखी है क्योंकि वह जल्द ही उसके बच्चे को जन्म देने वाली है। अत:, &lsquo;berth; birth&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "94",
                    section: "7",
                    question_en: "<p>94. Select the most appropriate option to fill in the blank.<br>The movie &lsquo;Desperate Ghosts&rsquo; was watched by my younger brother and he was ______ horror.</p>",
                    question_hi: "<p>94. Select the most appropriate option to fill in the blank.<br>The movie &lsquo;Desperate Ghosts&rsquo; was watched by my younger brother and he was ______ horror.</p>",
                    options_en: [
                        "<p>filled on</p>",
                        "<p>filled off</p>",
                        "<p>filled with</p>",
                        "<p>filled of</p>"
                    ],
                    options_hi: [
                        "<p>filled on</p>",
                        "<p>filled off</p>",
                        "<p>filled with</p>",
                        "<p>filled of</p>"
                    ],
                    solution_en: "<p>94.(c) filled with<br>&lsquo;Filled with&rsquo; is to express that someone experiences a strong emotion or feeling. The given sentence states that the movie &lsquo;Desperate Ghosts&rsquo; was watched by my younger brother and he was filled with horror. Hence, &lsquo;filled with&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>94.(c) filled with<br>&lsquo;Filled with&rsquo; यह व्यक्त करने के लिए है कि कोई व्यक्ति एक strong emotion या feeling का अनुभव करता है। दिए गए sentence में कहा गया है कि मेरे छोटे भई ने movie &lsquo;Desperate Ghosts&rsquo; देखी और वह डर (horror) से भर गया। अतः, &lsquo;filled with&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "95",
                    section: "7",
                    question_en: "<p>95. Select the option that can be used as a one-word substitute for the given group of words. <br>Modesty or shyness resulting from a lack of self-confidence</p>",
                    question_hi: "<p>95. Select the option that can be used as a one-word substitute for the given group of words. <br>Modesty or shyness resulting from a lack of self-confidence</p>",
                    options_en: [
                        "<p>Determination</p>",
                        "<p>Assurance</p>",
                        "<p>Conviction</p>",
                        "<p>Diffidence</p>"
                    ],
                    options_hi: [
                        "<p>Determination</p>",
                        "<p>Assurance</p>",
                        "<p>Conviction</p>",
                        "<p>Diffidence</p>"
                    ],
                    solution_en: "<p>95.(d) <strong>Diffidence </strong>- modesty or shyness resulting from a lack of self-confidence.<br><strong>Determination</strong> - the quality of having firmly decided to do something.<br><strong>Assurance</strong> - a promise that something will certainly happen or be true.<br><strong>Conviction </strong>- a very strong opinion or belief.</p>",
                    solution_hi: "<p>95.(d) <strong>Diffidence </strong>(संशय) - modesty or shyness resulting from a lack of self-confidence.<br><strong>Determination </strong>(दृढ़ निश्चय) - the quality of having firmly decided to do something.<br><strong>Assurance</strong> (आश्वासन) - a promise that something will certainly happen or be true.<br><strong>Conviction </strong>(दृढ़ विश्वास) - a very strong opinion or belief.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "96",
                    section: "7",
                    question_en: "<p>96. <strong>Cloze test :-</strong><br>An old man lived in the village. He was one of the (96) _____unfortunate people in the world. The whole village was tired (97) _____ him; he was always gloomy, he (98) _____ complained and was always in a bad mood. The (99) _____ he lived, the more vile he was becoming and the more (100) _____ were his words.&nbsp;<br>Select the most appropriate option to fill in the blank no. 96</p>",
                    question_hi: "<p>96. <strong>Cloze test :-</strong><br>An old man lived in the village. He was one of the (96) _____unfortunate people in the world. The whole village was tired (97) _____ him; he was always gloomy, he (98) _____ complained and was always in a bad mood. The (99) _____ he lived, the more vile he was becoming and the more (100) _____ were his words.&nbsp;<br>Select the most appropriate option to fill in the blank no. 96</p>",
                    options_en: [
                        "<p>maximum</p>",
                        "<p>utmost</p>",
                        "<p>most</p>",
                        "<p>main</p>"
                    ],
                    options_hi: [
                        "<p>maximum</p>",
                        "<p>utmost</p>",
                        "<p>most</p>",
                        "<p>main</p>"
                    ],
                    solution_en: "<p>96.(c) &lsquo;Most&rsquo; means greatest in number or amount. The given passage states that he was one of the most unfortunate people in the world. Hence, &lsquo;most&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>96.(c) \'Most\' का अर्थ है संख्या या राशि में सबसे बड़ा। दिए गए passage में कहा गया है कि वह दुनिया के सबसे दुर्भाग्यपूर्ण लोगों में से एक थे। अतः \'most\' सर्वाधिक उपयुक्त उत्तर है I</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "97",
                    section: "7",
                    question_en: "<p>97. <strong>Cloze test :-</strong><br>An old man lived in the village. He was one of the (96) _____unfortunate people in the world. The whole village was tired (97) _____ him; he was always gloomy, he (98) _____ complained and was always in a bad mood. The (99) _____ he lived, the more vile he was becoming and the more (100) _____ were his words.&nbsp;<br>Select the most appropriate option to fill in the blank no. 97</p>",
                    question_hi: "<p>97. <strong>Cloze test :-</strong><br>An old man lived in the village. He was one of the (96) _____unfortunate people in the world. The whole village was tired (97) _____ him; he was always gloomy, he (98) _____ complained and was always in a bad mood. The (99) _____ he lived, the more vile he was becoming and the more (100) _____ were his words.&nbsp;<br>Select the most appropriate option to fill in the blank no. 97</p>",
                    options_en: [
                        "<p>from</p>",
                        "<p>of</p>",
                        "<p>by</p>",
                        "<p>at</p>"
                    ],
                    options_hi: [
                        "<p>from</p>",
                        "<p>of</p>",
                        "<p>by</p>",
                        "<p>at</p>"
                    ],
                    solution_en: "<p>97.(b) The preposition &lsquo;of&rsquo; perfectly fits in the context of the sentence. The given passage states that the whole village was tired of him. Hence, &lsquo;of&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>97.(b) Preposition \'of\' वाक्य के संदर्भ में पूरी तरह से फिट बैठता है। दिए गए passage में कहा गया है कि पूरा गाँव उससे थक गया था। इसलिए, \'of\' सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "98",
                    section: "7",
                    question_en: "<p>98. <strong>Cloze test :-</strong><br>An old man lived in the village. He was one of the (96) _____unfortunate people in the world. The whole village was tired (97) _____ him; he was always gloomy, he (98) _____ complained and was always in a bad mood. The (99) _____ he lived, the more vile he was becoming and the more (100) _____ were his words.&nbsp;<br>Select the most appropriate option to fill in the blank no. 98</p>",
                    question_hi: "<p>98. <strong>Cloze test :-</strong><br>An old man lived in the village. He was one of the (96) _____unfortunate people in the world. The whole village was tired (97) _____ him; he was always gloomy, he (98) _____ complained and was always in a bad mood. The (99) _____ he lived, the more vile he was becoming and the more (100) _____ were his words.&nbsp;<br>Select the most appropriate option to fill in the blank no. 98</p>",
                    options_en: [
                        "<p>mostly</p>",
                        "<p>commonly</p>",
                        "<p>cyclically</p>",
                        "<p>constantly</p>"
                    ],
                    options_hi: [
                        "<p>mostly</p>",
                        "<p>commonly</p>",
                        "<p>cyclically</p>",
                        "<p>constantly</p>"
                    ],
                    solution_en: "<p>98.(d) &lsquo;Constantly&rsquo; means again and&nbsp;again. The given passage states that he was always gloomy, he again and again complained. Hence, &lsquo;constantly&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>98.(d) \'Constantly\' का अर्थ है बार-बार। दिए गए passage में कहा गया है कि वह हमेशा उदास रहता था, उसने बार-बार शिकायत की। इसलिए, \'constantly\' सबसे उपयुक्त उत्तर है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "99",
                    section: "7",
                    question_en: "<p>99. <strong>Cloze test :-</strong><br>An old man lived in the village. He was one of the (96) _____unfortunate people in the world. The whole village was tired (97) _____ him; he was always gloomy, he (98) _____ complained and was always in a bad mood. The (99) _____ he lived, the more vile he was becoming and the more (100) _____ were his words.&nbsp;<br>Select the most appropriate option to fill in the blank no. 99</p>",
                    question_hi: "<p>99. <strong>Cloze test :-</strong><br>An old man lived in the village. He was one of the (96) _____unfortunate people in the world. The whole village was tired (97) _____ him; he was always gloomy, he (98) _____ complained and was always in a bad mood. The (99) _____ he lived, the more vile he was becoming and the more (100) _____ were his words.&nbsp;<br>Select the most appropriate option to fill in the blank no. 99</p>",
                    options_en: [
                        "<p>long</p>",
                        "<p>lengthy</p>",
                        "<p>longer</p>",
                        "<p>longest</p>"
                    ],
                    options_hi: [
                        "<p>long</p>",
                        "<p>lengthy</p>",
                        "<p>longer</p>",
                        "<p>longest</p>"
                    ],
                    solution_en: "<p>99.(c) &lsquo;Longer&rsquo; means for a long time. The given passage states that the longer he lived, the more vile he was becoming. Hence, &lsquo;longer&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>99.(c) \'Longer\' का अर्थ है लंबे समय के लिए। दिए गए passage में कहा गया है कि वह जितने अधिक समय तक जीवित रहा, उतना ही अधिक नीच होता जा रहा था। इसलिए, \'longer\' सबसे उपयुक्त उत्तर है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "100",
                    section: "misc",
                    question_en: "<p>100. <strong>Cloze test :-</strong><br>An old man lived in the village. He was one of the (96) _____unfortunate people in the world. The whole village was tired (97) _____ him; he was always gloomy, he (98) _____ complained and was always in a bad mood. The (99) _____ he lived, the more vile he was becoming and the more (100) _____ were his words.&nbsp;<br>Select the most appropriate option to fill in the blank no. 100</p>",
                    question_hi: "<p>100. <strong>Cloze test :-</strong><br>An old man lived in the village. He was one of the (96) _____unfortunate people in the world. The whole village was tired (97) _____ him; he was always gloomy, he (98) _____ complained and was always in a bad mood. The (99) _____ he lived, the more vile he was becoming and the more (100) _____ were his words.&nbsp;<br>Select the most appropriate option to fill in the blank no. 100</p>",
                    options_en: [
                        "<p>fatal</p>",
                        "<p>mortal</p>",
                        "<p>poisonous</p>",
                        "<p>toxic</p>"
                    ],
                    options_hi: [
                        "<p>fatal</p>",
                        "<p>mortal</p>",
                        "<p>poisonous</p>",
                        "<p>toxic</p>"
                    ],
                    solution_en: "<p>100.(c) &lsquo;Poisonous&rsquo; means causing death or illness if you eat or drink it. The given passage states that the longer he lived, the more vile he was becoming the more poisonous were his words. Hence, &lsquo;poisonous&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>100.(c) \'Poisonous\' का अर्थ जहरीला हैं। दिए गए passage में कहा गया है कि वह जितने लंबे समय तक जीवित रहा, वह उतना ही अधिक जहरीला होता जा रहा। अतः \'poisonous\' सबसे उपयुक्त उत्तर है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'question-box d-flex align-items-center justify-content-center border rounded';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.question-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            const html = `
                <div class="card shadow-sm mb-4">
                    <div class="card-body">
                        <div class="question-text mb-4">
                            <div class="en">${question.question_en}</div>
                            <div class="hi" style="display:none">${question.question_hi}</div>
                        </div>
                        <div class="options">
                            ${['a', 'b', 'c', 'd'].map((opt, i) => `
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="radio" name="q${index}" 
                                        id="opt-${index}-${opt}" value="${opt}" 
                                        ${answers[question.id] === opt ? 'checked' : ''}
                                        onclick="selectAnswer('${question.id}', '${opt}')">
                                    <label class="form-check-label w-100 py-2 px-3 rounded hover-bg-light" for="opt-${index}-${opt}">
                                        <div class="en">${question.options_en[i]}</div>
                                        <div class="hi" style="display:none">${question.options_hi[i]}</div>
                                    </label>
                                </div>
                            `).join('')}
                        </div>
                        ${submitted ? `
                            <div class="alert ${answers[question.id] === question.correct ? 'alert-success' : 'alert-danger'} mt-3">
                                ${answers[question.id] ? (answers[question.id] === question.correct ? 'Correct!' : 'Incorrect!') : 'Not attempted'}
                                <div class="mt-2">
                                    <strong>Solution:</strong>
                                    <div class="en">${question.solution_en}</div>
                                    <div class="hi" style="display:none">${question.solution_hi}</div>
                                </div>
                            </div>
                        ` : ''}
                    </div>
                </div>
            `;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // If in mobile view, close the menu
            if (window.innerWidth < 768) {
                toggleMenu(false);
            }
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                document.getElementById(`box-${index}`).classList.add('attempted');
            }
        }
        // Navigate to previous question
        function prevQuestion() {
            if (currentQuestion > 0) {
                showQuestion(currentQuestion - 1);
            }
        }
        // Navigate to next question
        function nextQuestion() {
            if (currentQuestion < questions.length - 1) {
                showQuestion(currentQuestion + 1);
            }
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) {
                showQuestion(section.start);
            }
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update button text
            document.getElementById('lang-btn').innerHTML = currentLang === 'en' ? 
                '<i class="fas fa-language"></i> हिंदी' : 
                '<i class="fas fa-language"></i> English';
        }
        // Toggle menu for mobile
        function toggleMenu(show) {
            const nav = document.getElementById('question-nav');
            const content = document.getElementById('question-content');
            if (show === undefined) {
                show = nav.style.right === '-100%' || nav.style.right === '';
            }
            if (show) {
                nav.style.right = '0';
                if (window.innerWidth >= 768) {
                    content.style.marginRight = '320px';
                }
            } else {
                nav.style.right = window.innerWidth < 768 ? '-100%' : '0';
                content.style.marginRight = window.innerWidth < 768 ? '0' : '320px';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
            document.getElementById('total-count').textContent = questions.length;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.remove('attempted');
                    box.classList.add('bg-secondary', 'text-white');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.remove('attempted');
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.remove('attempted');
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            resultsModal.show();
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check-circle me-1"></i> Submitted';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            resultsModal.hide();
            showQuestion(0);
        }
    </script>
</body>
</html>