<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">15:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 15</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">15</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 15 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 0,
                end: 13
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 14,
                end: 14
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">1.</span><span style=\"font-family: Cambria Math;\"> The present cost of a building is &#8377;1,28,000. After a year, its cost increased to &#8377;1,46,000. The percentage increase (rounded off to the nearest integer) in the cost of the building is:</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">1.</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2311;&#2350;&#2366;&#2352;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2357;&#2352;&#2381;&#2340;&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> &#8377;1,28,000 </span><span style=\"font-family: Kohinoor Devanagari;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2357;&#2352;&#2381;&#2359;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2348;&#2366;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2311;&#2360;&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2348;&#2338;&#2364;&#2325;&#2352;</span><span style=\"font-family: Cambria Math;\"> &#8377;1,46,000 </span><span style=\"font-family: Kohinoor Devanagari;\">&#2361;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2327;&#2351;&#2366;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2311;&#2350;&#2366;&#2352;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2361;&#2369;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2357;&#2371;&#2342;&#2381;&#2343;&#2367;</span><span style=\"font-family: Cambria Math;\"> (</span><span style=\"font-family: Kohinoor Devanagari;\">&#2344;&#2367;&#2325;&#2335;&#2340;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2346;&#2370;&#2352;&#2381;&#2339;&#2366;&#2306;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2340;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2346;&#2370;&#2352;&#2381;&#2339;&#2366;&#2325;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\">) </span><span style=\"font-family: Kohinoor Devanagari;\">&#2332;&#2381;&#2334;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2368;&#2332;&#2367;&#2319;&#2404;</span></p>\n",
                    options_en: ["<p>13</p>\n", "<p>12</p>\n", 
                                "<p>15</p>\n", "<p>14</p>\n"],
                    options_hi: ["<p>13</p>\n", "<p>12</p>\n",
                                "<p>15</p>\n", "<p>14</p>\n"],
                    solution_en: "<p>1.(d)</p>\r\n<p><span style=\"font-family: Cambria Math;\">Present cost of the building = </span><span style=\"font-family: Cambria Math;\">1,28,000</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">After 1 year, cost of the building = </span><span style=\"font-family: Cambria Math;\">1,46,000</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Required percentage increase =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>146000</mn><mo>-</mo><mn>128000</mn></mrow><mn>128000</mn></mfrac><mo>=</mo><mn>14</mn><mo>%</mo></math></span></p>\n",
                    solution_hi: "<p>1.(d)</p>\r\n<p><span style=\"font-family: Kohinoor Devanagari;\">&#2311;&#2350;&#2366;&#2352;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2357;&#2352;&#2381;&#2340;&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2354;&#2366;&#2327;&#2340;</span><span style=\"font-family: Cambria Math;\"> = &#8377;1,28,000</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">1 </span><span style=\"font-family: Kohinoor Devanagari;\">&#2357;&#2352;&#2381;&#2359;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2348;&#2366;&#2342;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2311;&#2350;&#2366;&#2352;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2354;&#2366;&#2327;&#2340;</span><span style=\"font-family: Cambria Math;\"> = &#8377;1,46,000</span></p>\r\n<p><span style=\"font-family: Kohinoor Devanagari;\">&#2310;&#2357;&#2358;&#2381;&#2351;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2357;&#2371;&#2342;&#2381;&#2343;&#2367;</span><span style=\"font-family: Cambria Math;\"> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>146000</mn><mo>-</mo><mn>128000</mn></mrow><mn>128000</mn></mfrac><mo>=</mo><mn>14</mn><mo>%</mo></math></span></p>\n",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "2",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">2.</span><span style=\"font-family: Cambria Math;\"> Out of his total monthly salary, Mohan spends 20% on house rent and 50% of the rest is his total household expenditure. If he saves &#8377;10,600, then his total monthly salary is:</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">2.</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2350;&#2379;&#2361;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2309;&#2346;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2369;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2350;&#2366;&#2360;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2357;&#2375;&#2340;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> 20% </span><span style=\"font-family: Kohinoor Devanagari;\">&#2328;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2367;&#2352;&#2366;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2357;&#2381;&#2351;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2352;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Kohinoor Devanagari;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2358;&#2375;&#2359;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2357;&#2375;&#2340;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> 50% </span><span style=\"font-family: Kohinoor Devanagari;\">&#2309;&#2346;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2369;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2328;&#2352;&#2375;&#2354;&#2370;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2326;&#2352;&#2381;&#2330;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2357;&#2381;&#2351;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2352;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2357;&#2361;</span><span style=\"font-family: Cambria Math;\"> &#8377;10,600 </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2348;&#2330;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2352;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Kohinoor Devanagari;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2313;&#2360;&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2369;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2350;&#2366;&#2360;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2357;&#2375;&#2340;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2367;&#2340;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>&#8377;24,500</p>\n", "<p>&#8377;25,000</p>\n", 
                                "<p>&#8377;27,000</p>\n", "<p>&#8377;26,500</p>\n"],
                    options_hi: ["<p>&#8377;24,500</p>\n", "<p>&#8377;25,000</p>\n",
                                "<p>&#8377;27,000</p>\n", "<p>&#8377;26,500</p>\n"],
                    solution_en: "<p>2.(d)</p>\r\n<p><span style=\"font-family: Cambria Math;\">Let his total salary be &#8377;x.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">He spends 20% of salary on house rent, =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>20</mn><mn>100</mn></mfrac><mi>x</mi></math></span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Remaining salary =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>80</mn><mn>100</mn></mfrac><mo>&nbsp;</mo><mi>x</mi></math></span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Money spent on household expenditure =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>50</mn><mn>100</mn></mfrac><mo>&times;</mo><mfrac><mn>80</mn><mn>100</mn></mfrac><mi>x</mi><mo>=</mo><mfrac><mn>40</mn><mn>100</mn></mfrac><mi>x</mi></math></span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Saving =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>80</mn><mn>100</mn></mfrac><mi>x</mi><mo>-</mo><mfrac><mn>40</mn><mn>100</mn></mfrac><mi>x</mi><mo>=</mo><mfrac><mn>40</mn><mn>100</mn></mfrac><mi>x</mi></math></span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>40</mn><mn>100</mn></mfrac><mi>x</mi><mo>=</mo><mn>10600</mn></math></span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&rArr;</mo></math>x = 26,500</span></span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Hence, his total salary = &#8377;26,500</span></p>\n",
                    solution_hi: "<p>2.(d)</p>\r\n<p><span style=\"font-family: Kohinoor Devanagari;\">&#2350;&#2366;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2313;&#2360;&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2369;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2357;&#2375;&#2340;&#2344;</span><span style=\"font-family: Cambria Math;\"> &#8377;x </span><span style=\"font-family: Kohinoor Devanagari;\">&#2361;&#2376;&#2404;</span></p>\r\n<p><span style=\"font-family: Kohinoor Devanagari;\">&#2357;&#2361;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2357;&#2375;&#2340;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> 20% </span><span style=\"font-family: Kohinoor Devanagari;\">&#2328;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2367;&#2352;&#2366;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2326;&#2352;&#2381;&#2330;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2352;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>20</mn><mn>100</mn></mfrac><mi>x</mi></math> </span></p>\r\n<p><span style=\"font-family: Kohinoor Devanagari;\">&#2358;&#2375;&#2359;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2357;&#2375;&#2340;&#2344;</span><span style=\"font-family: Cambria Math;\"> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>80</mn><mn>100</mn></mfrac><mo>&nbsp;</mo><mi>x</mi></math></span></p>\r\n<p><span style=\"font-family: Kohinoor Devanagari;\">&#2328;&#2352;&#2375;&#2354;&#2370;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2326;&#2352;&#2381;&#2330;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2326;&#2352;&#2381;&#2330;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2327;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2346;&#2376;&#2360;&#2366;</span><span style=\"font-family: Cambria Math;\"> =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>50</mn><mn>100</mn></mfrac><mo>&times;</mo><mfrac><mn>80</mn><mn>100</mn></mfrac><mi>x</mi><mo>=</mo><mfrac><mn>40</mn><mn>100</mn></mfrac><mi>x</mi></math> </span></p>\r\n<p><span style=\"font-family: Kohinoor Devanagari;\">&#2348;&#2330;&#2340;</span><span style=\"font-family: Cambria Math;\"> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>80</mn><mn>100</mn></mfrac><mi>x</mi><mo>-</mo><mfrac><mn>40</mn><mn>100</mn></mfrac><mi>x</mi><mo>=</mo><mfrac><mn>40</mn><mn>100</mn></mfrac><mi>x</mi></math></span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>40</mn><mn>100</mn></mfrac><mi>x</mi><mo>=</mo><mn>10600</mn></math></span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&rArr;</mo></math>x = 26,500</span></span></p>\r\n<p><span style=\"font-family: Kohinoor Devanagari;\">&#2311;&#2360;&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Kohinoor Devanagari;\">&#2313;&#2360;&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2369;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2357;&#2375;&#2340;&#2344;</span><span style=\"font-family: Cambria Math;\"> = &#8377;26,500</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "3",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">3.</span><span style=\"font-family: Cambria Math;\"> The price of a laptop in a shop is decreased by 35%, as a result of which the sales increased by 20%. What is the effect on the total revenue of the shop from the sale of laptops?</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">3.</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2342;&#2369;&#2325;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2354;&#2376;&#2346;&#2335;&#2377;&#2346;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> 35% </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2361;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2332;&#2366;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Kohinoor Devanagari;\">&#2332;&#2367;&#2360;&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2346;&#2352;&#2367;&#2339;&#2366;&#2350;&#2360;&#2381;&#2357;&#2352;&#2370;&#2346;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2348;&#2367;&#2325;&#2381;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> 20% </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2357;&#2371;&#2342;&#2381;&#2343;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2361;&#2379;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2354;&#2376;&#2346;&#2335;&#2377;&#2346;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2348;&#2367;&#2325;&#2381;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2342;&#2369;&#2325;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2369;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2310;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2346;&#2381;&#2352;&#2349;&#2366;&#2357;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2346;&#2337;&#2364;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>10% increase</p>\n", "<p>22% decrease</p>\n", 
                                "<p>10% decrease</p>\n", "<p>18% increase</p>\n"],
                    options_hi: ["<p>10% <span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2357;&#2371;&#2342;&#2381;&#2343;&#2367;</span></p>\n", "<p>22% <span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2350;&#2368;</span></p>\n",
                                "<p>10% <span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2350;&#2368;</span></p>\n", "<p>18% <span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2357;&#2371;&#2342;&#2381;&#2343;&#2367;</span></p>\n"],
                    solution_en: "<p>3.(b)</p>\r\n<p><span style=\"font-family: Cambria Math;\">Price&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; </span><span style=\"font-family: Cambria Math;\">20 : 13</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Sale.&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<span style=\"text-decoration: underline;\"> </span></span><span style=\"text-decoration: underline;\"><span style=\"font-family: Cambria Math;\">5&nbsp; &nbsp;:&nbsp; 6</span></span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Revenue&nbsp; </span><span style=\"font-family: Cambria Math;\">100 :&nbsp; 78</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Hence, effect on the total revenue = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&nbsp;</mo><mn>100</mn><mo>-</mo><mn>78</mn></mrow><mn>100</mn></mfrac><mo>&times;</mo><mn>100</mn><mo>=</mo><mn>22</mn><mo>%</mo></math></span><span style=\"font-family: Cambria Math;\"> decrease</span></p>\n",
                    solution_hi: "<p>3.(b)</p>\r\n<p><span style=\"font-family: Kohinoor Devanagari;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; </span><span style=\"font-family: Cambria Math;\">20&nbsp; &nbsp;:&nbsp; &nbsp;13</span></p>\r\n<p><span style=\"font-family: Kohinoor Devanagari;\">&#2348;&#2367;&#2325;&#2381;&#2352;&#2368;.&nbsp; &nbsp; &nbsp; &nbsp;<span style=\"text-decoration: underline;\"> </span></span><span style=\"text-decoration: underline;\"><span style=\"font-family: Cambria Math;\">5&nbsp; &nbsp; :&nbsp; &nbsp;6</span></span></p>\r\n<p><span style=\"font-family: Kohinoor Devanagari;\">&#2310;&#2351;.&nbsp; &nbsp; &nbsp; &nbsp; </span><span style=\"font-family: Cambria Math;\">100 :&nbsp; 78</span></p>\r\n<p><span style=\"font-family: Kohinoor Devanagari;\">&#2309;&#2340;&#2307;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2369;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2310;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2346;&#2381;&#2352;&#2349;&#2366;&#2357;</span><span style=\"font-family: Cambria Math;\"> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&nbsp;</mo><mn>100</mn><mo>-</mo><mn>78</mn></mrow><mn>100</mn></mfrac><mo>&times;</mo><mn>100</mn><mo>=</mo><mn>22</mn><mo>%</mo></math></span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2350;&#2368;</span></p>\n",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "4",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">4.</span><span style=\"font-family: Cambria Math;\"> The price of a TV set increases by 20% in the first year and again by 5% in the next year. The net effect on the price of the TV set after two years compared to its initial price is a:</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">4.</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2335;&#2368;&#2357;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2360;&#2375;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2346;&#2361;&#2354;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2357;&#2352;&#2381;&#2359;</span><span style=\"font-family: Cambria Math;\"> 20% </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2357;&#2371;&#2342;&#2381;&#2343;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2361;&#2379;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Kohinoor Devanagari;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2309;&#2327;&#2354;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2357;&#2352;&#2381;&#2359;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2346;&#2369;&#2344;&#2307;</span><span style=\"font-family: Cambria Math;\"> 5% </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2357;&#2371;&#2342;&#2381;&#2343;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2361;&#2379;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2342;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2357;&#2352;&#2381;&#2359;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2348;&#2366;&#2342;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Kohinoor Devanagari;\">&#2335;&#2368;&#2357;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2360;&#2375;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Kohinoor Devanagari;\">&#2311;&#2360;&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2358;&#2369;&#2352;&#2369;&#2310;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2340;&#2369;&#2354;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2361;&#2369;&#2310;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2358;&#2369;&#2342;&#2381;&#2343;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2346;&#2381;&#2352;&#2349;&#2366;&#2357;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2361;&#2379;&#2327;&#2366;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>26% increase</p>\n", "<p>20% increase</p>\n", 
                                "<p>15% increase</p>\n", "<p>25% increase</p>\n"],
                    options_hi: ["<p>26% <span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2357;&#2371;&#2342;&#2381;&#2343;&#2367;</span></p>\n", "<p>20% <span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2357;&#2371;&#2342;&#2381;&#2343;&#2367;</span></p>\n",
                                "<p>15% <span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2357;&#2371;&#2342;&#2381;&#2343;&#2367;</span></p>\n", "<p>25% <span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2357;&#2371;&#2342;&#2381;&#2343;&#2367;</span></p>\n"],
                    solution_en: "<p>4.(a)</p>\r\n<p><span style=\"font-family: Cambria Math;\">First year&nbsp; &nbsp; &nbsp; &nbsp; </span><span style=\"font-family: Cambria Math;\">5&nbsp; &nbsp;:&nbsp; &nbsp;6</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Second year.&nbsp;<span style=\"text-decoration: underline;\"> </span></span><span style=\"text-decoration: underline;\"><span style=\"font-family: Cambria Math;\">20&nbsp; :&nbsp; 21</span></span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;100 : 126</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Overall increment =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>126</mn><mo>-</mo><mn>100</mn></mrow><mn>100</mn></mfrac><mo>&times;</mo><mn>100</mn><mo>=</mo><mfrac><mn>26</mn><mn>100</mn></mfrac><mo>&times;</mo><mn>100</mn><mo>=</mo><mn>26</mn><mo>%</mo></math></span></p>\n",
                    solution_hi: "<p>4.(a)</p>\r\n<p><span style=\"font-family: Kohinoor Devanagari;\">&#2346;&#2381;&#2352;&#2341;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2357;&#2352;&#2381;&#2359;</span><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp; &nbsp; &nbsp;</span><span style=\"font-family: Cambria Math;\">5 : 6</span></p>\r\n<p><span style=\"font-family: Kohinoor Devanagari;\">&#2342;&#2381;&#2357;&#2367;&#2340;&#2368;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2357;&#2352;&#2381;&#2359;.&nbsp; </span><span style=\"text-decoration: underline;\"><span style=\"font-family: Cambria Math;\">20 : 21</span></span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;100 : 126</span></p>\r\n<p><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2369;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2357;&#2371;&#2342;&#2381;&#2343;&#2367;</span><span style=\"font-family: Cambria Math;\"> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>126</mn><mo>-</mo><mn>100</mn></mrow><mn>100</mn></mfrac><mo>&times;</mo><mn>100</mn><mo>=</mo><mfrac><mn>26</mn><mn>100</mn></mfrac><mo>&times;</mo><mn>100</mn><mo>=</mo><mn>26</mn><mo>%</mo></math></span></p>\n",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "5",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">5.</span><span style=\"font-family: Cambria Math;\"> If the monthly salary of a person is &#8377;28,000 and the expenditure for the month is 92% of his salary, then the remaining amount from that month\'s salary is</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">5. </span><span style=\"font-family: Kohinoor Devanagari;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2367;&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2357;&#2381;&#2351;&#2325;&#2381;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2350;&#2366;&#2360;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2357;&#2375;&#2340;&#2344;</span><span style=\"font-family: Cambria Math;\"> &#8377;28,000 </span><span style=\"font-family: Kohinoor Devanagari;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2350;&#2361;&#2368;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2326;&#2352;&#2381;&#2330;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2313;&#2360;&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2357;&#2375;&#2340;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> 92% </span><span style=\"font-family: Kohinoor Devanagari;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Kohinoor Devanagari;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2313;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2350;&#2361;&#2368;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2357;&#2375;&#2340;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2358;&#2375;&#2359;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2352;&#2366;&#2358;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2367;&#2340;&#2344;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>&#8377;2,340</p>\n", "<p>&#8377;2,400</p>\n", 
                                "<p>&#8377;2,200</p>\n", "<p>&#8377;2,240</p>\n"],
                    options_hi: ["<p>&#8377;2,340</p>\n", "<p>&#8377;2,400</p>\n",
                                "<p>&#8377;2,200</p>\n", "<p>&#8377;2,240</p>\n"],
                    solution_en: "<p>5.(d)</p>\r\n<p><span style=\"font-family: Cambria Math;\">Monthly salary = &#8377;28,000</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Amount spent on expenditure =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>92</mn><mn>100</mn></mfrac><mo>&times;</mo><mn>28000</mn><mo>=</mo><mo>&#8377;</mo><mn>25760</mn></math></span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Remaining salary = 28000 - 25760 = &#8377;2,240</span></p>\n",
                    solution_hi: "<p>5.(d)</p>\r\n<p><span style=\"font-family: Kohinoor Devanagari;\">&#2350;&#2366;&#2360;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2357;&#2375;&#2340;&#2344;</span><span style=\"font-family: Cambria Math;\"> = &#8377;28,000</span></p>\r\n<p><span style=\"font-family: Kohinoor Devanagari;\">&#2357;&#2381;&#2351;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2326;&#2352;&#2381;&#2330;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2327;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2352;&#2366;&#2358;&#2367;</span><span style=\"font-family: Cambria Math;\"> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>92</mn><mn>100</mn></mfrac><mo>&times;</mo><mn>28000</mn><mo>=</mo><mo>&#8377;</mo><mn>25760</mn></math></span></p>\r\n<p><span style=\"font-family: Kohinoor Devanagari;\">&#2358;&#2375;&#2359;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2357;&#2375;&#2340;&#2344;</span><span style=\"font-family: Cambria Math;\"> = 28000 - 25760 = &#8377;2,240</span></p>\n",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "6",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">6. </span><span style=\"font-family: Cambria Math;\">Mridul\'s working hours per day increased by 25% and his hourly wages increased by 20%. By how much percent did his daily earnings increase?</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">6. </span><span style=\"font-family: Kohinoor Devanagari;\">&#2350;&#2371;&#2342;&#2369;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2342;&#2367;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2366;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2328;&#2306;&#2335;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> 25% </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2357;&#2371;&#2342;&#2381;&#2343;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2361;&#2369;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2313;&#2360;&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2346;&#2381;&#2352;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2328;&#2306;&#2335;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2350;&#2332;&#2342;&#2370;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> 20% </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2357;&#2371;&#2342;&#2381;&#2343;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2361;&#2369;&#2312;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2313;&#2360;&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2342;&#2376;&#2344;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2310;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2367;&#2340;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2357;&#2371;&#2342;&#2381;&#2343;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2361;&#2369;&#2312;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>30%</p>\n", "<p>46%</p>\n", 
                                "<p>50%</p>\n", "<p>40%</p>\n"],
                    options_hi: ["<p>30%</p>\n", "<p>46%</p>\n",
                                "<p>50%</p>\n", "<p>40%</p>\n"],
                    solution_en: "<p>6.(c)</p>\r\n<p><span style=\"font-family: Cambria Math;\">Working hour.&nbsp; </span><span style=\"font-family: Cambria Math;\">4 : 5</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Wages.&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<span style=\"text-decoration: underline;\"> </span></span><span style=\"text-decoration: underline;\"><span style=\"font-family: Cambria Math;\">5 : 6</span></span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;20 : 30</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">His increased earning =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>10</mn><mn>20</mn></mfrac><mo>&times;</mo><mn>100</mn><mo>=</mo><mn>50</mn><mo>%</mo></math></span></p>\n",
                    solution_hi: "<p>6.(c)</p>\r\n<p><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2366;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2360;&#2350;&#2351;</span><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp; </span><span style=\"font-family: Cambria Math;\">4 : 5</span></p>\r\n<p><span style=\"font-family: Kohinoor Devanagari;\">&#2350;&#2332;&#2342;&#2370;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; </span><span style=\"text-decoration: underline;\"><span style=\"font-family: Cambria Math;\">5 : 6</span></span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 20 : 30</span></p>\r\n<p><span style=\"font-family: Kohinoor Devanagari;\">&#2313;&#2360;&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2348;&#2338;&#2364;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2361;&#2369;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2350;&#2366;&#2312;</span><span style=\"font-family: Cambria Math;\"> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>10</mn><mn>20</mn></mfrac><mo>&times;</mo><mn>100</mn><mo>=</mo><mn>50</mn><mo>%</mo></math></span></p>\n",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "7",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">7. </span><span style=\"font-family: Cambria Math;\">When the price of an article is increased by 20% its sale reduces by 20%. What is the net effect on the revenue?</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">7. </span><span style=\"font-family: Kohinoor Devanagari;\">&#2332;&#2348;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2367;&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2357;&#2360;&#2381;&#2340;&#2369;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2368;&#2350;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> 20% </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2357;&#2371;&#2342;&#2381;&#2343;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2332;&#2366;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2313;&#2360;&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2348;&#2367;&#2325;&#2381;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> 20% </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2350;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2361;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2332;&#2366;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2310;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2358;&#2369;&#2342;&#2381;&#2343;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2346;&#2381;&#2352;&#2349;&#2366;&#2357;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>4% decrease</p>\n", "<p>4% increase</p>\n", 
                                "<p>20% decrease</p>\n", "<p>20% increase</p>\n"],
                    options_hi: ["<p>4% <span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2350;&#2368;</span></p>\n", "<p>4% <span style=\"font-family: Kohinoor Devanagari;\">&#2357;&#2371;&#2342;&#2381;&#2343;&#2367;</span></p>\n",
                                "<p>20% <span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2350;&#2368;</span></p>\n", "<p>20% <span style=\"font-family: Kohinoor Devanagari;\">&#2357;&#2371;&#2342;&#2381;&#2343;&#2367;</span></p>\n"],
                    solution_en: "<p>7.(a)</p>\r\n<p><span style=\"font-family: Cambria Math;\">Price<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&rarr;</mo></math></span><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; </span><span style=\"font-family: Cambria Math;\">5 : 6</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Sale&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&rarr;</mo></math> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;</span><span style=\"text-decoration: underline;\"><span style=\"font-family: Cambria Math;\">5 : 4</span></span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Revenue<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&rarr;</mo></math>&nbsp;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">25 : 24</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Decrement in revenue =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>25</mn></mfrac><mo>&times;</mo><mn>100</mn><mo>=</mo><mn>4</mn><mo>%</mo></math></span></p>\n",
                    solution_hi: "<p>7.(a)</p>\r\n<p><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2368;&#2350;&#2340;</span><span style=\"font-family: Cambria Math;\">&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&rarr;</mo></math> &nbsp; &nbsp; &nbsp;</span><span style=\"font-family: Cambria Math;\">5 : 6</span></p>\r\n<p><span style=\"font-family: Kohinoor Devanagari;\">&#2348;&#2367;&#2325;&#2381;&#2352;&#2368;.<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&rarr;</mo></math>&nbsp; &nbsp; &nbsp;<span style=\"text-decoration: underline;\"> </span></span><span style=\"text-decoration: underline;\"><span style=\"font-family: Cambria Math;\">5 : 4</span></span></p>\r\n<p><span style=\"font-family: Kohinoor Devanagari;\">&#2310;&#2351;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&rarr;</mo></math>.&nbsp; &nbsp; </span><span style=\"font-family: Cambria Math;\">&nbsp;</span><span style=\"font-family: Cambria Math;\">25 : 24</span></p>\r\n<p><span style=\"font-family: Kohinoor Devanagari;\">&#2310;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2350;&#2368;</span><span style=\"font-family: Cambria Math;\"> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>25</mn></mfrac><mo>&times;</mo><mn>100</mn><mo>=</mo><mn>4</mn><mo>%</mo></math></span></p>\n",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "8",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">8. </span><span style=\"font-family: Cambria Math;\">The cost of one kg sugar in 2020 was &#8377;46. If the cost of sugar increased by 2.5% in one year, how much would 5 kg sugar cost in 2021 ?</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">8. </span><span style=\"font-family: Cambria Math;\">2020 </span><span style=\"font-family: Kohinoor Devanagari;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> 1 kg </span><span style=\"font-family: Kohinoor Devanagari;\">&#2330;&#2368;&#2344;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2368;&#2350;&#2340;</span><span style=\"font-family: Cambria Math;\"> &#8377;46 </span><span style=\"font-family: Kohinoor Devanagari;\">&#2341;&#2368;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2357;&#2352;&#2381;&#2359;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2330;&#2368;&#2344;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2368;&#2350;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> 2.5% </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2357;&#2371;&#2342;&#2381;&#2343;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2361;&#2379;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> , </span><span style=\"font-family: Kohinoor Devanagari;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> 2021 </span><span style=\"font-family: Kohinoor Devanagari;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> 5 kg </span><span style=\"font-family: Kohinoor Devanagari;\">&#2330;&#2368;&#2344;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2368;&#2350;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2367;&#2340;&#2344;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2361;&#2379;&#2327;&#2368;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>&#8377;48.50</p>\n", "<p>&#8377;253.50</p>\n", 
                                "<p>&#8377;47.15</p>\n", "<p>&#8377;235.75</p>\n"],
                    options_hi: ["<p>&#8377;48.50</p>\n", "<p>&#8377;253.50</p>\n",
                                "<p>&#8377;47.15</p>\n", "<p>&#8377;235.75</p>\n"],
                    solution_en: "<p>8.(d)</p>\r\n<p><span style=\"font-family: Cambria Math;\">The cost of 1 kg of sugar in 2020 = &#8377;46</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">New cost of 1 kg of sugar in 2021 =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>102</mn><mo>.</mo><mn>5</mn></mrow><mn>100</mn></mfrac><mo>&times;</mo><mn>46</mn><mo>=</mo><mo>&#8377;</mo><mn>47</mn><mo>.</mo><mn>15</mn></math></span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Now, the cost of 5 kg of sugar = 47.15 &times; 5 = &#8377;235.75</span></p>\n",
                    solution_hi: "<p>8.(d)</p>\r\n<p><span style=\"font-family: Cambria Math;\">2020 </span><span style=\"font-family: Kohinoor Devanagari;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> 1 kg </span><span style=\"font-family: Kohinoor Devanagari;\">&#2330;&#2368;&#2344;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> = &#8377;46</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">2021 </span><span style=\"font-family: Kohinoor Devanagari;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> 1 kg </span><span style=\"font-family: Kohinoor Devanagari;\">&#2330;&#2368;&#2344;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2344;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>102</mn><mo>.</mo><mn>5</mn></mrow><mn>100</mn></mfrac><mo>&times;</mo><mn>46</mn><mo>=</mo><mo>&#8377;</mo><mn>47</mn><mo>.</mo><mn>15</mn></math></span></p>\r\n<p><span style=\"font-family: Kohinoor Devanagari;\">&#2309;&#2348;</span><span style=\"font-family: Cambria Math;\"> 5 kg </span><span style=\"font-family: Kohinoor Devanagari;\">&#2330;&#2368;&#2344;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> = 47.15 &times; 5 = &#8377;235.75</span></p>\n",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "9",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">9.</span><span style=\"font-family: Cambria Math;\"> Due to decrease in manpower, the production in a factory decreases by 30%. By what percent should the working hours be increased to restore the original production?</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">9.</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2358;&#2381;&#2352;&#2350;&#2358;&#2325;&#2381;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2350;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2366;&#2352;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2366;&#2352;&#2326;&#2366;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2313;&#2340;&#2381;&#2346;&#2366;&#2342;&#2344;</span><span style=\"font-family: Cambria Math;\"> 30% </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2361;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2332;&#2366;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2350;&#2370;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2313;&#2340;&#2381;&#2346;&#2366;&#2342;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2348;&#2361;&#2366;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2352;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2366;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2328;&#2306;&#2335;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2367;&#2340;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2357;&#2371;&#2342;&#2381;&#2343;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2332;&#2366;&#2344;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2330;&#2366;&#2361;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>42%</p>\n", "<p><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&nbsp;</mo><mn>42</mn><mfrac><mn>6</mn><mn>7</mn></mfrac></math>% </span></p>\n", 
                                "<p>43%</p>\n", "<p>41%</p>\n"],
                    options_hi: ["<p>42%</p>\n", "<p><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&nbsp;</mo><mn>42</mn><mfrac><mn>6</mn><mn>7</mn></mfrac></math>% </span></p>\n",
                                "<p>43%</p>\n", "<p>41%</p>\n"],
                    solution_en: "<p>9.(b)<span style=\"font-family: Cambria Math;\"> Production = Manpower <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&times;</mo></math></span><span style=\"font-family: Cambria Math;\"> Hours</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Manpower&nbsp; &nbsp; &nbsp;</span><span style=\"font-family: Cambria Math;\">10&nbsp; :&nbsp; 7</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Hours&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; </span><span style=\"font-family: Cambria Math;\">7&nbsp; :&nbsp; 10</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Required % =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>10</mn><mo>-</mo><mn>7</mn></mrow><mn>7</mn></mfrac><mo>&times;</mo><mn>100</mn><mo>=</mo><mfrac><mn>3</mn><mn>7</mn></mfrac><mo>&times;</mo><mn>100</mn><mo>=</mo><mn>42</mn><mfrac><mn>6</mn><mn>7</mn></mfrac><mo>%</mo></math> </span></p>\n",
                    solution_hi: "<p>9.(b) <span style=\"font-family: Kohinoor Devanagari;\">&#2313;&#2340;&#2381;&#2346;&#2366;&#2342;&#2344;</span><span style=\"font-family: Cambria Math;\"> = </span><span style=\"font-family: Kohinoor Devanagari;\">&#2358;&#2381;&#2352;&#2350;&#2358;&#2325;&#2381;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&times;</mo></math></span><span style=\"font-family: Kohinoor Devanagari;\">&#2328;&#2306;&#2335;&#2375;</span></p>\r\n<p><span style=\"font-family: Kohinoor Devanagari;\">&#2358;&#2381;&#2352;&#2350;&#2358;&#2325;&#2381;&#2340;&#2367;.&nbsp; &nbsp; &nbsp;</span><span style=\"font-family: Cambria Math;\">10&nbsp; :&nbsp; &nbsp;7</span></p>\r\n<p><span style=\"font-family: Kohinoor Devanagari;\">&#2328;&#2306;&#2335;&#2375;</span><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;</span><span style=\"font-family: Cambria Math;\">7&nbsp; &nbsp;:&nbsp; 10</span></p>\r\n<p><span style=\"font-family: Kohinoor Devanagari;\">&#2310;&#2357;&#2358;&#2381;&#2351;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span><span style=\"font-family: Cambria Math;\"> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>10</mn><mo>-</mo><mn>7</mn></mrow><mn>7</mn></mfrac><mo>&times;</mo><mn>100</mn><mo>=</mo><mfrac><mn>3</mn><mn>7</mn></mfrac><mo>&times;</mo><mn>100</mn><mo>=</mo><mn>42</mn><mfrac><mn>6</mn><mn>7</mn></mfrac><mo>%</mo></math></span></p>\n",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "10",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">10.</span><span style=\"font-family: Cambria Math;\"> A person spends 84% of his income and saves the remaining amount. If he saves &#8377;8,000 per month, what is his monthly income?</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">10.</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2357;&#2381;&#2351;&#2325;&#2381;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2309;&#2346;&#2344;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2310;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> 84% </span><span style=\"font-family: Kohinoor Devanagari;\">&#2326;&#2352;&#2381;&#2330;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2352;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2358;&#2375;&#2359;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2352;&#2366;&#2358;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2348;&#2330;&#2366;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2357;&#2361;</span><span style=\"font-family: Cambria Math;\"> &#8377;8,000 </span><span style=\"font-family: Kohinoor Devanagari;\">&#2346;&#2381;&#2352;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2350;&#2366;&#2361;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2348;&#2330;&#2366;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Kohinoor Devanagari;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2313;&#2360;&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2350;&#2366;&#2360;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2310;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2367;&#2340;&#2344;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>&#8377;54,000</p>\n", "<p>&#8377;48,000</p>\n", 
                                "<p>&#8377;50,000</p>\n", "<p>&#8377;45,000</p>\n"],
                    options_hi: ["<p>&#8377;54,000</p>\n", "<p>&#8377;48,000</p>\n",
                                "<p>&#8377;50,000</p>\n", "<p>&#8377;45,000</p>\n"],
                    solution_en: "<p>10.(c)</p>\r\n<p><span style=\"font-family: Cambria Math;\">Let the total income be </span><span style=\"font-family: Cambria Math;\">x</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>84</mn><mn>100</mn></mfrac><mi>x</mi><mo>+</mo><mn>8000</mn><mo>=</mo><mi>x</mi></math></span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&rArr;</mo><mi>x</mi><mo>-</mo><mfrac><mn>84</mn><mn>100</mn></mfrac><mi>x</mi><mo>=</mo><mn>8000</mn></math></span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&rArr;</mo><mfrac><mn>16</mn><mn>100</mn></mfrac><mi>x</mi><mo>=</mo><mn>8000</mn></math></span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&rArr;</mo></math>x = 50,000</span></span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Hence, his monthly income is </span><span style=\"font-family: Cambria Math;\">50,000.</span></p>\n",
                    solution_hi: "<p>10.(c)</p>\r\n<p><span style=\"font-family: Kohinoor Devanagari;\">&#2350;&#2366;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2369;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2310;&#2351;</span><span style=\"font-family: Cambria Math;\"> &#8377;x </span><span style=\"font-family: Kohinoor Devanagari;\">&#2361;&#2376;&#2404;</span></p>\r\n<p><span style=\"font-family: Kohinoor Devanagari;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>84</mn><mn>100</mn></mfrac><mi>x</mi><mo>+</mo><mn>8000</mn><mo>=</mo><mi>x</mi></math></span></p>\r\n<p><span style=\"font-family: Kohinoor Devanagari;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&rArr;</mo><mi>x</mi><mo>-</mo><mfrac><mn>84</mn><mn>100</mn></mfrac><mi>x</mi><mo>=</mo><mn>8000</mn></math></span></p>\r\n<p><span style=\"font-family: Kohinoor Devanagari;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&rArr;</mo><mfrac><mn>16</mn><mn>100</mn></mfrac><mi>x</mi><mo>=</mo><mn>8000</mn></math></span></p>\r\n<p><span style=\"font-family: Kohinoor Devanagari;\"><span style=\"font-weight: 400;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&rArr;</mo></math>x = 50,000</span></span></p>\r\n<p><span style=\"font-family: Kohinoor Devanagari;\">&#2309;&#2340;&#2307;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2313;&#2360;&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2350;&#2366;&#2360;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2310;&#2351;</span><span style=\"font-family: Cambria Math;\"> &#8377; 50,000 </span><span style=\"font-family: Kohinoor Devanagari;\">&#2361;&#2376;</span></p>\n",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "11",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">11. </span><span style=\"font-family: Cambria Math;\">Ravi spends 20% of his income on petrol, one-third of the remaining income on household expenditure, and the balance is his savings. If he spends &#8377;7,500 on petrol, find his household expenditure.</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">11. </span><span style=\"font-family: Kohinoor Devanagari;\">&#2352;&#2357;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2309;&#2346;&#2344;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2310;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> 20% </span><span style=\"font-family: Kohinoor Devanagari;\">&#2346;&#2375;&#2335;&#2381;&#2352;&#2379;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2357;&#2381;&#2351;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2352;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Kohinoor Devanagari;\">&#2358;&#2375;&#2359;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2310;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\">-</span><span style=\"font-family: Kohinoor Devanagari;\">&#2340;&#2367;&#2361;&#2366;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2328;&#2352;&#2375;&#2354;&#2370;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2326;&#2352;&#2381;&#2330;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2357;&#2381;&#2351;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2352;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Kohinoor Devanagari;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2358;&#2375;&#2359;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2313;&#2360;&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2348;&#2330;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2357;&#2361;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2346;&#2375;&#2335;&#2381;&#2352;&#2379;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> &#8377;7,500 </span><span style=\"font-family: Kohinoor Devanagari;\">&#2326;&#2352;&#2381;&#2330;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2352;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Kohinoor Devanagari;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2313;&#2360;&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2328;&#2352;&#2375;&#2354;&#2370;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2326;&#2352;&#2381;&#2330;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2332;&#2381;&#2334;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2368;&#2332;&#2367;&#2319;&#2404;</span></p>\n",
                    options_en: ["<p>&#8377;8,566.70</p>\n", "<p>&#8377;8,500</p>\n", 
                                "<p>&#8377;8,666.70</p>\n", "<p>&#8377;10,000</p>\n"],
                    options_hi: ["<p>&#8377;8,566.70</p>\n", "<p>&#8377;8,500</p>\n",
                                "<p>&#8377;8,666.70</p>\n", "<p>&#8377;10,000</p>\n"],
                    solution_en: "<p>11.(d)</p>\r\n<p><span style=\"font-family: Cambria Math;\">Let his total income be x.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>20</mn><mi>x</mi></mrow><mn>100</mn></mfrac><mo>=</mo><mn>7500</mn></math></span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&rArr;</mo><mo>&nbsp;</mo><mi>x</mi><mo>=</mo><mn>37500</mn></math></span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Now, remaining amount after spent on petrol = 37500 - 7500 = 30000</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Now, money spent on household expenditure =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac><mo>&times;</mo><mn>30000</mn><mo>=</mo><mn>10</mn><mo>,</mo><mn>000</mn></math> </span></p>\n",
                    solution_hi: "<p>11.(d)</p>\r\n<p><span style=\"font-family: Kohinoor Devanagari;\">&#2350;&#2366;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Kohinoor Devanagari;\">&#2313;&#2360;&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2369;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2310;&#2351;</span><span style=\"font-family: Cambria Math;\"> x </span><span style=\"font-family: Kohinoor Devanagari;\">&#2361;&#2376;&#2404;</span></p>\r\n<p><span style=\"font-family: Kohinoor Devanagari;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>20</mn><mi>x</mi></mrow><mn>100</mn></mfrac><mo>=</mo><mn>7500</mn></math></span></p>\r\n<p><span style=\"font-family: Kohinoor Devanagari;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&rArr;</mo><mo>&nbsp;</mo><mi>x</mi><mo>=</mo><mn>37500</mn></math></span></p>\r\n<p><span style=\"font-family: Kohinoor Devanagari;\">&#2309;&#2348;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Kohinoor Devanagari;\">&#2346;&#2375;&#2335;&#2381;&#2352;&#2379;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2357;&#2381;&#2351;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2352;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2348;&#2366;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2358;&#2375;&#2359;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2352;&#2366;&#2358;&#2367;</span><span style=\"font-family: Cambria Math;\"> = 37500 - 7500 = 30000</span></p>\r\n<p><span style=\"font-family: Kohinoor Devanagari;\">&#2309;&#2348;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Kohinoor Devanagari;\">&#2328;&#2352;&#2375;&#2354;&#2370;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2326;&#2352;&#2381;&#2330;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2326;&#2352;&#2381;&#2330;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2327;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2346;&#2376;&#2360;&#2366;</span><span style=\"font-family: Cambria Math;\"> =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac><mo>&times;</mo><mn>30000</mn><mo>=</mo><mn>10</mn><mo>,</mo><mn>000</mn></math> </span></p>\n",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "12",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">12. </span><span style=\"font-family: Cambria Math;\">X sells a washing machine to Y at a profit of 20%. Y sells it to Z at a loss of 12% and later Z sells it to T at a profit of 30%. If Z earns &#8377;87.60 more than X as profit, then at what price (in&#8377;) did Y buy the washing machine?</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">12. </span><span style=\"font-family: Cambria Math;\">X, Y </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> 20% </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2354;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2357;&#2366;&#2358;&#2367;&#2306;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2350;&#2358;&#2368;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2348;&#2375;&#2330;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> Y </span><span style=\"font-family: Kohinoor Devanagari;\">&#2311;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> 12% </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2361;&#2366;&#2344;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> Z </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2348;&#2375;&#2330;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2348;&#2366;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> Z </span><span style=\"font-family: Kohinoor Devanagari;\">&#2311;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> T </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> 30% </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2354;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2348;&#2375;&#2330;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> Z </span><span style=\"font-family: Kohinoor Devanagari;\">&#2354;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2352;&#2370;&#2346;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> X </span><span style=\"font-family: Kohinoor Devanagari;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> &#8377;87.60 </span><span style=\"font-family: Kohinoor Devanagari;\">&#2309;&#2343;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2350;&#2366;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Kohinoor Devanagari;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> Y </span><span style=\"font-family: Kohinoor Devanagari;\">&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2367;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> (&#8377; </span><span style=\"font-family: Kohinoor Devanagari;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\">) </span><span style=\"font-family: Kohinoor Devanagari;\">&#2357;&#2366;&#2358;&#2367;&#2306;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2350;&#2358;&#2368;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2326;&#2352;&#2368;&#2342;&#2368;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>960</p>\n", "<p>1,020</p>\n", 
                                "<p>900</p>\n", "<p>980</p>\n"],
                    options_hi: ["<p>960</p>\n", "<p>1,020</p>\n",
                                "<p>900</p>\n", "<p>980</p>\n"],
                    solution_en: "<p>12.(c)</p>\r\n<p><span style=\"font-family: Cambria Math;\">Let the C.P of washing machine be 100 units.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">S.P of X = C.P of Y = 120 units</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">S.P of Y = C.P of Z = 105.6 units</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">S.P of Z = C. P of T = 137.28 units</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Profit of X = 120 - 100 = 20 units</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Profit of Z = 137.28 - 105.6 = 31.68 units</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Difference between profit of X and Z = 31.68 - 20 = 11.68 units</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">11.68&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&rarr;</mo></math><span style=\"font-weight: 400;\">87.60</span></span></p>\r\n<p><span style=\"font-family: Cambria Math;\">120&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&rarr;</mo></math> <span style=\"font-weight: 400;\">900</span></span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Hence, the C.P of Y = &#8377;900</span></p>\n",
                    solution_hi: "<p>12.(c)</p>\r\n<p><span style=\"font-family: Kohinoor Devanagari;\">&#2350;&#2366;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Kohinoor Devanagari;\">&#2357;&#2366;&#2358;&#2367;&#2306;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2350;&#2358;&#2368;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2381;&#2352;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> 100 </span><span style=\"font-family: Kohinoor Devanagari;\">&#2311;&#2325;&#2366;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2361;&#2376;&#2404;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">X </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2357;&#2367;&#2325;&#2381;&#2352;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> = Y </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2381;&#2352;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> = 120 </span><span style=\"font-family: Kohinoor Devanagari;\">&#2311;&#2325;&#2366;&#2312;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Y </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2357;&#2367;&#2325;&#2381;&#2352;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> = Z </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2381;&#2352;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> = 105.6 </span><span style=\"font-family: Kohinoor Devanagari;\">&#2311;&#2325;&#2366;&#2312;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Z </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2357;&#2367;&#2325;&#2381;&#2352;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> = T </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2381;&#2352;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> = 137.28 </span><span style=\"font-family: Kohinoor Devanagari;\">&#2311;&#2325;&#2366;&#2312;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">X </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2354;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\"> = 120 - 100 = 20 units</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Z </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2354;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\"> = 137.28 - 105.6 = 31.68 units</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">X </span><span style=\"font-family: Kohinoor Devanagari;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> Z </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2354;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2348;&#2368;&#2330;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2309;&#2306;&#2340;&#2352;</span><span style=\"font-family: Cambria Math;\"> = 31.68 - 20 = 11.68 </span><span style=\"font-family: Kohinoor Devanagari;\">&#2311;&#2325;&#2366;&#2312;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">11.68&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&rarr;</mo></math><span style=\"font-weight: 400;\">87.60</span></span></p>\r\n<p><span style=\"font-family: Cambria Math;\">120&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&rarr;</mo></math><span style=\"font-weight: 400;\">900</span></span></p>\r\n<p><span style=\"font-family: Kohinoor Devanagari;\">&#2309;&#2340;&#2307;</span><span style=\"font-family: Cambria Math;\"> Y </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2381;&#2352;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> = &#8377;900</span></p>\n",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "13",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">13. </span><span style=\"font-family: Cambria Math;\">Ram\'s salary increased by 12% and then decreased by 8%. Find the overall percentage change in his salary.</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">13. </span><span style=\"font-family: Kohinoor Devanagari;\">&#2352;&#2366;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2357;&#2375;&#2340;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> 12% </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2357;&#2371;&#2342;&#2381;&#2343;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2361;&#2369;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2347;&#2367;&#2352;</span><span style=\"font-family: Cambria Math;\"> 8% </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2350;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2361;&#2369;&#2312;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2313;&#2360;&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2357;&#2375;&#2340;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2369;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2346;&#2352;&#2367;&#2357;&#2352;&#2381;&#2340;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2332;&#2381;&#2334;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2368;&#2332;&#2367;&#2319;&#2404;</span></p>\n",
                    options_en: ["<p>3.04% increase</p>\n", "<p>3.4% increase</p>\n", 
                                "<p>3.04% decrease</p>\n", "<p>3.4% decrease</p>\n"],
                    options_hi: ["<p>3.04% <span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2357;&#2371;&#2342;&#2381;&#2343;&#2367;</span></p>\n", "<p>3.4% <span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2357;&#2371;&#2342;&#2381;&#2343;&#2367;</span></p>\n",
                                "<p>3.04% <span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2350;&#2368;</span></p>\n", "<p>3.4% <span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2350;&#2368;</span></p>\n"],
                    solution_en: "<p>13.(a)</p>\r\n<p><span style=\"font-family: Cambria Math;\">Overall increased percentage =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>12</mn><mo>&nbsp;</mo><mo>-</mo><mn>8</mn><mo>-</mo><mfrac><mrow><mn>12</mn><mo>&times;</mo><mn>8</mn></mrow><mn>100</mn></mfrac></math> </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">= 4 - 0.96 = 3.04%</span></p>\n",
                    solution_hi: "<p>13.(a)</p>\r\n<p><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2369;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2346;&#2352;&#2367;&#2357;&#2352;&#2381;&#2340;&#2344;</span><span style=\"font-family: Cambria Math;\"> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>12</mn><mo>&nbsp;</mo><mo>-</mo><mn>8</mn><mo>-</mo><mfrac><mrow><mn>12</mn><mo>&times;</mo><mn>8</mn></mrow><mn>100</mn></mfrac></math></span></p>\r\n<p><span style=\"font-family: Cambria Math;\">= 4 - 0.96 = 3.04%</span></p>\n",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "14",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">14.</span><span style=\"font-family: Cambria Math;\"> From the salary of Hari. 15% is deducted as house rent, 20% of the remaining amount is spent on children\'s education. and 10% of the remaining balance is his medical expenses. Finally, he is left with &#8377;42,840. Find his total salary.</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">14.</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2361;&#2352;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2357;&#2375;&#2340;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2328;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2367;&#2352;&#2366;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2352;&#2370;&#2346;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> 15% </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2335;&#2380;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2332;&#2366;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Kohinoor Devanagari;\">&#2358;&#2375;&#2359;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2352;&#2366;&#2358;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> 20% </span><span style=\"font-family: Kohinoor Devanagari;\">&#2348;&#2330;&#2381;&#2330;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2358;&#2367;&#2325;&#2381;&#2359;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2326;&#2352;&#2381;&#2330;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2332;&#2366;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Kohinoor Devanagari;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2358;&#2375;&#2359;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2352;&#2366;&#2358;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> 10% </span><span style=\"font-family: Kohinoor Devanagari;\">&#2313;&#2360;&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2330;&#2367;&#2325;&#2367;&#2340;&#2381;&#2360;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2326;&#2352;&#2381;&#2330;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2361;&#2379;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2309;&#2306;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Kohinoor Devanagari;\">&#2313;&#2360;&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2346;&#2366;&#2360;</span><span style=\"font-family: Cambria Math;\"> &#8377;42,840 </span><span style=\"font-family: Kohinoor Devanagari;\">&#2358;&#2375;&#2359;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2348;&#2330;&#2340;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2361;&#2376;&#2306;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2313;&#2360;&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2369;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2357;&#2375;&#2340;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2332;&#2381;&#2334;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2368;&#2332;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2404;</span></p>\n",
                    options_en: ["<p>&#8377;72,500</p>\n", "<p>&#8377;65,000</p>\n", 
                                "<p>&#8377;70,000</p>\n", "<p>&#8377;75,000</p>\n"],
                    options_hi: ["<p>&#8377;72,500</p>\n", "<p>&#8377;65,000</p>\n",
                                "<p>&#8377;70,000</p>\n", "<p>&#8377;75,000</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">14.(c)</span><span style=\"font-family: Cambria Math;\"> let salary of Hari = y</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Y</span><span style=\"font-family: Cambria Math;\">&times;</span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>85</mn><mn>100</mn></mfrac><mo>&times;</mo><mfrac><mn>80</mn><mn>100</mn></mfrac><mo>&times;</mo><mfrac><mn>90</mn><mn>100</mn></mfrac></math>= </span><span style=\"font-family: Cambria Math;\">&#8377;42,840</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Y = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>42</mn><mo>,</mo><mn>840</mn><mo>&times;</mo><mn>100</mn><mo>&times;</mo><mn>100</mn><mo>&times;</mo><mn>100</mn></mrow><mrow><mn>85</mn><mo>&times;</mo><mn>80</mn><mo>&times;</mo><mn>90</mn></mrow></mfrac><mo>&nbsp;</mo></math><math></math></span><span style=\"font-family: Cambria Math;\"> = </span><span style=\"font-family: Cambria Math;\"> &#8377;70,000</span></p>\n",
                    solution_hi: "<p>14.(c) <span style=\"font-family: Kohinoor Devanagari;\">&#2350;&#2366;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2361;&#2352;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2357;&#2375;&#2340;&#2344;</span><span style=\"font-family: Cambria Math;\"> = y</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Y</span><span style=\"font-family: Cambria Math;\">&times;</span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>85</mn><mn>100</mn></mfrac><mo>&times;</mo><mfrac><mn>80</mn><mn>100</mn></mfrac><mo>&times;</mo><mfrac><mn>90</mn><mn>100</mn></mfrac></math>= </span><span style=\"font-family: Cambria Math;\">&#8377;42,840</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Y = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>42</mn><mo>,</mo><mn>840</mn><mo>&times;</mo><mn>100</mn><mo>&times;</mo><mn>100</mn><mo>&times;</mo><mn>100</mn></mrow><mrow><mn>85</mn><mo>&times;</mo><mn>80</mn><mo>&times;</mo><mn>90</mn></mrow></mfrac><mo>&nbsp;</mo></math></span><span style=\"font-family: Cambria Math;\"> = </span><span style=\"font-family: Cambria Math;\"> &#8377;70,000</span></p>\n",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "15",
                    section: "misc",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">15.</span><span style=\"font-family: Cambria Math;\"> Ramu works in a shop for &#8377;125 per hour. If his pay is increased to &#8377;145 per hour, find his percentage increase in pay.</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">15. </span><span style=\"font-family: Kohinoor Devanagari;\">&#2352;&#2366;&#2350;&#2370;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2342;&#2369;&#2325;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> &#8377;125 </span><span style=\"font-family: Kohinoor Devanagari;\">&#2346;&#2381;&#2352;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2328;&#2306;&#2335;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2357;&#2375;&#2340;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2366;&#2352;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2352;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2313;&#2360;&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2357;&#2375;&#2340;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2348;&#2338;&#2364;&#2366;&#2325;&#2352;</span><span style=\"font-family: Cambria Math;\"> &#8377;145 </span><span style=\"font-family: Kohinoor Devanagari;\">&#2346;&#2381;&#2352;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2328;&#2306;&#2335;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2342;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2332;&#2366;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Kohinoor Devanagari;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2313;&#2360;&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2357;&#2375;&#2340;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2361;&#2369;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2357;&#2371;&#2342;&#2381;&#2343;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2332;&#2381;&#2334;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2368;&#2332;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2404;</span></p>\n",
                    options_en: ["<p>12%</p>\n", "<p>16%</p>\n", 
                                "<p>14%</p>\n", "<p>18%</p>\n"],
                    options_hi: ["<p>12%</p>\n", "<p>16%</p>\n",
                                "<p>14%</p>\n", "<p>18%</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">15.(b)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">His increased pay = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>145</mn><mo>-</mo><mn>125</mn></mrow><mn>125</mn></mfrac><mo>&times;</mo><mn>100</mn><mo>=</mo><mfrac><mn>20</mn><mn>125</mn></mfrac><mo>&times;</mo><mn>100</mn><mo>=</mo><mn>16</mn><mo>%</mo></math> </span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">15.(b)</span></p>\r\n<p><span style=\"font-family: Kohinoor Devanagari;\">&#2313;&#2360;&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2348;&#2338;&#2364;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2361;&#2369;&#2310;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2357;&#2375;&#2340;&#2344;</span><span style=\"font-family: Cambria Math;\"> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>145</mn><mo>-</mo><mn>125</mn></mrow><mn>125</mn></mfrac><mo>&times;</mo><mn>100</mn><mo>=</mo><mfrac><mn>20</mn><mn>125</mn></mfrac><mo>&times;</mo><mn>100</mn><mo>=</mo><mn>16</mn><mo>%</mo></math></span></p>\n",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>