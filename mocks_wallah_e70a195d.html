<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">15:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 15</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">15</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 15 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 0,
                end: 13
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 14,
                end: 14
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "17",
                    question_en: "1. If B can do 20 percent of a work in 7 days, then B can complete the entire work in how many days ?",
                    question_hi: "1. यदि B किसी काम का 20 प्रतिशत भाग 7 दिनों में पूरा कर सकता है, तो B संपूर्ण काम कितने दिनों में पूरा कर सकता है ?",
                    options_en: [" 45 days", " 37 days", 
                                " 35 days", " 42 days"],
                    options_hi: [" 45 दिन", " 37 दिन",
                                " 35 दिन", " 42 दिन<br /> "],
                    solution_en: "1.(c) according to question, <br />Time taken by B to complete the 20% work = 7 days<br />100% (total work ) = 7 <math display=\"inline\"><mo>×</mo></math>  5 = 35 days ",
                    solution_hi: "1.(c) प्रश्न के अनुसार,<br />B द्वारा 20% कार्य पूरा करने में लिया गया समय = 7 दिन<br />100% (कुल कार्य) = 7 <math display=\"inline\"><mo>×</mo></math>  5 = 35 दिन",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "2",
                    section: "17",
                    question_en: "<p>2. A can finish a piece of work in <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>27</mn><mn>2</mn></mfrac></math> days and B can do the same work in half the time taken by A. By working together, what part of the same work they can finish in a day ?</p>",
                    question_hi: "<p>2. A एक काम को <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>27</mn><mn>2</mn></mfrac></math> दिनों में पूरा कर सकता है और B उसी काम को A द्वारा लिए गए समय के आधे समय में पूरा कर सकता है। एक साथ मिलकर काम करने पर वे उसी काम का कितना हिस्सा एक दिन में पूरा कर सकते हैं ?</p>",
                    options_en: ["<p><math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>9</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math></p>", 
                                "<p><math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math></p>"],
                    options_hi: ["<p><math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>9</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math></p>",
                                "<p><math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math></p>"],
                    solution_en: "<p>2.(a)<br>According to question,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731488296448.png\" alt=\"rId4\" width=\"260\" height=\"147\"><br>Total efficiency of A and B = 1 + 2 <br>= 3 units.<br>A and B&rsquo;s one day work = 3 &times; 1 = 3 units <br>Required part of work = <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mfrac><mrow><mn>27</mn></mrow><mrow><mn>2</mn></mrow></mfrac></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>9</mn></mfrac></math></p>",
                    solution_hi: "<p>2.(a)<br>प्रश्न के अनुसार,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731488296613.png\" alt=\"rId5\" width=\"271\" height=\"159\"><br>A और B की कुल क्षमता = 1 + 2 <br>= 3 इकाई.<br>A और B का एक दिन का कार्य = 3 &times; 1 <br>= 3 इकाई<br>कार्य का आवश्यक भाग = <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mfrac><mrow><mn>27</mn></mrow><mrow><mn>2</mn></mrow></mfrac></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>9</mn></mfrac></math></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "3",
                    section: "17",
                    question_en: "<p>3. Sunil and Pradeep can complete a work in 5 days and 15 days, respectively. They both work for one day and then Sunil leaves. In how many days is the remaining work completed by Pradeep ?</p>",
                    question_hi: "<p>3. सुनील और प्रदीप एक काम को क्रमशः 5 दिन और 15 दिन में पूरा कर सकते है। वे दोनों एक दिन काम करते हैं और फिर सुनील काम छोड़ देता है। प्रदीप शेष काम को कितने दिनों में पूरा करेगा ?</p>",
                    options_en: ["<p>12 days</p>", "<p>11 days</p>", 
                                "<p>9 days</p>", "<p>7 days</p>"],
                    options_hi: ["<p>12 दिन</p>", "<p>11 दिन</p>",
                                "<p>9 दिन</p>", "<p>7 दिन</p>"],
                    solution_en: "<p>3.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731488296724.png\" alt=\"rId6\" width=\"284\" height=\"148\"><br>According to question,<br>Efficiency of Sunil and Pradeep = 3 + 1 = 4 units<br>Work done by both in a day = 4 &times; 1 = 4 units<br>Remaining work = 15 - 4 = 11 units.<br>Required time = <math display=\"inline\"><mfrac><mrow><mn>11</mn></mrow><mrow><mn>1</mn></mrow></mfrac></math> = 11 days.</p>",
                    solution_hi: "<p>3.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731488296825.png\" alt=\"rId7\" width=\"252\" height=\"137\"><br>प्रश्न के अनुसार,<br>सुनील और प्रदीप की कार्य क्षमता = 3 + 1 = 4 इकाई<br>दोनों द्वारा एक दिन में किया गया कार्य = 4 &times; 1 = 4 इकाई<br>शेष कार्य = 15 - 4 = 11 इकाई।<br>आवश्यक समय = <math display=\"inline\"><mfrac><mrow><mn>11</mn></mrow><mrow><mn>1</mn></mrow></mfrac></math> = 11 दिन</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "4",
                    section: "17",
                    question_en: "<p>4. A can complete <math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> part of a work in 10 days. B is 5 times as efficient as A. In how many days will B alone complete the same work ?</p>",
                    question_hi: "<p>4. A किसी काम का <math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> भाग 10 दिनों में पूरा कर सकता है। B, A से 5 गुना कुशल है। B अकेले उसी काम को कितने दिनों मे पूरा करेगा ?</p>",
                    options_en: ["<p>2.5 days</p>", "<p>2 days</p>", 
                                "<p>1.5 days</p>", "<p>3 days</p>"],
                    options_hi: ["<p>2.5 दिन</p>", "<p>2 दिन</p>",
                                "<p>1.5 दिन</p>", "<p>3 दिन</p>"],
                    solution_en: "<p>4.(a) A can complete <math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> part of a work = 10 days <br>A can complete work = <math display=\"inline\"><mfrac><mrow><mn>50</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> days <br>According to question,<br>Ratio of efficiency of A and B <math display=\"inline\"><mo>&#8594;</mo></math> 1 : 5<br>Total work = <math display=\"inline\"><mfrac><mrow><mn>50</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> &times; 1 = <math display=\"inline\"><mfrac><mrow><mn>50</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math><br>Now, B alone complete the work = <math display=\"inline\"><mfrac><mrow><mn>50</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>5</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>50</mn><mn>20</mn></mfrac></math> = 2.5 days</p>",
                    solution_hi: "<p>4.(a) A किसी कार्य का <math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> भाग में पूरा कर सकता है = 10 दिन&nbsp;<br>A कार्य पूरा कर सकता है = <math display=\"inline\"><mfrac><mrow><mn>50</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> दिन<br>प्रश्न के अनुसार,<br>A और B की क्षमता का अनुपात <math display=\"inline\"><mo>&#8594;</mo></math> 1 : 5<br>कुल कार्य = <math display=\"inline\"><mfrac><mrow><mn>50</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> &times; 1 = <math display=\"inline\"><mfrac><mrow><mn>50</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math><br>अब, B अकेले काम पूरा करता है = <math display=\"inline\"><mfrac><mrow><mn>50</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>5</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>50</mn><mn>20</mn></mfrac></math> = 2.5 दिन</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "5",
                    section: "17",
                    question_en: "5. B alone can complete half of a work in 150 days. In 30 days what percentage of the work will be completed by B?",
                    question_hi: "5. B अकेले किसी काम का आधा भाग 150 दिनों में पूरा कर सकता है। 30 दिनों में B द्वारा उसी काम का कितना प्रतिशत पूरा किया जा सकता है?",
                    options_en: [" 11 percent ", " 15 percent ", 
                                " 10 percent ", " 12.5 percent"],
                    options_hi: [" 11 प्रतिशत", " 15 प्रतिशत",
                                " 10 प्रतिशत", " 12.5 प्रतिशत"],
                    solution_en: "5.(c) B alone complete half of a work = 150 days <br />B complete whole work = 300 days<br />So, Required percentage = <math display=\"inline\"><mfrac><mrow><mn>30</mn></mrow><mrow><mn>300</mn></mrow></mfrac></math> × 100 = 10%",
                    solution_hi: "5.(c) B अकेले काम का आधा भाग पूरा करता है = 150 दिन<br />B पूरा कार्य  करता है  = 300 दिन<br />अत: आवश्यक प्रतिशत = <math display=\"inline\"><mfrac><mrow><mn>30</mn></mrow><mrow><mn>300</mn></mrow></mfrac></math> × 100 = 10%",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "6",
                    section: "17",
                    question_en: "6. If D can complete 10 percent of a work in 3 days, then in how many days can D complete the entire work?",
                    question_hi: "6. यदि D किसी काम का 10 प्रतिशत भाग 3 दिन में पूरा कर सकता है, तो D संपूर्ण काम कितने दिनों में पूरा कर सकता है?",
                    options_en: [" 30 days", " 42 days", 
                                " 31 days", " 36 days"],
                    options_hi: [" 30 दिन ", " 42 दिन ",
                                " 31 दिन ", " 36 दिन"],
                    solution_en: "6.(a)<br />Time taken by D to complete the <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>10</mn></mrow></mfrac></math> part of a work = 3 days <br />Therefore total time taken by D to complete the whole work = 3 <math display=\"inline\"><mo>×</mo></math> 10 = 30 days ",
                    solution_hi: "6.(a)<br />किसी कार्य के <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>10</mn></mrow></mfrac></math> वें भाग को पूरा करने में D द्वारा लिया गया समय = 3 दिन<br />अतः पूरे कार्य को पूरा करने में D द्वारा लिया गया कुल समय = 3 <math display=\"inline\"><mo>×</mo></math> 10 = 30 दिन",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "7",
                    section: "17",
                    question_en: "<p>7. Three friends can complete a puzzle in 4 hours. If they call two more friends for help, how much time will it take them to complete the same puzzle ?</p>",
                    question_hi: "<p>7. तीन मित्र एक पहेली को 4 घंटे में पूरा कर सकते हैं। यदि वे मदद के लिए दो और मित्रों को बुलाते हैं, तो उन्हे उसी पहेली को पूरा करने में कितना समय लगेगा ?</p>",
                    options_en: ["<p>130 minutes</p>", "<p>120 minutes</p>", 
                                "<p>132 minutes</p>", "<p>144 minutes</p>"],
                    options_hi: ["<p>130 मिनट</p>", "<p>120 मिनट</p>",
                                "<p>132 मिनट</p>", "<p>144 मिनट</p>"],
                    solution_en: "<p>7.(d)<br><math display=\"inline\"><msub><mrow><mi>M</mi></mrow><mrow><mn>1</mn></mrow></msub><mo>&#215;</mo><mi>&#160;</mi><msub><mrow><mi>H</mi></mrow><mrow><mn>1</mn></mrow></msub></math> = M<sub>2 </sub>&times; H<sub>2</sub><br>3 &times; 4 = (3 + 2) &times; <math display=\"inline\"><mi>x</mi></math><br><math display=\"inline\"><mi>x</mi></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>12</mn><mn>5</mn></mfrac></math> hours<br>Hence, required time = <math display=\"inline\"><mfrac><mrow><mn>12</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> &times; 60 = 144 minute</p>",
                    solution_hi: "<p>7.(d)<br><math display=\"inline\"><msub><mrow><mi>M</mi></mrow><mrow><mn>1</mn></mrow></msub><mo>&#215;</mo><mi>&#160;</mi><msub><mrow><mi>H</mi></mrow><mrow><mn>1</mn></mrow></msub></math> = M<sub>2 </sub>&times; H<sub>2</sub><br>3 &times; 4 = (3 + 2) &times; <math display=\"inline\"><mi>x</mi></math><br><math display=\"inline\"><mi>x</mi></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>12</mn><mn>5</mn></mfrac></math> घंटे <br>अत: आवश्यक समय = <math display=\"inline\"><mfrac><mrow><mn>12</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> &times; 60 = 144 मिनट</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "8",
                    section: "17",
                    question_en: "<p>8. A alone can complete a work in <math display=\"inline\"><mfrac><mrow><mn>20</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> days. A and B together can complete the same work in 5 days. How much time will B take to complete the work alone ?</p>",
                    question_hi: "<p>8. A अकेले किसी काम को <math display=\"inline\"><mfrac><mrow><mn>20</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> दिनों में पूरा कर सकता है। A और B मिलकर उसी काम को 5 दिनों में पूरा कर सकते हैं। B को अकेले काम पूरा करने में कितना समय लगेगा ?</p>",
                    options_en: ["<p>22 Days</p>", "<p>20 Days</p>", 
                                "<p>15 Days</p>", "<p>18 Days</p>"],
                    options_hi: ["<p>22 दिन</p>", "<p>20 दिन</p>",
                                "<p>15 दिन</p>", "<p>18 दिन</p>"],
                    solution_en: "<p>8.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731488296925.png\" alt=\"rId8\" width=\"218\" height=\"151\"><br>Efficiency of B = 4 - 3 = 1<br>Therefore, time taken by B = <math display=\"inline\"><mfrac><mrow><mn>20</mn></mrow><mrow><mn>1</mn></mrow></mfrac></math> = 20 days</p>",
                    solution_hi: "<p>68.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731488297054.png\" alt=\"rId9\" width=\"185\" height=\"155\"><br>B की क्षमता = 4 - 3 = 1<br>B द्वारा लिया गया समय = <math display=\"inline\"><mfrac><mrow><mn>20</mn></mrow><mrow><mn>1</mn></mrow></mfrac></math> = 20 दिन</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "9",
                    section: "17",
                    question_en: "<p>9. C and D together can complete a work in 40 days. C is 40 percent more efficient than D. C alone can complete the same work in how many days ?</p>",
                    question_hi: "<p>9. C और D मिलकर एक काम को 40 दिनों में पूरा कर सकते हैं। C, D से 40 प्रतिशत अधिक कुशल है। C अकेले उसी काम को कितने दिनों में पूरा कर सकता है ?</p>",
                    options_en: ["<p><math display=\"inline\"><mfrac><mrow><mn>470</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> days</p>", "<p><math display=\"inline\"><mfrac><mrow><mn>480</mn></mrow><mrow><mn>11</mn></mrow></mfrac></math> days</p>", 
                                "<p><math display=\"inline\"><mfrac><mrow><mn>480</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> days</p>", "<p><math display=\"inline\"><mfrac><mrow><mn>460</mn></mrow><mrow><mn>9</mn></mrow></mfrac></math> days</p>"],
                    options_hi: ["<p><math display=\"inline\"><mfrac><mrow><mn>470</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> दिन</p>", "<p><math display=\"inline\"><mfrac><mrow><mn>480</mn></mrow><mrow><mn>11</mn></mrow></mfrac></math> दिन</p>",
                                "<p><math display=\"inline\"><mfrac><mrow><mn>480</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> दिन</p>", "<p><math display=\"inline\"><mfrac><mrow><mn>460</mn></mrow><mrow><mn>9</mn></mrow></mfrac></math> दिन</p>"],
                    solution_en: "<p>9.(c)<br>According to the question,<br>Efficiency C : D = 7 : 5<br>Total work = 40 &times; (7 + 5) = 480 unit<br>So, time taken by C to complete the work = <math display=\"inline\"><mfrac><mrow><mn>480</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> days</p>",
                    solution_hi: "<p>9.(c)<br>प्रश्न के अनुसार,<br>दक्षता C : D = 7 : 5<br>कुल कार्य = 40 &times; (7 + 5) = 480 इकाई<br>अतः, C द्वारा कार्य पूरा करने में लिया गया समय = <math display=\"inline\"><mfrac><mrow><mn>480</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> दिन</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "10",
                    section: "17",
                    question_en: "10.  Raj is 20 percent less efficient than Rahul. If Rahul can make a computer in 20 days, then Raj can make the same computer in how many days ?",
                    question_hi: "10.  राज, राहुल से 20 प्रतिशत कम कुशल है। यदि राहुल एक कंप्यूटर को 20 दिनों में बना सकता है, तो राज उसी कंप्यूटर को कितने दिनों में बना सकता है ?",
                    options_en: [" 27 days ", " 35 days ", 
                                " 30 days ", " 25 days"],
                    options_hi: [" 27 दिन", " 35 दिन",
                                " 30 दिन", " 25 दिन"],
                    solution_en: "10.(d)<br />According to the question,<br />Efficiency <math display=\"inline\"><mo>⇒</mo></math> Raj : Rahul = 4  :  5<br />Rahul can make a computer in 20 days <br />Then, total work = 20 × 5 = 100 unit<br />So, required time = <math display=\"inline\"><mfrac><mrow><mn>100</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> = 25 days ",
                    solution_hi: "10.(d)<br />प्रश्न के अनुसार,<br />दक्षता <math display=\"inline\"><mo>⇒</mo></math> राज : राहुल = 4 :5<br />राहुल 20 दिनों में एक कंप्यूटर बना सकता है<br />तब, कुल कार्य = 20 × 5 = 100 इकाई<br />अतः, आवश्यक समय  = <math display=\"inline\"><mfrac><mrow><mn>100</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> = 25 दिन",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "11",
                    section: "17",
                    question_en: "11. A  work was finished by Amit, Sumit and Vinit together. Amit and Sumit together finished 70 percent part  of the work and Sumit and Vinit together finished 50 percent part of the work. Who among the three is most efficient ? ",
                    question_hi: "11. अमित, सुमित और विनीत मिलकर एक काम पूरा करते हैं। अमित और सुमित मिलकर काम का 70 प्रतिशत भाग पूरा करते है तथा सुमित और विनीत मिलकर उस काम का 50 प्रतिशत भाग पूरा करते हैं। तीनों में से कौन सर्वाधिक कुशल है ?",
                    options_en: ["  Vinit", "  Sumit", 
                                "  Amit", " All are equal"],
                    options_hi: ["  विनीत", "  सुमित",
                                "  अमित", " सभी समान रूप से कुशल हैं"],
                    solution_en: "11.(c)<br />Let work done Amit, Sumit and Vinit be a, b and c respectively.<br />Total work = 100 units<br />According to question,<br />a + b + c = 100 units<br />a + b = 70 units<br />Then, c = 30 units<br />Now, b + c = 50 units<br />b = 20 units and a = 50 units.<br />Here, work done by Amit is highest.<br />Therefore, Amit is most efficient.",
                    solution_hi: "11.(c)<br />माना अमित, सुमित और विनीत द्वारा किया गया कार्य क्रमशः a, b और c है।<br />कुल कार्य = 100 इकाई<br />प्रश्न के अनुसार,<br />a + b + c = 100 इकाई<br />a + b = 70 इकाई<br />फिर, c = 30 इकाई<br />अब, b + c = 50 इकाई<br />b = 20 इकाई और a = 50 इकाई।<br />यहां अमित द्वारा किया गया कार्य सबसे अधिक है।<br />इसलिए, अमित सबसे कुशल है।",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "12",
                    section: "17",
                    question_en: "<p>12. X and Y together can complete a work in 20 days. Y alone can complete the same work in 25 days. In how many days will X alone complete the same work ?</p>",
                    question_hi: "<p>12. X और Y साथ मिलकर एक काम को 20 दिनों में पूरा कर सकते हैं। Y अकेले उसी काम को 25 दिनों में पूरा कर सकता है। X अकेले उसी काम को कितने दिनों में पूरा करेगा ?</p>",
                    options_en: ["<p>110 days</p>", "<p>120 days</p>", 
                                "<p>150 days</p>", "<p>100 days</p>"],
                    options_hi: ["<p>110 दिन</p>", "<p>120 दिन</p>",
                                "<p>150 दिन</p>", "<p>100 दिन</p>"],
                    solution_en: "<p>12.(d)<br>According to question,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731488297283.png\" alt=\"rId10\" width=\"210\" height=\"151\"><br>Required time = <math display=\"inline\"><mfrac><mrow><mn>100</mn></mrow><mrow><mo>(</mo><mn>5</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>4</mn><mo>)</mo></mrow></mfrac></math> = 100 days</p>",
                    solution_hi: "<p>12.(d)<br>प्रश्न के अनुसार,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731488297534.png\" alt=\"rId11\" width=\"216\" height=\"162\"><br>आवश्यक समय = <math display=\"inline\"><mfrac><mrow><mn>100</mn></mrow><mrow><mo>(</mo><mn>5</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>4</mn><mo>)</mo></mrow></mfrac></math> = 100 दिन</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "13",
                    section: "17",
                    question_en: "<p>13. Sohan alone can complete a work in 18 days and Mohan alone can complete the same work in 36 days. In how many days will both of them together complete the same work ?</p>",
                    question_hi: "<p>13. सोहन अकेले एक काम को 18 दिनों में पूरा कर सकता है और मोहन अकेले उसी काम को 36 दिनों में पूरा कर सकता है। वे दोनों मिलकर उसी काम को कितने दिनों में पूरा करेंगे ?</p>",
                    options_en: ["<p>12 days</p>", "<p>15 days</p>", 
                                "<p>14 days</p>", "<p>11 days</p>"],
                    options_hi: ["<p>12 दिन</p>", "<p>15 दिन</p>",
                                "<p>14 दिन</p>", "<p>11 दिन</p>"],
                    solution_en: "<p>13.(a)<br>According to question,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731488297643.png\" alt=\"rId12\" width=\"217\" height=\"144\"><br>Required time = <math display=\"inline\"><mfrac><mrow><mn>36</mn></mrow><mrow><mn>2</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>1</mn></mrow></mfrac></math> = 12 days.</p>",
                    solution_hi: "<p>13.(a)<br>प्रश्न के अनुसार,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731488297792.png\" alt=\"rId13\" width=\"199\" height=\"154\"><br>आवश्यक समय = <math display=\"inline\"><mfrac><mrow><mn>36</mn></mrow><mrow><mn>2</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>1</mn></mrow></mfrac></math> = 12 दिन।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "14",
                    section: "17",
                    question_en: "<p>14. A, B and C working alone can do a piece of work in 10, 12 and 15 days respectively. All three of them started the work together but B left the work 3 days before its completion. In how many days was the work completed ?</p>",
                    question_hi: "<p>14. A, B और C अकेले-अकेले तौर पर एक काम को क्रमशः 10, 12 और 15 दिनों में कर सकते हैं। उन तीनों ने एक साथ काम शुरू किया लेकिन B ने काम पूरा होने से 3 दिन पहले काम छोड़ दिया। काम कितने दिनों में पूरा हुआ ?</p>",
                    options_en: ["<p>5 days</p>", "<p>3 days</p>", 
                                "<p>2 days</p>", "<p>4 days</p>"],
                    options_hi: ["<p>5 दिन</p>", "<p>3 दिन</p>",
                                "<p>2 दिन</p>", "<p>4 दिन</p>"],
                    solution_en: "<p>14.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731488298008.png\" alt=\"rId14\" width=\"207\" height=\"137\"><br>As per question, B leaves before 3 days of completion of work.<br>Let us assume B works for 3 days.<br>Then, total work = 60 + (3 &times; 5) = 75 unit<br>Therefore, required time = <math display=\"inline\"><mfrac><mrow><mn>75</mn></mrow><mrow><mn>6</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>5</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>4</mn></mrow></mfrac></math> = 5 days.</p>",
                    solution_hi: "<p>14.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731488298157.png\" alt=\"rId15\" width=\"198\" height=\"145\"><br>प्रश्न के अनुसार, B काम पूरा होने से 3 दिन पहले निकल जाता है।<br>मान लें कि B 3 दिनों तक काम करता है।<br>तब, कुल कार्य = 60 + (3 &times; 5) = 75 इकाई<br>अत: आवश्यक समय = <math display=\"inline\"><mfrac><mrow><mn>75</mn></mrow><mrow><mn>6</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>5</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>4</mn></mrow></mfrac></math> = 5 दिन।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "15",
                    section: "misc",
                    question_en: "<p>15. John can complete a task in 6 hours, and Mark can complete the same task in 8 hours. How long will it take for both of them working together to finish the task ?</p>",
                    question_hi: "<p>15. जॉन एक काम को 6 घंटे पूरा कर सकता है, और मार्क उसी काम को 8 घंटे में पूरा कर सकता है। दोनों को एक साथ मिलकर काम पूरा करने में कितना समय लगेगा ?</p>",
                    options_en: ["<p><math display=\"inline\"><mfrac><mrow><mn>31</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> hours</p>", "<p><math display=\"inline\"><mfrac><mrow><mn>21</mn></mrow><mrow><mn>11</mn></mrow></mfrac></math> hours</p>", 
                                "<p><math display=\"inline\"><mfrac><mrow><mn>40</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> hours</p>", "<p><math display=\"inline\"><mfrac><mrow><mn>24</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> hours</p>"],
                    options_hi: ["<p><math display=\"inline\"><mfrac><mrow><mn>31</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> घंटे</p>", "<p><math display=\"inline\"><mfrac><mrow><mn>21</mn></mrow><mrow><mn>11</mn></mrow></mfrac></math> घंटे</p>",
                                "<p><math display=\"inline\"><mfrac><mrow><mn>40</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> घंटे</p>", "<p><math display=\"inline\"><mfrac><mrow><mn>24</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> घंटे</p>"],
                    solution_en: "<p>15.(d)<br>According to question,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731488298304.png\" alt=\"rId16\" width=\"211\" height=\"170\"><br>Required time = <math display=\"inline\"><mfrac><mrow><mn>24</mn></mrow><mrow><mn>4</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>3</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>24</mn><mn>7</mn></mfrac></math> hours.</p>",
                    solution_hi: "<p>15.(d)<br>प्रश्न के अनुसार,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731488298462.png\" alt=\"rId17\" width=\"201\" height=\"180\"><br>आवश्यक समय = <math display=\"inline\"><mfrac><mrow><mn>24</mn></mrow><mrow><mn>4</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>3</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>24</mn><mn>7</mn></mfrac></math>&nbsp;घंटे.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>