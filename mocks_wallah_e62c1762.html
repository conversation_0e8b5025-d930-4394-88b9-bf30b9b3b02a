<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">25:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 40</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">40</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 25 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["18"] = {
                name: "General Awareness",
                start: 0,
                end: 38
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="18">General Awareness</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 39,
                end: 39
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "18",
                    question_en: "1. Name the campaign launched in January 2018 by the Union Minister for the environment, forest and climate change, which lauds small positive actions performed by individuals or organizations to strengthen the cause of environmental protection. ",
                    question_hi: "1. केंद्रीय पर्यावरण, वन और जलवायु परिवर्तन मंत्री द्वारा जनवरी 2018 में शुरू किए गए अभियान का नाम बताइए, जो पर्यावरण संरक्षण के कारण को मजबूत करने के लिए व्यक्तियों या संगठनों द्वारा किए गए छोटे सकारात्मक कार्यों की सराहना करता है।",
                    options_en: [" Champions of the Earth ", " Green Skill Development Programme", 
                                " Green Good Deeds", " Clean Air Campaign"],
                    options_hi: [" चैंपियंस ऑफ़ द एअर्थ  ", " हरित कौशल विकास कार्यक्रम",
                                " ग्रीन गुड डीड्स", " स्वच्छ वायु अभियान"],
                    solution_en: "1.(c) \'Green Good Deeds\' initiative to promote environmental awareness and to mobilize people\'s participation for conservation of the environment. Green Skill Development Programme (GSDP) will enhance demographic dividends across the country. The Clean Air Campaign is a not-for-profit organization that motivates Georgians to take action to improve air quality and reduce traffic congestion. Champions of the Earth award, the UN’s highest environmental honor. ",
                    solution_hi: "1.(c) पर्यावरण जागरूकता को बढ़ावा देने और पर्यावरण के संरक्षण के लिए लोगों की भागीदारी को जुटाने के लिए \'ग्रीन गुड डीड्स\' पहल है। हरित कौशल विकास कार्यक्रम (जीएसडीपी) देश भर में जनसांख्यिकीय लाभांश बढ़ाएगा। स्वच्छ वायु अभियान एक गैर-लाभकारी संगठन है जो जॉर्जियाई लोगों को वायु गुणवत्ता में सुधार और यातायात की भीड़ को कम करने के लिए कार्रवाई करने के लिए प्रेरित करता है। चैंपियंस ऑफ द अर्थ अवार्ड, संयुक्त राष्ट्र का सर्वोच्च पर्यावरण सम्मान है।",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "2",
                    section: "18",
                    question_en: "<p>2. Which part of the human body is formed by the fusing of the ilium, ischium and the pubis ?</p>",
                    question_hi: "<p>2. मानव शरीर का कौन सा भाग इलियम, इस्चियम और प्यूबिस के संलयन से बनता है ?</p>",
                    options_en: ["<p>Jaw</p>", "<p>Hip bone</p>", 
                                "<p>Cranium</p>", "<p>Feet</p>"],
                    options_hi: ["<p>जबड़ा</p>", "<p>नितम्बास्थि</p>",
                                "<p>कपाल</p>", "<p>पैर</p>"],
                    solution_en: "<p>2.(b) Hip bone is part of the human body that is formed by the fusing of the ilium, ischium and the pubis. The adult human skeleton is made up of 206 bones.The femur(Thigh bone) is the strongest bone in the body, and it is the longest bone in the human body. The \"stapes\" in the middle ear is the smallest named bone in the human body.</p>",
                    solution_hi: "<p>2.(b) कूल्हे की हड्डी मानव शरीर का वह हिस्सा है जो इलियम, इस्चियम और प्यूबिस के संलयन से बनता है।वयस्क मानव कंकाल 206 हड्डियों से बना होता है। फीमर (जांघ की हड्डी) शरीर की सबसे मजबूत हड्डी है, और यह मानव शरीर की सबसे लंबी हड्डी है।मध्य कान में \"स्टेप\" मानव शरीर में सबसे छोटी नामित हड्डी है।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "3",
                    section: "18",
                    question_en: "<p>3. In which year did AR Rahman win two Oscars ?</p>",
                    question_hi: "<p>3. ए.आर. रहमान ने किस वर्ष दो ऑस्कर जीते ?</p>",
                    options_en: ["<p>2011</p>", "<p>2010</p>", 
                                "<p>2009</p>", "<p>2017</p>"],
                    options_hi: ["<p>2011</p>", "<p>2010</p>",
                                "<p>2009</p>", "<p>2017</p>"],
                    solution_en: "<p>3.(c) AR Rahman won two Oscars in 2009. In 2009, two Academy Awards, one for the original score for \'Slumdog Millionaire\' and the other for the hugely popular global hit &lsquo;Jai ho\' song from the same film.</p>",
                    solution_hi: "<p>3.(c) ए.आर. रहमान ने 2009 में दो ऑस्कर जीते थे। 2009 में, दो अकादमी पुरस्कार, एक \'स्लमडॉग मिलियनेयर\' के लिए मूल स्कोर के लिए और दूसरा एक ही फिल्म के बेहद लोकप्रिय वैश्विक हिट \'जय हो\' गीत के लिए।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "4",
                    section: "18",
                    question_en: "<p>4. Tsangpo and Dihang are other names of which river ?</p>",
                    question_hi: "<p>4. सांगपो और दिहांग किस नदी के अन्य नाम हैं ?</p>",
                    options_en: ["<p>Ganga</p>", "<p>Indus</p>", 
                                "<p>Yamuna</p>", "<p>Brahmaputra</p>"],
                    options_hi: ["<p>गंगा</p>", "<p>सिंधु</p>",
                                "<p>यमुना</p>", "<p>ब्रह्मपुत्र</p>"],
                    solution_en: "<p>4.(d) The Brahmaputra is also known as the Yarlung Tsangpo in Tibet, China. The Siang/Dihang River in Arunachal Pradesh, and Luit, Dilao in Assam, is a transboundary river that flows through Tibet, India, and Bangladesh.</p>",
                    solution_hi: "<p>4.(d) ब्रह्मपुत्र को तिब्बत, चीन में यारलुंग त्सांगपो के नाम से भी जाना जाता है। अरुणाचल प्रदेश में सियांग / दिहांग नदी, और असम में लुइट, दिलाओ, एक ट्रांसबाउंड्री नदी है जो तिब्बत, भारत और बांग्लादेश से होकर बहती है।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "5",
                    section: "18",
                    question_en: "<p>5. Which of the following methods did Holt Mackenzie develop for collecting revenue in India ?</p>",
                    question_hi: "<p>5. भारत में राजस्व एकत्र करने के लिए होल्ट मैकेंज़ी ने निम्नलिखित में से कौन सी विधि विकसित की ?</p>",
                    options_en: ["<p>Ryotwari system</p>", "<p>Mahalwari system</p>", 
                                "<p>Zamindari system</p>", "<p>Permanent settlement system</p>"],
                    options_hi: ["<p>रैयतवाड़ी व्यवस्था</p>", "<p>महलवाड़ी सिस्टम</p>",
                                "<p>जमींदारी व्यवस्था</p>", "<p>स्थायी बंदोबस्त प्रणाली</p>"],
                    solution_en: "<p>5.(b) In 1822, Englishman Holt Mackenzie devised a new system known as the Mahalwari System in the North-Western Provinces of the Bengal Presidency. The ryotwari system was a land revenue system in British India that was introduced by Sir Thomas Munro in 1820. The Zamindari System was introduced by Cornwallis in 1793 through the Permanent Settlement Act. The Permanent Settlement of Bengal was brought into effect by the East India Company headed by Governor-General Lord Cornwallis in 1793.</p>",
                    solution_hi: "<p>5.(b) 1822 में, अंग्रेज होल्ट मैकेंज़ी ने बंगाल प्रेसीडेंसी के उत्तर-पश्चिमी प्रांतों में महलवारी प्रणाली के रूप में जानी जाने वाली एक नई प्रणाली तैयार की। रैयतवारी प्रणाली ब्रिटिश भारत में एक भू-राजस्व प्रणाली थी जिसे सर थॉमस मुनरो ने 1820 में पेश किया था। 1793 में स्थायी बंदोबस्त अधिनियम के माध्यम से कार्नवालिस द्वारा जमींदारी प्रणाली की शुरुआत की गई थी।1793 में गवर्नर-जनरल लॉर्ड कार्नवालिस की अध्यक्षता में ईस्ट इंडिया कंपनी द्वारा बंगाल का स्थायी बंदोबस्त लागू किया गया था।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "6",
                    section: "18",
                    question_en: "<p>6. Name the first month of the national calendar of India based on the Saka Era which was adopted from 22 March 1957 along with the Gregorian calendar for the official purpose.</p>",
                    question_hi: "<p>6. शक युग पर आधारित भारत के राष्ट्रीय कैलेंडर के पहले महीने का नाम बताइए जिसे आधिकारिक उद्देश्य के लिए ग्रेगोरियन कैलेंडर के साथ 22 मार्च 1957 से अपनाया गया था।</p>",
                    options_en: ["<p>Magha</p>", "<p>Vaishakha</p>", 
                                "<p>Chaitra</p>", "<p>Phalguna</p>"],
                    options_hi: ["<p>माघ</p>", "<p>वैशाख</p>",
                                "<p>चैत्र</p>", "<p>फाल्गुन</p>"],
                    solution_en: "<p>6.(c) Chaitra is the first month of the national calendar of India based on the Saka Era which was adopted on 22 March 1957 along with the Gregorian calendar for the official purpose. Chaitra, Vaishakha, Jyeshtha, Ashadha, Shravana, Bhadra, Ashwin, Kartika Agrahayana, Pausha, Magha, Phalguna.</p>",
                    solution_hi: "<p>6.(c) चैत्र शक युग पर आधारित भारत के राष्ट्रीय कैलेंडर का पहला महीना है जिसे आधिकारिक उद्देश्य के लिए ग्रेगोरियन कैलेंडर के साथ 22 मार्च 1957 को अपनाया गया था। चैत्र, वैशाख, ज्येष्ठ, आषाढ़, श्रवण, भाद्र, अश्विन, कार्तिक अग्रहयण, पौष, माघ, फाल्गुन।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "7",
                    section: "18",
                    question_en: "<p>7. Which was the first social media site ?</p>",
                    question_hi: "<p>7. पहली सोशल मीडिया साइट कौन सी थी ?</p>",
                    options_en: ["<p>LinkedIn</p>", "<p>Six Degrees</p>", 
                                "<p>Myspace</p>", "<p>Friendster</p>"],
                    options_hi: ["<p>लिंक्डइन</p>", "<p>सिक्स डिग्री</p>",
                                "<p>माय स्पेस</p>", "<p>फ्रेंडस्टर</p>"],
                    solution_en: "<p>7.(b) Six Degrees is widely considered to be the very first social networking site. Founded by Andrew Weinreich in May 1996.</p>",
                    solution_hi: "<p>7.(b) सिक्स डिग्री को व्यापक रूप से पहली सोशल नेटवर्किंग साइट माना जाता है। मई 1996 में एंड्रयू वेनरिच द्वारा स्थापित।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "8",
                    section: "18",
                    question_en: "<p>8. What was the previous name of the Department of AYUSH ?</p>",
                    question_hi: "<p>8. आयुष विभाग का पुराना नाम क्या था ?</p>",
                    options_en: ["<p>Department of Health Science</p>", "<p>Department of Pharmaceuticals</p>", 
                                "<p>Department of Health and Family Welfare</p>", "<p>Department of Indian System of Medicine and Homeopathy</p>"],
                    options_hi: ["<p>स्वास्थ्य विज्ञान विभाग</p>", "<p>फार्मास्यूटिकल्स विभाग</p>",
                                "<p>स्वास्थ्य और परिवार कल्याण विभाग</p>", "<p>भारतीय चिकित्सा प्रणाली और होम्योपैथी विभाग</p>"],
                    solution_en: "<p>8.(d) Department of Indian System of Medicine and Homeopathy was the previous name of the Department of AYUSH. AYUSH is the acronym of the medical systems that are being practiced in India such as Ayurveda, Yoga &amp; Naturopathy, Unani, Siddha and Homeopathy. It was founded on 9 November 2014.</p>",
                    solution_hi: "<p>8.(d) आयुष विभाग का पुराना नाम भारतीय चिकित्सा पद्धति और होम्योपैथी विभाग था। आयुष आयुर्वेद, योग और प्राकृतिक चिकित्सा, यूनानी, सिद्ध और होम्योपैथी जैसे भारत में प्रचलित चिकित्सा प्रणालियों का संक्षिप्त रूप है। इसकी स्थापना 9 नवंबर 2014 को हुई थी।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "9",
                    section: "18",
                    question_en: "9. Who among the following scientists received the Nobel Prize for physiology and medicine for the discovery of a bacterium, Helicobacter Pylori which was responsible for peptic ulcers. ",
                    question_hi: "9. निम्नलिखित में से किस वैज्ञानिक को पेप्टिक अल्सर के लिए जिम्मेदार जीवाणु हेलिकोबैक्टर पाइलोरी की खोज के लिए शरीर विज्ञान और चिकित्सा के लिए नोबेल पुरस्कार मिला।",
                    options_en: [" James P. Allison and Tasuku Honjo", " Robin Warren and Barry Marshall", 
                                " William C. Campbell and Satoshi Omura ", " Bruce A Beutler and Jules A Hoffmann"],
                    options_hi: [" जेम्स पी. एलीसन और तासुकु होंजो", " रॉबिन वारेन और बैरी मार्शल",
                                " विलियम सी कैंपबेल और सतोशी ओमुरा ", " ब्रूस ए बीटलर और जूल्स ए हॉफमैन"],
                    solution_en: "9.(b) Robin Warren and Barry Marshall received the Nobel Prize for physiology and medicine for the discovery of a bacterium, Helicobacter Pylori which was responsible for peptic ulcers in 2005. The Nobel Prize in Physiology or Medicine 2021 was awarded jointly to David Julius and Ardem Patapoutian \"for their discoveries of receptors for temperature and touch.",
                    solution_hi: "9.(b) रॉबिन वारेन और बैरी मार्शल को एक जीवाणु, हेलिकोबैक्टर पाइलोरी की खोज के लिए शरीर विज्ञान और चिकित्सा के लिए नोबेल पुरस्कार मिला, जो 2005 में पेप्टिक अल्सर के लिए जिम्मेदार था। फिजियोलॉजी या मेडिसिन में नोबेल पुरस्कार 2021 को संयुक्त रूप से डेविड जूलियस और अर्डेम पटापाउटियन को \"तापमान और स्पर्श के लिए रिसेप्टर्स की उनकी खोजों के लिए\" से सम्मानित किया गया था।",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "10",
                    section: "18",
                    question_en: "<p>10. Which fundamental right is violated if a group of people are NOT given permission to open a Telugu medium school in Kerala ?</p>",
                    question_hi: "<p>10. यदि लोगों के एक समूह को केरल में तेलुगु माध्यम का स्कूल खोलने की अनुमति नहीं दी जाती है तो किस मौलिक अधिकार का उल्लंघन होता है ?</p>",
                    options_en: ["<p>Cultural and Educational Rights</p>", "<p>Right to Freedom of Religion</p>", 
                                "<p>Right to Equality</p>", "<p>Right to freedom</p>"],
                    options_hi: ["<p>सांस्कृतिक और शैक्षिक अधिकार</p>", "<p>धर्म की स्वतंत्रता का अधिकार</p>",
                                "<p>समानता का अधिकार</p>", "<p>स्वतंत्रता का अधिकार</p>"],
                    solution_en: "<p>10.(a) Cultural and Educational Rights are violated if a group of people are NOT given permission to open a Telugu medium school in Kerala. <br>The Constitution guarantees six fundamental rights to Indian citizens as follows: (i) right to equality, (ii) right to freedom, (iii) right against exploitation, (iv) right to freedom of religion, (v) cultural and educational rights, and (vi) right to constitutional remedies.</p>",
                    solution_hi: "<p>10.(a) यदि लोगों के एक समूह को केरल में तेलुगु माध्यम का स्कूल खोलने की अनुमति नहीं दी जाती है, तो सांस्कृतिक और शैक्षिक अधिकारों का उल्लंघन होता है।<br>संविधान भारतीय नागरिकों को छह मौलिक अधिकारों की गारंटी देता है: (i) समानता का अधिकार, (ii) स्वतंत्रता का अधिकार, (iii) शोषण के खिलाफ अधिकार, (iv) धर्म की स्वतंत्रता का अधिकार, (v) सांस्कृतिक और शैक्षिक अधिकार, और (vi) संवैधानिक उपचार का अधिकार।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "11",
                    section: "18",
                    question_en: "11. With which country’s Ministry of Road Transport and Highways, has India signed an MoU in March 2019 Technology Co-operation in the road infrastructure sector ?",
                    question_hi: "11. किस देश के सड़क परिवहन और राजमार्ग मंत्रालय के साथ, भारत ने मार्च, 2019 में सड़क अवसंरचना क्षेत्र में प्रौद्योगिकी सहयोग के लिए एक समझौता ज्ञापन पर हस्ताक्षर किए हैं ?",
                    options_en: [" France", " Canada", 
                                " Austria", " Australia"],
                    options_hi: [" फ्रांस", " कनाडा ",
                                " ऑस्ट्रिया", " ऑस्ट्रेलिया"],
                    solution_en: "11.(c) The Ministry of Road Transport and Highways, has India signed an MoU with Austria in March 2019  Technology Co-operation in the road infrastructure sector.",
                    solution_hi: "11.(c) सड़क परिवहन और राजमार्ग मंत्रालय ने भारत ने मार्च 2019 में सड़क अवसंरचना क्षेत्र में प्रौद्योगिकी सहयोग में ऑस्ट्रिया के साथ एक समझौता ज्ञापन पर हस्ताक्षर किए हैं।",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "12",
                    section: "18",
                    question_en: "<p>12. Which was the last Indian movie to have received a nomination for the Oscar in the best foreign-language film category ?</p>",
                    question_hi: "<p>12. सर्वश्रेष्ठ विदेशी भाषा की फिल्म श्रेणी में ऑस्कर के लिए नामांकन प्राप्त करने वाली अंतिम भारतीय फिल्म कौन सी थी ?</p>",
                    options_en: ["<p>Lagaan</p>", "<p>Mother India</p>", 
                                "<p>Village Rockstars</p>", "<p>Salaam Bombay</p>"],
                    options_hi: ["<p>लगान</p>", "<p>मदर इंडिया</p>",
                                "<p>विलेज रॉकस्टार</p>", "<p>सलाम बॉम्बे</p>"],
                    solution_en: "<p>12.(a) Lagaan was the last Indian movie to have received a nomination for the Oscar in the best foreign-language film category.</p>",
                    solution_hi: "<p>12.(a) लगान सर्वश्रेष्ठ विदेशी भाषा की फिल्म श्रेणी में ऑस्कर के लिए नामांकन प्राप्त करने वाली आखिरी भारतीय फिल्म थी।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "13",
                    section: "18",
                    question_en: "<p>13. The practice of intercropping refers to:</p>",
                    question_hi: "<p>13. अंतर-फसल का अभ्यास किस से संदर्भित करता है ?</p>",
                    options_en: ["<p>Growing two or more crops in proximity</p>", "<p>Changing crops every time</p>", 
                                "<p>Growing one plant year after year</p>", "<p>Growing different crops at different points in time</p>"],
                    options_hi: ["<p>निकटता में दो या दो से अधिक फसलें उगाना</p>", "<p>हर बार फसलें बदलना</p>",
                                "<p>साल दर साल एक पौधा उगाना</p>", "<p>अलग-अलग समय पर अलग-अलग फसलें उगाना</p>"],
                    solution_en: "<p>13.(a) The practice of intercropping refers to Growing two or more crops in proximity. The most common goal of intercropping is to produce a greater yield on a given piece of land by making use of resources that would otherwise not be utilized by a single crop.</p>",
                    solution_hi: "<p>13.(a) इंटरक्रॉपिंग की प्रथा से तात्पर्य दो या दो से अधिक फसलों को निकटता में उगाने से है। इंटरक्रॉपिंग का सबसे आम लक्ष्य उन संसाधनों का उपयोग करके भूमि के एक टुकड़े पर अधिक उपज पैदा करना है जो अन्यथा एक फसल द्वारा उपयोग नहीं किया जाएगा।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "14",
                    section: "18",
                    question_en: "<p>14. Name the scheme which provides risk coverage of Rs 2 lakhs for accidental death or full disability and Rs 1 lakh for partial disability for people in the age group 18 to 70 years.</p>",
                    question_hi: "<p>14. उस योजना का नाम बताइए जो 18 से 70 वर्ष की आयु के लोगों के लिए आकस्मिक मृत्यु या पूर्ण विकलांगता के लिए 2 लाख रुपये और आंशिक विकलांगता के लिए 1 लाख रुपये का जोखिम कवरेज प्रदान करती है।</p>",
                    options_en: ["<p>Pradhan Mantri Mudra Yojana</p>", "<p>Pradhan Mantri Suraksha Bima Yojana</p>", 
                                "<p>Pradhan Mantri Jeevan Jyoti Beema Yojana</p>", "<p>Atal Pension Yojana</p>"],
                    options_hi: ["<p>प्रधानमंत्री मुद्रा योजना</p>", "<p>प्रधानमंत्री सुरक्षा बीमा योजना</p>",
                                "<p>प्रधानमंत्री जीवन ज्योति बीमा योजना</p>", "<p>अटल पेंशन योजना</p>"],
                    solution_en: "<p>14.(b) Pradhan Mantri Suraksha Bima Yojana is the scheme that provides risk coverage of Rs 2 lakhs for accidental death or full disability and Rs 1 lakh for partial disability for people in the age group 18 to 70 years. It was formally launched by Prime Minister Narendra Modi on 8 May in Kolkata.</p>",
                    solution_hi: "<p>14.(b) प्रधान मंत्री सुरक्षा बीमा योजना वह योजना है जो 18 से 70 वर्ष की आयु के लोगों के लिए आकस्मिक मृत्यु या पूर्ण विकलांगता के लिए 2 लाख रुपये और आंशिक विकलांगता के लिए 1 लाख रुपये का जोखिम कवरेज प्रदान करती है। इसे औपचारिक रूप से प्रधान मंत्री नरेंद्र मोदी द्वारा 8 मई को कोलकाता में लॉन्च किया गया था।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "15",
                    section: "18",
                    question_en: "<p>15. Which organization with its headquarters in Kolkata and 16 regional stations is responsible for surveying the faunal resources in India.</p>",
                    question_hi: "<p>15. कोलकाता में मुख्यालय और 16 क्षेत्रीय स्टेशनों के साथ कौन सा संगठन भारत में जीव संसाधनों के सर्वेक्षण के लिए जिम्मेदार है।</p>",
                    options_en: ["<p>National Institute of Animal Welfare</p>", "<p>Zoological Survey of India</p>", 
                                "<p>Botanical Survey of India</p>", "<p>Animal Welfare Board of India</p>"],
                    options_hi: ["<p>राष्ट्रीय पशु कल्याण संस्थान</p>", "<p>भारतीय प्राणी सर्वेक्षण</p>",
                                "<p>भारतीय वानस्पतिक सर्वेक्षण</p>", "<p>भारतीय पशु कल्याण बोर्ड</p>"],
                    solution_en: "<p>15.(b) Zoological Survey of India with its headquarters in Kolkata and 16 regional stations is responsible for surveying the faunal resources in India. Founded on 1 July 2016. Present Director- Dr Dhriti Banerjee.</p>",
                    solution_hi: "<p>15.(b) भारतीय प्राणी सर्वेक्षण का मुख्यालय कोलकाता में है और 16 क्षेत्रीय स्टेशन भारत में जीव-जंतुओं के संसाधनों के सर्वेक्षण के लिए जिम्मेदार हैं।1 जुलाई 2016 को स्थापित। वर्तमान निदेशक- डॉ धृति बनर्जी।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "16",
                    section: "18",
                    question_en: "<p>16. Who among the following musicians won the Best Contemporary World Music Album-Global Drum Project in the year 2008 ?</p>",
                    question_hi: "<p>16. निम्नलिखित संगीतकारों में से किसने वर्ष 2008 में &ldquo;सर्वश्रेष्ठ समकालीन विश्व संगीत एल्बम-ग्लोबल ड्रम प्रोजेक्ट जीता ?</p>",
                    options_en: ["<p>Allah Rakha</p>", "<p>Ranjit Barot</p>", 
                                "<p>Sivamani</p>", "<p>Zakir Hussain</p>"],
                    options_hi: ["<p>अल्लाह रक्खा</p>", "<p>रंजीत बरोत</p>",
                                "<p>शिवमणि</p>", "<p>जाकिर हुसैन</p>"],
                    solution_en: "<p>16.(d) Zakir Hussain won the &ldquo;Best Contemporary World Music Album-Global Drum Project in the year 2008. Ustad Zakir Hussain is an Indian tabla virtuoso, composer, percussionist, music producer and film actor.</p>",
                    solution_hi: "<p>16.(d) जाकिर हुसैन ने वर्ष 2008 में \"सर्वश्रेष्ठ समकालीन विश्व संगीत एल्बम-ग्लोबल ड्रम प्रोजेक्ट जीता। उस्ताद जाकिर हुसैन एक भारतीय तबला कलाकार, संगीतकार, तालवादक, संगीत निर्माता और फिल्म अभिनेता हैं।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "17",
                    section: "18",
                    question_en: "<p>17. Name the scheme that the Government of India announced in Budget 2019, which will provide a guaranteed income of Rs 6,000 per year to small and marginal farmers.</p>",
                    question_hi: "<p>17. उस योजना का नाम बताइए, जिसकी घोषणा भारत सरकार ने 2019 के बजट में की थी, जो छोटे और सीमांत किसानों को प्रति वर्ष 6,000 रुपये की गारंटी आय प्रदान करेगी।</p>",
                    options_en: ["<p>Pradhan Mantri Kisan Samman Nidhi</p>", "<p>Pradhan Mantri Krishi Sinchai Yojana</p>", 
                                "<p>Paramparagat Krishi Vikas Yojana</p>", "<p>Pradhan Mantri Fasal Bima Yojana</p>"],
                    options_hi: ["<p>प्रधानमंत्री किसान सम्मान निधि</p>", "<p>प्रधानमंत्री कृषि सिंचाई योजना</p>",
                                "<p>परम्परागत कृषि विकास योजना</p>", "<p>प्रधानमंत्री फसल बीमा योजना</p>"],
                    solution_en: "<p>17.(a) Pradhan Mantri Kisan Samman Nidhi is the scheme that the Government of India announced in Budget 2019, which will provide a guaranteed income of Rs 6,000 per year to small and marginal farmers. It was started by Shri Narendra Modi on 24 February 2019.</p>",
                    solution_hi: "<p>17.(a) प्रधान मंत्री किसान सम्मान निधि वह योजना है जिसे भारत सरकार ने 2019 के बजट में घोषित किया था, जो छोटे और सीमांत किसानों को प्रति वर्ष 6,000 रुपये की गारंटीकृत आय प्रदान करेगी। इसकी शुरुआत श्री नरेंद्र मोदी ने 24 फरवरी 2019 को की थी।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "18",
                    section: "18",
                    question_en: "<p>18. Bardo Chham is folk dance of:</p>",
                    question_hi: "<p>18. बार्डो छम किसका लोक नृत्य है ?</p>",
                    options_en: ["<p>Uttarakhand</p>", "<p>Sikkim</p>", 
                                "<p>Arunachal Pradesh</p>", "<p>West Bengal</p>"],
                    options_hi: ["<p>उत्तराखंड</p>", "<p>सिक्किम</p>",
                                "<p>अरुणाचल प्रदेश</p>", "<p>पश्चिम बंगाल</p>"],
                    solution_en: "<p>18.(c) Bardo Chham is folk dance of Arunachal Pradesh. Bardo Chham is a folk dance traditional to the Himalayan Buddhist Tribes of Arunachal Pradesh, India. Chham literally translates to \"Dance\" in the \"Monpa\" language. Bardo Chham is based on the stories of the triumph of good over evil. According to local beliefs, both good and evil exist within mankind.</p>",
                    solution_hi: "<p>18.(c) बार्डो छम अरुणाचल प्रदेश का लोक नृत्य है। बार्डो छम भारत के अरुणाचल प्रदेश की हिमालयी बौद्ध जनजातियों का पारंपरिक लोक नृत्य है। छम का शाब्दिक अर्थ \"मोनपा\" भाषा में \"नृत्य\" है। बार्डो छम बुराई पर अच्छाई की जीत की कहानियों पर आधारित है। स्थानीय मान्यताओं के अनुसार, मानव के भीतर अच्छाई और बुराई दोनों मौजूद हैं।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "19",
                    section: "18",
                    question_en: "<p>19. Name the Governor-general under whom a new policy of &lsquo;paramountcy&rsquo; was initiated ?</p>",
                    question_hi: "<p>19. उस गवर्नर-जनरल का नाम बताइए जिसके तहत \'सर्वोच्चता\' की एक नई नीति शुरू की गई थी ?</p>",
                    options_en: ["<p>Lord Wellesley</p>", "<p>Lord Hasting</p>", 
                                "<p>Lord Cornwallis</p>", "<p>Lord Dalhousie</p>"],
                    options_hi: ["<p>लॉर्ड वैलेस्ली</p>", "<p>लॉर्ड हेस्टिंग</p>",
                                "<p>लॉर्ड कार्नवालिस</p>", "<p>लॉर्ड डलहौजी</p>"],
                    solution_en: "<p>19.(b) The Governor-general under whom a new policy of &lsquo;paramountcy&rsquo; was initiated was Lord Hasting.</p>",
                    solution_hi: "<p>19.(b) गवर्नर-जनरल जिसके तहत \'सर्वोच्चता\' की एक नई नीति शुरू की गई थी, वह लॉर्ड हेस्टिंग थे।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "20",
                    section: "18",
                    question_en: "<p>20. In which of the following cities was India&rsquo;s first bio-refinery plant inaugurated ?</p>",
                    question_hi: "<p>20. निम्नलिखित में से किस शहर में भारत के पहले जैव-रिफाइनरी संयंत्र का उद्घाटन किया गया ?</p>",
                    options_en: ["<p>Pune</p>", "<p>Valsad</p>", 
                                "<p>Ahmedabad</p>", "<p>Hyderabad</p>"],
                    options_hi: ["<p>पुणे</p>", "<p>वलसाड</p>",
                                "<p>अहमदाबाद</p>", "<p>हैदराबाद</p>"],
                    solution_en: "<p>20.(a) India&rsquo;s first bio-refinery plant was inaugurated in Pune. Nitin Gadkari inaugurated India\'s first biorefinery plant that produces ethanol from a variety of biomass in Pune.</p>",
                    solution_hi: "<p>20.(a) भारत के पहले जैव-रिफाइनरी संयंत्र का उद्घाटन पुणे में किया गया। नितिन गडकरी ने पुणे में विभिन्न प्रकार के बायोमास से इथेनॉल का उत्पादन करने वाले भारत के पहले बायोरिफाइनरी संयंत्र का उद्घाटन किया।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "21",
                    section: "18",
                    question_en: "<p>21. Which of the following causes Kala-azar ?</p>",
                    question_hi: "<p>21. निम्नलिखित में से किसके कारण काला-अजारी होता है ?</p>",
                    options_en: ["<p>Protozoa</p>", "<p>Virus</p>", 
                                "<p>Bacteria</p>", "<p>Fungi</p>"],
                    options_hi: ["<p>प्रोटोजोआ</p>", "<p>वायरस</p>",
                                "<p>बैक्टीरिया</p>", "<p>कवक</p>"],
                    solution_en: "<p>21.(a) Kala-Azar is a slow progressing indigenous disease caused by a protozoan parasite of the genus Leishmania. In India, Leishmania Donovan is the only parasite that causes this disease.</p>",
                    solution_hi: "<p>21.(a) काला-अजार एक धीमी गति से बढ़ने वाली स्वदेशी बीमारी है जो लीशमैनिया जीन के प्रोटोजोआ परजीवी के कारण होती है। भारत में, लीशमैनिया डोनोवन एकमात्र परजीवी है जो इस बीमारी का कारण बनता है</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "22",
                    section: "18",
                    question_en: "<p>22. Which of the following is present only in plant cells ?</p>",
                    question_hi: "<p>22. निम्नलिखित में से कौन केवल पादप कोशिकाओं में मौजूद होते हैं ?</p>",
                    options_en: ["<p>Nucleus</p>", "<p>Golgi apparatus</p>", 
                                "<p>Plastids</p>", "<p>Mitochondria</p>"],
                    options_hi: ["<p>नाभिक</p>", "<p>गॉल्जीकाय</p>",
                                "<p>प्लास्टिड</p>", "<p>माइटोकॉन्ड्रिया</p>"],
                    solution_en: "<p>22.(c) Plastids are present only in plant cells. The plastid is a membrane-bound organelle found in the cells of plants, algae, and some other eukaryotic organisms. Plastids are- Chloroplasts: photosynthesis; other plastids may have developed from chloroplasts. Etioplasts are chloroplasts that have not been exposed to light. Chromoplasts: pigment synthesis and storage. Leucoplasts: colourless, make terpenes such as resin.</p>",
                    solution_hi: "<p>22.(c) प्लास्टिड केवल पादप कोशिकाओं में मौजूद होते हैं। प्लास्टिड एक झिल्ली-बाध्य अंग है जो पौधों, शैवाल और कुछ अन्य यूकेरियोटिक जीवों की कोशिकाओं में पाया जाता है। प्लास्टिड हैं- क्लोरोप्लास्ट: प्रकाश संश्लेषण; अन्य प्लास्टिड क्लोरोप्लास्ट से विकसित हो सकते हैं। इटियोप्लास्ट क्लोरोप्लास्ट होते हैं जो प्रकाश के संपर्क में नहीं आते हैं। क्रोमोप्लास्ट: वर्णक संश्लेषण और भंडारण। ल्यूकोप्लास्ट: रंगहीन, राल जैसे टेरपेन बनाते हैं।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "23",
                    section: "18",
                    question_en: "<p>23. PETA India started in the year ____.</p>",
                    question_hi: "<p>23. भारत में PETA कब शुरू हुआ ?</p>",
                    options_en: ["<p>2000</p>", "<p>2003</p>", 
                                "<p>2002</p>", "<p>2001</p>"],
                    options_hi: ["<p>2000</p>", "<p>2003</p>",
                                "<p>2002</p>", "<p>2001</p>"],
                    solution_en: "<p>23.(a) PETA was founded in 2000, PETA India operates under the simple principle that animals are not ours to eat, wear, experiment on or use for entertainment. People for the Ethical Treatment of Animals (PETA) is the largest animal rights organization in the world, and PETA entities have more than 9 million members and supporters globally.</p>",
                    solution_hi: "<p>23.(a) पेटा की स्थापना 2000 में हुई थी, PETA इंडिया इस सरल सिद्धांत के तहत काम करती है कि जानवर हमारे खाने, पहनने, प्रयोग करने या मनोरंजन के लिए उपयोग करने के लिए नहीं हैं। पीपल फॉर द एथिकल ट्रीटमेंट ऑफ एनिमल्स (PETA) दुनिया का सबसे बड़ा पशु अधिकार संगठन है, और पेटा संस्थाओं के वैश्विक स्तर पर 9 मिलियन से अधिक सदस्य और समर्थक हैं।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "24",
                    section: "18",
                    question_en: "<p>24. Which of the following is an animal hormone ?</p>",
                    question_hi: "<p>24. निम्नलिखित में से कौन-सा पशु हॉर्मोन है ?</p>",
                    options_en: ["<p>Auxin</p>", "<p>Gibberellins</p>", 
                                "<p>Insulin</p>", "<p>Cytokinins</p>"],
                    options_hi: ["<p>ऑक्सिन</p>", "<p>गिबरेलिन्स</p>",
                                "<p>इंसुलिन</p>", "<p>साइटोकाइनिन</p>"],
                    solution_en: "<p>24.(c) Insulin is an animal hormone. Insulin is a peptide hormone produced by beta cells of the pancreatic islets, it is considered to be the main anabolic hormone of the body.</p>",
                    solution_hi: "<p>24.(c) इंसुलिन एक पशु हार्मोन है। इंसुलिन एक पेप्टाइड हार्मोन है जो अग्नाशयी आइलेट्स की बीटा कोशिकाओं द्वारा निर्मित होता है, इसे शरीर का मुख्य एनाबॉलिक हार्मोन माना जाता है।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "25",
                    section: "18",
                    question_en: "<p>25. Which war laid the foundation of British colonialism in India ?</p>",
                    question_hi: "<p>25. किस युद्ध ने भारत में ब्रिटिश उपनिवेशवाद की नींव रखी ?</p>",
                    options_en: ["<p>Battle of Buxar</p>", "<p>Second Anglo-Sikh war</p>", 
                                "<p>Battle of Plassey</p>", "<p>Siege of Srirangapatnam</p>"],
                    options_hi: ["<p>बक्सर की लड़ाई</p>", "<p>दूसरा आंग्ल-सिक्ख युद्ध</p>",
                                "<p>प्लासी का युद्ध</p>", "<p>श्रीरंगपट्टनम की घेराबंदी</p>"],
                    solution_en: "<p>25.(c) The Battle of Plassey was a decisive victory of the British East India Company over the Nawab of Bengal and his French allies on 23 June 1757, under the leadership of Robert Clive, which was possible due to the defection of Mir Jafar, who was Nawab Siraj-ud-Daulah\'s commander in chief. The Battle of Buxar was fought on 22/23 October 1764, between the forces under the command of the British East India Company, led by Hector Munro, and the combined armies of Mir Qasim, Nawab of Bengal.</p>",
                    solution_hi: "<p>25.(c) प्लासी की लड़ाई 23 जून 1757 को ब्रिटिश ईस्ट इंडिया कंपनी की निर्णायक जीत थी, जिसका नेतृत्व रॉबर्ट क्लाइव ने बंगाल के नवाब और उसके फ्रांसीसी सहयोगियों पर किया, जो मीर जाफर के दलबदल के कारण संभव हुआ, जो नवाब सिराज उद दौला के कमांडर-इन-चीफ थे। बक्सर की लड़ाई 22/23 अक्टूबर 1764 को हेक्टर मुनरो के नेतृत्व वाली ब्रिटिश ईस्ट इंडिया कंपनी की कमान और बंगाल के नवाब मीर कासिम की संयुक्त सेना के बीच लड़ी गई थी।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "26",
                    section: "18",
                    question_en: "<p>26. Silver ornament becomes black after some time when exposed to air because:</p>",
                    question_hi: "<p>26. चांदी का आभूषण हवा के संपर्क में आने पर कुछ समय बाद काला हो जाता है क्योंकि</p>",
                    options_en: ["<p>Silver reacts with Hydrogen in the air to form silver hydride.</p>", "<p>Silver reacts with Sulphur in the air to form silver sulphide.</p>", 
                                "<p>Silver reacts with Carbon in the air to form silver carbide.</p>", "<p>Silver reacts with nitrogen in the air to form silver nitrite.</p>"],
                    options_hi: ["<p>सिल्वर हाइड्राइड बनाने के लिए सिल्वर हवा में हाइड्रोजन के साथ अभिक्रिया करता है।</p>", "<p>सिल्वर सल्फाइड बनाने के लिए सिल्वर वायु में सल्फर के साथ अभिक्रिया करता है</p>",
                                "<p>सिल्वर कार्बाइड बनाने के लिए सिल्वर हवा में कार्बन के साथ अभिक्रिया करता है।</p>", "<p>सिल्वर नाइट्राइट बनाने के लिए सिल्वर हवा में नाइट्रोजन के साथ अभिक्रिया करता है।</p>"],
                    solution_en: "<p>26.(b) Silver ornaments become black after some time when exposed to air because Silver reacts with Sulphur in the air to form silver sulphide. Silver sulfide is an inorganic compound with the formula Ag<sub>2</sub>S. A dense black solid, it is the only sulfide of silver. It is useful as a photosensitizer in photography.</p>",
                    solution_hi: "<p>26.(b) चांदी के आभूषण हवा के संपर्क में आने पर कुछ समय बाद काले हो जाते हैं क्योंकि चांदी हवा में सल्फर के साथ प्रतिक्रिया करके सिल्वर सल्फाइड बनाती है।<br>सिल्वर सल्फाइड एक अकार्बनिक यौगिक है जिसका सूत्र Ag<sub>2</sub>S है। एक घना काला ठोस, यह चांदी का एकमात्र सल्फाइड है। यह फोटोग्राफी में फोटोसेंसिटाइजर के रूप में उपयोगी है।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "27",
                    section: "18",
                    question_en: "<p>27. The &lsquo;Animal Welfare Board of India&rsquo; established in the year 1962, under section 4 of the &lsquo;Prevention of Cruelty to Animal Act 1960&rsquo;, is a statutory advisory body, formed for advising the Government of India, on Animal welfare laws, falls under which ministry ?</p>",
                    question_hi: "<p>27. \'पशु कल्याण बोर्ड\' की स्थापना वर्ष 1962 में, \'पशु क्रूरता निवारण अधिनियम 1960\' की धारा 4 के तहत, एक वैधानिक सलाहकार निकाय है, जिसका गठन पशु कल्याण कानूनों पर भारत सरकार को सलाह देने के लिए किया गया है। यह किस मंत्रालय के अधीन है ?</p>",
                    options_en: ["<p>Ministry of food and agriculture</p>", "<p>Ministry of environment and forest and climate change</p>", 
                                "<p>Ministry of earth science</p>", "<p>Ministry of law and justice</p>"],
                    options_hi: ["<p>खाद्य और कृषि मंत्रालय</p>", "<p>पर्यावरण और वन और जलवायु परिवर्तन मंत्रालय</p>",
                                "<p>पृथ्वी विज्ञान मंत्रालय</p>", "<p>कानून और न्याय मंत्रालय</p>"],
                    solution_en: "<p>27.(b) The &lsquo;Animal Welfare Board of India&rsquo; established in the year 1962, under section 4 of the &lsquo;Prevention of Cruelty to Animal Act 1960&rsquo;, is a statutory advisory body, formed for advising the Government of India, on Animal welfare laws, falls under the Ministry of environment and forest and climate change.</p>",
                    solution_hi: "<p>27.(b) \'पशु कल्याण बोर्ड ऑफ इंडिया\' की स्थापना वर्ष 1962 में \'पशु क्रूरता निवारण अधिनियम 1960\' की धारा 4 के तहत की गई थी। पशु कल्याण कानूनों पर भारत सरकार को सलाह देने के लिए गठित एक वैधानिक सलाहकार निकाय है, जो पर्यावरण और वन और जलवायु परिवर्तन मंत्रालय के अंतर्गत आता है।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "28",
                    section: "18",
                    question_en: "<p>28. UN World Wildlife Day is celebrated on _____.</p>",
                    question_hi: "<p>28. संयुक्त राष्ट्र विश्व वन्यजीव दिवस कब मनाया जाता है ?</p>",
                    options_en: ["<p>5th June</p>", "<p>3rd March</p>", 
                                "<p>7th July</p>", "<p>11th November</p>"],
                    options_hi: ["<p>5 जून</p>", "<p>3 मार्च</p>",
                                "<p>7 जुलाई</p>", "<p>11 नवंबर</p>"],
                    solution_en: "<p>28.(b) UN World Wildlife Day is celebrated on 3rd March. World Wildlife Day is observed on 3 March in order to celebrate the flora and fauna of the world and also raise awareness about them. The Convention on International Trade in Endangered Species of Wild Fauna and Flora (<strong>CITES</strong>) was signed on 3 March 1973.</p>",
                    solution_hi: "<p>28.(b) संयुक्त राष्ट्र विश्व वन्यजीव दिवस 3 मार्च को मनाया जाता है। विश्व वन्यजीव दिवस 3 मार्च को दुनिया के वनस्पतियों और जीवों का जश्न मनाने और उनके बारे में जागरूकता बढ़ाने के लिए मनाया जाता है। वन्य जीवों और वनस्पतियों (<strong>सीआईटीईएस</strong>) की लुप्तप्राय प्रजातियों में अंतर्राष्ट्रीय व्यापार पर कन्वेंशन पर 3 मार्च 1973 को हस्ताक्षर किए गए थे।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "29",
                    section: "18",
                    question_en: "<p>29. Which ministry launched the website e-Sahaj that allows organizations/firms/companies/ individuals to apply for security clearance from anywhere over the internet ?</p>",
                    question_hi: "<p>29. किस मंत्रालय ने ई-सहज वेबसाइट लॉन्च की जो संगठनों/फर्मों/कंपनियों/व्यक्तियों को इंटरनेट पर कहीं से भी सुरक्षा मंजूरी के लिए आवेदन करने की अनुमति देती है ?</p>",
                    options_en: ["<p>Ministry of Home Affairs</p>", "<p>Ministry of External Affairs</p>", 
                                "<p>Ministry of Electronics and Information Technology</p>", "<p>Ministry of Corporate Affairs</p>"],
                    options_hi: ["<p>गृह मंत्रालय</p>", "<p>विदेश मंत्रालय</p>",
                                "<p>इलेक्ट्रॉनिक्स और सूचना प्रौद्योगिकी मंत्रालय</p>", "<p>कारपोरेट कार्य मंत्रालय</p>"],
                    solution_en: "<p>29.(a) The Ministry of Home Affairs launched the website e-Sahaj that allows organizations/firms/companies/ individuals to apply for security clearance from anywhere over the internet.</p>",
                    solution_hi: "<p>29.(a) गृह मंत्रालय ने ई-सहज वेबसाइट लॉन्च की जो संगठनों/फर्मों/कंपनियों/व्यक्तियों को इंटरनेट पर कहीं से भी सुरक्षा मंजूरी के लिए आवेदन करने की अनुमति देती है।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "30",
                    section: "18",
                    question_en: "<p>30. Who was the first Vice-President of India to be elected as President by second preference count of votes in the Presidential elections ?</p>",
                    question_hi: "<p>30. राष्ट्रपति चुनाव में वोटों की दूसरी वरीयता के आधार पर राष्ट्रपति के रूप में चुने जाने वाले भारत के पहले उपराष्ट्रपति कौन थे ?</p>",
                    options_en: ["<p>VV Giri</p>", "<p>Shankar Dayal Sharma</p>", 
                                "<p>K R Narayanan</p>", "<p>Ramaswami Venkataraman</p>"],
                    options_hi: ["<p>वी.वी. गिरि</p>", "<p>शंकर दयाल शर्मा</p>",
                                "<p>के आर नारायणन</p>", "<p>रामास्वामी वेंकटरमण</p>"],
                    solution_en: "<p>30.(a) VV Giri was the first Vice-President of India to be elected as President by second preference count of votes in the Presidential elections.</p>",
                    solution_hi: "<p>30.(a) वी वी गिरि भारत के पहले उपराष्ट्रपति थे जिन्हें राष्ट्रपति चुनाव में वोटों की दूसरी वरीयता के आधार पर राष्ट्रपति के रूप में चुना गया था।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "31",
                    section: "18",
                    question_en: "<p>31. Under which ministry was the National Mission for Manuscript established in February 2003 ?</p>",
                    question_hi: "<p>31. फरवरी 2003 में राष्ट्रीय पाण्डुलिपि मिशन की स्थापना किस मंत्रालय के अधीन की गई थी ?</p>",
                    options_en: ["<p>Ministry of Culture</p>", "<p>Ministry of Finance</p>", 
                                "<p>Ministry of Human Resource Development</p>", "<p>Ministry of Tourism</p>"],
                    options_hi: ["<p>संस्कृति मंत्रालय</p>", "<p>वित्त मंत्रालय</p>",
                                "<p>मानव संसाधन विकास मंत्रालय</p>", "<p>पर्यटन मंत्रालय</p>"],
                    solution_en: "<p>31.(a) The National Mission for Manuscript established in February 2003 comes under the Ministry of Culture, Government of India to survey, locate and conserve Indian manuscripts, with an aim to create a national resource base for manuscripts, for enhancing their access, awareness and use for educational purposes.</p>",
                    solution_hi: "<p>31.(a) फरवरी 2003 में स्थापित राष्ट्रीय पाण्डुलिपि मिशन, संस्कृति मंत्रालय, भारत सरकार के अधीन भारतीय पाण्डुलिपियों का सर्वेक्षण, पता लगाने और संरक्षण करने के लिए आता है, जिसका उद्देश्य पाण्डुलिपियों के लिए राष्ट्रीय संसाधन आधार बनाना, उनकी पहुँच, जागरूकता और शैक्षिक उद्देश्य के लिए उपयोग को बढ़ाना है।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "32",
                    section: "18",
                    question_en: "<p>32. Name the scheme initiated to protect elderly persons aged 60 years and above against a future fall in their interest income due to uncertain market conditions.</p>",
                    question_hi: "<p>32. अनिश्चित बाजार स्थितियों के कारण उनकी ब्याज आय में भविष्य में गिरावट के खिलाफ 60 वर्ष और उससे अधिक आयु के बुजुर्ग व्यक्तियों की सुरक्षा के लिए शुरू की गई योजना का नाम बताइए।</p>",
                    options_en: ["<p>Varishtha Pension Bima Yojana 2003</p>", "<p>Varishtha Pension Bima Yojana 2014</p>", 
                                "<p>Pradhan Mantri Vaya Vandana Yojana</p>", "<p>Atal Pension Yojana (APY)</p>"],
                    options_hi: ["<p>वरिष्ठ पेंशन बीमा योजना 2003</p>", "<p>वरिष्ठ पेंशन बीमा योजना 2014</p>",
                                "<p>प्रधानमंत्री वय वंदना योजना</p>", "<p>अटल पेंशन योजना</p>"],
                    solution_en: "<p>32.(c) Pradhan Mantri Vaya Vandana Yojana is the scheme initiated to protect elderly persons aged 60 years and above against a future fall in their interest income due to uncertain market conditions. The plan is subsidised by the government and was launched in May 2017.</p>",
                    solution_hi: "<p>32.(c) प्रधान मंत्री वय वंदना योजना 60 वर्ष और उससे अधिक आयु के बुजुर्गों को अनिश्चित बाजार स्थितियों के कारण उनकी ब्याज आय में भविष्य में गिरावट के खिलाफ सुरक्षा के लिए शुरू की गई योजना है। योजना को सरकार द्वारा सब्सिडी दी जाती है और मई 2017 में लॉन्च किया गया था।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "33",
                    section: "18",
                    question_en: "<p>33. Considering India&rsquo;s global leadership in the environmental protection and climate change sectors, which organization had chosen India as the global host for World Environment Day (WED) on 5<sup>th</sup> June 2018 ?</p>",
                    question_hi: "<p>33. पर्यावरण संरक्षण और जलवायु परिवर्तन क्षेत्रों में भारत के वैश्विक नेतृत्व को ध्यान में रखते हुए, किस संगठन ने 5 जून, 2018 को विश्व पर्यावरण दिवस (WED) के लिए भारत को वैश्विक मेजबान के रूप में चुना था ?</p>",
                    options_en: ["<p>International Union for Conservation of Nature</p>", "<p>Global Green Growth Institute</p>", 
                                "<p>United Nations Environment Programme</p>", "<p>European Environment Agency</p>"],
                    options_hi: ["<p>प्रकृति संरक्षण के लिए अंतर्राष्ट्रीय संघ</p>", "<p>ग्लोबल ग्रीन ग्रोथ इंस्टिट्यूट</p>",
                                "<p>संयुक्त राष्ट्र पर्यावरण कार्यक्रम</p>", "<p>यूरोपीय पर्यावरण एजेंसी</p>"],
                    solution_en: "<p>33.(c) Considering India&rsquo;s global leadership in the environmental protection and climate change sectors, the United Nations Environment Programme has chosen India as the global host for World Environment Day (WED) on 5<sup>th</sup> June 2018.</p>",
                    solution_hi: "<p>33.(c) पर्यावरण संरक्षण और जलवायु परिवर्तन क्षेत्रों में भारत के वैश्विक नेतृत्व को ध्यान में रखते हुए, संयुक्त राष्ट्र पर्यावरण कार्यक्रम ने 5 जून 2018 को विश्व पर्यावरण दिवस (WED) के लिए भारत को वैश्विक मेजबान के रूप में चुना है।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "34",
                    section: "18",
                    question_en: "<p>34. Who received the Padma Bhushan for Arts in the year 2020 ?</p>",
                    question_hi: "<p>34. वर्ष 2020 में कला के लिए पद्म भूषण किसे मिला ?</p>",
                    options_en: ["<p>Ajay Chakravorty</p>", "<p>Venu Srinivasan</p>", 
                                "<p>Chhannulal Mishra/</p>", "<p>Guru Shashadhar Acharya</p>"],
                    options_hi: ["<p>अजय चक्रवर्ती</p>", "<p>वेणु श्रीनिवासन</p>",
                                "<p>छन्नूलाल मिश्रा</p>", "<p>गुरु शशाधर आचार्य</p>"],
                    solution_en: "<p>34.(a) Ajay Chakravorty received the Padma Bhushan for Arts in the year 2020.</p>",
                    solution_hi: "<p>34.(a) अजय चक्रवर्ती को वर्ष 2020 में कला के लिए पद्म भूषण मिला।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "35",
                    section: "18",
                    question_en: "<p>35. Which constitutional Amendment led to the inclusion of Sikkim in the Indian Union ?</p>",
                    question_hi: "<p>35. किस संवैधानिक संशोधन के कारण सिक्किम को भारतीय संघ में शामिल किया गया ?</p>",
                    options_en: ["<p>33rd Amendment</p>", "<p>37th Amendment</p>", 
                                "<p>36th Amendment</p>", "<p>34th Amendment</p>"],
                    options_hi: ["<p>33वां संशोधन</p>", "<p>37वां संशोधन</p>",
                                "<p>36वां संशोधन</p>", "<p>34वां संशोधन</p>"],
                    solution_en: "<p>35.(c) 36th constitutional Amendment led to the inclusion of Sikkim in the Indian Union. Sikkim became the 22nd State of India Vide Constitution(36th Amendment) Act 1975</p>",
                    solution_hi: "<p>35.(c) 36वें संविधान संशोधन के कारण सिक्किम को भारतीय संघ में शामिल किया गया। संविधान (36वां संशोधन) अधिनियम, 1975 के तहत सिक्किम भारत का 22वां राज्य बना।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "36",
                    section: "18",
                    question_en: "<p>36. Which of the following dates is commemorated as National Technology Day in India ?</p>",
                    question_hi: "<p>36. निम्नलिखित में से किस तिथि को भारत में राष्ट्रीय प्रौद्योगिकी दिवस के रूप में मनाया जाता है ?</p>",
                    options_en: ["<p>May 2</p>", "<p>May 1</p>", 
                                "<p>May 7</p>", "<p>May 11</p>"],
                    options_hi: ["<p>2 मई</p>", "<p>1 मई</p>",
                                "<p>7 मई</p>", "<p>11 मई</p>"],
                    solution_en: "<p>36.(d) National Technology Day in India- May 11. <br>National Technology Day is celebrated on May 11 every year to commemorate the achievements of scientists, researchers, engineers and all others involved in the field of science and technology. The theme of the National Technology Day 2021 theme is &ldquo;Science and Technology for a Sustainable Future&rdquo;</p>",
                    solution_hi: "<p>36.(d) भारत में राष्ट्रीय प्रौद्योगिकी दिवस- 11 मई। <br>राष्ट्रीय प्रौद्योगिकी दिवस हर साल 11 मई को वैज्ञानिकों, शोधकर्ताओं, इंजीनियरों और विज्ञान और प्रौद्योगिकी के क्षेत्र में शामिल अन्य सभी की उपलब्धियों को मनाने के लिए मनाया जाता है। राष्ट्रीय प्रौद्योगिकी दिवस 2021 का विषय \"सतत भविष्य के लिए विज्ञान और प्रौद्योगिकी\" है।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "37",
                    section: "18",
                    question_en: "<p>37. Name the scheme started by the Department of Science and Technology that provides a platform for women scientists and technologists to pursue research in basic or applied sciences in the frontier areas of Science and Engineering ?</p>",
                    question_hi: "<p>37. विज्ञान और प्रौद्योगिकी विभाग द्वारा शुरू की गई उस योजना का नाम बताइए जो महिला वैज्ञानिकों और प्रौद्योगिकीविदों को विज्ञान और इंजीनियरिंग के अग्रणी क्षेत्रों में बुनियादी या अनुप्रयुक्त विज्ञान में अनुसंधान करने के लिए एक मंच प्रदान करती है ?</p>",
                    options_en: ["<p>Women Scientists scheme-B (WOS-B)</p>", "<p>Mobility Scheme</p>", 
                                "<p>Women Scientists scheme-A (WOS-A)</p>", "<p>Women Scientists scheme-C (WOS-C)</p>"],
                    options_hi: ["<p>महिला वैज्ञानिक योजना-B (WOS-B)</p>", "<p>गतिशीलता योजना</p>",
                                "<p>महिला वैज्ञानिक योजना-A (WOS-A)</p>", "<p>महिला वैज्ञानिक योजना-C (WOS-C)</p>"],
                    solution_en: "<p>37.(c) Women Scientists scheme-A (WOS-A) is the scheme started by the Department of Science and Technology that provides a platform for women scientists and technologists to pursue research in basic or applied sciences in the frontier areas of Science and Engineering. WOS-A programme supports pure R&amp;D projects in Basic &amp; Applied Sciences. On the other hand, the focus of the WOS-B programme is to provide S&amp;T solutions to challenges existing in society at the grassroots level.</p>",
                    solution_hi: "<p>37.(c) महिला वैज्ञानिक योजना-ए (WOS-A) विज्ञान और प्रौद्योगिकी विभाग द्वारा शुरू की गई योजना है जो महिला वैज्ञानिकों और प्रौद्योगिकीविदों को विज्ञान और इंजीनियरिंग के अग्रणी क्षेत्रों में बुनियादी या अनुप्रयुक्त विज्ञान में अनुसंधान करने के लिए एक मंच प्रदान करती है। WOS-A प्रोग्राम बेसिक और एप्लाइड साइंसेज में शुद्ध R&amp;D प्रोजेक्ट्स को सपोर्ट करता है। दूसरी ओर, WOS-B कार्यक्रम का फोकस जमीनी स्तर पर समाज में मौजूद चुनौतियों के लिए विज्ञान एवं प्रौद्योगिकी समाधान प्रदान करना है।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "38",
                    section: "18",
                    question_en: "<p>38. As of October 2020, name the head of Instagram who oversees all the functions of the business including engineering, production and operations.</p>",
                    question_hi: "<p>38. अक्टूबर 2020 तक, इंस्टाग्राम के मुखिया का नाम बताइये, जो इंजीनियरिंग, उत्पादन और संचालन सहित व्यवसाय के सभी कार्यों की देखरेख करता है |</p>",
                    options_en: ["<p>Steve Wozniak</p>", "<p>Ronald Wayne</p>", 
                                "<p>Adam Mosseri</p>", "<p>Mark Zuckerberg</p>"],
                    options_hi: ["<p>स्टीव वॉज़निक</p>", "<p>रोनाल्ड वेन</p>",
                                "<p>एडम मोसेरी</p>", "<p>मार्क जकरबर्ग</p>"],
                    solution_en: "<p>38.(c) As of October 2020, Adam Mosseri is the head of Instagram who oversees all the functions of the business including engineering, production and operations. He formerly served as an executive at Facebook.</p>",
                    solution_hi: "<p>38.(c) अक्टूबर 2020 तक, एडम मोसेरी इंस्टाग्राम के प्रमुख हैं, जो इंजीनियरिंग, उत्पादन और संचालन सहित व्यवसाय के सभी कार्यों की देखरेख करते हैं। उन्होंने पहले फेसबुक में एक कार्यकारी के रूप में कार्य किया।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "39",
                    section: "18",
                    question_en: "<p>39. In which city, on the ghats of Shipra river, is the Kumbh Mela organized ?</p>",
                    question_hi: "<p>39. शिप्रा नदी के घाटों पर किस शहर में कुंभ मेला आयोजित किया जाता है ?</p>",
                    options_en: ["<p>Ujjain</p>", "<p>Nashik</p>", 
                                "<p>Prayagraj</p>", "<p>Haridwar</p>"],
                    options_hi: ["<p>उज्जैन</p>", "<p>नासिक</p>",
                                "<p>प्रयागराज</p>", "<p>हरिद्वार</p>"],
                    solution_en: "<p>39.(a) Kumbh Mela is organized on the Ghats of Shipra River in Ujjain. It is celebrated in a cycle of approximately 12 years, to celebrate every revolution Brihaspati completes, at four river-bank pilgrimage sites: the Allahabad(Triveni Sangam-the Ganges, Yamuna and Saraswati), Haridwar(Ganga), Nashik(Godavari), and Ujjain(Shipra).</p>",
                    solution_hi: "<p>39.(a) उज्जैन में शिप्रा नदी के घाटों पर कुंभ मेले का आयोजन किया जाता है।<br>यह लगभग 12 वर्षों के चक्र में मनाया जाता है, हर क्रांति का जश्न मनाने के लिए बृहस्पति चार नदी-किनारे तीर्थ स्थलों पर पूरा करता है: इलाहाबाद (त्रिवेणी संगम-गंगा, यमुना और सरस्वती), हरिद्वार (गंगा), नासिक (गोदावरी), और उज्जैन (शिप्रा)।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "40",
                    section: "misc",
                    question_en: "<p>40. Lala Lajpat Rai died protesting which British government\'s decision ?</p>",
                    question_hi: "<p>40. लाला लाजपत राय की मृत्यु ब्रिटिश सरकार के किस निर्णय के विरोध में हुई थी ?</p>",
                    options_en: ["<p>Simon Commission</p>", "<p>Rowlatt Act</p>", 
                                "<p>Morley-Minto Reforms</p>", "<p>Government of India Act, 1919</p>"],
                    options_hi: ["<p>साइमन कमीशन</p>", "<p>रॉलेट एक्ट</p>",
                                "<p>मॉर्ले-मिंटो सुधार</p>", "<p>भारत सरकार अधिनियम, 1919</p>"],
                    solution_en: "<p>40.(a) Lala Lajpat Rai died protesting the British government\'s decision Simon Commission(1928.).Simon Commission, a group appointed in November 1927 by the British Conservative government under Stanley Baldwin to report on the working of the Indian constitution established by the Government of India Act of 1919.</p>",
                    solution_hi: "<p>40.(a) लाला लाजपत राय की मृत्यु ब्रिटिश सरकार के निर्णय साइमन कमीशन (1928) के विरोध में हुई। साइमन कमीशन नवंबर 1927 में ब्रिटिश कंजर्वेटिव सरकार द्वारा स्टैनली बाल्डविन के तहत नियुक्त एक समूह है जो भारत सरकार अधिनियम 1919 द्वारा स्थापित भारतीय संविधान के कामकाज पर रिपोर्ट करने के लिए है।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>