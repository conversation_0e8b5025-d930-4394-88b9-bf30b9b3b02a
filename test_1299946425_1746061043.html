<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Smart Test Interface</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        .question-nav{position:fixed;top:70px;right:0;height:calc(100vh - 70px);width:320px;transition:all .3s;z-index:100;display:flex;flex-direction:column}
        .question-content{margin-right:0;transition:all .3s}
        .question-box{width:40px;height:40px;margin:5px;cursor:pointer}
        .attempted{background-color:#3498db!important;color:white}
        .correct{background-color:#2ecc71!important;color:white}
        .incorrect{background-color:#e74c3c!important;color:white}
        .current{border:3px solid #f39c12!important}
        #question-boxes{overflow-y:auto;max-height:calc(100vh - 150px);padding-bottom:15px}
        @media (max-width:768px){.question-nav{width:100%;right:-100%}.question-content{margin-right:0}}
    </style>
</head>
<body>
    <div class="container-fluid p-0">
        <!-- Header with controls -->
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary sticky-top">
            <div class="container-fluid">
                <a class="navbar-brand" href="#">
                    <i class="fas fa-graduation-cap me-2"></i>Smart Test
                </a>
                <!-- Section selector -->
                <div class="navbar-nav me-auto section-tabs">
                    <select class="form-select" id="section-selector" onchange="switchSection(this.value)">
                        <option value="all">All Sections</option>
                    </select>
                </div>
                <div class="d-flex align-items-center">
                    <!-- Timer -->
                    <div class="bg-light text-primary rounded px-3 py-2 me-2 fw-bold" id="timer">
                        <i class="far fa-clock me-2"></i><span id="timer-display">100:00</span>
                    </div>
                    <!-- Submit button -->
                    <button class="btn btn-success me-2" id="submit-btn" onclick="submitTest()">
                        <i class="fas fa-check me-1"></i> Submit
                    </button>
                    <!-- Language toggle -->
                    <button class="btn btn-light me-2" id="lang-btn" onclick="toggleLanguage()">
                        <i class="fas fa-language"></i>
                    </button>
                    <!-- Menu toggle for mobile -->
                    <button class="btn btn-light d-lg-none" id="menu-toggle" onclick="toggleMenu()">
                        <i class="fas fa-th"></i>
                    </button>
                </div>
            </div>
        </nav>
        <div class="row m-0">
            <!-- Main content area -->
            <div class="col p-0 question-content" id="question-content">
                <div class="container py-4">
                    <!-- Question navigation -->
                    <div class="d-flex justify-content-between mb-4">
                        <button class="btn btn-outline-primary" onclick="prevQuestion()">
                            <i class="fas fa-chevron-left me-1"></i> Previous
                        </button>
                        <div class="fw-bold" id="question-counter">Question 1 of 100</div>
                        <button class="btn btn-outline-primary" onclick="nextQuestion()">
                            Next <i class="fas fa-chevron-right ms-1"></i>
                        </button>
                    </div>
                    <!-- Questions will be displayed here -->
                    <div id="questions-container"></div>
                </div>
            </div>
            <!-- Question navigation sidebar -->
            <div class="bg-light question-nav" id="question-nav">
                <div class="p-3">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h5 class="m-0">Questions</h5>
                        <div>
                            <span class="badge bg-secondary me-1" id="attempted-count">0</span>
                            <span class="badge bg-secondary" id="total-count">100</span>
                            <button class="btn btn-sm btn-outline-secondary ms-2 d-lg-none" onclick="toggleMenu()">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>
                    <div class="d-flex flex-wrap" id="question-boxes"></div>
                </div>
            </div>
        </div>
        <!-- Results modal -->
        <div class="modal fade" id="results-modal" tabindex="-1" aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content">
                    <div class="modal-header bg-primary text-white">
                        <h5 class="modal-title">Test Results</h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row text-center">
                            <div class="col-6 mb-3">
                                <div class="p-3 bg-primary bg-opacity-10 rounded">
                                    <h3 id="score-value">0</h3>
                                    <div class="small text-muted">Score</div>
                                </div>
                            </div>
                            <div class="col-6 mb-3">
                                <div class="p-3 bg-success bg-opacity-10 rounded">
                                    <h3 id="correct-value">0</h3>
                                    <div class="small text-muted">Correct</div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="p-3 bg-danger bg-opacity-10 rounded">
                                    <h3 id="incorrect-value">0</h3>
                                    <div class="small text-muted">Incorrect</div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="p-3 bg-secondary bg-opacity-10 rounded">
                                    <h3 id="unattempted-value">0</h3>
                                    <div class="small text-muted">Unattempted</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-primary" onclick="reviewTest()">Review Test</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 100 * 60;
        let resultsModal;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            resultsModal = new bootstrap.Modal(document.getElementById('results-modal'));
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 768) {
                document.getElementById('question-content').style.marginRight = '320px';
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["6"] = {
                name: "Reasoning",
                start: 0,
                end: 23
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="6">Reasoning</option>`;
            sections["18"] = {
                name: "General Awareness",
                start: 24,
                end: 48
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="18">General Awareness</option>`;
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 49,
                end: 73
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["7"] = {
                name: "English",
                start: 74,
                end: 98
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="7">English</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 99,
                end: 99
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "6",
                    question_en: "<p>1. Identify the option figure that can replace the question mark (?) in the following series to logically complete the series?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743652246643.png\" alt=\"rId5\" width=\"345\" height=\"80\"></p>",
                    question_hi: "<p>1. उस विकल्प आकृति का चयन कीजिए जो शृंखला को तर्कसंगत रूप से पूर्ण करने के लिए निम्नलिखित शृंखला में प्रश्न-चिह्न (?) को प्रतिस्थापित कर सकती है?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743652246643.png\" alt=\"rId5\" width=\"345\" height=\"80\"></p>",
                    options_en: [
                        "<p><img src=\"data:image/png;base64,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\" width=\"72\" height=\"73\"></p>",
                        "<p><img src=\"data:image/png;base64,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\" width=\"73\" height=\"74\"></p>",
                        "<p><img src=\"data:image/png;base64,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\" width=\"73\" height=\"73\"></p>",
                        "<p><img src=\"data:image/png;base64,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\" width=\"73\" height=\"72\"></p>"
                    ],
                    options_hi: [
                        "<p><img src=\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAL4AAADBCAIAAAD+Yp3eAAAgAElEQVR4Ae2dB3wURf//ff72RyC5vZqEQEgjIQRCCb0K0ougKNIFLBQVC02KiKCoqKAUAUFQmqDSEQRpjyhKi0FaaLne9+52926v7O79/nMTN3s1dyEXIG5e88pr++x85r1TvvOdufsYxkvTDEXRlIcPvAJRKEDRDM14vd77bDay5Krul8OXdu06zwdegQoV2L37/M0bBofDfZ9CYd654/ybb2wdPmwVH3gFKlRg5Ig1B366YDYR9xUXqxYu2JudNfP+/4x76IHxDz/4wiMPvcgHXoFgBR68f/z9/xn34P3jly45pFCYy9FJkr3eqeMHI4evfvnFDXzgFQhWoHWr98SiV0Ogk583Z8prm7dsPnXo54t84BUIVmDc2HVZmTNCoNOh/furvjymUqFe/o9XIJQCH7y/t0WzeTw6obThj0VUgEcnojz8yfAK8OiE14Y/E1EBHp2I8vAnwyvAoxNeG/5MRAV4dCLKw58MrwCPTnht+DMRFeDRiSgPfzK8Ajw64bXhz0RUgEcnojz8yfAK3BY6OEaq1RZuwDAyfFzlZ1wuj1Zr5d5oMuG0z2Oo/CJ+6+5W4LbQuXnTePDAhb17zu3edXrnjtM7fjhz+bImmvSaTPj+fX/t3HF6187Tu3ed+emnv86eKfV46Gju5a+JSQGaZjxuyuX0OCMGt4uiKJphgL9flH+3hc6JE1enT9syeNC8xx9/vV3bV5s0nrlyxZFoIj57trRL5w9aF77RpfNrvXpOmzhh1Tff/OZ0uqO5l78mJgVsNoe81PT3BdVfRcpw4UKxqqREbzBgDocbeotGE8VtoXP1qm7t2qO9es3NzHxKJGpX67HOgwa9s+27U1aLPULcf/xxfc7sb2XS/gJBx3r1erZsOXneO1t/++26201FuIs/VTkFNBrrL4cvzpm1/c031k2bujpkmDH9q9mzNry/cPtHH+75fMnP69YeP3myRKu1Rm5C3BY6Xq9Xo7FMm7qpsHAUIswWilJksi6Pd51XVHQrZBFCUTRBOBe8931Ow5FicYYAyczK6jF82GdHj16snC78XRUqcOOG4as1R7MzXxEig2TSnjJp78Ag650k6yuTDZSIn06oM1SEjM7NmTxj2rfHj13yeCJ9zLeLjsvpuX5N99Yb6wSJ/YSiNIkkv1nBsMWLfywulgenCkWJQ4cujB69sG7dLmJxSp06HTp3mnX6z+tWa6RSKvg5/JHoFbh5w7Bu7S/5jceIRK2FwmyBoKlAUBAcEKQpguQhwoYiUUOZLL97txcXf7zdSboiRHS76DCMl6Lo77f/MXDAexkZnaTSvAYN2vXpM3vrll9Jh5vb7KIo+vIl9auvrG9VOEwqayKTNe/2+GsfLtqLR9cpi5AG/lQEBW7eMHy97nDTJiPE4gIBklGndtfcnGc6dx7PDR07jm3dekSjRk9mZDxeN7VAJE7Oymrdv9+kpUt+uhK+33O76MCXvn5dt/7rw+3avpic3EIkTpdK+k+buu7SRbXT6WFTZdDbftj+e072BJGwMCkpNzvr2UUfbC25quU7VqxE8djgooMgeYKE0U8NWvrhot3csHDBjpkztzw/ZmW/vu8UthyWlJwhFqdIpc3S6k3etu0P7vfPfcOqQYck3cXFpUOfXZyd3UcoTBOKspsVDH/tlQ1arYWNbPOmX/v1m5Oc3Fooys3M7PzkwPm7d/9Jkn4lE3sxv1FVCnDRkUjy09Ne+vST/Wo1GhAUCtONG/o9e87MmLEmr9GA5ORckaixCHl+w/oTLs73z32rqkHH6/VaUGL7tlNDnp4rSGwnFNaTyTp2e3zWqVNXMcxBUTSOO+bN+y6t/nMicYZA0LJV4Ysb1h+5eUPPfRV+Ox4KcNGRSvOzsyZ8ve54uIi0WuvGjcezM8eLxS1FojyJeMjSJbvVKkvIgqfK0GEYYHpa8ulPWZkvyGSNhMJmTfJHfPHFnosXlTab/c8/r40a9XGdOt1Eoqz0Bk+NGrnaZMLDJYA/XoUKxISO1+s9euRSasqUxMR2InFeSkrf+e9uLC5WhuylVxk6Xq+XYZhTv1+bM+fbxo0HS6RNpLKm2ZmTli87fPbMrYEDPs7M7CMS5yUn9Xnj9VW//3ad2wyqQqX4RwUoECs6x45dTk97QyhsI5bk1E3p9978TReKVXFHx+v1Gg3YwQPnevWcmZbWGUHS69TuMHDA3NenrMnIGCAWN09Pb9ezx4wtm09YrY6QbxOQbH739hWICR2TCf9+++95uRNlshYSSW5K8jOffbpLLjfHt8JiE6lUmmfO3NKm9WhEkIsgaTJpr5SkEWJxlkTStGWLYR+8v/2volL2Yn4j3gpw0ZFI8jMavLxi+SEMI7nBanWYTLhKZf7ll7/nv7upcd4zycn5Uml+/dTxX687itnIkENbVVlhQRVcLo9Cbnp9yrrEhP5CUVrtWl0efWQIgqTLZN369F5w8aLS4YhkaIq3lP+253PREQobS0Rj33pzy7GjV8rCsSvHj109eODCxm9Pzpm97anBHxUUjJVJ84SitJSUVgVNp+7ccZqmQg9LVz06YIUeit6/7/zYsUvq1+9ep06bxx5r/9h/Ow0ZMn/7tlME7vy3Zd6dTS8HnWaIsCEi6N+p45Sxz3/ADSOGv/vkwLfathmVnd1bJmuJCOvXrtWiZfMXVq44dK1EF+79qx4dGJPRaNu161TjvJeFwhaJgqwH73/u008OhHsJ/nj8FPBDB0kXCNogSEeRsBMMQmEnobCTQNBWkNhCIGgsFjeUyhrKkprmNBwzaeK3diJS/RAvdCwocfDA+RbNX5NIWvHoxI+MCp/MQadAgNRPSGiSkNAsINSp0zwxsZVE3KVuyoCsjJHNCl75cuUBpcJMeWhveAeeuKFjsf98sKhli9el0tY8OhVmcPwu8EcnK6FOz5Ytxj81eDonzHj22dmjRy2YPGnJrLe/XvzxzvXrj5Vc1TDA8Ss8OF4vj078cu2ueDIXHUSYJ0Kef2Hc2s2bfuOE33/4/vRP+4t//bXk0iWNXo9FaTfh0bkrMjh+L8FFB3TO00Hn3GZzcAOGkQTudDhcLqeHCtOfCn5DHp1gTWrUES46FY5hxZRyHp2Y5Lr3LubRuffy7C55Yx6duyQj7r3XuCfROXigqFnBG2JxZ4Gg4MEHhn/6ycF7T/h7/43vQXRQ+0/7/8ppODUx4VlB4qCHHxzz2ac8OneAxHsPHQwj/ypSLFywa8b0jVPfWv/C+HUHD1y4A8r966O8ccOw9qsjuQ1fEiT2kkp65WS/tv7rsF6CMakVrx6Wk3QbDdjlS5oLxcpzZ0uPHb0sl5tiejP+4ipRQC43b9/2Z/++H3do/3bXLrP69/14544zVfLkeKEDf+HG46HcbhCcsdiaqiRh/EOgAmYT/vcF1aGf/96/r2jvnnPfbz999Yq2SsSJFzpV8nL8Q25fAafTY7XYdTqbVmvVqC0Kudlmi2o1kgqj5tGpUCL+gtAK8OiE1oU/WqECPDoVSsRfEFqB6kCHYcA8mzB/XuATEuwWAm4JuItzWYRTMJkhbw9xivNMrj5+t4e5hnt92ZODkxF8kf+RsojYxMKN0DGGEMT/YSH2/BISJgfKDvsyIsQjwh6KOzoMzdAUE+GXRGmKAV5F/itKBd8FL/PN9vL9Oinnp0nZU2wqI93OeR+aAu8WDC4dcE2UK9XRdEzygyzzRQRfg/LQcKNMEH83K3Ctv4zBqWaTX7bh+xnXgLsqyIhY4I87Oi6Xh8CdNqsjXMBspJ1wOUm3202xTkZOpwfH/O7CMZIkwbJfFMWQpIf7NBwjnb5TUDKG8ZION46R3GsI3OlyUQzDOOwuzMaeInHM6ST9nFRomiEIJ3sNfD32xQKzh92nKMbh8LpcXjr0/AH2QrDBeBmacTk9DruLwJ04BqMjMRuJY+CVHHaX21W+tg3DeF3OQBkxG+kky1eD8Hu+b4emGTvBTWzYLPAJBeKNaXWsuKNjt7tMRlyntWk11uCg09r0OhtqJjAb6bC7PG6Qu16vl8CdRgOu05bfYtBjcI1Lt5vGMCf3UfAU6w3JMAxmI416jL1Gp7WZjITd7qJpxmKx63VlL6PT2sC9NtLlKs8Dj4c2mwj2hXVa8HoVOEDRNON00ijKEITXXcGydmUWLzeFY07UbDcZcYMe0+tsUAqDHjMaMNRM2AnwtrDoYRjGTjjNJoJNkVZj1WltOBZpeonHQ6Nmgk0s997gbSgF6ajg5bmAxh0d3JfNCrlZXmoKDgq5WalAtRqbyYjbrA7S4YaZZLM6NGqrXF5+i1plsaBgBSeXi7JY7NxHqZSo2Uyw2DE0g5oJtcrCXqOQm7UaK46RFEWbjLhKicJTCrkZ3GsiuJJ53JReZ1Mqyl5YITfrdVjklVwYt5vBcUqnoy0WhiQjV1s0xbhdlMPugoBq1BaVElXIzVAKlRJVq1Cd1mq12N0uD6SHocHHoNfZ2BTJS00KudkSceE9N0gIplSUJZZ7b/C2Qm5WqyyRp0BwufHGzzeZjSYyOlACldKi1VgNesxmdcCqx4dOed7LS01+6KB+6CgVqEGPOewuiB1NM2aATrlkCrmJRcdoxFh05KUmpQLV62wEZ9aI203ptDaWdR86tgrQcThos9mjUlF6PY1hkdFxuTw4RqJmULCpVRaloowbKIUPIJCLUA1fJQtqN8xG6rSVQKf8GwjGhXvknkEHfmTw1dkPTqO2mIw4gTsZxhsTOgq5Wae1WS0OUFX72obRo6OQmzVqi81Gsq2ZmNGhaQbDKJ3OI5dTajVtNnspKhw9DOO1212oGVQ9bGEDFQj4D+mx+76HKkQnIBZ2V6m4F0odWDJzvzb4wSkVqE5rs1kdDMPEio5aZTEacCfpAX2WWEodUPD46iynE9zr9XpjQ4dhvG43jaKUWu0pLaUUCtpg8JIkoCfoD7ZyYNUDuQn4eGBGsmpo1FarxeHyvdjtlzrw4UoFGjKolKhWY7XbI83ZC0jQHWjrwBaGWlVWx7PFJiwzUdQeKzq+ege0ZgjcCbq4MaIDqyTMBlpCMaNDUYzdThuNHqXSI5d7FApKq2UwLGRjGSxB5Gu6qlVgLCmAm+DKS6VEjQbMTriqpNT5pypE1SpLcNCoLTqtzXGXo6NUgDrCaMB0WptKWS6ivNQEG62VQAfiaLXYXU7QtIy+woKfuEZtMZtwlwsUPLGVOh4PqK30eo9K5VEoPEolpdGArpbTGVxnURRNOtwGPca2wWHsahXINqMBdAlZqmDZrNPaMIysKnRUStAoNBpCBJMRN5uImBY9ugOlji8BNgJ3Wn39ZK6OSgVqMhGVQAcWPL5v1BkrOvBevc6GYyR0EYmhmezx0BYLpddTGg2lVoP/Oh1tMoFeuqe8ww+LerebsqB2rcbKLXJgO92C2u2Ey4ICwwFblfv6nlawRklVNJNhqw7HgBEkZADd21h+a+HOoGMy4m43BY033Fr/dtDx9cAtNquD8hlmouxhsbWGRm1BzQTps0zGgA5s6BgMlE5XFvR62mhkcByYB/3/nE6PQe/Xv4N1tNlE2AlQ1RI+4w3srquUqEZt1eswHHNWFTpajdVhB/P0gkN1/0aEvzKh94I757BWomnGYXebjAS3zroddGB9ZzLipMNtMuKxogMLc2h0jhYdmvb6LIG00UgZDDDQRiModTAM1Fn+fyTp1mqs/qUsaKJhNofbZ5N0uTywEQ0tfkYDbkHt8HcbqqSZrFFbfMZxp50ICC7Qw4hlFOLO2HU46ABDcxWiA3vpFtSh12ExoQPrLNBLtzpI0q3T+tUpel0Yu45v8IFGUdpsDgjh0NGoLVx0VEpgVQJrk/s6dwzNkA632USYjAAaAidB042iq6rUgTWgTgss19xg0GNWiyP6HxaBX8SdqbCMBsxJum3AQgrMnWzdX+lmMrfeMehBA9zfmhzWJFjWH/YZc1VKFDUTBA5GOdhXCm0S9PWzvU4ng+Nl6JhMNCcwVitjt4PmDudbJh1u6KfHdirVKtRkxMHCxPCLZ7xuF4VjYDCLwMsG1+AoaZWUOqxZJKB/rlZZzCaCtWz5l5Vh9+4AOkqFGRrajUZcoy7PJJ/JGGRerM1k1hwC6yxY2seEDgRFITcb9JgFtXMzOCw6LhdDELTFAogxGgMDioIuur+Bh3S42Q4UpEejBqMr3EFHmgLDoi6nBzQ+PGWDWFVS6sDeHEstdwO0E4wx/5TdHUAHdqRhY5Bb5MhLTUBKS8x2HS468OE6nx8uq06EgQh4L4uORm3R60CJVUGpA1pqDtpqBa0cvT44AJIgPZzRUIfDze0TyEtBWWizOjwBv+YEHZg4LY+qQocVJGDjXkJHqUBVSmDWZPMMZrnRgFViIAIWv+yjoN0oyrYOjJdtf8B+DTeDQ5c6vgXogRHZYAjmBhwxGEBpZLEwnH5WSHQwGxn5t4OAj0ZVdM5hqcN+KtyNe6bCgi/NWi9gkqAh3GpxQB+UmAYiVEoIYrl9Fh5hv60IpY5SAfo4LGf/kFTe/AqNjsdD22yUyRQBHcpopM1mbj8rWnTYUucf/8kqQQdak1XKEKZkrcaKmu33QFuHyzuLkUZtNRpwO1E2+h0rOgHDGkqFmS1IfGiGbSbDUfeAdjFbHEKsQ/SwoCWQ0ydnO+flGyw6/7SUQ6ED+nTcCgt6A0J3wTLnSd+oXJU0k+EooV4HfKS4waDHrdZ7oYelVAAvGbWqLMDyBo6Zu11ljoKxoqNRA7cN1jcFEhlNqQN7VdzBAZZseHuIUsfnpxi2gcw2mU2mAOevYHTUKmCH5DoEenyWUgJ3ErjTTriA/6TT4yvjHLfpdFHmI2AF3oABgcCdvp8ICtuZCnniDjSTlQoUtkYh+D4XPhxU+RwH05jQgZWOzwxY3rxluYlc6qhVwEoGXcPY1hLbRg5Z6gDHLrsdoBNkzgk84msps3VWcA/LN8CJsyNHDMOQZJldx2QkULMdNdsJ3Ol2UTZrCHRQs52i6KAAfAcYBgzGBfisaTVWJ+nxuOngUIEbZCh27gA6KiXwgkPNhAUlUDMImA34Hf9TroPXjAkdX8EA/ES5VuDo0bH7vJV12rLBI+6NodFxuYA5x2oFFp3IwWJhbDbgs+z7I0lg1+HWpLAGYX+ukKZoaFVSqywaNXAh1euAH5LLBXyxg0sdaDonHW7/APr2FEUHo6PThrFthiKjwmN3Bh2jASMdbifpgQEOoHDfNVZ0oGGN6zzKJSBCM1mjtjhJDxwS4fbJ2duDKyzG6WQwDKBjsVQcoG3Q91k4gZ3az20PlpfQV8Tr9TqdbgtqV6vKXGp8AJUZfkKiAx0QAkbCTUYcNdvhYFxAqVMT0DEZcY/PsQZMW/HNpAmY0RIrOsAYSgGXdW6Dl5P9YZvJGrXV5aJcTspmJbmtJc695vJmMsN4PR5gCQw1+BBYW8HqDEWBy6lvpoTL5TGb8AB7o1plsVqAU7bH5+huNOBs39PX3bOgqD1cWwe2GmF3kv3vc222EQSY3hCAjlZjhUhBBwHuf4+b8njAiAf3A468fWdKnQrN3jGjA2zQXhwjfQ3e8q41JCBiqWOFdluHHXjSBBc8fqUOmJ9iB9zo9eVD5eyYeagNWq+Ho6Fet9vjAfWRXufn+KxUoEYDbrU4CBzMeQioN1VK1GIBDRrMFqLC4rbJOKyb1CoUOpBw0YH+3VaLHbM5ggOOkXbCWaGFiQtTzUHHy3idpBs127njqVGiAxuVAc40/9xbXuoAJz+zmdJogE+gQhFVUCopn7s7Q5LQS9BsKp+SAdtSPlcv4F+hUQOHZRYCaF63+X47LLhzzr2Mu62Qh0UH+nho1JbgoNUA4wic6cblI8J2DULH6/W4KcwG6x2/uTsVljpeL5hRSuBObi89EB2aZkgSuK8rlZ7S0hiCXE6pVMCDh6IYxmu1+I2RwUF7X3VTNjuCjbfMD4QI7a/DxYW7HQEd7mUB275W113vYMo6XUQguhIVFsh+Csx3hO5U3MI8GnRAweMCXny+UYjy+V9shcX45g6CIkehiIGb0lIwU0KpBJZlEixsA2vVgLEOOKbNmqOgUVurAbOxoL99LKUOmNsQssIKwIW76xuT/hejwzBep9ODmu0BA/LRoAOHinAssNAqR4ckwWQr6L4eU6njo4fW60HB4/U6fJNpdNpIk2lYS5WdcMHWK3QBY82VETbgEB4HHdDyqzD4LCa2mH7lrkZVWF6v1+Oh7QRoinLNJ1GiA/PVaAj0AYU9LIYggPt6rEUOhEwuB+7uViuYceGbhA+n9MLxE3bcA5Y9cAjWaMBxjHT5zOsMw7A9gABXm+BdaJ0ncKfHTcGGf/A1wUfgtC/uLNgIdQI8FX90fJ5TSkU5+GCc1lyBYxHsKnO/FeiPwU4c5p5SKlDUDOYU+9bBAL+ZbTYBB1P2GughxE4c5p7Saqxcdxk74dKoLX9fUJ0/V3r+XGnRefm1Eh2cOEzjOKXVlpU6cjmYOhN9UCjA7D4UhY0qt4siHcCJ3aAHs9/ZaTRKBZjp4psdgdusJByWgWujELjT5HNv0qitkQOcRwuLK5MRzBWMfD08C2363FUfoJ4R/scdHYcdWNb1OjAjHwajAefOtgz5cgQBpmSzt+h1YFI6nJ3vdtO4r4vLnmVXMoCPArMxbaTRgLMX6HVgAQCHAywAYLU4jIbylzGbcG6P9NYt4/fbT48ds3pA/08GDfx05PAvv9tyCi5XAOdb0TpdJYPBAAw8vmUuaJrxuEFH3Wqxo2bgTgpXLPDNdAEGPZvV4XC44YRz+D2QDrfN6jCbiGgCaCGRYPa+zeZAzVHdgpoJm7XMRTpkjgQfjDs6bjdlJ1zc8Taf66SbXZgi+J1g0RJwl50Arvzwq3X5llBhnwke6DsFHwVaPCRYUoS9oGzZEd8yGqTDzT0Fl79g3+HGdcPX604UtpiekjQ8te7QvNw3Vyw/7LCDSXSMzy0QOB1XLuA4O5gFF0kBZQ/pdtjBGCd8Jd+Qp9NhB3Z2j5suH5lhQOfR6bs45DyYgIPAwOiz70V/i8MO1qmJaSQr7uhAmdhFh0KuO8TmHLsRsGxRwF3AM9i3qlK5c0K5zOAZEW7nnoJeDWykXq9XqUC3bj5Z0GSiILEjgrRMTRm//Iuf2aVKwNo5FFXJELxwk88dp9yzgl2iivatFhVg1/W/mL0r3Aa0zoc7G/J4gEGfK0vI7fijA6MNcl8K+TZ+BwNuCSUlSC0Mfnf6dthTwRdwT/nfqFSi32092bLFS2JxK5EoLzVl7PJlP/td4mt6AGf1SgS/B/nvsK/kf/hu3qsudO5mDTjvplSi2777tVXhixJJC7E4N7VuEDqci//lmzw6fgDw6PjJEXGHR8dPHh4dPzki7vDo+MnDo+MnR8QdHh0/eXh0/OSIuMOj4ycPj46fHBF3eHT85OHR8ZMj4g6Pjp88PDp+ckTc4dHxk4dHx0+OiDs8On7y8Oj4yRFxh0fHTx4eHT85Iu7w6PjJw6PjJ0fEHR4dP3l4dPzkiLjDo+MnD4+OnxwRd3h0/OTh0fGTI+IOj46fPDw6fnJE3OHR8ZPHH52cusnPf/H5z3BifIT/kZ1l/SKoQTs8On6ZyUVHJMyRikd/sni//xIkASuSgF32V9z8nlXTd3h0/HKYg04hguQKEof17rno7Zlbw4UZ07e++cbW/50oickh3C/Ke3aHR8cv67i+yQJBw4Q6vRMTBiOCp8OFhNpDH3pg7PJlv3DXA/R7Ys3d4dHxy1swI2LLyYKmkxCkNSKsK0AaIEg6IgwbEhOb//eRUSuX8+iY7ysuVi1csDc7a2aH9u+v+vKYSgUmLP57/m7eNH6z4X+tC2fUTRmaktKnwlA35am6yZPXrD4KfgQkYM5GTVeNL3X8clipMO/ZfX7CS+ueGfL5kKeXVhieGfLFsOdW7N1TBKb5/cvY4dHxQwesdmB3GY2YXm+LPtj/fdxUx48a+eUMv1ODFOBLnRqUmdWbFB6d6tW7BsXGo1ODMrN6k8KjU71616DYeHRqUGZWb1J4dKpX7xoUG49ODcrM6k0Kj0716l2DYuPRqUGZWb1J4dGpXr1rUGw8OjUoM6s3KTw61at3DYqNR6cGZWb1JoVHp3r1rkGx8ejUoMys3qTw6FSv3jUoNh6dGpSZ1ZsUHp3q1bsGxcajU4Mys3qTwqNTvXrXoNh4dGpQZlZvUnh0qlfvGhQbj04NyszqTQqPTvXqXYNi49GpQZlZvUnh0alevWtQbDw6NSgzqzcpPDrVq3cNio1HpwZlZvUmhUenevWuQbHx6NSgzKzepPDoVK/eNSg2Hp0alJnVm5TqQ8fp9KAooVCYS0p0V65oS2+ZrBa7x0NXb3r52KpMgbijQ9OMx0ORDpdcbvz9t5Lt237/cuWhZV8c3Lzp5J9/XNfprE6n2+Oh/oVLVldZHt6hB8UdHRS1XyhWbPvu16lvrevbZ07HDi8VFg5v0WJoh/bjn+j+9uRJX504UXTxokKtttD0v2z12DuU5VUVbdzRuXRJ8fW6A6NHzW/RYphM1k0s6oEgPRDkCZn0CbG4e6Pc5156cfmyL/b/+utlt5uqqlTxz6kGBeKIDsMwLpdn397fXxi/oF5qZwRpKJU2zs4amtPw5dycCY3zhqWmtkpIaPzQA0/17jlv9ap9mM3BFzzVkOVVFUUc0SEI57mzpdOnrczLGyiVZibUaZefP3HF8n27d/25a+efK5bve6L723Vq90hMbJSZ2XvE8Pm//nrFZMKrKmH8c+KtQBzR0WmtK1cc7tNnilhcKBbnt2374rRp2y5f0lhQO2omrl7Rfvbpgd69ZicltRSLm7dsOfSdd7YVFcnjneB/yfPdbgrDSI3GevmS+o9T148fv/TbbyVF5+XXrukNBowk3be/tnwc0bl2Tff8mJV5eYMQpJFE8sRbb3557myp0+lhM+/mTcOqL/c3aDBEKGyamtqmbeu395BVAtgAAAynSURBVOw5x57lNyqnAEXRNptDXmo8e+bm3j1nv/h837Sp6198YfnrU9YueG/72q9+OXzoQslVrcVCuFzleVGJuOKIzuXL6icHfpKe3lsozJFK+s2Z/c21Ej23LaxSod9sOJKVNUokap6SUtis6dSdO85UIg38LVwFTCZszZpfXn5pdc8e8zp2mNy82dCcnN5Zmd0bNuyZnz+obZsx3btNGz3y85UrDly6pOTeGOt2HNG5fl03ftzq/PzBCJIrEnV++aVPfjn8N2omPB6KphnS4T75a8k7c79Nb9BXJMqvX79d505z9+87H2sC+OuhAhRFWy32EyeuLFr044ABUxs3floq7SJEOiBIO6GwnUjYUSjsIBS2E4tbyWQdsrMH9O0zZfasr/fuOetwuCqnYRzR0WgsSz7b3737BIGgqUDQ+InuExYu2P7HqetKpVmvt5Vc1S75bF+fPtOTk5sKhbm5uX3GjV158terlUsGf5fD4Tp75uYrk9eKRc8Khc2EogyJJCM5uVVGRp/GjYc3bfJifv7YhjlPpdQtlEizhaJ0RNAkOan/wAELi4pKcZyshIBxRMdud10oVkycsFIs6i8UZWZnd+nQYcLA/h9Nn7b5ww93vTJ5Xe9e0xs16ieR1hOJOnfvPuvwoQt6va0SaeBv8Xq9Gg06Zcq65s1GIEhjobC+WNw6M3Pks8++N2fOhjWrD2777uTmTcc/WfzjoCfnN8odL0R6CEUNxOKsevW6Frac9f3205XQMI7oUBSNYeS+fecnTVqRnt5XIn5CJutXL7VvYeGorl1eKmg6NK3+IJGoV0Ji+8GD565be8ygx26z4VaJ9NeMW86fK/3ggx9atx6ZnNxMgNSvVauw2+NTFi744ccfTp06de36Nb1GY1Uq0QvFyu3bTn2yeO/4cUvS0nqKxDkCQcZj/3180qRlJ09ejVX8OKLj9XppmkFR4qf955vkv1mn9tOP/beLAGmYKMhLTChAkEa1Hmv/yMMD/vvoqEUf7Naorbw9sBIcM4zX7aa++Pxg/XrjJNIckbiuVJpfP/WlDxftx2wkTYUY23G7qNN/3ujb+6P0Br0QJA1BMlq2GD1zxlYMc8T0AnFEh6JoAneuWHG4Q/uZMlkvRNAcETQTIv0FCc8kJjwrEQ8WCtskJjZJTOzSKHfClNc2EbgzplfnL/Z6vR43pVSiC977rn69ZyTSTIm4ZfOCl79Zf/zGdQNF0SF/F5BhGBwnL11Sjx615NFHuyPCVKGw18ABS1EzEZOkcUTHbMZ+/OH3Z56ZJxb3EInaJiS0z8wYMuXVNXNmfzdn9raZM75t2+bl2rU7iERtBIld2rSesGH9kZs39TG9PX8xQTgP/XzhhfEfyaSdRaKMtLQeA/t/eOGCgmsECafS3Dk/JCcNE4nSatdq3b7d68eO/m00YuEuDj4eL3Q8burvv+UjR3ySm9sPQXIlkg5p9Uc/PXj5tRK93e6y2116HTZ92vaM9IlJSV19nfP2Xbu8vXfvGd77IjiTIhwxm/DFH+97/PEJiYk5iYlZLZo/98br3yiVpgi3sKdWLP+5bZtXJZJGiYmNmjQZ9O68rUXnS9mzFW7ECx2txrpzx6nOHV9LrdtWKMwRIUNnz9paclVHkm6aZmiacbspudz8zYbjuTmTJJI2UkmTtLTRq1cdNJuJ27eRV5jsGnOBRmMZN251Ts5TAiQ9IaHZ4EHTv9t6CkWjqnoO/Xxh4sTPU1LaCwQN69Vr17XL3L17Y7Dmxwudy5c1y5fta5L/nFgMLMXt28347rvfvf6NNobxnj9XOnjQp9lZvRGksVjU8525Gy4Uq/iCJ3qyFQpT3z6Lk2Q9hML6QmH7cWMX/e/E1SjtNH/9pfjow+3p6f2FolyxuGlq3QkbN56MPup4oXPunPz9hduysvoKEgsyMrqOH7f0yJG/g1+rpEQzY/qmwsKhiYn5CNJm4sTPDh++xHudBgsV7kjpLWOHdgsS6nQWiVOl0m6vvvJF0XmFwx6VgfjWLeO6tYdyc0dIJPmJgtxHHx791Zrj4SIKPh4vdIqLlYs//jGn4UCBoFmDBp1Hjlh86FBxcPQlJdqZMzb70GkiFLZ9ZfLSo0cu8+gECxXuyK2bxtaF82s91kksrieT9nrzjVVXLmtJ0h3ueu5xlQrduPFYo9znJdKmiYm5Dz0wavXqY9wLIm/HCx2Fwrxp4/EmjceIhC2TkpoXNH1j47eBhSFNMadP3+zfb3FGZk+hMC9J1nvunA1/FSn5CitynnHP3rplbN1qfq1anUTielIfOlevRIuOUgGGn3NzRkkkTRITcx9+aNSauwEdHHeeOH6xfdtpSUntBUjmfx8dOGf2FrncaLM5nE4P5aHdburWTePar46m1Z+ACFtKpI3q1xu+7It9Wo2VbyZz4Yi8XVpq7NzpfYGgq1BUTyzp9uory4qLlVGOaF6/pv9y5f7srGfE4jwEaVSn1ph1a09Ejo57Nl6lDk0zly6pnnv204bZfYXC9KSknHbtnn/1lfWHD128dctoQe1ajXX61G0ZDSZKJF2FohyZrFnDrMlbt/zm4T2UuflT0bZSaX76qSX16vVBhGkI0nbMmIVHj1zCsKiGM/84dWPunG/q1+8uFGZLJE3rp07YdDc0k71eL4riu3edHjz4nVq12ktlaan12jZvNnrc2I+mvbVu9qzNM6avb9tmfO3arRBhozp1WjUreH7JZ/suX1ZXpBV/3k8BoxF7b/6PHTqMT0zMFQjyu3ef8Nmn+/U6q99FYXY2bfxtQP+5SUnNECQjLa19927z9+0rCnNtiMPxKnXYqD5feqhx3itSaTOhsIlY1DYlubdUMliEPCMRPSURt5ZKs5OSmuY0HDPh5W9wfiCCVS3qDYJw/nywePSohbVrtxEIMnNyBowauez6dV3kAUGGAcOLC97bkdFglEicjghzmjYdNHPGpjNnbkUdszfu6BgN2M4dp3NzXpVJh9WtO1gieVwgaJeY0EYs6pgk65uePrxFi9dWrvhJqTBHTm30SfpXXUnTDI6R8+d9/+gjgwVIhlTasVPHOWfP3OQ68gYLQlG0nXBNfWujWDhIJE6VSDr06jHr999KorQlwgfGHR2GYbQay9q1Jz5ctHP2rA0vvbh4xIh3nxs6d/ToBW+9tWrxxzs2bDh+9YqG5yY4g6M9wni/WnM0N2eSTJYvFOWlp/d9fvSyvXuKIviul94yzntnV/v2E4RIgUicJES69evzoVJhjmbki32ruKPDxoSi9iuXtUePXN6zu+jHH8/u3fNX0XmFwYDx0LASVXrjl1/+fmH85+npTwiQLIEg/ZGHe40ft/zU7yU3b+rNZtzhcJGk22F32WwOjcZy+bJ6/ddHU5Ker127QChKQYRZjRuPnvLaZpvtrnG6CBCComiXy2O3u3DcieMkgTtBL53ilysI0Kkyu3bC+cepa80Lpteu1VGApCJIvfQGfbp2njfr7a27dp29ekV784bx8iXN/05cXbHi5yFPf94gbaxIlIMIkwVIw9qPDVm0aKdeb4v1G66+UqcykvD3RK2A2USs+vLoU4Pn1a/XQyTOF4kKUlO7tGkzYsiQWZMnLZvy2qrJk5aNGfNBjx4Ts7L6JSS0QIRZdeq0yM0ZMX/eD0XnKzP9jUcn6sy5uy+kKNqC2jdv+t+Qpxe1bDmiQYMOEkkughQIhZ3E4j5SyQCppK9Y3A1BChGksVTWJCu7W/NmE19+aYNOZwvpTFhhcnl0KpTonrkA9LZw8soV1dKluwb0fze17rDkpJ4iUbuEhGaJifmJiU0FgkKZrKtEPCgrc8LoUZ/9+OMpgwGrdJuBR+eeISPKFyVJ96VLyh0//rH4o51T31o19vn3Bz05/YknXunf781hw+ZNnrTkvflb1609+svhCyqVOcpnhryMRyekLPf8QdLh1mqsf/55c++eog3rTyxd8tOa1Ue+//7Po0cul94yuV2ekG7LMSWbRycmue6ZixmGAV1ap8fhAF1azObAcdJuB710jye0u3usaePRiVUx/voyBXh0eBQqqQCPTiWF42/j0eEZqKQCPDqVFI6/jUeHZ6CSCoRFJz9vzpTXtmzZ8sehQxf5wCsQrMC4seuyMmc8eP/4pUsOKRTm+4qLVQsX7M3Ompkse71zx0UjR6z+/8McfOAVCFagdav3xKJXQ6DzwP8b99AD4x956MVHH+YDr0AIBR58YPz9/xlXjo5CYd6549wbr28Z9twqPvAKVKjAiOGrD/x0wWzC77PZyKtXdYcPXdy18xwfeAUqVGD3rvM3bhgcDvd90FeeomiPhw+8AlEpAN0L/w8sNHDXnaidGwAAAABJRU5ErkJggg==\" width=\"72\" height=\"73\"></p>",
                        "<p><img src=\"data:image/png;base64,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\" width=\"73\" height=\"74\"></p>",
                        "<p><img src=\"data:image/png;base64,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\" width=\"73\" height=\"73\"></p>",
                        "<p><img src=\"data:image/png;base64,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\" width=\"73\" height=\"72\"></p>"
                    ],
                    solution_en: "<p>1.(c)<br><img src=\"data:image/png;base64,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\" width=\"73\" height=\"73\"></p>",
                    solution_hi: "<p>1.(c)<br><img src=\"data:image/png;base64,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\" width=\"73\" height=\"73\"></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "6",
                    question_en: "<p>2. In a certain code language, &lsquo;FEED&rsquo; is written as &lsquo;24&rsquo;, and &lsquo;ACCOUNT&rsquo; is written as &lsquo;42&rsquo;. How will &lsquo;PIZZA&rsquo; be written in that language?</p>",
                    question_hi: "<p>2. एक निश्चित कूट भाषा में &lsquo;FEED&rsquo; को &lsquo;24&rsquo; के रूप में लिखा जाता है और &lsquo;ACCOUNT&rsquo; को &lsquo;42&rsquo; के रूप में लिखा जाता है। उसी भाषा में &lsquo;PIZZA&rsquo; को किस प्रकार लिखा जाएगा?</p>",
                    options_en: [
                        "<p>40</p>",
                        "<p>38</p>",
                        "<p>30</p>",
                        "<p>36</p>"
                    ],
                    options_hi: [
                        "<p>40</p>",
                        "<p>38</p>",
                        "<p>30</p>",
                        "<p>36</p>"
                    ],
                    solution_en: "<p>2.(c) <strong>Logic :-</strong> (Number of letters)&times; 6<br>FEED :- (4)&times;6 = 24<br>ACCOUNT :- (7)&times;6 = 42<br>Similarly,<br>PIZZA :- (5)&times;6 = 30</p>",
                    solution_hi: "<p>2.(c) <strong>तर्क :- </strong>(अक्षरो की संख्या)&times;6<br>FEED :- (4)&times;6 = 24<br>ACCOUNT :- (7)&times;6 = 42<br>इसी प्रकार,<br>PIZZA :- (5)&times;6 = 30</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "6",
                    question_en: "<p>3. Which two numbers (not individual digits) should be interchanged to make the given equation correct? <br>[(39 - 3) &divide; 6] + 11 &times; 2 - 4 &times; 5 = 13</p>",
                    question_hi: "<p>3. दिए गए समीकरण को संतुलित करने के लिए किन दो संख्याओं (अकेले अंकों को नहीं) को आपस में बदलना होगा?<br>[(39 - 3) &divide; 6] + 11 &times; 2 - 4 &times; 5 = 13</p>",
                    options_en: [
                        "<p>11 and 4</p>",
                        "<p>2 and 6</p>",
                        "<p>2 and 5</p>",
                        "<p>3 and 6</p>"
                    ],
                    options_hi: [
                        "<p>11 और 4</p>",
                        "<p>2 और 6</p>",
                        "<p>2 और 5</p>",
                        "<p>3 और 6</p>"
                    ],
                    solution_en: "<p>3.(d) <strong>Given :-</strong> [(39 - 3) <math display=\"inline\"><mo>&#247;</mo></math> 6] + 11 &times; 2 - 4 &times; 5 = 13<br>After checking all the options one by one, option (d) satisfies. After interchanging 3 and 6 we get<br>[(39 - 6) <math display=\"inline\"><mo>&#247;</mo></math> 3] + 11 &times; 2 - 4 &times; 5<br>[(33) <math display=\"inline\"><mo>&#247;</mo></math> 3] + 22 - 20<br>11 + 22 - 20 = 13<br>L.H.S. = R.H.S.</p>",
                    solution_hi: "<p>3.(d) <strong>दिया गया :-</strong> [(39 - 3) <math display=\"inline\"><mo>&#247;</mo></math> 6] + 11 &times; 2 - 4 &times; 5 = 13<br>सभी विकल्पों की जांच करने पर विकल्प (d) संतुष्ट करता है। 3 और 6 को आपस में बदलने पर हमें प्राप्त होता है<br>[(39 - 6) <math display=\"inline\"><mo>&#247;</mo></math> 3] + 11 &times; 2 - 4 &times; 5<br>[(33) <math display=\"inline\"><mo>&#247;</mo></math> 3] + 22 - 20<br>11 + 22 - 20 = 13<br>L.H.S. = R.H.S.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "6",
                    question_en: "<p>4. Read the given statements and conclusions carefully. Assuming that the information given in the statements is true, even if it appears to be at variance with commonly known facts, decide which of the given conclusions logically follow(s) from the statements.<br><strong>Statements :</strong><br>1) Some drinks are juices.<br>2) Some juices are sodas.<br>3) All sodas are cocktails.<br><strong>Conclusions :</strong><br>I. No drink is a soda.<br>II. At least some juices are cocktails.</p>",
                    question_hi: "<p>4. दिए गए कथनों और निष्कर्षों को ध्यानपूर्वक पढ़ें। यह मानते हुए कि कथनों में दी गई जानकारी सत्य है, भले ही वह सामान्य रूप से ज्ञात तथ्यों से भिन्न प्रतीत होती हो, और तय करें कि दिए गए निष्कर्षों में से कौन-सा/से निष्कर्ष कथनों का तार्किक रूप से अनुसरण करता है/करते हैं।<br><strong>कथन :</strong><br>1) कुछ पेय जूस हैं।<br>2) कुछ जूस सोड़ा हैं।<br>3) सभी सोडा कॉकटेल हैं।<br><strong>निष्कर्ष :</strong><br>I. कोई भी पेय सोडा नहीं है।<br>II. कम से कम कुछ जूस कॉकटेल हैं।</p>",
                    options_en: [
                        "<p>Only conclusion Il follows</p>",
                        "<p>Neither conclusion I nor II follows</p>",
                        "<p>Both conclusions I and II follow</p>",
                        "<p>Only conclusion I follows</p>"
                    ],
                    options_hi: [
                        "<p>केवल निष्कर्ष Il अनुसरण करता है।</p>",
                        "<p>न तो निष्कर्ष I अनुसरण करता और न ही Il अनुसरण करता है।</p>",
                        "<p>निष्कर्ष। और Il दोनों अनुसरण करते हैं।</p>",
                        "<p>केवल निष्कर्ष I अनुसरण करता है।</p>"
                    ],
                    solution_en: "<p>4.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743652247313.png\" alt=\"rId11\" width=\"296\" height=\"68\"><br>Only conclusion II follows.</p>",
                    solution_hi: "<p>4.(a) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743652247447.png\" alt=\"rId12\" width=\"299\" height=\"71\"><br>केवल निष्कर्ष II अनुसरण करता है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "6",
                    question_en: "<p>5. Based on the English alphabetical order, three of the following four letter-clusters are alike in a certain way and thus form a group. Which letter-cluster does not belong to that group ?<br>(Note : The odd letter-cluster is not based on the number of consonants/vowels or their position in the letter-cluster.)</p>",
                    question_hi: "<p>5. अंग्रेजी वर्णमाला क्रम पर आधारित, निम्नलिखित चार अक्षर-समूहों में से तीन किसी निश्चित तरीके से समान हैं और इस प्रकार एक समूह बनाते हैं। किस अक्षर-समूह का संबंध उस समूह से नहीं है ?<br>(नोट : असंगत अक्षर-समूह, व्यंजनों/स्वरों की संख्या या इस अक्षर-समूह में उनकी स्थिति पर आधारित नहीं है।)</p>",
                    options_en: [
                        "<p>RWT</p>",
                        "<p>PUR</p>",
                        "<p>TYU</p>",
                        "<p>NSP</p>"
                    ],
                    options_hi: [
                        "<p>RWT</p>",
                        "<p>PUR</p>",
                        "<p>TYU</p>",
                        "<p>NSP</p>"
                    ],
                    solution_en: "<p>5.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743652247549.png\" alt=\"rId13\" width=\"116\" height=\"65\">, <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743652247643.png\" alt=\"rId14\" width=\"112\" height=\"73\">, <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743652247739.png\" alt=\"rId15\" width=\"127\" height=\"74\"><br>But,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743652247843.png\" alt=\"rId16\" width=\"135\" height=\"76\"></p>",
                    solution_hi: "<p>5.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743652247549.png\" alt=\"rId13\" width=\"116\" height=\"65\">, <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743652247643.png\" alt=\"rId14\" width=\"112\" height=\"73\">, <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743652247739.png\" alt=\"rId15\" width=\"127\" height=\"74\"><br>लेकिन,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743652247843.png\" alt=\"rId16\" width=\"135\" height=\"76\"></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "6",
                    question_en: "<p>6. Select the pair which is related to each other in the same way as the following &ndash;<br>186 : <math display=\"inline\"><msup><mrow><mn>16</mn></mrow><mrow><mn>8</mn></mrow></msup></math></p>",
                    question_hi: "<p>6. उस युग्म का चयन कीजिए जो एक दूसरे से उसी प्रकार संबंधित है जिस प्रकार नीचे दिया गया युग्म संबंधित है<br>186:<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>16</mn><mn>8</mn></msup></math></p>",
                    options_en: [
                        "<p>486:<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>46</mn><mn>4</mn></msup></math></p>",
                        "<p>237:<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>27</mn><mn>3</mn></msup></math></p>",
                        "<p>729:<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>79</mn><mn>4</mn></msup></math></p>",
                        "<p>324:<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>24</mn><mn>3</mn></msup></math></p>"
                    ],
                    options_hi: [
                        "<p>486:<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>46</mn><mn>4</mn></msup></math></p>",
                        "<p>237:<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>27</mn><mn>3</mn></msup></math></p>",
                        "<p>729:<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>79</mn><mn>4</mn></msup></math></p>",
                        "<p>324:<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>24</mn><mn>3</mn></msup></math></p>"
                    ],
                    solution_en: "<p>6.(b) <strong>Logic:-</strong> (Middle number is taken as power)<br>186 : 16<sup>8</sup> :- (16)<sup>8</sup> <br>Similarly,<br>237 : ? :- (27)<sup>3</sup></p>",
                    solution_hi: "<p>6.(b) <strong>तर्क:- </strong>(मध्य संख्या को घात के रूप में लिया जाता है)<br>186 : 16<sup>8</sup> :- (16)<sup>8</sup> <br>इसी प्रकार,<br>237 : ? :- (27)<sup>3</sup></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "6",
                    question_en: "<p>7. Select the term from among the given options that can replace the question mark (?) in the following series.<br>ADG, ILO, QTW, YBE, ?</p>",
                    question_hi: "<p>7. दिए गए विकल्पों में से उस पद का चयन कीजिए जो निम्नलिखित श्रृंखला में प्रश्न चिह्न (?) के स्थान पर आ सकता है।<br>ADG, ILO, QTW, YBE, ?</p>",
                    options_en: [
                        "<p>HKL</p>",
                        "<p>GIL</p>",
                        "<p>HJM</p>",
                        "<p>GJM</p>"
                    ],
                    options_hi: [
                        "<p>HKL</p>",
                        "<p>GIL</p>",
                        "<p>HJM</p>",
                        "<p>GJM</p>"
                    ],
                    solution_en: "<p>7.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743652247970.png\" alt=\"rId17\" width=\"277\" height=\"91\"></p>",
                    solution_hi: "<p>7.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743652247970.png\" alt=\"rId17\" width=\"277\" height=\"91\"></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "6",
                    question_en: "<p>8. Select the option that is related to the third word in the same way as the second word is related to the first word .( The words must be considered as meaningful English words and must NOT be related to each other bases on the number of letters /number of consonants / vowels in the word)<br>Map : Directions :: Manual : ____________</p>",
                    question_hi: "<p>8. उस विकल्प का चयन करें, जो तीसरे शब्द से उसी प्रकार संबंधित है, जैसे दूसरा शब्द पहले शब्द से संबंधित है। (शब्दों को हिन्दी के अर्थपूर्ण शब्दों के रूप में माना जाना चाहिए और ये शब्द, अक्षरों की संख्या/व्यंजनों/स्वरों की संख्या के आधार पर एक-दूसरे से संबंधित नहीं होने चाहिए)<br>नक्शा : दिशाएँ :: नियमावली :</p>",
                    options_en: [
                        "<p>Assumptions</p>",
                        "<p>Conclusions</p>",
                        "<p>Statements</p>",
                        "<p>Instructions</p>"
                    ],
                    options_hi: [
                        "<p>मान्यता</p>",
                        "<p>निष्कर्ष</p>",
                        "<p>कथन</p>",
                        "<p>अनुदेश</p>"
                    ],
                    solution_en: "<p>8.(d)<br>As &lsquo;Map&rsquo; provides &lsquo;Directions&rsquo; similarly a &lsquo;Manual&rsquo; provides &lsquo;Instructions&rsquo;.</p>",
                    solution_hi: "<p>8.(d)<br>जैसे \'नक्शा\', \'दिशाएँ \' दिखाता है उसी प्रकार \'नियमावली&rsquo;, \'अनुदेश \' प्रदर्शित करती है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "6",
                    question_en: "<p>9. If<br>&lsquo;A &amp; Z&rsquo; means &lsquo;A is the brother of Z&rsquo;,<br>&lsquo;A # Z&rsquo; means &lsquo;A is the sister of Z&rsquo;,<br>&lsquo;A ^ Z&rsquo; means &lsquo;A is the brother of Z&rsquo;s father&rsquo;,<br>&lsquo;A @ Z&rsquo; means &lsquo;A is the wife of Z&rsquo;,<br>&lsquo;A % Z&rsquo; means &lsquo;A is the mother of Z&rsquo;,<br>&lsquo;A &alpha; Z&rsquo; means &lsquo;A is the father of Z&rsquo;,<br>then how is J related to Z in the following expression?<br><strong>J % K # L @ M &alpha; N &amp; Z</strong></p>",
                    question_hi: "<p>9. यदि<br>\'A &amp; Z\' का अर्थ है \'A, Z का भाई है\',<br>\'A # Z\' का अर्थ है \'A, Z की बहन है\',<br>\'A ^ Z\' का अर्थ है \'A, Z के पिता का भाई है\',<br>\'A @ Z\' का अर्थ है \'A, Z की पत्नी है\',<br>\'A % Z\' का अर्थ है \'A, Z की माता है\',<br>\'A &alpha; Z\' का अर्थ है \'A, Z का पिता है\',<br>तो निम्न व्यंजक में J, Z से किस प्रकार संबंधित है?<br><strong>J % K # L @ M &alpha; N &amp; Z</strong></p>",
                    options_en: [
                        "<p>Mother</p>",
                        "<p>Father&rsquo;s mother</p>",
                        "<p>Mother&rsquo;s sister</p>",
                        "<p>Mother&rsquo;s mother</p>"
                    ],
                    options_hi: [
                        "<p>माता</p>",
                        "<p>दादी</p>",
                        "<p>मौसी</p>",
                        "<p>नानी</p>"
                    ],
                    solution_en: "<p>9.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743652248126.png\" alt=\"rId18\" width=\"157\" height=\"146\"><br>Hence, J is the mother&rsquo;s mother of Z.</p>",
                    solution_hi: "<p>9.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743652248126.png\" alt=\"rId18\" width=\"157\" height=\"146\"><br>अतः, J, Z की माँ की माँ (नानी ) है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "6",
                    question_en: "<p>10. Select the Venn diagram that best illustrates the relationship between the following classes. <br>Room, House, Shirt</p>",
                    question_hi: "<p>10. उस वेन आरेख का चयन कीजिए, जो निम्नलिखित वर्गों के बीच संबंध को सबसे अच्छी तरह दर्शाता है। <br>कमरा, घर, शर्ट</p>",
                    options_en: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743652248261.png\" alt=\"rId19\" width=\"179\" height=\"60\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743652248430.png\" alt=\"rId20\" width=\"133\" height=\"62\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743652248541.png\" alt=\"rId21\" width=\"193\" height=\"60\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743652248671.png\" alt=\"rId22\" width=\"89\" height=\"85\"></p>"
                    ],
                    options_hi: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743652248261.png\" alt=\"rId19\" width=\"179\" height=\"60\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743652248430.png\" alt=\"rId20\" width=\"133\" height=\"62\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743652248541.png\" alt=\"rId21\" width=\"193\" height=\"60\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743652248671.png\" alt=\"rId22\" width=\"89\" height=\"85\"></p>"
                    ],
                    solution_en: "<p>10.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743652248939.png\" alt=\"rId23\" width=\"221\" height=\"73\"></p>",
                    solution_hi: "<p>10.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743652249200.png\" alt=\"rId24\" width=\"209\" height=\"69\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "6",
                    question_en: "11. The position of how many letters will remain unchanged if each of the letters in the word ‘DRAFTING’ is arranged in the English alphabetical order?",
                    question_hi: "11.  यदि शब्द \'DRAFTING\' के प्रत्येक अक्षर को अँग्रेजी वर्णमाला क्रम में व्यवस्थित किया जाए, तो कितने अक्षरों का स्थान अपरिवर्तित रहेगा?",
                    options_en: [
                        " One",
                        " Two",
                        " Three",
                        " None"
                    ],
                    options_hi: [
                        " एक ",
                        " दो ",
                        " तीन",
                        " कोई नहीं"
                    ],
                    solution_en: "<p>11.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743652249374.png\" alt=\"rId25\" width=\"361\" height=\"149\"><br>Hence, the position of all letters will be changed.</p>",
                    solution_hi: "<p>11.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743652249374.png\" alt=\"rId25\" width=\"361\" height=\"149\"><br>अतः , सभी अक्षरों की स्थिति बदल दी जाएगी।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "6",
                    question_en: "<p>12. Select the correct mirror image of the given figure when the mirror is placed at MN as shown below.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743652249474.png\" alt=\"rId26\"></p>",
                    question_hi: "<p>12. जब दर्पण को नीचे दर्शाए गए अनुसार MN पर रखा जाता है तो दी गई आकृति के सही दर्पण प्रतिबिंब का चयन कीजिए।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743652249474.png\" alt=\"rId26\"></p>",
                    options_en: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743652249577.png\" alt=\"rId27\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743652249669.png\" alt=\"rId28\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743652249746.png\" alt=\"rId29\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743652249842.png\" alt=\"rId30\"></p>"
                    ],
                    options_hi: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743652249577.png\" alt=\"rId27\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743652249669.png\" alt=\"rId28\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743652249746.png\" alt=\"rId29\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743652249842.png\" alt=\"rId30\"></p>"
                    ],
                    solution_en: "<p>12.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743652249577.png\" alt=\"rId27\"></p>",
                    solution_hi: "<p>12.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743652249577.png\" alt=\"rId27\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "6",
                    question_en: "<p>13. 11 is related to 121 following a certain logic. Following the same logic, 22 is related to 484. To which of the following is 31 related, following the same logic? <br>(NOTE: Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g. 13 &ndash; Operations on 13 such as adding /deleting /multiplying etc. to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed)</p>",
                    question_hi: "<p>13. एक निश्चित तर्क का अनुसरण करते हुए 11, 121 से संबंधित है। उसी तर्क का अनुसरण करते हुए, 22, 484 से संबंधित है। उसी तर्क का अनुसरण करते हुए, 31 निम्नलिखित में से किससे संबंधित है? <br>(नोट: संख्याओं को उसके घटक अंकों में तोड़े बिना, पूर्ण संख्याओं पर संक्रियाएं की जानी चाहिए। उदाहरण के लिए संख्या 13 को लें - 13 पर संक्रियाएं, जैसे कि 13 में जोड़ना/घटाना/गुणा करना आदि, की जा सकती हैं। 13 को 1 और 3 में तोड़ने और फिर 1 और 3 पर गणितीय संक्रियाएं करने की अनुमति नहीं है)</p>",
                    options_en: [
                        "<p>961</p>",
                        "<p>957</p>",
                        "<p>943</p>",
                        "<p>973</p>"
                    ],
                    options_hi: [
                        "<p>961</p>",
                        "<p>957</p>",
                        "<p>943</p>",
                        "<p>973</p>"
                    ],
                    solution_en: "<p>13.(a)<br><strong>Logic:-</strong> <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><msup><mn>1</mn><mrow><mi>s</mi><mi>t</mi></mrow></msup><mi>n</mi><mi>o</mi><mo>.</mo><msup><mo>)</mo><mn>2</mn></msup></math> = 2<sup>nd </sup>no.<br>(11, 121) :- <math display=\"inline\"><mo>(</mo><mn>11</mn><msup><mrow><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup></math> = 121<br>(22, 484) :- <math display=\"inline\"><mo>(</mo><mn>22</mn><msup><mrow><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup></math> = 484<br>similarly<br>(31, ?) :- <math display=\"inline\"><mo>(</mo><mn>31</mn><msup><mrow><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup></math> = 961</p>",
                    solution_hi: "<p>13.(a)<br><strong>तर्क:-</strong> (प्रथम संख्या)<sup>2</sup><strong id=\"docs-internal-guid-5c91d876-7fff-0cca-1e68-8e9dc0f8a6bd\"> </strong>= दूसरी संख्या <br>(11, 121) :- <math display=\"inline\"><mo>(</mo><mn>11</mn><msup><mrow><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup></math> = 121<br>(22, 484) :- <math display=\"inline\"><mo>(</mo><mn>22</mn><msup><mrow><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup></math> = 484<br>इसी प्रकार <br>(31, ?) :- <math display=\"inline\"><mo>(</mo><mn>31</mn><msup><mrow><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup></math> = 961</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "6",
                    question_en: "<p>14. Which of the following numbers will replace the question mark (?) in the given series ?<br>3 , 10 , 24 , 45 , 73 , ?</p>",
                    question_hi: "<p>14. निम्नलिखित में से कौन-सी संख्या दी गई श्रृंखला में प्रश्न चिह्न (?) को प्रतिस्थापित करेगी?<br>3 , 10 , 24 , 45 , 73 , ?</p>",
                    options_en: [
                        "<p>107</p>",
                        "<p>106</p>",
                        "<p>108</p>",
                        "<p>109</p>"
                    ],
                    options_hi: [
                        "<p>107</p>",
                        "<p>106</p>",
                        "<p>108</p>",
                        "<p>109</p>"
                    ],
                    solution_en: "<p>14.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743652249980.png\" alt=\"rId31\" width=\"304\" height=\"59\"></p>",
                    solution_hi: "<p>14.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743652249980.png\" alt=\"rId31\" width=\"299\" height=\"58\"></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "6",
                    question_en: "<p>15. Six letters A, B, C, D, E and F are written on different faces of a dice. Three positions of this dice are shown in the figure. Find the letter on the face opposite to A.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743652250108.png\" alt=\"rId32\" width=\"223\" height=\"86\"></p>",
                    question_hi: "<p>15. एक पासे के विभिन्न फलकों पर छ: अक्षर A, B, C, D, E और F लिखे गए हैं। नीचे चित्र में इस पासे की तीन स्थितियाँ दिखाई गई है। अक्षर A के विपरीत फलक पर कौन-सा अक्षर है?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743652250108.png\" alt=\"rId32\" width=\"223\" height=\"86\"></p>",
                    options_en: [
                        "<p>C</p>",
                        "<p>D</p>",
                        "<p>E</p>",
                        "<p>F</p>"
                    ],
                    options_hi: [
                        "<p>C</p>",
                        "<p>D</p>",
                        "<p>E</p>",
                        "<p>F</p>"
                    ],
                    solution_en: "<p>15.(c) in the dice i and ii , letter (B , C) is common<br>So, the opposite of &lsquo;A&rsquo; is &lsquo;E&rsquo;.</p>",
                    solution_hi: "<p>15.(c) पासे i और ii में, अक्षर (B, C) उभयनिष्ठ है<br>तो, \'&lsquo;A&rsquo;\' का विपरीत &lsquo;E है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "6",
                    question_en: "<p>16. How many rectangles are there in the given figure? <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743652250236.png\" alt=\"rId33\" width=\"99\" height=\"99\"></p>",
                    question_hi: "<p>16 दी गई आकृति में कितने आयत हैं? <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743652250236.png\" alt=\"rId33\" width=\"95\" height=\"95\"></p>",
                    options_en: [
                        "<p>6</p>",
                        "<p>8</p>",
                        "<p>7</p>",
                        "<p>9</p>"
                    ],
                    options_hi: [
                        "<p>6</p>",
                        "<p>8</p>",
                        "<p>7</p>",
                        "<p>9</p>"
                    ],
                    solution_en: "<p>16.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743652250416.png\" alt=\"rId34\" width=\"118\" height=\"104\"><br>There are 9 rectangle <br>ABHI , BCGH, HGDE, HEFI , ACGI , IGDF, ABEF, BCDE, ACDF</p>",
                    solution_hi: "<p>16.(d) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743652250416.png\" alt=\"rId34\" width=\"118\" height=\"104\"><br>यहाँ 9 आयत हैं<br>ABHI , BCGH, HGDE, HEFI , ACGI , IGDF, ABEF, BCDE, ACDF</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "6",
                    question_en: "<p>17. In a certain code language, \'LAST\' is coded as \'5489\', and \'SILT\' is coded as \'9465\'. What is the code for \'I\' in that language?</p>",
                    question_hi: "<p>17. एक निश्चित कूट भाषा में \'LAST\' को \'5489\' के रूप में कूटबद्ध किया जाता है और \'SILT\' को \'9465\' के रूप में कूटबद्ध किया जाता है। उस भाषा में \'I\' के लिए कूट क्या है?</p>",
                    options_en: [
                        "<p>4</p>",
                        "<p>9</p>",
                        "<p>6</p>",
                        "<p>5</p>"
                    ],
                    options_hi: [
                        "<p>4</p>",
                        "<p>9</p>",
                        "<p>6</p>",
                        "<p>5</p>"
                    ],
                    solution_en: "<p>17.(c) LAST &rarr; 5489&hellip;&hellip;. (i)<br>SILT &rarr; 9465&hellip;&hellip;..(ii)<br>From (i) and (ii) &lsquo;L&rsquo;, &lsquo;S&rsquo; , &lsquo;T&rsquo; and &lsquo;5&rsquo; , &lsquo;4&rsquo; , &lsquo;9&rsquo; are common. The code of &lsquo;I&rsquo; = &lsquo;6&rsquo;.</p>",
                    solution_hi: "<p>17.(c) LAST &rarr; 5489&hellip;&hellip;. (i)<br>SILT &rarr; 9465&hellip;&hellip;..(ii)<br>(i) और (ii) से \'L\', \'S\', \'T\' और \'5\', \'4\', \'9\' उभयनिष्ठ हैं। \'I\' का कोड = \'6\'.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "6",
                    question_en: "<p>18. In a certain code language, \'DESTROY\' is written as \'DYORTSE\' and \'EFFECTS\' is written as \'ESTCEFF\'. How will \'FOUNDED\' be written in that language?</p>",
                    question_hi: "<p>18. एक निश्चित कूट भाषा में, \'DESTROY\' को \'DYORTSE\' के रूप में लिखा जाता है और \'EFFECTS\' को \'ESTCEFF\' के रूप में लिखा जाता है। उसी भाषा में \'FOUNDED\' को कैसे लिखा जाएगा?</p>",
                    options_en: [
                        "<p>FOEDDNU</p>",
                        "<p>DFOUND</p>",
                        "<p>FDEDNUO</p>",
                        "<p>DEDNUOF</p>"
                    ],
                    options_hi: [
                        "<p>FOEDDNU</p>",
                        "<p>DFOUNDE</p>",
                        "<p>FDEDNUO</p>",
                        "<p>DEDNUOF</p>"
                    ],
                    solution_en: "<p>18.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743652250540.png\" alt=\"rId35\" width=\"131\" height=\"73\">, <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743652250641.png\" alt=\"rId36\" width=\"128\" height=\"72\"><br>Similarly,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743652250748.png\" alt=\"rId37\" width=\"131\" height=\"71\"></p>",
                    solution_hi: "<p>18.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743652250540.png\" alt=\"rId35\" width=\"135\" height=\"75\">, <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743652250641.png\" alt=\"rId36\" width=\"130\" height=\"73\"><br>इसी प्रकार,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743652250748.png\" alt=\"rId37\" width=\"142\" height=\"77\"></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "6",
                    question_en: "<p>19. Select the option that represents the letters that, when placed from left to right in the blanks below, will complete the letter-series.<br>a_mx_q_yc_o_</p>",
                    question_hi: "<p>19. उस विकल्प का चयन करें, जो उन अक्षरों को दर्शाता है, जिन्हें निम्&zwj;न रिक्त स्थानों में बाएँ से दाएँ रखने पर अक्षर-शृंखला पूर्ण हो जाएगी।<br>a_mx_q_yc_o_</p>",
                    options_en: [
                        "<p>pnbrz</p>",
                        "<p>pbnrz</p>",
                        "<p>prnbx</p>",
                        "<p>nbprz</p>"
                    ],
                    options_hi: [
                        "<p>pnbrz</p>",
                        "<p>pbnrz</p>",
                        "<p>prnbx</p>",
                        "<p>nbprz</p>"
                    ],
                    solution_en: "<p>19.(b) a<span style=\"text-decoration: underline;\"><strong>p</strong></span>mx / <strong><span style=\"text-decoration: underline;\">b</span></strong>q<span style=\"text-decoration: underline;\"><strong>n</strong></span>y / c<span style=\"text-decoration: underline;\"><strong>r</strong></span>o<span style=\"text-decoration: underline;\"><strong>z</strong></span><br><strong>Logic :-</strong> Letters are increasing by +1.</p>",
                    solution_hi: "<p>19.(b) a<span style=\"text-decoration: underline;\"><strong>p</strong></span>mx / <span style=\"text-decoration: underline;\"><strong>b</strong></span>q<span style=\"text-decoration: underline;\"><strong>n</strong></span>y / c<span style=\"text-decoration: underline;\"><strong>r</strong></span>o<span style=\"text-decoration: underline;\"><strong>z</strong></span><br><strong>तर्क :-</strong> अक्षर +1 से बढ़ रहे हैं।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "6",
                    question_en: "<p>20. Select the correct combination of mathematical signs to sequentially replace * signs and to balance the given equation.<br>7 * 5 * (16 * 8 * 9) * 20</p>",
                    question_hi: "<p>20. * चिह्नों को क्रमिक रूप से प्रतिस्थापित करने और दिए गए समीकरण को संतुलित करने के लिए गणितीय चिह्नों के सही संयोजन का चयन करें।<br>7 * 5 * (16 * 8 * 9) * 20</p>",
                    options_en: [
                        "<p>&divide;, -, &times;, +, =</p>",
                        "<p>-, +, &divide;, &times;, =</p>",
                        "<p>-, +, &times;, &divide;, =</p>",
                        "<p>+, &times;, &divide;, -, =</p>"
                    ],
                    options_hi: [
                        "<p>&divide;, -, &times;, +, =</p>",
                        "<p>-, +, &divide;, &times;, =</p>",
                        "<p>-, +, &times;, &divide;, =</p>",
                        "<p>+, &times;, &divide;, -, =</p>"
                    ],
                    solution_en: "<p>20.(b) After going through all the options, option (c) satisfies. <br>7 - 5 + (16 <math display=\"inline\"><mo>&#247;</mo></math> 8 &times; 9) = 20<br>2 + (2 &times; 9) = 20<br>2 + 18 = 20<br>20 = 20<br>L.H.S. = R.H.S.</p>",
                    solution_hi: "<p>20.(b) सभी विकल्पों की जांच करने पर विकल्प (c) संतुष्ट करता है।<br>7 - 5 + (16 <math display=\"inline\"><mo>&#247;</mo></math> 8 &times; 9) = 20<br>2 + (2 &times; 9) = 20<br>2 + 18 = 20<br>20 = 20<br>L.H.S. = R.H.S.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "6",
                    question_en: "<p>21. Which of the following letter-clusters should replace # and % so that the pattern and relationship followed between the letter-cluster pair on the left side of :: is the same as that on the right side of :: ?<br># : VXB :: ACG : %</p>",
                    question_hi: "<p>21. निम्नलिखित में से कौन-सा अक्षर-समूह # और % के स्थान पर आना चाहिए ताकि :: के बाएँ ओर के अक्षर- समूह युग्म के बीच जिस पैटर्न और संबंध का अनुसरण किया गया है, उसी पैटर्न और संबंध का अनुसरण :: के दाएँ ओर के अक्षर-समूह युग्म में किया जाता हो?<br># : VXB :: ACG : %</p>",
                    options_en: [
                        "<p># - GIM, % - KMQ</p>",
                        "<p># - UVN, % - KMQ</p>",
                        "<p># - HDN, % - BDH</p>",
                        "<p># - UWA, % - BDH</p>"
                    ],
                    options_hi: [
                        "<p># - GIM, % - KMQ</p>",
                        "<p># - UVN, % - KMQ</p>",
                        "<p># - HDN, % - BDH</p>",
                        "<p># - UWA, % - BDH</p>"
                    ],
                    solution_en: "<p>21.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743652250902.png\" alt=\"rId38\" width=\"137\" height=\"95\"><br>Similarly,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743652251009.png\" alt=\"rId39\" width=\"140\" height=\"95\"></p>",
                    solution_hi: "<p>21.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743652250902.png\" alt=\"rId38\" width=\"137\" height=\"95\"><br>इसी प्रकार,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743652251009.png\" alt=\"rId39\" width=\"140\" height=\"95\"></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "6",
                    question_en: "<p>22. Three of the following word pairs are alike in some manner and hence form a group. Which word pair does not belong to that group?<br>(The words must be considered as meaningful English words and must not be grouped based on the number of letters/number of consonants/vowels in the word.)</p>",
                    question_hi: "<p>22. निम्नलिखित चार शब्द युग्मों में से तीन शब्द युग्म किसी प्रकार से एक समान हैं और इस प्रकार एक समूह बनाते हैं। कौन-सा शब्द युग्म उस समूह से संबंधित नहीं है?<br>(शब्दों को अर्थपूर्ण हिंदी शब्द माना जाना चाहिए और इन्हें शब्द में अक्षरों की संख्या/व्यंजनों/स्वरों की संख्या के आधार पर समूहीकृत नहीं किया जाना चाहिए।)</p>",
                    options_en: [
                        "<p>Ball - Sphere</p>",
                        "<p>Birthday hat - Cone</p>",
                        "<p>Television - Triangle</p>",
                        "<p>Ring - Circle</p>"
                    ],
                    options_hi: [
                        "<p>गेंद - गोला</p>",
                        "<p>जन्मदिन की टोपी - शंकु</p>",
                        "<p>टेलीविजन - त्रिभुज</p>",
                        "<p>अँगूठी - वृत्त</p>"
                    ],
                    solution_en: "<p>22.(c) As Ball is sphere , birthday hat is cone , ring is circle but television is not of triangle shape.</p>",
                    solution_hi: "<p>22.(c) जैसे गेंद गोलाकार है, जन्मदिन की टोपी शंकु है, अंगूठी वृत्त है लेकिन टेलीविजन त्रिभुज आकार का नहीं है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "23",
                    section: "6",
                    question_en: "<p>23. The sequence of folding a piece of paper and the manner in which the folded paper has been cut is shown below. Select the option figure that would most closely resemble the unfolded form of the paper.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743652251131.png\" alt=\"rId40\" width=\"225\" height=\"62\"></p>",
                    question_hi: "<p>23. एक कागज को मोड़ने का क्रम और मोड़े गए कागज को काटने का तरीका नीचे दर्शाया गया है। उस विकल्प आकृति का चयन कीजिए जो कागज के खुले रूप से (unfolded form) सबसे अधिक मिलती जुलती हो।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743652251131.png\" alt=\"rId40\" width=\"225\" height=\"62\"></p>",
                    options_en: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743652251244.png\" alt=\"rId41\" width=\"72\" height=\"72\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743652251399.png\" alt=\"rId42\" width=\"72\" height=\"72\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743652251494.png\" alt=\"rId43\" width=\"74\" height=\"75\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743652251629.png\" alt=\"rId44\" width=\"73\" height=\"71\"></p>"
                    ],
                    options_hi: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743652251797.png\" alt=\"rId45\" width=\"72\" height=\"73\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743652252006.png\" alt=\"rId46\" width=\"74\" height=\"74\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743652251494.png\" alt=\"rId43\" width=\"73\" height=\"74\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743652251629.png\" alt=\"rId44\" width=\"71\" height=\"69\"></p>"
                    ],
                    solution_en: "<p>23.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743652251244.png\" alt=\"rId41\" width=\"72\" height=\"72\"></p>",
                    solution_hi: "<p>23.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743652251244.png\" alt=\"rId41\" width=\"73\" height=\"73\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "24",
                    section: "6",
                    question_en: "<p>24. Select the triad from among the given options that is analogous to the given triad. <br>668 - 776 - 992</p>",
                    question_hi: "<p>24. दिए गए विकल्पों में से उस त्रयी का चयन कीजिए जो दी गई त्रयी के सदृश हो।<br>668 - 776 - 992</p>",
                    options_en: [
                        "<p>195 - 285 - 348</p>",
                        "<p>488 - 398 - 299</p>",
                        "<p>725 - 623 - 542</p>",
                        "<p>144 - 225 - 729</p>"
                    ],
                    options_hi: [
                        "<p>195 - 285 - 348</p>",
                        "<p>488 - 398 - 299</p>",
                        "<p>725 - 623 - 542</p>",
                        "<p>144 - 225 - 729</p>"
                    ],
                    solution_en: "<p>24.(b) <strong>Logic:-</strong> Digital sum of each number is 20<br><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743652252197.png\" alt=\"rId47\" width=\"190\" height=\"121\">Similarly<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743652252358.png\" alt=\"rId48\" width=\"207\" height=\"126\"></p>",
                    solution_hi: "<p>24.(b) तर्क:- प्रत्येक संख्या का अंकीय योग 20 है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743652252197.png\" alt=\"rId47\" width=\"190\" height=\"121\"> इसी प्रकार <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743652252358.png\" alt=\"rId48\" width=\"207\" height=\"126\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "25",
                    section: "18",
                    question_en: "<p>25. Select the option that represents the correct order of the given words as they would appear in an English dictionary. <br>1. Purposively <br>2. Pursuit <br>3. Purgatory <br>4. Purify <br>5. Purports <br>6. Purchased</p>",
                    question_hi: "<p>25. उस विकल्प का चयन करें, जो दिए गए शब्दों के उस सही क्रम को दर्शाता है, जिसमें वे अंग्रेज़ी शब्दकोश में दिखाई देंगे। <br>1. Purposively <br>2. Pursuit <br>3. Purgatory <br>4. Purify <br>5. Purports <br>6. Purchased</p>",
                    options_en: [
                        "<p>4, 5, 6, 3, 1, 2</p>",
                        "<p>6, 3, 4, 5, 1, 2</p>",
                        "<p>6, 3, 1, 5, 4, 2</p>",
                        "<p>4, 5, 3, 1, 6, 2</p>"
                    ],
                    options_hi: [
                        "<p>4, 5, 6, 3, 1, 2</p>",
                        "<p>6, 3, 4, 5, 1, 2</p>",
                        "<p>6, 3, 1, 5, 4, 2</p>",
                        "<p>4, 5, 3, 1, 6, 2</p>"
                    ],
                    solution_en: "<p>25.(b) <strong>The correct order is</strong> <br>Purchased(6) &rarr; Purgatory(3) &rarr; Purify(4) &rarr; Purports(5) &rarr; Purposively(1) &rarr; Pursuit(2)</p>",
                    solution_hi: "<p>25.(b) <strong>सही क्रम है</strong><br>Purchased(6) &rarr; Purgatory(3) &rarr; Purify(4) &rarr; Purports(5) &rarr; Purposively(1) &rarr; Pursuit(2)</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "26",
                    section: "18",
                    question_en: "<p>26. Shab-e-Barat is celebrated by which religious group?</p>",
                    question_hi: "<p>26. शब-ए-बारात किस धार्मिक समूह द्वारा मनाया जाता है ?</p>",
                    options_en: [
                        "<p>Muslim</p>",
                        "<p>Buddhist</p>",
                        "<p>Jain</p>",
                        "<p>Parsi</p>"
                    ],
                    options_hi: [
                        "<p>मुसलमान</p>",
                        "<p>बौद्ध</p>",
                        "<p>जैन</p>",
                        "<p>पारसी</p>"
                    ],
                    solution_en: "<p>26.(a) <strong>Muslim.</strong> Shab-e-Barat, also known as Mid-Sha\'ban, is observed on the 15th night of Sha\'ban in the Islamic calendar. Festivals celebrated by : Muslim - Eid al-Fitr, Eid al-Adha etc; Buddhist - Buddha Purnima, Vesak etc; Jain - Mahavir Jayanti, Paryushana etc; Parsi - Navroz, Jamshedi Navroz etc.</p>",
                    solution_hi: "<p>26.(a) <strong>मुसलमान।</strong> शब-ए-बारात, जिसे मध्य-शाबान के नाम से भी जाना जाता है, जो इस्लामी कैलेंडर में शाबान की 15वीं रात को मनाया जाता है। मुसलमानो द्वारा मनाए जाने वाले त्योहार: ईद-उल-फितर, ईद-उल-अज़हा आदि; बौद्ध- बुद्ध पूर्णिमा, वेसाक आदि; जैन - महावीर जयंती, पर्युषण आदि; पारसी - नवरोज़, जमशेदी नवरोज़ आदि।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "27",
                    section: "18",
                    question_en: "<p>27. In 2002, Zakir Hussain became the youngest percussionist to be honoured with which award?</p>",
                    question_hi: "<p>27. वर्ष 2002 में, ज़ाकिर हुसैन किस पुरस्कार से सम्मानित होने वाले सबसे कम उम्र के तालवादक बने?</p>",
                    options_en: [
                        "<p>Sangeet Natak Akademi Award</p>",
                        "<p>Grammy Award</p>",
                        "<p>Padma Bhushan</p>",
                        "<p>National Heritage Fellowship</p>"
                    ],
                    options_hi: [
                        "<p>संगीत नाटक अकादमी पुरस्कार</p>",
                        "<p>ग्रैमी पुरस्कार</p>",
                        "<p>पद्म भूषण</p>",
                        "<p>नेशनल हेरिटेज फैलोशिप</p>"
                    ],
                    solution_en: "<p>27.(c) <strong>Padma Bhushan.</strong> Ustad Zakir Hussain is the eldest son of tabla player Alla Rakha. His Awards: Sangeet Natak Akademi Award (1991), Padma Shri (1988), Padma Vibhushan (2023). He won three Grammys at the 66th Annual Grammy Awards in 2024. Indian tabla players: Pandit Shankar Ghosh, Pandit Udhai Mazumdar, Nandan Mehta, Pandit Swapan Chaudhuri, Pandit Vijay Ghate.</p>",
                    solution_hi: "<p>27.(c) <strong>पद्म भूषण।</strong> उस्ताद ज़ाकिर हुसैन तबला वादक अल्ला रक्खा के सबसे बड़े पुत्र हैं। उनके पुरस्कार: संगीत नाटक अकादमी पुरस्कार (1991), पद्म श्री (1988), पद्म विभूषण (2023)। उन्होंने 2024 में 66वें वार्षिक ग्रैमी अवार्ड्स में तीन ग्रैमी पुरस्कार जीते थे। भारतीय तबला वादक: पंडित शंकर घोष, पंडित उदय मजूमदार, नंदन मेहता, पंडित स्वपन चौधरी, पंडित विजय घाटे।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "28",
                    section: "18",
                    question_en: "<p>28. Who released the report linking alcohol consumption to an increased risk of cancer?</p>",
                    question_hi: "<p>28. शराब के सेवन को कैंसर के बढ़ते जोखिम से जोड़ने वाली रिपोर्ट किसने जारी की?</p>",
                    options_en: [
                        "<p>World Health Organization (WHO)</p>",
                        "<p>US Surgeon General</p>",
                        "<p>Centers for Disease Control and Prevention (CDC)</p>",
                        "<p>National Institutes of Health (NIH)</p>"
                    ],
                    options_hi: [
                        "<p>विश्व स्वास्थ्य संगठन (WHO)</p>",
                        "<p>यू.एस. सर्जन जनरल</p>",
                        "<p>रोग नियंत्रण और रोकथाम केंद्र (CDC)</p>",
                        "<p>राष्ट्रीय स्वास्थ्य संस्थान (NIH)</p>"
                    ],
                    solution_en: "<p>28.(b) <strong>US Surgeon General</strong> Dr. Vivek Murthy released a new Surgeon General&rsquo;s Advisory on Alcohol and Cancer Risk. The Surgeon General&rsquo;s report notes that alcohol consumption is the third leading preventable cause of cancer in the United States, after tobacco use and obesity. The World Health Organization (WHO) was founded in 1948 and is headquartered in Geneva, Switzerland.</p>",
                    solution_hi: "<p>28.(b) <strong>यू.एस. सर्जन जनरल</strong> डॉ. विवेक मूर्ति ने शराब और कैंसर के जोखिम पर सर्जन जनरल की नई सलाह जारी की। सर्जन जनरल की रिपोर्ट में कहा गया है कि संयुक्त राज्य अमेरिका में तंबाकू सेवन और मोटापे के बाद शराब का सेवन कैंसर का तीसरा सबसे बड़ा रोकथाम योग्य कारण है। विश्व स्वास्थ्य संगठन (WHO) की स्थापना 1948 में हुई थी और इसका मुख्यालय जिनेवा, स्विटज़रलैंड में है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "29",
                    section: "18",
                    question_en: "<p>29. In which year was the MPLADS scheme implemented?</p>",
                    question_hi: "<p>29. MPLADS योजना किस वर्ष लागू की गई थी?</p>",
                    options_en: [
                        "<p>2003</p>",
                        "<p>1983</p>",
                        "<p>1993</p>",
                        "<p>2013</p>"
                    ],
                    options_hi: [
                        "<p>2003</p>",
                        "<p>1983</p>",
                        "<p>1993</p>",
                        "<p>2013</p>"
                    ],
                    solution_en: "<p>29.(c) <strong>1993.</strong> Objective : The Member of Parliament Local Area Development Scheme (MPLADS) aims to empower MPs to recommend developmental projects that focus on creating durable community assets in areas such as drinking water, primary education, public health, sanitation, and road infrastructure, primarily within their constituencies. Each year, MPs receive ₹5 crore in two installments of ₹2.5 crore each. Notably, the funds allocated under MPLADS are non-lapsable.</p>",
                    solution_hi: "<p>29.(c) <strong>1993.</strong> उद्देश्य: संसद सदस्य स्थानीय क्षेत्र विकास योजना (MPLADS) का उद्देश्य सांसदों को विकास परियोजनाओं की सिफारिश करने के लिए सशक्त बनाना है, जो मुख्य रूप से उनके निर्वाचन क्षेत्रों में पेयजल, प्राथमिक शिक्षा, सार्वजनिक स्वास्थ्य, स्वच्छता और सड़क बुनियादी ढांचे जैसे क्षेत्रों में टिकाऊ सामुदायिक संपत्ति बनाने पर ध्यान केंद्रित करती हैं। प्रत्येक वर्ष, सांसदों को 2.5 करोड़ रुपये की दो किस्तों में 5 करोड़ रुपये मिलते हैं। उल्लेखनीय है कि MPLADS के तहत आवंटित धनराशि लैप्स नहीं होती है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "30",
                    section: "18",
                    question_en: "<p>30. Which two Indian cities have become the first to join the global list of accredited Wetland Cities under the Ramsar Convention on Wetlands in January 2025?</p>",
                    question_hi: "<p>30. जनवरी 2025 में वेटलैंड्स पर रामसर कन्वेंशन के तहत मान्यता प्राप्त वेटलैंड शहरों की वैश्विक सूची में शामिल होने वाले पहले दो भारतीय शहर कौन से हैं?</p>",
                    options_en: [
                        "<p>Jaipur and Bhopal</p>",
                        "<p>Indore and Udaipur</p>",
                        "<p>Mumbai and Pune</p>",
                        "<p>Kochi and Thiruvananthapuram</p>"
                    ],
                    options_hi: [
                        "<p>जयपुर और भोपाल</p>",
                        "<p>इंदौर और उदयपुर</p>",
                        "<p>मुंबई और पुणे</p>",
                        "<p>कोच्चि और तिरुवनंतपुरम</p>"
                    ],
                    solution_en: "<p>30.(b) <strong>Indore and Udaipur</strong> join the list of 31 Wetland Accredited Cities in the world. Wetland City Accreditation (WCA): It is a voluntary Accreditation system that provides an opportunity for cities that value their natural or human-made wetlands to gain international recognition and positive publicity for their efforts. It was approved at Uruguay in COP12 of Ramsar Convention (2015).</p>",
                    solution_hi: "<p>30.(b) <strong>इंदौर और उदयपुर</strong> दुनिया के 31 वेटलैंड मान्यता प्राप्त शहरों की सूची में शामिल हो गए हैं। वेटलैंड सिटी मान्यता (WCA): यह एक स्वैच्छिक मान्यता प्रणाली है जो उन शहरों को अवसर प्रदान करती है जो अपने प्राकृतिक या मानव निर्मित वेटलैंड्स को महत्व देते हैं ताकि उनके प्रयासों के लिए अंतरराष्ट्रीय मान्यता और सकारात्मक प्रचार प्राप्त हो सके। इसे रामसर कन्वेंशन (2015) के COP12 में उरुग्वे में अनुमोदित किया गया था।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "31",
                    section: "18",
                    question_en: "<p>31. In Pradhan Mantri Kisan Samman Nidhi Yojana, beneficiaries are provided with how much amount every year?</p>",
                    question_hi: "<p>31. प्रधानमंत्री किसान सम्मान निधि योजना में लाभार्थियों को हर वर्ष कितनी राशि प्रदान की जाती है?</p>",
                    options_en: [
                        "<p>₹6,000</p>",
                        "<p>₹4,000</p>",
                        "<p>₹5,000</p>",
                        "<p>₹3,000</p>"
                    ],
                    options_hi: [
                        "<p>₹6,000</p>",
                        "<p>₹4,000</p>",
                        "<p>₹5,000</p>",
                        "<p>₹3,000</p>"
                    ],
                    solution_en: "<p>31.(a) <strong>₹6,000</strong> - per year in three equal installments, every four months is transferred into the bank accounts of farmers&rsquo; families across the country through Direct Benefit Transfer (DBT) mode. Implemented by the Ministry of Agriculture and Farmers Welfare. Pradhan Mantri Kisan Samman Nidhi Yojana launched on 24th February, 2019.</p>",
                    solution_hi: "<p>31.(a) <strong>₹6,000</strong> - हर वर्ष तीन बराबर किस्तों में, हर चार महीने में प्रत्यक्ष लाभ अंतरण (DBT) मोड के माध्यम से देश भर के किसान परिवारों के बैंक खातों में धनराशि हस्तांतरित की जाती है। कृषि एवं किसान कल्याण मंत्रालय द्वारा लागू किया गया है। प्रधानमंत्री किसान सम्मान निधि योजना 24 फरवरी, 2019 को शुरू की गई थी।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "32",
                    section: "18",
                    question_en: "<p>32. Langvir Nritya is a folk dance from the state of ________</p>",
                    question_hi: "<p>32. लंगवीर नृत्य (Langvir Nritya)______राज्य का एक लोक नृत्य है।</p>",
                    options_en: [
                        "<p>Gujarat</p>",
                        "<p>Uttar Pradesh</p>",
                        "<p>Uttarakhand</p>",
                        "<p>Haryana</p>"
                    ],
                    options_hi: [
                        "<p>गुजरात</p>",
                        "<p>उत्तर प्रदेश</p>",
                        "<p>उत्तराखंड</p>",
                        "<p>हरियाणा</p>"
                    ],
                    solution_en: "<p>32.(c) <strong>Uttarakhand.</strong> Langvir Nritya is a dance form practiced by only men. The dancers use a long bamboo pole. States and their Folk dances : Gujarat - Garba and Dandiya Raas. Haryana - Phalgun dance, Luur dance, Daf dance, Gugga dance, and Khoria dance. Uttar Pradesh - Charkula, Rasiya, Nautanki.</p>",
                    solution_hi: "<p>32.(c) <strong>उत्तराखंड।</strong> लंगवीर नृत्य एक ऐसी नृत्य शैली है जिसे केवल पुरुषों द्वारा किया जाता है। इसमें नर्तक एक लंबे बांस के खंभे का उपयोग करते हैं। कुछ राज्य और उनके प्रमुख लोक नृत्य : गुजरात - गरबा और डांडिया रास। हरियाणा - फाल्गुन नृत्य, लूर नृत्य, डफ नृत्य, गुग्गा नृत्य और खोरिया नृत्य। उत्तर प्रदेश - चरकुला, रसिया, नौटंकी आदि।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "33",
                    section: "18",
                    question_en: "<p>33. Identify the sequence of implementation of the following taxes.<br>I.Land revenue<br>II.Sales tax<br>III.MODVAT<br>IV.Service tax</p>",
                    question_hi: "<p>33. निम्नलिखित करों को लागू करने के क्रम की पहचान कीजिए।<br>I.भूमि राजस्व<br>II.बिक्री कर<br>III.मोडवैट<br>IV.सेवा कर</p>",
                    options_en: [
                        "<p>III, II, IV, I</p>",
                        "<p>II, IV, I, III</p>",
                        "<p>IV, II, I, III</p>",
                        "<p>I, II, III, IV</p>"
                    ],
                    options_hi: [
                        "<p>III, II, IV, I</p>",
                        "<p>II, IV, I, III</p>",
                        "<p>IV, II, I, III</p>",
                        "<p>I, II, III, IV</p>"
                    ],
                    solution_en: "<p>33.(d) <strong>I, II, III, IV. </strong>India\'s tax system includes direct and indirect taxes. In 2017, the Goods and Services Tax (GST) was introduced as part of indirect taxes. Direct taxes are imposed on individuals and corporations, while indirect taxes are collected through the sale of goods and services. Key tax introductions in India include Income Tax, Gift Tax, Service Tax, Banking Transaction Tax, and GST.</p>",
                    solution_hi: "<p>33.(d) <strong>I, II, III, IV. </strong>भारत की कर प्रणाली में प्रत्यक्ष और अप्रत्यक्ष कर शामिल हैं। 2017 में, अप्रत्यक्ष करों के हिस्से के रूप में वस्तु एवं सेवा कर (GST) लागू किया गया था। प्रत्यक्ष कर व्यक्तियों और निगमों पर लगाए जाते हैं, जबकि अप्रत्यक्ष कर वस्तुओं एवं सेवाओं की बिक्री के माध्यम से एकत्र किए जाते हैं। भारत में लागू किये गये प्रमुख करों में आयकर, उपहार कर, सेवा कर, बैंकिंग लेनदेन कर और GST शामिल हैं।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "34",
                    section: "18",
                    question_en: "<p>34. Which state will host the Khelo India Youth Games (KIYG) and the Khelo India Para Games (KIPG) in April 2025?</p>",
                    question_hi: "<p>34. अप्रैल 2025 में खेलो इंडिया यूथ गेम्स (KIYG) और खेलो इंडिया पैरा गेम्स (KIPG) की मेजबानी कौन सा राज्य करेगा?</p>",
                    options_en: [
                        "<p>Uttar Pradesh</p>",
                        "<p>Bihar</p>",
                        "<p>Maharashtra</p>",
                        "<p>Madhya Pradesh</p>"
                    ],
                    options_hi: [
                        "<p>उत्तर प्रदेश</p>",
                        "<p>बिहार</p>",
                        "<p>महाराष्ट्र</p>",
                        "<p>मध्य प्रदेश</p>"
                    ],
                    solution_en: "<p>34 (b) <strong>Bihar</strong> will host the prestigious Khelo India Youth Games (KIYG) and the Khelo India Para Games (KIPG) in April 2025. This follows the successful organization of the Women&rsquo;s Asian Champions Trophy in Rajgir, further cementing Bihar&rsquo;s growing reputation in the sports arena.</p>",
                    solution_hi: "<p>34.(b) <strong>बिहार।बिहार</strong> अप्रैल 2025 में प्रतिष्ठित खेलो इंडिया यूथ गेम्स (KIYG) और खेलो इंडिया पैरा गेम्स (KIPG) की मेजबानी करेगा। यह महिला एशियाई चैंपियंस ट्रॉफी को सफलतापूर्वक आयोजित करने के बाद बिहार के खेल क्षेत्र में बढ़ती प्रतिष्ठा को मजबूत करता है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "35",
                    section: "18",
                    question_en: "<p>35. In the year of the coronation of Akbar, who among the following was designated as the Wakil of the Mughal kingdom with the title of &lsquo;Khan-i-Khanan&rsquo; ?</p>",
                    question_hi: "<p>35. अकबर के राज्याभिषेक के वर्ष में, निम्नलिखित में से किसे \'खान-ए-खाना (Khan-i-Khanan)\' की उपाधि के साथ मुगल साम्राज्य के वकील (Wakil) के रूप में पदनामित किया गया था?</p>",
                    options_en: [
                        "<p>Bairam Khan</p>",
                        "<p>Munim Khan</p>",
                        "<p>Hakim Mirza</p>",
                        "<p>Abdur Rahim</p>"
                    ],
                    options_hi: [
                        "<p>बैरम खाँ (Bairam Khan)</p>",
                        "<p>मुनीम खान (Munim Khan)</p>",
                        "<p>हकीम मिर्ज़ा (Hakim Mirza)</p>",
                        "<p>अब्दुर रहीम (Abdur Rahim)</p>"
                    ],
                    solution_en: "<p>35.(a) <strong>Bairam Khan</strong> was Akbar\'s trusted advisor, mentor and guardian after Humayun\'s death. He was also the chief of Humayun\'s army and led the Mughal army in the Second War of Panipat in 1556 (between the forces of the Mughal emperor Akbar and the Hindu king Hemu). He acted as Akbar\'s regent from 1556 to 1560.</p>",
                    solution_hi: "<p>35.(a)<strong> बैरम खाँ,</strong> हुमायूं की मृत्यु के बाद अकबर के विश्वसनीय सलाहकार, मार्गदर्शक और अभिभावक थे। वह हुमायूं की सेना के मुख्य सेनापति थे और उन्होंने 1556 में पानीपत के द्वितीय युद्ध (मुगल सम्राट अकबर और हिंदू राजा हेमू की सेनाओं के बीच) में मुगल सेना का नेतृत्व किया था। उन्होंने 1556 से 1560 तक अकबर के शासक के रूप में कार्य किया।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "36",
                    section: "18",
                    question_en: "<p>36. Match the following Sportsman in List I and Award in List II :<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743652252484.png\" alt=\"rId49\" width=\"432\" height=\"90\"></p>",
                    question_hi: "<p>36. निम्नलिखित सूची । के खिलाड़ियों का सूची ॥ के पुरस्कारों के साथ मिलान कीजिए।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743652252601.png\" alt=\"rId50\" width=\"373\" height=\"135\"></p>",
                    options_en: [
                        "<p>A-ii, B-i, C-iv, D-iii</p>",
                        "<p>A-iii, B-iv, C-i, D-ii</p>",
                        "<p>A-i, B-iii, C-ii, D-iv</p>",
                        "<p>A-iv, B-i, C-iii, D-ii</p>"
                    ],
                    options_hi: [
                        "<p>A-ii, B-i, C-iv, D-iii</p>",
                        "<p>A-iii, B-iv, C-i, D-ii</p>",
                        "<p>A-i, B-iii, C-ii, D-iv</p>",
                        "<p>A-iv, B-i, C-iii, D-ii</p>"
                    ],
                    solution_en: "<p>36.(a) <strong>A-ii, B-i, C-iv, D-iii</strong>. Devendra Jhajharia (Paralympic javelin thrower) : Awards - Major Dhyan Chand Khel Ratna (2017), Padma Shri (2012), Arjuna Award (2004). Sumit Antil (javelin thrower) : Award - Khel Ratna Award (2021). Neeraj Chopra : Awards - Arjuna Award (2018), Padma Shri (2022). Shikhar Dhawan is an Indian former cricketer.</p>",
                    solution_hi: "<p>36.(a)<strong> A-ii, B-i, C-iv, D-iii. </strong>देवेन्द्र झाझरिया (पैरालंपिक भाला फेंक खिलाड़ी) : पुरस्कार - मेजर ध्यानचंद खेल रत्न (2017), पद्म श्री (2012), अर्जुन पुरस्कार (2004)। सुमित अंतिल (भाला फेंक खिलाड़ी) : पुरस्कार - खेल रत्न पुरस्कार (2021)। नीरज चोपड़ा: पुरस्कार - अर्जुन पुरस्कार (2018), पद्म श्री (2022)। शिखर धवन एक भारतीय पूर्व क्रिकेटर हैं।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "37",
                    section: "18",
                    question_en: "<p>37. &lsquo;Ungalil Oruvan&rsquo; (One Among You) is the autobiography of which of the following chief ministers?</p>",
                    question_hi: "<p>37. \'उनगलिल ओरुवन\' (वन अमंग यू) निम्नलिखित में से किस मुख्यमंत्री की आत्मकथा है?</p>",
                    options_en: [
                        "<p>MK Stalin</p>",
                        "<p>K Chandrashekhar Rao</p>",
                        "<p>Basavaraj Bommai</p>",
                        "<p>Pinarayi Vijayan</p>"
                    ],
                    options_hi: [
                        "<p>एम.के. स्टालिन</p>",
                        "<p>के. चंद्रशेखर राव</p>",
                        "<p>बासवराज बोम्मई</p>",
                        "<p>पिनरई विजयन</p>"
                    ],
                    solution_en: "<p>37.(a) <strong>MK Stalin</strong>. Other Chief ministers&rsquo; books : Mamata Banerjee - &lsquo;My Unforgettable Memories&rsquo;, Yogi Adityanath - &lsquo;Hathyoga : Swaroop evam Sadhna&rsquo;, Arvind Kejriwal - &lsquo;Swaraj&rsquo;.</p>",
                    solution_hi: "<p>37.(a) <strong>एम.के. स्टालिन।</strong> अन्य मुख्यमंत्री और उनकी पुस्तकें: ममता बनर्जी - \'माई अनफॉरगटेबल मेमोरीज\', योगी आदित्यनाथ - \'हठयोग: स्वरूप एवं साधना\', अरविंद केजरीवाल - \'स्वराज\'।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "38",
                    section: "18",
                    question_en: "<p>38. In a metro station, it was observed that on an average 20 people per minute were using the escalator to reach at a height of 15 metres. Calculate the power of the escalator assuming the average mass of people to be 50 kg and acceleration due to gravity to be 10 m/s2.</p>",
                    question_hi: "<p>38. एक मेट्रो स्टेशन में देखा गया कि प्रति मिनट औसतन 20 लोग 15 मीटर की ऊँचाई तक पहुंचने के लिए चलती सीढ़ी (एस्केलेटर) का उपयोग कर रहे थे। यदि लोगों का औसत द्रव्यमान 50 kg और गुरुत्वीय त्वरण 10 m/s2 माना जाए तो एस्केलेटर की शक्ति (power) की गणना कीजिए।</p>",
                    options_en: [
                        "<p>1.25 kW</p>",
                        "<p>2.5 kW</p>",
                        "<p>15 kW</p>",
                        "<p>150 kW</p>"
                    ],
                    options_hi: [
                        "<p>1.25 kW</p>",
                        "<p>2.5 kW</p>",
                        "<p>15 kW</p>",
                        "<p>150 kW</p>"
                    ],
                    solution_en: "<p>38.(b) <strong>2.5 kW.</strong> To calculate the power of the escalator : P = <math display=\"inline\"><mfrac><mrow><mi>W</mi></mrow><mrow><mi>t</mi></mrow></mfrac></math> (W is the work done, and t is the time). Given, m = 50kg (mass of one person), g = 10m/s<sup>2</sup> (acceleration due to gravity), h =15 m (height), n = 20 people/min. Work done to lift one person is: W = mhg. Thus, the work done per minute is: 50 &times; 15 &times; 10 = 7500J. For 20 people per minute: 7500 &times; 20 = 150000J/min, 15000060 = 2500J/s = 2.5 kW. Thus, the power required is 2.5 kW.</p>",
                    solution_hi: "<p>38.(b) <strong>2.5 kW</strong>. एस्केलेटर की शक्ति की गणना करने के लिए: P = <math display=\"inline\"><mfrac><mrow><mi>W</mi></mrow><mrow><mi>t</mi></mrow></mfrac></math> (W किया गया कार्य है, और t समय है)। दिया गया है, m = 50kg (एक व्यक्ति का द्रव्यमान), g = 10m/s<sup>2</sup> (गुरुत्वाकर्षण के कारण त्वरण), h =15 m (ऊँचाई), n = 20 लोग/मिनट। एक व्यक्ति को उठाने के लिए किया गया कार्य है: W = mhg. इस प्रकार, प्रति मिनट किया गया कार्य है: 50 &times; 15 &times; 10 = 7500J. प्रति मिनट 20 लोगों के लिए: 7500 &times; 20 = 150000J/मिनट, 15000060 = 2500J/s = 2.5 kW. इस प्रकार, आवश्यक शक्ति 2.5 kW है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "39",
                    section: "18",
                    question_en: "<p>39. Who is the chairman of the State Planning Board?</p>",
                    question_hi: "<p>39. राज्य योजना बोर्ड (State Planning Board) का अध्यक्ष कौन होता है?</p>",
                    options_en: [
                        "<p>Advocate General</p>",
                        "<p>Governor</p>",
                        "<p>Chief Minister</p>",
                        "<p>Speaker</p>"
                    ],
                    options_hi: [
                        "<p>महाधिवक्ता</p>",
                        "<p>राज्यपाल</p>",
                        "<p>मुख्यमंत्री</p>",
                        "<p>विधानसभाध्यक्ष</p>"
                    ],
                    solution_en: "<p>39.(c) <strong>Chief Minister.</strong> A State Planning Board is a key advisory body responsible for drafting and monitoring state-level developmental policies and plans. It ensures effective resource allocation and alignment with national goals, focusing on sectors like poverty alleviation, education, and infrastructure. The board typically includes the Chief Minister as Chairperson, experts, and officials to guide economic and social development strategies. It collaborates with central bodies like NITI Aayog for effective planning and implementation. By evaluating ongoing programs, the board ensures sustainable growth and efficient policy execution.</p>",
                    solution_hi: "<p>39.(c) <strong>मुख्यमंत्री।</strong> राज्य योजना बोर्ड एक प्रमुख सलाहकार निकाय है जो राज्य-स्तरीय विकास नीतियों और योजनाओं का मसौदा तैयार करने और उनकी निगरानी करने के लिए जिम्मेदार है। यह गरीबी उन्मूलन, शिक्षा और बुनियादी ढांचे जैसे क्षेत्रों पर ध्यान केंद्रित करते हुए प्रभावी संसाधन आवंटन और राष्ट्रीय लक्ष्यों के साथ संरेखण सुनिश्चित करता है। बोर्ड में आमतौर पर मुख्यमंत्री अध्यक्ष के रूप में, विशेषज्ञ और अधिकारी शामिल होते हैं जो आर्थिक और सामाजिक विकास रणनीतियों का मार्गदर्शन करते हैं। यह प्रभावी योजना और कार्यान्वयन के लिए नीति आयोग जैसे केंद्रीय निकायों के साथ सहयोग करता है। क्रियान्वित कार्यक्रमों का मूल्यांकन करके, बोर्ड सतत विकास और कुशल नीति निष्पादन सुनिश्चित करता है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "40",
                    section: "18",
                    question_en: "<p>40. The Sarda River rises in the ________ glacier in the Nepal Himalayas.</p>",
                    question_hi: "<p>40. शारदा नदी (Sarda River) नेपाल हिमालय के ________ ग्लेशियर से निकलती है।</p>",
                    options_en: [
                        "<p>Milam</p>",
                        "<p>Bokhar Chu</p>",
                        "<p>Siachen</p>",
                        "<p>Machoi</p>"
                    ],
                    options_hi: [
                        "<p>मिलम (Milam)</p>",
                        "<p>बोखार-चू (Bokhar Chu)</p>",
                        "<p>सियाचिन (Siachen)</p>",
                        "<p>माचोई (Machoi)</p>"
                    ],
                    solution_en: "<p>40.(a) <strong>Milam.</strong> The Sharda River, a downstream tributary of the Kali (or Mahakali) River, originates in northern Uttarakhand, India, in the Great Himalayas, on the eastern slopes of the Nanda Devi massif at an elevation of 3,600 m (11,800 ft) in Pithoragarh district. It then flows along the India-Nepal border.</p>",
                    solution_hi: "<p>40.(a) <strong>मिलम</strong> (Milam)। शारदा नदी, काली (या महाकाली) नदी की एक सहायक नदी है, जिसका उद्गम भारत के उत्तरी उत्तराखंड में, विशाल हिमालय में, पिथौरागढ़ जिले में 3,600 मीटर (11,800 फीट) की ऊंचाई पर नंदा देवी पर्वतमाला की पूर्वी ढलानों पर होता है। इसके बाद यह नदी भारत-नेपाल सीमा पर प्रवाहित होती है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "41",
                    section: "18",
                    question_en: "<p>41. Under which of the following Acts, was Warren Hastings appointed as the Governor-General of Bengal?</p>",
                    question_hi: "<p>41. निम्नलिखित में से किस अधिनियम के तहत, वारेन हेस्टिंग्स (Warren Hastings) को बंगाल का गवर्नर-जनरल नियुक्त किया गया था?</p>",
                    options_en: [
                        "<p>The Regulating Act, 1773</p>",
                        "<p>The Government of India Act, 1858</p>",
                        "<p>The Government of India Act, 1909</p>",
                        "<p>The Government of India Act, 1935</p>"
                    ],
                    options_hi: [
                        "<p>रेग्युलेटिंग ऐक्ट, 1773</p>",
                        "<p>भारत सरकार अधिनियम, 1858</p>",
                        "<p>भारत सरकार अधिनियम, 1909</p>",
                        "<p>भारत सरकार अधिनियम, 1935</p>"
                    ],
                    solution_en: "<p>41.(a) <strong>The Regulating Act, 1773</strong> designated the Governor of Bengal as the Governor-General of Bengal and created an Executive Council of four members to assist him. This Act also placed the Governors of Bombay and Madras under the authority of the Governor-General of Bengal. Additionally, it stipulated the establishment of a Supreme Court in Calcutta in 1774. The Government of India Act 1858 - Transferred power from the East India Company to the British Crown. The Government of India Act 1909 - It introduced a separate electorate.</p>",
                    solution_hi: "<p>41.(a) <strong>रेग्युलेटिंग एक्ट, 1773 </strong>के तहत बंगाल के गवर्नर को बंगाल का गवर्नर-जनरल नियुक्त किया गया था और उनकी सहायता के लिए चार सदस्यों की एक कार्यकारी परिषद का गठन किया गया था। इस अधिनियम ने बॉम्बे और मद्रास के गवर्नरों को बंगाल के गवर्नर-जनरल के अधिकार क्षेत्र में रखा। इसके अलावा, इसमें 1774 में कलकत्ता में एक सर्वोच्च न्यायालय की स्थापना का प्रावधान किया गया। भारत शासन अधिनियम 1858 ईस्ट इंडिया कंपनी से ब्रिटिश क्राउन को सत्ता का हस्तांतरण करने के लिए महत्वपूर्ण था। इसके बाद, भारत शासन अधिनियम 1909 ने पृथक निर्वाचक मंडल की शुरुआत की, जो भारतीय राजनीतिक व्यवस्था में एक महत्वपूर्ण बदलाव था।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "42",
                    section: "18",
                    question_en: "<p>42. The horizontal distance between two adjacent crests or troughs is known as the __________.</p>",
                    question_hi: "<p>42. दो निकटवर्ती शृंगों या गर्त के बीच की क्षैतिज दूरी को __________ के रूप में जाना जाता है।</p>",
                    options_en: [
                        "<p>wave speed</p>",
                        "<p>wave frequency</p>",
                        "<p>wave period</p>",
                        "<p>wave length</p>"
                    ],
                    options_hi: [
                        "<p>तरंग-चाल</p>",
                        "<p>तरंग-आवृत्ति</p>",
                        "<p>तरंगावधि</p>",
                        "<p>तरंग-दैर्घ्य</p>"
                    ],
                    solution_en: "<p>42.(d) <strong>wave length.</strong> The wave speed (v) is defined as the distance traveled by a wave per unit time. The frequency of a wave is the total number of waves produced in a second. Wave period is the time it takes for one complete cycle of the wave to pass a certain point. Waves transport energy and the pattern of disturbance has information that propagates from one point to another.</p>",
                    solution_hi: "<p>42.(d) <strong>तरंग-दैर्घ्य।</strong> तरंग गति (v) को प्रति इकाई समय में तरंग द्वारा तय की गई दूरी के रूप में परिभाषित किया गया है। एक तरंग की आवृत्ति एक सेकंड में उत्पन्न तरंगों की कुल संख्या है। तरंग आवृत्ति वह समय है जो तरंग के एक पूर्ण चक्र को एक निश्चित बिंदु से गुजरने में लगता है। तरंगें ऊर्जा का परिवहन करती हैं और विक्षोभ के पैटर्न में जानकारी होती है जो एक बिंदु से दूसरे बिंदु तक फैलती है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "43",
                    section: "18",
                    question_en: "<p>43. The first Indian Olympic Games was hosted by which of the following cities?</p>",
                    question_hi: "<p>43. पहले भारतीय ओलंपिक खेल किस शहर में आयोजित किए गए थे?</p>",
                    options_en: [
                        "<p>Lahore</p>",
                        "<p>Madras</p>",
                        "<p>Bombay</p>",
                        "<p>Allahabad</p>"
                    ],
                    options_hi: [
                        "<p>लाहौर</p>",
                        "<p>मद्रास</p>",
                        "<p>बॉम्बे (बम्&zwj;बई)</p>",
                        "<p>इलाहाबाद</p>"
                    ],
                    solution_en: "<p>43.(a) <strong>Lahore.</strong> Dr. A. G. Noehren and Harry Crowe Buck launched the first Indian Olympic Games in Lahore in 1924 to engage youth in sports. India first participated in the Olympics in 1900, and the first modern Olympics were in Athens in 1896. The last hockey gold came in 1980. Wrestler K.D. Jadhav won India\'s first individual medal in 1952, and Milkha Singh was the first Indian to reach the Olympic finals in athletics.</p>",
                    solution_hi: "<p>43.(a) <strong>लाहौर।</strong> डॉ. ए. जी. नोहरन और हैरी क्रो बक ने युवाओं को खेलों में शामिल करने के लिए 1924 में लाहौर में प्रथम भारतीय ओलंपिक खेलों की शुरुआत की थी। भारत ने पहली बार 1900 में ओलंपिक में भाग लिया था और पहला आधुनिक ओलंपिक 1896 में एथेंस में हुआ था। आखिरी हॉकी गोल्ड 1980 में आया था। पहलवान के.डी. जाधव ने 1952 में भारत का पहला व्यक्तिगत पदक जीता और मिल्खा सिंह एथलेटिक्स में ओलंपिक फाइनल में पहुंचने वाले पहले भारतीय थे।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "44",
                    section: "18",
                    question_en: "<p>44. The earthquakes that are generated due to the explosion of chemical or nuclear devices are known as :</p>",
                    question_hi: "<p>44. रासायनिक या परमाणु यंत्रों के विस्फोट के कारण उत्पन्न होने वाला भूकंप क्या कहलाता है?</p>",
                    options_en: [
                        "<p>Collapse earthquakes</p>",
                        "<p>Explosion earthquakes</p>",
                        "<p>Tectonic earthquakes</p>",
                        "<p>Reservoir-induced earthquakes</p>"
                    ],
                    options_hi: [
                        "<p>निपात भूकंप (Collapse earthquakes)</p>",
                        "<p>विस्फोट भूकंप (Explosion earthquakes)</p>",
                        "<p>विवर्तननिक भूकंप (Tectonic earthquakes)</p>",
                        "<p>बांध-जनित भूकंप (Reservoir-induced earthquakes)</p>"
                    ],
                    solution_en: "<p>44.(b) <strong>Explosion earthquakes</strong>. An earthquake in simple words is the shaking of the earth. Tectonic earthquakes are produced by sudden movement along faults and plate boundaries. Reservoir-induced earthquakes are earthquakes caused by the physical processes that occur when large reservoirs are filled with water. Collapse earthquakes are small earthquakes in underground caverns and mines that are caused by seismic waves produced from the explosion of rock on the surface.</p>",
                    solution_hi: "<p>44.(b)<strong> विस्फोट भूकंप।</strong> भूकंप को सरल शब्दों में पृथ्वी का कंपन कहा जा सकता है। टेक्टोनिक भूकंप, भ्रंशों और प्लेट सीमाओं के साथ अचानक होने वाली हलचल से उत्पन्न होते हैं। जलाशय-प्रेरित भूकंप वे भूकंप हैं जो बड़े जलाशयों को पानी से भरने के दौरान होने वाली भौतिक प्रक्रियाओं के कारण होते हैं। निपात भूकंप भूमिगत गुफाओं और खदानों में छोटे भूकंप होते हैं जो सतह पर चट्टान के विस्फोट से उत्पन्न भूकंपीय तरंगों के कारण होते हैं।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "45",
                    section: "18",
                    question_en: "<p>45. Which gas is released when you combine vinegar and baking soda in a balloon, causing it to inflate?</p>",
                    question_hi: "<p>45. गुब्बारे में सिरका (vinegar) और बेकिंग सोडा (baking soda) मिलाने पर कौन-सी गैस निकलती है, जिससे वह फूल जाता है?</p>",
                    options_en: [
                        "<p>Carbon dioxide</p>",
                        "<p>Hydrogen</p>",
                        "<p>Oxygen</p>",
                        "<p>Nitrogen</p>"
                    ],
                    options_hi: [
                        "<p>कार्बन डाईऑक्साइड</p>",
                        "<p>हाइड्रोजन</p>",
                        "<p>ऑक्सीजन</p>",
                        "<p>नाइट्रोजन</p>"
                    ],
                    solution_en: "<p>45.(a) <strong>Carbon dioxide. </strong>When you combine vinegar (acetic acid) and baking soda (sodium bicarbonate), they react to produce carbon dioxide (CO<sub>2</sub>) gas. The reaction is as follows: CH<sub>3</sub>​COOH + NaHCO<sub>3</sub> ​&rarr; CH<sub>3</sub>​COONa + H<sub>2</sub>​O + CO<sub>2</sub>​. When you add the baking soda to the vinegar it causes a reaction. That reaction releases a gas called carbon dioxide (CO<sub>2</sub>) because the balloon forms a seal around the bottle, the gas produced cannot escape, so it fills up the balloon.</p>",
                    solution_hi: "<p>45.(a) <strong>कार्बन डाइऑक्साइड।</strong> जब आप सिरका (एसिटिक अम्ल) और बेकिंग सोडा (सोडियम बाइकार्बोनेट) को मिलाते हैं, तो वे अभिक्रिया करते हैं, और कार्बन डाइऑक्साइड (CO<sub>2</sub>) गैस उत्पन्न करते है। अभिक्रिया इस प्रकार है : CH<sub>3</sub>​COOH + NaHCO<sub>3</sub> ​&rarr; CH<sub>3</sub>​COONa + H<sub>2</sub>​O + CO<sub>2</sub>​. जब आप बेकिंग सोडा को सिरके में मिलाते हैं तब यह अभिक्रिया करता है। उस अभिक्रिया से कार्बन डाइऑक्साइड (CO<sub>2</sub>) नामक गैस निकलती है और उत्पादित गैस बाहर नहीं निकल पाती है क्योंकि गुब्बारा बोतल के चारों ओर एक सील बनाता है, इसलिए यह गुब्बारे को भर देती है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "46",
                    section: "18",
                    question_en: "<p>46. In Equestrian sports, ______ occurs when a horse or rider lowers an element of a jump that establishes the height of an obstacle.</p>",
                    question_hi: "<p>46. घुड़सवारी (Equestrian) के खेल में, ______ तब होता है जब एक घोड़ा या सवार छलांग के उस अवयव (element) को कम नीचे कर देता (lowers) है जो बाधा की ऊंचाई तय करता है।</p>",
                    options_en: [
                        "<p>knockdown</p>",
                        "<p>canter</p>",
                        "<p>clean round</p>",
                        "<p>curb</p>"
                    ],
                    options_hi: [
                        "<p>नॉकडाऊन (knockdown)</p>",
                        "<p>कांटर (canter)</p>",
                        "<p>क्लीन राउंड (clean round)</p>",
                        "<p>कुर्ब (curb)</p>"
                    ],
                    solution_en: "<p>46.(a) <strong>knockdown.</strong> Penalties: A knockdown usually results in 4 penalty points. Types of Knockdowns: Knockdowns can occur due to various reasons, including the horse\'s mistake, rider error, or a combination of both. Impact on Score: Accumulating penalty points can significantly affect a rider\'s overall score and ranking in the competition.</p>",
                    solution_hi: "<p>46.(a) <strong>नॉकडाऊन</strong> (knockdown)। दंड: नॉकडाउन के परिणामस्वरूप आमतौर पर 4 पेनल्टी पॉइंट मिलते हैं। नॉकडाउन के प्रकार: नॉकडाउन कई कारणों से हो सकते हैं, जिसमें घोड़े की गलती, सवार की गलती या दोनों का संयोजन शामिल है। स्कोर पर प्रभाव: पेनल्टी अंक एकत्रित होने से प्रतियोगिता में सवार के सम्पूर्ण स्कोर और रैंकिंग पर महत्वपूर्ण प्रभाव पड़ सकता है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "47",
                    section: "18",
                    question_en: "<p>47. In 1928, who among the following wrote the Congress Party&rsquo;s Nehru Report, a future constitution for independent India, based on the granting of dominion status?</p>",
                    question_hi: "<p>47. 1928 में, निम्नलिखित में से किसने कांग्रेस पार्टी की नेहरू रिपोर्ट, स्वतंत्र भारत के लिए एक भावी संविधान (a future constitution for independent India) लिखी, जो स्वतंत्र उपनिवेश (dominion status) का दर्जा देने पर आधारित थी?</p>",
                    options_en: [
                        "<p>Motilal Nehru</p>",
                        "<p>Jawaharlal Nehru</p>",
                        "<p>Subhash Chandra Bose</p>",
                        "<p>Mahatma Gandhi</p>"
                    ],
                    options_hi: [
                        "<p>मोतीलाल नेहरू</p>",
                        "<p>जवाहरलाल नेहरू</p>",
                        "<p>सुभाष चंद्र बोस</p>",
                        "<p>महात्मा गांधी</p>"
                    ],
                    solution_en: "<p>47.(a) <strong>Motilal Nehru.</strong> Recommendations of the Nehru Report: Dominion status, Rejection of separate electorates, Linguistic provinces, Nineteen fundamental rights including equal rights for women, Full protection of the cultural and religious interests of Muslims, Complete dissociation of religion from the state. Other members of the committee were Tej Bahadur Sapru, Ali Imam, M.S. Aney, Mangal Singh, Sohaib Qureshi, G.R. Pradhan and Subhash Chandra Bose.</p>",
                    solution_hi: "<p>47.(a) <strong>मोतीलाल नेहरू।</strong> नेहरू रिपोर्ट की सिफ़ारिशें: स्वतंत्र उपनिवेश, पृथक निर्वाचन क्षेत्रों की अस्वीकृति, भाषाई प्रांत, महिलाओं के लिए समान अधिकार सहित उन्नीस मौलिक अधिकार, मुसलमानों के सांस्कृतिक और धार्मिक हितों की पूर्ण सुरक्षा, राज्य से धर्म का पूर्ण पृथक्करण। समिति के अन्य सदस्य तेज बहादुर सप्रू, अली इमाम, एम.ए.स अने, मंगल सिंह, सोहेब कुरैशी, जी.आर. प्रधान और सुभाष चंद्र बोस थे।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "48",
                    section: "18",
                    question_en: "<p>48. Select the imbalanced chemical equation from the following.</p>",
                    question_hi: "<p>48. निम्नलिखित विकल्पों में से असंतुलित रासायनिक समीकरण का चयन कीजिए|</p>",
                    options_en: [
                        "<p>10 KClO<sub>3</sub> + 3 P<sub>4</sub> &rarr; 3 P<sub>4</sub>O<sub>10</sub> + 10 KCl</p>",
                        "<p>6 HClO<sub>4</sub> + P<sub>4</sub>O<sub>10</sub> &rarr; 4 H<sub>3</sub>PO<sub>4</sub> + 6 Cl<sub>2</sub>O<sub>7</sub></p>",
                        "<p>3 CaCl<sub>2</sub> + 2 Na<sub>3</sub>PO<sub>4</sub> &rarr; Ca<sub>3</sub> (PO<sub>4</sub>)<sub>2</sub> + 6 NaCl</p>",
                        "<p>3 Hg(OH)<sub>2</sub> + 2 H<sub>3</sub>PO<sub>4</sub> &rarr; Hg<sub>3</sub> (PO<sub>4</sub> )<sub>2</sub> + 6 H<sub>2</sub>O</p>"
                    ],
                    options_hi: [
                        "<p>10 KClO<sub>3</sub> + 3 P<sub>4</sub> &rarr; 3 P<sub>4</sub>O<sub>10</sub> + 10 KCl</p>",
                        "<p>6 HClO<sub>4</sub> + P<sub>4</sub>O<sub>10</sub> &rarr; 4 H<sub>3</sub>PO<sub>4</sub> + 6 Cl<sub>2</sub>O<sub>7</sub></p>",
                        "<p>3 CaCl<sub>2</sub> + 2 Na<sub>3</sub>PO<sub>4</sub> &rarr; Ca<sub>3</sub> (PO<sub>4</sub>)<sub>2</sub> + 6 NaCl</p>",
                        "<p>3 Hg(OH)<sub>2</sub> + 2 H<sub>3</sub>PO<sub>4</sub> &rarr; Hg<sub>3</sub> (PO<sub>4</sub>)<sub>2</sub> + 6 H<sub>2</sub>O</p>"
                    ],
                    solution_en: "<p>48.(b) <strong>6 HClO<sub>4</sub> + P<sub>4</sub>O<sub>10</sub> &rarr; 4H<sub>3</sub>PO<sub>4</sub> + 6 Cl<sub>2</sub>O<sub>7</sub>.</strong> A chemical equation is balanced when the number of atoms of each element is equal on both sides, achieved by adjusting coefficients. This ensures the law of conservation of mass, which states that atoms are rearranged to form products, but the total mass remains constant in a chemical reaction.</p>",
                    solution_hi: "<p>48.(b) <strong>6 HClO<sub>4</sub> + P<sub>4</sub>O<sub>10</sub> &rarr; 4H<sub>3</sub>PO<sub>4</sub> + 6 Cl<sub>2</sub>O<sub>7</sub>.</strong> रासायनिक समीकरण तब संतुलित होता है जब प्रत्येक तत्व के परमाणुओं की संख्या दोनों तरफ समान होती है, जिसे गुणांकों को समायोजित करके प्राप्त किया जाता है। यह द्रव्यमान के संरक्षण के नियम को सुनिश्चित करता है, जो बताता है कि उत्पाद बनाने के लिए परमाणुओं को पुनर्व्यवस्थित किया जाता है, लेकिन रासायनिक अभिक्रिया में कुल द्रव्यमान स्थिर रहता है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "49",
                    section: "18",
                    question_en: "<p>49. The Universal Adult Franchise in Indian Constitution is applicable to Indian Citizens from the age of _______________.</p>",
                    question_hi: "<p>49. भारतीय संविधान में सार्वभौमिक वयस्क मताधिकार ________ की आयु से भारतीय नागरिकों पर लागू होता है।</p>",
                    options_en: [
                        "<p>18 years</p>",
                        "<p>22 years</p>",
                        "<p>21 years</p>",
                        "<p>17 years</p>"
                    ],
                    options_hi: [
                        "<p>18 वर्ष</p>",
                        "<p>22 वर्ष</p>",
                        "<p>21 वर्ष</p>",
                        "<p>17 वर्ष</p>"
                    ],
                    solution_en: "<p>49.(a) <strong>18 years. </strong>Universal adult franchise allows all adults to vote. Article 325 of the Constitution ensures universal suffrage, prohibiting exclusion from electoral rolls based on religion, race, caste, or sex. Under this principle, all citizens aged 18 and above, regardless of social or economic background, have the right to vote. The 61st Amendment Act of 1988 lowered the voting age from 21 to 18, receiving presidential assent on 28th March 1989 from President R. Venkataraman.</p>",
                    solution_hi: "<p>49.(a) <strong>18 वर्ष।</strong> सार्वभौमिक वयस्क मताधिकार सभी वयस्कों को मतदान करने की अनुमति देता है। संविधान के अनुच्छेद 325 सार्वभौमिक मताधिकार सुनिश्चित करता है, धर्म, नस्ल, जाति या लिंग के आधार पर मतदाता सूची से बहिष्कार को रोकता है। इस सिद्धांत के तहत, 18 वर्ष या उससे अधिक आयु के सभी नागरिकों को, चाहे उनकी सामाजिक या आर्थिक पृष्ठभूमि कुछ भी हो, मतदान का अधिकार है। 1988 के 61वें संशोधन अधिनियम ने मतदान की आयु 21 वर्ष से घटाकर 18 वर्ष कर दी, जिसे 28 मार्च 1989 को राष्ट्रपति आर. वेंकटरमण से स्वीकृति मिली।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "50",
                    section: "17",
                    question_en: "<p>50. Digestion of food is an important function of the animal body. In animals like lions, cows, humans, etc., the process involves use of various organs starting from the mouth and ending with the anus. The longest part of this canal is known as the ________.</p>",
                    question_hi: "<p>50. खाद्य पदार्थों का पाचन, जन्तु शरीर का एक महत्वपूर्ण कार्य होता है। शेर, गाय, मनुष्य आदि जंतुओं में, मुख से शुरू होकर गुदा (anus) तक विभिन्न अंगों का उपयोग इस प्रक्रिया में शामिल होता है। इस मार्ग के दीर्घतम भाग को ________ कहा जाता है।</p>",
                    options_en: [
                        "<p>stomach</p>",
                        "<p>large intestine</p>",
                        "<p>oesophagus</p>",
                        "<p>small intestine</p>"
                    ],
                    options_hi: [
                        "<p>आमाशय (stomach)</p>",
                        "<p>बड़ी आंत (large intestine)</p>",
                        "<p>ग्रसिका (oesophagus)</p>",
                        "<p>छोटी आंत (small intestine)</p>"
                    ],
                    solution_en: "<p>50.(d) <strong>Small intestine.</strong> It is a 20-foot tube in the digestive system, breaks down food and absorbs nutrients. It has three parts: the duodenum, jejunum, and ileum, and helps digest food from the stomach while absorbing vitamins, minerals, carbs, fats, proteins, and water. The large intestine, about 5 feet long, is wider but shorter than the small intestine. The esophagus in adults is typically 10 - 13 inches long and about &frac34; inch in diameter at its narrowest point.</p>",
                    solution_hi: "<p>50.(d) <strong>छोटी आंत।</strong> यह पाचन तंत्र में 20 फुट लंबी नलिका होती है, जो खाद्य पदार्थ को तोड़ती है और पोषक तत्वों को अवशोषित करती है। इसके तीन भाग होते हैं: डुओडेनम, जेजुनम ​​और इलियम, और यह आमाशय से भोजन को पचाने में मदद करता है जबकि विटामिन, खनिज, कार्बोहाइड्रेट, वसा, प्रोटीन और पानी को अवशोषित करता है। बड़ी आंत, लगभग 5 फीट लंबी, छोटी आंत से चौड़ी लेकिन छोटी होती है। वयस्कों में ग्रासनली आमतौर पर 10 - 13 इंच लंबी होती है और इसके सबसे संकीर्ण बिंदु पर लगभग &frac34; इंच व्यास की होती है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "51",
                    section: "17",
                    question_en: "51.   The value of 72 ÷ 8 − 210 ÷ 15 − 5 + 2 × 5 is: ",
                    question_hi: "51. 72 ÷ 8 − 210 ÷ 15 − 5 + 2 × 5 का मान ज्ञात कीजिए।",
                    options_en: [
                        " 1 ",
                        " 3",
                        " 2 ",
                        " 0"
                    ],
                    options_hi: [
                        " 1 ",
                        " 3",
                        " 2 ",
                        " 0"
                    ],
                    solution_en: "51.(d)<br />72 ÷ 8 − 210 ÷ 15 − 5 + 2 × 5 <br />9 − 14 − 5 +10 = 0",
                    solution_hi: "51.(d)<br />72 ÷ 8 − 210 ÷ 15 − 5 + 2 × 5 <br />9 − 14 − 5 +10 = 0",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "52",
                    section: "17",
                    question_en: "<p>52. After working for 8 days, Rakhi finds that only 10 percent of the work is completed. She employs Poonam who is 20 percent more efficient than Rakhi. How many more days will they together take to complete the remaining work?</p>",
                    question_hi: "<p>52. 8 दिनों तक काम करने के बाद, राखी को पता चलता है कि अभी तक केवल 10 प्रतिशत काम पूरा हुआ है। वह पूनम को काम पर रखती है, जो राखी से 20 प्रतिशत अधिक कुशल है। शेष काम को पूरा करने में उन्हें कितने दिन और लगेंगे?</p>",
                    options_en: [
                        "<p><math display=\"inline\"><mfrac><mrow><mn>360</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> days</p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>360</mn></mrow><mrow><mn>11</mn></mrow></mfrac></math> days</p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>340</mn></mrow><mrow><mn>11</mn></mrow></mfrac></math> days</p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>340</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> days</p>"
                    ],
                    options_hi: [
                        "<p><math display=\"inline\"><mfrac><mrow><mn>360</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> दिन</p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>360</mn></mrow><mrow><mn>11</mn></mrow></mfrac></math> दिन</p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>340</mn></mrow><mrow><mn>11</mn></mrow></mfrac></math> दिन</p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>340</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> दिन</p>"
                    ],
                    solution_en: "<p>52.(b)<br>Time taken by Rakhi to complete the whole work = 8 &times; 10 = 80 days<br>Ratio of efficiency of Poonam and Rakhi= 6 : 5<br>Total work = 5 &times; 80 = 400 unit<br>Remaining work = 400 &times; 90% = 360 unit<br>Time taken by both to complete the remaining work = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>360</mn><mrow><mn>6</mn><mo>+</mo><mn>5</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>360</mn><mn>11</mn></mfrac></math>days</p>",
                    solution_hi: "<p>52.(b)<br>राखी द्वारा पूरा कार्य पूरा करने में लिया गया समय = 8 &times; 10 = 80 दिन<br>पूनम और राखी की दक्षता का अनुपात = 6 : 5<br>कुल कार्य = 5 &times; 80 = 400 इकाई<br>शेष कार्य = 400 &times; 90% = 360 इकाई<br>शेष कार्य को पूरा करने में दोनों द्वारा लिया गया समय = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>360</mn><mrow><mn>6</mn><mo>+</mo><mn>5</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>360</mn><mn>11</mn></mfrac></math>दिन</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "53",
                    section: "17",
                    question_en: "<p>53. Which of the following is NOT a correct formula?</p>",
                    question_hi: "<p>53. निम्न में से कौन-सा एक सही सूत्र नहीं है?</p>",
                    options_en: [
                        "<p>Cos<sup>2</sup>A Cot<sup>2</sup>A = Cot<sup>2</sup>A - Cos<sup>2</sup>A</p>",
                        "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mo>&#160;</mo><mo>+</mo><msup><mrow><mo>&#160;</mo><mi>T</mi><mi>a</mi><mi>n</mi></mrow><mn>2</mn></msup><mi>A</mi></mrow><mrow><msup><mrow><mi>T</mi><mi>a</mi><mi>n</mi></mrow><mn>2</mn></msup><mi>A</mi></mrow></mfrac></math> = sin<sup>2</sup>A</p>",
                        "<p>SecASinA = TanA</p>",
                        "<p>Cosec<sup>2</sup>A - Cot<sup>2</sup>A=1</p>"
                    ],
                    options_hi: [
                        "<p>Cos<sup>2</sup>A Cot<sup>2</sup>A = Cot<sup>2</sup>A - Cos<sup>2</sup>A</p>",
                        "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mo>&#160;</mo><mo>+</mo><msup><mrow><mo>&#160;</mo><mi>T</mi><mi>a</mi><mi>n</mi></mrow><mn>2</mn></msup><mi>A</mi></mrow><mrow><msup><mrow><mi>T</mi><mi>a</mi><mi>n</mi></mrow><mn>2</mn></msup><mi>A</mi></mrow></mfrac></math> = sin<sup>2</sup>A</p>",
                        "<p>SecASinA = TanA</p>",
                        "<p>Cosec<sup>2</sup>A - Cot<sup>2</sup>A = 1</p>"
                    ],
                    solution_en: "<p>53.(b)<br>Let A = <math display=\"inline\"><msup><mrow><mn>45</mn></mrow><mrow><mo>&#8728;</mo></mrow></msup></math><br>(a) <br>LHS = Cos<sup>2</sup>A Cot<sup>2</sup>A<br>= Cos<sup>2</sup>45 Cot<sup>2</sup>45 = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math><br>RHS = Cot<sup>2</sup>A - Cos<sup>2</sup>A&nbsp;<br>= Cot<sup>2</sup>45 - Cos<sup>2</sup>45&nbsp; = 1 - <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math><br>(b) <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mo>+</mo><msup><mrow><mi>T</mi><mi>a</mi><mi>n</mi></mrow><mn>2</mn></msup><mi>A</mi></mrow><mrow><msup><mrow><mi>T</mi><mi>a</mi><mi>n</mi></mrow><mn>2</mn></msup><mi>A</mi></mrow></mfrac></math> = sin<sup>2</sup>A<br>LHS = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mo>+</mo><msup><mrow><mi>T</mi><mi>a</mi><mi>n</mi></mrow><mn>2</mn></msup><mi>A</mi></mrow><mrow><msup><mrow><mi>T</mi><mi>a</mi><mi>n</mi></mrow><mn>2</mn></msup><mi>A</mi></mrow></mfrac></math><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mo>+</mo><mn>1</mn></mrow><mn>1</mn></mfrac></math> = 2<br>RHS = sin<sup>2</sup>A = sin<sup>2</sup>45 = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math><br>Hence LHS &ne; RHS<br><math display=\"inline\"><mo>&#8658;</mo></math>&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mo>+</mo><mi>T</mi><mi>a</mi><msup><mi>n</mi><mn>2</mn></msup><mi>A</mi></mrow><mrow><mi>T</mi><mi>a</mi><msup><mi>n</mi><mn>2</mn></msup><mi>A</mi></mrow></mfrac></math> = sin<sup>2</sup>A (not the correct formula).</p>",
                    solution_hi: "<p>53.(b)<br>माना A = <math display=\"inline\"><msup><mrow><mn>45</mn></mrow><mrow><mo>&#8728;</mo></mrow></msup></math><br>(a) <br>LHS = Cos<sup>2</sup>A Cot<sup>2</sup>A<br>= Cos<sup>2</sup>45 Cot<sup>2</sup>45 = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math><br>RHS = Cot<sup>2</sup>A - Cos<sup>2</sup>A&nbsp;<br>= Cot<sup>2</sup>45 - Cos<sup>2</sup>45 = 1 - <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math><br>(b) <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mo>+</mo><msup><mrow><mi>T</mi><mi>a</mi><mi>n</mi></mrow><mn>2</mn></msup><mi>A</mi></mrow><mrow><msup><mrow><mi>T</mi><mi>a</mi><mi>n</mi></mrow><mn>2</mn></msup><mi>A</mi></mrow></mfrac></math> = sin<sup>2</sup>A<br>LHS = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mo>+</mo><msup><mrow><mi>T</mi><mi>a</mi><mi>n</mi></mrow><mn>2</mn></msup><mi>A</mi></mrow><mrow><msup><mrow><mi>T</mi><mi>a</mi><mi>n</mi></mrow><mn>2</mn></msup><mi>A</mi></mrow></mfrac></math><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mo>+</mo><mn>1</mn></mrow><mn>1</mn></mfrac></math> = 2<br>RHS = sin<sup>2</sup>A = sin<sup>2</sup>45 = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math><br>अतः LHS &ne; RHS<br><math display=\"inline\"><mo>&#8658;</mo></math>&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mo>+</mo><mi>T</mi><mi>a</mi><msup><mi>n</mi><mn>2</mn></msup><mi>A</mi></mrow><mrow><mi>T</mi><mi>a</mi><msup><mi>n</mi><mn>2</mn></msup><mi>A</mi></mrow></mfrac></math> = sin<sup>2</sup>A (सही सूत्र नहीं है)।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "54",
                    section: "17",
                    question_en: "54. Which of the following numbers is not divisible by 11 ?",
                    question_hi: "54. निम्नलिखित में से कौन-सी संख्या 11 से विभाज्य नहीं है ?",
                    options_en: [
                        " 2763",
                        " 2651 ",
                        " 2728 ",
                        " 2563"
                    ],
                    options_hi: [
                        " 2763",
                        " 2651 ",
                        " 2728 ",
                        " 2563"
                    ],
                    solution_en: "54.(a) After checking all options one by one, only option (a) satisfied.<br /> “The given number can only be completely divided by 11 if the difference of the sum of digits at odd position and sum of digits at even position in a number is 0 or 11.”",
                    solution_hi: "54.(a) सभी विकल्पों को एक-एक करके जांचने के बाद, केवल विकल्प (a) संतुष्ट है।<br /> \"दी गई संख्या को केवल 11 से पूर्ण रूप से विभाजित किया जा सकता है यदि किसी संख्या में विषम स्थान पर अंकों के योग और सम स्थान पर अंकों के योग का अंतर 0 या 11 है।\"",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "55",
                    section: "17",
                    question_en: "<p>55. Rajesh is playing with a football having diameter of 28 cm. He wants to get a photo of his favourite player painted on the entire ball. The painter charges ₹2/cm&sup2;. What will be the cost (in ₹) of painting the ball (take &pi; =<math display=\"inline\"><mfrac><mrow><mn>22</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math>)?</p>",
                    question_hi: "<p>55. राजेश एक फुटबॉल से खेल रहा है जिसका व्यास 28 cm है। वह इस पूरी बॉल पर अपने पसंदीदा खिलाड़ी की तस्वीर पेंट कराना चाहता है। पेंटर ₹2/cm&sup2; का शुल्क लेता है। बॉल को पेंट करने की लागत (₹ में) क्या होगी? (&pi; =<math display=\"inline\"><mfrac><mrow><mn>22</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> लें)</p>",
                    options_en: [
                        "<p>4,928</p>",
                        "<p>2,414</p>",
                        "<p>4,828</p>",
                        "<p>2,464</p>"
                    ],
                    options_hi: [
                        "<p>4,928</p>",
                        "<p>2,414</p>",
                        "<p>4,828</p>",
                        "<p>2,464</p>"
                    ],
                    solution_en: "<p>55.(a) <strong>Given</strong> : diameter of sphere = 28 cm then radius = 14 cm<br>Surface area of sphere = 4<math display=\"inline\"><mi>&#960;</mi><msup><mrow><mi>r</mi></mrow><mrow><mn>2</mn></mrow></msup></math><br><math display=\"inline\"><mo>&#8658;</mo></math> Surface area of sphere = 4 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mn>7</mn></mfrac></math>&times; 14 &times; 14<br>= 2464 <math display=\"inline\"><msup><mrow><mi>c</mi><mi>m</mi></mrow><mrow><mn>2</mn></mrow></msup></math><br>Now,<br>Cost of painting the ball = 2464 &times; ₹2 = ₹4928</p>",
                    solution_hi: "<p>55.(a) दिया गया है : गोले का व्यास = 28 cm तो त्रिज्या = 14 cm<br>गोले का पृष्ठीय क्षेत्रफल = 4<math display=\"inline\"><mi>&#960;</mi><msup><mrow><mi>r</mi></mrow><mrow><mn>2</mn></mrow></msup></math><br>गोले का पृष्ठीय क्षेत्रफल = 4 &times; <math display=\"inline\"><mfrac><mrow><mn>22</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> &times; 14 &times; 14<br>= 2464 <math display=\"inline\"><msup><mrow><mi>c</mi><mi>m</mi></mrow><mrow><mn>2</mn></mrow></msup></math><br>अब,<br>गेंद को पेंट करने की लागत = 2464 &times; ₹2 = ₹4928</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "56",
                    section: "17",
                    question_en: "<p>56. In a Panchayat election, three contestants - A, B, and C- are contesting. 75% of the total population of 8000 cast their votes. There were no invalid or NOTA votes in the election. A got the least votes, with only 300 votes. B got 66% of the total votes and won the election. How many votes did B get?</p>",
                    question_hi: "<p>56. एक पंचायत चुनाव में, तीन प्रतियोगी - A, B, और C - चुनाव लड़ रहे हैं। 8000 की कुल जनसंख्या में से 75% ने मतदान किया। चुनाव में कोई अमान्य या नोटा (NOTA) मत नहीं थे। A को सबसे कम केवल 300 मत मिले। B को कुल मतों का 66% प्राप्त हुआ और वह चुनाव जीत गया। B को कितने मत मिले?</p>",
                    options_en: [
                        "<p>5280</p>",
                        "<p>3660</p>",
                        "<p>4060</p>",
                        "<p>3960</p>"
                    ],
                    options_hi: [
                        "<p>5280</p>",
                        "<p>3660</p>",
                        "<p>4060</p>",
                        "<p>3960</p>"
                    ],
                    solution_en: "<p>56.(d)<br>No. of voters who cast their votes = 8000 &times; 75% = 6000<br>No. of votes received by B = 6000 &times; 66% = 3960</p>",
                    solution_hi: "<p>56.(d)<br>वोट डालने वाले मतदाताओं की संख्या = 8000 &times; 75% = 6000<br>B को प्राप्त मतों की संख्या = 6000 &times; 66% = 3960</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "57",
                    section: "17",
                    question_en: "<p>57. The average of five numbers is 32. If one number is removed, then the average becomes 28. What is the removed number ?</p>",
                    question_hi: "<p>57. पांच संख्याओं का औसत 32 है। यदि एक संख्या हटा दी जाए, तो औसत 28 हो जाता है। हटाई गई संख्या कितनी है ?</p>",
                    options_en: [
                        "<p>48</p>",
                        "<p>52</p>",
                        "<p>47</p>",
                        "<p>49</p>"
                    ],
                    options_hi: [
                        "<p>48</p>",
                        "<p>52</p>",
                        "<p>47</p>",
                        "<p>49</p>"
                    ],
                    solution_en: "<p>57.(a)<br>Removed no. = 32 + (32 - 28) &times; 4 = 32 + 16 = 48&nbsp;<br><strong>Alternate :</strong> <br>Removed number = 32 &times; 5 - 28 &times; 4 = 160 - 112 = 48</p>",
                    solution_hi: "<p>57.(a)<br>हटाई गई संख्या = 32 + (32 - 28) &times; 4 = 32 +16 = 48&nbsp;<br><strong>वैकल्पिक :</strong> <br>हटाई गई संख्या = 32 &times; 5 - 28 &times; 4 = 160 - 112 = 48</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "58",
                    section: "17",
                    question_en: "<p>58. PQR is a triangle. MN is a line segment intersecting PQ in M and PR in N such that MN ∥ QR and divides &Delta;PQR into two parts, which are equal in area. What is the ratio of MQ to PQ?</p>",
                    question_hi: "<p>58. PQR एक त्रिभुज है। MN एक रेखाखंड है जो PQ को M पर और PR को N पर इस प्रकार प्रतिच्छेदित करता है कि MN ∥ QR है और &Delta;PQR को दो भागों में विभाजित करता है, जिनका क्षेत्रफल बराबर है। MQ और PQ का अनुपात क्या है?</p>",
                    options_en: [
                        "<p><math display=\"inline\"><msqrt><mn>2</mn></msqrt></math> : (<math display=\"inline\"><msqrt><mn>2</mn></msqrt></math> - 1)</p>",
                        "<p>1 : <math display=\"inline\"><msqrt><mn>2</mn></msqrt></math></p>",
                        "<p><math display=\"inline\"><msqrt><mn>2</mn></msqrt></math> : 1</p>",
                        "<p>(<math display=\"inline\"><msqrt><mn>2</mn></msqrt></math> - 1) : <math display=\"inline\"><msqrt><mn>2</mn></msqrt></math></p>"
                    ],
                    options_hi: [
                        "<p><math display=\"inline\"><msqrt><mn>2</mn></msqrt></math> : (<math display=\"inline\"><msqrt><mn>2</mn></msqrt></math> - 1)</p>",
                        "<p>1 : <math display=\"inline\"><msqrt><mn>2</mn></msqrt></math></p>",
                        "<p><math display=\"inline\"><msqrt><mn>2</mn></msqrt></math> : 1</p>",
                        "<p>(<math display=\"inline\"><msqrt><mn>2</mn></msqrt></math> - 1) : <math display=\"inline\"><msqrt><mn>2</mn></msqrt></math></p>"
                    ],
                    solution_en: "<p>58.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743652252722.png\" alt=\"rId51\" width=\"143\" height=\"132\"><br><math display=\"inline\"><mo>&#9651;</mo></math>PMN &sim; △PQR (by similar triangle rule))<br><math display=\"inline\"><mfrac><mrow><mi>P</mi><msup><mrow><mi>M</mi></mrow><mrow><mn>2</mn></mrow></msup></mrow><mrow><mi>P</mi><msup><mrow><mi>Q</mi></mrow><mrow><mn>2</mn></mrow></msup></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>a</mi><mi>r</mi><mo>(</mo><mo>&#9651;</mo><mi>P</mi><mi>M</mi><mi>N</mi><mo>)</mo></mrow><mrow><mi>a</mi><mi>r</mi><mo>(</mo><mo>&#9651;</mo><mi>P</mi><mi>Q</mi><mi>R</mi><mo>)</mo></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math><br><math display=\"inline\"><mfrac><mrow><mi>P</mi><msup><mrow><mi>M</mi></mrow><mrow></mrow></msup></mrow><mrow><mi>P</mi><msup><mrow><mi>Q</mi></mrow><mrow></mrow></msup></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msqrt><mn>2</mn></msqrt></mfrac></math><br><math display=\"inline\"><mfrac><mrow><mi>P</mi><mi>Q</mi><mo>-</mo><mi>Q</mi><mi>M</mi></mrow><mrow><mi>P</mi><mi>Q</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msqrt><mn>2</mn></msqrt></mfrac></math><br>1 - <math display=\"inline\"><mfrac><mrow><mi>M</mi><mi>Q</mi></mrow><mrow><mi>P</mi><mi>Q</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msqrt><mn>2</mn></msqrt></mfrac></math><br><math display=\"inline\"><mfrac><mrow><mi>M</mi><mi>Q</mi></mrow><mrow><mi>P</mi><mi>Q</mi></mrow></mfrac></math> = 1 - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msqrt><mn>2</mn></msqrt></mfrac></math>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msqrt><mn>2</mn></msqrt><mo>-</mo><mn>1</mn></mrow><msqrt><mn>2</mn></msqrt></mfrac></math></p>",
                    solution_hi: "<p>58.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743652252722.png\" alt=\"rId51\" width=\"157\" height=\"144\"><br><math display=\"inline\"><mo>&#9651;</mo></math>PMN &sim; △PQR (समरूप त्रिभुज नियम द्वारा))<br><math display=\"inline\"><mfrac><mrow><mi>P</mi><msup><mrow><mi>M</mi></mrow><mrow><mn>2</mn></mrow></msup></mrow><mrow><mi>P</mi><msup><mrow><mi>Q</mi></mrow><mrow><mn>2</mn></mrow></msup></mrow></mfrac></math> =&nbsp;<math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mo>&#9651;</mo><mi>P</mi><mi>M</mi><mi>N</mi><mo>)</mo><mo>&#160;</mo><mi>&#2325;&#2366;</mi><mo>&#160;</mo><mi>&#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;&#2347;&#2354;</mi></mrow><mrow><mo>(</mo><mo>&#9651;</mo><mi>P</mi><mi>Q</mi><mi>R</mi><mo>)</mo><mo>&#160;</mo><mi>&#2325;&#2366;</mi><mo>&#160;</mo><mi>&#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;&#2347;&#2354;</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math><br><math display=\"inline\"><mfrac><mrow><mi>P</mi><msup><mrow><mi>M</mi></mrow><mrow><mn>2</mn></mrow></msup></mrow><mrow><mi>P</mi><msup><mrow><mi>Q</mi></mrow><mrow><mn>2</mn></mrow></msup></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>a</mi><mi>r</mi><mo>(</mo><mo>&#9651;</mo><mi>P</mi><mi>M</mi><mi>N</mi><mo>)</mo></mrow><mrow><mi>a</mi><mi>r</mi><mo>(</mo><mo>&#9651;</mo><mi>P</mi><mi>Q</mi><mi>R</mi><mo>)</mo></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math><br><math display=\"inline\"><mfrac><mrow><mi>P</mi><msup><mrow><mi>M</mi></mrow><mrow></mrow></msup></mrow><mrow><mi>P</mi><msup><mrow><mi>Q</mi></mrow><mrow></mrow></msup></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msqrt><mn>2</mn></msqrt></mfrac></math><br><math display=\"inline\"><mfrac><mrow><mi>P</mi><mi>Q</mi><mo>-</mo><mi>Q</mi><mi>M</mi></mrow><mrow><mi>P</mi><mi>Q</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msqrt><mn>2</mn></msqrt></mfrac></math><br>1 - <math display=\"inline\"><mfrac><mrow><mi>M</mi><mi>Q</mi></mrow><mrow><mi>P</mi><mi>Q</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msqrt><mn>2</mn></msqrt></mfrac></math><br><math display=\"inline\"><mfrac><mrow><mi>M</mi><mi>Q</mi></mrow><mrow><mi>P</mi><mi>Q</mi></mrow></mfrac></math> = 1 - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msqrt><mn>2</mn></msqrt></mfrac></math>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msqrt><mn>2</mn></msqrt><mo>-</mo><mn>1</mn></mrow><msqrt><mn>2</mn></msqrt></mfrac></math></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "59",
                    section: "17",
                    question_en: "<p>59. If x &ndash; y = 25 and xy = 444, compute the value of x<sup>3</sup>&ndash; y<sup>3</sup></p>",
                    question_hi: "<p>59. यदि x &ndash; y = 25 और xy = 444 है, तो x<sup>3</sup>&ndash; y<sup>3</sup> के मान की गणना कीजिए।</p>",
                    options_en: [
                        "<p>48,925</p>",
                        "<p>42,985</p>",
                        "<p>28,495</p>",
                        "<p>26,725</p>"
                    ],
                    options_hi: [
                        "<p>48,925</p>",
                        "<p>42,985</p>",
                        "<p>28,495</p>",
                        "<p>26,725</p>"
                    ],
                    solution_en: "<p>59.(a)<br>x - y = 25<br>On squaring both side<br>(x - y)<sup>2</sup> = 625<br><math display=\"inline\"><msup><mrow><mi>x</mi></mrow><mrow><mn>2</mn></mrow></msup></math> + y<sup>2</sup> - 2xy = 625<br><math display=\"inline\"><msup><mrow><mi>x</mi></mrow><mrow><mn>2</mn></mrow></msup></math> + y<sup>2</sup> - 2 &times; 444 = 625 (given xy = 444)<br><math display=\"inline\"><msup><mrow><mi>x</mi></mrow><mrow><mn>2</mn></mrow></msup></math> + y<sup>2</sup> = 625 + 888 = 1513 <br><math display=\"inline\"><mi>x</mi></math><sup>3</sup>&ndash; y<sup>3</sup> = (x-y) (x<sup>2</sup>+y<sup>2</sup>+xy)<br>= (25) (1513 + 444)<br>= 25 <math display=\"inline\"><mo>&#215;</mo></math> 1957 = 48,925</p>",
                    solution_hi: "<p>59.(a)<br>x - y = 25<br>दोनों तरफ वर्ग करने पर<br>(x - y)<sup>2</sup> = 625<br><math display=\"inline\"><msup><mrow><mi>x</mi></mrow><mrow><mn>2</mn></mrow></msup></math> + y<sup>2</sup> - 2xy = 625<br><math display=\"inline\"><msup><mrow><mi>x</mi></mrow><mrow><mn>2</mn></mrow></msup></math> + y<sup>2</sup> - 2 &times; 444 = 625 (दिया है xy = 444)<br><math display=\"inline\"><msup><mrow><mi>x</mi></mrow><mrow><mn>2</mn></mrow></msup></math> + y<sup>2</sup> = 625 + 888 = 1513 <br><math display=\"inline\"><mi>x</mi></math><sup>3</sup>&ndash; y<sup>3</sup> = (x-y) (x<sup>2</sup>+y<sup>2</sup>+xy)<br>= (25) (1513 + 444)<br>= 25 <math display=\"inline\"><mo>&#215;</mo></math> 1957 = 48,925</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "60",
                    section: "17",
                    question_en: "<p>60. The ratio of acid and water in a mixture is 5 : 3. If 12 liters of water is added to it, the ratio of acid and water becomes 5 : 6. What is the amount of acid in this mixture?</p>",
                    question_hi: "<p>60. एक मिश्रण में अम्ल और पानी का अनुपात 5 : 3 है। यदि इसमें 12 लीटर पानी मिला दिया जाए, तो अम्ल और पानी का अनुपात 5 : 6 हो जाता है। इस मिश्रण में अम्ल की मात्रा कितनी है?</p>",
                    options_en: [
                        "<p>4 liters</p>",
                        "<p>16 liters</p>",
                        "<p>8 liters</p>",
                        "<p>20 liters</p>"
                    ],
                    options_hi: [
                        "<p>4 लीटर</p>",
                        "<p>16 लीटर</p>",
                        "<p>8 लीटर</p>",
                        "<p>20 लीटर</p>"
                    ],
                    solution_en: "<p>60.(d)<br>Let the quantity of acid and water be 5a and 3a respectively.<br>According to question,<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mi>a</mi></mrow><mrow><mn>3</mn><mi>a</mi><mo>+</mo><mn>12</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>6</mn></mfrac></math><br>30a = 15a + 60<br>a = 4.<br>Therefore, quantity of acid = 5a = 20 liter</p>",
                    solution_hi: "<p>60.(d)<br>माना अम्ल और पानी की मात्रा क्रमशः 5a और 3a है।<br>प्रश्न के अनुसार,<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mi>a</mi></mrow><mrow><mn>3</mn><mi>a</mi><mo>+</mo><mn>12</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>6</mn></mfrac></math><br>30a = 15a + 60<br>a = 4.<br>अत: अम्ल की मात्रा = 5a = 20 लीटर</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "61",
                    section: "17",
                    question_en: "<p>61. One pipe can fill a tank 6 times faster than another pipe. If both the pipes together can fill the tank in 40 minutes, then in how many minutes will the slower pipe alone be able to fill the tank?</p>",
                    question_hi: "<p>61. एक पाइप, दूसरे पाइप की तुलना में किसी टंकी को 6 गुना तेजी से भर सकता है। यदि दोनों पाइप एक साथ टंकी को 40 मिनट में भर सकते हैं, तो धीमा पाइप अकेले टंकी को कितने मिनट में भरेगा?</p>",
                    options_en: [
                        "<p>275</p>",
                        "<p>285</p>",
                        "<p>290</p>",
                        "<p>280</p>"
                    ],
                    options_hi: [
                        "<p>275</p>",
                        "<p>285</p>",
                        "<p>290</p>",
                        "<p>280</p>"
                    ],
                    solution_en: "<p>61.(d) <br>Ratio&nbsp; &nbsp; &nbsp; &nbsp; <math display=\"inline\"><mo>&#8594;</mo></math>&nbsp; pipe<sub>1</sub>&nbsp; :&nbsp; &nbsp; pipe<sub>2</sub><br>Efficiency <math display=\"inline\"><mo>&#8594;</mo></math>&nbsp; &nbsp;&nbsp; 1&nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; 6<br>Capacity of tank = 40 &times; 7 = 280 min <br>Time taken by slower pipe to fill the tank = <math display=\"inline\"><mfrac><mrow><mn>280</mn></mrow><mrow><mn>1</mn></mrow></mfrac></math> = 280 min</p>",
                    solution_hi: "<p>61.(d) <br>अनुपात <math display=\"inline\"><mo>&#8594;</mo></math> पाइप1 : पाइप2<br>क्षमता <math display=\"inline\"><mo>&#8594;</mo></math>&nbsp; &nbsp; &nbsp; 1&nbsp; &nbsp; &nbsp; :&nbsp; &nbsp;6<br>टैंक की क्षमता = 40 &times; 7 = 280 मिनट <br>धीमे पाइप द्वारा अकेले टंकी को भरने में लगा समय = <math display=\"inline\"><mfrac><mrow><mn>280</mn></mrow><mrow><mn>1</mn></mrow></mfrac></math> = 280 मिनट</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "62",
                    section: "17",
                    question_en: "<p>62. ₹1,380 has to be paid after 4 years. If four equal installments are required with a simple interest of 10% on each installment, then what is the value of each installment?</p>",
                    question_hi: "<p>62. ₹1,380 का भुगतान 4 वर्ष बाद करना है। यदि प्रत्येक किस्&zwj;त पर 10% साधारण ब्याज वाली चार समान किस्&zwj;तों की आवश्यकता है, तो प्रत्येक किस्&zwj;त का मूल्य क्या है?</p>",
                    options_en: [
                        "<p>₹200</p>",
                        "<p>₹500</p>",
                        "<p>₹300</p>",
                        "<p>₹400</p>"
                    ],
                    options_hi: [
                        "<p>₹200</p>",
                        "<p>₹500</p>",
                        "<p>₹300</p>",
                        "<p>₹400</p>"
                    ],
                    solution_en: "<p>62.(c)<br>Here,<br>P = ₹1380<br>R = 10 %<br>T = 4 years<br>Installment = <math display=\"inline\"><mfrac><mrow><mn>100</mn><mi>&#160;</mi><mi>P</mi><mi>&#160;</mi></mrow><mrow><mn>100</mn><mi>&#160;</mi><mi>t</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mfrac><mrow><mi>r</mi><mi>&#160;</mi><mi>t</mi><mi>&#160;</mi><mo>(</mo><mi>t</mi><mo>-</mo><mn>1</mn><mo>)</mo></mrow><mrow><mn>2</mn></mrow></mfrac></mrow></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math> I =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>100</mn><mo>&#215;</mo><mn>1380</mn></mrow><mrow><mn>100</mn><mo>&#215;</mo><mn>4</mn><mo>+</mo><mstyle displaystyle=\"true\"><mfrac><mrow><mn>10</mn><mo>&#215;</mo><mn>4</mn><mo>(</mo><mn>4</mn><mo>-</mo><mn>1</mn><mo>)</mo></mrow><mn>2</mn></mfrac></mstyle></mrow></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math> I =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>138000</mn><mrow><mn>400</mn><mo>+</mo><mstyle displaystyle=\"true\"><mfrac><mrow><mn>10</mn><mo>&#215;</mo><mn>4</mn><mo>&#215;</mo><mn>3</mn></mrow><mn>2</mn></mfrac></mstyle></mrow></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math> I =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>138000</mn><mrow><mo>&#160;</mo><mn>400</mn><mo>+</mo><mn>60</mn></mrow></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math> I =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>138000</mn><mn>460</mn></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math> I= ₹300&nbsp;</p>",
                    solution_hi: "<p>62.(c)<br>दिया गया है,<br>(मूलधन) P = ₹1380<br>(दर) R = 10 %<br>(समय) T = 4 वर्ष<br>किस्त (i) = <math display=\"inline\"><mfrac><mrow><mn>100</mn><mi>&#160;</mi><mi>P</mi><mi>&#160;</mi></mrow><mrow><mn>100</mn><mi>&#160;</mi><mi>t</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mfrac><mrow><mi>r</mi><mi>&#160;</mi><mi>t</mi><mi>&#160;</mi><mo>(</mo><mi>t</mi><mo>-</mo><mn>1</mn><mo>)</mo></mrow><mrow><mn>2</mn></mrow></mfrac></mrow></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math> I =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>100</mn><mo>&#215;</mo><mn>1380</mn></mrow><mrow><mn>100</mn><mo>&#215;</mo><mn>4</mn><mo>+</mo><mstyle displaystyle=\"true\"><mfrac><mrow><mn>10</mn><mo>&#215;</mo><mn>4</mn><mo>(</mo><mn>4</mn><mo>-</mo><mn>1</mn><mo>)</mo></mrow><mn>2</mn></mfrac></mstyle></mrow></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math> I =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>138000</mn><mrow><mn>400</mn><mo>+</mo><mstyle displaystyle=\"true\"><mfrac><mrow><mn>10</mn><mo>&#215;</mo><mn>4</mn><mo>&#215;</mo><mn>3</mn></mrow><mn>2</mn></mfrac></mstyle></mrow></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math> I =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>138000</mn><mrow><mo>&#160;</mo><mn>400</mn><mo>+</mo><mn>60</mn></mrow></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math> I =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>138000</mn><mn>460</mn></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math> I = ₹300&nbsp;</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "63",
                    section: "17",
                    question_en: "<p>63. In a circle of radius 5<math display=\"inline\"><msqrt><mn>13</mn></msqrt></math> cm, a chord is at a distance of 10 cm from the centre of the circle. Find the length (in cm) of the chord.</p>",
                    question_hi: "<p>63. 5<math display=\"inline\"><msqrt><mn>13</mn></msqrt></math> cm त्रिज्या वाले एक वृत्त में, एक जीवा वृत्त के केंद्र से 10 cm की दूरी पर है। जीवा की लंबाई (cm में) ज्ञात कीजिए।</p>",
                    options_en: [
                        "<p>30</p>",
                        "<p>28</p>",
                        "<p>36</p>",
                        "<p>15</p>"
                    ],
                    options_hi: [
                        "<p>30</p>",
                        "<p>28</p>",
                        "<p>36</p>",
                        "<p>15</p>"
                    ],
                    solution_en: "<p>63.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743652253005.png\" alt=\"rId52\" width=\"138\" height=\"132\"><br>In <math display=\"inline\"><mi>&#916;</mi></math>AON,<br>By using pythagoras theorem,<br><math display=\"inline\"><msup><mrow><mi>A</mi><mi>N</mi></mrow><mrow><mn>2</mn></mrow></msup></math> + ON<sup>2</sup> = AO<sup>2</sup><br><math display=\"inline\"><msup><mrow><mo>(</mo><mn>10</mn><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup></math> + AN<sup>2</sup> = (5<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>13</mn></msqrt></math>)<sup>2</sup><br>100 + <math display=\"inline\"><msup><mrow><mi>A</mi><mi>N</mi></mrow><mrow><mn>2</mn></mrow></msup></math> = 325<br>AN = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>325</mn><mo>-</mo><mn>100</mn></msqrt></math>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>225</mn></msqrt></math><br>AN = 15 cm<br>AN = NB = 15 cm<br>So, AB = 30 cm</p>",
                    solution_hi: "<p>63.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743652253005.png\" alt=\"rId52\" width=\"121\" height=\"116\"><br><math display=\"inline\"><mi>&#916;</mi></math>AON में,<br>पाइथागोरस प्रमेय से,<br><math display=\"inline\"><msup><mrow><mi>A</mi><mi>N</mi></mrow><mrow><mn>2</mn></mrow></msup></math> + ON<sup>2</sup> = AO<sup>2</sup><br><math display=\"inline\"><msup><mrow><mo>(</mo><mn>10</mn><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup></math> + AN<sup>2</sup> = (5<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>13</mn></msqrt></math>)<sup>2</sup><br>100 + <math display=\"inline\"><msup><mrow><mi>A</mi><mi>N</mi></mrow><mrow><mn>2</mn></mrow></msup></math> = 325<br>AN = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>325</mn><mo>-</mo><mn>100</mn></msqrt></math>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>225</mn></msqrt></math><br>AN = 15 cm<br>AN = NB = 15 cm<br>So, AB = 30 cm</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "64",
                    section: "17",
                    question_en: "<p>64. A motorcyclist rides his motorcycle at a speed of 90 km/hr in the first one hour and the next two hours at a speed of 120 km/hr. Find the average speed of the rider.</p>",
                    question_hi: "<p>64. एक मोटरसाइकिल चालक पहले एक घंटे में 90 km/hr की चाल से और अगले दो घंटों में 120 km/hr की चाल से अपनी मोटरसाइकिल चलाता है। चालक की औसत चाल ज्ञात कीजिए।</p>",
                    options_en: [
                        "<p>115 km/hr</p>",
                        "<p>120 km/hr</p>",
                        "<p>105 km/hr</p>",
                        "<p>110 km/hr</p>"
                    ],
                    options_hi: [
                        "<p>115 km/hr</p>",
                        "<p>120 km/hr</p>",
                        "<p>105 km/hr</p>",
                        "<p>110 km/hr</p>"
                    ],
                    solution_en: "<p>64.(d)<br>Average speed = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"true\" mathsize=\"14px\"><mfrac><mrow><mi>Total</mi><mo>&#160;</mo><mi>distance</mi><mo>&#160;</mo></mrow><mrow><mi>Total</mi><mo>&#160;</mo><mi>time</mi></mrow></mfrac></mstyle></math></p>\n<p>=&nbsp;<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>90</mn><mo>&#215;</mo><mn>1</mn><mo>+</mo><mn>120</mn><mo>&#215;</mo><mn>2</mn></mrow><mrow><mn>1</mn><mo>+</mo><mn>2</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>330</mn><mn>3</mn></mfrac></math>= 110 km/hr</p>",
                    solution_hi: "<p>64.(d)<br>औसत गति = <math style=\"font-family: Verdana;\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2325;&#2369;&#2354;</mi><mi>&#160;</mi><mi>&#2342;&#2370;&#2352;&#2368;</mi></mrow><mrow><mi>&#2325;&#2369;&#2354;</mi><mi>&#160;</mi><mi>&#2360;&#2350;&#2351;</mi></mrow></mfrac></math><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>90</mn><mo>&#215;</mo><mn>1</mn><mo>+</mo><mn>120</mn><mo>&#215;</mo><mn>2</mn></mrow><mrow><mn>1</mn><mo>+</mo><mn>2</mn></mrow></mfrac></math>&nbsp;= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>330</mn><mn>3</mn></mfrac></math>= 110 किमी/घंटा</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "65",
                    section: "17",
                    question_en: "<p>65. The percentage profit earned by selling an article for Rs.2,100 is equal to the percentage loss incurred by selling the same article for Rs.1,460. At what price should the article be sold to make 20% profit?</p>",
                    question_hi: "<p>65. एक वस्तु को Rs.2,100 में बेचने पर अर्जित प्रतिशत लाभ उसी वस्तु को Rs.1,460 में बेचने पर हुई प्रतिशत हानि के बराबर है। 20% लाभ कमाने के लिए वस्तु को किस कीमत पर बेचा जाना चाहिए?</p>",
                    options_en: [
                        "<p>Rs.2,156</p>",
                        "<p>Rs.2,056</p>",
                        "<p>Rs.2,136</p>",
                        "<p>Rs.2,256</p>"
                    ],
                    options_hi: [
                        "<p>Rs.2,156</p>",
                        "<p>Rs.2,056</p>",
                        "<p>Rs.2,136</p>",
                        "<p>Rs.2,256</p>"
                    ],
                    solution_en: "<p>65.(c)<br>Proft % = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>S</mi><mi>P</mi><mo>-</mo><mi>C</mi><mi>P</mi></mrow><mrow><mi>C</mi><mi>P</mi></mrow></mfrac></math> &times; 100 =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2100</mn><mo>-</mo><mi>CP</mi></mrow><mi>CP</mi></mfrac></math> &times; 100<br>Loss % = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>C</mi><mi>P</mi><mo>-</mo><mi>S</mi><mi>P</mi></mrow><mrow><mi>C</mi><mi>P</mi></mrow></mfrac></math> &times; 100 =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>CP</mi><mo>-</mo><mn>1460</mn></mrow><mi>CP</mi></mfrac></math> &times; 100<br>According to question,<br><math display=\"inline\"><mo>&#8658;</mo></math> Profit% = Loss %<br><math display=\"inline\"><mo>&#8658;</mo></math>&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2100</mn><mo>-</mo><mi>CP</mi></mrow><mi>CP</mi></mfrac></math>&times; 100 =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>CP</mi><mo>-</mo><mn>1460</mn></mrow><mi>CP</mi></mfrac></math> &times; 100<br><math display=\"inline\"><mo>&#8658;</mo></math> 2 CP = 2100 + 1460<br><math display=\"inline\"><mo>&#8658;</mo></math> 2 CP = 3560<br><math display=\"inline\"><mo>&#8658;</mo></math> CP = 1780<br>Now, <br>SP = 1780 <math display=\"inline\"><mo>&#215;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>120</mn><mn>100</mn></mfrac></math>= ₹ 2136</p>",
                    solution_hi: "<p>65.(c)<br>लाभ% = <math style=\"font-family: Verdana;\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2357;&#2367;&#2325;&#2381;&#2352;&#2351;</mi><mi>&#160;</mi><mi>&#2350;&#2370;&#2354;&#2381;&#2351;</mi><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mi>&#2325;&#2381;&#2352;&#2351;</mi><mi>&#160;</mi><mi>&#2350;&#2370;&#2354;&#2381;&#2351;</mi></mrow><mrow><mi>&#2325;&#2381;&#2352;&#2351;</mi><mi>&#160;</mi><mi>&#2350;&#2370;&#2354;&#2381;&#2351;</mi></mrow></mfrac></math> &times; 100 =&nbsp;<math style=\"font-family: Verdana;\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2100</mn><mo>-</mo><mi>&#2325;&#2381;&#2352;&#2351;</mi><mo>&#160;</mo><mi>&#2350;&#2370;&#2354;&#2381;&#2351;</mi></mrow><mrow><mi>&#2325;&#2381;&#2352;&#2351;</mi><mo>&#160;</mo><mi>&#2350;&#2370;&#2354;&#2381;&#2351;</mi></mrow></mfrac></math> &times; 100<br>Loss % = <math style=\"font-family: Verdana;\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2325;&#2381;&#2352;&#2351;</mi><mi>&#160;</mi><mi>&#2350;&#2370;&#2354;&#2381;&#2351;</mi><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mi>&#2357;&#2367;&#2325;&#2381;&#2352;&#2351;</mi><mi>&#160;</mi><mi>&#2350;&#2370;&#2354;&#2381;&#2351;</mi></mrow><mrow><mi>&#2325;&#2381;&#2352;&#2351;</mi><mi>&#160;</mi><mi>&#2350;&#2370;&#2354;&#2381;&#2351;</mi><mi>&#160;</mi></mrow></mfrac></math> &times; 100 =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2325;&#2381;&#2352;&#2351;</mi><mo>&#160;</mo><mi>&#2350;&#2370;&#2354;&#2381;&#2351;</mi><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mn>1460</mn></mrow><mrow><mi>&#2325;&#2381;&#2352;&#2351;</mi><mo>&#160;</mo><mi>&#2350;&#2370;&#2354;&#2381;&#2351;</mi></mrow></mfrac></math> &times; 100<br>प्रश्न के अनुसार, <br><math display=\"inline\"><mo>&#8658;</mo></math> लाभ% = हानि %<br><math display=\"inline\"><mo>&#8658;</mo></math>&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2100</mn><mo>-</mo><mi>&#2325;&#2381;&#2352;&#2351;</mi><mo>&#160;</mo><mi>&#2350;&#2370;&#2354;&#2381;&#2351;</mi></mrow><mrow><mi>&#2325;&#2381;&#2352;&#2351;</mi><mo>&#160;</mo><mi>&#2350;&#2370;&#2354;&#2381;&#2351;</mi></mrow></mfrac></math> &times; 100 =&nbsp;<math style=\"font-family: Verdana;\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2325;&#2381;&#2352;&#2351;</mi><mo>&#160;</mo><mi>&#2350;&#2370;&#2354;&#2381;&#2351;</mi><mo>-</mo><mn>1460</mn></mrow><mrow><mi>&#2325;&#2381;&#2352;&#2351;</mi><mo>&#160;</mo><mi>&#2350;&#2370;&#2354;&#2381;&#2351;</mi></mrow></mfrac></math> &times; 100<br><math display=\"inline\"><mo>&#8658;</mo></math> 2 &times; क्रय मूल्य = 2100 + 1460<br><math display=\"inline\"><mo>&#8658;</mo></math> 2 &times; क्रय मूल्य = 3560<br><math display=\"inline\"><mo>&#8658;</mo></math> क्रय मूल्य = ₹ 1780<br>अब, <br>&nbsp;विक्रय मूल्य = 1780 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>120</mn><mn>100</mn></mfrac></math>= ₹ 2136</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "66",
                    section: "17",
                    question_en: "<p>66. If the price of tea is increased by 40%, by how much percentage must the consumption of tea be decreased so as not to increase the expenditure ?</p>",
                    question_hi: "<p>66. यदि चाय की कीमतों में 40% की वृद्धि होती है, तो चाय की खपत को कितने प्रतिशत कम किया जाना चाहिए ताकि व्यय में कोई वृद्धि न हो?</p>",
                    options_en: [
                        "<p>35<math display=\"inline\"><mfrac><mrow><mn>9</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math>%</p>",
                        "<p>28<math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math>%</p>",
                        "<p>31<math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math>%</p>",
                        "<p>15<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>9</mn></mrow></mfrac></math>%</p>"
                    ],
                    options_hi: [
                        "<p>35<math display=\"inline\"><mfrac><mrow><mn>9</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math>%</p>",
                        "<p>28<math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math>%</p>",
                        "<p>31<math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math>%</p>",
                        "<p>15<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>9</mn></mrow></mfrac></math>%</p>"
                    ],
                    solution_en: "<p>66.(b)<br>According to the question,<br>Ratio -&nbsp; &nbsp;initial&nbsp; &nbsp; :&nbsp; &nbsp; final<br>Price -&nbsp; &nbsp; 100&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; 140<br>Consumption - 140 : 100<br>decrease% = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>140</mn><mo>-</mo><mn>100</mn></mrow><mn>140</mn></mfrac></math> &times; 100 = 28<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>7</mn></mfrac></math>%</p>",
                    solution_hi: "<p>66.(b)<br>प्रश्न के अनुसार,<br>अनुपात -&nbsp; प्रारंभिक&nbsp; :&nbsp; अंतिम<br>कीमत -&nbsp; &nbsp; &nbsp; 100&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp;140<br>खपत -&nbsp; &nbsp; &nbsp; &nbsp;140&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp;100<br>कमी% = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>140</mn><mo>-</mo><mn>100</mn></mrow><mn>140</mn></mfrac></math> &times; 100 = 28<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>7</mn></mfrac></math>%</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "67",
                    section: "17",
                    question_en: "<p>67. A person borrowed Rs. 15000 on compound interest at the rate of 30 percent per annum. If the interest is compounded half yearly, then what will be the amount to be paid after 1 year?</p>",
                    question_hi: "<p>67. एक व्यक्ति ने 30 प्रतिशत वार्षिक चक्रवृद्धि ब्याज की दर पर 15000 रुपए ऋण पर लिए। यदि ब्याज अर्धवार्षिक रूप से संयोजित किया जाता है, तो 1 वर्ष के बाद भुगतान की जाने वाली धनराशि कितनी होगी?</p>",
                    options_en: [
                        "<p>Rs.20428.5</p>",
                        "<p>Rs.19837.5</p>",
                        "<p>Rs.17482.5</p>",
                        "<p>Rs.18282.5</p>"
                    ],
                    options_hi: [
                        "<p>20428.5 रुपए</p>",
                        "<p>19837.5 रुपए</p>",
                        "<p>17482.5 रुपए</p>",
                        "<p>18282.5 रुपए</p>"
                    ],
                    solution_en: "<p>67.(b)<br>Rate for half yrs = <math display=\"inline\"><mfrac><mrow><mn>30</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = 15%<br>Effective CI for 1 yr = (15+15 +<math display=\"inline\"><mfrac><mrow><mn>15</mn><mo>&#215;</mo><mn>15</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math>)% = 32.25%<br>So, the amount to be paid after 1 yr = 132.25% of 15000 = ₹19,837.5</p>",
                    solution_hi: "<p>67.(b)<br>आधे साल के लिए दर = <math display=\"inline\"><mfrac><mrow><mn>30</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = 15%<br>1 वर्ष के लिए प्रभावी CI = (15+15 +<math display=\"inline\"><mfrac><mrow><mn>15</mn><mo>&#215;</mo><mn>15</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math>)% = 32.25%<br>तो, 1 वर्ष के बाद भुगतान की जाने वाली राशि = 132.25% of 15000 = ₹19,837.5</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "68",
                    section: "17",
                    question_en: "<p>68. Study the given table and answer the question that follows. <br>The given table shows the production of T-shirts by four companies i.e., C1, C2, C3 and C4 over the four years.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743652253194.png\" alt=\"rId53\"> <br>Which company had the maximum average production of T-shirts during these four years?</p>",
                    question_hi: "<p>68. दी गई तालिका का अध्ययन कीजिए और निम्नलिखित प्रश्न का उत्तर दीजिए। <br>दी गई तालिका चार वर्षों में चार कंपनियों यानी C1, C2, C3 और C4 द्वारा किए गए टी-शर्ट के उत्पादन को दर्शाती है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743652253317.png\" alt=\"rId54\"> <br>इन चार वर्षों के दौरान किस कंपनी के टी-शर्ट का औसत उत्पादन सर्वाधिक रहा?</p>",
                    options_en: [
                        "<p>C1</p>",
                        "<p>C3</p>",
                        "<p>C2</p>",
                        "<p>C4</p>"
                    ],
                    options_hi: [
                        "<p>C1</p>",
                        "<p>C3</p>",
                        "<p>C2</p>",
                        "<p>C4</p>"
                    ],
                    solution_en: "<p>68.(c)<br>Average of the production of T-shirts during four years are:-<br>For company C1 = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>243</mn><mo>+</mo><mn>248</mn><mo>+</mo><mn>232</mn><mo>+</mo><mn>242</mn></mrow><mn>4</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>965</mn><mn>4</mn></mfrac></math>= 241.25<br>For company C2 = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>244</mn><mo>+</mo><mn>239</mn><mo>+</mo><mn>240</mn><mo>+</mo><mn>244</mn></mrow><mn>4</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>967</mn><mn>4</mn></mfrac></math>= 241.75<br>For company C3 = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>244</mn><mo>+</mo><mn>236</mn><mo>+</mo><mn>235</mn><mo>+</mo><mn>230</mn></mrow><mn>4</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>945</mn><mn>4</mn></mfrac></math>= 236.25<br>For company C4 = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>240</mn><mo>+</mo><mn>230</mn><mo>+</mo><mn>248</mn><mo>+</mo><mn>242</mn></mrow><mn>4</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>960</mn><mn>4</mn></mfrac></math>= 240<br>Clearly, production of T-shirts in the four years by company C2 is maximum.</p>",
                    solution_hi: "<p>68.(c)<br>चार वर्षों के दौरान टी-शर्ट के उत्पादन का औसत इस प्रकार है:-<br>कंपनी C1 के लिए = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>243</mn><mo>+</mo><mn>248</mn><mo>+</mo><mn>232</mn><mo>+</mo><mn>242</mn></mrow><mn>4</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>965</mn><mn>4</mn></mfrac></math>= 241.25<br>कंपनी C2 के लिए = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>244</mn><mo>+</mo><mn>239</mn><mo>+</mo><mn>240</mn><mo>+</mo><mn>244</mn></mrow><mn>4</mn></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>967</mn><mn>4</mn></mfrac></math> = 241.75<br>कंपनी C3 के लिए = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>244</mn><mo>+</mo><mn>236</mn><mo>+</mo><mn>235</mn><mo>+</mo><mn>230</mn></mrow><mn>4</mn></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>945</mn><mn>4</mn></mfrac></math> = 236.25<br>कंपनी C4 के लिए = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>240</mn><mo>+</mo><mn>230</mn><mo>+</mo><mn>248</mn><mo>+</mo><mn>242</mn></mrow><mn>4</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>960</mn><mn>4</mn></mfrac></math>= 240<br>स्पष्टतः, कंपनी C2 द्वारा चार वर्षों में टी-शर्ट का उत्पादन अधिकतम है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "69",
                    section: "17",
                    question_en: "<p>69. A shopkeeper wanted to sell ₹2,000 worth of products. But he had two options, giving three successive discounts of 10% each or giving a single discount of 28%. What was the difference between the two options of discounts (in rupees) ?</p>",
                    question_hi: "<p>69. एक दुकानदार ₹2,000 मूल्य वाले उत्पाद बेचना चाहता था। लेकिन उसके पास दो विकल्प थे, प्रत्येक 10% की तीन क्रमिक छूट देना या 28% की एकल छूट देना। छूट के दोनों विकल्पों में कितना अंतर था (₹ में) ?</p>",
                    options_en: [
                        "<p>28</p>",
                        "<p>18</p>",
                        "<p>16</p>",
                        "<p>22</p>"
                    ],
                    options_hi: [
                        "<p>28</p>",
                        "<p>18</p>",
                        "<p>16</p>",
                        "<p>22</p>"
                    ],
                    solution_en: "<p>69.(b)<br>Net discount after three successive discount = 2000 - 2000 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>9</mn><mn>10</mn></mfrac></math>&times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>9</mn><mn>10</mn></mfrac></math>&times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>9</mn><mn>10</mn></mfrac></math> = 2000 - 1458 = 542<br>Discount of 28% = 2000 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>28</mn><mn>100</mn></mfrac></math>= 560<br>Required difference = 18</p>",
                    solution_hi: "<p>69.(b)<br>लगातार तीन छूट के बाद शुद्ध छूट = 2000 - 2000 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>9</mn><mn>10</mn></mfrac></math>&times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>9</mn><mn>10</mn></mfrac></math>&times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>9</mn><mn>10</mn></mfrac></math>= 2000 - 1458 = 542<br>28% की छूट = 2000 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>28</mn><mn>100</mn></mfrac></math>= 560<br>आवश्यक अंतर = 18</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "70",
                    section: "17",
                    question_en: "70. Study the given table and answer the question that follows.<br />The table shows the number of students studying in 6 different classes of 6 different schools.<br /> <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743652253485.png\" alt=\"rId55\" /> <br />The number of students studying in class X from school P forms approximately what percentage of the total number of students studying in class X from all schools together?",
                    question_hi: "70. दी गई तालिका का अध्ययन करें और नीचे दिए गए प्रश्न का उत्तर दें।<br />तालिका 6 अलग-अलग स्कूलों की 6 अलग-अलग कक्षाओं में पढ़ने वाले विद्यार्थियों की संख्या दर्शाती है।<br /> <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743652253657.png\" alt=\"rId56\" /> <br />स्कूल P से कक्षा X में पढ़ने वाले विद्यार्थियों की संख्या, सभी स्कूलों से मिलाकर कक्षा X में पढ़ने वाले विद्यार्थियों की कुल संख्या का लगभग कितना प्रतिशत है?",
                    options_en: [
                        " 8%",
                        " 24%",
                        " 32%",
                        " 16%"
                    ],
                    options_hi: [
                        " 8%",
                        " 24%",
                        " 32%",
                        " 16%"
                    ],
                    solution_en: "70.(d)<br />Required % = <math display=\"inline\"><mfrac><mrow><mn>144</mn></mrow><mrow><mn>910</mn></mrow></mfrac></math>×100 ≈ 16% ",
                    solution_hi: "70.(d)<br />आवश्यक % = <math display=\"inline\"><mfrac><mrow><mn>144</mn></mrow><mrow><mn>910</mn></mrow></mfrac></math>×100 ≈ 16% ",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "71",
                    section: "17",
                    question_en: "<p>71. If (sin <math display=\"inline\"><mi>x</mi></math> + sin y) = a and (cos x + cos y) = b, then find the value of (sin x sin y + cos x cos y).</p>",
                    question_hi: "<p>71. यदि (sin <math display=\"inline\"><mi>x</mi></math> + sin y) = a और (cos x + cos y) = b है, तो (sin x sin y + cos x cos y) का मान ज्ञात कीजिए।</p>",
                    options_en: [
                        "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi>a</mi><mn>2</mn></msup><mo>+</mo><msup><mi>b</mi><mn>2</mn></msup><mo>-</mo><mn>1</mn></mrow><mn>2</mn></mfrac></math></p>",
                        "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi>a</mi><mn>2</mn></msup><mo>-</mo><msup><mi>b</mi><mn>2</mn></msup><mo>-</mo><mn>2</mn></mrow><mn>2</mn></mfrac></math></p>",
                        "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi>a</mi><mn>2</mn></msup><mo>+</mo><msup><mi>b</mi><mn>2</mn></msup><mo>-</mo><mn>2</mn></mrow><mn>2</mn></mfrac></math></p>",
                        "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi>a</mi><mn>2</mn></msup><mo>+</mo><msup><mi>b</mi><mn>2</mn></msup><mo>+</mo><mn>2</mn></mrow><mn>2</mn></mfrac></math></p>"
                    ],
                    options_hi: [
                        "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi>a</mi><mn>2</mn></msup><mo>+</mo><msup><mi>b</mi><mn>2</mn></msup><mo>-</mo><mn>1</mn></mrow><mn>2</mn></mfrac></math></p>",
                        "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi>a</mi><mn>2</mn></msup><mo>-</mo><msup><mi>b</mi><mn>2</mn></msup><mo>-</mo><mn>2</mn></mrow><mn>2</mn></mfrac></math></p>",
                        "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi>a</mi><mn>2</mn></msup><mo>+</mo><msup><mi>b</mi><mn>2</mn></msup><mo>-</mo><mn>2</mn></mrow><mn>2</mn></mfrac></math></p>",
                        "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi>a</mi><mn>2</mn></msup><mo>+</mo><msup><mi>b</mi><mn>2</mn></msup><mo>+</mo><mn>2</mn></mrow><mn>2</mn></mfrac></math></p>"
                    ],
                    solution_en: "<p>71.(c)<br>(sin x + sin y) = a ----------------- (i)<br>Squaring both side, <br><math display=\"inline\"><mi>s</mi><mi>i</mi><msup><mrow><mi>n</mi></mrow><mrow><mn>2</mn></mrow></msup></math>x + sin<sup>2</sup>y + 2sinxsiny = a<sup>2 </sup>----------------- (ii)<br>(cos x + cos y) = b ----------------- (iii)<br>Squaring both side, <br><math display=\"inline\"><mi>c</mi><mi>o</mi><msup><mrow><mi>s</mi></mrow><mrow><mn>2</mn></mrow></msup></math>x + cos<sup>2</sup>y + 2cosxcosy = b<sup>2</sup> ----------------- (iv)<br>Adding eqn (ii) &amp; (iv) we get ;<br>(<math display=\"inline\"><mi>s</mi><mi>i</mi><msup><mrow><mi>n</mi></mrow><mrow><mn>2</mn></mrow></msup></math>x + cos<sup>2</sup>x) + (sin<sup>2</sup>y + cos<sup>2</sup>y) + 2sinxsiny + 2cosxcosy = a<sup>2</sup>+ b<sup>2</sup><br>1 + 1 + 2(sinxsiny + cosxcosy) = <math display=\"inline\"><msup><mrow><mi>a</mi></mrow><mrow><mn>2</mn></mrow></msup></math>+ b<sup>2</sup><br>2(1 + sinxsiny + cosxcosy) = <math display=\"inline\"><msup><mrow><mi>a</mi></mrow><mrow><mn>2</mn></mrow></msup></math>+ b<sup>2</sup><br>(1 + sinxsiny + cosxcosy) = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi>a</mi><mn>2</mn></msup><mo>+</mo><msup><mi>b</mi><mn>2</mn></msup></mrow><mn>2</mn></mfrac></math><br>sinxsiny + cosxcosy = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi>a</mi><mn>2</mn></msup><mo>+</mo><msup><mi>b</mi><mn>2</mn></msup></mrow><mn>2</mn></mfrac></math> - 1 =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi>a</mi><mn>2</mn></msup><mo>+</mo><msup><mi>b</mi><mn>2</mn></msup><mo>-</mo><mn>2</mn></mrow><mn>2</mn></mfrac></math></p>",
                    solution_hi: "<p>71.(c)<br>(sin x + sin y) = a ----------------- (i)<br>दोनों पक्षों का वर्ग करने पर, <br><math display=\"inline\"><mi>s</mi><mi>i</mi><msup><mrow><mi>n</mi></mrow><mrow><mn>2</mn></mrow></msup></math>x + sin<sup>2</sup>y + 2sinxsiny = a<sup>2</sup> ----------------- (ii)<br>(cos x + cos y) = b ----------------- (iii)<br>दोनों पक्षों का वर्ग करने पर, <br><math display=\"inline\"><mi>c</mi><mi>o</mi><msup><mrow><mi>s</mi></mrow><mrow><mn>2</mn></mrow></msup></math>x + cos<sup>2</sup>y + 2cosxcosy = b<sup>2</sup> ----------------- (iv)<br>समीकरण (ii) और (iv) जोड़ने पर हमें प्राप्त होता है;<br>(<math display=\"inline\"><mi>s</mi><mi>i</mi><msup><mrow><mi>n</mi></mrow><mrow><mn>2</mn></mrow></msup></math>x + cos<sup>2</sup>x) + (sin<sup>2</sup>y + cos<sup>2</sup>y) + 2sinxsiny + 2cosxcosy = a<sup>2</sup>+ b<sup>2</sup><br>1+1+2(sinxsiny + cosxcosy) = a<sup>2</sup>+ b<sup>2</sup><br>2(1 + sinxsiny + cosxcosy) = a<sup>2</sup>+ b<sup>2</sup><br>(1 + sinxsiny + cosxcosy) = <math display=\"inline\"><mfrac><mrow><msup><mrow><mi>a</mi></mrow><mrow><mn>2</mn></mrow></msup><mo>+</mo><msup><mrow><mi>b</mi></mrow><mrow><mn>2</mn></mrow></msup></mrow><mrow><mn>2</mn></mrow></mfrac></math><br>sinxsiny + cosxcosy = <math display=\"inline\"><mfrac><mrow><msup><mrow><mi>a</mi></mrow><mrow><mn>2</mn></mrow></msup><mo>+</mo><msup><mrow><mi>b</mi></mrow><mrow><mn>2</mn></mrow></msup></mrow><mrow><mn>2</mn></mrow></mfrac></math> - 1 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi>a</mi><mn>2</mn></msup><mo>+</mo><msup><mi>b</mi><mn>2</mn></msup><mo>-</mo><mn>2</mn></mrow><mn>2</mn></mfrac></math></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "72",
                    section: "17",
                    question_en: "<p>72. Amongst the following fractions, the largest and smallest fractions, respectively, are<br><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math>,<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math>,<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>5</mn></mfrac></math>,<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>6</mn></mfrac></math>,<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>4</mn></mfrac></math></p>",
                    question_hi: "<p>72. निम्नलिखित भिन्नों में से सबसे बड़ी और सबसे छोटी भिन्न क्रमशः है<br><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math>,<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math>,<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>5</mn></mfrac></math>,<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>6</mn></mfrac></math>,<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>4</mn></mfrac></math></p>",
                    options_en: [
                        "<p><math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math> and <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> and <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>6</mn></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math> and <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>4</mn></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> and <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>5</mn></mfrac></math></p>"
                    ],
                    options_hi: [
                        "<p><math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math> और <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> और <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>6</mn></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math> और <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>4</mn></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> और <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>5</mn></mfrac></math></p>"
                    ],
                    solution_en: "<pre>72.(a) LCM (2, 3, 5, 6, 4) = 60<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mo>&#215;</mo><mn>30</mn></mrow><mrow><mn>2</mn><mo>&#215;</mo><mn>30</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>30</mn><mn>60</mn></mfrac></math> <br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mo>&#215;</mo><mn>20</mn></mrow><mrow><mn>3</mn><mo>&#215;</mo><mn>20</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>20</mn><mn>60</mn></mfrac></math><br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>4</mn><mo>&#215;</mo><mn>12</mn></mrow><mrow><mn>5</mn><mo>&#215;</mo><mn>12</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>48</mn><mn>60</mn></mfrac></math><br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mo>&#215;</mo><mn>10</mn></mrow><mrow><mn>6</mn><mo>&#215;</mo><mn>10</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>50</mn><mn>60</mn></mfrac></math><br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3</mn><mo>&#215;</mo><mn>15</mn></mrow><mrow><mn>4</mn><mo>&#215;</mo><mn>15</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>45</mn><mn>60</mn></mfrac></math><br>The largest and the smallest fraction = <math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math> and <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math></pre>",
                    solution_hi: "<p>72.(a)&nbsp;LCM (2, 3, 5, 6, 4) = 60<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mo>&#215;</mo><mn>30</mn></mrow><mrow><mn>2</mn><mo>&#215;</mo><mn>30</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>30</mn><mn>60</mn></mfrac></math> <br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mo>&#215;</mo><mn>20</mn></mrow><mrow><mn>3</mn><mo>&#215;</mo><mn>20</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>20</mn><mn>60</mn></mfrac></math><br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>4</mn><mo>&#215;</mo><mn>12</mn></mrow><mrow><mn>5</mn><mo>&#215;</mo><mn>12</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>48</mn><mn>60</mn></mfrac></math><br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mo>&#215;</mo><mn>10</mn></mrow><mrow><mn>6</mn><mo>&#215;</mo><mn>10</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>50</mn><mn>60</mn></mfrac></math><br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3</mn><mo>&#215;</mo><mn>15</mn></mrow><mrow><mn>4</mn><mo>&#215;</mo><mn>15</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>45</mn><mn>60</mn></mfrac></math><br>सबसे बड़ी और सबसे छोटी भिन्न = <math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math> और <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "73",
                    section: "17",
                    question_en: "73. The age of the older of two boys is thrice that of the younger. 9 years ago it was five times that of the younger. Find the present age of each.",
                    question_hi: "73. दो लड़कों में से बड़े की आयु छोटे की आयु की तीन गुनी है। 9 वर्ष पहले यह छोटे की आयु से पांच गुना थी। प्रत्येक की वर्तमान आयु ज्ञात कीजिए।",
                    options_en: [
                        " 15 years, 45 years ",
                        " 18 years, 54 years ",
                        " 17 years, 51 years",
                        " 12 years, 36 years"
                    ],
                    options_hi: [
                        " 15 वर्ष , 45 वर्ष ",
                        " 18 वर्ष , 54 वर्ष ",
                        " 17 वर्ष , 51 वर्ष ",
                        " 12 वर्ष , 36 वर्ष "
                    ],
                    solution_en: "73.(b)<br />Let the age of younger boy be <math display=\"inline\"><mi>x</mi></math> year<br />Then, age of older boy = 3<math display=\"inline\"><mi>x</mi></math><br />Now, according to the question,<br />(<math display=\"inline\"><mn>3</mn><mi>x</mi></math> - 9) = 5 (x - 9)<br />3<math display=\"inline\"><mi>x</mi></math> - 9 = 5x - 45<br />2<math display=\"inline\"><mi>x</mi></math> = 36    <br /><math display=\"inline\"><mi>x</mi></math> = 18<br />So, the present age of each is 18 yr and 54 yr.",
                    solution_hi: "73.(b)<br />माना , छोटे लड़के की आयु = <math display=\"inline\"><mi>x</mi></math> वर्ष <br />तो, बड़े लड़के की आयु = 3<math display=\"inline\"><mi>x</mi></math><br />अब, प्रश्न के अनुसार,<br />(<math display=\"inline\"><mn>3</mn><mi>x</mi></math> - 9) = 5 (x - 9)<br />3<math display=\"inline\"><mi>x</mi></math> - 9 = 5x - 45<br />2<math display=\"inline\"><mi>x</mi></math> = 36    <br /><math display=\"inline\"><mi>x</mi></math> = 18<br />तो, प्रत्येक की वर्तमान आयु 18 वर्ष और 54 वर्ष है।",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "74",
                    section: "17",
                    question_en: "<p>74. If x : 6 :: 6 : y and x : y :: y : 6, then which of the options below gives the correct values of x and y in that order?</p>",
                    question_hi: "<p>74. यदि x : 6 :: 6 : y और x : y :: y : 6 है, तो नीचे दिए गए विकल्पों में से कौन-सा विकल्प इस क्रम में x और y के सही मान प्रदान करता है?</p>",
                    options_en: [
                        "<p>6 and 6</p>",
                        "<p>12 and 12</p>",
                        "<p>6 and 12</p>",
                        "<p>12 and 6</p>"
                    ],
                    options_hi: [
                        "<p>6 और 6</p>",
                        "<p>12 और 12</p>",
                        "<p>6 और 12</p>",
                        "<p>12 और 6</p>"
                    ],
                    solution_en: "<p>74.(a) By checking option one by one we get option (a) satisfies <br>x : 6 :: 6 : y <math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>6</mn><mn>6</mn></mfrac></math>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>6</mn><mn>6</mn></mfrac></math>&rArr; 1 : 1<br>x : y :: y : 6 <math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>6</mn><mn>6</mn></mfrac></math>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>6</mn><mn>6</mn></mfrac></math>&rArr; 1 : 1</p>",
                    solution_hi: "<p>74.(a) एक-एक करके विकल्प की जाँच करने पर हमें विकल्प (a) मिलता है जो संतुष्ट करता है <br>x : 6 :: 6 : y <math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>6</mn><mn>6</mn></mfrac></math>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>6</mn><mn>6</mn></mfrac></math>&rArr; 1 : 1<br>x : y :: y : 6 <math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>6</mn><mn>6</mn></mfrac></math>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>6</mn><mn>6</mn></mfrac></math>&rArr; 1 : 1</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "75",
                    section: "7",
                    question_en: "<p>75. Three students of class VII participated in a race during a school sports day. In a 1 km race, A wins the race with B by 100 m, and in a 300 m race, B beats C by 50 m. Then in the race of 1 km, with what margin will A beat C?</p>",
                    question_hi: "<p>75. स्कूल के खेल दिवस के दौरान कक्षा VII के तीन विद्यार्थियों ने 1 km की दौड़ में भाग लिया। यदि A, B से 100 m के अंतर से दौड़ जीत जाता है और B, C को 300 m की दौड़ में 50 m से हरा देता है, तो 1 km की दौड़ में, A, C को कितने अंतर से हराएगा ?</p>",
                    options_en: [
                        "<p>700 m</p>",
                        "<p>750 m</p>",
                        "<p>150 m</p>",
                        "<p>250 m</p>"
                    ],
                    options_hi: [
                        "<p>700 m</p>",
                        "<p>750 m</p>",
                        "<p>150 m</p>",
                        "<p>250 m</p>"
                    ],
                    solution_en: "<p>75.(d) In 1km race<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; A&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;B <br>Distance<math display=\"inline\"><mo>&#8594;</mo></math> 1000&nbsp; :&nbsp; 900 <br>In 300m race<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;B&nbsp; &nbsp; &nbsp;C&nbsp; &nbsp;<br>Distance<math display=\"inline\"><mo>&#8594;</mo></math> 300 : 250<br>Hence ,&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;A&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; B&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; C <br>Distance<math display=\"inline\"><mo>&#8594;</mo></math> 1000&nbsp; &nbsp;:&nbsp; &nbsp;900<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;300&times;3&nbsp; &nbsp;:&nbsp; 250&times;3<br><br>Distance <math display=\"inline\"><mo>&#8594;</mo></math>&nbsp; &nbsp; A&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;B&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;C<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;1000&nbsp; &nbsp;:&nbsp; &nbsp;900&nbsp; :&nbsp; &nbsp; 750<br>Margin between A beat C = 1000 - 750 = 250m</p>",
                    solution_hi: "<p>75.(d) 1 किमी दौड़ में<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;A&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;B&nbsp;<br>दूरी <math display=\"inline\"><mo>&#8594;</mo></math>&nbsp; &nbsp; &nbsp;1000&nbsp; &nbsp;:&nbsp; &nbsp; 900 <br>300 मीटर दौड़ में<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;B&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; C&nbsp;<br>दूरी <math display=\"inline\"><mo>&#8594;</mo></math>&nbsp; &nbsp; &nbsp; 300&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp;250<br>अत:&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;A&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;B&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; C <br>दूरी <math display=\"inline\"><mo>&#8594;</mo></math>&nbsp; &nbsp;1000&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; 900<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 300&times;3&nbsp; &nbsp; :&nbsp; &nbsp; 250&times;3<br><br>दूरी <math display=\"inline\"><mo>&#8594;</mo></math>&nbsp; &nbsp; A&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; B&nbsp; &nbsp; &nbsp; &nbsp; C<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 1000&nbsp; :&nbsp; 900&nbsp; :&nbsp; &nbsp;750<br>A और C के बीच का अंतर = 1000 - 750 = 250m</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "76",
                    section: "7",
                    question_en: "<p>76. Identify the segment in the sentence, which contains the grammatical error. <br>The Prime Minister said the country was in its way toward reaching its goal of becoming the first country with women in at least 40 percent of public sector management positions.</p>",
                    question_hi: "<p>76. Identify the segment in the sentence, which contains the grammatical error. <br>The Prime Minister said the country was in its way toward reaching its goal of becoming the first country with women in at least 40 percent of public sector management positions.</p>",
                    options_en: [
                        "<p>The Prime Minister said the country was</p>",
                        "<p>in its way toward reaching its goal of</p>",
                        "<p>in at least 40 percent of public sector management positions</p>",
                        "<p>becoming the first country with women</p>"
                    ],
                    options_hi: [
                        "<p>The Prime Minister said the country was</p>",
                        "<p>in its way toward reaching its goal of</p>",
                        "<p>in at least 40 percent of public sector management positions</p>",
                        "<p>becoming the first country with women</p>"
                    ],
                    solution_en: "<p>76.(b) There is a prepositional error in the given sentence. The preposition &lsquo;in&rsquo; must be replaced with &lsquo;on&rsquo;<br>Because \"In the way\" means that something is an obstacle. Or \"On the way\" means that something, or someone, is in the process of reaching a goal, or a destination. Hence, &lsquo;on its way towards&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>76.(b) दिए गए sentence में prepositional त्रुटि है। preposition \'in\' को \'on\' से बदला जाना चाहिए क्योंकि \"In the way\" का अर्थ है कि कुछ बाधा है। या \"On the way\" का अर्थ है कि कुछ, या कोई व्यक्ति, लक्ष्य या गंतव्य तक पहुंचने की प्रक्रिया में है। इसलिए, \'on its way towards\' सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "77",
                    section: "7",
                    question_en: "<p>77. Select the most appropriate Synonym of the given word <br>Wreak</p>",
                    question_hi: "<p>77. Select the most appropriate Synonym of the given word <br>Wreak</p>",
                    options_en: [
                        "<p>fail</p>",
                        "<p>cause</p>",
                        "<p>forget</p>",
                        "<p>neglect</p>"
                    ],
                    options_hi: [
                        "<p>fail</p>",
                        "<p>cause</p>",
                        "<p>forget</p>",
                        "<p>neglect</p>"
                    ],
                    solution_en: "<p>77.(b) <strong>Cause-</strong> a thing or person that makes something happen.<br><strong>Wreak-</strong> to cause great damage or harm to somebody/something.<br><strong>Fail-</strong> to not be successful in something. <br><strong>Forget-</strong> to not be able to remember something. <br><strong>Neglect-</strong> to give too little or no attention or care to somebody/something.</p>",
                    solution_hi: "<p dir=\"ltr\">77.(b) Cause (उत्पन्न करना) - a thing or person that makes something happen.<br>Wreak (बदला लेना) - to cause great damage or harm to somebody/something.<br>Fail (असफल होना) - to not be successful in something.<br>Forget (भूल जाना) - to not be able to remember something.<br>Neglect (अनादर करना) - to give too little or no attention or care to somebody/something.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "78",
                    section: "7",
                    question_en: "<p>78. Identify the best way to improve the underlined part of the given sentence. If there is no improvement required, select &lsquo;no improvement&rsquo;. <br>The labourers <span style=\"text-decoration: underline;\"><strong>are digging</strong></span> borewell in the field from sunrise and will continue to do so until sunset.</p>",
                    question_hi: "<p>78. Identify the best way to improve the underlined part of the given sentence. If there is no improvement required, select &lsquo;no improvement&rsquo;. <br>The labourers <span style=\"text-decoration: underline;\"><strong>are digging</strong></span> borewell in the field from sunrise and will continue to do so until sunset.</p>",
                    options_en: [
                        "<p>have digged</p>",
                        "<p>have been digging</p>",
                        "<p>were digging</p>",
                        "<p>no improvement</p>"
                    ],
                    options_hi: [
                        "<p>have digged</p>",
                        "<p>have been digging</p>",
                        "<p>were digging</p>",
                        "<p>no improvement</p>"
                    ],
                    solution_en: "<p>78.(b) have been digging<br>The Underlined initial part of the sentence must be in the &lsquo;Present Perfect Continuous tense(has/have been + V-ing)&rsquo; as the sentence mentions the starting and the ending time (from sunrise to sunset.) Hence, &lsquo;have been digging&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>78.(b) have been digging<br>Sentence का Underlined initial part &lsquo;Present Perfect Continuous tense(has/have been + V-ing)&rsquo; में होना चाहिए क्योंकि sentence में starting और ending समय (सूर्योदय से सूर्यास्त तक) का उल्लेख है। इसलिए, &lsquo;have been digging&rsquo; सर्वाधिक उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "79",
                    section: "7",
                    question_en: "<p>79. Identify the best way to improve the underlined part of the given sentence. If there is no improvement required, select &lsquo;no improvement&rsquo;.<br>Some miners <span style=\"text-decoration: underline;\"><strong>will have died</strong></span> at a blast in a coal mine in eastern Ukraine as per estimates.</p>",
                    question_hi: "<p>79. Identify the best way to improve the underlined part of the given sentence. If there is no improvement required, select &lsquo;no improvement&rsquo;.<br>Some miners <span style=\"text-decoration: underline;\"><strong>will have died</strong></span> at a blast in a coal mine in eastern Ukraine as per estimates.</p>",
                    options_en: [
                        "<p>Have died</p>",
                        "<p>Has died</p>",
                        "<p>dies</p>",
                        "<p>No improvement</p>"
                    ],
                    options_hi: [
                        "<p>Have died</p>",
                        "<p>Has died</p>",
                        "<p>dies</p>",
                        "<p>No improvement</p>"
                    ],
                    solution_en: "<p>79.(a) Have died<br>The given sentence is in the present perfect tense so it must have the verb in the present perfect form (has/have + V3(third form of the verb). Hence, &lsquo;have died(V3)&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>79.(a) Have died<br>दिया गया sentence present perfect tense ​​में है, इसलिए इसमें verb present perfect form (has/have +V3) में होनी चाहिए। इसलिए, \'have died(V3)\' सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "80",
                    section: "7",
                    question_en: "<p>80. Choose the most appropriate option to change the narration (direct/indirect) of the given sentence:<br>She said, &ldquo;Will you have time to do it ?&rdquo; And I said, &ldquo;Yes&rdquo;.</p>",
                    question_hi: "<p>80. Choose the most appropriate option to change the narration (direct/indirect) of the given sentence:<br>She said, &ldquo;Will you have time to do it ?&rdquo; And I said, &ldquo;Yes&rdquo;.</p>",
                    options_en: [
                        "<p>She asked (me) if I would have time to do it and I said that I would.</p>",
                        "<p>She asked that she would have time to do it and I said that I could.</p>",
                        "<p>She asked that I would have time to do it and I said that I would.</p>",
                        "<p>She asked me for time and I replied in the affirmative.</p>"
                    ],
                    options_hi: [
                        "<p>She asked (me) if I would have time to do it and I said that I would.</p>",
                        "<p>She asked that she would have time to do it and I said that I could.</p>",
                        "<p>She asked that I would have time to do it and I said that I would.</p>",
                        "<p>She asked me for time and I replied in the affirmative.</p>"
                    ],
                    solution_en: "<p>80.(a) She asked (me) if I would have time to do it and I said that I would.<br>(b) She asked <strong>that</strong> she would have time to do it and I said that I could. (Listener is missing)<br>(c) She <strong>asked that</strong> I would have time to do it and I said that I would. (Listener is missing)<br>(d) She asked me for time and I replied in the affirmative(Meaning of sentence changed)</p>",
                    solution_hi: "<p>80.(a) She asked (me) if I would have time to do it and I said that I would.<br>(b) She asked <strong>that</strong> she would have time to do it and I said that I could. (Listener नहीं है)<br>(c) She <strong>asked that</strong> I would have time to do it and I said that I would. (Listener नहीं है)<br>(d) She asked me for time and I replied in the affirmative(Sentence का अर्थ बदल गया है )</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "81",
                    section: "7",
                    question_en: "<p>81. Select the option that expresses the given sentence in active voice.<br>The homework must be done by you.</p>",
                    question_hi: "<p>81. Select the option that expresses the given sentence in active voice.<br>The homework must be done by you.</p>",
                    options_en: [
                        "<p>You should have done the homework.</p>",
                        "<p>You do the homework.</p>",
                        "<p>You will do the homework.</p>",
                        "<p>You must do the homework.</p>"
                    ],
                    options_hi: [
                        "<p>You should have done the homework.</p>",
                        "<p>You do the homework.</p>",
                        "<p>You will do the homework.</p>",
                        "<p>You must do the homework.</p>"
                    ],
                    solution_en: "<p>81.(d) You must do the homework. (Correct)<br>(a) You <span style=\"text-decoration: underline;\">should have done</span> the homework. (Incorrect Verb)<br>(b) You do the homework. (Modal is missing)<br>(c) You <span style=\"text-decoration: underline;\">will</span> do the homework. (Incorrect Modal)</p>",
                    solution_hi: "<p>81.(d) You must do the homework. (Correct)<br>(a) You <span style=\"text-decoration: underline;\">should have done</span> the homework. (गलत Verb)<br>(b) You do the homework. (Modal missing है)<br>(c) You <span style=\"text-decoration: underline;\">will</span> do the homework. (गलत Modal)</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "82",
                    section: "7",
                    question_en: "<p>82. Given below are four jumbled sentences. Pick the option that gives their correct order.<br>(A) I went near the edge of the water, and I saw a black something struggling above its surface. <br>(B) Then it rose higher and it was the trunk of my elephant. <br>(C) I thought he was drowning.<br>(D) I was helpless because I could not jump into the water and save the four hundred pounds of him.</p>",
                    question_hi: "<p>82. Given below are four jumbled sentences. Pick the option that gives their correct order.<br>(A) I went near the edge of the water, and I saw a black something struggling above its surface. <br>(B) Then it rose higher and it was the trunk of my elephant. <br>(C) I thought he was drowning.<br>(D) I was helpless because I could not jump into the water and save the four hundred pounds of him.</p>",
                    options_en: [
                        "<p>ACBD</p>",
                        "<p>DCBA</p>",
                        "<p>ABCD</p>",
                        "<p>CBDA</p>"
                    ],
                    options_hi: [
                        "<p>ACBD</p>",
                        "<p>DCBA</p>",
                        "<p>ABCD</p>",
                        "<p>CBDA</p>"
                    ],
                    solution_en: "<p>82.(c)ABCD<br>In the parajumble, the narrator&rsquo;s experience is mentioned . Sentence A will be the first sentence, as it is explained that the narrator saw a black something struggling in the water. B will follow A as in B, it is given he thought it was his elephant&rsquo;s trunk. B will be followed by C and at the end D. As it is explained he thought the elephant was drowning but he was helpless. Hence option ( c) has the correct sequence.</p>",
                    solution_hi: "<p>82.(c)ABCD<br>Parajumble में, narrator के अनुभव का उल्लेख किया गया है। Sentence A first sentence होगा, जैसा कि बताया गया है कि narrator ने पानी में एक काली वस्तु को strugglingकरते हुए देखा। A के बाद B आएगा , यह दिया गया है कि उसने सोचा कि यह उसके elephant&rsquo;s trunk है। B के बाद C आएगा और अंत में D होगा। जैसा कि समझाया गया है, उसने सोचा कि elephant डूब रहा था लेकिन वह helpless था। इसलिए option ( c) में सही sequence है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "83",
                    section: "7",
                    question_en: "<p>83. Identify the word that is misspelt.</p>",
                    question_hi: "<p>83. Identify the word that is misspelt.</p>",
                    options_en: [
                        "<p>ineffable</p>",
                        "<p>Replacable</p>",
                        "<p>Replacement</p>",
                        "<p>Relieve</p>"
                    ],
                    options_hi: [
                        "<p>ineffable</p>",
                        "<p>Replacable</p>",
                        "<p>Replacement</p>",
                        "<p>Relieve</p>"
                    ],
                    solution_en: "<p>83.(b) Replacable<br>&lsquo;Replaceable&rsquo; is the correct spelling.</p>",
                    solution_hi: "<p>83.(b) Replacable<br>&lsquo;Replaceable&rsquo; सही spelling है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "84",
                    section: "7",
                    question_en: "<p>84. Identify the segment in the sentence, which contains the grammatical error. <br>She gave me the details of all the schools she had visited while she was on her official tour.</p>",
                    question_hi: "<p>84. Identify the segment in the sentence, which contains the grammatical error. <br>She gave me the details of all the schools she had visited while she was on her official tour.</p>",
                    options_en: [
                        "<p>She gave me the details</p>",
                        "<p>of all the schools she had</p>",
                        "<p>visited while she was on her official tour</p>",
                        "<p>No error</p>"
                    ],
                    options_hi: [
                        "<p>She gave me the details</p>",
                        "<p>of all the schools she had</p>",
                        "<p>visited while she was on her official tour</p>",
                        "<p>No error</p>"
                    ],
                    solution_en: "<p>84.(d) No error. The given sentence is grammatically correct.</p>",
                    solution_hi: "<p>84.(d) No error. दिया गया वाक्य grammatically correct है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "85",
                    section: "7",
                    question_en: "<p>85. Select the most appropriate meaning of the given idiom.<br>When pigs fly</p>",
                    question_hi: "<p>85. Select the most appropriate meaning of the given idiom.<br>When pigs fly</p>",
                    options_en: [
                        "<p>Something that will never happen</p>",
                        "<p>Something that will never deviate</p>",
                        "<p>something that will get completed</p>",
                        "<p>Something that will never last long</p>"
                    ],
                    options_hi: [
                        "<p>Something that will never happen</p>",
                        "<p>Something that will never deviate</p>",
                        "<p>something that will get completed</p>",
                        "<p>Something that will never last long</p>"
                    ],
                    solution_en: "<p>85.(a) <strong>When pigs fly</strong> - something that will never happen.<br>E.g.- It will snow in the desert when pigs fly.</p>",
                    solution_hi: "<p>85.(a) <strong>When pigs fly</strong> - something that will never happen./कुछ ऐसा जो कभी नहीं होगा<br>E.g.- It will snow in the desert when pigs fly.</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "86",
                    section: "7",
                    question_en: "<p>86. Select the option that expresses the given sentence in active voice. <br>The painting was admired by art enthusiasts from around the world.</p>",
                    question_hi: "<p>86. Select the option that expresses the given sentence in active voice. <br>The painting was admired by art enthusiasts from around the world.</p>",
                    options_en: [
                        "<p>The painting was being admired by art enthusiasts from around the world.</p>",
                        "<p>Art enthusiasts from around the world admired the painting.</p>",
                        "<p>Art enthusiasts from around the world have admired the painting.</p>",
                        "<p>The painting has been admired by art enthusiasts from around the world.</p>"
                    ],
                    options_hi: [
                        "<p>The painting was being admired by art enthusiasts from around the world.</p>",
                        "<p>Art enthusiasts from around the world admired the painting.</p>",
                        "<p>Art enthusiasts from around the world have admired the painting.</p>",
                        "<p>The painting has been admired by art enthusiasts from around the world.</p>"
                    ],
                    solution_en: "<p>86.(b) Art enthusiasts from around the world admired the painting. (Correct)<br>(a) The painting was being admired by art enthusiasts from around the world. (Incorrect Sentence Structure)<br>(c) Art enthusiasts from around the world <span style=\"text-decoration: underline;\">have admired</span> the painting. (Incorrect Tense)<br>(d) The painting has been admired by art enthusiasts from around the world. (Incorrect Sentence Structure)</p>",
                    solution_hi: "<p>86.(b) Art enthusiasts from around the world admired the painting. (Correct)<br>(a) The painting was being admired by art enthusiasts from around the world. (गलत Sentence Structure)<br>(c) Art enthusiasts from around the world <span style=\"text-decoration: underline;\">have admired</span> the painting. (गलत Tense)<br>(d) The painting has been admired by art enthusiasts from around the world. (गलत Sentence Structure)</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "87",
                    section: "7",
                    question_en: "<p>87. Select the most appropriate idiom that can substitute the underlined words in the given sentence.<br>It&rsquo;s Christmas and everything looks so beautiful. It makes me believe that <span style=\"text-decoration: underline;\">life is wonderful and pleasant.</span></p>",
                    question_hi: "<p>87. Select the most appropriate idiom that can substitute the underlined words in the given sentence.<br>It&rsquo;s Christmas and everything looks so beautiful. It makes me believe that <span style=\"text-decoration: underline;\">life is wonderful and pleasant.</span></p>",
                    options_en: [
                        "<p>I&rsquo;m facing the music</p>",
                        "<p>I&rsquo;m changing my tune</p>",
                        "<p>life is a bowl of cherries</p>",
                        "<p>I&rsquo;m up in the air</p>"
                    ],
                    options_hi: [
                        "<p>I&rsquo;m facing the music</p>",
                        "<p>I&rsquo;m changing my tune</p>",
                        "<p>life is a bowl of cherries</p>",
                        "<p>I&rsquo;m up in the air</p>"
                    ],
                    solution_en: "<p>87.(c) <strong>Bowl of cherries</strong>- wonderful and pleasant.</p>",
                    solution_hi: "<p>87.(c) <strong>Bowl of cherries</strong> - wonderful and pleasant./अद्भुत एवं सुखद</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "88",
                    section: "7",
                    question_en: "<p>88. Select the word which means the same as the group of words given.<br>The science or philosophy of law</p>",
                    question_hi: "<p>88. Select the word which means the same as the group of words given.<br>The science or philosophy of law</p>",
                    options_en: [
                        "<p>Justice</p>",
                        "<p>infirmary</p>",
                        "<p>jurisprudence</p>",
                        "<p>archive</p>"
                    ],
                    options_hi: [
                        "<p>Justice</p>",
                        "<p>infirmary</p>",
                        "<p>jurisprudence</p>",
                        "<p>archive</p>"
                    ],
                    solution_en: "<p>88.(c) <strong>jurisprudence-</strong> The science or philosophy of law<br>(a) <strong>Justice</strong> -just behaviour or treatment.<br>(b) <strong>infirmary- </strong>a place in a large institution for the care of those who are ill.<br>(d) <strong>archive</strong> -a collection of historical documents or records providing information about a place, institution, or group of people.</p>",
                    solution_hi: "<p>88.(c) <strong>jurisprudence-विधिशास्त्र-</strong> The science or philosophy of law<br>(a) <strong>Justice -न्याय-</strong> just behaviour or treatment.<br>(b) <strong>infirmary-दुर्बलता-</strong> a place in a large institution for the care of those who are ill.<br>(d) <strong>archive -संग्रह- </strong>a collection of historical documents or records providing information about a place, institution, or group of people.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "89",
                    section: "7",
                    question_en: "<p>89. Select the option that can be used as a one-word substitute for the given group of words. <br>A person who is a lover of women.</p>",
                    question_hi: "<p>89. Select the option that can be used as a one-word substitute for the given group of words. <br>A person who is a lover of women.</p>",
                    options_en: [
                        "<p>Monogamist</p>",
                        "<p>Misogynist</p>",
                        "<p>Philologist</p>",
                        "<p>Philogynist</p>"
                    ],
                    options_hi: [
                        "<p>Monogamist</p>",
                        "<p>Misogynist</p>",
                        "<p>Philologist</p>",
                        "<p>Philogynist</p>"
                    ],
                    solution_en: "<p>89.(d) <strong>Philogynist-</strong> a person who is a lover of women.<br><strong>Monogamist-</strong> a person who is in a relationship with only one partner at a time.<br><strong>Misogynist-</strong> a man who hates women<br><strong>Philologist-</strong> who specializes in the study of language in oral and written historical sources</p>",
                    solution_hi: "<p>89.(d) <strong>Philogynist</strong> (स्&zwj;त्रीप्रेमी) - a person who is a lover of women.<br><strong>Monogamist</strong> (एकविवाही) - a person who is in a relationship with only one partner at a time.<br><strong>Misogynist</strong> (स्त्री द्वेषी) - a man who hates women<br><strong>Philologist</strong> (भाषाविद) - who specializes in the study of language in oral and written historical sources</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "90",
                    section: "7",
                    question_en: "90. Given below are four jumbled sentences. Pick the option that gives their correct order.<br />(A) The main objective was to have complete learning and realising one\'s inner potential. <br />(B) Students lived away from their homes for years together till they achieved their goals.<br />(C ) During that period, the gurus and their shishyas lived together in the Gurukul<br />(D) They helped each other in day-to-day life. ",
                    question_hi: "90. Given below are four jumbled sentences. Pick the option that gives their correct order.<br />(A) The main objective was to have complete learning and realising one\'s inner potential. <br />(B) Students lived away from their homes for years together till they achieved their goals.<br />(C ) During that period, the gurus and their shishyas lived together in the Gurukul<br />(D) They helped each other in day-to-day life. ",
                    options_en: [
                        " ABCD",
                        " CDAB",
                        " ACDB",
                        " CBDA"
                    ],
                    options_hi: [
                        " ABCD",
                        " CDAB",
                        " ACDB",
                        " CBDA"
                    ],
                    solution_en: "90. (b) CDAB<br />Sentence C will be the starting line as it contains the main idea of the parajumble i.e. The period of gurukuls. And, Sentence D states the lifestyle of gurus and shishyas. So, D will follow C. Further, Sentence A states about their main objective & Sentence B states about the students . So, B will follow A. Going through the options, option (b) has the correct sequence.",
                    solution_hi: "90. (b) CDAB<br />Sentence C starting line होगी क्योंकि इसमें parajumble का मुख्य विचार ‘The period of gurukuls’ शामिल है। Sentence D गुरुओं और शिष्यों की जीवन शैली को बताता है। तो, C के बाद D आएगा। आगे, Sentence A उनके मुख्य उद्देश्य के बारे में बताता है और Sentence B छात्रों के बारे में बताता है। तो, A के बाद B आएगा। options के माध्यम से जाने पर, option(b) में सही sequence है।",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "91",
                    section: "7",
                    question_en: "<p>91. Select the most appropriate Synonym of the given word.<br>Probe</p>",
                    question_hi: "<p>91. Select the most appropriate Synonym of the given word.<br>Probe</p>",
                    options_en: [
                        "<p>ignore</p>",
                        "<p>investigate</p>",
                        "<p>reply</p>",
                        "<p>explain</p>"
                    ],
                    options_hi: [
                        "<p>ignore</p>",
                        "<p>investigate</p>",
                        "<p>reply</p>",
                        "<p>explain</p>"
                    ],
                    solution_en: "<p>91.(b) Investigate <br>Investigate- to try to find out all the facts about something <br>Probe- to ask questions in order to find out secret or hidden information <br>Ignore- to pay no attention to somebody/something</p>",
                    solution_hi: "<p>91.(b) Investigate <br>Investigate ( जांच पड़ताल करना ) - to try to find out all the facts about something&nbsp;<br>Probe (छानबीन करना ) - to ask questions in order to find out secret or hidden information&nbsp;<br>Ignore (नज़रअंदाज़ करना ) - to pay no attention to somebody/something</p>\n<p>&nbsp;</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "92",
                    section: "7",
                    question_en: "<p>92. Select the most appropriate option to fill in the blank. <br>The Magic Fountain is renowned for ______ spectacular light and music shows.</p>",
                    question_hi: "<p>92. Select the most appropriate option to fill in the blank. <br>The Magic Fountain is renowned for ______ spectacular light and music shows.</p>",
                    options_en: [
                        "<p>its</p>",
                        "<p>their</p>",
                        "<p>his</p>",
                        "<p>it&rsquo;s</p>"
                    ],
                    options_hi: [
                        "<p>its</p>",
                        "<p>their</p>",
                        "<p>his</p>",
                        "<p>it&rsquo;s</p>"
                    ],
                    solution_en: "<p>92.(a) its <br>&ldquo;It\'s&rdquo; is a contraction and should be used where a sentence normally reads \"it is\", the apostrophe(&lsquo;s) indicates that part of a word has been removed like It\'s raining is a contraction of It is raining. Whereas, &ldquo;Its&rdquo; with no apostrophe(&lsquo;s) is the possessive form of the pronoun &lsquo;it&rsquo; which means belonging to it like the dog hurt its tail. Similarly in the given sentence, spectacular light and music shows belong to the Magic Fountain. Hence, &lsquo;its&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>92.(a) its <br>\"It\'s\" एक contraction है और इसका उपयोग वहां किया जाना चाहिए जहां एक sentence में सामान्य रूप से इसे \"<span style=\"text-decoration: underline;\">it is</span>\" पढ़ा जाता है। <br>apostrophe(&lsquo;s) इंगित करता है कि शब्द का एक हिस्सा हटा दिया गया है जैसे <strong><span style=\"text-decoration: underline;\">It\'s </span>raining, <span style=\"text-decoration: underline;\">It is</span></strong> raining का एक contraction है। जबकि, <strong>\"<span style=\"text-decoration: underline;\">Its</span>\"</strong> बिना <span style=\"text-decoration: underline;\"><strong>apostrophe(&lsquo;s)</strong></span> के &lsquo;<span style=\"text-decoration: underline;\"><strong>it</strong></span>&rsquo;(pronoun) की possessive form है जिसका अर्थ है इससे संबंधित। For example - The dog hurt <span style=\"text-decoration: underline;\"><strong>its </strong></span>tail. इसी तरह दिए गए sentence में, शानदार लाइट और म्यूजिक शो Magic Fountain से संबंधित हैं। अतः \'its\' सर्वाधिक उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "93",
                    section: "7",
                    question_en: "<p>93. Pick a word opposite in meaning to<br>Omit</p>",
                    question_hi: "<p>93. Pick a word opposite in meaning to<br>Omit</p>",
                    options_en: [
                        "<p>exclude</p>",
                        "<p>include</p>",
                        "<p>undertake</p>",
                        "<p>add</p>"
                    ],
                    options_hi: [
                        "<p>exclude</p>",
                        "<p>include</p>",
                        "<p>undertake</p>",
                        "<p>add</p>"
                    ],
                    solution_en: "<p>93.(b) include<br>Omit- leave out or exclude, either intentionally or forgetfully.<br>Include- to contain something as a part of something else.<br>Exclude- deny access to a place, group, or privilege.<br>Undertake- commit oneself to and begin (an enterprise or responsibility); take on.<br>Add- means join (something) to something else so as to increase the size, number, or amount.</p>",
                    solution_hi: "<p>93.(b) include<br>Omit (बाहर कर देना ) - leave out or exclude, either intentionally or forgetfully.<br>Include (शामिल होना ) - to contain something as a part of something else.<br>Exclude ( बाहर निकालना ) - deny access to a place, group, or privilege.<br>Undertake&nbsp; (दायित्व लेना ) - commit oneself to and begin (an enterprise or responsibility); take on.<br>Add (जोड़ना ) - means join (something) to something else so as to increase the size, number, or amount.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "94",
                    section: "7",
                    question_en: "<p>94. Pick a word opposite in meaning to<br><span style=\"text-decoration: underline;\"><strong>Formidable</strong></span> obstacles have to be overcome before success is achieved.</p>",
                    question_hi: "<p>94. Pick a word opposite in meaning to<br><span style=\"text-decoration: underline;\"><strong>Formidable</strong></span> obstacles have to be overcome before success is achieved.</p>",
                    options_en: [
                        "<p>indomitable</p>",
                        "<p>facile</p>",
                        "<p>safe</p>",
                        "<p>invincible</p>"
                    ],
                    options_hi: [
                        "<p>indomitable</p>",
                        "<p>facile</p>",
                        "<p>safe</p>",
                        "<p>invincible</p>"
                    ],
                    solution_en: "<p>94.(c) Safe<br>Formidable - inspiring fear or respect through being impressively large, powerful, intense, or capable.<br>Indomitable - impossible to subdue or defeat.<br>Facile - ignoring the true complexities of an issue; superficial.<br>Safe - protected from or not exposed to danger or risk; not likely to be harmed or lost<br>Invincible - too powerful to be defeated or overcome</p>",
                    solution_hi: "<p>94.(c) Safe<br>Formidable (डरावना ) - inspiring fear or respect through being impressively large, powerful, intense, or capable.<br>Indomitable ( अपराजेय ) - impossible to subdue or defeat.<br>Facile (सहज ) - ignoring the true complexities of an issue; superficial.<br>Safe (सुरक्षित ) - protected from or not exposed to danger or risk; not likely to be harmed or lost<br>Invincible (अपराजेय ) - too powerful to be defeated or overcome.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "95",
                    section: "7",
                    question_en: "95. Select the most appropriate option to fill in the blank.<br />The driver was_______ injured.He died within an hour. ",
                    question_hi: "95. Select the most appropriate option to fill in the blank.<br />The driver was_______ injured.He died within an hour. ",
                    options_en: [
                        " significantly",
                        " fatally",
                        " fatefully",
                        " vitally"
                    ],
                    options_hi: [
                        " significantly",
                        " fatally",
                        " fatefully",
                        " vitally"
                    ],
                    solution_en: "95.(b) Fatally - It means causing or ending in death.",
                    solution_hi: "95.(b) Fatally , इसका अर्थ है मृत्यु का कारण या अंत।",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "96",
                    section: "7",
                    question_en: "<p>96. <strong>Cloze test:-</strong><br>Pollution is destroying nature. The smoke that is (96) ______ from the chimneys of the factories, and from vehicles is the mixture of poisonous gases. Due to environmental pollution, there is (97) ______ in nature, so, many birds, animals, and plants are on the verge of (98) ______ day by day. The only way to (99) ______ environmental pollution is to plant many trees. By planting trees, we can get rid of many harmful poisonous gases. It is the responsibility of everyone to (100) ______ and protect our environment. <br>Select the most appropriate option to fill in the blank no. 96</p>",
                    question_hi: "<p>96. <strong>Cloze test:-</strong><br>Pollution is destroying nature. The smoke that is (96) ______ from the chimneys of the factories, and from vehicles is the mixture of poisonous gases. Due to environmental pollution, there is (97) ______ in nature, so, many birds, animals, and plants are on the verge of (98) ______ day by day. The only way to (99) ______ environmental pollution is to plant many trees. By planting trees, we can get rid of many harmful poisonous gases. It is the responsibility of everyone to (100) ______ and protect our environment.<br>Select the most appropriate option to fill in the blank no. 96</p>",
                    options_en: [
                        "<p>generated</p>",
                        "<p>absorbed</p>",
                        "<p>created</p>",
                        "<p>emitted</p>"
                    ],
                    options_hi: [
                        "<p>generated</p>",
                        "<p>absorbed</p>",
                        "<p>created</p>",
                        "<p>emitted</p>"
                    ],
                    solution_en: "<p>96.(d) &lsquo;Emit&rsquo; means to send out something, for example, a smell, a sound, smoke, heat or light. The given passage talks about the smoke that is emitted from the chimneys of the factories. Hence, &lsquo;emitted&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>96.(d) \'Emit\' का अर्थ है कुछ प्रसारित करना या छोड़ना, उदाहरण के लिए, गंध, ध्वनि, धुआं, गर्मी या प्रकाश। दिया गया गद्यांश कारखानों की चिमनियों से निकलने वाले धुएँ के बारे में बात करता है। इसलिए, \'emit\' सबसे उपयुक्त उत्तर है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "97",
                    section: "7",
                    question_en: "<p>97. <strong>Cloze test:-</strong><br>Pollution is destroying nature. The smoke that is (96) ______ from the chimneys of the factories, and from vehicles is the mixture of poisonous gases. Due to environmental pollution, there is (97) ______ in nature, so, many birds, animals, and plants are on the verge of (98) ______ day by day. The only way to (99) ______ environmental pollution is to plant many trees. By planting trees, we can get rid of many harmful poisonous gases. It is the responsibility of everyone to (100) ______ and protect our environment.<br>Select the most appropriate option to fill in the blank no. 97</p>",
                    question_hi: "<p>97. <strong>Cloze test:-</strong><br>Pollution is destroying nature. The smoke that is (96) ______ from the chimneys of the factories, and from vehicles is the mixture of poisonous gases. Due to environmental pollution, there is (97) ______ in nature, so, many birds, animals, and plants are on the verge of (98) ______ day by day. The only way to (99) ______ environmental pollution is to plant many trees. By planting trees, we can get rid of many harmful poisonous gases. It is the responsibility of everyone to (100) ______ and protect our environment.<br>Select the most appropriate option to fill in the blank no. 97</p>",
                    options_en: [
                        "<p>an imbalance</p>",
                        "<p>a difference</p>",
                        "<p>an inequality</p>",
                        "<p>a balance</p>"
                    ],
                    options_hi: [
                        "<p>an imbalance</p>",
                        "<p>a difference</p>",
                        "<p>an inequality</p>",
                        "<p>a balance</p>"
                    ],
                    solution_en: "<p>97.(a) &lsquo;Imbalance&rsquo; means a difference or not being equal. The given passage states that there is an imbalance in nature due to environmental pollution. Hence, &lsquo;an imbalance&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>97.(a) \'Imbalance\' का अर्थ है &lsquo;अंतर&rsquo; या &lsquo;ना के बराबर&rsquo; होना। दिए गए गद्यांश में कहा गया है कि पर्यावरण प्रदूषण के कारण प्रकृति में असंतुलन है। इसलिए, \'imbalance\' सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "98",
                    section: "7",
                    question_en: "<p>98. <strong>Cloze test:-</strong><br>Pollution is destroying nature. The smoke that is (96) ______ from the chimneys of the factories, and from vehicles is the mixture of poisonous gases. Due to environmental pollution, there is (97) ______ in nature, so, many birds, animals, and plants are on the verge of (98) ______ day by day. The only way to (99) ______ environmental pollution is to plant many trees. By planting trees, we can get rid of many harmful poisonous gases. It is the responsibility of everyone to (100) ______ and protect our environment.<br>Select the most appropriate option to fill in the blank no. 98</p>",
                    question_hi: "<p>98. <strong>Cloze test:-</strong><br>Pollution is destroying nature. The smoke that is (96) ______ from the chimneys of the factories, and from vehicles is the mixture of poisonous gases. Due to environmental pollution, there is (97) ______ in nature, so, many birds, animals, and plants are on the verge of (98) ______ day by day. The only way to (99) ______ environmental pollution is to plant many trees. By planting trees, we can get rid of many harmful poisonous gases. It is the responsibility of everyone to (100) ______ and protect our environment.<br>Select the most appropriate option to fill in the blank no.98</p>",
                    options_en: [
                        "<p>survival</p>",
                        "<p>distinction</p>",
                        "<p>appearance</p>",
                        "<p>extinction</p>"
                    ],
                    options_hi: [
                        "<p>survival</p>",
                        "<p>distinction</p>",
                        "<p>appearance</p>",
                        "<p>extinction</p>"
                    ],
                    solution_en: "<p>98.(d) &lsquo;Extinction&rsquo; means a situation in which something no longer exists. The given passage states that so, many birds, animals, and plants are on the verge of extinction day by day. Hence, &lsquo;extinction&rsquo; is the most appropriate answer</p>",
                    solution_hi: "<p>98.(d) \'Extinction\' का अर्थ ऐसी स्थिति से है जिसमें कुछ अब मौजूद नहीं है। दिए गए गद्यांश में कहा गया है कि कई पक्षी, जानवर और पौधे दिन-ब-दिन विलुप्त (extinction) होने के कगार पर हैं। इसलिए, \'extinction&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "99",
                    section: "7",
                    question_en: "<p>99. <strong>Cloze test:-</strong><br>Pollution is destroying nature. The smoke that is (96) ______ from the chimneys of the factories, and from vehicles is the mixture of poisonous gases. Due to environmental pollution, there is (97) ______ in nature, so, many birds, animals, and plants are on the verge of (98) ______ day by day. The only way to (99) ______ environmental pollution is to plant many trees. By planting trees, we can get rid of many harmful poisonous gases. It is the responsibility of everyone to (100) ______ and protect our environment.<br>Select the most appropriate option to fill in the blank no. 99</p>",
                    question_hi: "<p>99. <strong>Cloze test:-</strong><br>Pollution is destroying nature. The smoke that is (96) ______ from the chimneys of the factories, and from vehicles is the mixture of poisonous gases. Due to environmental pollution, there is (97) ______ in nature, so, many birds, animals, and plants are on the verge of (98) ______ day by day. The only way to (99) ______ environmental pollution is to plant many trees. By planting trees, we can get rid of many harmful poisonous gases. It is the responsibility of everyone to (100) ______ and protect our environment.<br>Select the most appropriate option to fill in the blank no. 99</p>",
                    options_en: [
                        "<p>eradicate</p>",
                        "<p>sustain</p>",
                        "<p>radiate</p>",
                        "<p>introduce</p>"
                    ],
                    options_hi: [
                        "<p>eradicate</p>",
                        "<p>sustain</p>",
                        "<p>radiate</p>",
                        "<p>introduce</p>"
                    ],
                    solution_en: "<p>99.(a) &lsquo;Eradicate&rsquo; means to destroy or get rid of something completely. The given passage states that the only way to eradicate environmental pollution is to plant many trees. Hence, &lsquo;eradicate&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>99.(a) \'Eradicate\' का अर्थ है किसी चीज को पूरी तरह से नष्ट करना या उससे छुटकारा पाना। दिए गए passage में कहा गया है कि पर्यावरण प्रदूषण को मिटाने का एकमात्र तरीका कई पेड़ लगाना है। इसलिए, \'eradicate\' सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "100",
                    section: "misc",
                    question_en: "<p>100. <strong>Cloze test:-</strong><br>Pollution is destroying nature. The smoke that is (96) ______ from the chimneys of the factories, and from vehicles is the mixture of poisonous gases. Due to environmental pollution, there is (97) ______ in nature, so, many birds, animals, and plants are on the verge of (98) ______ day by day. The only way to (99) ______ environmental pollution is to plant many trees. By planting trees, we can get rid of many harmful poisonous gases. It is the responsibility of everyone to (100) ______ and protect our environment.<br>Select the most appropriate option to fill in the blank no. 100</p>",
                    question_hi: "<p>100.<strong>Cloze test:-</strong><br>Pollution is destroying nature. The smoke that is (96) ______ from the chimneys of the factories, and from vehicles is the mixture of poisonous gases. Due to environmental pollution, there is (97) ______ in nature, so, many birds, animals, and plants are on the verge of (98) ______ day by day. The only way to (99) ______ environmental pollution is to plant many trees. By planting trees, we can get rid of many harmful poisonous gases. It is the responsibility of everyone to (100) ______ and protect our environment.<br>Select the most appropriate option to fill in the blank no. 100</p>",
                    options_en: [
                        "<p>ruin</p>",
                        "<p>preserve</p>",
                        "<p>destroy</p>",
                        "<p>neglect</p>"
                    ],
                    options_hi: [
                        "<p>ruin</p>",
                        "<p>preserve</p>",
                        "<p>destroy</p>",
                        "<p>neglect</p>"
                    ],
                    solution_en: "<p>100.(b) &lsquo;Preserve&rsquo; means to keep something safe or in good condition. The given passage states that it is the responsibility of everyone to preserve and protect our environment. Hence, &lsquo;preserve&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>100.(b) \'Preserve\' का अर्थ है किसी चीज को सुरक्षित या अच्छी स्थिति में रखना। दिए गए passage में कहा गया है कि हमारे पर्यावरण को संरक्षित और सुरक्षित करना सभी की जिम्मेदारी है। इसलिए, \'preserve\' सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'question-box d-flex align-items-center justify-content-center border rounded';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.question-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            const html = `
                <div class="card shadow-sm mb-4">
                    <div class="card-body">
                        <div class="question-text mb-4">
                            <div class="en">${question.question_en}</div>
                            <div class="hi" style="display:none">${question.question_hi}</div>
                        </div>
                        <div class="options">
                            ${['a', 'b', 'c', 'd'].map((opt, i) => `
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="radio" name="q${index}" 
                                        id="opt-${index}-${opt}" value="${opt}" 
                                        ${answers[question.id] === opt ? 'checked' : ''}
                                        onclick="selectAnswer('${question.id}', '${opt}')">
                                    <label class="form-check-label w-100 py-2 px-3 rounded hover-bg-light" for="opt-${index}-${opt}">
                                        <div class="en">${question.options_en[i]}</div>
                                        <div class="hi" style="display:none">${question.options_hi[i]}</div>
                                    </label>
                                </div>
                            `).join('')}
                        </div>
                        ${submitted ? `
                            <div class="alert ${answers[question.id] === question.correct ? 'alert-success' : 'alert-danger'} mt-3">
                                ${answers[question.id] ? (answers[question.id] === question.correct ? 'Correct!' : 'Incorrect!') : 'Not attempted'}
                                <div class="mt-2">
                                    <strong>Solution:</strong>
                                    <div class="en">${question.solution_en}</div>
                                    <div class="hi" style="display:none">${question.solution_hi}</div>
                                </div>
                            </div>
                        ` : ''}
                    </div>
                </div>
            `;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // If in mobile view, close the menu
            if (window.innerWidth < 768) {
                toggleMenu(false);
            }
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                document.getElementById(`box-${index}`).classList.add('attempted');
            }
        }
        // Navigate to previous question
        function prevQuestion() {
            if (currentQuestion > 0) {
                showQuestion(currentQuestion - 1);
            }
        }
        // Navigate to next question
        function nextQuestion() {
            if (currentQuestion < questions.length - 1) {
                showQuestion(currentQuestion + 1);
            }
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) {
                showQuestion(section.start);
            }
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update button text
            document.getElementById('lang-btn').innerHTML = currentLang === 'en' ? 
                '<i class="fas fa-language"></i> हिंदी' : 
                '<i class="fas fa-language"></i> English';
        }
        // Toggle menu for mobile
        function toggleMenu(show) {
            const nav = document.getElementById('question-nav');
            const content = document.getElementById('question-content');
            if (show === undefined) {
                show = nav.style.right === '-100%' || nav.style.right === '';
            }
            if (show) {
                nav.style.right = '0';
                if (window.innerWidth >= 768) {
                    content.style.marginRight = '320px';
                }
            } else {
                nav.style.right = window.innerWidth < 768 ? '-100%' : '0';
                content.style.marginRight = window.innerWidth < 768 ? '0' : '320px';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
            document.getElementById('total-count').textContent = questions.length;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.remove('attempted');
                    box.classList.add('bg-secondary', 'text-white');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.remove('attempted');
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.remove('attempted');
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            resultsModal.show();
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check-circle me-1"></i> Submitted';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            resultsModal.hide();
            showQuestion(0);
        }
    </script>
</body>
</html>