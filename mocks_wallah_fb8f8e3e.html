<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">20:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 15</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">15</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 20 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 0,
                end: 13
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 14,
                end: 14
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "17",
                    question_en: "<p>1. 10 horses can graze a field in 14 days, and 10 sheep take 28 days to graze the same field. How many days will 5 horses and 10 sheep take to graze the same field?</p>",
                    question_hi: "<p>1. 10 घोड़े एक खेत को 14 दिनों में चर सकते हैं, और 10 भेड़ें उसी खेत को चराने में 28 दिन का समय लेती हैं। 5 घोड़े और 10 भेड़ें एक ही खेत को कितने दिन में चरेंगी?</p>",
                    options_en: ["<p>28</p>", "<p>10</p>", 
                                "<p>14</p>", "<p>20</p>"],
                    options_hi: ["<p>28</p>", "<p>10</p>",
                                "<p>14</p>", "<p>20</p>"],
                    solution_en: "<p>1.(c)<br>10h &times; 14 = 10s &times; 28<br><math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>h</mi><mi>s</mi></mfrac><mo>=</mo><mfrac><mn>2</mn><mn>1</mn></mfrac></math><br>10(2) &times; 14 = [5(2) + 10(1)] &times; d<br>280 = 20 &times; d<br>d = 14</p>",
                    solution_hi: "<p>1.(c)<br>10h &times; 14 = 10s &times; 28<br><math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>h</mi><mi>s</mi></mfrac><mo>=</mo><mfrac><mn>2</mn><mn>1</mn></mfrac></math><br>10(2) &times; 14 = [5(2) + 10(1)] &times; d<br>280 = 20 &times; d<br>d = 14</p>",
                    correct: "c",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "2",
                    section: "17",
                    question_en: "<p>2. 22 boys, working together, can complete a work in 10 days, whereas 25 boys and 12 girls take 8 days to complete the same work. In how many days can only 6 girls, working together complete the same work?</p>",
                    question_hi: "<p>2 22 लड़के एक साथ काम करते हुए एक काम को 10 दिनों में पूरा कर सकते हैं, जबकि 25 लड़के और 12 लड़कियां उसी काम को पूरा करने में 8 दिन का समय लेते हैं। केवल 6 लड़कियां मिलकर उसी कार्य को कितने दिनों में पूरा कर सकती हैं?</p>",
                    options_en: ["<p>170</p>", "<p>177</p>", 
                                "<p>175</p>", "<p>176</p>"],
                    options_hi: ["<p>170</p>", "<p>177</p>",
                                "<p>175</p>", "<p>176</p>"],
                    solution_en: "<p>2.(d)<br>22B &times; 10 = (25B + 12G) &times; 8<br>220B = 200B + 96G<br>20B = 96G<br><math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>B</mi><mi>G</mi></mfrac><mo>=</mo><mfrac><mn>24</mn><mn>5</mn></mfrac></math><br>22(24) &times; 10 = 6(5) &times; d<br>d = 176</p>",
                    solution_hi: "<p>2.(d)<br>22B &times; 10 = (25B + 12G) &times; 8<br>220B = 200B + 96G<br>20B = 96G<br><math style=\"font-family:\'Times New Roman\'\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>B</mi><mi>G</mi></mfrac><mo>=</mo><mfrac><mn>24</mn><mn>5</mn></mfrac></math><br>22(24) &times; 10 = 6(5) &times; d<br>d = 176</p>",
                    correct: "d",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "3",
                    section: "17",
                    question_en: "<p>3. Radha can complete a piece of work in 3 days, while Raji can complete the same work in 2 days. Both of them complete this work by working together and get 150 as total payment for the work done. What is the share of Radha out of the above payment?</p>",
                    question_hi: "<p>3. राधा एक कार्य को 3 दिनों में पूरा कर सकती है, जबकि राजी उसी कार्य को 2 दिनों में पूरा कर सकती है। वे दोनों एक साथ कार्य करके इस कार्य को पूरा करते हैं और किए गए कार्य के लिए कुल भुगतान के रूप में 150 प्राप्त करते हैं। उपरोक्त भुगतान में से राधा का कितना हिस्सा है?</p>",
                    options_en: ["<p>70</p>", "<p>40</p>", 
                                "<p>30</p>", "<p>60</p>"],
                    options_hi: ["<p>70</p>", "<p>40</p>",
                                "<p>30</p>", "<p>60</p>"],
                    solution_en: "<p>3.(d)<br>Ratio of days = 3 : 2<br>Ratio of efficiency = 2 : 3<br>(3 + 2) unit = 150<br>1 unit = 30<br>2 unit = 60<br>Share of Radha = 60</p>",
                    solution_hi: "<p>3.(d)<br>दिनों का अनुपात = 3 : 2<br>दक्षता का अनुपात = 2 : 3<br>(3 + 2) इकाई = 150<br>1 इकाई = 30<br>2 इकाई = 60<br>राधा का हिस्सा = 60</p>",
                    correct: "d",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "4",
                    section: "17",
                    question_en: "<p>4. 18 men can do a piece of work in 20 days. After working for 8 days, they get a notice to complete the work 4 days earlier than originally scheduled. How many more men will be required to be hired to complete the work as per the new schedule?</p>",
                    question_hi: "<p>4. 18 आदमी एक काम को 20 दिनों में कर सकते हैं। 8 दिनों तक काम करने के बाद, उन्हें मूल रूप से निर्धारित समय से 4 दिन पहले काम पूरा करने का नोटिस मिलता है। नए शेड्यूल के अनुसार कार्य को पूरा करने के लिए और कितने पुरुषों को काम पर रखने की आवश्यकता होगी?</p>",
                    options_en: ["<p>9</p>", "<p>12</p>", 
                                "<p>8</p>", "<p>15</p>"],
                    options_hi: ["<p>9</p>", "<p>12</p>",
                                "<p>8</p>", "<p>15</p>"],
                    solution_en: "<p>4.(a)<br>18 &times; 8 + (18 + x) &times; 8 = 18 &times; 20<br>144 + 144 + 8x = 360<br>288 + 8x = 360<br>8x = 72<br>x = 9</p>",
                    solution_hi: "<p>4.(a)<br>18 &times; 8 + (18 + x) &times; 8 = 18 &times; 20<br>144 + 144 + 8x = 360<br>288 + 8x = 360<br>8x = 72<br>x = 9</p>",
                    correct: "a",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "5",
                    section: "17",
                    question_en: "<p>5. A , B and C can do a piece of work in 10, 20, and 30 days, respectively . In how many days can A do the work if he is assisted by B and C on every third day?</p>",
                    question_hi: "<p>5. A, B और C एक कार्य को क्रमशः 10, 20 और 30 दिनों में कर सकते हैं। यदि प्रत्येक तीसरे दिन B और C उसकी सहायता करते हैं, तो A कितने दिनों में कार्य कर सकता है?</p>",
                    options_en: ["<p>9 days</p>", "<p>8<math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>11</mn></mrow></mfrac></math> days</p>", 
                                "<p>8<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>11</mn></mrow></mfrac></math> days</p>", "<p>6<math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>11</mn></mrow></mfrac></math>days</p>"],
                    options_hi: ["<p>9 दिन</p>", "<p>8<math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>11</mn></mrow></mfrac></math> दिन</p>",
                                "<p>8<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>11</mn></mrow></mfrac></math> दिन</p>", "<p>6<math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>11</mn></mrow></mfrac></math>दिन</p>"],
                    solution_en: "<p>5.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1745658963309.png\" alt=\"rId5\" width=\"211\" height=\"165\"><br>1st day + 2nd day + 3rd day = 6 + 6 + 11<br>In 3 days 23 units of work completed<br>In next 3 days total work completed = 23 + 23 = 46<br>In 7th day = 46 + 6 = 52<br>In 8th day = 52 + 6 = 58<br>Now finally remaining work completes in <math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>11</mn></mrow></mfrac></math> part of the 9th day.<br>Total days = 3 + 3 + 1 + 1 + <math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>11</mn></mrow></mfrac></math> = 8<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>11</mn></mrow></mfrac></math> days</p>",
                    solution_hi: "<p>5.(c)<br><strong id=\"docs-internal-guid-ed0f5950-7fff-3b7c-9db2-47a87cce8570\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXeGpRH6Eez93IIMaDCKRZNlfTqT6QXyLJZJRS5s0NeieTnAYYen8bLGfkIuO38bc7lxNlxAPN7E3AbwH5wT_yzVNxxamPgcWhz42xrXhzR3smpNCxTPYByxD2wzdQWOijcySaBy9A?key=XwhxvHvyD_AtTEVTkFPC5LQo\" width=\"176\" height=\"140\"></strong><br>पहला दिन + दूसरा दिन + तीसरा दिन = 6 + 6 + 11<br>3 दिनों में 23 इकाई काम पूरा हुआ<br>अगले 3 दिनों में पूरा किया गया कुल कार्य = 23 + 23 = 46<br>7वें दिन = 46 + 6 = 52<br>8वें दिन = 52 + 6 = 58<br>अब अंत में शेष कार्य 9वें दिन के <math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>11</mn></mrow></mfrac></math>भाग में पूरा होता है।<br>कुल दिन = 3 + 3 + 1 + 1 + <math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>11</mn></mrow></mfrac></math> = 8<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>11</mn></mrow></mfrac></math> दिन</p>",
                    correct: "c",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "6",
                    section: "17",
                    question_en: "<p>6. 40 people were employed who could together make 60 toys in 8 hours. But 8 people left just before work was to begin. How many toys could then be made in 12 hours?</p>",
                    question_hi: "<p>6. 40 लोग कार्यरत थे जो एक साथ 8 घंटे में 60 खिलौने बना सकते थे। लेकिन काम शुरू होने से ठीक पहले 8 लोग निकल गए। फिर 12 घंटे में कितने खिलौने बनाए जा सकते थे?</p>",
                    options_en: ["<p>70</p>", "<p>52</p>", 
                                "<p>60</p>", "<p>72</p>"],
                    options_hi: ["<p>70</p>", "<p>52</p>",
                                "<p>60</p>", "<p>72</p>"],
                    solution_en: "<p>6.(d)<br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>m</mi><mi>a</mi><mi>n</mi><mn>1</mn><mo>&#215;</mo><mi>d</mi><mi>a</mi><mi>y</mi><mi>s</mi><mn>1</mn><mo>&#215;</mo><mi>h</mi><mi>o</mi><mi>u</mi><mi>r</mi><mi>s</mi><mn>1</mn></mrow><mrow><mi>w</mi><mi>o</mi><mi>r</mi><mi>k</mi><mn>1</mn></mrow></mfrac><mo>=</mo><mfrac><mrow><mi>m</mi><mi>a</mi><mi>n</mi><mn>2</mn><mo>&#215;</mo><mi>d</mi><mi>a</mi><mi>y</mi><mi>s</mi><mn>2</mn><mo>&#215;</mo><mi>h</mi><mi>o</mi><mi>u</mi><mi>r</mi><mi>s</mi><mn>1</mn></mrow><mrow><mi>w</mi><mi>o</mi><mi>r</mi><mi>k</mi><mn>2</mn></mrow></mfrac></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>40</mn><mo>&#215;</mo><mn>8</mn></mrow><mn>60</mn></mfrac><mo>=</mo><mfrac><mrow><mn>32</mn><mo>&#215;</mo><mn>12</mn></mrow><mi>x</mi></mfrac></math><br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>x</mi><mo>=</mo><mfrac><mrow><mn>32</mn><mo>&#215;</mo><mn>12</mn><mo>&#215;</mo><mn>60</mn></mrow><mrow><mn>40</mn><mo>&#215;</mo><mn>8</mn></mrow></mfrac></math> = 72</p>",
                    solution_hi: "<p>6.(d)<br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>m</mi><mi>a</mi><mi>n</mi><mn>1</mn><mo>&#215;</mo><mi>d</mi><mi>a</mi><mi>y</mi><mi>s</mi><mn>1</mn><mo>&#215;</mo><mi>h</mi><mi>o</mi><mi>u</mi><mi>r</mi><mi>s</mi><mn>1</mn></mrow><mrow><mi>w</mi><mi>o</mi><mi>r</mi><mi>k</mi><mn>1</mn></mrow></mfrac><mo>=</mo><mfrac><mrow><mi>m</mi><mi>a</mi><mi>n</mi><mn>2</mn><mo>&#215;</mo><mi>d</mi><mi>a</mi><mi>y</mi><mi>s</mi><mn>2</mn><mo>&#215;</mo><mi>h</mi><mi>o</mi><mi>u</mi><mi>r</mi><mi>s</mi><mn>1</mn></mrow><mrow><mi>w</mi><mi>o</mi><mi>r</mi><mi>k</mi><mn>2</mn></mrow></mfrac></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>40</mn><mo>&#215;</mo><mn>8</mn></mrow><mn>60</mn></mfrac><mo>=</mo><mfrac><mrow><mn>32</mn><mo>&#215;</mo><mn>12</mn></mrow><mi>x</mi></mfrac></math><br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>x</mi><mo>=</mo><mfrac><mrow><mn>32</mn><mo>&#215;</mo><mn>12</mn><mo>&#215;</mo><mn>60</mn></mrow><mrow><mn>40</mn><mo>&#215;</mo><mn>8</mn></mrow></mfrac></math> = 72</p>",
                    correct: "d",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "7",
                    section: "17",
                    question_en: "<p>7. S1 and S2 can do a piece of work together in 18 days, S2 and S3 can do the same work together in 27 days, while S3 and S1 can do it together in 54 days. In how many days can all 3, working together, do 50% of the work?</p>",
                    question_hi: "<p>7. S1 और S2 एक साथ एक कार्य को 18 दिनों में कर सकते हैं, S2 और S3 एक साथ समान कार्य को 27 दिनों में कर सकते हैं, जबकि S3 और S1 मिलकर इसे 54 दिनों में कर सकते हैं। सभी 3, एक साथ कार्य करते हुए, कार्य का 50% कितने दिनों में कर सकते हैं?</p>",
                    options_en: ["<p>8 days</p>", "<p>6 days</p>", 
                                "<p>7 days</p>", "<p>9 days</p>"],
                    options_hi: ["<p>8 दिन</p>", "<p>6 दिन</p>",
                                "<p>7 दिन</p>", "<p>9 दिन</p>"],
                    solution_en: "<p>7.(d)<br>Let the total work (L.C.M. of 18,27 and 54)= 108 unit<br>Efficiency of S1 + S2 = <math display=\"inline\"><mfrac><mrow><mn>108</mn></mrow><mrow><mn>18</mn></mrow></mfrac></math> = 6<br>Efficiency of S2 + S3 = <math display=\"inline\"><mfrac><mrow><mn>108</mn></mrow><mrow><mn>27</mn></mrow></mfrac></math> = 4<br>Efficiency of S3 + S1 = <math display=\"inline\"><mfrac><mrow><mn>108</mn></mrow><mrow><mn>54</mn></mrow></mfrac></math> = 2<br>Efficiency of (S1 + S2 + S3) = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>6</mn><mo>+</mo><mn>4</mn><mo>+</mo><mn>2</mn></mrow><mn>2</mn></mfrac></math> = 6<br>Total time to complete 50% of the work = <math display=\"inline\"><mfrac><mrow><mn>54</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math> = 9 days</p>",
                    solution_hi: "<p>7.(d)<br>माना कुल कार्य (18,27 और 54 का L.C.M.)= 108 इकाई<br>S1+ S2 की क्षमता = <math display=\"inline\"><mfrac><mrow><mn>108</mn></mrow><mrow><mn>18</mn></mrow></mfrac></math> = 6<br>S2 + S3 की क्षमता = <math display=\"inline\"><mfrac><mrow><mn>108</mn></mrow><mrow><mn>27</mn></mrow></mfrac></math> = 4<br>S3 + S1 की क्षमता = <math display=\"inline\"><mfrac><mrow><mn>108</mn></mrow><mrow><mn>54</mn></mrow></mfrac></math> = 2<br>(S1+ S2+ S3) की क्षमता = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>6</mn><mo>+</mo><mn>4</mn><mo>+</mo><mn>2</mn></mrow><mn>2</mn></mfrac></math> = 6<br>50% कार्य पूरा करने में लगा कुल समय = <math display=\"inline\"><mfrac><mrow><mn>54</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math> = 9 दिन</p>",
                    correct: "d",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "8",
                    section: "17",
                    question_en: "<p>8. A camp of soldiers has food for 200 days. After 20 days, 100 more soldiers join the camp and the food now can last for 80 days. What was the initial number of soldiers in the camp?</p>",
                    question_hi: "<p>8. सैनिकों के एक शिविर में 200 दिनों का भोजन होता है। 20 दिनों के बाद 100 और सैनिक शिविर में शामिल होते हैं और भोजन अब 80 दिनों तक चल सकता है। शिविर में सैनिकों की प्रारंभिक संख्या कितनी थी?</p>",
                    options_en: ["<p>140</p>", "<p>100</p>", 
                                "<p>120</p>", "<p>80</p>"],
                    options_hi: ["<p>140</p>", "<p>100</p>",
                                "<p>120</p>", "<p>80</p>"],
                    solution_en: "<p>8.(d)<br>Let the initial number of soldier = n<br>n &times; 200 = n &times; 20 + (n + 100) &times; 80<br>200n = 20n + 80n + 8000<br>100n = 8000<br>N = 80</p>",
                    solution_hi: "<p>8.(d)<br>माना सैनिक की प्रारंभिक संख्या = n<br>n &times; 200 = n &times; 20 + (n + 100) &times; 80<br>200n = 20n + 80n + 8000<br>100n = 8000<br>N = 80</p>",
                    correct: "d",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "9",
                    section: "17",
                    question_en: "<p>9. <math display=\"inline\"><msub><mrow><mi>G</mi></mrow><mrow><mn>1</mn></mrow></msub></math> can do a piece of work alone in 20 days, G<sub>2</sub> can do the same piece of work alone in 40 days while G3 can do it alone in 60 days. They worked together and completed the work. If the combined share received by G<sub>1</sub> and G<sub>2</sub> is ₹2,700 then what is the total amount received by all the 3 workers taken together ?</p>",
                    question_hi: "<p>9. <math display=\"inline\"><msub><mrow><mi>G</mi></mrow><mrow><mn>1</mn></mrow></msub></math> अकेले काम को 20 दिनों में कर सकता है, G<sub>2</sub> अकेले उसी काम को 40 दिनों में कर सकता है जबकि G<sub>3</sub> अकेले उसी काम को 60 दिनों में कर सकता है। उन्होंने एक साथ काम किया और काम पूरा किया। यदि G<sub>1</sub> और G<sub>2</sub> द्वारा प्राप्त संयुक्त हिस्सा ₹2,700 है, तो सभी 3 श्रमिकों को मिलाकर प्राप्त कुल राशि कितनी है?</p>",
                    options_en: ["<p>₹3,300</p>", "<p>₹3,000</p>", 
                                "<p>₹3,900</p>", "<p>₹3,600</p>"],
                    options_hi: ["<p>₹3,300</p>", "<p>₹3,000</p>",
                                "<p>₹3,900</p>", "<p>₹3,600</p>"],
                    solution_en: "<p>9.(a)<br>Let the total work = 120 unit<br>Efficiency of G<sub>1</sub> = <math display=\"inline\"><mfrac><mrow><mn>120</mn></mrow><mrow><mn>20</mn></mrow></mfrac></math> = 6<br>Efficiency of G<sub>2</sub> = <math display=\"inline\"><mfrac><mrow><mn>120</mn></mrow><mrow><mn>40</mn></mrow></mfrac></math> = 3<br>Efficiency of G<sub>1</sub> = <math display=\"inline\"><mfrac><mrow><mn>120</mn></mrow><mrow><mn>60</mn></mrow></mfrac></math> = 2<br>(6 + 3) = 2700<br>(6 + 3 + 2) = 3300</p>",
                    solution_hi: "<p>9.(a)<br>माना कुल कार्य = 120 इकाई<br>G<sub>1</sub> की क्षमता =<math display=\"inline\"><mfrac><mrow><mn>120</mn></mrow><mrow><mn>20</mn></mrow></mfrac></math> = 6<br>G<sub>2</sub> की क्षमता =<math display=\"inline\"><mfrac><mrow><mn>120</mn></mrow><mrow><mn>40</mn></mrow></mfrac></math> = 3<br>G<sub>1</sub> की क्षमता = <math display=\"inline\"><mfrac><mrow><mn>120</mn></mrow><mrow><mn>60</mn></mrow></mfrac></math> = 2<br>(6 + 3) = 2700<br>(6 + 3 + 2) = 3300</p>",
                    correct: "a",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "10",
                    section: "17",
                    question_en: "<p>10. Kajal can do a piece of work alone in 24 days, while Neha can do the same work in 40 days. Kajal started the work alone, and then after 12 days, Neha joined her till the completion of the work. How long did the work take to be completed since kajal started to work on it?</p>",
                    question_hi: "<p>10. काजल एक काम को अकेले 24 दिनों में कर सकती है, जबकि नेहा उसी काम को 40 दिनों में कर सकती है। काजल ने अकेले काम शुरू किया, और फिर 12 दिनों के बाद, नेहा काम पूरा होने तक उसके साथ जुड़ गई। काजल ने उस पर काम करना शुरू करने के बाद से काम पूरा होने में कितना समय लिया?</p>",
                    options_en: ["<p><math display=\"inline\"><mfrac><mrow><mn>37</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> days</p>", "<p><math display=\"inline\"><mfrac><mrow><mn>39</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> days</p>", 
                                "<p><math display=\"inline\"><mfrac><mrow><mn>37</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> days</p>", "<p><math display=\"inline\"><mfrac><mrow><mn>39</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> days</p>"],
                    options_hi: ["<p><math display=\"inline\"><mfrac><mrow><mn>37</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> दिन</p>", "<p><math display=\"inline\"><mfrac><mrow><mn>39</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> दिन</p>",
                                "<p><math display=\"inline\"><mfrac><mrow><mn>37</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> दिन</p>", "<p><math display=\"inline\"><mfrac><mrow><mn>39</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> दिन</p>"],
                    solution_en: "<p>10.(b)<br>Let the total work = 120 unit<br>Efficiency of Kajal = <math display=\"inline\"><mfrac><mrow><mn>120</mn></mrow><mrow><mn>24</mn></mrow></mfrac></math> = 5<br>Efficiency of Neha = <math display=\"inline\"><mfrac><mrow><mn>120</mn></mrow><mrow><mn>40</mn></mrow></mfrac></math> = 3<br>Work done in 12 days = 5 &times; 12 = 60 unit<br>Remaining work = 120 - 60 = 60 unit<br>Time = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>60</mn><mrow><mn>5</mn><mo>+</mo><mn>3</mn></mrow></mfrac></math> = 7.5 days<br>Total days = 12 + 7.5 = 19.5 days or <math display=\"inline\"><mfrac><mrow><mn>39</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> days</p>",
                    solution_hi: "<p>10.(b)<br>माना कुल कार्य = 120 इकाई<br>काजल की क्षमता = <math display=\"inline\"><mfrac><mrow><mn>120</mn></mrow><mrow><mn>24</mn></mrow></mfrac></math> = 5<br>नेहा की क्षमता = <math display=\"inline\"><mfrac><mrow><mn>120</mn></mrow><mrow><mn>40</mn></mrow></mfrac></math> = 3<br>12 दिनों में किया गया कार्य = 5 &times; 12 = 60 इकाई<br>शेष कार्य = 120 - 60 = 60 इकाई<br>समय = <math display=\"inline\"><mfrac><mrow><mn>60</mn></mrow><mrow><mn>5</mn><mo>+</mo><mn>3</mn></mrow></mfrac></math> = 7.5 दिन<br>कुल दिन = 12 + 7.5 = 19.5 दिन या <math display=\"inline\"><mfrac><mrow><mn>39</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> दिन</p>",
                    correct: "b",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "11",
                    section: "17",
                    question_en: "<p>11. 12 men can weave 20 shawls in 9 days. How many men will be required to weave 25 shawls in 27 days?</p>",
                    question_hi: "<p>11. 12 आदमी 9 दिनों में 20 शॉल बुन सकते हैं। तो 27 दिनों में 25 शॉल बुनने के लिए कितने पुरुषों की आवश्यकता होगी?</p>",
                    options_en: ["<p>3</p>", "<p>5</p>", 
                                "<p>4</p>", "<p>6</p>"],
                    options_hi: ["<p>3</p>", "<p>5</p>",
                                "<p>4</p>", "<p>6</p>"],
                    solution_en: "<p>11.(b)<br><math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>12</mn><mo>&#215;</mo><mn>9</mn></mrow><mn>20</mn></mfrac><mo>=</mo><mfrac><mrow><mi>n</mi><mo>&#215;</mo><mn>27</mn></mrow><mn>25</mn></mfrac></math><br>n = 5</p>",
                    solution_hi: "<p>11.(b)<br><math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>12</mn><mo>&#215;</mo><mn>9</mn></mrow><mn>20</mn></mfrac><mo>=</mo><mfrac><mrow><mi>n</mi><mo>&#215;</mo><mn>27</mn></mrow><mn>25</mn></mfrac></math><br>n = 5</p>",
                    correct: "b",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "12",
                    section: "17",
                    question_en: "<p>12. 15 men can do a work in 15 days working 8 hours a day. N men can do twice the work in 20 days working 10 hours a day. What is the value of N?</p>",
                    question_hi: "<p>12. 15 आदमी एक दिन में 8 घंटे काम करते हुए 15 दिनों में एक काम कर सकते हैं। N पुरुष दिन में 10 घंटे काम करके 20 दिनों में दो बार काम कर सकते हैं। N का मूल्य क्या है?</p>",
                    options_en: ["<p>14</p>", "<p>12</p>", 
                                "<p>16</p>", "<p>18</p>"],
                    options_hi: ["<p>14</p>", "<p>12</p>",
                                "<p>16</p>", "<p>18</p>"],
                    solution_en: "<p>12.(d)<br><math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>15</mn><mo>&#215;</mo><mn>15</mn><mo>&#215;</mo><mn>8</mn></mrow><mn>1</mn></mfrac><mo>=</mo><mfrac><mrow><mi>N</mi><mo>&#215;</mo><mn>20</mn><mo>&#215;</mo><mn>10</mn></mrow><mn>2</mn></mfrac></math><br>N = 18</p>",
                    solution_hi: "<p>12.(d)<br><math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>15</mn><mo>&#215;</mo><mn>15</mn><mo>&#215;</mo><mn>8</mn></mrow><mn>1</mn></mfrac><mo>=</mo><mfrac><mrow><mi>N</mi><mo>&#215;</mo><mn>20</mn><mo>&#215;</mo><mn>10</mn></mrow><mn>2</mn></mfrac></math><br>N = 18</p>",
                    correct: "d",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "13",
                    section: "17",
                    question_en: "<p>.13. It is given that men are twice as efficient than women in respect to doing work. If three men and two women can complete the work in 2 days, then in how many days can a woman working alone complete the work?</p>",
                    question_hi: "<p>13. यह दिया गया है कि किसी कार्य को करने में पुरुष, महिलाओं से दोगुने कार्य कुशल हैं | यदि तीन पुरुष और दो महिलाओं को किसी कार्य को समाप्त करने में 2 दिन लगते हैं, तो एक महिला अकेले कार्य करते हुए इस कार्य को कितने दिनों में समाप्त कर सकती है?</p>",
                    options_en: ["<p>12<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>", "<p>16</p>", 
                                "<p>10<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>", "<p>8</p>"],
                    options_hi: ["<p>12<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>", "<p>16</p>",
                                "<p>10<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>", "<p>8</p>"],
                    solution_en: "<p>13.(b) Man = 2 <math display=\"inline\"><mo>&#215;</mo></math> Woman<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo><mfrac><mrow><mi>M</mi><mi>a</mi><mi>n</mi></mrow><mrow><mi>W</mi><mi>o</mi><mi>m</mi><mi>a</mi><mi>n</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>1</mn></mfrac></math><br>Total Work = 2<math display=\"inline\"><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi></math>{3(2) + 2(1)} = 16 unit<br>No of days required for a woman = <math display=\"inline\"><mfrac><mrow><mn>16</mn></mrow><mrow><mn>1</mn></mrow></mfrac></math> <br>= 16 days</p>",
                    solution_hi: "<p>13.(b)<br>1 पुरुष = 2 महिलाओं<br><math style=\"font-family:Verdana\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo><mfrac><mi>&#2346;&#2369;&#2352;&#2369;&#2359;</mi><mi>&#2350;&#2361;&#2367;&#2354;&#2366;</mi></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>1</mn></mfrac></math><br>कुल कार्य = 2<math display=\"inline\"><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi></math>{3(2) + 2(1)} = 16 इकाई <br>एक महिला के लिए आवश्यक दिनों की संख्या = <math display=\"inline\"><mfrac><mrow><mn>16</mn></mrow><mrow><mn>1</mn></mrow></mfrac></math> = 16 दिन</p>",
                    correct: "b",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "14",
                    section: "17",
                    question_en: "<p>14. A, B and C, alone can do a piece of work in 9, 12 and 18 days respectively. They all started the work together, but A left after 3 days. In how many days, was the remaining work completed ?</p>",
                    question_hi: "<p>14. A, B और C अकेले किसी कार्य को क्रमशः 9, 12 और 18 दिनों में कर सकते हैं | उन सभी ने साथ कार्य शुरू किया, लेकिन 3 दिन के बाद A ने कार्य छोड़ दिया | शेष कार्य कितने दिनों में समाप्त हुआ ?</p>",
                    options_en: ["<p>2</p>", "<p><math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>", 
                                "<p><math display=\"inline\"><mfrac><mrow><mn>11</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>9</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math></p>"],
                    options_hi: ["<p>2</p>", "<p><math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>",
                                "<p><math display=\"inline\"><mfrac><mrow><mn>11</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>9</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math></p>"],
                    solution_en: "<p>14.(d) <br><strong id=\"docs-internal-guid-7da983a5-7fff-5c6d-690a-ac47a193cbc7\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXe6Z96LsfxSHCvXPNyj9S4lLhDJXqKvBcbFqUJg7qXFc64BTqqkoIfj0V0iwZGz5QyNq9a0bv5qnhrKuWBwTbo59q5vCP-BK2a-RmIyVys5D-AOtN-2IA0zrRd8h5wpjixHl2trPA?key=XwhxvHvyD_AtTEVTkFPC5LQo\" width=\"165\" height=\"127\"></strong><br>Let total work = 72 unit<br>Work done by All three in 3 days = 3(8 + 6 + 4) = 54 unit<br>Remaining work = 72 - 54 = 18 unit<br>Required number of days = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>18</mn><mrow><mn>6</mn><mo>+</mo><mn>4</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>9</mn><mn>5</mn></mfrac></math></p>",
                    solution_hi: "<p>14.(d) <br><strong id=\"docs-internal-guid-7da983a5-7fff-5c6d-690a-ac47a193cbc7\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXe6Z96LsfxSHCvXPNyj9S4lLhDJXqKvBcbFqUJg7qXFc64BTqqkoIfj0V0iwZGz5QyNq9a0bv5qnhrKuWBwTbo59q5vCP-BK2a-RmIyVys5D-AOtN-2IA0zrRd8h5wpjixHl2trPA?key=XwhxvHvyD_AtTEVTkFPC5LQo\" width=\"165\" height=\"127\"></strong><br>माना कुल कार्य = 72 इकाई <br>तीनों द्वारा 3 दिनों में किया गया कार्य =3(8 + 6 + 4) = 54 इकाई <br>शेष कार्य = 72 - 54 = 18 इकाई <br>आवश्यक दिनों की संख्या = <math display=\"inline\"><mi>&#160;</mi><mfrac><mrow><mn>18</mn></mrow><mrow><mn>6</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>4</mn></mrow></mfrac></math>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>9</mn><mn>5</mn></mfrac></math><br><strong>वैकल्पिक विधि :-</strong> <br>A द्वारा 3 दिनों में किया गया कार्य = 8<math display=\"inline\"><mo>&#215;</mo><mn>3</mn></math> <br>= 24 इकाई <br>शेष कार्य = 72 - 24 = 48 इकाई <br>इस कार्य को पूरा करने में B और C द्वारा लिया गया समय =<math display=\"inline\"><mfrac><mrow><mn>48</mn></mrow><mrow><mn>6</mn><mo>+</mo><mn>4</mn></mrow></mfrac></math>= 4<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>5</mn></mfrac></math><br>A को हटाने के बाद B और C द्वारा लिए गए दिनों की संख्या = 4<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>5</mn></mfrac></math> - 3 = 1<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>5</mn></mfrac></math> or <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>9</mn><mn>5</mn></mfrac></math>दिन</p>",
                    correct: "d",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "15",
                    section: "misc",
                    question_en: "<p>15. The ratio of the efficiencies of A, B and C is 2 : 5 : 3. Working together, they can complete a work in 27 days. B and C together can complete 4/9th part of that work in:</p>",
                    question_hi: "<p>15. A, B और C की कार्य क्षमता का अनुपात 2 : 5 : 3 है | एक साथ कार्य करते हुए, वे किसी कार्य को 27 दिन में पूरा कर सकते हैं | B और C एक साथ उस कार्य के 4/9 भाग को कितने दिनों में पूरा करेंगे ? </p>",
                    options_en: ["<p>27 days</p>", "<p>15 days</p>", 
                                "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math>17days</p>", "<p>24 days</p>"],
                    options_hi: ["<p>27 दिन</p>", "<p>15 दिन</p>",
                                "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math>17दिन</p>", "<p>24 दिन</p>"],
                    solution_en: "<p>15.(b) Let the efficiency of A, B and C are 2 unit , 5 unit and 3 unit respectively.<br>Total work = 27<math display=\"inline\"><mi>&#160;</mi><mo>&#215;</mo><mo>(</mo><mn>2</mn><mo>+</mo><mn>5</mn><mo>+</mo><mn>3</mn><mo>)</mo></math>= 270 unit<br>Time taken by B and C to complete <math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>9</mn></mrow></mfrac></math>th part of that work =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>270</mn><mo>&#215;</mo><mstyle displaystyle=\"true\"><mfrac><mn>4</mn><mn>9</mn></mfrac></mstyle></mrow><mrow><mn>5</mn><mo>+</mo><mn>3</mn></mrow></mfrac></math> = 15 days</p>",
                    solution_hi: "<p>15.(b) <br>माना , A, B और C की दक्षता क्रमशः 2 इकाई , 5 इकाई और 3 इकाई है।<br>कुल कार्य = 27<math display=\"inline\"><mi>&#160;</mi><mo>&#215;</mo><mo>(</mo><mn>2</mn><mo>+</mo><mn>5</mn><mo>+</mo><mn>3</mn><mo>)</mo></math>= 270 इकाई <br>उस कार्य का <math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>9</mn></mrow></mfrac></math> भाग पूरा करने में B और C द्वारा लिया गया समय = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>270</mn><mo>&#215;</mo><mstyle displaystyle=\"true\"><mfrac><mn>4</mn><mn>9</mn></mfrac></mstyle></mrow><mrow><mn>5</mn><mo>+</mo><mn>3</mn></mrow></mfrac></math>= 15 दिन</p>",
                    correct: "b",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>