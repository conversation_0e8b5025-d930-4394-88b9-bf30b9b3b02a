<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">30:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 25</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">25</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 30 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["6"] = {
                name: "Reasoning",
                start: 0,
                end: 23
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="6">Reasoning</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 24,
                end: 24
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "6",
                    question_en: "<p>1. 1200 apples were distributed among a group of boys. Each boy got thrice many of the apples as the number of boys in the group. The number of boys in the group was:</p>",
                    question_hi: "<p>1. 1200 सेब लड़कों के एक समूह में बांटे गए। प्रत्येक लड़के को समूह में लड़कों की संख्या से तीन गुना अधिक सेब मिले। समूह में लड़कों की संख्या कितनी थी ?</p>",
                    options_en: ["<p>20</p>", "<p>15</p>", 
                                "<p>25</p>", "<p>40</p>"],
                    options_hi: ["<p>20</p>", "<p>15</p>",
                                "<p>25</p>", "<p>40</p>"],
                    solution_en: "<p>1.(a)<br>Let the number of boys = x<br>Each boy got apple = 3x</p>\n<p dir=\"ltr\">&nbsp;</p>\n<p dir=\"ltr\">A/Q,&nbsp;</p>\n<p dir=\"ltr\">x &times; 3x = 1200</p>\n<p dir=\"ltr\">&rArr; x<sup>2 </sup>= 400</p>\n<p dir=\"ltr\">&rArr; x = 20</p>\n<p dir=\"ltr\">&nbsp;</p>",
                    solution_hi: "<p>1.(a)<br>माना लड़कों की संख्या = x<br>प्रत्येक लड़के को सेब मिला = 3x<br>ए / क्यू,<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>x</mi><mo>&#215;</mo><mn>3</mn><mi>x</mi><mo>=</mo><mn>1200</mn></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo><msup><mi>x</mi><mn>2</mn></msup><mo>=</mo><mi>&#160;</mi><mn>400</mn></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo><mi>x</mi><mi>&#160;</mi><mi>&#160;</mi><mo>=</mo><mi>&#160;</mi><mn>20</mn></math></p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "2",
                    section: "6",
                    question_en: "<p>2. If PUNJAB is coded as OSKKCE, then how will PRAYAG be coded ?</p>",
                    question_hi: "<p>2. यदि पंजाब को OSKKCE के रूप में कोडित किया जाता है, तो PRAYAG को कैसे कोडित किया जाएगा</p>",
                    options_en: ["<p>POXJCZ</p>", "<p>OPXJCZ</p>", 
                                "<p>POXZCJ</p>", "<p>OPXZCJ</p>"],
                    options_hi: ["<p>POXJCZ</p>", "<p>OPXJCZ</p>",
                                "<p>POXZCJ</p>", "<p>OPXZCJ</p>"],
                    solution_en: "<p>2.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728976662759.png\" alt=\"rId5\" width=\"251\" height=\"57\"></p>",
                    solution_hi: "<p>2.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728976662759.png\" alt=\"rId5\" width=\"251\" height=\"57\"></p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "3",
                    section: "6",
                    question_en: "<p>3. If the proportion &ldquo;All men are rich&rdquo; is false, then which of the following propositions can be claimed certainly to be true ?</p>",
                    question_hi: "<p>3. यदि अनुपात \"सभी पुरुष अमीर हैं\" गलत है, तो निम्नलिखित में से कौन सा प्रस्ताव निश्चित रूप से सत्य होने का दावा किया जा सकता है ?</p>",
                    options_en: ["<p>Some men are not rich</p>", "<p>No rich person is a man</p>", 
                                "<p>No man is rich</p>", "<p>Some men are rich</p>"],
                    options_hi: ["<p>कुछ पुरुष अमीर नहीं हैं</p>", "<p>कोई अमीर व्यक्ति एक आदमी है</p>",
                                "<p>कोई आदमी अमीर नहीं है</p>", "<p>कुछ आदमी अमीर हैं</p>"],
                    solution_en: "<p>3.(a)<br>&ldquo;All men are rich&rdquo; is false,<br>From the above sentence it is clear that all men are not rich that means some men are not rich.</p>",
                    solution_hi: "<p>3.(a)<br>\"सभी आदमी अमीर हैं\" झूठा है,<br>उपरोक्त वाक्य से यह स्पष्ट है कि सभी पुरुष धनी नहीं होते हैं अर्थात कुछ पुरुष धनी नहीं होते हैं।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "4",
                    section: "6",
                    question_en: "<p>4. Select the option that is related to the third term in the same way as the second term is related to the first term. <br>Uttar Pradesh : Lucknow : : Madhya Pradesh : ?</p>",
                    question_hi: "<p>4. उस विकल्प का चयन कीजिये जो तीसरे पद से उसी प्रकार संबंधित है जैसे दूसरा पद पहले पद से संबंधित है<br>उत्तर प्रदेश : लखनऊ : : मध्य प्रदेश : ?</p>",
                    options_en: ["<p>Bhopal</p>", "<p>Gwalior</p>", 
                                "<p>Indore</p>", "<p>Jabalpur</p>"],
                    options_hi: ["<p>भोपाल</p>", "<p>ग्वालियर</p>",
                                "<p>इंदौर</p>", "<p>जबलपुर</p>"],
                    solution_en: "<p>4.(a)<br>Lucknow is the capital of UP and Bhopal is the capital of MP.</p>",
                    solution_hi: "<p>4.(a)<br>लखनऊ उत्तर प्रदेश की राजधानी है और भोपाल मध्य प्रदेश की राजधानी है।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "5",
                    section: "6",
                    question_en: "<p>5. Select the option that is related to the fourth term in the same way as the third term is related to the first term ?<br>Shirt : Trouser : ? : Table</p>",
                    question_hi: "<p>5. उस विकल्प का चयन कीजिये जो चौथे पद से उसी प्रकार संबंधित है जैसे तीसरा पद पहले पद से संबंधित है ?<br>कमीज : पतलून : ? : मेज़</p>",
                    options_en: ["<p>Bed</p>", "<p>Chair</p>", 
                                "<p>Almirah</p>", "<p>Furniture</p>"],
                    options_hi: ["<p>बिस्तर</p>", "<p>कुर्सी</p>",
                                "<p>अलमारी</p>", "<p>फर्नीचर</p>"],
                    solution_en: "<p>5.(b)<br>As shirts are related to the trouser, in the same way Table is related to the Chair.</p>",
                    solution_hi: "<p>5.(b)<br>जिस प्रकार कमीज का सम्बन्ध पतलून से है, उसी प्रकार मेज का सम्बन्ध कुर्सी से है।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "6",
                    section: "6",
                    question_en: "<p>6. Select the number that can replace the question mark (?) in the following series: <br>2, 10, 30, 68 ,? , 222</p>",
                    question_hi: "<p>6.उस संख्या का चयन करें जो निम्नलिखित श्रृंखला में प्रश्नवाचक चिह्न (?) को प्रतिस्थापित कर सकती है:<br>2, 10, 30, 68 ,? , 222</p>",
                    options_en: ["<p>103</p>", "<p>120</p>", 
                                "<p>130</p>", "<p>110</p>"],
                    options_hi: ["<p>103</p>", "<p>120</p>",
                                "<p>130</p>", "<p>110</p>"],
                    solution_en: "<p>6.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728976662951.png\" alt=\"rId6\" width=\"198\" height=\"102\"></p>",
                    solution_hi: "<p>6.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728976662951.png\" alt=\"rId6\" width=\"198\" height=\"102\"></p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "7",
                    section: "6",
                    question_en: "<p>7. Four letter-clusters have been given out of which three are alike in some manner and one is different. Select the odd one.</p>",
                    question_hi: "<p>7. चार अक्षर-समूह दिए गए हैं जिनमें से तीन किसी न किसी रूप में एक जैसे हैं और एक भिन्न है। विजातीय का चयन कीजिये ?</p>",
                    options_en: ["<p>OQS</p>", "<p>JLN</p>", 
                                "<p>UWY</p>", "<p>ADG</p>"],
                    options_hi: ["<p>OQS</p>", "<p>JLN</p>",
                                "<p>UWY</p>", "<p>ADG</p>"],
                    solution_en: "<p>7.(d)<br>Logic :- Previous alphabet + 2 = next alphabet.</p>",
                    solution_hi: "<p>7.(d)<br>तर्क :- पिछला अक्षर + 2 = अगला अक्षर।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "8",
                    section: "6",
                    question_en: "<p>8. Select the term that can replace the question mark (?) in the following series. <br>AC, CF, EJ, GO, ?</p>",
                    question_hi: "<p>8. उस पद का चयन करें जो निम्नलिखित श्रृंखला में प्रश्नवाचक चिन्ह (?) को प्रतिस्थापित कर सकता है<br>AC, CF, EJ, GO, ?</p>",
                    options_en: ["<p>IU</p>", "<p>JV</p>", 
                                "<p>IV</p>", "<p>JU</p>"],
                    options_hi: ["<p>IU</p>", "<p>JV</p>",
                                "<p>IV</p>", "<p>JU</p>"],
                    solution_en: "<p>8.(a)<br>1st letter :- A + 2 = C, C + 2 = E, E + 2 = G, G + 2 = I<br>2nd letter :- C + 3 = F, F + 4 = J, J + 5 = O, O + 6 = U<br>Next term = IU</p>",
                    solution_hi: "<p>8.(a)<br>पहला अक्षर :- A + 2 = C, C + 2 = E, E + 2 = G, G + 2 = I<br>दूसरा अक्षर :- C + 3 = F, F + 4 = J, J + 5 = O, O + 6 = U<br>अगला अक्षर = IU</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "9",
                    section: "6",
                    question_en: "<p>9. If APPLE is coded as 25563 and RUNG is coded 7148, then PURPLE will be coded as:</p>",
                    question_hi: "<p>9. यदि APPLE को 25563 और RUNG को 7148 के रूप में कोडित किया जाता है, तो PURPLE को इस प्रकार कोडित किया जाएगा</p>",
                    options_en: ["<p>617563</p>", "<p>617553</p>", 
                                "<p>517563</p>", "<p>517653</p>"],
                    options_hi: ["<p>617563</p>", "<p>617553</p>",
                                "<p>517563</p>", "<p>517653</p>"],
                    solution_en: "<p>9.(c)<br>From the given examples we can observe that the <br>code of P = 5, U = 1, R = 7, L = 6, E = 3<br>Code for PURPLE = 517563</p>",
                    solution_hi: "<p>9.(c)<br>दिए गए उदाहरणों से हम देख सकते हैं कि:<br>P = 5, U = 1, R = 7, L = 6, E = 3 का कोड है। <br>PURPLE के लिए कोड = 517563</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "10",
                    section: "6",
                    question_en: "<p>10. In a certain code, ROAR is written as URDU. How will URDU be written in that code ?</p>",
                    question_hi: "<p>10. एक निश्चित कूट भाषा में ROAR को URDU लिखा जाता है। उसी कोड में URDU को कैसे लिखा जाएगा ?</p>",
                    options_en: ["<p>ROAR</p>", "<p>XUGX</p>", 
                                "<p>VXDQ</p>", "<p>VSOV</p>"],
                    options_hi: ["<p>ROAR</p>", "<p>XUGX</p>",
                                "<p>VXDQ</p>", "<p>VSOV</p>"],
                    solution_en: "<p>10.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728976663348.png\" alt=\"rId7\" width=\"260\" height=\"55\"></p>",
                    solution_hi: "<p>10.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728976663348.png\" alt=\"rId7\" width=\"260\" height=\"55\"></p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "11",
                    section: "6",
                    question_en: "<p>11. Avinash is a friend of Harsh. Pointing to an old man, Avinash asked Harsh, &ldquo;Who is he&rdquo;? Harsh replied, &ldquo;His son is my son&rsquo;s parental uncle&rdquo;. How is the old man related to Harsh ?</p>",
                    question_hi: "<p>11. अविनाश हर्ष का मित्र है। एक बूढ़े आदमी की ओर इशारा करते हुए अविनाश ने हर्ष से पूछा, \"वह कौन है\"? हर्ष ने उत्तर दिया, \"उसका पुत्र मेरे पुत्र का चाचा है\"। वृद्ध का हर्ष से क्या संबंध है ?</p>",
                    options_en: ["<p>Father</p>", "<p>Father&rsquo;s Father</p>", 
                                "<p>Brother</p>", "<p>Father&rsquo;s brother</p>"],
                    options_hi: ["<p>पिता</p>", "<p>पिता के पिता</p>",
                                "<p>भाई</p>", "<p>पिता का भाई</p>"],
                    solution_en: "<p>11.(a) Father<br><strong id=\"docs-internal-guid-29a32048-7fff-7bfa-ad99-bfa26a5ae092\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXce7WUa6Qxl5ChycZGqs3GTYnSoEpziHLHMLJz0s_2anlN9yd0814zQsYi6S3DrRkMHxzeYqnQ8DgbqPo8Tj5BNdXbhBt2ZL3aRblRQ-aGlKFunBw4P63nAA4Cq_vXMDUbQ72UOs4nqmOaaPz3WZh4p9I9E?key=sZYSr9k3dKcV3d54Qy-G4g\" width=\"121\" height=\"140\"></strong></p>",
                    solution_hi: "<p>11.(a)<br><strong id=\"docs-internal-guid-746295be-7fff-28ac-edb4-1fbe751e2e2a\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfJOdE642sdAQ2qJ6l1mtyiVUJeQoILgOMMPlwAclfYXfApKHz3MLHw70aLN8HYSI6byYhm1zc7ChPUUQ-hXkZg5kxJQs4H2WX-5RwgH9ohkR5qz-jvTVJv09DIy9jdxmE2J4VETVqJ0Xu-iiBbvkieh8X8?key=sZYSr9k3dKcV3d54Qy-G4g\" width=\"113\" height=\"129\"></strong></p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "12",
                    section: "6",
                    question_en: "<p>12. Seven students A, B, C, D, E, F, and H give a test. No two students get the same marks.A gets more marks than B. H gets more marks than A.Only two students get more marks than C. D gets more marks than C and B. But D does not get the highest marks. <br>If B did not get the lowest marks, then which of the following sequence of their marks is possible ?</p>",
                    question_hi: "<p>12. सात विद्यार्थी A, B, C, D, E, F और H एक परीक्षा देते हैं। किसी भी दो छात्रों को समान अंक नहीं मिलते हैं। A को B से अधिक अंक मिलते हैं। H को A से अधिक अंक मिलते हैं। केवल दो छात्रों को सी से अधिक अंक मिलते हैं। D को C और B से अधिक अंक मिलते हैं। लेकिन D को उच्चतम अंक नहीं मिलते हैं।<br>यदि B को सबसे कम अंक नहीं मिले, तो उनके अंकों का निम्नलिखित में से कौन सा क्रम संभव है ?</p>",
                    options_en: ["<p>H&gt;D&gt;C&gt;B&gt;E&gt;F&gt;A</p>", "<p>F&gt;D&gt;C&gt;E&gt;H&gt;B&gt;A</p>", 
                                "<p>H&gt;D&gt;C&gt;E&gt;A&gt;B&gt;F</p>", "<p>E&gt;C&gt;D&gt;H&gt;A&gt;B&gt;F</p>"],
                    options_hi: ["<p>H&gt;D&gt;C&gt;B&gt;E&gt;F&gt;A</p>", "<p>F&gt;D&gt;C&gt;E&gt;H&gt;B&gt;A</p>",
                                "<p>H&gt;D&gt;C&gt;E&gt;A&gt;B&gt;F</p>", "<p>E&gt;C&gt;D&gt;H&gt;A&gt;B&gt;F</p>"],
                    solution_en: "<p>12.(c)<br>H&gt;D&gt;C&gt;E&gt;A&gt;B&gt;F</p>",
                    solution_hi: "<p>12.(c)<br>H&gt;D&gt;C&gt;E&gt;A&gt;B&gt;F</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "13",
                    section: "6",
                    question_en: "<p>13. Among the following propositions two are related in such a way that one is denial of the other.Which are those propositions ?<br><strong>Propositions :</strong><br>a. All elephants are equal to cats.&nbsp;<br>b. Some elephants are not equal to cats<br>c. Some elephants are equal to cats&nbsp;<br>d. Few elephants are also equal to cats</p>",
                    question_hi: "<p>13. निम्नलिखित प्रस्तावों में से दो इस तरह से संबंधित हैं कि एक दूसरे से इनकार करते है। वे प्रस्ताव कौन से हैं ?<br><strong>प्रस्ताव :</strong><br>a. सभी हाथी बिल्लियों के समान हैं<br>b. कुछ हाथी बिल्लियों के बराबर नहीं होते<br>c. कुछ हाथी बिल्लियों के बराबर हैं<br>d. कुछ हाथी भी बिल्लियों के बराबर होते हैं</p>",
                    options_en: ["<p>a and b</p>", "<p>a and d</p>", 
                                "<p>c and d</p>", "<p>a and c</p>"],
                    options_hi: ["<p>a और b</p>", "<p>a और d</p>",
                                "<p>c और d</p>", "<p>a और c</p>"],
                    solution_en: "<p>13.(a)<br>A is the opposite of b.</p>",
                    solution_hi: "<p>13.(a)<br>A, B के विपरीत है।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "14",
                    section: "6",
                    question_en: "<p>14. In the given figure, rectangle represents &lsquo;male&rsquo; , triangle represents &lsquo;educated&rsquo; circle represents &lsquo;urban&rsquo; and square represents &lsquo;civil servants&rsquo; .What is the count of educated males who hail from urban area ?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728976663667.png\" alt=\"rId9\" width=\"171\" height=\"116\"></p>",
                    question_hi: "<p>14. दी गई आकृति में, आयत \'पुरुष\' का प्रतिनिधित्व करता है, त्रिभुज \'शिक्षित\' का प्रतिनिधित्व करता है वृत्त \'शहरी\' का प्रतिनिधित्व करता है और वर्ग \'सिविल सेवकों\' का प्रतिनिधित्व करता है। शहरी क्षेत्र से आने वाले शिक्षित पुरुषों की संख्या क्या है ?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728976663667.png\" alt=\"rId9\" width=\"171\" height=\"116\"></p>",
                    options_en: ["<p>2</p>", "<p>3</p>", 
                                "<p>6</p>", "<p>4</p>"],
                    options_hi: ["<p>2</p>", "<p>3</p>",
                                "<p>6</p>", "<p>4</p>"],
                    solution_en: "<p>14.(d)<br>From the given diagram it is clear that educated males who are from urban areas are the common region in all triangle, circle and rectangle and is represented by the number 4.</p>",
                    solution_hi: "<p>14.(d)<br>दिए गए आरेख से यह स्पष्ट है कि शिक्षित पुरुष जो शहरी क्षेत्रों से हैं, सभी त्रिभुज, वृत्त और आयत में सामान्य क्षेत्र हैं और संख्या 4 द्वारा दर्शाए गए हैं।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "15",
                    section: "6",
                    question_en: "<p>15. Six persons, M1,M2,M3, M4, M5 and M6 are sitting in a row facing towards the north (not necessarily in the same order). Only three people sit between M2 and M3 . M3 is sitting to the immediate left of M5. Only three people sit between M1 and M5. M4 sits to the immediate left of M6 and is to the right of M3. <br>How many people are sitting between M3 and M6 ?</p>",
                    question_hi: "<p>15. छह व्यक्ति, M1,M2,M3, M4, M5 और M6 एक पंक्ति में उत्तर की ओर मुख करके बैठे हैं (जरूरी नहीं कि इसी क्रम में)। M2 और M3 के बीच केवल तीन व्यक्ति बैठे हैं। M3, M5 के ठीक बायें बैठा है। M1 और M5 के बीच केवल तीन व्यक्ति बैठते हैं। M4, M6 के ठीक बायें बैठा है और M3 के दायीं ओर है।<br>M3 और M6 के मध्य कितने व्यक्ति बैठे हैं ?</p>",
                    options_en: ["<p>One</p>", "<p>Two</p>", 
                                "<p>Zero</p>", "<p>Three</p>"],
                    options_hi: ["<p>एक</p>", "<p>दो</p>",
                                "<p>शून्य</p>", "<p>तीन</p>"],
                    solution_en: "<p>15.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728976663851.png\" alt=\"rId10\" width=\"177\" height=\"54\"></p>",
                    solution_hi: "<p>15.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728976663851.png\" alt=\"rId10\" width=\"177\" height=\"54\"></p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "16",
                    section: "6",
                    question_en: "<p>16. Identify the number that does NOT belong to the following series.<br>10, 14, 28, 32, 64, 68, 132</p>",
                    question_hi: "<p>16. उस संख्या की पहचान करें जो निम्नलिखित श्रृंखला से संबंधित नहीं है<br>10, 14, 28, 32, 64, 68, 132</p>",
                    options_en: ["<p>64</p>", "<p>14</p>", 
                                "<p>32</p>", "<p>132</p>"],
                    options_hi: ["<p>64</p>", "<p>14</p>",
                                "<p>32</p>", "<p>132</p>"],
                    solution_en: "<p>16.(d)<br>10 + 4 = 14<br>14 <math display=\"inline\"><mo>&#215;</mo><mn>2</mn><mo>=</mo><mn>28</mn></math><br>28 + 4 = 32<br>32 <math display=\"inline\"><mo>&#215;</mo><mn>2</mn><mo>=</mo><mn>64</mn></math><br>64 + 4 = 68<br>68 <math display=\"inline\"><mo>&#215;</mo><mn>2</mn><mo>&#8800;</mo><mn>132</mn></math></p>",
                    solution_hi: "<p>16.(d)<br>10 + 4 = 14<br>14 <math display=\"inline\"><mo>&#215;</mo><mn>2</mn><mo>=</mo><mn>28</mn></math><br>28 + 4 = 32<br>32 <math display=\"inline\"><mo>&#215;</mo><mn>2</mn><mo>=</mo><mn>64</mn></math><br>64 + 4 = 68<br>68 <math display=\"inline\"><mo>&#215;</mo><mn>2</mn><mo>&#8800;</mo><mn>132</mn></math></p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "17",
                    section: "6",
                    question_en: "<p>17. Select the term that can replace the question mark (?) in the following series.<br>ACE, GIJ, LPO, PXT, ??</p>",
                    question_hi: "<p>17. उस पद का चयन करें जो निम्नलिखित श्रृंखला में प्रश्नवाचक चिन्ह (?) को प्रतिस्थापित कर सकता है<br>ACE, GIJ, LPO, PXT, ??</p>",
                    options_en: ["<p>SGY</p>", "<p>SYG</p>", 
                                "<p>SZG</p>", "<p>SGZ</p>"],
                    options_hi: ["<p>SGY</p>", "<p>SYG</p>",
                                "<p>SZG</p>", "<p>SGZ</p>"],
                    solution_en: "<p>17.(a)<br>1st alphabet + 6 = 1st alphabet of next + 5 = 1st alphabet of next and so on<br>2nd alphabet + 6 = 2nd alphabet of next + 7 = 2nd alphabet of next and so on<br>3rd alphabet + 5 = 3rd alphabet of next<br>Next term = P + 3 = S, X + 9 = G, T + 5 = Y<br>= SGY</p>",
                    solution_hi: "<p>17.(a)<br>पहला अक्षर + 6 = अगले का पहला अक्षर + 5 = अगले का पहला अक्षर इत्यादि<br>दूसरा अक्षर + 6 = अगले का दूसरा अक्षर + 7 = अगले का दूसरा अक्षर इत्यादि<br>तीसरा अक्षर + 5 = अगले का तीसरा अक्षर<br>अगला पद = P + 3 = S, X + 9 = G, T + 5 = Y<br>= SGY</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "18",
                    section: "6",
                    question_en: "<p>18. In a certain code language, &lsquo;Horses are animals&rsquo; is written as &lsquo;lu # @&rsquo; ,&rsquo; Animals are mammals&rsquo; is written as &lsquo;kt lu #&rsquo;, &lsquo;Are horses mammals&rsquo; is written as &lsquo;kt # @&rsquo;. What is the code for &ldquo;Animals&rdquo; in that code language ?</p>",
                    question_hi: "<p>18. एक निश्चित कोड भाषा में, &lsquo;Horses are animals&rsquo; को &lsquo;lu # @&rsquo; के रूप में लिखा जाता है, Animals are mammals को \'&lsquo;kt lu #&rsquo; लिखा जाता है, &lsquo;Are horses mammals&rsquo; को \'kt # @&rsquo; लिखा जाता है। उस कोड भाषा में &ldquo;Animals&rdquo; के लिए क्या कूट है ?</p>",
                    options_en: ["<p>kt</p>", "<p>#</p>", 
                                "<p>lu</p>", "<p>@</p>"],
                    options_hi: ["<p>kt</p>", "<p>#</p>",
                                "<p>lu</p>", "<p>@</p>"],
                    solution_en: "<p>18.(c)<br>Code for horses = @<br>Code for mammals = kt<br>Code for are = #<br>Code for Animals = lu</p>",
                    solution_hi: "<p>18.(c)<br>horses के लिए कोड = @<br>mammals के लिए कोड = kt<br>are लिए कोड हैं = #<br>Animals के लिए कोड = lu</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "19",
                    section: "6",
                    question_en: "<p>19. A, B, C, D, E are five friends. B is elder to C but is shortest in the group. C is taller than B , but younger to D. A is elder to D but not as tall as E. E is younger to C and is taller to B and D. <br>Which of the following sequences of their ages cannot be possible ?</p>",
                    question_hi: "<p>19. A, B, C, D, E पांच दोस्त हैं। B, C से बड़ा है लेकिन समूह में सबसे छोटा है। C, B से लंबा है, लेकिन D से छोटा है। A, D से बड़ा है, लेकिन E जितना लंबा नहीं है। E, C से छोटा है और B और D से लंबा है।<br>निम्नलिखित में से उनकी आयु का कौन-सा क्रम संभव नहीं हो सकता है ?</p>",
                    options_en: ["<p>A&gt;D&gt;B&gt;C&gt;E</p>", "<p>A&gt;D&gt;C&gt;B&gt;E</p>", 
                                "<p>B&gt;A&gt;D&gt;C&gt;E</p>", "<p>A&gt;B&gt;D&gt;C&gt;E</p>"],
                    options_hi: ["<p>A&gt;D&gt;B&gt;C&gt;E</p>", "<p>A&gt;D&gt;C&gt;B&gt;E</p>",
                                "<p>B&gt;A&gt;D&gt;C&gt;E</p>", "<p>A&gt;B&gt;D&gt;C&gt;E</p>"],
                    solution_en: "<p>19.(b)<br>A&gt;D&gt;C&gt;B&gt;E</p>",
                    solution_hi: "<p>19.(b)<br>A&gt;D&gt;C&gt;B&gt;E</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "20",
                    section: "6",
                    question_en: "<p>20. Eight persons A,B,C,D,E,F,G and H are sitting around a circular table facing towards the centre( not necessarily in the same order). A sits second to the right of C and C is third to the left of D. B is not the neighbour of C. B is second to the right of E. Only one person sits between D and G. G is second to the left of H. <br>What is the position of G with respect to C ?</p>",
                    question_hi: "<p>20. आठ व्यक्ति A,B,C,D,E,F,G और H एक वृताकार मेज के चारो ओर केंद्र की ओर मुख करके बैठे हैं (जरूरी नहीं कि इसी क्रम में हों)। A, C के दायें से दूसरे स्थान पर बैठा है और C, D के बायें से तीसरे स्थान पर बैठा है। B, C का पड़ोसी नहीं है। B, E के दायें से दूसरे स्थान पर है। D और G के बीच केवल एक व्यक्ति बैठा है। G, H के बायें से दूसरे स्थान पर है। <br>C के सन्दर्भ में G का स्थान क्या है ?</p>",
                    options_en: ["<p>Third to the left</p>", "<p>Immediate Right</p>", 
                                "<p>Third to the right</p>", "<p>Immediate left</p>"],
                    options_hi: ["<p>बायें से तीसरा</p>", "<p>निकटम दायाँ</p>",
                                "<p>दायें से तीसरा</p>", "<p>निकटम बायाँ</p>"],
                    solution_en: "<p>20.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728976664010.png\" alt=\"rId11\" width=\"124\" height=\"125\"></p>",
                    solution_hi: "<p>20.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728976664010.png\" alt=\"rId11\" width=\"124\" height=\"125\"></p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "21",
                    section: "6",
                    question_en: "<p>21. In a certain code language, &lsquo;NDRWCK&rsquo; is written as &lsquo;GUGPTZ&rsquo;. What is the code for &lsquo;MTSFHJ&rsquo; In that code language ?</p>",
                    question_hi: "<p>21. एक निश्चित कूट भाषा में, \'NDRWCK\' को \'GUGPTZ\' लिखा जाता है। उसी कूट भाषा में \'MTSFHJ\' के लिए कूट क्या है ?</p>",
                    options_en: ["<p>FKHYYY</p>", "<p>FKHZZZ</p>", 
                                "<p>FOPWWW</p>", "<p>FOPXXX</p>"],
                    options_hi: ["<p>FKHYYY</p>", "<p>FKHZZZ</p>",
                                "<p>FOPWWW</p>", "<p>FOPXXX</p>"],
                    solution_en: "<p>21.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728976664209.png\" alt=\"rId12\" width=\"176\" height=\"129\"></p>",
                    solution_hi: "<p>21.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728976664209.png\" alt=\"rId12\" width=\"176\" height=\"129\"></p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "22",
                    section: "6",
                    question_en: "<p>22. Read the given statement and conclusions carefully..Assuming that the information given in the statements is true, even if it appears to be in variance with commonly known facts, Decide which of the given conclusions logically follow(s) from the statements. <br><strong>Statements :</strong><br>I. All dogs are mammals.&nbsp;<br>II. No animals are dogs.&nbsp;<br><strong>Conclusions :</strong><br>(I) No animals are mammals <br>(II) Some animals are not mammals. <br>(III) No Dogs are animals <br>(IV) All mammals are dogs</p>",
                    question_hi: "<p>22.दिए गए कथन और निष्कर्षों को ध्यान से पढ़ें. यह मानते हुए कि कथनों में दी गई जानकारी सत्य है, भले ही वह सामान्य रूप से ज्ञात तथ्यों से भिन्न प्रतीत होती हो, तय करें कि दिए गए निष्कर्षों में से कौन सा कथनों का तार्किक रूप से अनुसरण करता है <br><strong>कथन :</strong><br>I. सभी कुत्ते स्तनधारी हैं।<br>II. कोई जानवर कुत्ते नहीं हैं<br><strong>निष्कर्ष :</strong><br>(I) कोई जानवर स्तनधारी नहीं हैं<br>(II) कुछ जानवर स्तनधारी नहीं हैं<br>(III) कोई कुत्ता जानवर नहीं है<br>(IV) सभी स्तनधारी कुत्ते हैं</p>",
                    options_en: ["<p>Only conclusion (III) follows</p>", "<p>Only conclusion (III) and (IV) follows.</p>", 
                                "<p>Only conclusion (I) follows</p>", "<p>Only conclusion (I) and (II) follows</p>"],
                    options_hi: ["<p>केवल निष्कर्ष (III) अनुसरण करता है</p>", "<p>केवल निष्कर्ष (III) और (IV) अनुसरण करता है।</p>",
                                "<p>केवल निष्कर्ष (I) अनुसरण करता है</p>", "<p>केवल निष्कर्ष (I) और (II) अनुसरण करता है</p>"],
                    solution_en: "<p>22.(a)<br><strong id=\"docs-internal-guid-c2df59f3-7fff-345b-42df-4c85b8a6bdbd\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXf9JMfO05ZDm2TGcOR1vPoLF1tH2E-HIj38_BuGqGcZEr21h78ggbbdjb55DFL0fK0H1vwyEiTBF33hPakg2pMVqj4BnrcuB5WomO8C5_JtAwYoY35N0NkOmXdTzTprPCMqbT15KcVk-tW2ZQjnWcfNVlki?key=sZYSr9k3dKcV3d54Qy-G4g\" width=\"118\" height=\"115\"></strong></p>\n<p>From the above diagram it is clear that no dogs are animals.</p>",
                    solution_hi: "<p>22.(a)</p>\n<p><strong id=\"docs-internal-guid-68804878-7fff-b891-78d3-2f52624c0c2e\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfdX-2GsN_mEah6fLEzktIPqYA0UPSlnlpp5UWMpOwZDXFnC4dENBA_zQAvpcfV1VzfctGiOlcecf_zAKeMhhyqV5W94TOdb1iKUODgu3yQOokweLeBkMNYN5ldps3-KXkPsEf41rHeBpOE-iA0Gu4JSWwf?key=sZYSr9k3dKcV3d54Qy-G4g\" width=\"118\" height=\"116\"></strong></p>\n<p>उपरोक्त आरेख से यह स्पष्ट है कि कोई कुत्ता जानवर नहीं है।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "23",
                    section: "6",
                    question_en: "<p>23. A man walks 10m forward towards the direction of sunrise and then 10m to its right. After that, every time turning to his left, he walks 5m, 15m, and 15m, respectively. What is the likely direction he is in now with reference to his starting point ?</p>",
                    question_hi: "<p>23. एक आदमी सूर्योदय की दिशा में 10m आगे चलता है और फिर 10m अपने दाहिनी ओर चलता है। उसके बाद, हर बार अपनी बाईं ओर मुड़कर, वह क्रमशः 5m, 15m, और 15m चलता है। अपने शुरुआती बिंदु के संदर्भ में अब वह किस संभावित दिशा में है ?</p>",
                    options_en: ["<p>East</p>", "<p>West</p>", 
                                "<p>South</p>", "<p>North</p>"],
                    options_hi: ["<p>पूर्व</p>", "<p>पश्चिम</p>",
                                "<p>दक्षिण</p>", "<p>उत्तर</p>"],
                    solution_en: "<p>23.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728976664472.png\" alt=\"rId14\" width=\"130\" height=\"123\"></p>",
                    solution_hi: "<p>23.(d)<br><strong id=\"docs-internal-guid-a34b97c6-7fff-8159-a2b6-2fee4089cb1c\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXe5xCJ_lwRONl4hr2elvB-1smIFOXxdRt321a1legrHd07295phDdalpQrtjum1fE-6ou8WL5TJ9eBINtbL3tzECFrPhooI8DI76aqJZkrShU7PnTlYnMCdLushjBQVdlLIZppPQ4SDgl4ot5fUX8EVPcg?key=sZYSr9k3dKcV3d54Qy-G4g\" width=\"136\" height=\"130\"></strong></p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "24",
                    section: "6",
                    question_en: "<p>24. Seven persons P,Q, R,S, A, B, and D are sitting one above the other on a ladder (not necessarily in the same order). B sits neither at the top nor at the bottom. Only four persons sit between P and R. <br>S sits immediately above A. Only one person sits between R and D. D sits below S. B sits above Q.<br>Three of the following are similar in certain way based on their seating arrangement and from a group, which of the following does not belong to that group ?</p>",
                    question_hi: "<p>24.सात व्यक्ति P, Q, R, S, A, B और D एक सीढ़ी पर एक के ऊपर एक बैठे हैं (जरूरी नहीं कि इसी क्रम में)। B न तो ऊपर और न ही नीचे बैठा है। P और R के मध्य केवल चार व्यक्ति बैठे हैं।<br>S, A के ठीक ऊपर बैठता है। केवल एक व्यक्ति R और D के बीच बैठता है। D, S के नीचे बैठता है। B, Q के ऊपर बैठता है।<br>बैठने की व्यवस्था के आधार पर निम्नलिखित में से तीन निश्चित रूप से समान हैं और एक समूह से, निम्नलिखित में से कौन उस समूह से संबंधित नहीं है ?</p>",
                    options_en: ["<p>S D</p>", "<p>B Q</p>", 
                                "<p>P A</p>", "<p>D B</p>"],
                    options_hi: ["<p>S D</p>", "<p>B Q</p>",
                                "<p>P A</p>", "<p>D B</p>"],
                    solution_en: "<p>24.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728976664595.png\" alt=\"rId15\" width=\"59\" height=\"144\"></p>",
                    solution_hi: "<p>24.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728976664595.png\" alt=\"rId15\" width=\"59\" height=\"144\"></p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "25",
                    section: "misc",
                    question_en: "<p>25. Select the term that can replace the question mark (?) in the following series. <br>O2F, R5H, U10J, X17L, ?</p>",
                    question_hi: "<p>25. उस पद का चयन करें जो निम्नलिखित श्रृंखला में प्रश्नवाचक चिन्ह (?) को प्रतिस्थापित कर सकता है<br>O2F, R5H, U10J, X17L, ?</p>",
                    options_en: ["<p>C82Q</p>", "<p>A26O</p>", 
                                "<p>B80M</p>", "<p>A26N</p>"],
                    options_hi: ["<p>C82Q</p>", "<p>A26O</p>",
                                "<p>B80M</p>", "<p>A26N</p>"],
                    solution_en: "<p>25.(d)<br>O + 3 = R, R + 3 = U, U + 3 = X, X + 3 = A<br>2 + 3 = 5, 5 + 5 = 10 , 10 + 7 = 17, 17 + 9 = 26<br>F+ 2 = H, H + 2 = J, J + 2 = L, L + 2 = N<br>Next term = A26N</p>",
                    solution_hi: "<p>25.(d)<br>O + 3 = R, R + 3 = U, U + 3 = X, X + 3 = A<br>2 + 3 = 5, 5 + 5 = 10 , 10 + 7 = 17, 17 + 9 = 26<br>F+ 2 = H, H + 2 = J, J + 2 = L, L + 2 = N<br>अगला पद = A26N</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>