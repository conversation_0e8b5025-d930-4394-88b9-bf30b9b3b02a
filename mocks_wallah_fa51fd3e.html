<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">15:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 15</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">15</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 15 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 0,
                end: 13
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 14,
                end: 14
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "17",
                    question_en: "<p>1. In one year, the average monthly expenditure of Sapna was Rs. 1100 for the first 3 months of the year, Rs. 1000 for the next 5 months and Rs. 1200 for the last 4 months. If the total annual saving was Rs. 1300, then what was sapna\'s average monthly income?</p>",
                    question_hi: "<p>1. एक वर्ष में, सपना का औसत मासिक व्यय वर्ष के प्रथम 3 महीनों के लिए 1100 रुपए, अगले 5 महीनों के लिए 1000 रुपए और अंतिम 4 महीनों के लिए 1200 रुपए था। यदि कुल वार्षिक बचत 1300 रुपए थी, तो सपना की औसत मासिक आय कितनी थी?</p>",
                    options_en: ["<p>Rs. 1250</p>", "<p>Rs. 1270</p>", 
                                "<p>Rs. 1210</p>", "<p>Rs. 1200</p>"],
                    options_hi: ["<p>1250 रुपए</p>", "<p>1270 रुपए</p>",
                                "<p>1210 रुपए</p>", "<p>1200 रुपए</p>"],
                    solution_en: "<p>1.(d)<br>Total expenditure of Sapna = 1100 &times; 3 + 1000 &times; 5 + 1200 &times; 4 = 13100<br>Total saving = 1300<br>Average monthly income of Sapna = <math display=\"inline\"><mfrac><mrow><mn>13100</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>1300</mn></mrow><mrow><mn>12</mn></mrow></mfrac></math></p>\n<p>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>14400</mn><mn>12</mn></mfrac></math></p>\n<p>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; = ₹1200</p>",
                    solution_hi: "<p>1.(d)<br>सपना का कुल खर्च = 1100 &times; 3 + 1000 &times; 5 + 1200 &times; 4 = 13100<br>कुल बचत = 1300<br>सपना की औसत मासिक आय = <math display=\"inline\"><mfrac><mrow><mn>13100</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>1300</mn></mrow><mrow><mn>12</mn></mrow></mfrac></math></p>\n<p>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>14400</mn><mn>12</mn></mfrac></math></p>\n<p>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;= ₹1200</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "2",
                    section: "17",
                    question_en: "<p>2. The ages of 7 men in a cricket team are 26, 25, 21, 29, 24, 28 and 22. What is their average age?</p>",
                    question_hi: "<p>2. एक क्रिकेट टीम में 7 पुरुषों की आयु 26, 25, 21, 29, 24, 28 और 22 है। उनकी औसत आयु कितनी है?</p>",
                    options_en: ["<p>24</p>", "<p>22</p>", 
                                "<p>25</p>", "<p>23</p>"],
                    options_hi: ["<p>24</p>", "<p>22</p>",
                                "<p>25</p>", "<p>23</p>"],
                    solution_en: "<p>2.(c)<br>Average age = <math display=\"inline\"><mfrac><mrow><mn>26</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>25</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>21</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>29</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>24</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>28</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>22</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>175</mn><mn>7</mn></mfrac></math> = 25</p>",
                    solution_hi: "<p>2.(c)<br>औसत आयु = <math display=\"inline\"><mfrac><mrow><mn>26</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>25</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>21</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>29</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>24</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>28</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>22</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>175</mn><mn>7</mn></mfrac></math>= 25</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "3",
                    section: "17",
                    question_en: "<p>3. The average of five numbers is 32. If one number is removed, then the average becomes 28. What is the removed number ?</p>",
                    question_hi: "<p>3. पांच संख्याओं का औसत 32 है। यदि एक संख्या हटा दी जाए, तो औसत 28 हो जाता है। हटाई गई संख्या कितनी है ?</p>",
                    options_en: ["<p>48</p>", "<p>52</p>", 
                                "<p>47</p>", "<p>49</p>"],
                    options_hi: ["<p>48</p>", "<p>52</p>",
                                "<p>47</p>", "<p>49</p>"],
                    solution_en: "<p>3.(a)<br>Removed no. = 32 + (32 - 28) &times; 4 = 32 + 16 = 48 <br><strong>Alternate :</strong> <br>Removed number = 32 &times; 5 - 28 &times; 4 = 160 - 112 = 48</p>",
                    solution_hi: "<p>3.(a)<br>हटाई गई संख्या = 32 + (32 - 28) &times; 4 = 32 + 16 = 48 <br><strong>वैकल्पिक :</strong> <br>हटाई गई संख्या = 32 &times; 5 - 28 &times; 4 = 160 - 112 = 48</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "4",
                    section: "17",
                    question_en: "<p>4. What will be the average of all the prime numbers before 32 ?</p>",
                    question_hi: "<p>4. 32 से पहले के सभी अभाज्य संख्याओं का औसत कितना है ?</p>",
                    options_en: ["<p>14.54</p>", "<p>15.60</p>", 
                                "<p>13.26</p>", "<p>17.82</p>"],
                    options_hi: ["<p>14.54</p>", "<p>15.60</p>",
                                "<p>13.26</p>", "<p>17.82</p>"],
                    solution_en: "<p>4.(a)<br>Prime no&rsquo;s up to 32 = 11 i.e.( 2, 3, 5, 7, 11, 13, 17, 19, 23, 29, 31)<br>Average = <math display=\"inline\"><mfrac><mrow><mn>2</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>3</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>5</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>7</mn><mo>+</mo><mi>&#160;</mi><mn>11</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>13</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>17</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>19</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>23</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>29</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>31</mn></mrow><mrow><mn>11</mn></mrow></mfrac></math></p>\n<p>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>160</mn><mn>11</mn></mfrac></math></p>\n<p>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; = 14.54</p>",
                    solution_hi: "<p>4.(a)<br>32 तक अभाज्य संख्या = 11 अर्थात( 2, 3, 5, 7, 11, 13, 17, 19, 23, 29, 31)<br>औसत = <math display=\"inline\"><mfrac><mrow><mn>2</mn><mo>+</mo><mn>3</mn><mo>+</mo><mn>5</mn><mo>+</mo><mn>7</mn><mo>+</mo><mn>11</mn><mo>+</mo><mn>13</mn><mo>+</mo><mn>17</mn><mo>+</mo><mn>19</mn><mo>+</mo><mn>23</mn><mo>+</mo><mn>29</mn><mo>+</mo><mn>31</mn></mrow><mrow><mn>11</mn></mrow></mfrac></math></p>\n<p>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>160</mn><mn>11</mn></mfrac></math></p>\n<p>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;= 14.54</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "5",
                    section: "17",
                    question_en: "5. Six friends have an average height of 144 cm. A boy with a height of 150 cm leaves the group. Find the new average height.",
                    question_hi: "5. छः दोस्तों की औसत ऊंचाई 144 cm है। 150 cm की ऊंचाई वाला एक लड़का समूह छोड़ देता है। समूह की नई औसत ऊंचाई ज्ञात कीजिए।",
                    options_en: [" 142.8 cm ", " 155.5 cm ", 
                                " 160.5 cm ", " 108.6 cm"],
                    options_hi: [" 142.8 cm ", " 155.5 cm ",
                                " 160.5 cm ", " 108.6 cm"],
                    solution_en: "5.(a)<br />New average height = 144 - <math display=\"inline\"><mfrac><mrow><mn>150</mn><mo>-</mo><mn>144</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> = 144 - 1.2 = 142.8 cm",
                    solution_hi: "5.(a) <br />नई औसत ऊंचाई  =   144 - <math display=\"inline\"><mfrac><mrow><mn>150</mn><mo>-</mo><mn>144</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> = 144 - 1.2 = 142.8 cm ",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "6",
                    section: "17",
                    question_en: "6. Average of 4 numbers is 52. Average of first two numbers is thrice the average of other two numbers. What is the sum of first two numbers?",
                    question_hi: "6. 4 संख्याओं का औसत 52 है। प्रथम दो संख्याओं का औसत अन्य दो संख्याओं के औसत का तीन गुना है। प्रथम दो संख्याओं का योग कितना है?",
                    options_en: [" 156 ", " 174", 
                                " 148 ", " 147"],
                    options_hi: [" 156 ", " 174",
                                " 148 ", " 147"],
                    solution_en: "<p>6.(a)<br>Let the numbers = a, b, c, d<br>ATQ,<br><math display=\"inline\"><mfrac><mrow><mi>a</mi><mo>+</mo><mi>b</mi></mrow><mrow><mn>2</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>3</mn><mfrac><mrow><mi>c</mi><mo>+</mo><mi>d</mi></mrow><mn>2</mn></mfrac></math><br>a + b = 3(c + d)<br>Now, a + b + c + d = 52 &times; 4 = 208<br>a + b + <math display=\"inline\"><mo>(</mo><mfrac><mrow><mi>a</mi><mo>+</mo><mi>b</mi></mrow><mrow><mn>3</mn></mrow></mfrac><mo>)</mo></math> = 208<br>4(a + b) = 624<br>a + b = 156</p>",
                    solution_hi: "<p>6.(a)<br>माना संख्याएँ = a, b, c, d<br>प्रश्न के अनुसार,<br><math display=\"inline\"><mfrac><mrow><mi>a</mi><mo>+</mo><mi>b</mi></mrow><mrow><mn>2</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>3</mn><mfrac><mrow><mi>c</mi><mo>+</mo><mi>d</mi></mrow><mn>2</mn></mfrac></math><br>a + b = 3(c + d)<br>अब , a + b + c + d = 52 &times; 4 = 208<br>a + b + <math display=\"inline\"><mo>(</mo><mfrac><mrow><mi>a</mi><mo>+</mo><mi>b</mi></mrow><mrow><mn>3</mn></mrow></mfrac><mo>)</mo></math> = 208<br>4(a + b) = 624<br>a + b = 156</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "7",
                    section: "17",
                    question_en: "7. The average marks of 13 students was 130. But later it was found that the score of one student was wrongly entered as 23 instead of 32. What is the correct average of their score ?",
                    question_hi: "7. 13 विद्यार्थियों के औसत अंक 130 थे। लेकिन बाद में यह पाया गया कि एक विद्यार्थी के अंक गलती से 32 के स्थान पर 23 दर्ज कर लिए गए थे। उनके अंक का सही औसत क्या है ?",
                    options_en: [" 132.28", " 131.72", 
                                " 130.69", " 130.88"],
                    options_hi: [" 132.28", " 131.72",
                                " 130.69", " 130.88"],
                    solution_en: "7.(c)<br />Correct average = 130 + <math display=\"inline\"><mfrac><mrow><mn>32</mn><mo>-</mo><mn>23</mn></mrow><mrow><mn>13</mn></mrow></mfrac></math> = 130 + 0.69 = 130.69 ",
                    solution_hi: "7.(c)<br />सही औसत = 130 + <math display=\"inline\"><mfrac><mrow><mn>32</mn><mo>-</mo><mn>23</mn></mrow><mrow><mn>13</mn></mrow></mfrac></math> = 130 + 0.69 = 130.69 ",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "8",
                    section: "17",
                    question_en: "<p>8. The average weight of a class of 22 students is 42 kg. If 2 new students of weight 33 kg and 39 kg are added to the class, then what will be the new average weight of 24 students ?</p>",
                    question_hi: "<p>8. 22 विद्यार्थियों की एक कक्षा का औसत भार 42 kg है। यदि कक्षा में 33 kg और 39 kg भार के 2 नए विद्यार्थी शामिल हो जाते हैं, तो 24 विद्यार्थियों का नया औसत भार कितना होगा ?</p>",
                    options_en: ["<p>41.5 kg</p>", "<p>39.5 kg</p>", 
                                "<p>43 kg</p>", "<p>42 kg</p>"],
                    options_hi: ["<p>41.5 kg</p>", "<p>39.5 kg</p>",
                                "<p>43 kg</p>", "<p>42 kg</p>"],
                    solution_en: "<p>8.(a)<br>Average weight of class = 42 kg<br>Net deviation in the average weight of new students = -9 - 3 = -12&nbsp;<br>New average weight = 42 - <math display=\"inline\"><mfrac><mrow><mn>12</mn></mrow><mrow><mn>24</mn></mrow></mfrac></math> = 42 - 0.5 = 41.5 kg</p>",
                    solution_hi: "<p>8.(a)<br>कक्षा का औसत वजन = 42 किग्रा<br>नए छात्रों के औसत वजन में शुद्ध विचलन = -9 - 3 = -12<br>नया औसत वजन = 42 - <math display=\"inline\"><mfrac><mrow><mn>12</mn></mrow><mrow><mn>24</mn></mrow></mfrac></math> = 42 - 0.5 = 41.5 किग्रा</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "9",
                    section: "17",
                    question_en: "<p>9. The average income of 35 employees is Rs. K. If the income of manager is also included, then the average income becomes Rs. 19000. If the income of the manager is Rs.54000, then what is the value of K?</p>",
                    question_hi: "<p>9. 35 कर्मचारियों की औसत आय K रुपए है। यदि प्रबंधक की आय भी शामिल कर ली जाए , तो औसत आय 19000 रुपए हो जाती है। यदि प्रबंधक की आय 54000 रुपए है, तो K का मान कितना है?</p>",
                    options_en: ["<p>Rs. 19,200</p>", "<p>Rs. 18,000</p>", 
                                "<p>Rs. 18,800</p>", "<p>Rs. 17,700</p>"],
                    options_hi: ["<p>19,200 रुपए</p>", "<p>18,000 रुपए</p>",
                                "<p>18,800 रुपए</p>", "<p>17,700 रुपए</p>"],
                    solution_en: "<p>9.(b)<br>Using Alligation method, we have;<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731579125229.png\" alt=\"rId5\" width=\"159\" height=\"130\"><br>So, we have ;<br><math display=\"inline\"><mfrac><mrow><mn>35</mn><mo>,</mo><mn>000</mn></mrow><mrow><mn>19</mn><mo>,</mo><mn>000</mn><mo>-</mo><mi>K</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>35</mn><mn>1</mn></mfrac></math><br>1000 = 19,000 - K<br>K = 18,000</p>",
                    solution_hi: "<p>9.(b)<br>एलीगेशन विधि का उपयोग करते हुए,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731579125391.png\" alt=\"rId6\" height=\"130\"><br>अत:, <br><math display=\"inline\"><mfrac><mrow><mn>35</mn><mo>,</mo><mn>000</mn></mrow><mrow><mi>&#160;</mi><mn>19</mn><mo>,</mo><mn>000</mn><mo>-</mo><mi>K</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>35</mn><mn>1</mn></mfrac></math><br>1000 = 19,000 - K<br>&nbsp; K = 18,000</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "10",
                    section: "17",
                    question_en: "10. Average age of 22 teachers is 64 years. After involving a new teacher What is the age of the new teacher? in that group the average increases by 1 year",
                    question_hi: "10. 22 शिक्षकों की औसत आयु 64 वर्ष है। उस समूह में एक नए शिक्षक को शामिल करने के बाद औसत में 1 वर्ष की वृद्धि हो जाती है। नए शिक्षक की आयु कितनी है?",
                    options_en: [" 93 years", " 87 years", 
                                " 89 years", " 91 years"],
                    options_hi: [" 93 वर्ष", " 87 वर्ष",
                                " 89 वर्ष", " 91 वर्ष"],
                    solution_en: "10.(b)<br />Age of new teacher = 64 + 1 × 23 = 87 yrs",
                    solution_hi: "10.(b)<br />नये शिक्षक की आयु = 64 + 1 × 23 = 87 वर्ष ",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "11",
                    section: "17",
                    question_en: "<p>11. Two numbers are given in such a way that the first number is half of the second number. If their average is 15, what is the first number ?</p>",
                    question_hi: "<p>11. दो संख्याएं इस प्रकार दी गई है कि पहली संख्या दूसरी संख्या की आधी है। यदि उनका औसत 15 है, तो पहली संख्या क्या है ?</p>",
                    options_en: ["<p>10</p>", "<p>14</p>", 
                                "<p>8</p>", "<p>12</p>"],
                    options_hi: ["<p>10</p>", "<p>14</p>",
                                "<p>8</p>", "<p>12</p>"],
                    solution_en: "<p>11.(a) Let the first numbers and second number <math display=\"inline\"><mfrac><mrow><mi>x</mi></mrow><mrow><mn>2</mn></mrow></mfrac></math> and x be respectively,<br>According to question,<br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>x</mi><mn>2</mn></mfrac></math>+ x = 15 &times; 2<br><math display=\"inline\"><mo>&#8658;</mo></math> x + 2x = 60<br><math display=\"inline\"><mo>&#8658;</mo></math> x = 20<br>First number = <math display=\"inline\"><mfrac><mrow><mn>20</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = 10</p>",
                    solution_hi: "<p>11.(a) माना कि पहली संख्या और दूसरी संख्या क्रमशः <math display=\"inline\"><mfrac><mrow><mi>x</mi></mrow><mrow><mn>2</mn></mrow></mfrac></math> और x हैं,<br>प्रश्न के अनुसार,<br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>x</mi><mn>2</mn></mfrac></math>+ x = 15 &times; 2<br><math display=\"inline\"><mo>&#8658;</mo></math> x + 2x = 60<br><math display=\"inline\"><mo>&#8658;</mo></math> x = 20<br>पहला संख्या = <math display=\"inline\"><mfrac><mrow><mn>20</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = 10</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "12",
                    section: "17",
                    question_en: "<p>12. The average of 4 numbers is 24. The average of the first 2 numbers is twice the average of the last 2 numbers. What is the sum of the last 2 numbers ?</p>",
                    question_hi: "<p>12. 4 संख्याओं का औसत 24 है। प्रथम 2 संख्याओं का औसत अंतिम 2 संख्याओं के औसत से दोगुना है। अंतिम 2 संख्याओं का योग क्या है?</p>",
                    options_en: ["<p>72</p>", "<p>56</p>", 
                                "<p>48</p>", "<p>32</p>"],
                    options_hi: ["<p>72</p>", "<p>56</p>",
                                "<p>48</p>", "<p>32</p>"],
                    solution_en: "<p>12.(d) Let the four numbers be a, b, c and d respectively,<br>Average of 4 numbers = 24<br><math display=\"inline\"><mo>&#8658;</mo></math> a + b + c + d = 96 &hellip;&hellip;(i)<br>According to question,<br><math display=\"inline\"><mo>&#8658;</mo></math><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mfenced><mrow><mi>a</mi><mo>+</mo><mi>b</mi></mrow></mfenced><mn>2</mn></mfrac><mo>=</mo><mn>2</mn><mo>&#215;</mo><mfrac><mfenced><mrow><mi>c</mi><mo>+</mo><mi>d</mi></mrow></mfenced><mn>2</mn></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math> 2(c + d) = (a + b)<br><math display=\"inline\"><mo>&#8658;</mo></math> 2(c + d) = 96 - (c + d) (from eq .i)<br><math display=\"inline\"><mo>&#8658;</mo></math> (c + d) = 32</p>",
                    solution_hi: "<p>12.(d) माना कि चार संख्याएँ क्रमशः a, b, c और d हैं,<br>4 संख्याओं का औसत = 24<br><math display=\"inline\"><mo>&#8658;</mo></math> a + b + c + d = 96 &hellip;&hellip;(i)<br>प्रश्न के अनुसार,<br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mfenced><mrow><mi>a</mi><mo>+</mo><mi>b</mi></mrow></mfenced><mn>2</mn></mfrac><mo>=</mo><mn>2</mn><mo>&#215;</mo><mfrac><mfenced><mrow><mi>c</mi><mo>+</mo><mi>d</mi></mrow></mfenced><mn>2</mn></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math> 2(c + d) = (a + b)<br><math display=\"inline\"><mo>&#8658;</mo></math> 2(c + d) = 96 - (c + d) (समीकरण (i) से)<br><math display=\"inline\"><mo>&#8658;</mo></math> (c + d) = 32</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "13",
                    section: "17",
                    question_en: "13. The average weight of T, U and V is 42 kg. If the average weight of T and U is 40 kg and the average weight of U and V is 36 kg, then what is the weight of U?",
                    question_hi: "<p>13. T, U और V का औसत भार 42 kg है। यदि T और U का औसत भार 40 kg है और U और V का औसत भार 36 kg है, तो U का भार कितना है?</p>",
                    options_en: ["<p>22 kg</p>", "<p>24 kg</p>", 
                                "<p>26 kg</p>", "<p>29 kg</p>"],
                    options_hi: ["<p>22 kg</p>", "<p>24 kg</p>",
                                "<p>26 kg</p>", "<p>29 kg</p>"],
                    solution_en: "<p>13.(c)<br>Weight of V = 42 + (42 - 40) &times; 2 = 46 kg<br>Weight of T = 42 + (42 - 36) &times; 2 = 54 kg<br>Total weight of (T + U + V) = 42 &times; 3 = 126 kg<br>Weight of U = 126 - (46 + 54) = 26 kg</p>",
                    solution_hi: "<p>13.(c)<br>V का भार = 42 + (42 - 40) &times; 2 = 46 kg<br>T का भार = 42 + (42 - 36) &times; 2 = 54 kg<br>(T + U + V )का कुल भार = 42 &times; 3 = 126 kg<br>U का भार = 126 - (46 + 54) = 26 kg</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "14",
                    section: "17",
                    question_en: "14. The average marks of a student in 6 subjects are 75. If the student scores 80 in one subject, what will be the total score in the remaining subjects.",
                    question_hi: "14. एक विद्यार्थी के 6 विषयों में प्राप्त औसत अंक 75 हैं। यदि उस विद्यार्थी को एक विषय में 80 अंक प्राप्त होते हैं, तो शेष विषयों में प्राप्त कुल अंक कितने होंगे?",
                    options_en: [" 370 ", " 340", 
                                " 350", " 380"],
                    options_hi: [" 370 ", " 340",
                                " 350", " 380"],
                    solution_en: "14.(a)<br />Total marks in 6 subjects = 75 × 6 = 450<br />Total score of remaining subjects = 450 - 80 = 370",
                    solution_hi: "14.(a)<br />6 विषयों में प्राप्त कुल अंक = 75 × 6 = 450<br />शेष विषयों में प्राप्त कुल अंक = 450 - 80 = 370",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "15",
                    section: "misc",
                    question_en: "<p>15. Average of five numbers is 36. The average of first two numbers is 1.5 times of the average of last three numbers. What is the sum of first two numbers?</p>",
                    question_hi: "<p>15. पांच संख्याओं का औसत 36 है। प्रथम दो संख्याओं का औसत अंतिम तीन संख्याओं के औसत का 1.5 गुना है। प्रथम दो संख्याओं का योग कितना है?</p>",
                    options_en: ["<p>85</p>", "<p>79</p>", 
                                "<p>78</p>", "<p>90</p>"],
                    options_hi: ["<p>85</p>", "<p>79</p>",
                                "<p>78</p>", "<p>90</p>"],
                    solution_en: "<p>15.(d)<br>Number -&nbsp; &nbsp; &nbsp; &nbsp;2 : 3<br>Average - 1.5<math display=\"inline\"><mi>x</mi></math> : x<br>Net average = <math display=\"inline\"><mfrac><mrow><mn>2</mn><mo>&#215;</mo><mn>1</mn><mo>.</mo><mn>5</mn><mi>x</mi><mo>+</mo><mn>3</mn><mi>x</mi></mrow><mrow><mn>2</mn><mo>+</mo><mn>3</mn></mrow></mfrac></math> = <math display=\"inline\"><mfrac><mrow><mn>6</mn><mi>x</mi></mrow><mrow><mn>5</mn></mrow></mfrac></math><br>36 = <math display=\"inline\"><mfrac><mrow><mn>6</mn><mi>x</mi></mrow><mrow><mn>5</mn></mrow></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math> x = 30<br>So, the sum of the first two no&rsquo;s = 2 &times; 1.5<math display=\"inline\"><mi>x</mi></math> = 2 &times; 45 = 90</p>",
                    solution_hi: "<p>15.(d)<br>संख्या -&nbsp; &nbsp; &nbsp; &nbsp;2 : 3<br>औसत - 1.5<math display=\"inline\"><mi>x</mi></math> : x<br>शुद्ध औसत = <math display=\"inline\"><mfrac><mrow><mn>2</mn><mo>&#215;</mo><mn>1</mn><mo>.</mo><mn>5</mn><mi>x</mi><mo>+</mo><mn>3</mn><mi>x</mi></mrow><mrow><mn>2</mn><mo>+</mo><mn>3</mn></mrow></mfrac></math> = <math display=\"inline\"><mfrac><mrow><mn>6</mn><mi>x</mi></mrow><mrow><mn>5</mn></mrow></mfrac></math><br>36 = <math display=\"inline\"><mfrac><mrow><mn>6</mn><mi>x</mi></mrow><mrow><mn>5</mn></mrow></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math> x = 30<br>तो पहली दो संख्याओं का योग = 2 &times; 1.5<math display=\"inline\"><mi>x</mi></math> = 2 &times; 45 = 90</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>