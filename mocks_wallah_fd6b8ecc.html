<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">12:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 25</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">25</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 12 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["7"] = {
                name: "English",
                start: 0,
                end: 23
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="7">English</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 24,
                end: 24
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "7",
                    question_en: "<p>1. Select the option that can be used as a one-word substitute for the underlined group of words in the following sentence.<br>Despite recent superficial moves toward political reconciliation, Libya remains in the grip of a bloodstained corrupt military <span style=\"text-decoration: underline;\">exclusive circle of people with a common purpose</span>.</p>",
                    question_hi: "<p>1. Select the option that can be used as a one-word substitute for the underlined group of words in the following sentence.<br>Despite recent superficial moves toward political reconciliation, Libya remains in the grip of a bloodstained corrupt military <span style=\"text-decoration: underline;\">exclusive circle of people with a common purpose</span>.</p>",
                    options_en: ["<p>oblique</p>", "<p>pique</p>", 
                                "<p>clique</p>", "<p>antique</p>"],
                    options_hi: ["<p>oblique</p>", "<p>pique</p>",
                                "<p>clique</p>", "<p>antique</p>"],
                    solution_en: "<p>1.(c) <strong>Clique</strong>- exclusive circle of people with a common purpose.<br><strong>Oblique</strong>- not expressed or done in a direct way.<br><strong>Pique</strong>- a feeling of anger, especially caused by someone damaging your feeling of being proud of yourself.<br><strong>Antique</strong>- made in an earlier period and considered to have value because of being beautiful, rare, old, or of high quality.</p>",
                    solution_hi: "<p>1.(c) <strong>Clique </strong>(गुट/समूह)- exclusive circle of people with a common purpose.<br><strong>Oblique </strong>(परोक्ष)- not expressed or done in a direct way.<br><strong>Pique </strong>(मनमुटाव)- a feeling of anger, especially caused by someone damaging your feeling of being proud of yourself.<br><strong>Antique </strong>(प्राचीन)- made in an earlier period and considered to have value because of being beautiful, rare, old, or of high quality.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "7",
                    question_en: "<p>2. Select the option that can be used as a one-word substitute for the given group of words.<br>An instrument for viewing distant objects in space.</p>",
                    question_hi: "<p>2. Select the option that can be used as a one-word substitute for the given group of words.<br>An instrument for viewing distant objects in space.</p>",
                    options_en: ["<p>Microscope</p>", "<p>Spectrograph</p>", 
                                "<p>Telescope</p>", "<p>Binoculars</p>"],
                    options_hi: ["<p>Microscope</p>", "<p>Spectrograph</p>",
                                "<p>Telescope</p>", "<p>Binoculars</p>"],
                    solution_en: "<p>2.(c) <strong>Telescope</strong>- an instrument for viewing distant objects in space.<br><strong>Microscope</strong>- device that uses lenses to make very small objects look larger.<br><strong>Spectrograph</strong>- an apparatus for photographing or otherwise recording spectra.<br><strong>Binoculars</strong>- an optical instrument with a lens for each eye, used for viewing distant objects.</p>",
                    solution_hi: "<p>2.(c) <strong>Telescope </strong>(दूरदर्शी)- an instrument for viewing distant objects in space.<br><strong>Microscope </strong>(सूक्ष्मदर्शी)- device that uses lenses to make very small objects look larger.<br><strong>Spectrograph </strong>(स्पेक्ट्रोग्राफ)- an apparatus for photographing or otherwise recording spectra.<br><strong>Binoculars </strong>(दूरबीन)- an optical instrument with a lens for each eye, used for viewing distant objects.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "7",
                    question_en: "<p>3. The following sentence contains a word with a spelling error. Rectify the sentence by selecting the correct spelling of the identified word from the given options.<br>I was upstaires when somebody rang the doorbell.</p>",
                    question_hi: "<p>3. The following sentence contains a word with a spelling error. Rectify the sentence by selecting the correct spelling of the identified word from the given options.<br>I was upstaires when somebody rang the doorbell.</p>",
                    options_en: ["<p>Upstares</p>", "<p>Upstaire</p>", 
                                "<p>upsatires</p>", "<p>Upstairs</p>"],
                    options_hi: ["<p>Upstares</p>", "<p>Upstaire</p>",
                                "<p>upsatires</p>", "<p>Upstairs</p>"],
                    solution_en: "<p>3.(d) Upstairs<br>\'Upstairs\' is the correct spelling.</p>",
                    solution_hi: "<p>3.(d) Upstairs<br>\'Upstairs\' सही spelling है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "7",
                    question_en: "<p>4. Select the most appropriate synonym of the given word.<br>Adversity</p>",
                    question_hi: "<p>4. Select the most appropriate synonym of the given word.<br>Adversity</p>",
                    options_en: ["<p>Success</p>", "<p>Misfortune</p>", 
                                "<p>Pleasure</p>", "<p>Advantage</p>"],
                    options_hi: ["<p>Success</p>", "<p>Misfortune</p>",
                                "<p>Pleasure</p>", "<p>Advantage</p>"],
                    solution_en: "<p>4.(b) <strong>Misfortune</strong>- bad luck or an unfortunate event.<br><strong>Adversity</strong>- difficulties or hardships.<br><strong>Success</strong>- the accomplishment of an aim or purpose.<br><strong>Pleasure</strong>- a feeling of happiness or satisfaction.<br><strong>Advantage</strong>- a condition or circumstance that puts one in a favorable position.</p>",
                    solution_hi: "<p>4.(b) <strong>Misfortune </strong>(दुर्भाग्य)- bad luck or an unfortunate event.<br><strong>Adversity </strong>(विपत्ति)- difficulties or hardships.<br><strong>Success </strong>(सफलता)- the accomplishment of an aim or purpose.<br><strong>Pleasure </strong>(आनंद)- a feeling of happiness or satisfaction.<br><strong>Advantage </strong>(लाभ)- a condition or circumstance that puts one in a favorable position.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "7",
                    question_en: "<p>5. Select the most appropriate ANTONYM of the following word.<br>Forbid</p>",
                    question_hi: "<p>5. Select the most appropriate ANTONYM of the following word.<br>Forbid</p>",
                    options_en: ["<p>Prohibit</p>", "<p>Allow</p>", 
                                "<p>Call</p>", "<p>Forsake</p>"],
                    options_hi: ["<p>Prohibit</p>", "<p>Allow</p>",
                                "<p>Call</p>", "<p>Forsake</p>"],
                    solution_en: "<p>5.(b) <strong>Allow</strong>- permit or give permission.<br><strong>Forbid</strong>- refuse to allow.<br><strong>Prohibit</strong>- formally forbid something by law or authority.<br><strong>Call</strong>- summon or request someone to come.<br><strong>Forsake</strong>- abandon or leave.</p>",
                    solution_hi: "<p>5.(b) <strong>Allow </strong>(अनुमति देना)- permit or give permission.<br><strong>Forbid </strong>(मना करना)- refuse to allow.<br><strong>Prohibit </strong>(मना करना)- formally forbid something by law or authority.<br><strong>Call </strong>(बुलाना)- summon or request someone to come.<br><strong>Forsake </strong>(त्यागना)- abandon or leave.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "7",
                    question_en: "<p>6. Parts of a sentence are given below in jumbled order. Arrange the parts in the correct order to form a meaningful sentence.<br>(A) while the employees<br>(B) meeting on time<br>(C) the boss arrived at the<br>(D) despite the heavy traffic<br>(E) were late</p>",
                    question_hi: "<p>6. Parts of a sentence are given below in jumbled order. Arrange the parts in the correct order to form a meaningful sentence.<br>(A) while the employees<br>(B) meeting on time<br>(C) the boss arrived at the<br>(D) despite the heavy traffic<br>(E) were late</p>",
                    options_en: ["<p>DCBAE</p>", "<p>DACEB</p>", 
                                "<p>BACED</p>", "<p>ACEDB</p>"],
                    options_hi: ["<p>DCBAE</p>", "<p>DACEB</p>",
                                "<p>BACED</p>", "<p>ACEDB</p>"],
                    solution_en: "<p>6.(a) <strong>DCBAE</strong><br>The given sentence starts with Part D as it introduces the main idea of the sentence, i.e. &lsquo;Despite the heavy traffic&rsquo;. Part C contains the subject of the first clause, &lsquo;the boss&rsquo; &amp; Part B states that the boss arrived at the meeting on time. So, B will follow C. Further, Part A states a contrasting idea using the conjunction &lsquo;while&rsquo; &amp; Part E states that the employees were late. So, E will follow A. Going through the options, option &lsquo;a&rsquo; has the correct sequence.</p>",
                    solution_hi: "<p>6.(a) <strong>DCBAE</strong><br>दिया गया sentence, Part D से प्रारंभ होता है क्योंकि यह sentence के मुख्य विचार &lsquo;Despite the heavy traffic&rsquo; को प्रस्तुत करता है। Part C में पहले clause का subject &lsquo;the boss&rsquo; है और Part B में कहा गया है कि boss समय पर meeting में पहुंचे। इसलिए, C के बाद B आएगा। इसके अलावा, Part A, conjunction &lsquo;while&rsquo; का उपयोग करके एक contrasting idea बताता है और Part E में कहा गया है कि employees देर से आए थे। इसलिए, A के बाद E आएगा। अतः options के माध्यम से जाने पर option &lsquo;a&rsquo; में सही sequence है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "7",
                    question_en: "7. Select the most appropriate option to substitute the underlined segment in the given sentence.<br />Charles Darwin is renowned for his book on evolution, ‘On the Origin of Species’, which laid down a treasure of<br />substantiation that evolution occurs, and projected a mechanism, that is, natural selection, for how it does so.",
                    question_hi: "7. Select the most appropriate option to substitute the underlined segment in the given sentence.<br />Charles Darwin is renowned for his book on evolution, ‘On the Origin of Species’, which laid down a treasure of<br />substantiation that evolution occurs, and projected a mechanism, that is, natural selection, for how it does so.",
                    options_en: [" laid into a treasure of substantiation", " laid about a treasure of substantiation", 
                                " laid away a treasure of substantiation", " laid out a treasure of substantiation"],
                    options_hi: [" laid into a treasure of substantiation", " laid about a treasure of substantiation",
                                " laid away a treasure of substantiation", " laid out a treasure of substantiation"],
                    solution_en: "7.(d) laid out a treasure of substantiation<br />‘Laid out’ means explained or described something in a clear and detailed way. The given sentence states that Charles Darwin is renowned for his book on evolution, ‘On the Origin of Species’, which laid down a treasure of<br />substantiation that evolution occurs. Hence, ‘laid out a treasure of substantiation’ is the most appropriate answer.",
                    solution_hi: "7.(d) laid out a treasure of substantiation<br />‘Laid out’ का अर्थ है किसी चीज़ को स्पष्ट और विस्तृत तरीके से समझाना या वर्णित करना। दिए गए sentence में कहा गया है कि चार्ल्स डार्विन evolution पर अपनी पुस्तक ‘On the Origin of Species’ के लिए प्रसिद्ध हैं, जिसमें इस बात की पुष्टि की गई है कि evolution होता है। अतः,  ‘laid out a treasure of substantiation’ सबसे उपयुक्त उत्तर है।",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "7",
                    question_en: "8. Parts of the following sentence have been given as options. Select the option that contains an error.<br />It is he who work for the company for more than a decade.",
                    question_hi: "8. Parts of the following sentence have been given as options. Select the option that contains an error.<br />It is he who work for the company for more than a decade.",
                    options_en: [" for more than a decade", " who work", 
                                " for the company ", " It is he"],
                    options_hi: [" for more than a decade", " who work",
                                " for the company ", " It is he"],
                    solution_en: "8.(b) who work<br />‘Who work’ must be replaced with ‘who has been working’ as we use the perfect continuous tense with ‘for/since’ to indicate time period. Hence, ‘who has been working’ is the most appropriate answer.",
                    solution_hi: "8.(b) who work<br />दिए गए sentence में ‘who work’ के स्थान पर ‘who has been working’ का प्रयोग होगा क्योंकि time period को इंगित करने के लिए ‘for/since’ के साथ  perfect continuous tense का प्रयोग करते हैं। अतः, ‘who has been working’ सबसे उपयुक्त उत्तर है।",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "7",
                    question_en: "<p>9. Select the option that correctly expresses the given sentence in passive voice.<br>The teachers of our school have already taken this training.</p>",
                    question_hi: "<p>9. Select the option that correctly expresses the given sentence in passive voice.<br>The teachers of our school have already taken this training.</p>",
                    options_en: ["<p>This training has already been taken by the teachers of our school.</p>", "<p>This training has been taken by the teachers of our school.</p>", 
                                "<p>This training has already been taken by the our school teachers.</p>", "<p>This training was already taken by the teachers of our school.</p>"],
                    options_hi: ["<p>This training has already been taken by the teachers of our school.</p>", "<p>This training has been taken by the teachers of our school.</p>",
                                "<p>This training has already been taken by the our school teachers.</p>", "<p>This training was already taken by the teachers of our school.</p>"],
                    solution_en: "<p>9.(a) This training has already been taken by the teachers of our school. (Correct)<br>(b) This training has been taken by the teachers of our school. (&lsquo;Already&rsquo; is missing)<br>(c) This training has already been taken by the our school teachers. (Incorrect Sentence Structure)<br>(d) This training <span style=\"text-decoration: underline;\">was already taken</span> by the teachers of our school. (Incorrect Tense)</p>",
                    solution_hi: "<p>9.(a) This training has already been taken by the teachers of our school. (Correct)<br>(b) This training has been taken by the teachers of our school. (&lsquo;Already&rsquo; missing है)<br>(c) This training has already been taken by the our school teachers. (गलत Sentence Structure)<br>(d) This training <span style=\"text-decoration: underline;\">was already taken</span> by the teachers of our school. (गलत Tense)</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "7",
                    question_en: "<p>10. Select the most appropriate ANTONYM of the underlined word.<br>He was very <span style=\"text-decoration: underline;\">cruel </span>in dealing with the students.</p>",
                    question_hi: "<p>10. Select the most appropriate ANTONYM of the underlined word.<br>He was very <span style=\"text-decoration: underline;\">cruel </span>in dealing with the students.</p>",
                    options_en: [" Skittish", " Kind", 
                                " Malevolent", " Kingly"],
                    options_hi: [" Skittish", " Kind",
                                " Malevolent", " Kingly"],
                    solution_en: "<p>10.(b) <strong>Kind</strong>- caring and compassionate.<br><strong>Cruel</strong>- causing pain or suffering.<br><strong>Skittish</strong>- nervous or easily frightened.<br><strong>Malevolent</strong>- having or showing a desire to harm others.<br><strong>Kingly</strong>- royal or majestic.</p>",
                    solution_hi: "<p>10.(b) <strong>Kind </strong>(दयालु)- caring and compassionate.<br><strong>Cruel</strong> (निर्दयी)- causing pain or suffering.<br><strong>Skittish </strong>(डरपोक)- nervous or easily frightened.<br><strong>Malevolent </strong>(द्वेषपूर्ण)- having or showing a desire to harm others.<br><strong>Kingly </strong>(राजसी)- royal or majestic.</p>",
                    correct: " b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "7",
                    question_en: "<p>11. Parts of a sentence are given below in jumbled order. Arrange the parts in the correct order to form a meaningful sentence.<br>(A) he had to take the bus<br>(B) his friend, who used to give<br>(C) to his office because he had<br>(D) him a lift, was on vacation<br>(E) never learned to drive and</p>",
                    question_hi: "<p>11. Parts of a sentence are given below in jumbled order. Arrange the parts in the correct order to form a meaningful sentence.<br>(A) he had to take the bus<br>(B) his friend, who used to give<br>(C) to his office because he had<br>(D) him a lift, was on vacation<br>(E) never learned to drive and</p>",
                    options_en: ["<p>DACEB</p>", "<p>ACEBD</p>", 
                                "<p>EBADC</p>", "<p>BADEC</p>"],
                    options_hi: ["<p>DACEB</p>", "<p>ACEBD</p>",
                                "<p>EBADC</p>", "<p>BADEC</p>"],
                    solution_en: "<p>11.(b) <strong>ACEBD</strong><br>The given sentence starts with Part A as it introduces the main idea of the sentence, i.e. &lsquo;He had to take the bus&rsquo;. Part C states the destination (his office) and has the conjunction &lsquo;because&rsquo; to state the reason for taking the bus. Part E states the reason for taking the bus, which is that he never learned to drive. So, E will follow C. Further, Part B talks about his friend and Part E states that his friend was on vacation. So, D will follow B. Going through the options, option &lsquo;b&rsquo; has the correct sequence.</p>",
                    solution_hi: "<p>11.(b) <strong>ACEBD</strong><br>दिया गया sentence, Part A से प्रारंभ होता है क्योंकि यह sentence के मुख्य विचार &lsquo;He had to take the bus&rsquo; को प्रस्तुत करता है। Part C गंतव्य (his office) बताता है और इसमे bus लेने का कारण बताने के लिए conjunction &lsquo;because&rsquo; है। Part E में bus लेने का कारण यह बताया गया है कि उसने कभी कार चलाना नहीं सीखा। इसलिए, C के बाद E आएगा। इसके अलावा, Part B उसके friend के बारे में बात करता है और Part E बताता है कि उसका friend छुट्टी पर था। इसलिए, B के बाद D आएगा। अतः options के माध्यम से जाने पर option &lsquo;b&rsquo; में सही sequence है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "7",
                    question_en: "<p>12. Select the option that expresses the following sentence in passive voice.<br>Ravi will play the match.</p>",
                    question_hi: "<p>12. Select the option that expresses the following sentence in passive voice.<br>Ravi will play the match.</p>",
                    options_en: ["<p>The match is played by Ravi.</p>", "<p>The match will have been played by Ravi.</p>", 
                                "<p>The match will be playing by Ravi.</p>", "<p>The match will be played by Ravi.</p>"],
                    options_hi: ["<p>The match is played by Ravi.</p>", "<p>The match will have been played by Ravi.</p>",
                                "<p>The match will be playing by Ravi.</p>", "<p>The match will be played by Ravi.</p>"],
                    solution_en: "<p>12.(d) The match will be played by Ravi. (Correct)<br>(a) The match<span style=\"text-decoration: underline;\"> is played by</span> Ravi. (Incorrect Tense)<br>(b) The match <span style=\"text-decoration: underline;\">will have been</span> played by Ravi. (Incorrect Helping Verb)<br>(c) The match will be <span style=\"text-decoration: underline;\">playing </span>by Ravi. (Incorrect form of the Verb)</p>",
                    solution_hi: "<p>12.(d) The match will be played by Ravi. (Correct)<br>(a) The match <span style=\"text-decoration: underline;\">is played</span> by Ravi. (गलत Tense)<br>(b) The match <span style=\"text-decoration: underline;\">will have been</span> played by Ravi. (गलत Helping Verb)<br>(c) The match will be <span style=\"text-decoration: underline;\">playing </span>by Ravi. (Verb की गलत form)</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "7",
                    question_en: "<p>13. Select the most appropriate option that can substitute the underlined word in the given sentence.<br>Our legal system must frame proper legal rules for <span style=\"text-decoration: underline;\">juvenile </span>criminals.</p>",
                    question_hi: "<p>13. Select the most appropriate option that can substitute the underlined word in the given sentence.<br>Our legal system must frame proper legal rules for <span style=\"text-decoration: underline;\">juvenile </span>criminals.</p>",
                    options_en: ["<p>senile</p>", "<p>dotage</p>", 
                                "<p>old</p>", "<p>young</p>"],
                    options_hi: ["<p>senile</p>", "<p>dotage</p>",
                                "<p>old</p>", "<p>young</p>"],
                    solution_en: "<p>13.(d) young<br>The given sentence states that our legal system must frame legal rules for young criminals. Hence, &lsquo;young&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>13.(d) young<br>दिए गए sentence में कहा गया है कि हमारी कानूनी व्यवस्था को युवा अपराधियों के लिए कानूनी नियम बनाने चाहिए। अतः, &lsquo;young&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "7",
                    question_en: "<p>14. Substitute the underlined word-segment with most appropriate idiom.<br>The doctors <span style=\"text-decoration: underline;\">tried very hard</span> to save his mother but they were unsuccessful.</p>",
                    question_hi: "<p>14. Substitute the underlined word-segment with most appropriate idiom.<br>The doctors<span style=\"text-decoration: underline;\"> tried very hard</span> to save his mother but they were unsuccessful.</p>",
                    options_en: ["<p>faced the music</p>", "<p>drew the line</p>", 
                                "<p>moved heaven and earth</p>", "<p>ran like clockwork</p>"],
                    options_hi: ["<p>faced the music</p>", "<p>drew the line</p>",
                                "<p>moved heaven and earth</p>", "<p>ran like clockwork</p>"],
                    solution_en: "<p>14.(c) <strong>Moved heaven and earth- </strong>tried very hard.</p>",
                    solution_hi: "<p>14.(c) <strong>Moved heaven and earth-</strong> tried very hard./बहुत प्रयास करना।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "7",
                    question_en: "<p>15. Select the most appropriate meaning of the given idiom.<br>Hit the nail on the head</p>",
                    question_hi: "<p>15. Select the most appropriate meaning of the given idiom.<br>Hit the nail on the head</p>",
                    options_en: ["<p>They are experiencing a headache.</p>", "<p>They have accurately identified or explained something.</p>", 
                                "<p>They\'re uncertain about a decision.</p>", "<p>They hit a nail with a hammer.</p>"],
                    options_hi: ["<p>They are experiencing a headache.</p>", "<p>They have accurately identified or explained something.</p>",
                                "<p>They\'re uncertain about a decision.</p>", "<p>They hit a nail with a hammer.</p>"],
                    solution_en: "<p>15.(b) <strong>Hit the nail on the head-</strong> they have accurately identified or explained something.<br>E.g.- When she said the project failed due to poor planning, she really hit the nail on the head.</p>",
                    solution_hi: "<p>15.(b)<strong> Hit the nail on the head-</strong> they have accurately identified or explained something./ऐसा कुछ करना या कहना जो बिल्कुल सही हो।<br>E.g.- When she said the project failed due to poor planning, she really hit the nail on the head.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "7",
                    question_en: "16. The following sentence has been split into four segments. Identify the segment that contains a grammatical error.<br />The windows / violent banged / during the / storm yesterday.",
                    question_hi: "16. The following sentence has been split into four segments. Identify the segment that contains a grammatical error.<br />The windows / violent banged / during the / storm yesterday.",
                    options_en: [" during the ", " The windows", 
                                " storm yesterday.", " violent banged"],
                    options_hi: [" during the ", " The windows",
                                " storm yesterday.", " violent banged"],
                    solution_en: "16.(d) violent banged<br />‘Violent’ must be replaced with ‘violently’ as the given sentence needs an adverb to modify the verb ‘banged’. Hence, ‘violently banged’ is the most appropriate answer.",
                    solution_hi: "16.(d) violent banged<br />‘Violent’ के स्थान पर ‘violently’ का प्रयोग होगा क्योंकि दिए गए sentence में  verb ‘banged’ को modify करने के लिए adverb की आवश्यकता है। अतः, ‘violently banged’ सबसे उपयुक्त उत्तर है।",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "7",
                    question_en: "17. Identify the INCORRECTLY spelt word in the following sentence and select its correct spelling from the given options.<br />His acquiantance with the author led to many fruitful collaborations.",
                    question_hi: "17. Identify the INCORRECTLY spelt word in the following sentence and select its correct spelling from the given options.<br />His acquiantance with the author led to many fruitful collaborations.",
                    options_en: [" acuiantance ", " acquaintance", 
                                " acquaintence", " aquaintance"],
                    options_hi: [" acuiantance ", " acquaintance",
                                " acquaintence", " aquaintance"],
                    solution_en: "17.(b) acquaintance<br />\'Acquaintance\' is the correct spelling.",
                    solution_hi: "17.(b) acquaintance<br />\'Acquaintance\' सही spelling है। ",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "7",
                    question_en: "<p>18. Identify the correct synonym of the word given below.<br>FATIGUE</p>",
                    question_hi: "<p>18. Identify the correct synonym of the word given below.<br>FATIGUE</p>",
                    options_en: ["<p>Mission</p>", "<p>Tiredness</p>", 
                                "<p>Vigour</p>", "<p>Obese</p>"],
                    options_hi: ["<p>Mission</p>", "<p>Tiredness</p>",
                                "<p>Vigour</p>", "<p>Obese</p>"],
                    solution_en: "<p>18.(b) <strong>Tiredness</strong>- state of being weary or fatigued.<br><strong>Fatigue</strong>- extreme tiredness resulting from mental or physical exertion.<br><strong>Mission</strong>- an important task or purpose assigned to a person or group.<br><strong>Vigour</strong>- physical strength and good health.<br><strong>Obese</strong>- having an unhealthy amount of body fat.</p>",
                    solution_hi: "<p>18.(b) <strong>Tiredness </strong>(थकावट)- state of being weary or fatigued.<br><strong>Fatigue </strong>(थकान)- extreme tiredness resulting from mental or physical exertion.<br><strong>Mission </strong>(उद्देश्य)- an important task or purpose assigned to a person or group.<br><strong>Vigour </strong>(शक्ति)- physical strength and good health.<br><strong>Obese </strong>(मोटापा)- having an unhealthy amount of body fat.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "7",
                    question_en: "<p>19. Select the most appropriate synonym of the underlined word.<br>She was <span style=\"text-decoration: underline;\">overstrung </span>before her dance performance.</p>",
                    question_hi: "<p>19. Select the most appropriate synonym of the underlined word.<br>She was <span style=\"text-decoration: underline;\">overstrung </span>before her dance performance.</p>",
                    options_en: ["<p>Cheerful</p>", "<p>Calm</p>", 
                                "<p>Placid</p>", "<p>Nervous</p>"],
                    options_hi: ["<p>Cheerful</p>", "<p>Calm</p>",
                                "<p>Placid</p>", "<p>Nervous</p>"],
                    solution_en: "<p>19.(d) <strong>Nervous</strong>- easily agitated or anxious.<br><strong>Overstrung</strong>- excessively tense or anxious.<br><strong>Cheerful</strong>- noticeably happy and optimistic.<br><strong>Calm</strong>- free from agitation or strong emotion.<br><strong>Placid</strong>- not easily upset or excited.</p>",
                    solution_hi: "<p>19.(d) <strong>Nervous </strong>(परेशान)- easily agitated or anxious.<br><strong>Overstrung </strong>(तनावग्रस्त)- excessively tense or anxious.<br><strong>Cheerful </strong>(प्रसन्नचित्त)- noticeably happy and optimistic.<br><strong>Calm </strong>(शांत)- free from agitation or strong emotion.<br><strong>Placid </strong>(सौम्य/शांत)- not easily upset or excited.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "7",
                    question_en: "<p>20. Select the option that can be used as a one-word substitute for the given group of words.<br>Awkward in movement or manner</p>",
                    question_hi: "<p>20. Select the option that can be used as a one-word substitute for the given group of words.<br>Awkward in movement or manner</p>",
                    options_en: ["<p>Alert</p>", "<p>Imbalanced</p>", 
                                "<p>Clumsy</p>", "<p>Clownish</p>"],
                    options_hi: ["<p>Alert</p>", "<p>Imbalanced</p>",
                                "<p>Clumsy</p>", "<p>Clownish</p>"],
                    solution_en: "<p>20.(c) <strong>Clumsy</strong>- awkward in movement or manner.<br><strong>Alert</strong>- quick to notice any unusual and potentially dangerous or difficult circumstances.<br><strong>Imbalanced</strong>- a situation in which two or more things are not equal in size, power, importance, etc.<br><strong>Clownish</strong>- someone who entertains people by wearing funny clothes with a painted face, and making people laugh.</p>",
                    solution_hi: "<p>20.(c) <strong>Clumsy </strong>(अनाड़ी/बेढंगा)- awkward in movement or manner.<br><strong>Alert </strong>(सतर्क)- quick to notice any unusual and potentially dangerous or difficult circumstances.<br><strong>Imbalanced </strong>(असंतुलित)- a situation in which two or more things are not equal in size, power, <strong>importance</strong>, etc.<br><strong>Clownish </strong>(मसख़रा)- someone who entertains people by wearing funny clothes with a painted face, and making people laugh.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "7",
                    question_en: "<p>21.<strong>Cloze Test:</strong><br>Azadi Ka Amrit Mahotsav is an (21) _________ by the Government of India to celebrate and remember 75 years of independence and the beautiful history of its people, culture and achievements. This Mahotsav is (22) _________ to the people of India, who have not only been instrumental in bringing India this far in its evolutionary journey but also (23) _________ the power and potential to realise Prime Minister, Narendra Modi\'s (24) _________ of activating India 2.0, fuelled by the spirit of Aatmanirbhar Bharat. The formal journey of Azadi Ka Amrit Mahotsav began on 12 March 2021, beginning a (25)______ to our 75th anniversary of independence and will finish a year later on 15 August 2023.<br>Select the most appropriate answer for blank number 21.</p>",
                    question_hi: "<p>21. <strong>Cloze Test:</strong><br>Azadi Ka Amrit Mahotsav is an (21) _________ by the Government of India to celebrate and remember 75 years of independence and the beautiful history of its people, culture and achievements. This Mahotsav is (22) _________ to the people of India, who have not only been instrumental in bringing India this far in its evolutionary journey but also (23) _________ the power and potential to realise Prime Minister, Narendra Modi\'s (24) _________ of activating India 2.0, fuelled by the spirit of Aatmanirbhar Bharat. The formal journey of Azadi Ka Amrit Mahotsav began on 12 March 2021, beginning a (25)______ to our 75th anniversary of independence and will finish a year later on 15 August 2023.<br>Select the most appropriate answer for blank number 21.</p>",
                    options_en: ["<p>associate</p>", "<p>initiative</p>", 
                                "<p>obligation</p>", "<p>occult</p>"],
                    options_hi: ["<p>associate</p>", "<p>initiative</p>",
                                "<p>obligation</p>", "<p>occult</p>"],
                    solution_en: "<p>21.(b) <strong>initiative</strong><br>&lsquo;Initiative&rsquo; means an introductory step. The given passage states that Azadi Ka Amrit Mahotsav is an initiative by the Government of India to celebrate and remember 75 years of independence. Hence, \'initiative\' is the most appropriate answer.</p>",
                    solution_hi: "<p>21.(b) <strong>initiative</strong><br>&lsquo;Initiative&rsquo; का अर्थ है एक प्रारम्भिक कदम। दिए गए passage में कहा गया है कि आज़ादी का अमृत महोत्सव भारत सरकार द्वारा स्वतंत्रता के 75 वर्ष मनाने और याद करने की एक पहल (initiative) है। अतः, \'initiative\' सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "7",
                    question_en: "<p>22.<strong> Cloze Test:</strong><br>Azadi Ka Amrit Mahotsav is an (21) _________ by the Government of India to celebrate and remember 75 years of independence and the beautiful history of its people, culture and achievements. This Mahotsav is (22) _________ to the people of India, who have not only been instrumental in bringing India this far in its evolutionary journey but also (23) _________ the power and potential to realise Prime Minister, Narendra Modi\'s (24) _________ of activating India 2.0, fuelled by the spirit of Aatmanirbhar Bharat. The formal journey of Azadi Ka Amrit Mahotsav began on 12 March 2021, beginning a (25)______ to our 75th anniversary of independence and will finish a year later on 15 August 2023.<br>Select the most appropriate answer for blank number 22.</p>",
                    question_hi: "<p>22. <strong>Cloze Test:</strong><br>Azadi Ka Amrit Mahotsav is an (21) _________ by the Government of India to celebrate and remember 75 years of independence and the beautiful history of its people, culture and achievements. This Mahotsav is (22) _________ to the people of India, who have not only been instrumental in bringing India this far in its evolutionary journey but also (23) _________ the power and potential to realise Prime Minister, Narendra Modi\'s (24) _________ of activating India 2.0, fuelled by the spirit of Aatmanirbhar Bharat. The formal journey of Azadi Ka Amrit Mahotsav began on 12 March 2021, beginning a (25)______ to our 75th anniversary of independence and will finish a year later on 15 August 2023.<br>Select the most appropriate answer for blank number 22.</p>",
                    options_en: ["<p>dedicated</p>", "<p>alienated</p>", 
                                "<p>refused</p>", "<p>withhold</p>"],
                    options_hi: ["<p>dedicated</p>", "<p>alienated</p>",
                                "<p>refused</p>", "<p>withhold</p>"],
                    solution_en: "<p>22.(a) <strong>dedicated</strong><br>&lsquo;Dedicated&rsquo; means intended for a particular person. The given passage states that this Mahotsav is dedicated to the people of India. Hence, &lsquo;dedicated&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>22.(a) <strong>dedicated</strong><br>&lsquo;Dedicated&rsquo; का अर्थ है किसी विशेष व्यक्ति के लिए प्रायोजित। दिए गए passage में कहा गया है कि यह महोत्सव भारत के लोगों को समर्पित है। अतः, &lsquo;dedicated&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "23",
                    section: "7",
                    question_en: "<p>23.<strong> Cloze Test:</strong><br>Azadi Ka Amrit Mahotsav is an (21) _________ by the Government of India to celebrate and remember 75 years of independence and the beautiful history of its people, culture and achievements. This Mahotsav is (22) _________ to the people of India, who have not only been instrumental in bringing India this far in its evolutionary journey but also (23) _________ the power and potential to realise Prime Minister, Narendra Modi\'s (24) _________ of activating India 2.0, fuelled by the spirit of Aatmanirbhar Bharat. The formal journey of Azadi Ka Amrit Mahotsav began on 12 March 2021, beginning a (25)______ to our 75th anniversary of independence and will finish a year later on 15 August 2023.<br>Select the most appropriate answer for blank number 23.</p>",
                    question_hi: "<p>23. <strong>Cloze Test:</strong><br>Azadi Ka Amrit Mahotsav is an (21) _________ by the Government of India to celebrate and remember 75 years of independence and the beautiful history of its people, culture and achievements. This Mahotsav is (22) _________ to the people of India, who have not only been instrumental in bringing India this far in its evolutionary journey but also (23) _________ the power and potential to realise Prime Minister, Narendra Modi\'s (24) _________ of activating India 2.0, fuelled by the spirit of Aatmanirbhar Bharat. The formal journey of Azadi Ka Amrit Mahotsav began on 12 March 2021, beginning a (25)______ to our 75th anniversary of independence and will finish a year later on 15 August 2023.<br>Select the most appropriate answer for blank number 23.</p>",
                    options_en: ["<p>clarifies</p>", "<p>possess</p>", 
                                "<p>concludes</p>", "<p>obtains</p>"],
                    options_hi: ["<p>clarifies</p>", "<p>possess</p>",
                                "<p>concludes</p>", "<p>obtains</p>"],
                    solution_en: "<p>23.(b) <strong>possess</strong><br>The given passage states that this Mahotsav is dedicated to the people of India who possess the power and potential to realise Prime Minister Narendra Modi&rsquo;s vision of activating India 2.O. Hence, &lsquo;possess&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>23.(b) <strong>possess</strong><br>दिए गए passage में कहा गया है कि यह महोत्सव भारत के उन लोगों को समर्पित है, जिनके पास प्रधानमंत्री नरेंद्र मोदी के India 2.O को सक्रिय करने के सपने को साकार करने की शक्ति और क्षमता(potential) है। अतः, &lsquo;possess&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "24",
                    section: "7",
                    question_en: "<p>24. <strong>Cloze Test:</strong><br>Azadi Ka Amrit Mahotsav is an (21) _________ by the Government of India to celebrate and remember 75 years of independence and the beautiful history of its people, culture and achievements. This Mahotsav is (22) _________ to the people of India, who have not only been instrumental in bringing India this far in its evolutionary journey but also (23) _________ the power and potential to realise Prime Minister, Narendra Modi\'s (24) _________ of activating India 2.0, fuelled by the spirit of Aatmanirbhar Bharat. The formal journey of Azadi Ka Amrit Mahotsav began on 12 March 2021, beginning a (25)______ to our 75th anniversary of independence and will finish a year later on 15 August 2023.<br>Select the most appropriate answer for blank number 24.</p>",
                    question_hi: "<p>24. <strong>Cloze Test:</strong><br>Azadi Ka Amrit Mahotsav is an (21) _________ by the Government of India to celebrate and remember 75 years of independence and the beautiful history of its people, culture and achievements. This Mahotsav is (22) _________ to the people of India, who have not only been instrumental in bringing India this far in its evolutionary journey but also (23) _________ the power and potential to realise Prime Minister, Narendra Modi\'s (24) _________ of activating India 2.0, fuelled by the spirit of Aatmanirbhar Bharat. The formal journey of Azadi Ka Amrit Mahotsav began on 12 March 2021, beginning a (25)______ to our 75th anniversary of independence and will finish a year later on 15 August 2023.<br>Select the most appropriate answer for blank number 24.</p>",
                    options_en: ["<p>graphics</p>", "<p>vision</p>", 
                                "<p>design</p>", "<p>control</p>"],
                    options_hi: ["<p>graphics</p>", "<p>vision</p>",
                                "<p>design</p>", "<p>control</p>"],
                    solution_en: "<p>24.(b) <strong>vision</strong><br>&lsquo;Vision&rsquo; means the ability to think about or plan the future with imagination or wisdom. The given passage states that This Mahotsav is dedicated to the people of India, who have not only been instrumental in bringing India this far in its evolutionary journey but also possess the power and potential to realise Prime Minister Narendra Modi\'s vision of activating India 2.0. Hence, &lsquo;vision&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>24.(b) <strong>vision</strong><br>&lsquo;Vision&rsquo; का अर्थ है कल्पना या बुद्धिमत्ता से भविष्य के बारे में सोचने या योजना बनाने की क्षमता। दिए गए passage में कहा गया है कि यह महोत्सव भारत के लोगों को समर्पित है, जिन्होंने न केवल भारत को अपनी विकास यात्रा में इतना आगे लाने में महत्वपूर्ण भूमिका निभाई है, बल्कि प्रधानमंत्री Narendra Modi के India 2.0 को सक्रिय करने के vision को साकार करने की शक्ति और क्षमता भी रखते हैं। अतः, &lsquo;vision&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "25",
                    section: "misc",
                    question_en: "<p>25.<strong> Cloze Test:</strong><br>Azadi Ka Amrit Mahotsav is an (21) _________ by the Government of India to celebrate and remember 75 years of independence and the beautiful history of its people, culture and achievements. This Mahotsav is (22) _________ to the people of India, who have not only been instrumental in bringing India this far in its evolutionary journey but also (23) _________ the power and potential to realise Prime Minister, Narendra Modi\'s (24) _________ of activating India 2.0, fuelled by the spirit of Aatmanirbhar Bharat. The formal journey of Azadi Ka Amrit Mahotsav began on 12 March 2021, beginning a (25)______ to our 75th anniversary of independence and will finish a year later on 15 August 2023.<br>Select the most appropriate answer for blank number 25.</p>",
                    question_hi: "<p>25. <strong>Cloze Test:</strong><br>Azadi Ka Amrit Mahotsav is an (21) _________ by the Government of India to celebrate and remember 75 years of independence and the beautiful history of its people, culture and achievements. This Mahotsav is (22) _________ to the people of India, who have not only been instrumental in bringing India this far in its evolutionary journey but also (23) _________ the power and potential to realise Prime Minister, Narendra Modi\'s (24) _________ of activating India 2.0, fuelled by the spirit of Aatmanirbhar Bharat. The formal journey of Azadi Ka Amrit Mahotsav began on 12 March 2021, beginning a (25)______ to our 75th anniversary of independence and will finish a year later on 15 August 2023.<br>Select the most appropriate answer for blank number 25.</p>",
                    options_en: ["<p>allocation</p>", "<p>countdown</p>", 
                                "<p>custom</p>", "<p>breakdown</p>"],
                    options_hi: ["<p>allocation</p>", "<p>countdown</p>",
                                "<p>custom</p>", "<p>breakdown</p>"],
                    solution_en: "<p>25.(b) <strong>countdown</strong><br>&lsquo;Countdown&rsquo; means an act of counting numerals in reverse order to zero. The given passage states that the formal journey of Azadi Ka Amrit Mahotsav began on 12 March 2021, beginning a countdown to our 75th anniversary of independence and will finish a year later on 15 August 2023. Hence, &lsquo;countdown&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>25.(b) <strong>countdown</strong><br>&lsquo;Countdown&rsquo; का अर्थ है शून्य तक उल्टे क्रम में अंकों को गिनने की क्रिया। दिए गए passage में कहा गया है कि आजादी का अमृत महोत्सव की औपचारिक यात्रा(formal journey) 12 मार्च 2021 को शुरू हुई, जिससे हमारी स्वतंत्रता की 75वीं anniversary का countdown शुरू हुआ और एक वर्ष बाद 15 अगस्त 2023 को समाप्त होगा। अतः, &lsquo;countdown&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>