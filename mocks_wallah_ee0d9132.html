<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #7209b7;
            --secondary: #4361ee;
            --accent: #f72585;
            --success: #06d6a0;
            --warning: #ffd166;
            --danger: #ef476f;
            --light: #f8f9fa;
            --dark: #212529;
        }
        body {
            background-color: #f0f2f5;
            font-size: 16px;
            padding-bottom: 60px; /* Space for fixed navigation */
        }
        .navbar {
            background: linear-gradient(135deg, var(--primary), var(--secondary)) !important;
            box-shadow: 0 2px 10px rgba(0,0,0,.1);
            padding: 0.5rem 1rem;
        }
        .navbar-brand {
            font-size: 1.1rem;
            margin-right: 0;
        }
        .navbar .container-fluid {
            justify-content: space-between;
        }
        .controls-group {
            display: flex;
            align-items: center;
            flex-wrap: nowrap;
        }
        .timer-display {
            background: linear-gradient(135deg, #4361ee, #3a0ca3);
            color: white;
            border-radius: 50px;
            box-shadow: 0 3px 8px rgba(0,0,0,.1);
            padding: 0.25rem 0.75rem;
            font-size: 0.875rem;
            white-space: nowrap;
        }
        .nav-btn {
            padding: 0.25rem 0.75rem;
            font-size: 0.875rem;
            margin-left: 0.25rem;
        }
        .question-nav {
            position: fixed;
            top: 56px;
            right: 0;
            height: calc(100vh - 56px);
            width: 280px;
            transform: translateX(280px);
            transition: transform .3s;
            z-index: 100;
            background: #fff;
            box-shadow: -5px 0 15px rgba(0,0,0,.1);
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .main-content {
            transition: margin .3s;
            min-height: calc(100vh - 116px);
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.5;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(32px, 1fr));
            gap: 6px;
        }
        .q-box {
            width: 35px;
            height: 35px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 5px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid #dee2e6;
            background: #fff;
            transition: all .2s;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: 0 3px 5px rgba(0,0,0,.1);
        }
        .q-box.attempted {
            background-color: var(--secondary);
            color: white;
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,.15);
        }
        .card {
            border-radius: 12px;
            overflow: hidden;
            border: none;
            box-shadow: 0 4px 12px rgba(0,0,0,.08);
        }
        .option-label {
            border-radius: 8px;
            transition: all .2s;
            border: 1px solid #dee2e6;
            padding: 0.75rem 1rem;
            font-size: 1rem;
        }
        .option-label:hover {
            background-color: #f8f9fa;
            transform: translateY(-2px);
            box-shadow: 0 3px 8px rgba(0,0,0,.05);
        }
        .form-check-input:checked + .option-label {
            border-color: var(--primary);
            background-color: rgba(114,9,183,.05);
        }
        .btn-primary {
            background-color: var(--primary);
            border-color: var(--primary);
        }
        .btn-outline-primary {
            color: var(--primary);
            border-color: var(--primary);
        }
        .btn-outline-primary:hover {
            background-color: var(--primary);
            color: white;
        }
        /* Bottom fixed navigation for mobile */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: white;
            box-shadow: 0 -2px 10px rgba(0,0,0,.1);
            padding: 10px 15px;
            display: flex;
            justify-content: space-between;
            z-index: 90;
        }
        @media (min-width: 992px) {
            .main-content.with-nav {
                margin-right: 280px;
            }
            .question-nav {
                transform: translateX(0);
            }
            .nav-toggle-btn {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(30px, 1fr));
            }
            .option-label {
                padding: 0.625rem 0.875rem;
                font-size: 0.95rem;
            }
            .question-text {
                font-size: 1rem;
            }
            .navbar-brand {
                font-size: 1rem;
            }
            .timer-display {
                font-size: 0.8125rem;
                padding: 0.2rem 0.6rem;
            }
            .nav-btn {
                padding: 0.2rem 0.6rem;
                font-size: 0.8125rem;
            }
            .container {
                padding-left: 10px;
                padding-right: 10px;
            }
        }
    </style>
</head>
<body>
    <!-- Header navbar -->
    <nav class="navbar navbar-dark sticky-top">
        <div class="container-fluid px-2">
            <a class="navbar-brand fw-bold" href="#"><i class="fas fa-graduation-cap me-1"></i></a>
            <div class="controls-group">
                <!-- Timer -->
                <div class="timer-display me-1 fw-bold" id="timer">
                    <i class="far fa-clock me-1"></i><span id="timer-display">90:00</span>
                </div>
                <!-- Submit button -->
                <button class="btn btn-light btn-sm nav-btn me-1" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Language toggle -->
                <button class="btn btn-light btn-sm nav-btn me-1" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="btn btn-light btn-sm nav-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </nav>
    <!-- Main content -->
    <div class="main-content" id="main-content">
        <div class="container py-3">
            <!-- Question counter -->
            <div class="fw-bold text-center mb-3" id="question-counter">Question 1 of 25</div>
            <!-- Questions will be displayed here -->
            <div id="questions-container"></div>
        </div>
    </div>
    <!-- Bottom navigation for mobile -->
    <div class="bottom-nav d-lg-none">
        <button class="btn btn-outline-primary px-3" onclick="prevQuestion()">
            <i class="fas fa-chevron-left me-1"></i> Previous
        </button>
        <button class="btn btn-outline-primary px-3" onclick="nextQuestion()">
            Next <i class="fas fa-chevron-right ms-1"></i>
        </button>
    </div>
    <!-- Question navigation sidebar -->
    <div class="question-nav" id="question-nav">
        <div class="p-3">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h5 class="m-0">Questions</h5>
                <div>
                    <span class="badge bg-secondary me-1" id="attempted-count">0</span>
                    <span class="badge bg-secondary" id="total-count">25</span>
                    <button class="btn btn-sm btn-outline-secondary ms-2 d-lg-none" onclick="toggleNav()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
            <!-- Section selector moved here -->
            <div class="mb-3">
                <label for="section-selector" class="form-label small text-muted">Jump to Section</label>
                <select class="form-select form-select-sm" id="section-selector" onchange="switchSection(this.value)">
                    <option value="all">All Sections</option>
                </select>
            </div>
            <div class="question-grid" id="question-boxes"></div>
        </div>
    </div>
    <!-- Results modal -->
    <div class="modal fade" id="results-modal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content border-0">
                <div class="modal-header border-0" style="background:linear-gradient(135deg,var(--primary),var(--secondary));color:white">
                    <h5 class="modal-title">Test Results</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body p-4">
                    <div class="row text-center g-2">
                        <div class="col-6">
                            <div class="p-3 rounded shadow-sm" style="background:linear-gradient(135deg,#4361ee,#3a0ca3);color:white">
                                <h3 id="score-value" class="mb-0 fw-bold">0</h3>
                                <div class="small">Score</div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="p-3 rounded shadow-sm" style="background:linear-gradient(135deg,#06d6a0,#1b9aaa);color:white">
                                <h3 id="correct-value" class="mb-0 fw-bold">0</h3>
                                <div class="small">Correct</div>
                            </div>
                        </div>
                        <div class="col-6 mt-2">
                            <div class="p-3 rounded shadow-sm" style="background:linear-gradient(135deg,#ef476f,#f72585);color:white">
                                <h3 id="incorrect-value" class="mb-0 fw-bold">0</h3>
                                <div class="small">Incorrect</div>
                            </div>
                        </div>
                        <div class="col-6 mt-2">
                            <div class="p-3 rounded shadow-sm" style="background:linear-gradient(135deg,#6c757d,#495057);color:white">
                                <h3 id="unattempted-value" class="mb-0 fw-bold">0</h3>
                                <div class="small">Unattempted</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer border-0">
                    <button type="button" class="btn btn-primary" onclick="reviewTest()">Review Test</button>
                </div>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 90 * 60;
        let resultsModal;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            resultsModal = new bootstrap.Modal(document.getElementById('results-modal'));
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["7"] = {
                name: "English",
                start: 0,
                end: 23
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="7">English</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 24,
                end: 24
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "7",
                    question_en: "<p>1. Select the option that expresses the given sentence in passive voice.</p>\r\n<p><span style=\"font-family: Times New Roman;\">Aditya passed the driving test quite easily. </span></p>",
                    question_hi: "",
                    options_en: ["<p>The driving test had passed quite easily by Aditya.</p>", "<p>The driving test have been passed quite easily by Aditya.</p>", 
                                "<p>The driving test could passed quite easily by Aditya.</p>", "<p>The driving test was passed quite easily by Aditya.</p>"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: "<p>1.(d)</p>\r\n<p><span style=\"font-weight: 400;\">&nbsp;(a) The driving test </span><span style=\"text-decoration: underline;\"><span style=\"font-weight: 400;\">had</span></span><span style=\"font-weight: 400;\"> passed quite easily by Aditya. (Incorrect Tense)</span></p>\r\n<p><span style=\"font-weight: 400;\">&nbsp;(b) The driving test </span><span style=\"text-decoration: underline;\"><span style=\"font-weight: 400;\">have been</span></span><span style=\"font-weight: 400;\"> passed quite easily by Aditya. (Incorrect Tense)</span></p>\r\n<p><span style=\"font-weight: 400;\">&nbsp;(c) The driving test </span><span style=\"text-decoration: underline;\"><span style=\"font-weight: 400;\">could</span></span><span style=\"font-weight: 400;\"> passed quite easily by Aditya. (Incorrect use of modal)</span></p>\r\n<p><span style=\"font-weight: 400;\">&nbsp;(d) The driving test was passed quite easily by Aditya. (Correct)</span></p>",
                    solution_hi: "<p>1.(d)</p>\r\n<p><span style=\"font-weight: 400;\">&nbsp;(a) The driving test </span><span style=\"text-decoration: underline;\"><span style=\"font-weight: 400;\">had</span></span><span style=\"font-weight: 400;\"> passed quite easily by Aditya. (Incorrect Tense)</span></p>\r\n<p><span style=\"font-weight: 400;\">&nbsp;(b) The driving test </span><span style=\"text-decoration: underline;\"><span style=\"font-weight: 400;\">have been</span></span><span style=\"font-weight: 400;\"> passed quite easily by Aditya. (Incorrect Tense)</span></p>\r\n<p><span style=\"font-weight: 400;\">&nbsp;(c) The driving test </span><span style=\"text-decoration: underline;\"><span style=\"font-weight: 400;\">could</span></span><span style=\"font-weight: 400;\"> passed quite easily by Aditya. (Incorrect use of modal)</span></p>\r\n<p><span style=\"font-weight: 400;\">&nbsp;(d) The driving test was passed quite easily by Aditya. (Correct)</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "7",
                    question_en: " <p>2. Select the most appropriate meaning of the given idiom. </span></p> <p><span style=\"font-family:Times New Roman\">To cut corners </span></p>",
                    question_hi: "",
                    options_en: [" <p> cut something easily without spoiling it </span></p>", " <p> cut something into pieces </span></p>", 
                                " <p> be able to do a job effortlessly </span></p>", " <p> not do a thing well in order to save money or effort </span></p>"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: " <p>2.(d)</span></p> <p><span style=\"font-family:Times New Roman\">To cut corners- not do a thing well in order to save money or effort </span></p> <p><span style=\"font-family:Times New Roman\">Example- The pathetic condition of the road in our village clearly indicates that the authority has cut corners while building it. </span></p>",
                    solution_hi: " <p>2.(d)</span></p> <p><span style=\"font-family:Times New Roman\">To cut corners- not do a thing well in order to save money or effort </span></p> <p><span style=\"font-family:Times New Roman\">Example- The pathetic condition of the road in our village clearly indicates that the authority has cut corners while building it. </span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "7",
                    question_en: " <p>3. Select the most appropriate meaning of the given idiom. </span></p> <p><span style=\"font-family:Times New Roman\">Cook the books </span></p>",
                    question_hi: "",
                    options_en: [" <p> To falsify financial records </span></p>", " <p> To be a good writer </span></p>", 
                                " <p> To write books on cooking </span></p>", " <p> To be a good editor </span></p>"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: " <p>3.(a)</span></p> <p><span style=\"font-family:Times New Roman\">Cook the books- to falsify financial records </span></p> <p><span style=\"font-family:Times New Roman\">Example- When the manager found that the clerk had cooked the books he immediately suspended him.</span></p>",
                    solution_hi: " <p>3.(a)</span></p> <p><span style=\"font-family:Times New Roman\">Cook the books- to falsify financial records </span></p> <p><span style=\"font-family:Times New Roman\">Example- When the manager found that the clerk had cooked the books he immediately suspended him.</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "7",
                    question_en: " <p>4. In the given sentence, identify the segment which contains a grammatical error. </span></p> <p><span style=\"font-family:Times New Roman\">Doctors say that home isolation is far more effective than hospitalize in cases of mild infection. </span></p>",
                    question_hi: "",
                    options_en: [" <p> far more effective </span></p>", " <p> than hospitalise </span></p>", 
                                " <p> isolation is </span></p>", " <p> Doctors say that</span></p>"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: " <p>4.(b)</span></p> <p><span style=\"font-family:Times New Roman\">Hospitalization means </span><span style=\"font-family:Times New Roman\">admission to the hospital for treatment. Its a noun and a noun will be used here and not a verb. The given sentence states that </span><span style=\"font-family:Times New Roman\">home isolation is far more effective than </span><span style=\"font-family:Times New Roman\">going to the hospital for treatment. Hence, ‘</span><span style=\"font-family:Times New Roman\">than hospitalization</span><span style=\"font-family:Times New Roman\">’ is the most appropriate answer.</span></p>",
                    solution_hi: " <p>4.(b)</span></p> <p><span style=\"font-family:Times New Roman\">Hospitalization means </span><span style=\"font-family:Times New Roman\">admission to the hospital for treatment. Its a noun and a noun will be used here and not a verb. The given sentence states that </span><span style=\"font-family:Times New Roman\">home isolation is far more effective than </span><span style=\"font-family:Times New Roman\">going to the hospital for treatment. Hence, ‘</span><span style=\"font-family:Times New Roman\">than hospitalization</span><span style=\"font-family:Times New Roman\">’ is the most appropriate answer.</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "7",
                    question_en: "<p>5. Select the most appropriate option to substitute the underlined segment in the given <span style=\"font-family: Times New Roman;\">sentence. If there is no need to substitute it, select &lsquo;No substitution required&rsquo;.&nbsp; </span></p>\r\n<p><span style=\"text-decoration: underline;\"><strong><span style=\"font-family: Times New Roman;\">Sometimes the family liking</span></strong></span><span style=\"font-family: Times New Roman;\"> to play a game together after dinner. </span></p>\n",
                    question_hi: "",
                    options_en: ["<p>Sometimes the family likes</p>\n", "<p>Sometime the family likes</p>\n", 
                                "<p>No substitution required</p>\n", "<p>Sometimes the family are like</p>\n"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: "<p>5.(a)</p>\r\n<p><span style=\"font-family: Times New Roman;\">The given sentence is in the present tense so the verb must be used in its present/base form(likes) and not in the present continuous form(liking). Hence, &lsquo;liking&rsquo; will be replaced by &lsquo;likes&rsquo; to make the given sentence grammatically correct.</span></p>\n",
                    solution_hi: "<p>5.(a)</p>\r\n<p><span style=\"font-family: Times New Roman;\">The given sentence is in the present tense so the verb must be used in its present/base form(likes) and not in the present continuous form(liking). Hence, &lsquo;liking&rsquo; will be replaced by &lsquo;likes&rsquo; to make the given sentence grammatically correct.</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "7",
                    question_en: " <p>6. Select the most appropriate word to fill in the blank.   </span></p> <p><span style=\"font-family:Times New Roman\">Have you thought about the ______ of the recent decision you took? </span></p>",
                    question_hi: "",
                    options_en: [" <p> conclusions </span></p>", " <p> complexes </span></p>", 
                                " <p> consequences </span></p>", " <p> concerns </span></p>"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: " <p>6.(c)</span></p> <p><span style=\"font-family:Times New Roman\">Consequences means a result of something.</span></p> <p><span style=\"font-family:Times New Roman\">The given sentence asks a question that have you thought about the result of the recent decision you took. Hence, ‘consequences’ is the most appropriate answer.</span></p>",
                    solution_hi: " <p>6.(c)</span></p> <p><span style=\"font-family:Times New Roman\">Consequences means a result of something.</span></p> <p><span style=\"font-family:Times New Roman\">The given sentence asks a question that have you thought about the result of the recent decision you took. Hence, ‘consequences’ is the most appropriate answer.</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "7",
                    question_en: "<p>7. Given below are four jumbled sentences. Out of the given options, select the one that gives <span style=\"font-family: Times New Roman;\">their correct order. </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">A. On either side of the trunk there are a few scattered tufts of short grass. </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">B. It is towards one of these fallen trees that I am now looking. </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">C. A very violent cyclonic storm has struck the forest, uprooting a number of trees. </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">D. The trunk of the tree is ripped apart and the branches are lying all around it. </span></p>\n",
                    question_hi: "",
                    options_en: ["<p>ABDC</p>\n", "<p>ADBC</p>\n", 
                                "<p>CBDA</p>\n", "<p>CDAB</p>\n"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: "<p>7.(c) CBDA</p>\r\n<p><span style=\"font-family: Times New Roman;\">Sentence C will be the starting line as it contains the main idea of the parajumble i.e. a violent cyclonic storm. However, Sentence B states that the narrator is looking towards one of these fallen trees. So, B will follow C. Further, Sentence D states that the trunk of the tree is ripped apart and the branches are lying all around it &amp; Sentence A states that there are a few scattered tufts of short grass on either side of the trunk. So, A will follow D. Going through the options, option c has the correct sequence. </span></p>\n",
                    solution_hi: "<p>7.(c) CBDA</p>\r\n<p><span style=\"font-family: Times New Roman;\">Sentence C will be the starting line as it contains the main idea of the parajumble i.e. a violent cyclonic storm. However, Sentence B states that the narrator is looking towards one of these fallen trees. So, B will follow C. Further, Sentence D states that the trunk of the tree is ripped apart and the branches are lying all around it &amp; Sentence A states that there are a few scattered tufts of short grass on either side of the trunk. So, A will follow D. Going through the options, option c has the correct sequence. </span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "7",
                    question_en: " <p>8. Select the most appropriate synonym of the given word. </span></p> <p><span style=\"font-family:Times New Roman\">Paranoid </span></p>",
                    question_hi: "",
                    options_en: [" <p> Impeccable </span></p>", " <p> Suspicious </span></p>", 
                                " <p> Insignificant </span></p>", " <p> Patronising </span></p>"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: " <p>8.(b) Suspicious</span></p> <p><span style=\"font-family:Times New Roman\">Suspicious- </span><span style=\"font-family:Times New Roman\">feeling that somebody has done something wrong, dishonest or illegal</span></p> <p><span style=\"font-family:Times New Roman\">Paranoid- </span><span style=\"font-family:Times New Roman\">wrongly believing that other people are trying to harm you or are saying bad things about you</span><span style=\"font-family:Times New Roman\"> </span></p> <p><span style=\"font-family:Times New Roman\">Impeccable- </span><span style=\"font-family:Times New Roman\">without any mistakes or faults, perfect</span><span style=\"font-family:Times New Roman\"> </span></p> <p><span style=\"font-family:Times New Roman\">Insignificant- </span><span style=\"font-family:Times New Roman\">of little value or importance</span><span style=\"font-family:Times New Roman\"> </span></p> <p><span style=\"font-family:Times New Roman\">Patronizing- </span><span style=\"font-family:Times New Roman\">to treat somebody in a way that shows that you think you are better, more intelligent, experienced, etc. than he/she is</span></p>",
                    solution_hi: " <p>8.(b) Suspicious</span></p> <p><span style=\"font-family:Times New Roman\">Suspicious- </span><span style=\"font-family:Times New Roman\">feeling that somebody has done something wrong, dishonest or illegal</span></p> <p><span style=\"font-family:Times New Roman\">Paranoid- </span><span style=\"font-family:Times New Roman\">wrongly believing that other people are trying to harm you or are saying bad things about you</span><span style=\"font-family:Times New Roman\"> </span></p> <p><span style=\"font-family:Times New Roman\">Impeccable- </span><span style=\"font-family:Times New Roman\">without any mistakes or faults, perfect</span><span style=\"font-family:Times New Roman\"> </span></p> <p><span style=\"font-family:Times New Roman\">Insignificant- </span><span style=\"font-family:Times New Roman\">of little value or importance</span><span style=\"font-family:Times New Roman\"> </span></p> <p><span style=\"font-family:Times New Roman\">Patronizing- </span><span style=\"font-family:Times New Roman\">to treat somebody in a way that shows that you think you are better, more intelligent, experienced, etc. than he/she is</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "7",
                    question_en: "<p>9. Select the INCORRECTLY spelt word.</p>\n",
                    question_hi: "",
                    options_en: ["<p>Endurance</p>\n", "<p>Tolerance</p>\n", 
                                "<p>Insistence</p>\n", "<p>Persistance</p>\n"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: "<p>9.(d) Persistence is the correct spelling.</p>\r\n<p>Endurance - the capacity of something to last or to withstand wear and tear</p>\r\n<p>Persistence&nbsp; - the fact of continuing in an opinion or course of action in spite of difficulty or opposition</p>\n",
                    solution_hi: "<p>9.(d) Persistence is the correct spelling.</p>\r\n<p>Endurance - the capacity of something to last or to withstand wear and tear</p>\r\n<p>Persistence&nbsp; - the fact of continuing in an opinion or course of action in spite of difficulty or opposition</p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "7",
                    question_en: " <p>10. Select the word which means the same as the group of words given. </span></p> <p><span style=\"font-family:Times New Roman\">One who has the power to do anything </span></p>",
                    question_hi: "",
                    options_en: [" <p> omnipresent </span></p>", " <p> multifaceted </span></p>", 
                                " <p> omnipotent </span></p>", " <p> multilingual </span></p>"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: " <p>10.(c) Omnipotent</span></p> <p><span style=\"font-family:Times New Roman\">Omnipotent- one who has the power to do anything </span></p> <p><span style=\"font-family:Times New Roman\">Omnipresent- </span><span style=\"font-family:Times New Roman\">present everywhere at the same time</span><span style=\"font-family:Times New Roman\"> </span></p> <p><span style=\"font-family:Times New Roman\">Multifaceted- </span><span style=\"font-family:Times New Roman\">having many aspects to be considered</span></p> <p><span style=\"font-family:Times New Roman\">Multilingual- </span><span style=\"font-family:Times New Roman\">written or printed in several languages</span></p>",
                    solution_hi: " <p>10.(c) Omnipotent</span></p> <p><span style=\"font-family:Times New Roman\">Omnipotent- one who has the power to do anything </span></p> <p><span style=\"font-family:Times New Roman\">Omnipresent- </span><span style=\"font-family:Times New Roman\">present everywhere at the same time</span><span style=\"font-family:Times New Roman\"> </span></p> <p><span style=\"font-family:Times New Roman\">Multifaceted- </span><span style=\"font-family:Times New Roman\">having many aspects to be considered</span></p> <p><span style=\"font-family:Times New Roman\">Multilingual- </span><span style=\"font-family:Times New Roman\">written or printed in several languages</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "7",
                    question_en: " <p>11. Select the most appropriate word to fill in the blank. </span></p> <p><span style=\"font-family:Times New Roman\">The headmaster ______ on the stage and gave a short speech. </span></p>",
                    question_hi: "",
                    options_en: [" <p> prescribed </span></p>", " <p> appeared </span></p>", 
                                " <p> delighted </span></p>", " <p> remembered </span></p>"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: " <p>11.(b)</span></p> <p><span style=\"font-family:Times New Roman\">Appeared means to suddenly be seen or to come into sight. </span><span style=\"font-family:Times New Roman\">The given sentence states that the headmaster is suddenly seen on the stage and gave a short speech. Hence, ‘</span><span style=\"font-family:Times New Roman\">appeared</span><span style=\"font-family:Times New Roman\">’ is the most appropriate answer.</span></p>",
                    solution_hi: " <p>11.(b)</span></p> <p><span style=\"font-family:Times New Roman\">Appeared means to suddenly be seen or to come into sight. </span><span style=\"font-family:Times New Roman\">The given sentence states that the headmaster is suddenly seen on the stage and gave a short speech. Hence, ‘</span><span style=\"font-family:Times New Roman\">appeared</span><span style=\"font-family:Times New Roman\">’ is the most appropriate answer.</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "7",
                    question_en: " <p>12. Select the most appropriate ANTONYM of the given word. </span></p> <p><span style=\"font-family:Times New Roman\">BRAVERY </span></p>",
                    question_hi: "",
                    options_en: [" <p> heroism </span></p>", " <p> strength </span></p>", 
                                " <p> cowardice </span></p>", " <p> courage </span></p>"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: " <p>12.(c)</span></p> <p><span style=\"font-family:Times New Roman\">Cowardice- </span><span style=\"font-family:Times New Roman\">a lack of courage</span></p> <p><span style=\"font-family:Times New Roman\">Bravery- </span><span style=\"font-family:Times New Roman\">actions that are brave</span><span style=\"font-family:Times New Roman\"> </span></p> <p><span style=\"font-family:Times New Roman\">Heroism- </span><span style=\"font-family:Times New Roman\">great courage</span><span style=\"font-family:Times New Roman\"> </span></p> <p><span style=\"font-family:Times New Roman\">Strength- </span><span style=\"font-family:Times New Roman\">the quality of being physically strong;</span><span style=\"font-family:Times New Roman\"> </span></p> <p><span style=\"font-family:Times New Roman\">Courage- </span><span style=\"font-family:Times New Roman\">the ability to control fear in a situation that may be dangerous or unpleasant</span><span style=\"font-family:Times New Roman\">  </span></p>",
                    solution_hi: " <p>12.(c)</span></p> <p><span style=\"font-family:Times New Roman\">Cowardice- </span><span style=\"font-family:Times New Roman\">a lack of courage</span></p> <p><span style=\"font-family:Times New Roman\">Bravery- </span><span style=\"font-family:Times New Roman\">actions that are brave</span><span style=\"font-family:Times New Roman\"> </span></p> <p><span style=\"font-family:Times New Roman\">Heroism- </span><span style=\"font-family:Times New Roman\">great courage</span><span style=\"font-family:Times New Roman\"> </span></p> <p><span style=\"font-family:Times New Roman\">Strength- </span><span style=\"font-family:Times New Roman\">the quality of being physically strong;</span><span style=\"font-family:Times New Roman\"> </span></p> <p><span style=\"font-family:Times New Roman\">Courage- </span><span style=\"font-family:Times New Roman\">the ability to control fear in a situation that may be dangerous or unpleasant</span><span style=\"font-family:Times New Roman\">  </span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "7",
                    question_en: " <p>13. Select the most appropriate synonym of the given word. </span></p> <p><span style=\"font-family:Times New Roman\">ABSOLUTE </span></p>",
                    question_hi: "",
                    options_en: [" <p> limited </span></p>", " <p> complete </span></p>", 
                                " <p> partial </span></p>", " <p> conditional </span></p>"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: " <p>13.(b)</span></p> <p><span style=\"font-family:Times New Roman\">Complete- </span><span style=\"font-family:Times New Roman\">having or including all parts; with nothing missing</span></p> <p><span style=\"font-family:Times New Roman\">Absolute- </span><span style=\"font-family:Times New Roman\">complete,  total</span></p> <p><span style=\"font-family:Times New Roman\">Limited- </span><span style=\"font-family:Times New Roman\">small or controlled in number, amount, etc.</span></p> <p><span style=\"font-family:Times New Roman\">Partial- </span><span style=\"font-family:Times New Roman\">not complete</span></p> <p><span style=\"font-family:Times New Roman\">Conditional- </span><span style=\"font-family:Times New Roman\">that only happens if something else is done or happens first</span></p>",
                    solution_hi: " <p>13.(b)</span></p> <p><span style=\"font-family:Times New Roman\">Complete- </span><span style=\"font-family:Times New Roman\">having or including all parts; with nothing missing</span></p> <p><span style=\"font-family:Times New Roman\">Absolute- </span><span style=\"font-family:Times New Roman\">complete,  total</span></p> <p><span style=\"font-family:Times New Roman\">Limited- </span><span style=\"font-family:Times New Roman\">small or controlled in number, amount, etc.</span></p> <p><span style=\"font-family:Times New Roman\">Partial- </span><span style=\"font-family:Times New Roman\">not complete</span></p> <p><span style=\"font-family:Times New Roman\">Conditional- </span><span style=\"font-family:Times New Roman\">that only happens if something else is done or happens first</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "7",
                    question_en: " <p>14. Select the most appropriate ANTONYM of the given word. </span></p> <p><span style=\"font-family:Times New Roman\">Clandestine </span></p>",
                    question_hi: "",
                    options_en: [" <p> Heavy </span></p>", " <p> Honest </span></p>", 
                                " <p> Dressed </span></p>", " <p> Opaque </span></p>"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: " <p>14.(b)</span></p> <p><span style=\"font-family:Times New Roman\">Honest- </span><span style=\"font-family:Times New Roman\"> telling the truth</span><span style=\"font-family:Times New Roman\"> </span></p> <p><span style=\"font-family:Times New Roman\">Clandestine- </span><span style=\"font-family:Times New Roman\">secret and often not legal</span><span style=\"font-family:Times New Roman\">  </span></p> <p><span style=\"font-family:Times New Roman\">Heavy- </span><span style=\"font-family:Times New Roman\">weighing a lot</span></p> <p><span style=\"font-family:Times New Roman\">Dressed- </span><span style=\"font-family:Times New Roman\">to put clothes on somebody or yourself</span><span style=\"font-family:Times New Roman\"> </span></p> <p><span style=\"font-family:Times New Roman\">Opaque- </span><span style=\"font-family:Times New Roman\">that you cannot see through</span><span style=\"font-family:Times New Roman\"> </span></p>",
                    solution_hi: " <p>14.(b)</span></p> <p><span style=\"font-family:Times New Roman\">Honest- </span><span style=\"font-family:Times New Roman\"> telling the truth</span><span style=\"font-family:Times New Roman\"> </span></p> <p><span style=\"font-family:Times New Roman\">Clandestine- </span><span style=\"font-family:Times New Roman\">secret and often not legal</span><span style=\"font-family:Times New Roman\">  </span></p> <p><span style=\"font-family:Times New Roman\">Heavy- </span><span style=\"font-family:Times New Roman\">weighing a lot</span></p> <p><span style=\"font-family:Times New Roman\">Dressed- </span><span style=\"font-family:Times New Roman\">to put clothes on somebody or yourself</span><span style=\"font-family:Times New Roman\"> </span></p> <p><span style=\"font-family:Times New Roman\">Opaque- </span><span style=\"font-family:Times New Roman\">that you cannot see through</span><span style=\"font-family:Times New Roman\"> </span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "7",
                    question_en: " <p>15. Select the option that can be used as a one-word substitute for the given group of words. </span></p> <p><span style=\"font-family:Times New Roman\">A person who presents a radio/television programme </span></p>",
                    question_hi: "",
                    options_en: [" <p> Idol </span></p>", " <p> Anchor </span></p>", 
                                " <p> Speaker </span></p>", " <p> Star </span></p>"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: " <p>15.(b) Anchor</span></p> <p><span style=\"font-family:Times New Roman\">Anchor- a person who presents a radio/television program </span></p> <p><span style=\"font-family:Times New Roman\">Idol- </span><span style=\"font-family:Times New Roman\">a person who is admired or loved</span><span style=\"font-family:Times New Roman\"> </span></p> <p><span style=\"font-family:Times New Roman\">Speaker- </span><span style=\"font-family:Times New Roman\">a person who makes a speech to a group of people</span></p> <p><span style=\"font-family:Times New Roman\">Star- </span><span style=\"font-family:Times New Roman\">a large ball of burning gas in outer space that you see as a small point of light in the sky at night</span><span style=\"font-family:Times New Roman\"> </span></p>",
                    solution_hi: " <p>15.(b) Anchor</span></p> <p><span style=\"font-family:Times New Roman\">Anchor- a person who presents a radio/television program </span></p> <p><span style=\"font-family:Times New Roman\">Idol- </span><span style=\"font-family:Times New Roman\">a person who is admired or loved</span><span style=\"font-family:Times New Roman\"> </span></p> <p><span style=\"font-family:Times New Roman\">Speaker- </span><span style=\"font-family:Times New Roman\">a person who makes a speech to a group of people</span></p> <p><span style=\"font-family:Times New Roman\">Star- </span><span style=\"font-family:Times New Roman\">a large ball of burning gas in outer space that you see as a small point of light in the sky at night</span><span style=\"font-family:Times New Roman\"> </span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "7",
                    question_en: "<p>16. Select the most appropriate option to substitute the underlined segment in the given sentence. If there is no need to substitute it, select &lsquo;No substitution&rsquo;.</p>\r\n<p><span style=\"text-decoration: underline;\"><strong><span style=\"font-family: Times New Roman;\">Had you not interfering</span></strong></span><span style=\"font-family: Times New Roman;\"><strong> </strong>in my affairs, I would have been in the US now. </span></p>\n",
                    question_hi: "",
                    options_en: ["<p>No substitution</p>\n", "<p>Had you not interfered</p>\n", 
                                "<p>Was you not interfere</p>\n", "<p>Have you not been interfering</p>\n"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: "<p>16.(b)</p>\r\n<p><span style=\"font-family: Times New Roman;\">&ldquo;Had + V</span><span style=\"font-family: Times New Roman;\">3</span><span style=\"font-family: Times New Roman;\"> ______ would have been + V</span><span style=\"font-family: Times New Roman;\">3</span><span style=\"font-family: Times New Roman;\">&rdquo; is a grammatically correct structure.Its a conditional sentence. However, &lsquo;interfered&rsquo; is the third form(V</span><span style=\"font-family: Times New Roman;\">3</span><span style=\"font-family: Times New Roman;\">) of &lsquo;interfere&rsquo; and not &lsquo;interfering&rsquo;. Hence, &lsquo;had you not interfered&rsquo; is the most appropriate answer. </span></p>\n",
                    solution_hi: "<p>16.(b)</p>\r\n<p><span style=\"font-family: Times New Roman;\">&ldquo;Had + V</span><span style=\"font-family: Times New Roman;\">3</span><span style=\"font-family: Times New Roman;\"> ______ would have been + V</span><span style=\"font-family: Times New Roman;\">3</span><span style=\"font-family: Times New Roman;\">&rdquo; is a grammatically correct structure.Its a conditional sentence. However, &lsquo;interfered&rsquo; is the third form(V</span><span style=\"font-family: Times New Roman;\">3</span><span style=\"font-family: Times New Roman;\">) of &lsquo;interfere&rsquo; and not &lsquo;interfering&rsquo;. Hence, &lsquo;had you not interfered&rsquo; is the most appropriate answer. </span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "7",
                    question_en: "<p>17. Sentences of a paragraph are given below in jumbled order. Arrange the sentences in the <span style=\"font-family: Times New Roman;\">right order to form a meaningful and coherent paragraph. </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">A. His father shifted his own ambitions to those of his son, providing him with models and </span><span style=\"font-family: Times New Roman;\">support for his first exhibition at the age of 13. </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">B. Pablo Picasso was the son of Jos&eacute; Ruiz Blasco, a professor of&nbsp;drawing, and Maria </span><span style=\"font-family: Times New Roman;\">Picasso L&oacute;pez. </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">C. From that point his ability to experiment with what he learned and to develop new </span><span style=\"font-family: Times New Roman;\">expressive means quickly allowed him to surpass his father&rsquo;s abilities. </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">D. His unusual adeptness for drawing began to&nbsp;manifest&nbsp;itself early, around the age of 10, </span><span style=\"font-family: Times New Roman;\">when he became his father&rsquo;s pupil. </span></p>\n",
                    question_hi: "",
                    options_en: ["<p>BDCA</p>\n", "<p>DCAB</p>\n", 
                                "<p>BADC</p>\n", "<p>CDAB</p>\n"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: "<p>17.(a) BDCA</p>\r\n<p><span style=\"font-family: Times New Roman;\">Sentence B will be the starting line as it contains the main idea of the parajumble i.e. Pablo Picasso. However, Sentence D states that his unusual adeptness for drawing began to&nbsp;manifest&nbsp;itself early, around the age of 10. So, D will follow B. Further, Sentence C states that his ability to experiment with what he learned and to develop new expressive means quickly allowed him to surpass his father&rsquo;s abilities &amp; Sentence A states that his father shifted his own ambitions to those of his son. So, A will follow C. Going through the options, option a has the correct sequence. </span></p>\n",
                    solution_hi: "<p>17.(a) BDCA</p>\r\n<p><span style=\"font-family: Times New Roman;\">Sentence B will be the starting line as it contains the main idea of the parajumble i.e. Pablo Picasso. However, Sentence D states that his unusual adeptness for drawing began to&nbsp;manifest&nbsp;itself early, around the age of 10. So, D will follow B. Further, Sentence C states that his ability to experiment with what he learned and to develop new expressive means quickly allowed him to surpass his father&rsquo;s abilities &amp; Sentence A states that his father shifted his own ambitions to those of his son. So, A will follow C. Going through the options, option a has the correct sequence. </span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "7",
                    question_en: "<p>18. The following sentence has been divided into parts. One of them may contain an error.<span style=\"font-family: Times New Roman;\">Select the part that contains the error from the given options. If you don&rsquo;t find any error, </span><span style=\"font-family: Times New Roman;\">mark &lsquo;No error&rsquo; as your answer. </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">The need today is for both sides / to introspect and re-examine the issue carefully / and </span><span style=\"font-family: Times New Roman;\">resolve it amicably. </span></p>\n",
                    question_hi: "",
                    options_en: ["<p>to introspect and re-examine the issue carefully</p>\n", "<p>and resolve it amicably</p>\n", 
                                "<p>No error</p>\n", "<p>The need today is for both sides</p>\n"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: "<p>18.(c)</p>\r\n<p><span style=\"font-family: Times New Roman;\">No error. The sentence is grammatically correct.</span></p>\n",
                    solution_hi: "<p>18.(c)</p>\r\n<p><span style=\"font-family: Times New Roman;\">No error. The sentence is grammatically correct.</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "7",
                    question_en: " <p>19. Select the INCORRECTLY spelt word. </span></p>",
                    question_hi: "",
                    options_en: [" <p> assimilate </span></p>", " <p> readable </span></p>", 
                                " <p> acquire </span></p>", " <p> acknowlege </span></p>"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: " <p>19.(d)</span></p> <p><span style=\"font-family:Times New Roman\">Acknowledge is the correct spelling </span></p>",
                    solution_hi: " <p>19.(d)</span></p> <p><span style=\"font-family:Times New Roman\">Acknowledge is the correct spelling </span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "7",
                    question_en: "<p>20. Select the correct indirect form of the given sentence.</p>\r\n<p><span style=\"font-family: Times New Roman;\">My niece said, &ldquo;I have decided to take coaching in badminton.&rdquo; </span></p>",
                    question_hi: "",
                    options_en: ["<p>My niece told me that she had decided to take coaching in badminton.</p>", "<p>My niece told that I have decided to take coaching in badminton.</p>", 
                                "<p>My niece said she is deciding to take coaching in badminton.</p>", "<p>My niece said she will decide to take coaching in badminton.</p>"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: "<p>20.(a)</p>\r\n<p><span style=\"font-weight: 400;\">&nbsp;(a) My niece told me that she had decided to take coaching in badminton. (Correct)</span></p>\r\n<p><span style=\"font-weight: 400;\">&nbsp;(b) My niece told that I </span><span style=\"text-decoration: underline;\"><span style=\"font-weight: 400;\">have</span></span><span style=\"font-weight: 400;\"> decided to take coaching in badminton. (Incorrect Tense)</span></p>\r\n<p><span style=\"font-weight: 400;\">&nbsp;(c) My niece said she<span style=\"text-decoration: underline;\"> </span></span><span style=\"text-decoration: underline;\"><span style=\"font-weight: 400;\">is</span></span><span style=\"font-weight: 400;\"><span style=\"text-decoration: underline;\"> </span>deciding to take coaching in badminton. (Incorrect Tense)</span></p>\r\n<p><span style=\"font-weight: 400;\">&nbsp;(d) My niece said she </span><span style=\"text-decoration: underline;\"><span style=\"font-weight: 400;\">will</span></span><span style=\"font-weight: 400;\"><span style=\"text-decoration: underline;\"> </span>decide to take coaching in badminton. (Incorrect Tense)</span></p>",
                    solution_hi: "<p>20.(a)</p>\r\n<p><span style=\"font-weight: 400;\">&nbsp;(a) My niece told me that she had decided to take coaching in badminton. (Correct)</span></p>\r\n<p><span style=\"font-weight: 400;\">&nbsp;(b) My niece told that I </span><span style=\"text-decoration: underline;\"><span style=\"font-weight: 400;\">have</span></span><span style=\"font-weight: 400;\"> decided to take coaching in badminton. (Incorrect Tense)</span></p>\r\n<p><span style=\"font-weight: 400;\">&nbsp;(c) My niece said she<span style=\"text-decoration: underline;\"> </span></span><span style=\"text-decoration: underline;\"><span style=\"font-weight: 400;\">is</span></span><span style=\"font-weight: 400;\"><span style=\"text-decoration: underline;\"> </span>deciding to take coaching in badminton. (Incorrect Tense)</span></p>\r\n<p><span style=\"font-weight: 400;\">&nbsp;(d) My niece said she </span><span style=\"text-decoration: underline;\"><span style=\"font-weight: 400;\">will</span></span><span style=\"font-weight: 400;\"><span style=\"text-decoration: underline;\"> </span>decide to take coaching in badminton. (Incorrect Tense)</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "7",
                    question_en: "<p>21. Cloze test :</p>\r\n<p><span style=\"font-family: Times New Roman;\">In the following passage, some words have been deleted. Select the most appropriate option </span><span style=\"font-family: Times New Roman;\">to fill in each blank. </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Kanjirapally has the highest number of rainy days in Kerala. It receives the highest (1)_____ of </span><span style=\"font-family: Times New Roman;\">summer rains, winter rains and northeast monsoons in Kerala, (2)_____ it one of the very few (3)</span>_____ in India enjoying equatorial rainforest type climate, with no (4) _____ dry season. The <span style=\"font-family: Times New Roman;\">well-distributed rainfall pattern of Kanjirapally is the (5)_____ reason for the phenomenon of </span><span style=\"font-family: Times New Roman;\">high yield of latex from rubber plantations in and around the town. </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Select the most appropriate option to fill in blank no.1 .</span></p>\n",
                    question_hi: "",
                    options_en: ["<p>amount</p>\n", "<p>number</p>\n", 
                                "<p>expanse</p>\n", "<p>extent</p>\n"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: "<p>21.(a) Amount</p>\r\n<p><span style=\"font-family: Times New Roman;\">The amount of something is how much of it there is, a quantity of something. </span><span style=\"font-family: Times New Roman;\">The given passage states that Kanjirapally receives the highest amount of summer rains. Hence, &lsquo;amount&rsquo; is the most appropriate answer. </span></p>\n",
                    solution_hi: "<p>21.(a) Amount</p>\r\n<p><span style=\"font-family: Times New Roman;\">The amount of something is how much of it there is, a quantity of something. </span><span style=\"font-family: Times New Roman;\">The given passage states that Kanjirapally receives the highest amount of summer rains. Hence, &lsquo;amount&rsquo; is the most appropriate answer. </span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "7",
                    question_en: "<p>22. Cloze test :</p>\r\n<p><span style=\"font-family: Times New Roman;\">In the following passage, some words have been deleted. Select the most appropriate option </span><span style=\"font-family: Times New Roman;\">to fill in each blank. </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Kanjirapally has the highest number of rainy days in Kerala. It receives the highest (1)_____ of </span><span style=\"font-family: Times New Roman;\">summer rains, winter rains and northeast monsoons in Kerala, (2)_____ it one of the very few (3)&nbsp;</span>_____ in India enjoying equatorial rainforest type climate, with no (4) _____ dry season. The <span style=\"font-family: Times New Roman;\">well-distributed rainfall pattern of Kanjirapally is the (5)_____ reason for the phenomenon of </span><span style=\"font-family: Times New Roman;\">high yield of latex from rubber plantations in and around the town. </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Select the most appropriate option to fill in blank no.2 .</span></p>\n",
                    question_hi: "",
                    options_en: ["<p>make</p>\n", "<p>making</p>\n", 
                                "<p>made</p>\n", "<p>makes</p>\n"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: "<p>22.(b) Making</p>\r\n<p><span style=\"font-family: Times New Roman;\">Making means </span><span style=\"font-family: Times New Roman;\">to make somebody/something become something. </span><span style=\"font-family: Times New Roman;\">The given passage states that Kanjirapally receives the highest amount of summer rain in Kerala making it one of the very few places in India that enjoys an equatorial rainforest-type climate. Hence, &lsquo;making&rsquo; is the most appropriate answer. </span></p>\n",
                    solution_hi: "<p>22.(b) Making</p>\r\n<p><span style=\"font-family: Times New Roman;\">Making means </span><span style=\"font-family: Times New Roman;\">to make somebody/something become something. </span><span style=\"font-family: Times New Roman;\">The given passage states that Kanjirapally receives the highest amount of summer rain in Kerala making it one of the very few places in India that enjoys an equatorial rainforest-type climate. Hence, &lsquo;making&rsquo; is the most appropriate answer. </span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "23",
                    section: "7",
                    question_en: "<p>23. Cloze test :</p>\r\n<p><span style=\"font-family: Times New Roman;\">In the following passage, some words have been deleted. Select the most appropriate option </span><span style=\"font-family: Times New Roman;\">to fill in each blank. </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Kanjirapally has the highest number of rainy days in Kerala. It receives the highest (1)_____ of </span><span style=\"font-family: Times New Roman;\">summer rains, winter rains and northeast monsoons in Kerala, (2)_____ it one of the very few (3)</span>_____ in India enjoying equatorial rainforest type climate, with no (4) _____ dry season. The <span style=\"font-family: Times New Roman;\">well-distributed rainfall pattern of Kanjirapally is the (5)_____ reason for the phenomenon of </span><span style=\"font-family: Times New Roman;\">high yield of latex from rubber plantations in and around the town. </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Select the most appropriate option to fill in blank no.3 .</span></p>\n",
                    question_hi: "",
                    options_en: ["<p>space</p>\n", "<p>places</p>\n", 
                                "<p>spot</p>\n", "<p>place</p>\n"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: "<p>23.(b) Places</p>\r\n<p><span style=\"font-family: Times New Roman;\">Place means </span><span style=\"font-family: Times New Roman;\">a particular position or area. </span><span style=\"font-family: Times New Roman;\">The given passage states that Kanjirapally makes Kerala one of the very few places in India that enjoys an equatorial rainforest-type climate. Hence, &lsquo;places&rsquo; is the most appropriate answer. </span></p>\n",
                    solution_hi: "<p>23.(b) Places</p>\r\n<p><span style=\"font-family: Times New Roman;\">Place means </span><span style=\"font-family: Times New Roman;\">a particular position or area. </span><span style=\"font-family: Times New Roman;\">The given passage states that Kanjirapally makes Kerala one of the very few places in India that enjoys an equatorial rainforest-type climate. Hence, &lsquo;places&rsquo; is the most appropriate answer. </span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "24",
                    section: "7",
                    question_en: "<p>24. Cloze test :</p>\r\n<p><span style=\"font-family: Times New Roman;\">In the following passage, some words have been deleted. Select the most appropriate option </span><span style=\"font-family: Times New Roman;\">to fill in each blank. </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Kanjirapally has the highest number of rainy days in Kerala. It receives the highest (1)_____ of </span><span style=\"font-family: Times New Roman;\">summer rains, winter rains and northeast monsoons in Kerala, (2)_____ it one of the very few (3)</span>____ in India enjoying equatorial rainforest type climate, with no (4) _____ dry season. The <span style=\"font-family: Times New Roman;\">well-distributed rainfall pattern of Kanjirapally is the (5)_____ reason for the phenomenon of </span><span style=\"font-family: Times New Roman;\">high yield of latex from rubber plantations in and around the town. </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Select the most appropriate option to fill in blank no.4 .</span></p>\n",
                    question_hi: "",
                    options_en: ["<p>individual</p>\n", "<p>divergent</p>\n", 
                                "<p>diverse</p>\n", "<p>distinct</p>\n"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: "<p>24.(d) Distinct</p>\r\n<p><span style=\"font-family: Times New Roman;\">Distinct means </span><span style=\"font-family: Times New Roman;\">clearly different from something else</span><span style=\"font-family: Times New Roman;\">. The given passage states that Kanjirapally has no different dry season. Hence, &lsquo;distinct&rsquo; is the most appropriate answer. </span></p>\n",
                    solution_hi: "<p>24.(d) Distinct</p>\r\n<p><span style=\"font-family: Times New Roman;\">Distinct means </span><span style=\"font-family: Times New Roman;\">clearly different from something else</span><span style=\"font-family: Times New Roman;\">. The given passage states that Kanjirapally has no different dry season. Hence, &lsquo;distinct&rsquo; is the most appropriate answer. </span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "25",
                    section: "misc",
                    question_en: "<p>25. <span style=\"font-family: Times New Roman;\">Cloze test :</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">In the following passage, some words have been deleted. Select the most appropriate option </span><span style=\"font-family: Times New Roman;\">to fill in each blank. </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Kanjirapally has the highest number of rainy days in Kerala. It receives the highest (1)_____ of </span><span style=\"font-family: Times New Roman;\">summer rains, winter rains and northeast monsoons in Kerala, (2)_____ it one of the very few (3)</span>_____ in India enjoying equatorial rainforest type climate, with no (4) _____ dry season. The <span style=\"font-family: Times New Roman;\">well-distributed rainfall pattern of Kanjirapally is the (5)_____ reason for the phenomenon of </span><span style=\"font-family: Times New Roman;\">high yield of latex from rubber plantations in and around the town. </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Select the most appropriate option to fill in blank no.5 .</span></p>\n",
                    question_hi: "",
                    options_en: ["<p>insignificant</p>\n", "<p>primary</p>\n", 
                                "<p>minor</p>\n", "<p>initial</p>\n"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: "<p>25.(b) Primary</p>\r\n<p><span style=\"font-family: Times New Roman;\">Primary means </span><span style=\"font-family: Times New Roman;\">most important or main.</span><span style=\"font-family: Times New Roman;\"> The given passage states that the well-distributed rainfall pattern of Kanjirapally is the main reason for the phenomenon of a high yield of latex. Hence, &lsquo;primary&rsquo; is the most appropriate answer. </span></p>\n",
                    solution_hi: "<p>25.(b) Primary</p>\r\n<p><span style=\"font-family: Times New Roman;\">Primary means </span><span style=\"font-family: Times New Roman;\">most important or main.</span><span style=\"font-family: Times New Roman;\"> The given passage states that the well-distributed rainfall pattern of Kanjirapally is the main reason for the phenomenon of a high yield of latex. Hence, &lsquo;primary&rsquo; is the most appropriate answer. </span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML - more compact
            let html = `
                <div class="card mb-3">
                    <div class="card-body">
                        <div class="question-text mb-3">
                            <div class="en">${question.question_en}</div>
                            <div class="hi" style="display:none">${question.question_hi}</div>
                        </div>
                        <div class="options">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = 'option-label';
                // When reviewing, highlight user's choice and correct answer
                if (submitted) {
                    if (opt === question.correct) {
                        optionClass += ' border-success bg-success bg-opacity-10';
                    }
                    if (answers[question.id] === opt && opt !== question.correct) {
                        optionClass += ' border-danger bg-danger bg-opacity-10';
                    }
                }
                html += `
                    <div class="form-check mb-2">
                        <input class="form-check-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${answers[question.id] === opt ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                        <label class="form-check-label w-100 ${optionClass}" for="opt-${index}-${opt}">
                            ${submitted && opt === question.correct ? '<i class="fas fa-check-circle text-success me-1"></i>' : ''}
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const alertClass = isCorrect ? 'alert-success' : 'alert-danger';
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="alert ${alertClass} mt-3 py-2">
                        <strong>${resultText}</strong>
                        <div class="mt-1 small">
                            <strong>Solution:</strong>
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                document.getElementById(`box-${index}`).classList.add('attempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { if (currentQuestion > 0) showQuestion(currentQuestion - 1); }
        function nextQuestion() { if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
            // Update button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const content = document.getElementById('main-content');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                if (window.innerWidth >= 992) {
                    content.classList.add('with-nav');
                }
            } else {
                nav.classList.remove('show');
                if (window.innerWidth >= 992) {
                    content.classList.remove('with-nav');
                }
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer - compact
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            if (confirm(`You have attempted ${attempted} of ${questions.length} questions.
${remaining} questions are unattempted.
Are you sure you want to submit?`)) {
                submitTest();
            }
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('bg-secondary', 'text-white');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            resultsModal.show();
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.classList.replace('btn-light', 'btn-success');
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            resultsModal.hide();
            showQuestion(0);
        }
        // Add event listener for window resize
        window.addEventListener('resize', function() {
            if (window.innerWidth >= 992) {
                if (document.getElementById('question-nav').classList.contains('show')) {
                    document.getElementById('main-content').classList.add('with-nav');
                }
            } else {
                document.getElementById('main-content').classList.remove('with-nav');
            }
        });
    </script>
</body>
</html>