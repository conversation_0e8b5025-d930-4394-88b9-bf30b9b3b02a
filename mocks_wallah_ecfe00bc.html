<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">12:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 25</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">25</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 12 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["7"] = {
                name: "English",
                start: 0,
                end: 23
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="7">English</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 24,
                end: 24
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "7",
                    question_en: "<p>1. Select the most appropriate ANTONYM of the underlined word.<br>Their sporting achievements at the highest level are being <span style=\"text-decoration: underline;\">utterly</span> ignored.</p>",
                    question_hi: "<p>1. Select the most appropriate ANTONYM of the underlined word.<br>Their sporting achievements at the highest level are being <span style=\"text-decoration: underline;\">utterly</span> ignored.</p>",
                    options_en: ["<p>Extremely</p>", "<p>Correctly</p>", 
                                "<p>Minimally</p>", "<p>Totally</p>"],
                    options_hi: ["<p>Extremely</p>", "<p>Correctly</p>",
                                "<p>Minimally</p>", "<p>Totally</p>"],
                    solution_en: "<p>1.(c) <strong>Minimally</strong>- to a very small degree.<br><strong>Utterly</strong>- completely or absolutely.<br><strong>Extremely</strong>- to a very high degree.<br><strong>Correctly</strong>- in an accurate or proper manner.<br><strong>Totally</strong>- completely or entirely.</p>",
                    solution_hi: "<p>1.(c) <strong>Minimally </strong>(न्यूनतम)- to a very small degree.<br><strong>Utterly </strong>(पूर्ण रूप से)- completely or absolutely.<br><strong>Extremely </strong>(अत्यधिक)- to a very high degree.<br><strong>Correctly </strong>(ठीक तरीके से)- in an accurate or proper manner.<br><strong>Totally </strong>(पूर्णतया)- completely or entirely.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "7",
                    question_en: "<p>2. Select the most appropriate synonym of the given word.<br>Stationary</p>",
                    question_hi: "<p>2. Select the most appropriate synonym of the given word.<br>Stationary</p>",
                    options_en: ["<p>Motile</p>", "<p>Portable</p>", 
                                "<p>Swindle</p>", "<p>Still</p>"],
                    options_hi: ["<p>Motile</p>", "<p>Portable</p>",
                                "<p>Swindle</p>", "<p>Still</p>"],
                    solution_en: "<p>2.(d) <strong>Still</strong>- not moving or making a sound.<br><strong>Stationary</strong>- not moving, fixed in one place.<br><strong>Motile</strong>- capable of movement.<br><strong>Portable</strong>- light and small enough to be easily carried or moved.<br><strong>Swindle</strong>- to cheat or defraud someone out of money or property.</p>",
                    solution_hi: "<p>2.(d) <strong>Still </strong>(स्थिर)- not moving or making a sound.<br><strong>Stationary </strong>(स्थिर)- not moving, fixed in one place.<br><strong>Motile </strong>(गतिशील)- capable of movement.<br><strong>Portable </strong>(वहनीय)- light and small enough to be easily carried or moved.<br><strong>Swindle </strong>(ठगी)- to cheat or defraud someone out of money or property.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "7",
                    question_en: "<p>3. Select the most appropriate option that can substitute the underlined words in the given sentence.<br>Rare species <span style=\"text-decoration: underline;\">of frogs were very quicker</span> identified by Stella.</p>",
                    question_hi: "<p>3. Select the most appropriate option that can substitute the underlined words in the given sentence.<br>Rare species <span style=\"text-decoration: underline;\">of frogs were very quicker</span> identified by Stella.</p>",
                    options_en: ["<p>to frogs were very quickly</p>", "<p>of frogs were very quickly</p>", 
                                "<p>in frogs were very quicker</p>", "<p>of frogs were very quicklier</p>"],
                    options_hi: ["<p>to frogs were very quickly</p>", "<p>of frogs were very quickly</p>",
                                "<p>in frogs were very quicker</p>", "<p>of frogs were very quicklier</p>"],
                    solution_en: "<p>3.(b) of frogs were very quickly<br>The given sentence needs an adverb &lsquo;quickly&rsquo; to modify the verb &lsquo;identified&rsquo;, not an adjective &lsquo;quicker&rsquo;. Hence, &lsquo;of frogs were quickly&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>3.(b) of frogs were very quickly<br>दिए गए sentence में verb &lsquo;identified&rsquo; को modify करने के लिए adjective &lsquo;quicker&rsquo; की नहीं बल्कि adverb &lsquo;quickly&rsquo; की आवश्यकता है। अतः, &lsquo;of frogs were quickly&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "7",
                    question_en: "<p>4. Select the most appropriate ANTONYM of the underlined word in the following sentence to fill in the blank.<br>Most of the flowers <span style=\"text-decoration: underline;\">bloom</span> in the spring season but they start to __________ during summer.</p>",
                    question_hi: "<p>4. Select the most appropriate ANTONYM of the underlined word in the following sentence to fill in the blank.<br>Most of the flowers bloom in the spring season but they start to __________ during summer.</p>",
                    options_en: ["<p>flush</p>", "<p>wither</p>", 
                                "<p>flourish</p>", "<p>blossom</p>"],
                    options_hi: ["<p>flush</p>", "<p>wither</p>",
                                "<p>flourish</p>", "<p>blossom</p>"],
                    solution_en: "<p>4.(b) <strong>Wither</strong>- to become dry and shriveled.<br><strong>Bloom</strong>- to produce flowers.<br><strong>Flush</strong>- become red and hot, typically as the result of illness or strong emotion.<br><strong>Flourish</strong>- to grow or develop in a healthy or vigorous way.<br><strong>Blossom</strong>- to grow and do well.</p>",
                    solution_hi: "<p>4.(b) <strong>Wither </strong>(मुरझाना/सूख जाना)- to become dry and shriveled.<br><strong>Bloom </strong>(पुष्पित होना)- to produce flowers.<br><strong>Flush </strong>(लालिमा)- become red and hot, typically as the result of illness or strong emotion.<br><strong>Flourish </strong>(विकसित होना)- to grow or develop in a healthy or vigorous way.<br><strong>Blossom </strong>(विकसित होना)- to grow and do well.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "7",
                    question_en: "5. Parts of a sentence are given below in jumbled order. Arrange the parts in the correct order to form a meaningful sentence.<br />O. delicious<br />R. she<br />Q. a<br />P. baked<br />S. chocolate cake",
                    question_hi: "5. Parts of a sentence are given below in jumbled order. Arrange the parts in the correct order to form a meaningful sentence.<br />O. delicious<br />R. she<br />Q. a<br />P. baked<br />S. chocolate cake",
                    options_en: [" RPQOS", " QOSRP  ", 
                                " OPQRS  ", " SRPQO"],
                    options_hi: [" RPQOS", " QOSRP  ",
                                " OPQRS  ", " SRPQO"],
                    solution_en: "5.(a) RPQOS<br />The given sentence starts with Part R as it contains the main subject of the parajumble, ‘She’. Part P contains the main verb of the sentence, ‘baked’ and Part Q has the article ‘a’ for the object of the verb. So, Q will follow P. Further, Part O has the adjective ‘delicious’ to describe the object and Part S contains the object of the verb, ‘chocolate cake’. So, S will follow O. Going through the options, option ‘a’ has the correct sequence.",
                    solution_hi: "5.(a) RPQOS<br />दिया गया sentence, Part R से शुरू होता है क्योंकि इसमें parajumble का मुख्य विषय, ‘She’ शामिल है। Part P में sentence की main verb ‘baked’ शामिल है और Part Q में verb के object के लिए article ‘a’ है। इसलिए, P के बाद Q आएगा। इसके अलावा, Part O में object का वर्णन करने के लिए adjective ‘delicious’ है और Part S में verb का object, ‘chocolate cake’ शामिल है। इसलिए, O के बाद S आएगा। अतः options के माध्यम से जाने पर, option \'a\' में सही sequence है।    ",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "7",
                    question_en: "<p>6. Select the most appropriate meaning of the phrase in bold.<br>Your so-called friend has <strong>cooked your goose.</strong></p>",
                    question_hi: "<p>6. Select the most appropriate meaning of the phrase in bold.<br>Your so-called friend has <strong>cooked your goose.</strong></p>",
                    options_en: ["<p>To complete a lot of work or a wide range of things</p>", "<p>When something develops completely and reaches maturity; when a child becomes an adult</p>", 
                                "<p>To spend a lot of time and energy but achieve nothing</p>", "<p>To interfere with, disrupt or ruin something for someone</p>"],
                    options_hi: ["<p>To complete a lot of work or a wide range of things</p>", "<p>When something develops completely and reaches maturity; when a child becomes an adult</p>",
                                "<p>To spend a lot of time and energy but achieve nothing</p>", "<p>To interfere with, disrupt or ruin something for someone</p>"],
                    solution_en: "<p>6.(d) <strong>Cooked your goose-</strong> to interfere with, disrupt or ruin something for someone.</p>",
                    solution_hi: "<p>6.(d) <strong>Cooked your goose-</strong> to interfere with, disrupt or ruin something for someone./किसी चीज़ में हस्तक्षेप करना।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "7",
                    question_en: "<p>7. Select the option that can be used as a one word substitute for the given group of words.<br>An often-imaginary place or state of utter perfection and happiness.</p>",
                    question_hi: "<p>7. Select the option that can be used as a one word substitute for the given group of words.<br>An often-imaginary place or state of utter perfection and happiness.</p>",
                    options_en: ["<p>Inferno</p>", "<p>Dystopia</p>", 
                                "<p>Euphoria</p>", "<p>Utopia</p>"],
                    options_hi: ["<p>Inferno</p>", "<p>Dystopia</p>",
                                "<p>Euphoria</p>", "<p>Utopia</p>"],
                    solution_en: "<p>7.(d) <strong>Utopia</strong>- an often-imaginary place or state of utter perfection and happiness.<br><strong>Inferno</strong>- a large fire that is dangerously out of control.<br><strong>Dystopia</strong>- an imagined world or society in which people lead wretched, dehumanized, fearful lives.<br><strong>Euphoria</strong>- a feeling or state of intense excitement and happiness.</p>",
                    solution_hi: "<p>7.(d) <strong>Utopia </strong>(आदर्श राज्य)- an often-imaginary place or state of utter perfection and happiness.<br><strong>Inferno </strong>(भयानक आग)- a large fire that is dangerously out of control.<br><strong>Dystopia </strong>(दुःस्थानता)- an imagined world or society in which people lead wretched, dehumanized, fearful lives.<br><strong>Euphoria </strong>(उत्साह की भावना)- a feeling or state of intense excitement and happiness.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "7",
                    question_en: "<p>8. Select the most appropriate ANTONYM of the given word.<br>Risible</p>",
                    question_hi: "<p>8. Select the most appropriate ANTONYM of the given word.<br>Risible</p>",
                    options_en: ["<p>Serious</p>", "<p>Curious</p>", 
                                "<p>Droll</p>", "<p>Giddy</p>"],
                    options_hi: ["<p>Serious</p>", "<p>Curious</p>",
                                "<p>Droll</p>", "<p>Giddy</p>"],
                    solution_en: "<p>8.(a) <strong>Serious</strong>- solemn or thoughtful in character or manner.<br><strong>Risible</strong>- provoking laughter through being ludicrous.<br><strong>Curious</strong>- eager to know or learn something.<br><strong>Droll</strong>- curious or unusual in a way that provokes dry amusement.<br><strong>Giddy</strong>- feeling silly, happy, and excited.</p>",
                    solution_hi: "<p>8.(a) <strong>Serious </strong>(गंभीर)- solemn or thoughtful in character or manner.<br><strong>Risible </strong>(हास्यास्पद)- provoking laughter through being ludicrous.<br><strong>Curious </strong>(जिज्ञासु)- eager to know or learn something.<br><strong>Droll </strong>(हास्यकर)- curious or unusual in a way that provokes dry amusement.<br><strong>Giddy </strong>(उत्साहित)- feeling silly, happy, and excited.</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "7",
                    question_en: "<p>9. Select the most appropriate option that can substitute the underlined words in the given sentence.<br>He was an irresponsible person <span style=\"text-decoration: underline;\">to the matter of taking up</span> responsibilities.</p>",
                    question_hi: "<p>9. Select the most appropriate option that can substitute the underlined words in the given sentence.<br>He was an irresponsible person to the matter of taking up responsibilities.</p>",
                    options_en: ["<p>at the matter of taking up</p>", "<p>in the matter of taking up</p>", 
                                "<p>by the matter of taking up</p>", "<p>along the matter of taking up</p>"],
                    options_hi: ["<p>at the matter of taking up</p>", "<p>in the matter of taking up</p>",
                                "<p>by the matter of taking up</p>", "<p>along the matter of taking up</p>"],
                    solution_en: "<p>9.(b) in the matter of taking up<br>&lsquo;In&rsquo; is used to indicate a specific situation or circumstance. The given passage states that he was an irresponsible person in the matter of taking up responsibilities. Hence, \'in the matter of taking up\' is the most appropriate answer.</p>",
                    solution_hi: "<p>9.(b) in the matter of taking up<br>किसी sentence में &lsquo;in&rsquo; का प्रयोग किसी विशिष्ट स्थिति या परिस्थिति को इंगित करने के लिए किया जाता है। दिए गए passage में कहा गया है कि वह ज़िम्मेदारियाँ लेने के मामले में एक irresponsible person था। अतः, \'in the matter of taking up\' सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "7",
                    question_en: "10. Select the correct spelling to fill in the blank.<br />Subir\'s long absence is likely to have ____________ for employees in the office.",
                    question_hi: "10. Select the correct spelling to fill in the blank.<br />Subir\'s long absence is likely to have ____________ for employees in the office.",
                    options_en: [" repercusions", " repercussions ", 
                                " reperrcussions", " rapercussions"],
                    options_hi: [" repercusions", " repercussions ",
                                " reperrcussions", " rapercussions"],
                    solution_en: "10.(b) repercussions<br />\'Repercussions\' is the correct spelling. ",
                    solution_hi: "10.(b) repercussions<br />\'Repercussions\' सही spelling है। ",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "7",
                    question_en: "11. Select the correctly spelt word.",
                    question_hi: "11. Select the correctly spelt word.",
                    options_en: [" Privelege ", " Priviledge", 
                                " Privilege", " Privilej"],
                    options_hi: [" Privelege ", " Priviledge",
                                " Privilege", " Privilej"],
                    solution_en: "11.(c) Privilege<br />\'Privilege\' is the correct spelling.",
                    solution_hi: "11.(c) Privilege<br />\'Privilege\' सही spelling है। ",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "7",
                    question_en: "12. Select the option that correctly expresses the given sentence in passive voice.<br />The shareholders will elect him a director.",
                    question_hi: "12. Select the option that correctly expresses the given sentence in passive voice.<br />The shareholders will elect him a director.",
                    options_en: [" He will be elected as director by the shareholders.", " The shareholders have elected him a director.", 
                                " The shareholders elected him a director. ", " He will be elected a director."],
                    options_hi: [" He will be elected as director by the shareholders.", " The shareholders have elected him a director.",
                                " The shareholders elected him a director. ", " He will be elected a director."],
                    solution_en: "12.(a) He will be elected as director by the shareholders. (Correct)<br />(b) The shareholders have elected him a director. (Incorrect Sentence Structure)<br />(c) The shareholders elected him a director. (Incorrect Sentence Structure)<br />(d) He will be elected a director. (Sentence is incomplete)",
                    solution_hi: "12.(a) He will be elected as director by the shareholders. (Correct)<br />(b) The shareholders have elected him a director. (गलत Sentence Structure)<br />(c) The shareholders elected him a director. (गलत Sentence Structure)<br />(d) He will be elected a director. (Sentence, incomplete है)",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "7",
                    question_en: "<p>13. What does the idiom \'Bite the bullet\' mean?</p>",
                    question_hi: "<p>13. What does the idiom \'Bite the bullet\' mean?</p>",
                    options_en: ["<p>Enjoy a delicious meal</p>", "<p>Face a difficult situation with courage</p>", 
                                "<p>Avoid confrontation</p>", "<p>Give someone a piece of advice</p>"],
                    options_hi: ["<p>Enjoy a delicious meal</p>", "<p>Face a difficult situation with courage</p>",
                                "<p>Avoid confrontation</p>", "<p>Give someone a piece of advice</p>"],
                    solution_en: "<p>13.(b)<strong> Bite the bullet-</strong> face a difficult situation with courage.<br>E.g.- I didn&rsquo;t want to go to the dentist, but I had to bite the bullet and get my tooth fixed.</p>",
                    solution_hi: "<p>13.(b)<strong> Bite the bullet-</strong> face a difficult situation with courage./किसी कठिन परिस्थिति का सामना करना। <br>E.g.- I didn&rsquo;t want to go to the dentist, but I had to bite the bullet and get my tooth fixed.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "7",
                    question_en: "14. In the given question, a statement divided into different segments is given. Rearrange all the segments to form a coherent statement.<br />O: its first constitution<br />P: and soon got himself elected<br />Q: the administration of the country<br />R: general Ayub Khan took over<br />S: after Pakistan framed",
                    question_hi: "14. In the given question, a statement divided into different segments is given. Rearrange all the segments to form a coherent statement.<br />O: its first constitution<br />P: and soon got himself elected<br />Q: the administration of the country<br />R: general Ayub Khan took over<br />S: after Pakistan framed",
                    options_en: [" SORQP ", " SPORQ", 
                                " POSRQ", " ROPQS"],
                    options_hi: [" SORQP ", " SPORQ",
                                " POSRQ", " ROPQS"],
                    solution_en: "14.(a) SORQP<br />The given sentence starts with Part S as it introduces the main idea of the sentence, i.e. the time period after Pakistan framed its first constitution. Part O contains the object of the verb ‘framed’ & Part R contains the main subject of the sentence. So, R will follow O. Further, Part Q states that general Ayub Khan took over the administration of the country & Part P states that he soon got himself elected. So, P will follow Q. Going through the options, option ‘a’ has the correct sequence.",
                    solution_hi: "14.(a) SORQP<br />दिया गया sentence, Part S से शुरू होता है क्योंकि यह sentence के मुख्य विचार  ‘the time period after Pakistan framed its first constitution’ को प्रस्तुत करता है। Part O में verb \'framed\' का object शामिल है और Part R में sentence का main subject शामिल है। इसलिए, O के बाद R आएगा। इसके अलावा, Part Q में कहा गया है कि जनरल अयूब खान ने देश का प्रशासन संभाला और Part P में कहा गया है कि उन्होंने जल्द ही स्वयं को निर्वाचित (elected) कर लिया। इसलिए, Q के बाद P,  आएगा। अतः options के माध्यम से जाने पर, option \'a\' में सही sequence है।",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "7",
                    question_en: "<p>15. Select the most appropriate synonym of the given word.<br>Pugnacious</p>",
                    question_hi: "<p>15. Select the most appropriate synonym of the given word.<br>Pugnacious</p>",
                    options_en: ["<p>Ally</p>", "<p>Belligerent</p>", 
                                "<p>Sly</p>", "<p>Dogmatic</p>"],
                    options_hi: ["<p>Ally</p>", "<p>Belligerent</p>",
                                "<p>Sly</p>", "<p>Dogmatic</p>"],
                    solution_en: "<p>15.(b) <strong>Belligerent</strong>- hostile and aggressive.<br><strong>Pugnacious</strong>- eager or quick to argue or fight.<br><strong>Ally</strong>- a person or group that cooperates with another for a common purpose.<br><strong>Sly</strong>- having or showing a cunning and deceitful nature.<br><strong>Dogmatic</strong>- inclined to lay down principles as undeniably true without consideration of evidence or the opinions of others.</p>",
                    solution_hi: "<p>15.(b) <strong>Belligerent </strong>(आक्रामक)- hostile and aggressive.<br><strong>Pugnacious </strong>(झगड़ालू/लड़ाकू)- eager or quick to argue or fight.<br><strong>Ally </strong>(सहयोगी)- a person or group that cooperates with another for a common purpose.<br><strong>Sly </strong>(धूर्त/शरारती)- having or showing a cunning and deceitful nature.<br><strong>Dogmatic </strong>(सिद्धांतवादी)- inclined to lay down principles as undeniably true without consideration of evidence or the opinions of others.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "7",
                    question_en: "16. Sentences of a paragraph are given below in jumbled order. Arrange the sentences in the correct order to form a meaningful and coherent paragraph.<br />A. It is also called a nuclear family.<br />B. It is good to be in a small family because there is better management of resources.<br />C. A small family is one with parents and a maximum of two children.<br />D. The advantages of a small family are numerous.",
                    question_hi: "16. Sentences of a paragraph are given below in jumbled order. Arrange the sentences in the correct order to form a meaningful and coherent paragraph.<br />A. It is also called a nuclear family.<br />B. It is good to be in a small family because there is better management of resources.<br />C. A small family is one with parents and a maximum of two children.<br />D. The advantages of a small family are numerous.",
                    options_en: [" CADB ", " BACD ", 
                                " CBDA ", " ACBD"],
                    options_hi: [" CADB ", " BACD ",
                                " CBDA ", " ACBD"],
                    solution_en: "16.(a) CADB<br />Sentence C will be the starting line as it contains the main subject of the parajumble i.e. a small family. And, Sentence A states nuclear family as another name for a small family. So, A will follow C. Further, Sentence D states that the advantages of a small family are numerous & Sentence B gives one reason why it is good to be in a small family. So, B will follow D. Going through the options, option ‘a’ has the correct sequence.",
                    solution_hi: "16.(a) CADB<br />Sentence C प्रारंभिक line होगी क्योंकि इसमें parajumble का मुख्य विषय ‘a small family’ शामिल है। और, Sentence A में छोटे परिवार के दूसरे नाम के रूप में nuclear family का उल्लेख किया गया है। इसलिए, C के बाद A आएगा। इसके अलावा, Sentence D बताता है कि छोटे परिवार के कई फायदे हैं और Sentence B कारण बताता है कि छोटे परिवार में रहना क्यों अच्छा है। इसलिए, D के बाद B आएगा। अतः options के माध्यम से जाने पर, option \'a\' में सही sequence है।",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "7",
                    question_en: "17. Parts of the following sentence have been given as options. Select the option that contains an error.<br />No other place in this country is so better as Gulmarg, Kashmir.",
                    question_hi: "17. Parts of the following sentence have been given as options. Select the option that contains an error.<br />No other place in this country is so better as Gulmarg, Kashmir.",
                    options_en: [" No other place", " in this country is", 
                                " so better as", " Gulmarg, Kashmir"],
                    options_hi: [" No other place", " in this country is",
                                " so better as", " Gulmarg, Kashmir"],
                    solution_en: "17.(c) so better as<br />Positive degree adjective is used in the structure ‘as…as’ or ‘so…as’ to make a comparison. Therefore, ‘better’ must be replaced with ‘good’. Hence, ‘so good as’ is the most appropriate answer.",
                    solution_hi: "17.(c) so better as<br />किसी sentence में तुलना करने के लिए structure ‘as…as’ or ‘so…as’  में positive degree adjective का प्रयोग किया जाता है। इसलिए, ‘better’ के स्थान पर ‘good’ का प्रयोग होगा। अतः , ‘so good as’ सबसे उपयुक्त उत्तर है।",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "7",
                    question_en: "<p>18. Select the option that expresses the following sentence in active voice.<br>The letter will be delivered by the postman tomorrow.</p>",
                    question_hi: "<p>18. Select the option that expresses the following sentence in active voice.<br>The letter will be delivered by the postman tomorrow.</p>",
                    options_en: ["<p>The postman delivered the letter tomorrow</p>", "<p>The postman will have delivered the letter tomorrow.</p>", 
                                "<p>The postman will deliver the letter tomorrow.</p>", "<p>The postman will be delivering the letter tomorrow.</p>"],
                    options_hi: ["<p>The postman delivered the letter tomorrow</p>", "<p>The postman will have delivered the letter tomorrow.</p>",
                                "<p>The postman will deliver the letter tomorrow.</p>", "<p>The postman will be delivering the letter tomorrow.</p>"],
                    solution_en: "<p>18.(c) The postman will deliver the letter tomorrow. (Correct)<br>(a) The postman <span style=\"text-decoration: underline;\">delivered</span> the letter tomorrow. (Incorrect Tense)<br>(b) The postman <span style=\"text-decoration: underline;\">will have delivered</span> the letter tomorrow. (Incorrect Verb)<br>(d) The postman <span style=\"text-decoration: underline;\">will be delivering</span> the letter tomorrow. (Incorrect Verb)</p>",
                    solution_hi: "<p>18.(c) The postman will deliver the letter tomorrow. (Correct)<br>(a) The postman <span style=\"text-decoration: underline;\">delivered</span> the letter tomorrow. (गलत Tense)<br>(b) The postman <span style=\"text-decoration: underline;\">will have delivered</span> the letter tomorrow. (गलत Verb)<br>(d) The postman <span style=\"text-decoration: underline;\">will be delivering</span> the letter tomorrow. (गलत Verb)</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "7",
                    question_en: "19. The following sentence has been split into four segments. Identify the segment that contains a grammatical error.<br />Our family is planning / a trip to Europe next summer / where we should visit several famous / landmarks and enjoy local cuisine.",
                    question_hi: "19. The following sentence has been split into four segments. Identify the segment that contains a grammatical error.<br />Our family is planning / a trip to Europe next summer / where we should visit several famous / landmarks and enjoy local cuisine.",
                    options_en: [" where we should visit several famous ", " Our family is planning", 
                                " landmarks and enjoy local cuisine ", " a trip to Europe next summer"],
                    options_hi: [" where we should visit several famous ", " Our family is planning",
                                " landmarks and enjoy local cuisine ", " a trip to Europe next summer"],
                    solution_en: "19.(a) where we should visit several famous<br />‘Should’ must be replaced with ‘will’ as the action of visiting several famous landmarks will happen in the future (next summer). Hence, ‘where we will visit several famous’ is the most appropriate answer.",
                    solution_hi: "19.(a) where we should visit several famous<br />दिए गए sentence में ‘should’ के स्थान पर ‘‘will’ का प्रयोग होगा क्योंकि कई प्रसिद्ध स्थलों पर जाने का action, future (next summer) में होगा। अतः, ‘where we will visit several famous’ सबसे उपयुक्त उत्तर है।",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "7",
                    question_en: "<p>20. Select the most appropriate ANTONYM of the underlined word in the following sentence to fill in the blank.<br>Most of the land near the town is <span style=\"text-decoration: underline;\">barren</span>, but the efforts of the farmers have turned it _______ .</p>",
                    question_hi: "<p>20. Select the most appropriate ANTONYM of the underlined word in the following sentence to fill in the blank.<br>Most of the land near the town is <span style=\"text-decoration: underline;\">barren</span>, but the efforts of the farmers have turned it_______ .</p>",
                    options_en: ["<p>sterile</p>", "<p>infertile</p>", 
                                "<p>fertile</p>", "<p>unfruitful</p>"],
                    options_hi: ["<p>sterile</p>", "<p>infertile</p>",
                                "<p>fertile</p>", "<p>unfruitful</p>"],
                    solution_en: "<p>20.(c) <strong>Fertile</strong>- capable of producing abundant vegetation or crops.<br><strong>Barren</strong>- unable to produce any vegetation or crops.<br><strong>Sterile</strong>- not able to reproduce.<br><strong>Infertile</strong>- not able to conceive children or young.<br><strong>Unfruitful</strong>- not yielding fruit or success.</p>",
                    solution_hi: "<p>20.(c) <strong>Fertile </strong>(उपजाऊ)- capable of producing abundant vegetation or crops.<br><strong>Barren </strong>(बंजर)- unable to produce any vegetation or crops.<br><strong>Sterile </strong>(बंजर)- not able to reproduce.<br><strong>Infertile </strong>(बांझ)- not able to conceive children or young.<br><strong>Unfruitful </strong>(अनुपजाऊ)- not yielding fruit or success.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "7",
                    question_en: "<p>21. <strong>Cloze Test:</strong><br>Americans have been arguing about pandemic (21) ______ for two years, and the debate is particularly fraught among parents of small children, for good reasons. While (22) _______ such as masking and isolation mean temporary discomfort or inconvenience for most people, their (23) ______ for still-developing young children are more mysterious, and possibly more significant and lasting. <br>Children with speech or language disorders offer perhaps the (24) ______ example of these murky trade-offs. Pandemic restrictions vary by state, county and school district, but I spoke with parents in California, New York, Massachusetts, Washington, New Jersey, lowa and Maryland, who said their children\'s speech therapy has been (25) ______ : first by the loss of in-person therapy and then by masking requirements, in places that have them.<br>Select the most appropriate option to fill in blank no. 21</p>",
                    question_hi: "<p>21. <strong>Cloze Test:</strong><br>Americans have been arguing about pandemic (21) ______ for two years, and the debate is particularly fraught among parents of small children, for good reasons. While (22) _______ such as masking and isolation mean temporary discomfort or inconvenience for most people, their (23) ______ for still-developing young children are more mysterious, and possibly more significant and lasting. <br>Children with speech or language disorders offer perhaps the (24) ______ example of these murky trade-offs. Pandemic restrictions vary by state, county and school district, but I spoke with parents in California, New York, Massachusetts, Washington, New Jersey, lowa and Maryland, who said their children\'s speech therapy has been (25) ______ : first by the loss of in-person therapy and then by masking requirements, in places that have them.<br>Select the most appropriate option to fill in blank no. 21</p>",
                    options_en: ["<p>restrictions</p>", "<p>intemperance</p>", 
                                "<p>advantages</p>", "<p>handicap</p>"],
                    options_hi: ["<p>restrictions</p>", "<p>intemperance</p>",
                                "<p>advantages</p>", "<p>handicap</p>"],
                    solution_en: "<p>21.(a) <strong>restrictions</strong><br>&lsquo;Restriction&rsquo; means a law or rule that limits or controls something. The given passage states that Americans have been arguing about pandemic restrictions for two years. Hence, &lsquo;restrictions&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>21.(a) <strong>restrictions</strong><br>&lsquo;Restriction&rsquo; का अर्थ है ऐसा कानून या नियम जो किसी चीज़ को सीमित या नियंत्रित करता है। दिए गए passage में कहा गया है कि Americans दो वर्षों से महामारी प्रतिबंधों के बारे में बहस कर रहे हैं। अतः, &lsquo;restrictions&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "7",
                    question_en: "<p>22. <strong>Cloze Test:</strong><br>Americans have been arguing about pandemic (21) ______ for two years, and the debate is particularly fraught among parents of small children, for good reasons. While (22) _______ such as masking and isolation mean temporary discomfort or inconvenience for most people, their (23) ______ for still-developing young children are more mysterious, and possibly more significant and lasting. <br>Children with speech or language disorders offer perhaps the (24) ______ example of these murky trade-offs. Pandemic restrictions vary by state, county and school district, but I spoke with parents in California, New York, Massachusetts, Washington, New Jersey, lowa and Maryland, who said their children\'s speech therapy has been (25) ______ : first by the loss of in-person therapy and then by masking requirements, in places that have them.<br>Select the most appropriate option to fill in blank no. 22</p>",
                    question_hi: "<p>22. <strong>Cloze Test:</strong><br>Americans have been arguing about pandemic (21) ______ for two years, and the debate is particularly fraught among parents of small children, for good reasons. While (22) _______ such as masking and isolation mean temporary discomfort or inconvenience for most people, their (23) ______ for still-developing young children are more mysterious, and possibly more significant and lasting. <br>Children with speech or language disorders offer perhaps the (24) ______ example of these murky trade-offs. Pandemic restrictions vary by state, county and school district, but I spoke with parents in California, New York, Massachusetts, Washington, New Jersey, lowa and Maryland, who said their children\'s speech therapy has been (25) ______ : first by the loss of in-person therapy and then by masking requirements, in places that have them.<br>Select the most appropriate option to fill in blank no. 22</p>",
                    options_en: ["<p>brandings</p>", "<p>measures</p>", 
                                "<p>models</p>", "<p>sluggishness</p>"],
                    options_hi: ["<p>brandings</p>", "<p>measures</p>",
                                "<p>models</p>", "<p>sluggishness</p>"],
                    solution_en: "<p>22.(b) <strong>measures</strong><br>&lsquo;Measures&rsquo; means steps taken to deal with a situation. The given passage states that measures such as masking and isolation mean temporary discomfort or inconvenience for most people. Hence, \'measures\' is the most appropriate answer.</p>",
                    solution_hi: "<p>22.(b) <strong>measures</strong><br>&lsquo;Measures&rsquo; का अर्थ है किसी स्थिति से निपटने के लिए उठाए गए कदम। दिए गए passage में कहा गया है कि mask लगाने और अलगाव (isolation) जैसे उपायों का मतलब ज्यादातर लोगों के लिए अस्थायी परेशानी या असुविधा है। अतः, \'measures\' सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "23",
                    section: "7",
                    question_en: "<p>23. <strong>Cloze Test:</strong><br>Americans have been arguing about pandemic (21) ______ for two years, and the debate is particularly fraught among parents of small children, for good reasons. While (22) _______ such as masking and isolation mean temporary discomfort or inconvenience for most people, their (23) ______ for still-developing young children are more mysterious, and possibly more significant and lasting. <br>Children with speech or language disorders offer perhaps the (24) ______ example of these murky trade-offs. Pandemic restrictions vary by state, county and school district, but I spoke with parents in California, New York, Massachusetts, Washington, New Jersey, lowa and Maryland, who said their children\'s speech therapy has been (25) ______ : first by the loss of in-person therapy and then by masking requirements, in places that have them.<br>Select the most appropriate option to fill in blank no. 23.</p>",
                    question_hi: "<p>23. <strong>Cloze Test:</strong><br>Americans have been arguing about pandemic (21) ______ for two years, and the debate is particularly fraught among parents of small children, for good reasons. While (22) _______ such as masking and isolation mean temporary discomfort or inconvenience for most people, their (23) ______ for still-developing young children are more mysterious, and possibly more significant and lasting. <br>Children with speech or language disorders offer perhaps the (24) ______ example of these murky trade-offs. Pandemic restrictions vary by state, county and school district, but I spoke with parents in California, New York, Massachusetts, Washington, New Jersey, lowa and Maryland, who said their children\'s speech therapy has been (25) ______ : first by the loss of in-person therapy and then by masking requirements, in places that have them.<br>Select the most appropriate option to fill in blank no. 23.</p>",
                    options_en: ["<p>marginalisation</p>", "<p>foundations</p>", 
                                "<p>disapproval</p>", "<p>consequences</p>"],
                    options_hi: ["<p>marginalisation</p>", "<p>foundations</p>",
                                "<p>disapproval</p>", "<p>consequences</p>"],
                    solution_en: "<p>23.(d) <strong>consequences</strong><br>&lsquo;Consequence&rsquo; means a result or effect, typically one that is unwelcome or unpleasant. The given passage states that their consequences for still-developing young children are more mysterious, and possibly more significant and lasting. Hence, \'consequences\' is the most appropriate answer.</p>",
                    solution_hi: "<p>23.(d) <strong>consequences</strong><br>&lsquo;Consequence&rsquo; का अर्थ है परिणाम या प्रभाव, आम तौर पर वह जो अवांछित या अप्रिय होता है। दिए गए passage में कहा गया है कि अभी भी developing young children के लिए उनके परिणाम अधिक रहस्यमय हैं, और संभवतः अधिक महत्वपूर्ण एवं स्थायी हैं। अतः, \'consequences\' सबसे उपयुक्त उत्तर है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "24",
                    section: "7",
                    question_en: "<p>24. <strong>Cloze Test:</strong><br>Americans have been arguing about pandemic (21) ______ for two years, and the debate is particularly fraught among parents of small children, for good reasons. While (22) _______ such as masking and isolation mean temporary discomfort or inconvenience for most people, their (23) ______ for still-developing young children are more mysterious, and possibly more significant and lasting. <br>Children with speech or language disorders offer perhaps the (24) ______ example of these murky trade-offs. Pandemic restrictions vary by state, county and school district, but I spoke with parents in California, New York, Massachusetts, Washington, New Jersey, lowa and Maryland, who said their children\'s speech therapy has been (25) ______ : first by the loss of in-person therapy and then by masking requirements, in places that have them.<br>Select the most appropriate option to fill in blank no. 24.</p>",
                    question_hi: "<p>24. <strong>Cloze Test:</strong><br>Americans have been arguing about pandemic (21) ______ for two years, and the debate is particularly fraught among parents of small children, for good reasons. While (22) _______ such as masking and isolation mean temporary discomfort or inconvenience for most people, their (23) ______ for still-developing young children are more mysterious, and possibly more significant and lasting. <br>Children with speech or language disorders offer perhaps the (24) ______ example of these murky trade-offs. Pandemic restrictions vary by state, county and school district, but I spoke with parents in California, New York, Massachusetts, Washington, New Jersey, lowa and Maryland, who said their children\'s speech therapy has been (25) ______ : first by the loss of in-person therapy and then by masking requirements, in places that have them.<br>Select the most appropriate option to fill in blank no. 24.</p>",
                    options_en: ["<p>clearest</p>", "<p>vague</p>", 
                                "<p>indistinct</p>", "<p>nebulous</p>"],
                    options_hi: ["<p>clearest</p>", "<p>vague</p>",
                                "<p>indistinct</p>", "<p>nebulous</p>"],
                    solution_en: "<p>24.(a) <strong>clearest</strong><br>&lsquo;Clearest&rsquo; means the most obvious. The given passage states that children with speech or language disorders offer perhaps the clearest example of these murky trade-offs. Hence, \'clearest\' is the most appropriate answer.</p>",
                    solution_hi: "<p>24.(a) <strong>clearest</strong><br>&lsquo;Clearest&rsquo; का अर्थ है सबसे स्पष्ट। दिए गए passage में कहा गया है कि speech या language disorders वाले बच्चे शायद इन अंधकारमय संतुलन (murky trade-offs) का सबसे स्पष्ट उदाहरण प्रस्तुत करते हैं। अतः, \'clearest\' सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "25",
                    section: "misc",
                    question_en: "<p>25. <strong>Cloze Test:</strong><br>Americans have been arguing about pandemic (21) ______ for two years, and the debate is particularly fraught among parents of small children, for good reasons. While (22) _______ such as masking and isolation mean temporary discomfort or inconvenience for most people, their (23) ______ for still-developing young children are more mysterious, and possibly more significant and lasting. <br>Children with speech or language disorders offer perhaps the (24) ______ example of these murky trade-offs. Pandemic restrictions vary by state, county and school district, but I spoke with parents in California, New York, Massachusetts, Washington, New Jersey, lowa and Maryland, who said their children\'s speech therapy has been (25) ______ : first by the loss of in-person therapy and then by masking requirements, in places that have them.<br>Select the most appropriate option to fill in blank no. 25.</p>",
                    question_hi: "<p>25. <strong>Cloze Test:</strong><br>Americans have been arguing about pandemic (21) ______ for two years, and the debate is particularly fraught among parents of small children, for good reasons. While (22) _______ such as masking and isolation mean temporary discomfort or inconvenience for most people, their (23) ______ for still-developing young children are more mysterious, and possibly more significant and lasting. <br>Children with speech or language disorders offer perhaps the (24) ______ example of these murky trade-offs. Pandemic restrictions vary by state, county and school district, but I spoke with parents in California, New York, Massachusetts, Washington, New Jersey, lowa and Maryland, who said their children\'s speech therapy has been (25) ______ : first by the loss of in-person therapy and then by masking requirements, in places that have them.<br>Select the most appropriate option to fill in blank no. 25.</p>",
                    options_en: ["<p>disrupted</p>", "<p>regulated</p>", 
                                "<p>measured</p>", "<p>normalised</p>"],
                    options_hi: ["<p>disrupted</p>", "<p>regulated</p>",
                                "<p>measured</p>", "<p>normalised</p>"],
                    solution_en: "<p>25.(a) <strong>disrupted</strong><br>&lsquo;Disrupted&rsquo; means prevent something from continuing. The given passage states that their children&rsquo;s speech therapy has been disrupted. Hence, \'disrupted\' is the most appropriate answer.</p>",
                    solution_hi: "<p>25.(a) <strong>disrupted</strong><br>&lsquo;Disrupted&rsquo; का अर्थ है किसी चीज़ को जारी रहने से रोकना। दिए गए passage में कहा गया है कि उनके बच्चों की speech therapy बाधित हो गई है। अतः, \'disrupted\' सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>