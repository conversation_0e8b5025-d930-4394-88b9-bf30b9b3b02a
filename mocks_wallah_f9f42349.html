<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #7209b7;
            --secondary: #4361ee;
            --accent: #f72585;
            --success: #06d6a0;
            --warning: #ffd166;
            --danger: #ef476f;
            --light: #f8f9fa;
            --dark: #212529;
        }
        body {
            background-color: #f0f2f5;
            font-size: 16px;
            padding-bottom: 60px; /* Space for fixed navigation */
        }
        .navbar {
            background: linear-gradient(135deg, var(--primary), var(--secondary)) !important;
            box-shadow: 0 2px 10px rgba(0,0,0,.1);
            padding: 0.5rem 1rem;
        }
        .navbar-brand {
            font-size: 1.1rem;
            margin-right: 0;
        }
        .navbar .container-fluid {
            justify-content: space-between;
        }
        .controls-group {
            display: flex;
            align-items: center;
            flex-wrap: nowrap;
        }
        .timer-display {
            background: linear-gradient(135deg, #4361ee, #3a0ca3);
            color: white;
            border-radius: 50px;
            box-shadow: 0 3px 8px rgba(0,0,0,.1);
            padding: 0.25rem 0.75rem;
            font-size: 0.875rem;
            white-space: nowrap;
        }
        .nav-btn {
            padding: 0.25rem 0.75rem;
            font-size: 0.875rem;
            margin-left: 0.25rem;
        }
        .question-nav {
            position: fixed;
            top: 56px;
            right: 0;
            height: calc(100vh - 56px);
            width: 280px;
            transform: translateX(280px);
            transition: transform .3s;
            z-index: 100;
            background: #fff;
            box-shadow: -5px 0 15px rgba(0,0,0,.1);
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .main-content {
            transition: margin .3s;
            min-height: calc(100vh - 116px);
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.5;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(32px, 1fr));
            gap: 6px;
        }
        .q-box {
            width: 35px;
            height: 35px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 5px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid #dee2e6;
            background: #fff;
            transition: all .2s;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: 0 3px 5px rgba(0,0,0,.1);
        }
        .q-box.attempted {
            background-color: var(--secondary);
            color: white;
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,.15);
        }
        .card {
            border-radius: 12px;
            overflow: hidden;
            border: none;
            box-shadow: 0 4px 12px rgba(0,0,0,.08);
        }
        .option-label {
            border-radius: 8px;
            transition: all .2s;
            border: 1px solid #dee2e6;
            padding: 0.75rem 1rem;
            font-size: 1rem;
        }
        .option-label:hover {
            background-color: #f8f9fa;
            transform: translateY(-2px);
            box-shadow: 0 3px 8px rgba(0,0,0,.05);
        }
        .form-check-input:checked + .option-label {
            border-color: var(--primary);
            background-color: rgba(114,9,183,.05);
        }
        .btn-primary {
            background-color: var(--primary);
            border-color: var(--primary);
        }
        .btn-outline-primary {
            color: var(--primary);
            border-color: var(--primary);
        }
        .btn-outline-primary:hover {
            background-color: var(--primary);
            color: white;
        }
        /* Bottom fixed navigation for mobile */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: white;
            box-shadow: 0 -2px 10px rgba(0,0,0,.1);
            padding: 10px 15px;
            display: flex;
            justify-content: space-between;
            z-index: 90;
        }
        @media (min-width: 992px) {
            .main-content.with-nav {
                margin-right: 280px;
            }
            .question-nav {
                transform: translateX(0);
            }
            .nav-toggle-btn {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(30px, 1fr));
            }
            .option-label {
                padding: 0.625rem 0.875rem;
                font-size: 0.95rem;
            }
            .question-text {
                font-size: 1rem;
            }
            .navbar-brand {
                font-size: 1rem;
            }
            .timer-display {
                font-size: 0.8125rem;
                padding: 0.2rem 0.6rem;
            }
            .nav-btn {
                padding: 0.2rem 0.6rem;
                font-size: 0.8125rem;
            }
            .container {
                padding-left: 10px;
                padding-right: 10px;
            }
        }
    </style>
</head>
<body>
    <!-- Header navbar -->
    <nav class="navbar navbar-dark sticky-top">
        <div class="container-fluid px-2">
            <a class="navbar-brand fw-bold" href="#"><i class="fas fa-graduation-cap me-1"></i></a>
            <div class="controls-group">
                <!-- Timer -->
                <div class="timer-display me-1 fw-bold" id="timer">
                    <i class="far fa-clock me-1"></i><span id="timer-display">15:00</span>
                </div>
                <!-- Submit button -->
                <button class="btn btn-light btn-sm nav-btn me-1" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Language toggle -->
                <button class="btn btn-light btn-sm nav-btn me-1" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="btn btn-light btn-sm nav-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </nav>
    <!-- Main content -->
    <div class="main-content" id="main-content">
        <div class="container py-3">
            <!-- Question counter -->
            <div class="fw-bold text-center mb-3" id="question-counter">Question 1 of 25</div>
            <!-- Questions will be displayed here -->
            <div id="questions-container"></div>
        </div>
    </div>
    <!-- Bottom navigation for mobile -->
    <div class="bottom-nav d-lg-none">
        <button class="btn btn-outline-primary px-3" onclick="prevQuestion()">
            <i class="fas fa-chevron-left me-1"></i> Previous
        </button>
        <button class="btn btn-outline-primary px-3" onclick="nextQuestion()">
            Next <i class="fas fa-chevron-right ms-1"></i>
        </button>
    </div>
    <!-- Question navigation sidebar -->
    <div class="question-nav" id="question-nav">
        <div class="p-3">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h5 class="m-0">Questions</h5>
                <div>
                    <span class="badge bg-secondary me-1" id="attempted-count">0</span>
                    <span class="badge bg-secondary" id="total-count">25</span>
                    <button class="btn btn-sm btn-outline-secondary ms-2 d-lg-none" onclick="toggleNav()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
            <!-- Section selector moved here -->
            <div class="mb-3">
                <label for="section-selector" class="form-label small text-muted">Jump to Section</label>
                <select class="form-select form-select-sm" id="section-selector" onchange="switchSection(this.value)">
                    <option value="all">All Sections</option>
                </select>
            </div>
            <div class="question-grid" id="question-boxes"></div>
        </div>
    </div>
    <!-- Results modal -->
    <div class="modal fade" id="results-modal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content border-0">
                <div class="modal-header border-0" style="background:linear-gradient(135deg,var(--primary),var(--secondary));color:white">
                    <h5 class="modal-title">Test Results</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body p-4">
                    <div class="row text-center g-2">
                        <div class="col-6">
                            <div class="p-3 rounded shadow-sm" style="background:linear-gradient(135deg,#4361ee,#3a0ca3);color:white">
                                <h3 id="score-value" class="mb-0 fw-bold">0</h3>
                                <div class="small">Score</div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="p-3 rounded shadow-sm" style="background:linear-gradient(135deg,#06d6a0,#1b9aaa);color:white">
                                <h3 id="correct-value" class="mb-0 fw-bold">0</h3>
                                <div class="small">Correct</div>
                            </div>
                        </div>
                        <div class="col-6 mt-2">
                            <div class="p-3 rounded shadow-sm" style="background:linear-gradient(135deg,#ef476f,#f72585);color:white">
                                <h3 id="incorrect-value" class="mb-0 fw-bold">0</h3>
                                <div class="small">Incorrect</div>
                            </div>
                        </div>
                        <div class="col-6 mt-2">
                            <div class="p-3 rounded shadow-sm" style="background:linear-gradient(135deg,#6c757d,#495057);color:white">
                                <h3 id="unattempted-value" class="mb-0 fw-bold">0</h3>
                                <div class="small">Unattempted</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer border-0">
                    <button type="button" class="btn btn-primary" onclick="reviewTest()">Review Test</button>
                </div>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 15 * 60;
        let resultsModal;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            resultsModal = new bootstrap.Modal(document.getElementById('results-modal'));
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["6"] = {
                name: "Reasoning",
                start: 0,
                end: 23
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="6">Reasoning</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 24,
                end: 24
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "6",
                    question_en: "<p>1.<span style=\"font-family: Times New Roman;\"> Four different positions of the same dice are shown, the six faces of which are numbered from 1 to 6. Select the number that will be on the face opposite to the one having the number &lsquo;1&rsquo;.</span><br><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1665461326/word/media/image19.png\"></p>",
                    question_hi: "<p>1. <span style=\"font-family: Palanquin Dark;\">एक ही पासे के चार अलग-अलग स्थिति दिखाई गई हैं, जिसमे 1 से 6 तक छह फलक गए हैं। उस संख्या का चयन कीजिये जो संख्या \'1\' वाले फलक के विपरीत फलक पर होगी।</span><br><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1665461326/word/media/image19.png\"></p>",
                    options_en: ["<p>6</p>", "<p>2</p>", 
                                "<p>4</p>", "<p>3</p>"],
                    options_hi: ["<p>6</p>", "<p>2</p>",
                                "<p>4</p>", "<p>3</p>"],
                    solution_en: "<p>1.(d) <span style=\"font-family: Times New Roman;\">From dice 1 and dice 2 we can conclude that number 3 will be on the face opposite to face having the number 1.</span></p>",
                    solution_hi: "<p>1.(d) <span style=\"font-family: Palanquin;\">पासे 1 और पासे 2 से हम यह निष्कर्ष निकाल सकते हैं कि संख्या 3 संख्या 1 वाले फलक के विपरीत फलक पर होगी।</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "6",
                    question_en: "<p>2.<span style=\"font-family: Times New Roman;\"> Parmila is the granddaughter of Akun who is married to Nikita. Murali is the brother-in-law of Akun who has two daughters but no son. Rahul is the cousin of Kamal and brother of Parmila. Uday and Vanmay are the sons-in-law of Nikita. Vanmay is married to Yamini and they have two daughters and one son. Uday has one son and one daughter. Tina and Smita are the daughters of Yamini. Murali is unmarried. How is Yamini related to Rahul?</span></p>",
                    question_hi: "<p><span style=\"font-family: Roboto;\">2. </span><span style=\"font-family: Palanquin;\">परमिला , अकुन की पोती है, जिसकी शादी निकिता से हुई है। मुरली , अकुन का साला है जिसकी दो बेटियां हैं लेकिन कोई बेटा नहीं है। राहुल , कमल का चचेरा भाई और परमिला का भाई है। उदय और वनमय , निकिता के दामाद हैं। वनमय की शादी यामिनी से हुई है और उनकी दो बेटियां और एक बेटा है। उदय का एक बेटा और एक बेटी है। टीना और स्मिता , यामिनी की बेटियां हैं। मुरली अविवाहित है। यामिनी का राहुल से क्या संबंध है?</span></p>",
                    options_en: ["<p>Aunt</p>", "<p>Mother</p>", 
                                "<p>Sister</p>", "<p>Daughter</p>"],
                    options_hi: ["<p>चाची</p>", "<p>माँ</p>",
                                "<p>बहन</p>", "<p>बेटी</p>"],
                    solution_en: "<p>2.(a)<br><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1665461326/word/media/image34.png\"><br><span style=\"font-family: Times New Roman;\">From the above diagram we can see that Yamini is the aunt of Rahul.</span></p>",
                    solution_hi: "<p>2.(a)<br><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1665461326/word/media/image34.png\"><br><span style=\"font-family: Palanquin;\">उपरोक्त चित्र से हम देख सकते हैं कि यामिनी राहुल की मौसी है।</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "6",
                    question_en: "<p>3. <span style=\"font-family: Times New Roman;\">Select the figure from among the given options that can replace the question mark (?) in the following series.</span></p>\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1665461326/word/media/image31.png\" width=\"309\" height=\"68\"></p>",
                    question_hi: "<p><span style=\"font-family: Roboto;\">3. </span><span style=\"font-family: Palanquin;\">दिए गए विकल्पों में से उस आकृति का चयन कीजिये जो निम्नलिखित श्रृंखला में प्रश्नवाचक चिन्ह (?) को प्रतिस्थापित कर सकता है।</span></p>\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1665461326/word/media/image31.png\" width=\"305\" height=\"67\"></p>",
                    options_en: ["<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1665461326/word/media/image10.png\" width=\"77\" height=\"71\"><span style=\"font-family: Times New Roman;\"> </span></p>", "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1665461326/word/media/image8.png\" width=\"77\" height=\"71\"></p>", 
                                "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1665461326/word/media/image16.png\" width=\"77\" height=\"71\"></p>", "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1665461326/word/media/image4.png\" width=\"77\" height=\"71\"></p>"],
                    options_hi: ["<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1665461326/word/media/image10.png\" width=\"77\" height=\"71\"><span style=\"font-family: Times New Roman;\"> </span></p>", "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1665461326/word/media/image8.png\" width=\"77\" height=\"71\"></p>",
                                "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1665461326/word/media/image16.png\" width=\"77\" height=\"71\"></p>", "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1665461326/word/media/image4.png\" width=\"77\" height=\"71\"></p>"],
                    solution_en: "<p>3.(b)</p>\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1665461326/word/media/image8.png\" width=\"77\" height=\"71\"></p>",
                    solution_hi: "<p>3.(b)</p>\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1665461326/word/media/image8.png\" width=\"77\" height=\"71\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "6",
                    question_en: "<p>4. <span style=\"font-family: Times New Roman;\">Study the given matrix carefully and select the number from among the given options that can replace the question mark (?) in it.</span><br><strong id=\"docs-internal-guid-9bba0deb-7fff-03d0-a5fd-269c5c917774\"></strong><strong id=\"docs-internal-guid-9bba0deb-7fff-03d0-a5fd-269c5c917774\"></strong><strong id=\"docs-internal-guid-def16071-7fff-1fb3-7c34-916f12f40e06\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXf3GtGhyARhfTwmdsT-tjGazkD0RONyvHq9NTcUJ_5uuTYjJteHhl98kqsON7vgRBsTfd05LNwRF5a0PPyfLzI6aAZOcxtnEI9vGc-GxOuI_w885FgwVlsZe0_EH5nr2GrhxYQH4Q?key=uId0YmVkLfO8CALZasynZatR\" width=\"310\" height=\"144\"></strong></p>",
                    question_hi: "<p><span style=\"font-family: Roboto;\">4. </span><span style=\"font-family: Palanquin;\">दिए गए मैट्रिक्स का ध्यानपूर्वक अध्ययन कीजिये और दिए गए विकल्पों में से उस संख्या का चयन कीजिये जो उसमें प्रश्न चिह्न (?) को प्रतिस्थापित कर सके।</span><br><strong id=\"docs-internal-guid-def16071-7fff-1fb3-7c34-916f12f40e06\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXf3GtGhyARhfTwmdsT-tjGazkD0RONyvHq9NTcUJ_5uuTYjJteHhl98kqsON7vgRBsTfd05LNwRF5a0PPyfLzI6aAZOcxtnEI9vGc-GxOuI_w885FgwVlsZe0_EH5nr2GrhxYQH4Q?key=uId0YmVkLfO8CALZasynZatR\" width=\"241\" height=\"112\"></strong></p>",
                    options_en: ["<p>431</p>", "<p>335</p>", 
                                "<p>100</p>", "<p>129</p>"],
                    options_hi: ["<p>431</p>", "<p>335</p>",
                                "<p>100</p>", "<p>129</p>"],
                    solution_en: "<p>4.(a) <span style=\"font-family: Times New Roman;\">&nbsp;&nbsp;</span><br><strong>Logic:</strong> (First column)<sup>3</sup> &ndash; (Second column)<sup>2 </sup>= (third column)<br>= (7)<sup>3</sup> &ndash; (13)<sup>2</sup> = 174<br>Similarly,<br>(11)<sup>3</sup> - (30)<sup>2</sup> = 431</p>",
                    solution_hi: "<p>4.(a) <span style=\"font-family: Times New Roman;\">&nbsp;</span><br><strong id=\"docs-internal-guid-540e6190-7fff-9e46-93a8-144fb2376be5\">तर्क :</strong> (पहला कॉलम)<sup>3</sup>&ndash; (दूसरा कॉलम)<sup>2 </sup>= तीसरा कॉलम&nbsp;<br>(7)<sup>3</sup> &ndash; (13)<sup>2</sup> = 174<br>इसी तरह,<br>(11)<sup>3</sup> - (30)<sup>2</sup> = 431</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "6",
                    question_en: "<p>5. <span style=\"font-family: Times New Roman;\">Select the given option in which the given figure is embedded (rotation is NOT allowed).</span></p>\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1665461326/word/media/image11.png\" width=\"66\" height=\"106\"></p>",
                    question_hi: "<p><span style=\"font-family: Roboto;\">5. </span><span style=\"font-family: Palanquin;\">दिए गए विकल्प का चयन कीजिये जिसमें दी गई आकृति अंतर्निहित है (घुमाने की अनुमति नहीं है)।</span></p>\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1665461326/word/media/image11.png\" width=\"66\" height=\"106\"></p>",
                    options_en: ["<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1665461326/word/media/image39.png\" width=\"63\" height=\"105\"><span style=\"font-family: Times New Roman;\"> </span></p>", "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1665461326/word/media/image26.png\" width=\"89\" height=\"99\"></p>", 
                                "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1665461326/word/media/image29.png\" width=\"91\" height=\"75\"><span style=\"font-family: Times New Roman;\"> </span></p>", "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1665461326/word/media/image32.png\" width=\"87\" height=\"90\"></p>"],
                    options_hi: ["<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1665461326/word/media/image39.png\" width=\"65\" height=\"109\"><span style=\"font-family: Times New Roman;\"> </span></p>", "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1665461326/word/media/image26.png\" width=\"89\" height=\"99\"></p>",
                                "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1665461326/word/media/image29.png\" width=\"83\" height=\"68\"><span style=\"font-family: Times New Roman;\"> </span></p>", "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1665461326/word/media/image32.png\" width=\"90\" height=\"93\"></p>"],
                    solution_en: "<p>5.(b)</p>\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1665461326/word/media/image26.png\" width=\"95\" height=\"105\"></p>",
                    solution_hi: "<p>5.(b)</p>\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1665461326/word/media/image26.png\" width=\"95\" height=\"105\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "6",
                    question_en: "<p>6. <span style=\"font-family: Times New Roman;\">Two orientations of the same box are shown. How will this box </span><span style=\"font-family: Times New Roman;\">look</span><span style=\"font-family: Times New Roman;\"> when unfolded?</span></p>\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1665461326/word/media/image24.png\" width=\"160\" height=\"79\"></p>",
                    question_hi: "<p><span style=\"font-family: Roboto;\">6. </span><span style=\"font-family: Palanquin;\">एक ही बॉक्स की दो स्थितियों को दर्शाया गया हैं। खोलने पर यह बॉक्स दिखेगा?</span></p>\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1665461326/word/media/image24.png\" width=\"156\" height=\"77\"></p>",
                    options_en: ["<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1665461326/word/media/image35.png\" width=\"89\" height=\"106\"></p>", "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1665461326/word/media/image7.png\" width=\"95\" height=\"113\"></p>", 
                                "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1665461326/word/media/image37.png\" width=\"91\" height=\"108\"><span style=\"font-family: Times New Roman;\"> </span></p>", "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1665461326/word/media/image38.png\" width=\"92\" height=\"110\"></p>"],
                    options_hi: ["<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1665461326/word/media/image35.png\" width=\"102\" height=\"121\"></p>", "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1665461326/word/media/image7.png\" width=\"100\" height=\"119\"></p>",
                                "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1665461326/word/media/image37.png\" width=\"98\" height=\"117\"><span style=\"font-family: Times New Roman;\"> </span></p>", "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1665461326/word/media/image38.png\" width=\"100\" height=\"119\"></p>"],
                    solution_en: "<p>6.(a)<br><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1665461326/word/media/image24.png\" width=\"172\" height=\"85\"><br><span style=\"font-family: Times New Roman;\">As we know that adjacent faces cannot be on opposite side.</span><br><span style=\"font-family: Times New Roman;\">From the given situation of dice we can conclude opposite of 5 cannot be 3 or 8 and opposite of 7</span><br><span style=\"font-family: Times New Roman;\">Cannot be 6 or 4</span><br><span style=\"font-family: Times New Roman;\">in option (b) opposite of 5 is 8</span><br><span style=\"font-family: Times New Roman;\">in option (c) opposite of 3 is 5</span><br><span style=\"font-family: Times New Roman;\">in option (d) opposite of 5 is 8</span><br><span style=\"font-family: Times New Roman;\">so, option (a) is follow as opposite of 3 is 4 , opposite of 6 is 8 and opposite of 7 is 5</span><br><span style=\"font-family: Times New Roman;\">can be possible. </span></p>",
                    solution_hi: "<p>6.(a)<br><span style=\"font-family: Palanquin;\"><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1665461326/word/media/image24.png\" width=\"172\" height=\"85\"></span><br><span style=\"font-family: Palanquin;\">जैसा कि हम जानते हैं कि आसन्न फलक , विपरीत दिशा में नहीं हो सकते।</span><br><span style=\"font-family: Palanquin;\">पासे की दी गई स्थिति से हम यह निष्कर्ष निकाल सकते हैं कि 5 का विपरीत 3 या 8 नहीं हो सकता और 7 का विपरीत नहीं हो सकता</span><br><span style=\"font-family: Palanquin;\">6 या 4 . नहीं हो सकता</span><br><span style=\"font-family: Palanquin;\">विकल्प (b) में 5 के विपरीत 8 है।</span><br><span style=\"font-family: Palanquin;\">विकल्प (c) में 3 के विपरीत 5 है।</span><br><span style=\"font-family: Palanquin;\">विकल्प (d) में 5 के विपरीत 8 है।</span><br><span style=\"font-family: Palanquin;\">इसलिए, विकल्प (a) अनुसरण करता है क्योंकि 3 के विपरीत 4 है, 6 के विपरीत 8 है और 7 के विपरीत 5 है</span><br><span style=\"font-family: Palanquin;\">संभव हो सकता है।</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "6",
                    question_en: "<p>7. <span style=\"font-family: Times New Roman;\">Read the given statements and conclusions carefully. Assuming that the information given in the statements is true, even if it appears to be at variance with commonly known facts, decide which of the given conclusions logically follow(s) from the statements.</span><br><span style=\"font-family: Times New Roman;\"><strong>Statements</strong>:</span><br><span style=\"font-family: Times New Roman;\">No pink is yellow.</span><br><span style=\"font-family: Times New Roman;\">No yellow is white.</span><br><span style=\"font-family: Times New Roman;\">All reds are yellows.</span><br><span style=\"font-family: Times New Roman;\"><strong>Conclusions</strong>:</span><br><span style=\"font-family: Times New Roman;\">I. No red is pink.</span><br><span style=\"font-family: Times New Roman;\">II. No red is white.</span><br><span style=\"font-family: Times New Roman;\">III. No white is yellow.</span></p>",
                    question_hi: "<p><span style=\"font-family: Roboto;\">7. </span><span style=\"font-family: Palanquin;\">दिए गए कथनों और निष्कर्षों को ध्यानपूर्वक पढ़ें। यह मानते हुए कि कथनों में दी गई जानकारी सत्य है, भले ही वह सामान्य रूप से ज्ञात तथ्यों से भिन्न प्रतीत होती हो, बताइये कि दिए गए निष्कर्षों में से कौन सा कथनों का तार्किक रूप से अनुसरण करता है।</span><br><span style=\"font-family: Palanquin;\"><strong>कथन</strong>:</span><br><span style=\"font-family: Palanquin;\">कोई गुलाबी पीला नहीं है.</span><br><span style=\"font-family: Palanquin;\">कोई पीला सफ़ेद नहीं है।</span><br><span style=\"font-family: Palanquin;\">सभी लाल पीला हैं।</span><br><span style=\"font-family: Palanquin;\"><strong>निष्कर्ष</strong>:</span><br><span style=\"font-family: Palanquin;\">I. कोई लाल गुलाबी नहीं है।</span><br><span style=\"font-family: Palanquin;\">II. कोई लाल सफ़ेद नहीं है।</span><br><span style=\"font-family: Palanquin;\">III. कोई सफ़ेद पीला नहीं है.</span></p>",
                    options_en: ["<p>Only conclusions I and II follow</p>", "<p>Only conclusions I and III follow</p>", 
                                "<p>Only conclusions II and III follow</p>", "<p>All the conclusions follow</p>"],
                    options_hi: ["<p>केवल निष्कर्ष I और II अनुसरण करते हैं</p>", "<p>केवल निष्कर्ष I और III अनुसरण करते हैं</p>",
                                "<p>केवल निष्कर्ष II और III अनुसरण करते हैं</p>", "<p>सभी निष्कर्ष अनुसरण करते हैं</p>"],
                    solution_en: "<p>7.(d)<br><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1665461326/word/media/image12.png\" width=\"152\" height=\"121\"><br><span style=\"font-family: Times New Roman;\">Clearly, we can see that all conclusions follow.</span></p>",
                    solution_hi: "<p>7.(d)<br><strong id=\"docs-internal-guid-1a69b487-7fff-fcbe-c764-8e4891ae75de\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdrpVsPAILt_OvzdmDVujujfV58MNjoBLSS2ni5L10BQ04mK157pRmvz2KTiQKb5EZlWhv6A663qW3lRh5ThRcJPCcHp6xhbfgQ3SBV8GsSINOPKLnQ-6EjWZ40Atql3iMqndWG?key=uId0YmVkLfO8CALZasynZatR\" width=\"169\" height=\"141\"></strong><br><span style=\"font-family: Palanquin;\">स्पष्ट रूप से, हम देख सकते हैं कि सभी निष्कर्ष अनुसरण करते हैं।</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "6",
                    question_en: "<p>8.<span style=\"font-family: Times New Roman;\"> Select the letter-cluster from among the given options that can replace the question mark (?) in the following series.</span><br><span style=\"font-family: Times New Roman;\">KYBA , </span><span style=\"font-family: Times New Roman;\">FTEE</span><span style=\"font-family: Times New Roman;\"> , AOHI, VJKO , ? , LZQA</span></p>",
                    question_hi: "<p><span style=\"font-family: Roboto;\">8. </span><span style=\"font-family: Palanquin;\">दिए गए विकल्पों में से उस अक्षर-समूह को </span><span style=\"font-family: Palanquin;\">चुनिए जो निम्नलिखित श्रृंखला में प्रश्नवाचक चिन्ह </span>को प्रतिस्थापित कर सकता है।<br><span style=\"font-family: Roboto;\">KYBA, </span><span style=\"font-family: Roboto;\">FTEE</span><span style=\"font-family: Roboto;\">, AOHI, VJKO, ?, LZQA</span></p>",
                    options_en: ["<p>PFNU</p>", "<p>QENU</p>", 
                                "<p>QEMI</p>", "<p>QDNI</p>"],
                    options_hi: ["<p>PFNU</p>", "<p>QENU</p>",
                                "<p>QEMI</p>", "<p>QDNI</p>"],
                    solution_en: "<p>8.(b) <span style=\"font-family: Times New Roman;\">For first letter of each word : K &ndash; 5 = F, F &ndash; 5 = A, A &ndash; 5 = V, V &ndash; 5 = </span><strong><span style=\"font-family: Times New Roman;\">Q</span></strong><span style=\"font-family: Times New Roman;\">, Q &ndash; 5 = L</span><br><span style=\"font-family: Times New Roman;\">For second letter of each word : Y &ndash; 5 = T, T &ndash; 5 = O, O &ndash; 5 = J, J &ndash; 5 = </span><strong><span style=\"font-family: Times New Roman;\">E</span></strong><span style=\"font-family: Times New Roman;\">, E &ndash; 5 = Z</span><br><span style=\"font-family: Times New Roman;\">For third letter of each word : B + 3 = E, E + 3 = H, H + 3 = K, K + 3 = <strong>N</strong>, N + 3 = Q</span><br><span style=\"font-family: Times New Roman;\">For fourth letter of each word : consecutive vowels are given &ndash; A, E, I, O, </span><strong><span style=\"font-family: Times New Roman;\">U</span></strong><span style=\"font-family: Times New Roman;\">, A</span><br><span style=\"font-family: Times New Roman;\">Hence, we get : QENU</span></p>",
                    solution_hi: "<p>8.(b) <span style=\"font-family: Palanquin;\">प्रत्येक शब्द के पहले अक्षर के लिए : K &ndash; 5 = F, F &ndash; 5 = A, A &ndash; 5 = V, V &ndash; 5 = </span><strong><span style=\"font-family: Roboto;\">Q</span></strong><span style=\"font-family: Roboto;\">, Q &ndash; 5 = L</span><br><span style=\"font-family: Palanquin;\">प्रत्येक शब्द के दूसरे अक्षर के लिए : Y &ndash; 5 = T, T &ndash; 5 = O, O &ndash; 5 = J, J &ndash; 5 = </span><strong><span style=\"font-family: Roboto;\">E</span></strong><span style=\"font-family: Roboto;\">, E &ndash; 5 = Z</span><br><span style=\"font-family: Palanquin;\">प्रत्येक शब्द के तीसरे अक्षर के लिए : B + 3 = E, E + 3 = H, H + 3 = K, K + 3 = <strong>N</strong>, N + 3 = Q</span><br><span style=\"font-family: Palanquin;\">प्रत्येक शब्द के चौथे अक्षर के लिए क्रमागत स्वर दिए गए हैं &ndash; A, E, I, O, <strong>U</strong>, A</span><br><span style=\"font-family: Palanquin;\">इसलिए, हम प्राप्त करते हैं : QENU</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "6",
                    question_en: "<p>9.<span style=\"font-family: Times New Roman;\"> In a certain code language, </span><span style=\"font-family: Times New Roman;\">\'RAKHI\'</span><span style=\"font-family: Times New Roman;\"> is coded as </span><span style=\"font-family: Times New Roman;\">36-2-22-16-18</span><span style=\"font-family: Times New Roman;\"> and </span><span style=\"font-family: Times New Roman;\">\'SHALU\'</span><span style=\"font-family: Times New Roman;\"> is coded as </span><span style=\"font-family: Times New Roman;\">38-16-2-24-42</span><span style=\"font-family: Times New Roman;\">. How will </span><span style=\"font-family: Times New Roman;\">\'MANJU</span><span style=\"font-family: Times New Roman;\">\' be coded in that language ?</span></p>",
                    question_hi: "<p><span style=\"font-family: Roboto;\">9. </span><span style=\"font-family: Palanquin;\">एक निश्चित कूट भाषा में </span><span style=\"font-family: Roboto;\">\'RAKHI\'</span><span style=\"font-family: Palanquin;\"> को </span><span style=\"font-family: Roboto;\">36-2-22-16-18 </span><span style=\"font-family: Palanquin;\">और </span><span style=\"font-family: Roboto;\">\'SHALU\'</span><span style=\"font-family: Palanquin;\"> को </span><span style=\"font-family: Roboto;\">38-16-2-24-42</span><span style=\"font-family: Palanquin;\"> के रूप में कोडित किया जाता है। उसी भाषा में </span><span style=\"font-family: Roboto;\">\'MANJU\'</span><span style=\"font-family: Palanquin;\"> को किस प्रकार कोडित किया जाएगा ?</span></p>",
                    options_en: ["<p>13-2-28-10-24</p>", "<p>13-2-14-10-24</p>", 
                                "<p>26-1-14-20-42</p>", "<p>26-2-28-20-42</p>"],
                    options_hi: ["<p>13-2-28-10-24</p>", "<p>13-2-14-10-24</p>",
                                "<p>26-1-14-20-42</p>", "<p>26-2-28-20-42</p>"],
                    solution_en: "<p>9.(d) <span style=\"font-family: Times New Roman;\"><strong>Logic </strong>:</span><span style=\"font-family: Times New Roman;\"> Each letter is coded by :- (Place value in alphabet &times;</span><span style=\"font-family: Times New Roman;\">2)</span><br><strong id=\"docs-internal-guid-007ed748-7fff-9d2a-d730-c59b7439f656\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXe3c2pbIZLBowl0K3i6hmE2M5Sv-qG3yLhxG4GbkDcI7LCYoTu0E8JLiA7wTnmuXmLU6rVUSIzRW4wYkrfq6N-jt732We9E6hmReuHdneHgFKN3VI1lznvzy0lhhUhqAAjK5XR0BQ?key=Mh_cSfYNBpRjs_hh_dqMQq0Q\" width=\"257\" height=\"76\"></strong><br><span style=\"font-family: Times New Roman;\">Similarly,</span><br><strong id=\"docs-internal-guid-c822dffe-7fff-e266-058f-4a1a71978f9a\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXeD9ZmNDXyokm9flcMMYxxuU3bMJosQrDFD09p-WbCflQU0uQ1GOPs4kjYVFqu5SQr-y04JwFC2hEAM1zVCO5CjignPJpKIBfY4d1P3lS41fuMIuqhuDoS939z_BfC-J_iUd5K4gg?key=Mh_cSfYNBpRjs_hh_dqMQq0Q\" width=\"266\" height=\"69\"></strong><br><br></p>",
                    solution_hi: "<p>9.(d) <span style=\"font-family: Palanquin;\"><strong>तर्क </strong>: प्रत्येक अक्षर को कूटबद्ध किया जाता है :- (वर्णमाला में स्थितीय मान &times;</span><span style=\"font-family: Roboto;\">2)</span><br><span style=\"font-family: Palanquin;\">&nbsp; <strong id=\"docs-internal-guid-007ed748-7fff-9d2a-d730-c59b7439f656\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXe3c2pbIZLBowl0K3i6hmE2M5Sv-qG3yLhxG4GbkDcI7LCYoTu0E8JLiA7wTnmuXmLU6rVUSIzRW4wYkrfq6N-jt732We9E6hmReuHdneHgFKN3VI1lznvzy0lhhUhqAAjK5XR0BQ?key=Mh_cSfYNBpRjs_hh_dqMQq0Q\" width=\"257\" height=\"76\"></strong></span><br><span style=\"font-family: Palanquin;\">इसी तरह,</span><br><strong id=\"docs-internal-guid-c822dffe-7fff-e266-058f-4a1a71978f9a\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXeD9ZmNDXyokm9flcMMYxxuU3bMJosQrDFD09p-WbCflQU0uQ1GOPs4kjYVFqu5SQr-y04JwFC2hEAM1zVCO5CjignPJpKIBfY4d1P3lS41fuMIuqhuDoS939z_BfC-J_iUd5K4gg?key=Mh_cSfYNBpRjs_hh_dqMQq0Q\" width=\"266\" height=\"69\"></strong></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "6",
                    question_en: "<p>10. <span style=\"font-family: Times New Roman;\">Select the option that is related to the third number in the same way as the second number is related to the first number.</span><br><span style=\"font-family: Times New Roman;\">23 : 441 :: 28 : ?</span></p>",
                    question_hi: "<p><span style=\"font-family: Roboto;\">10. </span><span style=\"font-family: Palanquin;\">उस विकल्प का चयन कीजिये जो तीसरी संख्या से उसी प्रकार संबंधित है जैसे दूसरी संख्या पहली संख्या से संबंधित है।</span><br><span style=\"font-family: Roboto;\">23 : 441 :: 28 : ?</span></p>",
                    options_en: ["<p>692</p>", "<p>494</p>", 
                                "<p>528</p>", "<p>676</p>"],
                    options_hi: ["<p>692</p>", "<p>494</p>",
                                "<p>528</p>", "<p>676</p>"],
                    solution_en: "<p>10.(d) <span style=\"font-family: Times New Roman;\">&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"bold-italic\">L</mi><mi mathvariant=\"bold-italic\">o</mi><mi mathvariant=\"bold-italic\">g</mi><mi mathvariant=\"bold-italic\">i</mi><mi mathvariant=\"bold-italic\">c</mi><mo>:</mo><mo>-</mo><mo>&#160;</mo><mi>n</mi><mo>&#160;</mo><mo>:</mo><mo>&#160;</mo><msup><mrow><mo>(</mo><mi>n</mi><mo>-</mo><mn>2</mn><mo>)</mo></mrow><mn>2</mn></msup><mo>&#160;</mo></math></span><br><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>23</mn><mo>:</mo><mo>&#160;</mo><msup><mfenced><mrow><mn>23</mn><mo>-</mo><mn>2</mn></mrow></mfenced><mrow><mn>2</mn><mo>&#160;</mo><mo>&#160;</mo></mrow></msup></math></span><br><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>23</mn><mo>:</mo><mn>441</mn></math></span><br><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>S</mi><mi>i</mi><mi>m</mi><mi>i</mi><mi>l</mi><mi>a</mi><mi>r</mi><mi>l</mi><mi>y</mi><mo>,</mo></math></span><br><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>28</mn><mo>:</mo><mo>&#160;</mo><msup><mfenced><mrow><mn>28</mn><mo>-</mo><mn>2</mn></mrow></mfenced><mn>2</mn></msup></math></span><br><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>28</mn><mo>:</mo><mo>&#160;</mo><mn>676</mn></math></span></p>",
                    solution_hi: "<p>10.(d) <math style=\"font-family: Verdana;\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"bold\">&#2340;&#2352;&#2381;&#2325;</mi><mo>&#160;</mo><mo>:</mo><mo>-</mo><mo>&#160;</mo><mi>n</mi><mo>&#160;</mo><mo>:</mo><mo>&#160;</mo><msup><mrow><mo>(</mo><mi>n</mi><mo>-</mo><mn>2</mn><mo>)</mo></mrow><mn>2</mn></msup><mo>&#160;</mo></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>23</mn><mo>:</mo><mo>&#160;</mo><msup><mfenced><mrow><mn>23</mn><mo>-</mo><mn>2</mn></mrow></mfenced><mrow><mn>2</mn><mo>&#160;</mo><mo>&#160;</mo></mrow></msup></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>23</mn><mo>:</mo><mn>441</mn></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#2311;&#2360;&#2368;</mi><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo><mi>&#2340;&#2352;&#2361;</mi><mo>&#160;</mo><mo>&#160;</mo><mo>,</mo></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>28</mn><mo>:</mo><mo>&#160;</mo><msup><mfenced><mrow><mn>28</mn><mo>-</mo><mn>2</mn></mrow></mfenced><mn>2</mn></msup></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>28</mn><mo>:</mo><mo>&#160;</mo><mn>676</mn></math></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "6",
                    question_en: "<p>11.<span style=\"font-family: Times New Roman;\"> Select the correct mirror image of the given combination when the mirror is placed at &lsquo;PQ&rsquo; as shown.</span><br><strong id=\"docs-internal-guid-04b55bbf-7fff-40bb-04e2-d09c3c733f09\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXelGaCmTLIP5fHYBOaOf_skxv9PKjQlfs7JHa1k4tPKJ-Jt-U2ZiTzlC-k9Cmmt1_xdUOhSnA4ZX_ZtF6vAmbxzGbLQixACi430v_2KdWgXSTOD08g-_DuoBH9383WVMQsgVRZK?key=uId0YmVkLfO8CALZasynZatR\" width=\"165\" height=\"113\"></strong></p>",
                    question_hi: "<p><span style=\"font-family: Roboto;\">11. </span><span style=\"font-family: Palanquin;\">दिए गए संयोजन की सही दर्पण छवि का चयन कीजिये जब दर्पण को \'PQ\' पर रखा गया है, जैसा कि दिखाया गया है।</span></p>\n<p><strong id=\"docs-internal-guid-d5217d22-7fff-861e-d09f-4df16581614c\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXelGaCmTLIP5fHYBOaOf_skxv9PKjQlfs7JHa1k4tPKJ-Jt-U2ZiTzlC-k9Cmmt1_xdUOhSnA4ZX_ZtF6vAmbxzGbLQixACi430v_2KdWgXSTOD08g-_DuoBH9383WVMQsgVRZK?key=uId0YmVkLfO8CALZasynZatR\" width=\"165\" height=\"113\"></strong></p>",
                    options_en: ["<p><strong id=\"docs-internal-guid-a5aa540b-7fff-781b-e296-9b6018e7c86f\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXeN1b1aEL7nSlyOJShXQ-5zLaSfJZhNDTJiRNs5uCmNMrm535prFMHR6Er7iuVrgr25GUiltgWB2vtNclc4hKDiBqUDOWzvocZc1lLNzJBR9OMx59Lj1yPq144BDtZFcejvaxQvew?key=uId0YmVkLfO8CALZasynZatR\" width=\"183\" height=\"20\"></strong></p>", "<p><strong id=\"docs-internal-guid-5a0d0f98-7fff-90ac-f400-7a0f683e207d\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcD_5qBJA7vNuG0lqKRVzdj6LJ5ORelyGsG5hjE6JbVXx9cs1Pvpk-jfcDBRlKnOmxiTkPi2SzEM7Q8_CtuDUVSD7eDY_1BSPA3eFXUxGF-q4YiT6Vb5AG2WTsrTHqv2n5zA2wl?key=uId0YmVkLfO8CALZasynZatR\" width=\"189\" height=\"20\"></strong></p>", 
                                "<p><strong id=\"docs-internal-guid-a94db741-7fff-e959-ba6c-2ef36af42c13\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXf2n_MSKJvW0AcPPd6R-VEuSjBRtpiCpopLBwYM9koP6VgEdl4_cJ8yoxjFxgKlw7WjMkB5UsatNPSM7-jq1TpNjC6PcuFsAuyutnQYm4aW7Q1_5r9qrf5fhsAW3SjufLqDnD1hYg?key=uId0YmVkLfO8CALZasynZatR\" width=\"189\" height=\"20\"></strong></p>", "<p><strong id=\"docs-internal-guid-e8b9fc49-7fff-f75f-e8c7-e8bf7a5fa814\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXc0y1EcOhicntZGTGaPyi6OJKiF9MRW7xljC_3oDVoUpWrDM872id9AvDhHu3uaI2Cgt0lf0-N9jj5buS8WH1bshXz5hwSZZRpDup_1VC5izkdjoz-ne7Ce2qJcet-OE8aSaitZ?key=uId0YmVkLfO8CALZasynZatR\" width=\"183\" height=\"20\"></strong></p>"],
                    options_hi: ["<p><strong id=\"docs-internal-guid-1961f389-7fff-a16e-22ba-c3663a716bbd\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXeN1b1aEL7nSlyOJShXQ-5zLaSfJZhNDTJiRNs5uCmNMrm535prFMHR6Er7iuVrgr25GUiltgWB2vtNclc4hKDiBqUDOWzvocZc1lLNzJBR9OMx59Lj1yPq144BDtZFcejvaxQvew?key=uId0YmVkLfO8CALZasynZatR\" width=\"183\" height=\"20\"></strong></p>", "<p><strong id=\"docs-internal-guid-77e84927-7fff-ad2e-d9df-2f05f9d6551a\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcD_5qBJA7vNuG0lqKRVzdj6LJ5ORelyGsG5hjE6JbVXx9cs1Pvpk-jfcDBRlKnOmxiTkPi2SzEM7Q8_CtuDUVSD7eDY_1BSPA3eFXUxGF-q4YiT6Vb5AG2WTsrTHqv2n5zA2wl?key=uId0YmVkLfO8CALZasynZatR\" width=\"189\" height=\"20\"></strong></p>",
                                "<p><strong id=\"docs-internal-guid-611f1fa7-7fff-c5e7-bcf7-756e551dae4c\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXf2n_MSKJvW0AcPPd6R-VEuSjBRtpiCpopLBwYM9koP6VgEdl4_cJ8yoxjFxgKlw7WjMkB5UsatNPSM7-jq1TpNjC6PcuFsAuyutnQYm4aW7Q1_5r9qrf5fhsAW3SjufLqDnD1hYg?key=uId0YmVkLfO8CALZasynZatR\" width=\"189\" height=\"20\"></strong></p>", "<p><strong id=\"docs-internal-guid-ef606f83-7fff-dde9-fb05-06862428da10\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXc0y1EcOhicntZGTGaPyi6OJKiF9MRW7xljC_3oDVoUpWrDM872id9AvDhHu3uaI2Cgt0lf0-N9jj5buS8WH1bshXz5hwSZZRpDup_1VC5izkdjoz-ne7Ce2qJcet-OE8aSaitZ?key=uId0YmVkLfO8CALZasynZatR\" width=\"183\" height=\"20\"></strong></p>"],
                    solution_en: "<p>11.(c)<br><strong id=\"docs-internal-guid-be15689a-7fff-a9b7-1a63-3ebccb5f997b\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXf2n_MSKJvW0AcPPd6R-VEuSjBRtpiCpopLBwYM9koP6VgEdl4_cJ8yoxjFxgKlw7WjMkB5UsatNPSM7-jq1TpNjC6PcuFsAuyutnQYm4aW7Q1_5r9qrf5fhsAW3SjufLqDnD1hYg?key=uId0YmVkLfO8CALZasynZatR\" width=\"189\" height=\"20\"></strong></p>",
                    solution_hi: "<p>11.(c)<br><strong id=\"docs-internal-guid-be15689a-7fff-a9b7-1a63-3ebccb5f997b\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXf2n_MSKJvW0AcPPd6R-VEuSjBRtpiCpopLBwYM9koP6VgEdl4_cJ8yoxjFxgKlw7WjMkB5UsatNPSM7-jq1TpNjC6PcuFsAuyutnQYm4aW7Q1_5r9qrf5fhsAW3SjufLqDnD1hYg?key=uId0YmVkLfO8CALZasynZatR\" width=\"189\" height=\"20\"></strong></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "6",
                    question_en: "<p>12.<span style=\"font-family: Times New Roman;\"> In the following Venn diagram, the hexagon stands for &lsquo;police officers&rsquo;, </span><span style=\"font-family: Times New Roman;\">the</span><span style=\"font-family: Times New Roman;\"> pentagon stands for &lsquo;graduates&rsquo; , the circle stands for &lsquo;females&rsquo; , and the square stands for &lsquo;Indians&rsquo;. The given numbers represent the number of persons in that particular category.</span><br><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1665461326/word/media/image40.png\" width=\"150\" height=\"124\"><br><span style=\"font-family: Times New Roman;\">How many </span><span style=\"font-family: Times New Roman;\">Indian</span><span style=\"font-family: Times New Roman;\"> police officers are graduates but NOT females?</span></p>",
                    question_hi: "<p><span style=\"font-family: Roboto;\">12.</span><span style=\"font-family: Palanquin;\"> निम्नलिखित वेन आरेख में, षट्भुज \'पुलिस अधिकारियों\' के लिए है, पंचकोण \'स्नातक\' के लिए है, वृत्त \'महिलाओं\' के लिए है, और वर्ग \'भारतीयों\' के लिए है। दी गई संख्याएं उस विशेष श्रेणी में व्यक्तियों की संख्या दर्शाती हैं।</span><br><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1665461326/word/media/image40.png\" width=\"159\" height=\"132\"><br><span style=\"font-family: Palanquin;\">कितने भारतीय पुलिस अधिकारी स्नातक हैं, लेकिन महिला नहीं हैं?</span></p>",
                    options_en: ["<p>11</p>", "<p>8</p>", 
                                "<p>13</p>", "<p>19</p>"],
                    options_hi: ["<p>11</p>", "<p>8</p>",
                                "<p>13</p>", "<p>19</p>"],
                    solution_en: "<p>12.(a) <span style=\"font-family: Times New Roman;\">Clearly, the common intersection point of pentagon, hexagon and square (excluding circle) is 11.</span><br><span style=\"font-family: Times New Roman;\">So, the number of </span><span style=\"font-family: Times New Roman;\">Indian</span><span style=\"font-family: Times New Roman;\"> police officers who are graduates but NOT females = 11.</span></p>",
                    solution_hi: "<p>12.(a) <span style=\"font-family: Palanquin;\">स्पष्ट रूप से, पंचभुज, षट्भुज और वर्ग (वृत्त को छोड़कर) का उभयनिष्ठ प्रतिच्छेदन बिंदु 11 है।</span><br><span style=\"font-family: Palanquin;\">इसलिए, भारतीय पुलिस अधिकारियों की संख्या जो स्नातक हैं लेकिन महिलाएं नहीं हैं = 11.</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "6",
                    question_en: "<p>13.<span style=\"font-family: Times New Roman;\"> In a certain code language, </span><span style=\"font-family: Times New Roman;\">MONEY</span><span style=\"font-family: Times New Roman;\"> is written as </span><span style=\"font-family: Times New Roman;\">PRQHB.</span><span style=\"font-family: Times New Roman;\"> In the same code language, </span><span style=\"font-family: Times New Roman;\">CREDIT</span><span style=\"font-family: Times New Roman;\"> will be written as:</span></p>",
                    question_hi: "<p><span style=\"font-family: Roboto;\">13.</span><span style=\"font-family: Palanquin;\"> एक निश्चित कूट भाषा में </span><span style=\"font-family: Roboto;\">MONEY</span><span style=\"font-family: Palanquin;\"> को </span><span style=\"font-family: Roboto;\">PRQHB</span><span style=\"font-family: Palanquin;\"> लिखा जाता है। उसी कोड भाषा में, </span><span style=\"font-family: Roboto;\">CREDIT </span><span style=\"font-family: Palanquin;\">को किस प्रकार लिखा जाएगा:</span></p>",
                    options_en: ["<p>FUGHLW</p>", "<p>FUHGLW</p>", 
                                "<p>FUHGWL</p>", "<p>FHGULW</p>"],
                    options_hi: ["<p>FUGHLW</p>", "<p>FUHGLW</p>",
                                "<p>FUHGWL</p>", "<p>FHGULW</p>"],
                    solution_en: "<p>13.(b)<br><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1665461326/word/media/image18.png\"><br><span style=\"font-family: Times New Roman;\">Similarly,</span><br><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1665461326/word/media/image23.png\"></p>",
                    solution_hi: "<p>13.(b)<br><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1665461326/word/media/image18.png\"><br><span style=\"font-family: Palanquin;\">इसी तरह,</span><br><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1665461326/word/media/image23.png\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "6",
                    question_en: "<p>14. <span style=\"font-family: Times New Roman;\">In a certain code language, </span><span style=\"font-family: Times New Roman;\">\'<strong>her father is okay</strong>\'</span><span style=\"font-family: Times New Roman;\"> is coded as </span><strong><span style=\"font-family: Times New Roman;\">6583</span></strong><span style=\"font-family: Times New Roman;\">, </span><span style=\"font-family: Times New Roman;\">\'<strong>my father is well</strong>\'</span><span style=\"font-family: Times New Roman;\"> is coded as </span><strong><span style=\"font-family: Times New Roman;\">5137</span></strong><span style=\"font-family: Times New Roman;\"> and </span><span style=\"font-family: Times New Roman;\">\'<strong>her arm is injured\'</strong></span><span style=\"font-family: Times New Roman;\"> is coded as </span><strong><span style=\"font-family: Times New Roman;\">2839</span></strong><span style=\"font-family: Times New Roman;\">. How will &lsquo;</span><strong><span style=\"font-family: Times New Roman;\">okay</span></strong><span style=\"font-family: Times New Roman;\">&rsquo; be coded in that language?</span></p>",
                    question_hi: "<p><span style=\"font-family: Roboto;\">14. </span><span style=\"font-family: Palanquin;\">एक निश्चित कोड भाषा में, </span><strong><span style=\"font-family: Roboto;\">\'her father is </span><span style=\"font-family: Roboto;\"> </span><span style=\"font-family: Roboto;\">okay\'</span></strong><span style=\"font-family: Palanquin;\"> को </span><strong><span style=\"font-family: Roboto;\">6583</span></strong><span style=\"font-family: Palanquin;\"><strong> </strong>के रूप में कोडित किया गया है, </span><span style=\"font-family: Roboto;\">\'<strong>my father is well\'</strong></span><span style=\"font-family: Palanquin;\"> को </span><strong><span style=\"font-family: Roboto;\">5137</span></strong><span style=\"font-family: Palanquin;\"> के रूप में कोडित किया गया है और </span><strong><span style=\"font-family: Roboto;\">\'her arm is injured</span></strong><span style=\"font-family: Palanquin;\"><strong>\'</strong> को </span><strong><span style=\"font-family: Roboto;\">2839</span></strong><span style=\"font-family: Palanquin;\"> के रूप में कोड किया गया है। उस </span><span style=\"font-family: Palanquin;\">भाषा में </span><strong><span style=\"font-family: Roboto;\">&lsquo;okay&rsquo;</span></strong><span style=\"font-family: Palanquin;\"> को कैसे कोड किया जाएगा?</span></p>",
                    options_en: ["<p>6</p>", "<p>8</p>", 
                                "<p>3</p>", "<p>5</p>"],
                    options_hi: ["<p>6</p>", "<p>8</p>",
                                "<p>3</p>", "<p>5</p>"],
                    solution_en: "<p>14.(a) <span style=\"font-family: Times New Roman;\">From </span><span style=\"font-family: Times New Roman;\">\'<strong>my father is well</strong>\' </span><span style=\"font-family: Times New Roman;\">and </span><span style=\"font-family: Times New Roman;\">\'<strong>her arm is injured</strong>\'</span><span style=\"font-family: Times New Roman;\">, we get the common code </span><strong><span style=\"font-family: Times New Roman;\">&ldquo;is&rdquo;</span></strong><span style=\"font-family: Times New Roman;\"> =</span><span style=\"font-family: Times New Roman;\"> <strong>3</strong></span><br><span style=\"font-family: Times New Roman;\">From </span><span style=\"font-family: Times New Roman;\">\'<strong>her father is okay</strong>\' </span><span style=\"font-family: Times New Roman;\">and </span><span style=\"font-family: Times New Roman;\">\'<strong>my father is well</strong>\',</span><span style=\"font-family: Times New Roman;\"> we get the common code </span><span style=\"font-family: Times New Roman;\">&ldquo;<strong>father</strong>&rdquo;</span><span style=\"font-family: Times New Roman;\"> = </span><strong><span style=\"font-family: Times New Roman;\">5</span></strong><br><span style=\"font-family: Times New Roman;\">From </span><span style=\"font-family: Times New Roman;\">\'<strong>her father is okay</strong>\' </span><span style=\"font-family: Times New Roman;\">and </span><span style=\"font-family: Times New Roman;\">\'<strong>her arm is injured</strong>\'</span><span style=\"font-family: Times New Roman;\">, we get the common code </span><span style=\"font-family: Times New Roman;\">&ldquo;<strong>her</strong>&rdquo;</span><span style=\"font-family: Times New Roman;\"> = </span><strong><span style=\"font-family: Times New Roman;\">8</span></strong><br><span style=\"font-family: Times New Roman;\">So, on comparing obtained codes with</span><span style=\"font-family: Times New Roman;\"> <strong>\'her father is okay\'</strong></span><span style=\"font-family: Times New Roman;\">, we get </span><strong><span style=\"font-family: Times New Roman;\">&lsquo;okay\'</span></strong><span style=\"font-family: Times New Roman;\"> =</span><span style=\"font-family: Times New Roman;\"> <strong>6</strong></span><span style=\"font-family: Times New Roman;\"> </span></p>",
                    solution_hi: "<p>14.(a) <span style=\"font-family: Roboto;\">\'<strong>my father is well\'</strong> </span><span style=\"font-family: Palanquin;\"> और </span><span style=\"font-family: Roboto;\"><strong>\'her arm is injured</strong>\'</span><span style=\"font-family: Palanquin;\"> से, हमें सामान्य कोड मिलता है </span><span style=\"font-family: Roboto;\"><strong>\"is\"</strong> </span><span style=\"font-family: Roboto;\">= 3</span><br><strong><span style=\"font-family: Roboto;\">\'her father is okay\'</span></strong><span style=\"font-family: Palanquin;\"> और </span><span style=\"font-family: Roboto;\">\'<strong>my father is well</strong>\'</span><span style=\"font-family: Palanquin;\"> हमें सामान्य कोड मिलता है </span><strong><span style=\"font-family: Roboto;\">&ldquo;father&rdquo;</span></strong><span style=\"font-family: Roboto;\"> = <strong>5</strong></span><br><strong><span style=\"font-family: Roboto;\">\'her father is okay\'</span></strong><span style=\"font-family: Palanquin;\"><strong> </strong>और </span><strong><span style=\"font-family: Roboto;\">\'her arm is injured\'</span></strong><span style=\"font-family: Palanquin;\"> से, हमें सामान्य कोड मिलता है </span><strong><span style=\"font-family: Roboto;\">&ldquo;her&rdquo;</span></strong><span style=\"font-family: Roboto;\"> = <strong>8</strong></span><br><span style=\"font-family: Palanquin;\">अतः प्राप्त कूटों की </span><strong><span style=\"font-family: Roboto;\">\'her father is okay\'</span></strong><span style=\"font-family: Palanquin;\"> से तुलना करने पर, हमें प्राप्त होता है </span><span style=\"font-family: Roboto;\"><strong>&lsquo;okay\'</strong>=<strong> 6</strong></span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "6",
                    question_en: "<p>15. <span style=\"font-family: Times New Roman;\">The sequence of folding a piece of paper (figure i) and the manner in which the folded paper has been cut (figure ii) is shown in the following figures, Select the option that would most closely resemble the unfolded form of (figure ii).</span></p>\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1665461326/word/media/image28.png\" width=\"172\" height=\"103\"></p>",
                    question_hi: "<p><span style=\"font-family: Roboto;\">15.</span><span style=\"font-family: Palanquin;\"> कागज के एक टुकड़े को मोड़ने का क्रम (आकृति i) और जिस तरीके से मुड़े हुए कागज को काटा गया है (आकृति ii) निम्नलिखित आकृतियों में दिखाया गया है। उस विकल्प का चयन कीजिये जो (आकृति ii) के खुले रूप के सबसे निकट से मिलता जुलता हो।</span></p>\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1665461326/word/media/image28.png\" width=\"152\" height=\"91\"></p>",
                    options_en: ["<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1665461326/word/media/image36.png\" width=\"83\" height=\"79\"><span style=\"font-family: Times New Roman;\"> </span></p>", "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1665461326/word/media/image20.png\" width=\"83\" height=\"79\"></p>", 
                                "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1665461326/word/media/image21.png\" width=\"83\" height=\"79\"><span style=\"font-family: Times New Roman;\"> </span></p>", "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1665461326/word/media/image21.png\" width=\"83\" height=\"79\"></p>\n<p><span style=\"font-family: Roboto;\"> </span></p>"],
                    options_hi: ["<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1665461326/word/media/image36.png\" width=\"83\" height=\"79\"><span style=\"font-family: Times New Roman;\"> </span></p>", "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1665461326/word/media/image20.png\" width=\"83\" height=\"79\"></p>",
                                "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1665461326/word/media/image21.png\" width=\"83\" height=\"79\"><span style=\"font-family: Times New Roman;\"> </span></p>", "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1665461326/word/media/image21.png\" width=\"83\" height=\"79\"></p>"],
                    solution_en: "<p>15.(b)<br><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1665461326/word/media/image20.png\" width=\"83\" height=\"79\"></p>",
                    solution_hi: "<p>15.(b)<br><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1665461326/word/media/image20.png\" width=\"83\" height=\"79\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "6",
                    question_en: "<p>16.<span style=\"font-family: Times New Roman;\"> Select the correct option that indicates the arrangement of the given words in a logical and meaningful order.</span><br><span style=\"font-family: Times New Roman;\">1. Starfish&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;</span><span style=\"font-family: Times New Roman;\">2. Blue whale&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;</span><span style=\"font-family: Times New Roman;\">3. Shark</span><br><span style=\"font-family: Times New Roman;\">4. Giant tortoise&nbsp; &nbsp; &nbsp;</span><span style=\"font-family: Times New Roman;\">5. Penguin</span></p>",
                    question_hi: "<p><span style=\"font-family: Roboto;\">16.</span><span style=\"font-family: Palanquin;\"> उस सही विकल्प का चयन कीजिये जो दिए गए शब्दों को तार्किक और अर्थपूर्ण क्रम में व्यवस्थित करता है।</span><br><span style=\"font-family: Roboto;\">1. Starfish&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;</span><span style=\"font-family: Roboto;\">2. Blue whale</span><br><span style=\"font-family: Roboto;\">3. Shark&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; </span><span style=\"font-family: Roboto;\">4. Giant tortoise</span><br><span style=\"font-family: Roboto;\">5. Penguin&nbsp;</span></p>",
                    options_en: ["<p>1, 5, 4, 2, 3</p>", "<p>1, 5, 3, 2, 4</p>", 
                                "<p>1, 5, 4, 3, 2</p>", "<p>4, 5, 1, 3, 2</p>"],
                    options_hi: ["<p>1, 5, 4, 2, 3</p>", "<p>1, 5, 3, 2, 4</p>",
                                "<p>1, 5, 4, 3, 2</p>", "<p>4, 5, 1, 3, 2</p>"],
                    solution_en: "<p>16.(c) <span style=\"font-family: Times New Roman;\">Marine animals are arranged in increasing order of their size.</span><br><span style=\"font-family: Times New Roman;\">Correct order will be :</span><br><span style=\"font-family: Times New Roman;\">Starfish(1) &lt;</span><span style=\"font-family: Times New Roman;\"> Penguin(5) &lt;</span><span style=\"font-family: Times New Roman;\"> Giant tortoise(4) &lt;&nbsp;</span><span style=\"font-family: Times New Roman;\"> Shark(3) &lt;&nbsp;</span><span style=\"font-family: Times New Roman;\"> Blue whale(2)</span></p>",
                    solution_hi: "<p>16.(c) <span style=\"font-family: Palanquin;\">समुद्री जानवरों को उनके आकार के बढ़ते क्रम में व्यवस्थित किया जाता है।</span><br><span style=\"font-family: Palanquin;\">सही क्रम होगा:</span><br><span style=\"font-family: Roboto;\">Starfish(1) <span style=\"font-family: Times New Roman;\">&lt;</span></span><span style=\"font-family: Roboto;\"> Penguin(5) <span style=\"font-family: Times New Roman;\">&lt;</span>&nbsp;</span><span style=\"font-family: Roboto;\">Giant tortoise(4) <span style=\"font-family: Times New Roman;\">&lt;</span> </span><span style=\"font-family: Roboto;\">Shark </span><span style=\"font-family: Roboto;\"> Blue(3) <span style=\"font-family: Times New Roman;\">&lt;</span> whale(2)</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "6",
                    question_en: "<p>17. <span style=\"font-family: Times New Roman;\">Five </span><span style=\"font-family: Times New Roman;\">persons</span><span style=\"font-family: Times New Roman;\"> are sitting in a row facing the north. One of the two persons at the extreme ends is an introvert and the other one is an extrovert. A thin person is sitting to the immediate right of an intelligent person. A weak person is sitting to the immediate left of the extrovert person. The intelligent person is sitting exactly between </span><span style=\"font-family: Times New Roman;\">the introvert</span><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Times New Roman;\">and the thin</span><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Times New Roman;\">persons</span><span style=\"font-family: Times New Roman;\">. Which of the following persons is sitting at the centre?</span></p>",
                    question_hi: "<p><span style=\"font-family: Roboto;\">17. </span><span style=\"font-family: Palanquin;\">पांच व्यक्ति एक पंक्ति में उत्तर की ओर मुख करके बैठे हैं। अंतिम छोर पर बैठे दो व्यक्तियों में से एक अंतर्मुखी (introvert) है और दूसरा बहिर्मुखी (extrovert) है। एक पतला व्यक्ति(thin), एक बुद्धिमान व्यक्ति (wise)के ठीक दायें बैठा है। एक कमजोर व्यक्ति(weak), बहिर्मुखी व्यक्ति के ठीक बायें बैठा है। बुद्धिमान व्यक्ति ,अंतर्मुखी और पतले व्यक्तियों के ठीक बीच में बैठा है। निम्नलिखित में से कौन सा व्यक्ति केंद्र में बैठा है?</span></p>",
                    options_en: ["<p>Extrovert</p>", "<p>Intelligent</p>", 
                                "<p>Weak</p>", "<p>Thin</p>"],
                    options_hi: ["<p>बहिर्मुखी</p>", "<p>बुद्धिमान</p>",
                                "<p>कमजोर</p>", "<p>पतला</p>"],
                    solution_en: "<p>17.(d) <span style=\"font-family: Times New Roman;\">After arranging persons as per directions given in question, we get following arrangement :</span><br><img src=\"data:image/png;base64,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\"><br><span style=\"font-family: Times New Roman;\">Clearly, we can see that Thin Person is sitting exactly at the </span><span style=\"font-family: Times New Roman;\">centre</span><span style=\"font-family: Times New Roman;\">.</span></p>",
                    solution_hi: "<p>17.(d) <span style=\"font-family: Palanquin;\">प्रश्न में दिए गए निर्देशों के अनुसार व्यक्तियों को व्यवस्थित करने के बाद, हमें निम्नलिखित व्यवस्था मिलती है:</span><br><img src=\"data:image/png;base64,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\"><br><span style=\"font-family: Palanquin;\">स्पष्ट रूप से, हम देख सकते हैं कि पतला (Thin) व्यक्ति बिल्कुल केंद्र में बैठा है।</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "6",
                    question_en: "<p>18. <span style=\"font-family: Times New Roman;\">Four letter clusters have been given, out of which three are alike in some manner and one is different. Select the one that is different.</span></p>",
                    question_hi: "<p><span style=\"font-family: Roboto;\">18. </span><span style=\"font-family: Palanquin;\">चार अक्षर-समूह दिए गए हैं, जिनमें से तीन किसी न किसी रूप में एक जैसे हैं और एक अलग है। जो अलग है उसे चुनें।</span></p>",
                    options_en: ["<p>ZCGLS</p>", "<p>NQUZF</p>", 
                                "<p>LOSXD</p>", "<p>FIMRX</p>"],
                    options_hi: ["<p>ZCGLS</p>", "<p>NQUZF</p>",
                                "<p>LOSXD</p>", "<p>FIMRX</p>"],
                    solution_en: "<p>18.(a)&nbsp;<br>In ZCGLS, Z + 3 = C, C = 4 = G, G + 5 = L, L + 7 = S<br>In NQUZF, N + 3 = Q, Q + 4 = U, U + 5 = Z, Z + 6 = F<br>In LOSXD, L + 3 = O, O + 4 = S, S + 5 = X, X + 6 = D<br>In FIMRX, F + 3 = I, I + 4 = M, M + 5 = R, R + 6 = X<br><strong id=\"docs-internal-guid-62c55097-7fff-4199-675d-d98b7ed90855\"></strong><span style=\"font-family: Times New Roman;\">Clearly, we can see that ZCGLS is an odd one.</span></p>",
                    solution_hi: "<p>18.(a)<br>ZCGLS में, Z + 3 = C, C = 4 = G, G + 5 = L, L + 7 = S<br>NQUZF में, N + 3 = Q, Q + 4 = U, U + 5 = Z, Z + 6 = F<br>LOSXD में, L + 3 = O, O + 4 = S, S + 5 = X, X + 6 = D<br>FIMRX में, F + 3 = I, I + 4 = M, M + 5 = R, R + 6 = X<br><strong id=\"docs-internal-guid-dc2cc615-7fff-a7d3-29cb-f12b40187e1e\"></strong><span style=\"font-family: Palanquin;\">स्पष्ट रूप से, हम देख सकते हैं कि ZCGLS एक विषम है।</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "6",
                    question_en: "<p>19. <span style=\"font-family: Times New Roman;\">Select the number from among the given options that can replace the question mark (?) in the following series.</span><br><span style=\"font-family: Times New Roman;\">10, 14, 31, 35, 73, 77, ?</span></p>",
                    question_hi: "<p><span style=\"font-family: Roboto;\">19.</span><span style=\"font-family: Palanquin;\"> दिए गए विकल्पों में से वह संख्या चुनिए जो निम्नलिखित श्रृंखला में प्रश्नवाचक चिन्ह (?) को प्रतिस्थापित कर सके।</span><br><span style=\"font-family: Roboto;\">10, 14, 31, 35, 73, 77, ?</span></p>",
                    options_en: ["<p>154</p>", "<p>90</p>", 
                                "<p>157</p>", "<p>81</p>"],
                    options_hi: ["<p>154</p>", "<p>90</p>",
                                "<p>157</p>", "<p>81</p>"],
                    solution_en: "<p>19.(c)<br><strong id=\"docs-internal-guid-63f24476-7fff-7e2a-8bc4-dadc02928e3e\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfdac_eDHs6Z1f-gsNMK6zGIkNHf9Gqthch7KszR_qtZpcLMty8I8B4IfawQBcTix4j7OUv6OMfUmX0OsFmwXqzq9MGgFwO8u6cRJJH3PEFl2j_VI1WElyLta1_TEBfFZRrFi2xU6RElZoonF6L9HOED6Pj?key=exyHa0_HzLGjK84hS5Ro28BJ\" width=\"200\" height=\"171\"></strong></p>",
                    solution_hi: "<p>19.(c)<br><strong id=\"docs-internal-guid-63f24476-7fff-7e2a-8bc4-dadc02928e3e\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfdac_eDHs6Z1f-gsNMK6zGIkNHf9Gqthch7KszR_qtZpcLMty8I8B4IfawQBcTix4j7OUv6OMfUmX0OsFmwXqzq9MGgFwO8u6cRJJH3PEFl2j_VI1WElyLta1_TEBfFZRrFi2xU6RElZoonF6L9HOED6Pj?key=exyHa0_HzLGjK84hS5Ro28BJ\" width=\"200\" height=\"171\"></strong></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "6",
                    question_en: "<p><span style=\"font-family: Times New Roman;\">20.</span><span style=\"font-family: Times New Roman;\"> Which two digits should be interchanged to make the given equation correct?</span><br><span style=\"font-family: Gungsuh;\">384 &divide; 16 &minus; 72 + 9 &times; 10 = 2</span></p>",
                    question_hi: "<p><span style=\"font-family: Roboto;\">20.</span><span style=\"font-family: Palanquin;\"> दिए गए समीकरण को सही बनाने के लिए किन दो अंकों को आपस में बदला जाना चाहिए?</span><br><span style=\"font-family: Nova Mono;\">384 &divide; 16 &minus; 72 + 9 &times; 10 = 2</span></p>",
                    options_en: ["<p>3 and 7</p>", "<p>7 and 9</p>", 
                                "<p>4 and 8</p>", "<p>6 and 9</p>"],
                    options_hi: ["<p>3 और 7</p>", "<p>7 और 9</p>",
                                "<p>4 और 8</p>", "<p>6 और 9</p>"],
                    solution_en: "<p>20.(b) <span style=\"font-family: Times New Roman;\">In this type of question, we will check through option one by one, and on doing so option (b) gets satisfied.</span><br><span style=\"font-family: Times New Roman;\">384 &divide;</span><span style=\"font-family: Gungsuh;\">16 &minus; 72 + 9 &times;</span><span style=\"font-family: Times New Roman;\">10 = 2</span><br><span style=\"font-family: Times New Roman;\">Interchanging 7 and 9 in above equation, we get</span><br><span style=\"font-family: Times New Roman;\">384&divide;&nbsp;</span><span style=\"font-family: Gungsuh;\"> 16 &minus; 92 + 7 &times;</span><span style=\"font-family: Times New Roman;\">10</span><br><span style=\"font-family: Times New Roman;\">= 24 &ndash; 92 + 70</span><br><span style=\"font-family: Times New Roman;\">= 94 &ndash; 92</span><br><span style=\"font-family: Times New Roman;\">= 2 = RHS</span></p>",
                    solution_hi: "<p>20.(b) <span style=\"font-family: Palanquin;\">इस प्रकार के प्रश्न में हम एक-एक करके विकल्प की जांच करेंगे, और ऐसा करने पर विकल्प (b) संतुष्ट हो जाता है।</span><br><span style=\"font-family: Roboto;\"><span style=\"font-family: Nova Mono;\">384 &divide; 16 &minus; 72 + 9 &times; 10 </span>&nbsp;= 2</span><br><span style=\"font-family: Palanquin;\">उपरोक्त समीकरण में 7 और 9 को आपस में बदलने पर, हम प्राप्त करते हैं</span><br><span style=\"font-family: Roboto;\">384 &divide;</span><span style=\"font-family: Nova Mono;\">16 &minus; 92 + 7 &times;</span><span style=\"font-family: Roboto;\">10</span><br><span style=\"font-family: Roboto;\">= 24 &ndash; 92 + 7&times;10</span><br><span style=\"font-family: Roboto;\">= 94 &ndash; 92</span><br><span style=\"font-family: Roboto;\">= 2 = RHS</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "6",
                    question_en: "<p>21. <span style=\"font-family: Times New Roman;\">Select the option that is related to the third term in the same way as the second term is related to the first term.</span><br><span style=\"font-family: Times New Roman;\">PATELS : BQFUTM :: NECTAR : ?</span></p>",
                    question_hi: "<p><span style=\"font-family: Roboto;\">21. </span><span style=\"font-family: Palanquin;\">उस विकल्प का चयन कीजिये जो तीसरे पद से उसी प्रकार संबंधित है जैसे दूसरा पद पहले पद से संबंधित है।</span><br><span style=\"font-family: Roboto;\">PATELS : BQFUTM :: NECTAR : ?</span></p>",
                    options_en: ["<p>FODUSZ</p>", "<p>FOUDSB</p>", 
                                "<p>FOVDBS</p>", "<p>OEUDQB</p>"],
                    options_hi: ["<p>FODUSZ</p>", "<p>FOUDSB</p>",
                                "<p>FOVDBS</p>", "<p>OEUDQB</p>"],
                    solution_en: "<p>21.(b)<br><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1665461326/word/media/image17.png\"><br><span style=\"font-family: Times New Roman;\">Similarly,</span><br><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1665461326/word/media/image30.png\"></p>",
                    solution_hi: "<p>21.(b)<br><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1665461326/word/media/image17.png\"><br><span style=\"font-family: Palanquin;\">इसी तरह,</span><br><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1665461326/word/media/image30.png\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "6",
                    question_en: "<p>22.<span style=\"font-family: Times New Roman;\"> Select the number from among the given options that can replace the question mark (?) in the following series.</span><br><span style=\"font-family: Times New Roman;\">16, 33, 100, 401, ?</span></p>",
                    question_hi: "<p><span style=\"font-family: Roboto;\">22.</span><span style=\"font-family: Palanquin;\"> दिए गए विकल्पों में से वह संख्या चुनिए जो निम्नलिखित श्रृंखला में प्रश्नवाचक चिन्ह (?) को प्रतिस्थापित कर सके।</span><br><span style=\"font-family: Roboto;\">16, 33, 100, 401, ?</span></p>",
                    options_en: ["<p>1588</p>", "<p>804</p>", 
                                "<p>1235</p>", "<p>2006</p>"],
                    options_hi: ["<p>1588</p>", "<p>804</p>",
                                "<p>1235</p>", "<p>2006</p>"],
                    solution_en: "<p>22.(d)<br><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1665461326/word/media/image14.png\"></p>",
                    solution_hi: "<p>22.(d)<br><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1665461326/word/media/image14.png\"></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "23",
                    section: "6",
                    question_en: "<p>23. <span style=\"font-family: Times New Roman;\">Which two numbers and which two signs should be interchanged to balance the following equation?</span><br><span style=\"font-family: Times New Roman;\">24 &divide; 16 &ndash; 96 + 48 &times; 12 = 195</span></p>",
                    question_hi: "<p><span style=\"font-family: Roboto;\">23. </span><span style=\"font-family: Palanquin;\">निम्नलिखित समीकरण को संतुलित करने के लिए किन दो संख्याओं और किन दो चिह्नों को आपस में बदला जाना चाहिए?</span><br><span style=\"font-family: Roboto;\">24 &divide; 16 &ndash; 96 + 48 &times; 12 = 195</span></p>",
                    options_en: ["<p>12 and 16, &times; and &divide;</p>", "<p>24 and 16, &ndash; and &divide;</p>", 
                                "<p>24 and 48, &times; and &divide;</p>", "<p>12 and 48, &times; and +</p>"],
                    options_hi: ["<p>12 और 16, &times; और &divide;</p>", "<p>24 और 16, &ndash; और &divide;</p>",
                                "<p>24 और 48, &times; और &divide;</p>", "<p>12 और 48, &times; और +</p>"],
                    solution_en: "<p>23.(a) <span style=\"font-family: Times New Roman;\">In this type of question, we will check through option one by one, and on doing so option (a) gets satisfied.</span><br><span style=\"font-family: Times New Roman;\">24 &divide; 16 &ndash; 96 + 48 &times; 12 &nbsp;= 195</span><br><span style=\"font-family: Times New Roman;\">Interchanging 12 and 16, </span><span style=\"font-family: Times New Roman;\"> and </span><span style=\"font-family: Times New Roman;\">&nbsp;in above equation, we get</span><br><span style=\"font-family: Roboto;\">24 &times;</span><span style=\"font-family: Roboto;\">12 &ndash; 96 + 48 &divide;</span><span style=\"font-family: Roboto;\">16</span><br><span style=\"font-family: Roboto;\">= 24 &times;</span><span style=\"font-family: Roboto;\">12 &ndash; 96 + 3</span><br><span style=\"font-family: Roboto;\">= 288 &ndash; 96 + 3</span><br><span style=\"font-family: Roboto;\">= 291 &ndash; 96</span><br><span style=\"font-family: Times New Roman;\">= 195 = RHS</span></p>",
                    solution_hi: "<p>23.(a) <span style=\"font-family: Palanquin;\">इस प्रकार के प्रश्न में हम एक-एक करके विकल्प की जांच करेंगे और ऐसा करने पर विकल्प (a) संतुष्ट हो जाता है।</span><br><span style=\"font-family: Roboto;\">24 &divide; 16 &ndash; 96 + 48 &times; 12 = 195</span><br><span style=\"font-family: Palanquin;\">12 और 16, </span><span style=\"font-family: Palanquin;\"> और </span><span style=\"font-family: Palanquin;\">&nbsp;को आपस में बदलने पर, उपरोक्त समीकरण से हम प्राप्त करते हैं</span><br><span style=\"font-family: Roboto;\">24 &times;</span><span style=\"font-family: Roboto;\">12 &ndash; 96 + 48 &divide;</span><span style=\"font-family: Roboto;\">16</span><br><span style=\"font-family: Roboto;\">= 24 &times;</span><span style=\"font-family: Roboto;\">12 &ndash; 96 + 3</span><br><span style=\"font-family: Roboto;\">= 288 &ndash; 96 + 3</span><br><span style=\"font-family: Roboto;\">= 291 &ndash; 96</span><br><span style=\"font-family: Roboto;\">= 195 = RHS</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "24",
                    section: "6",
                    question_en: "<p>24.<span style=\"font-family: Times New Roman;\"> A side of a square-shaped park is 12 m. If a squared-shaped garden with a side of 24 m is developed around the park, what will be the total area of the park including the garden?</span></p>",
                    question_hi: "<p><span style=\"font-family: Roboto;\">24. </span><span style=\"font-family: Palanquin;\">एक वर्गाकार पार्क की एक भुजा 12 m यदि पार्क के चारों ओर 24 m भुजा वाला वर्गाकार उद्यान विकसित किया जाए, तो उद्यान सहित पार्क का कुल क्षेत्रफल कितना होगा?</span></p>",
                    options_en: ["<p>144 m<sup>2</sup></p>", "<p>288 m<sup>2</sup></p>", 
                                "<p>576 m<sup>2</sup></p>", "<p>324 m<sup>2</sup></p>"],
                    options_hi: ["<p>144 m<sup>2</sup></p>", "<p>288 m<sup>2</sup></p>",
                                "<p>576 m<sup>2</sup></p>", "<p>324 m<sup>2</sup></p>"],
                    solution_en: "<p>24.(c)<br><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1665461326/word/media/image9.png\"><br><span style=\"font-family: Times New Roman;\">In this question we have to find the area of the park including the garden ,</span><br><span style=\"font-family: Times New Roman;\">so we will have to find the area of the garden.</span><br><span style=\"font-family: Times New Roman;\">24 &times; 24 = 576</span></p>",
                    solution_hi: "<p>24.(c)<br><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1665461326/word/media/image9.png\"><br><span style=\"font-family: Palanquin;\">इस प्रश्न में हमें बगीचे सहित पार्क का क्षेत्रफल ज्ञात करना है,</span><br><span style=\"font-family: Palanquin;\">इसलिए हमें बगीचे का क्षेत्रफल ज्ञात करना होगा।</span><br><span style=\"font-family: Roboto;\">24 &times; 24 = 576</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "25",
                    section: "misc",
                    question_en: "<p>25.<span style=\"font-family: Times New Roman;\"> Select the combination of letters that when sequentially placed in the blanks of the given series will complete the series.</span></p>\n<p><span style=\"font-family: Times New Roman;\">_ L H _ _ U_ H _ _ U _ H _ N U _ _ E N</span></p>",
                    question_hi: "<p><span style=\"font-family: Roboto;\">25. </span><span style=\"font-family: Palanquin;\">अक्षरों के उस संयोजन का चयन कीजिये जिसे दी गई श्रृंखला के रिक्त स्थानों में क्रमिक रूप से रखने पर श्रृंखला पूरी हो जाएगी।</span></p>\n<p><span style=\"font-family: Roboto;\">_ L H _ _ U_ H _ _ U _ H _ N U _ _ E N</span></p>",
                    options_en: ["<p>U, E, N, L, E, N, L, E, L, H</p>", "<p>E, U, N, L, N, E, L, E, H, L</p>", 
                                "<p>U, E, L, E, N, N, L, H, N, E</p>", "<p>U, E, N, L, E, L, H, L, E, H</p>"],
                    options_hi: ["<p>U, E, N, L, E, N, L, E, L, H</p>", "<p>E, U, N, L, N, E, L, E, H, L</p>",
                                "<p>U, E, L, E, N, N, L, H, N, E</p>", "<p>U, E, N, L, E, L, H, L, E, H</p>"],
                    solution_en: "<p>25.(a)<br><img src=\"data:image/png;base64,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\" width=\"448\" height=\"31\"></p>",
                    solution_hi: "<p>25.(a)<br><img src=\"data:image/png;base64,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\" width=\"505\" height=\"35\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML - more compact
            let html = `
                <div class="card mb-3">
                    <div class="card-body">
                        <div class="question-text mb-3">
                            <div class="en">${question.question_en}</div>
                            <div class="hi" style="display:none">${question.question_hi}</div>
                        </div>
                        <div class="options">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = 'option-label';
                // When reviewing, highlight user's choice and correct answer
                if (submitted) {
                    if (opt === question.correct) {
                        optionClass += ' border-success bg-success bg-opacity-10';
                    }
                    if (answers[question.id] === opt && opt !== question.correct) {
                        optionClass += ' border-danger bg-danger bg-opacity-10';
                    }
                }
                html += `
                    <div class="form-check mb-2">
                        <input class="form-check-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${answers[question.id] === opt ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                        <label class="form-check-label w-100 ${optionClass}" for="opt-${index}-${opt}">
                            ${submitted && opt === question.correct ? '<i class="fas fa-check-circle text-success me-1"></i>' : ''}
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const alertClass = isCorrect ? 'alert-success' : 'alert-danger';
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="alert ${alertClass} mt-3 py-2">
                        <strong>${resultText}</strong>
                        <div class="mt-1 small">
                            <strong>Solution:</strong>
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                document.getElementById(`box-${index}`).classList.add('attempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { if (currentQuestion > 0) showQuestion(currentQuestion - 1); }
        function nextQuestion() { if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
            // Update button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const content = document.getElementById('main-content');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                if (window.innerWidth >= 992) {
                    content.classList.add('with-nav');
                }
            } else {
                nav.classList.remove('show');
                if (window.innerWidth >= 992) {
                    content.classList.remove('with-nav');
                }
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer - compact
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            if (confirm(`You have attempted ${attempted} of ${questions.length} questions.
${remaining} questions are unattempted.
Are you sure you want to submit?`)) {
                submitTest();
            }
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('bg-secondary', 'text-white');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            resultsModal.show();
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.classList.replace('btn-light', 'btn-success');
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            resultsModal.hide();
            showQuestion(0);
        }
        // Add event listener for window resize
        window.addEventListener('resize', function() {
            if (window.innerWidth >= 992) {
                if (document.getElementById('question-nav').classList.contains('show')) {
                    document.getElementById('main-content').classList.add('with-nav');
                }
            } else {
                document.getElementById('main-content').classList.remove('with-nav');
            }
        });
    </script>
</body>
</html>