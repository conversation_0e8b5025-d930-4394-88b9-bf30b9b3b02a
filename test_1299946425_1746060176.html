<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Smart Test Interface</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        .question-nav{position:fixed;top:70px;right:0;height:calc(100vh - 70px);width:320px;transition:all .3s;z-index:100;display:flex;flex-direction:column}
        .question-content{margin-right:0;transition:all .3s}
        .question-box{width:40px;height:40px;margin:5px;cursor:pointer}
        .attempted{background-color:#3498db!important;color:white}
        .correct{background-color:#2ecc71!important;color:white}
        .incorrect{background-color:#e74c3c!important;color:white}
        .current{border:3px solid #f39c12!important}
        #question-boxes{overflow-y:auto;max-height:calc(100vh - 150px);padding-bottom:15px}
        @media (max-width:768px){.question-nav{width:100%;right:-100%}.question-content{margin-right:0}}
    </style>
</head>
<body>
    <div class="container-fluid p-0">
        <!-- Header with controls -->
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary sticky-top">
            <div class="container-fluid">
                <a class="navbar-brand" href="#">
                    <i class="fas fa-graduation-cap me-2"></i>Smart Test
                </a>
                <!-- Section selector -->
                <div class="navbar-nav me-auto section-tabs">
                    <select class="form-select" id="section-selector" onchange="switchSection(this.value)">
                        <option value="all">All Sections</option>
                    </select>
                </div>
                <div class="d-flex align-items-center">
                    <!-- Timer -->
                    <div class="bg-light text-primary rounded px-3 py-2 me-2 fw-bold" id="timer">
                        <i class="far fa-clock me-2"></i><span id="timer-display">100:00</span>
                    </div>
                    <!-- Submit button -->
                    <button class="btn btn-success me-2" id="submit-btn" onclick="submitTest()">
                        <i class="fas fa-check me-1"></i> Submit
                    </button>
                    <!-- Language toggle -->
                    <button class="btn btn-light me-2" id="lang-btn" onclick="toggleLanguage()">
                        <i class="fas fa-language"></i>
                    </button>
                    <!-- Menu toggle for mobile -->
                    <button class="btn btn-light d-lg-none" id="menu-toggle" onclick="toggleMenu()">
                        <i class="fas fa-th"></i>
                    </button>
                </div>
            </div>
        </nav>
        <div class="row m-0">
            <!-- Main content area -->
            <div class="col p-0 question-content" id="question-content">
                <div class="container py-4">
                    <!-- Question navigation -->
                    <div class="d-flex justify-content-between mb-4">
                        <button class="btn btn-outline-primary" onclick="prevQuestion()">
                            <i class="fas fa-chevron-left me-1"></i> Previous
                        </button>
                        <div class="fw-bold" id="question-counter">Question 1 of 100</div>
                        <button class="btn btn-outline-primary" onclick="nextQuestion()">
                            Next <i class="fas fa-chevron-right ms-1"></i>
                        </button>
                    </div>
                    <!-- Questions will be displayed here -->
                    <div id="questions-container"></div>
                </div>
            </div>
            <!-- Question navigation sidebar -->
            <div class="bg-light question-nav" id="question-nav">
                <div class="p-3">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h5 class="m-0">Questions</h5>
                        <div>
                            <span class="badge bg-secondary me-1" id="attempted-count">0</span>
                            <span class="badge bg-secondary" id="total-count">100</span>
                            <button class="btn btn-sm btn-outline-secondary ms-2 d-lg-none" onclick="toggleMenu()">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>
                    <div class="d-flex flex-wrap" id="question-boxes"></div>
                </div>
            </div>
        </div>
        <!-- Results modal -->
        <div class="modal fade" id="results-modal" tabindex="-1" aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content">
                    <div class="modal-header bg-primary text-white">
                        <h5 class="modal-title">Test Results</h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row text-center">
                            <div class="col-6 mb-3">
                                <div class="p-3 bg-primary bg-opacity-10 rounded">
                                    <h3 id="score-value">0</h3>
                                    <div class="small text-muted">Score</div>
                                </div>
                            </div>
                            <div class="col-6 mb-3">
                                <div class="p-3 bg-success bg-opacity-10 rounded">
                                    <h3 id="correct-value">0</h3>
                                    <div class="small text-muted">Correct</div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="p-3 bg-danger bg-opacity-10 rounded">
                                    <h3 id="incorrect-value">0</h3>
                                    <div class="small text-muted">Incorrect</div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="p-3 bg-secondary bg-opacity-10 rounded">
                                    <h3 id="unattempted-value">0</h3>
                                    <div class="small text-muted">Unattempted</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-primary" onclick="reviewTest()">Review Test</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 100 * 60;
        let resultsModal;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            resultsModal = new bootstrap.Modal(document.getElementById('results-modal'));
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 768) {
                document.getElementById('question-content').style.marginRight = '320px';
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["6"] = {
                name: "Reasoning",
                start: 0,
                end: 23
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="6">Reasoning</option>`;
            sections["18"] = {
                name: "General Awareness",
                start: 24,
                end: 48
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="18">General Awareness</option>`;
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 49,
                end: 73
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["7"] = {
                name: "English",
                start: 74,
                end: 98
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="7">English</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 99,
                end: 99
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "6",
                    question_en: "<p>1. Seven friends - L, M, N, O, D, E, and F - are seated around a circular table, facing the centre (but not necessarily in the same order). O is sitting fifth to the right of L. M has F and O as immediate neighbours. N and L are immediate neighbours of E.Who is sitting to the immediate left of D?</p>",
                    question_hi: "<p>1. सात मित्र - L, M, N, O, D, E और F, एक वृत्ताकार मेज के चारों ओर केंद्र की ओर मुख करके बैठे हैं (परंतु जरूरी नहीं कि वे इसी क्रम में बैठे हों)। O, L के दाएँ से पाँचवें स्थान पर बैठा है। F और O दोनों M के निकटतम पड़ोसी हैं। N और L, E के निकटतम पड़ोसी हैं। D के ठीक बाएँ कौन बैठा है?</p>",
                    options_en: [
                        "<p>O</p>",
                        "<p>M</p>",
                        "<p>L</p>",
                        "<p>F</p>"
                    ],
                    options_hi: [
                        "<p>O</p>",
                        "<p>M</p>",
                        "<p>L</p>",
                        "<p>F</p>"
                    ],
                    solution_en: "<p>1.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740982616086.png\" alt=\"rId4\" width=\"148\" height=\"142\"></p>",
                    solution_hi: "<p>1.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740982616086.png\" alt=\"rId4\" width=\"148\" height=\"142\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "6",
                    question_en: "<p>2. Select the option in which the given figure is embedded. (Rotation is NOT allowed).<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740982616220.png\" alt=\"rId5\" width=\"134\" height=\"70\"></p>",
                    question_hi: "<p>2. उस विकल्प का चयन करें, जिसमें दी गई आकृति सन्निहित है। (घूर्णन की अनुमति नहीं है। )<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740982616220.png\" alt=\"rId5\" width=\"134\" height=\"70\"></p>",
                    options_en: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740982616318.png\" alt=\"rId6\" width=\"129\" height=\"62\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740982616433.png\" alt=\"rId7\" width=\"129\" height=\"52\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740982616551.png\" alt=\"rId8\" width=\"130\" height=\"62\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740982616673.png\" alt=\"rId9\" width=\"131\" height=\"57\"></p>"
                    ],
                    options_hi: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740982616318.png\" alt=\"rId6\" width=\"131\" height=\"63\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740982616433.png\" alt=\"rId7\" width=\"129\" height=\"52\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740982616551.png\" alt=\"rId8\" width=\"130\" height=\"62\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740982616673.png\" alt=\"rId9\" width=\"131\" height=\"57\"></p>"
                    ],
                    solution_en: "<p>2.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740982616793.png\" alt=\"rId10\" width=\"152\" height=\"56\"></p>",
                    solution_hi: "<p>2.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740982616793.png\" alt=\"rId10\" width=\"152\" height=\"56\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "6",
                    question_en: "<p>3. PCNZ is related to ALWQ in a certain way based on the English alphabetical order. In the same way, RYLD is related to EHUS. To which of the following is HAXM related, following the same logic?</p>",
                    question_hi: "<p>3. अंग्रेजी वर्णमाला क्रम के आधार पर PCNZ, ALWQ से एक निश्चित प्रकार से संबंधित है। उसी प्रकार, RYLD, EHUS से संबंधित है। समान तर्क का अनुसरण करते हुए HAXM निम्नलिखित में से किससे संबंधित है?</p>",
                    options_en: [
                        "<p>NJGI</p>",
                        "<p>NJIG</p>",
                        "<p>NIGJ</p>",
                        "<p>NIJG</p>"
                    ],
                    options_hi: [
                        "<p>NJGI</p>",
                        "<p>NJIG</p>",
                        "<p>NIGJ</p>",
                        "<p>NIJG</p>"
                    ],
                    solution_en: "<p>3.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740982616936.png\" alt=\"rId11\" width=\"122\" height=\"82\">,&nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740982617100.png\" alt=\"rId12\" width=\"124\" height=\"83\"><br>Similarly,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740982617296.png\" alt=\"rId13\" width=\"121\" height=\"81\"></p>",
                    solution_hi: "<p>3.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740982616936.png\" alt=\"rId11\" width=\"122\" height=\"82\">,&nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740982617100.png\" alt=\"rId12\" width=\"124\" height=\"83\"><br>इसी प्रकार,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740982617296.png\" alt=\"rId13\" width=\"121\" height=\"81\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "6",
                    question_en: "<p>4. If &lsquo;E&rsquo; stands for &lsquo;&divide;&rsquo;, &lsquo;U&rsquo; stands for &lsquo;&times;&rsquo;, &lsquo;A&rsquo; stands for &lsquo;+&rsquo; and &lsquo;C&rsquo; stands for &lsquo;&ndash;&rsquo;, what will come in place of the question mark (?) in the following equation? <br>16 A 24 C 30 E 15 U 9 = ?</p>",
                    question_hi: "<p>4. यदि \'E\' का अर्थ \'&divide;\', \'U\' का अर्थ \'&times;\', \'A\' का अर्थ \'+\' और \'C\' का अर्थ \'&ndash;\' है, तो निम्नलिखित समीकरण में प्रश्न चिन्ह (?) के स्थान पर क्या आएगा?<br>16 A 24 C 30 E 15 U 9 = ?</p>",
                    options_en: [
                        "<p>78</p>",
                        "<p>32</p>",
                        "<p>22</p>",
                        "<p>66</p>"
                    ],
                    options_hi: [
                        "<p>78</p>",
                        "<p>32</p>",
                        "<p>22</p>",
                        "<p>66</p>"
                    ],
                    solution_en: "<p>4.(c) <strong>Given :-</strong> 16 A 24 C 30 E 15 U 9<br>As per given instruction after interchanging the letters with sign we get<br>16 + 24 - 30 &divide;&nbsp;15 &times; 9<br>40 - 2&times; 9 = 22</p>",
                    solution_hi: "<p>4.(c) <strong>दिया गया :- </strong>16 A 24 C 30 E 15 U 9<br>दिए गए निर्देश के अनुसार अक्षरों को चिन्हों से बदलने पर हमें प्राप्त होता है,<br>16 + 24 - 30 &divide;&nbsp;15 &times; 9<br>40 - 2&times; 9 = 22</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "6",
                    question_en: "<p>5. Four letter-clusters have been given, out of which three are alike in some manner and one is different. Select the letter-cluster that is different.<br>(<strong>Note : </strong>The odd one out is not based on the number of consonants/vowels or their position in the letter cluster.)</p>",
                    question_hi: "<p>5. चार अक्षर-समूह दिए गए हैं, जिनमें से तीन किसी न किसी रूप में एक समान हैं और एक उनसे असंगत&nbsp;है। उस असंगत अक्षर-समूह का चयन कीजिए।<br>(<strong>ध्यान दें </strong>: असंगत अक्षर-समूह, उस अक्षर-समूह में व्यंजनों/स्वरों की संख्या या उनके स्थान पर आधारित नहीं है।)</p>",
                    options_en: [
                        "<p>QRJI</p>",
                        "<p>JKQP</p>",
                        "<p>DEWV</p>",
                        "<p>OPMK</p>"
                    ],
                    options_hi: [
                        "<p>QRJI</p>",
                        "<p>JKQP</p>",
                        "<p>DEWV</p>",
                        "<p>OPMK</p>"
                    ],
                    solution_en: "<p>5.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740982617440.png\" alt=\"rId14\" width=\"122\" height=\"94\">,<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740982617533.png\" alt=\"rId15\" width=\"122\" height=\"103\">,<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740982617633.png\" alt=\"rId16\" width=\"114\" height=\"96\"><br>But,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740982617760.png\" alt=\"rId17\" width=\"115\" height=\"100\"></p>",
                    solution_hi: "<p>5.(d) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740982617861.png\" alt=\"rId18\" width=\"113\" height=\"96\">,<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740982617988.png\" alt=\"rId19\" width=\"121\" height=\"100\">, <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740982618108.png\" alt=\"rId20\" width=\"117\" height=\"93\"><br>लेकिन,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740982618243.png\" alt=\"rId21\" width=\"111\" height=\"95\"></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "6",
                    question_en: "<p>6. If each vowel in the word RELATION is changed to the letter following it in the English alphabetical order and each consonant is changed to the letter preceding it in the English alphabetical order, and then arranged in reverse order of English alphabetical order, then which of the following letters will be fourth from the right in the group of letters thus formed ?</p>",
                    question_hi: "<p>6. यदि शब्द RELATION के प्रत्येक स्वर को अंग्रेजी वर्णमाला क्रम में उसके बाद वाले अक्षर से बदल दिया जाए और प्रत्येक व्यंजन को अंग्रेजी वर्णमाला क्रम में उसके पहले वाले अक्षर से बदल दिया जाए, और फिर अंग्रेजी वर्णमाला क्रम के विपरीत क्रम में व्यवस्थित कर दिया जाए, तो इस प्रकार बने अक्षरों के समूह में निम्नलिखित में से कौन-सा अक्षर दाएं से चौथे स्थान पर होगा ?</p>",
                    options_en: [
                        "<p>Q</p>",
                        "<p>P</p>",
                        "<p>K</p>",
                        "<p>M</p>"
                    ],
                    options_hi: [
                        "<p>Q</p>",
                        "<p>P</p>",
                        "<p>K</p>",
                        "<p>M</p>"
                    ],
                    solution_en: "<p>6.(c)<br>Applying the given rules:<br>Each vowel + 1 And Each consonant -1. <br>According to the question,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740982618367.png\" alt=\"rId22\" width=\"181\" height=\"68\"><br>arranging in reverse order of English alphabetical order -<br>QFKBSJPM &rarr;&nbsp;SQPMKJFB<br>Fourth from the right in the group = K</p>",
                    solution_hi: "<p>6.(c)<br>नियमों को लागू करने के लिए:<br>प्रत्येक स्वर + 1<br>प्रत्येक व्यंजन - 1<br>प्रश्न के अनुसार ,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740982618367.png\" alt=\"rId22\" width=\"181\" height=\"68\"><br>अंग्रेजी वर्णमाला के विपरीत क्रम में व्यवस्थित करने पर -<br>QFKBSJPM &rarr; SQPMKJFB<br>समूह में दाएँ से चौथा = K</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "6",
                    question_en: "<p>7. In a certain code language, &lsquo;BAKE&rsquo; is coded as &lsquo;FDMF&rsquo; and &lsquo;TURN&rsquo; is coded as &lsquo;XXTO&rsquo;. How will &lsquo;MOCK&rsquo; be coded in the given language?</p>",
                    question_hi: "<p>7. एक निश्चित कूट भाषा में, &lsquo;BAKE&rsquo; को &lsquo;FDMF&rsquo; के रूप में कूटबद्ध किया जाता है और &lsquo;TURN&rsquo; को &lsquo;XXTO&rsquo; के रूप में कूटबद्ध किया जाता है। दी गई भाषा में &lsquo;MOCK&rsquo; को किस प्रकार कूटबद्ध किया जाएगा?</p>",
                    options_en: [
                        "<p>RRFL</p>",
                        "<p>QREL</p>",
                        "<p>RQGL</p>",
                        "<p>QSEL</p>"
                    ],
                    options_hi: [
                        "<p>RRFL</p>",
                        "<p>QREL</p>",
                        "<p>RQGL</p>",
                        "<p>QSEL</p>"
                    ],
                    solution_en: "<p>7.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740982618508.png\" alt=\"rId23\" width=\"125\" height=\"93\">&nbsp; and&nbsp; &nbsp;<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740982618648.png\" alt=\"rId24\" width=\"131\" height=\"96\">&nbsp;</p>\n<p>Similarly<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740982618790.png\" alt=\"rId25\" width=\"129\" height=\"100\"></p>",
                    solution_hi: "<p>7.(b)</p>\n<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740982618508.png\" alt=\"rId23\" width=\"125\" height=\"93\"> &nbsp;और &nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740982618648.png\" alt=\"rId24\" width=\"131\" height=\"96\">&nbsp;</p>\n<p>&nbsp;इसी प्रकार<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740982618790.png\" alt=\"rId25\" width=\"129\" height=\"100\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "6",
                    question_en: "<p>8. Identify the figure given in the options which when put in place of ? will logically complete the series.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740982618951.png\" alt=\"rId26\" width=\"300\" height=\"62\"></p>",
                    question_hi: "<p>8. विकल्पों में दी गई उस आकृति की पहचान कीजिए जिसे ? के स्थान पर रखने पर श्रृंखला तर्कसंगत रूप से पूर्ण हो जाएगी। <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740982618951.png\" alt=\"rId26\" width=\"300\" height=\"62\"></p>",
                    options_en: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740982619074.png\" alt=\"rId27\" width=\"91\" height=\"93\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740982619191.png\" alt=\"rId28\" width=\"90\" height=\"92\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740982619366.png\" alt=\"rId29\" width=\"91\" height=\"93\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740982619498.png\" alt=\"rId30\" width=\"90\" height=\"92\"></p>"
                    ],
                    options_hi: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740982619074.png\" alt=\"rId27\" width=\"92\" height=\"94\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740982619191.png\" alt=\"rId28\" width=\"91\" height=\"93\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740982619366.png\" alt=\"rId29\" width=\"91\" height=\"93\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740982619498.png\" alt=\"rId30\" width=\"91\" height=\"93\"></p>"
                    ],
                    solution_en: "<p>8.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740982619366.png\" alt=\"rId29\" width=\"90\" height=\"92\"></p>",
                    solution_hi: "<p>8.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740982619366.png\" alt=\"rId29\" width=\"90\" height=\"92\"></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "6",
                    question_en: "<p>9. Which of the following numbers will replace the question mark (?) in the given series?<br>7 28 84 336 1008 ?</p>",
                    question_hi: "<p>9. निम्नलिखित में से कौन-सी संख्या दी गई श्रृंखला में प्रश्न चिह्न (?) के स्थान पर आएगी?<br>7 28 84 336 1008 ?</p>",
                    options_en: [
                        "<p>4032</p>",
                        "<p>3902</p>",
                        "<p>3036</p>",
                        "<p>4128</p>"
                    ],
                    options_hi: [
                        "<p>4032</p>",
                        "<p>3902</p>",
                        "<p>3036</p>",
                        "<p>4128</p>"
                    ],
                    solution_en: "<p>9.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740982619645.png\" alt=\"rId31\" width=\"241\" height=\"66\"></p>",
                    solution_hi: "<p>9.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740982619645.png\" alt=\"rId31\" width=\"241\" height=\"66\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "6",
                    question_en: "<p>10. Read the given statements and conclusions carefully. Assuming that the information&nbsp;given in the statements is true, even if it appears to be at variance with commonly&nbsp;known facts, decide which of the given conclusions logically follow(s) from the<br>statements.<br><strong>Statements:</strong><br>Some handles are bars.<br>All bars are rods.<br>No rod is a stick.<br><strong>Conclusions:</strong><br>(I) Some handles are rods.<br>(II) No bar is a stick.</p>",
                    question_hi: "<p>10. दिए गए कथनों और निष्कर्षों को ध्यानपूर्वक पढ़ें। यह मानते हुए कि कथनों में दी गई जानकारी सत्य है, भले ही यह सामान्य ज्ञात तथ्यों से भिन्न प्रतीत होती हो, निर्णय लें कि दिए गए निष्कर्षों में से कौन सा/से निष्कर्ष कथनों का तार्किक रूप से अनुसरण करता है / करते हैं।<br><strong>कथनः</strong><br>कुछ हैंडल, शलाकें हैं। <br>सभी शलाकें, डंडियाँ हैं।<br>कोई डंडी, छड़ी नहीं है।<br><strong>निष्कर्षः</strong><br>(I) कुछ हैंडल, डंडियाँ हैं।<br>(II) कोई शलाकें, छड़ी नहीं है।</p>",
                    options_en: [
                        "<p>None of the conclusions follow</p>",
                        "<p>Only conclusion I follows</p>",
                        "<p>Only conclusion II follows</p>",
                        "<p>Both conclusions I and II follow</p>"
                    ],
                    options_hi: [
                        "<p>कोई भी निष्कर्ष अनुसरण नहीं करता है</p>",
                        "<p>दोनों निष्कर्ष । और II अनुसरण करते हैं</p>",
                        "<p>केवल निष्कर्ष II अनुसरण करता है</p>",
                        "<p>केवल निष्कर्ष । अनुसरण करता है</p>"
                    ],
                    solution_en: "<p>10.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740982619767.png\" alt=\"rId32\" width=\"255\" height=\"59\"><br>Both conclusion I and II follows.</p>",
                    solution_hi: "<p>10.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740982619920.png\" alt=\"rId33\" width=\"259\" height=\"61\"><br>निष्कर्ष I और II दोनों अनुसरण करते हैं।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "6",
                    question_en: "<p>11. Three of the following four options are alike in a certain way and thus form a group. Which is the one that does NOT belong to that group? <br>(<strong>NOTE: </strong>Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g. 13 &ndash; Operations on 13 such as adding/subtracting/multiplying etc. to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed.)</p>",
                    question_hi: "<p>11. निम्नलिखित चार विकल्पों में से तीन किसी न किसी रूप में समान हैं और इस प्रकार, एक समूह बनाते हैं। कौन-सा विकल्प उस समूह से संबंधित नहीं है? <br>(ध्यान दें: संख्याओं को उनके घटक अंकों में तोड़े बिना, संक्रियाएं पूर्ण संख्याओं पर की जानी चाहिए। उदाहरण के लिए 13 को लें- संख्या 13 में की जाने वाली संक्रिया, जैसे कि जोड़ना/घटाना/गुणा करना केवल 13 में किया जा सकता है। 13 को अंक 1 और 3 में तोड़ना और फिर 1 और 3 के साथ गणितीय संक्रियाएं करने की अनुमति नहीं है।)</p>",
                    options_en: [
                        "<p>12 &ndash; 122 &ndash; 99</p>",
                        "<p>89 &ndash; 212 &ndash; 34</p>",
                        "<p>23 &ndash; 84 &ndash; 38</p>",
                        "<p>58 &ndash; 139 &ndash; 23</p>"
                    ],
                    options_hi: [
                        "<p>12 &ndash; 122 &ndash; 99</p>",
                        "<p>89 &ndash; 212 &ndash; 34</p>",
                        "<p>23 &ndash; 84 &ndash; 38</p>",
                        "<p>58 &ndash; 139 &ndash; 23</p>"
                    ],
                    solution_en: "<p>11.(a)<br><strong>Logic:- </strong>1<sup>st</sup>no&nbsp;&times; 2 + 3<sup>rd</sup>no. = 2<sup>nd</sup>no.<br>(89 - 212 - 34) :- 89 &times;&nbsp;2 + 34 &rArr; 178 + 34 = 212<br>(23 - 84 - 38) :- 23 &times; 2 + 38 &rArr; 46 + 38 = 84<br>(58 - 139 - 23) :- 58 &times; 2 + 23 &rArr; 116 + 23 = 139<br>But<br>(12 - 122 - 99) :- 12 &times; 2 + 99 &rArr; 24 + 99 = 123 &ne; 122</p>",
                    solution_hi: "<p>11.(a)<br><strong id=\"docs-internal-guid-2895dfe6-7fff-0936-3a8c-1475742daecc\">Logic : - </strong>पहला नंबर &times; 2 + तीसरा नंबर &nbsp;= दूसरा नंबर.<br>(89 - 212 - 34) :- 89 &times;&nbsp;2 + 34 &rArr; 178 + 34 = 212<br>(23 - 84 - 38) :- 23 &times; 2 + 38 &rArr; 46 + 38 = 84<br>(58 - 139 - 23) :- 58 &times; 2 + 23 &rArr; 116 + 23 = 139<br>लेकिन<br>(12 - 122 - 99) :- 12 &times; 2 + 99 &rArr; 24 + 99 = 123 &ne; 122</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "6",
                    question_en: "<p>12. In a certain code language,<br>A + B means \'A is the brother of B\',<br>A - B means \'A is the son of B\',<br>A &times; B means \'A is the wife of B\' and<br>A &divide; B means \'A is the father of B\'.<br>Based on the above, how is Q related to S if &lsquo;P - Q &divide; R &times; S + T&rsquo; ?</p>",
                    question_hi: "<p>12. एक निश्चित कूट भाषा में,<br>A + B का अर्थ \'A, B का भाई है\',<br>A - B का अर्थ \'A, B का बेटा है\',<br>A &times; B का अर्थ \'A, B की पत्नी है\' और<br>A &divide; B का अर्थ \'A, B का पिता है\'।<br>उपरोक्त के आधार पर, यदि \'P - Q &divide; R &times; S + T&rsquo; है, तो Q का S से क्या संबंध है?</p>",
                    options_en: [
                        "<p>Husband\'s brother</p>",
                        "<p>Wife\'s father</p>",
                        "<p>Wife\'s brother</p>",
                        "<p>Father</p>"
                    ],
                    options_hi: [
                        "<p>पति का भाई</p>",
                        "<p>पत्नी के पिता</p>",
                        "<p>पत्नी का भाई</p>",
                        "<p>पिता</p>"
                    ],
                    solution_en: "<p>12.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740982620088.png\" alt=\"rId34\" width=\"214\" height=\"97\"><br>Clearly, we can see that Q is the father of S&rsquo;s wife.</p>",
                    solution_hi: "<p>12.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740982620088.png\" alt=\"rId34\" width=\"214\" height=\"97\"><br>अतः , हम देख सकते हैं कि &lsquo;Q&rsquo;, &lsquo;S&rsquo; की पत्नी का पिता है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "6",
                    question_en: "<p>13. Select the correct mirror image of the given combination when the mirror is placed at MN as shown below.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740982620211.png\" alt=\"rId35\" width=\"134\" height=\"101\"></p>",
                    question_hi: "<p>13. दर्पण को नीचे दर्शाए गए अनुसार MN पर रखे जाने पर दिए गए संयोजन के सही दर्पण प्रतिबिंब का चयन कीजिए। <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740982620211.png\" alt=\"rId35\" width=\"134\" height=\"101\"></p>",
                    options_en: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740982620350.png\" alt=\"rId36\" width=\"118\" height=\"23\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740982620461.png\" alt=\"rId37\" width=\"114\" height=\"25\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740982620587.png\" alt=\"rId38\" width=\"114\" height=\"22\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740982620698.png\" alt=\"rId39\" width=\"111\" height=\"33\"></p>"
                    ],
                    options_hi: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740982620350.png\" alt=\"rId36\" width=\"113\" height=\"22\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740982620461.png\" alt=\"rId37\" width=\"114\" height=\"25\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740982620587.png\" alt=\"rId38\" width=\"114\" height=\"22\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740982620698.png\" alt=\"rId39\" width=\"115\" height=\"34\"></p>"
                    ],
                    solution_en: "<p>13.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740982620698.png\" alt=\"rId39\" width=\"115\" height=\"34\"></p>",
                    solution_hi: "<p>13.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740982620698.png\" alt=\"rId39\" width=\"115\" height=\"34\"></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "6",
                    question_en: "<p>14. In a certain code language, \'future is bright\' is coded as \'pl mk ni\' and \'bright shooting star\' is coded as \'dg rz pl\'. How is \'bright\' coded in that language?</p>",
                    question_hi: "<p>14. एक निश्चित कूट भाषा में, \'future is bright\' को \'pl mk ni\' लिखा जाता है और \'bright shooting&nbsp;star\' को dg rz pl\' लिखा जाता है। उसी कूट भाषा में \'bright\' को कैसे लिखा जाएगा?</p>",
                    options_en: [
                        "<p>ni</p>",
                        "<p>dg</p>",
                        "<p>pl</p>",
                        "<p>mk</p>"
                    ],
                    options_hi: [
                        "<p>ni</p>",
                        "<p>dg</p>",
                        "<p>pl</p>",
                        "<p>mk</p>"
                    ],
                    solution_en: "<p>14.(c)&nbsp;<br>future is bright &rarr; pl mk ni&hellip;&hellip;.(i)<br>bright shooting star &rarr; dg rz pl&hellip;&hellip;.(ii)<br>From (i) and (ii) &lsquo;bright&rsquo; and &lsquo;pl&rsquo; are common. The code of &lsquo;bright&rsquo; = &lsquo;pl&rsquo;</p>",
                    solution_hi: "<p>14.(c) <br>future is bright &rarr; pl mk ni&hellip;&hellip;.(i)<br>bright shooting star &rarr; dg rz pl&hellip;&hellip;.(ii)<br>(i) और (ii) से \'bright\' और \'pl\' उभयनिष्ठ हैं। \'bright\' का कूट = \'pl\'</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "6",
                    question_en: "<p>15. What would be the letter on the opposite side of \'P\' if the given sheet is folded to form a cube?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740982620899.png\" alt=\"rId40\" width=\"117\" height=\"165\"></p>",
                    question_hi: "<p>15. यदि नीचे दी गई शीट को मोड़कर एक घन बनाया जाए तो P\' के विपरीत फलक पर कौन-सा अक्षर होगा?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740982620899.png\" alt=\"rId40\" width=\"117\" height=\"165\"></p>",
                    options_en: [
                        "<p>L</p>",
                        "<p>N</p>",
                        "<p>M</p>",
                        "<p>K</p>"
                    ],
                    options_hi: [
                        "<p>L</p>",
                        "<p>N</p>",
                        "<p>M</p>",
                        "<p>K</p>"
                    ],
                    solution_en: "<p>15.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740982621101.png\" alt=\"rId41\" width=\"121\" height=\"140\"><br>The opposite faces are :- L &harr; K , N &harr; P , O &harr; M</p>",
                    solution_hi: "<p>15.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740982621101.png\" alt=\"rId41\" width=\"121\" height=\"140\"><br>विपरीत फलक हैं:- L &harr; K , N &harr; P , O &harr; M</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "6",
                    question_en: "<p>16. Select the option that represents the letters that, when placed from left to right in the blanks below, will complete the letter series.<br>D _ F D _ F _ F _ D _ F</p>",
                    question_hi: "<p>16. उस विकल्प का चयन करें जो उन अक्षरों का निरूपण करता है, जो नीचे दिए गए रिक्त स्थानों में बाएं से दाएं रखे जाने पर अक्षर श्रृंखला को पूरा करेंगे।<br>D _ F D _ F _ F _ D _ F</p>",
                    options_en: [
                        "<p>D F F D D</p>",
                        "<p>F D D F F</p>",
                        "<p>D F D D F</p>",
                        "<p>F F D F F</p>"
                    ],
                    options_hi: [
                        "<p>D F F D D</p>",
                        "<p>F D D F F</p>",
                        "<p>D F D D F</p>",
                        "<p>F F D F F</p>"
                    ],
                    solution_en: "<p>16.(d) D <span style=\"text-decoration: underline;\"><strong>F</strong></span> F / D <span style=\"text-decoration: underline;\"><strong>F</strong></span> F / <span style=\"text-decoration: underline;\"><strong>D</strong></span> F<span style=\"text-decoration: underline;\"><strong>F</strong></span> / D <span style=\"text-decoration: underline;\"><strong>F</strong></span> F</p>",
                    solution_hi: "<p>16.(d) D <span style=\"text-decoration: underline;\"><strong>F</strong></span> F / D <span style=\"text-decoration: underline;\"><strong>F</strong></span> F / <span style=\"text-decoration: underline;\"><strong>D</strong></span> F<span style=\"text-decoration: underline;\"><strong>F</strong></span> / D <span style=\"text-decoration: underline;\"><strong>F</strong></span> F</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "6",
                    question_en: "<p>17. Rudra starts from Point A and drives 13 km towards west. He then takes a left turn, drives 12 km, turns left and drives 15 km. He then takes a left turn and drives 14 km. He takes a final left turn, drives 2 km and stops at Point P. How far (shortest distance) and towards which direction should he drive in order to reach Point A again? (All turns are 90 degrees turns only unless specified)</p>",
                    question_hi: "<p>17. रुद्र बिंदु A से ड्राइव करना शुरू करता है और पश्चिम की ओर 13 km तक ड्राइव करता है। फिर वह बाएं मुड़ता है, 12 km तक ड्राइव करता है, बाएं मुड़ता है और 15 km तक ड्राइव करता है। फिर वह बाएं मुड़ता है और 14 km तक ड्राइव करता है। वह अंत में बाएं मुड़ता है, 2 km तक ड्राइव करता है और बिंदु P पर रुक जाता है। बिंदु A पर दोबारा पहुंचने के लिए उसे कितनी दूर (नयूनतम दूरी) तक और किस दिशा में ड्राइव करना चाहिए? (जब तक। निर्दिष्ट न किया जाए, सभी मोड़ केवल 90 डिग्री के मोड़ हैं)</p>",
                    options_en: [
                        "<p>4 km to the south</p>",
                        "<p>2 km to the south</p>",
                        "<p>1 km to the south</p>",
                        "<p>3 km to the south</p>"
                    ],
                    options_hi: [
                        "<p>4 km दक्षिण की ओर</p>",
                        "<p>2 km दक्षिण की ओर</p>",
                        "<p>1 km दक्षिण की ओर</p>",
                        "<p>3 km दक्षिण की ओर</p>"
                    ],
                    solution_en: "<p>17.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740982621255.png\" alt=\"rId42\" width=\"181\" height=\"134\"><br>Hence, Rudra should drive 2 km towards the south.</p>",
                    solution_hi: "<p>17.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740982621447.png\" alt=\"rId43\" width=\"200\" height=\"145\"><br>इसलिए, रुद्र को दक्षिण की ओर 2 किमी गाड़ी चलानी चाहिए।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "6",
                    question_en: "<p>18. Three of the following four are alike in a certain way and thus form a group. Which is the one that does NOT belong to that group ?<br>(<strong>Note : </strong>The odd one out is not based on the number of consonants/vowels or their position in the letter cluster.)</p>",
                    question_hi: "<p>18. निम्नलिखित चार अक्षर-समूहों में से तीन अक्षर-समूह एक निश्चित प्रकार से समान हैं और इस प्रकार एक समूह बनाते हैं। वह कौन-सा अक्षर-समूह है जो उस समूह से संबंधित नहीं है ?<br>(<strong>ध्यान दें :</strong> असंगत अक्षर-समूह, उस अक्षर-समूह में व्यंजनों/स्वरों की संख्या या उनके स्थान पर आधारित नहीं है।)</p>",
                    options_en: [
                        "<p>LNQ</p>",
                        "<p>CEH</p>",
                        "<p>TVY</p>",
                        "<p>PQT</p>"
                    ],
                    options_hi: [
                        "<p>LNQ</p>",
                        "<p>CEH</p>",
                        "<p>TVY</p>",
                        "<p>PQT</p>"
                    ],
                    solution_en: "<p>18.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740982621643.png\" alt=\"rId44\" width=\"125\" height=\"72\">, <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740982621780.png\" alt=\"rId45\" width=\"123\" height=\"72\">, <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740982621913.png\" alt=\"rId46\" width=\"123\" height=\"71\"><br>But<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740982622026.png\" alt=\"rId47\" width=\"124\" height=\"73\"></p>",
                    solution_hi: "<p>18.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740982621643.png\" alt=\"rId44\" width=\"125\" height=\"72\">, <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740982621780.png\" alt=\"rId45\" width=\"123\" height=\"72\">, <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740982621913.png\" alt=\"rId46\" width=\"123\" height=\"71\"><br>लेकिन,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740982622026.png\" alt=\"rId47\" width=\"124\" height=\"73\"></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "6",
                    question_en: "<p>19. Which two numbers should be interchanged to make the given equation correct?<br>72 - 56 + (13 + 19) &times; 2 - 95 &divide; 5 + 17 = 76<br>(Note: Interchange should be done of the entire number and not individual digits of a given number.)</p>",
                    question_hi: "<p>19. दिए गए समीकरण को सही बनाने के लिए किन दो संख्याओं को आपस में बदला जाना चाहिए?<br>72 - 56 + (13 + 19) &times; 2 - 95 &divide; 5 + 17 = 76<br>(ध्यान दें: संपूर्ण संख्या को आपस में बदला जाना चाहिए, न कि दी गई संख्या के अलग-अलग अंकों को।)</p>",
                    options_en: [
                        "<p>19 and 5</p>",
                        "<p>19 and 17</p>",
                        "<p>17 and 56</p>",
                        "<p>13 and 17</p>"
                    ],
                    options_hi: [
                        "<p>19 और 5</p>",
                        "<p>19 और 17</p>",
                        "<p>17 और 56</p>",
                        "<p>13 और 17</p>"
                    ],
                    solution_en: "<p>19.(b)<br>72 - 56 + (13 + 19) &times; 2 - 95 &divide; 5 + 17 = 76<br>After checking options one by one only option (b) satisfies the condition. Hence, interchanging 19 and 17 in the above expression, we have ;<br>72 - 56 + (13 + 17) &times; 2 - 95 &divide; 5 +19&nbsp;<br>16 + 60 -19 + 19 = 76<br>76 = 76<br>LHS = RHS</p>",
                    solution_hi: "<p>19.(b)<br>72 - 56 + (13 + 19) &times; 2 - 95 &divide; 5 + 17 = 76<br>एक एक कर विकल्पों की जांच करने पर केवल विकल्प (b) संतुष्ट करता है अतः उपरोक्त अभिव्यक्ति में 19 और 17 को आपस में बदलने पर,&nbsp;<br>72 - 56 + (13 + 17) &times; 2 - 95 &divide; 5 +19&nbsp;<br>16 + 60 -19 + 19 = 76<br>76 = 76<br>LHS = RHS</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "6",
                    question_en: "20. Select the set in which the numbers are related in the same way as are the numbers of the following sets. <br />(NOTE: Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g. 13 – Operations on 13 such as adding/subtracting/multiplying etc. to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed.) <br />(18, 7, 119) <br />(11, 9, 90) ",
                    question_hi: "20. उस समुच्चय का चयन कीजिए जिसमें संख्याएं ठीक उसी प्रकार संबंधित हैं जिस प्रकार दिए गए समुच्चयों की संख्याएं संबंधित हैं।<br />(ध्यान दें: संख्याओं को उनके घटक अंकों में अलग-अलग किए बिना पूर्ण संख्याओं पर संक्रियाएं की जानी चाहिए। उदाहरण के लिए संख्या 13 - संख्या 13 पर संक्रियाएं जैसे 13 को जोड़ना/घटाना/गुणा करना आदि किया जा सकता है। 13 को 1 और 3 में अलग-अलग करने की और फिर 1 और 3 पर गणितीय संक्रियाएं करने की अनुमति नहीं है।)<br />(18, 7, 119) <br />(11, 9, 90) ",
                    options_en: [
                        " (16, 5, 65) ",
                        " (13, 5, 58) ",
                        " (17, 6, 102) ",
                        " (12, 4, 44) <br /> "
                    ],
                    options_hi: [
                        " (16, 5, 65) ",
                        " (13, 5, 58) ",
                        " (17, 6, 102) ",
                        " (12, 4, 44) "
                    ],
                    solution_en: "20.(d) Logic :- (1st number × 2nd number) - (2nd number) = 3rd number<br />(18, 7 ,119) :- (18 × 7) - (7) ⇒ (126) - (7) = 119<br />(11, 9, 90) :- (11 × 9) - (9) ⇒ (99) - (9) = 90<br />Similarly,<br />(12, 4 ,44) :- (12 × 4) - (4) ⇒ (48) - (4) = 44",
                    solution_hi: "20.(d) तर्क:- (पहली संख्या × दूसरी संख्या) - (दूसरी संख्या) = तीसरी संख्या  <br />(18, 7 ,119) :- (18 × 7) - (7) ⇒ (126) - (7) = 119<br />(11, 9, 90) :- (11 × 9) - (9) ⇒ (99) - (9) = 90<br />इसी प्रकार,<br />(12, 4 ,44) :- (12 × 4) - (4) ⇒ (48) - (4) = 44",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "6",
                    question_en: "<p>21. What should come in place of the question mark (?) in the given series based on the&nbsp;English alphabetical order?<br>FQN , CLL, ZGJ, WBH, ?</p>",
                    question_hi: "<p>21. अंग्रेजी वर्णमाला क्रम के आधार पर दी गई श्रृंखला में प्रश्न-चिह्न (?) के स्थान पर क्या आना चाहिए?<br>FQN, CLL, ZGJ, WBH, ?</p>",
                    options_en: [
                        "<p>SVF</p>",
                        "<p>SVE</p>",
                        "<p>TVE</p>",
                        "<p>TWF</p>"
                    ],
                    options_hi: [
                        "<p>SVF</p>",
                        "<p>SVE</p>",
                        "<p>TVE</p>",
                        "<p>TWF</p>"
                    ],
                    solution_en: "<p>21.(d) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740982622251.png\" alt=\"rId48\" width=\"304\" height=\"97\"></p>",
                    solution_hi: "<p>21.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740982622251.png\" alt=\"rId48\" width=\"304\" height=\"97\"></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "6",
                    question_en: "<p>22. How many triangles are there in the given figure?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740982622403.png\" alt=\"rId49\" width=\"162\" height=\"120\"></p>",
                    question_hi: "<p>22. दी गई आकृति में कितने त्रिभुज हैं?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740982622403.png\" alt=\"rId49\" width=\"162\" height=\"120\"></p>",
                    options_en: [
                        "<p>12</p>",
                        "<p>10</p>",
                        "<p>11</p>",
                        "<p>9</p>"
                    ],
                    options_hi: [
                        "<p>12</p>",
                        "<p>10</p>",
                        "<p>11</p>",
                        "<p>9</p>"
                    ],
                    solution_en: "<p>22.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740982622559.png\" alt=\"rId50\" width=\"209\" height=\"168\"><br>Total number of triangles = 8 + ABC + CBD = 10</p>",
                    solution_hi: "<p>22.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740982622559.png\" alt=\"rId50\" width=\"209\" height=\"168\"><br>त्रिभुजो की कुल संख्या = 8 + ABC + CBD = 10</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "23",
                    section: "6",
                    question_en: "<p>23. In a certain code language, &lsquo;CYCLE&rsquo; is coded as &lsquo;242241522&rsquo; and &lsquo;FINAL&rsquo; is coded as &lsquo;2118132615&rsquo;. How will &lsquo;MUSIC&rsquo; be coded in that language?</p>",
                    question_hi: "<p>23. एक निश्चित कूट भाषा में, &lsquo;CYCLE&rsquo; को &lsquo;242241522&rsquo; के रूप में कूटबद्ध किया जाता है और &lsquo;FINAL&rsquo; को &lsquo;2118132615&rsquo; के रूप में कूटबद्ध किया जाता है। उसी भाषा में &lsquo;MUSIC&rsquo; को किस प्रकार कूटबद्ध किया जाएगा?</p>",
                    options_en: [
                        "<p>146192184</p>",
                        "<p>14681824</p>",
                        "<p>146191824</p>",
                        "<p>1421191824</p>"
                    ],
                    options_hi: [
                        "<p>146192184</p>",
                        "<p>14681824</p>",
                        "<p>146191824</p>",
                        "<p>1421191824</p>"
                    ],
                    solution_en: "<p>23.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740982622723.png\" alt=\"rId51\" width=\"125\" height=\"109\">,&nbsp; &nbsp;<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740982622984.png\" alt=\"rId52\" width=\"125\" height=\"110\"><br>Similarly,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740982623147.png\" alt=\"rId53\" width=\"125\" height=\"112\"></p>",
                    solution_hi: "<p>23.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740982623411.png\" alt=\"rId54\" width=\"132\" height=\"113\">,&nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740982623552.png\" alt=\"rId55\" width=\"129\" height=\"112\"><br>इसी प्रकार<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740982623693.png\" alt=\"rId56\" width=\"139\" height=\"125\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "24",
                    section: "6",
                    question_en: "<p>24. A paper is folded and cut as shown below. How will it appear when unfolded?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740982623798.png\" alt=\"rId57\" width=\"255\" height=\"91\"></p>",
                    question_hi: "<p>24. नीचे दर्शाए गए अनुसार एक कागज को मोड़कर काटा जाता है। खोलने पर यह कैसा दिखाई देगा?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740982623798.png\" alt=\"rId57\" width=\"255\" height=\"91\"></p>",
                    options_en: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740982623918.png\" alt=\"rId58\" width=\"92\" height=\"91\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740982624020.png\" alt=\"rId59\" width=\"91\" height=\"90\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740982624128.png\" alt=\"rId60\" width=\"94\" height=\"92\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740982624223.png\" alt=\"rId61\" width=\"92\" height=\"84\"></p>"
                    ],
                    options_hi: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740982623918.png\" alt=\"rId58\" width=\"91\" height=\"90\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740982624020.png\" alt=\"rId59\" width=\"91\" height=\"90\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740982624128.png\" alt=\"rId60\" width=\"91\" height=\"89\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740982624223.png\" alt=\"rId61\" width=\"94\" height=\"86\"></p>"
                    ],
                    solution_en: "<p>24.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740982624223.png\" alt=\"rId61\" width=\"100\" height=\"92\"></p>",
                    solution_hi: "<p>24.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740982624223.png\" alt=\"rId61\" width=\"100\" height=\"92\"></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "25",
                    section: "18",
                    question_en: "<p>25. 26 is related to 12 following a certain logic. Following the same logic, 36 is related to 18. Which of the following is related to 20 using the same logic?</p>",
                    question_hi: "<p>25. एक निश्चित तर्क का अनुसरण करते हुए 26, 12 से संबंधित है। उसी तर्क का अनुसरण करते हुए 36, 18 से संबंधित है। उसी तर्क का उपयोग करते हुए निम्न में से कौन सी संख्&zwj;या 20 से संबंधित है?</p>",
                    options_en: [
                        "<p>53</p>",
                        "<p>42</p>",
                        "<p>35</p>",
                        "<p>45</p>"
                    ],
                    options_hi: [
                        "<p>53</p>",
                        "<p>42</p>",
                        "<p>35</p>",
                        "<p>45</p>"
                    ],
                    solution_en: "<p>25.(d) <strong>Logic :-</strong> (Multiplication of the digit of 1st number) = 2nd number<br>(26, 12) :- (2 &times; 6) = 12<br>(36 ,18) :- (3 &times; 6) = 18<br>Similarly,<br>(45 , 20) :- (4 &times; 5) = 20</p>",
                    solution_hi: "<p>25.(d) <strong>तर्क :-</strong> (पहली संख्या के अंक का गुणन) = दूसरी संख्या<br>(26, 12) :- (2 &times; 6) = 12<br>(36 ,18) :- (3 &times; 6) = 18<br>इसी प्रकार,<br>(45 , 20) :- (4 &times; 5) = 20</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "26",
                    section: "18",
                    question_en: "<p>26. The city of Hampi was named after the local mother goddess called_____________.</p>",
                    question_hi: "<p>26. हम्पी शहर का नाम स्थानीय मातृ देवी _________ के नाम पर रखा गया था।</p>",
                    options_en: [
                        "<p>Hampini</p>",
                        "<p>Hidimba</p>",
                        "<p>Hampidevi</p>",
                        "<p>Pampadevi</p>"
                    ],
                    options_hi: [
                        "<p>हम्पिनी</p>",
                        "<p>हिडिम्बा</p>",
                        "<p>हम्पीदेवी</p>",
                        "<p>पंपादेवी</p>"
                    ],
                    solution_en: "<p>26.(d) <strong>Pampadevi</strong>. Hampi is a temple town in Karnataka and was the capital of the Vijayanagara Empire. It was declared as a World Heritage Site by UNESCO in 1986.</p>",
                    solution_hi: "<p>26.(d)<strong> पंपादेवी।</strong> हम्पी कर्नाटक में एक मंदिरों का शहर है और विजयनगर साम्राज्य की राजधानी थी। इसे 1986 में यूनेस्को द्वारा विश्व धरोहर स्थल घोषित किया गया था।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "27",
                    section: "18",
                    question_en: "<p>27. Where is the headquarters of the Squash Rackets Federation of India located?</p>",
                    question_hi: "<p>27. स्क्वैश रैकेट फेडरेशन ऑफ इंडिया का मुख्यालय कहाँ स्थित है?</p>",
                    options_en: [
                        "<p>Chennai</p>",
                        "<p>Bangalore</p>",
                        "<p>Mumbai</p>",
                        "<p>New Delhi</p>"
                    ],
                    options_hi: [
                        "<p>चेन्नई</p>",
                        "<p>बैंगलोर</p>",
                        "<p>मुंबई</p>",
                        "<p>नई दिल्ली</p>"
                    ],
                    solution_en: "<p>27.(a) <strong>Chennai. </strong>Squash Rackets Federation of India (SRFI): It conducts the National Squash Championship, and promotes the game through the state level squash bodies. Squash court&rsquo;s Measurements - A rectangular surface (9.75m &times; 6.4m in dimension for singles and 13.72mx7.62m for doubles). Rackets used for squash are 686mm long and 215mm wide. Players - Saurav Ghosal, Dipika Pallikal, Joshna Chinappa, Anahat Singh and Abhay Singh.</p>",
                    solution_hi: "<p>27.(a) <strong>चेन्नई। </strong>स्क्वैश रैकेट्स फेडरेशन ऑफ इंडिया (SRFI): यह राष्ट्रीय स्क्वैश चैम्पियनशिप आयोजित करता है, और राज्य स्तरीय स्क्वैश निकायों के माध्यम से खेल को बढ़ावा देता है। स्क्वैश कोर्ट की माप - एक आयताकार सतह (एकल के लिए आयाम 9.75m x 6.4m और युगल के लिए 13.72m x 7.62m)। स्क्वैश के लिए उपयोग किए जाने वाले रैकेट 686 मिमी लंबे और 215 मिमी चौड़े हैं। खिलाड़ी - सौरव घोषाल, दीपिका पल्लीकल, जोशना चिनप्पा, अनाहत सिंह और अभय सिंह।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "28",
                    section: "18",
                    question_en: "<p>28. Which of the following is an example of White Goods?</p>",
                    question_hi: "<p>28. निम्नलिखित में से कौन-सा व्हाइट गूड्स (White Goods) का उदाहरण है?</p>",
                    options_en: [
                        "<p>Air Conditioners and LED Lights</p>",
                        "<p>Pharmacy products</p>",
                        "<p>Imported items</p>",
                        "<p>Agricultural equipment</p>"
                    ],
                    options_hi: [
                        "<p>एयर कंडीशनर और LED लाइट</p>",
                        "<p>फार्मेसी उत्पाद</p>",
                        "<p>आयातित वस्तुएं</p>",
                        "<p>कृषि उपकरण</p>"
                    ],
                    solution_en: "<p>28.(a) <strong>Air Conditioners and LED Lights.</strong> White goods refer to heavy consumer durables or large home appliances, which were traditionally available only in white. The white goods industry consists of refrigerators, washing machines and dryers, dishwashers, etc.</p>",
                    solution_hi: "<p>28.(a) <strong>एयर कंडीशनर और LED लाइट।</strong> व्हाइट गुड्स का अर्थ भारी उपभोक्ता टिकाऊ सामान या बड़े घरेलू उपकरण हैं, जो पारंपरिक रूप से केवल सफेद रंग में उपलब्ध होते है। व्हाइट गुड्स उद्योग में रेफ्रिजरेटर, वॉशिंग मशीन और ड्रायर, डिशवॉशर आदि शामिल हैं।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "29",
                    section: "18",
                    question_en: "<p>29. Which metallic element is called &lsquo;ferromagnetic&rsquo; because of its strong attraction?</p>",
                    question_hi: "<p>29.किस धात्विक तत्व को उसके प्रबल आकर्षण के कारण \'लौहचुम्बकीय\' कहा जाता है?</p>",
                    options_en: [
                        "<p>Iron</p>",
                        "<p>Molybdenum</p>",
                        "<p>Gold</p>",
                        "<p>Aluminium</p>"
                    ],
                    options_hi: [
                        "<p>आयरन</p>",
                        "<p>मोलिब्डेनम</p>",
                        "<p>गोल्ड</p>",
                        "<p>एल्युमीनियम</p>"
                    ],
                    solution_en: "<p>29.(a) <strong>Iron</strong> (Symbol : Fe, Atomic Number : 26) exhibits strong magnetic properties and is strongly attracted to magnets. Ferromagnetism is the ability of materials like iron, cobalt, and nickel to be magnetized and influenced by magnetic fields.</p>",
                    solution_hi: "<p>29.(a) <strong>आयरन </strong>(प्रतीक: Fe, परमाणु संख्या: 26) प्रबल चुंबकीय गुण प्रदर्शित करता है और चुम्बकों की ओर प्रबलता से आकर्षित होता है। लौहचुम्बकत्व, आयरन, कोबाल्ट और निकल जैसी पदार्थों की चुंबकीय क्षेत्रों द्वारा चुम्बकित और प्रभावित होने की क्षमता है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "30",
                    section: "18",
                    question_en: "<p>30. ______is the first South Indian musician to win a Grammy for Best World Music Album for his role as a ghatam and morsing player in Mickey Hart\'s album \'Planet Drum\'.</p>",
                    question_hi: "<p>30. मिकी हार्ट (Mickey Hart) के एल्बम \'प्लैनेट ड्रम\' (Planet Drum) में घटम और मोर्सिंग वादक के रूप में अपनी भूमिका हेतु सर्वश्रेष्ठ विश्व संगीत एल्बम के लिए ग्रैमी जीतने वाले पहले दक्षिण भारतीय संगीतकार कौन हैं?</p>",
                    options_en: [
                        "<p>Phani Narayana</p>",
                        "<p>S Ballesh</p>",
                        "<p>Dr. Ramachandra Murthy</p>",
                        "<p>TH Vinayakram</p>"
                    ],
                    options_hi: [
                        "<p>फानी नारायण (Phani Narayana)</p>",
                        "<p>एस बल्लेश (S Ballesh)</p>",
                        "<p>डॉ. रामचंद्र मूर्ति (Dr. Ramachandra Murthy)</p>",
                        "<p>टी एच विनायकराम (TH Vinayakram)</p>"
                    ],
                    solution_en: "<p>30.(d)<strong> TH Vinayakram</strong> (Vikku Vinayakram) : Awards - Padma Shri (2002), Sangeet Natak Akademi Fellowship (2012), Padma Bhushan (2014). Other personalities : Phani Narayana - Veena player. S Ballesh - Shehnai player. Dr. Ramachandra Murthy - Flute player.</p>",
                    solution_hi: "<p>30.(d) <strong>टी एच विनायकराम </strong>(विक्कू विनायकराम): पुरस्कार - पद्म श्री (2002), संगीत नाटक अकादमी फ़ेलोशिप (2012), पद्म भूषण (2014)। अन्य व्यक्तित्व : फणी नारायण - वीणा वादक। एस बलेश - शहनाई वादक। डॉ. रामचन्द्र मूर्ति - बाँसुरी वादक।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "31",
                    section: "18",
                    question_en: "<p>31. Who authored the book \"Vision to Victory: Unleashing India&rsquo;s SME Platform\", launched on October 23, 2024?</p>",
                    question_hi: "<p>31. 23 अक्टूबर, 2024 को प्रकाशित की गई पुस्तक \"विज़न टू विक्टरी: अनलीशिंग इंडियाज SME प्लेटफ़ॉर्म\" के लेखक कौन हैं?</p>",
                    options_en: [
                        "<p>Ajay Thakur</p>",
                        "<p>Subrahmanyam Jaishankar</p>",
                        "<p>Dr. S. L. Bhyrappa</p>",
                        "<p>Bob Woodward</p>"
                    ],
                    options_hi: [
                        "<p>अजय ठाकुर</p>",
                        "<p>सुब्रह्मण्यम जयशंकर</p>",
                        "<p>डॉ. एस. एल. भैरप्पा</p>",
                        "<p>बॉब वुडवर्ड</p>"
                    ],
                    solution_en: "<p>31. (a) <strong>Ajay Thakur.</strong> The book, launched on October 23, 2024, at Sahara Star, Mumbai, focuses on the role of SMEs in India\'s economic growth and strategies for their development.</p>",
                    solution_hi: "<p>31.(a) <strong>अजय ठाकुर।</strong> 23 अक्टूबर, 2024 को सहारा स्टार, मुंबई में प्रारंभ की गई यह पुस्तक भारत के आर्थिक विकास में SME की भूमिका और उनके विकास की रणनीतियों पर केंद्रित है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "32",
                    section: "18",
                    question_en: "<p>32. Which of the following is an inactivated (killed) polio vaccine developed in 1952?</p>",
                    question_hi: "<p>32. निम्नलिखित में से कौन-सी 1952 में विकसित एक निष्क्रिय (हत) पोलियो वैक्सीन है?</p>",
                    options_en: [
                        "<p>Salk vaccine</p>",
                        "<p>Imvanex vaccine</p>",
                        "<p>HDCV vaccine</p>",
                        "<p>TAB vaccine</p>"
                    ],
                    options_hi: [
                        "<p>साल्क वैक्सीन</p>",
                        "<p>इम्वेनेक्स वैक्सीन</p>",
                        "<p>एच.डी.सी.वी. (HDCV) वैक्सीन</p>",
                        "<p>टैब (TAB) वैक्सीन</p>"
                    ],
                    solution_en: "<p>32.(a) <strong>Salk vaccine. </strong>The Inactivated Polio Vaccine (IPV) developed by Jonas Salk and the Oral Polio Vaccine (OPV) developed by Albert Sabin. Polio (poliomyelitis) is a highly infectious viral disease that enters the body through the mouth, multiplying in the intestine before invading the nervous system. Imvanex vaccine used to protect against smallpox. HDCV (Human diploid cell vaccine) is a rabies vaccine. TAB vaccine (Typhoid-paratyphoid A and B) is used for the treatment of typhoid.</p>",
                    solution_hi: "<p>32.(a) <strong>साल्क वैक्सीन।</strong> जोनास साल्क द्वारा विकसित निष्क्रिय पोलियो वैक्सीन (IPV) और अल्बर्ट सबिन द्वारा विकसित ओरल पोलियो वैक्सीन (OPV)। पोलियो (पोलियोमाइलाइटिस) एक अत्यधिक संक्रामक वायरल रोग है जो मुंह के माध्यम से शरीर में प्रवेश करती है, तंत्रिका तंत्र पर आक्रमण करने से पहले आंत में बढ़ता है। इम्वेनेक्स वैक्सीन का उपयोग चेचक से बचाव के लिए किया जाता है। HDCV (ह्यूमन डिप्लोइड सेल वैक्सीन) एक रेबीज वैक्सीन है। टैब (TAB) वैक्सीन (टाइफाइड-पैराटाइफाइड A और B) का उपयोग टाइफाइड के उपचार के लिए किया जाता है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "33",
                    section: "18",
                    question_en: "<p>33. What is used to make transparent soaps?</p>",
                    question_hi: "<p>33. पारदर्शी साबुन बनाने में किसका प्रयोग किया जाता है?</p>",
                    options_en: [
                        "<p>Sodium chloride</p>",
                        "<p>Ethanol</p>",
                        "<p>Rosin</p>",
                        "<p>Sodium carbonate</p>"
                    ],
                    options_hi: [
                        "<p>सोडियम क्लोराइड</p>",
                        "<p>इथेनॉल</p>",
                        "<p>रोसिन</p>",
                        "<p>सोडियम कार्बोनेट</p>"
                    ],
                    solution_en: "<p>33.(b) <strong>Ethanol</strong> (C<sub>2</sub>H<sub>6</sub>O) appears as a clear colorless liquid with a characteristic vinous odor and pungent taste. Sodium carbonate (Na<sub>2</sub>CO<sub>3</sub>) is the disodium salt of carbonic acid with alkalinizing properties. Sodium chloride (NaCl) also known as salt, common salt, table salt or halite. Rosin is a solid form of resin obtained from pines and other plants, mostly conifers.</p>",
                    solution_hi: "<p>33.(b) <strong>इथेनॉल</strong> (C<sub>2</sub>H<sub>6</sub>O) एक स्पष्ट रंगहीन तरल पदार्थ होता है जिसमें एक विशिष्ट विषैली गंध और तीखा स्वाद होता है। सोडियम कार्बोनेट (Na<sub>2</sub>CO<sub>3</sub>) क्षारीय गुणों वाला कार्बोनिक अम्ल का डिसोडियम लवण है। सोडियम क्लोराइड (NaCl) को नमक, सामान्य नमक, टेबल नमक या हेलाइट के नाम से भी जाना जाता है। रोसिन, चीड़ और अन्य पौधों, ज्यादातर शंकुधारी पौधों से प्राप्त राल का एक ठोस रूप है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "34",
                    section: "18",
                    question_en: "<p>34. Which of the following parts/structures is located at the topmost part of a Stupa?</p>",
                    question_hi: "<p>34. निम्नलिखित में से कौन-सा भाग/संरचना स्तूप के सबसे ऊपरी भाग में स्थित होता है?</p>",
                    options_en: [
                        "<p>Torana</p>",
                        "<p>Harmika</p>",
                        "<p>Vedika</p>",
                        "<p>Medhi</p>"
                    ],
                    options_hi: [
                        "<p>तोरण</p>",
                        "<p>हर्मिका</p>",
                        "<p>वेदिका</p>",
                        "<p>मेधि</p>"
                    ],
                    solution_en: "<p>34.(b) <strong>Harmika.</strong> Torana: These are ornamental gateways found at the cardinal points around the base of the stupa. Vedika: This is a railing that encircles the dome of the stupa, typically located halfway up the structure. Medhi: This is the circular platform surrounding the base of the stupa, where devotees walk around for circumambulation (pradakshina).</p>",
                    solution_hi: "<p>34.(b) <strong>हर्मिका। </strong>तोरण: ये स्तूप के आधार के चारों ओर मुख्य बिंदुओं पर पाए जाने वाले सजावटी प्रवेश द्वार हैं। वेदिका: यह एक रेलिंग है जो स्तूप के गुंबद को चारो ओर से घेरती है, जो आमतौर पर संरचना के आधे हिस्से में स्थित होती है। मेधी: यह स्तूप के आधार के चारों ओर गोलाकार मंच है, जहां भक्त परिक्रमा (प्रदक्षिणा) के लिए घूमते हैं।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "35",
                    section: "18",
                    question_en: "<p>35. From which of the following Constitutions has the India Constitution adopted the system of &lsquo;First Past the Post&rsquo;?</p>",
                    question_hi: "<p>35. निम्नलिखित में से किस संविधान से भारतीय संविधान ने \'&lsquo;बहुलवादी (First Past the Post)&rsquo;\' की प्रणाली को अपनाया है?</p>",
                    options_en: [
                        "<p>Irish Constitution</p>",
                        "<p>French Constitution</p>",
                        "<p>United States Constitution</p>",
                        "<p>British Constitution</p>"
                    ],
                    options_hi: [
                        "<p>आयरिश संविधान</p>",
                        "<p>फ्रांसीसी संविधान</p>",
                        "<p>संयुक्त राज्य अमेरिका का संविधान</p>",
                        "<p>ब्रिटिश संविधान</p>"
                    ],
                    solution_en: "<p>35.(d) <strong>British Constitution. </strong>The First Past the Post (FPTP) system, also known as the simple majority system, is used in India for elections to the Lok Sabha and State Legislative Assemblies. Irish Constitution: Ireland uses the Single Transferable Vote (STV) system, a form of proportional representation. French Constitution: France employs a two-round system, where a candidate must win an absolute majority (over 50%) in the first round or a relative majority in the second round.</p>",
                    solution_hi: "<p>35.(d) <strong>ब्रिटिश संविधान। </strong>बहुलवादी (फर्स्ट पास्ट द पोस्ट (FPTP)) प्रणाली, जिसे साधारण बहुमत प्रणाली भी कहा जाता है, जिसका उपयोग भारत में लोकसभा और राज्य विधान सभाओं के चुनावों के लिए किया जाता है। आयरिश संविधान: आयरलैंड एकल संक्रमणीय मत (STV) प्रणाली का उपयोग करता है, जो आनुपातिक प्रतिनिधित्व का एक रूप है। फ्रांसीसी संविधान: फ्रांस दो-चरण प्रणाली का उपयोग करता है, जहाँ उम्मीदवार को पहले चरण में पूर्ण बहुमत (50% से अधिक) या दूसरे चरण में सापेक्ष बहुमत प्राप्त करना होता है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "36",
                    section: "18",
                    question_en: "<p>36. Select the INCORRECT pair of the location of the refinery and its state from the following</p>",
                    question_hi: "<p>36. रिफाइनरी तथा उनके संबंधित राज्य के संबंध में गलत युग्म का चयन करें।</p>",
                    options_en: [
                        "<p>Bina Refinery &ndash; Madhya Pradesh</p>",
                        "<p>Numaligarh Refinery &ndash; Assam</p>",
                        "<p>Koyali Refinery &ndash; Gujarat</p>",
                        "<p>Tatipaka Refinery &ndash; Telangana</p>"
                    ],
                    options_hi: [
                        "<p>बीना रिफाइनरी - मध्य<strong id=\"docs-internal-guid-b22ca90a-7fff-1c2c-91a5-4cd45d9c3e89\">&nbsp;</strong>प्रदेश</p>",
                        "<p>नुमालीगढ़ रिफाइनरी - असम</p>",
                        "<p>कोयली रिफाइनरी - गुजरात</p>",
                        "<p>तातिपाका रिफाइनरी - तेलंगाना</p>"
                    ],
                    solution_en: "<p>36.(d) <strong>Tatipaka Refinery &ndash; Telangana. </strong>Tatipaka Oil Refinery is located in the East Godavari district in Andhra Pradesh. It was set up on 3rd September 2001. Some list of oil refineries: Vadinar Refinery (Gujarat), Kochi Refinery (Kerala), Mangalore Refinery (Karnataka), Paradip Refinery (Odisha), Barauni Refinery (Barauni in Bihar).</p>",
                    solution_hi: "<p>36.(d) <strong>तातिपाका रिफ़ाइनरी &ndash; तेलंगाना</strong>। तातिपाका तेल रिफ़ाइनरी आंध्र प्रदेश के पूर्वी गोदावरी जिले में स्थित है। इसकी स्थापना 3 सितंबर 2001 को हुई थी। तेल रिफ़ाइनरियों की कुछ सूची: वाडिनार रिफ़ाइनरी (गुजरात), कोच्चि रिफ़ाइनरी (केरल), मैंगलोर रिफ़ाइनरी (कर्नाटक), पारादीप रिफ़ाइनरी (ओडिशा), बरौनी रिफ़ाइनरी (बिहार के बरौनी में)।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "37",
                    section: "18",
                    question_en: "<p>37. What is celebrated on October 15 in honor of Dr. A.P.J. Abdul Kalam\'s birth anniversary?</p>",
                    question_hi: "<p>37. डॉ. ए.पी.जे. अब्दुल कलाम की जयंती के उपलक्ष्य में 15 अक्टूबर को क्या मनाया जाता है?</p>",
                    options_en: [
                        "<p>National Science Day</p>",
                        "<p>World Student\'s Day</p>",
                        "<p>Teacher\'s Day</p>",
                        "<p>National Technology Day</p>"
                    ],
                    options_hi: [
                        "<p>राष्ट्रीय विज्ञान दिवस</p>",
                        "<p>विश्व छात्र दिवस</p>",
                        "<p>शिक्षक दिवस</p>",
                        "<p>राष्ट्रीय प्रौद्योगिकी दिवस</p>"
                    ],
                    solution_en: "<p>37.(b)<strong> World Student\'s Day,</strong> celebrated on the birth anniversary of Dr. A.P.J. Abdul Kalam, honors his remarkable legacy as India\'s \"Missile Man\" and a visionary leader. The theme for 2024, \"Empowering Students to be Agents of Change&rdquo;.</p>",
                    solution_hi: "<p>37.(b) <strong>विश्व छात्र दिवस, </strong>डॉ. ए.पी.जे. अब्दुल कलाम की जयंती पर मनाया जाने वाला भारत के \"मिसाइल मैन\" और दूरदर्शी नेता के रूप में उनकी उल्लेखनीय विरासत का सम्मान करता है। 2024 का थीम, \"छात्रों को बदलाव के एजेंट बनने के लिए सशक्त बनाना\"।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "38",
                    section: "18",
                    question_en: "<p>38. Which of the following is an example of prokaryotic cells?</p>",
                    question_hi: "<p>38. निम्नलिखित में से कौन-सा, प्रोकैरियोटिक कोशिकाओं का उदाहरण है?</p>",
                    options_en: [
                        "<p>Plasmodium</p>",
                        "<p>Leishmania</p>",
                        "<p>Yeast</p>",
                        "<p>Bacteria</p>"
                    ],
                    options_hi: [
                        "<p>प्लाज्मोडियम</p>",
                        "<p>लीशमैनिया</p>",
                        "<p>यीस्ट</p>",
                        "<p>बैक्टीरिया</p>"
                    ],
                    solution_en: "<p>38.(d) <strong>Bacteria. </strong>Prokaryotic cells lack a well-organized nucleus, with their genetic material consisting of a single DNA molecule located in the cytoplasm. These cells do not have a nuclear membrane, and they also lack organelles such as mitochondria, lysosomes, endoplasmic reticulum, chloroplasts, and nucleolus. Examples of prokaryotic cells include blue-green algae. Yeast is an example of a eukaryotic unicellular fungi cell.</p>",
                    solution_hi: "<p>38.(d) <strong>बैक्टीरिया। </strong>प्रोकैरियोटिक कोशिकाओं में सुव्यवस्थित केन्द्रक का अभाव होता है, जिसमें उनका आनुवंशिक पदार्थ कोशिका द्रव्य में स्थित एक एकल DNA अणु से बना होता है। इन कोशिकाओं में परमाणु झिल्ली नहीं होती है, और इनमें माइटोकॉन्ड्रिया, लाइसोसोम, एंडोप्लाज्मिक रेटिकुलम, क्लोरोप्लास्ट और न्यूक्लियोलस जैसे अंग भी नहीं होते हैं। प्रोकैरियोटिक कोशिकाओं के उदाहरणों में नीले-हरे शैवाल शामिल हैं। यीस्ट यूकेरियोटिक एककोशिकीय कवक कोशिका का एक उदाहरण है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "39",
                    section: "18",
                    question_en: "<p>39. What is the duration of a Kabaddi match?</p>",
                    question_hi: "<p>39. कबड्डी मैच की अवधि कितनी होती है?</p>",
                    options_en: [
                        "<p>Two halves of 40 minutes, with a half time break of 5 minutes</p>",
                        "<p>Two halves of 30 minutes, with a half time break of 10 minutes</p>",
                        "<p>Two halves of 20 minutes, with a half time break of 5 minutes</p>",
                        "<p>Two halves of 45 minutes, with a half time break of 10 minutes</p>"
                    ],
                    options_hi: [
                        "<p>40 मिनट के दो हाफ, 5 मिनट के हाफ टाइम ब्रेक के साथ</p>",
                        "<p>30 मिनट के दो हाफ, 10 मिनट के हाफ टाइम ब्रेक के साथ</p>",
                        "<p>20 मिनट के दो हाफ, 5 मिनट के हाफ टाइम ब्रेक के साथ</p>",
                        "<p>45 मिनट के दो हाफ, 10 मिनट के हाफ टाइम ब्रेक के साथ</p>"
                    ],
                    solution_en: "<p>39.(c) <strong>Kabaddi: </strong>Ancient Indian contact team sport played with two teams of seven players. Famous players- Pardeep Narwal, Anup Kumar, Rahul Chaudhari, Naveen Kumar, Pawan Sehrawat. Terminology- Do or Die Raid, Pursuit, Struggle, Super Tackle.</p>",
                    solution_hi: "<p>39.(c) <strong>कबड्डी: </strong>प्राचीन भारतीय संपर्क टीम खेल सात खिलाड़ियों की दो टीमों के साथ खेला जाता था। प्रसिद्ध खिलाड़ी- प्रदीप नरवाल, अनुप कुमार, राहुल चौधरी, नवीन कुमार, पवन सहरावत। शब्दावली- डु और डाई रेड, परसूट, स्ट्रगल, सुपर टैकल।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "40",
                    section: "18",
                    question_en: "<p>40. Which Governor General built the Victoria Memorial in Kolkata in 1905 in the memory of Queen Victoria of Britain?</p>",
                    question_hi: "<p>40. वर्ष 1905 में ब्रिटेन की महारानी विक्टोरिया की स्मृति में किस गवर्नर जनरल ने कोलकाता में विक्टोरिया मेमोरियल का निर्माण करवाया था?</p>",
                    options_en: [
                        "<p>Lord Canning</p>",
                        "<p>Lord Ripen</p>",
                        "<p>Lord Curzon</p>",
                        "<p>Lord Bentinck</p>"
                    ],
                    options_hi: [
                        "<p>लॉर्ड कैनिंग</p>",
                        "<p>लॉर्ड रिपन</p>",
                        "<p>लॉर्ड कर्जन</p>",
                        "<p>लॉर्ड बेंटिक</p>"
                    ],
                    solution_en: "<p>40.(c) <strong>Lord Curzon</strong> succeeded Lord Elgin and served as India&rsquo;s Viceroy between 1899 and 1905. Reforms during his reign - Calcutta Corporation Act, 1899, The rate of salt-tax was reduced, the Co-operative Credit Societies Act passed and developed Railways infrastructure.</p>",
                    solution_hi: "<p>40.(c)<strong> लॉर्ड कर्जन</strong>, लॉर्ड एल्गिन के उत्तराधिकारी बने और 1899 और 1905 के बीच भारत के वायसराय के रूप में कार्य किया। उनके शासनकाल के दौरान सुधार - कलकत्ता निगम अधिनियम 1899, नमक-कर की दर कम की गई, सहकारी क्रेडिट सोसायटी अधिनियम पारित किया गया, और रेलवे की आधारभूत संरचना का विकास किया गया ।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "41",
                    section: "18",
                    question_en: "<p>41. In which year the Second Anglo-Burmese War was fought ?</p>",
                    question_hi: "<p>41. द्वितीय एंग्लो-बर्मी युद्ध (Anglo-Burmese War) किस वर्ष लड़ा गया था?</p>",
                    options_en: [
                        "<p>1552</p>",
                        "<p>1652</p>",
                        "<p>1752</p>",
                        "<p>1852</p>"
                    ],
                    options_hi: [
                        "<p>1552</p>",
                        "<p>1652</p>",
                        "<p>1752</p>",
                        "<p>1852</p>"
                    ],
                    solution_en: "<p>41.(d) <strong>1852.</strong> Anglo-Burmese War conflict was between British India and Burma. The Second Anglo-Burmese War was fought during the times of Lord Dalhousie. The First War (1824-26) ended with the treaty of Yandabo (February 1826).</p>",
                    solution_hi: "<p>41.(d) <strong>1852. </strong>एंग्लो-बर्मी युद्ध ब्रिटिश भारत और बर्मा के मध्य हुआ था। दूसरा एंग्लो-बर्मी युद्ध लॉर्ड डलहौजी के शासनकाल में लड़ा गया था। प्रथम एंग्लो-बर्मी युद्ध (1824-26) यांडाबो की संधि (फरवरी 1826) के साथ समाप्त हुआ था।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "42",
                    section: "18",
                    question_en: "<p>42. Match the pair correctly:<br><span style=\"text-decoration: underline;\">National Waterways</span>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<span style=\"text-decoration: underline;\">Stretch</span> <br>a) NW-1&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;1. Kakinada-Puducherry <br>b) Nw-2&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 2. Kottapuram- Kallam<br>c) NW-3&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;3. Allahabad - Haldia<br>d) NW-5&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;4. Sadiya- Dhubri<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 5. Talcher-Dhamra</p>",
                    question_hi: "<p>42. दिए गए युग्मों को सुमेलित करें:<br><span style=\"text-decoration: underline;\">राष्ट्रीय जलमार्ग</span>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; <span style=\"text-decoration: underline;\">विस्तार</span> <br>a) एन.डब्ल्यू-1&nbsp; &nbsp; &nbsp; &nbsp; 1. काकीनाडा - पुडुचेरी (Kakinada-Puducherry) <br>b) एन.डब्ल्यू-2&nbsp; &nbsp; &nbsp; &nbsp; 2. कोट्टापुरम - कल्लम (Kottapuram- Kallam)<br>c) एन.डब्ल्यू-3&nbsp; &nbsp; &nbsp; &nbsp; 3. इलाहाबाद - हल्दिया (Allahabad -Haldia)<br>d) एन.डब्ल्यू-5&nbsp; &nbsp; &nbsp; &nbsp; 4. सादिया-धुबरी (Sadiya- Dhubri)<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;5. तालचर-धामरा (Talcher-Dhamra)</p>",
                    options_en: [
                        "<p>a-1, b-2, c-4, d-5</p>",
                        "<p>a-3, b-4, c-2, d-5</p>",
                        "<p>a-4, b-3, c-5, d- 1</p>",
                        "<p>a-2, b-5, c-3, d-1</p>"
                    ],
                    options_hi: [
                        "<p>a-1, b-2, c-4, d-5</p>",
                        "<p>a-3, b-4, c-2, d-5</p>",
                        "<p>a-4, b-3, c-5, d- 1</p>",
                        "<p>a-2, b-5, c-3, d-1</p>"
                    ],
                    solution_en: "<p>42.(b) <strong>a-3, b-4, c-2, d-5</strong>. There are 111 officially notified Inland National Waterways (NWs) in India identified for the purposes of inland water transport, as per The National Waterways Act, 2016. NW-1 is the longest waterway in India, stretching 1,620 kilometers from Haldia to Allahabad. NW-69 is the smallest (5 km) waterway in India. It is part of the Manimuthar River in Tamil Nadu.</p>",
                    solution_hi: "<p>42.(b)<strong> a-3, b-4, c-2, d-5.</strong> राष्ट्रीय जलमार्ग अधिनियम, 2016 के अनुसार, अंतर्देशीय जल परिवहन के उद्देश्य से भारत में 111 आधिकारिक रूप से अधिसूचित अंतर्देशीय राष्ट्रीय जलमार्ग (NW) हैं। NW-1 भारत का सबसे लंबा जलमार्ग है, जो हल्दिया से इलाहाबाद तक 1,620 किलोमीटर तक फैला है। NW-69 भारत का सबसे छोटा (5 किमी) जलमार्ग है। यह तमिलनाडु में मणिमुथर नदी का हिस्सा है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "43",
                    section: "18",
                    question_en: "<p>43. \'Maila Aanchal&rsquo;, one of the finest examples of regional novels is written by:</p>",
                    question_hi: "<p>43. आंचलिक उपन्यास के उत्कृष्ट उदाहरण \'मैला आँचल\' के उपन्यासकार है।</p>",
                    options_en: [
                        "<p>Phanishwar Nath Renu</p>",
                        "<p>Kamleshwar</p>",
                        "<p>Kedar Nath Singh</p>",
                        "<p>Munshi Premchand</p>"
                    ],
                    options_hi: [
                        "<p>फ़णीश्वर नाथ रेणु</p>",
                        "<p>कमलेश्वर</p>",
                        "<p>केदार नाथ सिंह</p>",
                        "<p>मुंशी प्रेमचंद</p>"
                    ],
                    solution_en: "<p>43.(a) <strong>Phanishwar Nath Renu.</strong> His Other novels and Stories: &ldquo;Parti parikatha&rdquo;, &ldquo;Deerghatapa&rdquo;, &ldquo;Juloos&rdquo;, &ldquo;Kitne Chaurahe&rdquo;, etc. Other Authors and their works: Kamleshwar - &ldquo;Kitne Pakistan&rdquo;. Kedar Nath Singh - &ldquo;Akaal mein Saaras&rdquo;, &ldquo;Yahaan Se Dekho&rdquo;. Munshi Premchand - &ldquo;Mansarovar&rdquo;, &ldquo;Seva Sadan&rdquo;, &ldquo;Rangbhumi&rdquo;, &ldquo;Godaan&rdquo;, &ldquo;Gaban&rdquo;, &ldquo;Premashram&rdquo;.</p>",
                    solution_hi: "<p>43.(a) <strong>फणीश्वर नाथ रेणु ।</strong> उनके अन्य उपन्यास और कहानियाँ: \"परती परिकथा\", \"दीर्घतप\", \"जुलूस\", \"कितने चौराहे\", आदि। अन्य लेखक और उनकी रचनाएँ: कमलेश्वर - \"कितने पाकिस्तान\"। केदार नाथ सिंह - \"अकाल में सारस\", \"यहाँ से देखो\"। मुंशी प्रेमचंद - \"मानसरोवर\", \"सेवा सदन\", \"रंगभूमि\", \"गोदान\", \"गबन\", \"प्रेमाश्रम\"।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "44",
                    section: "18",
                    question_en: "<p>44. Which of the following is an example of a double displacement reaction?</p>",
                    question_hi: "<p>44. निम्नलिखित में से कौन-सा द्विविस्थापन अभिक्रिया का एक उदाहरण है?</p>",
                    options_en: [
                        "<p>2Pb(NO<sub>3</sub>)<sub>2</sub> (s) + Heat &rarr; 2PbO (s) + 4NO<sub>2</sub> (g) + O<sub>2</sub> (g)</p>",
                        "<p>ZnO + C &rarr; + Zn + CO</p>",
                        "<p>Fe(s) + CuSO4 (aq) &rarr; FeSO4 (aq) + Cu (s)</p>",
                        "<p>Na<sub>2</sub>SO<sub>4</sub> (aq) + BaCl<sub>2 </sub>(aq) &rarr; BaSO<sub>4</sub> (s) + 2NaCl (aq)</p>"
                    ],
                    options_hi: [
                        "<p>2Pb(NO<sub>3</sub>)<sub>2</sub> (s) + Heat &rarr; 2PbO (s) + 4NO<sub>2</sub> (g) + O<sub>2</sub> (g)</p>",
                        "<p>ZnO + C &rarr; + Zn + CO</p>",
                        "<p>Fe(s) + CuSO<sub>4</sub> (aq) &rarr; FeSO4 (aq) + Cu (s)</p>",
                        "<p>Na<sub>2</sub>SO<sub>4</sub> (aq) + BaCl<sub>2</sub> (aq) &rarr; BaSO<sub>4</sub> (s) + 2NaCl (aq)</p>"
                    ],
                    solution_en: "<p>44.(d) Double displacement reactions - The chemical reactions in which one component each of both the reacting molecules is exchanged to form the products. It generally takes place in aqueous solutions in which the ions precipitate and there is an exchange of ions. Example - AgNO<sub>3</sub> (Silver nitrate) + NaCl (Sodium chloride) &rarr;&nbsp; AgCl (Silver chloride) + NaNO<sub>3</sub> (Sodium nitrate).</p>",
                    solution_hi: "<p>44.(d) दोहरी विस्थापन अभिक्रियाएँ - वे रासायनिक अभिक्रियाएँ जिनमें उत्पाद बनाने के लिए दोनों प्रतिक्रियाशील अणुओं में से प्रत्येक के एक-एक घटक का आदान-प्रदान होता है। यह आम तौर पर जलीय घोल में होता है, जिसमें आयन अवक्षेपित होते हैं और आयनों का आदान-प्रदान होता है। उदाहरण - AgNO<sub>3</sub> (सिल्वर नाइट्रेट) + NaCl (सोडियम क्लोराइड) &rarr; AgCl (सिल्वर क्लोराइड) + NaNO<sub>3</sub> (सोडियम नाइट्रेट)।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "45",
                    section: "18",
                    question_en: "<p>45. Which state does the noted Sindhi Sarangi player Lakha Khan belong to?</p>",
                    question_hi: "<p>45. विख्यात सिंधी सारंगी वादक लाखा खां किस राज्य के निवासी हैं?</p>",
                    options_en: [
                        "<p>Rajasthan</p>",
                        "<p>Punjab</p>",
                        "<p>Uttarakhand</p>",
                        "<p>Haryana</p>"
                    ],
                    options_hi: [
                        "<p>राजस्थान</p>",
                        "<p>पंजाब</p>",
                        "<p>उत्तराखंड</p>",
                        "<p>हरियाणा</p>"
                    ],
                    solution_en: "<p>45.(a) <strong>Rajasthan.</strong> Sindhi Sarangi player Lakha Khan was awarded with Padma Shri in 2021. Other sarangi players - Sabir Khan, Aruna Narayan, Ramesh Mishra, Dhruba Ghosh, Bundu Khan.</p>",
                    solution_hi: "<p>45.(a) <strong>राजस्थान। </strong>सिंधी सारंगी वादक लाखा खां को 2021 में पद्म श्री से सम्मानित किया गया था। अन्य सारंगी वादक - साबिर खान, अरुणा नारायण, रमेश मिश्रा, ध्रुबा घोष, बुंदू खान।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "46",
                    section: "18",
                    question_en: "<p>46. Who has taken command of the International Space Station (ISS) for the second time?</p>",
                    question_hi: "<p>46. किसने दूसरी बार अंतर्राष्ट्रीय अंतरिक्ष स्टेशन (ISS) का कमान संभाला है?</p>",
                    options_en: [
                        "<p>Kalpana Chawla</p>",
                        "<p>Sunita Williams</p>",
                        "<p>Rakesh Sharma</p>",
                        "<p>Peggy Whitson</p>"
                    ],
                    options_hi: [
                        "<p>कल्पना चावला</p>",
                        "<p>सुनीता विलियम्स</p>",
                        "<p>राकेश शर्मा</p>",
                        "<p>पेगी व्हिटसन</p>"
                    ],
                    solution_en: "<p>46.(b) <strong>Sunita Williams.</strong> She, along with fellow astronaut Butch Wilmore, has been on the station since June 5, 2024. Sunita Williams and Vic Willmore had originally planned to stay on the ISS for eight days. But their return was canceled due to technical issues with the spacecraft.&nbsp;</p>\n<p>&nbsp;</p>",
                    solution_hi: "<p>46.(b) <strong>सुनीता विलियम्स</strong>. वह 5 जून 2024 से साथी अंतरिक्ष यात्री बुच विलमोर के साथ स्टेशन पर हैं। सुनीता विलियम्स और विक विलमोर ने पहले ISS पर आठ दिन रहने की योजना बनाई थी, लेकिन उनके वापसी की योजना तकनीकी समस्याओं के कारण रद्द कर दी गई थी।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "47",
                    section: "18",
                    question_en: "<p>47. Who among the following was the founder of the Indian Association for the Cultivation of Science in 1876, an institution devoted to the pursuit of fundamental research in the frontier areas of basic sciences?</p>",
                    question_hi: "<p>47. निम्नलिखित में से कौन 1876 में इंडियन एसोसिएशन फॉर द कल्टीवेशन ऑफ साइंस के संस्थापक थे, जो बुनियादी विज्ञान के अग्रणी क्षेत्रों में मौलिक अनुसंधान की खोज के लिए समर्पित एक संस्था है?</p>",
                    options_en: [
                        "<p>Dr. Mahendra Lal Sircar</p>",
                        "<p>Chandrasekhara Venkata Raman</p>",
                        "<p>Har Gobind Khorana</p>",
                        "<p>Mokshagundam Visvesvaraya</p>"
                    ],
                    options_hi: [
                        "<p>डॉ महेंद्र लाल सरकार</p>",
                        "<p>चंद्रशेखर वेंकट रमन</p>",
                        "<p>हर गोबिंद खुराना</p>",
                        "<p>मोक्षगुंडम विश्वेश्वरैया</p>"
                    ],
                    solution_en: "<p>47.(a) <strong>Dr. Mahendra Lal Sircar. </strong>The Indian Association for the Cultivation of Science (IACS) in Calcutta was established on July 29, 1876. Recognized as the oldest institute in India dedicated to fundamental research, IACS focuses on advancing knowledge in the frontier areas of basic sciences. Professor C V Raman worked at IACS during 1907 to 1933, and it is here that he discovered the celebrated Effect that bears his name and for which he was awarded the Nobel Prize in Physics in 1930.</p>",
                    solution_hi: "<p>47.(a) <strong>डॉ. महेंद्र लाल सरकार</strong>। कलकत्ता में इंडियन एसोसिएशन फॉर द कल्टीवेशन ऑफ साइंस (IACS) की स्थापना 29 जुलाई, 1876 को हुई थी। मौलिक अनुसंधान के लिए समर्पित भारत के सबसे पुराने संस्थान के रूप में मान्यता प्राप्त, IACS बुनियादी विज्ञान के अग्रणी क्षेत्रों में ज्ञान को आगे बढ़ाने पर ध्यान केंद्रित करता है। प्रोफेसर सी. वी. रमन ने 1907 से 1933 के दौरान IACS में कार्य किया और यहीं पर उन्होंने प्रसिद्ध प्रभाव की खोज की जिसका नाम उनके नाम पर रखा गया और जिसके लिए उन्हें 1930 में भौतिकी में नोबेल पुरस्कार से सम्मानित किया गया।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "48",
                    section: "18",
                    question_en: "<p>48. The Suresh Tendulkar committee was formed in 2005 for_________.</p>",
                    question_hi: "<p>48. 2005 में _______ के लिए सुरेश तेंदुलकर समिति का गठन किया गया था।</p>",
                    options_en: [
                        "<p>economic reform</p>",
                        "<p>foreign policy</p>",
                        "<p>poverty estimates</p>",
                        "<p>employment</p>"
                    ],
                    options_hi: [
                        "<p>आर्थिक सुधार</p>",
                        "<p>विदेश नीति</p>",
                        "<p>गरीबी के अनुमान</p>",
                        "<p>रोजगार</p>"
                    ],
                    solution_en: "<p>48.(c) <strong>Poverty estimates.</strong> Other committees : Sukhamoy Chakravarty Committee (1985) - Examined issues related to monetary policy and economic reforms. Chelliah Committee (1991) - Recommended economic reforms and liberalization policies. Janaki Ballabh Patnaik Committee (2005) - Reviewed the implementation of the National Rural Employment Guarantee Act (NREGA).</p>",
                    solution_hi: "<p>48.(c) <strong>गरीबी के अनुमान।</strong> अन्य समितियाँ: सुखमय चक्रवर्ती समिति (1985) - मौद्रिक नीति और आर्थिक सुधारों से संबंधित मुद्दों की जाँच करने के लिए। चेलैया समिति (1991) - आर्थिक सुधारों और उदारीकरण नीतियों की सिफारिश करने के लिए। जानकी बल्लभ पटनायक समिति (2005) - राष्ट्रीय ग्रामीण रोजगार गारंटी अधिनियम (NREGA) के कार्यान्वयन की समीक्षा करने के लिए।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "49",
                    section: "18",
                    question_en: "<p>49. During a National Emergency, the term of Lok Sabha can be extended for:</p>",
                    question_hi: "<p>49. राष्ट्रीय आपातकाल के दौरान, लोकसभा का कार्यकाल _______ तक के लिए बढ़ाया जा सकता है।</p>",
                    options_en: [
                        "<p>three months at a time</p>",
                        "<p>six months at a time</p>",
                        "<p>one year at a time</p>",
                        "<p>two years at a time</p>"
                    ],
                    options_hi: [
                        "<p>एक बार में तीन माह</p>",
                        "<p>एक बार में छ: माह</p>",
                        "<p>एक बार में एक वर्ष</p>",
                        "<p>एक बार में दो वर्ष</p>"
                    ],
                    solution_en: "<p>49.(c) <strong>One year at a time. </strong>During a National Emergency in India, the term of the Lok Sabha can be extended by the Parliament. According to Article 83 of the Indian Constitution, the normal term of the Lok Sabha is five years. However, under Article 352, if a National Emergency is declared, the term of the Lok Sabha can be extended beyond five years. The extension can only be done for one year at a time and cannot extend beyond six months after the emergency has ceased to operate.</p>",
                    solution_hi: "<p>49.(c) <strong>एक बार में एक वर्ष। </strong>भारत में राष्ट्रीय आपातकाल के दौरान, संसद द्वारा लोकसभा का कार्यकाल बढ़ाया जा सकता है। भारतीय संविधान के अनुच्छेद 83 के अनुसार, लोकसभा का सामान्य कार्यकाल पाँच वर्ष का होता है। अनुच्छेद 352 के तहत, यदि राष्ट्रीय आपातकाल घोषित किया जाता है, तो लोकसभा का कार्यकाल पाँच वर्ष से अधिक बढ़ाया जा सकता है। इसका विस्तार एक बार में केवल एक वर्ष के लिए किया जा सकता है और आपातकाल समाप्त होने के बाद इसे छह महीने से अधिक नहीं बढ़ाया जा सकता है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "50",
                    section: "17",
                    question_en: "<p>50. Select the personality who received the Sangeet Natak Akademi 2021 award for his/her contribution in Mohiniyattam.</p>",
                    question_hi: "<p>50. उस भारतीय व्यक्तित्व की पहचान करें जिसे मोहिनीअट्टम में उसके योगदान के लिए संगीत नाटक अकादमी पुरस्कार 2021 से सम्&zwj;मानित किया गया।</p>",
                    options_en: [
                        "<p>Thokchom Ibemubi Devi</p>",
                        "<p>Bhuvan Kumar</p>",
                        "<p>Neena Prasad</p>",
                        "<p>Bijay Kumar Jena</p>"
                    ],
                    options_hi: [
                        "<p>थोकचोम इबेमुबी देवी</p>",
                        "<p>भुवन कुमार</p>",
                        "<p>नीना प्रसाद</p>",
                        "<p>बिजय कुमार जेना</p>"
                    ],
                    solution_en: "<p>50.(c) <strong>Neena Prasad.</strong> Sangeet Natak Akademi Award recipients in dance (2021) : Thokchom Ibemubi Devi - Manipuri, Bijay Kumar Jena - Music for Dance, Bhuvan Kumar - Chhau, Jayalakshmi Eshwar - Bharatanatyam, Shama Bhate - Kathak, Inchakkattu Ramachandran Pillai - Kathakali, N. Sailaja - Kuchipudi, Kalamandalam Girija - Other Major Traditions of Dance.</p>",
                    solution_hi: "<p>50.(c) <strong>नीना प्रसाद । </strong>नृत्य में संगीत नाटक अकादमी पुरस्कार प्राप्तकर्ता (2021): थोकचोम इबेमुबी देवी - मणिपुरी, बिजय कुमार जेना - नृत्य के लिए संगीत, भुवन कुमार - छाऊ, जयलक्ष्मी ईश्वर - भरतनाट्यम, शमा भाटे - कथक, इंचाकट्टू रामचंद्रन पिल्लई - कथकली, एन. शैलजा - कुचिपुड़ी, कलामंडलम गिरिजा - नृत्य की अन्य प्रमुख परंपराएँ।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "51",
                    section: "17",
                    question_en: "<p>51. Suraj covers 598 km in a boat in 39 hours against the stream and he takes 12 hours with the stream then find the speed of the stream ?</p>",
                    question_hi: "<p>51. सूरज, नाव से 598 km की दूरी धारा की विपरीत दिशा में 39 घंटे में तय करता है और धारा की दिशा में 12 घंटे में तय करता है, तो धारा की चाल ज्ञात कीजिए।</p>",
                    options_en: [
                        "<p>15.63 km/h</p>",
                        "<p>10.58 km/h</p>",
                        "<p>16.23 km/h</p>",
                        "<p>17.25 km/h</p>"
                    ],
                    options_hi: [
                        "<p>15.63 km/h</p>",
                        "<p>10.58 km/h</p>",
                        "<p>16.23 km/h</p>",
                        "<p>17.25 km/h</p>"
                    ],
                    solution_en: "<p>51.(d) Let the speed of boat and stream be x&nbsp;and y respectively,<br>According to question,<br>&rArr; x - y = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>598</mn><mn>39</mn></mfrac></math> &hellip;&hellip;(i)<br>&rArr; x + y = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>598</mn><mn>12</mn></mfrac></math> &hellip;&hellip;(ii)<br>On subtracting equation (i) from (ii) we get,<br>&rArr; 2y = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>598</mn><mn>12</mn></mfrac></math> - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>598</mn><mn>39</mn></mfrac></math><br>&rArr; 2y = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>7774</mn><mo>-</mo><mn>2392</mn></mrow><mn>156</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5382</mn><mn>156</mn></mfrac></math><br>&rArr; y = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5382</mn><mn>312</mn></mfrac></math> = 17.25 km/h</p>",
                    solution_hi: "<p>51.(d) नाव और धारा की गति क्रमशः x और y,<br>प्रश्न के अनुसार,<br>&rArr; x - y = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>598</mn><mn>39</mn></mfrac></math> &hellip;&hellip;(i)<br>&rArr; x + y = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>598</mn><mn>12</mn></mfrac></math> &hellip;&hellip;(ii)<br>समीकरण (ii) से (i) को घटाने पर, <br>&rArr; 2y = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>598</mn><mn>12</mn></mfrac></math> - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>598</mn><mn>39</mn></mfrac></math><br>&rArr; 2y = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>7774</mn><mo>-</mo><mn>2392</mn></mrow><mn>156</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5382</mn><mn>156</mn></mfrac></math><br>&rArr; y = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5382</mn><mn>312</mn></mfrac></math> = 17.25किमी/घंटा</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "52",
                    section: "17",
                    question_en: "<p>52. The smallest natural number which is divisible by 15, 60, 6 and 17 is:</p>",
                    question_hi: "<p>52. वह सबसे छोटी प्राकृतिक संख्या ज्ञात करें जो 15, 60, 6 और 17 से विभाज्य है।</p>",
                    options_en: [
                        "<p>1030</p>",
                        "<p>1020</p>",
                        "<p>1106</p>",
                        "<p>1065</p>"
                    ],
                    options_hi: [
                        "<p>1030</p>",
                        "<p>1020</p>",
                        "<p>1106</p>",
                        "<p>1065</p>"
                    ],
                    solution_en: "<p>52.(b) To find the smallest natural number divisible by 15, 60, 6, and 17, we take their Least Common Multiple (LCM).<br>15 = 3 &times; 5, 60 = 2<sup>2</sup> &times; 3 &times; 5 , 6 = 2 &times; 3, 17 = 17 (prime number)<br>LCM = 2<sup>2</sup> &times; 3<sup>1</sup> &times; 5<sup>1</sup> &times; 17<sup>1</sup><br>= 4 &times; 3 &times; 5 &times; 17 <br>= 1020</p>",
                    solution_hi: "<p>52.(b) 15, 60, 6 और 17 से विभाज्य सबसे छोटी प्राकृतिक संख्या ज्ञात करने के लिए, हम इनका लघुत्तम समापवर्त्य (LCM) लेते हैं।<br>15 = 3 &times; 5, 60 = 2<sup>2</sup> &times; 3 &times; 5 , 6 = 2 &times; 3, 17 = 17 (अभाज्य संख्या)<br>LCM = 2<sup>2</sup> &times; 3<sup>1</sup> &times; 5<sup>1</sup> &times; 17<sup>1</sup><br>= 4 &times; 3 &times; 5 &times; 17 <br>= 1020</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "53",
                    section: "17",
                    question_en: "<p>53. The top of a 12 m high tower makes an angle of depression of 60&deg; with the bottom of an electric pole and angle of depression of 30&deg; with the top of the pole. What is the height of the electric pole?</p>",
                    question_hi: "<p>53. 12 मीटर ऊंचे टॉवर का शीर्ष एक बिजली के खंभे के नीचे से 60&deg; का अवनमन कोण बनाता है और खम्भे के शीर्ष से 30&deg; का अवनमन कोण बनाता है। विद्युत पोल की ऊंचाई कितनी है?</p>",
                    options_en: [
                        "<p>10 m</p>",
                        "<p>9 m</p>",
                        "<p>8 m</p>",
                        "<p>11 m</p>"
                    ],
                    options_hi: [
                        "<p>10 m</p>",
                        "<p>9 m</p>",
                        "<p>8 m</p>",
                        "<p>11 m</p>"
                    ],
                    solution_en: "<p>53.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740982624355.png\" alt=\"rId62\" width=\"147\" height=\"131\"><br>Tower = AB ,CD = pole <br>AP = X , CD = 12 - X <br>tan 60&deg; = <math display=\"inline\"><mfrac><mrow><mn>12</mn></mrow><mrow><mi>B</mi><mi>C</mi><mi>&#160;</mi></mrow></mfrac></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math> = <math display=\"inline\"><mfrac><mrow><mn>12</mn></mrow><mrow><mi>B</mi><mi>C</mi><mi>&#160;</mi></mrow></mfrac></math><br>BC = 4<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math><br>For , triangle APD <br>tan 30&deg; = <math display=\"inline\"><mfrac><mrow><mi>A</mi><mi>P</mi></mrow><mrow><mi>P</mi><mi>D</mi></mrow></mfrac></math> <br><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><msqrt><mn>3</mn><mi>&#160;</mi></msqrt></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">X</mi><mrow><mn>4</mn><msqrt><mn>3</mn></msqrt></mrow></mfrac></math> <br>X = 4<br>Hence , CD = 12 - X = 12 - 4 = 8 m</p>",
                    solution_hi: "<p>53.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740982624355.png\" alt=\"rId62\" width=\"147\" height=\"131\"><br>टावर = AB ,CD = खंभा<br>AP = X , CD = 12 - X <br>tan 60&deg; = <math display=\"inline\"><mfrac><mrow><mn>12</mn></mrow><mrow><mi>B</mi><mi>C</mi><mi>&#160;</mi></mrow></mfrac></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math> = <math display=\"inline\"><mfrac><mrow><mn>12</mn></mrow><mrow><mi>B</mi><mi>C</mi><mi>&#160;</mi></mrow></mfrac></math><br>BC = 4<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math><br>त्रिभुज APD के लिए,<br>tan 30&deg; = <math display=\"inline\"><mfrac><mrow><mi>A</mi><mi>P</mi></mrow><mrow><mi>P</mi><mi>D</mi></mrow></mfrac></math> <br><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><msqrt><mn>3</mn><mi>&#160;</mi></msqrt></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">X</mi><mrow><mn>4</mn><msqrt><mn>3</mn></msqrt></mrow></mfrac></math><br>X = 4<br>अत:, CD = 12 - X = 12 - 4 = 8 m</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "54",
                    section: "17",
                    question_en: "<p>54. To clear the old stock, Arshad offers a discount scheme to his customers - on buying 24 glass tumblers get 12 glass tumblers free. What is the effective percentage discount given by Arshad to his customers?</p>",
                    question_hi: "<p>54. पुराने स्टॉक को खत्म करने के लिए, अरशद अपने ग्राहकों को एक छूट योजना के अंतर्गत 24 गिलास खरीदने पर 12 गिलास मुफ्त देता हैं। अरशद द्वारा अपने ग्राहकों को दी गई प्रभावी प्रतिशत छूट क्या है?</p>",
                    options_en: [
                        "<p>34<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math>%</p>",
                        "<p>30<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math>%</p>",
                        "<p>32<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math>%</p>",
                        "<p>33<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math>%</p>"
                    ],
                    options_hi: [
                        "<p>34<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math>%</p>",
                        "<p>30<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math>%</p>",
                        "<p>32<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math>%</p>",
                        "<p>33<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math>%</p>"
                    ],
                    solution_en: "<p>54.(d)<br>Effective discount % <br>= <math display=\"inline\"><mfrac><mrow><mn>12</mn></mrow><mrow><mn>12</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>24</mn></mrow></mfrac></math> &times; 100 <br>= 33<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math>%</p>",
                    solution_hi: "<p>54.(d)<br>प्रभावी छूट % <br>= <math display=\"inline\"><mfrac><mrow><mn>12</mn></mrow><mrow><mn>12</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>24</mn></mrow></mfrac></math> &times; 100 <br>= 33<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math>%</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "55",
                    section: "17",
                    question_en: "<p>55. If \'a\' is the smallest positive integer and the number 8764a529 is completely divisible by 9, then find the value of 17 (3a + 5).</p>",
                    question_hi: "<p>55. यदि \'a\' सबसे छोटा धनात्मक पूर्णांक है और संख्या 8764a529, 9 से पूर्णतः विभाज्य है, तो 17 (3a + 5) का मान ज्ञात कीजिए।</p>",
                    options_en: [
                        "<p>289</p>",
                        "<p>236</p>",
                        "<p>126</p>",
                        "<p>116</p>"
                    ],
                    options_hi: [
                        "<p>289</p>",
                        "<p>236</p>",
                        "<p>126</p>",
                        "<p>116</p>"
                    ],
                    solution_en: "<p>55.(a) <strong>Given: </strong>8764a529<br><strong>Divisibility of 9 :-</strong> The sum of all digits should be 9 or multiple of 9 then this number will be divisible by 9<br>8 + 7 + 6 + 4 + a + 5 + 2 + 9 = 41 + a <br>On putting the value of a = 4 then the number divisible by 9 <br>Now,<br>17 (3a + 5) = 17(3 &times; 4 + 5) = 289</p>",
                    solution_hi: "<p>55.(a) <strong>दिया है: </strong>8764a529<br><strong>9 की विभाजिता:-</strong> सभी अंकों का योग 9 या 9 का गुणक होना चाहिए, तो ये संख्या 9 से विभाज्य होगी।<br>8 + 7 + 6 + 4 + a + 5 + 2 + 9 = 41 + a <br>a का मान 4 रखने पर ,संख्या 9 से विभाज्य होगी <br>अब,<br>17 (3a + 5) = 17(3 &times; 4 + 5) = 289</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "56",
                    section: "17",
                    question_en: "<p>56. <math display=\"inline\"><mfrac><mrow><mi>P</mi></mrow><mrow><mi>q</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>19</mn><mn>5</mn></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>7</mn></mfrac></math> - 2.4 + (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>4</mn><mo>.</mo><mn>8</mn></mrow><mrow><mn>2</mn><mo>.</mo><mn>4</mn></mrow></mfrac></math>)<sup>2</sup> , where <math display=\"inline\"><mfrac><mrow><mi>P</mi></mrow><mrow><mi>q</mi></mrow></mfrac></math> is the simplest form, then find the sum of the digits of q</p>",
                    question_hi: "<p>56. यदि <math display=\"inline\"><mfrac><mrow><mi>P</mi></mrow><mrow><mi>q</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>19</mn><mn>5</mn></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>7</mn></mfrac></math> - 2.4 + (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>4</mn><mo>.</mo><mn>8</mn></mrow><mrow><mn>2</mn><mo>.</mo><mn>4</mn></mrow></mfrac></math>)<sup>2</sup> , जहाँ <math display=\"inline\"><mfrac><mrow><mi>P</mi></mrow><mrow><mi>q</mi></mrow></mfrac></math> सरलतम रूप है, तो q के अंकों का योग ज्ञात कीजिए।</p>",
                    options_en: [
                        "<p>5</p>",
                        "<p>6</p>",
                        "<p>8</p>",
                        "<p>7</p>"
                    ],
                    options_hi: [
                        "<p>5</p>",
                        "<p>6</p>",
                        "<p>8</p>",
                        "<p>7</p>"
                    ],
                    solution_en: "<p>56.(c) <br><math display=\"inline\"><mfrac><mrow><mi>P</mi></mrow><mrow><mi>q</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>19</mn><mn>5</mn></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>7</mn></mfrac></math> - 2.4 + (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>4</mn><mo>.</mo><mn>8</mn></mrow><mrow><mn>2</mn><mo>.</mo><mn>4</mn></mrow></mfrac></math>)<sup>2</sup><br>&rArr; <math display=\"inline\"><mfrac><mrow><mi>P</mi></mrow><mrow><mi>q</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>19</mn><mn>5</mn></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>7</mn></mfrac></math> - 2.4 + 4 <br>&rArr; <math display=\"inline\"><mfrac><mrow><mi>P</mi></mrow><mrow><mi>q</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>19</mn><mn>5</mn></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>7</mn></mfrac></math> + 1.6 <br>&rArr; <math display=\"inline\"><mfrac><mrow><mi>P</mi></mrow><mrow><mi>q</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>133</mn><mo>+</mo><mn>10</mn><mo>+</mo><mn>56</mn></mrow><mn>35</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>199</mn><mn>35</mn></mfrac></math><br>So, Sum of digit of q = 3 + 5 = 8</p>",
                    solution_hi: "<p>56.(c)<br><math display=\"inline\"><mfrac><mrow><mi>P</mi></mrow><mrow><mi>q</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>19</mn><mn>5</mn></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>7</mn></mfrac></math> - 2.4 + (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>4</mn><mo>.</mo><mn>8</mn></mrow><mrow><mn>2</mn><mo>.</mo><mn>4</mn></mrow></mfrac></math>)<sup>2</sup><br>&rArr; <math display=\"inline\"><mfrac><mrow><mi>P</mi></mrow><mrow><mi>q</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>19</mn><mn>5</mn></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>7</mn></mfrac></math> - 2.4 + 4 <br>&rArr; <math display=\"inline\"><mfrac><mrow><mi>P</mi></mrow><mrow><mi>q</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>19</mn><mn>5</mn></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>7</mn></mfrac></math> +&nbsp;1.6 <br>&rArr; <math display=\"inline\"><mfrac><mrow><mi>P</mi></mrow><mrow><mi>q</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>133</mn><mo>+</mo><mn>10</mn><mo>+</mo><mn>56</mn></mrow><mn>35</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>199</mn><mn>35</mn></mfrac></math><br>इसलिए, q के अंक का योग = 3 + 5 = 8</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "57",
                    section: "17",
                    question_en: "<p>57. If x is an integer such that x + <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mi>x</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>65</mn><mn>8</mn></mfrac></math>, then find the value of x - <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mi>x</mi></mrow></mfrac></math>.</p>",
                    question_hi: "<p>57. यदि x इस प्रकार एक पूर्णांक है कि x + <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mi>x</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>65</mn><mn>8</mn></mfrac></math> है, तो x - <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mi>x</mi></mrow></mfrac></math> का मान ज्ञात कीजिए।</p>",
                    options_en: [
                        "<p><math display=\"inline\"><mfrac><mrow><mn>63</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>63</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>36</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>32</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math></p>"
                    ],
                    options_hi: [
                        "<p><math display=\"inline\"><mfrac><mrow><mn>63</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>63</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>36</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>32</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math></p>"
                    ],
                    solution_en: "<p>57.(b) <br>x + <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mi>x</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>65</mn><mn>8</mn></mfrac></math><br>&rArr; x - <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mi>x</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mrow><mo>(</mo><mi>x</mi><mo>+</mo><mfrac><mn>1</mn><mi>x</mi></mfrac><mo>)</mo></mrow><mn>2</mn></msup><mo>-</mo><mn>4</mn></msqrt></math><br>= <math display=\"inline\"><msqrt><msup><mrow><mo>(</mo><mfrac><mrow><mn>65</mn></mrow><mrow><mn>8</mn></mrow></mfrac><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup><mo>-</mo><mn>4</mn></msqrt></math><br>= <math display=\"inline\"><msqrt><mfrac><mrow><mn>4225</mn></mrow><mrow><mn>64</mn></mrow></mfrac><mo>-</mo><mn>4</mn></msqrt></math> <br>= <math display=\"inline\"><msqrt><mfrac><mrow><mn>4225</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>256</mn></mrow><mrow><mn>64</mn></mrow></mfrac></msqrt></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mfrac><mn>3969</mn><mn>64</mn></mfrac></msqrt></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>63</mn><mn>8</mn></mfrac></math></p>",
                    solution_hi: "<p>57.(b) <br>x + <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mi>x</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>65</mn><mn>8</mn></mfrac></math><br>&rArr; x - <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mi>x</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mrow><mo>(</mo><mi>x</mi><mo>+</mo><mfrac><mn>1</mn><mi>x</mi></mfrac><mo>)</mo></mrow><mn>2</mn></msup><mo>-</mo><mn>4</mn></msqrt></math><br>= <math display=\"inline\"><msqrt><msup><mrow><mo>(</mo><mfrac><mrow><mn>65</mn></mrow><mrow><mn>8</mn></mrow></mfrac><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup><mo>-</mo><mn>4</mn></msqrt></math><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mfrac><mn>4225</mn><mn>64</mn></mfrac><mo>-</mo><mn>4</mn></msqrt></math> <br>= <math display=\"inline\"><msqrt><mfrac><mrow><mn>4225</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>256</mn></mrow><mrow><mn>64</mn></mrow></mfrac></msqrt></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mfrac><mn>3969</mn><mn>64</mn></mfrac></msqrt></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>63</mn><mn>8</mn></mfrac></math></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "58",
                    section: "17",
                    question_en: "<p>58. A can contains a mixture of two liquids P and Q in the ratio 8 : 7. When 5 Litre of the mixture are drawn off and the can is filled with Q, the ratio of P and Q becomes 2 : 3. How many litres of liquid P was contained by the can initially?</p>",
                    question_hi: "<p>58. एक कैन में दो तरल पदार्थ P और Q का मिश्रण 8 : 7 के अनुपात में है। यदि मिश्रण का 5 लीटर निकाल लिया जाए और कैन का शेष भाग Q से भर दिया जाए, तो P और Q का अनुपात 2 : 3 हो जाता है। ज्ञात कीजिए कि आरंभ में कैन में कितने लीटर तरल पदार्थ P था?</p>",
                    options_en: [
                        "<p><math display=\"inline\"><mfrac><mrow><mn>32</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> L</p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>28</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> L</p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>35</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> L</p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>25</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> L</p>"
                    ],
                    options_hi: [
                        "<p><math display=\"inline\"><mfrac><mrow><mn>32</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> L</p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>2</mn><mn>8</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> L</p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>35</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> L</p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>25</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> L</p>"
                    ],
                    solution_en: "<p>58.(a) <br>Let quantity of P and Q be 8x&nbsp;and 7x respectively,<br>5 litre of the mixture are drawn off<br>Then, mixture taken from liquid P = <math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>15</mn></mrow></mfrac></math> &times; 8 = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>8</mn><mn>3</mn></mfrac></math> ltr.<br>Mixture taken from liquid Q = <math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>15</mn></mrow></mfrac></math> &times; 7 = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>3</mn></mfrac></math> ltr.<br>According to the question,<br><math display=\"inline\"><mfrac><mrow><mn>8</mn><mi>x</mi><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mfrac><mrow><mn>8</mn></mrow><mrow><mn>3</mn></mrow></mfrac></mrow><mrow><mn>7</mn><mi>x</mi><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mfrac><mrow><mn>7</mn></mrow><mrow><mn>3</mn></mrow></mfrac><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>5</mn></mrow></mfrac></math> = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>3</mn></mfrac></math><br>&rArr; 24x - 8 = 14x + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>16</mn><mn>3</mn></mfrac></math><br>&rArr; 72x - 24 = 42x + 16<br>&rArr; 30x = 40<br>&rArr; x = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>3</mn></mfrac></math><br>So, initial quantity of liquid P = 8x&nbsp;<br>= 8 &times; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>3</mn></mfrac></math> = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>32</mn><mn>3</mn></mfrac></math> litre.</p>",
                    solution_hi: "<p>58.(a)<br>माना P और Q की मात्रा क्रमशः 8x&nbsp;और 7x है<br>5 लीटर मिश्रण को निकाला जाता है<br>फिर, तरल P से लिया गया मिश्रण = <math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>15</mn></mrow></mfrac></math> &times; 8 = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>8</mn><mn>3</mn></mfrac></math> लीटर<br>तरल Q से लिया गया मिश्रण = <math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>15</mn></mrow></mfrac></math> &times; 7 = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>3</mn></mfrac></math>लीटर<br>प्रश्न के अनुसार,<br><math display=\"inline\"><mfrac><mrow><mn>8</mn><mi>x</mi><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mfrac><mrow><mn>8</mn></mrow><mrow><mn>3</mn></mrow></mfrac></mrow><mrow><mn>7</mn><mi>x</mi><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mfrac><mrow><mn>7</mn></mrow><mrow><mn>3</mn></mrow></mfrac><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>5</mn></mrow></mfrac></math> = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>3</mn></mfrac></math><br>&rArr; 24x - 8 = 14x + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>16</mn><mn>3</mn></mfrac></math><br>&rArr; 72x - 24 = 42x + 16<br>&rArr; 30x = 40<br>&rArr; x = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>3</mn></mfrac></math><br>तो, तरल P की प्रारंभिक मात्रा = 8x&nbsp;<br>= 8 &times; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>3</mn></mfrac></math> = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>32</mn><mn>3</mn></mfrac></math>लीटर</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "59",
                    section: "17",
                    question_en: "<p>59. A can complete a piece of work in 20 days and B can complete the same work in 10 days. With the help of C, working together they completed the work in 5 days. The number of days in which C alone can complete the work is:</p>",
                    question_hi: "<p>59. A किसी काम को 20 दिनों में पूरा कर सकता है और B उसी काम को 10 दिनों में पूरा कर सकता है। C की मदद से, एक साथ मिलकर काम करते हुए उन्होंने 5 दिनों में वह काम पूरा किया। C अकेले उस काम को कितने दिनों में पूरा कर सकता है।</p>",
                    options_en: [
                        "<p>20</p>",
                        "<p>25</p>",
                        "<p>10</p>",
                        "<p>15</p>"
                    ],
                    options_hi: [
                        "<p>20</p>",
                        "<p>25</p>",
                        "<p>10</p>",
                        "<p>15</p>"
                    ],
                    solution_en: "<p>59.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740982624580.png\" alt=\"rId63\" width=\"183\" height=\"94\"><br>Efficiency of C = 4 - (2 + 1) = 1 unit<br>Time taken by C = <math display=\"inline\"><mfrac><mrow><mn>20</mn></mrow><mrow><mn>1</mn></mrow></mfrac></math> = 20 days</p>",
                    solution_hi: "<p>59.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740982624780.png\" alt=\"rId64\" width=\"195\" height=\"115\"><br>C की दक्षता = 4 - (2 + 1) = 1 इकाई<br>C द्वारा लिया गया समय = <math display=\"inline\"><mfrac><mrow><mn>20</mn></mrow><mrow><mn>1</mn></mrow></mfrac></math> = 20 दिन</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "60",
                    section: "17",
                    question_en: "<p>60. Two pipes, A and B, can fill a tank in 10 minutes and 20 minutes, respectively. The pipe C can empty the tank in 30 minutes. All the three pipes are opened at a time in the beginning. However, pipe C is closed 2 minutes before the tank is filled. In what time, will the tank be full (in minutes)?</p>",
                    question_hi: "<p>60. दो पाइप, A और B, एक टंकी को क्रमशः 10 मिनट और 20 मिनट में भर सकते हैं। पाइप C टंकी को 30 मिनट में खाली कर सकता है। प्रारंभ में तीनों पाइप एक साथ खोले जाते हैं। हालाँकि, टंकी भरने से 2 मिनट पहले पाइप C बंद कर दिया जाता है। कितने समय (मिनट में) में टंकी भर जाएगी?</p>",
                    options_en: [
                        "<p>12</p>",
                        "<p>10</p>",
                        "<p>8</p>",
                        "<p>6</p>"
                    ],
                    options_hi: [
                        "<p>12</p>",
                        "<p>10</p>",
                        "<p>8</p>",
                        "<p>6</p>"
                    ],
                    solution_en: "<p>60.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740982624959.png\" alt=\"rId65\" width=\"211\" height=\"112\"><br>Work done by pipe C for last 2 minutes = - 2 &times; 2 = -4 units<br>Work done by all pipes = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>60</mn><mo>-</mo><mn>4</mn></mrow><mrow><mn>6</mn><mo>+</mo><mn>3</mn><mo>-</mo><mn>2</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>56</mn><mn>7</mn></mfrac></math> = 8 units</p>",
                    solution_hi: "<p>60.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740982625081.png\" alt=\"rId66\" width=\"202\" height=\"108\"><br>पाइप C द्वारा पिछले 2 मिनट में किया गया कार्य = - 2 &times; 2 = - 4 इकाई<br>सभी पाइपों द्वारा किया गया कार्य = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>60</mn><mo>-</mo><mn>4</mn></mrow><mrow><mn>6</mn><mo>+</mo><mn>3</mn><mo>-</mo><mn>2</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>56</mn><mn>7</mn></mfrac></math> = 8 इकाई</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "61",
                    section: "17",
                    question_en: "<p>61.If sinA + cosA = <math display=\"inline\"><msqrt><mn>3</mn></msqrt></math>, then find the value of tanA + cotA + 2sinAcosA.</p>",
                    question_hi: "<p>61. यदि sinA + cosA = <math display=\"inline\"><msqrt><mn>3</mn></msqrt></math> है, तो tanA + cotA + 2sinAcosA का मान ज्ञात कीजिए।</p>",
                    options_en: [
                        "<p>6</p>",
                        "<p>2</p>",
                        "<p>1</p>",
                        "<p>3</p>"
                    ],
                    options_hi: [
                        "<p>6</p>",
                        "<p>2</p>",
                        "<p>1</p>",
                        "<p>3</p>"
                    ],
                    solution_en: "<p>61.(d) SinA + CosA = <math display=\"inline\"><msqrt><mn>3</mn></msqrt></math><br>Squaring on both side , we get <br>&rArr; 1 + 2 SinA CosA = 3<br>&rArr; SinA CosA = 1<br>tanA + cotA + 2sinAcosA = <math display=\"inline\"><mfrac><mrow><mi>S</mi><mi>i</mi><mi>n</mi><mi>A</mi></mrow><mrow><mi>C</mi><mi>o</mi><mi>s</mi><mi>A</mi></mrow></mfrac></math> + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>CosA</mi><mi>SinA</mi></mfrac></math> + 2sinAcosA&nbsp;<br>= <math display=\"inline\"><mfrac><mrow><msup><mrow><mi>S</mi><mi>i</mi><mi>n</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>A</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><msup><mrow><mi>C</mi><mi>o</mi><mi>s</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>A</mi></mrow><mrow><mi>C</mi><mi>o</mi><mi>s</mi><mi>A</mi><mi>S</mi><mi>i</mi><mi>n</mi><mi>A</mi></mrow></mfrac></math> + 2sinAcosA<br>= 1 + 2 = 3</p>",
                    solution_hi: "<p>61.(d) SinA + CosA = <math display=\"inline\"><msqrt><mn>3</mn></msqrt></math><br>दोनों पक्षो का वर्ग करने पर, हमें प्राप्त होता है<br>&rArr; 1 + 2 SinA CosA = 3<br>&rArr; SinA CosA = 1<br>tanA + cotA + 2sinAcosA = <math display=\"inline\"><mfrac><mrow><mi>S</mi><mi>i</mi><mi>n</mi><mi>A</mi></mrow><mrow><mi>C</mi><mi>o</mi><mi>s</mi><mi>A</mi></mrow></mfrac></math> + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>CosA</mi><mi>SinA</mi></mfrac></math> + 2sinAcosA&nbsp;<br>= <math display=\"inline\"><mfrac><mrow><msup><mrow><mi>S</mi><mi>i</mi><mi>n</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>A</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><msup><mrow><mi>C</mi><mi>o</mi><mi>s</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>A</mi></mrow><mrow><mi>C</mi><mi>o</mi><mi>s</mi><mi>A</mi><mi>S</mi><mi>i</mi><mi>n</mi><mi>A</mi></mrow></mfrac></math> + 2sinAcosA<br>= 1 + 2 = 3</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "62",
                    section: "17",
                    question_en: "<p>62. Two stations T1 and T2 300 km apart. A car starts from station T1 at 8 am and moves towards station T2 at a speed of 45 km/hr. and at 10 am another car starts from station T2 towards T1 at a speed of 60 km/hr. Then the cars will meet at :</p>",
                    question_hi: "<p>62. दो स्टेशन T1 और T2 एक-दूसरे से 300 km की दूरी पर है। एक कार स्टेशन T1 से 8 am पर चलना शुरू करती है और स्टेशन T2 की ओर 45 km/hr की चाल से चलती है। 10 am पर एक दूसरी कार स्टेशन T2 से T1 की ओर 60 km/hr की चाल से चलना शुरू करती है। दोनों कारें किस समय मिलेंगी ?</p>",
                    options_en: [
                        "<p>12:00 p.m.</p>",
                        "<p>11:00 a.m.</p>",
                        "<p>1:00 p.m.</p>",
                        "<p>10:00 a.m</p>"
                    ],
                    options_hi: [
                        "<p>12:00 p.m.</p>",
                        "<p>11:00 a.m.</p>",
                        "<p>1:00 p.m.</p>",
                        "<p>10:00 a.m</p>"
                    ],
                    solution_en: "<p>62.(a)<br>Distance covered by train T1 in 2 hours = 45 &times; 2 = 90 km<br>Remaining distance = 300 - 90 = 210 km<br>Relative speed = 45 + 60 = 105 km/h<br>Meeting time of the trains after 10 am = <math display=\"inline\"><mfrac><mrow><mn>210</mn></mrow><mrow><mn>105</mn></mrow></mfrac></math> = 2 hours<br>Required time = 10 + 2 = 12 : 00 p.m.</p>",
                    solution_hi: "<p>62.(a)<br>ट्रेन T1 द्वारा 2 घंटे में तय की गई दूरी = 45 &times; 2 = 90 km<br>शेष दूरी = 300 - 90 = 210 km<br>सापेक्ष गति = 45 + 60 = 105 km/h<br>सुबह 10 बजे के बाद ट्रेनों के मिलने का समय = <math display=\"inline\"><mfrac><mrow><mn>210</mn></mrow><mrow><mn>105</mn></mrow></mfrac></math> = 2 घंटे<br>आवश्यक समय = 10 + 2 = 12 : 00 p.m.</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "63",
                    section: "17",
                    question_en: "<p>63. If, in the quadrilateral JKLM, the measures of the angles J, K, L, and M are in the ratio 2 : 3 : 5 : 6, then the quadrilateral is a:</p>",
                    question_hi: "<p>63. यदि, चतुर्भुज JKLM में, कोण J, K, L और M की माप 2 : 3 : 5 : 6 के अनुपात में हैं, तो चतुर्भुज निम्नलिखित में से क्या है ?</p>",
                    options_en: [
                        "<p>Kite</p>",
                        "<p>parallelogram</p>",
                        "<p>trapezium</p>",
                        "<p>square</p>"
                    ],
                    options_hi: [
                        "<p>पतंग (Kite)</p>",
                        "<p>समांतर चतुर्भुज (parallelogram)</p>",
                        "<p>समलंब (trapezium)</p>",
                        "<p>वर्ग (square)</p>"
                    ],
                    solution_en: "<p>63.(c) <br>Ratio of the angle are 2 : 3 : 5 : 6<br>By checking option one by one ,<br><strong>Kite :-</strong> A kite has two pairs of equal adjacent angle , but here all angles are different <br><strong>Parallelogram:- </strong>Opposite angle in parallelogram would be same , but here all angles are different<br><strong>Square :- </strong>All four angles are equal to 90&deg; , but here all angles are different<br><strong>Trapezium :- </strong>Sum of two adjacent interior angle is 180&deg;<br>Here , angles are &rarr; 2x , 3x , 5x , 6x<br>We know that , <br>&rArr; 2x + 3x + 5x + 6x = 360<br>&rArr; x = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>360</mn><mn>16</mn></mfrac></math> = 22.5<br>So, sum of any two adjacent angle = 2x + 6x = 8 &times; 22.5 = 180&deg;<br>&there4;quadrilateral must be trapezium .</p>",
                    solution_hi: "<p>63.(c) <br>कोण का अनुपात क्रमश: <math display=\"inline\"><mo>&#8594;</mo></math> 2: 3: 5: 6 <br>एक - एक करके विकल्प की जाँच करने पर - <br><strong>पतंग :- </strong>एक पतंग में दो जोड़े समान कोण हैं, लेकिन यहां सभी कोण अलग हैं । <br><strong>समांतर चतुर्भुज:- </strong>समांतर चतुर्भुज में विपरीत कोण समान होगा, लेकिन यहां सभी कोण अलग-अलग हैं<br><strong>वर्ग:-</strong> सभी चार कोण 90 &deg; के बराबर हैं, लेकिन यहां सभी कोण अलग-अलग हैं<br><strong>समलंब चतुर्भुज:-</strong> किसी भी दो आसन्न कोण का योग = 180&deg;<br>यहाँ, कोण &rarr;&nbsp;2x, 3x, 5x, 6x <br>हम जानते हैं कि , <br>&rArr; 2x + 3x + 5x + 6x = 360<br>&rArr; x = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>360</mn><mn>16</mn></mfrac></math> = 22.5<br>तो, किसी भी दो आसन्न कोण का योग = 2x + 6x = 8 &times; 22.5 = 180&deg;<br>अतः , चतुर्भुज , समलंब चतुर्भुज हो सकता है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "64",
                    section: "17",
                    question_en: "<p>64. A closed cylindrical tank of radius 1.5 m and height 3 m is made from a sheet of metal,&nbsp;how much sheet of metal is required to do it?</p>",
                    question_hi: "<p>64. 1.5 मीटर त्रिज्या और 3 मीटर ऊँचाई वाली एक बंद बेलनाकार टंकी धातु की शीट से बनाई जाती है, इसे&nbsp;बनाने के लिए धातु की कितनी शीट की आवश्यकता होगी?</p>",
                    options_en: [
                        "<p>42.24 square metre</p>",
                        "<p>24.24 square metre</p>",
                        "<p>42.42 square metre</p>",
                        "<p>24.42 square metre</p>"
                    ],
                    options_hi: [
                        "<p>42.24 वर्ग मीटर</p>",
                        "<p>24.24 वर्ग मीटर</p>",
                        "<p>42.42 वर्ग मीटर</p>",
                        "<p>24.42 वर्ग मीटर</p>"
                    ],
                    solution_en: "<p>64.(c)<br>Cylindrical tank is closed <br>So, total surface area cylindrical tank = 2&pi;r(r+h)<br>2 &times; <math display=\"inline\"><mfrac><mrow><mn>22</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> &times; 1.5 (1.5 + 3) = 42.42 m<sup>2</sup><br>Hence, required metal used = 42.42 m<sup>2</sup></p>",
                    solution_hi: "<p>64.(c)<br>बेलनाकार टैंक बंद है <br>तो, बेलनाकार टैंक का कुल सतह क्षेत्रफल = 2&pi;r(r+h)<br>2 &times; <math display=\"inline\"><mfrac><mrow><mn>22</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> &times; 1.5 (1.5 + 3) = 42.42 m<sup>2</sup><br>अतः, प्रयुक्त आवश्यक धातु = 42.42 m<sup>2</sup></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "65",
                    section: "17",
                    question_en: "<p>65.In an election between two candidates, 75% of the eligible voters cast their votes and 6% of the votes cast were declared invalid. A candidate got 15228 votes, which were 90% of the total valid votes. Find the total number of eligible voters.</p>",
                    question_hi: "<p>65. दो प्रत्याशियों के बीच हुए चुनाव में, 75% पात्र मतदाताओं ने अपने मत दिए और दिए गए 6% मत अवैध घोषित कर दिए गए। एक प्रत्याशी को 15228 मत मिले, जो कुल वैध मतों का 90% थे। पात्र मतदाताओं की कुल संख्या ज्ञात कीजिए।</p>",
                    options_en: [
                        "<p>24850</p>",
                        "<p>27000</p>",
                        "<p>24000</p>",
                        "<p>25600</p>"
                    ],
                    options_hi: [
                        "<p>24850</p>",
                        "<p>27000</p>",
                        "<p>24000</p>",
                        "<p>25600</p>"
                    ],
                    solution_en: "<p>65.(c) Let the total votes be 100 %<br>Vote cast = 100 &times; <math display=\"inline\"><mfrac><mrow><mn>75</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 75 %<br>6% votes are invalid then total valid votes = 75 &times; <math display=\"inline\"><mfrac><mrow><mn>94</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 70.5 %<br>According to question,<br>&rArr; 70.5 % &times; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>90</mn><mn>100</mn></mfrac></math> = 15228 <br>&rArr; 100 % = 15228 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>100</mn><mrow><mn>90</mn><mo>&#215;</mo><mn>70</mn><mo>.</mo><mn>5</mn></mrow></mfrac></math> &times; 100 <br>= 24000</p>",
                    solution_hi: "<p>65.(c) माना कुल वोट = 100%<br>पड़े वोटो कि संख्या = 100 &times; <math display=\"inline\"><mfrac><mrow><mn>75</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 75 %<br>6% वोट अमान्य हैं तो कुल वैध वोट = 75 &times; <math display=\"inline\"><mfrac><mrow><mn>94</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 70.5 %<br>प्रश्न के अनुसार,<br>&rArr; 70.5 % &times; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>90</mn><mn>100</mn></mfrac></math> = 15228 <br>&rArr; 100 % = 15228 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>100</mn><mrow><mn>90</mn><mo>&#215;</mo><mn>70</mn><mo>.</mo><mn>5</mn></mrow></mfrac></math> &times; 100 <br>= 24000</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "66",
                    section: "17",
                    question_en: "<p>66. If x varies inversely as y<sup>3</sup> - 1 and is equal to 3 when y = 4, find x when y = 6.</p>",
                    question_hi: "<p>66. यदि y<sup>3</sup> - 1 का व्युत्क्रमानुपाती x है तथा y = 4 रखने पर x का मान 3 होता है, तो y = 6 रखने पर x का मान ज्ञात कीजिए।</p>",
                    options_en: [
                        "<p><math display=\"inline\"><mfrac><mrow><mn>189</mn></mrow><mrow><mn>215</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>191</mn></mrow><mrow><mn>215</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>190</mn></mrow><mrow><mn>216</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>189</mn></mrow><mrow><mn>216</mn></mrow></mfrac></math></p>"
                    ],
                    options_hi: [
                        "<p><math display=\"inline\"><mfrac><mrow><mn>189</mn></mrow><mrow><mn>215</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>191</mn></mrow><mrow><mn>215</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>190</mn></mrow><mrow><mn>216</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>189</mn></mrow><mrow><mn>216</mn></mrow></mfrac></math></p>"
                    ],
                    solution_en: "<p>66.(a) According to question , <br>&rArr;x &prop; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><msup><mi>y</mi><mn>3</mn></msup><mo>-</mo><mn>1</mn></mrow></mfrac></math><br>&rArr; x = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>k</mi><mrow><msup><mi>y</mi><mn>3</mn></msup><mo>-</mo><mn>1</mn></mrow></mfrac></math><br>Here x = 3 and y = 4<br>&rArr; 3 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>k</mi><mrow><msup><mn>4</mn><mn>3</mn></msup><mo>-</mo><mn>1</mn></mrow></mfrac></math><br>&rArr; k = 63 &times; 3 = 189<br><math display=\"inline\"><mo>&#8756;</mo></math>if y = 6 , x = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>189</mn><mrow><msup><mn>6</mn><mn>3</mn></msup><mo>-</mo><mn>1</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>189</mn><mn>215</mn></mfrac></math></p>",
                    solution_hi: "<p>66.(a) प्रश्न के अनुसार, <br>&rArr; x &prop;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><msup><mi>y</mi><mn>3</mn></msup><mo>-</mo><mn>1</mn></mrow></mfrac></math><br>&rArr; x = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>k</mi><mrow><msup><mi>y</mi><mn>3</mn></msup><mo>-</mo><mn>1</mn></mrow></mfrac></math><br>यहाँ x = 3 और y = 4<br>&rArr; 3 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>k</mi><mrow><msup><mn>4</mn><mn>3</mn></msup><mo>-</mo><mn>1</mn></mrow></mfrac></math><br>&rArr; k = 63 &times; 3 = 189<br><math display=\"inline\"><mo>&#8756;</mo></math>यदि y = 6 , x = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>189</mn><mrow><msup><mn>6</mn><mn>3</mn></msup><mo>-</mo><mn>1</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>189</mn><mn>215</mn></mfrac></math></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "67",
                    section: "17",
                    question_en: "<p>67. My little sister is filling a conical bag with gems. She knows that the capacity of each bag is 75.36 cubic inches. Find the slant height of the cone, upto three decimal places, if the diameter is 6 inches. (Take &pi;&nbsp;= 3.14)</p>",
                    question_hi: "<p>67. मेरी छोटी बहन एक शंक्वाकार थैले को कंचों से भर रही है। वह जानती है कि प्रत्येक बैग की धारिता 75.36 घन इंच है। यदि शंकु का व्यास 6 इंच है, तो दशमलव के तीन स्थान तक उसकी तिर्यक ऊँचाई ज्ञात कीजिए। ( &pi; = 3.14 लीजिए)</p>",
                    options_en: [
                        "<p>8.455 inches</p>",
                        "<p>8.544 inches</p>",
                        "<p>8.454 inches</p>",
                        "<p>8.545 inches</p>"
                    ],
                    options_hi: [
                        "<p>8.455 इंच</p>",
                        "<p>8.544 इंच</p>",
                        "<p>8.454 इंच</p>",
                        "<p>8.545 इंच</p>"
                    ],
                    solution_en: "<p>67.(b) <br>Volume of cone = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math>&pi;r<sup>2</sup>h<br>&rArr; 75.36 = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math> &times; 3.14 &times; 3 &times; 3 &times; h <br>&rArr; h = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>75</mn><mo>.</mo><mn>36</mn></mrow><mrow><mn>3</mn><mo>.</mo><mn>14</mn><mo>&#215;</mo><mn>3</mn></mrow></mfrac></math> = 8 inches<br>Now,<br>Slant height of cone = <math display=\"inline\"><msqrt><msup><mrow><mi>r</mi></mrow><mrow><mn>2</mn></mrow></msup><mo>+</mo><msup><mrow><mi>h</mi></mrow><mrow><mn>2</mn></mrow></msup></msqrt></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mn>3</mn><mn>2</mn></msup><mo>+</mo><msup><mn>8</mn><mn>2</mn></msup></msqrt></math> <br>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>9</mn><mo>+</mo><mn>64</mn></msqrt></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>73</mn></msqrt></math> = 8.544 inches</p>",
                    solution_hi: "<p>67.(b) शंकु की आयतन = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math>&pi;r<sup>2</sup>h<br>&rArr; 75.36 = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math> &times; 3.14 &times; 3 &times; 3 &times; h <br>&rArr; h = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>75</mn><mo>.</mo><mn>36</mn></mrow><mrow><mn>3</mn><mo>.</mo><mn>14</mn><mo>&#215;</mo><mn>3</mn></mrow></mfrac></math> = 8 इन्च<br>अब,<br>शंकु की तिरछी ऊंचाई = <math display=\"inline\"><msqrt><msup><mrow><mi>r</mi></mrow><mrow><mn>2</mn></mrow></msup><mo>+</mo><msup><mrow><mi>h</mi></mrow><mrow><mn>2</mn></mrow></msup></msqrt></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mn>3</mn><mn>2</mn></msup><mo>+</mo><msup><mn>8</mn><mn>2</mn></msup></msqrt></math> <br>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>9</mn><mo>+</mo><mn>64</mn></msqrt></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>73</mn></msqrt></math> = 8.544 इन्च</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "68",
                    section: "17",
                    question_en: "<p>68. By selling an article at <math display=\"inline\"><mfrac><mrow><mn>6</mn></mrow><mrow><mn>14</mn></mrow></mfrac></math> of its actual selling price, Sharad incurs a loss of 12%. If he sells it at 90% of its actual selling price, then the profit percentage is:</p>",
                    question_hi: "<p>68. किसी वस्तु को इसके मूल विक्रय मूल्य के <math display=\"inline\"><mfrac><mrow><mn>6</mn></mrow><mrow><mn>14</mn></mrow></mfrac></math> पर बेचने पर शरद को 12% की हानि होती है। यदि वह इसे मूल विक्रय मूल्य के 90% पर बेचता है, तो लाभ प्रतिशत ज्ञात कीजिए।</p>",
                    options_en: [
                        "<p>85.9%</p>",
                        "<p>85.4%</p>",
                        "<p>87.9%</p>",
                        "<p>84.8%</p>"
                    ],
                    options_hi: [
                        "<p>85.9%</p>",
                        "<p>85.4%</p>",
                        "<p>87.9%</p>",
                        "<p>84.8%</p>"
                    ],
                    solution_en: "<p>68.(d) Let the actual selling price of the article be x.<br>If Sharad sells it at <math display=\"inline\"><mfrac><mrow><mn>6</mn></mrow><mrow><mn>14</mn></mrow></mfrac></math> of its actual selling price, the S.P. = <math display=\"inline\"><mfrac><mrow><mn>6</mn></mrow><mrow><mn>14</mn></mrow></mfrac></math>x&nbsp;<br>At this selling price, Sharad incurs a loss of 12%.<br>C.P. = S.P. &times; (<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>100</mn><mrow><mn>100</mn><mo>-</mo><mi>Loss</mi><mi mathvariant=\"normal\">&#160;</mi><mi>Percentage</mi></mrow></mfrac></math>) <br>C.P. = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>6</mn><mn>14</mn></mfrac></math>x &times; (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>100</mn><mrow><mn>100</mn><mo>-</mo><mn>12</mn></mrow></mfrac></math>) = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>7</mn></mfrac></math>x &times; (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>100</mn><mn>88</mn></mfrac></math>) = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>75</mn><mi>x</mi></mrow><mn>154</mn></mfrac></math><br>If Sharad sells the article at 90% of its actual selling price, the new S.P. = <math display=\"inline\"><mn>0</mn><mo>.</mo><mn>9</mn></math>x <br>Profit Percentage = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>new</mi><mi mathvariant=\"normal\">&#160;</mi><mi mathvariant=\"normal\">S</mi><mo>.</mo><mi mathvariant=\"normal\">P</mi><mo>.</mo><mo>-</mo><mi mathvariant=\"normal\">C</mi><mo>.</mo><mi mathvariant=\"normal\">P</mi><mo>.</mo></mrow><mrow><mi mathvariant=\"normal\">C</mi><mo>.</mo><mi mathvariant=\"normal\">P</mi><mo>.</mo><mi mathvariant=\"normal\">&#160;</mi></mrow></mfrac></math> &times; 100 <br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>0</mn><mo>.</mo><mn>9</mn><mi>x</mi><mo>-</mo><mfrac><mrow><mn>75</mn><mi>x</mi></mrow><mn>154</mn></mfrac></mrow><mfrac><mrow><mn>75</mn><mi>x</mi></mrow><mn>154</mn></mfrac></mfrac></math> &times; 100</p>\n<p>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>63</mn><mo>.</mo><mn>6</mn><mi>x</mi></mrow><mrow><mn>75</mn><mi>x</mi></mrow></mfrac></math> &times; 100<br>Profit Percentage = <math display=\"inline\"><mfrac><mrow><mn>63600</mn></mrow><mrow><mn>750</mn></mrow></mfrac></math> = 84.8%</p>",
                    solution_hi: "<p>68.(d) मान लीजिए, वस्तु का वास्तविक विक्रय मूल्य = x<br>शरद वस्तु को उसके वास्तविक विक्रय मूल्य के <math display=\"inline\"><mfrac><mrow><mn>6</mn></mrow><mrow><mn>14</mn></mrow></mfrac></math>​ पर बेचता है, तो विक्रय मूल्य = <math display=\"inline\"><mfrac><mrow><mn>6</mn></mrow><mrow><mn>14</mn></mrow></mfrac></math>x&nbsp;<br>इस कीमत पर बेचने पर शरद को 12% का नुकसान होता है<br>क्रय मूल्य = वि. मू. &times; (<math style=\"font-family: Verdana;\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>100</mn><mrow><mn>100</mn><mo>-</mo><mi>&#2361;&#2366;&#2344;&#2367;</mi><mo>&#160;</mo><mi>&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</mi></mrow></mfrac></math>)&nbsp;<br>क्रय मूल्य = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>6</mn><mn>14</mn></mfrac></math>x &times; (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>100</mn><mrow><mn>100</mn><mo>-</mo><mn>12</mn></mrow></mfrac></math>) = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>7</mn></mfrac></math>x &times; (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>100</mn><mn>88</mn></mfrac></math>) = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>75</mn><mi>x</mi></mrow><mn>154</mn></mfrac></math><br>यदि शरद वस्तु को उसके वास्तविक विक्रय मूल्य के 90% पर बेचता है, तो नया विक्रय मूल्य = <math display=\"inline\"><mn>0</mn><mo>.</mo><mn>9</mn></math>x <br>लाभ प्रतिशत = <math style=\"font-family: Verdana;\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2344;&#2351;&#2366;</mi><mi mathvariant=\"normal\">&#160;</mi><mi>&#2357;&#2367;&#2325;&#2381;&#2352;&#2351;</mi><mi mathvariant=\"normal\">&#160;</mi><mi>&#2350;&#2370;&#2354;&#2381;&#2351;</mi><mo>-</mo><mi>&#2325;&#2381;&#2352;&#2351;</mi><mi mathvariant=\"normal\">&#160;</mi><mi>&#2350;&#2370;&#2354;&#2381;&#2351;</mi></mrow><mrow><mi>&#2325;&#2381;&#2352;&#2351;</mi><mi mathvariant=\"normal\">&#160;</mi><mi>&#2350;&#2370;&#2354;&#2381;&#2351;</mi></mrow></mfrac></math> &times; 100 <br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>0</mn><mo>.</mo><mn>9</mn><mi>x</mi><mo>-</mo><mfrac><mrow><mn>75</mn><mi>x</mi></mrow><mn>154</mn></mfrac></mrow><mfrac><mrow><mn>75</mn><mi>x</mi></mrow><mn>154</mn></mfrac></mfrac></math> &times; 100</p>\n<p>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>63</mn><mo>.</mo><mn>6</mn><mi>x</mi></mrow><mrow><mn>75</mn><mi>x</mi></mrow></mfrac></math> &times; 100<br>लाभ प्रतिशत = <math display=\"inline\"><mfrac><mrow><mn>63600</mn></mrow><mrow><mn>750</mn></mrow></mfrac></math> = 84.8%</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "69",
                    section: "17",
                    question_en: "<p>69. If a shopkeeper sells sugar at ₹44.8 per kg, he is able to make a 12% profit. Due to water seepage, <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>5</mn></mfrac></math> of the sugar is damaged. What should now be the selling price per kg of the rest of the sugar to have a 5% profit?</p>",
                    question_hi: "<p>69. यदि एक दुकानदार चीनी को ₹44.8 प्रति kg की दर से बेचता है, तो वह 12% लाभ अर्जित करता है। पानी के रिसाव के कारण चीनी का <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>5</mn></mfrac></math>&nbsp;हिस्सा खराब हो जाता है। 5% लाभ प्राप्त करने के लिए शेष चीनी का प्रति किलोग्राम विक्रय मूल्य क्या होना चाहिए?</p>",
                    options_en: [
                        "<p>₹49.5</p>",
                        "<p>₹52.5</p>",
                        "<p>₹48.5</p>",
                        "<p>₹51.8</p>"
                    ],
                    options_hi: [
                        "<p>₹49.5</p>",
                        "<p>₹52.5</p>",
                        "<p>₹48.5</p>",
                        "<p>₹51.8</p>"
                    ],
                    solution_en: "<p>69.(b) SP of 1 kg sugar = ₹44.8 <br>CP of 1 kg sugar = ₹44.8 &times; <math display=\"inline\"><mfrac><mrow><mn>100</mn></mrow><mrow><mn>112</mn></mrow></mfrac></math> = ₹40 <br>According to the question,<br>C.P.Remaining sugar = ₹40 &times; <math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> = ₹50 <br>New SP of 1 kg sugar after profit of 5% = ₹ 50 &times; <math display=\"inline\"><mfrac><mrow><mn>105</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = ₹52.5</p>",
                    solution_hi: "<p>69.(b) 1 किलो चीनी का विक्रय मूल्य = ₹44.8<br>1 किलो चीनी का क्रय मूल्य = ₹44.8 &times; <math display=\"inline\"><mfrac><mrow><mn>100</mn></mrow><mrow><mn>112</mn></mrow></mfrac></math> = ₹40 <br>प्रश्न के अनुसार,<br>शेष चीनी का क्रय मूल्य = ₹40 &times; <math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> = ₹50 <br>5% लाभ के बाद 1 किलो चीनी का नया विक्रय मूल्य = ₹ 50 &times; <math display=\"inline\"><mfrac><mrow><mn>105</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = ₹52.5</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "70",
                    section: "17",
                    question_en: "<p>70. A number when divided by a divisor leaves a remainder of 42. When thrice the original number is divided by the same divisor, the remainder is 74. What is the value of the divisor?</p>",
                    question_hi: "<p>70. किसी संख्या को एक भाजक से विभाजित करने पर शेषफल 42 प्राप्त होता है। मूल संख्या के तीन गुनी संख्या को उसी भाजक से विभाजित करने पर शेषफल 74 प्राप्त होता है। भाजक का मान ज्ञात कीजिए।</p>",
                    options_en: [
                        "<p>55</p>",
                        "<p>54</p>",
                        "<p>53</p>",
                        "<p>52</p>"
                    ],
                    options_hi: [
                        "<p>55</p>",
                        "<p>54</p>",
                        "<p>53</p>",
                        "<p>52</p>"
                    ],
                    solution_en: "<p>70.(d) <br>Let original number be x<br>Divisor = y and quotient = q<br>As, dividend = divisor &times; quotient + remainder<br>According to the question,<br>When, no. is divided by a divisor leaves a remainder of 42<br>x = yq + 42 -------(i)<br>When, thrice of the same number is divided by same divisor<br>3x = 3yq + 126 -------(ii)<br>Here, 3yd is completely divisible by divisor (d)<br>When, we divide 126 by divisor (d) remainder is 74<br>&rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>126</mn><mi>divisor</mi></mfrac></math> = 74<br>&rArr; divisor = rem.(<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>126</mn><mn>74</mn></mfrac></math>) = 52 <br><strong>Shortcut trick :-</strong> <br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mi>st</mi><mi mathvariant=\"normal\">&#160;</mi><mi>reminder</mi><mo>&#215;</mo><mn>3</mn></mrow><mi>divisor</mi></mfrac></math> = 74<br>&rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>42</mn><mo>&#215;</mo><mn>3</mn></mrow><mi>divisor</mi></mfrac></math> = 74<br>&rArr; divisor = rem.(<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>126</mn><mn>74</mn></mfrac></math>) = 52</p>",
                    solution_hi: "<p>70.(d) <br>माना , मूल संख्या = x <br>भाजक = y और भागफल = q<br>भाज्य = भाजक &times; भागफल + शेषफल<br>प्रश्न के अनुसार,<br>जब, संख्या को भाजक से विभाजित किया जाता है तो 42 शेष बचता है<br>x = yq + 42 -------(i)<br>जब, संख्या के तीन गुने को समान भाजक द्वारा विभाजित किया जाता है<br>3x = 3yq + 126 -------(ii)<br>यहाँ, 3yd , भाजक (d) से पूरी तरह विभाजित है<br>जब, हम 126 को भाजक (d) से विभाजित करते है तो शेषफल = 74<br>&rArr; <math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>126</mn><mi>&#2349;&#2366;&#2332;&#2325;</mi></mfrac></math> = 74<br>&rArr; भाजक = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>126</mn><mn>74</mn></mfrac></math>= शेषफल (52)<br><strong>शॉर्ट ट्रिक:-</strong> <br><math style=\"font-family:Verdana\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2346;&#2361;&#2354;&#2368;</mi><mi mathvariant=\"normal\">&#160;</mi><mi>&#2358;&#2375;&#2359;</mi><mo>&#160;</mo><mo>&#215;</mo><mn>3</mn></mrow><mrow><mi>&#2349;&#2366;&#2332;&#2325;</mi><mi mathvariant=\"normal\">&#160;</mi></mrow></mfrac></math> = 74<br>&rArr; <math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>42</mn><mo>&#215;</mo><mn>3</mn></mrow><mi>&#2349;&#2366;&#2332;&#2325;</mi></mfrac></math> = 74<br>&rArr; भाजक = शेष(<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>126</mn><mn>74</mn></mfrac></math>) = 52</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "71",
                    section: "17",
                    question_en: "<p>71. A sum, when invested at 20% simple interest per annum, amounts to ₹2160 after 3 years. What is the simple interest (in ₹) on the same sum at the same rate in 1 year?</p>",
                    question_hi: "<p>71. एक मूलधन, जब प्रति वर्ष 20% साधारण ब्याज पर निवेश किया जाता है, तो 3 वर्षों के बाद उसका मिश्रधन ₹2160 हो जाता है। 1 वर्ष में समान दर पर समान मूलधन पर साधारण ब्याज (₹ में) कितना है?</p>",
                    options_en: [
                        "<p>270</p>",
                        "<p>540</p>",
                        "<p>1080</p>",
                        "<p>135</p>"
                    ],
                    options_hi: [
                        "<p>270</p>",
                        "<p>540</p>",
                        "<p>1080</p>",
                        "<p>135</p>"
                    ],
                    solution_en: "<p>71.(a) Total simple interest after 3 years = 3 &times; 20% = 60% <br>Let initial amount = ₹100<br>Amount after 3 years = 100 &times; <math display=\"inline\"><mfrac><mrow><mn>160</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = ₹160<br>&rArr; ₹160 = ₹2160<br>&rArr; ₹100 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2160</mn><mo>&#215;</mo><mn>100</mn></mrow><mn>160</mn></mfrac></math> = ₹1350<br>&there4; Simple interest on the same sum at the same rate in 1 year<br>= ₹1350 &times; <math display=\"inline\"><mfrac><mrow><mn>20</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = ₹270</p>",
                    solution_hi: "<p>71.(a) <br>3 वर्ष के बाद कुल साधरण ब्याज = 3 &times; 20% = 60% <br>माना प्रारंभिक राशि = ₹100<br>3 साल के बाद राशि = 100 &times; <math display=\"inline\"><mfrac><mrow><mn>160</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = ₹160<br>&rArr; ₹160 = ₹2160<br>&rArr; ₹100 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2160</mn><mo>&#215;</mo><mn>100</mn></mrow><mn>160</mn></mfrac></math> = ₹1350<br>&there4; 1 वर्ष में समान दर और समान राशि पर साधारण ब्याज,<br>= ₹1350 &times; <math display=\"inline\"><mfrac><mrow><mn>20</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = ₹270</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "72",
                    section: "17",
                    question_en: "<p>72. Manish\'s average earning per month in the first three months of a year was ₹8784. In April, his earning was 25% more than the average earning in the first three months. If his average earning per month for the whole year is ₹99085, then what will be Manish\'s average earning (in ₹) per month from May to December?</p>",
                    question_hi: "<p>72. वर्ष के प्रथम तीन महीनों में मनीष की प्रति माह औसत आय ₹8784 थी। अप्रैल में उसकी आय, प्रथम तीन महीनों की औसत आय से 25% अधिक थी। यदि पूरे वर्ष के लिए उसकी प्रति माह औसत आय ₹99085 है, तो मई से दिसंबर तक मनीष की प्रति माह औसत आय (₹ में) कितनी होगी?</p>",
                    options_en: [
                        "<p>143958</p>",
                        "<p>143961</p>",
                        "<p>143962</p>",
                        "<p>143960</p>"
                    ],
                    options_hi: [
                        "<p>143958</p>",
                        "<p>143961</p>",
                        "<p>143962</p>",
                        "<p>143960</p>"
                    ],
                    solution_en: "<p>72.(b)<br>Sum of first three months = 8784 &times; 3 = ₹26352<br>His April month earning = 8784 &times; <math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> = ₹10980<br>Sum of the money of May to December <br>= 99085 &times; 12 - (26352 + 10980) <br>= 1189020- 37332 = ₹1151688<br>Required Average = <math display=\"inline\"><mfrac><mrow><mn>1151688</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math> = ₹143961</p>",
                    solution_hi: "<p>72.(b)<br>पहले तीन महीनों का योग = 8784 &times; 3 = ₹26352<br>अप्रैल महीने की कमाई = 8784 &times; <math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> = ₹10,980<br>मई से दिसंबर का योग = 99085 &times; 12 - (26352 + 10980) <br>= 1189020- 37332 = ₹11,51,688<br>आवश्यक औसत = <math display=\"inline\"><mfrac><mrow><mn>1151688</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math> = ₹14,3961</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "73",
                    section: "17",
                    question_en: "<p>73. Simplify the following :<br>{( -1.5 ) &times; ( - 8.2) - 3.3 } (4.2)<sup>2</sup></p>",
                    question_hi: "<p>73. निम्नलिखित को सरल कीजिए:<br>{( -1.5 ) &times; ( - 8.2) - 3.3 } (4.2)<sup>2</sup></p>",
                    options_en: [
                        "<p>155.76</p>",
                        "<p>158</p>",
                        "<p>158.76</p>",
                        "<p>155</p>"
                    ],
                    options_hi: [
                        "<p>155.76</p>",
                        "<p>158</p>",
                        "<p>158.76</p>",
                        "<p>155</p>"
                    ],
                    solution_en: "<p>73.(c) <br>{( -1.5 ) &times; ( - 8.2) - 3.3 } (4.2)<sup>2</sup><br>= {12.3 - 3.3} &times; 17.64<br>= 9 &times; 17.64 <br>= 158.76</p>",
                    solution_hi: "<p>73.(c) <br>{( -1.5 ) &times; ( - 8.2) - 3.3 } (4.2)<sup>2</sup><br>= {12.3 - 3.3} &times; 17.64<br>= 9 &times; 17.64 <br>= 158.76</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "74",
                    section: "17",
                    question_en: "<p>74. The given table shows the number of candidates appeared in an interview for different posts (A, B, C, D, E and F). What is the average number of candidates appearing for the posts C, D, E and F?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740982625240.png\" alt=\"rId67\" width=\"227\" height=\"151\"></p>",
                    question_hi: "<p>74. दी गई तालिका विभिन्न पदों (A, B, C, D, E और F) के लिए साक्षात्कार में उपस्थित अभ्यर्थियों की संख्या को दर्शाती है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740982625360.png\" alt=\"rId68\" width=\"164\" height=\"150\"> <br>C, D, E और F पदों के लिए उपस्थित होने वाले अभ्यर्थियों की औसत संख्या कितनी है?</p>",
                    options_en: [
                        "<p>7600</p>",
                        "<p>8000</p>",
                        "<p>7225</p>",
                        "<p>7500</p>"
                    ],
                    options_hi: [
                        "<p>7600</p>",
                        "<p>8000</p>",
                        "<p>7225</p>",
                        "<p>7500</p>"
                    ],
                    solution_en: "<p>74.(c) <br>Average of number of candidates who appeared <br>= <math display=\"inline\"><mfrac><mrow><mn>8600</mn><mo>+</mo><mn>9900</mn><mo>+</mo><mn>5300</mn><mo>+</mo><mn>5100</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> <br>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>28900</mn><mn>4</mn></mfrac></math> <br>= 7225</p>",
                    solution_hi: "<p>74.(c) <br>उपस्थित होने वाले अभ्यर्थियों की औसत संख्या <br>= <math display=\"inline\"><mfrac><mrow><mn>8600</mn><mo>+</mo><mn>9900</mn><mo>+</mo><mn>5300</mn><mo>+</mo><mn>5100</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> <br>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>28900</mn><mn>4</mn></mfrac></math> <br>= 7225</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "75",
                    section: "7",
                    question_en: "<p>75. Find the value of tan 72&deg; - tan 27&deg; - tan 72&deg; tan 27&deg;.</p>",
                    question_hi: "<p>75. tan 72&deg; - tan 27&deg; - tan 72&deg; tan 27&deg; का मान ज्ञात कीजिए।</p>",
                    options_en: [
                        "<p>-1</p>",
                        "<p>1</p>",
                        "<p>-2</p>",
                        "<p>0</p>"
                    ],
                    options_hi: [
                        "<p>-1</p>",
                        "<p>1</p>",
                        "<p>-2</p>",
                        "<p>0</p>"
                    ],
                    solution_en: "<p>75.(b)<br>tan72&deg; - tan27&deg; - tan72&deg; tan27&deg;<br>= tan 72&deg; - tan72&deg; tan27&deg; - tan27&deg;<br>= tan 72&deg; (1 - tan27&deg;) - tan27&deg;<br>= tan(45&deg; + 27&deg;)(1 - tan27&deg;) - tan27&deg;<br>= [<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>tan</mi><mn>45</mn><mo>&#176;</mo><mo>+</mo><mi>tan</mi><mn>27</mn><mo>&#176;</mo></mrow><mrow><mn>1</mn><mo>-</mo><mi>tan</mi><mn>45</mn><mo>&#176;</mo><mi>tan</mi><mn>27</mn><mo>&#176;</mo></mrow></mfrac></math>](1 - tan27&deg;) - tan27&deg;<br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>1</mn><mo>+</mo><mi>tan</mi><mn>27</mn><mo>&#176;</mo><mo>)</mo></mrow><mrow><mo>(</mo><mn>1</mn><mo>-</mo><mi>tan</mi><mn>27</mn><mo>&#176;</mo><mo>)</mo></mrow></mfrac></math>(1 - tan27&deg;) - tan27&deg;<br>= 1 + tan27&deg; - tan27&deg;<br>= 1</p>",
                    solution_hi: "<p>75.(b)<br>tan72&deg; - tan27&deg; - tan72&deg; tan27&deg;<br>= tan 72&deg; - tan72&deg; tan27&deg; - tan27&deg;<br>= tan 72&deg; (1 - tan27&deg;) - tan27&deg;<br>= tan(45&deg; + 27&deg;)(1 - tan27&deg;) - tan27&deg;<br>= [<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>tan</mi><mn>45</mn><mo>&#176;</mo><mo>+</mo><mi>tan</mi><mn>27</mn><mo>&#176;</mo></mrow><mrow><mn>1</mn><mo>-</mo><mi>tan</mi><mn>45</mn><mo>&#176;</mo><mi>tan</mi><mn>27</mn><mo>&#176;</mo></mrow></mfrac></math>](1 - tan27&deg;) - tan27&deg;<br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>1</mn><mo>+</mo><mi>tan</mi><mn>27</mn><mo>&#176;</mo><mo>)</mo></mrow><mrow><mo>(</mo><mn>1</mn><mo>-</mo><mi>tan</mi><mn>27</mn><mo>&#176;</mo><mo>)</mo></mrow></mfrac></math>(1 - tan27&deg;) - tan27&deg;<br>= 1 + tan27&deg; - tan27&deg;<br>= 1</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "76",
                    section: "7",
                    question_en: "<p>76. Choose the one which can be substituted for the given words/ sentence : <br>A boat or ship for conveying passengers and goods, especially over a relatively short distance and as a regular service</p>",
                    question_hi: "<p>76. Choose the one which can be substituted for the given words/ sentence : <br>A boat or ship for conveying passengers and goods, especially over a relatively short distance and as a regular service</p>",
                    options_en: [
                        "<p>Dinghy</p>",
                        "<p>Ferry</p>",
                        "<p>Freighter</p>",
                        "<p>Submarine</p>"
                    ],
                    options_hi: [
                        "<p>Dinghy</p>",
                        "<p>Ferry</p>",
                        "<p>Freighter</p>",
                        "<p>Submarine</p>"
                    ],
                    solution_en: "<p>76.(b) Ferry<br><strong>Ferry- </strong>A boat or ship for conveying passengers and goods, especially over a relatively short distance and as a regular service <br><strong>Dinghy- </strong>a small boat that you sail <br><strong>Freighter- </strong>a ship or an aircraft that carries only goods and not passengers <br><strong>Submarine- </strong>a type of ship that can travel under the water as well as on the surface</p>",
                    solution_hi: "<p>76.(b) Ferry<br><strong>Ferry(</strong>नौका)- A boat or ship for conveying passengers and goods, especially over a relatively short distance and as a regular service <br><strong>Dinghy</strong>(छोटी एवं खुली नाव)- a small boat that you sail <br><strong>Freighter</strong>( जहाज़ लादनेवाला )- a ship or an aircraft that carries only goods and not passengers <br><strong>Submarine</strong>(पनडुब्बी)- a type of ship that can travel under the water as well as on the surface</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "77",
                    section: "7",
                    question_en: "<p>77. Identify the segment in the sentence which contains grammatical error. Mark &lsquo;no error&rsquo; in case the sentence given is correct. <br>It was apparent to everyone present that he would die if he does not receive timely help.</p>",
                    question_hi: "<p>77. Identify the segment in the sentence which contains grammatical error. Mark &lsquo;no error&rsquo; in case the sentence given is correct. <br>It was apparent to everyone present that he would die if he does not receive timely help.</p>",
                    options_en: [
                        "<p>It was apparent to everyone present</p>",
                        "<p>that he would die</p>",
                        "<p>if he does not receive timely help.</p>",
                        "<p>No error</p>"
                    ],
                    options_hi: [
                        "<p>It was apparent to everyone present</p>",
                        "<p>that he would die</p>",
                        "<p>if he does not receive timely help.</p>",
                        "<p>No error</p>"
                    ],
                    solution_en: "<p>77.(c) if he does not receive timely help.<br>The given sentence is in the simple past tense so it must have the verb in the past form. Hence, &lsquo;If he didn&rsquo;t receive timely help&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>77.(c) if he does not receive timely help.<br>दिया गया वाक्य simple past tense में है इसलिए verb का past form होना चाहिए। इसलिए, &lsquo;If he didn&rsquo;t receive timely help&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "78",
                    section: "7",
                    question_en: "<p>78. Identify the best way to improve the underlined part of the given sentence. If there is no improvement required, select &lsquo;no improvement&rsquo;.<br><span style=\"text-decoration: underline;\"><strong>No sooner I saw</strong></span> the tiger than I ran away.</p>",
                    question_hi: "<p>78. Identify the best way to improve the underlined part of the given sentence. If there is no improvement required, select &lsquo;no improvement&rsquo;.<br><span style=\"text-decoration: underline;\"><strong>No sooner I saw</strong></span> the tiger than I ran away.</p>",
                    options_en: [
                        "<p>No sooner I had seen</p>",
                        "<p>No sooner did I see</p>",
                        "<p>As soon as I saw</p>",
                        "<p>No improvement</p>"
                    ],
                    options_hi: [
                        "<p>No sooner I had seen</p>",
                        "<p>No sooner did I see</p>",
                        "<p>As soon as I saw</p>",
                        "<p>No improvement</p>"
                    ],
                    solution_en: "<p>78.(b) No sooner did I see. <br>&ldquo;No sooner + Verb + Subject&rdquo; is the correct grammatical structure for the given sentence. Hence, &lsquo;No sooner did(Verb) I(Subject) see&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>78.(b) No sooner did I see. <br>&ldquo;No sooner + Verb + Subject&rdquo; दिए गए वाक्य के लिए सही व्याकरणिक संरचना है।. इसलिए, &lsquo;No sooner did(Verb) I(Subject) see&rsquo; सर्वाधिक उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "79",
                    section: "7",
                    question_en: "<p>79. Choose the best alternative which best expresses the meaning of the<strong> idiom/ phrase</strong> given below.<br>My father&rsquo;s dealings are open and <strong><span style=\"text-decoration: underline;\">above board</span></strong>.</p>",
                    question_hi: "<p>79. Choose the best alternative which best expresses the meaning of the<strong> idiom/ phrase</strong> given below.<br>My father&rsquo;s dealings are open and <strong><span style=\"text-decoration: underline;\">above board.</span></strong></p>",
                    options_en: [
                        "<p>to everyone&rsquo;s liking</p>",
                        "<p>mandatory</p>",
                        "<p>without any secret</p>",
                        "<p>very clear</p>"
                    ],
                    options_hi: [
                        "<p>to everyone&rsquo;s liking</p>",
                        "<p>mandatory</p>",
                        "<p>without any secret</p>",
                        "<p>very clear</p>"
                    ],
                    solution_en: "<p>79.(c) without any secret<br>Example- Everything he said at the annual meeting was perfectly honourable and above board.</p>",
                    solution_hi: "<p>79.(c) without any secret/ स्पष्ट रूप से। <br>Example- Everything he said at the annual meeting was perfectly honourable and above board.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "80",
                    section: "7",
                    question_en: "<p>80. Select the option that expresses the given sentence in indirect speech.<br>I said to Amit, &ldquo;I am not participating in the annual function.&rdquo;</p>",
                    question_hi: "<p>80. Select the option that expresses the given sentence in indirect speech.<br>I said to Amit, &ldquo;I am not participating in the annual function.&rdquo;</p>",
                    options_en: [
                        "<p>I said Amit that I was not participating in the annual function.</p>",
                        "<p>I told Amit that he was not participating in the annual function.</p>",
                        "<p>I told Amit that I was not participating in the annual function.</p>",
                        "<p>I told to Amit that I was not participating in the annual function.</p>"
                    ],
                    options_hi: [
                        "<p>I said Amit that I was not participating in the annual function.</p>",
                        "<p>I told Amit that he was not participating in the annual function.</p>",
                        "<p>I told Amit that I was not participating in the annual function.</p>",
                        "<p>I told to Amit that I was not participating in the annual function.</p>"
                    ],
                    solution_en: "<p>80.(c) I told Amit that I was not participating in the annual function. (Correct)<br>(a) I said Amit that I was not participating in the annual function. (Incorrect Reporting Verb)<br>(b) I told Amit that <span style=\"text-decoration: underline;\">he</span> was not participating in the annual function. (Incorrect Pronoun)<br>(d) I told <span style=\"text-decoration: underline;\">to</span> Amit that I was not participating in the annual function. (Incorrect use of &lsquo;to&rsquo;)</p>",
                    solution_hi: "<p>80.(c) I told Amit that I was not participating in the annual function. (Correct)<br>(a) I said Amit that I was not participating in the annual function. (गलत Reporting Verb)<br>(b) I told Amit that <span style=\"text-decoration: underline;\">he</span> was not participating in the annual function. (गलत Pronoun)<br>(d) I told <span style=\"text-decoration: underline;\">to</span> Amit that I was not participating in the annual function. (&lsquo;to&rsquo; का गलत use)</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "81",
                    section: "7",
                    question_en: "<p dir=\"ltr\">81. Select the correct passive form of the given sentences.<br>Khushi wiped down the tables.</p>",
                    question_hi: "<p dir=\"ltr\">81. Select the correct passive form of the given sentences.<br>Khushi wiped down the tables.</p>",
                    options_en: [
                        "<p>The tables were being wiped down by Khushi.</p>",
                        "<p>The tables was wiped down by Khushi.</p>",
                        "<p dir=\"ltr\">The tables are wiped down by Khushi.</p>",
                        "<p dir=\"ltr\">The tables were wiped down by Khushi.</p>"
                    ],
                    options_hi: [
                        "<p dir=\"ltr\">The tables were being wiped down by Khushi.</p>",
                        "<p dir=\"ltr\">&nbsp;The tables was wiped down by Khushi.</p>",
                        "<p dir=\"ltr\">&nbsp;The tables are wiped down by Khushi.</p>",
                        "<p dir=\"ltr\">&nbsp;The tables were wiped down by Khushi.</p>"
                    ],
                    solution_en: "<p>81.(d) The tables were wiped down by Khushi. (Correct)<br>(a) The tables were <span style=\"text-decoration: underline;\">being</span> wiped down by Khushi. (Incorrect Verb)<br>(b) The tables <span style=\"text-decoration: underline;\">was</span> wiped down by Khushi. (Incorrect Verb)<br>(c) The tables <span style=\"text-decoration: underline;\">are wiped</span> down by Khushi. (Incorrect Tense)</p>",
                    solution_hi: "<p>81.(d) The tables were wiped down by Khushi. (Correct)<br>(a) The tables were <span style=\"text-decoration: underline;\">being</span> wiped down by Khushi. (गलत Verb)<br>(b) The tables <span style=\"text-decoration: underline;\">was</span> wiped down by Khushi. (गलत Verb)<br>(c) The tables <span style=\"text-decoration: underline;\">are wiped</span> down by Khushi. (गलत Tense)</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "82",
                    section: "7",
                    question_en: "<p dir=\"ltr\">82. The following sentence has been split into four segments. Identify the segment that&nbsp;contains a grammatical error.<br>The couple / preferred to walk / until the last house / of the town.</p>",
                    question_hi: "<p>82. The following sentence has been split into four segments. Identify the segment that&nbsp;contains a grammatical error.<br>The couple / preferred to walk / until the last house / of the town.</p>",
                    options_en: [
                        "<p>of the town&nbsp;&nbsp;</p>",
                        "<p>preferred to walk&nbsp;&nbsp;&nbsp;</p>",
                        "<p>The couple&nbsp;</p>",
                        "<p>until the last house&nbsp;</p>"
                    ],
                    options_hi: [
                        "<p>of the town&nbsp;&nbsp;</p>",
                        "<p>preferred to walk&nbsp;&nbsp;&nbsp;</p>",
                        "<p>The couple&nbsp;</p>",
                        "<p dir=\"ltr\">until the last house&nbsp;</p>"
                    ],
                    solution_en: "<p>82.(d) <strong>until the last house</strong><br>&lsquo;Until&rsquo; must be replaced by &lsquo;to&rsquo; because when referring to a destination or endpoint, &lsquo;to&rsquo; is used. Hence, &lsquo;to the last house&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>82.(d) <strong>until the last house</strong> <br>&lsquo;Until&rsquo; के स्थान पर &lsquo;to&rsquo; का प्रयोग होगा क्योंकि जब किसी गंतव्य (destination) या अंतिम बिंदु (endpoint) को refer किया जाता है, तो &lsquo;to&rsquo; का प्रयोग किया जाता है। अतः, &lsquo;to the last house&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "83",
                    section: "7",
                    question_en: "<p>83. Find a word that is the synonym of&nbsp;Excruciating</p>",
                    question_hi: "<p>83. Find a word that is the synonym of&nbsp;Excruciating</p>",
                    options_en: [
                        "<p>Facile</p>",
                        "<p>Enticing</p>",
                        "<p>Alluring</p>",
                        "<p>Piercing</p>"
                    ],
                    options_hi: [
                        "<p>Facile</p>",
                        "<p>Enticing</p>",
                        "<p>Alluring</p>",
                        "<p>Piercing</p>"
                    ],
                    solution_en: "<p>83.(d) <strong>Piercing- </strong>strong and unpleasant<br><strong>Excruciating- </strong>extremely painful<br><strong>Facile- </strong>easy <br><strong>Enticing- </strong>attractive and interesting<br><strong>Alluring- </strong>attractive and exciting in a way</p>",
                    solution_hi: "<p>83.(d)<strong> Piercing-</strong> दर्दनाक<br><strong>Excruciating-</strong> अत्यंत दर्दनाक<br><strong>Facile-</strong> आसान<br><strong>Enticing- </strong>आकर्षक और दिलचस्प<br><strong>Alluring-</strong> एक तरह से आकर्षक और रोमांचक</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "84",
                    section: "7",
                    question_en: "<p dir=\"ltr\">84.Given below are four jumbled parts of a sentence. Pick the option that gives their correct order.</p>\n<p dir=\"ltr\">1&nbsp; When I got there<br>P. who collected the fees&nbsp; <br>Q. I found that the teacher <br>R. was on leave and so <br>S. the fees would be<br>6&nbsp; collected the next day.</p>\n<p>&nbsp;</p>",
                    question_hi: "<p dir=\"ltr\">84. Given below are four jumbled parts of a sentence. Pick the option that gives their correct order.</p>\n<p dir=\"ltr\">1&nbsp; When I got there<br>P. who collected the fees&nbsp; <br>Q. I found that the teacher <br>R. was on leave and so <br>S. the fees would be<br>6&nbsp; collected the next day.</p>",
                    options_en: [
                        "<p>PRSQ</p>",
                        "<p>QPRS</p>",
                        "<p>PRSQ</p>",
                        "<p>RPSQ</p>"
                    ],
                    options_hi: [
                        "<p>PRSQ</p>",
                        "<p>QPRS</p>",
                        "<p>PRSQ</p>",
                        "<p>RPSQ</p>"
                    ],
                    solution_en: "<p>84.(b) <strong>QPRS</strong> <br>The sentence starts with the given phrase &lsquo;When I got there&rsquo;. Part Q will follow this phrase as it contains the other clause &lsquo;I found that the teacher&rsquo;. However, Part P contains the relative pronoun &lsquo;who&rsquo; which is used to refer to the noun &lsquo;teacher&rsquo;. So, P will follow Q. Further, Part R contains the verb &lsquo;was&rsquo; which will follow Part P and Part R ends with the conjunction &lsquo;So&rsquo; which will connect Part S to it. So, S will follow R. Going through the options, option (b) has the correct sequence.</p>",
                    solution_hi: "<p>84.(b) <strong>QPRS</strong><br>Sentence दिए गए phrase &lsquo;When I got there&rsquo; से शुरू होता है।&nbsp; Part Q&nbsp; इस phrase के बाद आएगा&nbsp; क्योंकि इसमें अन्य clause &lsquo;I found that the teacher&rsquo; शामिल है। हालाँकि, Part P में relative pronoun &lsquo;who&rsquo; होता है, जिसका उपयोग noun &lsquo;teacher&rsquo; के संदर्भ में किया जाता है। तो, Q के बाद&nbsp; P आएगा। इसके अलावा, Part R में verb &lsquo;was&rsquo; है, जो Part P के बाद आएगा और Part R conjunction &lsquo;So&rsquo;&nbsp; के साथ समाप्त होता है, जो Part S को इससे जोड़ेगा। तो, R के बाद S आएगा । विकल्पों के माध्यम से, विकल्प (b) में सही क्रम है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "85",
                    section: "7",
                    question_en: "<p>85. Pick a word opposite in meaning to<br>His poems are <span style=\"text-decoration: underline;\"><strong>recondite</strong></span> in subject matter</p>",
                    question_hi: "<p>85. Pick a word opposite in meaning to<br>His poems are <span style=\"text-decoration: underline;\"><strong>recondite</strong></span> in subject matter</p>",
                    options_en: [
                        "<p>brief</p>",
                        "<p>simple</p>",
                        "<p>mystical</p>",
                        "<p>obscure</p>"
                    ],
                    options_hi: [
                        "<p>brief</p>",
                        "<p>simple</p>",
                        "<p>mystical</p>",
                        "<p>obscure</p>"
                    ],
                    solution_en: "<p>85.(b) <strong>Recondite </strong>- little known about or understood by people<br><strong>Simple - </strong>easy to understand, do or use<br><strong>Brief - </strong>short or quick<br><strong>Mystical -</strong> connected with the spirit; strange and wonderful<br><strong>Obscure -</strong> not well know</p>",
                    solution_hi: "<p>85.(b)<strong> Recondite (कम जानकारी) -</strong> little known about or understood by people<br><strong>Simple (सरल) - </strong>easy to understand, do or use<br><strong>Brief (छोटा) - </strong>short or quick<br><strong>Mystical (रहस्यवादी) </strong>- connected with the spirit; strange and wonderful<br><strong>Obscure (अस्पष्ट) - </strong>not well known</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "86",
                    section: "7",
                    question_en: "<p>86. Identify the best way to improve the underlined part of the given sentence. If there is no improvement required, select &lsquo;no improvement&rsquo;. <br>The Principal has <span style=\"text-decoration: underline;\"><strong>brought forward</strong></span> the idea that the college should work on Saturday.</p>",
                    question_hi: "<p>86. Identify the best way to improve the underlined part of the given sentence. If there is no improvement required, select &lsquo;no improvement&rsquo;. <br>The Principal has <span style=\"text-decoration: underline;\"><strong>brought forward</strong></span> the idea that the college should work on Saturday.</p>",
                    options_en: [
                        "<p>rejected</p>",
                        "<p>put forward</p>",
                        "<p>suggest</p>",
                        "<p>No improvement</p>"
                    ],
                    options_hi: [
                        "<p>rejected</p>",
                        "<p>put forward</p>",
                        "<p>suggest</p>",
                        "<p>No improvement</p>"
                    ],
                    solution_en: "<p>86.(b) Put forward<br><strong>Put forward</strong> means to suggest something for discussion.<br>Eg- The budget for the sports meet was put forward by the secretary.</p>",
                    solution_hi: "<p>86.(b) Put forward<br><strong>Put forward</strong> का अर्थ है &ldquo;चर्चा के लिए कुछ सुझाना&rdquo;।<br>Eg- The budget for the sports meet was put forward by the secretary./सचिव द्वारा खेलकूद प्रतियोगिता का बजट पेश किया गया।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "87",
                    section: "7",
                    question_en: "<p dir=\"ltr\">87. Select the INCORRECTLY spelt word in the given sentence.<br>A distinguished academecian, Amartya Sen has taught in India, Britain and the United&nbsp;States.</p>",
                    question_hi: "<p>87. Select the INCORRECTLY spelt word in the given sentence.<br>A distinguished academecian, Amartya Sen has taught in India, Britain and the United States.</p>",
                    options_en: [
                        "<p>Taught</p>",
                        "<p>Distinguished</p>",
                        "<p dir=\"ltr\">Academecian&nbsp;</p>",
                        "<p>Britain</p>"
                    ],
                    options_hi: [
                        "<p>Taught</p>",
                        "<p>Distinguished&nbsp;</p>",
                        "<p>Academecian&nbsp;</p>",
                        "<p>Britain</p>"
                    ],
                    solution_en: "<p>87.(c) Academecian<br>\'Academician\' is the correct spelling.</p>",
                    solution_hi: "<p>87.(c) Academecian<br>\'Academician\' सही spelling है।&nbsp;<strong id=\"docs-internal-guid-b8dcfecc-7fff-fde3-990b-8ff76f5dace7\"> </strong></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "88",
                    section: "7",
                    question_en: "<p>88.&nbsp;Select the option that expresses the given sentence in passive voice.<br><strong id=\"docs-internal-guid-0d12d61a-7fff-b1e8-263d-6e15bf96905a\"></strong>The gardener has watered the bushes.</p>",
                    question_hi: "<p>88.&nbsp;Select the option that expresses the given sentence in passive voice.<br><strong id=\"docs-internal-guid-0d12d61a-7fff-b1e8-263d-6e15bf96905a\"></strong>The gardener has watered the bushes.</p>",
                    options_en: [
                        "<p>The bushes have been watered by the gardener.</p>",
                        "<p>The bushes are being watered by the gardener.</p>",
                        "<p>The bushes were being watered by the gardener.</p>",
                        "<p>The bushes has been watered by the gardener.</p>"
                    ],
                    options_hi: [
                        "<p>The bushes have been watered by the gardener.</p>",
                        "<p>The bushes are being watered by the gardener.</p>",
                        "<p>The bushes were being watered by the gardener.</p>",
                        "<p>The bushes has been watered by the gardener.</p>"
                    ],
                    solution_en: "<p>88.(a) The bushes have been watered by the gardener. (Correct)<br>(b) The bushes <span style=\"text-decoration: underline;\">are being watered</span> by the gardener. (Incorrect Tense)<br>(c) The bushes <span style=\"text-decoration: underline;\">were being watered</span> by the gardener. (Incorrect Tense)<br>(d) The bushes <span style=\"text-decoration: underline;\">has</span> been watered by the gardener. (Incorrect Verb)</p>",
                    solution_hi: "<p>88.(a) The bushes have been watered by the gardener. (Correct)<br>(b) The bushes <span style=\"text-decoration: underline;\">are being watered</span> by the gardener. (गलत Tense)<br>(c) The bushes <span style=\"text-decoration: underline;\">were being watered</span> by the gardener. (गलत Tense)<br>(d) The bushes <span style=\"text-decoration: underline;\">has</span> been watered by the gardener. (गलत Verb)</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "89",
                    section: "7",
                    question_en: "<p>89. Select the most appropriate meaning of idiom .<br>dog in a manger</p>",
                    question_hi: "<p>89. Select the most appropriate meaning of idiom .<br>dog in a manger</p>",
                    options_en: [
                        "<p>warm</p>",
                        "<p>selfish</p>",
                        "<p>cold</p>",
                        "<p>selfless</p>"
                    ],
                    options_hi: [
                        "<p>warm</p>",
                        "<p>selfish</p>",
                        "<p>cold</p>",
                        "<p>selfless</p>"
                    ],
                    solution_en: "<p>89.(b) selfish<br>Example - Neither he does his work nor he lets others work he is a dog in the manger.</p>",
                    solution_hi: "<p>89. (b) selfish<br>उदाहरण - Neither he does his work nor he lets others work he is a dog in the manger. </p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "90",
                    section: "7",
                    question_en: "<p>90. Select the word which means the same as the group of words given<br>A period of thousand years</p>",
                    question_hi: "<p>90. Select the word which means the same as the group of words given<br>A period of thousand years</p>",
                    options_en: [
                        "<p>Centenary</p>",
                        "<p>Millennium</p>",
                        "<p>Decade</p>",
                        "<p>Century</p>"
                    ],
                    options_hi: [
                        "<p>Centenary</p>",
                        "<p>Millennium</p>",
                        "<p>Decade</p>",
                        "<p>Century</p>"
                    ],
                    solution_en: "<p>90.(b) <strong>Millennium-</strong> A period of thousand years <br><strong>Centenary-</strong> the year that comes exactly one hundred years after an important event<br><strong>Decade- </strong>a period of ten years <br><strong>Century-</strong> a particular period of 100 years that is used for giving dates</p>",
                    solution_hi: "<p>90.(b) <strong>Millennium-</strong> हजार वर्ष की अवधि <br><strong>Centenary- </strong>वह वर्ष जो किसी महत्वपूर्ण घटना के ठीक सौ वर्ष बाद आता है<br><strong>Decade- </strong>दस साल का समय<br><strong>Century- </strong>100 वर्षों की एक विशेष अवधि</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "91",
                    section: "7",
                    question_en: "<p dir=\"ltr\">91. Given below are four jumbled parts of a sentence. Pick the option that gives their correct order.</p>\n<p dir=\"ltr\">1 As an amateur <br>A. astronomer he had longed for<br>B. time in which <br>C. enough money to buy a good <br>D. telescope and for enough spare&nbsp;<br>6. to observe the heavens.</p>",
                    question_hi: "<p dir=\"ltr\">91. Given below are four jumbled parts of a sentence. Pick the option that gives their correct order.</p>\n<p dir=\"ltr\">1 As an amateur <br>A. astronomer he had longed for<br>B. time in which <br>C. enough money to buy a good <br>D. telescope and for enough spare&nbsp;<br>6. to observe the heavens.</p>",
                    options_en: [
                        "<p>DBAC</p>",
                        "<p>CDBA</p>",
                        "<p>ACDB</p>",
                        "<p>ADBC</p>"
                    ],
                    options_hi: [
                        "<p>DBAC</p>",
                        "<p>CDBA</p>",
                        "<p>ACDB</p>",
                        "<p>ADBC</p>"
                    ],
                    solution_en: "<p>91.(c) <strong>ACDB</strong><br>The sentence starts with the given phrase &lsquo;As an amateur&rsquo;. This phrase ends with an adjective &lsquo;amateur&rsquo; which will have a noun after it. So, A will follow 1. However, Part C contains a noun which will follow the preposition &lsquo;For&rsquo;, mentioned in Part A. Further, Part D contains a noun which will follow the adjective &lsquo;Good&rsquo;, mentioned in Part C and Part B contains the noun &lsquo;time&rsquo; which will follow the adjective &lsquo;spare&rsquo;, mentioned in Part D. So, B will follow D. Going through the options, option (c) has the correct sequence.&nbsp;</p>",
                    solution_hi: "<p>91.(c) <strong>ACDB</strong><br>वाक्य दिए गए phrase &lsquo;As an amateur&rsquo; से शुरू होता है। यह phrase एक adjective &lsquo;amateur&rsquo; के साथ समाप्त होता है जिसके बाद एक noun होगा । इसलिए, 1 के बाद A आएगा। Part C में एक noun है जो भाग A में उल्लिखित preposition &lsquo;For&rsquo;, के बाद आएगा । इसके अलावा, Part D में एक noun है जो adjective &lsquo;Good&rsquo;, के बाद आएगा , जो Part C भाग में उल्लिखित है। B में noun &lsquo;time&rsquo; है जो Part D में वर्णित adjective &lsquo;spare&rsquo; का के बाद आएगा । इसलिए, D के बाद B आएगा । विकल्पों के माध्यम से जाने पर, option (c) में सही क्रम है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "92",
                    section: "7",
                    question_en: "<p>92. Select the most appropriate to fill in the blank.<br>The boxer was knocked out cold by a powerful________ punch to the jaw.</p>",
                    question_hi: "<p>92. Select the most appropriate to fill in the blank.<br>The boxer was knocked out cold by a powerful________ punch to the jaw.</p>",
                    options_en: [
                        "<p>write</p>",
                        "<p>right</p>",
                        "<p>wright</p>",
                        "<p>rite</p>"
                    ],
                    options_hi: [
                        "<p>write</p>",
                        "<p>right</p>",
                        "<p>wright</p>",
                        "<p>rite</p>"
                    ],
                    solution_en: "<p>92.(b) <strong>right</strong><br>&lsquo;Right&rsquo; means done in the correct way. The given sentence states that the boxer was knocked out cold by a powerful right punch to the jaw. Hence, &lsquo;right&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>92.(b) <strong>right</strong><br>&lsquo;Right&rsquo; का अर्थ है सही तरीके (correct way) से किया गया। दिए गए sentence में कहा गया है कि मुक्केबाज (boxer) को जबड़े (jaw) पर एक powerful right punch मारकर गिरा दिया था। अतः, &lsquo;right&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "93",
                    section: "7",
                    question_en: "<p>93. Find a word that is the synonym of Erroneous</p>",
                    question_hi: "<p>93. Find a word that is the synonym of Erroneous</p>",
                    options_en: [
                        "<p>wrong</p>",
                        "<p>Unfair</p>",
                        "<p>False</p>",
                        "<p>Inaccurate</p>"
                    ],
                    options_hi: [
                        "<p>wrong</p>",
                        "<p>Unfair</p>",
                        "<p>False</p>",
                        "<p>Inaccurate</p>"
                    ],
                    solution_en: "<p>93.(a) wrong</p>",
                    solution_hi: "<p>93. (a) wrong / गलत। </p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "94",
                    section: "7",
                    question_en: "<p>94.Pick a word opposite in meaning to<br>Clamour</p>",
                    question_hi: "<p>94.Pick a word opposite in meaning to<br>Clamour</p>",
                    options_en: [
                        "<p>Quiet</p>",
                        "<p>Complaint</p>",
                        "<p>Babble</p>",
                        "<p>Danger</p>"
                    ],
                    options_hi: [
                        "<p>Quiet</p>",
                        "<p>Complaint</p>",
                        "<p>Babble</p>",
                        "<p>Danger</p>"
                    ],
                    solution_en: "<p>94.(a) <strong>Quiet-</strong> with very little or no noise <br><strong>Clamour-</strong> noise, hullabaloo<br><strong>Complaint-</strong> a statement that you are not satisfied with something<br><strong>Babble - </strong>the sound of many voices talking at the same time<br><strong>Danger -</strong> the chance that somebody/something may be hurt, killed, or damaged or that something bad may happen</p>",
                    solution_hi: "<p>94.(a) <strong>Quiet- </strong>बहुत कम या बिना शोर के। <br><strong>Clamour-</strong> शोर। <br><strong>Complaint-</strong> शिकायत करना। <br><strong>Babble -</strong> एक ही समय में कई आवाजों के बात करने की आवाज ; बड़बड़ाना। <br><strong>Danger - </strong>खतरा। </p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "95",
                    section: "7",
                    question_en: "<p>95. Select the most appropriate option to fill in the blank.<br>The plane was___________________ five hours late.</p>",
                    question_hi: "<p>95. Select the most appropriate option to fill in the blank.<br>The plane was___________________ five hours late.</p>",
                    options_en: [
                        "<p>evenly</p>",
                        "<p>almost</p>",
                        "<p>keeping</p>",
                        "<p>something</p>"
                    ],
                    options_hi: [
                        "<p>evenly</p>",
                        "<p>almost</p>",
                        "<p>keeping</p>",
                        "<p>something</p>"
                    ],
                    solution_en: "<p>95.(b) almost.<br>Almost - not quite ; nearly. <br>Example - I have almost finished the paper. Means nearly done.</p>",
                    solution_hi: "<p>95.(b) almost.<br>Almost - बिल्कुल नहीं; लगभग। <br>उदाहरण - I have almost finished the paper. / मैंने पेपर लगभग समाप्त कर लिया है इसका अर्थ है लगभग हो चुका है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "96",
                    section: "7",
                    question_en: "<p>96. <strong>Cloze Test :</strong><br>The inevitable effect of mechanical production is uniformity, and although uniformity may lead to monotony, it need not lack beauty. In fact, modern improvement in popular (96)______ is mainly the result of better design (97)______standardized, goods; and the average (98) ______ is buying better-designed goods, because those goods are (99)______ because the problem of taste has been (100)______ for him. <br>Select the appropriate option to fill in the blank number 96</p>",
                    question_hi: "<p>96. <strong>Cloze Test :</strong><br>The inevitable effect of mechanical production is uniformity, and although uniformity may lead to monotony, it need not lack beauty. In fact, modern improvement in popular (96)______ is mainly the result of better design (97)______standardized, goods; and the average (98) ______ is buying better-designed goods, because those goods are (99)______ because the problem of taste has been (100)______ for him. <br>Select the appropriate option to fill in the blank number 96</p>",
                    options_en: [
                        "<p>craft</p>",
                        "<p>technology</p>",
                        "<p>ideas</p>",
                        "<p>taste</p>"
                    ],
                    options_hi: [
                        "<p>craft</p>",
                        "<p>technology</p>",
                        "<p>ideas</p>",
                        "<p>taste</p>"
                    ],
                    solution_en: "<p>96.(d) taste<br>&lsquo;Taste&rsquo; means what a person likes or prefers. In the later part of the paragraph, the word &lsquo;taste&rsquo; is mentioned multiple times. Hence, &lsquo;taste&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>96.(d) taste<br>&lsquo;Taste&rsquo; इसका अर्थ है कि एक व्यक्ति क्या पसंद करता है। Paragraph के बाद के भाग में, &lsquo;taste&rsquo; शब्द का कई बार उल्लेख किया गया है। इसलिए, \'&lsquo;taste&rsquo;\' सबसे उपयुक्त उत्तर है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "97",
                    section: "7",
                    question_en: "<p>97. <strong>Cloze Test :</strong> <br>The inevitable effect of mechanical production is uniformity, and although uniformity may lead to monotony, it need not lack beauty. In fact, modern improvement in popular (96)______ is mainly the result of better design (97)______standardized, goods; and the average (98) ______ is buying better-designed goods, because those goods are (99)______ because the problem of taste has been (100)______ for him. <br>Select the appropriate option to fill in the blank number 97</p>",
                    question_hi: "<p>97.<strong> Cloze Test :</strong> <br>The inevitable effect of mechanical production is uniformity, and although uniformity may lead to monotony, it need not lack beauty. In fact, modern improvement in popular (96)______ is mainly the result of better design (97)______standardized, goods; and the average (98) ______ is buying better-designed goods, because those goods are (99)______ because the problem of taste has been (100)______ for him. <br>Select the appropriate option to fill in the blank number 97</p>",
                    options_en: [
                        "<p>on</p>",
                        "<p>of</p>",
                        "<p>in</p>",
                        "<p>for</p>"
                    ],
                    options_hi: [
                        "<p>on</p>",
                        "<p>of</p>",
                        "<p>in</p>",
                        "<p>for</p>"
                    ],
                    solution_en: "<p>97.(b) of<br>The preposition &lsquo;of&rsquo; is used to express the relationship of a part of something to its whole. The given passage states that the modern improvement in popular taste is mainly the result of the better design of standardized goods. Hence, &lsquo;of&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>97.(b) of<br>Preposition &lsquo;of&rsquo; का उपयोग किसी चीज़ के एक हिस्से के संबंध को उसके संपूर्ण से व्यक्त करने के लिए किया जाता है। दिए गए passage में कहा गया है कि लोकप्रिय स्वाद में आधुनिक सुधार मुख्य रूप से मानकीकृत वस्तुओं के बेहतर डिजाइन का परिणाम है। इसलिए, &lsquo;of&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "98",
                    section: "7",
                    question_en: "<p>98. <strong>Cloze Test :</strong><br>The inevitable effect of mechanical production is uniformity, and although uniformity may lead to monotony, it need not lack beauty. In fact, modern improvement in popular (96)______ is mainly the result of better design (97)______standardized, goods; and the average (98) ______ is buying better-designed goods, because those goods are (99)______ because the problem of taste has been (100)______ for him. <br>Select the appropriate option to fill in the blank number 98</p>",
                    question_hi: "<p>98. <strong>Cloze Test :</strong><br>The inevitable effect of mechanical production is uniformity, and although uniformity may lead to monotony, it need not lack beauty. In fact, modern improvement in popular (96)______ is mainly the result of better design (97)______standardized, goods; and the average (98) ______ is buying better-designed goods, because those goods are (99)______ because the problem of taste has been (100)______ for him. <br>Select the appropriate option to fill in the blank number 98</p>",
                    options_en: [
                        "<p>shopper</p>",
                        "<p>client</p>",
                        "<p>man</p>",
                        "<p>businessmen</p>"
                    ],
                    options_hi: [
                        "<p>shopper</p>",
                        "<p>client</p>",
                        "<p>man</p>",
                        "<p>businessmen</p>"
                    ],
                    solution_en: "<p>98.(a) <strong>shopper</strong><br>&lsquo;Shopper&rsquo; is a person who buys goods in a shop. The given passage states that the average shopper is buying better-designed goods. Hence, &lsquo;shopper&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>98.(a) <strong>shopper</strong><br>&lsquo;Shopper&rsquo; एक व्यक्ति है जो एक दुकान से सामान खरीदता है। दिया गया passage बताता है कि औसत दुकानदार बेहतर-डिज़ाइन वाले सामान खरीद रहा है। इसलिए, &lsquo;Shopper&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "99",
                    section: "7",
                    question_en: "<p>99. <strong>Cloze Test :</strong><br>The inevitable effect of mechanical production is uniformity, and although uniformity may lead to monotony, it need not lack beauty. In fact, modern improvement in popular (96)______ is mainly the result of better design (97)______standardized, goods; and the average (98) ______ is buying better-designed goods, because those goods are (99)______ because the problem of taste has been (100)______ for him. <br>Select the appropriate option to fill in the blank number 99</p>",
                    question_hi: "<p>99. <strong>Cloze Test :</strong><br>The inevitable effect of mechanical production is uniformity, and although uniformity may lead to monotony, it need not lack beauty. In fact, modern improvement in popular (96)______ is mainly the result of better design (97)______standardized, goods; and the average (98) ______ is buying better-designed goods, because those goods are (99)______ because the problem of taste has been (100)______ for him. <br>Select the appropriate option to fill in the blank number 99</p>",
                    options_en: [
                        "<p>gaudy</p>",
                        "<p>accessible</p>",
                        "<p>reliable</p>",
                        "<p>durable</p>"
                    ],
                    options_hi: [
                        "<p>gaudy</p>",
                        "<p>accessible</p>",
                        "<p>reliable</p>",
                        "<p>durable</p>"
                    ],
                    solution_en: "<p>99.(a) <strong>gaudy</strong><br>&lsquo;Gaudy&rsquo; means extravagantly bright or showy, typically so as to be tasteless. The given passage states that the average shopper is buying better-designed goods because those goods are bright or showy. Hence, &lsquo;gaudy&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>99.(a) <strong>gaudy</strong><br>&lsquo;Gaudy&rsquo; का अर्थ है - असाधारण रूप से उज्ज्वल या दिखावटी, आमतौर पर बेस्वाद होना। दिया गया passage बताता है कि औसत खरीदार बेहतर-डिज़ाइन वाले सामान खरीद रहे हैं क्योंकि वे सामान दिखावटी हैं। इसलिए, &lsquo;gaudy&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "100",
                    section: "misc",
                    question_en: "<p>100. <strong>Cloze Test :</strong><br>The inevitable effect of mechanical production is uniformity, and although uniformity may lead to monotony, it need not lack beauty. In fact, modern improvement in popular (96)______ is mainly the result of better design (97)______standardized, goods; and the average (98) ______ is buying better-designed goods, because those goods are (99)______ because the problem of taste has been (100)______ for him. <br>Select the appropriate option to fill in the blank number 100</p>",
                    question_hi: "<p>100. <strong>Cloze Test :</strong><br>The inevitable effect of mechanical production is uniformity, and although uniformity may lead to monotony, it need not lack beauty. In fact, modern improvement in popular (96)______ is mainly the result of better design (97)______standardized, goods; and the average (98) ______ is buying better-designed goods, because those goods are (99)______ because the problem of taste has been (100)______ for him. <br>Select the appropriate option to fill in the blank number 25</p>",
                    options_en: [
                        "<p>tackled</p>",
                        "<p>simplified</p>",
                        "<p>resolved</p>",
                        "<p>solved</p>"
                    ],
                    options_hi: [
                        "<p>tackled</p>",
                        "<p>simplified</p>",
                        "<p>resolved</p>",
                        "<p>solved</p>"
                    ],
                    solution_en: "<p>100.(c) <strong>resolved</strong><br>&lsquo;Resolved&rsquo; means to find an answer to a problem. The given passage states that the problem of taste is being discussed and resolved. Hence, &lsquo;resolved&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>100.(c)<strong> resolved</strong><br>&lsquo;Resolved&rsquo; - किसी समस्या का हल खोजना। दिए गए passage में कहा गया है कि स्वाद की समस्या पर चर्चा और समाधान किया जा रहा है। इसलिए, &lsquo;resolved&rsquo; सबसे उपयुक्त उत्तर है। <br><br></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'question-box d-flex align-items-center justify-content-center border rounded';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.question-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            const html = `
                <div class="card shadow-sm mb-4">
                    <div class="card-body">
                        <div class="question-text mb-4">
                            <div class="en">${question.question_en}</div>
                            <div class="hi" style="display:none">${question.question_hi}</div>
                        </div>
                        <div class="options">
                            ${['a', 'b', 'c', 'd'].map((opt, i) => `
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="radio" name="q${index}" 
                                        id="opt-${index}-${opt}" value="${opt}" 
                                        ${answers[question.id] === opt ? 'checked' : ''}
                                        onclick="selectAnswer('${question.id}', '${opt}')">
                                    <label class="form-check-label w-100 py-2 px-3 rounded hover-bg-light" for="opt-${index}-${opt}">
                                        <div class="en">${question.options_en[i]}</div>
                                        <div class="hi" style="display:none">${question.options_hi[i]}</div>
                                    </label>
                                </div>
                            `).join('')}
                        </div>
                        ${submitted ? `
                            <div class="alert ${answers[question.id] === question.correct ? 'alert-success' : 'alert-danger'} mt-3">
                                ${answers[question.id] ? (answers[question.id] === question.correct ? 'Correct!' : 'Incorrect!') : 'Not attempted'}
                                <div class="mt-2">
                                    <strong>Solution:</strong>
                                    <div class="en">${question.solution_en}</div>
                                    <div class="hi" style="display:none">${question.solution_hi}</div>
                                </div>
                            </div>
                        ` : ''}
                    </div>
                </div>
            `;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // If in mobile view, close the menu
            if (window.innerWidth < 768) {
                toggleMenu(false);
            }
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                document.getElementById(`box-${index}`).classList.add('attempted');
            }
        }
        // Navigate to previous question
        function prevQuestion() {
            if (currentQuestion > 0) {
                showQuestion(currentQuestion - 1);
            }
        }
        // Navigate to next question
        function nextQuestion() {
            if (currentQuestion < questions.length - 1) {
                showQuestion(currentQuestion + 1);
            }
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) {
                showQuestion(section.start);
            }
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update button text
            document.getElementById('lang-btn').innerHTML = currentLang === 'en' ? 
                '<i class="fas fa-language"></i> हिंदी' : 
                '<i class="fas fa-language"></i> English';
        }
        // Toggle menu for mobile
        function toggleMenu(show) {
            const nav = document.getElementById('question-nav');
            const content = document.getElementById('question-content');
            if (show === undefined) {
                show = nav.style.right === '-100%' || nav.style.right === '';
            }
            if (show) {
                nav.style.right = '0';
                if (window.innerWidth >= 768) {
                    content.style.marginRight = '320px';
                }
            } else {
                nav.style.right = window.innerWidth < 768 ? '-100%' : '0';
                content.style.marginRight = window.innerWidth < 768 ? '0' : '320px';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
            document.getElementById('total-count').textContent = questions.length;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.remove('attempted');
                    box.classList.add('bg-secondary', 'text-white');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.remove('attempted');
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.remove('attempted');
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            resultsModal.show();
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check-circle me-1"></i> Submitted';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            resultsModal.hide();
            showQuestion(0);
        }
    </script>
</body>
</html>