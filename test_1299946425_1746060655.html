<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Smart Test Interface</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        .question-nav{position:fixed;top:70px;right:0;height:calc(100vh - 70px);width:320px;transition:all .3s;z-index:100;display:flex;flex-direction:column}
        .question-content{margin-right:0;transition:all .3s}
        .question-box{width:40px;height:40px;margin:5px;cursor:pointer}
        .attempted{background-color:#3498db!important;color:white}
        .correct{background-color:#2ecc71!important;color:white}
        .incorrect{background-color:#e74c3c!important;color:white}
        .current{border:3px solid #f39c12!important}
        #question-boxes{overflow-y:auto;max-height:calc(100vh - 150px);padding-bottom:15px}
        @media (max-width:768px){.question-nav{width:100%;right:-100%}.question-content{margin-right:0}}
    </style>
</head>
<body>
    <div class="container-fluid p-0">
        <!-- Header with controls -->
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary sticky-top">
            <div class="container-fluid">
                <a class="navbar-brand" href="#">
                    <i class="fas fa-graduation-cap me-2"></i>Smart Test
                </a>
                <!-- Section selector -->
                <div class="navbar-nav me-auto section-tabs">
                    <select class="form-select" id="section-selector" onchange="switchSection(this.value)">
                        <option value="all">All Sections</option>
                    </select>
                </div>
                <div class="d-flex align-items-center">
                    <!-- Timer -->
                    <div class="bg-light text-primary rounded px-3 py-2 me-2 fw-bold" id="timer">
                        <i class="far fa-clock me-2"></i><span id="timer-display">100:00</span>
                    </div>
                    <!-- Submit button -->
                    <button class="btn btn-success me-2" id="submit-btn" onclick="submitTest()">
                        <i class="fas fa-check me-1"></i> Submit
                    </button>
                    <!-- Language toggle -->
                    <button class="btn btn-light me-2" id="lang-btn" onclick="toggleLanguage()">
                        <i class="fas fa-language"></i>
                    </button>
                    <!-- Menu toggle for mobile -->
                    <button class="btn btn-light d-lg-none" id="menu-toggle" onclick="toggleMenu()">
                        <i class="fas fa-th"></i>
                    </button>
                </div>
            </div>
        </nav>
        <div class="row m-0">
            <!-- Main content area -->
            <div class="col p-0 question-content" id="question-content">
                <div class="container py-4">
                    <!-- Question navigation -->
                    <div class="d-flex justify-content-between mb-4">
                        <button class="btn btn-outline-primary" onclick="prevQuestion()">
                            <i class="fas fa-chevron-left me-1"></i> Previous
                        </button>
                        <div class="fw-bold" id="question-counter">Question 1 of 100</div>
                        <button class="btn btn-outline-primary" onclick="nextQuestion()">
                            Next <i class="fas fa-chevron-right ms-1"></i>
                        </button>
                    </div>
                    <!-- Questions will be displayed here -->
                    <div id="questions-container"></div>
                </div>
            </div>
            <!-- Question navigation sidebar -->
            <div class="bg-light question-nav" id="question-nav">
                <div class="p-3">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h5 class="m-0">Questions</h5>
                        <div>
                            <span class="badge bg-secondary me-1" id="attempted-count">0</span>
                            <span class="badge bg-secondary" id="total-count">100</span>
                            <button class="btn btn-sm btn-outline-secondary ms-2 d-lg-none" onclick="toggleMenu()">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>
                    <div class="d-flex flex-wrap" id="question-boxes"></div>
                </div>
            </div>
        </div>
        <!-- Results modal -->
        <div class="modal fade" id="results-modal" tabindex="-1" aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content">
                    <div class="modal-header bg-primary text-white">
                        <h5 class="modal-title">Test Results</h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row text-center">
                            <div class="col-6 mb-3">
                                <div class="p-3 bg-primary bg-opacity-10 rounded">
                                    <h3 id="score-value">0</h3>
                                    <div class="small text-muted">Score</div>
                                </div>
                            </div>
                            <div class="col-6 mb-3">
                                <div class="p-3 bg-success bg-opacity-10 rounded">
                                    <h3 id="correct-value">0</h3>
                                    <div class="small text-muted">Correct</div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="p-3 bg-danger bg-opacity-10 rounded">
                                    <h3 id="incorrect-value">0</h3>
                                    <div class="small text-muted">Incorrect</div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="p-3 bg-secondary bg-opacity-10 rounded">
                                    <h3 id="unattempted-value">0</h3>
                                    <div class="small text-muted">Unattempted</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-primary" onclick="reviewTest()">Review Test</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 100 * 60;
        let resultsModal;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            resultsModal = new bootstrap.Modal(document.getElementById('results-modal'));
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 768) {
                document.getElementById('question-content').style.marginRight = '320px';
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["6"] = {
                name: "Reasoning",
                start: 0,
                end: 23
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="6">Reasoning</option>`;
            sections["18"] = {
                name: "General Awareness",
                start: 24,
                end: 48
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="18">General Awareness</option>`;
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 49,
                end: 73
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["7"] = {
                name: "English",
                start: 74,
                end: 98
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="7">English</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 99,
                end: 99
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "6",
                    question_en: "<p>1. Select the correct mirror image of the given figure when the mirror is placed at MN.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743489326063.png\" alt=\"rId4\" width=\"127\" height=\"102\"></p>",
                    question_hi: "<p>1. जब दर्पण को MN पर रखा जाता हो तो दी गई आकृति के सही दर्पण प्रतिबिंब का चयन कीजिए। <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743489326063.png\" alt=\"rId4\" width=\"127\" height=\"102\"></p>",
                    options_en: [
                        "<p><strong id=\"docs-internal-guid-563982b8-7fff-9045-21a0-fc3845dbd6d6\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcQe-4A7qhqcVID8Z2PCkHu5v8bwymtElsHnarSSR-3PbYkr-_gsWzKpPOhd0yVXCw4kdP0evaX_J-M6bP6k9KFF3KQDSNh2mZd8XVb9EF6IsXsSon1ojL_v3ZfBB07isYLCq6RxQ?key=sBohhWCaOfJ1gtcPkfo6Ojli\" width=\"137\" height=\"24\"></strong></p>",
                        "<p><strong id=\"docs-internal-guid-ad894711-7fff-a70e-bcc0-2549e83ec593\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXebC5N3iRbrbcFZXXXuqmDYtJxOD82MhTVV0kSJS5Et3S7BWTK8vm34qNnFvx8eoLMnKxsYVYpYCWsZqxIXaJy9qvYS9p4aiKnSe-stECcitPQ_Zldn9SnAKDZ0X11pxmwgdF3sKg?key=sBohhWCaOfJ1gtcPkfo6Ojli\" width=\"133\" height=\"23\"></strong></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743489326546.png\" alt=\"rId7\" width=\"130\" height=\"24\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743489326661.png\" alt=\"rId8\" width=\"131\" height=\"23\"></p>"
                    ],
                    options_hi: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743489326201.png\" alt=\"rId5\" width=\"129\" height=\"23\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743489326402.png\" alt=\"rId6\" width=\"130\" height=\"23\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743489326546.png\" alt=\"rId7\" width=\"130\" height=\"24\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743489326661.png\" alt=\"rId8\" width=\"131\" height=\"23\"></p>"
                    ],
                    solution_en: "<p>1.(d) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743489326990.png\" alt=\"rId10\" width=\"130\" height=\"23\"></p>",
                    solution_hi: "<p>1.(d) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743489326990.png\" alt=\"rId10\" width=\"130\" height=\"23\"></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "6",
                    question_en: "<p>2. Which figure should replace the question mark (?) if the following figure series were to be continued ?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743489327141.png\" alt=\"rId11\" width=\"349\" height=\"84\"></p>",
                    question_hi: "<p>2. यदि निम्नलिखित आकृति श्रृंखला को जारी रखना हो तो कौन-सी आकृति प्रश्न चिन्ह (?) के स्थान पर आनी चाहिए ?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743489327141.png\" alt=\"rId11\" width=\"349\" height=\"84\"></p>",
                    options_en: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743489327389.png\" alt=\"rId12\" width=\"87\" height=\"89\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743489327548.png\" alt=\"rId13\" width=\"84\" height=\"81\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743489327692.png\" alt=\"rId14\" width=\"88\" height=\"86\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743489327800.png\" alt=\"rId15\" width=\"94\" height=\"87\"></p>"
                    ],
                    options_hi: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743489327389.png\" alt=\"rId12\" width=\"85\" height=\"87\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743489327548.png\" alt=\"rId13\" width=\"87\" height=\"83\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743489327692.png\" alt=\"rId14\" width=\"91\" height=\"89\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743489327800.png\" alt=\"rId15\" width=\"90\" height=\"83\"></p>"
                    ],
                    solution_en: "<p>2.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743489327548.png\" alt=\"rId13\" width=\"86\" height=\"82\"></p>",
                    solution_hi: "<p>2.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743489327548.png\" alt=\"rId13\" width=\"86\" height=\"82\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "6",
                    question_en: "<p>3. In a certain code language, <br>\'A ! B\' means &lsquo;A is the father of B&rsquo;, <br>\'A &amp; B\' means &lsquo;A is the brother of B&rsquo;, <br>\'A * B\' means &lsquo;A is the wife of B&rsquo; and<br>\'A # B\' means &lsquo;A is the daughter of B&rsquo;. <br>Which of the following means that S is the husband\'s brother\'s wife of M ?</p>",
                    question_hi: "<p>3. एक निश्चित कूट भाषा में,<br>\'A ! B\' का अर्थ है कि &lsquo;A, B का पिता है&rsquo;,<br>\'A &amp; B\' का अर्थ है कि &lsquo;A, B का भाई है&rsquo;,<br>\'A * B\' का अर्थ है कि &lsquo;A, B की पत्नी है&rsquo; और<br>\'A # B\' का अर्थ है कि &lsquo;A, B की पुत्री है&rsquo;।&nbsp;<br>निम्नलिखित में से किसका अर्थ यह है कि S, M की देवरानी/जेठानी है ?&nbsp;</p>",
                    options_en: [
                        "<p>S &amp; F * J ! I # M</p>",
                        "<p>S * F &amp; J ! I # M</p>",
                        "<p>S &amp; F ! J * I # M</p>",
                        "<p>S &amp; F # J ! I * M</p>"
                    ],
                    options_hi: [
                        "<p>S &amp; F * J ! I # M</p>",
                        "<p>S * F &amp; J ! I # M</p>",
                        "<p>S &amp; F ! J * I # M</p>",
                        "<p>S &amp; F # J ! I * M</p>"
                    ],
                    solution_en: "<p>3.(b)<br>After checking all the options, only option (b) satisfied.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743489327930.png\" alt=\"rId16\" width=\"257\" height=\"118\"></p>",
                    solution_hi: "<p>3.(b)<br>सभी विकल्पों की जांच करने के बाद केवल विकल्प (b) ही संतुष्ट करता है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743489327930.png\" alt=\"rId16\" width=\"257\" height=\"118\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "6",
                    question_en: "<p>4. Read the given statements and conclusions carefully. Assuming that the information given in the statements is true, even if it appears to be at variance with commonly known facts, decide which of the given conclusions logically follow(s) from the statements. <br><strong>Statements:</strong> <br>Some Mirrors are Decoratives. <br>No Decorative is a Plant. <br>All Plants are Trees. <br><strong>Conclusions:</strong> <br>(I) Some Mirrors are Plants. <br>(II) Some Decoratives are Trees.</p>",
                    question_hi: "<p>4. दिए गए कथनों और निष्कर्षों का ध्यानपूर्वक अध्ययन कीजिए। कथनों में दी गई जानकारी को सत्य मानते हुए, भले ही यह सर्वज्ञात तथ्यों से भिन्न प्रतीत होती हो, निर्धारित कीजिए कि दिए गए निष्कर्षों में से कौन-सा/से निष्कर्ष कथनों का तर्कसंगत रूप से अनुसरण करता है/करते हैं। <br><strong>कथन:</strong> <br>कुछ दर्पण, सजावटी हैं। <br>कोई सजावटी, पौधा नहीं है। <br>सभी पौधे, वृक्ष हैं। <br><strong>निष्कर्ष:</strong> <br>(I) कुछ दर्पण, पौधे हैं। <br>(II) कुछ सजावटी, वृक्ष हैं।</p>",
                    options_en: [
                        "<p>Only conclusion (II) follows.</p>",
                        "<p>Both conclusions (I) and (II) follow.</p>",
                        "<p>Only conclusion (I) follows.</p>",
                        "<p>Neither conclusion (I) nor (II) follows.</p>"
                    ],
                    options_hi: [
                        "<p>केवल निष्कर्ष (II) अनुसरण करता है।</p>",
                        "<p>निष्कर्ष (I) और (II) दोनों का अनुसरण करते हैं।</p>",
                        "<p>केवल निष्कर्ष (I) अनुसरण करता है।</p>",
                        "<p>न तो निष्कर्ष (I) और न ही निष्कर्ष (II) अनुसरण करता है।</p>"
                    ],
                    solution_en: "<p>4.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743489328057.png\" alt=\"rId17\" width=\"314\" height=\"82\"><br>Hence, neither conclusion I nor II follows.</p>",
                    solution_hi: "<p>4.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743489328280.png\" alt=\"rId18\" width=\"343\" height=\"90\"><br>अतः, न तो निष्कर्ष I और न ही II अनुसरण करता है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "6",
                    question_en: "<p>5. Select the set in which the numbers are related in the same way as are the numbers of the following sets.<br>(<strong>NOTE :</strong> Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g. 13 &ndash; Operations on 13 such as adding / subtracting / multiplying etc. to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed.) <br>(3, 34, 5) <br>(10, 136, 6)</p>",
                    question_hi: "<p>5. उस समुचय का चयन कीजिए जिसमें संख्याएं ठीक उसी प्रकार संबंधित हैं जिस प्रकार दिए गए समुचयों की संख्याएं संबंधित हैं। (<strong>ध्यान दें :</strong> संख्याओं को उनके घटक अंकों में अलग-अलग किए बिना पूर्ण संख्याओं पर संक्रियाएं की जानी चाहिए। उदाहरण के लिए संख्या 13 - संख्या 13 पर संक्रियाएं जैसे 13 को जोड़ना/घटाना/गुणा करना आदि किया जा सकता है। 13 को 1 और 3 में अलग-अलग करने की और फिर 1 और 3 पर गणितीय संक्रियाएं करने की अनुमति नहीं है।) <br>(3, 34, 5) <br>(10, 136, 6)</p>",
                    options_en: [
                        "<p>(9, 115, 6)</p>",
                        "<p>(11, 125, 4)</p>",
                        "<p>(7, 113, 8)</p>",
                        "<p>(5, 60, 6)</p>"
                    ],
                    options_hi: [
                        "<p>(9, 115, 6)</p>",
                        "<p>(11, 125, 4)</p>",
                        "<p>(7, 113, 8)</p>",
                        "<p>(5, 60, 6)</p>"
                    ],
                    solution_en: "<p>5.(c) <strong>Logic :- </strong>(1st number)<sup>2</sup> + (3rd number)<sup>2</sup> = 2nd number<br>(3, 34, 5):- (3)<sup>2</sup> + (5)<sup>2</sup> &rArr; (9) + (25) = 34<br>(10, 136, 6) :- (10)<sup>2</sup> + (6)<sup>2</sup> &rArr; (100) + (36) = 136<br>Similarly,<br>(7, 113, 8) :- (7)<sup>2</sup> + (8)<sup>2</sup>&nbsp;&rArr; (49) + (64) = 113</p>",
                    solution_hi: "<p>5.(c) <strong>तर्क :- </strong>(पहली संख्या)<sup>2</sup> + (तीसरी संख्या)<sup>2</sup> = दूसरी संख्या<br>(3, 34, 5):- (3)<sup>2</sup> + (5)<sup>2</sup> &rArr; (9) + (25) = 34<br>(10, 136, 6) :- (10)<sup>2</sup> + (6)<sup>2</sup> &rArr; (100) + (36) = 136<br>इसी प्रकार,<br>(7, 113, 8) :- (7)<sup>2</sup> + (8)<sup>2</sup> &rArr; (49) + (64) = 113</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "6",
                    question_en: "<p>6. Select the option that represents the letters that when placed from left to right in the blanks below will complete the given letter series.<br>B _ E N B _ C E N B B C _ N B B C E _ B _ C</p>",
                    question_hi: "<p>6. उस विकल्प का चयन करें जो उन अक्षरों को निरूपित करता है जिन्&zwj;हें नीचे दिए गए रिक्त स्थानों में बाएँ से दाएँ रखे जाने पर दी गई अक्षर श्रृंखला पूरी हो जाएगी।<br>B _ E N B _ C E N B B C _ N B B C E _ B _ C</p>",
                    options_en: [
                        "<p>BCENB</p>",
                        "<p>BNECB</p>",
                        "<p>CNEBB</p>",
                        "<p>CBENB</p>"
                    ],
                    options_hi: [
                        "<p>BCENB</p>",
                        "<p>BNECB</p>",
                        "<p>CNEBB</p>",
                        "<p>CBENB</p>"
                    ],
                    solution_en: "<p>6.(d)<br>B <span style=\"text-decoration: underline;\"><strong>C</strong></span> E N B /<span style=\"text-decoration: underline;\"><strong>B</strong></span> C E N B /B C <span style=\"text-decoration: underline;\"><strong>E</strong></span> N B/ B C E <span style=\"text-decoration: underline;\"><strong>N</strong></span> B/ <span style=\"text-decoration: underline;\"><strong>B</strong></span> C</p>",
                    solution_hi: "<p>6.(d)<br>B <span style=\"text-decoration: underline;\"><strong>C</strong></span> E N B /<span style=\"text-decoration: underline;\"><strong>B</strong></span> C E N B /B C <span style=\"text-decoration: underline;\"><strong>E</strong></span> N B/ B C E <span style=\"text-decoration: underline;\"><strong>N</strong></span> B/ <span style=\"text-decoration: underline;\"><strong>B</strong></span> C</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "6",
                    question_en: "<p>7. Three of the following four are alike in a certain way and thus form a group. Which is the one that does NOT belong to that group ?&nbsp;<br>(<strong>Note : </strong>The odd one out is not based on the number of consonants/vowels or their position in the letter cluster.)</p>",
                    question_hi: "<p>7. निम्नलिखित चार अक्षर-समूहों में से तीन अक्षर-समूह एक निश्चित प्रकार से समान हैं और इस प्रकार एक समूह बनाते हैं। वह कौन-सा अक्षर-समूह है जो उस समूह से संबंधित नहीं है ?&nbsp;<br>(<strong>ध्यान दें : </strong>असंगत अक्षर-समूह, उस अक्षर-समूह में व्यंजनों/स्वरों की संख्या या उनके स्थान पर आधारित नहीं है।)</p>",
                    options_en: [
                        "<p>QSV</p>",
                        "<p>BDG</p>",
                        "<p>FHK</p>",
                        "<p>LNP</p>"
                    ],
                    options_hi: [
                        "<p>QSV</p>",
                        "<p>BDG</p>",
                        "<p>FHK</p>",
                        "<p>LNP</p>"
                    ],
                    solution_en: "<p>7.(d) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743489328627.png\" alt=\"rId19\" width=\"99\" height=\"59\"> ,&nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743489328747.png\" alt=\"rId20\" width=\"97\" height=\"57\"> ,&nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743489328956.png\" alt=\"rId21\" width=\"98\" height=\"58\"><br>But,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743489329067.png\" alt=\"rId22\" width=\"99\" height=\"59\"></p>",
                    solution_hi: "<p>7.(d) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743489328627.png\" alt=\"rId19\" width=\"99\" height=\"59\"> ,&nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743489328747.png\" alt=\"rId20\" width=\"97\" height=\"57\"> ,&nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743489328956.png\" alt=\"rId21\" width=\"98\" height=\"58\"><br>लेकिन,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743489329067.png\" alt=\"rId22\" width=\"99\" height=\"59\"></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "6",
                    question_en: "<p>8. 89 is related to 78 following a certain logic. Following the same logic, 97 is related to&nbsp;86. To which of the following is 72 related, following the same logic ?<br>(<strong>NOTE: </strong>Operations should be performed on the whole numbers, without breaking down the numbers into their constituent digits. E.g. 13 &ndash; Operations on 13 such as&nbsp;adding /subtracting /multiplying to 13 can be performed. Breaking down 13 into 1 and&nbsp;3 and then performing mathematical operations on 1 and 3 is not allowed.)</p>",
                    question_hi: "<p>8. 89, किसी निश्चित तर्क का अनुसरण करते हुए, 78 से संबंधित है। 97, उसी तर्क का अनुसरण करते हुए, 86 से संबंधित है। उसी तर्क का अनुसरण करते हुए, 72, निम्नलिखित में से किससे संबंधित है ?<br>(<strong>नोटः</strong> संख्याओं को उनके घटक अंकों में तोड़े बिना पूर्ण संख्याओं पर संक्रियाएँ की जानी चाहिए। उदाहरण के लिए 13 को लीजिए 13 पर संक्रियाएं, जैसे कि 13 में जोड़ना/घटाना/गुणा करना आदि, की जा सकती हैं। 13 को 1 और 3 में तोड़ने और फिर 1 और 3 पर गणितीय संक्रियाएँ करने की अनुमति नहीं है।)</p>",
                    options_en: [
                        "<p>66</p>",
                        "<p>59</p>",
                        "<p>61</p>",
                        "<p>68</p>"
                    ],
                    options_hi: [
                        "<p>66</p>",
                        "<p>59</p>",
                        "<p>61</p>",
                        "<p>68</p>"
                    ],
                    solution_en: "<p>8.(c) <strong>Logic:-</strong> (2nd number - 1st number) = 11<br>(89 - 78) :- (89 - 78) = 11<br>(97 - 86) :- (97 - 86) = 11<br>Similarly,<br>(72 - 61) :- (72 - 61) = 11</p>",
                    solution_hi: "<p>8.(c) <strong>तर्क:- </strong>(दूसरी संख्या - पहली संख्या) = 11<br>(89 - 78) :- (89 - 78) = 11<br>(97 - 86) :- (97 - 86) = 11<br>इसी प्रकार,<br>(72 - 61) :- (72 - 61) = 11</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "6",
                    question_en: "<p>9. In a certain code language, \'TWIN\' is coded as 2359 and \'WING\' is coded as 5302. What is the code for \'T\' in the given code language ?</p>",
                    question_hi: "<p>9. एक निश्चित कूट भाषा में. \'TWIN\' को \'2359\' लिखा जाता है और WING\' को 5302\' लिखा जाता है। उस कूट भाषा में T\' को कैसे लिखा जाएगा ?</p>",
                    options_en: [
                        "<p>9</p>",
                        "<p>2</p>",
                        "<p>5</p>",
                        "<p>3</p>"
                    ],
                    options_hi: [
                        "<p>9</p>",
                        "<p>2</p>",
                        "<p>5</p>",
                        "<p>3</p>"
                    ],
                    solution_en: "<p>9.(a) TWIN &rarr; 2359&hellip;&hellip;.(i)<br>WING &rarr; 5302&hellip;&hellip;(ii)<br>From (i) and (ii) &lsquo;WIN&rsquo; and &lsquo;235&rsquo; are common. The code of &lsquo;T&rsquo; = &lsquo;9&rsquo;.</p>",
                    solution_hi: "<p>9.(a) TWIN &rarr; 2359&hellip;&hellip;.(i)<br>WING &rarr; 5302&hellip;&hellip;(ii)<br>(i) और (ii) से \'WIN\' और \'235\' उभयनिष्ठ हैं। \'T\' का कोड = \'9\'.</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "6",
                    question_en: "<p>10. Select the figure from among the given options that can replace the question mark (?) and logically complete the series.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743489329715.png\" alt=\"rId27\" width=\"371\" height=\"77\"></p>",
                    question_hi: "<p>10. दिए गए विकल्पों में से उस आकृति का चयन कीजिए जो प्रश्न चिह्न(?) को प्रतिस्थापित कर सकती है और शृंखला को तार्किक रूप से पूरा कर सकती है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743489329715.png\" alt=\"rId27\" width=\"371\" height=\"77\"></p>",
                    options_en: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743489329825.png\" alt=\"rId28\" width=\"87\" height=\"87\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743489329945.png\" alt=\"rId29\" width=\"87\" height=\"87\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743489330054.png\" alt=\"rId30\" width=\"87\" height=\"84\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743489330179.png\" alt=\"rId31\" width=\"87\" height=\"84\"></p>"
                    ],
                    options_hi: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743489329825.png\" alt=\"rId28\" width=\"87\" height=\"87\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743489329945.png\" alt=\"rId29\" width=\"87\" height=\"87\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743489330054.png\" alt=\"rId30\" width=\"87\" height=\"84\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743489330179.png\" alt=\"rId31\" width=\"87\" height=\"84\"></p>"
                    ],
                    solution_en: "<p>10.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743489330370.png\" alt=\"rId32\" width=\"87\" height=\"84\"></p>",
                    solution_hi: "<p>10.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743489330370.png\" alt=\"rId32\" width=\"87\" height=\"84\"></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "6",
                    question_en: "<p>11. In a certain code language, \'RULE\' is written as \'VXNF\' and \'LEAD\' is written as \'PHCE\'. How will \'HERO\' be written in that language ?</p>",
                    question_hi: "<p>11. एक निश्चित कोड भाषा में \'RULE\' को \'VXNF\' लिखा जाता है और \'LEAD\' को \'PHCE\' लिखा जाता है। उसी भाषा में \'HERO\' कैसे लिखा जाएगा ?</p>",
                    options_en: [
                        "<p>MITP</p>",
                        "<p>MHUP</p>",
                        "<p>LHTP</p>",
                        "<p>LHUQ</p>"
                    ],
                    options_hi: [
                        "<p>MITP</p>",
                        "<p>MHUP</p>",
                        "<p>LHTP</p>",
                        "<p>LHUQ</p>"
                    ],
                    solution_en: "<p>11.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743489330522.png\" alt=\"rId33\" width=\"117\" height=\"82\"> ,&nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743489330658.png\" alt=\"rId34\" width=\"116\" height=\"82\"><br>Similarly,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743489330791.png\" alt=\"rId35\" width=\"113\" height=\"80\"></p>",
                    solution_hi: "<p>11.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743489330522.png\" alt=\"rId33\" width=\"117\" height=\"82\"> ,&nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743489330658.png\" alt=\"rId34\" width=\"116\" height=\"82\"><br>इसी प्रकार,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743489330791.png\" alt=\"rId35\" width=\"113\" height=\"80\"></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "6",
                    question_en: "<p>12. Select the correct mirror image of the given figure when the mirror is placed at MN as shown below.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743489330920.png\" alt=\"rId36\" width=\"131\" height=\"94\"></p>",
                    question_hi: "<p>12. दी गई आकृति के उस सही दर्पण प्रतिबिंब को चुनिए, जो नीचे दर्शाए गए अनुसार दर्पण को MN पर रखने पर बनेगा।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743489330920.png\" alt=\"rId36\" width=\"131\" height=\"94\"></p>",
                    options_en: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743489331085.png\" alt=\"rId37\" width=\"120\" height=\"26\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743489331184.png\" alt=\"rId38\" width=\"119\" height=\"26\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743489331300.png\" alt=\"rId39\" width=\"119\" height=\"26\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743489331461.png\" alt=\"rId40\" width=\"118\" height=\"26\"></p>"
                    ],
                    options_hi: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743489331085.png\" alt=\"rId37\" width=\"120\" height=\"26\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743489331184.png\" alt=\"rId38\" width=\"119\" height=\"26\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743489331300.png\" alt=\"rId39\" width=\"123\" height=\"27\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743489331461.png\" alt=\"rId40\" width=\"118\" height=\"26\"></p>"
                    ],
                    solution_en: "<p>12.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743489331461.png\" alt=\"rId40\" width=\"122\" height=\"27\"></p>",
                    solution_hi: "<p>12.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743489331461.png\" alt=\"rId40\" width=\"122\" height=\"27\"></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "6",
                    question_en: "<p>13. Select the option in which the numbers share the same relationship as that shared by the given pair of numbers.<br>(<strong>NOTE: </strong>Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g. 13 &ndash; Operations on 13 such as adding/deleting/multiplying etc. to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed.)<br>11 : 1331<br>9 : 729</p>",
                    question_hi: "<p>13. उस विकल्&zwj;प का चयन कीजिए जिसमें संख्&zwj;याओं के मध्&zwj;य वही संबंध है जो नीचे दिए गए युग्&zwj;म की संख्&zwj;याओं के मध्&zwj;य है।<br>(<strong>ध्यान दें:</strong> संख्याओं को उनके घटक अंकों में विभाजित किए बिना, पूर्ण संख्याओं पर संक्रियाएं की जानी चाहिए। उदाहरण के लिए 13 को लें- 13 पर की जाने वाली संक्रियाएं जैसे जोड़ना/ घटाना/ गुणा करना आदि केवल 13 पर की जा सकती हैं। 13 को 1 और 3 में तोड़ना और फिर 1 और 3 पर गणितीय संक्रियाएं करने की अनुमति नहीं है।)<br>11 : 1331<br>9 : 729</p>",
                    options_en: [
                        "<p>6 : 180</p>",
                        "<p>7 : 343</p>",
                        "<p>8 : 6561</p>",
                        "<p>13 : 2028</p>"
                    ],
                    options_hi: [
                        "<p>6 : 180</p>",
                        "<p>7 : 343</p>",
                        "<p>8 : 6561</p>",
                        "<p>13 : 2028</p>"
                    ],
                    solution_en: "<p>13.(b)<br><strong>Logic:- </strong>(1<sup>st</sup>no.)<sup>3</sup>&nbsp;= 2<sup>nd</sup>no.<br>(11, 1331) :- (11)<sup>3</sup>&nbsp;= 1331<br>(9, 729) :- (9)<sup>3</sup> = 729<br>similarly<br>(7, 343) :- (7)<sup>3</sup>&nbsp;= 343</p>",
                    solution_hi: "<p>13.(b)<br><strong>तर्क:- </strong>(पहली संख्या)<sup>3</sup>&nbsp;= दूसरी संख्या <br>(11, 1331) :- (11)<sup>3</sup>&nbsp;= 1331<br>(9, 729) :- (9)<sup>3</sup> = 729<br>इसी प्रकार <br>(7, 343) :- (7)<sup>3</sup> = 343</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "6",
                    question_en: "<p>14. What should come in place of the question mark (?) in the given series ?&nbsp;<br>66, 77, 99, 132, 176, ?</p>",
                    question_hi: "<p>14. दी गई श्रृंखला में प्रश्न चिह्न (?) के स्थान पर क्या आना चाहिए ?<br>66, 77, 99, 132, 176, ?</p>",
                    options_en: [
                        "<p>231</p>",
                        "<p>232</p>",
                        "<p>230</p>",
                        "<p>233</p>"
                    ],
                    options_hi: [
                        "<p>231</p>",
                        "<p>232</p>",
                        "<p>230</p>",
                        "<p>233</p>"
                    ],
                    solution_en: "<p>14.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743489331621.png\" alt=\"rId41\" width=\"296\" height=\"97\"></p>",
                    solution_hi: "<p>14.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743489331621.png\" alt=\"rId41\" width=\"296\" height=\"97\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "6",
                    question_en: "<p>15. What will come in the place of the question mark (?) in the following equation, if &lsquo;+&rsquo; and &lsquo;-&rsquo; are interchanged and &lsquo;&times;&rsquo; and &lsquo;&divide;&rsquo; are interchanged ?<br>34 + 56 &times; 8 &divide; 2 - 16 = ?</p>",
                    question_hi: "<p>15. यदि \'+\' और &lsquo;- &rsquo;को आपस में बदल दिया जाए तथा \'&times;\' और \'&divide;\' को आपस में बदल दिया जाए तो निम्नलिखित समीकरण में प्रश्न चिन्ह (?) के स्थान पर क्या आएगा ?<br>34 + 56 &times; 8 &divide; 2 - 16 = ?</p>",
                    options_en: [
                        "<p>23</p>",
                        "<p>36</p>",
                        "<p>38</p>",
                        "<p>30</p>"
                    ],
                    options_hi: [
                        "<p>23</p>",
                        "<p>36</p>",
                        "<p>38</p>",
                        "<p>30</p>"
                    ],
                    solution_en: "<p>15.(b) <strong>Given :-</strong> 34 + 56 &times; 8 &divide; 2 - 16<br>As per given instruction after interchanging &lsquo;+&rsquo; and &lsquo;-&rsquo; and &lsquo;&times;&rsquo; and &lsquo;&divide;&rsquo; we get<br>34 - 56 &divide; 8 &times; 2 + 16<br>34 - 7 &times; 2 + 16<br>34 - 14 + 16 = 36</p>",
                    solution_hi: "<p>15.(b) <strong>दिया गया :-</strong> 34 + 56 &times; 8 &divide; 2 - 16<br>दिए गए निर्देश के अनुसार \'+\' और \'-\' तथा \'&times;\' और \'&divide;\' को आपस में बदलने पर हमें प्राप्त होता है<br>34 - 56 &divide; 8 &times; 2 + 16<br>34 - 7 &times; 2 + 16<br>34 - 14 + 16 = 36</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "6",
                    question_en: "<p>16. Which of the following terms will replace the question mark (?) in the given series ?&nbsp;<br>BHVC, ZLTG, XPRK, ? , TXNS</p>",
                    question_hi: "<p>16. निम्नलिखित में से कौन-सा पद दी गई श्रृंखला में प्रश्न चिह्न (?) को प्रतिस्थापित करेगा ?<br>BHVC, ZLTG, XPRK, ?, TXNS</p>",
                    options_en: [
                        "<p>VTPO</p>",
                        "<p>VTPN</p>",
                        "<p>VPTN</p>",
                        "<p>VPTO</p>"
                    ],
                    options_hi: [
                        "<p>VTPO</p>",
                        "<p>VTPN</p>",
                        "<p>VPTN</p>",
                        "<p>VPTO</p>"
                    ],
                    solution_en: "<p>16.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743489331748.png\" alt=\"rId42\" width=\"351\" height=\"127\"></p>",
                    solution_hi: "<p>16.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743489331748.png\" alt=\"rId42\" width=\"351\" height=\"127\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "6",
                    question_en: "<p>17. Select the option in which the given figure is embedded. (Rotation is NOT allowed.)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743489331867.png\" alt=\"rId43\" width=\"119\" height=\"96\"></p>",
                    question_hi: "<p>17. विकल्प का चयन कीजिए जिसमें दी गई आकृति निहित है। (घूर्णन की अनुमति नहीं है)।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743489331867.png\" alt=\"rId43\" width=\"119\" height=\"96\"></p>",
                    options_en: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743489331985.png\" alt=\"rId44\" width=\"102\" height=\"96\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743489332108.png\" alt=\"rId45\" width=\"126\" height=\"79\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743489332249.png\" alt=\"rId46\" width=\"124\" height=\"82\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743489332356.png\" alt=\"rId47\" width=\"125\" height=\"87\"></p>"
                    ],
                    options_hi: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743489331985.png\" alt=\"rId44\" width=\"102\" height=\"96\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743489332108.png\" alt=\"rId45\" width=\"126\" height=\"79\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743489332249.png\" alt=\"rId46\" width=\"124\" height=\"82\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743489332356.png\" alt=\"rId47\" width=\"125\" height=\"87\"></p>"
                    ],
                    solution_en: "<p>17.(c) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743489332532.png\" alt=\"rId48\" width=\"142\" height=\"91\"></p>",
                    solution_hi: "<p>17.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743489332532.png\" alt=\"rId48\" width=\"142\" height=\"91\"></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "6",
                    question_en: "<p>18. All of the letters in the word &lsquo;JUNGLE&rsquo; are arranged in alphabetical order. How many letters are there in the English alphabetical series between the letter which is second from the left end and the one which is third from the right end in the new letter-cluster thus formed ?</p>",
                    question_hi: "<p>18. शब्द \'JUNGLE\' के सभी अक्षर वर्णानुक्रम में व्यवस्थित किये जाते हैं। इस प्रकार बने नए अक्षर-समूह में बाएं छोर से दूसरे और दाएं छोर से तीसरे अक्षर के बीच अंग्रेजी वर्णमाला क्रम में कितने अक्षर हैं ?</p>",
                    options_en: [
                        "<p>Three</p>",
                        "<p>Two</p>",
                        "<p>Five</p>",
                        "<p>Four</p>"
                    ],
                    options_hi: [
                        "<p>तीन</p>",
                        "<p>दो</p>",
                        "<p>पाँच</p>",
                        "<p>चार</p>"
                    ],
                    solution_en: "<p>18.(d) <strong>Given: </strong>JUNGLE<br>As per given instruction after arranging the letter alphabetically we get,- EGJLNU<br>The letter Second from the left end is &lsquo;G&rsquo; and third from the right end is &lsquo;L&rsquo; <br>Then, the number of letters between G and L is 4.</p>",
                    solution_hi: "<p>18.(d)<strong> दिया गया: </strong>JUNGLE<br>दिए गए निर्देश के अनुसार अक्षर को वर्णानुक्रम में व्यवस्थित करने पर हमें प्राप्त होता है, -EGJLNU,<br>बाएं छोर से दूसरा अक्षर \'G\' है और दाएं छोर से तीसरा अक्षर \'L\' है <br>फिर, G और L के बीच अक्षरों की संख्या 4 है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "6",
                    question_en: "<p>19. In a certain code language,<br>\'A @ B\' means &lsquo;A is the daughter of B&rsquo;,<br>\'A # B\' means &lsquo;A is the brother of B&rsquo;,<br>\'A + B\' means &lsquo;A is the wife of B&rsquo;,<br>\'A % B\' means &lsquo;A is the father of\' B&rsquo;.<br>How is M related to T if &lsquo;M # N @ P + S % T&rsquo; ?</p>",
                    question_hi: "<p>19. एक निश्चित कूट भाषा में,<br>\'A @ B\' का अर्थ है कि \'A, B की पुत्री है\',<br>\'A # B\' का अर्थ है कि \'A, B का भाई है\',<br>\'A + B\' का अर्थ है कि \'A, B की पत्नी है\',<br>\'A % B\' का अर्थ है कि \'A, B का पिता है\'।<br>यदि &lsquo;M # N @ P + S % T&rsquo; है तो M का T से क्या संबंध है ?</p>",
                    options_en: [
                        "<p>Father</p>",
                        "<p>Husband</p>",
                        "<p>Brother</p>",
                        "<p>Mother\'s brother</p>"
                    ],
                    options_hi: [
                        "<p>पिता</p>",
                        "<p>पति</p>",
                        "<p>भाई</p>",
                        "<p>मामा</p>"
                    ],
                    solution_en: "<p>19.(c) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743489332702.png\" alt=\"rId49\" width=\"176\" height=\"148\"><br>M is the brother of T.</p>",
                    solution_hi: "<p>19.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743489332702.png\" alt=\"rId49\" width=\"176\" height=\"148\"><br>M, T का भाई है |</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "6",
                    question_en: "<p>20. Which two numbers should be interchanged to make the given equation correct ?<br>79 &ndash; 43 + (23 + 75) &times; 3 &ndash; (30 &divide; 15) &times; 4 = 175<br>(<strong>NOTE : </strong>Numbers must be interchanged and not the constituent digits e.g. if 2 and 3&nbsp;are to be interchanged in the equation 43 &times; 3 + 4 &divide; 2, then interchanged equation is 43&nbsp;&times; 2 + 4 &divide; 3)</p>",
                    question_hi: "<p>20. दिए गए समीकरण को सही बनाने के लिए किन दो संख्याओं को आपस में बदला जाना चाहिए ?<br>79 &ndash; 43 + (23 + 75) &times; 3 &ndash; (30 &divide; 15) &times; 4 = 175<br>(<strong>ध्यान दें :</strong> संख्याओं को आपस में बदला जाना चाहिए और घटक अंकों को नहीं बदला जाना चाहिए।उदा. यदि समीकरण 43 &times; 3 + 4 &divide; 2 में 2 और 3 को आपस में बदलना है, तो बदला हुआ समीकरण 43 &times; 2 + 4 &divide; 3 होगा)</p>",
                    options_en: [
                        "<p>23 and 43</p>",
                        "<p>75 and 79</p>",
                        "<p>3 and 4</p>",
                        "<p>75 and 30</p>"
                    ],
                    options_hi: [
                        "<p>23 और 43</p>",
                        "<p>75 और 79</p>",
                        "<p>3 और 4</p>",
                        "<p>75 और 30</p>"
                    ],
                    solution_en: "<p>20.(d) <strong>Given :- </strong>79 - 43 + (23 + 75) &times; 3 - (30 &divide; 15) &times; 4 = 175<br>After checking all the options, option (d) satisfies. After interchanging 75 and 30 we get,<br>79 - 43 + (23 + 30) &times; 3 - (75 &divide; 15) &times; 4<br>79 - 43 + (53) &times; 3 - (5) &times; 4<br>36 + 159 - 20 = 175<br>L.H.S. = R.H.S.</p>",
                    solution_hi: "<p>20.(d) <strong>दिया गया :-</strong> 79 - 43 + (23 + 75) &times; 3 - (30 &divide; 15) &times; 4 = 175<br>सभी विकल्पों की जांच करने पर विकल्प (d) संतुष्ट करता है। 75 और 30 को आपस में बदलने पर हमें प्राप्त होता है,<br>79 - 43 + (23 + 30) &times; 3 - (75 &divide; 15) &times; 4<br>79 - 43 + (53) &times; 3 - (5) &times; 4<br>36 + 159 - 20 = 175<br>L.H.S. = R.H.S.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "6",
                    question_en: "<p>21. Read the given statements and conclusions carefully. Assuming that the information given in the statements is true, even if it appears to be at variance with commonly known facts, decide which of the given conclusions logically follow(s) from the statements.<br><strong>Statements :</strong><br>No cloud is a mountain.<br>All mountains are rivers.<br>All rivers are oceans.<br><strong>Conclusions :</strong><br>(I) All rivers can never be clouds.<br>(II) All mountains are oceans.</p>",
                    question_hi: "<p>21. दिए गए कथनों और निष्कर्षों को ध्यान पूर्वक पढ़ें। यह मानते हुए कि कथनों में दी गई जानकारी सत्य है,&nbsp;भले ही यह सामान्य रूप से ज्ञात तथ्यों से भिन्न प्रतीत होती हो, निर्णय लें कि दिए गए निष्कर्षों में से कौन सा/से निष्कर्ष कथनों का तार्किक रूप से अनुसरण करता है/करते हैं।<br><strong>कथन :</strong><br>कोई भी बादल, पर्वत नहीं है।<br>सभी पर्वत, नदियाँ हैं।<br>सभी नदियाँ, महासागर हैं।<br><strong>निष्कर्ष :</strong><br>(I) सभी नदियाँ कभी भी बादल नहीं हो सकती हैं।<br>(II) सभी पर्वत, महासागर हैं।</p>",
                    options_en: [
                        "<p>Both conclusions I and II follow</p>",
                        "<p>None of the conclusions follow</p>",
                        "<p>Only conclusion I follows</p>",
                        "<p>Only conclusion II follows</p>"
                    ],
                    options_hi: [
                        "<p>दोनों निष्कर्ष I और II अनुसरण करते हैं</p>",
                        "<p>कोई भी निष्कर्ष अनुसरण नहीं करता है</p>",
                        "<p>केवल निष्कर्ष I अनुसरण करता है</p>",
                        "<p>केवल निष्कर्ष ll अनुसरण करता है</p>"
                    ],
                    solution_en: "<p>21.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743489332843.png\" alt=\"rId50\" width=\"402\" height=\"114\"><br>Both conclusion I and II follow.</p>",
                    solution_hi: "<p>21.(a) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743489332962.png\" alt=\"rId51\" width=\"410\" height=\"116\"><br>निष्कर्ष I और II दोनों अनुसरण करते हैं।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "6",
                    question_en: "<p>22. Select the number from among the given options that can replace the question mark (?) in the following series.<br>13, ?, 25, 37, 53, 73</p>",
                    question_hi: "<p>22. दिए गए विकल्पों में से उस संख्या का चयन करें जो निम्नलिखित श्रृंखला में प्रश्न चिह्न (?) का स्थान लेसकती है।<br>13, ?, 25, 37, 53, 73</p>",
                    options_en: [
                        "<p>19</p>",
                        "<p>15</p>",
                        "<p>17</p>",
                        "<p>21</p>"
                    ],
                    options_hi: [
                        "<p>19</p>",
                        "<p>15</p>",
                        "<p>17</p>",
                        "<p>21</p>"
                    ],
                    solution_en: "<p>22.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743489333152.png\" alt=\"rId52\" width=\"271\" height=\"137\"></p>",
                    solution_hi: "<p>22.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743489333152.png\" alt=\"rId52\" width=\"271\" height=\"137\"></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "23",
                    section: "6",
                    question_en: "<p>23. Each of the letters in the word \'LASTING\' is arranged in the English alphabetical order. How many letters are there in the English alphabetical series between the letter which is second from the left end and the one which is fourth from the right end in the new letter-cluster thus formed ?</p>",
                    question_hi: "<p>23. शब्द \'LASTING\' के प्रत्येक अक्षर को अँग्रेजी वर्णमाला क्रम में व्यवस्थित किया जाता है। इस प्रकार बने नए अक्षर-समूह में बाएँ छोर से दूसरे अक्षर और दाएँ छोर से चौथे अक्षर के बीच अँग्रेजी वर्णमाला शृंखला में कितने अक्षर हैं ?</p>",
                    options_en: [
                        "<p>Five</p>",
                        "<p>Six</p>",
                        "<p>Three</p>",
                        "<p>Four</p>"
                    ],
                    options_hi: [
                        "<p>पाँच</p>",
                        "<p>छः</p>",
                        "<p>तीन</p>",
                        "<p>चार</p>"
                    ],
                    solution_en: "<p>23.(d) <strong>Given: </strong>LASTING<br>After arranging in english alphabetical order - AGILNST<br>Letter second from the left is &lsquo;G&rsquo; and fourth from the right end is &lsquo;L&rsquo;<br>Letter between &lsquo;G&rsquo; and &lsquo;L&rsquo; in english alphabet = 4(H, I, J, K)</p>",
                    solution_hi: "<p>23.(d) <strong>दिया है: </strong>LASTING<br>अंग्रेजी वर्णमाला क्रम में व्यवस्थित करने के बाद - AGILNST<br>बायीं ओर से दूसरा अक्षर \'G\' है और दायें छोर से चौथा अक्षर \'L\' है<br>अंग्रेजी वर्णमाला में \'G\' और \'L\' के बीच का अक्षर = 4(H, I, J, K)</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "24",
                    section: "6",
                    question_en: "<p>24. How many triangles are there in the given figure?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743489333290.png\" alt=\"rId53\" width=\"159\" height=\"81\"></p>",
                    question_hi: "<p>24. दी गई आकृति में कितने त्रिभुज हैं ?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743489333290.png\" alt=\"rId53\" width=\"159\" height=\"81\"></p>",
                    options_en: [
                        "<p>11</p>",
                        "<p>9</p>",
                        "<p>12</p>",
                        "<p>10</p>"
                    ],
                    options_hi: [
                        "<p>11</p>",
                        "<p>9</p>",
                        "<p>12</p>",
                        "<p>10</p>"
                    ],
                    solution_en: "<p>24.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743489333504.png\" alt=\"rId55\" width=\"196\" height=\"111\"><br>There are 10 triangle<br>ABM , BCM, CMD, DME, FEM, AMF, IHG, IJK, GLK, IGK</p>",
                    solution_hi: "<p>24.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743489333504.png\" alt=\"rId55\" width=\"196\" height=\"111\"><br>कुल 10 त्रिभुज हैं<br>ABM , BCM, CMD, DME, FEM, AMF, IHG, IJK, GLK, IGK</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "25",
                    section: "18",
                    question_en: "<p>25. A dice has numbers 1, 2, 3, 5, 6 and 8 on its sides. Two different positions of the same dice are shown below. Select the number that will be on the face opposite to the one having 6.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743489333593.png\" alt=\"rId56\" width=\"154\" height=\"76\"></p>",
                    question_hi: "<p>25. एक पासे के फलकों पर संख्याएँ 1, 2, 3, 5, 6 और 8 अंकित हैं। एक ही पासे की दो अलग-अलग स्थितियाँ नीचे दिखाई गई हैं। वह संख्या चुनिए, जो संख्या 6 वाले फलक के विपरीत फलक पर होगी।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743489333593.png\" alt=\"rId56\" width=\"154\" height=\"76\"></p>",
                    options_en: [
                        "<p>8</p>",
                        "<p>3</p>",
                        "<p>2</p>",
                        "<p>1</p>"
                    ],
                    options_hi: [
                        "<p>8</p>",
                        "<p>3</p>",
                        "<p>2</p>",
                        "<p>1</p>"
                    ],
                    solution_en: "<p>25.(a) From the both dices opposite faces are<br>5 &harr; 2, 3 &harr; 1, 6 &harr; 8</p>",
                    solution_hi: "<p>25.(a) दोनों पासों के विपरीत फलक हैं<br>5 &harr; 2, 3 &harr; 1, 6 &harr; 8</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "26",
                    section: "18",
                    question_en: "<p>26. In December 2024, which scheme was launched by the Indian government to promote long-haul cargo movement via inland waterways ?</p>",
                    question_hi: "<p>26. दिसंबर 2024 में भारतीय सरकार द्वारा किस योजना की शुरुआत की गई, जिसका उद्देश्य जलमार्गों के माध्यम से लंबी दूरी के माल परिवहन को बढ़ावा देना है ?</p>",
                    options_en: [
                        " Jal Marg Vikas            ",
                        " Sagarmala",
                        " Jalvahak            ",
                        " Bharatmala"
                    ],
                    options_hi: [
                        " जल मार्ग विकास      ",
                        " सागरमाला",
                        " जलवाहक                 ",
                        " भारतमाला"
                    ],
                    solution_en: "<p>26.(c) <strong>Jalvahak. </strong>The Jalvahak Scheme promotes long-distance cargo transport on key National Waterways with up to 35% cost reimbursement. A similar initiative, Jal Marg Vikas Project (2014), focuses on improving navigation on National Waterway 1 for eco-friendly transport.</p>",
                    solution_hi: "<p>26.(c) <strong>जलवाहक। </strong>जलवाहक योजना का उद्देश्य प्रमुख राष्ट्रीय जलमार्गों पर लंबी दूरी के माल परिवहन को बढ़ावा देना है, जिसमें 35% लागत की पुनर्भुगतान की सुविधा प्रदान की जाती है। इसी तरह की एक पहल, जल मार्ग विकास परियोजना (2014), का उद्देश्य राष्ट्रीय जलमार्ग 1 पर नेविगेशन को सुधारना है ताकि पर्यावरण के अनुकूल परिवहन को बढ़ावा दिया जा सके।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "27",
                    section: "18",
                    question_en: "<p>27. Who was the former Chief Minister of Haryana who passed away in December, 2024, at the age of 89 ?</p>",
                    question_hi: "<p>27. दिसंबर 2024 में 89 वर्ष की आयु में हरियाणा के किस पूर्व मुख्यमंत्री का निधन हो गया ?</p>",
                    options_en: [
                        "<p>Om Prakash Chautala</p>",
                        "<p>Bhupinder Singh Hooda</p>",
                        "<p>Devi Lal</p>",
                        "<p>Bansi Lal</p>"
                    ],
                    options_hi: [
                        "<p>ओम प्रकाश चौटाला</p>",
                        "<p>भूपेंद्र सिंह हुड्डा</p>",
                        "<p>देवी लाल</p>",
                        "<p>बंसी लाल</p>"
                    ],
                    solution_en: "<p>27.(a) <strong>Om Prakash Chautala. </strong>He was an Indian politician and the 7th Chief Minister of Haryana, representing the Indian National Lok Dal (INLD). He was the son of Chaudhary Devi Lal, the 6th Deputy Prime Minister of India.</p>",
                    solution_hi: "<p>27.(a) <strong>ओम प्रकाश चौटाला भा</strong>रतीय राजनीतिज्ञ और हरियाणा के 7वें मुख्यमंत्री थे, इन्होंने इंडियन नेशनल लोक दल (INLD) का प्रतिनिधित्व किया। वह भारत के 6वें उप प्रधानमंत्री चौधरी देवी लाल के पुत्र थे।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "28",
                    section: "18",
                    question_en: "<p>28. The Right to Freedom of Religion is covered under which Articles of the Constitution of India ?</p>",
                    question_hi: "28. धर्म की स्वतंत्रता का अधिकार भारत के संविधान के किन अनुच्छेदों के अंतर्गत आता है ? ",
                    options_en: [
                        " Articles 25-28",
                        " Articles 21-24",
                        " Articles 29-32",
                        " Articles 24-27"
                    ],
                    options_hi: [
                        " अनुच्छेद 25-28 ",
                        " अनुच्छेद 21-24 ",
                        " अनुच्छेद 29-32 ",
                        " अनुच्छेद 24-27 "
                    ],
                    solution_en: "<p>28.(a) <strong>Articles 25-28.</strong> Article 25: Freedom of conscience and the right to freely profess, practice, and propagate religion. Article 26: Freedom to manage religious affairs. Article 27: Freedom as to payment of taxes for promotion of any particular religion. Article 28 : Freedom as to attendance at religious instruction or religious worship in certain educational institutions.</p>",
                    solution_hi: "<p>28.(a) <strong>अनुच्छेद 25-28. </strong>अनुच्छेद 25 : अंतःकरण की और धर्म के अबाध रूप से मानने, आचरण और प्रचार करने की स्वतंत्रता। अनुच्छेद 26 : धार्मिक कार्यों के प्रबंध की स्वतंत्रता। अनुच्छेद 27 : किसी विशिष्ट धर्म की अभिवृद्धि के लिए करों के संदाय के बारे में स्वतंत्रता। अनुच्छेद 28 : कुछ शिक्षा संस्थानों में धार्मिक शिक्षा या धार्मिक उपासना में उपस्थित होने के बारे में स्वतंत्रता।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "29",
                    section: "18",
                    question_en: "<p>29. At which of the following places the written form of teachings of Jainism in which they are presently available, were written around 1500 years ago ?</p>",
                    question_hi: "<p>29. वर्तमान रूप में उपलब्ध जैन धर्म की शिक्षाओं का लिखित रूप लगभग 1500 वर्ष पूर्व निम्नलिखित में से किस स्थान पर लिखा गया था ?</p>",
                    options_en: [
                        " Pataliputra ",
                        " Shravanabelagola",
                        " Valabhi ",
                        " Mathura"
                    ],
                    options_hi: [
                        " पाटलिपुत्र (Pataliputra) ",
                        " श्रवणबेलगोला (Shravanabelagola) ",
                        " वल्लभी (Valabhi) ",
                        " मथुरा (Mathura) "
                    ],
                    solution_en: "<p>29.(c) <strong>Valabhi, </strong>an ancient city, was a prominent center for Jains and the site where the Vallabhi councils compiled the Jain religious canon (Jain Agams). The Third Jain Council, also known as Vallabhi Council II, was convened by monks of the Svetambara sect in Vallabhi, now in Gujarat, and presided over by Devarddhigani Kshamashramana.</p>",
                    solution_hi: "<p>29.(c)<strong> वल्लभी, </strong>एक प्राचीन नगर, जैनियों का एक प्रमुख केंद्र था और वह स्थान था जहाँ वल्लभी परिषदों ने जैन धर्मग्रंथों (जैन आगमों) का संकलित किया था। तीसरी जैन परिषद, जिसे वल्लभी परिषद द्वितीय के नाम से भी जाना जाता है, गुजरात के वल्लभी में श्वेतांबर संप्रदाय के भिक्षुओं द्वारा बुलाई गई थी, और जिसकी अध्यक्षता देवर्द्धिगणी क्षमाश्रमण ने की थी।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "30",
                    section: "18",
                    question_en: "<p>30. In which year did India first participate in the Paralympic Games ?</p>",
                    question_hi: "<p>30. भारत ने पहली बार पैरालंपिक खेलों (Paralympic Games) में किस वर्ष भाग लिया था ?&nbsp;</p>",
                    options_en: [
                        "<p>1964</p>",
                        "<p>1972</p>",
                        "<p>1968</p>",
                        "<p>1980</p>"
                    ],
                    options_hi: [
                        "<p>1964</p>",
                        "<p>1972</p>",
                        "<p>1968</p>",
                        "<p>1980</p>"
                    ],
                    solution_en: "<p>30.(c) <strong>1968.</strong> India has competed in every edition since 1984. The country debuted at the 1972 Games, where Murlikant Petkar won its first Paralympic gold in swimming. India achieved their most successful performance in Paralympic history at the Paris 2024 Games, securing a total of 29 medals - 7 gold, 9 silver, and 13 bronze.</p>",
                    solution_hi: "<p>30.(c) <strong>1968. </strong>भारत, 1984 से प्रत्येक संस्करण में भाग ले रहा है। देश ने 1972 के खेलों में अपनी शुरुआत की, जहाँ मुरलीकांत पेटकर ने तैराकी में अपना पहला पैरालिंपिक स्वर्ण पदक जीता था। भारत ने पेरिस 2024 खेलों में पैरालंपिक इतिहास में अपना सबसे सफल प्रदर्शन किया, जिसमें कुल 29 पदक - 7 स्वर्ण, 9 रजत और 13 कांस्य - हासिल किए।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "31",
                    section: "18",
                    question_en: "<p>31. Brihadiswara Temple of Tanjore was built by which of the following Chola emperors?</p>",
                    question_hi: "<p>31. तंजौर का बृहदीश्वर मंदिर निम्नलिखित में से किस चोल सम्राट द्वारा बनवाया गया था?</p>",
                    options_en: [
                        " Vijayalaya",
                        " Chamunda Raya",
                        " Rajendra I",
                        " Raja Raja Chola"
                    ],
                    options_hi: [
                        " विजयालय (Vijayalaya)",
                        " चामुंडा राय (Chamunda Raya)",
                        " राजेंद्र प्रथम (Rajendra I)",
                        " राजा राज चोल (Raja Raja Chola)"
                    ],
                    solution_en: "<p>31.(d) <strong>Raja Raja Chola. </strong>The Brihadisvara Temple (Dravidian-style) also known as Raja Rajeswara Temple is dedicated to Shiva. It was declared a UNESCO World Heritage Site in 1987. The Brihadisvara Temple at Gangaikondacholapuram was built by Rajendra I. The Airavatesvara Temple at Darasuram was built by Rajaraja Chola II. The Gomateshwara Temple (Bahubali) in Karnataka was built by Chamundaraya. Vijayalaya Chola was the founder of the Chola dynasty.</p>",
                    solution_hi: "<p>31.(d) <strong>राजा राज चोल। </strong>बृहदेश्वर मंदिर (द्रविड़ शैली) जिसे राजा राजेश्वर मंदिर के नाम से भी जाना जाता है, शिव को समर्पित है। इसे 1987 में यूनेस्को विश्व धरोहर स्थल घोषित किया गया था। गंगईकोंडचोलपुरम में बृहदेश्वर मंदिर राजेंद्र प्रथम द्वारा बनवाया गया था। दारासुरम में ऐरावतेश्वर मंदिर राजराज चोल द्वितीय द्वारा बनवाया गया था। कर्नाटक में गोमतेश्वर मंदिर (बाहुबली) चामुंडराय द्वारा बनाया गया था। विजयालय चोल, चोल वंश के संस्थापक थे।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "32",
                    section: "18",
                    question_en: "<p>32. Which material was largely used to build the stupa at Piprahwa in Uttar Pradesh ?</p>",
                    question_hi: "<p>32. उत्तर प्रदेश के पिपरहवा (Piprahwa) में स्तूप के निर्माण में मुख्यतः किस सामग्री का प्रयोग किया गया था ?</p>",
                    options_en: [
                        "<p>Bricks</p>",
                        "<p>Marble</p>",
                        "<p>Sandstone</p>",
                        "<p>Granite Stone</p>"
                    ],
                    options_hi: [
                        "<p>ईंट</p>",
                        "<p>संगमरमर</p>",
                        "<p>बलुआ पत्थर</p>",
                        "<p>ग्रेनाइट पत्थर</p>"
                    ],
                    solution_en: "<p>32.(a) <strong>Bricks.</strong> Piprahwa is a village near Birdpur in Siddharthnagar district of the Indian state of Uttar Pradesh. It is best known for its archaeological site and excavations that suggest that it may have been the burial place of the portion of the Buddha&rsquo;s ashes that were given to his own Sakya clan.</p>",
                    solution_hi: "<p>32.(a) <strong>ईंटें। </strong>पिपरहवा उत्तर प्रदेश राज्य के सिद्धार्थनगर जिले में बीरदपुर के पास एक गाँव है। यह अपने पुरातात्विक स्थल और उत्खनन के लिए सर्वाधिक प्रसिद्ध है, जिससे पता चलता है कि यह संभवतः बुद्ध की राख के उस भाग का दफ़न स्थल रहा होगा, जिसे उनके अपने शाक्य वंश को दे दिया गया था।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "33",
                    section: "18",
                    question_en: "<p>33. Which team did Haryana Steelers defeat to win their maiden Pro Kabaddi League (PKL) title in 2024 ?</p>",
                    question_hi: "<p>33. हरियाणा स्टीलर्स ने 2024 में अपनी पहली प्रो कबड्डी लीग (PKL) का खिताब जीतने के लिए किस टीम को हराया था ?</p>",
                    options_en: [
                        "<p>Bengal Warriors</p>",
                        "<p>Patna Pirates</p>",
                        "<p>Puneri Paltan</p>",
                        "<p>Dabang Delhi K.C.</p>"
                    ],
                    options_hi: [
                        "<p>बंगाल वॉरियर्स</p>",
                        "<p>पटना पाइरेट्स</p>",
                        "<p>पुनेरी पलटन</p>",
                        "<p>दबंग दिल्ली के.सी.</p>"
                    ],
                    solution_en: "<p>33.(b) <strong>Patna Pirates.</strong><br>Haryana Steelers clinched their first-ever Pro Kabaddi League (PKL) title by defeating Patna Pirates in the final match. They secured a 32-23 victory to claim the championship.</p>",
                    solution_hi: "<p>33.(b) <strong>पटना पाइरेट्स।</strong><br>हरियाणा स्टीलर्स ने पटना पाइरेट्स को फाइनल मैच में हराकर अपनी पहली प्रो कबड्डी लीग (PKL) चैंपियनशिप जीती। उन्होंने 32-23 के स्कोर से जीत हासिल कर खिताब अपने नाम किया।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "34",
                    section: "18",
                    question_en: "<p>34. Which of the following rivers originates from the Himalayas ?</p>",
                    question_hi: "<p>34. निम्नलिखित में से कौन-सी नदी हिमालय से निकलती है ?</p>",
                    options_en: [
                        " Godavari ",
                        " Tapi ",
                        " Narmada ",
                        " Ganga"
                    ],
                    options_hi: [
                        " गोदावरी ",
                        " तापी ",
                        " नर्मदा ",
                        " गंगा"
                    ],
                    solution_en: "<p>34.(d) <strong>Ganga. </strong>Origin: Gangotri glacier near Gaumukh (3,900 m) in the Uttarkashi district of Uttarakhand. Here, it is known as the Bhagirathi. It cuts through the Central and the Lesser Himalayas in narrow gorges. At Devprayag, the Bhagirathi meets the Alaknanda; hereafter, it is known as the Ganga (length 2,525 km). Other river origin: Godavari - Brahmagiri hills; Tapi - Satpura range; Narmada - Maikal ranges.</p>",
                    solution_hi: "<p>34.(d)&nbsp;<strong>गंगा। </strong>उद्गम: उत्तराखंड के उत्तरकाशी जिले में गौमुख (3,900 मीटर) के पास गंगोत्री ग्लेशियर। यहाँ, इसे भागीरथी के नाम से जाना जाता है। यह मध्य और लघु हिमालय को संकरी घाटियों में काटती है। देवप्रयाग में, भागीरथी अलकनंदा से मिलती है; इसके बाद, इसे गंगा (लंबाई 2,525 किमी) के नाम से जाना जाता है। अन्य नदी उद्गम: गोदावरी - ब्रह्मगिरी पहाड़ियाँ; तापी - सतपुड़ा पर्वतमाला; नर्मदा - मैकाल पर्वतमाला।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "35",
                    section: "18",
                    question_en: "<p>35. Who defeated Mohammad Ghori in the First Battle of Tarain fought in 1191 ?</p>",
                    question_hi: "<p>35. वर्ष 1191 में लड़े गए तराइन (Tarain) के प्रथम युद्ध में मोहम्मद गोरी को किसने हराया था ?</p>",
                    options_en: [
                        " Prithviraj Chauhan ",
                        " Yashovarman ",
                        " Vasudev ",
                        " Vidyadhar"
                    ],
                    options_hi: [
                        " पृथ्वीराज चौहान",
                        " यशोवर्मन",
                        " वासुदेव",
                        " विद्याधर"
                    ],
                    solution_en: "<p>35.(a) <strong>Prithviraj Chauhan.</strong> Jayachandra invited Muhammad Ghori to invade India (or to defeat Prithviraj). Prithviraj Chauhan defeated Muhammad Ghori in the first battle of Tarain or Thaneshwar. In 1192 AD Muhammad Ghori attacked and defeated Prithviraj in the second battle of Tarain.</p>",
                    solution_hi: "<p>35.(a) <strong>पृथ्वीराज चौहान।</strong> जयचंद्र ने मुहम्मद गोरी को भारत पर आक्रमण करने (पृथ्वीराज को हराने) के लिए आमंत्रित किया था। तराइन या थानेश्वर के प्रथम युद्ध में पृथ्वीराज चौहान ने मुहम्मद गोरी को हराया। 1192 ई. में मुहम्मद गोरी ने तराइन के द्वितीय युद्ध में पृथ्वीराज पर आक्रमण किया और उसे पराजित कर दिया।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "36",
                    section: "18",
                    question_en: "<p>36. The layer made up of unconsolidated rock, dust, soil, etc. that is deposited on a bed rock surface like a blanket is known as:</p>",
                    question_hi: "<p>36. असंघटित शैल, धूल, मृदा आदि से मिलकर बनी परत, जो कंबल की तरह आधार शैल पृष्ठ (bed rock surface) पर निक्षेपित होती है,_______ कहलाती है।</p>",
                    options_en: [
                        "<p>Talus</p>",
                        "<p>Regolith</p>",
                        "<p>Scree</p>",
                        "<p>Protolith</p>"
                    ],
                    options_hi: [
                        "<p>टैलस (Talus)</p>",
                        "<p>रेगोलिथ (Regolith)</p>",
                        "<p>स्क्री (Scree)</p>",
                        "<p>प्रोटोलिथ (Protolith)</p>"
                    ],
                    solution_en: "<p>36.(b) <strong>Regolith.</strong> It can be found both at the surface and at varying depths. This layer of fragmented soil material forms the outermost covering of the land and is present almost everywhere, covering the underlying bedrock. Regolith is primarily inorganic and acts like a blanket over unbroken rock.</p>",
                    solution_hi: "<p>36.(b) <strong>रेगोलिथ। </strong>यह सतह पर और अलग-अलग गहराई पर पाया जा सकता है। खंडित मृदा की यह परत भूमि का सबसे बाहरी आवरण बनाती है और लगभग हर जगह मौजूद होती है, जो अंतर्निहित आधार शैल पृष्ठ को ढकती है। रेगोलिथ मुख्य रूप से अकार्बनिक है और अखंडित चट्टान पर एक कंबल की तरह कार्य करता है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "37",
                    section: "18",
                    question_en: "<p>37. The black coating formed on silver jewellery is:</p>",
                    question_hi: "<p>37. चाँदी के आभूषणों पर बनने वाली काली परत क्या है ?</p>",
                    options_en: [
                        "<p>Ag<sub>2</sub>CO<sub>3</sub></p>",
                        "<p>AgNO<sub>3</sub></p>",
                        "<p>AgCl</p>",
                        "<p>Ag<sub>2</sub>S</p>"
                    ],
                    options_hi: [
                        "<p>Ag<sub>2</sub>CO<sub>3</sub></p>",
                        "<p>AgNO<sub>3</sub></p>",
                        "<p>AgCl</p>",
                        "<p>Ag<sub>2</sub>S</p>"
                    ],
                    solution_en: "<p>37.(d) <strong>Ag<sub>2</sub>S</strong> (silver sulfide) is the black coating that forms on silver jewelry due to a reaction with sulfur compounds in the air. Silver carbonate ( Ag<sub>2</sub>CO<sub>3</sub>) is a chemical compound of silver. It is used as a reagent in organic synthesis such as the Koenigs-Knorr reaction. Silver nitrate (AgNO<sub>3</sub>) appears as a crystalline solid that is white or colorless, but turns black when exposed to light or organic material. Silver chloride (AgCl) is a chloride of silver that occurs naturally as the mineral chlorargyrite. It is used to make photographic paper and pottery glazes.</p>",
                    solution_hi: "<p>37.(d) <strong>Ag<sub>2</sub>S </strong>(सिल्वर सल्फाइड) वह काले रंग की परत है जो चाँदी के आभूषणों पर वायुमंडल में उपस्थित सल्फर यौगिकों के साथ अभिक्रिया करने के कारण बनती है। सिल्वर कार्बोनेट (Ag<sub>2</sub>CO<sub>3</sub>) चाँदी का एक रासायनिक यौगिक है। इसे जैविक संश्लेषण में अभिकर्मक के रूप में उपयोग किया जाता है, जैसे कि कोनिग्स-नॉर अभिक्रिया में। सिल्वर नाइट्रेट (AgNO<sub>3</sub>) एक क्रिस्टलीय ठोस होता है जो सफेद या रंगहीन दिखाई देता है, लेकिन यह प्रकाश या जैविक पदार्थ के संपर्क में आने पर काला हो जाता है। सिल्वर क्लोराइड (AgCl) चाँदी का एक क्लोराइड है जो प्राकृतिक रूप से खनिज क्लोरार्जाइट के रूप में पाया जाता है। इसका उपयोग फोटोग्राफिक पेपर और मिट्टी के बर्तन बनाने में किया जाता है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "38",
                    section: "18",
                    question_en: "<p>38. Who wrote the patriotic song &lsquo;Sare Jahan Se Achha Hindustan Hamara&rsquo; that was published in the weekly journal Ittehad on 16 August 1904 ?</p>",
                    question_hi: "<p>38. 16 अगस्त 1904 को साप्ताहिक पत्रिका इत्तेहाद में प्रकाशित देशभक्ति गीत \'सारे जहां से अच्छा हिंदोस्तान हमारा\' किसने लिखा था ?</p>",
                    options_en: [
                        "<p>Muhammad Iqbal</p>",
                        "<p>Chandrasekhar Azad</p>",
                        "<p>Rabindranath Tagore</p>",
                        "<p>Sardar Bhagat Singh</p>"
                    ],
                    options_hi: [
                        "<p>मुहम्मद इकबाल</p>",
                        "<p>चंद्रशेखर आज़ाद</p>",
                        "<p>रविंद्रनाथ टैगोर</p>",
                        "<p>सरदार भगत सिंह</p>"
                    ],
                    solution_en: "<p>38.(a) <strong>Muhammad Iqbal</strong> (Allama Iqbal) is the National Poet of Pakistan. Iconic figures and their famous quotes: Bhagat Singh - &ldquo;Inquilab Zindabad&rdquo;. Subhash Chandra Bose - &ldquo;Jai Hind&rdquo;, &ldquo;Tum Mujhe Khoon Do, Mai Tumhe Azadi Dunga&rdquo;. Mahatma Gandhi - &ldquo;Karo ya Maro&rdquo;. Bankim Chandra Chatterjee - &ldquo;Vande Mataram&rdquo;. Pandit Madan Mohan Malviya - &ldquo;Satyameva Jayate&rdquo;.</p>",
                    solution_hi: "<p>38.(a) <strong>मुहम्मद इकबाल </strong>(अल्लामा इक़बाल) पाकिस्तान के राष्ट्रीय कवि हैं। प्रतिष्ठित शख्सियतें और उनके प्रसिद्ध उद्धरण (quotes) : भगत सिंह - \"इंकलाब जिंदाबाद\"। सुभाष चंद्र बोस - \"जय हिंद\", \"तुम मुझे ख़ून दो, मैं तुम्हे आज़ादी दूंगा\"। महात्मा गांधी - \"करो या मरो\"। बंकिम चंद्र चटर्जी - \"वंदे मातरम्\"। पंडित मदन मोहन मालवीय - \"सत्यमेव जयते\"।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "39",
                    section: "18",
                    question_en: "<p>39. Which of the following is considered unicellular green algae ?</p>",
                    question_hi: "<p>39. निम्नलिखित में से किसे, एककोशिकीय हरा शैवाल (unicellular green algae) माना जाता है ?</p>",
                    options_en: [
                        "<p>Oedogonium</p>",
                        "<p>Cladophora</p>",
                        "<p>Chlamydomonas</p>",
                        "<p>Chlorophyta</p>"
                    ],
                    options_hi: [
                        "<p>ओडोगोनियम (Oedogonium)</p>",
                        "<p>क्लैडोफोरा (Cladophora)</p>",
                        "<p>क्लैमाइडोमोनास (Chlamydomonas)</p>",
                        "<p>क्लोरोफाईटा (Chlorophyta)</p>"
                    ],
                    solution_en: "<p>39.(c) <strong>Chlamydomonas. </strong>Unicellular green algae are plant-like organisms that contain chlorophyll and are made up of a single cell: Chlamydomonas: A unicellular green algae with two flagella that can be found in freshwater, seawater, damp soil, stagnant water, and even snow. It can become so abundant that the entire body of water appears green. Chlorella: A unicellular green algae that is rich in proteins and is used as a food supplement. Volvox : A multicellular green algae that forms spherical colonies.</p>",
                    solution_hi: "<p>39.(c) <strong>क्लैमाइडोमोनास </strong>(Chlamydomonas)। एककोशिकीय हरे शैवाल पौधे जैसे जीव होते हैं जिनमें क्लोरोफिल होता है और यह एक ही कोशिका से बने होते हैं: क्लैमाइडोमोनास: दो कशाभिकाओं वाला एककोशिकीय हरे शैवाल जो मीठे जल, समुद्री जल, आर्द्र मृदा, स्थिर जल और यहाँ तक कि बर्फ में भी पाए जा सकते है। यह इतना अधिक मात्रा में मौजूद हो सकता है कि सम्पूर्ण जल क्षेत्र हरा दिखाई देने लगता है। क्लोरेला: एक एककोशिकीय हरे शैवाल जिसमें प्रोटीन की प्रचुर मात्रा पायी जाती है और इसका उपयोग खाद्य पूरक के रूप में किया जाता है। वोल्वॉक्स (Volvox) : एक बहुकोशिकीय हरे शैवाल जो गोलाकार कॉलोनियाँ बनाते है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "40",
                    section: "18",
                    question_en: "<p>40. When something accelerates along a circular path, ______keeps it going in the circle.</p>",
                    question_hi: "<p>40. जब कोई वस्तु एक वृत्ताकार पथ में त्वरित होती है, तो ______के कारण वस्तु उस वृत्ताकार पथ में गतिशील रहती है।</p>",
                    options_en: [
                        "<p>Friction</p>",
                        "<p>Centripetal force</p>",
                        "<p>Elastic force</p>",
                        "<p>Velocity</p>"
                    ],
                    options_hi: [
                        "<p>घर्षण</p>",
                        "<p>अभिकेन्द्र बल</p>",
                        "<p>प्रत्यास्थ बल</p>",
                        "<p>वेग</p>"
                    ],
                    solution_en: "<p>40.(b) <strong>Centripetal force. </strong>It is a force that acts towards the center of the circle, perpendicular to the direction of motion. It is necessary to counteract the tendency of the object to fly off in a straight line due to inertia. Friction is the force that opposes the relative motion between two surfaces in contact. Elastic force is the force exerted by a material when it is stretched or compressed and tries to return to its original shape. Velocity refers to the speed of an object in a particular direction.</p>",
                    solution_hi: "<p>40.(b) <strong>अभिकेन्द्र बल। </strong>यह एक ऐसा बल है जो गति की दिशा के लंबवत, वृत्त के केंद्र की ओर कार्य करता है। जड़त्व के कारण वस्तु के सीधी रेखा में उड़ने की प्रवृत्ति का प्रतिकार करना आवश्यक है। घर्षण वह बल है जो संपर्क में आने वाली दो सतहों के बीच सापेक्ष गति का विरोध करता है। प्रत्यास्थ बल वह बल है जो किसी पदार्थ द्वारा तब लगाया जाता है जब उसे खींचा या संपीड़ित किया जाता है तथा वह अपने मूल आकार में लौटने का प्रयास करता है।। वेग किसी वस्तु की एक विशेष दिशा में गति को संदर्भित करता है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "41",
                    section: "18",
                    question_en: "<p>41. The chemical compound present in turmeric is:</p>",
                    question_hi: "<p>41. हल्दी में निम्नलिखित में से कौन-सा रासायनिक यौगिक मौजूद होता है ?</p>",
                    options_en: [
                        "<p>curcumin</p>",
                        "<p>tartaric acid</p>",
                        "<p>carotene</p>",
                        "<p>capsaicin</p>"
                    ],
                    options_hi: [
                        "<p>करक्यूमिन (curcumin)</p>",
                        "<p>टार्टरिक अम्ल (tartaric acid)</p>",
                        "<p>कैरोटीन (carotene)</p>",
                        "<p>कैप्साइसिन (capsaicin)</p>"
                    ],
                    solution_en: "<p>41.(a) <strong>Curcumin</strong> (Diferuloylmethane), found in turmeric and Curcuma Xanthorrhiza oil, has antibacterial, anti-inflammatory, hypoglycemic, and antioxidant properties. It is effective in managing conditions like diabetes, arthritis, and oxidative stress. Studies also highlight its wound-healing and antimicrobial effects. As a pleiotropic molecule, curcumin influences various biological pathways, offering therapeutic potential in combating inflammation and promoting health.</p>",
                    solution_hi: "<p>41.(a) <strong>करक्यूमिन </strong>(डाइफेरूलोइलमीथेन) जो हल्दी और करक्यूमा जैन्थोरिजा तेल में पाए जाते हैं, इसमे जीवाणुरोधी, सूजनरोधी, हाइपोग्लाइसेमिक और एंटीऑक्सीडेंट गुण होते हैं। यह मधुमेह, गठिया और ऑक्सीडेटिव तनाव जैसी स्थितियों के प्रबंधन में प्रभावी है। अध्ययनों में इसके घाव भरने और रोगाणुरोधी प्रभावों में भी मदद करता है। एक बहुल अणु के रूप में, करक्यूमिन विभिन्न जैविक मार्गों को प्रभावित करता है, यह सूजन निवारण और स्वास्थ्य को बढ़ावा देने में चिकित्सीय क्षमता प्रदान करता है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "42",
                    section: "18",
                    question_en: "<p>42. Calculate the revenue receipts from the following information.<br>Revenue deficit= 2.80% of GDP Revenue expenditure= 10.80% of GDP</p>",
                    question_hi: "<p>42. निम्नलिखित जानकारी से राजस्व प्राप्तियों की गणना कीजिए।<br>राजस्व घाटा = जी . डी . पी . का 2.80% राजस्व व्यय = जी . डी . पी . का 10.80%</p>",
                    options_en: [
                        "<p>12.80</p>",
                        "<p>8.00</p>",
                        "<p>13.60</p>",
                        "<p>2.70</p>"
                    ],
                    options_hi: [
                        "<p>12.80</p>",
                        "<p>8.00</p>",
                        "<p>13.60</p>",
                        "<p>2.70</p>"
                    ],
                    solution_en: "<p>42.(b)<strong> 8.00. </strong>To calculate the Revenue Receipts, we need to use the given information about the Revenue Deficit and Revenue Expenditure as percentages of GDP.&nbsp;<br>Formula for Revenue Deficit : Revenue Deficit = Revenue Expenditure &minus; Revenue Receipts<br>Given :<br>Revenue Deficit = 2.80% of GDP<br>Revenue Expenditure = 10.80% of GDP<br>Now, We can set up an equation:<br>2.80% = 10.80% - Revenue Receipts<br>To find Revenue Receipts:<br>Revenue Receipts = 10.80% - 2.80% = 8.00%</p>",
                    solution_hi: "<p>42.(b)<strong> 8.00। </strong>राजस्व प्राप्तियों की गणना करने के लिए, हमें GDP के प्रतिशत के रूप में राजस्व घाटा और राजस्व व्यय के बारे में दी गई जानकारी का उपयोग करने की आवश्यकता है।<br>राजस्व घाटे का सूत्र : राजस्व घाटा = राजस्व व्यय - राजस्व प्राप्तियाँ<br>दिया गया :<br>राजस्व घाटा = GDP का 2.80%<br>राजस्व व्यय = GDP का 10.80%<br>अब, हम एक समीकरण बना सकते हैं:<br>2.80% = 10.80% - राजस्व प्राप्तियाँ<br>राजस्व प्राप्तियाँ ज्ञात करने के लिए:<br>राजस्व प्राप्तियाँ = 10.80% - 2.80% = 8.00%</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "43",
                    section: "18",
                    question_en: "<p>43. Which of the following banking institutions played an instrumental role in financial inclusion through microfinance loans and their expansion in India ?</p>",
                    question_hi: "<p>43. निम्नलिखित में से किस बैंकिंग संस्थान ने सूक्ष्मवित्त ऋणों (microfinance loans) के माध्यम से वित्तीय समावेशन और भारत में उनके विस्तार में महत्वपूर्ण भूमिका निभाई ?</p>",
                    options_en: [
                        "<p>State Bank of India</p>",
                        "<p>Reserve Bank of India</p>",
                        "<p>Central Bank of India</p>",
                        "<p>National Bank for Agriculture and Rural Development</p>"
                    ],
                    options_hi: [
                        "<p>भारतीय स्टेट बैंक</p>",
                        "<p>भारतीय रिज़र्व बैंक</p>",
                        "<p>सेन्ट्रल बैंक ऑफ इंडिया</p>",
                        "<p>राष्ट्रीय कृषि और ग्रामीण विकास बैंक</p>"
                    ],
                    solution_en: "<p>43.(d) <strong>National Bank for Agriculture and Rural Development</strong> (NABARD). It was established on 12 July 1982 during the Sixth Five-Year Plan, based on the recommendations of the B. Sivaramman Committee. Microfinance in India started through the Self-Help Group-Bank Linkage model which was an initiative by NABARD in 1992, to link the unorganised sector to the formal banking sector. The Reserve Bank of India (RBI) is the regulatory body for Micro Finance Institutions (MFIs) operating in the country.</p>",
                    solution_hi: "<p>43.(d) <strong>राष्ट्रीय कृषि और ग्रामीण विकास बैंक</strong> (NABARD)। इसकी स्थापना 12 जुलाई 1982 को छठी पंचवर्षीय योजना के दौरान बी. शिवरमन समिति की सिफारिशों के आधार पर की गई थी। भारत में माइक्रोफाइनेंस की शुरुआत स्व-सहायता समूह-बैंक लिंकेज मॉडल के माध्यम से हुई थी, जो 1992 में NABARD द्वारा असंगठित क्षेत्र को औपचारिक बैंकिंग क्षेत्र से जोड़ने के लिए एक पहल थी। भारतीय रिजर्व बैंक (RBI) देश में कार्यरत माइक्रो फाइनेंस संस्थानों (MFI) के लिए नियामक निकाय है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "44",
                    section: "18",
                    question_en: "<p>44. Which of the following is responsible for the red colour of beetroot ?</p>",
                    question_hi: "<p>44. चुकंदर के लाल रंग के होने के लिए निम्नलिखित में से कौन उत्तरदायी है ?</p>",
                    options_en: [
                        "<p>Curcumin</p>",
                        "<p>Betanin</p>",
                        "<p>Beta carotene</p>",
                        "<p>Lycopene</p>"
                    ],
                    options_hi: [
                        "<p>करक्यूमिन (Curcumin)</p>",
                        "<p>बीटानिन (Betanin)</p>",
                        "<p>बीटा कैरोटीन (Beta carotene)</p>",
                        "<p>लाइकोपीन (Lycopene)</p>"
                    ],
                    solution_en: "<p>44.(b) <strong>Betanin. </strong>The red color in beetroot is due to betacyanin, a water-soluble pigment located in the cell vacuole, not in chloroplasts. Curcumin : A yellow-orange pigment found in turmeric (Curcuma longa). Beta carotene : A yellow-orange pigment converted to vitamin A in the body, found in carrots and sweet potatoes. Lycopene is the red colored pigment abundantly found in red colored fruits and vegetables such as tomato, papaya, pink grapefruit, pink guava and watermelon.</p>",
                    solution_hi: "<p>44.(b) <strong>बीटानिन (Betanin)। </strong>चुकंदर में लाल रंग बीटासायनिन के कारण होता है, जो कोशिका रिक्तिका में स्थित एक जल-विलेय वर्णक है, जो कि क्लोरोप्लास्ट में नहीं बल्कि कोशिका रिक्तिका में पाया जाता है। करक्यूमिन : हल्दी (करक्यूमा लोंगा) में पाया जाने वाला एक पीला-नारंगी रंगद्रव्य है। बीटा कैरोटीन: शरीर में विटामिन A में परिवर्तित होने वाला एक पीला-नारंगी रंगद्रव्य, जो गाजर और शकरकंद में पाया जाता है। लाइकोपीन लाल रंग का वर्णक है जो टमाटर, पपीता, गुलाबी अंगूर, गुलाबी अमरूद और तरबूज जैसे लाल रंग के फलों और सब्जियों में प्रचुर मात्रा में पाया जाता है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "45",
                    section: "18",
                    question_en: "<p>45. Vijay Kichlu belongs to the ________.</p>",
                    question_hi: "<p>45. विजय किचलू&nbsp; _______से संबंधित हैं।</p>",
                    options_en: [
                        "<p>Bhendi Bazzar Gharana</p>",
                        "<p>Gwalior Gharana</p>",
                        "<p>Agra Gharana</p>",
                        "<p>Patiala Gharana</p>"
                    ],
                    options_hi: [
                        "<p>भिंडी बाजार घराना</p>",
                        "<p>ग्वालियर घराना</p>",
                        "<p>आगरा घराना</p>",
                        "<p>पटियाला घराना</p>"
                    ],
                    solution_en: "<p>45.(c)<strong> Agra Gharana.</strong> Vijay Kichlu was an Indian classical singer. Award - Padma Shri (2018). Agra Gharana was founded by Haji Sujan Khan and Ustad Gagghe Khuda Bakhsh. Other Exponents of Agra Gharana - Faiyaz Khan, Latafat Hussain Khan, Dinkar Kaikini and Lalith Rao.</p>",
                    solution_hi: "<p>45.(c) <strong>आगरा घराना। </strong>विजय किचलू एक भारतीय शास्त्रीय गायक थे। पुरस्कार - पद्म श्री (2018)। आगरा घराने की स्थापना हाजी सुजान खान और उस्ताद गग्गे खुदा बख्श ने की थी। आगरा घराने के अन्य प्रतिपादक - फ़ैयाज़ खान, लताफत हुसैन खान, दिनकर कैकिनी और ललित राव।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "46",
                    section: "18",
                    question_en: "<p>46. When was the Prime Minister&rsquo;s Employment Generation Programme (PMEGP) launched ?</p>",
                    question_hi: "<p>46. प्रधान मंत्री रोजगार सृजन कार्यक्रम (PMEGP) कब शुरू किया गया था ?</p>",
                    options_en: [
                        "<p>2008</p>",
                        "<p>2009</p>",
                        "<p>2006</p>",
                        "<p>2012</p>"
                    ],
                    options_hi: [
                        "<p>2008</p>",
                        "<p>2009</p>",
                        "<p>2006</p>",
                        "<p>2012</p>"
                    ],
                    solution_en: "<p>46.(a) <strong>2008.</strong> The Prime Minister\'s Employment Generation Programme (PMEGP) is a government scheme that aims to create employment opportunities in rural and urban areas. The scheme provides financial assistance for setting up self employment ventures, including for artisans and unemployed youth.</p>",
                    solution_hi: "<p>46.(a) <strong>​​2008.</strong> प्रधानमंत्री रोजगार सृजन कार्यक्रम (PMEGP) एक सरकारी योजना है जिसका उद्देश्य ग्रामीण और शहरी क्षेत्रों में रोजगार के अवसर उत्पन्न करना है। यह योजना कारीगरों और बेरोजगार युवाओं सहित स्वरोजगार उद्यम स्थापित करने के लिए वित्तीय सहायता प्रदान करती है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "47",
                    section: "18",
                    question_en: "<p>47. Terah taali dance belongs to which of the following states ?</p>",
                    question_hi: "<p>47. तेरह ताली नृत्य का संबंध निम्नलिखित में से किस राज्य से है ?</p>",
                    options_en: [
                        "<p>Kerala</p>",
                        "<p>Uttar Pradesh</p>",
                        "<p>Rajasthan</p>",
                        "<p>Uttaranchal</p>"
                    ],
                    options_hi: [
                        "<p>केरल</p>",
                        "<p>उत्तर प्रदेश</p>",
                        "<p>राजस्थान</p>",
                        "<p>उत्तरांचल</p>"
                    ],
                    solution_en: "<p>47.(c)<strong> Rajasthan.</strong> Terah Taali is performed by the Kamada tribes who are traditional snake charmers. Other popular folk dances of Rajasthan include: Ghoomar, Kalbeliya (Snake Charmer Dance), Kachchhi Ghodi, Bhavai, Kathputli, Bhopa, Chang, Gair.</p>",
                    solution_hi: "<p>47.(c)<strong> राजस्थान।</strong> तेरह ताली का प्रदर्शन कामदा जनजातियों द्वारा किया जाता है जो पारंपरिक सपेरे हैं। राजस्थान के अन्य लोकप्रिय लोक नृत्य : घूमर, कालबेलिया (स्नेक चार्मर नृत्य), कच्छी घोड़ी, भवई, कठपुतली, भोपा, चांग, ​​गैर।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "48",
                    section: "18",
                    question_en: "<p>48. The earthquakes that are generated due to the explosion of chemical or nuclear devices are known as :</p>",
                    question_hi: "<p>48. रासायनिक या परमाणु यंत्रों के विस्फोट के कारण उत्पन्न होने वाला भूकंप क्या कहलाता है ?</p>",
                    options_en: [
                        "<p>Collapse earthquakes</p>",
                        "<p>Explosion earthquakes</p>",
                        "<p>Tectonic earthquakes</p>",
                        "<p>Reservoir-induced earthquakes</p>"
                    ],
                    options_hi: [
                        "<p>निपात भूकंप (Collapse earthquakes)</p>",
                        "<p>विस्फोट भूकंप (Explosion earthquakes)</p>",
                        "<p>विवर्तननिक भूकंप (Tectonic earthquakes)</p>",
                        "<p>बांध-जनित भूकंप (Reservoir-induced earthquakes)</p>"
                    ],
                    solution_en: "<p>48.(b) <strong>Explosion earthquakes. </strong>An earthquake in simple words is the shaking of the earth. Tectonic earthquakes are produced by sudden movement along faults and plate boundaries. Reservoir-induced earthquakes are earthquakes caused by the physical processes that occur when large reservoirs are filled with water. Collapse earthquakes are small earthquakes in underground caverns and mines that are caused by seismic waves produced from the explosion of rock on the surface.</p>",
                    solution_hi: "<p>48.(b) <strong>विस्फोट भूकंप।</strong> भूकंप को सरल शब्दों में पृथ्वी का कंपन कहा जा सकता है। टेक्टोनिक भूकंप, भ्रंशों और प्लेट सीमाओं के साथ अचानक होने वाली हलचल से उत्पन्न होते हैं। जलाशय-प्रेरित भूकंप वे भूकंप हैं जो बड़े जलाशयों को पानी से भरने के दौरान होने वाली भौतिक प्रक्रियाओं के कारण होते हैं। निपात भूकंप भूमिगत गुफाओं और खदानों में छोटे भूकंप होते हैं जो सतह पर चट्टान के विस्फोट से उत्पन्न भूकंपीय तरंगों के कारण होते हैं।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "49",
                    section: "18",
                    question_en: "<p>49. Which organelle contains ribosomal RNA and is involved in the production of ribosomes ?</p>",
                    question_hi: "<p>49. किस कोशिकांग में राइबोसोमल आर.एन.ए. (ribosomal RNA) होता है और वह राइबोसोम के उत्पादन की प्रक्रिया में शामिल होता है ?</p>",
                    options_en: [
                        "<p>Endoplasmic reticulum</p>",
                        "<p>Nucleus</p>",
                        "<p>Nucleolus</p>",
                        "<p>Golgi apparatus</p>"
                    ],
                    options_hi: [
                        "<p>अन्तःप्रद्रव्यी जालिका (Endoplasmic reticulum)</p>",
                        "<p>केन्द्रक (Nucleus)</p>",
                        "<p>केन्द्रिका (Nucleolus)</p>",
                        "<p>गॉल्जीकाय (Golgi apparatus)</p>"
                    ],
                    solution_en: "<p>49.(c) <strong>Nucleolus </strong>is a region within the nucleus where ribosomal RNA (rRNA) is synthesized and ribosomes are assembled. It plays a crucial role in the production of ribosomes, which are essential for protein synthesis.Endoplasmic reticulum: involved in protein folding, lipid synthesis, and transport. Nucleus: contains most of the cell\'s genetic material, but the nucleolus is specifically responsible for ribosome production. Golgi apparatus: involved in protein modification, sorting, and packaging.</p>",
                    solution_hi: "<p>49.(c) <strong>केन्द्रिका </strong>केन्द्रक के अंदर एक क्षेत्र है जहाँ राइबोसोमल RNA (rRNA) संश्लेषित होता है तथा राइबोसोम को एकत्रित होता है। यह राइबोसोम के उत्पादन में एक महत्वपूर्ण भूमिका निभाता है, जो प्रोटीन संश्लेषण के लिए आवश्यक हैं। अन्तःप्रद्रव्यी जालिका : प्रोटीन वलय, लिपिड संश्लेषण एवं परिवहन में शामिल है। केन्द्रक : इसमें कोशिका के अधिकांश आनुवंशिक पदार्थ होते है, लेकिन केन्द्रिका विशेष रूप से राइबोसोम उत्पादन के लिए जिम्मेदार होती है। गॉल्जीकाय : प्रोटीन संशोधन, प्रवरण और पैकेजिंग में शामिल है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "50",
                    section: "17",
                    question_en: "<p>50. What is the title of the book that pays tribute to Justice A.N. Grover\'s legacy ?</p>",
                    question_hi: "<p>50. न्यायमूर्ति ए.एन. ग्रोवर की विरासत को समर्पित पुस्तक का शीर्षक क्या है ?</p>",
                    options_en: [
                        "<p>The Resolute Jurist</p>",
                        "<p>The Unyielding Judge</p>",
                        "<p>Justice and Legacy</p>",
                        "<p>The Life of A.N. Grover</p>"
                    ],
                    options_hi: [
                        "<p>द रेसोल्यूट ज्यूरिस्ट</p>",
                        "<p>द अनयील्डिंग जज</p>",
                        "<p>जस्टिस एंड लेगसी</p>",
                        "<p>द लाइफ ऑफ ए.एन. ग्रोवर</p>"
                    ],
                    solution_en: "<p>50.(b) <strong>The Unyielding Judge.</strong><br>The book honors Justice A.N. Grover\'s legacy and commitment to justice.</p>",
                    solution_hi: "<p>50.(b)<strong> द अनयील्डिंग जज।</strong> <br>यह पुस्तक न्यायमूर्ति ए.एन. ग्रोवर की विरासत और न्याय के प्रति उनकी प्रतिबद्धता को समर्पित है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "51",
                    section: "17",
                    question_en: "<p>51. If <math display=\"inline\"><mi>x</mi></math>&sup2; + y&sup2; = 280 and xy = 120, then find the value of <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>x</mi><mo>-</mo><mi>y</mi></mrow><mrow><mi>x</mi><mo>+</mo><mi>y</mi></mrow></mfrac></math> .</p>",
                    question_hi: "<p>51. यदि <math display=\"inline\"><mi>x</mi></math>&sup2; + y&sup2; = 280 और xy = 120 है, तो <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>x</mi><mo>-</mo><mi>y</mi></mrow><mrow><mi>x</mi><mo>+</mo><mi>y</mi></mrow></mfrac></math>&nbsp;का मान ज्ञात कीजिए।</p>",
                    options_en: [
                        "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><msqrt><mn>13</mn></msqrt></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mi>&#160;</mi><msqrt><mn>15</mn></msqrt></math></p>",
                        "<p><math display=\"inline\"><mi>&#160;</mi><msqrt><mn>13</mn></msqrt></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><msqrt><mn>15</mn></msqrt></mrow></mfrac></math></p>"
                    ],
                    options_hi: [
                        "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><msqrt><mn>13</mn></msqrt></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mi>&#160;</mi><msqrt><mn>15</mn></msqrt></math></p>",
                        "<p><math display=\"inline\"><mi>&#160;</mi><msqrt><mn>13</mn></msqrt></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><msqrt><mn>15</mn></msqrt></mrow></mfrac></math></p>"
                    ],
                    solution_en: "<p>51.(a)<br><math display=\"inline\"><mi>x</mi></math>&sup2; + y&sup2; = 280 and xy = 120<br><math display=\"inline\"><mi>x</mi></math>&sup2; + y&sup2; - 2xy = 280 - 2 &times; 120 = 40 &hellip;&hellip;&hellip;&hellip;&hellip;&hellip;..(i)<br><math display=\"inline\"><mi>x</mi></math>&sup2; + y&sup2; + 2xy = 280 + 2 &times; 120 = 520 &hellip;&hellip;&hellip;&hellip;&hellip;.(ii)<br>Now, on dividing (i) by (ii):<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>x</mi><mo>&#178;</mo><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>y</mi><mo>&#178;</mo><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>2</mn><mi>x</mi><mi>y</mi></mrow><mrow><mi>x</mi><mo>&#178;</mo><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>y</mi><mo>&#178;</mo><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>2</mn><mi>x</mi><mi>y</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>40</mn><mn>520</mn></mfrac></math> <br>&rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mfenced><mrow><mi>x</mi><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mi>y</mi></mrow></mfenced><mn>2</mn></msup><msup><mfenced><mrow><mi>x</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mi>y</mi></mrow></mfenced><mn>2</mn></msup></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>13</mn></mfrac></math><br>&rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mi>x</mi><mo>-</mo><mi>y</mi><mo>)</mo></mrow><mrow><mo>(</mo><mi>x</mi><mo>+</mo><mi>y</mi><mo>)</mo></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msqrt><mn>13</mn></msqrt></mfrac></math></p>",
                    solution_hi: "<p>51.(a)<br><math display=\"inline\"><mi>x</mi></math>&sup2; + y&sup2; = 280 और xy = 120<br><math display=\"inline\"><mi>x</mi></math>&sup2; + y&sup2; - 2xy = 280 - 2 &times; 120 = 40 &hellip;&hellip;&hellip;&hellip;&hellip;&hellip;..(i)<br><math display=\"inline\"><mi>x</mi></math>&sup2; + y&sup2; + 2xy = 280 + 2 &times; 120 = 520 &hellip;&hellip;&hellip;&hellip;&hellip;.(ii)<br>अब, (i) को (ii) से विभाजित करने पर:<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>x</mi><mo>&#178;</mo><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>y</mi><mo>&#178;</mo><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>2</mn><mi>x</mi><mi>y</mi></mrow><mrow><mi>x</mi><mo>&#178;</mo><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>y</mi><mo>&#178;</mo><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>2</mn><mi>x</mi><mi>y</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>40</mn><mn>520</mn></mfrac></math> <br>&rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mfenced><mrow><mi>x</mi><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mi>y</mi></mrow></mfenced><mn>2</mn></msup><msup><mfenced><mrow><mi>x</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mi>y</mi></mrow></mfenced><mn>2</mn></msup></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>13</mn></mfrac></math><br>&rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mi>x</mi><mo>-</mo><mi>y</mi><mo>)</mo></mrow><mrow><mo>(</mo><mi>x</mi><mo>+</mo><mi>y</mi><mo>)</mo></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msqrt><mn>13</mn></msqrt></mfrac></math></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "52",
                    section: "17",
                    question_en: "<p>52. If cot<sup>4</sup>ϕ - cot<sup>2</sup>ϕ = 1, then the value of cos<sup>4</sup>ϕ + cos<sup>2</sup>ϕ is:</p>",
                    question_hi: "<p>52. यदि cot<sup>4</sup>ϕ - cot<sup>2</sup>ϕ = 1 है, तो cos<sup>4</sup>ϕ + cos<sup>2</sup>ϕ का मान ज्ञात कीजिए।</p>",
                    options_en: [
                        "<p>2</p>",
                        "<p>1</p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><msqrt><mn>3</mn></msqrt></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>2</mn></mfrac></math></p>"
                    ],
                    options_hi: [
                        "<p>2</p>",
                        "<p>1</p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><msqrt><mn>3</mn></msqrt></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>2</mn></mfrac></math></p>"
                    ],
                    solution_en: "<p>52.(b)<br>cot<sup>4</sup>ϕ - cot<sup>2</sup>ϕ = 1<br>&rArr; cot<sup>4</sup>ϕ = 1 + cot<sup>2</sup>ϕ&nbsp;<br>&rArr; cot<sup>4</sup>ϕ = cosec<sup>2</sup>ϕ &hellip;..(cosec<sup>2</sup>ϕ =1 + cot<sup>2</sup>ϕ )<br>&rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi>cos</mi><mn>4</mn></msup><mi>&#981;</mi></mrow><mrow><msup><mi>sin</mi><mn>4</mn></msup><mi>&#981;</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><msup><mi>sin</mi><mn>2</mn></msup><mi>&#981;</mi></mrow></mfrac></math><br>&rArr; cos<sup>4</sup>ϕ = sin<sup>2</sup>ϕ &hellip;..(i)<br>Now,<br>cos<sup>4ϕ</sup>+ cos<sup>2</sup>ϕ = sin<sup>2</sup>ϕ + cos<sup>2</sup>ϕ = 1</p>",
                    solution_hi: "<p>52.(b)<br>cot<sup>4</sup>ϕ - cot<sup>2</sup>ϕ = 1<br>&rArr; cot<sup>4</sup>ϕ = 1 + cot<sup>2</sup>ϕ&nbsp;<br>&rArr; cot<sup>4</sup>ϕ = cosec<sup>2</sup>ϕ &hellip;..(cosec<sup>2</sup>ϕ =1 + cot<sup>2</sup>ϕ )<br>&rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi>cos</mi><mn>4</mn></msup><mi>&#981;</mi></mrow><mrow><msup><mi>sin</mi><mn>4</mn></msup><mi>&#981;</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><msup><mi>sin</mi><mn>2</mn></msup><mi>&#981;</mi></mrow></mfrac></math><br>&rArr; cos<sup>4</sup>ϕ = sin<sup>2</sup>ϕ &hellip;..(i)<br>अब ,<br>cos<sup>4ϕ</sup>+ cos<sup>2</sup>ϕ = sin<sup>2</sup>ϕ + cos<sup>2</sup>ϕ = 1</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "53",
                    section: "17",
                    question_en: "<p>53. 12.75% is equivalent to _______.</p>",
                    question_hi: "<p>53. 12.75% ______के समतुल्य है।</p>",
                    options_en: [
                        "<p><math display=\"inline\"><mfrac><mrow><mn>41</mn></mrow><mrow><mn>400</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>17</mn></mrow><mrow><mn>200</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>51</mn></mrow><mrow><mn>400</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>37</mn></mrow><mrow><mn>400</mn></mrow></mfrac></math></p>"
                    ],
                    options_hi: [
                        "<p><math display=\"inline\"><mfrac><mrow><mn>41</mn></mrow><mrow><mn>400</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>17</mn></mrow><mrow><mn>200</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>51</mn></mrow><mrow><mn>400</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>37</mn></mrow><mrow><mn>400</mn></mrow></mfrac></math></p>"
                    ],
                    solution_en: "<p>53.(c) <br>12.75% = <math display=\"inline\"><mfrac><mrow><mn>1275</mn></mrow><mrow><mn>10000</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>51</mn><mn>400</mn></mfrac></math></p>",
                    solution_hi: "<p>53.(c) <br>12.75% = <math display=\"inline\"><mfrac><mrow><mn>1275</mn></mrow><mrow><mn>10000</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>51</mn><mn>400</mn></mfrac></math></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "54",
                    section: "17",
                    question_en: "<p>54. If the 6 digit number 7002 * 4 is completely divisible by 8, then the smallest integer in place of * will be:</p>",
                    question_hi: "<p>54. यदि 6 अंकीय संख्या 7002*4, संख्या 8 से पूर्णतः विभाज्य है, तो * के स्थान पर आने वाला सबसे छोटा पूर्णांक क्या होगा ?</p>",
                    options_en: [
                        "<p>0</p>",
                        "<p>2</p>",
                        "<p>4</p>",
                        "<p>6</p>"
                    ],
                    options_hi: [
                        "<p>0</p>",
                        "<p>2</p>",
                        "<p>4</p>",
                        "<p>6</p>"
                    ],
                    solution_en: "<p>54.(b)<br><strong>Divisibility rule of 8 ;</strong><br>For any number to be divisible by 8, its last 3 digits should be divisible by 8.<br>For 7002x4, 2x4 should be divisible by 8<br>For this, x&nbsp;must be 2.</p>",
                    solution_hi: "<p>54.(b)<br><strong>8 का विभाज्यता नियम;</strong><br>किसी भी संख्या को 8 से विभाज्य होने के लिए उसके अंतिम 3 अंक 8 से विभाज्य होने चाहिए।<br>7002x4 के लिए, 2x4 को 8 से विभाज्य होना चाहिए<br>इसके लिए x&nbsp;= 2 होना चाहिए.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "55",
                    section: "17",
                    question_en: "<p>55. What is the Least Common Multiple of 2<sup>4</sup> &times; 3<sup>3</sup>, 2<sup>5</sup> &times; 3<sup>2</sup> and 2<sup>5</sup> &times; 3<sup>6</sup> ?</p>",
                    question_hi: "<p>55. 2<sup>4</sup> &times; 3<sup>3</sup>, 2<sup>5</sup> &times; 3<sup>2</sup> और 2<sup>5</sup> &times; 3<sup>6</sup> का लघुत्तम समापवर्त्य क्या है ?</p>",
                    options_en: [
                        "<p>2<sup>5</sup></p>",
                        "<p>2<sup>5</sup> &times; 3<sup>6</sup></p>",
                        "<p>2<sup>3</sup> &times; 3<sup>5</sup></p>",
                        "<p>2<sup>5</sup> &times; 3<sup>5</sup></p>"
                    ],
                    options_hi: [
                        "<p>2<sup>5</sup></p>",
                        "<p>2<sup>5</sup> &times; 3<sup>6</sup></p>",
                        "<p>2<sup>3</sup> &times; 3<sup>5</sup></p>",
                        "<p>2<sup>5</sup> &times; 3<sup>5</sup></p>"
                    ],
                    solution_en: "<p>55.(b)<br>a = 2<sup>4</sup> &times; 3<sup>3</sup><br>b = 2<sup>5</sup> &times; 3<sup>2</sup> <br>c = 2<sup>5</sup> &times; 3<sup>6</sup><br>LCM of (a, b, c) = 2<sup>5 </sup>&times; 3<sup>6</sup></p>",
                    solution_hi: "<p>55.(b)<br>a = 2<sup>4</sup> &times; 3<sup>3</sup><br>b = 2<sup>5</sup> &times; 3<sup>2</sup> <br>c = 2<sup>5</sup> &times; 3<sup>6</sup><br>(a, b, c) का LCM = 2<sup>5</sup> &times; 3<sup>6</sup></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "56",
                    section: "17",
                    question_en: "<p>56. Two pipes A and B can fill a tank in 20 and 30 hours, respectively. Both pipes are opened to fill the tank, but when the tank is one-third full, a leak develops through which one-fourth of the water supplied by both pipes goes out. Find the total time (in hours) taken to fill the tank.</p>",
                    question_hi: "<p>56. दो पाइप A और B एक टंकी को क्रमशः 20 और 30 घंटे में भर सकते हैं। टंकी को भरने के लिए दोनों पाइप खोले जाते हैं, लेकिन जब टंकी एक तिहाई भर जाती है, तो एक रिसाव विकसित होता है जिससे दोनों पाइपों द्वारा भरा गया एक-चौथाई पानी निकल जाता है। टंकी को भरने में लगने वाला कुल समय (घंटे में) ज्ञात कीजिए।</p>",
                    options_en: [
                        "<p>14<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>",
                        "<p>14</p>",
                        "<p>11<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math></p>",
                        "<p>12<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>"
                    ],
                    options_hi: [
                        "<p>14<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>",
                        "<p>14</p>",
                        "<p>11<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math></p>",
                        "<p>12<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>"
                    ],
                    solution_en: "<p>56.(a) <br>Two pipes can separately fill a tank in 20 hrs and 30 hrs respectively<br>Time taken by the two pipes to fill the tank = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>20</mn><mo>&#215;</mo><mn>30</mn></mrow><mrow><mn>20</mn><mo>+</mo><mn>30</mn><mi>&#160;</mi></mrow></mfrac></math> = 12 hours <br><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> of the tank if filled = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>12</mn><mn>3</mn></mfrac></math> = 4 hours <br>Now , <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> of the supplied water leak out<br><math display=\"inline\"><mo>&#8658;</mo></math> the filler pipes earlier efficiency = 1 - <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>4</mn></mfrac></math> units&nbsp;<br>Work done complete = (12 - 4) &times; <math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>32</mn><mn>3</mn></mfrac></math> = 10<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> hours <br>So, Total time = 4 + 10<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> = 14<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> hours</p>",
                    solution_hi: "<p>56.(a) दो पाइप अलग-अलग एक टैंक को क्रमशः 20 घंटे और 30 घंटे में भर सकते हैं<br>दोनों पाइपों द्वारा टंकी को भरने में लगा समय = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>20</mn><mo>&#215;</mo><mn>30</mn></mrow><mrow><mn>20</mn><mo>+</mo><mn>30</mn><mi>&#160;</mi></mrow></mfrac></math>&nbsp;= 12 घंटे <br>टंकी का&nbsp;<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> भाग भरने में लगा समय = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>12</mn><mn>3</mn></mfrac></math> = 4 घंटे&nbsp;<br>अब, आपूर्ति किए गए पानी में से <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> लीक हो जाता है<br><math display=\"inline\"><mo>&#8658;</mo></math> भराव पाइप की पिछली दक्षता = 1 - <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>4</mn></mfrac></math> इकाई<br>पूरा कार्य = (12 - 4) &times; <math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>32</mn><mn>3</mn></mfrac></math> = 10<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> घंटे <br>अत: कुल समय = 4 + 10<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> = 14<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> घंटे</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "57",
                    section: "17",
                    question_en: "<p>57. A shopkeeper marked an article at ₹5,000. The shopkeeper allows successive discounts of 20%,15% and 10%. The selling price of the article is:</p>",
                    question_hi: "<p>57. एक दुकानदार ने एक वस्तु पर ₹5,000 मूल्&zwj;य अंकित किया। दुकानदार 20%, 15% और 10% की क्रमिक छूट देता है। वस्तु का विक्रय मूल्य क्&zwj;या है ?</p>",
                    options_en: [
                        "<p>₹2,750</p>",
                        "<p>₹3,000</p>",
                        "<p>₹2,800</p>",
                        "<p>₹3,060</p>"
                    ],
                    options_hi: [
                        "<p>₹2,750</p>",
                        "<p>₹3,000</p>",
                        "<p>₹2,800</p>",
                        "<p>₹3,060</p>"
                    ],
                    solution_en: "<p>57.(d)<br>Selling Price = 5000 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>5</mn></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>17</mn><mn>20</mn></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>9</mn><mn>10</mn></mfrac></math> = ₹ 3,060</p>",
                    solution_hi: "<p>57.(d)<br>विक्रय मूल्य = 5000 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>5</mn></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>17</mn><mn>20</mn></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>9</mn><mn>10</mn></mfrac></math> = ₹ 3,060</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "58",
                    section: "17",
                    question_en: "<p>58. The given bar graph shows the number of students enrolled in Institutes A and B during 5 years (2018 to 2022).<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743489333765.png\" alt=\"rId57\" width=\"345\" height=\"261\"> <br>What is the ratio of the total students enrolled in Institute B in 2019, 2020 and 2022 to that of the total students enrolled in Institute A in 2018, 2020 and 2021 ?</p>",
                    question_hi: "<p>58. दिया गया बार ग्राफ 5 वर्षों (2018 से 2022) के दौरान संस्थान A और B में नामांकित छात्रों की संख्या को दर्शाता है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743489333886.png\" alt=\"rId58\" width=\"303\" height=\"229\"> <br>2019, 2020 और 2022 में संस्थान B में नामांकित छात्रों की कुल संख्या और 2018, 2020 और 2021 में संस्थान A में नामांकित छात्रों की कुल संख्या का अनुपात ज्ञात करें।</p>",
                    options_en: [
                        "<p>37 : 28</p>",
                        "<p>11 : 28</p>",
                        "<p>28 : 11</p>",
                        "<p>28 : 37</p>"
                    ],
                    options_hi: [
                        "<p>37 : 28</p>",
                        "<p>11 : 28</p>",
                        "<p>28 : 11</p>",
                        "<p>28 : 37</p>"
                    ],
                    solution_en: "<p>58.(a)<br>Students enrolled in institute B in 2019, 2020 and 2022 = 350 + 375 + 200 = 925<br>Students enrolled in institute A in 2018, 2020 and 2021 = 150 + 300 + 250 = 700<br>So, required ratio = 925 : 700 = 37 : 28</p>",
                    solution_hi: "<p>58.(a)<br>2019, 2020 और 2022 में संस्थान B में नामांकित छात्र = 350 + 375 + 200 = 925<br>2018, 2020 और 2021 में संस्थान A में नामांकित छात्र = 150 + 300 + 250 = 700<br>तो, आवश्यक अनुपात = 925 : 700 = 37 : 28</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "59",
                    section: "17",
                    question_en: "<p>59. The ratio of the diameters of two spheres is given as 1 : 4. The larger sphere is melted&nbsp;and 125 identical spheres are made out of the molten material. The smaller sphere is&nbsp;melted and 27 identical spheres are made out of the molten material. If the ratio of the&nbsp;volume of each of the 125 identical spheres to the volume of each of the 27 identical&nbsp;spheres is given as 1 : m, what is the value of m ?</p>",
                    question_hi: "<p>59. दो गोलों के व्यासों का अनुपात 1 : 4 दिया गया है। बड़े गोले को पिघलाया जाता है और पिघले हुए पदार्थ से 125 समरूप गोले बनाए जाते हैं। छोटे गोले को पिघलाया जाता है और पिघले हुए पदार्थ से 27 समरूप गोले बनाए जाते हैं। यदि 125 समरूप गोलों में से प्रत्येक के आयतन का, 27 समरूप गोलों में से प्रत्येक के आयतन से अनुपात 1 : m दिया गया है, तो m का मान क्या होगा ?</p>",
                    options_en: [
                        "<p><math display=\"inline\"><mfrac><mrow><mn>1728</mn></mrow><mrow><mn>125</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>125</mn></mrow><mrow><mn>1728</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>27</mn></mrow><mrow><mn>8000</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>64</mn></mrow><mrow><mn>3375</mn></mrow></mfrac></math></p>"
                    ],
                    options_hi: [
                        "<p><math display=\"inline\"><mfrac><mrow><mn>1728</mn></mrow><mrow><mn>125</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>125</mn></mrow><mrow><mn>1728</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>27</mn></mrow><mrow><mn>8000</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>64</mn></mrow><mrow><mn>3375</mn></mrow></mfrac></math></p>"
                    ],
                    solution_en: "<p>59.(b)<br>Let the radius of larger and smaller spheres 2x&nbsp;and <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>x</mi><mn>2</mn></mfrac></math> respectively.<br><strong>1st condition :-</strong><br>Volume of larger spheres = 125 &times; volume of identical spheres</p>\n<p>&rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>3</mn></mfrac></math>&pi;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msubsup><mi>R</mi><mn>1</mn><mn>3</mn></msubsup></math> = 125 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>3</mn></mfrac></math>&pi;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msubsup><mi>r</mi><mn>1</mn><mn>3</mn></msubsup></math></p>\n<p>&rArr; <math display=\"inline\"><msup><mrow><mo>(</mo><mn>2</mn><mi>x</mi><mo>)</mo></mrow><mrow><mn>3</mn></mrow></msup></math> = 125 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msubsup><mi>r</mi><mn>1</mn><mn>3</mn></msubsup></math></p>\n<p>&rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msubsup><mi>r</mi><mn>1</mn><mn>3</mn></msubsup></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mrow><mo>(</mo><mn>2</mn><mi>x</mi><mo>)</mo></mrow><mn>3</mn></msup><mn>125</mn></mfrac></math><br><strong>2nd condition :-</strong><br>Volume of smaller spheres = 27 &times; volume of identical spheres<br>&rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>3</mn></mfrac></math>&pi;(<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msubsup><mi>R</mi><mn>2</mn><mn>3</mn></msubsup></math>) = 27 &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>3</mn></mfrac></math>&pi;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msubsup><mi>r</mi><mn>2</mn><mn>3</mn></msubsup></math></p>\n<p>&rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>3</mn></mfrac></math>&pi;(<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"true\"><mfrac><mi>x</mi><mn>2</mn></mfrac></mstyle></math>)<sup>3</sup> = 27 &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>3</mn></mfrac></math>&pi;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msubsup><mi>r</mi><mn>2</mn><mn>3</mn></msubsup></math></p>\n<p>&rArr; <math display=\"inline\"><msubsup><mrow><mi>r</mi></mrow><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></msubsup></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mrow><mo>(</mo><mstyle displaystyle=\"true\"><mfrac><mi>x</mi><mn>2</mn></mfrac></mstyle><mo>)</mo></mrow><mn>3</mn></msup><mn>27</mn></mfrac></math><br>Ratio of of the volume = <math display=\"inline\"><mfrac><mrow><msup><mrow><mo>(</mo><mn>2</mn><mi>x</mi><mo>)</mo></mrow><mrow><mn>3</mn></mrow></msup></mrow><mrow><mn>125</mn></mrow></mfrac></math> : <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mrow><mo>(</mo><mstyle displaystyle=\"true\"><mfrac><mi>x</mi><mn>2</mn></mfrac></mstyle><mo>)</mo></mrow><mn>3</mn></msup><mn>27</mn></mfrac></math><br>= <math display=\"inline\"><mfrac><mrow><msup><mrow><mo>(</mo><mn>2</mn><mi>x</mi><mo>)</mo></mrow><mrow><mn>3</mn></mrow></msup></mrow><mrow><mn>125</mn></mrow></mfrac></math> : <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mrow><mo>(</mo><mi>x</mi><mo>)</mo></mrow><mn>3</mn></msup><mrow><mn>27</mn><mo>&#215;</mo><mn>8</mn></mrow></mfrac></math> = 1728 : 125<br>According to question<br>&rArr; <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mi>m</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1728</mn><mn>125</mn></mfrac></math><br>Hence the value of m = <math display=\"inline\"><mfrac><mrow><mn>125</mn></mrow><mrow><mn>1728</mn></mrow></mfrac></math><br><strong>Alternate method :</strong></p>\n<p>&rArr; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mfrac><mrow><mi>v</mi><mi>o</mi><mi>l</mi><mi>u</mi><mi>m</mi><mi>e</mi><mi>&#160;</mi><mi>o</mi><mi>f</mi><mi>&#160;</mi><mi>l</mi><mi>a</mi><mi>g</mi><mi>e</mi><mi>r</mi><mi>&#160;</mi><mi>s</mi><mi>p</mi><mi>h</mi><mi>e</mi><mi>r</mi><mi>e</mi></mrow><mn>125</mn></mfrac><mfrac><mrow><mi>v</mi><mi>o</mi><mi>l</mi><mi>u</mi><mi>m</mi><mi>e</mi><mi>&#160;</mi><mi>o</mi><mi>f</mi><mi>&#160;</mi><mi>s</mi><mi>m</mi><mi>a</mi><mi>l</mi><mi>l</mi><mi>e</mi><mi>r</mi><mi>&#160;</mi><mi>s</mi><mi>p</mi><mi>h</mi><mi>e</mi><mi>r</mi><mi>e</mi></mrow><mn>27</mn></mfrac></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>m</mi></mfrac></math></p>\n<p>&rArr; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"true\"><mfrac><mstyle displaystyle=\"true\"><mfrac><mrow><mfrac><mn>4</mn><mn>3</mn></mfrac><mi>&#960;</mi><mo>&#215;</mo><mo>(</mo><mn>2</mn><msup><mo>)</mo><mn>3</mn></msup></mrow><mn>125</mn></mfrac></mstyle><mstyle displaystyle=\"true\"><mfrac><mrow><mfrac><mn>4</mn><mn>3</mn></mfrac><mi>&#960;</mi><mo>&#215;</mo><msup><mfenced><mfrac><mn>1</mn><mn>2</mn></mfrac></mfenced><mn>3</mn></msup></mrow><mn>27</mn></mfrac></mstyle></mfrac></mstyle></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>m</mi></mfrac></math></p>\n<p>&rArr; <math display=\"inline\"><mfrac><mrow><mo>(</mo><mn>2</mn><msup><mrow><mo>)</mo></mrow><mrow><mn>3</mn></mrow></msup><mo>&#215;</mo><mn>27</mn></mrow><mrow><mo>(</mo><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac><msup><mrow><mo>)</mo></mrow><mrow><mn>3</mn></mrow></msup><mo>&#215;</mo><mn>125</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>m</mi></mfrac></math></p>\n<p>&rArr;&nbsp;<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>8</mn><mo>&#215;</mo><mn>27</mn><mo>&#215;</mo><mn>8</mn></mrow><mn>125</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>m</mi></mfrac></math></p>\n<p>&rArr; m =&nbsp;<math display=\"inline\"><mfrac><mrow><mn>125</mn></mrow><mrow><mn>1728</mn></mrow></mfrac></math></p>",
                    solution_hi: "<p>59.(b)<br>माना बड़े और छोटे गोले का त्रिज्या क्रमशः 2x&nbsp;और <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>x</mi><mn>2</mn></mfrac></math> है।<br><strong>पहली शर्त :-</strong><br>बड़े गोले का आयतन = 125 &times; समरूप गोले का आयतन</p>\n<p>&rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>3</mn></mfrac></math>&pi;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msubsup><mi>R</mi><mn>1</mn><mn>3</mn></msubsup></math> = 125 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>3</mn></mfrac></math>&pi;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msubsup><mi>r</mi><mn>1</mn><mn>3</mn></msubsup></math></p>\n<p>&rArr; <math display=\"inline\"><msup><mrow><mo>(</mo><mn>2</mn><mi>x</mi><mo>)</mo></mrow><mrow><mn>3</mn></mrow></msup></math> = 125 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msubsup><mi>r</mi><mn>1</mn><mn>3</mn></msubsup></math></p>\n<p>&rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msubsup><mi>r</mi><mn>1</mn><mn>3</mn></msubsup></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mrow><mo>(</mo><mn>2</mn><mi>x</mi><mo>)</mo></mrow><mn>3</mn></msup><mn>125</mn></mfrac></math></p>\n<p><strong>दूसरी शर्त :-</strong><br>छोटे गोले का आयतन = 27 &times; समरूप गोले का आयतन</p>\n<p>&rArr;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>3</mn></mfrac></math>&pi;(<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msubsup><mi>R</mi><mn>2</mn><mn>3</mn></msubsup></math>) = 27 &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>3</mn></mfrac></math>&pi;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msubsup><mi>r</mi><mn>2</mn><mn>3</mn></msubsup></math></p>\n<p>&rArr;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>3</mn></mfrac></math>&pi;(<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"true\"><mfrac><mi>x</mi><mn>2</mn></mfrac></mstyle></math>)<sup>3</sup> = 27 &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>3</mn></mfrac></math>&pi;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msubsup><mi>r</mi><mn>2</mn><mn>3</mn></msubsup></math><br>&rArr; <math display=\"inline\"><msubsup><mrow><mi>r</mi></mrow><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></msubsup></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mrow><mo>(</mo><mstyle displaystyle=\"true\"><mfrac><mi>x</mi><mn>2</mn></mfrac></mstyle><mo>)</mo></mrow><mn>3</mn></msup><mn>27</mn></mfrac></math><br>आयतन का अनुपात = <math display=\"inline\"><mfrac><mrow><msup><mrow><mo>(</mo><mn>2</mn><mi>x</mi><mo>)</mo></mrow><mrow><mn>3</mn></mrow></msup></mrow><mrow><mn>125</mn></mrow></mfrac></math> : <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mrow><mo>(</mo><mstyle displaystyle=\"true\"><mfrac><mi>x</mi><mn>2</mn></mfrac></mstyle><mo>)</mo></mrow><mn>3</mn></msup><mn>27</mn></mfrac></math><br>= <math display=\"inline\"><mfrac><mrow><msup><mrow><mo>(</mo><mn>2</mn><mi>x</mi><mo>)</mo></mrow><mrow><mn>3</mn></mrow></msup></mrow><mrow><mn>125</mn></mrow></mfrac></math> : <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mrow><mo>(</mo><mi>x</mi><mo>)</mo></mrow><mn>3</mn></msup><mrow><mn>27</mn><mo>&#215;</mo><mn>8</mn></mrow></mfrac></math> = 1728 : 125<br>प्रश्न के अनुसार <br>&rArr; <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mi>m</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1728</mn><mn>125</mn></mfrac></math><br>अतः m का मान = <math display=\"inline\"><mfrac><mrow><mn>125</mn></mrow><mrow><mn>1728</mn></mrow></mfrac></math><br><strong>वैकल्पिक विधि :</strong></p>\n<p>&rArr; <math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mstyle displaystyle=\"true\"><mfrac><mstyle displaystyle=\"true\"><mfrac><mrow><mi>&#2348;&#2396;&#2375;</mi><mo>&#160;</mo><mi>&#2327;&#2379;&#2354;&#2375;</mi><mo>&#160;</mo><mi>&#2325;&#2366;</mi><mo>&#160;</mo><mi>&#2310;&#2351;&#2340;&#2344;</mi><mo>&#160;</mo></mrow><mrow><mn>125</mn><mo>&#160;</mo></mrow></mfrac></mstyle><mrow><mi>&#2331;&#2379;&#2335;&#2375;</mi><mo>&#160;</mo><mi>&#2327;&#2379;&#2354;&#2375;</mi><mo>&#160;</mo><mi>&#2325;&#2366;</mi><mo>&#160;</mo><mi>&#2310;&#2351;&#2340;&#2344;</mi></mrow></mfrac></mstyle><mrow><mn>27</mn><mo>&#160;</mo></mrow></mfrac></math></p>\n<p>&rArr; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"true\"><mfrac><mstyle displaystyle=\"true\"><mfrac><mrow><mfrac><mn>4</mn><mn>3</mn></mfrac><mi>&#960;</mi><mo>&#215;</mo><mo>(</mo><mn>2</mn><msup><mo>)</mo><mn>3</mn></msup></mrow><mn>125</mn></mfrac></mstyle><mstyle displaystyle=\"true\"><mfrac><mrow><mfrac><mn>4</mn><mn>3</mn></mfrac><mi>&#960;</mi><mo>&#215;</mo><msup><mfenced><mfrac><mn>1</mn><mn>2</mn></mfrac></mfenced><mn>3</mn></msup></mrow><mn>27</mn></mfrac></mstyle></mfrac></mstyle></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>m</mi></mfrac></math></p>\n<p>&rArr; <math display=\"inline\"><mfrac><mrow><mo>(</mo><mn>2</mn><msup><mrow><mo>)</mo></mrow><mrow><mn>3</mn></mrow></msup><mo>&#215;</mo><mn>27</mn></mrow><mrow><mo>(</mo><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac><msup><mrow><mo>)</mo></mrow><mrow><mn>3</mn></mrow></msup><mo>&#215;</mo><mn>125</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>m</mi></mfrac></math></p>\n<p>&rArr;&nbsp;<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>8</mn><mo>&#215;</mo><mn>27</mn><mo>&#215;</mo><mn>8</mn></mrow><mn>125</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>m</mi></mfrac></math></p>\n<p>&rArr; m =&nbsp;<math display=\"inline\"><mfrac><mrow><mn>125</mn></mrow><mrow><mn>1728</mn></mrow></mfrac></math></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "60",
                    section: "17",
                    question_en: "<p>60. If sin 31&deg; = &alpha;, then the value of cot 59&deg; is found.</p>",
                    question_hi: "<p>60. यदि sin 31&deg; = &alpha;, है, तो cot 59&deg; का मान ज्ञात कीजिए।</p>",
                    options_en: [
                        "<p><math display=\"inline\"><mfrac><mrow><msqrt><mn>1</mn><mo>-</mo><mi>&#160;</mi><msup><mrow><mi>&#945;</mi></mrow><mrow><mn>2</mn></mrow></msup></msqrt></mrow><mrow><mn>2</mn><mi>&#945;</mi></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mi>&#945;</mi></mrow><mrow><msqrt><mn>1</mn><mo>-</mo><mi>&#160;</mi><msup><mrow><mi>&#945;</mi></mrow><mrow><mn>2</mn></mrow></msup></msqrt></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><msqrt><mn>1</mn><mo>+</mo><mi>&#160;</mi><msup><mrow><mi>&#945;</mi></mrow><mrow><mn>2</mn></mrow></msup></msqrt></mrow><mrow><mi>&#945;</mi></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mi>&#945;</mi></mrow><mrow><msqrt><mn>1</mn><mo>+</mo><mi>&#160;</mi><msup><mrow><mi>&#945;</mi></mrow><mrow><mn>2</mn></mrow></msup></msqrt></mrow></mfrac></math></p>"
                    ],
                    options_hi: [
                        "<p><math display=\"inline\"><mfrac><mrow><msqrt><mn>1</mn><mo>-</mo><mi>&#160;</mi><msup><mrow><mi>&#945;</mi></mrow><mrow><mn>2</mn></mrow></msup></msqrt></mrow><mrow><mn>2</mn><mi>&#945;</mi></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mi>&#945;</mi></mrow><mrow><msqrt><mn>1</mn><mo>-</mo><mi>&#160;</mi><msup><mrow><mi>&#945;</mi></mrow><mrow><mn>2</mn></mrow></msup></msqrt></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><msqrt><mn>1</mn><mo>+</mo><mi>&#160;</mi><msup><mrow><mi>&#945;</mi></mrow><mrow><mn>2</mn></mrow></msup></msqrt></mrow><mrow><mi>&#945;</mi></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mi>&#945;</mi></mrow><mrow><msqrt><mn>1</mn><mo>+</mo><mi>&#160;</mi><msup><mrow><mi>&#945;</mi></mrow><mrow><mn>2</mn></mrow></msup></msqrt></mrow></mfrac></math></p>"
                    ],
                    solution_en: "<p>60.(b) sin 31&deg; = &alpha;<br>sin (90&deg; - 59&deg;) = &alpha;<br>cos 59&deg; = &alpha;<br>sin 59&deg; = <math display=\"inline\"><msqrt><mn>1</mn><mo>-</mo><msup><mrow><mi>c</mi><mi>o</mi><mi>s</mi></mrow><mrow><mn>2</mn></mrow></msup><mn>59</mn><mo>&#176;</mo></msqrt></math><br>= <math display=\"inline\"><msqrt><mn>1</mn><mo>-</mo><msup><mrow><mi>&#945;</mi></mrow><mrow><mn>2</mn></mrow></msup></msqrt></math><br>Now,<br>cot 59&deg; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>cos</mi><mo>&#160;</mo><mn>59</mn><mo>&#176;</mo></mrow><mrow><mi>sin</mi><mo>&#160;</mo><mn>59</mn><mo>&#176;</mo></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>&#945;</mi><msqrt><mn>1</mn><mo>-</mo><msup><mi>&#945;</mi><mn>2</mn></msup></msqrt></mfrac></math></p>",
                    solution_hi: "<p>60.(b) sin 31&deg; = &alpha;<br>sin (90&deg; - 59&deg;) = &alpha;<br>cos 59&deg; = &alpha;<br>sin 59&deg; = <math display=\"inline\"><msqrt><mn>1</mn><mo>-</mo><msup><mrow><mi>c</mi><mi>o</mi><mi>s</mi></mrow><mrow><mn>2</mn></mrow></msup><mn>59</mn><mo>&#176;</mo></msqrt></math><br>= <math display=\"inline\"><msqrt><mn>1</mn><mo>-</mo><msup><mrow><mi>&#945;</mi></mrow><mrow><mn>2</mn></mrow></msup></msqrt></math><br>अब,<br>cot 59&deg; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>cos</mi><mo>&#160;</mo><mn>59</mn><mo>&#176;</mo></mrow><mrow><mi>sin</mi><mo>&#160;</mo><mn>59</mn><mo>&#176;</mo></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>&#945;</mi><msqrt><mn>1</mn><mo>-</mo><msup><mi>&#945;</mi><mn>2</mn></msup></msqrt></mfrac></math></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "61",
                    section: "17",
                    question_en: "<p>61. A sum of ₹18,000 gives a simple interest of ₹ 3,240 in 2 years and 3 months. The rate of interest per annum is:</p>",
                    question_hi: "<p>61. ₹18,000 की धनराशि पर 2 वर्ष और 3 माह में ₹3,240 का साधारण ब्याज प्राप्त होता है। वार्षिक ब्याज दर कितनी है ?</p>",
                    options_en: [
                        "<p>12%</p>",
                        "<p>8%</p>",
                        "<p>7%</p>",
                        "<p>10%</p>"
                    ],
                    options_hi: [
                        "<p>12%</p>",
                        "<p>8%</p>",
                        "<p>7%</p>",
                        "<p>10%</p>"
                    ],
                    solution_en: "<p>61.(b) SI = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>P</mi><mo>&#215;</mo><mi>R</mi><mo>&#215;</mo><mi>T</mi></mrow><mn>100</mn></mfrac></math><br>&rArr; 3240 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>18000</mn><mo>&#215;</mo><mi>R</mi><mo>&#215;</mo><mn>27</mn></mrow><mrow><mn>100</mn><mo>&#215;</mo><mn>12</mn></mrow></mfrac></math><br>&rArr; R =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3240</mn><mrow><mn>15</mn><mo>&#215;</mo><mn>27</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3240</mn><mn>405</mn></mfrac></math> = 8%</p>",
                    solution_hi: "<p>61.(b) साधारण ब्याज = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2350;&#2370;&#2354;&#2343;&#2344;</mi><mo>&#215;</mo><mi>&#2342;</mi><mi>&#2352;</mi><mo>&#215;</mo><mi>&#2360;</mi><mi>&#2350;</mi><mi>&#2351;</mi></mrow><mn>100</mn></mfrac></math><br>&rArr; 3240 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>18000</mn><mo>&#215;</mo><mi>R</mi><mo>&#215;</mo><mn>27</mn></mrow><mrow><mn>100</mn><mo>&#215;</mo><mn>12</mn></mrow></mfrac></math><br>&rArr; दर(R) = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3240</mn><mrow><mn>15</mn><mo>&#215;</mo><mn>27</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3240</mn><mn>405</mn></mfrac></math> = 8%</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "62",
                    section: "17",
                    question_en: "<p>62. If 70% of the cost price of an article is equal to 40% of its selling price, what is the profit percentage ?</p>",
                    question_hi: "<p>62. यदि किसी वस्तु के क्रय मूल्य का 70% उसके विक्रय मूल्य के 40% के बराबर है, तो लाभ प्रतिशत क्या है ?</p>",
                    options_en: [
                        "<p>75%</p>",
                        "<p>85%</p>",
                        "<p>20%</p>",
                        "<p>45%</p>"
                    ],
                    options_hi: [
                        "<p>75%</p>",
                        "<p>85%</p>",
                        "<p>20%</p>",
                        "<p>45%</p>"
                    ],
                    solution_en: "<p>62.(a) <br>According to the question,<br>CP &times; 70% = SP &times; 40%<br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>C</mi><mi>P</mi></mrow><mrow><mi>S</mi><mi>P</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>7</mn></mfrac></math><br>Hence, required % = <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> &times; 100 = 75%</p>",
                    solution_hi: "<p>62.(a) <br>प्रश्न के अनुसार,<br>CP &times; 70% = SP &times; 40%<br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>C</mi><mi>P</mi></mrow><mrow><mi>S</mi><mi>P</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>7</mn></mfrac></math><br>अतः, आवश्यक % = <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> &times; 100 = 75%</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "63",
                    section: "17",
                    question_en: "<p>63. Simplify the given expression.<br>(7.2 &times; 4.1 &divide; 12.3 + 22.5 of <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>25</mn></mfrac></math> -&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>1</mn><mn>10</mn></mfrac></mstyle></math>)</p>",
                    question_hi: "<p>63. दिए गए व्यंजक को सरल कीजिए I<br>(7.2 &times; 4.1 &divide; 12.3 + 22.5 का <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>25</mn></mfrac></math> -&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>1</mn><mn>10</mn></mfrac></mstyle></math>)</p>",
                    options_en: [
                        "<p>4.9</p>",
                        "<p>3.2</p>",
                        "<p>7.4</p>",
                        "<p>9.6</p>"
                    ],
                    options_hi: [
                        "<p>4.9</p>",
                        "<p>3.2</p>",
                        "<p>7.4</p>",
                        "<p>9.6</p>"
                    ],
                    solution_en: "<p>63.(b)<br>(7.2 &times; 4.1 &divide; 12.3 + 22.5 of <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>25</mn></mfrac></math> -&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>1</mn><mn>10</mn></mfrac></mstyle></math>)<br>= (7.2 &times; 4.1 &times; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><mn>12</mn><mo>.</mo><mn>3</mn></mrow></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>9</mn><mn>10</mn></mfrac></math> -&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>1</mn><mn>10</mn></mfrac></mstyle></math>)<br>= 2.4 + 0.9 - 0.1 = 3.2</p>",
                    solution_hi: "<p>63.(b)<br>(7.2 &times; 4.1 &divide; 12.3 + 22.5 का <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>25</mn></mfrac></math> -&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>1</mn><mn>10</mn></mfrac></mstyle></math>)<br>= (7.2 &times; 4.1 &times; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><mn>12</mn><mo>.</mo><mn>3</mn></mrow></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>9</mn><mn>10</mn></mfrac></math> -&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>1</mn><mn>10</mn></mfrac></mstyle></math>)<br>= 2.4 + 0.9 - 0.1 = 3.2</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "64",
                    section: "17",
                    question_en: "<p>64. A public library has an average attendance of 410 on Sundays and 230 for the remaining days. The average attendance per day of a month of 30 days beginning with Sunday would be:</p>",
                    question_hi: "<p>64. एक सार्वजनिक पुस्तकालय में रविवार को औसत उपस्थिति 410 और शेष दिनों में 230 होती है। रविवार से शुरू होने वाले 30 दिनों के महीने की प्रति दिन औसत उपस्थिति क्या होगी ?&nbsp;</p>",
                    options_en: [
                        "<p>230</p>",
                        "<p>254</p>",
                        "<p>260</p>",
                        "<p>320</p>"
                    ],
                    options_hi: [
                        "<p>230</p>",
                        "<p>254</p>",
                        "<p>260</p>",
                        "<p>320</p>"
                    ],
                    solution_en: "<p>64.(c)<br>As we know, no. of Sunday in the month of 30 days beginning with sunday = 5<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; Sunday&nbsp; &nbsp; Remaining days<br>No of days&nbsp; &nbsp; &nbsp; 5&nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 25&nbsp; &nbsp; &nbsp;= 1&nbsp; &nbsp;:&nbsp; &nbsp;5<br>Average&nbsp; &nbsp; &nbsp; &nbsp; 410&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 230 <br>Overall average = <math display=\"inline\"><mfrac><mrow><mn>410</mn><mo>&#215;</mo><mn>1</mn><mo>+</mo><mn>230</mn><mo>&#215;</mo><mn>5</mn></mrow><mrow><mn>1</mn><mo>+</mo><mn>5</mn></mrow></mfrac></math> <br>= <math display=\"inline\"><mfrac><mrow><mn>410</mn><mo>+</mo><mn>1150</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1560</mn><mn>6</mn></mfrac></math> = 260</p>",
                    solution_hi: "<p>64.(c)<br>जैसा कि हम जानते हैं, रविवार से शुरू होने वाले 30 दिनों के महीने में रविवार की संख्या = 5<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;रविवार : शेष दिन<br>दिनों की संख्या -&nbsp; &nbsp;5&nbsp; &nbsp; :&nbsp; &nbsp; &nbsp;25&nbsp; =&nbsp; 1&nbsp; :&nbsp; 5<br>औसत -&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;410&nbsp; &nbsp; &nbsp; &nbsp; 230 <br>कुल औसत = <math display=\"inline\"><mfrac><mrow><mn>410</mn><mo>&#215;</mo><mn>1</mn><mo>+</mo><mn>230</mn><mo>&#215;</mo><mn>5</mn></mrow><mrow><mn>1</mn><mo>+</mo><mn>5</mn></mrow></mfrac></math> <br>= <math display=\"inline\"><mfrac><mrow><mn>410</mn><mo>+</mo><mn>1150</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1560</mn><mn>6</mn></mfrac></math> = 260</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "65",
                    section: "17",
                    question_en: "<p>65. <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3</mn><mi>&#160;</mi><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#160;</mi><mn>58</mn><mo>&#176;</mo></mrow><mrow><mn>5</mn><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#160;</mi><mn>32</mn><mo>&#176;</mo></mrow></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mo>&#160;</mo><mi>cos</mi><mn>62</mn><mo>&#176;</mo></mrow><mrow><mn>5</mn><mo>&#160;</mo><mi>sin</mi><mn>28</mn><mo>&#176;</mo></mrow></mfrac></math> is equal to :</p>",
                    question_hi: "<p>65. <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3</mn><mi>&#160;</mi><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#160;</mi><mn>58</mn><mo>&#176;</mo></mrow><mrow><mn>5</mn><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#160;</mi><mn>32</mn><mo>&#176;</mo></mrow></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mo>&#160;</mo><mi>cos</mi><mn>62</mn><mo>&#176;</mo></mrow><mrow><mn>5</mn><mo>&#160;</mo><mi>sin</mi><mn>28</mn><mo>&#176;</mo></mrow></mfrac></math>&nbsp;का मान ज्ञात कीजिए।</p>",
                    options_en: [
                        "<p>0</p>",
                        "<p>2</p>",
                        "<p>3</p>",
                        "<p>1</p>"
                    ],
                    options_hi: [
                        "<p>0</p>",
                        "<p>2</p>",
                        "<p>3</p>",
                        "<p>1</p>"
                    ],
                    solution_en: "<p>65.(d) <br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3</mn><mi>&#160;</mi><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#160;</mi><mn>58</mn><mo>&#176;</mo></mrow><mrow><mn>5</mn><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#160;</mi><mn>32</mn><mo>&#176;</mo></mrow></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mo>&#160;</mo><mi>cos</mi><mn>62</mn><mo>&#176;</mo></mrow><mrow><mn>5</mn><mo>&#160;</mo><mi>sin</mi><mn>28</mn><mo>&#176;</mo></mrow></mfrac></math><br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3</mn><mi>&#160;</mi><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#160;</mi><mo>(</mo><mn>90</mn><mo>&#176;</mo><mo>-</mo><mn>32</mn><mo>)</mo><mo>&#176;</mo></mrow><mrow><mn>5</mn><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#160;</mi><mn>32</mn><mo>&#176;</mo><mi>&#160;</mi></mrow></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mo>&#160;</mo><mi>cos</mi><mo>(</mo><mn>90</mn><mo>&#176;</mo><mo>-</mo><mn>28</mn><mo>&#176;</mo><mo>)</mo></mrow><mrow><mn>5</mn><mo>&#160;</mo><mi>sin</mi><mn>28</mn><mo>&#176;</mo></mrow></mfrac></math> <br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3</mn><mo>&#160;</mo><mi mathvariant=\"bold-italic\">c</mi><mi mathvariant=\"bold-italic\">o</mi><mi mathvariant=\"bold-italic\">s</mi><mi mathvariant=\"bold-italic\">&#160;</mi><mn>32</mn><mo>&#176;</mo></mrow><mrow><mn>5</mn><mo>&#160;</mo><mi mathvariant=\"bold-italic\">c</mi><mi mathvariant=\"bold-italic\">o</mi><mi mathvariant=\"bold-italic\">s</mi><mi mathvariant=\"bold-italic\">&#160;</mi><mn>32</mn><mo>&#176;</mo></mrow></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mo>&#160;</mo><mi>sin</mi><mn>28</mn><mo>&#176;</mo></mrow><mrow><mn>5</mn><mo>&#160;</mo><mi>sin</mi><mn>28</mn><mo>&#176;</mo></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>5</mn></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>5</mn></mfrac></math> = 1</p>",
                    solution_hi: "<p>65.(d) <br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3</mn><mi>&#160;</mi><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#160;</mi><mn>58</mn><mo>&#176;</mo></mrow><mrow><mn>5</mn><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#160;</mi><mn>32</mn><mo>&#176;</mo></mrow></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mo>&#160;</mo><mi>cos</mi><mn>62</mn><mo>&#176;</mo></mrow><mrow><mn>5</mn><mo>&#160;</mo><mi>sin</mi><mn>28</mn><mo>&#176;</mo></mrow></mfrac></math><br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3</mn><mi>&#160;</mi><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#160;</mi><mo>(</mo><mn>90</mn><mo>&#176;</mo><mo>-</mo><mn>32</mn><mo>)</mo><mo>&#176;</mo></mrow><mrow><mn>5</mn><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#160;</mi><mn>32</mn><mo>&#176;</mo><mi>&#160;</mi></mrow></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mo>&#160;</mo><mi>cos</mi><mo>(</mo><mn>90</mn><mo>&#176;</mo><mo>-</mo><mn>28</mn><mo>&#176;</mo><mo>)</mo></mrow><mrow><mn>5</mn><mo>&#160;</mo><mi>sin</mi><mn>28</mn><mo>&#176;</mo></mrow></mfrac></math> <br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3</mn><mo>&#160;</mo><mi mathvariant=\"bold-italic\">c</mi><mi mathvariant=\"bold-italic\">o</mi><mi mathvariant=\"bold-italic\">s</mi><mi mathvariant=\"bold-italic\">&#160;</mi><mn>32</mn><mo>&#176;</mo></mrow><mrow><mn>5</mn><mo>&#160;</mo><mi mathvariant=\"bold-italic\">c</mi><mi mathvariant=\"bold-italic\">o</mi><mi mathvariant=\"bold-italic\">s</mi><mi mathvariant=\"bold-italic\">&#160;</mi><mn>32</mn><mo>&#176;</mo></mrow></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mo>&#160;</mo><mi>sin</mi><mn>28</mn><mo>&#176;</mo></mrow><mrow><mn>5</mn><mo>&#160;</mo><mi>sin</mi><mn>28</mn><mo>&#176;</mo></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>5</mn></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>5</mn></mfrac></math> = 1</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "66",
                    section: "17",
                    question_en: "<p>66. What is the length (in cm) of each side of a cube if the volume of a cube is 13,824 cm<sup>3</sup> ?</p>",
                    question_hi: "<p>66. यदि एक घन का आयतन 13,824 cm<sup>3</sup> है, तो घन की प्रत्येक भुजा की लंबाई (cm में) क्या है ?</p>",
                    options_en: [
                        "<p>20.4</p>",
                        "<p>24</p>",
                        "<p>22</p>",
                        "<p>34</p>"
                    ],
                    options_hi: [
                        "<p>20.4</p>",
                        "<p>24</p>",
                        "<p>22</p>",
                        "<p>34</p>"
                    ],
                    solution_en: "<p>66.(b) Volume of cube (a<sup>3</sup>) = 13,824 cm<sup>3</sup><br>Length of each side of cube = <math display=\"inline\"><mroot><mrow><mn>13824</mn></mrow><mrow><mn>3</mn></mrow></mroot></math> = 24cm </p>",
                    solution_hi: "<p>66.(b) घन का आयतन (a<sup>3</sup>) = 13,824 cm<sup>3</sup><br>घन की प्रत्येक भुजा की लंबाई = <math display=\"inline\"><mroot><mrow><mn>13824</mn></mrow><mrow><mn>3</mn></mrow></mroot></math> = 24cm</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "67",
                    section: "17",
                    question_en: "<p>67. If <math display=\"inline\"><mi>x</mi></math> + y + z = 1, xy + yz + zx = - 1 and xyz = - 1, then (x<sup>3</sup> + y<sup>3</sup> + z<sup>3</sup>) is equal to ________.</p>",
                    question_hi: "<p>67. यदि <math display=\"inline\"><mi>x</mi></math> + y + z = 1, xy + yz + zx = - 1 और xyz = - 1 है, तो (x<sup>3</sup> + y<sup>3</sup> + z<sup>3</sup>) का मान ज्ञात कीजिए।</p>",
                    options_en: [
                        "<p>0</p>",
                        "<p>1</p>",
                        "<p>2</p>",
                        "<p>3</p>"
                    ],
                    options_hi: [
                        "<p>0</p>",
                        "<p>1</p>",
                        "<p>2</p>",
                        "<p>3</p>"
                    ],
                    solution_en: "<p>67.(b)<br><strong>Formula used :</strong><br><math display=\"inline\"><msup><mrow><mi>x</mi></mrow><mrow><mn>3</mn></mrow></msup></math> + y<sup>3 </sup>+ z<sup>3</sup> - 3xyz<br>= (x + y + z)[(x + y + z)<sup>2</sup> - 3(xy + yz + zx)]<br>Now, <math display=\"inline\"><msup><mrow><mi>x</mi></mrow><mrow><mn>3</mn></mrow></msup></math>+ y<sup>3 </sup>+ z<sup>3</sup> - 3 &times; ( - 1) = 1[1<sup>2 </sup>- 3 &times; (-1)]<br><math display=\"inline\"><msup><mrow><mi>x</mi></mrow><mrow><mn>3</mn></mrow></msup></math>+ y<sup>3</sup> + z<sup>3</sup> + 3 = 4<br><math display=\"inline\"><msup><mrow><mi>x</mi></mrow><mrow><mn>3</mn></mrow></msup></math>+ y<sup>3 </sup>+ z<sup>3</sup> = 1</p>",
                    solution_hi: "<p>67.(b)<br><strong>प्रयुक्त सूत्र:</strong><br><math display=\"inline\"><msup><mrow><mi>x</mi></mrow><mrow><mn>3</mn></mrow></msup></math> + y<sup>3 </sup>+ z<sup>3</sup> - 3xyz<br>= (x + y + z)[(x + y + z)<sup>2</sup> - 3(xy + yz + zx)]<br><strong id=\"docs-internal-guid-d4723936-7fff-a465-87e6-930824a88a51\">अब</strong>, <math display=\"inline\"><msup><mrow><mi>x</mi></mrow><mrow><mn>3</mn></mrow></msup></math>+ y<sup>3 </sup>+ z<sup>3</sup> - 3 &times; ( - 1) = 1[1<sup>2 </sup>- 3 &times; (-1)]<br><math display=\"inline\"><msup><mrow><mi>x</mi></mrow><mrow><mn>3</mn></mrow></msup></math>+ y<sup>3</sup> + z<sup>3</sup> + 3 = 4<br><math display=\"inline\"><msup><mrow><mi>x</mi></mrow><mrow><mn>3</mn></mrow></msup></math>+ y<sup>3 </sup>+ z<sup>3</sup> = 1</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "68",
                    section: "17",
                    question_en: "<p>68. Which is the greatest ratio among the following ?<br><math display=\"inline\"><mfrac><mrow><mn>8</mn></mrow><mrow><mn>11</mn></mrow></mfrac></math>, <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>5</mn></mfrac></math>, <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math> and <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>11</mn></mfrac></math></p>",
                    question_hi: "<p>68. निम्नलिखित में से सबसे बड़ा अनुपात कौन सा है ?<br><math display=\"inline\"><mfrac><mrow><mn>8</mn></mrow><mrow><mn>11</mn></mrow></mfrac></math>, <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>5</mn></mfrac></math>, <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math> और <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>11</mn></mfrac></math></p>",
                    options_en: [
                        "<p><math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>8</mn></mrow><mrow><mn>11</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>11</mn></mrow></mfrac></math></p>"
                    ],
                    options_hi: [
                        "<p><math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>8</mn></mrow><mrow><mn>11</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>11</mn></mrow></mfrac></math></p>"
                    ],
                    solution_en: "<p>68.(a)<br><math display=\"inline\"><mfrac><mrow><mn>8</mn></mrow><mrow><mn>11</mn></mrow></mfrac></math>, <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>5</mn></mfrac></math>, <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math> and <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>11</mn></mfrac></math><br>Denominators of given fractions are 11 , 5 , 2 and 11<br>LCM(11, 5, 2, 11) = 110<br>Multiply each fraction by 110 we get fractions<br><math display=\"inline\"><mfrac><mrow><mn>8</mn></mrow><mrow><mn>11</mn></mrow></mfrac></math> &times; 110, <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>5</mn></mfrac></math> &times; 110, <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math> &times; 110 ,<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>11</mn></mfrac></math> &times; 110<br>= 80 , 88 , 55 and 10<br>Here, 88 is the largest among all<br>Hence, required fraction = <math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math></p>",
                    solution_hi: "<p>68.(a)<br><math display=\"inline\"><mfrac><mrow><mn>8</mn></mrow><mrow><mn>11</mn></mrow></mfrac></math>, <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>5</mn></mfrac></math>, <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math> और <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>11</mn></mfrac></math><br>दी गई भिन्नों के हर 11, 5, 2 और 11 हैं<br>LCM (11, 5, 2, 11) = 110<br>प्रत्येक भिन्न को 110 से गुणा करने पर हमें भिन्न प्राप्त होती है<br><math display=\"inline\"><mfrac><mrow><mn>8</mn></mrow><mrow><mn>11</mn></mrow></mfrac></math> &times; 110, <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>5</mn></mfrac></math> &times; 110, <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math> &times; 110 ,<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>11</mn></mfrac></math> &times; 110<br>= 80 , 88 , 55 और 10<br>यहाँ 88 सबसे बड़ा है<br>अत: अभीष्ट भिन्न = <math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "69",
                    section: "17",
                    question_en: "<p>69. R and S can finish a work in 10 days, S and T can finish it in 12 days and T and R can finish in 8 days. In how many days will the work be finished if they all work simultaneously ?</p>",
                    question_hi: "<p>69. R और S एक काम को 10 दिनों में समाप्त कर सकते हैं, S और T इसे 12 दिनों में समाप्त कर सकते हैं और T और R इसे 8 दिनों में समाप्त कर सकते हैं। यदि वे सभी एक साथ मिलकर काम करते हैं, तो काम कितने दिनों में समाप्त हो जाएगा ?</p>",
                    options_en: [
                        "<p><math display=\"inline\"><mfrac><mrow><mn>240</mn></mrow><mrow><mn>39</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>240</mn></mrow><mrow><mn>37</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>240</mn></mrow><mrow><mn>33</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>240</mn></mrow><mrow><mn>41</mn></mrow></mfrac></math></p>"
                    ],
                    options_hi: [
                        "<p><math display=\"inline\"><mfrac><mrow><mn>240</mn></mrow><mrow><mn>39</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>240</mn></mrow><mrow><mn>37</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>240</mn></mrow><mrow><mn>33</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>240</mn></mrow><mrow><mn>41</mn></mrow></mfrac></math></p>"
                    ],
                    solution_en: "<p>69.(b) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743489334115.png\" alt=\"rId59\" width=\"237\" height=\"124\"><br>Efficiency of R, S and T = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>12</mn><mo>+</mo><mn>10</mn><mo>+</mo><mn>15</mn></mrow><mn>2</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>37</mn><mn>2</mn></mfrac></math><br>Required time = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>120</mn><mo>&#215;</mo><mn>2</mn></mrow><mn>37</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>240</mn><mn>37</mn></mfrac></math> days</p>",
                    solution_hi: "<p>69.(b) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743489334249.png\" alt=\"rId60\" width=\"201\" height=\"123\"><br>R, S और T की दक्षता = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>12</mn><mo>+</mo><mn>10</mn><mo>+</mo><mn>15</mn></mrow><mn>2</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>37</mn><mn>2</mn></mfrac></math><br>आवश्यक समय = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>120</mn><mo>&#215;</mo><mn>2</mn></mrow><mn>37</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>240</mn><mn>37</mn></mfrac></math>&nbsp;दिन</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "70",
                    section: "17",
                    question_en: "<p>70. A man walking at the speed of 4 km/h covers a certain distance in 6 hours and 15 minutes. If he covers the same distance by cycle in 5 hours and 10 minutes, then the speed of the cycle in km/h is:</p>",
                    question_hi: "<p>70. एक व्यक्ति 4 km/h की चाल से चलते हुए एक निश्चित दूरी को 6 घंटे 15 मिनट में तय करता है। यदि वह समान दूरी 5 घंटे 10 मिनट में साइकिल से तय करता है, तो साइकिल की km/h में चाल क्या है ?</p>",
                    options_en: [
                        "<p>4 <math display=\"inline\"><mfrac><mrow><mn>26</mn></mrow><mrow><mn>31</mn></mrow></mfrac></math></p>",
                        "<p>3 <math display=\"inline\"><mfrac><mrow><mn>11</mn></mrow><mrow><mn>31</mn></mrow></mfrac></math></p>",
                        "<p>2 <math display=\"inline\"><mfrac><mrow><mn>9</mn></mrow><mrow><mn>31</mn></mrow></mfrac></math></p>",
                        "<p>3 <math display=\"inline\"><mfrac><mrow><mn>21</mn></mrow><mrow><mn>31</mn></mrow></mfrac></math></p>"
                    ],
                    options_hi: [
                        "<p>4 <math display=\"inline\"><mfrac><mrow><mn>26</mn></mrow><mrow><mn>31</mn></mrow></mfrac></math></p>",
                        "<p>3 <math display=\"inline\"><mfrac><mrow><mn>11</mn></mrow><mrow><mn>31</mn></mrow></mfrac></math></p>",
                        "<p>2 <math display=\"inline\"><mfrac><mrow><mn>9</mn></mrow><mrow><mn>31</mn></mrow></mfrac></math></p>",
                        "<p>3<math display=\"inline\"><mfrac><mrow><mn>21</mn></mrow><mrow><mn>31</mn></mrow></mfrac></math></p>"
                    ],
                    solution_en: "<p>70.(a) <br>Time taken by man to cover the distance by walking = 6 hrs 15 min = 6<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>25</mn><mn>4</mn></mfrac></math>hrs<br>Time taken by man to cover the distance by cycling = 5 hrs 10 min = 5<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>31</mn><mrow><mn>6</mn><mo>&#160;</mo></mrow></mfrac></math> hrs<br>Time &prop; <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mi>s</mi><mi>p</mi><mi>e</mi><mi>e</mi><mi>d</mi></mrow></mfrac></math> (when Distance is constant)<br>Time :-&nbsp; &nbsp;<math display=\"inline\"><mfrac><mrow><mn>25</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> : <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>31</mn><mrow><mn>6</mn><mo>&#160;</mo></mrow></mfrac></math>&nbsp;= 75 : 62<br>Speed :-&nbsp; &nbsp;62 : 75<br>62 unit = 4 km/hr<br>75 unit = <math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>62</mn></mrow></mfrac></math> &times; 75 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>150</mn><mn>31</mn></mfrac></math> = 4<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>26</mn><mn>31</mn></mfrac></math> km/hr</p>",
                    solution_hi: "<p>70.(a) <br>व्यक्ति द्वारा चलकर दूरी तय करने में लगा समय = 6 घंटे 15 मिनट = 6<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>25</mn><mn>4</mn></mfrac></math> घंटे<br>व्यक्ति द्वारा साइकिल चलाकर दूरी तय करने में लगा समय = 5 घंटे 10 मिनट = 5<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>31</mn><mrow><mn>6</mn><mo>&#160;</mo></mrow></mfrac></math>&nbsp;घंटे<br>समय &prop; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>&#2327;&#2340;&#2367;</mi></mfrac></math> (जब दूरी स्थिर हो)<br>समय :-&nbsp; <math display=\"inline\"><mfrac><mrow><mn>25</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> : <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>31</mn><mrow><mn>6</mn><mo>&#160;</mo></mrow></mfrac></math>&nbsp;= 75 : 62<br>&nbsp; गति :-&nbsp; &nbsp;62&nbsp; :&nbsp; 75<br>62 इकाई = 4 km/hr<br>75 इकाई = <math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>62</mn></mrow></mfrac></math> &times; 75 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>150</mn><mn>31</mn></mfrac></math> = 4<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>26</mn><mn>31</mn></mfrac></math>&nbsp;km/hr</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "71",
                    section: "17",
                    question_en: "<p>71. The average score of a batsman in twenty matches is 15 and in twenty-five other matches is 24. Find the average score in all the forty-five matches.</p>",
                    question_hi: "<p>71. एक बल्लेबाज का बीस मैचों में औसत स्कोर 15 है और पच्चीस अन्य मैचों में औसत स्कोर 24 है। सभी पैंतालीस मैचों में औसत स्कोर ज्ञात कीजिए।</p>",
                    options_en: [
                        "<p>21</p>",
                        "<p>20</p>",
                        "<p>19.5</p>",
                        "<p>22</p>"
                    ],
                    options_hi: [
                        "<p>21</p>",
                        "<p>20</p>",
                        "<p>19.5</p>",
                        "<p>22</p>"
                    ],
                    solution_en: "<p>71.(b) Total Score in twenty matches = 15 &times; 20 = 300<br>Total Score in twenty - five other matches = 24 &times; 25 = 600<br>average score in all the forty-five matches = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>300</mn><mo>+</mo><mn>600</mn></mrow><mn>45</mn></mfrac></math> = 20</p>",
                    solution_hi: "<p>71.(b) बीस मैचों में कुल स्कोर = 15 &times; 20 = 300<br>पच्चीस अन्य मैचों में कुल स्कोर = 24 &times; 25 = 600<br>सभी पैंतालीस मैचों में औसत स्कोर = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>300</mn><mo>+</mo><mn>600</mn></mrow><mn>45</mn></mfrac></math> = 20</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "72",
                    section: "17",
                    question_en: "<p>72. A mobile phone dealer buys a phone for ₹10,000 and sells it for ₹12,000. Later, he realizes that he could have sold it for ₹13,000. What is the percentage loss that he incurs ?</p>",
                    question_hi: "<p>72. एक मोबाइल फोन व्यापारी एक फोन को ₹10,000 में खरीदता है और ₹12,000 में बेचता है। बाद में, उसे ज्ञात होता है कि वह इसे ₹13,000 में बेच सकता था। उसे कितने प्रतिशत की हानि होती है ?</p>",
                    options_en: [
                        "<p>10%</p>",
                        "<p>20%</p>",
                        "<p>15%</p>",
                        "<p>25%</p>"
                    ],
                    options_hi: [
                        "<p>10%</p>",
                        "<p>20%</p>",
                        "<p>15%</p>",
                        "<p>25%</p>"
                    ],
                    solution_en: "<p>72.(a) <br>Cost price = 10,000<br>SP<sub>1</sub> = 12,000 <br>SP<sub>2</sub> = 13,000<br>Loss = SP<sub>2</sub> - SP<sub>1</sub> = 1000<br>Loss % = <math display=\"inline\"><mfrac><mrow><mn>1000</mn></mrow><mrow><mn>10000</mn></mrow></mfrac></math> &times; 100 = 10%</p>",
                    solution_hi: "<p>72.(a) <br>लागत मूल्य = 10,000<br>(विक्रय मूल्य)<sub>1</sub> = 12,000 <br>(विक्रय मूल्य)<sub>2</sub> = 13,000<br>हानि = (विक्रय मूल्य)<sub>2</sub> - (विक्रय मूल्य)<sub>1</sub> = 1000<br>हानि % = <math display=\"inline\"><mfrac><mrow><mn>1000</mn></mrow><mrow><mn>10000</mn></mrow></mfrac></math> &times; 100 = 10%</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "73",
                    section: "17",
                    question_en: "<p>73. If the income of P is 40% less than Q, then the combined income of P and Q is how much percent more than the income of P ?</p>",
                    question_hi: "<p>73. यदि P की आय Q से 40% कम है, तो P और Q की संयुक्त आय, P की आय से कितने प्रतिशत अधिक है ?</p>",
                    options_en: [
                        "<p>166<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>",
                        "<p>166<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>",
                        "<p>164<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>",
                        "<p>164<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>"
                    ],
                    options_hi: [
                        "<p>166<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>",
                        "<p>166<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>",
                        "<p>164<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>",
                        "<p>164<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>"
                    ],
                    solution_en: "<p>73.(b) <br>40% = <math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math><br>Income - P : Q = 3 : 5<br>Hence, required % = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>3</mn><mo>+</mo><mn>5</mn><mo>)</mo><mo>-</mo><mn>3</mn></mrow><mn>3</mn></mfrac></math> &times; 100 <br>= <math display=\"inline\"><mfrac><mrow><mn>500</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> = 166<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> %</p>",
                    solution_hi: "<p>73.(b) <br>40% = <math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math><br>आय - P : Q = 3 : 5<br>अतः, आवश्यक % = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>3</mn><mo>+</mo><mn>5</mn><mo>)</mo><mo>-</mo><mn>3</mn></mrow><mn>3</mn></mfrac></math> &times; 100<br>= <math display=\"inline\"><mfrac><mrow><mn>500</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> = 166<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> %</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "74",
                    section: "17",
                    question_en: "<p>74. The average monthly income of A and B is ₹3,590. The average monthly income of B and C is ₹4,250. The average monthly income of A and C is ₹6,500. What is the monthly income of C ?</p>",
                    question_hi: "<p>74. A और B की औसत मासिक आय ₹3,590 है। B और C की औसत मासिक आय ₹4,250 है। A और C की औसत मासिक आय ₹6,500 है। C की मासिक आय क्या है ?</p>",
                    options_en: [
                        "<p>₹7,545</p>",
                        "<p>₹6,984</p>",
                        "<p>₹8,190</p>",
                        "<p>₹7,160</p>"
                    ],
                    options_hi: [
                        "<p>₹7,545</p>",
                        "<p>₹6,984</p>",
                        "<p>₹8,190</p>",
                        "<p>₹7,160</p>"
                    ],
                    solution_en: "<p>74.(d)<br>Monthly income of C = monthly income of (A + B + C) - Monthly income of (A + B)<br>&rArr; (3590 + 4250 + 6500) - 2 &times; 3590 = 14340 - 7180 = ₹7,160</p>",
                    solution_hi: "<p>74.(d)<br>C की मासिक आय = (A + B + C) की मासिक आय - (A + B) की मासिक आय<br>&rArr; (3590 + 4250 + 6500) - 2 &times; 3590 = 14340 - 7180 = ₹7,160</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "75",
                    section: "7",
                    question_en: "<p>75. The following table shows the number of students admitted in to a university for higher education from 2010 to 2013.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743489334456.png\" alt=\"rId61\" width=\"231\" height=\"110\"> <br>What is the average number of students admitted in to the university from 2010 to 2013 ?</p>",
                    question_hi: "<p>75. निम्नलिखित तालिका में वर्ष 2010 से 2013 तक एक विश्वविद्यालय में उच्च शिक्षा के लिए दाखिला लेने वाले विद्यार्थियों की संख्या दर्शाई गई है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743489334559.png\" alt=\"rId62\" width=\"220\" height=\"119\"></p>",
                    options_en: [
                        "<p>495</p>",
                        "<p>499</p>",
                        "<p>510</p>",
                        "<p>522</p>"
                    ],
                    options_hi: [
                        "<p>495</p>",
                        "<p>499</p>",
                        "<p>510</p>",
                        "<p>522</p>"
                    ],
                    solution_en: "<p>75.(c) Average of number of students = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>450</mn><mo>+</mo><mn>540</mn><mo>+</mo><mn>370</mn><mo>+</mo><mn>680</mn></mrow><mn>4</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2040</mn><mn>4</mn></mfrac></math> = 510</p>",
                    solution_hi: "<p>75.(c) छात्रों की संख्या का औसत = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>450</mn><mo>+</mo><mn>540</mn><mo>+</mo><mn>370</mn><mo>+</mo><mn>680</mn></mrow><mn>4</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2040</mn><mn>4</mn></mfrac></math> = 510</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "76",
                    section: "7",
                    question_en: "<p>76. Parts of the following sentence have been given as options. Select the option that contains an error.<br>No other place in this country is so better as Gulmarg, Kashmir.</p>",
                    question_hi: "<p>76. Parts of the following sentence have been given as options. Select the option that contains an error.<br>No other place in this country is so better as Gulmarg, Kashmir.</p>",
                    options_en: [
                        "<p>No other place</p>",
                        "<p>in this country is</p>",
                        "<p>so better as</p>",
                        "<p>Gulmarg, Kashmir</p>"
                    ],
                    options_hi: [
                        "<p>No other place</p>",
                        "<p>in this country is</p>",
                        "<p>so better as</p>",
                        "<p>Gulmarg, Kashmir</p>"
                    ],
                    solution_en: "<p>76.(c) so better as<br>Positive degree adjective is used in the structure &lsquo;as&hellip;as&rsquo; or &lsquo;so&hellip;as&rsquo; to make a comparison. Therefore, &lsquo;better&rsquo; must be replaced with &lsquo;good&rsquo;. Hence, &lsquo;so good as&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>76.(c) so better as<br>किसी sentence में तुलना करने के लिए structure &lsquo;as&hellip;as&rsquo; or &lsquo;so&hellip;as&rsquo; में positive degree adjective का प्रयोग किया जाता है। इसलिए, &lsquo;better&rsquo; के स्थान पर &lsquo;good&rsquo; का प्रयोग होगा। अतः , &lsquo;so good as&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "77",
                    section: "7",
                    question_en: "<p>77. Select the word which means the same as the group of words given.<br>Showing great attention to detail and correct behaviour</p>",
                    question_hi: "<p>77. Select the word which means the same as the group of words given.<br>Showing great attention to detail and correct behaviour</p>",
                    options_en: [
                        "<p>Punctual</p>",
                        "<p>Zealous</p>",
                        "<p>Punctilious</p>",
                        "<p>Disciplined</p>"
                    ],
                    options_hi: [
                        "<p>Punctual</p>",
                        "<p>Zealous</p>",
                        "<p>Punctilious</p>",
                        "<p>Disciplined</p>"
                    ],
                    solution_en: "<p>77.(c) <strong>Punctilious-</strong> showing great attention to detail and correct behaviour . <br><strong>Punctual- </strong>doing something or happening at the right time<br><strong>Zealous-</strong> using great energy and enthusiasm<br><strong>Disciplined-</strong> to train somebody to obey and to behave in a controlled way</p>",
                    solution_hi: "<p>77.(c) <strong>Punctilious - </strong>सही व्यवहार पर बहुत ध्यान देना <br><strong>Punctual -</strong> सही समय पर कुछ करना या होना <br><strong>Zealous - </strong>ऊर्जावान या उत्&zwj;साही<br><strong>Disciplined - </strong>किसी को आज्ञा मानने और नियंत्रित तरीके से व्यवहार करने के लिए प्रशिक्षित करना</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "78",
                    section: "7",
                    question_en: "<p>78. Choose the alternative which best expresses the meaning of the idiom/phrase.<br>Throw up the sponge</p>",
                    question_hi: "<p>78. Choose the alternative which best expresses the meaning of the idiom/phrase.<br>Throw up the sponge</p>",
                    options_en: [
                        "<p>to love</p>",
                        "<p>to hate</p>",
                        "<p>to surrender</p>",
                        "<p>to attack</p>"
                    ],
                    options_hi: [
                        "<p>to love</p>",
                        "<p>to hate</p>",
                        "<p>to surrender</p>",
                        "<p>to attack</p>"
                    ],
                    solution_en: "<p>78.(c) to surrender</p>",
                    solution_hi: "<p>78.(c) to surrender / घुटने टेकना</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "79",
                    section: "7",
                    question_en: "<p>79. Select the option with the correct use of article(s).</p>",
                    question_hi: "<p>79. Select the option with the correct use of article(s).</p>",
                    options_en: [
                        "<p>She kept an lot of rules in her life, due to which she is flourishing today.</p>",
                        "<p>She kept lot of rules in her life, due to which she is flourishing today.</p>",
                        "<p>She kept the lot of rules in her life, due to which she is flourishing today.</p>",
                        "<p>She kept a lot of rules in her life, due to which she is flourishing today.</p>"
                    ],
                    options_hi: [
                        "<p>She kept an lot of rules in her life, due to which she is flourishing today.</p>",
                        "<p>She kept lot of rules in her life, due to which she is flourishing today.</p>",
                        "<p>She kept the lot of rules in her life, due to which she is flourishing today.</p>",
                        "<p>She kept a lot of rules in her life, due to which she is flourishing today.</p>"
                    ],
                    solution_en: "<p>79.(d) <strong>She kept a lot of rules in her life, due to which she is flourishing today.</strong><br>&lsquo;A lot of&rsquo; is the correct phrase, meaning a large number of people or things. Hence, option (d) is the most appropriate answer.</p>",
                    solution_hi: "<p>79.(d)<strong> She kept a lot of rules in her life, due to which she is flourishing today.</strong><br>&lsquo;A lot of&rsquo;, correct phrase है, जिसका अर्थ है बड़ी संख्या में लोग या चीज़ें। अतः, option (d) सबसे उपयुक्त उत्तर है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "80",
                    section: "7",
                    question_en: "<p>80. Identify the best way to improve the underlined part of the given sentence. If there is no improvement required, select &lsquo;no improvement&rsquo;.<br>Young men and women should get <span style=\"text-decoration: underline;\"><strong>habituated</strong></span> to reading and writing about current affairs.</p>",
                    question_hi: "<p>80. Identify the best way to improve the underlined part of the given sentence. If there is no improvement required, select &lsquo;no improvement&rsquo;.<br>Young men and women should get <strong><span style=\"text-decoration: underline;\">habituated</span></strong> to reading and writing about current affairs.</p>",
                    options_en: [
                        "<p>prepared</p>",
                        "<p>trained</p>",
                        "<p>used</p>",
                        "<p>No improvement</p>"
                    ],
                    options_hi: [
                        "<p>prepared</p>",
                        "<p>trained</p>",
                        "<p>used</p>",
                        "<p>No improvement</p>"
                    ],
                    solution_en: "<p>80.(c) used. <br><strong>&lsquo;Used to&rsquo;</strong> is used when we get into the habit of doing something.</p>",
                    solution_hi: "<p>80.(c)used. <br><strong>\'Used to\' </strong>का प्रयोग तब होता है जब हम किसी काम को आदतन करते है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "81",
                    section: "7",
                    question_en: "<p>81. Select the most appropriate synonym of the given word. <br>Solemn</p>",
                    question_hi: "<p>81. Select the most appropriate synonym of the given word. <br>Solemn</p>",
                    options_en: [
                        "<p>Dignified</p>",
                        "<p>Trivial</p>",
                        "<p>Excited</p>",
                        "<p>Frivolous</p>"
                    ],
                    options_hi: [
                        "<p>Dignified</p>",
                        "<p>Trivial</p>",
                        "<p>Excited</p>",
                        "<p>Frivolous</p>"
                    ],
                    solution_en: "<p>81.(a) <strong>Dignified</strong><br><strong>Dignified </strong>- behaving in a calm, serious way that makes other people respect you <br><strong>Trivial</strong>- of little importance <br><strong>Excited</strong>- feeling or showing happiness and enthusiasm<br><strong>Frivolous</strong>- not serious</p>",
                    solution_hi: "<p>81.(a) <strong>Dignified</strong><br><strong>Dignified </strong>- शांत, गंभीर तरीके से व्यवहार करना जिससे दूसरे लोग आपका सम्मान करें<br><strong>Trivial </strong>- कम महत्व का<br><strong>Excited</strong>- खुशी और उत्साह महसूस करना या दिखाना<br><strong>Frivolous </strong>- गंभीर नहीं</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "82",
                    section: "7",
                    question_en: "<p>82. My grandmother believes that drinking a glass of milk at bedtime is essential for health.</p>",
                    question_hi: "<p>82. My grandmother believes that drinking a glass of milk at bedtime is essential for health.</p>",
                    options_en: [
                        "<p>It is believed by my grandmother that drinking a glass of milk at bedtime is essential for health.</p>",
                        "<p>It is being believed by my grandmother that drinking a glass of milk at bedtime is essential for health.</p>",
                        "<p>It will have been believed by my grandmother that drinking a glass of milk at bedtime is essential for health.</p>",
                        "<p>It has been believed by my grandmother that drinking a glass of milk at bedtime is essential for health.</p>"
                    ],
                    options_hi: [
                        "<p>It is believed by my grandmother that drinking a glass of milk at bedtime is essential for health.</p>",
                        "<p>It is being believed by my grandmother that drinking a glass of milk at bedtime is essential for health.</p>",
                        "<p>It will have been believed by my grandmother that drinking a glass of milk at bedtime is essential for health.</p>",
                        "<p>It has been believed by my grandmother that drinking a glass of milk at bedtime is essential for health.</p>"
                    ],
                    solution_en: "<p>82.(a) It is believed by my grandmother that drinking a glass of milk at bedtime is essential for health. (Correct)<br>(b) It is <span style=\"text-decoration: underline;\">being believed</span> by my grandmother that drinking a glass of milk at bedtime is essential for health. (Incorrect use of &lsquo;being&rsquo;) <br>(c) It <span style=\"text-decoration: underline;\">will have been</span> believed by my grandmother that drinking a glass of milk at bedtime is essential for health. (Incorrect Tense)<br>(d) It <span style=\"text-decoration: underline;\">has been</span> believed by my grandmother that drinking a glass of milk at bedtime is essential for health. (Incorrect Tense)</p>",
                    solution_hi: "<p>82.(a) It is believed by my grandmother that drinking a glass of milk at bedtime is essential for health. (Correct)<br>(b) It is <span style=\"text-decoration: underline;\">being believed</span> by my grandmother that drinking a glass of milk at bedtime is essential for health. (&lsquo;Being का प्रयोग नहीं होगा।&rsquo;)&nbsp;<br>(c) It <span style=\"text-decoration: underline;\">will have been</span> believed by my grandmother that drinking a glass of milk at bedtime is essential for health. (गलत verb (will have been believed) का प्रयोग किया गया है। is believed का प्रयोग होगा।)&nbsp;<br>(d) It <span style=\"text-decoration: underline;\">has been</span> believed by my grandmother that drinking a glass of milk at bedtime is essential for health. (गलत tense (present perfect) का प्रयोग किया गया है। is believed (simple present) का प्रयोग होगा ।)&nbsp;</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "83",
                    section: "7",
                    question_en: "<p>83. The play on environment issues was performed with great professionalism by our students.</p>",
                    question_hi: "<p>83. The play on environment issues was performed with great professionalism by our students.</p>",
                    options_en: [
                        "<p>Our students performed the play on environment issues with great professionalism.</p>",
                        "<p>Our students were to perform with great professionalism the play on environment issue.</p>",
                        "<p>Our students have been performing the play on environment issues with great professionalism.</p>",
                        "<p>The play on environment issues was being performed with great professionalism by our students.</p>"
                    ],
                    options_hi: [
                        "<p>Our students performed the play on environment issues with great professionalism.</p>",
                        "<p>Our students were to perform with great professionalism the play on environment issue.</p>",
                        "<p>Our students have been performing the play on environment issues with great professionalism.</p>",
                        "<p>The play on environment issues was being performed with great professionalism by our students.</p>"
                    ],
                    solution_en: "<p>83.(a) Our students performed the play on environment issues with great professionalism. (Correct)<br>(b) Our students <span style=\"text-decoration: underline;\">were to perform with great professionalism the play on environment issue</span>. (Incorrect Tense and Structure)<br>(c) Our students <span style=\"text-decoration: underline;\">have been performing</span> the play on environment issues with great professionalism. (Tense has changed)<br>(d) <span style=\"text-decoration: underline;\">The play on environment issues was being performed with great professionalism by our students.</span> (Incorrect Tense and Structure)</p>",
                    solution_hi: "<p>83.(a) Our students performed the play on environment issues with great professionalism. (Correct)<br>(b) Our students <span style=\"text-decoration: underline;\">were to perform with great professionalism the play on environment issue</span>. (Sentence Structure सही नहीं है)<br>(c) Our students <span style=\"text-decoration: underline;\">have been performing</span> the play on environment issues with great professionalism. (गलत tense (present perfect continuous) का प्रयोग किया गया है। (performed) (simple past का प्रयोग होगा ।)&nbsp;<br>(d) <span style=\"text-decoration: underline;\">The play on environment issues was being performed with great professionalism by our students.</span> (Sentence Structure सही नहीं है)</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "84",
                    section: "7",
                    question_en: "<p>84. Find the correctly spelt word.</p>",
                    question_hi: "<p>84. Find the correctly spelt word.</p>",
                    options_en: [
                        "<p>ofensive</p>",
                        "<p>pleurisy</p>",
                        "<p>impasable</p>",
                        "<p>maintnance</p>"
                    ],
                    options_hi: [
                        "<p>ofensive</p>",
                        "<p>pleurisy</p>",
                        "<p>impasable</p>",
                        "<p>maintnance</p>"
                    ],
                    solution_en: "<p>84.(b) &lsquo;Pleurisy&rsquo; is the correct spelling.<br>Pleurisy is an inflammation of the lining of your lungs (pleura) that causes sharp chest pains.</p>",
                    solution_hi: "<p>84.(b) &lsquo;Pleurisy&rsquo; is the correct spelling.<br>Pleurisy आपके फेफड़ों के lining की सूजन है जो तेज सीने में दर्द का कारण बनता है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "85",
                    section: "7",
                    question_en: "<p>85. (A) He stopped at a large and brightly lighted restaurant.<br>(B) The man left his seat and walked to the great street called Broadway<br>(C) He went across this wide space.<br>(D) This was where the best food was served every evening.</p>",
                    question_hi: "<p>85. (A) He stopped at a large and brightly lighted restaurant.<br>(B) The man left his seat and walked to the great street called Broadway<br>(C) He went across this wide space.<br>(D) This was where the best food was served every evening.</p>",
                    options_en: [
                        "<p>BCAD</p>",
                        "<p>ABCD</p>",
                        "<p>ADBC</p>",
                        "<p>BDCA</p>"
                    ],
                    options_hi: [
                        "<p>BCAD</p>",
                        "<p>ABCD</p>",
                        "<p>ADBC</p>",
                        "<p>BDCA</p>"
                    ],
                    solution_en: "<p>85.(a) BCAD<br>Sentence B will be the starting line as it contains the main idea of the parajumble i.e. The man walked to the great street called Broadway. Sentence C states that he went across this street. So, C will follow B . Further, Sentence A states that he stopped there in a restaurant and Sentence D states the reason he went in the restaurant . So, D will follow A. Going through the options, option (a) BCAD has the correct sequence.</p>",
                    solution_hi: "<p>85.(a) BCAD<br>Sentence B starting line होगी क्योंकि इसमें parajumble का मुख्य विचार &lsquo;The man walked to the great street called Broadway&rsquo; शामिल है। Sentence C कहता है कि वह इस गली के पार चला गया। तो, B के बाद C आएगा। इसके अलावा, Sentence A कहता है कि वह वहाँ एक रेस्टोरेंट में रुका और Sentence D उसके रेस्टोरेंट में जाने का कारण बताता है। तो, A के बाद D आएगा । options के माध्यम से जाने पर, option (a) BCAD में सही sequence है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "86",
                    section: "7",
                    question_en: "<p>86. Select the option that rectifies the spelling error in the given sentence. <br>She is going to resiev a package in the mail today</p>",
                    question_hi: "<p>86. Select the option that rectifies the spelling error in the given sentence. <br>She is going to resiev a package in the mail today</p>",
                    options_en: [
                        "<p>recieive</p>",
                        "<p>receive</p>",
                        "<p>resieve</p>",
                        "<p>reseive</p>"
                    ],
                    options_hi: [
                        "<p>recieive</p>",
                        "<p>receive</p>",
                        "<p>resieve</p>",
                        "<p>reseive</p>"
                    ],
                    solution_en: "<p>86.(b) receive<br>&lsquo;Receive&rsquo; is the correct spelling.</p>",
                    solution_hi: "<p>86.(b) receive<br>&lsquo;Receive&rsquo; सही spelling है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "87",
                    section: "7",
                    question_en: "<p>87. Identify the segment in the sentence which contains grammatical error. Mark &lsquo;no error&rsquo; in case the sentence given is correct. <br>When I called at him yesterday, he was busy writing a book.</p>",
                    question_hi: "<p>87. Identify the segment in the sentence which contains grammatical error. Mark &lsquo;no error&rsquo; in case the sentence given is correct. <br>When I called at him yesterday, he was busy writing a book.</p>",
                    options_en: [
                        "<p>When I called at him</p>",
                        "<p>yesterday, he was busy</p>",
                        "<p>writing a book.</p>",
                        "<p>No error</p>"
                    ],
                    options_hi: [
                        "<p>When I called at him</p>",
                        "<p>yesterday, he was busy</p>",
                        "<p>writing a book.</p>",
                        "<p>No error</p>"
                    ],
                    solution_en: "<p>87.(a) When I called at him. <br>&lsquo;Call on&rsquo; is a phrase that means to make a short visit to a person or place. Hence, we will replace the preposition &lsquo;at&rsquo; with &lsquo;on&rsquo; because the narrator makes a short visit to someone when he was busy writing a book.</p>",
                    solution_hi: "<p>87.(a) When I called at him<br>&lsquo;Call on&rsquo; एक phrase है जिसका अर्थ है किसी व्यक्ति से थोड़े समय के लिए मिलना । इसलिए, हम preposition &lsquo;at&rsquo; को &lsquo;on&rsquo; से बदल देंगे क्योंकि वाचक (narrator) किसी के पास कुछ समय के लिए जाता है जब वह एक किताब लिखने में व्यस्त होता है। </p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "88",
                    section: "7",
                    question_en: "<p>88. Select the most appropriate meaning of idiom in the sentence.<br>On cloud nine</p>",
                    question_hi: "<p>88. Select the most appropriate meaning of idiom in the sentence.<br>On cloud nine</p>",
                    options_en: [
                        "<p>Extremely happy and excited</p>",
                        "<p>Very far away from home</p>",
                        "<p>Knowledgeable and wise</p>",
                        "<p>Crazy and foolish</p>"
                    ],
                    options_hi: [
                        "<p>Extremely happy and excited</p>",
                        "<p>Very far away from home</p>",
                        "<p>Knowledgeable and wise</p>",
                        "<p>Crazy and foolish</p>"
                    ],
                    solution_en: "<p>88.(a) Extremely happy and excited <br><strong>E.g-</strong> Raghav is on cloud nine as he topped his class in the recently held examination.</p>",
                    solution_hi: "<p>88.(a) Extremely happy and excited / बेहद खुश और उत्साहित<br><strong>Example -</strong> Raghav is on cloud nine as he topped his class in the recently held examination.<br><strong>उदाहरण -</strong> राघव बेहद खुश और उत्साहित है क्योंकि उसने हाल ही में आयोजित परीक्षा में अपनी कक्षा में प्रथम स्थान पाया है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "89",
                    section: "7",
                    question_en: "<p>89. Select the most appropriate option to fill in the blank.<br>Tom is trying to _______ as soon as possible.</p>",
                    question_hi: "<p>89. Select the most appropriate option to fill in the blank.<br>Tom is trying to ______ as soon as possible.</p>",
                    options_en: [
                        "<p>get a job</p>",
                        "<p>learn a job</p>",
                        "<p>receive a job</p>",
                        "<p>experience a job</p>"
                    ],
                    options_hi: [
                        "<p>get a job</p>",
                        "<p>learn a job</p>",
                        "<p>receive a job</p>",
                        "<p>experience a job</p>"
                    ],
                    solution_en: "<p>89.(a) get a job<br>The given sentence states that Tom is trying to get a job as soon as possible. Hence, &lsquo;get a job&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>89.(a) get a job<br>&lsquo;Get&rsquo; का अर्थ है कुछ प्राप्त करना। दिए गए sentence में कहा गया है कि टॉम जल्द से जल्द नौकरी पाने की कोशिश कर रहा है। अतः, &lsquo;get a job&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "90",
                    section: "7",
                    question_en: "<p>90. Given below are four jumbled sentences. Pick the option that gives their correct order.<br>(A) A constable was the first among them.<br>(B) At a corner was a shop with a wide glass window.<br>(C )The man picked up a big stone and threw it through the glass<br>(D) People came running around the corner.</p>",
                    question_hi: "<p>90. Given below are four jumbled sentences. Pick the option that gives their correct order.<br>(A) A constable was the first among them.<br>(B) At a corner was a shop with a wide glass window.<br>(C )The man picked up a big stone and threw it through the glass<br>(D) People came running around the corner.</p>",
                    options_en: [
                        "<p>DCBA</p>",
                        "<p>ACBD</p>",
                        "<p>BCDA</p>",
                        "<p>BDCA</p>"
                    ],
                    options_hi: [
                        "<p>DCBA</p>",
                        "<p>ACBD</p>",
                        "<p>BCDA</p>",
                        "<p>BDCA</p>"
                    ],
                    solution_en: "<p>90.(c) BCDA<br>Sentence B will be the starting line as it contains the main idea of the parajumble i.e. the shop with a glass window. Sentence C states that the man threw a stone through the glass . So, C will follow B . Further, Sentence D states that people started coming there and Sentence A states that a constable was the first one to come among them . So, A will follow D . Going through the options, option c has the correct sequence.</p>",
                    solution_hi: "<p>90.(c) BCDA<br>Sentence B starting line होगी क्योंकि इसमें parajumble का मुख्य विचार &lsquo;the shop with a glass window&rsquo; शामिल है। Sentence C कहता है कि आदमी ने कांच पर एक पत्थर फेंका। तो, B के बाद C आएगा। इसके अलावा, Sentence D कहता है कि लोग वहां आने लगे और Sentence A कहता है कि उनमें से सबसे पहले एक कांस्टेबल आया था। अत: D के बाद A आएगा । options के माध्यम से जाने पर, option c में सही sequence है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "91",
                    section: "7",
                    question_en: "<p>91. Select the word which means the same as the group of words given.<br>The science dealing with X-rays and other high-energy radiation, especially for the diagnosis and treatment of disease.</p>",
                    question_hi: "<p>91. Select the word which means the same as the group of words given.<br>The science dealing with X-rays and other high-energy radiation, especially for the diagnosis and treatment of disease.</p>",
                    options_en: [
                        "<p>nephrology</p>",
                        "<p>neurology</p>",
                        "<p>pathology</p>",
                        "<p>radiology</p>"
                    ],
                    options_hi: [
                        "<p>nephrology</p>",
                        "<p>neurology</p>",
                        "<p>pathology</p>",
                        "<p>radiology</p>"
                    ],
                    solution_en: "<p>91.(d) <strong>radiology - </strong>the science dealing with X-rays and other high-energy radiation<br>(a)<strong> nephrology- </strong>the branch of medicine that deals with the physiology and diseases of the kidneys.<br>(b) <strong>neurology- </strong>(related to nerves and the nervous system.) <br>(c) <strong>pathology- </strong>(the science of the causes and effects of diseases)</p>",
                    solution_hi: "<p>91.(d) <strong>radiology -</strong> विकिरण चिकित्सा विज्ञान - the science dealing with X-rays and other high-energy radiation<br>(a) <strong>nephrology-</strong>किडनी रोग विशेषज्ञ- the branch of medicine that deals with the physiology and diseases of the kidneys.<br>(b) <strong>neurology</strong>-तंत्रिका-विज्ञान- (related to nerves and the nervous system.) <br>(c) <strong>pathology</strong>-विकृति विज्ञान- (the science of the causes and effects of diseases) </p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "92",
                    section: "7",
                    question_en: "<p>92. Which of the following words has a similar meaning to the underlined word ?<br>Numerous tremors without any tectonic plate movement have <span style=\"text-decoration: underline;\">baffled</span> geologists.</p>",
                    question_hi: "<p>92. Which of the following words has a similar meaning to the underlined word ?<br>Numerous tremors without any tectonic plate movement have <span style=\"text-decoration: underline;\">baffled</span> geologists.</p>",
                    options_en: [
                        "<p>Angered</p>",
                        "<p>Perplexed</p>",
                        "<p>Amused</p>",
                        "<p>Irritated</p>"
                    ],
                    options_hi: [
                        "<p>Angered</p>",
                        "<p>Perplexed</p>",
                        "<p>Amused</p>",
                        "<p>Irritated</p>"
                    ],
                    solution_en: "<p>92.(b) <strong>Perplexed</strong>- completely puzzled or confused.<br><strong>Baffled</strong>- to be confused or bewildered.<br><strong>Angered</strong>- feeling or showing annoyance or displeasure.<br><strong>Amused</strong>- entertained or finding something funny.<br><strong>Irritated</strong>- showing annoyance or slight anger.</p>",
                    solution_hi: "<p>92.(b) <strong>Perplexed </strong>(घबराया हुआ) - completely puzzled or confused.<br><strong>Baffled </strong>(चकरा देना) - to be confused or bewildered. <br><strong>Angered</strong> (नाराज) - feeling or showing annoyance or displeasure.<br><strong>Amused </strong>(प्रसन्न) - entertained or finding something funny.<br><strong>Irritated </strong>(उत्तेजित करना) - showing annoyance or slight anger.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "93",
                    section: "7",
                    question_en: "<p>93. Pick a word opposite in the meaning to<br>Persuade</p>",
                    question_hi: "<p>93. Pick a word opposite in the meaning to<br>Persuade</p>",
                    options_en: [
                        "<p>Promote</p>",
                        "<p>Pervade</p>",
                        "<p>Dissolve</p>",
                        "<p>Dissuade</p>"
                    ],
                    options_hi: [
                        "<p>Promote</p>",
                        "<p>Pervade</p>",
                        "<p>Dissolve</p>",
                        "<p>Dissuade</p>"
                    ],
                    solution_en: "<p>93.(d) Dissuade, <br>Persuade (Verb) .It means to make somebody do something; convince. <br><strong>Dissuade (Verb) </strong>.It means to persuade somebody not to do something. <br><strong>Look at the sentence:</strong><br>Try to persuade him to come.</p>",
                    solution_hi: "<p>93.(d) Dissuade<br>Persuade (Verb) -इसका अर्थ है किसी से कुछ करवाना; मनवाना।<br><strong>Dissuade (Verb) -</strong>इसका अर्थ है किसी को कुछ न करने के लिए राजी करना। <br><strong>वाक्य देखिए:</strong><br>Try to persuade him to come./ उसे आने के लिए मनाने की कोशिश करो।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "94",
                    section: "7",
                    question_en: "<p>94. Select the most appropriate ANTONYM of the highlighted word<br>The worker is known to <strong>exert </strong>himself a lot.</p>",
                    question_hi: "<p>94. Select the most appropriate ANTONYM of the highlighted word<br>The worker is known to <strong>exert </strong>himself a lot.</p>",
                    options_en: [
                        "<p>stress</p>",
                        "<p>crouch</p>",
                        "<p>relax</p>",
                        "<p>emerge</p>"
                    ],
                    options_hi: [
                        "<p>stress</p>",
                        "<p>crouch</p>",
                        "<p>relax</p>",
                        "<p>emerge</p>"
                    ],
                    solution_en: "<p>94.(c) <strong>Relax</strong>- to rest or reduce physical or mental effort.<br><strong>Exert</strong>- to apply effort or force.<br><strong>Stress-</strong> mental or emotional strain.<br><strong>Crouch-</strong> to bend the knees and lower the body.<br><strong>Emerge-</strong> to come into view or become visible.</p>",
                    solution_hi: "<p>94.(c) <strong>Relax </strong>(आराम करना) - to rest or reduce physical or mental effort.<br><strong>Exert</strong> (बल लगाना) - to apply effort or force.<br><strong>Stress</strong> (तनाव) - mental or emotional strain.<br><strong>Crouch</strong> (झुक जाना) - to bend the knees and lower the body.<br><strong>Emerge</strong> (प्रकट होना) - to come into view or become visible.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "95",
                    section: "7",
                    question_en: "<p>95. Select the most appropriate homophones to fill in the blanks. <br>Her father _______ her to opt for a chemistry ______ in her final year.</p>",
                    question_hi: "<p>95. Select the most appropriate homophones to fill in the blanks. <br>Her father _______ her to opt for a chemistry ______ in her final year.</p>",
                    options_en: [
                        "<p>aloud; coarse</p>",
                        "<p>aloud; course</p>",
                        "<p>allowed; course</p>",
                        "<p>allowed; coarse</p>"
                    ],
                    options_hi: [
                        "<p>aloud; coarse</p>",
                        "<p>aloud; course</p>",
                        "<p>allowed; course</p>",
                        "<p>allowed; coarse</p>"
                    ],
                    solution_en: "<p>95.(c) allowed; course<br>&lsquo;Allowed&rsquo; means to give permission to do something. &lsquo;Course&rsquo; means a set of classes in a subject in a school or college. The given sentence states that her father allowed her to opt for a chemistry course in her final year. Hence, &lsquo;allowed; course&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>95.(c) allowed; course<br>&lsquo;Allowed&rsquo; का अर्थ है किसी काम को करने की permission देना। &lsquo;Course&rsquo; का अर्थ है school या college में किसी विषय की कक्षाओं का एक समूह। दिए गए sentence में कहा गया है कि उसके पिता ने उसे अपने अंतिम वर्ष में chemistry course चुनने की अनुमति दी। अतः , &lsquo;allowed; course&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "96",
                    section: "7",
                    question_en: "<p>96. <strong>Cloze Test :-</strong><br>Time is very precious and must not be wasted on petty matters that have (96)_____value in our lives. People keep money in safes but time (97)______be saved in this way. Time can be saved and utilized only by being alert, (98)______it very carefully and being conscious of its passage. People who made the right use of time by not wasting it on worldly (99)______and spent it on understanding the true nature of life and its purpose have left us with a (100)______ collection of religious books and wise thoughts. <br>Select the most appropriate option to fill in the blank no. 96</p>",
                    question_hi: "<p>96. <strong>Cloze Test :-</strong><br>Time is very precious and must not be wasted on petty matters that have (96)_____value in our lives. People keep money in safes but time (97)______be saved in this way. Time can be saved and utilized only by being alert, (98)______it very carefully and being conscious of its passage. People who made the right use of time by not wasting it on worldly (99)______and spent it on understanding the true nature of life and its purpose have left us with a (100)______ collection of religious books and wise thoughts.<br>Select the most appropriate option to fill in the blank no. 96</p>",
                    options_en: [
                        "<p>nothing</p>",
                        "<p>no</p>",
                        "<p>none</p>",
                        "<p>no one</p>"
                    ],
                    options_hi: [
                        "<p>nothing</p>",
                        "<p>no</p>",
                        "<p>none</p>",
                        "<p>no one</p>"
                    ],
                    solution_en: "<p>96.(b) no <br>The given passage states that time is very precious and must not be wasted on petty matters that have no value in our lives. Hence, <strong>&lsquo;no&rsquo; </strong>is the most appropriate answer.</p>",
                    solution_hi: "<p>96.(b) दिए गए passage में कहा गया है कि समय बहुत कीमती है और इसे उन छोटी-छोटी बातों में बर्बाद नहीं करना चाहिए जिनका हमारे जीवन में कोई मूल्य नहीं है। अतः \'no\' सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "97",
                    section: "7",
                    question_en: "<p>97. <strong>Cloze Test :-</strong><br>Time is very precious and must not be wasted on petty matters that have (96)_____value in our lives. People keep money in safes but time (97)______be saved in this way. Time can be saved and utilized only by being alert, (98)______it very carefully and being conscious of its passage. People who made the right use of time by not wasting it on worldly (99)______and spent it on understanding the true nature of life and its purpose have left us with a (100)______ collection of religious books and wise thoughts.<br>Select the most appropriate option to fill in the blank no. 97</p>",
                    question_hi: "<p>97. <strong>Cloze Test :-</strong><br>Time is very precious and must not be wasted on petty matters that have (96)_____value in our lives. People keep money in safes but time (97)______be saved in this way. Time can be saved and utilized only by being alert, (98)______it very carefully and being conscious of its passage. People who made the right use of time by not wasting it on worldly (99)______and spent it on understanding the true nature of life and its purpose have left us with a (100)______ collection of religious books and wise thoughts.<br>Select the most appropriate option to fill in the blank no. 97</p>",
                    options_en: [
                        "<p>wasn&rsquo;t</p>",
                        "<p>can&rsquo;t</p>",
                        "<p>hadn&rsquo;t</p>",
                        "<p>wouldn&rsquo;t</p>"
                    ],
                    options_hi: [
                        "<p>wasn&rsquo;t</p>",
                        "<p>can&rsquo;t</p>",
                        "<p>hadn&rsquo;t</p>",
                        "<p>wouldn&rsquo;t</p>"
                    ],
                    solution_en: "<p>97.(b) can&rsquo;t <br>The given passage states that people keep money in safes but time cannot be saved in this way. Hence, &lsquo;can&rsquo;t&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>97.(b) दिए गए गद्यांश में कहा गया है कि लोग धन को तिजोरियों में रख सकते हैं लेकिन इस तरह समय की बचत नहीं की जा सकती। इसलिए, \'can&rsquo;t\' सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "98",
                    section: "7",
                    question_en: "<p>98. <strong>Cloze Test :-</strong><br>Time is very precious and must not be wasted on petty matters that have (96)_____value in our lives. People keep money in safes but time (97)______be saved in this way. Time can be saved and utilized only by being alert, (98)______it very carefully and being conscious of its passage. People who made the right use of time by not wasting it on worldly (99)______and spent it on understanding the true nature of life and its purpose have left us with a (100)______ collection of religious books and wise thoughts.<br>Select the most appropriate option to fill in the blank no. 98</p>",
                    question_hi: "<p>98. <strong>Cloze Test :-</strong><br>Time is very precious and must not be wasted on petty matters that have (96)_____value in our lives. People keep money in safes but time (97)______be saved in this way. Time can be saved and utilized only by being alert, (98)______it very carefully and being conscious of its passage. People who made the right use of time by not wasting it on worldly (99)______and spent it on understanding the true nature of life and its purpose have left us with a (100)______ collection of religious books and wise thoughts.<br>Select the most appropriate option to fill in the blank no. 98</p>",
                    options_en: [
                        "<p>holding</p>",
                        "<p>creating</p>",
                        "<p>treating</p>",
                        "<p>spending</p>"
                    ],
                    options_hi: [
                        "<p>holding</p>",
                        "<p>creating</p>",
                        "<p>treating</p>",
                        "<p>spending</p>"
                    ],
                    solution_en: "<p>98.(d) &lsquo;Spending&rsquo; means to pass time. The given passage states that time can be saved and utilized only by being alert, spending it very carefully and being conscious of its passage. Hence, &lsquo;spending&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>98.(d) \'Spending\' का अर्थ है समय व्यतीत करना। दिए गए passage में कहा गया है कि समय को केवल सतर्क रहकर, बहुत सावधानी से खर्च करके उपयोग किया जा सकता है। इसलिए, \'spending\' सबसे उपयुक्त उत्तर है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "99",
                    section: "7",
                    question_en: "<p>99. <strong>Cloze Test :-</strong><br>Time is very precious and must not be wasted on petty matters that have (96)_____value in our lives. People keep money in safes but time (97)______be saved in this way. Time can be saved and utilized only by being alert, (98)______it very carefully and being conscious of its passage. People who made the right use of time by not wasting it on worldly (99)______and spent it on understanding the true nature of life and its purpose have left us with a (100)______ collection of religious books and wise thoughts.<br>Select the most appropriate option to fill in the blank no. 99</p>",
                    question_hi: "<p>99. <strong>Cloze Test :-</strong><br>Time is very precious and must not be wasted on petty matters that have (96)_____value in our lives. People keep money in safes but time (97)______be saved in this way. Time can be saved and utilized only by being alert, (98)______it very carefully and being conscious of its passage. People who made the right use of time by not wasting it on worldly (99)______and spent it on understanding the true nature of life and its purpose have left us with a (100)______ collection of religious books and wise thoughts.<br>Select the most appropriate option to fill in the blank no. 99</p>",
                    options_en: [
                        "<p>laughter</p>",
                        "<p>subjects</p>",
                        "<p>pleasures</p>",
                        "<p>chances</p>"
                    ],
                    options_hi: [
                        "<p>laughter</p>",
                        "<p>subjects</p>",
                        "<p>pleasures</p>",
                        "<p>chances</p>"
                    ],
                    solution_en: "<p>99.(c) &lsquo;Pleasures&rsquo; means a feeling of happy satisfaction and enjoyment. The given passage talks about the people who made the right use of time by not wasting it on worldly pleasures (satisfaction and enjoyment). Hence, &lsquo;pleasures&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>99.(c) \'Pleasures\' का अर्थ है सुखद संतुष्टि और आनंद की अनुभूति। दिया गया passage उन लोगों के बारे में बात करता है जिन्होंने सांसारिक सुखों (संतुष्टि और आनंद) पर इसे बर्बाद न करके समय का सही उपयोग किया। इसलिए, \'pleasures\' सबसे उपयुक्त उत्तर है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "100",
                    section: "misc",
                    question_en: "<p>100. <strong>Cloze Test :-</strong><br>Time is very precious and must not be wasted on petty matters that have (96)_____value in our lives. People keep money in safes but time (97)______be saved in this way. Time can be saved and utilized only by being alert, (98)______it very carefully and being conscious of its passage. People who made the right use of time by not wasting it on worldly (99)______and spent it on understanding the true nature of life and its purpose have left us with a (100)______ collection of religious books and wise thoughts.<br>Select the most appropriate option to fill in the blank no. 100</p>",
                    question_hi: "<p>100. <strong>Cloze Test :-</strong><br>Time is very precious and must not be wasted on petty matters that have (96)_____value in our lives. People keep money in safes but time (97)______be saved in this way. Time can be saved and utilized only by being alert, (98)______it very carefully and being conscious of its passage. People who made the right use of time by not wasting it on worldly (99)______and spent it on understanding the true nature of life and its purpose have left us with a (100)______ collection of religious books and wise thoughts.<br>Select the most appropriate option to fill in the blank no. 100</p>",
                    options_en: [
                        "<p>richer</p>",
                        "<p>richest</p>",
                        "<p>too rich</p>",
                        "<p>rich</p>"
                    ],
                    options_hi: [
                        "<p>richer</p>",
                        "<p>richest</p>",
                        "<p>too rich</p>",
                        "<p>rich</p>"
                    ],
                    solution_en: "<p>100.(d) &lsquo;Rich&rsquo; means containing a lot of something. The given passage talks about the people who spent time on understanding the true nature of life and its purpose have left us with a rich collection of religious books and wise thoughts. Hence, &lsquo;rich&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>100.(d) \'Rich\' का अर्थ है बहुत कुछ युक्त। दिया गया passage उन लोगों के बारे में बात करता है जिन्होंने जीवन की वास्तविक प्रकृति को समझने में समय बिताया और इसके उद्देश्य ने हमें &lsquo;धार्मिक पुस्तकों और बुद्धिमान विचारों&rsquo; के समृद्ध संग्रह के साथ छोड़ दिया है। इसलिए, \'rich\' सबसे उपयुक्त उत्तर है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'question-box d-flex align-items-center justify-content-center border rounded';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.question-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            const html = `
                <div class="card shadow-sm mb-4">
                    <div class="card-body">
                        <div class="question-text mb-4">
                            <div class="en">${question.question_en}</div>
                            <div class="hi" style="display:none">${question.question_hi}</div>
                        </div>
                        <div class="options">
                            ${['a', 'b', 'c', 'd'].map((opt, i) => `
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="radio" name="q${index}" 
                                        id="opt-${index}-${opt}" value="${opt}" 
                                        ${answers[question.id] === opt ? 'checked' : ''}
                                        onclick="selectAnswer('${question.id}', '${opt}')">
                                    <label class="form-check-label w-100 py-2 px-3 rounded hover-bg-light" for="opt-${index}-${opt}">
                                        <div class="en">${question.options_en[i]}</div>
                                        <div class="hi" style="display:none">${question.options_hi[i]}</div>
                                    </label>
                                </div>
                            `).join('')}
                        </div>
                        ${submitted ? `
                            <div class="alert ${answers[question.id] === question.correct ? 'alert-success' : 'alert-danger'} mt-3">
                                ${answers[question.id] ? (answers[question.id] === question.correct ? 'Correct!' : 'Incorrect!') : 'Not attempted'}
                                <div class="mt-2">
                                    <strong>Solution:</strong>
                                    <div class="en">${question.solution_en}</div>
                                    <div class="hi" style="display:none">${question.solution_hi}</div>
                                </div>
                            </div>
                        ` : ''}
                    </div>
                </div>
            `;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // If in mobile view, close the menu
            if (window.innerWidth < 768) {
                toggleMenu(false);
            }
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                document.getElementById(`box-${index}`).classList.add('attempted');
            }
        }
        // Navigate to previous question
        function prevQuestion() {
            if (currentQuestion > 0) {
                showQuestion(currentQuestion - 1);
            }
        }
        // Navigate to next question
        function nextQuestion() {
            if (currentQuestion < questions.length - 1) {
                showQuestion(currentQuestion + 1);
            }
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) {
                showQuestion(section.start);
            }
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update button text
            document.getElementById('lang-btn').innerHTML = currentLang === 'en' ? 
                '<i class="fas fa-language"></i> हिंदी' : 
                '<i class="fas fa-language"></i> English';
        }
        // Toggle menu for mobile
        function toggleMenu(show) {
            const nav = document.getElementById('question-nav');
            const content = document.getElementById('question-content');
            if (show === undefined) {
                show = nav.style.right === '-100%' || nav.style.right === '';
            }
            if (show) {
                nav.style.right = '0';
                if (window.innerWidth >= 768) {
                    content.style.marginRight = '320px';
                }
            } else {
                nav.style.right = window.innerWidth < 768 ? '-100%' : '0';
                content.style.marginRight = window.innerWidth < 768 ? '0' : '320px';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
            document.getElementById('total-count').textContent = questions.length;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.remove('attempted');
                    box.classList.add('bg-secondary', 'text-white');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.remove('attempted');
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.remove('attempted');
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            resultsModal.show();
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check-circle me-1"></i> Submitted';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            resultsModal.hide();
            showQuestion(0);
        }
    </script>
</body>
</html>