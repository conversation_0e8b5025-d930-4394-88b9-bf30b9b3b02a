<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">20:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 15</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">15</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 20 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 0,
                end: 13
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 14,
                end: 14
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">1. </span><span style=\"font-family: Cambria Math;\">If </span><span style=\"font-family: Cambria Math;\">&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi mathvariant=\"normal\">k</mi><mn>4</mn></msup><mo>+</mo><mfrac><mn>1</mn><msup><mi mathvariant=\"normal\">k</mi><mn>4</mn></msup></mfrac><mo>=</mo><mn>194</mn></math>, then what is the value of <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi mathvariant=\"normal\">k</mi><mn>3</mn></msup><mo>+</mo><mfrac><mn>1</mn><msup><mi mathvariant=\"normal\">k</mi><mn>3</mn></msup></mfrac></math></span><span style=\"font-family: Cambria Math;\">?</span></p>",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">1. </span><span style=\"font-family: Cambria Math;\">यदि</span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi mathvariant=\"normal\">k</mi><mn>4</mn></msup><mo>+</mo><mfrac><mn>1</mn><msup><mi mathvariant=\"normal\">k</mi><mn>4</mn></msup></mfrac><mo>=</mo><mn>194</mn></math></span><span style=\"font-family: Cambria Math;\">&nbsp;</span><span style=\"font-family: Cambria Math;\">है</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">तो</span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi mathvariant=\"normal\">k</mi><mn>3</mn></msup><mo>+</mo><mfrac><mn>1</mn><msup><mi mathvariant=\"normal\">k</mi><mn>3</mn></msup></mfrac></math></span><span style=\"font-family: Cambria Math;\">&nbsp;</span><span style=\"font-family: Cambria Math;\">का</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">मान</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">क्या</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">है</span><span style=\"font-family: Cambria Math;\">?</span></p>",
                    options_en: ["<p>42</p>", "<p>52</p>", 
                                "<p>36</p>", "<p>18</p>"],
                    options_hi: ["<p>42</p>", "<p>52</p>",
                                "<p>36</p>", "<p>18</p>"],
                    solution_en: "<p>1.(b) <span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>If</mi><mo>&#160;</mo><mi mathvariant=\"normal\">x</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mfrac><mn>1</mn><mi mathvariant=\"normal\">x</mi></mfrac><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mi mathvariant=\"normal\">k</mi><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo></math></span><br><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>then</mi><mo>&#160;</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mfrac><mn>1</mn><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup></mfrac><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">k</mi><mn>2</mn></msup><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mn>2</mn><mo>&#160;</mo></math></span><br><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>and</mi><mo>&#160;</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">x</mi><mn>3</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mfrac><mn>1</mn><msup><mi mathvariant=\"normal\">x</mi><mn>3</mn></msup></mfrac><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">k</mi><mn>3</mn></msup><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mn>3</mn><mi mathvariant=\"normal\">k</mi></math></span><br><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>Given</mi><mo>&#160;</mo><mi>that</mi><mo>&#160;</mo><mo>,</mo></math></span><br><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi mathvariant=\"normal\">k</mi><mn>4</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mfrac><mn>1</mn><msup><mi mathvariant=\"normal\">k</mi><mn>4</mn></msup></mfrac><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>194</mn><mo>&#160;</mo></math></span><br><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>Then</mi><mo>,</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">k</mi><mn>2</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mfrac><mn>1</mn><msup><mi mathvariant=\"normal\">k</mi><mn>2</mn></msup></mfrac><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>14</mn></math></span><br><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">k</mi><mo>&#160;</mo><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mo>&#160;</mo><mfrac><mn>1</mn><mi mathvariant=\"normal\">k</mi></mfrac><mo>&#160;</mo><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>4</mn><mo>&#160;</mo><mo>,</mo><mo>&#160;</mo><mo>&#160;</mo></math></span><br><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>Now</mi><mo>&#160;</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">k</mi><mn>3</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mfrac><mn>1</mn><msup><mi mathvariant=\"normal\">k</mi><mn>3</mn></msup></mfrac><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>52</mn></math></span></p>",
                    solution_hi: "<p>1.(b) <span style=\"font-family: Cambria Math;\"><math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#2351;&#2342;&#2367;</mi><mo>&#160;</mo><mo>&#160;</mo><mi mathvariant=\"normal\">x</mi><mo>&#160;</mo><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mo>&#160;</mo><mfrac><mn>1</mn><mi mathvariant=\"normal\">x</mi></mfrac><mo>&#160;</mo><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mo>&#160;</mo><mi mathvariant=\"normal\">k</mi><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo></math></span><br><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#2340;&#2348;</mi><mo>&#160;</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mfrac><mn>1</mn><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup></mfrac><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">k</mi><mn>2</mn></msup><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mn>2</mn><mo>&#160;</mo></math></span><br><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#2324;&#2352;</mi><mo>&#160;</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">x</mi><mn>3</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mfrac><mn>1</mn><msup><mi mathvariant=\"normal\">x</mi><mn>3</mn></msup></mfrac><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">k</mi><mn>3</mn></msup><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mn>3</mn><mi mathvariant=\"normal\">k</mi></math></span><br><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#2342;&#2367;&#2351;&#2366;</mi><mo>&#160;</mo><mi>&#2327;&#2351;&#2366;</mi><mo>&#160;</mo><mi>&#2361;&#2376;</mi><mo>&#160;</mo><mi>&#2325;&#2367;</mi><mo>&#160;</mo><mo>,</mo></math></span><br><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi mathvariant=\"normal\">k</mi><mn>4</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mfrac><mn>1</mn><msup><mi mathvariant=\"normal\">k</mi><mn>4</mn></msup></mfrac><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>194</mn><mo>&#160;</mo></math></span><br><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#2340;&#2348;</mi><mo>&#160;</mo><mo>,</mo><mo>&#160;</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">k</mi><mn>2</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mfrac><mn>1</mn><msup><mi mathvariant=\"normal\">k</mi><mn>2</mn></msup></mfrac><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>14</mn></math></span><br><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">k</mi><mo>&#160;</mo><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mo>&#160;</mo><mfrac><mn>1</mn><mi mathvariant=\"normal\">k</mi></mfrac><mo>&#160;</mo><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>4</mn><mo>&#160;</mo><mo>,</mo></math></span><br><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#2309;&#2348;</mi><mo>&#160;</mo><mo>,</mo><mo>&#160;</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">k</mi><mn>3</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mfrac><mn>1</mn><msup><mi mathvariant=\"normal\">k</mi><mn>3</mn></msup></mfrac><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>52</mn></math></span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">2.</span><span style=\"font-family: Cambria Math;\"> If </span><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">X</mi><mo>+</mo><mfrac><mn>1</mn><mrow><mi mathvariant=\"normal\">X</mi><mo>&#160;</mo></mrow></mfrac><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>8</mn></math> , then find the value of&nbsp; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mrow><msup><mi>x</mi><mn>2</mn></msup><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mn>8</mn><mi>x</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>2</mn></mrow></mfrac></math></span></p>",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">2. </span><span style=\"font-family: Cambria Math;\">यदि&nbsp;</span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">X</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mfrac><mn>1</mn><mrow><mi mathvariant=\"normal\">X</mi><mo>&#160;</mo></mrow></mfrac><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>8</mn></math></span><span style=\"font-family: Cambria Math;\">,&nbsp; </span><span style=\"font-family: Cambria Math;\">है </span><span style=\"font-family: Cambria Math;\">तो</span><span style=\"font-family: Cambria Math;\"> &nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mrow><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mn>8</mn><mi mathvariant=\"normal\">x</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>2</mn></mrow></mfrac></math> </span><span style=\"font-family: Cambria Math;\">का</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">मान</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">ज्ञात</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">कीजिए</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">।</span></p>",
                    options_en: ["<p>3</p>", "<p>4</p>", 
                                "<p>0</p>", "<p>5</p>"],
                    options_hi: ["<p>3</p>", "<p>4</p>",
                                "<p>0</p>", "<p>5</p>"],
                    solution_en: "<p>2.(d) <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>Given</mi><mo>&#160;</mo><mo>,</mo><mo>&#160;</mo><mo>&#160;</mo><mi mathvariant=\"normal\">x</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mfrac><mn>1</mn><mi mathvariant=\"normal\">x</mi></mfrac><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mo>&#160;</mo><mn>8</mn><mo>&#160;</mo></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo><mo>&#160;</mo><mi mathvariant=\"normal\">x</mi><mo>&#160;</mo><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mn>8</mn><mo>=</mo><mo>&#160;</mo><mfrac><mrow><mo>-</mo><mn>1</mn></mrow><mi mathvariant=\"normal\">x</mi></mfrac><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo><mo>.</mo><mo>.</mo><mo>.</mo><mo>&#160;</mo><mi>eq</mi><mo>(</mo><mn>1</mn><mo>)</mo></math><br><span style=\"font-weight: 400;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>Now</mi><mo>&#160;</mo><mo>&#160;</mo><mfrac><mn>5</mn><mrow><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mn>8</mn><mi mathvariant=\"normal\">x</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>2</mn></mrow></mfrac><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mfrac><mn>5</mn><mrow><mi mathvariant=\"normal\">x</mi><mo>&#160;</mo><mfenced><mrow><mo>&#160;</mo><mi mathvariant=\"normal\">x</mi><msup><mo>&#160;</mo><mo>&#160;</mo></msup><mo>-</mo><mo>&#160;</mo><mn>8</mn><mo>&#160;</mo><mo>&#160;</mo><mo>+</mo><mstyle displaystyle=\"true\"><mo>&#160;</mo><mfrac><mn>2</mn><mi mathvariant=\"normal\">x</mi></mfrac><mo>&#160;</mo></mstyle></mrow></mfenced></mrow></mfrac><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo><mo>.</mo><mo>.</mo><mo>.</mo><mo>.</mo><mi>eq</mi><mo>(</mo><mn>2</mn><mo>)</mo></math></span><br><span style=\"font-weight: 400;\">Putting the value of eq.(1) in eq. (2) , we get</span><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo><mo>&#160;</mo><mfrac><mn>5</mn><mrow><mi mathvariant=\"normal\">x</mi><mo>&#160;</mo><mfenced><mrow><mo>&#160;</mo><mi mathvariant=\"normal\">x</mi><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mn>8</mn><mo>&#160;</mo><mo>+</mo><mstyle displaystyle=\"true\"><mo>&#160;</mo><mfrac><mn>2</mn><mi mathvariant=\"normal\">x</mi></mfrac><mo>&#160;</mo></mstyle></mrow></mfenced></mrow></mfrac><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mfrac><mn>5</mn><mrow><mi mathvariant=\"normal\">x</mi><mo>&#160;</mo><mfenced><mrow><mo>&#160;</mo><mstyle displaystyle=\"true\"><mfrac><mn>2</mn><mi mathvariant=\"normal\">x</mi></mfrac><mo>&#160;</mo></mstyle><mo>-</mo><mstyle displaystyle=\"true\"><mo>&#160;</mo><mfrac><mn>1</mn><mi mathvariant=\"normal\">x</mi></mfrac><mo>&#160;</mo></mstyle></mrow></mfenced></mrow></mfrac><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>5</mn></math></p>",
                    solution_hi: "<p>2.(d) <math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#2342;&#2367;&#2351;&#2366;</mi><mo>&#160;</mo><mi>&#2327;&#2351;&#2366;</mi><mo>&#160;</mo><mi>&#2361;&#2376;</mi><mo>&#160;</mo><mo>,</mo><mo>&#160;</mo><mo>&#160;</mo><mi mathvariant=\"normal\">x</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mfrac><mn>1</mn><mi mathvariant=\"normal\">x</mi></mfrac><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mo>&#160;</mo><mn>8</mn><mo>&#160;</mo></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo><mo>&#160;</mo><mi>x</mi><mo>&#160;</mo><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mn>8</mn><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mfrac><mrow><mo>-</mo><mn>1</mn></mrow><mi>x</mi></mfrac><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo><mo>.</mo><mo>.</mo><mo>.</mo><mo>&#160;</mo><mi>&#2360;&#2350;&#2368;&#2325;&#2352;&#2339;</mi><mo>&#160;</mo><mo>(</mo><mn>1</mn><mo>)</mo></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#2309;&#2348;</mi><mo>&#160;</mo><mo>&#160;</mo><mfrac><mn>5</mn><mrow><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mn>8</mn><mi mathvariant=\"normal\">x</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>2</mn></mrow></mfrac><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mfrac><mn>5</mn><mrow><mi mathvariant=\"normal\">x</mi><mo>&#160;</mo><mfenced><mrow><mo>&#160;</mo><mi mathvariant=\"normal\">x</mi><msup><mo>&#160;</mo><mo>&#160;</mo></msup><mo>-</mo><mo>&#160;</mo><mn>8</mn><mo>&#160;</mo><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mstyle displaystyle=\"true\"><mfrac><mn>2</mn><mi mathvariant=\"normal\">x</mi></mfrac><mo>&#160;</mo></mstyle></mrow></mfenced></mrow></mfrac><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo><mo>.</mo><mo>.</mo><mo>.</mo><mo>.</mo><mi>&#2360;&#2350;&#2368;&#2325;&#2352;&#2339;</mi><mo>&#160;</mo><mo>(</mo><mn>2</mn><mo>)</mo></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#2360;&#2350;&#2368;&#2325;&#2352;&#2339;</mi><mo>&#160;</mo><mo>(</mo><mn>1</mn><mo>)</mo><mo>&#160;</mo><mi>&#2325;&#2366;</mi><mo>&#160;</mo><mi>&#2350;&#2366;&#2344;</mi><mo>&#160;</mo><mi>&#2360;&#2350;&#2368;&#2325;&#2352;&#2339;</mi><mo>&#160;</mo><mo>(</mo><mn>2</mn><mo>)</mo><mo>&#160;</mo><mi>&#2350;&#2375;&#2306;</mi><mo>&#160;</mo><mi>&#2352;&#2326;&#2344;&#2375;</mi><mo>&#160;</mo><mi>&#2346;&#2352;</mi><mo>,</mo><mo>&#160;</mo><mi>&#2361;&#2350;&#2375;</mi><mo>&#160;</mo><mi>&#2346;&#2381;&#2352;&#2366;&#2346;&#2381;&#2340;</mi><mo>&#160;</mo><mi>&#2361;&#2379;&#2340;&#2366;</mi><mo>&#160;</mo><mi>&#2361;&#2376;&#2306;</mi></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo><mo>&#160;</mo><mfrac><mn>5</mn><mrow><mi mathvariant=\"normal\">x</mi><mo>&#160;</mo><mfenced><mrow><mo>&#160;</mo><mi mathvariant=\"normal\">x</mi><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mn>8</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mstyle displaystyle=\"true\"><mfrac><mn>2</mn><mi mathvariant=\"normal\">x</mi></mfrac><mo>&#160;</mo></mstyle></mrow></mfenced></mrow></mfrac><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mfrac><mn>5</mn><mrow><mi mathvariant=\"normal\">x</mi><mo>&#160;</mo><mfenced><mstyle displaystyle=\"true\"><mo>&#160;</mo><mfrac><mn>2</mn><mi mathvariant=\"normal\">x</mi></mfrac><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mfrac><mn>1</mn><mi mathvariant=\"normal\">x</mi></mfrac><mo>&#160;</mo></mstyle></mfenced></mrow></mfrac><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>5</mn></math></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">3.</span><span style=\"font-family: Cambria Math;\"> If </span><span style=\"font-family: Cambria Math;\">&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">k</mi><mo>&#160;</mo><mo>+</mo><mfrac><mn>1</mn><mi mathvariant=\"normal\">k</mi></mfrac><mo>=</mo><mn>3</mn></math>, then what is the value of&nbsp; </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msup><mi mathvariant=\"normal\">k</mi><mn>3</mn></msup></mfrac><mo>+</mo><msup><mi mathvariant=\"normal\">k</mi><mn>3</mn></msup></math><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">?</span></p>",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">3. <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">k</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mfrac><mn>1</mn><mi mathvariant=\"normal\">k</mi></mfrac><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>3</mn></math> </span><span style=\"font-family: Cambria Math;\">यदि</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&nbsp;</span><span style=\"font-family: Cambria Math;\">है</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">तो</span><span style=\"font-family: Cambria Math;\"> &nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msup><mi mathvariant=\"normal\">k</mi><mn>3</mn></msup></mfrac><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">k</mi><mn>3</mn></msup></math></span><span style=\"font-family: Cambria Math;\">&nbsp;</span><span style=\"font-family: Cambria Math;\">का</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">मान</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">क्या</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">है</span><span style=\"font-family: Cambria Math;\">?</span></p>",
                    options_en: ["<p>36</p>", "<p>10</p>", 
                                "<p>18</p>", "<p>54</p>"],
                    options_hi: ["<p>36</p>", "<p>10</p>",
                                "<p>18</p>", "<p>54</p>"],
                    solution_en: "<p>3.(c) <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>When</mi><mo>&#160;</mo><mo>&#160;</mo><mi mathvariant=\"normal\">x</mi><mo>&#160;</mo><mo>+</mo><mfrac><mn>1</mn><mi mathvariant=\"normal\">x</mi></mfrac><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mi mathvariant=\"normal\">k</mi><mo>&#160;</mo><mo>&#160;</mo><mo>,</mo><mo>&#160;</mo><mo>&#160;</mo></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>then</mi><mo>&#160;</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">x</mi><mn>3</mn></msup><mo>+</mo><mfrac><mn>1</mn><msup><mi mathvariant=\"normal\">x</mi><mn>3</mn></msup></mfrac><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">k</mi><mn>3</mn></msup><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mn>3</mn><mi mathvariant=\"normal\">k</mi></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>k</mi><mo>&#160;</mo><mo>+</mo><mfrac><mn>1</mn><mi>k</mi></mfrac><mo>=</mo><mn>3</mn><mo>.</mo><mo>.</mo><mo>.</mo><mo>.</mo><mo>.</mo><mo>.</mo><mo>.</mo><mo>.</mo><mo>(</mo><mi>g</mi><mi>i</mi><mi>v</mi><mi>e</mi><mi>n</mi><mo>)</mo></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>Then</mi><mo>&#160;</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">k</mi><mn>3</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mfrac><mn>1</mn><msup><mi mathvariant=\"normal\">k</mi><mn>3</mn></msup></mfrac><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><msup><mn>3</mn><mn>3</mn></msup><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mn>3</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>3</mn><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>18</mn></math></p>",
                    solution_hi: "<p>3.(c) <math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#2332;&#2348;</mi><mo>&#160;</mo><mo>,</mo><mo>&#160;</mo><mo>&#160;</mo><mi mathvariant=\"normal\">x</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mfrac><mn>1</mn><mi mathvariant=\"normal\">x</mi></mfrac><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mi mathvariant=\"normal\">k</mi><mo>&#160;</mo><mo>&#160;</mo><mo>,</mo><mo>&#160;</mo></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#160;</mo><mi>&#2340;&#2348;</mi><mo>&#160;</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">x</mi><mn>3</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mfrac><mn>1</mn><msup><mi mathvariant=\"normal\">x</mi><mn>3</mn></msup></mfrac><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">k</mi><mn>3</mn></msup><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mn>3</mn><mi mathvariant=\"normal\">k</mi></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">k</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mfrac><mn>1</mn><mi mathvariant=\"normal\">k</mi></mfrac><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>3</mn><mo>.</mo><mo>.</mo><mo>.</mo><mo>.</mo><mo>.</mo><mo>.</mo><mo>.</mo><mo>.</mo><mo>(</mo><mi>&#2342;&#2367;&#2351;&#2366;</mi><mo>&#160;</mo><mi>&#2327;&#2351;&#2366;</mi><mo>&#160;</mo><mi>&#2361;&#2376;</mi><mo>&#160;</mo><mo>)</mo></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#2340;&#2348;</mi><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">k</mi><mn>3</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mfrac><mn>1</mn><mi mathvariant=\"normal\">k</mi></mfrac><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><msup><mn>3</mn><mn>3</mn></msup><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mn>3</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>3</mn><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>18</mn></math></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">4. </span><span style=\"font-family: Cambria Math;\">Which of the following statement is correct?</span><br>I. The value of 100<sup>2</sup> - 99<sup>2</sup> + 98<sup>2</sup> - 97<sup>2</sup> + 96<sup>2</sup> - 95<sup>2</sup> +&nbsp; 94<sup>2</sup> - 93<sup>2</sup>&nbsp; +&nbsp; ......&nbsp; + 2<sup>2</sup> -&nbsp; 1<sup>2</sup>&nbsp; is&nbsp; 5050.<br><span style=\"font-weight: 400;\">II. If </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>8</mn><mi>x</mi><mo>+</mo><mfrac><mn>8</mn><mi>x</mi></mfrac></math><span style=\"font-weight: 400;\"> </span><span style=\"font-weight: 400;\">= -16 and x &lt;0, then the value of &nbsp; </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi mathvariant=\"normal\">x</mi><mn>197</mn></msup><mo>+</mo><msup><mi mathvariant=\"normal\">x</mi><mrow><mo>-</mo><mn>197</mn></mrow></msup></math><span style=\"font-weight: 400;\">&nbsp; is 2.</span></p>",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">4. </span><span style=\"font-family: Cambria Math;\">निम्नलिखित</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">में</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">से</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">कौन</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">सा</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">कथन</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">सही</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">है</span><span style=\"font-family: Cambria Math;\">?</span><br><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">I. </span>100<sup>2</sup> - 99<sup>2</sup>+ 98<sup>2</sup> - 97<sup>2</sup>+ 96<sup>2</sup> - 95<sup>2</sup> +&nbsp; 94<sup>2</sup> - 93<sup>2</sup>&nbsp; +&nbsp; ......&nbsp; + 2<sup>2</sup> -&nbsp; 1<sup>2</sup> <span style=\"font-weight: 400;\">का मान 5050 है।</span></span><br><math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>II</mi><mo>.</mo><mo>&#160;</mo><mi>&#2351;&#2342;&#2367;</mi><mo>&#160;</mo><mo>&#160;</mo><mn>8</mn><mi mathvariant=\"normal\">x</mi><mo>+</mo><mfrac><mn>8</mn><mi mathvariant=\"normal\">x</mi></mfrac><mo>&#160;</mo><mo>=</mo><mo>-</mo><mn>16</mn><mo>&#160;</mo><mo>&#160;</mo><mi>&#2340;&#2341;&#2366;</mi><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo><mi mathvariant=\"normal\">x</mi><mo>&#160;</mo><mo>&#60;</mo><mo>&#160;</mo><mn>0</mn><mo>,</mo><mo>&#160;</mo><mi>&#2340;&#2379;</mi><mo>&#160;</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">X</mi><mn>197</mn></msup><mo>+</mo><msup><mi mathvariant=\"normal\">x</mi><mrow><mo>-</mo><mn>197</mn></mrow></msup><mo>&#160;</mo><mo>&#160;</mo><mi>&#2325;&#2366;</mi><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo><mi>&#2350;&#2366;&#2344;</mi><mo>&#160;</mo><mo>&#160;</mo><mn>2</mn><mo>&#160;</mo><mo>&#160;</mo><mi>&#2361;&#2376;&#2404;</mi></math></p>",
                    options_en: ["<p>Only I</p>", "<p>Only II</p>", 
                                "<p>Both I and II</p>", "<p>Neither I nor II</p>\n<p><span style=\"font-family: Cambria Math;\"> </span></p>"],
                    options_hi: ["<p><span style=\"font-family: Cambria Math;\">केवल</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">।</span></p>", "<p><span style=\"font-family: Cambria Math;\">केवल</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">॥</span></p>",
                                "<p><span style=\"font-family: Cambria Math;\">।</span><span style=\"font-family: Cambria Math;\"> &nbsp;</span><span style=\"font-family: Cambria Math;\">तथा&nbsp;</span><span style=\"font-family: Cambria Math;\"> <span style=\"font-weight: 400;\">॥</span> </span><span style=\"font-family: Cambria Math;\">दोनों</span></p>", "<p><span style=\"font-family: Cambria Math;\">ना</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">ही</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">।</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">ना</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">ही</span><span style=\"font-family: Cambria Math;\"> <span style=\"font-weight: 400;\">॥</span></span></p>"],
                    solution_en: "<p>4.(a) <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>Statement</mi><mo>&#160;</mo><mn>1</mn><mo>.</mo></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#160;</mo><mi>Sum</mi><mo>&#160;</mo><mi>of</mi><mo>&#160;</mo><mi>squares</mi><mo>&#160;</mo><mi>of</mi><mo>&#160;</mo><mi mathvariant=\"normal\">n</mi><mo>&#160;</mo><mi>even</mi><mo>&#160;</mo><mi>natural</mi><mo>&#160;</mo><mi>no</mi><mo>.</mo><mo>=</mo><mfrac><mn>2</mn><mn>3</mn></mfrac><mo>&#215;</mo><mo>(</mo><mi mathvariant=\"normal\">n</mi><mo>)</mo><mo>(</mo><mi mathvariant=\"normal\">n</mi><mo>+</mo><mn>1</mn><mo>)</mo><mo>(</mo><mn>2</mn><mi mathvariant=\"normal\">n</mi><mo>&#160;</mo><mo>+</mo><mn>1</mn><mo>)</mo></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#160;</mo><mo>&#160;</mo><mi>Sum</mi><mo>&#160;</mo><mi>of</mi><mo>&#160;</mo><mi>squares</mi><mo>&#160;</mo><mi>of</mi><mo>&#160;</mo><mi mathvariant=\"normal\">n</mi><mo>&#160;</mo><mi>odd</mi><mo>&#160;</mo><mi>natural</mi><mo>&#160;</mo><mi>no</mi><mo>.</mo><mo>&#160;</mo><mo>=</mo><mfrac><mn>1</mn><mn>3</mn></mfrac><mo>&#215;</mo><mi mathvariant=\"normal\">n</mi><mo>&#215;</mo><mo>(</mo><mn>2</mn><mi mathvariant=\"normal\">n</mi><mo>+</mo><mn>1</mn><mo>)</mo><mo>&#215;</mo><mo>(</mo><mn>2</mn><mi mathvariant=\"normal\">n</mi><mo>&#160;</mo><mo>-</mo><mn>1</mn><mo>)</mo></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#160;</mo><mo>=</mo><mo>(</mo><msup><mn>100</mn><mn>2</mn></msup><mo>+</mo><msup><mn>98</mn><mn>2</mn></msup><mo>+</mo><msup><mn>96</mn><mn>2</mn></msup><mo>.</mo><mo>.</mo><mo>.</mo><mo>.</mo><mo>.</mo><mo>.</mo><mo>.</mo><mo>+</mo><msup><mn>2</mn><mn>2</mn></msup><mo>&#160;</mo><mo>)</mo><mo>-</mo><mo>(</mo><mo>&#160;</mo><msup><mn>99</mn><mn>2</mn></msup><mo>+</mo><msup><mn>97</mn><mn>2</mn></msup><mo>+</mo><msup><mn>95</mn><mrow><mn>2</mn><mo>&#160;</mo><mo>.</mo><mo>.</mo><mo>.</mo><mo>.</mo><mo>.</mo><mo>.</mo><mo>.</mo></mrow></msup><mo>&#160;</mo><mo>+</mo><msup><mn>1</mn><mn>2</mn></msup><mo>)</mo></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>=</mo><mfrac><mn>2</mn><mn>3</mn></mfrac><mo>&#215;</mo><mo>(</mo><mn>50</mn><mo>)</mo><mo>(</mo><mn>50</mn><mo>+</mo><mn>1</mn><mo>)</mo><mo>(</mo><mo>&#160;</mo><mn>2</mn><mo>&#215;</mo><mn>50</mn><mo>+</mo><mn>1</mn><mo>)</mo><mo>&#160;</mo><mo>-</mo><mfrac><mn>1</mn><mn>3</mn></mfrac><mo>&#215;</mo><mn>50</mn><mo>&#215;</mo><mo>(</mo><mo>&#160;</mo><mn>2</mn><mo>&#215;</mo><mn>50</mn><mo>+</mo><mn>1</mn><mo>)</mo><mo>&#215;</mo><mo>(</mo><mo>&#160;</mo><mn>2</mn><mo>&#215;</mo><mn>50</mn><mo>-</mo><mn>1</mn><mo>)</mo><mo>&#160;</mo></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>=</mo><mfrac><mn>1</mn><mn>3</mn></mfrac><mo>&#215;</mo><mn>50</mn><mo>&#160;</mo><mo>&#215;</mo><mn>101</mn><mo>&#215;</mo><mo>(</mo><mn>102</mn><mo>-</mo><mn>99</mn><mo>)</mo><mo>=</mo><mn>5050</mn><mo>&#160;</mo></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>=</mo><mo>&#160;</mo><mo>&#160;</mo><mn>5050</mn><mo>&#160;</mo><mo>&#160;</mo><mi>Statement</mi><mo>&#160;</mo><mn>1</mn><mo>&#160;</mo><mi>is</mi><mo>&#160;</mo><mi>correct</mi><mo>&#160;</mo><mo>.</mo></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#160;</mo><mi>Statement</mi><mo>&#160;</mo><mn>2</mn><mo>.</mo></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>8</mn><mi mathvariant=\"normal\">x</mi><mo>+</mo><mfrac><mn>8</mn><mi mathvariant=\"normal\">x</mi></mfrac><mo>=</mo><mo>-</mo><mn>16</mn><mo>&#160;</mo></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo><mo>&#160;</mo><mi mathvariant=\"normal\">x</mi><mo>&#160;</mo><mo>+</mo><mfrac><mn>1</mn><mi mathvariant=\"normal\">x</mi></mfrac><mo>=</mo><mo>-</mo><mn>2</mn><mo>&#160;</mo></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo><mo>&#160;</mo><mo>&#160;</mo><mi mathvariant=\"normal\">x</mi><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mo>-</mo><mn>1</mn></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>Now</mi><mo>&#160;</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">x</mi><mn>197</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">x</mi><mrow><mo>-</mo><mn>197</mn></mrow></msup><mo>&#160;</mo><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mo>-</mo><mn>2</mn></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#160;</mo><mi>Statement</mi><mo>&#160;</mo><mn>2</mn><mo>&#160;</mo><mi>is</mi><mo>&#160;</mo><mi>incorrect</mi><mo>&#160;</mo><mo>.</mo></math></p>\n<p><span style=\"font-family: Cambria Math;\"> </span></p>",
                    solution_hi: "<p>4.(a) <math style=\"font-family: Verdana;\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#2325;&#2341;&#2344;</mi><mo>&#160;</mo><mn>1</mn><mo>.</mo></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#160;</mo><mi mathvariant=\"normal\">n</mi><mo>&#160;</mo><mi>&#2360;&#2350;</mi><mo>&#160;</mo><mi>&#2346;&#2381;&#2352;&#2366;&#2325;&#2371;&#2340;&#2367;&#2325;</mi><mo>&#160;</mo><mi>&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</mi><mo>&#160;</mo><mi>&#2325;&#2375;</mi><mo>&#160;</mo><mi>&#2357;&#2352;&#2381;&#2327;&#2379;&#2306;</mi><mo>&#160;</mo><mi>&#2325;&#2366;</mi><mo>&#160;</mo><mi>&#2351;&#2379;&#2327;</mi><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mfrac><mn>2</mn><mn>3</mn></mfrac><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mo>(</mo><mo>&#160;</mo><mi mathvariant=\"normal\">n</mi><mo>&#160;</mo><mo>)</mo><mo>&#160;</mo><mo>(</mo><mo>&#160;</mo><mi mathvariant=\"normal\">n</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>1</mn><mo>&#160;</mo><mo>)</mo><mo>&#160;</mo><mo>(</mo><mo>&#160;</mo><mn>2</mn><mi mathvariant=\"normal\">n</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>1</mn><mo>&#160;</mo><mo>)</mo></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">n</mi><mo>&#160;</mo><mi>&#2357;&#2367;&#2359;&#2350;</mi><mo>&#160;</mo><mi>&#2346;&#2381;&#2352;&#2366;&#2325;&#2371;&#2340;&#2367;&#2325;</mi><mo>&#160;</mo><mi>&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</mi><mo>&#160;</mo><mi>&#2325;&#2375;</mi><mo>&#160;</mo><mi>&#2357;&#2352;&#2381;&#2327;&#2379;&#2306;</mi><mo>&#160;</mo><mi>&#2325;&#2366;</mi><mo>&#160;</mo><mi>&#2351;&#2379;&#2327;</mi><mo>&#160;</mo><mo>=</mo><mfrac><mn>1</mn><mn>3</mn></mfrac><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mi mathvariant=\"normal\">n</mi><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mo>(</mo><mo>&#160;</mo><mn>2</mn><mi mathvariant=\"normal\">n</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>1</mn><mo>&#160;</mo><mo>)</mo><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mo>(</mo><mo>&#160;</mo><mn>2</mn><mi mathvariant=\"normal\">n</mi><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mn>1</mn><mo>&#160;</mo><mo>)</mo><mo>&#160;</mo><mo>&#160;</mo></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>=</mo><mo>&#160;</mo><mo>(</mo><mo>&#160;</mo><msup><mn>100</mn><mn>2</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mn>98</mn><mn>2</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mn>96</mn><mn>2</mn></msup><mo>&#160;</mo><mo>.</mo><mo>.</mo><mo>.</mo><mo>.</mo><mo>.</mo><mo>.</mo><mo>.</mo><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mn>2</mn><mn>2</mn></msup><mo>&#160;</mo><mo>)</mo><mo>&#160;</mo><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mo>&#160;</mo><mo>(</mo><mo>&#160;</mo><msup><mn>99</mn><mn>2</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mn>97</mn><mn>2</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mn>95</mn><mrow><mn>2</mn><mo>&#160;</mo><mo>.</mo><mo>.</mo><mo>.</mo><mo>.</mo><mo>.</mo><mo>.</mo><mo>.</mo></mrow></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mn>1</mn><mn>2</mn></msup><mo>&#160;</mo><mo>)</mo></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>=</mo><mo>&#160;</mo><mo>&#160;</mo><mfrac><mn>2</mn><mn>3</mn></mfrac><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mo>(</mo><mo>&#160;</mo><mn>50</mn><mo>&#160;</mo><mo>)</mo><mo>&#160;</mo><mo>(</mo><mo>&#160;</mo><mn>50</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>1</mn><mo>&#160;</mo><mo>)</mo><mo>&#160;</mo><mo>(</mo><mo>&#160;</mo><mn>2</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>50</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>1</mn><mo>&#160;</mo><mo>)</mo><mo>&#160;</mo><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mo>&#160;</mo><mfrac><mn>1</mn><mn>3</mn></mfrac><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>50</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mo>(</mo><mo>&#160;</mo><mn>2</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>50</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>1</mn><mo>&#160;</mo><mo>)</mo><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mo>(</mo><mo>&#160;</mo><mn>2</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>50</mn><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mn>1</mn><mo>&#160;</mo><mo>)</mo><mo>&#160;</mo><mo>&#160;</mo></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>=</mo><mo>&#160;</mo><mfrac><mn>1</mn><mn>3</mn></mfrac><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>50</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>101</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mo>(</mo><mo>&#160;</mo><mn>102</mn><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mn>99</mn><mo>&#160;</mo><mo>)</mo><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>5050</mn><mo>&#160;</mo></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#160;</mo><mo>&#160;</mo><mi>&#2325;&#2341;&#2344;</mi><mo>&#160;</mo><mn>2</mn><mo>&#160;</mo><mo>.</mo></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>8</mn><mi mathvariant=\"normal\">x</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mfrac><mn>8</mn><mi mathvariant=\"normal\">x</mi></mfrac><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mo>-</mo><mn>16</mn><mo>&#160;</mo><mo>&#160;</mo></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo><mo>&#160;</mo><mo>&#160;</mo><mi mathvariant=\"normal\">x</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mfrac><mn>1</mn><mi mathvariant=\"normal\">x</mi></mfrac><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mo>&#160;</mo><mo>-</mo><mn>2</mn><mo>&#160;</mo></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo><mo>&#160;</mo><mo>&#160;</mo><mi mathvariant=\"normal\">x</mi><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mo>-</mo><mn>1</mn></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#2309;&#2348;</mi><mo>,</mo><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">x</mi><mn>197</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">x</mi><mrow><mo>-</mo><mn>197</mn></mrow></msup><mo>&#160;</mo><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mo>-</mo><mn>2</mn></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>=</mo><mi>&#2325;&#2341;&#2344;</mi><mo>&#160;</mo><mo>&#160;</mo><mn>2</mn><mo>&#160;</mo><mo>&#160;</mo><mi>&#2327;&#2354;&#2340;</mi><mo>&#160;</mo><mo>&#160;</mo><mi>&#2361;&#2376;&#2404;</mi></math></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">5. </span><span style=\"font-family: Cambria Math;\">If ( 4a + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mi>a</mi></mfrac></math></span><span style=\"font-family: Cambria Math;\"> + 5) = 14, what is the value of ( 16a&sup2; +<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>25</mn><msup><mi>a</mi><mn>2</mn></msup></mfrac></math></span><span style=\"font-family: Cambria Math;\">)? </span></p>",
                    question_hi: "<p>5.<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">यदि</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">4a +&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mi>a</mi></mfrac></math> </span><span style=\"font-family: Cambria Math;\">+&nbsp; 5 ) = 14</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">है</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">तो</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">( 16a&sup2; +&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>25</mn><msup><mi>a</mi><mn>2</mn></msup></mfrac></math> </span><span style=\"font-family: Cambria Math;\">) </span><span style=\"font-family: Cambria Math;\">का</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">मान</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">क्या</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">होगा</span><span style=\"font-family: Cambria Math;\">?</span></p>",
                    options_en: ["<p>25</p>", "<p>36</p>", 
                                "<p>41</p>", "<p>40</p>"],
                    options_hi: ["<p>25</p>", "<p>36</p>",
                                "<p>41</p>", "<p>40</p>"],
                    solution_en: "<p>5.(c) <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>Identities</mi><mo>&#160;</mo><mo>:</mo><mo>-</mo><mo>&#160;</mo><mi>when</mi><mo>&#160;</mo><mi>ax</mi><mo>+</mo><mfrac><mi mathvariant=\"normal\">y</mi><mi mathvariant=\"normal\">a</mi></mfrac><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mi mathvariant=\"normal\">k</mi><mo>&#160;</mo><mo>,</mo><mo>&#160;</mo></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>then</mi><msup><mrow><mo>&#160;</mo><mo>(</mo><mi>ax</mi><mo>)</mo></mrow><mn>2</mn></msup><mo>+</mo><mfrac><msup><mi mathvariant=\"normal\">y</mi><mn>2</mn></msup><msup><mi mathvariant=\"normal\">a</mi><mn>2</mn></msup></mfrac><mo>=</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">k</mi><mn>2</mn></msup><mo>-</mo><mn>2</mn><mi>xy</mi><mo>&#160;</mo><mo>&#160;</mo></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>Now</mi><mo>,</mo><mo>&#160;</mo><mn>4</mn><mi mathvariant=\"normal\">a</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mfrac><mn>5</mn><mi mathvariant=\"normal\">a</mi></mfrac><mo>&#160;</mo><mo>+</mo><mn>5</mn><mo>=</mo><mn>14</mn><mo>&#160;</mo></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo><mo>&#160;</mo><mn>4</mn><mi mathvariant=\"normal\">a</mi><mo>&#160;</mo><mo>+</mo><mfrac><mn>5</mn><mi mathvariant=\"normal\">a</mi></mfrac><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>9</mn><mo>&#160;</mo><mo>&#8230;</mo><mo>&#8230;</mo><mi>eq</mi><mo>&#160;</mo><mo>(</mo><mn>1</mn><mo>)</mo></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>On</mi><mo>&#160;</mo><mi>squaring</mi><mo>&#160;</mo><mi>eq</mi><mo>&#160;</mo><mo>(</mo><mo>&#160;</mo><mn>1</mn><mo>&#160;</mo><mo>)</mo><mo>&#160;</mo><mo>,</mo><mo>&#160;</mo><mi>we</mi><mo>&#160;</mo><mi>get</mi><mo>&#160;</mo><mo>&#160;</mo></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo><mo>&#160;</mo><mn>16</mn><msup><mi mathvariant=\"normal\">a</mi><mn>2</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mfrac><mn>25</mn><msup><mi mathvariant=\"normal\">a</mi><mn>2</mn></msup></mfrac><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><msup><mn>9</mn><mn>2</mn></msup><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mn>2</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>4</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>5</mn><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>41</mn></math></p>\n<p><span style=\"font-family: Cambria Math;\"> </span></p>",
                    solution_hi: "<p>5.(c) <math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#2360;&#2370;&#2340;&#2381;&#2352;</mi><mo>&#160;</mo><mo>:</mo><mo>-</mo><mo>&#160;</mo><mi>&#2332;&#2348;</mi><mo>&#160;</mo><mo>&#160;</mo><mi>ax</mi><mo>&#160;</mo><mo>+</mo><mfrac><mi mathvariant=\"normal\">y</mi><mi mathvariant=\"normal\">a</mi></mfrac><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mi mathvariant=\"normal\">k</mi><mo>&#160;</mo><mo>,</mo></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#2340;&#2348;</mi><mo>&#160;</mo><mo>&#160;</mo><msup><mrow><mo>(</mo><mo>&#160;</mo><mi>ax</mi><mo>&#160;</mo><mo>)</mo></mrow><mn>2</mn></msup><mo>&#160;</mo><mo>+</mo><mfrac><msup><mi mathvariant=\"normal\">y</mi><mn>2</mn></msup><msup><mi mathvariant=\"normal\">a</mi><mn>2</mn></msup></mfrac><mo>=</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">k</mi><mn>2</mn></msup><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mn>2</mn><mi>xy</mi><mo>&#160;</mo><mo>&#160;</mo></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#2309;&#2348;</mi><mo>&#160;</mo><mo>,</mo><mo>&#160;</mo><mn>4</mn><mi mathvariant=\"normal\">a</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mfrac><mn>5</mn><mi mathvariant=\"normal\">a</mi></mfrac><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>5</mn><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>14</mn><mo>&#160;</mo></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo><mo>&#160;</mo><mn>4</mn><mi mathvariant=\"normal\">a</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mfrac><mn>5</mn><mi mathvariant=\"normal\">a</mi></mfrac><mo>&#160;</mo><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mo>&#160;</mo><mn>9</mn><mo>&#160;</mo><mo>&#8230;</mo><mo>&#8230;</mo><mi>&#2360;&#2350;&#2368;&#2325;&#2352;&#2339;</mi><mo>&#160;</mo><mo>&#160;</mo><mo>(</mo><mo>&#160;</mo><mn>1</mn><mo>&#160;</mo><mo>)</mo><mo>&#160;</mo><mo>&#160;</mo></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#2360;&#2350;&#2368;&#2325;&#2352;&#2339;</mi><mo>&#160;</mo><mo>&#160;</mo><mo>(</mo><mo>&#160;</mo><mn>1</mn><mo>&#160;</mo><mo>)</mo><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo><mi>&#2325;&#2366;</mi><mo>&#160;</mo><mi>&#2357;&#2352;&#2381;&#2327;</mi><mo>&#160;</mo><mi>&#2325;&#2352;&#2344;&#2375;</mi><mo>&#160;</mo><mi>&#2346;&#2352;</mi><mo>,</mo><mo>&#160;</mo><mi>&#2361;&#2350;</mi><mo>&#160;</mo><mi>&#2346;&#2366;&#2340;&#2375;</mi><mo>&#160;</mo><mi>&#2361;&#2376;&#2306;</mi><mo>&#160;</mo><mo>&#160;</mo></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo><mo>&#160;</mo><mn>16</mn><msup><mi mathvariant=\"normal\">a</mi><mn>2</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mfrac><mn>25</mn><msup><mi mathvariant=\"normal\">a</mi><mn>2</mn></msup></mfrac><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><msup><mn>9</mn><mn>2</mn></msup><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mn>2</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>4</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>5</mn><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>41</mn></math></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "17",
                    question_en: "<p>6.<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">If </span><span style=\"font-family: Cambria Math;\">&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#160;</mo><mi mathvariant=\"normal\">x</mi><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mfrac><mn>1</mn><mi mathvariant=\"normal\">x</mi></mfrac></math></span><span style=\"font-family: Cambria Math;\"> &nbsp;</span><span style=\"font-family: Cambria Math;\">=&nbsp; 13 then, what will be the value of&nbsp; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi mathvariant=\"normal\">x</mi><mn>4</mn></msup><mo>+</mo><mfrac><mn>1</mn><msup><mi mathvariant=\"normal\">x</mi><mn>4</mn></msup></mfrac></math></span></p>",
                    question_hi: "<p>6. <math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#2351;&#2342;&#2367;</mi><mo>&#160;</mo><mo>&#160;</mo><mi mathvariant=\"normal\">x</mi><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mfrac><mn>1</mn><mi mathvariant=\"normal\">x</mi></mfrac><mo>&#160;</mo><mo>=</mo><mn>13</mn><mo>&#160;</mo><mi>&#2361;&#2376;&#2306;</mi><mo>&#160;</mo><mo>,</mo><mo>&#160;</mo><mi>&#2340;&#2379;</mi><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">x</mi><mn>4</mn></msup><mo>+</mo><mfrac><mn>1</mn><msup><mi mathvariant=\"normal\">x</mi><mn>4</mn></msup></mfrac><mo>&#160;</mo><mi>&#2325;&#2366;</mi><mo>&#160;</mo><mi>&#2350;&#2366;&#2344;</mi><mo>&#160;</mo><mi>&#2325;&#2381;&#2351;&#2366;</mi><mo>&#160;</mo><mi>&#2361;&#2379;&#2327;&#2366;</mi><mo>?</mo></math></p>",
                    options_en: ["<p>28561</p>", "<p>29243</p>", 
                                "<p>27887</p>", "<p>29239</p>"],
                    options_hi: ["<p>28561</p>", "<p>29243</p>",
                                "<p>27887</p>", "<p>29239</p>"],
                    solution_en: "<p>6.(d) <span style=\"font-family: Cambria Math;\">Identity :&nbsp; when&nbsp; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>x</mi><mo>&#160;</mo><mo>-</mo><mfrac><mn>1</mn><mi>x</mi></mfrac><mo>=</mo><mi>k</mi></math> </span><span style=\"font-family: Cambria Math;\">,&nbsp;</span><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>then</mi><mo>,</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup><mo>+</mo><mfrac><mn>1</mn><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup></mfrac><mo>=</mo><msup><mi mathvariant=\"normal\">k</mi><mn>2</mn></msup><mo>+</mo><mn>2</mn></math><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo><mo>&#160;</mo><mi>x</mi><mo>-</mo><mfrac><mn>1</mn><mi>x</mi></mfrac><mo>=</mo><mn>13</mn><mo>,</mo><mo>&#160;</mo></math></span><br><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo><msup><mi>x</mi><mrow><mn>2</mn><mo>&#160;</mo></mrow></msup><mo>+</mo><mfrac><mn>1</mn><msup><mi>x</mi><mn>2</mn></msup></mfrac><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><msup><mn>13</mn><mn>2</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>2</mn><mo>&#160;</mo><mo>=</mo><mn>171</mn></math></span><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>And</mi><mo>&#160;</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">x</mi><mn>4</mn></msup><mo>&#160;</mo><mo>+</mo><mfrac><mn>1</mn><msup><mi mathvariant=\"normal\">x</mi><mn>4</mn></msup></mfrac><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><msup><mrow><mo>(</mo><mn>171</mn><mo>)</mo></mrow><mn>2</mn></msup><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mn>2</mn><mo>&#160;</mo></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo><mo>&#160;</mo><mo>&#160;</mo><mn>29241</mn><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mn>2</mn><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>29239</mn></math></p>",
                    solution_hi: "<p>6.(d) <span style=\"font-family: Cambria Math;\">जब</span><span style=\"font-family: Cambria Math;\"> &nbsp;</span><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>x</mi><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mfrac><mn>1</mn><mi>x</mi></mfrac><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mi>k</mi></math> ,&nbsp;</span><br><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#2340;&#2348;</mi><mo>&#160;</mo><mo>,</mo><mo>&#160;</mo><msup><mi>x</mi><mn>2</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mfrac><mn>1</mn><msup><mi>x</mi><mn>2</mn></msup></mfrac><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><msup><mi>k</mi><mn>2</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>2</mn></math>&nbsp;</span><br><span style=\"font-family: Cambria Math;\"><math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo><mo>&#160;</mo><mi>x</mi><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mfrac><mn>1</mn><mi>x</mi></mfrac><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>13</mn><mo>&#160;</mo><mo>,</mo><mo>&#160;</mo></math></span><br><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo><mo>&#160;</mo><msup><mi>x</mi><mrow><mn>2</mn><mo>&#160;</mo></mrow></msup><mo>+</mo><mfrac><mn>1</mn><msup><mi>x</mi><mn>2</mn></msup></mfrac><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><msup><mn>13</mn><mn>2</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>2</mn><mo>&#160;</mo><mo>=</mo><mn>171</mn></math></span><br><span style=\"font-family: Cambria Math;\"><math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#2324;&#2352;</mi><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">x</mi><mn>4</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mfrac><mn>1</mn><msup><mi mathvariant=\"normal\">x</mi><mn>4</mn></msup></mfrac><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><msup><mrow><mo>(</mo><mn>171</mn><mo>)</mo></mrow><mn>2</mn></msup><mo>-</mo><mn>2</mn><mo>&#160;</mo><mo>&#160;</mo></math></span><br><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo><mo>&#160;</mo><mo>&#160;</mo><mn>29241</mn><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mn>2</mn><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>29239</mn></math></span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">7. </span><span style=\"font-family: Cambria Math;\">The factors of&nbsp; x</span><sup><span style=\"font-family: Cambria Math;\">4</span></sup><span style=\"font-family: Cambria Math;\"> + x</span><sup><span style=\"font-family: Cambria Math;\">2</span></sup><span style=\"font-family: Cambria Math;\"> + 25 are:</span></p>",
                    question_hi: "<p>7.<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">x</span><sup><span style=\"font-family: Cambria Math;\">4</span></sup><span style=\"font-family: Cambria Math;\"> +&nbsp; x</span><sup><span style=\"font-family: Cambria Math;\">2</span></sup><span style=\"font-family: Cambria Math;\"> + 25 </span><span style=\"font-family: Cambria Math;\">के</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">गुणनखंड</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">इनमें</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">से</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">कौन</span><span style=\"font-family: Cambria Math;\">-</span><span style=\"font-family: Cambria Math;\">से</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">हैं</span><span style=\"font-family: Cambria Math;\">?</span></p>",
                    options_en: ["<p><span style=\"font-weight: 400;\">(x</span><sup><span style=\"font-weight: 400;\">2 </span></sup><span style=\"font-weight: 400;\">+ 3x - 5)(x</span><sup><span style=\"font-weight: 400;\">2</span></sup><span style=\"font-weight: 400;\"> - 3x + 5)</span></p>", "<p><span style=\"font-weight: 400;\">&nbsp;(x</span><sup><span style=\"font-weight: 400;\">2 </span></sup><span style=\"font-weight: 400;\">+ 3x + 5)(x</span><sup><span style=\"font-weight: 400;\">2</span></sup><span style=\"font-weight: 400;\"> - 3x + 5)</span></p>", 
                                "<p><span style=\"font-weight: 400;\">(x</span><sup><span style=\"font-weight: 400;\">2 </span></sup><span style=\"font-weight: 400;\">- 3x + 5)(x</span><sup><span style=\"font-weight: 400;\">2</span></sup><span style=\"font-weight: 400;\">-3x + 5)</span></p>", "<p><span style=\"font-weight: 400;\">(x</span><sup><span style=\"font-weight: 400;\">2 </span></sup><span style=\"font-weight: 400;\">+ 3x + 5)(x</span><sup><span style=\"font-weight: 400;\">2</span></sup><span style=\"font-weight: 400;\"> + 3x + 5)</span></p>"],
                    options_hi: ["<p><span style=\"font-weight: 400;\">(x</span><sup><span style=\"font-weight: 400;\">2 </span></sup><span style=\"font-weight: 400;\">+ 3x - 5)(x</span><sup><span style=\"font-weight: 400;\">2</span></sup><span style=\"font-weight: 400;\"> - 3x + 5)</span></p>", "<p><span style=\"font-weight: 400;\">&nbsp;(x</span><sup><span style=\"font-weight: 400;\">2 </span></sup><span style=\"font-weight: 400;\">+ 3x + 5)(x</span><sup><span style=\"font-weight: 400;\">2</span></sup><span style=\"font-weight: 400;\"> - 3x + 5)</span></p>",
                                "<p><span style=\"font-weight: 400;\">&nbsp;(x</span><sup><span style=\"font-weight: 400;\">2 </span></sup><span style=\"font-weight: 400;\">- 3x + 5)(x</span><sup><span style=\"font-weight: 400;\">2</span></sup><span style=\"font-weight: 400;\"> - 3x + 5)</span></p>", "<p><span style=\"font-weight: 400;\">(x</span><sup><span style=\"font-weight: 400;\">2 </span></sup><span style=\"font-weight: 400;\">+ 3x + 5)(x</span><sup><span style=\"font-weight: 400;\">2</span></sup><span style=\"font-weight: 400;\"> + 3x + 5)</span></p>"],
                    solution_en: "<p>7.(b) (x<sup>4 </sup>+ x<sup>2 </sup>+ 25)&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>=</mo><mo>&#160;</mo><mfenced><mrow><msup><mi>x</mi><mn>4</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mi>x</mi><mn>2</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mn>5</mn><mn>2</mn></msup></mrow></mfenced></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo><mo>&#160;</mo><mo>[</mo><mo>&#160;</mo><mo>{</mo><mo>&#160;</mo><msup><mfenced><mrow><msup><mi>x</mi><mn>2</mn></msup><mo>+</mo><mn>5</mn></mrow></mfenced><mn>2</mn></msup><mo>-</mo><mn>2</mn><mo>&#215;</mo><mn>5</mn><mo>&#215;</mo><msup><mi>x</mi><mn>2</mn></msup><mo>}</mo><mo>+</mo><msup><mi>x</mi><mn>2</mn></msup><mo>]</mo></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo><mo>[</mo><mo>&#160;</mo><msup><mfenced><mrow><msup><mi>x</mi><mn>2</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>5</mn></mrow></mfenced><mn>2</mn></msup><mo>-</mo><mn>10</mn><msup><mi>x</mi><mn>2</mn></msup><mo>+</mo><msup><mi>x</mi><mn>2</mn></msup><mo>]</mo></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo><mo>[</mo><msup><mfenced><mrow><msup><mi>x</mi><mn>2</mn></msup><mo>+</mo><mn>5</mn></mrow></mfenced><mn>2</mn></msup><mo>-</mo><mn>9</mn><mo>&#160;</mo><msup><mi>x</mi><mn>2</mn></msup><mo>]</mo><mo>&#160;</mo></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo><mo>[</mo><msup><mfenced><mrow><msup><mi>x</mi><mn>2</mn></msup><mo>+</mo><mn>5</mn></mrow></mfenced><mn>2</mn></msup><mo>-</mo><msup><mrow><mo>(</mo><mn>3</mn><mo>&#160;</mo><mi>x</mi><mo>)</mo></mrow><mn>2</mn></msup><mo>]</mo></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo><mo>(</mo><msup><mi>x</mi><mn>2</mn></msup><mo>+</mo><mn>3</mn><mi>x</mi><mo>+</mo><mn>5</mn><mo>)</mo><mo>(</mo><msup><mi>x</mi><mn>2</mn></msup><mo>-</mo><mn>3</mn><mi>x</mi><mo>+</mo><mn>5</mn><mo>)</mo></math></p>\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\n<p><span style=\"font-family: Cambria Math;\"> </span></p>",
                    solution_hi: "<p>7.(b) (x<sup>4 </sup>+ x<sup>2 </sup>+ 25) <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>=</mo><mo>&#160;</mo><mfenced><mrow><msup><mi mathvariant=\"normal\">x</mi><mn>4</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mn>5</mn><mn>2</mn></msup></mrow></mfenced></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo><mo>&#160;</mo><mo>[</mo><mo>{</mo><msup><mfenced><mrow><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>5</mn></mrow></mfenced><mn>2</mn></msup><mo>-</mo><mn>2</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>5</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup><mo>&#160;</mo><mo>}</mo><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup><mo>]</mo></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo><mo>&#160;</mo><mo>[</mo><mo>&#160;</mo><msup><mfenced><mrow><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup><mo>+</mo><mn>5</mn></mrow></mfenced><mn>2</mn></msup><mo>-</mo><mn>10</mn><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup><mo>+</mo><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup><mo>]</mo></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo><mo>[</mo><msup><mfenced><mrow><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup><mo>+</mo><mn>5</mn></mrow></mfenced><mn>2</mn></msup><mo>-</mo><mn>9</mn><mo>&#160;</mo><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup><mo>]</mo><mo>&#160;</mo></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo><mo>[</mo><msup><mfenced><mrow><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup><mo>+</mo><mn>5</mn></mrow></mfenced><mn>2</mn></msup><mo>-</mo><mo>&#160;</mo><msup><mrow><mo>(</mo><mn>3</mn><mo>&#160;</mo><mi mathvariant=\"normal\">x</mi><mo>)</mo></mrow><mn>2</mn></msup><mo>]</mo></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo><mo>(</mo><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup><mo>+</mo><mn>3</mn><mi mathvariant=\"normal\">x</mi><mo>+</mo><mn>5</mn><mo>)</mo><mo>(</mo><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup><mo>-</mo><mn>3</mn><mi mathvariant=\"normal\">x</mi><mo>+</mo><mn>5</mn><mo>)</mo></math></p>\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\n<p><span style=\"font-family: Cambria Math;\"> </span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">8.&nbsp;</span><span style=\"font-family: Cambria Math;\"> </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>If</mi><mo>&#160;</mo><mo>&#160;</mo><mi mathvariant=\"normal\">x</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mfrac><mn>1</mn><mi mathvariant=\"normal\">x</mi></mfrac><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>2</mn><mo>&#160;</mo><mi>cos&#952;</mi><mo>&#160;</mo><mo>,</mo><mo>&#160;</mo><mo>&#160;</mo><mi>then</mi><mo>&#160;</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">x</mi><mn>3</mn></msup><mo>+</mo><mfrac><mn>1</mn><msup><mi mathvariant=\"normal\">x</mi><mn>3</mn></msup></mfrac><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mo>?</mo></math><span style=\"font-family: Cambria Math;\"> </span></p>",
                    question_hi: "<p>8.&nbsp; <math style=\"font-family: Verdana;\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#2351;&#2342;&#2367;</mi><mo>&#160;</mo><mo>&#160;</mo><mi mathvariant=\"normal\">x</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mfrac><mn>1</mn><mi mathvariant=\"normal\">x</mi></mfrac><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>2</mn><mo>&#160;</mo><mi>cos&#952;</mi><mo>&#160;</mo><mo>&#160;</mo><mi>&#2361;&#2376;</mi><mo>,</mo><mo>&#160;</mo><mi>&#2340;&#2379;</mi><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">x</mi><mn>3</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mfrac><mn>1</mn><msup><mi mathvariant=\"normal\">x</mi><mn>3</mn></msup></mfrac><mo>&#160;</mo><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mo>?</mo></math></p>",
                    options_en: ["<p><span style=\"font-weight: 400;\">2 cos 2</span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">&#952;</mi></math></p>", "<p>cos3<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">&#952;</mi></math></p>", 
                                "<p>2cos 3<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">&#952;</mi></math></p>", "<p>cos 2<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">&#952;</mi></math></p>"],
                    options_hi: ["<p><span style=\"font-weight: 400;\">2 cos 2<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">&#952;</mi></math></span></p>", "<p>cos 3<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">&#952;</mi></math></p>",
                                "<p>2cos 3<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">&#952;</mi></math></p>", "<p>cos 2<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">&#952;</mi></math></p>"],
                    solution_en: "<p>8.(c) <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>cos</mi><mn>3</mn><mi mathvariant=\"normal\">&#952;</mi><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mo>(</mo><mn>4</mn><msup><mi>cos</mi><mn>3</mn></msup><mi mathvariant=\"normal\">&#952;</mi><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mn>3</mn><mi>cos&#952;</mi><mo>)</mo></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">x</mi><mo>&#160;</mo><mo>+</mo><mfrac><mn>1</mn><mi mathvariant=\"normal\">x</mi></mfrac><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>2</mn><mi>cos&#952;</mi><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo><mo>&#8230;</mo><mo>.</mo><mo>.</mo><mo>(</mo><mi>given</mi><mo>)</mo></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>Then</mi><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">x</mi><mn>3</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mfrac><mn>1</mn><msup><mi mathvariant=\"normal\">x</mi><mn>3</mn></msup></mfrac><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><msup><mrow><mo>(</mo><mn>2</mn><mo>&#160;</mo><mi>cos&#952;</mi><mo>&#160;</mo><mo>)</mo></mrow><mn>3</mn></msup><mo>&#160;</mo><mo>-</mo><mn>3</mn><mo>&#215;</mo><mn>2</mn><mo>&#160;</mo><mi>cos&#952;</mi><mo>&#160;</mo><mo>&#160;</mo></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>=</mo><mo>&#160;</mo><mn>2</mn><mo>&#160;</mo><mo>(</mo><mo>&#160;</mo><mn>4</mn><msup><mi>cos</mi><mn>3</mn></msup><mi mathvariant=\"normal\">&#952;</mi><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mn>3</mn><mi>cos&#952;</mi><mo>&#160;</mo><mo>)</mo><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>2</mn><mo>&#160;</mo><mi>cos</mi><mn>3</mn><mi mathvariant=\"normal\">&#952;</mi></math></p>",
                    solution_hi: "<p>8.(c)&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>cos</mi><mn>3</mn><mi mathvariant=\"normal\">&#952;</mi><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mo>(</mo><mn>4</mn><msup><mi>cos</mi><mn>3</mn></msup><mi mathvariant=\"normal\">&#952;</mi><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mn>3</mn><mi>cos&#952;</mi><mo>)</mo></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">x</mi><mo>&#160;</mo><mo>+</mo><mfrac><mn>1</mn><mi mathvariant=\"normal\">x</mi></mfrac><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>2</mn><mi>cos&#952;</mi><mo>&#160;</mo><mo>&#160;</mo><mo>&#8230;</mo><mo>.</mo><mo>(</mo><mo>&#160;</mo><mi>&#2342;&#2367;&#2351;&#2366;</mi><mo>&#160;</mo><mo>&#160;</mo><mi>&#2327;&#2351;&#2366;</mi><mo>&#160;</mo><mo>&#160;</mo><mi>&#2361;&#2376;</mi><mo>&#160;</mo><mo>)</mo></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#2340;&#2348;</mi><mo>&#160;</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">x</mi><mn>3</mn></msup><mo>+</mo><mo>&#160;</mo><mfrac><mn>1</mn><msup><mi mathvariant=\"normal\">x</mi><mn>3</mn></msup></mfrac><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><msup><mrow><mo>(</mo><mo>&#160;</mo><mn>2</mn><mo>&#160;</mo><mi>cos&#952;</mi><mo>&#160;</mo><mo>)</mo></mrow><mn>3</mn></msup><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mn>3</mn><mo>&#215;</mo><mn>2</mn><mo>&#160;</mo><mi>cos&#952;</mi></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>=</mo><mo>&#160;</mo><mn>2</mn><mo>&#160;</mo><mo>(</mo><mo>&#160;</mo><mn>4</mn><msup><mi>cos</mi><mn>3</mn></msup><mi mathvariant=\"normal\">&#952;</mi><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mn>3</mn><mi>cos&#952;</mi><mo>&#160;</mo><mo>)</mo><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>2</mn><mo>&#160;</mo><mi>cos</mi><mn>3</mn><mi mathvariant=\"normal\">&#952;</mi></math></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">9. <strong>&nbsp;</strong><span style=\"font-weight: 400;\">If x = 3 + 2</span><span style=\"font-weight: 400;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>2</mn></msqrt></math> </span><span style=\"font-weight: 400;\">, x &gt; 0, then the value of&nbsp; </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mi>x</mi></msqrt><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mfrac><mn>1</mn><msqrt><mi>x</mi></msqrt></mfrac></math><span style=\"font-weight: 400;\">&nbsp;</span><span style=\"font-weight: 400;\"> is:</span></span></p>",
                    question_hi: "<p>9.&nbsp;<span style=\"font-weight: 400;\">यदि x = 3 + 2</span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>2</mn></msqrt></math> <span style=\"font-weight: 400;\">, x &gt; 0 है, तो </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mi>x</mi></msqrt><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mfrac><mn>1</mn><msqrt><mi>x</mi></msqrt></mfrac></math><strong>&nbsp; </strong><span style=\"font-weight: 400;\">का मान क्या है?</span></p>",
                    options_en: ["<p>1</p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>2</mn></msqrt></math></p>", 
                                "<p>2</p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>2</mn><msqrt><mn>2</mn></msqrt></math></p>"],
                    options_hi: ["<p>1</p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>2</mn></msqrt></math></p>",
                                "<p>2</p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>2</mn><msqrt><mn>2</mn></msqrt></math></p>"],
                    solution_en: "<p>9.(c) <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">x</mi><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>3</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>2</mn><msqrt><mn>2</mn></msqrt></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo><mo>&#160;</mo><mi mathvariant=\"normal\">x</mi><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><msup><mrow><mo>(</mo><msqrt><mn>2</mn></msqrt><mo>)</mo></mrow><mn>2</mn></msup><mo>+</mo><msup><mrow><mo>(</mo><mn>1</mn><mo>)</mo></mrow><mn>2</mn></msup><mo>+</mo><mn>2</mn><msqrt><mn>2</mn></msqrt><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><msup><mrow><mo>(</mo><msqrt><mn>2</mn></msqrt><mo>+</mo><mn>1</mn><mo>)</mo></mrow><mn>2</mn></msup><mo>&#8658;</mo><msqrt><mi mathvariant=\"normal\">x</mi><mo>&#160;</mo></msqrt><mo>=</mo><mo>(</mo><msqrt><mn>2</mn></msqrt><mo>+</mo><mn>1</mn><mo>)</mo><mo>.</mo><mo>.</mo><mo>.</mo><mo>.</mo><mo>.</mo><mo>.</mo><mo>.</mo><mo>.</mo><mo>.</mo><mo>.</mo><mo>.</mo><mo>.</mo><mo>.</mo><mi>eq</mi><mo>.</mo><mn>1</mn></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>Taking</mi><mo>&#160;</mo><mi>reciprocal</mi><mo>&#160;</mo><mi>of</mi><mo>&#160;</mo><mi>eq</mi><mo>(</mo><mn>1</mn><mo>)</mo><mo>&#160;</mo><mi>we</mi><mo>&#160;</mo><mi>get</mi><mo>&#160;</mo><mo>,</mo></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo><mo>&#160;</mo><mfrac><mn>1</mn><msqrt><mi mathvariant=\"normal\">x</mi></msqrt></mfrac><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mfrac><mn>1</mn><mrow><mo>(</mo><msqrt><mn>2</mn><mo>&#160;</mo></msqrt><mo>+</mo><mn>1</mn><mo>)</mo></mrow></mfrac><mo>&#215;</mo><mfrac><mrow><mo>(</mo><msqrt><mn>2</mn></msqrt><mo>-</mo><mn>1</mn><mo>)</mo></mrow><mrow><mo>(</mo><msqrt><mn>2</mn></msqrt><mo>-</mo><mn>1</mn><mo>)</mo></mrow></mfrac><mo>=</mo><mfrac><mrow><mo>(</mo><msqrt><mn>2</mn></msqrt><mo>-</mo><mn>1</mn><mo>)</mo></mrow><mn>1</mn></mfrac><mo>=</mo><mo>(</mo><msqrt><mn>2</mn></msqrt><mo>-</mo><mn>1</mn><mo>)</mo><mo>.</mo><mo>.</mo><mo>.</mo><mo>.</mo><mi>eq</mi><mo>&#160;</mo><mo>(</mo><mo>&#160;</mo><mn>2</mn><mo>&#160;</mo><mo>)</mo></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>Now</mi><mo>&#160;</mo><mi>from</mi><mo>&#160;</mo><mi>eq</mi><mo>&#160;</mo><mo>(</mo><mo>&#160;</mo><mn>1</mn><mo>&#160;</mo><mo>)</mo><mo>&#160;</mo><mi>and</mi><mo>&#160;</mo><mi>eq</mi><mo>&#160;</mo><mo>(</mo><mo>&#160;</mo><mo>&#160;</mo><mn>2</mn><mo>&#160;</mo><mo>)</mo><mo>&#160;</mo><mi>we</mi><mo>&#160;</mo><mi>get</mi><mo>&#160;</mo><mo>,</mo></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo><msqrt><mi mathvariant=\"normal\">x</mi></msqrt><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mfrac><mn>1</mn><msqrt><mi mathvariant=\"normal\">x</mi></msqrt></mfrac><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mo>(</mo><msqrt><mn>2</mn></msqrt><mo>+</mo><mn>1</mn><mo>)</mo><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mo>(</mo><msqrt><mn>2</mn></msqrt><mo>-</mo><mn>1</mn><mo>)</mo><mo>&#160;</mo><mo>=</mo><mn>2</mn></math></p>",
                    solution_hi: "<p>9.(c)&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">x</mi><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>3</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>2</mn><msqrt><mn>2</mn></msqrt></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo><mo>&#160;</mo><mi mathvariant=\"normal\">x</mi><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><msup><mrow><mo>(</mo><msqrt><mn>2</mn></msqrt><mo>)</mo></mrow><mn>2</mn></msup><mo>+</mo><msup><mrow><mo>(</mo><mn>1</mn><mo>)</mo></mrow><mn>2</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>2</mn><msqrt><mn>2</mn></msqrt><mo>&#160;</mo><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><msup><mrow><mo>(</mo><msqrt><mn>2</mn><mo>&#160;</mo></msqrt><mo>+</mo><mn>1</mn><mo>)</mo></mrow><mn>2</mn></msup><mo>&#8658;</mo><mo>&#160;</mo><msqrt><mi mathvariant=\"normal\">x</mi><mo>&#160;</mo></msqrt><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mo>(</mo><msqrt><mn>2</mn><mo>&#160;</mo></msqrt><mo>+</mo><mo>&#160;</mo><mn>1</mn><mo>)</mo><mo>&#160;</mo><mo>.</mo><mo>.</mo><mo>.</mo><mi>&#2360;&#2350;&#2368;&#2325;&#2352;&#2339;</mi><mo>&#160;</mo><mo>(</mo><mo>&#160;</mo><mn>1</mn><mo>&#160;</mo><mo>)</mo></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#2360;&#2350;&#2368;&#2325;&#2352;&#2339;</mi><mo>&#160;</mo><mo>(</mo><mn>1</mn><mo>)</mo><mo>&#160;</mo><mi>&#2325;&#2366;</mi><mo>&#160;</mo><mi>&#2357;&#2381;&#2351;&#2369;&#2340;&#2381;&#2325;&#2381;&#2352;&#2350;</mi><mo>&#160;</mo><mi>&#2354;&#2375;&#2344;&#2375;</mi><mo>&#160;</mo><mi>&#2346;&#2352;</mi><mo>,</mo><mo>&#160;</mo><mi>&#2361;&#2350;</mi><mo>&#160;</mo><mi>&#2346;&#2381;&#2352;&#2366;&#2346;&#2381;&#2340;</mi><mo>&#160;</mo><mi>&#2325;&#2352;&#2340;&#2375;</mi><mo>&#160;</mo><mi>&#2361;&#2376;&#2306;</mi></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo><mfrac><mn>1</mn><msqrt><mi mathvariant=\"normal\">x</mi></msqrt></mfrac><mo>&#160;</mo><mo>=</mo><mfrac><mn>1</mn><mrow><mo>(</mo><mo>&#160;</mo><msqrt><mn>2</mn><mo>&#160;</mo></msqrt><mo>+</mo><mn>1</mn><mo>&#160;</mo><mo>)</mo></mrow></mfrac><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mfrac><mrow><mo>(</mo><msqrt><mn>2</mn></msqrt><mo>-</mo><mn>1</mn><mo>)</mo></mrow><mrow><mo>(</mo><msqrt><mn>2</mn></msqrt><mo>-</mo><mn>1</mn><mo>)</mo></mrow></mfrac><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mfrac><mrow><mo>(</mo><msqrt><mn>2</mn></msqrt><mo>-</mo><mn>1</mn><mo>)</mo></mrow><mn>1</mn></mfrac><mo>=</mo><mo>(</mo><msqrt><mn>2</mn></msqrt><mo>-</mo><mn>1</mn><mo>)</mo><mo>.</mo><mo>.</mo><mo>.</mo><mo>.</mo><mi>&#2360;&#2350;&#2368;&#2325;&#2352;&#2339;</mi><mo>&#160;</mo><mo>(</mo><mo>&#160;</mo><mn>2</mn><mo>&#160;</mo><mo>)</mo></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#2309;&#2348;</mi><mo>,</mo><mo>&#160;</mo><mi>&#2360;&#2350;&#2368;&#2325;&#2352;&#2339;</mi><mo>&#160;</mo><mo>(</mo><mn>1</mn><mo>)</mo><mo>&#160;</mo><mi>&#2324;&#2352;</mi><mo>&#160;</mo><mi>&#2360;&#2350;&#2368;&#2325;&#2352;&#2339;</mi><mo>&#160;</mo><mo>(</mo><mn>2</mn><mo>)</mo><mo>&#160;</mo><mi>&#2360;&#2375;</mi><mo>&#160;</mo><mi>&#2361;&#2350;</mi><mo>&#160;</mo><mi>&#2346;&#2381;&#2352;&#2366;&#2346;&#2381;&#2340;</mi><mo>&#160;</mo><mi>&#2361;&#2379;&#2340;&#2366;</mi><mo>&#160;</mo><mi>&#2361;&#2376;</mi><mo>,</mo></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo><msqrt><mi mathvariant=\"normal\">x</mi></msqrt><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mfrac><mn>1</mn><msqrt><mi mathvariant=\"normal\">x</mi></msqrt></mfrac><mo>=</mo><mo>&#160;</mo><mo>(</mo><msqrt><mn>2</mn><mo>&#160;</mo></msqrt><mo>+</mo><mn>1</mn><mo>)</mo><mo>&#160;</mo><mo>-</mo><mo>(</mo><msqrt><mn>2</mn></msqrt><mo>-</mo><mn>1</mn><mo>)</mo><mo>&#160;</mo><mo>=</mo><mn>2</mn></math></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">10.</span><span style=\"font-family: Cambria Math;\"> </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>If</mi><mo>&#160;</mo><mo>&#160;</mo><mi mathvariant=\"normal\">x</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mfrac><mn>1</mn><mi mathvariant=\"normal\">x</mi></mfrac><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mo>-</mo><mn>2</mn><mo>,</mo><mo>&#160;</mo><mo>&#160;</mo><mi>then</mi><mo>&#160;</mo><mi>what</mi><mo>&#160;</mo><mi>is</mi><mo>&#160;</mo><mi>the</mi><mo>&#160;</mo><mi>value</mi><mo>&#160;</mo><mi>of</mi><mo>&#160;</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">x</mi><mn>17</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">x</mi><mrow><mo>-</mo><mn>17</mn></mrow></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">x</mi><mn>12</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">x</mi><mrow><mo>-</mo><mn>12</mn></mrow></msup><mo>&#160;</mo><mo>?</mo><mo>&#160;</mo><mo>&#160;</mo><mo>(</mo><mo>&#160;</mo><mi mathvariant=\"normal\">x</mi><mo>&#160;</mo><mo>&#60;</mo><mo>&#160;</mo><mn>0</mn><mo>&#160;</mo><mo>)</mo></math></p>",
                    question_hi: "<p>10. <span style=\"font-family: Nirmala UI;\">यदि</span><span style=\"font-family: Cambria Math;\">&nbsp; x + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>x</mi></mfrac></math> =&nbsp; </span><span style=\"font-family: Cambria Math;\">-2 </span><span style=\"font-family: Nirmala UI;\">है</span><span style=\"font-family: Cambria Math;\">,&nbsp; </span><span style=\"font-family: Nirmala UI;\">तो</span><span style=\"font-family: Cambria Math;\"> &nbsp;</span><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>x</mi><mn>17</mn></msup><mo>+</mo><msup><mi>x</mi><mrow><mo>-</mo><mn>17</mn></mrow></msup><mo>+</mo><msup><mi>x</mi><mn>12</mn></msup><mo>+</mo><msup><mi>x</mi><mrow><mo>-</mo><mn>12</mn></mrow></msup></math> </span><span style=\"font-family: Nirmala UI;\">का</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">मान</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">क्या</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">है</span><span style=\"font-family: Cambria Math;\"> ? ( x&nbsp; &lt;&nbsp; 0 )</span></p>",
                    options_en: ["<p>-2</p>", "<p>-1</p>", 
                                "<p>1</p>", "<p>0</p>"],
                    options_hi: ["<p>-2</p>", "<p>-1</p>",
                                "<p>1</p>", "<p>0</p>"],
                    solution_en: "<p>10.(d) <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>when</mi><mo>&#160;</mo><mo>&#160;</mo><mi mathvariant=\"normal\">x</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mfrac><mn>1</mn><mi mathvariant=\"normal\">x</mi></mfrac><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mo>-</mo><mn>2</mn><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo><mi>then</mi><mo>&#160;</mo><mo>&#160;</mo><mi mathvariant=\"normal\">x</mi><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mo>-</mo><mn>1</mn><mo>&#160;</mo></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>Now</mi><mo>&#160;</mo><mo>,</mo><mo>&#160;</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">x</mi><mn>17</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">x</mi><mrow><mo>-</mo><mn>17</mn></mrow></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">x</mi><mn>12</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">x</mi><mrow><mo>-</mo><mn>12</mn></mrow></msup><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>=</mo><mo>&#160;</mo><mo>-</mo><mn>1</mn><mo>&#160;</mo><mo>-</mo><mn>1</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>1</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>1</mn><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>0</mn><mo>&#160;</mo></math></p>",
                    solution_hi: "<p>10.(d) <span style=\"font-family: Cambria Math;\"><math style=\"font-family: Verdana;\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#2332;&#2348;</mi><mo>&#160;</mo><mo>&#160;</mo><mi mathvariant=\"normal\">x</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mfrac><mn>1</mn><mi mathvariant=\"normal\">x</mi></mfrac><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mo>-</mo><mn>2</mn><mo>&#160;</mo><mo>&#160;</mo><mi>&#2340;&#2348;</mi><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo><mi mathvariant=\"normal\">x</mi><mo>&#160;</mo><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mo>-</mo><mn>1</mn><mo>&#160;</mo></math></span><br><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#2309;&#2348;</mi><mo>&#160;</mo><mo>,</mo><mo>&#160;</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">x</mi><mn>17</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">x</mi><mrow><mo>-</mo><mn>17</mn></mrow></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">x</mi><mn>12</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">x</mi><mrow><mo>-</mo><mn>12</mn></mrow></msup><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo></math></span><br><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>=</mo><mo>&#160;</mo><mo>-</mo><mn>1</mn><mo>&#160;</mo><mo>&#160;</mo><mo>-</mo><mn>1</mn><mo>&#160;</mo><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>1</mn><mo>&#160;</mo><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>1</mn><mo>&#160;</mo><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>0</mn><mo>&#160;</mo></math></span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">11. </span><span style=\"font-family: Cambria Math;\">If x&nbsp; -&nbsp; y&nbsp; =&nbsp; 1 and x<sup>2 </sup></span><span style=\"font-family: Cambria Math;\">+ y<sup>2</sup>&nbsp;</span><span style=\"font-family: Cambria Math;\"> = 41 where x, y &ge; 0, then the value of x&nbsp; +&nbsp; y will be:</span></p>",
                    question_hi: "<p>11. <span style=\"font-family: Nirmala UI;\">यदि</span><span style=\"font-family: Cambria Math;\"> x &ndash; y = 1 </span><span style=\"font-family: Nirmala UI;\">और</span><span style=\"font-family: Cambria Math;\"> x<sup>2 </sup>+ y<sup>2</sup>&nbsp; = 41 </span><span style=\"font-family: Cambria Math;\">&nbsp;</span><span style=\"font-family: Nirmala UI;\">है</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">जहाँ</span><span style=\"font-family: Cambria Math;\"> x, y &ge; 0 </span><span style=\"font-family: Nirmala UI;\">है</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">तो</span><span style=\"font-family: Cambria Math;\"> x + y </span><span style=\"font-family: Nirmala UI;\">का</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">मान</span><span style=\"font-family: Cambria Math;\"> _________ </span><span style=\"font-family: Nirmala UI;\">होगा।</span></p>",
                    options_en: ["<p>9</p>", "<p>8</p>", 
                                "<p>6</p>", "<p>7</p>"],
                    options_hi: ["<p>9</p>", "<p>8</p>",
                                "<p>6</p>", "<p>7</p>"],
                    solution_en: "<p>11.(a) <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>Given</mi><mo>&#160;</mo><mi>that</mi><mo>&#160;</mo><mo>,</mo><mo>&#160;</mo><mo>&#160;</mo><mi mathvariant=\"normal\">x</mi><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mi mathvariant=\"normal\">y</mi><mo>&#160;</mo><mo>=</mo><mn>1</mn><mo>&#160;</mo><mo>&#160;</mo><mi>and</mi><mo>&#160;</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">y</mi><mn>2</mn></msup><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>41</mn><mo>&#160;</mo></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>Now</mi><mo>&#160;</mo><mo>,</mo><msup><mrow><mo>&#160;</mo><mo>(</mo><mo>&#160;</mo><mi mathvariant=\"normal\">x</mi><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mi mathvariant=\"normal\">y</mi><mo>)</mo></mrow><mn>2</mn></msup><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">y</mi><mn>2</mn></msup><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mn>2</mn><mi>xy</mi><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>41</mn><mo>-</mo><mn>2</mn><mi>xy</mi></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo><mo>&#160;</mo><mo>&#160;</mo><mi>xy</mi><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>20</mn><mo>&#160;</mo></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>Now</mi><mo>&#160;</mo><mi>by</mi><mo>&#160;</mo><mi>using</mi><mo>&#160;</mo><mo>&#160;</mo><mi>formula</mi><mo>,</mo><mo>&#160;</mo></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mo>&#160;</mo><mi mathvariant=\"normal\">x</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mi mathvariant=\"normal\">y</mi><mo>&#160;</mo><mo>)</mo></mrow><mn>2</mn></msup><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">y</mi><mn>2</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>2</mn><mi>xy</mi></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo><mo>&#160;</mo><msup><mrow><mo>(</mo><mo>&#160;</mo><mo>&#160;</mo><mi mathvariant=\"normal\">x</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mi mathvariant=\"normal\">y</mi><mo>&#160;</mo><mo>)</mo></mrow><mn>2</mn></msup><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>41</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>2</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>20</mn><mo>&#160;</mo><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mo>&#160;</mo><mn>81</mn></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo><mo>&#160;</mo><mo>&#160;</mo><mi mathvariant=\"normal\">x</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mi mathvariant=\"normal\">y</mi><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>9</mn></math></p>",
                    solution_hi: "<p>11.(a) <math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#2342;&#2367;&#2351;&#2366;</mi><mo>&#160;</mo><mi>&#2327;&#2351;&#2366;</mi><mo>&#160;</mo><mi>&#2361;&#2376;</mi><mo>&#160;</mo><mi>&#2325;&#2367;</mi><mo>&#160;</mo><mo>,</mo><mo>&#160;</mo><mi mathvariant=\"normal\">x</mi><mo>-</mo><mi mathvariant=\"normal\">y</mi><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>1</mn><mo>&#160;</mo><mo>&#160;</mo><mi>&#2324;&#2352;</mi><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup><mo>+</mo><msup><mi mathvariant=\"normal\">y</mi><mn>2</mn></msup><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>41</mn><mo>&#160;</mo></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#2309;&#2348;</mi><mo>&#160;</mo><mo>,</mo><msup><mrow><mo>&#160;</mo><mo>(</mo><mo>&#160;</mo><mi mathvariant=\"normal\">x</mi><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mi mathvariant=\"normal\">y</mi><mo>)</mo></mrow><mn>2</mn></msup><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">y</mi><mn>2</mn></msup><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mn>2</mn><mi>xy</mi><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>41</mn><mo>-</mo><mn>2</mn><mi>xy</mi><mo>&#160;</mo></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo><mo>&#160;</mo><mo>&#160;</mo><mi>xy</mi><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>20</mn><mo>&#160;</mo></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#2309;&#2348;</mi><mo>&#160;</mo><mi>&#2360;&#2370;&#2340;&#2381;&#2352;</mi><mo>&#160;</mo><mi>&#2325;&#2366;</mi><mo>&#160;</mo><mi>&#2346;&#2381;&#2352;&#2351;&#2379;&#2327;</mi><mo>&#160;</mo><mi>&#2325;&#2352;&#2344;&#2375;</mi><mo>&#160;</mo><mi>&#2346;&#2352;</mi><mo>&#160;</mo></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mo>&#160;</mo><mi mathvariant=\"normal\">x</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mi mathvariant=\"normal\">y</mi><mo>&#160;</mo><mo>)</mo></mrow><mn>2</mn></msup><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">y</mi><mn>2</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>2</mn><mi>xy</mi></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo><mo>&#160;</mo><msup><mrow><mo>(</mo><mo>&#160;</mo><mo>&#160;</mo><mi mathvariant=\"normal\">x</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mi mathvariant=\"normal\">y</mi><mo>&#160;</mo><mo>)</mo></mrow><mn>2</mn></msup><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>41</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>2</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>20</mn><mo>&#160;</mo><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mo>&#160;</mo><mn>81</mn></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo><mo>&#160;</mo><mo>&#160;</mo><mi mathvariant=\"normal\">x</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mi mathvariant=\"normal\">y</mi><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>9</mn></math></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">12. </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>If</mi><mo>&#160;</mo><mn>8</mn><msup><mi mathvariant=\"normal\">a</mi><mn>3</mn></msup><mo>+</mo><mo>&#160;</mo><mn>27</mn><msup><mi mathvariant=\"normal\">b</mi><mn>3</mn></msup><mo>=</mo><mn>16</mn><mo>&#160;</mo><mi>and</mi><mo>&#160;</mo><mn>2</mn><mi mathvariant=\"normal\">a</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>3</mn><mi mathvariant=\"normal\">b</mi><mo>=</mo><mo>&#160;</mo><mn>4</mn><mo>,</mo><mo>&#160;</mo><mi>then</mi><mo>&#160;</mo><mi>find</mi><mo>&#160;</mo><mi>the</mi><mo>&#160;</mo><mi>value</mi><mo>&#160;</mo><mi>of</mi><mo>&#160;</mo><mn>16</mn><msup><mi mathvariant=\"normal\">a</mi><mn>4</mn></msup><mo>+</mo><mn>81</mn><msup><mi mathvariant=\"normal\">b</mi><mn>4</mn></msup><mo>.</mo></math></p>",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">12. </span><span style=\"font-family: Nirmala UI;\">यदि</span><span style=\"font-family: Cambria Math;\"> </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>8</mn><msup><mi>a</mi><mn>3</mn></msup><mo>+</mo><mn>27</mn><msup><mi>b</mi><mn>3</mn></msup><mo>=</mo><mn>16</mn></math><span style=\"font-family: Cambria Math;\"> और 2a + 3b= 4 </span><span style=\"font-family: Nirmala UI;\">है</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">तो</span><span style=\"font-family: Cambria Math;\"> </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>16</mn><msup><mi>a</mi><mn>4</mn></msup><mo>+</mo><mn>81</mn><msup><mi>b</mi><mn>4</mn></msup></math><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">का</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">मान</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">ज्ञात</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">कीजिए</span><span style=\"font-family: Cambria Math;\"> |</span></p>",
                    options_en: ["<p>26</p>", "<p>30</p>", 
                                "<p>28</p>", "<p>32</p>"],
                    options_hi: ["<p>26</p>", "<p>30</p>",
                                "<p>28</p>", "<p>32</p>"],
                    solution_en: "<p>12.(d) <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>8</mn><msup><mi mathvariant=\"normal\">a</mi><mn>3</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>27</mn><msup><mi mathvariant=\"normal\">b</mi><mn>3</mn></msup><mo>&#160;</mo><mo>=</mo><mn>16</mn><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo><mo>&#8230;</mo><mo>&#8230;</mo><mi>eq</mi><mo>.</mo><mn>1</mn><mo>&#160;</mo></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>and</mi><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo><mn>2</mn><mi mathvariant=\"normal\">a</mi><mo>&#160;</mo><mo>+</mo><mn>3</mn><mi mathvariant=\"normal\">b</mi><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>4</mn><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo><mo>&#8230;</mo><mo>&#8230;</mo><mi>eq</mi><mo>.</mo><mn>2</mn><mo>&#160;</mo><mo>&#160;</mo></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>Taking</mi><mo>&#160;</mo><mi>cube</mi><mo>&#160;</mo><mi>of</mi><mo>&#160;</mo><mi>eq</mi><mo>.</mo><mn>2</mn><mo>,</mo><mo>&#160;</mo><mi>we</mi><mo>&#160;</mo><mi>get</mi></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#160;</mo><msup><mrow><mo>(</mo><mn>2</mn><mi mathvariant=\"normal\">a</mi><mo>&#160;</mo><mo>+</mo><mn>3</mn><mi mathvariant=\"normal\">b</mi><mo>)</mo></mrow><mn>3</mn></msup><mo>=</mo><mo>(</mo><mn>4</mn><msup><mo>)</mo><mn>3</mn></msup><mo>&#160;</mo></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo><mo>&#160;</mo><mo>(</mo><mo>&#160;</mo><mn>8</mn><msup><mi mathvariant=\"normal\">a</mi><mn>3</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>27</mn><msup><mi mathvariant=\"normal\">b</mi><mn>3</mn></msup><mo>+</mo><mn>18</mn><mi>ab</mi><mo>&#160;</mo><mo>(</mo><mo>&#160;</mo><mn>2</mn><mi mathvariant=\"normal\">a</mi><mo>+</mo><mn>3</mn><mi mathvariant=\"normal\">b</mi><mo>&#160;</mo><mo>)</mo><mo>&#160;</mo><mo>)</mo><mo>=</mo><mo>&#160;</mo><mn>64</mn><mo>&#160;</mo></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo><mo>&#160;</mo><mo>(</mo><mo>&#160;</mo><mo>&#160;</mo><mn>8</mn><msup><mi mathvariant=\"normal\">a</mi><mn>3</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>27</mn><msup><mi mathvariant=\"normal\">b</mi><mn>3</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>72</mn><mi>ab</mi><mo>&#160;</mo><mo>)</mo><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>64</mn><mo>&#160;</mo></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo><mo>&#160;</mo><mo>(</mo><mo>&#160;</mo><mn>16</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>72</mn><mi>ab</mi><mo>&#160;</mo><mo>)</mo><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>64</mn><mo>&#160;</mo></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo><mo>&#160;</mo><mo>&#160;</mo><mi>ab</mi><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mfrac><mn>2</mn><mn>3</mn></mfrac><mo>&#160;</mo></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>Squaring</mi><mo>&#160;</mo><mo>&#160;</mo><mi>eq</mi><mo>.</mo><mn>2</mn><mo>&#160;</mo><mo>,</mo><mo>&#160;</mo><mi>we</mi><mo>&#160;</mo><mi>get</mi><mo>&#160;</mo></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#160;</mo><msup><mrow><mo>(</mo><mo>&#160;</mo><mn>2</mn><mi mathvariant=\"normal\">a</mi><mo>&#160;</mo><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>3</mn><mi mathvariant=\"normal\">b</mi><mo>&#160;</mo><mo>)</mo></mrow><mn>2</mn></msup><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><msup><mn>4</mn><mn>2</mn></msup><mo>&#160;</mo><mo>&#160;</mo><mo>&#8658;</mo><mo>&#160;</mo><mo>(</mo><mo>&#160;</mo><mn>4</mn><msup><mi mathvariant=\"normal\">a</mi><mn>2</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>9</mn><msup><mi mathvariant=\"normal\">b</mi><mn>2</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>12</mn><mi>ab</mi><mo>&#160;</mo><mo>)</mo><mo>=</mo><mo>&#160;</mo><mn>16</mn></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo><mo>&#160;</mo><mn>4</mn><msup><mi mathvariant=\"normal\">a</mi><mn>2</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>9</mn><msup><mi mathvariant=\"normal\">b</mi><mn>2</mn></msup><mo>&#160;</mo><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>16</mn><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mn>12</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mfrac><mn>2</mn><mn>3</mn></mfrac><mo>&#160;</mo><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mo>&#160;</mo><mn>8</mn><mo>&#160;</mo></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo><mo>&#160;</mo><mn>4</mn><msup><mi mathvariant=\"normal\">a</mi><mn>2</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>9</mn><msup><mi mathvariant=\"normal\">b</mi><mn>2</mn></msup><mo>&#160;</mo><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>8</mn><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo><mo>&#8230;</mo><mo>&#8230;</mo><mo>.</mo><mi>eq</mi><mo>.</mo><mn>3</mn></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>Squaring</mi><mo>&#160;</mo><mo>&#160;</mo><mi>eq</mi><mo>.</mo><mn>3</mn><mo>&#160;</mo><mo>,</mo><mo>&#160;</mo><mi>we</mi><mo>&#160;</mo><mi>get</mi><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>16</mn><msup><mi mathvariant=\"normal\">a</mi><mn>4</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>81</mn><msup><mi mathvariant=\"normal\">b</mi><mn>4</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>72</mn><msup><mi mathvariant=\"normal\">a</mi><mn>2</mn></msup><msup><mi mathvariant=\"normal\">b</mi><mn>2</mn></msup><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>64</mn><mo>&#160;</mo><mo>&#160;</mo></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo><mo>&#160;</mo><mn>16</mn><msup><mi mathvariant=\"normal\">a</mi><mn>4</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>81</mn><msup><mi mathvariant=\"normal\">b</mi><mn>4</mn></msup><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>64</mn><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mn>72</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mfrac><mn>4</mn><mn>9</mn></mfrac><mo>&#160;</mo><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>64</mn><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mn>32</mn><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>32</mn></math></p>",
                    solution_hi: "<p>12.(d)&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>8</mn><msup><mi mathvariant=\"normal\">a</mi><mn>3</mn></msup><mo>+</mo><mn>27</mn><msup><mi mathvariant=\"normal\">b</mi><mn>3</mn></msup><mo>&#160;</mo><mo>=</mo><mn>16</mn><mo>&#160;</mo><mo>&#8230;</mo><mo>&#8230;</mo><mi>&#2360;&#2350;&#2368;&#2325;&#2352;&#2339;</mi><mo>&#160;</mo><mo>&#160;</mo><mo>(</mo><mn>1</mn><mo>)</mo></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#160;</mo><mi>&#2324;&#2352;</mi><mo>&#160;</mo><mn>2</mn><mi mathvariant=\"normal\">a</mi><mo>&#160;</mo><mo>+</mo><mn>3</mn><mi mathvariant=\"normal\">b</mi><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>4</mn><mo>&#160;</mo><mo>&#160;</mo><mo>&#8230;</mo><mo>&#8230;</mo><mi>&#2360;&#2350;&#2368;&#2325;&#2352;&#2339;</mi><mo>&#160;</mo><mo>&#160;</mo><mo>(</mo><mn>2</mn><mo>)</mo><mo>&#160;</mo></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#2360;&#2350;&#2368;&#2325;&#2352;&#2339;</mi><mo>&#160;</mo><mo>(</mo><mn>2</mn><mo>)</mo><mo>&#160;</mo><mi>&#2325;&#2366;</mi><mo>&#160;</mo><mi>&#2328;&#2344;</mi><mo>&#160;</mo><mi>&#2354;&#2375;&#2344;&#2375;</mi><mo>&#160;</mo><mi>&#2346;&#2352;</mi><mo>,</mo></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#160;</mo><msup><mrow><mo>(</mo><mn>2</mn><mi mathvariant=\"normal\">a</mi><mo>&#160;</mo><mo>+</mo><mn>3</mn><mi mathvariant=\"normal\">b</mi><mo>)</mo></mrow><mn>3</mn></msup><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mo>(</mo><mn>4</mn><msup><mo>)</mo><mn>3</mn></msup></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo><mo>[</mo><mo>&#160;</mo><mn>8</mn><msup><mi mathvariant=\"normal\">a</mi><mn>3</mn></msup><mo>+</mo><mn>27</mn><msup><mi mathvariant=\"normal\">b</mi><mn>3</mn></msup><mo>+</mo><mn>18</mn><mi>ab</mi><mo>&#160;</mo><mo>(</mo><mn>2</mn><mi mathvariant=\"normal\">a</mi><mo>+</mo><mn>3</mn><mi mathvariant=\"normal\">b</mi><mo>)</mo><mo>]</mo><mo>&#160;</mo><mo>=</mo><mn>64</mn><mo>&#160;</mo></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo><mo>(</mo><mn>8</mn><msup><mi mathvariant=\"normal\">a</mi><mn>3</mn></msup><mo>+</mo><mo>&#160;</mo><mn>27</mn><msup><mi mathvariant=\"normal\">b</mi><mn>3</mn></msup><mo>+</mo><mn>72</mn><mi>ab</mi><mo>)</mo><mo>=</mo><mn>64</mn><mo>&#160;</mo></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo><mo>(</mo><mn>16</mn><mo>+</mo><mn>72</mn><mi>ab</mi><mo>)</mo><mo>=</mo><mn>64</mn><mo>&#160;</mo></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo><mi>ab</mi><mo>=</mo><mfrac><mn>2</mn><mn>3</mn></mfrac></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#2360;&#2350;&#2368;&#2325;&#2352;&#2339;</mi><mo>&#160;</mo><mo>(</mo><mn>2</mn><mo>)</mo><mo>&#160;</mo><mi>&#2325;&#2366;</mi><mo>&#160;</mo><mi>&#2357;&#2352;&#2381;&#2327;</mi><mo>&#160;</mo><mi>&#2325;&#2352;&#2344;&#2375;</mi><mo>&#160;</mo><mi>&#2346;&#2352;</mi><mo>,</mo></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mn>2</mn><mi mathvariant=\"normal\">a</mi><mo>&#160;</mo><mo>+</mo><mn>3</mn><mi mathvariant=\"normal\">b</mi><mo>)</mo></mrow><mn>2</mn></msup><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><msup><mn>4</mn><mn>2</mn></msup><mo>&#8658;</mo><mo>(</mo><mn>4</mn><msup><mi mathvariant=\"normal\">a</mi><mn>2</mn></msup><mo>+</mo><mn>9</mn><msup><mi mathvariant=\"normal\">b</mi><mn>2</mn></msup><mo>+</mo><mn>12</mn><mi>ab</mi><mo>)</mo><mo>=</mo><mn>16</mn></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo><mo>&#160;</mo><mn>4</mn><msup><mi mathvariant=\"normal\">a</mi><mn>2</mn></msup><mo>+</mo><mo>&#160;</mo><mn>9</mn><msup><mi mathvariant=\"normal\">b</mi><mn>2</mn></msup><mo>&#160;</mo><mo>=</mo><mn>16</mn><mo>-</mo><mn>12</mn><mo>&#160;</mo><mo>&#215;</mo><mfrac><mn>2</mn><mn>3</mn></mfrac><mo>=</mo><mn>8</mn><mo>&#160;</mo></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo><mo>&#160;</mo><mn>4</mn><msup><mi mathvariant=\"normal\">a</mi><mn>2</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>9</mn><msup><mi mathvariant=\"normal\">b</mi><mn>2</mn></msup><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>8</mn><mo>&#160;</mo><mo>&#8230;</mo><mo>&#8230;</mo><mo>.</mo><mi>&#2360;&#2350;&#2368;&#2325;&#2352;&#2339;</mi><mo>&#160;</mo><mo>(</mo><mn>3</mn><mo>)</mo><mo>&#160;</mo></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#160;</mo><mi>&#2360;&#2350;&#2368;&#2325;&#2352;&#2339;</mi><mo>&#160;</mo><mo>(</mo><mn>3</mn><mo>)</mo><mo>&#160;</mo><mi>&#2325;&#2366;</mi><mo>&#160;</mo><mi>&#2357;&#2352;&#2381;&#2327;</mi><mo>&#160;</mo><mi>&#2325;&#2352;&#2344;&#2375;</mi><mo>&#160;</mo><mi>&#2346;&#2352;</mi><mo>,</mo></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#160;</mo><mn>16</mn><msup><mi mathvariant=\"normal\">a</mi><mn>4</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>81</mn><msup><mi mathvariant=\"normal\">b</mi><mn>4</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>72</mn><msup><mi mathvariant=\"normal\">a</mi><mn>2</mn></msup><msup><mi mathvariant=\"normal\">b</mi><mn>2</mn></msup><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>64</mn><mo>&#160;</mo><mo>&#160;</mo></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo><mo>&#160;</mo><mn>16</mn><msup><mi mathvariant=\"normal\">a</mi><mn>4</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>81</mn><msup><mi mathvariant=\"normal\">b</mi><mn>4</mn></msup><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>64</mn><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mn>72</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mfrac><mn>4</mn><mn>9</mn></mfrac><mo>&#160;</mo><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>64</mn><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mn>32</mn><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>32</mn></math></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">13. </span><span style=\"font-family: Cambria Math;\">( m<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>x</mi></math> + n ) is a factor of :</span></p>",
                    question_hi: "<p>13. ( <span style=\"font-family: Cambria Math;\">m<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>x</mi></math> + n ) </span><span style=\"font-family: Nirmala UI;\">निम्न</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">में</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">से</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">किसका</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">एक</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">गुणनखंड</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">है</span><span style=\"font-family: Cambria Math;\">?</span></p>",
                    options_en: ["<p><span style=\"font-family: Cambria Math;\"> </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi mathvariant=\"normal\">m</mi><mn>2</mn></msup><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>2</mn><mi>nx</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">n</mi><mn>2</mn></msup></math></p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi mathvariant=\"normal\">m</mi><mn>2</mn></msup><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>2</mn><mi>mnx</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">n</mi><mn>2</mn></msup></math></p>", 
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi mathvariant=\"normal\">m</mi><mn>2</mn></msup><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>2</mn><mi>mx</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">n</mi><mn>2</mn></msup></math></p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi mathvariant=\"normal\">m</mi><mn>2</mn></msup><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>2</mn><mi>mn</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">n</mi><mn>2</mn></msup></math></p>\n<p><span style=\"font-family: Cambria Math;\"> </span></p>"],
                    options_hi: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi mathvariant=\"normal\">m</mi><mn>2</mn></msup><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>2</mn><mi>nx</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">n</mi><mn>2</mn></msup></math></p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi mathvariant=\"normal\">m</mi><mn>2</mn></msup><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>2</mn><mi>mnx</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">n</mi><mn>2</mn></msup></math></p>",
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi mathvariant=\"normal\">m</mi><mn>2</mn></msup><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>2</mn><mi>mx</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">n</mi><mn>2</mn></msup></math></p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi mathvariant=\"normal\">m</mi><mn>2</mn></msup><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>2</mn><mi>mn</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">n</mi><mn>2</mn></msup></math></p>"],
                    solution_en: "<p>13.(b) <span style=\"font-family: \'Cambria Math\';\">A number is the factor of its square. .</span><br><span style=\"font-family: Cambria Math;\">Squaring the identity ( m<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>x</mi></math> + n )</span><br><span style=\"font-family: Cambria Math;\">( m<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>x</mi></math> +n )<sup>2 </sup>= ( m<sup>2</sup><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>x</mi></math><sup>2 </sup>+ 2mn<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>x</mi></math> + n<sup>2 </sup>)</span></p>",
                    solution_hi: "<p>13.(b) <span style=\"font-family: Nirmala UI;\">एक</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">संख्या</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">इसके</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">वर्ग</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">का</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">गुणनखंड</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">है।</span><br><span style=\"font-family: Nirmala UI;\">( m<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>x</mi></math> + n ) का</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">वर्ग</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">करने</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">पर</span><br><span style=\"font-family: Cambria Math;\">( m<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>x</mi></math> + n )<sup>2 </sup>= ( m<sup>2</sup><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>x</mi></math><sup>2 </sup>+ 2mn<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>x</mi></math> + n<sup>2 </sup>)&nbsp;</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">14. </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>If</mi><mo>&#160;</mo><mi mathvariant=\"normal\">x</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mi mathvariant=\"normal\">y</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mi mathvariant=\"normal\">z</mi><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>0</mn><mo>,</mo><mo>&#160;</mo><mi>then</mi><mo>&#160;</mo><mi>what</mi><mo>&#160;</mo><mi>is</mi><mo>&#160;</mo><mi>the</mi><mo>&#160;</mo><mi>value</mi><mo>&#160;</mo><mi>of</mi><mo>&#160;</mo><mfrac><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup><mrow><mo>(</mo><mo>&#160;</mo><mi>yz</mi><mo>&#160;</mo><mo>)</mo></mrow></mfrac><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mfrac><msup><mi mathvariant=\"normal\">y</mi><mn>2</mn></msup><mrow><mo>(</mo><mo>&#160;</mo><mi>xz</mi><mo>&#160;</mo><mo>)</mo></mrow></mfrac><mo>&#160;</mo><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mfrac><msup><mi mathvariant=\"normal\">z</mi><mn>2</mn></msup><mrow><mo>(</mo><mo>&#160;</mo><mi>xy</mi><mo>&#160;</mo><mo>)</mo></mrow></mfrac><mo>&#160;</mo><mo>?</mo></math></p>",
                    question_hi: "<p>14. <span style=\"font-family: Cambria Math;\"><math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#160;</mo><mi>&#2351;&#2342;&#2367;</mi><mo>&#160;</mo><mo>&#160;</mo><mi>x</mi><mo>+</mo><mi>y</mi><mo>+</mo><mi>z</mi><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>0</mn><mo>,</mo><mo>&#160;</mo><mi>&#2361;&#2376;</mi><mo>,</mo><mo>&#160;</mo><mi>&#2340;&#2379;</mi><mo>&#160;</mo><mo>&#160;</mo><mfrac><msup><mi>x</mi><mn>2</mn></msup><mrow><mo>(</mo><mo>&#160;</mo><mi>y</mi><mi>z</mi><mo>&#160;</mo><mo>)</mo></mrow></mfrac><mo>+</mo><mfrac><msup><mi>y</mi><mn>2</mn></msup><mrow><mo>(</mo><mo>&#160;</mo><mi>x</mi><mi>z</mi><mo>&#160;</mo><mo>)</mo></mrow></mfrac><mo>&#160;</mo><mo>+</mo><mfrac><msup><mi>z</mi><mn>2</mn></msup><mrow><mo>(</mo><mo>&#160;</mo><mi>x</mi><mi>y</mi><mo>&#160;</mo><mo>)</mo></mrow></mfrac><mo>&#160;</mo><mi>&#2325;&#2366;</mi><mo>&#160;</mo><mi>&#2350;&#2366;&#2344;</mi><mo>&#160;</mo><mi>&#2325;&#2381;&#2351;&#2366;</mi><mo>&#160;</mo><mi>&#2361;&#2376;</mi><mo>?</mo><mo>&#160;</mo><mo>?</mo></math></span></p>",
                    options_en: ["<p>1</p>", "<p>0</p>", 
                                "<p>2</p>", "<p>3</p>"],
                    options_hi: ["<p>1</p>", "<p>0</p>",
                                "<p>2</p>", "<p>3</p>"],
                    solution_en: "<p>14.(d) <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi mathvariant=\"normal\">x</mi><mn>3</mn></msup><mo>+</mo><msup><mi mathvariant=\"normal\">y</mi><mn>3</mn></msup><mo>+</mo><msup><mi mathvariant=\"normal\">z</mi><mn>3</mn></msup><mo>-</mo><mn>3</mn><mi>xyz</mi><mo>&#160;</mo><mo>=</mo><mo>(</mo><mo>&#160;</mo><mi mathvariant=\"normal\">x</mi><mo>+</mo><mi mathvariant=\"normal\">y</mi><mo>+</mo><mi mathvariant=\"normal\">z</mi><mo>)</mo><mo>(</mo><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup><mo>+</mo><msup><mi mathvariant=\"normal\">y</mi><mn>2</mn></msup><mo>+</mo><msup><mi mathvariant=\"normal\">z</mi><mn>2</mn></msup><mo>-</mo><mi>xy</mi><mo>-</mo><mi>yz</mi><mo>-</mo><mi>zx</mi><mo>)</mo></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>When</mi><mo>&#160;</mo><mo>(</mo><mi mathvariant=\"normal\">x</mi><mo>+</mo><mi mathvariant=\"normal\">y</mi><mo>+</mo><mi mathvariant=\"normal\">z</mi><mo>)</mo><mo>&#160;</mo><mo>=</mo><mn>0</mn><mo>&#160;</mo><mo>,</mo><mo>&#160;</mo><mo>&#160;</mo><mi>then</mi><mo>&#160;</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">x</mi><mn>3</mn></msup><mo>+</mo><msup><mi mathvariant=\"normal\">y</mi><mn>3</mn></msup><mo>+</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">z</mi><mn>3</mn></msup><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>3</mn><mi>xyz</mi><mo>&#160;</mo><mo>&#8230;</mo><mo>&#8230;</mo><mi>eq</mi><mo>.</mo><mn>1</mn></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">x</mi><mo>+</mo><mi mathvariant=\"normal\">y</mi><mo>+</mo><mi mathvariant=\"normal\">z</mi><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>0</mn><mo>&#160;</mo><mo>&#8230;</mo><mo>&#8230;</mo><mo>&#160;</mo><mo>(</mo><mi>given</mi><mo>)</mo><mo>&#160;</mo><mo>&#160;</mo></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo><mo>&#160;</mo><mfrac><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup><mrow><mo>(</mo><mi>YZ</mi><mo>)</mo></mrow></mfrac><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mfrac><msup><mi mathvariant=\"normal\">y</mi><mn>2</mn></msup><mrow><mo>(</mo><mi>xz</mi><mo>)</mo></mrow></mfrac><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mfrac><msup><mi mathvariant=\"normal\">z</mi><mn>2</mn></msup><mrow><mo>(</mo><mi>xy</mi><mo>)</mo></mrow></mfrac><mo>&#160;</mo><mo>=</mo><mfrac><mrow><msup><mi mathvariant=\"normal\">x</mi><mn>3</mn></msup><mo>+</mo><msup><mi mathvariant=\"normal\">y</mi><mn>3</mn></msup><mo>+</mo><msup><mi mathvariant=\"normal\">z</mi><mn>3</mn></msup></mrow><mi>xyz</mi></mfrac><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mfrac><mrow><mn>3</mn><mi>xyz</mi></mrow><mi>xyz</mi></mfrac><mo>&#160;</mo><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>3</mn></math></p>\n<p><span style=\"font-weight: 400;\"> </span></p>",
                    solution_hi: "<p>14.(d) <strong><math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi mathvariant=\"normal\">x</mi><mn>3</mn></msup><mo>+</mo><msup><mi mathvariant=\"normal\">y</mi><mn>3</mn></msup><mo>+</mo><msup><mi mathvariant=\"normal\">z</mi><mn>3</mn></msup><mo>-</mo><mn>3</mn><mi>xyz</mi><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mo>(</mo><mo>&#160;</mo><mi mathvariant=\"normal\">x</mi><mo>+</mo><mi mathvariant=\"normal\">y</mi><mo>+</mo><mi mathvariant=\"normal\">z</mi><mo>&#160;</mo><mo>)</mo><mo>(</mo><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup><mo>+</mo><msup><mi mathvariant=\"normal\">y</mi><mn>2</mn></msup><mo>+</mo><msup><mi mathvariant=\"normal\">z</mi><mn>2</mn></msup><mo>-</mo><mi>xy</mi><mo>-</mo><mi>yz</mi><mo>-</mo><mi>zx</mi><mo>)</mo></math></strong><br><strong><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#160;</mo><mi>&#2332;&#2348;</mi><mo>&#160;</mo><mo>(</mo><mi mathvariant=\"normal\">x</mi><mo>+</mo><mi mathvariant=\"normal\">y</mi><mo>+</mo><mi mathvariant=\"normal\">z</mi><mo>)</mo><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>0</mn><mo>&#160;</mo><mo>&#160;</mo><mo>,</mo><mo>&#160;</mo><mo>&#160;</mo><mi>&#2340;&#2348;</mi><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">x</mi><mn>3</mn></msup><mo>+</mo><msup><mi mathvariant=\"normal\">y</mi><mn>3</mn></msup><mo>+</mo><msup><mi mathvariant=\"normal\">z</mi><mn>3</mn></msup><mo>=</mo><mn>3</mn><mi>xyz</mi><mo>&#160;</mo><mo>&#8230;</mo><mo>&#8230;</mo><mi>&#2360;&#2350;&#2368;&#2325;&#2352;&#2339;</mi><mo>&#160;</mo><mo>(</mo><mn>1</mn><mo>)</mo></math></strong><br><strong><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">x</mi><mo>+</mo><mi mathvariant=\"normal\">y</mi><mo>+</mo><mi mathvariant=\"normal\">z</mi><mo>&#160;</mo><mo>=</mo><mn>0</mn><mo>&#160;</mo><mo>&#8230;</mo><mo>&#8230;</mo><mo>(</mo><mo>&#160;</mo><mi>&#2342;&#2367;&#2351;&#2366;</mi><mo>&#160;</mo><mi>&#2327;&#2351;&#2366;</mi><mo>&#160;</mo><mi>&#2361;&#2376;</mi><mo>&#160;</mo><mo>)</mo><mo>&#160;</mo><mo>&#160;</mo></math></strong><br><strong><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo><mo>&#160;</mo><mfrac><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup><mrow><mo>(</mo><mo>&#160;</mo><mi>YZ</mi><mo>&#160;</mo><mo>)</mo></mrow></mfrac><mo>+</mo><mfrac><msup><mi mathvariant=\"normal\">y</mi><mn>2</mn></msup><mrow><mo>(</mo><mo>&#160;</mo><mi>xz</mi><mo>&#160;</mo><mo>)</mo></mrow></mfrac><mo>+</mo><mfrac><msup><mi mathvariant=\"normal\">z</mi><mn>2</mn></msup><mrow><mo>(</mo><mo>&#160;</mo><mi>xy</mi><mo>&#160;</mo><mo>)</mo></mrow></mfrac><mo>=</mo><mfrac><mrow><msup><mi mathvariant=\"normal\">x</mi><mn>3</mn></msup><mo>+</mo><msup><mi mathvariant=\"normal\">y</mi><mn>3</mn></msup><mo>+</mo><msup><mi mathvariant=\"normal\">z</mi><mn>3</mn></msup></mrow><mi>xyz</mi></mfrac><mo>&#160;</mo><mo>=</mo><mfrac><mrow><mn>3</mn><mi>xyz</mi></mrow><mi>xyz</mi></mfrac><mo>=</mo><mo>&#160;</mo><mn>3</mn></math></strong></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "misc",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">15. </span><span style=\"font-family: Cambria Math;\">What is the possible value of ( a + b + c ) &ndash; 3 , if a</span><sup><span style=\"font-family: Cambria Math;\">2</span></sup><span style=\"font-family: Cambria Math;\"> + b</span><sup><span style=\"font-family: Cambria Math;\">2</span></sup><span style=\"font-family: Cambria Math;\"> + c</span><span style=\"font-family: Cambria Math;\"><sup>2</sup></span><span style=\"font-family: Cambria Math;\"> = 9 and ab + bc + ca = 8?</span></p>",
                    question_hi: "<p>15.<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">यदि</span><span style=\"font-family: Cambria Math;\">&nbsp; a</span><sup><span style=\"font-family: Cambria Math;\">2</span></sup><span style=\"font-family: Cambria Math;\"> + b</span><span style=\"font-family: Cambria Math;\"><sup>2</sup></span><span style=\"font-family: Cambria Math;\"> + c</span><span style=\"font-family: Cambria Math;\"><sup>2</sup></span><span style=\"font-family: Cambria Math;\"> = 9 </span><span style=\"font-family: Nirmala UI;\">और</span><span style=\"font-family: Cambria Math;\"> ab + bc + ca = 8&nbsp; </span><span style=\"font-family: Nirmala UI;\">है</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">तो </span><span style=\"font-family: Cambria Math;\">( a + b + c ) &ndash; 3 </span><span style=\"font-family: Nirmala UI;\">का</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">संभावित</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">मान</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">क्या</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">है</span><span style=\"font-family: Cambria Math;\">?</span></p>",
                    options_en: ["<p>5</p>", "<p>3</p>", 
                                "<p>9</p>", "<p>2</p>"],
                    options_hi: ["<p>5</p>", "<p>3</p>",
                                "<p>9</p>", "<p>2</p>"],
                    solution_en: "<p>15.(d)&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mi mathvariant=\"normal\">x</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mi mathvariant=\"normal\">y</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mi mathvariant=\"normal\">z</mi><mo>)</mo></mrow><mn>2</mn></msup><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup><mo>+</mo><msup><mi mathvariant=\"normal\">y</mi><mn>2</mn></msup><mo>+</mo><msup><mi mathvariant=\"normal\">z</mi><mn>2</mn></msup><mo>+</mo><mn>2</mn><mo>&#160;</mo><mo>(</mo><mi>xy</mi><mo>+</mo><mi>yz</mi><mo>+</mo><mi>zx</mi><mo>)</mo></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>&#8658;</mo><mo>(</mo><mo>&#160;</mo><mi mathvariant=\"normal\">a</mi><mo>+</mo><mi mathvariant=\"normal\">b</mi><mo>+</mo><mi mathvariant=\"normal\">c</mi><mo>)</mo></mrow><mn>2</mn></msup><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>9</mn><mo>+</mo><mn>2</mn><mo>&#215;</mo><mn>8</mn></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo><mi mathvariant=\"normal\">a</mi><mo>+</mo><mi mathvariant=\"normal\">b</mi><mo>+</mo><mi mathvariant=\"normal\">c</mi><mo>=</mo><mn>5</mn><mo>&#160;</mo></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#160;</mo><mi>Now</mi><mo>,</mo><mo>&#160;</mo><mrow><mo>(</mo><mi mathvariant=\"normal\">a</mi><mo>&#160;</mo><mo>+</mo><mi mathvariant=\"normal\">b</mi><mo>&#160;</mo><mo>+</mo><mi mathvariant=\"normal\">c</mi><mo>)</mo></mrow><mo>&#160;</mo><mo>-</mo><mn>3</mn><mo>&#160;</mo><mo>=</mo><mn>5</mn><mo>-</mo><mn>3</mn><mo>&#160;</mo><mo>=</mo><mn>2</mn></math></p>\n<p><span style=\"font-family: Cambria Math;\"> </span></p>",
                    solution_hi: "<p><math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>15</mn><mo>.</mo><mo>(</mo><mi>d</mi><mo>)</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mo>&#160;</mo><mi mathvariant=\"normal\">x</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mi mathvariant=\"normal\">y</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mi mathvariant=\"normal\">z</mi><mo>&#160;</mo><mo>)</mo></mrow><mn>2</mn></msup><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">y</mi><mn>2</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">z</mi><mn>2</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>2</mn><mo>&#160;</mo><mo>(</mo><mo>&#160;</mo><mi>xy</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mi>yz</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mi>zx</mi><mo>&#160;</mo><mo>)</mo></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>&#8658;</mo><mo>(</mo><mo>&#160;</mo><mi mathvariant=\"normal\">a</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mi mathvariant=\"normal\">b</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mi mathvariant=\"normal\">c</mi><mo>)</mo></mrow><mn>2</mn></msup><mo>=</mo><mo>&#160;</mo><mn>9</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>2</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>8</mn></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo><mi mathvariant=\"normal\">a</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mi mathvariant=\"normal\">b</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mi mathvariant=\"normal\">c</mi><mo>&#160;</mo><mo>=</mo><mn>5</mn><mo>&#160;</mo></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#2309;&#2348;</mi><mo>,</mo><mo>&#160;</mo><mrow><mo>(</mo><mo>&#160;</mo><mi mathvariant=\"normal\">a</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mi mathvariant=\"normal\">b</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mi mathvariant=\"normal\">c</mi><mo>)</mo></mrow><mo>&#160;</mo><mo>-</mo><mn>3</mn><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>5</mn><mo>-</mo><mn>3</mn><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>2</mn></math></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>