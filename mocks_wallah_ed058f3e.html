<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">15:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 15</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">15</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 15 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["6"] = {
                name: "Reasoning",
                start: 0,
                end: 13
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="6">Reasoning</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 14,
                end: 14
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "6",
                    question_en: "<p>1. <span style=\"font-family: Cambria Math;\">Select the option that is related to the fifth term in the same way as the second term is related to the first term and fourth term related to third term.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">11 : 1309 :: 15 : 3345 :: 7 : ? </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC CHSL 01/06/2022 (Evening)</span></p>\n",
                    question_hi: "<p>1. <span style=\"font-family: Nirmala UI;\">&#2313;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2325;&#2354;&#2381;&#2346;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2330;&#2351;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2366;&#2306;&#2330;&#2357;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2325;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2348;&#2306;&#2343;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2376;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2370;&#2360;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2361;&#2354;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2330;&#2380;&#2341;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2368;&#2360;&#2352;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2348;&#2306;&#2343;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">11:1309 :</span><span style=\"font-family: Cambria Math;\">: 15: 3345 :: 7: ?</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC CHSL 01/06/2022 (Evening)</span></p>\n",
                    options_en: ["<p>329</p>\n", "<p>333</p>\n", 
                                "<p>330</p>\n", "<p>335</p>\n"],
                    options_hi: ["<p>329</p>\n", "<p>333</p>\n",
                                "<p>330</p>\n", "<p>335</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">1.(</span><span style=\"font-family: Cambria Math;\">a)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Logic :</span><span style=\"font-family: Cambria Math;\">-</span><span style=\"font-family: Cambria Math;\"> n </span><span style=\"font-family: Cambria Math;\">:</span><span style=\"font-family: Cambria Math;\"> (n&sup3; </span><span style=\"font-family: Cambria Math;\">- 2n)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">In </span><span style=\"font-family: Cambria Math;\">11 :</span><span style=\"font-family: Cambria Math;\"> 1309, <span style=\"font-weight: 400;\">11</span><span style=\"font-weight: 400;\"> : </span><span style=\"font-weight: 400;\">(</span><span style=\"font-weight: 400;\">11&sup3; </span><span style=\"font-weight: 400;\">- </span><span style=\"font-weight: 400;\">2 &times; </span><span style=\"font-weight: 400;\">11)</span><span style=\"font-weight: 400;\"> = </span><span style=\"font-weight: 400;\">11</span><span style=\"font-weight: 400;\"> : </span><span style=\"font-weight: 400;\">(1331</span><span style=\"font-weight: 400;\"> - </span><span style=\"font-weight: 400;\">22)</span><span style=\"font-weight: 400;\"> </span></span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">= </span><span style=\"font-weight: 400;\">11</span></span><span style=\"font-family: Cambria Math;\">&nbsp;</span><span style=\"font-family: Cambria Math;\">: 1309</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">In </span><span style=\"font-family: Cambria Math;\">15 :</span><span style=\"font-family: Cambria Math;\"> 3345, </span><span style=\"font-family: Cambria Math;\"> : <span style=\"font-weight: 400;\">(</span><span style=\"font-weight: 400;\">15&sup3;</span><span style=\"font-weight: 400;\">&nbsp;- </span><span style=\"font-weight: 400;\">2&times;</span><span style=\"font-weight: 400;\">15)</span><span style=\"font-weight: 400;\"> = </span><span style=\"font-weight: 400;\">15</span><span style=\"font-weight: 400;\"> : </span><span style=\"font-weight: 400;\">(3375</span><span style=\"font-weight: 400;\"> - </span><span style=\"font-weight: 400;\">30)</span></span><span style=\"font-family: Cambria Math;\">&nbsp;</span><span style=\"font-family: Cambria Math;\">= 15 : 3345</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Similarly, in </span><span style=\"font-family: Cambria Math;\">7 :</span><span style=\"font-family: Cambria Math;\"> (?), <span style=\"font-weight: 400;\">&nbsp;</span><span style=\"font-weight: 400;\">7</span><span style=\"font-weight: 400;\"> :</span><span style=\"font-weight: 400;\"> </span><span style=\"font-weight: 400;\">(</span><span style=\"font-weight: 400;\">7&sup3;</span><span style=\"font-weight: 400;\">&nbsp;- </span><span style=\"font-weight: 400;\">2&times;</span><span style=\"font-weight: 400;\">7)</span><span style=\"font-weight: 400;\"> = </span><span style=\"font-weight: 400;\">7</span><span style=\"font-weight: 400;\"> : </span><span style=\"font-weight: 400;\">(343</span><span style=\"font-weight: 400;\"> - </span><span style=\"font-weight: 400;\">14)</span></span><span style=\"font-family: Cambria Math;\">&nbsp;</span><span style=\"font-family: Cambria Math;\">= 7 : 329</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">1.(</span><span style=\"font-family: Cambria Math;\">a)</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2340;&#2352;&#2381;&#2325;</span><span style=\"font-family: Cambria Math;\"> :</span><span style=\"font-family: Cambria Math;\">- </span><span style=\"font-family: Cambria Math;\"> :&nbsp;</span><span style=\"font-family: Cambria Math;\">n </span><span style=\"font-family: Cambria Math;\">:</span><span style=\"font-family: Cambria Math;\"> (n&sup3; </span><span style=\"font-family: Cambria Math;\">- 2n)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><math style=\"font-family: Verdana;\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#2350;&#2375;&#2306;</mi><mo>,</mo></math>&nbsp;</span><span style=\"font-family: Cambria Math;\">11 :</span><span style=\"font-family: Cambria Math;\"> 1309, <span style=\"font-weight: 400;\">11</span><span style=\"font-weight: 400;\"> : </span><span style=\"font-weight: 400;\">(</span><span style=\"font-weight: 400;\">11&sup3; </span><span style=\"font-weight: 400;\">- </span><span style=\"font-weight: 400;\">2 &times; </span><span style=\"font-weight: 400;\">11)</span><span style=\"font-weight: 400;\"> = </span><span style=\"font-weight: 400;\">11</span><span style=\"font-weight: 400;\"> : </span><span style=\"font-weight: 400;\">(1331</span><span style=\"font-weight: 400;\"> - </span><span style=\"font-weight: 400;\">22)</span><span style=\"font-weight: 400;\"> </span></span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">= </span><span style=\"font-weight: 400;\">11</span></span><span style=\"font-family: Cambria Math;\">&nbsp;</span><span style=\"font-family: Cambria Math;\">: 1309</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&nbsp;</span><span style=\"font-family: Cambria Math;\">15 :</span><span style=\"font-family: Cambria Math;\"> 3345, <math style=\"font-family: Verdana;\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#2350;&#2375;&#2306;</mi><mo>,</mo></math>15 </span><span style=\"font-family: Cambria Math;\">: <span style=\"font-weight: 400;\">(</span><span style=\"font-weight: 400;\">15&sup3;</span><span style=\"font-weight: 400;\">&nbsp;- </span><span style=\"font-weight: 400;\">2&times;</span><span style=\"font-weight: 400;\">15)</span><span style=\"font-weight: 400;\"> = </span><span style=\"font-weight: 400;\">15</span><span style=\"font-weight: 400;\"> : </span><span style=\"font-weight: 400;\">(3375</span><span style=\"font-weight: 400;\"> - </span><span style=\"font-weight: 400;\">30)</span></span><span style=\"font-family: Cambria Math;\">&nbsp;</span><span style=\"font-family: Cambria Math;\">= 15 : 3345</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><math style=\"font-family: Verdana;\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#2311;&#2360;&#2368;</mi><mo>&nbsp;</mo><mi>&#2346;&#2381;&#2352;&#2325;&#2366;&#2352;</mi><mo>,</mo><mo>&nbsp;</mo></math>&nbsp;</span><span style=\"font-family: Cambria Math;\">7 :</span><span style=\"font-family: Cambria Math;\"> (?) <math style=\"font-family: Verdana;\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#2350;&#2375;&#2306;</mi><mo>,</mo></math>, <span style=\"font-weight: 400;\">&nbsp;</span><span style=\"font-weight: 400;\">7</span><span style=\"font-weight: 400;\"> :</span><span style=\"font-weight: 400;\"> </span><span style=\"font-weight: 400;\">(</span><span style=\"font-weight: 400;\">7&sup3;</span><span style=\"font-weight: 400;\">&nbsp;- </span><span style=\"font-weight: 400;\">2&times;</span><span style=\"font-weight: 400;\">7)</span><span style=\"font-weight: 400;\"> = </span><span style=\"font-weight: 400;\">7</span><span style=\"font-weight: 400;\"> : </span><span style=\"font-weight: 400;\">(343</span><span style=\"font-weight: 400;\"> - </span><span style=\"font-weight: 400;\">14)</span></span><span style=\"font-family: Cambria Math;\">&nbsp;</span><span style=\"font-family: Cambria Math;\">= 7 : 329</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "6",
                    question_en: "<p>2. <span style=\"font-family: Cambria Math;\">Select the option that is related to the fourth term in the same way as the first term is related to the second term and the fifth term is related to the sixth term</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">5 :</span><span style=\"font-family: Cambria Math;\"> 7 :: ? : </span><span style=\"font-family: Cambria Math;\">271 :</span><span style=\"font-family: Cambria Math;\">: 22 : 466</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC CHSL 01/06/2022 (Evening)</span></p>\n",
                    question_hi: "<p>2. <span style=\"font-family: Nirmala UI;\">&#2313;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2325;&#2354;&#2381;&#2346;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2330;&#2351;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2330;&#2380;&#2341;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2325;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2348;&#2306;&#2343;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2376;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2361;&#2354;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2370;&#2360;&#2352;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2348;&#2306;&#2343;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2366;&#2306;&#2330;&#2357;&#2366;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2331;&#2336;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2348;&#2306;&#2343;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">5 :</span><span style=\"font-family: Cambria Math;\"> 7 :: ? : </span><span style=\"font-family: Cambria Math;\">271 :</span><span style=\"font-family: Cambria Math;\">: 22 : 466</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC CHSL 01/06/2022 (Evening)</span></p>\n",
                    options_en: ["<p>22</p>\n", "<p>24</p>\n", 
                                "<p>17</p>\n", "<p><span style=\"font-family: Cambria Math;\">18</span></p>\n"],
                    options_hi: ["<p>22</p>\n", "<p>24</p>\n",
                                "<p>17</p>\n", "<p>18</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">2.(</span><span style=\"font-family: Cambria Math;\">c) Lo</span><span style=\"font-family: Cambria Math;\">gic :</span><span style=\"font-family: Cambria Math;\">- n </span><span style=\"font-family: Cambria Math;\">: n&sup2; </span><span style=\"font-family: Cambria Math;\">- 18</span></p>\r\n<p><span style=\"font-weight: 400;\">In 5 : 7, </span><span style=\"font-weight: 400;\">5</span><span style=\"font-weight: 400;\"> </span><span style=\"font-weight: 400;\">: </span><span style=\"font-weight: 400;\">5<span style=\"font-family: Cambria Math;\">&sup2;</span></span><span style=\"font-weight: 400;\">&nbsp;- 18 = </span><span style=\"font-weight: 400;\">5</span><span style=\"font-weight: 400;\"> : </span><span style=\"font-weight: 400;\">25</span><span style=\"font-weight: 400;\"> - 18 = 5 : 7</span></p>\r\n<p><span style=\"font-weight: 400;\">In 22 : 466, </span><span style=\"font-weight: 400;\">22</span><span style=\"font-weight: 400;\"> : </span><span style=\"font-weight: 400;\">22<span style=\"font-family: Cambria Math;\">&sup2;</span></span><span style=\"font-weight: 400;\">&nbsp;- 18 = </span><span style=\"font-weight: 400;\">22</span><span style=\"font-weight: 400;\"> : </span><span style=\"font-weight: 400;\">484</span><span style=\"font-weight: 400;\"> - 18 = 22 : 466</span></p>\r\n<p><span style=\"font-weight: 400;\">Similarly, (?) : 271,&nbsp; </span><span style=\"font-weight: 400;\">17</span><span style=\"font-weight: 400;\"> :</span><span style=\"font-weight: 400;\"> </span><span style=\"font-weight: 400;\">17<span style=\"font-family: Cambria Math;\">&sup2;</span></span><span style=\"font-weight: 400;\">&nbsp;- 18 = 17 : 289 - 18 = </span><strong>17</strong><span style=\"font-weight: 400;\"> : 271 </span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">2.(</span><span style=\"font-family: Cambria Math;\">c)</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2340;&#2352;&#2381;&#2325;</span><span style=\"font-family: Cambria Math;\"> :</span><span style=\"font-family: Cambria Math;\">-&nbsp;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">n </span><span style=\"font-family: Cambria Math;\">: n&sup2; </span><span style=\"font-family: Cambria Math;\">- 18</span></p>\r\n<p><span style=\"font-weight: 400;\">&nbsp;5 : 7 <math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#2350;&#2375;&#2306;</mi><mo>,</mo></math> </span><span style=\"font-weight: 400;\">5</span><span style=\"font-weight: 400;\"> </span><span style=\"font-weight: 400;\">: </span><span style=\"font-weight: 400;\">5<span style=\"font-family: Cambria Math;\">&sup2;</span></span><span style=\"font-weight: 400;\">&nbsp;- 18 = </span><span style=\"font-weight: 400;\">5</span><span style=\"font-weight: 400;\"> : </span><span style=\"font-weight: 400;\">25</span><span style=\"font-weight: 400;\"> - 18 = 5 : 7</span></p>\r\n<p><span style=\"font-weight: 400;\">&nbsp;22 : 466&nbsp;<math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#2350;&#2375;&#2306;</mi><mo>,</mo></math> </span><span style=\"font-weight: 400;\">22</span><span style=\"font-weight: 400;\"> : </span><span style=\"font-weight: 400;\">22<span style=\"font-family: Cambria Math;\">&sup2;</span></span><span style=\"font-weight: 400;\">&nbsp;- 18 = </span><span style=\"font-weight: 400;\">22</span><span style=\"font-weight: 400;\"> : </span><span style=\"font-weight: 400;\">484</span><span style=\"font-weight: 400;\"> - 18 = 22 : 466</span></p>\r\n<p><span style=\"font-weight: 400;\"><math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#2311;&#2360;&#2368;</mi><mo>&nbsp;</mo><mi>&#2346;&#2381;&#2352;&#2325;&#2366;&#2352;</mi><mo>,</mo><mo>&nbsp;</mo></math>&nbsp;(?) : 271&nbsp;<math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#2350;&#2375;&#2306;</mi><mo>,</mo></math> &nbsp;</span><span style=\"font-weight: 400;\">17</span><span style=\"font-weight: 400;\"> :</span><span style=\"font-weight: 400;\"> </span><span style=\"font-weight: 400;\">17<span style=\"font-family: Cambria Math;\">&sup2;</span></span><span style=\"font-weight: 400;\">&nbsp;- 18 = 17 : 289 - 18 = </span><strong>17</strong><span style=\"font-weight: 400;\"> : 271</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "6",
                    question_en: "<p>3.<span style=\"font-family: Cambria Math;\"> Select the option that is related to the third term in the same way as the second term is related to the first term and the sixth term is related to the fifth term.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">12 :</span><span style=\"font-family: Cambria Math;\"> 139 :: 7 : ? :: </span><span style=\"font-family: Cambria Math;\">18 :</span><span style=\"font-family: Cambria Math;\"> 319</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC CHSL 01/06/2022 (Evening)</span></p>\n",
                    question_hi: "<p>3. <span style=\"font-family: Nirmala UI;\">&#2313;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2325;&#2354;&#2381;&#2346;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2330;&#2351;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2368;&#2360;&#2352;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2325;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2348;&#2306;&#2343;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2376;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2370;&#2360;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2361;&#2354;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2331;&#2336;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2366;&#2306;&#2330;&#2357;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2348;&#2306;&#2343;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">12 :</span><span style=\"font-family: Cambria Math;\"> 139 :: 7 : ? :: </span><span style=\"font-family: Cambria Math;\">18 :</span><span style=\"font-family: Cambria Math;\"> 319</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC CHSL 01/06/2022 (Evening)</span></p>\n",
                    options_en: ["<p>40</p>\n", "<p>56</p>\n", 
                                "<p>44</p>\n", "<p>52</p>\n"],
                    options_hi: ["<p>40</p>\n", "<p>56</p>\n",
                                "<p>44</p>\n", "<p>52</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">3.(</span><span style=\"font-family: Cambria Math;\">c)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Logic :</span><span style=\"font-family: Cambria Math;\">-</span><span style=\"font-family: Cambria Math;\"> n </span><span style=\"font-family: Cambria Math;\">: n&sup2;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> - 5</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">In </span><span style=\"font-family: Cambria Math;\">12 :</span><span style=\"font-family: Cambria Math;\"> 139, 12 </span><span style=\"font-family: Cambria Math;\">: 12&sup2; </span><span style=\"font-family: Cambria Math;\">- 5 = 12&nbsp;: 144 - 5 = 12 : 139</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">In </span><span style=\"font-family: Cambria Math;\">18 :</span><span style=\"font-family: Cambria Math;\"> 319, 18 </span><span style=\"font-family: Cambria Math;\">: 18&sup2; </span><span style=\"font-family: Cambria Math;\">- 5 = 18&nbsp;: 324 - 5 = 18 : 319</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Similarly, </span><span style=\"font-family: Cambria Math;\">7 :</span><span style=\"font-family: Cambria Math;\"> (?), 7 : 7&sup2;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> - 5 = 7&nbsp;: 49 - 5 = 7 : </span><span style=\"font-family: Cambria Math;\">44</span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">3.(</span><span style=\"font-family: Cambria Math;\">c)</span></p>\r\n<p><strong><span style=\"font-family: Nirmala UI;\">&#2340;&#2352;&#2381;&#2325;</span></strong><span style=\"font-family: Cambria Math;\"> :</span><span style=\"font-family: Cambria Math;\">-&nbsp;</span><span style=\"font-family: Cambria Math;\">n </span><span style=\"font-family: Cambria Math;\">: n&sup2;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> - 5</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&nbsp;</span><span style=\"font-family: Cambria Math;\">12 :</span><span style=\"font-family: Cambria Math;\"> 139&nbsp;<math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#2350;&#2375;&#2306;</mi><mo>,</mo><mo>&nbsp;</mo></math> 12 </span><span style=\"font-family: Cambria Math;\">: 12&sup2; </span><span style=\"font-family: Cambria Math;\">- 5 = 12&nbsp;: 144 - 5 = 12 : 139</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&nbsp;</span><span style=\"font-family: Cambria Math;\">18 :</span><span style=\"font-family: Cambria Math;\"> 319&nbsp;<math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#2350;&#2375;&#2306;</mi><mo>,</mo><mo>&nbsp;</mo></math> 18 </span><span style=\"font-family: Cambria Math;\">: 18&sup2; </span><span style=\"font-family: Cambria Math;\">- 5 = 18&nbsp;: 324 - 5 = 18 : 319</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#2311;&#2360;&#2368;</mi><mo>&nbsp;</mo><mi>&#2340;&#2352;&#2361;</mi></math>&nbsp;</span><span style=\"font-family: Cambria Math;\">7 :</span><span style=\"font-family: Cambria Math;\"> (?)&nbsp;<math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#2350;&#2375;&#2306;</mi><mo>,</mo><mo>&nbsp;</mo></math> 7 : 7&sup2;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> - 5 = 7&nbsp;: 49 - 5 = 7 : </span><span style=\"font-family: Cambria Math;\">44</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "6",
                    question_en: "<p>4. <span style=\"font-family: Cambria Math;\">Select the option that is related to the fifth number in the same way as the second number is related to the</span><span style=\"font-family: Cambria Math;\"> first number and the fourth number is related to the third number.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">5 :</span><span style=\"font-family: Cambria Math;\"> 117 :: 6 : 208 :: 8 : ?</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC CHSL 01/06/2022 (Evening)</span></p>\n",
                    question_hi: "<p>4. <span style=\"font-family: Nirmala UI;\">&#2313;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2325;&#2354;&#2381;&#2346;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2330;&#2351;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2366;&#2306;&#2330;&#2357;&#2368;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2325;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2348;&#2306;&#2343;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2376;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2370;&#2360;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;</span><span style=\"font-family: Nirmala UI;\">&#2361;&#2354;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2348;&#2306;&#2343;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2330;&#2380;&#2341;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2368;&#2360;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2348;&#2306;&#2343;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">5 :</span><span style=\"font-family: Cambria Math;\"> 117 :: 6 : 208 :: 8 : ?</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC CHSL 01/06/2022 (Evening)</span></p>\n",
                    options_en: ["<p>405</p>\n", "<p>450</p>\n", 
                                "<p>504</p>\n", "<p>540</p>\n"],
                    options_hi: ["<p>405</p>\n", "<p>450</p>\n",
                                "<p>504</p>\n", "<p>540</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">4.(</span><span style=\"font-family: Cambria Math;\">c)</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Logic :</span><span style=\"font-family: Cambria Math;\">-</span></strong><span style=\"font-family: Cambria Math;\"> n </span><span style=\"font-family: Cambria Math;\">: n&sup3; </span><span style=\"font-family: Cambria Math;\">-</span><span style=\"font-family: Cambria Math;\"> 8</span></p>\r\n<p><span style=\"font-weight: 400;\">In 5 : 117, </span><span style=\"font-weight: 400;\">5</span><span style=\"font-weight: 400;\"> : </span><span style=\"font-weight: 400;\">5<span style=\"font-family: Cambria Math;\">&sup3;</span></span><span style=\"font-weight: 400;\">&nbsp;- 8 = 5 : 125 - 8 = 5 : 117</span></p>\r\n<p><span style=\"font-weight: 400;\">In 6 : 208, </span><span style=\"font-weight: 400;\">6</span><span style=\"font-weight: 400;\"> : </span><span style=\"font-weight: 400;\">6<span style=\"font-family: Cambria Math;\">&sup3;</span></span><span style=\"font-weight: 400;\">&nbsp;-</span><span style=\"font-weight: 400;\"> 8 = 6 : 216 - 8 = 6 : 208</span></p>\r\n<p><span style=\"font-weight: 400;\">Similarly, in 8 : (?), </span><span style=\"font-weight: 400;\">8</span><span style=\"font-weight: 400;\"> : </span><span style=\"font-weight: 400;\">8<span style=\"font-family: Cambria Math;\">&sup3;</span></span><span style=\"font-weight: 400;\">&nbsp;</span><span style=\"font-weight: 400;\">- 8 = 8 : 512 - 8 = 8 : </span><strong>504</strong></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">4.(</span><span style=\"font-family: Cambria Math;\">c)</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2340;&#2352;&#2381;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">:</span><span style=\"font-family: Cambria Math;\">- n</span><span style=\"font-family: Cambria Math;\"> : n&sup3;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> - 8</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><math style=\"font-family: Verdana;\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>5</mn><mo>&nbsp;</mo><mo>:</mo><mo>&nbsp;</mo><mn>117</mn><mo>&nbsp;</mo><mi>&#2350;&#2375;&#2306;</mi><mo>,</mo><mo>&nbsp;</mo><mn>5</mn><mo>&nbsp;</mo><mo>:</mo><mo>&nbsp;</mo><msup><mn>5</mn><mn>3</mn></msup><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>8</mn><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>5</mn><mo>&nbsp;</mo><mo>:</mo><mo>&nbsp;</mo><mn>125</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>8</mn><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>5</mn><mo>&nbsp;</mo><mo>:</mo><mo>&nbsp;</mo><mn>117</mn><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mn>6</mn><mo>&nbsp;</mo><mo>:</mo><mo>&nbsp;</mo><mn>208</mn><mo>&nbsp;</mo><mi>&#2350;&#2375;&#2306;</mi><mo>,</mo><mo>&nbsp;</mo><mn>6</mn><mo>&nbsp;</mo><mo>:</mo><mo>&nbsp;</mo><msup><mn>6</mn><mn>3</mn></msup><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>8</mn><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>6</mn><mo>&nbsp;</mo><mo>:</mo><mo>&nbsp;</mo><mn>216</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>8</mn><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>6</mn><mo>&nbsp;</mo><mo>:</mo><mo>&nbsp;</mo><mn>208</mn><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mi>&#2311;&#2360;&#2368;</mi><mo>&nbsp;</mo><mi>&#2340;&#2352;&#2361;</mi><mo>,</mo><mo>&nbsp;</mo><mn>8</mn><mo>&nbsp;</mo><mo>:</mo><mo>&nbsp;</mo><mo>(</mo><mo>?</mo><mo>)</mo><mo>&nbsp;</mo><mi>&#2350;&#2375;&#2306;</mi><mo>,</mo><mo>&nbsp;</mo><mn>8</mn><mo>&nbsp;</mo><mo>:</mo><mo>&nbsp;</mo><msup><mn>8</mn><mn>3</mn></msup><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>8</mn><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>8</mn><mo>&nbsp;</mo><mo>:</mo><mo>&nbsp;</mo><mn>512</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>8</mn><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>8</mn><mo>&nbsp;</mo><mo>:</mo><mo>&nbsp;</mo><mn>504</mn></math></span></p>\r\n<p>&nbsp;</p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "6",
                    question_en: "<p>5. <span style=\"font-family: Cambria Math;\">Select the option that is related to the third term in the same way as the second term is related to the first term and the sixth term is related to the fifth term.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">INSTEAD :</span><span style=\"font-family: Cambria Math;\"> NISTEDA :: JUSTIC</span><span style=\"font-family: Cambria Math;\">E : ? :: </span><span style=\"font-family: Cambria Math;\">LIBERAL :</span><span style=\"font-family: Cambria Math;\"> ILBERLA</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC CHSL 01/06/2022 (Evening)</span></p>\n",
                    question_hi: "<p>5. <span style=\"font-family: Nirmala UI;\">&#2313;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2325;&#2354;&#2381;&#2346;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2330;&#2351;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2368;&#2360;&#2352;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2325;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2348;&#2306;&#2343;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2376;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2370;&#2360;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2361;&#2354;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2331;&#2336;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2366;&#2306;&#2330;&#2357;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2348;&#2306;&#2343;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">INSTEAD :</span><span style=\"font-family: Cambria Math;\"> NISTEDA :: JUSTICE : ? :: </span><span style=\"font-family: Cambria Math;\">LIBERAL :</span><span style=\"font-family: Cambria Math;\"> ILBERLA</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC CHSL 01/06/2022 (Evening)</span></p>\n",
                    options_en: ["<p>UJTSIEC</p>\n", "<p>UJSTIEC</p>\n", 
                                "<p>JUTSICE</p>\n", "<p>JUSITCE</p>\n"],
                    options_hi: ["<p>UJTSIEC</p>\n", "<p>UJSTIEC</p>\n",
                                "<p>JUTSICE</p>\n", "<p>JUSITCE</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">5.(</span><span style=\"font-family: Cambria Math;\">b)</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1667892833/word/media/image1.png\" width=\"135\" height=\"76\"></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1667892833/word/media/image2.png\" width=\"138\" height=\"82\"></p>\r\n<p><span style=\"font-family: Cambria Math;\">Similarly,</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1667892833/word/media/image3.png\" width=\"154\" height=\"88\"></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">5.(</span><span style=\"font-family: Cambria Math;\">b)</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1667892833/word/media/image1.png\" width=\"140\" height=\"79\"></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1667892833/word/media/image2.png\" width=\"142\" height=\"85\"></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2311;&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2352;&#2361;</span><span style=\"font-family: Cambria Math;\">,</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1667892833/word/media/image3.png\" width=\"159\" height=\"91\"></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "6",
                    question_en: " <p>6. </span><span style=\"font-family:Cambria Math\">Select the option that is related to the fifth number in the same way as the second number is related to the first number and the fourth number is related to the third number.</span></p> <p><span style=\"font-family:Cambria Math\">9 : 135  ::  6 : 54  : : 7 : ?</span></p> <p><span style=\"font-family:Cambria Math\">SSC CHSL 02/06/2022 (Morning)</span></p>",
                    question_hi: " <p>6. </span><span style=\"font-family:Nirmala UI\">उस</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">विकल्प</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">का</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">चयन</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">करें</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">जो</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">पांचवीं</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">संख्या</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">से</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">उसी</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">प्रकार</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">संबंधित</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">है</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">जैसे</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">दूसरी</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">संख्या</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">पहली</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">संख्या</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">से</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">संबंधित</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">है</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">और</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">चौथी</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">संख्या</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">तीसरी</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">संख्या</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">से</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">संबंधित</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">है।</span></p> <p><span style=\"font-family:Cambria Math\">9 : 135 :: 6 : 54 : :7 : ?</span></p> <p><span style=\"font-family:Cambria Math\">SSC CHSL 02/06/2022 (Morning)</span></p>",
                    options_en: [" <p> 84</span></p>", " <p> 74</span></p>", 
                                " <p> 77</span></p>", " <p> 85</span></p>"],
                    options_hi: [" <p> 84</span></p>", " <p> 74</span></p>",
                                " <p> 77</span></p>", " <p> 85</span></p>"],
                    solution_en: " <p><span style=\"font-family:Cambria Math\">6.(</span><span style=\"font-family:Cambria Math\">c)</span></p> <p><span style=\"font-family:Cambria Math\">In </span><span style=\"font-family:Cambria Math\">9 :</span><span style=\"font-family:Cambria Math\"> 135, 9(9 - 1) + 9(9 - 2)  = 9 × 8 + 9 × 7 = 72 + 63 = 135</span></p> <p><span style=\"font-family:Cambria Math\">In </span><span style=\"font-family:Cambria Math\">6 :</span><span style=\"font-family:Cambria Math\"> 54 ,  6(6 - 1) + 6(6 - 2) = 6×5 + 6×4 = 30 + 24 = 54</span></p> <p><span style=\"font-family:Cambria Math\">Similarly In </span><span style=\"font-family:Cambria Math\">7 :</span><span style=\"font-family:Cambria Math\"> ? , 7(7 - 1) + 7(7 - 2) = 7×6 + 7 × 5 = 42 + 35 = </span><span style=\"font-family:Cambria Math\">77</span></p> <p><span style=\"font-family:Cambria Math\">So, we get </span><span style=\"font-family:Cambria Math\">7 :</span><span style=\"font-family:Cambria Math\"> 77</span></p>",
                    solution_hi: " <p><span style=\"font-family:Cambria Math\">6.(</span><span style=\"font-family:Cambria Math\">c)</span></p> <p><span style=\"font-family:Cambria Math\">In </span><span style=\"font-family:Cambria Math\">9 :</span><span style=\"font-family:Cambria Math\">135, 9(9 - 1) + 9(9 - 2)  = 9×8 + 9×7 = 72 + 63 = 135</span></p> <p><span style=\"font-family:Cambria Math\">In </span><span style=\"font-family:Cambria Math\">6 :</span><span style=\"font-family:Cambria Math\"> 54 ,  6(6 - 1) + 6(6 - 2) = 6×5 + 6×4 = 30 + 24 = 54</span></p> <p><span style=\"font-family:Cambria Math\">Similarly In </span><span style=\"font-family:Cambria Math\">7 :</span><span style=\"font-family:Cambria Math\"> ? , 7(7 - 1) + 7(7 - 2) = 7×6 + 7×5 = 42 + 35 = </span><span style=\"font-family:Cambria Math\">77</span></p> <p><span style=\"font-family:Cambria Math\">So, we get </span><span style=\"font-family:Cambria Math\">7 :</span><span style=\"font-family:Cambria Math\"> 77</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "6",
                    question_en: " <p>7. </span><span style=\"font-family:Cambria Math\">Select the option that is relat</span><span style=\"font-family:Cambria Math\">ed to the fifth term in the same way as the second term is related to the first term and the fourth term is related to the third term.</span></p> <p><span style=\"font-family:Cambria Math\">PEARL :</span><span style=\"font-family:Cambria Math\"> LRAEP :: ULTRA : ARTLU :: VIRAL : ?</span></p> <p><span style=\"font-family:Cambria Math\">SSC CHSL 02/06/2022 (Morning)</span></p>",
                    question_hi: " <p>7. </span><span style=\"font-family:Nirmala UI\">उस</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">विकल्प</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">का</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">चयन</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">करें</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">जो</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">पांचवें</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">पद</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">से</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">उसी</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">प्रकार</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">संबंधित</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">है</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">जैसे</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">दूसरा</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">पद</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">पहले</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">पद</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">से</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">और</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">चौथा</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">पद</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">तीसरे</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">पद</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">से</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">संबंधित</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">है।</span></p> <p><span style=\"font-family:Cambria Math\">PEARL :</span><span style=\"font-family:Cambria Math\"> LRAEP :: ULTRA : ARTLU :: VIRAL : ?</span></p> <p><span style=\"font-family:Cambria Math\">SSC CHSL 02/06/2022 (Morning)</span></p>",
                    options_en: [" <p> LIRAV</span></p>", " <p> AIRLV</span></p>", 
                                " <p> LARIV</span></p>", " <p> ALRIV</span></p>"],
                    options_hi: [" <p> LIRAV</span></p>", " <p> AIRLV</span></p>",
                                " <p> LARIV</span></p>", " <p> ALRIV</span></p>"],
                    solution_en: " <p><span style=\"font-family:Cambria Math\">7.(</span><span style=\"font-family:Cambria Math\">c)</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1667892833/word/media/image4.png\"/><span style=\"font-family:Cambria Math\">       </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1667892833/word/media/image5.png\"/></p> <p><span style=\"font-family:Cambria Math\">Similarly, </span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1667892833/word/media/image6.png\"/></p>",
                    solution_hi: " <p><span style=\"font-family:Cambria Math\">7.(</span><span style=\"font-family:Cambria Math\">c)</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1667892833/word/media/image4.png\"/></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1667892833/word/media/image5.png\"/></p> <p><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">उसी</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">प्रकार</span><span style=\"font-family:Cambria Math\">, </span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1667892833/word/media/image6.png\"/></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "6",
                    question_en: "<p>8. <span style=\"font-family: Cambria Math;\">Select the option that is related to the fifth term in the same way as the second term is related to the first term and fourth term related to third term.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">12 :</span><span style=\"font-family: Cambria Math;\"> 208 :: 9 : 108 :: 15 </span><span style=\"font-family: Cambria Math;\">: ?</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC CHSL 02/06/2022 (Morning)</span></p>\n",
                    question_hi: "<p>8. <span style=\"font-family: Nirmala UI;\">&#2313;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2325;&#2354;&#2381;&#2346;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2330;&#2351;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2366;&#2306;&#2330;&#2357;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2325;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2348;&#2306;&#2343;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2376;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2370;&#2360;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2361;&#2354;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2330;&#2380;&#2341;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2368;&#2360;&#2352;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2348;&#2306;&#2343;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">12:208 :</span><span style=\"font-family: Cambria Math;\">: 9:108 :: 15:?</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC CHSL 02/06/2022 (Morning)</span></p>\n",
                    options_en: ["<p>350</p>\n", "<p>360</p>\n", 
                                "<p>325</p>\n", "<p>365</p>\n"],
                    options_hi: ["<p>350</p>\n", "<p>360</p>\n",
                                "<p>325</p>\n", "<p>365</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">8.(</span><span style=\"font-family: Cambria Math;\">a)&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>L</mi><mi>o</mi><mi>g</mi><mi>i</mi><mi>c</mi><mo>&nbsp;</mo><mo>:</mo><mo>-</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mi>n</mi><mo>&nbsp;</mo><mo>:</mo><mo>&nbsp;</mo><mo>[</mo><msup><mi>n</mi><mn>2</mn></msup><mo>+</mo><msup><mrow><mo>(</mo><mfrac><mi>n</mi><mn>3</mn></mfrac><mo>)</mo></mrow><mn>3</mn></msup><mo>]</mo><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mi>I</mi><mi>n</mi><mo>&nbsp;</mo><mn>12</mn><mo>&nbsp;</mo><mo>:</mo><mo>&nbsp;</mo><mn>208</mn><mo>,</mo><mo>&nbsp;</mo><mn>12</mn><mo>&nbsp;</mo><mo>:</mo><mo>&nbsp;</mo><mo>[</mo><msup><mn>12</mn><mn>2</mn></msup><mo>+</mo><msup><mrow><mo>(</mo><mfrac><mn>12</mn><mn>3</mn></mfrac><mo>)</mo></mrow><mn>3</mn></msup><mo>]</mo><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>12</mn><mo>&nbsp;</mo><mo>:</mo><mo>&nbsp;</mo><mo>[</mo><mn>144</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><msup><mn>4</mn><mn>3</mn></msup><mo>]</mo><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mo>=</mo><mo>&nbsp;</mo><mn>12</mn><mo>&nbsp;</mo><mo>:</mo><mo>&nbsp;</mo><mo>[</mo><mn>144</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>64</mn><mo>]</mo><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>12</mn><mo>&nbsp;</mo><mo>:</mo><mo>&nbsp;</mo><mn>208</mn><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mi>I</mi><mi>n</mi><mo>&nbsp;</mo><mn>9</mn><mo>&nbsp;</mo><mo>:</mo><mo>&nbsp;</mo><mn>108</mn><mo>,</mo><mo>&nbsp;</mo><mn>9</mn><mo>&nbsp;</mo><mo>:</mo><mo>&nbsp;</mo><mo>[</mo><msup><mn>9</mn><mn>2</mn></msup><mo>+</mo><msup><mrow><mo>(</mo><mfrac><mn>9</mn><mn>3</mn></mfrac><mo>)</mo></mrow><mn>3</mn></msup><mo>]</mo><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>9</mn><mo>&nbsp;</mo><mo>:</mo><mo>&nbsp;</mo><mo>[</mo><mn>81</mn><mo>+</mo><msup><mn>3</mn><mn>3</mn></msup><mo>]</mo><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mo>=</mo><mo>&nbsp;</mo><mn>9</mn><mo>&nbsp;</mo><mo>:</mo><mo>&nbsp;</mo><mo>[</mo><mn>81</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>27</mn><mo>]</mo><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>9</mn><mo>&nbsp;</mo><mo>:</mo><mo>&nbsp;</mo><mn>108</mn><mspace linebreak=\"newline\"></mspace><mo>&nbsp;</mo><mi>S</mi><mi>i</mi><mi>m</mi><mi>i</mi><mi>l</mi><mi>a</mi><mi>r</mi><mi>l</mi><mi>y</mi><mo>,</mo><mo>&nbsp;</mo><mi>i</mi><mi>n</mi><mo>&nbsp;</mo><mn>15</mn><mo>&nbsp;</mo><mo>:</mo><mo>&nbsp;</mo><mo>(</mo><mo>?</mo><mo>)</mo><mo>,</mo><mo>&nbsp;</mo><mn>15</mn><mo>&nbsp;</mo><mo>:</mo><mo>&nbsp;</mo><mo>[</mo><msup><mn>15</mn><mn>2</mn></msup><mo>+</mo><msup><mrow><mo>(</mo><mfrac><mn>15</mn><mn>3</mn></mfrac><mo>)</mo></mrow><mn>3</mn></msup><mo>]</mo><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>15</mn><mo>&nbsp;</mo><mo>:</mo><mo>&nbsp;</mo><mo>[</mo><mn>225</mn><mo>+</mo><msup><mn>5</mn><mn>3</mn></msup><mo>]</mo><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mo>=</mo><mo>&nbsp;</mo><mn>15</mn><mo>&nbsp;</mo><mo>:</mo><mo>&nbsp;</mo><mo>[</mo><mn>225</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>125</mn><mo>]</mo><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>15</mn><mo>&nbsp;</mo><mo>:</mo><mo>&nbsp;</mo><mn>350</mn></math></span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">8.(</span><span style=\"font-family: Cambria Math;\">a)</span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>L</mi><mi>o</mi><mi>g</mi><mi>i</mi><mi>c</mi><mo>&nbsp;</mo><mo>:</mo><mo>-</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mi>n</mi><mo>&nbsp;</mo><mo>:</mo><mo>&nbsp;</mo><mo>[</mo><msup><mi>n</mi><mn>2</mn></msup><mo>+</mo><msup><mrow><mo>(</mo><mfrac><mi>n</mi><mn>3</mn></mfrac><mo>)</mo></mrow><mn>3</mn></msup><mo>]</mo><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mi>I</mi><mi>n</mi><mo>&nbsp;</mo><mn>12</mn><mo>&nbsp;</mo><mo>:</mo><mo>&nbsp;</mo><mn>208</mn><mo>,</mo><mo>&nbsp;</mo><mn>12</mn><mo>&nbsp;</mo><mo>:</mo><mo>&nbsp;</mo><mo>[</mo><msup><mn>12</mn><mn>2</mn></msup><mo>+</mo><msup><mrow><mo>(</mo><mfrac><mn>12</mn><mn>3</mn></mfrac><mo>)</mo></mrow><mn>3</mn></msup><mo>]</mo><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>12</mn><mo>&nbsp;</mo><mo>:</mo><mo>&nbsp;</mo><mo>[</mo><mn>144</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><msup><mn>4</mn><mn>3</mn></msup><mo>]</mo><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mo>=</mo><mo>&nbsp;</mo><mn>12</mn><mo>&nbsp;</mo><mo>:</mo><mo>&nbsp;</mo><mo>[</mo><mn>144</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>64</mn><mo>]</mo><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>12</mn><mo>&nbsp;</mo><mo>:</mo><mo>&nbsp;</mo><mn>208</mn><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mi>I</mi><mi>n</mi><mo>&nbsp;</mo><mn>9</mn><mo>&nbsp;</mo><mo>:</mo><mo>&nbsp;</mo><mn>108</mn><mo>,</mo><mo>&nbsp;</mo><mn>9</mn><mo>&nbsp;</mo><mo>:</mo><mo>&nbsp;</mo><mo>[</mo><msup><mn>9</mn><mn>2</mn></msup><mo>+</mo><msup><mrow><mo>(</mo><mfrac><mn>9</mn><mn>3</mn></mfrac><mo>)</mo></mrow><mn>3</mn></msup><mo>]</mo><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>9</mn><mo>&nbsp;</mo><mo>:</mo><mo>&nbsp;</mo><mo>[</mo><mn>81</mn><mo>+</mo><msup><mn>3</mn><mn>3</mn></msup><mo>]</mo><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mo>=</mo><mo>&nbsp;</mo><mn>9</mn><mo>&nbsp;</mo><mo>:</mo><mo>&nbsp;</mo><mo>[</mo><mn>81</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>27</mn><mo>]</mo><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>9</mn><mo>&nbsp;</mo><mo>:</mo><mo>&nbsp;</mo><mn>108</mn><mspace linebreak=\"newline\"></mspace><mo>&nbsp;</mo><mi>S</mi><mi>i</mi><mi>m</mi><mi>i</mi><mi>l</mi><mi>a</mi><mi>r</mi><mi>l</mi><mi>y</mi><mo>,</mo><mo>&nbsp;</mo><mi>i</mi><mi>n</mi><mo>&nbsp;</mo><mn>15</mn><mo>&nbsp;</mo><mo>:</mo><mo>&nbsp;</mo><mo>(</mo><mo>?</mo><mo>)</mo><mo>,</mo><mo>&nbsp;</mo><mn>15</mn><mo>&nbsp;</mo><mo>:</mo><mo>&nbsp;</mo><mo>[</mo><msup><mn>15</mn><mn>2</mn></msup><mo>+</mo><msup><mrow><mo>(</mo><mfrac><mn>15</mn><mn>3</mn></mfrac><mo>)</mo></mrow><mn>3</mn></msup><mo>]</mo><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>15</mn><mo>&nbsp;</mo><mo>:</mo><mo>&nbsp;</mo><mo>[</mo><mn>225</mn><mo>+</mo><msup><mn>5</mn><mn>3</mn></msup><mo>]</mo><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mo>=</mo><mo>&nbsp;</mo><mn>15</mn><mo>&nbsp;</mo><mo>:</mo><mo>&nbsp;</mo><mo>[</mo><mn>225</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>125</mn><mo>]</mo><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>15</mn><mo>&nbsp;</mo><mo>:</mo><mo>&nbsp;</mo><mn>350</mn></math></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "6",
                    question_en: "<p>9. <span style=\"font-family: Cambria Math;\">Select the option that is related to the fifth number in the same way as the second number is related to the first number and the fourth number is related to the third number.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">207 : 30 : :108 : 19 : : 63 : ?</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC CHSL 02/06/2022 (Morning)</span></p>\n",
                    question_hi: "<p>9. <span style=\"font-family: Nirmala UI;\">&#2313;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2325;&#2354;&#2381;&#2346;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2330;&#2351;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2366;&#2306;&#2330;&#2357;&#2368;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2325;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2348;&#2306;&#2343;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2376;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2370;&#2360;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2361;&#2354;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2348;&#2306;&#2343;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2330;&#2380;&#2341;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2368;&#2360;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2348;&#2306;&#2343;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">207 : 30 : :108 : 19 : : 63 : ?</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC CHSL 02/06/2022 (Morning)</span></p>\n",
                    options_en: ["<p>18</p>\n", "<p>20</p>\n", 
                                "<p>14</p>\n", "<p>11</p>\n"],
                    options_hi: ["<p>18</p>\n", "<p>20</p>\n",
                                "<p>14</p>\n", "<p>11</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">9.(</span><span style=\"font-family: Cambria Math;\">c)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Logic :-</span><span style=\"font-family: Cambria Math;\"> (n - 7) &times; 9 : n</span></p>\r\n<p><span style=\"font-weight: 400;\">In 207 : 30, </span><span style=\"font-weight: 400;\">(30-7)</span><span style=\"font-weight: 400;\"> &times; </span><span style=\"font-weight: 400;\">9 : 30 = 23 &times;</span><span style=\"font-weight: 400;\"> 9 : 30 = 207 : 30</span></p>\r\n<p><span style=\"font-weight: 400;\">In 108 : 19, </span><span style=\"font-weight: 400;\">(19-7)</span><span style=\"font-weight: 400;\"> &times;</span><span style=\"font-weight: 400;\"> 9 : </span><span style=\"font-weight: 400;\">19</span><span style=\"font-weight: 400;\"> = 12 &times;</span><span style=\"font-weight: 400;\"> 9 : </span><span style=\"font-weight: 400;\">19</span><span style=\"font-weight: 400;\"> = 108 : 19</span></p>\r\n<p><span style=\"font-weight: 400;\">Similarly, 63 : (?), </span><span style=\"font-weight: 400;\">(14-7)&times;</span><span style=\"font-weight: 400;\"> </span><span style=\"font-weight: 400;\"> 9 : 14 = 7 &times;</span><span style=\"font-weight: 400;\"> 9 : 14 = 63 : </span><strong>14</strong></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">9.(</span><span style=\"font-family: Cambria Math;\">c)</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2340;&#2352;&#2381;&#2325;</span><span style=\"font-family: Cambria Math;\"> :</span><span style=\"font-family: Cambria Math;\">-&nbsp;</span><span style=\"font-family: Cambria Math;\">(n - 7) &times; 9 : n</span></p>\r\n<p><span style=\"font-weight: 400;\">207 : 30 &#2350;&#2375;&#2306;, </span><span style=\"font-weight: 400;\">(30-7)</span><span style=\"font-weight: 400;\"> &times;</span><span style=\"font-weight: 400;\"> 9 : 30 = 23 &times;</span><span style=\"font-weight: 400;\"> 9 : 30 = 207 : 30</span></p>\r\n<p><span style=\"font-weight: 400;\">108 : 19 &#2350;&#2375;&#2306;, </span><span style=\"font-weight: 400;\">(19-7)</span><span style=\"font-weight: 400;\"> &times;</span><span style=\"font-weight: 400;\"> 9 : </span><span style=\"font-weight: 400;\">19</span><span style=\"font-weight: 400;\"> = 12 &times;</span><span style=\"font-weight: 400;\"> 9 : </span><span style=\"font-weight: 400;\">19</span><span style=\"font-weight: 400;\"> = 108 : 19</span></p>\r\n<p><span style=\"font-weight: 400;\">&nbsp;&#2313;&#2360;&#2368; &#2346;&#2381;&#2352;&#2325;&#2366;&#2352;, 63 : (?) &#2350;&#2375;&#2306;, </span><span style=\"font-weight: 400;\">(14-7)</span><span style=\"font-weight: 400;\"> &times;</span><span style=\"font-weight: 400;\"> 9 : 14 = 7 &times;</span><span style=\"font-weight: 400;\"> 9 : 14 = 63 : </span><strong>14</strong></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "6",
                    question_en: "<p>10<span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\">Select the option that is related to the fourth term in the same way as the first term is related to the second term and the fifth term is related to the sixth term</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">16 :</span><span style=\"font-family: Cambria Math;\"> 36 :: ? : </span><span style=\"font-family: Cambria Math;\">50 :</span><span style=\"font-family: Cambria Math;\">: 9 : 22</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC CHSL 02/06/2022 (Morning)</span></p>\n",
                    question_hi: "<p>10<span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2325;&#2354;&#2381;&#2346;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2330;&#2351;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2330;&#2380;&#2341;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2325;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2348;&#2306;&#2343;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2376;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2361;&#2354;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2370;&#2360;&#2352;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2348;&#2306;&#2343;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2366;&#2306;&#2330;&#2357;&#2366;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2331;&#2336;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2348;&#2306;&#2343;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">16 :</span><span style=\"font-family: Cambria Math;\"> 36 :: ? : </span><span style=\"font-family: Cambria Math;\">50 :</span><span style=\"font-family: Cambria Math;\">: 9 : 22</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC CHSL 02/06/2022 (Morning)</span></p>\n",
                    options_en: ["<p>31</p>\n", "<p>23</p>\n", 
                                "<p>34</p>\n", "<p>2<span style=\"font-family: Cambria Math;\">2</span></p>\n"],
                    options_hi: ["<p>31</p>\n", "<p>23</p>\n",
                                "<p>34</p>\n", "<p>22</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">10</span><span style=\"font-family: Cambria Math;\">.(b)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Logic :-</span><span style=\"font-family: Cambria Math;\"> n : n &times; </span><span style=\"font-family: Cambria Math;\">2 + 4</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">In </span><span style=\"font-family: Cambria Math;\">16 :</span><span style=\"font-family: Cambria Math;\"> 36, 16 : 16 &times; </span><span style=\"font-family: Cambria Math;\">2 + 4 = 16 : 32 + 4 = 16 : 36</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">In </span><span style=\"font-family: Cambria Math;\">9 :</span><span style=\"font-family: Cambria Math;\"> 22, 9 : 9 &times; </span><span style=\"font-family: Cambria Math;\">2 + 4 = 9 : 18 + 4 = 9 : 22</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Similarly, in (?</span><span style=\"font-family: Cambria Math;\">) :</span><span style=\"font-family: Cambria Math;\"> 50, 23 : 23 &times; </span><span style=\"font-family: Cambria Math;\">2 + 4 = 23 : 46 + 4 = </span><span style=\"font-family: Cambria Math;\">23</span><span style=\"font-family: Cambria Math;\"> : 50 </span></p>\n",
                    solution_hi: "<p>10<span style=\"font-family: Cambria Math;\">.(b)</span></p>\r\n<p><strong><span style=\"font-family: Nirmala UI;\">&#2340;&#2352;&#2381;&#2325;</span><span style=\"font-family: Cambria Math;\">:</span></strong><span style=\"font-family: Cambria Math;\"><strong>- </strong>n : n &#143;&times; </span><span style=\"font-family: Cambria Math;\">2 + 4</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">16 :</span><span style=\"font-family: Cambria Math;\"> 36 </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\">, 16 : 16 &times;</span><span style=\"font-family: Cambria Math;\"> 2 + 4 = 16 </span><span style=\"font-family: Cambria Math;\">: 32 + 4 = 16 : 36</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">9 :</span><span style=\"font-family: Cambria Math;\"> 22 </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\">, 9 : 9 &times;</span><span style=\"font-family: Cambria Math;\"> 2 + 4 = 9 : 18 + 4 = 9 : 22</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2325;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\">, (?) : 50 </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">23 :</span><span style=\"font-family: Cambria Math;\"> 23 &times;</span><span style=\"font-family: Cambria Math;\"> 2 + 4 = 23 : 46 + 4 = </span><span style=\"font-family: Cambria Math;\">23</span><span style=\"font-family: Cambria Math;\"> : 50 </span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "6",
                    question_en: "<p>11<span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\">Select the option that is related to the third number in the same way as the second number is related to the firs</span><span style=\"font-family: Cambria Math;\">t number and the sixth number is related to the fifth number.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">21 :</span><span style=\"font-family: Cambria Math;\"> 54 :: 24 : ? :: </span><span style=\"font-family: Cambria Math;\">29 :</span><span style=\"font-family: Cambria Math;\"> 78</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC CHSL 02/06/2022 (Morning)</span></p>\n",
                    question_hi: "<p>11<span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2325;&#2354;&#2381;&#2346;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2330;&#2351;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;&#2332;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2368;&#2360;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2325;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2348;&#2306;&#2343;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2376;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2370;&#2360;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2361;&#2354;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2348;&#2306;&#2343;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2331;&#2336;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2366;&#2306;&#2330;&#2357;&#2368;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2348;&#2306;&#2343;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">21 :</span><span style=\"font-family: Cambria Math;\"> 54 :: 24 : ? :: </span><span style=\"font-family: Cambria Math;\">29 :</span><span style=\"font-family: Cambria Math;\"> 78</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC CHSL 02/06/2022 (Morning)</span></p>\n",
                    options_en: ["<p>56</p>\n", "<p>63</p>\n", 
                                "<p>30</p>\n", "<p>49</p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\n"],
                    options_hi: ["<p>56</p>\n", "<p>63</p>\n",
                                "<p>30</p>\n", "<p>49</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">11</span><span style=\"font-family: Cambria Math;\">.(b)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Logic :-</span><span style=\"font-family: Cambria Math;\"> n : [n &times; </span><span style=\"font-family: Cambria Math;\">2 + (n - 9)]</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">In </span><span style=\"font-family: Cambria Math;\">21 :</span><span style=\"font-family: Cambria Math;\"> 54, 21 : [21 &times;</span><span style=\"font-family: Cambria Math;\">&nbsp;2 + (21 - 9)] = 21 : [42 + 12] = 21 : 54</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">In </span><span style=\"font-family: Cambria Math;\">29 :</span><span style=\"font-family: Cambria Math;\"> 78, 29 : [29 &times;</span><span style=\"font-family: Cambria Math;\">&nbsp;2 + (29 - 9)] = 29 : [58 + 20] = 29 : 78</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Similarly, in </span><span style=\"font-family: Cambria Math;\">24 :</span><span style=\"font-family: Cambria Math;\"> (?), 24 : [24 &times;</span><span style=\"font-family: Cambria Math;\"> 2 + (24 - 9)] = 24 : [48 + 15] = 24 : </span><span style=\"font-family: Cambria Math;\">63</span></p>\n",
                    solution_hi: "<p>11<span style=\"font-family: Cambria Math;\">.(b)</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2340;&#2352;&#2381;&#2325;</span><span style=\"font-family: Cambria Math;\"> :</span><span style=\"font-family: Cambria Math;\">- n : [n &times; </span><span style=\"font-family: Cambria Math;\">2 + (n - 9)]</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">21 :</span><span style=\"font-family: Cambria Math;\"> 54 </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\">, 21 : [21 &times;</span><span style=\"font-family: Cambria Math;\"> 2 + (21 - 9)] = 21 : [42 + 12] = 21 : 54</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">29 :</span><span style=\"font-family: Cambria Math;\"> 78 </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\">, 29 : [29 &times;</span><span style=\"font-family: Cambria Math;\"> 2 + (29 - 9)] = 29 : [58 + 20] = 29 : 78</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2313;&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2325;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\">, 24 : (?) </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\">, 24 : [24 &times;</span><span style=\"font-family: Cambria Math;\">&nbsp;2 + (24 - 9)] = 24 : [48 + 15] = 24 : </span><span style=\"font-family: Cambria Math;\">63</span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "6",
                    question_en: "<p>12<span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\">Select the set in which the numbers are related in the same way as are the numbers of the following set.</span></p>\r\n<p><span style=\"font-weight: 400;\">(37, 28, 333)</span></p>\r\n<p><span style=\"font-weight: 400;\">(41, 39, 82)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">NOTE :</span><span style=\"font-family: Cambria Math;\"> Operations should be performed on the whole numbers, without breaking </span><span style=\"font-family: Cambria Math;\">down</span><span style=\"font-family: Cambria Math;\"> the numbers into its constituent digits. </span><span style=\"font-family: Cambria Math;\">E.g. 13 &ndash; Operations on 13 such as adding /deleting /multiplying etc. to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is NOT allowed)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC CHSL 02/06/2022 (Morning)</span></p>\n",
                    question_hi: "<p>12<span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2350;&#2369;&#2330;&#2381;&#2330;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2330;&#2351;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2367;&#2360;&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;&#2319;&#2305;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2325;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2348;&#2306;&#2343;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2376;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2350;&#2369;&#2330;&#2381;&#2330;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;&#2319;&#2305;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2348;&#2306;&#2343;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2306;&#2404;</span></p>\r\n<p><span style=\"font-weight: 400;\">(37, 28 , 333)</span></p>\r\n<p><span style=\"font-weight: 400;\">(41, 39 , 82)</span></p>\r\n<p><span style=\"font-weight: 400;\">(&#2344;&#2379;&#2335;: &#2360;&#2306;&#2326;&#2381;&#2351;&#2366; &#2325;&#2379; &#2313;&#2360;&#2325;&#2375; &#2328;&#2335;&#2325; &#2309;&#2306;&#2325;&#2379;&#2306; &#2350;&#2375;&#2306; &#2357;&#2367;&#2349;&#2366;&#2332;&#2367;&#2340; &#2325;&#2367;&#2319; &#2348;&#2367;&#2344;&#2366;, &#2346;&#2370;&#2352;&#2381;&#2339; &#2360;&#2306;&#2326;&#2381;&#2351;&#2366;&#2323;&#2306; &#2346;&#2352; &#2327;&#2339;&#2367;&#2340;&#2368;&#2351; &#2360;&#2306;&#2325;&#2381;&#2352;&#2367;&#2351;&#2366;&#2319; &#2325;&#2367;&#2351;&#2366; &#2332;&#2366;&#2344;&#2366; &#2330;&#2366;&#2361;&#2367;&#2319;&#2404; &#2313;&#2342;&#2366;&#2361;&#2352;&#2339; &#2325;&#2375; &#2354;&#2367;&#2319;, 13 - 13 &#2346;&#2352; &#2327;&#2339;&#2367;&#2340;&#2368;&#2351; &#2360;&#2306;&#2325;&#2381;&#2352;&#2367;&#2351;&#2366;&#2319; &#2332;&#2376;&#2360;&#2375; &#2325;&#2367; &#2332;&#2379;&#2337;&#2364;&#2344;&#2375;/&#2328;&#2335;&#2366;&#2344;&#2375;/&#2327;&#2369;&#2339;&#2366; &#2325;&#2352;&#2344;&#2375; &#2310;&#2342;&#2367; &#2346;&#2352; 13 &#2350;&#2375;&#2306; &#2325;&#2367;&#2351;&#2366; &#2332;&#2366; &#2360;&#2325;&#2340;&#2366; &#2361;&#2376;&#2404; 13 &#2325;&#2379; 1 &#2324;&#2352; 3 &#2350;&#2375;&#2306; &#2340;&#2379;&#2337;&#2364;&#2325;&#2352; &#2324;&#2352; &#2347;&#2367;&#2352; 1 &#2324;&#2352; 3 &#2346;&#2352; &#2327;&#2339;&#2367;&#2340;&#2368;&#2351; &#2360;&#2306;&#2325;&#2381;&#2352;&#2367;&#2351;&#2366;&#2323;&#2306; &#2325;&#2379; &#2325;&#2352;&#2344;&#2375; &#2325;&#2368; &#2309;&#2344;&#2369;&#2350;&#2340;&#2367; &#2344;&#2361;&#2368;&#2306; &#2361;&#2376;&#2404;)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC CHSL 02/06/2022 (Morning)</span></p>\n",
                    options_en: ["<p><span style=\"font-weight: 400;\">(18, 13, 90)</span></p>\n", "<p><span style=\"font-weight: 400;\">(17, 14, 67)</span></p>\n", 
                                "<p><span style=\"font-weight: 400;\">(16, 12, 54)</span></p>\n", "<p><span style=\"font-weight: 400;\">(19, 15, 44)</span></p>\n"],
                    options_hi: ["<p><span style=\"font-weight: 400;\">(18, 13, 90)</span></p>\n", "<p><span style=\"font-weight: 400;\">&nbsp;(17, 14, 67)</span></p>\n",
                                "<p><span style=\"font-weight: 400;\">(16, 12, 54)</span></p>\n", "<p><span style=\"font-weight: 400;\">(19, 15, 44)</span></p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">12</span><span style=\"font-family: Cambria Math;\">.(a)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Logic :-</span><span style=\"font-family: Cambria Math;\"> [a, (a - b), a &times; </span><span style=\"font-family: Cambria Math;\">b] </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">In (37, 28, 333), [37, (37 - 9), 37 &times;</span><span style=\"font-family: Cambria Math;\"> 9] = [37, 28, 333]</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">In (41, 39, 82), [41, (41 - 2), 41&times;&nbsp;</span><span style=\"font-family: Cambria Math;\"> 2] = [41, 39, 82] </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Similarly, in (18, 13, 90), [18, (18 - 5), 18 &times;</span><span style=\"font-family: Cambria Math;\"> 5] = [18, 13, 90]</span></p>\n",
                    solution_hi: "<p>12<span style=\"font-family: Cambria Math;\">.(a)</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2340;&#2352;&#2381;&#2325;</span><span style=\"font-family: Cambria Math;\"> :</span><span style=\"font-family: Cambria Math;\">- [a, (a - b), a &times; </span><span style=\"font-family: Cambria Math;\">b] </span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\">, [37, (37 - 9), 37 &times;</span><span style=\"font-family: Cambria Math;\"> 9] = [37, 28, 333]</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\">, [41, (41 - 2), </span><span style=\"font-family: Cambria Math;\">41 &times;</span><span style=\"font-family: Cambria Math;\"> 2] = [41, 39, 82] </span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2311;&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2352;&#2361;</span><span style=\"font-family: Cambria Math;\">, (18, 13, 90) </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\">, [18, (18 - 5), 18 &times;</span><span style=\"font-family: Cambria Math;\"> 5] = [18, 13, 90]</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "6",
                    question_en: "<p>13<span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\">Select the option that is related to the fifth number in the same way as the second number is related to the first number and the fourth number is related to t</span><span style=\"font-family: Cambria Math;\">he third number.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">6 :</span><span style=\"font-family: Cambria Math;\"> 96 :: 8 : 144 :: 5 : ?</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC CHSL 02/06/2022 (Afternoon)</span></p>\n",
                    question_hi: "<p>13<span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2325;&#2354;&#2381;&#2346;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2330;&#2351;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2366;&#2306;&#2330;&#2357;&#2368;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2325;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2348;&#2306;&#2343;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2376;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2370;&#2360;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2361;&#2354;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2348;&#2306;&#2343;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2330;&#2380;&#2341;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2368;&#2360;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2348;&#2306;&#2343;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">6 :</span><span style=\"font-family: Cambria Math;\"> 96 :: 8 : 144 :: 5 : ?</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC CHSL 02/06/2022 (Afternoon)</span></p>\n",
                    options_en: ["<p>70</p>\n", "<p>90</p>\n", 
                                "<p>85</p>\n", "<p>75</p>\n"],
                    options_hi: ["<p>70</p>\n", "<p>90</p>\n",
                                "<p>85</p>\n", "<p>75</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">13</span><span style=\"font-family: Cambria Math;\">.(d)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Logic :-</span><span style=\"font-family: Cambria Math;\"> n : n &times; </span><span style=\"font-family: Cambria Math;\">(n + 10) </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">In </span><span style=\"font-family: Cambria Math;\">6 :</span><span style=\"font-family: Cambria Math;\"> 96, 6 : 6 &times;</span><span style=\"font-family: Cambria Math;\"> (6 + 10) = 6 : 6 &times;</span><span style=\"font-family: Cambria Math;\"> 16 = 6 : 96</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">In </span><span style=\"font-family: Cambria Math;\">8 :</span><span style=\"font-family: Cambria Math;\"> 144, 8 : 8 &times;</span><span style=\"font-family: Cambria Math;\"> (8 + 10) = 8 : 8 &times;</span><span style=\"font-family: Cambria Math;\"> 18 = 8 : 144</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">In </span><span style=\"font-family: Cambria Math;\">5 :</span><span style=\"font-family: Cambria Math;\"> (?), 5 : 5 &times;</span><span style=\"font-family: Cambria Math;\"> (5 + 10) = 5 : 5 &times;</span><span style=\"font-family: Cambria Math;\"> 15 = 5 : </span><span style=\"font-family: Cambria Math;\">75</span></p>\n",
                    solution_hi: "<p>13<span style=\"font-family: Cambria Math;\">.(d)</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2340;&#2352;&#2381;&#2325;</span><span style=\"font-family: Cambria Math;\"> :</span><span style=\"font-family: Cambria Math;\">- n : n &times;</span><span style=\"font-family: Cambria Math;\"> (n + 10) </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">6 :</span><span style=\"font-family: Cambria Math;\"> 96 </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\">, 6 : 6 &times;</span><span style=\"font-family: Cambria Math;\"> (6 + 10) = 6 : 6 &times;</span><span style=\"font-family: Cambria Math;\"> 16 = 6 : 96</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">8 :</span><span style=\"font-family: Cambria Math;\"> 144 </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\">, 8 : 8 &times;</span><span style=\"font-family: Cambria Math;\"> (8 + 10) = 8 : 8 &times;</span><span style=\"font-family: Cambria Math;\"> 18 = 8 : 144</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">5 :</span><span style=\"font-family: Cambria Math;\"> (?) </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\">, 5 : 5 &times;</span><span style=\"font-family: Cambria Math;\"> (5 + 10) = 5 : 5 &times;</span><span style=\"font-family: Cambria Math;\"> 15 = 5 : </span><span style=\"font-family: Cambria Math;\">75</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "6",
                    question_en: "<p>14<span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\">Select the option that is related to the third term in the same way as the second term is related to the first term and the sixth term is related to the fifth term.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">19 :</span><span style=\"font-family: Cambria Math;\"> 90 :: 25 : ? :: </span><span style=\"font-family: Cambria Math;\">13 :</span><span style=\"font-family: Cambria Math;\"> 60</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC CHSL 02/06/2022 (Afternoon)</span></p>\n",
                    question_hi: "<p>14<span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2325;&#2354;&#2381;&#2346;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2330;&#2351;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2368;&#2360;&#2352;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2325;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2348;&#2306;&#2343;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2376;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2370;&#2360;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2361;&#2354;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2331;&#2336;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2366;&#2306;&#2330;&#2357;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2348;&#2306;&#2343;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">19 :</span><span style=\"font-family: Cambria Math;\"> 90 :: 25 : ? :: </span><span style=\"font-family: Cambria Math;\">13 :</span><span style=\"font-family: Cambria Math;\"> 60</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC CHSL 02/06/2022 (Afternoon)</span></p>\n",
                    options_en: ["<p>120</p>\n", "<p>100</p>\n", 
                                "<p>115</p>\n", "<p>110</p>\n"],
                    options_hi: ["<p>120</p>\n", "<p>100</p>\n",
                                "<p>115</p>\n", "<p>110</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">14</span><span style=\"font-family: Cambria Math;\">.(a</span><span style=\"font-family: Cambria Math;\">)</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Logic :-</span></strong><span style=\"font-family: Cambria Math;\"> n : n &times;</span><span style=\"font-family: Cambria Math;\"> 5 - 5</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">In </span><span style=\"font-family: Cambria Math;\">19 :</span><span style=\"font-family: Cambria Math;\"> 90, 19 : 19 &times; </span><span style=\"font-family: Cambria Math;\">5 - 5 = 19 : 95 - 5 = 19 : 90</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">In </span><span style=\"font-family: Cambria Math;\">13 :</span><span style=\"font-family: Cambria Math;\"> 60, 13 : 13&times; </span><span style=\"font-family: Cambria Math;\">5 - 5 = 13 : 65 - 5 = 13 : 60</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">In </span><span style=\"font-family: Cambria Math;\">25 :</span><span style=\"font-family: Cambria Math;\"> (?), 25 : 25 &times; </span><span style=\"font-family: Cambria Math;\">5 - 5 = 25 : 125 - 5 = 25 : </span><span style=\"font-family: Cambria Math;\">120</span></p>\n",
                    solution_hi: "<p>14<span style=\"font-family: Cambria Math;\">.(a)</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2340;&#2352;&#2381;&#2325;</span><span style=\"font-family: Cambria Math;\"> :</span><span style=\"font-family: Cambria Math;\">- n : n &times; </span><span style=\"font-family: Cambria Math;\">5 - 5</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">19 :</span><span style=\"font-family: Cambria Math;\"> 90 </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\">, 19 : 19 &times;</span><span style=\"font-family: Cambria Math;\"> 5 - 5 = 19 : 95 - 5 = 19 : 90</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">13 :</span><span style=\"font-family: Cambria Math;\"> 60 </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\">, 13 : 13 &times;</span><span style=\"font-family: Cambria Math;\"> 5 - 5 = 13 : 65 - 5 = 13 : 60</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">25</span><span style=\"font-family: Cambria Math;\"> :</span><span style=\"font-family: Cambria Math;\"> (?) </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\">, 25 : 25 &times; </span><span style=\"font-family: Cambria Math;\">5 - 5 = 25 : 125 - 5 = 25 : </span><span style=\"font-family: Cambria Math;\">120</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "misc",
                    question_en: " <p>15</span><span style=\"font-family:Cambria Math\">.</span><span style=\"font-family:Cambria Math\"> Select the option that is related to the third word in the same way as the second </span><span style=\"font-family:Cambria Math\">word  is</span><span style=\"font-family:Cambria Math\"> related to the first word. (The words must be considered as meaningful English words and must NOT be related to each other based on the number of letters/number of </span><span style=\"font-family:Cambria Math\">consonants/vowels in the word)</span></p> <p><span style=\"font-family:Cambria Math\">Golf :</span><span style=\"font-family:Cambria Math\"> Stick :: Cricket : ?</span></p> <p><span style=\"font-family:Cambria Math\">SSC CHSL 02/06/2022 (Afternoon)</span></p>",
                    question_hi: " <p>15</span><span style=\"font-family:Cambria Math\">.</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">उस</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">विकल्प</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">का</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">चयन</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">करें</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">जो</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">तीसरे</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">शब्द</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">से</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">उसी</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">प्रकार</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">संबंधित</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">है</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">जैसे</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">दूसरा</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">शब्द</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">पहले</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">शब्द</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">से</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">संबंधित</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">है।</span><span style=\"font-family:Cambria Math\"> (</span><span style=\"font-family:Nirmala UI\">शब्दो</span><span style=\"font-family:Nirmala UI\">ं</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">को</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">अर्थपूर्ण</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">अंग्रेजी</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">शब्दों</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">रूप</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">माना</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">जाना</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">चाहिए</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">और</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">शब्द</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">अक्षरों</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">की</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">संख्या</span><span style=\"font-family:Cambria Math\">/</span><span style=\"font-family:Nirmala UI\">व्यंजनों</span><span style=\"font-family:Cambria Math\">/</span><span style=\"font-family:Nirmala UI\">स्वरों</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">की</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">संख्या</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">आधार</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">पर</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">एक</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">दूसरे</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">से</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">संबंधित</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">नहीं</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">किया</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">जाना</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">चाहिए।</span><span style=\"font-family:Cambria Math\">)</span></p> <p><span style=\"font-family:Nirmala UI\">गोल्फ</span><span style=\"font-family:Cambria Math\"> : </span><span style=\"font-family:Nirmala UI\">स्टिक</span><span style=\"font-family:Cambria Math\"> :: </span><span style=\"font-family:Nirmala UI\">क्रिकेट</span><span style=\"font-family:Cambria Math\"> : ?</span></p> <p><span style=\"font-family:Cambria Math\">SSC CHSL 02/06/2022 (Afternoon)</span></p>",
                    options_en: [" <p> Bat</span></p>", " <p> Stadium</span></p>", 
                                " <p> Ball</span></p>", " <p> Wicket</span></p>"],
                    options_hi: [" <p> </span><span style=\"font-family:Nirmala UI\">बल्ला</span></p>", " <p> </span><span style=\"font-family:Nirmala UI\">स्टेडियम</span></p>",
                                " <p> </span><span style=\"font-family:Nirmala UI\">गेंद</span><span style=\"font-family:Cambria Math\"> </span></p>", " <p> </span><span style=\"font-family:Nirmala UI\">विकेट</span></p>"],
                    solution_en: " <p><span style=\"font-family:Cambria Math\">15</span><span style=\"font-family:Cambria Math\">.(a)</span></p> <p><span style=\"font-family:Cambria Math\">Golf is played with Stick. Similarly, Cricket is played with Bat.</span></p>",
                    solution_hi: " <p>15</span><span style=\"font-family:Cambria Math\">.(a)</span></p> <p><span style=\"font-family:Nirmala UI\">गोल्फ</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">स्टिक</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">से</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">खेला</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">जाता</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">है।</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">इसी</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">तरह</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">क्रिकेट</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">बल्ला</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">से</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">खेला</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">जाता</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">है।</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>