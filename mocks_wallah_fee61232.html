<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">6:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 15</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">15</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 6 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["7"] = {
                name: "English",
                start: 0,
                end: 13
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="7">English</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 14,
                end: 14
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "7",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">1. </span><span style=\"font-family: Cambria Math;\">Choose the word that is opposite in meaning to the given word. </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Frustrate</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">1. </span><span style=\"font-family: Cambria Math;\">Choose the word that is opposite in meaning to the given word. </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Frustrate</span></p>\n",
                    options_en: ["<p>Circumvent<span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p>Foster</p>\n", 
                                "<p>Irk</p>\n", "<p>Fetter</p>\n"],
                    options_hi: ["<p>Circumvent<span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p>Foster</p>\n",
                                "<p>Irk</p>\n", "<p>Fetter</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">1.(</span><span style=\"font-family: Cambria Math;\">b) </span><span style=\"font-family: Cambria Math;\">Foster</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Frustrate-</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">a feeling of anger because you cannot get what you want</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Circumvent - to find a clever way of avoiding a difficulty or rule</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Irk- to irritate or annoy somebody</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Fetter - a chain or manacle used to restrain a prisoner, typically placed around the ankles.</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">1.(</span><span style=\"font-family: Cambria Math;\">b) </span><span style=\"font-family: Cambria Math;\">Foster</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Frustrate (</span><span style=\"font-family: Kokila;\">&#2361;&#2340;&#2366;&#2358;</span><span style=\"font-family: Cambria Math;\">) -</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">a feeling of anger because you cannot get what you want</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Circumvent (</span><span style=\"font-family: Kokila;\">&#2342;&#2352;&#2325;&#2367;&#2344;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\">)- to find a clever way of avoiding a difficulty or rule</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Irk (</span><span style=\"font-family: Kokila;\">&#2360;&#2340;&#2366;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> )</span><span style=\"font-family: Cambria Math;\">-to irritate or annoy somebody</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Fetter (</span><span style=\"font-family: Kokila;\">&#2348;&#2375;&#2337;&#2364;&#2368;</span><span style=\"font-family: Cambria Math;\">)- a chain or manacle used to restrain a prisoner, typically placed around the ankles</span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "7",
                    question_en: "<p>2. <span style=\"font-family: Cambria Math;\">Choose the word that is opposite in meaning to the given word. </span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> Accessory </span></p>\n",
                    question_hi: "<p>2. <span style=\"font-family: Cambria Math;\">Choose the word that is opposite in meaning to the given word. </span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> Accessory </span></p>\n",
                    options_en: ["<p>Substitute<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p>Surplus</p>\n", 
                                "<p>Essential</p>\n", "<p>Option</p>\n"],
                    options_hi: ["<p>Substitute<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p>Surplus</p>\n",
                                "<p>Essential</p>\n", "<p>Option</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">2.(</span><span style=\"font-family: Cambria Math;\">c) </span><span style=\"font-family: Cambria Math;\">Essential</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Accessory - </span><span style=\"font-family: Cambria Math;\">something that can be added to a thing in order to make it more useful, versatile, or attractive</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Substitute - </span><span style=\"font-family: Cambria Math;\">use or add in place of</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Surplus - more than needed and used</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Option - to choose from a set of possibilities</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">2.(</span><span style=\"font-family: Cambria Math;\">c)</span><span style=\"font-family: Cambria Math;\">Essential</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Accessory (</span><span style=\"font-family: Kokila;\">&#2360;&#2361;&#2366;&#2351;&#2325;</span><span style=\"font-family: Cambria Math;\">) - </span><span style=\"font-family: Cambria Math;\">something that can be added to a thing in order to make it more useful, versatile, or attractive</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Substitute (</span><span style=\"font-family: Kokila;\">&#2325;&#2367;&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2381;&#2341;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\">) - </span><span style=\"font-family: Cambria Math;\">use or add in place of</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Surplus (</span><span style=\"font-family: Kokila;\">&#2310;&#2343;&#2367;&#2325;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> )</span><span style=\"font-family: Cambria Math;\">- more than needed and used</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Option (</span><span style=\"font-family: Kokila;\">&#2357;&#2367;&#2325;&#2354;&#2381;&#2346;</span><span style=\"font-family: Cambria Math;\">) - to choose from a set of possibilities</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "7",
                    question_en: "<p>3. <span style=\"font-family: Cambria Math;\"> Choose the word that is opposite in meaning to the given word. </span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Amiss</span></strong></p>\n",
                    question_hi: "<p>3. <span style=\"font-family: Cambria Math;\">Choose the word that is opposite in meaning to the given word. </span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Amiss</span></strong></p>\n",
                    options_en: ["<p>Impeccable</p>\n", "<p>Marred<span style=\"font-family: Cambria Math;\"> </span></p>\n", 
                                "<p>Gaily</p>\n", "<p><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">Vitiated</span></p>\n"],
                    options_hi: ["<p>Impeccable</p>\n", "<p>Marred<span style=\"font-family: Cambria Math;\"> </span></p>\n",
                                "<p>Gaily</p>\n", "<p><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">Vitiated</span></p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">3.(</span><span style=\"font-family: Cambria Math;\">a) </span><span style=\"font-family: Cambria Math;\">Impeccable</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Amiss - </span><span style=\"font-family: Cambria Math;\">wrong, not suitable, or not as expected</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Marred </span><span style=\"font-family: Cambria Math;\">- </span><span style=\"font-family: Cambria Math;\">to</span><span style=\"font-family: Cambria Math;\"> harm or damage something good</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Gaily </span><span style=\"font-family: Cambria Math;\">- </span><span style=\"font-family: Cambria Math;\">happily</span><span style=\"font-family: Cambria Math;\"> , cheerfully</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Vitiated - </span><span style=\"font-family: Cambria Math;\">to make something less effective</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">3.(</span><span style=\"font-family: Cambria Math;\">a) </span><span style=\"font-family: Cambria Math;\">Impeccable</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Amiss (</span><span style=\"font-family: Kokila;\">&#2309;&#2344;&#2369;&#2330;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\">)</span><span style=\"font-family: Cambria Math;\">- </span><span style=\"font-family: Cambria Math;\">wrong, not suitable, or not as expected</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Marred(</span><span style=\"font-family: Kokila;\">&#2348;&#2367;&#2327;&#2366;&#2396;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\">) -</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">to harm or damage something good</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Gaily (</span><span style=\"font-family: Kokila;\">&#2360;&#2369;&#2326;&#2346;&#2370;&#2352;&#2381;&#2357;&#2325;</span><span style=\"font-family: Cambria Math;\">)</span><span style=\"font-family: Cambria Math;\">- </span><span style=\"font-family: Cambria Math;\">happily</span><span style=\"font-family: Cambria Math;\"> , cheerfully</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Vitiated (</span><span style=\"font-family: Kokila;\">&#2344;&#2369;&#2325;&#2364;&#2360;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2361;&#2369;&#2306;&#2330;&#2366;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\">)</span><span style=\"font-family: Cambria Math;\"> - </span><span style=\"font-family: Cambria Math;\">to make something less effective</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "7",
                    question_en: "<p>4. <span style=\"font-family: Cambria Math;\">Choose the word that is opposite in meaning to the given word. </span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Worried</span></strong><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                    question_hi: "<p>4. <span style=\"font-family: Cambria Math;\">Choose the word that is opposite in meaning to the given word. </span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Worried</span></strong><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                    options_en: ["<p>Leisure</p>\n", "<p>Withdraw</p>\n", 
                                "<p>Proceed</p>\n", "<p>Unconcerned</p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\n"],
                    options_hi: ["<p>Leisure</p>\n", "<p>Withdraw</p>\n",
                                "<p>Proceed</p>\n", "<p>Unconcerned</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">4.(</span><span style=\"font-family: Cambria Math;\">d)</span><span style=\"font-family: Cambria Math;\"> Unconcerned</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Unconcerned - Not to think about anything.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Leisure - Free time </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Withdraw - </span><span style=\"font-family: Cambria Math;\">to move or order somebody to move back or away from a place</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Proceed- </span><span style=\"font-family: Cambria Math;\">to continue doing something</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">4.(</span><span style=\"font-family: Cambria Math;\">d)</span><span style=\"font-family: Cambria Math;\"> Unconcerned</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Unconcerned (</span><span style=\"font-family: Kokila;\">&#2313;&#2342;&#2366;&#2360;&#2368;&#2344;</span><span style=\"font-family: Cambria Math;\"> )</span><span style=\"font-family: Cambria Math;\">- Not to think about anything.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Leisure (</span><span style=\"font-family: Kokila;\">&#2310;&#2352;&#2366;&#2350;</span><span style=\"font-family: Cambria Math;\">)- Free time </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Withdraw (</span><span style=\"font-family: Kokila;\">&#2344;&#2367;&#2325;&#2366;&#2354;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\">) - </span><span style=\"font-family: Cambria Math;\">to move or order somebody to move back or away from a place</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Proceed (</span><span style=\"font-family: Kokila;\">&#2310;&#2327;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2348;&#2338;&#2364;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\">)- </span><span style=\"font-family: Cambria Math;\">to continue doing something</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "7",
                    question_en: "<p>5. <span style=\"font-family: Cambria Math;\">Choose the word that is opposite in meaning to the given word. </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Despondent</span></p>\n",
                    question_hi: " <p>5. </span><span style=\"font-family:Cambria Math\">Choose the word that is opposite in meaning to the given word. </span></p> <p><span style=\"font-family:Cambria Math\">Despondent</span></p>",
                    options_en: ["<p>Precise</p>\n", "<p>Despaired</p>\n", 
                                "<p>Reproach</p>\n", "<p>Hopeful</p>\n"],
                    options_hi: [" <p> Precise </span></p>", " <p> Despaired </span></p>",
                                " <p> Reproach  </span></p>", " <p> Hopeful </span></p>"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">5.(</span><span style=\"font-family: Cambria Math;\">d) </span><span style=\"font-family: Cambria Math;\">Hopeful</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Despondent - </span><span style=\"font-family: Cambria Math;\">a deep dejection arising from a conviction of the uselessness of further effort.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Precise </span><span style=\"font-family: Cambria Math;\">-</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">exactly</span><span style=\"font-family: Cambria Math;\"> or sharply defined or stated</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Despaired - </span><span style=\"font-family: Cambria Math;\">to lose all hope that something will happen</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Reproach - </span><span style=\"font-family: Cambria Math;\">an expression of rebuke or disapproval</span></p>\n",
                    solution_hi: " <p><span style=\"font-family:Cambria Math\">5.(</span><span style=\"font-family:Cambria Math\">d) </span><span style=\"font-family:Cambria Math\">Hopeful</span></p> <p><span style=\"font-family:Cambria Math\">Despondent</span><span style=\"font-family:Cambria Math\"> (</span><span style=\"font-family:Kokila\">हताश</span><span style=\"font-family:Cambria Math\">) </span><span style=\"font-family:Cambria Math\">-</span><span style=\"font-family:Cambria Math\">a deep dejection arising from a conviction of the uselessness of further effort.</span></p> <p><span style=\"font-family:Cambria Math\">Precise (</span><span style=\"font-family:Kokila\">स्पष्ट</span><span style=\"font-family:Cambria Math\">) </span><span style=\"font-family:Cambria Math\">-</span><span style=\"font-family:Cambria Math\">exactly or sharply defined or stated</span></p> <p><span style=\"font-family:Cambria Math\">Despaired (</span><span style=\"font-family:Kokila\">निराश</span><span style=\"font-family:Cambria Math\">)- </span><span style=\"font-family:Cambria Math\">to lose all hope that something will happen</span></p> <p><span style=\"font-family:Cambria Math\">Reproach(</span><span style=\"font-family:Kokila\">डाँटना</span><span style=\"font-family:Cambria Math\">)- </span><span style=\"font-family:Cambria Math\">an expression of rebuke or disapproval</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "7",
                    question_en: " <p>6.</span><span style=\"font-family:Cambria Math\"> Choose the word that is opposite in meaning to the given word.</span></p> <p><span style=\"font-family:Cambria Math\"> Nadir</span></p>",
                    question_hi: " <p>6.</span><span style=\"font-family:Cambria Math\"> Choose the word that is opposite in meaning to the given word.</span></p> <p><span style=\"font-family:Cambria Math\"> Nadir</span></p>",
                    options_en: [" <p>  Climax</span></p>", " <p>  Base</span></p>", 
                                " <p>  Bottom</span></p>", " <p>  Docile</span></p>"],
                    options_hi: [" <p>  Climax</span></p>", " <p>  Base</span></p>",
                                " <p>  Bottom</span></p>", " <p>  Docile</span></p>"],
                    solution_en: " <p><span style=\"font-family:Cambria Math\">6.(</span><span style=\"font-family:Cambria Math\">a) </span><span style=\"font-family:Cambria Math\">Climax</span></p> <p><span style=\"font-family:Cambria Math\">Nadir - of lowest level OR the point of least achievement</span></p> <p><span style=\"font-family:Cambria Math\">Base - </span><span style=\"font-family:Cambria Math\">foundation or starting point for something</span></p> <p><span style=\"font-family:Cambria Math\">Bottom - in the lowest position</span></p> <p><span style=\"font-family:Cambria Math\">Docile - </span><span style=\"font-family:Cambria Math\">quiet and easy to influence, persuade or control</span></p>",
                    solution_hi: " <p><span style=\"font-family:Cambria Math\">6.(</span><span style=\"font-family:Cambria Math\">a) </span><span style=\"font-family:Cambria Math\">Climax</span></p> <p><span style=\"font-family:Cambria Math\">Nadir (</span><span style=\"font-family:Kokila\">पतन</span><span style=\"font-family:Cambria Math\">)- of lowest level OR the point of least achievement</span></p> <p><span style=\"font-family:Cambria Math\">Base (</span><span style=\"font-family:Kokila\">आधार</span><span style=\"font-family:Cambria Math\">)- </span><span style=\"font-family:Cambria Math\">foundation or starting point for something</span></p> <p><span style=\"font-family:Cambria Math\">Bottom (</span><span style=\"font-family:Kokila\">नीचे</span><span style=\"font-family:Cambria Math\">)  -</span><span style=\"font-family:Cambria Math\"> in the lowest position</span></p> <p><span style=\"font-family:Cambria Math\">Docile (</span><span style=\"font-family:Kokila\">विनम्र</span><span style=\"font-family:Cambria Math\">)- </span><span style=\"font-family:Cambria Math\">quiet and easy to influence, persuade or control</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "7",
                    question_en: " <p>7</span><span style=\"font-family:Cambria Math\">. Choose the word that is opposite in meaning to the given word. </span></p> <p><span style=\"font-family:Cambria Math\">Knack</span></p>",
                    question_hi: " <p>7</span><span style=\"font-family:Cambria Math\">. Choose the word that is opposite in meaning to the given word. </span></p> <p><span style=\"font-family:Cambria Math\">Knack</span></p>",
                    options_en: [" <p>  Vile</span></p>", " <p>  Process</span></p>", 
                                " <p>  Lesson</span></p>", " <p>  Inability</span><span style=\"font-family:Cambria Math\"> </span></p>"],
                    options_hi: [" <p>  Vile</span></p>", " <p>  Process</span></p>",
                                " <p>  Lesson</span></p>", " <p>  Inability</span><span style=\"font-family:Cambria Math\"> </span></p>"],
                    solution_en: " <p><span style=\"font-family:Cambria Math\">7.(</span><span style=\"font-family:Cambria Math\">d) </span><span style=\"font-family:Cambria Math\">Inability</span></p> <p><span style=\"font-family:Cambria Math\">Knack - </span><span style=\"font-family:Cambria Math\">a special skill, talent, or aptitude making for ease and dexterity</span></p> <p><span style=\"font-family:Cambria Math\">Vile </span><span style=\"font-family:Cambria Math\">-  A</span><span style=\"font-family:Cambria Math\"> little worth or value or morally despicable</span></p> <p><span style=\"font-family:Cambria Math\">Process - </span><span style=\"font-family:Cambria Math\">something we do in order to achieve a certain result</span></p> <p><span style=\"font-family:Cambria Math\">Lesson - A piece of instruction or something to be learned</span></p>",
                    solution_hi: " <p><span style=\"font-family:Cambria Math\">7.(</span><span style=\"font-family:Cambria Math\">d) </span><span style=\"font-family:Cambria Math\">Inability</span></p> <p><span style=\"font-family:Cambria Math\">Knack (</span><span style=\"font-family:Kokila\">आदत</span><span style=\"font-family:Cambria Math\">)- </span><span style=\"font-family:Cambria Math\">a special skill, talent, or aptitude making for ease and dexterity</span></p> <p><span style=\"font-family:Cambria Math\">Vile (</span><span style=\"font-family:Kokila\">घिनौना</span><span style=\"font-family:Cambria Math\">)</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">-  A</span><span style=\"font-family:Cambria Math\"> little worth or value or morally despicable</span></p> <p><span style=\"font-family:Cambria Math\">Process (</span><span style=\"font-family:Kokila\">प्रक्रिया</span><span style=\"font-family:Cambria Math\">) - </span><span style=\"font-family:Cambria Math\">something we do in order to achieve a certain result</span></p> <p><span style=\"font-family:Cambria Math\">Lesson (</span><span style=\"font-family:Kokila\">पाठ</span><span style=\"font-family:Cambria Math\">)- A piece of instruction or something to be learned</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "7",
                    question_en: " <p>8. </span><span style=\"font-family:Cambria Math\">Choose the word that is opposite in meaning to the given word.</span></p> <p><span style=\"font-family:Cambria Math\"> Anomalous</span></p>",
                    question_hi: " <p>8. </span><span style=\"font-family:Cambria Math\">Choose the word that is opposite in meaning to the given word.</span></p> <p><span style=\"font-family:Cambria Math\"> Anomalous</span></p>",
                    options_en: [" <p>  </span><span style=\"font-family:Cambria Math\">Endemic</span></p>", " <p>  </span><span style=\"font-family:Cambria Math\">Outlandish</span></p>", 
                                " <p>  </span><span style=\"font-family:Cambria Math\">Customary</span></p>", " <p>  </span><span style=\"font-family:Cambria Math\">Alien</span></p>"],
                    options_hi: [" <p>  </span><span style=\"font-family:Cambria Math\">Endemic</span></p>", " <p>  </span><span style=\"font-family:Cambria Math\">Outlandish</span></p>",
                                " <p>  </span><span style=\"font-family:Cambria Math\">Customary</span></p>", " <p>  </span><span style=\"font-family:Cambria Math\">Alien</span></p>"],
                    solution_en: " <p><span style=\"font-family:Cambria Math\">8.(</span><span style=\"font-family:Cambria Math\">c)</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">Customary</span></p> <p><span style=\"font-family:Cambria Math\"> Anomalous - Something that deviates from normal or usual</span></p> <p><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">Endemic - </span><span style=\"font-family:Cambria Math\">especially of a disease or a condition regularly occurring within an area or community.</span></p> <p><span style=\"font-family:Cambria Math\"> Outlandish - Something strange and difficult to like or accept</span></p> <p><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">Alien - </span><span style=\"font-family:Cambria Math\">belonging or relating to foreign country or another planet</span></p>",
                    solution_hi: " <p><span style=\"font-family:Cambria Math\">8.(</span><span style=\"font-family:Cambria Math\">c)</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">Customary</span></p> <p><span style=\"font-family:Cambria Math\"> Anomalous (</span><span style=\"font-family:Kokila\">नियमविरूद्ध</span><span style=\"font-family:Cambria Math\">) - Something that deviates from normal or usual</span></p> <p><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">Endemic (</span><span style=\"font-family:Kokila\">स्थानिक</span><span style=\"font-family:Cambria Math\">) - </span><span style=\"font-family:Cambria Math\">especially of a disease or a condition regularly occurring within an area or community.</span></p> <p><span style=\"font-family:Cambria Math\"> Outlandish (</span><span style=\"font-family:Kokila\">विदेशी</span><span style=\"font-family:Cambria Math\">)- Something strange and difficult to like or accept</span></p> <p><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">Alien (</span><span style=\"font-family:Kokila\">विदेशी</span><span style=\"font-family:Cambria Math\">)- </span><span style=\"font-family:Cambria Math\">belonging or relating to foreign country or another planet</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "7",
                    question_en: " <p><span style=\"font-family:Cambria Math\">9.</span><span style=\"font-family:Cambria Math\"> Choose the word that is opposite in meaning to the given word. </span></p> <p><span style=\"font-family:Cambria Math\">Objurgation</span></p>",
                    question_hi: " <p><span style=\"font-family:Cambria Math\">9.</span><span style=\"font-family:Cambria Math\">Choose</span><span style=\"font-family:Cambria Math\"> the word that is opposite in meaning to the given word. </span></p> <p><span style=\"font-family:Cambria Math\">Objurgation</span></p>",
                    options_en: [" <p> Hamper </span></p>", " <p> Endorsement </span></p>", 
                                " <p> Reproof </span></p>", " <p> Commination</span></p>"],
                    options_hi: [" <p>  Hamper</span><span style=\"font-family:Cambria Math\"> </span></p>", " <p>  Endorsement</span><span style=\"font-family:Cambria Math\"> </span></p>",
                                " <p>  Reproof</span><span style=\"font-family:Cambria Math\"> </span></p>", " <p> Commination</span></p>"],
                    solution_en: " <p><span style=\"font-family:Cambria Math\">9.(</span><span style=\"font-family:Cambria Math\">b) </span><span style=\"font-family:Cambria Math\">Endorsement</span></p> <p><span style=\"font-family:Cambria Math\">Objurgation - a harsh reprimand or criticism</span></p> <p><span style=\"font-family:Cambria Math\">Hamper -to impede the natural activity </span><span style=\"font-family:Cambria Math\">of :</span><span style=\"font-family:Cambria Math\"> encumber. Bad weather hampered the search effort. </span></p> <p><span style=\"font-family:Cambria Math\">Reproof -something that you say to somebody when you do not approve of what he/she has done</span></p> <p><span style=\"font-family:Cambria Math\">Commination-a threat of punishment or vengeance.</span></p>",
                    solution_hi: " <p><span style=\"font-family:Cambria Math\">9.(</span><span style=\"font-family:Cambria Math\">b) </span><span style=\"font-family:Cambria Math\">Endorsement</span></p> <p><span style=\"font-family:Cambria Math\">Objurgation(</span><span style=\"font-family:Kokila\">डांट</span><span style=\"font-family:Cambria Math\">-</span><span style=\"font-family:Kokila\">फटकार</span><span style=\"font-family:Cambria Math\">) - a harsh reprimand or criticism</span></p> <p><span style=\"font-family:Cambria Math\">Hamper(</span><span style=\"font-family:Kokila\">प्रतिबंधित</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">करना</span><span style=\"font-family:Cambria Math\">) -to impede the natural activity of : encumber. Bad weather hampered the search effort. </span></p> <p><span style=\"font-family:Cambria Math\">Reproof(</span><span style=\"font-family:Kokila\">फटकार</span><span style=\"font-family:Cambria Math\">) -something that you say to somebody when you do not approve of what he/she has done</span></p> <p><span style=\"font-family:Cambria Math\">Commination(</span><span style=\"font-family:Kokila\">दैवी</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">शाप</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">का</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">भय</span><span style=\"font-family:Cambria Math\">)-a threat of punishment or vengeance.</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "7",
                    question_en: " <p><span style=\"font-family:Cambria Math\">10. </span><span style=\"font-family:Cambria Math\">Choose the word that is opposite in meaning to the given word. </span></p> <p><span style=\"font-family:Cambria Math\">Adequate</span></p>",
                    question_hi: " <p><span style=\"font-family:Cambria Math\">10. </span><span style=\"font-family:Cambria Math\">Choose the word that is opposite in meaning to the given word. </span></p> <p><span style=\"font-family:Cambria Math\">Adequate</span></p>",
                    options_en: [" <p> Indolent </span></p>", " <p> Superficial </span></p>", 
                                " <p> Wanting </span></p>", " <p> Taunting</span></p>"],
                    options_hi: [" <p> Indolent </span></p>", " <p> Superficial </span></p>",
                                " <p> Wanting </span></p>", " <p> Taunting</span></p>"],
                    solution_en: " <p><span style=\"font-family:Cambria Math\">10.(</span><span style=\"font-family:Cambria Math\">c)</span><span style=\"font-family:Cambria Math\">Wanting</span></p> <p><span style=\"font-family:Cambria Math\">Adequate - enough or satisfactory for a particular purpose</span></p> <p><span style=\"font-family:Cambria Math\">Indolent  -</span><span style=\"font-family:Cambria Math\">not wanting to work</span></p> <p><span style=\"font-family:Cambria Math\">Superficial  -</span><span style=\"font-family:Cambria Math\">not studying or thinking about something in a deep or complete way</span></p> <p><span style=\"font-family:Cambria Math\">Taunting -to try to make somebody angry or upset by saying unpleasant or cruel things</span></p>",
                    solution_hi: " <p><span style=\"font-family:Cambria Math\">10.(</span><span style=\"font-family:Cambria Math\">c)</span><span style=\"font-family:Cambria Math\">Wanting</span></p> <p><span style=\"font-family:Cambria Math\">Adequate(</span><span style=\"font-family:Kokila\">पर्याप्त</span><span style=\"font-family:Cambria Math\">) - enough or satisfactory for a particular purpose</span></p> <p><span style=\"font-family:Cambria Math\">Indolent(</span><span style=\"font-family:Kokila\">आलसी</span><span style=\"font-family:Cambria Math\">) -not wanting to work</span></p> <p><span style=\"font-family:Cambria Math\">Superficial(</span><span style=\"font-family:Kokila\">अगंभीर</span><span style=\"font-family:Cambria Math\">) -not studying or thinking about something in a deep or complete way</span></p> <p><span style=\"font-family:Cambria Math\">Taunting(</span><span style=\"font-family:Kokila\">मज़ाक</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">करना</span><span style=\"font-family:Cambria Math\">) -to try to make somebody angry or upset by saying unpleasant or cruel things</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "7",
                    question_en: " <p>11. </span><span style=\"font-family:Cambria Math\">Select the word that is opposite in meaning (ANTONYM) to the word given below. Celestial</span></p>",
                    question_hi: " <p>11. </span><span style=\"font-family:Cambria Math\">Select the word that is opposite in meaning (ANTONYM) to the word given below. Celestial</span></p>",
                    options_en: [" <p>Terrestrial</span><span style=\"font-family:Cambria Math\"> </span></p>", " <p> Heavenly</span></p>", 
                                " <p> Divine</span></p>", " <p> Stellar</span></p>"],
                    options_hi: [" <p>Terrestrial </span></p>", " <p> Heavenly</span></p>",
                                " <p> Divine</span></p>", " <p> Stellar</span></p>"],
                    solution_en: " <p><span style=\"font-family:Cambria Math\">11.(</span><span style=\"font-family:Cambria Math\">a) </span><span style=\"font-family:Cambria Math\">Terrestrial</span></p> <p><span style=\"font-family:Cambria Math\">Celestial - relating to or inhabiting a divine heaven.</span></p> <p><span style=\"font-family:Cambria Math\">Heavenly -connected with heaven or the sky</span></p> <p><span style=\"font-family:Cambria Math\">Divine - connected with God or a god</span></p> <p><span style=\"font-family:Cambria Math\">Stellar - connected with the stars</span></p>",
                    solution_hi: " <p><span style=\"font-family:Cambria Math\">11.(</span><span style=\"font-family:Cambria Math\">a) </span><span style=\"font-family:Cambria Math\">Terrestrial</span></p> <p><span style=\"font-family:Cambria Math\">Celestial(</span><span style=\"font-family:Kokila\">आकाशीय</span><span style=\"font-family:Cambria Math\">) - relating to or inhabiting a divine heaven.</span></p> <p><span style=\"font-family:Cambria Math\">Heavenly(</span><span style=\"font-family:Kokila\">स्वर्गीय</span><span style=\"font-family:Cambria Math\">) -connected with heaven or the sky</span></p> <p><span style=\"font-family:Cambria Math\">Divine(</span><span style=\"font-family:Kokila\">दिव्य</span><span style=\"font-family:Cambria Math\">) - connected with God or a god</span></p> <p><span style=\"font-family:Cambria Math\">Stellar(</span><span style=\"font-family:Kokila\">तारा</span><span style=\"font-family:Cambria Math\">-</span><span style=\"font-family:Kokila\">संबंधी</span><span style=\"font-family:Cambria Math\">) - connected with the stars</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "7",
                    question_en: " <p><span style=\"font-family:Cambria Math\">12.</span><span style=\"font-family:Cambria Math\">Select</span><span style=\"font-family:Cambria Math\"> the word that is opposite in meaning (ANTONYM) to the word given below.</span></p> <p><span style=\"font-family:Cambria Math\"> Fiend</span></p>",
                    question_hi: " <p><span style=\"font-family:Cambria Math\">12.</span><span style=\"font-family:Cambria Math\">Select</span><span style=\"font-family:Cambria Math\"> the word that is opposite in meaning (ANTONYM) to the word given below.</span></p> <p><span style=\"font-family:Cambria Math\"> Fiend</span></p>",
                    options_en: [" <p> Vile  </span></p>", " <p> Junkie  </span></p>", 
                                " <p> Saint  </span></p>", " <p> Atrocious</span></p>"],
                    options_hi: [" <p> Vile  </span></p>", " <p> Junkie  </span></p>",
                                " <p> Saint  </span></p>", " <p> Atrocious</span></p>"],
                    solution_en: " <p><span style=\"font-family:Cambria Math\">12.(</span><span style=\"font-family:Cambria Math\">c)  </span><span style=\"font-family:Cambria Math\">Saint</span></p> <p><span style=\"font-family:Cambria Math\">Fiend - an evil and cruel person</span></p> <p><span style=\"font-family:Cambria Math\">Vile - extremely unpleasant</span></p> <p><span style=\"font-family:Cambria Math\">Junkie - a person with an insatiable craving for something</span></p> <p><span style=\"font-family:Cambria Math\">Atrocious - very cruel or evil </span></p>",
                    solution_hi: " <p><span style=\"font-family:Cambria Math\">12.(</span><span style=\"font-family:Cambria Math\">c) </span><span style=\"font-family:Cambria Math\">Saint</span></p> <p><span style=\"font-family:Cambria Math\">Fiend(</span><span style=\"font-family:Kokila\">क्रूर</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">व्यक्ति</span><span style=\"font-family:Cambria Math\">) - an evil and cruel person</span></p> <p><span style=\"font-family:Cambria Math\">Vile(</span><span style=\"font-family:Kokila\">निकम्मा</span><span style=\"font-family:Cambria Math\">) - extremely unpleasant</span></p> <p><span style=\"font-family:Cambria Math\">Junkie(</span><span style=\"font-family:Kokila\">लालची</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">व्यक्ति</span><span style=\"font-family:Cambria Math\">) - a person with an insatiable craving for something</span></p> <p><span style=\"font-family:Cambria Math\">Atrocious(</span><span style=\"font-family:Kokila\">अति</span><span style=\"font-family:Cambria Math\">-</span><span style=\"font-family:Kokila\">दुष्ट</span><span style=\"font-family:Cambria Math\">) - very cruel or evil </span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "7",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">13. </span><span style=\"font-family: Cambria Math;\">Choose the word that is opposite in meaning to the given word. </span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\"> Inept</span></strong></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">13. </span><span style=\"font-family: Cambria Math;\">Choose the word that is opposite in meaning to the given word. </span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\"> Inept</span></strong></p>\n",
                    options_en: ["<p>Wanting</p>\n", "<p>Competent</p>\n", 
                                "<p>Barred</p>\n", "<p>Callow</p>\n"],
                    options_hi: ["<p>Wanting</p>\n", "<p>Competent</p>\n",
                                "<p>Barred</p>\n", "<p>Callow</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">13.(</span><span style=\"font-family: Cambria Math;\">b) </span><span style=\"font-family: Cambria Math;\">Competent</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Inept -</span><span style=\"font-family: Cambria Math;\">not skilled or effective</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Wanting - not having enough of something</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Barred - to be blocked from entrance or not allowed to do something </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Callow </span><span style=\"font-family: Cambria Math;\">-(</span><span style=\"font-family: Cambria Math;\">of a young person) inexperienced and immature.</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">13.(</span><span style=\"font-family: Cambria Math;\">b) </span><span style=\"font-family: Cambria Math;\">Competent</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Inept(</span><span style=\"font-family: Kokila;\">&#2309;&#2351;&#2379;&#2327;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\">) -</span><span style=\"font-family: Cambria Math;\">not skilled or effective</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Wanting(</span><span style=\"font-family: Kokila;\">&#2309;&#2349;&#2366;&#2357;&#2327;&#2381;&#2352;&#2360;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\">) - not having enough of something</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Barred(</span><span style=\"font-family: Kokila;\">&#2357;&#2352;&#2381;&#2332;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\">) - to be blocked from entrance or not allowed to do something </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Callow(</span><span style=\"font-family: Kokila;\">&#2309;&#2344;&#2369;&#2349;&#2357;&#2361;&#2368;&#2344;</span><span style=\"font-family: Cambria Math;\">) </span><span style=\"font-family: Cambria Math;\">-(</span><span style=\"font-family: Cambria Math;\">of a young person) inexperienced and immature.</span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "7",
                    question_en: " <p><span style=\"font-family:Cambria Math\">14</span><span style=\"font-family:Cambria Math\">.Select</span><span style=\"font-family:Cambria Math\"> the word that is opposite in meaning (ANTONYM) to the word given below.</span></p> <p><span style=\"font-family:Cambria Math\"> Wane</span></p>",
                    question_hi: " <p><span style=\"font-family:Cambria Math\">14</span><span style=\"font-family:Cambria Math\">.Select</span><span style=\"font-family:Cambria Math\"> the word that is opposite in meaning (ANTONYM) to the word given below.</span></p> <p><span style=\"font-family:Cambria Math\"> Wane</span></p>",
                    options_en: [" <p> Intensify </span></p>", " <p> Evanesce </span></p>", 
                                " <p> Diminish </span></p>", " <p> Contract</span></p>"],
                    options_hi: [" <p> Intensify </span></p>", " <p> Evanesce </span></p>",
                                " <p> Diminish </span></p>", " <p> Contract</span></p>"],
                    solution_en: " <p><span style=\"font-family:Cambria Math\">14.(</span><span style=\"font-family:Cambria Math\">a) </span><span style=\"font-family:Cambria Math\">Intensify</span></p> <p><span style=\"font-family:Cambria Math\">Wane - to decrease in strength, intensity</span></p> <p><span style=\"font-family:Cambria Math\">Evanesce </span><span style=\"font-family:Cambria Math\">-  to</span><span style=\"font-family:Cambria Math\"> disappear or be forgotten</span></p> <p><span style=\"font-family:Cambria Math\">Diminish - to reduce in size or importance</span></p> <p><span style=\"font-family:Cambria Math\">Contract - to shrink or to enter into a legal agreement</span></p>",
                    solution_hi: " <p><span style=\"font-family:Cambria Math\">14.(</span><span style=\"font-family:Cambria Math\">a) </span><span style=\"font-family:Cambria Math\">Intensify</span></p> <p><span style=\"font-family:Cambria Math\">Wane(</span><span style=\"font-family:Kokila\">पतन</span><span style=\"font-family:Cambria Math\">)- to decrease in strength, intensity</span></p> <p><span style=\"font-family:Cambria Math\">Evanesce(</span><span style=\"font-family:Kokila\">ग़ायब</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">होना</span><span style=\"font-family:Cambria Math\">) -  to disappear or be forgotten</span></p> <p><span style=\"font-family:Cambria Math\">Diminish(</span><span style=\"font-family:Kokila\">कम</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">करना</span><span style=\"font-family:Cambria Math\">) - to reduce in size or importance</span></p> <p><span style=\"font-family:Cambria Math\">Contract(</span><span style=\"font-family:Kokila\">संविदा</span><span style=\"font-family:Cambria Math\">) - to shrink or to enter into a legal agreement</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "misc",
                    question_en: " <p><span style=\"font-family:Cambria Math\">15.</span><span style=\"font-family:Cambria Math\">Choose</span><span style=\"font-family:Cambria Math\"> the word that is opposite in meaning to the given word. </span></p> <p><span style=\"font-family:Cambria Math\">Delinquency</span></p>",
                    question_hi: " <p><span style=\"font-family:Cambria Math\">15.</span><span style=\"font-family:Cambria Math\">Choose</span><span style=\"font-family:Cambria Math\"> the word that is opposite in meaning to the given word. </span></p> <p><span style=\"font-family:Cambria Math\">Delinquency</span></p>",
                    options_en: [" <p> Neglect </span></p>", " <p> Fulfilment </span></p>", 
                                " <p> Tardiness </span></p>", " <p> Default</span></p>"],
                    options_hi: [" <p> Neglect </span></p>", " <p> Fulfilment </span></p>",
                                " <p> Tardiness </span></p>", " <p> Default</span></p>"],
                    solution_en: " <p><span style=\"font-family:Cambria Math\">15.(</span><span style=\"font-family:Cambria Math\">b) </span><span style=\"font-family:Cambria Math\">Fulfilment</span></p> <p><span style=\"font-family:Cambria Math\">Delinquency - neglect of one’s duty</span></p> <p><span style=\"font-family:Cambria Math\">Neglect </span><span style=\"font-family:Cambria Math\">-  to</span><span style=\"font-family:Cambria Math\"> fail to pay enough attention to someone or something</span></p> <p><span style=\"font-family:Cambria Math\">Tardiness - the quality of being late or slow</span></p> <p><span style=\"font-family:Cambria Math\">Default - A default situation is what exists or happens unless someone or something changes it</span></p>",
                    solution_hi: " <p><span style=\"font-family:Cambria Math\">15.(</span><span style=\"font-family:Cambria Math\">b) </span><span style=\"font-family:Cambria Math\">Fulfilment</span></p> <p><span style=\"font-family:Cambria Math\">Delinquency(</span><span style=\"font-family:Kokila\">अपराध</span><span style=\"font-family:Cambria Math\">) - neglect of one’s duty</span></p> <p><span style=\"font-family:Cambria Math\">Neglect(</span><span style=\"font-family:Kokila\">उपेक्षा</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">करना</span><span style=\"font-family:Cambria Math\">) -  to fail to pay enough attention to someone or something</span></p> <p><span style=\"font-family:Cambria Math\">Tardiness(</span><span style=\"font-family:Kokila\">मंदी</span><span style=\"font-family:Cambria Math\">) - the quality of being late or slow</span></p> <p><span style=\"font-family:Cambria Math\">Default(</span><span style=\"font-family:Kokila\">गलती</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">करना</span><span style=\"font-family:Cambria Math\">) - A default situation is what exists or happens unless someone or something changes it</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>