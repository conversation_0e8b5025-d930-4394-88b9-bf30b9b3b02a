<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">12:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 25</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">25</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 12 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["7"] = {
                name: "English",
                start: 0,
                end: 23
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="7">English</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 24,
                end: 24
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "7",
                    question_en: "<p>1. <span style=\"font-family: Cambria Math;\">Select the most appropriate meaning of the given idiom. </span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Burn your bridges</span></strong></p>\n",
                    question_hi: "<p>1. <span style=\"font-family: Cambria Math;\">Select the most appropriate meaning of the given idiom. </span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Burn your bridges</span></strong></p>\n",
                    options_en: ["<p>To have a fight</p>\n", "<p>To disclose a secret <span style=\"font-family: Cambria Math;\"> </span></p>\n", 
                                "<p>To be puzzled<span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p>To eliminate the possibility of return or retreat</p>\n"],
                    options_hi: ["<p>To have a fight</p>\n", "<p>To disclose a secret <span style=\"font-family: Cambria Math;\"> </span></p>\n",
                                "<p>To be puzzled<span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p>To eliminate the possibility of return or retreat</p>\n"],
                    solution_en: "<p>1.(d) Burn your bridges - To eliminate the possibility of return or retreat</p>\r\n<p><span style=\"font-family: Cambria Math;\">E.g.- </span><span style=\"font-family: Cambria Math;\">If you insult the boss when you leave, you\'ll be burning your bridges and you\'ll never be able to go back to work there again</span><span style=\"font-family: Cambria Math;\">. </span></p>\n",
                    solution_hi: "<p>1.(d) Burn your bridges - To eliminate the possibility of return or retreat/<span style=\"font-family: Nirmala UI;\">&#2357;&#2366;&#2346;&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2368;&#2331;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2335;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2349;&#2366;&#2357;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2326;&#2340;&#2381;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2375;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2313;&#2342;&#2366;&#2361;&#2352;&#2339;</span><span style=\"font-family: Cambria Math;\"> - </span><span style=\"font-family: Cambria Math;\">If you insult the boss when you leave, you\'ll be burning your bridges and you\'ll never be able to go back to work there again</span><span style=\"font-family: Cambria Math;\">./</span><span style=\"font-family: Nirmala UI;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2346;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2331;&#2379;&#2396;&#2340;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2350;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2377;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2346;&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2340;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2306;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2346;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2369;&#2344;&#2307;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2366;&#2346;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2349;&#2366;&#2357;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2326;&#2340;&#2381;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2340;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2346;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2347;&#2367;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2349;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2361;&#2366;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2366;&#2346;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2361;&#2368;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2366;&#2319;&#2306;&#2327;&#2375;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "7",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">2.</span><span style=\"font-family: Cambria Math;\"> Choose the option that is the correct direct form of the sentence.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> She said, since it was evening, she had to go for a run.</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">2.</span><span style=\"font-family: Cambria Math;\"> Choose the option that is the correct direct form of the sentence.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> She said, since it was evening, she had to go for a run.</span></p>\n",
                    options_en: ["<p>She said, \"It is evening I must ran.\"</p>\n", "<p><span style=\"font-family: Cambria Math;\">She said, \"It is evening I might go for a run.\"</span></p>\n", 
                                "<p>She said, \"It is evening I should go for a run.\"</p>\n", "<p>She said, \"It is evening I must go for a run.\"</p>\n"],
                    options_hi: ["<p>She said, \"It is evening I must ran.\"</p>\n", "<p><span style=\"font-family: Cambria Math;\">She said, \"It is evening I might go for a run.\"</span></p>\n",
                                "<p>She said, \"It is evening I should go for a run.\"</p>\n", "<p>She said, \"It is evening I must go for a run.\"</p>\n"],
                    solution_en: "<p>2.(d) <span style=\"font-family: Cambria Math;\">She said, \"It is evening I must go for a run.\"(Correct)</span></p>\r\n<p><span style=\"font-weight: 400;\">(a) She said, \"It is evening I must </span><span style=\"text-decoration: underline;\"><span style=\"font-weight: 400;\">ran</span></span><span style=\"font-weight: 400;\">.\"(Incorrect Verb)</span></p>\r\n<p><span style=\"font-weight: 400;\">(b) </span><span style=\"font-weight: 400;\">She said, \"It is evening I </span><span style=\"text-decoration: underline;\"><span style=\"font-weight: 400;\">might</span></span><span style=\"font-weight: 400;\"> go for a run.\"</span><span style=\"font-weight: 400;\">(Incorrect Modal Verb)</span></p>\r\n<p><span style=\"font-weight: 400;\">(c) She said, \"It is evening I </span><span style=\"text-decoration: underline;\"><span style=\"font-weight: 400;\">should</span></span><span style=\"font-weight: 400;\"> go for a run.\"(Incorrect Modal Verb)&nbsp;&nbsp;</span></p>\n",
                    solution_hi: "<p>2.(d) <span style=\"font-family: Cambria Math;\">She said, \"It is evening I must go for a run.\"(Correct)</span></p>\r\n<p><span style=\"font-weight: 400;\">(a) She said, \"It is evening I must </span><span style=\"text-decoration: underline;\"><span style=\"font-weight: 400;\">ran</span></span><span style=\"font-weight: 400;\">.\"(&#2327;&#2354;&#2340; Verb)</span></p>\r\n<p><span style=\"font-weight: 400;\">(b) </span><span style=\"font-weight: 400;\">She said, \"It is evening I </span><span style=\"text-decoration: underline;\"><span style=\"font-weight: 400;\">might</span></span><span style=\"font-weight: 400;\"> go for a run.\"</span><span style=\"font-weight: 400;\">(&#2327;&#2354;&#2340; Modal Verb)</span></p>\r\n<p><span style=\"font-weight: 400;\">(c) She said, \"It is evening I </span><span style=\"text-decoration: underline;\"><span style=\"font-weight: 400;\">should</span></span><span style=\"font-weight: 400;\"> go for a run.\"(&#2327;&#2354;&#2340; Modal Verb)&nbsp;&nbsp;</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "7",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">3</span><span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\">Select the most appropriate option to fill in the blank.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> In the early autumn fierce fighting took place, the ________</span><span style=\"font-family: Cambria Math;\"> Serbians bearing a </span><span style=\"font-family: Cambria Math;\">prominent part. </span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">3</span><span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\">Select the most appropriate option to fill in the blank.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> In the early autumn fierce fighting took place, the _________</span><span style=\"font-family: Cambria Math;\"> Serbians bearing a </span><span style=\"font-family: Cambria Math;\">prominent part. </span></p>\n",
                    options_en: ["<p>cowardly</p>\n", "<p>unabashed</p>\n", 
                                "<p>gallant</p>\n", "<p>bore</p>\n"],
                    options_hi: ["<p>cowardly</p>\n", "<p>unabashed</p>\n",
                                "<p>gallant</p>\n", "<p>bore</p>\n"],
                    solution_en: "<p>3.(c)</p>\r\n<p><span style=\"font-family: Cambria Math;\">&lsquo;Gallant&rsquo; means </span><span style=\"font-family: Cambria Math;\">a man who is brave &amp; courageous</span><span style=\"font-family: Cambria Math;\">. The given sentence states that </span><span style=\"font-family: Cambria Math;\">in the early autumn fierce fighting took place, the </span><span style=\"font-family: Cambria Math;\">gallant </span><span style=\"font-family: Cambria Math;\">Serbians bearing a prominent part</span><span style=\"font-family: Cambria Math;\">. Hence, &lsquo;gallant&rsquo; is the most appropriate answer.</span></p>\n",
                    solution_hi: "<p>3.(c)</p>\r\n<p><span style=\"font-family: Cambria Math;\">&lsquo;Gallant&rsquo; </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2352;&#2381;&#2341;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2361;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2381;&#2351;&#2325;&#2381;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2361;&#2366;&#2342;&#2369;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2366;&#2361;&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2379;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2319;</span><span style=\"font-family: Cambria Math;\"> sentence </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2361;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2358;&#2369;&#2352;&#2369;&#2310;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2358;&#2352;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2315;&#2340;&#2369;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2349;&#2351;&#2306;&#2325;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2337;&#2364;&#2366;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2369;&#2312;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2367;&#2360;&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2368;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2352;&#2381;&#2348;&#2367;&#2351;&#2366;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2350;&#2369;&#2326;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2367;&#2360;&#2381;&#2360;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2341;&#2375;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2311;&#2360;&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\">, &lsquo;gallant&rsquo; </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2348;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2346;&#2351;&#2369;&#2325;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2340;&#2381;&#2340;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "7",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">4. </span><span style=\"font-family: Cambria Math;\">Choose the incorrectly spelt word.</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">4. </span><span style=\"font-family: Cambria Math;\">Choose the incorrectly spelt word.</span></p>\n",
                    options_en: ["<p>WASSE</p>\n", "<p>FITTER</p>\n", 
                                "<p>COURIER</p>\n", "<p>COURSE</p>\n"],
                    options_hi: ["<p>WASSE</p>\n", "<p>FITTER</p>\n",
                                "<p>COURIER</p>\n", "<p>COURSE</p>\n"],
                    solution_en: "<p>4.(a) WASSE</p>\r\n<p><span style=\"font-family: Cambria Math;\">&ldquo;WASTE&rdquo; </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2361;&#2368;</span><span style=\"font-family: Cambria Math;\"> spelling </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                    solution_hi: "<p>4.(a) WASSE</p>\r\n<p><span style=\"font-family: Cambria Math;\">&ldquo;WASTE&rdquo; </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2361;&#2368;</span><span style=\"font-family: Cambria Math;\"> spelling </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "7",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">5. </span><span style=\"font-family: Cambria Math;\">The given sentence is in active voice. Change the voice of the sentence. Select the correct option from the sentences given in options. </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Vasant has delivered the speech.</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">5. </span><span style=\"font-family: Cambria Math;\">The given sentence is in active voice. Change the voice of the sentence. Select the correct option from the sentences given in options. </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Vasant has delivered the speech.</span></p>\n",
                    options_en: ["<p>Vasant delivers the speech on time.</p>\n", "<p>The speech was delivered by Vasant.</p>\n", 
                                "<p>The speech was timely delivered by Vasant</p>\n", "<p>The speech has been delivered by Vasant.</p>\n"],
                    options_hi: ["<p>Vasant delivers the speech on time.</p>\n", "<p>The speech was delivered by Vasant.</p>\n",
                                "<p>The speech was timely delivered by Vasant</p>\n", "<p>The speech has been delivered by Vasant.</p>\n"],
                    solution_en: "<p>5.(d) <span style=\"font-family: Cambria Math;\">The speech has been delivered by Vasant.(Correct)</span></p>\r\n<p><span style=\"font-weight: 400;\">(a) Vasant delivers the speech on time.(Incorrect Sentence Structure)&nbsp;</span></p>\r\n<p><span style=\"font-weight: 400;\">(b) The speech </span><span style=\"text-decoration: underline;\"><span style=\"font-weight: 400;\">was delivered</span></span><span style=\"font-weight: 400;\"> by Vasant.(Incorrect Tense)</span></p>\r\n<p><span style=\"font-weight: 400;\">(c) The speech </span><span style=\"text-decoration: underline;\"><span style=\"font-weight: 400;\">was timely delivered</span></span><span style=\"font-weight: 400;\"> by Vasant.(Incorrect Tense)</span></p>\n",
                    solution_hi: "<p>5.(d) <span style=\"font-family: Cambria Math;\">The speech has been delivered by Vasant.(Correct)</span></p>\r\n<p><span style=\"font-weight: 400;\">(a) Vasant delivers the speech on time.(&#2327;&#2354;&#2340;&nbsp; Sentence Structure)&nbsp;</span></p>\r\n<p><span style=\"font-weight: 400;\">(b) The speech </span><span style=\"text-decoration: underline;\"><span style=\"font-weight: 400;\">was delivered</span></span><span style=\"font-weight: 400;\"> by Vasant.(&#2327;&#2354;&#2340; Tense)</span></p>\r\n<p><span style=\"font-weight: 400;\">(c) The speech </span><span style=\"text-decoration: underline;\"><span style=\"font-weight: 400;\">was timely delivered</span></span><span style=\"font-weight: 400;\"> by Vasant.(&#2327;&#2354;&#2340; Tense)</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "7",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">6. </span><span style=\"font-family: Cambria Math;\">Choose the word that is opposite in meaning to the given word. </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Abduct</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">6. </span><span style=\"font-family: Cambria Math;\">Choose the word that is opposite in meaning to the given word. </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Abduct</span></p>\n",
                    options_en: ["<p>Redeem</p>\n", "<p>Impress</p>\n", 
                                "<p>Sociable</p>\n", "<p>Hijack</p>\n"],
                    options_hi: ["<p>Redeem</p>\n", "<p>Impress</p>\n",
                                "<p>Sociable</p>\n", "<p>Hijack</p>\n"],
                    solution_en: "<p>6.(a)</p>\r\n<p><span style=\"font-family: Cambria Math;\">Abduct - </span><span style=\"font-family: Cambria Math;\">to take hold of somebody and take him/her away illegally</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Redeem - </span><span style=\"font-family: Cambria Math;\">to prevent something from being completely bad</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Impress - </span><span style=\"font-family: Cambria Math;\">to make somebody feel admiration and respect</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Sociable -</span><span style=\"font-family: Cambria Math;\">enjoying being with other people; friendly</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Hijack -</span><span style=\"font-family: Cambria Math;\">to take control of a plane, etc. by force, usually for political reasons</span></p>\n",
                    solution_hi: "<p>6.(a)</p>\r\n<p><span style=\"font-family: Cambria Math;\">Abduct(</span><span style=\"font-family: Nirmala UI;\">&#2309;&#2346;&#2361;&#2352;&#2339;</span><span style=\"font-family: Cambria Math;\">) - </span><span style=\"font-family: Cambria Math;\">to take hold of somebody and take him/her away illegally</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Redeem( </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2366;&#2346;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2352;&#2379;&#2325;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\">) - </span><span style=\"font-family: Cambria Math;\">to prevent something from being completely bad</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Impress(</span><span style=\"font-family: Nirmala UI;\">&#2331;&#2366;&#2346;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2331;&#2379;&#2396;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\">) - </span><span style=\"font-family: Cambria Math;\">to make somebody feel admiration and respect</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Sociable(</span><span style=\"font-family: Nirmala UI;\">&#2350;&#2367;&#2354;&#2344;&#2360;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\">) - </span><span style=\"font-family: Cambria Math;\">enjoying being with other people; friendly</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Hijack(</span><span style=\"font-family: Nirmala UI;\">&#2357;&#2366;&#2361;&#2344;</span><span style=\"font-family: Cambria Math;\"> ,</span><span style=\"font-family: Nirmala UI;\">&#2357;&#2366;&#2351;&#2369;&#2351;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2370;&#2335;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> ) -</span><span style=\"font-family: Cambria Math;\">to take control of a plane, etc. by force, usually for political reasons</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "7",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">7. </span><span style=\"font-family: Cambria Math;\">Some parts of one or more sentences have been jumbled up, and labelled A, B, C and </span><span style=\"font-family: Cambria Math;\">D. Select the option that gives the correct sequence in which these parts can be rearranged to form a meaningful and grammatically correct sentence.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Their approach sensitized</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>A. </strong>of it. Similarly, behind them, in the halls,</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>B.</strong> flicked on when they came within ten feet</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>C. </strong>a switch somewhere and the nursery light</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>D.</strong> lights went on and off as they left them behind, with a soft automaticity.</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">7. </span><span style=\"font-family: Cambria Math;\">Some parts of one or more sentences have been jumbled up, and labelled A, B, C and </span><span style=\"font-family: Cambria Math;\">D. Select the option that gives the correct sequence in which these parts can be rearranged to form a meaningful and grammatically correct sentence.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Their approach sensitized</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>A.</strong> of it. Similarly, behind them, in the halls,</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>B.</strong> flicked on when they came within ten feet</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>C. </strong>a switch somewhere and the nursery light</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>D.</strong> lights went on and off as they left them behind, with a soft automaticity.</span></p>\n",
                    options_en: ["<p>DCBA</p>\n", "<p>CBAD</p>\n", 
                                "<p>BADC</p>\n", "<p>BCAD</p>\n"],
                    options_hi: ["<p>DCBA</p>\n", "<p>CBAD</p>\n",
                                "<p>BADC</p>\n", "<p>BCAD</p>\n"],
                    solution_en: "<p>7.(b) <span style=\"font-family: Cambria Math;\">CBAD </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Sentence C will be the starting line as it introduces the main idea of the parajumble i.e. </span><span style=\"font-family: Cambria Math;\">Their approach sensitized a switch somewhere. </span><span style=\"font-family: Cambria Math;\">However, Sentence B states that </span><span style=\"font-family: Cambria Math;\"> the nursery light flicked on</span><span style=\"font-family: Cambria Math;\">. So, B will follow C. Further, Sentence A states that they came within ten feet of it and Sentence D states that</span><span style=\"font-family: Cambria Math;\"> behind them, in the halls lights went on and off as they left them behind</span><span style=\"font-family: Cambria Math;\">. So, D will follow A. Going through the options, option b has the correct sequence.</span></p>\n",
                    solution_hi: "<p>7.(b) <span style=\"font-family: Cambria Math;\">CBAD </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Sentence C </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2366;&#2352;&#2306;&#2349;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> line </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2379;&#2327;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2351;&#2379;&#2306;&#2325;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2311;&#2360;&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2376;&#2352;&#2366;&#2332;&#2306;&#2348;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2369;&#2326;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2330;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> &lsquo;</span><span style=\"font-family: Cambria Math;\">Their approach sensitized a switch somewhere&rsquo;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2358;&#2366;&#2350;&#2367;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2366;&#2354;&#2366;&#2305;&#2325;&#2367;</span><span style=\"font-family: Cambria Math;\">, Sentence B </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2340;&#2366;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2352;&#2381;&#2360;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2340;&#2381;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2354;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\">, C </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2366;&#2342;</span><span style=\"font-family: Cambria Math;\"> B </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2319;&#2327;&#2366;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2327;&#2375;</span><span style=\"font-family: Cambria Math;\">, Sentence A </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2361;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2311;&#2360;&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2347;&#2368;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2349;&#2368;&#2340;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2341;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> Sentence D </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2361;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2344;&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2368;&#2331;&#2375;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2377;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2352;&#2379;&#2358;&#2344;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2330;&#2354;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2306;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2351;&#2379;&#2306;&#2325;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2344;&#2381;&#2361;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2368;&#2331;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2331;&#2379;&#2337;&#2364;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2341;&#2375;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\">, A, </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2366;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">D </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2319;&#2327;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2404;</span><span style=\"font-family: Cambria Math;\"> options </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2343;&#2381;&#2351;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2366;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> , option b </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2361;&#2368;</span><span style=\"font-family: Cambria Math;\"> sequence </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "7",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">8. </span><span style=\"font-family: Cambria Math;\">Select the most appropriate meaning of the given idiom. </span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Save your breath</span></strong></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">8. </span><span style=\"font-family: Cambria Math;\">Select the most appropriate meaning of the given idiom. </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Save your breath</span></p>\n",
                    options_en: ["<p>To make an effort that will be futile</p>\n", "<p>To hold one\'s anger</p>\n", 
                                "<p>To work hard</p>\n", "<p>To be resentful</p>\n"],
                    options_hi: ["<p>To make an effort that will be futile</p>\n", "<p>To hold one\'s anger</p>\n",
                                "<p>To work hard</p>\n", "<p>To be resentful</p>\n"],
                    solution_en: "<p>8.(a) <span style=\"font-family: Cambria Math;\">Save your breath -To make an effort that will be futile </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">E.g. </span><span style=\"font-family: Cambria Math;\">If you tell someone to save their breath, you mean </span><span style=\"font-family: Cambria Math;\">that they should not bother saying something, because you will not agree</span><span style=\"font-family: Cambria Math;\"> to it or you do not want to hear it.</span></p>\n",
                    solution_hi: "<p>8.(a<span style=\"font-family: Cambria Math;\">) </span><span style=\"font-family: Cambria Math;\">Save your breath -To make an effort that will be futile/</span><span style=\"font-family: Nirmala UI;\">&#2320;&#2360;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2351;&#2366;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2381;&#2351;&#2352;&#2381;&#2341;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2379;&#2327;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2313;&#2342;&#2366;&#2361;&#2352;&#2339;</span><span style=\"font-family: Cambria Math;\"> - </span><span style=\"font-family: Cambria Math;\">If you tell someone to save their breath, you mean </span><span style=\"font-family: Cambria Math;\">that they should not bother saying something, because you will not agree</span><span style=\"font-family: Cambria Math;\"> to it or you do not want to hear it./</span><span style=\"font-family: Nirmala UI;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2346;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> <span style=\"font-weight: 400;\">Save your breath </span></span><span style=\"font-family: Cambria Math;\">&nbsp;</span><span style=\"font-family: Nirmala UI;\">&#2325;&#2361;&#2340;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2306;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2346;&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2340;&#2354;&#2348;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2344;&#2381;&#2361;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2369;&#2331;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2361;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2361;&#2350;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2361;&#2368;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2336;&#2366;&#2344;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2330;&#2366;&#2361;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2351;&#2379;&#2306;&#2325;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2346;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2311;&#2360;&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2361;&#2350;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2361;&#2368;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2379;&#2306;&#2327;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2346;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2311;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2369;&#2344;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2361;&#2368;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2330;&#2366;&#2361;&#2340;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2306;&#2404;</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "7",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">9.</span><span style=\"font-family: Cambria Math;\">Fill in the blank with an appropriate option.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Participants were ___________</span><span style=\"font-family: Cambria Math;\">&nbsp;</span><span style=\"font-family: Cambria Math;\">a meal of steak and French fries under dim light.</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">9.</span><span style=\"font-family: Cambria Math;\">Fill in the blank with an appropriate option.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Participants were ___________</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">a meal of steak and French fries under dim light.</span></p>\n",
                    options_en: ["<p>served</p>\n", "<p>nerved</p>\n", 
                                "<p>cured</p>\n", "<p>calmed</p>\n"],
                    options_hi: ["<p>served</p>\n", "<p>nerved</p>\n",
                                "<p>cured</p>\n", "<p>calmed</p>\n"],
                    solution_en: "<p>9.(a)</p>\r\n<p><span style=\"font-family: Cambria Math;\">&lsquo;Serve&rsquo; means </span><span style=\"font-family: Cambria Math;\">to give food or drink to somebody during a meal</span><span style=\"font-family: Cambria Math;\">. The given sentence states that participants were served a meal of steak and French fries under dim light. Hence, &lsquo;served&rsquo; is the most appropriate answer</span></p>\n",
                    solution_hi: "<p>9.(a)</p>\r\n<p><span style=\"font-family: Cambria Math;\">&lsquo;Serve&rsquo; </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2352;&#2381;&#2341;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2349;&#2379;&#2332;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2380;&#2352;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2326;&#2366;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2375;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2375;&#2344;&#2366;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2319;</span><span style=\"font-family: Cambria Math;\"> sentence </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2361;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2349;&#2366;&#2327;&#2367;&#2351;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2306;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2325;&#2366;&#2358;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2381;&#2335;&#2375;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2347;&#2381;&#2352;&#2375;&#2306;&#2330;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2347;&#2381;&#2352;&#2366;&#2311;&#2332;&#2364;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2349;&#2379;&#2332;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;&#2379;&#2360;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2351;&#2366;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2311;&#2360;&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\">, &lsquo;served&rsquo; </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2348;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2346;&#2351;&#2369;&#2325;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2340;&#2381;&#2340;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "7",
                    question_en: "<p>10.<span style=\"font-family: Cambria Math;\"> Choose the word that can substitute the given group of words. </span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">One who does not express oneself freely</span></strong></p>\n",
                    question_hi: "<p>10.<span style=\"font-family: Cambria Math;\"> Choose the word that can substitute the given group of words. </span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">One who does not express oneself freely</span></strong></p>\n",
                    options_en: ["<p>Skeptic</p>\n", "<p>Erudite</p>\n", 
                                "<p>Introvert</p>\n", "<p>Pessimist</p>\n"],
                    options_hi: ["<p>Skeptic</p>\n", "<p>Erudite</p>\n",
                                "<p>Introvert</p>\n", "<p>Pessimist</p>\n"],
                    solution_en: "<p>10.(c)</p>\r\n<p><span style=\"font-family: Cambria Math;\">Introvert - One who does not express oneself freely</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Skeptic -</span><span style=\"font-family: Cambria Math;\">a person who doubts that something is true, right, etc.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Erudite -</span><span style=\"font-family: Cambria Math;\">having or showing great knowledge that is based on careful study</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Pessimist -</span><span style=\"font-family: Cambria Math;\">a person who always thinks that bad things will happen or that something will be not be successful</span></p>\n",
                    solution_hi: "<p>10.(c) <span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Introvert(</span><span style=\"font-family: Nirmala UI;\">&#2309;&#2306;&#2340;&#2352;&#2381;&#2350;&#2369;&#2326;&#2368;</span><span style=\"font-family: Cambria Math;\">) - One who does not express oneself freely</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Skeptic(</span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2342;&#2375;&#2361;&#2357;&#2366;&#2342;&#2368;</span><span style=\"font-family: Cambria Math;\">) -</span><span style=\"font-family: Cambria Math;\">a person who doubts that something is true, right, etc.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Erudite(</span><span style=\"font-family: Nirmala UI;\">&#2348;&#2369;&#2342;&#2381;&#2343;&#2367;&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\">) -</span><span style=\"font-family: Cambria Math;\">having or showing great knowledge that is based on careful study</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Pessimist(</span><span style=\"font-family: Nirmala UI;\">&#2344;&#2367;&#2352;&#2366;&#2358;&#2366;&#2357;&#2366;&#2342;&#2368;</span><span style=\"font-family: Cambria Math;\">) -</span><span style=\"font-family: Cambria Math;\">a person who always thinks that bad things will happen or that something will be not be successful</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "7",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">11. </span><span style=\"font-family: Cambria Math;\">Choose the word that means the same as the given word. </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Faddish </span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">11. </span><span style=\"font-family: Cambria Math;\">Choose the word that means the same as the given word. </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Faddish </span></p>\n",
                    options_en: ["<p>Unpopular</p>\n", "<p>Phony</p>\n", 
                                "<p>Barge</p>\n", "<p>Fashionable</p>\n"],
                    options_hi: ["<p>Unpopular</p>\n", "<p>Phony</p>\n",
                                "<p>Barge</p>\n", "<p>Fashionable</p>\n"],
                    solution_en: "<p>11.(d)</p>\r\n<p><span style=\"font-family: Cambria Math;\">Faddish - </span><span style=\"font-family: Cambria Math;\">fashionable but not likely to stay fashionable for a long time</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Unpopular -</span><span style=\"font-family: Cambria Math;\">not liked or enjoyed by many people</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Phony -</span><span style=\"font-family: Cambria Math;\">represented as real but actually false</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Barge - </span><span style=\"font-family: Cambria Math;\">a long narrow boat with a flat bottom that is used for carrying goods or people on a canal or river</span></p>\n",
                    solution_hi: "<p>11.(d)</p>\r\n<p><span style=\"font-family: Cambria Math;\">Faddish(</span><span style=\"font-family: Nirmala UI;\">&#2358;&#2380;&#2325;&#2364;&#2368;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2367;&#2332;&#2366;&#2332;&#2364;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2360;&#2381;&#2341;&#2366;&#2351;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> )</span><span style=\"font-family: Cambria Math;\"> - </span><span style=\"font-family: Cambria Math;\">fashionable but not likely to stay fashionable for a long time</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Unpopular(</span><span style=\"font-family: Nirmala UI;\">&#2309;&#2346;&#2381;&#2352;&#2360;&#2367;&#2342;&#2381;&#2343;</span><span style=\"font-family: Cambria Math;\">) -</span><span style=\"font-family: Cambria Math;\">not liked or enjoyed by many people</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Phony(</span><span style=\"font-family: Nirmala UI;\">&#2332;&#2366;&#2354;&#2368;</span><span style=\"font-family: Cambria Math;\">) -</span><span style=\"font-family: Cambria Math;\">represented as real but actually false</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Barge(</span><span style=\"font-family: Nirmala UI;\">&#2344;&#2366;&#2357;</span><span style=\"font-family: Cambria Math;\">) - </span><span style=\"font-family: Cambria Math;\">a long narrow boat with a flat bottom that is used for carrying goods or people on a canal or river</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "7",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">12. </span><span style=\"font-family: Cambria Math;\">Select the most appropriate \'one word\" for the expression given below. </span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">A person who can speak multiple languages.</span></strong></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">12. </span><span style=\"font-family: Cambria Math;\">Select the most appropriate \'one word\" for the expression given below. </span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">A person who can speak multiple languages.</span></strong></p>\n",
                    options_en: ["<p>Minaret</p>\n", "<p>Polyglot</p>\n", 
                                "<p>Spire</p>\n", "<p>Intestate</p>\n"],
                    options_hi: ["<p>Minaret</p>\n", "<p>Polyglot</p>\n",
                                "<p>Spire</p>\n", "<p>Intestate</p>\n"],
                    solution_en: "<p>12.(b)</p>\r\n<p><span style=\"font-family: Cambria Math;\">Polyglot -A person who can speak multiple languages.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Minaret -</span><span style=\"font-family: Cambria Math;\">a tall thin tower, usually forming part of a building where Muslims meet and pray (a mosque)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Spire -</span><span style=\"font-family: Cambria Math;\">a tall pointed tower on the top of a church</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Intestate -</span><span style=\"font-family: Cambria Math;\"> the condition of an estate of a person who dies without a will, and owns property that is worth more than their outstanding debts</span><span style=\"font-family: Cambria Math;\">. </span></p>\n",
                    solution_hi: "<p>12.(b)</p>\r\n<p><span style=\"font-family: Cambria Math;\">Polyglot(</span><span style=\"font-family: Nirmala UI;\">&#2348;&#2361;&#2369;&#2349;&#2366;&#2359;&#2368;</span><span style=\"font-family: Cambria Math;\">) -A person who can speak multiple languages.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Minaret(</span><span style=\"font-family: Nirmala UI;\">&#2343;&#2380;&#2352;&#2361;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\">) -</span><span style=\"font-family: Cambria Math;\">a tall thin tower, usually forming part of a building where Muslims meet and pray (a mosque)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Spire(</span><span style=\"font-family: Nirmala UI;\">&#2358;&#2367;&#2326;&#2352;</span><span style=\"font-family: Cambria Math;\">) -</span><span style=\"font-family: Cambria Math;\">a tall pointed tower on the top of a church</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Intestate(</span><span style=\"font-family: Nirmala UI;\">&#2348;&#2367;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2360;&#2368;&#2351;&#2340;&#2344;&#2366;&#2350;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2366;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> ) -</span><span style=\"font-family: Cambria Math;\"> the condition of an estate of a person who dies without a will, and owns property that is worth more than their outstanding debts</span><span style=\"font-family: Cambria Math;\">. </span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "7",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">13.</span><span style=\"font-family: Cambria Math;\">Fill in the blank with an appropriate option.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">_______________ threats to elephants requires public and political will to take action.</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">13.</span><span style=\"font-family: Cambria Math;\">Fill in the blank with an appropriate option.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">_________ threats to elephants requires public and political will to take action.</span></p>\n",
                    options_en: ["<p>Dignifying</p>\n", "<p>Addressing</p>\n", 
                                "<p>Perplexing</p>\n", "<p>Stagnating</p>\n"],
                    options_hi: ["<p>Dignifying</p>\n", "<p>Addressing</p>\n",
                                "<p>Perplexing</p>\n", "<p>Stagnating</p>\n"],
                    solution_en: "<p>13.(b)</p>\r\n<p><span style=\"font-family: Cambria Math;\">&lsquo;</span><span style=\"font-family: Cambria Math;\">Addressing</span><span style=\"font-family: Cambria Math;\">&rsquo; means </span><span style=\"font-family: Cambria Math;\">to make an important speech to an audience</span><span style=\"font-family: Cambria Math;\">. The given sentence states that addressing threats to elephants requires public and political will to take action. Hence, &lsquo;</span><span style=\"font-family: Cambria Math;\">addressing</span><span style=\"font-family: Cambria Math;\">&rsquo; is the most appropriate answer.</span></p>\n",
                    solution_hi: "<p>13.(b)</p>\r\n<p><span style=\"font-family: Cambria Math;\">&lsquo;</span><span style=\"font-family: Cambria Math;\">Addressing</span><span style=\"font-family: Cambria Math;\">&rsquo; </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2352;&#2381;&#2341;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2352;&#2381;&#2358;&#2325;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2361;&#2340;&#2381;&#2357;&#2346;&#2370;&#2352;&#2381;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2349;&#2366;&#2359;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2375;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2366;&#2325;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2361;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2366;&#2341;&#2367;&#2351;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2326;&#2340;&#2352;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2348;&#2379;&#2343;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;&#2352;&#2381;&#2352;&#2357;&#2366;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2344;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2352;&#2366;&#2332;&#2344;&#2368;&#2340;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2311;&#2330;&#2381;&#2331;&#2366;&#2358;&#2325;&#2381;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2357;&#2358;&#2381;&#2351;&#2325;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2379;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2311;&#2360;&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\">, &lsquo;</span><span style=\"font-family: Cambria Math;\">addressing</span><span style=\"font-family: Cambria Math;\">&rsquo; </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2348;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2346;&#2351;&#2369;&#2325;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2340;&#2381;&#2340;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "7",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">14. </span><span style=\"font-family: Cambria Math;\">Select the word that is closest in meaning (SYNONYM) to the word given below.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">DEPRESSED</span></strong></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">14. </span><span style=\"font-family: Cambria Math;\">Select the word that is closest in meaning (SYNONYM) to the word given below.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">DEPRESSED</span></p>\n",
                    options_en: ["<p>DOLEFUL</p>\n", "<p>DYNAMIC</p>\n", 
                                "<p>STURDY</p>\n", "<p>NOISY</p>\n"],
                    options_hi: ["<p>DOLEFUL</p>\n", "<p>DYNAMIC</p>\n",
                                "<p>STURDY</p>\n", "<p>NOISY</p>\n"],
                    solution_en: "<p>14.(a)</p>\r\n<p><span style=\"font-family: Cambria Math;\">Depressed -</span><span style=\"font-family: Cambria Math;\"> unhappy and without hope</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">DYNAMIC -</span><span style=\"font-family: Cambria Math;\">(used about a person) full of energy and ideas; active</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">STURDY -</span><span style=\"font-family: Cambria Math;\">strong and healthy; that will not break easily</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">NOISY -making a lot of or too much noise; full of noise</span></p>\n",
                    solution_hi: "<p>14.(a)</p>\r\n<p><span style=\"font-family: Cambria Math;\">Depressed(</span><span style=\"font-family: Nirmala UI;\">&#2309;&#2357;&#2360;&#2366;&#2342;&#2327;&#2381;&#2352;&#2360;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\">) -</span><span style=\"font-family: Cambria Math;\"> unhappy and without hope</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">DYNAMIC(</span><span style=\"font-family: Nirmala UI;\">&#2327;&#2340;&#2367;&#2358;&#2368;&#2354;</span><span style=\"font-family: Cambria Math;\">) -</span><span style=\"font-family: Cambria Math;\">(used about a person) full of energy and ideas; active</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">STURDY(</span><span style=\"font-family: Nirmala UI;\">&#2340;&#2327;&#2337;&#2364;&#2366;</span><span style=\"font-family: Cambria Math;\">) -</span><span style=\"font-family: Cambria Math;\">strong and healthy; that will not break easily</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">NOISY(</span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;&#2354;&#2366;&#2361;&#2354;&#2351;&#2369;&#2325;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\">) -making a lot of or too much noise; full of noise</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "7",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">15. </span><span style=\"font-family: Cambria Math;\">Some parts of a few sentences have been jumbled up, and labelled A, B, C and D. </span><span style=\"font-family: Cambria Math;\">Select the option that gives the correct sequence in which these parts can be rearranged to form a meaningful and grammatically correct sentence.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">My years in my father\'s house</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>A.</strong> would have been unbearable had</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>B.</strong> the feel of his hand clutching mine, his</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>C. </strong>I not had my brother. I never forgot</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>D.</strong> refusal to abandon me. Perhaps he and I would have been close even otherwise.</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">15. </span><span style=\"font-family: Cambria Math;\">Some parts of a few sentences have been jumbled up, and labelled A, B, C and D. </span><span style=\"font-family: Cambria Math;\">Select the option that gives the correct sequence in which these parts can be rearranged to form a meaningful and grammatically correct sentence.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">My years in my father\'s house</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>A.</strong> would have been unbearable had</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>B.</strong> the feel of his hand clutching mine, his</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>C.</strong> I not had my brother. I never forgot</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>D.</strong> refusal to abandon me. Perhaps he and I would have been close even otherwise.</span></p>\n",
                    options_en: ["<p>ACBD</p>\n", "<p>DBCA</p>\n", 
                                "<p>CBDA</p>\n", "<p>BCDA</p>\n"],
                    options_hi: ["<p>ACBD</p>\n", "<p>DBCA</p>\n",
                                "<p>CBDA</p>\n", "<p>BCDA</p>\n"],
                    solution_en: "<p>15.(a) <span style=\"font-family: Cambria Math;\">ACBD </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Sentence A will be the starting line as it introduces the main idea of the parajumble i.e. </span><span style=\"font-family: Cambria Math;\">My years in my father\'s house would have been unbearable. </span><span style=\"font-family: Cambria Math;\">However, Sentence C states that if </span><span style=\"font-family: Cambria Math;\">I not had my brother.</span><span style=\"font-family: Cambria Math;\"> So, C will follow A. Further, Sentence B states that </span><span style=\"font-family: Cambria Math;\">I never forgot the feel of his hand clutching mine</span><span style=\"font-family: Cambria Math;\"> and Sentence D states that </span><span style=\"font-family: Cambria Math;\">his refusal to abandon me</span><span style=\"font-family: Cambria Math;\">. So, D will follow B. Going through the options, option a has the correct sequence.</span></p>\n",
                    solution_hi: "<p>15.(a)<span style=\"font-family: Cambria Math;\">ACBD </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Sentence A </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2366;&#2352;&#2306;&#2349;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> line </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2379;&#2327;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2351;&#2379;&#2306;&#2325;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2311;&#2360;&#2350;&#2375;</span><span style=\"font-family: Cambria Math;\"> parajumble </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2369;&#2326;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2330;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> &lsquo;</span><span style=\"font-family: Cambria Math;\">My years in my father\'s house would have been unbearable&rsquo; </span><span style=\"font-family: Nirmala UI;\">&#2358;&#2366;&#2350;&#2367;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2366;&#2354;&#2366;&#2305;&#2325;&#2367;</span><span style=\"font-family: Cambria Math;\">, Sentence C </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2361;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2327;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2352;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2366;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2349;&#2366;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2361;&#2368;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2379;&#2340;&#2366;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\">, A, </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2366;&#2342;</span><span style=\"font-family: Cambria Math;\"> C </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2319;&#2327;&#2366;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2327;&#2375;</span><span style=\"font-family: Cambria Math;\">, Sentence B </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2361;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2376;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2360;&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2366;&#2341;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2352;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2366;&#2341;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2325;&#2337;&#2364;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2349;&#2366;&#2357;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2349;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2361;&#2368;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2349;&#2370;&#2354;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> Sentence D </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2361;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2360;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2369;&#2333;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2381;&#2351;&#2366;&#2327;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2311;&#2344;&#2325;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2367;&#2351;&#2366;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\">, B, </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2366;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">D </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2319;&#2327;&#2366;&#2404;</span><span style=\"font-family: Cambria Math;\"> options </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2343;&#2381;&#2351;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2366;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> , option a </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2361;&#2368;</span><span style=\"font-family: Cambria Math;\"> sequence </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "7",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">16. </span><span style=\"font-family: Cambria Math;\">Choose the word that can substitute the given group of words. </span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">A person who has given up their beliefs</span></strong></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">16. </span><span style=\"font-family: Cambria Math;\">Choose the word that can substitute the given group of words. </span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">A person who has given up their beliefs</span></strong></p>\n",
                    options_en: ["<p>Misanthrope</p>\n", "<p>Versatile</p>\n", 
                                "<p>Apostate</p>\n", "<p>Crusade</p>\n"],
                    options_hi: ["<p>Misanthrope</p>\n", "<p>Versatile</p>\n",
                                "<p>Apostate</p>\n", "<p>Crusade</p>\n"],
                    solution_en: "<p>16.(c)</p>\r\n<p><span style=\"font-family: Cambria Math;\">Apostate -A person who has given up their beliefs</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Misanthrope -</span><span style=\"font-family: Cambria Math;\">a person who hates or avoids other people</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Versatile - </span><span style=\"font-family: Cambria Math;\">capable of or adapted for turning easily from one to another of various tasks, fields of endeavor</span><span style=\"font-family: Cambria Math;\">, etc.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Crusade - </span><span style=\"font-family: Cambria Math;\">a fight for something that you believe to be good or against something that you believe to be bad</span></p>\n",
                    solution_hi: "<p>16.(c)</p>\r\n<p><span style=\"font-family: Cambria Math;\">Apostate(</span><span style=\"font-family: Nirmala UI;\">&#2360;&#2381;&#2357;&#2343;&#2352;&#2381;&#2350;&#2340;&#2381;&#2351;&#2366;&#2327;&#2368;</span><span style=\"font-family: Cambria Math;\">) -A person who has given up their beliefs</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Misanthrope(</span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2344;&#2357;&#2342;&#2381;&#2357;&#2375;&#2359;&#2368;</span><span style=\"font-family: Cambria Math;\">) -</span><span style=\"font-family: Cambria Math;\">a person who hates or avoids other people</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Versatile(</span><span style=\"font-family: Nirmala UI;\">&#2348;&#2361;&#2369;&#2350;&#2369;&#2326;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2349;&#2366;&#2357;&#2366;&#2344;&#2381;</span><span style=\"font-family: Cambria Math;\">) - </span><span style=\"font-family: Cambria Math;\">capable of or adapted for turning easily from one to another of various tasks, fields of endeavor</span><span style=\"font-family: Cambria Math;\">, etc.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Crusade(</span><span style=\"font-family: Nirmala UI;\">&#2343;&#2352;&#2381;&#2350;&#2351;&#2369;&#2342;&#2381;&#2343;</span><span style=\"font-family: Cambria Math;\">) - </span><span style=\"font-family: Cambria Math;\">a fight for something that you believe to be good or against something that you believe to be bad</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "7",
                    question_en: "<p>17. <span style=\"font-family: Cambria Math;\">Parts of the following sentence are given as options. Identify the part that contains a grammatical error. If there is no error, select \"No error\'.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Shyam, so good with weapons, fail to find the words / to tell him that everything he\'d&nbsp; </span><span style=\"font-family: Cambria Math;\">done / so far, all the compromises he\'d made, had been for love.</span></p>\n",
                    question_hi: "<p>17. <span style=\"font-family: Cambria Math;\">Parts of the following sentence are given as options. Identify the part that contains a grammatical error. If there is no error, select \"No error\'.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Shyam, so good with weapons, fail to find the words / to tell him that everything he\'d&nbsp; </span><span style=\"font-family: Cambria Math;\">done / so far, all the compromises he\'d made, had been for love.</span></p>\n",
                    options_en: ["<p>to tell him that everything he\'d done</p>\n", "<p>Shyam, so good with weapons, fail to find the words</p>\n", 
                                "<p>so far, all the compromises he\'d made, had been for love.</p>\n", "<p>No error</p>\n"],
                    options_hi: ["<p>to tell him that everything he\'d done</p>\n", "<p>Shyam, so good with weapons, fail to find the words</p>\n",
                                "<p>so far, all the compromises he\'d made, had been for love.</p>\n", "<p>No error</p>\n"],
                    solution_en: "<p>17.(b)</p>\r\n<p><span style=\"font-family: Cambria Math;\">According to the &ldquo;</span><span style=\"text-decoration: underline;\"><span style=\"font-family: Cambria Math;\">Subject-Verb Agreement Rule</span></span><span style=\"font-family: Cambria Math;\">&rdquo;, a singular subject always takes a singular verb and a plural subject always takes a plural verb. In the given sentence, &lsquo;</span><span style=\"font-family: Cambria Math;\">Shyam</span><span style=\"font-family: Cambria Math;\">&rsquo; is a singular subject that will take &lsquo;fails&rsquo; as a singular verb. Hence, &lsquo;</span><span style=\"font-family: Cambria Math;\">Shyam, so good with weapons, fails to find the words</span><span style=\"font-family: Cambria Math;\">&rsquo; is the most appropriate answer.</span></p>\n",
                    solution_hi: "<p>17.(b)</p>\r\n<p><span style=\"font-family: Cambria Math;\">&ldquo;</span><span style=\"text-decoration: underline;\"><span style=\"font-family: Cambria Math;\">Subject-Verb Agreement Rule</span></span><span style=\"font-family: Cambria Math;\">&rdquo; </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2344;&#2369;&#2360;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\">, singular subject </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2350;&#2375;&#2358;&#2366;</span><span style=\"font-family: Cambria Math;\"> singular verb </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2375;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> plural subject </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2350;&#2375;&#2358;&#2366;</span><span style=\"font-family: Cambria Math;\"> plural verb </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2375;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2319;</span><span style=\"font-family: Cambria Math;\"> sentence </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\">, &lsquo;</span><span style=\"font-family: Cambria Math;\">Shyam</span><span style=\"font-family: Cambria Math;\">&rsquo; </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> singular subject </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2379;</span><span style=\"font-family: Cambria Math;\"> &lsquo;fails&rsquo; </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> singular verb </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2352;&#2370;&#2346;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2375;&#2327;&#2366;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2311;&#2360;&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\">,&lsquo;</span><span style=\"font-family: Cambria Math;\">Shyam, so good with weapons, fails to find the words</span><span style=\"font-family: Cambria Math;\">&rsquo; </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2348;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2346;&#2351;&#2369;&#2325;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2340;&#2381;&#2340;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "7",
                    question_en: "<p>18. <span style=\"font-family: Cambria Math;\">Parts of the following sentence are given as options. Identify the part that contains a grammatical error. If there is no error, select \"No error\'. </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">That will truly/been a milestone to/celebrate.</span></p>\n",
                    question_hi: "<p>18. <span style=\"font-family: Cambria Math;\">Parts of the following sentence are given as options. Identify the part that contains a grammatical error. If there is no error, select \"No error\'. </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">That will truly/been a milestone to/celebrate.</span></p>\n",
                    options_en: ["<p>No error</p>\n", "<p>celebrate.</p>\n", 
                                "<p>been a milestone to</p>\n", "<p>That will truly</p>\n"],
                    options_hi: ["<p>No error</p>\n", "<p>celebrate.</p>\n",
                                "<p>been a milestone to</p>\n", "<p>That will truly</p>\n"],
                    solution_en: "<p>18.(c)</p>\r\n<p><span style=\"font-family: Cambria Math;\">The verb &lsquo;been&rsquo; will be replaced with &lsquo;be&rsquo; because the given sentence is giving a futuristic sense. Hence, &lsquo;That will truly be a milestone&rsquo; is the most appropriate answer.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                    solution_hi: "<p>18.(c)</p>\r\n<p><span style=\"font-family: Cambria Math;\">verb \'been\' </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> \'be\' </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2342;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2366;&#2319;&#2327;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2351;&#2379;&#2306;&#2325;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> sentence futuristic </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2379;&#2343;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2352;&#2361;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2311;&#2360;&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\">, &lsquo;That will truly be a milestone&rsquo; </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2348;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2346;&#2351;&#2369;&#2325;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2340;&#2381;&#2340;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "7",
                    question_en: "<p>19. <span style=\"font-family: Cambria Math;\">Select the word that is closest in meaning (SYNONYM) to the word given below.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">THRIFTY</span></strong></p>\n",
                    question_hi: "<p>19. <span style=\"font-family: Cambria Math;\">Select the word that is closest in meaning (SYNONYM) to the word given below.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">THRIFTY</span></strong></p>\n",
                    options_en: ["<p>PRUDENT</p>\n", "<p>WASTEFUL</p>\n", 
                                "<p>GENEROUS</p>\n", "<p>EXTRAVAGANT</p>\n"],
                    options_hi: ["<p>PRUDENT</p>\n", "<p>WASTEFUL</p>\n",
                                "<p>GENEROUS</p>\n", "<p>EXTRAVAGANT</p>\n"],
                    solution_en: "<p>19.(a)</p>\r\n<p><span style=\"font-family: Cambria Math;\">THRIFTY -</span><span style=\"font-family: Cambria Math;\">given to or marked by economy and good management</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">WASTEFUL -</span><span style=\"font-family: Cambria Math;\">using more of something than necessary; causing waste</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">GENEROUS - </span><span style=\"font-family: Cambria Math;\">happy to give more money, help, etc. than is usual or expected</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">EXTRAVAGANT - </span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">spending too much money</span><span style=\"font-family: Cambria Math;\">,</span></p>\n",
                    solution_hi: "<p>19.(a)</p>\r\n<p><span style=\"font-family: Cambria Math;\">THRIFTY(</span><span style=\"font-family: Nirmala UI;\">&#2350;&#2367;&#2340;&#2357;&#2381;&#2351;&#2351;&#2368;</span><span style=\"font-family: Cambria Math;\">) -</span><span style=\"font-family: Cambria Math;\">given to or marked by economy and good management</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">WASTEFUL(</span><span style=\"font-family: Nirmala UI;\">&#2361;&#2366;&#2344;&#2367;&#2325;&#2366;&#2352;&#2325;</span><span style=\"font-family: Cambria Math;\">) -</span><span style=\"font-family: Cambria Math;\">using more of something than necessary; causing waste</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">GENEROUS(</span><span style=\"font-family: Nirmala UI;\">&#2342;&#2366;&#2344;&#2358;&#2368;&#2354;</span><span style=\"font-family: Cambria Math;\">) - </span><span style=\"font-family: Cambria Math;\">happy to give more money, help, etc. than is usual or expected</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">EXTRAVAGANT(</span><span style=\"font-family: Nirmala UI;\">&#2309;&#2346;&#2357;&#2381;&#2351;&#2351;&#2368;</span><span style=\"font-family: Cambria Math;\">) - </span><span style=\"font-family: Cambria Math;\">spending too much money</span><span style=\"font-family: Cambria Math;\">,</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "7",
                    question_en: "<p>20. <span style=\"font-family: Cambria Math;\">Choose the correctly spelt word.</span></p>\n",
                    question_hi: "<p>20. <span style=\"font-family: Cambria Math;\">Choose the correctly spelt word.</span></p>\n",
                    options_en: ["<p>TEMPORERY</p>\n", "<p>CAMOFLAGUE</p>\n", 
                                "<p>PSEUDONYM</p>\n", "<p>TABBOO</p>\n"],
                    options_hi: ["<p>TEMPORERY</p>\n", "<p>CAMOFLAGUE</p>\n",
                                "<p>PSEUDONYM</p>\n", "<p>TABBOO</p>\n"],
                    solution_en: "<p>20.(c)&rdquo;<span style=\"font-family: Cambria Math;\">PSEUDONYM&rdquo; is the correct spelling.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">camouflage - to disguise</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">TABOO - that is avoided or forbidden for religious or social reasons</span></p>\n",
                    solution_hi: "<p>20.(c)&rdquo;<span style=\"font-family: Cambria Math;\">PSEUDONYM&rdquo; </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2361;&#2368;</span><span style=\"font-family: Cambria Math;\"> spelling </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "7",
                    question_en: "<p>21. <strong>Cloze Test:-</strong></p>\r\n<p><span style=\"font-family: Cambria Math;\">The man with the shotgun (21)_________</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> the guard to his knees. He handed up the shotgun to </span><span style=\"font-family: Cambria Math;\">his partner and yanked the guard\'s wrists up behind his back and locked (22)___________</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> together with a pair of handcuffs. He (23)___________</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> him onto the floor with a kick between the shoulder blades. Then he took his shotgun back and (24)_________</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> to the security gate at the end of the counter. He was short and heavy and moved with peculiar slowness. \"Buzz him (25)____________ </span><span style=\"font-family: \'Cambria Math\';\">\"his partner said.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Select the most appropriate option to fill in the blank No. 21.</span></p>\n",
                    question_hi: "<p>21.&nbsp;<strong> Cloze Test:-</strong></p>\r\n<p><span style=\"font-family: Cambria Math;\">The man with the shotgun (21)_________</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> the guard to his knees. He handed up the shotgun to </span><span style=\"font-family: Cambria Math;\">his partner and yanked the guard\'s wrists up behind his back and locked (22)___________</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> together with a pair of handcuffs. He (23)___________</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> him onto the floor with a kick between the shoulder blades. Then he took his shotgun back and (24)_________</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> to the security gate at the end of the counter. He was short and heavy and moved with peculiar slowness. \"Buzz him (25)____________ </span><span style=\"font-family: \'Cambria Math\';\">\"his partner said.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">Select the most appropriate option to fill in the blank No. 21.</span></p>\n",
                    options_en: ["<p>steered</p>\n", "<p>pushed</p>\n", 
                                "<p>motivated</p>\n", "<p>swayed</p>\n"],
                    options_hi: ["<p>steered</p>\n", "<p>pushed</p>\n",
                                "<p>motivated</p>\n", "<p>swayed</p>\n"],
                    solution_en: "<p>21.(b)</p>\r\n<p><span style=\"font-family: Cambria Math;\">&lsquo;Pushed&rsquo; means </span><span style=\"font-family: Cambria Math;\">to move something in a specified way by exerting force</span><span style=\"font-family: Cambria Math;\">. The given passage states that </span><span style=\"font-family: Cambria Math;\">the man with the shotgun </span><span style=\"font-family: Cambria Math;\">pushed</span><span style=\"font-family: Cambria Math;\"> the guard to his knees</span><span style=\"font-family: Cambria Math;\">. Hence, &lsquo;</span><span style=\"font-family: Cambria Math;\">pushed</span><span style=\"font-family: Cambria Math;\">&rsquo; is the most appropriate answer.</span></p>\n",
                    solution_hi: "<p>21.(b)</p>\r\n<p><span style=\"font-family: Cambria Math;\">&lsquo;Pushed&rsquo; </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2352;&#2381;&#2341;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2327;&#2366;&#2325;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2360;&#2381;&#2340;&#2369;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2367;&#2352;&#2381;&#2342;&#2367;&#2359;&#2381;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2352;&#2368;&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2367;&#2354;&#2366;&#2344;&#2366;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2319;</span><span style=\"font-family: Cambria Math;\"> passage </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2361;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2344;&#2381;&#2342;&#2370;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2366;&#2354;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2381;&#2351;&#2325;&#2381;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2366;&#2352;&#2381;&#2337;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2346;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2328;&#2369;&#2335;&#2344;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2343;&#2325;&#2375;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2367;&#2351;&#2366;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2311;&#2360;&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\">, &lsquo;</span><span style=\"font-family: Cambria Math;\">pushed</span><span style=\"font-family: Cambria Math;\">&rsquo; </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2348;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2346;&#2351;&#2369;&#2325;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2340;&#2381;&#2340;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "7",
                    question_en: "<p>22. <strong>Cloze Test:-</strong></p>\r\n<p><span style=\"font-family: Cambria Math;\">The man with the shotgun (21)_________</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> the guard to his knees. He handed up the shotgun to </span><span style=\"font-family: Cambria Math;\">his partner and yanked the guard\'s wrists up behind his back and locked (22)___________</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> together with a pair of handcuffs. He (23)___________</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> him onto the floor with a kick between the shoulder blades. Then he took his shotgun back and (24)_________</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> to the security gate at the end of the counter. He was short and heavy and moved with peculiar slowness. \"Buzz him (25)____________ </span><span style=\"font-family: \'Cambria Math\';\">\"his partner said.</span></p>\r\n<p><span style=\"font-family: \'Cambria Math\';\">Select the most appropriate option to fill in the blank No. 22.</span></p>\n",
                    question_hi: "<p>22.&nbsp; <strong>Cloze Test:-</strong></p>\r\n<p><span style=\"font-family: Cambria Math;\">The man with the shotgun (21)_________</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> the guard to his knees. He handed up the shotgun to </span><span style=\"font-family: Cambria Math;\">his partner and yanked the guard\'s wrists up behind his back and locked (22)___________</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> together with a pair of handcuffs. He (23)___________</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> him onto the floor with a kick between the shoulder blades. Then he took his shotgun back and (24)_________</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> to the security gate at the end of the counter. He was short and heavy and moved with peculiar slowness. \"Buzz him (25)____________ </span><span style=\"font-family: \'Cambria Math\';\">\"his partner said.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> Select the most appropriate option to fill in the blank No. 22.</span></p>\n",
                    options_en: ["<p>them</p>\n", "<p>that</p>\n", 
                                "<p>these</p>\n", "<p>It</p>\n"],
                    options_hi: ["<p>them</p>\n", "<p>that</p>\n",
                                "<p>these</p>\n", "<p>It</p>\n"],
                    solution_en: "<p>22.(a)</p>\r\n<p><span style=\"font-family: Cambria Math;\">The pronoun &lsquo;</span><span style=\"font-family: Cambria Math;\">them</span><span style=\"font-family: Cambria Math;\">&rsquo; means </span><span style=\"font-family: Cambria Math;\">the people or things mentioned earlier</span><span style=\"font-family: Cambria Math;\">. The given passage states that he l</span><span style=\"font-family: Cambria Math;\">ocked them together with a pair of handcuffs</span><span style=\"font-family: Cambria Math;\">. Hence, &lsquo;</span><span style=\"font-family: Cambria Math;\">them</span><span style=\"font-family: Cambria Math;\">&rsquo; is the most appropriate answer.</span></p>\n",
                    solution_hi: "<p>22.(a)</p>\r\n<p><span style=\"font-family: Cambria Math;\">Pronoun &lsquo;</span><span style=\"font-family: Cambria Math;\">them</span><span style=\"font-family: Cambria Math;\">&rsquo; </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2352;&#2381;&#2341;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2361;&#2354;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2340;&#2366;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2379;&#2327;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2330;&#2368;&#2332;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2319;</span><span style=\"font-family: Cambria Math;\"> passage </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2361;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2360;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2344;&#2381;&#2361;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2379;&#2337;&#2364;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2341;&#2325;&#2337;&#2364;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2366;&#2341;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2306;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2367;&#2351;&#2366;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2311;&#2360;&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\">, &lsquo;</span><span style=\"font-family: Cambria Math;\">them</span><span style=\"font-family: Cambria Math;\">&rsquo; </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2348;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2346;&#2351;&#2369;&#2325;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2340;&#2381;&#2340;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "23",
                    section: "7",
                    question_en: "<p>23.&nbsp; <strong>Cloze Test:-</strong></p>\r\n<p><span style=\"font-family: Cambria Math;\">The man with the shotgun (21)_________</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> the guard to his knees. He handed up the shotgun to </span><span style=\"font-family: Cambria Math;\">his partner and yanked the guard\'s wrists up behind his back and locked (22)___________</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> together with a pair of handcuffs. He (23)___________</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> him onto the floor with a kick between the shoulder blades. Then he took his shotgun back and (24)_________</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> to the security gate at the end of the counter. He was short and heavy and moved with peculiar slowness. \"Buzz him (25)____________ </span><span style=\"font-family: \'Cambria Math\';\">\"his partner said.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Select the most appropriate option to fill in the blank No.23.</span></p>\n",
                    question_hi: "<p>23. <strong>Cloze Test:-</strong></p>\r\n<p><span style=\"font-family: Cambria Math;\">The man with the shotgun (21)_________</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> the guard to his knees. He handed up the shotgun to </span><span style=\"font-family: Cambria Math;\">his partner and yanked the guard\'s wrists up behind his back and locked (22)___________</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> together with a pair of handcuffs. He (23)___________</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> him onto the floor with a kick between the shoulder blades. Then he took his shotgun back and (24)_________</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> to the security gate at the end of the counter. He was short and heavy and moved with peculiar slowness. \"Buzz him (25)____________ </span><span style=\"font-family: \'Cambria Math\';\">\"his partner said.</span></p>\r\n<p><span style=\"font-family: \'Cambria Math\';\">Select the most appropriate option to fill in the blank No.23.</span></p>\n",
                    options_en: ["<p>toppled</p>\n", "<p>ascended</p>\n", 
                                "<p>placed</p>\n", "<p>straightened</p>\n"],
                    options_hi: ["<p>toppled</p>\n", "<p>ascended</p>\n",
                                "<p>placed</p>\n", "<p>straightened</p>\n"],
                    solution_en: "<p>23.(a)</p>\r\n<p><span style=\"font-family: Cambria Math;\">&lsquo;</span><span style=\"font-family: Cambria Math;\">Toppled</span><span style=\"font-family: Cambria Math;\">&rsquo; means </span><span style=\"font-family: Cambria Math;\">to fall down on the ground</span><span style=\"font-family: Cambria Math;\">. The given passage states that </span><span style=\"font-family: Cambria Math;\">he toppled him onto the floor with a kick between the shoulder blades</span><span style=\"font-family: Cambria Math;\">. Hence, &lsquo;</span><span style=\"font-family: Cambria Math;\">toppled</span><span style=\"font-family: Cambria Math;\">&rsquo; is the most appropriate answer.</span></p>\n",
                    solution_hi: "<p>23.(a)</p>\r\n<p><span style=\"font-family: Cambria Math;\">&lsquo;</span><span style=\"font-family: Cambria Math;\">Toppled</span><span style=\"font-family: Cambria Math;\">&rsquo; </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2352;&#2381;&#2341;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2350;&#2368;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2367;&#2352;&#2344;&#2366;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2319;</span><span style=\"font-family: Cambria Math;\"> passage </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2361;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2360;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2306;&#2343;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2381;&#2354;&#2375;&#2337;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2368;&#2330;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2352;&#2325;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2347;&#2352;&#2381;&#2358;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2367;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2367;&#2351;&#2366;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2311;&#2360;&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\">, &lsquo;</span><span style=\"font-family: Cambria Math;\">toppled</span><span style=\"font-family: Cambria Math;\">&rsquo; </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2348;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2346;&#2351;&#2369;&#2325;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2340;&#2381;&#2340;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "24",
                    section: "7",
                    question_en: "<p>24.&nbsp; <strong>Cloze Test:-</strong></p>\r\n<p><span style=\"font-family: Cambria Math;\">The man with the shotgun (21)_________</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> the guard to his knees. He handed up the shotgun to </span><span style=\"font-family: Cambria Math;\">his partner and yanked the guard\'s wrists up behind his back and locked (22)___________</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> together with a pair of handcuffs. He (23)___________</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> him onto the floor with a kick between the shoulder blades. Then he took his shotgun back and (24)_________</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> to the security gate at the end of the counter. He was short and heavy and moved with peculiar slowness. \"Buzz him (25)____________ </span><span style=\"font-family: \'Cambria Math\';\">\"his partner said.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> Select the most appropriate option to fill in the blank No. 24.</span></p>\n",
                    question_hi: "<p>24. <strong>Cloze Test:-</strong></p>\r\n<p><span style=\"font-family: Cambria Math;\">The man with the shotgun (21)_________</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> the guard to his knees. He handed up the shotgun to </span><span style=\"font-family: Cambria Math;\">his partner and yanked the guard\'s wrists up behind his back and locked (22)___________</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> together with a pair of handcuffs. He (23)___________</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> him onto the floor with a kick between the shoulder blades. Then he took his shotgun back and (24)_________</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> to the security gate at the end of the counter. He was short and heavy and moved with peculiar slowness. \"Buzz him (25)____________ </span><span style=\"font-family: \'Cambria Math\';\">\"his partner said.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> Select the most appropriate option to fill in the blank No. 24.</span></p>\n",
                    options_en: ["<p>went over</p>\n", "<p>went under</p>\n", 
                                "<p>went along</p>\n", "<p>went besides</p>\n"],
                    options_hi: ["<p>went over</p>\n", "<p>went under</p>\n",
                                "<p>went along</p>\n", "<p>went besides</p>\n"],
                    solution_en: "<p>24.(a)</p>\r\n<p><span style=\"font-family: Cambria Math;\">The given passage states that </span><span style=\"font-family: Cambria Math;\">he took his shotgun back and went over to the security gate</span><span style=\"font-family: Cambria Math;\">. Hence, &lsquo;</span><span style=\"font-family: Cambria Math;\">went over</span><span style=\"font-family: Cambria Math;\">&rsquo; is the most appropriate answer.</span></p>\n",
                    solution_hi: "<p>24.(a)</p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2342;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2319;</span><span style=\"font-family: Cambria Math;\"> passage </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2361;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2361;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2346;&#2344;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2344;&#2381;&#2342;&#2370;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2366;&#2346;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2369;&#2352;&#2325;&#2381;&#2359;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2381;&#2357;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2330;&#2354;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2351;&#2366;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2311;&#2360;&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\">, &lsquo;</span><span style=\"font-family: Cambria Math;\">went over</span><span style=\"font-family: Cambria Math;\">&rsquo; </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2348;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2346;&#2351;&#2369;&#2325;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2340;&#2381;&#2340;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "25",
                    section: "misc",
                    question_en: "<p>25. <strong>Cloze Test:-</strong></p>\r\n<p><span style=\"font-family: Cambria Math;\">The man with the shotgun (21)_________</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> the guard to his knees. He handed up the shotgun to </span><span style=\"font-family: Cambria Math;\">his partner and yanked the guard\'s wrists up behind his back and locked (22)___________</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> together with a pair of handcuffs. He (23)___________</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> him onto the floor with a kick between the shoulder blades. Then he took his shotgun back and (24)_________</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> to the security gate at the end of the counter. He was short and heavy and moved with peculiar slowness. \"Buzz him (25)____________ </span><span style=\"font-family: \'Cambria Math\';\">\"his partner said.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> Select the most appropriate option to fill in the blank No.25.</span></p>\n",
                    question_hi: "<p>25. <strong>Cloze Test:-</strong></p>\r\n<p><span style=\"font-family: Cambria Math;\">The man with the shotgun (21)_________</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> the guard to his knees. He handed up the shotgun to </span><span style=\"font-family: Cambria Math;\">his partner and yanked the guard\'s wrists up behind his back and locked (22)___________</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> together with a pair of handcuffs. He (23)___________</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> him onto the floor with a kick between the shoulder blades. Then he took his shotgun back and (24)_________</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> to the security gate at the end of the counter. He was short and heavy and moved with peculiar slowness. \"Buzz him (25)____________ </span><span style=\"font-family: \'Cambria Math\';\">\"his partner said.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Select the most appropriate option to fill in the blank No.25.</span></p>\n",
                    options_en: ["<p>to</p>\n", "<p>on</p>\n", 
                                "<p>with</p>\n", "<p>in</p>\n"],
                    options_hi: ["<p>to</p>\n", "<p>on</p>\n",
                                "<p>with</p>\n", "<p>in</p>\n"],
                    solution_en: "<p>25.(d)</p>\r\n<p><span style=\"font-family: Cambria Math;\">&lsquo;Buzz in&rsquo; means to allow someone to enter a door. The given passage states that </span><span style=\"font-family: Cambria Math;\">his partner told him Buzz in.</span><span style=\"font-family: Cambria Math;\"> Hence, &lsquo;in&rsquo; is the most appropriate answer.</span></p>\n",
                    solution_hi: "<p>25.(d)</p>\r\n<p><span style=\"font-family: Cambria Math;\">&lsquo;Buzz in&rsquo; </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2352;&#2381;&#2341;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2352;&#2357;&#2366;&#2332;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2357;&#2375;&#2358;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2344;&#2369;&#2350;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2375;&#2344;&#2366;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2319;</span><span style=\"font-family: Cambria Math;\"> passage </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2361;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2360;&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2366;&#2341;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">Buzz in</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2361;&#2366;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2311;&#2360;&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\">, &lsquo;in&rsquo; </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2348;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2346;&#2351;&#2369;&#2325;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2340;&#2381;&#2340;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>