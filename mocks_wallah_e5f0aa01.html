<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #7209b7;
            --secondary: #4361ee;
            --accent: #f72585;
            --success: #06d6a0;
            --warning: #ffd166;
            --danger: #ef476f;
            --light: #f8f9fa;
            --dark: #212529;
        }
        body {
            background-color: #f0f2f5;
            font-size: 16px;
            padding-bottom: 60px; /* Space for fixed navigation */
        }
        .navbar {
            background: linear-gradient(135deg, var(--primary), var(--secondary)) !important;
            box-shadow: 0 2px 10px rgba(0,0,0,.1);
            padding: 0.5rem 1rem;
        }
        .navbar-brand {
            font-size: 1.1rem;
            margin-right: 0;
        }
        .navbar .container-fluid {
            justify-content: space-between;
        }
        .controls-group {
            display: flex;
            align-items: center;
            flex-wrap: nowrap;
        }
        .timer-display {
            background: linear-gradient(135deg, #4361ee, #3a0ca3);
            color: white;
            border-radius: 50px;
            box-shadow: 0 3px 8px rgba(0,0,0,.1);
            padding: 0.25rem 0.75rem;
            font-size: 0.875rem;
            white-space: nowrap;
        }
        .nav-btn {
            padding: 0.25rem 0.75rem;
            font-size: 0.875rem;
            margin-left: 0.25rem;
        }
        .question-nav {
            position: fixed;
            top: 56px;
            right: 0;
            height: calc(100vh - 56px);
            width: 280px;
            transform: translateX(280px);
            transition: transform .3s;
            z-index: 100;
            background: #fff;
            box-shadow: -5px 0 15px rgba(0,0,0,.1);
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .main-content {
            transition: margin .3s;
            min-height: calc(100vh - 116px);
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.5;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(32px, 1fr));
            gap: 6px;
        }
        .q-box {
            width: 35px;
            height: 35px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 5px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid #dee2e6;
            background: #fff;
            transition: all .2s;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: 0 3px 5px rgba(0,0,0,.1);
        }
        .q-box.attempted {
            background-color: var(--secondary);
            color: white;
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,.15);
        }
        .card {
            border-radius: 12px;
            overflow: hidden;
            border: none;
            box-shadow: 0 4px 12px rgba(0,0,0,.08);
        }
        .option-label {
            border-radius: 8px;
            transition: all .2s;
            border: 1px solid #dee2e6;
            padding: 0.75rem 1rem;
            font-size: 1rem;
        }
        .option-label:hover {
            background-color: #f8f9fa;
            transform: translateY(-2px);
            box-shadow: 0 3px 8px rgba(0,0,0,.05);
        }
        .form-check-input:checked + .option-label {
            border-color: var(--primary);
            background-color: rgba(114,9,183,.05);
        }
        .btn-primary {
            background-color: var(--primary);
            border-color: var(--primary);
        }
        .btn-outline-primary {
            color: var(--primary);
            border-color: var(--primary);
        }
        .btn-outline-primary:hover {
            background-color: var(--primary);
            color: white;
        }
        /* Bottom fixed navigation for mobile */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: white;
            box-shadow: 0 -2px 10px rgba(0,0,0,.1);
            padding: 10px 15px;
            display: flex;
            justify-content: space-between;
            z-index: 90;
        }
        @media (min-width: 992px) {
            .main-content.with-nav {
                margin-right: 280px;
            }
            .question-nav {
                transform: translateX(0);
            }
            .nav-toggle-btn {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(30px, 1fr));
            }
            .option-label {
                padding: 0.625rem 0.875rem;
                font-size: 0.95rem;
            }
            .question-text {
                font-size: 1rem;
            }
            .navbar-brand {
                font-size: 1rem;
            }
            .timer-display {
                font-size: 0.8125rem;
                padding: 0.2rem 0.6rem;
            }
            .nav-btn {
                padding: 0.2rem 0.6rem;
                font-size: 0.8125rem;
            }
            .container {
                padding-left: 10px;
                padding-right: 10px;
            }
        }
    </style>
</head>
<body>
    <!-- Header navbar -->
    <nav class="navbar navbar-dark sticky-top">
        <div class="container-fluid px-2">
            <a class="navbar-brand fw-bold" href="#"><i class="fas fa-graduation-cap me-1"></i></a>
            <div class="controls-group">
                <!-- Timer -->
                <div class="timer-display me-1 fw-bold" id="timer">
                    <i class="far fa-clock me-1"></i><span id="timer-display">15:00</span>
                </div>
                <!-- Submit button -->
                <button class="btn btn-light btn-sm nav-btn me-1" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Language toggle -->
                <button class="btn btn-light btn-sm nav-btn me-1" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="btn btn-light btn-sm nav-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </nav>
    <!-- Main content -->
    <div class="main-content" id="main-content">
        <div class="container py-3">
            <!-- Question counter -->
            <div class="fw-bold text-center mb-3" id="question-counter">Question 1 of 25</div>
            <!-- Questions will be displayed here -->
            <div id="questions-container"></div>
        </div>
    </div>
    <!-- Bottom navigation for mobile -->
    <div class="bottom-nav d-lg-none">
        <button class="btn btn-outline-primary px-3" onclick="prevQuestion()">
            <i class="fas fa-chevron-left me-1"></i> Previous
        </button>
        <button class="btn btn-outline-primary px-3" onclick="nextQuestion()">
            Next <i class="fas fa-chevron-right ms-1"></i>
        </button>
    </div>
    <!-- Question navigation sidebar -->
    <div class="question-nav" id="question-nav">
        <div class="p-3">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h5 class="m-0">Questions</h5>
                <div>
                    <span class="badge bg-secondary me-1" id="attempted-count">0</span>
                    <span class="badge bg-secondary" id="total-count">25</span>
                    <button class="btn btn-sm btn-outline-secondary ms-2 d-lg-none" onclick="toggleNav()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
            <!-- Section selector moved here -->
            <div class="mb-3">
                <label for="section-selector" class="form-label small text-muted">Jump to Section</label>
                <select class="form-select form-select-sm" id="section-selector" onchange="switchSection(this.value)">
                    <option value="all">All Sections</option>
                </select>
            </div>
            <div class="question-grid" id="question-boxes"></div>
        </div>
    </div>
    <!-- Results modal -->
    <div class="modal fade" id="results-modal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content border-0">
                <div class="modal-header border-0" style="background:linear-gradient(135deg,var(--primary),var(--secondary));color:white">
                    <h5 class="modal-title">Test Results</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body p-4">
                    <div class="row text-center g-2">
                        <div class="col-6">
                            <div class="p-3 rounded shadow-sm" style="background:linear-gradient(135deg,#4361ee,#3a0ca3);color:white">
                                <h3 id="score-value" class="mb-0 fw-bold">0</h3>
                                <div class="small">Score</div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="p-3 rounded shadow-sm" style="background:linear-gradient(135deg,#06d6a0,#1b9aaa);color:white">
                                <h3 id="correct-value" class="mb-0 fw-bold">0</h3>
                                <div class="small">Correct</div>
                            </div>
                        </div>
                        <div class="col-6 mt-2">
                            <div class="p-3 rounded shadow-sm" style="background:linear-gradient(135deg,#ef476f,#f72585);color:white">
                                <h3 id="incorrect-value" class="mb-0 fw-bold">0</h3>
                                <div class="small">Incorrect</div>
                            </div>
                        </div>
                        <div class="col-6 mt-2">
                            <div class="p-3 rounded shadow-sm" style="background:linear-gradient(135deg,#6c757d,#495057);color:white">
                                <h3 id="unattempted-value" class="mb-0 fw-bold">0</h3>
                                <div class="small">Unattempted</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer border-0">
                    <button type="button" class="btn btn-primary" onclick="reviewTest()">Review Test</button>
                </div>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 15 * 60;
        let resultsModal;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            resultsModal = new bootstrap.Modal(document.getElementById('results-modal'));
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["6"] = {
                name: "Reasoning",
                start: 0,
                end: 23
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="6">Reasoning</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 24,
                end: 24
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "6",
                    question_en: "<p>1. In a certain code, 6218 means &lsquo;Laptop is a computer&rsquo; and 8217 means &lsquo;Computer is a machine&rsquo;. Which of the following is the code for &lsquo;Machine makes our life easy&rsquo;?</p>",
                    question_hi: "<p>1. <span style=\"font-family: Palanquin Dark;\">एक निश्चित कूट भाषा में, 6218 का अर्थ है &lsquo;Laptop is a computer&rsquo; और 8217 का अर्थ है &lsquo;Computer is a machine&rsquo;. &lsquo;Machine makes our life easy&rsquo; के लिए निम्नलिखित कौन से कूट का प्रयोग किया जायेगा?</span></p>",
                    options_en: ["<p>75344</p>", "<p>75894</p>", 
                                "<p>58795</p>", "<p>76834</p>"],
                    options_hi: ["<p>75344</p>", "<p>75894</p>",
                                "<p>58795</p>", "<p>76834</p>"],
                    solution_en: "<p>1.(a) <strong><span style=\"font-family: Palanquin Dark;\">Logic</span></strong><span style=\"font-family: Palanquin Dark;\"> : Number of alphabet in the given word.</span><br><span style=\"font-family: Palanquin Dark;\">&nbsp;In a certain code,</span><br><span style=\"font-family: Palanquin Dark;\">&lsquo;Laptop is a computer &lsquo;6218</span><br><span style=\"font-family: Palanquin Dark;\">&lsquo;Computer is a machine &lsquo;8217&lsquo;</span><br><span style=\"font-family: Palanquin Dark;\">&lsquo;Machine makes our life easy&rsquo; 75344&rsquo;</span></p>",
                    solution_hi: "<p>1.(a) <span style=\"font-family: Palanquin Dark;\">तर्क: दिए गए शब्द में वर्णमाला की संख्या</span><br><span style=\"font-family: Palanquin Dark;\">एक निश्चित कोड में,</span><br><span style=\"font-family: Palanquin Dark;\">&lsquo;Laptop is a computer &lsquo;6218</span><br><span style=\"font-family: Palanquin Dark;\">&lsquo;Computer is a machine &lsquo;8217&lsquo;</span><br><span style=\"font-family: Palanquin Dark;\">&lsquo;Machine makes our life easy&rsquo; 75344&rsquo;</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "6",
                    question_en: "<p>2.<span style=\"font-family: Palanquin Dark;\"> Six letters D, d, E, e, F, f, are written on the different faces of a dice. Two positions of this dice are shown. Select the letter that will be on the face opposite to the face having letter &lsquo;D&rsquo;.</span><br><span style=\"font-family: Palanquin Dark;\"> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1665484840/word/media/image28.png\"></p>",
                    question_hi: "<p><span style=\"font-family: Palanquin Dark;\">2. </span><span style=\"font-family: Palanquin Dark;\">एक पासे के विभिन्न फलकों पर छह अक्षर D, d, E, e, F, f लिखे गए हैं। इस पासे की दो स्थितियाँ दिखाई गई हैं। उस अक्षर का चयन करें जो \'D\' अक्षर वाले फलक के विपरीत फलकपर होगा।</span><br><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1665484840/word/media/image28.png\"></p>",
                    options_en: ["<p>E</p>", "<p>f</p>", 
                                "<p>e</p>", "<p>d</p>"],
                    options_hi: ["<p>E</p>", "<p>f</p>",
                                "<p>e</p>", "<p>d</p>"],
                    solution_en: "<p>2.(b)<strong> <span style=\"font-family: Palanquin Dark;\">Logic :</span></strong><br><span style=\"font-family: Palanquin Dark;\">Find out common letters on both dice . And move either clockwise or anticlockwise. Here we c an see if we take &ldquo;e&rsquo; as a common letter on both sides of dice. And then move in either clockwise or anticlockwise direction. We find out that d, f are opposite to each other.</span><br><strong id=\"docs-internal-guid-85eea5db-7fff-c35a-3314-46109e4f8964\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfLStXQRv4McvIMFJNmNsrL5R6ZkDjO-eKvJYM-7bbvLdPio9W9-GHeFJ2_iP_GAClildMKiYnhoN4G5-lUF0_Vu76cBJ_fdipnMdP9qGZozpIKrXfq3255l-87CSzAsBUne1QVk1cgWMvryKkMQ4hVmk9t?key=3CG07PQ74LV66ubMJGDHTg\" width=\"122\" height=\"118\"></strong></p>",
                    solution_hi: "<p>2.(b) <strong><span style=\"font-family: Palanquin Dark;\">तर्क :</span></strong><br><span style=\"font-family: Palanquin Dark;\">दोनों पासों पर उभयनिष्ठ अक्षर ज्ञात कीजिए और या तो दक्षिणावर्त या वामावर्त दिशा में आगे बढ़ें। यहाँ हम देख सकते हैं कि क्या हम पासे के दोनों ओर \"e\" को उभयनिष्ठ अक्षर के रूप में लेते हैं और फिर दक्षिणावर्त या वामावर्त दिशा में आगे बढ़ें। हम पाते हैं कि d, f एक दूसरे के विपरीत हैं।</span><br><strong id=\"docs-internal-guid-85eea5db-7fff-c35a-3314-46109e4f8964\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfLStXQRv4McvIMFJNmNsrL5R6ZkDjO-eKvJYM-7bbvLdPio9W9-GHeFJ2_iP_GAClildMKiYnhoN4G5-lUF0_Vu76cBJ_fdipnMdP9qGZozpIKrXfq3255l-87CSzAsBUne1QVk1cgWMvryKkMQ4hVmk9t?key=3CG07PQ74LV66ubMJGDHTg\" width=\"122\" height=\"118\"></strong></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "6",
                    question_en: "<p>3. <span style=\"font-family: Palanquin Dark;\">Nine employees, K, L, M, N, O, P, Q, R and S, are sitting in the office in a straight line, all facing the east. O is third to the right of M. K is third to the left of P. Q is between P and S. N is fourth to the right of M. P is to the immediate right of N. R is fourth to the left of O. Which of the following statements is NOT correct?</span></p>",
                    question_hi: "<p><span style=\"font-family: Palanquin Dark;\">3. </span><span style=\"font-family: Palanquin Dark;\">नौ कर्मचारी, K, L, M, N, O, P, Q, R और S, एक सीधी रेखा में पूर्व की और मुख करके बैठे है। O, M के दायें से तीसरे स्थान पर है। K, P के बायें से तीसरे स्थान पर है। Q, P और S के बीच में है।N, M के दायें चौथा है। P, N के ठीक दायें है।R, O के बायें चौथा है।निम्नलिखित में से कौन सा कथन सही नहीं है?</span></p>",
                    options_en: ["<p>R and S are sitting at the corners.</p>", "<p>L is sitting third to the left of O.</p>", 
                                "<p>Q is sitting fourth to the right of K.</p>", "<p>O is sitting between K and N.</p>"],
                    options_hi: ["<p>R और S कोनों पर बैठे हैं।</p>", "<p>L, O के बायें से तीसरे स्थान पर बैठा है।</p>",
                                "<p>Q, K के दायें चौथे स्थान पर बैठा है।</p>", "<p>O, K और N के बीच बैठा है।</p>"],
                    solution_en: "<p>3.(b) <span style=\"font-family: Palanquin Dark;\">Arrange given conditions. All are facing in the east direction.</span><br><strong id=\"docs-internal-guid-04d562bc-7fff-b9ad-793b-c9a2cd6b1eb8\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfRA28U6L7ZcqwH3LyWwZtxccfFBrp02c_76knmZUCqpyEnjUjWR9Yq-bYqLKpCvpCmIfxiiUpTk4An9iJjgGFviJho9bI9aIRyWpCKKX2lpKg3QQs0svkQVYuOJdSdg0tGdMK7iUADls5QKuZRjae_T6g?key=mF2bi77WvZKcLSzGVIdZIFCj\" width=\"299\" height=\"45\"></strong><br><span style=\"font-family: Palanquin Dark;\">We can see in figure only option (b) which is wrong.</span></p>",
                    solution_hi: "<p>3.(b) <span style=\"font-family: Palanquin Dark;\">दी गई स्थिति की व्यवस्था करें। सभी का मुख पूर्व दिशा की ओर है।</span><br><strong id=\"docs-internal-guid-04d562bc-7fff-b9ad-793b-c9a2cd6b1eb8\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfRA28U6L7ZcqwH3LyWwZtxccfFBrp02c_76knmZUCqpyEnjUjWR9Yq-bYqLKpCvpCmIfxiiUpTk4An9iJjgGFviJho9bI9aIRyWpCKKX2lpKg3QQs0svkQVYuOJdSdg0tGdMK7iUADls5QKuZRjae_T6g?key=mF2bi77WvZKcLSzGVIdZIFCj\" width=\"299\" height=\"45\"></strong><br><span style=\"font-family: Palanquin Dark;\">हम केवल आकृति विकल्प (b) में देख सकते हैं जो गलत है।</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "6",
                    question_en: "<p>4.<span style=\"font-family: Palanquin Dark;\"> The sequence of folding a piece of paper and the manner in which the folded paper has been cut is shown in the figure. How would this paper look when unfolded?</span><br><span style=\"font-family: Palanquin Dark;\"> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1665484840/word/media/image36.png\" width=\"297\" height=\"92\"></p>",
                    question_hi: "<p><span style=\"font-family: Palanquin Dark;\">4. </span><span style=\"font-family: Palanquin Dark;\">निम्नलिखित आकृतियों में कागज के एक टुकड़े को मोड़ने का क्रम और जिस तरीके से मुड़े हुए कागज को काटा गया है उसे चित्र में दिखाया गया है। खोलने पर यह कागज़कैसा दिखेगा?</span><br><span style=\"font-family: Palanquin Dark;\"> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1665484840/word/media/image36.png\" width=\"301\" height=\"93\"></p>",
                    options_en: ["<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1665484840/word/media/image34.png\" width=\"85\" height=\"82\"></p>", "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1665484840/word/media/image13.png\" width=\"85\" height=\"82\"></p>", 
                                "<p><strong id=\"docs-internal-guid-7d1c9e59-7fff-8b0a-9c37-d41578699a22\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXe6pZhFPM0raqb9Jjsbiok-idSWr-dskYPuMLM83LAl6_PQre5JVNY_dmPl61ER9r04wxO59G4SrrrObusKz9lRFu-bAHdMAYpiwwWz7FBIU9ralA_HDC4PJbcZ6GqeR9G0dqXdkw?key=3CG07PQ74LV66ubMJGDHTg\" width=\"80\" height=\"75\"></strong></p>", "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1665484840/word/media/image30.png\" width=\"85\" height=\"82\"></p>"],
                    options_hi: ["<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1665484840/word/media/image34.png\" width=\"85\" height=\"82\"></p>", "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1665484840/word/media/image13.png\" width=\"85\" height=\"82\"></p>",
                                "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1665484840/word/media/image13.png\" width=\"85\" height=\"82\"></p>", "<p><strong id=\"docs-internal-guid-7d1c9e59-7fff-8b0a-9c37-d41578699a22\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXe6pZhFPM0raqb9Jjsbiok-idSWr-dskYPuMLM83LAl6_PQre5JVNY_dmPl61ER9r04wxO59G4SrrrObusKz9lRFu-bAHdMAYpiwwWz7FBIU9ralA_HDC4PJbcZ6GqeR9G0dqXdkw?key=3CG07PQ74LV66ubMJGDHTg\" width=\"80\" height=\"75\"></strong></p>"],
                    solution_en: "<p>4.(d) <span style=\"font-family: Palanquin Dark;\">The unfolded paper is as follows.</span><br><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1665484840/word/media/image30.png\" width=\"85\" height=\"82\"></p>",
                    solution_hi: "<p>4.(d) <span style=\"font-family: Palanquin Dark;\">खुला हुआ पेपर इस प्रकार है:</span><br><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1665484840/word/media/image30.png\" width=\"85\" height=\"82\"></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "6",
                    question_en: "<p>5.<span style=\"font-family: Palanquin Dark;\"> Four letter-clusters have been given, out of which three are alike in some manner and one is different. Select the one that is different.</span></p>",
                    question_hi: "<p><span style=\"font-family: Palanquin Dark;\">5. </span><span style=\"font-family: Palanquin Dark;\">चार अक्षर-समूह दिए गए हैं, जिनमें से तीन किसी न किसी रूप में एक जैसे हैं और एक अलग है। जो अलग है उसका चयन करे ।</span></p>",
                    options_en: ["<p>TWY</p>", "<p>GJM</p>", 
                                "<p>LOQ</p>", "<p>BEG</p>"],
                    options_hi: ["<p>TWY</p>", "<p>GJM</p>",
                                "<p>LOQ</p>", "<p>BEG</p>"],
                    solution_en: "<p>5.(b) <span style=\"font-family: Arial Unicode MS;\">Except option (b)</span><br><span style=\"font-family: Arial Unicode MS;\">T, T + 3 = W , W + 2 = Y</span><br><strong><span style=\"font-family: Arial Unicode MS;\">G, G + 3 = J ,&nbsp; J + 3 + M</span></strong><br><span style=\"font-family: Arial Unicode MS;\">L , L + 3 = O , O + 2 = Q</span><br><span style=\"font-family: Arial Unicode MS;\">B , B + 3 = E , E + 2 = G</span></p>",
                    solution_hi: "<p>5.(b) विकल्प (b) को छोड़कर<br><span style=\"font-family: Arial Unicode MS;\">T, T + 3 = W , W + 2 = Y</span><br><strong><span style=\"font-family: Arial Unicode MS;\">G, G + 3 = J ,&nbsp; J + 3 + M</span></strong><br><span style=\"font-family: Arial Unicode MS;\">L , L + 3 = O , O + 2 = Q</span><br><span style=\"font-family: Arial Unicode MS;\">B , B + 3 = E , E + 2 = G</span></p>\n<p><span style=\"font-family: Arial Unicode MS;\"> </span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "6",
                    question_en: "<p>6.<span style=\"font-family: Arial Unicode MS;\"> Select the option that is related to the third term in the same way as the second term is related to the first term.</span><br><span style=\"font-family: Arial Unicode MS;\">HARMONY : BMLGUDK:: RESOLVE : ?</span></p>",
                    question_hi: "<pre><span style=\"font-family: Arial Unicode MS;\">6. </span><span style=\"font-family: Palanquin Dark;\">उस विकल्प का चयन करें जो तीसरे पद से उसी प्रकार संबंधित है जैसे दूसरा पद पहले पद से सम्बंधित है ।</span><br><span style=\"font-family: Palanquin Dark;\">HARMONY : BMLGUDK :: RESOLVE : ?</span></pre>",
                    options_en: ["<p>VEOIVHU</p>", "<p>OVEJUHV</p>", 
                                "<p>EVOJHVU</p>", "<p><span style=\"font-family: Arial Unicode MS;\">VOEIUVH</span><span style=\"font-family: Arial Unicode MS;\"> </span></p>"],
                    options_hi: ["<p>VEOIVHU</p>", "<p>OVEJUHV</p>",
                                "<p>EVOJHVU</p>", "<p><span style=\"font-family: Palanquin Dark;\">VOEIUVH</span><span style=\"font-family: Palanquin Dark;\"> </span></p>"],
                    solution_en: "<p>6.(a)<br><strong id=\"docs-internal-guid-3128af97-7fff-9002-4205-6cf038597529\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfa2e7P2oYTOqoY6gZWWt6vlGYlTKtdiDQVBBc8qvjIFUb51KEGuKwMABXyDeDpDcoxyJGzvpS9WN96aBJ9En7iEAozRj9tGqhxXgKfXcNtX4VkaziuHo_GKaDEldUETGg0ptDh?key=3CG07PQ74LV66ubMJGDHTg\" width=\"235\" height=\"113\"></strong><br><span style=\"font-family: Palanquin Dark;\">Similarly,</span><br><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXc-01F8Rl7zH5nITWB8ofO4lr0-BOKYjTBMPQ4hXX16Xl7_Nzui3ihE_UKCORStLFmvhG8rkCrSoha_GNbUGKTMhX21pGtaQJiNmYIJhaM88MsSXxkA0Gqx5wHFaE4fPAbAvUx8=w1920-h912?key=3CG07PQ74LV66ubMJGDHTg\" alt=\"Displaying \" width=\"301\" height=\"144\"></p>",
                    solution_hi: "<p>6.(a)<br><strong id=\"docs-internal-guid-612cfcf4-7fff-af11-f77e-a0bae22de7f3\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXeYr0BhC0O--evffBpb75w7yguNYquAVV3qPotZe-RuX5fVIXbrJQDRlTxfE8LLTMhMXpJPqVSeECm51ZNfUvs9SI_j9pbqL5AoiTXwlwMz59n24W_VjZcM2jahDlB89zoW1r55NQ?key=3CG07PQ74LV66ubMJGDHTg\" width=\"250\" height=\"121\"></strong><br><span style=\"font-family: Palanquin Dark;\">उसी प्रकार,</span><br><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXeqx0Sg1gb9p73cq5-VR_LMwRTPh1jDXHeRrvi0tfxP5cnXrfIWR_YysDMWq7cs6U_jUoH9EWfvVojpTIOuFnM_FskCBXQ8e2_V9__GODIbgC3Ib8vKwwZbvZdcHF86vGlZ_qWW=w1920-h912?key=3CG07PQ74LV66ubMJGDHTg\" alt=\"Displaying \" width=\"258\" height=\"124\"></p>\n<p><span style=\"font-family: Palanquin Dark;\"> </span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "6",
                    question_en: "<p>7.<span style=\"font-family: Palanquin Dark;\"> In a certain code language, if </span><span style=\"font-family: Palanquin Dark;\">ESTEEM</span><span style=\"font-family: Palanquin Dark;\"> is written as </span><span style=\"font-family: Palanquin Dark;\">HVWHHP</span><span style=\"font-family: Palanquin Dark;\">, then how will </span><span style=\"font-family: Palanquin Dark;\">DOCTOR </span><span style=\"font-family: Palanquin Dark;\">be written in the same code language?</span></p>",
                    question_hi: "<p><span style=\"font-family: Palanquin Dark;\">7. </span><span style=\"font-family: Palanquin Dark;\">एक निश्चित कूट भाषा में, ESTEEM को HVWHHP के रूप में लिखा जाता है, तो DOCTOR को उसी कूट भाषा में किस रूप में लिखा जायेगा?</span></p>",
                    options_en: ["<p><span style=\"font-family: Palanquin Dark;\">GRFWRU</span></p>", "<p><span style=\"font-family: Palanquin Dark;\">GRGVST</span></p>", 
                                "<p>FQEVQT</p>", "<p>HSGXSV</p>"],
                    options_hi: ["<p><span style=\"font-family: Palanquin Dark;\">GRFWRU</span></p>", "<p><span style=\"font-family: Palanquin Dark;\">GRGVST</span></p>",
                                "<p>FQEVQT</p>", "<p>HSGXSV</p>\n<p><span style=\"font-family: Palanquin Dark;\"> </span></p>"],
                    solution_en: "<p>7.(a) <span style=\"font-family: Palanquin Dark;\">Here we can see the +3 rule is used.</span><br><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1665484840/word/media/image31.png\"><br><span style=\"font-family: Palanquin Dark;\">Similar</span><br><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1665484840/word/media/image11.png\"></p>",
                    solution_hi: "<p>7.(a) <span style=\"font-family: Palanquin Dark;\">यहां हम देख सकते हैं कि +3 नियम का उपयोग किया जाता है।</span><br><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1665484840/word/media/image31.png\"><br><span style=\"font-family: Palanquin Dark;\">उसी प्रकार</span><br><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1665484840/word/media/image11.png\"></p>\n<p><span style=\"font-family: Palanquin Dark;\"> </span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "6",
                    question_en: "<p>8.<span style=\"font-family: Palanquin Dark;\"> If A denotes &lsquo;addition&rsquo;, B denotes &lsquo;multiplication&rsquo;, C denotes &lsquo;subtraction&rsquo;, and D denotes &lsquo;division&rsquo;, then what will be the value of the following expression?</span><br><span style=\"font-family: Palanquin Dark;\">66 A (132 D 12) C (4 A 3) B (15 D 5) A 16 B (&ndash;3)</span></p>",
                    question_hi: "<p><span style=\"font-family: Palanquin Dark;\">8. </span><span style=\"font-family: Palanquin Dark;\">यदि A जोड़ को, B गुणा को,C घटाना को तथा D भाग को दर्शाता है, तो निम्न व्यंजक का मान क्या होगा</span><br><span style=\"font-family: Palanquin Dark;\">66 A (132 D 12) C (4 A 3) B (15 D 5) A 16 B (&ndash;3)</span></p>",
                    options_en: ["<p>8</p>", "<p>10</p>", 
                                "<p>6</p>", "<p>56</p>"],
                    options_hi: ["<p>8</p>", "<p>10</p>",
                                "<p>6</p>", "<p>56</p>"],
                    solution_en: "<p>8.(a) <span style=\"font-family: Palanquin Dark;\">Given that</span><br><span style=\"font-family: Palanquin Dark;\">A denotes &lsquo;+&rsquo;&nbsp; ,&nbsp; </span><span style=\"font-family: Arial Unicode MS;\">B denotes &lsquo;&times;&rsquo;&nbsp; ,&nbsp; </span><span style=\"font-family: Arial Unicode MS;\">C denotes &lsquo;&ndash;&rsquo;&nbsp; ,&nbsp; </span><span style=\"font-family: Arial Unicode MS;\">D denotes &divide;&lsquo;</span><span style=\"font-family: Arial Unicode MS;\">&rsquo;</span><br><span style=\"font-family: Arial Unicode MS;\">Put the sign in given equation&nbsp;&nbsp;</span><br><span style=\"font-family: Arial Unicode MS;\">66 + (132 &divide;</span><span style=\"font-family: Arial Unicode MS;\">12) &ndash; (4 + 3) &times; (15&divide;&nbsp;</span><span style=\"font-family: Arial Unicode MS;\"> 5) + 16 &times; ( &ndash; 3) = 8</span></p>",
                    solution_hi: "<p>8.(a) <span style=\"font-family: Palanquin Dark;\">मान लीजिये</span><br><span style=\"font-family: Palanquin Dark;\">A </span><span style=\"font-family: Palanquin Dark;\"> \'+\' को दर्शाता है&nbsp; , </span><span style=\"font-family: Palanquin Dark;\">B </span><span style=\"font-family: Arial Unicode MS;\"> \'&times;\' को दर्शाता है&nbsp; ,</span><span style=\"font-family: Arial Unicode MS;\">C </span><span style=\"font-family: Palanquin Dark;\"> \'-\' को दर्शाता है&nbsp; , </span><span style=\"font-family: Palanquin Dark;\">D </span><span style=\"font-family: Palanquin Dark;\"> </span><span style=\"font-family: Palanquin Dark;\">&lsquo;&divide;</span><span style=\"font-family: Palanquin Dark;\">&rsquo; </span><span style=\"font-family: Palanquin Dark;\">को दर्शाता है</span><br><span style=\"font-family: Palanquin Dark;\">दिए गए समीकरण में चिह्न लगाएं</span><br><span style=\"font-family: Palanquin Dark;\">66 + (132 &divide;</span><span style=\"font-family: Arial Unicode MS;\">12) &ndash; (4 + 3) &times; (15&divide;&nbsp;</span><span style=\"font-family: Arial Unicode MS;\"> 5) + 16 &times; ( &ndash; 3) = 8</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "6",
                    question_en: "<p>9.<span style=\"font-family: Arial Unicode MS;\"> If \'@\' means \'addition\', \'%\' means \'multiplication\', \'$\' means \'division\', and \'#\' means \'subtraction\', then find the value of the following expression.</span><br><span style=\"font-family: Arial Unicode MS;\">23 @ 105 $ 15 % 6 # 29</span></p>",
                    question_hi: "<p><span style=\"font-family: Arial Unicode MS;\">9. </span><span style=\"font-family: Palanquin Dark;\">यदि &lsquo;@&rsquo; का अर्थ &lsquo;जोड़&rsquo; है , &lsquo;%&rsquo; का अर्थ &lsquo;गुणा&rsquo; है, &lsquo;$&rsquo; का अर्थ &lsquo;भाग&rsquo; है, &lsquo;#&rsquo; का अर्थ &lsquo;घटाना&rsquo;, तो निम्न व्यंजक का मान ज्ञात कीजिए</span><br><span style=\"font-family: Palanquin Dark;\">23 @ 105 $ 15 % 6 # 29</span></p>",
                    options_en: ["<p>23</p>", "<p>28</p>", 
                                "<p>36</p>", "<p>40</p>"],
                    options_hi: ["<p>23</p>", "<p>28</p>",
                                "<p>36</p>", "<p>40</p>"],
                    solution_en: "<p>9.(c) <span style=\"font-family: Palanquin Dark;\">Given that&nbsp; &nbsp;(@) = + ,&nbsp; &nbsp;</span><span style=\"font-family: \'Palanquin Dark\';\">$ = &divide; ,&nbsp; </span><span style=\"font-family: \'Arial Unicode MS\';\">% = &times;,&nbsp; </span><span style=\"font-family: \'Arial Unicode MS\';\">#&nbsp; =&nbsp; &ndash;</span><br><span style=\"font-family: Arial Unicode MS;\">Convert the sign into mathematical operation. And solve the equation</span><br><span style=\"font-family: Arial Unicode MS;\">23+105 &divide;</span><span style=\"font-family: Arial Unicode MS;\">15 &times;6 &ndash; 29 = 36</span></p>",
                    solution_hi: "<p>9.(c) <span style=\"font-family: Palanquin Dark;\">दिया गया है कि&nbsp; &nbsp; (@) = + ,&nbsp; </span><span style=\"font-family: \'Palanquin Dark\';\">$ = &divide; ,&nbsp; </span><span style=\"font-family: \'Arial Unicode MS\';\">% =&nbsp; &times; , </span><span style=\"font-family: \'Arial Unicode MS\';\"># = &ndash;</span><br><span style=\"font-family: Palanquin Dark;\">चिन्ह को गणितीय संक्रिया में बदलें और समीकरण को हल करें।</span><br><span style=\"font-family: Palanquin Dark;\">23 + 105 &divide;</span><span style=\"font-family: Arial Unicode MS;\">15 &times; 6 &ndash; 29 = 36</span></p>\n<p><span style=\"font-family: Arial Unicode MS;\"> </span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "6",
                    question_en: "<p>10.<span style=\"font-family: Arial Unicode MS;\"> Select the combination of letters that when sequentially placed in the blanks of the given series will complete the series.</span><br><span style=\"font-family: Arial Unicode MS;\">g _ o _ d k _ m _ f _ o _ k k _ g f o _ d _ k m</span></p>",
                    question_hi: "<p><span style=\"font-family: Arial Unicode MS;\">10. </span><span style=\"font-family: Palanquin Dark;\">अक्षरों के उस संयोजन का चयन करें जिसे दी गई श्रृंखला के रिक्त स्थानों में क्रमिक रूप से रखने पर श्रृंखला पूरी हो जाएगी।</span><br><span style=\"font-family: Palanquin Dark;\">g _ o _ d k _ m _ f _ o _ k k _ g f o _ d _ k m</span></p>",
                    options_en: ["<p><span style=\"font-family: Arial Unicode MS;\">f o k d o d m o k</span></p>", "<p><span style=\"font-family: Arial Unicode MS;\">f o k g o d m o k</span></p>", 
                                "<p>f o k g o d f o k</p>", "<p>f o k g o d m o m</p>"],
                    options_hi: ["<p><span style=\"font-family: Palanquin Dark;\">f o k d o d m o k</span></p>", "<p><span style=\"font-family: Palanquin Dark;\">f o k g o d m o k</span></p>",
                                "<p>f o k g o d f o k</p>", "<p>f o k g o d m o m</p>"],
                    solution_en: "<p>10.(b) <span style=\"font-family: Palanquin Dark;\">Given series is as</span><br><span style=\"font-family: Palanquin Dark;\">g-o-dk-m-f-o-kk-gfo-d - km</span><br><span style=\"font-family: Palanquin Dark;\">gfoodkkm / </span><span style=\"font-family: Palanquin Dark;\">gfoodkkm</span><span style=\"font-family: Palanquin Dark;\"> / </span><span style=\"font-family: Palanquin Dark;\">gfoodkkm</span></p>",
                    solution_hi: "<p>10.(b) <span style=\"font-family: Palanquin Dark;\">दी गई श्रंखला इस प्रकार है</span><br><span style=\"font-family: Palanquin Dark;\">g-o-dk-m-f-o-kk-gfo-d - km</span><br><span style=\"font-family: Palanquin Dark;\">gfoodkkm /&nbsp;</span><span style=\"font-family: Palanquin Dark;\">gfoodkkm</span><span style=\"font-family: Palanquin Dark;\"> / </span><span style=\"font-family: Palanquin Dark;\">gfoodkkm</span></p>\n<p><span style=\"font-family: Palanquin Dark;\"> </span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "6",
                    question_en: "<p>11.<span style=\"font-family: Palanquin Dark;\"> Select the correct mirror image of the given figure when the mirror is placed at &lsquo;PQ&rsquo; as shown.</span><br><span style=\"font-family: Palanquin Dark;\"> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1665484840/word/media/image4.png\" width=\"89\" height=\"127\"></p>",
                    question_hi: "<p><span style=\"font-family: Palanquin Dark;\">11. </span><span style=\"font-family: Palanquin Dark;\">जब दर्पण को निम्नलिखित चित्र के अनुसार PQ पर रखा गया हो, तब दिए गए संयोजन का दर्पण में प्रतिबिम्ब चयनित करे</span><br><span style=\"font-family: Palanquin Dark;\"> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1665484840/word/media/image4.png\" width=\"95\" height=\"135\"></p>",
                    options_en: ["<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1665484840/word/media/image17.png\" width=\"82\" height=\"80\"><span style=\"font-family: Palanquin Dark;\"> </span></p>", "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1665484840/word/media/image33.png\" width=\"82\" height=\"80\"></p>", 
                                "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1665484840/word/media/image8.png\" width=\"82\" height=\"80\"><span style=\"font-family: Palanquin Dark;\"> </span></p>", "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1665484840/word/media/image24.png\" width=\"82\" height=\"80\"></p>"],
                    options_hi: ["<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1665484840/word/media/image17.png\" width=\"82\" height=\"80\"><span style=\"font-family: Palanquin Dark;\"> </span></p>", "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1665484840/word/media/image33.png\" width=\"82\" height=\"80\"></p>",
                                "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1665484840/word/media/image8.png\" width=\"82\" height=\"80\"><span style=\"font-family: Palanquin Dark;\"> </span></p>", "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1665484840/word/media/image24.png\" width=\"82\" height=\"80\"></p>"],
                    solution_en: "<p>11.(b) <span style=\"font-family: Palanquin Dark;\">The correct mirror image is</span><br><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1665484840/word/media/image33.png\" width=\"82\" height=\"80\"></p>",
                    solution_hi: "<p>11.(b) <span style=\"font-family: Palanquin Dark;\">सही दर्पण प्रतिबिम्ब है</span><br><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1665484840/word/media/image33.png\" width=\"82\" height=\"80\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "6",
                    question_en: "<p>12.<span style=\"font-family: Palanquin Dark;\"> Select the letter-cluster from among the given options that can replace the question mark (?) in the following series.</span><br><span style=\"font-family: Palanquin Dark;\">RSX, NQW, JOV, FMU, ?</span></p>",
                    question_hi: "<p><span style=\"font-family: Palanquin Dark;\">12. </span><span style=\"font-family: Palanquin Dark;\">&nbsp;दिए गए विकल्पों में से वह संख्या चुनिए जो निम्नलिखित श्रृंखला में प्रश्नवाचक चिन्ह (?) को प्रतिस्थापित कर सके।</span><br><span style=\"font-family: Palanquin Dark;\">RSX, NQW, JOV, FMU, ?</span></p>",
                    options_en: ["<p>BLT</p>", "<p>DKS</p>", 
                                "<p>CJS</p>", "<p>BKT</p>"],
                    options_hi: ["<p>BLT</p>", "<p>DKS</p>",
                                "<p>CJS</p>", "<p>BKT</p>"],
                    solution_en: "<p>12.(d)<br><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1665484840/word/media/image43.png\" width=\"295\" height=\"80\"></p>",
                    solution_hi: "<p>12.(d)<br><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1665484840/word/media/image43.png\" width=\"284\" height=\"77\"></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "6",
                    question_en: "<p>13.<span style=\"font-family: Palanquin Dark;\"> Select the correct option that indicates the arrangement of the given words in a logical and meaningful order.</span><br><span style=\"font-family: Palanquin Dark;\">1. Core</span><br><span style=\"font-family: Palanquin Dark;\">2. Atmosphere</span><br><span style=\"font-family: Palanquin Dark;\">3. Universe</span><br><span style=\"font-family: Palanquin Dark;\">4. Surface</span><br><span style=\"font-family: Palanquin Dark;\">5. Galaxy </span></p>",
                    question_hi: "<p><span style=\"font-family: Palanquin Dark;\">13. </span><span style=\"font-family: Palanquin Dark;\">उस सही विकल्प का चयन करें जो दिए गए शब्दों की व्यवस्था को तार्किक और सार्थक आदेश में इंगित करता है।</span><br><span style=\"font-family: Palanquin Dark;\">1. Core</span><br><span style=\"font-family: Palanquin Dark;\">2. Atmosphere</span><br><span style=\"font-family: Palanquin Dark;\">3. Universe</span><br><span style=\"font-family: Palanquin Dark;\">4. Surface</span><br><span style=\"font-family: Palanquin Dark;\">5. Galaxy </span></p>",
                    options_en: ["<p>1, 4, 2, 5, 3</p>", "<p>4, 3, 5, 2, 1</p>", 
                                "<p>1, 4, 2, 3, 5</p>", "<p>1, 3, 4, 5, 2</p>"],
                    options_hi: ["<p>1, 4, 2, 5, 3</p>", "<p>4, 3, 5, 2, 1</p>",
                                "<p>1, 4, 2, 3, 5</p>", "<p>1, 3, 4, 5, 2</p>"],
                    solution_en: "<p>13.(a) <span style=\"font-family: Palanquin Dark;\">The meaningful order is 1, 4, 2, 5, 3. </span></p>",
                    solution_hi: "<p>13.(a) <span style=\"font-family: Palanquin Dark;\">सार्थक क्रम 1, 4, 2, 5, 3 है।</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "6",
                    question_en: "<p>14.<span style=\"font-family: Palanquin Dark;\"> Select the option that is related to the third number in the same way as the second number is related to the first number and the sixth number is related to the fifth Number.</span><br><span style=\"font-family: Palanquin Dark;\">21 : 112 :: 36 : ? :: 51 : 272 </span></p>",
                    question_hi: "<p><span style=\"font-family: Palanquin Dark;\">14. </span><span style=\"font-family: Palanquin Dark;\">उस विकल्प का चयन करें जो तीसरी संख्या से उसी प्रकार संबंधित है जैसे दूसरी संख्या पहली संख्या से संबंधित है और छठी संख्या पांचवें से संबंधित है</span><br><span style=\"font-family: Palanquin Dark;\">21 : 112 :: 36 : ? :: 51 : 272 </span></p>",
                    options_en: ["<p>192</p>", "<p>252</p>", 
                                "<p>72</p>", "<p>198</p>\n<p><span style=\"font-family: Palanquin Dark;\"> </span></p>"],
                    options_hi: ["<p>192</p>", "<p>252</p>",
                                "<p>72</p>", "<p>198</p>"],
                    solution_en: "<p>14.(a) <span style=\"font-family: Palanquin Dark;\"><strong>Logic </strong>:</span><span style=\"font-family: Palanquin Dark;\"> (3N : : 16 N)</span><br><span style=\"font-family: Palanquin Dark;\">So (36 : :192)</span><br><span style=\"font-family: Palanquin Dark;\">(3&times;12::12&times;16)</span></p>",
                    solution_hi: "<p>14.(a) <span style=\"font-family: Palanquin Dark;\"><strong>तर्क</strong> :</span><span style=\"font-family: Palanquin Dark;\"> (3N : : 16 N)</span><br><span style=\"font-family: Palanquin Dark;\">इसलिए (36 : :192)</span><br><span style=\"font-family: Palanquin Dark;\">(3&times;12::12&times;16)</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "6",
                    question_en: "<p>15.<span style=\"font-family: Arial Unicode MS;\"> The average salary of the entire teaching staff in a college is ₹2,000 per day. The average salary of the male teachers is ₹2,500 and that of the female teachers is ₹1,200. If the number of male teachers is 16, then find the number of female teachers in the College.</span></p>",
                    question_hi: "<p><span style=\"font-family: Arial Unicode MS;\">15. </span><span style=\"font-family: Palanquin Dark;\">एक कॉलेज में पूरे शिक्षण स्टाफ का औसत वेतन ₹2,000 प्रति दिन है। पुरुष शिक्षकों का औसत वेतन ₹2,500 है और महिला शिक्षकों का औसत वेतन ₹1,200 है। यदि पुरुष शिक्षकों की संख्या 16 है, तो कॉलेज में महिला शिक्षकों की संख्या ज्ञात कीजिए।</span></p>",
                    options_en: ["<p>12</p>", "<p>10</p>", 
                                "<p>18</p>", "<p>14</p>"],
                    options_hi: ["<p>12</p>", "<p>10</p>",
                                "<p>18</p>", "<p>14</p>"],
                    solution_en: "<p>15.(b) <span style=\"font-family: Palanquin Dark;\">According to the question given . </span><span style=\"font-family: Palanquin Dark;\">that</span><span style=\"font-family: Palanquin Dark;\"> average salary of the entire staff is 2000. Average salary of male </span><span style=\"font-family: Palanquin Dark;\">teacher 2500</span><span style=\"font-family: Palanquin Dark;\"> rs /day. And salary of female teacher is 1200 rs/ day</span><br><span style=\"font-family: Palanquin Dark;\">By allegation</span><br><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1665484840/word/media/image45.png\"></p>",
                    solution_hi: "<p>15.(b) <span style=\"font-family: Palanquin Dark;\">दिए गए प्रश्न के अनुसार पूरे स्टाफ का औसत वेतन 2000 है।</span><br><span style=\"font-family: Palanquin Dark;\">पुरुष शिक्षक का औसत वेतन 2500 रुपये / दिन है।</span><br><span style=\"font-family: Palanquin Dark;\">और महिला शिक्षक का वेतन 1200 रुपये / दिन है</span><br><span style=\"font-family: Palanquin Dark;\">एलीगेशन द्वारा</span><br><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1665484840/word/media/image45.png\"></p>\n<p><span style=\"font-family: Palanquin Dark;\"> </span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "6",
                    question_en: "<p>16.<span style=\"font-family: Palanquin Dark;\"> Study the given matrix carefully and select the number from among the given options that can replace the question mark (?) in it.</span><br><span style=\"font-family: Palanquin Dark;\"><strong id=\"docs-internal-guid-8b4221a9-7fff-4a7d-a79d-c092f6591bac\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfGvawwVV7i1fdlXt9ob5rIF4DNi2v22xjETyjDcvocZjxx8MZZPg74d9ENKl5Z_q5otyb0PEk71eSGgsfsFyckcRsH6ZDrFTV4evOd6q6BZr4saH2XDKTPG2Cly7PPIQUlSbki?key=3CG07PQ74LV66ubMJGDHTg\" width=\"306\" height=\"117\"></strong></span></p>",
                    question_hi: "<p><span style=\"font-family: Palanquin Dark;\">16. </span><span style=\"font-family: Palanquin Dark;\">दिए गए मैट्रिक्स का ध्यानपूर्वक अध्ययन करें और दिए गए विकल्पों में से संख्या का चयन करें जो इसमें प्रश्नवाचक चिन्ह (?) को प्रतिस्थापित कर सकता है।</span><br><strong id=\"docs-internal-guid-8b4221a9-7fff-4a7d-a79d-c092f6591bac\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfGvawwVV7i1fdlXt9ob5rIF4DNi2v22xjETyjDcvocZjxx8MZZPg74d9ENKl5Z_q5otyb0PEk71eSGgsfsFyckcRsH6ZDrFTV4evOd6q6BZr4saH2XDKTPG2Cly7PPIQUlSbki?key=3CG07PQ74LV66ubMJGDHTg\" width=\"337\" height=\"129\"></strong></p>",
                    options_en: ["<p>102</p>", "<p>108</p>", 
                                "<p>62</p>", "<p>78</p>"],
                    options_hi: ["<p>102</p>", "<p>108</p>",
                                "<p>62</p>", "<p>78</p>"],
                    solution_en: "<p>16.(c) <strong><span style=\"font-family: Palanquin Dark;\">Logic :</span></strong><br>[(first number ✖ second number +( second number <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#247;</mo></math>2)]<br><span style=\"font-family: Arial Unicode MS;\">Like ( 12 &times; 8) + (8<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#247;</mo></math></span><span style=\"font-family: Arial Unicode MS;\">2) = 100</span><br><span style=\"font-family: Arial Unicode MS;\">So (15 &times; 4) + (4<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#247;</mo></math></span><span style=\"font-family: Arial Unicode MS;\">2) = 62</span></p>",
                    solution_hi: "<p>16.(c) <strong><span style=\"font-family: Palanquin Dark;\">तर्क </span><span style=\"font-family: Palanquin Dark;\">:</span></strong><br><span style=\"font-family: Arial Unicode MS;\"><strong id=\"docs-internal-guid-356e492b-7fff-1e3d-d027-aad201c5912f\">[(पहला अंक ✖ दूसरा अंक + (दूसरा अंक &divide; 2)]</strong></span><br><span style=\"font-family: Arial Unicode MS;\">जैसे (12 &times; 8) + (8<strong id=\"docs-internal-guid-356e492b-7fff-1e3d-d027-aad201c5912f\">&divide; </strong></span><span style=\"font-family: Arial Unicode MS;\">2) = 100</span><br><span style=\"font-family: Arial Unicode MS;\">इसलिए (15 &times; 4) + (4 <strong id=\"docs-internal-guid-356e492b-7fff-1e3d-d027-aad201c5912f\">&divide; </strong></span><span style=\"font-family: Arial Unicode MS;\">2) = 62</span></p>\n<p><span style=\"font-family: Arial Unicode MS;\"> </span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "6",
                    question_en: "<p>17.<span style=\"font-family: Arial Unicode MS;\"> Read the given statements and conclusions carefully. Assuming that the information given in the statements is true, even if it appears to be at variance with commonly known facts, decide which of the given conclusions logically follow(s) from the statements.</span><br><span style=\"font-family: Arial Unicode MS;\"><strong>Statements</strong>:</span><br><span style=\"font-family: Arial Unicode MS;\">All bats are birds.</span><br><span style=\"font-family: Arial Unicode MS;\">Some rats are bats.</span><br><strong><span style=\"font-family: Arial Unicode MS;\">Conclusions:</span></strong><br><span style=\"font-family: Arial Unicode MS;\">I. Some rats are birds.</span><br><span style=\"font-family: Arial Unicode MS;\">II. Some birds are bats.</span><br><span style=\"font-family: Arial Unicode MS;\">III. All rats are bats.</span></p>",
                    question_hi: "<p><span style=\"font-family: Arial Unicode MS;\">17. </span><span style=\"font-family: Palanquin Dark;\">दिए गए कथनों और निष्कर्षों को ध्यानपूर्वक पढ़ें। यह मानते हुए कि कथनों में दी गई जानकारी सत्य है, भले ही वह सामान्य रूप से ज्ञात तथ्यों से भिन्न प्रतीत होती हो, बताइये कि दिए गए निष्कर्षों में से कौन सा कथनों का तार्किक रूप से अनुसरण करता है।</span><br><span style=\"font-family: Palanquin Dark;\"><strong>कथन</strong>:-</span><br><span style=\"font-family: Palanquin Dark;\">सभी चमगादड़ पक्षी हैं।</span><br><span style=\"font-family: Palanquin Dark;\">कुछ चूहे चमगादड़ हैं।</span><br><span style=\"font-family: Palanquin Dark;\"><strong>निष्कर्ष</strong>:</span><br><span style=\"font-family: Palanquin Dark;\">I. कुछ चूहे पक्षी हैं।</span><br><span style=\"font-family: Palanquin Dark;\">II. कुछ पक्षी चमगादड़ हैं।</span><br><span style=\"font-family: Palanquin Dark;\">III. सभी चूहे चमगादड़ हैं।</span></p>",
                    options_en: ["<p>Only conclusion II follows.</p>", "<p>Only conclusions I and II follow.</p>", 
                                "<p>Only conclusions I and III follow.</p>", "<p>Only conclusion I <span style=\"font-family: Arial Unicode MS;\">follows</span><span style=\"font-family: Arial Unicode MS;\">. </span></p>"],
                    options_hi: ["<p>केवल निष्कर्ष II अनुसरण करता है।</p>", "<p>केवल निष्कर्ष I और II अनुसरण करते हैं।</p>",
                                "<p>केवल निष्कर्ष I और III अनुसरण करते हैं।</p>", "<p>केवल निष्कर्ष I अनुसरण करता है।</p>"],
                    solution_en: "<p>17.(b) <span style=\"font-family: Palanquin Dark;\">By venn diagram we can see only the conclusion (i) and (ii) follows.</span><br><strong id=\"docs-internal-guid-d712d5e4-7fff-4c19-60d0-01ef653ee1d3\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXe6GwKsKq7-6369z9j63RVb4zs3d391-Mm_V1rswSA5LEaylxZwpcTmKBSZnHI6ymuT3W5SA17ebYwnLlEdoU7hsYfxM5bCLr63oibF_c8NEjOE5woRHXmYh2POFL91e1x6jMhi?key=3CG07PQ74LV66ubMJGDHTg\" width=\"223\" height=\"144\"></strong></p>",
                    solution_hi: "<p>17.(b) <span style=\"font-family: Palanquin Dark;\">वेन आरेख से हम केवल निष्कर्ष (i) और (ii) का अनुसरण करते हुए देख सकते हैं।</span><br><strong id=\"docs-internal-guid-4558e8fa-7fff-5c49-9bf0-df4e1ced3a81\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcz9Isnfskmqsjfe22NZl9IyT2ImB4lqcRz_Dp0mUAgxaaXCpgS0vwp3-Qr-zDBlnrOTLxModPFj6WwJ6J_L8AF27qVobqgQCxZhiP8MlPaNa1IOGquI1Ae5tVxxJJy2nezMzmTRQ?key=3CG07PQ74LV66ubMJGDHTg\" width=\"261\" height=\"169\"></strong></p>\n<p><span style=\"font-family: Palanquin Dark;\"> </span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "6",
                    question_en: "<p>18.<span style=\"font-family: Palanquin Dark;\"> Six letters A, B, C, D, E and F are written on different faces of a dice. Two positions of the same dice are shown. Select the letter that will be on the face opposite to the one having &lsquo;C&lsquo;.</span><br><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1665484840/word/media/image42.png\" width=\"164\" height=\"81\"></p>",
                    question_hi: "<p><span style=\"font-family: Palanquin Dark;\">18. </span><span style=\"font-family: Palanquin Dark;\">पासे के विभिन्न फलकों पर छह अक्षर A, B, C, D, E और F लिखे गए हैं। इस पासे की दो स्थितियाँ दिखाई गई हैं। उस अक्षर का चयन करें जो \'C\' वाले के विपरीत फलक पर होगा।</span><br><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1665484840/word/media/image42.png\" width=\"148\" height=\"73\"></p>",
                    options_en: ["<p>F</p>", "<p>D</p>", 
                                "<p>B</p>", "<p>E</p>"],
                    options_hi: ["<p>F</p>", "<p>D</p>",
                                "<p>B</p>", "<p>E</p>"],
                    solution_en: "<p>18.(d) Find out common letters on both&nbsp; dice and then move in either clockwise or anti clockwise direction .<br>( A&rarr;A , F&rarr; D, C &rarr; E).So &lsquo;E&rsquo; will be opposite to &lsquo;C&rsquo;&nbsp;</p>",
                    solution_hi: "<p>18.(d)&nbsp;दोनों पासों पर उभयनिष्ठ अक्षर ज्ञात करें और फिर दक्षिणावर्त या वामावर्त दिशा में आगे बढ़ें।<br>( A&rarr;A , F&rarr; D, C &rarr; E)&nbsp;<br>इसलिए \'E\' \'C\' के विपरीत होगा।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "6",
                    question_en: "<p>19.<span style=\"font-family: Palanquin Dark;\"> Select the number from among the given options that can replace the question mark (?) in the following series.</span><br><span style=\"font-family: Palanquin Dark;\">3, 11, 31, 69, 131, ?</span></p>",
                    question_hi: "<p><span style=\"font-family: Palanquin Dark;\">19. </span><span style=\"font-family: Palanquin Dark;\">दिए गए विकल्पों में से वह संख्या चुनिए जो निम्नलिखित श्रृंखला में प्रश्नवाचक चिन्ह (?) को प्रतिस्थापित कर सके।</span><br><span style=\"font-family: Palanquin Dark;\">3, 11, 31, 69, 131, ?</span></p>",
                    options_en: ["<p>152</p>", "<p>163</p>", 
                                "<p>198</p>", "<p>223</p>"],
                    options_hi: ["<p>152</p>", "<p>163</p>",
                                "<p>198</p>", "<p>223</p>"],
                    solution_en: "<p>19.(d)<br><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1665484840/word/media/image12.png\" width=\"251\" height=\"61\"></p>",
                    solution_hi: "<p>19.(d)<br><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1665484840/word/media/image12.png\" width=\"263\" height=\"64\"></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "6",
                    question_en: "<p>20.<span style=\"font-family: Palanquin Dark;\"> Select the figure from among the given options that can replace the question mark(?) in the following series.</span><br><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1665484840/word/media/image21.png\" width=\"250\" height=\"66\"></p>",
                    question_hi: "<p><span style=\"font-family: Palanquin Dark;\">20. </span><span style=\"font-family: Palanquin Dark;\">दिए गए विकल्पों में से उस आकृति का चयन करें जो निम्नलिखित श्रृंखला में प्रश्न चिह्न (?) को प्रतिस्थापित कर सकता है।</span><br><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1665484840/word/media/image21.png\" width=\"255\" height=\"67\"></p>",
                    options_en: ["<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1665484840/word/media/image7.png\" width=\"73\" height=\"66\"><span style=\"font-family: Palanquin Dark;\"> </span></p>", "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1665484840/word/media/image10.png\" width=\"73\" height=\"66\"></p>", 
                                "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1665484840/word/media/image9.png\" width=\"73\" height=\"66\"><span style=\"font-family: Palanquin Dark;\"> </span></p>", "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1665484840/word/media/image20.png\" width=\"73\" height=\"66\"></p>"],
                    options_hi: ["<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1665484840/word/media/image7.png\" width=\"73\" height=\"66\"><span style=\"font-family: Palanquin Dark;\"> </span></p>", "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1665484840/word/media/image10.png\" width=\"73\" height=\"66\"></p>",
                                "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1665484840/word/media/image9.png\" width=\"73\" height=\"66\"><span style=\"font-family: Palanquin Dark;\"> </span></p>", "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1665484840/word/media/image20.png\" width=\"73\" height=\"66\"></p>"],
                    solution_en: "<p>20.(a) <span style=\"font-family: Palanquin Dark;\">Only option (a) can replace the ? in given series</span><br><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1665484840/word/media/image7.png\" width=\"73\" height=\"66\"></p>",
                    solution_hi: "<p>20.(a) <span style=\"font-family: Palanquin Dark;\">केवल विकल्प (a) दी गई श्रृंखला में (?) को प्रतिस्थापित कर सकता है।</span><br><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1665484840/word/media/image7.png\" width=\"73\" height=\"66\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "6",
                    question_en: "<p>21.<span style=\"font-family: Palanquin Dark;\"> In a certain code language </span><span style=\"font-family: Palanquin Dark;\">&lsquo;</span><span style=\"font-family: Palanquin Dark;\">ROUBST</span><span style=\"font-family: Palanquin Dark;\">&rsquo; </span><span style=\"font-family: Palanquin Dark;\">is coded as</span><span style=\"font-family: Palanquin Dark;\"> 61</span><span style=\"font-family: Palanquin Dark;\">. How will </span><span style=\"font-family: Palanquin Dark;\">&lsquo;FORTUNATE&rsquo;</span><span style=\"font-family: Palanquin Dark;\"> be coded in that language?</span></p>",
                    question_hi: "<p>21. <span style=\"font-family: Palanquin Dark;\">एक निश्चित कूट भाषा में, &lsquo;</span><span style=\"font-family: Palanquin Dark;\">ROUBST</span><span style=\"font-family: Palanquin Dark;\">&rsquo; को 61 के रूप में कूटबद्ध किया जाता है। उसी भाषा में &lsquo;FORTUNATE&rsquo; को कैसे कूटबद्ध किया जायेगा?</span></p>",
                    options_en: ["<p>124</p>", "<p>114</p>", 
                                "<p>141</p>", "<p>142</p>"],
                    options_hi: ["<p>124</p>", "<p>114</p>",
                                "<p>141</p>", "<p>142</p>"],
                    solution_en: "<p>21.(b)<strong> </strong><span style=\"font-family: Palanquin Dark;\"><strong>Logi</strong>c : </span><span style=\"font-family: Palanquin Dark;\">Sum of the opposite of place value of the letters - total number of letters</span><br><span style=\"font-family: Palanquin Dark;\">ROBUST - 6 = 9 + 12 + 6 + 25 + 8 + 7 - 6 = 61</span><br><span style=\"font-family: Palanquin Dark;\">Similarly</span><span style=\"font-family: Palanquin Dark;\">FORTUNATE - 9 = 21 + 12 + 9 + 7 + 6 + 13 + 26 + 7 + 22 - 9 = 114</span></p>",
                    solution_hi: "<p>21.(b)<span style=\"font-family: Palanquin Dark;\"> <strong>&nbsp;</strong></span><span style=\"font-family: Palanquin Dark;\"><strong>तर्क</strong> : अक्षरों के स्थानीय मान के विपरीत का योग - अक्षरों की कुल संख्या</span><br><span style=\"font-family: Palanquin Dark;\">ROBUST - 6 = 9 + 12 + 6 + 25 + 8 + 7 - 6 = 61</span><br><span style=\"font-family: Palanquin Dark;\">उसी प्रकार</span><span style=\"font-family: Palanquin Dark;\">FORTUNATE - 9 = 21 + 12 + 9 + 7 + 6 + 13 + 26 + 7 + 22 - 9 = 114</span></p>\n<p><span style=\"font-family: Palanquin Dark;\"> </span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "6",
                    question_en: "<p>22.<span style=\"font-family: Palanquin Dark;\"> Select the number from among the given options that can replace the question mark (?) in the following series.</span><br><span style=\"font-family: Palanquin Dark;\">58, 67, 83, 108, ?</span></p>",
                    question_hi: "<p>22. <span style=\"font-family: Palanquin Dark;\">दिए गए विकल्पों में से वह संख्या चुनिए जो निम्नलिखित श्रृंखला में प्रश्नवाचक चिन्ह (?) को प्रतिस्थापित कर सके।</span><br><span style=\"font-family: Palanquin Dark;\">58, 67, 83, 108, ?</span></p>",
                    options_en: ["<p>178</p>", "<p>157</p>", 
                                "<p>139</p>", "<p>144</p>"],
                    options_hi: ["<p>178</p>", "<p>157</p>",
                                "<p>139</p>", "<p>144</p>"],
                    solution_en: "<p><span style=\"font-family: Palanquin Dark;\">22.(d)&nbsp;<strong> </strong></span><span style=\"font-family: Palanquin Dark;\"><strong>Logic</strong> </span><span style=\"font-family: Palanquin Dark;\">: sum of square of number</span><br><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1665484840/word/media/image38.png\"></p>",
                    solution_hi: "<p>22.(d) <span style=\"font-family: Palanquin Dark;\">तर्क : संख्या के वर्ग का योग</span><br><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1665484840/word/media/image38.png\"></p>\n<p><span style=\"font-family: Palanquin Dark;\"> </span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "23",
                    section: "6",
                    question_en: "<p>23.<span style=\"font-family: Palanquin Dark;\"> Select the option in which the given figure is embedded(rotation is NOT allowed).</span><br><span style=\"font-family: Palanquin Dark;\"> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1665484840/word/media/image2.png\"></p>",
                    question_hi: "<p><span style=\"font-family: Palanquin Dark;\">23. </span><span style=\"font-family: Palanquin Dark;\">उस आकृति का चयन करें जिसमें आकृति अंतर्निहित है (घुमाने की अनुमति नहीं है)।</span><br><span style=\"font-family: Palanquin Dark;\"> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1665484840/word/media/image2.png\"></p>",
                    options_en: ["<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1665484840/word/media/image25.png\" width=\"85\" height=\"77\"><span style=\"font-family: Palanquin Dark;\"> </span></p>", "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1665484840/word/media/image18.png\" width=\"85\" height=\"77\"></p>", 
                                "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1665484840/word/media/image3.png\" width=\"85\" height=\"77\"><span style=\"font-family: Palanquin Dark;\"> </span></p>", "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1665484840/word/media/image32.png\" width=\"85\" height=\"77\"></p>"],
                    options_hi: ["<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1665484840/word/media/image25.png\" width=\"85\" height=\"77\"><span style=\"font-family: Palanquin Dark;\"> </span></p>", "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1665484840/word/media/image18.png\" width=\"85\" height=\"77\"></p>",
                                "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1665484840/word/media/image3.png\" width=\"85\" height=\"77\"><span style=\"font-family: Palanquin Dark;\"> </span></p>", "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1665484840/word/media/image32.png\" width=\"85\" height=\"77\"></p>"],
                    solution_en: "<p>23.(d) <span style=\"font-family: Palanquin Dark;\">The given figure is embedded is</span><br><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1665484840/word/media/image32.png\" width=\"85\" height=\"77\"></p>",
                    solution_hi: "<p>23.(d) <span style=\"font-family: Palanquin Dark;\">दिया गया चित्र सन्निहित है</span><br><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1665484840/word/media/image32.png\" width=\"85\" height=\"82\"></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "24",
                    section: "6",
                    question_en: "<p>24. <span style=\"font-family: Palanquin Dark;\">Select the set of classes the relationship among which is being illustrated by the given Venn diagram.</span><br><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1665484840/word/media/image5.png\" width=\"109\" height=\"95\"></p>",
                    question_hi: "<p>24. <span style=\"font-family: Palanquin Dark;\">वर्गों के उस समुच्चय का चयन करें जिसके बीच संबंध दिए गए वेन आरेख द्वारा दर्शाया जा रहा है।</span><br><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1665484840/word/media/image5.png\" width=\"107\" height=\"94\"></p>",
                    options_en: ["<p>Women, <span style=\"font-family: Palanquin Dark;\">Gynaecologists</span><span style=\"font-family: Palanquin Dark;\">, Doctors</span></p>", "<p>Males, Females, Daughters</p>", 
                                "<p>Entrepreneurs,Women,Philanthropists</p>", "<p>Eatables, Plums, Fruits</p>"],
                    options_hi: ["<p>महिला, स्त्री रोग विशेषज्ञ, डॉक्टर</p>", "<p>पुरुष, महिलाये, बेटिया</p>",
                                "<p>उद्यमी, महिला, परोपकारी</p>", "<p>खाने योग्य, आलूभुखारे , फल</p>"],
                    solution_en: "<p>24.(c) <span style=\"font-family: Palanquin Dark;\">Entrepreneurs can </span><span style=\"font-family: Palanquin Dark;\">be a women</span><span style=\"font-family: Palanquin Dark;\"> . A woman can be an entrepreneur. A philanthropist can be a woman. Or a women can bea philanthropists</span><br><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1665484840/word/media/image5.png\" width=\"103\" height=\"90\"></p>",
                    solution_hi: "<p>24.(c) <span style=\"font-family: Palanquin Dark;\">उद्यमी, महिलाएं हो सकती हैं। एक महिला, उद्यमी हो सकती है। एक परोपकारी, महिला हो सकती है या एक महिला, परोपकारी हो सकती है</span><br><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1665484840/word/media/image5.png\" width=\"96\" height=\"84\"></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "25",
                    section: "misc",
                    question_en: "<p>25. <span style=\"font-family: Palanquin Dark;\">&nbsp;&lsquo;A + B&rsquo; means &lsquo;A is the daughter of B&rsquo;.</span><br><span style=\"font-family: Palanquin Dark;\">&lsquo;A $ B&rsquo; means &lsquo;A is the husband of B&rsquo;.</span><br><span style=\"font-family: Palanquin Dark;\">&lsquo;A @ B&rsquo; means &lsquo;A is the brother of B&rsquo;.</span><br><span style=\"font-family: Palanquin Dark;\">&lsquo;A &amp; B&rsquo; means &lsquo;A is the mother of B&rsquo;.</span><br><span style=\"font-family: Palanquin Dark;\">&lsquo;A % B&rsquo; means &lsquo;A is the son of B&rsquo;.</span><br><span style=\"font-family: Palanquin Dark;\">If &lsquo;W @ S % K $ G &amp; U &amp; T @ R + C&rsquo;, then which of the following statements is correct?</span></p>",
                    question_hi: "<p>25.<span style=\"font-family: Palanquin Dark;\"> </span><span style=\"font-family: Palanquin Dark;\">\'A + B\' का अर्थ है \'A B की बेटी है\'।</span><br><span style=\"font-family: Palanquin Dark;\">\'A $ B\' का अर्थ है \'A B का पति है\'।</span><br><span style=\"font-family: Palanquin Dark;\">\'A @ B\' का अर्थ है \'A B का भाई है\'।</span><br><span style=\"font-family: Palanquin Dark;\">\'A &amp; B\' का अर्थ है \'A B की माँ है\'।</span><br><span style=\"font-family: Palanquin Dark;\">\'A % B\' का अर्थ है \'A B का बेटा है\'।</span><br><span style=\"font-family: Palanquin Dark;\">यदि &lsquo;W @ S % K $ G &amp; U &amp; T @ R + C&rsquo;, है, तो निम्नलिखित में से कौन सा कथन सही है?</span></p>",
                    options_en: ["<p>G is the maternal grandmother of R.</p>", "<p>G is the father of W.</p>", 
                                "<p>C is the father-in-law of K.</p>", "<p>C is the wife of U.</p>"],
                    options_hi: ["<p>G, R की नानी है।</p>", "<p>G, W का पिता है।</p>",
                                "<p>C, K का ससुर है।</p>", "<p>C, U की पत्नी है।</p>"],
                    solution_en: "<p>25.(a) <span style=\"font-family: Palanquin Dark;\">Given that</span><br><span style=\"font-family: Palanquin Dark;\">&lsquo;A+ B&rsquo; means &lsquo;A is the daughter of &lsquo;B&rsquo;</span><br><span style=\"font-family: Palanquin Dark;\">&lsquo;A$B&rsquo; means &lsquo; A is the husband of the &lsquo;B&rsquo;</span><br><span style=\"font-family: Palanquin Dark;\">&lsquo;A@B&rsquo; means &lsquo;A is the brother of &lsquo;B&rsquo;</span><br><span style=\"font-family: Palanquin Dark;\">&lsquo;A &amp; B&rsquo; means &lsquo;A is the mother of &lsquo;B&rsquo;</span><br><span style=\"font-family: Palanquin Dark;\">&lsquo;A % B&rsquo; means &lsquo; A is the son of the &lsquo; B&rsquo;</span><br><span style=\"font-family: Palanquin Dark;\">And asked &lsquo;w@ S % K $ G &amp; U <span style=\"font-weight: 400;\">&amp;</span>T @ R + C&rsquo;</span><br><span style=\"font-family: Palanquin Dark;\">Decode the given sign . We can see the relation.</span><br><strong id=\"docs-internal-guid-743b570d-7fff-e5c8-1175-1c28473cbfce\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXc4EAtZcsm6ENal3P8YN_YJEXEBEt5lbeInyeIALTaQ7Qc1XZiuc5hjAS_3oymXOaLJ7yyOsmeDV981wMp32f3Y-4laFtTv2Ubcw8PFlxBZKcUcsVy5BWWk_Cj5sTqnalIdrB0-KQ?key=3CG07PQ74LV66ubMJGDHTg\" width=\"371\" height=\"148\"></strong></p>",
                    solution_hi: "<p>25.(a) <span style=\"font-family: Palanquin Dark;\">दिया गया है,</span><br><span style=\"font-family: Palanquin Dark;\">\'A + B\' का अर्थ है \'A\', \'B\' की बेटी है</span><br><span style=\"font-family: Palanquin Dark;\">\'A $ B\' का अर्थ है \'A\', \'B\' का पति है</span><br><span style=\"font-family: Palanquin Dark;\">\'A @ B\' का अर्थ है \'A\', \'B\' का भाई है</span><br><span style=\"font-family: Palanquin Dark;\">\'A &amp; B\' का अर्थ है \'A\', \'B\' की मां है</span><br><span style=\"font-family: Palanquin Dark;\">\'A% B\' का अर्थ है \'A\', \'B\' का बेटा है</span><br><span style=\"font-family: Palanquin Dark;\">और पूछा गया है, \'w @ S % K $ G &amp; U <span style=\"font-weight: 400;\">&amp;</span>T@ R + C\'</span><br><span style=\"font-family: Palanquin Dark;\">दिए गए चिन्ह को डिकोड करें। हम संबंध देख सकते हैं।</span><br><strong id=\"docs-internal-guid-8c103a72-7fff-f29d-c0a5-6af8a2358001\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdPUYFFW0WiUFK1og0GE6jAxlM06bx_w_zbaZ-mv68AKUkrUOf4TUZvH9qTXeZ0pn1kOXDUnhh3it6nsv47JL9_G1UOGKbjNv5GUj3d61fgPfFn04OgTmsJ5VL5qyr0U4lCrozLFQ?key=3CG07PQ74LV66ubMJGDHTg\" width=\"340\" height=\"160\"></strong></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML - more compact
            let html = `
                <div class="card mb-3">
                    <div class="card-body">
                        <div class="question-text mb-3">
                            <div class="en">${question.question_en}</div>
                            <div class="hi" style="display:none">${question.question_hi}</div>
                        </div>
                        <div class="options">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = 'option-label';
                // When reviewing, highlight user's choice and correct answer
                if (submitted) {
                    if (opt === question.correct) {
                        optionClass += ' border-success bg-success bg-opacity-10';
                    }
                    if (answers[question.id] === opt && opt !== question.correct) {
                        optionClass += ' border-danger bg-danger bg-opacity-10';
                    }
                }
                html += `
                    <div class="form-check mb-2">
                        <input class="form-check-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${answers[question.id] === opt ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                        <label class="form-check-label w-100 ${optionClass}" for="opt-${index}-${opt}">
                            ${submitted && opt === question.correct ? '<i class="fas fa-check-circle text-success me-1"></i>' : ''}
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const alertClass = isCorrect ? 'alert-success' : 'alert-danger';
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="alert ${alertClass} mt-3 py-2">
                        <strong>${resultText}</strong>
                        <div class="mt-1 small">
                            <strong>Solution:</strong>
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                document.getElementById(`box-${index}`).classList.add('attempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { if (currentQuestion > 0) showQuestion(currentQuestion - 1); }
        function nextQuestion() { if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
            // Update button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const content = document.getElementById('main-content');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                if (window.innerWidth >= 992) {
                    content.classList.add('with-nav');
                }
            } else {
                nav.classList.remove('show');
                if (window.innerWidth >= 992) {
                    content.classList.remove('with-nav');
                }
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer - compact
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            if (confirm(`You have attempted ${attempted} of ${questions.length} questions.
${remaining} questions are unattempted.
Are you sure you want to submit?`)) {
                submitTest();
            }
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('bg-secondary', 'text-white');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            resultsModal.show();
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.classList.replace('btn-light', 'btn-success');
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            resultsModal.hide();
            showQuestion(0);
        }
        // Add event listener for window resize
        window.addEventListener('resize', function() {
            if (window.innerWidth >= 992) {
                if (document.getElementById('question-nav').classList.contains('show')) {
                    document.getElementById('main-content').classList.add('with-nav');
                }
            } else {
                document.getElementById('main-content').classList.remove('with-nav');
            }
        });
    </script>
</body>
</html>