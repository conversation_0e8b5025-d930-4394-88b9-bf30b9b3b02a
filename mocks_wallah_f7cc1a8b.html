<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">10:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 15</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">15</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 10 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["6"] = {
                name: "Reasoning",
                start: 0,
                end: 13
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="6">Reasoning</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 14,
                end: 14
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "6",
                    question_en: " <p>1. </span><span style=\"font-family:Cambria Math\">The second number in the given number-pairs is obtained by performing certain mathematical operation(s) on the first number. The same operation(s) is/are followed in all the number-pairs except one. Find that odd number-pair.</span></p>",
                    question_hi: " <p><span style=\"font-family:Cambria Math\">1. </span><span style=\"font-family:Nirmala UI\">दिए</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">गए</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">संख्या</span><span style=\"font-family:Cambria Math\"> -</span><span style=\"font-family:Nirmala UI\">युग्मों</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">दूसरी</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">संख्या</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">पहली</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">संख्या</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">पर</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">कुछ</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">गणितीय</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">संक्रिया</span><span style=\"font-family:Cambria Math\"> (</span><span style=\"font-family:Nirmala UI\">एं</span><span style=\"font-family:Cambria Math\">) </span><span style=\"font-family:Nirmala UI\">करके</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">प्राप्त</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">की</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">जाती</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">है।</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">एक</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">संख्या</span><span style=\"font-family:Cambria Math\">-</span><span style=\"font-family:Nirmala UI\">युग्म</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">को</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">छोड़कर</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">शेष</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">सभी</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">संख्या</span><span style=\"font-family:Cambria Math\"> -</span><span style=\"font-family:Nirmala UI\">युग्मों</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">समान</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">संक्रिया</span><span style=\"font-family:Cambria Math\">(</span><span style=\"font-family:Nirmala UI\">ओं</span><span style=\"font-family:Cambria Math\">) </span><span style=\"font-family:Nirmala UI\">का</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">पालन</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">किया</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">जाता</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">है।</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">विषम</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">संख्या</span><span style=\"font-family:Cambria Math\"> -</span><span style=\"font-family:Nirmala UI\">य</span><span style=\"font-family:Nirmala UI\">ुग्म</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">ज्ञात</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">करें।</span></p>",
                    options_en: [" <p> 20 : 11</span></p>", " <p> 6  : 4</span></p>", 
                                " <p> 15 : 9</span></p>", " <p> 12 : 7</span></p>"],
                    options_hi: [" <p> 20 : 11</span></p>", " <p> 6  : 4</span></p>",
                                " <p> 15 : 9</span></p>", " <p> 12 : 7</span></p>"],
                    solution_en: " <p>1.(c)</span></p> <p><span style=\"font-family:Cambria Math\">Logic </span><span style=\"font-family:Cambria Math\">:- (Second number) × 2 - 2 = First number</span></p> <p><span style=\"font-family:Cambria Math\">20:11 :- (11)×2 -2 :- 22 - 2 = 20</span></p> <p><span style=\"font-family:Cambria Math\">6 : 4 :- (4) × 2 - 2 :- 8 -2 = 6</span></p> <p><span style=\"font-family:Cambria Math\">12 : 7 :- (7)×2 - 2 :- 14 - 2 = 12</span></p> <p><span style=\"font-family:Cambria Math\">But,</span></p> <p><span style=\"font-family:Cambria Math\">15 : 9 :- (9)×</span><span style=\"font-family:Cambria Math\"> 2 - 2 :- 18 - 2 = 16 (Not 15)</span></p>",
                    solution_hi: " <p>1.(c) </span></p> <p><span style=\"font-family:Nirmala UI\">तर्क</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">:- (</span><span style=\"font-family:Nirmala UI\">दूसरी</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">संख्या</span><span style=\"font-family:Cambria Math\">) × 2 - 2 = </span><span style=\"font-family:Nirmala UI\">पहली</span><span style=\"font-family:Cambria Math\">  </span><span style=\"font-family:Nirmala UI\">संख्या</span></p> <p><span style=\"font-family:Cambria Math\">20:11 :- (11)×2 -2 :- 22 - 2 = 20</span></p> <p><span style=\"font-family:Cambria Math\">6 : 4 :- (4) × 2 - 2 :- 8 -2 = 6</span></p> <p><span style=\"font-family:Cambria Math\">12 : 7 :- (7)×2 - 2 :- 14 - 2 = 12</span></p> <p><span style=\"font-family:Nirmala UI\">लेकिन</span><span style=\"font-family:Cambria Math\">,</span></p> <p><span style=\"font-family:Cambria Math\">15 : 9 :- (9)× 2 - 2 :- 18 - 2 = 16 (15 </span><span style=\"font-family:Nirmala UI\">नहीं</span><span style=\"font-family:Cambria Math\">)</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "6",
                    question_en: " <p><span style=\"font-family:Cambria Math\">2. </span><span style=\"font-family:Cambria Math\">Four letter-clusters have been given, out of which three are alike in some manner and one is different. Select the odd letter-cluster.</span></p>",
                    question_hi: " <p><span style=\"font-family:Cambria Math\">2. </span><span style=\"font-family:Nirmala UI\">चार</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">अक्षर</span><span style=\"font-family:Cambria Math\">-</span><span style=\"font-family:Nirmala UI\">समूह</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">दिए</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">गए</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">हैं</span><span style=\"font-family:Cambria Math\">, </span><span style=\"font-family:Nirmala UI\">जिनमें</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">से</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">तीन</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">अक्षर</span><span style=\"font-family:Cambria Math\">-</span><span style=\"font-family:Nirmala UI\">समूह</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">किसी</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">न</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">किसी</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">रूप</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">म</span><span style=\"font-family:Nirmala UI\">ें</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">एक</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">जैसे</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">हैं</span><span style=\"font-family:Cambria Math\">  </span><span style=\"font-family:Nirmala UI\">और</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">एक</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">भिन्न</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">है।</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">उस</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">भिन्न</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">अक्षर</span><span style=\"font-family:Cambria Math\">- </span><span style=\"font-family:Nirmala UI\">समूह</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">का</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">चयन</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">कीजिए</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">।</span></p>",
                    options_en: [" <p> BFJN  </span></p>", " <p> DHLP  </span></p>", 
                                " <p> HJKP </span></p>", " <p> JNRV</span></p>"],
                    options_hi: [" <p> BFJN  </span></p>", " <p> DHLP  </span></p>",
                                " <p> HJKP </span></p>", " <p> JNRV</span></p>"],
                    solution_en: " <p>2.(c)</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1705124250/word/media/image1.png\"/><span style=\"font-family:Cambria Math\">, </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1705124250/word/media/image2.png\"/><span style=\"font-family:Cambria Math\">, </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1705124250/word/media/image3.png\"/></p> <p><span style=\"font-family:Cambria Math\">But,</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1705124250/word/media/image4.png\"/></p> <p><span style=\"font-family:Cambria Math\">               </span></p> <p><span style=\"font-family:Cambria Math\">      </span></p>",
                    solution_hi: " <p>2.(c)</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1705124250/word/media/image1.png\"/><span style=\"font-family:Cambria Math\">, </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1705124250/word/media/image2.png\"/><span style=\"font-family:Cambria Math\">, </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1705124250/word/media/image3.png\"/></p> <p><span style=\"font-family:Nirmala UI\">लेकिन</span><span style=\"font-family:Cambria Math\">,</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1705124250/word/media/image4.png\"/></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "6",
                    question_en: " <p>3.</span><span style=\"font-family:Cambria Math\"> Three of the following four triads are alike in a certain way as they are formed by performing the same mathematical operations among themselves and thus form a group. Which triad does NOT belong to that group?</span></p>",
                    question_hi: " <p>3. </span><span style=\"font-family:Nirmala UI\">निम्नलिखित</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">चार</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">त्रिक</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">से</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">तीन</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">एक</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">निश्चित</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">तरीके</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">से</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">समान</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">हैं</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">क्योंकि</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">वे</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">आपस</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">समान</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">गणितीय</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">संक्रियाएं</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">करके</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">निर्मित</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">हुए</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">हैं</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">और</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">इस</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">प्रकार</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">वे</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">एक</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">समूह</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">बनाते</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">हैं।</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">विकल्पों</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">दिए</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">त्रिकों</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">से</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">कौन</span><span style=\"font-family:Cambria Math\">- </span><span style=\"font-family:Nirmala UI\">सात्रिक</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">उस</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">समूह</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">से</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">संबंधित</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">नहीं</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">है</span><span style=\"font-family:Cambria Math\">?</span></p>",
                    options_en: [" <p> </span><span style=\"font-family:Cambria Math\">36 : 13 : 7</span></p>", " <p> </span><span style=\"font-family:Cambria Math\">16 : 9 : 5</span></p>", 
                                " <p> </span><span style=\"font-family:Cambria Math\">72 : 1</span><span style=\"font-family:Cambria Math\">6 : 8</span></p>", " <p> </span><span style=\"font-family:Cambria Math\">1 : 3 : 2</span></p>"],
                    options_hi: [" <p> </span><span style=\"font-family:Cambria Math\">36 : 13 : 7</span></p>", " <p> </span><span style=\"font-family:Cambria Math\">16 : 9 : 5</span></p>",
                                " <p> </span><span style=\"font-family:Cambria Math\">72 : 16 : 8</span></p>", " <p> </span><span style=\"font-family:Cambria Math\">1 : 3 : 2</span></p>"],
                    solution_en: " <p>3.(c)</span></p> <p><span style=\"font-family:Cambria Math\">Logic:</span><span style=\"font-family:Cambria Math\"> (Second number - Third number)</span><span style=\"font-family:Cambria Math\">2</span><span style=\"font-family:Cambria Math\"> = First  number</span></p> <p><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">36 : 13 : 7 → (13 - 7)</span><span style=\"font-family:Cambria Math\">2</span><span style=\"font-family:Cambria Math\">  = 36</span></p> <p><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">16 : 9 : 5 →  (9 - 5)</span><span style=\"font-family:Cambria Math\">2</span><span style=\"font-family:Cambria Math\">  = 16</span></p> <p><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">1 : 3 : 2 →  (3 - 2)</span><span style=\"font-family:Cambria Math\">2</span><span style=\"font-family:Cambria Math\">  = 1</span></p> <p><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">But,  </span><span style=\"font-family:Cambria Math\">72 : 16 : 8 → (16 - 8)</span><span style=\"font-family:Cambria Math\">2</span><span style=\"font-family:Cambria Math\">  = 64 (Not 72)</span></p>",
                    solution_hi: " <p>3.(c)</span></p> <p><span style=\"font-family:Nirmala UI\">तर्क</span><span style=\"font-family:Cambria Math\">:</span><span style=\"font-family:Cambria Math\"> (</span><span style=\"font-family:Nirmala UI\">दूसरी</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">संख्या</span><span style=\"font-family:Cambria Math\"> - </span><span style=\"font-family:Nirmala UI\">तीसरी</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">संख्या</span><span style=\"font-family:Cambria Math\">)</span><span style=\"font-family:Cambria Math\">2</span><span style=\"font-family:Cambria Math\"> = </span><span style=\"font-family:Nirmala UI\">पहली</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">संख्या</span><span style=\"font-family:Cambria Math\"> </span></p> <p><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">36 : 13 : 7 → (13 - 7)</span><span style=\"font-family:Cambria Math\">2</span><span style=\"font-family:Cambria Math\">  = 36</span></p> <p><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">16 : 9 : 5 →  (9 - 5)</span><span style=\"font-family:Cambria Math\">2</span><span style=\"font-family:Cambria Math\">  = 16</span></p> <p><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">1 : 3 : 2 →  (3 - 2)</span><span style=\"font-family:Cambria Math\">2</span><span style=\"font-family:Cambria Math\">  = 1</span></p> <p><span style=\"font-family:Nirmala UI\">लेकिन</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">,  </span><span style=\"font-family:Cambria Math\">72 : 16 : 8 → (16 - 8)</span><span style=\"font-family:Cambria Math\">2</span><span style=\"font-family:Cambria Math\">  = 64 (72 </span><span style=\"font-family:Nirmala UI\">नहीं</span><span style=\"font-family:Cambria Math\">)</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "6",
                    question_en: " <p>4. </span><span style=\"font-family:Cambria Math\">Three of the following four letter-clusters are alike in a certain way and one is different. Pick the odd one out.</span></p>",
                    question_hi: " <p>4.  </span><span style=\"font-family:Nirmala UI\">निम्नलिखित</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">चार</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">अक्षर</span><span style=\"font-family:Cambria Math\">-</span><span style=\"font-family:Nirmala UI\">समूहों</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">से</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">तीन</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">किसी</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">प्रकार</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">एक</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">जैसे</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">हैं</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">और</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">एक</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">भिन्न</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">है।</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">भिन्न</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">अक्षर</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">समूह</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">का</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">चयन</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">कीजिए।</span></p>",
                    options_en: [" <p> KPH </span></p>", " <p> GTD</span></p>", 
                                " <p> DWA</span></p>", " <p> EVC</span></p>"],
                    options_hi: [" <p> KPH </span></p>", " <p> GTD</span></p>",
                                " <p> DWA</span></p>", " <p> EVC</span></p>"],
                    solution_en: " <p>4.(d)</span></p> <p><span style=\"font-family:Cambria Math\">          </span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1705124250/word/media/image5.png\"/></p> <p><span style=\"font-family:Cambria Math\">But,</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1705124250/word/media/image6.png\"/></p>",
                    solution_hi: " <p>4.(d)</span></p> <p><span style=\"font-family:Cambria Math\">    </span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1705124250/word/media/image7.png\"/></p> <p><span style=\"font-family:Nirmala UI\">लेकिन</span><span style=\"font-family:Cambria Math\">,</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1705124250/word/media/image8.png\"/></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "6",
                    question_en: " <p><span style=\"font-family:Cambria Math\">5. </span><span style=\"font-family:Cambria Math\">Three of the following four letter-clusters are alike in a certain way and one is different.</span></p> <p><span style=\"font-family:Cambria Math\">Select the one that is different.</span></p>",
                    question_hi: " <p><span style=\"font-family:Cambria Math\">5. </span><span style=\"font-family:Nirmala UI\">निम्नलिखित</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">चार</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">अक्षर</span><span style=\"font-family:Cambria Math\">-</span><span style=\"font-family:Nirmala UI\">समूहों</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">से</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">तीन</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">एक</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">निश्चित</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">तरीके</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">से</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">एकसमान</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">हैं</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">और</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">एक</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">असंगत</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">है।</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">जो</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">असंगत</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">है</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">उसका</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">चयन</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">कीजिए</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">।</span></p>",
                    options_en: [" <p> DFJL </span></p>", " <p> KMOQ </span></p>", 
                                " <p> ACGI</span></p>", " <p> HJNP </span></p>"],
                    options_hi: [" <p> DFJL </span></p>", " <p> KMOQ </span></p>",
                                " <p> ACGI</span></p>", " <p> HJNP </span></p>"],
                    solution_en: " <p>5.(b)</span></p> <p><span style=\"font-family:Cambria Math\">      </span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1705124250/word/media/image9.png\"/></p> <p><span style=\"font-family:Cambria Math\">But, </span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1705124250/word/media/image10.png\"/></p>",
                    solution_hi: " <p>5.(b)   </span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1705124250/word/media/image9.png\"/></p> <p><span style=\"font-family:Nirmala UI\">लेकिन</span><span style=\"font-family:Cambria Math\">,</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1705124250/word/media/image10.png\"/></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "6",
                    question_en: " <p><span style=\"font-family:Cambria Math\">6. </span><span style=\"font-family:Cambria Math\">The second number in the given number-pairs is obtained by performing certain mathematical operation(s) on the first number. The same operation(s) is/are followed in all the number-pairs, excep</span><span style=\"font-family:Cambria Math\">t one. Find that odd number-pair.</span></p>",
                    question_hi: " <p><span style=\"font-family:Cambria Math\">6. </span><span style=\"font-family:Nirmala UI\">दिए</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">गए</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">संख्या</span><span style=\"font-family:Cambria Math\"> - </span><span style=\"font-family:Nirmala UI\">युग्मों</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">दूसरी</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">संख्या</span><span style=\"font-family:Cambria Math\">, </span><span style=\"font-family:Nirmala UI\">पहली</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">संख्या</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">पर</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">कुछ</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">गणितीय</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">संक्रियाएँ</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">करके</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">प्राप्त</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">की</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">जाती</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">है।</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">एक</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">को</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">छोड़कर</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">सभी</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">संख्या</span><span style=\"font-family:Cambria Math\"> - </span><span style=\"font-family:Nirmala UI\">युग्मों</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">समान</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">संक्रियाएँ</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">की</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">जाती</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">हैं।</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">वह</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">असंगत</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">संख्या</span><span style=\"font-family:Cambria Math\">-</span><span style=\"font-family:Nirmala UI\">युग्म</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">ज्ञात</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">कीजिए</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">जिस</span><span style=\"font-family:Cambria Math\">-</span><span style=\"font-family:Nirmala UI\">पर</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">समान</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">संक्रियाएँ</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">नहीं</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">की</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">गईं।</span><span style=\"font-family:Cambria Math\"> </span></p>",
                    options_en: [" <p> 6286 : 1965</span></p>", " <p> 6571 :  2448</span></p>", 
                                " <p> 8247 :  3926</span></p>", " <p> 6903 :  2582</span></p>"],
                    options_hi: [" <p> 6286 : 1965</span></p>", " <p> 6571 :  2448</span></p>",
                                " <p> 8247 :  3926</span></p>", " <p> 6903 :  2582</span></p>"],
                    solution_en: " <p>6.(b)</span></p> <p><span style=\"font-family:Cambria Math\">Logic:- (First number - Second number) = 4321</span></p> <p>:- (6286 - 1965) = 4321</span></p> <p>:- (8247 - 3926) = 4321</span></p> <p>:- (6903 - 2582) = 4321</span></p> <p><span style=\"font-family:Cambria Math\">But, </span></p> <p>:- (6571 - 2448) = 4123</span></p>",
                    solution_hi: " <p>6.(b)</span></p> <p><span style=\"font-family:Nirmala UI\">तर्क</span><span style=\"font-family:Cambria Math\">:- (</span><span style=\"font-family:Nirmala UI\">पहली</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">संख्या</span><span style=\"font-family:Cambria Math\"> - </span><span style=\"font-family:Nirmala UI\">दूसरी</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">संख्या</span><span style=\"font-family:Cambria Math\">) = 4321</span></p> <p>:- (6286 - 1965) = 4321</span></p> <p>:- (8247 - 3926) = 4321</span></p> <p>:- (6903 - 2582) = 4321</span></p> <p><span style=\"font-family:Nirmala UI\">लेकिन</span><span style=\"font-family:Cambria Math\">,</span></p> <p>:- (6571 - 2448) = 4123</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "6",
                    question_en: " <p><span style=\"font-family:Cambria Math\">7. </span><span style=\"font-family:Cambria Math\">The second number in the given number pairs is obtained by performing certain mathematical operation(s) on the first number. The same operation(s) are followed in all the number pairs, EXCEPT one. Find that odd number pair,</span></p>",
                    question_hi: " <p><span style=\"font-family:Cambria Math\">7. </span><span style=\"font-family:Nirmala UI\">दिए</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">गए</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">संख्या</span><span style=\"font-family:Cambria Math\">-</span><span style=\"font-family:Nirmala UI\">युग्मों</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">दूसरी</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">संख्या</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">पहली</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">संख्या</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">पर</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">कुछ</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">गणितीय</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">संक्रिया</span><span style=\"font-family:Cambria Math\">/</span><span style=\"font-family:Nirmala UI\">संकियाएं</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">करके</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">प्राप्त</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">की</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">गई</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">है।</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">एक</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">संख्या</span><span style=\"font-family:Cambria Math\">-</span><span style=\"font-family:Nirmala UI\">युग्म</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">को</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">छोड़कर</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">सभी</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">संख्या</span><span style=\"font-family:Cambria Math\">-</span><span style=\"font-family:Nirmala UI\">युग्मों</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">समान</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">संक्रिया</span><span style=\"font-family:Cambria Math\">/</span><span style=\"font-family:Nirmala UI\">संकियाओं</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">का</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">अनुसरण</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">किया</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">गया</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">है।</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">वह</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">असंगत</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">संख्या</span><span style=\"font-family:Cambria Math\">-</span><span style=\"font-family:Nirmala UI\">युग्म</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">ज्ञात</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">कीजिए।</span></p>",
                    options_en: [" <p> (87, 522) </span></p>", " <p> (84, 672)  </span></p>", 
                                " <p> (92, 552) </span></p>", ""],
                    options_hi: [" <p> (87, 522) </span></p>", " <p> (84, 672)  </span></p>",
                                " <p> (92, 552) </span></p>", ""],
                    solution_en: " <p>7.(b)</span></p> <p><span style=\"font-family:Cambria Math\">Logic :- (Second number </span><span style=\"font-family:Cambria Math\"> First number) = 6</span></p> <p> :- (522 </span><span style=\"font-family:Cambria Math\"> 87) = 6</span></p> <p>:- (552 </span><span style=\"font-family:Cambria Math\"> 92) = 6</span></p> <p> :- (384 </span><span style=\"font-family:Cambria Math\"> 64) = 6</span></p> <p><span style=\"font-family:Cambria Math\">But,</span></p>",
                    solution_hi: " <p>7.(b)</span></p> <p><span style=\"font-family:Nirmala UI\">तर्क</span><span style=\"font-family:Cambria Math\"> :- (</span><span style=\"font-family:Nirmala UI\">दूसरी</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">संख्या</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">पहली</span><span style=\"font-family:Cambria Math\">  </span><span style=\"font-family:Nirmala UI\">संख्या</span><span style=\"font-family:Cambria Math\"> ) = 6</span></p> <p> :- (522 </span><span style=\"font-family:Cambria Math\"> 87) = 6</span></p> <p>:- (552 </span><span style=\"font-family:Cambria Math\"> 92) = 6</span></p> <p> :- (384 </span><span style=\"font-family:Cambria Math\"> 64) = 6</span></p> <p><span style=\"font-family:Nirmala UI\">लेकिन</span><span style=\"font-family:Cambria Math\">,</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "6",
                    question_en: " <p>8. </span><span style=\"font-family:Cambria Math\">Three of the following four letter-clusters are alike in a certain way and one is different. Pick the odd one out.</span></p>",
                    question_hi: " <p>8. </span><span style=\"font-family:Nirmala UI\">निम्नलिखित</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">चार</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">अक्षर</span><span style=\"font-family:Cambria Math\">-</span><span style=\"font-family:Nirmala UI\">समूहों</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">से</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">तीन</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">किसी</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">प्रकार</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">एक</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">जैसे</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">हैं</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">और</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">एक</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">भिन्न</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">है।</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">भिन्न</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">अक्षर</span><span style=\"font-family:Cambria Math\">-</span><span style=\"font-family:Nirmala UI\">समू</span><span style=\"font-family:Nirmala UI\">ह</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">का</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">चयन</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">कीजिए।</span></p>",
                    options_en: [" <p> JQM</span></p>", " <p> DWG  </span></p>", 
                                " <p> BXE  </span></p>", " <p> AZD</span></p>"],
                    options_hi: [" <p> JQM</span></p>", " <p> DWG  </span></p>",
                                " <p> BXE  </span></p>", " <p> AZD</span></p>"],
                    solution_en: " <p>8.(c)</span></p> <p><span style=\"font-family:Cambria Math\">  </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1705124250/word/media/image11.png\"/><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1705124250/word/media/image12.png\"/><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1705124250/word/media/image13.png\"/></p> <p><span style=\"font-family:Cambria Math\">But,</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1705124250/word/media/image14.png\"/></p>",
                    solution_hi: " <p>8.(c)</span></p> <p><span style=\"font-family:Cambria Math\"> </span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1705124250/word/media/image15.png\"/><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1705124250/word/media/image16.png\"/><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1705124250/word/media/image17.png\"/></p> <p><span style=\"font-family:Nirmala UI\">लेकिन</span><span style=\"font-family:Cambria Math\">,</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1705124250/word/media/image18.png\"/></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "6",
                    question_en: " <p>9.  </span><span style=\"font-family:Cambria Math\">Three of the following four triads are alike in a certain way as they are formed by performing the same mathematical operations among themselves and thus form a group. </span></p> <p><span style=\"font-family:Cambria Math\">Which triad does NOT belong to that group?</span></p>",
                    question_hi: " <p>9.</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">निम्नलिखित</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">चार</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">त्रिक</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">से</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">तीन</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">एक</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">निश्चित</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">तरीके</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">से</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">समान</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">हैं</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">क्योंकि</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">वे</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">आपस</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">समान</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">गणितीय</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">संक्रियाएं</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">करके</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">निर्मित</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">हुए</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">हैं</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">और</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">इस</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">प्रकार</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">वे</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">एक</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">समूह</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">बनाते</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">हैं।</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">विकल्पों</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">दिए</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">त्रिकों</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">से</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">कौन</span><span style=\"font-family:Cambria Math\">- </span><span style=\"font-family:Nirmala UI\">सा</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">त्रिक</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">उस</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">समूह</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">से</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">संबंधित</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">नहीं</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">है</span><span style=\"font-family:Cambria Math\">?</span></p>",
                    options_en: [" <p> 12 : 47 : 60 </span></p>", " <p> 4 : 16 : 20</span></p>", 
                                " <p> 2 :</span><span style=\"font-family:Cambria Math\"> 7 : 10</span></p>", " <p> 5 : 19 : 25</span></p>"],
                    options_hi: [" <p> 12 : 47 : 60 </span></p>", " <p> 4 : 16 : 20</span></p>",
                                " <p> 2 : 7 : 10</span></p>", " <p> 5 : 19 : 25</span></p>"],
                    solution_en: " <p>9.(b)</span></p> <p><span style=\"font-family:Cambria Math\">Logic:</span><span style=\"font-family:Cambria Math\"> (First num. + Second num.) + 1 = Third num.</span><span style=\"font-family:Cambria Math\"> </span></p> <p><span style=\"font-family:Cambria Math\">12 : 47 : 60 </span><span style=\"font-family:Cambria Math\">→ (12 + 47) + 1 = 60 </span></p> <p><span style=\"font-family:Cambria Math\">2 : 7 : 10 </span><span style=\"font-family:Cambria Math\">→ (2 + 7) + 1 = 10 </span></p> <p><span style=\"font-family:Cambria Math\">5 : 19 : 25 </span><span style=\"font-family:Cambria Math\">→ (5 + 19) + 1 = 25 </span></p> <p><span style=\"font-family:Cambria Math\">But, 4 : 16 : 20 </span><span style=\"font-family:Cambria Math\">→ (4 + 16) + 0 = 20</span></p>",
                    solution_hi: " <p>9.(b)</span></p> <p><span style=\"font-family:Nirmala UI\">तर्क</span><span style=\"font-family:Cambria Math\">: </span><span style=\"font-family:Cambria Math\">(</span><span style=\"font-family:Nirmala UI\">पहली</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">संख्या</span><span style=\"font-family:Cambria Math\">  + </span><span style=\"font-family:Nirmala UI\">दूसरी</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">संख्या</span><span style=\"font-family:Cambria Math\">) +  1 =  </span><span style=\"font-family:Nirmala UI\">तीसरी</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">संख्या</span><span style=\"font-family:Cambria Math\">    </span></p> <p><span style=\"font-family:Cambria Math\">12 : 47 : 60 </span><span style=\"font-family:Cambria Math\">→ (12 + 47) + 1 = 60 </span></p> <p><span style=\"font-family:Cambria Math\">2 : 7 : 10 </span><span style=\"font-family:Cambria Math\">→ (2 + 7) + 1 = 10 </span></p> <p><span style=\"font-family:Cambria Math\">5 : 19 : 25 </span><span style=\"font-family:Cambria Math\">→ (5 + 19) + 1 = 25 </span></p> <p><span style=\"font-family:Cambria Math\">But, 4 : 16 : 20 </span><span style=\"font-family:Cambria Math\">→ (4 + 16) + 0 = 20</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "6",
                    question_en: " <p>10. </span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">Three of the following four letter-clusters are alike in a certain way and one is different.</span></p> <p><span style=\"font-family:Cambria Math\">Pick the odd one out.</span></p> <p><span style=\"font-family:Cambria Math\">AFKP</span></p> <p><span style=\"font-family:Cambria Math\">GKPS</span></p> <p><span style=\"font-family:Cambria Math\">SXCH</span></p> <p><span style=\"font-family:Cambria Math\">JOTY</span></p>",
                    question_hi: " <p>10.</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">निम्नलिखित</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">चार</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">अक्षर</span><span style=\"font-family:Cambria Math\">-</span><span style=\"font-family:Nirmala UI\">समूहों</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">से</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">तीन</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">किसी</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">तरीके</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">से</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">समान</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">हैं</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">और</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">एक</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">भिन्न</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">है।</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">भिन्न</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">को</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">चुनें।</span></p> <p><span style=\"font-family:Cambria Math\">AFKP</span></p> <p><span style=\"font-family:Cambria Math\">GKPS</span></p> <p><span style=\"font-family:Cambria Math\">SXCH</span></p> <p><span style=\"font-family:Cambria Math\">JOTY</span></p>",
                    options_en: [" <p> </span><span style=\"font-family:Cambria Math\">JOTY</span></p>", " <p> </span><span style=\"font-family:Cambria Math\">SXCH</span></p>", 
                                " <p> </span><span style=\"font-family:Cambria Math\">GKPS</span></p>", " <p> </span><span style=\"font-family:Cambria Math\">AFKP</span></p>"],
                    options_hi: [" <p> </span><span style=\"font-family:Cambria Math\">JOTY</span></p>", " <p> </span><span style=\"font-family:Cambria Math\">SXCH</span></p>",
                                " <p> </span><span style=\"font-family:Cambria Math\">GKPS</span></p>", " <p> </span><span style=\"font-family:Cambria Math\">AFKP</span></p>"],
                    solution_en: " <p>10.(c)</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1705124250/word/media/image19.png\"/><span style=\"font-family:Cambria Math\">   </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1705124250/word/media/image20.png\"/><span style=\"font-family:Cambria Math\">   </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1705124250/word/media/image21.png\"/></p> <p><span style=\"font-family:Cambria Math\">But,</span><span style=\"font-family:Cambria Math\"> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1705124250/word/media/image22.png\"/></p>",
                    solution_hi: " <p>10.(c)</span></p> <p><span style=\"font-family:Cambria Math\">  </span><span style=\"font-family:Cambria Math\">  </span></p> <p><span style=\"font-family:Nirmala UI\">लेकिन</span><span style=\"font-family:Cambria Math\">, </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1705124250/word/media/image19.png\"/><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1705124250/word/media/image20.png\"/><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1705124250/word/media/image21.png\"/><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1705124250/word/media/image22.png\"/></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "6",
                    question_en: " <p><span style=\"font-family:Cambria Math\">11. </span><span style=\"font-family:Cambria Math\">Three of the following four letter-clusters are alike in a certain way and one is different. Pick the odd one out</span></p>",
                    question_hi: " <p><span style=\"font-family:Cambria Math\">11.</span><span style=\"font-family:Nirmala UI\">निम्नलिखित</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">चार</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">अक्षर</span><span style=\"font-family:Cambria Math\">-</span><span style=\"font-family:Nirmala UI\">समूहों</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">से</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">तीन</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">किसी</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">प्रकार</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">एक</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">जैसे</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">हैं</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">और</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">एक</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">भिन्न</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">है।</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">भिन्न</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">अक्षर</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">समूह</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">का</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">चयन</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">कीजिए।</span></p>",
                    options_en: [" <p> HMS</span></p>", " <p> AFZ  </span></p>", 
                                " <p> KQP  </span></p>", " <p> DIW</span></p>"],
                    options_hi: [" <p> HMS</span></p>", " <p> AFZ  </span></p>",
                                " <p> KQP  </span></p>", " <p> DIW </span></p>"],
                    solution_en: " <p>11.(c)</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1705124250/word/media/image23.png\"/><span style=\"font-family:Cambria Math\">  </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1705124250/word/media/image24.png\"/><span style=\"font-family:Cambria Math\">  </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1705124250/word/media/image25.png\"/></p> <p><span style=\"font-family:Cambria Math\">But, </span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1705124250/word/media/image26.png\"/></p>",
                    solution_hi: " <p>11.(c)</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1705124250/word/media/image27.png\"/><span style=\"font-family:Cambria Math\">  </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1705124250/word/media/image28.png\"/><span style=\"font-family:Cambria Math\">  </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1705124250/word/media/image29.png\"/></p> <p><span style=\"font-family:Nirmala UI\">लेकिन</span><span style=\"font-family:Cambria Math\">,</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1705124250/word/media/image30.png\"/></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "6",
                    question_en: " <p><span style=\"font-family:Cambria Math\">12.</span><span style=\"font-family:Cambria Math\">The second number in the given number-pairs is obtained by performing certain mathematical operation(s) on the first number. The same operation(s) is/are followed in all the number-pairs, except one. Find that odd number-pair. </span></p>",
                    question_hi: " <p><span style=\"font-family:Cambria Math\">12.</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">दिए</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">गए</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">संख्या</span><span style=\"font-family:Cambria Math\">-</span><span style=\"font-family:Nirmala UI\">युग्मों</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">दूसरी</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">संख्या</span><span style=\"font-family:Cambria Math\">, </span><span style=\"font-family:Nirmala UI\">पहली</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">संख्या</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">पर</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">कुछ</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">गणितीय</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">संक्रियाएँ</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">करके</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">प्राप्त</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">की</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">जाती</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">है।</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">एक</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">को</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">छोड</span><span style=\"font-family:Nirmala UI\">़कर</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">सभी</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">संख्या</span><span style=\"font-family:Cambria Math\"> -</span><span style=\"font-family:Nirmala UI\">युग्मों</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">समान</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">संक्रियाएँ</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">की</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">जाती</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">है।</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">वह</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">असंगत</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">संख्या</span><span style=\"font-family:Cambria Math\">-</span><span style=\"font-family:Nirmala UI\">युग्म</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">ज्ञात</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">कीजिए</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">जिस</span><span style=\"font-family:Cambria Math\"> - </span><span style=\"font-family:Nirmala UI\">पर</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">समान</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">संक्रियाएँ</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">नहीं</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">की</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">गई।</span></p>",
                    options_en: [" <p> 225 : 169  </span></p>", " <p> 361 : 400 </span></p>", 
                                " <p> 529 : 576 </span></p>", " <p> 441 : 484</span></p>"],
                    options_hi: [" <p> 225 : 169  </span></p>", " <p> 361 : 400 </span></p>",
                                " <p> 529 : 57</span><span style=\"font-family:Cambria Math\">6 </span></p>", " <p> 441 : 484</span></p>"],
                    solution_en: " <p>12.(a)</span></p> <p><span style=\"font-family:Cambria Math\">Logic :- Squares of consecutive number</span></p> <p><span style=\"font-family:Cambria Math\">361 : 400 :- (19)</span><span style=\"font-family:Cambria Math\">2</span><span style=\"font-family:Cambria Math\"> : (20)</span><span style=\"font-family:Cambria Math\">2</span></p> <p><span style=\"font-family:Cambria Math\">529 : 576 :- (23)</span><span style=\"font-family:Cambria Math\">2</span><span style=\"font-family:Cambria Math\"> : (24)</span><span style=\"font-family:Cambria Math\">2</span></p> <p><span style=\"font-family:Cambria Math\">441 : 484 :- (21)</span><span style=\"font-family:Cambria Math\">2</span><span style=\"font-family:Cambria Math\"> : (22)</span><span style=\"font-family:Cambria Math\">2</span></p> <p><span style=\"font-family:Cambria Math\">But,</span></p> <p><span style=\"font-family:Cambria Math\">225 : 169 :- (15)</span><span style=\"font-family:Cambria Math\">2</span><span style=\"font-family:Cambria Math\">: (13)</span><span style=\"font-family:Cambria Math\">2</span></p>",
                    solution_hi: " <p>12.(a)</span></p> <p><span style=\"font-family:Nirmala UI\">तर्क</span><span style=\"font-family:Cambria Math\"> :- </span><span style=\"font-family:Nirmala UI\">क्रमागत</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">संख्या</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">वर्ग</span></p> <p><span style=\"font-family:Cambria Math\">361 : 400 :- (19)</span><span style=\"font-family:Cambria Math\">2</span><span style=\"font-family:Cambria Math\"> : (20)</span><span style=\"font-family:Cambria Math\">2</span></p> <p><span style=\"font-family:Cambria Math\">529 : 576 :- (23)</span><span style=\"font-family:Cambria Math\">2</span><span style=\"font-family:Cambria Math\"> : (24)</span><span style=\"font-family:Cambria Math\">2</span></p> <p><span style=\"font-family:Cambria Math\">441 : 484 :- (21)</span><span style=\"font-family:Cambria Math\">2</span><span style=\"font-family:Cambria Math\"> : (22)</span><span style=\"font-family:Cambria Math\">2</span></p> <p><span style=\"font-family:Nirmala UI\">लेकिन</span><span style=\"font-family:Cambria Math\">,</span></p> <p><span style=\"font-family:Cambria Math\">225 : 169 :- (15)</span><span style=\"font-family:Cambria Math\">2</span><span style=\"font-family:Cambria Math\">: (13)</span><span style=\"font-family:Cambria Math\">2</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "6",
                    question_en: " <p><span style=\"font-family:Cambria Math\">13. </span><span style=\"font-family:Cambria Math\">The second number in the given number pairs is obtained by performing certain mathematical operation(s) on the first number. The same operation(s) are f</span><span style=\"font-family:Cambria Math\">ollowed in all the number pairs except one. Find that odd number pair.</span></p>",
                    question_hi: " <p><span style=\"font-family:Cambria Math\">13. </span><span style=\"font-family:Nirmala UI\">दिए</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">गए</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">संख्या</span><span style=\"font-family:Cambria Math\"> -</span><span style=\"font-family:Nirmala UI\">युग्मों</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">दूसरी</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">संख्या</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">पहली</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">संख्या</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">पर</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">कुछ</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">निश्चित</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">गणितीय</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">संक्रियाएं</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">करके</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">प्राप्त</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">की</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">जाती</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">है।</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">एक</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">को</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">छोड़कर</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">अन्य</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">सभी</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">संख्या</span><span style=\"font-family:Cambria Math\">- </span><span style=\"font-family:Nirmala UI\">युग्मों</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">समान</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">संक्रियाओं</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">का</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">पालन</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">किया</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">गया</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">है।</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">असंगत</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">संख्या</span><span style=\"font-family:Cambria Math\">-</span><span style=\"font-family:Nirmala UI\">युग्म</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">ज्ञात</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">कीजिए।</span></p>",
                    options_en: [" <p>  4 : 70</span></p>", " <p>  3 : 30</span></p>", 
                                " <p>  6 : 222</span></p>", " <p>  7 : 350</span></p>"],
                    options_hi: [" <p>  4 : 70</span></p>", " <p>  3 : 30</span></p>",
                                " <p>  6 :</span><span style=\"font-family:Cambria Math\"> 222</span></p>", " <p>  7 : 350</span></p>"],
                    solution_en: " <p>13.(a)</span></p> <p><span style=\"font-family:Cambria Math\">Logic :- (First number)</span><span style=\"font-family:Cambria Math\">3</span><span style=\"font-family:Cambria Math\"> + (First number) = Second number</span></p> <p><span style=\"font-family:Cambria Math\">3 : 30 :- (3)</span><span style=\"font-family:Cambria Math\">3</span><span style=\"font-family:Cambria Math\"> + 3 ⇒ (27)+ 3 = 30</span></p> <p><span style=\"font-family:Cambria Math\">6 : 222 :- (6)</span><span style=\"font-family:Cambria Math\">3</span><span style=\"font-family:Cambria Math\"> + 6 ⇒ (216) + 6 = 222</span></p> <p><span style=\"font-family:Cambria Math\">7 : 350 :- (7)</span><span style=\"font-family:Cambria Math\">3</span><span style=\"font-family:Cambria Math\"> + 7 ⇒ (343) + 7 = 350</span></p> <p><span style=\"font-family:Cambria Math\">But,</span></p> <p><span style=\"font-family:Cambria Math\">4 : 70 :- (4)</span><span style=\"font-family:Cambria Math\">3</span><span style=\"font-family:Cambria Math\"> + 4 ⇒ (64)+ 4 = 68 (Not 70)</span></p>",
                    solution_hi: " <p>13.(a)</span></p> <p><span style=\"font-family:Nirmala UI\">तर्क</span><span style=\"font-family:Cambria Math\"> :- (</span><span style=\"font-family:Nirmala UI\">पहली</span><span style=\"font-family:Cambria Math\">  </span><span style=\"font-family:Nirmala UI\">संख्या</span><span style=\"font-family:Cambria Math\">)</span><span style=\"font-family:Cambria Math\">3</span><span style=\"font-family:Cambria Math\"> + (</span><span style=\"font-family:Nirmala UI\">पहली</span><span style=\"font-family:Cambria Math\">  </span><span style=\"font-family:Nirmala UI\">संख्या</span><span style=\"font-family:Cambria Math\">) = </span><span style=\"font-family:Nirmala UI\">दूसरी</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">संख्या</span></p> <p><span style=\"font-family:Cambria Math\">3 : 30 :- (3)</span><span style=\"font-family:Cambria Math\">3</span><span style=\"font-family:Cambria Math\"> + 3 ⇒ (27)+ 3 = 30</span></p> <p><span style=\"font-family:Cambria Math\">6 : 222 :- (6)</span><span style=\"font-family:Cambria Math\">3</span><span style=\"font-family:Cambria Math\"> + 6 ⇒ (216) + 6 = 222</span></p> <p><span style=\"font-family:Cambria Math\">7 : 350 :- (7)</span><span style=\"font-family:Cambria Math\">3</span><span style=\"font-family:Cambria Math\"> + 7 ⇒ (343) + 7 = 350</span></p> <p><span style=\"font-family:Nirmala UI\">लेकिन</span><span style=\"font-family:Cambria Math\">,</span></p> <p><span style=\"font-family:Cambria Math\">4 : 70 :- (4)</span><span style=\"font-family:Cambria Math\">3</span><span style=\"font-family:Cambria Math\"> + 4 ⇒ (64)+ 4 = 68 (70 </span><span style=\"font-family:Nirmala UI\">नहीं</span><span style=\"font-family:Cambria Math\">)</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "6",
                    question_en: " <p>14. </span><span style=\"font-family:Cambria Math\"> Three of the following four letter-clusters are alike in a certain way and one is different. Pick the odd one out.</span></p>",
                    question_hi: " <p>14. </span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">निम्नलिखित</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">चार</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">अक्षर</span><span style=\"font-family:Cambria Math\">-</span><span style=\"font-family:Nirmala UI\">समूहों</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">से</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">तीन</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">किसी</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">प्रकार</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">एक</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">जैसे</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">हैं</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">और</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">एक</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">भिन्न</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">है।</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">भिन्न</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">अक्षर</span><span style=\"font-family:Cambria Math\">-</span><span style=\"font-family:Nirmala UI\">समूह</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">का</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">चयन</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">कीजिए।</span></p>",
                    options_en: [" <p> CAX</span></p>", " <p> AYZ</span></p>", 
                                " <p> FDU</span></p>", " <p> HDS</span></p>"],
                    options_hi: [" <p> CAX</span></p>", " <p> AYZ</span></p>",
                                " <p> FDU</span></p>", " <p> HDS</span></p>"],
                    solution_en: " <p>14.(d)</span></p> <p><span style=\"font-family:Cambria Math\">, </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1705124250/word/media/image31.png\"/><span style=\"font-family:Cambria Math\">, </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1705124250/word/media/image32.png\"/><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1705124250/word/media/image33.png\"/></p> <p><span style=\"font-family:Cambria Math\">But, </span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1705124250/word/media/image34.png\"/></p>",
                    solution_hi: " <p>14.(d)</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1705124250/word/media/image35.png\"/><span style=\"font-family:Cambria Math\">, , </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1705124250/word/media/image36.png\"/><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1705124250/word/media/image37.png\"/></p> <p><span style=\"font-family:Nirmala UI\">लेकिन</span><span style=\"font-family:Cambria Math\">,</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1705124250/word/media/image38.png\"/></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "misc",
                    question_en: " <p><span style=\"font-family:Cambria Math\">15. </span><span style=\"font-family:Cambria Math\">Three of the following four letter-clusters are alike in a certain way and one is different.</span></p> <p><span style=\"font-family:Cambria Math\">Pick the odd one out.</span></p> <p><span style=\"font-family:Cambria Math\">PTXB</span></p> <p><span style=\"font-family:Cambria Math\">GKOS</span></p> <p><span style=\"font-family:Cambria Math\">HKNQ</span></p> <p><span style=\"font-family:Cambria Math\">MQUY</span></p>",
                    question_hi: " <p><span style=\"font-family:Cambria Math\">15.</span><span style=\"font-family:Nirmala UI\">निम्नलिखित</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">चार</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">अक्षर</span><span style=\"font-family:Cambria Math\">-</span><span style=\"font-family:Nirmala UI\">समूहों</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">से</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">तीन</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">किसी</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">तरीके</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">से</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">समान</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">हैं</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">और</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">एक</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">भिन्न</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">है।</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">भिन्न</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">को</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">चुनें।</span></p> <p><span style=\"font-family:Cambria Math\">PTXB</span></p> <p><span style=\"font-family:Cambria Math\">GKOS</span></p> <p><span style=\"font-family:Cambria Math\">HKNQ</span></p> <p><span style=\"font-family:Cambria Math\">MQUY</span></p>",
                    options_en: [" <p> PTXB  </span></p>", " <p> MQUY </span></p>", 
                                " <p> GKOS  </span></p>", " <p> HKNQ</span></p>"],
                    options_hi: [" <p> PTXB  </span></p>", " <p> MQUY </span></p>",
                                " <p> GKOS  </span></p>", " <p> HKNQ</span></p>"],
                    solution_en: " <p>15.(d)</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1705124250/word/media/image39.png\"/><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1705124250/word/media/image40.png\"/><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1705124250/word/media/image41.png\"/></p> <p><span style=\"font-family:Cambria Math\">But, </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1705124250/word/media/image42.png\"/></p>",
                    solution_hi: " <p>15.(d)</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1705124250/word/media/image39.png\"/><span style=\"font-family:Cambria Math\"> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1705124250/word/media/image40.png\"/><span style=\"font-family:Cambria Math\"> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1705124250/word/media/image41.png\"/></p> <p><span style=\"font-family:Nirmala UI\">लेकिन</span><span style=\"font-family:Cambria Math\">, </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1705124250/word/media/image42.png\"/></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>