<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">5:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 15</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">15</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 5 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["18"] = {
                name: "General Awareness",
                start: 0,
                end: 13
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="18">General Awareness</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 14,
                end: 14
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "18",
                    question_en: "<p>1. In 1910, The United India House at Seattle, USA, was set up by _____.</p>",
                    question_hi: "<p>1. 1910 में USA के सिएटल में यूनाइटेड इंडिया हाउस की स्थापना _____के द्वारा की गई थी।</p>",
                    options_en: ["<p>Shyamji Krishna Varma</p>", "<p>Tarak Nath Das and GD Kumar</p>", 
                                "<p>Khudiram Bose and Prafulla Chaki</p>", "<p>VD Savarkar and Ganesh</p>"],
                    options_hi: ["<p>श्यामजी कृष्ण वर्मा</p>", "<p>तारकनाथ दास और जी.डी. कुमार</p>",
                                "<p>ख़ुदीराम बोस और प्रफ़ुल्ल चाकी</p>", "<p>वी.डी. सावरकर और गणेश</p>"],
                    solution_en: "<p>1.(b) <strong>Tarak Nath Das and GD Kumar.</strong> They were Indian revolutionaries who collaborated to promote Indian independence from British rule. Shyamji Krishna Varma founded the Indian Home Rule Society, India House, and The Indian Sociologist in London. Khudiram Bose and Prafulla Chaki were also revolutionaries involved in the Muzaffarpur Bomb Case on April 30, 1908. Vinayak Damodar Savarkar and his brother Ganesh Damodar Savarkar established the Abhinav Bharat Society, also known as the Young India Society, in 1904.</p>",
                    solution_hi: "<p>1.(b)<strong> तारकनाथ दास और जी.डी. कुमार। </strong>वे भारतीय क्रांतिकारी थे जिन्होंने ब्रिटिश शासन से भारतीय स्वतंत्रता को बढ़ावा देने के लिए सहयोग किया था। श्यामजी कृष्ण वर्मा ने लंदन में इंडियन होम रूल सोसाइटी, इंडिया हाउस और द इंडियन सोशियोलॉजिस्ट की स्थापना की थी। ख़ुदीराम बोस और प्रफ़ुल्ल चाकी भी 30 अप्रैल, 1908 को मुजफ्फरपुर बम कांड में शामिल क्रांतिकारी थे। विनायक दामोदर सावरकर और उनके भाई गणेश दामोदर सावरकर ने 1904 में अभिनव भारत सोसाइटी की स्थापना की, जिसे यंग इंडिया सोसाइटी के नाम से भी जाना जाता है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "18",
                    question_en: "<p>2. Ghiyas ud-din Balban had set up a ____ department called \'Diwan-i-arz\'.</p>",
                    question_hi: "<p>2. गयास उद-दीन बलबन ने एक ____विभाग की स्थापना करवाई थी, जिसे \'दीवान-ए-अर्ज़\' कहा जाता था।</p>",
                    options_en: ["<p>military</p>", "<p>commerce</p>", 
                                "<p>education</p>", "<p>agriculture</p>"],
                    options_hi: ["<p>सैन्य</p>", "<p>वाणिज्यिक</p>",
                                "<p>शैक्षिक</p>", "<p>कृषि</p>"],
                    solution_en: "<p>2.(a) <strong>military</strong>. Balban was the ninth Sultan of the Mamluk dynasty of Delhi Sultanate, he appointed spies in every Department to break the powers of Chahalgani. He Introduced the practice of Sijada (prostration) and Paibos (kissing the monarch\'s feet) in his court. Different departments in Delhi sultanate: Diwan-i-Risalat (judicial and religious administration), Diwan-i-Bandagan (slaves), Diwan-i-Ishtiaq (pensions), Diwan-i-Khairat (charity), and Diwan-i-Insha (correspondence).</p>",
                    solution_hi: "<p>2.(a) <strong>सैन्य। </strong>बलबन दिल्ली सल्तनत के मामलुक वंश का नौवां सुल्तान था, उसने चहलगानी की शक्तियों को विभक्त करने के लिए प्रत्येक विभाग में जासूस नियुक्त किया। उसने अपने दरबार में सिजदा (दंडवत प्रणाम) और पैबोस (राजा के पैर चूमना) की प्रथा शुरू की। दिल्ली सल्तनत में विभिन्न विभाग: दीवान-ए-रिसालत (न्यायिक और धार्मिक प्रशासन), दीवान-ए-बंदगान (दास), दीवान-ए-इश्तियाक (पेंशन), ​​दीवान-ए-खैरात (दान), और दीवान-ए-इंशा (पत्र-व्यवहार)।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "18",
                    question_en: "<p>3. Which of the following Delhi sultans introduced two coins, namely silver tanka and copper jital?</p>",
                    question_hi: "<p>3. दिल्ली के निम्नलिखित में से किस सुल्तान ने चांदी के टंका और तांबे के जीतल नामक दो सिक्के चलवाए?</p>",
                    options_en: ["<p>Qutbuddin Aibak</p>", "<p>Bahlul Lodi</p>", 
                                "<p>Ghiyasuddin Balban</p>", "<p>Iltutmish</p>"],
                    options_hi: ["<p>कुतुबुद्दीन ऐबक</p>", "<p>बहलोल लोदी</p>",
                                "<p>गयासुद्दीन बलबन</p>", "<p>इल्तुतमिश</p>"],
                    solution_en: "<p>3.(d) <strong>Iltutmish </strong>(1211-1236). He was a slave and son-in-law of Qutubuddin Aibak, considered the real founder of Delhi Sultanate. He organized his nobles into a group of forty known as the Turkan-i-Chahalgani or Dal Chalisa. He introduced the Iqtadari system, dividing the kingdom into Iqtas assigned to nobles in exchange for a salary, which influenced later kingdoms.</p>",
                    solution_hi: "<p>3.(d) <strong>इल्तुतमिश </strong>(1211-1236)। वह कुतुबुद्दीन ऐबक का गुलाम और दामाद था, जिसे दिल्ली सल्तनत का वास्तविक संस्थापक माना जाता है। उसने अपने कुलीनों को चालीस के समूह में संगठित किया जिसे तुर्कान-ए-चहलगानी या दल चालीसा के नाम से जाना जाता है। उसने इक्तादारी प्रणाली की शुरुआत की, जिसके तहत राज्य को इक्ता में विभाजित किया गया और वेतन के बदले कुलीनों को इक्ता दिया गया, जिसका बाद के राज्यों पर भी प्रभाव पड़ा।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "18",
                    question_en: "<p>4. Who founded the Theosophical Society in New York in 1875?</p>",
                    question_hi: "<p>4. 1875 में न्यूयॉर्क में थियोसोफिकल सोसायटी की स्थापना किसने की थी?</p>",
                    options_en: ["<p>Naoroji Furdunji and Satyendra Nath Bose</p>", "<p>Baba Dayal Das and Madan Lal</p>", 
                                "<p>Ramabai Ranade and GK Devadhar</p>", "<p>Madame HP Blavatsky and Colonel Olcott</p>"],
                    options_hi: ["<p>नौरोजी फरदुनजी और सत्येंद्र नाथ बोस</p>", "<p>बाबा दयाल दास और मदन लाल</p>",
                                "<p>रमाबाई रानाडे और जीके देवधर</p>", "<p>मैडम एच.पी. ब्लावात्स्की और कर्नल ओल्कोट</p>"],
                    solution_en: "<p>4.(d) <strong>Madame HP Blavatsky and Colonel Olcott.</strong> The Theosophical Society, emphasized the study of ancient Hindu, Buddhist, and Zoroastrian philosophies. It advocated for universal brotherhood based on principles from the Upanishads and Vedas. Annie Besant joined the society in 1889 and first came to India on 16 November 1893. Its headquarters in India was established in Adyar, Madras (now Chennai). Ramabai Ranade and G. K. Devadhar were both founders of the Poona Seva Sadan.</p>",
                    solution_hi: "<p>4.(d) <strong>मैडम एच.पी. ब्लावात्स्की और कर्नल ओल्कोट।</strong> थियोसोफिकल सोसाइटी ने प्राचीन हिंदू, बौद्ध और पारसी दर्शनों के अध्ययन पर जोर दिया। इन्होंने उपनिषदों और वेदों के सिद्धांतों पर आधारित सार्वभौमिक भाईचारे का समर्थन किया। एनी बेसेंट 1889 में सोसाइटी में शामिल हुईं और पहली बार 16 नवंबर 1893 को भारत आईं थी। भारत में इसका मुख्यालय अड्यार, मद्रास (अब चेन्नई) में स्थापित किया गया था। रमाबाई रानाडे और जी. के. देवधर दोनों पूना सेवा सदन के संस्थापक थे।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "18",
                    question_en: "<p>5. Who among the following sent Megasthenes to the court of Chandragupta Maurya?</p>",
                    question_hi: "<p>5. निम्नलिखित में से किसने मेगस्थनीज़ को चन्द्रगुप्त मौर्य के दरबार में भेजा था?</p>",
                    options_en: ["<p>Antiochus I Soter</p>", "<p>Alexander III</p>", 
                                "<p>Seleucus I Nicator</p>", "<p>Antiochus III</p>"],
                    options_hi: ["<p>एंटिओकस प्रथम सोटर</p>", "<p>अलेक्ज़ेंडर तृतीय</p>",
                                "<p>सेल्यूकस प्रथम निकेटर</p>", "<p>एंटिओकस तृतीय</p>"],
                    solution_en: "<p>5.(c) <strong>Seleucus I Nicator. </strong>Chandragupta Maurya (321-297 BCE) captured Pataliputra from the last Nanda ruler at age 25, aided by Kautilya (Chanakya). After the Seleucid-Mauryan War, he established a treaty with Seleucus Nicator I, gaining territories in Balochistan and Afghanistan. In exchange for 500 war elephants that helped Seleucus at the Battle of Ipsus, Chandragupta also married Seleucus\'s daughter, further strengthening their alliance.</p>",
                    solution_hi: "<p>5.(c) <strong>सेल्यूकस प्रथम निकेटर।</strong> चंद्रगुप्त मौर्य (321-297 ईसा पूर्व) ने 25 वर्ष की आयु में अंतिम नंद शासक से पाटलिपुत्र पर कब्ज़ा कर लिया था, जिसमें कौटिल्य (चाणक्य) ने उनकी सहायता की थी। सेल्यूसिड-मौर्य युद्ध के बाद, उन्होंने सेल्यूकस निकेटर प्रथम के साथ एक संधि की, जिसके तहत उन्हें बलूचिस्तान और अफगानिस्तान में क्षेत्र प्राप्त हुए। इप्सस के युद्ध में सेल्यूकस की सहायता करने वाले 500 युद्ध हाथियों के बदले में, चंद्रगुप्त ने सेल्यूकस की बेटी से विवाह भी किया, जिससे उनका संबंध और मजबूत हो गया।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "18",
                    question_en: "<p>6. \'Drain of Wealth\', a critique of the colonial exploitation, was given by which of the following nationalist leaders?</p>",
                    question_hi: "<p>6. औपनिवेशिक शोषण की आलोचना \'धन-निष्कासन\' (Drain of Wealth) निम्नलिखित में से किस राष्ट्रवादी नेता द्वारा दी गई थी?</p>",
                    options_en: ["<p>Dadabhai Naoroji</p>", "<p>Rajendra Prasad</p>", 
                                "<p>Chakravarti Rajagopalachari</p>", "<p>Sardar Vallabhbhai Patel</p>"],
                    options_hi: ["<p>दादाभाई नौरोजी</p>", "<p>राजेन्द्र प्रसाद</p>",
                                "<p>चक्रवर्ती राजगोपालाचारी</p>", "<p>सरदार वल्लभभाई पटेल</p>"],
                    solution_en: "<p>6.(a) <strong>Dadabhai Naoroji </strong>(Grand Old man of India). He explained in the theory of \'Drain of Wealth&rsquo; (1867) that how the British colonial government systematically drained India\'s wealth and resources to Britain, making India economically poor. He became President of the Congress Party in 1886 (Calcutta), 1893 (Lahore) and 1906 (Calcutta). Famous books: Dadabhai Naoroji - &ldquo;Poverty and Un-British Rule in India&rdquo;. Rajendra Prasad - &ldquo;India Divided&rdquo;. Chakravarti Rajagopalachari - &ldquo;Chakravarti Tirumagan&rdquo;. Sardar Vallabhbhai Patel - &ldquo;Arthik Evam Videsh Neeti&rdquo;.</p>",
                    solution_hi: "<p>6.(a) <strong>दादाभाई नौरोजी</strong> (ग्रैन्ड ओल्ड मैन ऑफ इंडिया)। उन्होंने \'ड्रैन ऑफ वेल्थ\' (1867) के सिद्धांत में बताया कि कैसे ब्रिटिश औपनिवेशिक सरकार ने व्यवस्थित रूप से भारत के धन और संसाधनों को ब्रिटेन ले गए, जिससे भारत आर्थिक रूप से निर्धन हो गया। वह 1886 (कलकत्ता), 1893 (लाहौर) और 1906 (कलकत्ता) में कांग्रेस पार्टी के अध्यक्ष बने। प्रसिद्ध पुस्तकें : दादा भाई नौरोजी - \"पावर्टी एण्ड अनब्रिटिश रूल इन इंडिया\"। राजेंद्र प्रसाद - &ldquo;इंडिया डिवाइडेड&rdquo;। चक्रवर्ती राजगोपालाचारी - &ldquo;चक्रवर्ती तिरुमगन&rdquo;। सरदार वल्लभ भाई पटेल - &ldquo;आर्थिक एवं विदेश नीति&rdquo;।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "18",
                    question_en: "<p>7. Which of the following Acts gave the government enormous powers to repress political activities, and allowed detention of political prisoners without trial for two years?</p>",
                    question_hi: "<p>7. निम्नलिखित में से किस अधिनियम ने सरकार को राजनीतिक गतिविधियों को दबाने की अपार शक्तियाँ दी, और राजनीतिक कैदियों को बिना मुकदमे के दो वर्ष तक हिरासत में रखने की अनुमति दी?</p>",
                    options_en: ["<p>India Contract Act, 1872</p>", "<p>Rowlatt Act, 1919</p>", 
                                "<p>Criminal Tribes Act, 1871</p>", "<p>Indian Slavery Act, 1843</p>"],
                    options_hi: ["<p>भारत अनुबंध अधिनियम, 1872</p>", "<p>रौलेट एक्ट, 1919</p>",
                                "<p>आपराधिक जनजाति अधिनियम, 1871</p>", "<p>भारतीय दासता अधिनियम, 1843</p>"],
                    solution_en: "<p>7.(b) <strong>Rowlatt Act, 1919 : </strong>This Act passed on March 10, 1919, was introduced by British colonial authorities to suppress nationalist and revolutionary movements in India. India Contract Act, 1872: Regulates contracts and agreements in India. Criminal Tribes Act, 1871: Targeted \"criminal tribes\" and was repealed in 1952. Indian Slavery Act, 1843: Abolished slavery in British India.</p>",
                    solution_hi: "<p>7.(b) <strong>रौलेट एक्ट, 1919: </strong>यह अधिनियम (एक्ट) 10 मार्च, 1919 को पारित हुआ था, जिसे भारत में राष्ट्रवादी और क्रांतिकारी आंदोलनों को दबाने के लिए ब्रिटिश औपनिवेशिक अधिकारियों द्वारा पेश किया गया था। भारत अनुबंध अधिनियम, 1872: भारत में अनुबंधों और समझौतों को नियंत्रित करता है। आपराधिक जनजाति अधिनियम, 1871: \"आपराधिक जनजातियों\" को लक्षित किया गया और 1952 में निरस्त कर दिया गया था। भारतीय दासता अधिनियम, 1843: ब्रिटिश भारत में दासता को समाप्त कर दिया गया था।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "18",
                    question_en: "<p>8. According to the inscriptions, Pushyamitra Shunga was a devotee of which of the following Gods?</p>",
                    question_hi: "<p>8. शिलालेखों के अनुसार, पुष्यमित्र शुंग निम्नलिखित में से किस देवता के भक्त थे?</p>",
                    options_en: ["<p>Rama</p>", "<p>Shiva</p>", 
                                "<p>Krishna</p>", "<p>Indra</p>"],
                    options_hi: ["<p>राम</p>", "<p>शिव</p>",
                                "<p>कृष्ण</p>", "<p>इंद्र</p>"],
                    solution_en: "<p>8.(c) <strong>Krishna</strong>. Pushyamitra Shunga founded the Shunga Empire after assassinating the last Mauryan emperor, Brihadratha Maurya. He ruled from 184 BC to 75 BC, driving out the Greeks through the Shunga-Greek war. The capital of the Shunga Empire was Pataliputra.</p>",
                    solution_hi: "<p>8.(c)<strong> कृष्ण।</strong> पुष्यमित्र शुंग ने अंतिम मौर्य सम्राट बृहद्रथ मौर्य की हत्या के बाद शुंग साम्राज्य की स्थापना की। उन्होंने 184 ईसा पूर्व से 75 ईसा पूर्व तक शासन किया, शुंग-यूनानी युद्ध के माध्यम से यूनानियों को बाहर निकाल दिया। शुंग साम्राज्य की राजधानी पाटलिपुत्र थी।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "18",
                    question_en: "<p>9. Khudai Khidmatgars, a non-violent movement among the Pathans was started by ____.</p>",
                    question_hi: "<p>9. पठानों के बीच एक अहिंसात्मक आंदोलन ख़ुदाई खि़दमतग़ार की शुरुआत _____ के द्वारा की गई थी।</p>",
                    options_en: ["<p>Alluri Sitarama Raju</p>", "<p>Bakht Khan</p>", 
                                "<p>Khan Abdul Ghaffar Khan</p>", "<p>Sachindra Bakshi</p>"],
                    options_hi: ["<p>अल्लूरी सीताराम राजू</p>", "<p>बख़्त खान</p>",
                                "<p>ख़ान अब्दुल ग़फ़्फ़ार ख़ान</p>", "<p>सचिन्द्र बख़्शी</p>"],
                    solution_en: "<p>9.(c) <strong>Khan Abdul Ghaffar Khan,</strong> known as \'Frontier Gandhi\' in India and \'Bacha Khan\' in Pakistan. Important Organizations and Their Founders : Atmiya Sabha- Raja Ram Mohan Roy , Arya Samaj- Swami Dayanand Saraswati, Muslim League- Agha Khan &amp; Salimullah, Sarvajanik Sabha- Dada Bhai Naoroji, Sharada Sadan - Rama Bai, Servants of Indian Society - Gopal Krishna Gokhale.</p>",
                    solution_hi: "<p>9.(c) <strong>ख़ान अब्दुल ग़फ़्फ़ार ख़ान,</strong> जिन्हें भारत में \'फ्रंटियर गांधी\' और पाकिस्तान में \'बाचा खान\' के नाम से जाना जाता है। महत्वपूर्ण संगठन एवं उनके संस्थापक: आत्मीय सभा - राजा राम मोहन राय, आर्य समाज - स्वामी दयानंद सरस्वती, मुस्लिम लीग - आगा खान और सलीमुल्लाह, सार्वजनिक सभा - दादा भाई नौरोजी, शारदा सदन - रमा बाई,सर्वेन्ट्स ऑफ़ इंडिया सोसाइटी - गोपाल कृष्ण गोखले।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "18",
                    question_en: "<p>10. To gain control over Kannauj the Tripartite Struggle was fought between which of the following dynasties?</p>",
                    question_hi: "<p>10. निम्नलिखित में से किन राजवंशों के बीच कन्नौज पर शासन करने के लिए त्रिपक्षीय संघर्ष हुआ था?</p>",
                    options_en: ["<p>Pratihara, Rashtrakuta and Pala</p>", "<p>Solanki, Parmar and Chandella</p>", 
                                "<p>Khalji, Tughlaq and Lodhi</p>", "<p>Pallava, Pandya and Chola</p>"],
                    options_hi: ["<p>प्रतिहार, राष्ट्रकूट और पाल</p>", "<p>सोलंकी, परमार और चंदेल</p>",
                                "<p>खि़लजी, तुग़लक और लोधी</p>", "<p>पल्लव, पांड्य और चोल</p>"],
                    solution_en: "<p>10.(a) Tripartite Struggle for Kannauj : This included the Palas, Pratiharas, and Rashtrakutas, lasting 200 years and weakening all three powers. This systemic wave of conflict facilitated the Turkish conquest, as Kanauj\'s strategic location on the Ganga trade route and its significance as Harshvardhana\'s former capital made it a vital center of power and commerce.</p>",
                    solution_hi: "<p>10.(a) कन्नौज के लिए त्रिपक्षीय संघर्ष: इसमें पाल, प्रतिहार और राष्ट्रकूट शामिल थे, जो 200 वर्ष तक चला और तीनों शक्तियों को कमजोर कर दिया। संघर्ष की इस व्यवस्थित लहर ने तुर्की विजय को आसान बना दिया, क्योंकि गंगा व्यापार मार्ग पर कन्नौज की रणनीतिक स्थिति और हर्षवर्धन की पूर्व राजधानी के रूप में इसके महत्व ने इसे शक्ति और वाणिज्य का एक महत्वपूर्ण केंद्र बना दिया।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "18",
                    question_en: "<p>11. Match the following social reform organisations with their respective founders.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1732004594329.png\" alt=\"rId4\" width=\"398\" height=\"112\"></p>",
                    question_hi: "<p>11. निम्नलिखित समाज सुधार संगठनों का उनसे संबंधित संस्थापकों के साथ मिलान कीजिए।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1732004594457.png\" alt=\"rId5\" width=\"281\" height=\"132\"></p>",
                    options_en: ["<p>a-ii, b-iii, c-iv, d-i</p>", "<p>a-iii, b-i, c-ii, d-iv</p>", 
                                "<p>a-iv, b-iii, c-ii, d-i</p>", "<p>a-i, b-ii, c-iv, d-iii</p>"],
                    options_hi: ["<p>a-ii, b-iii, c-iv, d-i</p>", "<p>a-iii, b-i, c-ii, d-iv</p>",
                                "<p>a-iv, b-iii, c-ii, d-i</p>", "<p>a-i, b-ii, c-iv, d-iii</p>"],
                    solution_en: "<p>11.(b) <strong>a-iii, b-i, c-ii, d-iv. </strong>The Brahmo Samaj was a monotheistic reform movement in Hinduism that started in 1828 in Kolkata. The Ramakrishna Mission was founded by Ramakrishna\'s chief disciple, Swami Vivekananda, at Belur Math, West Bengal, on May 1, 1897. Prarthana Samaj, meaning \"Prayer Society\" in Sanskrit, emerged as a Hindu reform movement in Bombay in 1867. Arya Samaj was established in April 1875 in Bombay.</p>",
                    solution_hi: "<p>11.(b)<strong> a-iii, b-i, c-ii, d-iv. </strong>ब्रह्मो समाज हिंदू धर्म में एक एकेश्वरवादी सुधार आंदोलन था जो 1828 में कोलकाता में शुरू हुआ था। रामकृष्ण मिशन की स्थापना रामकृष्ण के प्रमुख शिष्य स्वामी विवेकानंद ने 1 मई, 1897 को पश्चिम बंगाल के बेलूर मठ में की थी। प्रार्थना समाज, जिसका संस्कृत में अर्थ है \"प्रार्थना संघ\", 1867 में बॉम्बे में एक हिंदू सुधार आंदोलन के रूप में उभरा। आर्य समाज की स्थापना अप्रैल 1875 में बॉम्बे में की गई थी।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "18",
                    question_en: "12. The revolution of 1857 started in May from which of the following cantonments in India?",
                    question_hi: "12. 1857 की क्रांति मई में भारत की किस छावनी से आरंभ हुई थी?",
                    options_en: [" Kanpur ", " Jhansi", 
                                " Lucknow", " Meerut<br /> "],
                    options_hi: [" कानपुर", " झांसी",
                                " लखनऊ", " मेरठ"],
                    solution_en: "<p>12.(d) <strong>Meerut</strong>. The 1857 Revolution, also known as the Indian Mutiny, began in Meerut on May 10 and spread to Delhi, Agra, Kanpur, and Lucknow. It was suppressed after the rebels were defeated in Gwalior on June 20, 1858. Mangal Pandey is recognized as the first martyr of the revolt. The British referred to the uprising as the \'devil\'s wind\' and ultimately suppressed it in 1859. Following the revolt, the Government of India Act 1858 transferred control of Indian administration to the British crown.</p>",
                    solution_hi: "<p>12.(d) <strong>मेरठ</strong>। 1857 की क्रांति, जिसे भारतीय विद्रोह के नाम से भी जाना जाता है, 10 मई को मेरठ में शुरू हुई और दिल्ली, आगरा, कानपुर और लखनऊ तक फैल गई। 20 जून, 1858 को ग्वालियर में विद्रोहियों की हार के बाद इस विद्रोह दबा दिया गया था। मंगल पांडे को विद्रोह के पहले शहीद के रूप में जाना जाता था। अंग्रेजों ने विद्रोह को \'डेविल्स विंड\' कहा और अंततः 1859 में इसे दबा दिया। विद्रोह के बाद, भारत सरकार अधिनियम 1858 ने भारतीय प्रशासन का नियंत्रण ब्रिटिश क्राउन को हस्तांतरित कर दिया।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "18",
                    question_en: "<p>13. To promote agriculture, Muhammad Tughlaq founded which of the following new ministries?</p>",
                    question_hi: "<p>13. कृषि को बढ़ावा देने के लिए मुहम्मद तुग़लक ने निम्नलिखित में से कौन-से नए मंत्रालय की स्थापना की थी?</p>",
                    options_en: ["<p>Diwan-i-Arz</p>", "<p>Diwan-i Amir-i kohi</p>", 
                                "<p>Diwan-i-Insha</p>", "<p>Diwan-i-Risalat</p>"],
                    options_hi: ["<p>दीवान-ए-अर्ज़</p>", "<p>दीवान-ए अमीर-ए कोही</p>",
                                "<p>दीवान-ए-इंशा</p>", "<p>दीवान-ए-रिसालत</p>"],
                    solution_en: "<p>13.(b) <strong>Diwan-i Amir-i kohi. </strong>The Diwan-i-Kohi aimed to increase land under cultivation. Muhammad Tughlaq also promoted agriculture by abolishing or reducing agrarian cesses and introducing agricultural loans called Sondhar. The Diwan-i-Arz, established by Ghiyas ud din Balban, was the military department of the Delhi Sultanate. The Diwan-i-Insha managed royal correspondence, decrees, letters, and official documents. The Diwan-i-Risalat handled foreign affairs and religious matters, overseeing diplomacy, protocol, and sending ambassadors.</p>",
                    solution_hi: "<p>13.(b) <strong>दीवान-ए अमीर-ए कोही।</strong> दीवान-ए-कोही का उद्देश्य खेती के तहत भूमि को बढ़ाना था। मुहम्मद तुगलक ने कृषि उपकरों को समाप्त करके या कम करके तथा सोंधर नामक कृषि ऋण शुरू करके कृषि को बढ़ावा दिया। ग़यासुद्दीन बलबन द्वारा स्थापित दीवान-ए-अर्ज़, दिल्ली सल्तनत का सैन्य विभाग था। दीवान-ए-इंशा शाही पत्राचार, फरमान, पत्र और आधिकारिक दस्तावेजों का प्रबंधन करता था। दीवान-ए-रिसालत विदेशी मामलों और धार्मिक मामलों को संभालता था, कूटनीति, प्रोटोकॉल की देखरेख करता था और राजदूत भेजता था।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "18",
                    question_en: "<p>14. Who among the following Pallava kings occupied Vatapi (Badami) and defeated the Chalukyas?</p>",
                    question_hi: "<p>14. निम्नलिखित में से किस पल्लव राजा ने वातापी (बादामी) पर कब्ज़ा करके चालुक्यों को हराया था?</p>",
                    options_en: ["<p>Sivaskanda Varman</p>", "<p>Parameswaravarman</p>", 
                                "<p>Simhavarman</p>", "<p>Narasimhavarman</p>"],
                    options_hi: ["<p>शिवस्कंद वर्मन</p>", "<p>परमेश्वरवर्मन</p>",
                                "<p>सिंहवर्मन</p>", "<p>नरसिंहवर्मन</p>"],
                    solution_en: "<p>14.(d) <strong>Narasimhavarman</strong>. Pulakesin II, a ruler of the Chalukya dynasty, reigned from 610 CE to 642 CE, expanding his empire across much of the Deccan region. However, in 642-643 CE, Pallava king Narasimha Varman I defeated Pulakesin II when his general, Shiruttondar Paranjoti, captured the Chalukya capital, Vatapi. Simhavishnu is regarded as the founder of the Pallava dynasty, a Tamil dynasty that ruled parts of South India from the 3rd to the 9th century.</p>",
                    solution_hi: "<p>14.(d) <strong>नरसिंहवर्मन</strong>। चालुक्य वंश के शासक पुलकेशिन द्वितीय ने 610 ई. से 642 ई. तक शासन किया और अपने साम्राज्य का विस्तार दक्कन क्षेत्र के अधिकांश भाग में किया है। हालाँकि, 642-643 ई. में, पल्लव राजा नरसिंह वर्मन प्रथम ने पुलकेशिन द्वितीय को तब हराया जब उनके सेनापति शिरुत्तोंदर परंजोति ने चालुक्य की राजधानी वातापी पर कब्ज़ा कर लिया था। सिंहविष्णु को पल्लव वंश का संस्थापक माना जाता है, जो एक तमिल राजवंश था जिसने तीसरी से 9वीं शताब्दी तक दक्षिण भारत के कुछ हिस्सों पर शासन किया था।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "misc",
                    question_en: "<p>15. Medini Rai of Chanderi, Hasan Khan of Mewat and Mahmud Lodi joined Rana Sanga with their forces to fight against which of the following Mughal rulers?</p>",
                    question_hi: "<p>15. चंदेरी के मेदिनी राय, मेवात के हसन खान और महमूद लोदी निम्नलिखित में से किस मुग़ल शासक के खिलाफ लड़ने के लिए अपनी सेना सहित राणा सांगा के साथ मिल गए थे?</p>",
                    options_en: ["<p>Babur</p>", "<p>Akbar</p>", 
                                "<p>Aurangzeb</p>", "<p>Humayun</p>"],
                    options_hi: ["<p>बाबर</p>", "<p>अकबर</p>",
                                "<p>औरंगजेब</p>", "<p>हुमायूँ</p>"],
                    solution_en: "<p>15.(a) <strong>Babur</strong>. He was a descendant of Genghis Khan and Timur, founded the Mughal Empire in northern India, making Agra its power center. He achieved victories in these key battles: the Battle of Khanwa (1527), where he defeated Rana Sanga and the Rajput Confederation for dominance in northern India; the Battle of Chanderi (1528), where he defeated Medini Rai of Malwa; and the Battle of Ghagra (1529), where he defeated Mahmud Lodi.</p>",
                    solution_hi: "<p>15.(a) <strong>बाबर</strong>। वह चंगेज खान और तैमूर का वंशज था, उसने उत्तर भारत में मुगल साम्राज्य की स्थापना की, आगरा को अपना शक्ति केंद्र बनाया। उसने इन प्रमुख लड़ाइयों में जीत हासिल की: खानवा की लड़ाई (1527), जहाँ उसने उत्तर भारत में प्रभुत्व के लिए राणा सांगा और राजपूत संघ को हराया; चंदेरी की लड़ाई (1528), जहाँ उसने मालवा के मेदिनी राय को हराया; और घाघरा की लड़ाई (1529), जहाँ उसने महमूद लोदी को हराया।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>