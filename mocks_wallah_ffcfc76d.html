<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">10:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 10</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">10</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 10 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["31"] = {
                name: "Computer Knowledge",
                start: 0,
                end: 8
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="31">Computer Knowledge</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 9,
                end: 9
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "31",
                    question_en: "<p>1. Which of the following chart types is best suited for showing trends over time?</p>",
                    question_hi: "<p>1. समय के साथ रूझान (trends) प्रदर्शित करने के लिए निम्नलिखित में से किस प्रकार का चार्ट सबसे उपयुक्त है?</p>",
                    options_en: ["<p>Line chart</p>", "<p>Scatter plot</p>", 
                                "<p>Pie chart</p>", "<p>Bar chart</p>"],
                    options_hi: ["<p>लाइन चार्ट (Line chart)</p>", "<p>स्कैटर प्लॉट (Scatter plot)</p>",
                                "<p>पाई चार्ट (Pie chart)</p>", "<p>बार चार्ट (Bar chart)</p>"],
                    solution_en: "<p>1.(a) <strong>Line chart.</strong> These charts use lines to connect data points, allowing you to visualize how a value changes over time. This makes them ideal for highlighting trends and patterns in data collected over regular intervals (e.g., daily, monthly, yearly).</p>",
                    solution_hi: "<p>1.(a) <strong>लाइन चार्ट</strong> (Line chart)। ये चार्ट डेटा बिंदुओं को जोड़ने के लिए लाइनों का उपयोग करते हैं, जिससे आप यह कल्पना कर सकते हैं कि समय के साथ मूल्य कैसे बदलता है। यह उन्हें नियमित अंतराल (जैसे, दैनिक, मासिक, वार्षिक) पर एकत्र किए गए डेटा में रुझान और पैटर्न को उजागर करने के लिए आदर्श बनाता है।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "2",
                    section: "31",
                    question_en: "2.  Which of the following options quickly changes the font of the selected text in Microsoft Word 365 without opening the Font dialog box?",
                    question_hi: "2. निम्न में से कौन-सा विकल्प फ़ॉन्ट डायलॉग बॉक्स (Font dialog box) खोले बिना माइक्रोसॉफ्ट वर्ड 365 (Microsoft Word 365) में सेलेक्‍ट किए गए टेक्स्ट के फ़ॉन्ट को तुरंत बदल देता है?",
                    options_en: [" Right-click and select \"Font\"  ", " Press Ctrl + + F ", 
                                " Use the Font drop-down menu on the Status bar ", " Double-click the text"],
                    options_hi: [" राइट-क्लिक करना और \"Font\" सेलेक्‍ट करना ", " Ctrl + + F दबाना ",
                                " स्टेटस बार पर फ़ॉन्ट ड्रॉप-डाउन मेन्‍यू (Font drop-down menu) का उपयोग करना", " टेक्स्ट पर डबल-क्लिक करना"],
                    solution_en: "<p>2.(a) <strong>Right-click and select \"Font\".</strong> Ctrl + F - It opens a find box that allows you to search for characters, text, and phrases in the current document. Double click on a word if you want to select it.</p>",
                    solution_hi: "<p>2.(a) <strong>राइट-क्लिक (Right-click) करना और \"Font\" सेलेक्&zwj;ट करना।</strong> Ctrl + F - यह एक फाइन्ड बॉक्स (find box) खोलता है जो आपको करेंट दस्तावेज़ में कैरेक्टर (characters), टेस्ट (text) और वाक्यांशों (phrases) को खोजने (search) की अनुमति देता है। यदि आप किसी शब्द को सेलेक्ट (select) करना चाहते हैं तो उस पर डबल क्लिक (Double click) करें।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "3",
                    section: "31",
                    question_en: "3. The ________ symbol is employed to separate the username and domain name within an email address. ",
                    question_hi: "3. ई-मेल एड्रेस में यूज़रनेम और डोमेन नेम पृथक करने के लिए __________ प्रतीक का उपयोग किया जाता है।",
                    options_en: [" &  ", " . (dot) ", 
                                " @ ", " $"],
                    options_hi: [" &  ", " .(dot) ",
                                " @ ", " $"],
                    solution_en: "<p>3.(c) <strong>@.</strong> &amp; - Connects two strings of text (concatenation). * and / - Multiplication and division. A formula always begins with an equal sign (=).</p>",
                    solution_hi: "<p>3.(c) <strong>@ ।</strong> &amp; - टेस्ट (text) (concatenation) के दो स्ट्रिंग (strings) को जोड़ता है । * और/ - गुणा (Multiplication) और भाग (division)। कोई भी सूत्र (formula) हमेशा बराबर के चिन्ह (=) से शुरू होता है।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "4",
                    section: "31",
                    question_en: "<p>4. Which of the following is NOT a Change Case option in MS word&nbsp;365?</p>",
                    question_hi: "<p>4. एमएस वर्ड 365 (MS Word 365) में, निम्न में से कौन-सा एक चेंज केस (Change Case) ऑप्&zwj;शन नहीं होता है?</p>",
                    options_en: ["<p>Sentence case</p>", "<p>Page case</p>", 
                                "<p>Uppercase</p>", "<p>Toggle case</p>"],
                    options_hi: ["<p>सेन्&zwj;टेंस केस (Sentence case)</p>", "<p>पेज केस (Page case)</p>",
                                "<p>अपरकेस (Uppercase)</p>", "<p>टॉगलकेस (Toggle case)</p>"],
                    solution_en: "<p>4.(b) <strong>Page case. </strong>In the Home tab, the font consists of change cases. To capitalize the first letter of a sentence and leave all other letters as lowercase, click Sentence case. To exclude capital letters from your text, click lowercase. To shift between two case views (for example, to shift between Capitalize Each Word and the opposite, cAPITALIZE eACH wORD), click tOGGLE cASE.</p>",
                    solution_hi: "<p>4.(b) <strong>पेज केस </strong>(Page case)। होम टैब (Home tab) में, फ़ॉन्ट में परिवर्तन के केस (case) शामिल होते हैं। किसी वाक्य के पहले अक्षर को बड़े अक्षरों में लिखने और अन्य सभी अक्षरों को छोटे अक्षरों में रखने के लिए, सेंटेन्स केस (Sentence case) पर क्लिक करें। अपने टेक्स्ट (text) से बड़े अक्षरों को बाहर करने के लिए, छोटे अक्षरों (lowercase) पर क्लिक करें। दो केस दृश्यों (उदाहरण के लिए, प्रत्येक शब्द को बड़े अक्षरों में लिखने और उसके विपरीत के बीच बदलाव करने के लिए , cAPITALIZE eACH wORD) के बीच शिफ्ट करने के लिए, टॉगल केस (tOGGLE cASE) पर क्लिक करें।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "5",
                    section: "31",
                    question_en: "<p>5. What is the primary differentiating factor between broadband and dialup connections offered by ISPs?</p>",
                    question_hi: "<p>5. आईएसपी (ISP) द्वारा प्रदान किए गए ब्रॉडबैंड और डायलअप कनेक्शन के बीच प्राथमिक अंतर कारक क्या है?</p>",
                    options_en: ["<p>Dialup connections offer higher security levels than broadband.</p>", "<p>Broadband provides faster internet speeds compared to dialup.</p>", 
                                "<p>Dialup connections are wireless, while broadband requires wired connections.</p>", "<p>Dialup offers unlimited data usage, unlike broadband</p>"],
                    options_hi: ["<p>ब्रॉडबैंड की तुलना में डायलअप कनेक्शन उच्च सुरक्षा स्तर प्रदान करते हैं</p>", "<p>डायलअप की तुलना में ब्रॉडबैंड अधिक तेज इंटरनेट स्पीड प्रदान करता है</p>",
                                "<p>डायलअप कनेक्शन वायरलेस होते हैं, जबकि ब्रॉडबैंड के लिए वायरयुक्त कनेक्शन की आवश्यकता होती है।</p>", "<p>ब्रॉडबैंड के विपरीत, डायलअप असीमित डेटा उपयोग प्रदान करता है।</p>"],
                    solution_en: "<p>5.(b) Broadband allows for quicker loading of web pages, streaming of videos and music, and downloading of large files. Internet service provider (ISP) is a company that provides individuals and organizations access to the internet and other related services.</p>",
                    solution_hi: "<p>5.(b) ब्रॉडबैंड वेब पेजों को तेजी से लोड करने, वीडियो और संगीत की स्ट्रीमिंग और बड़ी फ़ाइलों को डाउनलोड करने की अनुमति देता है। इंटरनेट सर्विस प्रोवाइडर (ISP) एक ऐसी कंपनी है जो व्यक्तियों (individuals) और संगठनों (organizations) को इंटरनेट और अन्य संबंधित सेवाओं (services) तक पहुंच प्रदान करती है।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "6",
                    section: "31",
                    question_en: "6. What is the purpose of the Help feature in Microsoft Word 365?",
                    question_hi: "6. माइक्रोसॉफ्ट वर्ड 365 (Microsoft Word 365) में, हेल्प (Help) फीचर का उद्देश्य क्या होता है?",
                    options_en: [" To provide access to online tutorials and resources ", " To provide assistance in creating documents  ", 
                                " To allow customisation of the user interface ", " To offer grammar and spelling suggestions"],
                    options_hi: ["<p>ऑनलाइन ट्यूटोरियल और संसाधनों (resources) के लिए एक्सेस प्रदान करना</p>", "<p>डॉक्यूमेंट बनाने में सहायता प्रदान करना</p>",
                                "<p>यूज़र इंटरफ़ेस (user interface) के कस्टमाइजेशन (customisation) की सुविधा देना</p>", "<p>व्याकरण और वर्तनी संबंधी सुझाव देना</p>"],
                    solution_en: "<p>6.(b) There are three primary ways to access the Help feature: Tell Me Box: Click in the \"Tell me what you want to do\" box in the top right corner of the Word window. Type your query related to a Word function or task, and the Help feature will suggest relevant articles or tutorials. Help Menu: Click on the \"File\" tab and then navigate to \"Help\" in the left-hand pane. This opens a menu with various Help options. F1 Key: Press the F1 key on your keyboard. This is a universal shortcut that opens context-sensitive Help based on the current selection or task you\'re working on in Word.</p>",
                    solution_hi: "<p>6.(b) Help feature तक पहुंचने (access) के तीन प्रमुख तरीके हैं: टेल मी बॉक्स (Tell me box): वर्ड विंडो के ऊपरी दाईं कोने में \"Tell me what you want to do\" बॉक्स में क्लिक करें। एक वर्ड कार्य के संबंध में अपना प्रश्न टाइप करें, और हेल्प फीचर संबंधित लेख या ट्यूटोरियल का सुझाव देगा। हेल्प मेनू: \"फ़ाइल\" टैब पर क्लिक करें और फिर बाईं-हाथ की पट्टी में \"हेल्प\" पर नेविगेट करें। यह विभिन्न हेल्प विकल्पों के साथ एक मेनू खोलता है। F1 कुंजी: अपने कीबोर्ड पर F1 कुंजी दबाएं। यह एक सामान्य शॉर्टकट है जो वर्तमान चयन या कार्य के आधार पर संदर्भ-संवेदी हेल्प खोलता है जिस पर आप वर्ड में काम कर रहे हैं।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "7",
                    section: "31",
                    question_en: "<p>7. How do you change the font style and size of selected text in MS-Excel 365?</p>",
                    question_hi: "<p>7. एमएस एक्सेल 365 (MS Excel 365) में, चयनित टेक्स्ट की फ़ॉन्ट शैली और साइज़ कैसे बदल सकते हैं?</p>",
                    options_en: ["<p>Access the &lsquo;Home&rsquo; tab, use the &lsquo;Font&rsquo; group</p>", "<p>Apply a new cell style to the selected text</p>", 
                                "<p>Press the &lsquo;Ctrl+F&rsquo; shortcut keys together</p>", "<p>Right-click the text and choose &lsquo;Font&rsquo;.</p>"],
                    options_hi: ["<p>होम\' टैब को एक्&zwj;सेस करें, \'फ़ॉन्ट\' ग्रुप का उपयोग करें।</p>", "<p>चयनित टेक्स्ट पर एक नया सेल स्टाइल लागू करें।</p>",
                                "<p>\'Ctrl+F\' शॉर्टकट की एक साथ दबाएँ।</p>", "<p>टेक्स्ट पर राइट-क्लिक करें और \'फ़ॉन्ट\' चुनें।</p>"],
                    solution_en: "<p>7.(a) The Font group on the Home tab provides a dedicated area for changing font styles, sizes, and other text formatting options within your selected Excel cells. This makes it the most efficient way to achieve the desired outcome.</p>",
                    solution_hi: "<p>7.(a) होम टैब पर फ़ॉन्ट समूह आपके चयनित एक्सेल सेल के भीतर फ़ॉन्ट शैली, आकार और अन्य टेक्स्ट फ़ॉर्मेटिंग विकल्पों को बदलने के लिए एक समर्पित क्षेत्र प्रदान करता है। यह इसे वांछित परिणाम प्राप्त करने का सबसे प्रभावी तरीका बनाता है।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "8",
                    section: "31",
                    question_en: "<p>8. Which of the following is the correct option to create a table in MS-Word 2010?</p>",
                    question_hi: "<p>8. एमएस-वर्ड 2010 (MS-Word 2010) में टेबल क्रिएट करने के लिए निम्नलिखित में से कौन-सा विकल्प सही है?</p>",
                    options_en: ["<p>Home Tab &rarr; Create Table</p>", "<p>Home Tab &rarr; Insert Table</p>", 
                                "<p>Create Tab &rarr; Insert Table</p>", "<p>Insert Tab &rarr; Table Button</p>"],
                    options_hi: ["<p>Home Tab &rarr; Create Table</p>", "<p>Home Tab &rarr; Insert Table</p>",
                                "<p>Create Tab &rarr; Insert Table</p>", "<p>Insert Tab &rarr; Table Button</p>"],
                    solution_en: "<p>8.(d) <strong>Insert Tab &rarr; Table Button</strong></p>",
                    solution_hi: "<p>8.(d) <strong>Insert Tab &rarr; Table Button</strong></p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "9",
                    section: "31",
                    question_en: "<p>9. Which of the following functions is executed in MS Word 365 when Ctrl + V is used?</p>",
                    question_hi: "<p>9. एमएस वर्ड 365 (MS Word 365) में \'Ctrl + V\' का उपयोग करने पर निम्नलिखित में से कौन-सा कार्य निष्पादित होता है?</p>",
                    options_en: ["<p>Copy the data</p>", "<p>Cut the data</p>", 
                                "<p>Paste the data</p>", "<p>Select all the data</p>"],
                    options_hi: ["<p>डेटा \'कॉपी\' होता है</p>", "<p>डेटा \'कट\' होता है</p>",
                                "<p>डेटा \'पेस्ट\' होता है</p>", "<p>संपूर्णडेटा \'सेलेक्ट\' होता है</p>"],
                    solution_en: "<p>9.(c) <strong>Paste the data. </strong>Select all the data - Ctrl + A.</p>",
                    solution_hi: "<p>9.(c) <strong>डेटा \'पेस्ट\' होता है</strong> (Paste the data)। संपूर्ण डेटा \'सेलेक्ट\' करने के लिए - Ctrl + A.</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "10",
                    section: "misc",
                    question_en: "<p>10. In a worksheet, there is a list of student scores in a column. To count the number of students who scored above 90, which Excel 365 function should be used?</p>",
                    question_hi: "<p>10. वर्कशीट में, एक कॉलम में विद्यार्थियों के अंकों की एक सूची है। 90 से अधिक अंक प्राप्त करने वाले विद्यार्थियों की संख्या की गणना के लिए एक्सेल 365 (Excel 365) के किस फ़ंक्शन का उपयोग किया जाना चाहिए?</p>",
                    options_en: ["<p>COUNT</p>", "<p>COUNTIF</p>", 
                                "<p>COUNTBLANK</p>", "<p>COUNTA</p>"],
                    options_hi: ["<p>COUNT</p>", "<p>COUNTIF</p>",
                                "<p>COUNTBLANK</p>", "<p>COUNTA</p>"],
                    solution_en: "<p>10.(b) <strong>COUNTIF -</strong> One of the statistical functions, to count the number of cells that meet a criterion; for example, to count the number of times a particular city appears in a customer list. COUNTBLANK function, one of the Statistical functions, to count the number of empty cells in a range of cells.</p>",
                    solution_hi: "<p>10.(b) <strong>COUNTIF -</strong> सांख्यिकीय कार्यों में से एक, किसी मानदंड को पूरा करने वाली सेल की संख्या की गणना करना; उदाहरण के लिए, यह गिनना(count) कि कोई विशेष शहर ग्राहक सूची(customer list) में कितनी बार दिखाई देता है। COUNTBLANK फ़ंक्शन, सांख्यिकीय कार्यों में से एक, सेल की एक श्रृंखला में रिक्त सेल (empty cells ) की संख्या की गणना करने के लिए होता है।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>