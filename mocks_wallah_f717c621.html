<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">8:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 25</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">25</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 8 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["7"] = {
                name: "English",
                start: 0,
                end: 23
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="7">English</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 24,
                end: 24
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "7",
                    question_en: "<p>1. A state governed by old people</p>\r\n<p><span style=\"font-family: Times New Roman;\">SSC CGL Tier II, 18/11/2020 </span></p>",
                    question_hi: "",
                    options_en: ["<p>Autocracy</p>", "<p>Bureaucracy</p>", 
                                "<p>Gerontocracy</p>", "<p>Democracy</p>"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: "<p>1.c.<strong><span style=\"font-family: Times New Roman;\">Gerontocracy</span></strong><span style=\"font-family: Times New Roman;\">- a state governed by old people.</span></p>\r\n<p><strong><span style=\"font-family: Times New Roman;\">Autocracy</span></strong><span style=\"font-family: Times New Roman;\">- a system of government of a country in which one person has complete power</span></p>\r\n<p><strong><span style=\"font-family: Times New Roman;\">Bureaucracy</span></strong><span style=\"font-family: Times New Roman;\">- a system of government run by state officials.</span></p>\r\n<p><strong><span style=\"font-family: Times New Roman;\">Democracy</span></strong><span style=\"font-family: Times New Roman;\">- a system in which the government of a country is elected by the people.</span></p>",
                    solution_hi: "<p>1.c.<strong><span style=\"font-family: Times New Roman;\">Gerontocracy</span></strong><span style=\"font-family: Times New Roman;\">- a state governed by old people.</span></p>\r\n<p><strong><span style=\"font-family: Times New Roman;\">Autocracy</span></strong><span style=\"font-family: Times New Roman;\">- a system of government of a country in which one person has complete power</span></p>\r\n<p><strong><span style=\"font-family: Times New Roman;\">Bureaucracy</span></strong><span style=\"font-family: Times New Roman;\">- a system of government run by state officials.</span></p>\r\n<p><strong><span style=\"font-family: Times New Roman;\">Democracy</span></strong><span style=\"font-family: Times New Roman;\">- a system in which the government of a country is elected by the people.</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "7",
                    question_en: "<p>2.A statement which cannot be contradicted</p>\r\n<p><span style=\"font-family: Times New Roman;\">SSC CGL Tier II, 18/11/2020</span></p>",
                    question_hi: "",
                    options_en: ["<p>Irresistible</p>", "<p>Irrational</p>", 
                                "<p>Irreparable</p>", "<p>Irrefutable</p>"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: "<p>2.d.<strong><span style=\"font-family: Times New Roman;\">Irrefutable</span></strong><span style=\"font-family: Times New Roman;\">- a statement which cannot be contradicted.</span></p>\r\n<p><strong><span style=\"font-family: Times New Roman;\">Irresistible</span></strong><span style=\"font-family: Times New Roman;\">- cannot be stopped or prevented.</span></p>\r\n<p><strong><span style=\"font-family: Times New Roman;\">Irrational</span></strong><span style=\"font-family: Times New Roman;\">- illogical or unreasonable statement. </span></p>\r\n<p><strong><span style=\"font-family: Times New Roman;\">Irreparable</span></strong><span style=\"font-family: Times New Roman;\">- something which cannot be repaired, reconstructed.</span></p>",
                    solution_hi: "<p>2.d.<strong><span style=\"font-family: Times New Roman;\">Irrefutable</span></strong><span style=\"font-family: Times New Roman;\">- a statement which cannot be contradicted.</span></p>\r\n<p><strong><span style=\"font-family: Times New Roman;\">Irresistible</span></strong><span style=\"font-family: Times New Roman;\">- cannot be stopped or prevented.</span></p>\r\n<p><strong><span style=\"font-family: Times New Roman;\">Irrational</span></strong><span style=\"font-family: Times New Roman;\">- illogical or unreasonable statement. </span></p>\r\n<p><strong><span style=\"font-family: Times New Roman;\">Irreparable</span></strong><span style=\"font-family: Times New Roman;\">- something which cannot be repaired, reconstructed.</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "7",
                    question_en: "<p>3. A group of cattle</p>\r\n<p><span style=\"font-family: Times New Roman;\">SSC CGL Tier II, 18/11/2020</span></p>",
                    question_hi: "",
                    options_en: ["<p>Pride</p>", "<p>Herd</p>", 
                                "<p>Flock</p>", "<p>Litter</p>"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: "<p>3.b<span style=\"font-family: Times New Roman;\">.<strong>Herd</strong></span><span style=\"font-family: Times New Roman;\">- A group of cattle.</span></p>\r\n<p><strong><span style=\"font-family: Times New Roman;\">Pride</span></strong><span style=\"font-family: Times New Roman;\">- a group of lions.</span></p>\r\n<p><strong><span style=\"font-family: Times New Roman;\">Flock</span></strong><span style=\"font-family: Times New Roman;\">- a group of animals, birds or sheep.</span></p>\r\n<p><strong><span style=\"font-family: Times New Roman;\">Litter</span></strong><span style=\"font-family: Times New Roman;\">- pieces of paper, that are left in a public place.</span></p>",
                    solution_hi: "<p>3.b<span style=\"font-family: Times New Roman;\">.<strong>Herd</strong></span><span style=\"font-family: Times New Roman;\">- A group of cattle.</span></p>\r\n<p><strong><span style=\"font-family: Times New Roman;\">Pride</span></strong><span style=\"font-family: Times New Roman;\">- a group of lions.</span></p>\r\n<p><strong><span style=\"font-family: Times New Roman;\">Flock</span></strong><span style=\"font-family: Times New Roman;\">- a group of animals, birds or sheep.</span></p>\r\n<p><strong><span style=\"font-family: Times New Roman;\">Litter</span></strong><span style=\"font-family: Times New Roman;\">- pieces of paper, that are left in a public place.</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "7",
                    question_en: "<p>4. Obsessive desire to lose weight by refusing to eat</p>\r\n<p><span style=\"font-family: Times New Roman;\">SSC CGL Tier II, 18/11/2020</span></p>",
                    question_hi: "",
                    options_en: ["<p>Anorexia</p>", "<p>Dyslexia</p>", 
                                "<p>Asphyxia</p>", "<p>Pyrexia</p>"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: "<p>4.a.<strong><span style=\"font-family: Times New Roman;\">Anorexia</span></strong><span style=\"font-family: Times New Roman;\">- Obsessive desire to lose weight by refusing to eat.</span></p>\r\n<p><strong><span style=\"font-family: Times New Roman;\">Dyslexia</span></strong><span style=\"font-family: Times New Roman;\">- </span><span style=\"font-family: Times New Roman;\">a learning disorder that involves difficulty reading due to problems identifying speech sounds and learning how they relate to letters and words.</span></p>\r\n<p><strong><span style=\"font-family: Times New Roman;\">Pyrexia</span></strong><span style=\"font-family: Times New Roman;\">- r</span><span style=\"font-family: Times New Roman;\">aised body temperature, type of fever.</span></p>\r\n<p><strong><span style=\"font-family: Times New Roman;\">Asphyxia</span></strong><span style=\"font-family: Times New Roman;\">- </span><span style=\"font-family: Times New Roman;\">the state of being unable to breathe, which causes somebody to die or to become unconscious</span></p>",
                    solution_hi: "<p>4.a.<strong><span style=\"font-family: Times New Roman;\">Anorexia</span></strong><span style=\"font-family: Times New Roman;\">- Obsessive desire to lose weight by refusing to eat.</span></p>\r\n<p><strong><span style=\"font-family: Times New Roman;\">Dyslexia</span></strong><span style=\"font-family: Times New Roman;\">- </span><span style=\"font-family: Times New Roman;\">a learning disorder that involves difficulty reading due to problems identifying speech sounds and learning how they relate to letters and words.</span></p>\r\n<p><strong><span style=\"font-family: Times New Roman;\">Pyrexia</span></strong><span style=\"font-family: Times New Roman;\">- r</span><span style=\"font-family: Times New Roman;\">aised body temperature, type of fever.</span></p>\r\n<p><strong><span style=\"font-family: Times New Roman;\">Asphyxia</span></strong><span style=\"font-family: Times New Roman;\">- </span><span style=\"font-family: Times New Roman;\">the state of being unable to breathe, which causes somebody to die or to become unconscious</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "7",
                    question_en: "<p>5. Not supporting any side in an argument</p>\r\n<p><span style=\"font-family: Times New Roman;\">SSC CGL Tier II, 18/11/2020</span></p>",
                    question_hi: "",
                    options_en: ["<p>Impartial</p>", "<p>Involved</p>", 
                                "<p>Natural</p>", "<p>Biased</p>"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: "<p>5.a.<strong><span style=\"font-family: Times New Roman;\">Impartial</span></strong><span style=\"font-family: Times New Roman;\">- Not supporting any side in an argument.</span></p>\r\n<p><strong><span style=\"font-family: Times New Roman;\">Biased</span></strong><span style=\"font-family: Times New Roman;\">- </span><span style=\"font-family: Times New Roman;\">having a preference for one thing over another.</span></p>",
                    solution_hi: "<p>5.a.<strong><span style=\"font-family: Times New Roman;\">Impartial</span></strong><span style=\"font-family: Times New Roman;\">- Not supporting any side in an argument.</span></p>\r\n<p><strong><span style=\"font-family: Times New Roman;\">Biased</span></strong><span style=\"font-family: Times New Roman;\">- </span><span style=\"font-family: Times New Roman;\">having a preference for one thing over another.</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "7",
                    question_en: "<p>6. Act of giving up the throne</p>\r\n<p><span style=\"font-family: Times New Roman;\">SSC CGL Tier II, 18/11/2020 </span></p>",
                    question_hi: "",
                    options_en: ["<p>Abdication</p>", "<p>Admiration</p>", 
                                "<p>Adulation</p>", "<p>Addiction</p>"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: "<p>6.a.<strong><span style=\"font-family: Times New Roman;\">Abdication</span></strong><span style=\"font-family: Times New Roman;\">- Act of giving up the throne.</span></p>\r\n<p><strong><span style=\"font-family: Times New Roman;\">Admiration</span></strong><span style=\"font-family: Times New Roman;\">- </span><span style=\"font-family: Times New Roman;\">a feeling of liking and respecting somebody/something very much.</span></p>\r\n<p><strong><span style=\"font-family: Times New Roman;\">Adulation</span></strong><span style=\"font-family: Times New Roman;\">- </span><span style=\"font-family: Times New Roman;\">extreme admiration</span></p>\r\n<p><strong><span style=\"font-family: Times New Roman;\">Addiction</span></strong><span style=\"font-family: Times New Roman;\">- </span><span style=\"font-family: Times New Roman;\">a condition in which a person finds themselves unable to stop using a substance or engaging in a behavior.</span></p>",
                    solution_hi: "<p>6.a.<strong><span style=\"font-family: Times New Roman;\">Abdication</span></strong><span style=\"font-family: Times New Roman;\">- Act of giving up the throne.</span></p>\r\n<p><strong><span style=\"font-family: Times New Roman;\">Admiration</span></strong><span style=\"font-family: Times New Roman;\">- </span><span style=\"font-family: Times New Roman;\">a feeling of liking and respecting somebody/something very much.</span></p>\r\n<p><strong><span style=\"font-family: Times New Roman;\">Adulation</span></strong><span style=\"font-family: Times New Roman;\">- </span><span style=\"font-family: Times New Roman;\">extreme admiration</span></p>\r\n<p><strong><span style=\"font-family: Times New Roman;\">Addiction</span></strong><span style=\"font-family: Times New Roman;\">- </span><span style=\"font-family: Times New Roman;\">a condition in which a person finds themselves unable to stop using a substance or engaging in a behavior.</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "7",
                    question_en: "<p>7. A group of people travelling together, especially in a desert</p>\r\n<p><span style=\"font-family: Times New Roman;\">SSC CGL Tier II, 18/11/2020</span></p>",
                    question_hi: "",
                    options_en: ["<p>Pilgrimage</p>", "<p>Parade</p>", 
                                "<p>Caravan <span style=\"font-family: Times New Roman;\"> </span></p>", "<p>Procession</p>"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: "<p>7.c.<strong><span style=\"font-family: Times New Roman;\">Caravan</span></strong><span style=\"font-family: Times New Roman;\">- a group of people travelling together, especially in a desert.</span></p>\r\n<p><strong><span style=\"font-family: Times New Roman;\">Pilgrimage</span></strong><span style=\"font-family: Times New Roman;\">- a long journey that a person makes to visit a religious place.</span></p>\r\n<p><strong><span style=\"font-family: Times New Roman;\">Parade</span></strong><span style=\"font-family: Times New Roman;\">- it is a procession of people or vehicles moving through a public place in order to celebrate an important day or event.</span></p>\r\n<p><strong><span style=\"font-family: Times New Roman;\">Procession</span></strong><span style=\"font-family: Times New Roman;\">- a number of people </span><span style=\"font-family: Times New Roman;\">vehicles, etc. that move slowly in a line, especially as part of a ceremony</span></p>",
                    solution_hi: "<p>7.c.<strong><span style=\"font-family: Times New Roman;\">Caravan</span></strong><span style=\"font-family: Times New Roman;\">- a group of people travelling together, especially in a desert.</span></p>\r\n<p><strong><span style=\"font-family: Times New Roman;\">Pilgrimage</span></strong><span style=\"font-family: Times New Roman;\">- a long journey that a person makes to visit a religious place.</span></p>\r\n<p><strong><span style=\"font-family: Times New Roman;\">Parade</span></strong><span style=\"font-family: Times New Roman;\">- it is a procession of people or vehicles moving through a public place in order to celebrate an important day or event.</span></p>\r\n<p><strong><span style=\"font-family: Times New Roman;\">Procession</span></strong><span style=\"font-family: Times New Roman;\">- a number of people </span><span style=\"font-family: Times New Roman;\">vehicles, etc. that move slowly in a line, especially as part of a ceremony</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "7",
                    question_en: "<p>8. A fast moving stream of water</p>\r\n<p><span style=\"font-family: Times New Roman;\">SSC CGL Tier II, 18/11/2020</span></p>",
                    question_hi: "",
                    options_en: ["<p>Fissure</p>", "<p>Volcano</p>", 
                                "<p>Quake</p>", "<p>Torrent</p>"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: "<p>8.d.<strong><span style=\"font-family: Times New Roman;\">Torrent</span></strong><span style=\"font-family: Times New Roman;\">-a fast moving stream of water.</span></p>\r\n<p><strong><span style=\"font-family: Times New Roman;\">Fissure</span></strong><span style=\"font-family: Times New Roman;\">-</span><span style=\"font-family: Times New Roman;\"> a long deep crack in something, especially in rock or in the earth</span></p>\r\n<p><strong><span style=\"font-family: Times New Roman;\">Volcano</span></strong><span style=\"font-family: Times New Roman;\">- </span><span style=\"font-family: Times New Roman;\">a mountain with a hole (crater) at the top through which steam, hot melted rock (lava), fire, etc. sometimes come out</span></p>\r\n<p><strong><span style=\"font-family: Times New Roman;\">Quake</span></strong><span style=\"font-family: Times New Roman;\">- </span><span style=\"font-family: Times New Roman;\">(used about a person) to shake</span></p>",
                    solution_hi: "<p>8.d.<strong><span style=\"font-family: Times New Roman;\">Torrent</span></strong><span style=\"font-family: Times New Roman;\">-a fast moving stream of water.</span></p>\r\n<p><strong><span style=\"font-family: Times New Roman;\">Fissure</span></strong><span style=\"font-family: Times New Roman;\">-</span><span style=\"font-family: Times New Roman;\"> a long deep crack in something, especially in rock or in the earth</span></p>\r\n<p><strong><span style=\"font-family: Times New Roman;\">Volcano</span></strong><span style=\"font-family: Times New Roman;\">- </span><span style=\"font-family: Times New Roman;\">a mountain with a hole (crater) at the top through which steam, hot melted rock (lava), fire, etc. sometimes come out</span></p>\r\n<p><strong><span style=\"font-family: Times New Roman;\">Quake</span></strong><span style=\"font-family: Times New Roman;\">- </span><span style=\"font-family: Times New Roman;\">(used about a person) to shake</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "7",
                    question_en: " <p>9. A detailed list of things in a place </span></p> <p><span style=\"font-family:Times New Roman\">SSC CGL Tier II, 18/11/2020</span></p>",
                    question_hi: "",
                    options_en: [" <p> Glossary </span></p>", " <p> Inventory </span></p>", 
                                " <p> Itinerary </span></p>", " <p> Directory </span></p>"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: " <p>9.b.</span><span style=\"font-family:Times New Roman\">Inventory</span><span style=\"font-family:Times New Roman\">-</span><span style=\"font-family:Times New Roman\"> </span><span style=\"font-family:Times New Roman\">A detailed list of things in a place.</span></p> <p><span style=\"font-family:Times New Roman\">Glossary</span><span style=\"font-family:Times New Roman\">- </span><span style=\"font-family:Times New Roman\">a list of special or unusual words and their meanings, usually at the end of a text or book.</span></p> <p><span style=\"font-family:Times New Roman\">Itinerary</span><span style=\"font-family:Times New Roman\">- </span><span style=\"font-family:Times New Roman\">a plan of a journey, including the route and the places that you will visit.</span></p> <p><span style=\"font-family:Times New Roman\">Directory</span><span style=\"font-family:Times New Roman\">- a list of names, addresses and telephone numbers in the order of the alphabet.</span></p>",
                    solution_hi: " <p>9.b.</span><span style=\"font-family:Times New Roman\">Inventory</span><span style=\"font-family:Times New Roman\">-</span><span style=\"font-family:Times New Roman\"> </span><span style=\"font-family:Times New Roman\">A detailed list of things in a place.</span></p> <p><span style=\"font-family:Times New Roman\">Glossary</span><span style=\"font-family:Times New Roman\">- </span><span style=\"font-family:Times New Roman\">a list of special or unusual words and their meanings, usually at the end of a text or book.</span></p> <p><span style=\"font-family:Times New Roman\">Itinerary</span><span style=\"font-family:Times New Roman\">- </span><span style=\"font-family:Times New Roman\">a plan of a journey, including the route and the places that you will visit.</span></p> <p><span style=\"font-family:Times New Roman\">Directory</span><span style=\"font-family:Times New Roman\">- a list of names, addresses and telephone numbers in the order of the alphabet.</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "7",
                    question_en: "<p>10. A glass container in which fish can be kept</p>\r\n<p><span style=\"font-family: Times New Roman;\">SSC CGL Tier II, 18/11/2020</span></p>",
                    question_hi: "",
                    options_en: ["<p>Aquatic</p>", "<p>Aqua</p>", 
                                "<p>Fishery</p>", "<p>Aquarium</p>"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: "<p>10.d.<strong><span style=\"font-family: Times New Roman;\">Aquarium</span></strong><span style=\"font-family: Times New Roman;\">- A glass container in which fish can be kept.</span></p>\r\n<p><strong><span style=\"font-family: Times New Roman;\">Aquatic</span></strong><span style=\"font-family: Times New Roman;\">- </span><span style=\"font-family: Times New Roman;\">living or taking place in, on or near water</span></p>\r\n<p><strong><span style=\"font-family: Times New Roman;\">Fishery</span></strong><span style=\"font-family: Times New Roman;\">- </span><span style=\"font-family: Times New Roman;\">a place where fish are reared for commercial purposes.</span></p>",
                    solution_hi: "<p>10.d.<strong><span style=\"font-family: Times New Roman;\">Aquarium</span></strong><span style=\"font-family: Times New Roman;\">- A glass container in which fish can be kept.</span></p>\r\n<p><strong><span style=\"font-family: Times New Roman;\">Aquatic</span></strong><span style=\"font-family: Times New Roman;\">- </span><span style=\"font-family: Times New Roman;\">living or taking place in, on or near water</span></p>\r\n<p><strong><span style=\"font-family: Times New Roman;\">Fishery</span></strong><span style=\"font-family: Times New Roman;\">- </span><span style=\"font-family: Times New Roman;\">a place where fish are reared for commercial purposes.</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "7",
                    question_en: "<p>11. Causing great damage or suffering.</p>\r\n<p><span style=\"font-family: Times New Roman;\">SSC CGL Tier II, 11/9/2019</span></p>",
                    question_hi: "",
                    options_en: ["<p>Catastrophic</p>", "<p>Spasmodic</p>", 
                                "<p>Catatonic</p>", "<p>Chasm</p>"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: "<p>11.<span style=\"font-family: Times New Roman;\">a. </span><strong><span style=\"font-family: Times New Roman;\">Catastrophic</span></strong><span style=\"font-family: Times New Roman;\">-involving or causing sudden great damage or suffering.</span></p>\r\n<p><strong><span style=\"font-family: Times New Roman;\">Spasmodic</span></strong><span style=\"font-family: Times New Roman;\"><strong> </strong>-Occurring or done in brief, irregular bursts.</span></p>\r\n<p><strong><span style=\"font-family: Times New Roman;\">Catatonic</span></strong><span style=\"font-family: Times New Roman;\">-relating to or characterized by catatonia (schizophrenia)</span></p>\r\n<p><strong><span style=\"font-family: Times New Roman;\">Chasm</span></strong><span style=\"font-family: Times New Roman;\">-a deep fissure in the earth\'s surface.</span></p>",
                    solution_hi: "<p>11.<span style=\"font-family: Times New Roman;\">a. </span><strong><span style=\"font-family: Times New Roman;\">Catastrophic</span></strong><span style=\"font-family: Times New Roman;\">-involving or causing sudden great damage or suffering.</span></p>\r\n<p><strong><span style=\"font-family: Times New Roman;\">Spasmodic</span></strong><span style=\"font-family: Times New Roman;\"><strong> </strong>-Occurring or done in brief, irregular bursts.</span></p>\r\n<p><strong><span style=\"font-family: Times New Roman;\">Catatonic</span></strong><span style=\"font-family: Times New Roman;\">-relating to or characterized by catatonia (schizophrenia)</span></p>\r\n<p><strong><span style=\"font-family: Times New Roman;\">Chasm</span></strong><span style=\"font-family: Times New Roman;\">-a deep fissure in the earth\'s surface.</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "7",
                    question_en: "<p>12. A strong feeling of dislike towards someone or something.</p>\r\n<p><span style=\"font-family: Times New Roman;\">SSC CGL Tier II, 11/9/2019</span></p>",
                    question_hi: "",
                    options_en: ["<p>sympathy</p>", "<p>telepathy</p>", 
                                "<p>antipathy</p>", "<p>empathy</p>"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: "<p>12.<span style=\"font-family: Times New Roman;\">c.</span><strong><span style=\"font-family: Times New Roman;\">antipathy</span></strong><span style=\"font-family: Times New Roman;\">- a deep-seated feeling of aversion.</span></p>\r\n<p><strong><span style=\"font-family: Times New Roman;\">sympathy</span></strong><span style=\"font-family: Times New Roman;\">-feelings of pity and sorrow for someone else\'s misfortune.</span></p>\r\n<p><strong><span style=\"font-family: Times New Roman;\">telepathy</span></strong><span style=\"font-family: Times New Roman;\">-he supposed communication of thoughts or ideas by means other than the known senses.</span></p>\r\n<p><strong><span style=\"font-family: Times New Roman;\">empathy</span></strong><span style=\"font-family: Times New Roman;\">-the ability to understand and share the feelings of another.</span></p>",
                    solution_hi: "<p>12.<span style=\"font-family: Times New Roman;\">c.</span><strong><span style=\"font-family: Times New Roman;\">antipathy</span></strong><span style=\"font-family: Times New Roman;\">- a deep-seated feeling of aversion.</span></p>\r\n<p><strong><span style=\"font-family: Times New Roman;\">sympathy</span></strong><span style=\"font-family: Times New Roman;\">-feelings of pity and sorrow for someone else\'s misfortune.</span></p>\r\n<p><strong><span style=\"font-family: Times New Roman;\">telepathy</span></strong><span style=\"font-family: Times New Roman;\">-he supposed communication of thoughts or ideas by means other than the known senses.</span></p>\r\n<p><strong><span style=\"font-family: Times New Roman;\">empathy</span></strong><span style=\"font-family: Times New Roman;\">-the ability to understand and share the feelings of another.</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "7",
                    question_en: "<p>13. To express in an unclear way.</p>\r\n<p><span style=\"font-family: Times New Roman;\">SSC CGL Tier II, 11/9/2019</span></p>",
                    question_hi: "",
                    options_en: ["<p>eloquent</p>", "<p>garbled</p>", 
                                "<p>lucid</p>", "<p>Intelligible</p>"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: "<p>13.<span style=\"font-family: Times New Roman;\">b.</span><strong><span style=\"font-family: Times New Roman;\">garbled</span></strong><span style=\"font-family: Times New Roman;\">-</span><span style=\"font-family: Times New Roman;\"> confused and distorted; unclear.</span></p>\r\n<p><span style=\"font-family: Times New Roman;\"><strong>eloquent</strong>-fluent or persuasive in speaking or writing.</span></p>\r\n<p><span style=\"font-family: Times New Roman;\"><strong>lucid</strong>-expressed clearly; easy to understand</span></p>\r\n<p><span style=\"font-family: Times New Roman;\"><strong>Intelligible</strong>-able to be understood; comprehensible.</span></p>",
                    solution_hi: "<p>13.<span style=\"font-family: Times New Roman;\">b.</span><strong><span style=\"font-family: Times New Roman;\">garbled</span></strong><span style=\"font-family: Times New Roman;\">-</span><span style=\"font-family: Times New Roman;\"> confused and distorted; unclear.</span></p>\r\n<p><span style=\"font-family: Times New Roman;\"><strong>eloquent</strong>-fluent or persuasive in speaking or writing.</span></p>\r\n<p><span style=\"font-family: Times New Roman;\"><strong>lucid</strong>-expressed clearly; easy to understand</span></p>\r\n<p><span style=\"font-family: Times New Roman;\"><strong>Intelligible</strong>-able to be understood; comprehensible.</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "7",
                    question_en: "<p>14. Something happening by chance in a happy and beneficial way</p>\r\n<p><span style=\"font-family: Times New Roman;\">SSC CGL Tier II, 11/9/2019</span></p>",
                    question_hi: "",
                    options_en: ["<p>serenity</p>", "<p>serendipity</p>", 
                                "<p>misadventure</p>", "<p>fortitude</p>"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: "<p>14.<span style=\"font-family: Times New Roman;\">b.<strong>serendipity</strong></span></p>\r\n<p><span style=\"font-family: Times New Roman;\"><strong>serenity</strong>-the state of being calm, peaceful, and untroubled.</span></p>\r\n<p><span style=\"font-family: Times New Roman;\"><strong>misadventure</strong>- death caused by a person accidentally while performing a legal act without negligence or intent to harm.</span></p>\r\n<p><span style=\"font-family: Times New Roman;\"><strong>fortitude</strong>- courage in pain or adversity.</span></p>",
                    solution_hi: "<p>14.<span style=\"font-family: Times New Roman;\">b.<strong>serendipity</strong></span></p>\r\n<p><span style=\"font-family: Times New Roman;\"><strong>serenity</strong>-the state of being calm, peaceful, and untroubled.</span></p>\r\n<p><span style=\"font-family: Times New Roman;\"><strong>misadventure</strong>- death caused by a person accidentally while performing a legal act without negligence or intent to harm.</span></p>\r\n<p><span style=\"font-family: Times New Roman;\"><strong>fortitude</strong>- courage in pain or adversity.</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "7",
                    question_en: "<p>15. Lacking in variety and interest.</p>\r\n<p><span style=\"font-family: Times New Roman;\">SSC CGL Tier II, 11/9/2019</span></p>",
                    question_hi: "",
                    options_en: ["<p>monotonous</p>", "<p>fresh</p>", 
                                "<p>exclamatory</p>", "<p>vibrant</p>"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: "<p>15.<span style=\"font-family: Times New Roman;\">a. <strong>monotonous</strong>-dull, tedious, and repetitious; lacking in variety and interest.</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">b.<strong>fresh</strong>- </span><span style=\"font-family: Times New Roman;\">not previously known or used; new or different.</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">c.<strong>exclamation</strong>-expressing</span><span style=\"font-family: Times New Roman;\"> surprise, strong emotion, or pain.</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">d. <strong>vibrant</strong>-full of energy and life.</span></p>",
                    solution_hi: "<p>15.<span style=\"font-family: Times New Roman;\">a. <strong>monotonous</strong>-dull, tedious, and repetitious; lacking in variety and interest.</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">b.<strong>fresh</strong>- </span><span style=\"font-family: Times New Roman;\">not previously known or used; new or different.</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">c.<strong>exclamation</strong>-expressing</span><span style=\"font-family: Times New Roman;\"> surprise, strong emotion, or pain.</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">d. <strong>vibrant</strong>-full of energy and life.</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "7",
                    question_en: "<p>16.Something causing shock or dismay.</p>\r\n<p><span style=\"font-family: Times New Roman;\">SSC CGL Tier II, 11/9/2019</span></p>",
                    question_hi: "",
                    options_en: ["<p>frivolous</p>", "<p>appalling</p>", 
                                "<p>mischievous</p>", "<p>remarkable</p>"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: "<p>16.<span style=\"font-family: Times New Roman;\">b.<strong>appalling</strong>-causing shock or dismay; horrific.</span></p>\r\n<p><span style=\"font-family: Times New Roman;\"><strong>frivolous</strong>-not having any serious purpose or value.</span></p>\r\n<p><span style=\"font-family: Times New Roman;\"><strong>mischievous</strong>-causing or showing a fondness for causing trouble in a playful way.</span></p>\r\n<p><span style=\"font-family: Times New Roman;\"><strong>remarkable</strong>- </span><span style=\"font-family: Times New Roman;\">worthy of attention; striking.</span></p>",
                    solution_hi: "<p>16.<span style=\"font-family: Times New Roman;\">b.<strong>appalling</strong>-causing shock or dismay; horrific.</span></p>\r\n<p><span style=\"font-family: Times New Roman;\"><strong>frivolous</strong>-not having any serious purpose or value.</span></p>\r\n<p><span style=\"font-family: Times New Roman;\"><strong>mischievous</strong>-causing or showing a fondness for causing trouble in a playful way.</span></p>\r\n<p><span style=\"font-family: Times New Roman;\"><strong>remarkable</strong>- </span><span style=\"font-family: Times New Roman;\">worthy of attention; striking.</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "7",
                    question_en: "<p>17.Continuing for a very long time.</p>\r\n<p><span style=\"font-family: Times New Roman;\"> SSC CGL Tier II, 11/9/2019</span></p>",
                    question_hi: "",
                    options_en: ["<p>laconic</p>", "<p>interim</p>", 
                                "<p>concise</p>", "<p>interminable</p>"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: "<p>17.<span style=\"font-family: Times New Roman;\">d. <strong>interminable</strong>-endless or apparently endless (often used hyperbolically).</span></p>\r\n<p><span style=\"font-family: Times New Roman;\"><strong>laconic</strong>-</span><span style=\"font-family: Times New Roman;\"> using very few words.</span></p>\r\n<p><span style=\"font-family: Times New Roman;\"><strong>interim</strong>-the intervening time.</span></p>\r\n<p><span style=\"font-family: Times New Roman;\"><strong>concise</strong>- </span><span style=\"font-family: Times New Roman;\">brief but comprehensive.</span></p>",
                    solution_hi: "<p>17.<span style=\"font-family: Times New Roman;\">d. <strong>interminable</strong>-endless or apparently endless (often used hyperbolically).</span></p>\r\n<p><span style=\"font-family: Times New Roman;\"><strong>laconic</strong>-</span><span style=\"font-family: Times New Roman;\"> using very few words.</span></p>\r\n<p><span style=\"font-family: Times New Roman;\"><strong>interim</strong>-the intervening time.</span></p>\r\n<p><span style=\"font-family: Times New Roman;\"><strong>concise</strong>- </span><span style=\"font-family: Times New Roman;\">brief but comprehensive.</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "7",
                    question_en: "<p>18.Impossible to satisfy.</p>\r\n<p><span style=\"font-family: Times New Roman;\">SSC CGL Tier II, 11/9/2019</span></p>",
                    question_hi: "",
                    options_en: ["<p>palatable</p>", "<p>insatiable</p>", 
                                "<p>insane</p>", "<p>magnanimous</p>"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: "<p>18.<span style=\"font-family: Times New Roman;\">b. <strong>insatiable</strong>-</span><span style=\"font-family: Times New Roman;\"> impossible to satisfy.</span></p>\r\n<p><strong><span style=\"font-family: Times New Roman;\">palatable</span></strong><span style=\"font-family: Times New Roman;\"><strong> </strong>pleasant to taste.</span></p>\r\n<p><span style=\"font-family: Times New Roman;\"><strong>insane</strong>- </span><span style=\"font-family: Times New Roman;\">a state of mind which prevents normal perception, behaviour, or social interaction</span></p>\r\n<p><span style=\"font-family: Times New Roman;\"><strong>magnanimous</strong>-generous or forgiving, especially towards a rival or less powerful person.</span></p>",
                    solution_hi: "<p>18.<span style=\"font-family: Times New Roman;\">b. <strong>insatiable</strong>-</span><span style=\"font-family: Times New Roman;\"> impossible to satisfy.</span></p>\r\n<p><strong><span style=\"font-family: Times New Roman;\">palatable</span></strong><span style=\"font-family: Times New Roman;\"><strong> </strong>pleasant to taste.</span></p>\r\n<p><span style=\"font-family: Times New Roman;\"><strong>insane</strong>- </span><span style=\"font-family: Times New Roman;\">a state of mind which prevents normal perception, behaviour, or social interaction</span></p>\r\n<p><span style=\"font-family: Times New Roman;\"><strong>magnanimous</strong>-generous or forgiving, especially towards a rival or less powerful person.</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "7",
                    question_en: "<p>19.Splendid and expensive-looking.</p>\r\n<p><span style=\"font-family: Times New Roman;\">SSC CGL Tier II, 11/9/2019</span></p>",
                    question_hi: "",
                    options_en: ["<p>sumptuous</p>", "<p>peculiar</p>", 
                                "<p>curious</p>", "<p>malicious</p>"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: "<p>19.<span style=\"font-family: Times New Roman;\">a. <strong>sumptuous</strong>- </span><span style=\"font-family: Times New Roman;\">splendid and expensive-looking.</span></p>\r\n<p><strong><span style=\"font-family: Times New Roman;\">peculiar </span></strong><span style=\"font-family: Times New Roman;\">-</span><span style=\"font-family: Times New Roman;\"> different to what is normal or expected; strange.</span></p>\r\n<p><strong><span style=\"font-family: Times New Roman;\">curious</span></strong><span style=\"font-family: Times New Roman;\">-</span><span style=\"font-family: Times New Roman;\">eager to know or learn something.</span></p>\r\n<p><strong><span style=\"font-family: Times New Roman;\">malicious </span></strong><span style=\"font-family: Times New Roman;\">-</span><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Times New Roman;\"> intended to do harm.</span></p>",
                    solution_hi: "<p>19.<span style=\"font-family: Times New Roman;\">a. <strong>sumptuous</strong>- </span><span style=\"font-family: Times New Roman;\">splendid and expensive-looking.</span></p>\r\n<p><strong><span style=\"font-family: Times New Roman;\">peculiar </span></strong><span style=\"font-family: Times New Roman;\">-</span><span style=\"font-family: Times New Roman;\"> different to what is normal or expected; strange.</span></p>\r\n<p><strong><span style=\"font-family: Times New Roman;\">curious</span></strong><span style=\"font-family: Times New Roman;\">-</span><span style=\"font-family: Times New Roman;\">eager to know or learn something.</span></p>\r\n<p><strong><span style=\"font-family: Times New Roman;\">malicious </span></strong><span style=\"font-family: Times New Roman;\">-</span><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Times New Roman;\"> intended to do harm.</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "7",
                    question_en: "<p>20.Seeming reasonable.</p>\r\n<p><span style=\"font-family: Times New Roman;\">SSC CGL Tier II, 11/9/2019</span></p>",
                    question_hi: "",
                    options_en: ["<p>plausible</p>", "<p>permeable</p>", 
                                "<p>versatile</p>", "<p>volatile</p>"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: "<p>20.<span style=\"font-family: Times New Roman;\">a.<strong>plausible</strong></span><span style=\"font-family: Times New Roman;\"><strong> </strong>seeming reasonable or probable.</span></p>\r\n<p><strong><span style=\"font-family: Times New Roman;\">permeable</span></strong><span style=\"font-family: Times New Roman;\">- </span><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Times New Roman;\">allowing liquids or gases to pass through it.</span></p>\r\n<p><strong><span style=\"font-family: Times New Roman;\">versatile</span></strong><span style=\"font-family: Times New Roman;\">-</span><span style=\"font-family: Times New Roman;\">able to adapt or be adapted to many different functions or activities.</span></p>\r\n<p><strong><span style=\"font-family: Times New Roman;\">volatile </span></strong><span style=\"font-family: Times New Roman;\">- easily evaporated at normal temperatures.</span></p>",
                    solution_hi: "<p>20.<span style=\"font-family: Times New Roman;\">a.<strong>plausible</strong></span><span style=\"font-family: Times New Roman;\"><strong> </strong>seeming reasonable or probable.</span></p>\r\n<p><strong><span style=\"font-family: Times New Roman;\">permeable</span></strong><span style=\"font-family: Times New Roman;\">- </span><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Times New Roman;\">allowing liquids or gases to pass through it.</span></p>\r\n<p><strong><span style=\"font-family: Times New Roman;\">versatile</span></strong><span style=\"font-family: Times New Roman;\">-</span><span style=\"font-family: Times New Roman;\">able to adapt or be adapted to many different functions or activities.</span></p>\r\n<p><strong><span style=\"font-family: Times New Roman;\">volatile </span></strong><span style=\"font-family: Times New Roman;\">- easily evaporated at normal temperatures.</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "7",
                    question_en: "<p>21.The fear of water.</p>\r\n<p><span style=\"font-family: Times New Roman;\">SSC CGL Tier II, 11/9/2019</span></p>",
                    question_hi: "",
                    options_en: ["<p>claustrophobia</p>", "<p>autophobia</p>", 
                                "<p>hydrophobia</p>", "<p>pyrophobia</p>"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: "<p>21.<span style=\"font-family: Times New Roman;\">c.<strong>hydrophobia</strong>-Fear of Water </span></p>\r\n<p><span style=\"font-family: Times New Roman;\"><strong>claustrophobia</strong>- extreme or irrational fear of confined places.</span></p>\r\n<p><strong><span style=\"font-family: Times New Roman;\">autophobia </span></strong><span style=\"font-family: Times New Roman;\"> is the specific phobia of isolation</span></p>\r\n<p><span style=\"font-family: Times New Roman;\"><strong>pyrophobia</strong>- </span><span style=\"font-family: Times New Roman;\">is a fear of fire</span></p>",
                    solution_hi: "<p>21.<span style=\"font-family: Times New Roman;\">c.<strong>hydrophobia</strong>-Fear of Water </span></p>\r\n<p><span style=\"font-family: Times New Roman;\"><strong>claustrophobia</strong>- extreme or irrational fear of confined places.</span></p>\r\n<p><strong><span style=\"font-family: Times New Roman;\">autophobia </span></strong><span style=\"font-family: Times New Roman;\"> is the specific phobia of isolation</span></p>\r\n<p><span style=\"font-family: Times New Roman;\"><strong>pyrophobia</strong>- </span><span style=\"font-family: Times New Roman;\">is a fear of fire</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "7",
                    question_en: "<p>22. A distinguished conductor or performer of classical music</p>\r\n<p><span style=\"font-family: Times New Roman;\">SSC CGL Tier II, 11/9/2019</span></p>",
                    question_hi: "",
                    options_en: ["<p>maestro</p>", "<p>layman</p>", 
                                "<p>amateur</p>", "<p>novice</p>"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: "<p>22.<span style=\"font-family: Times New Roman;\">a. <strong>maestro </strong></span><span style=\"font-family: Times New Roman;\"> - a distinguished conductor or performer of classical music.</span></p>\r\n<p><span style=\"font-family: Times New Roman;\"><strong>layman</strong>-a person without professional or specialized knowledge in a particular subject.</span></p>\r\n<p><strong><span style=\"font-family: Times New Roman;\">amateur</span></strong><span style=\"font-family: Times New Roman;\">- a person who engages in a pursuit, especially a sport, on an unpaid rather than a professional basis.</span></p>\r\n<p><span style=\"font-family: Times New Roman;\"><strong>novice </strong>- </span><span style=\"font-family: Times New Roman;\">a person new to and inexperienced in a job or situation.</span></p>",
                    solution_hi: "<p>22.<span style=\"font-family: Times New Roman;\">a. <strong>maestro </strong></span><span style=\"font-family: Times New Roman;\"> - a distinguished conductor or performer of classical music.</span></p>\r\n<p><span style=\"font-family: Times New Roman;\"><strong>layman</strong>-a person without professional or specialized knowledge in a particular subject.</span></p>\r\n<p><strong><span style=\"font-family: Times New Roman;\">amateur</span></strong><span style=\"font-family: Times New Roman;\">- a person who engages in a pursuit, especially a sport, on an unpaid rather than a professional basis.</span></p>\r\n<p><span style=\"font-family: Times New Roman;\"><strong>novice </strong>- </span><span style=\"font-family: Times New Roman;\">a person new to and inexperienced in a job or situation.</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "23",
                    section: "7",
                    question_en: "<p>23.A dome-shaped shelter built from blocks of solid snow used by Eskimos</p>\r\n<p><span style=\"font-family: Times New Roman;\">SSC CGL Tier II, 12/9/2019</span></p>",
                    question_hi: "",
                    options_en: ["<p>wigwam</p>", "<p>yurt</p>", 
                                "<p>tepee</p>", "<p>igloo</p>"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: "<p>23. <span style=\"font-family: Times New Roman;\">d. <strong>igloo</strong></span><span style=\"font-family: Times New Roman;\"><strong> </strong>- a type of dome-shaped shelter built from blocks of solid snow</span></p>\r\n<p><span style=\"font-family: Times New Roman;\"><strong>wigwam</strong>- a pyramidal framework of poles used to support runner beans, sweet peas, and other climbing plants.</span></p>\r\n<p><span style=\"font-family: Times New Roman;\"><strong>yurt</strong>- circular tent of felt or skins used by nomads </span></p>\r\n<p><span style=\"font-family: Times New Roman;\"><strong>tepee</strong>-</span><span style=\"font-family: Times New Roman;\">a portable conical tent made of canvas on a frame of poles, used by North American Indians</span></p>",
                    solution_hi: "<p>23. <span style=\"font-family: Times New Roman;\">d. <strong>igloo</strong></span><span style=\"font-family: Times New Roman;\"><strong> </strong>- a type of dome-shaped shelter built from blocks of solid snow</span></p>\r\n<p><span style=\"font-family: Times New Roman;\"><strong>wigwam</strong>- a pyramidal framework of poles used to support runner beans, sweet peas, and other climbing plants.</span></p>\r\n<p><span style=\"font-family: Times New Roman;\"><strong>yurt</strong>- circular tent of felt or skins used by nomads </span></p>\r\n<p><span style=\"font-family: Times New Roman;\"><strong>tepee</strong>-</span><span style=\"font-family: Times New Roman;\">a portable conical tent made of canvas on a frame of poles, used by North American Indians</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "24",
                    section: "7",
                    question_en: "<p>24.The Science which studies the crust of the earth. SSC CGL Tier II, 12/9/2019</p>",
                    question_hi: "",
                    options_en: ["<p>zoology</p>", "<p>etymology</p>", 
                                "<p>biology</p>", "<p>geology</p>"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: "<p>24. <span style=\"font-family: Times New Roman;\">d. <strong>geology</strong></span><span style=\"font-family: Times New Roman;\">-</span><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Times New Roman;\">the science which deals with the physical structure and substance of the earth</span></p>\r\n<p><strong><span style=\"font-family: Times New Roman;\">zoology</span></strong><span style=\"font-family: Times New Roman;\">-the animal life of a particular area or time.</span></p>\r\n<p><span style=\"font-family: Times New Roman;\"><strong>etymology</strong>- the origin of a word and the historical development of its meaning.</span></p>\r\n<p><strong><span style=\"font-family: Times New Roman;\">biology</span></strong><span style=\"font-family: Times New Roman;\"><strong> </strong>-the plants and animals of a particular area.</span></p>",
                    solution_hi: "<p>24. <span style=\"font-family: Times New Roman;\">d. <strong>geology</strong></span><span style=\"font-family: Times New Roman;\">-</span><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Times New Roman;\">the science which deals with the physical structure and substance of the earth</span></p>\r\n<p><strong><span style=\"font-family: Times New Roman;\">zoology</span></strong><span style=\"font-family: Times New Roman;\">-the animal life of a particular area or time.</span></p>\r\n<p><span style=\"font-family: Times New Roman;\"><strong>etymology</strong>- the origin of a word and the historical development of its meaning.</span></p>\r\n<p><strong><span style=\"font-family: Times New Roman;\">biology</span></strong><span style=\"font-family: Times New Roman;\"><strong> </strong>-the plants and animals of a particular area.</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "25",
                    section: "misc",
                    question_en: "<p>25. The cultivation of grapevines.</p>\r\n<p><span style=\"font-family: Times New Roman;\">SSC CGL Tier II, 12/9/2019</span></p>",
                    question_hi: "",
                    options_en: ["<p>agriculture</p>", "<p>horticulture</p>", 
                                "<p>viticulture</p>", "<p>sericulture</p>"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: "<p>25.<span style=\"font-family: Times New Roman;\">c.<strong>viticulture </strong>- </span><span style=\"font-family: Times New Roman;\">the cultivation of grapevines.</span></p>\r\n<p><span style=\"font-family: Times New Roman;\"><strong>agriculture </strong>- </span><span style=\"font-family: Times New Roman;\">the science or practice of farming, including cultivation of the soil for the growing of crops</span></p>\r\n<p><strong><span style=\"font-family: Times New Roman;\">horticulture</span></strong><span style=\"font-family: Times New Roman;\">-</span><span style=\"font-family: Times New Roman;\">the art or practice of garden cultivation and management.</span></p>\r\n<p><strong><span style=\"font-family: Times New Roman;\">sericulture</span></strong><span style=\"font-family: Times New Roman;\">-</span><span style=\"font-family: Times New Roman;\"> the production of silk and the rearing of silkworms for this purpose.</span></p>",
                    solution_hi: "<p>25.<span style=\"font-family: Times New Roman;\">c.<strong>viticulture </strong>- </span><span style=\"font-family: Times New Roman;\">the cultivation of grapevines.</span></p>\r\n<p><span style=\"font-family: Times New Roman;\"><strong>agriculture </strong>- </span><span style=\"font-family: Times New Roman;\">the science or practice of farming, including cultivation of the soil for the growing of crops</span></p>\r\n<p><strong><span style=\"font-family: Times New Roman;\">horticulture</span></strong><span style=\"font-family: Times New Roman;\">-</span><span style=\"font-family: Times New Roman;\">the art or practice of garden cultivation and management.</span></p>\r\n<p><strong><span style=\"font-family: Times New Roman;\">sericulture</span></strong><span style=\"font-family: Times New Roman;\">-</span><span style=\"font-family: Times New Roman;\"> the production of silk and the rearing of silkworms for this purpose.</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>