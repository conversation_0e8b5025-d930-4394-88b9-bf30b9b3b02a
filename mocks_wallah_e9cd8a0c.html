<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #7209b7;
            --secondary: #4361ee;
            --accent: #f72585;
            --success: #06d6a0;
            --warning: #ffd166;
            --danger: #ef476f;
            --light: #f8f9fa;
            --dark: #212529;
        }
        body {
            background-color: #f0f2f5;
            font-size: 16px;
            padding-bottom: 60px; /* Space for fixed navigation */
        }
        .navbar {
            background: linear-gradient(135deg, var(--primary), var(--secondary)) !important;
            box-shadow: 0 2px 10px rgba(0,0,0,.1);
            padding: 0.5rem 1rem;
        }
        .navbar-brand {
            font-size: 1.1rem;
            margin-right: 0;
        }
        .navbar .container-fluid {
            justify-content: space-between;
        }
        .controls-group {
            display: flex;
            align-items: center;
            flex-wrap: nowrap;
        }
        .timer-display {
            background: linear-gradient(135deg, #4361ee, #3a0ca3);
            color: white;
            border-radius: 50px;
            box-shadow: 0 3px 8px rgba(0,0,0,.1);
            padding: 0.25rem 0.75rem;
            font-size: 0.875rem;
            white-space: nowrap;
        }
        .nav-btn {
            padding: 0.25rem 0.75rem;
            font-size: 0.875rem;
            margin-left: 0.25rem;
        }
        .question-nav {
            position: fixed;
            top: 56px;
            right: 0;
            height: calc(100vh - 56px);
            width: 280px;
            transform: translateX(280px);
            transition: transform .3s;
            z-index: 100;
            background: #fff;
            box-shadow: -5px 0 15px rgba(0,0,0,.1);
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .main-content {
            transition: margin .3s;
            min-height: calc(100vh - 116px);
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.5;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(32px, 1fr));
            gap: 6px;
        }
        .q-box {
            width: 35px;
            height: 35px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 5px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid #dee2e6;
            background: #fff;
            transition: all .2s;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: 0 3px 5px rgba(0,0,0,.1);
        }
        .q-box.attempted {
            background-color: var(--secondary);
            color: white;
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,.15);
        }
        .card {
            border-radius: 12px;
            overflow: hidden;
            border: none;
            box-shadow: 0 4px 12px rgba(0,0,0,.08);
        }
        .option-label {
            border-radius: 8px;
            transition: all .2s;
            border: 1px solid #dee2e6;
            padding: 0.75rem 1rem;
            font-size: 1rem;
        }
        .option-label:hover {
            background-color: #f8f9fa;
            transform: translateY(-2px);
            box-shadow: 0 3px 8px rgba(0,0,0,.05);
        }
        .form-check-input:checked + .option-label {
            border-color: var(--primary);
            background-color: rgba(114,9,183,.05);
        }
        .btn-primary {
            background-color: var(--primary);
            border-color: var(--primary);
        }
        .btn-outline-primary {
            color: var(--primary);
            border-color: var(--primary);
        }
        .btn-outline-primary:hover {
            background-color: var(--primary);
            color: white;
        }
        /* Bottom fixed navigation for mobile */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: white;
            box-shadow: 0 -2px 10px rgba(0,0,0,.1);
            padding: 10px 15px;
            display: flex;
            justify-content: space-between;
            z-index: 90;
        }
        @media (min-width: 992px) {
            .main-content.with-nav {
                margin-right: 280px;
            }
            .question-nav {
                transform: translateX(0);
            }
            .nav-toggle-btn {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(30px, 1fr));
            }
            .option-label {
                padding: 0.625rem 0.875rem;
                font-size: 0.95rem;
            }
            .question-text {
                font-size: 1rem;
            }
            .navbar-brand {
                font-size: 1rem;
            }
            .timer-display {
                font-size: 0.8125rem;
                padding: 0.2rem 0.6rem;
            }
            .nav-btn {
                padding: 0.2rem 0.6rem;
                font-size: 0.8125rem;
            }
            .container {
                padding-left: 10px;
                padding-right: 10px;
            }
        }
    </style>
</head>
<body>
    <!-- Header navbar -->
    <nav class="navbar navbar-dark sticky-top">
        <div class="container-fluid px-2">
            <a class="navbar-brand fw-bold" href="#"><i class="fas fa-graduation-cap me-1"></i></a>
            <div class="controls-group">
                <!-- Timer -->
                <div class="timer-display me-1 fw-bold" id="timer">
                    <i class="far fa-clock me-1"></i><span id="timer-display">20:00</span>
                </div>
                <!-- Submit button -->
                <button class="btn btn-light btn-sm nav-btn me-1" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Language toggle -->
                <button class="btn btn-light btn-sm nav-btn me-1" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="btn btn-light btn-sm nav-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </nav>
    <!-- Main content -->
    <div class="main-content" id="main-content">
        <div class="container py-3">
            <!-- Question counter -->
            <div class="fw-bold text-center mb-3" id="question-counter">Question 1 of 25</div>
            <!-- Questions will be displayed here -->
            <div id="questions-container"></div>
        </div>
    </div>
    <!-- Bottom navigation for mobile -->
    <div class="bottom-nav d-lg-none">
        <button class="btn btn-outline-primary px-3" onclick="prevQuestion()">
            <i class="fas fa-chevron-left me-1"></i> Previous
        </button>
        <button class="btn btn-outline-primary px-3" onclick="nextQuestion()">
            Next <i class="fas fa-chevron-right ms-1"></i>
        </button>
    </div>
    <!-- Question navigation sidebar -->
    <div class="question-nav" id="question-nav">
        <div class="p-3">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h5 class="m-0">Questions</h5>
                <div>
                    <span class="badge bg-secondary me-1" id="attempted-count">0</span>
                    <span class="badge bg-secondary" id="total-count">25</span>
                    <button class="btn btn-sm btn-outline-secondary ms-2 d-lg-none" onclick="toggleNav()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
            <!-- Section selector moved here -->
            <div class="mb-3">
                <label for="section-selector" class="form-label small text-muted">Jump to Section</label>
                <select class="form-select form-select-sm" id="section-selector" onchange="switchSection(this.value)">
                    <option value="all">All Sections</option>
                </select>
            </div>
            <div class="question-grid" id="question-boxes"></div>
        </div>
    </div>
    <!-- Results modal -->
    <div class="modal fade" id="results-modal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content border-0">
                <div class="modal-header border-0" style="background:linear-gradient(135deg,var(--primary),var(--secondary));color:white">
                    <h5 class="modal-title">Test Results</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body p-4">
                    <div class="row text-center g-2">
                        <div class="col-6">
                            <div class="p-3 rounded shadow-sm" style="background:linear-gradient(135deg,#4361ee,#3a0ca3);color:white">
                                <h3 id="score-value" class="mb-0 fw-bold">0</h3>
                                <div class="small">Score</div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="p-3 rounded shadow-sm" style="background:linear-gradient(135deg,#06d6a0,#1b9aaa);color:white">
                                <h3 id="correct-value" class="mb-0 fw-bold">0</h3>
                                <div class="small">Correct</div>
                            </div>
                        </div>
                        <div class="col-6 mt-2">
                            <div class="p-3 rounded shadow-sm" style="background:linear-gradient(135deg,#ef476f,#f72585);color:white">
                                <h3 id="incorrect-value" class="mb-0 fw-bold">0</h3>
                                <div class="small">Incorrect</div>
                            </div>
                        </div>
                        <div class="col-6 mt-2">
                            <div class="p-3 rounded shadow-sm" style="background:linear-gradient(135deg,#6c757d,#495057);color:white">
                                <h3 id="unattempted-value" class="mb-0 fw-bold">0</h3>
                                <div class="small">Unattempted</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer border-0">
                    <button type="button" class="btn btn-primary" onclick="reviewTest()">Review Test</button>
                </div>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 20 * 60;
        let resultsModal;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            resultsModal = new bootstrap.Modal(document.getElementById('results-modal'));
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 0,
                end: 23
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 24,
                end: 24
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "17",
                    question_en: "<p>1. <span style=\"font-family: Times New Roman;\">The value of 14 - 20 &times; [7 - {18 &divide; 2 of 3 - (15 - 25 &divide; 5 &times; 4)}].</span></p>",
                    question_hi: "<p><span style=\"font-family: Baloo;\"><span style=\"font-family: Times New Roman;\">1. 14 - 20 &times; [7 - {18 &divide; 2 of 3 - (15 - 25 </span><span style=\"font-family: Times New Roman;\">&divide; 5 &times; 4)}]</span> का मान है।</span></p>",
                    options_en: ["<p>0</p>", "<p>24</p>", 
                                "<p>6</p>", "<p>34</p>"],
                    options_hi: ["<p>0</p>", "<p>24</p>",
                                "<p>6</p>", "<p>34</p>"],
                    solution_en: "<p>1.(d)<br><span style=\"font-family: Times New Roman;\">14 - 20 &times; [7 - {18 &divide; 2 of 3 - (15 - 25 &divide; 5 &times; 4)}]</span><br><span style=\"font-family: Times New Roman;\">14 - 20 &times; [7 - {18 &divide; 6 - (15 - 5 &times; 4)}]</span><br><span style=\"font-family: Times New Roman;\">14 - 20 &times; [7 - {3 - (15 - 20)}]</span><br><span style=\"font-family: Times New Roman;\">14 - 20 &times; [7 - {3 - (-5)}]</span><br><span style=\"font-family: Times New Roman;\">14 - 20 &times; [7 - 8]</span><br><span style=\"font-family: Times New Roman;\">14 - 20 &times; [-1] &rArr;</span><span style=\"font-family: Times New Roman;\">14 + 20 = 34</span></p>",
                    solution_hi: "<p>1.(d)<br><span style=\"font-family: Times New Roman;\">14 - 20 &times; [7 - {18 &divide; 2 of 3 - (15 - 25 &divide; 5 &times; 4)}]</span><br><span style=\"font-family: Times New Roman;\">14 - 20 &times; [7 - {18 &divide; 6 - (15 - 5 &times; 4)}]</span><br><span style=\"font-family: Times New Roman;\">14 - 20 &times; [7 - {3 - (15 - 20)}]</span><br><span style=\"font-family: Times New Roman;\">14 - 20 &times; [7 - {3 - (-5)}]</span><br><span style=\"font-family: Times New Roman;\">14 - 20 &times; [7 - 8]</span><br><span style=\"font-family: Times New Roman;\">14 - 20 &times; [-1] &rArr;</span><span style=\"font-family: Times New Roman;\">14 + 20 = 34</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "17",
                    question_en: "<p>2. <span style=\"font-family: Times New Roman;\">A circle touches all the four sides of quadrilateral ABCD whose sides are AB = 8.4 cm, BC = 9.8 cm, and CD = 5.6 cm. The length of side AD(in cm) is:</span></p>",
                    question_hi: "<p>2. <span style=\"font-family: Baloo;\">एक वृत्त चतुर्भुज ABCD की चारों भुजाओं को स्पर्श करता है जिसकी भुजाएँ AB = 8.4 सेमी, BC = 9.8 सेमी और CD = 5.6 सेमी हैं। भुजा AD की लंबाई सेमी में है |</span></p>",
                    options_en: ["<p>4.9</p>", "<p>4.2</p>", 
                                "<p>3.8</p>", "<p>2.8</p>"],
                    options_hi: ["<p>4.9</p>", "<p>4.2</p>",
                                "<p>3.8</p>", "<p>2.8</p>"],
                    solution_en: "<p>2.(b)<br><strong id=\"docs-internal-guid-283ff021-7fff-1122-84d1-40dfd61b8eb6\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdOu6bMhr9MUo1kIQlR-iNsyFBrThMKGDbZuxS_6cdNx2lsy2gMmY7JPGAir_xS8oJpbXKxi9UXmrxydK1hAz11HOqlb-Xa_EoYpnhe1Ej86LTVUidNt8ixtHAFj05st9c5z5P6xoTbv7qeOgCBWnWeEk30?key=TuSxBgvdZGn7d_vsP0g73HU4\" width=\"119\" height=\"111\"></strong><br><span style=\"font-family: Times New Roman;\">If a circle touches all the sides of the quadrilateral then the sum of opposite sides of the quadrilateral are equal.</span><br><span style=\"font-family: Times New Roman;\">So, AB + DC = BC + AD</span><br><span style=\"font-family: Times New Roman;\">AD = 8.4cm + 5.6cm - 9.8cm = 4.2cm</span></p>",
                    solution_hi: "<p>2.(b)<br><strong id=\"docs-internal-guid-283ff021-7fff-1122-84d1-40dfd61b8eb6\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdOu6bMhr9MUo1kIQlR-iNsyFBrThMKGDbZuxS_6cdNx2lsy2gMmY7JPGAir_xS8oJpbXKxi9UXmrxydK1hAz11HOqlb-Xa_EoYpnhe1Ej86LTVUidNt8ixtHAFj05st9c5z5P6xoTbv7qeOgCBWnWeEk30?key=TuSxBgvdZGn7d_vsP0g73HU4\" width=\"116\" height=\"108\"></strong><br><span style=\"font-family: Baloo;\">यदि एक वृत्त चतुर्भुज की सभी भुजाओं को स्पर्श करे तो चतुर्भुज की सम्मुख भुजाओं का योग बराबर होता है।</span><br><span style=\"font-family: Baloo;\">इसलिए, AB + DC = BC + AD</span><br><span style=\"font-family: Times New Roman;\">AD = 8.4cm + 5.6cm - 9.8cm = 4.2cm</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "17",
                    question_en: "<p>3. <span style=\"font-family: Times New Roman;\">If 2x<sup>2</sup> </span><span style=\"font-family: Times New Roman;\">- 8x - 1 = 0, then what is the value of&nbsp; 8x<sup>3</sup> - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msup><mi>x</mi><mn>3</mn></msup></mfrac></math> </span><span style=\"font-family: Times New Roman;\">?</span></p>",
                    question_hi: "<p>3. <span style=\"font-family: Baloo;\">यदि </span><span style=\"font-family: Times New Roman;\">2x<sup>2</sup></span><span style=\"font-family: Times New Roman;\">&nbsp;- 8x - 1 = 0, </span><span style=\"font-family: Baloo;\">तो</span><span style=\"font-family: Times New Roman;\">&nbsp; 8x<sup>3</sup> -&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msup><mi>x</mi><mn>3</mn></msup></mfrac></math></span><span style=\"font-family: Times New Roman;\">&nbsp;</span><span style=\"font-family: Baloo;\">का मान क्या है?</span></p>",
                    options_en: ["<p>560<span style=\"font-family: Times New Roman;\"> </span></p>", "<p>540</p>", 
                                "<p>524</p>", "<p>464</p>"],
                    options_hi: ["<p>560</p>", "<p>540</p>",
                                "<p>524</p>", "<p>464</p>"],
                    solution_en: "<p>3.(a)<br><span style=\"font-family: Times New Roman;\">2x</span><span style=\"font-family: Times New Roman;\">&sup2;</span><span style=\"font-family: Times New Roman;\"> -1 = 8x dividing by x</span><br><span style=\"font-family: Times New Roman;\">2x - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>x</mi></mfrac></math> </span><span style=\"font-family: Times New Roman;\">= 8 Taking cube on both side</span><br><span style=\"font-family: Times New Roman;\"> 8x<sup>3</sup> - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msup><mi>x</mi><mn>3</mn></msup></mfrac></math> </span><span style=\"font-family: Times New Roman;\">= 8<sup>3</sup> </span><span style=\"font-family: Times New Roman;\">+ 3 &times; 2 &times; 8</span><br><span style=\"font-family: Times New Roman;\"> 8x<sup>3</sup> - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msup><mi>x</mi><mn>3</mn></msup></mfrac></math> = 512 + 48 = 560</span></p>",
                    solution_hi: "<p>3.(a)<br><span style=\"font-family: Times New Roman;\">2x</span><span style=\"font-family: Times New Roman;\">&sup2;</span><span style=\"font-family: Times New Roman;\"> - 1 = 8x,&nbsp;</span><span style=\"font-family: Times New Roman;\">x </span><span style=\"font-family: Baloo;\">से भाग देने पर</span><br><span style=\"font-family: Times New Roman;\">2x - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>x</mi></mfrac></math> </span><span style=\"font-family: Times New Roman;\">= 8</span><br><span style=\"font-family: Baloo;\">दोनों पक्षों का घन करने पर,</span><br><span style=\"font-family: Times New Roman;\">8x<sup>3</sup> - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msup><mi>x</mi><mn>3</mn></msup></mfrac></math> </span><span style=\"font-family: Times New Roman;\">= 8<sup>3</sup> </span><span style=\"font-family: Times New Roman;\">+ 3 &times; 2 &times; 8</span><br><span style=\"font-family: Times New Roman;\"> 8x<sup>3</sup> - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msup><mi>x</mi><mn>3</mn></msup></mfrac></math> = 512 + 48 = 560</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "17",
                    question_en: "<p>4. <span style=\"font-family: Times New Roman;\">A, B and C divide a certain sum of money among themselves. The average of the amounts with them is 4520. Share of A is 10<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>3</mn></mfrac></math></span><span style=\"font-family: Times New Roman;\">% more than share of B and 33<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math></span><span style=\"font-family: Times New Roman;\">% less </span><span style=\"font-family: Times New Roman;\">than share</span><span style=\"font-family: Times New Roman;\"> of C. What is the share of B (in rupee)?</span></p>",
                    question_hi: "<p>4. <span style=\"font-family: Baloo;\">A, B और C एक निश्चित राशि को आपस में बांट लेते हैं। उनके पास राशियों का औसत 4520 है। A का हिस्सा B के हिस्से से</span><span style=\"font-family: Times New Roman;\"> 10 <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>3</mn></mfrac></math></span><span style=\"font-family: Times New Roman;\">% </span><span style=\"font-family: Baloo;\">अधिक है और C के हिस्से से</span><span style=\"font-family: Times New Roman;\"> 33<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math></span><span style=\"font-family: Times New Roman;\">% </span><span style=\"font-family: Baloo;\">कम है।</span><span style=\"font-family: Times New Roman;\"> B </span><span style=\"font-family: Baloo;\">का हिस्सा (रुपये में) क्या है?</span></p>",
                    options_en: ["<p>3500<span style=\"font-family: Times New Roman;\"> </span></p>", "<p>5976</p>", 
                                "<p>3600</p>", "<p>3984</p>"],
                    options_hi: ["<p>3500</p>", "<p>5976</p>",
                                "<p>3600</p>", "<p>3984</p>"],
                    solution_en: "<p>4.(c)<br><span style=\"font-family: Times New Roman;\">Ratio of A and B = 110<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>3</mn></mfrac></math></span><span style=\"font-family: Times New Roman;\">: 100 &rArr;</span><span style=\"font-family: Times New Roman;\"> 83 : 75</span><br><span style=\"font-family: Times New Roman;\">Ratio of A and C = 66<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>3</mn></mfrac></math></span><span style=\"font-family: Times New Roman;\">: 100 &rArr; </span><span style=\"font-family: Times New Roman;\">2 : 3</span><br><span style=\"font-family: Times New Roman;\">Balancing the ratio : 166 : 150 : 249</span><br><span style=\"font-family: Times New Roman;\">Average of A, B and C = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>166</mn><mo>+</mo><mn>150</mn><mo>+</mo><mn>249</mn></mrow><mn>3</mn></mfrac></math> </span><span style=\"font-family: Times New Roman;\">= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>565</mn><mn>3</mn></mfrac></math></span><br><span style=\"font-family: Times New Roman;\">So, 1 unit = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4520</mn><mfrac><mn>565</mn><mn>3</mn></mfrac></mfrac></math> = 24</span><br><span style=\"font-family: Times New Roman;\">Share of B = 24 &times; 150 = 3600</span></p>",
                    solution_hi: "<p>4.(c)<br><span style=\"font-family: Times New Roman;\">A </span><span style=\"font-family: Baloo;\">और</span><span style=\"font-family: Times New Roman;\"> B </span><span style=\"font-family: Baloo;\">का अनुपात</span><span style=\"font-family: Times New Roman;\"> = 110<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>3</mn></mfrac></math></span><span style=\"font-family: Times New Roman;\">: 100 &rArr;</span><span style=\"font-family: Times New Roman;\"> 83 : 75</span><br><span style=\"font-family: Times New Roman;\">A </span><span style=\"font-family: Baloo;\">और</span><span style=\"font-family: Times New Roman;\"> C </span><span style=\"font-family: Baloo;\">का अनुपात </span><span style=\"font-family: Times New Roman;\">= 66<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>3</mn></mfrac></math>:</span><span style=\"font-family: Times New Roman;\"> 100 &rArr;&nbsp;</span><span style=\"font-family: Times New Roman;\">2 : 3</span><br><span style=\"font-family: Baloo;\">अनुपात को संतुलित करने पर </span><span style=\"font-family: Times New Roman;\">: 166 : 150 : 249</span><br><span style=\"font-family: Times New Roman;\">A, B</span><span style=\"font-family: Baloo;\"> और </span><span style=\"font-family: Times New Roman;\">C</span><span style=\"font-family: Baloo;\"> का औसत </span><span style=\"font-family: Times New Roman;\">=</span><span style=\"font-family: Times New Roman;\">&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>166</mn><mo>+</mo><mn>150</mn><mo>+</mo><mn>249</mn></mrow><mn>3</mn></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>565</mn><mn>3</mn></mfrac></math> इकाई</span><br>इसलिए, 1 इकाई = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4520</mn><mfrac><mn>565</mn><mn>3</mn></mfrac></mfrac></math>= 24<br>B का हिस्सा = 24 &times; 150 = 3600</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "17",
                    question_en: "<p>5. <span style=\"font-family: Times New Roman;\">If 3 tan&theta; = 2<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math> sin&theta;, 0&deg; &lt; &theta; &lt; 90&deg;&nbsp;</span><span style=\"font-family: Times New Roman;\"> then find the value of&nbsp; 2sin<sup>2</sup>&theta; - 3 cos<sup>2</sup>3&theta;</span><span style=\"font-family: Times New Roman;\">.</span></p>",
                    question_hi: "<p>5. <span style=\"font-family: Baloo;\">यदि <span style=\"font-family: Times New Roman;\">3 tan&theta; = 2<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math> sin&theta;, 0&deg; &lt; &theta; &lt; 90&deg;</span></span><span style=\"font-family: Times New Roman;\">, 2sin<sup>2</sup>&theta; - 3 cos<sup>2</sup>3&theta; </span><span style=\"font-family: Baloo;\">का मान ज्ञात कीजिए।</span></p>",
                    options_en: ["<p>1<span style=\"font-family: Times New Roman;\"> </span></p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>2</mn></mfrac></math></p>", 
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math></p>", "<p>-<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>2</mn></mfrac></math></p>"],
                    options_hi: ["<p>1</p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>2</mn></mfrac></math></p>",
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math></p>", "<p>-<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>2</mn></mfrac></math></p>"],
                    solution_en: "<p>5.(c)<br>3tan&theta; = 2<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math> sin&theta;<br>sec&theta; =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><msqrt><mn>3</mn></msqrt></mfrac></math>, &theta; = 30&deg;<br>2sin<sup>2</sup>&theta; - 3cos2<sup>2</sup>3&theta; <br>= 2 &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>4</mn></mfrac></math> - 3 &times; 0 <br>=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math></p>",
                    solution_hi: "<p>5.(c)<br>3tan&theta; = 2<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math> sin&theta;<br>sec&theta; =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><msqrt><mn>3</mn></msqrt></mfrac></math>, &theta; = 30&deg;<br>2sin<sup>2</sup>&theta; - 3cos2<sup>2</sup>3&theta; <br>= 2 &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>4</mn></mfrac></math> - 3 &times; 0 <br>=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "17",
                    question_en: "<p>6. <span style=\"font-family: Times New Roman;\">The area of a circular park is 12474 m</span><span style=\"font-family: Times New Roman;\">&sup2;</span><span style=\"font-family: Times New Roman;\">. There </span><span style=\"font-family: Times New Roman;\">is 3.5</span><span style=\"font-family: Times New Roman;\"> m wide path around the park. What is the area (in m&sup2;</span><span style=\"font-family: Times New Roman;\">) of the path? (Take &pi; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mn>7</mn></mfrac></math></span><span style=\"font-family: Times New Roman;\">)</span></p>",
                    question_hi: "<p>6. <span style=\"font-family: Baloo;\">एक वृत्ताकार पार्क का क्षेत्रफल 12474 वर्ग मीटर है। पार्क के चारों ओर 3.5 मीटर चौड़ा पथ है। पथ का क्षेत्रफल (वर्ग मीटर में) क्या है?</span><span style=\"font-family: Times New Roman;\">(&pi; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mn>7</mn></mfrac></math></span><span style=\"font-family: Times New Roman;\">)</span></p>",
                    options_en: ["<p>1424.5</p>", "<p>1435.5</p>", 
                                "<p>1380.5</p>", "<p>1440.5</p>"],
                    options_hi: ["<p>1424.5</p>", "<p>1435.5</p>",
                                "<p>1380.5</p>", "<p>1440.5</p>"],
                    solution_en: "<p>6.(a)<br><span style=\"font-family: Times New Roman;\">Area of the park = 12474 =&nbsp;&pi;r<sup>2</sup></span><br><span style=\"font-family: Times New Roman;\">r = 63 m</span><br><span style=\"font-family: Times New Roman;\">Area of the ring = </span><span style=\"font-family: Times New Roman;\">(R<sup>2</sup> - r<sup>2</sup></span><span style=\"font-family: Times New Roman;\">)</span><br><span style=\"font-family: Times New Roman;\">Area of the ring = </span><span style=\"font-family: Times New Roman;\">(66.5<sup>2</sup> - 63<sup>2</sup></span><span style=\"font-family: Times New Roman;\">)&nbsp;</span><br><span style=\"font-family: Times New Roman;\">= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mn>7</mn></mfrac></math> &times; 129.5 &times; 3.5 <br>= 1424.5 m<sup>2</sup></span></p>",
                    solution_hi: "<p>6.(a)<br><span style=\"font-family: Baloo;\">पार्क का क्षेत्रफल</span><span style=\"font-family: Times New Roman;\"> = 12474 =&nbsp;&pi;r<sup>2</sup></span><br><span style=\"font-family: Times New Roman;\">r = 63 m</span><br><span style=\"font-family: Baloo;\">पथ का क्षेत्रफल</span><span style=\"font-family: Times New Roman;\"> = (R<sup>2</sup> - r<sup>2</sup>)</span><br><span style=\"font-family: Baloo;\">पथ का क्षेत्रफल</span><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Times New Roman;\">= </span><span style=\"font-family: Times New Roman;\">(66.5<sup>2</sup> - 63<sup>2</sup></span><span style=\"font-family: Times New Roman;\">)&nbsp;</span><br><span style=\"font-family: Times New Roman;\">= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mn>7</mn></mfrac></math> &times; 129.5 &times; 3.5 <br>= 1424.5 m<sup>2</sup></span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "17",
                    question_en: "<p>7. <span style=\"font-family: Times New Roman;\">In </span><span style=\"font-family: Gungsuh;\">ABC, &ang;C = 90&deg; and Q is the midpoint of BC. If AC = 2<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>10</mn></msqrt></math> cm and AB = 10 cm</span><span style=\"font-family: Times New Roman;\">, then the length of AQ is:</span></p>",
                    question_hi: "<p>7. <span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Baloo;\">ABC में, C = 90&deg; और Q, BC का मध्यबिंदु है। यदि AB = 10 सेमी और AC = 2<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>10</mn></msqrt></math></span><span style=\"font-family: Baloo;\"> सेमी, तो AQ की लंबाई है |</span></p>",
                    options_en: ["<p>&radic;55 cm</p>", "<p>5&radic;3 cm</p>", 
                                "<p>5&radic;2 cm</p>", "<p>3&radic;5 cm</p>"],
                    options_hi: ["<p>&radic;55 cm</p>", "<p>5&radic;3 cm</p>",
                                "<p>5&radic;2 cm</p>", "<p>3&radic;5 cm</p>"],
                    solution_en: "<p>7.(a)<br><strong id=\"docs-internal-guid-d043c28d-7fff-dfff-0f58-1653b00729de\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXelSIbg5xJ0L9PypLh2vfbv7yDiISl4NJe_4Tb7tCn-v0TjvyU9gR-ZwApxOycu7dzfCgIvC9DwDBN1_wnsmzUDiDFQpZdlcDaQbG71FBofdGxx7STi-0YabE3X06zsmT5Xu1tk7rL3o-5ipVqHujIuwKQ?key=TuSxBgvdZGn7d_vsP0g73HU4\" width=\"160\" height=\"130\"></strong><br><span style=\"font-family: Times New Roman;\">Applying pythagoras in </span><span style=\"font-family: Times New Roman;\">ABC,</span><br><span style=\"font-family: Baloo;\">AB<sup>2</sup> - AC<sup>2</sup> = CB<sup>2<br>CB =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>100</mn><mo>-</mo><mn>40</mn></msqrt></math> = 2<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>15</mn></msqrt></math><br>CQ =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math>CB = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>15</mn></msqrt></math></sup></span><br><span style=\"font-family: Times New Roman;\">Applying pythagoras in </span><span style=\"font-family: Times New Roman;\">ACQ</span><br><span style=\"font-family: Baloo;\">AQ<sup>2</sup> = AC<sup>2</sup> + CQ<sup>2</sup><br>AQ<sup>2</sup> = 40 + 15&nbsp; &nbsp; &nbsp; &nbsp;<br>AQ =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>55</mn></msqrt></math> cm</span></p>",
                    solution_hi: "<p>7.(a)<br><strong id=\"docs-internal-guid-d043c28d-7fff-dfff-0f58-1653b00729de\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXelSIbg5xJ0L9PypLh2vfbv7yDiISl4NJe_4Tb7tCn-v0TjvyU9gR-ZwApxOycu7dzfCgIvC9DwDBN1_wnsmzUDiDFQpZdlcDaQbG71FBofdGxx7STi-0YabE3X06zsmT5Xu1tk7rL3o-5ipVqHujIuwKQ?key=TuSxBgvdZGn7d_vsP0g73HU4\" width=\"160\" height=\"130\"></strong><br><span style=\"font-family: Times New Roman;\">ABC</span><span style=\"font-family: Baloo;\"> में पाइथागोरस लगाने से,<br>AB<sup>2</sup> - AC<sup>2</sup> = CB<sup>2<br>CB =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>100</mn><mo>-</mo><mn>40</mn></msqrt></math> = 2<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>15</mn></msqrt></math><br>CQ =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math>CB = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>15</mn></msqrt></math></sup></span><br><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Times New Roman;\">ACQ </span><span style=\"font-family: Baloo;\">में पाइथागोरस लगाने से,<br>AQ<sup>2</sup> = AC<sup>2</sup> + CQ<sup>2</sup><br>AQ<sup>2</sup> = 40 + 15&nbsp; &nbsp; &nbsp; &nbsp;<br>AQ =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>55</mn></msqrt></math> cm</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "17",
                    question_en: "<p>8. <span style=\"font-family: Times New Roman;\">The cost price of an article is Rs.280. A shopkeeper sells it by allowing 16% discount on its marked price and still gains 20%. What is the marked price (in rupee) of the article?</span></p>",
                    question_hi: "<p>8. <span style=\"font-family: Baloo;\">एक वस्तु का क्रय मूल्य 280 रुपये है। एक दुकानदार इसे इसके अंकित मूल्य पर 16% की छूट देकर बेचता है और फिर भी 20% का लाभ प्राप्त करता है। वस्तु का अंकित मूल्य (रुपये में) क्या है?</span></p>",
                    options_en: ["<p>400<span style=\"font-family: Times New Roman;\"> </span></p>", "<p>360<span style=\"font-family: Times New Roman;\"> </span></p>", 
                                "<p>420</p>", "<p>350</p>"],
                    options_hi: ["<p>400</p>", "<p>360</p>",
                                "<p>420</p>", "<p>350</p>"],
                    solution_en: "<p>8.(a)<br><span style=\"font-family: Times New Roman;\">CP = 280</span><br><span style=\"font-family: Times New Roman;\">Profit = 20%</span><br><span style=\"font-family: Times New Roman;\">SP = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>120</mn><mn>100</mn></mfrac></math> &times;</span><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Times New Roman;\">280 = 336</span><br><span style=\"font-family: Times New Roman;\">Discount = 16%</span><br><span style=\"font-family: Times New Roman;\">Marked price = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>100</mn><mn>84</mn></mfrac></math> &times; </span><span style=\"font-family: Times New Roman;\">336 = 400 </span></p>",
                    solution_hi: "<p>8.(a)<br><span style=\"font-family: Baloo;\">क्रय मूल्य </span><span style=\"font-family: Times New Roman;\">= 280</span><br><span style=\"font-family: Baloo;\">लाभ </span><span style=\"font-family: Times New Roman;\">= 20%</span><br><span style=\"font-family: Baloo;\">विक्रय मूल्य</span><span style=\"font-family: Times New Roman;\"> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>120</mn><mn>100</mn></mfrac></math> &times; 280 = 336</span><br><span style=\"font-family: Baloo;\">छूट </span><span style=\"font-family: Times New Roman;\">= 16%</span><br><span style=\"font-family: Baloo;\">अंकित मूल्य <span style=\"font-family: Times New Roman;\">= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>100</mn><mn>84</mn></mfrac></math> &times; </span><span style=\"font-family: Times New Roman;\">336 = 400</span></span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "17",
                    question_en: "<p>9. <span style=\"font-family: Times New Roman;\">A and B can do a </span><span style=\"font-family: Times New Roman;\">certain work</span><span style=\"font-family: Times New Roman;\"> in 18 days and 30 days, respectively. They work together for 5 days. C alone completes the remaining work in 15 days. A and C together can complete <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>6</mn></mfrac></math></span><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Times New Roman;\">th part of the same work in:</span></p>",
                    question_hi: "<p>9. <span style=\"font-family: Baloo;\">A और B एक निश्चित कार्य को क्रमशः 18 दिन और 30 दिन में पूरा कर सकते हैं। वे 5 दिनों के लिए एक साथ काम करते हैं। C अकेला शेष कार्य को 15 दिनों में पूरा करता है। A और C मिलकर समान कार्य के <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>6</mn></mfrac></math></span><span style=\"font-family: Baloo;\">वें ​​भाग को कितने समय में पूरा कर सकते हैं?</span></p>",
                    options_en: ["<p>6 days</p>", "<p>8 days</p>", 
                                "<p>9 days</p>", "<p>5 days</p>"],
                    options_hi: ["<p>6 <span style=\"font-family: Baloo;\">दिन</span><span style=\"font-family: Times New Roman;\"> </span></p>", "<p>8 <span style=\"font-family: Baloo;\">दिन </span></p>",
                                "<p>9 <span style=\"font-family: Baloo;\">दिन </span></p>", "<p>5<span style=\"font-family: Baloo;\"> दिन</span></p>"],
                    solution_en: "<p>9.(c)<br><span style=\"font-family: Times New Roman;\">Let the total work be 90 units</span><br><span style=\"font-family: Times New Roman;\">Then efficiency of A = 5 unit</span><br><span style=\"font-family: Times New Roman;\">Efficiency of B = 3 unit</span><br><span style=\"font-family: Times New Roman;\">Total efficiency of A and B = 8 unit</span><br><span style=\"font-family: Times New Roman;\">Work done in 5 days = 8 &times; 5 = 40unit&nbsp;</span><br><span style=\"font-family: Times New Roman;\">Work left = 50 unit</span><br><span style=\"font-family: Times New Roman;\">Efficiency of C = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>50</mn><mn>15</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>10</mn><mn>3</mn></mfrac></math></span><span style=\"font-family: Times New Roman;\">unit</span><br><span style=\"font-family: Times New Roman;\">Total efficiency of A and C = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>25</mn><mn>3</mn></mfrac></math></span><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Times New Roman;\">unit</span><br><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>6</mn></mfrac></math>th of the work = 75 unit</span><br><span style=\"font-family: Times New Roman;\">Time taken by the A and C = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>75</mn><mfrac><mn>25</mn><mn>3</mn></mfrac></mfrac></math></span><span style=\"font-family: Times New Roman;\">= 9 days</span></p>",
                    solution_hi: "<p>9.(c)<br><span style=\"font-family: Baloo;\">माना कुल कार्य </span><span style=\"font-family: Times New Roman;\">90 </span><span style=\"font-family: Baloo;\">इकाई </span><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Baloo;\">है</span><br><span style=\"font-family: Baloo;\">तब</span><span style=\"font-family: Times New Roman;\"> A </span><span style=\"font-family: Baloo;\">की क्षमता</span><span style=\"font-family: Times New Roman;\"> = 5 </span><span style=\"font-family: Baloo;\">इकाई</span><br><span style=\"font-family: Times New Roman;\">B </span><span style=\"font-family: Baloo;\">की क्षमता</span><span style=\"font-family: Times New Roman;\"> = 3 </span><span style=\"font-family: Baloo;\">इकाई</span><br><span style=\"font-family: Times New Roman;\">A </span><span style=\"font-family: Baloo;\">और</span><span style=\"font-family: Times New Roman;\"> B </span><span style=\"font-family: Baloo;\">की कुल क्षमता</span><span style=\"font-family: Times New Roman;\"> = 8 </span><span style=\"font-family: Baloo;\">इकाई</span><br><span style=\"font-family: Times New Roman;\">5 </span><span style=\"font-family: Baloo;\">दिनों</span><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Baloo;\">में किया गया कार्य</span><span style=\"font-family: Times New Roman;\"> = 8 &times; 5 = 40 </span><span style=\"font-family: Baloo;\">इकाई</span><br><span style=\"font-family: Baloo;\">शेष कार्य</span><span style=\"font-family: Times New Roman;\"> = 50 </span><span style=\"font-family: Baloo;\">इकाई</span><br><span style=\"font-family: Times New Roman;\">C </span><span style=\"font-family: Baloo;\">की क्षमता</span><span style=\"font-family: Times New Roman;\"> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>50</mn><mn>15</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>10</mn><mn>3</mn></mfrac></math></span><span style=\"font-family: Baloo;\">इकाई</span><br><span style=\"font-family: Times New Roman;\">A </span><span style=\"font-family: Baloo;\">और</span><span style=\"font-family: Times New Roman;\"> C</span><span style=\"font-family: Baloo;\"> की कुल दक्षता</span><span style=\"font-family: Times New Roman;\"> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>25</mn><mn>3</mn></mfrac></math></span><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Baloo;\">इकाई</span><br><span style=\"font-family: Baloo;\">कार्य का</span><span style=\"font-family: Times New Roman;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>6</mn></mfrac></math></span><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Baloo;\">वाँ भाग</span><span style=\"font-family: Times New Roman;\"> = 75 </span><span style=\"font-family: Baloo;\">इकाई</span><br><span style=\"font-family: Times New Roman;\">A </span><span style=\"font-family: Baloo;\">और</span><span style=\"font-family: Times New Roman;\"> C </span><span style=\"font-family: Baloo;\">द्वारा लिया गया समय</span><span style=\"font-family: Times New Roman;\"> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>75</mn><mfrac><mn>25</mn><mn>3</mn></mfrac></mfrac></math> </span><span style=\"font-family: Times New Roman;\">= 9 </span><span style=\"font-family: Baloo;\">दिन</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "17",
                    question_en: "<p>10. &nbsp;&Delta;ABC &sim; &Delta;DEF <span style=\"font-family: Times New Roman;\">and the area of &nbsp;&Delta;ABC </span><span style=\"font-family: Times New Roman;\">is 13.5 cm</span><span style=\"font-family: Times New Roman;\">&sup2;</span><span style=\"font-family: Times New Roman;\"> and the area of &Delta;DEF </span><span style=\"font-family: Times New Roman;\">is 24 cm</span><span style=\"font-family: Times New Roman;\">&sup2;</span><span style=\"font-family: Times New Roman;\">. If BC = 3.15 cm, then the length (in cm) of EF is:</span></p>",
                    question_hi: "<p>10. &nbsp;&Delta;ABC &sim; &Delta;DEF<span style=\"font-family: Times New Roman;\">&nbsp;</span><span style=\"font-family: Baloo;\">और &Delta;ABC का क्षेत्रफल 13.5 सेमी</span><span style=\"font-family: Times New Roman;\">&sup2;</span><span style=\"font-family: Baloo;\"> है और &Delta;DEF</span><span style=\"font-family: Baloo;\">&nbsp;का क्षेत्रफल 24 सेमी</span><span style=\"font-family: Times New Roman;\">&sup2;</span><span style=\"font-family: Baloo;\"> है। यदि BC = 3.15 सेमी, तो EF की लंबाई (सेमी में) है |</span></p>",
                    options_en: ["<p>4.8</p>", "<p>3.9</p>", 
                                "<p>5.1</p>", "<p>4.2</p>"],
                    options_hi: ["<p>4.8</p>", "<p>3.9</p>",
                                "<p>5.1</p>", "<p>4.2</p>"],
                    solution_en: "<p>10.(d)<br><span style=\"font-family: Times New Roman;\">Area of similar triangle are in the ratio of the square of their sides</span><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>A</mi><mi>r</mi><mi>e</mi><mi>a</mi><mi>&#160;</mi><mi>o</mi><mi>f</mi><mi>&#160;</mi><mi>&#916;</mi><mi>A</mi><mi>B</mi><mi>C</mi></mrow><mrow><mi>A</mi><mi>r</mi><mi>e</mi><mi>a</mi><mi>&#160;</mi><mi>o</mi><mi>f</mi><mi>&#160;</mi><mi>&#916;</mi><mi>D</mi><mi>E</mi><mi>F</mi><mi>&#160;</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>B</mi><msup><mi>C</mi><mn>2</mn></msup></mrow><mrow><mi>E</mi><msup><mi>F</mi><mn>2</mn></msup></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>13.5</mn><mn>24</mn></mfrac></math> = (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3.15</mn><mi>x</mi></mfrac></math>)<sup>2<br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>9</mn><mn>16</mn></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mn>3</mn><mn>2</mn></msup><msup><mn>4</mn><mn>2</mn></msup></mfrac></math> = (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3.15</mn><mi>x</mi></mfrac></math>)2</sup><br><span style=\"font-family: Times New Roman;\">x = 4.2 cm</span></p>",
                    solution_hi: "<p>10.(d)<br><span style=\"font-family: Baloo;\">समरूप त्रिभुज का क्षेत्रफल उनकी भुजाओं के वर्ग के अनुपात में होता है।</span><br><math style=\"font-family: Verdana;\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#916;</mi><mi>A</mi><mi>B</mi><mi>C</mi><mo>&#160;</mo><mi>&#2325;&#2366;</mi><mo>&#160;</mo><mo>&#160;</mo><mi>&#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;&#2347;&#2354;</mi></mrow><mrow><mi>&#160;</mi><mi>&#916;</mi><mi>D</mi><mi>E</mi><mi>F</mi><mi>&#160;</mi><mi>&#2325;&#2366;</mi><mo>&#160;</mo><mo>&#160;</mo><mi>&#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;&#2347;&#2354;</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>B</mi><msup><mi>C</mi><mn>2</mn></msup></mrow><mrow><mi>E</mi><msup><mi>F</mi><mn>2</mn></msup></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>13.5</mn><mn>24</mn></mfrac></math> = (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3.15</mn><mi>x</mi></mfrac></math>)<sup>2<br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>9</mn><mn>16</mn></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mn>3</mn><mn>2</mn></msup><msup><mn>4</mn><mn>2</mn></msup></mfrac></math> = (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3.15</mn><mi>x</mi></mfrac></math>)2</sup><br><span style=\"font-family: Times New Roman;\">x = 4.2 cm</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "17",
                    question_en: "<p>11. <span style=\"font-family: Times New Roman;\">Atul purchased Bread costing Rs.20 and gave a 100 rupee note to the shopkeeper. The shopkeeper gave the balance money in coins of denomination 2, 5, and 10. If these coins are in the ratio 5 : 4 : 1, then how many 5 coins did the shopkeeper give?</span></p>",
                    question_hi: "<p>11. <span style=\"font-family: Baloo;\">अतुल ने 20 रुपये की रोटी खरीदी और दुकानदार को 100 रुपये का नोट दिया। दुकानदार ने शेष राशि 2, 5 और 10 मूल्यवर्ग के सिक्कों में दी। यदि ये सिक्के 5:4:1 के अनुपात में हैं, तो दुकानदार ने कितने 5 सिक्के दिए?</span></p>",
                    options_en: ["<p>5</p>", "<p>6</p>", 
                                "<p>8</p>", "<p>4</p>"],
                    options_hi: ["<p>5</p>", "<p>6</p>",
                                "<p>8</p>", "<p>4</p>"],
                    solution_en: "<p>11.(c)<br><span style=\"font-family: Times New Roman;\">Ratio of 2Rs., 5Rs. and 10Rs. are in the ratio = 5 : 4 : 1</span><br><span style=\"font-family: Times New Roman;\">Amount Ratio = 10 : 20 : 10</span><br><span style=\"font-family: Times New Roman;\">Total Amount = 40 unit = 80Rs</span><br><span style=\"font-family: Times New Roman;\">1 unit = 2Rs.</span><br><span style=\"font-family: Times New Roman;\">Number of 5 rupees coin = 4 &times; 2 = 8</span></p>",
                    solution_hi: "<p>11.(c)<br><span style=\"font-family: Times New Roman;\">2Rs., 5Rs. </span><span style=\"font-family: Baloo;\">और </span><span style=\"font-family: Times New Roman;\">10 Rs </span><span style=\"font-family: Baloo;\">का अनुपात&nbsp;</span><span style=\"font-family: Times New Roman;\">= 5 : 4 : 1</span><br><span style=\"font-family: Baloo;\">राशि अनुपात </span><span style=\"font-family: Times New Roman;\">= 10 : 20 : 10</span><br><span style=\"font-family: Baloo;\">कुल राशि</span><span style=\"font-family: Times New Roman;\"> = 40 unit = 80Rs</span><br><span style=\"font-family: Times New Roman;\">1इकाई = 2Rs.</span><br><span style=\"font-family: Times New Roman;\">5</span><span style=\"font-family: Baloo;\"> रुपये के सिक्के की संख्या</span><span style=\"font-family: Times New Roman;\"> = 4 &times; 2 = 8</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "17",
                    question_en: "<p>12. <span style=\"font-family: Times New Roman;\">The value of 90 &divide; 20 of 6 &times; [11 &divide; 4 of {3 &times; 2 - (3 - 8)}] &divide; (9 &divide; 3 &times; 2) is:</span></p>",
                    question_hi: "<p>12. <span style=\"font-family: Times New Roman;\"> 90 &divide; 20 of 6 &times; [11 &divide; 4 of {3 &times; 2 - (3 - 8)}] &divide; (9 &divide; 3 &times; 2) </span><span style=\"font-family: Baloo;\">का मान है |</span></p>",
                    options_en: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>36</mn></mfrac></math></p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>32</mn></mfrac></math></p>", 
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>9</mn><mn>8</mn></mfrac></math></p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>8</mn></mfrac></math></p>"],
                    options_hi: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>36</mn></mfrac></math></p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>32</mn></mfrac></math></p>",
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>9</mn><mn>8</mn></mfrac></math></p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>8</mn></mfrac></math></p>"],
                    solution_en: "<p>12.(b)<br><span style=\"font-family: Times New Roman;\">90 &divide; 20 of 6 &times; [11 &divide; 4 of {3 &times; 2 - (3 - 8)}] &divide; (9 &divide; 3 &times; 2)</span><br><span style=\"font-family: Times New Roman;\">90 &divide; 120 &times; [11 &divide; 4 of {3 &times; 2 + 5}] &divide; (3 &times; 2)</span><br><span style=\"font-family: Times New Roman;\">90 &divide; 120 &times; [11 &divide; 4 of 11] &divide; 6</span><br><span style=\"font-family: Times New Roman;\">90 &divide; 120 &times; [11 &divide; 44] &divide; 6</span><br><span style=\"font-family: Times New Roman;\">&rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>4</mn></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>11</mn><mn>44</mn></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>6</mn></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>32</mn></mfrac></math></span></p>",
                    solution_hi: "<p>12.(b)<br><span style=\"font-family: Times New Roman;\">90 &divide; 20 का&nbsp; 6 &times; [11 &divide; 4 का&nbsp; {3 &times; 2 - (3 - 8)}] &divide; (9 &divide; 3 &times; 2)</span><br><span style=\"font-family: Times New Roman;\">90 &divide; 120 &times; [11 &divide; 4 का&nbsp; {3 &times; 2 + 5}] </span><span style=\"font-family: Times New Roman;\">&divide; (3 &times; 2)</span><br><span style=\"font-family: Times New Roman;\">90 &divide; 120 &times; [11 &divide; 4 का&nbsp; 11] &divide; 6</span><br><span style=\"font-family: Times New Roman;\">90 &divide; 120 &times; [11 &divide; 44] &divide; 6</span><br><span style=\"font-family: Times New Roman;\">&rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>4</mn></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>11</mn><mn>44</mn></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>6</mn></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>32</mn></mfrac></math></span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "17",
                    question_en: "<p>13.<span style=\"font-family: Times New Roman;\"> The average of eleven numbers is 56. The average of the first three numbers is 52 and that of the next five numbers is 60. The 9th and 10th numbers are 3 and 1 more than the 11th number respectively. What is the average of 9th and 11th numbers?</span></p>",
                    question_hi: "<p>13.<span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Baloo;\">ग्यारह संख्याओं का औसत 56 है। पहली तीन संख्याओं का औसत 52 है और अगली पांच संख्याओं का औसत 60 है। 9वीं और 10वीं संख्या क्रमशः 11वीं संख्या से 3 और 1 अधिक है। 9वीं और 11वीं संख्याओं का औसत क्या है?</span></p>",
                    options_en: ["<p>53.5<span style=\"font-family: Times New Roman;\"> </span></p>", "<p>52</p>", 
                                "<p>52.5</p>", "<p>54</p>"],
                    options_hi: ["<p>53.5</p>", "<p>52</p>",
                                "<p>52.5</p>", "<p>54</p>"],
                    solution_en: "<p>13.(a)<br><span style=\"font-family: Times New Roman;\">Total number = 11 &times; 56 = 616</span><br><span style=\"font-family: Times New Roman;\">Total of first three numbers = 52 &times; 3 = 156</span><br><span style=\"font-family: Times New Roman;\">Total of next three numbers = 60 &times; 5 = 300</span><br><span style=\"font-family: Times New Roman;\">Sum of 9</span><span style=\"font-family: Times New Roman;\">th</span><span style=\"font-family: Times New Roman;\">, 10</span><span style=\"font-family: Times New Roman;\">th</span><span style=\"font-family: Times New Roman;\"> and 11</span><span style=\"font-family: Times New Roman;\">th</span><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Times New Roman;\">number = 616 - 156 - 300 = 160</span><br><span style=\"font-family: Times New Roman;\">Let the 11th number = x then 10th = x + 1 and 9th = x + 3</span><br><span style=\"font-family: Times New Roman;\">3x + 4 = 160</span><br><span style=\"font-family: Times New Roman;\">3x = 156 So, x = 52 and x + 3 = 55</span><br><span style=\"font-family: Times New Roman;\">Average of 52 and 55 is 53.5.</span></p>",
                    solution_hi: "<p>13.(a)<br><span style=\"font-family: Baloo;\">कुल संख्या</span><span style=\"font-family: Times New Roman;\"> = 11 &times; 56 = 616</span><br><span style=\"font-family: Baloo;\">पहली तीन संख्याओं का योग </span><span style=\"font-family: Times New Roman;\">= 52 &times; 3 = 156</span><br><span style=\"font-family: Baloo;\">अगली तीन संख्याओं का योग</span><span style=\"font-family: Times New Roman;\"> = 60 &times; 5 = 300</span><br><span style=\"font-family: Baloo;\">9वीं, 10वीं और 11वीं </span><span style=\"font-family: Baloo;\">संख्या का योग</span><span style=\"font-family: Times New Roman;\"> = 616 - 156 - 300 = 160</span><br><span style=\"font-family: Baloo;\">माना</span><span style=\"font-family: Baloo;\"> 11वीं</span><span style=\"font-family: Baloo;\"> संख्या</span><span style=\"font-family: Times New Roman;\"> = x </span><span style=\"font-family: Baloo;\">तो </span><span style=\"font-family: Baloo;\">10वीं = x + 1 </span><span style=\"font-family: Baloo;\">और </span><span style=\"font-family: Baloo;\">9वीं = x + 3</span><br><span style=\"font-family: Times New Roman;\">3x + 4 = 160</span><br><span style=\"font-family: Times New Roman;\">3x = 156 So, x = 52 </span><span style=\"font-family: Baloo;\">तथा </span><span style=\"font-family: Times New Roman;\">x + 3 = 55</span><br><span style=\"font-family: Times New Roman;\">52 </span><span style=\"font-family: Baloo;\">और</span><span style=\"font-family: Times New Roman;\"> 55 </span><span style=\"font-family: Baloo;\">का औसत</span><span style=\"font-family: Baloo;\"> 53.5 है।</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "17",
                    question_en: "<p>14. <span style=\"font-family: Times New Roman;\">The radii of two concentric circles are 12 cm and 13 cm. AB is </span><span style=\"font-family: Times New Roman;\">a diameter</span><span style=\"font-family: Times New Roman;\"> of the bigger circle. BD is a tangent to a smaller circle touching it at D. Find the length (in cm) of AD. (Correct to one decimal place)</span></p>",
                    question_hi: "<p>14. <span style=\"font-family: Baloo;\">दो संकेंद्रित वृत्तों की त्रिज्याएँ 12 सेमी और 13 सेमी हैं। AB बड़े वृत्त का व्यास है। BD एक छोटे वृत्त की स्पर्श रेखा है जो इसे D पर स्पर्श करती है। AD की लंबाई (सेमी में) ज्ञात कीजिए। (एक दशमलव स्थान पर सही)</span></p>",
                    options_en: ["<p>24.5</p>", "<p>23.5</p>", 
                                "<p>25.5</p>", "<p>17.6</p>"],
                    options_hi: ["<p>24.5</p>", "<p>23.5</p>",
                                "<p>25.5</p>", "<p>17.6</p>"],
                    solution_en: "<p>14.(a)<br><strong id=\"docs-internal-guid-90885522-7fff-e01e-5dfd-3697482e2e89\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcB_Sii5oDGTb5wi5tRHUe6zTTj7ybmOr4kgH6CPqMNu-KmwYfr67Y8_5xJGFYeyJhg50oonaEzfbNZCnGEbPaK3LQPqvGYT9tVbhgv9S3blbUMMJiFYGLEDrIG2V7CMdZfQNS5bCcQtDE6OiQRJB2FM3s0?key=TuSxBgvdZGn7d_vsP0g73HU4\" width=\"210\" height=\"156\"></strong><br><span style=\"font-family: Times New Roman;\">In</span><span style=\"font-family: Times New Roman;\">, BD = 5 by pythagoras theorem</span><br><span style=\"font-family: Gungsuh;\">As, &ang;C = &ang;D = 90&deg; and &ang;B is common is both the triangles</span><br><span style=\"font-family: Times New Roman;\">So, &Delta;ABC &sim; &Delta;OBD</span><br><span style=\"font-family: Times New Roman;\">Ratio of corresponding side of &Delta;ABC : &Delta;OBD </span><span style=\"font-family: Times New Roman;\">&nbsp;= 2 : 1</span><br><span style=\"font-family: Times New Roman;\">DB = 5 then CB = 10 so, CD = 5cm</span><br><span style=\"font-family: Times New Roman;\">Similarly AC = 24cm</span><br><span style=\"font-family: Times New Roman;\">Applying pythagoras in &Delta;</span><span style=\"font-family: Times New Roman;\">ACD</span><br>AD<sup>2</sup> = 24<sup>2</sup> + 5<sup>2</sup> <br>= 601<br>= 24.5 cm</p>",
                    solution_hi: "<p>14.(a)<br><strong id=\"docs-internal-guid-90885522-7fff-e01e-5dfd-3697482e2e89\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcB_Sii5oDGTb5wi5tRHUe6zTTj7ybmOr4kgH6CPqMNu-KmwYfr67Y8_5xJGFYeyJhg50oonaEzfbNZCnGEbPaK3LQPqvGYT9tVbhgv9S3blbUMMJiFYGLEDrIG2V7CMdZfQNS5bCcQtDE6OiQRJB2FM3s0?key=TuSxBgvdZGn7d_vsP0g73HU4\" width=\"210\" height=\"156\"></strong><br><br><span style=\"font-family: Baloo;\">पाइथागोरस प्रमेय द्वारा,</span><br><span style=\"font-family: Times New Roman;\">BD = 5</span><br><span style=\"font-family: Baloo;\">क्योंकि</span><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Gungsuh;\">&ang;</span><span style=\"font-family: Times New Roman;\">C = </span><span style=\"font-family: Gungsuh;\">&ang;</span><span style=\"font-family: Times New Roman;\">D = 90&deg; </span><span style=\"font-family: Baloo;\">और </span><span style=\"font-family: Gungsuh;\">&ang;B </span><span style=\"font-family: Baloo;\">दोनों त्रिभुजों में उभयनिष्ठ हैं</span><br><span style=\"font-family: Baloo;\">तो,</span><span style=\"font-family: Times New Roman;\"> &Delta;ABC &sim; &Delta;OBD</span><br><span style=\"font-family: Times New Roman;\">&Delta;ABC : &Delta;OBD&nbsp; </span><span style=\"font-family: Baloo;\">की संगत भुजा का अनुपात</span><span style=\"font-family: Times New Roman;\"> = 2</span><span style=\"font-family: Times New Roman;\"> : 1</span><br><span style=\"font-family: Times New Roman;\">DB = 5 फिर CB = 10 इसलिए, CD = 5cm</span><br><span style=\"font-family: Baloo;\">उसी प्रकार</span><span style=\"font-family: Times New Roman;\">, AC = 24cm</span><br><span style=\"font-family: Times New Roman;\">&Delta;ACD </span><span style=\"font-family: Baloo;\">में पाइथागोरस प्रमेय लगाने पर</span><br>AD<sup>2</sup> = 24<sup>2</sup> + 5<sup>2</sup> <br>= 601<br>= 24.5 cm</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "17",
                    question_en: "<p>15. <span style=\"font-family: Times New Roman;\">A train running at 40.5 km/h takes 24 seconds to cross a pole. How much time (in seconds) will it take to pass a 450 m long bridge?</span></p>",
                    question_hi: "<p>15. <span style=\"font-family: Baloo;\">40.5 किमी/घंटा की गति से चलने वाली एक रेलगाड़ी एक खम्भे को पार करने में 24 सेकंड का समय लेती है। 450 मीटर लंबे पुल को पार करने में कितना समय (सेकंड में) लगेगा?</span></p>",
                    options_en: ["<p>56 <span style=\"font-family: Times New Roman;\"> </span></p>", "<p>52</p>", 
                                "<p>60</p>", "<p>64</p>"],
                    options_hi: ["<p>56 <span style=\"font-family: Times New Roman;\"> </span></p>", "<p>5 2</p>",
                                "<p>60 <span style=\"font-family: Times New Roman;\"> </span></p>", "<p>64</p>"],
                    solution_en: "<p>15.(d)<br><span style=\"font-family: Times New Roman;\">Speed of the train =&nbsp;</span><span style=\"font-family: Times New Roman;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>40</mn><mo>.</mo><mn>5</mn></mrow><mn>18</mn></mfrac></math> &times; 5 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>45</mn><mn>4</mn></mfrac></math></span><span style=\"font-family: Times New Roman;\">&nbsp;</span><span style=\"font-family: Times New Roman;\">m/s</span><br><span style=\"font-family: Times New Roman;\">It takes 24 sec to cross its length </span><br><span style=\"font-family: Times New Roman;\">Time taken by train to cross a bridge =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>450</mn><mfrac><mn>45</mn><mn>4</mn></mfrac></mfrac></math></span><span style=\"font-family: Times New Roman;\">&nbsp;</span><span style=\"font-family: Times New Roman;\">= 40sec</span><br><span style=\"font-family: Times New Roman;\">Total time = 40 + 24 = 64 sec</span></p>",
                    solution_hi: "<p>15.(d)<br><span style=\"font-family: Baloo;\">ट्रैन की गति</span><span style=\"font-family: Times New Roman;\"> =&nbsp; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>40</mn><mo>.</mo><mn>5</mn></mrow><mn>18</mn></mfrac></math> &times; 5 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>45</mn><mn>4</mn></mfrac></math> </span><span style=\"font-family: Baloo;\">मीटर/सेकंड</span><br><span style=\"font-family: Baloo;\">ट्रेन अपनी लंबाई को पार करने में 24 सेकंड का समय लेती है</span><br><span style=\"font-family: Baloo;\">एक पुल को पार करने में ट्रेन द्वारा लिया गया समय&nbsp;</span><span style=\"font-family: Times New Roman;\">=</span><span style=\"font-family: Times New Roman;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>450</mn><mfrac><mn>45</mn><mn>4</mn></mfrac></mfrac></math> </span><span style=\"font-family: Times New Roman;\">= 40 </span><span style=\"font-family: Baloo;\">सेकंड</span><span style=\"font-family: Times New Roman;\"> </span><br><span style=\"font-family: Baloo;\">कुल समय&nbsp;</span><span style=\"font-family: Times New Roman;\">= 40 + 24 = 64 </span><span style=\"font-family: Baloo;\">सेकंड</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "17",
                    question_en: "<p>16.<span style=\"font-family: Times New Roman;\"> If&nbsp; x<sup>4</sup> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msup><mi>x</mi><mn>4</mn></msup></mfrac></math> </span><span style=\"font-family: Times New Roman;\">= 727, x &gt; 1, then what is the value of </span><span style=\"font-family: Times New Roman;\">&nbsp;(x - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>x</mi></mfrac></math></span><span style=\"font-family: Times New Roman;\">)</span><span style=\"font-family: Times New Roman;\">?</span></p>",
                    question_hi: "<p>16.<span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Baloo;\">यदि <span style=\"font-family: Times New Roman;\">x<sup>4</sup> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msup><mi>x</mi><mn>4</mn></msup></mfrac></math></span></span><span style=\"font-family: Times New Roman;\">&nbsp;</span><span style=\"font-family: Times New Roman;\">= 727, x &gt; 1</span><span style=\"font-family: Baloo;\">, तो</span><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Times New Roman;\">(x - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>x</mi></mfrac></math></span><span style=\"font-family: Times New Roman;\">) </span><span style=\"font-family: Baloo;\">का मान क्या है?</span></p>",
                    options_en: ["<p>6<span style=\"font-family: Times New Roman;\"> </span></p>", "<p>-6</p>", 
                                "<p>-5</p>", "<p>5</p>"],
                    options_hi: ["<p>6<span style=\"font-family: Times New Roman;\"> </span></p>", "<p>-6<span style=\"font-family: Times New Roman;\"> </span></p>",
                                "<p>-5<span style=\"font-family: Times New Roman;\"> </span></p>", "<p>5</p>"],
                    solution_en: "<p>16.(d)<br><span style=\"font-family: Times New Roman;\">x<sup>4</sup> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msup><mi>x</mi><mn>4</mn></msup></mfrac></math> = 727</span><br><span style=\"font-family: Times New Roman;\">adding 2 and taking root on both side&nbsp;</span><br><span style=\"font-family: Times New Roman;\">x<sup>2</sup> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msup><mi>x</mi><mn>2</mn></msup></mfrac></math> = 27</span><br><span style=\"font-family: Times New Roman;\">subtracting 2 from both side and taking root</span><br><span style=\"font-family: Times New Roman;\">x -</span><span style=\"font-family: Times New Roman;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>x</mi></mfrac></math> = 5 and -5, But x &gt; 1 So, x = 5</span></p>",
                    solution_hi: "<p>16.(d)<br><span style=\"font-family: Times New Roman;\">x<sup>4</sup> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msup><mi>x</mi><mn>4</mn></msup></mfrac></math> </span>= 727<br><span style=\"font-family: Baloo;\">दोनों पक्षों</span><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Baloo;\">में</span><span style=\"font-family: Times New Roman;\"> 2 </span><span style=\"font-family: Baloo;\">जोड़कर सबका वर्गमूल करने पर,</span><br><span style=\"font-family: Baloo;\"><span style=\"font-family: Times New Roman;\">x<sup>2</sup> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msup><mi>x</mi><mn>2</mn></msup></mfrac></math> = 27</span> </span></p>\n<p><span style=\"font-family: Baloo;\">दोनों पक्षों में से</span><span style=\"font-family: Times New Roman;\"> 2 </span><span style=\"font-family: Baloo;\">घटा कर सबका वर्गमूल करने पर,</span><br><span style=\"font-family: Times New Roman;\">x -</span><span style=\"font-family: Baloo;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>x</mi></mfrac></math>= 5 और -5, </span><span style=\"font-family: Baloo;\">लेकिन</span><span style=\"font-family: Times New Roman;\"> x &gt; 1 </span><span style=\"font-family: Baloo;\">इसलिए</span><span style=\"font-family: Times New Roman;\"> , x = 5</span></p>\n<p>&nbsp;</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "17",
                    question_en: "<p>17. <span style=\"font-family: Times New Roman;\">Find the greatest value of b so that 30a68b (a &gt; b) is divisible by 11.</span></p>",
                    question_hi: "<p>17. <span style=\"font-family: Times New Roman;\">b </span><span style=\"font-family: Baloo;\">का वह अधिकतम मान ज्ञात कीजिए जो</span><span style=\"font-family: Times New Roman;\"> 30a68b (a &gt; b) 11</span><span style=\"font-family: Baloo;\"> से विभाज्य हो।</span></p>",
                    options_en: ["<p>4</p>", "<p>9</p>", 
                                "<p>3</p>", "<p>6</p>"],
                    options_hi: ["<p>4</p>", "<p>9</p>",
                                "<p>3<span style=\"font-family: Times New Roman;\"> </span></p>", "<p>6</p>"],
                    solution_en: "<p>17.(c)<br><span style=\"font-family: Times New Roman;\">Sum of digits at even places = 0 + 6 + b = 6 + b</span><br><span style=\"font-family: Times New Roman;\">Sum of digits at odd places = 3 + a + 8 = 11 + a</span><br><span style=\"font-family: Times New Roman;\">11 + a - 6 - b = 0 or 11</span><br><span style=\"font-family: Times New Roman;\">If 5 + a - b = 0 then a &lt; b (Condition not satisfying )</span><br><span style=\"font-family: Times New Roman;\">So, For a &gt; b, 5 + a - b = 11</span><br><span style=\"font-family: Times New Roman;\">a - b = 6 as both a and b both are single digit numbers and we need the maximum value of b. So, a = 9, b = 3 </span></p>",
                    solution_hi: "<p>17.(c)<br><span style=\"font-family: Baloo;\">सम स्थानों पर अंकों का योग =</span><span style=\"font-family: Times New Roman;\"> 0 + 6 + b = 6 + b</span><br><span style=\"font-family: Baloo;\">विषम स्थानों पर अंकों का योग =</span><span style=\"font-family: Times New Roman;\"> 3 + a + 8 = 11 + a</span><br><span style=\"font-family: Times New Roman;\">11 + a - 6 - b = 0&nbsp;</span><span style=\"font-family: Baloo;\">या</span><span style=\"font-family: Times New Roman;\"> 11</span><br><span style=\"font-family: Baloo;\">यदि</span><span style=\"font-family: Times New Roman;\"> 5 + a - b = 0 </span><span style=\"font-family: Baloo;\">तो</span><span style=\"font-family: Times New Roman;\"> a &lt; b </span><span style=\"font-family: Baloo;\">(स्थिति संतोषजनक नहीं है)</span><br><span style=\"font-family: Baloo;\">अत:</span><span style=\"font-family: Times New Roman;\"> a &gt; b </span><span style=\"font-family: Baloo;\">के लिए</span><span style=\"font-family: Times New Roman;\">, 5 + a - b = 11</span><br><span style=\"font-family: Times New Roman;\">a - b = 6 </span><span style=\"font-family: Baloo;\">क्योंकि</span><span style=\"font-family: Times New Roman;\"> a </span><span style=\"font-family: Baloo;\">और</span><span style=\"font-family: Times New Roman;\"> b </span><span style=\"font-family: Baloo;\">दोनों एक अंक की संख्या हैं और हमें </span><span style=\"font-family: Times New Roman;\">b </span><span style=\"font-family: Baloo;\">के अधिकतम मान की आवश्यकता है। तो</span><span style=\"font-family: Times New Roman;\">, a = 9, b = 3 </span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "17",
                    question_en: "<p>18.<span style=\"font-family: Times New Roman;\"> A trader bought 640 kg of rice. He sold a part of rice at 20% profit and the rest at 5% loss. He earned a profit of 15% in the entire transaction. What is the quantity (in kg) of rice that he sold at 5% loss?</span></p>",
                    question_hi: "<p>18. <span style=\"font-family: Baloo;\">एक व्यापारी ने 640 किग्रा चावल खरीदा। उसने चावल का एक भाग 20% लाभ पर और शेष 5% हानि पर बेचा। उसने पूरे लेनदेन में 15% का लाभ कमाया। चावल की मात्रा (किलो में) क्या है जिसे उसने 5% हानि पर बेचा?</span></p>",
                    options_en: ["<p>128</p>", "<p>132</p>", 
                                "<p>154</p>", "<p>256</p>"],
                    options_hi: ["<p>128</p>", "<p>132</p>",
                                "<p>154</p>", "<p>256</p>"],
                    solution_en: "<p>18.(a)<br><strong id=\"docs-internal-guid-ce180ba9-7fff-6934-3887-e9f6e8229194\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcHN9hjBJxzf0el_VLRVf4vj4c9xXor0v3dHf2e0kCvxWZAKIOaI-V8nQwYft0Alb9QfA_tIQFOLFPzQWa2l58bH_n4cIz7hEdzJP0swb7ASjm73aJ0L_UiqzK9IrKU3ShEOYBQW0jQOT_nI8ykrAvXKfba?key=TuSxBgvdZGn7d_vsP0g73HU4\" width=\"140\" height=\"138\"></strong><br><span style=\"font-family: Times New Roman;\">Ratio of 20% Profit : 5 % loss = 4 : 1</span><br><span style=\"font-family: Times New Roman;\">Quantity of 5% loss = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>5</mn></mfrac></math> </span><span style=\"font-family: Times New Roman;\">&times; 640 = 128</span></p>",
                    solution_hi: "<p>18.(a)<br><strong id=\"docs-internal-guid-ce180ba9-7fff-6934-3887-e9f6e8229194\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcHN9hjBJxzf0el_VLRVf4vj4c9xXor0v3dHf2e0kCvxWZAKIOaI-V8nQwYft0Alb9QfA_tIQFOLFPzQWa2l58bH_n4cIz7hEdzJP0swb7ASjm73aJ0L_UiqzK9IrKU3ShEOYBQW0jQOT_nI8ykrAvXKfba?key=TuSxBgvdZGn7d_vsP0g73HU4\" width=\"140\" height=\"138\"></strong><br><span style=\"font-family: Baloo;\">अनुपात लाभ का</span><span style=\"font-family: Times New Roman;\"> 20% : </span><span style=\"font-family: Baloo;\">हानि का </span><span style=\"font-family: Times New Roman;\">5% = 4: 1</span><br><span style=\"font-family: Times New Roman;\">5% </span><span style=\"font-family: Baloo;\">हानि की मात्रा</span><span style=\"font-family: Times New Roman;\"> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>5</mn></mfrac></math></span><span style=\"font-family: Times New Roman;\">&times; 640 = 128</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "17",
                    question_en: "<p>19. <span style=\"font-family: Times New Roman;\">If x - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>x</mi></mfrac></math> </span><span style=\"font-family: Times New Roman;\">= 1, then what is the value of x<sup>8</sup> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msup><mi>x</mi><mn>8</mn></msup></mfrac></math> </span><span style=\"font-family: Times New Roman;\">?</span></p>",
                    question_hi: "<p>19. <span style=\"font-family: Baloo;\">यदि</span><span style=\"font-family: Times New Roman;\"> x - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>x</mi></mfrac></math> </span><span style=\"font-family: Times New Roman;\">= 1,</span><span style=\"font-family: Baloo;\"> तो <span style=\"font-family: Times New Roman;\">x<sup>8</sup> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msup><mi>x</mi><mn>8</mn></msup></mfrac></math></span></span><span style=\"font-family: Times New Roman;\">&nbsp;</span><span style=\"font-family: Baloo;\">का मान क्या है?</span></p>",
                    options_en: ["<p>3<span style=\"font-family: Times New Roman;\"> </span></p>", "<p>119</p>", 
                                "<p>47</p>", "<p>-1</p>"],
                    options_hi: ["<p>3<span style=\"font-family: Times New Roman;\"> </span></p>", "<p>119<span style=\"font-family: Times New Roman;\"> </span></p>",
                                "<p>47<span style=\"font-family: Times New Roman;\"> </span></p>", "<p>-1</p>"],
                    solution_en: "<p>19.(c)<br><span style=\"font-family: Times New Roman;\">x - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>x</mi></mfrac></math> = 1 Squaring on both sides</span><br><span style=\"font-family: Times New Roman;\">x<sup>2</sup> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msup><mi>x</mi><mn>2</mn></msup></mfrac></math> = 1<sup>2</sup> + 2 = 3&nbsp; <br>Squaring on both sides<br>x<sup>4</sup> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msup><mi>x</mi><mn>4</mn></msup></mfrac></math> = 3<sup>2</sup> - 2 = 7</span><br><span style=\"font-family: Times New Roman;\">x<sup>8</sup> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msup><mi>x</mi><mn>8</mn></msup></mfrac></math> = 7<sup>2</sup> - 2 = 47</span></p>",
                    solution_hi: "<p>19.(c)<br><span style=\"font-family: Baloo;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>x</mi><mo>-</mo><mfrac><mn>1</mn><mi>x</mi></mfrac></math>=1&nbsp; &nbsp;</span><span style=\"font-family: Baloo;\">दोनों पक्षों का वर्ग करने पर,</span><br><span style=\"font-family: Times New Roman;\">x<sup>2</sup> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msup><mi>x</mi><mn>2</mn></msup></mfrac></math> = 1<sup>2</sup> + 2 = 3</span><br><span style=\"font-family: Baloo;\">दोनों पक्षों का वर्ग करने पर,</span><br><span style=\"font-family: Times New Roman;\">x<sup>4</sup> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msup><mi>x</mi><mn>4</mn></msup></mfrac></math> = 3<sup>2</sup> - 2 = 7</span><br><span style=\"font-family: Times New Roman;\">x<sup>8</sup> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msup><mi>x</mi><mn>8</mn></msup></mfrac></math> = 7<sup>2</sup> - 2 = 47</span></p>\n<p>&nbsp;</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "17",
                    question_en: "<p>20. <span style=\"font-family: Times New Roman;\">A train is to cover 370 km at a uniform speed. After running 100 km, the train could run at a speed 5 km/h less than its normal speed due to some technical fault. The train got delayed by 36 minutes. What is the normal speed of the train, in km/h?</span></p>",
                    question_hi: "<p>20. <span style=\"font-family: Baloo;\">एक ट्रेन को एक समान गति से 370 किमी की दूरी तय करनी है। 100 किमी चलने के बाद, ट्रेन कुछ तकनीकी खराबी के कारण अपनी सामान्य गति से 5 किमी / घंटा कम गति से चल सकती थी। ट्रेन 36 मिनट की देरी से चली। ट्रेन की सामान्य गति किमी/घंटा में क्या है?</span></p>",
                    options_en: ["<p>48 <span style=\"font-family: Times New Roman;\"> </span></p>", "<p>45 <span style=\"font-family: Times New Roman;\"> </span></p>", 
                                "<p>40</p>", "<p>50</p>"],
                    options_hi: ["<p>48 <span style=\"font-family: Times New Roman;\"> </span></p>", "<p>45 <span style=\"font-family: Times New Roman;\"> </span></p>",
                                "<p>40 <span style=\"font-family: Times New Roman;\"> </span></p>", "<p>50</p>"],
                    solution_en: "<p>20.(d)<br><span style=\"font-family: Times New Roman;\">For the distance of 270 Km</span><br><span style=\"font-family: Times New Roman;\">According to question</span><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>270</mn><mrow><mi>x</mi><mo>+</mo><mn>5</mn></mrow></mfrac></math> -&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>270</mn><mi>x</mi></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>36</mn><mn>60</mn></mfrac></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>270</mn><mi>x</mi><mo>-</mo><mn>270</mn><mi>x</mi><mo>+</mo><mn>1350</mn></mrow><mrow><mi>x</mi><mo>(</mo><mi>x</mi><mo>+</mo><mn>5</mn><mo>)</mo></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>5</mn></mfrac></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#160;</mi><mn>450</mn></mrow><mrow><mi>x</mi><mo>(</mo><mi>x</mi><mo>+</mo><mn>5</mn><mo>)</mo></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>5</mn></mfrac></math><br>x(x + 5) = 2250<br>so, x + 5 = 50</p>",
                    solution_hi: "<p>20.(d)<br><span style=\"font-family: Times New Roman;\">270 </span><span style=\"font-family: Baloo;\">किमी की दूरी तय करने के लिए</span><br><span style=\"font-family: Baloo;\">प्रश्न के अनुसार</span><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>270</mn><mrow><mi>x</mi><mo>+</mo><mn>5</mn></mrow></mfrac></math> -&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>270</mn><mi>x</mi></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>36</mn><mn>60</mn></mfrac></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>270</mn><mi>x</mi><mo>-</mo><mn>270</mn><mi>x</mi><mo>+</mo><mn>1350</mn></mrow><mrow><mi>x</mi><mo>(</mo><mi>x</mi><mo>+</mo><mn>5</mn><mo>)</mo></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>5</mn></mfrac></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#160;</mi><mn>450</mn></mrow><mrow><mi>x</mi><mo>(</mo><mi>x</mi><mo>+</mo><mn>5</mn><mo>)</mo></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>5</mn></mfrac></math><br>x(x + 5) = 2250<br>इसलिए , x + 5 = 50</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "17",
                    question_en: "<p>21. <span style=\"font-family: Times New Roman;\">Two equal sums were lent on simple interest at 6% and 10% per annum respectively. The first sum was recovered two years later than the second sum and the amount in each case was Rs. 1105. What was the sum (in Rs.) lent in each scheme?</span></p>",
                    question_hi: "<p>21. <span style=\"font-family: Baloo;\">दो समान राशियों को साधारण ब्याज पर क्रमशः 6% और 10% प्रतिवर्ष की दर से उधार दिया गया। पहली राशि दूसरी राशि की तुलना में दो साल बाद वसूल की गई और प्रत्येक मामले में राशि 1105 रुपये थी। प्रत्येक योजना में उधार दी गई राशि (रुपये में) क्या थी?</span></p>",
                    options_en: ["<p>900</p>", "<p>850</p>", 
                                "<p>936</p>", "<p>891</p>"],
                    options_hi: ["<p>900</p>", "<p>850</p>",
                                "<p>936</p>", "<p>891</p>"],
                    solution_en: "<p>21.(b)<br><span style=\"font-family: Times New Roman;\">For the equal sum, interest is the same then the product of time and rate of interest is the same</span><br><span style=\"font-family: Times New Roman;\">So, the rate in the ratio of 3 : 5 therefore the time is in the ratio of 5 : 3</span><br><span style=\"font-family: Times New Roman;\">According to question, difference in the time = 2 years</span><br><span style=\"font-family: Times New Roman;\">So, time is 5 years and 3 years</span><br><span style=\"font-family: Times New Roman;\">Interest in each case = 30% of the sum lent</span><br><span style=\"font-family: Times New Roman;\">Total amount = 130% of the sum lent = 1105</span><br><span style=\"font-family: Times New Roman;\">Sum lent = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1105</mn><mn>130</mn></mfrac></math> &times; 100</span><span style=\"font-family: Times New Roman;\"> = 850</span></p>",
                    solution_hi: "<p>21.(b)<br><span style=\"font-family: Baloo;\">समान राशि के लिए, ब्याज समान है तो समय और ब्याज दर का गुणनफल समान है</span><br><span style=\"font-family: Baloo;\">अत: दर</span><span style=\"font-family: Times New Roman;\"> 3 : 5 </span><span style=\"font-family: Baloo;\">के अनुपात में है इसलिए समय</span><span style=\"font-family: Times New Roman;\"> 5 : 3 </span><span style=\"font-family: Baloo;\">के अनुपात में है</span><br><span style=\"font-family: Baloo;\">प्रश्नों के प्रकार, समय में अंतर</span><span style=\"font-family: Times New Roman;\"> = 2 </span><span style=\"font-family: Baloo;\">वर्ष</span><br><span style=\"font-family: Baloo;\">तो, समय </span><span style=\"font-family: Times New Roman;\">5 </span><span style=\"font-family: Baloo;\">वर्ष और </span><span style=\"font-family: Times New Roman;\">3 </span><span style=\"font-family: Baloo;\">वर्ष है</span><br><span style=\"font-family: Baloo;\">प्रत्येक स्थिति में ब्याज </span><span style=\"font-family: Times New Roman;\">= </span><span style=\"font-family: Baloo;\">दी गई राशि का</span><span style=\"font-family: Times New Roman;\"> 30%</span><br><span style=\"font-family: Baloo;\">कुल राशि</span><span style=\"font-family: Times New Roman;\"> = </span><span style=\"font-family: Baloo;\">उधार दी गई राशि का</span><span style=\"font-family: Times New Roman;\"> 130%&nbsp; </span><span style=\"font-family: Times New Roman;\">= 1105</span><br><span style=\"font-family: Baloo;\">उधार दी गई राशि <span style=\"font-family: Times New Roman;\">= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1105</mn><mn>130</mn></mfrac></math> &times; 100</span><span style=\"font-family: Times New Roman;\"> = 850</span></span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "17",
                    question_en: "<p>22. <span style=\"font-family: Times New Roman;\">If 3 sec&theta; + 4 cos&theta; - 4<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math> = 0 </span><span style=\"font-family: Times New Roman;\">where </span><span style=\"font-family: Times New Roman;\">is an acute angle then the value of &theta;</span><span style=\"font-family: Times New Roman;\">&nbsp;is:</span></p>",
                    question_hi: "<p>22. <span style=\"font-family: Baloo;\">यदि</span><span style=\"font-family: Times New Roman;\"> 3 sec&theta; + 4 cos&theta; - 4<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math> = 0</span><span style=\"font-family: Times New Roman;\">, </span><span style=\"font-family: Baloo;\">जहां</span><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Baloo;\">एक न्यून कोण है तो</span><span style=\"font-family: Times New Roman;\"> &theta;</span><span style=\"font-family: Times New Roman;\">&nbsp;</span><span style=\"font-family: Baloo;\">का मान है |</span></p>",
                    options_en: ["<p>20&deg;<span style=\"font-family: Times New Roman;\"> </span></p>", "<p>30&shy;&deg;</p>", 
                                "<p>60&deg;</p>", "<p>45&deg;</p>"],
                    options_hi: ["<p>20&deg;</p>", "<p>30&shy;&deg;</p>",
                                "<p>60&deg;</p>", "<p>45&deg;</p>"],
                    solution_en: "<p>22.(b)<br><span style=\"font-family: Times New Roman;\">Going through option</span><br><span style=\"font-family: Times New Roman;\">Put the value of </span><span style=\"font-family: Times New Roman;\">= 30&deg;</span><br>3<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><msqrt><mn>3</mn></msqrt></mfrac></math> + 4<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msqrt><mn>3</mn></msqrt><mn>2</mn></mfrac></math> - 4<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math> = 0<br>2<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math> + 2<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math> - 4<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math> = 0<br><span style=\"font-family: Times New Roman;\">Hence Verified</span></p>",
                    solution_hi: "<p>22.(b)<br><span style=\"font-family: Baloo;\">सभी विकल्पों को जांचने के बाद</span><span style=\"font-family: Times New Roman;\">,</span><br><span style=\"font-family: Times New Roman;\">= 30&deg; </span><span style=\"font-family: Baloo;\">रखने पर,</span><br><span style=\"font-family: Baloo;\">3<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><msqrt><mn>3</mn></msqrt></mfrac></math> + 4<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msqrt><mn>3</mn></msqrt><mn>2</mn></mfrac></math> - 4<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math> = 0<br>2<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math> + 2<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math> - 4<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math> = 0</span><br><span style=\"font-family: Times New Roman;\">LHS = RHS</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "23",
                    section: "17",
                    question_en: "<p>23. <span style=\"font-family: Times New Roman;\">The value of&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>t</mi><mi>a</mi><mi>n</mi><mo>(</mo><mn>45</mn><mo>&#176;</mo><mo>-</mo><mi>&#945;</mi><mo>)</mo></mrow><mrow><mi>c</mi><mi>o</mi><mi>t</mi><mo>(</mo><mn>45</mn><mo>&#176;</mo><mo>+</mo><mi>&#945;</mi><mo>)</mo></mrow></mfrac></math> -&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mi>c</mi><mi>o</mi><mi>s</mi><mn>19</mn><mo>&#176;</mo><mo>+</mo><mi>s</mi><mi>i</mi><mi>n</mi><mn>71</mn><mo>&#176;</mo><mo>)</mo><mo>(</mo><mi>s</mi><mi>e</mi><mi>c</mi><mn>19</mn><mo>&#176;</mo><mo>+</mo><mi>c</mi><mi>o</mi><mi>s</mi><mi>e</mi><mi>c</mi><mn>71</mn><mo>&#176;</mo><mo>)</mo></mrow><mrow><mi>t</mi><mi>a</mi><mi>n</mi><mn>12</mn><mo>&#176;</mo><mo>.</mo><mi>t</mi><mi>a</mi><mi>n</mi><mn>24</mn><mo>&#176;</mo><mo>.</mo><mi>&#160;</mi><mi>t</mi><mi>a</mi><mi>n</mi><mn>66</mn><mo>&#176;</mo><mo>.</mo><mi>t</mi><mi>a</mi><mi>n</mi><mn>78</mn><mo>&#176;</mo></mrow></mfrac></math> </span><span style=\"font-family: Times New Roman;\">is:</span></p>",
                    question_hi: "<p>23. &nbsp;<span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>t</mi><mi>a</mi><mi>n</mi><mo>(</mo><mn>45</mn><mo>&#176;</mo><mo>-</mo><mi>&#945;</mi><mo>)</mo></mrow><mrow><mi>c</mi><mi>o</mi><mi>t</mi><mo>(</mo><mn>45</mn><mo>&#176;</mo><mo>+</mo><mi>&#945;</mi><mo>)</mo></mrow></mfrac></math> -&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mi>c</mi><mi>o</mi><mi>s</mi><mn>19</mn><mo>&#176;</mo><mo>+</mo><mi>s</mi><mi>i</mi><mi>n</mi><mn>71</mn><mo>&#176;</mo><mo>)</mo><mo>(</mo><mi>s</mi><mi>e</mi><mi>c</mi><mn>19</mn><mo>&#176;</mo><mo>+</mo><mi>c</mi><mi>o</mi><mi>s</mi><mi>e</mi><mi>c</mi><mn>71</mn><mo>&#176;</mo><mo>)</mo></mrow><mrow><mi>t</mi><mi>a</mi><mi>n</mi><mn>12</mn><mo>&#176;</mo><mo>.</mo><mi>t</mi><mi>a</mi><mi>n</mi><mn>24</mn><mo>&#176;</mo><mo>.</mo><mi>&#160;</mi><mi>t</mi><mi>a</mi><mi>n</mi><mn>66</mn><mo>&#176;</mo><mo>.</mo><mi>t</mi><mi>a</mi><mi>n</mi><mn>78</mn><mo>&#176;</mo></mrow></mfrac></math>&nbsp;</span><span style=\"font-family: Baloo;\">का</span><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Baloo;\">मान है:</span></p>",
                    options_en: ["<p>-3</p>", "<p>0</p>", 
                                "<p>-2</p>", "<p>2</p>"],
                    options_hi: ["<p>-3<span style=\"font-family: Times New Roman;\"> </span></p>", "<p>0<span style=\"font-family: Times New Roman;\"> </span></p>",
                                "<p>-2</p>", "<p>2</p>"],
                    solution_en: "<p>23.(a)<br>Let &alpha; = 0<br><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>t</mi><mi>a</mi><mi>n</mi><mo>(</mo><mn>45</mn><mo>&#176;</mo><mo>-</mo><mi>&#945;</mi><mo>)</mo></mrow><mrow><mi>c</mi><mi>o</mi><mi>t</mi><mo>(</mo><mn>45</mn><mo>&#176;</mo><mo>+</mo><mi>&#945;</mi><mo>)</mo></mrow></mfrac></math> -&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mi>c</mi><mi>o</mi><mi>s</mi><mn>19</mn><mo>&#176;</mo><mo>+</mo><mi>s</mi><mi>i</mi><mi>n</mi><mn>71</mn><mo>&#176;</mo><mo>)</mo><mo>(</mo><mi>s</mi><mi>e</mi><mi>c</mi><mn>19</mn><mo>&#176;</mo><mo>+</mo><mi>c</mi><mi>o</mi><mi>s</mi><mi>e</mi><mi>c</mi><mn>71</mn><mo>&#176;</mo><mo>)</mo></mrow><mrow><mi>t</mi><mi>a</mi><mi>n</mi><mn>12</mn><mo>&#176;</mo><mo>.</mo><mi>t</mi><mi>a</mi><mi>n</mi><mn>24</mn><mo>&#176;</mo><mo>.</mo><mi>&#160;</mi><mi>t</mi><mi>a</mi><mi>n</mi><mn>66</mn><mo>&#176;</mo><mo>.</mo><mi>t</mi><mi>a</mi><mi>n</mi><mn>78</mn><mo>&#176;</mo></mrow></mfrac></math>&nbsp;<br></span><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>t</mi><mi>a</mi><mi>n</mi><mo>(</mo><mn>45</mn><mo>&#176;</mo><mo>)</mo></mrow><mrow><mi>c</mi><mi>o</mi><mi>t</mi><mo>(</mo><mn>45</mn><mo>&#176;</mo><mo>)</mo></mrow></mfrac></math> -&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mi>c</mi><mi>o</mi><mi>s</mi><mn>19</mn><mo>&#176;</mo><mo>+</mo><mi>c</mi><mi>o</mi><mi>s</mi><mn>19</mn><mo>&#176;</mo><mo>)</mo><mo>(</mo><mi>s</mi><mi>e</mi><mi>c</mi><mn>19</mn><mo>&#176;</mo><mo>+</mo><mi>s</mi><mi>e</mi><mi>c</mi><mn>19</mn><mo>&#176;</mo><mo>)</mo></mrow><mrow><mi>t</mi><mi>a</mi><mi>n</mi><mn>12</mn><mo>&#176;</mo><mo>.</mo><mi>t</mi><mi>a</mi><mi>n</mi><mn>24</mn><mo>&#176;</mo><mo>.</mo><mi>&#160;</mi><mi>c</mi><mi>o</mi><mi>t</mi><mn>24</mn><mo>&#176;</mo><mi>c</mi><mi>o</mi><mi>t</mi><mn>12</mn><mo>&#176;</mo></mrow></mfrac></math><br>1 -&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mi>c</mi><mi>o</mi><mi>s</mi><mn>19</mn><mo>&#176;</mo><mo>.</mo><mn>2</mn><mi>s</mi><mi>e</mi><mi>c</mi><mn>19</mn><mo>&#176;</mo><mi>&#160;</mi></mrow><mn>1</mn></mfrac></math>&nbsp;<br></span><span style=\"font-family: Times New Roman;\">&rarr; 1 -&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mo>&#215;</mo><mn>2</mn></mrow><mn>1</mn></mfrac></math> = 1 - 4 = -3</span></p>",
                    solution_hi: "<p>23.(a)<br><span style=\"font-family: Baloo;\">माना की &alpha; = 0<br><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>t</mi><mi>a</mi><mi>n</mi><mo>(</mo><mn>45</mn><mo>&#176;</mo><mo>-</mo><mi>&#945;</mi><mo>)</mo></mrow><mrow><mi>c</mi><mi>o</mi><mi>t</mi><mo>(</mo><mn>45</mn><mo>&#176;</mo><mo>+</mo><mi>&#945;</mi><mo>)</mo></mrow></mfrac></math> -&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mi>c</mi><mi>o</mi><mi>s</mi><mn>19</mn><mo>&#176;</mo><mo>+</mo><mi>s</mi><mi>i</mi><mi>n</mi><mn>71</mn><mo>&#176;</mo><mo>)</mo><mo>(</mo><mi>s</mi><mi>e</mi><mi>c</mi><mn>19</mn><mo>&#176;</mo><mo>+</mo><mi>c</mi><mi>o</mi><mi>s</mi><mi>e</mi><mi>c</mi><mn>71</mn><mo>&#176;</mo><mo>)</mo></mrow><mrow><mi>t</mi><mi>a</mi><mi>n</mi><mn>12</mn><mo>&#176;</mo><mo>.</mo><mi>t</mi><mi>a</mi><mi>n</mi><mn>24</mn><mo>&#176;</mo><mo>.</mo><mi>&#160;</mi><mi>t</mi><mi>a</mi><mi>n</mi><mn>66</mn><mo>&#176;</mo><mo>.</mo><mi>t</mi><mi>a</mi><mi>n</mi><mn>78</mn><mo>&#176;</mo></mrow></mfrac></math>&nbsp;<br></span><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>t</mi><mi>a</mi><mi>n</mi><mo>(</mo><mn>45</mn><mo>&#176;</mo><mo>)</mo></mrow><mrow><mi>c</mi><mi>o</mi><mi>t</mi><mo>(</mo><mn>45</mn><mo>&#176;</mo><mo>)</mo></mrow></mfrac></math> -&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mi>c</mi><mi>o</mi><mi>s</mi><mn>19</mn><mo>&#176;</mo><mo>+</mo><mi>c</mi><mi>o</mi><mi>s</mi><mn>19</mn><mo>&#176;</mo><mo>)</mo><mo>(</mo><mi>s</mi><mi>e</mi><mi>c</mi><mn>19</mn><mo>&#176;</mo><mo>+</mo><mi>s</mi><mi>e</mi><mi>c</mi><mn>19</mn><mo>&#176;</mo><mo>)</mo></mrow><mrow><mi>t</mi><mi>a</mi><mi>n</mi><mn>12</mn><mo>&#176;</mo><mo>.</mo><mi>t</mi><mi>a</mi><mi>n</mi><mn>24</mn><mo>&#176;</mo><mo>.</mo><mi>&#160;</mi><mi>c</mi><mi>o</mi><mi>t</mi><mn>24</mn><mo>&#176;</mo><mi>c</mi><mi>o</mi><mi>t</mi><mn>12</mn><mo>&#176;</mo></mrow></mfrac></math><br>1 -&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mi>c</mi><mi>o</mi><mi>s</mi><mn>19</mn><mo>&#176;</mo><mo>.</mo><mn>2</mn><mi>s</mi><mi>e</mi><mi>c</mi><mn>19</mn><mo>&#176;</mo><mi>&#160;</mi></mrow><mn>1</mn></mfrac></math>&nbsp;<br></span><span style=\"font-family: Times New Roman;\">&rarr; 1 -&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mo>&#215;</mo><mn>2</mn></mrow><mn>1</mn></mfrac></math> = 1 - 4 = -3</span></span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "24",
                    section: "17",
                    question_en: "<p>24. <span style=\"font-family: Times New Roman;\">A man and a woman, working together can do a work in 66 days. The ratio of their working efficiencies is 3 : 2. In how many days 6 men and 2 women working together can do the same work?</span></p>",
                    question_hi: "<p>24. <span style=\"font-family: Baloo;\">एक पुरुष और एक महिला एक साथ काम करते हुए 66 दिनों में एक काम कर सकते हैं। उनकी कार्य क्षमता का अनुपात 3 : 2 है। 6 पुरुष और 2 महिलाएं एक साथ कार्य करते हुए समान कार्य को कितने दिनों में कर सकते हैं?</span></p>",
                    options_en: ["<p>18</p>", "<p>15</p>", 
                                "<p>14</p>", "<p>12</p>"],
                    options_hi: ["<p>18</p>", "<p>15</p>",
                                "<p>14</p>", "<p>12</p>"],
                    solution_en: "<p>24.(b)<br><span style=\"font-family: Times New Roman;\">Total work = time &times; total efficiency = 66 &times; 5 = 330</span><br><span style=\"font-family: Times New Roman;\">6 men and 2 women efficiency = 6 &times; 3 + 2 &times; 2 = 22</span><br><span style=\"font-family: Times New Roman;\">Time required =</span><span style=\"font-family: Times New Roman;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>330</mn><mn>22</mn></mfrac></math> </span><span style=\"font-family: Times New Roman;\">= 15</span></p>",
                    solution_hi: "<p>24.(b)<br><span style=\"font-family: Baloo;\">कुल कार्य = समय &times; कुल दक्षता </span><span style=\"font-family: Times New Roman;\">= 66 &times; 5 = 330</span><br><span style=\"font-family: Times New Roman;\">6 </span><span style=\"font-family: Baloo;\">पुरुष और </span><span style=\"font-family: Times New Roman;\">2 </span><span style=\"font-family: Baloo;\">महिला क्षमता</span><span style=\"font-family: Times New Roman;\"> = 6 &times; 3 + 2 &times; 2 </span><span style=\"font-family: \'Times New Roman\';\">= 22</span><br><span style=\"font-family: Baloo;\">आवश्यक समय</span><span style=\"font-family: Times New Roman;\"> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>330</mn><mn>22</mn></mfrac></math></span><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Times New Roman;\">= 15</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "25",
                    section: "misc",
                    question_en: "<p>25.<span style=\"font-family: Times New Roman;\"> A shopkeeper marks his goods 30% higher than the cost price and allows a discount of 10% on the marked price. In order to earn 6.5% more profit, what discount percent should he allow on the marked price?</span></p>",
                    question_hi: "<p>25.<span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Baloo;\">एक दुकानदार अपने माल पर क्रय मूल्य से 30% अधिक अंकित करता है और अंकित मूल्य पर 10% की छूट देता है। 6.5% अधिक लाभ अर्जित करने के लिए उसे अंकित मूल्य पर कितने प्रतिशत की छूट देनी चाहिए?</span></p>",
                    options_en: ["<p>6</p>", "<p>5.5<span style=\"font-family: Times New Roman;\"> </span></p>", 
                                "<p>4</p>", "<p>5</p>"],
                    options_hi: ["<p>6</p>", "<p>5.5</p>",
                                "<p>4</p>", "<p>5</p>"],
                    solution_en: "<p>25.(d)<br><span style=\"font-family: Times New Roman;\">Let the CP = 100</span><br><span style=\"font-family: Times New Roman;\">Then MP = 130</span><br><span style=\"font-family: Times New Roman;\">After first discount = 117</span><br><span style=\"font-family: Times New Roman;\">If 6.5% Profit more then SP = 117 + 6.5 = 123.5</span><br><span style=\"font-family: Times New Roman;\">Now discount = 6.5</span><br><span style=\"font-family: Times New Roman;\">Discount percentage =</span><span style=\"font-family: Times New Roman;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>6</mn><mo>.</mo><mn>5</mn></mrow><mn>130</mn></mfrac></math> &times; 100 </span><span style=\"font-family: Times New Roman;\">= 5%</span></p>",
                    solution_hi: "<p>25.(d)<br><span style=\"font-family: Baloo;\">माना क्रय मूल्य</span><span style=\"font-family: Times New Roman;\"> = 100</span><br><span style=\"font-family: Baloo;\">फिर अंकित मूल्य</span><span style=\"font-family: Times New Roman;\"> = 130</span><br><span style=\"font-family: Baloo;\">पहली छूट के बाद </span><span style=\"font-family: Times New Roman;\">= 117</span><br><span style=\"font-family: Baloo;\">यदि </span><span style=\"font-family: Times New Roman;\">6.5%</span><span style=\"font-family: Baloo;\"> लाभ अधिक है तो विक्रय मूल्य</span><span style=\"font-family: Times New Roman;\"> = 117 + 6.5 = 123.5</span><br><span style=\"font-family: Baloo;\">अब छूट</span><span style=\"font-family: Times New Roman;\"> = 6.5</span><br><span style=\"font-family: Baloo;\">छूट प्रतिशत </span><span style=\"font-family: Times New Roman;\">=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>6</mn><mo>.</mo><mn>5</mn></mrow><mn>130</mn></mfrac></math> &times; 100 = 5%</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML - more compact
            let html = `
                <div class="card mb-3">
                    <div class="card-body">
                        <div class="question-text mb-3">
                            <div class="en">${question.question_en}</div>
                            <div class="hi" style="display:none">${question.question_hi}</div>
                        </div>
                        <div class="options">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = 'option-label';
                // When reviewing, highlight user's choice and correct answer
                if (submitted) {
                    if (opt === question.correct) {
                        optionClass += ' border-success bg-success bg-opacity-10';
                    }
                    if (answers[question.id] === opt && opt !== question.correct) {
                        optionClass += ' border-danger bg-danger bg-opacity-10';
                    }
                }
                html += `
                    <div class="form-check mb-2">
                        <input class="form-check-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${answers[question.id] === opt ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                        <label class="form-check-label w-100 ${optionClass}" for="opt-${index}-${opt}">
                            ${submitted && opt === question.correct ? '<i class="fas fa-check-circle text-success me-1"></i>' : ''}
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const alertClass = isCorrect ? 'alert-success' : 'alert-danger';
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="alert ${alertClass} mt-3 py-2">
                        <strong>${resultText}</strong>
                        <div class="mt-1 small">
                            <strong>Solution:</strong>
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                document.getElementById(`box-${index}`).classList.add('attempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { if (currentQuestion > 0) showQuestion(currentQuestion - 1); }
        function nextQuestion() { if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
            // Update button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const content = document.getElementById('main-content');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                if (window.innerWidth >= 992) {
                    content.classList.add('with-nav');
                }
            } else {
                nav.classList.remove('show');
                if (window.innerWidth >= 992) {
                    content.classList.remove('with-nav');
                }
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer - compact
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            if (confirm(`You have attempted ${attempted} of ${questions.length} questions.
${remaining} questions are unattempted.
Are you sure you want to submit?`)) {
                submitTest();
            }
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('bg-secondary', 'text-white');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            resultsModal.show();
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.classList.replace('btn-light', 'btn-success');
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            resultsModal.hide();
            showQuestion(0);
        }
        // Add event listener for window resize
        window.addEventListener('resize', function() {
            if (window.innerWidth >= 992) {
                if (document.getElementById('question-nav').classList.contains('show')) {
                    document.getElementById('main-content').classList.add('with-nav');
                }
            } else {
                document.getElementById('main-content').classList.remove('with-nav');
            }
        });
    </script>
</body>
</html>