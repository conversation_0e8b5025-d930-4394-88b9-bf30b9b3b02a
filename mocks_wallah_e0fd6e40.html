<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">15:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 25</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">25</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 15 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["6"] = {
                name: "Reasoning",
                start: 0,
                end: 23
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="6">Reasoning</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 24,
                end: 24
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "6",
                    question_en: " <p>1. Which of the following numbers will replace the question mark (?) in the given series?</p> <p>17, 20, 60, 64, 256, 261, 1305,?</p>",
                    question_hi: " <p>1. </span><span style=\"font-family:Palanquin\">निम्नलिखित में से कौन सी संख्या दी गई श्रृंखला में प्रश्न चिह्न (?) को प्रतिस्थापित करेगी ?</span></p> <p><span style=\"font-family:Roboto\">17, 20 , 60 , 64 , 256 , 261 , 1305 , ?</span></p>",
                    options_en: [" <p> 1346</p>", " <p> 1361</p>", 
                                " <p> 1284</p>", " <p> 1311</p>"],
                    options_hi: [" <p> 1346    </span></p>", " <p> 1361    </span></p>",
                                " <p> 1284    </span></p>", " <p> 1311</span></p>"],
                    solution_en: " <p>1.(d)</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669441637/word/media/image2.png\"/></p>",
                    solution_hi: " <p>1.(d)</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669441637/word/media/image2.png\"/></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "6",
                    question_en: "<p>2.<span style=\"font-family: Roboto;\"> Select the option that represents the correct order of the given words as they would appear in an English dictionary.</span></p>\r\n<p><span style=\"font-family: Roboto;\">1.Beginner 2.Beheld 3.Beggar 4.Belief 5.Behind</span></p>\n",
                    question_hi: "<p>2. <span style=\"font-family: Palanquin;\">&#2313;&#2360; &#2357;&#2367;&#2325;&#2354;&#2381;&#2346; &#2325;&#2366; &#2330;&#2351;&#2344; &#2325;&#2352;&#2375;&#2306; &#2332;&#2379; &#2342;&#2367;&#2319; &#2327;&#2319; &#2358;&#2348;&#2381;&#2342;&#2379;&#2306; &#2325;&#2375; &#2360;&#2361;&#2368; &#2325;&#2381;&#2352;&#2350; &#2325;&#2379; &#2342;&#2352;&#2381;&#2358;&#2366;&#2340;&#2366; &#2361;&#2376;, &#2332;&#2376;&#2360;&#2366; &#2325;&#2367; &#2357;&#2375; &#2319;&#2325; &#2309;&#2306;&#2327;&#2381;&#2352;&#2375;&#2332;&#2368; &#2358;&#2348;&#2381;&#2342;&#2325;&#2379;&#2358; &#2350;&#2375;&#2306; &#2342;&#2367;&#2326;&#2366;&#2312; &#2342;&#2375;&#2340;&#2375; &#2361;&#2376;&#2306;&#2404;</span></p>\r\n<p><span style=\"font-family: Roboto;\">1.Beginner 2.Beheld 3.Beggar</span><span style=\"font-family: Roboto;\"> 4.Belief 5.Behind</span></p>\n",
                    options_en: ["<p>1, 3, 2, 5, 4</p>\n", "<p>3, 2, 1, 5, 4</p>\n", 
                                "<p>3, 1, 2, 5, 4</p>\n", "<p>1, 3, 5, 2, 4</p>\n"],
                    options_hi: ["<p>1, 3, 2, 5, 4</p>\n", "<p>3, 2, 1, 5, 4</p>\n",
                                "<p>3, 1, 2, 5, 4</p>\n", "<p>1, 3, 5, 2, 4</p>\n"],
                    solution_en: "<p>2.(c)</p>\r\n<p><span style=\"font-family: Roboto;\">Correct order will be :</span></p>\r\n<p><span style=\"font-family: Roboto;\">Beggar &rarr;</span><span style=\"font-family: Roboto;\"> Beginner &rarr;</span><span style=\"font-family: Roboto;\"> Beheld &rarr;</span><span style=\"font-family: Roboto;\"> Behind &rarr;</span><span style=\"font-family: Roboto;\"> Belief </span></p>\n",
                    solution_hi: "<p>2.(c) <span style=\"font-family: Palanquin;\">&#2360;&#2361;&#2368; &#2325;&#2381;&#2352;&#2350; &#2361;&#2379;&#2327;&#2366;:- </span></p>\r\n<p><span style=\"font-family: Palanquin;\">Beggar <span style=\"font-family: Roboto;\">&rarr;</span></span><span style=\"font-family: Roboto;\"> Beginner &rarr;</span><span style=\"font-family: Roboto;\"> Beheld &rarr;</span><span style=\"font-family: Roboto;\"> Behind &rarr;</span><span style=\"font-family: Roboto;\"> Belief </span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "6",
                    question_en: " <p>3.</span><span style=\"font-family:Roboto\"> Seven friends P, Q, R, S, T, U and V, each has a different age. P is older than only U and R. R is older than U. Q is older than S but younger than T. T is not the oldest among all. The age of how many persons is between the ages of Q and R?</span></p>",
                    question_hi: " <p>3.</span><span style=\"font-family:Palanquin\">सात मित्र P, Q, R, S, T, U और V, प्रत्येक की आयु अन्य से भिन्न है। P केवल U और R से बड़ा है। R, U से बड़ा है। Q, S से बड़ा है लेकिन T से छोटा है। T सभी में सबसे बड़ा नहीं है। कितने लोगों की आयु Q और R की आयु के मध्य है ?</span></p>",
                    options_en: [" <p> 0</span></p>", " <p> 2</span></p>", 
                                " <p> 3</span></p>", " <p> 1</span></p>"],
                    options_hi: [" <p> 0</span></p>", " <p> 2</span></p>",
                                " <p> 3</span></p>", " <p> 1</span></p>"],
                    solution_en: " <p>3.(b)</span></p> <p><span style=\"font-family:Roboto\">After arranging the persons as per directions given in question, we get following arrangement : </span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669441637/word/media/image25.png\"/></p> <p><span style=\"font-family:Roboto\">Clearly, we can see that the number of persons whose ages lie between Q and R are 2.</span></p>",
                    solution_hi: " <p>3.(b)</span></p> <p><span style=\"font-family:Palanquin\">व्यक्तियों को प्रश्न में दिए गए निर्देशों के अनुसार व्यवस्थित करने के बाद, हमें निम्नलिखित व्यवस्था मिलती है:</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669441637/word/media/image25.png\"/></p> <p><span style=\"font-family:Palanquin\">स्पष्ट रूप से, हम देख सकते हैं कि Q और R के बीच आयु वाले व्यक्तियों की संख्या 2 है।</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "6",
                    question_en: "<p>4. <span style=\"font-family: Palanquin;\">In a certain code language, \'Read the code\' is coded as \'18 20 3\', \'Decode the code\' is coded as \'4 20 3\' and \'code is easy\' is coded as \'3 9 5\'. How will \'make one code\' be coded in that language?</span></p>\n",
                    question_hi: "<p>4.<span style=\"font-family: Palanquin;\">&#2319;&#2325; &#2344;&#2367;&#2358;&#2381;&#2330;&#2367;&#2340; &#2325;&#2370;&#2335; &#2349;&#2366;&#2359;&#2366; &#2350;&#2375;&#2306;, \'Read the code\' &#2325;&#2379; \'18 20 3\' &#2325;&#2375; &#2352;&#2370;&#2346; &#2350;&#2375;&#2306; &#2325;&#2370;&#2335;&#2348;&#2342;&#2381;&#2343; &#2325;&#2367;&#2351;&#2366; &#2332;&#2366;&#2340;&#2366; &#2361;&#2376;, \'Decode the code\' &#2325;&#2379; \'4 20 3\' &#2325;&#2375; &#2352;&#2370;&#2346; &#2350;&#2375;&#2306; &#2325;&#2370;&#2335;&#2348;&#2342;&#2381;&#2343; &#2325;&#2367;&#2351;&#2366; &#2332;&#2366;&#2340;&#2366; &#2361;&#2376; &#2324;&#2352; \'code is easy\' &#2325;&#2379; \'3 9 5\' &#2325;&#2375; &#2352;&#2370;&#2346; &#2350;&#2375;&#2306; &#2325;&#2370;&#2335;&#2348;&#2342;&#2381;&#2343; &#2325;&#2367;&#2351;&#2366; &#2332;&#2366;&#2340;&#2366; &#2361;&#2376;&#2404; &#2313;&#2360;&#2368; &#2349;&#2366;&#2359;&#2366; &#2350;&#2375;&#2306; \'make one code\' &#2325;&#2379; &#2325;&#2367;&#2360; &#2346;&#2381;&#2352;&#2325;&#2366;&#2352; &#2325;&#2379;&#2337;&#2367;&#2340; &#2325;&#2367;&#2351;&#2366; &#2332;&#2366;&#2319;&#2327;&#2366; ?</span></p>\n",
                    options_en: ["<p>19 18 5</p>\n", "<p>12 13 15</p>\n", 
                                "<p>13 15 3</p>\n", "<p>1 2 5</p>\n"],
                    options_hi: ["<p>19 18 5</p>\n", "<p>12 13 15</p>\n",
                                "<p>13 15 3</p>\n", "<p>1 2 5</p>\n"],
                    solution_en: "<p>4.(c) <span style=\"font-family: Roboto;\">Tricky approach : </span></p>\r\n<p>Read the code &nbsp;&rarr; R ,&nbsp; T&nbsp; ,&nbsp; C &nbsp;&rarr;&nbsp;18 , 20 , 3</p>\r\n<p>We can clearly see&nbsp; that words are coded as place value of first letter of the word&nbsp;</p>\r\n<p>therefore, \'make one code\'&nbsp; will be coded as &nbsp;M &rarr;13, O&rarr; 15, C &rarr; 3 .</p>\n",
                    solution_hi: "<p>4.(c) <span style=\"font-family: Roboto;\">Tricky approach : </span></p>\r\n<p>Read the code &nbsp;&rarr; R ,&nbsp; T&nbsp; ,&nbsp; C &nbsp;&rarr;&nbsp;18 , 20 , 3</p>\r\n<p>&#2361;&#2350; &#2360;&#2381;&#2346;&#2359;&#2381;&#2335; &#2352;&#2370;&#2346; &#2360;&#2375; &#2342;&#2375;&#2326; &#2360;&#2325;&#2340;&#2375; &#2361;&#2376;&#2306; &#2325;&#2367; &#2358;&#2348;&#2381;&#2342;&#2379;&#2306; &#2325;&#2379; &#2358;&#2348;&#2381;&#2342; &#2325;&#2375; &#2346;&#2361;&#2354;&#2375; &#2309;&#2325;&#2381;&#2359;&#2352; &#2325;&#2375; &#2360;&#2381;&#2341;&#2366;&#2344;&#2368;&#2351; &#2350;&#2366;&#2344; &#2325;&#2375; &#2352;&#2370;&#2346; &#2350;&#2375;&#2306; &#2325;&#2379;&#2337;&#2367;&#2340; &#2325;&#2367;&#2351;&#2366; &#2327;&#2351;&#2366; &#2361;&#2376;</p>\r\n<p>&#2311;&#2360;&#2354;&#2367;&#2319;, \'make one code\' &nbsp;&#2325;&#2379; M &rarr;13, O&rarr; 15, C &rarr; 3 &#2325;&#2375; &#2352;&#2370;&#2346; &#2350;&#2375;&#2306; &#2325;&#2379;&#2337;&#2367;&#2340; &#2325;&#2367;&#2351;&#2366; &#2332;&#2366;&#2319;&#2327;&#2366;</p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "6",
                    question_en: " <p>5. </span><span style=\"font-family:Palanquin\">Select the correct mirror image of the given combination when the mirror is placed at MN as shown.</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669441637/word/media/image31.png\"/></p>",
                    question_hi: " <p>5.</span><span style=\"font-family:Palanquin\"> जब दर्पण को MN पर रखा जाता है जैसा कि दिखाया गया है तो दिए गए संयोजन की सही दर्पण छवि का चयन करें ।</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669441637/word/media/image31.png\"/></p>",
                    options_en: [" <p> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669441637/word/media/image21.png\"/></p>", " <p> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669441637/word/media/image42.png\"/></p>", 
                                " <p> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669441637/word/media/image33.png\"/></p>", " <p> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669441637/word/media/image24.png\"/></p>"],
                    options_hi: [" <p>  </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669441637/word/media/image21.png\"/></p>", " <p> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669441637/word/media/image42.png\"/></p>",
                                " <p> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669441637/word/media/image33.png\"/></p>", " <p> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669441637/word/media/image24.png\"/></p>"],
                    solution_en: " <p>5.(c)</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669441637/word/media/image33.png\"/></p>",
                    solution_hi: " <p>5.(c)</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669441637/word/media/image33.png\"/></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "6",
                    question_en: " <p>6. </span><span style=\"font-family:Roboto\">Select the Venn diagram that best represents the relationship between the following classes.</span></p> <p><span style=\"font-family:Roboto\">Doctors, Fathers, Mothers</span></p>",
                    question_hi: " <p>6. </span><span style=\"font-family:Palanquin\">वेन आरेख का चयन करें जो निम्नलिखित वर्गों के बीच संबंध को सर्वोत्तम रूप से दर्शाता है।</span></p> <p><span style=\"font-family:Palanquin\">डॉक्टर, पिता, माता</span></p>",
                    options_en: [" <p> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669441637/word/media/image32.png\"/></p>", " <p> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669441637/word/media/image36.png\"/></p>", 
                                " <p> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669441637/word/media/image10.png\"/></p>", " <p> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669441637/word/media/image37.png\"/></p>"],
                    options_hi: [" <p> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669441637/word/media/image32.png\"/></p>", " <p> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669441637/word/media/image36.png\"/></p>",
                                " <p> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669441637/word/media/image10.png\"/><span style=\"font-family:Roboto\">   </span></p>", " <p> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669441637/word/media/image37.png\"/></p>"],
                    solution_en: " <p>6.(b)</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669441637/word/media/image13.png\"/></p>",
                    solution_hi: " <p>6.(b)</span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669441637/word/media/image13.png\"/></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "6",
                    question_en: "<p>7.<span style=\"font-family: Roboto;\"> Select the correct combination of mathematical signs to replace the * signs and to balance the given equation.</span></p>\r\n<p><span style=\"font-family: Roboto;\">78*43*2*56*27*4</span></p>\n",
                    question_hi: "<p>7.<span style=\"font-family: Palanquin;\"> * &#2330;&#2367;&#2361;&#2381;&#2344;&#2379;&#2306; &#2325;&#2379; &#2346;&#2381;&#2352;&#2340;&#2367;&#2360;&#2381;&#2341;&#2366;&#2346;&#2367;&#2340; &#2325;&#2352;&#2344;&#2375; &#2324;&#2352; &#2342;&#2367;&#2319; &#2327;&#2319; &#2360;&#2350;&#2368;&#2325;&#2352;&#2339; &#2325;&#2379; &#2360;&#2306;&#2340;&#2369;&#2354;&#2367;&#2340; &#2325;&#2352;&#2344;&#2375; &#2325;&#2375; &#2354;&#2367;&#2319; &#2327;&#2339;&#2367;&#2340;&#2368;&#2351; &#2330;&#2367;&#2361;&#2381;&#2344;&#2379;&#2306; &#2325;&#2375; &#2360;&#2361;&#2368; &#2360;&#2306;&#2351;&#2379;&#2332;&#2344; &#2325;&#2366; &#2330;&#2351;&#2344; &#2325;&#2352;&#2375;&#2306;&#2404;</span></p>\r\n<p><span style=\"font-family: Roboto;\">78*43*2*56*27*4</span></p>\n",
                    options_en: ["<p>+,=,+,&times;,&minus;</p>\n", "<p>&minus;,&divide;,=,+,&times;</p>\n", 
                                "<p>&minus;,&times;,=,+,&times;</p>\n", "<p>+,&times;,&minus;,=,&times;</p>\n"],
                    options_hi: ["<p>+,=,+,&times;,&minus;</p>\n", "<p>&minus;,&divide;,=,+,&times;</p>\n",
                                "<p>&minus;,&times;,=,+,&times;</p>\n", "<p>+,&times;,&minus;,=,&times;</p>\n"],
                    solution_en: "<p>7.(d) <span style=\"font-family: Nova Mono;\">In this type of question, we will check by putting options one by one and doing so option (d) gets satisfied.</span></p>\r\n<p><span style=\"font-family: Nova Mono;\">78*43*2*56*27*4</span></p>\r\n<p><span style=\"font-family: Nova Mono;\">Putting the values of option (d) in above expression, we get</span></p>\r\n<p><span style=\"font-family: Nova Mono;\">78 + 43 &times;</span><span style=\"font-family: Nova Mono;\"> 2 - 56 = 27 &times;</span><span style=\"font-family: Nova Mono;\">&nbsp;4</span></p>\r\n<p><span style=\"font-family: Nova Mono;\">LHS </span></p>\r\n<p><span style=\"font-family: Nova Mono;\">= 78 + 43 &times;</span><span style=\"font-family: Nova Mono;\"> 2 - 56</span></p>\r\n<p><span style=\"font-family: Nova Mono;\">= 78 + 86 - 56</span></p>\r\n<p><span style=\"font-family: Nova Mono;\">= 164 - 56</span></p>\r\n<p><span style=\"font-family: Nova Mono;\">= 108</span></p>\r\n<p><span style=\"font-family: Nova Mono;\">RHS = 27 &times; </span><span style=\"font-family: Nova Mono;\">4 = 108</span></p>\r\n<p><span style=\"font-family: Nova Mono;\">So, LHS = RHS </span></p>\n",
                    solution_hi: "<p>7.(d)</p>\r\n<p><span style=\"font-family: Palanquin;\">&#2311;&#2360; &#2346;&#2381;&#2352;&#2325;&#2366;&#2352; &#2325;&#2375; &#2346;&#2381;&#2352;&#2358;&#2381;&#2344; &#2350;&#2375;&#2306; &#2361;&#2350; &#2319;&#2325;-&#2319;&#2325; &#2325;&#2352;&#2325;&#2375; &#2357;&#2367;&#2325;&#2354;&#2381;&#2346; &#2352;&#2326;&#2325;&#2352; &#2332;&#2366;&#2305;&#2330; &#2325;&#2352;&#2375;&#2306;&#2327;&#2375; &#2324;&#2352; &#2320;&#2360;&#2366; &#2325;&#2352;&#2344;&#2375; &#2360;&#2375; &#2357;&#2367;&#2325;&#2354;&#2381;&#2346; (d) &#2360;&#2306;&#2340;&#2369;&#2359;&#2381;&#2335; &#2361;&#2379; &#2332;&#2366;&#2340;&#2366; &#2361;&#2376;&#2404;</span></p>\r\n<p><span style=\"font-family: Roboto;\">78*43*2*56*27*4</span></p>\r\n<p><span style=\"font-family: Palanquin;\">&#2313;&#2346;&#2352;&#2379;&#2325;&#2381;&#2340; &#2357;&#2381;&#2351;&#2306;&#2332;&#2325; &#2350;&#2375;&#2306; &#2357;&#2367;&#2325;&#2354;&#2381;&#2346; (d) &#2325;&#2375; &#2350;&#2366;&#2344;&#2379;&#2306; &#2325;&#2379; &#2352;&#2326;&#2344;&#2375; &#2346;&#2352;, &#2361;&#2350;&#2375;&#2306; &#2346;&#2381;&#2352;&#2366;&#2346;&#2381;&#2340; &#2361;&#2379;&#2340;&#2366; &#2361;&#2376;</span></p>\r\n<p><span style=\"font-family: Roboto;\">78 + 43 <span style=\"font-family: Nova Mono;\">&times;</span>&nbsp;</span><span style=\"font-family: Roboto;\"> 2 - 56 = 27 <span style=\"font-family: Nova Mono;\">&times;</span></span><span style=\"font-family: Roboto;\">&nbsp;4</span></p>\r\n<p><span style=\"font-family: Roboto;\">LHS </span></p>\r\n<p><span style=\"font-family: Roboto;\">= 78 + 43 <span style=\"font-family: Nova Mono;\">&times;&nbsp;</span></span><span style=\"font-family: Roboto;\"> 2 - 56 = 78 + 86 - 56</span></p>\r\n<p><span style=\"font-family: Roboto;\">= 164 - 56 = 108</span></p>\r\n<p><span style=\"font-family: Roboto;\">RHS = 27 <span style=\"font-family: Nova Mono;\">&times;</span></span><span style=\"font-family: Roboto;\"> 4 = 108</span></p>\r\n<p><span style=\"font-family: Palanquin;\">&#2311;&#2360;&#2354;&#2367;&#2319;, LHS = RHS </span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "6",
                    question_en: "<p>8.<span style=\"font-family: Palanquin;\"> Select the correct combination of mathematical signs to replace the * signs and to balance the given equation.</span></p>\r\n<p><span style=\"font-family: Palanquin;\">230 * 1 * 5 * 16 * 41 * 21</span></p>\n",
                    question_hi: "<p>8.<span style=\"font-family: Palanquin;\"> * &#2330;&#2367;&#2361;&#2381;&#2344;&#2379;&#2306; &#2325;&#2379; &#2346;&#2381;&#2352;&#2340;&#2367;&#2360;&#2381;&#2341;&#2366;&#2346;&#2367;&#2340; &#2325;&#2352;&#2344;&#2375; &#2324;&#2352; &#2342;&#2367;&#2319; &#2327;&#2319; &#2360;&#2350;&#2368;&#2325;&#2352;&#2339; &#2325;&#2379; &#2360;&#2306;&#2340;&#2369;&#2354;&#2367;&#2340; &#2325;&#2352;&#2344;&#2375; &#2325;&#2375; &#2354;&#2367;&#2319; &#2327;&#2339;&#2367;&#2340;&#2368;&#2351; &#2330;&#2367;&#2361;&#2381;&#2344;&#2379;&#2306; &#2325;&#2375; &#2360;&#2361;&#2368; &#2360;&#2306;&#2351;&#2379;&#2332;&#2344; &#2325;&#2366; &#2330;&#2351;&#2344; &#2325;&#2352;&#2375;&#2306;&#2404;</span></p>\r\n<p><span style=\"font-family: Roboto;\">230 * 1 * 5 * 16 * 41 * 21</span></p>\n",
                    options_en: ["<p>&times;, &divide;, +, &ndash;, =</p>\n", "<p>+, &divide;, &times;, &ndash;, =</p>\n", 
                                "<p>&ndash;, +, &divide;, &times;, =</p>\n", "<p>&divide;, +, &ndash;, &times;, =</p>\n"],
                    options_hi: ["<p>&times;, &divide;, +, &ndash;, =</p>\n", "<p>+, &divide;, &times;, &ndash;, =</p>\n",
                                "<p>&ndash;, +, &divide;, &times;, =</p>\n", "<p>&divide;, +, &ndash;, &times;, =</p>\n"],
                    solution_en: "<p>&nbsp;</p>\r\n<p>8.(a)</p>\r\n<p><span style=\"font-family: Roboto;\">In this type of question, we will check by putting options one by one and doing so option (a) gets satisfied.</span></p>\r\n<p><span style=\"font-family: Roboto;\">230*1*5*16*41*21</span></p>\r\n<p><span style=\"font-family: Roboto;\">Putting the values of option (a) in above expression, we get </span></p>\r\n<p><span style=\"font-family: Roboto;\">230 &times;</span><span style=\"font-family: Roboto;\">1<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&divide;</mo></math> </span><span style=\"font-family: Roboto;\">5 + 16 - 41 = 21</span></p>\r\n<p><span style=\"font-family: Roboto;\">LHS </span></p>\r\n<p><span style=\"font-family: Roboto;\">= 230 &times; </span><span style=\"font-family: Roboto;\">1&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&divide;</mo></math></span><span style=\"font-family: Roboto;\"> 5 + 16 - 41</span></p>\r\n<p><span style=\"font-family: Roboto;\">= 230 &times;</span><span style=\"font-family: Roboto;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>5</mn></mfrac></math> +&nbsp;16 - 41</span></p>\r\n<p><span style=\"font-family: Roboto;\">= 46 + 16 - 41</span></p>\r\n<p><span style=\"font-family: Roboto;\">= 62 - 41</span></p>\r\n<p><span style=\"font-family: Roboto;\">= 21 = RHS</span></p>\n",
                    solution_hi: "<p>8.(a)</p>\r\n<p><span style=\"font-family: Palanquin;\">&#2311;&#2360; &#2346;&#2381;&#2352;&#2325;&#2366;&#2352; &#2325;&#2375; &#2346;&#2381;&#2352;&#2358;&#2381;&#2344; &#2350;&#2375;&#2306; &#2361;&#2350; &#2319;&#2325;-&#2319;&#2325; &#2325;&#2352;&#2325;&#2375; &#2357;&#2367;&#2325;&#2354;&#2381;&#2346; &#2352;&#2326;&#2325;&#2352; &#2332;&#2366;&#2306;&#2330; &#2325;&#2352;&#2375;&#2306;&#2327;&#2375; &#2324;&#2352; &#2320;&#2360;&#2366; &#2325;&#2352;&#2344;&#2375; &#2360;&#2375; &#2357;&#2367;&#2325;&#2354;&#2381;&#2346; (a) &#2360;&#2306;&#2340;&#2369;&#2359;&#2381;&#2335; &#2361;&#2379; &#2332;&#2366;&#2340;&#2366; &#2361;&#2376;&#2404;</span></p>\r\n<p><span style=\"font-family: Roboto;\">230*1*5*16*41*21</span></p>\r\n<p><span style=\"font-family: Palanquin;\">&#2313;&#2346;&#2352;&#2379;&#2325;&#2381;&#2340; &#2357;&#2381;&#2351;&#2306;&#2332;&#2325; &#2350;&#2375;&#2306; &#2357;&#2367;&#2325;&#2354;&#2381;&#2346; (a) &#2325;&#2375; &#2350;&#2366;&#2344;&#2379;&#2306; &#2325;&#2379; &#2352;&#2326;&#2344;&#2375; &#2346;&#2352;, &#2361;&#2350;&#2375;&#2306; &#2346;&#2381;&#2352;&#2366;&#2346;&#2381;&#2340; &#2361;&#2379;&#2340;&#2366; &#2361;&#2376;</span></p>\r\n<p><span style=\"font-family: Roboto;\">230 &times;</span><span style=\"font-family: Roboto;\">1<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&divide;</mo></math> </span><span style=\"font-family: Roboto;\">5 + 16 - 41 = 21</span></p>\r\n<p><span style=\"font-family: Roboto;\">LHS </span><span style=\"font-family: Roboto;\">= 230 &times; </span><span style=\"font-family: Roboto;\">1&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&divide;</mo></math></span><span style=\"font-family: Roboto;\"> 5 + 16 - 41</span></p>\r\n<p><span style=\"font-family: Roboto;\">= 230 &times;</span><span style=\"font-family: Roboto;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>5</mn></mfrac></math> +&nbsp;16 - 41</span></p>\r\n<p><span style=\"font-family: Roboto;\">= 46 + 16 - 41</span></p>\r\n<p><span style=\"font-family: Roboto;\">= 62 - 41</span></p>\r\n<p><span style=\"font-family: Roboto;\">= 21 = RHS</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "6",
                    question_en: " <p>9.</span><span style=\"font-family:Roboto\"> Select the correct mirror image of the given combination when the mirror is placed at MN as shown .</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669441637/word/media/image3.png\"/></p>",
                    question_hi: " <p>9. </span><span style=\"font-family:Palanquin\">जब दर्पण को MN पर रखा जाता है जैसा कि दिखाया गया है तो दिए गए संयोजन की सही दर्पण छवि का चयन करें।</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669441637/word/media/image3.png\"/></p>",
                    options_en: [" <p> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669441637/word/media/image23.png\"/></p>", " <p> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669441637/word/media/image45.png\"/></p>", 
                                " <p> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669441637/word/media/image14.png\"/></p>", " <p> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669441637/word/media/image17.png\"/></p>"],
                    options_hi: [" <p> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669441637/word/media/image23.png\"/><span style=\"font-family:Roboto\">  </span></p>", " <p> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669441637/word/media/image45.png\"/></p>",
                                " <p> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669441637/word/media/image14.png\"/></p>", " <p> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669441637/word/media/image17.png\"/></p>"],
                    solution_en: " <p>9.(c)</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669441637/word/media/image14.png\"/></p>",
                    solution_hi: " <p>9.(c)</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669441637/word/media/image14.png\"/></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "6",
                    question_en: "<p>10.<span style=\"font-family: Roboto;\"> Select the correct combination of mathematical signs to replace the * signs and to balance the given equation.</span></p>\r\n<p><span style=\"font-family: Roboto;\">41*4*39*73*25*4*30</span></p>\n",
                    question_hi: "<p>10.<span style=\"font-family: Palanquin;\"> * &#2330;&#2367;&#2361;&#2381;&#2344;&#2379;&#2306; &#2325;&#2379; &#2346;&#2381;&#2352;&#2340;&#2367;&#2360;&#2381;&#2341;&#2366;&#2346;&#2367;&#2340; &#2325;&#2352;&#2344;&#2375; &#2324;&#2352; &#2342;&#2367;&#2319; &#2327;&#2319; &#2360;&#2350;&#2368;&#2325;&#2352;&#2339; &#2325;&#2379; &#2360;&#2306;&#2340;&#2369;&#2354;&#2367;&#2340; &#2325;&#2352;&#2344;&#2375; &#2325;&#2375; &#2354;&#2367;&#2319; &#2327;&#2339;&#2367;&#2340;&#2368;&#2351; &#2330;&#2367;&#2361;&#2381;&#2344;&#2379;&#2306; &#2325;&#2375; &#2360;&#2361;&#2368; &#2360;&#2306;&#2351;&#2379;&#2332;&#2344; &#2325;&#2366; &#2330;&#2351;&#2344; &#2325;&#2352;&#2375;&#2306;&#2404;</span></p>\r\n<p><span style=\"font-family: Roboto;\">41*4*39*73*25*4*30</span></p>\n",
                    options_en: ["<p>&minus;,&times;,+,=,&divide;,+</p>\n", "<p>&times;,+,&minus;,=, &times;,+</p>\n", 
                                "<p>&divide;,+,=,&times;,&minus;,+</p>\n", "<p>&minus;,=,+,&times;,&minus;,&times;</p>\n"],
                    options_hi: ["<p>&minus;,&times;,+,=,&divide;,+</p>\n", "<p>&times;,+,&minus;,=, &times;,+</p>\n",
                                "<p>&divide;,+,=,&times;,&minus;,+</p>\n", "<p>&minus;,=,+,&times;,&minus;,&times;</p>\n"],
                    solution_en: "<p>10.(b)</p>\r\n<p><span style=\"font-family: Nova Mono;\">In this type of question, we will check by putting options one by one and doing so option (b) gets satisfied.</span></p>\r\n<p><span style=\"font-family: Nova Mono;\">41*4*39*73*25*4*30</span></p>\r\n<p><span style=\"font-family: Nova Mono;\">Putting values of option (b) in above expression, we get </span></p>\r\n<p><span style=\"font-family: Roboto;\">41 &times; </span><span style=\"font-family: Roboto;\">4 + 39 - 73 = 25 &times; </span><span style=\"font-family: Roboto;\">4 + 30</span></p>\r\n<p><span style=\"font-family: Roboto;\">LHS = 41 &times;&nbsp;</span><span style=\"font-family: Roboto;\"> 4 + 39 - 73</span></p>\r\n<p><span style=\"font-family: Roboto;\">= 164 + 39 - 73</span></p>\r\n<p><span style=\"font-family: Roboto;\">= 203 - 73</span></p>\r\n<p><span style=\"font-family: Roboto;\">= 130</span></p>\r\n<p><span style=\"font-family: Roboto;\">RHS = 25 &times; </span><span style=\"font-family: Roboto;\">4 + 30 = 100 + 30 = 130</span></p>\r\n<p><span style=\"font-family: Nova Mono;\">So, LHS = RHS</span></p>\n",
                    solution_hi: "<p>10.(b) <span style=\"font-family: Palanquin;\">&#2311;&#2360; &#2346;&#2381;&#2352;&#2325;&#2366;&#2352; &#2325;&#2375; &#2346;&#2381;&#2352;&#2358;&#2381;&#2344; &#2350;&#2375;&#2306; &#2361;&#2350; &#2319;&#2325;-&#2319;&#2325; &#2325;&#2352;&#2325;&#2375; &#2357;&#2367;&#2325;&#2354;&#2381;&#2346; &#2352;&#2326;&#2325;&#2352; &#2332;&#2366;&#2306;&#2330; &#2325;&#2352;&#2375;&#2306;&#2327;&#2375; &#2324;&#2352; &#2320;&#2360;&#2366; &#2325;&#2352;&#2344;&#2375; &#2360;&#2375; &#2357;&#2367;&#2325;&#2354;&#2381;&#2346; (b) &#2360;&#2306;&#2340;&#2369;&#2359;&#2381;&#2335; &#2361;&#2379; &#2332;&#2366;&#2340;&#2366; &#2361;&#2376;&#2404;</span></p>\r\n<p><span style=\"font-family: Roboto;\">41*4*39*73*25*4*30</span></p>\r\n<p><span style=\"font-family: Palanquin;\">&#2313;&#2346;&#2352;&#2379;&#2325;&#2381;&#2340; &#2357;&#2381;&#2351;&#2306;&#2332;&#2325; &#2350;&#2375;&#2306; &#2357;&#2367;&#2325;&#2354;&#2381;&#2346; (b) &#2325;&#2375; &#2350;&#2366;&#2344; &#2352;&#2326;&#2344;&#2375; &#2346;&#2352;, &#2361;&#2350;&#2375;&#2306; &#2346;&#2381;&#2352;&#2366;&#2346;&#2381;&#2340; &#2361;&#2379;&#2340;&#2366; &#2361;&#2376;</span></p>\r\n<p><span style=\"font-family: Roboto;\">41 &times; </span><span style=\"font-family: Roboto;\">4 + 39 - 73 = 25 &times; </span><span style=\"font-family: Roboto;\">4 + 30</span></p>\r\n<p><span style=\"font-family: Roboto;\">LHS = 41 &times;&nbsp;</span><span style=\"font-family: Roboto;\"> 4 + 39 - 73</span></p>\r\n<p><span style=\"font-family: Roboto;\">= 164 + 39 - 73</span></p>\r\n<p><span style=\"font-family: Roboto;\">= 203 - 73</span></p>\r\n<p><span style=\"font-family: Roboto;\">= 130</span></p>\r\n<p><span style=\"font-family: Roboto;\">RHS = 25 &times; </span><span style=\"font-family: Roboto;\">4 + 30 = 100 + 30 = 130 </span></p>\r\n<p><span style=\"font-family: Palanquin;\">&#2311;&#2360;&#2354;&#2367;&#2319;, LHS = RHS</span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "6",
                    question_en: "<p>11.<span style=\"font-family: Palanquin;\"> The second number in the given number-pairs is obtained by performing certain mathematical operation(s) on the first number. The same operation(s) are followed in all the number-pairs EXCEPT one. Find that odd number-pair.</span></p>\n",
                    question_hi: "<p>11.<span style=\"font-family: Palanquin;\"> &#2357;&#2367;&#2359;&#2350; &#2360;&#2306;&#2326;&#2381;&#2351;&#2366;-&#2351;&#2369;&#2327;&#2381;&#2350; &#2332;&#2381;&#2334;&#2366;&#2340; &#2325;&#2368;&#2332;&#2367;&#2319;&#2404;</span></p>\n",
                    options_en: ["<p>4 : 48</p>\n", "<p>6 : 180</p>\n", 
                                "<p>8 : 448</p>\n", "<p>9 : 639</p>\n"],
                    options_hi: ["<p>4 : 48</p>\n", "<p>6 : 180</p>\n",
                                "<p>8 : 448</p>\n", "<p>9 : 639</p>\n"],
                    solution_en: "<p>11.(d) logic :: (n) : (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>n</mi><mn>3</mn></msup><mo>-</mo><msup><mi>n</mi><mn>2</mn></msup></math>)</p>\r\n<p><span style=\"font-family: Roboto;\">In 4 : 48, (4)</span><span style=\"font-family: Roboto;\">: </span><span style=\"font-family: Roboto;\">&nbsp;(<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>4</mn><mn>3</mn></msup><mo>-</mo><mo>&nbsp;</mo><msup><mn>4</mn><mn>2</mn></msup></math>) = 4 : (64 - 16) = 4 : 48</span></p>\r\n<p><span style=\"font-family: Roboto;\">In 6 : 180, (6)</span><span style=\"font-family: Roboto;\">: (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>6</mn><mn>3</mn></msup><mo>-</mo><mo>&nbsp;</mo><msup><mn>6</mn><mn>2</mn></msup></math>)</span><span style=\"font-family: Roboto;\">&nbsp;6 : (216 - 36) = 6 : 180</span></p>\r\n<p><span style=\"font-family: Roboto;\">In 8 : 448,(8)&nbsp;</span><span style=\"font-family: Roboto;\"> : (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>8</mn><mn>3</mn></msup><mo>-</mo><mo>&nbsp;</mo><msup><mn>8</mn><mn>2</mn></msup></math>)</span><span style=\"font-family: Roboto;\">&nbsp;8 : (512 - 64) = 8 : 448</span></p>\r\n<p><span style=\"font-family: Roboto;\">In 9 : 639,(9)&nbsp;</span><span style=\"font-family: Roboto;\"> : (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>9</mn><mn>3</mn></msup><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><msup><mn>9</mn><mn>2</mn></msup></math>) </span><span style=\"font-family: Roboto;\">9 : (729 - 81) = 9 : 648 &ne; </span><span style=\"font-family: Roboto;\">9 : 639</span></p>\r\n<p><span style=\"font-family: Roboto;\">So, 9 : 639 is an odd one.</span></p>\n",
                    solution_hi: "<p>11.(d) &#2340;&#2352;&#2381;&#2325; :<span style=\"font-family: Roboto;\"> </span><span style=\"font-family: Roboto;\"> : (n) : <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><msup><mi>n</mi><mn>3</mn></msup><mo>-</mo><mo>&nbsp;</mo><msup><mi>n</mi><mn>2</mn></msup><mo>)</mo></math></span></p>\r\n<p><span style=\"font-family: Palanquin;\">4 : 48 &#2350;&#2375;&#2306;, (4) </span><span style=\"font-family: Roboto;\">: (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>4</mn><mn>3</mn></msup><mo>-</mo><msup><mn>4</mn><mn>2</mn></msup></math>)&nbsp;</span><span style=\"font-family: Roboto;\">= 4 : (64 - 16) = 4 : 48</span></p>\r\n<p><span style=\"font-family: Palanquin;\">6 : 180 &#2350;&#2375;&#2306;, (6)&nbsp;</span><span style=\"font-family: Roboto;\"> : (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>6</mn><mrow><mn>3</mn><mo>&nbsp;</mo><mi>_</mi></mrow></msup><msup><mn>6</mn><mn>2</mn></msup></math>) </span><span style=\"font-family: Roboto;\">= 6 : (216 - 36) = 6 : 180</span></p>\r\n<p><span style=\"font-family: Palanquin;\">8 : 448 &#2350;&#2375;&#2306;, </span><span style=\"font-family: Roboto;\">(8) : (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>8</mn><mn>3</mn></msup><mo>-</mo><mo>&nbsp;</mo><msup><mn>8</mn><mn>2</mn></msup></math>)</span><span style=\"font-family: Roboto;\"> = 8 : (512 - 64) = 8 : 448</span></p>\r\n<p><span style=\"font-family: Palanquin;\">9 : 639 &#2350;&#2375;&#2306;, (9) </span><span style=\"font-family: Roboto;\">: (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>9</mn><mn>3</mn></msup><mo>-</mo><mo>&nbsp;</mo><msup><mn>9</mn><mn>2</mn></msup></math>) &nbsp;</span><span style=\"font-family: Roboto;\">= 9 : (729 - 81) = 9 : 648 &ne;</span><span style=\"font-family: Roboto;\">&nbsp;9 : 639</span></p>\r\n<p><span style=\"font-family: Palanquin;\">&#2309;&#2340;&#2307;, 9 : 639 &#2319;&#2325; &#2357;&#2367;&#2359;&#2350; &#2360;&#2306;&#2326;&#2381;&#2351;&#2366; &#2361;&#2376;&#2404;</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "6",
                    question_en: " <p>12. </span><span style=\"font-family:Palanquin\">Select the option that represents the letters that, when placed from left to right in the blanks, will complete the letter series.</span></p> <p><span style=\"font-family:Palanquin\">_ </span><span style=\"font-family:Palanquin\">P N I E X</span><span style=\"font-family:Palanquin\"> _ </span><span style=\"font-family:Palanquin\">N I E Y P N</span><span style=\"font-family:Palanquin\"> _ </span><span style=\"font-family:Palanquin\">E Z P</span><span style=\"font-family:Palanquin\"> _ I _</span></p>",
                    question_hi: " <p>12. </span><span style=\"font-family:Palanquin\">उस विकल्प का चयन करें जो उन अक्षरों का प्रतिनिधित्व करता है जिन्हें रिक्त स्थान में बाएं से दाएं रखे जाने पर और अक्षर श्रृंखला को पूरा करेगा।</span></p> <p><span style=\"font-family:Roboto\"> _ </span><span style=\"font-family:Roboto\">P N I E X</span><span style=\"font-family:Roboto\"> _ </span><span style=\"font-family:Roboto\">N I E Y P N</span><span style=\"font-family:Roboto\"> _ </span><span style=\"font-family:Roboto\">E Z P</span><span style=\"font-family:Roboto\"> _ I _</span></p>",
                    options_en: [" <p> I E W P N</span></p>", " <p> N I E W P</span></p>", 
                                " <p> W P I N E</span></p>", " <p> E W P N I</span></p>"],
                    options_hi: [" <p> I E W P N</span></p>", " <p> N I E W P</span></p>",
                                " <p> W P I N E</span></p>", " <p> E W P N I</span></p>"],
                    solution_en: " <p>12.(c)</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669441637/word/media/image43.png\"/></p> <p><span style=\"font-family:Roboto\">W</span><span style=\"font-family:Roboto\"> P N I E / </span><span style=\"font-family:Roboto\">X </span><span style=\"font-family:Roboto\">P</span><span style=\"font-family:Roboto\"> N I E</span><span style=\"font-family:Roboto\"> / </span><span style=\"font-family:Roboto\">Y P N</span><span style=\"font-family:Roboto\"> I </span><span style=\"font-family:Roboto\">E</span><span style=\"font-family:Roboto\"> / </span><span style=\"font-family:Roboto\">Z P </span><span style=\"font-family:Roboto\">N</span><span style=\"font-family:Roboto\"> I </span><span style=\"font-family:Roboto\">E</span></p> <p><span style=\"font-family:Roboto\">First letter of each pair is in alphabetical order : W, X, Y, Z.</span></p>",
                    solution_hi: " <p>12.(c)</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669441637/word/media/image43.png\"/></p> <p><span style=\"font-family:Palanquin\">प्रत्येक जोड़े का पहला अक्षर वर्णानुक्रम में है: W , X , Y , Z</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "6",
                    question_en: " <p>13. </span><span style=\"font-family:Palanquin\">Select the option that is related to the fifth term in the same way as the second term is related to the first term and the fourth term is related to the third term.</span></p> <p><span style=\"font-family:Palanquin\">BROWSER : SERWBRO :: CAPTURE : URETCAP :: DESKTOP : ?</span></p>",
                    question_hi: " <p>13. </span><span style=\"font-family:Palanquin\">उउस विकल्प का चयन करें जो पांचवें पद से उसी प्रकार संबंधित है जैसे दूसरा पद पहले पद से और चौथा पद तीसरे पद से संबंधित है।</span></p> <p><span style=\"font-family:Roboto\">BROWSER : SERWBRO :: CAPTURE : URETCAP :: DESKTOP : ?</span></p>",
                    options_en: [" <p> SEDKTOP</span></p>", " <p> TOPKDES</span></p>", 
                                " <p> TPOKEDS</span></p>", " <p> TOPKSED</span></p>"],
                    options_hi: [" <p> SEDKTOP</span></p>", " <p> TOPKDES</span></p>",
                                " <p> TPOKEDS</span></p>", " <p> TOPKSED</span></p>"],
                    solution_en: " <p>13.(b)</span></p> <p><span style=\"font-family:Roboto\">1  2  3  4  5  6  7     5  6  7  4  1  2  3</span></p> <p><span style=\"font-family:Roboto\">B R O  W S E R =  </span><span style=\"font-family:Roboto\">S  E R W  B R O</span></p> <p><span style=\"font-family:Roboto\">1  2 3  4 5 6  7      5  6  7 4 1  2  3</span></p> <p><span style=\"font-family:Roboto\">C A P T U R E =  </span><span style=\"font-family:Roboto\">U R E T C A </span><span style=\"font-family:Roboto\">P</span></p> <p><span style=\"font-family:Roboto\">Similarly,</span></p> <p><span style=\"font-family:Roboto\">1  2 3  4 5 6  7     5  6  7 4 1  2  3</span></p> <p><span style=\"font-family:Roboto\">D E S K T O P =  T</span><span style=\"font-family:Roboto\"> O P K D E </span><span style=\"font-family:Roboto\">S</span></p>",
                    solution_hi: " <p>13.(b)</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669441637/word/media/image8.png\"/></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669441637/word/media/image38.png\"/></p> <p><span style=\"font-family:Palanquin\">इसी तरह,</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669441637/word/media/image35.png\"/></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "6",
                    question_en: "<p><span style=\"font-family: Roboto;\">14. </span><span style=\"font-family: Roboto;\">Select the correct combination of mathematical signs to sequentially replace the * signs and to balance the given equation.</span></p>\r\n<p><span style=\"font-family: Roboto;\">93*65*3*56*14*40</span></p>\n",
                    question_hi: "<p>14.<span style=\"font-family: Palanquin;\"> * &#2330;&#2367;&#2361;&#2381;&#2344;&#2379;&#2306; &#2325;&#2379; &#2325;&#2381;&#2352;&#2350;&#2366;&#2344;&#2369;&#2360;&#2366;&#2352; &#2348;&#2342;&#2354;&#2344;&#2375; &#2324;&#2352; &#2342;&#2367;&#2319; &#2327;&#2319; &#2360;&#2350;&#2368;&#2325;&#2352;&#2339; &#2325;&#2379; &#2360;&#2306;&#2340;&#2369;&#2354;&#2367;&#2340; &#2325;&#2352;&#2344;&#2375; &#2325;&#2375; &#2354;&#2367;&#2319; &#2327;&#2339;&#2367;&#2340;&#2368;&#2351; &#2330;&#2367;&#2361;&#2381;&#2344;&#2379;&#2306; &#2325;&#2375; &#2360;&#2361;&#2368; &#2360;&#2306;&#2351;&#2379;&#2332;&#2344; &#2325;&#2366; &#2330;&#2351;&#2344; &#2325;&#2368;&#2332;&#2367;&#2319;&#2404;</span></p>\r\n<p><span style=\"font-family: Roboto;\">93*65*3*56*14*40</span></p>\n",
                    options_en: ["<p>&ndash;,+,&times;,&divide;, =</p>\n", "<p>&times;, +, &ndash;,&divide;, =</p>\n", 
                                "<p>+, &ndash;, &divide;,&times;, =</p>\n", "<p>+,&ndash;, &times;,&divide;, =</p>\n"],
                    options_hi: ["<p>&ndash;,+,&times;,&divide;, =</p>\n", "<p>&times;, +, &ndash;,&divide;, =</p>\n",
                                "<p>+, &ndash;, &divide;,&times;, =</p>\n", "<p>+,&ndash;, &times;,&divide;, =</p>\n"],
                    solution_en: "<p>14.(a)</p>\r\n<p><span style=\"font-family: Roboto;\">In this type of question, we will check by putting options one by one and doing so option (a) gets satisfied.</span></p>\r\n<p><span style=\"font-family: Roboto;\">93*65*3*56*14*40</span></p>\r\n<p><span style=\"font-family: Roboto;\">Putting values of option (a) in above expression, we get </span></p>\r\n<p><span style=\"font-family: Roboto;\">93 - 65 + 3 &times; </span><span style=\"font-family: Roboto;\">56 &divide; </span><span style=\"font-family: Roboto;\">14 = 40</span></p>\r\n<p><span style=\"font-family: Roboto;\">LHS </span><span style=\"font-family: Roboto;\">= 93 - 65 + 3 </span><span style=\"font-family: Roboto;\"> 56 &divide;</span><span style=\"font-family: Roboto;\">14 </span></p>\r\n<p><span style=\"font-family: Roboto;\">= 93 - 65 + 3 &times; </span><span style=\"font-family: Roboto;\">4 </span></p>\r\n<p><span style=\"font-family: Roboto;\">= 93 - 65 + 12</span></p>\r\n<p><span style=\"font-family: Roboto;\">= 105 - 65</span></p>\r\n<p><span style=\"font-family: Roboto;\">= 40 = RHS</span></p>\n",
                    solution_hi: "<p>14.(a)</p>\r\n<p><span style=\"font-family: Palanquin;\">&#2311;&#2360; &#2346;&#2381;&#2352;&#2325;&#2366;&#2352; &#2325;&#2375; &#2346;&#2381;&#2352;&#2358;&#2381;&#2344; &#2350;&#2375;&#2306; &#2361;&#2350; &#2319;&#2325;-&#2319;&#2325; &#2325;&#2352;&#2325;&#2375; &#2357;&#2367;&#2325;&#2354;&#2381;&#2346; &#2337;&#2366;&#2354;&#2325;&#2352; &#2332;&#2366;&#2306;&#2330; &#2325;&#2352;&#2375;&#2306;&#2327;&#2375; &#2324;&#2352; &#2320;&#2360;&#2366; &#2325;&#2352;&#2344;&#2375; &#2360;&#2375; &#2357;&#2367;&#2325;&#2354;&#2381;&#2346; (a) &#2360;&#2306;&#2340;&#2369;&#2359;&#2381;&#2335; &#2361;&#2379; &#2332;&#2366;&#2340;&#2366; &#2361;&#2376;&#2404;</span></p>\r\n<p><span style=\"font-family: Roboto;\">93*65*3*56*14*40</span></p>\r\n<p><span style=\"font-family: Palanquin;\">&#2313;&#2346;&#2352;&#2379;&#2325;&#2381;&#2340; &#2357;&#2381;&#2351;&#2306;&#2332;&#2325; &#2350;&#2375;&#2306; &#2357;&#2367;&#2325;&#2354;&#2381;&#2346; (a) &#2325;&#2375; &#2350;&#2366;&#2344; &#2352;&#2326;&#2344;&#2375; &#2346;&#2352;, &#2361;&#2350;&#2375;&#2306; &#2346;&#2381;&#2352;&#2366;&#2346;&#2381;&#2340; &#2361;&#2379;&#2340;&#2366; &#2361;&#2376;</span></p>\r\n<p><span style=\"font-family: Roboto;\">93 - 65 + 3 &times;&nbsp;</span><span style=\"font-family: Roboto;\"> 56 &divide;</span><span style=\"font-family: Roboto;\">14 = 40</span></p>\r\n<p><span style=\"font-family: Roboto;\">LHS = 93 - 65 + 3 &times; </span><span style=\"font-family: Roboto;\">56 &divide; </span><span style=\"font-family: Roboto;\">14 </span></p>\r\n<p><span style=\"font-family: Roboto;\">= 93 - 65 + 3 &times; </span><span style=\"font-family: Roboto;\">4 </span></p>\r\n<p><span style=\"font-family: Roboto;\">= 93 - 65 + 12</span></p>\r\n<p><span style=\"font-family: Roboto;\">= 105 - 65</span></p>\r\n<p><span style=\"font-family: Roboto;\">= 40 = RHS</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "6",
                    question_en: " <p>15. </span><span style=\"font-family:Roboto\">The second number in the given number pairs is obtained by performing certain mathematical operation(s) on the first number. The same operation(s) are followed in all the number pairs, except one. Find that odd number pair.</span></p>",
                    question_hi: "<p>15.<span style=\"font-family: Palanquin;\"> &#2357;&#2367;&#2359;&#2350; &#2360;&#2306;&#2326;&#2381;&#2351;&#2366; &#2351;&#2369;&#2327;&#2381;&#2350; &#2332;&#2381;&#2334;&#2366;&#2340; &#2325;&#2368;&#2332;&#2367;&#2319;&#2404;</span></p>\n",
                    options_en: [" <p> 6 : 36</span></p>", " <p> 2 : 15</span></p>", 
                                " <p> 3 : 20</span></p>", " <p> 5 : 30</span></p>"],
                    options_hi: ["<p>6 : 36<span style=\"font-family: Roboto;\"> </span></p>\n", "<p>2 : 15</p>\n",
                                "<p>3 : 20</p>\n", "<p>5 : 30</p>\n"],
                    solution_en: " <p>15.(a)</span></p> <p><span style=\"font-family:Roboto\">In 6 : 36, the second number that is 36 is a square of the first number that is 6. So, option (a) is an odd one.</span></p>",
                    solution_hi: "<p>15.(a) <span style=\"font-family: Palanquin;\">&#2357;&#2367;&#2325;&#2354;&#2381;&#2346; (a) &#2325;&#2379; &#2331;&#2379;&#2337;&#2364;&#2325;&#2352;, &#2309;&#2344;&#2381;&#2351; &#2360;&#2349;&#2368; &#2360;&#2306;&#2326;&#2381;&#2351;&#2366;&#2319;&#2306; &#2344;&#2368;&#2330;&#2375; &#2342;&#2367;&#2319; &#2346;&#2376;&#2335;&#2352;&#2381;&#2344; &#2325;&#2366; &#2309;&#2344;&#2369;&#2360;&#2352;&#2339; &#2325;&#2352;&#2340;&#2368; &#2361;&#2376;&#2306;</span></p>\r\n<p><span style=\"font-weight: 400;\">(&#2346;&#2361;&#2354;&#2368; &#2360;&#2306;&#2326;&#2381;&#2351;&#2366; +1)&times; 5 = &#2342;&#2370;&#2360;&#2352;&#2368; &#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "6",
                    question_en: "<p>16. <span style=\"font-family: Arial Unicode MS;\">If I denotes &lsquo;&divide;&rsquo;, J denotes &lsquo;&times;&rsquo;, K denotes &lsquo;&minus;&rsquo; and L denotes &lsquo;+&rsquo;, then what will come in place of &lsquo;?&rsquo; in the following equation?</span></p>\r\n<p><span style=\"font-family: Arial Unicode MS;\">42 L 18 K (7 J 4) L (21 I 3) J 9 L 22 = ?</span></p>\n",
                    question_hi: "<p>16. <span style=\"font-family: Palanquin;\">&#2351;&#2342;&#2367; I &#2325;&#2366; &#2309;&#2352;&#2381;&#2341; \'&divide;\' &#2361;&#2376;, J &#2325;&#2366; &#2309;&#2352;&#2381;&#2341; \'&times;\' &#2361;&#2376;, K &#2325;&#2366; &#2309;&#2352;&#2381;&#2341; \'&ndash;\' &#2361;&#2376; &#2324;&#2352; L &#2325;&#2366; &#2309;&#2352;&#2381;&#2341; \'+\' &#2361;&#2376;, &#2340;&#2379; &#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340; &#2360;&#2350;&#2368;&#2325;&#2352;&#2339; &#2350;&#2375;&#2306; \'?\' &#2325;&#2375; &#2360;&#2381;&#2341;&#2366;&#2344; &#2346;&#2352; &#2325;&#2381;&#2351;&#2366; &#2310;&#2319;&#2327;&#2366;?</span></p>\r\n<p><span style=\"font-family: Roboto;\">42 L 18 K (7 J 4) L (21 I 3) J 9 L 22 = ?</span></p>\n",
                    options_en: ["<p>117</p>\n", "<p>120</p>\n", 
                                "<p>114</p>\n", "<p>127</p>\n"],
                    options_hi: ["<p>117</p>\n", "<p>120</p>\n",
                                "<p>114</p>\n", "<p>127</p>\n"],
                    solution_en: "<p>16.(a)</p>\r\n<p><span style=\"font-family: Roboto;\">42 L 18 K (7 J 4) L (21 I 3) J 9 L 22</span></p>\r\n<p><span style=\"font-family: Roboto;\">Applying the directions in above expression, we get</span></p>\r\n<p><span style=\"font-family: Roboto;\">42 + 18 - (7 &times; </span><span style=\"font-family: Roboto;\">4) + (21&divide;</span><span style=\"font-family: Roboto;\"> 3) &times; </span><span style=\"font-family: Roboto;\">9 + 22</span></p>\r\n<p><span style=\"font-family: Roboto;\">= 42 + 18 - (28) + (7) &times; </span><span style=\"font-family: Roboto;\">9 + 22</span></p>\r\n<p><span style=\"font-family: Roboto;\">= 42 + 18 - 28 + 63 + 22 = 145 - 28 = 117</span></p>\n",
                    solution_hi: "<p>16.(a)</p>\r\n<p><span style=\"font-family: Roboto;\">42 L 18 K (7 J 4) L (21 I 3) J 9 L 22</span></p>\r\n<p><span style=\"font-family: Palanquin;\">&#2313;&#2346;&#2352;&#2379;&#2325;&#2381;&#2340; &#2357;&#2381;&#2351;&#2306;&#2332;&#2325; &#2350;&#2375;&#2306; &#2344;&#2367;&#2352;&#2381;&#2342;&#2375;&#2358;&#2379;&#2306; &#2325;&#2379; &#2354;&#2366;&#2327;&#2370; &#2325;&#2352;&#2344;&#2375; &#2346;&#2352;, </span></p>\r\n<p><span style=\"font-family: Roboto;\">42 + 18 - (7 &times; </span><span style=\"font-family: Roboto;\">4) + (21&divide;</span><span style=\"font-family: Roboto;\"> 3) &times; </span><span style=\"font-family: Roboto;\">9 + 22</span></p>\r\n<p><span style=\"font-family: Roboto;\">= 42 + 18 - (28) + (7) &times; </span><span style=\"font-family: Roboto;\">9 + 22</span></p>\r\n<p><span style=\"font-family: Roboto;\">= 42 + 18 - 28 + 63 + 22 = 145 - 28 = 117 </span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "6",
                    question_en: " <p>17.</span><span style=\"font-family:Roboto\"> Select the option that represents the letters that, when sequentially placed from left to right in the blanks below, will complete the letter series.</span></p> <p><span style=\"font-family:Roboto\">_ a _ _ W a L W _ L _ W a L _ a _a W _ L</span></p>",
                    question_hi: " <p>17. </span><span style=\"font-family:Palanquin\">उस विकल्प का चयन करें जो उन अक्षरों का प्रतिनिधित्व करता है, जिन्हें नीचे श्रृंखला में दिए गए रिक्त स्थानों में क्रमिक रूप से बाएं से दाएं रखा जाता है तो अक्षर श्रृंखला पूर्ण हो जाएगी।</span></p> <p><span style=\"font-family:Roboto\">_ a _ _ W a L W _ L _ W a L _ a _a W _ L</span></p>",
                    options_en: [" <p> W L a a L W L a</span></p>", " <p> W L a a a W L a</span></p>", 
                                " <p> W L a a a W L W</span></p>", " <p> L L a a a W L a</span></p>"],
                    options_hi: [" <p> W L a a L W L a      </span></p>", " <p> W L a a a W L a</span></p>",
                                " <p> W L a a a W L W     </span></p>", " <p> L L a a a W L a</span></p>"],
                    solution_en: " <p>17.(b)</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669441637/word/media/image41.png\"/></p> <p><span style=\"font-family:Roboto\">W</span><span style=\"font-family:Roboto\"> a </span><span style=\"font-family:Roboto\">L</span><span style=\"font-family:Roboto\"> </span><span style=\"font-family:Roboto\">a</span><span style=\"font-family:Roboto\"> W a L / W </span><span style=\"font-family:Roboto\">a</span><span style=\"font-family:Roboto\"> L </span><span style=\"font-family:Roboto\">a</span><span style=\"font-family:Roboto\"> W a L / </span><span style=\"font-family:Roboto\">W</span><span style=\"font-family:Roboto\"> a </span><span style=\"font-family:Roboto\">L</span><span style=\"font-family:Roboto\"> a W </span><span style=\"font-family:Roboto\">a</span><span style=\"font-family:Roboto\"> L</span><span style=\"font-family:Roboto\"> </span></p>",
                    solution_hi: " <p>17.(b)</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669441637/word/media/image41.png\"/></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "6",
                    question_en: " <p>18.</span><span style=\"font-family:Roboto\"> </span><span style=\"font-family:Roboto\">A paper</span><span style=\"font-family:Roboto\"> is folded and cut as shown. How will it appear when unfolded ?</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669441637/word/media/image34.png\"/></p>",
                    question_hi: " <p>18. </span><span style=\"font-family:Palanquin\">एक कागज को मोड़ा जाता है और दिखाए गए अनुसार काटा जाता है। खोले जाने पर यह कैसा दिखाई देगा?</span></p> <p><span style=\"font-family:Roboto\">   </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669441637/word/media/image34.png\"/></p>",
                    options_en: [" <p> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669441637/word/media/image6.png\"/></p>", " <p> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669441637/word/media/image20.png\"/></p>", 
                                " <p> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669441637/word/media/image9.png\"/></p>", " <p> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669441637/word/media/image1.png\"/></p>"],
                    options_hi: [" <p> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669441637/word/media/image6.png\"/><span style=\"font-family:Roboto\">   </span></p>", " <p> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669441637/word/media/image20.png\"/></p>",
                                " <p> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669441637/word/media/image9.png\"/><span style=\"font-family:Roboto\">   </span></p>", " <p> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669441637/word/media/image1.png\"/></p>"],
                    solution_en: " <p>18.(d)</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669441637/word/media/image1.png\"/></p>",
                    solution_hi: " <p>18.(d)</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669441637/word/media/image1.png\"/></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "6",
                    question_en: " <p>19.</span><span style=\"font-family:Roboto\"> Select the correct mirror image of the given combination when the mirror is placed at XY as shown.</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669441637/word/media/image26.png\"/></p>",
                    question_hi: " <p>19.</span><span style=\"font-family:Palanquin\"> जब दर्पण को XY पर रखा जाता है तो दिए गए संयोजन की सही दर्पण छवि का चयन करें।</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669441637/word/media/image26.png\"/></p>",
                    options_en: [" <p> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669441637/word/media/image39.png\"/></p>", " <p> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669441637/word/media/image11.png\"/></p>", 
                                " <p> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669441637/word/media/image7.png\"/></p>", " <p> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669441637/word/media/image18.png\"/></p>"],
                    options_hi: [" <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669441637/word/media/image39.png\"/></p>", " <p> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669441637/word/media/image11.png\"/></p>",
                                " <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669441637/word/media/image7.png\"/></p>", " <p> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669441637/word/media/image18.png\"/></p>"],
                    solution_en: " <p>19.(c)</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669441637/word/media/image7.png\"/></p>",
                    solution_hi: " <p>19.(c)</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669441637/word/media/image7.png\"/></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "6",
                    question_en: " <p>20.</span><span style=\"font-family:Roboto\"> How many squares are there in the given figure ?</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669441637/word/media/image28.png\"/></p>",
                    question_hi: " <p>20. </span><span style=\"font-family:Palanquin\">दी गई आकृति में कितने वर्ग हैं?</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669441637/word/media/image28.png\"/></p>",
                    options_en: [" <p> 20</span></p>", " <p> 18</span></p>", 
                                " <p> 12</span></p>", " <p> 24</span></p>"],
                    options_hi: [" <p> 20</span></p>", " <p> 18</span></p>",
                                " <p> 12</span></p>", " <p> 24</span></p>"],
                    solution_en: " <p>20.(b)</span></p> <p><span style=\"font-family:Roboto\">Clearly, from the diagram given in the question, </span></p> <p><span style=\"font-family:Roboto\">On counting the number of squares we find that there total 18 squares.</span></p>",
                    solution_hi: " <p>20.(b)</span></p> <p><span style=\"font-family:Palanquin\">स्पष्ट रूप से, प्रश्न में दिए गए आरेख से,</span></p> <p><span style=\"font-family:Palanquin\">वर्गों की संख्या गिनने पर हम पाते हैं कि कुल 18 वर्ग हैं।</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "6",
                    question_en: " <p>21.</span><span style=\"font-family:Palanquin\"> A paper is folded and cut as shown below. How will it appear when unfolded ?</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669441637/word/media/image4.png\"/></p>",
                    question_hi: " <p>21.</span><span style=\"font-family:Palanquin\"> एक कागज को मोड़ा और काटा जाता है जैसा कि नीचे दिखाया गया है। इसे खोले जाने पर यह कैसा दिखाई देगा?</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669441637/word/media/image4.png\"/></p>",
                    options_en: [" <p> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669441637/word/media/image40.png\"/></p>", " <p> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669441637/word/media/image27.png\"/></p>", 
                                " <p> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669441637/word/media/image16.png\"/></p>", " <p> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669441637/word/media/image19.png\"/></p>"],
                    options_hi: [" <p> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669441637/word/media/image40.png\"/></p>", " <p> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669441637/word/media/image27.png\"/></p>",
                                " <p> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669441637/word/media/image16.png\"/></p>", " <p> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669441637/word/media/image19.png\"/></p>"],
                    solution_en: " <p>21.(c)</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669441637/word/media/image16.png\"/></p>",
                    solution_hi: " <p>21.(c)</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669441637/word/media/image16.png\"/></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "6",
                    question_en: "<p>22.<span style=\"font-family: Roboto;\"> Select the option that is related to the fifth term in the same way as the second term is related to the first term and fourth term related to third term.</span></p>\r\n<p><span style=\"font-family: Roboto;\">14:21::6:9::12:?</span></p>\n",
                    question_hi: "<p>22. <span style=\"font-family: Palanquin;\">&#2313;&#2360; &#2357;&#2367;&#2325;&#2354;&#2381;&#2346; &#2325;&#2366; &#2330;&#2351;&#2344; &#2325;&#2352;&#2375;&#2306; &#2332;&#2379; &#2346;&#2366;&#2306;&#2330;&#2357;&#2375;&#2306; &#2346;&#2342; &#2360;&#2375; &#2313;&#2360;&#2368; &#2346;&#2381;&#2352;&#2325;&#2366;&#2352; &#2360;&#2306;&#2348;&#2306;&#2343;&#2367;&#2340; &#2361;&#2376; &#2332;&#2376;&#2360;&#2375; &#2342;&#2370;&#2360;&#2352;&#2366; &#2346;&#2342; &#2346;&#2361;&#2354;&#2375; &#2346;&#2342; &#2360;&#2375; &#2324;&#2352; &#2330;&#2380;&#2341;&#2366; &#2346;&#2342; &#2340;&#2368;&#2360;&#2352;&#2375; &#2346;&#2342; &#2360;&#2375; &#2360;&#2306;&#2348;&#2306;&#2343;&#2367;&#2340; &#2361;&#2376;&#2404;</span></p>\r\n<p><span style=\"font-family: Roboto;\">14 : 21 : : 6 : 9 : : 12 : ?</span></p>\n",
                    options_en: ["<p>24</p>\n", "<p>35</p>\n", 
                                "<p>18</p>\n", "<p>20</p>\n"],
                    options_hi: ["<p>24</p>\n", "<p>35</p>\n",
                                "<p>18</p>\n", "<p>20</p>\n"],
                    solution_en: "<p>22.(c)</p>\r\n<p><span style=\"font-family: Roboto;\">Logic : n &times; </span><span style=\"font-family: Roboto;\">2 : n &times; </span><span style=\"font-family: Roboto;\">3</span></p>\r\n<p><span style=\"font-family: Roboto;\">In 14 : 21, 7 &times; </span><span style=\"font-family: Roboto;\">2 : 7 &times; </span><span style=\"font-family: Roboto;\">3 = 14 : 21</span></p>\r\n<p><span style=\"font-family: Roboto;\">In 6 : 9, 3 &times; </span><span style=\"font-family: Roboto;\">2 : 3 &times; </span><span style=\"font-family: Roboto;\">3 = 6 : 9</span></p>\r\n<p><span style=\"font-family: Roboto;\">In 12 : (?), 6 &times; </span><span style=\"font-family: Roboto;\">2 : 6 &times; </span><span style=\"font-family: Roboto;\">&nbsp;3 = 12 : </span><span style=\"font-family: Roboto;\">18</span></p>\n",
                    solution_hi: "<p>22.(c)</p>\r\n<p><span style=\"font-family: Palanquin;\">&#2340;&#2352;&#2381;&#2325; :</span><span style=\"font-family: Roboto;\">- n &times; </span><span style=\"font-family: Roboto;\">2 : n &times; </span><span style=\"font-family: Roboto;\">3</span></p>\r\n<p><span style=\"font-family: Palanquin;\">14 : 21 &#2350;&#2375;&#2306;, 7 &times; </span><span style=\"font-family: Roboto;\">2 : 7 &times; </span><span style=\"font-family: Roboto;\">3 = 14 : 21</span></p>\r\n<p><span style=\"font-family: Palanquin;\">6 : 9 &#2350;&#2375;&#2306;, 3 &times; </span><span style=\"font-family: Roboto;\">2 : 3 &times; </span><span style=\"font-family: Roboto;\">3 = 6 : 9</span></p>\r\n<p><span style=\"font-family: Palanquin;\">12 : (?) &#2350;&#2375;&#2306;, 6 &times; </span><span style=\"font-family: Roboto;\">2 : 6 &times; </span><span style=\"font-family: Roboto;\">3 = 12 : </span><span style=\"font-family: Roboto;\">18</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "23",
                    section: "6",
                    question_en: "<p>23.<span style=\"font-family: Roboto;\"> If 20 July 2021 was Tuesday, then what was the day of the week on 28 November 2021?</span></p>\n",
                    question_hi: "<p>23. <span style=\"font-family: Palanquin;\">&#2351;&#2342;&#2367; 20 &#2332;&#2369;&#2354;&#2366;&#2312; 2021 &#2325;&#2379; &#2350;&#2306;&#2327;&#2354;&#2357;&#2366;&#2352; &#2341;&#2366;, &#2340;&#2379; 28 &#2344;&#2357;&#2306;&#2348;&#2352; 2021 &#2325;&#2379; &#2360;&#2346;&#2381;&#2340;&#2366;&#2361; &#2325;&#2366; &#2325;&#2380;&#2344; &#2360;&#2366; &#2342;&#2367;&#2344; &#2341;&#2366; ?</span></p>\n",
                    options_en: ["<p>Sunday</p>\n", "<p>Tuesday</p>\n", 
                                "<p>Monday</p>\n", "<p>Saturday</p>\n"],
                    options_hi: ["<p>&#2352;&#2357;&#2367;&#2357;&#2366;&#2352;</p>\n", "<p>&#2350;&#2306;&#2327;&#2354;&#2357;&#2366;&#2352;</p>\n",
                                "<p>&#2360;&#2379;&#2350;&#2357;&#2366;&#2352;</p>\n", "<p>&#2358;&#2344;&#2367;&#2357;&#2366;&#2352;</p>\n"],
                    solution_en: "<p>23.(a)</p>\r\n<p><span style=\"font-family: Palanquin;\">From 20 July 2021 to 28 November 2021, total number of odd days = 11 + 31 + 30 + 31 + 28 = 131</span></p>\r\n<p><span style=\"font-family: Palanquin;\">Net odd days = 131 - (18 &times; </span><span style=\"font-family: Palanquin;\">7) = 131 - 126 = 5 </span></p>\r\n<p><span style=\"font-family: Palanquin;\">So, the day of the week on 28 November 2021 = Tuesday + 5 odd days = Sunday</span></p>\n",
                    solution_hi: "<p>23.(a) <span style=\"font-family: Palanquin;\">20 &#2332;&#2369;&#2354;&#2366;&#2312; 2021 &#2360;&#2375; 28 &#2344;&#2357;&#2306;&#2348;&#2352; 2021 &#2340;&#2325;, &#2357;&#2367;&#2359;&#2350; &#2342;&#2367;&#2344;&#2379;&#2306; &#2325;&#2368; &#2325;&#2369;&#2354; &#2360;&#2306;&#2326;&#2381;&#2351;&#2366;= 11 + 31 + 30 + 31 + 28 = 131</span></p>\r\n<p><span style=\"font-family: Palanquin;\">&#2358;&#2369;&#2342;&#2381;&#2343; &#2357;&#2367;&#2359;&#2350; &#2342;&#2367;&#2344; = 131 - (18 &times; </span><span style=\"font-family: Roboto;\">7) = 131 - 126 = 5 </span></p>\r\n<p><span style=\"font-family: Palanquin;\">&#2340;&#2379;, 28 &#2344;&#2357;&#2306;&#2348;&#2352; 2021 &#2325;&#2379; &#2360;&#2346;&#2381;&#2340;&#2366;&#2361; &#2325;&#2366; &#2342;&#2367;&#2344; = &#2350;&#2306;&#2327;&#2354;&#2357;&#2366;&#2352; + 5 odd days = &#2352;&#2357;&#2367;&#2357;&#2366;&#2352;</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "24",
                    section: "6",
                    question_en: " <p>24.</span><span style=\"font-family:Palanquin\"> In a certain code language, ‘RACE’ is written as ‘AREC’, and ‘PEAK’ is written as ‘EPKA’. How will ‘CORE’ be written in that language?</span></p>",
                    question_hi: " <p>24.</span><span style=\"font-family:Palanquin\"> एक निश्चित कोड भाषा में, \'RACE\' को \'AREC\' लिखा जाता है, और \'PEAK\' को \'EPKA\' लिखा जाता है। उसी भाषा में \'CORE\' कैसे लिखा जाएगा ?</span></p>",
                    options_en: [" <p> OERC</span></p>", " <p> ECRE</span></p>", 
                                " <p> EORC</span></p>", " <p> OCER</span></p>"],
                    options_hi: [" <p> OERC  </span></p>", " <p> ECRE   </span></p>",
                                " <p> EORC   </span></p>", " <p> OCER</span></p>"],
                    solution_en: " <p>24.(d)</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669441637/word/media/image5.png\"/></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669441637/word/media/image29.png\"/></p> <p><span style=\"font-family:Roboto\">Similarly,</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669441637/word/media/image15.png\"/></p>",
                    solution_hi: " <p>24.(d)</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669441637/word/media/image5.png\"/><span style=\"font-family:Roboto\">     ,           </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669441637/word/media/image29.png\"/></p> <p><span style=\"font-family:Palanquin\">इसी तरह,</span></p> <p><span style=\"font-family:Roboto\">                    </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669441637/word/media/image15.png\"/></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "25",
                    section: "misc",
                    question_en: "<p><span style=\"font-family: Roboto;\">25.</span><span style=\"font-family: Roboto;\"> If &lsquo;A&rsquo; denotes &lsquo;+&rsquo;, &lsquo;B&rsquo; denotes &lsquo;&times;&rsquo;, &lsquo;C&rsquo; denotes &lsquo;&ndash;&rsquo; and &lsquo;D&rsquo; denotes &lsquo;&divide;&rsquo;, then what will come in place of &lsquo;?&rsquo; in the following equation?</span></p>\r\n<p><span style=\"font-family: Roboto;\">71 A 3 B 2 C 6 B (65 D 13) C 7 B4 = ?</span></p>\n",
                    question_hi: "<p>25. <span style=\"font-family: Palanquin;\">&#2351;&#2342;&#2367; \'A\' &#2325;&#2366; &#2309;&#2352;&#2381;&#2341; \'+\', \'B\' &#2325;&#2366; &#2309;&#2352;&#2381;&#2341; \'&times;\', \'C\' &#2325;&#2366; &#2309;&#2352;&#2381;&#2341; \'-\' &#2324;&#2352; \'D\' &#2325;&#2366; &#2309;&#2352;&#2381;&#2341; \'&divide;\' &#2361;&#2376;, &#2340;&#2379; &#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340; &#2360;&#2350;&#2368;&#2325;&#2352;&#2339; &#2350;&#2375;&#2306; \'?\' &#2325;&#2375; &#2360;&#2381;&#2341;&#2366;&#2344; &#2346;&#2352; &#2325;&#2381;&#2351;&#2366; &#2310;&#2319;&#2327;&#2366;?</span></p>\r\n<p><span style=\"font-family: Roboto;\">71 A 3 B 2 C 6 B (65 D 13) C 7 B 4 = ?</span></p>\n",
                    options_en: ["<p>18</p>\n", "<p>19</p>\n", 
                                "<p>44</p>\n", "<p>14</p>\n"],
                    options_hi: ["<p>18</p>\n", "<p>19</p>\n",
                                "<p>44</p>\n", "<p>14</p>\n"],
                    solution_en: "<p>25.(b)</p>\r\n<p><span style=\"font-family: Roboto;\">71 A 3 B 2 C 6 B (65 D 13) C 7 B 4</span></p>\r\n<p><span style=\"font-family: Roboto;\">Applying the directions in above expression, we get</span></p>\r\n<p><span style=\"font-family: Roboto;\">71 + 3 &times; </span><span style=\"font-family: Roboto;\">2 - 6 &times; </span><span style=\"font-family: Roboto;\">(65 &divide; </span><span style=\"font-family: Roboto;\">13) - 7 </span><span style=\"font-family: Roboto;\"> 4</span></p>\r\n<p><span style=\"font-family: Roboto;\">= 71 + 3 &times;</span><span style=\"font-family: Roboto;\">2 - 6 &times; </span><span style=\"font-family: Roboto;\">(5) - 7 &times;&nbsp;</span><span style=\"font-family: Roboto;\"> 4</span></p>\r\n<p><span style=\"font-family: Roboto;\">= 71 + 6 - 30 - 28</span></p>\r\n<p><span style=\"font-family: Roboto;\">= 77 - 58</span></p>\r\n<p><span style=\"font-family: Roboto;\">= 19</span></p>\n",
                    solution_hi: "<p>25.(b)</p>\r\n<p><span style=\"font-family: Roboto;\">71 A 3 B 2 C 6 B (65 D 13) C 7 B 4</span></p>\r\n<p><span style=\"font-family: Palanquin;\">&#2313;&#2346;&#2352;&#2379;&#2325;&#2381;&#2340; &#2357;&#2381;&#2351;&#2306;&#2332;&#2325; &#2350;&#2375;&#2306; &#2344;&#2367;&#2352;&#2381;&#2342;&#2375;&#2358;&#2379;&#2306; &#2325;&#2379; &#2354;&#2366;&#2327;&#2370; &#2325;&#2352;&#2344;&#2375; &#2346;&#2352;, &#2361;&#2350; &#2346;&#2381;&#2352;&#2366;&#2346;&#2381;&#2340; &#2325;&#2352;&#2340;&#2375; &#2361;&#2376;&#2306;</span></p>\r\n<p><span style=\"font-family: Roboto;\">71 + 3 &times; </span><span style=\"font-family: Roboto;\">2 - 6 &times; </span><span style=\"font-family: Roboto;\">(65 &divide; </span><span style=\"font-family: Roboto;\">13) - 7 &times; </span><span style=\"font-family: Roboto;\">4</span></p>\r\n<p><span style=\"font-family: Roboto;\">= 71 + 3 &times; </span><span style=\"font-family: Roboto;\">2 - 6 &times;</span><span style=\"font-family: Roboto;\">(5) - 7 &times;</span><span style=\"font-family: Roboto;\">&nbsp;4</span></p>\r\n<p><span style=\"font-family: Roboto;\">= 71 + 6 - 30 - 28 = 77 - 58 = 19</span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
           // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${question.question_en}</div>
                        <div class="hi" style="display:none">${question.question_hi}</div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                        <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>