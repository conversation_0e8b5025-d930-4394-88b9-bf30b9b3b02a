<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">25:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 50</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">50</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 25 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["18"] = {
                name: "General Awareness",
                start: 0,
                end: 48
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="18">General Awareness</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 49,
                end: 49
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "18",
                    question_en: "<p>1. A 20-page autobiography,&rsquo; Waiting for a Visa&rsquo;, is written by ___________, which is about his experiences with untouchability.</p>",
                    question_hi: "<p>1. एक 20-पेज की आत्मकथा, \'वेटिंग फॉर ए वीजा\' ___________ द्वारा लिखी गई है, जो अस्पृश्यता के साथ उनके अनुभवों के बारे में है।</p>",
                    options_en: ["<p>Jyotiba Phule</p>", "<p>Gopal Krishna Gokhale</p>", 
                                "<p>Bhimrao Ramji Ambedkar</p>", "<p>Mahatma Gandhi</p>"],
                    options_hi: ["<p>ज्योतिबा फुले</p>", "<p>गोपाल कृष्ण गोखले</p>",
                                "<p>भीमराव रामजी अंबेडकर</p>", "<p>महात्मा गांधी</p>"],
                    solution_en: "<p>1.(c) <strong>Bhimrao Ramji Ambedkar. </strong>Personalities and their Autobiographies: Mohandas Karamchand Gandhi - &ldquo;The Story of My Experiments with Truth&rdquo;, Jawaharlal Nehru- &ldquo;An Autobiography&rdquo;, Anna Chandy - &ldquo;Atmakatha&rdquo;, B. V. Acharya- &ldquo;All from Memory&rdquo;, Abhinav Bindra- &ldquo;A Shot at History&rdquo;.</p>",
                    solution_hi: "<p>1.(c) <strong>भीमराव रामजी अम्बेडकर</strong>। व्यक्तित्व एवं उनकी आत्मकथाएँ: मोहनदास करमचंद गांधी - द स्टोरी ऑफ़ माय एक्सपेरिमेंट्स विथ ट्रुथ, जवाहरलाल नेहरू - &ldquo;एन ऑटोबायोग्राफी&rdquo;, अन्ना चांडी - &ldquo;आत्मकथा&rdquo;, बी. वी. आचार्य - &ldquo;ऑल फ्रॉम मेमोरी&rdquo;, अभिनव बिंद्रा - ए शॉट एट हिस्ट्री।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "2",
                    section: "18",
                    question_en: "<p>2. Terekhol, Chapora, Mapusa, Sal, Zuari etc. are the major rivers of which state?</p>",
                    question_hi: "<p>2. तेरेखोल, चापोरा, मापुसा, साल, जुवारी आदि किस राज्य की प्रमुख नदियाँ हैं?</p>",
                    options_en: ["<p>Telangana</p>", "<p>Goa</p>", 
                                "<p>Chhattisgarh</p>", "<p>Gujarat</p>"],
                    options_hi: ["<p>तेलंगाना</p>", "<p>गोवा</p>",
                                "<p>छत्तीसगढ</p>", "<p>गुजरात</p>"],
                    solution_en: "<p>2.(b) <strong>Goa</strong>. Major rivers in states: Telangana - Godavari, Krishna, Musi, and Tungabhadra. Chhattisgarh - Mahanadi, Indravati and Shivnath. Gujarat - Narmada, Tapi, Mahi, and Sabarmati.</p>",
                    solution_hi: "<p>2.(b) <strong>गोवा</strong>। राज्यों की प्रमुख नदियाँ: तेलंगाना - गोदावरी, कृष्णा, मुसी और तुंगभद्रा। छत्तीसगढ़ - महानदी, इंद्रावती और शिवनाथ। गुजरात - नर्मदा, तापी, माही, और साबरमती ।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "3",
                    section: "18",
                    question_en: "<p>3. Which of the below mentioned items are NOT the part of revenue expenditure as per government budget?</p>",
                    question_hi: "<p>3. निम्नलिखित मदों में से कौन-सा सरकार के बजट के अनुसार राजस्व व्यय का भाग नहीं है?</p>",
                    options_en: ["<p>Infrastructural Development Expenditure</p>", "<p>Salaries and Pensions</p>", 
                                "<p>Subsidies</p>", "<p>Interest payments</p>"],
                    options_hi: ["<p>अवसंरचनात्मक विकास व्यय</p>", "<p>वेतन और पेंशन</p>",
                                "<p>सब्सिडी</p>", "<p>ब्याज भुगतान</p>"],
                    solution_en: "<p>3.(a) <strong>Infrastructural Development Expenditure. </strong>Revenue expenditure: This includes expenses incurred by the government on its day-to-day operations and obligations. It primarily covers non-asset-creating activities. Examples- Salaries and pensions, Subsidies, Interest payments.</p>",
                    solution_hi: "<p>3.(a) <strong>अवसंरचनात्मक विकास व्यय। </strong>राजस्व व्यय: इसमें सरकार द्वारा अपने दिन-प्रतिदिन के कार्यों और अनुबंधों पर लागतों को शामिल किया जाता है। यह मुख्य रूप से निगमित निवेश-सृजन गतिविधियों को कवर करता है। उदाहरण - वेतन और पेंशन, सब्सिडी, ब्याज भुगतान।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "4",
                    section: "18",
                    question_en: "<p>4. Which of the following Constitutional Amendment Acts of the Indian Constitution facilitated the appointment of the same person as governor for two or more states?</p>",
                    question_hi: "<p>4. भारतीय संविधान के निम्नलिखित में से किस संवैधानिक संशोधन अधिनियम ने एक ही व्यक्ति को दो या दो से अधिक राज्यों के राज्यपाल के रूप में नियुक्त करने की सुविधा प्रदान की?</p>",
                    options_en: ["<p>7<sup>th</sup></p>", "<p>5<sup>th</sup></p>", 
                                "<p>8<sup>th</sup></p>", "<p>10<sup>th</sup></p>"],
                    options_hi: ["<p>7वाँ</p>", "<p>5वाँ</p>",
                                "<p>8वाँ</p>", "<p>10वाँ</p>"],
                    solution_en: "<p>4.(a) <strong>7<sup>th</sup> </strong>Constitutional Amendment Act of 1956. 8th Constitutional Amendment Act of 1960 - It extended the reservation of seats for Scheduled Castes, Scheduled Tribes, and Anglo-Indians in the Lok Sabha and State Legislative Assemblies until 1970. 10th Constitutional Amendment Act of 1961- Incorporation of Dadra, Nagar and Haveli as a Union Territory, consequent to acquisition from Portugal.</p>",
                    solution_hi: "<p>4.(a) <strong>7वां</strong> संवैधानिक संशोधन अधिनियम 1956। 8वाँ संवैधानिक संशोधन अधिनियम 1960 - यह लोकसभा और राज्य विधानसभाओं में अनुसूचित जाति, अनुसूचित जनजाति और एंग्लो-इंडियन के लिए सीटों का आरक्षण 1970 तक बढ़ाया। 10वां संवैधानिक संशोधन अधिनियम 1961 - पुर्तगाल से अधिग्रहण के परिणामस्वरूप दादर, नगर और हवेली को केंद्र शासित प्रदेश के रूप में शामिल किया गया।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "5",
                    section: "18",
                    question_en: "<p>5. Choose the correct group of greenhouse gases.</p>",
                    question_hi: "<p>5. ग्रीनहाउस गैसों का सही समूह चुनें।</p>",
                    options_en: ["<p>CH<sub>4</sub>, O<sub>2</sub>, CO<sub>2</sub>, N<sub>2</sub></p>", "<p>CH<sub>4</sub>, O<sub>2</sub>, CO<sub>2</sub>, O<sub>3</sub></p>", 
                                "<p>CH<sub>4</sub>, N<sub>2</sub>O, CO<sub>2</sub>, O<sub>3</sub></p>", "<p>H<sub>2</sub>, O<sub>2</sub>, CO<sub>2</sub>, N<sub>2</sub></p>"],
                    options_hi: ["<p>CH<sub>4</sub>, O<sub>2</sub>, CO<sub>2</sub>, N<sub>2</sub></p>", "<p>CH<sub>4</sub>, O<sub>2</sub>, CO<sub>2</sub>, O<sub>3</sub></p>",
                                "<p>CH<sub>4</sub>, N<sub>2</sub>O, CO<sub>2</sub>, O<sub>3</sub></p>", "<p>H<sub>2</sub>, O<sub>2</sub>, CO<sub>2</sub>, N<sub>2</sub></p>"],
                    solution_en: "<p>5.(c) <strong>CH<sub>4</sub>, N<sub>2</sub>O, CO<sub>2</sub>, O<sub>3</sub></strong>. Greenhouse gases - It absorbs infrared radiation (net heat energy) emitted from Earth&rsquo;s surface and reradiates it back to Earth&rsquo;s surface. Kyoto Protocol - It operationalizes the United Nations Framework Convention on Climate Change by committing industrialized countries and economies in transition to limit and reduce greenhouse gases (GHG) emissions.</p>",
                    solution_hi: "<p>5.(c) <strong>CH<sub>4</sub>, N<sub>2</sub>O, CO<sub>2</sub>, O<sub>3</sub>.</strong> ग्रीनहाउस गैसें - यह पृथ्वी की सतह से उत्सर्जित अवरक्त विकिरण (शुद्ध ऊष्मीय ऊर्जा) को अवशोषित करती है और इसे वापस पृथ्वी की सतह पर ले जाती हैं। क्योटो प्रोटोकॉल - यह ग्रीनहाउस गैसों (GHG) उत्सर्जन को सीमित करने और कम करने के लिए औद्योगिक देशों और अर्थव्यवस्थाओं को प्रतिबद्ध करके जलवायु परिवर्तन पर संयुक्त राष्ट्र फ्रेमवर्क कन्वेंशन का संचालन करता है।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "6",
                    section: "18",
                    question_en: "<p>6. In which year did India\'s &lsquo;National Food for Work Programme&rsquo; begin?</p>",
                    question_hi: "<p>6. भारत का \'नेशनल फूड फॉर वर्क प्रोग्राम\' किस वर्ष शुरू किया गया था?</p>",
                    options_en: ["<p>2003</p>", "<p>2002</p>", 
                                "<p>2004</p>", "<p>2000</p>"],
                    options_hi: ["<p>2003 में</p>", "<p>2002 में</p>",
                                "<p>2004 में</p>", "<p>2000 में</p>"],
                    solution_en: "<p>6.(c) <strong>2004</strong>. National Food for Work Programme (NFWP) - It was launched in India on November 14, 2004, to generate supplementary wage employment and ensure food security in the 150 most backward districts. It provided food grains as wages to workers involved in labor-intensive projects.</p>",
                    solution_hi: "<p>6.(c) <strong>2004</strong>. नेशनल फूड फॉर वर्क प्रोग्राम (NFWP) - इसे भारत में 14 नवंबर 2004 को पूरक वेतन रोजगार उत्पन्न करने और 150 सबसे पिछड़े जिलों में खाद्य सुरक्षा सुनिश्चित करने के लिए लॉन्च किया गया था। इसने श्रम प्रधान परियोजनाओं में शामिल श्रमिकों को मजदूरी के रूप में खाद्यान्न प्रदान किया।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "7",
                    section: "18",
                    question_en: "<p>7. Which of the following was NOT a disadvantage of the green revolution?</p>",
                    question_hi: "<p>7. निम्नलिखित में से कौन-सी हरित क्रांति की हानि नहीं थी?</p>",
                    options_en: ["<p>High use of fertilisers and pesticides resulted in health illnesses.</p>", "<p>Most of the crops introduced during green revolution were intensive crops.</p>", 
                                "<p>It promoted extensive use of chemicals.</p>", "<p>It reduced India&rsquo;s import of food grains</p>"],
                    options_hi: ["<p>उर्वरकों और कीटनाशकों के उच्च उपयोग के परिणामस्वरूप गंभीर स्वास्थ्य बीमारियां हुईं।</p>", "<p>हरित क्रांति के दौरान पेश की गई अधिकांश फसलें गहन फसलें थी।</p>",
                                "<p>इसने रसायनों के व्यापक उपयोग को बढ़ावा दिया।</p>", "<p>इसने भारत का अनाज आयात कम कर दिया था।</p>"],
                    solution_en: "<p>7.(d) The Green Revolution, emphasizing high-yielding variety (HYV) seeds, aimed to address food shortages globally. Its primary goals were to increase food grain production, reduce poverty, and promote self-reliance in agriculture in developing countries.</p>",
                    solution_hi: "<p>7.(d) हरित क्रांति, उच्च उपज वाले किस्म (HYV) बीजों पर जोर देती है, जिसका उद्देश्य विश्व स्तर पर भोजन की कमी को दूर करना है। इसका प्राथमिक लक्ष्य खाद्यान्न उत्पादन बढ़ाना, गरीबी कम करना और विकासशील देशों में कृषि में आत्मनिर्भरता को बढ़ावा देना था।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "8",
                    section: "18",
                    question_en: "<p>8. Terraces that are level on opposite sides of the valley are referred to as ______ terraces.</p>",
                    question_hi: "<p>8. घाटी के विपरीत किनारों पर समतल होने वाली वेदिकाओं को, ________ वेदिकाओं के रूप में संदर्भित किया जाता है।</p>",
                    options_en: ["<p>horizontal</p>", "<p>paired</p>", 
                                "<p>symmetrical</p>", "<p>triangular</p>"],
                    options_hi: ["<p>क्षैतिज</p>", "<p>युग्&zwj;मित</p>",
                                "<p>सममित</p>", "<p>त्रिभुजीय</p>"],
                    solution_en: "<p>8.(b) <strong>paired </strong>terraces are terraces at the same elevation on opposite sides of a valley, often formed by rivers or glaciers, appearing \"paired\" due to their mirrored positions. Symmetrical terrace - A developed on the other side of the river forming a narrow elevated area that seems to be cut by the river.</p>",
                    solution_hi: "<p>8.(b) <strong>युग्&zwj;मित </strong>वेदिकाओं घाटी के विपरीत किनारों पर समान ऊंचाई पर स्थित वेदिकाओं हैं, जो अक्सर नदियों या ग्लेशियरों द्वारा बनाई जाती हैं, जो उनकी प्रतिबिंबित स्थिति के कारण \"युग्मित\" दिखाई देती हैं। सममित वेदिका - नदी के दूसरी ओर एक संकीर्ण ऊंचा क्षेत्र बनाते हुए विकसित किया गया है जो नदी द्वारा काटा हुआ प्रतीत होता है।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "9",
                    section: "18",
                    question_en: "<p>9. Which of these festivals commemorates the resurrection of Jesus from the dead?</p>",
                    question_hi: "<p>9. निम्न में से कौन सा त्योहार मृतक से यीशु के पुनरुत्थान की याद दिलाता है?</p>",
                    options_en: ["<p>Good Friday</p>", "<p>New Year</p>", 
                                "<p>Christmas</p>", "<p>Easter</p>"],
                    options_hi: ["<p>गुड फ्राइडे</p>", "<p>नव वर्ष</p>",
                                "<p>क्रिसमस</p>", "<p>ईस्टर</p>"],
                    solution_en: "<p>9.(d) <strong>Easter</strong>. Good Friday: Commemorating the crucifixion and death of Jesus Christ. Christmas: This holiday celebrates the birth of Jesus.</p>",
                    solution_hi: "<p>9.(d) <strong>ईस्टर</strong>। गुड फ्राइडे - ईसा मसीह के सूली पर चढ़ने और मृत्यु की याद में मनाया जाता है। क्रिसमस: येशु मसीह के जन्म का जश्न मनाया जाता है।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "10",
                    section: "18",
                    question_en: "<p>10. Rural Self-Employment Training Institute is an initiative of which Ministry of the Government of India?</p>",
                    question_hi: "<p>10. ग्रामीण स्व-रोजगार प्रशिक्षण संस्थान भारत सरकार के किस मंत्रालय की एक पहल है?</p>",
                    options_en: ["<p>Ministry of Agriculture</p>", "<p>Ministry of Rural Development</p>", 
                                "<p>Ministry of Labour and Employment</p>", "<p>Ministry of Finance</p>"],
                    options_hi: ["<p>कृषि मंत्रालय</p>", "<p>ग्रामीण विकास मंत्रालय</p>",
                                "<p>श्रम और रोजगार मंत्रालय</p>", "<p>वित्त मंत्रालय</p>"],
                    solution_en: "<p>10.(b) <strong>Ministry of Rural Development.</strong> Rural Self-Employment Training Institute (RSETI) - It aims to promote self-employment and entrepreneurial skills among rural youth through skill development training and credit assistance.</p>",
                    solution_hi: "<p>10.(b)<strong> ग्रामीण विकास मंत्रालय</strong>। ग्रामीण स्व-रोजगार प्रशिक्षण संस्थानों (RSETI) - इसका उद्देश्य कौशल विकास प्रशिक्षण और ऋण सहायता के माध्यम से ग्रामीण युवाओं के बीच स्वरोजगार और उद्यमशीलता कौशल को बढ़ावा देना है।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "11",
                    section: "18",
                    question_en: "<p>11. Which of the following statements is correct with respect to the Attorney General of India?</p>",
                    question_hi: "<p>11. निम्नलिखित में से कौन-सा कथन भारत के महान्यायवादी के संबंध में सही है?</p>",
                    options_en: ["<p>He/She can participate in sessions of the parliament.</p>", "<p>He/She can vote in the Lok Sabha during the passage of confidence motion and&nbsp;no-confidence motion.</p>", 
                                "<p>He/She cannot participate in joint sitting of the parliament.</p>", "<p>He/She does not enjoy any immunity or privileges as compared to members of the parliament.</p>"],
                    options_hi: ["<p>वह संसद के सत्रों में भाग ले सकता/सकती है।</p>", "<p>वह विश्वास प्रस्ताव और अविश्वास प्रस्ताव पारित होने के दौरान लोकसभा में मतदान कर सकता/सकती है।</p>",
                                "<p>वह संसद की संयुक्त बैठक में भाग नहीं ले सकता/सकती।</p>", "<p>उसे संसद के सदस्यों की तुलना में कोई छूट या विशेषाधिकार प्राप्त नहीं होता है।</p>"],
                    solution_en: "<p>11.(a) Article 88 of the Indian Constitution allows the Attorney General to participate in and speak during proceedings of both Houses of Parliament and joint sittings, as well as in parliamentary committees, but without voting rights.</p>",
                    solution_hi: "<p>11.(a) भारतीय संविधान का अनुच्छेद 88 अटॉर्नी जनरल को संसद के दोनों सदनों और संयुक्त बैठकों के साथ-साथ संसदीय समितियों की कार्यवाही में भाग लेने और बोलने की अनुमति देता है, लेकिन मतदान के अधिकार के बिना।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "12",
                    section: "18",
                    question_en: "<p>12. Consider the below mentioned statements and select the correct option with respect to the Indian folk and tribal dance forms: <br>i) Matki is the folk dance of Assam. <br>ii) Thang Ta is the folk dance of Manipur.<br>iii) Fugdi is the folk dance of Goa.</p>",
                    question_hi: "<p>12. नीचे दिए गए कथनों पर विचार कीजिए और भारतीय लोक और जनजातीय नृत्य शैलियों के संबंध में सही विकल्प का चयन कीजिए। <br>i) मटकी असम का लोक नृत्य है। <br>ii) थांग ता (Thang Ta) मणिपुर का लोक नृत्य है। <br>iii) फुगड़ी (Fugdi) गोवा का लोक नृत्य है।</p>",
                    options_en: ["<p>Only (ii) is right.</p>", "<p>Both (i) and (ii) are right.</p>", 
                                "<p>Both (ii) and (iii) are right.</p>", "<p>Only (i) is right.</p>"],
                    options_hi: ["<p>केवल (ii) सही है।</p>", "<p>(i) और (ii), दोनों सही हैं।</p>",
                                "<p>(ii) और (iii), दोनों सही हैं।</p>", "<p>केवल (i) सही है।</p>"],
                    solution_en: "<p>12.(c) <strong>Both (ii) and (iii) are right. </strong>Matki is the folk dance of Madhya Pradesh. Indian folk and tribal dances: Bihu Dance and Jhumur (Assam), Bardo Chham (Arunachal Pradesh), Raut Nacha (Chhattisgarh), Nati (Himachal Pradesh), Duffmuttu (Kerala).</p>",
                    solution_hi: "<p>12.(c) <strong>(ii) और (iii), दोनों सही हैं।</strong> मटकी मध्य प्रदेश का लोक नृत्य है। भारतीय लोक और जनजातीय नृत्य: बिहू नृत्य और झुमुर (असम), बार्डो छम (अरुणाचल प्रदेश), राऊत नाचा (छत्तीसगढ़), नाटी (हिमाचल प्रदेश), डफमुट्ट (केरल)।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "13",
                    section: "18",
                    question_en: "<p>13.The students in a physics lab have been asked to measure the amount of electric current in a circuit. Which device should they use?</p>",
                    question_hi: "<p>13. भौतिकी प्रयोगशाला में विद्यार्थियों को किसी परिपथ में विद्युत धारा की मात्रा मापने के लिए कहा गया है। उन्हें किस डिवाइस का उपयोग करना चाहिए?</p>",
                    options_en: ["<p>Altimeter</p>", "<p>Ammeter</p>", 
                                "<p>Ohmmeter</p>", "<p>Voltmeter</p>"],
                    options_hi: ["<p>अल्टीमीटर</p>", "<p>ऐमीटर</p>",
                                "<p>ओममीटर</p>", "<p>वोल्टमीटर</p>"],
                    solution_en: "<p>13.(b) <strong>Ammeter</strong>. The current is the flow of electrons whose unit is ampere. Altimeter: This device measures altitude. Ohmmeter: This device measures resistance. Voltmeter: This device measures voltage.</p>",
                    solution_hi: "<p>13 (b) <strong>ऐमीटर</strong>। धारा इलेक्ट्रॉनों का प्रवाह है जिसकी इकाई एम्पीयर है। अल्टीमीटर: यह उपकरण ऊंचाई मापता है। ओममीटर: यह उपकरण प्रतिरोध मापता है। वोल्टमीटर: यह उपकरण वोल्टेज मापता है।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "14",
                    section: "18",
                    question_en: "<p>14. Which American President has written &lsquo;Dreams from My Father&rsquo;, an autobiography?</p>",
                    question_hi: "<p>14. किस अमेरिकी राष्ट्रपति ने आत्मकथा \'ड्रीम्स फ्रॉम माई फादर\' (Dreams from My Father) लिखी है?</p>",
                    options_en: ["<p>George Bush</p>", "<p>John Kennedy</p>", 
                                "<p>Barack Obama</p>", "<p>George Washington</p>"],
                    options_hi: ["<p>जॉर्ज बुश (George Bush)</p>", "<p>जॉन केनेडी (John Kennedy)</p>",
                                "<p>बराक ओबामा (Barack Obama)</p>", "<p>जॉर्ज वाशिंगटन (George Washington)</p>"],
                    solution_en: "<p>14.(c) <strong>Barack Obama.</strong> He was the first African American (44th president) president of the United States. Autobiographies of Famous Politicians: &ldquo;Decision Points&rdquo; (George W. Bush), &ldquo;Autobiography 1743-1790&rdquo; (Thomas Jefferson), &ldquo;An American Life&rdquo; (Ronald Reagan), &ldquo;A long Walk To Freedom&rdquo; (Nelson Mandela), &ldquo;Wings Of Fire&rdquo; (A.P.J. Abdul Kalam).</p>",
                    solution_hi: "<p>14.(c) <strong>बराक ओबामा </strong>(Barack Obama)। वह संयुक्त राज्य अमेरिका के पहले अफ्रीकी अमेरिकी (44वें ) राष्ट्रपति थे। प्रसिद्ध राजनेताओं की आत्मकथाएँ: &ldquo;डिसीजन पॉइंट्स&rdquo; (जॉर्ज डब्ल्यू बुश), &ldquo;ऑटोबायोग्राफी 1743-1790&rdquo; (थॉमस जेफरसन), &ldquo;एन अमेरिकन लाइफ&rdquo; (रोनाल्ड रीगन), &ldquo;ए लॉन्ग वॉक टू फ्रीडम&rdquo; (नेल्सन मंडेला), &ldquo;विंग्स ऑफ फायर&rdquo; (ए.पी.जे. अब्दुल कलाम)।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "15",
                    section: "18",
                    question_en: "<p>15. Which Article of the Constitution of India mentions that the state shall make effective provision for securing public assistance in cases of unemployment, old age, sickness and disablement?</p>",
                    question_hi: "<p>15. भारतीय संविधान के किस अनुच्छेद में उल्लेख किया गया है कि राज्य बेरोजगारी, वृद्धावस्था, बीमारी और विकलांगता के मामलों में सार्वजनिक सहायता सुनिश्चित करने के लिए प्रभावी प्रावधान करेगा?</p>",
                    options_en: ["<p>Article 45</p>", "<p>Article 47</p>", 
                                "<p>Article 39</p>", "<p>Article 41</p>"],
                    options_hi: ["<p>अनुच्छेद 45</p>", "<p>अनुच्छेद 47</p>",
                                "<p>अनुच्छेद 39</p>", "<p>अनुच्छेद 41</p>"],
                    solution_en: "<p>15.(d)<strong> Article 41.</strong> Under the Directive Principles of State Policy (Part IV): Article 39 - Certain principles of policy to be followed by the State. Article 45 - Provision for early childhood care and education to children below the age of six years. Article 47- Duty of the State to raise the level of nutrition and the standard of living and to improve public health.</p>",
                    solution_hi: "<p>15.(d)<strong> अनुच्छेद 41</strong>. राज्य के नीति निर्देशक सिद्धांतों (भाग IV) के तहत: अनुच्छेद 39 - राज्य द्वारा पालन किए जाने वाले नीति के कुछ सिद्धांत। अनुच्छेद 45 - छह वर्ष से कम उम्र के बच्चों के लिए प्रारंभिक बाल्यावस्था देखभाल और शिक्षा का प्रावधान। अनुच्छेद 47- पोषण स्तर और जीवन स्तर को ऊपर उठाना तथा सार्वजनिक स्वास्थ्य में सुधार करना राज्य का कर्तव्य है।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "16",
                    section: "18",
                    question_en: "<p>16. In the game of javelin throw the weight of Javelin for women is:</p>",
                    question_hi: "<p>16. भाला फेंक खेल (javelin throw) में महिलाओं के लिए प्रयुक्त होने वाले भाले का वजन कितना होता है?</p>",
                    options_en: ["<p>600 gm</p>", "<p>800 gm</p>", 
                                "<p>700 gm</p>", "<p>825 gm</p>"],
                    options_hi: ["<p>600 gm</p>", "<p>800 gm</p>",
                                "<p>700 gm</p>", "<p>825 gm</p>"],
                    solution_en: "<p>16.(a) <strong>600 gm</strong>. The men\'s javelin throw weighs 800 grams and measures between 2.6 m and 2.7 m, while the women\'s javelin throw is between 2.2 m and 2.3 m.</p>",
                    solution_hi: "<p>16.(a) (a) <strong>600 gm. </strong>पुरुषों की भाला फेंक का वजन 800 ग्राम होता है और इसकी माप 2.6 मीटर से 2.7 मीटर के बीच होती है, जबकि महिलाओं की भाला फेंक 2.2 मीटर और 2.3 मीटर के बीच होती है।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "17",
                    section: "18",
                    question_en: "<p>17. The famous artist Tejan Bai is related to which of the following?</p>",
                    question_hi: "<p>17. प्रसिद्ध कलाकार तीजन बाई का संबंध निम्नलिखित में से किससे है?</p>",
                    options_en: ["<p>Pandwani</p>", "<p>comment</p>", 
                                "<p>Kalbelia</p>", "<p>Bhavai</p>"],
                    options_hi: ["<p>पंडवानी</p>", "<p>टिप्पनी</p>",
                                "<p>कालबेलिया</p>", "<p>भवाई</p>"],
                    solution_en: "<p>17.(a) <strong>Pandwani</strong>. Other Artists - Guru Jhaduram Dewangan, Lakshmi Bai Banjare, Meena Sahu. Teejan Bai is a famous artist of Chhattisgarh. He has been awarded the Padma Shri (1988), Padma Vibhushan (2019) and Sangeet Natak Akademi Award (1995).</p>",
                    solution_hi: "<p>17.(a) <strong>पंडवानी</strong>। अन्य कलाकार - गुरु झाडूराम देवांगन, लक्ष्मी बाई बंजारे, मीना साहू। तीजन बाई छत्तीसगढ़ की प्रसिद्ध कलाकार हैं। उन्हें पद्म श्री (1988), पद्म विभूषण (2019) और संगीत नाटक अकादमी पुरस्कार (1995) से सम्मानित किया गया है।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "18",
                    section: "18",
                    question_en: "<p>18. Liberalisation is a part of ________ reforms.</p>",
                    question_hi: "<p>18. उदारीकरण, ________ सुधारों का एक हिस्सा है।</p>",
                    options_en: ["<p>social</p>", "<p>political</p>", 
                                "<p>land</p>", "<p>economic</p>"],
                    options_hi: ["<p>सामाजिक</p>", "<p>राजनीतिक</p>",
                                "<p>भूमि</p>", "<p>आर्थिक</p>"],
                    solution_en: "<p>18.(d) <strong>economic</strong>. Liberalization: Reducing or removing government regulations and restrictions on economic activities, trade, and business operations. Comprehensive reform policies initiated in 1991.</p>",
                    solution_hi: "<p>18.(d) <strong>आर्थिक</strong>। उदारीकरण: आर्थिक गतिविधियों, व्यापार और व्यावसायिक संचालन पर सरकारी नियमों और प्रतिबंधों को कम करना या हटाना। 1991 में व्यापक सुधार नीतियां शुरू की गईं।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "19",
                    section: "18",
                    question_en: "<p>19. The victory of the ________ marked the foundation stone of the Delhi Sultanate in India.</p>",
                    question_hi: "<p>19. ________ की जीत ने भारत में दिल्ली सल्तनत की आधारशिला रखी।</p>",
                    options_en: ["<p>Afghans</p>", "<p>Arabs</p>", 
                                "<p>Persians</p>", "<p>Turks</p>"],
                    options_hi: ["<p>अफगानियों</p>", "<p>अरबों</p>",
                                "<p>फ़ारसियों</p>", "<p>तुर्कों</p>"],
                    solution_en: "<p>19.(d) <strong>Turks</strong>. The Delhi Sultanate, an Islamic empire based in Delhi, spanned much of the Indian subcontinent for 320 years from 1206 to 1526. It was founded by Qutb-ud-din Aibak in 1206, marking the beginning of the Mamluk Dynasty.</p>",
                    solution_hi: "<p>19.(d) <strong>तुर्कों</strong>। दिल्ली सल्तनत, दिल्ली में स्थित एक इस्लामी साम्राज्य, 1206 से 1526 तक 320 वर्षों तक भारतीय उपमहाद्वीप के अधिकांश हिस्से में फैला था। इसकी स्थापना कुतुब-उद-दीन ऐबक ने 1206 में मामलुक राजवंश की शुरुआत के रूप में की थी।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "20",
                    section: "18",
                    question_en: "<p>20. Goods and services that are provided by the government to all the people and whose consumption is not rivalrous are known as:</p>",
                    question_hi: "<p>20. वे वस्तुएँ और सेवाएँ जो सरकार द्वारा सभी लोगों को प्रदान की जाती हैं और जिनके उपभोग की कोई प्रतिद्वंद्विता नहीं है, क्&zwj;या कहलाती हैं?</p>",
                    options_en: ["<p>public goods</p>", "<p>final goods</p>", 
                                "<p>private goods</p>", "<p>intermediate goods</p>"],
                    options_hi: ["<p>सार्वजनिक माल</p>", "<p>अंतिम माल</p>",
                                "<p>निजी माल</p>", "<p>मध्&zwj;यवर्ती माल</p>"],
                    solution_en: "<p>20.(a)<strong> public goods. </strong>Final goods are consumed by individuals, not directly provided by the government. Private goods are also consumed by individuals, and their consumption is rivalrous. Intermediate goods are used to produce other goods, not directly consumed by individuals.</p>",
                    solution_hi: "<p>20.(a) <strong>सार्वजनिक माल।</strong> अंतिम वस्तुओं का उपभोग व्यक्तियों द्वारा किया जाता है, यह सीधे सरकार द्वारा प्रदान नहीं किया जाता है। निजी वस्तुओं का उपभोग भी व्यक्तियों द्वारा किया जाता है, और उनका उपभोग प्रतिद्वंद्वितापूर्ण होता है। मध्यवर्ती वस्तुओं का उपयोग अन्य वस्तुओं के उत्पादन के लिए किया जाता है, न कि सीधे व्यक्तियों द्वारा उपभोग किया जाता है।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "21",
                    section: "18",
                    question_en: "<p>21. Excessive use of pesticides, insecticides and inorganic fertilisers is not good for human health. This is due to the phenomena called:</p>",
                    question_hi: "<p>21. पीड़कनाशकों, कीटनाशकों और अकार्बनिक उर्वरकों का अत्यधिक उपयोग मानव स्वास्थ्य के लिए अच्छा नहीं है। ऐसा किस परिघटना के कारण है?</p>",
                    options_en: ["<p>biomagnification</p>", "<p>intoxication</p>", 
                                "<p>biotransformation</p>", "<p>biodilution</p>"],
                    options_hi: ["<p>जैव-आवर्धन (biomagnification)</p>", "<p>आविषांचन (intoxication)</p>",
                                "<p>जैव-रूपांतरण (biotransformation)</p>", "<p>जैव-तनूकरण (biodilution)</p>"],
                    solution_en: "<p>21.(a) <strong>Biomagnification</strong>. Increasing concentration of harmful substances up the food chain. Biodilution: Decreasing concentration of harmful substances up the food chain. Biotransformation: Chemical alteration of ingested substances by organisms.</p>",
                    solution_hi: "<p>21.(a) <strong>जैव-आवर्धन</strong> (Biomagnification)। खाद्य श्रृंखला (फूड चेन ) में हानिकारक पदार्थों की बढ़ती सांद्रता। जैव-तनूकरण: खाद्य श्रृंखला में हानिकारक पदार्थों की सांद्रता कम होना। जैव-रूपांतरण: जीवों द्वारा ग्रहण किए गए पदार्थों का रासायनिक परिवर्तन।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "22",
                    section: "18",
                    question_en: "<p>22. Padma Bhushan and Sangeet Natak Akademi award winner Rukmini Devi Arundale was an exponent of _________ .</p>",
                    question_hi: "<p>22. पद्म भूषण और संगीत नाटक अकादमी पुरस्कार विजेता रुक्मिणी देवी अरुंडेल _________ की प्रसिद्ध नृत्यांगना थीं।</p>",
                    options_en: ["<p>Bharatanatyam</p>", "<p>Kathakali</p>", 
                                "<p>Kathak</p>", "<p>Sattriya</p>"],
                    options_hi: ["<p>भरतनाट्यम</p>", "<p>कथकली</p>",
                                "<p>कथक</p>", "<p>सत्रिया</p>"],
                    solution_en: "<p>22.(a) <strong>Bharatanatyam</strong>. Traditional dance originating from Hindu temples in Tamil Nadu, performed mainly by women. Famous Artists: Meenakshi Sundaram Pillai, Bala Saraswati, Padma Subrahmanyam, and Alarmel Valli.</p>",
                    solution_hi: "<p>22.(a) <strong>भरतनाट्यम</strong>। तमिलनाडु के हिंदू मंदिरों से उत्पन्न पारंपरिक नृत्य, मुख्य रूप से महिलाओं द्वारा किया जाता है। प्रसिद्ध कलाकार: मीनाक्षी सुंदरम पिल्लई, बाला सरस्वती, पद्मा सुब्रमण्यम, और अलार्मेल वल्ली।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "23",
                    section: "18",
                    question_en: "<p>23. Which of the following structures stores carbohydrates?</p>",
                    question_hi: "<p>23. निम्नलिखित में से कौन-सी संरचना कार्बोहाइड्रेट का संग्रहण करती है?</p>",
                    options_en: ["<p>Aleuroplast</p>", "<p>Elaioplast</p>", 
                                "<p>Amyloplast</p>", "<p>Chloroplast</p>"],
                    options_hi: ["<p>एल्यूरोप्लास्ट</p>", "<p>इलायोप्लास्ट</p>",
                                "<p>एमाइलोप्लास्ट</p>", "<p>क्लोरोप्लास्ट</p>"],
                    solution_en: "<p>23.(c) <strong>Amyloplast</strong>. Plastids store carbohydrates as starch. Aleuroplast: Leucoplasts storing proteins. Elaioplast: Leucoplasts storing lipids, fats, and oils. Chloroplast: Organelles in plants and algae cells, responsible for photosynthesis.</p>",
                    solution_hi: "<p>23.(c) <strong>एमाइलोप्लास्ट</strong>। प्लास्टिड्स कार्बोहाइड्रेट को स्टार्च के रूप में संग्रहित करते हैं। एल्यूरोप्लास्ट: ल्यूकोप्लास्ट प्रोटीन का भंडारण करते हैं। इलायोंप्लास्ट: ल्यूकोप्लास्ट लिपिड, वसा और तेल का भंडारण करते हैं। क्लोरोप्लास्ट: पौधों और शैवाल कोशिकाओं में अंगक, प्रकाश संश्लेषण के लिए जिम्मेदार।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "24",
                    section: "18",
                    question_en: "<p>24. Who among the following Gupta emperors held the title of Vikramaditya?</p>",
                    question_hi: "<p>24. निम्नलिखित गुप्त सम्राटों में से किसने विक्रमादित्य की उपाधि धारण की थी?</p>",
                    options_en: ["<p>Chandragupta I</p>", "<p>Skandagupta</p>", 
                                "<p>Chandragupta II</p>", "<p>Budhagupta</p>"],
                    options_hi: ["<p>चन्द्रगुप्त प्रथम</p>", "<p>स्कन्दगुप्त</p>",
                                "<p>चंद्रगुप्त द्वितीय</p>", "<p>बुधगुप्त</p>"],
                    solution_en: "<p>24.(c)<strong> Chandragupta II </strong>- Next Gupta emperor after Samudragupta (335 - 375 CE) ruled during India\'s Golden Age (375 CE - 415 CE), known from coins and Supia pillar inscriptions. Chandragupta I: Started the Gupta Era. He assumed the title of &lsquo;Maharajadhiraja&lsquo;. Skandagupta was a &lsquo;Vaishnavite&lsquo;.</p>",
                    solution_hi: "<p>24.(c) <strong>चंद्रगुप्त द्वितीय </strong>- समुद्रगुप्त (335 - 375 CE) के बाद अगले गुप्त सम्राट ने भारत के स्वर्ण युग (375 CE- 415 CE) के दौरान शासन किया, जिसे सिक्कों और सुपिया स्तंभ शिलालेखों से जाना जाता है। चंद्रगुप्त प्रथम: गुप्त युग की शुरुआत की। उन्होंने \'महाराजाधिराज\' की उपाधि धारण की। स्कंदगुप्त एक \'वैष्णव\' थे।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "25",
                    section: "18",
                    question_en: "<p>25. Who among the following received the National Award for &lsquo;Best Music Direction&rsquo; in September 2022 for his song \'Marange Toh Vahin Jaa Kar\' in the documentary film \'1232 KMs\'?</p>",
                    question_hi: "<p>25. निम्नलिखित में से किसने सितंबर 2022 में डॉक्यूमेंट्री फिल्म \'1232 केएम\' (\'1232 KMs) में अपने गीत \'मरेंगे तो वहीं जा कर\' के लिए \'सर्वश्रेष्ठ संगीत निर्देशन\' का राष्ट्रीय पुरस्कार प्राप्त किया?</p>",
                    options_en: ["<p>Vishal Bharadwaj</p>", "<p>Swanand Kirkire</p>", 
                                "<p>Amit Trivedi</p>", "<p>Ajay Atul</p>"],
                    options_hi: ["<p>विशाल भारद्वाज</p>", "<p>स्वानंद किरकिरे</p>",
                                "<p>अमित त्रिवेदी</p>", "<p>अजय अतुल</p>"],
                    solution_en: "<p>25.(a) <strong>Vishal Bharadwaj. </strong>National Film Awards: Prominent Indian film award ceremony since 1954, administered by Indian government \'s Directorate of Film Festivals since 1973, along with IFFI and Indian Panorama. 69th National Film Awards: Best Actor - Allu Arjun (Pushpa), Best Feature Film - Rocketry, Best Director - Nikhil Mahajan (Godavari), Best Hindi Film - Sardar Udham.</p>",
                    solution_hi: "<p>25.(a) <strong>विशाल भारद्वाज।</strong> राष्ट्रीय फिल्म पुरस्कार: 1954 से प्रमुख भारतीय फिल्म पुरस्कार समारोह, आईएफएफआई और भारतीय पैनोरमा के साथ, 1973 से भारत सरकार के फिल्म महोत्सव निदेशालय द्वारा प्रशासित। 69वें राष्ट्रीय फिल्म पुरस्कार: सर्वश्रेष्ठ अभिनेता - अल्लू अर्जुन (पुष्पा), सर्वश्रेष्ठ फीचर फिल्म - रॉकेट्री, सर्वश्रेष्ठ निर्देशक - निखिल महाजन (गोदावरी), सर्वश्रेष्ठ हिंदी फिल्म - सरदार उधम।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "26",
                    section: "18",
                    question_en: "<p>26. After which infamous incident of 1919 was the Hunter Commission appointed?</p>",
                    question_hi: "<p>26. 1919 की किस कुख्यात घटना के बाद हंटर आयोग नियुक्त किया गया था?</p>",
                    options_en: ["<p>Indian educational reforms</p>", "<p>Kheda mill strike</p>", 
                                "<p>Jallianwala Bagh massacre</p>", "<p>Komagata Maru</p>"],
                    options_hi: ["<p>भारतीय शैक्षिक सुधार</p>", "<p>खेड़ा मिल हड़ताल</p>",
                                "<p>जलियांवाला बाग हत्याकांड</p>", "<p>कामागाटा मारू</p>"],
                    solution_en: "<p>26.(c) <strong>Jallianwala Bagh massacre.</strong> Gruesome execution of hundreds on 13th April 1919 by British Indian army under Brigadier R.E.H Dyer. Kheda Satyagraha (1918): Focused on the peasant-Patidar community who refused to accept a 23% tax increase enforced on them despite a devastating crop failure and plague and cholera outbreak. Komagata Maru: Japanese ship, Indians\' immigration attempt to Canada in April 1914.</p>",
                    solution_hi: "<p>26.(c) <strong>जलियांवाला बाग हत्याकांड।</strong> 13 अप्रैल 1919 को ब्रिगेडियर आर.ई.एच डायर के नेतृत्व में ब्रिटिश भारतीय सेना द्वारा सैकड़ों लोगों की भयानक हत्या। किसान-पाटीदार समुदाय पर केंद्रित था, जिन्होंने विनाशकारी फसल विफलता और प्लेग और हैजा के प्रकोप के बावजूद उन पर लागू 23% कर वृद्धि को स्वीकार करने से इनकार कर दिया था। कोमागाटा मारू: जापानी जहाज, अप्रैल 1914 में भारतीयों का कनाडा में आप्रवासन का प्रयास।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "27",
                    section: "18",
                    question_en: "<p>27. The first National Winter Games were hosted by _________ .</p>",
                    question_hi: "<p>27. प्रथम राष्ट्रीय शीतकालीन खेलों का आयोजन _________ में किया गया था।</p>",
                    options_en: ["<p>Gulmarg</p>", "<p>Manali</p>", 
                                "<p>Srinagar</p>", "<p>Auli</p>"],
                    options_hi: ["<p>गुलमर्ग</p>", "<p>मनाली</p>",
                                "<p>श्रीनगर</p>", "<p>ऑली</p>"],
                    solution_en: "<p>27.(a) <strong>Gulmarg</strong>. Khelo India Winter Games: National grassroots winter games of India. First held in 2020, with two legs: Khelo India Ladakh Winter Games (Leh) and Khelo India Jammu and Kashmir Winter Games (Gulmarg).</p>",
                    solution_hi: "<p>27.(a) <strong>गुलमर्ग</strong>। खेलो इंडिया शीतकालीन खेल: भारत के राष्ट्रीय जमीनी स्तर के शीतकालीन खेल। पहली बार 2020 में दो चरणों में आयोजित किया गया: खेलो इंडिया लद्दाख शीतकालीन खेल (लेह) और खेलो इंडिया जम्मू और कश्मीर शीतकालीन खेल (गुलमर्ग)।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "28",
                    section: "18",
                    question_en: "<p>28. The Statue of Unity is located at the bank of which of these rivers?</p>",
                    question_hi: "<p>28. स्टैच्यूऑफ यूनिटी निम्न में से किस नदी के तट पर स्थित है?</p>",
                    options_en: ["<p>Narmada</p>", "<p>Krishna</p>", 
                                "<p>Yamuna</p>", "<p>Ganga</p>"],
                    options_hi: ["<p>नर्मदा</p>", "<p>कृष्णा</p>",
                                "<p>यमुना</p>", "<p>गंगा</p>"],
                    solution_en: "<p>28.(a) <strong>Narmada</strong>. Statue of Unity: World\'s tallest statue, 182 meters (597 ft) high, on Narmada River facing Sardar Sarovar Dam. It was designed by Ram V. Sutar and constructed by Larsen and Toubro on Sadhu Bet island. Inaugurated on: 31 October 2018.</p>",
                    solution_hi: "<p>28.(a) <strong>नर्मदा</strong>। स्टैच्यू ऑफ यूनिटी: दुनिया की सबसे ऊंची प्रतिमा, 182 मीटर (597 फीट) ऊंची, सरदार सरोवर बांध के सामने नर्मदा नदी पर है। इसे राम वी. सुतार द्वारा डिजाइन किया गया था और साधु बेट द्वीप पर लार्सन एंड टुब्रो द्वारा निर्मित किया गया था। उद्घाटन: 31 अक्टूबर 2018.</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "29",
                    section: "18",
                    question_en: "<p>29. Which of the following parts of the Constitution of India has been referred to as the \'conscience of the Indian Constitution\'?</p>",
                    question_hi: "<p>29. भारत के संविधान के निम्नलिखित में से किस भाग को \'भारतीय संविधान की अंतरात्मा\' के रूप में संदर्भित किया गया है?</p>",
                    options_en: ["<p>Centre-State relations</p>", "<p>Fundamental Duties</p>", 
                                "<p>Fundamental rights</p>", "<p>Citizenship</p>"],
                    options_hi: ["<p>केंद्र-राज्य संबंध</p>", "<p>मौलिक कर्तव्य</p>",
                                "<p>मौलिक अधिकार</p>", "<p>नागरिकता</p>"],
                    solution_en: "<p>29.(c) <strong>Fundamental rights -</strong> Enshrined in Indian Constitution Part III (Articles 12-35). Guarantees human rights, equality, freedom, and dignity for citizens, acting as a shield against arbitrary state power. Citizenship (Part II, Articles 5-11) defines who is an Indian citizen. Centre-State relations (Part XI, Articles 239-255) define the distribution of power between the central and state governments.</p>",
                    solution_hi: "<p>29.(c) <strong>मौलिक अधिकार - </strong>भारतीय संविधान भाग III (अनुच्छेद 12-35) में निहित। मनमाने बचाव से राज्य सत्ता के खिलाफ ढाल के रूप में कार्य करते हुए, नागरिकों के लिए मानवाधिकार, समानता, स्वतंत्रता और सम्मान की गारंटी देता है। नागरिकता (भाग II, अनुच्छेद 5-11) परिभाषित करती है कि भारतीय नागरिक कौन है। केंद्र-राज्य संबंध (भाग XI, अनुच्छेद 239-255) केंद्र और राज्य सरकारों के बीच शक्ति के वितरण को परिभाषित करते हैं।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "30",
                    section: "18",
                    question_en: "<p>30. Which of the following patterns was followed in Mohenjodaro town planning?</p>",
                    question_hi: "<p>30. मोहनजोदड़ो नगर नियोजन में निम्नलिखित में से किस प्रकार के पैटर्न का पालन किया गया था?</p>",
                    options_en: ["<p>circular</p>", "<p>rectangular</p>", 
                                "<p>grid</p>", "<p>cylindrical</p>"],
                    options_hi: ["<p>वृत्तीय</p>", "<p>आयताकार</p>",
                                "<p>जाल</p>", "<p>बेलनाकार</p>"],
                    solution_en: "<p>30.(c) <strong>Grid</strong>. Mohenjo-daro was characterized by a well-organized, grid plan. Houses and buildings were arranged along wide, main streets running north-south intersected by narrower lanes running east-west.</p>",
                    solution_hi: "<p>30.(c) <strong>जाल </strong>(Grid)। मोहनजो-दारो की विशेषता एक सुव्यवस्थित, ग्रिड योजना थी। घरों और इमारतों को चौड़ी, मुख्य सड़कों पर व्यवस्थित किया गया था जो उत्तर-दक्षिण की ओर जाती थीं और पूर्व-पश्चिम की ओर जाने वाली संकरी गलियों से मिलती थीं।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "31",
                    section: "18",
                    question_en: "<p>31. Which ethnic group is the largest in China, accounting for over 90% of the population?</p>",
                    question_hi: "<p>31. चीन का कौन-सा जातीय समूह सबसे बड़ा है, जिसकी जनसंख्या 90% से अधिक है?</p>",
                    options_en: ["<p>Uighur</p>", "<p>Zhuang</p>", 
                                "<p>Manchu</p>", "<p>Han</p>"],
                    options_hi: ["<p>उइघुर</p>", "<p>जुआंग</p>",
                                "<p>मांचू</p>", "<p>हान</p>"],
                    solution_en: "<p>31.(d) <strong>Han</strong>. List of countries by ethnic groups: India - Indo-Aryan (72%), Dravidian (25%); Nepal - Chhetri (16.6%), Brahman-Hill (12.2%); Bhutan - Ngalop (also known as Bhote) 50%, Nepali (35%); Cambodia - Khmer (95.4%), Cham (2.4%); Curacao - Curacaoan (75.4%), Dutch (6%); Hong Kong - Chinese (92%), Filipino (2.5%); and Ireland - Irish (82.2%).</p>",
                    solution_hi: "<p>31.(d) <strong>हान</strong>। जातीय समूहों द्वारा देशों की सूची: भारत - इंडो-आर्यन (72%), द्रविड़ियन (25%); नेपाल - छेत्री (16.6%), ब्राह्मण-हिल (12.2%); भूटान - नगालोप (भोटे के नाम से भी जाना जाता है) 50%, नेपाली (35%); कंबोडिया - खमेर (95.4%), चाम (2.4%); क्युरासाओ - क्युरासाओन (75.4%), डच (6%); हांगकांग - चाइनीज़ (92%), फिलिपिनो (2.5%); और आयरलैंड - आयरिश (82.2%)।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "32",
                    section: "18",
                    question_en: "<p>32. How many gold medals has the Indian Hockey team won in the Olympics till 2023?</p>",
                    question_hi: "<p>32. भारतीय हॉकी टीम ने 2023 तक ओलंपिक में कितने स्वर्ण पदक जीते हैं?</p>",
                    options_en: ["<p>11</p>", "<p>7</p>", 
                                "<p>8</p>", "<p>10</p>"],
                    options_hi: ["<p>11</p>", "<p>7</p>",
                                "<p>8</p>", "<p>10</p>"],
                    solution_en: "<p>32.(c) <strong>8</strong>. India\'s 12 Olympic medals in hockey - 1928 Amsterdam Olympics (Gold), 1932 Los Angeles Olympics (Gold), 1936 Berlin Olympics (Gold), 1948 London Olympics (Gold), 1952 Helsinki Olympics (Gold), 1956 Melbourne Olympics (Gold), 1960 Rome Olympics (Silver), 1964 Tokyo Olympics (Gold), 1968 Mexico City Olympics (Bronze), 1972 Munich Olympics (Bronze), 1980 Moscow Olympics (Gold), and 2020 Tokyo Olympics (Bronze).</p>",
                    solution_hi: "<p>32.(c) <strong>8</strong>. हॉकी में भारत के 12 ओलंपिक पदक - 1928 एम्स्टर्डम ओलंपिक (स्वर्ण), 1932 लॉस एंजिल्स ओलंपिक (स्वर्ण), 1936 बर्लिन ओलंपिक (स्वर्ण), 1948 लंदन ओलंपिक (स्वर्ण), 1952 हेलसिंकी ओलंपिक (स्वर्ण), 1956 मेलबर्न ओलंपिक (स्वर्ण) , 1960 रोम ओलंपिक (रजत), 1964 टोक्यो ओलंपिक (स्वर्ण), 1968 मैक्सिको सिटी ओलंपिक (कांस्य), 1972 म्यूनिख ओलंपिक (कांस्य), 1980 मॉस्को ओलंपिक (स्वर्ण), और 2020 टोक्यो ओलंपिक (कांस्य)।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "33",
                    section: "18",
                    question_en: "<p>33. Under Liberalisation in 1991, several foreign exchange reforms were initiated. In this context, which of the following is true for devaluation of domestic currency?</p>",
                    question_hi: "<p>33. 1991 में उदारीकरण के तहत, कई विदेशी मुद्रा विनिमय सुधार शुरू किए गए थे। इस संदर्भ में, निम्नलिखित में से कौन-सा कथन घरेलू मुद्रा के अवमूल्यन के लिए सही है?</p>",
                    options_en: ["<p>Fixing the domestic currency to all foreign currency</p>", "<p>Lowering the value of foreign currency in relation to domestic currency</p>", 
                                "<p>Increasing value of domestic currency in relation to foreign currency</p>", "<p>Lowering the value of domestic currency in relation to foreign currency</p>"],
                    options_hi: ["<p>सभी विदेशी मुद्राओं के लिए घरेलू मुद्रा को स्थिर (फिक्स) करना</p>", "<p>घरेलू मुद्रा के सापेक्ष विदेशी मुद्रा के मूल्य को कम करना</p>",
                                "<p>विदेशी मुद्रा के सापेक्ष घरेलू मुद्रा का मूल्य बढ़ाना</p>", "<p>विदेशी मुद्रा के सापेक्ष घरेलू मुद्रा के मूल्य को कम करना</p>"],
                    solution_en: "<p>33.(d) 1991 Economic Reforms: It was initiated by PM Narasimha Rao, aimed at Liberalisation, Privatisation, and Globalisation (LPG); Increased private sector involvement, decreased government\'s role in domestic industry, boosted growth rate.</p>",
                    solution_hi: "<p>33.(d) 1991 आर्थिक सुधार: इसकी शुरुआत प्रधानमंत्री नरसिम्हा राव द्वारा की गई थी, जिसका उद्देश्य उदारीकरण, निजीकरण और वैश्वीकरण (एलपीजी) था; निजी क्षेत्र की भागीदारी बढ़ी, घरेलू उद्योग में सरकार की भूमिका घटी, विकास दर को बढ़ावा मिला।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "34",
                    section: "18",
                    question_en: "<p>34. The All India Trade Union Congress worked for the improvement of the workers&rsquo; working and living conditions. One of the main leaders of this was ________.</p>",
                    question_hi: "<p>34. अखिल भारतीय मजदूर संघ कांग्रेस (All India Trade Union Congress) ने श्रमिकों के काम करने और रहने की स्थिति में सुधार के लिए काम किया। इसके प्रमुख नेताओं में से एक ________ थे।</p>",
                    options_en: ["<p>Jyotirao Phule</p>", "<p>MN Roy</p>", 
                                "<p>GB Pant</p>", "<p>MG Ranade</p>"],
                    options_hi: ["<p>ज्योतिराव फुले</p>", "<p>एम.एन. रॉय</p>",
                                "<p>जी.बी. पंत</p>", "<p>एम.जी. रानाडे</p>"],
                    solution_en: "<p>34.(b) <strong>MN Roy. </strong>All India Trade Union Congress: It was the oldest Indian trade union federation, founded on 31 October 1920 in Bombay (now Mumbai). Its founders were - Lala Lajpat Rai, Joseph Baptista, N.M. Joshi and Diwan Chaman Lall.</p>",
                    solution_hi: "<p>34.(b) <strong>एम.एन. रॉय</strong>। ऑल इंडिया ट्रेड यूनियन कांग्रेस: यह सबसे पुराना भारतीय ट्रेड यूनियन महासंघ था, जिसकी स्थापना 31 अक्टूबर 1920 को बॉम्बे (अब मुंबई) में हुई थी। इसके संस्थापक थे - लाला लाजपत राय, जोसेफ बैप्टिस्टा, एन.एम. जोशी और दीवान चमन लाल।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "35",
                    section: "18",
                    question_en: "<p>35. From which of the following countries has &lsquo;advisory jurisdiction of the Supreme Court&rsquo; been adopted in the Constitution of India?</p>",
                    question_hi: "<p>35. निम्नलिखित में से किस देश से भारत के संविधान में \'सर्वोच्च न्यायालय के सलाहकार क्षेत्राधिकार\' को अपनाया गया है?</p>",
                    options_en: ["<p>Cuba</p>", "<p>Canada</p>", 
                                "<p>Germany</p>", "<p>Portugal </p>"],
                    options_hi: ["<p>क्यूबा</p>", "<p>कनाडा</p>",
                                "<p>जर्मनी</p>", "<p>पुर्तगाल</p>"],
                    solution_en: "<p>35.(b) <strong>Canada</strong>. Borrowed Features of Indian Constitution: Canada - Residuary powers vested with the centre and Centre appoints the Governors at the states. Ireland - Directive Principles of State Policy and Method of Election of the president. Germany - Fundamental Rights are suspended during Emergency.</p>",
                    solution_hi: "<p>35.(b) <strong>कनाडा</strong>। भारतीय संविधान की ली गई विशेषताएँ: कनाडा - अवशिष्ट शक्तियाँ केंद्र के पास निहित हैं और केंद्र राज्यों में राज्यपालों की नियुक्ति करता है। आयरलैंड - राज्य के नीति निदेशक सिद्धांत और राष्ट्रपति के चुनाव की विधि। जर्मनी - आपातकाल के दौरान मौलिक अधिकार निलंबित कर दिए जाते हैं।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "36",
                    section: "18",
                    question_en: "<p>36.Who among the following was the Secretary of State of India and a member of the Cabinet Mission sent to India by the British Government in 1946?</p>",
                    question_hi: "<p>36. निम्नलिखित में से कौन 1946 में ब्रिटिश सरकार द्वारा भारत में भेजे गए भारत के राज्य सचिव और कैबिनेट मिशन के सदस्य थे?</p>",
                    options_en: ["<p>John Simon</p>", "<p>Stafford Cripps</p>", 
                                "<p>Lord Irwin</p>", "<p>Lord Pethick-Lawrence</p>"],
                    options_hi: ["<p>जॉन साइमन (John Simon)</p>", "<p>स्टैफोर्ड क्रिप्स (Stafford Cripps)</p>",
                                "<p>लॉर्ड इरविन (Lord Irwin)</p>", "<p>लॉर्ड पेथिक-लॉरेंस (Lord Pethick-Lawrence)</p>"],
                    solution_en: "<p>36.(d) <strong>Lord Pethick-Lawrence</strong>. Cabinet Mission (1946): The mission had three British cabinet members - Pethick Lawrence (Secretary of State for India), Stafford Cripps (President of the Board of Trade) and A.V. Alexander (First Lord of Admiralty).</p>",
                    solution_hi: "<p>36.(d) <strong>लॉर्ड पेथिक-लॉरेंस</strong> (Lord Pethick-Lawrence)। कैबिनेट मिशन (1946): मिशन में तीन ब्रिटिश कैबिनेट सदस्य थे - पेथिक लॉरेंस (भारत के राज्य सचिव), स्टैफ़ोर्ड क्रिप्स (व्यापार बोर्ड के अध्यक्ष) और ए.वी. अलेक्जेंडर (नौवाहनविभाग के पहले लॉर्ड)।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "37",
                    section: "18",
                    question_en: "<p>37. Which Article of the Constitution of India debars the courts to inquire into the proceedings of the State Legislature?</p>",
                    question_hi: "<p>37. भारत के संविधान का कौन-सा अनुच्छेद अदालतों को राज्य विधानमंडल की कार्यवाही की जांच करने से रोकता है?</p>",
                    options_en: ["<p>Article 209</p>", "<p>Article 211</p>", 
                                "<p>Article 210</p>", "<p>Article 212</p>"],
                    options_hi: ["<p>अनुच्छेद 209</p>", "<p>अनुच्छेद 211</p>",
                                "<p>अनुच्छेद 210</p>", "<p>अनुच्छेद 212</p>"],
                    solution_en: "<p>37.(d) <strong>Article 212.</strong> Article 209 - Regulation by law of procedure in the Legislature of the State in relation to financial business. Article 210 - Language to be used in the Legislature. Article 211 - Restriction on discussion in the Legislature.</p>",
                    solution_hi: "<p>37.(d) <strong>अनुच्छेद 212.</strong> अनुच्छेद 209 - वित्तीय व्यवसाय के संबंध में राज्य के विधायिका में प्रक्रिया का कानून द्वारा विनियमन। अनुच्छेद 210 - विधायिका में प्रयोग की जाने वाली भाषा। अनुच्छेद 211 - विधायिका में चर्चा पर प्रतिबंध।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "38",
                    section: "18",
                    question_en: "<p>38. Which of the following statements are correct regarding the tribal Festival Medaram Jathara? <br>A. This is the largest tribal festival of India. <br>B. This festival is celebrated by the Koya community of Telangana. <br>C. This festival is celebrated once in two years in the month of \'Magha\' (February) on the full moon day.</p>",
                    question_hi: "<p>38. जनजातीय त्योहार मेदराम जात्रा के संबंध में निम्नलिखित में से कौन सा कथन सही है? <br>A. यह भारत का सबसे बड़ा जनजातीय त्योहार है।<br>B. यह त्योहार तेलंगाना के कोया समुदाय द्वारा मनाया जाता है।<br>C. यह त्योहार दो वर्ष में एक बार \'माघ\' (फरवरी) के महीने में पूर्णिमा के दिन मनाया जाता है।</p>",
                    options_en: ["<p>A, B and C</p>", "<p>B and C only</p>", 
                                "<p>A and C only</p>", "<p>A and B only</p>"],
                    options_hi: ["<p>A, B और C</p>", "<p>केवल B और C</p>",
                                "<p>केवल A और C</p>", "<p>केवल A और B</p>"],
                    solution_en: "<p>38.(a) <strong>A, B and C</strong>. Famous Festivals Of Telangana: Bonalu, Bathukamma, Ugadi, Peerla Panduga, Sammakka Sarakka Jatara. Tribal Festivals in India: Hornbill festival (Nagaland), Tusu Parab (Jharkhand), Bastar Dussehra (Chhattisgarh) and Puttari (Karnataka).</p>",
                    solution_hi: "<p>38.(a) <strong>A, B और C.</strong> तेलंगाना के प्रसिद्ध त्यौहार: बोनालु, बथुकम्मा, उगादि, पीरला पांडुगा, सम्मक्का सरक्का जात्रा। भारत में जनजातीय त्यौहार: हॉर्नबिल त्यौहार (नागालैंड), टुसु परब (झारखंड), बस्तर दशहरा (छत्तीसगढ़) और पुट्टारी (कर्नाटक)।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "39",
                    section: "18",
                    question_en: "<p>39. With reference to the travellers who visited India, who among the following was from Portugal?</p>",
                    question_hi: "<p>39. भारत आने वाले यात्रियों के संदर्भ में निम्नलिखित में से कौन पुर्तगाल से था?</p>",
                    options_en: ["<p>Fran&ccedil;ois Bernier</p>", "<p>Nicolo Conti</p>", 
                                "<p>Ibn Batuta</p>", "<p>Duarte Barbosa</p>"],
                    options_hi: ["<p>फ्रेंकोइस बर्नियर</p>", "<p>निकोलो कोंटी</p>",
                                "<p>इब्न बतूता</p>", "<p>दुआर्टे बारबोसा</p>"],
                    solution_en: "<p>39.(d) <strong>Duarte Barbosa.</strong> Francois Bernier: French physician and traveler who visited India in the 17th century. Nicolo Conti: Italian merchant and explorer who visited India in the 15th century. Ibn Battuta: Muslim scholar and explorer from Morocco who visited India in the 14th century.</p>",
                    solution_hi: "<p>39.(d) <strong>दुआर्टे बारबोसा।</strong> फ्रेंकोइस बर्नियर: फ्रांसीसी चिकित्सक और यात्री जिन्होंने 17वीं शताब्दी में भारत का दौरा किया था। निकोलो कोंटी: इतालवी व्यापारी और खोजकर्ता जिन्होंने 15वीं शताब्दी में भारत का दौरा किया था। इब्न बतूता: मोरक्को के मुस्लिम विद्वान और खोजकर्ता जिन्होंने 14वीं शताब्दी में भारत का दौरा किया था।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "40",
                    section: "18",
                    question_en: "<p>40. Of which state was Mirza Wajid Ali Shah the king in 1856, when it was annexed into the British Empire in India under the doctrine of lapse policy?</p>",
                    question_hi: "<p>40. 1856 में मिर्जा वाजिद अली शाह किस राज्य के राजा थे, जब इसे व्यपगत के सिद्धांत (डॉक्टराइन ऑफ लैप्स नीति) के तहत भारत में ब्रिटिश साम्राज्य में मिला लिया गया था?</p>",
                    options_en: ["<p>Nagpur</p>", "<p>Satara</p>", 
                                "<p>Jhansi</p>", "<p>Awadh</p>"],
                    options_hi: ["<p>नागपुर</p>", "<p>सतारा</p>",
                                "<p>झांसी</p>", "<p>अवध</p>"],
                    solution_en: "<p>40.(d) <strong>Awadh</strong>. Doctrine of Lapse: British policy in India for annexation, stating unruled territories automatically join the empire. It was implemented by Lord Dalhousie (Governor-General from 1848 to 1856). Annexation States: Satara (1848), Sambalpur (1850), Udaipur (1852), Nagpur (1853), Jhansi (1854).</p>",
                    solution_hi: "<p>40.(d) <strong>अवध</strong>। व्यपगत का सिद्धांत: भारत में विलय के लिए ब्रिटिश नीति, अनियंत्रित क्षेत्रों को स्वचालित रूप से साम्राज्य में शामिल करना बताती है। इसे लॉर्ड डलहौजी (गवर्नर-जनरल 1848 से 1856 तक) द्वारा लागू किया गया था। विलय राज्य: सतारा (1848), संबलपुर (1850), उदयपुर (1852), नागपुर (1853), झाँसी (1854)।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "41",
                    section: "18",
                    question_en: "<p>41. The Konkan Railway line passes through which of the following groups of states?</p>",
                    question_hi: "<p>41. कोंकण रेलवे लाइन, राज्यों के निम्नलिखित में से किस समूह से होकर गुजरती है?</p>",
                    options_en: ["<p>Madhya Pradesh, Goa and Karnataka</p>", "<p>Maharashtra, Goa and Telangana</p>", 
                                "<p>Maharashtra, Goa and Karnataka</p>", "<p>Maharashtra, Gujarat and Karnataka</p>"],
                    options_hi: ["<p>मध्य प्रदेश, गोवा और कर्नाटक</p>", "<p>महाराष्ट्र, गोवा और तेलंगाना</p>",
                                "<p>महाराष्ट्र, गोवा और कर्नाटक</p>", "<p>महाराष्ट्र, गुजरात और कर्नाटक</p>"],
                    solution_en: "<p>41.(c) <strong>Maharashtra, Goa and Karnataka.</strong> Konkan Railway: It runs parallel to the Arabian Sea and is operated by Konkan Railway Corporation. Its Headquartered at CBD Belapur (in Navi Mumbai, Maharashtra). The first passenger train on these tracks: 20 March 1993 (between Udupi and Mangalore).</p>",
                    solution_hi: "<p>41.(c) <strong>महाराष्ट्र, गोवा और कर्नाटक</strong>। कोंकण रेलवे: यह अरब सागर के समानांतर चलता है और कोंकण रेलवे कॉर्पोरेशन द्वारा संचालित होता है। इसका मुख्यालय सीबीडी बेलापुर (नवी मुंबई, महाराष्ट्र ) में है। इन पटरियों पर पहली यात्री ट्रेन: 20 मार्च 1993 (उडुपी और मैंगलोर के बीच)।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "42",
                    section: "18",
                    question_en: "<p>42. Which Five-Year Plan was formulated by governments of two political parties?</p>",
                    question_hi: "<p>42. निम्नलिखित में से कौन-सी पंचवर्षीय योजना दो राजनीतिक दलों की सरकारों द्वारा तैयार की गई थी?</p>",
                    options_en: ["<p>Eighth Five-Year Plan</p>", "<p>Sixth Five-Year Plan</p>", 
                                "<p>Fifth Five-Year Plan</p>", "<p>Ninth Five-Year Plan</p>"],
                    options_hi: ["<p>आठवीं पंचवर्षीय योजना</p>", "<p>छठी पंचवर्षीय योजना</p>",
                                "<p>पांचवीं पंचवर्षीय योजना</p>", "<p>नौवीं पंचवर्षीय योजना</p>"],
                    solution_en: "<p>42.(b) <strong>Sixth Five-Year Plan </strong>(1980-1985): Led by Indira Gandhi. Targeted growth - 5.2%, Achieved - 5.7%. Fifth Five Year Plan (1974 -1978) : This plan was terminated in 1978 by the newly elected Moraji Desai government. Targeted growth - 4.8 %, Achieved - 4.4 %. Eighth Five Year Plan (1992- 1997): leadership by V. Narasimha Rao. Targeted growth - 5.6 %, Achieved - 6.8 %.</p>",
                    solution_hi: "<p>42.(b) <strong>छठी पंचवर्षीय योजना</strong> (1980-1985): इंदिरा गांधी के नेतृत्व में । लक्षित वृद्धि - 5.2%, हासिल - 5.7%। पांचवी पंचवर्षीय योजना (1974 -1978) : इस योजना को 1978 में नवनिर्वाचित मोराजी देसाई सरकार ने समाप्त कर दिया। लक्षित वृद्धि - 4.8%, हासिल - 4.4%। आठवीं पंचवर्षीय योजना (1992-1997): वी. नरसिम्हा राव का नेतृत्व। लक्षित वृद्धि - 5.6%, हासिल - 6.8%।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "43",
                    section: "18",
                    question_en: "<p>43. From what period in history did humans become food producers?</p>",
                    question_hi: "<p>43. मनुष्य इतिहास में किस काल से खाद्य उत्पादक बना?</p>",
                    options_en: ["<p>Neolithic Age</p>", "<p>Mesolithic Age</p>", 
                                "<p>Palaeolithic Age</p>", "<p>Chalcolithic Age</p>"],
                    options_hi: ["<p>नवपाषाण युग</p>", "<p>मध्यपाषाण युग</p>",
                                "<p>पुरापाषाण युग</p>", "<p>ताम्रपाषाण युग</p>"],
                    solution_en: "<p>43.(a) <strong>Neolithic Age:</strong> Transition from hunter-gatherers to food producers, from about 10,000 years ago. Mesolithic: Environmental changes period, about 12,000 to 10,000 years ago brought about a change in human societal behavior. Palaeolithic: Extends from 2 million years ago to about 12,000 years ago. Chalcolithic Age: Transitional period between Neolithic and Bronze Age.</p>",
                    solution_hi: "<p>43.(a) <strong>नवपाषाण युग:</strong> लगभग 10,000 वर्ष पूर्व से शिकारी-संग्राहकों से खाद्य उत्पादकों में संक्रमण। मध्यपाषाण युग: लगभग 12,000 से 10,000 वर्ष पूर्व पर्यावरणीय परिवर्तन काल ने मानव सामाजिक व्यवहार में परिवर्तन लाया। पुरापाषाण युग: 2 मिलियन वर्ष पूर्व से लगभग 12,000 वर्ष पूर्व तक फैला हुआ है। ताम्रपाषाण युग: नवपाषाण और कांस्य युग के बीच का संक्रमणकालीन काल।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "44",
                    section: "18",
                    question_en: "<p>44. The completely Indian intact vegetation is called endemic or indigenous species, while the vegetation which comes from outside India is called ________.</p>",
                    question_hi: "<p>44. पूर्ण रूप से भारतीय अक्षत वनस्पति को स्थानिक या देशज प्रजाति कहा जाता है, जबकि जो वनस्पतियां भारत के बाहर से आती हैं, उन्हें ________कहा जाता है।</p>",
                    options_en: ["<p>local species</p>", "<p>Foreign species</p>", 
                                "<p>Non-local species</p>", "<p>endangered species</p>"],
                    options_hi: ["<p>स्थानीय प्रजाति</p>", "<p>विदेशज प्रजाति</p>",
                                "<p>गैर-स्थानीय प्रजाति</p>", "<p>लुप्तप्राय प्रजाति</p>"],
                    solution_en: "<p>44.(b) <strong>Foreign species.</strong> Endemic and Indigenous: Both terms refer to species naturally occurring in a specific geographic area and not introduced from elsewhere. While \"indigenous\" is broader and can include all native organisms, \"endemic\" is more specific to species found only in that particular region and nowhere else.</p>",
                    solution_hi: "<p>44.(b) <strong>विदेशज प्रजाति</strong>। स्थानिक और देशज: दोनों शब्द एक विशिष्ट भौगोलिक क्षेत्र में स्वाभाविक रूप से पाए जाने वाली प्रजातियों को संदर्भित करते हैं और कहीं और से नहीं लाए गए हैं। जबकि \"देशज\" व्यापक है और इसमें सभी मूल जीव शामिल हो सकते हैं, \"स्थानिक\" केवल उस विशेष क्षेत्र में पाई जाने वाली प्रजातियों के लिए अधिक विशिष्ट है और कहीं नहीं।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "45",
                    section: "18",
                    question_en: "<p>45. The Dancer Couple Dhananjayans were awarded the Rashtriya Kalidas Samman Award (2019-2020) for their contribution to which Indian dance form?</p>",
                    question_hi: "<p>45. डांसर युगल धनंजयन को किस भारतीय नृत्य शैली में उनके योगदान के लिए राष्ट्रीय कालिदास सम्मान पुरस्कार (2019-2020) से सम्मानित किया गया?</p>",
                    options_en: ["<p>Mohiniyattam</p>", "<p>Kathak</p>", 
                                "<p>Bharatanatyam</p>", "<p>Kuchipudi</p>"],
                    options_hi: ["<p>मोहिनीअट्टम</p>", "<p>कथक</p>",
                                "<p>भरतनाट्यम</p>", "<p>कुचिपुड़ी</p>"],
                    solution_en: "<p>45.(c) <strong>Bharatanatyam </strong>(TamilNadu). Other Dancers - Alarmel Valli, Yamini Krishnamurthy, Vyjayanthimala. Vannadil Pudiyaveettil Dhananjayan and Shanta Dhananjayan (also known as the Dhananjayans) are the dancing couple of India and were awarded Padma Bhushan (2009).</p>",
                    solution_hi: "<p>45.(c) <strong>भरतनाट्यम </strong>(तमिलनाडु)। अन्य नर्तक - अलारमेल वल्ली, यामिनी कृष्णमूर्ति, वैजयंतीमाला। वन्नादिल पुदियावीट्टिल धनंजयन और शांता धनंजयन (जिन्हें धनंजयन के नाम से भी जाना जाता है) भारत के नृत्य युगल हैं और उन्हें पद्म भूषण (2009) से सम्मानित किया गया था।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "46",
                    section: "18",
                    question_en: "<p>46. Which of the following is the correct statement about the directive principles of state policy?</p>",
                    question_hi: "<p>46. राज्य के नीति-निर्देशक सिद्धांतों के बारे में निम्नलिखित में से कौन-सा कथन सही है?</p>",
                    options_en: ["<p>Articles 12-35 of the constitution of India deals with directive principles.</p>", "<p>Articles 36-51 of the constitution of India deals with directive principles.</p>", 
                                "<p>Articles 62-71 of the constitution of India deals with directive principles.</p>", "<p>Articles 52-62 of the constitution of India deals with directive principles.</p>"],
                    options_hi: ["<p>भारत के संविधान के अनुच्छेद 12-35 नीति-निर्देशक सिद्धांतों से संबंधित हैं।</p>", "<p>भारत के संविधान के अनुच्छेद 36-51 नीति-निर्देशक सिद्धांतों से संबंधित हैं।</p>",
                                "<p>भारत के संविधान के अनुच्छेद 62-71 नीति-निर्देशक सिद्धांतों से संबंधित हैं।</p>", "<p>भारत के संविधान के अनुच्छेद 52-62 में नीति-निर्देशक सिद्धांतों से संबंधित हैं।</p>"],
                    solution_en: "<p>46.(b) Under Part IV (Articles 36-51) of the Directive Principles of State Policy. Article 39A - Free Legal aid. Article 40 - Organization of Panchayats. Article 44 - Uniform Civil Code. Article 47 - Nutrition, Standard of living and public health. Article 48A - Environment and Wildlife Protection. Article 50 - Judiciary should be separate from the Executive.</p>",
                    solution_hi: "<p>46.(b) राज्य के नीति निदेशक सिद्धांतों के भाग IV (अनुच्छेद 36-51) के तहत। अनुच्छेद 39A - निःशुल्क कानूनी सहायता। अनुच्छेद 40 - पंचायतों का संगठन। अनुच्छेद 44 - समान नागरिक संहिता। अनुच्छेद 47 - पोषण, जीवन स्तर और सार्वजनिक स्वास्थ्य। अनुच्छेद 48A - पर्यावरण एवं वन्यजीव संरक्षण। अनुच्छेद 50 - न्यायपालिका कार्यपालिका से अलग होनी चाहिए।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "47",
                    section: "18",
                    question_en: "<p>47. Surat, Navsari and Bhavnagar are known for their involvement in which industry?</p>",
                    question_hi: "<p>47. सूरत, नवसारी और भावनगर किस उद्योग में अपनी भागीदारी के लिए जाने जाते हैं?</p>",
                    options_en: ["<p>Software industry</p>", "<p>Iron and steel industry</p>", 
                                "<p>Automobile industry</p>", "<p>Diamond industry</p>"],
                    options_hi: ["<p>सॉफ्टवेयर उद्योग</p>", "<p>लौह एवं इस्पात उद्योग</p>",
                                "<p>ऑटोमोबाइल उद्योग</p>", "<p>हीरा उद्योग</p>"],
                    solution_en: "<p>47.(d) <strong>Diamond industry.</strong> Software Industry: These are in Bangalore, Hyderabad, and Pune. Iron and steel industry: This industry is more concentrated in eastern and central India with Jamshedpur and Rourkela being more prominent examples.</p>",
                    solution_hi: "<p>47.(d) <strong>हीरा उद्योग</strong>। सॉफ्टवेयर उद्योग: ये बैंगलोर, हैदराबाद और पुणे में हैं। लौह और इस्पात उद्योग: यह उद्योग पूर्वी और मध्य भारत में अधिक केंद्रित है, जिसमें जमशेदपुर और राउरकेला इसके प्रमुख उदाहरण हैं।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "48",
                    section: "18",
                    question_en: "<p>48. Which scheme was started in 2000 for additional central assistance to be given to states for basic services such as primary health, primary education, rural shelter, etc.?</p>",
                    question_hi: "<p>48. प्राथमिक स्वास्थ्य, प्राथमिक शिक्षा, ग्रामीण आश्रय आदि जैसी मूलभूत सेवाओं के लिए राज्यों को दी जाने वाली अतिरिक्त केंद्रीय सहायता के लिए 2000 में कौन-सी योजना शुरू की गई थी?</p>",
                    options_en: ["<p>Swarnajayanti Gram Swarojgar Yojana</p>", "<p>Prime Minister Rojgar Yojana</p>", 
                                "<p>Rural Employment Generation Programme</p>", "<p>Pradhan Mantri Gramodaya Yojana</p>"],
                    options_hi: ["<p>स्वर्णजयंती ग्राम स्वरोजगार योजना</p>", "<p>प्रधानमंत्री रोजगार योजना</p>",
                                "<p>ग्रामीण रोजगार सृजन कार्यक्रम</p>", "<p>प्रधानमंत्री ग्रामोदय योजना</p>"],
                    solution_en: "<p>48.(d) <strong>Pradhan Mantri Gramodaya Yojana.</strong> It was launched in 2000-2001. Objective - Sustainable human development at the village level. Swarnajayanti Gram Swarojgar Yojana (SGSY) - Launched in April 1999, sole self-employment program. Prime Minister Rojgar Yojana - Launched in October 1993 to provide self-employment to India\'s educated youth and women. The Khadi and Village Industries Commission (KVIC) launched the Rural Employment Generation Programme (REGP) in April, 1995.</p>",
                    solution_hi: "<p>48.(d) <strong>प्रधानमंत्री ग्रामोदय योजना। </strong>इसे 2000-2001 में लॉन्च किया गया था। उद्देश्य - ग्रामीण स्तर पर सतत मानव विकास। स्वर्णजयंती ग्राम स्वरोजगार योजना (SGSY) - अप्रैल 1999 में शुरू की गई, एकमात्र स्वरोजगार कार्यक्रम। प्रधानमंत्री रोज़गार योजना - भारत के शिक्षित युवाओं और महिलाओं को स्व-रोज़गार प्रदान करने के लिए अक्टूबर 1993 में शुरू की गई। खादी और ग्रामोद्योग आयोग (KVIC) ने अप्रैल, 1995 में ग्रामीण रोजगार सृजन कार्यक्रम (REGP) शुरू किया।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "49",
                    section: "18",
                    question_en: "<p>49. In which year did the Indian Badminton team create history by winning the Thomas Cup tournament?</p>",
                    question_hi: "<p>49. भारतीय बैडमिंटन टीम ने किस वर्ष थॉमस कप टूर्नामेंट जीतकर इतिहास रचा था?</p>",
                    options_en: ["<p>2020</p>", "<p>2019</p>", 
                                "<p>2022</p>", "<p>2021</p>"],
                    options_hi: ["<p>2020</p>", "<p>2019</p>",
                                "<p>2022</p>", "<p>2021</p>"],
                    solution_en: "<p>49.(c) <strong>2022</strong>. India won its maiden Thomas Cup 2022, beating 14 time champion Indonesia 3-0 in the final in Bangkok. The Thomas Cup (first held in 1948-1949) is the World Men&rsquo;s Team Championship.</p>",
                    solution_hi: "<p>49.(c) <strong>2022</strong>. भारत ने बैंकॉक में फाइनल में 14 बार के चैंपियन इंडोनेशिया को 3-0 से हराकर अपना पहला थॉमस कप 2022 जीता। थॉमस कप (पहली बार 1948-1949 में आयोजित) विश्व पुरुष टीम चैम्पियनशिप है।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "50",
                    section: "misc",
                    question_en: "<p>50. The movie RRR has recently been honoured with the prestigious Golden Globe award for the Best Original Song for the track &lsquo;Natu Natu&rsquo;. Who are the singers of this song?</p>",
                    question_hi: "<p>50. फिल्म आरआरआर (RRR) को हाल ही में ट्रैक \'नाटूनाटू\' के लिए सर्वश्रेष्ठ मूल गीत के लिए प्रतिष्ठित गोल्डन ग्लोब पुरस्कार से सम्मानित किया गया है। इस गाने के गायक कौन हैं?</p>",
                    options_en: ["<p>Rahul Sipligunj and Kaala Bhairava</p>", "<p>Silambarasan TR and Vineeth Sreenivasan</p>", 
                                "<p>KJ Yesudas and Anirudh Ravichander</p>", "<p>Shankar Mahadevan and Rajesh Krishnan</p>"],
                    options_hi: ["<p>राहुल सिपलीगंज और कालभैरव</p>", "<p>सिलम्बरासन टी.आर., विनीत श्रीनिवासन</p>",
                                "<p>के.जे. येसुदास और अनिरुद्ध रविचंदर</p>", "<p>शंकर महादेवन और राजेश कृष्णन</p>"],
                    solution_en: "<p>50.(a) <strong>Rahul Sipligunj and Kaala Bhairava.</strong> &lsquo;Naatu Naatu&rsquo; becomes the first Asian song to win the coveted award. Golden Globe Awards: Annual accolades for excellence in American and international film and television since 1944.</p>",
                    solution_hi: "<p>50.(a) <strong>राहुल सिपलीगंज और कालभैरव। </strong>\'नाटू नाटू\' प्रतिष्ठित पुरस्कार जीतने वाला पहला एशियाई गीत बन गया। गोल्डन ग्लोब पुरस्कार: 1944 से अमेरिकी और अंतर्राष्ट्रीय फिल्म और टेलीविजन में उत्कृष्टता के लिए वार्षिक पुरस्कार।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>