<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">10:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 14</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">14</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 10 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 0,
                end: 12
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 13,
                end: 13
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "17",
                    question_en: "<p>1. <span style=\"font-family: Cambria Math;\">If&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>x</mi><mo>+</mo><mfrac><mn>1</mn><mi>x</mi></mfrac><mo>=</mo><mo>&#160;</mo><mfrac><mn>17</mn><mn>4</mn></mfrac></math> </span><span style=\"font-family: Cambria Math;\">, x &gt; 1, then what is the value of&nbsp; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>x</mi><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mfrac><mn>1</mn><mi>x</mi></mfrac></math></span><span style=\"font-family: Cambria Math;\">&nbsp;</span><span style=\"font-family: Cambria Math;\">?</span></p>",
                    question_hi: "<p>1. <span style=\"font-family: Cambria Math;\">यदि</span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>x</mi><mo>+</mo><mfrac><mn>1</mn><mi>x</mi></mfrac><mo>=</mo><mfrac><mn>17</mn><mn>4</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\">&nbsp;</span><span style=\"font-family: Cambria Math;\">, x &gt; 1, </span><span style=\"font-family: Cambria Math;\">तो</span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>x</mi><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mfrac><mn>1</mn><mi>x</mi></mfrac></math>&nbsp;</span><span style=\"font-family: Cambria Math;\">का</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">मान</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">क्या</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">है </span><span style=\"font-family: Cambria Math;\">?</span></p>",
                    options_en: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>9</mn><mn>4</mn></mfrac></math><span style=\"font-family: Cambria Math;\"> </span></p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>2</mn></mfrac></math><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> </span></p>", 
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>8</mn><mn>3</mn></mfrac></math><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> </span></p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>15</mn><mn>4</mn></mfrac></math></p>"],
                    options_hi: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>9</mn><mn>4</mn></mfrac></math><span style=\"font-family: Cambria Math;\"> </span></p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>2</mn></mfrac></math></p>",
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>8</mn><mn>3</mn></mfrac></math></p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>15</mn><mn>4</mn></mfrac></math></p>"],
                    solution_en: "<p>1.(d)&nbsp;<br><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>x</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mfrac><mn>1</mn><mi>x</mi></mfrac><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mfrac><mn>17</mn><mn>4</mn></mfrac></math></span><br><span style=\"font-family: Cambria Math;\">Squaring both side</span><br><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>x</mi><mn>2</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mfrac><mn>1</mn><mrow><msup><mi>x</mi><mn>2</mn></msup><mo>&#160;</mo></mrow></mfrac><mo>=</mo><mo>&#160;</mo><mfrac><mn>289</mn><mn>16</mn></mfrac><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mn>2</mn></math></span><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>x</mi><mn>2</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mfrac><mn>1</mn><mrow><msup><mi>x</mi><mn>2</mn></msup><mo>&#160;</mo></mrow></mfrac><mo>-</mo><mo>&#160;</mo><mn>2</mn><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mfrac><mn>225</mn><mn>16</mn></mfrac><mo>&#160;</mo><mo>&#8658;</mo><mi>x</mi><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mfrac><mn>1</mn><mi>x</mi></mfrac><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mfrac><mn>15</mn><mn>4</mn></mfrac></math><br><strong><span style=\"font-family: Cambria Math;\">Short-Trick:</span></strong><br><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>x</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mfrac><mn>1</mn><mi>x</mi></mfrac><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mfrac><mn>17</mn><mn>4</mn></mfrac></math></span><br><span style=\"font-family: Cambria Math;\">x = 4, satisfy</span><br><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>x</mi><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mfrac><mn>1</mn><mi>x</mi></mfrac><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>4</mn><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mfrac><mn>1</mn><mn>4</mn></mfrac><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mfrac><mn>15</mn><mn>4</mn></mfrac></math></span></p>",
                    solution_hi: "<p>1.(d)&nbsp;<br><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>x</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mfrac><mn>1</mn><mi>x</mi></mfrac><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mfrac><mn>17</mn><mn>4</mn></mfrac></math></span><br><span style=\"font-weight: 400;\">दोनों पक्षों का वर्ग करने पर,</span><br><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>x</mi><mn>2</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mfrac><mn>1</mn><mrow><msup><mi>x</mi><mn>2</mn></msup><mo>&#160;</mo></mrow></mfrac><mo>=</mo><mo>&#160;</mo><mfrac><mn>289</mn><mn>16</mn></mfrac><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mn>2</mn></math></span><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>x</mi><mn>2</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mfrac><mn>1</mn><mrow><msup><mi>x</mi><mn>2</mn></msup><mo>&#160;</mo></mrow></mfrac><mo>-</mo><mo>&#160;</mo><mn>2</mn><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mfrac><mn>225</mn><mn>16</mn></mfrac><mo>&#160;</mo><mo>&#8658;</mo><mi>x</mi><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mfrac><mn>1</mn><mi>x</mi></mfrac><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mfrac><mn>15</mn><mn>4</mn></mfrac></math>&nbsp;<br><strong>वैकल्पिक विधि,</strong><br><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>x</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mfrac><mn>1</mn><mi>x</mi></mfrac><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mfrac><mn>17</mn><mn>4</mn></mfrac></math></span><br><span style=\"font-family: Cambria Math;\">x = 4,&nbsp;<span style=\"font-weight: 400;\">सभी शर्तों को पूरा करता है,</span></span><br><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>x</mi><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mfrac><mn>1</mn><mi>x</mi></mfrac><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>4</mn><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mfrac><mn>1</mn><mn>4</mn></mfrac><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mfrac><mn>15</mn><mn>4</mn></mfrac></math></span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "17",
                    question_en: "<p>2. <span style=\"font-family: Cambria Math;\">If&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>x</mi><mn>4</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mi>x</mi><mn>2</mn></msup><msup><mi>y</mi><mn>2</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mi>y</mi><mn>4</mn></msup><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>21</mn></math> </span><span style=\"font-family: Cambria Math;\">and&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>x</mi><mn>2</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mi>x</mi><mi>y</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mi>y</mi><mn>2</mn></msup><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>3</mn></math> </span><span style=\"font-family: Cambria Math;\">,then what is the value of (- xy)?</span></p>",
                    question_hi: "<p>2. <span style=\"font-family: Cambria Math;\">यदि&nbsp;</span><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>x</mi><mn>4</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mi>x</mi><mn>2</mn></msup><msup><mi>y</mi><mn>2</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mi>y</mi><mn>4</mn></msup><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>21</mn></math></span><span style=\"font-family: Cambria Math;\">&nbsp;</span><span style=\"font-family: Cambria Math;\">और&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>x</mi><mn>2</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mi>x</mi><mi>y</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mi>y</mi><mn>2</mn></msup><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>3</mn></math></span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">तो</span><span style=\"font-family: Cambria Math;\"> (- xy) </span><span style=\"font-family: Cambria Math;\">का</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">मान</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">क्या</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">है</span><span style=\"font-family: Cambria Math;\">?</span></p>",
                    options_en: ["<p>2<span style=\"font-family: Cambria Math;\"> </span></p>", "<p>1<span style=\"font-family: Cambria Math;\"> </span></p>", 
                                "<p>-1</p>", "<p>-2</p>"],
                    options_hi: ["<p>2</p>", "<p>1</p>",
                                "<p>-1<span style=\"font-family: Cambria Math;\"> </span></p>", "<p>-2</p>"],
                    solution_en: "<p>2.(a)&nbsp;x<sup>2</sup> + xy + y<sup>2</sup> = 3<br>x<sup>2</sup> &nbsp;+ y<sup>2</sup> = 3 - xy<br><span style=\"font-family: Cambria Math;\">Squaring both side</span><br>x<sup>4</sup>&nbsp; + y<sup>4</sup> + 2x<sup>2</sup>y<sup>2</sup>= 9 + x<sup>2</sup>y<sup>2</sup> - 6xy<br>x<sup>4</sup> + x<sup>2</sup>y<sup>2</sup> + y<sup>4</sup> = 9 - 6xy<br>xy = -2 &rArr; (-xy) = 2</p>",
                    solution_hi: "<p>2.(a)&nbsp;x<sup>2</sup> + xy + y<sup>2</sup> = 3<br>x<sup>2</sup> &nbsp;+ y<sup>2</sup> = 3 - xy<br><span style=\"font-weight: 400;\">दोनों पक्षों का वर्ग करने पर</span><br>x<sup>4</sup>&nbsp; + y<sup>4</sup> + 2x<sup>2</sup>y<sup>2</sup>= 9 + x<sup>2</sup>y<sup>2</sup> - 6xy<br>x<sup>4</sup> + x<sup>2</sup>y<sup>2</sup> + y<sup>4</sup> = 9 - 6xy<br>xy = -2 &rArr; (-xy) = 2</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "17",
                    question_en: "<p>3. <span style=\"font-family: Cambria Math;\">If x + y + z = 2,&nbsp; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi mathvariant=\"normal\">x</mi><mn>3</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">y</mi><mn>3</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">z</mi><mn>3</mn></msup><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mn>3</mn><mi>xyz</mi><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>74</mn></math></span><span style=\"font-family: Cambria Math;\"> then <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">y</mi><mn>2</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">z</mi><mn>2</mn></msup><mo>)</mo></math></span><span style=\"font-family: Cambria Math;\"> is equal to </span></p>",
                    question_hi: "<p>3. <span style=\"font-family: Cambria Math;\">यदि</span><span style=\"font-family: Cambria Math;\"> x + y + z = 2,&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi mathvariant=\"normal\">x</mi><mn>3</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">y</mi><mn>3</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">z</mi><mn>3</mn></msup><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mn>3</mn><mi>xyz</mi><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>74</mn></math> </span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">तो</span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">y</mi><mn>2</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">z</mi><mn>2</mn></msup><mo>)</mo></math></span><span style=\"font-family: Cambria Math;\">&nbsp;</span><span style=\"font-family: Cambria Math;\">बराबर</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">है</span><span style=\"font-family: Cambria Math;\">:</span></p>",
                    options_en: ["<p>22<span style=\"font-family: Cambria Math;\"> </span></p>", "<p>29<span style=\"font-family: Cambria Math;\"> </span></p>", 
                                "<p>26</p>", "<p>24</p>"],
                    options_hi: ["<p>22<span style=\"font-family: Cambria Math;\"> </span></p>", "<p>29<span style=\"font-family: Cambria Math;\"> </span></p>",
                                "<p>26<span style=\"font-family: Cambria Math;\"> </span></p>", "<p>24</p>"],
                    solution_en: "<p>3.(c)<br>x<sup>3</sup> + y<sup>3</sup> + z<sup>3</sup> - 3xyz = (x + y + z ){(x + y + z )<sup>2</sup> - 3(xy + yz + zx)}<br>74 = 2 {4 - 3(xy + yz + zx)}<br><span style=\"font-family: Cambria Math;\">xy + yz + zx =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>37</mn><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mn>4</mn></mrow><mrow><mo>-</mo><mn>3</mn></mrow></mfrac></math> </span><span style=\"font-family: Cambria Math;\">= -11</span><br>(x + y + z)<sup>2</sup> = x<sup>2</sup> + y<sup>2</sup> + z<sup>2</sup> + 2(xy + yz+ zx)<br>x<sup>2</sup> + y<sup>2</sup> + z<sup>2</sup> = 4 + 22 = 26</p>",
                    solution_hi: "<p>3.(c)<br>x<sup>3</sup> + y<sup>3</sup> + z<sup>3</sup> - 3xyz = (x + y + z ){(x + y + z )<sup>2</sup> - 3(xy + yz + zx)}<br>74 = 2{4 - 3(xy + yz + zx)}<br><span style=\"font-family: Cambria Math;\">xy + yz + zx =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>37</mn><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mn>4</mn></mrow><mrow><mo>-</mo><mn>3</mn></mrow></mfrac></math> </span><span style=\"font-family: Cambria Math;\">= -11</span><br>(x + y + z)<sup>2</sup> = x<sup>2</sup> + y<sup>2</sup> + z<sup>2</sup> + 2(xy + yz+ zx)<br>x<sup>2</sup> + y<sup>2</sup> + z<sup>2</sup> = 4 + 22 = 26</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "17",
                    question_en: "<p>4. <span style=\"font-family: Cambria Math;\">If&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>9</mn><mo>(</mo><msup><mi mathvariant=\"normal\">a</mi><mn>2</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">b</mi><mn>2</mn></msup><mo>)</mo><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">c</mi><mn>2</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>20</mn><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>12</mn><mo>(</mo><mi mathvariant=\"normal\">a</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>2</mn><mi mathvariant=\"normal\">b</mi><mo>)</mo></math></span><span style=\"font-family: Cambria Math;\">, then the value of&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>6</mn><mi mathvariant=\"normal\">a</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>9</mn><mi mathvariant=\"normal\">b</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>2</mn><mi mathvariant=\"normal\">c</mi></msqrt></math> </span><span style=\"font-family: Cambria Math;\">is:</span></p>",
                    question_hi: "<p>4. <span style=\"font-family: Cambria Math;\">यदि</span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>9</mn><mo>(</mo><msup><mi mathvariant=\"normal\">a</mi><mn>2</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">b</mi><mn>2</mn></msup><mo>)</mo><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">c</mi><mn>2</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>20</mn><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>12</mn><mo>(</mo><mi mathvariant=\"normal\">a</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>2</mn><mi mathvariant=\"normal\">b</mi><mo>)</mo></math></span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">तो</span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>6</mn><mi mathvariant=\"normal\">a</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>9</mn><mi mathvariant=\"normal\">b</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>2</mn><mi mathvariant=\"normal\">c</mi></msqrt></math>&nbsp;</span><span style=\"font-family: Cambria Math;\">का</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">मान</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">है</span><span style=\"font-family: Cambria Math;\">:</span></p>",
                    options_en: ["<p>3<span style=\"font-family: Cambria Math;\"> </span></p>", "<p>4<span style=\"font-family: Cambria Math;\"> </span></p>", 
                                "<p>2</p>", "<p>6</p>"],
                    options_hi: ["<p>3<span style=\"font-family: Cambria Math;\"> </span></p>", "<p>4<span style=\"font-family: Cambria Math;\"> </span></p>",
                                "<p>2<span style=\"font-family: Cambria Math;\"> </span></p>", "<p>6</p>"],
                    solution_en: "<p>4.(b)<br>9(a<sup>2</sup> + b<sup>2</sup>) + c<sup>2</sup> + 20&nbsp; = 12(a + 2b)<br>(3a - 2)<sup>2</sup> + (3b - 4) + c<sup>2</sup> = 0<br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">a</mi><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mfrac><mn>2</mn><mn>3</mn></mfrac><mo>,</mo><mo>&#160;</mo><mi mathvariant=\"normal\">b</mi><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mfrac><mn>4</mn><mn>3</mn></mfrac><mo>,</mo><mo>&#160;</mo><mi mathvariant=\"normal\">c</mi><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>0</mn></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>6</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mfrac><mn>2</mn><mn>3</mn></mfrac><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>9</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mfrac><mn>4</mn><mn>3</mn></mfrac><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>2</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>0</mn></msqrt></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>=</mo><mo>&#160;</mo><msqrt><mn>4</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>12</mn></msqrt><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>4</mn></math></p>",
                    solution_hi: "<p>4.(b)<br>9(a<sup>2</sup> + b<sup>2</sup>) + c<sup>2</sup> + 20&nbsp; = 12(a + 2b)<br>(3a - 2)<sup>2</sup> + (3b - 4) + c<sup>2</sup> = 0<br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">a</mi><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mfrac><mn>2</mn><mn>3</mn></mfrac><mo>,</mo><mo>&#160;</mo><mi mathvariant=\"normal\">b</mi><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mfrac><mn>4</mn><mn>3</mn></mfrac><mo>,</mo><mo>&#160;</mo><mi mathvariant=\"normal\">c</mi><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>0</mn></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>6</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mfrac><mn>2</mn><mn>3</mn></mfrac><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>9</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mfrac><mn>4</mn><mn>3</mn></mfrac><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>2</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>0</mn></msqrt></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>=</mo><mo>&#160;</mo><msqrt><mn>4</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>12</mn></msqrt><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>4</mn></math></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "17",
                    question_en: "<p>5. <span style=\"font-family: Cambria Math;\">If&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mn>2</mn><mi mathvariant=\"normal\">x</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mi mathvariant=\"normal\">y</mi><mo>)</mo></mrow><mn>3</mn></msup><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><msup><mrow><mo>(</mo><mi mathvariant=\"normal\">x</mi><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mn>2</mn><mi mathvariant=\"normal\">y</mi><mo>)</mo></mrow><mn>3</mn></msup><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mo>(</mo><mi mathvariant=\"normal\">x</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>3</mn><mi mathvariant=\"normal\">y</mi><mo>)</mo><mo>(</mo><msup><mi>Ax</mi><mn>2</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mi>By</mi><mn>2</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mi>Cxy</mi><mo>)</mo></math> , </span>then what is the value of (A + 2B + C) ?</p>",
                    question_hi: "<p>5. <span style=\"font-family: Cambria Math;\">यदि</span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mn>2</mn><mi mathvariant=\"normal\">x</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mi mathvariant=\"normal\">y</mi><mo>)</mo></mrow><mn>3</mn></msup><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><msup><mrow><mo>(</mo><mi mathvariant=\"normal\">x</mi><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mn>2</mn><mi mathvariant=\"normal\">y</mi><mo>)</mo></mrow><mn>3</mn></msup><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mo>(</mo><mi mathvariant=\"normal\">x</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>3</mn><mi mathvariant=\"normal\">y</mi><mo>)</mo><mo>(</mo><msup><mi>Ax</mi><mn>2</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mi>By</mi><mn>2</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mi>Cxy</mi><mo>)</mo></math>&nbsp;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">तो</span><span style=\"font-family: Cambria Math;\"> (A + 2B + C) </span><span style=\"font-family: Cambria Math;\">का</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">मान</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">क्या</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">है </span><span style=\"font-family: Cambria Math;\">?</span></p>",
                    options_en: ["<p>13<span style=\"font-family: Cambria Math;\"> </span></p>", "<p>7</p>", 
                                "<p>14<span style=\"font-family: Cambria Math;\"> </span></p>", "<p>10</p>"],
                    options_hi: ["<p>13<span style=\"font-family: Cambria Math;\"> </span></p>", "<p>7<span style=\"font-family: Cambria Math;\"> </span></p>",
                                "<p>14</p>", "<p>10</p>"],
                    solution_en: "<p>5.(d)&nbsp;a<sup>3</sup> - b<sup>3</sup> = (a - b) (a<sup>2 </sup>+ b<sup>2</sup> + ab)<br>(2x + y)<sup>3</sup> - (x - 2y)<sup>3</sup> = (2x + y - x + 2y) {(2x + y)<sup>2</sup> + (x - 2y)<sup>2</sup> + (2x + y)(x - 2y)}<br>= (x + 3y) {4x<sup>2</sup> + y<sup>2</sup> + 4xy + x<sup>2</sup> + 4y<sup>2</sup> - 4xy + 2x<sup>2</sup>- 4xy + yx - 2y<sup>2</sup>}<br>= (x + 3y)(7x<sup>2</sup> + 3y<sup>2 </sup>- 3xy) --------- (i)<br>(2x + y)<sup>3</sup> - (x - 2y)<sup>3</sup> = (x + 3y)[Ax<sup>2</sup> + By<sup>2</sup> + Cxy]------(ii)<br>On comparing equation (i) with (ii)<br>A = 7, B = 3, C = -3<br>(A + 2B + C) = 7 + 6 - 3 = 10</p>",
                    solution_hi: "<p>5.(d)&nbsp;a<sup>3</sup> - b<sup>3</sup> = (a - b) (a<sup>2 </sup>+ b<sup>2</sup> + ab)<br>(2x + y)<sup>3</sup> - (x - 2y)<sup>3</sup> = (2x + y - x + 2y) {(2x + y)<sup>2</sup> + (x - 2y)<sup>2</sup> + (2x + y)(x - 2y)}<br>= (x + 3y) {4x<sup>2</sup> + y<sup>2</sup> + 4xy + x<sup>2</sup> + 4y<sup>2</sup> - 4xy + 2x<sup>2</sup>- 4xy + yx - 2y<sup>2</sup>}<br>= (x + 3y)(7x<sup>2</sup> + 3y<sup>2 </sup>- 3xy) --------- (i)<br>(2x + y)<sup>3</sup> - (x - 2y)<sup>3</sup> = (x + 3y)[Ax<sup>2</sup> + By<sup>2</sup> + Cxy]------(ii)<br>समीकरण (i) की तुलना (ii) से करने पर,<br>A = 7, B = 3, C = -3<br>(A + 2B + C) = 7 + 6 - 3 = 10</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "17",
                    question_en: "<p>6.<span style=\"font-family: Cambria Math;\"> If <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi mathvariant=\"normal\">a</mi><mn>4</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">b</mi><mn>4</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">a</mi><mn>2</mn></msup><msup><mi mathvariant=\"normal\">b</mi><mn>2</mn></msup><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>273</mn></math></span><span style=\"font-family: Cambria Math;\"> and&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi mathvariant=\"normal\">a</mi><mn>2</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">b</mi><mn>2</mn></msup><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mi>ab</mi><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>21</mn></math> </span><span style=\"font-family: Cambria Math;\">, then one of the values of&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><mfrac><mn>1</mn><mi mathvariant=\"normal\">a</mi></mfrac><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mfrac><mn>1</mn><mi mathvariant=\"normal\">b</mi></mfrac><mo>)</mo></math> </span><span style=\"font-family: \'Cambria Math\';\">is :</span></p>",
                    question_hi: "<p>6. <span style=\"font-family: Cambria Math;\">यदि</span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi mathvariant=\"normal\">a</mi><mn>4</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">b</mi><mn>4</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">a</mi><mn>2</mn></msup><msup><mi mathvariant=\"normal\">b</mi><mn>2</mn></msup><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>273</mn></math>&nbsp;</span><span style=\"font-family: Cambria Math;\">और</span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi mathvariant=\"normal\">a</mi><mn>2</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">b</mi><mn>2</mn></msup><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mi>ab</mi><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>21</mn></math>&nbsp;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">तो</span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><mfrac><mn>1</mn><mi>a</mi></mfrac><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mfrac><mn>1</mn><mi>b</mi></mfrac><mo>)</mo></math></span><span style=\"font-family: Cambria Math;\">&nbsp;</span><span style=\"font-family: Cambria Math;\">का</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">एक</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">मान</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">है</span><span style=\"font-family: Cambria Math;\">:</span></p>",
                    options_en: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>-</mo><mn>9</mn></mrow><mn>4</mn></mfrac></math></p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>-</mo><mn>3</mn></mrow><mn>4</mn></mfrac></math></p>", 
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>9</mn><mn>8</mn></mfrac></math><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> </span></p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>2</mn></mfrac></math><span style=\"font-family: Cambria Math;\"> </span></p>"],
                    options_hi: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>-</mo><mn>9</mn></mrow><mn>4</mn></mfrac></math></p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>-</mo><mn>3</mn></mrow><mn>4</mn></mfrac></math></p>",
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>9</mn><mn>8</mn></mfrac></math><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> </span></p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>2</mn></mfrac></math></p>"],
                    solution_en: "<p>6.(b)&nbsp;a<sup>4</sup> + b<sup>4</sup> + a<sup>2</sup>b<sup>2</sup> = 273<br>(a<sup>2</sup> + b<sup>2 </sup>- ab)(a<sup>2</sup> + b<sup>2 </sup>+ ab) = 273<br>21(a<sup>2</sup> + b<sup>2 </sup>+ ab) = 273<br>(a<sup>2</sup> + b<sup>2 </sup>+ ab) = 13...eqn 1<br>(a<sup>2</sup> + b<sup>2 </sup>- ab) = 21...eqn 2<br>Solving 1 &amp; 2<br>a&sup2; + b&sup2; = 17 &rArr; ab = - 4<br>(a + b)&sup2; = 17 - 8 &rArr; (a + b)&sup2; = 9 &rArr; a + b = 3<br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi mathvariant=\"normal\">a</mi></mfrac><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mfrac><mn>1</mn><mi mathvariant=\"normal\">b</mi></mfrac><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mfrac><mrow><mi mathvariant=\"normal\">a</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mi mathvariant=\"normal\">b</mi></mrow><mi>ab</mi></mfrac><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mfrac><mn>3</mn><mrow><mo>-</mo><mn>4</mn></mrow></mfrac><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mo>-</mo><mfrac><mn>3</mn><mn>4</mn></mfrac></math></p>",
                    solution_hi: "<p>6.(b)&nbsp;a<sup>4</sup> + b<sup>4</sup> + a<sup>2</sup>b<sup>2</sup> = 273<br>(a<sup>2</sup> + b<sup>2 </sup>- ab)(a<sup>2</sup> + b<sup>2 </sup>+ ab) = 273<br>21(a<sup>2</sup> + b<sup>2 </sup>+ ab) = 273<br>(a<sup>2</sup> + b<sup>2 </sup>+ ab) = 13&hellip;&hellip;(i)<br>(a<sup>2</sup> + b<sup>2 </sup>- ab) = 21&hellip;&hellip;(ii)<br>समीकरण (i) और (ii) को हल करने पर,<br>a&sup2; + b&sup2; = 17 &rArr; ab = - 4<br>(a + b)&sup2; = 17 - 8 &rArr; (a + b)&sup2; = 9 &rArr; a + b = 3<br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi mathvariant=\"normal\">a</mi></mfrac><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mfrac><mn>1</mn><mi mathvariant=\"normal\">b</mi></mfrac><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mfrac><mrow><mi mathvariant=\"normal\">a</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mi mathvariant=\"normal\">b</mi></mrow><mi>ab</mi></mfrac><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mfrac><mn>3</mn><mrow><mo>-</mo><mn>4</mn></mrow></mfrac><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mo>-</mo><mfrac><mn>3</mn><mn>4</mn></mfrac></math></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "17",
                    question_en: "<p>7. <span style=\"font-family: Cambria Math;\">If 2x + 3y + 1 = 0, then what is the value of <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><mn>8</mn><msup><mi mathvariant=\"normal\">x</mi><mn>3</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>8</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>27</mn><msup><mi mathvariant=\"normal\">y</mi><mn>3</mn></msup><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mn>18</mn><mi>xy</mi><mo>)</mo></math></span><span style=\"font-family: Cambria Math;\"> ?</span></p>",
                    question_hi: "<p>7. <span style=\"font-family: Cambria Math;\">यदि</span><span style=\"font-family: Cambria Math;\"> 2x + 3y + 1 = 0, </span><span style=\"font-family: Cambria Math;\">तो</span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><mn>8</mn><msup><mi mathvariant=\"normal\">x</mi><mn>3</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>8</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>27</mn><msup><mi mathvariant=\"normal\">y</mi><mn>3</mn></msup><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mn>18</mn><mi>xy</mi><mo>)</mo></math>&nbsp;</span><span style=\"font-family: Cambria Math;\">का</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">मान</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">क्या</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">है</span><span style=\"font-family: Cambria Math;\">?</span></p>",
                    options_en: ["<p>7<span style=\"font-family: Cambria Math;\"> </span></p>", "<p>-9</p>", 
                                "<p>-7</p>", "<p>9</p>"],
                    options_hi: ["<p>7<span style=\"font-family: Cambria Math;\"> </span></p>", "<p>-9<span style=\"font-family: Cambria Math;\"> </span></p>",
                                "<p>-7<span style=\"font-family: Cambria Math;\"> </span></p>", "<p>9</p>"],
                    solution_en: "<p>7.(a) <span style=\"font-family: Cambria Math;\">2x + 3y + 1 = 0</span><br><span style=\"font-family: Cambria Math;\">2x + 3y = -1</span><br><span style=\"font-family: Cambria Math;\">On cubing both sides,</span><br>(2x + 3y)&sup3; = (-1)&sup3;<br>8x&sup3; + 27y&sup3; + 3.2x.3y(-1) = -1<br>8x&sup3; + 27y&sup3; - 18xy = -1<br>Adding 8 both sides,<br>8x&sup3; + 27y&sup3; - 18xy + 8 = 7</p>",
                    solution_hi: "<p>7.(a) <span style=\"font-family: Cambria Math;\">2x + 3y + 1 = 0</span><br><span style=\"font-family: Cambria Math;\">2x + 3y = -1</span><br>दोनों पक्षों का घन करने पर,<br>(2x + 3y)&sup3; = (-1)&sup3;<br>8x&sup3; + 27y&sup3; + 3.2x.3y(-1) = -1<br>8x&sup3; + 27y&sup3; - 18xy = -1<br>Aदोनों पक्षों में 8 जोड़ने पर,<br>8x&sup3; + 27y&sup3; - 18xy + 8 = 7</p>\n<p>&nbsp;</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "17",
                    question_en: "<p>8. <span style=\"font-family: Cambria Math;\">If&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">x</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mfrac><mn>1</mn><mi mathvariant=\"normal\">x</mi></mfrac><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>7</mn></math> </span><span style=\"font-family: Cambria Math;\">, then <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mfrac><mn>1</mn><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup></mfrac></math></span><span style=\"font-family: Cambria Math;\">&nbsp;</span><span style=\"font-family: Cambria Math;\">is equal to :</span></p>",
                    question_hi: "<p>8.<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">यदि</span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">x</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mfrac><mn>1</mn><mi mathvariant=\"normal\">x</mi></mfrac><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>7</mn></math>&nbsp;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">तो</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mfrac><mn>1</mn><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup></mfrac></math> </span><span style=\"font-family: Cambria Math;\">बराबर</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">है</span><span style=\"font-family: Cambria Math;\">:&nbsp;</span></p>",
                    options_en: ["<p>47<span style=\"font-family: Cambria Math;\"> </span></p>", "<p>49</p>", 
                                "<p>61</p>", "<p>51</p>"],
                    options_hi: ["<p>47</p>", "<p>49</p>",
                                "<p>61</p>", "<p>51</p>"],
                    solution_en: "<p>8.(a) <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">x</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mfrac><mn>1</mn><mi mathvariant=\"normal\">x</mi></mfrac><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>7</mn></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>x</mi><mn>2</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mfrac><mn>1</mn><msup><mi>x</mi><mn>2</mn></msup></mfrac><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><msup><mn>7</mn><mn>2</mn></msup><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mn>2</mn><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>47</mn></math></p>",
                    solution_hi: "<p>8.(a) <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">x</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mfrac><mn>1</mn><mi mathvariant=\"normal\">x</mi></mfrac><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>7</mn></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>x</mi><mn>2</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mfrac><mn>1</mn><msup><mi>x</mi><mn>2</mn></msup></mfrac><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><msup><mn>7</mn><mn>2</mn></msup><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mn>2</mn><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>47</mn></math></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "17",
                    question_en: "<p>9.<span style=\"font-family: Cambria Math;\"> Simplify the following expression.</span><br><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle mathsize=\"18px\"><mfrac><mrow><mn>5</mn><msup><mrow><mo>(</mo><msup><mi mathvariant=\"normal\">a</mi><mn>6</mn></msup><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">b</mi><mn>6</mn></msup><mo>)</mo></mrow><mn>3</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>5</mn><msup><mrow><mo>(</mo><msup><mi mathvariant=\"normal\">b</mi><mn>6</mn></msup><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">c</mi><mn>6</mn></msup><mo>)</mo></mrow><mn>3</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>5</mn><msup><mrow><mo>(</mo><msup><mi mathvariant=\"normal\">c</mi><mn>6</mn></msup><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">a</mi><mn>6</mn></msup><mo>)</mo></mrow><mn>3</mn></msup></mrow><mrow><mn>2</mn><msup><mrow><mo>(</mo><msup><mi mathvariant=\"normal\">a</mi><mn>3</mn></msup><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">b</mi><mn>3</mn></msup><mo>)</mo></mrow><mn>3</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>2</mn><msup><mrow><mo>(</mo><msup><mi mathvariant=\"normal\">b</mi><mn>3</mn></msup><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">c</mi><mn>3</mn></msup><mo>)</mo></mrow><mn>3</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>2</mn><msup><mrow><mo>(</mo><msup><mi mathvariant=\"normal\">c</mi><mn>3</mn></msup><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">a</mi><mn>3</mn></msup><mo>)</mo></mrow><mn>3</mn></msup></mrow></mfrac></mstyle></math></span></p>",
                    question_hi: "<p>9. <span style=\"font-family: Cambria Math;\">निम्नलिखित</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">व्यंजक</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">को</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">सरल</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">कीजिए।</span><br><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle mathsize=\"18px\"><mfrac><mrow><mn>5</mn><msup><mrow><mo>(</mo><msup><mi mathvariant=\"normal\">a</mi><mn>6</mn></msup><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">b</mi><mn>6</mn></msup><mo>)</mo></mrow><mn>3</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>5</mn><msup><mrow><mo>(</mo><msup><mi mathvariant=\"normal\">b</mi><mn>6</mn></msup><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">c</mi><mn>6</mn></msup><mo>)</mo></mrow><mn>3</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>5</mn><msup><mrow><mo>(</mo><msup><mi mathvariant=\"normal\">c</mi><mn>6</mn></msup><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">a</mi><mn>6</mn></msup><mo>)</mo></mrow><mn>3</mn></msup></mrow><mrow><mn>2</mn><msup><mrow><mo>(</mo><msup><mi mathvariant=\"normal\">a</mi><mn>3</mn></msup><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">b</mi><mn>3</mn></msup><mo>)</mo></mrow><mn>3</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>2</mn><msup><mrow><mo>(</mo><msup><mi mathvariant=\"normal\">b</mi><mn>3</mn></msup><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">c</mi><mn>3</mn></msup><mo>)</mo></mrow><mn>3</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>2</mn><msup><mrow><mo>(</mo><msup><mi mathvariant=\"normal\">c</mi><mn>3</mn></msup><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">a</mi><mn>3</mn></msup><mo>)</mo></mrow><mn>3</mn></msup></mrow></mfrac></mstyle></math></span></p>",
                    options_en: ["<p><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>2</mn></mfrac><mo>(</mo><msup><mi mathvariant=\"normal\">a</mi><mn>3</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">b</mi><mn>3</mn></msup><mo>)</mo><mo>(</mo><msup><mi mathvariant=\"normal\">b</mi><mn>3</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">c</mi><mn>3</mn></msup><mo>)</mo><mo>(</mo><msup><mi mathvariant=\"normal\">c</mi><mn>3</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">a</mi><mn>3</mn></msup><mo>)</mo></math></span></p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>2</mn></mfrac><mo>(</mo><msup><mi mathvariant=\"normal\">a</mi><mn>3</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">b</mi><mn>3</mn></msup><mo>)</mo><mo>(</mo><msup><mi mathvariant=\"normal\">b</mi><mn>3</mn></msup><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">c</mi><mn>3</mn></msup><mo>)</mo><mo>(</mo><msup><mi mathvariant=\"normal\">c</mi><mn>3</mn></msup><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">a</mi><mn>3</mn></msup><mo>)</mo></math></p>", 
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>2</mn></mfrac><mo>(</mo><msup><mi mathvariant=\"normal\">a</mi><mn>3</mn></msup><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">b</mi><mn>3</mn></msup><mo>)</mo><mo>(</mo><msup><mi mathvariant=\"normal\">b</mi><mn>3</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">c</mi><mn>3</mn></msup><mo>)</mo><mo>(</mo><msup><mi mathvariant=\"normal\">c</mi><mn>3</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">a</mi><mn>3</mn></msup><mo>)</mo></math></p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>2</mn></mfrac><mo>(</mo><msup><mi mathvariant=\"normal\">a</mi><mn>3</mn></msup><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">b</mi><mn>3</mn></msup><mo>)</mo><mo>(</mo><msup><mi mathvariant=\"normal\">b</mi><mn>3</mn></msup><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">c</mi><mn>3</mn></msup><mo>)</mo><mo>(</mo><msup><mi mathvariant=\"normal\">c</mi><mn>3</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">a</mi><mn>3</mn></msup><mo>)</mo></math></p>"],
                    options_hi: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>2</mn></mfrac><mo>(</mo><msup><mi mathvariant=\"normal\">a</mi><mn>3</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">b</mi><mn>3</mn></msup><mo>)</mo><mo>(</mo><msup><mi mathvariant=\"normal\">b</mi><mn>3</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">c</mi><mn>3</mn></msup><mo>)</mo><mo>(</mo><msup><mi mathvariant=\"normal\">c</mi><mn>3</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">a</mi><mn>3</mn></msup><mo>)</mo></math></p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>2</mn></mfrac><mo>(</mo><msup><mi mathvariant=\"normal\">a</mi><mn>3</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">b</mi><mn>3</mn></msup><mo>)</mo><mo>(</mo><msup><mi mathvariant=\"normal\">b</mi><mn>3</mn></msup><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">c</mi><mn>3</mn></msup><mo>)</mo><mo>(</mo><msup><mi mathvariant=\"normal\">c</mi><mn>3</mn></msup><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">a</mi><mn>3</mn></msup><mo>)</mo></math></p>",
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>2</mn></mfrac><mo>(</mo><msup><mi mathvariant=\"normal\">a</mi><mn>3</mn></msup><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">b</mi><mn>3</mn></msup><mo>)</mo><mo>(</mo><msup><mi mathvariant=\"normal\">b</mi><mn>3</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">c</mi><mn>3</mn></msup><mo>)</mo><mo>(</mo><msup><mi mathvariant=\"normal\">c</mi><mn>3</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">a</mi><mn>3</mn></msup><mo>)</mo></math></p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>2</mn></mfrac><mo>(</mo><msup><mi mathvariant=\"normal\">a</mi><mn>3</mn></msup><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">b</mi><mn>3</mn></msup><mo>)</mo><mo>(</mo><msup><mi mathvariant=\"normal\">b</mi><mn>3</mn></msup><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">c</mi><mn>3</mn></msup><mo>)</mo><mo>(</mo><msup><mi mathvariant=\"normal\">c</mi><mn>3</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">a</mi><mn>3</mn></msup><mo>)</mo></math></p>"],
                    solution_en: "<p>9.(a) <br><span style=\"font-family: Cambria Math;\"><strong id=\"docs-internal-guid-a985a9f8-7fff-d140-3084-a207f8412b6c\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mo>[</mo><msup><mrow><mo>(</mo><msup><mi mathvariant=\"normal\">a</mi><mn>6</mn></msup><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">b</mi><mn>6</mn></msup><mo>)</mo></mrow><mn>3</mn></msup><msup><mrow><mo>(</mo><msup><mi mathvariant=\"normal\">b</mi><mn>6</mn></msup><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">c</mi><mn>6</mn></msup><mo>)</mo></mrow><mn>3</mn></msup><msup><mrow><mo>(</mo><msup><mi mathvariant=\"normal\">c</mi><mn>6</mn></msup><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">a</mi><mn>6</mn></msup><mo>)</mo></mrow><mn>3</mn></msup><mo>]</mo></mrow><mrow><mn>2</mn><mo>[</mo><msup><mrow><mo>(</mo><msup><mi mathvariant=\"normal\">a</mi><mn>3</mn></msup><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">b</mi><mn>3</mn></msup><mo>)</mo></mrow><mn>3</mn></msup><msup><mrow><mo>(</mo><msup><mi mathvariant=\"normal\">b</mi><mn>3</mn></msup><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">c</mi><mn>3</mn></msup><mo>)</mo></mrow><mn>3</mn></msup><msup><mrow><mo>(</mo><msup><mi mathvariant=\"normal\">c</mi><mn>3</mn></msup><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">a</mi><mn>3</mn></msup><mo>)</mo></mrow><mn>3</mn></msup><mo>]</mo></mrow></mfrac></math></strong></span><br><span style=\"font-family: Cambria Math;\">As we know that when a + b + c = 0 then a<sup>3 </sup>+ b<sup>3 </sup>+ c<sup>3</sup> = 3abc</span><br><span style=\"font-family: Cambria Math;\">So,&nbsp;<strong id=\"docs-internal-guid-a985a9f8-7fff-d140-3084-a207f8412b6c\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mo>[</mo><mn>3</mn><mo>(</mo><msup><mi mathvariant=\"normal\">a</mi><mn>6</mn></msup><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">b</mi><mn>6</mn></msup><mo>)</mo><mo>(</mo><msup><mi mathvariant=\"normal\">b</mi><mn>6</mn></msup><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">c</mi><mn>6</mn></msup><mo>)</mo><mo>(</mo><msup><mi mathvariant=\"normal\">c</mi><mn>6</mn></msup><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">a</mi><mn>6</mn></msup><mo>)</mo><mo>]</mo></mrow><mrow><mn>2</mn><mo>[</mo><mn>3</mn><mo>(</mo><msup><mi mathvariant=\"normal\">a</mi><mn>3</mn></msup><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">b</mi><mn>3</mn></msup><mo>)</mo><mo>(</mo><msup><mi mathvariant=\"normal\">b</mi><mn>3</mn></msup><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">c</mi><mn>3</mn></msup><mo>)</mo><mo>(</mo><msup><mi mathvariant=\"normal\">c</mi><mn>3</mn></msup><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">a</mi><mn>3</mn></msup><mo>)</mo><mo>]</mo></mrow></mfrac></math> </strong></span><br><span style=\"font-family: Cambria Math;\"><strong id=\"docs-internal-guid-a985a9f8-7fff-d140-3084-a207f8412b6c\">= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mo>[</mo><mn>3</mn><mo>(</mo><msup><mi mathvariant=\"normal\">a</mi><mn>3</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">b</mi><mn>3</mn></msup><mo>)</mo><mo>(</mo><msup><mi mathvariant=\"normal\">b</mi><mn>3</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">c</mi><mn>3</mn></msup><mo>)</mo><mo>(</mo><msup><mi mathvariant=\"normal\">c</mi><mn>3</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">a</mi><mn>3</mn></msup><mo>)</mo><mo>(</mo><msup><mi mathvariant=\"normal\">a</mi><mn>3</mn></msup><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">b</mi><mn>3</mn></msup><mo>)</mo><mo>(</mo><msup><mi mathvariant=\"normal\">b</mi><mn>3</mn></msup><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">c</mi><mn>3</mn></msup><mo>)</mo><mo>(</mo><msup><mi mathvariant=\"normal\">c</mi><mn>3</mn></msup><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">a</mi><mn>3</mn></msup><mo>)</mo><mo>]</mo></mrow><mrow><mn>2</mn><mo>[</mo><mn>3</mn><mo>(</mo><msup><mi mathvariant=\"normal\">a</mi><mn>3</mn></msup><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">b</mi><mn>3</mn></msup><mo>)</mo><mo>(</mo><msup><mi mathvariant=\"normal\">b</mi><mn>3</mn></msup><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">c</mi><mn>3</mn></msup><mo>)</mo><mo>(</mo><msup><mi mathvariant=\"normal\">c</mi><mn>3</mn></msup><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">a</mi><mn>3</mn></msup><mo>)</mo><mo>]</mo></mrow></mfrac></math></strong></span><br><span style=\"font-family: Cambria Math;\"><strong><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>=</mo><mo>&#160;</mo><mfrac><mn>5</mn><mn>2</mn></mfrac><mo>(</mo><msup><mi mathvariant=\"normal\">a</mi><mn>3</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">b</mi><mn>3</mn></msup><mo>)</mo><mo>(</mo><msup><mi mathvariant=\"normal\">b</mi><mn>3</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">c</mi><mn>3</mn></msup><mo>)</mo><mo>(</mo><msup><mi mathvariant=\"normal\">c</mi><mn>3</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">a</mi><mn>3</mn></msup><mo>)</mo></math></strong></span></p>",
                    solution_hi: "<p>9.(a)<br><span style=\"font-family: Cambria Math;\"><strong id=\"docs-internal-guid-a985a9f8-7fff-d140-3084-a207f8412b6c\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mo>[</mo><msup><mrow><mo>(</mo><msup><mi mathvariant=\"normal\">a</mi><mn>6</mn></msup><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">b</mi><mn>6</mn></msup><mo>)</mo></mrow><mn>3</mn></msup><msup><mrow><mo>(</mo><msup><mi mathvariant=\"normal\">b</mi><mn>6</mn></msup><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">c</mi><mn>6</mn></msup><mo>)</mo></mrow><mn>3</mn></msup><msup><mrow><mo>(</mo><msup><mi mathvariant=\"normal\">c</mi><mn>6</mn></msup><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">a</mi><mn>6</mn></msup><mo>)</mo></mrow><mn>3</mn></msup><mo>]</mo></mrow><mrow><mn>2</mn><mo>[</mo><msup><mrow><mo>(</mo><msup><mi mathvariant=\"normal\">a</mi><mn>3</mn></msup><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">b</mi><mn>3</mn></msup><mo>)</mo></mrow><mn>3</mn></msup><msup><mrow><mo>(</mo><msup><mi mathvariant=\"normal\">b</mi><mn>3</mn></msup><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">c</mi><mn>3</mn></msup><mo>)</mo></mrow><mn>3</mn></msup><msup><mrow><mo>(</mo><msup><mi mathvariant=\"normal\">c</mi><mn>3</mn></msup><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">a</mi><mn>3</mn></msup><mo>)</mo></mrow><mn>3</mn></msup><mo>]</mo></mrow></mfrac></math></strong></span><br><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">जैसा कि हम जानते हैं कि जब</span> a + b + c = 0 <span style=\"font-weight: 400;\">तब&nbsp; </span></span>a<sup>3</sup> + b<sup>3</sup> + c<sup>3</sup> = 3abc<br><span style=\"font-family: Cambria Math;\"><strong id=\"docs-internal-guid-a985a9f8-7fff-d140-3084-a207f8412b6c\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mo>[</mo><mn>3</mn><mo>(</mo><msup><mi mathvariant=\"normal\">a</mi><mn>6</mn></msup><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">b</mi><mn>6</mn></msup><mo>)</mo><mo>(</mo><msup><mi mathvariant=\"normal\">b</mi><mn>6</mn></msup><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">c</mi><mn>6</mn></msup><mo>)</mo><mo>(</mo><msup><mi mathvariant=\"normal\">c</mi><mn>6</mn></msup><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">a</mi><mn>6</mn></msup><mo>)</mo><mo>]</mo></mrow><mrow><mn>2</mn><mo>[</mo><mn>3</mn><mo>(</mo><msup><mi mathvariant=\"normal\">a</mi><mn>3</mn></msup><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">b</mi><mn>3</mn></msup><mo>)</mo><mo>(</mo><msup><mi mathvariant=\"normal\">b</mi><mn>3</mn></msup><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">c</mi><mn>3</mn></msup><mo>)</mo><mo>(</mo><msup><mi mathvariant=\"normal\">c</mi><mn>3</mn></msup><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">a</mi><mn>3</mn></msup><mo>)</mo><mo>]</mo></mrow></mfrac></math> </strong><br><strong id=\"docs-internal-guid-a985a9f8-7fff-d140-3084-a207f8412b6c\">= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mo>[</mo><mn>3</mn><mo>(</mo><msup><mi mathvariant=\"normal\">a</mi><mn>3</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">b</mi><mn>3</mn></msup><mo>)</mo><mo>(</mo><msup><mi mathvariant=\"normal\">b</mi><mn>3</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">c</mi><mn>3</mn></msup><mo>)</mo><mo>(</mo><msup><mi mathvariant=\"normal\">c</mi><mn>3</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">a</mi><mn>3</mn></msup><mo>)</mo><mo>(</mo><msup><mi mathvariant=\"normal\">a</mi><mn>3</mn></msup><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">b</mi><mn>3</mn></msup><mo>)</mo><mo>(</mo><msup><mi mathvariant=\"normal\">b</mi><mn>3</mn></msup><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">c</mi><mn>3</mn></msup><mo>)</mo><mo>(</mo><msup><mi mathvariant=\"normal\">c</mi><mn>3</mn></msup><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">a</mi><mn>3</mn></msup><mo>)</mo><mo>]</mo></mrow><mrow><mn>2</mn><mo>[</mo><mn>3</mn><mo>(</mo><msup><mi mathvariant=\"normal\">a</mi><mn>3</mn></msup><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">b</mi><mn>3</mn></msup><mo>)</mo><mo>(</mo><msup><mi mathvariant=\"normal\">b</mi><mn>3</mn></msup><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">c</mi><mn>3</mn></msup><mo>)</mo><mo>(</mo><msup><mi mathvariant=\"normal\">c</mi><mn>3</mn></msup><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">a</mi><mn>3</mn></msup><mo>)</mo><mo>]</mo></mrow></mfrac></math></strong><br><strong><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>=</mo><mo>&#160;</mo><mfrac><mn>5</mn><mn>2</mn></mfrac><mo>(</mo><msup><mi mathvariant=\"normal\">a</mi><mn>3</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">b</mi><mn>3</mn></msup><mo>)</mo><mo>(</mo><msup><mi mathvariant=\"normal\">b</mi><mn>3</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">c</mi><mn>3</mn></msup><mo>)</mo><mo>(</mo><msup><mi mathvariant=\"normal\">c</mi><mn>3</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">a</mi><mn>3</mn></msup><mo>)</mo></math></strong></span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "17",
                    question_en: "<p>10.<span style=\"font-family: Cambria Math;\"> If y = 2x + 1, then what is the value of&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><mn>8</mn><msup><mi mathvariant=\"normal\">x</mi><mn>3</mn></msup><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">y</mi><mn>3</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>6</mn><mi>xy</mi><mo>)</mo></math> </span><span style=\"font-family: Cambria Math;\">?</span></p>",
                    question_hi: "<p>10.<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">यदि</span><span style=\"font-family: Cambria Math;\"> y = 2x + 1, </span><span style=\"font-family: Cambria Math;\">तो</span><span style=\"font-family: Cambria Math;\"> </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><mn>8</mn><msup><mi mathvariant=\"normal\">x</mi><mn>3</mn></msup><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">y</mi><mn>3</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>6</mn><mi>xy</mi><mo>)</mo></math><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">का</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">मान</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">क्या</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">है</span><span style=\"font-family: Cambria Math;\"> ?</span></p>",
                    options_en: ["<p>-15<span style=\"font-family: Cambria Math;\"> </span></p>", "<p>1</p>", 
                                "<p>-1</p>", "<p>15</p>"],
                    options_hi: ["<p>-15<span style=\"font-family: Cambria Math;\"> </span></p>", "<p>1<span style=\"font-family: Cambria Math;\"> </span></p>",
                                "<p>-1<span style=\"font-family: Cambria Math;\"> </span></p>", "<p>15</p>"],
                    solution_en: "<p>10.(c) <span style=\"font-family: Cambria Math;\">As, two variables can&rsquo;t be solved from one equation, so let one variable according to your choice.</span><br><span style=\"font-family: Cambria Math;\">Let x = 0 then y = 1&nbsp;</span><br>(8x<strong id=\"docs-internal-guid-e16cb401-7fff-4fa4-db3d-f88fbfef1b3e\"><sup>3</sup></strong> - y<strong id=\"docs-internal-guid-e16cb401-7fff-4fa4-db3d-f88fbfef1b3e\"><sup>3</sup></strong> + 6xy) = - y<strong id=\"docs-internal-guid-e16cb401-7fff-4fa4-db3d-f88fbfef1b3e\"><sup>3</sup></strong> = -1<br><strong>Alternatively, </strong>(2x - y)<strong id=\"docs-internal-guid-e16cb401-7fff-4fa4-db3d-f88fbfef1b3e\"><sup>3 </sup></strong>= (-1)<strong id=\"docs-internal-guid-e16cb401-7fff-4fa4-db3d-f88fbfef1b3e\"><sup>3</sup></strong><br>&rArr; 8x<sup>3</sup> - y<sup>3</sup> + 6xy = -1</p>",
                    solution_hi: "<p>10.(c) <span style=\"font-weight: 400;\">चूंकि, एक समीकरण से दो चरों को हल नहीं किया जा सकता है, इसलिए अपनी पसंद के अनुसार एक चर को कुछ भी मान लें।&nbsp;</span><br><span style=\"font-weight: 400;\">माना की,&nbsp;</span><span style=\"font-family: \'Cambria Math\';\">x = 0 तब y = 1</span><br><span style=\"font-family: \'Cambria Math\';\">(8x<sup>3</sup> - y<sup>3</sup> + 6xy) = - y<sup>3</sup> = -1</span><br><span style=\"font-family: \'Cambria Math\';\"><strong id=\"docs-internal-guid-e361af88-7fff-69d6-acff-378c1fef477f\">वैकल्पिक विधि,</strong></span><br>(2x - y)<strong id=\"docs-internal-guid-e16cb401-7fff-4fa4-db3d-f88fbfef1b3e\"><sup>3 </sup></strong>= (-1)<strong id=\"docs-internal-guid-e16cb401-7fff-4fa4-db3d-f88fbfef1b3e\"><sup>3</sup></strong><br>&rArr; 8x<sup>3</sup> - y<sup>3</sup> + 6xy = -1</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "17",
                    question_en: "<p>11. <span style=\"font-family: Cambria Math;\">If&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">x</mi><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mfrac><mn>2</mn><mi mathvariant=\"normal\">x</mi></mfrac><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>15</mn></math> </span><span style=\"font-family: Cambria Math;\">, then what is the value of&nbsp; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mfrac><mn>4</mn><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup></mfrac><mo>)</mo></math></span><span style=\"font-family: Cambria Math;\"> ?</span></p>",
                    question_hi: "<p>11. <span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">यदि</span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">x</mi><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mfrac><mn>2</mn><mi mathvariant=\"normal\">x</mi></mfrac><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>15</mn></math>&nbsp;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">तो</span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mfrac><mn>4</mn><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup></mfrac><mo>)</mo></math></span><span style=\"font-family: Cambria Math;\">&nbsp;</span><span style=\"font-family: Cambria Math;\">का</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">मान</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">क्या</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">है </span><span style=\"font-family: Cambria Math;\">?</span></p>",
                    options_en: ["<p>227<span style=\"font-family: Cambria Math;\"> </span></p>", "<p>221<span style=\"font-family: Cambria Math;\"> </span></p>", 
                                "<p>223<span style=\"font-family: Cambria Math;\"> </span></p>", "<p>229</p>"],
                    options_hi: ["<p>227<span style=\"font-family: Cambria Math;\"> </span></p>", "<p>221<span style=\"font-family: Cambria Math;\"> </span></p>",
                                "<p>223</p>", "<p>229</p>"],
                    solution_en: "<p>11.(d)&nbsp;<br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">x</mi><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mfrac><mn>2</mn><mi mathvariant=\"normal\">x</mi></mfrac><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>15</mn></math> <span style=\"font-family: \'Cambria Math\';\">&hellip;..squaring both side</span><br><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mfrac><mn>4</mn><mrow><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup><mo>&#160;</mo></mrow></mfrac><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mn>4</mn><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>225</mn></math></span><br><span style=\"font-family: Cambria Math;\">&rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mfrac><mn>4</mn><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup></mfrac><mo>)</mo><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>229</mn></math></span></p>",
                    solution_hi: "<p>11.(d)&nbsp;<br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">x</mi><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mfrac><mn>2</mn><mi mathvariant=\"normal\">x</mi></mfrac><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>15</mn></math> <span style=\"font-family: \'Cambria Math\';\">&hellip;..</span><span style=\"font-weight: 400;\">दोनों पक्षों का वर्ग करने पर,</span><br><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mfrac><mn>4</mn><mrow><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup><mo>&#160;</mo></mrow></mfrac><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mn>4</mn><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>225</mn></math></span><br><span style=\"font-family: Cambria Math;\">&rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mfrac><mn>4</mn><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup></mfrac><mo>)</mo><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>229</mn></math></span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "17",
                    question_en: "<p>12. <span style=\"font-family: Cambria Math;\">What is the coefficient of x<sup>2</sup></span><span style=\"font-family: Cambria Math;\">&nbsp;in the expansion of&nbsp; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle mathsize=\"18px\"><msup><mrow><mo>(</mo><mn>5</mn><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mfrac><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup><mn>3</mn></mfrac><mo>)</mo></mrow><mn>3</mn></msup></mstyle></math></span><span style=\"font-family: Cambria Math;\"> ?</span></p>",
                    question_hi: "<p>12. <span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle mathsize=\"18px\"><msup><mrow><mo>(</mo><mn>5</mn><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mfrac><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup><mn>3</mn></mfrac><mo>)</mo></mrow><mn>3</mn></msup></mstyle></math></span><span style=\"font-family: Cambria Math;\">&nbsp;</span><span style=\"font-family: Cambria Math;\">को</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">विस्तृत</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">करने</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">पर</span><span style=\"font-family: Cambria Math;\"> x<sup>2</sup></span><span style=\"font-family: Cambria Math;\">&nbsp;</span><span style=\"font-family: Cambria Math;\">का</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">गुणांक</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">क्या</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">होगा </span><span style=\"font-family: Cambria Math;\">?</span></p>",
                    options_en: ["<p><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>-</mo><mn>25</mn></mrow><mn>3</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> </span></p>", "<p>-25<span style=\"font-family: Cambria Math;\"> </span></p>", 
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>-</mo><mn>5</mn></mrow><mn>3</mn></mfrac></math></p>", "<p>25</p>"],
                    options_hi: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>-</mo><mn>25</mn></mrow><mn>3</mn></mfrac></math></p>", "<p>-25</p>",
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>-</mo><mn>5</mn></mrow><mn>3</mn></mfrac></math></p>", "<p>25</p>"],
                    solution_en: "<p>12.(b)&nbsp;(a - b)<sup>3</sup> = a<sup>3</sup> - 3a<sup>2</sup>b + 3ab<sup>2</sup> - b<sup>3</sup><br>Only term 3a<sup>2</sup>b has x<sup>2</sup> in it<br>-3a<sup>2</sup>b = - 3 &times; 25 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup><mn>3</mn></mfrac></math> = -25</p>",
                    solution_hi: "<p>12.(b) (a - b)<sup>3</sup> = a<sup>3</sup> - 3a<sup>2</sup>b + 3ab<sup>2</sup> - b<sup>3</sup><br>केवल पद 3a<sup>2</sup>b में x<sup>2</sup> है<br>-3a<sup>2</sup>b = - 3 &times; 25 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup><mn>3</mn></mfrac></math> = -25</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "17",
                    question_en: "<p>13.<span style=\"font-family: Cambria Math;\"> If&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi mathvariant=\"normal\">x</mi><mn>4</mn></msup><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mn>62</mn><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>1</mn><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>0</mn></math> </span><span style=\"font-family: Cambria Math;\">, where x &gt; 0, then the value of x<sup>3</sup></span><span style=\"font-family: Cambria Math;\"> +&nbsp;x<sup>-</sup><sup>3 </sup>is :</span></p>",
                    question_hi: "<p>13. <span style=\"font-family: Cambria Math;\">यदि</span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi mathvariant=\"normal\">x</mi><mn>4</mn></msup><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mn>62</mn><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>1</mn><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>0</mn></math>&nbsp;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">जहाँ</span><span style=\"font-family: Cambria Math;\"> x &gt;</span><span style=\"font-family: Cambria Math;\"> 0 </span><span style=\"font-family: Cambria Math;\">है</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">तो</span><span style=\"font-family: Cambria Math;\"> x<sup>3</sup> +&nbsp;x<sup>-</sup><sup>3</sup></span><span style=\"font-family: Cambria Math;\">&nbsp;</span><span style=\"font-family: Cambria Math;\">का</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">मान</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">है</span><span style=\"font-family: Cambria Math;\">:</span></p>",
                    options_en: ["<p>488</p>", "<p>364</p>", 
                                "<p>512</p>", "<p>500</p>"],
                    options_hi: ["<p>488<span style=\"font-family: Cambria Math;\"> </span></p>", "<p>364<span style=\"font-family: Cambria Math;\"> </span></p>",
                                "<p>512</p>", "<p>500</p>"],
                    solution_en: "<p>13.(a)<br><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi mathvariant=\"normal\">x</mi><mn>4</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>62</mn><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>1</mn><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>0</mn></math> dividing both side by&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup></math></span><br><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mfrac><mn>1</mn><mrow><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup><mo>&#160;</mo></mrow></mfrac><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>62</mn></math>&nbsp;adding 2 and taking root on both the side </span><br><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">x</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mfrac><mn>1</mn><mi mathvariant=\"normal\">x</mi></mfrac><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>8</mn></math></span><br><span style=\"font-family: Cambria Math;\">So,&nbsp;</span><br><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi mathvariant=\"normal\">x</mi><mn>3</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">x</mi><mrow><mo>-</mo><mn>3</mn></mrow></msup><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><msup><mn>8</mn><mn>3</mn></msup><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mn>3</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>8</mn><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>512</mn><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mn>24</mn><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>488</mn></math></span></p>",
                    solution_hi: "<p>13.(a)<br><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi mathvariant=\"normal\">x</mi><mn>4</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>62</mn><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>1</mn><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>0</mn></math></span><br><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">दोनों पक्षों को</span> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup></math>&nbsp;</span><span style=\"font-weight: 400;\">से विभाजित करने पर&nbsp;</span><br><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mfrac><mn>1</mn><mrow><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup><mo>&#160;</mo></mrow></mfrac><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>62</mn></math></span><br><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">दोनों तरफ 2 जोड़ने के बाद वर्गमूल लेने पर</span></span><br><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">x</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mfrac><mn>1</mn><mi mathvariant=\"normal\">x</mi></mfrac><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>8</mn></math></span><br><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">इसलिए</span>,&nbsp;</span><br><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi mathvariant=\"normal\">x</mi><mn>3</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">x</mi><mrow><mo>-</mo><mn>3</mn></mrow></msup><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><msup><mn>8</mn><mn>3</mn></msup><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mn>3</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>8</mn><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>512</mn><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mn>24</mn><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>488</mn></math></span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "misc",
                    question_en: "<p>14. <span style=\"font-family: Cambria Math;\">Given that&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi mathvariant=\"normal\">x</mi><mn>8</mn></msup><mo>&#160;</mo><mo>-</mo><mn>34</mn><msup><mi mathvariant=\"normal\">x</mi><mn>4</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>1</mn><mo>=</mo><mn>0</mn></math> </span><span style=\"font-family: Cambria Math;\">, x </span><span style=\"font-family: Cambria Math;\">&gt; 0. What is the value of (x<sup>3</sup></span><span style=\"font-family: Cambria Math;\"> + x<sup>-3</sup>)?</span></p>",
                    question_hi: "<p>14. <span style=\"font-family: Cambria Math;\">दिया</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">गया</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">है</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">कि</span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi mathvariant=\"normal\">x</mi><mn>8</mn></msup><mo>&#160;</mo><mo>-</mo><mn>34</mn><msup><mi mathvariant=\"normal\">x</mi><mn>4</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>1</mn><mo>=</mo><mn>0</mn></math>&nbsp;</span><span style=\"font-family: Cambria Math;\">, x &gt; 0. (x<sup>3</sup> + x<sup>-3</sup>) </span><span style=\"font-family: Cambria Math;\">का</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">मान</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">क्या</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">है </span><span style=\"font-family: Cambria Math;\">?</span></p>",
                    options_en: ["<p>12<span style=\"font-family: Cambria Math;\"> </span></p>", "<p>14<span style=\"font-family: Cambria Math;\"> </span></p>", 
                                "<p>18</p>", "<p>16</p>"],
                    options_hi: ["<p>12</p>", "<p>14<span style=\"font-family: Cambria Math;\"> </span></p>",
                                "<p>18<span style=\"font-family: Cambria Math;\"> </span></p>", "<p>16</p>"],
                    solution_en: "<p>14.(b)<br><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi mathvariant=\"normal\">x</mi><mn>8</mn></msup><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mn>34</mn><msup><mi mathvariant=\"normal\">x</mi><mn>4</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>1</mn><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>0</mn></math> dividing both side by&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi mathvariant=\"normal\">x</mi><mn>4</mn></msup></math></span><br><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi mathvariant=\"normal\">x</mi><mn>4</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mfrac><mn>1</mn><msup><mi mathvariant=\"normal\">x</mi><mn>4</mn></msup></mfrac><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>34</mn></math>&nbsp;adding 2 and taking root on both side </span><br><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mfrac><mn>1</mn><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup></mfrac><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>6</mn></math>&nbsp;subtracting 2 and taking root on both side</span><br><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">x</mi><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mfrac><mn>1</mn><mi mathvariant=\"normal\">x</mi></mfrac><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>2</mn></math>&nbsp;taking cube on both side</span><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi mathvariant=\"normal\">x</mi><mn>3</mn></msup><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mfrac><mn>1</mn><mrow><msup><mi mathvariant=\"normal\">x</mi><mn>3</mn></msup><mo>&#160;</mo></mrow></mfrac><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><msup><mn>2</mn><mn>3</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>2</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>3</mn><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>8</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>6</mn><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>14</mn></math></p>",
                    solution_hi: "<p>14.(b) <span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi mathvariant=\"normal\">x</mi><mn>8</mn></msup><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mn>34</mn><msup><mi mathvariant=\"normal\">x</mi><mn>4</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>1</mn><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>0</mn></math></span><br><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">दोनों पक्षों को <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi mathvariant=\"normal\">x</mi><mn>4</mn></msup></math></span>&nbsp;</span><span style=\"font-weight: 400;\">से विभाजित करने पर&nbsp;</span><br><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi mathvariant=\"normal\">x</mi><mn>4</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mfrac><mn>1</mn><msup><mi mathvariant=\"normal\">x</mi><mn>4</mn></msup></mfrac><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>34</mn></math> </span><br><span style=\"font-weight: 400;\">दोनों तरफ 2 जोड़ने के बाद वर्गमूल लेने पर</span><br><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mfrac><mn>1</mn><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup></mfrac><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>6</mn></math> </span><br><span style=\"font-weight: 400;\">दोनों तरफ 2 घटाने के बाद वर्गमूल लेने पर</span><br><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">x</mi><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mfrac><mn>1</mn><mi mathvariant=\"normal\">x</mi></mfrac><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>2</mn></math> </span><br><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">दोनों पक्षों का घन करने पर,</span></span><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi mathvariant=\"normal\">x</mi><mn>3</mn></msup><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mfrac><mn>1</mn><mrow><msup><mi mathvariant=\"normal\">x</mi><mn>3</mn></msup><mo>&#160;</mo></mrow></mfrac><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><msup><mn>2</mn><mn>3</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>2</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>3</mn><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>8</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>6</mn><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>14</mn></math></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>