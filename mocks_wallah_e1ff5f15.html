<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">8:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 15</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">15</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 8 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["18"] = {
                name: "General Awareness",
                start: 0,
                end: 13
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="18">General Awareness</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 14,
                end: 14
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "18",
                    question_en: "<p>1. What is the contribution of nuclear power in India to the country&rsquo;s overall electricity generation supply?</p>",
                    question_hi: "<p>1. देश की समग्र बिजली उत्पादन आपूर्ति में भारत में नाभिकीय ऊर्जा का क्या योगदान है?</p>",
                    options_en: ["<p>20-25%</p>", "<p>35-40%</p>", 
                                "<p>10-15%</p>", "<p>Less than 5%</p>"],
                    options_hi: ["<p>20-25%</p>", "<p>35-40%</p>",
                                "<p>10-15%</p>", "<p>5% से कम</p>"],
                    solution_en: "<p>1.(d) <strong>Less than 5%</strong>. India has a total installed nuclear power capacity of around 6,780 megawatts (MW) spread across various nuclear power plants. Different energy sources to India\'s electricity generation is as follows: <strong>Thermal Power </strong>(Coal, Gas, and Oil) - Around 60-65% of India\'s electricity generation. <strong>Renewable Energy:</strong> Wind Power: 10-15%, Solar Power: 10-15%, Hydroelectric Power: Around 10 -15%, Biomass Power: Around 2-3%.</p>",
                    solution_hi: "<p>1.(d) <strong>5% से कम। </strong>भारत की कुल स्थापित नाभिकीय ऊर्जा क्षमता लगभग 6,780 मेगावाट (MW) है जो विभिन्न नाभिकीय ऊर्जा संयंत्रों में फैली हुई है। भारत की बिजली उत्पादन के लिए विभिन्न ऊर्जा स्रोत इस प्रकार हैं: <strong>थर्मल पावर</strong> (कोयला, गैस और तेल) - भारत की बिजली उत्पादन का लगभग 60-65%। <strong>नवीकरणीय ऊर्जा :</strong> पवन ऊर्जा : 10-15%, सौर ऊर्जा : 10-15%, जलविद्युत ऊर्जा : लगभग 10 -15%, बायोमास ऊर्जा: लगभग 2-3%।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "2",
                    section: "18",
                    question_en: "<p>2. Which state in India has one of the oldest and largest oil field?</p>",
                    question_hi: "<p>2. भारत के किस राज्य में सबसे पुराना और सबसे बड़ा तेल क्षेत्र है?</p>",
                    options_en: ["<p>Gujarat</p>", "<p>Maharashtra</p>", 
                                "<p>Tamil nadu</p>", "<p>Assam</p>"],
                    options_hi: ["<p>गुजरात</p>", "<p>महाराष्ट्र</p>",
                                "<p>तमिलनाडु</p>", "<p>असम</p>"],
                    solution_en: "<p>2.(d) <strong>Assam</strong>. Digboi is the oil city of Assam. The first oil well of India was dug here and the first refinery was set up in 1901.<strong> Other oil fields in India -</strong> Bombay oil fields (Maharashtra), Ravva oil field (Andhra Pradesh), Aishwarya oil field (Rajasthan), Ashoknagar Oilfield (West Bengal), Mangala Area (Rajasthan).</p>",
                    solution_hi: "<p>2.(d) <strong>असम</strong>। डिगबोई असम का तेल नगर है। यहाँ भारत का पहला तेल कुआँ खोदा गया और प्रथम रिफाइनरी 1901 में स्थापित की गई थी। <strong>भारत के अन्य तेल क्षेत्र -</strong> बॉम्बे तेल क्षेत्र (महाराष्ट्र), राव्वा तेल क्षेत्र (आंध्र प्रदेश), ऐश्वर्या तेल क्षेत्र (राजस्थान), अशोकनगर तेल क्षेत्र (पश्चिम बंगाल), मंगला क्षेत्र (राजस्थान)।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "3",
                    section: "18",
                    question_en: "<p>3. Which of the following comes under the jurisdiction of the Ministry of Mines?</p>",
                    question_hi: "<p>3. निम्नलिखित में से कौन खान मंत्रालय के अधिकार क्षेत्र में आता है?</p>",
                    options_en: ["<p>Coal</p>", "<p>Petroleum</p>", 
                                "<p>Copper</p>", "<p>Natural Gas</p>"],
                    options_hi: ["<p>कोयला</p>", "<p>पेट्रोलियम</p>",
                                "<p>तांबा</p>", "<p>प्राकृतिक गैस</p>"],
                    solution_en: "<p>3.(c) <strong>Copper</strong>. The Ministry of Mines - Responsible for the regulation and development of minerals and mining activities in the country. While petroleum and natural gas fall under the purview of the Ministry of Petroleum and Natural Gas. The Ministry of Coal - Responsible for development and exploitation of coal and lignite reserves.</p>",
                    solution_hi: "<p>3.(c) <strong>ताँबा</strong>। खनन (Mines ) मंत्रालय - देश में खनिजों और खनन गतिविधियों के विनियमन और विकास के लिए जिम्मेदार है। जबकि पेट्रोलियम और प्राकृतिक गैस पेट्रोलियम और प्राकृतिक गैस मंत्रालय के दायरे में आते हैं। कोयला मंत्रालय - कोयला और लिग्नाइट भंडार के विकास और दोहन के लिए जिम्मेदार है ।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "4",
                    section: "18",
                    question_en: "<p>4. In the following group of materials, which group contains only non-biodegradable materials?</p>",
                    question_hi: "<p>4. निम्नलिखित पदार्थो के समूह में, किस समूह में केवल गैर-जैवनिम्नीकरणीय पदार्थ शामिल हैं?</p>",
                    options_en: ["<p>Scraps, cotton, Wool</p>", "<p>Plastic, DDT, bakelite</p>", 
                                "<p>Wood, paper, leather</p>", "<p>Food scraps,animal waste</p>"],
                    options_hi: ["<p>स्क्रैप, कपास, ऊन</p>", "<p>प्लास्टिक, DDT, बैकलाइट</p>",
                                "<p>लकड़ी, कागज, चमड़ा</p>", "<p>खाद्य स्क्रैप, पशु अपशिष्ट</p>"],
                    solution_en: "<p>4.(b) <strong>Plastic, DDT, bakelite. Non-biodegradable:</strong> Cannot be decomposed into simpler forms by the action of microorganisms. Examples - Plastic polythenes, plastic bottles, Plastic-related products, Glass materials, Metals, batteries and a large number of medical wastes. <strong>Biodegradable</strong>: Can be decomposed into simpler forms by the action of microorganisms. Examples - Wood, Leaves, Papers, Dead bodies of the Animals, Eggshells, Food materials, Animal wastes and Human wastes.</p>",
                    solution_hi: "<p>4.(b) <strong>प्लास्टिक, DDT, बैकेलाइट। नॉन-बायोडिग्रेडेबल:</strong> सूक्ष्मजीवों की क्रिया द्वारा सरल रूपों में विघटित नहीं किया जा सकता है। उदाहरण - प्लास्टिक पॉलिथीन, प्लास्टिक की बोतलें, प्लास्टिक से संबंधित उत्पाद, कांच सामग्री, धातु, बैटरी और बड़ी संख्या में चिकित्सा अपशिष्ट। <strong>बायोडिग्रेडेबल</strong>: सूक्ष्मजीवों की क्रिया द्वारा सरल रूपों में विघटित किया जा सकता है। उदाहरण - लकड़ी, पत्तियाँ, कागज़, जानवरों के शव, अंडे के छिलके, खाद्य सामग्री, जानवरों के अपशिष्ट और मानव अपशिष्ट।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "5",
                    section: "18",
                    question_en: "<p>5. Which of the following minerals is contained in bauxite, a clay like substance?</p>",
                    question_hi: "<p>5. निम्नलिखित में से कौन सा खनिज बॉक्साइट, एक मिट्टी जैसे पदार्थ में निहित है?</p>",
                    options_en: ["<p>Coal</p>", "<p>Aluminium</p>", 
                                "<p>Iron</p>", "<p>Thorium</p>"],
                    options_hi: ["<p>कोयला</p>", "<p>एल्यूमिनियम</p>",
                                "<p>लोहा</p>", "<p>थोरियम</p>"],
                    solution_en: "<p>5.(b) <strong>Aluminium </strong>(Al, atomic number-13). <strong>Bauxite</strong> is a mixture of hydrous aluminum oxides, aluminum hydroxides, clay minerals, and insoluble materials such as quartz, hematite, magnetite, siderite, and goethite. The aluminum minerals in bauxite can include: <strong>gibbsite </strong>Al(OH)<sub>3</sub>, <strong>boehmite </strong>(&gamma;-AlO(OH)), and diaspore (&alpha;-AlO(OH)).</p>",
                    solution_hi: "<p>5.(b) एल्यूमिनियम (अल, परमाणु क्रमांक-13)। <strong>बॉक्साइट </strong>जलीय एल्यूमीनियम ऑक्साइड, एल्यूमीनियम हाइड्रॉक्साइड, मिट्टी के खनिज और अघुलनशील पदार्थों जैसे क्वार्ट्ज, हेमेटाइट, मैग्नेटाइट, साइडराइट और गोइथाइट का मिश्रण है। बॉक्साइट में एल्यूमीनियम खनिज :<strong>गिबसाइट </strong>(&gamma;-AlO(OH)<sub>3</sub>, <strong>बोहेमाइट </strong>(&gamma;-AlO(OH)), और डायस्पोर (&alpha;-AlO(OH) शामिल हो सकते हैं।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "6",
                    section: "18",
                    question_en: "<p>6. _________ is the largest producer of crude oil and natural gas in India.</p>",
                    question_hi: "<p>6. _________ भारत में कच्चे तेल और प्राकृतिक गैस का सबसे बड़ा उत्पादक है।</p>",
                    options_en: ["<p>BHEL</p>", "<p>ONGC</p>", 
                                "<p>NTPC</p>", "<p>SAIL</p>"],
                    options_hi: ["<p>BHEL</p>", "<p>ONGC</p>",
                                "<p>NTPC</p>", "<p>SAIL</p>"],
                    solution_en: "<p>6.(b) <strong>ONGC </strong>(Oil and Natural Gas Corporation). Headquarters - New Delhi. <strong>Founded </strong>- 14 August 1956. It is a Maharatna Company. <strong>Maharatna Companies:</strong> BHEL (Bharat Heavy Electricals Limited), NTPC (National Thermal Power Corporation), SAIL (Steel Authority of India Limited), CIL (Coal India Limited), IOCL (Indian Oil Corporation Limited).</p>",
                    solution_hi: "<p>6.(b) <strong>ONGC </strong>(तेल और प्राकृतिक गैस निगम)। मुख्यालय - नई दिल्ली I <strong>स्थापना </strong>- 14 अगस्त 1956। यह एक महारत्न कंपनी है। <strong>महारत्न कंपनियां:</strong> BHEL (भारत हेवी इलेक्ट्रिकल्स लिमिटेड), NTPC (नेशनल थर्मल पावर कॉर्पोरेशन), SAIL (स्टील अथॉरिटी ऑफ इंडिया लिमिटेड), CIL (कोल इंडिया लिमिटेड), IOCL (इंडियन ऑयल कॉर्पोरेशन लिमिटेड)।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "7",
                    section: "18",
                    question_en: "<p>7. Which of the following resources is fossil fuel?</p>",
                    question_hi: "<p>7. निम्नलिखित में से कौन सा संसाधन जीवाश्म ईंधन है?</p>",
                    options_en: ["<p>Wind Power</p>", "<p>Coal</p>", 
                                "<p>Hydropower</p>", "<p>Nuclear Power</p>"],
                    options_hi: ["<p>पवन ऊर्जा</p>", "<p>कोयला</p>",
                                "<p>जल विद्युत</p>", "<p>परमाणु ऊर्जा</p>"],
                    solution_en: "<p>7.(b) <strong>Coal </strong>(Classified into four main types: Peat, Lignite, Bituminous and Anthracite). <strong>Renewable energy -</strong> Energy derived from naturally replenishing sources that are practically inexhaustible. <strong>Source of Renewable energy -</strong> Solar Energy, Wind Energy, Hydropower, Biomass Energy, Geothermal Energy, Tidal Energy. <strong>Non-renewable energy -</strong> Includes fossil fuels like, oil, and natural gas and nuclear energy.</p>",
                    solution_hi: "<p>7.(b) <strong>कोयला </strong>(चार मुख्य प्रकारों में वर्गीकृत: पीट, लिग्नाइट, बिटुमिनस और एन्थ्रेसाइट)। <strong>नवीकरणीय ऊर्जा - </strong>प्राकृतिक रूप से पुनःपूर्ति करने वाले स्रोतों से प्राप्त ऊर्जा जो व्यावहारिक रूप से अक्षय हैं। <strong>नवीकरणीय ऊर्जा के स्रोत -</strong> सौर ऊर्जा, पवन ऊर्जा, जल विद्युत, बायोमास ऊर्जा, भूतापीय ऊर्जा, ज्वारीय ऊर्जा। <strong>गैर-नवीकरणीय ऊर्जा - </strong>इसमें तेल, प्राकृतिक गैस और परमाणु ऊर्जा जैसे जीवाश्म ईंधन शामिल हैं।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "8",
                    section: "18",
                    question_en: "<p>8. Where is the headquarters of Nuclear Power Corporation of India Limited (NPCIL)?</p>",
                    question_hi: "<p>8. न्यूक्लियर पावर कॉरपोरेशन ऑफ इंडिया लिमिटेड (NPCIL) का मुख्यालय कहाँ है?</p>",
                    options_en: ["<p>Guwahati</p>", "<p>Mumbai</p>", 
                                "<p>Delhi</p>", "<p>Kanpur</p>"],
                    options_hi: ["<p>गुवाहाटी</p>", "<p>मुंबई</p>",
                                "<p>दिल्ली</p>", "<p>कानपुर</p>"],
                    solution_en: "<p>8.(b) <strong>Mumbai. Nuclear Power Corporation of India Limited (NPCIL) -</strong> It was founded on 1 September, 1987. It is an Indian public sector undertaking (PSU) and is responsible for the generation of nuclear power for electricity. NPCIL is administered by the Department of Atomic Energy (DAE).</p>",
                    solution_hi: "<p>8.(b) <strong>मुंबई। न्यूक्लियर पावर कॉरपोरेशन ऑफ इंडिया लिमिटेड (NPCIL) - </strong>इसकी स्थापना 1 सितंबर, 1987 को हुई थी। यह एक भारतीय सार्वजनिक क्षेत्र का उपक्रम (PSU) है और बिजली के लिए परमाणु ऊर्जा उत्पादन के लिए उत्तरदायी है। NPCIL का संचालन परमाणु ऊर्जा विभाग (DAE) द्वारा किया जाता है।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "9",
                    section: "18",
                    question_en: "<p>9. Which of the following is used as a fuel in nuclear reactors?</p>",
                    question_hi: "<p>9. निम्नलिखित में से किसका उपयोग परमाणु रिएक्टरों में ईंधन के रूप में किया जाता है?</p>",
                    options_en: ["<p>Copper</p>", "<p>Cobalt</p>", 
                                "<p>Uranium</p>", "<p>Iodine</p>"],
                    options_hi: ["<p>कॉपर</p>", "<p>कोबाल्ट</p>",
                                "<p>यूरेनियम</p>", "<p>आयोडीन</p>"],
                    solution_en: "<p>9.(c) <strong>Uranium</strong>. Nuclear power plants use a certain type of uranium U-235 as fuel because its atoms are easily split apart. List of <strong>Nuclear power plants in India with their establishment years and states :</strong> Kakrapar Atomic Power Station (1993) - Gujarat, Madras Atomic Power Station (1984) - Tamil Nadu, Narora Atomic Power Station (1991) - Uttar Pradesh, Kaiga Nuclear Power Plant (2000) - Karnataka, Tarapur Atomic Power Station (1969) - Maharashtra.</p>",
                    solution_hi: "<p>9.(c) <strong>यूरेनियम</strong>। यूरेनियम U-235 का उपयोग परमाणु ऊर्जा संयंत्र ईंधन के रूप में करते हैं क्योंकि इसके परमाणु आसानी से विभाजित हो जाते हैं। <strong>भारत में परमाणु ऊर्जा संयंत्रों की सूची, उनकी स्थापना के वर्ष और राज्य : </strong>काकरापार परमाणु ऊर्जा स्टेशन (1993) - गुजरात, मद्रास परमाणु ऊर्जा स्टेशन (1984) - तमिलनाडु, नरोरा परमाणु ऊर्जा स्टेशन (1991) - उत्तर प्रदेश, कैगा परमाणु ऊर्जा संयंत्र (2000) - कर्नाटक, तारापुर परमाणु ऊर्जा स्टेशन (1969) - महाराष्ट्र।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "10",
                    section: "18",
                    question_en: "<p>10. The Hasdeo Valley in Chhattisgarh is famous for _________mines.</p>",
                    question_hi: "<p>10. छत्तीसगढ़ में स्थित हसदेव घाटी _________ की खादानों के लिए प्रसिद्ध है।</p>",
                    options_en: ["<p>silicon</p>", "<p>silver</p>", 
                                "<p>panna</p>", "<p>coal</p>"],
                    options_hi: ["<p>सिलिकॉन</p>", "<p>चांदी</p>",
                                "<p>पन्ना</p>", "<p>कोयला</p>"],
                    solution_en: "<p>10.(d) <strong>Coal</strong>. Hasdeo Valley is a forest and it is home to a diverse ecology and adivasi communities such as the Gonds. Chirimiri Coalfield, Korba Coalfield is located in the valley of Hasdeo river. It is also home to a number of other minerals, including iron ore, bauxite, and limestone.</p>",
                    solution_hi: "<p>10.(d) <strong>कोयला</strong>। हसदेव घाटी एक जंगल है और यह विविध पारिस्थितिकी और गोंड जैसे आदिवासी समुदायों का घर है। चिरिमिरी कोयला क्षेत्र, कोरबा कोयला क्षेत्र हसदेव नदी की घाटी में स्थित है। यह लौह अयस्क, बॉक्साइट और चूना पत्थर सहित कई अन्य खनिजों का भी भंडार है।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "11",
                    section: "18",
                    question_en: "11. The Tuticorin Thermal Power Station is situated in the state of:",
                    question_hi: "11. तूतीकोरिन थर्मल पावर स्टेशन किस राज्य में स्थित है? ",
                    options_en: [" Tamil Nadu", " Karnataka", 
                                " Telangana", " Maharashtra<br /> "],
                    options_hi: [" तमिलनाडु", " कर्नाटक",
                                " तेलंगाना", " महाराष्ट्र"],
                    solution_en: "<p>11.(a) <strong>Tamil Nadu.</strong> Tamil Nadu has<strong> four coal - based thermal power plants:</strong> Ennore, Mettur, North Chennai (Biggest in Tamil Nadu), and Tuticorin (second - highest in production). The Vindhyachal Thermal Power Station in the Singrauli district of Madhya Pradesh, with an installed capacity of 4,760 MW, is the biggest thermal power plant in India.</p>",
                    solution_hi: "<p>11.(a) <strong>तमिलनाडु </strong>। तमिलनाडु में <strong>चार कोयला-आधारित थर्मल पावर प्लांट हैं:</strong> एन्नोर, मेट्टूर, उत्तरी चेन्नई (तमिलनाडु में सबसे बड़ा), और तूतीकोरिन (उत्पादन में दूसरा- सबसे अधिक)। मध्य प्रदेश के सिंगरौली जिले में विंध्याचल थर्मल पावर स्टेशन, 4,760 मेगावाट क्षमता के साथ स्थापित , भारत का सबसे बड़ा थर्मल पावर प्लांट है।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "12",
                    section: "18",
                    question_en: "<p>12. In which state is the &lsquo;Badampahar&rsquo; iron - ore mine located?</p>",
                    question_hi: "<p>12. लौह-अयस्क खदान \'बदमपहाड़\' किस राज्य में स्थित है?</p>",
                    options_en: ["<p>Madhya Pradesh</p>", "<p>Odisha</p>", 
                                "<p>Karnataka</p>", "<p>Maharashtra</p>"],
                    options_hi: ["<p>मध्य प्रदेश</p>", "<p>ओडिशा</p>",
                                "<p>कर्नाटक</p>", "<p>महाराष्ट्र</p>"],
                    solution_en: "<p>12.(b) <strong>Odisha</strong>. Badampahar mines are located in the Mayurbhanj and Kendujhar districts, where high-grade hematite ore is found. Hematite has the best physical qualities required to make steel, jewellery, or precious gems. The only working mines of <strong>diamond</strong> in India are in Panna district of <strong>Madhya Pradesh.</strong> Kolar gold mine is in Karnataka..</p>",
                    solution_hi: "<p>12.(b) <strong>ओडिशा</strong>। बादामपहाड़ की खदानें मयूरभंज और केंदुझार जिलों में स्थित हैं, जहाँ उच्च श्रेणी का हेमेटाइट अयस्क पाया जाता है। हेमेटाइट में स्टील, आभूषण या कीमती रत्न बनाने के लिए आवश्यक सर्वोत्तम भौतिक गुण हैं। भारत में <strong>हीरे </strong>की एकमात्र चालू खदानें <strong>मध्य प्रदेश </strong>के पन्ना जिले में हैं। कोलार सोने की खान कर्नाटक में है।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "13",
                    section: "18",
                    question_en: "<p>13. Which of the following states became India&rsquo;s prime producer of Vanadium?</p>",
                    question_hi: "<p>13. भारत का निम्नलिखित में से कौन सा राज्य वैनेडियम का प्रमुख उत्पादक बन गया है?</p>",
                    options_en: ["<p>Odisha</p>", "<p>West Bengal</p>", 
                                "<p>Arunachal Pradesh</p>", "<p>Madhya Pradesh</p>"],
                    options_hi: ["<p>ओडिशा</p>", "<p>पश्चिम बंगाल</p>",
                                "<p>अरुणाचल प्रदेश</p>", "<p>मध्य प्रदेश</p>"],
                    solution_en: "<p>13.(c) <strong>Arunachal Pradesh. Vanadium</strong> (V) - It is a rare, hard, ductile gray-white element found combined in certain minerals and used mainly to produce certain alloys. It is a chemical element with atomic number 23, Discovered by Andres Manuel in 1801. <strong>Odisha</strong> is the leading iron ore producer in India.<strong> Madhya Pradesh</strong> (Panna) - The only industrial-scale diamond mine in India.</p>",
                    solution_hi: "<p>13.(c) <strong>अरुणाचल प्रदेश। वैनेडियम (V</strong>) - यह एक दुर्लभ, कठोर, लचीला ग्रे-सफेद तत्व है जो कुछ खनिजों में संयुक्त रूप से पाया जाता है और मुख्य रूप से कुछ मिश्र धातुओं का उत्पादन करने के लिए उपयोग किया जाता है। यह परमाणु संख्या 23 वाला एक रासायनिक तत्व है, जिसकी खोज 1801 में एंड्रेस मैनुअल ने की थी। <strong>ओडिशा </strong>भारत में प्रमुख लौह अयस्क उत्पादक है। <strong>मध्य प्रदेश</strong> (पन्ना) - भारत में एकमात्र औद्योगिक पैमाने की हीरे की खदान है।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "14",
                    section: "18",
                    question_en: "<p>14. In which of the following districts is the DulHasti Power Station situated?</p>",
                    question_hi: "<p>14.दुलहस्ती पावर स्टेशन निम्नलिखित में से किस जिले में स्थित है?</p>",
                    options_en: ["<p>Kishtwar</p>", "<p>Donda</p>", 
                                "<p>Poonch</p>", "<p>Anantnag</p>"],
                    options_hi: ["<p>किश्तवाड़</p>", "<p>डोंडा</p>",
                                "<p>पुंछ</p>", "<p>अनंतनाग</p>"],
                    solution_en: "<p>14.(a) <strong>Kishtwar </strong>(Jammu and Kashmir). Dulhasti power station is run-of-the-river with a <strong>pondage scheme</strong> with an installed capacity of 390 MW to harness the hydropower potential of river Chenab. The Power Station was commissioned in 2007. <strong>List of Hydro Electric Projects in Jammu and Kashmir - </strong>Baglihar Hydroelectric Project, Salal Hydroelectric Project, Sewa Hydroelectric Project, Chenani Hydroelectric Project.</p>",
                    solution_hi: "<p>14.(a) <strong>किश्तवाड़ </strong>(जम्मू-कश्मीर)। दुलहस्ती पावर स्टेशन चिनाब नदी की जलविद्युत क्षमता का उपयोग करने के लिए 390 MW की स्थापित क्षमता के साथ एक <strong>जल संचयन योजना</strong> के साथ रन-ऑफ-द-रिवर है। पावर स्टेशन 2007 में चालू किया गया था। <strong>जम्मू और कश्मीर में जलविद्युत परियोजनाओं की सूची -</strong> बगलिहार जलविद्युत परियोजना, सलाल जलविद्युत परियोजना, सेवा जलविद्युत परियोजना, चेनानी जलविद्युत परियोजना।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "15",
                    section: "misc",
                    question_en: "<p>15. The Gua mines of Jharkhand are associated with which of the following minerals?</p>",
                    question_hi: "<p>15. झारखंड की गुआ खदानें निम्नलिखित में से किस खनिज से जुड़ी हैं?</p>",
                    options_en: ["<p>Bauxite</p>", "<p>Iron-ore</p>", 
                                "<p>Zinc</p>", "<p>Coal</p>"],
                    options_hi: ["<p>बॉक्साइट</p>", "<p>लौह-अयस्क</p>",
                                "<p>जस्ता</p>", "<p>कोयला</p>"],
                    solution_en: "<p>15.(b) <strong>Iron-Ore.</strong> Gua mines are found in Jharkhand in Chhota Nagpur Plateau. Odisha is India\'s leading bauxite producer (Kalahandi, Sambalpur, and Panchpatali). Zinc mines - Rampura Agucha Mine, Sindesar Khurd Mine, Zawar Mine, Kayad Mine, and Rajpura Dariba Mine. Major Coal mines - Jharkhand (Jharia, Dhanbad, Bokaro, Jayanti, Godda, Giridih), West Bengal (Raniganj Coalfield, Dalingkot, Birbhum, Chinakuri).</p>",
                    solution_hi: "<p>15.(b) <strong>लौह अयस्क।</strong> झारखंड में छोटा नागपुर के पठार में गुआ की खानें पाई जाती हैं। ओडिशा भारत का प्रमुख बॉक्साइट उत्पादक (कालाहांडी, संबलपुर और पंचपताली) है। जस्ता की खदानें - रामपुरा अगुचा खदान, सिंदेसर खुर्द खदान, जावर खदान, कयाद खदान और राजपुरा दरीबा खदान। प्रमुख कोयला खदानें - झारखंड (झरिया, धनबाद, बोकारो, जयंती, गोड्डा, गिरिडीह), पश्चिम बंगाल (रानीगंज कोलफील्ड, डालिंगकोट, बीरभूम, चिनकुरी)।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>