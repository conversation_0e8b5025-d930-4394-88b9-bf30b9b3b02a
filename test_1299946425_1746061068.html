<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Smart Test Interface</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        .question-nav{position:fixed;top:70px;right:0;height:calc(100vh - 70px);width:320px;transition:all .3s;z-index:100;display:flex;flex-direction:column}
        .question-content{margin-right:0;transition:all .3s}
        .question-box{width:40px;height:40px;margin:5px;cursor:pointer}
        .attempted{background-color:#3498db!important;color:white}
        .correct{background-color:#2ecc71!important;color:white}
        .incorrect{background-color:#e74c3c!important;color:white}
        .current{border:3px solid #f39c12!important}
        #question-boxes{overflow-y:auto;max-height:calc(100vh - 150px);padding-bottom:15px}
        @media (max-width:768px){.question-nav{width:100%;right:-100%}.question-content{margin-right:0}}
    </style>
</head>
<body>
    <div class="container-fluid p-0">
        <!-- Header with controls -->
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary sticky-top">
            <div class="container-fluid">
                <a class="navbar-brand" href="#">
                    <i class="fas fa-graduation-cap me-2"></i>Smart Test
                </a>
                <!-- Section selector -->
                <div class="navbar-nav me-auto section-tabs">
                    <select class="form-select" id="section-selector" onchange="switchSection(this.value)">
                        <option value="all">All Sections</option>
                    </select>
                </div>
                <div class="d-flex align-items-center">
                    <!-- Timer -->
                    <div class="bg-light text-primary rounded px-3 py-2 me-2 fw-bold" id="timer">
                        <i class="far fa-clock me-2"></i><span id="timer-display">100:00</span>
                    </div>
                    <!-- Submit button -->
                    <button class="btn btn-success me-2" id="submit-btn" onclick="submitTest()">
                        <i class="fas fa-check me-1"></i> Submit
                    </button>
                    <!-- Language toggle -->
                    <button class="btn btn-light me-2" id="lang-btn" onclick="toggleLanguage()">
                        <i class="fas fa-language"></i>
                    </button>
                    <!-- Menu toggle for mobile -->
                    <button class="btn btn-light d-lg-none" id="menu-toggle" onclick="toggleMenu()">
                        <i class="fas fa-th"></i>
                    </button>
                </div>
            </div>
        </nav>
        <div class="row m-0">
            <!-- Main content area -->
            <div class="col p-0 question-content" id="question-content">
                <div class="container py-4">
                    <!-- Question navigation -->
                    <div class="d-flex justify-content-between mb-4">
                        <button class="btn btn-outline-primary" onclick="prevQuestion()">
                            <i class="fas fa-chevron-left me-1"></i> Previous
                        </button>
                        <div class="fw-bold" id="question-counter">Question 1 of 100</div>
                        <button class="btn btn-outline-primary" onclick="nextQuestion()">
                            Next <i class="fas fa-chevron-right ms-1"></i>
                        </button>
                    </div>
                    <!-- Questions will be displayed here -->
                    <div id="questions-container"></div>
                </div>
            </div>
            <!-- Question navigation sidebar -->
            <div class="bg-light question-nav" id="question-nav">
                <div class="p-3">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h5 class="m-0">Questions</h5>
                        <div>
                            <span class="badge bg-secondary me-1" id="attempted-count">0</span>
                            <span class="badge bg-secondary" id="total-count">100</span>
                            <button class="btn btn-sm btn-outline-secondary ms-2 d-lg-none" onclick="toggleMenu()">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>
                    <div class="d-flex flex-wrap" id="question-boxes"></div>
                </div>
            </div>
        </div>
        <!-- Results modal -->
        <div class="modal fade" id="results-modal" tabindex="-1" aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content">
                    <div class="modal-header bg-primary text-white">
                        <h5 class="modal-title">Test Results</h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row text-center">
                            <div class="col-6 mb-3">
                                <div class="p-3 bg-primary bg-opacity-10 rounded">
                                    <h3 id="score-value">0</h3>
                                    <div class="small text-muted">Score</div>
                                </div>
                            </div>
                            <div class="col-6 mb-3">
                                <div class="p-3 bg-success bg-opacity-10 rounded">
                                    <h3 id="correct-value">0</h3>
                                    <div class="small text-muted">Correct</div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="p-3 bg-danger bg-opacity-10 rounded">
                                    <h3 id="incorrect-value">0</h3>
                                    <div class="small text-muted">Incorrect</div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="p-3 bg-secondary bg-opacity-10 rounded">
                                    <h3 id="unattempted-value">0</h3>
                                    <div class="small text-muted">Unattempted</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-primary" onclick="reviewTest()">Review Test</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 100 * 60;
        let resultsModal;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            resultsModal = new bootstrap.Modal(document.getElementById('results-modal'));
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 768) {
                document.getElementById('question-content').style.marginRight = '320px';
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["6"] = {
                name: "Reasoning",
                start: 0,
                end: 23
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="6">Reasoning</option>`;
            sections["18"] = {
                name: "General Awareness",
                start: 24,
                end: 48
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="18">General Awareness</option>`;
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 49,
                end: 73
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["7"] = {
                name: "English",
                start: 74,
                end: 98
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="7">English</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 99,
                end: 99
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "6",
                    question_en: "<p>1. Select the option that represents the letters that when placed from left to right in the blanks below will complete the given letter series.<br>N _ B A N A _ A _ A N A N A B _ N A N A B A _ A N A</p>",
                    question_hi: "<p>1. उस विकल्प का चयन करें जो उन अक्षरों को निरूपित करता है जिन्&zwj;हें नीचे दिए गए रिक्त स्थानों में बाएँ से दाएँ रखे जाने पर दी गई अक्षर श्रृंखला पूरी हो जाएगी।<br>N _ B A N A _ A _ A N A N A B _ N A N A B A _ A N A</p>",
                    options_en: [
                        "<p>ANANB</p>",
                        "<p>NANAB</p>",
                        "<p>ANABN</p>",
                        "<p>ANBAN</p>"
                    ],
                    options_hi: [
                        "<p>ANANB</p>",
                        "<p>NANAB</p>",
                        "<p>ANABN</p>",
                        "<p>ANBAN</p>"
                    ],
                    solution_en: "<p>1.(d)<br>N<span style=\"text-decoration: underline;\"><strong>A</strong></span>BANA/<span style=\"text-decoration: underline;\"><strong>N</strong></span>A<span style=\"text-decoration: underline;\"><strong>B</strong></span>ANA/NAB<span style=\"text-decoration: underline;\"><strong>A</strong></span>NA/NABA<span style=\"text-decoration: underline;\"><strong>N</strong></span>A/NA</p>",
                    solution_hi: "<p>1.(d)<br>N<span style=\"text-decoration: underline;\"><strong>A</strong></span>BANA/<span style=\"text-decoration: underline;\"><strong>N</strong></span>A<span style=\"text-decoration: underline;\"><strong>B</strong></span>ANA/NAB<span style=\"text-decoration: underline;\"><strong>A</strong></span>NA/NABA<span style=\"text-decoration: underline;\"><strong>N</strong></span>A/NA</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "6",
                    question_en: "<p>2. In this question, three statements are given, followed by two conclusions numbered I and II. Assuming the statements to be true, even if they seem to be at variance with commonly known facts, decide which of the conclusion(s) logically follows/follow from the statements. <br><strong>Statements :</strong> <br>All teachers are dancers. <br>All dancers are engineers. <br>Some engineers are beautiful. <br><strong>Conclusions :</strong> <br>I. All teachers are beautiful. <br>II. Some dancers are beautiful.</p>",
                    question_hi: "<p>2. इस प्रश्न में तीन कथन दिए गए हैं, जिसके बाद दो निष्कर्ष I और II दिए गए हैं। कथनों को सत्य मानते हुए, भले ही वे सामान्य रूप से ज्ञात तथ्यों से भिन्न प्रतीत होते हों, यह निर्णय लीजिए कि कौन-सा/से निष्कर्ष कथनों का तार्किक रूप से अनुसरण करता है/करते हैं।&nbsp;<br><strong>कथन :</strong> <br>सभी शिक्षक, नर्तक हैं। <br>सभी नर्तक, इंजीनियर हैं। <br>कुछ इंजीनियर, सुंदर हैं। <br><strong>निष्कर्ष :</strong> <br>I. सभी शिक्षक, सुंदर हैं। <br>II. कुछ नर्तक, सुंदर हैं।</p>",
                    options_en: [
                        "<p>Both conclusions I and II follow.</p>",
                        "<p>Neither conclusion I nor II follows.</p>",
                        "<p>Only conclusion II follows.</p>",
                        "<p>Only conclusion I follows</p>"
                    ],
                    options_hi: [
                        "<p>निष्कर्ष I और II दोनों अनुसरण करते हैं।</p>",
                        "<p>न तो निष्कर्ष I और न ही II अनुसरण करता है।</p>",
                        "<p>केवल निष्कर्ष II अनुसरण करता है।</p>",
                        "<p>केवल निष्कर्ष I अनुसरण करता है।</p>"
                    ],
                    solution_en: "<p>2.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744431423168.png\" alt=\"rId5\" width=\"260\" height=\"131\"><br>Neither conclusion I nor II follows.</p>",
                    solution_hi: "<p>2.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744431423349.png\" alt=\"rId6\" width=\"287\" height=\"131\"><br>न तो निष्कर्ष I और न ही II अनुसरण करता है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "6",
                    question_en: "<p>3. Select the number from among the given options that can replace the question mark (?) in the following series.<br>389, 420, 457, 498, 541, ?</p>",
                    question_hi: "<p>3. दिए गए विकल्पों में से उस संख्या का चयन कीजिए जो निम्नलिखित श्रृंखला में प्रश्न चिह्न (?) के स्&zwj;थान पर आ सकती है।<br>389, 420, 457, 498, 541, ?</p>",
                    options_en: [
                        "<p>588</p>",
                        "<p>584</p>",
                        "<p>598</p>",
                        "<p>572</p>"
                    ],
                    options_hi: [
                        "<p>588</p>",
                        "<p>584</p>",
                        "<p>598</p>",
                        "<p>572</p>"
                    ],
                    solution_en: "<p>3.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744431423668.png\" alt=\"rId7\" width=\"223\" height=\"68\"></p>",
                    solution_hi: "<p>3.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744431423977.png\" alt=\"rId8\" width=\"224\" height=\"74\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "6",
                    question_en: "<p>4. Based on the English alphabetical order, three of the following four letter-clusters are alike in a certain way and thus form a group. Which letter-cluster does not belong to that group? <br>(Note : The odd letter-cluster is not based on the number of consonants/vowels or their position in the letter-cluster.)</p>",
                    question_hi: "<p>4. अंग्रेजी वर्णमाला क्रम पर आधारित, निम्नलिखित चार अक्षर-समूहों में से तीन किसी निश्चित तरीके से समान हैं और इस प्रकार एक समूह बनाते हैं। किस अक्षर-समूह का संबंध उस समूह से नहीं है? <br>(नोट : असंगत अक्षर-समूह, व्यंजनों/स्वरों की संख्या या इस अक्षर-समूह में उनकी स्थिति पर आधारित नहीं है।)</p>",
                    options_en: [
                        "<p>QUS</p>",
                        "<p>OSP</p>",
                        "<p>SWT</p>",
                        "<p>UYV</p>"
                    ],
                    options_hi: [
                        "<p>QUS</p>",
                        "<p>OSP</p>",
                        "<p>SWT</p>",
                        "<p>UYV</p>"
                    ],
                    solution_en: "<p>4.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744431424195.png\" alt=\"rId9\" width=\"107\" height=\"63\">, <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744431424392.png\" alt=\"rId10\" width=\"108\" height=\"63\">, <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744431424599.png\" alt=\"rId11\" width=\"105\" height=\"62\"><br>But,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744431424878.png\" alt=\"rId12\" width=\"110\" height=\"64\"></p>",
                    solution_hi: "<p>4.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744431424195.png\" alt=\"rId9\" width=\"107\" height=\"63\">, <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744431424392.png\" alt=\"rId10\" width=\"108\" height=\"63\">, <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744431424599.png\" alt=\"rId11\" width=\"105\" height=\"62\"><br>लेकिन,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744431424878.png\" alt=\"rId12\" width=\"110\" height=\"64\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "6",
                    question_en: "<p>5. Select the set in which the numbers are related in the same way as are the numbers of the following sets.<br>(NOTE : Operations should be performed on the whole numbers, without breaking down the numbers into its constituent&nbsp;digits. E.g. 13 &ndash; Operations on 13 such as adding /deleting /multiplying etc. to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed)<br>(108, 27, 9)<br>(132, 33, 11)</p>",
                    question_hi: "<p>5. उस समुच्चय का चयन करें जिसकी संख्याएं उसी तरह से संबंधित हैं जिस तरह निम्नलिखित समुच्चयों की संख्याएं संबंधित हैं।<br>(नोट : संख्याओं को उसके घटक अंकों में तोड़े बिना, पूर्ण संख्याओं पर संक्रियाएं की जानी चाहिए। उदाहरण के लिए संख्या 13 को लें - 13 पर संक्रियाएं, जैसे कि 13 में जोड़ना/घटाना/गुणा करना आदि, की जा सकती हैं। 13 को 1 और 3 में तोड़ने और फिर 1 और 3 पर गणितीय संक्रियाएं करने की अनुमति नहीं है)<br>(108, 27, 9)<br>(132, 33, 11)</p>",
                    options_en: [
                        "<p>(180, 45, 10)</p>",
                        "<p>(180, 55, 15)</p>",
                        "<p>(180, 45, 15)</p>",
                        "<p>(190, 45, 15)</p>"
                    ],
                    options_hi: [
                        "<p>(180, 45, 10)</p>",
                        "<p>(180, 55, 15)</p>",
                        "<p>(180, 45, 15)</p>",
                        "<p>(190, 45, 15)</p>"
                    ],
                    solution_en: "<p>5.(c) <strong>Logic :- </strong>(2nd number + 3rd number) &times; 3 = 1st number<br>(108, 27, 9) :- (27 + 9) &times; 3 &rArr; (36) &times; 3 = 108<br>(132, 33,11) :- (33 + 11) &times; 3 &rArr; (44) &times; 3 = 132<br>Similarly,<br>(180, 45, 15) :- (45 + 15) &times; 3 &rArr; (60) &times; 3 = 180</p>",
                    solution_hi: "<p>5.(c)<strong> तर्क :- </strong>(दूसरी संख्या + तीसरी संख्या) &times; 3 = पहली संख्या<br>(108, 27, 9) :- (27 + 9) &times; 3 &rArr; (36) &times; 3 = 108<br>(132, 33,11) :- (33 + 11)&times; 3 &rArr; (44)&times; 3 = 132<br>इसी प्रकार,<br>(180, 45, 15) :- (45 + 15) &times; 3 &rArr; (60) &times; 3 = 180</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "6",
                    question_en: "<p>6. Identify the figure given in the options which when put in place of ? will logically complete the series.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744431425197.png\" alt=\"rId13\" width=\"419\" height=\"83\"></p>",
                    question_hi: "<p>6. विकल्पों में दी गई उस आकृति की पहचान कीजिए जिसे ? के स्थान पर रखने पर श्रृंखला तर्कसंगत रूप से पूर्ण हो जाएगी।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744431425197.png\" alt=\"rId13\" width=\"419\" height=\"83\"></p>",
                    options_en: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744431425320.png\" alt=\"rId14\" width=\"80\" height=\"78\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744431425502.png\" alt=\"rId15\" width=\"80\" height=\"78\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744431425713.png\" alt=\"rId16\" width=\"80\" height=\"78\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744431425908.png\" alt=\"rId17\" width=\"82\" height=\"80\"></p>"
                    ],
                    options_hi: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744431425320.png\" alt=\"rId14\" width=\"80\" height=\"78\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744431425502.png\" alt=\"rId15\" width=\"81\" height=\"80\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744431425713.png\" alt=\"rId16\" width=\"80\" height=\"78\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744431425908.png\" alt=\"rId17\" width=\"79\" height=\"78\"></p>"
                    ],
                    solution_en: "<p>6.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744431425320.png\" alt=\"rId14\" width=\"79\" height=\"77\"></p>",
                    solution_hi: "<p>6.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744431425320.png\" alt=\"rId14\" width=\"81\" height=\"80\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "6",
                    question_en: "<p>7. Select the correct mirror image of the given figure when the mirror is placed at MN as shown.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744431426033.png\" alt=\"rId18\"></p>",
                    question_hi: "<p>7. जब दर्पण को दर्शाए गए अनुसार MN पर रखा जाता है, तो दी गई आकृति के सही दर्पण प्रतिबिंब का चयन करें।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744431426033.png\" alt=\"rId18\"></p>",
                    options_en: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744431426134.png\" alt=\"rId19\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744431426237.png\" alt=\"rId20\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744431426410.png\" alt=\"rId21\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744431426530.png\" alt=\"rId22\"></p>"
                    ],
                    options_hi: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744431426134.png\" alt=\"rId19\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744431426237.png\" alt=\"rId20\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744431426410.png\" alt=\"rId21\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744431426530.png\" alt=\"rId22\"></p>"
                    ],
                    solution_en: "<p>7.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744431426530.png\" alt=\"rId22\"></p>",
                    solution_hi: "<p>7.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744431426530.png\" alt=\"rId22\"></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "6",
                    question_en: "<p>8. Three of the following four are alike in a certain way and thus form a group. Which is the one that does NOT belong to that group? <br>(Note : The odd one out is not based on the number of consonants/vowels or their position in the letter cluster.)</p>",
                    question_hi: "<p>8. निम्नलिखित चार अक्षर-समूहों में से तीन अक्षर-समूह एक निश्चित प्रकार से समान हैं और इस प्रकार एक समूह बनाते हैं। वह कौन-सा अक्षर-समूह है जो उस समूह से संबंधित नहीं है? <br>(ध्यान दें : असंगत अक्षर-समूह, उस अक्षर-समूह में व्यंजनों/स्वरों की संख्या या उनके स्थान पर आधारित नहीं है।)</p>",
                    options_en: [
                        "<p>JNP</p>",
                        "<p>HKM</p>",
                        "<p>CGI</p>",
                        "<p>RVX</p>"
                    ],
                    options_hi: [
                        "<p>JNP</p>",
                        "<p>HKM</p>",
                        "<p>CGI</p>",
                        "<p>RVX</p>"
                    ],
                    solution_en: "<p>8.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744431426635.png\" alt=\"rId23\" width=\"137\" height=\"89\">,<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744431426735.png\" alt=\"rId24\" width=\"134\" height=\"93\">,<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744431426838.png\" alt=\"rId25\" width=\"134\" height=\"94\"><br>But,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744431427038.png\" alt=\"rId26\" width=\"107\" height=\"61\"></p>",
                    solution_hi: "<p>8.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744431426635.png\" alt=\"rId23\" width=\"137\" height=\"89\">,<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744431426735.png\" alt=\"rId24\" width=\"134\" height=\"93\">,<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744431426838.png\" alt=\"rId25\" width=\"134\" height=\"94\"><br>लेकिन,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744431427038.png\" alt=\"rId26\" width=\"111\" height=\"63\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "6",
                    question_en: "<p>9. Select the letter-cluster from among the given options that can replace the question mark (?) in the following series. <br>AGMZ, CEOX, ECQV, ?</p>",
                    question_hi: "<p>9. दिए गए विकल्पों में से उस अक्षर-समूह को चुनें जो निम्नलिखित श्रृंखला में प्रश्नवाचक चिह्न (?) के स्थान पर आएगा।<br>AGMZ, CEOX, ECQV, ?</p>",
                    options_en: [
                        "<p>GAST</p>",
                        "<p>GBTS</p>",
                        "<p>GCSW</p>",
                        "<p>GDSX</p>"
                    ],
                    options_hi: [
                        "<p>GAST</p>",
                        "<p>GBTS</p>",
                        "<p>GCSW</p>",
                        "<p>GDSX</p>"
                    ],
                    solution_en: "<p>9.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744431427593.png\" alt=\"rId27\" width=\"307\" height=\"96\"></p>",
                    solution_hi: "<p>9.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744431427593.png\" alt=\"rId27\" width=\"307\" height=\"96\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "6",
                    question_en: "<p>10. Select the option that is related to the fifth letter-cluster in the same way as the second letter-cluster is related to the first letter-cluster and the fourth letter-cluster is related to the third letter-cluster.<br>JOCK : GLZH :: HURT : EROQ :: SPIN : ?</p>",
                    question_hi: "<p>10. उस विकल्प का चयन करें जो पांचवें अक्षर-समूह से उसी प्रकार संबंधित है जिस प्रकार दूसरा अक्षर-समूह पहले अक्षर-समूह से संबंधित है और चौथा अक्षर-समूह तीसरे अक्षर-समूह से संबंधित है।<br>JOCK : GLZH :: HURT : EROQ :: SPIN : ?</p>",
                    options_en: [
                        "<p>PMFK</p>",
                        "<p>QMEK</p>",
                        "<p>QNFK</p>",
                        "<p>PNEL</p>"
                    ],
                    options_hi: [
                        "<p>PMFK</p>",
                        "<p>QMEK</p>",
                        "<p>QNFK</p>",
                        "<p>PNEL</p>"
                    ],
                    solution_en: "<p>10.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744431427838.png\" alt=\"rId28\" width=\"168\" height=\"118\">,<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744431427962.png\" alt=\"rId29\" width=\"177\" height=\"119\"><br>Similarly,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744431428083.png\" alt=\"rId30\" width=\"178\" height=\"121\"></p>",
                    solution_hi: "<p>10.(a) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744431427838.png\" alt=\"rId28\" width=\"168\" height=\"118\">,<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744431427962.png\" alt=\"rId29\" width=\"177\" height=\"119\"><br>इसी प्रकार,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744431428083.png\" alt=\"rId30\" width=\"178\" height=\"121\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "6",
                    question_en: "<p>11. In a certain code language, \'MENIAL\' is coded as \'NIEAML\' and \'INCOME\' is coded as \'CONMIE\'. What is the code for \'FOREGO\' in the given code language?</p>",
                    question_hi: "<p>11. एक निश्चित कूट भाषा में, MENIAL\' को \'NIEAML\' लिखा जाता है और INCOME\' को \'CONMIE\' लिखा जाता है। उस कूट भाषा में FOREGO\' को कैसे लिखा जाएगा?</p>",
                    options_en: [
                        "<p>REOGFO</p>",
                        "<p>OFOREG</p>",
                        "<p>OGEROF</p>",
                        "<p>FOGERO</p>"
                    ],
                    options_hi: [
                        "<p>REOGFO</p>",
                        "<p>OFOREG</p>",
                        "<p>OGEROF</p>",
                        "<p>FOGERO</p>"
                    ],
                    solution_en: "<p>11.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744431428782.png\" alt=\"rId31\" width=\"195\" height=\"81\">, <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744431429097.png\" alt=\"rId32\" width=\"193\" height=\"80\"><br>Similarly,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744431429412.png\" alt=\"rId33\" width=\"211\" height=\"87\"></p>",
                    solution_hi: "<p>11.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744431428782.png\" alt=\"rId31\" width=\"195\" height=\"81\">, <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744431429097.png\" alt=\"rId32\" width=\"193\" height=\"80\"><br>इसी प्रकार,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744431429412.png\" alt=\"rId33\" width=\"211\" height=\"87\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "6",
                    question_en: "<p>12. Which two numbers (not digits) should be interchanged to make the given equation correct?<br>116 - 92 &divide; 4 + 25 &times; 2 - 20 = 150</p>",
                    question_hi: "<p>12. दिए गए समीकरण को सही करने के लिए किन दो संख्याओं (अंक नहीं) को आपस में बदलना चाहिए?<br>116 - 92 &divide; 4 + 25 &times; 2 - 20 = 150</p>",
                    options_en: [
                        "<p>116 and 92</p>",
                        "<p>25 and 20</p>",
                        "<p>2 and 4</p>",
                        "<p>20 and 92</p>"
                    ],
                    options_hi: [
                        "<p>116 और 92</p>",
                        "<p>25 और 20</p>",
                        "<p>2 और 4</p>",
                        "<p>20 और 92</p>"
                    ],
                    solution_en: "<p>12.(c)<br><strong>Given:</strong> 116 - 92 &divide; 4 + 25&nbsp;&times; 2 - 20 = 150<br>As per the given instruction after interchanging the number 2 and 4, we get.<br>116 - 92 &divide; 2 + 25&nbsp;&times; 4 - 20 = 150<br>116 - 46 + 100 - 20 = 150<br>216 - 66 = 150</p>",
                    solution_hi: "<p>12.(c)<br><strong>दिया गया है: </strong>116 - 92 &divide; 4 + 25&nbsp;&times; 2 - 20 = 150<br>दिए गए निर्देश के अनुसार संख्या 2 और 4 को आपस में बदलने पर हमें प्राप्त होता है।<br>116 - 92 &divide; 2 + 25 &times; 4 - 20 = 150<br>116 - 46 + 100 - 20 = 150<br>216 - 66 = 150</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "6",
                    question_en: "<p>13. In a certain code language,<br>&lsquo;A + B&rsquo; means &lsquo;A is the mother of B&rsquo;, <br>&lsquo;A &ndash; B&rsquo; means &lsquo;A is the father of B&rsquo;, <br>&lsquo;A $ B&rsquo; means &lsquo;A is the son of B&rsquo;, <br>&lsquo;A % B&rsquo; means &lsquo;A is the sister of B&rsquo;, <br>&lsquo;A / B&rsquo; means &lsquo;A is the wife of B&rsquo; <br>&lsquo;A = B&rsquo; means &lsquo;A is the brother of B&rsquo;. <br>Which of the following means A is the daughter of K?<br>i. A + K / J &ndash; P = D <br>ii. J % P = K + A = D <br>iii. K + A / P &ndash; D % J <br>iv. D + K = A / P &ndash; J</p>",
                    question_hi: "<p>13. एक निश्चित कूट भाषा में, <br>&lsquo;A + B&rsquo; का अर्थ है &lsquo;A, B की माँ है&rsquo;, <br>&lsquo;A &ndash; B&rsquo; का अर्थ है &lsquo;A, B का पिता है&rsquo;, <br>&lsquo;A $ B&rsquo; का अर्थ है &lsquo;A, B का पुत्र है&rsquo;, <br>&lsquo;A % B&rsquo; का अर्थ है &lsquo;A, B की बहन है&rsquo;, <br>&lsquo;A / B&rsquo; का अर्थ है &lsquo;A, B की पत्नी है&rsquo; <br>&lsquo;A = B&rsquo; का अर्थ है &lsquo;A, B का भाई है&rsquo;।<br>निम्नलिखित में से किसका अर्थ है कि A, K की पुत्री है? <br>i. A + K / J &ndash; P = D <br>ii. J % P = K + A = D <br>iii. K + A / P &ndash; D % J <br>iv. D + K = A / P &ndash; J</p>",
                    options_en: [
                        "<p>ii</p>",
                        "<p>i</p>",
                        "<p>iii</p>",
                        "<p>iv</p>"
                    ],
                    options_hi: [
                        "<p>ii</p>",
                        "<p>i</p>",
                        "<p>iii</p>",
                        "<p>iv</p>"
                    ],
                    solution_en: "<p>13.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744431429605.png\" alt=\"rId34\" width=\"140\" height=\"123\"><br>A is the daughter of K.</p>",
                    solution_hi: "<p>13.(c)<br><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744431429605.png\" alt=\"rId34\" width=\"140\" height=\"123\"><br>A, K की बेटी है.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "6",
                    question_en: "<p>14. 12 is related to 40 following a certain logic. Following the same logic, 9 is related to 31. To which of the following is 16 related using the same logic?</p>",
                    question_hi: "<p>14. एक निश्चित तर्क का अनुसरण करते हुए 12, 40 से संबंधित है। उसी तर्क का अनुसरण करते हुए, 9, 31 से संबंधित है। उसी तर्क का उपयोग करते हुए, 16 निम्न में से किससे संबंधित है?</p>",
                    options_en: [
                        "<p>51</p>",
                        "<p>52</p>",
                        "<p>63</p>",
                        "<p>61</p>"
                    ],
                    options_hi: [
                        "<p>51</p>",
                        "<p>52</p>",
                        "<p>63</p>",
                        "<p>61</p>"
                    ],
                    solution_en: "<p>14.(b) <strong>Logic :- </strong>(1st number &times; 3) + 4 = (2nd number) <br>(12, 40) :- (12 &times; 3)+4 &rArr; 36 + 4 = 40<br>(9, 31) :- (9 &times; 3) + 4 &rArr; 27 + 4 = 31<br>Similarly,<br>(16, ?) :- (16 &times; 3) + 4 &rArr; 48 + 4 = 52</p>",
                    solution_hi: "<p>14.(b) <strong>तर्क :- </strong>(पहली संख्या &times; 3) + 4 = (दूसरी संख्या)<br>(12, 40) :- (12 &times; 3)+4 &rArr; 36 + 4 = 40<br>(9, 31) :- (9 &times; 3) + 4 &rArr; 27 + 4 = 31<br>इसी प्रकार,<br>(16, ?) :- (16 &times; 3) + 4 &rArr; 48 + 4 = 52</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "6",
                    question_en: "<p>15. How many triangles are there in the following figure?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744431429787.png\" alt=\"rId35\" width=\"111\" height=\"101\"></p>",
                    question_hi: "<p>15. नीचे दी गई आकृति में कितने त्रिभुज हैं?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744431429787.png\" alt=\"rId35\" width=\"111\" height=\"101\"></p>",
                    options_en: [
                        "<p>16</p>",
                        "<p>17</p>",
                        "<p>14</p>",
                        "<p>15</p>"
                    ],
                    options_hi: [
                        "<p>16</p>",
                        "<p>17</p>",
                        "<p>14</p>",
                        "<p>15</p>"
                    ],
                    solution_en: "<p>15.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744431429910.png\" alt=\"rId36\" width=\"154\" height=\"152\"><br>There are 16 triangle<br>ABJ, AJH, BJC, CDJ, JDE, JFE, JGF, JGH, CGE, AGE, ACE, AGC, CJE, GJE, AJG, AJC</p>",
                    solution_hi: "<p>15.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744431429910.png\" alt=\"rId36\" width=\"154\" height=\"152\"><br>16 त्रिभुज हैं<br>ABJ, AJH, BJC, CDJ, JDE, JFE, JGF, JGH, CGE, AGE, ACE, AGC, CJE, GJE, AJG, AJC</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "6",
                    question_en: "<p>16. In a certain code language, \'MONEY\' is written as \'64381\' and \'HONEY\' is written as \'54381\'. How will \'H\' be written in that code language?</p>",
                    question_hi: "<p>16. एक निश्चित कूट भाषा में, \'MONEY\' को \'64381\' लिखा जाता है और \'HONEY\' को \'54381\' लिखा जाता है। उस कूट भाषा में \'H\' को कैसे लिखा जाएगा?</p>",
                    options_en: [
                        "<p>4</p>",
                        "<p>6</p>",
                        "<p>8</p>",
                        "<p>5</p>"
                    ],
                    options_hi: [
                        "<p>4</p>",
                        "<p>6</p>",
                        "<p>8</p>",
                        "<p>5</p>"
                    ],
                    solution_en: "<p>16.(d) MONEY &rarr; 64381&hellip;&hellip;&hellip; (i)<br>HONEY &rarr; 54381&hellip;&hellip;&hellip;(ii)<br>From (i) and (ii) &lsquo;ONEY&rsquo; and &lsquo;4381&rsquo; are common. The code of &lsquo;H&rsquo; = &lsquo;5&rsquo;.</p>",
                    solution_hi: "<p>16.(d) MONEY &rarr; 64381&hellip;&hellip;&hellip; (i)<br>HONEY &rarr; 54381&hellip;&hellip;&hellip;(ii)<br>(i) और (ii) से \'ONEY\' और \'4381\' उभयनिष्ठ हैं। \'H\' का कोड = \'5\'.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "6",
                    question_en: "<p>17. Identify the option figure that when put in place of the question mark (?) will logically complete the series. <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744431430101.png\" alt=\"rId37\" width=\"314\" height=\"75\"></p>",
                    question_hi: "<p>17. उस विकल्प आकृति की पहचान कीजिए जिसे प्रश्न चिह्न (?) के स्थान पर रखने पर शृंखला तार्किक रूप से पूरी हो जाएगी।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744431430101.png\" alt=\"rId37\" width=\"314\" height=\"75\"></p>",
                    options_en: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744431430204.png\" alt=\"rId38\" width=\"81\" height=\"78\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744431430333.png\" alt=\"rId39\" width=\"82\" height=\"75\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744431430507.png\" alt=\"rId40\" width=\"83\" height=\"81\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744431430614.png\" alt=\"rId41\" width=\"80\" height=\"79\"></p>"
                    ],
                    options_hi: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744431430204.png\" alt=\"rId38\" width=\"80\" height=\"77\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744431430333.png\" alt=\"rId39\" width=\"81\" height=\"74\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744431430507.png\" alt=\"rId40\" width=\"81\" height=\"79\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744431430614.png\" alt=\"rId41\" width=\"80\" height=\"79\"></p>"
                    ],
                    solution_en: "<p>17.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744431430204.png\" alt=\"rId38\" width=\"80\" height=\"77\"></p>",
                    solution_hi: "<p>17.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744431430204.png\" alt=\"rId38\" width=\"80\" height=\"77\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "6",
                    question_en: "<p>18. Select the term from among the given options that can replace the question mark (?) in the following series.<br>PJ 16, SM 25, VP 36, YS 49, ?</p>",
                    question_hi: "<p>18. दिए गए विकल्पों में से उस पद का चयन कीजिए, जो निम्नलिखित शृंखला में प्रश्न-चिह्न (?) को प्रतिस्थापित कर सकता है।<br>PJ 16, SM 25, VP 36, YS 49, ?</p>",
                    options_en: [
                        "<p>CW 81</p>",
                        "<p>BV 64</p>",
                        "<p>DW 64</p>",
                        "<p>CV 36</p>"
                    ],
                    options_hi: [
                        "<p>CW 81</p>",
                        "<p>BV 64</p>",
                        "<p>DW 64</p>",
                        "<p>CV 36</p>"
                    ],
                    solution_en: "<p>18.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744431430720.png\" alt=\"rId42\" width=\"391\" height=\"134\"></p>",
                    solution_hi: "<p>18.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744431430720.png\" alt=\"rId42\" width=\"382\" height=\"131\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "6",
                    question_en: "<p>19. Which two signs should be interchanged to make the following equation correct?<br>247 &divide; 13 + 16 &times; 3 &minus; 148 = 119</p>",
                    question_hi: "<p>19. निम्नलिखित समीकरण को सही करने के लिए किन दो चिह्नों को आपस में बदलना चाहिए ?<br>247 &divide; 13 + 16 &times; 3 &minus; 148 = 119</p>",
                    options_en: [
                        "<p>+ and &times;</p>",
                        "<p>&minus; and +</p>",
                        "<p>&minus; and &times;</p>",
                        "<p>&divide; and &times;</p>"
                    ],
                    options_hi: [
                        "<p>+ और &times;</p>",
                        "<p>&minus; और +</p>",
                        "<p>&minus; और &times;</p>",
                        "<p>&divide; और &times;</p>"
                    ],
                    solution_en: "<p>19.(b) <strong>Given:-</strong> 247 <math display=\"inline\"><mo>&#247;</mo></math> 13 + 16 &times; 3 - 148 = 119<br>After going through all the options, option b satisfied. After interchanging &lsquo;+&rsquo; and &lsquo;-&rsquo;, we get<br>&rArr; 247 &divide; 13 - 16 &times; 3 + 148 = 119<br>&rArr; 19 - 48 + 148 = 119<br>&rArr; 19 + 100 = 119</p>",
                    solution_hi: "<p>19.(b) <strong>दिया गया:-</strong> 247 <math display=\"inline\"><mo>&#247;</mo></math> 13 + 16 &times; 3 - 148 = 119<br>सभी विकल्पों की जांच करने पर विकल्प b संतुष्ट करता है। \'+\' और \'-\' को आपस में बदलने के बाद, हमें मिलता है<br>&rArr; 247 &divide; 13 - 16 &times; 3 + 148 = 119<br>&rArr; 19 - 48 + 148 = 119<br>&rArr; 19 + 100 = 119</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "6",
                    question_en: "<p>20. All the alphabets in the word \'CARDBOARD\' are arranged in the alphabetical order. How many alphabets are there in the English alphabetical series between the alphabet which is fourth from the left end and the one which is second from the right end in the newly formed cluster of nine letters?</p>",
                    question_hi: "<p>20. \'CARDBOARD\' शब्द के सभी अक्षरों को वर्णानुक्रम में व्यवस्थित किया जाता है। नौ अक्षरों के नवसंरचित समूह में बाएं से चौथे और दाएं से दूसरे अक्षर के बीच अंग्रेजी वर्णमाला श्रृंखला में कितने अक्षर हैं?</p>",
                    options_en: [
                        "<p>12</p>",
                        "<p>16</p>",
                        "<p>14</p>",
                        "<p>10</p>"
                    ],
                    options_hi: [
                        "<p>12</p>",
                        "<p>16</p>",
                        "<p>14</p>",
                        "<p>10</p>"
                    ],
                    solution_en: "<p>20.(c)<br><strong>Given : -</strong> C A R D B O A R D <br>After arranging alphabetically :- A A B C D D O R R<br>Fourth alphabet from left end = C<br>Second alphabet from right end = R<br>No. of alphabet between C and R in alphabetical order = 14</p>",
                    solution_hi: "<p>20.(c)<br><strong>दिया गया है : -</strong> C A R D B O A R D <br>वर्णानुक्रम में व्यवस्थित करने के बाद :- A A B C D D O R R<br>बाएं छोर से चौथा अक्षर = C<br>दाएं छोर से दूसरा अक्षर = R<br>वर्णमाला क्रम में C और R के बीच अक्षरों की संख्या = 14</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "6",
                    question_en: "21. In a certain language, \'make someone happy\' is written as \'Ab Te Dp\' and \'someone robbed him\' is written as \"Te Ko Vi. How is \'someone\' written in the given language ?",
                    question_hi: "21. एक निश्चित कूट भाषा में, \'make someone happy\' को ‘Ab Te Dp\' लिखा जाता है और ‘someone robbed him\' को ‘Te Ko Vi’ लिखा जाता है। उस कूट भाषा में \'someone\' को कैसे लिखा जाएगा ?",
                    options_en: [
                        " Ko",
                        " Ab ",
                        " Te ",
                        " Vi"
                    ],
                    options_hi: [
                        " Ko",
                        " Ab ",
                        " Te ",
                        " Vi"
                    ],
                    solution_en: "21.(c) make someone happy → Ab Te Dp…….. (i)<br />                 someone robbed him → Te Ko Vi……… (ii)<br />From (i) and (ii) ‘someone’ and ‘Te’ are common. The code of ‘someone’ = ‘Te.’",
                    solution_hi: "21.(c) make someone happy → Ab Te Dp…….. (i)<br />                 someone robbed him → Te Ko Vi……… (ii)<br />(i) और (ii) से ‘someone\' और \'Te\' उभयनिष्ठ हैं। \'someone\' का कूट = \'Te.\'",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "6",
                    question_en: "<p>22. Select the correct mirror image of the given figure when the mirror is placed at MN as shown.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744431430927.png\" alt=\"rId43\" width=\"132\" height=\"172\"></p>",
                    question_hi: "<p>22. जब दर्पण को दर्शाए गए अनुसार MN पर रखा जाता है, तो दी गई आकृति के सही दर्पण प्रतिबिंब का चयन करें।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744431430927.png\" alt=\"rId43\" width=\"147\" height=\"192\"></p>",
                    options_en: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744431431129.png\" alt=\"rId44\" width=\"89\" height=\"25\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744431431338.png\" alt=\"rId45\" width=\"93\" height=\"26\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744431431464.png\" alt=\"rId46\" width=\"89\" height=\"22\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744431431655.png\" alt=\"rId47\" width=\"90\" height=\"22\"></p>"
                    ],
                    options_hi: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744431431129.png\" alt=\"rId44\" width=\"89\" height=\"25\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744431431338.png\" alt=\"rId45\" width=\"90\" height=\"25\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744431431464.png\" alt=\"rId46\" width=\"89\" height=\"22\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744431431655.png\" alt=\"rId47\" width=\"90\" height=\"22\"></p>"
                    ],
                    solution_en: "<p>22.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744431431338.png\" alt=\"rId45\" width=\"90\" height=\"25\"></p>",
                    solution_hi: "<p>22.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744431431338.png\" alt=\"rId45\" width=\"90\" height=\"25\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "23",
                    section: "6",
                    question_en: "<p>23. Six people&mdash;A, B, C, D, E, and F&mdash;are sitting around a circular table, facing the centre (but not necessarily in the same order). A is an immediate neighbour of both E and F. C is second to the left of F. B is not is an immediate neighbour of E. What is the position of D?</p>",
                    question_hi: "<p>23. छः व्यक्ति - A, B, C, D, E और F- एक वृत्ताकार मेज के चारों ओर केंद्र की ओर मुख करके बैठे हैं (परंतु जरूरी नहीं कि वे इसी क्रम में बैठे हों)। E और F दोनों का निकटतम पड़ोसी A है। C, F के बाएँ से दूसरे स्थान पर है। B, E का निकटतम पड़ोसी नहीं है। D का स्थान कौन-सा है?</p>",
                    options_en: [
                        "<p>Immediate left of A</p>",
                        "<p>Immediate left of C</p>",
                        "<p>Second to the right of B</p>",
                        "<p>Immediate left of F</p>"
                    ],
                    options_hi: [
                        "<p>A के ठीक बाएँ</p>",
                        "<p>C के ठीक बाएँ</p>",
                        "<p>B के दाएँ से दूसरा</p>",
                        "<p>F के ठीक बाएँ</p>"
                    ],
                    solution_en: "<p>23.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744431431771.png\" alt=\"rId48\" width=\"161\" height=\"110\"><br>D is sitting immediate to the left of C.</p>",
                    solution_hi: "<p>23.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744431431771.png\" alt=\"rId48\" width=\"161\" height=\"110\"><br>D, C के ठीक बाईं ओर बैठा है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "24",
                    section: "6",
                    question_en: "<p>24. G is the brother of F. E is the father of K. C is the daughter of A. F is the mother of E. C is the sister of E. How is G related to A?</p>",
                    question_hi: "<p>24. G, F का भाई है। E, K का पिता है। C, A की पुत्री है। F, E की माता है। C, E की बहन है। G, A से किस प्रकार संबंधित है?</p>",
                    options_en: [
                        "<p>Wife&rsquo;s brother</p>",
                        "<p>Sister&rsquo;s husband</p>",
                        "<p>Husband&rsquo;s sister</p>",
                        "<p>Brother&rsquo;s wife</p>"
                    ],
                    options_hi: [
                        "<p>साला</p>",
                        "<p>जीजा</p>",
                        "<p>ननद</p>",
                        "<p>भाभी</p>"
                    ],
                    solution_en: "<p>24.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744431431873.png\" alt=\"rId49\" width=\"162\" height=\"141\"><br>&lsquo;G&rsquo; is the brother of A&rsquo;s wife.</p>",
                    solution_hi: "<p>24.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744431431873.png\" alt=\"rId49\" width=\"162\" height=\"141\"><br>\'G\', \'A\', का साला हैI</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "25",
                    section: "18",
                    question_en: "<p>25. Three statements are given, followed by three conclusions numbered I, II and III. Assuming the statements to be true, even if they seem to be at variance with commonly known facts, decide which of the conclusions logically follow(s) from the statements.<br><strong>Statements :</strong><br>Some rats are mouse.<br>No mouse is a rodent.<br>Some rats are kittens.<br><strong>Conclusions :</strong><br>I. No rodent is a kitten.<br>II. Some rats are rodent.<br>III. No kitten is a mouse.</p>",
                    question_hi: "<p>25. तीन कथन दिए गए हैं, जिसके बाद तीन निष्कर्ष I, II और III दिए गए हैं। कथनों को सत्य मानते हुए, भले ही वे सामान्य रूप से ज्ञात तथ्यों से भिन्न प्रतीत होते हों, निर्धारित करें कि कौन सा/से निष्कर्ष कथनों का तार्किक रूप से अनुसरण करता/करते है/हैं। <br><strong>कथन :</strong> <br>कुछ चूहे, मूषक हैं। <br>कोई मूषक, कृंतक नहीं है। <br>कुछ चूहे, बिल्ली-शावक (Kittens) है। <br><strong>निष्&zwj;कर्ष :</strong> <br>I. कोई कृंतक, बिल्ली-शावक (Kittens) नहीं है। <br>II. कुछ चूहे, कृंतक हैं। <br>III. कोई बिल्ली-शावक (Kittens), चूहा नहीं है।</p>",
                    options_en: [
                        " Only conclusion III follows ",
                        " Only conclusion II follows  ",
                        " Neither conclusion follows  ",
                        " Only conclusion I follows"
                    ],
                    options_hi: [
                        " केवल निष्कर्ष III अनुसरण करता है",
                        " केवल निष्कर्ष II अनुसरण करता है  ",
                        " कोई भी निष्कर्ष अनुसरण नहीं करता है ",
                        " केवल निष्कर्ष I अनुसरण करता है"
                    ],
                    solution_en: "<p>25.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744431432281.png\" alt=\"rId50\" width=\"421\" height=\"66\"><br>Neither conclusion follows</p>",
                    solution_hi: "<p>25.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744431432582.png\" alt=\"rId51\" width=\"436\" height=\"66\"><br>कोई भी निष्कर्ष अनुसरण नहीं करता है</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "26",
                    section: "18",
                    question_en: "<p>26. During a National Emergency, the term of Lok Sabha can be extended for:</p>",
                    question_hi: "<p>26. राष्ट्रीय आपातकाल के दौरान, लोकसभा का कार्यकाल _______ तक के लिए बढ़ाया जा सकता है।</p>",
                    options_en: [
                        "<p>three months at a time</p>",
                        "<p>six months at a time</p>",
                        "<p>one year at a time</p>",
                        "<p>two years at a time</p>"
                    ],
                    options_hi: [
                        "<p>एक बार में तीन माह</p>",
                        "<p>एक बार में छ: माह</p>",
                        "<p>एक बार में एक वर्ष</p>",
                        "<p>एक बार में दो वर्ष</p>"
                    ],
                    solution_en: "<p>26.(c) <strong>One year at a time</strong>. During a National Emergency in India, the term of the Lok Sabha can be extended by the Parliament. According to Article 83 of the Indian Constitution, the normal term of the Lok Sabha is five years. However, under Article 352, if a National Emergency is declared, the term of the Lok Sabha can be extended beyond five years. The extension can only be done for one year at a time and cannot extend beyond six months after the emergency has ceased to operate.</p>",
                    solution_hi: "<p>26.(c) <strong>एक बार में एक वर्ष।</strong> भारत में राष्ट्रीय आपातकाल के दौरान, संसद द्वारा लोकसभा का कार्यकाल बढ़ाया जा सकता है। भारतीय संविधान के अनुच्छेद 83 के अनुसार, लोकसभा का सामान्य कार्यकाल पाँच वर्ष का होता है। अनुच्छेद 352 के तहत, यदि राष्ट्रीय आपातकाल घोषित किया जाता है, तो लोकसभा का कार्यकाल पाँच वर्ष से अधिक बढ़ाया जा सकता है। इसका विस्तार एक बार में केवल एक वर्ष के लिए किया जा सकता है और आपातकाल समाप्त होने के बाद इसे छह महीने से अधिक नहीं बढ़ाया जा सकता है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "27",
                    section: "18",
                    question_en: "<p>27. In which of the following sports/games is the term \'bull\'s eye\' used?</p>",
                    question_hi: "<p>27. बुल्स आई (bull\'s eye)\' शब्द का उपयोग निम्नलिखित में से किस खेल में होता है?</p>",
                    options_en: [
                        "<p>Chess</p>",
                        "<p>Kho-kho</p>",
                        "<p>Shooting</p>",
                        "<p>Kabaddi</p>"
                    ],
                    options_hi: [
                        "<p>शतरंज</p>",
                        "<p>खो-खो</p>",
                        "<p>निशानेबाजी</p>",
                        "<p>कबड्डी</p>"
                    ],
                    solution_en: "<p>27.(c) <strong>Shooting.</strong> In shooting sports, particularly archery, darts, and target shooting, the \"bull\'s eye\" refers to the center of the target, which scores the highest points. Chess is a strategy board game, and terms like \"checkmate\" and \"castling\" are used. Kho-kho is a tag-team sport, and terms like \"chase\" and \"catch\" are used. Kabaddi is a contact team sport, and terms like \"raid\" and \"tackle\" are used.</p>",
                    solution_hi: "<p>27.(c) <strong>निशानेबाजी।</strong> निशानेबाजी खेलों में, विशेष रूप से तीरंदाजी, डार्ट और टारगेट शूटिंग में, \"बुल्स आई\" लक्ष्य के केंद्र को संदर्भित करता है, जो अधिकतम अंक प्राप्त करता है। शतरंज एक रणनीतिक बोर्ड गेम है, तथा इसमें \"चेकमेट\" और \"कैसलिंग\" जैसे शब्दों का उपयोग किया जाता है। खो-खो एक टैग-टीम खेल है, तथा इसमें \"चेज़\" और \"कैच\" जैसे शब्दों का उपयोग किया जाता है। कबड्डी एक संपर्क टीम खेल है, तथा इसमें \"रेड\" और \"टैकल\" जैसे शब्दों का उपयोग किया जाता है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "28",
                    section: "18",
                    question_en: "<p>28. Who among the following personalities renounced his knighthood after the Jallianwala Bagh massacre?</p>",
                    question_hi: "<p>28. निम्नलिखित में से किस व्यक्ति ने जलियांवाला बाग हत्याकांड के बाद अपनी नाइटहुड की उपाधि को त्याग दिया था?</p>",
                    options_en: [
                        "<p>Syed Ahmed Khan</p>",
                        "<p>CV Raman</p>",
                        "<p>Rabindranath Tagore</p>",
                        "<p>JC Bose</p>"
                    ],
                    options_hi: [
                        "<p>सैय्यद अहमद खान</p>",
                        "<p>सी वी रमन</p>",
                        "<p>रवीन्द्रनाथ टैगोर</p>",
                        "<p>जे सी बोस</p>"
                    ],
                    solution_en: "<p>28.(c) <strong>Rabindranath Tagore.</strong> The Jallianwala Bagh massacre occurred on April 13, 1919, when British troops, led by General Dyer, fired on a peaceful crowd in Amritsar, killing hundreds. In protest, Mahatma Gandhi returned the Kaiser-i-Hind title in 1920. The Hunter Commission was set up in 1919 to investigate the incident.</p>",
                    solution_hi: "<p>28.(c) <strong>रवीन्द्रनाथ टैगोर।</strong> जलियाँवाला बाग हत्याकांड 13 अप्रैल, 1919 को हुआ था, जब जनरल डायर के नेतृत्व में ब्रिटिश सैनिकों ने अमृतसर में शांतिपूर्ण भीड़ पर गोलीबारी की थी, जिसमें सैकड़ों व्यक्ति मारे गए थे। इसके विरोध में, महात्मा गांधी ने 1920 में कैसर-ए-हिंद की उपाधि वापस कर दी थी। इस घटना की जाँच के लिए 1919 में हंटर आयोग का गठन किया गया था।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "29",
                    section: "18",
                    question_en: "<p>29 Where did the West Bengal Film Journalists Association (WBFJA) host its annual awards on January 13, 2025 ?</p>",
                    question_hi: "<p>29. पश्चिम बंगाल फिल्म पत्रकार संघ (WBFJA) ने 13 जनवरी, 2025 को अपने वार्षिक पुरस्कारों का आयोजन कहाँ किया?</p>",
                    options_en: [
                        "<p>Nandan Cinema, Kolkata</p>",
                        "<p>Priya Cinema, Kolkata</p>",
                        "<p>PVR Cinemas, Kolkata</p>",
                        "<p>Metro Cinema, Kolkata</p>"
                    ],
                    options_hi: [
                        "<p>नंदन सिनेमा, कोलकाता</p>",
                        "<p>प्रिया सिनेमा, कोलकाता</p>",
                        "<p>PVR सिनेमा, कोलकाता</p>",
                        "<p>मेट्रो सिनेमा, कोलकाता</p>"
                    ],
                    solution_en: "<p>29.(b) <strong>Priya Cinema, Kolkata.</strong> Renowned filmmaker Aparna Sen received the &lsquo;Satyajit Ray Lifetime Achievement Award. The event celebrated Bengali cinema with awards in categories like Best Film, Best Actor, and Best Director, and paid tribute to Raj Kapoor on his birth centenary with a thematic tribute to the circus industry.</p>",
                    solution_hi: "<p>29.(b) <strong>प्रिया सिनेमा, कोलकाता।</strong> प्रसिद्ध फिल्म निर्माता अपर्णा सेन को &lsquo;सत्यजीत रे लाइफटाइम अचीवमेंट अवार्ड&rsquo; मिला। इस कार्यक्रम में बंगाली सिनेमा को सर्वश्रेष्ठ फिल्म, सर्वश्रेष्ठ अभिनेता और सर्वश्रेष्ठ निर्देशक जैसी श्रेणियों में पुरस्कार दिए गए और सर्कस उद्योग को एक विषयगत श्रद्धांजलि के साथ राज कपूर को उनकी जन्म शताब्दी पर श्रद्धांजलि दी गई।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "30",
                    section: "18",
                    question_en: "<p>30. The Quit India Resolution was ratified in the__________ session of Indian National Congress to launch the movement.</p>",
                    question_hi: "<p>30. आंदोलन शुरू करने के लिए भारतीय राष्ट्रीय कांग्रेस के _________ सत्र में भारत छोड़ो संकल्प (Quit India Resolution) अनुमोदित किया गया था।</p>",
                    options_en: [
                        "<p>Bombay</p>",
                        "<p>Bankipur</p>",
                        "<p>Lucknow</p>",
                        "<p>Nagpur</p>"
                    ],
                    options_hi: [
                        "<p>बॉम्&zwj;बे</p>",
                        "<p>बांकीपुर</p>",
                        "<p>लखनऊ</p>",
                        "<p>नागपुर</p>"
                    ],
                    solution_en: "<p>30.(a) <strong>Bombay session.</strong> This session took place at the Gowalia Tank Maidan (August Kranti Maidan), on August 8, 1942, where Mahatma Gandhi gave his famous \"Do or Die\" speech. The resolution was moved by Jawaharlal Nehru.</p>",
                    solution_hi: "<p>30.(a) <strong>बॉम्बे अधिवेशन।</strong> यह अधिवेशन 8 अगस्त, 1942 को गोवालिया टैंक मैदान (अगस्त क्रांति मैदान) में हुआ था, जहाँ महात्मा गांधी ने अपना प्रसिद्ध नारा \"करो या मरो\" दिया था। यह प्रस्ताव जवाहरलाल नेहरू द्वारा पेश किया गया था।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "31",
                    section: "18",
                    question_en: "<p>31. The Asian Games is also known as___________.</p>",
                    question_hi: "<p>31. एशियाई खेलों को _________ के रूप में भी जाना जाता है।</p>",
                    options_en: [
                        "<p>Asiad</p>",
                        "<p>World Cup</p>",
                        "<p>Commonwealth Games</p>",
                        "<p>National Games</p>"
                    ],
                    options_hi: [
                        "<p>एशियाड</p>",
                        "<p>विश्व कप</p>",
                        "<p>राष्ट्रमंडल खेल</p>",
                        "<p>राष्ट्रीय खेल</p>"
                    ],
                    solution_en: "<p>31.(a) <strong>Asiad.</strong> The Asian Games (Asiad) is a multi-sport event held every four years among Asian nations. The first edition of the Asian Games was held in New Delhi on 8 to 11 March 1951. The 19th Asian Games were held in Hangzhou, China, from September 23 to October 08, 2023.</p>",
                    solution_hi: "<p>31.(a) <strong>एशियाड।</strong> एशियाई खेल (एशियाड) एशियाई देशों के बीच प्रत्येक चार वर्ष में आयोजित होने वाला एक बहु-खेल आयोजन है। एशियाई खेलों का प्रथम संस्करण 8 से 11 मार्च 1951 को नई दिल्ली में आयोजित किया गया था। 19वें एशियाई खेल 23 सितंबर से 08 अक्टूबर 2023 तक हांग्जो, चीन में आयोजित किए गए थे।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "32",
                    section: "18",
                    question_en: "<p>32. Which of the following classes has the largest number of animals?</p>",
                    question_hi: "<p>32. निम्नलिखित में से किस वर्ग में जंतुओं की संख्या सबसे अधिक है?</p>",
                    options_en: [
                        "<p>Insects</p>",
                        "<p>Pisces</p>",
                        "<p>Mammals</p>",
                        "<p>Reptiles</p>"
                    ],
                    options_hi: [
                        "<p>कीट</p>",
                        "<p>मत्स्य</p>",
                        "<p>स्तनधारी</p>",
                        "<p>सरीसृप</p>"
                    ],
                    solution_en: "<p>32.(a) <strong>Insects.</strong> Insects, belonging to the class Insecta, form the largest group within the phylum Arthropoda, which itself is the largest phylum in the animal kingdom. Insects account for approximately 75% of all species in Arthropoda. Their body is divided into three parts: head, thorax, and abdomen, and they possess an exoskeleton made of chitin. Insects have an open circulatory system and are triploblastic in nature, with three embryonic layers.</p>",
                    solution_hi: "<p>32.(a) <strong>कीट।</strong> कीट, इन्सेक्टा वर्ग से संबंधित, संघ आर्थ्रोपोडा के भीतर सबसे बड़ा समूह बनाते हैं, जो स्वयं जन्तु जगत में सबसे बड़ा संघ है। आर्थ्रोपोडा की सभी प्रजातियों में से लगभग 75% कीट हैं। उनका शरीर तीन भागों में विभाजित है: सिर, वक्ष और उदर, और उनके पास काइटिन से बना एक बाह्यकंकाल होता है। कीटों में एक खुला परिसंचरण तंत्र होता है और वे प्रकृति में ट्रिपलोब्लास्टिक होते हैं, जिसमें तीन भ्रूण परतें होती हैं।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "33",
                    section: "18",
                    question_en: "<p>33. Which of the following Articles of the Constitution of India states about the executive power of the Union?</p>",
                    question_hi: "<p>33. भारतीय संविधान का निम्नलिखित में से कौन-सा अनुच्छेद संघ की कार्यकारी शक्ति के बारे में बताता है?</p>",
                    options_en: [
                        "<p>Article 55</p>",
                        "<p>Article 52</p>",
                        "<p>Article 53</p>",
                        "<p>Article 54</p>"
                    ],
                    options_hi: [
                        "<p>अनुच्छेद 55</p>",
                        "<p>अनुच्छेद 52</p>",
                        "<p>अनुच्छेद 53</p>",
                        "<p>अनुच्छेद 54</p>"
                    ],
                    solution_en: "<p>33.(c) <strong>Article 53</strong> : The executive power of the Union shall be vested in the President and shall be exercised by him either directly or through officers subordinate to him in accordance with this Constitution. Article 52 : There shall be a President of India. Article 54 : The President shall be elected by the members of an electoral college consisting of elected members of both Houses of Parliament and the elected members of the Legislative Assemblies of the States. Article 55 : Manner of the election of the President.</p>",
                    solution_hi: "<p>33.(c) <strong>अनुच्छेद 53</strong> : संघ की कार्यपालिका शक्ति राष्ट्रपति में निहित होगी और वह इसका प्रयोग इस संविधान के अनुसार सीधे या अपने अधीनस्थ अधिकारियों के माध्यम से करेगा। अनुच्छेद 52 : भारत का एक राष्ट्रपति होगा। अनुच्छेद 54 : राष्ट्रपति का चुनाव संसद के दोनों सदनों के निर्वाचित सदस्यों और राज्यों की विधानसभाओं के निर्वाचित सदस्यों से मिलकर बने निर्वाचक मंडल के सदस्यों द्वारा किया जाएगा। अनुच्छेद 55 : राष्ट्रपति के निर्वाचन की रीति।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "34",
                    section: "18",
                    question_en: "<p>34. Which company launched commercial operations of a 660-MW unit at the Khurja Super Thermal Power Plant in Uttar Pradesh?</p>",
                    question_hi: "<p>34. उत्तर प्रदेश के खुर्जा सुपर थर्मल पावर प्लांट में 660-MW यूनिट के वाणिज्यिक संचालन की शुरुआत किस कंपनी ने की ?</p>",
                    options_en: [
                        "<p>NTPC</p>",
                        "<p>THDC India Ltd</p>",
                        "<p>Adani Power</p>",
                        "<p>Tata Power</p>"
                    ],
                    options_hi: [
                        "<p>NTPC</p>",
                        "<p>THDC इंडिया लिमिटेड</p>",
                        "<p>अडानी पावर</p>",
                        "<p>टाटा पावर</p>"
                    ],
                    solution_en: "<p>34.(b) <strong>THDC India Ltd.</strong> THDC India Ltd (THDCIL) launched commercial operations of a 660-MW unit at its Khurja Super Thermal Power Plant (STPP) in Uttar Pradesh, marking its entry into the thermal energy sector.</p>",
                    solution_hi: "<p>34.(b) <strong>THDC इंडिया लिमिटेड।</strong> टीएचडीसी इंडिया लिमिटेड (THDCIL) ने उत्तर प्रदेश में खुर्जा सुपर थर्मल पावर प्लांट (STPP) में 660-MW यूनिट का वाणिज्यिक संचालन शुरू किया, जिससे कंपनी ने थर्मल ऊर्जा क्षेत्र में प्रवेश किया।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "35",
                    section: "18",
                    question_en: "<p>35. From the given alternatives, identify an inner planet.</p>",
                    question_hi: "<p>35. दिए गए विकल्पों में से आंतरिक ग्रह की पहचान करें।</p>",
                    options_en: [
                        "<p>Saturn</p>",
                        "<p>Jupiter</p>",
                        "<p>Mercury</p>",
                        "<p>Uranus</p>"
                    ],
                    options_hi: [
                        "<p>शनि</p>",
                        "<p>बृहस्पति</p>",
                        "<p>बुध</p>",
                        "<p>वरुण</p>"
                    ],
                    solution_en: "<p>35.(c) <strong>Mercury.</strong> Mercury, Venus, Earth, and Mars are called Inner Planets as they lie between the Sun and the Asteroid Belt. These first four planets are Terrestrial, meaning earth-like, made of rock and metals with high densities. The outer planets - Jupiter, Saturn, Uranus, and Neptune - are called Jovian or Gas Giants, meaning Jupiter-like, much larger than terrestrial planets, with thick atmospheres mostly of helium and hydrogen.</p>",
                    solution_hi: "<p>35.(c) <strong>बुध।</strong> बुध, शुक्र, पृथ्वी और मंगल को आंतरिक ग्रह कहा जाता है क्योंकि वे सूर्य और क्षुद्रग्रह बेल्ट के बीच स्थित हैं। ये पहले चार ग्रह स्थलीय हैं, अर्थात पृथ्वी जैसे, उच्च घनत्व वाले चट्टान और धातुओं से बने हैं। बाह्य ग्रह बृहस्पति, शनि, यूरेनस और नेपच्यून - को जोवियन या गैस दानव कहा जाता है, जिसका अर्थ है बृहस्पति के समान, स्थलीय ग्रहों की तुलना में बहुत बड़ा, तथा जिसका घना वायुमंडल मुख्यतः हीलियम और हाइड्रोजन से बना है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "36",
                    section: "18",
                    question_en: "<p>36. The term \'bishop\' is associated with which game?</p>",
                    question_hi: "<p>36. \'बिशप\' शब्द किस खेल से संबंधित है?</p>",
                    options_en: [
                        "<p>basketball</p>",
                        "<p>hockey</p>",
                        "<p>chess</p>",
                        "<p>golf</p>"
                    ],
                    options_hi: [
                        "<p>बास्केटबाल</p>",
                        "<p>हॉकी</p>",
                        "<p>शतरंज</p>",
                        "<p>गोल्फ़</p>"
                    ],
                    solution_en: "<p>36.(c) <strong>chess.</strong> In chess, each player starts with two bishops. The bishop moves diagonally and plays a key role early in the game. Chess is played between two players, each controlling 16 pieces, typically white and black. The board has 64 squares arranged in an 8x8 grid, featuring alternating light and dark squares in a chequered pattern.</p>",
                    solution_hi: "<p>36.(c) <strong>शतरंज।</strong> शतरंज में, प्रत्येक खिलाड़ी दो बिशप के साथ शुरू करता है। बिशप तिरछे चलता है और खेल की शुरुआत में महत्वपूर्ण भूमिका निभाता है। शतरंज दो खिलाड़ियों के बीच खेला जाता है, जिनमें से प्रत्येक के पास 16 मोहरे होते हैं, जो सामान्यतः सफ़ेद और काले होते हैं। बोर्ड में 8<math display=\"inline\"><mo>&#215;</mo></math>8 ग्रिड में व्यवस्थित 64 वर्ग होते हैं, जिसमें चेकर पैटर्न में एकांतर हल्के और गहरे रंग के वर्ग होते हैं।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "37",
                    section: "18",
                    question_en: "<p>37. From which country were the ideals of justice of the Indian Constitution borrowed?</p>",
                    question_hi: "<p>37. भारतीय संविधान के न्याय के आदर्श किस देश से गृहीत किए गए थे?</p>",
                    options_en: [
                        "<p>The USSR</p>",
                        "<p>The UK</p>",
                        "<p>The US</p>",
                        "<p>Japan</p>"
                    ],
                    options_hi: [
                        "<p>यूएसएसआर (USSR)</p>",
                        "<p>यूके ( UK)</p>",
                        "<p>यूएस (US)</p>",
                        "<p>जापान</p>"
                    ],
                    solution_en: "<p>37.(a) <strong>The USSR</strong>. Borrowed features of the Constitution : USSR (Now Russia) - Fundamental duties, and The ideals of justice (social, economic, and political), expressed in the Preamble. Britain - Parliamentary government, Rule of Law, Legislative procedure, Single citizenship, Cabinet system, Prerogative writs, Parliamentary privileges, Bicameralism. Japan - Concept of &ldquo;procedure established by Law&rdquo;. United States of America - Impeachment of the president, Functions of president and vice-president, Removal of Supreme Court and High court judges, Fundamental Rights, Judicial review, Independence of judiciary, The preamble of the constitution.</p>",
                    solution_hi: "<p>37.(a) <strong>यूएसएसआर (USSR)</strong>। संविधान की अपनाई गई विशेषताएँ: USSR (अब रूस) - मौलिक कर्तव्य, और न्याय के आदर्श (सामाजिक, आर्थिक और राजनीतिक), प्रस्तावना में व्यक्त किए गए। ब्रिटेन - संसदीय सरकार, विधि का शासन, विधायी प्रक्रिया, एकल नागरिकता, कैबिनेट प्रणाली, विशेषाधिकार रिट, संसदीय विशेषाधिकार, द्विसदनीयता। जापान - \"विधि द्वारा स्थापित प्रक्रिया\" की अवधारणा। संयुक्त राज्य अमेरिका - राष्ट्रपति का महाभियोग, राष्ट्रपति और उपराष्ट्रपति के कार्य, सर्वोच्च न्यायालय और उच्च न्यायालय के न्यायाधीशों को हटाना, मौलिक अधिकार, न्यायिक समीक्षा, न्यायपालिका की स्वतंत्रता, संविधान की प्रस्तावना।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "38",
                    section: "18",
                    question_en: "38. Who called the preamble of the Indian Constitution \'The Political Horoscope of our Constitution\'?",
                    question_hi: "38. किसने भारतीय संविधान की उद्देशिका को हमारे संविधान की राजनीतिक कुंडली कहा है?",
                    options_en: [
                        " Dr. KM Munshi",
                        " Sir Alladi Krishnaswamy lyer  ",
                        " M. Hidayatullah",
                        " Sir Ernest Baker "
                    ],
                    options_hi: [
                        " डॉ. के.एम. मुंशी ",
                        " सर अल्लादी कृष्णास्वामी अय्यर  ",
                        " एम. हिदायतुल्लाह ",
                        " सर अर्नेस्ट बेकर"
                    ],
                    solution_en: "<p>38.(a) <strong>Dr. KM Munshi</strong>. He was a member of the Drafting Committee of the Indian Constitution. Other names for the preamble: Soul of the Constitution: By Thakurdas Bhargav. Identity card of the Constitution: By N.A. Palkhiwala. Keynote to the Constitution: By Sir Ernest Barker.</p>",
                    solution_hi: "<p>38.(a) <strong>डॉ. के.एम. मुंशी</strong> भारतीय संविधान की प्रारूप समिति के सदस्य थे। प्रस्तावना के अन्य नाम - संविधान की आत्मा : ठाकुरदास भार्गव द्वारा। संविधान का पहचान पत्र: एन.ए. पालखीवाला द्वारा। संविधान का मुख्य विचार: सर अर्नेस्ट बार्कर द्वारा।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "39",
                    section: "18",
                    question_en: "<p>39. Henry Louis Vivian Derozio, a teacher at Hindu College, Calcutta, in the 1820s, promoted radical ideas and encouraged his pupils to question all authority. Referred to as the _________ his students attacked tradition and custom, demanded education for women and campaigned for the freedom of thought and expression.</p>",
                    question_hi: "<p>39. 1820 के दशक में कलकत्ता के हिंदू कॉलेज के एक शिक्षक हेनरी लुई विवियन डेरोजियो ने चरमपंथी विचारों को बढ़ावा दिया और अपने विद्यार्थियों को सभी सत्ताओं पर सवाल उठाने के लिए प्रोत्साहित किया। उनके नेतृत्व में ________ में शामिल, उनके छात्रों ने परंपराओं और रीति-रिवाज़ों पर हमला किया, महिलाओं के लिए शिक्षा की मांग की और विचार और अभिव्यक्ति की स्वतंत्रता के लिए अभियान चलाया।</p>",
                    options_en: [
                        "<p>Young Christian Movement</p>",
                        "<p>Young Men\'s Movement</p>",
                        "<p>Young Radical Movement</p>",
                        "<p>Young Bengal Movement</p>"
                    ],
                    options_hi: [
                        "<p>यंग क्रिश्चियन आंदोलन</p>",
                        "<p>यंग मेन्स आंदोलन</p>",
                        "<p>यंग रेडिकल आंदोलन</p>",
                        "<p>यंग बंगाल आंदोलन</p>"
                    ],
                    solution_en: "<p>39.(d) <strong>Young Bengal Movement</strong>. Other Reform Organisations and its Founders : The Brahmo Samaj (1828) - Raja Ram Mohan Roy, The Veda Samaj (1864) - Keshab Chandra Sen and K. Sridharalu Naidu, The Prarthana Samaj (1867) - Atmaram Pandurang.</p>",
                    solution_hi: "<p>39.(d)<strong> यंग बंगाल आंदोलन।</strong> अन्य सुधार संगठन और उसके संस्थापक: ब्रह्म समाज (1828) - राजा राम मोहन राय, वेद समाज (1864) - केशव चंद्र सेन और के.श्रीधरलु नायडू, प्रार्थना समाज (1867) - आत्माराम पांडुरंग।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "40",
                    section: "18",
                    question_en: "<p>40. Which is the weakest intermolecular force, considered as the Van der Waals force, often found in halogens, noble gases and other non-polar molecules?</p>",
                    question_hi: "<p>40. वान डेर वाल्स बल के रूप में माना जाने वाला सबसे दुर्बल अंतर-आणविक बल कौन-सा है, जो अक्सर हैलोजन, आदर्श गैसों और अन्य गैर-ध्रुवीय अणुओं में पाया जाता है?</p>",
                    options_en: [
                        "<p>London dispersion forces</p>",
                        "<p>Ion-dipole forces</p>",
                        "<p>Dipole-induced dipole forces</p>",
                        "<p>Dipole - dipole forces</p>"
                    ],
                    options_hi: [
                        "<p>लंदन प्रकीर्णन बल</p>",
                        "<p>आयन-द्विध्रुव बल</p>",
                        "<p>द्विध्रुव-प्रेरित द्विध्रुव बल</p>",
                        "<p>द्विध्रुव-द्विध्रुव बल</p>"
                    ],
                    solution_en: "<p>40.(a) <strong>London dispersion forces</strong>. These forces are always attractive and interaction energy is inversely proportional to the sixth power of the distance between two interacting particles (i.e., 1/r⁶&nbsp;where r is the distance between two particles). These forces are important only at short distances (~500 pm) and their magnitude depends on the polarisability of the particle.</p>",
                    solution_hi: "<p>40.(a) <strong>लंदन प्रकीर्णन बल।</strong> ये बल हमेशा आकर्षक होते हैं और परस्पर क्रिया ऊर्जा, दो कणों के बीच की दूरी के छठे घात (अर्थात 1/r⁶, जहाँ r दो कणों के बीच की दूरी है) के व्युत्क्रमानुपाती होती है। ये बल केवल छोटी दूरी (~500 pm) पर ही महत्वपूर्ण होते हैं और उनका परिमाण कण की ध्रुवीकरण क्षमता पर निर्भर करता है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "41",
                    section: "18",
                    question_en: "<p>41. Who is the author of the poetry \'Chand Pukhraj Ka\'?</p>",
                    question_hi: "<p>41. \'चांद पुखराज का (Chand Pukhraj Ka)\' काव्य के लेखक कौन हैं?</p>",
                    options_en: [
                        "<p>Anand Bakshi</p>",
                        "<p>Gulzar</p>",
                        "<p>Gurmel Singh</p>",
                        "<p>Shailendra</p>"
                    ],
                    options_hi: [
                        "<p>आनंद बख्शी</p>",
                        "<p>गुलजार</p>",
                        "<p>गुरमेल सिंह</p>",
                        "<p>शैलेंद्र</p>"
                    ],
                    solution_en: "<p>41.(b) <strong>Gulzar.</strong> His awards : Dadasaheb Phalke Award in 2013; Padma Bhushan in 2004; Sahitya Akademi Award for Urdu in 2002. His other Poems - Triveni, Raat Pashmine Ki, Suspected Poems. Anand Bakshi - Nagme, Kisse, Baatein, Yaadein. Gurmel Singh - Economic Liberalisation and Indian Agriculture. Shailendra - Na Bairi Na Koi Begana.</p>",
                    solution_hi: "<p>41.(b) <strong>गुलज़ार।</strong> उनके पुरस्कार: 2013 में दादा साहब फाल्के पुरस्कार; 2004 में पद्म भूषण; 2002 में उर्दू के लिए साहित्य अकादमी पुरस्कार। उनकी अन्य कविताएँ - त्रिवेणी, रात पश्मीने की, सस्पेक्टेड पोयम्स। आनंद बख्शी - नग्मे, किस्से, बातें, यादें। गुरमेल सिंह - इकॉनोमिक लिबरलाइजेशन एंड इंडियन एग्रीकल्चर। शैलेन्द्र - ना बैरी ना कोई बेगाना।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "42",
                    section: "18",
                    question_en: "<p>42. The All India MCC Murugappa Gold Cup Tournament in Chennai is held for which sport?</p>",
                    question_hi: "<p>42. चेन्नई में अखिल भारतीय एमसीसी मुरुगप्पा गोल्ड कप टूर्नामेंट किस खेल के लिए आयोजित किया जाता है?</p>",
                    options_en: [
                        "<p>Basketball</p>",
                        "<p>Handball</p>",
                        "<p>Hockey</p>",
                        "<p>Football</p>"
                    ],
                    options_hi: [
                        "<p>बास्केटबॉल</p>",
                        "<p>हैंडबॉल</p>",
                        "<p>हॉकी</p>",
                        "<p>फुटबॉल</p>"
                    ],
                    solution_en: "<p>42.(c) <strong>Hockey.</strong> Murugappa Gold Cup Hockey Tournament 2024 : Winner - Railways Sports Promotion Board (RSPB); Runner up - Maharashtra. Prominent Indian Hockey Players : Jarmanpreet Singh, Sanjay Rana, Raj Kumar Pal, Abhishek and Sukhjeet Singh.</p>",
                    solution_hi: "<p>42.(c) <strong>हॉकी।</strong> मुरुगप्पा गोल्ड कप हॉकी टूर्नामेंट 2024: विजेता - रेलवे स्पोर्ट्स प्रमोशन बोर्ड (RSPB); उपविजेता - महाराष्ट्र। प्रमुख भारतीय हॉकी खिलाड़ी: जरमनप्रीत सिंह, संजय राणा, राज कुमार पाल, अभिषेक और सुखजीत सिंह।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "43",
                    section: "18",
                    question_en: "<p>43. Foods like pizza, burger are rich in:</p>",
                    question_hi: "<p>43. पिज्जा, बर्गर जैसे खाद्य पदार्थ ___________ से भरपूर होते हैं।</p>",
                    options_en: [
                        "<p>Proteins</p>",
                        "<p>Vitamins</p>",
                        "<p>Carbohydrates</p>",
                        "<p>Minerals</p>"
                    ],
                    options_hi: [
                        "<p>प्रोटीन</p>",
                        "<p>विटामिन</p>",
                        "<p>कार्बोहाइड्रेट</p>",
                        "<p>खनिज पदार्थ</p>"
                    ],
                    solution_en: "<p>43.(c) <strong>Carbohydrates.</strong> This is primarily due to the presence of ingredients like bread (pizza crust, burger buns), which are made from refined flour. Carbohydrates are a major energy source and are abundant in such fast foods. In addition to carbohydrates, these foods may also contain fats (from cheese, oils, and meats) and some proteins (from meat, cheese, or toppings), but carbohydrates are the dominant nutrient in these items.</p>",
                    solution_hi: "<p>43.(c) <strong>कार्बोहाइड्रेट।</strong> यह मुख्य रूप से ब्रेड (पिज्जा क्रस्ट, बर्गर बन्स) जैसी सामग्री की उपस्थिति के कारण होता है, जो परिष्कृत आटे से बने होते हैं। कार्बोहाइड्रेट एक प्रमुख ऊर्जा स्रोत हैं और ऐसे फास्ट फूड में प्रचुर मात्रा में होते हैं। कार्बोहाइड्रेट के अलावा, इन खाद्य पदार्थों में वसा (पनीर, तेल और मांस) और कुछ प्रोटीन (मांस, पनीर या टॉपिंग) भी हो सकते हैं, लेकिन इन वस्तुओं में कार्बोहाइड्रेट प्रमुख पोषक तत्व हैं।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "44",
                    section: "18",
                    question_en: "<p>44. The Durg-Bastar-Chandrapur iron ore belt lies in ____________ states.</p>",
                    question_hi: "<p>44. दुर्ग-बस्तर-चंद्रपुर लौह अयस्क पट्टी (बेल्ट) ___________ राज्यों में स्थित है।</p>",
                    options_en: [
                        "<p>Maharashtra and Karnataka</p>",
                        "<p>Chhattisgarh and Maharashtra</p>",
                        "<p>Maharashtra and Telangana</p>",
                        "<p>Chhattisgarh and Jharkhand</p>"
                    ],
                    options_hi: [
                        "<p>महाराष्ट्र और कर्नाटक</p>",
                        "<p>छत्तीसगढ़ और महाराष्ट्र</p>",
                        "<p>महाराष्ट्र और तेलंगाना</p>",
                        "<p>छत्तीसगढ़ और झारखंड</p>"
                    ],
                    solution_en: "<p>44.(b) <strong>Chhattisgarh and Maharashtra.</strong> The Durg-Bastar-Chandrapur iron ore belt stretches across the Durg and Bastar districts in Chhattisgarh and the Chandrapur district in Maharashtra. Iron ore mines in India: Bailadila (Chhattisgarh), Joda (Odisha), Noamundi (Jharkhand), and Kudremukh (Karnataka).</p>",
                    solution_hi: "<p>44.(b) <strong>छत्तीसगढ़ और महाराष्ट्र।</strong> दुर्ग-बस्तर-चंद्रपुर लौह अयस्क बेल्ट छत्तीसगढ़ में दुर्ग और बस्तर जिलों तथा महाराष्ट्र में चंद्रपुर जिले तक फैला हुआ है। भारत में लौह अयस्क की खदानें: बैलाडिला (छत्तीसगढ़), जोड़ा (ओडिशा), नोआमुंडी (झारखंड), और कुद्रेमुख (कर्नाटक)।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "45",
                    section: "18",
                    question_en: "<p>45. Gloves are NOT used in which of the following sports?</p>",
                    question_hi: "<p>45. निम्नलिखित में से किस खेल में दस्ताने (Gloves) का उपयोग नहीं होता है?</p>",
                    options_en: [
                        "<p>Basketball</p>",
                        "<p>Hockey</p>",
                        "<p>Cricket</p>",
                        "<p>Football</p>"
                    ],
                    options_hi: [
                        "<p>बास्केटबॉल</p>",
                        "<p>हॉकी</p>",
                        "<p>क्रिकेट</p>",
                        "<p>फुटबॉल</p>"
                    ],
                    solution_en: "<p>45.(a) <strong>Basketball</strong> requires a good grip on the ball and quick hand movements, which are best achieved with bare hands. Gloves would hinder these movements and reduce control over the ball. Games where gloves are generally not required include volleyball, tennis and rugby.</p>",
                    solution_hi: "<p>45.(a) <strong>बास्केटबॉल</strong> में गेंद पर अच्छी पकड़ और हाथों के तेज़ संचालन की आवश्यकता होती है, जो नंगे हाथों से सबसे अच्छी तरह से हासिल की जा सकती हैं। दस्ताने इन गतिविधियों में बाधा डालते हैं और गेंद पर नियंत्रण कम करते हैं। ऐसे खेल जिनमें सामान्यतः दस्ताने की ज़रूरत नहीं होती है, उनमें वॉलीबॉल, टेनिस और रग्बी शामिल हैं।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "46",
                    section: "18",
                    question_en: "<p>46. The All-India Muslim League, popularly known as the Muslim League, was founded in _________ .</p>",
                    question_hi: "<p>46. अखिल भारतीय मुस्लिम लीग, जिसे मुस्लिम लीग के नाम से जाना जाता है, की स्थापना __________ में हुई थी।</p>",
                    options_en: [
                        "<p>1910</p>",
                        "<p>1906</p>",
                        "<p>1908</p>",
                        "<p>1904</p>"
                    ],
                    options_hi: [
                        "<p>1910</p>",
                        "<p>1906</p>",
                        "<p>1908</p>",
                        "<p>1904</p>"
                    ],
                    solution_en: "<p>46.(b) <strong>1906.</strong> The All-India Muslim League was formed during the tenure of Lord Minto II, who served as the Viceroy of India from 1905 to 1910. The League was founded in Dhaka, British India (Bangladesh), in 1906 and supported the partition of Bengal. It sought separate electorates for Muslims, a demand that was conceded by the government in 1909.</p>",
                    solution_hi: "<p>46.(b) <strong>1906.</strong> अखिल भारतीय मुस्लिम लीग का गठन लॉर्ड मिंटो द्वितीय के कार्यकाल के दौरान हुआ था, जो 1905 से 1910 तक भारत के वायसराय रहे। लीग की स्थापना 1906 में ढाका, ब्रिटिश भारत (बांग्लादेश) में हुई थी और इसने बंगाल के विभाजन का समर्थन किया था। इसने मुसलमानों के लिए अलग निर्वाचन क्षेत्र की मांग की, जिसे सरकार ने 1909 में स्वीकार कर लिया।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "47",
                    section: "18",
                    question_en: "<p>47. Which of the following agencies is responsible for the operation and maintenance of the Hazira- Vijaypur-Jagdishpur pipeline?</p>",
                    question_hi: "<p>47. हजीरा-विजयपुर-जगदीशपुर पाइपलाइन के संचालन और रखरखाव के लिए निम्नलिखित में से कौन-सी एजेंसी जिम्मेदार है?</p>",
                    options_en: [
                        "<p>Hindustan Petroleum Corporation Limited</p>",
                        "<p>Gas Authority of India Limited</p>",
                        "<p>Indian Oil Corporation Limited</p>",
                        "<p>Oil and Natural Gas Commission</p>"
                    ],
                    options_hi: [
                        "<p>हिंदुस्तान पेट्रोलियम कॉर्पोरेशन लिमिटेड</p>",
                        "<p>गैस अथॉरिटी ऑफ इंडिया लिमिटेड</p>",
                        "<p>इंडियन ऑयल कॉर्पोरेशन लिमिटेड</p>",
                        "<p>ऑयल एंड नेचुरल गैस कमीशन</p>"
                    ],
                    solution_en: "<p>47.(b) <strong>Gas Authority of India Limited (GAIL)</strong>. Hazira-Vijaipur-Jagdishpur (HVJ) is India\'s first cross-country natural gas pipeline commissioned in 1987-88 with compressor stations at Hazira, Jhabua (Madhya pradesh), Vijaipur (Madhya Pradesh) and Auraiya (Uttar Pradesh). It transports natural gas from Hazira in Gujarat to Jagdishpur in Uttar Pradesh.</p>",
                    solution_hi: "<p>47.(b)<strong> गैस अथॉरिटी ऑफ इंडिया लिमिटेड (GAIL)</strong>। हजीरा-विजयपुर-जगदीशपुर (HVJ) भारत की पहली क्रॉस-कंट्री प्राकृतिक गैस पाइपलाइन है जिसे 1987-88 में हजीरा, झाबुआ (मध्य प्रदेश), विजयपुर (मध्य प्रदेश) और औरैया (उत्तर प्रदेश) में कंप्रेसर स्टेशनों के साथ चालू किया गया था। यह गुजरात के हजीरा से उत्तर प्रदेश के जगदीशपुर तक प्राकृतिक गैस का परिवहन करता है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "48",
                    section: "18",
                    question_en: "<p>48. Which is a colourless, odourless gas of the alkane series of hydrocarbons with a chemical formula of C<sub>3</sub>H<sub>8</sub> ?</p>",
                    question_hi: "<p>48. C<sub>3</sub>H<sub>8</sub> के रासायनिक सूत्र वाली, हाइड्रोकार्बन की एल्केन श्रृंखला की एक रंगहीन, गंधहीन गैस कौन-सी है?</p>",
                    options_en: [
                        "<p>Ethane</p>",
                        "<p>Butane</p>",
                        "<p>Propane</p>",
                        "<p>Pentane</p>"
                    ],
                    options_hi: [
                        "<p>एथेन</p>",
                        "<p>ब्यूटेन</p>",
                        "<p>प्रोपेन</p>",
                        "<p>पेन्टेन</p>"
                    ],
                    solution_en: "<p>48.(c) Propane. Its main uses include home and water heating, cooking and refrigerating food, clothes drying, and powering farm and industrial equipment. The chemical formula for ethane is C<sub>2</sub>H<sub>6</sub>, for butane is C<sub>4</sub>H<sub>10</sub>, and for pentane is C<sub>5</sub>H<sub>12</sub>.</p>",
                    solution_hi: "<p>48.(c) प्रोपेन। इसके मुख्य उपयोगों में होम और वाटर हीटिंग, कुकिंग और फूड रेफ्रीजेरेटिंग, कपड़े सुखाना (clothes drying), तथा कृषि और औद्योगिक उपकरणों को शक्ति प्रदान करना शामिल है। इथेन का रासायनिक सूत्र C<sub>2</sub>H<sub>6</sub> है, ब्यूटेन का C<sub>4</sub>H<sub>10</sub> है, और पेंटेन का C<sub>5</sub>H<sub>12</sub> है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "49",
                    section: "18",
                    question_en: "<p>49. Where did the 6th India-Australia Maritime Security Dialogue take place?</p>",
                    question_hi: "<p>49. 6वें भारत-ऑस्ट्रेलिया समुद्री सुरक्षा संवाद का आयोजन कहाँ हुआ?</p>",
                    options_en: [
                        "<p>New Delhi</p>",
                        "<p>Canberra</p>",
                        "<p>Sydney</p>",
                        "<p>Melbourne</p>"
                    ],
                    options_hi: [
                        "<p>नई दिल्ली</p>",
                        "<p>कैनबरा</p>",
                        "<p>सिडनी</p>",
                        "<p>मेलबर्न</p>"
                    ],
                    solution_en: "<p>49.(b) <strong>Canberra.</strong> The two sides conferred on ways to sustain a safe and secure maritime environment conducive for inclusive growth and global well being. They exchanged views on various topics of mutual interest, including the maritime security environment in the Indo-Pacific region.</p>",
                    solution_hi: "<p>49.(b) <strong>कैनबरा।</strong> दोनों पक्षों ने समावेशी विकास और वैश्विक भलाई के लिए एक सुरक्षित और संरक्षित समुद्री वातावरण बनाए रखने के तरीकों पर चर्चा की। उन्होंने हिंद-प्रशांत क्षेत्र में समुद्री सुरक्षा पर्यावरण सहित आपसी रुचि के विभिन्न विषयों पर विचारों का आदान-प्रदान किया।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "50",
                    section: "17",
                    question_en: "<p>50. Which state won the PATA Gold Award 2024 for its innovative \'Holiday Heist\' campaign?</p>",
                    question_hi: "<p>50. किस राज्य ने अपनी अभिनव \'हॉलिडे हेस्ट\' अभियान के लिए PATA गोल्ड अवार्ड 2024 जीता?</p>",
                    options_en: [
                        "<p>Rajasthan</p>",
                        "<p>Kerala</p>",
                        "<p>Goa</p>",
                        "<p>Himachal Pradesh</p>"
                    ],
                    options_hi: [
                        "<p>राजस्थान</p>",
                        "<p>केरल</p>",
                        "<p>गोवा</p>",
                        "<p>हिमाचल प्रदेश</p>"
                    ],
                    solution_en: "<p>50.(b) <strong>Kerala.</strong> Kerala Tourism clinched the PATA (Pacific Asia Travel Association) Gold Award 2024 in the Marketing category for its innovative \'Holiday Heist\' campaign. The campaign aimed to highlight Kerala\'s unique tourism offerings by blending storytelling and digital innovation, enhancing its appeal to global audiences.</p>",
                    solution_hi: "<p>50.(b) <strong>केरल।</strong> केरल पर्यटन ने PATA (पैसिफिक एशिया ट्रैवल एसोसिएशन) गोल्ड अवार्ड 2024 को मार्केटिंग श्रेणी में \'हॉलिडे हेस्ट\' अभियान के लिए जीता। इस अभियान का उद्देश्य केरल की अद्वितीय पर्यटन पेशकशों को प्रस्तुत करना था, जिसमें कहानी कहने और डिजिटल नवाचार का संगम था, जो वैश्विक दर्शकों के लिए इसकी आकर्षण को बढ़ाता है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "51",
                    section: "17",
                    question_en: "<p>51. A scalene triangle ABC has two sides whose measures are 3.8 cm and 6 cm, respectively. Which of the following CANNOT be the measure (in cm) of its third side?</p>",
                    question_hi: "<p>51. एक विषमबाहु त्रिभुज ABC की दो भुजाओं की माप क्रमशः 3.8 cm और 6 cm हैं। निम्नलिखित में से कौन-सी इसकी तीसरी भुजा की माप (cm में) नहीं हो सकती है?</p>",
                    options_en: [
                        "<p>3.0</p>",
                        "<p>2.7</p>",
                        "<p>2.4</p>",
                        "<p>2.2</p>"
                    ],
                    options_hi: [
                        "<p>3.0</p>",
                        "<p>2.7</p>",
                        "<p>2.4</p>",
                        "<p>2.2</p>"
                    ],
                    solution_en: "<p>51.(d)<br>AB = 3.8 cm<br>BC = 6 cm<br>CA &gt;&nbsp;BC - AB <br>&nbsp; &nbsp; &nbsp; &gt; 6 - 3.8<br>&nbsp; &nbsp; &nbsp; &gt; 2.2 cm<br>On checking the option we can see that only option (d) cannot be the side of the scalene triangle.</p>",
                    solution_hi: "<p>51.(d)<br>AB = 3.8 cm<br>BC = 6 cm<br>CA &gt;&nbsp;BC - AB <br>&nbsp; &nbsp; &nbsp; &gt; 6 - 3.8<br>&nbsp; &nbsp; &nbsp; &gt; 2.2 cm<br>विकल्प की जाँच करने पर हम देख सकते हैं कि केवल विकल्प (d) विषमबाहु त्रिभुज की भुजा नहीं हो सकती है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "52",
                    section: "17",
                    question_en: "<p>52. The average salary of a group of 12 employees in an institution is ₹3,950 per month and that of another group of employees is ₹1,850. If the average salary of all employees is ₹2,150, then the total number of employees is:</p>",
                    question_hi: "<p>52 एक संस्थान में 12 कर्मचारियों के समूह का औसत वेतन ₹3,950 प्रति माह और कर्मचारियों के एक दूसरे समूह का औसत वेतन ₹1,850 प्रति माह है। यदि सभी कर्मचारियों का औसत वेतन ₹2,150 है, तो कर्मचारियों की कुल संख्या ज्ञात कीजिए।</p>",
                    options_en: [
                        "<p>84</p>",
                        "<p>100</p>",
                        "<p>88</p>",
                        "<p>72</p>"
                    ],
                    options_hi: [
                        "<p>84</p>",
                        "<p>100</p>",
                        "<p>88</p>",
                        "<p>72</p>"
                    ],
                    solution_en: "<p>52.(a) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744431432983.png\" alt=\"rId52\" width=\"213\" height=\"170\"><br><math display=\"inline\"><mfrac><mrow><mn>12</mn></mrow><mrow><mi>x</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>6</mn></mfrac></math>&rArr; x = 72<br>Total employees = (<math display=\"inline\"><mi>x</mi></math> + 12) = 84</p>",
                    solution_hi: "<p>52.(a) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744431433392.png\" alt=\"rId53\" width=\"219\" height=\"177\"><br><math display=\"inline\"><mfrac><mrow><mn>12</mn></mrow><mrow><mi>x</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>6</mn></mfrac></math>&rArr; x = 72<br>कुल कर्मचारी = (<math display=\"inline\"><mi>x</mi></math> + 12) = 84</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "53",
                    section: "17",
                    question_en: "53. Find the third proportion to 36 and 48.",
                    question_hi: "53. 36 और 48 का तृतीय समानुपाती ज्ञात कीजिए।",
                    options_en: [
                        " 36  ",
                        " 54 ",
                        " 48 ",
                        " 64"
                    ],
                    options_hi: [
                        " 36  ",
                        " 54 ",
                        " 48 ",
                        " 64"
                    ],
                    solution_en: "53.(d)<br />Third proportion = <math display=\"inline\"><mfrac><mrow><msup><mrow><mo>(</mo><mn>48</mn><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup></mrow><mrow><mn>36</mn></mrow></mfrac></math> = 8 × 8 = 64",
                    solution_hi: "53.(d)<br />तीसरा अनुपात = <math display=\"inline\"><mfrac><mrow><msup><mrow><mo>(</mo><mn>48</mn><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup></mrow><mrow><mn>36</mn></mrow></mfrac></math> = 8 × 8 = 64",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "54",
                    section: "17",
                    question_en: "<p>54. If 6 people are required to complete a work in 1 hour 20 minutes, then how long will only 5 people take to complete the same work?</p>",
                    question_hi: "<p>54. यदि किसी कार्य को 1 घंटे 20 मिनट में पूरा करने के लिए 6 व्यक्तियों की आवश्यकता होती है, तो उसी कार्य को केवल 5 व्यक्ति कितने समय में पूरा करेंगे?</p>",
                    options_en: [
                        "<p>105 minutes</p>",
                        "<p>90 minutes</p>",
                        "<p>96 minutes</p>",
                        "<p>100 minutes</p>"
                    ],
                    options_hi: [
                        "<p>105 मिनट</p>",
                        "<p>90 मिनट</p>",
                        "<p>96 मिनट</p>",
                        "<p>100 मिनट</p>"
                    ],
                    solution_en: "<p>54.(c)<br>Let the required time taken be &lsquo;t&rsquo;<br>1hr 20min = 60 + 20 = 80 minutes<br>According to the question,<br>6 &times; 80 = 5 &times; t<br>480 = 5t<br>t = <math display=\"inline\"><mfrac><mrow><mn>480</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> = 96 minutes</p>",
                    solution_hi: "<p>54.(c)<br>माना कि लिया गया आवश्यक समय \'t\' है<br>1 घंटा 20 मिनट = 60 + 20 = 80 मिनट<br>प्रश्न के अनुसार, <br>6 &times; 80 = 5 &times; t<br>480 = 5t<br>t = <math display=\"inline\"><mfrac><mrow><mn>480</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> = 96 मिनट</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "55",
                    section: "17",
                    question_en: "<p>55. If Psin60&deg; = Qcosec45&deg;, then the value of <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi>P</mi><mn>2</mn></msup><mo>+</mo><msup><mi>Q</mi><mn>2</mn></msup></mrow><mrow><msup><mi>P</mi><mn>2</mn></msup><mo>-</mo><msup><mi>Q</mi><mn>2</mn></msup></mrow></mfrac></math> is equal to:</p>",
                    question_hi: "<p>55. यदि Psin60&deg; = Qcosec45&deg;, है, तो <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi>P</mi><mn>2</mn></msup><mo>+</mo><msup><mi>Q</mi><mn>2</mn></msup></mrow><mrow><msup><mi>P</mi><mn>2</mn></msup><mo>-</mo><msup><mi>Q</mi><mn>2</mn></msup></mrow></mfrac></math>&nbsp;का मान किसके बराबर है?</p>",
                    options_en: [
                        "<p><math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>11</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>11</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>11</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>7</mn></mrow><mrow><mn>11</mn></mrow></mfrac></math></p>"
                    ],
                    options_hi: [
                        "<p><math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>11</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>11</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>11</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>7</mn></mrow><mrow><mn>11</mn></mrow></mfrac></math></p>"
                    ],
                    solution_en: "<p>55.(c)<br>Psin60&deg; = Qcosec45&deg;<br>P&times;<math display=\"inline\"><mfrac><mrow><msqrt><mn>3</mn></msqrt></mrow><mrow><mn>2</mn></mrow></mfrac></math> = Q&times;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>2</mn></msqrt></math><br><math display=\"inline\"><mfrac><mrow><mi>P</mi></mrow><mrow><mi>Q</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><msqrt><mn>2</mn></msqrt></mrow><msqrt><mn>3</mn></msqrt></mfrac></math><br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi>P</mi><mn>2</mn></msup><mo>+</mo><msup><mi>Q</mi><mn>2</mn></msup></mrow><mrow><msup><mi>P</mi><mn>2</mn></msup><mo>-</mo><msup><mi>Q</mi><mn>2</mn></msup></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mrow><mo>(</mo><mn>2</mn><msqrt><mn>2</mn></msqrt><mo>)</mo></mrow><mn>2</mn></msup><mo>+</mo><msup><mrow><mo>(</mo><msqrt><mn>3</mn></msqrt><mo>)</mo></mrow><mn>2</mn></msup></mrow><mrow><msup><mrow><mo>(</mo><mn>2</mn><msqrt><mn>2</mn></msqrt><mo>)</mo></mrow><mn>2</mn></msup><mo>-</mo><msup><mrow><mo>(</mo><msqrt><mn>3</mn></msqrt><mo>)</mo></mrow><mn>2</mn></msup></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>8</mn><mo>+</mo><mn>3</mn></mrow><mrow><mn>8</mn><mo>-</mo><mn>3</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>11</mn><mn>5</mn></mfrac></math></p>",
                    solution_hi: "<p>55.(c)<br>Psin60&deg; = Qcosec45&deg;<br>P&times;<math display=\"inline\"><mfrac><mrow><msqrt><mn>3</mn></msqrt></mrow><mrow><mn>2</mn></mrow></mfrac></math> = Q&times;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>2</mn></msqrt></math><br><math display=\"inline\"><mfrac><mrow><mi>P</mi></mrow><mrow><mi>Q</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><msqrt><mn>2</mn></msqrt></mrow><msqrt><mn>3</mn></msqrt></mfrac></math><br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi>P</mi><mn>2</mn></msup><mo>+</mo><msup><mi>Q</mi><mn>2</mn></msup></mrow><mrow><msup><mi>P</mi><mn>2</mn></msup><mo>-</mo><msup><mi>Q</mi><mn>2</mn></msup></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mrow><mo>(</mo><mn>2</mn><msqrt><mn>2</mn></msqrt><mo>)</mo></mrow><mn>2</mn></msup><mo>+</mo><msup><mrow><mo>(</mo><msqrt><mn>3</mn></msqrt><mo>)</mo></mrow><mn>2</mn></msup></mrow><mrow><msup><mrow><mo>(</mo><mn>2</mn><msqrt><mn>2</mn></msqrt><mo>)</mo></mrow><mn>2</mn></msup><mo>-</mo><msup><mrow><mo>(</mo><msqrt><mn>3</mn></msqrt><mo>)</mo></mrow><mn>2</mn></msup></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>8</mn><mo>+</mo><mn>3</mn></mrow><mrow><mn>8</mn><mo>-</mo><mn>3</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>11</mn><mn>5</mn></mfrac></math></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "56",
                    section: "17",
                    question_en: "<p>56. In June, Rohit&rsquo;s bank account balance is ₹5,000 for 25 days, ₹20,000 for 2 days and ₹1,500 for 3 days. What is the average balance (in ₹) in Rohit\'s bank account in June?</p>",
                    question_hi: "<p>56. जून में, रोहित के बैंक खाते का बैलेंस 25 दिनों के लिए ₹5,000, 2 दिनों के लिए ₹20,000 और 3 दिनों के लिए ₹1,500 है। जून में रोहित के बैंक खाते में औसत बैलेंस (₹ में) कितना है?</p>",
                    options_en: [
                        "<p>5650</p>",
                        "<p>5575</p>",
                        "<p>6000</p>",
                        "<p>5200</p>"
                    ],
                    options_hi: [
                        "<p>5650</p>",
                        "<p>5575</p>",
                        "<p>6000</p>",
                        "<p>5200</p>"
                    ],
                    solution_en: "<p>56.(a)<br>Average balance = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>25</mn><mo>&#215;</mo><mn>5000</mn><mo>)</mo><mo>+</mo><mo>(</mo><mn>2</mn><mo>&#215;</mo><mn>20000</mn><mo>)</mo><mo>+</mo><mo>(</mo><mn>3</mn><mo>&#215;</mo><mn>1500</mn><mo>)</mo></mrow><mrow><mn>25</mn><mo>+</mo><mn>2</mn><mo>+</mo><mn>3</mn></mrow></mfrac></math> = 5650</p>",
                    solution_hi: "<p>56.(a)<br>औसत बैलेंस = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>25</mn><mo>&#215;</mo><mn>5000</mn><mo>)</mo><mo>+</mo><mo>(</mo><mn>2</mn><mo>&#215;</mo><mn>20000</mn><mo>)</mo><mo>+</mo><mo>(</mo><mn>3</mn><mo>&#215;</mo><mn>1500</mn><mo>)</mo></mrow><mrow><mn>25</mn><mo>+</mo><mn>2</mn><mo>+</mo><mn>3</mn></mrow></mfrac></math> = 5650</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "57",
                    section: "17",
                    question_en: "<p>57. The given pie-charts show the distribution of Graduate and Post-Graduate level students in five different colleges A, B, C, D and E<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744431433688.png\" alt=\"rId54\" width=\"345\" height=\"229\"> <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744431433811.png\" alt=\"rId55\" width=\"359\" height=\"226\"> <br>How many students of colleges A and B are studying at Graduate level?</p>",
                    question_hi: "<p>57. दिए गए पाई-चार्ट पांच अलग-अलग महावि&zwnj;द्यालयों A, B, C, D और E में स्नातक और स्नातकोत्तर स्तर के छात्रों के वितरण को दर्शाते हैं।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744431433991.png\" alt=\"rId56\" width=\"326\" height=\"191\"> <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744431434113.png\" alt=\"rId57\" width=\"329\" height=\"217\"> <br>महाविद्यालय A और B के कितने छात्र स्नातक स्तर पर अध्ययन कर रहे हैं?</p>",
                    options_en: [
                        "<p>5,987</p>",
                        "<p>4,520</p>",
                        "<p>6,993</p>",
                        "<p>7,052</p>"
                    ],
                    options_hi: [
                        "<p>5,987</p>",
                        "<p>4,520</p>",
                        "<p>6,993</p>",
                        "<p>7,052</p>"
                    ],
                    solution_en: "<p>57.(c)<br>Students who studying at graduate level in both collage = 18% + 19% = 37%<br>100% = 18900<br>37% = 189 &times; 37 = 6993</p>",
                    solution_hi: "<p>57.(c)<br>दोनों महाविद्यालयों में स्नातक स्तर पर अध्ययन करने वाले छात्र = 18% + 19% = 37%<br>100% = 18900<br>37% = 189 &times; 37 = 6993</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "58",
                    section: "17",
                    question_en: "<p>58. Two pipes X and Y can fill a tank in 14 hours and 21 hours, respectively. Both pipes are opened simultaneously to fill the tank. In how many hours will the empty tank be filled?</p>",
                    question_hi: "<p>58. दो पाइप X और Y एक टंकी को क्रमशः 14 घंटे और 21 घंटे में भर सकते हैं। टंकी को भरने के लिए दोनों पाइप एक साथ खोले जाते हैं। खाली टंकी कितने घंटे में भर जायेगी?</p>",
                    options_en: [
                        "<p>8<math display=\"inline\"><mfrac><mrow><mi>&#160;</mi><mn>2</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> hours</p>",
                        "<p>6<math display=\"inline\"><mfrac><mrow><mi>&#160;</mi><mn>2</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> hours</p>",
                        "<p>7<math display=\"inline\"><mfrac><mrow><mi>&#160;</mi><mn>2</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> hours</p>",
                        "<p>5<math display=\"inline\"><mfrac><mrow><mi>&#160;</mi><mn>2</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> hours</p>"
                    ],
                    options_hi: [
                        "<p>8<math display=\"inline\"><mfrac><mrow><mi>&#160;</mi><mn>2</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> घंटे</p>",
                        "<p>6<math display=\"inline\"><mfrac><mrow><mi>&#160;</mi><mn>2</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> घंटे</p>",
                        "<p>7<math display=\"inline\"><mfrac><mrow><mi>&#160;</mi><mn>2</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> घंटे</p>",
                        "<p>5<math display=\"inline\"><mfrac><mrow><mi>&#160;</mi><mn>2</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> घंटे</p>"
                    ],
                    solution_en: "<p>58.(a)<br>Time taken to fill tank = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>14</mn><mo>&#215;</mo><mn>21</mn></mrow><mrow><mn>14</mn><mo>+</mo><mn>21</mn></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>14</mn><mo>&#215;</mo><mn>21</mn></mrow><mn>35</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>42</mn><mn>5</mn></mfrac></math>= 8<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>5</mn></mfrac></math> hours</p>",
                    solution_hi: "<p>58.(a)<br>टंकी भरने में लगा समय = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>14</mn><mo>&#215;</mo><mn>21</mn></mrow><mrow><mn>14</mn><mo>+</mo><mn>21</mn></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>14</mn><mo>&#215;</mo><mn>21</mn></mrow><mn>35</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>42</mn><mn>5</mn></mfrac></math>= 8<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>5</mn></mfrac></math>&nbsp;घंटे</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "59",
                    section: "17",
                    question_en: "<p>59. On dividing a certain number by 304, we get 43 as the remainder. If the same number is divided by 16, what will be the remainder?</p>",
                    question_hi: "<p>59. एक निश्चित संख्या को 304 से विभाजित करने पर हम शेषफल के रूप में 43 प्राप्त करते हैं। यदि उसी संख्या को 16 से विभाजित किया जाए, तो शेषफल क्या होगा?</p>",
                    options_en: [
                        "<p>8</p>",
                        "<p>11</p>",
                        "<p>15</p>",
                        "<p>12</p>"
                    ],
                    options_hi: [
                        "<p>8</p>",
                        "<p>11</p>",
                        "<p>15</p>",
                        "<p>12</p>"
                    ],
                    solution_en: "<p>59.(b) <br>Let number = (304 + 43) = 347<br>Now, required remainder = <math display=\"inline\"><mfrac><mrow><mn>347</mn></mrow><mrow><mn>16</mn></mrow></mfrac></math> = 11</p>",
                    solution_hi: "<p>59.(b) <br>माना संख्या = (304 + 43) = 347<br>अब, आवश्यक शेषफल = <math display=\"inline\"><mfrac><mrow><mn>347</mn></mrow><mrow><mn>16</mn></mrow></mfrac></math> = 11</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "60",
                    section: "17",
                    question_en: "<p>60. On the tag price, a seller offers a 10% discount and a cashback of ₹250. He still earns 10% profit. If the cost price is ₹1,200, then the tag price (rounded off to 2 decimal places) is ₹______.</p>",
                    question_hi: "<p>60. एक विक्रेता अंकित मूल्य पर 10% छूट और ₹250 का कैशबैक प्रदान करता है। वह अभी भी 10% लाभ अर्जित करता है। यदि क्रय मूल्य ₹1,200 है, तो अंकित मूल्य (दशमलव के दो स्थानों तक पूर्णांकित) ₹______ है।</p>",
                    options_en: [
                        "<p>1447.44</p>",
                        "<p>1777.44</p>",
                        "<p>1744.44</p>",
                        "<p>1774.44</p>"
                    ],
                    options_hi: [
                        "<p>1447.44</p>",
                        "<p>1777.44</p>",
                        "<p>1744.44</p>",
                        "<p>1774.44</p>"
                    ],
                    solution_en: "<p>60.(c)<br>Cost price = 1,200<br>Selling price = 1200 <math display=\"inline\"><mo>&#215;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>11</mn><mn>10</mn></mfrac></math>= 1320<br>Also,<br>marked price <math display=\"inline\"><mo>&#215;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>9</mn><mn>10</mn></mfrac></math>- 250 = Selling price <br>marked price <math display=\"inline\"><mo>&#215;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>9</mn><mn>10</mn></mfrac></math>= 1320 + 250 <br>marked price = 1570 <math display=\"inline\"><mo>&#215;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>10</mn><mn>9</mn></mfrac></math><br>marked price = 1744.44</p>",
                    solution_hi: "<p>60.(c)<br>लागत मूल्य = 1,200<br>विक्रय मूल्य = 1200 <math display=\"inline\"><mo>&#215;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>11</mn><mn>10</mn></mfrac></math> = 1320<br>अब ,<br>अंकित मूल्य <math display=\"inline\"><mo>&#215;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>9</mn><mn>10</mn></mfrac></math>- 250 = विक्रय मूल्य <br>अंकित मूल्य <math display=\"inline\"><mo>&#215;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>9</mn><mn>10</mn></mfrac></math>= 1320 + 250 <br>अंकित मूल्य = 1570 <math display=\"inline\"><mo>&#215;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>10</mn><mn>9</mn></mfrac></math>= 1744.44</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "61",
                    section: "17",
                    question_en: "<p>61. In &Delta;ABC, P is a point on AB such that PB : AP = 3 : 4 and PQ is parallel to AC. If AR and QS are perpendicular to PC and QS = 9 cm, what is the length (in cm) of AR?</p>",
                    question_hi: "<p>61. &Delta;ABC में, AB पर एक बिंदु P इस प्रकार है कि PB : AP=3 :4 है और PQ, AC के समांतर है। यदि AR और QS, PC के लंबवत हैं और QS = 9 cm है, तो AR की लंबाई (cm में) कितनी है?</p>",
                    options_en: [
                        "<p>28</p>",
                        "<p>35</p>",
                        "<p>21</p>",
                        "<p>14</p>"
                    ],
                    options_hi: [
                        "<p>28</p>",
                        "<p>35</p>",
                        "<p>21</p>",
                        "<p>14</p>"
                    ],
                    solution_en: "<p>61.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744431434298.png\" alt=\"rId58\" width=\"182\" height=\"138\"><br>As, PQ<math display=\"inline\"><mi>&#2405;</mi></math> AC, △BQP &sim; △BCA (by AAA rule)<br>Then, <math display=\"inline\"><mfrac><mrow><mi>B</mi><mi>P</mi></mrow><mrow><mi>A</mi><mi>B</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>Q</mi><mi>P</mi></mrow><mrow><mi>A</mi><mi>C</mi></mrow></mfrac></math>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>B</mi><mi>Q</mi></mrow><mrow><mi>B</mi><mi>C</mi></mrow></mfrac></math>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>7</mn></mfrac></math><br>Now, In <math display=\"inline\"><mo>&#9651;</mo></math>SPQ &amp; △RCA <br><math display=\"inline\"><mi>&#8736;</mi></math>QSP = &ang;ARC = 90&deg;<br><math display=\"inline\"><mi>&#8736;</mi></math>QPS = &ang;ACR (alternate angles)<br>So, <math display=\"inline\"><mo>&#9651;</mo></math>SPQ &sim; △RCA (by AAA rule)<br>Then, <math display=\"inline\"><mfrac><mrow><mi>Q</mi><mi>S</mi></mrow><mrow><mi>A</mi><mi>R</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>Q</mi><mi>P</mi></mrow><mrow><mi>A</mi><mi>C</mi></mrow></mfrac></math>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>7</mn></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math>&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>9</mn><mrow><mi>A</mi><mi>R</mi></mrow></mfrac></math>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>7</mn></mfrac></math><br>AR = <math display=\"inline\"><mfrac><mrow><mn>9</mn><mo>&#215;</mo><mn>7</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> = 21 cm</p>",
                    solution_hi: "<p>61.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744431434298.png\" alt=\"rId58\" width=\"182\" height=\"138\"><br>जैसे, PQ<math display=\"inline\"><mi>&#2405;</mi></math> AC, △BQP &sim; △BCA (AAA नियम द्वारा)<br>फिर, <math display=\"inline\"><mfrac><mrow><mi>B</mi><mi>P</mi></mrow><mrow><mi>A</mi><mi>B</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>Q</mi><mi>P</mi></mrow><mrow><mi>A</mi><mi>C</mi></mrow></mfrac></math>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>B</mi><mi>Q</mi></mrow><mrow><mi>B</mi><mi>C</mi></mrow></mfrac></math>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>7</mn></mfrac></math><br>अब, में <math display=\"inline\"><mo>&#9651;</mo></math>SPQ और △RCA <br><math display=\"inline\"><mi>&#8736;</mi></math>QSP = &ang;ARC = 90&deg;<br><math display=\"inline\"><mi>&#8736;</mi></math>QPS = &ang;ACR (वैकल्पिक आंतरिक कोण)<br>तो, <math display=\"inline\"><mo>&#9651;</mo></math>SPQ &sim; △RCA (AAA नियम द्वारा)<br>फिर, <math display=\"inline\"><mfrac><mrow><mi>Q</mi><mi>S</mi></mrow><mrow><mi>A</mi><mi>R</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>Q</mi><mi>P</mi></mrow><mrow><mi>A</mi><mi>C</mi></mrow></mfrac></math>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>7</mn></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>9</mn><mrow><mi>A</mi><mi>R</mi></mrow></mfrac></math>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>7</mn></mfrac></math><br>AR = <math display=\"inline\"><mfrac><mrow><mn>9</mn><mo>&#215;</mo><mn>7</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> = 21 cm</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "62",
                    section: "17",
                    question_en: "<p>62. A train covers a distance of 850 metres in 45 seconds. Travelling at the same speed, how many kilometres will the train cover in an hour?</p>",
                    question_hi: "<p>62. एक ट्रेन 45 सेकंड में 850 मीटर की दूरी तय करती है। समान गति से यात्रा करते हुए ट्रेन एक घंटे में कितने किलोमीटर की दूरी तय करेगी?</p>",
                    options_en: [
                        "<p>67.5</p>",
                        "<p>69</p>",
                        "<p>68</p>",
                        "<p>67.2</p>"
                    ],
                    options_hi: [
                        "<p>67.5</p>",
                        "<p>69</p>",
                        "<p>68</p>",
                        "<p>67.2</p>"
                    ],
                    solution_en: "<p>62.(c)<br>Speed of train = <math display=\"inline\"><mfrac><mrow><mn>850</mn></mrow><mrow><mn>45</mn></mrow></mfrac></math>m/s<br>In km/h = <math display=\"inline\"><mfrac><mrow><mn>850</mn></mrow><mrow><mn>45</mn></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>18</mn><mn>5</mn></mfrac></math>= 34 &times; 2 = 68 km/h<br>Hence, distance cover by train in 1 hour = 68 km</p>",
                    solution_hi: "<p>62.(c)<br>ट्रेन की गति = <math display=\"inline\"><mfrac><mrow><mn>850</mn></mrow><mrow><mn>45</mn></mrow></mfrac></math> मी/से<br>किमी/घंटा में = <math display=\"inline\"><mfrac><mrow><mn>850</mn></mrow><mrow><mn>45</mn></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>18</mn><mn>5</mn></mfrac></math>= 34 &times; 2 = 68 किमी/घंटा<br>अतः, ट्रेन द्वारा 1 घंटे में तय की गई दूरी = 68 किमी</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "63",
                    section: "17",
                    question_en: "<p>63. How many metres of cloth will be required to make a conical tent, the radius of whose base is 21 metres and height is 28 metres. The width of the cloth is 5 metres.<br>(Where &pi; =<math display=\"inline\"><mfrac><mrow><mi>&#160;</mi><mn>22</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math>)</p>",
                    question_hi: "<p>63. एक शंक्वाकार तंबू बनाने में कितने मीटर कपड़े की आवश्यकता होगी, जिसके आधार की त्रिज्या 21 मीटर और ऊँचाई 28 मीटर है। कपड़े की चौड़ाई 5 मीटर है। (जहाँ &pi; =<math display=\"inline\"><mfrac><mrow><mi>&#160;</mi><mn>22</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math>)</p>",
                    options_en: [
                        "<p>470</p>",
                        "<p>462</p>",
                        "<p>456</p>",
                        "<p>478</p>"
                    ],
                    options_hi: [
                        "<p>470</p>",
                        "<p>462</p>",
                        "<p>456</p>",
                        "<p>478</p>"
                    ],
                    solution_en: "<p>63.(b)<br>CSA of conical tent = <math display=\"inline\"><mi>&#960;</mi><mi>r</mi><mi>l</mi></math><br>Let the required length of cloth be x metre<br>Slant height of tent (l) =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mi>r</mi><mn>2</mn></msup><mo>+</mo><msup><mi>h</mi><mn>2</mn></msup></msqrt></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mrow><mo>(</mo><mn>21</mn><mo>)</mo></mrow><mn>2</mn></msup><mo>+</mo><msup><mrow><mo>(</mo><mn>28</mn><mo>)</mo></mrow><mn>2</mn></msup></msqrt></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>1225</mn></msqrt></math>= 35 m<br>Area of cloth = CSA of conical tent<br>5 &times; <math display=\"inline\"><mi>x</mi></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mn>7</mn></mfrac></math>&times; 21 &times; 35<br>5<math display=\"inline\"><mi>x</mi></math> = 2310<br><math display=\"inline\"><mi>x</mi></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2310</mn><mn>5</mn></mfrac></math>= 462</p>",
                    solution_hi: "<p>63.(b)<br>तंबू का CSA = <math display=\"inline\"><mi>&#960;</mi><mi>r</mi><mi>l</mi></math><br>माना कपड़े की आवश्यक लंबाई <math display=\"inline\"><mi>x</mi></math>&nbsp;मीटर है ।&nbsp;<br>तंबू की तिर्यक ऊंचाई(<math display=\"inline\"><mi>l</mi></math>) =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mi>r</mi><mn>2</mn></msup><mo>+</mo><msup><mi>h</mi><mn>2</mn></msup></msqrt></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mrow><mo>(</mo><mn>21</mn><mo>)</mo></mrow><mn>2</mn></msup><mo>+</mo><msup><mrow><mo>(</mo><mn>28</mn><mo>)</mo></mrow><mn>2</mn></msup></msqrt></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>1225</mn></msqrt></math>= 35 मीटर<br>कपड़े का क्षेत्रफल = शंक्वाकार तंबू का वक्र पृष्ठीय क्षेत्रफल<br>5 &times; <math display=\"inline\"><mi>x</mi></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mn>7</mn></mfrac></math>&times; 21 &times; 35<br>5<math display=\"inline\"><mi>x</mi></math> = 2310<br><math display=\"inline\"><mi>x</mi></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2310</mn><mn>5</mn></mfrac></math>= 462</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "64",
                    section: "17",
                    question_en: "<p>64. An amusement park gives an offer to its customers that one person will be given free entry with four other people. What is the effective percentage discount given by the amusement park?</p>",
                    question_hi: "<p>64. एक मनोरंजन पार्क अपने ग्राहकों को एक ऑफर देता है कि एक व्यक्ति को चार अन्य लोगों के साथ मुफ्त प्रवेश दिया जाएगा। मनोरंजन पार्क द्वारा दी गई प्रभावी प्रतिशत छूट कितनी है?</p>",
                    options_en: [
                        "<p>14%</p>",
                        "<p>18%</p>",
                        "<p>20%</p>",
                        "<p>16%</p>"
                    ],
                    options_hi: [
                        "<p>14%</p>",
                        "<p>18%</p>",
                        "<p>20%</p>",
                        "<p>16%</p>"
                    ],
                    solution_en: "<p>64.(c)<br>Discount % = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>1</mn><mo>+</mo><mn>4</mn></mrow></mfrac></math>&times;100 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>100</mn><mn>5</mn></mfrac></math>= 20%</p>",
                    solution_hi: "<p>64.(c)<br>छूट % = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>1</mn><mo>+</mo><mn>4</mn></mrow></mfrac></math> &times; 100 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>100</mn><mn>5</mn></mfrac></math>= 20%</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "65",
                    section: "17",
                    question_en: "<p>65. On a sum, simple interest at the annual rate of 10 percent for 8 years is Rs. 8000. What will be the compound interest on the same sum at the annual rate of 10 percent (compounding annually) for 3 years?</p>",
                    question_hi: "<p>65. किसी धनराशि पर 8 वर्षों के लिए 10 प्रतिशत वार्षिक की दर से साधारण ब्याज 8000 रुपए है। उसी धनराशि पर 3 वर्षों के लिए 10 प्रतिशत वार्षिक की दर (वार्षिक रूप से संयोजित) से चक्रवृद्धि ब्याज कितना होगा?</p>",
                    options_en: [
                        "<p>Rs. 3450</p>",
                        "<p>Rs. 3750</p>",
                        "<p>Rs. 3220</p>",
                        "<p>Rs. 3310</p>"
                    ],
                    options_hi: [
                        "<p>3450 रुपए</p>",
                        "<p>3750 रुपए</p>",
                        "<p>3220 रुपए</p>",
                        "<p>3310 रुपए</p>"
                    ],
                    solution_en: "<p>65.(d)<br>Let principal be 100% <br>8 &times; 10% = 80% ---------------- ₹8000<br>100% ---------------- <math display=\"inline\"><mfrac><mrow><mn>8000</mn></mrow><mrow><mn>80</mn></mrow></mfrac></math> &times; 100 = ₹10,000<br>Time = 3 yrs, rate = 10% = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>10</mn></mrow></mfrac></math><br>Principal&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;Amount<br>&nbsp; &nbsp; &nbsp;10&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;11<br>&nbsp; &nbsp; &nbsp;10&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;11<br>&nbsp; &nbsp; &nbsp;10&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;11<br>___________________<br>&nbsp; &nbsp; 1000&nbsp; &nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; 1331<br>CI = 1331 - 1000 = 331 unit <br>Now, 1000 unit ------------- ₹10,000<br>Then, 331 unit ------------- <math display=\"inline\"><mfrac><mrow><mn>10</mn><mo>,</mo><mn>000</mn></mrow><mrow><mn>1000</mn></mrow></mfrac></math> &times; 331 = ₹3310</p>",
                    solution_hi: "<p>65.(d)<br>मान लीजिए मूलधन 100% है<br>8 &times; 10% = 80% ---------------- ₹8000<br>100% ---------------- <math display=\"inline\"><mfrac><mrow><mn>8000</mn></mrow><mrow><mn>80</mn></mrow></mfrac></math> &times; 100 = ₹10,000<br>समय = 3 वर्ष, दर = 10% = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>10</mn></mrow></mfrac></math><br>&nbsp; मूलधन&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;राशि <br>&nbsp; &nbsp; &nbsp;10&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;11<br>&nbsp; &nbsp; &nbsp;10&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;11<br>&nbsp; &nbsp; &nbsp;10&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;11<br>&nbsp;___________________<br>&nbsp; &nbsp; 1000&nbsp; &nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; 1331<br>चक्रवृद्धि ब्याज = 1331-1000 = 331 इकाई<br>अब, 1000 इकाई ------------- ₹10,000<br>अतः , 331 इकाई ------------- <math display=\"inline\"><mfrac><mrow><mn>10</mn><mo>,</mo><mn>000</mn></mrow><mrow><mn>1000</mn></mrow></mfrac></math> &times; 331 = ₹3310</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "66",
                    section: "17",
                    question_en: "<p>66. The following graph shows the sale of few items of a garment company for three months.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744431434429.png\" alt=\"rId59\" width=\"436\" height=\"264\"> <br>What is the percentage of the sale of the least selling item in the three months over the total sale of the company?</p>",
                    question_hi: "<p>66. निम्नलिखित ग्राफ तीन महीने के लिए एक परिधान कंपनी की कुछ वस्तुओं की बिक्री को दर्शाता है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744431434717.png\" alt=\"rId60\" width=\"411\" height=\"248\"> <br>कंपनी की कुल बिक्री की तुलना में तीन महीनों में सबसे कम बिकने वाली वस्तु की बिक्री का प्रतिशत कितना है?</p>",
                    options_en: [
                        "<p>28%</p>",
                        "<p>25%</p>",
                        "<p>16%</p>",
                        "<p>20%</p>"
                    ],
                    options_hi: [
                        "<p>28%</p>",
                        "<p>25%</p>",
                        "<p>16%</p>",
                        "<p>20%</p>"
                    ],
                    solution_en: "<p>66.(c)<br>Total sale of the company in 3 months = (6 + 4 + 7 + 5 + 3) + (7 + 6 + 10 + 9 + 8) + (13 + 10 + 11 + 14 + 12) = 125 mn units <br>After checking the graph, least selling item in three months is T-shirt = (4 + 6 + 10) = 20 mn unit<br>Hence, required % = <math display=\"inline\"><mfrac><mrow><mn>20</mn></mrow><mrow><mn>125</mn></mrow></mfrac></math> &times; 100 = 16%</p>",
                    solution_hi: "<p>66.(c)<br>3 महीने में कंपनी की कुल बिक्री = (6 + 4 + 7 + 5 + 3) + (7 + 6 + 10 + 9 + 8) + (13 + 10 + 11 + 14 + 12) = 125 मिलियन यूनिट <br>ग्राफ़ की जाँच करने के बाद, तीन महीनों में सबसे कम बिकने वाली वस्तु टी-शर्ट है = (4 + 6 + 10) = 20 मिलियन यूनिट <br>अत: आवश्यक % = <math display=\"inline\"><mfrac><mrow><mn>20</mn></mrow><mrow><mn>125</mn></mrow></mfrac></math> &times; 100 = 16%</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "67",
                    section: "17",
                    question_en: "<p>67. If p - q = 9, and p&sup2; + q&sup2; = 127. Find the value of pq .</p>",
                    question_hi: "<p>67. यदि p - q = 9, और p&sup2; + q&sup2; = 127 है, तो pq का मान ज्ञात कीजिए।</p>",
                    options_en: [
                        "<p>12</p>",
                        "<p>15</p>",
                        "<p>23</p>",
                        "<p>27</p>"
                    ],
                    options_hi: [
                        "<p>12</p>",
                        "<p>15</p>",
                        "<p>23</p>",
                        "<p>27</p>"
                    ],
                    solution_en: "<p>67.(c)<br>(p - q<math display=\"inline\"><msup><mrow><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup></math> = p<sup>2 </sup>+ q<sup>2 </sup>- 2pq<br>81 = 127 - 2pq<br>2pq = 46 <br>pq = <math display=\"inline\"><mfrac><mrow><mn>46</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = 23</p>",
                    solution_hi: "<p>67.(c)<br>(p - q<math display=\"inline\"><msup><mrow><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup></math> = p<sup>2 </sup>+ q<sup>2 </sup>- 2pq<br>81 = 127 - 2pq<br>2pq = 46 <br>pq = <math display=\"inline\"><mfrac><mrow><mn>46</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = 23</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "68",
                    section: "17",
                    question_en: "<p>68. What will be the greatest four digit number which can be completely divided by 15, 20, 25 and 30?</p>",
                    question_hi: "<p>68. चार अंकों की वह सबसे बड़ी संख्या कौन-सी होगी, जिसे 15, 20, 25 और 30 से पूर्णतः विभाजित किया जा सकता है?</p>",
                    options_en: [
                        "<p>9900</p>",
                        "<p>9300</p>",
                        "<p>9700</p>",
                        "<p>9930</p>"
                    ],
                    options_hi: [
                        "<p>9900</p>",
                        "<p>9300</p>",
                        "<p>9700</p>",
                        "<p>9930</p>"
                    ],
                    solution_en: "<p>68.(a)<br>Greatest four digit number = 9999<br>LCM(15 , 20 , 25 , 30) = 300<br>Now, <math display=\"inline\"><mfrac><mrow><mn>9999</mn></mrow><mrow><mn>300</mn></mrow></mfrac></math> = 99(remainder)<br>Hence, required number = 9999 - 99 = 9900</p>",
                    solution_hi: "<p>68.(a)<br>चार अंकों की सबसे बड़ी संख्या = 9999<br>ल.स.प. (15 , 20 , 25 , 30) = 300<br>अब, <math display=\"inline\"><mfrac><mrow><mn>9999</mn></mrow><mrow><mn>300</mn></mrow></mfrac></math> = 99(शेष)<br>अतः, आवश्यक संख्या = 9999 - 99 = 9900</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "69",
                    section: "17",
                    question_en: "<p>69. R gives <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math> of the toffees he has with him to S. If now R has k% of the toffies with S, then find the value of k.</p>",
                    question_hi: "<p>69. R अपने पास मौजूद टॉफ़ी का <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math> भाग S को देता है। यदि अब R के पास S के पास मौजूद टॉफ़ी का k% है, तो k का मान ज्ञात कीजिए।</p>",
                    options_en: [
                        "<p><math display=\"inline\"><mfrac><mrow><mn>500</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>200</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>",
                        "<p>140</p>",
                        "<p>200</p>"
                    ],
                    options_hi: [
                        "<p><math display=\"inline\"><mfrac><mrow><mn>500</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>200</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>",
                        "<p>140</p>",
                        "<p>200</p>"
                    ],
                    solution_en: "<p>69.(a)<br>Let R has x toffees <br>Toffees given by R to S = <math display=\"inline\"><mfrac><mrow><mn>3</mn><mi>x</mi></mrow><mrow><mn>8</mn></mrow></mfrac></math><br>Then, now R has toffees = x - <math display=\"inline\"><mfrac><mrow><mn>3</mn><mi>x</mi></mrow><mrow><mn>8</mn></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mi>x</mi></mrow><mn>8</mn></mfrac></math><br>Hence, k% = <math display=\"inline\"><mfrac><mrow><mfrac><mrow><mn>5</mn><mi>x</mi></mrow><mrow><mn>8</mn></mrow></mfrac></mrow><mrow><mfrac><mrow><mn>3</mn><mi>x</mi></mrow><mrow><mn>8</mn></mrow></mfrac></mrow></mfrac></math> &times; 100 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>3</mn></mfrac></math>&times; 100 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>500</mn><mn>3</mn></mfrac></math></p>",
                    solution_hi: "<p>69.(a)<br>माना R के पास x टॉफियाँ हैं <br>R द्वारा S को दी गई टॉफ़ी = <math display=\"inline\"><mfrac><mrow><mn>3</mn><mi>x</mi></mrow><mrow><mn>8</mn></mrow></mfrac></math><br>फिर, अब R के पास टॉफ़ी = x - <math display=\"inline\"><mfrac><mrow><mn>3</mn><mi>x</mi></mrow><mrow><mn>8</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mi>x</mi></mrow><mn>8</mn></mfrac></math><br>अत:, k% = <math display=\"inline\"><mfrac><mrow><mfrac><mrow><mn>5</mn><mi>x</mi></mrow><mrow><mn>8</mn></mrow></mfrac></mrow><mrow><mfrac><mrow><mn>3</mn><mi>x</mi></mrow><mrow><mn>8</mn></mrow></mfrac></mrow></mfrac></math> &times; 100 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>3</mn></mfrac></math>&times; 100 =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>500</mn><mn>3</mn></mfrac></math></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "70",
                    section: "17",
                    question_en: "<p>70. The value of (<math display=\"inline\"><mfrac><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mi>e</mi><mi>c</mi><mi>A</mi></mrow><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mi>e</mi><mi>c</mi><mi>A</mi><mo>-</mo><mn>1</mn><mi>&#160;</mi></mrow></mfrac></math> +&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>cos</mi><mi>e</mi><mi>c</mi><mi>A</mi></mrow><mrow><mi>cos</mi><mi>e</mi><mi>c</mi><mi>A</mi><mo>+</mo><mn>1</mn></mrow></mfrac></math>) (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>s</mi><mi>e</mi><mi>c</mi><mi>A</mi></mrow><mrow><mi>s</mi><mi>e</mi><mi>c</mi><mi>A</mi><mo>-</mo><mn>1</mn></mrow></mfrac></math>+&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>s</mi><mi>e</mi><mi>c</mi><mi>A</mi></mrow><mrow><mi>s</mi><mi>e</mi><mi>c</mi><mi>A</mi><mo>+</mo><mn>1</mn></mrow></mfrac></math>)<sup>-1</sup> , when A = 60&deg; is:</p>",
                    question_hi: "<p>70. A = 60&deg; होने पर (<math display=\"inline\"><mfrac><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mi>e</mi><mi>c</mi><mi>A</mi></mrow><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mi>e</mi><mi>c</mi><mi>A</mi><mo>-</mo><mn>1</mn><mi>&#160;</mi></mrow></mfrac></math> +&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>cos</mi><mi>e</mi><mi>c</mi><mi>A</mi></mrow><mrow><mi>cos</mi><mi>e</mi><mi>c</mi><mi>A</mi><mo>+</mo><mn>1</mn></mrow></mfrac></math>) (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>s</mi><mi>e</mi><mi>c</mi><mi>A</mi></mrow><mrow><mi>s</mi><mi>e</mi><mi>c</mi><mi>A</mi><mo>-</mo><mn>1</mn></mrow></mfrac></math>+&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>s</mi><mi>e</mi><mi>c</mi><mi>A</mi></mrow><mrow><mi>s</mi><mi>e</mi><mi>c</mi><mi>A</mi><mo>+</mo><mn>1</mn></mrow></mfrac></math>)<sup>-1</sup>का मान क्या होगा?</p>",
                    options_en: [
                        "<p>4</p>",
                        "<p>3</p>",
                        "<p>1</p>",
                        "<p>2</p>"
                    ],
                    options_hi: [
                        "<p>4</p>",
                        "<p>3</p>",
                        "<p>1</p>",
                        "<p>2</p>"
                    ],
                    solution_en: "<p>70.(b) <br>(<math display=\"inline\"><mfrac><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mi>e</mi><mi>c</mi><mi>A</mi></mrow><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mi>e</mi><mi>c</mi><mi>A</mi><mo>-</mo><mn>1</mn><mi>&#160;</mi></mrow></mfrac></math> +&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>cos</mi><mi>e</mi><mi>c</mi><mi>A</mi></mrow><mrow><mi>cos</mi><mi>e</mi><mi>c</mi><mi>A</mi><mo>+</mo><mn>1</mn></mrow></mfrac></math>) (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>s</mi><mi>e</mi><mi>c</mi><mi>A</mi></mrow><mrow><mi>s</mi><mi>e</mi><mi>c</mi><mi>A</mi><mo>-</mo><mn>1</mn></mrow></mfrac></math>+&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>s</mi><mi>e</mi><mi>c</mi><mi>A</mi></mrow><mrow><mi>s</mi><mi>e</mi><mi>c</mi><mi>A</mi><mo>+</mo><mn>1</mn></mrow></mfrac></math>)<sup>-1</sup><br>= <math display=\"inline\"><mfrac><mrow><mfrac><mrow><msup><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mi>e</mi><mi>c</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>A</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>e</mi><mi>c</mi><mi>A</mi><mi>&#160;</mi><mi>&#160;</mi><mo>+</mo><msup><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mi>e</mi><mi>c</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>A</mi><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>e</mi><mi>c</mi><mi>A</mi><mi>&#160;</mi></mrow><mrow><msup><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mi>e</mi><mi>c</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>A</mi><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><msup><mrow><mi>&#160;</mi><mn>1</mn></mrow><mrow><mn>2</mn></mrow></msup></mrow></mfrac></mrow><mrow><mfrac><mrow><msup><mrow><mi>s</mi><mi>e</mi><mi>c</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>A</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>s</mi><mi>e</mi><mi>c</mi><mi>A</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><msup><mrow><mi>s</mi><mi>e</mi><mi>c</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>A</mi><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mi>s</mi><mi>e</mi><mi>c</mi><mi>A</mi><mi>&#160;</mi><mi>&#160;</mi><mi>&#160;</mi></mrow><mrow><msup><mrow><mi>s</mi><mi>e</mi><mi>c</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>A</mi><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><msup><mrow><mi>&#160;</mi><mn>1</mn></mrow><mrow><mn>2</mn></mrow></msup><mi>&#160;</mi></mrow></mfrac></mrow></mfrac></math><br>= <math display=\"inline\"><mfrac><mrow><mn>2</mn><mi>&#160;</mi><msup><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mi>e</mi><mi>c</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>A</mi></mrow><mrow><msup><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mi>e</mi><mi>c</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>A</mi><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><msup><mrow><mi>&#160;</mi><mn>1</mn></mrow><mrow></mrow></msup></mrow></mfrac></math> &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>s</mi><mi>e</mi><msup><mi>c</mi><mn>2</mn></msup><mi>A</mi><mo>-</mo><mn>1</mn></mrow><mrow><mn>2</mn><mi>s</mi><mi>e</mi><msup><mi>c</mi><mn>2</mn></msup><mi>A</mi></mrow></mfrac></math><br>= <math display=\"inline\"><mfrac><mrow><msup><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mi>e</mi><mi>c</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>A</mi></mrow><mrow><msup><mrow><mi>c</mi><mi>o</mi><mi>t</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>A</mi></mrow></mfrac></math> &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi>tan</mi><mn>2</mn></msup><mi>A</mi></mrow><mrow><mi>s</mi><mi>e</mi><msup><mi>c</mi><mn>2</mn></msup><mi>A</mi></mrow></mfrac></math><br>Now , A = 60&deg;<br>= <math display=\"inline\"><mfrac><mrow><mfrac><mrow><mn>4</mn></mrow><mrow><mn>3</mn></mrow></mfrac></mrow><mrow><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>4</mn></mfrac></math>= 4 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>4</mn></mfrac></math>= 3</p>",
                    solution_hi: "<p>70.(b) <br>(<math display=\"inline\"><mfrac><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mi>e</mi><mi>c</mi><mi>A</mi></mrow><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mi>e</mi><mi>c</mi><mi>A</mi><mo>-</mo><mn>1</mn><mi>&#160;</mi></mrow></mfrac></math> +&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>cos</mi><mi>e</mi><mi>c</mi><mi>A</mi></mrow><mrow><mi>cos</mi><mi>e</mi><mi>c</mi><mi>A</mi><mo>+</mo><mn>1</mn></mrow></mfrac></math>) (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>s</mi><mi>e</mi><mi>c</mi><mi>A</mi></mrow><mrow><mi>s</mi><mi>e</mi><mi>c</mi><mi>A</mi><mo>-</mo><mn>1</mn></mrow></mfrac></math>+&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>s</mi><mi>e</mi><mi>c</mi><mi>A</mi></mrow><mrow><mi>s</mi><mi>e</mi><mi>c</mi><mi>A</mi><mo>+</mo><mn>1</mn></mrow></mfrac></math>)<sup>-1</sup><br>= <math display=\"inline\"><mfrac><mrow><mfrac><mrow><msup><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mi>e</mi><mi>c</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>A</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>e</mi><mi>c</mi><mi>A</mi><mi>&#160;</mi><mi>&#160;</mi><mo>+</mo><msup><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mi>e</mi><mi>c</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>A</mi><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>e</mi><mi>c</mi><mi>A</mi><mi>&#160;</mi></mrow><mrow><msup><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mi>e</mi><mi>c</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>A</mi><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><msup><mrow><mi>&#160;</mi><mn>1</mn></mrow><mrow><mn>2</mn></mrow></msup></mrow></mfrac></mrow><mrow><mfrac><mrow><msup><mrow><mi>s</mi><mi>e</mi><mi>c</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>A</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>s</mi><mi>e</mi><mi>c</mi><mi>A</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><msup><mrow><mi>s</mi><mi>e</mi><mi>c</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>A</mi><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mi>s</mi><mi>e</mi><mi>c</mi><mi>A</mi><mi>&#160;</mi><mi>&#160;</mi><mi>&#160;</mi></mrow><mrow><msup><mrow><mi>s</mi><mi>e</mi><mi>c</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>A</mi><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><msup><mrow><mi>&#160;</mi><mn>1</mn></mrow><mrow><mn>2</mn></mrow></msup><mi>&#160;</mi></mrow></mfrac></mrow></mfrac></math><br>= <math display=\"inline\"><mfrac><mrow><mn>2</mn><mi>&#160;</mi><msup><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mi>e</mi><mi>c</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>A</mi></mrow><mrow><msup><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mi>e</mi><mi>c</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>A</mi><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><msup><mrow><mi>&#160;</mi><mn>1</mn></mrow><mrow></mrow></msup></mrow></mfrac></math> &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>s</mi><mi>e</mi><msup><mi>c</mi><mn>2</mn></msup><mi>A</mi><mo>-</mo><mn>1</mn></mrow><mrow><mn>2</mn><mi>s</mi><mi>e</mi><msup><mi>c</mi><mn>2</mn></msup><mi>A</mi></mrow></mfrac></math><br>= <math display=\"inline\"><mfrac><mrow><msup><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mi>e</mi><mi>c</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>A</mi></mrow><mrow><msup><mrow><mi>c</mi><mi>o</mi><mi>t</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>A</mi></mrow></mfrac></math> &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi>tan</mi><mn>2</mn></msup><mi>A</mi></mrow><mrow><mi>s</mi><mi>e</mi><msup><mi>c</mi><mn>2</mn></msup><mi>A</mi></mrow></mfrac></math><br>अब , A = 60&deg;<br>= <math display=\"inline\"><mfrac><mrow><mfrac><mrow><mn>4</mn></mrow><mrow><mn>3</mn></mrow></mfrac></mrow><mrow><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>4</mn></mfrac></math>= 4 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>4</mn></mfrac></math>= 3</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "71",
                    section: "17",
                    question_en: "<p>71. Simplify the given expression.<br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>4</mn><mo>&#215;</mo><mn>5</mn><mo>&#247;</mo><mn>4</mn><mi>&#160;</mi><mi>o</mi><mi>f</mi><mi>&#160;</mi><mn>5</mn><mo>&#215;</mo><mn>4</mn><mo>&#247;</mo><mo>(</mo><mn>6</mn><mo>+</mo><mn>6</mn><mo>&#215;</mo><mn>6</mn><mo>&#247;</mo><mn>6</mn><mi>&#160;</mi><mi>o</mi><mi>f</mi><mi>&#160;</mi><mn>6</mn><mo>-</mo><mn>6</mn><mo>&#247;</mo><mn>6</mn><mo>&#215;</mo><mn>6</mn><mo>)</mo></math></p>",
                    question_hi: "<p>71. दिए गए व्यंजक को सरल कीजिए।<br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>4</mn><mo>&#215;</mo><mn>5</mn><mo>&#247;</mo><mn>4</mn><mi>&#160;</mi><mi>o</mi><mi>f</mi><mi>&#160;</mi><mn>5</mn><mo>&#215;</mo><mn>4</mn><mo>&#247;</mo><mo>(</mo><mn>6</mn><mo>+</mo><mn>6</mn><mo>&#215;</mo><mn>6</mn><mo>&#247;</mo><mn>6</mn><mi>&#160;</mi><mi>o</mi><mi>f</mi><mi>&#160;</mi><mn>6</mn><mo>-</mo><mn>6</mn><mo>&#247;</mo><mn>6</mn><mo>&#215;</mo><mn>6</mn><mo>)</mo></math></p>",
                    options_en: [
                        "<p>2</p>",
                        "<p>12</p>",
                        "<p>20</p>",
                        "<p>4</p>"
                    ],
                    options_hi: [
                        "<p>2</p>",
                        "<p>12</p>",
                        "<p>20</p>",
                        "<p>4</p>"
                    ],
                    solution_en: "<p>71.(d)<br><math style=\"font-family: Verdana;\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>4</mn><mo>&#215;</mo><mn>5</mn><mo>&#247;</mo><mn>4</mn><mi>&#160;</mi><mi>o</mi><mi>f</mi><mi>&#160;</mi><mn>5</mn><mo>&#215;</mo><mi>&#160;</mi><mn>4</mn><mo>&#247;</mo><mo>(</mo><mn>6</mn><mo>+</mo><mn>6</mn><mo>&#215;</mo><mn>6</mn><mo>&#247;</mo><mn>6</mn><mi>&#160;</mi><mi>o</mi><mi>f</mi><mi>&#160;</mi><mn>6</mn><mo>-</mo><mn>6</mn><mo>&#247;</mo><mn>6</mn><mo>&#215;</mo><mi>&#160;</mi><mn>6</mn><mo>)</mo></math><br>&rArr; <math style=\"font-family: Verdana;\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>4</mn><mo>&#215;</mo><mn>5</mn><mo>&#247;</mo><mn>20</mn><mo>&#215;</mo><mi>&#160;</mi><mn>4</mn><mo>&#247;</mo><mo>(</mo><mn>6</mn><mo>+</mo><mn>6</mn><mo>&#215;</mo><mn>6</mn><mo>&#247;</mo><mn>36</mn><mo>-</mo><mn>6</mn><mo>&#247;</mo><mn>6</mn><mo>&#215;</mo><mi>&#160;</mi><mn>6</mn><mo>)</mo></math><br>&rArr; <math style=\"font-family: Verdana;\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>1</mn><mo>&#215;</mo><mi>&#160;</mi><mn>4</mn><mo>&#247;</mo><mo>(</mo><mn>6</mn><mo>+</mo><mn>1</mn><mo>-</mo><mn>6</mn><mo>)</mo></math><br>&rArr; <math display=\"inline\"><mn>1</mn><mo>&#215;</mo><mi>&#160;</mi><mn>4</mn><mo>&#247;</mo><mo>(</mo><mn>1</mn><mo>)</mo></math> = 4</p>",
                    solution_hi: "<p>71.(d)<br><math style=\"font-family: Verdana;\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>4</mn><mo>&#215;</mo><mn>5</mn><mo>&#247;</mo><mn>4</mn><mi>&#160;</mi><mi>o</mi><mi>f</mi><mi>&#160;</mi><mn>5</mn><mo>&#215;</mo><mi>&#160;</mi><mn>4</mn><mo>&#247;</mo><mo>(</mo><mn>6</mn><mo>+</mo><mn>6</mn><mo>&#215;</mo><mn>6</mn><mo>&#247;</mo><mn>6</mn><mi>&#160;</mi><mi>o</mi><mi>f</mi><mi>&#160;</mi><mn>6</mn><mo>-</mo><mn>6</mn><mo>&#247;</mo><mn>6</mn><mo>&#215;</mo><mi>&#160;</mi><mn>6</mn><mo>)</mo></math><br>&rArr; <math style=\"font-family: Verdana;\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>4</mn><mo>&#215;</mo><mn>5</mn><mo>&#247;</mo><mn>20</mn><mo>&#215;</mo><mi>&#160;</mi><mn>4</mn><mo>&#247;</mo><mo>(</mo><mn>6</mn><mo>+</mo><mn>6</mn><mo>&#215;</mo><mn>6</mn><mo>&#247;</mo><mn>36</mn><mo>-</mo><mn>6</mn><mo>&#247;</mo><mn>6</mn><mo>&#215;</mo><mi>&#160;</mi><mn>6</mn><mo>)</mo></math><br>&rArr; <math style=\"font-family: Verdana;\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>1</mn><mo>&#215;</mo><mi>&#160;</mi><mn>4</mn><mo>&#247;</mo><mo>(</mo><mn>6</mn><mo>+</mo><mn>1</mn><mo>-</mo><mn>6</mn><mo>)</mo></math><br>&rArr; <math display=\"inline\"><mn>1</mn><mo>&#215;</mo><mi>&#160;</mi><mn>4</mn><mo>&#247;</mo><mo>(</mo><mn>1</mn><mo>)</mo></math> = 4</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "72",
                    section: "17",
                    question_en: "<p>72. A retailer marks up his goods by 130% and offers 25% discount. What will be the selling price (in ₹) if the cost price is ₹1600 ?</p>",
                    question_hi: "<p>72. एक खुदरा विक्रेता अपने सामान का मूल्य 130% बढ़ाकर अंकित करता है और 25% की छूट देता है। यदि क्रय मूल्य ₹ 1,600 है, तो विक्रय मूल्य (₹ में) क्या होगा ?</p>",
                    options_en: [
                        "<p>3680</p>",
                        "<p>2760</p>",
                        "<p>1765</p>",
                        "<p>1980</p>"
                    ],
                    options_hi: [
                        "<p>3680</p>",
                        "<p>2760</p>",
                        "<p>1765</p>",
                        "<p>1980</p>"
                    ],
                    solution_en: "<p>72.(b)<br>Selling price = 1600 <math display=\"inline\"><mo>&#215;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>23</mn><mn>10</mn></mfrac></math>&times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>4</mn></mfrac></math>= 2760</p>",
                    solution_hi: "<p>72.(b)<br>विक्रय मूल्य = 1600 <math display=\"inline\"><mo>&#215;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>23</mn><mn>10</mn></mfrac></math>&times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>4</mn></mfrac></math>= 2760</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "73",
                    section: "17",
                    question_en: "<p>73. The distance between the parallel sides of a trapezium is 18 cm. If the area of the trapezium is 1188 cm<sup>2</sup> , then what is the sum of the lengths of the parallel sides?</p>",
                    question_hi: "<p>73. एक समलंब चतुर्भुज की समांतर भुजाओं के बीच की दूरी 18 सेमी है। यदि समलंब चतुर्भुज का क्षेत्रफल 1188 सेमी<sup>2 </sup>है, तो समांतर भुजाओं की लंबाई का योग क्या है?</p>",
                    options_en: [
                        "<p>150 cm</p>",
                        "<p>115 cm</p>",
                        "<p>126 cm</p>",
                        "<p>132 cm</p>"
                    ],
                    options_hi: [
                        "<p>150 सेमी</p>",
                        "<p>115 सेमी</p>",
                        "<p>126 सेमी</p>",
                        "<p>132 सेमी</p>"
                    ],
                    solution_en: "<p>73.(d)<br>Area of trapezium = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> &times; sum of parallel sides &times; height<br>1188 = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> &times; sum of parallel sides &times; 18<br>sum of parallel sides = 132 cm</p>",
                    solution_hi: "<p>73.(d)<br>समलम्ब चतुर्भुज का क्षेत्रफल = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> &times; समांतर भुजाओं का योग &times; ऊँचाई <br>1188 = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> &times; समांतर भुजाओं का योग &times; 18<br>समांतर भुजाओं का योग = 132 cm</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "74",
                    section: "17",
                    question_en: "74. The sum of five consecutive numbers is 80. Find the largest number.",
                    question_hi: "74. पाँच क्रमागत संख्याओं का योग 80 है। सबसे बड़ी संख्या ज्ञात कीजिए।",
                    options_en: [
                        " 18",
                        " 15",
                        " 19",
                        " 14"
                    ],
                    options_hi: [
                        " 18",
                        " 15",
                        " 19",
                        " 14"
                    ],
                    solution_en: "74.(a) Let the five consecutive numbers are x - 2 , x -1 , x , x + 1 , x + 2<br />According to question<br />x - 2 +  x -1 + x +  x + 1 + x + 2   = 80<br /> <math display=\"inline\"><mo>⇒</mo></math>   5x = 80     ⇒ x = 16<br />Largest number = x + 2 = 16 + 2 = 18      ",
                    solution_hi: "74.(a) मान लीजिए कि पाँच क्रमागत संख्याएँ  x - 2 , x -1 , x , x + 1 , x + 2 हैं<br />प्रश्न के अनुसार , <br /> x - 2 +  x -1 + x +  x + 1 + x + 2   = 80<br /><math display=\"inline\"><mo>⇒</mo></math>   5x = 80     ⇒ x = 16<br />सबसे बड़ी संख्या  = x + 2 = 16 + 2 = 18  ",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "75",
                    section: "7",
                    question_en: "<p>75. The weight of the Five friends are 67 kg, 85 kg, 70 kg, 90 kg, and 103 kg. Find the average of their weights in kg.</p>",
                    question_hi: "<p>75. पांचों दोस्तों का भार 67 kg, 85 kg, 70 kg, 90 kg और 103 kg है। उनके भार का औसत (kg में) ज्ञात कीजिए।</p>",
                    options_en: [
                        "<p>90</p>",
                        "<p>80</p>",
                        "<p>83</p>",
                        "<p>87</p>"
                    ],
                    options_hi: [
                        "<p>90</p>",
                        "<p>80</p>",
                        "<p>83</p>",
                        "<p>87</p>"
                    ],
                    solution_en: "<p>75.(c)<br>Average weight = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>67</mn><mo>+</mo><mn>85</mn><mo>+</mo><mn>70</mn><mo>+</mo><mn>90</mn><mo>+</mo><mn>103</mn></mrow><mn>5</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>415</mn><mn>5</mn></mfrac></math>= 83 kg</p>",
                    solution_hi: "<p>75.(c)<br>औसत भार = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>67</mn><mo>+</mo><mn>85</mn><mo>+</mo><mn>70</mn><mo>+</mo><mn>90</mn><mo>+</mo><mn>103</mn></mrow><mn>5</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>415</mn><mn>5</mn></mfrac></math>= 83 kg</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "76",
                    section: "7",
                    question_en: "76. Identify the segment in the sentence, which contains the grammatical error.<br />Several governments payed lip service to the women\'s cause.",
                    question_hi: "76. Identify the segment in the sentence, which contains the grammatical error.<br />Several governments payed lip service to the women\'s cause.",
                    options_en: [
                        " Several governments payed ",
                        " lip service",
                        " to the ",
                        " women\'s cause"
                    ],
                    options_hi: [
                        " Several governments payed ",
                        " lip service",
                        " to the ",
                        " women\'s cause"
                    ],
                    solution_en: "<p>76.(a) Several governments payed<br>Incorrect 2<sup>nd</sup> form of the verb &lsquo;pay&rsquo; is used. The correct form is &lsquo;Paid&rsquo;. Hence, Several governments paid is the most appropriate answer.</p>",
                    solution_hi: "<p>76.(a) Several governments payed<br>&lsquo;Pay&rsquo; Incorrect है , क्योंकि Verb का 2<sup>nd</sup> form &lsquo;Paid&rsquo; है। इसलिए, Several governments paid सबसे उपयुक्त है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "77",
                    section: "7",
                    question_en: "77.  Identify the word that is misspelt.",
                    question_hi: "77.  Identify the word that is misspelt.",
                    options_en: [
                        " Comedian",
                        " Communication",
                        " poultry",
                        " Phenomenan"
                    ],
                    options_hi: [
                        " Comedian",
                        " Communication",
                        " poultry",
                        " Phenomenan"
                    ],
                    solution_en: "77.(d) ‘Phenomenon’ is the correct spelling.",
                    solution_hi: "77.(d) ‘Phenomenon’ सही spelling है। ",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "78",
                    section: "7",
                    question_en: "<p>78. Identify the best way to improve the underlined part of the given sentence. If there is no improvement required, select &lsquo;no improvement&rsquo;.<br>The little boy<strong> </strong><span style=\"text-decoration: underline;\"><strong>kick the ball.</strong></span></p>",
                    question_hi: "<p>78. Identify the best way to improve the underlined part of the given sentence. If there is no improvement required, select &lsquo;no improvement&rsquo;.<br>The little boy <span style=\"text-decoration: underline;\"><strong>kick the ball.</strong></span></p>",
                    options_en: [
                        "<p>have been kicked by the ball</p>",
                        "<p>has been kicked the ball</p>",
                        "<p>was kicked the ball</p>",
                        "<p>kicked the ball</p>"
                    ],
                    options_hi: [
                        "<p>have been kicked by the ball</p>",
                        "<p>has been kicked the ball</p>",
                        "<p>was kicked the ball</p>",
                        "<p>kicked the ball</p>"
                    ],
                    solution_en: "<p>78.(d) kicked the ball<br>The later part of the given sentence must be in the simple past tense(V<sub>2</sub>). Hence, &lsquo;kicked(V<sub>2</sub>) the ball&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>78.(d) kicked the ball<br>दिए गए वाक्य का बाद वाला भाग simple past tense(V<sub>2</sub>) में होना चाहिए। इसलिए, &lsquo;kicked(V<sub>2</sub>) the ball&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "79",
                    section: "7",
                    question_en: "79. Find a word that is the synonym of the given word.<br />Inception",
                    question_hi: "79. Find a word that is the synonym of the given word.<br />Inception",
                    options_en: [
                        " inundation ",
                        " incarnation",
                        " genesis ",
                        " demise"
                    ],
                    options_hi: [
                        " inundation ",
                        " incarnation",
                        " genesis ",
                        " demise"
                    ],
                    solution_en: "79.(c) genesis <br />Inception- Beginning or the establishment of an organisation or institution etc.<br />Inundation- An overwhelming abundance of people or things.<br />Incarnation- Rebirth<br />Genesis- The beginning or the origin of something<br />Demise- Death",
                    solution_hi: "79.(c) genesis<br />Inception- शुरुआत<br />Inundation- बाढ़<br />Incarnation- अवतार<br />Genesis- उत्पत्ति<br />Demise- मृत्यु",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "80",
                    section: "7",
                    question_en: "<p>80. Select the option that expresses the given sentence in active voice.<br>The contract was grabbed by the biggest telecom company in India.</p>",
                    question_hi: "<p>80. Select the option that expresses the given sentence in active voice.<br>The contract was grabbed by the biggest telecom company in India.</p>",
                    options_en: [
                        "<p>The biggest telecom company in India grabbed the contract.</p>",
                        "<p>The biggest telecom company in India grabs the contract.</p>",
                        "<p>The biggest telecom company in India will grab the contract.</p>",
                        "<p>The biggest telecom company in India has grabbed the contract.</p>"
                    ],
                    options_hi: [
                        "<p>The biggest telecom company in India grabbed the contract.</p>",
                        "<p>The biggest telecom company in India grabs the contract.</p>",
                        "<p>The biggest telecom company in India will grab the contract.</p>",
                        "<p>The biggest telecom company in India has grabbed the contract.</p>"
                    ],
                    solution_en: "<p>80.(a) The biggest telecom company in India grabbed the contract. (Correct)<br>(b) The biggest telecom company in India <span style=\"text-decoration: underline;\">grabs</span> the contract. (Incorrect Tense)<br>(c) The biggest telecom company in India <span style=\"text-decoration: underline;\">will grab</span> the contract. (Incorrect Tense)<br>(d) The biggest telecom company in India <span style=\"text-decoration: underline;\">has grabbed</span> the contract. (Incorrect Tense)</p>",
                    solution_hi: "<p>80.(a) The biggest telecom company in India grabbed the contract. (Correct)<br>(b) The biggest telecom company in India <span style=\"text-decoration: underline;\">grabs</span> the contract. (गलत Tense)<br>(c) The biggest telecom company in India <span style=\"text-decoration: underline;\">will grab</span> the contract. (गलत Tense)<br>(d) The biggest telecom company in India <span style=\"text-decoration: underline;\">has grabbed</span> the contract. (गलत Tense)</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "81",
                    section: "7",
                    question_en: "<p>81. Identify the segment in the sentence, which contains the grammatical error.<br>When I reached home the neighbours took my mother to the hospital fearing that I might take too much time.</p>",
                    question_hi: "<p>81. Identify the segment in the sentence, which contains the grammatical error.<br>When I reached home the neighbours took my mother to the hospital fearing that I might take too much time.</p>",
                    options_en: [
                        "<p>When I reached home</p>",
                        "<p>the neighbours took my mother</p>",
                        "<p>that I might take too much time</p>",
                        "<p>to the hospital fearing</p>"
                    ],
                    options_hi: [
                        "<p>When I reached home</p>",
                        "<p>the neighbours took my mother</p>",
                        "<p>that I might take too much time</p>",
                        "<p>to the hospital fearing</p>"
                    ],
                    solution_en: "<p>81.(b) the neighbours took my mother<br>If two actions took place in the past then the 1st action must be in the <span style=\"text-decoration: underline;\">Past perfect tense</span>(Had + V<sub>3</sub>) and the 2nd action must be in the <span style=\"text-decoration: underline;\">Simple Past tense</span>(V<sub>2</sub>). Hence, &lsquo;took&rsquo; will be replaced by &lsquo;had taken&rsquo; to make the given sentence grammatically correct.</p>",
                    solution_hi: "<p>81.(b) the neighbours took my mother <br>यदि दो actions past में हुए हैं तो 1st action <span style=\"text-decoration: underline;\">Past perfect tense</span> (Had +V<sub>3</sub>) में होना चाहिए और 2nd action <span style=\"text-decoration: underline;\">Simple Past tense</span> (V<sub>2</sub>) में होना चाहिए। इसलिए, दिए गए sentence को सही करने के लिए \'took\' को \'had taken\' से बदल दिया जाएगा।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "82",
                    section: "7",
                    question_en: "82. Given below are four jumbled sentences. Pick the option that gives their correct order.<br />(A) A strong wind began to blow.<br />(B) One evening, Taro and his parents were sitting in a corner of their hut.<br />(C ) Suddenly Taro’s father said, “I wish I had a cup of tea.”<br />(D) It whistled through the cracks of the hut and everyone felt very cold.",
                    question_hi: "82. Given below are four jumbled sentences. Pick the option that gives their correct order.<br />(A) A strong wind began to blow.<br />(B) One evening, Taro and his parents were sitting in a corner of their hut.<br />(C ) Suddenly Taro’s father said, “I wish I had a cup of tea.”<br />(D) It whistled through the cracks of the hut and everyone felt very cold.",
                    options_en: [
                        " ACBD",
                        " BADC",
                        " BDCA",
                        " CBDA"
                    ],
                    options_hi: [
                        " ACBD",
                        " BADC",
                        " BDCA",
                        " CBDA"
                    ],
                    solution_en: "82 (b) BADC<br />Sentence B will be the starting line as it contains the main idea of the parajumble i.e. one evening Taro and his parents were sitting in their hut. Sentence A states that a strong wind began to blow. So, A will follow B. Further, Sentence D states that the wind started coming inside and they felt cold and Sentence C states that suddenly his father wished for a cup of tea . So, C will follow D.Going through the options, option (b) BADC has the correct sequence.",
                    solution_hi: "82 (b) BADC<br />Sentence B प्रारंभिक line होगी क्योंकि इसमें parajumble का मुख्य विचार है यानी एक शाम Taro और उसके माता-पिता अपनी झोपड़ी में बैठे थे। Sentence A कहता है कि तेज हवा चलने लगी। तो, B के बाद A आएगा।आगे, sentence D कहता है कि हवा अंदर आने लगी और उन्हें ठंड महसूस हुई और sentence C कहता है कि अचानक उसके पिता को एक कप चाय की इच्छा हुई। इसलिए D के बाद C आएगा। Options के माध्यम से, option (b) BADC में सही क्रम है।",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "83",
                    section: "7",
                    question_en: "<p>83. Choose the word opposite in the meaning to the word underlined in the given sentence.<br>The Board of Directors showered <span style=\"text-decoration: underline;\"><strong>encomium</strong></span> on the Chairman for his liberal policies.</p>",
                    question_hi: "<p>83. Choose the word opposite in the meaning to the word underlined in the given sentence.<br>The Board of Directors showered <span style=\"text-decoration: underline;\"><strong>encomium</strong></span> on the Chairman for his liberal policies.</p>",
                    options_en: [
                        "<p>denunciation</p>",
                        "<p>generosity</p>",
                        "<p>tribute</p>",
                        "<p>praise</p>"
                    ],
                    options_hi: [
                        "<p>denunciation</p>",
                        "<p>generosity</p>",
                        "<p>tribute</p>",
                        "<p>praise</p>"
                    ],
                    solution_en: "<p>83. (a) Denunciation- Public condemnation of someone or something.<br>Encomium- A speech or piece of writing that praises someone or something highly.<br>Generosity- The quality of being kind and generous.<br>Tribute- An act, statement, or gift that is intended to show gratitude, respect, or admiration.<br>Praise- Express warm approval or admiration of.</p>",
                    solution_hi: "<p>83. (a) Denunciation-निंदा- Public condemnation of someone or something.<br>Encomium-प्रशंसा भाषण- A speech or piece of writing that praises someone or something highly.<br>Generosity-उदारता- The quality of being kind and generous.<br>Tribute-सम्मान- An act, statement, or gift that is intended to show gratitude, respect, or admiration.<br>Praise-प्रशंसा- Express warm approval or admiration of.</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "84",
                    section: "7",
                    question_en: "84.  Identify the word that is misspelt.",
                    question_hi: "84.  Identify the word that is misspelt.",
                    options_en: [
                        " Deceit",
                        " Labrinth",
                        " Laborious",
                        " runner"
                    ],
                    options_hi: [
                        " Deceit",
                        " Labrinth",
                        " Laborious",
                        " runner"
                    ],
                    solution_en: "84.(b) Labrinth<br />‘Labyrinth’ is the correct spelling",
                    solution_hi: "84.(b) Labrinth<br />\'Labyrinth\' सही spelling है",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "85",
                    section: "7",
                    question_en: "<p>85. Identify the best way to improve the underlined part of the given sentence. If there is no improvement required, select &lsquo;no improvement&rsquo;. <br>Sania Nehwal&rsquo;s growing stature in sports arena had led her name recommended for the Khel Ratna Award</p>",
                    question_hi: "<p>85. Identify the best way to improve the underlined part of the given sentence. If there is no improvement required, select &lsquo;no improvement&rsquo;. <br>Sania Nehwal&rsquo;s growing stature in sports arena had led her name recommended for the Khel Ratna Award</p>",
                    options_en: [
                        "<p>Being recommended</p>",
                        "<p>To be recommended</p>",
                        "<p>Been recommended</p>",
                        "<p>No improvement</p>"
                    ],
                    options_hi: [
                        "<p>Being recommended</p>",
                        "<p>To be recommended</p>",
                        "<p>Been recommended</p>",
                        "<p>No improvement</p>"
                    ],
                    solution_en: "<p>85. (b) To be recommended <br>The given sentence will have an infinitive(with the preposition &lsquo;to&rsquo;) to form the correct passive voice structure(to be + V<sub>3</sub>). Hence, &lsquo;to be recommended(V<sub>3</sub>)&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>85. (b) To be recommended<br>सही passive voice structure(to be +V<sub>3</sub>) बनाने के लिए दिए गए sentence में एक infinitive (with the preposition &lsquo;to) होगा। इसलिए, \'to be recommended (V<sub>3</sub>)\' सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "86",
                    section: "7",
                    question_en: "<p>86. Select the most appropriate meaning of the given idiom.<br>Get itchy feet</p>",
                    question_hi: "<p>86. Select the most appropriate meaning of the given idiom.<br>Get itchy feet</p>",
                    options_en: [
                        "<p>Judge something primarily on appearance</p>",
                        "<p>Join a popular trend or activity</p>",
                        "<p>Learn something by memorising it without a thought to what is being learnt</p>",
                        "<p>To start to want to travel or do something different</p>"
                    ],
                    options_hi: [
                        "<p>Judge something primarily on appearance</p>",
                        "<p>Join a popular trend or activity</p>",
                        "<p>Learn something by memorising it without a thought to what is being learnt</p>",
                        "<p>To start to want to travel or do something different</p>"
                    ],
                    solution_en: "<p>86.(d) <strong>Get itchy feet</strong>- to start to want to travel or do something different.<br>E.g.- After a few months in one place, she started to get itchy feet and wanted to travel again.</p>",
                    solution_hi: "<p>86.(d) <strong>Get itchy feet</strong> - to start to want to travel or do something different./यात्रा करने या कुछ अलग करने की इच्छा होना।<br>E.g.- After a few months in one place, she started to get itchy feet and wanted to travel again.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "87",
                    section: "7",
                    question_en: "<p>87. Choose the best alternative which best expresses the meaning of the idiom/ phrase given below.<br>Take to one&rsquo;s beds</p>",
                    question_hi: "<p>87. Choose the best alternative which best expresses the meaning of the idiom/ phrase given below.<br>Take to one&rsquo;s beds</p>",
                    options_en: [
                        "<p>to measure up to one&rsquo;s standard</p>",
                        "<p>to shake in fear</p>",
                        "<p>to fall sick</p>",
                        "<p>to run slowly</p>"
                    ],
                    options_hi: [
                        "<p>to measure up to one&rsquo;s standard</p>",
                        "<p>to shake in fear</p>",
                        "<p>to fall sick</p>",
                        "<p>to run slowly</p>"
                    ],
                    solution_en: "<p>87.(c) to fall sick</p>",
                    solution_hi: "<p>87.(c) to fall sick/बीमार पड़ना</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "88",
                    section: "7",
                    question_en: "<p>88. Select the option that expresses the given sentence in active voice.<br>By whom was grammar taught to you?</p>",
                    question_hi: "<p>88. Select the option that expresses the given sentence in active voice.<br>By whom was grammar taught to you?</p>",
                    options_en: [
                        "<p>Did he teach you grammar?</p>",
                        "<p>Who had taught you grammar?</p>",
                        "<p>You were taught grammar by whom?</p>",
                        "<p>Who taught you grammar?</p>"
                    ],
                    options_hi: [
                        "<p>Did he teach you grammar?</p>",
                        "<p>Who had taught you grammar?</p>",
                        "<p>You were taught grammar by whom?</p>",
                        "<p>Who taught you grammar?</p>"
                    ],
                    solution_en: "<p>88.(d) Who taught you grammar? (Correct)<br>(a) Did he teach you grammar? (Incorrect Sentence Structure)<br>(b) Who <span style=\"text-decoration: underline;\">had taught </span>you grammar? (Incorrect Tense)<br>(c) You were taught grammar by whom? (Incorrect Sentence Structure)</p>",
                    solution_hi: "<p>88.(d) Who taught you grammar? (Correct)<br>(a) Did he teach you grammar? (गलत Sentence Structure)<br>(b) Who <span style=\"text-decoration: underline;\">had taught</span> you grammar? (गलत Tense)<br>(c) You were taught grammar by whom? (गलत Sentence Structure)</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "89",
                    section: "7",
                    question_en: "<p>89. Select the option that can be used as a one-word substitute for the given group of words. <br>A mythological animal with one horn on its forehead</p>",
                    question_hi: "<p>89. Select the option that can be used as a one-word substitute for the given group of words. <br>A mythological animal with one horn on its forehead</p>",
                    options_en: [
                        "<p>Unific</p>",
                        "<p>Unipod</p>",
                        "<p>Unicameral</p>",
                        "<p>Unicorn</p>"
                    ],
                    options_hi: [
                        "<p>Unific</p>",
                        "<p>Unipod</p>",
                        "<p>Unicameral</p>",
                        "<p>Unicorn</p>"
                    ],
                    solution_en: "<p>89.(d) <strong>Unicorn-</strong> A mythological animal with one horn on its forehead<br><strong>Unific-</strong> tending to produce unity.<br><strong>Unipod-</strong> a one-legged support for a camera.<br><strong>Unicameral-</strong> having or consisting of a single legislative chamber.</p>",
                    solution_hi: "<p>89.(d) <strong>Unicorn</strong> (एक सींग का जानवर) - A mythological animal with one horn on its forehead<br><strong>Unific</strong> (एकीकृत) - tending to produce unity.<br><strong>Unipod</strong> (एकपदीय) - a one-legged support for a camera.<br><strong>Unicameral</strong> (एकसदनीय) - having or consisting of a single legislative chamber.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "90",
                    section: "7",
                    question_en: "<p>90. Select the option that can be used as a one-word substitute for the underlined words.<br>I do not like him because he <span style=\"text-decoration: underline;\">always disapproves the things or opinions of others.</span></p>",
                    question_hi: "<p>90. Select the option that can be used as a one-word substitute for the underlined words.<br>I do not like him because he <span style=\"text-decoration: underline;\">always disapproves the things or opinions of others.</span></p>",
                    options_en: [
                        "<p>is jealous of other people</p>",
                        "<p>is critical of others</p>",
                        "<p>is insensitive of others feelings</p>",
                        "<p>is a hate monger</p>"
                    ],
                    options_hi: [
                        "<p>is jealous of other people</p>",
                        "<p>is critical of others</p>",
                        "<p>is insensitive of others feelings</p>",
                        "<p>is a hate monger</p>"
                    ],
                    solution_en: "<p>90.(b) always disapproves the things or opinions of others- critical of others<br>&lsquo;Critical&rsquo; means expressing adverse or disapproving comments or judgements. Hence, option (b) is the most appropriate answer.</p>",
                    solution_hi: "<p>90.(b) always disapproves the things or opinions of others- critical of others<br>&lsquo;Critical&rsquo; का अर्थ है adverse या disapproving comments या judgements व्यक्त करना। इसलिए, option (b) सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "91",
                    section: "7",
                    question_en: "91. Given below are four jumbled sentences. Pick the option that gives their correct order.<br />(A) It was given to those who were born into high-status social groups of a particular race.<br />(B) All women, men of lower classes and the poor and working people, in general, were not allowed to vote<br />(C ) In the beginning, the vote was restricted.<br />(D) Wealthy men who owned property also had voting rights.",
                    question_hi: "91. Given below are four jumbled sentences. Pick the option that gives their correct order.<br />(A) It was given to those who were born into high-status social groups of a particular race.<br />(B) All women, men of lower classes and the poor and working people, in general, were not allowed to vote<br />(C ) In the beginning, the vote was restricted.<br />(D) Wealthy men who owned property also had voting rights.",
                    options_en: [
                        " CADB",
                        " ADBC",
                        " BCDA",
                        " DCBA"
                    ],
                    options_hi: [
                        " CADB",
                        " ADBC",
                        " BCDA",
                        " DCBA"
                    ],
                    solution_en: "91.(a) CADB<br />Sentence C will be the starting line as it contains the main idea of the parajumble i.e. in the beginning, the right to vote was restricted to some people . Sentence A states that only some high-status social groups were allowed to vote . So, A will follow C. Further, Sentence D states that wealthy men were also allowed to vote  and Sentence B mentions the people who were not allowed to vote. So, B will follow D. Going through the options, option (a) CADB has the correct sequence.",
                    solution_hi: "91(a) CADB<br />Sentence C प्रारंभिक line होगी क्योंकि इसमें parajumble का मुख्य विचार है यानी शुरुआत में वोट देने का अधिकार कुछ लोगों तक ही सीमित था। Sentence A बताता है कि केवल कुछ उच्च-दर्जे वाले सामाजिक समूहों को मतदान करने की अनुमति थी। तो,C के बाद A  आएगा। आगे, sentence D कहता है कि धनी पुरुषों को भी मतदान करने की अनुमति दी गई थी और sentence B में उन लोगों का उल्लेख है जिन्हें वोट देने की अनुमति नहीं थी। तो D के बाद B आएगा  । Options के माध्यम से, option (a) CADB में सही क्रम है।",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "92",
                    section: "7",
                    question_en: "92. Select the most appropriate option to fill in the blank.<br />The teacher kept stopping her online lesson ____ to check if her students were following it. ",
                    question_hi: "92. Select the most appropriate option to fill in the blank.<br />The teacher kept stopping her online lesson ____ to check if her students were following it. ",
                    options_en: [
                        " occasionally   ",
                        " indifferently ",
                        " impartially ",
                        " unresponsively "
                    ],
                    options_hi: [
                        " occasionally   ",
                        " indifferently ",
                        " impartially ",
                        " unresponsively "
                    ],
                    solution_en: "92.(a) Occasionally.<br />‘Occasionally’ means not often or regular. The given sentence states that the teacher kept stopping her online lesson occasionally to check if her students were following it. Hence, ‘occasionally’ is the most appropriate answer.",
                    solution_hi: "92.(a) Occasionally. <br />‘Occasionally’  का अर्थ है- जो नियमित नहीं है। दिए गए वाक्य में कहा गया है कि शिक्षिका समय-समय पर अपने ऑनलाइन पाठ को यह जांचने के लिए रोकती रही कि उसके छात्र उसकी बात समझ  पा रहे हैं या नहीं  । इसलिए,  ‘occasionally’ सबसे उपयुक्त उत्तर है।<br />              ",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "93",
                    section: "7",
                    question_en: "93. Select the most appropriate Synonym of the given word.<br />Lofty  ",
                    question_hi: "93. Select the most appropriate Synonym of the given word<br />Lofty  ",
                    options_en: [
                        " humble ",
                        " plain ",
                        " towering  ",
                        " silent "
                    ],
                    options_hi: [
                        " humble ",
                        " plain ",
                        " towering  ",
                        " silent"
                    ],
                    solution_en: "93.(c) Towering- extremely tall, especially in comparison with the surroundings.<br />Lofty-very tall and impressive.<br />Humble- not thinking that you are better or more important than other people, not proud.<br />Plain- easy to see, hear or understand, clear.<br />Silent- where there is no noise, very quiet.",
                    solution_hi: "93.(c) Towering-ऊंचा- extremely tall, especially in comparison with the surroundings.<br />Lofty-बुलंद-very tall and impressive.<br />Humble-विनम्र- not thinking that you are better or more important than other people, not proud.<br />Plain-मैदान- easy to see, hear or understand, clear.<br />Silent-चुपचाप- where there is no noise, very quiet.<br />      ",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "94",
                    section: "7",
                    question_en: "<p>94. Choose the word opposite in meaning to the given word.<br>Apathy</p>",
                    question_hi: "<p>94. Choose the word opposite in meaning to the given word.<br>Apathy</p>",
                    options_en: [
                        "<p>indifference</p>",
                        "<p>empathy</p>",
                        "<p>eagerness</p>",
                        "<p>antipathy</p>"
                    ],
                    options_hi: [
                        "<p>indifference</p>",
                        "<p>empathy</p>",
                        "<p>eagerness</p>",
                        "<p>antipathy</p>"
                    ],
                    solution_en: "<p>94.(b) Empathy<br>Apathy - a lack of emotion or emotional expressiveness<br>Indifference - lack of interest, concern, or sympathy.<br>Empathy - feelings of pity and sorrow for someone else\'s misfortune while also having gone through it.<br>Eagerness - enthusiasm to do or to have something; keenness.<br>Antipathy - a deep-seated feeling of aversion.</p>",
                    solution_hi: "<p>94.(b) Empathy<br>Apathy - उदासीनता<br>Indifference - अपक्षपात<br>Empathy - सहानुभूति<br>Eagerness- उत्सुकता<br>Antipathy - घृणा</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "95",
                    section: "7",
                    question_en: "95. Select the most appropriate option to fill in the blank.<br />Man_______ be happy if his basic needs are not satisfied. ",
                    question_hi: "95. Select the most appropriate option to fill in the blank.<br />Man_______ be happy if his basic needs are not satisfied. ",
                    options_en: [
                        "  must  ",
                        "  doesn’t  ",
                        "  can’t   ",
                        "  will"
                    ],
                    options_hi: [
                        "  must  ",
                        "  doesn’t  ",
                        "  can’t   ",
                        "  will                          "
                    ],
                    solution_en: "95.(c)  can’t <br />The given sentence states that Man cannot be happy if his basic needs are not satisfied. So, ‘can’t’ is the correct answer.",
                    solution_hi: "95.(c)  can’t <br />दिए गए वाक्य में कहा गया है कि मनुष्य तब तक खुश नहीं रह सकता जब तक कि उसकी बुनियादी जरूरतें पूरी नहीं होती हैं। अतः, \'can’t’\' सही उत्तर है।<br />                    ",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "96",
                    section: "7",
                    question_en: "<p>96. <strong>Cloze test:-</strong><br>(96) ______ changes in science and technology lead to modernisation of technology as well as upgradation of knowledge. In order to upgrade or modernize technology, management must (97) ______ employees to accept new technology. (98) ______ training of staff becomes necessary to update their knowledge and to (99) ______ their skills. This is possible only (100) ______ effective communication between the management and the employees. <br>Select the most appropriate option to fill in the blank no. 96</p>",
                    question_hi: "<p>96. <strong>Cloze test:-</strong><br>(96) ______ changes in science and technology lead to modernisation of technology as well as upgradation of knowledge. In order to upgrade or modernize technology, management must (97) ______ employees to accept new technology. (98) ______ training of staff becomes necessary to update their knowledge and to (99) ______ their skills. This is possible only (100) ______ effective communication between the management and the employees.<br>Select the most appropriate option to fill in the blank no. 96</p>",
                    options_en: [
                        "<p>No</p>",
                        "<p>Slow</p>",
                        "<p>Ultimate</p>",
                        "<p>Rapid</p>"
                    ],
                    options_hi: [
                        "<p>No</p>",
                        "<p>Slow</p>",
                        "<p>Ultimate</p>",
                        "<p>Rapid</p>"
                    ],
                    solution_en: "<p>96.(d) &lsquo;Rapid&rsquo; means happening very quickly. The given passage talks about the changes in science and technology that are happening very quickly. Hence, &lsquo;rapid&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>96.(d) \'Rapid\' का अर्थ है बहुत जल्दी घटित होना। दिया गया गद्यांश विज्ञान और प्रौद्योगिकी में होने वाले परिवर्तनों के बारे में बात करता है जो बहुत तेज़ी से हो रहे हैं। इसलिए, \'rapid\' सबसे उपयुक्त उत्तर है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "97",
                    section: "7",
                    question_en: "<p>97. <strong>Cloze test:-</strong><br>(96) ______ changes in science and technology lead to modernisation of technology as well as upgradation of knowledge. In order to upgrade or modernize technology, management must (97) ______ employees to accept new technology. (98) ______ training of staff becomes necessary to update their knowledge and to (99) ______ their skills. This is possible only (100) ______ effective communication between the management and the employees.<br>Select the most appropriate option to fill in the blank no. 97</p>",
                    question_hi: "<p>97. <strong>Cloze test:-</strong><br>(96) ______ changes in science and technology lead to modernisation of technology as well as upgradation of knowledge. In order to upgrade or modernize technology, management must (97) ______ employees to accept new technology. (98) ______ training of staff becomes necessary to update their knowledge and to (99) ______ their skills. This is possible only (100) ______ effective communication between the management and the employees.<br>Select the most appropriate option to fill in the blank no. 97</p>",
                    options_en: [
                        "<p>dissuade</p>",
                        "<p>discourage</p>",
                        "<p>persuade</p>",
                        "<p>deactivate</p>"
                    ],
                    options_hi: [
                        "<p>dissuade</p>",
                        "<p>discourage</p>",
                        "<p>persuade</p>",
                        "<p>deactivate</p>"
                    ],
                    solution_en: "<p>97.(c) Persuade&rsquo; means to make somebody believe something. The given passage states that management must make employees believe to accept new technology in order to upgrade or modernize technology. Hence, &lsquo;persuade&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>97.(c) &lsquo;Persuade\' का अर्थ है किसी को कुछ विश्वास दिलाना। दिए गए passage में कहा गया है कि प्रबंधन को प्रौद्योगिकी के उन्नयन या आधुनिकीकरण के लिए कर्मचारियों को नई तकनीक को स्वीकार करने के लिए विश्वास दिलाना चाहिए। इसलिए, \'persuade\' सबसे उपयुक्त उत्तर है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "98",
                    section: "7",
                    question_en: "<p>98. <strong>Cloze test:-</strong><br>(96) ______ changes in science and technology lead to modernisation of technology as well as upgradation of knowledge. In order to upgrade or modernize technology, management must (97) ______ employees to accept new technology. (98) ______ training of staff becomes necessary to update their knowledge and to (99) ______ their skills. This is possible only (100) ______ effective communication between the management and the employees.<br>Select the most appropriate option to fill in the blank no. 98</p>",
                    question_hi: "<p>98. <strong>Cloze test:-</strong><br>(96) ______ changes in science and technology lead to modernisation of technology as well as upgradation of knowledge. In order to upgrade or modernize technology, management must (97) ______ employees to accept new technology. (98) ______ training of staff becomes necessary to update their knowledge and to (99) ______ their skills. This is possible only (100) ______ effective communication between the management and the employees.<br>Select the most appropriate option to fill in the blank no. 98</p>",
                    options_en: [
                        "<p>Intermittent</p>",
                        "<p>Irregular</p>",
                        "<p>Regular</p>",
                        "<p>Improper</p>"
                    ],
                    options_hi: [
                        "<p>Intermittent</p>",
                        "<p>Irregular</p>",
                        "<p>Regular</p>",
                        "<p>Improper</p>"
                    ],
                    solution_en: "<p>98.(c) &lsquo;Regular&rsquo; means done or happening often. The given passage states that regular training of staff becomes necessary to update their knowledge. Hence, &lsquo;regular&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>98.(c) \'Regular\' का अर्थ है अक्सर घटित होना। दिए गए गद्यांश में कहा गया है कि कर्मचारियों का नियमित प्रशिक्षण उनके ज्ञान को अद्यतन(update) करने के लिए आवश्यक हो जाता है। इसलिए, \'regular\' सबसे उपयुक्त उत्तर है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "99",
                    section: "7",
                    question_en: "<p>99. <strong>Cloze test:-</strong><br>(96) ______ changes in science and technology lead to modernisation of technology as well as upgradation of knowledge. In order to upgrade or modernize technology, management must (97) ______ employees to accept new technology. (98) ______ training of staff becomes necessary to update their knowledge and to (99) ______ their skills. This is possible only (100) ______ effective communication between the management and the employees.<br>Select the most appropriate option to fill in the blank no. 99</p>",
                    question_hi: "<p>99. <strong>Cloze test:-</strong><br>(96) ______ changes in science and technology lead to modernisation of technology as well as upgradation of knowledge. In order to upgrade or modernize technology, management must (97) ______ employees to accept new technology. (98) ______ training of staff becomes necessary to update their knowledge and to (99) ______ their skills. This is possible only (100) ______ effective communication between the management and the employees.<br>Select the most appropriate option to fill in the blank no. 99</p>",
                    options_en: [
                        "<p>hamper</p>",
                        "<p>enhance</p>",
                        "<p>imitate</p>",
                        "<p>decrease</p>"
                    ],
                    options_hi: [
                        "<p>hamper</p>",
                        "<p>enhance</p>",
                        "<p>imitate</p>",
                        "<p>decrease</p>"
                    ],
                    solution_en: "<p>99.(b) &lsquo;Enhance&rsquo; means to improve something or to make something look better. The given passage states that regular training of staff becomes necessary to update their knowledge and to enhance their skills. Hence, &lsquo;enhance&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>99.(b) &lsquo;Enhance&rsquo; का अर्थ है कुछ सुधारना या कुछ बेहतर दिखाना। दिए गए passage में कहा गया है कि कर्मचारियों का नियमित प्रशिक्षण उनके ज्ञान को अद्यतन(update) करने और उनके कौशल को बढ़ाने के लिए आवश्यक हो जाता है। इसलिए, \'enhance\' सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "100",
                    section: "misc",
                    question_en: "<p>100. <strong>Cloze test:-</strong><br>(96) ______ changes in science and technology lead to modernisation of technology as well as upgradation of knowledge. In order to upgrade or modernize technology, management must (97) ______ employees to accept new technology. (98) ______ training of staff becomes necessary to update their knowledge and to (99) ______ their skills. This is possible only (100) ______ effective communication between the management and the employees.<br>Select the most appropriate option to fill in the blank no. 100</p>",
                    question_hi: "<p>100. <strong>Cloze test:-</strong><br>(96) ______ changes in science and technology lead to modernisation of technology as well as upgradation of knowledge. In order to upgrade or modernize technology, management must (97) ______ employees to accept new technology. (98) ______ training of staff becomes necessary to update their knowledge and to (99) ______ their skills. This is possible only (100) ______ effective communication between the management and the employees.<br>Select the most appropriate option to fill in the blank no. 100</p>",
                    options_en: [
                        "<p>by</p>",
                        "<p>through</p>",
                        "<p>throughout</p>",
                        "<p>with</p>"
                    ],
                    options_hi: [
                        "<p>by</p>",
                        "<p>through</p>",
                        "<p>throughout</p>",
                        "<p>with</p>"
                    ],
                    solution_en: "<p>100.(b) &lsquo;Through&rsquo; means with the help of something. The given passage states that this is possible with the help of effective communication between the management and the employees. Hence, &lsquo;through&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>100.(b) \'Through\' का अर्थ है किसी चीज की मदद से। दिए गए passage में कहा गया है कि यह प्रबंधन और कर्मचारियों के बीच प्रभावी संचार (communication) की मदद से संभव है। इसलिए, \'through\' सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'question-box d-flex align-items-center justify-content-center border rounded';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.question-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            const html = `
                <div class="card shadow-sm mb-4">
                    <div class="card-body">
                        <div class="question-text mb-4">
                            <div class="en">${question.question_en}</div>
                            <div class="hi" style="display:none">${question.question_hi}</div>
                        </div>
                        <div class="options">
                            ${['a', 'b', 'c', 'd'].map((opt, i) => `
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="radio" name="q${index}" 
                                        id="opt-${index}-${opt}" value="${opt}" 
                                        ${answers[question.id] === opt ? 'checked' : ''}
                                        onclick="selectAnswer('${question.id}', '${opt}')">
                                    <label class="form-check-label w-100 py-2 px-3 rounded hover-bg-light" for="opt-${index}-${opt}">
                                        <div class="en">${question.options_en[i]}</div>
                                        <div class="hi" style="display:none">${question.options_hi[i]}</div>
                                    </label>
                                </div>
                            `).join('')}
                        </div>
                        ${submitted ? `
                            <div class="alert ${answers[question.id] === question.correct ? 'alert-success' : 'alert-danger'} mt-3">
                                ${answers[question.id] ? (answers[question.id] === question.correct ? 'Correct!' : 'Incorrect!') : 'Not attempted'}
                                <div class="mt-2">
                                    <strong>Solution:</strong>
                                    <div class="en">${question.solution_en}</div>
                                    <div class="hi" style="display:none">${question.solution_hi}</div>
                                </div>
                            </div>
                        ` : ''}
                    </div>
                </div>
            `;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // If in mobile view, close the menu
            if (window.innerWidth < 768) {
                toggleMenu(false);
            }
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                document.getElementById(`box-${index}`).classList.add('attempted');
            }
        }
        // Navigate to previous question
        function prevQuestion() {
            if (currentQuestion > 0) {
                showQuestion(currentQuestion - 1);
            }
        }
        // Navigate to next question
        function nextQuestion() {
            if (currentQuestion < questions.length - 1) {
                showQuestion(currentQuestion + 1);
            }
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) {
                showQuestion(section.start);
            }
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update button text
            document.getElementById('lang-btn').innerHTML = currentLang === 'en' ? 
                '<i class="fas fa-language"></i> हिंदी' : 
                '<i class="fas fa-language"></i> English';
        }
        // Toggle menu for mobile
        function toggleMenu(show) {
            const nav = document.getElementById('question-nav');
            const content = document.getElementById('question-content');
            if (show === undefined) {
                show = nav.style.right === '-100%' || nav.style.right === '';
            }
            if (show) {
                nav.style.right = '0';
                if (window.innerWidth >= 768) {
                    content.style.marginRight = '320px';
                }
            } else {
                nav.style.right = window.innerWidth < 768 ? '-100%' : '0';
                content.style.marginRight = window.innerWidth < 768 ? '0' : '320px';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
            document.getElementById('total-count').textContent = questions.length;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.remove('attempted');
                    box.classList.add('bg-secondary', 'text-white');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.remove('attempted');
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.remove('attempted');
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            resultsModal.show();
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check-circle me-1"></i> Submitted';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            resultsModal.hide();
            showQuestion(0);
        }
    </script>
</body>
</html>