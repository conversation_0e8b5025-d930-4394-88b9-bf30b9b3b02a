<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">12:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 25</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">25</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 12 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["7"] = {
                name: "English",
                start: 0,
                end: 23
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="7">English</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 24,
                end: 24
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "7",
                    question_en: "<p>1. Select the most appropriate option to substitute the underlined segment in the following sentence.</p>\\r\\n<p>With the harsh words of the boss, Srujan felt sad, but his colleague\'s words <span style=\"text-decoration: underline;\">created insult to injury.</span></p>\\n",
                    question_hi: "<p>1. Select the most appropriate option to substitute the underlined segment in the following sentence.</p>\\r\\n<p>With the harsh words of the boss, Srujan felt sad, but his colleague\'s words <span style=\"text-decoration: underline;\">created insult to injury.</span></p>\\n",
                    options_en: ["<p>attached insult to injury</p>\\n", "<p>provoked insult to injury</p>\\n", 
                                "<p>added injury to insult</p>\\n", "<p>added insult to injury</p>\\n"],
                    options_hi: ["<p>attached insult to injury</p>\\n", "<p>provoked insult to injury</p>\\n",
                                "<p>added injury to insult</p>\\n", "<p>added insult to injury</p>\\n"],
                    solution_en: "<p>1.(d) Added insult to injury - made the situation even worse.</p>\\n",
                    solution_hi: "<p>1.(d) Added insult to injury - <span style=\"font-family: Palanquin Dark;\">made the situation even worse./ &#2325;&#2367;&#2360;&#2368; &#2330;&#2368;&#2332;&#2364; &#2325;&#2379; &#2324;&#2352; &#2309;&#2343;&#2367;&#2325; &#2348;&#2342;&#2340;&#2352; &#2348;&#2344;&#2366;&#2344;&#2366;&#2404; </span></p>\\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "7",
                    question_en: "<p>2. <span style=\"font-family: Palanquin Dark;\">Select the option that correctly expresses the following sentence in passive voice. </span></p>\\r\\n<p><span style=\"font-family: Palanquin Dark;\">The child will recite the poem.</span></p>\\n",
                    question_hi: "<p>2. <span style=\"font-family: Palanquin Dark;\">Select the option that correctly expresses the following sentence in passive voice. </span></p>\\r\\n<p><span style=\"font-family: Palanquin Dark;\">The child will recite the poem.</span></p>\\n",
                    options_en: ["<p>By child poem will be recited.</p>\\n", "<p>The poem will be recited by the child.</p>\\n", 
                                "<p>The poem is recited by the child.</p>\\n", "<p>The poem will recite by the child.</p>\\n"],
                    options_hi: ["<p>By child poem will be recited.</p>\\n", "<p>The poem will be recited by the child.</p>\\n",
                                "<p>The poem is recited by the child.</p>\\n", "<p>The poem will recite by the child.</p>\\n"],
                    solution_en: "<p>2.(b)&nbsp; <span style=\"font-weight: 400;\">The poem will be recited by the child.(Correct)</span></p>\\r\\n<p><span style=\"font-weight: 400;\">(a) By child poem will be recited.(Incorrect Sentence Structure)</span></p>\\r\\n<p><span style=\"font-weight: 400;\">(c) The poem </span><span style=\"text-decoration: underline;\"><span style=\"font-weight: 400;\">is</span></span><span style=\"font-weight: 400;\"> recited by the child.(Incorrect Tense)</span></p>\\r\\n<p><span style=\"font-weight: 400;\">(d) The poem </span><span style=\"text-decoration: underline;\"><span style=\"font-weight: 400;\">will recite</span></span><span style=\"font-weight: 400;\"> by the child.(Incorrect Verb)</span></p>\\n",
                    solution_hi: "<p>2.(b) <span style=\"font-weight: 400;\">The poem will be recited by the child.(Correct)</span></p>\\r\\n<p><span style=\"font-weight: 400;\">(a) By child poem will be recited.(&#2327;&#2354;&#2340;&nbsp; Sentence Structure)</span></p>\\r\\n<p><span style=\"font-weight: 400;\">(c) The poem </span><span style=\"text-decoration: underline;\"><span style=\"font-weight: 400;\">is</span></span><span style=\"font-weight: 400;\"> recited by the child.(&#2327;&#2354;&#2340; Tense)</span></p>\\r\\n<p><span style=\"font-weight: 400;\">(d) The poem </span><span style=\"text-decoration: underline;\"><span style=\"font-weight: 400;\">will recite</span></span><span style=\"font-weight: 400;\"> by the child.(&#2327;&#2354;&#2340; Verb)</span></p>\\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "7",
                    question_en: " <p>3. </span><span style=\"font-family:Palanquin Dark\">Parts of the following sentence have been given as options. Select the option that contains an error.</span></p> <p><span style=\"font-family:Palanquin Dark\">You need to acknowledge the itinary and accommodation details of your journey as per the given calendar.</span></p>",
                    question_hi: " <p>3. </span><span style=\"font-family:Palanquin Dark\">Parts of the following sentence have been given as options. Select the option that contains an error.</span></p> <p><span style=\"font-family:Palanquin Dark\">You need to acknowledge the itinary and accommodation details of your journey as per the given calendar.</span></p>",
                    options_en: [" <p> calendar</span></p>", " <p> accommodation</span></p>", 
                                " <p> acknowledge</span></p>", " <p> itinary</span></p>"],
                    options_hi: [" <p> calendar</span></p>", " <p> accommodation</span></p>",
                                " <p> acknowledge</span></p>", " <p> itinary</span></p>"],
                    solution_en: " <p>3.(d) </span><span style=\"font-family:Palanquin Dark\">Itinary</span></p> <p><span style=\"font-family:Palanquin Dark\">‘Itinerary’ is the correct spelling.</span></p>",
                    solution_hi: " <p>3.(d) </span><span style=\"font-family:Palanquin Dark\">Itinary</span></p> <p><span style=\"font-family:Palanquin Dark\">‘Itinerary’ सही  spelling है। </span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "7",
                    question_en: "<p>4. <span style=\"font-family: Palanquin Dark;\">Choose the correct meaning of the underlined word in the given sentence. </span></p>\\r\\n<p><span style=\"font-family: Palanquin Dark;\">When I was in Class 5, I won the first prize in </span><span style=\"font-family: Palanquin Dark;\"><span style=\"text-decoration: underline;\">extempore</span> </span><span style=\"font-family: Palanquin Dark;\">at my school.</span></p>\\n",
                    question_hi: "<p>4. <span style=\"font-family: Palanquin Dark;\">Choose the correct meaning of the underlined word in the given sentence. </span></p>\\r\\n<p><span style=\"font-family: Palanquin Dark;\">When I was in Class 5, I won the first prize in </span><span style=\"font-family: Palanquin Dark;\"><span style=\"text-decoration: underline;\">extempore</span> </span><span style=\"font-family: Palanquin Dark;\">at my school.</span></p>\\n",
                    options_en: ["<p>A short piece of writing on a subject</p>\\n", "<p>A sports event</p>\\n", 
                                "<p>A speech delivered without prior preparation or rehearsal</p>\\n", "<p>A dramatic performance on stage</p>\\n"],
                    options_hi: ["<p>A short piece of writing on a subject</p>\\n", "<p>A sports event</p>\\n",
                                "<p>A speech delivered without prior preparation or rehearsal</p>\\n", "<p>A dramatic performance on stage</p>\\n"],
                    solution_en: "<p>4.(c) <span style=\"font-family: Palanquin Dark;\">Extempore - a speech delivered without prior preparation or rehearsal.</span></p>\\n",
                    solution_hi: "<p>4.(c) <span style=\"font-family: Palanquin Dark;\">Extempore</span><span style=\"font-family: Palanquin Dark;\"> -</span><span style=\"font-family: Palanquin Dark;\"> a speech delivered without prior preparation or rehearsal. / &#2348;&#2367;&#2344;&#2366; &#2346;&#2370;&#2352;&#2381;&#2357; &#2340;&#2376;&#2351;&#2366;&#2352;&#2368; &#2351;&#2366; &#2346;&#2370;&#2352;&#2381;&#2357;&#2366;&#2349;&#2381;&#2351;&#2366;&#2360; &#2325;&#2375; &#2342;&#2367;&#2351;&#2366; &#2327;&#2351;&#2366; &#2349;&#2366;&#2359;&#2339;&#2404;</span></p>\\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "7",
                    question_en: " <p>5. </span><span style=\"font-family:Palanquin Dark\">Parts of a sentence are given below in jumbled order. Arrange the parts in the correct order to form a meaningful sentence.</span></p> <p><span style=\"font-family:Palanquin Dark\">A. fell before me</span></p> <p><span style=\"font-family:Palanquin Dark\">B. a carcass of an animal</span></p> <p><span style=\"font-family:Palanquin Dark\">C. when suddenly</span></p> <p><span style=\"font-family:Palanquin Dark\">D. my heart skipped a beat</span></p>",
                    question_hi: " <p>5. </span><span style=\"font-family:Palanquin Dark\">Parts of a sentence are given below in jumbled order. Arrange the parts in the correct order to form a meaningful sentence.</span></p> <p><span style=\"font-family:Palanquin Dark\">A. fell before me</span></p> <p><span style=\"font-family:Palanquin Dark\">B. a carcass of an animal</span></p> <p><span style=\"font-family:Palanquin Dark\">C. when suddenly</span></p> <p><span style=\"font-family:Palanquin Dark\">D. my heart skipped a beat</span></p>",
                    options_en: [" <p> BACD</span></p>", " <p> CDBA</span></p>", 
                                " <p> ABCD</span></p>", " <p> DCBA</span></p>"],
                    options_hi: [" <p> BACD</span></p>", " <p> CDBA</span></p>",
                                " <p> ABCD</span></p>", " <p> DCBA</span></p>"],
                    solution_en: " <p>5.(d) </span><span style=\"font-family:Palanquin Dark\">DCBA</span></p> <p><span style=\"font-family:Palanquin Dark\">The given sentence starts with Part D as it contains the main idea of the sentence, i.e. my heart skipped a beat. Part D will be followed by Part C as it contains the conjunction ‘when’ which is used to denote the time of occurrence. Further, Part B contains the subject of the second clause and Part A contains the verb of the second clause. So, A will follow B. Going through the options, option ‘d’ has the correct sequence.</span></p>",
                    solution_hi: " <p>5.(d) </span><span style=\"font-family:Palanquin Dark\">DCBA</span></p> <p><span style=\"font-family:Palanquin Dark\">दिया गया वाक्य Part D से प्रारम्भ होगा क्योंकि इसमें वाक्य का मुख्य विचार ‘my heart skipped a beat’ शामिल है। Part D के बाद Part C आएगा क्योंकि इसमें conjunction ‘when’ शामिल है जिसका प्रयोग घटना के समय को दर्शाने के लिए किया जाता है। इसके अलावा, Part B में दूसरे clause का subject शामिल है और Part A में दूसरे clause का verb शामिल है। इसलिए, B के बाद A आएगा। Options के माध्यम से जाने पर, option \'d\' में सही sequence है।</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "7",
                    question_en: " <p>6. </span><span style=\"font-family:Palanquin Dark\">Sentences of a paragraph are given below in jumbled order. Arrange the sentences in the correct order to form a meaningful and coherent paragraph. </span></p> <p><span style=\"font-family:Palanquin Dark\">A) So then why are not Silicon Valley entrepreneurs clamouring to decode the ancient wisdom?</span></p> <p><span style=\"font-family:Palanquin Dark\">B) The failure rate in finding a new drug is as high as 95%. </span></p> <p><span style=\"font-family:Palanquin Dark\">C) Because these once-thriving cultures are now reduced to an endangered tribe.</span></p> <p><span style=\"font-family:Palanquin Dark\">D) According to industry data, it takes $2.6 billion and, on average, fourteen years to develop a new drug.</span></p>",
                    question_hi: " <p>6. </span><span style=\"font-family:Palanquin Dark\">Sentences of a paragraph are given below in jumbled order. Arrange the sentences in the correct order to form a meaningful and coherent paragraph. </span></p> <p><span style=\"font-family:Palanquin Dark\">A) So then why are not Silicon Valley entrepreneurs clamouring to decode the ancient wisdom?</span></p> <p><span style=\"font-family:Palanquin Dark\">B) The failure rate in finding a new drug is as high as 95%. </span></p> <p><span style=\"font-family:Palanquin Dark\">C) Because these once-thriving cultures are now reduced to an endangered tribe.</span></p> <p><span style=\"font-family:Palanquin Dark\">D) According to industry data, it takes $2.6 billion and, on average, fourteen years to develop a new drug.</span></p>",
                    options_en: [" <p> BADC</span></p>", " <p> DCBA</span></p>", 
                                " <p> DBAC</span></p>", " <p> ACDB</span></p>"],
                    options_hi: [" <p> BADC</span></p>", " <p> DCBA</span></p>",
                                " <p> DBAC</span></p>", " <p> ACDB</span></p>"],
                    solution_en: " <p>6.(c) </span><span style=\"font-family:Palanquin Dark\">DBAC</span></p> <p><span style=\"font-family:Palanquin Dark\">Sentence D will be the starting line as it contains the main idea of the sentence, i.e. the time and money required to develop a new drug. And, Sentence B states the failure rate in finding a new drug. So, B will follow D. Further, Sentence A states the question asking for the reason behind the unwillingness of Silicon Valley Entrepreneurs to decode the ancient wisdom & Sentence C states the answer to the question. So, C will follow A. Going through the options, option ‘c’ has the correct sequence.</span></p>",
                    solution_hi: " <p>6.(c) </span><span style=\"font-family:Palanquin Dark\">DBAC</span></p> <p><span style=\"font-family:Palanquin Dark\">Sentence D प्रारंभिक line होगी क्योंकि इसमें वाक्य का मुख्य विचार ‘the time and money required to develop a new drug’ शामिल है। और, sentence B नई दवा खोजने में विफलता का दर बताता है। इसलिए, D के बाद B आएगा। इसके अलावा, sentence A में प्राचीन ज्ञान को समझने के लिए सिलिकॉन वैली उद्यमियों की अनिच्छा के पीछे का कारण पूछने वाला प्रश्न बताया गया है और sentence C प्रश्न का उत्तर बताता है। इसलिए, A के बाद C आएगा। Options के माध्यम से जाने पर, option ‘c’ में सही sequence है।</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "7",
                    question_en: "<p>7. <span style=\"font-family: Palanquin Dark;\">Select the most appropriate synonym of the given word. </span></p>\\r\\n<p><strong><span style=\"font-family: Palanquin Dark;\">Resentment </span></strong></p>\\n",
                    question_hi: "<p>7. <span style=\"font-family: Palanquin Dark;\">Select the most appropriate synonym of the given word. </span></p>\\r\\n<p><strong><span style=\"font-family: Palanquin Dark;\">Resentment </span></strong></p>\\n",
                    options_en: ["<p>Calmness</p>\\n", "<p>Happiness</p>\\n", 
                                "<p>Anger</p>\\n", "<p>Relaxation</p>\\n"],
                    options_hi: ["<p>Calmness</p>\\n", "<p>Happiness</p>\\n",
                                "<p>Anger</p>\\n", "<p>Relaxation</p>\\n"],
                    solution_en: "<p>7.(c) <strong>Anger</strong>- <span style=\"font-family: Palanquin Dark;\">a strong feeling of annoyance, displeasure, or hostility.</span></p>\\r\\n<p><span style=\"font-family: Palanquin Dark;\"><strong>Resentment</strong>-</span><span style=\"font-family: Palanquin Dark;\"> a feeling of angry displeasure at something.</span></p>\\r\\n<p><span style=\"font-family: Palanquin Dark;\"><strong>Calmness</strong>-</span><span style=\"font-family: Palanquin Dark;\"> the state of being free from disturbance or violent activity.</span></p>\\r\\n<p><span style=\"font-family: Palanquin Dark;\"><strong>Happiness</strong>-</span><span style=\"font-family: Palanquin Dark;\"> the state of being happy.</span></p>\\r\\n<p><span style=\"font-family: Palanquin Dark;\"><strong>Relaxation </strong>-</span><span style=\"font-family: Palanquin Dark;\"> the state of being free from tension and anxiety.</span></p>\\n",
                    solution_hi: "<p>7.(c) <strong>Anger </strong><span style=\"font-family: Palanquin Dark;\">(&#2325;&#2381;&#2352;&#2379;&#2343;)</span><span style=\"font-family: Palanquin Dark;\"> - </span><span style=\"font-family: Palanquin Dark;\">a strong feeling of annoyance, displeasure, or hostility.</span></p>\\r\\n<p><strong><span style=\"font-family: Palanquin Dark;\">Resentment</span></strong><span style=\"font-family: Palanquin Dark;\"><strong> </strong>(&#2325;&#2381;&#2352;&#2379;&#2343;) - a feeling of angry displeasure at something.</span></p>\\r\\n<p><strong><span style=\"font-family: Palanquin Dark;\">Calmness </span></strong><span style=\"font-family: Palanquin Dark;\">(&#2358;&#2366;&#2306;&#2340;&#2367;) - the state of being free from disturbance or violent activity.</span></p>\\r\\n<p><strong><span style=\"font-family: Palanquin Dark;\">Happiness </span></strong><span style=\"font-family: Palanquin Dark;\">(&#2346;&#2381;&#2352;&#2360;&#2344;&#2381;&#2344;&#2340;&#2366;) -</span><span style=\"font-family: Palanquin Dark;\"> </span><span style=\"font-family: Palanquin Dark;\">the state of being happy.</span></p>\\r\\n<p><strong><span style=\"font-family: Palanquin Dark;\">Relaxation </span></strong><span style=\"font-family: Palanquin Dark;\">(&#2310;&#2352;&#2366;&#2350;) - the state of being free from tension and anxiety.</span></p>\\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "7",
                    question_en: "<p>8. <span style=\"font-family: Palanquin Dark;\">Select the option with the correct spelling that can replace the underlined word in the </span><span style=\"font-family: Palanquin Dark;\">given sentence. </span></p>\\r\\n<p><span style=\"font-family: Palanquin Dark;\">Kasturi acts as a </span><span style=\"text-decoration: underline;\"><span style=\"font-family: Palanquin Dark;\">liason</span></span><span style=\"font-family: Palanquin Dark;\"> between patients and staff.</span></p>\\n",
                    question_hi: "<p>8. <span style=\"font-family: Palanquin Dark;\">Select the option with the correct spelling that can replace the underlined word in the </span><span style=\"font-family: Palanquin Dark;\">given sentence. </span></p>\\r\\n<p><span style=\"font-family: Palanquin Dark;\">Kasturi acts as a </span><span style=\"text-decoration: underline;\"><span style=\"font-family: Palanquin Dark;\">liason</span></span><span style=\"font-family: Palanquin Dark;\"> between patients and staff.</span></p>\\n",
                    options_en: ["<p>liaision</p>\\n", "<p>laision</p>\\n", 
                                "<p>liaison</p>\\n", "<p>liaisan</p>\\n"],
                    options_hi: ["<p>liaision</p>\\n", "<p>laision</p>\\n",
                                "<p>liaison</p>\\n", "<p>liaisan</p>\\n"],
                    solution_en: "<p>8.(c) <span style=\"font-family: Palanquin Dark;\">Liaison</span></p>\\r\\n<p><span style=\"font-family: Palanquin Dark;\">&lsquo;Liaison&rsquo; is the correct spelling.</span></p>\\n",
                    solution_hi: "<p>8.(c) <span style=\"font-family: Palanquin Dark;\">Liaison</span></p>\\r\\n<p><span style=\"font-family: Palanquin Dark;\">&lsquo;Liaison&rsquo; &#2360;&#2361;&#2368; spelling &#2361;&#2376;&#2404; </span></p>\\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "7",
                    question_en: "<p>9. <span style=\"font-family: Palanquin Dark;\">Select the most appropriate ANTONYM of the given word.</span></p>\\r\\n<p><strong><span style=\"font-family: Palanquin Dark;\">Meagre</span></strong></p>\\n",
                    question_hi: "<p>9. <span style=\"font-family: Palanquin Dark;\">Select the most appropriate ANTONYM of the given word.</span></p>\\r\\n<p><strong><span style=\"font-family: Palanquin Dark;\">Meagre</span></strong></p>\\n",
                    options_en: ["<p>Regional</p>\\n", "<p>Racial</p>\\n", 
                                "<p>Stupid</p>\\n", "<p>Generous</p>\\n"],
                    options_hi: ["<p>Regional</p>\\n", "<p>Racial</p>\\n",
                                "<p>Stupid</p>\\n", "<p>Generous</p>\\n"],
                    solution_en: "<p>9.(d) <strong>Generous</strong>-<span style=\"font-family: Palanquin Dark;\"> larger or more plentiful than is usual or necessary.</span></p>\\r\\n<p><strong><span style=\"font-family: Palanquin Dark;\">Meagre</span></strong><span style=\"font-family: Palanquin Dark;\">- lacking in quantity or quality.</span></p>\\r\\n<p><strong><span style=\"font-family: Palanquin Dark;\">Regional</span></strong><span style=\"font-family: Palanquin Dark;\">- relating to or characteristic of a region.</span></p>\\r\\n<p><strong><span style=\"font-family: Palanquin Dark;\">Racial</span></strong><span style=\"font-family: Palanquin Dark;\">- connected with differences in race or ethnicity.</span></p>\\n",
                    solution_hi: "<p>9.(d) <strong>Generous </strong><span style=\"font-family: Palanquin Dark;\">(&#2346;&#2381;&#2352;&#2330;&#2369;&#2352; &#2350;&#2366;&#2340;&#2381;&#2352;&#2366;) - larger or more plentiful than is usual or necessary.</span></p>\\r\\n<p><strong><span style=\"font-family: Palanquin Dark;\">Meagre</span><span style=\"font-family: Palanquin Dark;\"> </span></strong><span style=\"font-family: Palanquin Dark;\">(&#2309;&#2354;&#2381;&#2346;) - lacking in quantity or quality.</span></p>\\r\\n<p><strong><span style=\"font-family: Palanquin Dark;\">Regional </span></strong><span style=\"font-family: Palanquin Dark;\">(&#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;&#2368;&#2351;) - relating to or characteristic of a region.</span></p>\\r\\n<p><strong><span style=\"font-family: Palanquin Dark;\">Racial </span></strong><span style=\"font-family: Palanquin Dark;\">(&#2344;&#2360;&#2381;&#2354;&#2368;&#2351;) - connected with differences in race or ethnicity.</span></p>\\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "7",
                    question_en: "<p>10. <span style=\"font-family: Palanquin Dark;\">Select the most appropriate synonym for the </span><strong><span style=\"font-family: Palanquin Dark;\">underlined </span></strong><span style=\"font-family: Palanquin Dark;\">word in the following sentence. </span></p>\\r\\n<p><span style=\"font-family: Palanquin Dark;\">The politician\'s </span><span style=\"font-family: Palanquin Dark;\"><span style=\"text-decoration: underline;\">pompous</span> </span><span style=\"font-family: Palanquin Dark;\">rhetoric during the campaign failed to garner much support from the public.</span></p>\\n",
                    question_hi: "<p>10. <span style=\"font-family: Palanquin Dark;\">Select the most appropriate synonym for the </span><strong><span style=\"font-family: Palanquin Dark;\">underlined </span></strong><span style=\"font-family: Palanquin Dark;\">word in the following sentence. </span></p>\\r\\n<p><span style=\"font-family: Palanquin Dark;\">The politician\'s </span><span style=\"text-decoration: underline;\"><span style=\"font-family: Palanquin Dark;\">pompous </span></span><span style=\"font-family: Palanquin Dark;\">rhetoric during the campaign failed to garner much support from the public.</span></p>\\n",
                    options_en: ["<p>Conciliatory</p>\\n", "<p>Benign</p>\\n", 
                                "<p>Supercilious</p>\\n", "<p>Diplomatic</p>\\n"],
                    options_hi: ["<p>Conciliatory</p>\\n", "<p>Benign</p>\\n",
                                "<p>Supercilious</p>\\n", "<p>Diplomatic</p>\\n"],
                    solution_en: "<p>10.(c) <strong>Supercilious-</strong> <span style=\"font-family: Palanquin Dark;\">behaving as if you think you are superior to others.</span></p>\\r\\n<p><span style=\"font-family: Palanquin Dark;\"><strong>Pompous</strong>-</span><span style=\"font-family: Palanquin Dark;\"> acting in a way that makes them look better than others.</span></p>\\r\\n<p><strong><span style=\"font-family: Palanquin Dark;\">Conciliatory-</span></strong><span style=\"font-family: Palanquin Dark;\"> intended to pacify someone.</span></p>\\r\\n<p><strong><span style=\"font-family: Palanquin Dark;\">Benign-</span></strong><span style=\"font-family: Palanquin Dark;\"> gentle and kindly.</span></p>\\r\\n<p><strong><span style=\"font-family: Palanquin Dark;\">Diplomatic-</span></strong><span style=\"font-family: Palanquin Dark;\"> smoothness and skill in handling matters.</span></p>\\n",
                    solution_hi: "<p>10.(c) <strong>Supercilious</strong><span style=\"font-family: Palanquin Dark;\"><strong> </strong>(&#2328;&#2350;&#2306;&#2337;&#2368;/&#2309;&#2349;&#2367;&#2350;&#2366;&#2344;&#2368;) - behaving as if you think you are superior to others.</span></p>\\r\\n<p><strong><span style=\"font-family: Palanquin Dark;\">Pompous</span></strong><span style=\"font-family: Palanquin Dark;\"><strong> </strong>(&#2310;&#2337;&#2306;&#2348;&#2352;&#2346;&#2370;&#2352;&#2381;&#2339;) - acting in a way that makes them look better than others.</span></p>\\r\\n<p><strong><span style=\"font-family: Palanquin Dark;\">Conciliatory</span></strong><span style=\"font-family: Palanquin Dark;\"><strong> </strong>(&#2350;&#2376;&#2340;&#2381;&#2352;&#2368;&#2346;&#2370;&#2352;&#2381;&#2339;) - intended to pacify someone.</span></p>\\r\\n<p><strong><span style=\"font-family: Palanquin Dark;\">Benign</span></strong><span style=\"font-family: Palanquin Dark;\"><strong> </strong>(&#2342;&#2351;&#2366;&#2354;&#2369;) - gentle and kindly.</span></p>\\r\\n<p><strong><span style=\"font-family: Palanquin Dark;\">Diplomatic</span></strong><span style=\"font-family: Palanquin Dark;\"><strong> </strong>(&#2352;&#2366;&#2332;&#2344;&#2351;&#2367;&#2325;) - smoothness and skill in handling matters.</span></p>\\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "7",
                    question_en: "<p>11. <span style=\"font-family: Palanquin Dark;\">Parts of a sentence are given below in jumbled order. Select the option that arranges </span><span style=\"font-family: Palanquin Dark;\">the parts in the correct order to form a meaningful sentence.</span></p>\\r\\n<p><span style=\"font-family: Palanquin Dark;\">A.In accordance with hierarchical status</span></p>\\r\\n<p><span style=\"font-family: Palanquin Dark;\">B.Of the services rendered </span></p>\\r\\n<p><span style=\"font-family: Palanquin Dark;\">C.Rewards are given</span></p>\\r\\n<p><span style=\"font-family: Palanquin Dark;\">D.And are not related to the economic value</span></p>\\n",
                    question_hi: "<p>11. <span style=\"font-family: Palanquin Dark;\">Parts of a sentence are given below in jumbled order. Select the option that arranges </span><span style=\"font-family: Palanquin Dark;\">the parts in the correct order to form a meaningful sentence.</span></p>\\r\\n<p><span style=\"font-family: Palanquin Dark;\">A.In accordance with hierarchical status</span></p>\\r\\n<p><span style=\"font-family: Palanquin Dark;\">B.Of the services rendered </span></p>\\r\\n<p><span style=\"font-family: Palanquin Dark;\">C.Rewards are given</span></p>\\r\\n<p><span style=\"font-family: Palanquin Dark;\">D.And are not related to the economic value</span></p>\\n",
                    options_en: ["<p>A, B, D, C</p>\\n", "<p>A, D, B, C</p>\\n", 
                                "<p>C, A, D, B</p>\\n", "<p>C, B, A, D</p>\\n"],
                    options_hi: ["<p>A, B, D, C</p>\\n", "<p>A, D, B, C</p>\\n",
                                "<p>C, A, D, B</p>\\n", "<p>C, B, A, D</p>\\n"],
                    solution_en: "<p>11.(c) <span style=\"font-family: Palanquin Dark;\">CADB</span></p>\\r\\n<p><span style=\"font-family: Palanquin Dark;\">The given sentence starts with Part C as it contains the main subject of the parajumble, i.e. rewards. Part C will be followed by Part A as it states the criterion for distribution of rewards. Further, Part D states that rewards are not related to economic value and Part B states the services rendered. So, B will follow D. Going through the options, option &lsquo;c&rsquo; has the correct sequence.</span></p>\\n",
                    solution_hi: "<p>11.(c) <span style=\"font-family: Palanquin Dark;\">CADB</span></p>\\r\\n<p><span style=\"font-family: Palanquin Dark;\">&#2342;&#2367;&#2351;&#2366; &#2327;&#2351;&#2366; &#2357;&#2366;&#2325;&#2381;&#2351; Part C &#2360;&#2375; &#2346;&#2381;&#2352;&#2366;&#2352;&#2350;&#2381;&#2349; &#2361;&#2379;&#2327;&#2366; &#2325;&#2381;&#2351;&#2379;&#2306;&#2325;&#2367; &#2311;&#2360;&#2350;&#2375;&#2306; parajumble &#2325;&#2366; &#2350;&#2369;&#2326;&#2381;&#2351; &#2357;&#2367;&#2359;&#2351; &lsquo;rewards&rsquo; &#2358;&#2366;&#2350;&#2367;&#2354; &#2361;&#2376;&#2404; Part C &#2325;&#2375; &#2348;&#2366;&#2342; Part A &#2310;&#2319;&#2327;&#2366; &#2325;&#2381;&#2351;&#2379;&#2306;&#2325;&#2367; &#2311;&#2360;&#2350;&#2375;&#2306; &#2346;&#2369;&#2352;&#2360;&#2381;&#2325;&#2366;&#2352;&#2379;&#2306; &#2325;&#2375; &#2357;&#2367;&#2340;&#2352;&#2339; &#2325;&#2366; &#2350;&#2366;&#2346;&#2342;&#2306;&#2337; &#2361;&#2376;&#2404; &#2311;&#2360;&#2325;&#2375; &#2309;&#2354;&#2366;&#2357;&#2366;, Part D &#2350;&#2375;&#2306; &#2325;&#2361;&#2366; &#2327;&#2351;&#2366; &#2361;&#2376; &#2325;&#2367; &#2346;&#2369;&#2352;&#2360;&#2381;&#2325;&#2366;&#2352; &#2310;&#2352;&#2381;&#2341;&#2367;&#2325; &#2350;&#2370;&#2354;&#2381;&#2351; &#2360;&#2375; &#2360;&#2306;&#2348;&#2306;&#2343;&#2367;&#2340; &#2344;&#2361;&#2368;&#2306; &#2361;&#2376;&#2306; &#2324;&#2352; Part B &#2346;&#2381;&#2352;&#2342;&#2366;&#2344; &#2325;&#2368; &#2327;&#2312; &#2360;&#2375;&#2357;&#2366;&#2323;&#2306; &#2325;&#2375; &#2348;&#2366;&#2352;&#2375; &#2350;&#2375;&#2306; &#2348;&#2340;&#2366;&#2340;&#2366; &#2361;&#2376;&#2404; &#2311;&#2360;&#2354;&#2367;&#2319;, D &#2325;&#2375; &#2348;&#2366;&#2342; B &#2310;&#2319;&#2327;&#2366;&#2404; Options &#2325;&#2375; &#2350;&#2366;&#2343;&#2381;&#2351;&#2350; &#2360;&#2375; &#2332;&#2366;&#2344;&#2375; &#2346;&#2352;, option &lsquo;c&rsquo; &#2350;&#2375;&#2306; &#2360;&#2361;&#2368; sequence &#2361;&#2376;&#2404;</span></p>\\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "7",
                    question_en: "<p>12. <span style=\"font-family: Palanquin Dark;\">Select the option that expresses the given sentence in a positive degree of comparison.</span></p>\\r\\n<p><span style=\"font-family: Palanquin Dark;\">She is the dullest child in the class.</span></p>\\n",
                    question_hi: "<p>12. <span style=\"font-family: Palanquin Dark;\">Select the option that expresses the given sentence in a positive degree of comparison.</span></p>\\r\\n<p><span style=\"font-family: Palanquin Dark;\">She is the dullest child in the class.</span></p>\\n",
                    options_en: ["<p>She is very duller in the class.</p>\\n", "<p>Dullest child in the class is she.</p>\\n", 
                                "<p>No other child in the class is as dull as she.</p>\\n", "<p>In the class of all she is dull.</p>\\n"],
                    options_hi: ["<p>She is very duller in the class.</p>\\n", "<p>Dullest child in the class is she.</p>\\n",
                                "<p>No other child in the class is as dull as she.</p>\\n", "<p>In the class of all she is dull.</p>\\n"],
                    solution_en: "<p>12.(c)&nbsp;<span style=\"font-weight: 400;\">No other child in the class is as dull as she.(Correct)</span></p>\\r\\n<p><span style=\"font-weight: 400;\">(a) She is very </span><span style=\"text-decoration: underline;\"><span style=\"font-weight: 400;\">duller</span></span><span style=\"font-weight: 400;\"> in the class.(Incorrect Degree- Comparative degree)</span></p>\\r\\n<p><span style=\"font-weight: 400;\">(b) </span><span style=\"text-decoration: underline;\"><span style=\"font-weight: 400;\">Dullest</span></span><span style=\"font-weight: 400;\"> child in the class is she.(Incorrect Degree- Superlative degree)</span></p>\\r\\n<p><span style=\"font-weight: 400;\">(d) In the class of all she is dull.(Incorrect Sentence Structure)</span></p>\\n",
                    solution_hi: "<p>12.(c) <span style=\"font-weight: 400;\">No other child in the class is as dull as she.(Correct)</span></p>\\r\\n<p><span style=\"font-weight: 400;\">(a) She is very </span><span style=\"text-decoration: underline;\"><span style=\"font-weight: 400;\">duller</span></span><span style=\"font-weight: 400;\"> in the class.(&#2327;&#2354;&#2340; Degree- Comparative degree)</span></p>\\r\\n<p><span style=\"font-weight: 400;\">(b) </span><span style=\"text-decoration: underline;\"><span style=\"font-weight: 400;\">Dullest</span></span><span style=\"font-weight: 400;\"> child in the class is she.(&#2327;&#2354;&#2340; Degree- Superlative degree)</span></p>\\r\\n<p><span style=\"font-weight: 400;\">(d) In the class of all she is dull.(&#2327;&#2354;&#2340; Sentence Structure)</span></p>\\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "7",
                    question_en: "<p>13. <span style=\"font-family: Palanquin Dark;\">Select the most appropriate ANTONYM of the underlined word. </span></p>\\r\\n<p><span style=\"font-family: Palanquin Dark;\">Wealthier countries are obligated to fulfil a </span><span style=\"text-decoration: underline;\"><span style=\"font-family: Palanquin Dark;\">commitment</span></span><span style=\"font-family: Palanquin Dark;\"> made in the Paris Agreement to provide $100 billion a year in international climate finance.</span></p>\\n",
                    question_hi: "<p>13. <span style=\"font-family: Palanquin Dark;\">Select the most appropriate ANTONYM of the underlined word. </span></p>\\r\\n<p><span style=\"font-family: Palanquin Dark;\">Wealthier countries are obligated to fulfil a </span><span style=\"text-decoration: underline;\"><span style=\"font-family: Palanquin Dark;\">commitment</span></span><span style=\"font-family: Palanquin Dark;\"> made in the Paris Agreement to provide $100 billion a year in international climate finance.</span></p>\\n",
                    options_en: ["<p>feeble</p>\\n", "<p>drape</p>\\n", 
                                "<p>breach</p>\\n", "<p>hale</p>\\n"],
                    options_hi: ["<p>feeble</p>\\n", "<p>drape</p>\\n",
                                "<p>breach</p>\\n", "<p>hale</p>\\n"],
                    solution_en: "<p>13.(c) <strong>Breach </strong>- <span style=\"font-family: Palanquin Dark;\">an act of breaking a rule, law or practice.</span></p>\\r\\n<p><span style=\"font-family: Palanquin Dark;\"><strong>Commitment</strong>-</span><span style=\"font-family: Palanquin Dark;\"> a strong adherence to an idea or system.</span></p>\\r\\n<p><span style=\"font-family: Palanquin Dark;\"><strong>Feeble</strong>-</span><span style=\"font-family: Palanquin Dark;\"> lacking physical strength, especially because of age or weakness.</span></p>\\r\\n<p><span style=\"font-family: Palanquin Dark;\"><strong>Drape</strong>- </span><span style=\"font-family: Palanquin Dark;\">to arrange loosely on something.</span></p>\\r\\n<p><span style=\"font-family: Palanquin Dark;\"><strong>Hale</strong>-</span><span style=\"font-family: Palanquin Dark;\"> strong and healthy.</span></p>\\n",
                    solution_hi: "<p>13.(c) <strong>Breach </strong><span style=\"font-family: Palanquin Dark;\">(&#2313;&#2354;&#2381;&#2354;&#2306;&#2328;&#2344; &#2325;&#2352;&#2344;&#2366;) - an act of breaking a rule, law or practice.</span></p>\\r\\n<p><strong><span style=\"font-family: Palanquin Dark;\">Commitment</span></strong><span style=\"font-family: Palanquin Dark;\"><strong> </strong>(&#2357;&#2330;&#2344;&#2348;&#2342;&#2381;&#2343;&#2340;&#2366;) - a strong adherence to an idea or system.</span></p>\\r\\n<p><strong><span style=\"font-family: Palanquin Dark;\">Feeble </span></strong><span style=\"font-family: Palanquin Dark;\">(&#2342;&#2369;&#2352;&#2381;&#2348;&#2354;) - lacking physical strength, especially because of age or weakness.</span></p>\\r\\n<p><strong><span style=\"font-family: Palanquin Dark;\">Drape </span></strong><span style=\"font-family: Palanquin Dark;\">(</span><span style=\"font-family: Palanquin;\">&#2360;&#2332;&#2366;&#2344;&#2366;</span><span style=\"font-family: Palanquin;\">) -</span><span style=\"font-family: Palanquin;\"> </span><span style=\"font-family: Palanquin;\">to arrange loosely on something.</span></p>\\r\\n<p><strong><span style=\"font-family: Palanquin;\">Hale </span></strong><span style=\"font-family: Palanquin Dark;\">(&#2360;&#2381;&#2357;&#2360;&#2381;&#2341;) - strong and healthy.</span></p>\\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "7",
                    question_en: "<p>14. <span style=\"font-family: Palanquin Dark;\">Select the most appropriate idiom for the underlined segment in the following sentence.</span></p>\\r\\n<p><span style=\"font-family: Palanquin Dark;\">Brave people never hide </span><span style=\"text-decoration: underline;\"><span style=\"font-family: Palanquin Dark;\">at the moment of decision.</span></span></p>\\n",
                    question_hi: "<p>14. <span style=\"font-family: Palanquin Dark;\">Select the most appropriate idiom for the underlined segment in the following sentence.</span></p>\\r\\n<p><span style=\"font-family: Palanquin Dark;\">Brave people never hide </span><span style=\"text-decoration: underline;\"><span style=\"font-family: Palanquin Dark;\">at the moment of decision.</span></span></p>\\n",
                    options_en: ["<p>When the crunch comes</p>\\n", "<p>Beat the clock</p>\\n", 
                                "<p>A hell of time</p>\\n", "<p>Turn back the hands of time</p>\\n"],
                    options_hi: ["<p>When the crunch comes</p>\\n", "<p>Beat the clock</p>\\n",
                                "<p>A hell of time</p>\\n", "<p>Turn back the hands of time</p>\\n"],
                    solution_en: "<p>14.(a) <span style=\"font-family: Palanquin Dark;\">When the crunch comes - at the moment of decision.</span></p>\\n",
                    solution_hi: "<p>14.(a) <span style=\"font-family: Palanquin Dark;\">When the crunch comes - at the moment of decision/ &#2344;&#2367;&#2352;&#2381;&#2339;&#2366;&#2351;&#2325; &#2325;&#2381;&#2359;&#2339;&#2404; </span></p>\\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "7",
                    question_en: "<p>15. <span style=\"font-family: Palanquin Dark;\">Select the option that expresses the given sentence in active voice. </span></p>\\r\\n<p><span style=\"font-family: Palanquin Dark;\">French is taught to us by Miss Glenn.</span></p>\\n",
                    question_hi: "<p>15. <span style=\"font-family: Palanquin Dark;\">Select the option that expresses the given sentence in active voice. </span></p>\\r\\n<p><span style=\"font-family: Palanquin Dark;\">French is taught to us by Miss Glenn.</span></p>\\n",
                    options_en: ["<p>Miss Glenn taught us French.</p>\\n", "<p>Miss Glenn teach us French.</p>\\n", 
                                "<p>Miss Glenn teaches us French.</p>\\n", "<p>Miss Glenn is teaching us French.</p>\\n"],
                    options_hi: ["<p>Miss Glenn taught us French.</p>\\n", "<p>Miss Glenn teach us French.</p>\\n",
                                "<p>Miss Glenn teaches us French.</p>\\n", "<p>Miss Glenn is teaching us French.</p>\\n"],
                    solution_en: "<p>15.(c)&nbsp; <span style=\"font-weight: 400;\">Miss Glenn teaches us French.(Correct)</span></p>\\r\\n<p><span style=\"font-weight: 400;\">(a) Miss Glenn </span><span style=\"text-decoration: underline;\"><span style=\"font-weight: 400;\">taught</span></span><span style=\"font-weight: 400;\"><span style=\"text-decoration: underline;\"> </span>us French.(Incorrect Tense)</span></p>\\r\\n<p><span style=\"font-weight: 400;\">(b) Miss Glenn </span><span style=\"text-decoration: underline;\"><span style=\"font-weight: 400;\">teach</span></span><span style=\"font-weight: 400;\"><span style=\"text-decoration: underline;\"> </span>us French.(Incorrect form of the Verb)</span></p>\\r\\n<p><span style=\"font-weight: 400;\">(d) Miss Glenn </span><span style=\"text-decoration: underline;\"><span style=\"font-weight: 400;\">is teaching</span></span><span style=\"font-weight: 400;\"> us French.(Incorrect Tense)</span></p>\\n",
                    solution_hi: "<p>15.(c)<strong>&nbsp;</strong><span style=\"font-weight: 400;\">Miss Glenn teaches us French.(Correct)</span></p>\\r\\n<p><span style=\"font-weight: 400;\">(a) Miss Glenn </span><span style=\"text-decoration: underline;\"><span style=\"font-weight: 400;\">taught</span></span><span style=\"font-weight: 400;\"><span style=\"text-decoration: underline;\"> </span>us French.(&#2327;&#2354;&#2340; Tense)</span></p>\\r\\n<p><span style=\"font-weight: 400;\">(b) Miss Glenn </span><span style=\"text-decoration: underline;\"><span style=\"font-weight: 400;\">teach</span></span><span style=\"font-weight: 400;\"><span style=\"text-decoration: underline;\"> </span>us French.(Verb &#2325;&#2366;&nbsp; &#2327;&#2354;&#2340; form)</span></p>\\r\\n<p><span style=\"font-weight: 400;\">(d) Miss Glenn </span><span style=\"text-decoration: underline;\"><span style=\"font-weight: 400;\">is teaching</span></span><span style=\"font-weight: 400;\"><span style=\"text-decoration: underline;\"> </span>us French.(&#2327;&#2354;&#2340; Tense)</span></p>\\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "7",
                    question_en: "<p>16. <span style=\"font-family: Palanquin Dark;\">Select the option that will improve the underlined part of the given sentence. </span></p>\\r\\n<p><span style=\"font-family: Palanquin Dark;\">Adyasha </span><span style=\"font-family: Palanquin Dark;\"><span style=\"text-decoration: underline;\">would have been looked</span> </span><span style=\"font-family: Palanquin Dark;\">gorgeous in ethnic apparel.</span></p>\\n",
                    question_hi: "<p>16. <span style=\"font-family: Palanquin Dark;\">Select the option that will improve the underlined part of the given sentence. </span></p>\\r\\n<p><span style=\"font-family: Palanquin Dark;\">Adyasha </span><span style=\"font-family: Palanquin Dark;\"><span style=\"text-decoration: underline;\">would have been looked</span></span><span style=\"font-family: Palanquin Dark;\">&nbsp;gorgeous in ethnic apparel.</span></p>\\n",
                    options_en: ["<p>was looked</p>\\n", "<p>would have looked</p>\\n", 
                                "<p>had looking</p>\\n", "<p>would be looked</p>\\n"],
                    options_hi: ["<p>was looked</p>\\n", "<p>would have looked</p>\\n",
                                "<p>had looking</p>\\n", "<p>would be looked</p>\\n"],
                    solution_en: "<p>16.(b) <span style=\"font-family: Palanquin Dark;\">would have looked</span></p>\\r\\n<p><span style=\"font-family: Palanquin Dark;\">The given sentence is an example of active voice and the correct grammatical structure is &lsquo;would have + </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>V</mi><mn>3</mn></msub></math><span style=\"font-family: Palanquin Dark;\">&rsquo;. Hence, &lsquo;would have looked(</span><span style=\"font-family: Palanquin Dark;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>V</mi><mn>3</mn></msub></math></span><span style=\"font-family: Palanquin Dark;\">)&rsquo; is the most appropriate answer.</span></p>\\n",
                    solution_hi: "<p>16.(b) <span style=\"font-family: Palanquin Dark;\">would have looked</span></p>\\r\\n<p><span style=\"font-family: Palanquin Dark;\">&#2342;&#2367;&#2351;&#2366; &#2327;&#2351;&#2366; &#2357;&#2366;&#2325;&#2381;&#2351; active voice &#2325;&#2366; &#2313;&#2342;&#2366;&#2361;&#2352;&#2339; &#2361;&#2376; &#2311;&#2360;&#2354;&#2367;&#2319; &#2360;&#2361;&#2368; grammatical structure &lsquo;would have + </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>V</mi><mn>3</mn></msub></math><span style=\"font-family: Palanquin Dark;\">&rsquo; &#2361;&#2379;&#2327;&#2366; &#2404; &#2309;&#2340;&#2307;, &lsquo;would have looked(</span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>V</mi><mn>3</mn></msub></math><span style=\"font-family: Palanquin Dark;\">)&rsquo; &#2360;&#2348;&#2360;&#2375; &#2313;&#2346;&#2351;&#2369;&#2325;&#2381;&#2340; &#2313;&#2340;&#2381;&#2340;&#2352; &#2361;&#2376;&#2404;</span></p>\\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "7",
                    question_en: " <p>17. </span><span style=\"font-family:Palanquin Dark\">The following sentence has been split into four segments. Identify the segment that contains a grammatical error. </span></p> <p><span style=\"font-family:Palanquin Dark\">Babbal is / a oldest member / in our / Black Ice Club.</span></p>",
                    question_hi: " <p>17. </span><span style=\"font-family:Palanquin Dark\">The following sentence has been split into four segments. Identify the segment that contains a grammatical error. </span></p> <p><span style=\"font-family:Palanquin Dark\">Babbal is / a oldest member / in our / Black Ice Club.</span></p>",
                    options_en: [" <p> in our</span></p>", " <p> Black Ice Club</span></p>", 
                                " <p> Babbal is</span></p>", " <p> a oldest member</span></p>"],
                    options_hi: [" <p> in our</span></p>", " <p> Black Ice Club</span></p>",
                                " <p> Babbal is</span></p>", " <p> a oldest member</span></p>"],
                    solution_en: " <p>17.(d) </span><span style=\"font-family:Palanquin Dark\">a oldest member</span></p> <p><span style=\"font-family:Palanquin Dark\">‘A oldest member’ must be replaced with ‘the oldest member’ as we always use the definite article ‘the’ before any superlative degree. Similarly, in the given sentence, ‘oldest’ is the superlative degree of ‘old’. Hence, ‘the oldest member’ is the most appropriate answer.</span></p>",
                    solution_hi: " <p>17.(d) </span><span style=\"font-family:Palanquin Dark\">a oldest member</span></p> <p><span style=\"font-family:Palanquin Dark\">‘A oldest member’ के स्थान पर ‘the oldest member’ का प्रयोग होगा क्योंकि हम हमेशा किसी भी superlative degree से पहले definite article ‘the’ का प्रयोग करते हैं। इसी प्रकार, दिए गए वाक्य में ‘oldest’ , ‘old’ की superlative degree है। अतः ‘the oldest member’ सबसे उपयुक्त उत्तर है।</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "7",
                    question_en: " <p>18. </span><span style=\"font-family:Palanquin Dark\">Parts of a sentence are given below in jumbled order, except the first part. Arrange the parts in the correct order to form a meaningful sentence. </span></p> <p><span style=\"font-family:Palanquin Dark\">Such poststructuralist pronouncements... </span></p> <p><span style=\"font-family:Palanquin Dark\">A. not only in the Anglo-American academia</span></p> <p><span style=\"font-family:Palanquin Dark\">B. on the literature of the Third World,</span></p> <p><span style=\"font-family:Palanquin Dark\">C. however, are a common feature of the literary theory practiced</span></p> <p><span style=\"font-family:Palanquin Dark\">D. but also in the Third-World countries</span></p>",
                    question_hi: " <p>18. </span><span style=\"font-family:Palanquin Dark\">Parts of a sentence are given below in jumbled order, except the first part. Arrange the parts in the correct order to form a meaningful sentence. </span></p> <p><span style=\"font-family:Palanquin Dark\">Such poststructuralist pronouncements... </span></p> <p><span style=\"font-family:Palanquin Dark\">A. not only in the Anglo-American academia</span></p> <p><span style=\"font-family:Palanquin Dark\">B. on the literature of the Third World,</span></p> <p><span style=\"font-family:Palanquin Dark\">C. however, are a common feature of the literary theory practiced</span></p> <p><span style=\"font-family:Palanquin Dark\">D. but also in the Third-World countries</span></p>",
                    options_en: [" <p> ABCD</span></p>", " <p> ADCB</span></p>", 
                                " <p> BCAD</span></p>", " <p> CABD</span></p>"],
                    options_hi: [" <p> ABCD</span></p>", " <p> ADCB</span></p>",
                                " <p> BCAD</span></p>", " <p> CABD</span></p>"],
                    solution_en: " <p>18.(c) </span><span style=\"font-family:Palanquin Dark\">BCAD</span></p> <p><span style=\"font-family:Palanquin Dark\">The given sentence starts with the phrase ‘such poststructural pronouncements’. Part B states the field in which the declarations have been made & Part C talks about the common feature of literary theory. So, C will follow B. Further, Part A states the community in which literary theory is practiced & Part D states the countries practicing the literary theory. So, D will follow A. Going through the options, option ‘c’ has the correct sequence.</span></p>",
                    solution_hi: " <p>18.(c) </span><span style=\"font-family:Palanquin Dark\">BCAD</span></p> <p><span style=\"font-family:Palanquin Dark\">दिया गया वाक्य phrase ‘such poststructural pronouncements’ से प्रारम्भ होता है। Part B उस क्षेत्र को बताता है जिसमें घोषणाएँ की गई हैं और Part C साहित्यिक सिद्धांत की सामान्य विशेषता के बारे में बात करता है। इसलिए, B के बाद C आएगा। इसके अलावा, Part A उस समुदाय को बताता है जिसमें साहित्यिक सिद्धांत का अभ्यास किया जाता है और Part D साहित्यिक सिद्धांत का अभ्यास करने वाले देशों को बताता है। इसलिए, A के बाद D आएगा। Options के माध्यम से जाने पर, option ‘c’ में सही sequence है।</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "7",
                    question_en: "<p>19. <span style=\"font-family: Palanquin Dark;\">Select the most appropriate ANTONYM of the given word.</span></p>\\r\\n<p><span style=\"font-family: Palanquin Dark;\"> Fundamental</span></p>\\n",
                    question_hi: "<p>19. <span style=\"font-family: Palanquin Dark;\">Select the most appropriate ANTONYM of the given word.</span></p>\\r\\n<p><span style=\"font-family: Palanquin Dark;\"> Fundamental</span></p>\\n",
                    options_en: ["<p>Impact</p>\\n", "<p>Neutral</p>\\n", 
                                "<p>Least</p>\\n", "<p>Link</p>\\n"],
                    options_hi: ["<p>Impact</p>\\n", "<p>Neutral</p>\\n",
                                "<p>Least</p>\\n", "<p>Link</p>\\n"],
                    solution_en: "<p>19.(c) <strong>Least</strong>- <span style=\"font-family: Palanquin Dark;\">smallest in amount, extent or significance.</span></p>\\r\\n<p><span style=\"font-family: Palanquin Dark;\"><strong>Fundamental</strong>-</span><span style=\"font-family: Palanquin Dark;\"> serving as an essential component.</span></p>\\r\\n<p><span style=\"font-family: Palanquin Dark;\"><strong>Impact</strong>-</span><span style=\"font-family: Palanquin Dark;\"> a marked effect or influence.</span></p>\\r\\n<p><span style=\"font-family: Palanquin Dark;\"><strong>Neutral</strong>- </span><span style=\"font-family: Palanquin Dark;\">not supporting or helping either side in a conflict.</span></p>\\r\\n<p><span style=\"font-family: Palanquin Dark;\"><strong>Link-</strong> </span><span style=\"font-family: Palanquin Dark;\">a relationship between two things or situations.</span></p>\\n",
                    solution_hi: "<p>19.(c) <strong>Least</strong> <span style=\"font-family: Palanquin Dark;\">(&#2344;&#2381;&#2351;&#2370;&#2344;&#2340;&#2350;) - smallest in amount, extent or significance.</span></p>\\r\\n<p><strong><span style=\"font-family: Palanquin Dark;\">Fundamental</span></strong><span style=\"font-family: Palanquin Dark;\"><strong> </strong>(&#2350;&#2380;&#2354;&#2367;&#2325;) - serving as an essential component.</span></p>\\r\\n<p><strong><span style=\"font-family: Palanquin Dark;\">Impact</span></strong><span style=\"font-family: Palanquin Dark;\"><strong> </strong>(&#2346;&#2381;&#2352;&#2349;&#2366;&#2357;) - a marked effect or influence.</span></p>\\r\\n<p><strong><span style=\"font-family: Palanquin Dark;\">Neutral </span></strong><span style=\"font-family: Palanquin Dark;\">(</span><span style=\"font-family: Palanquin;\">&#2344;&#2367;&#2359;&#2381;&#2346;&#2325;&#2381;&#2359;</span><span style=\"font-family: Palanquin;\">) -</span><span style=\"font-family: Palanquin;\"> </span><span style=\"font-family: Palanquin;\">not supporting or helping either side in a conflict.</span></p>\\r\\n<p><strong><span style=\"font-family: Palanquin;\">Link </span></strong><span style=\"font-family: Palanquin Dark;\">(&#2360;&#2350;&#2381;&#2348;&#2344;&#2381;&#2343;) - a relationship between two things or situations.</span></p>\\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "7",
                    question_en: " <p>20. </span><span style=\"font-family:Palanquin Dark\">Select the most appropriate homophone to fill in the blank. </span></p> <p><span style=\"font-family:Palanquin Dark\">Even animals find it difficult to ____________ the loss of their loved ones.</span></p>",
                    question_hi: " <p>20. </span><span style=\"font-family:Palanquin Dark\">Select the most appropriate homophone to fill in the blank. </span></p> <p><span style=\"font-family:Palanquin Dark\">Even animals find it difficult to ____________ the loss of their loved ones.</span></p>",
                    options_en: [" <p> bear </span></p>", " <p> wear </span></p>", 
                                " <p> where </span></p>", " <p> bare </span></p>"],
                    options_hi: [" <p> bear </span></p>", " <p> wear </span></p>",
                                " <p> where </span></p>", " <p> bare </span></p>"],
                    solution_en: " <p>20.(a) </span><span style=\"font-family:Palanquin Dark\">bear</span></p> <p><span style=\"font-family:Palanquin Dark\">‘Bear’ means to accept something painful or unpleasant. The given passage states that even animals find it difficult to bear the loss of their loved ones. Hence, ‘bear’ is the most appropriate answer.</span></p>",
                    solution_hi: " <p>20.(a) </span><span style=\"font-family:Palanquin Dark\">bear</span></p> <p><span style=\"font-family:Palanquin Dark\">‘Bear’ का अर्थ है किसी दुखद या अप्रिय बात को स्वीकार करना। दिए गए passage में कहा गया है कि जानवरों को भी अपने प्रियजनों के नुकसान को सहन करना मुश्किल लगता है। अतः ‘bear’ सबसे उपयुक्त उत्तर है।</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "7",
                    question_en: "<p>21.<strong> Cloze Test:</strong></p>\\r\\n<p><span style=\"font-family: Palanquin Dark;\">Unemployment arises from a variety of _____________ (21). One which is always recurring and the effects of which we have had a recent example, is the disorganisation of industry resulting from a long war; this is a serious, _____________ (22) admitting of no easy _____________ (23) at the best of times. Again there is the unemployment which followed a marked diminution in the _____________ (24) of any raw product, such as cotton; fewer hands are required in the mills and the factories. We may call this cause of bad harvests. Similar, but more serious is the effect of changes in industry due to the invention of machinery which does more work and require ____________ (25) hands. Yet another cause is strike or lockout and this is more to be deplored because such a stoppage is due to a very trivial matter, perhaps the men are working half an hour longer than what their Union permits.</span></p>\\r\\n<p><span style=\"font-family: Palanquin Dark;\">Select the most appropriate option to fill in blank number 21.</span></p>\\n",
                    question_hi: "<p>21. <strong>Cloze Test:</strong></p>\\r\\n<p><span style=\"font-family: Palanquin Dark;\">Unemployment arises from a variety of _____________ (21). One which is always recurring and the effects of which we have had a recent example, is the disorganisation of industry resulting from a long war; this is a serious, _____________ (22) admitting of no easy _____________ (23) at the best of times. Again there is the unemployment which followed a marked diminution in the _____________ (24) of any raw product, such as cotton; fewer hands are required in the mills and the factories. We may call this cause of bad harvests. Similar, but more serious is the effect of changes in industry due to the invention of machinery which does more work and require ____________ (25) hands. Yet another cause is strike or lockout and this is more to be deplored because such a stoppage is due to a very trivial matter, perhaps the men are working half an hour longer than what their Union permits.</span></p>\\r\\n<p><span style=\"font-family: Palanquin Dark;\">Select the most appropriate option to fill in blank number 21.</span></p>\\n",
                    options_en: ["<p>causes</p>\\n", "<p>thoughts</p>\\n", 
                                "<p>things</p>\\n", "<p>types</p>\\n"],
                    options_hi: ["<p>causes</p>\\n", "<p>thoughts</p>\\n",
                                "<p>things</p>\\n", "<p>types</p>\\n"],
                    solution_en: "<p>21.(a) <span style=\"font-family: Palanquin Dark;\">Causes</span></p>\\r\\n<p><span style=\"font-family: Palanquin Dark;\">&lsquo;Cause&rsquo; means a person or thing that gives rise to an action or phenomenon. The given passage states that unemployment arises from a variety of causes. Hence, &lsquo;causes&rsquo; is the most appropriate answer.</span></p>\\n",
                    solution_hi: "<p>21.(a) <span style=\"font-family: Palanquin Dark;\">Causes</span></p>\\r\\n<p><span style=\"font-family: Palanquin Dark;\">&lsquo;Cause&rsquo; &#2325;&#2366; &#2309;&#2352;&#2381;&#2341; &#2361;&#2376; &#2325;&#2379;&#2312; &#2357;&#2381;&#2351;&#2325;&#2381;&#2340;&#2367; &#2351;&#2366; &#2357;&#2360;&#2381;&#2340;&#2369; &#2332;&#2367;&#2360;&#2325;&#2368; &#2357;&#2332;&#2361; &#2360;&#2375; &#2325;&#2379;&#2312; action &#2351;&#2366; phenomenon &#2361;&#2379;&#2340;&#2366; &#2361;&#2376;&#2404; &#2342;&#2367;&#2319; &#2327;&#2319; passage &#2350;&#2375;&#2306; &#2325;&#2361;&#2366; &#2327;&#2351;&#2366; &#2361;&#2376; &#2325;&#2367; &#2348;&#2375;&#2352;&#2379;&#2332;&#2327;&#2366;&#2352;&#2368; &#2357;&#2367;&#2349;&#2367;&#2344;&#2381;&#2344; &#2325;&#2366;&#2352;&#2339;&#2379;&#2306; &#2360;&#2375; &#2313;&#2340;&#2381;&#2346;&#2344;&#2381;&#2344; &#2361;&#2379;&#2340;&#2368; &#2361;&#2376;&#2404; &#2309;&#2340;&#2307; &lsquo;causes&rsquo; &#2360;&#2348;&#2360;&#2375; &#2313;&#2346;&#2351;&#2369;&#2325;&#2381;&#2340; &#2313;&#2340;&#2381;&#2340;&#2352; &#2361;&#2376;&#2404;</span></p>\\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "7",
                    question_en: "<p>22. <strong>Cloze Test:</strong></p>\\r\\n<p><span style=\"font-family: Palanquin Dark;\">Unemployment arises from a variety of _____________ (21). One which is always recurring and the effects of which we have had a recent example, is the disorganisation of industry resulting from a long war; this is a serious, _____________ (22) admitting of no easy _____________ (23) at the best of times. Again there is the unemployment which followed a marked diminution in the _____________ (24) of any raw product, such as cotton; fewer hands are required in the mills and the factories. We may call this cause of bad harvests. Similar, but more serious is the effect of changes in industry due to the invention of machinery which does more work and require ____________ (25) hands. Yet another cause is strike or lockout and this is more to be deplored because such a stoppage is due to a very trivial matter, perhaps the men are working half an hour longer than what their Union permits.</span></p>\\r\\n<p><span style=\"font-family: Palanquin Dark;\"> </span><span style=\"font-family: Palanquin Dark;\">Select the most appropriate option to fill in blank number 22.</span></p>\\n",
                    question_hi: "<p>22. <strong>Cloze Test:</strong></p>\\r\\n<p><span style=\"font-family: Palanquin Dark;\">Unemployment arises from a variety of _____________ (21). One which is always recurring and the effects of which we have had a recent example, is the disorganisation of industry resulting from a long war; this is a serious, _____________ (22) admitting of no easy _____________ (23) at the best of times. Again there is the unemployment which followed a marked diminution in the _____________ (24) of any raw product, such as cotton; fewer hands are required in the mills and the factories. We may call this cause of bad harvests. Similar, but more serious is the effect of changes in industry due to the invention of machinery which does more work and require ____________ (25) hands. Yet another cause is strike or lockout and this is more to be deplored because such a stoppage is due to a very trivial matter, perhaps the men are working half an hour longer than what their Union permits.</span></p>\\r\\n<p><span style=\"font-family: Palanquin Dark;\">Select the most appropriate option to fill in blank number 22.</span></p>\\n",
                    options_en: ["<p>difference</p>\\n", "<p>discussion</p>\\n", 
                                "<p>nature</p>\\n", "<p>problem</p>\\n"],
                    options_hi: ["<p>difference</p>\\n", "<p>discussion</p>\\n",
                                "<p>nature</p>\\n", "<p>problem</p>\\n"],
                    solution_en: "<p>22.(d) <span style=\"font-family: Palanquin Dark;\">Problem</span></p>\\r\\n<p><span style=\"font-family: Palanquin Dark;\">The given passage states that </span><span style=\"font-family: Palanquin Dark;\">disorganisation</span><span style=\"font-family: Palanquin Dark;\"> is a serious problem. Hence, &lsquo;problem&rsquo; is the most appropriate answer.</span></p>\\n",
                    solution_hi: "<p>22.(d) <span style=\"font-family: Palanquin Dark;\">Problem</span></p>\\r\\n<p><span style=\"font-family: Palanquin Dark;\">&#2342;&#2367;&#2319; &#2327;&#2319; passage &#2350;&#2375;&#2306; &#2325;&#2361;&#2366; &#2327;&#2351;&#2366; &#2361;&#2376; &#2325;&#2367; &#2309;&#2357;&#2381;&#2351;&#2357;&#2360;&#2381;&#2341;&#2366; &#2319;&#2325; &#2327;&#2306;&#2349;&#2368;&#2352; &#2360;&#2350;&#2360;&#2381;&#2351;&#2366; &#2361;&#2376;&#2404; &#2309;&#2340;&#2307; &lsquo;problem&rsquo; &#2360;&#2348;&#2360;&#2375; &#2313;&#2346;&#2351;&#2369;&#2325;&#2381;&#2340; &#2313;&#2340;&#2381;&#2340;&#2352; &#2361;&#2376;&#2404;</span></p>\\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "23",
                    section: "7",
                    question_en: "<p>23. <strong>Cloze Test:</strong></p>\\r\\n<p><span style=\"font-family: Palanquin Dark;\">Unemployment arises from a variety of _____________ (21). One which is always recurring and the effects of which we have had a recent example, is the disorganisation of industry resulting from a long war; this is a serious, _____________ (22) admitting of no easy _____________ (23) at the best of times. Again there is the unemployment which followed a marked diminution in the _____________ (24) of any raw product, such as cotton; fewer hands are required in the mills and the factories. We may call this cause of bad harvests. Similar, but more serious is the effect of changes in industry due to the invention of machinery which does more work and require ____________ (25) hands. Yet another cause is strike or lockout and this is more to be deplored because such a stoppage is due to a very trivial matter, perhaps the men are working half an hour longer than what their Union permits.</span></p>\\r\\n<p><span style=\"font-family: Palanquin Dark;\">Select the most appropriate option to fill in blank number 23.</span></p>\\n",
                    question_hi: "<p>23. <strong>Cloze Test:</strong></p>\\r\\n<p><span style=\"font-family: Palanquin Dark;\">Unemployment arises from a variety of _____________ (21). One which is always recurring and the effects of which we have had a recent example, is the disorganisation of industry resulting from a long war; this is a serious, _____________ (22) admitting of no easy _____________ (23) at the best of times. Again there is the unemployment which followed a marked diminution in the _____________ (24) of any raw product, such as cotton; fewer hands are required in the mills and the factories. We may call this cause of bad harvests. Similar, but more serious is the effect of changes in industry due to the invention of machinery which does more work and require ____________ (25) hands. Yet another cause is strike or lockout and this is more to be deplored because such a stoppage is due to a very trivial matter, perhaps the men are working half an hour longer than what their Union permits.</span></p>\\r\\n<p><span style=\"font-family: Palanquin Dark;\">Select the most appropriate option to fill in blank number 23.</span></p>\\n",
                    options_en: ["<p>key</p>\\n", "<p>clue</p>\\n", 
                                "<p>answer</p>\\n", "<p>solution</p>\\n"],
                    options_hi: ["<p>key</p>\\n", "<p>clue</p>\\n",
                                "<p>answer</p>\\n", "<p>solution</p>\\n"],
                    solution_en: "<p>23.(d) <span style=\"font-family: Palanquin Dark;\">Solution</span></p>\\r\\n<p><span style=\"font-family: Palanquin Dark;\">The given passage states that the problem of disorganisation has no easy solution. Hence, &lsquo;solution&rsquo; is the most appropriate answer.</span></p>\\n",
                    solution_hi: "<p>23.(d) <span style=\"font-family: Palanquin Dark;\">Solution</span></p>\\r\\n<p><span style=\"font-family: Palanquin Dark;\">&#2342;&#2367;&#2319; &#2327;&#2319; passage &#2350;&#2375;&#2306; &#2325;&#2361;&#2366; &#2327;&#2351;&#2366; &#2361;&#2376; &#2325;&#2367; &#2309;&#2357;&#2381;&#2351;&#2357;&#2360;&#2381;&#2341;&#2366; &#2325;&#2368; &#2360;&#2350;&#2360;&#2381;&#2351;&#2366; &#2325;&#2366; &#2325;&#2379;&#2312; &#2310;&#2360;&#2366;&#2344; &#2360;&#2350;&#2366;&#2343;&#2366;&#2344; &#2344;&#2361;&#2368;&#2306; &#2361;&#2376;&#2404; &#2309;&#2340;&#2307; &lsquo;solution&rsquo; &#2360;&#2348;&#2360;&#2375; &#2313;&#2346;&#2351;&#2369;&#2325;&#2381;&#2340; &#2313;&#2340;&#2381;&#2340;&#2352; &#2361;&#2376;&#2404;</span></p>\\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "24",
                    section: "7",
                    question_en: "<p>24. <strong>Cloze Test:</strong></p>\\r\\n<p><span style=\"font-family: Palanquin Dark;\">Unemployment arises from a variety of _____________ (21). One which is always recurring and the effects of which we have had a recent example, is the disorganisation of industry resulting from a long war; this is a serious, _____________ (22) admitting of no easy _____________ (23) at the best of times. Again there is the unemployment which followed a marked diminution in the _____________ (24) of any raw product, such as cotton; fewer hands are required in the mills and the factories. We may call this cause of bad harvests. Similar, but more serious is the effect of changes in industry due to the invention of machinery which does more work and require ____________ (25) hands. Yet another cause is strike or lockout and this is more to be deplored because such a stoppage is due to a very trivial matter, perhaps the men are working half an hour longer than what their Union permits.</span></p>\\r\\n<p><span style=\"font-family: Palanquin Dark;\"> </span><span style=\"font-family: Palanquin Dark;\">Select the most appropriate option to fill in blank number 24.</span></p>\\n",
                    question_hi: "<p>24. <strong>Cloze Test:</strong></p>\\r\\n<p><span style=\"font-family: Palanquin Dark;\">Unemployment arises from a variety of _____________ (21). One which is always recurring and the effects of which we have had a recent example, is the disorganisation of industry resulting from a long war; this is a serious, _____________ (22) admitting of no easy _____________ (23) at the best of times. Again there is the unemployment which followed a marked diminution in the _____________ (24) of any raw product, such as cotton; fewer hands are required in the mills and the factories. We may call this cause of bad harvests. Similar, but more serious is the effect of changes in industry due to the invention of machinery which does more work and require ____________ (25) hands. Yet another cause is strike or lockout and this is more to be deplored because such a stoppage is due to a very trivial matter, perhaps the men are working half an hour longer than what their Union permits.</span></p>\\r\\n<p><span style=\"font-family: Palanquin Dark;\">Select the most appropriate option to fill in blank number 24</span></p>\\n",
                    options_en: ["<p>material</p>\\n", "<p>quality</p>\\n", 
                                "<p>quantity</p>\\n", "<p>cloth</p>\\n"],
                    options_hi: ["<p>material</p>\\n", "<p>quality</p>\\n",
                                "<p>quantity</p>\\n", "<p>cloth</p>\\n"],
                    solution_en: "<p>24.(c) <span style=\"font-family: Palanquin Dark;\">Quantity</span></p>\\r\\n<p><span style=\"font-family: Palanquin Dark;\">&lsquo;Quantity&rsquo; means the amount of a material. The given passage states that there is a marked decrease in the quantity of any raw product, such as cotton. Hence, &lsquo;quantity&rsquo; is the most appropriate answer.</span></p>\\n",
                    solution_hi: "<p>24.(c) <span style=\"font-family: Palanquin Dark;\">Quantity</span></p>\\r\\n<p><span style=\"font-family: Palanquin Dark;\">&lsquo;Quantity&rsquo; &#2325;&#2366; &#2309;&#2352;&#2381;&#2341; &#2361;&#2376; &#2325;&#2367;&#2360;&#2368; &#2346;&#2342;&#2366;&#2352;&#2381;&#2341; &#2325;&#2368; &#2350;&#2366;&#2340;&#2381;&#2352;&#2366;&#2404; &#2342;&#2367;&#2319; &#2327;&#2319; passage &#2350;&#2375;&#2306; &#2325;&#2361;&#2366; &#2327;&#2351;&#2366; &#2361;&#2376; &#2325;&#2367; &#2325;&#2346;&#2366;&#2360; &#2332;&#2376;&#2360;&#2375; &#2325;&#2367;&#2360;&#2368; &#2349;&#2368; &#2325;&#2330;&#2381;&#2330;&#2375; &#2313;&#2340;&#2381;&#2346;&#2366;&#2342; &#2325;&#2368; &#2350;&#2366;&#2340;&#2381;&#2352;&#2366; &#2350;&#2375;&#2306; &#2313;&#2354;&#2381;&#2354;&#2375;&#2326;&#2344;&#2368;&#2351; &#2325;&#2350;&#2368; &#2310;&#2312; &#2361;&#2376;&#2404; &#2309;&#2340;&#2307; &lsquo;quantity&rsquo; &#2360;&#2348;&#2360;&#2375; &#2313;&#2346;&#2351;&#2369;&#2325;&#2381;&#2340; &#2313;&#2340;&#2381;&#2340;&#2352; &#2361;&#2376;&#2404;</span></p>\\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "25",
                    section: "misc",
                    question_en: "<p>25. <strong>Cloze Test:</strong></p>\\r\\n<p><span style=\"font-family: Palanquin Dark;\">Unemployment arises from a variety of _____________ (21). One which is always recurring and the effects of which we have had a recent example, is the disorganisation of industry resulting from a long war; this is a serious, _____________ (22) admitting of no easy _____________ (23) at the best of times. Again there is the unemployment which followed a marked diminution in the _____________ (24) of any raw product, such as cotton; fewer hands are required in the mills and the factories. We may call this cause of bad harvests. Similar, but more serious is the effect of changes in industry due to the invention of machinery which does more work and require ____________ (25) hands. Yet another cause is strike or lockout and this is more to be deplored because such a stoppage is due to a very trivial matter, perhaps the men are working half an hour longer than what their Union permits.</span></p>\\r\\n<p><span style=\"font-family: Palanquin Dark;\">Select the most appropriate option to fill in blank number 25.</span></p>\\n",
                    question_hi: " <p>25. Cloze Test:</span></p> <p><span style=\"font-family:Palanquin Dark\">In the following passage, some words have been deleted. Read the passage carefully and select the most appropriate option to fill in each blank.</span></p> <p><span style=\"font-family:Palanquin Dark\">Unemployment arises from a variety of _____________ (21). One which is always recurring and the effects of which we have had a recent example, is the disorganisation of industry resulting from a long war; this is a serious, _____________  (22) admitting of no easy _____________  (23) at the best of times. Again there is the unemployment which followed a marked diminution in the _____________  (24) of any raw product, such as cotton; fewer hands are required in the mills and the factories. We may call this cause of bad harvests. Similar, but more serious is the effect of changes in industry due to the invention of machinery which does more work and require ____________ (25) hands. Yet another cause is strike or lockout and this is more to be deplored because such a stoppage is due to a very trivial matter, perhaps the men are working half an hour longer than what their Union permits.</span></p> <p><span style=\"font-family:Palanquin Dark\">Select the most appropriate option to fill in blank number 25.</span></p>",
                    options_en: ["<p>fewer</p>\\n", "<p>more</p>\\n", 
                                "<p>many</p>\\n", "<p>a lot of</p>\\n"],
                    options_hi: [" <p> fewer</span></p>", " <p> more</span></p>",
                                " <p> many</span></p>", " <p> a lot of</span></p>"],
                    solution_en: "<p>25.(a) <span style=\"font-family: Palanquin Dark;\">Fewer</span></p>\\r\\n<p><span style=\"font-family: Palanquin Dark;\">When we connect two or more adjectives with a conjunction, their degree should be the same. Similarly, in the given sentence, the conjunction &lsquo;and&rsquo; connects two adjectives. First adjective(more serious) is comparative degree, so the second adjective will also be comparative degree. Hence, &lsquo;fewer&rsquo; is the most appropriate answer.</span></p>\\n",
                    solution_hi: " <p>25.(a) </span><span style=\"font-family:Palanquin Dark\">Fewer</span></p> <p><span style=\"font-family:Palanquin Dark\">जब हम दो या दो से अधिक adjectives को conjunction से जोड़ते हैं तो उनकी degree समान होनी चाहिए। इसी प्रकार दिए गए वाक्य में conjunction ‘and’ दो adjectives को जोड़ता है। पहला adjective (more serious) comparative degree है, इसलिए दूसरा adjective भी comparative degree  होगा। अतः ‘fewer’ सबसे उपयुक्त उत्तर है।</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>