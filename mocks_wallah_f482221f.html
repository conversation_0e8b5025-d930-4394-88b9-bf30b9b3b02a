<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">60:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 100</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">100</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 60 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["6"] = {
                name: "Reasoning",
                start: 0,
                end: 23
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="6">Reasoning</option>`;
            sections["18"] = {
                name: "General Awareness",
                start: 24,
                end: 48
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="18">General Awareness</option>`;
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 49,
                end: 73
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["7"] = {
                name: "English",
                start: 74,
                end: 98
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="7">English</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 99,
                end: 99
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "6",
                    question_en: "<p>1. Four positions of the same dice are shown. Select the number that will be on the face opposite to the one showing &lsquo;3&rsquo;<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744276374211.png\" alt=\"rId4\" width=\"346\" height=\"71\"></p>",
                    question_hi: "<p>1. एक ही पासे की चार अवस्थाएँ दी गयी हैं । उस संख्या का चयन कीजिए जो &lsquo;3&rsquo; वाले फलक के विपरीत फलक पर होगी ? <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744276374211.png\" alt=\"rId4\" width=\"346\" height=\"71\"></p>",
                    options_en: ["<p>5</p>", "<p>6</p>", 
                                "<p>4</p>", "<p>2</p>"],
                    options_hi: ["<p>5</p>", "<p>6</p>",
                                "<p>4</p>", "<p>2</p>"],
                    solution_en: "<p>1.(a) After observing the four positions of the dice, the following conclusions are derived:<br>4 &rArr;&nbsp;6<br>1 &rArr; 2<br><strong>5 &rArr; 3</strong><br>So, the face opposite to &lsquo;3&rsquo; is &lsquo;5&rsquo;.</p>",
                    solution_hi: "<p>1.(a) पासे की चार स्थितियों को देखने के बाद, निम्नलिखित निष्कर्ष निकलते हैं:<br>4 &rArr;&nbsp;6<br>1 &rArr; 2<br><strong>5 &rArr; 3</strong><br>अतः, \'3\' के सामने का फलक \'5\' है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "6",
                    question_en: "<p>2. Find the odd one out.</p>",
                    question_hi: "<p>2. अलग शब्द का चयन कीजिए।</p>",
                    options_en: ["<p>Meanness</p>", "<p>Prosperity</p>", 
                                "<p>Lack</p>", "<p>Pauperism</p>"],
                    options_hi: ["<p>दरिद्रता</p>", "<p>समृद्धि</p>",
                                "<p>अभाव</p>", "<p>कंगाली</p>"],
                    solution_en: "<p>2.(b) All the other options mean negative habit, behaviour or situation in terms of money and amenities. Whereas, Prosperity means &lsquo;successful in material terms, flourishing financially&rsquo;.</p>",
                    solution_hi: "<p>2.(b) अन्य सभी विकल्पों का अर्थ है धन और सुविधाओं के मामले में नकारात्मक आदत, व्यवहार या स्थिति। जबकि, समृद्धि का अर्थ है \'भौतिक दृष्टि से सफल, आर्थिक रूप से समृद्ध\'।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "6",
                    question_en: "<p>3. Select the set of letters that when sequentially placed in the blanks of the given letter series will complete the series.<br>f_hg_fh_gf_hg_fh_g</p>",
                    question_hi: "<p>3. अक्षरों के उस समूह का चयन कीजिए जिन्हें दी गयी अक्षर श्रृंखला के खाली स्थानों में क्रमिक रूप से भरने पर यह श्रृंखला पूरी हो जाएगी।<br>f_hg_fh_gf_hg_fh_g</p>",
                    options_en: ["<p>g , h , f , g , h , f</p>", "<p>f, g , h , f , g , h</p>", 
                                "<p>g , f , g , f , h , f</p>", "<p>h , f , g , h , f , g</p>"],
                    options_hi: ["<p>g, h, f, g, h, f</p>", "<p>f, g, h, f, g, h</p>",
                                "<p>g, f, g, f, h, f</p>", "<p>h, f, g, h, f, g</p>"],
                    solution_en: "<p>3.(a) The required pattern is:<br>f <span style=\"text-decoration: underline;\">g </span>&nbsp;h/g <span style=\"text-decoration: underline;\">h</span> f/h <span style=\"text-decoration: underline;\">f</span> g/f g h/g <span style=\"text-decoration: underline;\">h</span> f/h <span style=\"text-decoration: underline;\">f</span> g</p>",
                    solution_hi: "<p>3.(a) आवश्यक पैटर्न है:<br>f <span style=\"text-decoration: underline;\">g </span>&nbsp;h/g <span style=\"text-decoration: underline;\">h</span> f/h <span style=\"text-decoration: underline;\">f</span> g/f g h/g <span style=\"text-decoration: underline;\">h</span> f/h <span style=\"text-decoration: underline;\">f</span> g</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "6",
                    question_en: "<p>4. Select the figure that can replace thequestion mark(?) in the following series <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744276374332.png\" alt=\"rId5\" width=\"335\" height=\"77\"></p>",
                    question_hi: "<p>4. उस आकृति का चयन कीजिए जो निम्नलिखित श्रृंखला में प्रश्न चिन्ह (?) के स्थान पर आ सकती है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744276374332.png\" alt=\"rId5\" width=\"335\" height=\"77\"></p>",
                    options_en: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744276374447.png\" alt=\"rId6\" width=\"78\" height=\"68\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744276374603.png\" alt=\"rId7\" width=\"78\" height=\"64\"></p>", 
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744276374692.png\" alt=\"rId8\" width=\"83\" height=\"68\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744276374785.png\" alt=\"rId9\" width=\"79\" height=\"67\"></p>"],
                    options_hi: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744276374447.png\" alt=\"rId6\" width=\"78\" height=\"68\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744276374603.png\" alt=\"rId7\" width=\"78\" height=\"64\"></p>",
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744276374692.png\" alt=\"rId8\" width=\"78\" height=\"64\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744276374785.png\" alt=\"rId9\" width=\"79\" height=\"67\"></p>"],
                    solution_en: "<p>4.(d) The figures ♢and @ is moving clockwise within the square and in every successive figure the following patterns are observed 〇, # , +, and ☆ .</p>",
                    solution_hi: "<p>4.(d) आंकड़े ♢and @ वर्ग के भीतर दक्षिणावर्त घूम रहे हैं और प्रत्येक क्रमिक आकृति में निम्नलिखित पैटर्न देखे गए हैं 〇, # , +, and ☆</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "6",
                    question_en: "<p>5. Arrange the following in a logical sequence from small to big.<br>1. Crocodile <br>2. Lizard <br>3. Whale <br>4. Housefly <br>5. Monkey</p>",
                    question_hi: "<p>5. निम्नलिखित को एक तार्किक रूप से छोटे से बड़े के क्रम में व्यवस्थित कीजिए। <br>1. मगरमच्छ <br>2. छिपकली <br>3. व्हेल <br>4 .घरेलू मक्खी <br>5. बंदर</p>",
                    options_en: ["<p>4, 5, 2, 1,3</p>", "<p>4, 3, 2, 1, 5</p>", 
                                "<p>3, 5, 4, 1, 2</p>", "<p>4, 2, 5, 1, 3</p>"],
                    options_hi: ["<p>4, 5, 2, 1, 3</p>", "<p>4, 3, 2, 1, 5</p>",
                                "<p>3, 5, 4, 1, 2</p>", "<p>4, 2, 5, 1, 3</p>"],
                    solution_en: "<p>5.(d) The logical sequence according to their size: <br>Housefly&nbsp;&lt; Lizard &lt; Monkey &lt; Crocodile &lt; Whale<strong id=\"docs-internal-guid-0a07e119-7fff-2adf-be7e-54c43ad81e2e\"></strong>&nbsp;<lizard <=\"\" monkey=\"\" crocodile=\"\" whale<=\"\" body=\"\"></lizard></p>",
                    solution_hi: "<p>5.(d) उनके आकार के अनुसार तार्किक क्रम: <br>हाउसफ्लाई &lt; छिपकली &lt; बंदर &lt; मगरमच्छ &lt; व्हेल</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "6",
                    question_en: "<p>6. Select the option figure in which the given figure is embedded (rotation is NOT allowed).<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744276374874.png\" alt=\"rId10\" width=\"105\" height=\"101\"></p>",
                    question_hi: "<p>6. विकल्पों में से उस आकृति का चयन कीजिए जिसमें दी गयी आकृति अंतर्निहित है ।(घुमाने की अनुमति नहीं है)&nbsp;<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744276374874.png\" alt=\"rId10\" width=\"105\" height=\"101\"></p>",
                    options_en: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744276374963.png\" alt=\"rId11\" width=\"85\" height=\"83\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744276375103.png\" alt=\"rId12\" width=\"85\" height=\"80\"></p>", 
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744276375211.png\" alt=\"rId13\" width=\"85\" height=\"85\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744276375312.png\" alt=\"rId14\" width=\"85\" height=\"82\"></p>"],
                    options_hi: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744276374963.png\" alt=\"rId11\" width=\"85\" height=\"83\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744276375103.png\" alt=\"rId12\" width=\"86\" height=\"81\"></p>",
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744276375534.png\" alt=\"rId16\" width=\"84\" height=\"84\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744276375312.png\" alt=\"rId14\" width=\"85\" height=\"82\"></p>"],
                    solution_en: "<p>6.(d) The image given below shows how the question figure is embedded in option (d).<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744276375633.png\" alt=\"rId17\" width=\"87\" height=\"85\"></p>",
                    solution_hi: "<p>6.(d) नीचे दिया गया चित्र दिखाता है कि कैसे प्रश्न आकृति विकल्प (d) में सन्निहित है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744276375633.png\" alt=\"rId17\" width=\"87\" height=\"85\"></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "6",
                    question_en: "<p>7. Select the option that is related to the third word in the same way as the second word is related to the first word.<br>Ministers : Council :: Sailors : ?</p>",
                    question_hi: "<p>7. उस विकल्प का चयन कीजिए जो तीसरे शब्द से ठीक उसी प्रकार संबंधित है जिस प्रकार दूसरा शब्द पहले शब्द से संबंधित है।<br>मंत्री : परिषद :: नाविक : ?</p>",
                    options_en: ["<p>Ship</p>", "<p>Captain</p>", 
                                "<p>Crew</p>", "<p>Sea</p>"],
                    options_hi: ["<p>जहाज़</p>", "<p>कप्तान</p>",
                                "<p>दल</p>", "<p>समुद्र</p>"],
                    solution_en: "<p>7.(c) Here, &lsquo;Council&rsquo; means a group of ministers. Similarly, crew means a group of sailors.</p>",
                    solution_hi: "<p>7.(c) यहाँ, \'परिषद\' का अर्थ है मंत्रियों का समूह। इसी तरह, चालक दल का अर्थ नाविकों का एक समूह है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "6",
                    question_en: "<p>8. How many triangles are present in the given figure ?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744276375757.png\" alt=\"rId18\" width=\"145\" height=\"119\"></p>",
                    question_hi: "<p>8. दी गयी आकृति में कितने त्रिभुज मौजूद हैं ?&nbsp;<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744276375757.png\" alt=\"rId18\" width=\"145\" height=\"119\"></p>",
                    options_en: ["<p>21</p>", "<p>22</p>", 
                                "<p>23</p>", "<p>20</p>"],
                    options_hi: ["<p>21</p>", "<p>22</p>",
                                "<p>23</p>", "<p>20</p>"],
                    solution_en: "<p>8.(c) There are a total of 23 distinct triangles in the given figure.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744276375972.png\" alt=\"rId20\" width=\"156\" height=\"147\"><br>ABQ, BCD, QBD, PQN, QUX, NQX, UDX, DEF, DXF, MNL, LNK, NKJ, NXJ, FXJ, FIJ, FGH, FIH, BNF, BLH, LNJ, JFH, JNF, XQD.</p>",
                    solution_hi: "<p>8.(c) दी गई आकृति में कुल 23 अलग-अलग त्रिभुज हैं।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744276375972.png\" alt=\"rId20\" width=\"156\" height=\"147\"><br>ABQ, BCD, QBD, PQN, QUX, NQX, UDX, DEF, DXF, MNL, LNK, NKJ, NXJ, FXJ, FIJ, FGH, FIH, BNF, BLH, LNJ, JFH, JNF, XQD.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "6",
                    question_en: "<p>9. If each letter of the English alphabet is assigned an odd numerical value in increasing order, such as A = 1, B = 3 and so on , then what will be the code of HONEY ?</p>",
                    question_hi: "<p>9. यदि अंग्रेजी वर्णमाला के प्रत्येक अक्षर को बढ़ते क्रम में एक विषम संख्यात्मक मान दिया जाए, जैसे A = 1, B = 3 तथा इसी प्रकार से आगे, तो HONEY का कूट क्या होगा ?</p>",
                    options_en: ["<p>132725745</p>", "<p>132725747</p>", 
                                "<p>152927949</p>", "<p>152927947</p>"],
                    options_hi: ["<p>132725745</p>", "<p>132725747</p>",
                                "<p>152927949</p>", "<p>152927947</p>"],
                    solution_en: "<p>9.(c) As per the given information A=1, B=3 and so on, thus place values of Honey are: <br>H = 15, O = 29, N = 27, E = 9, Y = 49</p>",
                    solution_hi: "<p>9.(c) दी गई जानकारी के अनुसार A = 1, B = 3 इत्यादि, इस प्रकार शहद के स्थानीय मान हैं:<br>H = 15, O = 29, N = 27, E = 9, Y = 49</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "6",
                    question_en: "<p>10. Select the odd letter-cluster.</p>",
                    question_hi: "<p>10. अलग अक्षर समूह का चयन कीजिए।</p>",
                    options_en: ["<p>TVW</p>", "<p>LNP</p>", 
                                "<p>FHJ</p>", "<p>DFH</p>"],
                    options_hi: ["<p>TVW</p>", "<p>LNP</p>",
                                "<p>FHJ</p>", "<p>DFH</p>"],
                    solution_en: "<p>10.(a) <strong>The logic: </strong>+2, rule is used<br>TVW = T + 2 = V , V + 1=W(odd one)<br>LNP = L + 2 = N , N + 2 = P, FHJ = F + 2 = H , H + 2 = J..so on<br>The following pattern is being followed by all the options except &lsquo; TVW&rsquo;.</p>",
                    solution_hi: "<p>10.(a) <strong>तर्क:</strong> +2, नियम का उपयोग किया जाता है<br>TVW = T + 2 = V, V + 1 = W (विषम एक)<br>LNP = L + 2 = N, N + 2 = P, FHJ = F + 2 = H, H + 2 = J&hellip;.आदि<br>निम्नलिखित पैटर्न को छोड़कर सभी विकल्पों का अनुसरण किया जा रहा है &lsquo; TVW&rsquo;.</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "6",
                    question_en: "<p>11. The two given expressions on both the side of the &lsquo;-&rsquo; sign will have the same value if two numbers from either side or both side are interchanged .Select the correct numbers to be interchanged from the given options.<br>3 + 5 &times; 4 - 24 &divide; 3 = 7 &times; 4 - 3 + 36 &divide; 6</p>",
                    question_hi: "<p>11. &lsquo;-&rsquo; चिन्ह के दोनों ओर दिए गए व्यंजकों का मान बराबर होगा यदि उनमें से किसी एक ओर या दोनों ओर की दो संख्याओं को आपस में बदल दिया जाए। दिए गए विकल्पों में से आपस में बदली जाने वाली सही संख्याओं का चयन कीजिए।<br>3 + 5 &times; 4 - 24 &divide; 3 = 7 &times; 4 - 3 + 36 &divide; 6</p>",
                    options_en: ["<p>4, 7</p>", "<p>5, 7</p>", 
                                "<p>24, 36</p>", "<p>6, 3</p>"],
                    options_hi: ["<p>4, 7</p>", "<p>5, 7</p>",
                                "<p>24, 36</p>", "<p>6, 3</p>"],
                    solution_en: "<p>11.(b) By observing all the options, we conclude that interchanging &lsquo;5 and 7&rsquo; gives the desired result.<br>LHS<br>= 3 + 7 &times; 4 - 24 &divide; 3<br>= 3 + 7 &times; 4 - 8 <br>&rArr; 3 + 28 - 8 <br>&rArr; 31 - 8 = 23<br>RHS<br>= 5 &times; 4 - 3 + 36 &divide; 6<br>= 5 &times; 4 - 3 + 6 <br>&rArr; 26 - 3 = 23</p>",
                    solution_hi: "<p>11.(b) सभी विकल्पों का अवलोकन करके, हम यह निष्कर्ष निकालते हैं कि \'5 और 7\' को आपस में बदलने से वांछित परिणाम प्राप्त होता है।<br>LHS<br>= 3 + 7 &times; 4 - 24 &divide; 3<br>= 3 + 7 &times; 4 - 8 <br>&rArr; 3 + 28 - 8 <br>&rArr; 31 - 8 = 23<br>RHS<br>= 5 &times; 4 - 3 + 36 &divide; 6<br>= 5 &times; 4 - 3 + 6 <br>&rArr; 26 - 3 = 23</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "6",
                    question_en: "<p>12. Select the option in which the words share a relationship as that shared by the given pair of words. <br>Blunder : Error</p>",
                    question_hi: "<p>12. उस विकल्प का चयन कीजिए जिसमें शब्दों का आपस में वही संबंध है जो संबंध शब्दों के दिए गए युग्म में है।<br>बड़ी भूल : अशुद्धि</p>",
                    options_en: ["<p>Euphoria : Happiness</p>", "<p>Speak : Hear</p>", 
                                "<p>War : Peace</p>", "<p>Anger : Rage</p>"],
                    options_hi: ["<p>उत्साह : ख़ुशी</p>", "<p>बोलना : सुनना</p>",
                                "<p>युद्ध : शांति</p>", "<p>क्रोध : रोष</p>"],
                    solution_en: "<p>12.(a) Blunder : The highest intensity of error. <br>Similarly, Euphoria : a feeling or state of extreme excitement and happiness.</p>",
                    solution_hi: "<p>12.(a) भूल : त्रुटि की उच्चतम तीव्रता<br>इसी तरह, यूफोरिया: अत्यधिक उत्साह और खुशी की भावना या अवस्था।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "6",
                    question_en: "<p>13. Select the option that is related to the third number in the same way as the second number is related to the first number and the sixth number is related to the fifth number.<br>72 : 108 :: 84 : ? :: 102 : 153</p>",
                    question_hi: "<p>13. उस विकल्प का चयन कीजिए जो तीसरी संख्या से ठीक उसी प्रकार संबंधित है जिस प्रकार दूसरी संख्या पहली संख्या से तथा छठी संख्या पाँचवीं संख्या से संबंधित है।<br>72 : 108 :: 84 : ? :: 102 : 153</p>",
                    options_en: ["<p>126</p>", "<p>144</p>", 
                                "<p>117</p>", "<p>135</p>"],
                    options_hi: ["<p>126</p>", "<p>144</p>",
                                "<p>117</p>", "<p>135</p>"],
                    solution_en: "<p>13.(a) Following pattern is followed<br>72 : 108&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;102 : 153<br>12 &times; 6 = 72&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 17 &times; 6 = 102 <br>12 &times; 9 = 108&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;17 &times; 9 = 153 <br>Similarly,<br>14 &times; 6 = 84<br>14 &times; 9 = <strong>126</strong></p>",
                    solution_hi: "<p>13.(a) निम्नलिखित पैटर्न का पालन किया जाता है<br>72 : 108&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;102 : 153<br>12 &times; 6 = 72&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 17 &times; 6 = 102 <br>12 &times; 9 = 108&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;17 &times; 9 = 153<br>इसी तरह,<br>14 &times; 6 = 84<br>14 &times; 9 = <strong>126</strong></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "6",
                    question_en: "<p>14. In the given Venn diagram, the triangle represents students playing table tennis , the rectangle represents students playing badminton, the circle represents female students, and the pentagon represents students playing football. The numbers given in the diagram represent the number of persons in the particular category.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744276376119.png\" alt=\"rId21\" width=\"219\" height=\"155\"> <br>How many female students play both table tennis and badminton ?</p>",
                    question_hi: "<p>14. दिए गए वेन आरेख में, &lsquo;त्रिभुज&rsquo; टेबल टेनिस खेलने वाले छात्रों को दर्शाता है, &lsquo;आयत&rsquo; बैडमिंटन खेलने वाले छात्रों को दर्शाता है, &lsquo;वृत्त&rsquo; छात्राओं को दर्शाता है, तथा &lsquo;पंचभुज&rsquo; फुटबॉल खेलने वाले छात्रों को दर्शाता है। आरेख में दी गयी संख्याएँ उस विशेष श्रेणी में मौजूद लोगों की संख्या को दर्शाती हैं ।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744276376119.png\" alt=\"rId21\" width=\"219\" height=\"155\"><br>कितनी छात्राएँ टेबल टेनिस तथा बैडमिंटन दोनों खेलती हैं ?</p>",
                    options_en: ["<p>22</p>", "<p>7</p>", 
                                "<p>9</p>", "<p>18</p>"],
                    options_hi: ["<p>22</p>", "<p>7</p>",
                                "<p>9</p>", "<p>18</p>"],
                    solution_en: "<p>14.(b) The circle represents female students, the triangle represents students playing table tennis and the rectangle represents students playing badminton. So, the number common to all is 7 because it lies in Circe, triangle and rectangle.</p>",
                    solution_hi: "<p>14.(b) निम्नलिखित पैटर्न का पालन किया जाता है | वृत्त महिला छात्रों का प्रतिनिधित्व करता है, त्रिभुज टेबल टेनिस खेलने वाले छात्रों का प्रतिनिधित्व करता है और आयत बैडमिंटन खेलने वाले छात्रों का प्रतिनिधित्व करता है। अतः, सभी के लिए उभयनिष्ठ संख्या 7 है क्योंकि यह वृत्त, त्रिभुज और आयत में स्थित है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "6",
                    question_en: "<p>15. Which of the option figures is the exact mirror image of the given figure when the mirror is held at the right side ?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744276376219.png\" alt=\"rId22\" width=\"157\" height=\"55\"></p>",
                    question_hi: "<p>15. विकल्पों में दी गयी कौन सी आकृति नीचे दी गयी आकृति का सही दर्पण प्रतिबिंब होगी, यदि दर्पण को दायीं तरफ रखा जाता है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744276376219.png\" alt=\"rId22\" width=\"157\" height=\"55\"></p>",
                    options_en: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744276376369.png\" alt=\"rId23\" width=\"153\" height=\"20\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744276376460.png\" alt=\"rId24\" width=\"163\" height=\"21\"></p>", 
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744276376597.png\" alt=\"rId25\" width=\"165\" height=\"22\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744276376706.png\" alt=\"rId26\" width=\"161\" height=\"21\"></p>"],
                    options_hi: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744276376369.png\" alt=\"rId23\" width=\"153\" height=\"20\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744276376460.png\" alt=\"rId24\" width=\"171\" height=\"22\"></p>",
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744276376597.png\" alt=\"rId25\" width=\"165\" height=\"22\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744276376706.png\" alt=\"rId26\" width=\"169\" height=\"22\"></p>"],
                    solution_en: "<p>15.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744276376706.png\" alt=\"rId26\" width=\"177\" height=\"23\"></p>",
                    solution_hi: "<p>15.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744276376706.png\" alt=\"rId26\" width=\"177\" height=\"23\"></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "6",
                    question_en: "<p>16. Select the letter- cluster that can replace the question mark (?) in the following series&nbsp;<br>aYd, fTi , kOn , pJs , ?</p>",
                    question_hi: "<p>16. उस अक्षर-समूह का चयन कीजिए जो नीचे दी गयी श्रृंखला में प्रश्नचिन्ह (?) के स्थान पर आता है।<br>aYd , fTi , kOn , pJs , ?</p>",
                    options_en: ["<p>uFw</p>", "<p>VeX</p>", 
                                "<p>uEx</p>", "<p>uEw</p>"],
                    options_hi: ["<p>uFw</p>", "<p>VeX</p>",
                                "<p>uEx</p>", "<p>uEw</p>"],
                    solution_en: "<p>16.(c) +5, rule is used &amp; middle alphabet is in capital.<br>a + 5 = f<br>Y + 5 = T<br>d + 5 = i<br>Similarly, p + 5 = <strong>u,</strong><br>J + 5 = <strong>E</strong><br>s + 5 = <strong>x</strong></p>",
                    solution_hi: "<p>16.(c) +5, नियम का प्रयोग किया जाता है<br>a + 5 = f<br>Y + 5 = T<br>d + 5 = i<br>इसी तरह, p + 5 = <strong>u,</strong><br>J + 5 = <strong>E</strong><br>s + 5 = <strong>x</strong></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "6",
                    question_en: "<p>17. Shaan has a total of Rs 5,500 with him. He buys product &lsquo;Z&rsquo; at Rs 5,000 from this sum and then sells it to another person, thus making a profit of 15% on it. With all the money he has now, he buys product &lsquo;X&rsquo; and then sells it to another person making a profit of 25% on it. What is the total money Shaan has now ?</p>",
                    question_hi: "<p>17. शान के पास कुल 5,500 रुपये हैं। वह इस राशि में से 5,000 रुपये देकर वस्तु Z को खरीदता है तथा इसे किसी अन्य व्यक्ति को बेच देता है, जिससे उसे इस पर 15% का लाभ होता है। अब उसके पास जितने रुपये हैं, उससे वह वस्तु X खरीदता है तथा इसे 25% के लाभ पर किसी अन्य व्यक्ति को बेच देता है।शान के पास अब कितने रुपये हैं ?</p>",
                    options_en: ["<p>Rs. 7,812.50</p>", "<p>Rs. 7,187.50</p>", 
                                "<p>Rs. 6,325.50</p>", "<p>Rs. 7,815.50</p>"],
                    options_hi: ["<p>Rs. 7,812.50</p>", "<p>Rs. 7,187.50</p>",
                                "<p>Rs. 6,325.50</p>", "<p>Rs. 7,815.50</p>"],
                    solution_en: "<p>17.(a) Total amount that Shaam has = Rs 5500<br>He buys product &lsquo;Z&rsquo; at Rs 5,000 from this sum and then sells it to another person, thus making a profit of 15% on it.<br>Thus now the total sum of money Shaam has = 5000 &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>115</mn><mn>100</mn></mfrac></math> <br>= Rs 5750 = Rs (5750 + 500)<br>= Rs 6250<br>Again he buys product &lsquo;X&rsquo; and then sells it to another person making a profit of 25% on it. <br>Total money now with him <br>= 6250 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>4</mn></mfrac></math> = Rs 7812.50</p>",
                    solution_hi: "<p>17.(a) शाम के पास कुल राशि = 5500<br>वह इस राशि से 5,000 रुपये में उत्पाद \'Z\' खरीदता है और फिर इसे किसी अन्य व्यक्ति को बेचता है, इस प्रकार इस पर 15% का लाभ होता है<br>इस प्रकार अब शाम की कुल धनराशि <br>= 5000 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>115</mn><mn>100</mn></mfrac></math><br>= 5750 रु <br>= (5750 + 500) रुपये<br>= 6250 रु<br>वह फिर से उत्पाद \'X\' खरीदता है और फिर उस पर 25% का लाभ अर्जित करने वाले किसी अन्य व्यक्ति को बेच देता है।<br>उसके पास अब कुल धन = 6250 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>4</mn></mfrac></math><br>= 7812.50 रु</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "6",
                    question_en: "<p>18. Select the option in which the numbers are related in the same way as are the numbers in the given set.<br>(13, 65, 117)</p>",
                    question_hi: "<p>18. उस विकल्प का चयन कीजिए जिसमें संख्याओं के बीच ठीक वही संबंध है जो संबंध दिए गए समूह की संख्याओं में है।<br>(13, 65, 117)</p>",
                    options_en: ["<p>(14, 70, 127)</p>", "<p>(15, 75, 135)</p>", 
                                "<p>(17, 85, 163)</p>", "<p>(12, 55, 109)</p>"],
                    options_hi: ["<p>(14, 70, 127)</p>", "<p>(15, 75, 135)</p>",
                                "<p>(17, 85, 163)</p>", "<p>(12, 55, 109)</p>"],
                    solution_en: "<p>18.(b) The pattern followed in the given set (13, 65, 117) is<br>13 &times; 5 = 65<br>13 &times; 9 = 117<br>Similarly only one option satisfies it i.e. (15, 75, 135)<br>15 &times; 5 = 75<br>15 &times; 9 = 135</p>",
                    solution_hi: "<p>18.(b) दिए गए सेट (13, 65, 117) में अनुसरण किया गया पैटर्न है<br>13 &times; 5 = 65<br>13 &times; 9 = 117<br>इसी प्रकार केवल एक विकल्प इसे संतुष्ट करता है अर्थात (15, 75, 135)<br>15 &times; 5 = 75<br>15 &times; 9 = 135</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "6",
                    question_en: "<p>19. Study the given pattern carefully and select the number that can replace the question mark (?) in it.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744276376794.png\" alt=\"rId27\" width=\"195\" height=\"107\"></p>",
                    question_hi: "<p>19. दिए गए प्रारूप का ध्यानपूर्वक अध्ययन कीजिए तथा उस संख्या का चयन कीजिए जो इसमें प्रश्न चिन्ह (?) के स्थान पर आ सकती है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744276376794.png\" alt=\"rId27\" width=\"195\" height=\"107\"></p>",
                    options_en: ["<p>6</p>", "<p>9</p>", 
                                "<p>7</p>", "<p>8</p>"],
                    options_hi: ["<p>6</p>", "<p>9</p>",
                                "<p>7</p>", "<p>8</p>"],
                    solution_en: "<p>19.(b) In this question, logic has been applied row wise i.e.<br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>1</mn><mi>s</mi><mi>t</mi><mo>&#160;</mo><mi>t</mi><mi>e</mi><mi>r</mi><mi>m</mi><mo>&#215;</mo><mn>3</mn><mi>r</mi><mi>d</mi><mo>&#160;</mo><mi>t</mi><mi>e</mi><mi>r</mi><mi>m</mi><mo>)</mo></mrow><mrow><mn>2</mn><mi>n</mi><mi>d</mi><mo>&#160;</mo><mi>t</mi><mi>e</mi><mi>r</mi><mi>m</mi></mrow></mfrac></math> = 4th term.<br>So, as per 4th row = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>18</mn><mo>&#215;</mo><mn>8</mn><mo>)</mo></mrow><mi>x</mi></mfrac></math>&nbsp;= 16<br>x = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>18</mn><mo>&#215;</mo><mn>8</mn><mo>)</mo></mrow><mn>16</mn></mfrac></math> = 9</p>",
                    solution_hi: "<p>19.(b) इस प्रश्न में तर्क को पंक्ति के अनुसार लागू किया गया है अर्थात<br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mi>&#2346;&#2361;&#2354;&#2366;</mi><mo>&#160;</mo><mi>&#2346;&#2342;</mi><mo>&#215;</mo><mi>&#2340;&#2368;&#2360;&#2352;&#2366;</mi><mo>&#160;</mo><mi>&#2346;&#2342;</mi><mo>)</mo></mrow><mrow><mi>&#2342;&#2370;&#2360;&#2352;&#2366;</mi><mo>&#160;</mo><mi>&#2346;&#2342;</mi><mo>&#160;</mo></mrow></mfrac></math> = चौथा पद ।<br>इसी प्रकार, चौथी पंक्ति के अनुसार<br>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>18</mn><mo>&#215;</mo><mn>8</mn><mo>)</mo></mrow><mi>x</mi></mfrac></math>&nbsp;= 16<br>x = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>18</mn><mo>&#215;</mo><mn>8</mn><mo>)</mo></mrow><mn>16</mn></mfrac></math> = 9</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "6",
                    question_en: "<p>20. The sequence of folding a piece of paper and the manner in which the folded paper has been cut is shown in the following figures. How would this paper look when unfolded ?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744276376890.png\" alt=\"rId28\" width=\"246\" height=\"75\"></p>",
                    question_hi: "<p>20. कागज़ के एक टुकड़े को मोड़ने का क्रम तथा उसे काटने का तरीका निम्नलिखित आकृतियों में दर्शाया गया है। खुलने के बाद यह कागज़ कैसा दिखेगा ? <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744276376890.png\" alt=\"rId28\" width=\"246\" height=\"75\"></p>",
                    options_en: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744276376992.png\" alt=\"rId29\" width=\"76\" height=\"74\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744276377133.png\" alt=\"rId30\" width=\"77\" height=\"75\"></p>", 
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744276377243.png\" alt=\"rId31\" width=\"78\" height=\"76\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744276377341.png\" alt=\"rId32\" width=\"76\" height=\"74\"></p>"],
                    options_hi: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744276376992.png\" alt=\"rId29\" width=\"77\" height=\"75\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744276377133.png\" alt=\"rId30\" width=\"77\" height=\"75\"></p>",
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744276377243.png\" alt=\"rId31\" width=\"77\" height=\"75\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744276377341.png\" alt=\"rId32\" width=\"77\" height=\"75\"></p>"],
                    solution_en: "<p>20.(c) The image given below shows how the paper will look like when it is unfolded.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744276377243.png\" alt=\"rId31\" width=\"76\" height=\"74\"></p>",
                    solution_hi: "<p>20.(c) नीचे दिए गए चित्र में दिखाया गया है कि कागज को खोलने पर वह कैसा दिखेगा।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744276377243.png\" alt=\"rId31\" width=\"76\" height=\"74\"></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "6",
                    question_en: "<p>21. Read the given statements and conclusions carefully.Assuming that the information given in the statements is true , even if it appears to be at variation with commonly known facts, decide which of the given conclusions logically follow from the statements. <br><strong>Statement:</strong><br>1.All parakeets are cuckoos. <br>2.All cuckoos are rabbits.<br>3.All rabbits are snakes. <br><strong>Conclusions:</strong><br>I. All parakeets are snakes.<br>II. All snakes are cuckoos. <br>III. All rabbits are parakeets. <br>IV. All cuckoos are snakes.</p>",
                    question_hi: "<p>21. दिए गए कथनों तथा निष्कर्षों का ध्यानपूर्वक अध्ययन कीजिए ।<br>यह मानते हुए कि कथनों में दी गयी जानकारी सही है, भले ही यह सामान्य रूप से ज्ञात तथ्यों से भिन्न प्रतीत होती है, तय करें कि इन कथनों से निम्नलिखित में से कौन सा निष्कर्ष निकाला जा सकता है ? <br><strong>कथन :</strong><br>सभी तोते कोयल हैं&nbsp;<br>सभी कोयल खरगोश हैं |<br>सभी खरगोश साँप हैं |<br><strong>निष्कर्ष :</strong><br>I. सभी तोते साँप हैं ।<br>II. सभी साँप कोयल हैं।<br>III. सभी खरगोश तोते हैं।<br>IV. सभी कोयल साँप हैं।</p>",
                    options_en: ["<p>Only conclusions II and III follow.</p>", "<p>Only conclusions I and II follow.</p>", 
                                "<p>All the conclusions follow.</p>", "<p>Only conclusions I and IV follow.</p>"],
                    options_hi: ["<p>केवल निष्कर्ष II और III सही है।</p>", "<p>केवल निष्कर्ष I और II सही है।</p>",
                                "<p>सभी निष्कर्ष सही हैं।</p>", "<p>केवल निष्कर्ष I तथा IV सही हैं।</p>"],
                    solution_en: "<p>21.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744276377455.png\" alt=\"rId33\" width=\"191\" height=\"174\"><br>Clearly, only conclusion I and IV follows</p>",
                    solution_hi: "<p>21.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744276377574.png\" alt=\"rId34\" width=\"209\" height=\"190\"><br>स्पष्ट रूप से, केवल निष्कर्ष I और IV अनुसरण करते हैं।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "6",
                    question_en: "<p>22. Select the number that can replace the question mark (?) in the following series.&nbsp;<br>40 , 37 , 43 , 34 , 46 , ?</p>",
                    question_hi: "<p>22. उस संख्या का चयन कीजिए जो निम्नलिखित श्रृंखला में प्रश्न चिन्ह (?) के स्थान पर आ सकती है।<br>40 , 37 , 43 , 34 , 46 , ?</p>",
                    options_en: ["<p>51</p>", "<p>61</p>", 
                                "<p>31</p>", "<p>41</p>"],
                    options_hi: ["<p>51</p>", "<p>61</p>",
                                "<p>31</p>", "<p>41</p>"],
                    solution_en: "<p>22.(c) 40 , 37, 43 , 34 , 46 , <strong>31</strong><br>There are two series which are going on simultaneously.<br>1st series(from left to right)<br>40 + 3 = 43<br>43 + 3 = 46<br>2nd series ( from left to right)<br>37 - 3 = 34<br>34 - 3 = 31</p>",
                    solution_hi: "<p>22.(c) 40, 37, 43, 34, 46, <strong>31</strong><br>दो सीरीज हैं जो एक साथ चल रही हैं।<br>पहली श्रृंखला (बाएं से दाएं)<br>40 + 3 = 43<br>43 + 3 = 46<br>दूसरी श्रृंखला (बाएं से दाएं)<br>37 - 3 = 34<br>34 - 3 = 31</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "23",
                    section: "6",
                    question_en: "<p>23. There is a family of five members: K, L, M, N and O. Among them , there is one married couple.O is unmarried and is the brother of K, N is the sister of O, M is the only married female and the mother of N, L and O are the only males in the group.Who is the father of K ?</p>",
                    question_hi: "<p>23. पाँच सदस्यों- K, L, M, N तथा O का एक परिवार है। उनमें से एक विवाहित जोड़ा है। O अविवाहित है तथा K का भाई है । N, O की बहन है । M एकमात्र विवाहित महिला है तथा N की माँ है । समूह में केवल L और O ही पुरुष हैं। K का पिता कौन है ?</p>",
                    options_en: ["<p>O</p>", "<p>M</p>", 
                                "<p>K</p>", "<p>L</p>"],
                    options_hi: ["<p>O</p>", "<p>M</p>",
                                "<p>K</p>", "<p>L</p>"],
                    solution_en: "<p>23.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744276377673.png\" alt=\"rId35\" width=\"182\" height=\"104\"><br>By observing the above diagram, we can say that L is the father of K.</p>",
                    solution_hi: "<p>23.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744276377673.png\" alt=\"rId35\" width=\"182\" height=\"104\"><br>ऊपर दिए गए आरेख को देखकर हम कह सकते हैं कि L, K का पिता है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "24",
                    section: "6",
                    question_en: "<p>24. Select the number - pair that is different.</p>",
                    question_hi: "<p>24. उस संख्या युग्म का चयन कीजिए जो अलग है।</p>",
                    options_en: ["<p>225 - 241</p>", "<p>169 - 197</p>", 
                                "<p>121 - 145</p>", "<p>289 - 325</p>"],
                    options_hi: ["<p>225 - 241</p>", "<p>169 - 197</p>",
                                "<p>121 - 145</p>", "<p>289 - 325</p>"],
                    solution_en: "<p>24.(a) The pattern followed is <br>169 - 197<br>(13)<sup>2</sup> = 169 , (14)<sup>2</sup> + 1 = 197<br>121-145<br>(11)<sup>2</sup> = 121, (12)<sup>2 </sup>+ 1 = 145<br>289 - 325<br>(17)<sup>2</sup> = 289, (18)<sup>2</sup> + 1 = 325<br>But, option a (225-241) does not follow this fix pattern.</p>",
                    solution_hi: "<p>24.(a) निम्नलिखित प्रतिरूप है<br>169 - 197<br>(13)<sup>2</sup> = 169 , (14)<sup>2</sup> + 1 = 197<br>121-145<br>(11)<sup>2</sup> = 121, (12)<sup>2 </sup>+ 1 = 145<br>289 - 325<br>(17)<sup>2</sup> = 289, (18)<sup>2</sup> + 1 = 325<br>लेकिन, विकल्प a (225-241) फिक्स पैटर्न का पालन नहीं करता है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "25",
                    section: "18",
                    question_en: "<p>25. In a certain code language, U is written as C, K is written as H , L is written as U, N is written as E, S is written as L, E is written as K, and C is written as N .How will &lsquo;KNUCKLES&rsquo; be written as in that language ?</p>",
                    question_hi: "<p>25. किसी निश्चित कूट भाषा में, U को C लिखा जाता है, K को H लिखा जाता है, L को U लिखा जाता है, N को E लिखा जाता है, S को L लिखा जाता है, E को K लिखा जाता है तथा C को N लिखा जाता है। इस भाषा में &lsquo;KNUCKLES&rsquo; को कैसे लिखा जाएगा ?</p>",
                    options_en: ["<p>KECNKUHL</p>", "<p>CHUECKN</p>", 
                                "<p>HECNHULK</p>", "<p>HECNHUKL</p>"],
                    options_hi: ["<p>KECNKUHL</p>", "<p>CHUECKN</p>",
                                "<p>HECNHULK</p>", "<p>HECNHUKL</p>"],
                    solution_en: "<p>25.(d) As per the question,<br>U &rArr; C , K &rArr; H, L &rArr; U, N &rArr; E, S &rArr; L, E &rArr; K ,C &rArr; N<br>Thus, KNUCKLES will be written as HECNHUKL.</p>",
                    solution_hi: "<p>25.(d) प्रश्न के अनुसार,<br>U &rArr; C , K &rArr; H, L &rArr; U, N &rArr; E, S &rArr; L, E &rArr; K , C &rArr; N<br>इस प्रकार, KNUCKLES को HECNHUKL लिखा जाएगा</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "26",
                    section: "18",
                    question_en: "<p>26. Which National Park among the following is the largest protected area in the Eastern Himalayan sub-region ?</p>",
                    question_hi: "<p>26. निम्नलिखित में से कौन सा राष्ट्रीय उद्यान पूर्वी हिमालयी उप-क्षेत्र में सबसे बड़ा संरक्षित क्षेत्र है ?</p>",
                    options_en: ["<p>Keibul Lamjao National Park</p>", "<p>Namdapha National Park</p>", 
                                "<p>Jim Corbett National Park</p>", "<p>Bandipur National Park</p>"],
                    options_hi: ["<p>कीबुल लामजाओ राष्ट्रीय उद्यान</p>", "<p>नामदफा राष्ट्रीय उद्यान</p>",
                                "<p>जिम कॉर्बेट राष्ट्रीय उद्यान</p>", "<p>बांदीपुर राष्ट्रीय उद्यान</p>"],
                    solution_en: "<p>26.(b) Namdapha National Park is the largest protected area in the Eastern Himalayan sub-region in Arunachal Pradesh. It has more than 1,000 floral and about 1,400 faunal species, it is a biodiversity hotspot in the Eastern Himalayas. Keibul lamjao national park: bishnupur, Manipur Jim corbett national park : nainital, uttrakhand Bandipur national park: Karnataka</p>",
                    solution_hi: "<p>26.(b) नमदाफा राष्ट्रीय उद्यान अरुणाचल प्रदेश में पूर्वी हिमालयी उप-क्षेत्र का सबसे बड़ा संरक्षित क्षेत्र है। इसमें 1,000 से अधिक पुष्प और लगभग 1,400 जीव प्रजातियां हैं, यह पूर्वी हिमालय में एक जैव विविधता हॉटस्पॉट है।<br>कीबुल लामजाओ राष्ट्रीय उद्यान: बिष्णुपुर, मणिपुर<br>जिम कॉर्बेट राष्ट्रीय उद्यान: नैनीताल, उत्तराखंड<br>बांदीपुर राष्ट्रीय उद्यान: कर्नाटक।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "27",
                    section: "18",
                    question_en: "<p>27. In terms of area, which state has the largest forest cover in India ?</p>",
                    question_hi: "<p>27. क्षेत्रफल के लिहाज से, भारत में किस राज्य में सबसे बड़ा वन क्षेत्र है ?</p>",
                    options_en: ["<p>Madhya Pradesh</p>", "<p>Odisha</p>", 
                                "<p>Maharashtra</p>", "<p>Kerala</p>"],
                    options_hi: ["<p>मध्य प्रदेश</p>", "<p>ओडिशा</p>",
                                "<p>महाराष्ट्र</p>", "<p>केरल</p>"],
                    solution_en: "<p>27.(a) Area-wise Madhya Pradesh has the largest forest cover in the country followed by Arunachal Pradesh, Chhattisgarh, Odisha and Maharashtra. Arunachal Pradesh is the state with the maximum percentage (79.63%) of forest cover. According to a report by the Union Environment Ministry in December 2019, Total Forest and Tree Cover rises to 24.56 percent of the total geographical area of the Country.</p>",
                    solution_hi: "<p>27.(a) क्षेत्रफल के हिसाब से मध्य प्रदेश में देश का सबसे बड़ा वन क्षेत्र है, इसके बाद अरुणाचल प्रदेश, छत्तीसगढ़, ओडिशा और महाराष्ट्र हैं। अरुणाचल प्रदेश वन क्षेत्र का अधिकतम प्रतिशत (79.63%) वाला राज्य है। दिसंबर 2019 में केंद्रीय पर्यावरण मंत्रालय की एक रिपोर्ट के अनुसार, देश के कुल भौगोलिक क्षेत्र का कुल वन और वृक्ष आवरण बढ़कर 24.56 प्रतिशत हो गया।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "28",
                    section: "18",
                    question_en: "<p>28. In which year was the Currency Building in the BBD Bagh or Dalhousie area of Kolkata constructed ?</p>",
                    question_hi: "<p>28. कोलकाता के बीबीडी बाग या डलहौजी क्षेत्र में मुद्रा निर्माण किस वर्ष में हुआ था ?</p>",
                    options_en: ["<p>1900</p>", "<p>1833</p>", 
                                "<p>1850</p>", "<p>1910</p>"],
                    options_hi: ["<p>1900</p>", "<p>1833</p>",
                                "<p>1850</p>", "<p>1910</p>"],
                    solution_en: "<p>28.(b) The Currency Building in Kolkata was built in the year 1833 which lies in the city\'s Dalhousie region. The beautiful colonial building was designed in Italian style with Venetian windows. This building was the office of the Reserve Bank of India till the year 1937.</p>",
                    solution_hi: "<p>28.(b) कोलकाता में मुद्रा भवन वर्ष 1833 में बनाया गया था जो शहर के डलहौजी क्षेत्र में स्थित है। सुंदर औपनिवेशिक इमारत को इतालवी शैली में विनीशियन खिड़कियों के साथ डिजाइन किया गया था। यह भवन वर्ष 1937 तक भारतीय रिजर्व बैंक का कार्यालय था।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "29",
                    section: "18",
                    question_en: "<p>29. Chiropody is a branch of science related to which part of the body ?</p>",
                    question_hi: "<p>29. चिरोपोडी विज्ञान की एक शाखा है, जो शरीर के किस अंग से संबंधित है ?</p>",
                    options_en: ["<p>Kidney</p>", "<p>Lungs</p>", 
                                "<p>Liver</p>", "<p>Feet</p>"],
                    options_hi: ["<p>किडनी</p>", "<p>फेफड़े</p>",
                                "<p>लीवर</p>", "<p>पैर का पंजा</p>"],
                    solution_en: "<p>29.(d) Chiropody is the study and treatment of problems and diseases of people\'s feet. Study of kidney is known as Nephrology. Study of lungs is known as Pulmonology. Study of the liver is known as Hepatology.</p>",
                    solution_hi: "<p>29.(d) चिरोपोडी लोगों के पैरों की समस्याओं और रोगों का अध्ययन और उपचार है।<br>गुर्दे के अध्ययन को नेफ्रोलॉजी के रूप में जाना जाता है।<br>फेफड़ों के अध्ययन को पल्मोनोलॉजी के रूप में जाना जाता है।<br>जिगर के अध्ययन को हेपेटोलॉजी के रूप में जाना जाता है</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "30",
                    section: "18",
                    question_en: "<p>30. Which dynasty built the pancha rathas of Mahabalipuram ?</p>",
                    question_hi: "<p>30. किस वंश ने महाबलीपुरम के पंच रथों का निर्माण किया ?</p>",
                    options_en: ["<p>Chera</p>", "<p>Pallava</p>", 
                                "<p>Satavahana</p>", "<p>Chola</p>"],
                    options_hi: ["<p>चेरा</p>", "<p>पल्लव</p>",
                                "<p>सातवाहन</p>", "<p>चोल</p>"],
                    solution_en: "<p>30.(b) Pancha Rathas are the monolithic chariot structures at Mahabalipuram, Tamil Nadu. They were constructed during the reign of Pallava Kings Mahendravarman I and Narasimhavarman I. An interesting aspect is that each ratha is named after the Pandavas of the Mahabharata fame.</p>",
                    solution_hi: "<p>30.(b) पंच रथ महाबलीपुरम, तमिलनाडु में अखंड रथ संरचनाएं हैं। इनका निर्माण पल्लव राजाओं महेंद्रवर्मन प्रथम और नरसिंहवर्मन प्रथम के शासनकाल के दौरान किया गया था। एक दिलचस्प पहलू यह है कि प्रत्येक रथ का नाम महाभारत प्रसिद्धि के पांडवों के नाम पर रखा गया है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "31",
                    section: "18",
                    question_en: "<p>31. In which of the following locations was the Quit India Movement launched by Mahatma Gandhi in 1942 ?</p>",
                    question_hi: "<p>31. 1942 में महात्मा गांधी द्वारा भारत छोड़ो आंदोलन निम्नलिखित में से किस स्थान पर शुरू किया गया था ?</p>",
                    options_en: ["<p>August Kranti Maidan</p>", "<p>Shivaji Park</p>", 
                                "<p>Jallianwala Bagh</p>", "<p>Pragati Maidan</p>"],
                    options_hi: ["<p>अगस्त क्रांति मैदान</p>", "<p>शिवाजी पार्क</p>",
                                "<p>जलियाँवाला बाग</p>", "<p>प्रगति मैदान</p>"],
                    solution_en: "<p>31.(a) Quit India Movement was launched by August Kranti Maidan in the greater Mumbai district of Maharashtra. Mahatma Gandhi on 8th August 1942 started the Quit India Movement with a Speech that British must leave India immediately or else mass agitations would take place.</p>",
                    solution_hi: "<p>31.(a) भारत छोड़ो आंदोलन महाराष्ट्र के अधिक से अधिक मुंबई जिले में अगस्त क्रांति मैदान द्वारा शुरू किया गया था। 8 अगस्त 1942 ई, को महात्मा गांधी ने एक भाषण के साथ भारत छोड़ो आंदोलन शुरू किया कि अंग्रेजों को तुरंत भारत छोड़ देना चाहिए अन्यथा बड़े पैमाने पर आंदोलन होंगे।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "32",
                    section: "18",
                    question_en: "<p>32. What is the colour of the light emitted by the Sun ?</p>",
                    question_hi: "<p>32. सूर्य द्वारा उत्सर्जित प्रकाश का रंग क्या है ?</p>",
                    options_en: ["<p>Yellow</p>", "<p>Orange</p>", 
                                "<p>Red</p>", "<p>White</p>"],
                    options_hi: ["<p>पीला</p>", "<p>नारंगी</p>",
                                "<p>लाल</p>", "<p>सफेद</p>"],
                    solution_en: "<p>32.(d) The light emitted by the Sun is white, which consists of all visible frequencies of light red, orange, yellow, green, blue, indigo and violet all of which form the colors of the rainbow and to remember the colour pattern remember a word &lsquo;VIBGYOR&rsquo; which is formed by the starting letters, in reverse order.</p>",
                    solution_hi: "<p>32.(d) सूर्य द्वारा उत्सर्जित प्रकाश सफेद होता है, जिसमें हल्के लाल, नारंगी, पीले, हरे, नीले, इंडिगो और वायलेट की सभी दृश्य आवृत्तियां शामिल होती हैं, जो सभी इंद्रधनुष के रंग बनाते हैं और रंग पैटर्न को याद रखने के लिए एक शब्द याद रखें \'VIBGYOR\' जो शुरुआती अक्षरों से उल्टे क्रम में बनता है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "33",
                    section: "18",
                    question_en: "<p>33. Name the law in Physics which states that equal volume of all gases under the same conditions of temperature and pressure contain equal numbers of molecules.</p>",
                    question_hi: "<p>33. भौतिकी में वह कौन सा नियम जो यह बताता है कि तापमान और दाब की समान परिस्थितियों में सभी गैसों की समान आयतन में समान अणु की संख्या होती हैं।</p>",
                    options_en: ["<p>Avogadro&rsquo;s Law</p>", "<p>Charles\'s Law</p>", 
                                "<p>Ohm&rsquo;s Law</p>", "<p>Boyle\'s Law</p>"],
                    options_hi: ["<p>अवोगाद्रो का नियम</p>", "<p>चार्ल्स का नियम</p>",
                                "<p>ओम का नियम</p>", "<p>बॉयल्स लॉ</p>"],
                    solution_en: "<p>33.(a) Avogadro\'s law states that \"equal volumes of all gases, at the same temperature and pressure, have the same number of molecules<br><math display=\"inline\"><mfrac><mrow><msub><mrow><mi>V</mi></mrow><mrow><mn>1</mn></mrow></msub></mrow><mrow><msub><mrow><mi>n</mi></mrow><mrow><mn>1</mn></mrow></msub></mrow></mfrac><mo>=</mo><mfrac><mrow><msub><mrow><mi>V</mi></mrow><mrow><mn>2</mn></mrow></msub></mrow><mrow><msub><mrow><mi>n</mi></mrow><mrow><mn>2</mn></mrow></msub></mrow></mfrac></math>; V<sub>1</sub> = first volume, V<sub>2 </sub>= second volume, n<sub>1 </sub>= first amount of gas(in moles), n<sub>2 </sub>= second amount of gas(in moles)</p>",
                    solution_hi: "<p>33.(a) अवोगाद्रो का नियम कहता है कि \"सभी गैसों के समान आयतन, समान तापमान और दबाव पर, अणुओं की संख्या समान होती है &rdquo;।<br><math display=\"inline\"><mfrac><mrow><msub><mrow><mi>V</mi></mrow><mrow><mn>1</mn></mrow></msub></mrow><mrow><msub><mrow><mi>n</mi></mrow><mrow><mn>1</mn></mrow></msub></mrow></mfrac><mo>=</mo><mfrac><mrow><msub><mrow><mi>V</mi></mrow><mrow><mn>2</mn></mrow></msub></mrow><mrow><msub><mrow><mi>n</mi></mrow><mrow><mn>2</mn></mrow></msub></mrow></mfrac></math>;&nbsp;<br>यहाँ , V<sub>1 </sub>= पहला आयतन , V<sub>2 </sub>= दूसरा आयतन , n<sub>1 </sub>= गैस की पहली मात्रा (मोल में) , n<sub>2 </sub>= गैस की दूसरी मात्रा (मोल में)</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "34",
                    section: "18",
                    question_en: "<p>34. What was the theme of the 107th Indian Science Congress held in Bengaluru ?</p>",
                    question_hi: "<p>34. बेंगलुरु में आयोजित 107 वीं भारतीय विज्ञान कांग्रेस का विषय क्या था ?</p>",
                    options_en: ["<p>Science and Technology for National Development</p>", "<p>Reaching the Unreached through Science and Technology</p>", 
                                "<p>Future India : Science and Technology</p>", "<p>Science and Technology: Rural Development</p>"],
                    options_hi: ["<p>राष्ट्रीय विकास के लिए विज्ञान और प्रौद्योगिकी</p>", "<p>विज्ञान और प्रौद्योगिकी के माध्यम से अगम तक पहुंचना</p>",
                                "<p>भविष्य का भारत: विज्ञान और प्रौद्योगिकी</p>", "<p>विज्ञान और प्रौद्योगिकी: ग्रामीण विकास</p>"],
                    solution_en: "<p>34.(d) Theme of the 107th Indian Science Congress held in Bengaluru is &ldquo;Reaching the Unreached through Science and Technology&rdquo;. It was held from 3 to 7 January at University of Agricultural Sciences, GKVK Campus, Bangalore, Karnataka focusing on \'Science &amp; Technology: Rural Development\'.</p>",
                    solution_hi: "<p>34.(d) बेंगलुरु में आयोजित 107वीं भारतीय विज्ञान कांग्रेस का विषय \"विज्ञान और प्रौद्योगिकी के माध्यम से पहुंच से बाहर तक पहुंचना\" है। यह 3 से 7 जनवरी तक कृषि विज्ञान विश्वविद्यालय, जीकेवीके कैंपस, बैंगलोर, कर्नाटक में \'विज्ञान\' पर केंद्रित था।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "35",
                    section: "18",
                    question_en: "<p>35. In January 2020, B. Sai Deepak set a Guinness World Record for most side lunges in 60 seconds. How many lunges did he do ?</p>",
                    question_hi: "<p>35. जनवरी 2020 में, बी साईं दीपक ने 60 सेकंड में सबसे अधिक साइड लंग्स (side lunges) मरने का गिनीज वर्ल्ड रिकॉर्ड बनाया। उसने कितने साइड लंग्स मारे ?</p>",
                    options_en: ["<p>30</p>", "<p>59</p>", 
                                "<p>50</p>", "<p>40</p>"],
                    options_hi: ["<p>30</p>", "<p>59</p>",
                                "<p>50</p>", "<p>40</p>"],
                    solution_en: "<p>35.(b) In January 2020, B Sai Deepak has set a Guinness World Records for most side lunges in 60 seconds. Deepak made the record by doing 59 side lunges in 60 seconds, which is his fourth overall Guinness World Records.</p>",
                    solution_hi: "<p>35.(b) जनवरी 2020 में, बी साई दीपक ने 60 सेकंड में सबसे अधिक साइड लंग्स के लिए गिनीज वर्ल्ड रिकॉर्ड बनाया है। दीपक ने 60 सेकंड में 59 साइड&nbsp;लंग्स करके रिकॉर्ड बनाया, जो उनका चौथा समग्र गिनीज वर्ल्ड रिकॉर्ड है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "36",
                    section: "18",
                    question_en: "<p>36. The 23rd National Youth Festival (NYF) 2020 was celebrated in Lucknow to commemorate the birth anniversary of ______.</p>",
                    question_hi: "<p>36. _____की जयंती मनाने के लिए लखनऊ में 23 वां राष्ट्रीय युवा महोत्सव (NYF) 2020 मनाया गया।</p>",
                    options_en: ["<p>Jawaharlal Nehru</p>", "<p>Sardar Vallabhbhai Patel</p>", 
                                "<p>Swami Vivekananda</p>", "<p>Mahatma Gandhi</p>"],
                    options_hi: ["<p>जवाहरलाल नेहरू</p>", "<p>सरदार वल्लभभाई पटेल</p>",
                                "<p>स्वामी विवेकानंद</p>", "<p>महात्मा गांधी</p>"],
                    solution_en: "<p>36.(c) National Youth Day or Yuva Diwas is observed on 12 January across India. The day is celebrated every year to commemorate the birth anniversary of Swami Vivekananda. It aims to create awareness and to provide knowledge to the youth about the rights in India.</p>",
                    solution_hi: "<p>36.(c) राष्ट्रीय युवा दिवस या युवा दिवस पूरे भारत में 12 जनवरी को मनाया जाता है। यह दिवस हर साल स्वामी विवेकानंद की जयंती के उपलक्ष्य में मनाया जाता है। इसका उद्देश्य जागरूकता पैदा करना और भारत में अधिकारों के बारे में युवाओं को ज्ञान प्रदान करना है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "37",
                    section: "18",
                    question_en: "<p>37. As per the government rules, how much percentage of advance tax needs to be paid by 15th June by an individual who is liable to pay advance tax ?</p>",
                    question_hi: "<p>37. सरकारी नियमों के अनुसार, अग्रिम कर का भुगतान करने के लिए उत्तरदायी व्यक्ति द्वारा 15 जून तक अग्रिम कर का कितना प्रतिशत भुगतान करने की आवश्यकता है ?</p>",
                    options_en: ["<p>25%</p>", "<p>15%</p>", 
                                "<p>30%</p>", "<p>10%</p>"],
                    options_hi: ["<p>25%</p>", "<p>15%</p>",
                                "<p>30%</p>", "<p>10%</p>"],
                    solution_en: "<p>37.(b) As per the government rules, 15% of advance tax needs to be paid by 15th June by an individual who is liable to pay advance tax. Advance tax is a form of income tax that is payable in case your tax liability exceeds INR 10,000 for a particular financial year. This tax must be paid by the taxpayers in the same year the income is received. For this purpose, advance tax is also known as \'pay-as-you-earn\' scheme.</p>",
                    solution_hi: "<p>37.(b) सरकार के नियमों के अनुसार, अग्रिम कर का 15% भुगतान 15 जून तक उस व्यक्ति द्वारा किया जाना है जो अग्रिम कर का भुगतान करने के लिए उत्तरदायी है। अग्रिम कर एक प्रकार का आयकर है जो किसी विशेष वित्तीय वर्ष के लिए आपकी कर देयता INR 10,000 से अधिक होने पर देय होता है। इस कर का भुगतान करदाताओं द्वारा उसी वर्ष किया जाना चाहिए जिस वर्ष आय प्राप्त होती है। इस उद्देश्य के लिए, अग्रिम कर को \'पे-एज़-यू-अर्न\' योजना के रूप में भी जाना जाता है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "38",
                    section: "18",
                    question_en: "<p>38. VISHWAS, which is a major e-governance initiative launched by the government in January 2020, is the acronym for which of the following ?</p>",
                    question_hi: "<p>38. जनवरी 2020 में सरकार द्वारा शुरू की गई VISHWAS, जो एक प्रमुख इ -शासन पहल है ,निम्नलिखित में से किसका संक्षिप्त है ?</p>",
                    options_en: ["<p>Video Integration and Statewide Advanced Security</p>", "<p>Video Integration and State Wide Advanced System</p>", 
                                "<p>Video Interface and Statewide Advanced Security</p>", "<p>Video Integration and System Wide Advanced Security</p>"],
                    options_hi: ["<p>वीडियो इंटीग्रेशन और स्टेट वाइड एडवांस्ड सिक्योरिटी</p>", "<p>वीडियो इंटीग्रेशन और स्टेट वाइड एडवांस्ड सिस्टम</p>",
                                "<p>वीडियो इंटरफ़ेस और स्टेट वाइड एडवांस् सिक्योरिटी</p>", "<p>वीडियो एकीकरण और सिस्टम वाइड उन्नत</p>"],
                    solution_en: "<p>38.(a) In January, a major e-governance initiative Project VISHWAS (Video Integration and State Wide Advanced Security) was launched with the aim to improve Law and Order Management, Proactive traffic management and control, Crime detection through video analytics and Post incident Investigation and video forensics.</p>",
                    solution_hi: "<p>38.(a) जनवरी में, एक प्रमुख ई-गवर्नेंस पहल प्रोजेक्ट विश्वास (वीडियो इंटीग्रेशन एंड स्टेट वाइड एडवांस्ड सिक्योरिटी) को कानून और व्यवस्था प्रबंधन, सक्रिय यातायात प्रबंधन और नियंत्रण, वीडियो एनालिटिक्स के माध्यम से अपराध का पता लगाने और घटना के बाद की जांच और वीडियो फोरेंसिक में सुधार के उद्देश्य से शुरू किया गया था। सुरक्षा</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "39",
                    section: "18",
                    question_en: "<p>39. The researchers of which academic institution employed the nanoscale phenomenon called \'Electrokinetic streaming potential\' to harvest energy from flowing water on a small scale like water flowing through household water taps ?</p>",
                    question_hi: "<p>39. निम्नलिखित में से किस शैक्षणिक संस्थान के शोधकर्ताओं ने \'विधुत गतिक धारा वैभव (Electrokinetic streaming potential), नामक नैनोस्केल परिघटना (nanoscale phenomenon)का प्रयोग छोटे स्तर पर बहते हुआ पानी , जैसे घरेलू पानी की नलो से बहने वाले पानी से ऊर्जा का निर्माण करने में किया ?</p>",
                    options_en: ["<p>IIT Delhi</p>", "<p>IIT Bombay</p>", 
                                "<p>IIT Madras</p>", "<p>IIT Guwahati</p>"],
                    options_hi: ["<p>IIT दिल्ली</p>", "<p>IIT बॉम्बे</p>",
                                "<p>IIT मद्रास</p>", "<p>IIT गुवाहाटी</p>"],
                    solution_en: "<p>39.(d) On 30 December 2019, the researchers of IIT Guwahati employed the nanoscale phenomenon called &ldquo;Electrokinetic streaming potential&rdquo; to harvest energy from flowing water on the small length scale like water flowing through household water taps.</p>",
                    solution_hi: "<p>39.(d) 30 दिसंबर 2019 को, IIT गुवाहाटी के शोधकर्ताओं ने घरेलू पानी के नल से बहने वाले पानी की तरह छोटे लंबाई के पैमाने पर बहने वाले पानी से ऊर्जा की कटाई के लिए \"इलेक्ट्रोकेनेटिक स्ट्रीमिंग क्षमता\" नामक नैनोस्केल घटना को नियोजित किया।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "40",
                    section: "18",
                    question_en: "<p>40. Which is the first Indian company to hit the ₹10 lakh crore mark in market capitalisation ?</p>",
                    question_hi: "<p>40. बाजार पूंजीकरण में 10 लाख करोड़ का आंकड़ा छूने वाली पहली भारतीय कंपनी कौन सी है ?</p>",
                    options_en: ["<p>Reliance Industries</p>", "<p>ICICI Bank</p>", 
                                "<p>Tata Consultancy Services</p>", "<p>HDFC Bank</p>"],
                    options_hi: ["<p>रिलायंस इंडस्ट्रीज</p>", "<p>आईसीआईसीआई बैंक</p>",
                                "<p>टाटा कंसल्टेंसी सर्विसेज</p>", "<p>एचडीएफसी बैंक</p>"],
                    solution_en: "<p>40.(a) Mukesh Ambani-led Reliance Industries (RIL) in November 2019 became the first Indian company to hit the ₹10 lakh crore mark in market capitalization. It happened because the stocks of this company suddenly bulls to a new height.</p>",
                    solution_hi: "<p>40.(a) मुकेश अंबानी की अगुवाई वाली रिलायंस इंडस्ट्रीज (आरआईएल) नवंबर 2019 में बाजार पूंजीकरण में ₹10 लाख करोड़ का आंकड़ा पार करने वाली पहली भारतीय कंपनी बन गई। ऐसा इसलिए हुआ क्योंकि इस कंपनी के शेयर अचानक नई ऊंचाई पर पहुंच गए।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "41",
                    section: "18",
                    question_en: "<p>41. The Indian Railways has integrated its helpline numbers into a single number. What is the number ?</p>",
                    question_hi: "<p>41. भारतीय रेलवे ने अपने हेल्पलाइन नंबरों को एक ही नंबर में एकीकृत किया है। वह नंबर क्या है ?</p>",
                    options_en: ["<p>150</p>", "<p>145</p>", 
                                "<p>160</p>", "<p>139</p>"],
                    options_hi: ["<p>150</p>", "<p>145</p>",
                                "<p>160</p>", "<p>139</p>"],
                    solution_en: "<p>41.(d) The Indian Railways has integrated its helpline numbers into a single number 139 which will help the customer make their travel convenient.</p>",
                    solution_hi: "<p>41.(d) भारतीय रेलवे ने अपने हेल्पलाइन नंबरों को एक नंबर 139 में एकीकृत किया है जो ग्राहकों को अपनी यात्रा को सुविधाजनक बनाने में मदद करेगा।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "42",
                    section: "18",
                    question_en: "<p>42. Ishwar Sharma has been honoured with the Global Child Prodigy Award 2020. What is this award associated with ?</p>",
                    question_hi: "<p>42. ईश्वर शर्मा को ग्लोबल चाइल्ड प्रोडिजी अवार्ड 2020 से सम्मानित किया गया है। यह पुरस्कार किससे जुड़ा है ?</p>",
                    options_en: ["<p>Yoga</p>", "<p>Sports</p>", 
                                "<p>Literature</p>", "<p>Science</p>"],
                    options_hi: ["<p>योग</p>", "<p>खेल</p>",
                                "<p>साहित्य</p>", "<p>विज्ञान</p>"],
                    solution_en: "<p>42.(a) Ishwar Sharma from the UK has been honoured with the Global Child Prodigy Award 2020. The award recognized for the achievements in spiritual discipline yoga. He has started an e-petition with the UK government to include yoga in the national school curriculum and nearly 20,000 people signed.</p>",
                    solution_hi: "<p>42.(a) यूके के ईश्वर शर्मा को ग्लोबल चाइल्ड प्रोडिजी अवार्ड 2020 से सम्मानित किया गया है। यह पुरस्कार आध्यात्मिक अनुशासन योग में उपलब्धियों के लिए मान्यता प्राप्त है। उन्होंने राष्ट्रीय स्कूल पाठ्यक्रम में योग को शामिल करने के लिए यूके सरकार के साथ एक ई-याचिका शुरू की है और लगभग 20,000 लोगों ने हस्ताक्षर किए हैं।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "43",
                    section: "18",
                    question_en: "<p>43. Jasprit Bumrah has been selected to receive which of the following awards for his performance in international cricket in the 2018-19 season ?</p>",
                    question_hi: "<p>43. जसप्रीत बुमराह को 2018-19 सत्र में अंतरराष्ट्रीय क्रिकेट में उनके प्रदर्शन के लिए निम्नलिखित में से कौन सा पुरस्कार के लिए चुना गया है ?</p>",
                    options_en: ["<p>Madhavrao Scindia</p>", "<p>M.A. Chidambaram</p>", 
                                "<p>C.K. Nayudu</p>", "<p>Polly Umrigar</p>"],
                    options_hi: ["<p>माधवराव सिंधिया</p>", "<p>एम. ए. चिदंबरम</p>",
                                "<p>सी. के. नायडू</p>", "<p>पोली उमरीगर</p>"],
                    solution_en: "<p>43.(d) In January, 2020 Jasprit Bumrah received the prestigious Polly Umrigar Award for the best international male cricketer in 2018-19 and Poonam Yadav for the best international cricketer (women).</p>",
                    solution_hi: "<p>43.(d) जनवरी, 2020 में जसप्रीत बुमराह को 2018-19 में सर्वश्रेष्ठ अंतरराष्ट्रीय पुरुष क्रिकेटर के लिए प्रतिष्ठित पॉली उमरीगर पुरस्कार और सर्वश्रेष्ठ अंतरराष्ट्रीय क्रिकेटर (महिला) के लिए पूनम यादव को मिला।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "44",
                    section: "18",
                    question_en: "<p>44. Which district has been awarded the Plastic Waste Management Award -2020 for being the best district of India in the plastic waste management category during Swachhta Hi Seva 2019 ?</p>",
                    question_hi: "<p>44. स्वच्छ ही सेवा 2019 के दौरान प्लास्टिक अपशिष्ट प्रबंधन श्रेणी में भारत का सर्वश्रेष्ठ जिला होने के लिए किस जिले को प्लास्टिक अपशिष्ट प्रबंधन पुरस्कार -2020 से सम्मानित किया गया है ?</p>",
                    options_en: ["<p>Majuli</p>", "<p>Hojai</p>", 
                                "<p>Dibrugarh</p>", "<p>Jorhat</p>"],
                    options_hi: ["<p>माजुली</p>", "<p>होजई</p>",
                                "<p>डिब्रूगढ़</p>", "<p>जोरहाट</p>"],
                    solution_en: "<p>44.(c) Assam\'s Dibrugarh district won the Plastic Waste Management Award 2020. It was awarded for being the best district of India in plastic waste management.</p>",
                    solution_hi: "<p>44.(c) असम के डिब्रूगढ़ जिले ने प्लास्टिक कचरा प्रबंधन पुरस्कार 2020 जीता। इसे प्लास्टिक कचरा प्रबंधन में भारत का सर्वश्रेष्ठ जिला होने के लिए सम्मानित किया गया।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "45",
                    section: "18",
                    question_en: "<p>45. The police of which state was honoured with the President\'s Colours award in December 2019 ?</p>",
                    question_hi: "<p>45. दिसंबर 2019 में किस राज्य की पुलिस को राष्ट्रपति रंग सम्मान से सम्मानित किया गया था ?</p>",
                    options_en: ["<p>Gujarat</p>", "<p>Maharashtra</p>", 
                                "<p>Tamil Nadu</p>", "<p>Kerala</p>"],
                    options_hi: ["<p>गुजरात</p>", "<p>महाराष्ट्र</p>",
                                "<p>तमिलनाडु</p>", "<p>केरल</p>"],
                    solution_en: "<p>45.(a) Gujarat police was honoured with the President\'s Colours award on 15 December 2019. Gujarat became the seventh state police force in India to be bestowed with the honour of the President&rsquo;s Colours. It was presented by the Vice President, Shri M. Venkaiah Naidu. <br>The President\'s Colours Award is also called NISHAAN.</p>",
                    solution_hi: "<p>45.(a) गुजरात पुलिस को 15 दिसंबर 2019 को राष्ट्रपति के रंग पुरस्कार से सम्मानित किया गया। गुजरात राष्ट्रपति रंगों के सम्मान से सम्मानित होने वाला भारत का सातवां राज्य पुलिस बल बन गया। इसे उपराष्ट्रपति श्री एम. वेंकैया नायडू ने प्रस्तुत किया। प्रेसिडेंट्स कलर्स अवार्ड को निशान भी कहा जाता है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "46",
                    section: "18",
                    question_en: "<p>46. G. Babita Rayudu took charge as an Executive Director for which of the following organisations in January 2020 ?</p>",
                    question_hi: "<p>46. जी. बबीता रायडू ने जनवरी 2020 में निम्नलिखित में से किस संगठन के लिए एक कार्यकारी निदेशक के रूप में कार्यभार संभाला ?</p>",
                    options_en: ["<p>Insurance Regulatory and Development Authority of India</p>", "<p>Bombay Stock Exchange</p>", 
                                "<p>Small Industries Development Bank of India</p>", "<p>The Securities and Exchange Board of India</p>"],
                    options_hi: ["<p>भारतीय बीमा विनियामक और विकास प्राधिकरण</p>", "<p>बॉम्बे स्टॉक एक्सचेंज</p>",
                                "<p>भारतीय लघु उद्योग विकास बैंक</p>", "<p>भारतीय प्रतिभूति और विनिमय बोर्ड</p>"],
                    solution_en: "<p>46.(d) G. Babita Rayudu took charge as an Executive Director for The Securities and Exchange Board of India popularly known as &lsquo;SEBI&rsquo; in January 2020. She will handle the legal affairs department, enforcement department and special enforcement cell.</p>",
                    solution_hi: "<p>46.(d) जी. बबीता रायुडू ने जनवरी 2020 में भारतीय प्रतिभूति और विनिमय बोर्ड के कार्यकारी निदेशक के रूप में कार्यभार संभाला, जिसे \'सेबी\' के नाम से जाना जाता है। वह कानूनी मामलों के विभाग, प्रवर्तन विभाग और विशेष प्रवर्तन प्रकोष्ठ को संभालेंगी।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "47",
                    section: "18",
                    question_en: "<p>47. Pongal festival is celebrated for four days in Tamil Nadu. What is the fourth day of Pongal called ?</p>",
                    question_hi: "<p>47. पोंगल त्योहार तमिलनाडु में चार दिनों के लिए मनाया जाता है। पोंगल के चौथे दिन को क्या कहा जाता है ?</p>",
                    options_en: ["<p>Thai Pongal</p>", "<p>Kaanum Pongal</p>", 
                                "<p>Bhogi Pongal</p>", "<p>Mattu Pongal</p>"],
                    options_hi: ["<p>थाई पोंगल</p>", "<p>कनुम पोंगल</p>",
                                "<p>भोगी पोंगल</p>", "<p>मट्टू पोंगल</p>"],
                    solution_en: "<p>47.(b) Pongal is a Harvest festival generally celebrated in January. It is a multi-day(four day) Hindu harvest festival of South India, particularly in the Tamil community. The four days are: Bhogi Pongal, Surya Pongal , Mattu Pongal and Kaanum Pongal.</p>",
                    solution_hi: "<p>47.(b) पोंगल आम तौर पर जनवरी में मनाया जाने वाला फसल कटाई का त्योहार है। यह दक्षिण भारत का एक बहु-दिवसीय (चार&nbsp; दिवसीय) हिंदू फसल उत्सव है, विशेष रूप से तमिल समुदाय में। चार दिन हैं: भोगी पोंगल, सूर्य पोंगल, मट्टू पोंगल और कानुम पोंगल।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "48",
                    section: "18",
                    question_en: "<p>48. The famous 11-day long &lsquo;Dhanu Jatra&rsquo;, considered as the largest open-air theatre of the world is celebrated in which state ?</p>",
                    question_hi: "<p>48. 11 दिनों तक चलने वाला प्रसिद्ध \'धनु यात्रा \' जो विश्व का सबसे बड़ा खुला रंगमंच मन जाता है, किस राज्य में मनाया जाता है ?</p>",
                    options_en: ["<p>Odisha</p>", "<p>Assam</p>", 
                                "<p>Manipur</p>", "<p>Meghalaya</p>"],
                    options_hi: ["<p>ओडिशा</p>", "<p>असम</p>",
                                "<p>मणिपुर</p>", "<p>मेघालय</p>"],
                    solution_en: "<p>48.(a) A famous 11-days long Dhanu Jatra or Dhanu Yatra is an annual drama-based open air theatrical performance celebrated in Bargarh, Odisha. Spread across a 8 km radius area around the Bargarh municipality, it is the world\'s largest open air theater, one that finds a mention in the Guinness Book of World Records.</p>",
                    solution_hi: "<p>48.(a) एक प्रसिद्ध 11-दिवसीय धनु जात्रा या धनु यात्रा एक वार्षिक नाटक-आधारित ओपन एयर नाट्य प्रदर्शन है जो ओडिशा के बरगढ़ में मनाया जाता है। बरगढ़ नगरपालिका के आसपास 8 किमी के दायरे में फैला यह दुनिया का सबसे बड़ा ओपन एयर थिएटर है, जिसका नाम गिनीज बुक ऑफ वर्ल्ड रिकॉर्ड में दर्ज है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "49",
                    section: "18",
                    question_en: "<p>49. In which year was the Nahargarh Fort in Jaipur built by Maharaja Sawai Jai Singh II ?</p>",
                    question_hi: "<p>49. जयपुर में महाराजा सवाई जय सिंह द्वितीय द्वारा निर्मित नाहरगढ़ किला किस वर्ष बनाया गया था ?</p>",
                    options_en: ["<p>1805</p>", "<p>1734</p>", 
                                "<p>1780</p>", "<p>1800</p>"],
                    options_hi: ["<p>1805</p>", "<p>1734</p>",
                                "<p>1780</p>", "<p>1800</p>"],
                    solution_en: "<p>49.(b) Nahargarh Fort was built in 1734 by the ruler of that time Maharaja of Jaipur, Sawai Jai Singh II. He constructed the fort to form an important defence ring for the city mainly from the tigers and other wild animals. It was also used to keep an eye on the enemy from a long distance.</p>",
                    solution_hi: "<p>49.(b) नाहरगढ़ किला 1734 में जयपुर के महाराजा सवाई जय सिंह द्वितीय द्वारा बनवाया गया था। उन्होंने मुख्य रूप से बाघों और अन्य जंगली जानवरों से शहर के लिए एक महत्वपूर्ण रक्षा रिंग बनाने के लिए किले का निर्माण किया। इसका इस्तेमाल दूर से दुश्मन पर नजर रखने के लिए भी किया जाता था।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "50",
                    section: "17",
                    question_en: "<p>50. Which of the following rivers flows through Tiruttani, a famous pilgrimage place of South India ?</p>",
                    question_hi: "<p>50. निम्नलिखित में से कौन सी नदियाँ दक्षिण भारत के प्रसिद्ध तीर्थ स्थान तिरुत्तनी से होकर बहती हैं ?</p>",
                    options_en: ["<p>Vaigai</p>", "<p>Nandi</p>", 
                                "<p>Palar</p>", "<p>Kaveri</p>"],
                    options_hi: ["<p>वैगई</p>", "<p>नंदी</p>",
                                "<p>पालर</p>", "<p>कावेरी</p>"],
                    solution_en: "<p>50.(b) Nandi river flows through Tiruttani, a famous pilgrimage place in Karnataka. It is 560 Km long and is also known by the name of Northern penner river.</p>",
                    solution_hi: "<p>50.(b) नंदी नदी कर्नाटक के एक प्रसिद्ध तीर्थ स्थान तिरुत्तानी से होकर बहती है। यह 560 किलोमीटर लंबा है और इसे उत्तरी पेन्नेर नदी के नाम से भी जाना जाता है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "51",
                    section: "17",
                    question_en: "<p>51. A shopkeeper bought 80 kg of rice at a discount of 10%. Besides 1 kg rice was offered free to him on the purchase of every 20 kg rice. If he sells the rice at the marked price, his profit percentage will be:</p>",
                    question_hi: "<p>51. एक दुकानदार ने 10% छूट पर 80 किलो ग्राम चावल ख़रीदा । इसके अलावा, प्रत्येक 20 किलो चावल की ख़रीद पर उसे 1 किलो चावल मुफ्त दिया गया । यदि वह चावल को बाज़ार मूल्य पर बेच देता है, तो उसके लाभ का प्रतिशत होगा ।</p>",
                    options_en: ["<p>15<math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math>%</p>", "<p>16<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math>%</p>", 
                                "<p>15<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math>%</p>", "<p>14<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math>%</p>"],
                    options_hi: ["<p>15<math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math>%</p>", "<p>16<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math>%</p>",
                                "<p>15<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math>%</p>", "<p>14<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math>%</p>"],
                    solution_en: "<p>51.(b) Let M.P of 1kg rice = ₹1<br>Then, M.P of 80 kg rice = ₹ 80<br>But after 10% discount, C.P for customer = ₹ 72<br>Also 1 kg rice was given free for every 20 kg. Therefore, customer got 84 kg rice at ₹ 72<br>Selling price for 84 kg rice = ₹ 84<br>Profit % = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>84</mn><mo>-</mo><mn>72</mn></mrow><mn>72</mn></mfrac></math> &times; 100&nbsp; = 16<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math>%</p>",
                    solution_hi: "<p>51.(b) मान लीजिए 1 किलो चावल का अंकित मूल्य = ₹1<br>तो, 80 किग्रा चावल का अंकित मूल्य = ₹ 80<br>लेकिन 10% छूट के बाद, ग्राहक के लिए क्रय मूल्य = ₹ 72<br>साथ ही हर 20 किलो पर 1 किलो चावल मुफ्त दिया गया। इसलिए ग्राहक को ₹72 में 84 किलो चावल मिला<br>84 किलो चावल का विक्रय मूल्य = ₹ 84<br>लाभ% = <math display=\"inline\"><mfrac><mrow><mn>84</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>72</mn></mrow><mrow><mn>72</mn></mrow></mfrac></math> &times; 100 = 16<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math>%</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "52",
                    section: "17",
                    question_en: "<p>52. The given table represents the exports (in ₹crores) of four items A,B, C and D over a period of six years. Study the table carefully and answer the questions that follows:<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744276377828.png\" alt=\"rId36\" width=\"304\" height=\"190\"> <br>In which year, the exports of item D were 1.4 times the average exports of item B during the six years ?</p>",
                    question_hi: "<p>52. दी गयी तालिका छः वर्षों की अवधि के दौरान चार वस्तुओं A, B, C और D के निर्यात (करोड़ रुपये में) को दर्शाती है | इस तालिका का अध्ययन करें तथा फिर पूछे गए प्रश्नों के उत्तर दें&nbsp;<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744276378015.png\" alt=\"rId37\" width=\"389\" height=\"195\"> <br>किस वर्ष वस्तु D का निर्यात छः वर्षों में वस्तु B के औसत निर्यात का 1.4 गुना रहा है ?</p>",
                    options_en: ["<p>2014</p>", "<p>2013</p>", 
                                "<p>2011</p>", "<p>2012</p>"],
                    options_hi: ["<p>2014</p>", "<p>2013</p>",
                                "<p>2011</p>", "<p>2012</p>"],
                    solution_en: "<p>52.(b) Average export of item B during six years = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>128</mn><mo>+</mo><mn>134</mn><mo>+</mo><mn>138</mn><mo>+</mo><mn>169</mn><mo>+</mo><mn>182</mn><mo>+</mo><mn>209</mn></mrow><mn>6</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>960</mn><mn>6</mn></mfrac></math> = 160 crore<br>1.4 &times; 160 = 224 crore<br>In 2013, export of item D was 224 crore.</p>",
                    solution_hi: "<p>52.(b) छह वर्षों के दौरान आइटम B का औसत निर्यात = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>128</mn><mo>+</mo><mn>134</mn><mo>+</mo><mn>138</mn><mo>+</mo><mn>169</mn><mo>+</mo><mn>182</mn><mo>+</mo><mn>209</mn></mrow><mn>6</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>960</mn><mn>6</mn></mfrac></math> = 160 = 160 करोड़ <br>1.4 &times;&nbsp;160 = 224 करोड़ <br>2013 में आइटम D का निर्यात 224 करोड़ था।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "53",
                    section: "17",
                    question_en: "<p>53. The average of 24 numbers is 56. The average of the first 10 numbers is 71.7 and that of the next 11 numbers is 42. The next three numbers (i.e 22<sup>nd</sup>, 23<sup>rd</sup> and 24<sup>th</sup>) are in the ratio <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math> : <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>1</mn><mn>3</mn></mfrac></mstyle></math> : <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>12</mn></mfrac></math>. What is the average of the 22<sup>nd</sup> and 24<sup>th</sup> numbers ?</p>",
                    question_hi: "<p>53. 24 संख्याओं का औसत 56 है । पहली 10 संख्याओं का औसत 71.7 है तथा अगली 11 संख्याओं का औसत 42 है । अगली तीन संख्याएँ (अर्थात 22वीं, 23वीं तथा 24वीं) <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math> : <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>1</mn><mn>3</mn></mfrac></mstyle></math> : <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>12</mn></mfrac></math>&nbsp;के अनुपात में हैं । 22वीं तथा 24वीं संख्याओं का औसत कितना है ?</p>",
                    options_en: ["<p>49.5</p>", "<p>58</p>", 
                                "<p>55</p>", "<p>60.5</p>"],
                    options_hi: ["<p>49.5</p>", "<p>58</p>",
                                "<p>55</p>", "<p>60.5</p>"],
                    solution_en: "<p>53.(d) The last three numbers are in ratio: <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math> : <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>1</mn><mn>3</mn></mfrac></mstyle></math> : <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>12</mn></mfrac></math> = 6a : 4a : 5a<br>According to question:<br>24 &times; 56 = 10 &times; 71.7 + 11 &times; 42 + 6a + 4a + 5a<br>1344 = 717 + 462 + 15a<br>1344 = 1179 + 15 a<br>a = 11<br><math display=\"inline\"><mfrac><mrow><msup><mrow><mn>22</mn></mrow><mrow><mi>n</mi><mi>d</mi></mrow></msup><mo>+</mo><mi>&#160;</mi><mn>2</mn><msup><mrow><mn>4</mn></mrow><mrow><mi>t</mi><mi>h</mi></mrow></msup></mrow><mrow><mn>2</mn></mrow></mfrac><mi>&#160;</mi></math>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>66</mn><mo>+</mo><mn>55</mn></mrow><mn>2</mn></mfrac></math> = 60.5</p>",
                    solution_hi: "<p>53.(d) अंतिम तीन संख्याएँ अनुपात में हैं: <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math> : <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>1</mn><mn>3</mn></mfrac></mstyle></math> : <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>12</mn></mfrac></math> = 6a : 4a : 5a<br>प्रश्न के अनुसार:<br>24 &times; 56 = 10 &times; 71.7 + 11 &times; 42 + 6a + 4a + 5a<br>1344 = 717 + 462 + 15a<br>1344 = 1179 + 15 a<br>a = 11<br><math style=\"font-family:Verdana\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mn>20</mn><mi>&#2357;&#2368;&#2306;</mi></msup><mo>+</mo><mn>2</mn><msup><mn>4</mn><mi>&#2357;&#2368;&#2306;</mi></msup></mrow><mn>2</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>66</mn><mo>+</mo><mn>55</mn></mrow><mn>2</mn></mfrac></math> = 60.5</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "54",
                    section: "17",
                    question_en: "<p>54. The ratio of ages of A and B, 8 years ago, was 2 : 3. Four years ago, the ratio of their ages was 5 : 7. What will be the ratio of their ages 8 years from now ?</p>",
                    question_hi: "<p>54. 8 वर्ष पहले A और B की उम्र का अनुपात 2 : 3 था | चार वर्ष पहले, उनकी उम्र का अनुपात 5 : 7 था | अब से 8 वर्ष के बाद उनकी उम्र का अनुपात क्या होगा ?</p>",
                    options_en: ["<p>7 : 8</p>", "<p>4 : 5</p>", 
                                "<p>3 : 4</p>", "<p>5 : 6</p>"],
                    options_hi: ["<p>7 : 8</p>", "<p>4 : 5</p>",
                                "<p>3 : 4</p>", "<p>5 : 6</p>"],
                    solution_en: "<p>54.(b) Ratio 8 years ago was = 2 : 3 &hellip;(i) &rArr;&nbsp;difference = 1 unit<br>Ratio 4 years ago was = 5 : 7 &rArr; 2 unit &hellip;(ii)<br>Difference in ratio must be the same as both have increased by the same number of years. Thus, multiply (i) by 2;<br>Ratio 8 years ago was = 4 : 6<br>Ratio 4 years ago was = 5 : 7<br>In 4 years, ratio increase by 1, <br>Thus, 1 ratio = 4 years<br>And <br>Age 4 years ago = 20 years and 28 years<br>Age after 8 years from now will be 32 years and 40 years.<br>Required ratio = 4 : 5</p>",
                    solution_hi: "<p>54.(b) 8 साल पहले अनुपात = 2 : 3 &hellip;(i) <br>&rArr; अंतर = 1 unit<br>4 साल पहले अनुपात = 5 : 7 &rArr; 2 unit &hellip;(ii)<br>अनुपात में अंतर समान होना चाहिए क्योंकि दोनों में समान वर्षों की वृद्धि हुई है। इस प्रकार, (i) को 2 से गुणा करें;<br>8 साल पहले अनुपात = 4 : 6<br>4 साल पहले अनुपात = 5 : 7<br>4 वर्षों में, अनुपात में 1 की वृद्धि हुई,<br>अत: 1 अनुपात = 4 वर्ष<br>और उम्र 4 साल पहले = 20 साल और 28 साल<br>अब से 8 साल बाद उम्र 32 साल और 40 साल होगी।<br>आवश्यक अनुपात = 4 : 5</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "55",
                    section: "17",
                    question_en: "<p>55. If P = <math display=\"inline\"><mfrac><mrow><msup><mrow><mi>x</mi></mrow><mrow><mn>3</mn></mrow></msup><mo>+</mo><msup><mrow><mi>y</mi></mrow><mrow><mn>3</mn></mrow></msup></mrow><mrow><mo>(</mo><mi>x</mi><mo>-</mo><mi>y</mi><msup><mrow><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup><mo>+</mo><mn>3</mn><mi>x</mi><mi>y</mi></mrow></mfrac></math>, Q = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mrow><mo>(</mo><mi>x</mi><mo>+</mo><mi>y</mi><mo>)</mo></mrow><mn>2</mn></msup><mo>-</mo><mn>3</mn><mi>x</mi><mi>y</mi></mrow><mrow><msup><mi>x</mi><mn>3</mn></msup><mo>-</mo><msup><mi>y</mi><mn>3</mn></msup></mrow></mfrac></math> and R = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mrow><mo>(</mo><mi>x</mi><mo>+</mo><mi>y</mi><mo>)</mo></mrow><mn>2</mn></msup><mo>+</mo><msup><mrow><mo>(</mo><mi>x</mi><mo>-</mo><mi>y</mi><mo>)</mo></mrow><mn>2</mn></msup></mrow><mrow><msup><mi>x</mi><mn>2</mn></msup><mo>-</mo><msup><mi>y</mi><mn>2</mn></msup></mrow></mfrac></math>, then what is the value of (P &divide; Q) &times; R ?</p>",
                    question_hi: "<p>55. यदि P = <math display=\"inline\"><mfrac><mrow><msup><mrow><mi>x</mi></mrow><mrow><mn>3</mn></mrow></msup><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><msup><mrow><mi>y</mi></mrow><mrow><mn>3</mn></mrow></msup></mrow><mrow><mo>(</mo><mi>x</mi><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mi>y</mi><msup><mrow><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup><mo>+</mo><mi>&#160;</mi><mn>3</mn><mi>x</mi><mi>y</mi></mrow></mfrac></math>, Q = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mrow><mo>(</mo><mi>x</mi><mo>+</mo><mi>y</mi><mo>)</mo></mrow><mn>2</mn></msup><mo>-</mo><mn>3</mn><mi>x</mi><mi>y</mi></mrow><mrow><msup><mi>x</mi><mn>3</mn></msup><mo>-</mo><msup><mi>y</mi><mn>3</mn></msup></mrow></mfrac></math> तथा R = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mrow><mo>(</mo><mi>x</mi><mo>+</mo><mi>y</mi><mo>)</mo></mrow><mn>2</mn></msup><mo>+</mo><msup><mrow><mo>(</mo><mi>x</mi><mo>-</mo><mi>y</mi><mo>)</mo></mrow><mn>2</mn></msup></mrow><mrow><msup><mi>x</mi><mn>2</mn></msup><mo>-</mo><msup><mi>y</mi><mn>2</mn></msup></mrow></mfrac></math> है, तो (P &divide; Q) &times; R का मान क्या होगा ?</p>",
                    options_en: ["<p>2(x<sup>2 </sup>+ y<sup>2</sup>)&nbsp;</p>", "<p>4xy</p>", 
                                "<p>x<sup>2 </sup>+ y<sup>2</sup></p>", "<p>2xy</p>"],
                    options_hi: ["<p>2(x<sup>2 </sup>+ y<sup>2</sup>)</p>", "<p>4xy</p>",
                                "<p>x<sup>2 </sup>+ y<sup>2</sup></p>", "<p>2xy</p>"],
                    solution_en: "<p>55.(a) P = <math display=\"inline\"><mfrac><mrow><msup><mrow><mi>x</mi></mrow><mrow><mn>3</mn></mrow></msup><mo>+</mo><msup><mrow><mi>y</mi></mrow><mrow><mn>3</mn></mrow></msup></mrow><mrow><mo>(</mo><mi>x</mi><mo>-</mo><mi>y</mi><msup><mrow><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup><mo>+</mo><mn>3</mn><mi>x</mi><mi>y</mi></mrow></mfrac></math>,&nbsp;Q = <math display=\"inline\"><mfrac><mrow><mo>(</mo><mi>x</mi><mo>+</mo><mi>y</mi><msup><mrow><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup><mo>-</mo><mn>3</mn><mi>x</mi><mi>y</mi></mrow><mrow><msup><mrow><mi>x</mi></mrow><mrow><mn>3</mn></mrow></msup><mo>-</mo><msup><mrow><mi>y</mi></mrow><mrow><mn>3</mn></mrow></msup></mrow></mfrac></math> and R =&nbsp;<math display=\"inline\"><mfrac><mrow><mo>(</mo><mi>x</mi><mo>+</mo><mi>y</mi><msup><mrow><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup><mo>+</mo><mo>(</mo><mi>x</mi><mo>-</mo><mi>y</mi><msup><mrow><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup></mrow><mrow><msup><mrow><mi>x</mi></mrow><mrow><mn>2</mn></mrow></msup><mo>-</mo><mi>y</mi><msup><mrow></mrow><mrow><mn>2</mn></mrow></msup></mrow></mfrac></math> <br>(P &divide; Q) &times; R = (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi>x</mi><mn>3</mn></msup><mo>+</mo><msup><mi>y</mi><mn>3</mn></msup></mrow><mrow><msup><mrow><mo>(</mo><mi>x</mi><mo>-</mo><mi>y</mi><mo>)</mo></mrow><mn>2</mn></msup><mo>+</mo><mn>3</mn><mi>x</mi><mi>y</mi></mrow></mfrac></math> &divide; <math display=\"inline\"><mfrac><mrow><mo>(</mo><mi>x</mi><mo>+</mo><mi>y</mi><msup><mrow><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup><mo>-</mo><mn>3</mn><mi>x</mi><mi>y</mi></mrow><mrow><msup><mrow><mi>x</mi></mrow><mrow><mn>3</mn></mrow></msup><mo>-</mo><msup><mrow><mi>y</mi></mrow><mrow><mn>3</mn></mrow></msup></mrow></mfrac></math>) &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mrow><mo>(</mo><mi>x</mi><mo>+</mo><mi>y</mi><mo>)</mo></mrow><mn>2</mn></msup><mo>+</mo><msup><mrow><mo>(</mo><mi>x</mi><mo>-</mo><mi>y</mi><mo>)</mo></mrow><mn>2</mn></msup></mrow><mrow><msup><mi>x</mi><mn>2</mn></msup><mo>-</mo><msup><mi>y</mi><mn>2</mn></msup></mrow></mfrac></math> <br>= <math display=\"inline\"><mfrac><mrow><msup><mrow><mi>x</mi></mrow><mrow><mn>3</mn></mrow></msup><mo>+</mo><msup><mrow><mi>y</mi></mrow><mrow><mn>3</mn></mrow></msup></mrow><mrow><msup><mrow><mi>x</mi></mrow><mrow><mn>2</mn></mrow></msup><mo>+</mo><msup><mrow><mi>y</mi></mrow><mrow><mn>2</mn></mrow></msup><mo>+</mo><mi>x</mi><mi>y</mi></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi>x</mi><mn>3</mn></msup><mo>-</mo><msup><mi>y</mi><mn>3</mn></msup></mrow><mrow><msup><mi>x</mi><mn>2</mn></msup><mo>+</mo><msup><mi>y</mi><mn>2</mn></msup><mo>-</mo><mi>x</mi><mi>y</mi></mrow></mfrac></math> &times; <math display=\"inline\"><mfrac><mrow><mn>2</mn><mo>(</mo><msup><mrow><mi>x</mi></mrow><mrow><mn>2</mn></mrow></msup><mo>+</mo><msup><mrow><mi>y</mi></mrow><mrow><mn>2</mn></mrow></msup><mo>)</mo></mrow><mrow><msup><mrow><mi>x</mi></mrow><mrow><mn>2</mn></mrow></msup><mo>-</mo><mi>y</mi><msup><mrow></mrow><mrow><mn>2</mn></mrow></msup></mrow></mfrac></math>&nbsp;<br>= 2(x<sup>2 </sup>+ y<sup>2</sup>)</p>",
                    solution_hi: "<p>55.(a) P = <math display=\"inline\"><mfrac><mrow><msup><mrow><mi>x</mi></mrow><mrow><mn>3</mn></mrow></msup><mo>+</mo><msup><mrow><mi>y</mi></mrow><mrow><mn>3</mn></mrow></msup></mrow><mrow><mo>(</mo><mi>x</mi><mo>-</mo><mi>y</mi><msup><mrow><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup><mo>+</mo><mn>3</mn><mi>x</mi><mi>y</mi></mrow></mfrac></math>,&nbsp;Q = <math display=\"inline\"><mfrac><mrow><mo>(</mo><mi>x</mi><mo>+</mo><mi>y</mi><msup><mrow><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup><mo>-</mo><mn>3</mn><mi>x</mi><mi>y</mi></mrow><mrow><msup><mrow><mi>x</mi></mrow><mrow><mn>3</mn></mrow></msup><mo>-</mo><msup><mrow><mi>y</mi></mrow><mrow><mn>3</mn></mrow></msup></mrow></mfrac></math> और R =&nbsp;<math display=\"inline\"><mfrac><mrow><mo>(</mo><mi>x</mi><mo>+</mo><mi>y</mi><msup><mrow><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup><mo>+</mo><mo>(</mo><mi>x</mi><mo>-</mo><mi>y</mi><msup><mrow><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup></mrow><mrow><msup><mrow><mi>x</mi></mrow><mrow><mn>2</mn></mrow></msup><mo>-</mo><mi>y</mi><msup><mrow></mrow><mrow><mn>2</mn></mrow></msup></mrow></mfrac></math> <br>(P &divide; Q) &times; R = (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi>x</mi><mn>3</mn></msup><mo>+</mo><msup><mi>y</mi><mn>3</mn></msup></mrow><mrow><msup><mrow><mo>(</mo><mi>x</mi><mo>-</mo><mi>y</mi><mo>)</mo></mrow><mn>2</mn></msup><mo>+</mo><mn>3</mn><mi>x</mi><mi>y</mi></mrow></mfrac></math> &divide; <math display=\"inline\"><mfrac><mrow><mo>(</mo><mi>x</mi><mo>+</mo><mi>y</mi><msup><mrow><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup><mo>-</mo><mn>3</mn><mi>x</mi><mi>y</mi></mrow><mrow><msup><mrow><mi>x</mi></mrow><mrow><mn>3</mn></mrow></msup><mo>-</mo><msup><mrow><mi>y</mi></mrow><mrow><mn>3</mn></mrow></msup></mrow></mfrac></math>) &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mrow><mo>(</mo><mi>x</mi><mo>+</mo><mi>y</mi><mo>)</mo></mrow><mn>2</mn></msup><mo>+</mo><msup><mrow><mo>(</mo><mi>x</mi><mo>-</mo><mi>y</mi><mo>)</mo></mrow><mn>2</mn></msup></mrow><mrow><msup><mi>x</mi><mn>2</mn></msup><mo>-</mo><msup><mi>y</mi><mn>2</mn></msup></mrow></mfrac></math> <br>= <math display=\"inline\"><mfrac><mrow><msup><mrow><mi>x</mi></mrow><mrow><mn>3</mn></mrow></msup><mo>+</mo><msup><mrow><mi>y</mi></mrow><mrow><mn>3</mn></mrow></msup></mrow><mrow><msup><mrow><mi>x</mi></mrow><mrow><mn>2</mn></mrow></msup><mo>+</mo><msup><mrow><mi>y</mi></mrow><mrow><mn>2</mn></mrow></msup><mo>+</mo><mi>x</mi><mi>y</mi></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi>x</mi><mn>3</mn></msup><mo>-</mo><msup><mi>y</mi><mn>3</mn></msup></mrow><mrow><msup><mi>x</mi><mn>2</mn></msup><mo>+</mo><msup><mi>y</mi><mn>2</mn></msup><mo>-</mo><mi>x</mi><mi>y</mi></mrow></mfrac></math> &times; <math display=\"inline\"><mfrac><mrow><mn>2</mn><mo>(</mo><msup><mrow><mi>x</mi></mrow><mrow><mn>2</mn></mrow></msup><mo>+</mo><msup><mrow><mi>y</mi></mrow><mrow><mn>2</mn></mrow></msup><mo>)</mo></mrow><mrow><msup><mrow><mi>x</mi></mrow><mrow><mn>2</mn></mrow></msup><mo>-</mo><mi>y</mi><msup><mrow></mrow><mrow><mn>2</mn></mrow></msup></mrow></mfrac></math>&nbsp;<br>= 2(x<sup>2 </sup>+ y<sup>2</sup>)</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "56",
                    section: "17",
                    question_en: "<p>56. The diagonal of a square A is (a + b) units. What is the area (in square units) of the square drawn on the diagonal of square B whose area is twice the area of square A ?</p>",
                    question_hi: "<p>56. एक वर्ग A का विकर्ण (a + b) इकाई है । वर्ग B, जिसका क्षेत्रफल वर्ग A के क्षेत्रफल से दोगुना है, उसके विकर्ण पर खींचे गए वर्ग का क्षेत्रफल (वर्ग इकाई में) कितना होगा ?</p>",
                    options_en: ["<p>(a + b)<sup>2</sup></p>", "<p>2(a + b)<sup>2</sup></p>", 
                                "<p>4(a + b)<sup>2</sup></p>", "<p>8(a + b)<sup>2</sup></p>"],
                    options_hi: ["<p>(a + b)<sup>2</sup></p>", "<p>2(a + b)<sup>2</sup></p>",
                                "<p>4(a + b)<sup>2</sup></p>", "<p>8(a + b)<sup>2</sup></p>"],
                    solution_en: "<p>56.(b) Let the side of square A = x<br>Diagonal A =&nbsp;<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>2</mn></msqrt></math>x = (a + b) units<br>Side of square B = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>2</mn></msqrt></math>x<br>Area B = 2(a + b)<sup>2</sup></p>",
                    solution_hi: "<p>56.(b) माना वर्ग A की भुजा = x<br>विकर्ण =&nbsp;<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>2</mn></msqrt></math>x = (a + b) इकाई <br>वर्ग B की भुजा = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>2</mn></msqrt></math>x<br>B का क्षेत्रफल = 2(a + b)<sup>2</sup></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "57",
                    section: "17",
                    question_en: "<p>57. If the nine-digit number 708x6y8z9 is divisible by 99, then what is the value of x + y + z ?</p>",
                    question_hi: "<p>57. यदि नौ अंकों की संख्या 708x6y8z9 , 99 से विभाज्य है, तो x + y + z का मान क्या है ?</p>",
                    options_en: ["<p>9</p>", "<p>16</p>", 
                                "<p>5</p>", "<p>27</p>"],
                    options_hi: ["<p>9</p>", "<p>16</p>",
                                "<p>5</p>", "<p>27</p>"],
                    solution_en: "<p>57.(b) It is given that 708x6y8z9 is divisible by 99.<br>Thus, 708x6y8z9 is divisible by both 11 and 9 For divisibility by 9,<br>the sum of digits are divisible by 9 (7 + 0 + 8 + x +6 + y + 8 + z + 9 = 38 + x + y + z.<br>We get 2 as remainder when 3&divide; 9. Thus, 2 + x + y +z must be divisible by 9)<br>Possible values of (z + y + x) = 7,16,25, etc. <br>For divisibility by 11, the difference of sum of digits at odd and even place is divisible by 11 (i.e. in 708x6y8z9 : (9 + 8 + 6 + 8 + 7) - (z + y + x + 0) = 38 - (z + y + x) is divisible by 11)<br>Possible values of (z + y + x) = 38,5,16 etc.<br>In such questions, we must directly verify options.</p>",
                    solution_hi: "<p>57.(b) यह दिया गया है कि 708x6y8z9, 99 से विभाज्य है।<br>अत:, 708x6y8z9, 11 और 9 दोनों से विभाज्य है 9 से विभाज्यता के लिए, अंकों का योग (7 + 0 + 8 + x + 6 + y + 8 + z + 9 = 38 + x + y + z), 9 से विभाज्य है। 38 &divide; 9 होने पर हमें शेषफल 2 मिलता है। इस प्रकार, 2 + x + y + z को 9 से विभाज्य होना चाहिए।<br>(z + y + x) = 7,16,25, आदि के संभावित मान।<br>11 से विभाज्यता के लिए, विषम और सम स्थान पर अंकों के योग का अंतर 11 से विभाज्य है (अर्थात 708x6y8z9 में: (9 + 8 + 6 + 8 + 7) - (z + y + x + 0) = 38 - (z + y + x), 11 से विभाज्य है)<br>(z + y + x) = 38, 5, 16 आदि के संभावित मान।<br>ऐसे प्रश्नों में, हमें सीधे विकल्पों का सत्यापन करना चाहिए।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "58",
                    section: "17",
                    question_en: "<p>58. The value of&nbsp; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3</mn><mfrac><mn>2</mn><mn>3</mn></mfrac><mo>&#247;</mo><mfrac><mn>11</mn><mn>30</mn></mfrac><mi>&#160;</mi><mi>o</mi><mi>f</mi><mi>&#160;</mi><mfrac><mn>2</mn><mn>3</mn></mfrac><mo>-</mo><mfrac><mn>1</mn><mn>4</mn></mfrac><mi>&#160;</mi><mi>o</mi><mi>f</mi><mi>&#160;</mi><mn>2</mn><mfrac><mn>1</mn><mn>2</mn></mfrac><mo>&#247;</mo><mfrac><mn>3</mn><mn>5</mn></mfrac><mo>&#215;</mo><mn>4</mn><mfrac><mn>4</mn><mn>5</mn></mfrac></mrow><mrow><mfrac><mn>2</mn><mn>5</mn></mfrac><mi>&#160;</mi><mi>o</mi><mi>f</mi><mi>&#160;</mi><mn>7</mn><mfrac><mn>1</mn><mn>2</mn></mfrac><mo>&#247;</mo><mfrac><mn>3</mn><mn>4</mn></mfrac><mo>-</mo><mfrac><mn>3</mn><mn>4</mn></mfrac><mo>&#215;</mo><mn>1</mn><mfrac><mn>1</mn><mn>2</mn></mfrac><mo>&#247;</mo><mn>2</mn><mfrac><mn>1</mn><mn>4</mn></mfrac></mrow></mfrac></math> is:</p>",
                    question_hi: "<p>58. <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3</mn><mfrac><mn>2</mn><mn>3</mn></mfrac><mo>&#247;</mo><mfrac><mn>11</mn><mn>30</mn></mfrac><mo>&#160;</mo><mi>&#2325;&#2366;</mi><mo>&#160;</mo><mfrac><mn>2</mn><mn>3</mn></mfrac><mo>-</mo><mfrac><mn>1</mn><mn>4</mn></mfrac><mi>&#2325;&#2366;</mi><mo>&#160;</mo><mn>2</mn><mfrac><mn>1</mn><mn>2</mn></mfrac><mo>&#247;</mo><mfrac><mn>3</mn><mn>5</mn></mfrac><mo>&#215;</mo><mn>4</mn><mfrac><mn>4</mn><mn>5</mn></mfrac></mrow><mrow><mfrac><mn>2</mn><mn>5</mn></mfrac><mi>&#2325;&#2366;</mi><mi>&#160;</mi><mn>7</mn><mfrac><mn>1</mn><mn>2</mn></mfrac><mo>&#247;</mo><mfrac><mn>3</mn><mn>4</mn></mfrac><mo>-</mo><mfrac><mn>3</mn><mn>4</mn></mfrac><mo>&#215;</mo><mn>1</mn><mfrac><mn>1</mn><mn>2</mn></mfrac><mo>&#247;</mo><mn>2</mn><mfrac><mn>1</mn><mn>4</mn></mfrac></mrow></mfrac></math>&nbsp;का मान है :</p>",
                    options_en: ["<p>2<math display=\"inline\"><mfrac><mrow><mn>6</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math></p>", "<p>3<math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math></p>", 
                                "<p>2<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>9</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>10</mn></mrow><mrow><mn>21</mn></mrow></mfrac></math></p>"],
                    options_hi: ["<p>2<math display=\"inline\"><mfrac><mrow><mn>6</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math></p>", "<p>3<math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math></p>",
                                "<p>2<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>9</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>10</mn></mrow><mrow><mn>21</mn></mrow></mfrac></math></p>"],
                    solution_en: "<p>58.(a) Applying BODMAS Rule<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3</mn><mfrac><mn>2</mn><mn>3</mn></mfrac><mo>&#247;</mo><mfrac><mn>11</mn><mn>30</mn></mfrac><mo>&#160;</mo><mi>o</mi><mi>f</mi><mo>&#160;</mo><mfrac><mn>2</mn><mn>3</mn></mfrac><mo>-</mo><mfrac><mn>1</mn><mn>4</mn></mfrac><mo>&#160;</mo><mi>o</mi><mi>f</mi><mo>&#160;</mo><mn>2</mn><mfrac><mn>1</mn><mn>2</mn></mfrac><mo>&#247;</mo><mfrac><mn>3</mn><mn>5</mn></mfrac><mo>&#215;</mo><mn>4</mn><mfrac><mn>4</mn><mn>5</mn></mfrac></mrow><mrow><mfrac><mn>2</mn><mn>5</mn></mfrac><mo>&#160;</mo><mi>o</mi><mi>f</mi><mo>&#160;</mo><mn>7</mn><mfrac><mn>1</mn><mn>2</mn></mfrac><mo>&#247;</mo><mfrac><mn>3</mn><mn>4</mn></mfrac><mo>-</mo><mfrac><mn>3</mn><mn>4</mn></mfrac><mo>&#215;</mo><mn>1</mn><mfrac><mn>1</mn><mn>2</mn></mfrac><mo>&#247;</mo><mn>2</mn><mfrac><mn>1</mn><mn>4</mn></mfrac></mrow></mfrac></math><br>&rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mfrac><mn>11</mn><mn>3</mn></mfrac><mo>&#247;</mo><mfrac><mn>11</mn><mn>45</mn></mfrac><mo>-</mo><mfrac><mn>5</mn><mn>8</mn></mfrac><mo>&#247;</mo><mfrac><mn>3</mn><mn>5</mn></mfrac><mo>&#215;</mo><mfrac><mn>24</mn><mn>5</mn></mfrac></mrow><mrow><mn>3</mn><mo>&#247;</mo><mfrac><mn>3</mn><mn>4</mn></mfrac><mo>-</mo><mfrac><mn>3</mn><mn>4</mn></mfrac><mo>&#215;</mo><mfrac><mn>3</mn><mn>2</mn></mfrac><mo>&#247;</mo><mfrac><mn>9</mn><mn>4</mn></mfrac></mrow></mfrac></math><br>&rArr; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>15</mn><mo>-</mo><mn>5</mn></mrow><mrow><mn>4</mn><mo>-</mo><mfrac><mn>1</mn><mn>2</mn></mfrac></mrow></mfrac></math> = 2<math display=\"inline\"><mfrac><mrow><mn>6</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math></p>",
                    solution_hi: "<p>58.(a) BODMAS नियम लागू करें<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3</mn><mfrac><mn>2</mn><mn>3</mn></mfrac><mo>&#247;</mo><mfrac><mn>11</mn><mn>30</mn></mfrac><mo>&#160;</mo><mi>&#2325;&#2366;</mi><mo>&#160;</mo><mfrac><mn>2</mn><mn>3</mn></mfrac><mo>-</mo><mfrac><mn>1</mn><mn>4</mn></mfrac><mi>&#2325;&#2366;</mi><mo>&#160;</mo><mn>2</mn><mfrac><mn>1</mn><mn>2</mn></mfrac><mo>&#247;</mo><mfrac><mn>3</mn><mn>5</mn></mfrac><mo>&#215;</mo><mn>4</mn><mfrac><mn>4</mn><mn>5</mn></mfrac></mrow><mrow><mfrac><mn>2</mn><mn>5</mn></mfrac><mi>&#2325;&#2366;</mi><mi>&#160;</mi><mn>7</mn><mfrac><mn>1</mn><mn>2</mn></mfrac><mo>&#247;</mo><mfrac><mn>3</mn><mn>4</mn></mfrac><mo>-</mo><mfrac><mn>3</mn><mn>4</mn></mfrac><mo>&#215;</mo><mn>1</mn><mfrac><mn>1</mn><mn>2</mn></mfrac><mo>&#247;</mo><mn>2</mn><mfrac><mn>1</mn><mn>4</mn></mfrac></mrow></mfrac></math><br>&rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mfrac><mn>11</mn><mn>3</mn></mfrac><mo>&#247;</mo><mfrac><mn>11</mn><mn>45</mn></mfrac><mo>-</mo><mfrac><mn>5</mn><mn>8</mn></mfrac><mo>&#247;</mo><mfrac><mn>3</mn><mn>5</mn></mfrac><mo>&#215;</mo><mfrac><mn>24</mn><mn>5</mn></mfrac></mrow><mrow><mn>3</mn><mo>&#247;</mo><mfrac><mn>3</mn><mn>4</mn></mfrac><mo>-</mo><mfrac><mn>3</mn><mn>4</mn></mfrac><mo>&#215;</mo><mfrac><mn>3</mn><mn>2</mn></mfrac><mo>&#247;</mo><mfrac><mn>9</mn><mn>4</mn></mfrac></mrow></mfrac></math><br>&rArr; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>15</mn><mo>-</mo><mn>5</mn></mrow><mrow><mn>4</mn><mo>-</mo><mfrac><mn>1</mn><mn>2</mn></mfrac></mrow></mfrac></math> = 2<math display=\"inline\"><mfrac><mrow><mn>6</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "59",
                    section: "17",
                    question_en: "<p>59. A train takes 2<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math>hours less for a journey of 300 km, if its speed is increased by 20km/h from its usual speed . How much time will it take to cover a distance of 192 km at its usual speed ?</p>",
                    question_hi: "<p>59. यदि एक ट्रेन की चाल उसकी सामान्य चाल से 20 किमी/घंटा बढ़ा दी जाए, तो इसे 300 किमी की यात्रा में 2<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> घंटे कम लगते हैं | अपनी सामान्य चाल से इसे 192 किमी की दूरी तय करने में कितना समय (घंटा) लगेगा ?</p>",
                    options_en: ["<p>4.8</p>", "<p>2.4</p>", 
                                "<p>3</p>", "<p>6</p>"],
                    options_hi: ["<p>4.8</p>", "<p>2.4</p>",
                                "<p>3</p>", "<p>6</p>"],
                    solution_en: "<p>59.(a) For such question, we directly put values in given formula:<br>Distance = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>S</mi><mi>p</mi><mi>e</mi><mi>e</mi><mi>d</mi><mo>&#215;</mo><mo>(</mo><mi>S</mi><mi>p</mi><mi>e</mi><mi>e</mi><mi>d</mi><mo>&#177;</mo><mi>&#160;</mi><mi>i</mi><mi>n</mi><mi>c</mi><mi>r</mi><mi>e</mi><mi>a</mi><mi>s</mi><mi>e</mi><mi>&#160;</mi><mi>i</mi><mi>n</mi><mi>&#160;</mi><mi>s</mi><mi>p</mi><mi>e</mi><mi>e</mi><mi>d</mi><mo>)</mo></mrow><mrow><mi>i</mi><mi>n</mi><mi>c</mi><mi>r</mi><mi>e</mi><mi>a</mi><mi>s</mi><mi>e</mi><mi>&#160;</mi><mi>i</mi><mi>n</mi><mi>&#160;</mi><mi>s</mi><mi>p</mi><mi>e</mi><mi>e</mi><mi>d</mi></mrow></mfrac></math> &times; time difference<br>Let usual speed of train = S km/h<br>&rArr; 300 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>S</mi><mo>&#215;</mo><mo>(</mo><mi>S</mi><mo>+</mo><mn>20</mn><mo>)</mo></mrow><mn>20</mn></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>2</mn></mfrac></math><br>&rArr; S = 40 km/h <br>&rArr; 192 = 40 &times; time<br>&rArr; Time = 4.8 hours</p>",
                    solution_hi: "<p>59.(a) ऐसे प्रश्न के लिए, हम सीधे दिए गए सूत्र में मान रखते हैं:<br>दूरी <br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2330;&#2366;&#2354;</mi><mo>&#215;</mo><mo>(</mo><mi>&#2330;&#2366;&#2354;</mi><mo>&#160;</mo><mo>&#177;</mo><mo>&#160;</mo><mi>&#2330;&#2366;&#2354;</mi><mo>&#160;</mo><mi>&#2350;&#2375;&#2306;</mi><mo>&#160;</mo><mi>&#2357;&#2371;&#2342;&#2381;&#2343;&#2367;</mi><mo>)</mo></mrow><mrow><mi>&#2330;&#2366;&#2354;</mi><mo>&#160;</mo><mi>&#2350;&#2375;&#2306;</mi><mo>&#160;</mo><mi>&#2357;&#2371;&#2342;&#2381;&#2343;&#2367;</mi></mrow></mfrac></math> &times; समय में अंतर<br>माना ट्रैन की गति = S किमी/घंटा<br>&rArr; 300 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2327;&#2340;&#2367;</mi><mo>&#215;</mo><mo>(</mo><mi>&#2327;&#2340;&#2367;</mi><mo>+</mo><mn>20</mn><mo>)</mo></mrow><mn>20</mn></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>2</mn></mfrac></math><br>&rArr; गति = 40 किमी/घंटा<br>&rArr; 192 = 40 &times; समय<br>&rArr; समय = 4.8 घंटे<strong id=\"docs-internal-guid-301a695e-7fff-3e58-023f-0d185ce0795e\"> </strong></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "60",
                    section: "17",
                    question_en: "<p>60. The compound interest on a certain sum at 16<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math>% p.a. for 3 years is ₹ 6,350. What will be the simple interest on the same sum at the same rate for 5<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> years ?</p>",
                    question_hi: "<p>60. एक निश्चित राशि पर 16<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac><mi>%</mi></math> प्रति वर्ष की दर से 3 वर्षों का चक्रवृद्धि ब्याज 6,350 रुपये है | इसी राशि पर इसी दर से 5<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> वर्षों का साधारण ब्याज कितना होगा ?</p>",
                    options_en: ["<p>₹ 7,620</p>", "<p>₹ 9,600</p>", 
                                "<p>₹ 11,400</p>", "<p>₹ 10,200</p>"],
                    options_hi: ["<p>₹ 7,620</p>", "<p>₹ 9,600</p>",
                                "<p>₹ 11,400</p>", "<p>₹ 10,200</p>"],
                    solution_en: "<p>60.(d) Rate of interest = 16<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math>% p.a. = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math><br>This question can be solved by either of two methods:<br><strong>Method 1:</strong><br>Rate = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>a</mi></mfrac></math> and time = 3 years.<br>Let Principal = (a)<sup>3 </sup>= (6)<sup>3</sup> = 216 units<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744276378153.png\" alt=\"rId38\" width=\"126\" height=\"174\"><br>Total CI = 36 + 36 + 36 + 6 + 6 + 6 + 1 = 127 Units<br>127 units = ₹ 6350<br>1 unit = ₹ 50<br>Principal = ₹ 216 &times; 50&nbsp;<br>Simple Interest = <math display=\"inline\"><mfrac><mrow><mn>216</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>50</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>17</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>1</mn></mrow><mrow><mn>3</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>6</mn></mrow></mfrac></math><br>= ₹ 10200<br><strong>Short-trick:</strong><br>For simple interest, time given = <math display=\"inline\"><mfrac><mrow><mn>17</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math><br>Check the option which is multiple of 17 and mark the answer.</p>",
                    solution_hi: "<p>60.(d) ब्याज की दर = 16<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math>% p.a.= <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math><br>इस प्रश्न को दो विधियों में से किसी एक द्वारा हल किया जा सकता है:<br><strong>विधि 1: </strong>दर = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>a</mi></mfrac></math> और समय = 3 वर्ष.<br>माना की, मूलधन = (a)<sup>3 </sup>= (6)<sup>3</sup> = 216 इकाई<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744276378153.png\" alt=\"rId38\" width=\"126\" height=\"174\"><br>कुल CI = 36 + 36 + 36 + 6 + 6 + 6 + 1 <br>= 127 इकाई<br>127 इकाई = ₹ 6350<br>1 इकाई = ₹ 50<br>मूलधन = ₹ 216<math display=\"inline\"><mo>&#215;</mo></math>50 <br>साधारण ब्याज = <math display=\"inline\"><mfrac><mrow><mn>216</mn><mo>&#215;</mo><mn>50</mn><mo>&#215;</mo><mn>17</mn><mo>&#215;</mo><mn>1</mn></mrow><mrow><mn>3</mn><mo>&#215;</mo><mn>6</mn></mrow></mfrac></math> = ₹ 10200<br><strong>विधि 2:</strong> <br>साधारण ब्याज के लिए , दिया गया समय = <math display=\"inline\"><mfrac><mrow><mn>17</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math><br>उस विकल्प की जाँच करें जो 17 का गुणज है और उत्तर को चिह्नित करें।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "61",
                    section: "17",
                    question_en: "<p>61. If 12cos<sup>2</sup>&theta; - 2sin<sup>2</sup>&theta; + 3cos&theta; = 3, 0&deg; &lt; &theta; &lt; 90&deg;, then what is the value of&nbsp;<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>cosec&#952;</mi><mo>+</mo><mi>sec&#952;</mi></mrow><mrow><mi>tan&#952;</mi><mo>+</mo><mi>cot&#952;</mi></mrow></mfrac></math> ?</p>",
                    question_hi: "<p>61. यदि 12cos<sup>2</sup>&theta; - 2sin<sup>2</sup>&theta; + 3cos&theta; = 3, 0&deg; &lt; &theta; &lt; 90&deg; है, तो <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>cosec</mi><mo>&#160;</mo><mi mathvariant=\"normal\">&#952;</mi><mo>+</mo><mi>sec</mi><mo>&#160;</mo><mi mathvariant=\"normal\">&#952;</mi></mrow><mrow><mi>tan</mi><mo>&#160;</mo><mi mathvariant=\"normal\">&#952;</mi><mo>+</mo><mi>cot</mi><mo>&#160;</mo><mi mathvariant=\"normal\">&#952;</mi></mrow></mfrac></math>&nbsp;का मान क्या होगा ?</p>",
                    options_en: ["<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>4</mn><mo>+</mo><msqrt><mn>3</mn></msqrt></mrow><mn>4</mn></mfrac></math></p>", "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mo>+</mo><mn>2</mn><msqrt><mn>2</mn></msqrt></mrow><mn>2</mn></mfrac></math></p>", 
                                "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mo>+</mo><msqrt><mn>3</mn></msqrt></mrow><mn>4</mn></mfrac></math></p>", "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mo>+</mo><msqrt><mn>3</mn></msqrt></mrow><mn>2</mn></mfrac></math></p>"],
                    options_hi: ["<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>4</mn><mo>+</mo><msqrt><mn>3</mn></msqrt></mrow><mn>4</mn></mfrac></math></p>", "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mo>+</mo><mn>2</mn><msqrt><mn>2</mn></msqrt></mrow><mn>2</mn></mfrac></math></p>",
                                "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mo>+</mo><msqrt><mn>3</mn></msqrt></mrow><mn>4</mn></mfrac></math></p>", "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mo>+</mo><msqrt><mn>3</mn></msqrt></mrow><mn>2</mn></mfrac></math></p>"],
                    solution_en: "<p>61.(d) 12cos<sup>2</sup>&theta; - 2sin<sup>2</sup>&theta; + 3cos&theta; = 3, 0&deg; &lt; &theta; &lt; 90&deg;<br>Put sin<sup>2</sup>&theta; = 1 - cos<sup>2</sup>&theta;<br>We get: 14 cos2&theta; + 3 cos&theta; - 5 = 0 <br>cos&theta; = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> &rArr; &theta; = 60&deg;<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>cosec&#952;</mi><mi mathvariant=\"normal\">&#160;</mi><mo>+</mo><mi mathvariant=\"normal\">&#160;</mi><mi>sec&#952;</mi></mrow><mrow><mi>tan&#952;</mi><mi mathvariant=\"normal\">&#160;</mi><mo>+</mo><mi mathvariant=\"normal\">&#160;</mi><mi>cot&#952;</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>cos</mi><mi>e</mi><mi>c</mi><mo>&#160;</mo><mn>60</mn><mo>&#176;</mo><mo>+</mo><mi>s</mi><mi>e</mi><mi>c</mi><mo>&#160;</mo><mn>60</mn><mo>&#176;</mo></mrow><mrow><mi>tan</mi><mo>&#160;</mo><mn>60</mn><mo>&#176;</mo><mo>+</mo><mi>c</mi><mi>o</mi><mi>t</mi><mo>&#160;</mo><mn>60</mn><mo>&#176;</mo></mrow></mfrac></math> <br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mfrac><mn>2</mn><msqrt><mn>3</mn></msqrt></mfrac><mo>+</mo><mn>2</mn></mrow><mrow><msqrt><mn>3</mn></msqrt><mo>+</mo><mfrac><mn>1</mn><msqrt><mn>3</mn></msqrt></mfrac></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mo>+</mo><msqrt><mn>3</mn></msqrt></mrow><mn>2</mn></mfrac></math></p>",
                    solution_hi: "<p>61.(d) 12cos<sup>2</sup>&theta; - 2sin<sup>2</sup>&theta; + 3cos&theta; = 3, 0&deg; &lt; &theta; &lt; 90&deg;<br>sin<sup>2</sup>&theta; = 1 - cos<sup>2</sup>&theta; रखें।<br>14 cos2&theta; + 3 cos&theta; - 5 = 0&nbsp;<br>cos&theta; = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> &rArr; &theta; = 60&deg;<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mi>e</mi><mi>c</mi><mi>&#952;</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>s</mi><mi>e</mi><mi>c</mi><mi>&#952;</mi></mrow><mrow><mi>t</mi><mi>a</mi><mi>n</mi><mi>&#952;</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>c</mi><mi>o</mi><mi>t</mi><mi>&#952;</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>cos</mi><mi>e</mi><mi>c</mi><mo>&#160;</mo><mn>60</mn><mo>&#176;</mo><mo>+</mo><mi>s</mi><mi>e</mi><mi>c</mi><mo>&#160;</mo><mn>60</mn><mo>&#176;</mo></mrow><mrow><mi>tan</mi><mo>&#160;</mo><mn>60</mn><mo>&#176;</mo><mo>+</mo><mi>c</mi><mi>o</mi><mi>t</mi><mo>&#160;</mo><mn>60</mn><mo>&#176;</mo></mrow></mfrac></math> <br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mfrac><mn>2</mn><msqrt><mn>3</mn></msqrt></mfrac><mo>+</mo><mn>2</mn></mrow><mrow><msqrt><mn>3</mn></msqrt><mo>+</mo><mfrac><mn>1</mn><msqrt><mn>3</mn></msqrt></mfrac></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mo>+</mo><msqrt><mn>3</mn></msqrt></mrow><mn>2</mn></mfrac></math></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "62",
                    section: "17",
                    question_en: "<p>62. Sudha sold an article to Renu for ₹576 at a loss of 20%. Renu spent a sum of ₹224 on its transportation and sold it to Raghu at a price which would have given Sudha a profit of 24%. The percentage of gain for Renu is:</p>",
                    question_hi: "<p>62. सुधा ने 20% हानि पर रेणु को एक वस्तु 576 रुपये में बेच दी । रेणु ने इसके परिवहन पर 224 रुपये खर्च किये तथा रघु को उस कीमत पर बेच दिया जिस कीमत पर सुधा को 24% का लाभ होता । रेणु के लिए प्रतिशत लाभ है :</p>",
                    options_en: ["<p>10.5%</p>", "<p>11.6%</p>", 
                                "<p>12.9%</p>", "<p>13.2%</p>"],
                    options_hi: ["<p>10.5%</p>", "<p>11.6%</p>",
                                "<p>12.9%</p>", "<p>13.2%</p>"],
                    solution_en: "<p>62.(b) C.P for Sudha = ₹ 576 &times; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>100</mn><mn>80</mn></mfrac></math> = ₹ 720<br>C.P. for Renu = ₹ (576 + 224) = ₹ 800<br>C.P for Raghu = ₹ <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>124</mn><mn>100</mn></mfrac></math> &times; 720 = ₹ 892.80<br>Renu profit %&nbsp;= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>892</mn><mo>.</mo><mn>80</mn><mo>-</mo><mn>800</mn></mrow><mn>800</mn></mfrac></math> &times; 100 = 11.6%</p>",
                    solution_hi: "<p>62.(b) <br>सुधा के लिए क्रय मूल्य ₹ 576 &times; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>100</mn><mn>80</mn></mfrac></math> = ₹ 720<br>रेणु के लिए क्रय मूल्य = ₹ (576 + 224) = ₹ 800<br>रघु के लिए क्रय मूल्य = ₹ <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>124</mn><mn>100</mn></mfrac></math> &times; 720&nbsp;= ₹ 892.80<br>रेणु का लाभ % =&nbsp;<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>892</mn><mo>.</mo><mn>80</mn><mo>-</mo><mn>800</mn></mrow><mn>800</mn></mfrac></math> &times; 100&nbsp;= 11.6%</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "63",
                    section: "17",
                    question_en: "<p>63. Pipes A and B can fill a tank in 10 hours and 40 hours, respectively. C is an outlet pipe attached to the tank. If all the three pipes are opened simultaneously, it takes 80 minutes more time than what A and B together take to fill the tank. A and B are kept open for 7 hours and then closed and C is opened. C will now empty the tank in:</p>",
                    question_hi: "<p>63. पाइप A तथा B किसी टंकी को क्रमशः 10 घंटे तथा 40 घंटे में भर सकते हैं | C एक निकास पाइप है जो टंकी से जुड़ा हुआ है | यदि सभी तीन पाइपों को एक साथ चालू कर दिया जाए, तो टंकी को भरने में A और B के द्वारा एक साथ लिए गए समय की तुलना में 80 मिनट अधिक लगते हैं | A और B को 7 घंटों तक चालू छोड़ा जाता है तथा फिर बंद करके पाइप C को चालू किया जाता है | C इस टंकी को कितने समय में खाली करेगा ?</p>",
                    options_en: ["<p>45.5 hours</p>", "<p>38.5 hours</p>", 
                                "<p>42 hours</p>", "<p>49 hours</p>"],
                    options_hi: ["<p>45.5 घंटे</p>", "<p>38.5 घंटे</p>",
                                "<p>42 घंटे</p>", "<p>49 घंटे</p>"],
                    solution_en: "<p>63.(d) <br>Pipe A can fill a tank in 10 hours <br>Pipe B can fill a tank in 40 hours <br>Total capacity = l cm (10, 40) = 40 units<br>In 1 hour, Pipe A fills = 4 units and pipe B fills = 1 unit<br>A and B together fills tank in <math display=\"inline\"><mfrac><mrow><mn>40</mn></mrow><mrow><mn>4</mn><mo>+</mo><mn>1</mn></mrow></mfrac></math> = 8 hour<br>Together A, B and C fill the tank in 8 +<math display=\"inline\"><mi>&#160;</mi><mfrac><mrow><mn>80</mn></mrow><mrow><mn>60</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>28</mn><mn>3</mn></mfrac></math>hours<br><strong id=\"docs-internal-guid-c1dda3b5-7fff-52ab-1384-220f4ab3f52b\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcxU8z-iJgGG0yThoL1jLiHAQs7Ds6EKBa_gOXQc8D9GSjh2U69Qh2XDAhJx12vwtSyxvQFpXep7bMLTMZ4sLyUtiQCgJl5WRvIcBklIQsGOEe5TdQqQYtc_HfARPLuweMqZrDDuQ?key=eh23hdtQJfGadeVMkKnzW2-L\" width=\"165\" height=\"132\"></strong><br>&rArr; Efficiency of C = 1 unit<br>Total capacity of tank = 56 units<br>In 7 hours, A and B fills = 7 <math display=\"inline\"><mo>&#215;</mo></math> 7 = 49 units<br>Time taken by C to empty 49 units = 49 hours</p>",
                    solution_hi: "<p>63.(d)<br>पाइप A एक टैंक को 10 घंटे में भर सकता है<br>पाइप B एक टैंक को 40 घंटे में भर सकता है<br>कुल क्षमता = LCM (10, 40) = 40 इकाइयाँ<br>1 घंटे में, पाइप A भरता है = 4 इकाई और पाइप B भरता है = 1 इकाई <br>A और B को मिलाकर टंकी को भरने में लिया गया समय = <math display=\"inline\"><mi>&#160;</mi><mfrac><mrow><mn>40</mn></mrow><mrow><mn>4</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>1</mn></mrow></mfrac></math>= 8 घंटे<br>A, B और C को मिलाकर टंकी को भरने में लिया गया समय = 8 + <math display=\"inline\"><mfrac><mrow><mn>80</mn></mrow><mrow><mn>60</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>28</mn><mn>3</mn></mfrac></math>घंटे<br><strong id=\"docs-internal-guid-c1dda3b5-7fff-52ab-1384-220f4ab3f52b\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcxU8z-iJgGG0yThoL1jLiHAQs7Ds6EKBa_gOXQc8D9GSjh2U69Qh2XDAhJx12vwtSyxvQFpXep7bMLTMZ4sLyUtiQCgJl5WRvIcBklIQsGOEe5TdQqQYtc_HfARPLuweMqZrDDuQ?key=eh23hdtQJfGadeVMkKnzW2-L\" width=\"165\" height=\"132\"></strong><br>&rArr; C की क्षमता = 1 इकाई<br>टैंक की कुल क्षमता = 56 इकाई<br>7 घंटे में, A और B भरता है = 7 &times; 7 <br>= 49 इकाइयां<br>C द्वारा 49 इकाई खाली करने में लिया गया समय = 49 घंटे</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "64",
                    section: "17",
                    question_en: "<p>64. In &Delta;ABC, &ang;C = 90&deg;, AC = 5cm and BC = 12 cm. The bisector of &ang;A meets BC at D. What is the length of AD ?</p>",
                    question_hi: "<p>64. त्रिभुज ABC में, &ang;C = 90&deg;, AC = 5 सेमी तथा BC = 12 सेमी है । कोण A का समद्विभाजक BC से D पर मिलता है । AD की लंबाई कितनी है ?</p>",
                    options_en: ["<p><math display=\"inline\"><mfrac><mrow><mn>5</mn><msqrt><mn>13</mn></msqrt></mrow><mrow><mn>3</mn></mrow></mfrac></math>cm</p>", "<p><math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>3</mn></mrow></mfrac><msqrt><mn>13</mn></msqrt></math>cm</p>", 
                                "<p>2<math display=\"inline\"><msqrt><mn>13</mn></msqrt></math>cm</p>", "<p><math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac><msqrt><mn>13</mn></msqrt></math>cm</p>"],
                    options_hi: ["<p><math display=\"inline\"><mfrac><mrow><mn>5</mn><msqrt><mn>13</mn></msqrt></mrow><mrow><mn>3</mn></mrow></mfrac></math>cm</p>", "<p><math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>3</mn></mrow></mfrac><msqrt><mn>13</mn></msqrt></math>cm</p>",
                                "<p>2<math display=\"inline\"><msqrt><mn>13</mn></msqrt></math>cm</p>", "<p><math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac><msqrt><mn>13</mn></msqrt></math>cm</p>"],
                    solution_en: "<p>64.(a)<br>AD is the angle bisector of &ang;A<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744276378390.png\" alt=\"rId40\" width=\"237\" height=\"111\"><br>According to angle bisector theorem: <br><math display=\"inline\"><mfrac><mrow><mi>A</mi><mi>C</mi></mrow><mrow><mi>A</mi><mi>B</mi></mrow></mfrac><mo>=</mo><mfrac><mrow><mi>C</mi><mi>D</mi></mrow><mrow><mi>B</mi><mi>D</mi></mrow></mfrac></math> &rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>13</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>x</mi><mrow><mn>12</mn><mo>-</mo><mi>x</mi></mrow></mfrac></math> &rArr; x = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>10</mn><mn>3</mn></mfrac></math><br>AD = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mrow><mo>(</mo><mn>5</mn><mo>)</mo></mrow><mn>2</mn></msup><mo>+</mo><msup><mrow><mo>(</mo><mfrac><mn>10</mn><mn>3</mn></mfrac><mo>)</mo></mrow><mn>2</mn></msup></msqrt></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><msqrt><mn>13</mn></msqrt></mrow><mrow><mn>3</mn><mo>&#160;</mo></mrow></mfrac></math> cm</p>",
                    solution_hi: "<p>64.(a) AD, &ang;A का कोण समद्विभाजक है<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744276378390.png\" alt=\"rId40\" width=\"237\" height=\"111\"><br>कोण द्विभाजक प्रमेय के अनुसार:<br><math display=\"inline\"><mfrac><mrow><mi>A</mi><mi>C</mi></mrow><mrow><mi>A</mi><mi>B</mi></mrow></mfrac><mo>=</mo><mfrac><mrow><mi>C</mi><mi>D</mi></mrow><mrow><mi>B</mi><mi>D</mi></mrow></mfrac></math> &rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>13</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>x</mi><mrow><mn>12</mn><mo>-</mo><mi>x</mi></mrow></mfrac></math> &rArr; x = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>10</mn><mn>3</mn></mfrac></math><br>AD = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mrow><mo>(</mo><mn>5</mn><mo>)</mo></mrow><mn>2</mn></msup><mo>+</mo><msup><mrow><mo>(</mo><mfrac><mn>10</mn><mn>3</mn></mfrac><mo>)</mo></mrow><mn>2</mn></msup></msqrt></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><msqrt><mn>13</mn></msqrt></mrow><mrow><mn>3</mn><mo>&#160;</mo></mrow></mfrac></math> cm</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "65",
                    section: "17",
                    question_en: "<p>65. If 5sin&theta; = 4, then the value of <math display=\"inline\"><mfrac><mrow><mi>s</mi><mi>e</mi><mi>c</mi><mi>&#952;</mi><mo>+</mo><mn>4</mn><mi>c</mi><mi>o</mi><mi>t</mi><mi>&#952;</mi></mrow><mrow><mn>4</mn><mi>t</mi><mi>a</mi><mi>n</mi><mi>&#952;</mi><mo>-</mo><mn>5</mn><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#952;</mi></mrow></mfrac></math> is:</p>",
                    question_hi: "<p>65. यदि 5sin&theta; = 4 है, तो <math display=\"inline\"><mfrac><mrow><mi>s</mi><mi>e</mi><mi>c</mi><mi>&#952;</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>4</mn><mi>c</mi><mi>o</mi><mi>t</mi><mi>&#952;</mi></mrow><mrow><mn>4</mn><mi>t</mi><mi>a</mi><mi>n</mi><mi>&#952;</mi><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>5</mn><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#952;</mi></mrow></mfrac></math> का मान क्या होगा ?</p>",
                    options_en: ["<p><math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math></p>", 
                                "<p>2</p>", "<p>1</p>"],
                    options_hi: ["<p><math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math></p>",
                                "<p>2</p>", "<p>1</p>"],
                    solution_en: "<p>65.(c) sin&theta; = <math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math><br>&rArr; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>s</mi><mi>e</mi><mi>c</mi><mi>&#952;</mi><mo>+</mo><mn>4</mn><mi>c</mi><mi>o</mi><mi>t</mi><mi>&#952;</mi></mrow><mrow><mn>4</mn><mi>t</mi><mi>a</mi><mi>n</mi><mi>&#952;</mi><mo>-</mo><mn>5</mn><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#952;</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mstyle displaystyle=\"true\"><mfrac><mn>5</mn><mn>3</mn></mfrac></mstyle><mo>)</mo><mo>+</mo><mn>4</mn><mo>(</mo><mstyle displaystyle=\"true\"><mfrac><mn>3</mn><mn>4</mn></mfrac></mstyle><mo>)</mo></mrow><mrow><mn>4</mn><mo>(</mo><mstyle displaystyle=\"true\"><mfrac><mn>4</mn><mn>3</mn></mfrac></mstyle><mo>)</mo><mo>-</mo><mn>5</mn><mo>(</mo><mstyle displaystyle=\"true\"><mfrac><mn>3</mn><mn>5</mn></mfrac></mstyle><mo>)</mo></mrow></mfrac></math> = 2</p>",
                    solution_hi: "<p>65.(c) sin&theta; = <math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math><br>&rArr; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>s</mi><mi>e</mi><mi>c</mi><mi>&#952;</mi><mo>+</mo><mn>4</mn><mi>c</mi><mi>o</mi><mi>t</mi><mi>&#952;</mi></mrow><mrow><mn>4</mn><mi>t</mi><mi>a</mi><mi>n</mi><mi>&#952;</mi><mo>-</mo><mn>5</mn><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#952;</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mstyle displaystyle=\"true\"><mfrac><mn>5</mn><mn>3</mn></mfrac></mstyle><mo>)</mo><mo>+</mo><mn>4</mn><mo>(</mo><mstyle displaystyle=\"true\"><mfrac><mn>3</mn><mn>4</mn></mfrac></mstyle><mo>)</mo></mrow><mrow><mn>4</mn><mo>(</mo><mstyle displaystyle=\"true\"><mfrac><mn>4</mn><mn>3</mn></mfrac></mstyle><mo>)</mo><mo>-</mo><mn>5</mn><mo>(</mo><mstyle displaystyle=\"true\"><mfrac><mn>3</mn><mn>5</mn></mfrac></mstyle><mo>)</mo></mrow></mfrac></math> = 2</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "66",
                    section: "17",
                    question_en: "<p>66. In an examination in which the full marks were 500, A scored 25% more marks than B. B scored 60% more marks than C and C scored 20% less marks than D. If A scored 80% marks, then the percentage of marks obtained by D is:</p>",
                    question_hi: "<p>66. एक परीक्षा जिसमें पूर्णांक 500 है, उसमें A को B से 25% अधिक अंक आए हैं । B को C से 60% अधिक अंक मिले हैं तथा C को D से 20% कम अंक मिले हैं । यदि A को 80% अंक प्राप्त हुए हैं, तो D के द्वारा प्राप्त अंक का प्रतिशत ज्ञात कीजिए</p>",
                    options_en: ["<p>60%</p>", "<p>54%</p>", 
                                "<p>65%</p>", "<p>50%</p>"],
                    options_hi: ["<p>60%</p>", "<p>54%</p>",
                                "<p>65%</p>", "<p>50%</p>"],
                    solution_en: "<p>66.(d) According to question:<br>Marks scored by A : B : C : D = 40 : 32 : 20 : 25<br>A = 40 x = 80%<br>Then, D = 50%</p>",
                    solution_hi: "<p>66.(d) प्रश्न के अनुसार:<br>A : B : C : D द्वारा बनाए गए अंक = 40 : 32 : 20 : 25<br>A = 40x = 80%<br>फिर , D = 50%</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "67",
                    section: "17",
                    question_en: "<p>67. D is the midpoint of BC of &Delta;ABC. Point E lies on AC such that CE = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math>AC. BE and AD intersect at G. What is <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>A</mi><mi>G</mi></mrow><mrow><mi>G</mi><mi>D</mi></mrow></mfrac></math> ?</p>",
                    question_hi: "<p>67. D त्रिभुज ABC का मध्य बिंदु है । बिंदु E, AC पर इस प्रकार स्थित है कि CE = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math>AC है । BE और AD एक-दूसरे को D पर प्रतिच्छेद करते हैं । <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>A</mi><mi>G</mi></mrow><mrow><mi>G</mi><mi>D</mi></mrow></mfrac></math> क्या है ?</p>",
                    options_en: ["<p>8 : 3</p>", "<p>4 : 1</p>", 
                                "<p>5 : 2</p>", "<p>3 : 1</p>"],
                    options_hi: ["<p>8 : 3</p>", "<p>4 : 1</p>",
                                "<p>5 : 2</p>", "<p>3 : 1</p>"],
                    solution_en: "<p>67.(b) <br>Apply mass point theorem in the following diagram:<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744276378539.png\" alt=\"rId41\" width=\"160\" height=\"167\"><br>AG : GD = 4 : 1</p>",
                    solution_hi: "<p>67.(b) निम्नलिखित चित्र में mass point प्रमेय लागू करें:<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744276378539.png\" alt=\"rId41\" width=\"160\" height=\"167\"><br>AG : GD = 4 : 1</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "68",
                    section: "17",
                    question_en: "<p>68. Sides AB and DC of a cyclic quadrilateral ABCD are produced to meet at E, and sides AD and BC are produced to meet at F. If &ang;ADC = 75&deg; and &ang;BEC = 52&deg;, then the difference between &ang;BAD and &ang;AFB ?</p>",
                    question_hi: "<p>68. एक चक्रीय चतुर्भुज ABCD की भुजाओं AB और DC को बढ़ाया जाता है जो E पर मिलती हैं तथा भुजाओं AD और BC को बढ़ाया जाता है जो F पर मिलती हैं । यदि &ang;ADC = 75&deg; तथा &ang;BEC = 52&deg; है, तो &ang;BAD तथा &ang;AFB के बीच क्या अंतर है ?</p>",
                    options_en: ["<p>23&deg;</p>", "<p>22&deg;</p>", 
                                "<p>31&deg;</p>", "<p>21&deg;</p>"],
                    options_hi: ["<p>23&deg;</p>", "<p>22&deg;</p>",
                                "<p>31&deg;</p>", "<p>21&deg;</p>"],
                    solution_en: "<p>68.(c) &ang;ADC = 75&deg;, &ang;BEC = 52&deg;<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744276378748.png\" alt=\"rId42\" width=\"199\" height=\"194\"><br>&ang;EBC = 75&deg;(in cyclic quadrilateral, exterior angle is equal to interior opposite angle)<br>&ang;BCE = &ang;BAD = &ang;FCD = 53&deg; (sum of angles of triangle is 180&deg;)<br>&ang;FDC = 105&deg;<br>Thus, &ang;DFC = 22&deg;<br>&ang;BAD - &ang;AFB = (53 - 22)&deg; = 31&deg;</p>",
                    solution_hi: "<p>68.(c) &ang;ADC = 75&deg;, &ang;BEC = 52&deg;<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744276378748.png\" alt=\"rId42\" width=\"199\" height=\"194\"><br>&ang;EBC = 75&deg;(चक्रीय चतुर्भुज में, बाहरी कोण आंतरिक विपरीत कोण के बराबर होता है।)<br>&ang;BCE = &ang;BAD = &ang;FCD = 53&deg; (त्रिभुज के कोणों का योग 180&deg; होता है।)<br>&ang;FDC = 105&deg;<br>इस प्रकार, &ang;DFC = 22&deg;<br>&ang;BAD - &ang;AFB = (53 - 22)&deg; = 31&deg;</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "69",
                    section: "17",
                    question_en: "<p>69. The given table represents the exports (in ₹crores) of four items A,B, C and D over a period of six years. Study the table carefully and answer the questions that follows:<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744276378896.png\" alt=\"rId43\" width=\"347\" height=\"182\"> <br>What is the ratio of the total exports of item A in 2014 and 2015 to the total exports of item C in 2011 and 2015 ?</p>",
                    question_hi: "<p>69. दी गयी तालिका छः वर्षों की अवधि के दौरान चार वस्तुओं A, B, C और D के निर्यात (करोड़ रुपये में) को दर्शाती है | इस तालिका का अध्ययन करें तथा फिर पूछे गए प्रश्नों के उत्तर दें |&nbsp;<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744276379058.png\" alt=\"rId44\" width=\"338\" height=\"184\"> <br>2014 और 2015 में वस्तु A के कुल निर्यात का 2011 और 2015 में वस्तु C के कुल निर्यात से अनुपात कितना है ?</p>",
                    options_en: ["<p>4 : 3</p>", "<p>7 : 5</p>", 
                                "<p>5 : 4</p>", "<p>3 : 2</p>"],
                    options_hi: ["<p>4 : 3</p>", "<p>7 : 5</p>",
                                "<p>5 : 4</p>", "<p>3 : 2</p>"],
                    solution_en: "<p>69.(d) Total exports of item A in 2014 and 2015 = 425 + 400 = 825<br>Total exports of item C in 2011 and 2015 = 244 + 306 = 550<br>Required ratio = 825 : 550 = 3 : 2</p>",
                    solution_hi: "<p>69.(d) 2014 और 2015 में आइटम A का कुल निर्यात = 425 + 400 = 825<br>2011 और 2015 में आइटम C का कुल निर्यात = 244 + 306 = 550<br>आवश्यक अनुपात = 825 : 550 = 3 : 2</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "70",
                    section: "17",
                    question_en: "<p>70. The given table represents the exports (in ₹crores) of four items A,B, C and D over a period of six years. Study the table carefully and answer the questions that follows:<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744276379232.png\" alt=\"rId45\" width=\"352\" height=\"188\"> <br>The total exports of item D in 2010, 2012 and 2014 is what percentage of the total exports of all the four items in 2011 and 2012 ?</p>",
                    question_hi: "<p>70. दी गयी तालिका छः वर्षों की अवधि के दौरान चार वस्तुओं A, B, C और D के निर्यात (करोड़ रुपये में) को दर्शाती है | इस तालिका का अध्ययन करें तथा फिर पूछे गए प्रश्नों के उत्तर दें&nbsp;<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744276379401.png\" alt=\"rId46\" width=\"351\" height=\"205\"> <br>2010, 2012 तथा 2014 में वस्तु D का कुल निर्यात 2011 तथा 2012 में सभी चार वस्तुओं के कुल निर्यात का कितना प्रतिशत है ?</p>",
                    options_en: ["<p>44.8%</p>", "<p>45%</p>", 
                                "<p>46.2%</p>", "<p>44%</p>"],
                    options_hi: ["<p>44.8%</p>", "<p>45%</p>",
                                "<p>46.2%</p>", "<p>44%</p>"],
                    solution_en: "<p>70.(d) Total export of item D in 2010, 2012 and 2014 = 214 + 247 + 309 = 770<br>Total export of all four item in 2011 and 2012 = (250 + 134 + 244 + 282) + (225 + 138 + 230 + 247) = 1750<br>Required % = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>770</mn><mn>1750</mn></mfrac></math> &times; 100 = 44%</p>",
                    solution_hi: "<p>70.(d) 2010, 2012 और 2014 में आइटम D का कुल निर्यात = 214 + 247 + 309 = 770<br>2011 और 2012 में चारों वस्तुओं का कुल निर्यात = (250 + 134 + 244 + 282) + (225 + 138 + 230 + 247) = 1750<br>आवश्यक % =<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>770</mn><mn>1750</mn></mfrac></math> &times; 100 = 44%</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "71",
                    section: "17",
                    question_en: "<p>71. The value of&nbsp; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mrow><mi>s</mi><mi>e</mi><mi>c</mi></mrow><mn>6</mn></msup><mi>&#952;</mi><mo>&#160;</mo><mo>-</mo><msup><mrow><mo>&#160;</mo><mi>t</mi><mi>a</mi><mi>n</mi></mrow><mn>6</mn></msup><mi>&#952;</mi><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mn>3</mn><msup><mrow><mi>s</mi><mi>e</mi><mi>c</mi></mrow><mn>2</mn></msup><mi>&#952;</mi><msup><mrow><mi>t</mi><mi>a</mi><mi>n</mi></mrow><mn>2</mn></msup><mi>&#952;</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>1</mn></mrow><mrow><msup><mrow><mi>c</mi><mi>o</mi><mi>s</mi></mrow><mn>4</mn></msup><mi>&#952;</mi><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><msup><mrow><mi>s</mi><mi>i</mi><mi>n</mi></mrow><mn>4</mn></msup><mi>&#952;</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>2</mn><msup><mrow><mi>s</mi><mi>i</mi><mi>n</mi></mrow><mn>2</mn></msup><mi>&#952;</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>2</mn></mrow></mfrac></math> is:</p>",
                    question_hi: "<p>71. <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mrow><mi>s</mi><mi>e</mi><mi>c</mi></mrow><mn>6</mn></msup><mi>&#952;</mi><mo>&#160;</mo><mo>-</mo><msup><mrow><mo>&#160;</mo><mi>t</mi><mi>a</mi><mi>n</mi></mrow><mn>6</mn></msup><mi>&#952;</mi><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mn>3</mn><msup><mrow><mi>s</mi><mi>e</mi><mi>c</mi></mrow><mn>2</mn></msup><mi>&#952;</mi><msup><mrow><mi>t</mi><mi>a</mi><mi>n</mi></mrow><mn>2</mn></msup><mi>&#952;</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>1</mn></mrow><mrow><msup><mrow><mi>c</mi><mi>o</mi><mi>s</mi></mrow><mn>4</mn></msup><mi>&#952;</mi><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><msup><mrow><mi>s</mi><mi>i</mi><mi>n</mi></mrow><mn>4</mn></msup><mi>&#952;</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>2</mn><msup><mrow><mi>s</mi><mi>i</mi><mi>n</mi></mrow><mn>2</mn></msup><mi>&#952;</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>2</mn></mrow></mfrac></math>&nbsp;का मान क्या है ?</p>",
                    options_en: ["<p><math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>", "<p>1</p>", 
                                "<p><math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>"],
                    options_hi: ["<p><math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>", "<p>1</p>",
                                "<p><math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>"],
                    solution_en: "<p>71.(a) <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mrow><mi>s</mi><mi>e</mi><mi>c</mi></mrow><mn>6</mn></msup><mi>&#952;</mi><mo>&#160;</mo><mo>-</mo><msup><mrow><mo>&#160;</mo><mi>t</mi><mi>a</mi><mi>n</mi></mrow><mn>6</mn></msup><mi>&#952;</mi><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mn>3</mn><msup><mrow><mi>s</mi><mi>e</mi><mi>c</mi></mrow><mn>2</mn></msup><mi>&#952;</mi><msup><mrow><mi>t</mi><mi>a</mi><mi>n</mi></mrow><mn>2</mn></msup><mi>&#952;</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>1</mn></mrow><mrow><msup><mrow><mi>c</mi><mi>o</mi><mi>s</mi></mrow><mn>4</mn></msup><mi>&#952;</mi><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><msup><mrow><mi>s</mi><mi>i</mi><mi>n</mi></mrow><mn>4</mn></msup><mi>&#952;</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>2</mn><msup><mrow><mi>s</mi><mi>i</mi><mi>n</mi></mrow><mn>2</mn></msup><mi>&#952;</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>2</mn></mrow></mfrac></math><br>&rArr; a<sup>3</sup> - b<sup>3</sup> = (a - b)(a<sup>2</sup> + b<sup>2</sup> + ab)<br>&rArr; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mrow><mi>s</mi><mi>e</mi><mi>c</mi></mrow><mn>6</mn></msup><mi>&#952;</mi><mo>&#160;</mo><mo>-</mo><msup><mrow><mo>&#160;</mo><mi>t</mi><mi>a</mi><mi>n</mi></mrow><mn>6</mn></msup><mi>&#952;</mi><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mn>3</mn><msup><mrow><mi>s</mi><mi>e</mi><mi>c</mi></mrow><mn>2</mn></msup><mi>&#952;</mi><msup><mrow><mi>t</mi><mi>a</mi><mi>n</mi></mrow><mn>2</mn></msup><mi>&#952;</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>1</mn></mrow><mrow><msup><mrow><mi>c</mi><mi>o</mi><mi>s</mi></mrow><mn>4</mn></msup><mi>&#952;</mi><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><msup><mrow><mi>s</mi><mi>i</mi><mi>n</mi></mrow><mn>4</mn></msup><mi>&#952;</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>2</mn><msup><mrow><mi>s</mi><mi>i</mi><mi>n</mi></mrow><mn>2</mn></msup><mi>&#952;</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>2</mn></mrow></mfrac></math><br>&rArr; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><msup><mrow><mi>s</mi><mi>e</mi><mi>c</mi></mrow><mn>2</mn></msup><mi>&#952;</mi><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><msup><mrow><mi>t</mi><mi>a</mi><mi>n</mi></mrow><mn>2</mn></msup><mi>&#952;</mi><mo>)</mo><mo>(</mo><msup><mrow><mi>s</mi><mi>e</mi><mi>c</mi></mrow><mn>4</mn></msup><mi>&#952;</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mrow><mi>t</mi><mi>a</mi><mi>n</mi></mrow><mn>4</mn></msup><mi>&#952;</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>3</mn><msup><mrow><mi>s</mi><mi>e</mi><mi>c</mi></mrow><mn>2</mn></msup><mi>&#952;</mi><msup><mrow><mi>t</mi><mi>a</mi><mi>n</mi></mrow><mn>2</mn></msup><mi>&#952;</mi><mo>)</mo></mrow><mrow><mo>(</mo><msup><mrow><mi>c</mi><mi>o</mi><mi>s</mi></mrow><mn>2</mn></msup><mi>&#952;</mi><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><msup><mrow><mi>s</mi><mi>i</mi><mi>n</mi></mrow><mn>2</mn></msup><mi>&#952;</mi><mo>)</mo><mo>(</mo><msup><mrow><mi>c</mi><mi>o</mi><mi>s</mi></mrow><mn>2</mn></msup><mi>&#952;</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mrow><mi>s</mi><mi>i</mi><mi>n</mi></mrow><mn>2</mn></msup><mi>&#952;</mi><mo>)</mo><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>2</mn><msup><mrow><mi>s</mi><mi>i</mi><mi>n</mi></mrow><mn>2</mn></msup><mi>&#952;</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>2</mn></mrow></mfrac></math> - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>3</mn><mi>s</mi><mi>e</mi><msup><mi>c</mi><mn>2</mn></msup><mi>&#952;</mi><msup><mi>tan</mi><mn>2</mn></msup><mi>&#952;</mi><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mn>1</mn><mo>)</mo></mrow><mrow><mo>(</mo><msup><mi>cos</mi><mn>2</mn></msup><mi>&#952;</mi><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><msup><mi>sin</mi><mn>2</mn></msup><mi>&#952;</mi><mo>)</mo><mo>(</mo><msup><mi>cos</mi><mn>2</mn></msup><mi>&#952;</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mi>sin</mi><mn>2</mn></msup><mi>&#952;</mi><mo>)</mo><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>2</mn><msup><mi>sin</mi><mn>2</mn></msup><mi>&#952;</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>2</mn></mrow></mfrac></math><br>&rArr; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><msup><mrow><mi>s</mi><mi>e</mi><mi>c</mi></mrow><mn>4</mn></msup><mi>&#952;</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mrow><mi>t</mi><mi>a</mi><mi>n</mi></mrow><mn>4</mn></msup><mi>&#952;</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>3</mn><msup><mrow><mi>s</mi><mi>e</mi><mi>c</mi></mrow><mn>2</mn></msup><mi>&#952;</mi><msup><mrow><mi>t</mi><mi>a</mi><mi>n</mi></mrow><mn>2</mn></msup><mi>&#952;</mi><mo>)</mo><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mn>3</mn><msup><mrow><mi>s</mi><mi>e</mi><mi>c</mi></mrow><mn>2</mn></msup><mi>&#952;</mi><msup><mrow><mi>t</mi><mi>a</mi><mi>n</mi></mrow><mn>2</mn></msup><mi>&#952;</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>1</mn></mrow><mrow><mo>(</mo><msup><mrow><mi>c</mi><mi>o</mi><mi>s</mi></mrow><mn>2</mn></msup><mi>&#952;</mi><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><msup><mrow><mi>s</mi><mi>i</mi><mi>n</mi></mrow><mn>2</mn></msup><mi>&#952;</mi><mo>)</mo><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>2</mn><msup><mrow><mi>s</mi><mi>i</mi><mi>n</mi></mrow><mn>2</mn></msup><mi>&#952;</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>2</mn></mrow></mfrac></math><br>&rArr; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mrow><mi>s</mi><mi>e</mi><mi>c</mi></mrow><mn>4</mn></msup><mi>&#952;</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mrow><mi>t</mi><mi>a</mi><mi>n</mi></mrow><mn>4</mn></msup><mi>&#952;</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>1</mn></mrow><mrow><msup><mrow><mi>c</mi><mi>o</mi><mi>s</mi></mrow><mn>2</mn></msup><mi>&#952;</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mrow><mi>s</mi><mi>i</mi><mi>n</mi></mrow><mn>2</mn></msup><mi>&#952;</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>2</mn></mrow></mfrac></math> <br>&rArr; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mrow><mi>s</mi><mi>e</mi><mi>c</mi></mrow><mn>4</mn></msup><mi>&#952;</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mrow><mi>t</mi><mi>a</mi><mi>n</mi></mrow><mn>4</mn></msup><mi>&#952;</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>1</mn></mrow><mn>3</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>3</mn></mfrac></math></p>",
                    solution_hi: "<p>71.(a) <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mrow><mi>s</mi><mi>e</mi><mi>c</mi></mrow><mn>6</mn></msup><mi>&#952;</mi><mo>&#160;</mo><mo>-</mo><msup><mrow><mo>&#160;</mo><mi>t</mi><mi>a</mi><mi>n</mi></mrow><mn>6</mn></msup><mi>&#952;</mi><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mn>3</mn><msup><mrow><mi>s</mi><mi>e</mi><mi>c</mi></mrow><mn>2</mn></msup><mi>&#952;</mi><msup><mrow><mi>t</mi><mi>a</mi><mi>n</mi></mrow><mn>2</mn></msup><mi>&#952;</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>1</mn></mrow><mrow><msup><mrow><mi>c</mi><mi>o</mi><mi>s</mi></mrow><mn>4</mn></msup><mi>&#952;</mi><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><msup><mrow><mi>s</mi><mi>i</mi><mi>n</mi></mrow><mn>4</mn></msup><mi>&#952;</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>2</mn><msup><mrow><mi>s</mi><mi>i</mi><mi>n</mi></mrow><mn>2</mn></msup><mi>&#952;</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>2</mn></mrow></mfrac></math><br>&rArr; a<sup>3</sup> - b<sup>3</sup> = (a - b)(a<sup>2</sup> + b<sup>2</sup> + ab)<br>&rArr; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mrow><mi>s</mi><mi>e</mi><mi>c</mi></mrow><mn>6</mn></msup><mi>&#952;</mi><mo>&#160;</mo><mo>-</mo><msup><mrow><mo>&#160;</mo><mi>t</mi><mi>a</mi><mi>n</mi></mrow><mn>6</mn></msup><mi>&#952;</mi><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mn>3</mn><msup><mrow><mi>s</mi><mi>e</mi><mi>c</mi></mrow><mn>2</mn></msup><mi>&#952;</mi><msup><mrow><mi>t</mi><mi>a</mi><mi>n</mi></mrow><mn>2</mn></msup><mi>&#952;</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>1</mn></mrow><mrow><msup><mrow><mi>c</mi><mi>o</mi><mi>s</mi></mrow><mn>4</mn></msup><mi>&#952;</mi><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><msup><mrow><mi>s</mi><mi>i</mi><mi>n</mi></mrow><mn>4</mn></msup><mi>&#952;</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>2</mn><msup><mrow><mi>s</mi><mi>i</mi><mi>n</mi></mrow><mn>2</mn></msup><mi>&#952;</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>2</mn></mrow></mfrac></math><br>&rArr; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><msup><mrow><mi>s</mi><mi>e</mi><mi>c</mi></mrow><mn>2</mn></msup><mi>&#952;</mi><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><msup><mrow><mi>t</mi><mi>a</mi><mi>n</mi></mrow><mn>2</mn></msup><mi>&#952;</mi><mo>)</mo><mo>(</mo><msup><mrow><mi>s</mi><mi>e</mi><mi>c</mi></mrow><mn>4</mn></msup><mi>&#952;</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mrow><mi>t</mi><mi>a</mi><mi>n</mi></mrow><mn>4</mn></msup><mi>&#952;</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>3</mn><msup><mrow><mi>s</mi><mi>e</mi><mi>c</mi></mrow><mn>2</mn></msup><mi>&#952;</mi><msup><mrow><mi>t</mi><mi>a</mi><mi>n</mi></mrow><mn>2</mn></msup><mi>&#952;</mi><mo>)</mo></mrow><mrow><mo>(</mo><msup><mrow><mi>c</mi><mi>o</mi><mi>s</mi></mrow><mn>2</mn></msup><mi>&#952;</mi><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><msup><mrow><mi>s</mi><mi>i</mi><mi>n</mi></mrow><mn>2</mn></msup><mi>&#952;</mi><mo>)</mo><mo>(</mo><msup><mrow><mi>c</mi><mi>o</mi><mi>s</mi></mrow><mn>2</mn></msup><mi>&#952;</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mrow><mi>s</mi><mi>i</mi><mi>n</mi></mrow><mn>2</mn></msup><mi>&#952;</mi><mo>)</mo><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>2</mn><msup><mrow><mi>s</mi><mi>i</mi><mi>n</mi></mrow><mn>2</mn></msup><mi>&#952;</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>2</mn></mrow></mfrac></math> - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>3</mn><mi>s</mi><mi>e</mi><msup><mi>c</mi><mn>2</mn></msup><mi>&#952;</mi><msup><mi>tan</mi><mn>2</mn></msup><mi>&#952;</mi><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mn>1</mn><mo>)</mo></mrow><mrow><mo>(</mo><msup><mi>cos</mi><mn>2</mn></msup><mi>&#952;</mi><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><msup><mi>sin</mi><mn>2</mn></msup><mi>&#952;</mi><mo>)</mo><mo>(</mo><msup><mi>cos</mi><mn>2</mn></msup><mi>&#952;</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mi>sin</mi><mn>2</mn></msup><mi>&#952;</mi><mo>)</mo><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>2</mn><msup><mi>sin</mi><mn>2</mn></msup><mi>&#952;</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>2</mn></mrow></mfrac></math><br>&rArr; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><msup><mrow><mi>s</mi><mi>e</mi><mi>c</mi></mrow><mn>4</mn></msup><mi>&#952;</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mrow><mi>t</mi><mi>a</mi><mi>n</mi></mrow><mn>4</mn></msup><mi>&#952;</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>3</mn><msup><mrow><mi>s</mi><mi>e</mi><mi>c</mi></mrow><mn>2</mn></msup><mi>&#952;</mi><msup><mrow><mi>t</mi><mi>a</mi><mi>n</mi></mrow><mn>2</mn></msup><mi>&#952;</mi><mo>)</mo><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mn>3</mn><msup><mrow><mi>s</mi><mi>e</mi><mi>c</mi></mrow><mn>2</mn></msup><mi>&#952;</mi><msup><mrow><mi>t</mi><mi>a</mi><mi>n</mi></mrow><mn>2</mn></msup><mi>&#952;</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>1</mn></mrow><mrow><mo>(</mo><msup><mrow><mi>c</mi><mi>o</mi><mi>s</mi></mrow><mn>2</mn></msup><mi>&#952;</mi><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><msup><mrow><mi>s</mi><mi>i</mi><mi>n</mi></mrow><mn>2</mn></msup><mi>&#952;</mi><mo>)</mo><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>2</mn><msup><mrow><mi>s</mi><mi>i</mi><mi>n</mi></mrow><mn>2</mn></msup><mi>&#952;</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>2</mn></mrow></mfrac></math><br>&rArr; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mrow><mi>s</mi><mi>e</mi><mi>c</mi></mrow><mn>4</mn></msup><mi>&#952;</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mrow><mi>t</mi><mi>a</mi><mi>n</mi></mrow><mn>4</mn></msup><mi>&#952;</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>1</mn></mrow><mrow><msup><mrow><mi>c</mi><mi>o</mi><mi>s</mi></mrow><mn>2</mn></msup><mi>&#952;</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mrow><mi>s</mi><mi>i</mi><mi>n</mi></mrow><mn>2</mn></msup><mi>&#952;</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>2</mn></mrow></mfrac></math> <br>&rArr; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mrow><mi>s</mi><mi>e</mi><mi>c</mi></mrow><mn>4</mn></msup><mi>&#952;</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mrow><mi>t</mi><mi>a</mi><mi>n</mi></mrow><mn>4</mn></msup><mi>&#952;</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>1</mn></mrow><mn>3</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>3</mn></mfrac></math></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "72",
                    section: "17",
                    question_en: "<p>72. Two chords AB and CD of a circle with centre O intersect each other at P. If &ang;APC = 95&deg; and &ang;AOD = 110&deg;, then what &ang;BOC is:</p>",
                    question_hi: "<p>72. केंद्र O वाले एक वृत्त की दो जीवाएँ AB तथा CD हैं जो एक-दूसरे को P पर प्रतिच्छेद करती हैं । यदि &ang;APC = 95&deg; तथा &ang;AOD = 110&deg; है, तो &ang;BOC का मान क्या होगा ?</p>",
                    options_en: ["<p>65&deg;</p>", "<p>70&deg;</p>", 
                                "<p>60&deg;</p>", "<p>55&deg;</p>"],
                    options_hi: ["<p>65&deg;</p>", "<p>70&deg;</p>",
                                "<p>60&deg;</p>", "<p>55&deg;</p>"],
                    solution_en: "<p>72.(c) <br>&ang;APC = 95&deg;, &ang;AOD = 110&deg;. Join BD<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744276379554.png\" alt=\"rId47\" width=\"150\" height=\"155\"><br>&ang;ABD = 55&deg; (angle formed at the centre is double the angle formed at the arc of circle).<br>&ang;BPD = 95&deg;<br>&ang;BDC = 180&deg; - (95 + 55)&deg; = 180&deg; - (150)&deg; = 30&deg;<br>&ang;BOC = 60&deg; (angle formed at the centre is double the angle formed at the arc of circle).</p>",
                    solution_hi: "<p>72.(c) <br>&ang;APC = 95&deg;, &ang;AOD = 110&deg;. BD को मिलाये.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744276379554.png\" alt=\"rId47\" width=\"150\" height=\"155\"><br>&ang;ABD = 55&deg; (केंद्र पर बना कोण वृत्त के चाप पर बने कोण का दोगुना है).<br>&ang;BPD = 95&deg;<br>&ang;BDC = 180&deg; - (95 + 55)&deg; = 180&deg; - (150)&deg; = 30&deg;<br>&ang;BOC = 60&deg; (केंद्र पर बना कोण वृत्त के चाप पर बने कोण का दोगुना है)।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "73",
                    section: "17",
                    question_en: "<p>73. If 16a<sup>4</sup> +3 6a<sup>2</sup>b<sup>2</sup> + 81b<sup>4</sup> = 91 and 4a<sup>2</sup> + 9b<sup>2</sup> - 6ab = 13, then what is the value of 3ab ?</p>",
                    question_hi: "<p>73. यदि 16a<sup>4</sup> +3 6a<sup>2</sup>b<sup>2</sup> + 81b<sup>4</sup> = 91 तथा 4a<sup>2</sup> + 9b<sup>2</sup> - 6ab = 13 है, तो 3ab का मान क्या होगा ?</p>",
                    options_en: ["<p>-3</p>", "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>2</mn></mfrac></math></p>", 
                                "<p>5</p>", "<p>- <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>"],
                    options_hi: ["<p>- 3</p>", "<p><math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>",
                                "<p>5</p>", "<p>- <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>"],
                    solution_en: "<p>73.(d) 16a<sup>4</sup> +3 6a<sup>2</sup>b<sup>2</sup> + 81b<sup>4</sup> = 91<br>(4a<sup>2 </sup>+ 9b<sup>2 </sup>- 6ab)(4a<sup>2 </sup>+ 9b<sup>2 </sup>+ 6ab) = 91<br>(13)(4a<sup>2 </sup>+ 9b<sup>2 </sup>+ 6ab) = 91<br>4a<sup>2 </sup>+ 9b<sup>2 </sup>+ 6ab = 7 &hellip;(i)<br>4a<sup>2 </sup>+ 9b<sup>2 </sup>- 6ab = 13 (ii)<br>(ii) - (i) &rArr; -12ab = 6 &rArr; ab = - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math><br>&rArr; 3ab = - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>2</mn></mfrac></math></p>",
                    solution_hi: "<p>73.(d) 16a<sup>4</sup> +3 6a<sup>2</sup>b<sup>2</sup> + 81b<sup>4</sup> = 91<br>(4a<sup>2 </sup>+ 9b<sup>2 </sup>- 6ab)(4a<sup>2 </sup>+ 9b<sup>2 </sup>+ 6ab) = 91<br>(13)(4a<sup>2 </sup>+ 9b<sup>2 </sup>+ 6ab) = 91<br>4a<sup>2 </sup>+ 9b<sup>2 </sup>+ 6ab = 7 &hellip;(i)<br>4a<sup>2 </sup>+ 9b<sup>2 </sup>- 6ab = 13 (ii)<br>(ii) - (i) &rArr; -12ab = 6 &rArr; ab = - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math><br>&rArr; 3ab = - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>2</mn></mfrac></math></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "74",
                    section: "17",
                    question_en: "<p>74. If x<sup>2</sup> - 2<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>5</mn></msqrt></math>x + 1 = 0, then what is the value of x<sup>5</sup> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msup><mi>x</mi><mn>5</mn></msup></mfrac></math> ?</p>",
                    question_hi: "<p>74. यदि x<sup>2</sup> - 2<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>5</mn></msqrt></math>x + 1 = 0, है, तो x<sup>5</sup> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msup><mi>x</mi><mn>5</mn></msup></mfrac></math>&nbsp;का मान क्या होगा ?</p>",
                    options_en: ["<p>610<math display=\"inline\"><msqrt><mn>5</mn></msqrt></math></p>", "<p>408<math display=\"inline\"><msqrt><mn>5</mn></msqrt></math></p>", 
                                "<p>612<math display=\"inline\"><msqrt><mn>5</mn></msqrt></math></p>", "<p>406<math display=\"inline\"><msqrt><mn>5</mn></msqrt></math></p>"],
                    options_hi: ["<p>610<math display=\"inline\"><msqrt><mn>5</mn></msqrt></math></p>", "<p>408<math display=\"inline\"><msqrt><mn>5</mn></msqrt></math></p>",
                                "<p>612<math display=\"inline\"><msqrt><mn>5</mn></msqrt></math></p>", "<p>406<math display=\"inline\"><msqrt><mn>5</mn></msqrt></math></p>"],
                    solution_en: "<p>74.(a)&nbsp;x<sup>2</sup> - 2<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>5</mn></msqrt></math>x + 1 = 0<br>&rArr; x + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>x</mi></mfrac></math> = 2<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>5</mn></msqrt></math><br>&rArr; x<sup>5</sup> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msup><mi>x</mi><mn>5</mn></msup></mfrac></math> = (x<sup>2 </sup>+ <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msup><mi>x</mi><mn>2</mn></msup></mfrac></math>)(x<sup>3</sup> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msup><mi>x</mi><mn>3</mn></msup></mfrac></math>) - (x + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>x</mi></mfrac></math>)<br>x2 +<math display=\"inline\"><mi>&#160;</mi><mfrac><mrow><mn>1</mn></mrow><mrow><msup><mrow><mi>x</mi></mrow><mrow><mn>2</mn></mrow></msup></mrow></mfrac></math> = 18 &rArr; x<sup>3</sup> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msup><mi>x</mi><mn>3</mn></msup></mfrac></math> = 34<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>5</mn></msqrt></math><br>&rArr; x<sup>5</sup> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msup><mi>x</mi><mn>5</mn></msup></mfrac></math> = 610<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>5</mn></msqrt></math></p>",
                    solution_hi: "<p>74.(a)&nbsp;x<sup>2</sup> - 2<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>5</mn></msqrt></math>x + 1 = 0<br>&rArr; x + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>x</mi></mfrac></math> = 2<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>5</mn></msqrt></math><br>&rArr; x<sup>5</sup> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msup><mi>x</mi><mn>5</mn></msup></mfrac></math> = (x<sup>2 </sup>+ <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msup><mi>x</mi><mn>2</mn></msup></mfrac></math>)(x<sup>3</sup> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msup><mi>x</mi><mn>3</mn></msup></mfrac></math>) - (x + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>x</mi></mfrac></math>)<br>x2 +<math display=\"inline\"><mi>&#160;</mi><mfrac><mrow><mn>1</mn></mrow><mrow><msup><mrow><mi>x</mi></mrow><mrow><mn>2</mn></mrow></msup></mrow></mfrac></math> = 18 &rArr; x<sup>3</sup> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msup><mi>x</mi><mn>3</mn></msup></mfrac></math> = 34<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>5</mn></msqrt></math><br>&rArr; x<sup>5</sup> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msup><mi>x</mi><mn>5</mn></msup></mfrac></math> = 610<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>5</mn></msqrt></math></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "75",
                    section: "7",
                    question_en: "<p>75. The given table represents the exports (in ₹crores) of four items A,B, C and D over a period of six years. Study the table carefully and answer the questions that follows:<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744276379678.png\" alt=\"rId48\" width=\"308\" height=\"169\"> <br>The total exports of item A from 2012 to 2014 is what percentage less than the total exports of all the four items in 2015 ? (correct to one decimal place)</p>",
                    question_hi: "<p>75. दी गयी तालिका छः वर्षों की अवधि के दौरान चार वस्तुओं A, B, C और D के निर्यात (करोड़ रुपये में) को दर्शाती है | इस तालिका का अध्ययन करें तथा फिर पूछे गए प्रश्नों के उत्तर दें&nbsp;<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744276379785.png\" alt=\"rId49\" width=\"327\" height=\"211\"> <br>2012 से 2014 तक वस्तु A का कुल निर्यात 2015 में सभी वस्तुओं के कुल निर्यात से कितना प्रतिशत (दशमलव के एक स्थान तक) कम है ?</p>",
                    options_en: ["<p>15.2%</p>", "<p>13.8%</p>", 
                                "<p>16.7%</p>", "<p>14.3%</p>"],
                    options_hi: ["<p>15.2%</p>", "<p>13.8%</p>",
                                "<p>16.7%</p>", "<p>14.3%</p>"],
                    solution_en: "<p>75.(d) <br>Total export of item A from 2012 to 2014 = 225 + 370 + 425 = 1020<br>Total export in 2015 = 400 + 209 + 306 + 275 = 1190<br>Required % = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1190</mn><mo>-</mo><mn>1020</mn></mrow><mn>1190</mn></mfrac></math> &times; 100 = 14.28%</p>",
                    solution_hi: "<p>75.(d) 2012 से 2014 तक आइटम A का कुल निर्यात = 225 + 370 + 425 = 1020<br>2015 में कुल निर्यात = 400 + 209 + 306 + 275 = 1190<br>आवश्यक % = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1190</mn><mo>-</mo><mn>1020</mn></mrow><mn>1190</mn></mfrac></math> &times; 100 = 14.28%</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "76",
                    section: "7",
                    question_en: "<p>76. Identify the segment in the sentence which contains the grammatical error from the given options.<br>Modern man is completely engross in the mad pursuit of material pleasures and luxuries.</p>",
                    question_hi: "<p>76. Identify the segment in the sentence which contains the grammatical error from the given options.<br>Modern man is completely engross in the mad pursuit of material pleasures and luxuries.</p>",
                    options_en: ["<p>completely engross</p>", "<p>Modern man is</p>", 
                                "<p>material pleasures and luxuries</p>", "<p>mad pursuit of</p>"],
                    options_hi: ["<p>completely engross</p>", "<p>Modern man is</p>",
                                "<p>material pleasures and luxuries</p>", "<p>mad pursuit of</p>"],
                    solution_en: "<p>76.(a) completely engross.<br>&lsquo;completely engross&rsquo; should be replaced with &lsquo;completely engrossed&rsquo;. In the above sentence the helping verb &lsquo;is&rsquo; is followed by main verb &lsquo;engross&rsquo; but here the third form of the main verb should be used &lsquo;engrossed&rsquo;. The given sentence states that &lsquo;the modern Man is still engrossed in the mad pursuit of material pleasures and luxuries&rsquo;.</p>",
                    solution_hi: "<p>76.(a) completely engross.<br>&lsquo;Completely engross&rsquo; के स्थान पर &lsquo;completely engrossed&rsquo; का प्रयोग किया जाएगा। उपरोक्त sentence में helping verb &lsquo;is&rsquo; के बाद main verb &lsquo;engross&rsquo; आता है, लेकिन यहाँ main verb की third form &lsquo;engrossed&rsquo; का प्रयोग किया जाना चाहिए। दिए गए sentence कहा गया है कि आधुनिक मनुष्य अभी भी भौतिक सुखों (material pleasures) और विलासिता (luxuries) की पागल खोज में लिप्त है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "77",
                    section: "7",
                    question_en: "<p>77. Select the correct passive form of the given sentence.<br>They offered me a chair</p>",
                    question_hi: "<p>77. Select the correct passive form of the given sentence.<br>They offered me a chair</p>",
                    options_en: ["<p>I offered a chair to them</p>", "<p>A chair is offered to me by them.</p>", 
                                "<p>A chair was being offered to me.</p>", "<p>I was offered a chair by them.</p>"],
                    options_hi: ["<p>I offered a chair to them</p>", "<p>A chair is offered to me by them.</p>",
                                "<p>A chair was being offered to me.</p>", "<p>I was offered a chair by them.</p>"],
                    solution_en: "<p>77.(d) I was offered a chair by them.(Correct) <br>They offered me a chair<br>(a) I offered a chair to them.(Meaning has been changed) <br>(b) A chair <span style=\"text-decoration: underline;\">is offered</span> to me by them.(Incorrect Tense)<br>(c) A chair <span style=\"text-decoration: underline;\">was being offered</span> to me. (Incorrect Tense)</p>",
                    solution_hi: "<p>77.(d) I was offered a chair by them.(Correct) <br>They offered me a chair<br>(a) I offered a chair to them.(Meaning change है) <br>(b) A chair <span style=\"text-decoration: underline;\">is offered</span> to me by them.(गलत Tense)<br>(c) A chair <span style=\"text-decoration: underline;\">was being offered</span> to me. (गलत Tense)</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "78",
                    section: "7",
                    question_en: "<p>78. Select the most appropriate meaning of the given idiom.<br>A close-fisted person</p>",
                    question_hi: "<p>78. Select the most appropriate meaning of the given idiom.<br>A close-fisted person</p>",
                    options_en: ["<p>A strong person</p>", "<p>A cruel person</p>", 
                                "<p>A kind person</p>", "<p>A miserly person</p>"],
                    options_hi: ["<p>A strong person</p>", "<p>A cruel person</p>",
                                "<p>A kind person</p>", "<p>A miserly person</p>"],
                    solution_en: "<p>78.(d) A close-fisted person - A miserly person.</p>",
                    solution_hi: "<p>78.(d) A close-fisted person - A miserly person./एक कंजूस व्यक्ति।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "79",
                    section: "7",
                    question_en: "<p>79. Select the most appropriate meaning of the given idiom.<br>A bed of roses</p>",
                    question_hi: "<p>79. Select the most appropriate meaning of the given idiom.<br>A bed of roses</p>",
                    options_en: ["<p>A valley full of flowers</p>", "<p>An easy and happy situation</p>", 
                                "<p>A difficult path</p>", "<p>A pleasant perfume</p>"],
                    options_hi: ["<p>A valley full of flowers</p>", "<p>An easy and happy situation</p>",
                                "<p>A difficult path</p>", "<p>A pleasant perfume</p>"],
                    solution_en: "<p>79.(b) A bed of roses - An easy and happy situation.</p>",
                    solution_hi: "<p>79.(b) A bed of roses - An easy and happy situation./एक आसान और सुखद स्थिति।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "80",
                    section: "7",
                    question_en: "<p>80. Select the most appropriate word to fill in the blank.<br>I like both tea and coffee but prefer the _______.</p>",
                    question_hi: "<p>80. Select the most appropriate word to fill in the blank.<br>I like both tea and coffee but prefer the _______.</p>",
                    options_en: ["<p>later</p>", "<p>least</p>", 
                                "<p>last</p>", "<p>latter</p>"],
                    options_hi: ["<p>later</p>", "<p>least</p>",
                                "<p>last</p>", "<p>latter</p>"],
                    solution_en: "<p>80.(d) latter. <br>&lsquo;latter&rsquo; should be used here because between tea and coffee the subject likes coffee, and latter means &lsquo;the second one of two things or people that have been mentioned&rsquo;.</p>",
                    solution_hi: "<p>80.(d) latter. <br>यहां &lsquo;latter&rsquo; का प्रयोग होना चाहिए क्योंकि tea और coffee में से subject को coffee पसंद है, और latter का अर्थ है \'जिन दो वस्तुओं या लोगों का उल्लेख किया गया है उनमें से दूसरा।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "81",
                    section: "7",
                    question_en: "<p>81. Select the correct indirect form of the given sentence.<br>\"What a good idea!\", Seema remarked.</p>",
                    question_hi: "<p>81. Select the correct indirect form of the given sentence.<br>\"What a good idea!\", Seema remarked.</p>",
                    options_en: ["<p>Seema said what a good idea it is.</p>", "<p>Seema told what an idea!</p>", 
                                "<p>Seema exclaimed that it was a very good idea.</p>", "<p>Seema exclaimed that the idea is good.</p>"],
                    options_hi: ["<p>Seema said what a good idea it is.</p>", "<p>Seema told what an idea!</p>",
                                "<p>Seema exclaimed that it was a very good idea.</p>", "<p>Seema exclaimed that the idea is good.</p>"],
                    solution_en: "<p>81.(c) Seema exclaimed that it was a very good idea.(Correct)<br>(a) Seema <span style=\"text-decoration: underline;\">said what</span> a good idea it is.(Incorrect Reported Verb, Tense)<br>(b) Seema <span style=\"text-decoration: underline;\">told what</span> an idea! (Incorrect Reported Verb)<br>(d) Seema exclaimed that <span style=\"text-decoration: underline;\">the idea is good.</span> (Incorrect Tense)</p>",
                    solution_hi: "<p>81.(c) Seema exclaimed that it was a very good idea.(Correct)<br>(a) Seema <span style=\"text-decoration: underline;\">said what</span> a good idea it is.(गलत Reported Verb, Tense)<br>(b) Seema <span style=\"text-decoration: underline;\">told what </span>an idea! (गलत Reported Verb)<br>(d) Seema exclaimed that <span style=\"text-decoration: underline;\">the idea is good.</span> (गलत Tense)</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "82",
                    section: "7",
                    question_en: "<p>82.<strong> Cloze Test :</strong><br>A (82) ______of trucks carrying soldiers was coming down the mountain road. The trucks (83) _____as there had been heavy snowfall in that area. Suddenly, with a (84) ______ a huge tree on the hill side fell bringing along with it boulders and mud. (85) ______, the driver of first truck stopped in time. The soldiers got down and started (86) ______the road.<br>Select the most appropriate option to fill in the blank no.82</p>",
                    question_hi: "<p>82. <strong>Cloze Test :</strong><br>A (82) ______of trucks carrying soldiers was coming down the mountain road. The trucks (83) _____as there had been heavy snowfall in that area. Suddenly, with a (84) ______ a huge tree on the hill side fell bringing along with it boulders and mud. (85) ______, the driver of first truck stopped in time. The soldiers got down and started (86) ______the road.<br>Select the most appropriate option to fill in the blank no.82</p>",
                    options_en: ["<p>flock</p>", "<p>convoy</p>", 
                                "<p>bevy</p>", "<p>crew</p>"],
                    options_hi: ["<p>flock</p>", "<p>convoy</p>",
                                "<p>bevy</p>", "<p>crew</p>"],
                    solution_en: "<p>82.(b) convoy. <br>Convoy means &lsquo; a group of ships or vehicles travelling together, typically one accompanied by armed troops, warships, or other vehicles for protection&rsquo;.</p>",
                    solution_hi: "<p>82.(b) convoy. <br>Convoy का अर्थ है \'ships या vehicles का एक group जो एक साथ यात्रा करता है, आमतौर पर protection के लिए armed troops, warships या अन्य वाहन साथ होते हैं।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "83",
                    section: "7",
                    question_en: "<p>83. <strong>Cloze Test :</strong><br>A (82) ______of trucks carrying soldiers was coming down the mountain road. The trucks (83) _____as there had been heavy snowfall in that area. Suddenly, with a (84) ______ a huge tree on the hill side fell bringing along with it boulders and mud. (85) ______, the driver of first truck stopped in time. The soldiers got down and started (86) ______the road.<br>Select the most appropriate option to fill in the blank no. 83</p>",
                    question_hi: "<p>83. <strong>Cloze Test :</strong><br>A (82) ______of trucks carrying soldiers was coming down the mountain road. The trucks (83) _____as there had been heavy snowfall in that area. Suddenly, with a (84) ______ a huge tree on the hill side fell bringing along with it boulders and mud. (85) ______, the driver of first truck stopped in time. The soldiers got down and started (86) ______the road.<br>Select the most appropriate option to fill in the blank no. 83</p>",
                    options_en: ["<p>was moving</p>", "<p>are moving</p>", 
                                "<p>has moved</p>", "<p>were moving</p>"],
                    options_hi: ["<p>was moving</p>", "<p>are moving</p>",
                                "<p>has moved</p>", "<p>were moving</p>"],
                    solution_en: "<p>83.(d) were moving. <br>Since the subject &lsquo;The trucks&rsquo; are plural therefore &lsquo;were&rsquo; is to be used and as the action has taken place in past tense therefore Past continuous tense has to be used.</p>",
                    solution_hi: "<p>83.(d) were moving. <br>चूँकि subject &lsquo;The trucks&rsquo; plural है इसलिए &lsquo;were&rsquo; का प्रयोग किया जाएगा तथा जैसा कि action, past tense में हुआ है इसलिए Past continuous tense का प्रयोग किया जाएगा।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "84",
                    section: "7",
                    question_en: "<p>84. <strong>Cloze Test :</strong><br>A (82) ______of trucks carrying soldiers was coming down the mountain road. The trucks (83) _____as there had been heavy snowfall in that area. Suddenly, with a (84) ______ a huge tree on the hill side fell bringing along with it boulders and mud. (85) ______, the driver of first truck stopped in time. The soldiers got down and started (86) ______the road.<br>Select the most appropriate option to fill in the blank no. 84.</p>",
                    question_hi: "<p>84. <strong>Cloze Test :</strong><br>A (82) ______of trucks carrying soldiers was coming down the mountain road. The trucks (83) _____as there had been heavy snowfall in that area. Suddenly, with a (84) ______ a huge tree on the hill side fell bringing along with it boulders and mud. (85) ______, the driver of first truck stopped in time. The soldiers got down and started (86) ______the road.<br>Select the most appropriate option to fill in the blank no.84.</p>",
                    options_en: ["<p>crash</p>", "<p>scream</p>", 
                                "<p>splash</p>", "<p>buzz</p>"],
                    options_hi: ["<p>crash</p>", "<p>scream</p>",
                                "<p>splash</p>", "<p>buzz</p>"],
                    solution_en: "<p>84.(a) <br><strong>Crash</strong> - to fall suddenly<br><strong>Splash</strong> - to fall or to make liquid fall noisily or fly in drops onto a person or thing<br><strong>Buzz</strong> - to make the sound that bees, etc. make when flying</p>",
                    solution_hi: "<p>84.(a) <br><strong>Crash</strong> (टकराना) - to fall suddenly<br><strong>Splash</strong> (छप-छप करना) - to fall or to make liquid fall noisily or fly in drops onto a person or thing<br><strong>Buzz</strong> (भिनभिनाना) - to make the sound that bees, etc. make when flying</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "85",
                    section: "7",
                    question_en: "<p>85. <strong>Cloze Test :</strong><br>A (82) ______of trucks carrying soldiers was coming down the mountain road. The trucks (83) _____as there had been heavy snowfall in that area. Suddenly, with a (84) ______ a huge tree on the hill side fell bringing along with it boulders and mud. (85) ______, the driver of first truck stopped in time. The soldiers got down and started (86) ______the road.<br>Select the most appropriate option to fill in the blank no. 85</p>",
                    question_hi: "<p>85. <strong>Cloze Test :</strong><br>A (82) ______of trucks carrying soldiers was coming down the mountain road. The trucks (83) _____as there had been heavy snowfall in that area. Suddenly, with a (84) ______ a huge tree on the hill side fell bringing along with it boulders and mud. (85) ______, the driver of first truck stopped in time. The soldiers got down and started (86) ______the road.<br>Select the most appropriate option to fill in the blank no.85</p>",
                    options_en: ["<p>Similarly</p>", "<p>logically</p>", 
                                "<p>Magically</p>", "<p>Fortunately</p>"],
                    options_hi: ["<p>Similarly</p>", "<p>logically</p>",
                                "<p>Magically</p>", "<p>Fortunately</p>"],
                    solution_en: "<p>85.(d) Fortunately.<br>&lsquo;Fortunately&rsquo; should fill in the blanks because in the above sentence the driver is saved from the accident caused due to crashing of a huge tree that shows luck.</p>",
                    solution_hi: "<p>85.(d) Fortunately.<br>यहाँ &lsquo;Fortunately&rsquo; रिक्त स्थान को भरेगा क्योंकि उपरोक्त sentence में driver एक विशाल वृक्ष के गिरने से हुए accident से बच गया है, जो luck को दर्शाता है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "86",
                    section: "7",
                    question_en: "<p>86. <strong>Cloze Test :</strong><br>A (82) ______of trucks carrying soldiers was coming down the mountain road. The trucks (83) _____as there had been heavy snowfall in that area. Suddenly, with a (84) ______ a huge tree on the hill side fell bringing along with it boulders and mud. (85) ______, the driver of first truck stopped in time. The soldiers got down and started (86) ______the road.<br>Select the most appropriate option to fill in the blank no. 5</p>",
                    question_hi: "<p>86. <strong>Cloze Test :</strong><br>A (82) ______of trucks carrying soldiers was coming down the mountain road. The trucks (83) _____as there had been heavy snowfall in that area. Suddenly, with a (84) ______ a huge tree on the hill side fell bringing along with it boulders and mud. (85) ______, the driver of first truck stopped in time. The soldiers got down and started (86) ______the road.<br>Select the most appropriate option to fill in the blank no. 5</p>",
                    options_en: ["<p>changing</p>", "<p>altering</p>", 
                                "<p>clearing</p>", "<p>moving</p>"],
                    options_hi: ["<p>changing</p>", "<p>altering</p>",
                                "<p>clearing</p>", "<p>moving</p>"],
                    solution_en: "<p>86.(c) clearing.</p>",
                    solution_hi: "<p>86.(c) clearing.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "87",
                    section: "7",
                    question_en: "<p>87. Select the appropriate synonym of the given word.<br>EXPENSIVE</p>",
                    question_hi: "<p>87. Select the appropriate synonym of the given word.<br>EXPENSIVE</p>",
                    options_en: ["<p>Mild</p>", "<p>Sober</p>", 
                                "<p>Gentle</p>", "<p>Dear</p>"],
                    options_hi: ["<p>Mild</p>", "<p>Sober</p>",
                                "<p>Gentle</p>", "<p>Dear</p>"],
                    solution_en: "<p>87.(d) <br><strong>Dear</strong> - highly valued. <br><strong>EXPENSIVE</strong> - costing a lot of money.<br><strong>Mild</strong> - not severe, serious, or harsh.<br><strong>Sober</strong> - not affected by alcohol; not drunk.<br><strong>Gentle</strong> - having or showing a mild, kind, or tender temperament or character.</p>",
                    solution_hi: "<p>87.(d) <br><strong>Dear</strong> (अत्यधिक मंहगा) - highly valued. <br><strong>EXPENSIVE</strong> (अत्यधिक मंहगा) - costing a lot of money.<br><strong>Mild</strong> (सौम्य) - not severe, serious, or harsh.<br><strong>Sober</strong> (सादगीपूर्ण) - not affected by alcohol; not drunk.<br><strong>Gentle</strong> (सज्जन) - having or showing a mild, kind, or tender temperament or character.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "88",
                    section: "7",
                    question_en: "<p>88. Select the appropriate antonym of the given word.<br>DIVIDE</p>",
                    question_hi: "<p>88. Select the appropriate antonym of the given word.<br>DIVIDE</p>",
                    options_en: ["<p>Split</p>", "<p>Engulf</p>", 
                                "<p>Unite</p>", "<p>Break</p>"],
                    options_hi: ["<p>Split</p>", "<p>Engulf</p>",
                                "<p>Unite</p>", "<p>Break</p>"],
                    solution_en: "<p>88.(c) <br><strong>Unite</strong> - come or bring together for a common purpose or action.<br><strong>DIVIDE</strong> - separate or be separated into parts.<br><strong>Split</strong> - break or cause to break forcibly into parts, especially into halves or along the grain.<br><strong>Engulf</strong> - (of a natural force) sweep over (something) so as to surround or cover it completely.<br><strong>Break</strong> - separate into pieces as a result of a blow, shock, or strain.</p>",
                    solution_hi: "<p>88.(c) <br><strong>Unite</strong> (एकजुट होना) - come or bring together for a common purpose or action.<br><strong>DIVIDE</strong> (विभाजन करना) - separate or be separated into parts.<br><strong>Split</strong> (विभाजित करना) - break or cause to break forcibly into parts, especially into halves or along the grain.<br><strong>Engulf</strong> (घेर लेना/निगल लेना) - (of a natural force) sweep over (something) so as to surround or cover it completely.<br><strong>Break</strong> (तोड़ना) - separate into pieces as a result of a blow, shock, or strain.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "89",
                    section: "7",
                    question_en: "<p>89. Select the wrongly spelt word.</p>",
                    question_hi: "<p>89. Select the wrongly spelt word.</p>",
                    options_en: ["<p>Cremator</p>", "<p>Creater</p>", 
                                "<p>Cricketer</p>", "<p>Cracker</p>"],
                    options_hi: ["<p>Cremator</p>", "<p>Creater</p>",
                                "<p>Cricketer</p>", "<p>Cracker</p>"],
                    solution_en: "<p>89.(b) Creater. <br>Creator is the correct spelling.</p>",
                    solution_hi: "<p>89.(b) Creater. <br>Creator सही spelling है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "90",
                    section: "7",
                    question_en: "<p>90. Identify the segment in the sentence which contains the grammatical error from the given options.<br>My brother, who live in Delhi, has written me a letter.</p>",
                    question_hi: "<p>90. Identify the segment in the sentence which contains the grammatical error from the given options.<br>My brother, who live in Delhi, has written me a letter.</p>",
                    options_en: ["<p>who lived in Delhi</p>", "<p>has written</p>", 
                                "<p>me a letter</p>", "<p>My brother</p>"],
                    options_hi: ["<p>who lived in Delhi</p>", "<p>has written</p>",
                                "<p>me a letter</p>", "<p>My brother</p>"],
                    solution_en: "<p>90.(a) who lived in Delhi. <br>&ldquo;who lives in Delhi&rdquo; should be used in place of &ldquo;who live&rdquo;.because the subject(My brother) is a singular noun.</p>",
                    solution_hi: "<p>90.(a) who lived in Delhi. <br>&ldquo;Who live&rdquo; के स्थान पर &ldquo;who lives in Delhi&rdquo; का प्रयोग होना चाहिए, क्योंकि subject &lsquo;My brother&rsquo;, singular noun है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "91",
                    section: "7",
                    question_en: "<p>91. Select the wrongly spelt word.</p>",
                    question_hi: "<p>91. Select the wrongly spelt word.</p>",
                    options_en: ["<p>Charisma</p>", "<p>Choir</p>", 
                                "<p>Champion</p>", "<p>Chouffer</p>"],
                    options_hi: ["<p>Charisma</p>", "<p>Choir</p>",
                                "<p>Champion</p>", "<p>Chouffer</p>"],
                    solution_en: "<p>91.(d) Chouffer.<br>Chauffeur is the correct spelling.</p>",
                    solution_hi: "<p>91.(d) Chouffer.<br>Chauffeur सही spelling है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "92",
                    section: "7",
                    question_en: "<p>92. Select the appropriate synonym of the given word.<br>RETAIN</p>",
                    question_hi: "<p>92. Select the appropriate synonym of the given word.<br>RETAIN</p>",
                    options_en: ["<p>Convoy</p>", "<p>Maintain</p>", 
                                "<p>Destroy</p>", "<p>Gain</p>"],
                    options_hi: ["<p>Convoy</p>", "<p>Maintain</p>",
                                "<p>Destroy</p>", "<p>Gain</p>"],
                    solution_en: "<p>92.(b) <strong>Maintain</strong> - cause or enable (a condition or situation) to continue.<br><strong>RETAIN</strong> - continue to have (something); keep possession of.<br><strong>Convoy</strong> - a group of ships or vehicles travelling together, typically one accompanied by armed troops, warships, or other vehicles for protection.<br><strong>Destroy</strong> - end the existence of (something) by damaging or attacking it.<br><strong>Gain</strong> - obtain or secure (something wanted or desirable).</p>",
                    solution_hi: "<p>92.(b) <strong>Maintain</strong> (बनाए रखना) - cause or enable (a condition or situation) to continue.<br><strong>RETAIN</strong> (बनाए रखना) - continue to have (something); keep possession of.<br><strong>Convoy</strong> (काफिला) - a group of ships or vehicles travelling together, typically one accompanied by armed troops, warships, or other vehicles for protection.<br><strong>Destroy</strong> (नष्ट करना) - end the existence of (something) by damaging or attacking it.<br><strong>Gain</strong> (प्राप्त करना) - obtain or secure (something wanted or desirable).</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "93",
                    section: "7",
                    question_en: "<p>93. Select the most appropriate sequence from the given options to make a meaningful paragraph from jumbled sentences.<br>A. Aesop was one of them who lived in Greece about 2500 years ago.<br>B. He told many interesting stories to the people.<br>C. There were many talented people in ancient Greece.<br>D. Although, he was very ugly, he had a very clever brain.</p>",
                    question_hi: "<p>93. Select the most appropriate sequence from the given options to make a meaningful paragraph from jumbled sentences.<br>A. Aesop was one of them who lived in Greece about 2500 years ago.<br>B. He told many interesting stories to the people.<br>C. There were many talented people in ancient Greece.<br>D. Although, he was very ugly, he had a very clever brain.</p>",
                    options_en: ["<p>BDAC</p>", "<p>CADB</p>", 
                                "<p>CDBA</p>", "<p>BADC</p>"],
                    options_hi: ["<p>BDAC</p>", "<p>CADB</p>",
                                "<p>CDBA</p>", "<p>BADC</p>"],
                    solution_en: "<p>93.(b) CADB.<br>Sentence C says about &ldquo;many talented people in ancient Greece&rdquo; and Sentence A states that Aesop was one of them who lived in Greece about 2500 years ago. A will follow C. <br>Sentence D says about Aesop that he was very ugly, he had a very clever brain. So, D will follow A. <br>Option (b) CADB is the correct sequence.</p>",
                    solution_hi: "<p>93.(b)<br>Sentence C में \"प्राचीन Greece में कई प्रतिभाशाली लोगों\" के बारे में बताया गया है और Sentence A में कहा गया है कि Aesop उनमें से एक था जो लगभग 2500 वर्ष पूर्व Greece में रहता था। इसलिए A, C के बाद आएगा। Sentence D में Aesop के बारे में कहा गया है कि वह बहुत ugly था, उसका दिमाग बहुत तेज था। इसलिए, D, A के बाद आएगा। अतः, option (b) CADB में सही sequence है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "94",
                    section: "7",
                    question_en: "<p>94. Select the most appropriate word to fill in the blank.<br>He tried to ______ my ring.</p>",
                    question_hi: "<p>94. Select the most appropriate word to fill in the blank.<br>He tried to ______ my ring.</p>",
                    options_en: ["<p>steel</p>", "<p>steal</p>", 
                                "<p>still</p>", "<p>stile</p>"],
                    options_hi: ["<p>steel</p>", "<p>steal</p>",
                                "<p>still</p>", "<p>stile</p>"],
                    solution_en: "<p>94.(b) steal.</p>",
                    solution_hi: "<p>94.(b) steal.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "95",
                    section: "7",
                    question_en: "<p>95. Select the appropriate antonym of the given word.<br>DEXTERITY</p>",
                    question_hi: "<p>95. Select the appropriate antonym of the given word.<br>DEXTERITY</p>",
                    options_en: ["<p>Agility</p>", "<p>Skill</p>", 
                                "<p>Ignorance</p>", "<p>Mastery</p>"],
                    options_hi: ["<p>Agility</p>", "<p>Skill</p>",
                                "<p>Ignorance</p>", "<p>Mastery</p>"],
                    solution_en: "<p>95.(c) <br><strong>Ignorance</strong> - lack of knowledge or information.<br><strong>DEXTERITY</strong> - skill in performing tasks, especially with the hands.<br><strong>Agility</strong> - ability to move quickly and easily.<br><strong>Skill</strong> - the ability to do something well; expertise.<br><strong>Mastery</strong> - comprehensive knowledge or skill in a particular subject or activity.</p>",
                    solution_hi: "<p>95.(c) <br><strong>Ignorance</strong> (अज्ञान) - lack of knowledge or information.<br><strong>DEXTERITY</strong> (निपुणता) - skill in performing tasks, especially with the hands.<br><strong>Agility</strong> (फुर्ती) - ability to move quickly and easily.<br><strong>Skill</strong> (कौशल) - the ability to do something well; expertise.<br><strong>Mastery</strong> (महारत) - comprehensive knowledge or skill in a particular subject or activity.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "96",
                    section: "7",
                    question_en: "<p>96. Select the most appropriate sequence from the given options to make a meaningful paragraph from jumbled sentences.<br>A. He is a gifted volleyball player.<br>B. But now a days he does not play international matches.<br>C. It is because he had an accident last year.<br>D. Sanjay is my best friend.</p>",
                    question_hi: "<p>96. Select the most appropriate sequence from the given options to make a meaningful paragraph from jumbled sentences.<br>A. He is a gifted volleyball player.<br>B. But now a days he does not play international matches.<br>C. It is because he had an accident last year.<br>D. Sanjay is my best friend.</p>",
                    options_en: ["<p>DABC</p>", "<p>DCAB</p>", 
                                "<p>CDBA</p>", "<p>ABCD</p>"],
                    options_hi: ["<p>DABC</p>", "<p>DCAB</p>",
                                "<p>CDBA</p>", "<p>ABCD</p>"],
                    solution_en: "<p>96.(a) DABC. <br>Sentence D says that Sanjay is my best friend and sentence A states that he is a gifted volleyball player. A will follow D. Sentence B says that &ldquo;he does not play international matches&rdquo; and sentence C states the reason for that. C will follow B. Option (a) DABC is the correct sequence.</p>",
                    solution_hi: "<p>96.(a) DABC. <br>Sentence D कहता है कि Sanjay मेरा best friend है और sentence A कहता है कि वह एक प्रतिभाशाली volleyball player है। इसलिए, A, D के बाद आएगा। Sentence B कहता है कि \"वह international match नहीं खेलता है\" और sentence C इसका कारण बताता है। इसलिए C, B के बाद आएगा। अतः, option (a) DABC में सही sequence है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "97",
                    section: "7",
                    question_en: "<p>97. Select one word for the following group of words.<br>One who loves his country</p>",
                    question_hi: "<p>97. Select one word for the following group of words.<br>One who loves his country</p>",
                    options_en: ["<p>Collaborator</p>", "<p>Conspirator</p>", 
                                "<p>Patriot</p>", "<p>Traitor</p>"],
                    options_hi: ["<p>Collaborator</p>", "<p>Conspirator</p>",
                                "<p>Patriot</p>", "<p>Traitor</p>"],
                    solution_en: "<p>97.(c)<br><strong>Patriot</strong> - One who loves his country : <br><strong>Collaborator</strong> - a person who works jointly on an activity or project; an associate.<br><strong>Conspirator</strong> - a person who takes part in a conspiracy.<br><strong>Traitor</strong> - a person who betrays someone or something, such as a friend, cause, or principle.</p>",
                    solution_hi: "<p>97.(c)<br><strong>Patriot</strong> (देश-भक्त) - One who loves his country : <br><strong>Collaborator</strong> (सहयोगी) - a person who works jointly on an activity or project; an associate.<br><strong>Conspirator</strong> (षड्यंत्रकारी) - a person who takes part in a conspiracy.<br><strong>Traitor</strong> (देशद्रोही) - a person who betrays someone or something, such as a friend, cause, or principle.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "98",
                    section: "7",
                    question_en: "<p>98. Select the most appropriate option to substitute the underline segment in the given sentence.If there is no need to substitute it, select no substitution.<br>Hardly had he <span style=\"text-decoration: underline;\">sit on the chair than</span> it broke.</p>",
                    question_hi: "<p>98. Select the most appropriate option to substitute the underline segment in the given sentence.If there is no need to substitute it, select no substitution.<br>Hardly had he <span style=\"text-decoration: underline;\">sit on the chair than</span> it broke.</p>",
                    options_en: ["<p>no substitution</p>", "<p>sat on the chair when</p>", 
                                "<p>sat onto a chair then</p>", "<p>sit in the chair when</p>"],
                    options_hi: ["<p>no substitution</p>", "<p>sat on the chair when</p>",
                                "<p>sat onto a chair then</p>", "<p>sit in the chair when</p>"],
                    solution_en: "<p>98.(b) sat on the chair when. <br>&ldquo;sat on the chair when&rdquo; should be used in place of &ldquo;sit on the chair than&rdquo;. Here instead of &lsquo;than&rsquo;, &lsquo;when&rsquo; should be used because Hardly, scarcely and barely are followed by when, while no sooner is followed by than.</p>",
                    solution_hi: "<p>98.(b) sat on the chair when. <br>&ldquo;Sit on the chair than&rdquo; के स्थान पर &ldquo;sat on the chair when&rdquo; का प्रयोग किया जाना चाहिए। यहाँ \'than\' के बजाय \'when\' का प्रयोग किया जाना चाहिए क्योंकि Hardly, scarcely और barely के बाद when आता है, जबकि no sooner के बाद than आता है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "99",
                    section: "7",
                    question_en: "<p>99. Select the most appropriate option to substitute the underline segment in the given sentence.If there is no need to substitute it, select no improvement.<br>The diver <span style=\"text-decoration: underline;\">dive in the pool</span> from a great height.</p>",
                    question_hi: "<p>99. Select the most appropriate option to substitute the underline segment in the given sentence.If there is no need to substitute it, select no improvement.<br>The diver <span style=\"text-decoration: underline;\">dive in the pool</span> from a great height.</p>",
                    options_en: ["<p>dived at the pool</p>", "<p>no improvement</p>", 
                                "<p>dives to a pool</p>", "<p>dived into the pool</p>"],
                    options_hi: ["<p>dived at the pool</p>", "<p>no improvement</p>",
                                "<p>dives to a pool</p>", "<p>dived into the pool</p>"],
                    solution_en: "<p>99.(d) dived into the pool.<br>&lsquo;Dived into the pool&rsquo; should be used instead of &lsquo;dive in the pool&rsquo;. The preposition &ldquo;into&rdquo; should be used with dive.</p>",
                    solution_hi: "<p>99.(d) dived into the pool.<br>&lsquo;Dive in the pool&rsquo; के स्थान पर &lsquo;Dived into the pool&rsquo; का प्रयोग किया जाना चाहिए। &lsquo;Dive&rsquo; के साथ preposition &ldquo;into&rdquo; का प्रयोग किया जाना चाहिए।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "100",
                    section: "misc",
                    question_en: "<p>100. Select the word which means the same as the words of the group given.<br>Incapable of paying debts</p>",
                    question_hi: "<p>100. Select the word which means the same as the words of the group given.<br>Incapable of paying debts</p>",
                    options_en: ["<p>Extravagant</p>", "<p>Obsolete</p>", 
                                "<p>Insolvent</p>", "<p>Corrupt</p>"],
                    options_hi: ["<p>Extravagant</p>", "<p>Obsolete</p>",
                                "<p>Insolvent</p>", "<p>Corrupt</p>"],
                    solution_en: "<p>100.(c) <br><strong>Insolvent</strong> - Incapable of paying debts<br><strong>Extravagant</strong> - lacking restraint in spending money or using resources.<br><strong>Obsolete</strong> - no longer produced or used; out of date.<br><strong>Corrupt</strong> - evil or morally depraved.</p>",
                    solution_hi: "<p>100.(c) <br><strong>Insolvent</strong> (दिवालिया) - Incapable of paying debts<br><strong>Extravagant</strong> (फ़िजूल ख़र्च) - lacking restraint in spending money or using resources.<br><strong>Obsolete</strong> (अप्रचलित) - no longer produced or used; out of date.<br><strong>Corrupt</strong> (भ्रष्ट) - evil or morally depraved.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>