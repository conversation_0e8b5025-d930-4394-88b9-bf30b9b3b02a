<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">8:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 15</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">15</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 8 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["18"] = {
                name: "General Awareness",
                start: 0,
                end: 13
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="18">General Awareness</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 14,
                end: 14
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "18",
                    question_en: "<p>1. Who is the author of &ldquo;Early History of India&rdquo;?</p>",
                    question_hi: "<p>1. \"अर्ली हिस्ट्री ऑफ इंडिया\" के लेखक कौन हैं?</p>",
                    options_en: ["<p>K A Nilakanta Sastri</p>", "<p>R C Majumdar</p>", 
                                "<p>Vincent Arthur Smith</p>", "<p>R G Bhandarkar</p>"],
                    options_hi: ["<p>के ए नीलकांत शास्त्री</p>", "<p>आर सी मजूमदार</p>",
                                "<p>विन्सेंट आर्थर स्मिथ</p>", "<p>आर जी भंडारकर</p>"],
                    solution_en: "<p>1.(c) <strong>Vincent Arthur Smith </strong>was an Irish Indologist and art historian. K.A. Nilakanta Sastri - &ldquo;A History of South India: From Prehistoric Times to the Fall of Vijayanagar&rdquo;. RC Majumdar - &ldquo;Ancient India&rdquo;. RG Bhandarkar - &ldquo;First book of Sanskrit&rdquo;. Walter Ashlin Fairservis - &ldquo;The Roots of Ancient India: The Archaeology of Early Indian Civilization&rdquo;.</p>",
                    solution_hi: "<p>1.(c) <strong>विन्सेन्ट आर्थर स्मिथ </strong>एक आयरिश इंडोलॉजिस्ट और कला इतिहासकार थे। के ए नीलकांत शास्त्री - &ldquo;अ हिस्ट्री ऑफ़ साउथ इंडिया: फ्रॉम प्रेहिस्टोरिक टाइम्स टू द फॉल ऑफ़ विजयनगर\"। आर.सी.मजूमदार - \"एन्सिएंट इंडिया\"। आर.जी. भंडारकर - \"फर्स्ट बुक ऑफ संस्कृत\"। वाल्टर एशलिन फेयरसर्विस - \"द रूट्स ऑफ़ एन्सिएंट इंडिया : द आर्कोलॉजी ऑफ़ अर्ली इंडियन सिविलाइज़ेशन&rdquo; ।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "2",
                    section: "18",
                    question_en: "<p>2. Name the famous Indian writer who has written novels like &lsquo;Untouchable&rsquo; and &lsquo;Coolie&rsquo;.</p>",
                    question_hi: "<p>2. उस प्रसिद्ध भारतीय लेखक का नाम बताइए जिसने \'अनटचेबल&rsquo; और \'कुली\' जैसे उपन्यास लिखे हैं।</p>",
                    options_en: ["<p>Anita Desai</p>", "<p>Mulk Raj Anand</p>", 
                                "<p>RK Narayan</p>", "<p>Kamla Das</p>"],
                    options_hi: ["<p>अनीता देसाई</p>", "<p>मुल्क राज आनंद</p>",
                                "<p>आर के नारायण</p>", "<p>कमला दास</p>"],
                    solution_en: "<p>2.(b) <strong>Mulk Raj Anand. Other books </strong>(&ldquo;Lajwanti&rdquo;, &ldquo;Private Life of an Indian Prince&rdquo;, &ldquo;Book of Indian Beauty&rdquo;). <strong>Anita Desai </strong>(&ldquo;Clear Light of Day&rdquo;, &ldquo;Fasting-Feasting&rdquo;, &ldquo;Fire on the Mountain&rdquo;). <strong>R.K. Narayan</strong> (&ldquo;Malgudi Days&rdquo;, &ldquo;Swami and Friends&rdquo;, &ldquo;The English Teacher&rdquo;, &ldquo;Waiting for the Mahatma&rdquo;). <strong>Kamala Das</strong> (&ldquo;My Story&rdquo;, &ldquo;Padmavati the Harlot and Other Stories&rdquo;).</p>",
                    solution_hi: "<p>2.(b) <strong>मुल्क राज आनंद। अन्य पुस्तकें</strong> (\"लाजवंती\", &ldquo;प्राइवेट लाइफ ऑफ़ एन इंडियन प्रिंस\", \"बुक ऑफ़ इंडियन ब्यूटी\")। <strong>अनीता देसाई </strong>(\"क्लियर लाइट ऑफ डे\", \"फास्टिंग-फीस्टिंग\", \"फायर ऑन द माउंटेन\")। <strong>R.K. नारायण</strong> (\"मालगुडी डेज़\", \"स्वामी एंड फ्रेंड्स\", \"द इंग्लिश टीचर\", \"वेटिंग फॉर द महात्मा\")। <strong>कमला दास</strong> (\"माई स्टोरी\", \"पद्मावती द हार्लोट एंड अदर स्टोरीज़\")।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "3",
                    section: "18",
                    question_en: "<p>3. Who wrote the famous book &lsquo;Ignited Minds&rsquo;?</p>",
                    question_hi: "<p>3. प्रसिद्ध पुस्तक \'इग्नाइटेड माइंड्स\' किसने लिखी है?</p>",
                    options_en: ["<p>Jhumpa Lahiri</p>", "<p>A.P.J Abdul Kalam</p>", 
                                "<p>Chetan Bhagat</p>", "<p>Robin Sharma</p>"],
                    options_hi: ["<p>झुम्पा लाहिड़ी</p>", "<p>ए. पी. जे. अब्दुल कलाम</p>",
                                "<p>चेतन भगत</p>", "<p>रॉबिन शर्मा</p>"],
                    solution_en: "<p>3.(b) <strong>A.P.J Abdul Kalam.</strong> His Other books: &ldquo;India 2020: A Vision for the New Millenium&rdquo;, &ldquo;Wings of Fire : An Autobiography&rdquo;, &ldquo;The Luminous Sparks&rdquo;, &ldquo;Mission India&rdquo;, &ldquo;Inspiring Thoughts&rdquo;, &ldquo;Indomitable Spirit&rdquo;, &ldquo;Target 3 billion&rdquo;. <strong>Other Authors and their books:</strong> Jhumpa Lahiri - &ldquo;The Namesake&rdquo;, &ldquo;Interpreter of Maladies&rdquo;, &ldquo;Unaccustomed Earth&rdquo;. Chetan Bhagat - &ldquo;Five Point Someone&rdquo;, &ldquo;Half Girlfriend&rdquo;, &ldquo;The 3 Mistakes of My Life&rdquo;. Robin Sharma - &ldquo;The 5 AM Club&rdquo;, &ldquo;The Greatness Guide&rdquo;, &ldquo;The Mastery Manual&rdquo;.</p>",
                    solution_hi: "<p>3.(b) <strong>ए. पी. जे. अब्दुल कलाम। </strong>उनकी अन्य पुस्तकें: \"इंडिया 2020: ए विजन फॉर द न्यू मिलेनियम\", \"विंग्स ऑफ फायर: एन ऑटोबायोग्राफी\", \"द ल्यूमिनस स्पार्क्स\", \"मिशन इंडिया\", \"इंस्पायरिंग थॉट्स\", \"इंडोमीटेबल स्पिरिट\", \"टारगेट 3 बिलियन\" &rdquo;। <strong>अन्य लेखक और उनकी पुस्तकें: </strong>झुम्पा लाहिड़ी - \"द नेमसेक\", \"इंटरप्रेटर ऑफ़ मैलाडीज़\", \"अनअकस्टम्ड अर्थ\"। चेतन भगत - \"फाइव पॉइंट समवन\", \"हाफ गर्लफ्रेंड\", \"द 3 मिस्टेक्स ऑफ माई लाइफ\"। रॉबिन शर्मा - \"द 5 एएम क्लब\", \"द ग्रेटनेस गाइड\", \"द मास्टरी मैनुअल\"।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "4",
                    section: "18",
                    question_en: "<p>4. Who is the author of the Panchatantra collection of fables?</p>",
                    question_hi: "<p>4. दंतकथाओं के पंचतंत्र संग्रह के लेखक कौन हैं?</p>",
                    options_en: ["<p>Vishnu Sharma</p>", "<p>Ved Shastri</p>", 
                                "<p>Skandagupta</p>", "<p>Vishnu Gupta</p>"],
                    options_hi: ["<p>विष्णु शर्मा</p>", "<p>वेद शास्त्री</p>",
                                "<p>स्कन्दगुप्त</p>", "<p>विष्णु गुप्ता</p>"],
                    solution_en: "<p>4.(a) <strong>Vishnu Sharma. </strong>\"<strong>Panchatantra\" </strong>- translates to \"Five Principles\" written in sanskrit. Fables primarily feature animals and convey moral, political, and practical wisdom through their stories. <strong>Skandagupta </strong>- son and successor of Kumaragupta I. His reign is known for the Huna Invasion. <strong>VishnuGupta </strong>- Also known as Chanakya or Kautilya . He authored &ldquo;<strong>Arthashastra</strong>&rdquo; - a book on statecraft.</p>",
                    solution_hi: "<p>4.(a) <strong>विष्णु शर्मा। </strong>\"<strong>पंचतंत्र</strong>\" - संस्कृत में लिखे गए \"पांच सिद्धांतों\" का अनुवाद है। दंतकथाएँ मुख्य रूप से जानवरों को दर्शाती हैं और अपनी कहानियों के माध्यम से नैतिक, राजनीतिक और व्यावहारिक ज्ञान बताती हैं। <strong>स्कंदगुप्त </strong>- कुमारगुप्त प्रथम के पुत्र और उत्तराधिकारी। उनका शासनकाल हूण आक्रमण के लिए जाना जाता है। <strong>विष्णुगुप्त </strong>- जिन्हें चाणक्य या कौटिल्य के नाम से भी जाना जाता है। उन्होंने \"<strong>अर्थशास्त्र</strong>\" लिखा - जो शासन कला पर एक पुस्तक है।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "5",
                    section: "18",
                    question_en: "<p>5. &lsquo;Flights&rsquo;, a novel about travel in the 21<sup>st</sup> century and human anatomy is written by which of the following Booker prize winner (2018) writers?</p>",
                    question_hi: "<p>5. 21 वीं सदी में यात्रा और मानव शरीर रचना विज्ञान के बारे में एक उपन्यास \'फ्लाइट्स\' निम्नलिखित में से किस बुकर पुरस्कार विजेता (2018) लेखक द्वारा लिखा गया है?</p>",
                    options_en: ["<p>Virginie Despentes</p>", "<p>Arundhati Roy</p>", 
                                "<p>Olga Tokarczuk</p>", "<p>Taslima Nasreen</p>"],
                    options_hi: ["<p>वर्जिनी डेस्पेंट्स</p>", "<p>अरुंधति रॉय</p>",
                                "<p>ओल्गा टोकार्ज़ुक</p>", "<p>तसलीमा नसरीन</p>"],
                    solution_en: "<p>5.(c) <strong>Olga Tokarczuk</strong>. She is a Polish author and awarded the Nobel Prize in Literature (2018). <strong>Her Other Books -</strong> \"Bieguni\'\', \"Primeval and Other Times\", \"The Books of Jacob\". <strong>Other Books and Author : Virginie Despentes -</strong> \"Les Jolies Choses\", \"Vernon Subutex\", \"King Kong Theory\".<strong> Taslima Nasreen - </strong>\"Lajja\", \"My Girlhood\", \"French Lover\". <strong>Arundhati Roy - </strong>\"The God of Small Things\", \"The Ministry of Utmost Happiness\", \"The Algebra of Infinite Justice\".</p>",
                    solution_hi: "<p>5.(c) <strong>ओल्गा टोकार्ज़ुक </strong>- एक पोलिश लेखिका और इन्हें साहित्य में नोबेल पुरस्कार (2018) से सम्मानित किया गया है। <strong>इनकी अन्य पुस्तकें - </strong>\"बीगुनी\'\', \"प्राइमवल एंड अदर टाइम्स\", \"द बुक्स ऑफ जैकब\"। <strong>अन्य पुस्तकें और लेखक: वर्जिनी डेस्पेंटेस -</strong> \"लेस जोलिस चोसेस\", \"वर्नोन सुब्यूटेक्स\", \"किंग कांग थ्योरी\"। <strong>तसलीमा नसरीन -</strong> \"लज्जा\", \"माई गर्लहुड\", \"फ्रेंच लवर\"। <strong>अरुंधति रॉय - </strong>\"द गॉड ऑफ स्मॉल थिंग्स\", \"द मिनिस्ट्री ऑफ अटमोस्ट हैप्पीनेस\", \"द अलजेब्रा ऑफ इनफिनिट जस्टिस\"।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "6",
                    section: "18",
                    question_en: "<p>6. Rangbhumi, Godan, Gaban and Vardaan are the novels written by:</p>",
                    question_hi: "<p>6. रंगभूमि, गोदान, गबन और वरदान किसके द्वारा लिखे गए उपन्यास हैं?</p>",
                    options_en: ["<p>Sumitra Nandan Pant</p>", "<p>Ramdhari Singh Dinkar</p>", 
                                "<p>Munshi Prem Chand</p>", "<p>Maithili sharan Gupt</p>"],
                    options_hi: ["<p>सुमित्रा नंदन पंत</p>", "<p>रामधारी सिंह दिनकर</p>",
                                "<p>मुंशी प्रेम चंद</p>", "<p>मैथिली शरण गुप्ता</p>"],
                    solution_en: "<p>6.(c) <strong>Munshi Prem Chand.</strong> His original name - Dhanpat Rai Srivastava. Other Books - &lsquo;idgah&rsquo;, &lsquo;Kaphan&rsquo;, &lsquo;Bade ghar ki beti&rsquo;, &lsquo;Panch Parmeshwar&rsquo;, &lsquo;Poos ki Raat&rsquo;. Famous Author and books : <strong>Sumitra Nandan Pant -</strong> &lsquo;Gramya&rsquo;, &lsquo;Pallav&rsquo;, &lsquo;Tarapath&rsquo;; <strong>Ramdhari Singh Dinkar - &lsquo;</strong>Rashmirathi&rsquo;, &lsquo;Urvashi&rsquo;, &lsquo;Neel Kusum&rsquo;; <strong>Maithili Sharma Gupt</strong> - &lsquo;Bharat-Bharati&rsquo;, &lsquo;Jayadrath Vadh&rsquo;, &lsquo;Yashodhara&rsquo;।</p>",
                    solution_hi: "<p>6.(c) <strong>मुंशी प्रेम चंद।</strong> <strong>उनका मूल नाम - </strong>धनपत राय श्रीवास्तव। अन्य पुस्तकें - \'ईदगाह\', \'कफन\', \'बड़े घर की बेटी\', \'पंच परमेश्वर\', \'पूस की रात\'। प्रसिद्ध लेखक और पुस्तकें: <strong>सुमित्रा नंदन पंत -</strong> \'ग्राम्या\', \'पल्लव\', \'तारापथ\'; रामधारी सिंह दिनकर - \'रश्मिरथी\', \'उर्वशी\', \'नील कुसुम\';<strong> मैथिली शरण गुप्त -</strong> \'भारत-भारती\', \'जयद्रथ वध\', \'यशोधरा\'।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "7",
                    section: "18",
                    question_en: "<p>7. Who wrote the book, Stri Purush Tulana (A comparison between Women and Men) which criticised the social differences between men and women?</p>",
                    question_hi: "<p>7. स्त्री पुरुष तुलना (महिलाओं और पुरुषों के बीच एक तुलना) पुस्तक किसने लिखी, जिसने पुरुषों और महिलाओं के बीच सामाजिक अंतर की आलोचना की?</p>",
                    options_en: ["<p>Tarabai Shinde</p>", "<p>Pandita Ramabai</p>", 
                                "<p>Raja Ram Mohan Roy</p>", "<p>Ishwar Chandra Vidyasagar</p>"],
                    options_hi: ["<p>ताराबाई शिंदे</p>", "<p>पंडिता रमाबाई</p>",
                                "<p>राजा राम मोहन राय</p>", "<p>ईश्वर चंद्र विद्यासागर</p>"],
                    solution_en: "<p>7.(a) <strong>Tarabai Shinde</strong> was a feminist activist who protested patriarchy and caste in 19th century India. Other indian social reformer author books - <strong>Pandita Ramabai</strong> (&ldquo;The High-caste Hindu Woman&rdquo;, &ldquo;Pandita Ramabai Through Her Own Words: Selected Works&rdquo;); <strong>Raja Ram Mohan Roy</strong> (&ldquo;Tuhfat-ul-Muwahhidin&rdquo;, &ldquo;Vedanta Gantha&rdquo;);<strong> Ishwar Chandra Vidyasagar </strong>(&ldquo;Betaal Panchabinsati&rdquo;, &ldquo;Shakuntala&rdquo;).</p>",
                    solution_hi: "<p>7.(a) <strong>ताराबाई शिंदे। </strong>वह एक नारीवादी कार्यकत्री थीं जिन्होंने 19वीं सदी के भारत में पितृसत्ता और जातिवाद का विरोध किया था। अन्य भारतीय समाज सुधारक लेखकों की पुस्तकें - <strong>पंडिता रमाबाई </strong>(\"उच्च जाति की हिंदू महिला\", \"पंडिता रमाबाई अपने शब्दों के माध्यम से: चयनित कार्य\"); <strong>राजा राम मोहन रॉय</strong> (\"तुहफ़त-उल-मुवाहिदीन\", \"वेदांत गंथा\"); <strong>ईश्वर चंद्र विद्यासागर </strong>(\'बेताल पंचबिनसति\', \'शकुंतला\')।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "8",
                    section: "18",
                    question_en: "<p>8. Who was the author of the literary work &lsquo;Ratnavali&rsquo;?</p>",
                    question_hi: "<p>8. साहित्यिक कृति \'रत्नावली\' के लेखक कौन थे?</p>",
                    options_en: ["<p>Chanakya</p>", "<p>Shudraka</p>", 
                                "<p>Harshavardhana</p>", "<p>Kalidasa</p>"],
                    options_hi: ["<p>चाणक्य</p>", "<p>शूद्रक</p>",
                                "<p>हर्षवर्धन</p>", "<p>कालिदास</p>"],
                    solution_en: "<p>8.(c) <strong>Harshavardhana.</strong> Other books - &ldquo;Nagananda&rdquo;, &ldquo;Priyadarsika&rdquo;. <strong>Kalidas </strong>- &ldquo;Meghadūta&rdquo;, &ldquo;Abhijnana Shakuntalam&rdquo;, &ldquo;Malavikagnimitram&rdquo;, &ldquo;Vikramorvasiyam&rdquo;, &ldquo;Raghuvamsa&rdquo;, &ldquo;Kumarasambhava&rdquo;, &ldquo;Ritusamhara&rdquo;. <strong>Shudraka </strong>- &ldquo;Mricchakatika&rdquo;, &ldquo;Vinavasavadatta&rdquo;, &ldquo;Padmaprabhritaka&rdquo;. <strong>Chanakya </strong>- &ldquo;Arthashastra&rdquo;, &ldquo;Chanakya Niti&rdquo;.</p>",
                    solution_hi: "<p>8.(c) <strong>हर्षवर्धन</strong>। अन्य पुस्तकें - \"नागानंद\", \"प्रियदर्शिका\"। <strong>कालिदास </strong>- \"मेघदूत\", \"अभिज्ञान शकुंतलम\", \"मालविकाग्निमित्रम्\", \"विक्रमोर्वशीयम्\", \"रघुवंश\", \"कुमारसंभव\", \"ऋतुसंहार\"। <strong>शूद्रक </strong>- \"मृच्छकटिक\", \"विनवासवदत्ता\" \"पद्मप्रभृतक\"। <strong>चाणक्य </strong>- \"अर्थशास्त्र\", \"चाणक्य नीति\"।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "9",
                    section: "18",
                    question_en: "<p>9. &lsquo;Waiting for a visa&rsquo; is whose autobiography?</p>",
                    question_hi: "<p>9. &lsquo;वेटिंग फॉर वीज़ा\' किसकी आत्मकथा है?</p>",
                    options_en: ["<p>Indira Gandhi</p>", "<p>BR Ambedkar</p>", 
                                "<p>Satyajit Roy</p>", "<p>Jawaharlal Nehru</p>"],
                    options_hi: ["<p>इंदिरा गांधी</p>", "<p>बीआर अम्बेडकर</p>",
                                "<p>सत्यजीत रॉय</p>", "<p>जवाहर लाल नेहरू</p>"],
                    solution_en: "<p>9.(b) <strong>BR Ambedkar</strong> was a social reformer who served as chairman of the drafting committee of the constituent assembly. <strong>Famous autobiography by Indians -</strong> &ldquo;An Autobiography&rdquo; (Jawaharlal Nehru), &ldquo;Wings of Fire&rdquo; (Dr. A. P. J. Abdul Kalam), &ldquo;The Story of My Experiments with Truth&rdquo; (Mahatma Gandhi).</p>",
                    solution_hi: "<p>9.(b) <strong>बी.आर. अंबेडकर </strong>एक समाज सुधारक थे जिन्होंने संविधान सभा की मसौदा समिति के अध्यक्ष के रूप में कार्य किया। <strong>भारतीयों द्वारा प्रसिद्ध आत्मकथा -</strong> \"एन ऑटोबायोग्राफी\" (जवाहरलाल नेहरू), \"विंग्स ऑफ फायर\" (डॉ. ए.पी.जे. अब्दुल कलाम), \"द स्टोरी ऑफ माई एक्सपेरिमेंट्स विद ट्रुथ\" (महात्मा गांधी)।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "10",
                    section: "18",
                    question_en: "<p>10. The novel &lsquo;Tamas&rsquo; has been written by:</p>",
                    question_hi: "<p>10. उपन्यास \'तमस\' किसके द्वारा लिखा गया है:</p>",
                    options_en: ["<p>Khushwant Singh</p>", "<p>Amrita Pritam</p>", 
                                "<p>RK Narayan</p>", "<p>Bhisham Sahani</p>"],
                    options_hi: ["<p>खुशवंत सिंह</p>", "<p>अमृता प्रीतम</p>",
                                "<p>आर.के. नारायण</p>", "<p>भीष्म साहनी</p>"],
                    solution_en: "<p>10.(d) <strong>Bhisham Sahni :&nbsp;</strong>Six Hindi Novels - &ldquo;Jharokhe&rdquo;, &ldquo;Kadian&rdquo;, &ldquo;Basanti&rdquo;, &ldquo;Mayyadas Ki Madi&rdquo;, &ldquo;Kunto&rdquo;, and &ldquo;Neeloo&rdquo;<strong>. R.K.Narayan: Books -</strong> &ldquo;The Man-Eater of Malgudi \'\', &ldquo;Swami and Friends&rdquo;, &ldquo;The world of Nagaraj&rdquo;.<strong> Amrita Pritam :&nbsp;</strong>Books - \"Pinjar\", \"Kala Gulab\'\', \"Raseedi Ticket\", . <strong>Khushwant Singh: </strong>Books <strong>-</strong> \"Train to Pakistan\'\', \"The Company of Women\", \"Truth, Love and a Little Malice\", \"I Shall Not Hear the Nightingale\".</p>",
                    solution_hi: "<p>10.(d) <strong>भीष्म साहनी:</strong> छह हिंदी उपन्यास - \"झरोखे\", \"कड़ियाँ\", \"बसन्ती\", \"मय्यादास की माड़ी\", \"कुंतो\", और \"नीलू\"। <strong>आर.के.नारायण: पुस्तकें -</strong> \"द मैन-ईटर ऑफ मालगुडी\'\', \"स्वामी एंड फ्रेंड्स\", \"द वर्ल्ड ऑफ नागराज\"। <strong>अमृता प्रीतम:</strong> पुस्तकें - \"पिंजर\", \"काला गुलाब\", \"रसीदी टिकट\",\"। <strong>खुशवंत सिंह :</strong> पुस्तकें - \"ट्रेन टू पाकिस्तान\", \"द कंपनी ऑफ वुमेन\", \"ट्रुथ, लव एंड ए लिटिल\" मैलिस\", \"आई शैल नॉट हियर द नाइटिंगेल\"।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "11",
                    section: "18",
                    question_en: "<p>11. &lsquo;No Nation for Women&rsquo; is written by:</p>",
                    question_hi: "<p>11. &lsquo;No Nation for Women&rsquo; किसके द्वारा रचित है?</p>",
                    options_en: ["<p>Kishwar Desai</p>", "<p>Priyanka Dubey</p>", 
                                "<p>Indumati Desai</p>", "<p>Manav Kaul</p>"],
                    options_hi: ["<p>किश्वर देसाई</p>", "<p>प्रियंका दुबे</p>",
                                "<p>इंदुमती देसाई</p>", "<p>मानव कौल</p>"],
                    solution_en: "<p>11.(b)<strong> Priyanka Dubey</strong>. Books of <strong>Kishwar Desai</strong> - &ldquo;Witness the Night&rdquo;, &ldquo;Origins of Love&rdquo;, &ldquo;The Sea of Innocence&rdquo;, &ldquo;The Longest Kiss: The Life and Times of Devika Rani&rdquo;. <strong>Manav Kaul -</strong> Tumhare Baare Mein, A Night in the Hills, Rooh, Prema kabutar.</p>",
                    solution_hi: "<p>11.(b) <strong>प्रियंका दुबे। किश्वर देसाई की पुस्तकें -</strong> \"विटनेस द नाइट\", \"ओरिजिंस ऑफ लव\", \"द सी ऑफ इनोसेंस\", \"द लॉन्गेस्ट किस: द लाइफ एण्ड टाइम्स ऑफ देविका रानी\"। <strong>मानव कौल -</strong> तुम्हारे बारे में, ए नाइट इन द हिल्स, रूह, प्रेम कबूतर।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "12",
                    section: "18",
                    question_en: "<p>12. The novel : &lsquo;A Prime Minister to Remember&rsquo; was written by:</p>",
                    question_hi: "<p>12. उपन्यास: \'ए प्राइम मिनिस्टर टू रिमेम्बर\' किसके द्वारा लिखा गया था:</p>",
                    options_en: ["<p>Arundhati Roy</p>", "<p>Tavleen Singh</p>", 
                                "<p>Admiral Sushil Kumar</p>", "<p>Bipin Chandra Pal</p>"],
                    options_hi: ["<p>अरुंधति रॉय</p>", "<p>तवलीन सिंह</p>",
                                "<p>एडमिरल सुशील कुमार</p>", "<p>बिपिन चंद्र पाल</p>"],
                    solution_en: "<p>12.(c) <strong>Admiral Sushil Kumar.</strong> He was an Indian Navy Admiral who served as Chief of Naval Staff of the Indian Navy. <strong>Books and Authors -</strong> &ldquo;The India Story&rdquo; (Bimal Jalal), &ldquo;Listen to Your Heart&rdquo;, &ldquo;The London Adventure&rdquo; (Ruskin Bond), Pride, Prejudice and Punditry (Shashi Tharoor), &ldquo;Atal Bihari Vajpayee&rdquo; (Sagarika Ghose), Ambedkar: A Life&rdquo; (Shashi Tharoor), &ldquo;Spare&rdquo; (J. R. Moehringer).</p>",
                    solution_hi: "<p>12.(c) <strong>एडमिरल सुशील कुमार। </strong>वह एक भारतीय नौसेना एडमिरल थे जिन्होंने भारतीय नौसेना के नौसेना स्टाफ के प्रमुख के रूप में कार्य किया।<strong> पुस्तकें और लेखक -</strong> \"द इंडिया स्टोरी\" (बिमल जलाल), \"लिस्टेन टू योर हार्ट\", \"द लंदन एडवेंचर\" (रस्किन बॉन्ड), प्राइड, प्रेजुडिस एण्ड पंडित्री (शशि थरूर), \"अटल बिहारी वाजपेयी\" (सागरिका घोष) , अम्बेडकर: ए लाइफ\'\' (शशि थरूर), \'\'स्पेयर\'\' (J. R. मोहरिंगर)।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "13",
                    section: "18",
                    question_en: "<p>13. Who wrote the book &lsquo;Tahqiq ma lil-Hind&rsquo; ?</p>",
                    question_hi: "<p>13. पुस्तक \'तहक़ीक़ मा लिल-हिंद\'(Tahqiq ma lil-Hind) किसने लिखी है ?</p>",
                    options_en: ["<p>Al-Masudi</p>", "<p>Megasthenes</p>", 
                                "<p>Al-Biruni</p>", "<p>Ibn Batuta</p>"],
                    options_hi: ["<p>अल-मसूदी (Al-Masudi)</p>", "<p>मेगस्थनीज (Megasthenes)</p>",
                                "<p>अल-बरुनी (Al-Biruni)</p>", "<p>इब्न बतूता (Ibn Batuta)</p>"],
                    solution_en: "<p>13.(c)<strong> Al-Biruni :</strong> An Iranian scholar, who was contemporary to Mahmud of Ghazni. He translated Patanjali\'s work on grammar into Arabic. He also wrote \"Chronology of Ancient Nations\" (to determine the duration of various historical eras) and &ldquo;Tahqiq ma lil-Hind&rdquo; (History of India).</p>",
                    solution_hi: "<p>13.(c) <strong>अल-बरूनी :&nbsp;</strong>एक ईरानी विद्वान, जो महमूद गजनवी का समकालीन था। उन्होंने व्याकरण पर पतंजलि के कार्य का अरबी में अनुवाद किया। उन्होंने \"प्राचीन राष्ट्रों का कालक्रम\" (विभिन्न ऐतिहासिक युगों की अवधि निर्धारित करने के लिए) और \"तहक़ीक़ मा लिल-हिंद\" (भारत का इतिहास) भी लिखा।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "14",
                    section: "18",
                    question_en: "<p>14. Who wrote the famous poem &lsquo;Khoob Ladi Mardani&rsquo;?</p>",
                    question_hi: "<p>14. प्रसिद्ध कविता \'खूब लड़ी मर्दानी\' की रचना किसके द्वारा की गई थी?</p>",
                    options_en: ["<p>Subhadra Kumari Chauhan</p>", "<p>Sarojini Naidu</p>", 
                                "<p>MK Gandhi</p>", "<p>Sri Aurobindo</p>"],
                    options_hi: ["<p>सुभद्रा कुमारी चौहान</p>", "<p>सरोजिनी नायडू</p>",
                                "<p>एमके गांधी</p>", "<p>श्री अरबिंदो</p>"],
                    solution_en: "<p>14.(a) <strong>Subhadra Kumari Chauhan. </strong>&ldquo;Khoob Ladi Mardani&rdquo; - It narrates the valor of the Queen of Jhansi &lsquo;Rani LaxmiBai&rsquo;. Her other poems - &ldquo;Jallianwala Bagh mein Vasant&rdquo;, &ldquo;Veeron Ka Kaisa Ho Basant&rdquo;, &ldquo;Rakhi Ki Chunauti&rdquo;, and &ldquo;Vida&rdquo;. Notable Indians and their literary works : <strong>Sarojini Naidu</strong> (Bharatiya Kokila - The Nightingale of India) - &ldquo;Golden Threshold&rdquo;, &ldquo;In the Bazaars of Hyderabad&rdquo;.<strong> MK Gandhi -</strong> &ldquo;Hind Swaraj&rdquo; (first literary work), &ldquo;My experiments with the truth&rdquo;. Literary Works of <strong>Sri</strong> <strong>Aurobindo </strong>- &ldquo;Karmayogin&rdquo;, &ldquo;Essays on the Gita&rdquo;, &ldquo;The Life Divine&rdquo;.</p>",
                    solution_hi: "<p>14.(a) <strong>सुभद्रा कुमारी चौहान। \"खूब लड़ी मर्दानी\"</strong> - यह झाँसी की रानी \'रानी लक्ष्मीबाई\' की वीरता का वर्णन करती है। उनकी अन्य कविताएँ - \"जलियाँवाला बाग में वसंत\", \"वीरों का कैसा हो बसंत\", \"राखी की चुनौती\", और \"विदा\"। उल्लेखनीय भारतीय और उनकी साहित्यिक कृतियाँ: <strong>सरोजिनी नायडू</strong> (भारत कोकिला - द नाइटिंगेल ऑफ़ इंडिया) - \"गोल्डन थ्रेशोल्ड\", \"इन द बाज़ार्स ऑफ़ हैदराबाद\"।<strong> MK गांधी </strong>- \"हिंद स्वराज\" (पहली साहित्यिक कृति), \"माइ एक्सपेरीमेंट्स विथ द ट्रुथ\"। <strong>श्री अरबिंदो </strong>की साहित्यिक कृतियाँ - \"कर्मयोगिन\", \"एससेस ऑन द गीता\", \"द लाइफ डिवाइन\"।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "15",
                    section: "misc",
                    question_en: "<p>15. Who is the author of the book &lsquo;Why I Am a Hindu&rsquo;?</p>",
                    question_hi: "<p>15. \'व्हाई आई एम ए हिंदू\' पुस्तक के लेखक कौन हैं?</p>",
                    options_en: ["<p>Salman Rushdie</p>", "<p>Chetan Bhagat</p>", 
                                "<p>Shashi Tharoor</p>", "<p>Bhisham Sahni</p>"],
                    options_hi: ["<p>सलमान रुश्दी</p>", "<p>चेतन भगत</p>",
                                "<p>शशि थरूर</p>", "<p>भीष्म साहनी</p>"],
                    solution_en: "<p>15.(c) <strong>Shashi Tharoor. Other Important Books (Shashi Tharoor) -</strong> &lsquo;An Era of Darkness: The British Empire in India&rsquo; , &lsquo;The Hindu Way: An Introduction to Hinduism&rsquo; , &lsquo;Nehru: The Invention of India&rsquo; , &lsquo;The Paradoxical Prime Minister&rsquo;, &lsquo;The Battle of Belonging&rsquo;, &lsquo;The Great Indian Novel&rsquo;. <strong>Awards </strong>: Pravasi Bharatiya Samman, Sahitya Akademi (An Era of Darkness: The British Empire in India), Chevalier de la Legion d&rsquo;Honneur (France).</p>",
                    solution_hi: "<p>15.(c)<strong> शशि थरूर।</strong> <strong>अन्य महत्वपूर्ण पुस्तकें (शशि थरूर) - </strong>\'एन एरा ऑफ डार्कनेस: द ब्रिटिश एम्पायर इन इंडिया\', \'द हिंदू वे: एन इंट्रोडक्शन टू हिंदूइज्म\', \'नेहरू: द इन्वेंशन ऑफ इंडिया\', \'द पैराडॉक्सिकल प्राइम मिनिस्टर\', \'द बैटल ऑफ बिलॉन्गिंग\', \'द ग्रेट इंडियन नॉवेल\'। <strong>पुरस्कार</strong>: प्रवासी भारतीय सम्मान, साहित्य अकादमी (एन एरा ऑफ डार्कनेस: द ब्रिटिश एम्पायर इन इंडिया), शेवेलियर डे ला लीजन डी\'होनूर (फ्रांस)।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>