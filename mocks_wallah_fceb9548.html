<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #7209b7;
            --secondary: #4361ee;
            --accent: #f72585;
            --success: #06d6a0;
            --warning: #ffd166;
            --danger: #ef476f;
            --light: #f8f9fa;
            --dark: #212529;
        }
        body {
            background-color: #f0f2f5;
            font-size: 16px;
            padding-bottom: 60px; /* Space for fixed navigation */
        }
        .navbar {
            background: linear-gradient(135deg, var(--primary), var(--secondary)) !important;
            box-shadow: 0 2px 10px rgba(0,0,0,.1);
            padding: 0.5rem 1rem;
        }
        .navbar-brand {
            font-size: 1.1rem;
            margin-right: 0;
        }
        .navbar .container-fluid {
            justify-content: space-between;
        }
        .controls-group {
            display: flex;
            align-items: center;
            flex-wrap: nowrap;
        }
        .timer-display {
            background: linear-gradient(135deg, #4361ee, #3a0ca3);
            color: white;
            border-radius: 50px;
            box-shadow: 0 3px 8px rgba(0,0,0,.1);
            padding: 0.25rem 0.75rem;
            font-size: 0.875rem;
            white-space: nowrap;
        }
        .nav-btn {
            padding: 0.25rem 0.75rem;
            font-size: 0.875rem;
            margin-left: 0.25rem;
        }
        .question-nav {
            position: fixed;
            top: 56px;
            right: 0;
            height: calc(100vh - 56px);
            width: 280px;
            transform: translateX(280px);
            transition: transform .3s;
            z-index: 100;
            background: #fff;
            box-shadow: -5px 0 15px rgba(0,0,0,.1);
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .main-content {
            transition: margin .3s;
            min-height: calc(100vh - 116px);
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.5;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(32px, 1fr));
            gap: 6px;
        }
        .q-box {
            width: 35px;
            height: 35px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 5px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid #dee2e6;
            background: #fff;
            transition: all .2s;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: 0 3px 5px rgba(0,0,0,.1);
        }
        .q-box.attempted {
            background-color: var(--secondary);
            color: white;
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,.15);
        }
        .card {
            border-radius: 12px;
            overflow: hidden;
            border: none;
            box-shadow: 0 4px 12px rgba(0,0,0,.08);
        }
        .option-label {
            border-radius: 8px;
            transition: all .2s;
            border: 1px solid #dee2e6;
            padding: 0.75rem 1rem;
            font-size: 1rem;
        }
        .option-label:hover {
            background-color: #f8f9fa;
            transform: translateY(-2px);
            box-shadow: 0 3px 8px rgba(0,0,0,.05);
        }
        .form-check-input:checked + .option-label {
            border-color: var(--primary);
            background-color: rgba(114,9,183,.05);
        }
        .btn-primary {
            background-color: var(--primary);
            border-color: var(--primary);
        }
        .btn-outline-primary {
            color: var(--primary);
            border-color: var(--primary);
        }
        .btn-outline-primary:hover {
            background-color: var(--primary);
            color: white;
        }
        /* Bottom fixed navigation for mobile */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: white;
            box-shadow: 0 -2px 10px rgba(0,0,0,.1);
            padding: 10px 15px;
            display: flex;
            justify-content: space-between;
            z-index: 90;
        }
        @media (min-width: 992px) {
            .main-content.with-nav {
                margin-right: 280px;
            }
            .question-nav {
                transform: translateX(0);
            }
            .nav-toggle-btn {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(30px, 1fr));
            }
            .option-label {
                padding: 0.625rem 0.875rem;
                font-size: 0.95rem;
            }
            .question-text {
                font-size: 1rem;
            }
            .navbar-brand {
                font-size: 1rem;
            }
            .timer-display {
                font-size: 0.8125rem;
                padding: 0.2rem 0.6rem;
            }
            .nav-btn {
                padding: 0.2rem 0.6rem;
                font-size: 0.8125rem;
            }
            .container {
                padding-left: 10px;
                padding-right: 10px;
            }
        }
    </style>
</head>
<body>
    <!-- Header navbar -->
    <nav class="navbar navbar-dark sticky-top">
        <div class="container-fluid px-2">
            <a class="navbar-brand fw-bold" href="#"><i class="fas fa-graduation-cap me-1"></i></a>
            <div class="controls-group">
                <!-- Timer -->
                <div class="timer-display me-1 fw-bold" id="timer">
                    <i class="far fa-clock me-1"></i><span id="timer-display">12:00</span>
                </div>
                <!-- Submit button -->
                <button class="btn btn-light btn-sm nav-btn me-1" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Language toggle -->
                <button class="btn btn-light btn-sm nav-btn me-1" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="btn btn-light btn-sm nav-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </nav>
    <!-- Main content -->
    <div class="main-content" id="main-content">
        <div class="container py-3">
            <!-- Question counter -->
            <div class="fw-bold text-center mb-3" id="question-counter">Question 1 of 25</div>
            <!-- Questions will be displayed here -->
            <div id="questions-container"></div>
        </div>
    </div>
    <!-- Bottom navigation for mobile -->
    <div class="bottom-nav d-lg-none">
        <button class="btn btn-outline-primary px-3" onclick="prevQuestion()">
            <i class="fas fa-chevron-left me-1"></i> Previous
        </button>
        <button class="btn btn-outline-primary px-3" onclick="nextQuestion()">
            Next <i class="fas fa-chevron-right ms-1"></i>
        </button>
    </div>
    <!-- Question navigation sidebar -->
    <div class="question-nav" id="question-nav">
        <div class="p-3">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h5 class="m-0">Questions</h5>
                <div>
                    <span class="badge bg-secondary me-1" id="attempted-count">0</span>
                    <span class="badge bg-secondary" id="total-count">25</span>
                    <button class="btn btn-sm btn-outline-secondary ms-2 d-lg-none" onclick="toggleNav()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
            <!-- Section selector moved here -->
            <div class="mb-3">
                <label for="section-selector" class="form-label small text-muted">Jump to Section</label>
                <select class="form-select form-select-sm" id="section-selector" onchange="switchSection(this.value)">
                    <option value="all">All Sections</option>
                </select>
            </div>
            <div class="question-grid" id="question-boxes"></div>
        </div>
    </div>
    <!-- Results modal -->
    <div class="modal fade" id="results-modal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content border-0">
                <div class="modal-header border-0" style="background:linear-gradient(135deg,var(--primary),var(--secondary));color:white">
                    <h5 class="modal-title">Test Results</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body p-4">
                    <div class="row text-center g-2">
                        <div class="col-6">
                            <div class="p-3 rounded shadow-sm" style="background:linear-gradient(135deg,#4361ee,#3a0ca3);color:white">
                                <h3 id="score-value" class="mb-0 fw-bold">0</h3>
                                <div class="small">Score</div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="p-3 rounded shadow-sm" style="background:linear-gradient(135deg,#06d6a0,#1b9aaa);color:white">
                                <h3 id="correct-value" class="mb-0 fw-bold">0</h3>
                                <div class="small">Correct</div>
                            </div>
                        </div>
                        <div class="col-6 mt-2">
                            <div class="p-3 rounded shadow-sm" style="background:linear-gradient(135deg,#ef476f,#f72585);color:white">
                                <h3 id="incorrect-value" class="mb-0 fw-bold">0</h3>
                                <div class="small">Incorrect</div>
                            </div>
                        </div>
                        <div class="col-6 mt-2">
                            <div class="p-3 rounded shadow-sm" style="background:linear-gradient(135deg,#6c757d,#495057);color:white">
                                <h3 id="unattempted-value" class="mb-0 fw-bold">0</h3>
                                <div class="small">Unattempted</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer border-0">
                    <button type="button" class="btn btn-primary" onclick="reviewTest()">Review Test</button>
                </div>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 12 * 60;
        let resultsModal;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            resultsModal = new bootstrap.Modal(document.getElementById('results-modal'));
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["7"] = {
                name: "English",
                start: 0,
                end: 23
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="7">English</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 24,
                end: 24
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "7",
                    question_en: " <p>1.Select the most appropriate meaning of given idiom</span></p> <p><span style=\"font-family:Times New Roman\">False colors</span></p>",
                    question_hi: "",
                    options_en: [" <p> Very high price</span></p>", " <p> To flatter</span></p>", 
                                " <p> To overlook</span></p>", " <p> False pretenses</span></p>"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: " <p><span style=\"font-family:Times New Roman\">(d) False pretenses</span></p>",
                    solution_hi: " <p><span style=\"font-family:Times New Roman\">(d) False pretenses</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "7",
                    question_en: " <p>2.Select the active form of  the given sentence</span></p> <p><span style=\"font-family:Times New Roman\">Physically challenged people should not be laughed at by the public.</span></p>",
                    question_hi: "",
                    options_en: [" <p> Physically challenged people should not laugh at the public. </span></p>", " <p> The public will not be laughing at physically challenged people.</span></p>", 
                                " <p> The public shall not be laughing at physically challenged people.</span></p>", " <p> The public should not laugh at physically challenged people.</span></p>"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: " <p><span style=\"font-family:Times New Roman\">(d) </span></p> <p><span style=\"font-family:Times New Roman\">a. Physically challenged people should not laugh at the public. (Meaning of the sentence completely changed)</span></p> <p><span style=\"font-family:Times New Roman\">b.The public will not be laughing at physically challenged people. (Tense changed)</span></p> <p><span style=\"font-family:Times New Roman\">c.The public shall not be laughing at physically challenged people. (Tense changed)</span></p> <p><span style=\"font-family:Times New Roman\">d.The public should not laugh at physically challenged people. (correct)</span></p>",
                    solution_hi: " <p><span style=\"font-family:Times New Roman\">(d) </span></p> <p><span style=\"font-family:Times New Roman\">a. Physically challenged people should not laugh at the public. (Meaning of the sentence completely changed)</span></p> <p><span style=\"font-family:Times New Roman\">b.The public will not be laughing at physically challenged people. (Tense changed)</span></p> <p><span style=\"font-family:Times New Roman\">c.The public shall not be laughing at physically challenged people. (Tense changed)</span></p> <p><span style=\"font-family:Times New Roman\">d.The public should not laugh at physically challenged people. (correct)</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "7",
                    question_en: " <p>3.Select the antonym of the following word</span></p> <p><span style=\"font-family:Times New Roman\">NUGATORY</span></p>",
                    question_hi: "",
                    options_en: [" <p>voluptuous</span></p>", " <p>slender</span></p>", 
                                " <p>valuable      </span></p>", " <p>worthless</span></p>"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: " <p><span style=\"font-family:Times New Roman\">(c) Nugatory-of no value or importance</span></p> <p><span style=\"font-family:Times New Roman\">Slender-gracefully thin</span></p> <p><span style=\"font-family:Times New Roman\">Voluptuous-curvaceous and sexually attractive</span></p> <p><span style=\"font-family:Times New Roman\">Valuable- </span><span style=\"font-family:Times New Roman\">worth a lot of money</span></p>",
                    solution_hi: " <p><span style=\"font-family:Times New Roman\">(c) Nugatory-of no value or importance</span></p> <p><span style=\"font-family:Times New Roman\">Slender-gracefully thin</span></p> <p><span style=\"font-family:Times New Roman\">Voluptuous-curvaceous and sexually attractive</span></p> <p><span style=\"font-family:Times New Roman\">Valuable- </span><span style=\"font-family:Times New Roman\">worth a lot of money</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "7",
                    question_en: " <p>4.Given are jumbled sentences.Select the option that gives their correct order</span></p> <p><span style=\"font-family:Times New Roman\">1. Local industries often</span></p> <p><span style=\"font-family:Times New Roman\">P. protest the high salaries</span></p> <p><span style=\"font-family:Times New Roman\">Q that this will unreasonably raise</span></p> <p><span style=\"font-family:Times New Roman\">R. arguing vehemently</span></p> <p><span style=\"font-family:Times New Roman\">S. offered by multinational firms</span></p> <p><span style=\"font-family:Times New Roman\">6. all wages to an excessive level.</span></p>",
                    question_hi: "",
                    options_en: [" <p> RQPS</span></p>", " <p> PSRQ</span></p>", 
                                " <p> SRQP</span></p>", " <p> PRSQ</span></p>"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: " <p><span style=\"font-family:Times New Roman\">(b) PSRQ</span></p> <p><span style=\"font-family:Times New Roman\">Sentence P mentions what local industries do & Sentence S mentions what local industries protest. So, S will follow P. Further, sentences R and Q mentions why they protest. So, Q will follow R. Going through the options, option b has the correct sequence.</span></p>",
                    solution_hi: " <p><span style=\"font-family:Times New Roman\">(b) PSRQ</span></p> <p><span style=\"font-family:Times New Roman\">Sentence P mentions what local industries do & Sentence S mentions what local industries protest. So, S will follow P. Further, sentences R and Q mentions why they protest. So, Q will follow R. Going through the options, option b has the correct sequence.</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "7",
                    question_en: " <p>5.Select the group of words which means the same as the word given</span></p> <p><span style=\"font-family:Times New Roman\">Heath</span></p>",
                    question_hi: "",
                    options_en: [" <p> Large scale destruction by fire</span></p>", " <p> an area of open uncultivated land</span></p>", 
                                " <p> An immoral person </span></p>", " <p> A person who gambles a lot</span></p>"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: " <p><span style=\"font-family:Times New Roman\">(b) Heath- an area of open uncultivated land</span></p>",
                    solution_hi: " <p><span style=\"font-family:Times New Roman\">(b) Heath- an area of open uncultivated land</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "7",
                    question_en: "<p>6.Select the word which means the same as the group of words given</p>\r\n<p><span style=\"font-family: Times New Roman;\">Persons living at the same time</span></p>",
                    question_hi: "",
                    options_en: ["<p>cosmopolitans</p>", "<p>compatriots</p>", 
                                "<p>colleagues</p>", "<p>contemporaries</p>"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: "<p><span style=\"font-family: Times New Roman;\">(d) Contemporaries- Persons living at the same time</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Cosmopolitans-including people from many different countries</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Compatriots- </span><span style=\"font-family: Times New Roman;\">a person who comes from the same country as you</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Colleagues- </span><span style=\"font-family: Times New Roman;\">a person who works at the same place as you</span></p>",
                    solution_hi: "<p><span style=\"font-family: Times New Roman;\">(d) Contemporaries- Persons living at the same time</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Cosmopolitans-including people from many different countries</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Compatriots- </span><span style=\"font-family: Times New Roman;\">a person who comes from the same country as you</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Colleagues- </span><span style=\"font-family: Times New Roman;\">a person who works at the same place as you</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "7",
                    question_en: "<p>7. <span style=\"font-family: Times New Roman;\">Directions (7-11): In the following passage word have been deleted.Fill in the blanks with the help of the alternatives given.Select the most appropriate option for each blank</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Children are loved by all human beings.But (7)......this world of human (8)..........there is no (9).......nuisance.than a boy (10).......the age of fourteen.He is neither ornamental (11)........useful.</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Select the most appropriate option for blank no. 7.</span></p>",
                    question_hi: "",
                    options_en: ["<p>of</p>", "<p>on<span style=\"font-family: Times New Roman;\"> </span></p>", 
                                "<p>for<span style=\"font-family: Times New Roman;\"> </span></p>", "<p>in</p>"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: "<p><span style=\"font-family: Times New Roman;\">(d) We live in this world. Hence, &lsquo;in&rsquo; is the most appropriate answer.</span></p>",
                    solution_hi: "<p><span style=\"font-family: Times New Roman;\">(d) We live in this world. Hence, &lsquo;in&rsquo; is the most appropriate answer.</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "7",
                    question_en: "<p>8. <span style=\"font-family: Times New Roman;\">Directions (7-11): In the following passage word have been deleted.Fill in the blanks with the help of the alternatives given.Select the most appropriate option for each blank</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Children are loved by all human beings.But (7)......this world of human (8)..........there is no (9).......nuisance.than a boy (10).......the age of fourteen.He is neither ornamental (11)........useful.</span></p>\r\n<p><span style=\"font-family: \'times new roman\', times, serif;\">Select the most appropriate option for blank no. 8.</span></p>",
                    question_hi: "",
                    options_en: ["<p>affairs</p>", "<p>life<span style=\"font-family: Times New Roman;\"> </span></p>", 
                                "<p>beings<span style=\"font-family: Times New Roman;\"> </span></p>", "<p>world</p>"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: "<p><span style=\"font-family: Times New Roman;\">(c) We generally use &lsquo;beings&rsquo; with &lsquo;human&rsquo;. Hence, &lsquo;beings&rsquo; is the most appropriate answer.</span></p>",
                    solution_hi: "<p><span style=\"font-family: Times New Roman;\">(c) We generally use &lsquo;beings&rsquo; with &lsquo;human&rsquo;. Hence, &lsquo;beings&rsquo; is the most appropriate answer.</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "7",
                    question_en: "<p>9. <span style=\"font-family: Times New Roman;\">Directions (7-11): In the following passage word have been deleted.Fill in the blanks with the help of the alternatives given.Select the most appropriate option for each blank</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Children are loved by all human beings.But (7)......this world of human (8)..........there is no (9).......nuisance.than a boy (10).......the age of fourteen.He is neither ornamental (11)........useful.</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Select the most appropriate option for blank no. 9.</span></p>",
                    question_hi: "",
                    options_en: ["<p>worst</p>", "<p>bad<span style=\"font-family: Times New Roman;\"> </span></p>", 
                                "<p>worse<span style=\"font-family: Times New Roman;\"> </span></p>", "<p>better</p>"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: "<p><span style=\"font-family: Times New Roman;\">(c) Worse means not good and it is used to compare two things as no other nuisance and a boy of the age of fourteen is compared in the passage. Hence, &lsquo;worse&rsquo; is the most appropriate answer.</span></p>",
                    solution_hi: "<p><span style=\"font-family: Times New Roman;\">(c) Worse means not good and it is used to compare two things as no other nuisance and a boy of the age of fourteen is compared in the passage. Hence, &lsquo;worse&rsquo; is the most appropriate answer.</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "7",
                    question_en: "<p>10. <span style=\"font-family: Times New Roman;\">Directions (7-11): In the following passage word have been deleted.Fill in the blanks with the help of the alternatives given.Select the most appropriate option for each blank</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Children are loved by all human beings.But (7)......this world of human (8)..........there is no (9).......nuisance.than a boy (10).......the age of fourteen.He is neither ornamental (11)........useful.</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Select the most appropriate option for blank no. 10.</span></p>",
                    question_hi: "",
                    options_en: ["<p>at</p>", "<p>of<span style=\"font-family: Times New Roman;\"> </span></p>", 
                                "<p>on<span style=\"font-family: Times New Roman;\"> </span></p>", "<p>in</p>"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: "<p><span style=\"font-family: Times New Roman;\">(b) All the other options except &lsquo;of&rsquo; do not fit in the context of the passage because we generally say the age of thirty, forty, etc. Hence, &lsquo;of&rsquo; is the most appropriate answer.</span></p>",
                    solution_hi: "<p><span style=\"font-family: Times New Roman;\">(b) All the other options except &lsquo;of&rsquo; do not fit in the context of the passage because we generally say the age of thirty, forty, etc. Hence, &lsquo;of&rsquo; is the most appropriate answer.</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "7",
                    question_en: "<p>11. <span style=\"font-family: Times New Roman;\">Directions (7-11): In the following passage word have been deleted.Fill in the blanks with the help of the alternatives given.Select the most appropriate option for each blank</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Children are loved by all human beings.But (7)......this world of human (8)..........there is no (9).......nuisance.than a boy (10).......the age of fourteen.He is neither ornamental (11)........useful.</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Select the most appropriate option for blank no. 11.</span></p>",
                    question_hi: "",
                    options_en: ["<p>and</p>", "<p>or<span style=\"font-family: Times New Roman;\"> </span></p>", 
                                "<p>nor<span style=\"font-family: Times New Roman;\"> </span></p>", "<p>so</p>"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: "<p><span style=\"font-family: Times New Roman;\">(c) Neither is used earlier in the sentence and &lsquo;Neither-nor&rsquo; is a fixed conjunction pair. Hence, &lsquo;nor&rsquo; is the most appropriate answer.</span></p>",
                    solution_hi: "<p><span style=\"font-family: Times New Roman;\">(c) Neither is used earlier in the sentence and &lsquo;Neither-nor&rsquo; is a fixed conjunction pair. Hence, &lsquo;nor&rsquo; is the most appropriate answer.</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "7",
                    question_en: "<p>12.<span style=\"font-family: \'times new roman\', times, serif;\">Select the most appropriate option to substitute the underlined segment in the given sentence. If no substitution is required , Select \'no improvement\'.&nbsp;</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">In the party she spoke to me </span><span style=\"text-decoration: underline;\"><span style=\"font-family: Times New Roman;\">very friendly.</span></span></p>",
                    question_hi: "",
                    options_en: ["<p>in very friendly manner<span style=\"font-family: Times New Roman;\"> </span></p>", "<p>with very friendly manner</p>", 
                                "<p>in a friendly manner<span style=\"font-family: Times New Roman;\"> </span></p>", "<p>no improvement</p>"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: "<p><span style=\"font-family: Times New Roman;\">(c) We will place &lsquo;manner&rsquo; after the adverb &lsquo;friendly&rsquo; to grammatically correct the sentence. Hence, &lsquo;in a friendly manner&rsquo; is the most appropriate answer.</span></p>",
                    solution_hi: "<p><span style=\"font-family: Times New Roman;\">(c) We will place &lsquo;manner&rsquo; after the adverb &lsquo;friendly&rsquo; to grammatically correct the sentence. Hence, &lsquo;in a friendly manner&rsquo; is the most appropriate answer.</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "7",
                    question_en: "<p>13.Select the most appropriate option to substitute the underlined segment in the given sentence. If no substitution is required ,select no improvement</p>\r\n<p><span style=\"font-family: Times New Roman;\">My opinion </span><span style=\"text-decoration: underline;\"><span style=\"font-family: Times New Roman;\">for</span></span><span style=\"font-family: Times New Roman;\"> the film is that it will bag the national award.</span></p>",
                    question_hi: "",
                    options_en: ["<p>on</p>", "<p>to<span style=\"font-family: Times New Roman;\"> </span></p>", 
                                "<p>about<span style=\"font-family: Times New Roman;\"> </span></p>", "<p>no improvement</p>"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: "<p><span style=\"font-family: Times New Roman;\">(c) We will replace &lsquo;for&rsquo; with &lsquo;about&rsquo; to grammatically correct the sentence. Hence, &lsquo;about&rsquo; is the most appropriate answer.</span></p>",
                    solution_hi: "<p><span style=\"font-family: Times New Roman;\">(c) We will replace &lsquo;for&rsquo; with &lsquo;about&rsquo; to grammatically correct the sentence. Hence, &lsquo;about&rsquo; is the most appropriate answer.</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "7",
                    question_en: " <p>14.Select the most appropriate option to fill in the blanks</span></p> <p><span style=\"font-family:Times New Roman\">It was too hot so some volunteers handed______ fresh lime soda to everyone.</span></p>",
                    question_hi: "",
                    options_en: [" <p> on</span><span style=\"font-family:Times New Roman\">    </span></p>", " <p> out</span><span style=\"font-family:Times New Roman\">   </span></p>", 
                                " <p> around</span><span style=\"font-family:Times New Roman\">    </span></p>", " <p> over</span></p>"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: " <p><span style=\"font-family:Times New Roman\">(c) ‘Hand around’ means to offer around things like drinks/tea/biscuits, you give one to each person in a group as volunteers did in the given sentence. Hence, ‘around’ is the most appropriate answer.</span></p>",
                    solution_hi: " <p><span style=\"font-family:Times New Roman\">(c) ‘Hand around’ means to offer around things like drinks/tea/biscuits, you give one to each person in a group as volunteers did in the given sentence. Hence, ‘around’ is the most appropriate answer.</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "7",
                    question_en: " <p>15.choose the correct meaning of given idiom.</span></p> <p><span style=\"font-family:Times New Roman\">Have someone in one\'s spell </span></p>",
                    question_hi: "",
                    options_en: [" <p> to have enchanted or captivated the attention of someone</span></p>", " <p> To deceive</span></p>", 
                                " <p> To ignore</span></p>", " <p> To treat someone in they way he treated you</span></p>"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: " <p><span style=\"font-family:Times New Roman\">(a) To have enchanted or captivated the attention of someone.</span></p>",
                    solution_hi: " <p><span style=\"font-family:Times New Roman\">(a) To have enchanted or captivated the attention of someone.</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "7",
                    question_en: " <p>16.Select the most appropriate option to fill in the blanks.</span></p> <p><span style=\"font-family:Times New Roman\">Our business is finally coming ____after a lot of ups and downs.</span></p>",
                    question_hi: "",
                    options_en: [" <p> along</span></p>", " <p> across</span></p>", 
                                " <p> apart</span></p>", " <p> in</span></p>"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: " <p><span style=\"font-family:Times New Roman\">(a) ‘Come along’ means to make progress or to improve. The given sentence states that their business is finally making progress. Hence, ‘along’ is the most appropriate answer.</span></p>",
                    solution_hi: " <p><span style=\"font-family:Times New Roman\">(a) ‘Come along’ means to make progress or to improve. The given sentence states that their business is finally making progress. Hence, ‘along’ is the most appropriate answer.</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "7",
                    question_en: " <p>17.In the sentence identify the segment that contains grammatical error.</span></p> <p><span style=\"font-family:Times New Roman\">The data shows that the unemployment rate has raised to 7 per cent the highest in five years. </span></p>",
                    question_hi: "",
                    options_en: [" <p>The data shows that</span></p>", " <p>the unemployment rate has raised to</span></p>", 
                                " <p>7 per cent the highest in five years.</span></p>", " <p>No error</span></p>"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: " <p><span style=\"font-family:Times New Roman\">(b) “Has/Have +  V</span><span style=\"font-family:Times New Roman\">3</span><span style=\"font-family:Times New Roman\">(risen)” is grammatically the correct structure for the given sentence. Hence, ‘the unemployment rate has risen to’ is the most appropriate answer.</span></p>",
                    solution_hi: " <p><span style=\"font-family:Times New Roman\">(b) “Has/Have +  V</span><span style=\"font-family:Times New Roman\">3</span><span style=\"font-family:Times New Roman\">(risen)” is grammatically the correct structure for the given sentence. Hence, ‘the unemployment rate has risen to’ is the most appropriate answer.</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "7",
                    question_en: " <p>18.Select the synonym of the following word</span></p> <p><span style=\"font-family:Times New Roman\">GAMBIT</span></p>",
                    question_hi: "",
                    options_en: [" <p>expression</span></p>", " <p>trick</span><span style=\"font-family:Times New Roman\">      </span></p>", 
                                " <p>explanation    </span></p>", " <p>appeal</span></p>"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: " <p><span style=\"font-family:Times New Roman\">(b) Gambit- trick</span></p> <p><span style=\"font-family:Times New Roman\">Expression- something that you say that shows your feelings </span></p> <p><span style=\"font-family:Times New Roman\">Appeal- </span><span style=\"font-family:Times New Roman\">to make a serious request for something you need or want very much</span></p>",
                    solution_hi: " <p><span style=\"font-family:Times New Roman\">(b) Gambit- trick</span></p> <p><span style=\"font-family:Times New Roman\">Expression- something that you say that shows your feelings </span></p> <p><span style=\"font-family:Times New Roman\">Appeal- </span><span style=\"font-family:Times New Roman\">to make a serious request for something you need or want very much</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "7",
                    question_en: "<p>19.Select the correctly spelt word</p>\n",
                    question_hi: "",
                    options_en: ["<p>mountanious</p>\n", "<p>mountaneous</p>\n", 
                                "<p>mountainous</p>\n", "<p>mountanous</p>\n"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: "<p><span style=\"font-family: Times New Roman;\">(c) Mountainous is the correct spelling</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Mountainous - having many mountains</span></p>\r\n<p>&nbsp;</p>\n",
                    solution_hi: "<p><span style=\"font-family: Times New Roman;\">(c) Mountainous is the correct spelling</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Mountainous - having many mountains</span></p>\r\n<p>&nbsp;</p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "7",
                    question_en: " <p>20.Select the antonym of the following word</span></p> <p><span style=\"font-family:Times New Roman\">DESPONDENCY</span></p>",
                    question_hi: "",
                    options_en: [" <p>humility      </span></p>", " <p>pleasure</span><span style=\"font-family:Times New Roman\">  </span></p>", 
                                " <p>cheerfulness</span><span style=\"font-family:Times New Roman\">    </span></p>", " <p>excitement</span></p>"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: " <p><span style=\"font-family:Times New Roman\">(c) Despondency- </span><span style=\"font-family:Times New Roman\">the state of being despondent or extremely low in spirits</span></p> <p><span style=\"font-family:Times New Roman\">Cheerfulness- </span><span style=\"font-family:Times New Roman\">the state of being happy and positive:</span></p> <p><span style=\"font-family:Times New Roman\">Humility- </span><span style=\"font-family:Times New Roman\">the quality of not thinking that you are better than other people</span></p>",
                    solution_hi: " <p><span style=\"font-family:Times New Roman\">(c) Despondency- </span><span style=\"font-family:Times New Roman\">the state of being despondent or extremely low in spirits</span></p> <p><span style=\"font-family:Times New Roman\">Cheerfulness- </span><span style=\"font-family:Times New Roman\">the state of being happy and positive:</span></p> <p><span style=\"font-family:Times New Roman\">Humility- </span><span style=\"font-family:Times New Roman\">the quality of not thinking that you are better than other people</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "7",
                    question_en: "<p>21.Select the passive form of the given sentence</p>\r\n<p><span style=\"font-family: Times New Roman;\">A constable arrested him as soon as he jumped the wall.</span></p>",
                    question_hi: "",
                    options_en: ["<p>An arrest were made by a constable as soon as he jumped the wall.</p>", "<p>He was arrested by a constable as soon as he jumped the wall.</p>", 
                                "<p>A constable was arrested by him as soon as he jumped the wall.</p>", "<p>An arrest was made by him as soon as he jumped the wall.</p>"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: "<p><span style=\"font-family: Times New Roman;\">(b)</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">a. An arrest was made by a constable as soon as he jumped the wall. (Incorrect verb)</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">b.He was arrested by a constable as soon as he jumped the wall. (Correct)</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">c. A constable was arrested by him as soon as he jumped the wall. (Meaning has changed)</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">d.An arrest was made by him as soon as he jumped the wall. (Meaning changed)</span></p>",
                    solution_hi: "<p><span style=\"font-family: Times New Roman;\">(b)</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">a. An arrest was made by a constable as soon as he jumped the wall. (Incorrect verb)</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">b.He was arrested by a constable as soon as he jumped the wall. (Correct)</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">c. A constable was arrested by him as soon as he jumped the wall. (Meaning has changed)</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">d.An arrest was made by him as soon as he jumped the wall. (Meaning changed)</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "7",
                    question_en: " <p>22.Select the correctly spelt word</span></p>",
                    question_hi: "",
                    options_en: [" <p>jewelery</span></p>", " <p>jewellery</span></p>", 
                                " <p>jwelllry      </span></p>", " <p>jewllery</span></p>"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: " <p><span style=\"font-family:Times New Roman\">(b) Jewellery is the correct spelling</span></p>",
                    solution_hi: " <p><span style=\"font-family:Times New Roman\">(b) Jewellery is the correct spelling</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "23",
                    section: "7",
                    question_en: " <p>23.Select the synonym of the following word</span></p> <p><span style=\"font-family:Times New Roman\">JEREMIAD</span></p>",
                    question_hi: "",
                    options_en: [" <p>friction</span></p>", " <p>incident</span></p>", 
                                " <p>trouble        </span></p>", " <p>accident</span></p>"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: " <p><span style=\"font-family:Times New Roman\">(c) Jeremiad- trouble</span></p> <p><span style=\"font-family:Times New Roman\">Friction- </span><span style=\"font-family:Times New Roman\">disagreement between people or groups</span></p>",
                    solution_hi: " <p><span style=\"font-family:Times New Roman\">(c) Jeremiad- trouble</span></p> <p><span style=\"font-family:Times New Roman\">Friction- </span><span style=\"font-family:Times New Roman\">disagreement between people or groups</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "24",
                    section: "7",
                    question_en: " <p>24.In the sentence identify the segment that contains grammatical error.</span></p> <p><span style=\"font-family:Times New Roman\">He should mix his authority with little of kindness and common sense </span></p>",
                    question_hi: "",
                    options_en: [" <p>He should mix</span></p>", " <p>his authority with little</span></p>", 
                                " <p>of kindness and common sense   </span></p>", " <p>No error</span></p>"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: " <p><span style=\"font-family:Times New Roman\">(b) We will use ‘a little’ in place of ‘little’ as the sentence is positive. Hence, ‘his authority with a little’ is the most appropriate answer.</span></p>",
                    solution_hi: " <p><span style=\"font-family:Times New Roman\">(b) We will use ‘a little’ in place of ‘little’ as the sentence is positive. Hence, ‘his authority with a little’ is the most appropriate answer.</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "25",
                    section: "misc",
                    question_en: " <p>25.Given are jumbled sentences.Select the option that gives their correct order</span></p> <p><span style=\"font-family:Times New Roman\">1. In order to judge the inside of others, study your own</span></p> <p><span style=\"font-family:Times New Roman\">P. and though one has one prevailing passion</span></p> <p><span style=\"font-family:Times New Roman\">Q for, men, in general are very much alike</span></p> <p><span style=\"font-family:Times New Roman\">R. yet their operations are very much the same</span></p> <p><span style=\"font-family:Times New Roman\">S. and another has another</span></p> <p><span style=\"font-family:Times New Roman\">6. and whatever engages or disgusts, pleases or offends you in others, will engage, disgust, please or offend others in you.</span></p>",
                    question_hi: "",
                    options_en: [" <p> QPSR</span></p>", " <p> PQRS</span></p>", 
                                " <p> RQPS</span></p>", " <p> PRQS</span></p>"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: " <p><span style=\"font-family:Times New Roman\">(a) QPSR</span></p> <p><span style=\"font-family:Times New Roman\">Sentence Q mentions men are alike in some way & Sentence P mentions though their passions might be different. So, P will follow Q Further, Sentences S and R mentions their way of executing is somewhat similar. Going through the options, option a has the correct sequence.</span></p>",
                    solution_hi: " <p><span style=\"font-family:Times New Roman\">(a) QPSR</span></p> <p><span style=\"font-family:Times New Roman\">Sentence Q mentions men are alike in some way & Sentence P mentions though their passions might be different. So, P will follow Q Further, Sentences S and R mentions their way of executing is somewhat similar. Going through the options, option a has the correct sequence.</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML - more compact
            let html = `
                <div class="card mb-3">
                    <div class="card-body">
                        <div class="question-text mb-3">
                            <div class="en">${question.question_en}</div>
                            <div class="hi" style="display:none">${question.question_hi}</div>
                        </div>
                        <div class="options">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = 'option-label';
                // When reviewing, highlight user's choice and correct answer
                if (submitted) {
                    if (opt === question.correct) {
                        optionClass += ' border-success bg-success bg-opacity-10';
                    }
                    if (answers[question.id] === opt && opt !== question.correct) {
                        optionClass += ' border-danger bg-danger bg-opacity-10';
                    }
                }
                html += `
                    <div class="form-check mb-2">
                        <input class="form-check-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${answers[question.id] === opt ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                        <label class="form-check-label w-100 ${optionClass}" for="opt-${index}-${opt}">
                            ${submitted && opt === question.correct ? '<i class="fas fa-check-circle text-success me-1"></i>' : ''}
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const alertClass = isCorrect ? 'alert-success' : 'alert-danger';
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="alert ${alertClass} mt-3 py-2">
                        <strong>${resultText}</strong>
                        <div class="mt-1 small">
                            <strong>Solution:</strong>
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                document.getElementById(`box-${index}`).classList.add('attempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { if (currentQuestion > 0) showQuestion(currentQuestion - 1); }
        function nextQuestion() { if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
            // Update button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const content = document.getElementById('main-content');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                if (window.innerWidth >= 992) {
                    content.classList.add('with-nav');
                }
            } else {
                nav.classList.remove('show');
                if (window.innerWidth >= 992) {
                    content.classList.remove('with-nav');
                }
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer - compact
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            if (confirm(`You have attempted ${attempted} of ${questions.length} questions.
${remaining} questions are unattempted.
Are you sure you want to submit?`)) {
                submitTest();
            }
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('bg-secondary', 'text-white');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            resultsModal.show();
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.classList.replace('btn-light', 'btn-success');
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            resultsModal.hide();
            showQuestion(0);
        }
        // Add event listener for window resize
        window.addEventListener('resize', function() {
            if (window.innerWidth >= 992) {
                if (document.getElementById('question-nav').classList.contains('show')) {
                    document.getElementById('main-content').classList.add('with-nav');
                }
            } else {
                document.getElementById('main-content').classList.remove('with-nav');
            }
        });
    </script>
</body>
</html>