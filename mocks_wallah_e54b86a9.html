<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">25:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 40</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">40</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 25 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["18"] = {
                name: "General Awareness",
                start: 0,
                end: 38
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="18">General Awareness</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 39,
                end: 39
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "18",
                    question_en: "<p>1. What is Sukanya Samriddhi Yojana ?</p>",
                    question_hi: "<p>1. सुकन्या समृद्धि योजना क्या है ?</p>",
                    options_en: ["<p>A scheme to provide bicycles for girl studying in the 10th class.</p>", "<p>A scheme to develop self- defence skill in girl</p>", 
                                "<p>A scheme to provide skill that give employability to women.</p>", "<p>A small deposit scheme for girl child.</p>"],
                    options_hi: ["<p>10वीं कक्षा में पढ़ने वाली लड़कियों के लिए साइकिल उपलब्ध करवाने की योजना।</p>", "<p>लड़कियों में आत्मरक्षा कौशल विकसित करने की योजना</p>",
                                "<p>महिलाओं को रोजगार देने वाले कौशल प्रदान करने की योजना।</p>", "<p>बालिकाओं के लिए एक लघु जमा योजना</p>"],
                    solution_en: "<p>1.(d) Sukanya Samriddhi Yojana is a small deposit scheme for girl children launched in the year 2015. It is a government of India backed saving scheme targeted at the parents of girl children. The scheme encourages parents to build a fund for the future education and marriage expenses for their female child.</p>",
                    solution_hi: "<p>1.(d) सुकन्या समृद्धि योजना वर्ष 2015 में शुरू की गई बालिकाओं के लिए एक छोटी जमा योजना है। यह भारत सरकार समर्थित बचत योजना है जो बालिकाओं के माता-पिता को लक्षित करती है। यह योजना माता-पिता को उनकी बेटी के भविष्य की शिक्षा और शादी के खर्च के लिए एक फंड बनाने के लिए प्रोत्साहित करती है।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "2",
                    section: "18",
                    question_en: "<p>2. Which of the following countries is NOT a member of BIMSTEC ?</p>",
                    question_hi: "<p>2. निम्नलिखित में से कौन सा देश BIMSTEC का सदस्य नहीं है ?</p>",
                    options_en: ["<p>Maldives</p>", "<p>India</p>", 
                                "<p>Nepal</p>", "<p>Bhutan</p>"],
                    options_hi: ["<p>मालदीव</p>", "<p>भारत</p>",
                                "<p>नेपाल</p>", "<p>भूटान</p>"],
                    solution_en: "<p>2.(a) Maldives is not a member country of BIMSTEC. The Bay of Bengal Initiative for Multi-Sectoral Technical and Economic Cooperation is an international organisation of seven south Asian countries, was established on June 6, 1997. The BIMSTEC member states &ndash; Bangladesh, Bhutan, India, Myanmar, Nepal, Sri Lanka, and Thailand.</p>",
                    solution_hi: "<p>2.(a) मालदीव BIMSTEC का सदस्य देश नहीं है। बंगाल की खाड़ी बहु-क्षेत्रीय तकनीकी और आर्थिक सहयोग पहल सात दक्षिण एशियाई देशों का एक अंतरराष्ट्रीय संगठन है, जिसकी स्थापना 6 जून 1997 को हुई थी। BIMSTEC सदस्य देश - बांग्लादेश, भूटान, भारत, म्यांमार, नेपाल, श्रीलंका और थाईलैंड।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "3",
                    section: "18",
                    question_en: "<p>3. Which of the following states is NOT a member of &lsquo;Seven Sisters&lsquo; states of North-East India ?</p>",
                    question_hi: "<p>3. निम्नलिखित में से कौन सा राज्य उत्तर-पूर्वी भारत के \'सात बहन&rsquo;\' राज्यों की सदस्य नहीं है ?</p>",
                    options_en: ["<p>Tripura</p>", "<p>Mizoram</p>", 
                                "<p>Meghalaya</p>", "<p>Sikkim</p>"],
                    options_hi: ["<p>त्रिपुरा</p>", "<p>मिजोरम</p>",
                                "<p>मेघालय</p>", "<p>सिक्किम</p>"],
                    solution_en: "<p>3.(d) Sikkim is not a member of the &lsquo;Seven Sisters&lsquo; states of North-East India. North-East India is the easternmost region of India representing both a geographic and political - administrative division of the country. It comprises seven states of Arunachal Pradesh, Assam, Manipur, Meghalaya, Mizoram, Nagaland and Tripura.</p>",
                    solution_hi: "<p>3.(d) सिक्किम पूर्वोत्तर भारत के \'सेवन सिस्टर्स\' राज्यों का सदस्य नहीं है। उत्तर-पूर्वी भारत, भारत का सबसे पूर्वी क्षेत्र है जो देश के भौगोलिक और राजनीतिक - प्रशासनिक विभाजन दोनों का प्रतिनिधित्व करता है। इसमें अरुणाचल प्रदेश, असम, मणिपुर, मेघालय, मिजोरम, नागालैंड और त्रिपुरा के सात राज्य शामिल हैं।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "4",
                    section: "18",
                    question_en: "<p>4. In which year was The Environment (Protection) Act passed by the parliament of India ?</p>",
                    question_hi: "<p>4. भारत की संसद द्वारा पर्यावरण (संरक्षण) अधिनियम किस वर्ष पारित किया गया था ?</p>",
                    options_en: ["<p>1986</p>", "<p>1988</p>", 
                                "<p>1990</p>", "<p>1991</p>"],
                    options_hi: ["<p>1986</p>", "<p>1988</p>",
                                "<p>1990</p>", "<p>1991</p>"],
                    solution_en: "<p>4.(a) The Environment (Protection) Act was passed by the parliament of India in the year of 1986. It authorises the Central Government to protect and improve environmental quality, control and reduce pollution from all sources and prohibit or restrict the operation of any industrial facility on environmental grounds.</p>",
                    solution_hi: "<p>4.(a) पर्यावरण (संरक्षण) अधिनियम 1986 के वर्ष में भारत की संसद द्वारा पारित किया गया था। यह केंद्र सरकार को पर्यावरण की गुणवत्ता की रक्षा और सुधार करने, सभी स्रोतों से प्रदूषण को नियंत्रित करने, पर्यावरण पर किसी भी औद्योगिक सुविधा के संचालन को प्रतिबंधित करने के लिए अधिकृत करता है।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "5",
                    section: "18",
                    question_en: "<p>5. Between two cities does India&rsquo;s first semi high - speed train &lsquo;Vande Bharat Express&rsquo; run ?</p>",
                    question_hi: "<p>5. भारत की पहली सामाजिक उच्च गति वाली रेलगाड़ी \"वंदे भारत एक्सप्रेस\" किन दो शहरों के बीच चलती है ?</p>",
                    options_en: ["<p>Puri and Howrah Junction</p>", "<p>Ahmedabad and Mumbai central</p>", 
                                "<p>Hazrat Nizamuddin and Jhansi Junction</p>", "<p>New Delhi and Varanasi Junction</p>"],
                    options_hi: ["<p>पुरी और हावड़ा जंक्शन</p>", "<p>अहमदाबाद और मुंबई सेंट्रल</p>",
                                "<p>हजरत निजामुद्दीन और झांसी जंक्शन</p>", "<p>नई दिल्ली और वाराणसी जंक्शन\\</p>"],
                    solution_en: "<p>5.(d) Between the two cities New Delhi and Varanasi Junction, India&rsquo;s first semi high - speed train &ldquo;Vande Bharat Express &rdquo; runs. It is also known as Train 18, which was designed and manufactured by Integral Coach Factory at Perambur, Chennai under the Indian Government&rsquo;s Make in India Initiative, over a span of 18 months (2019 - 21).</p>",
                    solution_hi: "<p>5.(d) दो शहरों नई दिल्ली और वाराणसी जंक्शन के बीच भारत की पहली सेमी हाई स्पीड ट्रेन \"वंदे भारत एक्सप्रेस\" चलती है। इसे ट्रेन 18 के रूप में भी जाना जाता है, जिसे 18 महीने (2019 - 21) की अवधि में भारत सरकार की मेक इन इंडिया पहल के तहत पेरंबूर, चेन्नई में इंटीग्रल कोच फैक्ट्री द्वारा डिजाइन और निर्मित किया गया था।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "6",
                    section: "18",
                    question_en: "<p>6. Which of the following is a multi-barrel rocket system developed by DRDO ?</p>",
                    question_hi: "<p>6. निम्नलिखित में से कौन सा DRDO द्वारा विकसित एक मल्टी बैरल रॉकेट प्रणाली है ?</p>",
                    options_en: ["<p>Trishul</p>", "<p>Pinaka</p>", 
                                "<p>Prithvi</p>", "<p>Dhanush</p>"],
                    options_hi: ["<p>त्रिशूल</p>", "<p>पिनाक</p>",
                                "<p>पृथ्वी</p>", "<p>धनुष</p>"],
                    solution_en: "<p>6.(b) Pinaka is a multi - barrel rocket system indigenously developed by DRDO. It was launched on 24 June 2021, at Integrated Test Range (ITR), Chandipur off the coast of Odisha.</p>",
                    solution_hi: "<p>6.(b) पिनाका एक बहु - बैरल रॉकेट प्रणाली है जिसे DRDO द्वारा स्वदेशी रूप से विकसित किया गया है। इसे 24 जून 2021 को ओडिशा के तट पर एकीकृत परीक्षण रेंज (ITR), चांदीपुर में लॉन्च किया गया था।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "7",
                    section: "18",
                    question_en: "<p>7. If the mass of a person is 60 kg on the surface of earth then the same person&lsquo;s mass on the surface of the moon will be:</p>",
                    question_hi: "<p>7. यदि किसी व्यक्ति का द्रव्यमान पृथ्वी की सतह पर 60 kg है तो चंद्रमा की सतह पर उसी व्यक्ति का द्रव्यमान कितना होगा ?</p>",
                    options_en: ["<p>0 kg</p>", "<p>360 kg</p>", 
                                "<p>60 kg</p>", "<p>10 kg</p>"],
                    options_hi: ["<p>0 kg</p>", "<p>360 kg</p>",
                                "<p>60 kg</p>", "<p>10 kg</p>"],
                    solution_en: "<p>7.(c) If the mass of a person is 60 kg on the surface of earth then the same person&lsquo;s mass on the surface of the moon will be exactly the same i.e. 60 kg. Because mass is the quantitative measure of inertia, a fundamental property of all matter, and it does not change by changing the position or place of the matter.</p>",
                    solution_hi: "<p>7.(c) यदि किसी व्यक्ति का द्रव्यमान पृथ्वी की सतह पर 60 किग्रा है तो चंद्रमा की सतह पर उसी व्यक्ति का द्रव्यमान ठीक वैसा ही होगा अर्थात 60 किग्रा। क्योंकि द्रव्यमान जड़त्व का मात्रात्मक माप है, जो सभी पदार्थों का एक मूलभूत गुण है, और यह पदार्थ की स्थिति या स्थान को बदलने से नहीं बदलता है।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "8",
                    section: "18",
                    question_en: "<p>8. Who was honoured with the 55th Jnanpith Award for the year 2019 ?</p>",
                    question_hi: "<p>8. वर्ष 2019 के लिए 55वें ज्ञानपीठ पुरस्कार से किसे सम्मानित किया गया ?</p>",
                    options_en: ["<p>A&nbsp; Achuthan Namboothiri</p>", "<p>Krishna Sobti</p>", 
                                "<p>Shobha Rao</p>", "<p>Chitra Mudgal</p>"],
                    options_hi: ["<p>ए अच्युतन नंबूथिरी</p>", "<p>कृष्णा सोबती</p>",
                                "<p>शोभा राव</p>", "<p>चित्रा मुदगल</p>"],
                    solution_en: "<p>8.(a) Malayalam poet Akkitham Achuthan Namboothiri was honoured with the 55th Jnanpith Award for the year 2019. The Jnanpith award is the highest literary award given by the Indian Jnanpith Trust for Indian Literature. Any citizen of India who writes in any of the 22 languages mentioned in the 8th schedule is eligible for this award.</p>",
                    solution_hi: "<p>8.(a) मलयालम कवि अक्किथम अच्युतन नंबूथिरी को वर्ष 2019 के लिए 55वें ज्ञानपीठ पुरस्कार से सम्मानित किया गया। ज्ञानपीठ पुरस्कार भारतीय साहित्य के लिए भारतीय ज्ञानपीठ ट्रस्ट द्वारा दिया जाने वाला सर्वोच्च साहित्यिक पुरस्कार है। भारत का कोई भी नागरिक जो आठवीं अनुसूची में उल्लिखित 22 भाषाओं में से किसी एक में लिखता है, इस पुरस्कार के लिए पात्र है।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "9",
                    section: "18",
                    question_en: "<p>9. Mahatma Gandhi started the famous &lsquo;Salt March&rsquo; from Sabarmati to Dandi. In which district of Gujarat is Dandi ?</p>",
                    question_hi: "<p>9. महात्मा गांधी ने साबरमती से दांडी तक प्रसिद्ध \'नमक मार्च\' की शुरुआत की। दांडी गुजरात के किस जिले में है ?</p>",
                    options_en: ["<p>Navsari</p>", "<p>Surat</p>", 
                                "<p>Porbandar</p>", "<p>Kutch</p>"],
                    options_hi: ["<p>नवसारी</p>", "<p>सूरत</p>",
                                "<p>पोरबंदर</p>", "<p>कच्छ</p>"],
                    solution_en: "<p>9.(a) Dandi is in the district of Navsari in Gujarat and Mahatma Gandhi started the famous &lsquo;Salt March&rsquo; from Sabarmati to Dandi.</p>",
                    solution_hi: "<p>9.(a) दांडी गुजरात के नवसारी जिले में है और महात्मा गांधी ने साबरमती से दांडी तक प्रसिद्ध \' नमक मार्च &rsquo; शुरू किया था।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "10",
                    section: "18",
                    question_en: "<p>10. Which of the following passes connects Srinagar and Leh ?</p>",
                    question_hi: "<p>10. निम्नलिखित में से कौन सा दर्रा श्रीनगर और लेह को जोड़ता है ?</p>",
                    options_en: ["<p>Jelep La</p>", "<p>Nathu La</p>", 
                                "<p>Bara La</p>", "<p>Zoji La</p>"],
                    options_hi: ["<p>जेलेपला दर्रा</p>", "<p>नाथूला दर्रा</p>",
                                "<p>बाराला दर्रा</p>", "<p>ज़ोजीला दर्रा</p>"],
                    solution_en: "<p>10.(d) Zoji La pass connects Srinagar and Leh. It is a high mountain pass in the Himalayas in the UT of Ladakh, having an elevation of 3528 m.</p>",
                    solution_hi: "<p>10.(d) जोजी ला दर्रा श्रीनगर और लेह को जोड़ता है। यह केंद्र शासित प्रदेश लद्दाख में हिमालय में एक उच्च पर्वतीय दर्रा है, जिसकी ऊंचाई 3528 मीटर है।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "11",
                    section: "18",
                    question_en: "<p>11. Which of the following countries is NOT a permanent member of United Nations Security Council ?</p>",
                    question_hi: "<p>11. निम्नलिखित में से कौन सा देश संयुक्त राष्ट्र सुरक्षा परिषद का स्थायी सदस्य नहीं है ?</p>",
                    options_en: ["<p>France</p>", "<p>United Kingdom</p>", 
                                "<p>Japan</p>", "<p>China</p>"],
                    options_hi: ["<p>फ्रांस</p>", "<p>यूनाइटीड किंगडम</p>",
                                "<p>जापान</p>", "<p>चीन</p>"],
                    solution_en: "<p>11.(c) Japan is not a permanent member of the United Nations Security Council. UNSC is one of the six principal organs of the UN, charged with ensuring international peace and security, recommending the admission of new UN members to the general assembly and approving any changes to the UN charter.</p>",
                    solution_hi: "<p>11.(c) जापान संयुक्त राष्ट्र सुरक्षा परिषद का स्थायी सदस्य नहीं है। UNSC संयुक्त राष्ट्र के छह प्रमुख अंगों में से एक है, जिस पर अंतर्राष्ट्रीय शांति और सुरक्षा सुनिश्चित करने, संयुक्त राष्ट्र के नए सदस्यों को महासभा में प्रवेश की सिफारिश करने और संयुक्त राष्ट्र चार्टर में किसी भी बदलाव को मंजूरी देने का भार है।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "12",
                    section: "18",
                    question_en: "12. In Which of the following continents is the Gobi desert located ?",
                    question_hi: "12. गोबी मरुस्थल निम्नलिखित में से किस महाद्वीप में स्थित है ?",
                    options_en: [" Europe", " North America ", 
                                " Asia ", " Africa "],
                    options_hi: [" यूरोप ", " उत्तरी अमेरिका",
                                " एशिया", " अफ्रीका"],
                    solution_en: "12.(c) The Gobi desert is located in the Asian continent. Gobi desert is a vast arid region in northern China and southern Mongolia. It is known for its dunes, mountains and rare animals such as snow leopards and Bactrian camels. ",
                    solution_hi: "12.(c) गोबी मरुस्थल एशियाई महाद्वीप में स्थित है। गोबी मरुस्थल उत्तरी चीन और दक्षिणी मंगोलिया में एक विशाल शुष्क क्षेत्र है। यह अपने टीलों, पहाड़ों और दुर्लभ जानवरों जैसे हिम तेंदुए और बैक्ट्रियन ऊंटों के लिए जाना जाता है।",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "13",
                    section: "18",
                    question_en: "<p>13. Which of the following is in the list of Maharatna Central Public Sector Enterprises ?</p>",
                    question_hi: "<p>13. निम्नलिखित में से कौन महारत्न केंद्रीय सार्वजनिक क्षेत्र के उद्यमों की सूची में है ?</p>",
                    options_en: ["<p>India Tourism Development corporation</p>", "<p>Central Coalfields Limited</p>", 
                                "<p>Coal India Limited</p>", "<p>Cochin Shipyard</p>"],
                    options_hi: ["<p>भारत पर्यटन विकास निगम</p>", "<p>केंद्रीय कोयला क्षेत्र लिमिटेड</p>",
                                "<p>भारतीय कोयला लिमिटेड</p>", "<p>कोचीन शिपयार्ड</p>"],
                    solution_en: "<p>13.(c) Coal India Limited is in the list of Maharatna Central Public Sector Enterprises. As of January 2020, there are 10 Maharatna and 14 Navratna companies in India.<br>The 10 Maharatna companies are : <br>1. Bharat Heavy Electricals Ltd.(BHEL)<br>2. Bharat Petroleum Co Ltd. (BPCL)<br>3. Coal India Ltd.<br>4. GAIL<br>5. Hindustan Petroleum Co Ltd.(HPCL)<br>6. Indian Oil Corporation Ltd.(IOCL)<br>7. Power Grid Corporation of India Ltd.<br>8. SAIL<br>9. ONGC<br>10. NTPC Lt.</p>",
                    solution_hi: "<p>13.(c) भारतीय कोयला लिमिटेड महारत्न सेंट्रल पब्लिक सेक्टर एंटरप्राइजेज की सूची में है। जनवरी 2020 तक, भारत में 10 महारत्न और 14 नवरत्न कंपनियां हैं।<br>1. Bharat Heavy Electricals Ltd.(BHEL)<br>2. Bharat Petroleum Co Ltd. (BPCL)<br>3. Coal India Ltd.<br>4. GAIL<br>5. Hindustan Petroleum Co Ltd.(HPCL)<br>6. Indian Oil Corporation Ltd.(IOCL)<br>7. Power Grid Corporation of India Ltd.<br>8. SAIL<br>9. ONGC<br>10. NTPC Lt.</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "14",
                    section: "18",
                    question_en: "<p>14. What is &lsquo;UBUNTU&rsquo; ?</p>",
                    question_hi: "<p>14. UBUNTU क्या है ?</p>",
                    options_en: ["<p>Operating System</p>", "<p>Malware</p>", 
                                "<p>External Hard drive</p>", "<p>Web Browser</p>"],
                    options_hi: ["<p>ऑपरेटिंग सिस्टम</p>", "<p>मैलवेयर</p>",
                                "<p>बाह्य हार्ड ड्राइव</p>", "<p>वेब ब्राउज़र</p>"],
                    solution_en: "<p>14.(a) UBUNTU is an operating system. It is a Linux distribution based on Debian and composed mostly of free and open - source software.</p>",
                    solution_hi: "<p>14.(a) UBUNTU एक ऑपरेटिंग सिस्टम है। यह डेबियन पर आधारित एक लिनक्स वितरण है और ज्यादातर फ्री और ओपन - सोर्स सॉफ्टवेयर से बना है।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "15",
                    section: "18",
                    question_en: "<p>15. Which two banks were merged with Bank of Baroda with effect from 1st April 2019 ?</p>",
                    question_hi: "<p>15. 1 अप्रैल 2019 से किन दो बैंकों का बैंक ऑफ बड़ौदा में विलय कर दिया गया ?</p>",
                    options_en: ["<p>Allahabad Bank and Canara Bank</p>", "<p>Vijaya Bank and Dena Bank</p>", 
                                "<p>Syndicate Bank and UCO Bank</p>", "<p>Union Bank of India and Andhra Bank</p>"],
                    options_hi: ["<p>इलाहाबाद बैंक और केनरा बैंक</p>", "<p>विजया बैंक और देना बैंक</p>",
                                "<p>सिंडिकेट बैंक और यूको बैंक</p>", "<p>यूनियन बैंक ऑफ इंडिया और आंध्रा बैंक</p>"],
                    solution_en: "<p>15.(b) Vijaya Bank and Dena Bank were merged with Bank of Baroda with effect from 1st April 2019. Bank of Baroda is a nationalised banking and financial services company under the ownership of the Ministry of Finance and the fourth largest nationalised bank headquartered in Alkapuri, Vadodara.</p>",
                    solution_hi: "<p>15.(b) विजया बैंक और देना बैंक को 1 अप्रैल 2019 से बैंक ऑफ बड़ौदा में मिला दिया गया था। बैंक ऑफ बड़ौदा वित्त मंत्रालय के स्वामित्व में एक राष्ट्रीयकृत बैंकिंग और वित्तीय सेवा कंपनी है और चौथा सबसे बड़ा राष्ट्रीयकृत बैंक है। इसके मुख्यालय अलकापुरी, वडोदरा में स्थित है।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "16",
                    section: "18",
                    question_en: "<p>16. Which part of the computer is called its brain ?</p>",
                    question_hi: "<p>16. कंप्यूटर के किस भाग को उसका मस्तिष्क कहा जाता है ?</p>",
                    options_en: ["<p>Monitor</p>", "<p>CPU</p>", 
                                "<p>Hard Disc</p>", "<p>ROM</p>"],
                    options_hi: ["<p>मॉनिटर</p>", "<p>CPU</p>",
                                "<p>हार्ड डिस्क</p>", "<p>ROM</p>"],
                    solution_en: "<p>16.(b) The CPU is the part of a computer which is called its brain. CPU is the electronic circuitry that executes instructions comprising a computer program. It performs basic arithmetic logic, controlling and input/output operations specified by the instructions in the program.</p>",
                    solution_hi: "<p>16.(b) CPU कंप्यूटर का वह भाग होता है जिसे उसका ब्रेन कहा जाता है। सीपीयू इलेक्ट्रॉनिक सर्किटरी है जो कंप्यूटर प्रोग्राम से जुड़े निर्देशों को निष्पादित करता है। यह प्रोग्राम में निर्देशों द्वारा निर्दिष्ट बुनियादी अंकगणितीय तर्क, नियंत्रण और इनपुट/आउटपुट का संचालन करता है।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "17",
                    section: "18",
                    question_en: "<p>17. In which year did ISRO launch the Mars Orbiter Mission ?</p>",
                    question_hi: "<p>17. ISRO ने किस वर्ष मंगल कक्षित्र मिशन (मंगलयान) प्रक्षेपित किया था ?</p>",
                    options_en: ["<p>2013</p>", "<p>2015</p>", 
                                "<p>2012</p>", "<p>2014</p>"],
                    options_hi: ["<p>2013</p>", "<p>2015</p>",
                                "<p>2012</p>", "<p>2014</p>"],
                    solution_en: "<p>17.(a) In 2013, ISRO launched the Mars Orbiter Mission. Mars Orbiter mission also known as Mangalayaan is a space probe orbiting Mars, since 24th September 2014. It was India&rsquo;s first interplanetary spacecraft.</p>",
                    solution_hi: "<p>17.(a) 2013 में इसरो ने मार्स ऑर्बिटर मिशन लॉन्च किया था। मार्स ऑर्बिटर मिशन जिसे मंगलयान के नाम से भी जाना जाता है, 24 सितंबर 2014 से मंगल ग्रह की परिक्रमा करने वाला एक अंतरिक्ष यान है। यह भारत का पहला अंतरग्रहीय अंतरिक्ष यान था।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "18",
                    section: "18",
                    question_en: "18. Which of the following units is used for measuring the amount of a substance ?",
                    question_hi: "18. निम्नलिखित में से किस इकाई का उपयोग किसी पदार्थ की मात्रा को मापने के लिए किया जाता है ?",
                    options_en: [" Mole", " Tesla", 
                                " Joule", " Lux"],
                    options_hi: [" मोल ", " टेस्ला ",
                                " जूल ", " लक्स "],
                    solution_en: "<p>18.(a) Mole is used for measuring the amount of a substance. It is the base unit of amount of substance in the International System of Units (SI). It is defined as 6.02214076<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#215;</mo><msup><mn>10</mn><mn>23</mn></msup></math></p>",
                    solution_hi: "18.(a) मोल का प्रयोग किसी पदार्थ की मात्रा मापने के लिए किया जाता है। मोल इंटरनेशनल सिस्टम ऑफ यूनिट्स (SI) में पदार्थ की मात्रा की आधार इकाई है। इसे 6.02214076<math display=\"inline\"><mo>×</mo><msup><mrow><mn>10</mn></mrow><mrow><mn>23</mn></mrow></msup></math> के रूप में परिभाषित किया गया है।",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "19",
                    section: "18",
                    question_en: "<p>19. Who is the author of &lsquo;Rajatarangini&rsquo; ?</p>",
                    question_hi: "<p>19. \'राजतरंगिणी\' के लेखक कौन हैं ?</p>",
                    options_en: ["<p>Kalidasa</p>", "<p>Chand Bardai</p>", 
                                "<p>Kalhana</p>", "<p>Jayadeva</p>"],
                    options_hi: ["<p>कालिदास</p>", "<p>चन्द्र बरदाई</p>",
                                "<p>कल्हण</p>", "<p>जयदेव</p>"],
                    solution_en: "<p>19.(c) Kalhana, the kashmiri historian, is the author of the legendary and historical chronicle Rajatarangini. It was written in the 12th century CE.</p>",
                    solution_hi: "<p>19.(c) कश्मीरी इतिहासकार, कल्हण, पौराणिक और ऐतिहासिक कालक्रम \'राजतरंगिणी\' के लेखक हैं। यह 12वीं शताब्दी ई. में लिखा गया था।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "20",
                    section: "18",
                    question_en: "<p>20. Pattachitra style of painting is one of the oldest and most popular art forms of which of the following states ?</p>",
                    question_hi: "<p>20. पेंटिंग की पट्टाचित्र शैली निम्नलिखित में से किस राज्य की सबसे पुरानी और सबसे लोकप्रिय कला रूपों में से एक है ?</p>",
                    options_en: ["<p>Bihar</p>", "<p>Andhra Pradesh</p>", 
                                "<p>Odisha</p>", "<p>Rajasthan</p>"],
                    options_hi: ["<p>बिहार</p>", "<p>आंध्र प्रदेश</p>",
                                "<p>ओड़िसा</p>", "<p>राजस्थान</p>"],
                    solution_en: "<p>20.(c) Pattachitra style of painting is one of the oldest and most popular art forms of Odisha. The pattachitra art form is known for its intricate details as well as mythological narratives and folktales inscribed in it.</p>",
                    solution_hi: "<p>20.(c) पेंटिंग की पट्टाचित्र शैली ओडिशा के सबसे पुराने और सबसे लोकप्रिय कला रूपों में से एक है। पट्टाचित्र कला रूप अपने जटिल विवरणों के साथ-साथ पौराणिक कथाओं और इसमें अंकित लोककथाओं के लिए जाना जाता है।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "21",
                    section: "18",
                    question_en: "<p>21. Which Sikh Guru established the Khalsa Panth ?</p>",
                    question_hi: "<p>21. खालसा पंथ की स्थापना किस सिख गुरु ने की थी ?</p>",
                    options_en: ["<p>Shri Guru Tegh Bahadur ji</p>", "<p>Shri Guru Gobind Singh ji</p>", 
                                "<p>Shri Guru Har Gobind ji</p>", "<p>Shri Guru Nanak ji</p>"],
                    options_hi: ["<p>श्री गुरु तेग बहादुर जी</p>", "<p>श्री गुरु गोबिंद सिंह जी</p>",
                                "<p>श्री गुरु हर गोबिंद जी</p>", "<p>श्री गुरु नानक जी</p>"],
                    solution_en: "<p>21.(b) The 10th Sikh Guru Shri Guru Gobind Singh ji established the Khalsa Panth in 1699. Khalsa Panth refers to both a community that considers Sikhism as its faith, as well as a special group which was a key event in the history of Sikhism.</p>",
                    solution_hi: "<p>21.(b) 10वें सिख गुरु श्री गुरु गोबिंद सिंह जी ने 1699 में खालसा पंथ की स्थापना की। खालसा पंथ एक ऐसे समुदाय को संदर्भित करता है जो सिख धर्म को अपना विश्वास मानता है साथ ही एक विशेष समूह जो सिख धर्म के इतिहास में एक महत्वपूर्ण घटना थी।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "22",
                    section: "18",
                    question_en: "<p>22. Which of the following monuments is NOT located in Delhi ?</p>",
                    question_hi: "<p>22. निम्नलिखित में से कौन सा स्मारक दिल्ली में स्थित नहीं है ?</p>",
                    options_en: ["<p>Buland Darwaza</p>", "<p>Alai Darwaza</p>", 
                                "<p>Humayun\'s Tomb</p>", "<p>India Gate</p>"],
                    options_hi: ["<p>बुलंद दरवाजा</p>", "<p>अलाई दरवाजा</p>",
                                "<p>हुमायूँ का मकबरा</p>", "<p>इंडिया गेट</p>"],
                    solution_en: "<p>22.(a) All three monuments, Alai Darwaza, Humayun\'s Tomb, India Gate are located in Delhi. Only Buland Darwaza is located in Fatehpur Sikri, Agra also called &ldquo;Door of Victory&rdquo;. It was built by Mughal Emperor Akbar in 1575 to commemorate the victory over Gujarat.</p>",
                    solution_hi: "<p>22.(a) तीनों स्मारक, अलाई दरवाजा, हुमायूँ का मकबरा, इंडिया गेट दिल्ली में स्थित हैं। केवल बुलंद दरवाजा फतेहपुर सीकरी, आगरा में स्थित है। जिसे \"विजय का द्वार\" भी कहा जाता है, इसे मुगल सम्राट अकबर ने 1575 में गुजरात पर जीत के उपलक्ष्य में बनवाया था।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "23",
                    section: "18",
                    question_en: "<p>23. Which of the following diseases is caused by a virus ?</p>",
                    question_hi: "<p>23. निम्नलिखित में से कौन-सा रोग विषाणु से होता है ?</p>",
                    options_en: ["<p>Cholera</p>", "<p>Chicken Pox</p>", 
                                "<p>Typhoid</p>", "<p>Tuberculosis</p>"],
                    options_hi: ["<p>हैजा</p>", "<p>छोटी चेचक</p>",
                                "<p>टाइफाइड</p>", "<p>क्षय रोग</p>"],
                    solution_en: "<p>23.(b) Chicken Pox, a highly contagious illness caused by primary infection with a virus called Varicella Zoster Virus (VZV). The vaccine was developed in Japan, by Michiaki Takahashi.</p>",
                    solution_hi: "<p>23.(b) चिकन पॉक्स, एक अत्यधिक संक्रामक बीमारी है जो वैरीसेला जोस्टर वायरस (VZV) नामक वायरस के प्राथमिक संक्रमण के कारण होती है। इसका टीका जापान में मिचियाकी ताकाहाशी द्वारा विकसित किया गया था।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "24",
                    section: "18",
                    question_en: "<p>24. In the domain of computers and the internet , what is the full form of URL ?</p>",
                    question_hi: "24. कंप्यूटर और इंटरनेट के क्षेत्र में URL का पूर्ण रूप क्या है ?",
                    options_en: [" Unique Resource Location   ", " Unique Revoked Location ", 
                                " Uniform Resource Locator", " Universal Resource Locator"],
                    options_hi: [" Unique Resource Location   ", " Unique Revoked Location ",
                                " Uniform Resource Locator", " Universal Resource Locator"],
                    solution_en: "24.(c) In the domain of computers and the internet, the full form of URL is Uniform Resource Locator. URL is a reference to a web resource that specifies its location on a computer network and a mechanism for retrieving it.",
                    solution_hi: "24.(c) कंप्यूटर और इंटरनेट के क्षेत्र में, URL का पूर्ण रूप यूनिफ़ॉर्म रिसोर्स लोकेटर है। URL एक वेब संसाधन का एक संदर्भ है जो कंप्यूटर नेटवर्क पर अपना स्थान और इसे पुनः प्राप्त करने के लिए एक तंत्र को निर्दिष्ट करता है।",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "25",
                    section: "18",
                    question_en: "<p>25. The term &lsquo;Sericulture&rsquo; is related to which of the following ?</p>",
                    question_hi: "<p>25. \'सेरीकल्चर\' शब्द निम्नलिखित में से किससे संबंधित है ?</p>",
                    options_en: ["<p>Fish Farming</p>", "<p>Bird Farming</p>", 
                                "<p>Silk Farming</p>", "<p>Bee Farming</p>"],
                    options_hi: ["<p>मत्स्य पालन</p>", "<p>पक्षी पालन</p>",
                                "<p>रेशम पालन</p>", "<p>मधुमक्खी पालन</p>"],
                    solution_en: "<p>25.(c) The term &lsquo;Sericulture&rsquo; is related to Silk Farming. Sericulture is the cultivation of silkworms to produce silk. Bombyx mori is the most widely used species of silkworm.</p>",
                    solution_hi: "<p>25.(c) \'सेरीकल्चर\' शब्द रेशम की खेती से संबंधित है। रेशम उत्पादन रेशम के कीड़ों की खेती है। बॉम्बेक्स मोरी रेशमकीट की सबसे व्यापक रूप से इस्तेमाल की जाने वाली प्रजाति है।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "26",
                    section: "18",
                    question_en: "<p>26. Which of the following shows a symbiotic relationship ?</p>",
                    question_hi: "<p>26. निम्नलिखित में से कौन सहजीवी संबंध दर्शाता है</p>",
                    options_en: ["<p>Funaria</p>", "<p>Lichen</p>", 
                                "<p>Marsilea</p>", "<p>Ulothrix</p>"],
                    options_hi: ["<p>फनेरिया</p>", "<p>लाइकेन</p>",
                                "<p>मार्सिलिया</p>", "<p>उलोथ्रिक</p>"],
                    solution_en: "<p>26.(b) The lichen shows a mutualistic symbiotic relationship. Symbiosis is any type of close and long - term biological interaction between two different biological organisms, be it mutualistic, commensalism or parasitic. The organisms which are involved in the symbiotic relation are called symbionts and they must have to be of different species.</p>",
                    solution_hi: "<p>26.(b) लाइकेन एक पारस्परिक सहजीवी संबंध को दर्शाता है। सहजीवन दो अलग - अलग जैविक जीवों के बीच किसी भी प्रकार का घनिष्ठ और दीर्घकालिक जैविक संपर्क है, चाहे वह पारस्परिक, सहभोजवाद या परजीवी हो। सहजीवी संबंध में शामिल जीवों को सहजीवन कहा जाता है और उन्हें विभिन्न प्रजातियों का होना चाहिए।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "27",
                    section: "18",
                    question_en: "<p>27. Name the theme declared by the United Nations for World Environment Day, 2020.</p>",
                    question_hi: "<p>27. विश्व पर्यावरण दिवस, 2020 के लिए संयुक्त राष्ट्र द्वारा घोषित थीम का नाम बताइये ?</p>",
                    options_en: ["<p>Beat Plastic Pollution</p>", "<p>Biodiversity</p>", 
                                "<p>Connecting people to Nature</p>", "<p>Water Pollution</p>"],
                    options_hi: ["<p>प्लास्टिक प्रदूषण को रोकिये</p>", "<p>जैव विविधता</p>",
                                "<p>लोगों को प्रकृति से जोड़ना</p>", "<p>जल प्रदूषण</p>"],
                    solution_en: "<p>27.(b) The theme declared by the United Nations for World Environment Day, 2020 is Biodiversity and was hosted in Colombia, in partnership with Germany. World Environment Day is the UN day for encouraging worldwide awareness and action to protect our environment.</p>",
                    solution_hi: "<p>27.(b) विश्व पर्यावरण दिवस, 2020 के लिए संयुक्त राष्ट्र द्वारा घोषित विषय जैव विविधता है और जर्मनी के साथ साझेदारी में कोलंबिया में आयोजित किया गया था। विश्व पर्यावरण दिवस हमारे पर्यावरण की रक्षा के लिए दुनिया भर में जागरूकता और कार्रवाई को प्रोत्साहित करने के लिए संयुक्त राष्ट्र दिवस है।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "28",
                    section: "18",
                    question_en: "<p>28. Who was the founder of the Prarthana Samaj ?</p>",
                    question_hi: "<p>28. प्रार्थना समाज के संस्थापक कौन थे</p>",
                    options_en: ["<p>Atmaram Pandurang</p>", "<p>Swami Vivekananda</p>", 
                                "<p>Raja Ram Mohan Roy</p>", "<p>Swami Dayanand Saraswati</p>"],
                    options_hi: ["<p>आत्माराम पांडुरंग</p>", "<p>स्वामी विवेकानंद</p>",
                                "<p>राजा राम मोहन राय</p>", "<p>स्वामी दयानंद सरस्वती</p>"],
                    solution_en: "<p>28.(a) Atmaram Pandurang was the founder of the Prarthana Samaj. It was a leading society for socio - religious reform, established on 31st March 1867 in Bombay.</p>",
                    solution_hi: "<p>28.(a) आत्माराम पांडुरंग प्रार्थना समाज के संस्थापक थे। यह 31 मार्च 1867 को बॉम्बे में स्थापित सामाजिक - धार्मिक सुधार के लिए एक अग्रणी समाज था।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "29",
                    section: "18",
                    question_en: "<p>29. Through which of the following mediums can sound NOT travel ?</p>",
                    question_hi: "<p>29. निम्नलिखित में से किस माध्यम से ध्वनि गति नहीं कर सकती है ?</p>",
                    options_en: ["<p>Steel</p>", "<p>Vacuum</p>", 
                                "<p>Air</p>", "<p>Milk</p>"],
                    options_hi: ["<p>इस्पात</p>", "<p>निर्वात</p>",
                                "<p>वायु</p>", "<p>दूध</p>"],
                    solution_en: "<p>29.(b) Sound cannot travel through the vacuum because sound waves are longitudinal waves that travel through a medium like air or water and the speed of sound in air is 343m/sec.</p>",
                    solution_hi: "<p>29.(b) ध्वनि निर्वात के माध्यम से यात्रा नहीं कर सकती क्योंकि ध्वनि तरंगें अनुदैर्ध्य तरंगें होती हैं जो हवा या पानी जैसे माध्यम से यात्रा करती हैं और हवा में ध्वनि की गति 343m/sec होती है।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "30",
                    section: "18",
                    question_en: "<p>30. In which city was the Khelo India Youth Games, 2020 held ?</p>",
                    question_hi: "<p>30. खेलो इंडिया यूथ गेम्स, 2020 किस शहर में आयोजित किया गया था ?</p>",
                    options_en: ["<p>Guwahati</p>", "<p>Kolkata</p>", 
                                "<p>Chennai</p>", "<p>Banglore</p>"],
                    options_hi: ["<p>गुवाहाटी</p>", "<p>कोलकाता</p>",
                                "<p>चेन्नई</p>", "<p>बैंगलोर</p>"],
                    solution_en: "<p>30.(a) The Khelo India Youth Games, 2020 was held in Guwahati. The third Khelo India Youth Games was held from 10 to 22 January 2020 in Assam. On January 31st 2018, prime minister Narendra Modi inaugurated Khelo India Youth Games at the Indira Gandhi arena.</p>",
                    solution_hi: "<p>30.(a) खेलो इंडिया यूथ गेम्स, 2020 गुवाहाटी में आयोजित किया गया था। तीसरा खेलो इंडिया यूथ गेम्स 10 से 22 जनवरी 2020 तक असम में आयोजित किया गया था। 31 जनवरी 2018 को, प्रधान मंत्री नरेंद्र मोदी ने इंदिरा गांधी स्टेडियम में खेलो इंडिया यूथ गेम्स का उद्घाटन किया।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "31",
                    section: "18",
                    question_en: "<p>31. What is the maximum strength of the members of the Lok Sabha ?</p>",
                    question_hi: "<p>31. लोकसभा सदस्यों की अधिकतम संख्या कितनी होती है ?</p>",
                    options_en: ["<p>547</p>", "<p>549</p>", 
                                "<p>552</p>", "<p>543</p>"],
                    options_hi: ["<p>547</p>", "<p>549</p>",
                                "<p>552</p>", "<p>543</p>"],
                    solution_en: "<p>31.(c) The maximum strength of the members of the Lok Sabha allotted by the constitution of India is 552. Initially, in 1950 it was 500 in number. Currently, the house has 543 elected members.</p>",
                    solution_hi: "<p>31.(c) भारत के संविधान द्वारा आवंटित लोकसभा के सदस्यों की अधिकतम संख्या 552 है। प्रारंभ में, 1950 में यह संख्या में 500 थी। वर्तमान में, सदन में 543 निर्वाचित सदस्य हैं।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "32",
                    section: "18",
                    question_en: "<p>32. In which the year was the second battle of Panipat fought between Akbar and Hemu ?</p>",
                    question_hi: "<p>32. किस वर्ष पानीपत की दूसरी लड़ाई अकबर और हेमू के बीच लड़ी गई थी</p>",
                    options_en: ["<p>1576</p>", "<p>1526</p>", 
                                "<p>1556</p>", "<p>1536</p>"],
                    options_hi: ["<p>1576</p>", "<p>1526</p>",
                                "<p>1556</p>", "<p>1536</p>"],
                    solution_en: "<p>32.(c) On November 5th, 1556 the second battle of Panipat was fought between Akbar and Hemu. Hemu was defeated and beheaded by Akbar&rsquo;s guardian Bairam Khan and the battle ended in a decisive Mughal victory.</p>",
                    solution_hi: "<p>32.(c) 5 नवंबर, 1556 को पानीपत की दूसरी लड़ाई अकबर और हेमू के बीच लड़ी गई थी।<br>अकबर के संरक्षक बैरम खान द्वारा हेमू को पराजित किया गया और उसका सिर काट दिया गया और लड़ाई एक निर्णायक मुगल जीत में समाप्त हुई।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "33",
                    section: "18",
                    question_en: "<p>33. In which state is the Kudankulam Nuclear Power Station located ?</p>",
                    question_hi: "<p>33. कुडनकुलम परमाणु ऊर्जा केंद्र किस राज्य में स्थित है</p>",
                    options_en: ["<p>Tamil Nadu</p>", "<p>Gujarat</p>", 
                                "<p>Rajasthan</p>", "<p>Karnataka</p>"],
                    options_hi: ["<p>तमिलनाडू</p>", "<p>गुजरात</p>",
                                "<p>राजस्थान</p>", "<p>कर्नाटक</p>"],
                    solution_en: "<p>33.(a) The Kudankulam is the largest nuclear power station in India located in KudanKulam, Tirunelveli district of the state of Tamil Nadu.</p>",
                    solution_hi: "<p>33.(a) कुडनकुलम भारत का सबसे बड़ा परमाणु ऊर्जा केंद्र है जो तमिलनाडु राज्य के तिरुनेलवेली जिले के कुडनकुलम में स्थित है।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "34",
                    section: "18",
                    question_en: "<p>34. Who among the following was the youngest President of India ?</p>",
                    question_hi: "<p>34. निम्नलिखित में से कौन भारत के सबसे युवा राष्ट्रपति थे ?</p>",
                    options_en: ["<p>Shri Neelam Sanjiva Reddy</p>", "<p>Dr. S.Radhakrishnan</p>", 
                                "<p>Dr. Zakir Hussain</p>", "<p>Dr. Rajendra Prasad</p>"],
                    options_hi: ["<p>श्री नीलम संजीव रेड्डी</p>", "<p>डॉ. एस. राधाकृष्णन</p>",
                                "<p>डॉ. जाकिर हुसैन</p>", "<p>डॉ राजेंद्र प्रसाद</p>"],
                    solution_en: "<p>34.(a) Shri Neelam Sanjiva Reddy was the youngest President of India. He was an Indian politician who served as the sixth president of India, serving from 1977 to 1982.</p>",
                    solution_hi: "<p>34.(a) श्री नीलम संजीव रेड्डी भारत के सबसे युवा राष्ट्रपति थे। वह एक भारतीय राजनेता थे, जिन्होंने 1977 से 1982 तक भारत के छठे राष्ट्रपति के रूप में कार्य किया।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "35",
                    section: "18",
                    question_en: "<p>35. Which of the following two countries of South America are land locked ?</p>",
                    question_hi: "<p>35. दक्षिण अमेरिका के निम्नलिखित दो देशों में से कौन सा देश भूमिबद्ध है ?</p>",
                    options_en: ["<p>Paraguay and Bolivia</p>", "<p>Brazil and Venezuela</p>", 
                                "<p>Guyana and Suriname</p>", "<p>Chile and Ecuador</p>"],
                    options_hi: ["<p>पराग्वे और बोलीविया</p>", "<p>ब्राजील और वेनेजुएला</p>",
                                "<p>गुयाना और सूरीनाम</p>", "<p>चिली और इक्वाडोर</p>"],
                    solution_en: "<p>35.(a) The two countries Paraguay and Bolivia of South America are landlocked. These two are the only landlocked countries outside of Afro - Eurasia whereas neither Australia nor North America have any landlocked countries.</p>",
                    solution_hi: "<p>35.(a) दक्षिण अमेरिका के पराग्वे और बोलीविया दोनों देश स्&zwj;थलरुद्घ हैं। ये दोनों एफ्रो - यूरेशिया के बाहर एकमात्र लैंडलॉक देश हैं जबकि न तो ऑस्ट्रेलिया और न ही उत्तरी अमेरिका में कोई लैंडलॉक देश है।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "36",
                    section: "18",
                    question_en: "<p>36. Where is Central Potato Research Institute of India located ?</p>",
                    question_hi: "<p>36. भारत का केन्द्रीय आलू अनुसंधान संस्थान कहाँ स्थित है ?</p>",
                    options_en: ["<p>Delhi</p>", "<p>Lucknow</p>", 
                                "<p>Ranchi</p>", "<p>Shimla</p>"],
                    options_hi: ["<p>दिल्ली</p>", "<p>लखनऊ</p>",
                                "<p>रांची</p>", "<p>शिमला</p>"],
                    solution_en: "<p>36.(d) Central Potato Research Institute of India is located in the heart of Shimla near Bemloe, Himachal Pradesh. Its mission is to carry out research, education, and extension on potato in collaboration with National and international partners for enhancing productivity and profitability and achieving sustainable food.</p>",
                    solution_hi: "<p>36.(d) भारतीय केंद्रीय आलू अनुसंधान संस्थान हिमाचल प्रदेश के बेमलो के पास शिमला के मध्य में स्थित है। इसका मिशन उत्पादकता और लाभप्रदता बढ़ाने और टिकाऊ भोजन प्राप्त करने के लिए राष्ट्रीय और अंतर्राष्ट्रीय भागीदारों के सहयोग से आलू पर अनुसंधान, शिक्षा और विस्तार करना है।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "37",
                    section: "18",
                    question_en: "<p>37. Where is the Head Quarters of the International Court of Justice located ?</p>",
                    question_hi: "<p>37. अंतर्राष्ट्रीय न्यायालय का मुख्यालय कहाँ स्थित है ?</p>",
                    options_en: ["<p>Paris</p>", "<p>New York</p>", 
                                "<p>The Hague</p>", "<p>Washington D.C.</p>"],
                    options_hi: ["<p>पेरिस</p>", "<p>न्यूयॉर्क</p>",
                                "<p>हेग</p>", "<p>वाशिंगटन डी सी</p>"],
                    solution_en: "<p>37.(c) The headquarters of the International Court of Justice is located in The Hague. It is one of the six principal organs of the United Nations. It settles disputes between states in accordance with international law and gives advisory opinions on international legal issues.</p>",
                    solution_hi: "<p>37.(c) अंतर्राष्ट्रीय न्यायालय का मुख्यालय हेग में स्थित है। यह संयुक्त राष्ट्र के छह प्रमुख अंगों में से एक है। यह अंतरराष्ट्रीय कानून के अनुसार राज्यों के बीच विवादों का निपटारा करता है और अंतरराष्ट्रीय कानूनी मुद्दों पर राय देता है।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "38",
                    section: "18",
                    question_en: "<p>38. Who said , &rdquo;Freedom is my birth right and I shall have it&rdquo; ?</p>",
                    question_hi: "<p>38. किसने कहा था \"स्वतंत्रता मेरा जन्मसिद्ध अधिकार है और मैं इसे लेकर रहूँगा\" ?</p>",
                    options_en: ["<p>Bhagat Singh</p>", "<p>Chandra Shekhar Azad</p>", 
                                "<p>Bal Gangadhar Tilak</p>", "<p>Gopal Krishan Gokhale</p>"],
                    options_hi: ["<p>भगत सिंह</p>", "<p>चंद्रशेखर आज़ाद</p>",
                                "<p>बाल गंगाधर तिलक</p>", "<p>गोपाल कृष्ण गोखले</p>"],
                    solution_en: "<p>38.(c) Bal Gangadhar Tilak once said , &lsquo;Freedom is my birth right and I shall have it&rsquo;. Bal Gangadhar Tilak was one of the first and strongest advocates of &lsquo;Swaraj&rsquo; and a strong radical in Indian consciousness.</p>",
                    solution_hi: "<p>38.(c) बाल गंगाधर तिलक ने एक बार कहा था, \"स्वतंत्रता मेरा जन्मसिद्ध अधिकार है और मैं इसे लेकर रहूंगा\"। बाल गंगाधर तिलक \'स्वराज\' के पहले और सबसे मजबूत पैरोकारों में से एक थे और भारतीय चेतना में एक मजबूत कट्टरपंथी थे।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "39",
                    section: "18",
                    question_en: "<p>39. Which Indian state has the longest mainland coastline ?</p>",
                    question_hi: "<p>39. किस भारतीय राज्य की मुख्य भूमि की तटरेखा सबसे लंबी है ?</p>",
                    options_en: ["<p>Maharashtra</p>", "<p>Kerala</p>", 
                                "<p>Gujarat</p>", "<p>Odisha</p>"],
                    options_hi: ["<p>महाराष्ट्र</p>", "<p>केरल</p>",
                                "<p>गुजरात</p>", "<p>उड़ीसा</p>"],
                    solution_en: "<p>39.(c) Gujarat has the longest mainland coastline. The coastline of Gujarat is 992 miles (1,596 km) long which is made by the arabean sea. There are 14 Coastal Economic Zones which are built in Gujarat coastal area under the Sagarmala initiative.</p>",
                    solution_hi: "<p>39.(c) गुजरात में मुख्य भूमि की सबसे लंबी तटरेखा है। गुजरात की तटरेखा 992 मील (1,596 किमी) लंबी है जो अरब सागर द्वारा बनाई गई है। 14 तटीय आर्थिक क्षेत्र हैं जो सागरमाला पहल के तहत गुजरात तटीय क्षेत्र में बनाए गए हैं।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "40",
                    section: "misc",
                    question_en: "<p>40. Where is the Sabarimala temple located ?</p>",
                    question_hi: "<p>40. सबरीमाला मंदिर कहाँ स्थित है</p>",
                    options_en: ["<p>Maharashtra</p>", "<p>Odisha</p>", 
                                "<p>Kerala</p>", "<p>Andhra Pradesh</p>"],
                    options_hi: ["<p>महाराष्ट्र</p>", "<p>उड़ीसा</p>",
                                "<p>केरल</p>", "<p>आंध्र प्रदेश</p>"],
                    solution_en: "<p>40.(c) Sabarimala temple is located in Kerala. It is located in the sabarimala hill inside the Periyar Tiger Reserve in Pathanamthitta district. In 2018, the supreme court gave its landmark judgement allowing women of menstruating age to enter the Sabarimala temple.</p>",
                    solution_hi: "<p>40.(c) सबरीमाला मंदिर केरल में स्थित है। यह पठानमथिट्टा जिले में पेरियार टाइगर रिजर्व के अंदर सबरीमाला पहाड़ी में स्थित है। 2018 में, सर्वोच्च न्यायालय ने अपना ऐतिहासिक फैसला दिया जिसमें मासिक धर्म की उम्र की महिलाओं को सबरीमाला मंदिर में प्रवेश करने की अनुमति दी गई थी।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>