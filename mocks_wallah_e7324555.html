<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">10:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 15</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">15</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 10 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["6"] = {
                name: "Reasoning",
                start: 0,
                end: 13
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="6">Reasoning</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 14,
                end: 14
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "6",
                    question_en: "<p>1. Study the given diagram carefully and answer the question that follows. The numbers in different sections indicate the number of persons who have applied for different posts. <br><br><strong id=\"docs-internal-guid-bd42923d-7fff-b829-61e8-87f177805969\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdkHUN1v4K6rST1MZfGeh8CTE2FakfojAuasjxMhkvpfjzkbKEB6wiQM_VWwtdrIO6KNwOZxJvmZKv00QLzb2-UZCCGXbYmhtVD7-2WexWau8AbLw7D5OlWviuiyF4mct9YVivm?key=OITYw97FLkMQoDclNr-C9DB4\" width=\"136\" height=\"177\"></strong><br>What is the total number of persons who have applied for more than one post?</p>",
                    question_hi: "<p>1. दिए गए आरेख का ध्यानपूर्वक अध्ययन करें और निम्नलिखित प्रश्&zwj;न का उत्तर दें। विभिन्न खंडों में दी गई संख्याएं अलग-अलग पदों के लिए आवेदन करने वाले व्यक्तियों की संख्या को दर्शाती हैं।<br><strong id=\"docs-internal-guid-f35135cf-7fff-0779-ceab-81638bff9889\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcBJ_3zo_wjKDHUfDrWW-m1WIAuBGI31s6ZlJ3F6-t-tgRwgNYB5O3pdRn3fghnk5WEgKjnEQ4uubP0viMrsyNS6n5B6TL2W88qs9pPjGtge0TwH-kjMTmIFXwT295qSi37uKGjfw?key=OITYw97FLkMQoDclNr-C9DB4\" width=\"199\" height=\"233\"></strong><br>एक से अधिक पदों के लिए आवेदन करने वाले व्यक्तियों की कुल संख्या कितनी है?<br>Teacher - अध्यापक<br>Engineer -अभियंता<br>Accountant &ndash; लेखाकार</p>",
                    options_en: ["<p>21</p>", "<p>113</p>", 
                                "<p>15</p>", "<p>18</p>"],
                    options_hi: ["<p>21</p>", "<p>113</p>",
                                "<p>15</p>", "<p>18</p>"],
                    solution_en: "<p>1.(a) Total number of person who have applied for more than 1 post = 8 + 6 + 4 + 3 = 21.</p>",
                    solution_hi: "<p>1.(a) 1 से अधिक पद के लिए आवेदन करने वाले व्यक्तियों की कुल संख्या = 8 + 6 + 4 + 3 = 21.</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "6",
                    question_en: "<p>2. The following Venn diagram shows Men\'s profession as Doctors and Chess Players.<br><strong id=\"docs-internal-guid-5da8b221-7fff-c2fd-b79b-43381fbb3985\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXen1l6_Q_K_qjmauuGyBOOlA_vZFtLemGkJWVc6B1ZSWK6-Bt6171DDC7CenRM3nlKDTh_w7pSXWBtG5YfsO1H7E6-l2m52ffO9Ctb0IXc5K1OOqWPC9RzrxTvQ1g1lRHV8aUZwfQ?key=OITYw97FLkMQoDclNr-C9DB4\" width=\"181\" height=\"204\"></strong><br>How many men are doctors?</p>",
                    question_hi: "<p>2. नीचे दिया गया वेन आरेख डॉक्टरों (Doctors) और शतरंज खिलाड़ियों (Chess Players) के रूप में पुरुषों (Men) के पेशे को दर्शाता है।<br><br><strong id=\"docs-internal-guid-b8354557-7fff-1612-285d-f29dc1e86720\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXenRN8063tKZdStZ_8wAusSVjUHa32w4Z5vKlM9046tKSfxR_0JY19CELRPswx5-oZ5hWAwlqdaYBvreLn06_i0nHM50lfOk7ZFByroEVyURDETRSXZm3YXqChXoGSou0wmmNr_vg?key=OITYw97FLkMQoDclNr-C9DB4\" width=\"190\" height=\"221\"></strong><br>Men - पुरुष, Doctors - डॉक्टर्स, Chess Players - शतरंज खिलाड़ी<br>कितने पुरुष डॉक्टर हैं?</p>",
                    options_en: ["<p>47</p>", "<p>34</p>", 
                                "<p>24</p>", "<p>23</p>"],
                    options_hi: ["<p>47</p>", "<p>34</p>",
                                "<p>24</p>", "<p>23</p>"],
                    solution_en: "<p>2.(a) Total number of Men who are doctors = 23 + 24 = 47.</p>",
                    solution_hi: "<p>2.(a) डॉक्टर बनने वाले पुरुषों की कुल संख्या = 23 + 24 = 47.</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "6",
                    question_en: "<p>3. Study the given Venn diagram and answer the question that follows.<br><strong id=\"docs-internal-guid-4c5c5586-7fff-e1c7-8a54-2e81c6b0e398\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXd1NfSZBayDzqxFL3ARHdu5RdhFxlF5jU686dEz_uaX0o4YWjs75IRB0yl0LikQVJ8NaAG7tl3uMVqvx5V_RFniUbeQ8N7AmnEC9AFlDNNro529rTLZXuC9iUUS6TbDXTr0fap2Aw?key=OITYw97FLkMQoDclNr-C9DB4\" width=\"204\" height=\"200\"></strong><br>How many women entrepreneurs are NOT Indian?</p>",
                    question_hi: "<p>3. दिए गए वेन आरेख का अध्ययन करें और नीचे दिए गए प्रश्न का उत्तर दें<br><strong id=\"docs-internal-guid-a349e1e6-7fff-261c-91bc-fe99e848e912\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcaocgFmEiwH-kKH4yXtLn46iLapC2IF5HNabfqJDnA0nmIfB6J1-_HczNtwxN9vzGgSWzYg0IrhgLRYIpMWBBI7usEqFqc5TdNFthBEHOBatmAqNq5rXtSlCgOoUxaFLyJiI4y?key=OITYw97FLkMQoDclNr-C9DB4\" width=\"196\" height=\"210\"></strong><br>कितनी महिला उद्यमी भारतीय नहीं हैं?</p>",
                    options_en: ["<p>11</p>", "<p>17</p>", 
                                "<p>29</p>", "<p>18</p>"],
                    options_hi: ["<p>11</p>", "<p>17</p>",
                                "<p>29</p>", "<p>18</p>"],
                    solution_en: "<p>3.(d) The number of women entrepreneurs who are not Indian = 18</p>",
                    solution_hi: "<p>3.(d) महिला उद्यमियों की संख्या जो भारतीय नहीं हैं = 18</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "6",
                    question_en: "<p>4. Study the given Venn diagram and answer the question that follows.<br><strong id=\"docs-internal-guid-0fd2868b-7fff-9f84-65db-3b2699eea3bf\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXd_e4bgG_RSGXwwrSUr3afTdN9LpWJxmHqbaDVLckS6xB3jvGTIhLpt3EDFIe8Fvanz4eLC5h48OtG-8N-2y5_jNp8bbypxS0Jn75fq50xbxU1byrYk4IRAXy1PY5FQOT0qODwy?key=OITYw97FLkMQoDclNr-C9DB4\" width=\"192\" height=\"207\"></strong><br>How many smart people are polite ?</p>",
                    question_hi: "<p>4. दिए गए वेन आरेख का अध्ययन करें और नीचे दिए गए प्रश्न का उत्तर दें।<br><strong id=\"docs-internal-guid-7ff4bde9-7fff-ce17-bca4-20f20ccb6373\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfq2R1h_7GGUkQc52PFLUgjXdj0qygsU-mdWVHXgCJmw7cbyV23XTz3j3RD6wBIc_B6m7ROXk-u_GpQTOCMQTJ5wbP3j1kDBC2kMF8WeEa1a8CSQICPYfsTRD9mS4FA-JMgVwviTA?key=OITYw97FLkMQoDclNr-C9DB4\" width=\"198\" height=\"222\"></strong><br>कितने चतुर लोग विनम्र हैं?</p>",
                    options_en: ["<p>19</p>", "<p>63</p>", 
                                "<p>12</p>", "<p>7</p>"],
                    options_hi: ["<p>19</p>", "<p>63</p>",
                                "<p>12</p>", "<p>7</p>"],
                    solution_en: "<p>4.(a) Number of smart people who are polite = 12 + 7 = 19</p>",
                    solution_hi: "<p>4.(a) चतुर और विनम्र लोगों की संख्या = 12 + 7 = 19</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "6",
                    question_en: "<p>5. Select the Venn diagram that best illustrates the relationship between the following classes.<br>Men, Engineers and Fathers</p>",
                    question_hi: "<p>5. उस वेन आरेख का चयन करें जो निम्&zwj;नलिखित वर्गों के बीच के संबंध को सर्वोत्&zwj;तम रूप से दर्शाता है। पुरुष, इंजीनियर और पिता</p>",
                    options_en: ["<p><strong id=\"docs-internal-guid-46770f76-7fff-c86c-b84a-27a40c4aff52\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXefIxrCJnHFnj-ZzOw2cXwhqN8wx9qLQHi2svYOwORdIV12ynnc0NBxWabqKQ0-NK6FpuTTzbQ_A14OyRjvM5OLFMH1FNHOexPmSWOjIneiHoxsUX_ac1Ic8gAMuN0EW2_br41b?key=OITYw97FLkMQoDclNr-C9DB4\" width=\"100\" height=\"100\"></strong></p>", "<p><strong id=\"docs-internal-guid-7fbd31d2-7fff-11e9-db78-4863746c46e0\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcuzKV57y5OmuLAk1hGcQIpqjtJTqpPVU4A6Er-TRxsCMyzVncQIXcL-_lYPDBWwfAlYTIjS96kyT8bWx1stfeGhhnmaPvuvXJ3iCL9fkE2yH1tm4y8Aj4t5vCbQuA-fEbWMnZ9QA?key=OITYw97FLkMQoDclNr-C9DB4\" width=\"109\" height=\"109\"></strong></p>", 
                                "<p><strong id=\"docs-internal-guid-e9437494-7fff-3f74-d501-1437acee5f80\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfCCZTWM3kOHfJj2uYasZKxONqPtPj2CGTDM-CIqtfHi_t0ie-tGPV44CTUXXOI5BHa1YXbx4kpfla9MxXzoLz0ocrgDFnm2INh6iRN2UyiR2EonNlJ5W8rqCgSvKbHoMurOGKE6w?key=OITYw97FLkMQoDclNr-C9DB4\" width=\"112\" height=\"112\"></strong></p>", "<p><strong id=\"docs-internal-guid-7c9ef90a-7fff-930c-4c5c-070ef0f938a9\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfi2f-Im8Z5nBJjDF2-ZQouWKpCMWAMl0FuvHmII5rl0D40eXLMqo0Zpj77sFFF4eBkJ8biZevnWiXNPIF8TfXrmxzijQLCL2ypBirvp97L-BU_u57wRqLxI26WUHYfA_AsVhg-?key=OITYw97FLkMQoDclNr-C9DB4\" width=\"110\" height=\"110\"></strong></p>"],
                    options_hi: ["<p><strong id=\"docs-internal-guid-46770f76-7fff-c86c-b84a-27a40c4aff52\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXefIxrCJnHFnj-ZzOw2cXwhqN8wx9qLQHi2svYOwORdIV12ynnc0NBxWabqKQ0-NK6FpuTTzbQ_A14OyRjvM5OLFMH1FNHOexPmSWOjIneiHoxsUX_ac1Ic8gAMuN0EW2_br41b?key=OITYw97FLkMQoDclNr-C9DB4\" width=\"100\" height=\"100\"></strong></p>", "<p><strong id=\"docs-internal-guid-7fbd31d2-7fff-11e9-db78-4863746c46e0\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcuzKV57y5OmuLAk1hGcQIpqjtJTqpPVU4A6Er-TRxsCMyzVncQIXcL-_lYPDBWwfAlYTIjS96kyT8bWx1stfeGhhnmaPvuvXJ3iCL9fkE2yH1tm4y8Aj4t5vCbQuA-fEbWMnZ9QA?key=OITYw97FLkMQoDclNr-C9DB4\" width=\"109\" height=\"109\"></strong></p>",
                                "<p><strong id=\"docs-internal-guid-e9437494-7fff-3f74-d501-1437acee5f80\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfCCZTWM3kOHfJj2uYasZKxONqPtPj2CGTDM-CIqtfHi_t0ie-tGPV44CTUXXOI5BHa1YXbx4kpfla9MxXzoLz0ocrgDFnm2INh6iRN2UyiR2EonNlJ5W8rqCgSvKbHoMurOGKE6w?key=OITYw97FLkMQoDclNr-C9DB4\" width=\"112\" height=\"112\"></strong></p>", "<p><strong id=\"docs-internal-guid-7c9ef90a-7fff-930c-4c5c-070ef0f938a9\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfi2f-Im8Z5nBJjDF2-ZQouWKpCMWAMl0FuvHmII5rl0D40eXLMqo0Zpj77sFFF4eBkJ8biZevnWiXNPIF8TfXrmxzijQLCL2ypBirvp97L-BU_u57wRqLxI26WUHYfA_AsVhg-?key=OITYw97FLkMQoDclNr-C9DB4\" width=\"110\" height=\"110\"></strong></p>"],
                    solution_en: "<p>5.(b)<br><strong id=\"docs-internal-guid-02ef6f21-7fff-023f-d1c5-e3eb06768ab5\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXem-xW9nzN-XnHrk7OyyZzpP0dwZw3m3cywD3DssO2Qo59khEHJGd_NvikrFEKzYvsjis4xd4frQVuI_bdf-d47kao9mqSqn0pKk8dQgsOeKBiiY-XB-lQS3D5HtsR0mRGF98bu?key=OITYw97FLkMQoDclNr-C9DB4\" width=\"202\" height=\"152\"></strong></p>",
                    solution_hi: "<p>5.(b) <br><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1732168464968.png\" alt=\"rId17\" width=\"211\" height=\"156\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "6",
                    question_en: "<p>6. How many people like either only cars or only Volkswagen vehicles as per the given Venn diagram? <br><strong id=\"docs-internal-guid-0df18ca6-7fff-c079-5ce7-6a18a4983d77\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfxsdMqR7_5-4183E6PVqm09wUQum0047CQoQTw8qrbWkcU7iLyvOmMr0p8Bu7CfcRqEUpHS5Lo6VQoK07DUdS6PPFS5pME8B5o1w9MsuoDrv-aZoQX4eyQyPAoPY_xgyBlm4uHcA?key=OITYw97FLkMQoDclNr-C9DB4\" width=\"147\" height=\"164\"></strong></p>",
                    question_hi: "<p>6. दिए गए वेन आरेख के अनुसार कितने लोग या तो केवल कार या केवल फॉक्सवैगन वाहन पसंद करते हैं? <br><strong id=\"docs-internal-guid-a0508177-7fff-3bb3-ea37-83ed34be520c\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXeQhy-JEDXNUA1oYErqy2X8m0Cyo_eFAbnQXJqIFPArFsB69ya5MkP8a1ZvNimdTiVWOal-0Mg_pdJlMQyMNrfWKWklRF99w4nccNicaWje1D_74FmVhbnZmGkCobnQ2tzNO3yrgQ?key=OITYw97FLkMQoDclNr-C9DB4\" width=\"137\" height=\"160\"></strong><br>Black Vehicles - काला वाहन, Car कार, Volkswagon Vehicle - फॉक्सवैगन वाहन</p>",
                    options_en: ["<p>85</p>", "<p>66</p>", 
                                "<p>28</p>", "<p>75</p>"],
                    options_hi: ["<p>85</p>", "<p>66</p>",
                                "<p>28</p>", "<p>75</p>"],
                    solution_en: "<p>6.(a) The number of people likes either only cars or only Volkswagon = 47 + 38 = 85</p>",
                    solution_hi: "<p>6.(a) या तो केवल कारें या केवल वोक्सवैगन पसंद करने वाले लोगों की संख्या = 47 + 38 = 85</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "6",
                    question_en: "<p>7. Study the given Venn diagram and answer the question that follows.<br><strong id=\"docs-internal-guid-77816e53-7fff-d811-8529-ce67781be357\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXeYS8THx2bdPEAu1oMEXNSERIbSTjkyqcI3Sg4WA6ZJD-LnMaABkRG3sryRK_QoRu039HvGQEPwTf_SgJY8KuRPU2Itxd0WsIFTbuWUaQV0NQZWyGNl3JTuETb9mrAV9br6nGHGtA?key=OITYw97FLkMQoDclNr-C9DB4\" width=\"217\" height=\"213\"></strong><br>How many women are either smart or brave or both?</p>",
                    question_hi: "<p>7. दिए गए वेन आरेख का अध्ययन करें और नीचे दिए गए प्रश्न का उत्तर दें।<br><strong id=\"docs-internal-guid-fe894ea0-7fff-ff95-602d-06026d07eaab\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdML1wVYcvxdXo-qoKzqFjBiTrstPHevJA7CXdBiJM5s_qUR_DwnzTXq_uWWtB8ATjslfuWmEk_cexDl7yLzIIrF6fIFiuDSHyKVSRMELekQxdWKPQSPJ1KkGGbY7FbPF_pohNofQ?key=OITYw97FLkMQoDclNr-C9DB4\" width=\"199\" height=\"198\"></strong><br>कितनी महिलाएं या तो चतुर है या बहादुर हैं या दोनों हैं?</p>",
                    options_en: ["<p>27</p>", "<p>23</p>", 
                                "<p>30</p>", "<p>26</p>"],
                    options_hi: ["<p>27</p>", "<p>23</p>",
                                "<p>30</p>", "<p>26</p>"],
                    solution_en: "<p>7.(c) women are either smart or brave or both = 13 + 14 + 3 = 30</p>",
                    solution_hi: "<p>7.(c) महिलाएं या तो चतुर या बहादुर या दोनों हैं = 13 + 14 + 3 = 30</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "6",
                    question_en: "<p>8. How many people play either only volleyball or only chess as per the given Venn diagram?<br><strong id=\"docs-internal-guid-68baeed3-7fff-a3df-b82d-e9c6324ed5be\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXchQRQ7B0WHz50d1RZZWGzltniYVw-Etc_BMnh0JfqzrZtgB-cmp5731lLBUQXy_l_a_Fh7-IqXiqgr2-mdcrQUy8X7vTZUyYOcy96yEBu88_NJ_Q76tK_-IWRGApbGJv9efBjgLA?key=OITYw97FLkMQoDclNr-C9DB4\" width=\"241\" height=\"190\"></strong></p>",
                    question_hi: "<p>8. दिए गए वेन आरेख के अनुसार ऐसे कितने लोग हैं जो या तो केवल वॉलीबॉल या केवल शतरंज खेलते हैं?<br><strong id=\"docs-internal-guid-21f2e477-7fff-ceeb-98c9-cd04733d6bd5\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXccUKrDmFY5_t1WpPQHVedMotu8ZjmkcVAmnr0pXUvMSPX6w3QqNat4x_ycRKYovnMpmIcMngkGSp99E6u-5tn6dOchPdI5T8ebksqaVQOb0oZ7Tcvh3jUD2VwCBof_FWV9-gb4rA?key=OITYw97FLkMQoDclNr-C9DB4\" width=\"260\" height=\"198\"></strong></p>",
                    options_en: ["<p>81</p>", "<p>88</p>", 
                                "<p>15</p>", "<p>66</p>"],
                    options_hi: ["<p>81</p>", "<p>88</p>",
                                "<p>15</p>", "<p>66</p>"],
                    solution_en: "<p>8.(d) people who play either only volleyball or only chess <br>23 + 43 = 66</p>",
                    solution_hi: "<p>8.(d) जो लोग या तो केवल वॉलीबॉल या केवल शतरंज खेलते हैं <br>23 + 43 = 66</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "6",
                    question_en: "<p>9. The diagram given below has four different shapes, depicting different farmers of a small village, with different assets. The circle represents the farmers who own land of more than 100 yards, the pentagon represents farmers who own cows, the rhombus represents farmers who own goats, and the triangle represents farmers who own tractors.<br><strong id=\"docs-internal-guid-ac575d6f-7fff-418a-dc58-2e134b7a0807\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcGuqd6bK14uuVj-vwOkPXjaXqMwyusgaZCCLJH2JOt9PzrZtDDhWUsp5DwutiMKiOA0Fy9WiWDHpY4ceDxhOQ8tcF4tHfQk-SsUZ1Dd1TWeb07Z5lEi4Vr7hbvwUj9bn43vJhS?key=OITYw97FLkMQoDclNr-C9DB4\" width=\"223\" height=\"156\"></strong><br>How many farmers are there who have all four assets?</p>",
                    question_hi: "<p>9. नीचे दिए गए आरेख में चार अलग-अलग आकृतियाँ हैं, जो एक छोटे से गाँव के विभिन्न किसानों को अलग-अलग संपत्ति के साथ दर्शाती हैं। वृत्त उन किसानों का प्रतिनिधित्व करता है, जिनके पास 100 गज से अधिक की जमीन है, पंचभुज उन किसानों का प्रतिनिधित्व करता है, जिनके पास गाएं हैं, समचतुर्भुज उन किसानों का प्रतिनिधित्व करता है, जिनके पास बकरियां हैं और त्रिभुज उन किसानों का प्रतिनिधित्व करता है, जिनके पास ट्रैक्टर हैं।<br><strong id=\"docs-internal-guid-ac575d6f-7fff-418a-dc58-2e134b7a0807\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcGuqd6bK14uuVj-vwOkPXjaXqMwyusgaZCCLJH2JOt9PzrZtDDhWUsp5DwutiMKiOA0Fy9WiWDHpY4ceDxhOQ8tcF4tHfQk-SsUZ1Dd1TWeb07Z5lEi4Vr7hbvwUj9bn43vJhS?key=OITYw97FLkMQoDclNr-C9DB4\" width=\"223\" height=\"156\"></strong><br>ऐसे कितने किसान हैं, जिनके पास चारों संपत्तियां हैं?</p>",
                    options_en: ["<p>14</p>", "<p>30</p>", 
                                "<p>16</p>", "<p>21</p>"],
                    options_hi: ["<p>14</p>", "<p>30</p>",
                                "<p>16</p>", "<p>21</p>"],
                    solution_en: "<p>9.(c) Farmers who have all four assets are16.</p>",
                    solution_hi: "<p>9.(c) ऐसे किसान जिनके पास सभी चार संपत्तियाँ 16 हैं</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "6",
                    question_en: "<p>10. Study the given diagram carefully and answer the question that follows. The numbers in different sections indicate the number of students who like particular subjects.<br><br><strong id=\"docs-internal-guid-a3fa9bc0-7fff-e0a8-35a8-3b794de120ac\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXel2LCeqRRhxK-vhA7ZjnYJW5Nm0qs19aYr2OkvsBsOsrYqZK9_g7FtKDmLrl5d1rGV3zZ22rgwcg30J4uCZse7vAzYmU-17GcDYxLevjSWQ0SD4Yhwm2w_whm0DS4PLjCb4EBwzw?key=OITYw97FLkMQoDclNr-C9DB4\" width=\"193\" height=\"215\"></strong><br>How many students like Hindi and Science both, but NOT Maths?</p>",
                    question_hi: "<p>10. दिए गए आरेख का ध्यानपूर्वक अध्ययन कीजिए और नीचे दिए गए प्रश्न का उत्तर दीजिए। विभिन्न वर्गों वाली संख्याएँ उन छात्रों की संख्या दर्शाती हैं जो विशेष विषयों को पसंद करते हैं।<br><br><strong id=\"docs-internal-guid-2115d670-7fff-161e-ed26-1f3182dfd50a\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXf7CwaZrUQNwjpZ7MEJ5w2YaQvFQc8asdWNKzE4fobAXfMWvch1RB4pKfcCl73YaQ7sMadPvKcM31lK8qwv_3o4Me3pRMn3n9JAJs2I64byvQMxuyJMc0sOGBXhpt85DWE7nZJ83Q?key=OITYw97FLkMQoDclNr-C9DB4\" width=\"152\" height=\"178\"></strong><br>कितने छात्र हिंदी और विज्ञान दोनों पसंद करते हैं, लेकिन गणित को पसंद नहीं करते?</p>",
                    options_en: [" 12 ", " 11 ", 
                                " 4 ", " 10<br /> "],
                    options_hi: [" 12 ", " 11 ",
                                " 4 ", " 10"],
                    solution_en: "10.(c)<br />No. of students like Hindi and Science both but not maths = 4",
                    solution_hi: "10.(c)<br />हिंदी और विज्ञान दोनों पसंद करने वाले लेकिन गणित पसंद नहीं करने वाले छात्रों की संख्या = 4",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "6",
                    question_en: "<p>11. The following Venn diagram shows people\'s liking for Tea, Coffee and Soup.<br><strong id=\"docs-internal-guid-b669ee29-7fff-92df-06bd-d0507f9956d1\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdgdTiQwSfwUi97yfzJm5aRfXRW251xS23qT3OWi5YdTeckZpAilYF_l9-1qaC7kxrjvQvFnx0YSPCqCx30fShHnGJ3Q7Dg3zXEsH-9PjCKH2HXLNnKZq_-Li2pBRoSL2tVoFZJ?key=OITYw97FLkMQoDclNr-C9DB4\" width=\"248\" height=\"207\"></strong><br>How many people like tea and coffee both?</p>",
                    question_hi: "<p>11. नीचे दिया गया वेन आरेख चाय (Tea), कॉफी (Coffee) और सूप (Soup) के लिए लोगों की पसंद को दर्शाता है।<br><strong id=\"docs-internal-guid-4b234fbb-7fff-b279-c1ba-55bcdadc49ad\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcs3GSiQyE44qeHFekPvv1ETmbnjgIJgMQ8Ie1x2taWkjoJ39ToStG1u3UoSG10a_qbrqHoQTy_wRUftQjf1QzOafe0tQZtTMrRnxyfrW7mGbMlgCUFiMysPsCchXhjsxEOGhvyAQ?key=OITYw97FLkMQoDclNr-C9DB4\" width=\"214\" height=\"183\"></strong><br>Tea - चाय, Coffee - कॉफी, Soup - सूप<br>कितने लोग चाय और कॉफी दोनों पसंद करते हैं?</p>",
                    options_en: ["<p>43</p>", "<p>29</p>", 
                                "<p>21</p>", "<p>13</p>"],
                    options_hi: ["<p>43</p>", "<p>29</p>",
                                "<p>21</p>", "<p>13</p>"],
                    solution_en: "<p>11.(c)<br>Number of People like tea and coffee both = 13 + 8 = 21</p>",
                    solution_hi: "<p>11.(c)<br>चाय और कॉफ़ी दोनों पसंद करने वाले व्यक्तियों की संख्या = 13 + 8 = 21</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "6",
                    question_en: "<p>12. How many people like either only spicy or only sweet as per the given Venn diagram?<br><strong id=\"docs-internal-guid-406cc95b-7fff-5fcb-3879-a7dc74558764\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcurTwkHBPdiMAD-3ZXZHjyQFelA9YkJGFC_-OxmDnrHghxCZR4vRYhfKVpACNPxkAQq2crJsZFMEWr70PMM-aR0D4xaw3jTVTDKg0hHwXLjtP1ggL36xXiX2M8YSnPuBBJazimDQ?key=OITYw97FLkMQoDclNr-C9DB4\" width=\"208\" height=\"201\"></strong></p>",
                    question_hi: "<p>12. दिए गए वेन आरेख के अनुसार कितने लोग या तो केवल मसालेदार या केवल मीठा पसंद करते हैं?<br><strong id=\"docs-internal-guid-0f1b7197-7fff-8239-77c7-9ce63c706fe7\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfAQFMinBwMWNf724oI0KOPJECK_IJHHxkamtUFmPYJjzcDrMo7rN8OlgkyvX2FxhFwGLv77GIkIeDU_WIEN4AE8JqFVAOhR49B6BQ10OIqJ9rx0kBjNjhPeEltVBHjo_2fCeHYzw?key=OITYw97FLkMQoDclNr-C9DB4\" width=\"193\" height=\"203\"></strong><br>Sweet - मीठा, Sour - खट्टा, Spicy - मसालेदार</p>",
                    options_en: ["<p>110</p>", "<p>120</p>", 
                                "<p>135</p>", "<p>144</p>"],
                    options_hi: ["<p>110</p>", "<p>120</p>",
                                "<p>135</p>", "<p>144</p>"],
                    solution_en: "<p>12.(b)<br>No. of people like either only spicy or only sweet = 65 + 55 = 120</p>",
                    solution_hi: "<p>12.(b)<br>केवल मसालेदार या केवल मीठा पसंद करने वाले लोगों की संख्या = 65 + 55 = 120</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "6",
                    question_en: "<p>13. Study the given diagram carefully and answer the question that follows. The numbers in different sections indicate the number of persons who can drive different vehicles.<br><strong id=\"docs-internal-guid-3c11d866-7fff-1499-461f-f4ee4beaf4e7\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcGPx0moB8VTGa4r66G_7pR9e_CfHl6gqxCoSpI7lgKMyrPghFfpWrv2G6EZXiMeEdNlZZR3h7qXSpDi0HWOuZFOa3Zs0LhYrlJgyjQFE3QXd2Aw12faN7CtOGyeiGcI3v1lnTd?key=OITYw97FLkMQoDclNr-C9DB4\" width=\"140\" height=\"213\"></strong><br>What is the difference between the number of persons who can drive all the three vehicles and that of persons who can drive any two of these vehicles?</p>",
                    question_hi: "<p>13. दिए गए आरेख का ध्यानपूर्वक अध्ययन करें और निम्नलिखित प्रश्&zwj;न का उत्तर दें। विभिन्न खंडों में दी गई संख्याएं अलग-अलग वाहन चला सकने वाले व्यक्तियों की संख्या को दर्शाती हैं।<br><strong id=\"docs-internal-guid-0a5008b9-7fff-4102-dd08-59f653a6d248\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcaeETl8mJBrKowwwQytN31ptMeVrtMJUWiHk12iC44lvuhr9QqQIZ4qoe9KxEnspyJkNc_16oVJhhh3M_eI5u39hvQ-Xs40xkUP06kaxetLekOqnbzYFHTAU9g-PK-L5gskMhQOw?key=OITYw97FLkMQoDclNr-C9DB4\" width=\"145\" height=\"209\"></strong><br>सभी तीनों वाहन चला सकने वाले व्यक्तियों की संख्या और इनमें से कोई भी दो वाहन चला सकने वाले व्यक्तियों की संख्या के बीच का अंतर क्या है?</p>",
                    options_en: ["<p>27</p>", "<p>15</p>", 
                                "<p>12</p>", "<p>21</p>"],
                    options_hi: ["<p>27</p>", "<p>15</p>",
                                "<p>12</p>", "<p>21</p>"],
                    solution_en: "<p>13.(d)<br>Person who can drive all three vehicle = 6<br>Person who can drive any two of these vehicles = 12 + 8 + 7 = 27<br>Required difference = 27 - 6 = 21</p>",
                    solution_hi: "<p>13.(d)<br>व्यक्ति जो तीनों वाहन चला सकता है = 6<br>व्यक्ति जो इनमें से किन्हीं दो वाहनों को चला सकता है = 12 + 8 + 7 = 27<br>आवश्यक अंतर = 27 - 6 = 21</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "6",
                    question_en: "<p>14. Study the given diagram carefully and answer the question that follows. The numbers in different sections indicate the number of students who like to study different subjects. <br><strong id=\"docs-internal-guid-c8904421-7fff-c598-4e6f-5e27a2e9b4b4\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXchgW57Yh-UxFXEJN0HdWmyUzXz_pFChibVYcgCEJ9dL0DWs_2486uVLzaQJ3jKAemPGbnM2kcmgZA47jKSvPc6eKAuYklyIDRWUDXpsQfXW1jXE7rquCL-YnxHbLLqCym_7TpSpg?key=OITYw97FLkMQoDclNr-C9DB4\" width=\"243\" height=\"224\"></strong><br>What is the difference between the number of students who like to study all the three subjects and that of students who like to study biology only?</p>",
                    question_hi: "<p>14. दिए गए आरेख का ध्यानपूर्वक अध्ययन करें और निम्नलिखित प्रश्&zwj;न का उत्तर दें। विभिन्न खंडों में दी गई संख्याएं अलग-अलग विषयों का अध्ययन करना पसंद करने वाले छात्रों की संख्या को दर्शाती हैं।<br><strong id=\"docs-internal-guid-ca680992-7fff-0bad-7ce0-f03591c2453d\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfrIaJxOxpw4weNz2QDTh499cO_ajA-obsW3CWNG9KNDyxMQBuEMeZ8BOYwvzlcExUkeecZPkKBNRY-b9c0C1TX2mABQxsxNuenqLFfgk6r8zwKc5l3MBq3Qf-NZug_AHpLKxZf?key=OITYw97FLkMQoDclNr-C9DB4\" width=\"256\" height=\"204\"></strong><br>सभी तीनों विषयों का अध्ययन करना पसंद करने वाले छात्रों की संख्या और केवल जीव विज्ञान का अध्ययन करना पसंद करने वाले छात्रों की संख्या के बीच का अंतर क्या है?<br>Biology - जीव विज्ञान<br>Physics - भौतिक विज्ञान<br>Chemistry - रसायन विज्ञान</p>",
                    options_en: ["<p>16</p>", "<p>3</p>", 
                                "<p>19</p>", "<p>17</p>"],
                    options_hi: ["<p>16</p>", "<p>3</p>",
                                "<p>19</p>", "<p>17</p>"],
                    solution_en: "<p>14.(a)<br>No. of students who like to study all three subjects = 4<br>No. of students who like to study biology only = 20 <br>Required difference = 20 - 4 = 16</p>",
                    solution_hi: "<p>14.(a)<br>उन छात्रों की संख्या जो तीनों विषयों का अध्ययन करना पसंद करते हैं = 4<br>केवल जीव विज्ञान पढ़ना पसंद करने वाले छात्रों की संख्या = 20 <br>आवश्यक अंतर = 20 - 4 = 16</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "misc",
                    question_en: "<p>15. Study the given diagram carefully and answer the question that follows. The numbers in different sections indicate the number of persons travelling by different modes of transport.<br><strong id=\"docs-internal-guid-3f059f3f-7fff-65b0-0f66-1056e4b9bb01\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXc_PaHL2xWxmRy3KPuWatNfRJX83PCO9GUtyQrU8vAMaUnGLbhKQ8ClHCUwPJSnBOMteG5kNB1z46ZgEft8zRx8AeQe-nkRgGj7eoUUNqckYZpXC6EkwEkVMdSE4f-I-8rgNQTC7A?key=OITYw97FLkMQoDclNr-C9DB4\" width=\"176\" height=\"205\"></strong><br>How many people travel by bus and car both, but NOT by train?</p>",
                    question_hi: "<p>15. दिए गए आरेख का ध्यानपूर्वक अध्ययन कीजिए और नीचे दिए गए प्रश्न का उत्तर दीजिए। विभिन्न वर्गों वाली संख्याएँ परिवहन के विभिन्न साधनों से यात्रा करने वाले व्यक्तियों की संख्या को दर्शाती हैं।<br><strong id=\"docs-internal-guid-4fdf6959-7fff-923e-3e47-030d81d4109a\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXf7-ugLP0Gn5_i6VwqTvoOSMYtqCVhwjpk5tkLDrSWFl7-hnO8taYIa4zdl0sAWwvWqFLYtLBr-YWey_DS-yztP7KT98-860iDqiO0vSY1tqh3wrGlANiTpuTw0dhu5q8g4QBE6Nw?key=OITYw97FLkMQoDclNr-C9DB4\" width=\"171\" height=\"203\"></strong><br>कितने व्यक्ति बस और कार दोनों से यात्रा करते हैं, लेकिन ट्रेन से यात्रा नहीं करते?</p>",
                    options_en: ["<p>19</p>", "<p>27</p>", 
                                "<p>9</p>", "<p>17</p>"],
                    options_hi: ["<p>19</p>", "<p>27</p>",
                                "<p>9</p>", "<p>17</p>"],
                    solution_en: "<p>15.(c)<br>People travel by bus and car both, but Not by train = 9</p>",
                    solution_hi: "<p>15.(c)<br>लोग बस और कार दोनों से यात्रा करते हैं, लेकिन ट्रेन से नहीं = 9</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>