<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #7209b7;
            --secondary: #4361ee;
            --accent: #f72585;
            --success: #06d6a0;
            --warning: #ffd166;
            --danger: #ef476f;
            --light: #f8f9fa;
            --dark: #212529;
        }
        body {
            background-color: #f0f2f5;
            font-size: 16px;
            padding-bottom: 60px; /* Space for fixed navigation */
        }
        .navbar {
            background: linear-gradient(135deg, var(--primary), var(--secondary)) !important;
            box-shadow: 0 2px 10px rgba(0,0,0,.1);
            padding: 0.5rem 1rem;
        }
        .navbar-brand {
            font-size: 1.1rem;
            margin-right: 0;
        }
        .navbar .container-fluid {
            justify-content: space-between;
        }
        .controls-group {
            display: flex;
            align-items: center;
            flex-wrap: nowrap;
        }
        .timer-display {
            background: linear-gradient(135deg, #4361ee, #3a0ca3);
            color: white;
            border-radius: 50px;
            box-shadow: 0 3px 8px rgba(0,0,0,.1);
            padding: 0.25rem 0.75rem;
            font-size: 0.875rem;
            white-space: nowrap;
        }
        .nav-btn {
            padding: 0.25rem 0.75rem;
            font-size: 0.875rem;
            margin-left: 0.25rem;
        }
        .question-nav {
            position: fixed;
            top: 56px;
            right: 0;
            height: calc(100vh - 56px);
            width: 280px;
            transform: translateX(280px);
            transition: transform .3s;
            z-index: 100;
            background: #fff;
            box-shadow: -5px 0 15px rgba(0,0,0,.1);
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .main-content {
            transition: margin .3s;
            min-height: calc(100vh - 116px);
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.5;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(32px, 1fr));
            gap: 6px;
        }
        .q-box {
            width: 35px;
            height: 35px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 5px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid #dee2e6;
            background: #fff;
            transition: all .2s;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: 0 3px 5px rgba(0,0,0,.1);
        }
        .q-box.attempted {
            background-color: var(--secondary);
            color: white;
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,.15);
        }
        .card {
            border-radius: 12px;
            overflow: hidden;
            border: none;
            box-shadow: 0 4px 12px rgba(0,0,0,.08);
        }
        .option-label {
            border-radius: 8px;
            transition: all .2s;
            border: 1px solid #dee2e6;
            padding: 0.75rem 1rem;
            font-size: 1rem;
        }
        .option-label:hover {
            background-color: #f8f9fa;
            transform: translateY(-2px);
            box-shadow: 0 3px 8px rgba(0,0,0,.05);
        }
        .form-check-input:checked + .option-label {
            border-color: var(--primary);
            background-color: rgba(114,9,183,.05);
        }
        .btn-primary {
            background-color: var(--primary);
            border-color: var(--primary);
        }
        .btn-outline-primary {
            color: var(--primary);
            border-color: var(--primary);
        }
        .btn-outline-primary:hover {
            background-color: var(--primary);
            color: white;
        }
        /* Bottom fixed navigation for mobile */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: white;
            box-shadow: 0 -2px 10px rgba(0,0,0,.1);
            padding: 10px 15px;
            display: flex;
            justify-content: space-between;
            z-index: 90;
        }
        @media (min-width: 992px) {
            .main-content.with-nav {
                margin-right: 280px;
            }
            .question-nav {
                transform: translateX(0);
            }
            .nav-toggle-btn {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(30px, 1fr));
            }
            .option-label {
                padding: 0.625rem 0.875rem;
                font-size: 0.95rem;
            }
            .question-text {
                font-size: 1rem;
            }
            .navbar-brand {
                font-size: 1rem;
            }
            .timer-display {
                font-size: 0.8125rem;
                padding: 0.2rem 0.6rem;
            }
            .nav-btn {
                padding: 0.2rem 0.6rem;
                font-size: 0.8125rem;
            }
            .container {
                padding-left: 10px;
                padding-right: 10px;
            }
        }
    </style>
</head>
<body>
    <!-- Header navbar -->
    <nav class="navbar navbar-dark sticky-top">
        <div class="container-fluid px-2">
            <a class="navbar-brand fw-bold" href="#"><i class="fas fa-graduation-cap me-1"></i></a>
            <div class="controls-group">
                <!-- Timer -->
                <div class="timer-display me-1 fw-bold" id="timer">
                    <i class="far fa-clock me-1"></i><span id="timer-display">18:00</span>
                </div>
                <!-- Submit button -->
                <button class="btn btn-light btn-sm nav-btn me-1" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Language toggle -->
                <button class="btn btn-light btn-sm nav-btn me-1" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="btn btn-light btn-sm nav-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </nav>
    <!-- Main content -->
    <div class="main-content" id="main-content">
        <div class="container py-3">
            <!-- Question counter -->
            <div class="fw-bold text-center mb-3" id="question-counter">Question 1 of 25</div>
            <!-- Questions will be displayed here -->
            <div id="questions-container"></div>
        </div>
    </div>
    <!-- Bottom navigation for mobile -->
    <div class="bottom-nav d-lg-none">
        <button class="btn btn-outline-primary px-3" onclick="prevQuestion()">
            <i class="fas fa-chevron-left me-1"></i> Previous
        </button>
        <button class="btn btn-outline-primary px-3" onclick="nextQuestion()">
            Next <i class="fas fa-chevron-right ms-1"></i>
        </button>
    </div>
    <!-- Question navigation sidebar -->
    <div class="question-nav" id="question-nav">
        <div class="p-3">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h5 class="m-0">Questions</h5>
                <div>
                    <span class="badge bg-secondary me-1" id="attempted-count">0</span>
                    <span class="badge bg-secondary" id="total-count">25</span>
                    <button class="btn btn-sm btn-outline-secondary ms-2 d-lg-none" onclick="toggleNav()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
            <!-- Section selector moved here -->
            <div class="mb-3">
                <label for="section-selector" class="form-label small text-muted">Jump to Section</label>
                <select class="form-select form-select-sm" id="section-selector" onchange="switchSection(this.value)">
                    <option value="all">All Sections</option>
                </select>
            </div>
            <div class="question-grid" id="question-boxes"></div>
        </div>
    </div>
    <!-- Results modal -->
    <div class="modal fade" id="results-modal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content border-0">
                <div class="modal-header border-0" style="background:linear-gradient(135deg,var(--primary),var(--secondary));color:white">
                    <h5 class="modal-title">Test Results</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body p-4">
                    <div class="row text-center g-2">
                        <div class="col-6">
                            <div class="p-3 rounded shadow-sm" style="background:linear-gradient(135deg,#4361ee,#3a0ca3);color:white">
                                <h3 id="score-value" class="mb-0 fw-bold">0</h3>
                                <div class="small">Score</div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="p-3 rounded shadow-sm" style="background:linear-gradient(135deg,#06d6a0,#1b9aaa);color:white">
                                <h3 id="correct-value" class="mb-0 fw-bold">0</h3>
                                <div class="small">Correct</div>
                            </div>
                        </div>
                        <div class="col-6 mt-2">
                            <div class="p-3 rounded shadow-sm" style="background:linear-gradient(135deg,#ef476f,#f72585);color:white">
                                <h3 id="incorrect-value" class="mb-0 fw-bold">0</h3>
                                <div class="small">Incorrect</div>
                            </div>
                        </div>
                        <div class="col-6 mt-2">
                            <div class="p-3 rounded shadow-sm" style="background:linear-gradient(135deg,#6c757d,#495057);color:white">
                                <h3 id="unattempted-value" class="mb-0 fw-bold">0</h3>
                                <div class="small">Unattempted</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer border-0">
                    <button type="button" class="btn btn-primary" onclick="reviewTest()">Review Test</button>
                </div>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 18 * 60;
        let resultsModal;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            resultsModal = new bootstrap.Modal(document.getElementById('results-modal'));
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["6"] = {
                name: "Reasoning",
                start: 0,
                end: 23
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="6">Reasoning</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 24,
                end: 24
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "6",
                    question_en: " <p>1. In a certain language, CAPTAIN is written as 3172195. How will REFER be written in the same language? ",
                    question_hi: "<p><span style=\"font-family: Baloo;\">1. एक निश्चित भाषा में CAPTAIN को 3172195 लिखा जाता है | इसी भाषा में REFER को कैसे लिखा जाएगा ? </span></p>",
                    options_en: [" <p> 15651</span><span style=\"font-family:Times New Roman\">           </span></p>", " <p> 15659           </span></p>", 
                                " <p> 95659</span><span style=\"font-family:Times New Roman\">           </span></p>", " <p> 65956</span></p>"],
                    options_hi: ["<p>15651</p>", "<p>15659</p>",
                                "<p>95659</p>", "<p>65956</p>"],
                    solution_en: " <p><span style=\"font-family:Times New Roman\">(c) Here the pattern : positional values of alphabets are used and if there are two digits then sum of the digits are used.</span></p> <p><span style=\"font-family:Times New Roman\">CAPTAIN : 3, 1, (1+6), (2+0), 1, 9, (1+4) i.e. 3172195;</span></p> <p><span style=\"font-family:Times New Roman\">Similarly, REFER :(1+8), 5, 6, 5, (1+8) i.e 95659.</span></p>",
                    solution_hi: "<p><span style=\"font-family: Times New Roman;\">(c) यहाँ पैटर्न: अक्षरों के स्थितीय मानों का उपयोग किया जाता है और यदि दो अंक हैं तो अंकों के योग का उपयोग किया जाता है।</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">CAPTAIN : 3, 1, (1+6), (2+0), 1, 9, (1+4)अर्थात. 3172195;</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">उसी प्रकार, REFER :(1+8), 5, 6, 5, (1+8)अर्थात 95659.</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "6",
                    question_en: " <p>2. Find out the alternative figure which contains figure (X) as its part.</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1626668011/word/media/image16.png\"/><span style=\"font-family:Times New Roman\"> ",
                    question_hi: "<p><span style=\"font-family: Baloo;\">2. &#2311;&#2344; &#2357;&#2367;&#2325;&#2354;&#2381;&#2346;&#2379;&#2306; &#2350;&#2375;&#2306; &#2360;&#2375; &#2357;&#2379; &#2330;&#2367;&#2340;&#2381;&#2352; &#2338;&#2370;&#2306;&#2338;&#2375;&#2306; &#2332;&#2367;&#2360;&#2350;&#2375; &#2330;&#2367;&#2340;&#2381;&#2352; X &#2331;&#2369;&#2346;&#2366; &#2361;&#2369;&#2310; &#2361;&#2376; I</span></p>\r\n<p><span style=\"font-family: Times New Roman;\"> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1626668011/word/media/image16.png\"></p>\n",
                    options_en: [" <p> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1626668011/word/media/image19.png\"/></p>", " <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1626668011/word/media/image5.png\"/><span style=\"font-family:Times New Roman\">   </span></p>", 
                                " <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1626668011/word/media/image4.png\"/><span style=\"font-family:Times New Roman\">    </span></p>", " <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1626668011/word/media/image9.png\"/><span style=\"font-family:Times New Roman\"> </span></p>"],
                    options_hi: ["<p><img src=\"https://ssccglpinnacle.com/images/mceu_48897775411668861727462.png\" width=\"96\" height=\"90\"></p>\n", "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1626668011/word/media/image5.png\"></p>\n",
                                "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1626668011/word/media/image4.png\"></p>\n", "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1626668011/word/media/image9.png\"></p>\n"],
                    solution_en: " <p>(a)</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1626668011/word/media/image19.png\"/></p>",
                    solution_hi: "<p>(a)</p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1626668011/word/media/image19.png\"></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "6",
                    question_en: "<p>3. A figure of transparent sheet with a pattern is given below. Select the option that shows how the pattern would appear when the transparent sheet is folded at the dotted line.</p>\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1626668011/word/media/image24.png\"><span style=\"font-family: Times New Roman;\"> </span></p>",
                    question_hi: "<p><span style=\"font-family: Baloo;\">3. एक पैटर्न के साथ पारदर्शी शीट का एक आंकड़ा नीचे दिया गया है। बिंदीदार रेखा पर पारदर्शी शीट को </span><span style=\"font-family: Baloo;\">मोड़ने</span><span style=\"font-family: Baloo;\"> पर पैटर्न कैसे दिखाई देगा, इसके चार विकल्पों में से |</span></p>\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1626668011/word/media/image24.png\"></p>",
                    options_en: ["<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1626668011/word/media/image3.png\"></p>", "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1626668011/word/media/image8.png\"></p>", 
                                "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1626668011/word/media/image23.png\"></p>", "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1626668011/word/media/image22.png\"></p>"],
                    options_hi: ["<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1626668011/word/media/image3.png\"></p>", "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1626668011/word/media/image8.png\"></p>",
                                "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1626668011/word/media/image23.png\"></p>", "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1626668011/word/media/image22.png\"></p>"],
                    solution_en: "<p>(a)<span style=\"font-family: Times New Roman;\"> </span></p>\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1626668011/word/media/image3.png\"></p>",
                    solution_hi: "<p>(a)<span style=\"font-family: Times New Roman;\"> </span></p>\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1626668011/word/media/image3.png\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "6",
                    question_en: " <p>4.  Five articles, bat, ball, fan, table and chair, are kept one above the other (not necessarily in the same order). The bat is four places above the fan. The table is between the ball and the chair. The chair is three places below the bat.  Which article is at the second position from the top? ",
                    question_hi: "<p><span style=\"font-family: Baloo;\">4. पांच वस्तुओं यथा, बल्ला, गेंद, पंखा, मेज और कुर्सी, को एक के ऊपर एक रखा गया है लेकिन ज़रूरी नहीं कि इसी क्रम में | बल्ला पंखे से चार स्थान ऊपर है | मेज, गेंद तथा कुर्सी के बीच है | कुर्सी, बल्ले से तीन स्थान नीचे है | कौन सी वस्तु ऊपर से दूसरे स्थान पर है ? </span></p>",
                    options_en: [" <p>  Fan      </span></p>", " <p>  Table      </span></p>", 
                                " <p>  Ball      </span></p>", " <p>  Chair  </span></p>"],
                    options_hi: ["<p>पंखा</p>", "<p>मेज</p>",
                                "<p>गेंद</p>", "<p>कुर्सी</p>"],
                    solution_en: " <p><span style=\"font-family:Times New Roman\">(c)</span><span style=\"font-family:Times New Roman\">  As per given conditions, the following order is obtained:</span></p> <p><span style=\"font-family:Times New Roman\">BAT,  BALL, TABLE, CHAIR, FAN</span></p> <p><span style=\"font-family:Times New Roman\">Hence, Ball is at the second position from the top.</span></p>",
                    solution_hi: "<p><span style=\"font-family: Times New Roman;\">(c)</span><span style=\"font-family: Times New Roman;\"> दी गई शर्तों के अनुसार, निम्नलिखित आदेश प्राप्त होता है:</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">BAT, BALL, TABLE, CHAIR, FAN</span></p>\r\n<p>अतः गेंद ऊपर से दूसरे स्थान पर है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "6",
                    question_en: "<p>5. Select the set in which the numbers are related in the same way as are the numbers of the following set</p>\r\n<p><span style=\"font-weight: 400;\">&nbsp;(21, 84, 2)</span></p>",
                    question_hi: "<p><span style=\"font-family: Baloo;\">5. इनमे से वह सेट ढूंढें जिनमे संख्याएँ उसी तरह सम्बंधित हैं जिस तरह से प्रश्न सेट की संख्याएँ आपस में संबंधित हैं</span></p>\r\n<p><span style=\"font-family: Baloo;\"><span style=\"font-weight: 400;\">&nbsp;(21, 84, 2)</span></span></p>",
                    options_en: ["<p>(9,54,3)</p>", "<p>(63, 126,2)</p>", 
                                "<p>(13,104,8)</p>", "<p><span style=\"font-weight: 400;\">(18, 144, 3)</span></p>"],
                    options_hi: ["<p><span style=\"font-weight: 400;\">(9,54,3)&nbsp; &nbsp; </span></p>", "<p><span style=\"font-weight: 400;\">(63, 126,2)&nbsp; &nbsp; </span></p>",
                                "<p><span style=\"font-weight: 400;\">&nbsp;(13,104,8) &nbsp; </span></p>", "<p><span style=\"font-weight: 400;\">(18, 144, 3)</span></p>"],
                    solution_en: "<p>(a)<span style=\"font-family: Times New Roman;\"> Here, the logic: (1st term<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#215;</mo></math> </span><span style=\"font-family: Times New Roman;\">3rd term)<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#215;</mo></math></span><span style=\"font-family: Times New Roman;\">2= 2nd term</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Similarly, (9,54,3)=(9,9<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#215;</mo></math></span><span style=\"font-family: Times New Roman;\">3</span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#215;</mo></math><span style=\"font-family: \'Times New Roman\';\">2,3)</span></p>",
                    solution_hi: "<p>(a)<span style=\"font-family: Times New Roman;\"> यहाँ, तर्क: (पहला पद &times; तीसरा पद) &times;2= दूसरा पद</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">उसी प्रकार, (9,54,3)=(9,9<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#215;</mo></math></span><span style=\"font-family: Times New Roman;\">3</span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#215;</mo></math><span style=\"font-family: \'Times New Roman\';\">2,3)</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "6",
                    question_en: " <p>6. Three of the four letter clusters are alike in a certain way and one is different, find the odd one from the options. ",
                    question_hi: "<p><span style=\"font-family: Baloo;\">6. दिए हुए अक्षर समूहों में से तीन अक्षर समूह एक ही प्रकार से सम्बंधित हैं जबकि एक अक्षर समूह भिन्न है उसे पहचाने |</span></p>",
                    options_en: [" <p> SVYB</span></p>", " <p> AXUR          </span></p>", 
                                " <p> MRWB        </span></p>", " <p> </span><span style=\"font-family:Times New Roman\">LRVA</span></p>"],
                    options_hi: ["<p>SVYB</p>", "<p>AXUR</p>",
                                "<p>MRWB</p>", "<p><span style=\"font-family: Times New Roman;\">LRVA</span></p>"],
                    solution_en: "<p><span style=\"font-family: Times New Roman;\">(d) Here, the logic: gaps between the alphabets are odd except option (d).</span></p>",
                    solution_hi: "<p><span style=\"font-family: Times New Roman;\">(d) यहां, तर्क: विकल्प (d) को छोड़कर अक्षरों के बीच अंतराल विषम हैं।</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "6",
                    question_en: " <p>7. Three of the following four numbers are alike in a certain way and one is different. Pick the number that is different. ",
                    question_hi: "<p><span style=\"font-family: Baloo;\">7. निम्नलिखित चार में से तीन संख्याएं किसी निश्चित प्रकार से एक जैसी हैं और एक अलग है | उस संख्या का चयन करें जो अलग है </span></p>",
                    options_en: [" <p>123</span></p>", " <p>789</span></p>", 
                                " <p>483</span></p>", " <p>567</span></p>"],
                    options_hi: ["<p>123</p>", "<p>789</p>",
                                "<p>483</p>", "<p>567</p>"],
                    solution_en: " <p><span style=\"font-family:Times New Roman\">(c) The logic: Except 483, in all others , the sum of digits is as 6 , 24 , 18 , i.e. , divisible by 6. But in 483 , the sum is 15, not divisible by 6. So, it is an odd one out.</span></p>",
                    solution_hi: "<p><span style=\"font-family: Times New Roman;\">(c) तर्क: 483 को छोड़कर, अन्य सभी में, अंकों का योग 6, 24, 18, यानी 6 से विभाज्य है, लेकिन 483 में, योग 15 है, 6 से विभाज्य नहीं है। तो, यह एक विषम है।&nbsp;</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "6",
                    question_en: "<p>8. D is the son of C and brother of E. E is the niece of F. C is the sister of B and aunt of A. The father of B has two kids, a son and a daughter. If A is the son of F , then how is F related to D?</p>\n",
                    question_hi: "<p><span style=\"font-family: Baloo;\">8. D, C &#2325;&#2366; &#2348;&#2375;&#2335;&#2366; &#2361;&#2376; &#2324;&#2352; E &#2325;&#2366; &#2349;&#2366;&#2312; &#2361;&#2376; | E, F &#2325;&#2368; &#2349;&#2340;&#2368;&#2332;&#2368; &#2361;&#2376; | C, B &#2325;&#2368; &#2348;&#2361;&#2344; &#2324;&#2352; A &#2325;&#2368; &#2310;&#2306;&#2335;&#2368; &#2361;&#2376; | B &#2325;&#2375; &#2346;&#2367;&#2340;&#2366; &#2325;&#2375; &#2342;&#2379; &#2348;&#2330;&#2381;&#2330;&#2375; &#2361;&#2376;&#2306; - &#2319;&#2325; &#2348;&#2375;&#2335;&#2366; &#2324;&#2352; &#2319;&#2325; &#2348;&#2375;&#2335;&#2368; | &#2351;&#2342;&#2367; A, F &#2325;&#2366; &#2348;&#2375;&#2335;&#2366; &#2361;&#2376;, &#2340;&#2379; F, D &#2360;&#2375; &#2325;&#2367;&#2360; &#2346;&#2381;&#2352;&#2325;&#2366;&#2352; &#2360;&#2306;&#2348;&#2306;&#2343;&#2367;&#2340; &#2361;&#2376; ? </span></p>\n",
                    options_en: ["<p>Cousin</p>\n", "<p>Aunt</p>\n", 
                                "<p>Maternal Aunt</p>\n", "<p>Sister</p>\n"],
                    options_hi: ["<p>&#2330;&#2330;&#2375;&#2352;&#2366;/ &#2350;&#2380;&#2360;&#2375;&#2352;&#2366; &#2349;&#2366;&#2312; /&#2348;&#2361;&#2344;</p>\n", "<p>&#2310;&#2306;&#2335;&#2368;</p>\n",
                                "<pre id=\"tw-target-text\" class=\"tw-data-text tw-text-large tw-ta\" data-placeholder=\"Translation\"><span class=\"Y2IQFc\" lang=\"hi\">&#2350;&#2366;&#2350;&#2368;</span></pre>\n", "<p>&#2348;&#2361;&#2344;</p>\n"],
                    solution_en: "<p><span style=\"font-family: Times New Roman;\">(c)As the statements are given in the question, we get the following diagram.</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1626668011/word/media/image33.png\"></p>\r\n<p><span style=\"font-family: Times New Roman;\">According to the family chart, F is Maternal aunt of D.</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Times New Roman;\">(c)&#2332;&#2376;&#2360;&#2366; &#2325;&#2367; &#2346;&#2381;&#2352;&#2358;&#2381;&#2344; &#2350;&#2375;&#2306; &#2325;&#2341;&#2344; &#2342;&#2367;&#2319; &#2327;&#2319; &#2361;&#2376;&#2306;, &#2361;&#2350;&#2375;&#2306; &#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340; &#2310;&#2352;&#2375;&#2326; &#2346;&#2381;&#2352;&#2366;&#2346;&#2381;&#2340; &#2361;&#2379;&#2340;&#2366; &#2361;&#2376;&#2404;</span></p>\r\n<p><span style=\"font-family: Times New Roman;\"><img src=\"https://ssccglpinnacle.com/images/mceu_52669375211668861008893.png\"></span></p>\r\n<p>&#2346;&#2352;&#2367;&#2357;&#2366;&#2352; &#2330;&#2366;&#2352;&#2381;&#2335; &#2325;&#2375; &#2309;&#2344;&#2369;&#2360;&#2366;&#2352;, F, D &#2325;&#2368;&nbsp;<span class=\"Y2IQFc\" lang=\"hi\">&#2350;&#2366;&#2350;&#2368;</span></p>\r\n<p>&nbsp;&#2361;&#2376;&#2404;</p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "6",
                    question_en: "<p>9. Arrange the following words in a logical and meaningful manner</p>\r\n<p><span style=\"font-family: Times New Roman;\">1. Snake</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">2.Mongoose</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">3. Frog </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">4.Moth </span></p>",
                    question_hi: "<p><span style=\"font-family: Baloo;\">9. दिए हुए शब्दों को एक तार्किक और बोधगम्य तरीके से सजाएँ </span></p>\r\n<p><span style=\"font-family: Baloo;\">1. सांप</span></p>\r\n<p><span style=\"font-family: Baloo;\">2. नेवला</span></p>\r\n<p><span style=\"font-family: Baloo;\">3. मेंढक</span></p>\r\n<p><span style=\"font-family: Baloo;\">4. कीट</span></p>",
                    options_en: ["<p>(1,2,3,4)</p>", "<p>(4,3,1,2)</p>", 
                                "<p>(3,2,1,4)</p>", "<p><span style=\"font-weight: 400;\">(1,4,2,3)</span></p>"],
                    options_hi: ["<p><span style=\"font-weight: 400;\">(1,2,3,4)&nbsp;</span><span style=\"font-weight: 400;\"> </span></p>", "<p><span style=\"font-weight: 400;\">(4,3,1,2)&nbsp; &nbsp; &nbsp; </span></p>",
                                "<p><span style=\"font-weight: 400;\">(3,2,1,4) </span></p>", "<p><span style=\"font-weight: 400;\">(1,4,2,3)</span></p>"],
                    solution_en: "<p><span style=\"font-family: Times New Roman;\">(b) Here, the food chain is considered. Mongoose kills snake, snake kills frog, frog eats moth.</span></p>\r\n<p><span style=\"font-family: Times New Roman;\"> Moth<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8594;</mo></math></span><span style=\"font-family: Times New Roman;\">frog<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8594;</mo></math></span><span style=\"font-family: Times New Roman;\">snake<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8594;</mo></math></span><span style=\"font-family: Times New Roman;\">Mongoose</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">So , (b) is the correct option.</span></p>",
                    solution_hi: "<p><span style=\"font-family: Times New Roman;\">(b) यहां, खाद्य श्रृंखला पर विचार किया जाता है। नेवला सांप को मारता है, सांप मेंढक को मारता है, मेंढक कीट को खाता है।</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">कीट<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8594;</mo></math><span style=\"font-family: Baloo;\">मेंढक</span></span><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8594;</mo></math><span style=\"font-family: Baloo;\">सांप</span></span><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8594;</mo></math><span style=\"font-family: Baloo;\">नेवला</span></span></p>\r\n<p>तो, (b) सही विकल्प है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "6",
                    question_en: "<p>10. Select the venn diagram that represents the correct relationship between the following classes.</p>\n<p><span style=\"font-family: Times New Roman;\">Pentagon , Octagon , Figure </span></p>",
                    question_hi: "<p><span style=\"font-family: Baloo;\">10. उस वेन आरेख का चयन करें जो निम्नलिखित वर्गों के बीच सही संबंध को दर्शाता है |</span></p>\n<p><span style=\"font-family: Baloo;\">पंचभुज, </span><span style=\"font-family: Baloo;\">अष्टभुज</span><span style=\"font-family: Baloo;\">, आकृति </span></p>",
                    options_en: ["<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1626668011/word/media/image13.png\"></p>", "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1626668011/word/media/image30.png\"></p>", 
                                "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1626668011/word/media/image18.png\"></p>", "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1626668011/word/media/image28.png\"></p>"],
                    options_hi: ["<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1626668011/word/media/image13.png\"></p>", "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1626668011/word/media/image30.png\"></p>",
                                "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1626668011/word/media/image18.png\"></p>", "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1626668011/word/media/image28.png\"></p>"],
                    solution_en: "<p>(a)<span style=\"font-family: Times New Roman;\"> Pentagon and Octagon both are figures.</span></p>\n<p><strong id=\"docs-internal-guid-05edfe5c-7fff-2cb1-123a-381b9cdfd6c2\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXc3CVLxNYDGsQrT5iJqtH6g_B5_sU-0srsBzX5kg7F73M7cpu9WMvo-0T_yheAdO0MTsqAp6OBM03EalLVx3Sibm_SZAnJQGBSeWvC5fBrHRBoMI5Zu5b3D6DxWwTq2dhpKr_1P7g?key=u3-Gnt1USFjFx8h4QpKY6y93\" width=\"92\" height=\"90\"></strong></p>\n<p><span style=\"font-family: Times New Roman;\"> </span></p>",
                    solution_hi: "<p>(a)<span style=\"font-family: Times New Roman;\"> पेंटागन और अष्टागन &nbsp;दोनों आकृतियाँ हैं।</span></p>\n<p><strong id=\"docs-internal-guid-9fd1f1ad-7fff-5209-ed43-f6063f0803ae\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdJbed7gGW4mIDCaNqAqi4lxjQNCuKEJ4uYXeUTG1Z4IE_uhLLk-juJiCR6s7oPYDxsOjvLTjoT1yIg0xHQzhh3b4CVsIwv4xuEnXkuygm7MzWRvNEeXv3y3AN-0HC8wJ7zxNzu?key=u3-Gnt1USFjFx8h4QpKY6y93\" width=\"84\" height=\"82\"></strong></p>\n<p><span style=\"font-family: Times New Roman;\"> </span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "6",
                    question_en: "<p>11. Two different positions of the same dice are shown. Which number will be at the top if 2 is at the bottom?</p>\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1626668011/word/media/image17.png\"><span style=\"font-family: Times New Roman;\"> </span></p>",
                    question_hi: "<p><span style=\"font-family: Baloo;\">11. एक ही पासे की दो अलग अलग अवस्थाएं दी गयी हैं | शीर्ष पर कौन सी संख्या होगी यदि तल पर 2 है ?</span></p>\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1626668011/word/media/image17.png\"></p>",
                    options_en: ["<p>5<span style=\"font-family: Times New Roman;\"> </span></p>", "<p>2<span style=\"font-family: Times New Roman;\"> </span></p>", 
                                "<p>4</p>", "<p>6</p>"],
                    options_hi: ["<p>5</p>", "<p>2</p>",
                                "<p>4</p>", "<p>6</p>"],
                    solution_en: "<p><span style=\"font-family: Times New Roman;\">(d)</span></p>\n<p><img src=\"admin/resources/qdocs/test-1-EN-1/1626668011/word/media/image34.png\"></p>\n<p><span style=\"font-family: Times New Roman;\">Here 3 is common to both dice, then move clockwise or anticlockwise from 3. We can conclude </span></p>\n<p><span style=\"font-family: Times New Roman;\">1<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8596;</mo></math></span><span style=\"font-family: Times New Roman;\">4, 6</span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8596;</mo></math><span style=\"font-family: \'Times New Roman\';\">2.</span></p>",
                    solution_hi: "<p><span style=\"font-family: Times New Roman;\">(d)</span></p>\n<p><img src=\"admin/resources/qdocs/test-1-EN-1/1626668011/word/media/image34.png\"></p>\n<p>यहाँ 3 दोनों पासों के लिए उभयनिष्ठ है, फिर 3 से दक्षिणावर्त या वामावर्त घुमाएँ। हम निष्कर्ष निकाल सकते हैं</p>\n<p><span style=\"font-family: Times New Roman;\">1<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8596;</mo></math></span><span style=\"font-family: Times New Roman;\">4, 6</span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8596;</mo></math><span style=\"font-family: \'Times New Roman\';\">2.</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "6",
                    question_en: " <p>12. ‘PROFESSOR’ is related to ‘EDUCATION’ in the same way as ‘LAWYER’ is related to  ",
                    question_hi: "<p><span style=\"font-family: Baloo;\">12. &lsquo;प्रोफेसर&rsquo; , &lsquo;शिक्षा&rsquo; से उसी तरह संबंधित है जिस तरह &lsquo;वकील&rsquo; संबंधित है ?</span></p>",
                    options_en: [" <p> Judge              </span></p>", " <p> Justice</span></p>", 
                                " <p> Arbitration           </span></p>", " <p> Communication</span></p>"],
                    options_hi: ["<p>न्यायाधीश</p>", "<p>न्याय</p>",
                                "<p>मध्यस्तता</p>", "<p>सम्प्रेषण</p>"],
                    solution_en: "<p><span style=\"font-family: Times New Roman;\">(b)&lsquo;Professor&rsquo; is associated with imparting &lsquo;Education&rsquo;, similarly, &lsquo;Lawyer&rsquo; is associated with seeking &lsquo;Justice&rsquo;.</span></p>",
                    solution_hi: "<p><span style=\"font-family: Times New Roman;\">(b)\'प्रोफेसर\' \'शिक्षा\' देने से जुड़ा है, इसी तरह, \'वकील\' \'न्याय\' मांगने से जुड़ा है।</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "6",
                    question_en: " <p>13. Three of the following four words are alike in the same way while one is odd, find the odd one out ",
                    question_hi: "<p><span style=\"font-family: Baloo;\">13. निम्नलिखित चार शब्दों में से तीन एक ही तरह से समान हैं जबकि एक विषम है, विषम का पता लगाएं|</span></p>",
                    options_en: [" <p>Barrier    </span></p>", " <p>Boulder</span></p>", 
                                " <p>Rock</span></p>", " <p>Ascetic </span></p>"],
                    options_hi: ["<p>अवरोध</p>", "<p>शिलाखंड</p>",
                                "<p>चट्टान</p>", "<p>तपस्वी</p>"],
                    solution_en: "<p><span style=\"font-family: Times New Roman;\">(d)&lsquo;Ascetic&rsquo; means one who is severely self disciplined, all other words are something which prevent people from moving ahead.</span></p>",
                    solution_hi: "<p><span style=\"font-family: Times New Roman;\">(d)तपस्वी का अर्थ है वह जो गंभीर रूप से आत्म अनुशासित हो, अन्य सभी शब्द कुछ ऐसे हैं जो लोगों को आगे बढ़ने से रोकते हैं।</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "6",
                    question_en: "<p>14. Three statements are given, followed by three conclusions numbered I, II and III. Assuming the statements to be true, even if they seem to be at variance with commonly known facts,decide which of the <span style=\"font-family: Times New Roman;\">conclusion</span><span style=\"font-family: Times New Roman;\"> logically follows from the statements.</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Statements:</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">All rocks are oceans.</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">All oceans are balloons.</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Some watches are balloons.</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Conclusions:</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">I.</span><span style=\"font-family: Times New Roman;\">Some oceans are balloons.</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">II.Some watches are oceans.</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">III.All oceans are rocks. </span></p>",
                    question_hi: "<p><span style=\"font-family: Baloo;\">14. तीन कथन दिए गए हैं </span><span style=\"font-family: Baloo;\">जिनके</span><span style=\"font-family: Baloo;\"> बाद तीन निष्कर्ष I, II और III हैं | इन कथनों को सही मानें, भले ही वे सामान्य रूप से ज्ञात तथ्यों से भिन्न प्रतीत होते हैं | बताएं कि इन कथनों से कौन सा निष्कर्ष तार्किक रूप से निकाला जा सकता है ?</span></p>\r\n<p><span style=\"font-family: Baloo;\">कथन :</span></p>\r\n<p><span style=\"font-family: Baloo;\">सभी चट्टानें सागर हैं | </span></p>\r\n<p><span style=\"font-family: Baloo;\">सभी सागर गुब्बारे हैं | </span></p>\r\n<p><span style=\"font-family: Baloo;\">कुछ घड़ियाँ गुब्बारा हैं | </span></p>\r\n<p><span style=\"font-family: Baloo;\">निष्कर्ष :</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">I.</span><span style=\"font-family: Baloo;\"> कुछ सागर गुब्बारे हैं | </span></p>\r\n<p><span style=\"font-family: Baloo;\">II.कुछ घड़ियाँ सागर हैं | </span></p>\r\n<p><span style=\"font-family: Baloo;\">III. सभी सागर चट्टान हैं | </span></p>",
                    options_en: ["<p>Only conclusion II follows.</p>", "<p>Only conclusions I and III follow.</p>", 
                                "<p>Only conclusions I and II follow.</p>", "<p>Only conclusion I follows.</p>"],
                    options_hi: ["<p>केवल निष्कर्ष II सही है |</p>", "<p>केवल निष्कर्ष I और III सही है |</p>",
                                "<p>केवल निष्कर्ष I और II सही है |</p>", "<p>केवल निष्कर्ष I सही है |</p>"],
                    solution_en: "<p><span style=\"font-family: Times New Roman;\">(d)</span><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Times New Roman;\">Balloons and rocks have a relation of SOME with rocks, so conc. I is true but III is false. II is also false as watches don&rsquo;t have any relation with oceans. </span></p>\r\n<p><img src=\"admin/resources/qdocs/test-1-EN-1/1626668011/word/media/image12.png\" /></p>",
                    solution_hi: "<p><span style=\"font-family: Times New Roman;\">(d)गुब्बारों और चट्टानों का चट्टानों के साथ कुछ का संबंध है, इसलिए संक्षिप्त। I सत्य है लेकिन III गलत है। II भी गलत है क्योंकि घड़ियों का महासागरों से कोई संबंध नहीं है।</span></p>\r\n<p><img src=\"admin/resources/qdocs/test-1-EN-1/1626668011/word/media/image12.png\" /></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "6",
                    question_en: " <p>15. Select the option that is related with third letter cluster in the same way as the the second letter cluster is related to first letter cluster:</span></p> <p><span style=\"font-family:Times New Roman\">UQZX : WOBV :: AMNL : ? ",
                    question_hi: "<p><span style=\"font-family: Baloo;\">15. उस विकल्प का चयन करें जो तीसरे अक्षर समूह से ठीक उसी प्रकार संबंधित है जिस प्रकार दूसरा अक्षर समूह पहले अक्षर समूह से संबंधित है | </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">UQZX : WOBV :: AMNL : ?</span></p>",
                    options_en: [" <p> CKLP</span><span style=\"font-family:Times New Roman\"> </span></p>", " <p> CKPJ           </span></p>", 
                                " <p> </span><span style=\"font-family:Times New Roman\">CKPQ</span><span style=\"font-family:Times New Roman\">         </span></p>", " <p> CKPI</span></p>"],
                    options_hi: ["<p>CKLP</p>", "<p>CKPJ</p>",
                                "<p><span style=\"font-family: Times New Roman;\">CKPQ</span></p>", "<p>CKPI</p>"],
                    solution_en: " <p><span style=\"font-family:Times New Roman\">(b)Here, +2, -2 , +2,-2.. Rules are used.</span></p> <p><span style=\"font-family:Times New Roman\">UQZX</span><span style=\"font-family:Times New Roman\"> = WOBV, U+2=W, then, Q-2=O, Z+2=B and X - 2 = V </span></p> <p><span style=\"font-family:Times New Roman\">Thus, code for AMNL will be CKPJ.</span></p>",
                    solution_hi: "<p><span style=\"font-family: Times New Roman;\">(b)यहां, +2, -2 , +2,-2.. नियमों का प्रयोग किया जाता है।</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">UQZX</span><span style=\"font-family: Times New Roman;\"> = WOBV, U+2=W,फिर, Q-2=O, Z+2=B और&nbsp; X - 2 = V </span></p>\r\n<p>अत: AMNL का कोड CKPJ होगा।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "6",
                    question_en: "<p>16. <span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Times New Roman;\">Select the combinations of letters that when sequentially placed in the gaps of the given letter series will complete the series.</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">ab_db_abc_ba_bcd_a </span></p>",
                    question_hi: "<p><span style=\"font-family: Baloo;\">16. अक्षरों के उस संयोजन का चयन करें जिसे दी </span><span style=\"font-family: Baloo;\">गयी</span><span style=\"font-family: Baloo;\"> अक्षर श्रृंखला के खाली स्थानों में </span><span style=\"font-family: Baloo;\">क्रमिक</span><span style=\"font-family: Baloo;\"> रूप से रखने पर यह श्रृंखला पूरी हो जाएगी ? </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">ab_db_abc_ba_bcd_a</span></p>",
                    options_en: ["<p>aadab</p>", "<p>cabde</p>", 
                                "<p>cadab</p>", "<p>dcbcb</p>"],
                    options_hi: ["<p>aadab</p>", "<p>cabde</p>",
                                "<p>cadab</p>", "<p>dcbcb</p>"],
                    solution_en: "<p><span style=\"font-family: Times New Roman;\">(c) Here, the repetitive series is following as: </span><span style=\"font-family: Times New Roman;\">ab</span><span style=\"font-family: Times New Roman;\">c</span><span style=\"font-family: Times New Roman;\">db</span><span style=\"font-family: Times New Roman;\">a</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">required series will be as : ab</span><span style=\"font-family: Times New Roman;\">c</span><span style=\"font-family: Times New Roman;\">db</span><span style=\"font-family: Times New Roman;\">a</span><span style=\"font-family: Times New Roman;\">/</span><span style=\"font-family: Times New Roman;\">abc</span><span style=\"font-family: Times New Roman;\">d</span><span style=\"font-family: Times New Roman;\">ba/ </span><span style=\"font-family: Times New Roman;\">a</span><span style=\"font-family: Times New Roman;\">bcd</span><span style=\"font-family: Times New Roman;\">b</span><span style=\"font-family: Times New Roman;\">a </span><span style=\"font-family: Times New Roman;\">. </span><span style=\"font-family: Times New Roman;\">So the required code is : </span><span style=\"font-family: Times New Roman;\">cadab</span><span style=\"font-family: Times New Roman;\">.</span></p>",
                    solution_hi: "<p><span style=\"font-family: Times New Roman;\">(c)&nbsp;</span></p>\r\n<p>यहाँ, दोहराई जाने वाली श्रृंखला इस प्रकार है: abcdba</p>\r\n<p>आवश्यक श्रृंखला इस प्रकार होगी: abcdba/abcdba/abcdba। तो आवश्यक कोड है : cadab.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "6",
                    question_en: " <p>17. </span><span style=\"font-family:Times New Roman\">Study the given pattern carefully and select the letter that can replace the question mark (?) in it. </span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1626668011/word/media/image27.png\"/><span style=\"font-family:Times New Roman\"> ",
                    question_hi: "<p><span style=\"font-family: Baloo;\">17. दिए गए पैटर्न का ध्यानपूर्वक अध्ययन करें और उस अक्षर का चयन करें जो उसमें प्रश्नवाचक चिह्न (?) को प्रतिस्थापित कर सके।</span></p>\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1626668011/word/media/image27.png\"></p>",
                    options_en: [" <p> S</span></p>", " <p> K</span></p>", 
                                " <p> J</span></p>", " <p> R</span></p>"],
                    options_hi: ["<p>S</p>", "<p>K</p>",
                                "<p>J</p>", "<p>R</p>"],
                    solution_en: "<p><span style=\"font-family: Times New Roman;\">(c) </span><span style=\"font-family: Times New Roman;\">The sum of the alphabet numbers of letters which are vertically opposite to each other is 19.</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">A + R = 19, G + L = 19</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Similarly, I + J = 19</span></p>",
                    solution_hi: "<p><span style=\"font-family: Times New Roman;\">(c) एक दूसरे के लंबवत विपरीत अक्षरों की वर्णमाला संख्याओं का योग 19 है।</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">A + R = 19, G + L = 19</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">इसी तरह, I + J = 19</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "6",
                    question_en: " <p>18. In a certain code language, ‘98563’ is written as ‘32896’. What will be the code for ‘72104’ in that code language? ",
                    question_hi: "<p><span style=\"font-family: Baloo;\">18. यदि एक निश्चित कोड भाषा में 98563 को 32896 लिखा जाता है, तो उस भाषा में 72104 &rsquo;कैसे लिखा जाएगा?</span></p>",
                    options_en: [" <p> 24650</span></p>", " <p> 21362</span></p>", 
                                " <p> 15495           </span></p>", " <p> 15437</span></p>"],
                    options_hi: ["<p>24650</p>", "<p>21362</p>",
                                "<p>15495</p>", "<p>15437</p>"],
                    solution_en: "<p><span style=\"font-family: Times New Roman;\">(d) Here, numbers greater than 6 are subtracted by 6 and smaller than or equal to 6 are added by 3.</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">9 - 6 = 3&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 7 - 6 = 1</span><span style=\"font-family: Times New Roman;\">&nbsp;</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">8 - 6 = 2&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 2 + 3 = 5&nbsp; &nbsp; &nbsp; &nbsp;&nbsp;</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">5 + 3 = 8&nbsp; Similarly,&nbsp; &nbsp;1 + 3 = 4</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">6 + 3 = 9&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 0 + 3 = 3</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">3 + 3 = 6&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 4 + 3 = 7</span></p>",
                    solution_hi: "<p><span style=\"font-family: Times New Roman;\">(d) यहाँ, 6 से बड़ी संख्याओं को 6 से घटाया जाता है और 6 से छोटी या उसके बराबर संख्या को 3 से जोड़ा जाता है।</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">9 - 6 = 3&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 7 - 6 = 1</span><span style=\"font-family: Times New Roman;\">&nbsp;</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">8 - 6 = 2&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 2 + 3 = 5&nbsp; &nbsp; &nbsp; &nbsp;&nbsp;</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">5 + 3 = 8 &nbsp;इसी तरह,,&nbsp; &nbsp;1 + 3 = 4</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">6 + 3 = 9&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 0 + 3 = 3</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">3 + 3 = 6&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 4 + 3 = 7</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "6",
                    question_en: " <p>19</span><span style=\"font-family:Times New Roman\"> Select the number that can replace the question mark (?) in the following series.</span></p> <p><span style=\"font-family:Times New Roman\">1138, 1150, 1163, 1177, ?  </span><span style=\"font-family:Times New Roman\">",
                    question_hi: "<p><span style=\"font-family: Baloo;\">19. निम्नलिखित श्रृंखला में प्रश्न संख्या (?) को प्रतिस्थापित करने वाली संख्या का चयन करें। </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">1138, 1150, 1163, 1177, ? </span></p>",
                    options_en: [" <p> 1196 </span></p>", " <p> 1202</span><span style=\"font-family:Times New Roman\"> </span></p>", 
                                " <p> 1192 </span></p>", " <p> 1204 </span></p>"],
                    options_hi: ["<p><span style=\"font-family: Times New Roman;\">1196 </span></p>", "<p><span style=\"font-family: Times New Roman;\">1202</span></p>",
                                "<p><span style=\"font-family: Times New Roman;\">1192 </span></p>", "<p><span style=\"font-family: Times New Roman;\">1204 </span></p>"],
                    solution_en: "<p><span style=\"font-family: Times New Roman;\">(c)Here, the pattern: 1138+12 = 1350, 1350+13 = 1363, 1363+14 = 1377, 1377+15 = 1392</span></p>",
                    solution_hi: "<p><span style=\"font-family: Times New Roman;\">(c)यहाँ, पैटर्न: 1138+12 = 1350, 1350+13 = 1363, 1363+14 = 1377, 1377+15 = 1392</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "6",
                    question_en: " <p>20. Select the word pair which are inter related in the same way as the following word pair:</span></p> <p><span style=\"font-family:Times New Roman\">Attire : Tuxedo ",
                    question_hi: "<p><span style=\"font-family: Baloo;\">20. उस शब्द युग्म का चयन करें जिसके बीच ठीक वही संबंध है जो संबंध नीचे दिए गए शब्द युग्म में है | </span></p>\r\n<p><span style=\"font-family: Baloo;\">पोशाक : टक्सीडो</span></p>",
                    options_en: [" <p> Shuttle : Badminton </span></p>", " <p> Qualities : generosity </span></p>", 
                                " <p> Timber : Wood  </span></p>", " <p> Mangroves : Furniture  </span></p>"],
                    options_hi: ["<p>कॉर्क : बैडमिंटन</p>", "<p>गुण : दानशीलता</p>",
                                "<p>टिम्बर: लकड़ी</p>", "<p>मैंग्रोव : फर्नीचर</p>"],
                    solution_en: " <p><span style=\"font-family:Times New Roman\">(b)‘Tuxedo’ is a kind of ‘Attire’, similarly, ‘Generosity’ is a kind of ‘Quality’.</span></p>",
                    solution_hi: "<p><span style=\"font-family: Times New Roman;\">(b)\'टक्सेडो\' एक तरह का \'पोशाक \' है, इसी तरह, \'दानशीलता \' एक तरह का&nbsp; \'गुण\' है।.</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "6",
                    question_en: "<p>21. Which two signs should be interchanged in the following equation to make it correct:</p>\r\n<p><span style=\"font-family: Times New Roman;\">12<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#215;</mo></math></span><span style=\"font-family: Times New Roman;\">8-9<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#247;</mo></math></span><span style=\"font-family: Times New Roman;\">3+6=2 </span></p>",
                    question_hi: "<p><span style=\"font-family: Baloo;\">21. नीचे दिए गए समीकरण को सही बनाने के लिए इसमें किन दो चिन्हों को आपस में बदलने की आवश्यकता है ?</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">12<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#215;</mo></math></span><span style=\"font-family: Times New Roman;\">8-9<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#247;</mo></math></span><span style=\"font-family: Times New Roman;\">3+6=2 </span></p>",
                    options_en: ["<p><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#215;</mo></math>&amp; -</span></p>", "<p><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#215;</mo></math> &amp; +</span></p>", 
                                "<p>+ &amp; -</p>", "<p>- &amp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#247;</mo></math></p>"],
                    options_hi: ["<p><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#215;</mo></math>&amp; -</span></p>", "<p><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#215;</mo></math>&nbsp;&amp; +</span></p>",
                                "<p>+ &amp; -</p>", "<p>- &amp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#247;</mo></math></p>"],
                    solution_en: "<p><span style=\"font-family: Times New Roman;\">(b) 12<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#215;</mo></math></span><span style=\"font-family: Times New Roman;\">8-9<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#247;</mo></math></span><span style=\"font-family: Times New Roman;\">3+6=2 </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Interchange <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#215;</mo></math></span><span style=\"font-family: Times New Roman;\"> &amp; + , We get</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">12+8-9</span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#247;</mo></math><span style=\"font-family: Times New Roman;\">3</span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#215;</mo></math><span style=\"font-family: \'Times New Roman\';\">6</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">20-18=2=RHS</span></p>",
                    solution_hi: "<p><span style=\"font-family: Times New Roman;\">(b) 12<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#215;</mo></math></span><span style=\"font-family: Times New Roman;\">8-9<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#247;</mo></math></span><span style=\"font-family: Times New Roman;\">3+6=2 </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#215;</mo></math></span><span style=\"font-family: Times New Roman;\"> और + बदलने पर&nbsp; ,हम पाते हैं</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">12+8-9</span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#247;</mo></math><span style=\"font-family: Times New Roman;\">3</span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#215;</mo></math><span style=\"font-family: \'Times New Roman\';\">6</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">20-18=2=RHS</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "6",
                    question_en: "<p>22. How many triangles are there in the given figure?</p>\n<p><strong id=\"docs-internal-guid-0593afd6-7fff-89fc-c1b2-d9eb94dfae2a\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcNGZWW-hmQYLuW2yvDv9Sd9ncbTebC1wEe_--nC9WAJXCJ2-dhOm4EzzTS-81TPoFQZVrPOKCe-4PzsKQMbMMBYRpbAeQLzkY9sJhJ5vNEZ3Z2_2nPBR4WQVXt3DBIQ3mnjerM?key=jT-LmXentAfg4PSzJhdddkF5\" width=\"134\" height=\"116\"></strong></p>",
                    question_hi: "<p><span style=\"font-family: Baloo;\">22. दी गयी आकृति में कितने त्रिभुज हैं ? </span></p>\n<p><strong id=\"docs-internal-guid-0593afd6-7fff-89fc-c1b2-d9eb94dfae2a\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcNGZWW-hmQYLuW2yvDv9Sd9ncbTebC1wEe_--nC9WAJXCJ2-dhOm4EzzTS-81TPoFQZVrPOKCe-4PzsKQMbMMBYRpbAeQLzkY9sJhJ5vNEZ3Z2_2nPBR4WQVXt3DBIQ3mnjerM?key=jT-LmXentAfg4PSzJhdddkF5\" width=\"134\" height=\"116\"></strong></p>",
                    options_en: ["<p>14</p>", "<p>15</p>", 
                                "<p>11</p>", "<p>13</p>"],
                    options_hi: ["<p>14</p>", "<p>15</p>",
                                "<p>11</p>", "<p>13</p>"],
                    solution_en: "<p><span style=\"font-family: Times New Roman;\">(a)</span></p>\n<p><strong id=\"docs-internal-guid-a40a55b2-7fff-db9c-9145-8a086d1b0a68\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcNqLBPFmnHYIE8J24PG0Q6GJbY85rfHv353DQCOyqIWfhTDYrPwug2MW9qmAG0dUI8Dsr9HVDdkuKMQG-dZwPysJ5ODR5wlYlBL_kSlk2-vav0Rg_tRtLPh5ewXJL_sUJGqW0Kzg?key=jT-LmXentAfg4PSzJhdddkF5\" width=\"193\" height=\"190\"></strong></p>\n<p dir=\"ltr\">Triangle are :- ABD, BCH, HGF, DEF, IJL, JLK, ILK, IVL, KML, MNT, TUN, NOP, TSR, PQR</p>\n<p>&nbsp;</p>",
                    solution_hi: "<p><span style=\"font-family: Times New Roman;\">(a)</span></p>\n<p><strong id=\"docs-internal-guid-a40a55b2-7fff-db9c-9145-8a086d1b0a68\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcNqLBPFmnHYIE8J24PG0Q6GJbY85rfHv353DQCOyqIWfhTDYrPwug2MW9qmAG0dUI8Dsr9HVDdkuKMQG-dZwPysJ5ODR5wlYlBL_kSlk2-vav0Rg_tRtLPh5ewXJL_sUJGqW0Kzg?key=jT-LmXentAfg4PSzJhdddkF5\" width=\"193\" height=\"190\"></strong></p>\n<p dir=\"ltr\">त्रिभुज :- ABD, BCH, HGF, DEF, IJL, JLK, ILK, IVL, KML, MNT, TUN, NOP, TSR, PQR</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "23",
                    section: "6",
                    question_en: "<p>23. Select the correct mirror image of the given combination when the mirror is placed at MN as shown below.</p>\n<p dir=\"ltr\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcTWfDQPrKspxshE9KnV7xhNW7jLXhDuRmNvSJ1T8FTdlp8OwhCQGMwwKIbg_D6wPUdTR85AMmtuWA_HTCHcpknPfBP9k12GVYoUw6St_lU8O7HOllh8QbRrAX7KDAoYDJzh0Dx?key=HdXF7DP_JORvxyyajZRjmcjz\" width=\"140\" height=\"124\"></p>\n<p>&nbsp;</p>",
                    question_hi: "<p><span style=\"font-family: Baloo;\">23. </span>दर्पण को नीचे दर्शाए अनुसार MN पर रखे जाने पर दिए गए संयोजन के सही दर्पण प्रतिबिंब का चयन कीजिए।</p>\n<p dir=\"ltr\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcTWfDQPrKspxshE9KnV7xhNW7jLXhDuRmNvSJ1T8FTdlp8OwhCQGMwwKIbg_D6wPUdTR85AMmtuWA_HTCHcpknPfBP9k12GVYoUw6St_lU8O7HOllh8QbRrAX7KDAoYDJzh0Dx?key=HdXF7DP_JORvxyyajZRjmcjz\" width=\"140\" height=\"124\"></p>\n<p>&nbsp;</p>",
                    options_en: ["<p><strong id=\"docs-internal-guid-665b2567-7fff-b733-ed23-ae2edee5399f\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcBWon2uEJfAARHhIfVH3nzH9_LcbpRNDvyyNER5nD4HQtxVuqr3OMRXmq9zpOhkeG6k2ojvQbk8CVVbYxZKL1zOGqLFcXYvq09a4nAePLkYGCy_m5fAvk63L9j2GjrrxYWfEI65A?key=HdXF7DP_JORvxyyajZRjmcjz\" width=\"104\" height=\"30\"></strong></p>", "<p><strong id=\"docs-internal-guid-5d9cf43d-7fff-37bb-c1ea-627f129ebb0e\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXesgWUuKUlSAj74jVpdBsgRdPkOroS9nSeFmCFNlzmdm8c12Gz1Wn-f4J53ETOax7XPEVev4qCUdZgNtbyOixefmpdGqxZyN74FqjNqqg4LnhhE3DRFRMZU8w72KblCJ6WEKXSznw?key=HdXF7DP_JORvxyyajZRjmcjz\" width=\"106\" height=\"29\"></strong></p>", 
                                "<p><strong id=\"docs-internal-guid-cb5a3e0f-7fff-53da-e9a5-d77cec1e583a\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXenkxIXnXdnz2um16rWSnhghL02B6LMIrzy5DE2dZBwUbxj97HNygJqmXySUwge16qelxMii1Vz3OdOSg_ciUrTEDqD2oy16n8udr0jllFsGZnAJzULfbdC70kn0y37GZ7LsYM6?key=HdXF7DP_JORvxyyajZRjmcjz\" width=\"103\" height=\"30\"></strong></p>", "<p><strong id=\"docs-internal-guid-2a6024bd-7fff-7319-1d97-6c53b1467f42\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfrBFB0kg9xokbmpMP8ZdKuDCzWqECprpgtTYcWvEVAE4DmTmXPfS1BkmNRGuRDuGTMYByRpDGHcexsvevy83fxgwVCuRrlP5WICcMVnOoXbOYWBk8KKfIp1iTdubMmKbwrb90K-w?key=HdXF7DP_JORvxyyajZRjmcjz\" width=\"106\" height=\"36\"></strong></p>"],
                    options_hi: ["<p><strong id=\"docs-internal-guid-20cc2fe8-7fff-292a-2204-81fad372154a\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcBWon2uEJfAARHhIfVH3nzH9_LcbpRNDvyyNER5nD4HQtxVuqr3OMRXmq9zpOhkeG6k2ojvQbk8CVVbYxZKL1zOGqLFcXYvq09a4nAePLkYGCy_m5fAvk63L9j2GjrrxYWfEI65A?key=HdXF7DP_JORvxyyajZRjmcjz\" width=\"104\" height=\"30\"></strong></p>", "<p><strong id=\"docs-internal-guid-6505fbc9-7fff-450b-be25-b6bb45a2cf13\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXesgWUuKUlSAj74jVpdBsgRdPkOroS9nSeFmCFNlzmdm8c12Gz1Wn-f4J53ETOax7XPEVev4qCUdZgNtbyOixefmpdGqxZyN74FqjNqqg4LnhhE3DRFRMZU8w72KblCJ6WEKXSznw?key=HdXF7DP_JORvxyyajZRjmcjz\" width=\"106\" height=\"29\"></strong></p>",
                                "<p><strong id=\"docs-internal-guid-55c41ab7-7fff-62ab-3afd-aaef142eb297\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXenkxIXnXdnz2um16rWSnhghL02B6LMIrzy5DE2dZBwUbxj97HNygJqmXySUwge16qelxMii1Vz3OdOSg_ciUrTEDqD2oy16n8udr0jllFsGZnAJzULfbdC70kn0y37GZ7LsYM6?key=HdXF7DP_JORvxyyajZRjmcjz\" width=\"103\" height=\"30\"></strong></p>", "<p><strong id=\"docs-internal-guid-28dbb6a9-7fff-83dd-a04e-c2e9598ec3a8\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfrBFB0kg9xokbmpMP8ZdKuDCzWqECprpgtTYcWvEVAE4DmTmXPfS1BkmNRGuRDuGTMYByRpDGHcexsvevy83fxgwVCuRrlP5WICcMVnOoXbOYWBk8KKfIp1iTdubMmKbwrb90K-w?key=HdXF7DP_JORvxyyajZRjmcjz\" width=\"106\" height=\"36\"></strong></p>"],
                    solution_en: "<p><span style=\"font-family: Times New Roman;\">(a)</span></p>\n<p><strong id=\"docs-internal-guid-2e605571-7fff-c5da-9ff0-7a03eec4a9ea\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcBWon2uEJfAARHhIfVH3nzH9_LcbpRNDvyyNER5nD4HQtxVuqr3OMRXmq9zpOhkeG6k2ojvQbk8CVVbYxZKL1zOGqLFcXYvq09a4nAePLkYGCy_m5fAvk63L9j2GjrrxYWfEI65A?key=HdXF7DP_JORvxyyajZRjmcjz\" width=\"104\" height=\"30\"></strong></p>",
                    solution_hi: "<p><span style=\"font-family: Times New Roman;\">(a)</span></p>\n<p><strong id=\"docs-internal-guid-2e605571-7fff-c5da-9ff0-7a03eec4a9ea\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcBWon2uEJfAARHhIfVH3nzH9_LcbpRNDvyyNER5nD4HQtxVuqr3OMRXmq9zpOhkeG6k2ojvQbk8CVVbYxZKL1zOGqLFcXYvq09a4nAePLkYGCy_m5fAvk63L9j2GjrrxYWfEI65A?key=HdXF7DP_JORvxyyajZRjmcjz\" width=\"104\" height=\"30\"></strong></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "24",
                    section: "6",
                    question_en: "<p>24.&nbsp; Which of the following numbers will replace the question mark (?) in the given series?</p>\n<p dir=\"ltr\">244&nbsp; 232.1&nbsp; 220.2&nbsp; 208.3&nbsp; 196.4&nbsp; ?</p>\n<p>&nbsp;</p>",
                    question_hi: "<p><span style=\"font-family: Baloo;\">24.&nbsp; </span>निम्नलिखित में से कौन-सी संख्या दी गई श्रृंखला में प्रश्न चिह्न (?) के स्थान पर आएगी?</p>\n<p dir=\"ltr\">244&nbsp; 232.1&nbsp; 220.2&nbsp; 208.3&nbsp; 196.4&nbsp; ?</p>\n<p>&nbsp;</p>",
                    options_en: ["<p>184.5</p>", "<p dir=\"ltr\">&nbsp;172.5</p>\n<p>&nbsp;</p>", 
                                "<p>180.5</p>", "<p dir=\"ltr\">176.5</p>\n<p>&nbsp;</p>"],
                    options_hi: ["<p dir=\"ltr\">184.5&nbsp;</p>\n<p>&nbsp;</p>", "<p dir=\"ltr\">172.5</p>\n<p>&nbsp;</p>",
                                "<p dir=\"ltr\">180.5</p>\n<p>&nbsp;</p>", "<p>176.5</p>"],
                    solution_en: "<p><span style=\"font-family: Times New Roman;\">(a)</span></p>\n<p><strong id=\"docs-internal-guid-fe518a73-7fff-3ddd-a923-34626ff82eab\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdFegPalEZ95CcUX-_3zfVNzjWah0M37ZP-K7u2XRp7vGUK_wTZ8B4paeiMdeOOSHCmdK--RCLA0cuIcRRV1gYRUVDIgXb4YYvBREhcbOFC7ayGEG-n1Be4J_iZrKxn_-z_wwcH-w?key=RpLz4JyOhmrQQv7QLlW5jRYY\" width=\"393\" height=\"74\"></strong></p>",
                    solution_hi: "<p><span style=\"font-family: Times New Roman;\">(a)</span></p>\n<p><strong id=\"docs-internal-guid-a3f6d28a-7fff-6abd-5e9b-7314db53d037\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdFegPalEZ95CcUX-_3zfVNzjWah0M37ZP-K7u2XRp7vGUK_wTZ8B4paeiMdeOOSHCmdK--RCLA0cuIcRRV1gYRUVDIgXb4YYvBREhcbOFC7ayGEG-n1Be4J_iZrKxn_-z_wwcH-w?key=RpLz4JyOhmrQQv7QLlW5jRYY\" width=\"398\" height=\"75\"></strong></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "25",
                    section: "misc",
                    question_en: "<p>25. Select the missing number from the given options.</p>\r\n<p><span style=\"font-family: Times New Roman;\">11&nbsp; &nbsp; 3&nbsp; &nbsp; 94 </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">9&nbsp; &nbsp; &nbsp; 3&nbsp; &nbsp; 54</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">14&nbsp; &nbsp; &nbsp;?&nbsp; &nbsp; 71</span></p>",
                    question_hi: "<p><span style=\"font-family: Baloo;\">25. दिए गए विकल्पों में से लुप्त संख्या ज्ञात करें |</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">11&nbsp; &nbsp; 3&nbsp; &nbsp; 94 </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">9&nbsp; &nbsp; &nbsp; 3&nbsp; &nbsp; 54</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">14&nbsp; &nbsp; &nbsp;?&nbsp; &nbsp; 71</span></p>",
                    options_en: ["<p>2</p>", "<p>3</p>", 
                                "<p>4</p>", "<p>5</p>"],
                    options_hi: ["<p>2</p>", "<p>3</p>",
                                "<p>4</p>", "<p>5</p>"],
                    solution_en: "<p><span style=\"font-family: Times New Roman;\">(d) Here, the logic row wise: square of the 1st element-cube of the 2nd element=3rd element</span></p>\r\n<p dir=\"ltr\"><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>11</mn><mn>2</mn></msup><mo>-</mo><msup><mn>3</mn><mn>3</mn></msup><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>94</mn><mspace linebreak=\"newline\"></mspace><mi>S</mi><mi>i</mi><mi>m</mi><mi>i</mi><mi>l</mi><mi>a</mi><mi>r</mi><mi>l</mi><mi>y</mi><mo>&#160;</mo><mo>,</mo><mo>&#160;</mo><msup><mn>14</mn><mn>2</mn></msup><mo>-</mo><msup><mn>5</mn><mn>3</mn></msup><mo>=</mo><mo>&#160;</mo><mn>71</mn></math></span></p>",
                    solution_hi: "<p><span style=\"font-family: Times New Roman;\">(d)यहां, &nbsp;पंक्ति के अनुसार तर्क: दूसरे तत्व के पहले तत्व-घन का वर्ग = तीसरा तत्व</span></p>\r\n<p dir=\"ltr\"><span style=\"font-family: Times New Roman;\"><math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>11</mn><mn>2</mn></msup><mo>-</mo><msup><mn>3</mn><mn>3</mn></msup><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>94</mn><mspace linebreak=\"newline\"/><mi>&#2313;&#2360;&#2368;</mi><mo>&#160;</mo><mo>&#160;</mo><mi>&#2346;&#2381;&#2352;&#2325;&#2366;&#2352;</mi><mo>&#160;</mo><mo>,</mo><mo>&#160;</mo><msup><mn>14</mn><mn>2</mn></msup><mo>-</mo><msup><mn>5</mn><mn>3</mn></msup><mo>=</mo><mo>&#160;</mo><mn>71</mn></math></span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML - more compact
            let html = `
                <div class="card mb-3">
                    <div class="card-body">
                        <div class="question-text mb-3">
                            <div class="en">${question.question_en}</div>
                            <div class="hi" style="display:none">${question.question_hi}</div>
                        </div>
                        <div class="options">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = 'option-label';
                // When reviewing, highlight user's choice and correct answer
                if (submitted) {
                    if (opt === question.correct) {
                        optionClass += ' border-success bg-success bg-opacity-10';
                    }
                    if (answers[question.id] === opt && opt !== question.correct) {
                        optionClass += ' border-danger bg-danger bg-opacity-10';
                    }
                }
                html += `
                    <div class="form-check mb-2">
                        <input class="form-check-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${answers[question.id] === opt ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                        <label class="form-check-label w-100 ${optionClass}" for="opt-${index}-${opt}">
                            ${submitted && opt === question.correct ? '<i class="fas fa-check-circle text-success me-1"></i>' : ''}
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const alertClass = isCorrect ? 'alert-success' : 'alert-danger';
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="alert ${alertClass} mt-3 py-2">
                        <strong>${resultText}</strong>
                        <div class="mt-1 small">
                            <strong>Solution:</strong>
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                document.getElementById(`box-${index}`).classList.add('attempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { if (currentQuestion > 0) showQuestion(currentQuestion - 1); }
        function nextQuestion() { if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
            // Update button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const content = document.getElementById('main-content');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                if (window.innerWidth >= 992) {
                    content.classList.add('with-nav');
                }
            } else {
                nav.classList.remove('show');
                if (window.innerWidth >= 992) {
                    content.classList.remove('with-nav');
                }
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer - compact
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            if (confirm(`You have attempted ${attempted} of ${questions.length} questions.
${remaining} questions are unattempted.
Are you sure you want to submit?`)) {
                submitTest();
            }
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('bg-secondary', 'text-white');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            resultsModal.show();
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.classList.replace('btn-light', 'btn-success');
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            resultsModal.hide();
            showQuestion(0);
        }
        // Add event listener for window resize
        window.addEventListener('resize', function() {
            if (window.innerWidth >= 992) {
                if (document.getElementById('question-nav').classList.contains('show')) {
                    document.getElementById('main-content').classList.add('with-nav');
                }
            } else {
                document.getElementById('main-content').classList.remove('with-nav');
            }
        });
    </script>
</body>
</html>