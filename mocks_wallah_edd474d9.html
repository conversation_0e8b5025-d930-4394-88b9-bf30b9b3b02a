<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">5:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 15</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">15</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 5 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["18"] = {
                name: "General Awareness",
                start: 0,
                end: 13
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="18">General Awareness</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 14,
                end: 14
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "18",
                    question_en: "<p>1. When a person is NOT paying anything for public goods, this is known as ____.</p>",
                    question_hi: "<p>1. जब कोई व्यक्ति सार्वजनिक वस्तुओं के लिए कुछ भी भुगतान नहीं कर रहा है, तो इसे ____ के रूप में जाना जाता है।</p>",
                    options_en: ["<p>Non-excludable</p>", "<p>Rivalrous</p>", 
                                "<p>Free rider</p>", "<p>Private good</p>"],
                    options_hi: ["<p>गैर-बहिष्कृत</p>", "<p>प्रतिद्वंद्विता</p>",
                                "<p>फ्री राइडर</p>", "<p>निजी वस्तुएं</p>"],
                    solution_en: "<p>1.(c) <strong>Free rider. </strong>In the context of public goods, a free rider is able to use or enjoy the benefits of the public good without contributing to its cost. This is possible because of the nature of public goods, which are typically non-excludable. Example: Using public parks without paying for them.</p>",
                    solution_hi: "<p>1.(c)<strong> फ्री राइडर।</strong> सार्वजनिक वस्तुओं के संदर्भ में, एक फ्री राइडर सार्वजनिक वस्तु की लागत में योगदान किए बिना उसके लाभों का उपयोग या आनंद लेने में सक्षम होता है। यह सार्वजनिक वस्तुओं की प्रकृति के कारण संभव है, जो सामान्यतः गैर-बहिष्कृत होती हैं। उदाहरण: बिना भुगतान किए सार्वजनिक पार्कों का उपयोग करना।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "18",
                    question_en: "<p>2. Match the following items. <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1732009865503.png\" alt=\"rId4\" width=\"426\" height=\"125\"></p>",
                    question_hi: "<p>2. निम्नलिखित मदों का मिलान कीजिए I<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1732009865711.png\" alt=\"rId5\" width=\"345\" height=\"141\"></p>",
                    options_en: ["<p>i (b), ii (d), iii (a), iv (c)</p>", "<p>i (d), ii (a), iii (b), iv (c)</p>", 
                                "<p>i (b), ii (c), iii (d), iv (a)</p>", "<p>i (c), ii (d), iii (b), iv (a)</p>"],
                    options_hi: ["<p>i (b), ii (d), iii (a), iv (c)</p>", "<p>i (d), ii (a), iii (b), iv (c)</p>",
                                "<p>i (b), ii (c), iii (d), iv (a)</p>", "<p>i (c), ii (d), iii (b), iv (a)</p>"],
                    solution_en: "<p>2.(a) <strong>i (b), ii (d), iii (a), iv (c). </strong>The Kasturba Gandhi Balika Vidyalaya (KGBV) is a government-run residential school for girls from weaker sections of society. The Mid-Day Meal Scheme (MDM) provides free, nutritious meals to school children across India. The National Assessment and Accreditation Council (NAAC) accredits higher education institutions, while the All India Council for Technical Education (AICTE) oversees technical education under the Department of Higher Education.</p>",
                    solution_hi: "<p>2.(a)<strong> i (b), ii (d), iii (a), iv (c). </strong>कस्तूरबा गांधी बालिका विद्यालय (KGBV) समाज के कमजोर वर्गों की लड़कियों के लिए सरकार द्वारा संचालित आवासीय विद्यालय है। मध्याह्न भोजन योजना (MDM) पूरे भारत में स्कूली बच्चों को निःशुल्क, पौष्टिक भोजन प्रदान करती है। राष्ट्रीय मूल्यांकन और प्रत्यायन परिषद (NAAC) उच्च शिक्षा संस्थानों को मान्यता देती है, जबकि अखिल भारतीय तकनीकी शिक्षा परिषद (AICTE) उच्च शिक्षा विभाग के तहत तकनीकी शिक्षा की देखरेख करती है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "18",
                    question_en: "<p>3. Which of the following is a difference between self-help groups (SHGs) and microfinance institutions (MFIs) in India?</p>",
                    question_hi: "<p>3. निम्नलिखित में से कौन-सा विकल्प भारत में स्वयं सहायता समूहों (SHGs) और सूक्ष्म वित्त संस्थाओं (MFIs) के बीच अंतर है?</p>",
                    options_en: ["<p>SHGs are non-profit organisations, while MFIs are for-profit organisations</p>", "<p>SHGs provide only credit, while MFIs provide a range of financial services.</p>", 
                                "<p>SHGs are typically small, community-based organisations, while MFIs are larger and more formal institutions.</p>", "<p>SHGs rely on government subsidies for funding, while MFIs rely on commercial sources of funding.</p>"],
                    options_hi: ["<p>SHGs गैर-लाभकारी संगठन हैं, जबकि MFIs लाभकारी संगठन हैं।</p>", "<p>SHG केवल क्रेडिट प्रदान करते हैं, जबकि MFI वित्तीय सेवाओं की एक शृंखला प्रदान करते हैं।</p>",
                                "<p>SHG आमतौर पर छोटे, समुदाय-आधारित संगठन होते हैं, जबकि MFI बड़े और अधिक औपचारिक संस्था होते हैं।</p>", "<p>SHGs वित्तपोषण के लिए सरकारी सब्सिडी पर निर्भर होते हैं, जबकि MFIs वित्तपोषण के वाणिज्यिक स्रोतों पर भरोसा करते हैं।</p>"],
                    solution_en: "<p>3.(c) Self-Help Groups (SHGs) in India, started in the late 1980s by NGOs, typically consist of 10 to 20 women, serving as financial intermediaries. Examples include Amba Foundation, ASRLM, and Chamoli SHG. Microfinance institutions (MFIs), like Ujivan Financial Services and Annapurna Finance, provide banking services to low-income groups, playing a key role in poverty alleviation.</p>",
                    solution_hi: "<p>3.(c) भारत में स्वयं सहायता समूह (SHG) 1980 के दशक के अंत में NGO द्वारा शुरू किए गए थे, जिनमें आमतौर पर 10 से 20 महिलाएं शामिल होती हैं, जो वित्तीय मध्यस्थ के रूप में काम करती हैं। उदाहरणों में अंबा फाउंडेशन, ASRLM और चमोली SHG शामिल हैं। उज्वजीवन फाइनेंशियल सर्विसेज और अन्नपूर्णा फाइनेंस जैसी माइक्रोफाइनेंस संस्थाएं (MFI) कम आय वाले समूहों को बैंकिंग सेवाएं प्रदान करती हैं, जो गरीबी उन्मूलन में महत्वपूर्ण भूमिका निभाती हैं।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "18",
                    question_en: "<p>4. The Working Group under the chairmanship of ____ in the year____ proposed a new intermediate monetary aggregate to be referred to as NM2.</p>",
                    question_hi: "<p>4. ____की अध्यक्षता में बने कार्य समूह ने वर्ष ____ में एक नए मध्यवर्ती मौद्रिक समुच्चय को NM2 के रूप में संदर्भित करने का प्रस्ताव दिया।</p>",
                    options_en: ["<p>Dr. C Rangarajan; 1996</p>", "<p>Dr. YV Reddy; 1998</p>", 
                                "<p>Dr. KV Kamath; 1995</p>", "<p>Dr. PK Mohanty; 1998</p>"],
                    options_hi: ["<p>डॉ. सी. रंगराजन; 1996</p>", "<p>डॉ. वाई.वी. रेड्डी; 1998</p>",
                                "<p>डॉ. के.वी. कामथ; 1995</p>", "<p>डॉ. पी.के. मोहंती; 1998</p>"],
                    solution_en: "<p>4.(b) <strong>Dr. YV Reddy; 1998.</strong> NM2 (Narrow Money 2) measures the money supply in an economy, including currency, demand deposits, and time deposits. It indicates liquidity and helps guide monetary policy and economic regulation. Central banks, such as the RBI, use NM2 to control inflation and manage economic growth, reflecting broader money supply aspects for policy decisions.</p>",
                    solution_hi: "<p>4.(b) <strong>डॉ. वाई.वी. रेड्डी; 1998. </strong>NM2 (नैरो मनी 2) मुद्रा, मांग जमा और सावधि जमा सहित अर्थव्यवस्था में मुद्रा आपूर्ति को मापता है। यह तरलता को इंगित करता है और यह मौद्रिक नीति तथा आर्थिक विनियमन को निर्देशित करने में मदद करता है। RBI जैसे केंद्रीय बैंक मुद्रास्फीति को नियंत्रित करने और आर्थिक विकास का प्रबंधन करने के लिए NM2 का उपयोग करते हैं, जो नीतिगत निर्णयों के लिए व्यापक मुद्रा आपूर्ति पहलुओं को दर्शाता है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "18",
                    question_en: "5. What is the primary benefit offered to businesses in Special Economic Zones (SEZs)? ",
                    question_hi: "5. विशेष आर्थिक क्षेत्रों {Special Economic Zones (SEZs)} में व्यवसायों को दिया जाने वाला प्राथमिक लाभ क्या है? ",
                    options_en: [" Tax and duty concessions ", " Guaranteed market share  ", 
                                " Mandatory government contracts ", " Unlimited foreign investment "],
                    options_hi: [" कर और शुल्क रियायतें", "  गारंटीयुक्त मार्केट शेयर ",
                                " अनिवार्य सरकारी अनुबंध ", " असीमित विदेशी निवेश"],
                    solution_en: "<p>5.(a) <strong>Tax and duty concessions.</strong> The incentives and facilities offered to the units in SEZs for attracting investments into the SEZs, including foreign investment include:- Duty free import/domestic procurement of goods for development, operation and maintenance of SEZ units. 100% Income Tax exemption on export income for SEZ units under Section 10AA of the Income Tax Act for first 5 years, 50% for next 5 years thereafter and 50% of the ploughed back export profit for next 5 years.</p>",
                    solution_hi: "<p>5.(a) <strong>कर और शुल्क रियायतें।</strong> विदेशी निवेश सहित विशेष आर्थिक क्षेत्र में निवेश को आकर्षित करने के लिए विशेष आर्थिक क्षेत्र में इकाइयों को दिए जाने वाले प्रोत्साहन और सुविधाओं में शामिल हैं:- विशेष आर्थिक क्षेत्र इकाइयों के विकास, संचालन और रखरखाव के लिए वस्तुओं का शुल्क मुक्त आयात/घरेलू खरीद। विशेष आर्थिक क्षेत्र इकाइयों के लिए आयकर अधिनियम की धारा 10AA के तहत निर्यात आय पर पहले 5 वर्षों के लिए 100% आयकर छूट, उसके बाद अगले 5 वर्षों के लिए 50% और अगले 5 वर्षों के लिए वापस किए गए निर्यात लाभ का 50%।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "18",
                    question_en: "<p>6. The \'Make in India\' initiative primarily focuses on which sector?</p>",
                    question_hi: "<p>6. \'मेक इन इंडिया (Make in India)\' पहल मुख्य रूप से किस क्षेत्र पर केंद्रित है?</p>",
                    options_en: ["<p>Manufacturing</p>", "<p>Service</p>", 
                                "<p>Agriculture</p>", "<p>Import</p>"],
                    options_hi: ["<p>विनिर्माण</p>", "<p>सेवा</p>",
                                "<p>कृषि</p>", "<p>आयात</p>"],
                    solution_en: "<p>6.(a) <strong>Manufacturing</strong>. Make in India Initiative: Launched on September 25, 2014, by Prime Minister Narendra Modi, initiative aims to transform India into a global manufacturing hub by boosting the manufacturing sector&rsquo;s contribution to GDP, creating jobs, attracting foreign investments, and improving the ease of doing business.</p>",
                    solution_hi: "<p>6.(a)<strong> विनिर्माण।</strong> मेक इन इंडिया पहल: 25 सितंबर, 2014 को प्रधान मंत्री नरेंद्र मोदी द्वारा शुरू की गई थी। इस पहल का उद्देश्य सकल घरेलू उत्पाद में विनिर्माण क्षेत्र के योगदान को बढ़ावा देकर, रोजगार सृजन, विदेशी निवेश आकर्षित करने और व्यापार करने में आसानी में सुधार करके भारत को वैश्विक विनिर्माण केंद्र में बदलना है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "18",
                    question_en: "7. Which industrial policy marked the beginning of liberalisation in the Indian industry, reducing the restrictions on private companies?",
                    question_hi: "7.  किस औद्योगिक नीति ने निजी कंपनियों पर प्रतिबंधों को कम करके भारतीय उद्योग में उदारीकरण की शुरुआत की? ",
                    options_en: [" Industrial Policy 1991", " Industrial Policy 1980", 
                                " Industrial Policy 1977", " Industrial Policy 1956"],
                    options_hi: [" औद्योगिक नीति 1991 ", " औद्योगिक नीति 1980 ",
                                " औद्योगिक नीति 1977 ", " औद्योगिक नीति 1956"],
                    solution_en: "<p>7.(a) <strong>Industrial Policy 1991.</strong> It was introduced as part of the broader economic reforms initiated by the government of P.V. Narasimha Rao, with Dr. Manmohan Singh as the Finance Minister. This policy marked a paradigm shift from the previous regime of licenses, permits, and regulations to a more open and market-oriented economy. Important features included : Abolition of industrial licensing for most industries, Liberalization of foreign technology agreements, and Abolition of the MRTP Act (Monopolies and Restrictive Trade Practices Act).</p>",
                    solution_hi: "<p>7.(a)<strong> औद्योगिक नीति 1991.</strong>यह पी.वी. नरसिंह राव की सरकार द्वारा शुरू किए गए व्यापक आर्थिक सुधारों के हिस्से के रूप में पेश किया गया था, जिसमें डॉ. मनमोहन सिंह वित्त मंत्री थे। यह नीति लाइसेंस, परमिट और नियमों के पिछले शासन से एक अधिक खुली और बाजार-उन्मुख अर्थव्यवस्था की ओर एक मौलिक बदलाव का प्रतीक थी। महत्वपूर्ण विशेषताओं में शामिल थे: अधिकांश उद्योगों के लिए औद्योगिक लाइसेंसिंग का उन्मूलन, विदेशी प्रौद्योगिकी समझौतों का उदारीकरण, और MRTP अधिनियम (एकाधिकार और प्रतिबंधात्मक व्यापार प्रथाएं अधिनियम) का उन्मूलन।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "18",
                    question_en: "<p>8. Which of the following is a correct statement?</p>",
                    question_hi: "<p>8. निम्नलिखित में से कौन सा कथन सही है?</p>",
                    options_en: ["<p>National Income is GNP at FC.</p>", "<p>National Income is NDP at MP.</p>", 
                                "<p>National Income is GDP at MP.</p>", "<p>National Income is NNP at FC.</p>"],
                    options_hi: ["<p>राष्&zwj;ट्रीय आय उपादान लागत (FC) पर सकल राष्&zwj;ट्रीय उत्&zwj;पाद (GNP) है।</p>", "<p>राष्&zwj;ट्रीय आय, बाजार मूल्&zwj;य (MP) पर निवल देशी उत्&zwj;पाद (NDP) है।</p>",
                                "<p>राष्&zwj;ट्रीय आय, बाजार मूल्&zwj;य (MP) पर सकल देशी उत्&zwj;पाद (GDP) है।</p>", "<p>राष्&zwj;ट्रीय आय , उपादान लागत (FC) पर निवल राष्&zwj;ट्रीय उत्&zwj;पाद (NNP) है।</p>"],
                    solution_en: "<p>(d) <strong id=\"docs-internal-guid-e61e188f-7fff-713a-28b7-6bbe4b1a89e7\">National Income is NNP at FC.</strong> Net National Product at factor cost is equal to sum total of value added at factor cost or net domestic product at factor cost and net factor income from abroad. NNP at Factor Cost = NNP at Market Price &ndash; Net Indirect tax.</p>",
                    solution_hi: "<p dir=\"ltr\">(d) <strong>राष्&zwj;ट्रीय आय , उपादान लागत (FC) पर निवल राष्&zwj;ट्रीय उत्&zwj;पाद (NNP) है। </strong>कारक लागत पर शुद्ध राष्ट्रीय उत्पाद कारक लागत पर जोड़े गए मूल्य या कारक लागत पर शुद्ध घरेलू उत्पाद और विदेश से शुद्ध कारक आय के योग के बराबर है। कारक लागत पर NNP = बाजार मूल्य पर NNP - शुद्ध अप्रत्यक्ष कर।</p>\n<p>&nbsp;</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "18",
                    question_en: "<p>9. What is the primary objective of the Micro, Small, and Medium Enterprises Development (MSMED) Act of 2006 in India?</p>",
                    question_hi: "<p>9. भारत में सूक्ष्म, लघु और मध्यम उद्यम विकास (MSMED) अधिनियम, 2006 का प्राथमिक उद्देश्य क्या है?</p>",
                    options_en: ["<p>To regulate foreign companies</p>", "<p>To focus on large-scale industries only</p>", 
                                "<p>To increase import tariffs to protect MSMEs</p>", "<p>To promote and facilitate the development of MSMEs</p>"],
                    options_hi: ["<p>विदेशी कंपनियों को विनियमित करना</p>", "<p>केवल बड़े पैमाने के उद्योगों पर ध्यान केंद्रित करना</p>",
                                "<p>MSMEs की सुरक्षा के लिए आयात शुल्क बढ़ाना</p>", "<p>MSMEs के विकास को बढ़ावा देना और सुविधाजनक बनाना</p>"],
                    solution_en: "<p>9.(d) Currently, MSMEs (Micro, Small, and Medium Enterprises) are defined under the Micro, Small and Medium Enterprises Development Act, 2006. The Act classifies them as micro, small and medium enterprises based on: (i) investment in plant and machinery for enterprises engaged in manufacturing or production of goods, and (ii) investment in equipment for enterprises providing services. MSMEs play a vital role in generating significant employment opportunities with relatively lower capital investment compared to large industries.</p>",
                    solution_hi: "<p>9.(d) वर्तमान में, MSME (सूक्ष्म, लघु और मध्यम उद्यम) को सूक्ष्म, लघु और मध्यम उद्यम विकास अधिनियम, 2006 के तहत परिभाषित किया गया है। अधिनियम उन्हें निम्नलिखित के आधार पर सूक्ष्म, लघु और मध्यम उद्यमों के रूप में वर्गीकृत करता है: (i) वस्तुओं के विनिर्माण या उत्पादन में लगे उद्यमों के लिए संयंत्र और मशीनरी में निवेश, और (ii) सेवाएं प्रदान करने वाले उद्यमों के लिए उपकरणों में निवेश। बड़े उद्योगों की तुलना में अपेक्षाकृत कम पूंजी निवेश के साथ MSME रोजगार के महत्वपूर्ण अवसर पैदा करने में महत्वपूर्ण भूमिका निभाते हैं।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "18",
                    question_en: "<p>10. NDP = ____.</p>",
                    question_hi: "<p>10. एन.डी.पी. (NDP) = ____।</p>",
                    options_en: ["<p>GDP &ndash; Depreciation</p>", "<p>GDP + Depreciation</p>", 
                                "<p>GDP &ndash; Net factor income from abroad</p>", "<p>GDP + Net factor income from abroad</p>"],
                    options_hi: ["<p>जी.डी.पी. (GDP) - मूल्यह्रास</p>", "<p>जी.डी.पी. (GDP) + मूल्यह्रास</p>",
                                "<p>जी.डी.पी. (GDP) - विदेशों से शुद्ध कारक आय</p>", "<p>जी.डी.पी. (GDP) + विदेशों से शुद्ध कारक आय</p>"],
                    solution_en: "<p>10.(a) <strong>GDP &ndash; Depreciation. </strong>Net Domestic Product (NDP) - Aggregate value of goods and services produced within the domestic territory of a country which does not include the depreciation of capital stock. Net Domestic Product at Market Prices (NDPMP) - This measure allows policy-makers to estimate how much the country has to spend just to maintain their current GDP. NDP at Factor Cost (NDPFC) - NDP at factor cost is the income earned by the factors in the form of wages, profits, rent, interest, etc., within the domestic territory of a country. NDPFC= NDPMP - Net ProductTaxes - Net ProductionTaxes.</p>",
                    solution_hi: "<p>10.(a) <strong>जी.डी.पी.- मूल्यह्रास।</strong> शुद्ध घरेलू उत्पाद (NDP) - किसी देश के घरेलू क्षेत्र में उत्पादित वस्तुओं और सेवाओं का कुल मूल्य जिसमें पूंजी स्टॉक का मूल्यह्रास शामिल नहीं है। बाजार मूल्य पर शुद्ध घरेलू उत्पाद (NDPMP) - यह उपाय नीति-निर्माताओं को यह अनुमान लगाने की अनुमति देता है कि देश को अपने वर्तमान सकल घरेलू उत्पाद को बनाए रखने के लिए कितना खर्च करना होगा। कारक लागत पर NDP (NDPFC) - कारक लागत पर NDP किसी देश के घरेलू क्षेत्र में मजदूरी, लाभ, किराया, ब्याज आदि के रूप में कारकों द्वारा अर्जित आय है। NDPFC = NDPMP - शुद्ध उत्पादकर - शुद्ध उत्पादनकर।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "18",
                    question_en: "<p>11. During the period of 1947 to 1990, how many five-year plans were completed?</p>",
                    question_hi: "<p>11. 1947 से 1990 की अवधि के दौरान, कितनी पंचवर्षीय योजनाएँ पूरी की गईं?</p>",
                    options_en: ["<p>6</p>", "<p>7</p>", 
                                "<p>9</p>", "<p>8</p>"],
                    options_hi: ["<p>6</p>", "<p>7</p>",
                                "<p>9</p>", "<p>8</p>"],
                    solution_en: "<p>11.(b) <strong>7</strong>. Five Year Plans - First (1951-56), Second (1956 -61), Third (1961-66), Plan Holidays (1966 -69), Fourth (1969-74), Rolling Plan (1978-80), Sixth (1980-85), Seventh (1985-90), Annual Plans (1990-92), Eighth (1992-97), Ninth (1997-2002), Tenth (2002-07), Eleventh (2007-2012), Twelfth (2012-17).</p>",
                    solution_hi: "<p>11.(b) <strong>7</strong>. पंचवर्षीय योजनाएं - प्रथम (1951-56), दूसरी (1956 -61), तीसरी (1961-66), योजना अवकाश (1966 -69), चौथी (1969-74), रोलिंग प्लान (1978-80), छठी (1980-85), सातवीं (1985-90), वार्षिक योजनाएं (1990-92), आठवीं (1992-97), नौवीं (1997-2002), दसवीं (2002-07), ग्यारहवीं (2007-2012), बारहवीं (2012-17)।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "18",
                    question_en: "<p>12. Which of the following is an example of capital receipts?</p>",
                    question_hi: "<p>12. निम्नलिखित में से कौन-सा पूँजीगत प्राप्तियों का उदाहरण है?</p>",
                    options_en: ["<p>Revenue from the sale of government assets</p>", "<p>Taxes on income</p>", 
                                "<p>Interest on government bonds</p>", "<p>Grants-in-aid from other governments</p>"],
                    options_hi: ["<p>सरकारी परिसंपत्तियों की बिक्री से राजस्व</p>", "<p>आय पर कर</p>",
                                "<p>सरकारी बॉन्ड पर ब्याज</p>", "<p>अन्य सरकारों से सहायता अनुदान</p>"],
                    solution_en: "<p>12.(a) <strong>Revenue from the sale of government assets. </strong>Capital receipts are government receipts that create liabilities or reduce financial assets. Key components include loans raised from the public (market borrowings), borrowings from the Reserve Bank and commercial banks through treasury bills, loans from foreign governments and international organizations, and recoveries of loans granted by the central government.</p>",
                    solution_hi: "<p>12.(a) <strong>सरकारी परिसंपत्तियों की बिक्री से राजस्व। </strong>पूंजी प्राप्तियां सरकारी प्राप्तियां हैं जो देनदारियों का निर्माण करती हैं या वित्तीय संपत्तियों को कम करती हैं। प्रमुख घटकों में जनता से लिए गए ऋण (बाजार उधार), ट्रेजरी बिलों के माध्यम से रिजर्व बैंक और वाणिज्यिक बैंकों से उधार, विदेशी सरकारों और अंतरराष्ट्रीय संगठनों से ऋण और केंद्र सरकार द्वारा दिए गए ऋणों की वसूली शामिल हैं।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "18",
                    question_en: "13. Which of the following is NOT correct regarding circular flow of income?",
                    question_hi: "13.  आय के चक्रीय प्रवाह के संबंध में निम्नलिखित में से कौन-सा सही नहीं है?",
                    options_en: [" Savings as well as government expenditure are injections to the circular flow ", " The income of one sector becomes the expenditure of the other.   ", 
                                " In a circular flow, leakages are equal to injections.  ", " The real flow and monetary flow move in a circular manner in an opposite direction."],
                    options_hi: [" बचत के साथ-साथ सरकारी व्यय चक्रीय प्रवाह में अंत:क्षेपण (injections) हैं।", " एक क्षेत्र की आय दूसरे की व्यय बन जाती है।",
                                " एक चक्रीय प्रवाह में क्षरण अंत:क्षेपण (injections) के बराबर होते हैं।", " वास्तविक प्रवाह और मौद्रिक प्रवाह विपरीत दिशा में चक्रीय गति करते हैं।"],
                    solution_en: "13.(a) Savings are considered a leakage from the circular flow of income because they represent income that is not immediately spent on goods and services. Government expenditure is an injection, as it adds to the overall demand in the economy.",
                    solution_hi: "13.(a) बचत को आय के चक्रीय प्रवाह से रिसाव (leakage) माना जाता है क्योंकि वे ऐसी आय का प्रतिनिधित्व करते हैं जो वस्तुओं और सेवाओं पर तुरंत खर्च नहीं की जाती है। सरकारी व्यय एक अंत:क्षेपण (injections) है, क्योंकि यह अर्थव्यवस्था में समग्र मांग को बढ़ाता है।",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "18",
                    question_en: "<p>14. Which of the following sectors in India faces seasonal unemployment?</p>",
                    question_hi: "<p>14. भारत में निम्नलिखित में से कौन-सा क्षेत्र मौसमी बेरोजगारी का सामना करता है?</p>",
                    options_en: ["<p>Agriculture</p>", "<p>Manufacturing</p>", 
                                "<p>Medical</p>", "<p>Information technology</p>"],
                    options_hi: ["<p>कृषि</p>", "<p>विनिर्माण</p>",
                                "<p>चिकित्सा</p>", "<p>सूचना प्रौद्योगिकी</p>"],
                    solution_en: "<p>14.(a) <strong>Agriculture</strong>. Seasonal unemployment occurs when people are jobless during specific seasons when labor demand is low. It arises from a mismatch between the skills of the workforce and those required for available jobs. Unemployment is a situation in which individuals are ready and willing to work at the prevailing rate of wages but cannot get the work.</p>",
                    solution_hi: "<p>14.(a) <strong>कृषि। </strong>मौसमी बेरोज़गारी तब होती है जब लोग विशिष्ट मौसमों के दौरान बेरोज़गार होते हैं जब श्रम की मांग कम होती है। यह कार्यबल के कौशल और उपलब्ध नौकरियों के लिए आवश्यक कौशल के बीच बेमेल से उत्पन्न होता है। बेरोज़गारी एक ऐसी स्थिति है जिसमें व्यक्ति मौजूदा मज़दूरी दर पर काम करने के लिए तैयार और इच्छुक होते हैं, लेकिन उन्हें काम नहीं मिल पाता है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "misc",
                    question_en: "<p>15. The term &lsquo;Microfinancing&rsquo; was first used in the 1970s during the development of Grameen Bank of Bangladesh, which was founded by ____.</p>",
                    question_hi: "<p>15. माइक्रोफाइनेंसिंनें सिंग\' शब्द का पहली बार उपयोग 1970 के दशक में बांग्लादेश के ग्रामीण बैंक के विकास के दौरान किया गया था, जिसकी स्थापना ____ द्वारा की गई थी।</p>",
                    options_en: ["<p>Muhammad Yunus</p>", "<p>Adam Smith</p>", 
                                "<p>Jeremy Bentham</p>", "<p>Alfred Marshall</p>"],
                    options_hi: ["<p>मुहम्मद यूनुस</p>", "<p>एडम स्मिथ</p>",
                                "<p>जेरेमी बेंथम</p>", "<p>अल्फ्रेड मार्शल</p>"],
                    solution_en: "<p>15.(a) <strong>Muhammad Yunus.</strong> Muhammad Yunus is a Bangladeshi economist, entrepreneur, politician, and civil society leader. He was awarded the Nobel Peace Prize in 2006 for founding the Grameen Bank and pioneering microcredit and microfinance. Microfinance provides banking services to low-income individuals or groups who typically lack access to traditional financial institutions.</p>",
                    solution_hi: "<p>15.(a) <strong>मुहम्मद यूनुस।</strong> मुहम्मद यूनुस एक बांग्लादेशी अर्थशास्त्री, उद्यमी, राजनीतिज्ञ और नागरिक समाज के नेता हैं। उन्हें ग्रामीण बैंक की स्थापना और माइक्रोक्रेडिट और माइक्रोफाइनेंस में अग्रणी भूमिका निभाने के लिए 2006 में नोबेल शांति पुरस्कार से सम्मानित किया गया था। माइक्रोफाइनेंस कम आय वाले व्यक्तियों या समूहों को बैंकिंग सेवाएँ प्रदान करता है, जिनकी आम तौर पर पारंपरिक वित्तीय संस्थानों तक पहुँच नहीं होती है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>