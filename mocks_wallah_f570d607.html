<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">30:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 26</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">26</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 30 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["6"] = {
                name: "Reasoning",
                start: 0,
                end: 24
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="6">Reasoning</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 25,
                end: 25
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "6",
                    question_en: "1. Select the number from among the given options that can replace the question mark (?) in the following series:<br />111, 90, 71, 54, ?",
                    question_hi: "1. दिए गए विकल्पों में से उस संख्या का चयन करें जो निम्नलिखित श्रृंखला में प्रश्नवाचक चिह्न (?) को प्रतिस्थापित कर सकती है।<br />111, 90, 71, 54, ?",
                    options_en: [" 50", " 39", 
                                " 41", " 49"],
                    options_hi: [" 50", " 39",
                                " 41", " 49"],
                    solution_en: "1.(b) <br />111 - 21 = 90<br />90 - 19 = 71<br />71 - 17 = 54<br />54 - 15 = 39",
                    solution_hi: "1.(b)<br />111 - 21 = 90<br />90 - 19 = 71<br />71 - 17 = 54<br />54 - 15 = 39",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "2",
                    section: "6",
                    question_en: "2. ‘A camel is called the ship of the desert\' is drawn from the assumption,",
                    question_hi: "2. ‘ऊंट को रेगिस्तान का जहाज कहा जाता है\' इसके पीछे क्या अवधारणा है?",
                    options_en: [" The shape of a camel is like a ship. ", " Camels are used for transportation in deserts. ", 
                                " Camels have humps. ", " Camels are mammals."],
                    options_hi: [" ऊंट का आकार जहाज जैसा होता है।", " ऊंटों का उपयोग रेगिस्तान में परिवहन के लिए किया जाता है।",
                                " ऊंटों के कूबड़ होते हैं।", " ऊंट स्तनधारी होते हैं।"],
                    solution_en: "2.(b) A camel is called the \'ship of the desert\' because camels are used for transportation in deserts. They carry very big loads from one side of the desert to the other. Dromedary camels can carry over two hundred kilograms on their backs. ",
                    solution_hi: "2.(b) ऊंट को \'रेगिस्तान का जहाज\' कहा जाता है क्योंकि ऊंटों का उपयोग रेगिस्तान में परिवहन के लिए किया जाता है। वे रेगिस्तान के एक तरफ से दूसरी तरफ बहुत बड़ा भार उठाते हैं। ड्रोमेडरी ऊंट अपनी पीठ पर दो सौ किलोग्राम से अधिक वजन उठा सकते हैं।",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "3",
                    section: "6",
                    question_en: "3. If the position of a letter in the english alphabet is the value of the letter, then what will be the total of all the letters in the word EXAGGERATION?",
                    question_hi: "3. यदि अंग्रेजी वर्णमाला में एक अक्षर का स्थान ही अक्षर का मान है, तो शब्द EXAGGERATION के सभी अक्षरों का योग कितना होगा?",
                    options_en: [" 136", " 134", 
                                " 132", " 126"],
                    options_hi: [" 136", " 134",
                                " 132", " 126"],
                    solution_en: "3.(d) EXAGGERATION<br />= 5+24+1+7+7+5+18+1+20+9+15+14 = 126",
                    solution_hi: "3.(d) EXAGGERATION<br />= 5+24+1+7+7+5+18+1+20+9+15+14 = 126",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "4",
                    section: "6",
                    question_en: "<p>4. Read the given statements and conclusions carefully. Assuming that the information given in the statement is true, even If it appears to be at variance with commonly known facts, decide which of the given conclusions logically follow(s) from the statements.<br><strong>Statements:</strong><br>1. All engineers are graduates.<br>2. All graduates are successful.<br><strong>Conclusion:</strong> <br>1. All successful are graduates.<br>2. All engineers are successful.</p>",
                    question_hi: "<p>4. दिए गए कथनों और निष्कर्षों को ध्यान से पढ़ें यह मानते हुए कि कथन में दी गई जानकारी सत्य है, भले ही वह सामान्य रूप से ज्ञात तथ्यों से भिन्न प्रतीत होती हो, बताइये कि कौन सा निष्कर्ष कथनों का तार्किक रूप से अनुसरण करता है।<br><strong>कथन:</strong><br>1. सभी इंजीनियर स्नातक हैं।<br>2. सभी स्नातक सफल हैं।<br><strong>निष्कर्ष:</strong><br>1. सभी सफल स्नातक हैं।<br>2. सभी इंजीनियर सफल हैं।</p>",
                    options_en: ["<p>Either I or II</p>", "<p>Only II</p>", 
                                "<p>Both I and II</p>", "<p>Only 1</p>"],
                    options_hi: ["<p>I अथवा II</p>", "<p>केवल II</p>",
                                "<p>I और II दोनों</p>", "<p>केवल 1</p>"],
                    solution_en: "<p>4.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727945955785.png\" alt=\"rId5\" width=\"212\" height=\"139\"><br>From the above venn diagram, it is clear that only conclusion (II) follows.</p>",
                    solution_hi: "<p>4.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727945955884.png\" alt=\"rId6\" width=\"203\" height=\"133\"><br>उपरोक्त वेन आरेख से यह स्पष्ट है कि केवल निष्कर्ष (II) अनुसरण करता है।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "5",
                    section: "6",
                    question_en: "5. What conclusions can be drawn from the premises ‘All teachers are hardworking persons’ and ‘Most hardworking persons are women’ ?",
                    question_hi: "5. इस कथन से क्या निष्कर्ष निकाला जा सकता है, \'सभी शिक्षक मेहनती व्यक्ति हैं\' और \'अधिकांश मेहनती व्यक्ति महिलाएं हैं\'?",
                    options_en: [" All women are hardworking persons.", " No teacher is a woman. ", 
                                " Some women are hardworking persons. ", " All teachers are women. "],
                    options_hi: [" सभी महिलाएं मेहनती हैं।", " कोई शिक्षक महिला नहीं है।",
                                " कुछ महिलाएं मेहनती व्यक्ति हैं।", " सभी शिक्षक महिला हैं।"],
                    solution_en: "5.(c) From the given statements, it is clear that most hardworking persons are women so it will definitely be true that some women are hardworking persons.",
                    solution_hi: "5.(c) दिए गए कथनों से यह स्पष्ट है कि अधिकांश मेहनती व्यक्ति महिलाएं हैं, इसलिए यह निश्चित रूप से सच होगा कि कुछ महिलाएं मेहनती व्यक्ति हैं।",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "6",
                    section: "6",
                    question_en: "6. Which of the given options best classiﬁes the following items.<br />Altimeter, Barometer, Dynamometer. Thermometer",
                    question_hi: "6. दिए गए विकल्पों में से कौन सा विकल्प निम्नलिखित मदों का सर्वोत्तम वर्गीकरण करता है।<br />अल्टीमीटर, बैरोमीटर, डायनेमोमीटर, थर्मामीटर",
                    options_en: [" Heating equipment  ", " Navigation equipment  ", 
                                " Measuring instruments", " Weighing instruments "],
                    options_hi: [" तापीय उपकरण ", " नौपरिवहन उपकरण",
                                " मापन उपकरण", " वजनी  उपकरण"],
                    solution_en: "6.(c) All are measuring instruments.",
                    solution_hi: "6.(c) सभी मापक यंत्र हैं।",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "7",
                    section: "6",
                    question_en: "7. Select the word-pair from the given options where the first word is related to the second word in the same way as the word-pair are given below.<br />Butterfly : Insect :: ?",
                    question_hi: "7. दिए गए विकल्पों में से शब्द-युग्म का चयन कीजिये जहां पहले शब्द को दूसरे शब्द के रूप में उसी तरह युग्मित है जैसे नीचे दिया गया शब्द युग्म है?<br />तितली : कीट :: ?",
                    options_en: [" Rodent : Rat ", " Ostrich : Bird ", 
                                " Furniture : Chair ", " Mammal : Man "],
                    options_hi: [" कृंतक : चूहा", " शुतुरमुर्ग : चिड़िया",
                                " फर्नीचर : कुर्सी", " स्तनपायी : पुरुष"],
                    solution_en: "7.(b) Second word is the category of the first word. So option (B) is related to the given words.",
                    solution_hi: "7.(b) दूसरा शब्द पहले शब्द की श्रेणी है। अतः विकल्प (B) दिए गए शब्दों से संबंधित है।",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "8",
                    section: "6",
                    question_en: "8. Select the options that is related to the third term in the same way as the second term is related to the first term.<br />Map : Location :: Compass : ?",
                    question_hi: "8. उन विकल्पों का चयन कीजिए जो तीसरे पद से उसी प्रकार संबंधित हैं जैसे दूसरा पद पहले पद से संबंधित है।<br />मानचित्र :  स्थान :: दिशा सूचक : ?",
                    options_en: [" Tourist", " Radar ", 
                                " Direction ", " Magnet "],
                    options_hi: [" पर्यटक", " राडार",
                                " दिशा", " चुंबक"],
                    solution_en: "8.(c) Map is related to location, in the same way, compass is related to direction.",
                    solution_hi: "8.(c) मानचित्र स्थान से संबंधित है, ठीक उसी तरह कम्पास का संबंध दिशा से है।",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "9",
                    section: "6",
                    question_en: "9. Study the given pattern carefully and select the number from among the given options that can replace the question mark(?) in it.<br /> <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727945955986.png\" alt=\"rId7\" /> ",
                    question_hi: "9. दिए गए पैटर्न का ध्यानपूर्वक अध्ययन कीजिये और दिए गए विकल्पों में से उस संख्या का चयन कीजिये जो इसके प्रश्न चिह्न (?) को प्रतिस्थापित कर सके।<br /> <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727945955986.png\" alt=\"rId7\" /> ",
                    options_en: [" 50", " 54", 
                                " 42", " 40"],
                    options_hi: [" 50", " 54",
                                " 42", " 40"],
                    solution_en: "9.(c)<br />30 - 12 + 2 =20<br />42 - 14 + 2 = 30<br />56 - 16 + 2 = 42",
                    solution_hi: "9.(c)<br />30 - 12 + 2 =20<br />42 - 14 + 2 = 30<br />56 - 16 + 2 = 42",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "10",
                    section: "6",
                    question_en: "<p>10. A tourist wanted to see the planetarium in a city. He walked from the railway station in the east direction and arrived at a pond. He turned left and then at the second corner of the pond, he turned right. Soon he reached another corner of the pond. He turned left and arrived at the planetarium. In what direction was the railway station situated with respect to the planetarium?</p>",
                    question_hi: "<p>10. एक पर्यटक एक शहर में तारामंडल देखना चाहता था। वह रेलवे स्टेशन से पूर्व दिशा में चला और एक बिंदु पर पहुंचा। वह बाएँ मुड़ा और फिर बिंदु के दूसरे कोने पर, वह दाएँ मुड़ा। जल्द ही वह बिंदु के दूसरे कोने में पहुंच गया। वह बाएँ मुड़ा और तारामंडल पर पहुँचा। तारामंडल के सन्दर्भ में रेलवे स्टेशन किस दिशा में स्थित था?</p>",
                    options_en: ["<p>South-east</p>", "<p>South-west</p>", 
                                "<p>North-west</p>", "<p>North-east</p>"],
                    options_hi: ["<p>दक्षिण पूर्व</p>", "<p>दक्षिण पश्चिम</p>",
                                "<p>उत्तर पश्चिम</p>", "<p>उत्तर पूर्व</p>"],
                    solution_en: "<p>10.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727945956067.png\" alt=\"rId8\" width=\"269\" height=\"189\"></p>",
                    solution_hi: "<p>10.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727945956184.png\" alt=\"rId9\" width=\"273\" height=\"209\"></p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "11",
                    section: "6",
                    question_en: "11. Select the letter-cluster from among the given options that can replace the question mark(?) in the following series?<br />ABC, EFG, IJK, OPQ, ?",
                    question_hi: "11. दिए गए विकल्पों में से अक्षर समूह का चयन कीजिये जो निम्नलिखित श्रृंखला में प्रश्न चिह्न (?) को प्रतिस्थापित कर सकता है?<br />ABC, EFG, IJK, OPQ, ?",
                    options_en: [" XYZ", " UVW", 
                                " TUV", " RST"],
                    options_hi: [" XYZ", " UVW",
                                " TUV", " RST"],
                    solution_en: "11.(b) C+2 = E, G+2 = I, K+4 =O, Q+4 = U<br />After that three consecutive letters are written. ",
                    solution_hi: "11.(b) C+2 = E, G+2 = I, K+4 =O, Q+4 = U<br />उसके बाद लगातार तीन अक्षर लिखे जाते हैं।",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "12",
                    section: "6",
                    question_en: "<p>12. Select the venn diagram that best represents the relationship between the following classes.<br>Sportsperson, Shooters, Swimmers</p>",
                    question_hi: "<p>12. वेन आरेख का चयन कीजिये जो निम्नलिखित वर्गों के बीच संबंध का सबसे अच्छा प्रतिनिधित्व करता है।<br>खिलाड़ी, निशानेबाज़, तैराक</p>",
                    options_en: ["<p><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727945956306.png\" alt=\"rId10\" width=\"126\" height=\"111\"></p>", "<p><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727945956393.png\" alt=\"rId11\" width=\"126\" height=\"105\"></p>", 
                                "<p><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727945956530.png\" alt=\"rId12\" width=\"193\" height=\"80\"></p>", "<p><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727945956662.png\" alt=\"rId13\" width=\"179\" height=\"105\"></p>"],
                    options_hi: ["<p><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727945956306.png\" alt=\"rId10\" width=\"126\" height=\"111\"></p>", "<p><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727945956393.png\" alt=\"rId11\" width=\"126\" height=\"105\"></p>",
                                "<p><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727945956530.png\" alt=\"rId12\" width=\"193\" height=\"80\"></p>", "<p><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727945956662.png\" alt=\"rId13\" width=\"179\" height=\"105\"></p>"],
                    solution_en: "<p>12.(a)<br><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727945956785.png\" alt=\"rId14\" width=\"234\" height=\"124\"></p>",
                    solution_hi: "<p>12.(a)<br><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727945956908.png\" alt=\"rId15\" width=\"247\" height=\"131\"></p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "13",
                    section: "6",
                    question_en: "13. Find the next word from among the given options which will give more points in the scrabble game (Marks of a letter are its position number in the english alphabet)<br />A, AT, BAT, ?",
                    question_hi: "13. दिए गए विकल्पों में से अगला शब्द ज्ञात कीजिए जो स्क्रैबल गेम में अधिक अंक प्रदान करेगा (एक अक्षर के अंक अंग्रेजी वर्णमाला में उसकी स्थिति की संख्या हैं):<br />A, AT, BAT, ?",
                    options_en: [" BATH", " BATE ", 
                                " BAIT ", " BATS"],
                    options_hi: [" BATH", " BATE ",
                                " BAIT ", " BATS"],
                    solution_en: "13.(d) A+1 = B, S+1 = T<br />Next term = BATS",
                    solution_hi: "13.(d) A+1 = B, S+1 = T<br />अगला पद = BATS",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "14",
                    section: "6",
                    question_en: "<p>14. In a certain code language, FRAGRANCE is written as ESZHQBMDD. How will EXCELLENCE be written as in that language?</p>",
                    question_hi: "<p>14. एक निश्चित कूट भाषा में FRAGRANCE को ESZHQBMDD लिखा जाता है। EXCELLENCE को उसी भाषा में कैसे लिखा जाएगा?</p>",
                    options_en: ["<p>DYBFKMDOBF</p>", "<p>DYBYMKDOBF</p>", 
                                "<p>DYBFKMDQBF</p>", "<p>DYBFKMDPBF</p>"],
                    options_hi: ["<p>DYBFKMDOBF</p>", "<p>DYBYMKDOBF</p>",
                                "<p>DYBFKMDQBF</p>", "<p>DYBFKMDPBF</p>"],
                    solution_en: "<p>14.(a)<br><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727945957023.png\" alt=\"rId16\" width=\"296\" height=\"136\"><br>Similarly<br><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727945957123.png\" alt=\"rId17\" width=\"306\" height=\"125\"></p>",
                    solution_hi: "<p>14.(a)<br><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727945957023.png\" alt=\"rId16\" width=\"296\" height=\"136\"><br><br>उसी प्रकार <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727945957123.png\" alt=\"rId17\" width=\"306\" height=\"125\"></p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "15",
                    section: "6",
                    question_en: "<p>15. Manoj is the husband of Sapna, whose mother is Neelam. Neelam&rsquo;s grandson is Aditya, who is the brother of Shefali. Shefali is the daughter of Vijay who is the brother of Manoj&rsquo;s wife. How is Sapna related to Shefali?</p>",
                    question_hi: "<p>15. मनोज सपना का पति हैं, जिनकी मां नीलम हैं। नीलम का पोता आदित्य है, जो शेफाली का भाई है। शेफाली विजय की पुत्री है जो मनोज की पत्नी का भाई है। सपना का शेफाली से क्या संबंध है?</p>",
                    options_en: ["<p>Sister</p>", "<p>Maternal Aunt</p>", 
                                "<p>Paternal Aunt</p>", "<p>Daughter</p>"],
                    options_hi: ["<p>बहन</p>", "<p>मामी</p>",
                                "<p>बुआ</p>", "<p>बहन</p>"],
                    solution_en: "<p>15.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727945957214.png\" alt=\"rId18\" width=\"277\" height=\"177\"></p>",
                    solution_hi: "<p>15.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727945957348.png\" alt=\"rId19\" width=\"271\" height=\"173\"></p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "16",
                    section: "6",
                    question_en: "16. If C means cube, A means \'+’, B means ‘-’, D means \'x‘ and E means divide, then solve the following.<br />{((3A6)x(6B3))/((6B4)A(3B2))}C =?",
                    question_hi: "16. यदि C का अर्थ घन है, A का अर्थ है \'+\' है, B का अर्थ है \'-\', D का अर्थ है \'x\' और E का अर्थ विभाजित करना है। फिर निम्नलिखित को हल कीजिये ?<br />{((3A6)x(6B3))/((6B4)A(3B2))}C =?",
                    options_en: [" 729", " 927", 
                                " 9", " 27"],
                    options_hi: [" 729", " 927",
                                " 9", " 27"],
                    solution_en: "16.(a)<br />{((3A6)x(6B3))/((6B4)A(3B2))}C<br />= <math display=\"inline\"><msup><mrow><mo>{</mo><mo>(</mo><mo>(</mo><mn>3</mn><mo>+</mo><mn>6</mn><mo>)</mo><mo>×</mo><mo>(</mo><mn>6</mn><mo>-</mo><mn>3</mn><mo>)</mo><mo>)</mo><mo>÷</mo><mo>(</mo><mo>(</mo><mn>6</mn><mo>-</mo><mn>4</mn><mo>)</mo><mo>+</mo><mo>(</mo><mn>3</mn><mo>-</mo><mn>2</mn><mo>)</mo><mo>)</mo><mo>}</mo></mrow><mrow><mn>3</mn></mrow></msup></math><br />= <math display=\"inline\"><msup><mrow><mo>{</mo><mo>(</mo><mn>9</mn><mo>×</mo><mn>3</mn><mo>)</mo><mo>÷</mo><mo>(</mo><mn>2</mn><mo>+</mo><mn>1</mn><mo>)</mo><mo>}</mo></mrow><mrow><mn>3</mn></mrow></msup></math><br />= <math display=\"inline\"><msup><mrow><mo>{</mo><mn>27</mn><mo>÷</mo><mn>3</mn><mo>}</mo></mrow><mrow><mn>3</mn></mrow></msup></math><br />= <math display=\"inline\"><msup><mrow><mn>9</mn></mrow><mrow><mn>3</mn></mrow></msup></math> = 729",
                    solution_hi: "16.(a)<br />{((3A6)x(6B3))/((6B4)A(3B2))}C<br />= <math display=\"inline\"><msup><mrow><mo>{</mo><mo>(</mo><mo>(</mo><mn>3</mn><mo>+</mo><mn>6</mn><mo>)</mo><mo>×</mo><mo>(</mo><mn>6</mn><mo>-</mo><mn>3</mn><mo>)</mo><mo>)</mo><mo>÷</mo><mo>(</mo><mo>(</mo><mn>6</mn><mo>-</mo><mn>4</mn><mo>)</mo><mo>+</mo><mo>(</mo><mn>3</mn><mo>-</mo><mn>2</mn><mo>)</mo><mo>)</mo><mo>}</mo></mrow><mrow><mn>3</mn></mrow></msup></math><br />= <math display=\"inline\"><msup><mrow><mo>{</mo><mo>(</mo><mn>9</mn><mo>×</mo><mn>3</mn><mo>)</mo><mo>÷</mo><mo>(</mo><mn>2</mn><mo>+</mo><mn>1</mn><mo>)</mo><mo>}</mo></mrow><mrow><mn>3</mn></mrow></msup></math><br />= <math display=\"inline\"><msup><mrow><mo>{</mo><mn>27</mn><mo>÷</mo><mn>3</mn><mo>}</mo></mrow><mrow><mn>3</mn></mrow></msup></math><br />= <math display=\"inline\"><msup><mrow><mn>9</mn></mrow><mrow><mn>3</mn></mrow></msup></math> = 729",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "17",
                    section: "6",
                    question_en: "<p>17. Study the given patterns carefully and select the option that can replace the question mark (?) in it.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727945957437.png\" alt=\"rId20\"></p>",
                    question_hi: "<p>17. दिए गए पैटर्न का ध्यानपूर्वक अध्ययन कीजिये और उस विकल्प का चयन करें जो प्रश्न चिह्न को प्रतिस्थापित कर सकता है ?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727945957437.png\" alt=\"rId20\"></p>",
                    options_en: ["<p><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727945957551.png\" alt=\"rId21\"></p>", "<p><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727945957723.png\" alt=\"rId22\"></p>", 
                                "<p><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727945957835.png\" alt=\"rId23\"></p>", "<p><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727945957931.png\" alt=\"rId24\" width=\"94\" height=\"74\"></p>"],
                    options_hi: ["<p><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727945957551.png\" alt=\"rId21\"></p>", "<p><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727945957723.png\" alt=\"rId22\"></p>",
                                "<p><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727945957835.png\" alt=\"rId23\"></p>", "<p><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727945957931.png\" alt=\"rId24\" width=\"94\" height=\"74\"></p>"],
                    solution_en: "<p>17.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727945957551.png\" alt=\"rId21\"></p>",
                    solution_hi: "<p>17.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727945957551.png\" alt=\"rId21\"></p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "18",
                    section: "6",
                    question_en: "18. If the given sequence QwE@rTy;MnB#vCx/DfG*hJk is reversed, then what will be the letter that precedes the letter D?",
                    question_hi: "18. यदि दिए गए क्रमQwE@rTy;MnB#vCx/DfG*hJk को उलट दिया जाए। तो D अक्षर से पहले कौन सा अक्षर होगा?",
                    options_en: [" X", " G", 
                                " c", " f"],
                    options_hi: [" X", " G",
                                " c", " f"],
                    solution_en: "18.(d) In the given sequence, f is after D but when we reversed the sequence f will come before D.",
                    solution_hi: "18.(d) दिए गए अनुक्रम में f, D के बाद है लेकिन जब हम अनुक्रम को उलटते हैं तो f, D से पहले आ जाएगा।",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "19",
                    section: "6",
                    question_en: "19. Select the option that is related to the third term in the same way and the second term is related to the first term.<br />Direct : Indirect :: Different : ?",
                    question_hi: "19. उस विकल्प का चयन करें जो उसी प्रकार तीसरे पद से संबंधित है और दूसरा पद पहले पद से संबंधित है।<br />प्रत्यक्ष : अप्रत्यक्ष :: अंतर : ?",
                    options_en: [" Inanimate ", " Inconsistent ", 
                                " Identical  ", " Indistinct "],
                    options_hi: [" अचेतन", " असंगत",
                                " समान", " अस्पष्ट"],
                    solution_en: "19.(c) Opposite of direct is indirect, in the same way, opposite of Difference is Identical.",
                    solution_hi: "19.(c) प्रत्यक्ष का विपरीत अप्रत्यक्ष है, उसी प्रकार अंतर के विपरीत समान है।",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "20",
                    section: "6",
                    question_en: "<p>20. A college offers two papers to a student such that if a student studies Philosophy, he or she must study French. One who studies History cannot choose Psychology. French students cannot study History. French students cannot opt for Spanish. Only History students can study Political Science. No Psychology student has opted for Sociology. If so, what other discipline will a Psychology student have to study?</p>",
                    question_hi: "<p>20. एक कॉलेज एक छात्र को दो विषय इस प्रकार प्रदान करता है कि यदि कोई छात्र दर्शनशास्त्र का अध्ययन करता है। उसे फ्रेंच का अध्ययन करना होगा । इतिहास का अध्ययन करने वाला मनोविज्ञान का चयन नहीं कर सकता। फ्रेंच छात्र इतिहास का अध्ययन नहीं कर सकते। फ्रेंच छात्र स्पेनिश का विकल्प नहीं चुन सकते। केवल इतिहास के छात्र ही राजनीति विज्ञान का अध्ययन कर सकते हैं। मनोविज्ञान के किसी भी छात्र ने समाजशास्त्र का विकल्प नहीं चुना है। यदि ऐसा है, तो मनोविज्ञान के छात्र को और कौन-से विषय का अध्ययन करना होगा?</p>",
                    options_en: ["<p>French</p>", "<p>Sociology</p>", 
                                "<p>Political Science</p>", "<p>Spanish</p>"],
                    options_hi: ["<p>फ़्रेंच</p>", "<p>समाज शास्त्र</p>",
                                "<p>राजनीति विज्ञान</p>", "<p>स्पेनिश</p>"],
                    solution_en: "<p>20.(d)<br><strong id=\"docs-internal-guid-4f9e0d16-7fff-01eb-a0fb-068d562e6fb1\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdCTObMtMo4LjIs2SFuO6yuxlakRUkZjq-nMnZpnMTIOOx8DE-D0VOmxaB7VJcnbDUJvvJfKIXlYkuJtA97OpwZS5bE130UrjHTQb6WU7UgepxzU3YdfqK33LbuhL5h_ULllm7P3dKyFUq9bcodRdsx9mbA?key=rSnbH74DlEvnIlKf-Nvc8w\" width=\"202\" height=\"122\"></strong></p>",
                    solution_hi: "<p>20.(d)<br><strong id=\"docs-internal-guid-9f29a563-7fff-776a-64ca-6f0af7e1f936\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcBGVeBeXutzmkG8xci687Hy_q10oWjh9YvJcEQ-RKjEsGGA1oACybWvgplVpHql9RBlOm1fpytKySi81C6kp0g20Wphp1p22P174zv5-3UtSZ1MujX8vKx7AojYBLcAFm153gELfyGDXaLAWyRBrqiPta3?key=rSnbH74DlEvnIlKf-Nvc8w\" width=\"193\" height=\"125\"></strong></p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "21",
                    section: "6",
                    question_en: "21. Out of the four items listed, three are alike in some manner and one is different. Select the odd one.<br />Petal, Sepal, Pollen, Ligament",
                    question_hi: "21. सूचीबद्ध चार वस्तुओं में से तीन समान तरीके से समान हैं और एक अलग है। विजातीय का चयन कीजिये ?<br />पंखुड़ी, बाह्यदल, पराग, स्नायु",
                    options_en: [" Ligament ", " Petal ", 
                                " Pollen ", " Sepal "],
                    options_hi: [" अस्थि-बंधन", " पंखुड़ी",
                                " पराग", " बाह्यदल"],
                    solution_en: "21.(a) Except ligament, all are parts of a flower.",
                    solution_hi: "21.(a) लिगामेंट को छोड़कर सभी एक फूल के अंग हैं।",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "22",
                    section: "6",
                    question_en: "<p>22. Six persons &ndash; teacher, lawyer, doctor, politician, artist and writer - are sitting around a hexagonal shaped table such that the lawyer and the writer are not sitting next to each other. The teacher is sitting between the doctor and the lawyer. The politician is sitting next to the doctor. Who is sitting between the lawyer and the writer?</p>",
                    question_hi: "<p>22. छह व्यक्ति - शिक्षक, वकील, डॉक्टर, राजनेता, कलाकार और लेखक - एक षट्कोणीय आकार की मेज के चारों ओर इस प्रकार बैठे हैं कि वकील और लेखक एक दूसरे के बगल में नहीं बैठे हैं। शिक्षक, डॉक्टर और वकील के बीच बैठा है। राजनेता, डॉक्टर के बगल में बैठा है। वकील और लेखक के बीच में कौन बैठा है?</p>",
                    options_en: ["<p>Artist</p>", "<p>Writer</p>", 
                                "<p>Politician</p>", "<p>Teacher</p>"],
                    options_hi: ["<p>कलाकार</p>", "<p>लेखक</p>",
                                "<p>राजनीतिज्ञ</p>", "<p>शिक्षक</p>"],
                    solution_en: "<p>22.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727945958040.png\" alt=\"rId25\" width=\"241\" height=\"166\"></p>",
                    solution_hi: "<p>22.(a)<br><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727945958135.png\" alt=\"rId26\" width=\"236\" height=\"173\"></p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "23",
                    section: "6",
                    question_en: "<p>23. In a certain code language, REGULATION is written as QDFTKZSHNM. How will SYMPOSIUM be written as in that language?</p>",
                    question_hi: "<p>23. एक निश्चित कोड भाषा में REGULATION को QDFTKZSHNM लिखा जाता है, तो SYMPOSIUM को उसी भाषा में कैसे लिखा जाएगा?</p>",
                    options_en: ["<p>RXLONRTHL</p>", "<p>RXLNORHVL</p>", 
                                "<p>RXLONRHTL</p>", "<p>RXLONRJTL</p>"],
                    options_hi: ["<p>RXLONRTHL</p>", "<p>RXLNORHVL</p>",
                                "<p>RXLONRHTL</p>", "<p>RXLONRJTL</p>"],
                    solution_en: "<p>23.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727945958248.png\" alt=\"rId27\" width=\"356\" height=\"123\">&nbsp; ,&nbsp; &nbsp; &nbsp;<img src=\"data:image/png;base64,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\" width=\"320\" height=\"127\"></p>",
                    solution_hi: "<p>23.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727945958248.png\" alt=\"rId27\" width=\"356\" height=\"123\">&nbsp; ,&nbsp; &nbsp; &nbsp;<img src=\"data:image/png;base64,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\" width=\"320\" height=\"127\"></p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "24",
                    section: "6",
                    question_en: "<p>24. Select the venn diagram that best represents the relationship between the following classes.<br>Cardboard boxes, Glass tumblers, Wooden stools</p>",
                    question_hi: "<p>24. वेन आरेख का चयन कीजिये जो निम्नलिखित वर्गों के बीच के संबंध को सर्वोत्तम रूप से दर्शाता है।<br>कार्ड बॉक्स, कांच के गिलास, लकड़ी के स्टूल</p>",
                    options_en: ["<p><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727945958441.png\" alt=\"rId29\"></p>", "<p><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727945958541.png\" alt=\"rId30\"></p>", 
                                "<p><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727945958638.png\" alt=\"rId31\"></p>", "<p><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727945958735.png\" alt=\"rId32\"></p>"],
                    options_hi: ["<p><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727945958441.png\" alt=\"rId29\"></p>", "<p><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727945958541.png\" alt=\"rId30\"></p>",
                                "<p><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727945958638.png\" alt=\"rId31\"></p>", "<p><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727945958735.png\" alt=\"rId32\"></p>"],
                    solution_en: "<p>24.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727945958912.png\" alt=\"rId33\" width=\"209\" height=\"138\"></p>",
                    solution_hi: "<p>24.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727945959003.png\" alt=\"rId34\" width=\"212\" height=\"136\"></p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "25",
                    section: "6",
                    question_en: "25. Select the option that is related to the third term in the same way as the second term is related to the ﬁrst term.<br />Book : Author :: Movie : ?",
                    question_hi: "25. उस विकल्प का चयन कीजिये जो तीसरे पद से उसी प्रकार संबंधित है जैसे दूसरा पद पहले पद से संबंधित है।<br />पुस्तक : लेखक :: चलचित्र :?",
                    options_en: [" Actress ", " Actor ", 
                                " Director ", " Audience "],
                    options_hi: [" अभिनेत्री", " अभिनेता",
                                " निर्देशक ", " दर्शक"],
                    solution_en: "25.(c) A book is written by Author and the Movie is directed by Director.",
                    solution_hi: "25.(c) पुस्तक लेखक द्वारा लिखी जाती है और फिल्म निर्देशक द्वारा निर्देशित की जाती है । <br /> ",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "26",
                    section: "misc",
                    question_en: "26. Which of the following is the mirror image of INSINUATION ?",
                    question_hi: "26. निम्नलिखित में से कौन INSINUATION का दर्पण प्रतिबिम्ब है?",
                    options_en: [" <br /> <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727945959116.png\" alt=\"rId35\" />", " <br /> <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727945959224.png\" alt=\"rId36\" />", 
                                " <br /> <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727945959315.png\" alt=\"rId37\" />", " <br /> <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727945959386.png\" alt=\"rId38\" />"],
                    options_hi: [" <br /> <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727945959116.png\" alt=\"rId35\" />", " <br /> <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727945959224.png\" alt=\"rId36\" />",
                                " <br /> <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727945959315.png\" alt=\"rId37\" />", " <br /> <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727945959386.png\" alt=\"rId38\" />"],
                    solution_en: "26 (b)<br /> <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727945959224.png\" alt=\"rId36\" />",
                    solution_hi: "26 (b)<br /> <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727945959224.png\" alt=\"rId36\" />",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>