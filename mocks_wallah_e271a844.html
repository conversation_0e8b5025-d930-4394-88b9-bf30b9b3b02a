<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">25:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 40</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">40</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 25 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["18"] = {
                name: "General Awareness",
                start: 0,
                end: 38
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="18">General Awareness</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 39,
                end: 39
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "18",
                    question_en: "<p>1. Being light and non- inflammable , ______ gas is used to blow balloons.</p>",
                    question_hi: "<p>1. हल्का और ज्वलनशील होने के कारण, _____गैस का उपयोग गुब्बारों में हवा भरने के लिए किया जाता है।</p>",
                    options_en: ["<p>Chlorine</p>", "<p>Neon</p>", 
                                "<p>Oxygen</p>", "<p>Helium</p>"],
                    options_hi: ["<p>क्लोरीन</p>", "<p>नियोन</p>",
                                "<p>ऑक्सीजन</p>", "<p>हीलियम</p>"],
                    solution_en: "<p>1.(d) Being light and non- inflammable , Helium gas is used to blow balloons. Helium (He), chemical element, inert gas of Group 18 (noble gases) of the periodic table. Helium is used for medicine, scientific research, arc welding, refrigeration, gas for aircraft, coolant for nuclear reactors, cryogenic research and detecting gas leaks.</p>",
                    solution_hi: "<p>1.(d) हल्का और ज्वलनशील होने के कारण हीलियम गैस का उपयोग गुब्बारों को उड़ाने के लिए किया जाता है। हीलियम (He), रासायनिक तत्व, आवर्त सारणी के समूह 18 (महान गैस) की अक्रिय गैस। हीलियम का उपयोग दवा, वैज्ञानिक अनुसंधान, आर्क वेल्डिंग, प्रशीतन, विमान के लिए गैस, परमाणु रिएक्टरों के लिए शीतलक, क्रायोजेनिक अनुसंधान और गैस रिसाव का पता लगाने के लिए किया जाता है।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "2",
                    section: "18",
                    question_en: "<p>2. Which is the correct full form of <strong>IPBES </strong>from below ?</p>",
                    question_hi: "<p>2. नीचे से <strong>IPBES </strong>का सही पूर्ण रूप कौन सा है ?</p>",
                    options_en: ["<p>International Policy of Biodiversity and Ecosystem Services.</p>", "<p>Intergovernmental Platform on Biodiversity and Ecosystem Services</p>", 
                                "<p>Intergovernmental Science Policy Platform on Biodiversity and Ecosystem Services</p>", "<p>International Programme of Biodiversity and Ecosystem Sciences</p>"],
                    options_hi: ["<p>International Policy of Biodiversity and Ecosystem Services.</p>", "<p>Intergovernmental Platform on Biodiversity and Ecosystem Services</p>",
                                "<p>Intergovernmental Science Policy Platform on Biodiversity and Ecosystem Services</p>", "<p>International Programme of Biodiversity and Ecosystem Sciences</p>"],
                    solution_en: "<p>2.(c) Intergovernmental Science Policy Platform on Biodiversity and Ecosystem Services (IPBES) headquarter Bonn, Germany an independent intergovernmental body which provides policymakers with objective scientific assessments about the state of knowledge regarding the planet\'s biodiversity, ecosystems and the benefits.</p>",
                    solution_hi: "<p>2.(c) जैव विविधता और पारिस्थितिकी तंत्र सेवाओं पर अंतर सरकारी विज्ञान नीति मंच (आईपीबीईएस) मुख्यालय बॉन, जर्मनी एक स्वतंत्र अंतरसरकारी निकाय है जो नीति निर्माताओं को ग्रह की जैव विविधता, पारिस्थितिक तंत्र और लाभों के बारे में ज्ञान की स्थिति के बारे में वस्तुनिष्ठ वैज्ञानिक आकलन प्रदान करता है।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "3",
                    section: "18",
                    question_en: "<p>3. When was the Anandpur Sahib Resolution passed ?</p>",
                    question_hi: "<p>3. आनंदपुर साहिब प्रस्ताव कब पारित किया गया था ?</p>",
                    options_en: ["<p>1975</p>", "<p>1970</p>", 
                                "<p>1973</p>", "<p>1980</p>"],
                    options_hi: ["<p>1975</p>", "<p>1970</p>",
                                "<p>1973</p>", "<p>1980</p>"],
                    solution_en: "<p>3.(c) The Anandpur Sahib Resolution passed on 16&ndash;17 October 1973. The document was adopted unanimously by the working committee of the Shiromani Akali Dal. Anandpur Sahib was founded by Guru Tegh Bahadur. It is located in Rupnagar, Punjab.</p>",
                    solution_hi: "<p>3.(c) आनंदपुर साहिब प्रस्ताव 16-17 अक्टूबर 1973 को पारित हुआ। दस्तावेज़ को शिरोमणि अकाली दल की कार्य समिति द्वारा सर्वसम्मति से अपनाया गया था। आनंदपुर साहिब की स्थापना गुरु तेग बहादुर ने की थी। यह पंजाब के रूपनगर में स्थित है।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "4",
                    section: "18",
                    question_en: "<p>4. Which of the following missile systems are offered by the US to India as an alternative to Russian S-400s ?</p>",
                    question_hi: "<p>4. निम्नलिखित में से कौन सी मिसाइल प्रणाली अमेरिका द्वारा भारत को रूसी S-400s के विकल्प के रूप में प्रस्तुत की गई थी ?</p>",
                    options_en: ["<p>ARROW-2</p>", "<p>THAAD and PAC-3</p>", 
                                "<p>M-11</p>", "<p>S-300</p>"],
                    options_hi: ["<p>एरो-2</p>", "<p>थाड और पीएसी-3</p>",
                                "<p>M-11</p>", "<p>S-300</p>"],
                    solution_en: "<p>4.(b) THAAD(Theater High Altitude Area Defense) and PAC-3 missile systems are offered by the US to India as an alternative to Russian S-400s.THAAD is a ground-based system designed to destroy theater ballistic missile threats to troops, military assets and allied territories.</p>",
                    solution_hi: "<p>4.(b) THAAD (थिएटर हाई एल्टीट्यूड एरिया डिफेंस) और PAC-3 मिसाइल सिस्टम अमेरिका द्वारा भारत को रूसी S-400s के विकल्प के रूप में पेश किए जाते हैं। THAAD एक जमीन आधारित प्रणाली है जिसे सैनिकों, सैन्य संपत्तियों और थिएटर बैलिस्टिक मिसाइल खतरों को नष्ट करने के लिए डिज़ाइन किया गया है। संबद्ध क्षेत्र।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "5",
                    section: "18",
                    question_en: "5. Which company has acquired British toymakers Hamleys?",
                    question_hi: "5. किस कंपनी ने ब्रिटिश खिलौने बनाने वाली कंपनी हैमलीज़ का अधिग्रहण किया है?",
                    options_en: [" Reliance Brands ", " K. Raheja Corp Group ", 
                                " Tata ", " Future Group "],
                    options_hi: [" रिलायंस ब्रांड", " के रहेजा कॉर्प ग्रुप",
                                " टाटा ", " फ्यूचर ग्रुप"],
                    solution_en: "5.(a) Reliance Brands acquired British toymakers Hamleys. Mukesh Ambanki is the owner of Reliance company.",
                    solution_hi: "5.(a) रिलायंस ब्रांड्स ने ब्रिटिश टॉयमेकर हैमलीज का अधिग्रहण किया। मुकेश अंबंकी रिलायंस कंपनी के मालिक हैं।",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "6",
                    section: "18",
                    question_en: "6. Which one of the following programmes aims at creating self-employment opportunities ?",
                    question_hi: "6. निम्नलिखित में से किस कार्यक्रम का उद्देश्य स्वरोजगार के अवसर पैदा करना है ?",
                    options_en: [" SSA", " NRHM", 
                                " ICDS", " PMRY"],
                    options_hi: [" SSA", " NRHM",
                                " ICDS", " PMRY"],
                    solution_en: "6.(d) Pradhan Mantri Rozgar Yojana or PMRY is a Central Government initiative which aims to provide self-employment opportunities to educated youths who are unemployed. The scheme, launched in 1993.",
                    solution_hi: "6.(d) प्रधानमंत्री रोजगार योजना या पीएमआरवाई केंद्र सरकार की एक पहल है जिसका उद्देश्य शिक्षित युवाओं को स्वरोजगार के अवसर प्रदान करना है जो बेरोजगार हैं। 1993 में शुरू की गई योजना।",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "7",
                    section: "18",
                    question_en: "<p>7. In which of the following states is the Pachmarhi Biosphere Reserve located ?</p>",
                    question_hi: "<p>7. निम्नलिखित में से किस राज्य में पंचमढ़ी जीवमंडल आरक्षित स्थित है ?</p>",
                    options_en: ["<p>Uttar Pradesh</p>", "<p>Madhya Pradesh</p>", 
                                "<p>Kerala</p>", "<p>Punjab</p>"],
                    options_hi: ["<p>उत्तर प्रदेश</p>", "<p>मध्य प्रदेश</p>",
                                "<p>केरल</p>", "<p>पंजाब</p>"],
                    solution_en: "<p>7.(b) Pachmarhi Biosphere Reserve located in Madhya Pradesh.UNESCO added Pachmarhi park to its list of Biosphere Reserves in May 2009.</p>",
                    solution_hi: "<p>7.(b) पचमढ़ी बायोस्फीयर रिजर्व मध्य प्रदेश में स्थित है। यूनेस्को ने मई 2009 में पचमढ़ी पार्क को बायोस्फीयर रिजर्व की अपनी सूची में शामिल किया।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "8",
                    section: "18",
                    question_en: "8. Where is the cache memory located?",
                    question_hi: "8. कैश मेमोरी कहाँ स्थित है?",
                    options_en: [" CPU", " RAM", 
                                " CU", " Monitor "],
                    options_hi: [" CPU", " RAM",
                                " CU", " मॉनिटर "],
                    solution_en: "8.(a) The cache memory located in the CPU. Cache memory is sometimes called CPU (Central Processing Unit) memory because it is typically integrated into the CPU chip or placed on a separate chip that has a separate bus interconnect with the CPU.",
                    solution_hi: "8.(a) कैश मेमोरी CPU में स्थित होती है। कैश मेमोरी को कभी-कभी CPU (सेंट्रल प्रोसेसिंग यूनिट) मेमोरी कहा जाता है क्योंकि इसे आमतौर पर CPU चिप में एकीकृत किया जाता है या एक अलग चिप पर रखा जाता है जिसमें CPU के साथ एक अलग बस इंटरकनेक्ट होता है।",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "9",
                    section: "18",
                    question_en: "<p>9. Where is the famous Wagah Border located ?</p>",
                    question_hi: "<p>9. प्रसिद्ध वाघा बॉर्डर कहाँ स्थित है ?</p>",
                    options_en: ["<p>Ahmedabad</p>", "<p>Amritsar</p>", 
                                "<p>Kashmir</p>", "<p>Jaipur</p>"],
                    options_hi: ["<p>अहमदाबाद</p>", "<p>अमृतसर</p>",
                                "<p>कश्मीर</p>", "<p>जयपुर</p>"],
                    solution_en: "<p>9.(b) Wahga is situated 600 meters (2,000 ft) west of the border and lies on the historic Grand Trunk Road between Lahore and Amritsar in India. It is known as the Attari border.</p>",
                    solution_hi: "<p>9.(b) वाहगा सीमा के पश्चिम में 600 मीटर (2,000 फीट) की दूरी पर स्थित है और भारत में लाहौर और अमृतसर के बीच ऐतिहासिक ग्रैंड ट्रंक रोड पर स्थित है। इसे अटारी बॉर्डर के नाम से जाना जाता है।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "10",
                    section: "18",
                    question_en: "10. The Government of India, with its flagship programme Swachh Bharat Mission, aims is a ______.",
                    question_hi: "10. भारत सरकार, अपने प्रमुख कार्यक्रम स्वच्छ भारत मिशन का उद्देश्य क्या है?",
                    options_en: [" poverty-free environment ", " pollution-free environment ", 
                                " terror-free environment ", " corruption-free environment "],
                    options_hi: [" गरीबी मुक्त वातावरण", " प्रदूषण मुक्त वातावरण",
                                " आतंक मुक्त वातावरण", " भ्रष्टाचार मुक्त वातावरण"],
                    solution_en: "10.(b) The Government of India, with its flagship programme Swachh Bharat Mission, aims is a pollution-free environment. Swachh Bharat Mission Launched in 2014. The main aim of the project was to create sanitation facilities for all and provide every rural family with a toilet by 2019.",
                    solution_hi: "10.(b) भारत सरकार, अपने प्रमुख कार्यक्रम स्वच्छ भारत मिशन के साथ, एक प्रदूषण मुक्त वातावरण है। स्वच्छ भारत मिशन 2014 में शुरू किया गया था। परियोजना का मुख्य उद्देश्य सभी के लिए स्वच्छता सुविधाएं बनाना और 2019 तक हर ग्रामीण परिवार को शौचालय प्रदान करना था।",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "11",
                    section: "18",
                    question_en: "<p>11. When were the first Lok Sabha elections held ?</p>",
                    question_hi: "<p>11. पहला लोकसभा चुनाव कब हुआ था</p>",
                    options_en: ["<p>1949 -50</p>", "<p>1951-52</p>", 
                                "<p>1948-49</p>", "<p>1953-54</p>"],
                    options_hi: ["<p>1949 -50</p>", "<p>1951-52</p>",
                                "<p>1948-49</p>", "<p>1953-54</p>"],
                    solution_en: "<p>11.(b) The first Lok Sabha elections were held in 1951-52. The maximum size of the Lok Sabha as outlined in the Constitution of India is 552 members.G.V. Mavalankar was the first Lok Sabha Speaker.</p>",
                    solution_hi: "<p>11.(b) पहला लोकसभा चुनाव 1951-52 में हुआ था। भारत के संविधान में उल्लिखित लोकसभा का अधिकतम आकार 552 सदस्यों का है। जी.वी. मावलंकर पहले लोकसभा अध्यक्ष थे।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "12",
                    section: "18",
                    question_en: "<p>12. The cell with a dark boundary in _____ is called an active cell.</p>",
                    question_hi: "<p>12. MS एक्सेल में डार्क बाउंड्री वाले सेल को एक्टिव सेल कहा जाता है।</p>",
                    options_en: ["<p>MS Excel</p>", "<p>MS PowerPoint</p>", 
                                "<p>MS DOS</p>", "<p>MS Word</p>"],
                    options_hi: ["<p>MS एक्सेल</p>", "<p>MS पावरपॉइंट</p>",
                                "<p>MS डॉक्यूमेंट</p>", "<p>MS वर्ड</p>"],
                    solution_en: "<p>12.(a) The cell with a dark boundary in MS Excel is called an active cell. The active cell can refer to the cell present in the Excel spreadsheet which is selected currently by clicking the mouse or the keyboard keys.</p>",
                    solution_hi: "<p>12.(a) MS एक्सेल में डार्क बाउंड्री वाले सेल को एक्टिव सेल कहा जाता है। सक्रिय सेल एक्सेल स्प्रेडशीट में मौजूद सेल को संदर्भित कर सकता है जिसे वर्तमान में माउस या कीबोर्ड कीज़ पर क्लिक करके चुना जाता है।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "13",
                    section: "18",
                    question_en: "<p>13. ONGC comes under which of the following sectors ?</p>",
                    question_hi: "<p>13. ONGC निम्नलिखित में से किस क्षेत्र के अंतर्गत आता है ?</p>",
                    options_en: ["<p>Energy</p>", "<p>Education</p>", 
                                "<p>Health</p>", "<p>Agriculture</p>"],
                    options_hi: ["<p>उर्जा</p>", "<p>शिक्षा</p>",
                                "<p>स्वास्थ्य</p>", "<p>कृषि</p>"],
                    solution_en: "<p>13.(a) ONGC - Oil and Natural Gas Corporation Limited was founded on 14 August 1956 comes under the energy sector.CEO- Subhash Kumar (2021).</p>",
                    solution_hi: "<p>13.(a) ONGC - तेल और प्राकृतिक गैस निगम लिमिटेड की स्थापना 14 अगस्त 1956 को हुई थी जो ऊर्जा क्षेत्र के अंतर्गत आता है। CEO- सुभाष कुमार (2021)</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "14",
                    section: "18",
                    question_en: "<p>14. Which fluid carries absorbed fat from the intestine and drains excess fluid from extra cellular space back into the blood ?</p>",
                    question_hi: "<p>14. कौन सा द्रव आंत से अवशोषित वसा को ले जाता है और अतिरिक्त कोशिकीय स्थान से अतिरिक्त तरल पदार्थ को वापस रक्त में प्रवाहित करता है ?</p>",
                    options_en: ["<p>Plasma</p>", "<p>Lymph</p>", 
                                "<p>Platelets</p>", "<p>Capillaries</p>"],
                    options_hi: ["<p>प्लाज्मा</p>", "<p>लसीका</p>",
                                "<p>प्लेटलेट्स</p>", "<p>केशिका</p>"],
                    solution_en: "<p>14.(b) Lymph Carries absorb fat from the intestine and drain excess fluid from extra]cellular space back into the blood. Plasma is the liquid portion of blood. About 55% of our blood is plasma. Platelets, or thrombocytes, are small, colorless cell fragments in our blood that form clots and stop or prevent bleeding. Capillaries are small, thin blood vessels that connect the arteries and the veins.</p>",
                    solution_hi: "<p>14.(b) लसीका वाहक आंत से वसा को अवशोषित करता है और अतिरिक्त तरल पदार्थ को बाह्य अंतरिक्ष से वापस रक्त में बहा देता है। प्लाज्मा रक्त का तरल भाग है। हमारे रक्त का लगभग 55% हिस्सा प्लाज्मा है। प्लेटलेट्स, या थ्रोम्बोसाइट्स, हमारे रक्त में छोटे, रंगहीन कोशिका के टुकड़े होते हैं जो थक्के बनाते हैं और रक्तस्राव को रोकते हैं या रोकते हैं। केशिकाएं छोटी, पतली रक्त वाहिकाएं होती हैं जो धमनियों और नसों को जोड़ती हैं।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "15",
                    section: "18",
                    question_en: "15. When did Non- Cooperation and Khilafat Movement begin ?",
                    question_hi: "15. असहयोग और खिलाफत आंदोलन कब शुरू हुआ ?",
                    options_en: [" 1920", " 1931", 
                                " 1928", " 1946"],
                    options_hi: [" 1920", " 1931",
                                " 1928", " 1946"],
                    solution_en: "15.(a) Non- Cooperation and Khilafat Movement began in 1920. organized by Mohandas (Mahatma) Gandhi, to induce the British government of India to grant self-government, or swaraj, to India.Khilafat movement started give  to pressure the British government to preserve the authority of the Ottoman Sultan as Caliph of Islam following the breakup of the Ottoman Empire at the end of the war.",
                    solution_hi: "15.(a) असहयोग और खिलाफत आंदोलन 1920 में शुरू हुआ। मोहनदास (महात्मा) गांधी द्वारा आयोजित, भारत की ब्रिटिश सरकार को भारत को स्व-सरकार, या स्वराज देने के लिए प्रेरित करने के लिए। खिलाफत आंदोलन ने ब्रिटिश सरकार को अधिकार बनाए रखने के लिए दबाव देना शुरू कर दिया। युद्ध के अंत में तुर्क साम्राज्य के टूटने के बाद इस्लाम के खलीफा के रूप में तुर्क सुल्तान का।",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "16",
                    section: "18",
                    question_en: "16. Which of the following states  had the highest value in literacy according to the HDI (Human Development Index) report of 2011 ?",
                    question_hi: "16. 2011 की HDI (मानव विकास सूचकांक) रिपोर्ट के अनुसार निम्नलिखित में से किस राज्य में साक्षरता का उच्चतम मान था ?",
                    options_en: [" Delhi ", " Odisha ", 
                                " Kerala ", " Punjab "],
                    options_hi: [" दिल्ली", " उड़ीसा",
                                " केरल", " पंजाब"],
                    solution_en: "16.(c) Kerala  had the highest value in literacy according to the HDI (Human Development Index) report of 2011. Kottayam district has recorded the highest literacy rate (97.21%).",
                    solution_hi: "16.(c) 2011 की HDI (मानव विकास सूचकांक) रिपोर्ट के अनुसार केरल में साक्षरता का उच्चतम मूल्य था। कोट्टायम जिले ने उच्चतम साक्षरता दर (97.21%) दर्ज की है।",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "17",
                    section: "18",
                    question_en: "<p>17. What is the average temperature of the surface water of the oceans ?</p>",
                    question_hi: "<p>17. महासागरों के सतही जल का औसत तापमान कितना होता है ?</p>",
                    options_en: ["<p>32 degrees Celsius</p>", "<p>27 degrees Celsius</p>", 
                                "<p>20 degrees Celsius</p>", "<p>17 degrees Celsius</p>"],
                    options_hi: ["<p>32 डिग्री सेल्सियस</p>", "<p>27 डिग्री सेल्सियस</p>",
                                "<p>20 डिग्री सेल्सियस</p>", "<p>17 डिग्री सेल्सियस</p>"],
                    solution_en: "<p>17.(d) The average temperature of the ocean surface waters is about 17 degrees Celsius (62.6 degrees Fahrenheit). 90 % of the total volume of the ocean is found below the thermocline in the deep ocean.</p>",
                    solution_hi: "<p>17.(d) समुद्र की सतह के पानी का औसत तापमान लगभग 17 डिग्री सेल्सियस (62.6 डिग्री फ़ारेनहाइट) है। महासागर के कुल आयतन का 90% गहरे महासागर में थर्मोकलाइन के नीचे पाया जाता है।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "18",
                    section: "18",
                    question_en: "18. Who among the following is one of the founders of Google ?",
                    question_hi: "18. निम्नलिखित में से कौन गूगल के संस्थापकों में से एक है ?",
                    options_en: [" Jon Koum  ", " Larry Page ", 
                                " Jerry Yang ", " Mark Zuckerberg "],
                    options_hi: [" जॉन कोउम ", " लेरी पेज",
                                " जैरी यांग", " मार्क जकरबर्ग"],
                    solution_en: "18.(b) Larry Page and Sergey Brin  are one of the founders of Google. Google was founded on September 4, 1998.  Mark Zuckerberg is the founder of Facebook. Jerry Yang is the co-founder and former CEO of Yahoo! Inc. Jon Koum was the CEO and co-founder of Whatsapp.",
                    solution_hi: "18.(b) लैरी पेज और सर्गेई ब्रिन गूगल के संस्थापकों में से एक हैं। गूगल की स्थापना 4 सितंबर, 1998 को हुई थी।  मार्क जुकरबर्ग फेसबुक के संस्थापक हैं। जेरी यांग याहू इंक के सह-संस्थापक और पूर्व CEO हैं जॉन कौम व्हाट्सएप के CEO और सह-संस्थापक थे।",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "19",
                    section: "18",
                    question_en: "<p>19. An amount of money given by the employer to the employee at the time of retirement for services rendered is called:</p>",
                    question_hi: "<p>19. नियोक्ता द्वारा कर्मचारी को सेवानिवृत्ति के समय प्रदान की गई सेवाओं के लिए दी गई राशि को क्या कहा जाता है ?</p>",
                    options_en: ["<p>Pension</p>", "<p>Provident Fund</p>", 
                                "<p>Bonus</p>", "<p>Gratuity</p>"],
                    options_hi: ["<p>पेंशन</p>", "<p>भविष्य निधि</p>",
                                "<p>अधिलाभ</p>", "<p>आनुतोषिक</p>"],
                    solution_en: "<p>19.(d) An amount of money given by the employer to the employee at the time of retirement for services rendered is called Gratuity. Provident Fund is a government-managed retirement savings scheme for employees, who can contribute a part of their savings towards their pension fund, every month.</p>",
                    solution_hi: "<p>19.(d) नियोक्ता द्वारा कर्मचारी को सेवानिवृत्ति के समय प्रदान की गई सेवाओं के लिए दी गई राशि को ग्रेच्युटी कहा जाता है। भविष्य निधि कर्मचारियों के लिए सरकार द्वारा प्रबंधित सेवानिवृत्ति बचत योजना है, जो हर महीने अपनी बचत का एक हिस्सा अपने पेंशन फंड में योगदान कर सकते हैं।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "20",
                    section: "18",
                    question_en: "<p>20. According to the National Tiger Conservation Authority (NTCA), approximately how much percentage is accounted for tiger deaths due to poaching and electrocution from 2012 to 2018 ?</p>",
                    question_hi: "<p>20. राष्ट्रीय बाघ संरक्षण प्राधिकरण (NTCA) के अनुसार, 2012 से 2018 तक अवैध शिकार और बिजली के झटके के कारण बाघों की मौत का लगभग कितना प्रतिशत है ?</p>",
                    options_en: ["<p>23%</p>", "<p>12%</p>", 
                                "<p>31%</p>", "<p>42%</p>"],
                    options_hi: ["<p>23%</p>", "<p>12%</p>",
                                "<p>31%</p>", "<p>42%</p>"],
                    solution_en: "<p>20.(c) According to the National Tiger Conservation Authority (NTCA), approximately 31% is accounted for tiger deaths due to poaching and electrocution from 2012 to 2018.India has 2,967 tigers, according to the latest population estimation which was carried out in 2018.</p>",
                    solution_hi: "<p>20.(c) राष्ट्रीय बाघ संरक्षण प्राधिकरण (NTCA) के अनुसार, 2012 से 2018 तक अवैध शिकार और बिजली के झटके के कारण लगभग 31% बाघों की मौत हुई है। 2018 में किए गए नवीनतम जनसंख्या अनुमान के अनुसार, भारत में 2,967 बाघ हैं।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "21",
                    section: "18",
                    question_en: "21. The Sharia is the law governing the _______community.",
                    question_hi: "21. शरिया _______समुदाय को नियंत्रित करने वाला कानून है।",
                    options_en: [" Muslim ", " Jewish ", 
                                " Christian ", " Jain "],
                    options_hi: [" मुसलमान", " यहूदी",
                                " ईसाई", " जैन"],
                    solution_en: "21.(a) The Sharia is the law governing the Muslim community. For many Muslims, the word means simply \"justice,\" and they will consider any law that promotes justice and social welfare to conform to Sharia.",
                    solution_hi: "21.(a) शरिया मुस्लिम समुदाय को नियंत्रित करने वाला कानून है। कई मुसलमानों के लिए, शब्द का अर्थ केवल \"न्याय\" है, और वे शरिया के अनुरूप न्याय और सामाजिक कल्याण को बढ़ावा देने वाले किसी भी कानून पर विचार करेंगे।",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "22",
                    section: "18",
                    question_en: "<p>22. When did Babur defeated Ibrahim Lodhi ?</p>",
                    question_hi: "<p>22. बाबर ने इब्राहिम लोदी को कब हराया था ?</p>",
                    options_en: ["<p>1526</p>", "<p>1628</p>", 
                                "<p>1739</p>", "<p>1761</p>"],
                    options_hi: ["<p>1526</p>", "<p>1628</p>",
                                "<p>1739</p>", "<p>1761</p>"],
                    solution_en: "<p>22.(a) In 1526, the Mughal forces of Babur, the king of Kabulistan (Kabul, present Afghanistan), defeated Ibrahim\'s much larger army in the First Battle of Panipat.</p>",
                    solution_hi: "<p>22.(a) 1526 में, काबुलिस्तान (काबुल, वर्तमान अफगानिस्तान) के राजा बाबर की मुगल सेना ने पानीपत की पहली लड़ाई में इब्राहिम की बहुत बड़ी सेना को हरा दिया।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "23",
                    section: "18",
                    question_en: "<p>23. Which of the following acid is the constituent of eyewash ?</p>",
                    question_hi: "<p>23. निम्नलिखित में से कौन सा अम्ल नेत्रधावन का घटक है ?</p>",
                    options_en: [" Sulphuric acid ", " Boric acid ", 
                                " Acetic acid ", " Hydrochloric acid"],
                    options_hi: [" सल्फ्यूरिक अम्ल ", " बोरिक अम्ल ",
                                " एसीटिक अम्ल", " हाइड्रोक्लोरिक अम्ल"],
                    solution_en: "<p>23.(b) Boric acid(H<sub>3</sub>BO<sub>3</sub>) is the constituent of eyewash. Boric acid is also used as a fungicide and as an insecticide powder. Boric Acid is a weakly acidic hydrate of boric oxide with mild antiseptic, antifungal, and antiviral properties.</p>",
                    solution_hi: "<p>23.(b) बोरिक एसिड (H<sub>3</sub>BO<sub>3</sub>) आईवॉश का घटक है। बोरिक एसिड का उपयोग कवकनाशी और कीटनाशक पाउडर के रूप में भी किया जाता है। बोरिक एसिड हल्के एंटीसेप्टिक, एंटिफंगल और एंटीवायरल गुणों के साथ बोरिक ऑक्साइड का एक कमजोर अम्लीय हाइड्रेट है।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "24",
                    section: "18",
                    question_en: "<p>24. Which article of the Indian Constitution guarantees &lsquo;equality of opportunity&rsquo; in the matters of public employment ?</p>",
                    question_hi: "<p>24. भारतीय संविधान का कौन सा अनुच्छेद सार्वजनिक रोजगार के मामलों में \'अवसर की समानता\' की गारंटी देता है</p>",
                    options_en: ["<p>Article 17</p>", "<p>Article 18</p>", 
                                "<p>Article 15</p>", "<p>Article 16</p>"],
                    options_hi: ["<p>अनुच्छेद 17</p>", "<p>अनुच्छेद 18</p>",
                                "<p>अनुच्छेद 15</p>", "<p>अनुच्छेद 16</p>"],
                    solution_en: "<p>24.(d) Article 16 -Equality of opportunity in matters of public employment. Article 15- Prohibition of discrimination on grounds of religion, race, caste, sex or place of birth. Article 18- Abolition of titles , Article 17- &ldquo;Untouchability&rdquo; is abolished .</p>",
                    solution_hi: "<p>24.(d) अनुच्छेद 16 - लोक नियोजन के मामलों में अवसर की समानता। अनुच्छेद 15- धर्म, मूलवंश, जाति, लिंग या जन्म स्थान के आधार पर भेदभाव का निषेध। अनुच्छेद 18- उपाधियों का उन्मूलन, अनुच्छेद 17- \"अस्पृश्यता\" को समाप्त किया जाता है।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "25",
                    section: "18",
                    question_en: "25. Which was the first state to implement a 10% reservation in government jobs and higher education for the Economically Weaker Section ?",
                    question_hi: "25. आर्थिक रूप से कमजोर वर्ग के लिए सरकारी नौकरियों और उच्च शिक्षा में 10% आरक्षण लागू करने वाला पहला राज्य कौन सा था ?",
                    options_en: [" Uttar Pradesh ", " Maharashtra ", 
                                " Madhya Pradesh  ", " Gujarat  "],
                    options_hi: [" ’उत्तर प्रदेश", " महाराष्ट्र",
                                " मध्य प्रदेश", " गुजरात"],
                    solution_en: "25.(d) Gujarat was the first state to implement a 10% reservation in government jobs and higher education for the Economically Weaker Section. It was done in the 103rd amendment.",
                    solution_hi: "25.(d) गुजरात आर्थिक रूप से कमजोर वर्ग के लिए सरकारी नौकरियों और उच्च शिक्षा में 10% आरक्षण लागू करने वाला पहला राज्य था। यह 103वें संशोधन में किया गया था।",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "26",
                    section: "18",
                    question_en: "<p>26. As of October 2020, who among the following is the CEO of Amazon and the owner of Blue Origin ?</p>",
                    question_hi: "<p>26. अक्टूबर 2020 तक, निम्नलिखित में से कौन अमेजन का CEO और ब्लू ओरिजिन का मालिक है ?</p>",
                    options_en: ["<p>Tim Cook</p>", "<p>Mark Zuckerberg</p>", 
                                "<p>Eric Schmidt</p>", "<p>Jeff Bezos</p>"],
                    options_hi: ["<p>टिम कुक</p>", "<p>मार्क जकरबर्ग</p>",
                                "<p>एरिक श्मिट</p>", "<p>जेफ बेजोस</p>"],
                    solution_en: "<p>26.(d) As of October 2020, Jeff Bezos is the CEO of Amazon and the owner of Blue Origin. Tim Cook CEO of Apple. Mark Zukerberg is the CEO of Facebook.</p>",
                    solution_hi: "<p>26.(d) अक्टूबर 2020 तक, जेफ बेजोस अमेज़न के CEO और ब्लू ओरिजिन के मालिक हैं। ऐप्पल के CEO टिम कुक है। मार्क जुकरबर्ग फेसबुक के सीईओ हैं।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "27",
                    section: "18",
                    question_en: "27. FAO is a specialized agency that works on  the behalf of the UN . Choose its full form.",
                    question_hi: "27. FAO एक विशेष एजेंसी है जो UN की ओर से काम करती है। इसका पूर्ण रूप क्या है?",
                    options_en: [" Food and Agriculture Organization ", " Foreign Aviation Organization", 
                                " Fund for Agriculture Organization", " Foreign Agriculture Organization"],
                    options_hi: [" Food and Agriculture Organization ", " Foreign Aviation Organization",
                                " Fund for Agriculture Organization", " Foreign Agriculture Organization"],
                    solution_en: "27.(a) Food and Agriculture Organisation (FAO),The functions of FAO: the Organization collects, analyses and disseminates information; advises governments on policy and planning. It was founded in October 1945. The FAO is composed of 197 member states headquartered in Rome.",
                    solution_hi: "27.(a) खाद्य और कृषि संगठन (FAO), FAO के कार्य: संगठन जानकारी एकत्र, विश्लेषण और प्रसार करता है; नीति और योजना पर सरकारों को सलाह देता है। इसकी स्थापना अक्टूबर 1945 में हुई थी। FAO 197 सदस्य देशों से बना है जिसका मुख्यालय रोम में है।",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "28",
                    section: "18",
                    question_en: "<p>28. Among the following, which satellite facility is equipped with the state-of-the-art data acquisition systems that receive data from various satellites ?</p>",
                    question_hi: "<p>28. निम्नलिखित में से कौन सी उपग्रह सुविधा अत्याधुनिक डेटा अधिग्रहण प्रणाली से सुसज्जित है जो विभिन्न उपग्रहों से डेटा प्राप्त करती है ?</p>",
                    options_en: ["<p>SCATSAT-1</p>", "<p>AGEOS</p>", 
                                "<p>ISRO</p>", "<p>IMGEOS</p>"],
                    options_hi: ["<p>SCATSAT-1</p>", "<p>AGEOS</p>",
                                "<p>ISRO</p>", "<p>IMGEOS</p>"],
                    solution_en: "<p>28.(d) Integrated Multi Mission Ground Segment for Earth Observation Satellites (IMGEOS) is the satellite facility equipped with the state-of-the-art data acquisition systems that receive data from various satellites.</p>",
                    solution_hi: "<p>28.(d) अर्थ ऑब्जर्वेशन सैटेलाइट्स के लिए इंटीग्रेटेड मल्टी मिशन ग्राउंड सेगमेंट (IMGEOS) अत्याधुनिक डेटा अधिग्रहण प्रणालियों से लैस उपग्रह सुविधा है जो विभिन्न उपग्रहों से डेटा प्राप्त करती है।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "29",
                    section: "18",
                    question_en: "<p>29. The study of landforms and their evolution is called:</p>",
                    question_hi: "<p>29. भू-आकृतियों और उनके विकास के अध्ययन को_____कहा जाता है।</p>",
                    options_en: ["<p>Soil Geography</p>", "<p>Climatology</p>", 
                                "<p>Geomorphology</p>", "<p>Hydrology</p>"],
                    options_hi: ["<p>मृदा भूगोल</p>", "<p>जलवायु विज्ञान</p>",
                                "<p>भू-आकृति विज्ञान</p>", "<p>जल विज्ञान</p>"],
                    solution_en: "<p>29.(c) The study of landforms and their evolution is called Geomorphology. Climatology is the study of climate and how it changes over time. Hydrology is the study of the distribution and movement of water both on and below the Earth\'s surface. Soil geography seeks to understand the distribution and formation of soils on the earth\'s surface.</p>",
                    solution_hi: "<p>29.(c) भू-आकृतियों और उनके विकास के अध्ययन को भू-आकृति विज्ञान कहा जाता है। जलवायु विज्ञान जलवायु का अध्ययन है और यह समय के साथ कैसे बदलता है। जल विज्ञान पृथ्वी की सतह पर और नीचे दोनों जगह पानी के वितरण और गति का अध्ययन है। मृदा भूगोल पृथ्वी की सतह पर मिट्टी के वितरण और गठन को समझने का प्रयास करता है।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "30",
                    section: "18",
                    question_en: "<p>30. Which acid is produced by human&rsquo;s stomach ?</p>",
                    question_hi: "<p>30. मनुष्य के आमाशय में कौन सा अम्ल उत्पन्न होता है ?</p>",
                    options_en: ["<p>Lactic acid</p>", "<p>Sulphuric acid</p>", 
                                "<p>Citric acid</p>", "<p>Hydrochloric acid</p>"],
                    options_hi: ["<p>लैक्टिक अम्ल</p>", "<p>सल्फ्यूरिक अम्ल</p>",
                                "<p>साइट्रिक अम्ल</p>", "<p>हाइड्रोक्लोरिक अम्ल</p>"],
                    solution_en: "<p>30.(d) Hydrochloric acid is produced by the human\'s stomach. Lactic acid is mainly produced in muscle cells and red blood cells.Sulfuric acid (H<sub>2</sub>SO<sub>4</sub>), also called oil of vitriol, or hydrogen sulfate, dense, colourless, oily, corrosive liquid; one of the most commercially important of all chemicals. Citric acid is found naturally in citrus fruits, especially lemons and limes.</p>",
                    solution_hi: "<p>30.(d) हाइड्रोक्लोरिक एसिड मानव के पेट द्वारा निर्मित होता है। लैक्टिक एसिड मुख्य रूप से मांसपेशियों की कोशिकाओं और लाल रक्त कोशिकाओं में उत्पन्न होता है। सल्फ्यूरिक एसिड (H<sub>2</sub>SO<sub>4</sub>), जिसे विट्रियल का तेल भी कहा जाता है, या हाइड्रोजन सल्फेट, घने, रंगहीन, तैलीय, संक्षारक तरल; सभी रसायनों में सबसे व्यावसायिक रूप से महत्वपूर्ण है। साइट्रिक एसिड प्राकृतिक रूप से खट्टे फलों में पाया जाता है, खासकर नींबू और नीबू में।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "31",
                    section: "18",
                    question_en: "<p>31. Who is the Sports Minister of India as of October 2020 ?</p>",
                    question_hi: "<p>31. अक्टूबर 2020 तक भारत के खेल मंत्री कौन हैं ?</p>",
                    options_en: ["<p>Hardeep Singh</p>", "<p>Dilip Pandey</p>", 
                                "<p>Kiren Rijiju</p>", "<p>RVS Rathore</p>"],
                    options_hi: ["<p>हरदीप सिंह</p>", "<p>दिलीप पांडे</p>",
                                "<p>किरण रिजिजू</p>", "<p>आर.वी.एस. राठौर</p>"],
                    solution_en: "<p>31.(c) Kiren Rijiju is the Sports Minister of India as of October 2020. Sports Minister of India Anurag Thakur 2021.</p>",
                    solution_hi: "<p>31.(c) किरेन रिजिजू अक्टूबर 2020 तक भारत के खेल मंत्री हैं। 2021 में भारत के खेल मंत्री अनुराग ठाकुर है ।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "32",
                    section: "18",
                    question_en: "<p>32. When was the INSAT-1B launched ?</p>",
                    question_hi: "<p>32. इन्सैट-1B को कब प्रक्षेपित किया गया था ?</p>",
                    options_en: ["<p>1990</p>", "<p>1989</p>", 
                                "<p>1987</p>", "<p>1983</p>"],
                    options_hi: ["<p>1990</p>", "<p>1989</p>",
                                "<p>1987</p>", "<p>1983</p>"],
                    solution_en: "<p>32.(d) The Indian National Satellite System or INSAT -1B, is a series of multipurpose geostationary satellites launched by ISRO to satisfy telecommunications, broadcasting, meteorology, and search and rescue operations. It was commissioned in 1983.</p>",
                    solution_hi: "<p>32.(d) भारतीय राष्ट्रीय उपग्रह प्रणाली या इन्सैट -1बी, दूरसंचार, प्रसारण, मौसम विज्ञान, और खोज और बचाव कार्यों को संतुष्ट करने के लिए इसरो द्वारा लॉन्च किए गए बहुउद्देशीय भूस्थिर उपग्रहों की एक श्रृंखला है। इसे 1983 में कमीशन किया गया था।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "33",
                    section: "18",
                    question_en: "<p>33. According to the Department of Agriculture and Cooperation of India, what is the approximate share of India in the production of pulses worldwide ?</p>",
                    question_hi: "<p>33. भारत के कृषि एवं सहकारिता विभाग के अनुसार विश्व भर में दालों के उत्पादन में भारत का लगभग कितना हिस्सा है ?</p>",
                    options_en: ["<p>11.2%</p>", "<p>25 %</p>", 
                                "<p>21%</p>", "<p>32.5%</p>"],
                    options_hi: ["<p>11.2%</p>", "<p>25 %</p>",
                                "<p>21%</p>", "<p>32.5%</p>"],
                    solution_en: "<p>33.(b) According to the Department of Agriculture and Cooperation of India, the approximate share of India in the production of pulses worldwide is 25%.</p>",
                    solution_hi: "<p>33.(b) भारत के कृषि और सहकारिता विभाग के अनुसार, दुनिया भर में दालों के उत्पादन में भारत का लगभग हिस्सा 25% है।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "34",
                    section: "18",
                    question_en: "34. As of Post Monsoon Estimates of 2018-2019, which of the following is the largest coffee -  producing state in India, according to the Coffee Board India ?",
                    question_hi: "34. कॉफी बोर्ड इंडिया के अनुसार, 2018-2019 के मानसून के बाद के अनुमानों के अनुसार, निम्नलिखित में से कौन भारत में सबसे बड़ा कॉफी उत्पादक राज्य है ?",
                    options_en: [" Punjab ", " Andhra Pradesh ", 
                                " Bihar ", " Karnataka "],
                    options_hi: [" पंजाब", " आंध्र प्रदेश",
                                " बिहार", " कर्नाटक"],
                    solution_en: "34.(d) Karnataka is the largest coffee producing state in India. Brazil is the largest coffee producing country in the world.Arabica is a type of coffee produced in Karnataka. Chikmagalur, Karnataka is famous for coffee.",
                    solution_hi: "34.(d) कर्नाटक भारत का सबसे बड़ा कॉफी उत्पादक राज्य है। ब्राजील दुनिया का सबसे बड़ा कॉफी उत्पादक देश है। अरेबिका कर्नाटक में उत्पादित एक प्रकार की कॉफी है। चिकमगलूर, कर्नाटक कॉफी के लिए प्रसिद्ध है।",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "35",
                    section: "18",
                    question_en: "<p>35. Which of the following agendas was signed by the world leaders in 1992 at UNCED that works for achieving Global Sustainable Development ?</p>",
                    question_hi: "<p>35. 1992 में UNCED में विश्व नेताओं द्वारा निम्नलिखित में से किस एजेंडा पर हस्ताक्षर किए गए थे जो वैश्विक सतत विकास को प्राप्त करने के लिए काम करता है ?</p>",
                    options_en: ["<p>Agenda 23</p>", "<p>Agenda 21</p>", 
                                "<p>Agenda 32</p>", "<p>Agenda 27</p>"],
                    options_hi: ["<p>एजेंडा 23</p>", "<p>एजेंडा 21</p>",
                                "<p>एजेंडा 32</p>", "<p>एजेंडा 27</p>"],
                    solution_en: "<p>35.(b) Agenda 21 was signed by the world leaders in 1992 at UNCED that works for achieving Global Sustainable Development. Agenda 21 is intended to set out an international program of action for achieving sustainable development during the 21st century.</p>",
                    solution_hi: "<p>35.(b) एजेंडा 21 पर 1992 में UNCED में विश्व नेताओं द्वारा हस्ताक्षर किए गए थे जो वैश्विक सतत विकास को प्राप्त करने के लिए काम करता है। एजेंडा 21 का उद्देश्य 21वीं सदी के दौरान सतत विकास प्राप्त करने के लिए कार्रवाई का एक अंतरराष्ट्रीय कार्यक्रम तैयार करना है।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "36",
                    section: "18",
                    question_en: "36. Which among the following was the capital city of British India (1773-1911) ?",
                    question_hi: "36. निम्नलिखित में से कौन ब्रिटिश भारत की राजधानी थी (1773-1911)",
                    options_en: [" Lucknow ", " Bombay ", 
                                " Calcutta ", " Delhi "],
                    options_hi: [" लखनऊ", " बॉम्बे",
                                " कलकत्ता", " दिल्ली "],
                    solution_en: "36.(c) Calcutta was the capital city of British India (1773-1911) after that Delhi became capital of India. King George V proclaimed the transfer of the capital from Calcutta to Delhi at the climax of the 1911 Imperial Durbar on 12 December 1911.",
                    solution_hi: "36.(c) कलकत्ता ब्रिटिश भारत की राजधानी थी (1773-1911) उसके बाद दिल्ली भारत की राजधानी बन गई। किंग जॉर्ज पंचम ने 12 दिसंबर 1911 को 1911 के शाही दरबार के चरमोत्कर्ष पर राजधानी को कलकत्ता से दिल्ली स्थानांतरित करने की घोषणा की।",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "37",
                    section: "18",
                    question_en: "<p>37. As of January 2019, which of the following ports is listed amongst the world &lsquo;s top 30 Container ports as per latest Lloyd report ?</p>",
                    question_hi: "<p>37. नवीनतम लॉयड रिपोर्ट के अनुसार जनवरी 2019 तक, निम्नलिखित में से कौन सा बंदरगाह दुनिया के शीर्ष 30 कंटेनर बंदरगाहों में सूचीबद्ध है ?</p>",
                    options_en: ["<p>Kandla Port</p>", "<p>Haldia Port</p>", 
                                "<p>Jawaharlal Nehru Port</p>", "<p>Mormugao Port</p>"],
                    options_hi: ["<p>कांडला बंदरगाह</p>", "<p>हल्दिया बंदरगाह</p>",
                                "<p>जवाहरलाल नेहरू बंदरगाह</p>", "<p>मोरमुगाओ बंदरगाह</p>"],
                    solution_en: "<p>37.(c) As of January 2019,Jawaharlal Nehru Port is listed amongst the world &lsquo;s top 30 Container ports as per latest Lloyd report.Nhava Sheva, is the largest container port in India and one of the most important harbours on the Western coast of the subcontinent.The Port of Shanghai is the biggest port in the world based on cargo throughput.</p>",
                    solution_hi: "<p>37.(c) जनवरी 2019 तक, जवाहरलाल नेहरू पोर्ट नवीनतम लॉयड रिपोर्ट के अनुसार दुनिया के शीर्ष 30 कंटेनर बंदरगाहों में सूचीबद्ध है। न्हावा शेवा, भारत का सबसे बड़ा कंटेनर बंदरगाह है और उपमहाद्वीप के पश्चिमी तट पर सबसे महत्वपूर्ण बंदरगाहों में से एक है। कार्गो थ्रूपुट के आधार पर पोर्ट ऑफ शंघाई दुनिया का सबसे बड़ा बंदरगाह है।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "38",
                    section: "18",
                    question_en: "38. Who among the following has authored (edited) “India: The Future Is Now” ?",
                    question_hi: "38. निम्नलिखित में से किसने \"इंडिया: द फ्यूचर इज़ नाउ\" का लेखक (संपादित) किया है ?",
                    options_en: [" Anita Desai", " Narendra Modi", 
                                " Kiran Desai", " Shashi Tharoor"],
                    options_hi: [" अनीता देसाई", " नरेंद्र मोदी",
                                " किरण देसाई", " शशि थरूर"],
                    solution_en: "38.(d) Shashi Tharoor  has authored (edited) “India: The Future Is Now”. Books by him , (The Great Indian Novel,Show Businesses,Riot,India: From Midnight to the Millennium Bookless in Baghdad,India Shastra: Reflections on the Nation in our Time,An Era of Darkness: The British Empire in India,Why I Am A Hindu,The Paradoxical Prime Minister,The Hindu Way: An Introduction to Hinduism, Pax Indica,The Five Dollar Smile: Fourteen Early Stories and a Farce in Two Acts,The Elephant, the Tiger and the Cellphone,Nehru: The Invention of India, Tharoorosaurus.)",
                    solution_hi: "38.(d) शशि थरूर ने \"इंडिया: द फ्यूचर इज़ नाउ\" लिखा (संपादित) किया है। उनके द्वारा पुस्तकें, (द ग्रेट इंडियन नॉवेल, शो बिजनेस, दंगा, भारत: मिडनाइट टू द मिलेनियम बुकलेस इन बगदाद, भारत शास्त्र: रिफ्लेक्शंस ऑन द नेशन इन अवर टाइम, एन एरा ऑफ डार्कनेस: द ब्रिटिश एम्पायर इन इंडिया, व्हाई आई एम ए हिंदू, द पैराडॉक्सिकल प्राइम मिनिस्टर, द हिंदू वे: एन इंट्रोडक्शन टू हिंदुइज्म, पैक्स इंडिका, द फाइव डॉलर स्माइल: फोर अर्ली स्टोरीज एंड ए फार्स इन टू एक्ट्स, द एलीफेंट, द टाइगर एंड द सेलफोन, नेहरू: द इन्वेंशन ऑफ भारत, थारूरोसॉरस।)",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "39",
                    section: "18",
                    question_en: "39. When did archaeologist B.B. Lal carry out excavation at Hastinapur, situated in Meerut district ?",
                    question_hi: "39. पुरातत्वविद् बी बी लाल ने मेरठ जिले में स्थित हस्तिनापुर में खुदाई कब की थी ?",
                    options_en: [" 1957-58", " 1949-50", 
                                " 1962-63", " 1951-52"],
                    options_hi: [" 1957-58", " 1949-50",
                                " 1962-63", " 1951-52"],
                    solution_en: "39.(d) B.B. Lal carried out excavations at Hastinapur, situated in Meerut district in 1951-52.Lal worked on the \"Archaeology of Ramayana Sites\" project funded by the ASI, which excavated five sites mentioned in the Hindu epic Ramayana ",
                    solution_hi: "39.(d) बी.बी.लाल ने 1951-52 में मेरठ जिले में स्थित हस्तिनापुर में खुदाई की।लाल ने एएसआई द्वारा वित्त पोषित \"रामायण स्थलों की पुरातत्व\" परियोजना पर काम किया, जिसने हिंदू महाकाव्य रामायण में वर्णित पांच स्थलों की खुदाई की।",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "40",
                    section: "misc",
                    question_en: "<p>40. Which of the following is the memorial of India&rsquo;s second Prime Minister Lal Bahadur Shastri ?</p>",
                    question_hi: "<p>40. निम्नलिखित में से कौन सा भारत के दूसरे प्रधानमंत्री लाल बहादुर शास्त्री का स्मारक है ?</p>",
                    options_en: ["<p>Kisan Ghat</p>", "<p>Shanti Van</p>", 
                                "<p>Vijay Ghat</p>", "<p>Raj Ghat</p>"],
                    options_hi: ["<p>किसान घाट</p>", "<p>शांति वन</p>",
                                "<p>विजय घाट</p>", "<p>राज घाट</p>"],
                    solution_en: "<p>40.(c) Vijay Ghat is the memorial of India&rsquo;s second Prime Minister Lal Bahadur Shastri.Shantivan, or the Forest of Peace, is the Samadhi, or cremation spot of India\'s first Prime Minister, Jawaharlal Nehru. Raj Ghat is the memorial of Mahatma Gandhi, the Father of the Nation. Kisan Ghat is the samadhi of Chaudhary Charan Singh.</p>",
                    solution_hi: "<p>40.(c) विजय घाट भारत के दूसरे प्रधान मंत्री लाल बहादुर शास्त्री का स्मारक है। शांतिवन, या शांति का वन, भारत के पहले प्रधान मंत्री जवाहरलाल नेहरू का समाधि या श्मशान स्थल है। राज घाट राष्ट्रपिता महात्मा गांधी का स्मारक है। किसान घाट चौधरी चरण सिंह की समाधि है।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>