<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Smart Test Interface</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        .question-nav{position:fixed;top:70px;right:0;height:calc(100vh - 70px);width:320px;transition:all .3s;z-index:100;display:flex;flex-direction:column}
        .question-content{margin-right:0;transition:all .3s}
        .question-box{width:40px;height:40px;margin:5px;cursor:pointer}
        .attempted{background-color:#3498db!important;color:white}
        .correct{background-color:#2ecc71!important;color:white}
        .incorrect{background-color:#e74c3c!important;color:white}
        .current{border:3px solid #f39c12!important}
        #question-boxes{overflow-y:auto;max-height:calc(100vh - 150px);padding-bottom:15px}
        @media (max-width:768px){.question-nav{width:100%;right:-100%}.question-content{margin-right:0}}
    </style>
</head>
<body>
    <div class="container-fluid p-0">
        <!-- Header with controls -->
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary sticky-top">
            <div class="container-fluid">
                <a class="navbar-brand" href="#">
                    <i class="fas fa-graduation-cap me-2"></i>Smart Test
                </a>
                <!-- Section selector -->
                <div class="navbar-nav me-auto section-tabs">
                    <select class="form-select" id="section-selector" onchange="switchSection(this.value)">
                        <option value="all">All Sections</option>
                    </select>
                </div>
                <div class="d-flex align-items-center">
                    <!-- Timer -->
                    <div class="bg-light text-primary rounded px-3 py-2 me-2 fw-bold" id="timer">
                        <i class="far fa-clock me-2"></i><span id="timer-display">100:00</span>
                    </div>
                    <!-- Submit button -->
                    <button class="btn btn-success me-2" id="submit-btn" onclick="submitTest()">
                        <i class="fas fa-check me-1"></i> Submit
                    </button>
                    <!-- Language toggle -->
                    <button class="btn btn-light me-2" id="lang-btn" onclick="toggleLanguage()">
                        <i class="fas fa-language"></i>
                    </button>
                    <!-- Menu toggle for mobile -->
                    <button class="btn btn-light d-lg-none" id="menu-toggle" onclick="toggleMenu()">
                        <i class="fas fa-th"></i>
                    </button>
                </div>
            </div>
        </nav>
        <div class="row m-0">
            <!-- Main content area -->
            <div class="col p-0 question-content" id="question-content">
                <div class="container py-4">
                    <!-- Question navigation -->
                    <div class="d-flex justify-content-between mb-4">
                        <button class="btn btn-outline-primary" onclick="prevQuestion()">
                            <i class="fas fa-chevron-left me-1"></i> Previous
                        </button>
                        <div class="fw-bold" id="question-counter">Question 1 of 100</div>
                        <button class="btn btn-outline-primary" onclick="nextQuestion()">
                            Next <i class="fas fa-chevron-right ms-1"></i>
                        </button>
                    </div>
                    <!-- Questions will be displayed here -->
                    <div id="questions-container"></div>
                </div>
            </div>
            <!-- Question navigation sidebar -->
            <div class="bg-light question-nav" id="question-nav">
                <div class="p-3">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h5 class="m-0">Questions</h5>
                        <div>
                            <span class="badge bg-secondary me-1" id="attempted-count">0</span>
                            <span class="badge bg-secondary" id="total-count">100</span>
                            <button class="btn btn-sm btn-outline-secondary ms-2 d-lg-none" onclick="toggleMenu()">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>
                    <div class="d-flex flex-wrap" id="question-boxes"></div>
                </div>
            </div>
        </div>
        <!-- Results modal -->
        <div class="modal fade" id="results-modal" tabindex="-1" aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content">
                    <div class="modal-header bg-primary text-white">
                        <h5 class="modal-title">Test Results</h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row text-center">
                            <div class="col-6 mb-3">
                                <div class="p-3 bg-primary bg-opacity-10 rounded">
                                    <h3 id="score-value">0</h3>
                                    <div class="small text-muted">Score</div>
                                </div>
                            </div>
                            <div class="col-6 mb-3">
                                <div class="p-3 bg-success bg-opacity-10 rounded">
                                    <h3 id="correct-value">0</h3>
                                    <div class="small text-muted">Correct</div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="p-3 bg-danger bg-opacity-10 rounded">
                                    <h3 id="incorrect-value">0</h3>
                                    <div class="small text-muted">Incorrect</div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="p-3 bg-secondary bg-opacity-10 rounded">
                                    <h3 id="unattempted-value">0</h3>
                                    <div class="small text-muted">Unattempted</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-primary" onclick="reviewTest()">Review Test</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 100 * 60;
        let resultsModal;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            resultsModal = new bootstrap.Modal(document.getElementById('results-modal'));
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 768) {
                document.getElementById('question-content').style.marginRight = '320px';
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["6"] = {
                name: "Reasoning",
                start: 0,
                end: 23
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="6">Reasoning</option>`;
            sections["18"] = {
                name: "General Awareness",
                start: 24,
                end: 48
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="18">General Awareness</option>`;
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 49,
                end: 73
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["7"] = {
                name: "English",
                start: 74,
                end: 98
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="7">English</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 99,
                end: 99
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "6",
                    question_en: "<p>1. In a certain code language, <br>&lsquo;A + B&rsquo; means &lsquo;A is the sister of B&rsquo;, <br>&lsquo;A &ndash; B&rsquo; means &lsquo;A is the brother of B&rsquo;, <br>&lsquo;A &times; B&rsquo; means &lsquo;A is the father of B&rsquo;, <br>&lsquo;A &divide; B&rsquo; means &lsquo;A is the wife of B&rsquo;. <br>Based on the above, how is P related to T if &lsquo;P + Q &divide; R &times; S &minus; T&rsquo;?</p>",
                    question_hi: "<p>1. एक निश्चित कूट भाषा में, <br>A + B का अर्थ है, A, B की बहन है\', <br>A - B का अर्थ है, A, B का भाई है\', <br>A &times; B का अर्थ है, A, B के पिता है\', <br>A &divide; B का अर्थ है, A, B की पत्नी है\'।<br>उपरोक्त के आधार पर, यदि \'P + Q &divide; R &times; S - T\' है, तो P, T से किस प्रकार संबंधित है?</p>",
                    options_en: [
                        "<p>Father&rsquo;s sister</p>",
                        "<p>Mother&rsquo;s sister</p>",
                        "<p>Sister</p>",
                        "<p>Mother</p>"
                    ],
                    options_hi: [
                        "<p>पिता की बहन</p>",
                        "<p>माँ की बहन</p>",
                        "<p>बहन</p>",
                        "<p>माँ</p>"
                    ],
                    solution_en: "<p>1.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1738991568786.png\" alt=\"rId4\" width=\"298\" height=\"145\"><br>P is the sister of T&rsquo;s mother.</p>",
                    solution_hi: "<p>1.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1738991568786.png\" alt=\"rId4\" width=\"298\" height=\"145\"><br>P, T की माँ की बहन है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "6",
                    question_en: "<p>2. In this question, three statements are given, followed by three conclusions numbered&nbsp;I, II and III. Assuming the statements to be true, even if they seem to be at variance&nbsp;with commonly known facts, decide which of the conclusion(s) logically follows/follow<br>from the statements.<br><strong>Statements:</strong><br>Some carrots are onions.<br>All onions are potatoes.<br>All potatoes are radish.<br><strong>Conclusions:</strong><br>I. Some carrots are radish.<br>II. All onions are radish.<br>III. No carrot is a potato.</p>",
                    question_hi: "<p>2. इस प्रश्न में तीन कथन दिए गए हैं, जिनके बाद तीन निष्कर्ष I, II और III दिए गए हैं। कथनों को सत्य&nbsp;मानते हुए भले ही वे सामान्य रूप से ज्ञात तथ्यों से अलग प्रतीत होते हों, निर्धारित करें कि कौन-सा/से&nbsp;निष्कर्ष दिए गए कथनों का तार्किक रूप से अनुसरण करता है/करते हैं।<br><strong>कथन:</strong><br>कुछ गाजर, प्याज हैं।<br>सभी प्याज, आलू हैं।<br>सभी आलू, मूली हैं।<br><strong>निष्कर्ष:</strong><br>I. कुछ गाजर, मूली हैं।<br>II. सभी प्याज, मूली हैं।<br>III. कोई गाजर, आलू नहीं है।</p>",
                    options_en: [
                        "<p>Only conclusion II follows.</p>",
                        "<p>Both conclusions I and II follow.</p>",
                        "<p>Only conclusion III follows.</p>",
                        "<p>Only conclusion I follows.</p>"
                    ],
                    options_hi: [
                        "<p>केवल निष्कर्ष II अनुसरण करता है।</p>",
                        "<p>निष्कर्ष I और II, दोनों अनुसरण करते हैं।</p>",
                        "<p>केवल निष्कर्ष III अनुसरण करता है।</p>",
                        "<p>केवल निष्कर्ष I अनुसरण करता है।</p>"
                    ],
                    solution_en: "<p>2.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1738991568877.png\" alt=\"rId5\" width=\"326\" height=\"94\"><br>Both conclusion I and II follow.</p>",
                    solution_hi: "<p>2.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1738991568997.png\" alt=\"rId6\" width=\"362\" height=\"114\"><br>निष्कर्ष I और II दोनों अनुसरण करते हैं।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "6",
                    question_en: "<p>3. In a certain code language, &lsquo;cat dog monkey&rsquo; is coded as &lsquo;55 66 88&rsquo;; &lsquo;camel cat donkey&rsquo; is coded as &lsquo;66 99 33&rsquo; and &lsquo;dog goat tiger&rsquo; is coded as &lsquo;88 11 22&rsquo;. How is &lsquo;monkey&rsquo; coded in that language?</p>",
                    question_hi: "<p>3. एक निश्चित कूट भाषा में \'cat dog monkey\' को \'55 66 88\' के रूप में कूटबद्ध किया गया है; &lsquo;camel cat donkey&rsquo; को \'66 99 33\' के रूप में कूटबद्ध किया गया है और &lsquo;dog goat tiger&rsquo; को \'88 11 22\' के रूप में कूटबद्ध किया गया है। उसी भाषा में \'monkey\' को किस प्रकार कूटबद्ध किया गया है?</p>",
                    options_en: [
                        "<p>11</p>",
                        "<p>66</p>",
                        "<p>55</p>",
                        "<p>88</p>"
                    ],
                    options_hi: [
                        "<p>11</p>",
                        "<p>66</p>",
                        "<p>55</p>",
                        "<p>88</p>"
                    ],
                    solution_en: "<p>3.(c)<br><span style=\"text-decoration: underline;\">cat</span> <span style=\"text-decoration: underline;\">dog</span> <strong>monkey </strong><math display=\"inline\"><mo>&#8594;</mo></math> <strong>55 </strong><span style=\"text-decoration: underline;\">66</span><strong> </strong><span style=\"text-decoration: underline;\">88</span> <br>camel <span style=\"text-decoration: underline;\">cat</span> donkey <math display=\"inline\"><mo>&#8594;</mo></math> <span style=\"text-decoration: underline;\">66<strong> </strong></span>99 33 <br><span style=\"text-decoration: underline;\">dog</span> goat tiger <math display=\"inline\"><mo>&#8594;</mo></math> <span style=\"text-decoration: underline;\">88</span><strong> </strong>11 22<br>From above coding the code for &lsquo;<strong>monkey</strong>&rsquo; is <strong>55</strong>.</p>",
                    solution_hi: "<p>3.(c)<br><span style=\"text-decoration: underline;\">cat</span> <span style=\"text-decoration: underline;\">dog</span> <strong>monkey </strong><math display=\"inline\"><mo>&#8594;</mo></math> <strong>55 </strong><span style=\"text-decoration: underline;\">66</span><strong> </strong><span style=\"text-decoration: underline;\">88</span> <br>camel <span style=\"text-decoration: underline;\">cat</span> donkey <math display=\"inline\"><mo>&#8594;</mo></math> <span style=\"text-decoration: underline;\">66<strong> </strong></span>99 33 <br><span style=\"text-decoration: underline;\">dog</span> goat tiger <math display=\"inline\"><mo>&#8594;</mo></math> <span style=\"text-decoration: underline;\">88</span><strong> </strong>11 22<br>उपरोक्त कोड से &lsquo;<strong>monkey</strong>&rsquo; का कोड <strong>55 </strong>है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "6",
                    question_en: "<p>4. Select the correct mirror image of the given combination when the mirror is placed at<br>MN as shown below.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1738991569110.png\" alt=\"rId7\" width=\"124\" height=\"103\"></p>",
                    question_hi: "<p>4. दर्पण को नीचे दर्शाए अनुसार MN पर रखे जाने पर दिए गए संयोजन के सही दर्पण प्रतिबिंब का चयन कीजिए।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1738991569110.png\" alt=\"rId7\" width=\"124\" height=\"103\"></p>",
                    options_en: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1738991569206.png\" alt=\"rId8\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1738991569333.png\" alt=\"rId9\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1738991569438.png\" alt=\"rId10\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1738991569528.png\" alt=\"rId11\"></p>"
                    ],
                    options_hi: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1738991569206.png\" alt=\"rId8\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1738991569333.png\" alt=\"rId9\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1738991569438.png\" alt=\"rId10\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1738991569528.png\" alt=\"rId11\"></p>"
                    ],
                    solution_en: "<p>4.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1738991569438.png\" alt=\"rId10\"></p>",
                    solution_hi: "<p>4.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1738991569438.png\" alt=\"rId10\"></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "6",
                    question_en: "<p>5. Select the option that is related to the fifth letter-cluster in the same way as the second letter-cluster is related to the first letter-cluster and the fourth letter-cluster is related to the third letter-cluster.<br>FREE : GTHI :: GAPS : HCSW :: SOAR : ?</p>",
                    question_hi: "<p>5. उस विकल्प का चयन करें जो पाँचवें अक्षर-समूह से उसी प्रकार संबंधित है जिस प्रकार दूसरा अक्षर-समूह, पहले अक्षर-समूह से संबंधित है और चौथा अक्षर-समूह, तीसरे अक्षर-समूह से संबंधित है।<br>FREE : GTHI :: GAPS : HCSW :: SOAR : ?</p>",
                    options_en: [
                        "<p>TQEV</p>",
                        "<p>TRDV</p>",
                        "<p>TQDV</p>",
                        "<p>TQDU</p>"
                    ],
                    options_hi: [
                        "<p>TQEV</p>",
                        "<p>TRDV</p>",
                        "<p>TQDV</p>",
                        "<p>TQDU</p>"
                    ],
                    solution_en: "<p>5.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1738991569645.png\" alt=\"rId12\" width=\"140\" height=\"101\"> ,&nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1738991569747.png\" alt=\"rId13\" width=\"140\" height=\"100\"><br>Similarly,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1738991569885.png\" alt=\"rId14\" width=\"140\"></p>",
                    solution_hi: "<p>5.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1738991569645.png\" alt=\"rId12\" width=\"140\" height=\"101\"> ,&nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1738991569747.png\" alt=\"rId13\" width=\"140\" height=\"100\"><br>इसी प्रकार,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1738991569885.png\" alt=\"rId14\" width=\"140\"></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "6",
                    question_en: "<p>6. Which of the following numbers will replace the question mark (?) in the given series?<br>67 100 146 179 225 258 ?</p>",
                    question_hi: "<p>6. निम्नलिखित में से कौन-सी संख्या दी गई श्रृंखला में प्रश्न चिह्न (?) के स्थान पर आएगी?<br>67 100 146 179 225 258 ?</p>",
                    options_en: [
                        "<p>304</p>",
                        "<p>292</p>",
                        "<p>318</p>",
                        "<p>325</p>"
                    ],
                    options_hi: [
                        "<p>304</p>",
                        "<p>292</p>",
                        "<p>318</p>",
                        "<p>325</p>"
                    ],
                    solution_en: "<p>6.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1738991570003.png\" alt=\"rId15\" width=\"392\" height=\"96\"></p>",
                    solution_hi: "<p>6.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1738991570003.png\" alt=\"rId15\" width=\"392\" height=\"96\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "6",
                    question_en: "<p>7. What will come in the place of &lsquo;?&rsquo; in the following equation if &lsquo;+&rsquo; and &lsquo;&times;&rsquo; are interchanged and &lsquo;&ndash;&rsquo; and &lsquo;&divide;&rsquo; are interchanged?&nbsp;<br>21 &ndash; 7 &times; 26 + 46 &divide; 36 = ?</p>",
                    question_hi: "<p>7. यदि \'+\' और \'&times;\' को आपस में बदल दिया जाए तथा \'&ndash;\' और \'&divide;\' को आपस में बदल दिया जाए तो निम्नलिखित समीकरण में (?) के स्थान पर क्या आएगा? <br>21 &ndash; 7 &times; 26 + 46 &divide; 36 = ?</p>",
                    options_en: [
                        "<p>908</p>",
                        "<p>1163</p>",
                        "<p>760</p>",
                        "<p>8667</p>"
                    ],
                    options_hi: [
                        "<p>908</p>",
                        "<p>1163</p>",
                        "<p>760</p>",
                        "<p>8667</p>"
                    ],
                    solution_en: "<p>7.(b) <strong>Given</strong> :- 21 - 7 &times; 26 + 46 &divide;&nbsp;36<br>As per given instruction after interchanging &lsquo;+&rsquo; and &lsquo;&times;&rsquo; and &lsquo;-&rsquo; and &lsquo;&divide;&rsquo; we get<br>21 &divide; 7 + 26 &times; 46 - 36<br>3 + 1196 - 36 = 1163</p>",
                    solution_hi: "<p>7.(b) <strong>दिया गया :-</strong> 21 - 7 &times; 26 + 46 &divide; 36<br>दिए गए निर्देश के अनुसार \'+\' और \'&times;\' तथा \'-\' और \'&divide;\' को आपस में बदलने पर हमें प्राप्त होता है<br>21 &divide; 7 + 26 &times; 46 - 36<br>3 + 1196 - 36 = 1163</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "6",
                    question_en: "<p>8. Select the combination of letters that when sequentially placed in the blanks of the<br>given series will complete the series.<br>_ n p _ r r _ p _ m m _ p q _ r q _ n m _ n p _ r r q _ n m</p>",
                    question_hi: "<p>8. वर्णों के उस संयोजन का चयन कीजिये जिसे दिए गए वर्ण श्रंखला के रिक्त स्थानों में क्रमिक रूप से&nbsp;रखने पर श्रंखला पूरी हो जाएगी।<br>_ n p _ r r _ p _ m m _ p q _ r q _ n m _ n p _ r r q _ n m</p>",
                    options_en: [
                        "<p>mqqrnrpmqp</p>",
                        "<p>mqqnnrpmqp</p>",
                        "<p>mrqnnrmpqp</p>",
                        "<p>mqqrnnpmqp</p>"
                    ],
                    options_hi: [
                        "<p>mqqrnrpmqp</p>",
                        "<p>mqqnnrpmqp</p>",
                        "<p>mrqnnrmpqp</p>",
                        "<p>mqqrnnpmqp</p>"
                    ],
                    solution_en: "<p>8.(b) <br><span style=\"text-decoration: underline;\"><strong>m</strong></span>np<span style=\"text-decoration: underline;\"><strong>q</strong></span>rr<span style=\"text-decoration: underline;\"><strong>q</strong></span>p<span style=\"text-decoration: underline;\"><strong>n</strong></span>m / m<span style=\"text-decoration: underline;\"><strong>n</strong></span>pq<span style=\"text-decoration: underline;\"><strong>r</strong></span>rq<span style=\"text-decoration: underline;\"><strong>p</strong></span>nm / <span style=\"text-decoration: underline;\"><strong>m</strong></span>np<span style=\"text-decoration: underline;\"><strong>q</strong></span>rrq<span style=\"text-decoration: underline;\"><strong>p</strong></span>nm</p>",
                    solution_hi: "<p>8.(b) <br><span style=\"text-decoration: underline;\"><strong>m</strong></span>np<span style=\"text-decoration: underline;\"><strong>q</strong></span>rr<span style=\"text-decoration: underline;\"><strong>q</strong></span>p<span style=\"text-decoration: underline;\"><strong>n</strong></span>m / m<span style=\"text-decoration: underline;\"><strong>n</strong></span>pq<span style=\"text-decoration: underline;\"><strong>r</strong></span>rq<span style=\"text-decoration: underline;\"><strong>p</strong></span>nm / <span style=\"text-decoration: underline;\"><strong>m</strong></span>np<span style=\"text-decoration: underline;\"><strong>q</strong></span>rrq<span style=\"text-decoration: underline;\"><strong>p</strong></span>nm</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "6",
                    question_en: "<p>9. Each of the letters in the word MASONRY are arranged in alphabetical order. How many letters are there in the English alphabetical series between the letter that is third from the left and the one that is second from the right in the new letter cluster thus formed?</p>",
                    question_hi: "<p>9. MASONRY शब्द के प्रत्येक अक्षर को वर्णमाला क्रम में व्यवस्थित किया जाता है। इस प्रकार बने नए अक्षर समूह में बाएं से तीसरे अक्षर और दाएं से दूसरे अक्षर के बीच अंग्रेजी वर्णमाला श्रृंखला में कितने अक्षर हैं?</p>",
                    options_en: [
                        "<p>7</p>",
                        "<p>6</p>",
                        "<p>5</p>",
                        "<p>4</p>"
                    ],
                    options_hi: [
                        "<p>7</p>",
                        "<p>6</p>",
                        "<p>5</p>",
                        "<p>4</p>"
                    ],
                    solution_en: "<p>9.(d)<br><strong>Given</strong> :- MASONRY <br>After arranging the letters in alphabetical order we get<br>AM<strong>N</strong>OR<strong>S</strong>Y<br>Third from the left is N and second from right is S. There are 4(O, P, Q, R) letters in the English alphabetical series between N and S.</p>",
                    solution_hi: "<p>9.(d)<br><strong>दिया गया :-</strong> MASONRY <br>अक्षरों को वर्णमाला क्रम में व्यवस्थित करने के बाद हमें प्राप्त होता है<br>AM<strong>N</strong>OR<strong>S</strong>Y<br>बाएं से तीसरा N है और दाएं से दूसरा S है। अंग्रेजी वर्णमाला श्रृंखला में N और S के बीच 4 (O, P, Q, R) अक्षर हैं।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "6",
                    question_en: "<p>10. Which letter-cluster will replace the question mark (?) to complete the given series? <br>NDGB, OFJF, ?, TMSQ, XRYX</p>",
                    question_hi: "<p>10. दी गई शृंखला को पूरा करने के लिए प्रश्न चिह्न (?) के स्थान पर कौन-सा अक्षर-समूह आएगा?<br>NDGB, OFJF, ?, TMSQ, XRYX</p>",
                    options_en: [
                        "<p>QJNK</p>",
                        "<p>QINK</p>",
                        "<p>RINJ</p>",
                        "<p>RHNK</p>"
                    ],
                    options_hi: [
                        "<p>QJNK</p>",
                        "<p>QINK</p>",
                        "<p>RINJ</p>",
                        "<p>RHNK</p>"
                    ],
                    solution_en: "<p>10.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1738991570100.png\" alt=\"rId16\" width=\"401\" height=\"145\"></p>",
                    solution_hi: "<p>10.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1738991570100.png\" alt=\"rId16\" width=\"401\" height=\"145\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "6",
                    question_en: "<p>11. Identify the figure given in the options that when put in place of the question mark (?) will logically complete the series.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1738991570228.png\" alt=\"rId17\" width=\"344\" height=\"81\"></p>",
                    question_hi: "<p>11. विकल्पों में दी गई उस आकृति को पहचानिए जिसे प्रश्न चिह्न (?) के स्थान पर रखने पर श्रृंखला तार्किक रूप से पूरी हो जाएगी।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1738991570228.png\" alt=\"rId17\" width=\"344\" height=\"81\"></p>",
                    options_en: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1738991570320.png\" alt=\"rId18\" width=\"90\" height=\"90\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1738991570429.png\" alt=\"rId19\" width=\"90\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1738991570545.png\" alt=\"rId20\" width=\"90\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1738991570654.png\" alt=\"rId21\" width=\"90\"></p>"
                    ],
                    options_hi: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1738991570320.png\" alt=\"rId18\" width=\"90\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1738991570429.png\" alt=\"rId19\" width=\"90\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1738991570545.png\" alt=\"rId20\" width=\"90\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1738991570654.png\" alt=\"rId21\" width=\"90\"></p>"
                    ],
                    solution_en: "<p>11.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1738991570429.png\" alt=\"rId19\" width=\"90\"></p>",
                    solution_hi: "<p>11.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1738991570429.png\" alt=\"rId19\" width=\"90\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "6",
                    question_en: "<p>12. Three statements are given, followed by Three conclusions numbered I, II and III. Assuming the statements to be true, even if they seem to be at variance with commonly known facts, decide which of the conclusions logically follow(s) from the statements.&nbsp;<br><strong>Statements</strong> : <br>No chess is a can. <br>Not a single can is a horn. <br>Every horn is an adapter. <br><strong>Conclusions</strong> : <br>I. Some adapters which are horns are chess as well. <br>II. No chess is a horn. <br>III. Some adapters are horns.</p>",
                    question_hi: "<p>12. तीन कथन दिए गए हैं जिनके बाद I, II और III से संख्&zwj;यांकित तीन निष्कर्ष दिए गए हैं। कथनों को सत्य मानते हुए, भले ही वे सामान्य रूप से ज्ञात तथ्यों से भिन्न प्रतीत होते हों, तय कीजिए कि कौन सा/से निष्कर्ष, कथनों का तार्किक रूप से अनुसरण करता/करतेहै/हैं?<br><strong>कथन</strong> : <br>कोई भी चेस, कैन नहीं है। <br>एक भी कैन, हॉर्न नहीं है। <br>प्रत्येक हॉर्न, एडेप्टर है। <br><strong>निष्कर्ष</strong> : <br>I. कुछ एडेप्टर जो हॉर्न हैं, वे चेस भी हैं। <br>II. कोई भी चेस, हॉर्न नहीं है। <br>III. कुछ एडेप्टर, हॉर्न हैं।</p>",
                    options_en: [
                        "<p>Both conclusions II and III follow</p>",
                        "<p>Only conclusion III follows</p>",
                        "<p>All conclusions follow</p>",
                        "<p>Either conclusion I or II follows</p>"
                    ],
                    options_hi: [
                        "<p>निष्कर्ष II और III दोनों अनुसरण करते हैं</p>",
                        "<p>केवल निष्कर्ष III अनुसरण करता है</p>",
                        "<p>सभी निष्कर्ष अनुसरण करते हैं</p>",
                        "<p>या तो निष्कर्ष I या II अनुसरण करता है</p>"
                    ],
                    solution_en: "<p>12.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1738991570791.png\" alt=\"rId22\" width=\"364\" height=\"91\"><br>Only conclusion III follow.</p>",
                    solution_hi: "<p>12.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1738991571019.png\" alt=\"rId23\" width=\"350\"><br>केवल निष्कर्ष III अनुसरण करता है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "6",
                    question_en: "<p>13. Select the correct mirror image of the given combination when the mirror is placed at MN as shown below.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1738991571155.png\" alt=\"rId24\" width=\"119\" height=\"113\"></p>",
                    question_hi: "<p>13. दर्पण को नीचे दर्शाए अनुसार MN पर रखे जाने पर दिए गए संयोजन के सही दर्पण प्रतिबिंब का चयन कीजिए।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1738991571155.png\" alt=\"rId24\" width=\"119\" height=\"113\"></p>",
                    options_en: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1738991571287.png\" alt=\"rId25\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1738991571379.png\" alt=\"rId26\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1738991571475.png\" alt=\"rId27\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1738991571573.png\" alt=\"rId28\"></p>"
                    ],
                    options_hi: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1738991571287.png\" alt=\"rId25\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1738991571379.png\" alt=\"rId26\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1738991571475.png\" alt=\"rId27\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1738991571573.png\" alt=\"rId28\"></p>"
                    ],
                    solution_en: "<p>13.(a) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1738991571287.png\" alt=\"rId25\"></p>",
                    solution_hi: "<p>13.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1738991571287.png\" alt=\"rId25\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "6",
                    question_en: "<p>14. In a certain code language, TRUST is written as UTXWY and GROUP is written as HTRYU. How will VISIT be written in that language?</p>",
                    question_hi: "<p>14. एक निश्चित कूट भाषा में \'TRUST\' को \'UTXWY\' लिखा जाता है और \'GROUP\' को \'HTRYU\' लिखा जाता है। उस भाषा में \'VISIT\' कैसे लिखा जाएगा?</p>",
                    options_en: [
                        "<p>WLUNY</p>",
                        "<p>XKUNY</p>",
                        "<p>WKUNZ</p>",
                        "<p>WKVMY</p>"
                    ],
                    options_hi: [
                        "<p>WLUNY</p>",
                        "<p>XKUNY</p>",
                        "<p>WKUNZ</p>",
                        "<p>WKVMY</p>"
                    ],
                    solution_en: "<p>14.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1738991571667.png\" alt=\"rId29\" width=\"130\" height=\"77\">,<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1738991571835.png\" alt=\"rId30\" width=\"144\" height=\"83\"><br>similarly,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1738991571952.png\" alt=\"rId31\" width=\"128\" height=\"75\"></p>",
                    solution_hi: "<p>14.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1738991571667.png\" alt=\"rId29\" width=\"130\" height=\"77\">,<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1738991571835.png\" alt=\"rId30\" width=\"144\" height=\"83\"><br>इसी प्रकार,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1738991571952.png\" alt=\"rId31\" width=\"128\" height=\"75\"></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "6",
                    question_en: "<p>15. Which of the following terms will replace the question mark (?) in the given series? <br>SOWJ, QPUK, OQSL, ?, KSON</p>",
                    question_hi: "<p>15. निम्नलिखित शृंखला में प्रश्न-चिह्न (?) के स्थान पर नीचे दिए गए विकल्पों में से कौन-सा अक्षर-समूह आएगा?<br>SOWJ, QPUK, OQSL, ?, KSON</p>",
                    options_en: [
                        "<p>MSQN</p>",
                        "<p>MRQM</p>",
                        "<p>NRPM</p>",
                        "<p>NQQL</p>"
                    ],
                    options_hi: [
                        "<p>MSQN</p>",
                        "<p>MRQM</p>",
                        "<p>NRPM</p>",
                        "<p>NQQL</p>"
                    ],
                    solution_en: "<p>15.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1738991572108.png\" alt=\"rId32\" width=\"383\" height=\"106\"></p>",
                    solution_hi: "<p>15.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1738991572108.png\" alt=\"rId32\" width=\"383\" height=\"106\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "6",
                    question_en: "<p>16. 49 is related to 96 following a certain logic. Following the same logic, 54 is related to 106. To which of the following is 36 related following the same logic?<br>(NOTE: Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g. 13 &ndash; Operations on 13 such as adding /subtracting /multiplying etc. to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3is not allowed.)</p>",
                    question_hi: "<p>16. 49, एक निश्चित तर्क का अनुसरण करते हुए 96 से संबंधित है। 54, समान तर्क का अनुसरण करते हुए 106 से संबंधित है। समान तर्क का अनुसरण करते हुए, 36, निम्नलिखित में से किससे संबंधित है?<br>(ध्यान दें: संख्याओं को उनके घटक अंकों में अलग-अलग किए बिना पूर्ण संख्याओं पर संक्रियाएं की जानी चाहिए। उदाहरण के लिए संख्या 13- संख्या 13 पर संक्रियाएं जैसे 13 को जोड़ना/घटाना/गुणा करना आदि किया जा सकता है। 13 को 1 और 3 में अलग-अलग करने की और फिर 1 और 3 पर गणितीय संक्रियाएं करने की अनुमति नहीं है।)</p>",
                    options_en: [
                        "<p>78</p>",
                        "<p>62</p>",
                        "<p>70</p>",
                        "<p>56</p>"
                    ],
                    options_hi: [
                        "<p>78</p>",
                        "<p>62</p>",
                        "<p>70</p>",
                        "<p>56</p>"
                    ],
                    solution_en: "<p>16.(c) <strong>Logic:-</strong> (1st number -1)&times; 2 = 2nd number<br>(49 - 96) :- (49 - 1)&times; 2 &rArr; (48)&times; 2 = 96<br>(54 - 106) :- (54 - 1)&times; 2 &rArr; (53) &times; 2 = 106 <br>Similarly,<br>(36 - 70) :- (36 - 1)&times; 2 &rArr; (35)&times; 2 = 70</p>",
                    solution_hi: "<p>16.(c) <strong>तर्क:-</strong> (पहली संख्या -1)&times; 2 = दूसरी संख्या<br>(49 - 96) :- (49 - 1)&times; 2 &rArr; (48)&times; 2 = 96<br>(54 - 106) :- (54 - 1)&times; 2 &rArr; (53) &times; 2 = 106 <br>इसी प्रकार,<br>(36 - 70) :- (36 - 1)&times; 2 &rArr; (35)&times; 2 = 70</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "6",
                    question_en: "<p>17. Six letters N, P, R, S, T and U are written on different faces of a dice, Two positions of this dice are shown in the figure. Which is the letter on the face opposite to the face containing U?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1738991572213.png\" alt=\"rId33\" width=\"200\" height=\"96\"></p>",
                    question_hi: "<p>17. एक पासे के अलग-अलग फलकों पर छः अक्षर-N,P,R,S,T और U अंकित हैं। निम्न आकृति में इसी पासे की दो अलग-अलग स्थितियां दर्शाई गई हैं। अक्षर U वाले फलक के विपरीत फलक पर कौन-सा अक्षर है?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1738991572213.png\" alt=\"rId33\" width=\"200\" height=\"96\"></p>",
                    options_en: [
                        "<p>N</p>",
                        "<p>R</p>",
                        "<p>P</p>",
                        "<p>S</p>"
                    ],
                    options_hi: [
                        "<p>N</p>",
                        "<p>R</p>",
                        "<p>P</p>",
                        "<p>S</p>"
                    ],
                    solution_en: "<p>17.(b) From the two dice the opposite face are :- P &harr; S , U &harr; R</p>",
                    solution_hi: "<p>17.(b) दो पासों से विपरीत फलक हैं:- P &harr; S , U &harr; R</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "6",
                    question_en: "<p>18. Select the set in which the numbers are related in the same way as are the numbers of the given sets.<br>(NOTE : Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g. 13- Operations on 13 such as adding/deleting/multiplying, etc. to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed.)<br>(9, 36, 12)<br>(12, 48, 16)</p>",
                    question_hi: "<p>18. उस समुच्चय का चयन करें जिसमें दी गई संख्याएँ आपस में उसी प्रकार संबंधित हैं, जिस प्रकार प्रश्न में दिए गए समुच्चय की संख्याएँ आपस में संबंधित हैं।<br>(ध्यान दें : संख्याओं को उनके घटक अंकों में तोड़े बिना, पूर्ण संख्याओं पर गणितीय संक्रियाएँ की जानी चाहिए। जैसे, 13 के मामले में - 13 पर की जाने वाली विभिन्न गणितीय संक्रियाएँ जैसे 13 में जोड़ना/घटाना/ गुणा करना आदि 13 पर की जा सकती हैं। लेकिन 13 को 1 और 3 में तोड़ने और फिर 1 और 3 पर गणितीय संक्रियाएँ करने की अनुमति नहीं है।)<br>(9, 36, 12)<br>(12, 48, 16)</p>",
                    options_en: [
                        "<p>(21, 84, 24)</p>",
                        "<p>(21, 81,28)</p>",
                        "<p>(21, 81,24)</p>",
                        "<p>(21, 84, 28)</p>"
                    ],
                    options_hi: [
                        "<p>(21, 84, 24)</p>",
                        "<p>(21, 81,28)</p>",
                        "<p>(21, 81,24)</p>",
                        "<p>(21, 84, 28)</p>"
                    ],
                    solution_en: "<p>18.(d) <strong>Logic</strong> :- (3rd number - 1st number) &times; 12 = 2nd number<br>(9, 36, 12) :- (12 - 9)&times;12 &rArr; (3) &times; 12 = 36<br>(12, 48, 16) :- (16 - 12)&times;12 &rArr; (4)&times;12 = 48<br>Similarly,<br>(21, 84, 28) :- (28 - 21)&times;12 &rArr; (7)&times; 12 = 84</p>",
                    solution_hi: "<p>18.(d) <strong>तर्क</strong> :- (तीसरी संख्या - पहली संख्या ) &times; 12 = दूसरी संख्या<br>(9, 36, 12) :- (12 - 9)&times;12 &rArr; (3) &times; 12 = 36<br>(12, 48, 16) :- (16 - 12)&times;12 &rArr; (4) &times;12 = 48<br>इसी प्रकार,<br>(21, 84, 28) :- (28 - 21)&times;12 &rArr; (7) &times; 12 = 84</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "6",
                    question_en: "<p>19. Identify the figure given in the options that when put in place of the question mark (?) will logically complete the series.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1738991572352.png\" alt=\"rId34\" width=\"389\" height=\"79\"></p>",
                    question_hi: "<p>19. विकल्पों में दी गई उस आकृति को पहचानिए जिसे प्रश्न चिह्न (?) के स्थान पर रखने पर श्रृंखला तार्किक रूप से पूरी हो जाएगी।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1738991572352.png\" alt=\"rId34\" width=\"389\" height=\"79\"></p>",
                    options_en: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1738991572482.png\" alt=\"rId35\" width=\"90\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1738991572600.png\" alt=\"rId36\" width=\"90\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1738991572746.png\" alt=\"rId37\" width=\"90\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1738991572868.png\" alt=\"rId38\" width=\"90\"></p>"
                    ],
                    options_hi: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1738991572482.png\" alt=\"rId35\" width=\"90\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1738991572600.png\" alt=\"rId36\" width=\"90\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1738991572746.png\" alt=\"rId37\" width=\"90\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1738991572868.png\" alt=\"rId38\" width=\"90\"></p>"
                    ],
                    solution_en: "<p>19.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1738991572977.png\" alt=\"rId39\" width=\"90\"></p>",
                    solution_hi: "<p>19.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1738991572977.png\" alt=\"rId39\" width=\"90\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "6",
                    question_en: "<p>20. Which two numbers should be interchanged to make the given equation correct? <br>17 &times; 7 + (21 &divide; 3) &times; 5 &ndash; 28 + 34 = 72 <br>(NOTE : Numbers must be interchanged and not the constituent digits e.g. if 2 and 3 are to be interchanged in the equation 43 &times; 3 + 4 &divide; 2, then interchanged equation is 43 &times; 2 + 4 &divide; 3)</p>",
                    question_hi: "<p>20. दिए गए समीकरण को सही बनाने के लिए किन दो संख्याओं को आपस में बदला जाना चाहिए? <br>17 &times; 7 + (21 &divide; 3) &times; 5 &ndash; 28 + 34 = 72 <br>(ध्यान दें : संख्याओं को आपस में बदला जाना चाहिए और घटक अंकों को नहीं बदला जाना चाहिए। उदा. यदि समीकरण 43 &times; 3 + 4 &divide; 2 में 2 और 3 को आपस में बदलना है, तो बदला हुआ समीकरण 43 &times; 2 + 4 &divide; 3 होगा)</p>",
                    options_en: [
                        "<p>21 and 28</p>",
                        "<p>5 and 7</p>",
                        "<p>17 and 28</p>",
                        "<p>7 and 3</p>"
                    ],
                    options_hi: [
                        "<p>21 और 28</p>",
                        "<p>5 और 7</p>",
                        "<p>17 और 28</p>",
                        "<p>7 और 3</p>"
                    ],
                    solution_en: "<p>20.(d) <strong>Given</strong> :- 17 &times; 7 + (21 &divide; 3) &times; 5 - 28 + 34 = 72<br>After going through all the options, option (d) satisfies. After interchanging 7 and 3 we get<br>17 &times; 3 + (21 &divide; 7) &times; 5 - 28 + 34<br>51 + 3 &times; 5 + 6<br>51 + 15 + 6 = 72<br>L.H.S. = R.H.S.</p>",
                    solution_hi: "<p>20.(d) <strong>दिया गया :-</strong> 17 &times; 7 + (21 &divide; 3) &times; 5 - 28 + 34 = 72<br>सभी विकल्पों की जांच करने पर विकल्प (d) संतुष्ट करता है। 7 और 3 को आपस में बदलने पर हमें प्राप्त होता है<br>17 &times; 3 + (21 &divide; 7) &times; 5 - 28 + 34<br>51 + 3 &times; 5 + 6<br>51 + 15 + 6 = 72<br>L.H.S. = R.H.S.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "6",
                    question_en: "<p>21. The age of a father will be double the age of his son ten years later. 10 years ago, the father&rsquo;s age was six times the age of the son. How many years from now, the ratio of their ages will be 3 : 2 ?</p>",
                    question_hi: "<p>21. दस साल बाद एक पिता की उम्र उसके बेटे की उम्र से दोगुनी हो जाएगी। 10 साल पहले, पिता की उम्र बेटे की उम्र से छः गुना थी। अब से कितने वर्ष बाद उनकी आयु का अनुपात 3 : 2 होगा?</p>",
                    options_en: [
                        "<p>30</p>",
                        "<p>20</p>",
                        "<p>35</p>",
                        "<p>25</p>"
                    ],
                    options_hi: [
                        "<p>30</p>",
                        "<p>20</p>",
                        "<p>35</p>",
                        "<p>25</p>"
                    ],
                    solution_en: "<p>21.(c)<br>Let present age of father be x&nbsp;and son be y<br>10 year later -<br>(x&nbsp;+ 10) = 2(y + 10)<br>x - 2y = 10 -----(i)<br>10 year ago - <br>(x&nbsp;- 10) = 6(y - 10)<br>x - 6y = - 50-----(ii)<br>By the equation (i) - equation (ii) we get;<br>4y = 60 <math display=\"inline\"><mo>&#8658;</mo></math> y = 15<br>Put y = 15 in equation (i) we get;<br>x - 2 (15) = 10 &rArr; x = 40 <br>So, present age of father and son be 40 and 15 respectively<br>Now, let after \'T\'&nbsp;years ratio of their age be 3 : 2<br>Then, <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>40</mn><mo>+</mo><mi mathvariant=\"normal\">T</mi></mrow><mrow><mn>15</mn><mo>+</mo><mi mathvariant=\"normal\">T</mi></mrow></mfrac></math> = 32<br>80 + 2T&nbsp;= 45 + 3T &rArr; T = 35 years</p>",
                    solution_hi: "<p>21.(c)<br>माना पिता की वर्तमान आयु x&nbsp;और पुत्र की आयु y है<br>10 साल बाद -<br>(x&nbsp;+ 10) = 2(y + 10)<br>x - 2y = 10 -----(i)<br>10 साल पहले - <br>(x&nbsp;- 10) = 6(y - 10)<br>x - 6y = - 50-----(ii)<br>समीकरण (i) - समीकरण (ii) से हम पाते हैं;<br>4y = 60 <math display=\"inline\"><mo>&#8658;</mo></math> y = 15<br>समीकरण (i) में y = 15 रखने पर हमें प्राप्त होता है;<br>x - 2 (15) = 10 &rArr; x = 40 <br>तो, पिता और पुत्र की वर्तमान आयु क्रमशः 40 और 15 वर्ष है<br>अब, मान लीजिए कि \'T\' वर्ष के बाद उनकी आयु का अनुपात 3 : 2 है<br>फिर, <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>40</mn><mo>+</mo><mi mathvariant=\"normal\">T</mi></mrow><mrow><mn>15</mn><mo>+</mo><mi mathvariant=\"normal\">T</mi></mrow></mfrac></math> = 32<br>80 + 2T&nbsp;= 45 + 3T &rArr; T = 35 वर्ष</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "6",
                    question_en: "<p>22. 12 is related to 40 following a certain logic. Following the same logic, 9 is related to 31. To which of the following is 16 related using the same logic?</p>",
                    question_hi: "<p>22. एक निश्चित तर्क का अनुसरण करते हुए 12, 40 से संबंधित है। उसी तर्क का अनुसरण करते हुए, 9, 31 से संबंधित है। उसी तर्क का उपयोग करते हुए, 16 निम्न में से किससे संबंधित है?</p>",
                    options_en: [
                        "<p>51</p>",
                        "<p>52</p>",
                        "<p>63</p>",
                        "<p>61</p>"
                    ],
                    options_hi: [
                        "<p>51</p>",
                        "<p>52</p>",
                        "<p>63</p>",
                        "<p>61</p>"
                    ],
                    solution_en: "<p>22.(b) <strong>Logic</strong> :- (1st number &times; 3) + 4 = (2nd number) <br>(12, 40) :- (12 &times; 3)+4 &rArr; 36 + 4 = 40<br>(9, 31) :- (9 &times; 3)+ 4 &rArr; 27 + 4 = 31<br>Similarly,<br>(16, ?) :- (16 &times; 3)+ 4 &rArr; 48 + 4 = 52</p>",
                    solution_hi: "<p>22.(b) <strong>तर्क</strong> :- (पहली संख्या &times; 3) + 4 = (दूसरी संख्या)<br>(12, 40) :- (12 &times; 3)+4 &rArr; 36 + 4 = 40<br>(9, 31) :- (9 &times; 3)+4 &rArr; 27 + 4 = 31<br>इसी प्रकार,<br>(16, ?) :- (16 &times; 3)+4 &rArr; 48 + 4 = 52</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "23",
                    section: "6",
                    question_en: "<p>23. Select the option figure in which the given figure (X) is embedded as its part (rotation is NOT allowed). <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1738991573091.png\" alt=\"rId40\" width=\"127\" height=\"109\"></p>",
                    question_hi: "<p>23. उस विकल्प आकृति का चयन कीजिए, जिसमें दी गई आकृति (X) उसके एक भाग के रूप में निहित है (घूर्णन की अनुमति नहीं है)।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1738991573091.png\" alt=\"rId40\" width=\"127\" height=\"109\"></p>",
                    options_en: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1738991573210.png\" alt=\"rId41\" width=\"120\" height=\"108\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1738991573301.png\" alt=\"rId42\" width=\"120\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1738991573391.png\" alt=\"rId43\" width=\"120\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1738991573500.png\" alt=\"rId44\" width=\"120\"></p>"
                    ],
                    options_hi: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1738991573210.png\" alt=\"rId41\" width=\"120\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1738991573301.png\" alt=\"rId42\" width=\"120\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1738991573391.png\" alt=\"rId43\" width=\"120\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1738991573500.png\" alt=\"rId44\" width=\"120\"></p>"
                    ],
                    solution_en: "<p>23.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1738991573678.png\" alt=\"rId45\" width=\"120\"></p>",
                    solution_hi: "<p>23.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1738991573678.png\" alt=\"rId45\" width=\"120\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "24",
                    section: "6",
                    question_en: "<p>24. How many rectangles are there in the given figure?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1738991573857.png\" alt=\"rId46\" width=\"186\" height=\"122\"></p>",
                    question_hi: "<p>24. दी गई आकृति में कितने आयत हैं?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1738991573857.png\" alt=\"rId46\" width=\"186\" height=\"122\"></p>",
                    options_en: [
                        "<p>7</p>",
                        "<p>5</p>",
                        "<p>6</p>",
                        "<p>8</p>"
                    ],
                    options_hi: [
                        "<p>7</p>",
                        "<p>5</p>",
                        "<p>6</p>",
                        "<p>8</p>"
                    ],
                    solution_en: "<p>24.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1738991573957.png\" alt=\"rId47\" width=\"298\" height=\"176\"><br>Total number of rectangles = 6 + ABDC = 7</p>",
                    solution_hi: "<p>24.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1738991573957.png\" alt=\"rId47\" width=\"298\" height=\"176\"><br>कुल आयतो की संख्या = 6 + ABDC = 7</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "25",
                    section: "18",
                    question_en: "<p>25. Select the option that represents the correct order of the given words as they would appear in an English dictionary.<br>1. Consistence<br>2. Conscience<br>3. Conflagration<br>4. Consequence<br>5. Considerable<br>6. Consignment</p>",
                    question_hi: "<p>25. उस विकल्प का चयन कीजिए, जो दिए गए शब्दों के सही क्रम को निरूपित करता है, जैसे कि वे अँग्रेजी शब्दकोश में दिखाई देते हैं।<br>1. Consistence<br>2. Conscience<br>3. Conflagration<br>4. Consequence<br>5. Considerable<br>6. Consignment</p>",
                    options_en: [
                        "<p>3, 4, 2, 5,6, 1</p>",
                        "<p>4, 3, 2, 5, 6, 1</p>",
                        "<p>3, 2, 4,5,6,1</p>",
                        "<p>4, 2, 3, 5,1,6</p>"
                    ],
                    options_hi: [
                        "<p>3, 4, 2, 5,6, 1</p>",
                        "<p>4, 3, 2, 5, 6, 1</p>",
                        "<p>3, 2, 4,5,6,1</p>",
                        "<p>4, 2, 3, 5,1,6</p>"
                    ],
                    solution_en: "<p>25.(c) The correct order is :- <br>Conflagration(3) &rarr; Conscience(2) &rarr; Consequence(4) &rarr; Considerable(5) &rarr; Consignment(6) &rarr; Consistence(1)</p>",
                    solution_hi: "<p>25.(c) सही क्रम है:-<br>Conflagration(3) &rarr; Conscience(2) &rarr; Consequence(4) &rarr; Considerable(5) &rarr; Consignment(6) &rarr; Consistence(1)</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "26",
                    section: "18",
                    question_en: "<p>26. In which city of Maharashtra is Kalidasa Festival organised every year?</p>",
                    question_hi: "<p>26. महाराष्ट्र के किस शहर में हर वर्ष कालिदास महोत्सव का आयोजन किया जाता है?</p>",
                    options_en: [
                        "<p>Nasik</p>",
                        "<p>Nagpur</p>",
                        "<p>Pune</p>",
                        "<p>Mumbai</p>"
                    ],
                    options_hi: [
                        "<p>नासिक</p>",
                        "<p>नागपुर</p>",
                        "<p>पुणे</p>",
                        "<p>मुंबई</p>"
                    ],
                    solution_en: "<p>26.(b) <strong>Nagpur.</strong> Kalidas Festival is organized in the honor of great poet Kalidas in the month of November. Festivals of Maharashtra: Banganga Festival (Mumbai), Bhaubeej, Jiviti Puja, Pola Festival, Ganesh Chaturthi, Narali Poornima, and Vat Poornima.</p>",
                    solution_hi: "<p>26.(b) <strong>नागपुर।</strong> नवंबर माह में महान कवि कालिदास के सम्मान में कालिदास महोत्सव का आयोजन किया जाता है। महाराष्ट्र के त्यौहार: बाणगंगा महोत्सव (मुंबई), भाऊबीज, जीवती पूजा, पोला महोत्सव, गणेश चतुर्थी, नारली पूर्णिमा और वात पूर्णिमा।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "27",
                    section: "18",
                    question_en: "<p>27. The victory of the ________ marked the foundation stone of the Delhi Sultanate in India.</p>",
                    question_hi: "<p>27. ________ की जीत ने भारत में दिल्ली सल्तनत की आधारशिला रखी।</p>",
                    options_en: [
                        "<p>Afghans</p>",
                        "<p>Arabs</p>",
                        "<p>Persians</p>",
                        "<p>Turks</p>"
                    ],
                    options_hi: [
                        "<p>अफगानियों</p>",
                        "<p>अरबों</p>",
                        "<p>फ़ारसियों</p>",
                        "<p>तुर्कों</p>"
                    ],
                    solution_en: "<p>27.(d) <strong>Turks.</strong> The Delhi Sultanate, an Islamic empire based in Delhi, spanned much of the Indian subcontinent for 320 years from 1206 to 1526. It was founded by Qutb-ud-din Aibak in 1206, marking the beginning of the Mamluk Dynasty.</p>",
                    solution_hi: "<p>27.(d) <strong>तुर्कों।</strong> दिल्ली सल्तनत, दिल्ली में स्थित एक इस्लामी साम्राज्य, 1206 से 1526 तक 320 वर्षों तक भारतीय उपमहाद्वीप के अधिकांश हिस्से में फैला था। इसकी स्थापना कुतुब-उद-दीन ऐबक ने 1206 में मामलुक राजवंश की शुरुआत के रूप में की थी।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "28",
                    section: "18",
                    question_en: "<p>28. In which of the following places is the Losar Festival celebrated?</p>",
                    question_hi: "<p>28. निम्नलिखित में से किस स्थान पर लोसर महोत्सव मनाया जाता है?</p>",
                    options_en: [
                        "<p>Himachal Pradesh</p>",
                        "<p>Nagaland</p>",
                        "<p>Ladakh</p>",
                        "<p>Lakshadweep</p>"
                    ],
                    options_hi: [
                        "<p>हिमाचल प्रदेश</p>",
                        "<p>नागालैंड</p>",
                        "<p>लद्दाख</p>",
                        "<p>लक्षद्वीप</p>"
                    ],
                    solution_en: "<p>28.(c) <strong>Ladakh.</strong> It marks the beginning of the New Year for the Tibetan calendar. It is also celebrated in Arunachal Pradesh by the Memba tribe for five days. Other festivals of Ladakh - Hemis Tsechu, Sindhu Darshan Festival, Saka Dawa Festival, Phyang Tserup, etc. Arunachal pradesh - Siang River Festival, Ziro Festival, Solung Festival, Nyokum Festival, etc.</p>",
                    solution_hi: "<p>28.(c) <strong>लद्दाख।</strong> यह तिब्बती कैलेंडर के लिए नए साल की शुरुआत का प्रतीक है। इसे अरुणाचल प्रदेश में मेम्बा जनजाति द्वारा भी पांच दिनों तक मनाया जाता है। लद्दाख के अन्य त्योहार - हेमिस त्सेचु, सिंधु दर्शन महोत्सव, साका दावा महोत्सव, फ्यांग त्सेरुप, आदि। अरुणाचल प्रदेश - सियांग नदी महोत्सव, जीरो महोत्सव, सोलुंग महोत्सव, न्योकुम महोत्सव, आदि।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "29",
                    section: "18",
                    question_en: "<p>29. As of August, 2023, which of the following states has India&rsquo;s first fully functionally literate district?</p>",
                    question_hi: "<p>29. अगस्त 2023 तक, निम्नलिखित में से किस राज्य में भारत का पहला पूरी तरह कार्यात्मक रूप से साक्षर जिला (fully functionally literate district) है?</p>",
                    options_en: [
                        "<p>Madhya Pradesh</p>",
                        "<p>Kerala</p>",
                        "<p>Goa</p>",
                        "<p>Maharashtra</p>"
                    ],
                    options_hi: [
                        "<p>मध्य प्रदेश</p>",
                        "<p>केरल</p>",
                        "<p>गोवा</p>",
                        "<p>महाराष्ट्र</p>"
                    ],
                    solution_en: "<p>29.(a) <strong>Madhya Pradesh.</strong> <br>Tribal-dominated Mandla region has become the first fully &ldquo;functionally literate&rdquo; district in the country.</p>",
                    solution_hi: "<p>29.(a) <strong>मध्य प्रदेश।</strong> <br>आदिवासी बहुल&nbsp;मंडला जिला देश का पहला पूर्ण रूप से &ldquo;कार्यात्मक रूप से साक्षर&rdquo; जिला बन गया है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "30",
                    section: "18",
                    question_en: "<p>30. Which substance is fortified with Calcium, Vitamin D and Vitamin B12 ?</p>",
                    question_hi: "<p>30. कौन सा पदार्थ कैल्शियम, विटामिन D और विटामिन B12 से भरपूर है?</p>",
                    options_en: [
                        "<p>Eggs</p>",
                        "<p>Cow\'s milk</p>",
                        "<p>Dark green leafy vegetables</p>",
                        "<p>Soy milk</p>"
                    ],
                    options_hi: [
                        "<p>अंडे</p>",
                        "<p>गाय का दूध</p>",
                        "<p>गहरे हरे पत्ते वाली सब्जियाँ</p>",
                        "<p>सोया दूध</p>"
                    ],
                    solution_en: "<p>30.(d) <strong>Soy milk. </strong>Vitamin D helps our body to use calcium for bones and teeth. It is a Fat-soluble vitamin (A, D, E, and K). Food Sources - Cod liver oil, Egg yolk, Fortified cereals, Tuna fish. Deficiency disease - Rickets. Vitamin B12 (Scientific name - Cobalamin) is a water-soluble vitamin (C and B complex). Sources - Fish, Meat, Poultry, Eggs, and Dairy products. Deficiency disease - Pernicious anemia.</p>",
                    solution_hi: "<p>30.(d) <strong>सोया दूध। </strong>विटामिन D हमारे शरीर को हड्डियों और दांतों के लिए कैल्शियम का उपयोग करने में मदद करता है। वसा में घुलनशील विटामिन (A, D, E, और K) है। खाद्य स्रोत - कॉड लिवर तेल, अंडे की जर्दी, फोर्टिफाइड अनाज, टूना मछली। कमी से बीमारी - सूखा रोग। विटामिन B12 (वैज्ञानिक नाम - कोबालामिन) जल में घुलनशील विटामिन (C और B कॉम्प्लेक्स) है। स्रोत - मछली, मांस, पोल्ट्री, अंडे और डेयरी उत्पाद। कमी से बीमारी - घातक रक्ताल्पता।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "31",
                    section: "18",
                    question_en: "<p>31. Vilayat Khan is known for playing which of the following instruments ?</p>",
                    question_hi: "<p>31. विलायत खान निम्नलिखित में से किस वाद्य-यंत्र को बजाने के लिए जाने जाते हैं ?</p>",
                    options_en: [
                        " Santoor",
                        " Sarod ",
                        " Violin",
                        " Sitar"
                    ],
                    options_hi: [
                        " संतूर ",
                        " सरोद ",
                        " वायलिन ",
                        " सितार"
                    ],
                    solution_en: "<p>31.(d) <strong>Sitar</strong>. Famous Indian musicians with instruments: Sitar- Pt Ravi Shankar, Shahid Parvez Khan. Santoor - Pt Shiv Kumar Sharma, Bhajan Sopori. Sarod - Allauddin Khan, Ali Akbar Khan. Violin - MS Gopalakrishnan, Smt M Rajam.</p>",
                    solution_hi: "<p>31.(d) <strong>सितार।</strong> प्रसिद्ध भारतीय वाद्ययंत्र संगीतकार: सितार- पं. रविशंकर, शाहिद परवेज़ खान। संतूर - पं. शिव कुमार शर्मा, भजन सोपोरी। सरोद - अलाउद्दीन खान, अली अकबर खान। वायलिन - एम.एस. गोपालकृष्णन, श्रीमती एम राजम।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "32",
                    section: "18",
                    question_en: "<p>32. From which country is the power of judicial review taken in the Indian Constitution?</p>",
                    question_hi: "<p>32. भारतीय संविधान में न्यायिक समीक्षा की शक्ति किस देश से ली गई है?</p>",
                    options_en: [
                        "<p>United Kingdom</p>",
                        "<p>United States of America</p>",
                        "<p>France</p>",
                        "<p>Japan</p>"
                    ],
                    options_hi: [
                        "<p>यूनाइटेड किंगडम</p>",
                        "<p>संयुक्त राज्य अमेरिका</p>",
                        "<p>फ्रांस</p>",
                        "<p>जापान</p>"
                    ],
                    solution_en: "<p>32.(b) <strong>United States of America</strong>. Sources of Indian Constitution: USA- Fundamental Rights, Functions of president and vice-president, Impeachment of the president. UK- Legislative procedure, Single citizenship, Cabinet system, Bicameralism. France- Concept of &ldquo;Republic&rdquo;. Japan- Concept of &ldquo;procedure established by Law&rdquo;.</p>",
                    solution_hi: "<p>32.(b) <strong>संयुक्त राज्य अमेरिका।</strong> भारतीय संविधान के स्रोत: यूएसए - मौलिक अधिकार, राष्ट्रपति और उपराष्ट्रपति के कार्य, राष्ट्रपति पर महाभियोग। यूनाइटेड किंगडम - विधायी प्रक्रिया, एकल नागरिकता, कैबिनेट प्रणाली, द्विसदनीयता। फ़्रांस- \"गणतंत्र\" की अवधारणा। जापान- \"कानून द्वारा स्थापित प्रक्रिया\" की अवधारणा।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "33",
                    section: "18",
                    question_en: "<p>33. Which Indian cricketer has written the book &lsquo;The Test of My Life&rsquo;?</p>",
                    question_hi: "<p>33. किस भारतीय क्रिकेटर ने \'द टेस्ट ऑफ माई लाइफ\' नामक पुस्तक लिखी है ?</p>",
                    options_en: [
                        "<p>Yuvraj Singh</p>",
                        "<p>Ms Dhoni</p>",
                        "<p>Sachin Tendulkar</p>",
                        "<p>Kapil Dev</p>"
                    ],
                    options_hi: [
                        "<p>युवराज सिंह</p>",
                        "<p>एम.एस. धोनी</p>",
                        "<p>सचिन तेंदुलकर</p>",
                        "<p>कपिल देव</p>"
                    ],
                    solution_en: "<p>33.(a) <strong>Yuvraj Singh.</strong> His awards: Arjuna Award (2012), Padma Shri (2014). Autobiographies of cricketers: Sachin Tendulkar - &ldquo;Playing It My Way&rdquo;, Shane Warne - &ldquo;No Spin&rdquo;. Sunil Gavaskar - &ldquo;Sunny Days&rdquo;, Rickey ponting - &ldquo;Ponting: At the Close of Play&rdquo;, Sourav Ganguly - &ldquo;A Century Is Not Enough&rdquo;. Kapil Dev - \" The Straight from the Heart\".</p>",
                    solution_hi: "<p>33.(a) <strong>युवराज सिंह।</strong> उनके पुरस्कार: अर्जुन पुरस्कार (2012), पद्म श्री (2014)। क्रिकेटरों की आत्मकथाएँ: सचिन तेंदुलकर - \"प्लेइंग इट माई वे\", शेन वार्न - \"नो स्पिन\"। सुनील गावस्कर - \"सनी डेज़\", रिकी पोंटिंग - \"पोंटिंग: एट द क्लोज़ ऑफ़ प्ले\", सौरव गांगुली - \"ए सेंचुरी इज़ नॉट इनफ\"। कपिल देव - \"द स्ट्रेट फ्रॉम द हार्ट\"।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "34",
                    section: "18",
                    question_en: "<p>34. Choose the correct group of greenhouse gases.</p>",
                    question_hi: "<p>34. ग्रीनहाउस गैसों का सही समूह चुनें।</p>",
                    options_en: [
                        "<p>CH<sub>4</sub>, O<sub>2</sub>, CO<sub>2</sub>, N<sub>2</sub></p>",
                        "<p>CH<sub>4</sub>, O<sub>2</sub>, CO<sub>2</sub>, O<sub>3</sub></p>",
                        "<p>CH<sub>4</sub>, N<sub>2</sub>O, CO<sub>2</sub>, O<sub>3</sub></p>",
                        "<p>H<sub>2</sub>, O<sub>2</sub>, CO<sub>2</sub>, N<sub>2</sub></p>"
                    ],
                    options_hi: [
                        "<p>CH<sub>4</sub>, O<sub>2</sub>, CO<sub>2</sub>, N<sub>2</sub></p>",
                        "<p>CH<sub>4</sub>, O<sub>2</sub>, CO<sub>2</sub>, O<sub>3</sub></p>",
                        "<p>CH<sub>4</sub>, N<sub>2</sub>O, CO<sub>2</sub>, O<sub>3</sub></p>",
                        "<p>H<sub>2</sub>, O<sub>2</sub>, CO<sub>2</sub>, N<sub>2</sub></p>"
                    ],
                    solution_en: "<p>34.(c) <strong>CH<sub>4</sub>, N<sub>2</sub>O, CO<sub>2</sub>, O<sub>3</sub>.</strong> Greenhouse gases - It absorbs infrared radiation (net heat energy) emitted from Earth&rsquo;s surface and reradiates it back to Earth&rsquo;s surface. Kyoto Protocol - It operationalizes the United Nations Framework Convention on Climate Change by committing industrialized countries and economies in transition to limit and reduce greenhouse gases (GHG) emissions.</p>",
                    solution_hi: "<p>34.(c) <strong>CH<sub>4</sub>, N<sub>2</sub>O, CO<sub>2</sub>, O<sub>3 </sub>।</strong> ग्रीनहाउस गैसें - यह पृथ्वी की सतह से उत्सर्जित अवरक्त विकिरण (शुद्ध उष्मा ऊर्जा) को अवशोषित करती है और इसे वापस पृथ्वी की सतह पर ले जाती हैं। क्योटो प्रोटोकॉल - यह ग्रीनहाउस गैसों (GHG) उत्सर्जन को सीमित करने और कम करने के लिए औद्योगिक देशों और अर्थव्यवस्थाओं को प्रतिबद्ध करके जलवायु परिवर्तन पर संयुक्त राष्ट्र फ्रेमवर्क कन्वेंशन का संचालन करता है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "35",
                    section: "18",
                    question_en: "<p>35. When is Guru Nanak Jayanti celebrated in India as per the Hindu lunar calendar?</p>",
                    question_hi: "<p>35. हिंदू चंद्र कैलेंडर के अनुसार भारत में गुरु नानक जयंती कब मनाई जाती है?</p>",
                    options_en: [
                        "<p>Kartika Poornima</p>",
                        "<p>Paush Poornima</p>",
                        "<p>Paush Amavasya</p>",
                        "<p>Kartika Amavasya</p>"
                    ],
                    options_hi: [
                        "<p>कार्तिक पूर्णिमा</p>",
                        "<p>पौष पूर्णिमा</p>",
                        "<p>पौष अमावस्या</p>",
                        "<p>कार्तिक अमावस्या</p>"
                    ],
                    solution_en: "<p>35.(a) <strong>Kartika Poornima.</strong> Guru Nanak Jayanti (also known as Gurpurab) is sikh festival. It is observed as the birthday of the first guru, Guru Nanak. The founder of Sikhism was Guru Nanak. Famous sikh festivals and months: Maghi (Magh), Hola Mohalla (Chaitra), Lohri (Pausha), Sodal Mela (Bhadon).</p>",
                    solution_hi: "<p>35.(a) <strong>कार्तिक पूर्णिमा।</strong> गुरु नानक जयंती (जिसे गुरुपर्व के नाम से भी जाना जाता है) एक सिख त्योहार है। इसे प्रथम गुरु, गुरु नानक के जन्मदिन के रूप में मनाया जाता है। सिख धर्म के संस्थापक गुरु नानक थे। प्रसिद्ध सिख त्यौहार और महीने: माघी (माघ), होला मोहल्ला (चैत्र), लोहड़ी (पौष), सोडल मेला (भादों)।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "36",
                    section: "18",
                    question_en: "<p>36. Shree Hanuman Vyayam Prasarak Mandal is situated in _______.</p>",
                    question_hi: "<p>36. श्री हनुमान व्यायाम प्रसारक मंडल कहॉं स्थित है?</p>",
                    options_en: [
                        "<p>Nagpur</p>",
                        "<p>New Delhi</p>",
                        "<p>Amravati</p>",
                        "<p>Mumbai</p>"
                    ],
                    options_hi: [
                        "<p>नागपुर</p>",
                        "<p>नई दिल्ली</p>",
                        "<p>अमरावती</p>",
                        "<p>मुंबई</p>"
                    ],
                    solution_en: "<p>36.(c) <strong>Amravati.</strong> Shree H. V. P. Mandal, established in 1914 and is registered under Bombay Public Trust Act 1950 and Societies Registration Act 1860. It is a Voluntary, Social, Non-Political &amp; Secular Institute, managed with democratic principles &amp; practices.</p>",
                    solution_hi: "<p>36.(c) <strong>अमरावती।</strong> श्री एच. वी. पी. मंडल, 1914 में स्थापित और बॉम्बे पब्लिक ट्रस्ट अधिनियम 1950 और सोसायटी पंजीकरण अधिनियम 1860 के तहत पंजीकृत है। यह एक स्वैच्छिक, सामाजिक, गैर-राजनीतिक और धर्मनिरपेक्ष संस्थान है, जो लोकतांत्रिक सिद्धांतों और प्रथाओं के साथ प्रबंधित होता है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "37",
                    section: "18",
                    question_en: "<p>37. A _________ is one billionth of a metre.</p>",
                    question_hi: "<p>37. एक _________ एक मीटर का एक अरबवां भाग होता है।</p>",
                    options_en: [
                        "<p>parsec</p>",
                        "<p>micrometre</p>",
                        "<p>angstrom</p>",
                        "<p>nanometre</p>"
                    ],
                    options_hi: [
                        "<p>पारसेक</p>",
                        "<p>माइक्रोमीटर</p>",
                        "<p>एंगस्ट्रॉम</p>",
                        "<p>नैनोमीटर</p>"
                    ],
                    solution_en: "<p>37.(d) <strong>nanometre.</strong> In the International System of Units, the prefix \"nano\" means one-billionth, or 10<sup>-9</sup>. Special length units for short and large lengths are: 1 micrometre (&micro;m) = 10<sup>&ndash;6</sup> m; 1 fermi (f) = 10<sup>&ndash;15</sup> m; 1 angstrom ( &Aring;) = 10<sup>&ndash;10</sup> m; 1 astronomical unit (AU) (average distance of the Sun from the Earth) = 1.496 &times; 10<sup>11</sup> m; 1 light year (ly) = 9.46 &times; 10<sup>15</sup> m (distance that light travels with velocity of 3 &times; 108 m s<sup>&ndash;1</sup> in 1 year); 1 parsec = 3.08 &times; 10<sup>16</sup> m (Parsec is the distance at which average radius of earth&rsquo;s orbit subtends an angle of 1 arc second).</p>",
                    solution_hi: "<p>37.(d) <strong>नैनोमीटर।</strong> अंतर्राष्ट्रीय इकाई प्रणाली में, उपसर्ग \"नैनो\" का अर्थ एक-बिलियनवाँ या 10<sup>-9</sup> है। छोटी और बड़ी लंबाई के लिए विशेष लंबाई इकाइयाँ हैं: 1 माइक्रोमीटर (&micro;m) = 10<sup>&ndash;6</sup> मीटर; 1 फ़र्मी (f) = 10<sup>&ndash;15</sup> मीटर; 1 एंगस्ट्रॉम (&Aring;) = 10<sup>&ndash;10 </sup>मीटर; 1 खगोलीय इकाई (AU) (पृथ्वी से सूर्य की औसत दूरी) = 1.496 &times; 10<sup>11</sup> मीटर; 1 प्रकाश वर्ष (ly) = 9.46 &times; 10<sup>15</sup> मीटर (3 &times; 108 m s<sup>&ndash;1</sup> के वेग से प्रकाश द्वारा 1 वर्ष में तय की गई दूरी); 1 पारसेक = 3.08 &times; 10<sup>16</sup> मीटर (पारसेक वह दूरी है जिस पर पृथ्वी की कक्षा की औसत त्रिज्या 1 चाप सेकंड का कोण बनाती है)।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "38",
                    section: "18",
                    question_en: "<p>38. Swami Vivekananda established which of the following social organisations in 1897?</p>",
                    question_hi: "<p>38. स्वामी विवेकानन्द ने सन् 1897 में निम्नलिखित में से किस सामाजिक संगठन की स्थापना की थी?</p>",
                    options_en: [
                        "<p>Arya Samaj</p>",
                        "<p>Prarthana Samaj</p>",
                        "<p>Paramhansa Sabha</p>",
                        "<p>Ramakrishna Mission</p>"
                    ],
                    options_hi: [
                        "<p>आर्य समाज</p>",
                        "<p>प्रार्थना समाज</p>",
                        "<p>परमहंस सभा</p>",
                        "<p>रामकृष्ण मिशन</p>"
                    ],
                    solution_en: "<p>38.(d) <strong>Ramakrishna Mission</strong> - Named after Ramakrishna Paramhansa (Swami Vivekananda&rsquo;s guru). Aim: Salvation through social service and selfless action. Other Organizations for reform: The Brahmo Samaj (1828), The Prarthana Samaj (1867), The Veda Samaj (1864), and The Mohammedan Anglo-Oriental College (1875).</p>",
                    solution_hi: "<p>38.(d) <strong>रामकृष्ण मिशन</strong> - इसका नाम रामकृष्ण परमहंस (स्वामी विवेकानन्द के गुरु) के नाम पर रखा गया। उद्देश्य: समाज सेवा और निःस्वार्थ कार्य के माध्यम से मुक्ति। सुधार के लिए अन्य संगठन: ब्रह्म समाज (1828), प्रार्थना समाज (1867), वेद समाज (1864), और मोहम्मडन एंग्लो-ओरिएंटल कॉलेज (1875)।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "39",
                    section: "18",
                    question_en: "<p>39. Which of the following awards is given in the field of dance by the government of Madhya Pradesh?</p>",
                    question_hi: "<p>39. निम्न में से कौन-सा पुरस्कार मध्य प्रदेश सरकार द्वारा नृत्य के क्षेत्र में दिया जाता है?</p>",
                    options_en: [
                        "<p>Nandi Award for Best Choreographer</p>",
                        "<p>Kalidas Samman</p>",
                        "<p>Sanatan Sangeet Sanskriti</p>",
                        "<p>Tagore Ratna</p>"
                    ],
                    options_hi: [
                        "<p>सर्वश्रेष्ठ कोरियोग्राफर के लिए नंदी पुरस्कार</p>",
                        "<p>कालिदास सम्मान</p>",
                        "<p>सनातन संगीत संस्कृति</p>",
                        "<p>टैगोर रत्न</p>"
                    ],
                    solution_en: "<p>39.(b) <strong>Kalidas Samman</strong> was first awarded in 1980. First recipients: Semmangudi Srinivasa Iyer and Mallikarjun Mansur. Sanatan Sangeet Sanskriti award recognizes services rendered in music and dance. Nandi Awards (Andhra Pradesh government) recognise excellence in Telugu cinema, Telugu theater, and Telugu television.</p>",
                    solution_hi: "<p>39.(b) <strong>कालिदास सम्मान</strong> पहली बार 1980 में प्रदान किया गया था। पहले प्राप्तकर्ता: सेम्मनगुडी श्रीनिवास अय्यर और मल्लिकार्जुन मंसूर। सनातन संगीत संस्कृति पुरस्कार संगीत और नृत्य में प्रदान की गई सेवाओं को मान्यता देती है। नंदी पुरस्कार (आंध्र प्रदेश सरकार) तेलुगु सिनेमा, तेलुगु थिएटर और तेलुगु टेलीविजन में उत्कृष्टता को मान्यता देते हैं।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "40",
                    section: "18",
                    question_en: "<p>40. The height of the net in volleyball for women must be ________.</p>",
                    question_hi: "<p>40. महिला वॉलीबॉल में नेट की ऊंचाई कितनी होनी चाहिए?</p>",
                    options_en: [
                        "<p>2.24 m</p>",
                        "<p>2.15 m</p>",
                        "<p>2.10 m</p>",
                        "<p>2.43 m</p>"
                    ],
                    options_hi: [
                        "<p>2.24 m</p>",
                        "<p>2.15 m</p>",
                        "<p>2.10 m</p>",
                        "<p>2.43 m</p>"
                    ],
                    solution_en: "<p>40.(a) <strong>2.24 m.</strong> Volleyball net height for men: 2.43m. Net width: 1m. Court dimensions: 18m &times; 9m. Other sports: Football field: Maximum length - 120 m, maximum width - 90m, minimum length - 90 m, minimum width - 45 m. Hockey field dimension: 91.4 m &times; 55 m. Tennis field dimension: 23.77 m &times; 8.23 m (single) and 23.77 m &times; 10.9 m (doubles).</p>",
                    solution_hi: "<p>40.(a) <strong>2.24 m</strong>. पुरुषों के लिए वॉलीबॉल नेट की ऊंचाई: 2.43 m, नेट (जाल) की चौड़ाई: 1 m., कोर्ट आयाम: 18m &times; 9m, अन्य खेल: फुटबॉल मैदान: अधिकतम लंबाई - 120 m, अधिकतम चौड़ाई - 90m, न्यूनतम लंबाई - 90m, न्यूनतम चौड़ाई - 45m, हॉकी मैदान का आयाम: 91.4 m &times; 55 m, टेनिस मैदान का आयाम: 23.77m&nbsp;&times; 8.23 m (एकल) और 23.77 m &times; 10.9 m (युगल)।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "41",
                    section: "18",
                    question_en: "<p>41. Table sugar mostly contains</p>",
                    question_hi: "<p>41. टेबल शुगर (Table sugar) में अधिकतर क्या शामिल होता हैं?</p>",
                    options_en: [
                        "<p>maltose</p>",
                        "<p>sucrose</p>",
                        "<p>fructose</p>",
                        "<p>glucose</p>"
                    ],
                    options_hi: [
                        "<p>माल्टोज़</p>",
                        "<p>सुक्रोज़</p>",
                        "<p>फ्रक्टोज़</p>",
                        "<p>ग्लूकोज़</p>"
                    ],
                    solution_en: "<p>41.(b) <strong>sucrose.</strong> Natural disaccharide found in fruits, honey, and vegetables; composed of glucose and fructose. Maltose - Disaccharide of two glucose molecules. Fructose - Ketohexose obtained from the hydrolysis of sucrose, along with glucose. Glucose - A component of sucrose, forming the disaccharide along with fructose.</p>",
                    solution_hi: "<p>41.(b) <strong>सुक्रोज़।</strong> फलों, शहद और सब्जियों में पाया जाने वाला प्राकृतिक डिसैकराइड; ग्लूकोज और फ्रुक्टोज से बना है। माल्टोज़ - दो ग्लूकोज अणुओं का डिसैकराइड। फ्रुक्टोज - ग्लूकोज के साथ सुक्रोज के हाइड्रोलिसिस से प्राप्त कीटोहेक्सोज। ग्लूकोज - सुक्रोज का एक घटक, जो फ्रुक्टोज के साथ मिलकर डिसैकराइड बनाता है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "42",
                    section: "18",
                    question_en: "<p>42. The first edition of Indian Women\'s Premier League (cricket) was held in the year ________ .</p>",
                    question_hi: "<p>42. भारतीय महिला प्रीमियर लीग (क्रिकेट) का पहला संस्करण वर्ष ________ में आयोजित किया गया था।</p>",
                    options_en: [
                        "<p>2022</p>",
                        "<p>2023</p>",
                        "<p>2000</p>",
                        "<p>2021</p>"
                    ],
                    options_hi: [
                        "<p>2022</p>",
                        "<p>2023</p>",
                        "<p>2000</p>",
                        "<p>2021</p>"
                    ],
                    solution_en: "<p>42.(b) <strong>2023.</strong> The Women\'s Premier League (WPL), also known as the TATA WPL for sponsorship reasons, is a women\'s Twenty20 cricket franchise league in India, owned and operated by the Board of Control for Cricket in India (BCCI). Mumbai Indians won the first season, led by Harmanpreet Kaur. The 2024 Women\'s Premier League: Winner - Royal Challengers Bangalore.</p>",
                    solution_hi: "<p>42.(b) <strong>2023.</strong> महिला प्रीमियर लीग (WPL), जिसे प्रायोजन कारणों से टाटा WPL के नाम से भी जाना जाता है, भारत में महिलाओं की ट्वेंटी-20 क्रिकेट फ़्रैंचाइज़ी लीग है, जिसका स्वामित्व और संचालन भारतीय क्रिकेट कंट्रोल बोर्ड (BCCI) के पास है। मुंबई इंडियंस ने पहला सीजन हरमनप्रीत कौर के नेतृत्व में जीता। 2024 महिला प्रीमियर लीग: विजेता - रॉयल चैलेंजर्स बैंगलोर।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "43",
                    section: "18",
                    question_en: "<p>43. In India, \"APEDA\" is related to which of the following?</p>",
                    question_hi: "<p>43. भारत में, \"APEDA\" का संबंध निम्नलिखित में से किससे है?</p>",
                    options_en: [
                        "<p>Arms and ammunition</p>",
                        "<p>Agriculture</p>",
                        "<p>IT Services</p>",
                        "<p>Highways</p>"
                    ],
                    options_hi: [
                        "<p>हथियार और गोला बारूद</p>",
                        "<p>कृषि</p>",
                        "<p>सूचना प्रौद्योगिकी सेवाएं</p>",
                        "<p>राजमार्ग</p>"
                    ],
                    solution_en: "<p>43.(b) <strong>Agriculture.</strong> The Agricultural and Processed Food Products Export Development Authority (APEDA) was established by the Government of India under the Agricultural and Processed Food Products Export Development Authority Act passed by the Parliament in December, 1985. The act came into effect on February 13, 1986.</p>",
                    solution_hi: "<p>43.(b) <strong>कृषि।</strong> कृषि और प्रसंस्कृत खाद्य उत्पाद निर्यात विकास प्राधिकरण (APEDA) की स्थापना भारत सरकार द्वारा दिसंबर, 1985 में संसद द्वारा पारित कृषि और प्रसंस्कृत खाद्य उत्पाद निर्यात विकास प्राधिकरण अधिनियम के तहत की गई थी। यह अधिनियम 13 फरवरी 1986 को लागू हुआ।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "44",
                    section: "18",
                    question_en: "<p>44. Which Five-Year Plan was formulated by governments of two political parties?</p>",
                    question_hi: "<p>44. निम्नलिखित में से कौन-सी पंचवर्षीय योजना दो राजनीतिक दलों की सरकारों द्वारा तैयार की गई थी?</p>",
                    options_en: [
                        "<p>Eighth Five-Year Plan</p>",
                        "<p>Sixth Five-Year Plan</p>",
                        "<p>Fifth Five-Year Plan</p>",
                        "<p>Ninth Five-Year Plan</p>"
                    ],
                    options_hi: [
                        "<p>आठवीं पंचवर्षीय योजना</p>",
                        "<p>छठी पंचवर्षीय योजना</p>",
                        "<p>पांचवीं पंचवर्षीय योजना</p>",
                        "<p>नौवीं पंचवर्षीय योजना</p>"
                    ],
                    solution_en: "<p>44.(b) <strong>Sixth Five-Year Plan</strong> (1980-1985): Led by Indira Gandhi. Targeted growth - 5.2%, Achieved - 5.7%. Fifth Five Year Plan (1974 -1978) : This plan was terminated in 1978 by the newly elected Moraji Desai government. Targeted growth - 4.8 %, Achieved - 4.4 %. Eighth Five Year Plan (1992- 1997): leadership by V. Narasimha Rao. Targeted growth - 5.6 %, Achieved - 6.8 %.</p>",
                    solution_hi: "<p>44.(b) <strong>छठी पंचवर्षीय योजना </strong>(1980-1985): इंदिरा गांधी के नेतृत्व में । लक्षित वृद्धि - 5.2%, हासिल - 5.7%। पांचवी पंचवर्षीय योजना (1974 -1978) : इस योजना को 1978 में नवनिर्वाचित मोराजी देसाई सरकार ने समाप्त कर दिया। लक्षित वृद्धि - 4.8%, हासिल - 4.4%। आठवीं पंचवर्षीय योजना (1992-1997): वी. नरसिम्हा राव का नेतृत्व। लक्षित वृद्धि - 5.6%, हासिल - 6.8%।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "45",
                    section: "18",
                    question_en: "<p>45. What kind of image does the eye lens form of the object on the retina?</p>",
                    question_hi: "<p>45. नेत्र लेंस किसी बिंब का रेटिना पर किस प्रकार का प्रतिबिंब बनाता है?</p>",
                    options_en: [
                        "<p>Inverted real image</p>",
                        "<p>No image</p>",
                        "<p>Real and opaque image</p>",
                        "<p>Inverted virtual image</p>"
                    ],
                    options_hi: [
                        "<p>उल्टा वास्तविक प्रतिबिंब</p>",
                        "<p>कोई प्रतिबिंब नहीं</p>",
                        "<p>वास्तविक और अपारदर्शी प्रतिबिंब</p>",
                        "<p>उल्टा आभासी प्रतिबिंब</p>"
                    ],
                    solution_en: "<p>45.(a) <strong>Inverted real image. </strong>The human eye lens acts like a convex lens, which means it converges light rays. The image formed by a convex lens on the retina is real because it can be projected onto a screen.</p>",
                    solution_hi: "<p>45.(a) <strong>उल्टा वास्तविक प्रतिबिंब ।</strong> मानव नेत्र का लेंस उत्तल लेंस की तरह कार्य करता है, जिसका अर्थ है कि यह प्रकाश किरणों को परिवर्तित करता है। उत्तल लेंस द्वारा रेटिना पर बनी प्रतिबिम्ब वास्तविक होती है क्योंकि इसे स्क्रीन पर प्रक्षेपित किया जा सकता है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "46",
                    section: "18",
                    question_en: "<p>46. What is the name of the earliest form of writing known in the Indian subcontinent?</p>",
                    question_hi: "<p>46. भारतीय उपमहाद्वीप में ज्ञात, लेखन की सबसे प्रारंभिक शैली का क्या नाम है?</p>",
                    options_en: [
                        "<p>Pali Script</p>",
                        "<p>Indus Script</p>",
                        "<p>Brahmi Script</p>",
                        "<p>Sanskrit Script</p>"
                    ],
                    options_hi: [
                        "<p>पाली लिपि</p>",
                        "<p>सिंधु लिपि</p>",
                        "<p>ब्राह्मी लिपि</p>",
                        "<p>संस्कृत लिपि</p>"
                    ],
                    solution_en: "<p>46.(b) <strong>Indus Script.</strong> It is also known as the Harappan script and the Indus Valley Script, is a corpus of symbols produced by the Indus Valley Civilisation. Sumerian cuneiform and Egyptian hieroglyphs are generally considered the earliest true writing systems.</p>",
                    solution_hi: "<p>46.(b) <strong>सिंधु लिपि।</strong> इसे हड़प्पा लिपि और सिंधु घाटी लिपि के नाम से भी जाना जाता है, सिंधु घाटी सभ्यता द्वारा निर्मित प्रतीकों का एक संग्रह है। सुमेरियन क्यूनिफॉर्म और मिस्र की चित्रलिपि को सामान्य तौर पर सबसे पुरानी वास्तविक लेखन प्रणाली माना जाता है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "47",
                    section: "18",
                    question_en: "<p>47. The Election Commission of India became a three-member body in 1989, again it was made into a single member body in the year _________ .</p>",
                    question_hi: "<p>47. भारत का चुनाव आयोग 1989 में एक तीन सदस्यीय निकाय बन गया, इसे फिर से वर्ष _________ में एकल सदस्यीय निकाय बनाया गया</p>",
                    options_en: [
                        "<p>1990</p>",
                        "<p>1993</p>",
                        "<p>1992</p>",
                        "<p>1991</p>"
                    ],
                    options_hi: [
                        "<p>1990</p>",
                        "<p>1993</p>",
                        "<p>1992</p>",
                        "<p>1991</p>"
                    ],
                    solution_en: "<p>47.(a) <strong>1990.</strong> Evolution of the Election Commission of India (ECI): Single-member body from 1950 to 15th October 1989. ECI became a three-member body from 16th October 1989 to 1st January 1990. ECI was a single-member body from 2nd January 1990 to 30th September 1993. ECI returned to a three-member Body from 1st October 1993 onwards.</p>",
                    solution_hi: "<p>47.(a) <strong>1990.</strong> भारतीय चुनाव आयोग (ECI): 1950 से 15 अक्टूबर 1989 तक एकल सदस्यीय निकाय। ECI 16 अक्टूबर 1989 से 1 जनवरी 1990 तक तीन सदस्यीय निकाय बन गया। ECI, 2 जनवरी 1990 से 30 सितंबर 1993 तक एक सदस्यीय निकाय था। 1 अक्टूबर 1993 से ECI तीन सदस्यीय निकाय फिर से बन गया ।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "48",
                    section: "18",
                    question_en: "<p>48. Which of the following is/are NOT a part of the financial sector?</p>",
                    question_hi: "<p>48. निम्नलिखित में से कौन सा/से वित्तीय क्षेत्र का हिस्सा नहीं है/हैं?</p>",
                    options_en: [
                        "<p>Department of Agriculture &amp; Farmers Welfare</p>",
                        "<p>Investment banks</p>",
                        "<p>Commercial banks</p>",
                        "<p>Stock exchange operations</p>"
                    ],
                    options_hi: [
                        "<p>कृषि एवं किसान कल्याण विभाग</p>",
                        "<p>निवेश बैंक</p>",
                        "<p>वाणिज्यिक बैंक</p>",
                        "<p>स्टॉक एक्सचेंज संचालन</p>"
                    ],
                    solution_en: "<p>48.(a) <strong>Department of Agriculture &amp; Farmers Welfare.</strong> Parts of Financial System - Financial Assets, Financial Intermediaries/Financial Institutions, Financial Markets, Financial Rates of Return, Financial Instruments, Financial Services, etc. The Indian financial system is broadly classified into two broad groups - Organized sector and Unorganized sector.</p>",
                    solution_hi: "<p>48.(a) ​​<strong>कृषि और किसान कल्याण विभाग। वित्तीय प्रणाली के भाग</strong> - वित्तीय परिसंपत्तियाँ, वित्तीय मध्यस्थ/वित्तीय संस्थान, वित्तीय बाजार, रिटर्न की वित्तीय दरें, वित्तीय उपकरण, वित्तीय सेवाएँ, आदि। भारतीय वित्तीय प्रणाली को सामान्यतः दो व्यापक समूहों में वर्गीकृत किया गया है - संगठित क्षेत्र और असंगठित क्षेत्र।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "49",
                    section: "18",
                    question_en: "<p>49. Which mountain range includes the Shiwalik range as a part?</p>",
                    question_hi: "<p>49. शिवालिक पर्वत माला, निम्निलिखित में से कौन-सी पर्वत माला का भाग है?</p>",
                    options_en: [
                        "<p>Western Ghats</p>",
                        "<p>Eastern Ghats</p>",
                        "<p>Himalayas</p>",
                        "<p>Aravalli</p>"
                    ],
                    options_hi: [
                        "<p>पश्चिमी घाट</p>",
                        "<p>पूर्वी घाट</p>",
                        "<p>हिमालय</p>",
                        "<p>अरावली</p>"
                    ],
                    solution_en: "<p>49.(c) <strong>Himalayas.</strong> The Himalayan mountains are divided into three main parallel ranges: Himadri (Great Himalayas or Inner Himalayas), Himachal (Lesser Himalayas), and Shiwaliks (Outer Himalayas). Western Ghats (Sahyadri): They are a biodiversity hotspot and traverse the States of Kerala, Tamil Nadu, Karnataka, Goa, Maharashtra and Gujarat. Eastern Ghat is the discontinuous mountain range that passes through the states of Odisha, Andhra Pradesh, Telangana, Karnataka and Tamil Nadu.</p>",
                    solution_hi: "<p>49.(c) <strong>हिमालय</strong> । हिमालय पर्वत को तीन मुख्य समानांतर श्रेणियों में विभाजित किया गया है : हिमाद्रि (महान हिमालय या आंतरिक हिमालय), हिमाचल (लघु हिमालय), और शिवालिक (बाहरी हिमालय)। पश्चिमी घाट (सह्याद्रि): वे एक जैव विविधता हॉटस्पॉट हैं और केरल, तमिलनाडु, कर्नाटक, गोवा, महाराष्ट्र और गुजरात राज्यों से होकर गुजरते हैं। पूर्वी घाट एक असंतुलित पर्वत श्रृंखला है जो ओडिशा, आंध्र प्रदेश, तेलंगाना, कर्नाटक और तमिलनाडु राज्यों से होकर गुजरती है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "50",
                    section: "17",
                    question_en: "<p>50. Which of the following structures stores carbohydrates?</p>",
                    question_hi: "<p>50. निम्नलिखित में से कौन-सी संरचना कार्बोहाइड्रेट का संग्रहण करती है?</p>",
                    options_en: [
                        "<p>Aleuroplast</p>",
                        "<p>Elaioplast</p>",
                        "<p>Amyloplast</p>",
                        "<p>Chloroplast</p>"
                    ],
                    options_hi: [
                        "<p>एल्यूरोप्लास्ट</p>",
                        "<p>इलायोप्लास्ट</p>",
                        "<p>एमाइलोप्लास्ट</p>",
                        "<p>क्लोरोप्लास्ट</p>"
                    ],
                    solution_en: "<p>50.(c) <strong>Amyloplast.</strong> Plastids store carbohydrates as starch. Aleuroplast: Leucoplasts storing proteins. Elaioplast: Leucoplasts storing lipids, fats, and oils. Chloroplast: Organelles in plants and algae cells, responsible for photosynthesis.</p>",
                    solution_hi: "<p>50.(c) <strong>एमाइलोप्लास्ट।</strong> प्लास्टिड्स कार्बोहाइड्रेट को स्टार्च के रूप में संग्रहित करते हैं। एल्यूरोप्लास्ट: ल्यूकोप्लास्ट प्रोटीन का भंडारण करते हैं। इलायोंप्लास्ट: ल्यूकोप्लास्ट लिपिड, वसा और तेल का भंडारण करते हैं। क्लोरोप्लास्ट: पौधों और शैवाल कोशिकाओं में अंगक, प्रकाश संश्लेषण के लिए जिम्मेदार। </p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "51",
                    section: "17",
                    question_en: "<p>51. The distance between the centres of two circles is 81 cm, and their radii are 42 cm and&nbsp;51 cm. What is the length (in cm) of the direct common tangent to the circles?</p>",
                    question_hi: "<p>51. दो वृत्तों के केन्द्रों के बीच की दूरी 81 cm है, और उनकी त्रिज्या 42 cm और 51 cm हैं। वृत्तों की सीधी&nbsp;उभयनिष्ठ स्पर्श रेखा की लंबाई (cm में) क्या है?</p>",
                    options_en: [
                        "<p><math display=\"inline\"><mn>36</mn><msqrt><mn>5</mn></msqrt></math></p>",
                        "<p>51 <math display=\"inline\"><msqrt><mn>6</mn></msqrt></math></p>",
                        "<p>13 <math display=\"inline\"><msqrt><mn>3</mn></msqrt></math></p>",
                        "<p>40<math display=\"inline\"><msqrt><mn>2</mn></msqrt></math></p>"
                    ],
                    options_hi: [
                        "<p><math display=\"inline\"><mn>36</mn><msqrt><mn>5</mn></msqrt></math></p>",
                        "<p>51<math display=\"inline\"><msqrt><mn>6</mn></msqrt></math></p>",
                        "<p>13<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math></p>",
                        "<p>40<math display=\"inline\"><msqrt><mn>2</mn></msqrt></math></p>"
                    ],
                    solution_en: "<p>51.(a) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1738991574164.png\" alt=\"rId48\" width=\"256\" height=\"152\"><br>Length of direct common tangent (PQ) = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mrow><mo>(</mo><mi>AB</mi><mo>)</mo></mrow><mn>2</mn></msup><mi mathvariant=\"normal\">&#160;</mi><mo>-</mo><msup><mrow><mo>(</mo><mi>BQ</mi><mi mathvariant=\"normal\">&#160;</mi><mo>-</mo><mi mathvariant=\"normal\">&#160;</mi><mi>AP</mi><mo>)</mo></mrow><mn>2</mn></msup></msqrt></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mrow><mo>(</mo><mn>81</mn><mo>)</mo></mrow><mn>2</mn></msup><mo>-</mo><msup><mrow><mo>(</mo><mn>9</mn><mo>)</mo></mrow><mn>2</mn></msup></msqrt></math><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mo>(</mo><mn>81</mn><mo>+</mo><mn>9</mn><mo>)</mo><mo>(</mo><mn>81</mn><mo>-</mo><mn>9</mn><mo>)</mo></msqrt></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mo>(</mo><mn>90</mn><mo>)</mo><mo>&#215;</mo><mo>(</mo><mn>72</mn><mo>)</mo><mo>&#160;</mo></msqrt></math>=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mo>(</mo><mn>18</mn><mo>&#215;</mo><mn>5</mn><mo>)</mo><mo>&#215;</mo><mo>(</mo><mn>18</mn><mo>&#215;</mo><mn>4</mn><mo>)</mo></msqrt></math> = 18 &times; 2&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>5</mn></msqrt></math> = 36<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>5</mn></msqrt></math> cm</p>",
                    solution_hi: "<p>51.(a) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1738991574164.png\" alt=\"rId48\" width=\"256\" height=\"152\"><br>सीधी उभयनिष्ठ स्पर्शरेखा की लंबाई (PQ) = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mrow><mo>(</mo><mi>AB</mi><mo>)</mo></mrow><mn>2</mn></msup><mi mathvariant=\"normal\">&#160;</mi><mo>-</mo><msup><mrow><mo>(</mo><mi>BQ</mi><mi mathvariant=\"normal\">&#160;</mi><mo>-</mo><mi mathvariant=\"normal\">&#160;</mi><mi>AP</mi><mo>)</mo></mrow><mn>2</mn></msup></msqrt></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mrow><mo>(</mo><mn>81</mn><mo>)</mo></mrow><mn>2</mn></msup><mo>-</mo><msup><mrow><mo>(</mo><mn>9</mn><mo>)</mo></mrow><mn>2</mn></msup></msqrt></math><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mo>(</mo><mn>81</mn><mo>+</mo><mn>9</mn><mo>)</mo><mo>(</mo><mn>81</mn><mo>-</mo><mn>9</mn><mo>)</mo></msqrt></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mo>(</mo><mn>90</mn><mo>)</mo><mo>&#215;</mo><mo>(</mo><mn>72</mn><mo>)</mo><mo>&#160;</mo></msqrt></math>=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mo>(</mo><mn>18</mn><mo>&#215;</mo><mn>5</mn><mo>)</mo><mo>&#215;</mo><mo>(</mo><mn>18</mn><mo>&#215;</mo><mn>4</mn><mo>)</mo></msqrt></math> = 18 &times; 2&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>5</mn></msqrt></math> = 36<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>5</mn></msqrt></math>&nbsp;सेमी</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "52",
                    section: "17",
                    question_en: "52. The average weight of 35 students in a class is 40 kgs. Five of the students of this class whose average weight is 42 kgs left the class. Find the average weight of the remaining 30 students.",
                    question_hi: "52. एक कक्षा के 35 विद्यार्थियों का औसत भार 40 kg है। इस कक्षा के पांच विद्यार्थी जिनका औसत भार 42 kg है, कक्षा छोड़ देते हैं। शेष 30 विद्यार्थियों का औसत भार ज्ञात कीजिए।",
                    options_en: [
                        " 39 <math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math>kgs",
                        " 29  <math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math>kgs",
                        " 49 <math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math>kgs",
                        " 19 <math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math>kgs"
                    ],
                    options_hi: [
                        " 39 <math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> kgs",
                        " 29 <math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> kgs",
                        " 49 <math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> kgs",
                        " 19 <math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> kgs"
                    ],
                    solution_en: "<p>52.(a) <br>Average weight of remaining 30 student = 40 - 5 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mrow><mo>(</mo><mn>42</mn><mo>-</mo><mn>40</mn><mo>)</mo></mrow><mrow><mn>35</mn><mo>-</mo><mn>5</mn></mrow></mfrac></mstyle></math>&nbsp;= 40 - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math> = 39 <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>3</mn></mfrac></math>kgs</p>",
                    solution_hi: "<p>52.(a) <br>शेष 30 छात्रों का औसत वजन = 40 - 5 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mrow><mo>(</mo><mn>42</mn><mo>-</mo><mn>40</mn><mo>)</mo></mrow><mrow><mn>35</mn><mo>-</mo><mn>5</mn></mrow></mfrac></mstyle></math>&nbsp;= 40 - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math> = 39 <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>3</mn></mfrac></math>kgs</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "53",
                    section: "17",
                    question_en: "<p>53. Find the length of the sides of a triangle, if its angles are in the ratio 2 : 4 : 6 and its circumradius is 12 cm.</p>",
                    question_hi: "<p>53. उस त्रिभुज की भुजाओं की लंबाई ज्ञात करें, जिसके कोणों का अनुपात 2 : 4 : 6 है और जिसकी परित्रिज्या 12 cm है।</p>",
                    options_en: [
                        "<p>6 cm, 6<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math> cm, 12 cm</p>",
                        "<p>5 cm, 6 cm, 10 cm</p>",
                        "<p>15 cm, 15<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math> cm, 30 cm</p>",
                        "<p>12 cm, 12<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math> cm, 24 cm</p>"
                    ],
                    options_hi: [
                        "<p>6cm, 6<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math> cm, 12 cm</p>",
                        "<p>5 cm, 6 cm, 10 cm</p>",
                        "<p>15 cm, 15<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math> cm, 30 cm</p>",
                        "<p>12 cm, 12<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math> cm, 24 cm</p>"
                    ],
                    solution_en: "<p>53.(d) <br>Let angles of the triangles be 2x&nbsp;, 4x and 6x <br>2x&nbsp;+ 4x + 6x = 180&deg;<br>12x&nbsp;= 180&deg; &rArr; x = 15&deg;<br>Angles of triangle = 30&deg; , 60&deg; , 90&deg;<br>Ratio of sides = 1 : <math display=\"inline\"><msqrt><mn>3</mn></msqrt></math> : 2<br>Since, one angle is 90&deg; so, triangle is right angled <br>Now, circumradius of right angle triangle = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>hypotenuse</mi><mn>2</mn></mfrac></math> <br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>hypotenuse</mi><mn>2</mn></mfrac></math> = 12 &rArr; hypotenuse = 24 cm<br>Now, 2 unit = 24 cm , 1 unit = 12 cm<br>Other sides be 12 cm, 12<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math> cm<br>Hence, sides are 12 cm, 12<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math> cm, 24 cm</p>",
                    solution_hi: "<p>53.(d) <br>माना त्रिभुज के कोण 2x, 4x और 6x हैं <br>2x&nbsp;+ 4x + 6x = 180&deg;<br>12x&nbsp;= 180&deg; &rArr; x = 15&deg;<br>त्रिभुज के कोण = 30&deg;, 60&deg;, 90&deg;<br>भुजाओं का अनुपात = 1 : <math display=\"inline\"><msqrt><mn>3</mn></msqrt></math> : 2<br>चूँकि, एक कोण 90&deg; का है, इसलिए त्रिभुज समकोण है <br>अब, समकोण त्रिभुज की परित्रिज्या = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2325;&#2352;&#2381;&#2339;</mi><mo>&#160;</mo></mrow><mn>2</mn></mfrac></math><br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2325;&#2352;&#2381;&#2339;</mi><mo>&#160;</mo></mrow><mn>2</mn></mfrac></math> = 12 &rArr; कर्ण = 24 सेमी<br>अब, 2 इकाई = 24 सेमी <math display=\"inline\"><mo>&#8658;</mo></math>1 इकाई = 12 सेमी<br>अन्य भुजाएँ = 12 सेमी, 12<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math> सेमी<br>अत: भुजाएँ 12 सेमी, 12<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math> सेमी, 24 सेमी हैं</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "54",
                    section: "17",
                    question_en: "<p>54. The given table represents the percentage marks of three students in three subjects. Study the table and answer the question.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1738991574308.png\" alt=\"rId49\" width=\"286\" height=\"99\"> <br>The average marks obtain by Sohan in all three subject is equal to:</p>",
                    question_hi: "<p>54. दी गई तालिका तीन विषयों में तीन छात्रों के प्रतिशत अंकों को दर्शाती है। तालिका का अध्ययन कीजिए और प्रश्न का उत्तर दीजिए। <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1738991574490.png\" alt=\"rId50\" width=\"204\" height=\"106\"> <br>सोहन द्वारा सभी तीनों विषयों में प्राप्त किए गए औसत अंक _______ के बराबर हैं।</p>",
                    options_en: [
                        "<p>82</p>",
                        "<p>84</p>",
                        "<p>78</p>",
                        "<p>80</p>"
                    ],
                    options_hi: [
                        "<p>82</p>",
                        "<p>84</p>",
                        "<p>78</p>",
                        "<p>80</p>"
                    ],
                    solution_en: "<p>54.(a) <br>Average = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>84</mn><mo>+</mo><mn>80</mn><mo>+</mo><mn>82</mn></mrow><mn>3</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>246</mn><mn>3</mn></mfrac></math> = 82</p>",
                    solution_hi: "<p>54.(a) <br>औसत = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>84</mn><mo>+</mo><mn>80</mn><mo>+</mo><mn>82</mn></mrow><mn>3</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>246</mn><mn>3</mn></mfrac></math> = 82</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "55",
                    section: "17",
                    question_en: "<p>55. The average of 25 integers is zero. What is the maximum possible number of positive integers?</p>",
                    question_hi: "<p>55. 25 पूर्णांकों का औसत शून्य है। धनात्मक पूर्णांकों की अधिकतम संभावित संख्या कितनी होगी ?</p>",
                    options_en: [
                        "<p>12</p>",
                        "<p>20</p>",
                        "<p>13</p>",
                        "<p>24</p>"
                    ],
                    options_hi: [
                        "<p>12</p>",
                        "<p>20</p>",
                        "<p>13</p>",
                        "<p>24</p>"
                    ],
                    solution_en: "<p>55.(d)<br>Average of 1 negative number = average of 24 positive numbers<br>{Because here maximum (+ive) integers are asked whereas the average of 25 numbers is zero.}<br>So the maximum positive integers will be 24</p>",
                    solution_hi: "<p>55.(d)<br>1 ऋणात्मक संख्या का औसत = 24 धनात्मक संख्याओं का औसत<br>(क्योंकि यहां अधिकतम धनात्मक पूर्णांक पूछे गए है जबकि 25 संख्याओ का औसत शून्य है।)<br>अतः अधिकतम धनात्मक पूर्णांक 24 होंगे</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "56",
                    section: "17",
                    question_en: "<p>56. The following table shows the expenditure of a company (in ₹ lakh) per annum over the given years. Study the table and answer the question that follows.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1738991574633.png\" alt=\"rId51\" width=\"416\" height=\"115\"> <br>The total amount of bonus paid by the company during the given period is what percentage of the total amount of fuels and transport paid during this period (correct to two places of decimals)?</p>",
                    question_hi: "<p>56. निम्नलिखित तालिका में, दिए गए वर्षों में एक कंपनी का वार्षिक व्यय (₹ लाख में) दर्शाया गया है। तालिका का अध्ययन कीजिए और नीचे दिए गए प्रश्न का उत्तर दीजिए। <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1738991574727.png\" alt=\"rId52\" width=\"389\" height=\"129\"> <br>दी गई अवधि के दौरान कंपनी द्वारा भुगतान की गई बोनस की कुल राशि, इस अवधि के दौरान ईंधन और परिवहन पर भुगतान की गई कुल राशि का कितने प्रतिशत है (दो दशमलव स्थान तक पूर्णांकित)?</p>",
                    options_en: [
                        "<p>3.92%</p>",
                        "<p>2.39%</p>",
                        "<p>3.29%</p>",
                        "<p>2.93%</p>"
                    ],
                    options_hi: [
                        "<p>3.92%</p>",
                        "<p>2.39%</p>",
                        "<p>3.29%</p>",
                        "<p>2.93%</p>"
                    ],
                    solution_en: "<p>56.(d)<br>Total bonus = 3 + 4.25 + 4.50 + 4.85 + 4.90 = 21.5 lakh<br>Total amount of fuel and transport = 110 + 145 + 120 + 168 + 190 = 733 lakh<br>Required % = <math display=\"inline\"><mfrac><mrow><mn>21</mn><mo>.</mo><mn>5</mn></mrow><mrow><mn>733</mn></mrow></mfrac></math> &times; 100 = 2.93%</p>",
                    solution_hi: "<p>56.(d)<br>कुल बोनस = 3 + 4.25 + 4.50 + 4.85 + 4.90 = 21.5 लाख<br>ईंधन और परिवहन की कुल राशि = 110 + 145 + 120 + 168 + 190 = 733 लाख<br>आवश्यक % = <math display=\"inline\"><mfrac><mrow><mn>21</mn><mo>.</mo><mn>5</mn></mrow><mrow><mn>733</mn></mrow></mfrac></math> &times; 100 = 2.93%</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "57",
                    section: "17",
                    question_en: "<p>57. If 4sin&sup2; &theta; = 3(1 + cos&theta;), 0&deg; &lt; &theta; &lt; 90&deg;, then what is the value of <math display=\"inline\"><msqrt><mn>15</mn></msqrt></math> tan&theta; + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><msqrt><mn>15</mn></msqrt></mfrac></math> sin&theta; + 2sec&theta;?</p>",
                    question_hi: "<p>57. यदि 4sin&sup2; &theta; = 3(1 + cos&theta;), 0&deg; &lt; &theta; &lt; 90&deg; है, तो <math display=\"inline\"><msqrt><mn>15</mn></msqrt></math>tan&theta; + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><msqrt><mn>15</mn></msqrt></mfrac></math> sin&theta; + 2sec&theta; का मान क्या है ?</p>",
                    options_en: [
                        "<p>24</p>",
                        "<p>4<math display=\"inline\"><msqrt><mn>15</mn></msqrt></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>8</mn></mrow><mrow><msqrt><mn>15</mn></msqrt></mrow></mfrac></math></p>",
                        "<p>8</p>"
                    ],
                    options_hi: [
                        "<p>24</p>",
                        "<p>4<math display=\"inline\"><msqrt><mn>15</mn></msqrt></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>8</mn></mrow><mrow><msqrt><mn>15</mn></msqrt></mrow></mfrac></math></p>",
                        "<p>8</p>"
                    ],
                    solution_en: "<p>57.(a)<br>4sin&sup2; &theta; = 3(1 + cos&theta;)<br>4(1 - cos&sup2; &theta;) = 3(1 + cos&theta;)<br>4 (1 - cos &theta;) (1 + cos &theta;) = 3(1 + cos&theta;)<br>(1 - cos &theta;) = <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math><br>cos &theta; = 1 - <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>4</mn></mfrac></math> = &nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>base</mi><mi>hypotenuse</mi></mfrac></math><br>Perpendicular = <math display=\"inline\"><mroot><mrow><mn>15</mn></mrow><mrow></mrow></mroot></math> <br>Now,<br><math display=\"inline\"><msqrt><mn>15</mn></msqrt></math>tan&theta; + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><msqrt><mn>15</mn></msqrt></mfrac></math> sin&theta; + 2sec&theta;<br><math display=\"inline\"><msqrt><mn>15</mn></msqrt></math> &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>Perpendicular</mi><mi>base</mi></mfrac></math> +<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><msqrt><mn>15</mn></msqrt></mfrac></math> &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>Perpendicular</mi><mi>hypotenuse</mi></mfrac></math> + 2 &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>hypotenuse</mi><mi>base</mi></mfrac></math><br><math display=\"inline\"><msqrt><mn>15</mn></msqrt></math> &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msqrt><mn>15</mn></msqrt><mn>1</mn></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><msqrt><mn>15</mn></msqrt></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msqrt><mn>15</mn></msqrt><mn>4</mn></mfrac></math> &times;&nbsp; + 2 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>1</mn></mfrac></math><br>15 + 1 + 8 = 24</p>",
                    solution_hi: "<p>57.(a)<br>4sin&sup2; &theta; = 3(1 + cos&theta;)<br>4(1 - cos&sup2; &theta;) = 3(1 + cos&theta;)<br>4 (1 - cos &theta;) (1 + cos &theta;) = 3(1 + cos&theta;)<br>(1 - cos &theta;) = <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math><br>cos &theta; = 1 - <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>4</mn></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>&#2310;&#2343;&#2366;&#2352;</mi><mrow><mo>&#160;</mo><mi>&#2325;&#2352;&#2381;&#2339;</mi><mo>&#160;</mo></mrow></mfrac></math><br>लम्ब = <math display=\"inline\"><mroot><mrow><mn>15</mn></mrow><mrow></mrow></mroot></math> <br>अब,<br><math display=\"inline\"><msqrt><mn>15</mn></msqrt></math>tan&theta; + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><msqrt><mn>15</mn></msqrt></mfrac></math> sin&theta; + 2sec&theta;<br><math display=\"inline\"><msqrt><mn>15</mn></msqrt></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&#160;</mo><mi>&#2354;&#2350;&#2381;&#2348;</mi><mo>&#160;</mo></mrow><mrow><mi>&#2310;&#2343;&#2366;&#2352;</mi><mo>&#160;</mo></mrow></mfrac></math>+ <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><msqrt><mn>15</mn></msqrt></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>&#2354;&#2350;&#2381;&#2348;</mi><mrow><mi>&#2325;&#2352;&#2381;&#2339;</mi><mo>&#160;</mo></mrow></mfrac></math> + 2 &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2325;&#2352;&#2381;&#2339;</mi><mo>&#160;</mo></mrow><mrow><mi>&#2310;&#2343;&#2366;&#2352;</mi><mo>&#160;</mo></mrow></mfrac></math><br><math display=\"inline\"><msqrt><mn>15</mn></msqrt></math> &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msqrt><mn>15</mn></msqrt><mn>1</mn></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><msqrt><mn>15</mn></msqrt></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msqrt><mn>15</mn></msqrt><mn>4</mn></mfrac></math> &times;&nbsp; + 2 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>1</mn></mfrac></math><br>15 + 1 + 8 = 24</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "58",
                    section: "17",
                    question_en: "<p>58. If &fnof;(p) = sin<sup>p+2</sup> x + cos<sup>p+2 </sup>x, then the value of 6&fnof;(2) - 4&fnof;(4) + 10&fnof;(0) is:</p>",
                    question_hi: "<p>58. यदि &fnof;(p) = sin<sup>p+2 </sup>x + cos<sup>p+2 </sup>x, है, तो 6&fnof;(2) - 4&fnof;(4) + 10&fnof;(0) का मान ज्ञात कीजिए।</p>",
                    options_en: [
                        "<p>12</p>",
                        "<p>10</p>",
                        "<p>11</p>",
                        "<p>14</p>"
                    ],
                    options_hi: [
                        "<p>12</p>",
                        "<p>10</p>",
                        "<p>11</p>",
                        "<p>14</p>"
                    ],
                    solution_en: "<p>58.(a)<br>Take x&nbsp;= 45&deg;<br><strong>6&fnof;(2)</strong> = 6 &times; ( sin<sup>p+2</sup> x + cos<sup>p+2 </sup>x) <br>= 6 &times; ( sin<sup>4</sup>x&nbsp;+ cos<sup>4</sup>x) <br>= 6 &times; ( <math display=\"inline\"><mo>(</mo><mfrac><mrow><mn>1</mn></mrow><mrow><msqrt><mn>2</mn></msqrt></mrow></mfrac><msup><mrow><mo>)</mo></mrow><mrow><mn>4</mn></mrow></msup></math> + <math display=\"inline\"><mo>(</mo><mfrac><mrow><mn>1</mn></mrow><mrow><msqrt><mn>2</mn></msqrt></mrow></mfrac><msup><mrow><mo>)</mo></mrow><mrow><mn>4</mn></mrow></msup></math>)<br>= 6 &times; ( <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> + <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math>) = 6 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math> = <strong>3</strong><br><strong>4&fnof;(4) </strong>= 4 &times; ( sin<sup>6</sup> x + cos<sup>6</sup>x) <br>= 4 &times; ( (sin<sup>2</sup> x)<sup>3</sup> + (cos<sup>2</sup> x)<sup>3</sup>)<br>= 4 &times; (<math display=\"inline\"><mo>(</mo><mfrac><mrow><mn>1</mn></mrow><mrow><msqrt><mn>2</mn></msqrt></mrow></mfrac><msup><mrow><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup></math>)<sup>3</sup> + (<math display=\"inline\"><mo>(</mo><mfrac><mrow><mn>1</mn></mrow><mrow><msqrt><mn>2</mn></msqrt></mrow></mfrac><msup><mrow><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup></math>)<sup>3</sup><br>= 4 &times; ( <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math> + <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math>) = 4 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>4</mn></mfrac></math> = <strong>1</strong><br><strong>10&fnof;(0) </strong>= 10 &times; ( sin<sup>2</sup> x + cos<sup>2</sup>x) <br>= 10 &times; (1) = <strong>10</strong><br>Hence, 6&fnof;(2) - 4&fnof;(4) + 10&fnof;(0) = 3 - 1 + 10 = 12</p>",
                    solution_hi: "<p>58.(a)<br>x = 45&deg; लेने पर <br><strong>6&fnof;(2)</strong> = 6 &times; ( sin<sup>p+2</sup> x + cos<sup>p+2 </sup>x) <br>= 6 &times; ( sin<sup>4</sup>x&nbsp;+ cos<sup>4</sup>x) <br>= 6 &times; ( <math display=\"inline\"><mo>(</mo><mfrac><mrow><mn>1</mn></mrow><mrow><msqrt><mn>2</mn></msqrt></mrow></mfrac><msup><mrow><mo>)</mo></mrow><mrow><mn>4</mn></mrow></msup></math> + <math display=\"inline\"><mo>(</mo><mfrac><mrow><mn>1</mn></mrow><mrow><msqrt><mn>2</mn></msqrt></mrow></mfrac><msup><mrow><mo>)</mo></mrow><mrow><mn>4</mn></mrow></msup></math>)<br>= 6 &times; ( <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> + <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math>) = 6 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math> = <strong>3</strong><br><strong>4&fnof;(4) </strong>= 4 &times; ( sin<sup>6</sup> x + cos<sup>6</sup>x) <br>= 4 &times; ( (sin<sup>2</sup> x)<sup>3</sup> + (cos<sup>2</sup> x)<sup>3</sup>)<br>= 4 &times; (<math display=\"inline\"><mo>(</mo><mfrac><mrow><mn>1</mn></mrow><mrow><msqrt><mn>2</mn></msqrt></mrow></mfrac><msup><mrow><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup></math>)<sup>3</sup> + (<math display=\"inline\"><mo>(</mo><mfrac><mrow><mn>1</mn></mrow><mrow><msqrt><mn>2</mn></msqrt></mrow></mfrac><msup><mrow><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup></math>)<sup>3</sup><br>= 4 &times; ( <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math> + <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math>) = 4 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>4</mn></mfrac></math> = <strong>1</strong><br><strong>10&fnof;(0) </strong>= 10 &times; ( sin<sup>2</sup> x + cos<sup>2</sup>x) <br>= 10 &times; (1) = <strong>10</strong><br>अतः, 6&fnof;(2) - 4&fnof;(4) + 10&fnof;(0) = 3 - 1 + 10 = 12</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "59",
                    section: "17",
                    question_en: "<p>59. A shopkeeper marks up the price of the oil by 40% and uses a faulty machine which measures 15% less. If the shopkeeper gives a discount of 32%, the profit/loss percentage is:</p>",
                    question_hi: "<p>59. एक दुकानदार तेल का मूल्य 40% अधिक अंकित करता है और एक खराब तराजू का उपयोग करता है जो 15% कम मापती है। यदि दुकानदार 32% की छूट देता है, तो उसका लाभ/हानि प्रतिशत ज्ञात कीजिए।</p>",
                    options_en: [
                        "<p>12% Profit</p>",
                        "<p>25% Profit</p>",
                        "<p>15% Loss</p>",
                        "<p>18% Loss</p>"
                    ],
                    options_hi: [
                        "<p>12% लाभ</p>",
                        "<p>25% लाभ</p>",
                        "<p>15% हानि</p>",
                        "<p>18% हानि</p>"
                    ],
                    solution_en: "<p>59.(a)<br>Ratio - CP : SP<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;100 : 140<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;850 : 1000<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;100 : 68<br>&mdash;--------------------------<br>Final - 850 : 14 &times; 68 = 50 : 56<br>profit% = <math display=\"inline\"><mfrac><mrow><mn>56</mn><mo>-</mo><mn>50</mn></mrow><mrow><mn>50</mn></mrow></mfrac></math> &times; 100 = 12</p>",
                    solution_hi: "<p>59.(a)<br>अनुपात - क्रय मूल्य : विक्रय मूल्य<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 100 : 140<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 850 : 1000<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 100 : 68<br>-------------------------------------<br>अंतिम -&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;850 : 14 &times; 68 = 50 : 56<br>लाभ% = <math display=\"inline\"><mfrac><mrow><mn>56</mn><mo>-</mo><mn>50</mn></mrow><mrow><mn>50</mn></mrow></mfrac></math> &times; 100 = 12</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "60",
                    section: "17",
                    question_en: "<p>60. The following table shows the total candidates appeared and number of candidates present, in different exam centres &ndash; P, Q and R. Study the table and answer the question that follows.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1738991574837.png\" alt=\"rId53\" width=\"325\" height=\"144\"> <br>&lsquo;Total&rsquo; denotes total candidates applied for the centre,&lsquo;Present&rsquo; denotes the candidates appeared. In which year was the number of absentees the second highest in total of all centres?</p>",
                    question_hi: "<p>60. निम्नलिखित तालिका विभिन्न परीक्षा केंद्रों P, Q और R में कुल उपस्थित उम्मीदवारों और उपस्थित उम्मीदवारों की संख्या को दर्शाती है। तालिका का अध्ययन कीजिए और निम्नलिखित प्रश्न का उत्तर दीजिए।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1738991574974.png\" alt=\"rId54\" width=\"295\" height=\"153\"> <br>\'कुल\' केंद्र के लिए आवेदन करने वाले कुल उम्मीदवारों को दर्शाता है, \'उपस्थित\' उपस्थित उम्मीदवारों को दर्शाता है।<br>किस वर्ष में सभी केन्द्रों में अनुपस्थित रहने वालों की संख्या दूसरी सबसे अधिक थी?</p>",
                    options_en: [
                        "<p>2018</p>",
                        "<p>2017</p>",
                        "<p>2020</p>",
                        "<p>2019</p>"
                    ],
                    options_hi: [
                        "<p>2018</p>",
                        "<p>2017</p>",
                        "<p>2020</p>",
                        "<p>2019</p>"
                    ],
                    solution_en: "<p>60.(d)<br>The number of absentees in 2018 = (64 + 65 + 65) - (60 + 45 + 55) = 34<br>The number of absentees in 2017 = (50 + 75 + 45) - (45 + 55 + 40) = 30 <br>The number of absentees in 2020 = (55 + 80 + 90) - (44 + 66 + 85) = 30<br>The number of absentees in 2019 = (80 + 84 + 70) - (69 + 72 + 62) = 31<br>It is clear from the above expression that in 2019 the number of absentees is the second highest in total of all centres.</p>",
                    solution_hi: "<p>60.(d)<br>2018 में अनुपस्थितों की संख्या = (64 + 65 + 65) - (60 + 45 + 55) = 34<br>2017 में अनुपस्थितों की संख्या = (50 + 75 + 45) - (45 + 55 + 40) = 30 <br>2020 में अनुपस्थितों की संख्या= (55 + 80 + 90) - (44 + 66 + 85) = 30<br>2019 में अनुपस्थितों की संख्या= (80 + 84 + 70) - (69 + 72 + 62) = 31<br>उपरोक्त अभिव्यक्ति से यह स्पष्ट है कि 2019 में अनुपस्थितों की संख्या सभी केंद्रों के कुल योग में दूसरी सबसे अधिक है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "61",
                    section: "17",
                    question_en: "<p>61. A tank is filled in 45 minutes by two pipes, A and B. Pipe B fills the tank twice as fast as A. How much time (in minutes) will pipe A alone take to fill the tank?</p>",
                    question_hi: "<p>61. दो पाइप A और B, एक टंकी को 45 मिनट में भरते हैं। पाइप B, पाइप A की तुलना में टंकी को दोगुना तेज भरता है। अकेले पाइप A को टंकी भरने में कितना समय (मिनट में) लगेगा?</p>",
                    options_en: [
                        "<p>135</p>",
                        "<p>140</p>",
                        "<p>125</p>",
                        "<p>115</p>"
                    ],
                    options_hi: [
                        "<p>135</p>",
                        "<p>140</p>",
                        "<p>125</p>",
                        "<p>115</p>"
                    ],
                    solution_en: "<p>61.(a)<br>Ratio <math display=\"inline\"><mo>&#8594;</mo></math>&nbsp; &nbsp; &nbsp; &nbsp; A : B <br>Efficiency <math display=\"inline\"><mo>&#8594;</mo></math> 1 : 2<br>Total work = 45 &times;&nbsp;3 = 135<br>Time taken by Pipe A alone = <math display=\"inline\"><mfrac><mrow><mn>135</mn></mrow><mrow><mn>1</mn></mrow></mfrac></math> = 135 minutes</p>",
                    solution_hi: "<p>61.(a)<br>अनुपात <math display=\"inline\"><mo>&#8594;</mo></math> A : B <br>दक्षता <math display=\"inline\"><mo>&#8594;</mo></math>&nbsp; &nbsp;1 : 2<br>कुल कार्य = 45 &times;&nbsp;3 = 135<br>अकेले पाइप A द्वारा लिया गया समय = <math display=\"inline\"><mfrac><mrow><mn>135</mn></mrow><mrow><mn>1</mn></mrow></mfrac></math> = 135 मिनट</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "62",
                    section: "17",
                    question_en: "<p>62. A solid metallic cube having surface area of 54 cm&sup2; is melted to form smaller cubes of surface area <math display=\"inline\"><mfrac><mrow><mn>8</mn></mrow><mrow><mn>12</mn></mrow></mfrac></math> cm&sup2; each. Find the number of smaller cubes.</p>",
                    question_hi: "<p>62. 54 cm&sup2; पृष्ठीय क्षेत्रफल वाले एक ठोस धातु के घन को पिघलाकर प्रत्येक <math display=\"inline\"><mfrac><mrow><mn>8</mn></mrow><mrow><mn>12</mn></mrow></mfrac></math>cm&sup2; पृष्ठीय क्षेत्रफल वाले छोटे-छोटे घन बनाए जाते हैं। छोटे घनों की संख्या ज्ञात कीजिए।</p>",
                    options_en: [
                        "<p>512</p>",
                        "<p>343</p>",
                        "<p>216</p>",
                        "<p>729</p>"
                    ],
                    options_hi: [
                        "<p>512</p>",
                        "<p>343</p>",
                        "<p>216</p>",
                        "<p>729</p>"
                    ],
                    solution_en: "<p>62.(d) <br>Let the no. of smaller cube be n<br>Surface area of larger cube = 6<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><msub><mi mathvariant=\"normal\">a</mi><mn>1</mn></msub><mn>2</mn></msup></math> = 54<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><msub><mi mathvariant=\"normal\">a</mi><mn>1</mn></msub><mn>2</mn></msup></math> = 9 &rArr; a<sub>1</sub> = 3 cm<br>Surface area of smaller cube = 6<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><msub><mi mathvariant=\"normal\">a</mi><mn>2</mn></msub><mn>2</mn></msup></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>8</mn><mn>12</mn></mfrac></math><br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><msub><mi mathvariant=\"normal\">a</mi><mn>2</mn></msub><mn>2</mn></msup></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>8</mn><mrow><mn>12</mn><mo>&#215;</mo><mn>6</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>9</mn></mfrac></math> &rArr; a<sub>2</sub> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math><br>Volume of larger cube = n &times; volume of smaller cube<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><msub><mi mathvariant=\"normal\">a</mi><mn>1</mn></msub><mn>3</mn></msup></math> = n &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msubsup><mi mathvariant=\"normal\">a</mi><mn>2</mn><mn>3</mn></msubsup></math><br><math display=\"inline\"><msup><mrow><mn>3</mn></mrow><mrow><mn>3</mn></mrow></msup></math> = n &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msup><mn>3</mn><mn>3</mn></msup></mfrac></math><br>27 = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">n</mi><mn>27</mn></mfrac></math><br>n = 729</p>",
                    solution_hi: "<p>62.(d) <br>माना कि छोटे घनो की संख्या n है।<br>बड़े घन का पृष्ठीय क्षेत्रफल = 6<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><msub><mi mathvariant=\"normal\">a</mi><mn>1</mn></msub><mn>2</mn></msup></math> = 54<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><msub><mi mathvariant=\"normal\">a</mi><mn>1</mn></msub><mn>2</mn></msup></math> = 9 &rArr; a<sub>1</sub> = 3 cm<br>छोटे घन की पृष्ठीय क्षेत्रफल = 6<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><msub><mi mathvariant=\"normal\">a</mi><mn>2</mn></msub><mn>2</mn></msup></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>8</mn><mn>12</mn></mfrac></math><br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><msub><mi mathvariant=\"normal\">a</mi><mn>2</mn></msub><mn>2</mn></msup></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>8</mn><mrow><mn>12</mn><mo>&#215;</mo><mn>6</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>9</mn></mfrac></math> &rArr; a<sub>2</sub> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math><br>बडे़ घन का आयतन = n &times; छोटे घन का आयतन<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><msub><mi mathvariant=\"normal\">a</mi><mn>1</mn></msub><mn>3</mn></msup></math> = n &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msubsup><mi mathvariant=\"normal\">a</mi><mn>2</mn><mn>3</mn></msubsup></math><br><math display=\"inline\"><msup><mrow><mn>3</mn></mrow><mrow><mn>3</mn></mrow></msup></math> = n &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msup><mn>3</mn><mn>3</mn></msup></mfrac></math><br>27 = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">n</mi><mn>27</mn></mfrac></math><br>n = 729</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "63",
                    section: "17",
                    question_en: "<p>63. Simplify the following <br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mfenced open=\"{\" close=\"}\"><mrow><mn>3</mn><mo>+</mo><mn>3</mn><mo>(</mo><mfrac><mn>1</mn><mn>4</mn></mfrac><mo>&#247;</mo><mfrac><mn>15</mn><mn>20</mn></mfrac><mo>+</mo><mn>2</mn><mo>)</mo><mo>-</mo><mn>18</mn><mo>&#215;</mo><mfrac><mn>1</mn><mn>3</mn></mfrac></mrow></mfenced></mrow><mrow><mn>2</mn><mo>(</mo><mn>4</mn><mo>+</mo><mn>20</mn><mo>&#247;</mo><mn>4</mn><mi mathvariant=\"bold-italic\">&#160;</mi><mo>)</mo></mrow></mfrac></math></p>",
                    question_hi: "<p>63. निम्नांकित का मान ज्ञात कीजिए।<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mfenced open=\"{\" close=\"}\"><mrow><mn>3</mn><mo>+</mo><mn>3</mn><mo>(</mo><mfrac><mn>1</mn><mn>4</mn></mfrac><mo>&#247;</mo><mfrac><mn>15</mn><mn>20</mn></mfrac><mo>+</mo><mn>2</mn><mo>)</mo><mo>-</mo><mn>18</mn><mo>&#215;</mo><mfrac><mn>1</mn><mn>3</mn></mfrac></mrow></mfenced></mrow><mrow><mn>2</mn><mo>(</mo><mn>4</mn><mo>+</mo><mn>20</mn><mo>&#247;</mo><mn>4</mn><mi mathvariant=\"bold-italic\">&#160;</mi><mo>)</mo></mrow></mfrac></math></p>",
                    options_en: [
                        "<p><math display=\"inline\"><mfrac><mrow><mn>7</mn></mrow><mrow><mn>18</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>9</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>9</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>11</mn></mrow><mrow><mn>18</mn></mrow></mfrac></math></p>"
                    ],
                    options_hi: [
                        "<p><math display=\"inline\"><mfrac><mrow><mn>7</mn></mrow><mrow><mn>18</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>9</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>9</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>11</mn></mrow><mrow><mn>18</mn></mrow></mfrac></math></p>"
                    ],
                    solution_en: "<p>63.(b)<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mfenced open=\"{\" close=\"}\"><mrow><mn>3</mn><mo>+</mo><mn>3</mn><mo>(</mo><mfrac><mn>1</mn><mn>4</mn></mfrac><mo>&#247;</mo><mfrac><mn>15</mn><mn>20</mn></mfrac><mo>+</mo><mn>2</mn><mo>)</mo><mo>-</mo><mn>18</mn><mo>&#215;</mo><mfrac><mn>1</mn><mn>3</mn></mfrac></mrow></mfenced></mrow><mrow><mn>2</mn><mo>(</mo><mn>4</mn><mo>+</mo><mn>20</mn><mo>&#247;</mo><mn>4</mn><mi mathvariant=\"bold-italic\">&#160;</mi><mo>)</mo></mrow></mfrac></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mo>{</mo><mn>3</mn><mo>+</mo><mn>3</mn><mo>(</mo><mfrac><mn>1</mn><mn>3</mn></mfrac><mo>+</mo><mn>2</mn><mo>)</mo><mo>-</mo><mn>6</mn><mo>}</mo></mrow><mrow><mn>2</mn><mo>(</mo><mn>4</mn><mo>+</mo><mn>5</mn><mo>)</mo></mrow></mfrac></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mo>{</mo><mn>3</mn><mo>+</mo><mn>3</mn><mo>(</mo><mfrac><mn>7</mn><mn>3</mn></mfrac><mo>)</mo><mo>-</mo><mn>6</mn><mo>}</mo></mrow><mrow><mn>2</mn><mo>(</mo><mn>9</mn><mi mathvariant=\"bold-italic\">&#160;</mi><mo>)</mo></mrow></mfrac></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mo>{</mo><mn>3</mn><mo>+</mo><mn>7</mn><mo>-</mo><mn>6</mn><mo>}</mo></mrow><mn>18</mn></mfrac></math><br><math display=\"inline\"><mfrac><mrow><mn>8</mn></mrow><mrow><mn>18</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>9</mn></mfrac></math></p>",
                    solution_hi: "<p>63.(b)<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mfenced open=\"{\" close=\"}\"><mrow><mn>3</mn><mo>+</mo><mn>3</mn><mo>(</mo><mfrac><mn>1</mn><mn>4</mn></mfrac><mo>&#247;</mo><mfrac><mn>15</mn><mn>20</mn></mfrac><mo>+</mo><mn>2</mn><mo>)</mo><mo>-</mo><mn>18</mn><mo>&#215;</mo><mfrac><mn>1</mn><mn>3</mn></mfrac></mrow></mfenced></mrow><mrow><mn>2</mn><mo>(</mo><mn>4</mn><mo>+</mo><mn>20</mn><mo>&#247;</mo><mn>4</mn><mi mathvariant=\"bold-italic\">&#160;</mi><mo>)</mo></mrow></mfrac></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mo>{</mo><mn>3</mn><mo>+</mo><mn>3</mn><mo>(</mo><mfrac><mn>1</mn><mn>3</mn></mfrac><mo>+</mo><mn>2</mn><mo>)</mo><mo>-</mo><mn>6</mn><mo>}</mo></mrow><mrow><mn>2</mn><mo>(</mo><mn>4</mn><mo>+</mo><mn>5</mn><mo>)</mo></mrow></mfrac></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mo>{</mo><mn>3</mn><mo>+</mo><mn>3</mn><mo>(</mo><mfrac><mn>7</mn><mn>3</mn></mfrac><mo>)</mo><mo>-</mo><mn>6</mn><mo>}</mo></mrow><mrow><mn>2</mn><mo>(</mo><mn>9</mn><mi mathvariant=\"bold-italic\">&#160;</mi><mo>)</mo></mrow></mfrac></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mo>{</mo><mn>3</mn><mo>+</mo><mn>7</mn><mo>-</mo><mn>6</mn><mo>}</mo></mrow><mn>18</mn></mfrac></math><br><math display=\"inline\"><mfrac><mrow><mn>8</mn></mrow><mrow><mn>18</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>9</mn></mfrac></math></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "64",
                    section: "17",
                    question_en: "<p>64. If x<sup>2</sup> + y<sup>2</sup> + z<sup>2</sup> = xy + yz + zx, then the value of ( <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>17</mn><msup><mi mathvariant=\"normal\">x</mi><mn>4</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>9</mn><msup><mi mathvariant=\"normal\">y</mi><mn>4</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>16</mn><msup><mi mathvariant=\"normal\">z</mi><mn>4</mn></msup></mrow><mrow><mn>8</mn><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup><msup><mi mathvariant=\"normal\">y</mi><mn>2</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>6</mn><msup><mi mathvariant=\"normal\">y</mi><mn>2</mn></msup><msup><mi mathvariant=\"normal\">z</mi><mn>2</mn></msup><mo>+</mo><mn>10</mn><msup><mi mathvariant=\"normal\">z</mi><mn>2</mn></msup><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup></mrow></mfrac></math>) is:</p>",
                    question_hi: "<p>64. यदि <math display=\"inline\"><mi>x</mi></math><sup>2</sup> + y<sup>2</sup> + z<sup>2</sup> = xy + yz + zx है, तो ( <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>17</mn><msup><mi mathvariant=\"normal\">x</mi><mn>4</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>9</mn><msup><mi mathvariant=\"normal\">y</mi><mn>4</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>16</mn><msup><mi mathvariant=\"normal\">z</mi><mn>4</mn></msup></mrow><mrow><mn>8</mn><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup><msup><mi mathvariant=\"normal\">y</mi><mn>2</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>6</mn><msup><mi mathvariant=\"normal\">y</mi><mn>2</mn></msup><msup><mi mathvariant=\"normal\">z</mi><mn>2</mn></msup><mo>+</mo><mn>10</mn><msup><mi mathvariant=\"normal\">z</mi><mn>2</mn></msup><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup></mrow></mfrac></math>) का मान ज्ञात कीजिए।</p>",
                    options_en: [
                        " 2.00",
                        " 3.25",
                        " 2.50",
                        " 1.75"
                    ],
                    options_hi: [
                        "<p>2.00</p>",
                        "<p>3.25</p>",
                        "<p>2.50</p>",
                        "<p>1.75</p>"
                    ],
                    solution_en: "<p>64.(d) <br>Let x&nbsp;= y = z = 1 <br>Put the value in the given expression<br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>17</mn><msup><mi mathvariant=\"normal\">x</mi><mn>4</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>9</mn><msup><mi mathvariant=\"normal\">y</mi><mn>4</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>16</mn><msup><mi mathvariant=\"normal\">z</mi><mn>4</mn></msup></mrow><mrow><mn>8</mn><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup><msup><mi mathvariant=\"normal\">y</mi><mn>2</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>6</mn><msup><mi mathvariant=\"normal\">y</mi><mn>2</mn></msup><msup><mi mathvariant=\"normal\">z</mi><mn>2</mn></msup><mo>+</mo><mn>10</mn><msup><mi mathvariant=\"normal\">z</mi><mn>2</mn></msup><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup></mrow></mfrac></math><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi mathvariant=\"normal\">&#160;</mi><mn>17</mn><msup><mrow><mo>(</mo><mn>1</mn><mo>)</mo></mrow><mn>4</mn></msup><mo>+</mo><mn>9</mn><msup><mrow><mo>(</mo><mn>1</mn><mo>)</mo></mrow><mn>4</mn></msup><mo>+</mo><msup><mrow><mn>16</mn><mo>(</mo><mn>1</mn><mo>)</mo></mrow><mn>4</mn></msup></mrow><mrow><mn>8</mn><msup><mrow><mo>(</mo><mn>1</mn><mo>)</mo></mrow><mn>2</mn></msup><msup><mrow><mo>(</mo><mn>1</mn><mo>)</mo></mrow><mn>2</mn></msup><mo>+</mo><mn>6</mn><msup><mrow><mo>(</mo><mn>1</mn><mo>)</mo></mrow><mn>2</mn></msup><msup><mrow><mo>(</mo><mn>1</mn><mo>)</mo></mrow><mn>2</mn></msup><mo>+</mo><mn>10</mn><msup><mrow><mo>(</mo><mn>1</mn><mo>)</mo></mrow><mn>2</mn></msup><msup><mrow><mo>(</mo><mn>1</mn><mo>)</mo></mrow><mn>2</mn></msup></mrow></mfrac></math><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>17</mn><mo>+</mo><mn>9</mn><mo>+</mo><mn>16</mn></mrow><mrow><mn>8</mn><mo>+</mo><mn>6</mn><mo>+</mo><mn>10</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>42</mn><mn>24</mn></mfrac></math> = 1.75</p>",
                    solution_hi: "<p>64.(d) <br>माना x&nbsp;= y = z = 1 <br>दिए गए व्यंजक में मान रखने पर <br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>17</mn><msup><mi mathvariant=\"normal\">x</mi><mn>4</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>9</mn><msup><mi mathvariant=\"normal\">y</mi><mn>4</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>16</mn><msup><mi mathvariant=\"normal\">z</mi><mn>4</mn></msup></mrow><mrow><mn>8</mn><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup><msup><mi mathvariant=\"normal\">y</mi><mn>2</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>6</mn><msup><mi mathvariant=\"normal\">y</mi><mn>2</mn></msup><msup><mi mathvariant=\"normal\">z</mi><mn>2</mn></msup><mo>+</mo><mn>10</mn><msup><mi mathvariant=\"normal\">z</mi><mn>2</mn></msup><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup></mrow></mfrac></math><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi mathvariant=\"normal\">&#160;</mi><mn>17</mn><msup><mrow><mo>(</mo><mn>1</mn><mo>)</mo></mrow><mn>4</mn></msup><mo>+</mo><mn>9</mn><msup><mrow><mo>(</mo><mn>1</mn><mo>)</mo></mrow><mn>4</mn></msup><mo>+</mo><msup><mrow><mn>16</mn><mo>(</mo><mn>1</mn><mo>)</mo></mrow><mn>4</mn></msup></mrow><mrow><mn>8</mn><msup><mrow><mo>(</mo><mn>1</mn><mo>)</mo></mrow><mn>2</mn></msup><msup><mrow><mo>(</mo><mn>1</mn><mo>)</mo></mrow><mn>2</mn></msup><mo>+</mo><mn>6</mn><msup><mrow><mo>(</mo><mn>1</mn><mo>)</mo></mrow><mn>2</mn></msup><msup><mrow><mo>(</mo><mn>1</mn><mo>)</mo></mrow><mn>2</mn></msup><mo>+</mo><mn>10</mn><msup><mrow><mo>(</mo><mn>1</mn><mo>)</mo></mrow><mn>2</mn></msup><msup><mrow><mo>(</mo><mn>1</mn><mo>)</mo></mrow><mn>2</mn></msup></mrow></mfrac></math><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>17</mn><mo>+</mo><mn>9</mn><mo>+</mo><mn>16</mn></mrow><mrow><mn>8</mn><mo>+</mo><mn>6</mn><mo>+</mo><mn>10</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>42</mn><mn>24</mn></mfrac></math> = 1.75</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "65",
                    section: "17",
                    question_en: "65. A bigger circle centre at O and a smaller circle centre at P touch each other externally such that the length of their common tangent LM is 12 cm. If the radius of the bigger circle is 18 cm, then what will be the radius (in cm) of the smaller circle?",
                    question_hi: "65. केंद्र O वाला एक बड़ा वृत्त और केंद्र P वाला एक छोटा वृत्त, एक-दूसरे को बाह्य रूप से इस प्रकार स्पर्श करते हैं कि उनकी उभयनिष्ठ स्पर्श रेखा LM की लंबाई 12 cm है। यदि बड़े वृत्त की त्रिज्या 18 cm है, तो छोटे वृत्त की त्रिज्या (cm में) ज्ञात कीजिए? ",
                    options_en: [
                        " 2 ",
                        " 1 ",
                        " 3 ",
                        " 4"
                    ],
                    options_hi: [
                        " 2 ",
                        " 1 ",
                        " 3 ",
                        " 4"
                    ],
                    solution_en: "<p>65.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1738991575089.png\" alt=\"rId55\" width=\"257\" height=\"158\"><br>Length of common tangent (LM) = 2<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msub><mi mathvariant=\"normal\">r</mi><mn>1</mn></msub><msub><mi mathvariant=\"normal\">r</mi><mn>2</mn></msub></msqrt></math> <br>12 = 2<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msub><mi mathvariant=\"normal\">r</mi><mn>1</mn></msub><mo>&#215;</mo><mn>18</mn></msqrt></math> <br>144 = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi mathvariant=\"normal\">r</mi><mn>1</mn></msub><mo>&#215;</mo><mn>72</mn></math><br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi mathvariant=\"normal\">r</mi><mn>1</mn></msub></math> = 2 cm</p>",
                    solution_hi: "<p>65.(a) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1738991575089.png\" alt=\"rId55\" width=\"257\" height=\"158\"><br>उभयनिष्ठ स्पर्शरेखा की लंबाई(LM) = 2<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msub><mi mathvariant=\"normal\">r</mi><mn>1</mn></msub><msub><mi mathvariant=\"normal\">r</mi><mn>2</mn></msub></msqrt></math> <br>12 = 2<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msub><mi mathvariant=\"normal\">r</mi><mn>1</mn></msub><mo>&#215;</mo><mn>18</mn></msqrt></math> <br>144 = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi mathvariant=\"normal\">r</mi><mn>1</mn></msub><mo>&#215;</mo><mn>72</mn></math><br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi mathvariant=\"normal\">r</mi><mn>1</mn></msub></math> = 2 सेमी</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "66",
                    section: "17",
                    question_en: "<p>66. If <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mi>sec</mi><mi mathvariant=\"normal\">&#160;</mi><mi mathvariant=\"normal\">A</mi><mo>+</mo><mi>tan</mi><mi mathvariant=\"normal\">&#160;</mi><mi mathvariant=\"normal\">A</mi><mo>)</mo></mrow><mrow><mi>sec</mi><mi mathvariant=\"normal\">&#160;</mi><mi mathvariant=\"normal\">A</mi><mo>-</mo><mi>tan</mi><mi mathvariant=\"normal\">&#160;</mi><mi mathvariant=\"normal\">A</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>2</mn><mfrac><mn>51</mn><mn>79</mn></mfrac></math> then the value of sin A is equal to:</p>",
                    question_hi: "<p>66. यदि <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mi>sec</mi><mi mathvariant=\"normal\">&#160;</mi><mi mathvariant=\"normal\">A</mi><mo>+</mo><mi>tan</mi><mi mathvariant=\"normal\">&#160;</mi><mi mathvariant=\"normal\">A</mi><mo>)</mo></mrow><mrow><mi>sec</mi><mi mathvariant=\"normal\">&#160;</mi><mi mathvariant=\"normal\">A</mi><mo>-</mo><mi>tan</mi><mi mathvariant=\"normal\">&#160;</mi><mi mathvariant=\"normal\">A</mi></mrow></mfrac></math> =&nbsp; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>2</mn><mfrac><mn>51</mn><mn>79</mn></mfrac></math>&nbsp;है, तो sin A का मान कितना होगा ?</p>",
                    options_en: [
                        "<p><math display=\"inline\"><mfrac><mrow><mn>77</mn></mrow><mrow><mn>144</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>61</mn></mrow><mrow><mn>169</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>87</mn></mrow><mrow><mn>169</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>65</mn></mrow><mrow><mn>144</mn></mrow></mfrac></math></p>"
                    ],
                    options_hi: [
                        "<p><math display=\"inline\"><mfrac><mrow><mn>77</mn></mrow><mrow><mn>144</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>61</mn></mrow><mrow><mn>169</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>87</mn></mrow><mrow><mn>169</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>65</mn></mrow><mrow><mn>144</mn></mrow></mfrac></math></p>"
                    ],
                    solution_en: "<p>66.(d)<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mi>sec</mi><mi mathvariant=\"normal\">&#160;</mi><mi mathvariant=\"normal\">A</mi><mo>+</mo><mi>tan</mi><mi mathvariant=\"normal\">&#160;</mi><mi mathvariant=\"normal\">A</mi><mo>)</mo></mrow><mrow><mi>sec</mi><mi mathvariant=\"normal\">&#160;</mi><mi mathvariant=\"normal\">A</mi><mo>-</mo><mi>tan</mi><mi mathvariant=\"normal\">&#160;</mi><mi mathvariant=\"normal\">A</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>2</mn><mfrac><mn>51</mn><mn>79</mn></mfrac></math><br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mi>sec</mi><mi mathvariant=\"normal\">&#160;</mi><mi mathvariant=\"normal\">A</mi><mo>+</mo><mi>tan</mi><mi mathvariant=\"normal\">&#160;</mi><mi mathvariant=\"normal\">A</mi><mo>)</mo></mrow><mrow><mi>sec</mi><mi mathvariant=\"normal\">&#160;</mi><mi mathvariant=\"normal\">A</mi><mo>-</mo><mi>tan</mi><mi mathvariant=\"normal\">&#160;</mi><mi mathvariant=\"normal\">A</mi></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>209</mn><mn>79</mn></mfrac></math><br>By applying componendo and dividendo<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>sec</mi><mi mathvariant=\"normal\">&#160;</mi><mi mathvariant=\"normal\">A</mi><mi mathvariant=\"normal\">&#160;</mi></mrow><mrow><mi>tan</mi><mi mathvariant=\"normal\">&#160;</mi><mi mathvariant=\"normal\">A</mi></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>209</mn><mo>+</mo><mn>79</mn></mrow><mrow><mn>209</mn><mo>-</mo><mn>79</mn></mrow></mfrac></math><br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>sec</mi><mi mathvariant=\"normal\">&#160;</mi><mi mathvariant=\"normal\">A</mi><mi mathvariant=\"normal\">&#160;</mi></mrow><mrow><mi>tan</mi><mi mathvariant=\"normal\">&#160;</mi><mi mathvariant=\"normal\">A</mi></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>288</mn><mn>130</mn></mfrac></math><br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mfrac><mn>1</mn><mi>cosA</mi></mfrac><mfrac><mrow><mi>s</mi><mi>in</mi><mi mathvariant=\"normal\">&#160;</mi><mi mathvariant=\"normal\">A</mi></mrow><mrow><mi>c</mi><mi>os</mi><mi mathvariant=\"normal\">&#160;</mi><mi mathvariant=\"normal\">A</mi></mrow></mfrac></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>288</mn><mn>130</mn></mfrac></math><br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><mi>sin</mi><mi mathvariant=\"normal\">&#160;</mi><mi mathvariant=\"normal\">A</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>144</mn><mn>65</mn></mfrac></math><br>sinA = <math display=\"inline\"><mfrac><mrow><mn>65</mn></mrow><mrow><mn>144</mn></mrow></mfrac></math></p>",
                    solution_hi: "<p>66.(d)<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mi>sec</mi><mi mathvariant=\"normal\">&#160;</mi><mi mathvariant=\"normal\">A</mi><mo>+</mo><mi>tan</mi><mi mathvariant=\"normal\">&#160;</mi><mi mathvariant=\"normal\">A</mi><mo>)</mo></mrow><mrow><mi>sec</mi><mi mathvariant=\"normal\">&#160;</mi><mi mathvariant=\"normal\">A</mi><mo>-</mo><mi>tan</mi><mi mathvariant=\"normal\">&#160;</mi><mi mathvariant=\"normal\">A</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>2</mn><mfrac><mn>51</mn><mn>79</mn></mfrac></math><br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mi>sec</mi><mi mathvariant=\"normal\">&#160;</mi><mi mathvariant=\"normal\">A</mi><mo>+</mo><mi>tan</mi><mi mathvariant=\"normal\">&#160;</mi><mi mathvariant=\"normal\">A</mi><mo>)</mo></mrow><mrow><mi>sec</mi><mi mathvariant=\"normal\">&#160;</mi><mi mathvariant=\"normal\">A</mi><mo>-</mo><mi>tan</mi><mi mathvariant=\"normal\">&#160;</mi><mi mathvariant=\"normal\">A</mi></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>209</mn><mn>79</mn></mfrac></math><br>योगांतरा अनुपात के प्रयोग से&nbsp;<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>sec</mi><mi mathvariant=\"normal\">&#160;</mi><mi mathvariant=\"normal\">A</mi><mi mathvariant=\"normal\">&#160;</mi></mrow><mrow><mi>tan</mi><mi mathvariant=\"normal\">&#160;</mi><mi mathvariant=\"normal\">A</mi></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>209</mn><mo>+</mo><mn>79</mn></mrow><mrow><mn>209</mn><mo>-</mo><mn>79</mn></mrow></mfrac></math><br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>sec</mi><mi mathvariant=\"normal\">&#160;</mi><mi mathvariant=\"normal\">A</mi><mi mathvariant=\"normal\">&#160;</mi></mrow><mrow><mi>tan</mi><mi mathvariant=\"normal\">&#160;</mi><mi mathvariant=\"normal\">A</mi></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>288</mn><mn>130</mn></mfrac></math><br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mfrac><mn>1</mn><mi>cosA</mi></mfrac><mfrac><mrow><mi>s</mi><mi>in</mi><mi mathvariant=\"normal\">&#160;</mi><mi mathvariant=\"normal\">A</mi></mrow><mrow><mi>c</mi><mi>os</mi><mi mathvariant=\"normal\">&#160;</mi><mi mathvariant=\"normal\">A</mi></mrow></mfrac></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>288</mn><mn>130</mn></mfrac></math><br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><mi>sin</mi><mi mathvariant=\"normal\">&#160;</mi><mi mathvariant=\"normal\">A</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>144</mn><mn>65</mn></mfrac></math><br>sinA = <math display=\"inline\"><mfrac><mrow><mn>65</mn></mrow><mrow><mn>144</mn></mrow></mfrac></math></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "67",
                    section: "17",
                    question_en: "<p>67. A shopkeeper decides to raise the marked price of an article by 40%. How much percentage discount should he allow to be able to sell the article at the original marked price?</p>",
                    question_hi: "<p>67. एक दुकानदार एक वस्तु के अंकित मूल्य में 40% की वृद्धि करने का निर्णय लेता है। वस्तु को मूल अंकित मूल्य पर बेच पाने के लिए उसे कितने प्रतिशत की छूट देनी चाहिए ?</p>",
                    options_en: [
                        "<p>28<math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> %</p>",
                        "<p>31<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math>%</p>",
                        "<p>24<math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> %</p>",
                        "<p>26<math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math>%</p>"
                    ],
                    options_hi: [
                        "<p>28<math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> %</p>",
                        "<p>31<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math>%</p>",
                        "<p>24<math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> %</p>",
                        "<p>26<math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math>%</p>"
                    ],
                    solution_en: "<p>67.(a)<br>Let the MP = 100 units<br>MP after increment = 100 &times; <math display=\"inline\"><mfrac><mrow><mn>140</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 140 units<br>Required discount = <math display=\"inline\"><mfrac><mrow><mn>140</mn><mo>-</mo><mn>100</mn></mrow><mrow><mn>140</mn></mrow></mfrac></math> &times; 100 = 28<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>7</mn></mfrac></math> %</p>",
                    solution_hi: "<p>67.(a)<br>माना , अंकित मूल्य = 100 इकाई <br>वृद्धि के बाद अंकित मूल्य = 100 &times; <math display=\"inline\"><mfrac><mrow><mn>140</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 140 इकाई <br>आवश्यक छूट = <math display=\"inline\"><mfrac><mrow><mn>140</mn><mo>-</mo><mn>100</mn></mrow><mrow><mn>140</mn></mrow></mfrac></math> &times; 100 = 28<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>7</mn></mfrac></math> %</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "68",
                    section: "17",
                    question_en: "<p>68. Arun and Bhaskar run a race of 3 km. First, Arun gives Bhaskar a head start of 400 m and beats him by 30 seconds. While coming back, Arun gives Bhaskar a lead of 2.5 minutes and gets beaten by 500 m. What is the difference between the times in minutes in which Arun and Bhaskar can run the race for one side separately?</p>",
                    question_hi: "<p>68. अरुण और भास्कर ने 3 km की दौड़ लगाई। अरुण भास्कर को 400 m की बढ़त देता है और उसे 30 सेकंड से हरा देता है। वापस आते समय, अरुण भास्कर को 2.5 मिनट की बढ़त देता है और 500 m से हार जाता है। मिनट में, उस समय के बीच का अंतर कितना है, जिसमें अरुण और भास्कर एक तरफ की रेस के लिए अलग-अलग दौड़ लगा सकते हैं?</p>",
                    options_en: [
                        "<p>2 min</p>",
                        "<p>1.5 min</p>",
                        "<p>2.5 min</p>",
                        "<p>3 min</p>"
                    ],
                    options_hi: [
                        "<p>2 मिनट</p>",
                        "<p>1.5 मिनट</p>",
                        "<p>2.5 मिनट</p>",
                        "<p>3 मिनट</p>"
                    ],
                    solution_en: "<p>68.(b)<br><strong>Case 1:</strong> <br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; Arun&nbsp; :&nbsp; Bhaskar <br>Distance <math display=\"inline\"><mo>&#8594;</mo></math> 3000&nbsp; :&nbsp; 2600 <br>Time <math display=\"inline\"><mo>&#8594;</mo></math>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; t&nbsp; &nbsp; &nbsp; :&nbsp; &nbsp;t + 0.5 <br>Time taken to cover 3000 m = t minutes<br>For 2500 m = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">t</mi><mn>3000</mn></mfrac></math> &times; 2500 =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mi mathvariant=\"normal\">t</mi></mrow><mn>6</mn></mfrac></math> minutes<br><strong>Case 2:</strong><br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; Arun : Bhaskar <br>Distance <math display=\"inline\"><mo>&#8594;</mo></math> 2500 : 3000 <br>Time <math display=\"inline\"><mo>&#8594;</mo></math>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mi mathvariant=\"normal\">t</mi></mrow><mn>6</mn></mfrac></math> :&nbsp;<strong><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mi mathvariant=\"normal\">t</mi></mrow><mn>6</mn></mfrac></math></strong> + 2.5 <br>Now speed of Bhaskar will be same for both side<br>Hence, from case 1 and case 2<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2600</mn><mrow><mi mathvariant=\"normal\">t</mi><mo>+</mo><mn>0</mn><mo>.</mo><mn>5</mn></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3000</mn><mrow><mstyle displaystyle=\"true\"><mfrac><mrow><mn>5</mn><mi mathvariant=\"normal\">t</mi></mrow><mn>6</mn></mfrac></mstyle><mo>+</mo><mn>2</mn><mo>.</mo><mn>5</mn></mrow></mfrac></math><br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>13</mn><mrow><mi mathvariant=\"normal\">t</mi><mo>+</mo><mn>0</mn><mo>.</mo><mn>5</mn></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>90</mn><mrow><mn>5</mn><mi mathvariant=\"normal\">t</mi><mo>+</mo><mn>15</mn></mrow></mfrac></math><br>65t&nbsp;+ 195 = 90t + 45<br>25t&nbsp;= 150<br>t = 6 minute<br>Time taken by arun to travel 3000 m = 6 minute<br>Time taken by Bhaskar to travel 3000 m = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mi mathvariant=\"normal\">t</mi></mrow><mn>6</mn></mfrac><mo>+</mo><mn>2</mn><mo>.</mo><mn>5</mn></math> = 7.5 minute<br>Required difference = 7.5 - 6 = 1.5 minutes</p>",
                    solution_hi: "<p>68.(b)<br><strong>Case 1:</strong> <br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; अरुण : भास्कर <br>दूरी <math display=\"inline\"><mo>&#8594;</mo></math>&nbsp; &nbsp;3000 : 2600 <br>समय <math display=\"inline\"><mo>&#8594;</mo></math>&nbsp; &nbsp; &nbsp; t&nbsp; &nbsp;:&nbsp; &nbsp;t + 0.5 <br>अरुण द्वारा 3000 मीटर की दूरी तय करने में लिया गया समय = t&nbsp;मिनट<br>2500 मीटर के लिए = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">t</mi><mn>3000</mn></mfrac></math> &times; 2500 =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mi mathvariant=\"normal\">t</mi></mrow><mn>6</mn></mfrac></math>&nbsp;मिनट<br><strong>Case 2:</strong><br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;अरुण&nbsp; :&nbsp; भास्कर <br>दूरी <math display=\"inline\"><mo>&#8594;</mo></math>&nbsp; 2500&nbsp; :&nbsp; 3000 <br>समय <math display=\"inline\"><mo>&#8594;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mi mathvariant=\"normal\">t</mi></mrow><mn>6</mn></mfrac></math>&nbsp;&nbsp; :&nbsp; <strong><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mi mathvariant=\"normal\">t</mi></mrow><mn>6</mn></mfrac></math></strong> + 2.5<br>अब भास्कर की गति दोनों तरफ के लिए समान होगी<br>अतः , <strong>Case 1 और Case 2 से</strong><br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2600</mn><mrow><mi mathvariant=\"normal\">t</mi><mo>+</mo><mn>0</mn><mo>.</mo><mn>5</mn></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3000</mn><mrow><mstyle displaystyle=\"true\"><mfrac><mrow><mn>5</mn><mi mathvariant=\"normal\">t</mi></mrow><mn>6</mn></mfrac></mstyle><mo>+</mo><mn>2</mn><mo>.</mo><mn>5</mn></mrow></mfrac></math><br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>13</mn><mrow><mi mathvariant=\"normal\">t</mi><mo>+</mo><mn>0</mn><mo>.</mo><mn>5</mn></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>90</mn><mrow><mn>5</mn><mi mathvariant=\"normal\">t</mi><mo>+</mo><mn>15</mn></mrow></mfrac></math><br>65t&nbsp;+ 195 = 90t + 45<br>25t = 150<br>t= 6 मिनट<br>अरुण द्वारा 3000 मीटर की दूरी तय करने में लिया गया समय = 6 मिनट<br>भास्कर को 3000 मीटर की दूरी तय करने में लगा समय = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mi mathvariant=\"normal\">t</mi></mrow><mn>6</mn></mfrac><mo>+</mo><mn>2</mn><mo>.</mo><mn>5</mn></math> = 7.5 मिनट<br>आवश्यक अंतर = 7.5 - 6 = 1.5 मिनट</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "69",
                    section: "17",
                    question_en: "<p>69. Radhika requested the cashier of a bank to get her coins in lieu of her cheque worth ₹1,845. Cashier gave her a packet containing ₹5, ₹10 and ₹20 coins in the ratio of 3 : 5 : 7. What is the total worth of the smallest coin received by her ?</p>",
                    question_hi: "<p>69. राधिका ने एक बैंक के कैशियर से अनुरोध किया कि वह उसके ₹1,845 के चेक के बदले में उसे सिक्के दे दे। कैशियर ने उसे ₹5, ₹10 और ₹20 के सिक्कों का एक पैकेट 3 : 5 : 7 के अनुपात में दिया। उसके द्वारा प्राप्त सबसे छोटे सिक्के का कुल मूल्य कितना है?</p>",
                    options_en: [
                        "<p>₹135</p>",
                        "<p>₹125</p>",
                        "<p>₹145</p>",
                        "<p>₹105</p>"
                    ],
                    options_hi: [
                        "<p>₹135</p>",
                        "<p>₹125</p>",
                        "<p>₹145</p>",
                        "<p>₹105</p>"
                    ],
                    solution_en: "<p>69.(a)<br>Ratio - 5 : 10 :&nbsp; 20<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;3&nbsp; :&nbsp; 5&nbsp; :&nbsp; 7<br>&mdash;--------------------------<br>Final - 15 : 50 : 140<br>Total amount = 15 + 50 + 140 = 205 units<br>205 units = ₹ 1845<br>(required worth) 15 units = <math display=\"inline\"><mfrac><mrow><mn>1845</mn></mrow><mrow><mn>205</mn></mrow></mfrac></math> &times; 15 = ₹ 135</p>",
                    solution_hi: "<p>69.(a)<br>अनुपात - 5 : 10 : 20<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;3&nbsp; :&nbsp; 5&nbsp; :&nbsp; 7<br>-----------------------------<br>अंतिम - 15 : 50 : 140<br>कुल राशि = 15 + 50 + 140 = 205 इकाई<br>205 इकाई = ₹ 1845<br>(आवश्यक मूल्य) 15 इकाई= <math display=\"inline\"><mfrac><mrow><mn>1845</mn></mrow><mrow><mn>205</mn></mrow></mfrac></math> &times; 15 = ₹ 135</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "70",
                    section: "17",
                    question_en: "<p>70. From the salary of a software engineer, if 15% is used as house rent, 25% of the remaining is spent on household items, 25% of the remaining as income tax, and 15% of the remaining on clothing, then he is left with ₹10,404. Find his total salary.</p>",
                    question_hi: "<p>70. एक सॉफ्टवेयर इंजीनियर के वेतन में से, यदि 15% मकान के किराए पर खर्च किया जाता है, शेष का 25% घरेलू सामान पर, शेष का 25% आयकर के रूप में, और शेष का 15% कपड़ों पर खर्च किया जाता है, तो उसके पास ₹10,404 शेष बचते हैं। उसका कुल वेतन ज्ञात कीजिए।</p>",
                    options_en: [
                        "<p>₹25,600</p>",
                        "<p>₹22,600</p>",
                        "<p>₹23,600</p>",
                        "<p>₹24,600</p>"
                    ],
                    options_hi: [
                        "<p>₹25,600</p>",
                        "<p>₹22,600</p>",
                        "<p>₹23,600</p>",
                        "<p>₹24,600</p>"
                    ],
                    solution_en: "<p>70.(a)<br>Let salary of software engineer be Rs. x<br>According to the question,<br>x &times; <math display=\"inline\"><mfrac><mrow><mn>85</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>75</mn><mn>100</mn></mfrac></math>&times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>75</mn><mn>100</mn></mfrac></math> &times; <math display=\"inline\"><mfrac><mrow><mn>85</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 10404<br>x = 10404 &times; <math display=\"inline\"><mfrac><mrow><mn>20</mn></mrow><mrow><mn>17</mn></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>3</mn></mfrac></math>&times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>3</mn></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>20</mn><mn>17</mn></mfrac></math><br>x = ₹25,600</p>",
                    solution_hi: "<p>70.(a)<br>माना सॉफ्टवेयर इंजीनियर का वेतन रु. x है <br>प्रश्न के अनुसार,<br>x &times; <math display=\"inline\"><mfrac><mrow><mn>85</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>75</mn><mn>100</mn></mfrac></math>&times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>75</mn><mn>100</mn></mfrac></math> &times; <math display=\"inline\"><mfrac><mrow><mn>85</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 10404<br>x = 10404 &times; <math display=\"inline\"><mfrac><mrow><mn>20</mn></mrow><mrow><mn>17</mn></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>3</mn></mfrac></math>&times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>3</mn></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>20</mn><mn>17</mn></mfrac></math><br>x = ₹25,600</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "71",
                    section: "17",
                    question_en: "<p>71. A man invested a sum of ₹5,000 on simple interest for 5 years such that the rate of&nbsp;interest for the first 2 years is 10% per annum, for the next 3 years it is 12% per annum, How much interest (in ₹), will he earn at the end of 5 years?</p>",
                    question_hi: "<p>71. एक व्यक्ति ने ₹5,000 की धनराशि को 5 वर्ष के लिए साधारणा ब्याज पर इस प्रकार निवेश करता है कि&nbsp;पहले 2 वर्ष के लिए ब्याज दर 10% वार्षिक है, अगले 3 वर्ष के लिए यह 12% वार्षिक है। 5 वर्ष के अंत&nbsp;में वह कितना ब्याज (₹ में) अर्जित करेगा?</p>",
                    options_en: [
                        "<p>3180</p>",
                        "<p>3000</p>",
                        "<p>2450</p>",
                        "<p>2800</p>"
                    ],
                    options_hi: [
                        "<p>3180</p>",
                        "<p>3000</p>",
                        "<p>2450</p>",
                        "<p>2800</p>"
                    ],
                    solution_en: "<p>71.(d)<br>Total interest gain in 5 year = 2 &times; 10% + 3 &times; 12% = 56%<br>So, interest earn at the end of 5 year = 5000 &times; 56% = 2800</p>",
                    solution_hi: "<p>71.(d)<br>5 वर्ष में कुल ब्याज लाभ = 2 &times; 10% + 3 &times; 12% = 56%<br>तो, 5 वर्ष के अंत में अर्जित ब्याज = 5000 &times; 56% = 2800</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "72",
                    section: "17",
                    question_en: "<p>72. If <math display=\"inline\"><mi>x</mi></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>x</mi></mfrac></math>= 15, then the value of <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&#160;</mo><mn>7</mn><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup><mo>-</mo><mn>9</mn><mi mathvariant=\"normal\">x</mi><mo>+</mo><mn>7</mn></mrow><mrow><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup><mo>-</mo><mi mathvariant=\"normal\">x</mi><mo>+</mo><mn>1</mn></mrow></mfrac></math>1 is :</p>",
                    question_hi: "<p>72. यदि <math display=\"inline\"><mi>x</mi></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi mathvariant=\"normal\">x</mi></mfrac></math> = 15 है, <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&#160;</mo><mn>7</mn><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup><mo>-</mo><mn>9</mn><mi mathvariant=\"normal\">x</mi><mo>+</mo><mn>7</mn></mrow><mrow><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup><mo>-</mo><mi mathvariant=\"normal\">x</mi><mo>+</mo><mn>1</mn></mrow></mfrac></math> का मान क्या है?</p>",
                    options_en: [
                        "<p>- <math display=\"inline\"><mfrac><mrow><mn>48</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math></p>",
                        "<p>- <math display=\"inline\"><mfrac><mrow><mn>22</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>48</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>22</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math></p>"
                    ],
                    options_hi: [
                        "<p>- <math display=\"inline\"><mfrac><mrow><mn>48</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math></p>",
                        "<p>- <math display=\"inline\"><mfrac><mrow><mn>22</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>48</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>22</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math></p>"
                    ],
                    solution_en: "<p>72.(c) <br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&#160;</mo><mn>7</mn><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup><mo>-</mo><mn>9</mn><mi mathvariant=\"normal\">x</mi><mo>+</mo><mn>7</mn></mrow><mrow><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup><mo>-</mo><mi mathvariant=\"normal\">x</mi><mo>+</mo><mn>1</mn></mrow></mfrac></math><br>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&#160;</mo><mn>7</mn><mi mathvariant=\"normal\">x</mi><mo>-</mo><mn>9</mn><mo>+</mo><mstyle displaystyle=\"true\"><mfrac><mn>7</mn><mi mathvariant=\"normal\">x</mi></mfrac></mstyle></mrow><mrow><mi mathvariant=\"normal\">x</mi><mo>-</mo><mn>1</mn><mo>+</mo><mstyle displaystyle=\"true\"><mfrac><mn>1</mn><mi mathvariant=\"normal\">x</mi></mfrac></mstyle><mo>&#160;</mo></mrow></mfrac></math><br>=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>7</mn><mo>(</mo><mi mathvariant=\"normal\">x</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mstyle displaystyle=\"true\"><mfrac><mn>1</mn><mi mathvariant=\"normal\">x</mi></mfrac></mstyle><mo>)</mo><mo>-</mo><mn>9</mn></mrow><mrow><mo>(</mo><mi mathvariant=\"normal\">x</mi><mo>+</mo><mfrac><mn>1</mn><mi mathvariant=\"normal\">x</mi></mfrac><mo>)</mo><mo>-</mo><mn>1</mn></mrow></mfrac></math> <br>=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>7</mn><mo>&#215;</mo><mn>15</mn><mo>-</mo><mn>9</mn></mrow><mrow><mn>15</mn><mo>-</mo><mn>1</mn></mrow></mfrac></math> <br>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>96</mn><mn>14</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>48</mn><mn>7</mn></mfrac></math></p>",
                    solution_hi: "<p>72.(c) <br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&#160;</mo><mn>7</mn><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup><mo>-</mo><mn>9</mn><mi mathvariant=\"normal\">x</mi><mo>+</mo><mn>7</mn></mrow><mrow><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup><mo>-</mo><mi mathvariant=\"normal\">x</mi><mo>+</mo><mn>1</mn></mrow></mfrac></math><br>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&#160;</mo><mn>7</mn><mi mathvariant=\"normal\">x</mi><mo>-</mo><mn>9</mn><mo>+</mo><mstyle displaystyle=\"true\"><mfrac><mn>7</mn><mi mathvariant=\"normal\">x</mi></mfrac></mstyle></mrow><mrow><mi mathvariant=\"normal\">x</mi><mo>-</mo><mn>1</mn><mo>+</mo><mstyle displaystyle=\"true\"><mfrac><mn>1</mn><mi mathvariant=\"normal\">x</mi></mfrac></mstyle><mo>&#160;</mo></mrow></mfrac></math><br>=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>7</mn><mo>(</mo><mi mathvariant=\"normal\">x</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mstyle displaystyle=\"true\"><mfrac><mn>1</mn><mi mathvariant=\"normal\">x</mi></mfrac></mstyle><mo>)</mo><mo>-</mo><mn>9</mn></mrow><mrow><mo>(</mo><mi mathvariant=\"normal\">x</mi><mo>+</mo><mfrac><mn>1</mn><mi mathvariant=\"normal\">x</mi></mfrac><mo>)</mo><mo>-</mo><mn>1</mn></mrow></mfrac></math> <br>=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>7</mn><mo>&#215;</mo><mn>15</mn><mo>-</mo><mn>9</mn></mrow><mrow><mn>15</mn><mo>-</mo><mn>1</mn></mrow></mfrac></math> <br>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>96</mn><mn>14</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>48</mn><mn>7</mn></mfrac></math></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "73",
                    section: "17",
                    question_en: "<p>73. Twelve men working for 9 hours a day complete a piece of work in 24 days. In how many days can 8 men working for 12 hours a day complete the same piece of work ?</p>",
                    question_hi: "<p>73. बारह आदमी प्रतिदिन 9 घंटे काम करते हुए एक काम को 24 दिनों में पूरा करते हैं। 8 आदमी प्रतिदिन 12 घंटे काम करके उसी काम को कितने दिनों में पूरा कर सकते हैं?</p>",
                    options_en: [
                        "<p>28 days</p>",
                        "<p>27 days</p>",
                        "<p>24 days</p>",
                        "<p>21 days</p>"
                    ],
                    options_hi: [
                        "<p>28 दिन</p>",
                        "<p>27 दिन</p>",
                        "<p>24 दिन</p>",
                        "<p>21 दिन</p>"
                    ],
                    solution_en: "<p>73.(b)<br><strong>Formula used :</strong>-&nbsp; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi mathvariant=\"normal\">M</mi><mn>1</mn></msub><msub><mi mathvariant=\"normal\">D</mi><mn>1</mn></msub><msub><mi mathvariant=\"normal\">H</mi><mn>1</mn></msub></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi mathvariant=\"normal\">M</mi><mn>2</mn></msub><msub><mi mathvariant=\"normal\">D</mi><mn>2</mn></msub><msub><mi mathvariant=\"normal\">H</mi><mn>2</mn></msub></math><br><math display=\"inline\"><mo>&#8658;</mo></math> 12 &times; 24 &times; 9 = 8 &times; x &times; 12<br><math display=\"inline\"><mo>&#8658;</mo></math> x =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>12</mn><mo>&#215;</mo><mn>24</mn><mo>&#215;</mo><mn>9</mn><mo>&#160;</mo></mrow><mrow><mn>8</mn><mo>&#215;</mo><mn>12</mn></mrow></mfrac></math> = 27<br>Hence, required days = 27</p>",
                    solution_hi: "<p>73.(b)<br><strong>प्रयुक्त सूत्र:-</strong>&nbsp; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi mathvariant=\"normal\">M</mi><mn>1</mn></msub><msub><mi mathvariant=\"normal\">D</mi><mn>1</mn></msub><msub><mi mathvariant=\"normal\">H</mi><mn>1</mn></msub></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi mathvariant=\"normal\">M</mi><mn>2</mn></msub><msub><mi mathvariant=\"normal\">D</mi><mn>2</mn></msub><msub><mi mathvariant=\"normal\">H</mi><mn>2</mn></msub></math><br><math display=\"inline\"><mo>&#8658;</mo></math> 12 &times; 24 &times; 9 = 8 &times; x &times; 12<br><math display=\"inline\"><mo>&#8658;</mo></math> x =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>12</mn><mo>&#215;</mo><mn>24</mn><mo>&#215;</mo><mn>9</mn><mo>&#160;</mo></mrow><mrow><mn>8</mn><mo>&#215;</mo><mn>12</mn></mrow></mfrac></math> = 27<br>अतः, आवश्यक दिन = 27</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "74",
                    section: "17",
                    question_en: "<p>74. Ramendra purchased some watermelons for ₹1,500. He sold one-fourth of them at a loss of 12%. At what gain percentage should the remaining watermelons be sold so as to gain 25% on the whole transaction (correct to two decimal places) ?</p>",
                    question_hi: "<p>74. रामेद्र ने कुछ तरबूज ₹1,500 में खरीदे। उसने उनमें से एक-चौथाई तरबूज 12% की हानि पर बेच दिए। शेष तरबूजों को कितने लाभ प्रतिशत पर बेचा जाना चाहिए ताकि पूरे लेनदेन पर 25% का लाभ हो (दशमलव के दो स्थानों तक सही)?</p>",
                    options_en: [
                        "<p>37.65%</p>",
                        "<p>33.73%</p>",
                        "<p>33.43%</p>",
                        "<p>37.33%</p>"
                    ],
                    options_hi: [
                        "<p>37.65%</p>",
                        "<p>33.73%</p>",
                        "<p>33.43%</p>",
                        "<p>37.33%</p>"
                    ],
                    solution_en: "<p>74.(d) <br>When profit is 25% on whole transaction then amount = 1500 &times; 125% = ₹ 1875<br>SP of one-fourth watermelons = 1500 &times; <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mn>25</mn></mfrac></math> = ₹ 330<br>According to the question,<br>1500 &times; <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mi>x</mi><mo>+</mo><mn>100</mn><mo>)</mo></mrow><mn>100</mn></mfrac></math> = 1875 - 330<br>45 &times; (x + 100)&nbsp;= 1545 &times; 4<br>(x + 100)&nbsp;&nbsp;= 137.33<br><math display=\"inline\"><mi>x</mi></math> = 37.33%<br>So, we can say that the remaining watermelons sell at a 37.33% profit.</p>",
                    solution_hi: "<p>74.(d) <br>जब पूरे लेनदेन पर लाभ 25% है तो राशि = 1500 &times; 125% = ₹ 1875<br>एक चौथाई तरबूज़ का विक्रय मूल्य = 1500 &times; <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mn>25</mn></mfrac></math> = ₹ 330<br>प्रश्न के अनुसार,<br>1500 &times; <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mi>x</mi><mo>+</mo><mn>100</mn><mo>)</mo></mrow><mn>100</mn></mfrac></math> = 1875 - 330<br>45 &times; (x + 100)&nbsp;= 1545 &times; 4<br>(x + 100)&nbsp;&nbsp;= 137.33<br><math display=\"inline\"><mi>x</mi></math> = 37.33%<br>तो, हम कह सकते हैं कि शेष तरबूज़ 37.33% लाभ पर बेचे गये होगे।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "75",
                    section: "7",
                    question_en: "<p>75. Study the given table and answer the question that follows.<br>The table shows the number of students appeared and qualified (in thousands) district-wise in the EMCET examination in a state.<br><strong id=\"docs-internal-guid-be2ce9d1-7fff-b259-19d9-3564d9da0158\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXeP16ZTvO2p_r1upUbG0aDyiHeqe38PPcTnxxjAGJ_Bi-etq_ASodVSJL9TthhGfvwDTpAxuoIeCvTDhsZ3ixIWBRsnAiFilutCZEo0BKZD3J5nqMK7lDpaIYS9yMnHutj_9rLJ?key=V2XySf82cjYQ5JpUeeU2veTy\" width=\"574\" height=\"108\"></strong></p>\n<p>The total percentage of students who qualified in the year 2016 (correct up to two decimals) is:</p>",
                    question_hi: "<p>75. निम्नलिखित तालिका का अध्ययन कीजिए और नीचे दिए गए प्रश्न का उत्तर दीजिए।<br>तालिका में एक राज्य में EMCET परीक्षा में जिलेवार उपस्थित होने वाले और उत्तीर्ण (हजार में) होने वाले विद्यार्थियों की संख्या को दर्शाया गया है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1738991575315.png\" alt=\"rId57\" width=\"571\" height=\"103\"> <br>वर्ष 2016 में उत्तीर्ण होने वाले विद्यार्थियों का कुल प्रतिशत क्या है (केवल दशमलव के दो स्थान तक)?</p>",
                    options_en: [
                        "<p>52.82%</p>",
                        "<p>55.18%</p>",
                        "<p>44.72%</p>",
                        "<p>54.78%</p>"
                    ],
                    options_hi: [
                        "<p>52.82%</p>",
                        "<p>55.18%</p>",
                        "<p>44.72%</p>",
                        "<p>54.78%</p>"
                    ],
                    solution_en: "<p>75.(d)<br>Total number of students in 2016 = 55 + 35 + 48 + 50 + 55 + 60 = 303<br>Qualified students = 42 + 22 + 26 + 30 + 27 + 19 = 166<br>Required % = <math display=\"inline\"><mfrac><mrow><mn>166</mn></mrow><mrow><mn>303</mn></mrow></mfrac></math> &times; 100 = 54.78%</p>",
                    solution_hi: "<p>75.(d)<br>2016 में छात्रों की कुल संख्या = 55 + 35 + 48 + 50 + 55 + 60 = 303<br>उत्तीर्ण छात्र = 42 + 22 + 26 + 30 + 27 + 19 = 166<br>आवश्यक % = <math display=\"inline\"><mfrac><mrow><mn>166</mn></mrow><mrow><mn>303</mn></mrow></mfrac></math> &times; 100 = 54.78%</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "76",
                    section: "7",
                    question_en: "<p>76. The following sentence has been split into four segments. Identify the segment that contains a grammatical error.<br>The Supreme Court set away / the verdict of the High Court / and agreed to hear the&nbsp;plea / of the complainant.</p>",
                    question_hi: "<p>76. The following sentence has been split into four segments. Identify the segment that contains&nbsp; &nbsp;grammatical error.<br>The Supreme Court set away / the verdict of the High Court / and agreed to hear the&nbsp;plea / of the complainant.</p>",
                    options_en: [
                        "<p>of the complainant</p>",
                        "<p>The Supreme Court set away</p>",
                        "<p>and agreed to hear the plea</p>",
                        "<p>the verdict of the High Court</p>"
                    ],
                    options_hi: [
                        "<p>of the complainant</p>",
                        "<p>The Supreme Court set away</p>",
                        "<p>and agreed to hear the plea</p>",
                        "<p>the verdict of the High Court</p>"
                    ],
                    solution_en: "<p>76.(b) The Supreme Court set away<br>&lsquo;Set away&rsquo; must be replaced with &lsquo;set aside&rsquo;. The phrasal verb &lsquo;set aside&rsquo; means to deprive of legal effect because it has been proved wrong. The given sentence states that the Supreme Court set aside the verdict of the High Court. Hence, &lsquo;The Supreme Court set aside&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>76.(b) The Supreme Court set away<br>&lsquo;Set away&rsquo; के स्थान पर &lsquo;set aside&rsquo; का प्रयोग किया जाएगा। Phrasal verb &lsquo;set aside&rsquo; का अर्थ है किसी कानूनी प्रभाव(legal effect) को समाप्त करना क्योंकि यह गलत साबित हुआ है। दिए गए sentence में कहा गया है कि सुप्रीम कोर्ट ने हाई कोर्ट के फैसले को रद्द कर दिया। अतः &lsquo;The Supreme Court set aside&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "77",
                    section: "7",
                    question_en: "<p>77. Select the most appropriate option that can substitute the underlined segment in the given sentence.<br>I <span style=\"text-decoration: underline;\">have been yet</span> to go to the flower exhibition at Delhi University.</p>",
                    question_hi: "<p>77. Select the most appropriate option that can substitute the underlined segment in the given sentence.<br>I <span style=\"text-decoration: underline;\">have been yet</span> to go to the flower exhibition at Delhi University.</p>",
                    options_en: [
                        "<p>have yet been</p>",
                        "<p>have yet being</p>",
                        "<p>had been yet</p>",
                        "<p>have yet</p>"
                    ],
                    options_hi: [
                        "<p>have yet been</p>",
                        "<p>have yet being</p>",
                        "<p>had been yet</p>",
                        "<p>have yet</p>"
                    ],
                    solution_en: "<p>77.(d) have yet<br>The given sentence is in the active voice as the subject &lsquo;I&rsquo; is the being considered the doer of the action. Therefore, &lsquo;been&rsquo; will be removed from the sentence. &lsquo;Have to&rsquo; is a semi-modal and takes the first form of the verb(go) after it. Hence, &lsquo;have yet&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>77.(d) have yet<br>दिया गया Sentence, active voice में है क्योंकि subject &lsquo;I&rsquo; को action का doer माना जाता है। इसलिए, sentence से \'been\' हटा दिया जाएगा। \'Have to\' एक semi-modal है और इसके बाद verb(go) की first form का प्रयोग होता है। अतः, \'have yet\' सबसे उपयुक्त उत्तर है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "78",
                    section: "7",
                    question_en: "<p>78. In the following questions, out of the four alternatives, choose the one which can be substituted for the given words/sentence.&nbsp;<br>A person who collects and /or studies stamps.</p>",
                    question_hi: "<p>78. In the following questions, out of the four alternatives, choose the one which can be substituted for the given words/sentence. <br>A person who collects and /or studies stamps.</p>",
                    options_en: [
                        "<p>Pioneer</p>",
                        "<p>Philatelist</p>",
                        "<p>Pianist</p>",
                        "<p>Philanthropist</p>"
                    ],
                    options_hi: [
                        "<p>Pioneer</p>",
                        "<p>Philatelist</p>",
                        "<p>Pianist</p>",
                        "<p>Philanthropist</p>"
                    ],
                    solution_en: "<p>78.(b) <strong>philatelist</strong> - a person who collects and study postage stamps.<br><strong>Pioneer</strong> - a person who is among the first to explore or settle a new country or area<br><strong>Pianist</strong> - A person who plays the piano.<br><strong>Philanthropist</strong> - a person who seeks to promote the welfare of others, especially by the generous donation of money to good causes.</p>",
                    solution_hi: "<p>78.(b) <strong>philatelist</strong> - एक व्यक्ति जो डाक टिकटों का संग्रह और अध्ययन करता है।<br><strong>Pioneer</strong> - एक व्यक्ति जो किसी नए देश या क्षेत्र का पता लगाने या बसने वालों में सबसे पहले है।<br><strong>Pianist </strong>- एक व्यक्ति जो piano बजाता है।<br><strong>Philanthropist</strong> - एक व्यक्ति जो दूसरों के कल्याण को बढ़ावा देना चाहता है, विशेष रूप से अच्छे कारणों के लिए ( धन के उदार दान से)।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "79",
                    section: "7",
                    question_en: "<p>79. Select the option that can substitute the bracketed word segment correctly and complete the following sentence meaningfully.<br>You all need to (get into) the books prescribed in your syllabus.</p>",
                    question_hi: "<p>79. Select the option that can substitute the bracketed word segment correctly and complete the following sentence meaningfully.<br>You all need to (get into) the books prescribed in your syllabus.</p>",
                    options_en: [
                        "<p>read abreast</p>",
                        "<p>read about</p>",
                        "<p>go through</p>",
                        "<p>go about</p>"
                    ],
                    options_hi: [
                        "<p>read abreast</p>",
                        "<p>read about</p>",
                        "<p>go through</p>",
                        "<p>go about</p>"
                    ],
                    solution_en: "<p>79.(c) go through<br>&lsquo;Go through&rsquo; is a phrasal verb which means to study something in detail. The given sentence states that you all need to go through the books prescribed in your syllabus. Hence, &lsquo;go through&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>79.(c) go through<br>&lsquo;Go through&rsquo; एक phrasal verb है जिसका अर्थ है किसी चीज़ का विस्तार से अध्ययन करना। दिए गए sentence में कहा गया है कि आप सभी को अपने syllabus में निर्धारित(prescribed) पुस्तकों को पढ़ना चाहिए। अतः, &lsquo;go through&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "80",
                    section: "7",
                    question_en: "<p>80. Choose the best alternative which best expresses the meaning of the idiom/ phrase given below as underlined in the sentence.<br>Sheila\'s <span style=\"text-decoration: underline;\"><strong>tall tales</strong></span> have no meaning.</p>",
                    question_hi: "<p>80. Choose the best alternative which best expresses the meaning of the idiom/ phrase given below as underlined in the sentence.<br>Sheila\'s <span style=\"text-decoration: underline;\"><strong>tall tales</strong></span> have no meaning.</p>",
                    options_en: [
                        "<p>jealousy</p>",
                        "<p>greed</p>",
                        "<p>boasting</p>",
                        "<p>pride</p>"
                    ],
                    options_hi: [
                        "<p>jealousy</p>",
                        "<p>greed</p>",
                        "<p>boasting</p>",
                        "<p>pride</p>"
                    ],
                    solution_en: "<p>80.(c) Boasting<br><strong>Example</strong> - My uncle has always been fond of telling tall tales about his time overseas.</p>",
                    solution_hi: "<p>80 (c) Boasting/ शेखी बघारना <br><strong>Example</strong> - My uncle has always been fond of telling tall tales about his time overseas./ मेरे अंकल को विदेश में अपने बिताए समय के बारे में शेखी बघारने का हमेशा से शौक रहा है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "81",
                    section: "7",
                    question_en: "<p>81. Find a word that is the synonym of :<br>Peculiar</p>",
                    question_hi: "<p>81. Find a word that is the synonym of :<br>Peculiar</p>",
                    options_en: [
                        "<p>special</p>",
                        "<p>strange</p>",
                        "<p>ordinary</p>",
                        "<p>rare</p>"
                    ],
                    options_hi: [
                        "<p>special</p>",
                        "<p>strange</p>",
                        "<p>ordinary</p>",
                        "<p>rare</p>"
                    ],
                    solution_en: "<p>81.(b) strange<br><strong>Peculiar</strong> - different to what is normal or expected; strange.<br><strong>Look at the sentence:</strong><br>It seems peculiar that he would leave town and not tell anybody.</p>",
                    solution_hi: "<p>81.(b) strange<br><strong>Peculiar</strong> - जो सामान्य या अपेक्षित है उससे भिन्न।<br><strong>वाक्य निर्माण:</strong><br>It seems peculiar that he would leave town and not tell anybody.<br>यह अजीब लगता है कि वह शहर छोड़ देगा और किसी को नहीं बताएगा।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "82",
                    section: "7",
                    question_en: "<p>82. Given below are four jumbled sentences. Find the correct order: <br>P: The Information Technology today is rightly called the Technology of the Century as it has found its application and use in every walk society of the world.&nbsp;&nbsp; <br>Q: Distances no longer exist and the world appears to have shrunk into a Global Village.&nbsp;&nbsp;&nbsp; <br>R: It is really a landmark achievement that more than six billion population of the world will soon be living in a virtual village, as&nbsp;&nbsp;compact as any small Indian village of a few thousand population.&nbsp; <br>S:The Wisdom of the wisest is today available to the stupidest of the person thus ushering in an era of real equality of opportunity to all.</p>",
                    question_hi: "<p>82. Given below are four jumbled sentences. Find the correct order: <br>P: The Information Technology today is rightly called the Technology of the Century as it has found its application and use in every walk society of the world.&nbsp;&nbsp; <br>Q: Distances no longer exist and the world appears to have shrunk into a Global Village.&nbsp;&nbsp;&nbsp; <br>R: It is really a landmark achievement that more than six billion population of the world will soon be living in a virtual village, as&nbsp;&nbsp;compact as any small Indian village of a few thousand population.&nbsp; <br>S:The Wisdom of the wisest is today available to the stupidest of the person thus ushering in an era of real equality of opportunity to all.</p>",
                    options_en: [
                        "<p>&nbsp;QPSR</p>",
                        "<p>&nbsp;PQRS</p>",
                        "<p>&nbsp;RSQP</p>",
                        "<p>&nbsp;QRSP</p>"
                    ],
                    options_hi: [
                        "<p>&nbsp;QPSR</p>",
                        "<p>&nbsp;PQRS</p>",
                        "<p>&nbsp;RSQP</p>",
                        "<p>&nbsp;QRSP</p>"
                    ],
                    solution_en: "<p>82.(b) PQRS. <br>Sentence P is the starting line of the parajumble because it tells the subject of the parajumble that is Information Technology. Q will be followed by R as Q mentions about a global village and R further elaborates that.Finally S will be the last sentence.So PQRS is the correct order. So Option (b) is the correct answer.</p>",
                    solution_hi: "<p>82.(b) PQRS. <br>वाक्य P, parajumble की शुरुआती लाइन है क्योंकि यह parajumble के विषय को बताती है जो कि सूचना प्रौद्योगिकी (Information Technology) है। Q के बाद R होगा क्योंकि Q एक वैश्विक गांव (global village) के बारे में उल्लेख करता है और R विस्तार से बताता है। अंत में S अंतिम वाक्य होगा। इसलिए PQRS सही क्रम है। तो विकल्प (b) सही उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "83",
                    section: "7",
                    question_en: "<p>83. Find the correctly spelt word.</p>",
                    question_hi: "<p>83. Find the correctly spelt word.</p>",
                    options_en: [
                        "<p>Auxiliary</p>",
                        "<p>auxilary</p>",
                        "<p>Auxiliury</p>",
                        "<p>auxiliery</p>"
                    ],
                    options_hi: [
                        "<p>Auxiliary</p>",
                        "<p>auxilary</p>",
                        "<p>Auxiliury</p>",
                        "<p>auxiliery</p>"
                    ],
                    solution_en: "<p>83.(a) Auxiliary</p>",
                    solution_hi: "<p>83.(a) Auxiliary.</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "84",
                    section: "7",
                    question_en: "<p>84. Choose the word opposite in meaning to the given word.<br>Controversial</p>",
                    question_hi: "<p>84. Choose the word opposite in meaning to the given word.<br>Controversial</p>",
                    options_en: [
                        "<p>uncertain</p>",
                        "<p>dubious</p>",
                        "<p>undisputed</p>",
                        "<p>questionable</p>"
                    ],
                    options_hi: [
                        "<p>uncertain</p>",
                        "<p>dubious</p>",
                        "<p>undisputed</p>",
                        "<p>questionable</p>"
                    ],
                    solution_en: "<p>84.(c) <strong>Undisputed</strong> - not disputed or called in question; accepted. <br><strong>Controversial</strong> - giving rise or likely to give rise to controversy or public disagreement. <br><strong>Example</strong> : Abortion is a highly controversial subject.</p>",
                    solution_hi: "<p>84.(c) <strong>undisputed</strong> - साफ़-जाहिर <br><strong>Controversial</strong> - विवादित <br><strong>उदहारण</strong> : Abortion is a highly controversial subject./ गर्भपात एक अत्यधिक विवादास्पद विषय है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "85",
                    section: "7",
                    question_en: "<p>85. Choose the best alternative which best expresses the meaning of the idiom/ phrase given below. <br>The teacher&rsquo;s announcement to conduct a snap test came as <span style=\"text-decoration: underline;\"><strong>a bolt from the blue</strong></span> to many students.</p>",
                    question_hi: "<p>85. Choose the best alternative which best expresses the meaning of the idiom/ phrase given below. <br>The teacher&rsquo;s announcement to conduct a snap test came as <span style=\"text-decoration: underline;\"><strong>a bolt from the blue</strong></span> to many students.</p>",
                    options_en: [
                        "<p>imaginary</p>",
                        "<p>unexpected</p>",
                        "<p>forbidden</p>",
                        "<p>heavenly</p>"
                    ],
                    options_hi: [
                        "<p>imaginary</p>",
                        "<p>unexpected</p>",
                        "<p>forbidden</p>",
                        "<p>heavenly</p>"
                    ],
                    solution_en: "<p>85.(b) unexpected</p>",
                    solution_hi: "<p>85.(b) unexpected/अप्रत्याशित।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "86",
                    section: "7",
                    question_en: "<p>86. Identify the segment in the sentence which contains grammatical error. Mark &lsquo;no error&rsquo; in case the sentence given is correct. <br>It is better to do not calculate your gains before they are realised.</p>",
                    question_hi: "<p>86. Identify the segment in the sentence which contains grammatical error. Mark &lsquo;no error&rsquo; in case the sentence given is correct. <br>It is better to do not calculate your gains before they are realised.</p>",
                    options_en: [
                        "<p>It is better</p>",
                        "<p>to do not calculate your gains</p>",
                        "<p>before they are realised.</p>",
                        "<p>No error</p>"
                    ],
                    options_hi: [
                        "<p>It is better</p>",
                        "<p>to do not calculate your gains</p>",
                        "<p>before they are realised.</p>",
                        "<p>No error</p>"
                    ],
                    solution_en: "<p>86.(b) to do not calculate your gains <br>The use of the verb &lsquo;do&rsquo; in the former part of the sentence is incorrect. Hence, we will remove &lsquo;do&rsquo; &amp; &lsquo;to not to calculate your gains&rsquo; becomes the most appropriate answer.</p>",
                    solution_hi: "<p>86.(b) to do not calculate your gains. <br>वाक्य के पूर्व भाग में क्रिया &lsquo;do&rsquo; का प्रयोग गलत है। इसलिए, हम &lsquo;do&rsquo; को हटा देंगे और &lsquo;to not to calculate your gains&rsquo; सबसे उपयुक्त उत्तर बन जाएगा।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "87",
                    section: "7",
                    question_en: "<p>87. In the following questions, out of the four alternatives, choose the one which can be substituted for the given words/sentence.<br>Military waking signal sounded in the morning</p>",
                    question_hi: "<p>87. In the following questions, out of the four alternatives, choose the one which can be substituted for the given words/sentence.<br>Military waking signal sounded in the morning</p>",
                    options_en: [
                        "<p>Military waking signal sounded in the morning Reveille</p>",
                        "<p>Lullaby</p>",
                        "<p>Anthem</p>",
                        "<p>Soprano</p>"
                    ],
                    options_hi: [
                        "<p>Military waking signal sounded in the morning Reveille</p>",
                        "<p>Lullaby</p>",
                        "<p>Anthem</p>",
                        "<p>Soprano</p>"
                    ],
                    solution_en: "<p>87.(a) Reveille. <br><strong>Reveille</strong> - a signal sounded especially on a bugle or drum to wake personnel in the armed forces.<br><strong>Lullaby-</strong> a quiet, gentle song sung to send a child to sleep.<br><strong>Anthem-</strong> a rousing or uplifting song identified with a particular group, body, or cause. <br><strong>Soprano-</strong> the highest singing voice.</p>",
                    solution_hi: "<p>87.(a) Reveille. <br><strong>R&eacute;veille</strong> - सशस्त्र बलों में कर्मियों को जगाने के लिए विशेष रूप से बिगुल या ड्रम पर बजने वाला एक संकेत।<br><strong>Lullaby-</strong> लोरी <br><strong>Anthem</strong> - एक विशेष समूह को पहचाना जाने वाला एक उत्साही या उत्थान गीत<br><strong id=\"docs-internal-guid-eecd7b02-7fff-2d69-5580-645132c1f74c\">Soprano-</strong> सबसे ऊंची गायन आवाज।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "88",
                    section: "7",
                    question_en: "<p>88. Find the correctly spelt word.</p>",
                    question_hi: "<p>88. Find the correctly spelt word.</p>",
                    options_en: [
                        "<p>Sanctimonous</p>",
                        "<p>sanctimoneous</p>",
                        "<p>sanctimonious</p>",
                        "<p>sanctiminious</p>"
                    ],
                    options_hi: [
                        "<p>Sanctimonous</p>",
                        "<p>sanctimoneous</p>",
                        "<p>sanctimonious</p>",
                        "<p>sanctiminious</p>"
                    ],
                    solution_en: "<p>88.(c) sanctimonious</p>",
                    solution_hi: "<p>88.(c) sanctimonious / पाखंडी।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "89",
                    section: "7",
                    question_en: "<p>89. Select the option that expresses the given sentence in reported speech.<br>\'Do you have a fair copy of this document?\' The editor asked the reporter.</p>",
                    question_hi: "<p>89. Select the option that expresses the given sentence in reported speech.<br>\'Do you have a fair copy of this document?\' The editor asked the reporter.</p>",
                    options_en: [
                        "<p>The editor asked the reporter whether did he have a fair copy of that document</p>",
                        "<p>The editor asked the reporter whether he had a fair copy of that document</p>",
                        "<p>The editor asked the reporter whether he had had a fair copy of that document</p>",
                        "<p>The editor asked the reporter whether had he had a fair copy of that document.</p>"
                    ],
                    options_hi: [
                        "<p>The editor asked the reporter whether did he have a fair copy of that document</p>",
                        "<p>The editor asked the reporter whether he had a fair copy of that document</p>",
                        "<p>The editor asked the reporter whether he had had a fair copy of that document</p>",
                        "<p>The editor asked the reporter whether had he had a fair copy of that document.</p>"
                    ],
                    solution_en: "<p>89.(b) The editor asked the reporter whether he had a fair copy of that document.(Correct)<br>(a) The editor asked the reporter whether <span style=\"text-decoration: underline;\">did he have</span> a fair copy of that document.(Incorrect Tense)<br>(c) The editor asked the reporter whether he <span style=\"text-decoration: underline;\">had had</span> a fair copy of that document.(Repetition of Verb)<br>(d) The editor asked the reporter <span style=\"text-decoration: underline;\">whether had he</span> had a fair copy of that document.(Incorrect Sentence Structure)</p>",
                    solution_hi: "<p>89.(b) The editor asked the reporter whether he had a fair copy of that document.(Correct)<br>(a) The editor asked the reporter whether <span style=\"text-decoration: underline;\">did he have</span> a fair copy of that document.(गलत Tense)<br>(c) The editor asked the reporter whether he <span style=\"text-decoration: underline;\">had ha</span>d a fair copy of that document.(Verb का Repetition)<br>(d) The editor asked the reporter <span style=\"text-decoration: underline;\">whether had he</span> had a fair copy of that document.(गलत Sentence Structure)</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "90",
                    section: "7",
                    question_en: "<p>90. Select the most appropriate option to fill in the blank.<br>Scientists and politicians are ______ becoming aware of another factor that could seriously threaten the _____balance between production and consumption of food and climate change.</p>",
                    question_hi: "<p>90. Select the most appropriate option to fill in the blank.<br>Scientists and politicians are _____ becoming aware of another factor that could seriously threaten the _____ balance between production and consumption of food and climate change.</p>",
                    options_en: [
                        "<p>both; tenuous</p>",
                        "<p>duo; questionable</p>",
                        "<p>alike; feeble</p>",
                        "<p>alike; shaky</p>"
                    ],
                    options_hi: [
                        "<p>both; tenuous</p>",
                        "<p>duo; questionable</p>",
                        "<p>alike; feeble</p>",
                        "<p>alike; shaky</p>"
                    ],
                    solution_en: "<p>90.(a) both; tenuous<br>Tenuous means very weak or slight.</p>",
                    solution_hi: "<p>90.(a) both; tenuous<br>Tenuous का अर्थ है- बहुत कमजोर या मामूली।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "91",
                    section: "7",
                    question_en: "<p>91.&nbsp;Select the option that expresses the given sentence in passive voice.<strong id=\"docs-internal-guid-cd1b0f8d-7fff-9d7a-7a13-e91acd20507b\"></strong></p>\n<p>The rescue team could have completed the task if there had been uninterrupted power supply.</p>",
                    question_hi: "<p>91.Select the option that expresses the given sentence in passive voice.&nbsp;</p>\n<p>The rescue team could have completed the task if there had been uninterrupted power supply.</p>",
                    options_en: [
                        "<p>The task can have completed by the rescue team if there was uninterrupted power supply.</p>",
                        "<p>The task could have been completed by the rescue team if there had been uninterrupted power supply.</p>",
                        "<p>The task could be completed by the rescue team if there was uninterrupted power supply.</p>",
                        "<p>The task could have to be completed by the rescue team if there was uninterrupted power supply.</p>"
                    ],
                    options_hi: [
                        "<p>The task can have completed by the rescue team if there was uninterrupted power supply.</p>",
                        "<p>The task could have been completed by the rescue team if there had been uninterrupted power supply.</p>",
                        "<p>The task could be completed by the rescue team if there was uninterrupted power supply.</p>",
                        "<p>The task could have to be completed by the rescue team if there was uninterrupted power supply.</p>"
                    ],
                    solution_en: "<p>91.(b) The task could have been completed by the rescue team if there had been uninterrupted power supply.(Correct)<br>(a) The task <span style=\"text-decoration: underline;\">can have</span> completed by the rescue team if there was uninterrupted power supply.(Incorrect Helping Verbs)<br>(c) The task <span style=\"text-decoration: underline;\">could be</span> completed by the rescue team if there was uninterrupted power supply.(Incorrect Helping Verbs)<br>(d) The task <span style=\"text-decoration: underline;\">could have to b</span>e completed by the rescue team if there was uninterrupted power supply.(Incorrect Helping Verbs)</p>",
                    solution_hi: "<p>91.(b) The task could have been completed by the rescue team if there had been uninterrupted power supply.(Correct)<br>(a) The task <span style=\"text-decoration: underline;\">can have</span> completed by the rescue team if there was uninterrupted power supply.(गलत Helping Verbs)<br>(c) The task <span style=\"text-decoration: underline;\">could be</span> completed by the rescue team if there was uninterrupted power supply.(गलत Helping Verbs)<br>(d) The task <span style=\"text-decoration: underline;\">could have to be</span> completed by the rescue team if there was uninterrupted power supply.(गलत Helping Verbs)</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "92",
                    section: "7",
                    question_en: "<p>92. Find a word that is the synonym of<br>CANTANKEROUS</p>",
                    question_hi: "<p>92. Find a word that is the synonym of<br>CANTANKEROUS</p>",
                    options_en: [
                        "<p>Cancerous</p>",
                        "<p>Ferocious</p>",
                        "<p>Quarrelsome</p>",
                        "<p>Fissiparous</p>"
                    ],
                    options_hi: [
                        "<p>Cancerous</p>",
                        "<p>Ferocious</p>",
                        "<p>Quarrelsome</p>",
                        "<p>Fissiparous</p>"
                    ],
                    solution_en: "<p>92.(c) Quarrelsome <br><strong>Cantankerous-</strong> bad-tempered, argumentative, and uncooperative.<br><strong>Cancerous-</strong> affected by or showing abnormalities characteristic of cancer.<br><strong>Ferocious-</strong> savagely fierce, cruel, or violent.<br><strong>Fissiparous-</strong> inclined to cause or undergo division into separate parts or groups.</p>",
                    solution_hi: "<p>92.(c) Quarrelsome <br><strong>Cantankerous-</strong> बुरे स्वभाव वाले, तर्कशील और असहयोगी। <br><strong>Cancerous-</strong> कैंसर की असामान्यताओं से प्रभावित या दिखा रहा है।<br><strong>Ferocious-</strong> बेरहमी से , क्रूर, या हिंसक। <br><strong>Fissiparous-</strong> अलग-अलग हिस्सों या समूहों में विभाजन का कारण</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "93",
                    section: "7",
                    question_en: "<p>93. Pick a word opposite in meaning to <br>Gullible</p>",
                    question_hi: "<p>93. Pick a word opposite in meaning to <br>Gullible</p>",
                    options_en: [
                        "<p>susceptible</p>",
                        "<p>cynical</p>",
                        "<p>severe</p>",
                        "<p>skeptical</p>"
                    ],
                    options_hi: [
                        "<p>susceptible</p>",
                        "<p>cynical</p>",
                        "<p>severe</p>",
                        "<p>skeptical</p>"
                    ],
                    solution_en: "<p>93.(d) Skeptical<br><strong>Gullible-</strong> easily persuaded to believe something.<br><strong>Skeptical-</strong> not easily convinced, having doubts or reservations.<br><strong>Susceptible-</strong> likely or liable to be influenced or harmed by a particular thing.<br><strong>Cynical-</strong> believing that people are motivated purely by self-interest, distrustful of human sincerity or integrity.<br><strong>Severe-</strong> very great, intense.</p>",
                    solution_hi: "<p>93. (d) Skeptical<br><strong>Gullible-</strong> किसी बात पर विश्वास करने के लिए आसानी से राजी हो जाना ।<br><strong>Skeptical-</strong> आसानी से आश्वस्त नहीं होना, संदेह या आपत्ति होना<br><strong>Susceptible-किसी</strong> विशेष चीज़ से प्रभावित या क्षतिग्रस्त होने की संभावना या उत्तरदायी।<br><strong>Cynical-</strong> मानवीय ईमानदारी या अखंडता के प्रति अविश्वास ।<br><strong>Severe-</strong> गहन।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "94",
                    section: "7",
                    question_en: "<p>94. Select the most appropriate option to fill in the blank.<br>He is always angry________ his children.</p>",
                    question_hi: "<p>94. Select the most appropriate option to fill in the blank.<br>He is always angry________ his children.</p>",
                    options_en: [
                        "<p>with</p>",
                        "<p>on</p>",
                        "<p>over</p>",
                        "<p>at</p>"
                    ],
                    options_hi: [
                        "<p>He is always angry________ his children. with</p>",
                        "<p>on</p>",
                        "<p>over</p>",
                        "<p>at</p>"
                    ],
                    solution_en: "<p>94.(a) with<br>Structure - &ldquo;angry with somebody&rdquo;</p>",
                    solution_hi: "<p>94.(a) with/ साथ। <br>Structure - &ldquo;angry with somebody&rdquo;</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "95",
                    section: "7",
                    question_en: "<p>95. Given below are four jumbled sentences. Find the correct order: <br>P: They must be given or rather \'fed\' with coal and given petrol to drink from time to time.<br>Q : Already man spends most of his time looking after and waiting upon them.<br>R : Yet he has grown so dependent on them that they have almost become the masters now.<br>S : It is very true that machines were made for the sole purpose of being man&rsquo;s servants.</p>",
                    question_hi: "<p>95. Given below are four jumbled sentences. Find the correct order: <br>P: They must be given or rather \'fed\' with coal and given petrol to drink from time to time.<br>Q : Already man spends most of his time looking after and waiting upon them.<br>R : Yet he has grown so dependent on them that they have almost become the masters now.<br>S : It is very true that machines were made for the sole purpose of being man&rsquo;s servants.</p>",
                    options_en: [
                        "<p>RSPQ</p>",
                        "<p>RSQP</p>",
                        "<p>SPQR</p>",
                        "<p>SRQP</p>"
                    ],
                    options_hi: [
                        "<p>RSPQ</p>",
                        "<p>RSQP</p>",
                        "<p>SPQR</p>",
                        "<p>SRQP</p>"
                    ],
                    solution_en: "<p>95.(d) SRQP <br>S will be the first sentence and it is immediately followed by sentence R. It tells that although machines were made to serve men, they have almost become masters now. However, next will be the sentence Q followed by sentence P which tells how machines are becoming masters now. Hence, the correct sequence is (d) SRQP.</p>",
                    solution_hi: "<p>95.(d) SRQP <br>S पहला वाक्य होगा और उसके ठीक बाद वाक्य R होगा। यह बताता है कि यद्यपि मशीनों को पुरुषों की सेवा के लिए बनाया गया था, वे अब लगभग मालिक बन गई हैं। हालाँकि, अगला वाक्य Q होगा जिसके बाद वाक्य P होगा जो बताता है कि कैसे मशीनें अब मालिक बन रही हैं। अत: सही क्रम (d) SRQP है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "96",
                    section: "7",
                    question_en: "<p>96. <strong>Cloze Test :</strong><br>Good taste is not always ___(96)___ and most people manage to get ___(97)___ without it. It is fortunate, therefore, that so many of the ___(98)___ of life are now chosen for us by ___(99)___ Even among luxuries, the margin for ___(100)___ taste is constantly being reduced.<br>Select the appropriate option to fill in the blank number 96.</p>",
                    question_hi: "<p>96. <strong>Cloze Test :</strong><br>Good taste is not always ___(96)___ and most people manage to get ___(97)___ without it. It is fortunate, therefore, that so many of the ___(98)___ of life are now chosen for us by ___(99)___ Even among luxuries, the margin for ___(100)___ taste is constantly being reduced.<br>Select the appropriate option to fill in the blank number 96.</p>",
                    options_en: [
                        "<p>genuine</p>",
                        "<p>laboured</p>",
                        "<p>impulsive</p>",
                        "<p>inborn</p>"
                    ],
                    options_hi: [
                        "<p>genuine</p>",
                        "<p>laboured</p>",
                        "<p>impulsive</p>",
                        "<p>inborn</p>"
                    ],
                    solution_en: "<p>96.(d) inborn. <br>An &lsquo;inborn&rsquo; quality is one that we are born with. The given passage states that no one is born with good taste. Hence, &lsquo;inborn&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>96.(d) inborn. <br>एक &lsquo;inborn&rsquo;(\'जन्मजात\') गुण वो है जिसके साथ हम पैदा होते हैं। दिए गए passage में कहा गया है कि कोई भी अच्छे taste के साथ पैदा नहीं होता है। इसलिए,&lsquo;inborn&rsquo;(\'जन्मजात\') सबसे उपयुक्त उत्तर है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "97",
                    section: "7",
                    question_en: "<p>97. <strong>Cloze Test :</strong><br>Good taste is not always ___(96)___ and most people manage to get ___(97)___ without it. It is fortunate, therefore, that so many of the ___(98)___ of life are now chosen for us by ___(99)___ Even among luxuries, the margin for ___(100)___ taste is constantly being reduced.<br>Select the appropriate option to fill in the blank number 97.</p>",
                    question_hi: "<p>97. <strong>Cloze Test :</strong><br>Good taste is not always ___(96)___ and most people manage to get ___(97)___ without it. It is fortunate, therefore, that so many of the ___(98)___ of life are now chosen for us by ___(99)___ Even among luxuries, the margin for ___(100)___ taste is constantly being reduced.<br>Select the appropriate option to fill in the blank number 97.</p>",
                    options_en: [
                        "<p>ahead</p>",
                        "<p>along</p>",
                        "<p>through</p>",
                        "<p>around</p>"
                    ],
                    options_hi: [
                        "<p>ahead</p>",
                        "<p>along</p>",
                        "<p>through</p>",
                        "<p>around</p>"
                    ],
                    solution_en: "<p>97.(b) along. <br>The phrase &lsquo;get along&rsquo; means to manage to live or survive. The given passage states that no one is born with good taste &amp; people manage to live or survive without it. Hence, &lsquo;along&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>97.(b) along. <br>वाक्यांश (phrase) &lsquo;get along&rsquo; का अर्थ है - किसी के साथ मैत्रीपूर्ण संबंध रखना ताकि साथ रह सकें । दिए गए passage में कहा गया है कि कोई भी अच्छे taste के साथ पैदा नहीं होता है और लोग इसके बिना जीने या जीवित रहने का प्रबंधन करते हैं। इसलिए, &lsquo;along&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "98",
                    section: "7",
                    question_en: "<p>98. <strong>Cloze Test :</strong><br>Good taste is not always ___(96)___ and most people manage to get ___(97)___ without it. It is fortunate, therefore, that so many of the ___(98)___ of life are now chosen for us by ___(99)___ Even among luxuries, the margin for ___(100)___ taste is constantly being reduced.<br>Select the appropriate option to fill in the blank number 98.</p>",
                    question_hi: "<p>98. <strong>Cloze Test :</strong><br>Good taste is not always ___(96)___ and most people manage to get ___(97)___ without it. It is fortunate, therefore, that so many of the ___(98)___ of life are now chosen for us by ___(99)___ Even among luxuries, the margin for ___(100)___ taste is constantly being reduced.<br>Select the appropriate option to fill in the blank number 98.</p>",
                    options_en: [
                        "<p>amenities</p>",
                        "<p>comforts</p>",
                        "<p>luxuries</p>",
                        "<p>necessities</p>"
                    ],
                    options_hi: [
                        "<p>amenities</p>",
                        "<p>comforts</p>",
                        "<p>luxuries</p>",
                        "<p>necessities</p>"
                    ],
                    solution_en: "<p>98.(d) necessities. <br>&lsquo;Necessities&rsquo; means the need for something, the fact that something must be done or must happen. The given passage talks about the necessities of life. Hence, &lsquo;necessities&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>98.(d) necessities. <br>&lsquo;Necessities&rsquo; का मतलब है - आवश्यक जरूरतें। दिया गया passage जीवन की आवश्यकताओं के बारे में बात करता है। इसलिए, &lsquo;Necessities&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "99",
                    section: "7",
                    question_en: "<p>99. <strong>Cloze Test :</strong><br>Good taste is not always ___(96)___ and most people manage to get ___(97)___ without it. It is fortunate, therefore, that so many of the ___(98)___ of life are now chosen for us by ___(99)___ Even among luxuries, the margin for ___(100)___ taste is constantly being reduced.<br>Select the appropriate option to fill in the blank number 99.</p>",
                    question_hi: "<p>99. <strong>Cloze Test :</strong><br>Good taste is not always ___(96)___ and most people manage to get ___(97)___ without it. It is fortunate, therefore, that so many of the ___(98)___ of life are now chosen for us by ___(99)___ Even among luxuries, the margin for ___(100)___ taste is constantly being reduced.<br>Select the appropriate option to fill in the blank number 99.</p>",
                    options_en: [
                        "<p>connoisseurs</p>",
                        "<p>artists</p>",
                        "<p>experts</p>",
                        "<p>scientists</p>"
                    ],
                    options_hi: [
                        "<p>connoisseurs</p>",
                        "<p>artists</p>",
                        "<p>experts</p>",
                        "<p>scientists</p>"
                    ],
                    solution_en: "<p>99.(c) experts. <br>&lsquo;Expert&rsquo; is a person who has a lot of special knowledge or skill. The given passage talks about the necessities of life that are now chosen for us by experts. Hence, &lsquo;expert&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>99.(c) experts. <br>&lsquo;Expert&rsquo; एक ऐसा व्यक्ति है जिसके पास बहुत अधिक विशेष ज्ञान या कौशल है। दिया गया passage जीवन की उन आवश्यकताओं के बारे में बात करता है जो &lsquo;Experts&rsquo; द्वारा हमारे लिए चुनी गई हैं। इसलिए, &lsquo;Expert&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "100",
                    section: "misc",
                    question_en: "<p>100.<strong>Cloze Test :</strong><br>Good taste is not always ___(96)___ and most people manage to get ___(97)___ without it. It is fortunate, therefore, that so many of the ___(98)___ of life are now chosen for us by ___(99)___ Even among luxuries, the margin for ___(100)___ taste is constantly being reduced.<br>Select the appropriate option to fill in the blank number 100.</p>",
                    question_hi: "<p>100. <strong>Cloze Test :</strong><br>Good taste is not always ___(96)___ and most people manage to get ___(97)___ without it. It is fortunate, therefore, that so many of the ___(98)___ of life are now chosen for us by ___(99)___ Even among luxuries, the margin for ___(100)___ taste is constantly being reduced.<br>Select the appropriate option to fill in the blank number 100.</p>",
                    options_en: [
                        "<p>local</p>",
                        "<p>particular</p>",
                        "<p>impersonal</p>",
                        "<p>personal</p>"
                    ],
                    options_hi: [
                        "<p>local</p>",
                        "<p>particular</p>",
                        "<p>impersonal</p>",
                        "<p>personal</p>"
                    ],
                    solution_en: "<p>100.(d) personal. <br>&lsquo;Personal&rsquo; means of or belonging to one particular person. The given passage states that the margin for personal taste is constantly being reduced. Hence, &lsquo;personal&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>100.(d) personal. <br>&lsquo;Personal&rsquo; का अर्थ है- किसी एक व्यक्ति विशेष से या उससे संबंधित। दिया गया passage बताता है कि व्यक्तिगत taste को लगातार कम किया जा रहा है। इसलिए, &lsquo;personal&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'question-box d-flex align-items-center justify-content-center border rounded';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.question-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            const html = `
                <div class="card shadow-sm mb-4">
                    <div class="card-body">
                        <div class="question-text mb-4">
                            <div class="en">${question.question_en}</div>
                            <div class="hi" style="display:none">${question.question_hi}</div>
                        </div>
                        <div class="options">
                            ${['a', 'b', 'c', 'd'].map((opt, i) => `
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="radio" name="q${index}" 
                                        id="opt-${index}-${opt}" value="${opt}" 
                                        ${answers[question.id] === opt ? 'checked' : ''}
                                        onclick="selectAnswer('${question.id}', '${opt}')">
                                    <label class="form-check-label w-100 py-2 px-3 rounded hover-bg-light" for="opt-${index}-${opt}">
                                        <div class="en">${question.options_en[i]}</div>
                                        <div class="hi" style="display:none">${question.options_hi[i]}</div>
                                    </label>
                                </div>
                            `).join('')}
                        </div>
                        ${submitted ? `
                            <div class="alert ${answers[question.id] === question.correct ? 'alert-success' : 'alert-danger'} mt-3">
                                ${answers[question.id] ? (answers[question.id] === question.correct ? 'Correct!' : 'Incorrect!') : 'Not attempted'}
                                <div class="mt-2">
                                    <strong>Solution:</strong>
                                    <div class="en">${question.solution_en}</div>
                                    <div class="hi" style="display:none">${question.solution_hi}</div>
                                </div>
                            </div>
                        ` : ''}
                    </div>
                </div>
            `;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // If in mobile view, close the menu
            if (window.innerWidth < 768) {
                toggleMenu(false);
            }
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                document.getElementById(`box-${index}`).classList.add('attempted');
            }
        }
        // Navigate to previous question
        function prevQuestion() {
            if (currentQuestion > 0) {
                showQuestion(currentQuestion - 1);
            }
        }
        // Navigate to next question
        function nextQuestion() {
            if (currentQuestion < questions.length - 1) {
                showQuestion(currentQuestion + 1);
            }
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) {
                showQuestion(section.start);
            }
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update button text
            document.getElementById('lang-btn').innerHTML = currentLang === 'en' ? 
                '<i class="fas fa-language"></i> हिंदी' : 
                '<i class="fas fa-language"></i> English';
        }
        // Toggle menu for mobile
        function toggleMenu(show) {
            const nav = document.getElementById('question-nav');
            const content = document.getElementById('question-content');
            if (show === undefined) {
                show = nav.style.right === '-100%' || nav.style.right === '';
            }
            if (show) {
                nav.style.right = '0';
                if (window.innerWidth >= 768) {
                    content.style.marginRight = '320px';
                }
            } else {
                nav.style.right = window.innerWidth < 768 ? '-100%' : '0';
                content.style.marginRight = window.innerWidth < 768 ? '0' : '320px';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
            document.getElementById('total-count').textContent = questions.length;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.remove('attempted');
                    box.classList.add('bg-secondary', 'text-white');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.remove('attempted');
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.remove('attempted');
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            resultsModal.show();
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check-circle me-1"></i> Submitted';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            resultsModal.hide();
            showQuestion(0);
        }
    </script>
</body>
</html>