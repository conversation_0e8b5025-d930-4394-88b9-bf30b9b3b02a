<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">10:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 10</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">10</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 10 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["31"] = {
                name: "Computer Knowledge",
                start: 0,
                end: 8
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="31">Computer Knowledge</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 9,
                end: 9
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "31",
                    question_en: "<p>1. In MS Excel 365 what is the cell address of the cell two columns to the right and three&nbsp;rows down from cell D7 ?</p>",
                    question_hi: "<p>1. एमएस एक्सेल 365 (MS Excel 365) में, सेल D7 से दायीं ओर दो कॉलम और तीन रो (rows) नीचे&nbsp;वाले सेल का सेल एड्रेस क्या होगा?</p>",
                    options_en: ["<p>F9</p>", "<p>E9</p>", 
                                "<p>G10</p>", "<p>F10</p>"],
                    options_hi: ["<p>F9</p>", "<p>E9</p>",
                                "<p>G10</p>", "<p>F10</p>"],
                    solution_en: "<p>1.(d) <strong>F10.</strong> ROW &ndash; A row is a horizontal line of cells. COLUMN &ndash; A column is a vertical line of cells. Excel 2007 onward (2010, 2016, etc.) has a total of 1,048,576 rows and 16,384 columns.</p>",
                    solution_hi: "<p>1.(d) <strong>F10.</strong> रो (ROW) - रो (row) सेल की एक क्षैतिज रेखा (horizontal line) है। COLUMN - कॉलम, सेल्स (cells) की एक ऊर्ध्वाधर रेखा (vertical line) है। एक्सेल 2007 और उनके बाद के संस्करण (2010, 2016, आदि) में कुल 1,048,576 रो और 16,384 कॉलम हैं।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "2",
                    section: "31",
                    question_en: "<p>2. Where is the position of the menu bar in accordance with the title bar in MS word 365?</p>",
                    question_hi: "<p>2. एमएस वर्ड 365 (MS Word 365) में, टाइटल बार के संदर्भ में मेनू बार की स्थिति कहां होती है?</p>",
                    options_en: ["<p>Menu bar is situated below the title bar.</p>", "<p>Menu bar is situated above the title bar.</p>", 
                                "<p>Menu bar is situated to the left of the title bar.</p>", "<p>Menu bar is situated to the right of the title bar.</p>"],
                    options_hi: ["<p>मेनू बार, टाइटल बार के नीचे स्थित होता है।</p>", "<p>मेनू बार, टाइटल बार के ऊपर स्थित होता है।</p>",
                                "<p>मेनू बार, टाइटल बार के बाई ओर स्थित होता है।</p>", "<p>मेनू बार, टाइटल बार के दाई ओर स्थित होता है।</p>"],
                    solution_en: "<p>2.(a) In MS Word 365, the title bar appears at the very top of the application window and displays the document name. Following that, directly beneath the title bar, is the menu bar. The menu bar contains all the various menus and options we can use to format, edit, and manipulate your document content.</p>",
                    solution_hi: "<p>2.(a) एमएस वर्ड 365 में, टाइटल बार एप्लिकेशन विंडो के सबसे ऊपर दिखाई देता है और दस्तावेज़ का नाम प्रदर्शित करता है। उसके बाद, टाइटल बार के ठीक नीचे, मेनू बार है। मेनू बार में सभी विभिन्न मेनू और विकल्प होते हैं जिनका उपयोग हम आपके दस्तावेज़ सामग्री को प्रारूपित (format) करने, संपादित (edit) करने और हेरफेर (manipulate) करने के लिए कर सकते हैं।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "3",
                    section: "31",
                    question_en: "<p>3. In MS Excel 365 what is the Microsoft Excel formula to create an absolute cell reference for cell B2 ?</p>",
                    question_hi: "<p>3. एमएस एक्सेल 365 (MS Excel 365) में, सेल B2 के लिए एब्सोल्यूट सेल रेफ़रेंस बनाने के लिए माइक्रोसॉफ्ट एक्सेल फ्रॉर्मूला क्या है?</p>",
                    options_en: ["<p>B2</p>", "<p>$B$2</p>", 
                                "<p>$B2</p>", "<p>B$2</p>"],
                    options_hi: ["<p>B2</p>", "<p>$B$2</p>",
                                "<p>$B2</p>", "<p>B$2</p>"],
                    solution_en: "<p>3.(b) <strong>$B$2.</strong> Absolute cell reference is a method of writing a formula in a spreadsheet document so copying that formula to another cell does not change the cell its formula references. Relative cell references are basic cell references that adjust and change when copied or when using AutoFill.</p>",
                    solution_hi: "<p>3.(b) <strong>$B$2.</strong> एब्सोल्यूट सेल रिफरेन्स (Absolute cell reference) एक स्प्रेडशीट डॉक्यूमेंट में सूत्र (formula) लिखने की एक विधि है, इसलिए उस सूत्र को किसी अन्य सेल में कॉपी करने से सेल के सूत्र संदर्भ नहीं बदलते हैं। रिलेटिव सेल रिफरेन्स (Relative cell references) बेसिक सेल संदर्भ (basic cell references) हैं जो कॉपी किए जाने पर या ऑटोफ़िल (AutoFill) का उपयोग करते समय समायोजित (adjust) और बदलते हैं।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "4",
                    section: "31",
                    question_en: "<p>4. In Microsoft Excel 365, how do you start to write a formula?</p>",
                    question_hi: "<p>4. एमएस एक्सेल 365 (MS Excel 365) में, आप फॉर्मूला लिखना किस प्रकार शुरू करते हैं?</p>",
                    options_en: ["<p>By typing =</p>", "<p>By typing ;</p>", 
                                "<p>By typing /</p>", "<p>By typing :</p>"],
                    options_hi: ["<p>= टाइप करके</p>", "<p>; टाइप करके</p>",
                                "<p>/ टाइप करके</p>", "<p>: टाइप करके</p>"],
                    solution_en: "<p>4.(a) <strong>By typing =</strong> <strong>.</strong> Symbols of Excel formulas: Range (:), Intersection (&lt; space &gt;), Union (,), Concatenation (&amp;), Not equal to (&lt;&gt;).</p>",
                    solution_hi: "<p>4.(a) <strong>= टाइप करके। </strong>Excel सूत्रों के प्रतीक: रेंज (Range) (:), इंटरसेक्शन (Intersection) (&lt;स्पेस&gt;), यूनियन (,), कान्कैटनेशन (&amp;), (&lt;&gt;) बराबर (equal) नहीं है।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "5",
                    section: "31",
                    question_en: "<p>5. Which of the following is an Internet accessing technique where computer uses its&nbsp;_____ modem to dial a telephone number given to the user by an Internet Service Provider?</p>",
                    question_hi: "<p>5. निम्नलिखित में से कौन-सा विकल्प एक ऐसी इंटरनेट एक्सेसिंग तकनीक है जिसमें इंटरनेट सर्विस&nbsp;प्रोवाइडर द्वारा यूज़र को दिए गए टेलीफोन नंबर को डायल करने के लिए कंप्यूटर द्वारा इसके मॉडेम का&nbsp;उपयोग किया जाता है?</p>",
                    options_en: ["<p>Digital Subscriber Line</p>", "<p>Dial up Connection</p>", 
                                "<p>Broadband Connection</p>", "<p>Integrated Services Digital Network (ISDN) Service</p>"],
                    options_hi: ["<p>डिजिटल सब्सक्राइबर लाइन</p>", "<p>डायल अप कनेक्शन</p>",
                                "<p>ब्रॉडबैंड कनेक्शन</p>", "<p>इंटीग्रेटेड सर्विसेज डिजिटल नेटवर्क (ISDN) सर्विस</p>"],
                    solution_en: "<p>5.(b) <strong>Dial up Connection. </strong>Digital Subscriber Line (DSL), digital subscriber loop is a communication medium, which is used to transfer the internet through copper wire telecommunication lines. Broadband connection refers to transmission of wide bandwidth data over a high speed internet connection. ISDN is a set of protocols that is based on high-speed fully digitized telephone service.</p>",
                    solution_hi: "<p>5.(b) <strong>डायल अप कनेक्शन।</strong> डिजिटल सब्सक्राइबर लाइन (DSL), डिजिटल सब्सक्राइबर लूप एक संचार माध्यम है, जिसका उपयोग तांबे के तार दूरसंचार लाइनों के माध्यम से इंटरनेट स्थानांतरित करने के लिए किया जाता है। ब्रॉडबैंड कनेक्शन (Broadband connection) का तात्पर्य हाई स्पीड इंटरनेट कनेक्शन पर वाइड बैंडविड्थ डेटा (wide bandwidth data) के प्रसारण से है। ISDN प्रोटोकॉल का एक सेट है जो हाई-स्पीड पूर्णतया डिजीटल टेलीफोन सेवा पर आधारित है।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "6",
                    section: "31",
                    question_en: "<p>6. In MS-Word 365, which action allows you to replace a specific word or phrase&nbsp;throughout the entire document?</p>",
                    question_hi: "<p>6. MS-Word 365 में निम्नलिखित में से कौन-सी कार्रवाई से आप संपूर्ण डॉक्यूमेंट में किसी विशिष्ट शब्द या वाक्यांश को प्रतिस्थापित कर सकते हैं?</p>",
                    options_en: [" Cut ", " Copy ", 
                                "<p>Paste</p>", "<p>Find and Replace</p>"],
                    options_hi: ["<p>कट</p>", "<p>कॉपी</p>",
                                "<p>पेस्ट</p>", "<p>फाइंड एंड रिप्लेस</p>"],
                    solution_en: "<p>6.(d) <strong>Find and Replace</strong> (Ctrl + H). Shortcut keys: Cut - Ctrl + X, Copy - Ctrl + C, Paste - Ctrl + V.</p>",
                    solution_hi: "<p>6.(d) <strong>फाइंड एंड रिप्लेस</strong> (Ctrl + H). शॉर्टकट कुंजियाँ (Shortcut keys): कट (Cut) - Ctrl + X, कॉपी (Copy) - Ctrl + C, पेस्ट (Paste) - Ctrl + V.</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "7",
                    section: "31",
                    question_en: "<p>7. In MS-Word 365, what is the primary difference between the \'Save\' and \'Save As\'&nbsp;commands?</p>",
                    question_hi: "<p>7. एमएस वर्ड 365 (MS Word 365) में &lsquo;Save\' और \'Save As\' कमांड के बीच मुख्य अंतर क्या है?</p>",
                    options_en: ["<p>\'Save\' is used to save the document with a new filename or in a different location,&nbsp;while \'Save As\' is used to save the document for the first time.</p>", "<p>\'Save\' is used to save the document in the cloud storage, while \'Save As\' is used to&nbsp;save the document on the local computer.</p>", 
                                "<p>\'Save\' is used to save the document for the first time, while \'Save As\' is used to save&nbsp;the document with a new filename or in a different location.</p>", "<p>Both \'Save\' and \'Save As\' perform the same function of saving the document; they&nbsp;are just two different names for the same command.</p>"],
                    options_hi: ["<p>\'Save का उपपोग डॉक्यूमेंट को नए फ़ाइल नाम के साथ या किसी भिन्न स्थान पर सेव करने के लिए&nbsp;किया जाता है, जबकि \'Save As\' का उपयोग डॉक्यूमेंट को पहली बार सेव करने के लिए किया जाता है।</p>", "<p>\'Save का उपयोग डॉक्यूमेंट को क्लाउड स्टोरेज में सेव करने के लिए किया जाता है, जबकि \'Save&nbsp;As\' का उपयोग डॉक्यूमेंट को स्थानीय कंप्यूटर पर सेव करने के लिए किया जाता है।</p>",
                                "<p>\'Save\' का उपयोग डॉक्यूमेंट को पहली बार सेव करने के लिए किया जाता है, जबकि \'Save As\' का&nbsp;उपयोग डॉक्यूमेंट को नए फ़ाइल नाम या किसी भिन्न स्थान पर सेव करने के लिए किया जाता है।</p>", "<p>\'Save\' और \'Save As\' दोनों का उपयोग डॉक्यूमेंट को सेव करने के लिए किया जाता है; वे एक ही&nbsp;कमांड के दो अलग-अलग नाम हैं।</p>"],
                    solution_en: "<p>7.(c) We use \'Save\' for quick saves of our ongoing work on the same document. We use \'Save As\' when we need more control over the filename, location, or file format of our saved document.</p>",
                    solution_hi: "<p>7.(c) हम एक ही दस्तावेज़ पर अपने चल रहे कार्य को शीघ्र रूप से सहेजने के लिए \'Save\' का उपयोग करते हैं। जब हमें अपने सहेजे गए दस्तावेज़ के फ़ाइल नाम, स्थान या फ़ाइल प्रारूप पर अधिक नियंत्रण की आवश्यकता होती है तो हम \'Save As\' का उपयोग करते हैं।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "8",
                    section: "31",
                    question_en: "<p>8. Which option in Page Setup of MS word 365 determines whether the content of the&nbsp;document is displayed vertically or horizontally?</p>",
                    question_hi: "<p>8. एमएस वर्ड 365 (MS Word 365) के पेज सेटअप (Page Setup) में कौन-सा ऑप्शन यह निर्धारित&nbsp;करता है कि डॉक्यूमेंट का कंटेंट लंबवत (vertically) या क्षैतिज (horizontally) रूप से प्रदर्शित होगा?</p>",
                    options_en: ["<p>Columns</p>", "<p>Paper Size</p>", 
                                "<p>Margins</p>", "<p>Orientation</p>"],
                    options_hi: ["<p>कॉलम्स (Columns)</p>", "<p>पेपर साइज़ (Paper Size)</p>",
                                "<p>मार्जिन्स (Margins)</p>", "<p>ओरिएंटेशन (Orientation)</p>"],
                    solution_en: "<p>8.(d) <strong>Orientation.</strong> In MS Excel, there are two types of page orientation: Portrait and Landscape. The portrait page orientation is used to print tall pages. In portrait orientation, information is printed parallel to the page\'s short edge. Landscape page orientation is used to print wide pages.</p>",
                    solution_hi: "<p>8.(d) <strong>ओरिएंटेशन </strong>(Orientation)। MS Excel में, पेज ओरिएंटेशन (page orientation) दो प्रकार के होते हैं: पोर्ट्रेट और लैंडस्केप। पोर्ट्रेट पेज ओरिएंटेशन (portrait page orientation) का उपयोग लंबे पेजों (tall pages) को प्रिंट करने के लिए किया जाता है। पोर्ट्रेट ओरिएंटेशन (portrait orientation) में, इनफार्मेशन पेज के छोटे किनारे (short edge) के समानांतर मुद्रित (parallel printed) होती है। लैंडस्केप पेज ओरिएंटेशन (Landscape page orientation) का उपयोग विस्तृत पेजों (wide pages) को प्रिंट करने के लिए किया जाता है।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "9",
                    section: "31",
                    question_en: "<p>9. Which of the following is an effective method to handle SPAM emails?</p>",
                    question_hi: "<p>9. स्पैम ईमेल को हैंडल करने के लिए निम्नलिखित में से कौन-सा एक प्रभावी तरीका है?</p>",
                    options_en: ["<p>Forward the email to all your contacts</p>", "<p>Mark the email as SPAM or Junk</p>", 
                                "<p>Unsubscribe from all mailing lists</p>", "<p>Reply to the sender asking them to stop</p>"],
                    options_hi: ["<p>अपने सभी संपर्कों को ईमेल फ़ॉरवर्ड करना</p>", "<p>ईमेल को स्पैम या जंक के रूप में चिह्नित करना</p>",
                                "<p>सभी मेलिंग सूचियों से सदस्यता समाप्त करना</p>", "<p>प्रेषक को उत्तर देकर उन्हें रुकने के लिए कहना</p>"],
                    solution_en: "<p>9.(b) <strong>Mark the email as SPAM or Junk.</strong> Spam email is unsolicited and unwanted junk email sent out in bulk to an indiscriminate recipient list. Typically, spam is sent for commercial purposes. It can be sent in massive volume by botnets, networks of infected computers.</p>",
                    solution_hi: "<p>9.(b) <strong>ईमेल को स्पैम या जंक के रूप में चिह्नित करना । </strong>स्पैम ईमेल (Spam email) अनसोलिसिटेड (unsolicited) और अनवांटेड जंक (unwanted junk) ईमेल है जो विशृंखल प्राप्तकर्ता सूची (indiscriminate recipient list) में बल्क (bulk) मात्रा में भेजा जाता है। आमतौर पर, स्पैम (spam) व्यावसायिक उद्देश्यों के लिए भेजा जाता है। इसे बॉटनेट (botnets), इन्फेक्टिड कंप्यूटरों के नेटवर्क द्वारा अधिक मात्रा (massive volume) में भेजा जा सकता है।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "10",
                    section: "misc",
                    question_en: "<p>10. Which of the following is the most appropriate option to print pages 1, 3, 5 and so on&nbsp;in MS Word 365?</p>",
                    question_hi: "<p>10. एमएस वर्ड 365 (MS Word 365) में, पेज 1, 3, 5 और इसी प्रकार आगे भी, पेजों को प्रिंट करने के लिएनिम्नलिखित में से कौन-सा ऑप्शन सबसे उपयुक्त है?</p>",
                    options_en: ["<p>Print Odd pages</p>", "<p>Print</p>", 
                                "<p>Print Even pages</p>", "<p>Print all</p>"],
                    options_hi: ["<p>प्रिंट ओड पेजेज (Print Odd pages)</p>", "<p>प्रिंट (Print)</p>",
                                "<p>प्रिंट इवन पेजेज (Print Even pages)</p>", "<p>प्रिंट ऑल (Print all)</p>"],
                    solution_en: "<p>10.(a) <strong>Print Odd pages.</strong> Print Even pages: This would print only the even pages (e.g., 2, 4, 6). Print All : This would print all pages of the document. custom pages print : To print pages (1-3) or use hyphens for page ranges (e.g., 2-4).</p>",
                    solution_hi: "<p>10.(a) <strong>प्रिंट ओड पेजेज</strong> <strong>(Print Odd pages)।</strong> प्रिंट इवन पेजेज (Print Even pages): यह केवल इवन पेज (even pages) प्रिंट करेगा (जैसे, 2, 4, 6)। प्रिंट ऑल (Print All): यह डॉक्यूमेंट के सभी पेजेज को प्रिंट करता है। कस्टम पेज प्रिंट (custom pages print) : पेजों को प्रिंट करने के लिए (1-3) या पेज रेंज के लिए हाइफ़न का उपयोग करें (उदाहरण के लिए, 2-4)।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>