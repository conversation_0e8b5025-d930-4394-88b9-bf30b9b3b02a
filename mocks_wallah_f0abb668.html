<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #7209b7;
            --secondary: #4361ee;
            --accent: #f72585;
            --success: #06d6a0;
            --warning: #ffd166;
            --danger: #ef476f;
            --light: #f8f9fa;
            --dark: #212529;
        }
        body {
            background-color: #f0f2f5;
            font-size: 16px;
            padding-bottom: 60px; /* Space for fixed navigation */
        }
        .navbar {
            background: linear-gradient(135deg, var(--primary), var(--secondary)) !important;
            box-shadow: 0 2px 10px rgba(0,0,0,.1);
            padding: 0.5rem 1rem;
        }
        .navbar-brand {
            font-size: 1.1rem;
            margin-right: 0;
        }
        .navbar .container-fluid {
            justify-content: space-between;
        }
        .controls-group {
            display: flex;
            align-items: center;
            flex-wrap: nowrap;
        }
        .timer-display {
            background: linear-gradient(135deg, #4361ee, #3a0ca3);
            color: white;
            border-radius: 50px;
            box-shadow: 0 3px 8px rgba(0,0,0,.1);
            padding: 0.25rem 0.75rem;
            font-size: 0.875rem;
            white-space: nowrap;
        }
        .nav-btn {
            padding: 0.25rem 0.75rem;
            font-size: 0.875rem;
            margin-left: 0.25rem;
        }
        .question-nav {
            position: fixed;
            top: 56px;
            right: 0;
            height: calc(100vh - 56px);
            width: 280px;
            transform: translateX(280px);
            transition: transform .3s;
            z-index: 100;
            background: #fff;
            box-shadow: -5px 0 15px rgba(0,0,0,.1);
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .main-content {
            transition: margin .3s;
            min-height: calc(100vh - 116px);
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.5;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(32px, 1fr));
            gap: 6px;
        }
        .q-box {
            width: 35px;
            height: 35px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 5px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid #dee2e6;
            background: #fff;
            transition: all .2s;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: 0 3px 5px rgba(0,0,0,.1);
        }
        .q-box.attempted {
            background-color: var(--secondary);
            color: white;
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,.15);
        }
        .card {
            border-radius: 12px;
            overflow: hidden;
            border: none;
            box-shadow: 0 4px 12px rgba(0,0,0,.08);
        }
        .option-label {
            border-radius: 8px;
            transition: all .2s;
            border: 1px solid #dee2e6;
            padding: 0.75rem 1rem;
            font-size: 1rem;
        }
        .option-label:hover {
            background-color: #f8f9fa;
            transform: translateY(-2px);
            box-shadow: 0 3px 8px rgba(0,0,0,.05);
        }
        .form-check-input:checked + .option-label {
            border-color: var(--primary);
            background-color: rgba(114,9,183,.05);
        }
        .btn-primary {
            background-color: var(--primary);
            border-color: var(--primary);
        }
        .btn-outline-primary {
            color: var(--primary);
            border-color: var(--primary);
        }
        .btn-outline-primary:hover {
            background-color: var(--primary);
            color: white;
        }
        /* Bottom fixed navigation for mobile */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: white;
            box-shadow: 0 -2px 10px rgba(0,0,0,.1);
            padding: 10px 15px;
            display: flex;
            justify-content: space-between;
            z-index: 90;
        }
        @media (min-width: 992px) {
            .main-content.with-nav {
                margin-right: 280px;
            }
            .question-nav {
                transform: translateX(0);
            }
            .nav-toggle-btn {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(30px, 1fr));
            }
            .option-label {
                padding: 0.625rem 0.875rem;
                font-size: 0.95rem;
            }
            .question-text {
                font-size: 1rem;
            }
            .navbar-brand {
                font-size: 1rem;
            }
            .timer-display {
                font-size: 0.8125rem;
                padding: 0.2rem 0.6rem;
            }
            .nav-btn {
                padding: 0.2rem 0.6rem;
                font-size: 0.8125rem;
            }
            .container {
                padding-left: 10px;
                padding-right: 10px;
            }
        }
    </style>
</head>
<body>
    <!-- Header navbar -->
    <nav class="navbar navbar-dark sticky-top">
        <div class="container-fluid px-2">
            <a class="navbar-brand fw-bold" href="#"><i class="fas fa-graduation-cap me-1"></i></a>
            <div class="controls-group">
                <!-- Timer -->
                <div class="timer-display me-1 fw-bold" id="timer">
                    <i class="far fa-clock me-1"></i><span id="timer-display">90:00</span>
                </div>
                <!-- Submit button -->
                <button class="btn btn-light btn-sm nav-btn me-1" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Language toggle -->
                <button class="btn btn-light btn-sm nav-btn me-1" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="btn btn-light btn-sm nav-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </nav>
    <!-- Main content -->
    <div class="main-content" id="main-content">
        <div class="container py-3">
            <!-- Question counter -->
            <div class="fw-bold text-center mb-3" id="question-counter">Question 1 of 25</div>
            <!-- Questions will be displayed here -->
            <div id="questions-container"></div>
        </div>
    </div>
    <!-- Bottom navigation for mobile -->
    <div class="bottom-nav d-lg-none">
        <button class="btn btn-outline-primary px-3" onclick="prevQuestion()">
            <i class="fas fa-chevron-left me-1"></i> Previous
        </button>
        <button class="btn btn-outline-primary px-3" onclick="nextQuestion()">
            Next <i class="fas fa-chevron-right ms-1"></i>
        </button>
    </div>
    <!-- Question navigation sidebar -->
    <div class="question-nav" id="question-nav">
        <div class="p-3">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h5 class="m-0">Questions</h5>
                <div>
                    <span class="badge bg-secondary me-1" id="attempted-count">0</span>
                    <span class="badge bg-secondary" id="total-count">25</span>
                    <button class="btn btn-sm btn-outline-secondary ms-2 d-lg-none" onclick="toggleNav()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
            <!-- Section selector moved here -->
            <div class="mb-3">
                <label for="section-selector" class="form-label small text-muted">Jump to Section</label>
                <select class="form-select form-select-sm" id="section-selector" onchange="switchSection(this.value)">
                    <option value="all">All Sections</option>
                </select>
            </div>
            <div class="question-grid" id="question-boxes"></div>
        </div>
    </div>
    <!-- Results modal -->
    <div class="modal fade" id="results-modal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content border-0">
                <div class="modal-header border-0" style="background:linear-gradient(135deg,var(--primary),var(--secondary));color:white">
                    <h5 class="modal-title">Test Results</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body p-4">
                    <div class="row text-center g-2">
                        <div class="col-6">
                            <div class="p-3 rounded shadow-sm" style="background:linear-gradient(135deg,#4361ee,#3a0ca3);color:white">
                                <h3 id="score-value" class="mb-0 fw-bold">0</h3>
                                <div class="small">Score</div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="p-3 rounded shadow-sm" style="background:linear-gradient(135deg,#06d6a0,#1b9aaa);color:white">
                                <h3 id="correct-value" class="mb-0 fw-bold">0</h3>
                                <div class="small">Correct</div>
                            </div>
                        </div>
                        <div class="col-6 mt-2">
                            <div class="p-3 rounded shadow-sm" style="background:linear-gradient(135deg,#ef476f,#f72585);color:white">
                                <h3 id="incorrect-value" class="mb-0 fw-bold">0</h3>
                                <div class="small">Incorrect</div>
                            </div>
                        </div>
                        <div class="col-6 mt-2">
                            <div class="p-3 rounded shadow-sm" style="background:linear-gradient(135deg,#6c757d,#495057);color:white">
                                <h3 id="unattempted-value" class="mb-0 fw-bold">0</h3>
                                <div class="small">Unattempted</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer border-0">
                    <button type="button" class="btn btn-primary" onclick="reviewTest()">Review Test</button>
                </div>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 90 * 60;
        let resultsModal;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            resultsModal = new bootstrap.Modal(document.getElementById('results-modal'));
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["6"] = {
                name: "Reasoning",
                start: 0,
                end: 23
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="6">Reasoning</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 24,
                end: 24
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "6",
                    question_en: "<p>1. P and Q are husband and wife. P\'s father-in-law is R\'s wife\'s father. Q is the brother of T. How is P related to R\'s wife ?</p>\r\n<p>&nbsp;</p>\n",
                    question_hi: "<p>1. P &#2324;&#2352; Q &#2346;&#2340;&#2367; &#2324;&#2352; &#2346;&#2340;&#2381;&#2344;&#2368; &#2361;&#2376;&#2306;&#2404; P &#2325;&#2366; &#2360;&#2360;&#2369;&#2352;, R &#2325;&#2368; &#2346;&#2340;&#2381;&#2344;&#2368; &#2325;&#2366; &#2346;&#2367;&#2340;&#2366; &#2361;&#2376;&#2404; Q, T &#2325;&#2366; &#2349;&#2366;&#2312; &#2361;&#2376;&#2404; P, R &#2325;&#2368; &#2346;&#2340;&#2381;&#2344;&#2368; &#2360;&#2375; &#2325;&#2367;&#2360; &#2346;&#2381;&#2352;&#2325;&#2366;&#2352; &#2360;&#2306;&#2348;&#2306;&#2343;&#2367;&#2340; &#2361;&#2376;?</p>\n",
                    options_en: ["<p>Aunt <span style=\"font-family: Times New Roman;\"> </span></p>\n", "<p>Cousin</p>\n", 
                                "<p>Sister-in-law <span style=\"font-family: Times New Roman;\"> </span></p>\n", "<p>Brother</p>\n"],
                    options_hi: ["<p>&#2330;&#2366;&#2330;&#2368;</p>\n", "<p>&#2330;&#2330;&#2375;&#2352;&#2375; &#2349;&#2366;&#2312;</p>\n",
                                "<p>&#2349;&#2366;&#2349;&#2368;</p>\n", "<p>&#2349;&#2366;&#2312;</p>\n"],
                    solution_en: "<p>1.(c) According to the following family diagram, R\'s wife is the sister-in-law of P.</p>\r\n<p><img src=\"https://ssccglpinnacle.com/images/mceu_54026188111689046720395.png\"></p>\n",
                    solution_hi: "<p>1.(c) &#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340; &#2346;&#2352;&#2367;&#2357;&#2366;&#2352; &#2310;&#2352;&#2375;&#2326; &#2325;&#2375; &#2309;&#2344;&#2369;&#2360;&#2366;&#2352;, P, R &#2325;&#2368; &#2346;&#2340;&#2381;&#2344;&#2368; &#2325;&#2368; &#2349;&#2366;&#2349;&#2368; &#2361;&#2376;&#2404;</p>\r\n<p><img src=\"https://ssccglpinnacle.com/images/mceu_90466886411689046809261.png\"></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "6",
                    question_en: " <p>2. How many triangles are there in the given figure?</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646739263/word/media/image1.png\"/></p>",
                    question_hi: " <p>2. दी गई आकृति में कितने त्रिभुज हैं?</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646739263/word/media/image1.png\"/></p>",
                    options_en: [" <p> 11</span></p>", " <p> 14</span></p>", 
                                " <p> 12</span></p>", " <p> 13</span></p>"],
                    options_hi: [" <p> 11</span></p>", " <p> 14</span></p>",
                                " <p> 12</span></p>", " <p> 13</span></p>"],
                    solution_en: " <p>2.</span><span style=\"font-family:Times New Roman\">(c)</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646739263/word/media/image25.png\"/></p> <p><span style=\"font-family:Times New Roman\">This type of triangles consist of 3 triangles. In the upper half 3 such types of figures are available. </span></p> <p><span style=\"font-family:Times New Roman\">So,  number of triangles = 3 × 3 = 9</span></p> <p><span style=\"font-family:Times New Roman\">In the lower half  = 3 </span></p> <p><span style=\"font-family:Times New Roman\">So, total triangles = 9 + 3 = 12</span></p>",
                    solution_hi: " <p><span style=\"font-family:Roboto\">2.</span><span style=\"font-family:Times New Roman\">(c) </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646739263/word/media/image25.png\"/></p> <p><span style=\"font-family:Baloo\">इस प्रकार के त्रिभुजों में 3 त्रिभुज होते हैं। ऊपरी भाग में 3 प्रकार की आकृतियाँ उपलब्ध हैं। अत: त्रिभुजों की संख्या </span></p> <p><span style=\"font-family:Times New Roman\">= 3 × 3 = 9</span></p> <p><span style=\"font-family:Baloo\">निचले आधे भाग में = 3</span></p> <p><span style=\"font-family:Baloo\">अत: कुल त्रिभुज = 9 + 3 = 12</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "6",
                    question_en: "<p>3. Select the figure that will come next in place of question mark[?] in the following figure series.</p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646739263/word/media/image15.png\" width=\"400\" height=\"97\"></p>\n",
                    question_hi: "<p>3. &#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340; &#2310;&#2325;&#2371;&#2340;&#2367; &#2358;&#2381;&#2352;&#2371;&#2306;&#2326;&#2354;&#2366; &#2350;&#2375;&#2306; &#2346;&#2381;&#2352;&#2358;&#2381;&#2344;&#2357;&#2366;&#2330;&#2325; &#2330;&#2367;&#2361;&#2381;&#2344;[?] &#2325;&#2375; &#2360;&#2381;&#2341;&#2366;&#2344; &#2346;&#2352; &#2310;&#2327;&#2375; &#2310;&#2344;&#2375; &#2357;&#2366;&#2354;&#2368; &#2310;&#2325;&#2371;&#2340;&#2367; &#2325;&#2366; &#2330;&#2351;&#2344; &#2325;&#2352;&#2375;&#2306;&#2404;</p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646739263/word/media/image15.png\" width=\"371\" height=\"90\"></p>\n",
                    options_en: ["<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646739263/word/media/image12.png\" width=\"103\" height=\"87\"></p>\n", "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646739263/word/media/image7.png\" width=\"99\" height=\"101\"></p>\n", 
                                "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646739263/word/media/image22.png\" width=\"100\" height=\"102\"></p>\n", "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646739263/word/media/image11.png\" width=\"103\" height=\"83\"></p>\n"],
                    options_hi: ["<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646739263/word/media/image12.png\" width=\"100\" height=\"84\"></p>\n", "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646739263/word/media/image7.png\" width=\"98\" height=\"100\"></p>\n",
                                "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646739263/word/media/image22.png\" width=\"102\" height=\"105\"></p>\n", "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646739263/word/media/image11.png\" width=\"100\" height=\"81\"></p>\n"],
                    solution_en: "<p>3. <span style=\"font-family: Times New Roman;\">(b)</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646739263/word/media/image18.png\" width=\"98\" height=\"100\"></p>\n",
                    solution_hi: "<p><span style=\"font-family: Roboto;\">3. </span><span style=\"font-family: Times New Roman;\">(b)</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646739263/word/media/image18.png\" width=\"99\" height=\"101\"></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "6",
                    question_en: " <p>4. Select the word-pair in which the two words are related in the same way as are the two words in the following word-pair. </span></p> <p><span style=\"font-family:Times New Roman\">Spoon : Utensil </span></p>",
                    question_hi: " <p>4. उस शब्द-युग्म का चयन करें जिसमें दो शब्द उसी प्रकार संबंधित हैं जैसे निम्नलिखित शब्द-युग्म में दो शब्द हैं। </span></p> <p><span style=\"font-family:Baloo\">चम्मच : बर्तन </span></p>",
                    options_en: [" <p> Scooter : Vehicle </span></p>", " <p> Bus : Passenger </span></p>", 
                                " <p> House : Expenditure </span></p>", " <p> Kitchen : Cooking </span></p>"],
                    options_hi: [" <p> स्कूटर: वाहन</span></p>", " <p> बस: यात्री</span></p>",
                                " <p> घर : व्यय</span></p>", " <p> रसोई: पाक कला</span></p>"],
                    solution_en: " <p>4. </span><span style=\"font-family:Times New Roman\">(a) Spoon is a type of utensil. Similarly, Scooter is a type of vehicle.</span></p>",
                    solution_hi: " <p><span style=\"font-family:Roboto\">4. </span><span style=\"font-family:Baloo\">(a) चम्मच एक प्रकार का बर्तन है। इसी प्रकार स्कूटर एक प्रकार का वाहन है।</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "6",
                    question_en: " <p>5. Vijay is 8 years younger than Amit. The sum of the current ages of Vijay and Amit is 42 years. What is the current age of  Vijay? </span></p>",
                    question_hi: " <p>5. विजय, अमित से 8 वर्ष छोटा है। विजय और अमित की वर्तमान आयु का योग 42 वर्ष है। विजय की वर्तमान आयु क्या है?</span></p>",
                    options_en: [" <p> 17 years </span></p>", " <p> 25 years </span></p>", 
                                " <p> 21 years </span></p>", " <p> 15 years </span></p>"],
                    options_hi: [" <p> 17 वर्ष</span></p>", " <p> 25 वर्ष</span></p>",
                                " <p> 21 वर्ष</span></p>", " <p> 15 वर्ष</span></p>"],
                    solution_en: " <p>5.</span><span style=\"font-family:Times New Roman\">(a) Let Vijay’s age = V and Amit’s age = A</span></p> <p><span style=\"font-family:Times New Roman\">Now, According to the Question</span></p> <p><span style=\"font-family:Times New Roman\">A - V = 8 ………..(i)</span></p> <p><span style=\"font-family:Times New Roman\">V + A = 42  ……..(ii)</span></p> <p><span style=\"font-family:Times New Roman\">After solving the equations, we get</span></p> <p><span style=\"font-family:Times New Roman\">V = 17 years</span></p>",
                    solution_hi: " <p><span style=\"font-family:Roboto\">5.</span><span style=\"font-family:Baloo\">(a) माना विजय की आयु = V और अमित की आयु = A</span></p> <p><span style=\"font-family:Baloo\">अब, प्रश्न के अनुसार</span></p> <p><span style=\"font-family:Times New Roman\">A - V = 8 ………..(i)</span></p> <p><span style=\"font-family:Times New Roman\">V + A = 42  ……..(ii)</span></p> <p><span style=\"font-family:Baloo\">समीकरणों को हल करने के बाद, हम प्राप्त करते हैं</span></p> <p><span style=\"font-family:Baloo\">V = 17 वर्ष</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "6",
                    question_en: "<p>6. Which letter will be the face opposite to the one showing &lsquo;N&rsquo; when the given sheet is folded to form a cube?</p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646739263/word/media/image5.png\" /></p>",
                    question_hi: "<p>6. जब दी गई शीट को एक घन बनाने के लिए मोड़ा जाता है, तो कौन-सा अक्षर \'N\' दर्शाने वाले अक्षर के विपरीत होगा?</p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646739263/word/media/image5.png\" /></p>",
                    options_en: ["<p>H</p>", "<p>Z</p>", 
                                "<p>B</p>", "<p>E</p>"],
                    options_hi: ["<p>H</p>", "<p>Z</p>",
                                "<p>B</p>", "<p>E</p>"],
                    solution_en: "<p>6.<span style=\"font-family: Times New Roman;\">(d) When the paper will be folded, the opposite pairs will be </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Z&hArr;</span><span style=\"font-family: Times New Roman;\">W , B&hArr;</span><span style=\"font-family: Times New Roman;\">H and E&hArr;</span><span style=\"font-family: Times New Roman;\">N.</span></p>",
                    solution_hi: "<p><span style=\"font-family: Roboto;\">6.</span><span style=\"font-family: Baloo;\">(d) जब कागज को मोड़ा जाएगा, तो विपरीत जोड़े होंगे:-</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Z&hArr;</span><span style=\"font-family: Times New Roman;\">W , B&hArr;</span><span style=\"font-family: Times New Roman;\">H and E&hArr;</span><span style=\"font-family: Times New Roman;\">N.</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "6",
                    question_en: "<p>7. Read the given statements and conclusions carefully. Assuming that the information given in the statements is true, even if it appears to be at variance with commonly known facts, decide which of the given conclusions logically follow(s) from the statements.</p>\r\n<p><strong><span style=\"font-family: Times New Roman;\">Statements: </span></strong></p>\r\n<p><span style=\"font-family: Times New Roman;\">All camels are doves. </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Some doves are eagles. </span></p>\r\n<p><strong><span style=\"font-family: Times New Roman;\">Conclusions: </span></strong></p>\r\n<p><span style=\"font-family: Times New Roman;\">I. All</span><span style=\"font-family: Times New Roman;\"> camels are eagles. </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">II. Some camels are eagles. </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">III. Some eagles are doves. </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">IV. Some doves are camels. </span></p>\n",
                    question_hi: " <p>7. दिए गए कथनों और निष्कर्षों को ध्यानपूर्वक पढ़िए। यह मानते हुए कि कथनों में दी गई जानकारी सत्य है, भले ही वह सामान्य रूप से ज्ञात तथ्यों से भिन्न प्रतीत होती हो, तय करें कि दिए गए निष्कर्षों में से कौन सा कथनों का तार्किक रूप से अनुसरण करता है।</span></p> <p><span style=\"font-family:Baloo\">कथन: </span></p> <p><span style=\"font-family:Baloo\">सभी ऊंट कबूतर हैं।</span></p> <p><span style=\"font-family:Baloo\">कुछ कबूतर चील हैं।</span></p> <p><span style=\"font-family:Baloo\">निष्कर्ष:</span></p> <p><span style=\"font-family:Baloo\">I. सभी ऊंट चील हैं।</span></p> <p><span style=\"font-family:Baloo\">II. कुछ ऊंट चील हैं।</span></p> <p><span style=\"font-family:Baloo\">III. कुछ चील कबूतर हैं।</span></p> <p><span style=\"font-family:Baloo\">IV. कुछ कबूतर ऊंट हैं।</span></p>",
                    options_en: ["<p>Both conclusions II and III follow .</p>\n", "<p>Both conclusions III and IV follow .</p>\n", 
                                "<p>Both conclusions I and II follow .</p>\n", "<p>Both conclusions I and IV follow .</p>\n"],
                    options_hi: [" <p> निष्कर्ष II और III दोनों अनुसरण करते हैं।</span></p>", " <p> निष्कर्ष III और IV दोनों अनुसरण करते हैं।</span></p>",
                                " <p> निष्कर्ष I और II दोनों अनुसरण करते हैं।</span></p>", " <p> निष्कर्ष I और IV दोनों अनुसरण करते हैं।</span></p>"],
                    solution_en: "<p>7.<span style=\"font-family: Times New Roman;\">(b) According to the following venn diagram, Both conclusions III and IV follow.</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646739263/word/media/image9.png\"></p>\n",
                    solution_hi: " <p><span style=\"font-family:Roboto\">7.</span><span style=\"font-family:Baloo\">(b) निम्नलिखित वेन आरेख के अनुसार, निष्कर्ष III और IV दोनों अनुसरण करते हैं।</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646739263/word/media/image9.png\"/></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "6",
                    question_en: " <p>8. Select the option that is related to the third number in the same way as the second number is related to the first number. </span></p> <p><span style=\"font-family:Times New Roman\">25 : 750 : : 30 : ? </span></p>",
                    question_hi: " <p>8. उस विकल्प का चयन करें जो तीसरी संख्या से उसी प्रकार संबंधित है जैसे दूसरी संख्या पहली संख्या से संबंधित है।</span></p> <p><span style=\"font-family:Times New Roman\">25 : 750 : : 30 : ? </span></p>",
                    options_en: [" <p> 1110 </span></p>", " <p> 1275 </span></p>", 
                                " <p> 1625 </span></p>", " <p> 1050 </span></p>"],
                    options_hi: [" <p> 1110 </span></p>", " <p> 1275 </span></p>",
                                " <p> 1625 </span></p>", " <p> 1050 </span></p>"],
                    solution_en: " <p>8.</span><span style=\"font-family:Times New Roman\">(d) Logic :- {n : n(n+5) :: n : n(n+5)}</span></p> <p><span style=\"font-family:Cardo\">So, 30 : 30(30+5) → 30 :  1050</span></p>",
                    solution_hi: " <p>8.(d) तर्क : - {n : n(n+5) :: n : n(n+5)}</span></p> <p><span style=\"font-family:Arial Unicode MS\">इसलिए, 30 : 30(30+5) → 30 :  1050</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "6",
                    question_en: " <p>9. Which of the following combinations of words best represents the venn diagram given below?</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646739263/word/media/image19.png\"/></p>",
                    question_hi: " <p>9. निम्नलिखित में से कौन सा शब्द संयोजन नीचे दिए गए वेन आरेख का सबसे अच्छा प्रतिनिधित्व करता है?</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646739263/word/media/image19.png\"/></p>",
                    options_en: [" <p> Father , Doctor, Males</span></p>", " <p> Females, Males, Aunts</span></p>", 
                                " <p> Females, Daughters, sisters</span></p>", " <p> Mothers, Sisters, Brothers </span></p>"],
                    options_hi: [" <p> पिता, डॉक्टर, पुरुष</span></p>", " <p> मादा, नर, चाची</span></p>",
                                " <p> मादा, बेटियां, बहनें</span></p>", " <p> माताओं, बहनों, भाइयों</span></p>"],
                    solution_en: " <p>9.</span><span style=\"font-family:Times New Roman\">(c) </span><span style=\"font-family:Times New Roman\">As all Daughters and sisters are female. And all daughters might or might not be sisters. So, option c is correct</span></p> <p><span style=\"font-family:Times New Roman\">Trick: there is a trick in the question as, all sisters are daughters so the perfect answer for this would be a circle inside a circle and another circle inside that smaller circle.</span></p>",
                    solution_hi: " <p><span style=\"font-family:Roboto\">9.</span><span style=\"font-family:Baloo\">(c) चूंकि सभी बेटियां और बहनें महिलाएं हैं। और सभी बेटियां बहनें हो भी सकती हैं और नहीं भी। अत: विकल्प c सही है</span></p> <p><span style=\"font-family:Baloo\">ट्रिक: इस प्रश्न में एक ट्रिक है, क्योंकि सभी बहनें बेटियाँ हैं, इसलिए इसका सही उत्तर एक वृत्त के अंदर एक वृत्त और उस छोटे वृत्त के अंदर दूसरा वृत्त होगा।</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "6",
                    question_en: "<p>10. Select the combination of letters that when sequentially placed in the blanks of the given letter series will complete the series.</p>\r\n<p><span style=\"font-family: Times New Roman;\">a _ _ gb _ _ acegb _ _ aceg _ _ _ </span></p>",
                    question_hi: "<p>10. अक्षरों के उस संयोजन का चयन करें जिसे दी गई अक्षर श्रंखला के रिक्त स्थानों में क्रमिक रूप से रखने पर श्रंखला पूरी हो जाएगी।</p>\r\n<p><span style=\"font-family: Times New Roman;\">a _ _ gb _ _ acegb _ _ aceg _ _ _ </span></p>",
                    options_en: ["<p>dfdfbcedf <span style=\"font-family: Times New Roman;\"> </span></p>", "<p>cedfdfbdf</p>", 
                                "<p>fdfbdfced <span style=\"font-family: Times New Roman;\"> </span></p>", "<p>cefbdfdfd</p>"],
                    options_hi: ["<p>dfdfbcedf <span style=\"font-family: Times New Roman;\"> </span></p>", "<p>cedfdfbdf</p>",
                                "<p>fdfbdfced <span style=\"font-family: Times New Roman;\"> </span></p>", "<p>cefbdfdfd</p>"],
                    solution_en: "<p>10.<span style=\"font-family: Times New Roman;\">(b) The repetitive series is - &lsquo;</span><span style=\"font-family: Times New Roman;\">acegbdf</span><span style=\"font-family: Times New Roman;\">&rsquo;</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">a</span><strong><span style=\"font-family: Times New Roman;\">ce</span></strong><span style=\"font-family: Times New Roman;\">gb</span><strong><span style=\"font-family: Times New Roman;\">df</span></strong><span style=\"font-family: Times New Roman;\">acegb</span><strong><span style=\"font-family: Times New Roman;\">df</span></strong><span style=\"font-family: Times New Roman;\">aceg</span><strong><span style=\"font-family: Times New Roman;\">bdf</span></strong></p>\r\n<p><span style=\"font-family: Times New Roman;\">cedfdfbdf</span><span style=\"font-family: Times New Roman;\"> is the correct answer.</span></p>",
                    solution_hi: "<p><span style=\"font-family: Roboto;\">10.</span><span style=\"font-family: Baloo;\">(b) दोहराव वाली श्रृंखला है - \'acegbdf\' </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">a</span><strong><span style=\"font-family: Times New Roman;\">ce</span></strong><span style=\"font-family: Times New Roman;\">gb</span><strong><span style=\"font-family: Times New Roman;\">df</span></strong><span style=\"font-family: Times New Roman;\">acegb</span><strong><span style=\"font-family: Times New Roman;\">df</span></strong><span style=\"font-family: Times New Roman;\">aceg</span><strong><span style=\"font-family: Times New Roman;\">bdf</span></strong></p>\r\n<p><span style=\"font-family: Times New Roman;\">cedfdfbdf</span><span style=\"font-family: Baloo;\"> सही उत्तर है।</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "6",
                    question_en: " <p>11. Select the number from among the given options that can replace the question mark (?) in the following series. </span></p> <p><span style=\"font-family:Times New Roman\">2, 4, 5, 19, 71, ? </span></p>",
                    question_hi: " <p>11. दिए गए विकल्पों में से वह संख्या चुनिए जो निम्नलिखित श्रृंखला में प्रश्नवाचक चिन्ह (?) को प्रतिस्थापित कर सके।</span></p> <p><span style=\"font-family:Times New Roman\">2, 4, 5, 19, 71, ? </span></p>",
                    options_en: [" <p> 361 </span></p>", " <p> 316 </span></p>", 
                                " <p> 216 </span></p>", " <p> 261 </span></p>"],
                    options_hi: [" <p> 361 </span></p>", " <p> 316 </span></p>",
                                " <p> 216 </span></p>", " <p> 261 </span></p>"],
                    solution_en: " <p>11.</span><span style=\"font-family:Times New Roman\">(a) 2, 4, 5, 19, 71, ?</span></p> <p><span style=\"font-family:Times New Roman\">The pattern is -</span></p> <p><span style=\"font-family:Times New Roman\">2 × 1 + 2 = 4</span></p> <p><span style=\"font-family:Times New Roman\">4 × 2 - 3 = 5</span></p> <p><span style=\"font-family:Times New Roman\">5 × 3 + 4 = 19</span></p> <p><span style=\"font-family:Times New Roman\">19 × 4 - 5 = 71</span></p> <p><span style=\"font-family:Times New Roman\">71 × 5 + 6 = 361   </span></p>",
                    solution_hi: " <p><span style=\"font-family:Roboto\">11.</span><span style=\"font-family:Times New Roman\">(a) 2, 4, 5, 19, 71, ?</span></p> <p><span style=\"font-family:Baloo\">पैटर्न है -</span></p> <p><span style=\"font-family:Times New Roman\">2 × 1 + 2 = 4</span></p> <p><span style=\"font-family:Times New Roman\">4 × 2 - 3 = 5</span></p> <p><span style=\"font-family:Times New Roman\">5 × 3 + 4 = 19</span></p> <p><span style=\"font-family:Times New Roman\">17 × 4 - 5 = 71</span></p> <p><span style=\"font-family:Times New Roman\">71 × 5 + 6 = 361   </span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "6",
                    question_en: " <p>12. Four number-pairs have been given, out of which three are alike in some manner and one is different. Select the one that is different. </span></p>",
                    question_hi: " <p>12. चार संख्या-जोड़े दिए गए हैं, जिनमें से तीन किसी न किसी रूप में एक जैसे हैं और एक भिन्न है। जो अलग है उसे चुनें।</span></p>",
                    options_en: [" <p> 5 : 65 </span></p>", " <p> 8 : 104 </span></p>", 
                                " <p> 12 : 146 </span></p>", " <p> 11 : 143 </span></p>"],
                    options_hi: [" <p> 5 : 65 </span></p>", " <p> 8 : 104 </span></p>",
                                " <p> 12 : 146 </span></p>", " <p> 11 : 143 </span></p>"],
                    solution_en: " <p>12.</span><span style=\"font-family:Times New Roman\">(c) Logic :-  (n : 13n) </span></p> <p><span style=\"font-family:Times New Roman\">But option (c) does not follow the pattern.</span></p>",
                    solution_hi: " <p><span style=\"font-family:Roboto\">12.</span><span style=\"font-family:Baloo\">(c) तर्क :- (n : 13n) </span></p> <p><span style=\"font-family:Baloo\">लेकिन विकल्प (c) पैटर्न का पालन नहीं करता है।</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "6",
                    question_en: "<p>13. The sequence of folding a piece of paper and the manner in which the folded paper has been cut is shown in the following figures. How would this paper look when unfolded?</p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646739263/word/media/image20.png\"></p>\n",
                    question_hi: " <p>13. कागज के एक टुकड़े को मोड़ने का क्रम और जिस तरीके से मुड़े हुए कागज को काटा गया है, उसे निम्नलिखित आकृतियों में दिखाया गया है। खोलने पर यह पेपर कैसा दिखेगा?</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646739263/word/media/image20.png\"/></p>",
                    options_en: ["<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646739263/word/media/image29.png\"></p>\n", "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646739263/word/media/image31.png\"></p>\n", 
                                "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646739263/word/media/image24.png\"></p>\n", "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646739263/word/media/image3.png\"></p>\n"],
                    options_hi: [" <p> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646739263/word/media/image29.png\"/></p>", " <p> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646739263/word/media/image31.png\"/></p>",
                                " <p> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646739263/word/media/image24.png\"/></p>", " <p> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646739263/word/media/image3.png\"/></p>"],
                    solution_en: "<p><span style=\"font-family: Times New Roman;\">13.(b)</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">&nbsp;</span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646739263/word/media/image17.png\"></p>\n",
                    solution_hi: " <p><span style=\"font-family:Roboto\">13.</span><span style=\"font-family:Times New Roman\">(b) </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646739263/word/media/image17.png\"/></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "6",
                    question_en: " <p>14. Select the option that represents the correct order of the given words as they would appear in an English dictionary. </span></p> <p><span style=\"font-family:Times New Roman\">1. Rubric </span></p> <p><span style=\"font-family:Times New Roman\">2. Ruminate </span></p> <p><span style=\"font-family:Times New Roman\">3. Rose </span></p> <p><span style=\"font-family:Times New Roman\">4. Reader </span></p> <p><span style=\"font-family:Times New Roman\">5. Reap </span></p>",
                    question_hi: " <p>14. उस विकल्प का चयन करें जो दिए गए शब्दों के सही क्रम का प्रतिनिधित्व करता है जैसा कि वे एक अंग्रेजी शब्दकोश में दिखाई देंगे।</span></p> <p><span style=\"font-family:Times New Roman\">1. Rubric </span></p> <p><span style=\"font-family:Times New Roman\">2. Ruminate </span></p> <p><span style=\"font-family:Times New Roman\">3. Rose </span></p> <p><span style=\"font-family:Times New Roman\">4. Reader </span></p> <p><span style=\"font-family:Times New Roman\">5. Reap </span></p>",
                    options_en: [" <p> 4, 5, 3, 1, 2 </span></p>", " <p> 4, 3, 5, 1, 2 </span></p>", 
                                " <p> 4, 5, 3, 2, 1 </span></p>", " <p> 4, 5, 1, 3, 2 </span></p>"],
                    options_hi: [" <p> 4, 5, 3, 1, 2 </span></p>", " <p> 4, 3, 5, 1, 2 </span></p>",
                                " <p> 4, 5, 3, 2, 1 </span></p>", " <p> 4, 5, 1, 3, 2 </span></p>"],
                    solution_en: " <p>14.</span><span style=\"font-family:Times New Roman\">(a) The correct order of words according to the English alphabets is -</span></p> <p><span style=\"font-family:Cardo\">Reader → Reap →  Rose → Rubric → Ruminate </span></p>",
                    solution_hi: " <p><span style=\"font-family:Roboto\">14.</span><span style=\"font-family:Baloo\">(a) अंग्रेजी अक्षरों के अनुसार शब्दों का सही क्रम है -</span></p> <p><span style=\"font-family:Cardo\"> Reader → Reap →  Rose → Rubric → Ruminate</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "6",
                    question_en: "<p>15. Select the option in which the numbers share the same relationship as that shared by the numbers of the given set.</p>\r\n<p><span style=\"font-weight: 400;\">(8, 44, 224) </span></p>",
                    question_hi: "<p>15. उस विकल्प का चयन करें जिसमें संख्याएं वही संबंध साझा करती हैं जो दिए गए सेट की संख्याओं द्वारा साझा किया जाता है।</p>\r\n<p><span style=\"font-weight: 400;\">(8, 44, 224) </span></p>\r\n<p>&nbsp;</p>",
                    options_en: ["<p>(11, 59, 299) <span style=\"font-family: Times New Roman;\"> </span></p>", "<p>(9, 80, 312)</p>", 
                                "<p>(15, 75, 301) <span style=\"font-family: Times New Roman;\"> </span></p>", "<p>(7, 39, 190)</p>"],
                    options_hi: ["<p>(11, 59, 299) <span style=\"font-family: Times New Roman;\"> </span></p>", "<p>(9, 80, 312)</p>",
                                "<p>(15, 75, 301)</p>", "<p>(7, 39, 190)</p>"],
                    solution_en: "<p>15.<span style=\"font-family: Times New Roman;\">(a) Logic :- (n : 5n+4 : 5(5n+4)+4)</span></p>\r\n<p><span style=\"font-family: Cardo;\">So, (11, 59, 299) &rarr; (11, 55+4, 295+4)</span></p>",
                    solution_hi: "<p><span style=\"font-family: Roboto;\">15.</span><span style=\"font-family: Baloo;\">(a) तर्क :- (n : 5n+4 : 5(5n+4)+4)</span></p>\r\n<p><span style=\"font-family: Arial Unicode MS;\">इसलिए, (11, 59, 299) &rarr; (11, 55+4, 295+4)</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "6",
                    question_en: " <p><span style=\"font-family:Times New Roman\">16. Select the option that is related to the third term in the same way as the second term is related to the first term. </span></p> <p><span style=\"font-family:Times New Roman\">SLIM : MHQO : : COLD : ? </span></p>",
                    question_hi: " <p>16. उस विकल्प का चयन करें जो तीसरे पद से उसी प्रकार संबंधित है जैसे दूसरा पद पहले पद से संबंधित है। </span></p> <p><span style=\"font-family:Times New Roman\">SLIM : MHQO : : COLD : ? </span></p>",
                    options_en: [" <p> GPZK </span></p>", " <p> GKPZ </span></p>", 
                                " <p> GPKZ </span></p>", " <p> GZPK </span></p>"],
                    options_hi: [" <p> GPZK </span></p>", " <p> GKPZ </span></p>",
                                " <p> GPKZ </span></p>", " <p> GZPK </span></p>"],
                    solution_en: " <p>16.</span><span style=\"font-family:Times New Roman\">(d) Logic :- (i) Arrange the following alphabets in ascending order according to their place values.</span></p> <p> +4, -4, +4, -4..rule is used.</span></p> <p><span style=\"font-family:Cardo\">COLD → Reverse order = CDLO → Now apply  +4, -4, +4, -4..rule </span></p> <p><span style=\"font-family:Cardo\">So, COLD → GZPK</span></p>",
                    solution_hi: " <p><span style=\"font-family:Roboto\">16.</span><span style=\"font-family:Baloo\">(d) तर्क :- (i) निम्नलिखित वर्णों को उनके स्थानीय मान के अनुसार आरोही क्रम में व्यवस्थित कीजिए।</span></p> <p> 4, -4, 4, -4..नियम का प्रयोग किया जाता है।</span></p> <p><span style=\"font-family:Arial Unicode MS\">COLD → विपरीत क्रम = CDLO → </span></p> <p><span style=\"font-family:Baloo\">अब +4, -4, +4, -4..नियम लागू करें</span></p> <p><span style=\"font-family:Arial Unicode MS\">तो, COLD → GZPK</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "6",
                    question_en: "<p>17. Select the correct mirror image of the given combination when the mirror is placed at &lsquo;PQ&rsquo; as shown.</p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646739263/word/media/image28.png\" width=\"190\" height=\"154\"></p>\n",
                    question_hi: "<p>17. &#2342;&#2367;&#2319; &#2327;&#2319; &#2360;&#2306;&#2351;&#2379;&#2332;&#2344; &#2325;&#2368; &#2360;&#2361;&#2368; &#2342;&#2352;&#2381;&#2346;&#2339; &#2331;&#2357;&#2367; &#2325;&#2366; &#2330;&#2351;&#2344; &#2325;&#2352;&#2375;&#2306; &#2332;&#2348; &#2342;&#2352;&#2381;&#2346;&#2339; &#2325;&#2379; \'PQ\' &#2346;&#2352; &#2342;&#2367;&#2326;&#2366;&#2351;&#2366; &#2327;&#2351;&#2366; &#2361;&#2376;&#2404;</p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646739263/word/media/image28.png\" width=\"203\" height=\"164\"></p>\n",
                    options_en: ["<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646739263/word/media/image2.png\" width=\"202\" height=\"23\"></p>\n", "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646739263/word/media/image8.png\" width=\"201\" height=\"23\"></p>\n", 
                                "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646739263/word/media/image30.png\" width=\"201\" height=\"25\"></p>\n", "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646739263/word/media/image10.png\" width=\"205\" height=\"27\"></p>\n"],
                    options_hi: ["<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646739263/word/media/image2.png\" width=\"201\" height=\"23\"></p>\n", "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646739263/word/media/image8.png\" width=\"201\" height=\"23\"></p>\n",
                                "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646739263/word/media/image30.png\"></p>\n", "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646739263/word/media/image10.png\" width=\"205\" height=\"27\"></p>\n"],
                    solution_en: "<p>17.<span style=\"font-family: Times New Roman;\">(d)</span></p>\r\n<p><span style=\"font-family: Times New Roman;\"> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646739263/word/media/image14.png\" width=\"223\" height=\"60\"></p>\n",
                    solution_hi: "<p><span style=\"font-family: Roboto;\">17.</span><span style=\"font-family: Times New Roman;\">(d)&nbsp;</span></p>\r\n<p><span style=\"font-family: Times New Roman;\"><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646739263/word/media/image14.png\" width=\"245\" height=\"66\"></span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "6",
                    question_en: "<p>18. Select the option in which the numbers are related in the same way as are the numbers of the following set.</p>\r\n<p>(57, 126, 199)&nbsp;</p>\n",
                    question_hi: "<p>18. उस विकल्प का चयन करें जिसमें संख्याएँ उसी प्रकार संबंधित हैं जैसे निम्नलिखित सेट की संख्याएँ हैं।</p>\r\n<p>(57, 126, 199)&nbsp;</p>",
                    options_en: ["<p>(75, 148, 217)</p>\n", "<p>(29, 96, 168)</p>\n", 
                                "<p>(63, 132, 205)</p>\n", "<p>(43, 112, 178)</p>\n"],
                    options_hi: ["<p>(75, 148, 217)</p>", "<p>(29, 96, 168)</p>",
                                "<p>(63, 132, 205)</p>", "<p>(43, 112, 178)</p>"],
                    solution_en: "<p>18.<span style=\"font-family: Times New Roman;\">(c) Logic : (n : n + 69 : n + 142 )</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Similarly</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">63 + 69 = 132 and 63 + 142 = 205.</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Roboto;\">18.</span><span style=\"font-family: Baloo;\">(c) तर्क: (n : n + 69 : n + 142 )</span></p>\r\n<p><span style=\"font-family: Baloo;\">उसी प्रकार,</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">63 + 69 = 132 and 63 + 142 = 205.</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "6",
                    question_en: " <p>19. In a code language, if SEND is written as 168, then how will PURSE be written in the same language? </span></p>",
                    question_hi: " <p>19. एक कोड भाषा में, यदि SEND को 168 लिखा जाता है, तो उसी भाषा में PURSE को कैसे लिखा जाएगा?</span></p>",
                    options_en: [" <p> 185 </span><span style=\"font-family:Times New Roman\">  </span></p>", " <p> 225     </span></p>", 
                                " <p> 395      </span></p>", " <p> 415 </span></p>"],
                    options_hi: [" <p> 185 </span><span style=\"font-family:Times New Roman\">  </span></p>", " <p> 225     </span></p>",
                                " <p> 395      </span></p>", " <p> 415 </span></p>"],
                    solution_en: " <p>19.</span><span style=\"font-family:Times New Roman\">(c) Logic :- (Sum of the place values of alphabets) × (Number of Alphabets)</span></p> <p><span style=\"font-family:Cardo\">For “PURSE” → (16+21+18+19+5) × 5 = 79 × 5 = 395</span></p>",
                    solution_hi: " <p><span style=\"font-family:Roboto\">19.</span><span style=\"font-family:Baloo\">(c) तर्क :- (अक्षरों के स्थानीय मानों का योग) × (अक्षरों की संख्या)</span></p> <p><span style=\"font-family:Cardo\">For “PURSE” → (16+21+18+19+5) × 5 = 79 × 5 = 395</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "6",
                    question_en: "<p>20. Select the option in which the given figure is embedded (rotation is not allowed).</p>\r\n<p><img src=\"https://ssccglpinnacle.com/images/mceu_28190694811674104352661.png\" width=\"116\" height=\"111\"></p>\n",
                    question_hi: "<p>20. &#2313;&#2360; &#2357;&#2367;&#2325;&#2354;&#2381;&#2346; &#2325;&#2366; &#2330;&#2351;&#2344; &#2325;&#2352;&#2375;&#2306; &#2332;&#2367;&#2360;&#2350;&#2375;&#2306; &#2342;&#2368; &#2327;&#2312; &#2310;&#2325;&#2371;&#2340;&#2367; &#2309;&#2306;&#2340;&#2307;&#2360;&#2381;&#2341;&#2366;&#2346;&#2367;&#2340; &#2361;&#2376;&#2404; (&#2352;&#2379;&#2335;&#2375;&#2358;&#2344; &#2325;&#2368; &#2309;&#2344;&#2369;&#2350;&#2340;&#2367; &#2344;&#2361;&#2368;&#2306; &#2361;&#2376;&#2404;)</p>\r\n<p><img src=\"https://ssccglpinnacle.com/images/mceu_45175175211674104372522.png\"></p>\n",
                    options_en: ["<p><img src=\"https://ssccglpinnacle.com/images/mceu_46486286011678095452000.png\" width=\"99\" height=\"96\"></p>\n", "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646739263/word/media/image21.png\"></p>\n", 
                                "<p><img src=\"https://ssccglpinnacle.com/images/mceu_68797772011675919866787.png\" width=\"105\" height=\"103\"></p>\n", "<p><img src=\"https://ssccglpinnacle.com/images/ssc%20chsl%20reasoning%202021%20set%2001%2024032023%20206%20hgduyasgdyuasgffdasf.PNG\" alt=\"\" width=\"99\" height=\"91\"></p>\n"],
                    options_hi: ["<p><img src=\"https://ssccglpinnacle.com/images/mceu_2581835211678095483071.png\" width=\"109\" height=\"106\"></p>\n", "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646739263/word/media/image21.png\"></p>\n",
                                "<p><img src=\"https://ssccglpinnacle.com/images/mceu_68797772011675919866787.png\" width=\"102\" height=\"100\"></p>\n", "<p><img src=\"https://ssccglpinnacle.com/images/ssc%20chsl%20reasoning%202021%20set%2001%2024032023%20206%20hgduyasgdyuasgffdasf.PNG\" alt=\"\" width=\"99\" height=\"91\"></p>\n"],
                    solution_en: "<p>20.<span style=\"font-family: Times New Roman;\">(b)</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646739263/word/media/image6.png\" width=\"100\" height=\"97\"></p>\n",
                    solution_hi: "<p><span style=\"font-family: Roboto;\">20.</span><span style=\"font-family: Times New Roman;\">(b)</span></p>\r\n<p><span style=\"font-family: Times New Roman;\"> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646739263/word/media/image6.png\"></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "6",
                    question_en: " <p>21. In a certain code language, BEYOND is written as BDFNPY. How will PROMISE be written in that language? </span></p>",
                    question_hi: " <p>21. एक निश्चित कोड भाषा में, BEYOND को BDFNPY के रूप में लिखा जाता है। PROMISE को उसी भाषा में कैसे लिखा जाएगा?</span></p>",
                    options_en: [" <p> FJNPQSR </span><span style=\"font-family:Times New Roman\">    </span></p>", " <p> ESIMORP </span></p>", 
                                " <p> </span><span style=\"font-family:Times New Roman\">EIMOPRS</span><span style=\"font-family:Times New Roman\"> </span><span style=\"font-family:Times New Roman\">     </span></p>", " <p> </span><span style=\"font-family:Times New Roman\">FJMPPRS</span><span style=\"font-family:Times New Roman\"> </span></p>"],
                    options_hi: [" <p> FJNPQSR </span><span style=\"font-family:Times New Roman\">    </span></p>", " <p> ESIMORP </span></p>",
                                " <p> </span><span style=\"font-family:Times New Roman\">EIMOPRS</span><span style=\"font-family:Times New Roman\"> </span><span style=\"font-family:Times New Roman\">    </span></p>", " <p> </span><span style=\"font-family:Times New Roman\">FJMPPRS</span><span style=\"font-family:Times New Roman\"> </span></p>"],
                    solution_en: " <p>21.</span><span style=\"font-family:Times New Roman\">(d) Logic :- (i) Arrange the given word in Ascending order according to their place values.</span></p> <p> Now, Vowels will increase by 1.</span></p> <p><span style=\"font-family:Cardo\">PROMISE → After arranging → </span><span style=\"font-family:Times New Roman\">EIMOPRS</span></p> <p><span style=\"font-family:Times New Roman\">Then increase the vowels by 1.</span></p> <p><span style=\"font-family:Times New Roman\">So the final code for PROMISE will be </span><span style=\"font-family:Times New Roman\">FJMPPRS</span><span style=\"font-family:Times New Roman\">.</span></p>",
                    solution_hi: " <p><span style=\"font-family:Roboto\">21.</span><span style=\"font-family:Baloo\">(d) तर्क :- (i) दिए गए शब्द को उनके स्थानीय मान के अनुसार आरोही क्रम में व्यवस्थित करें।</span></p> <p> अब स्वरों में 1 की वृद्धि होगी।</span></p> <p><span style=\"font-family:Arial Unicode MS\">PROMISE → व्यवस्थित करने के बाद → </span><span style=\"font-family:Times New Roman\">EIMOPRS</span></p> <p><span style=\"font-family:Baloo\">फिर स्वरों को 1 से बढ़ाएँ।</span></p> <p><span style=\"font-family:Baloo\">तो PROMISE का अंतिम कोड </span><span style=\"font-family:Times New Roman\">FJMPPRS</span><span style=\"font-family:Baloo\"> होगा।</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "6",
                    question_en: " <p>22. Three of the following four letter-clusters are alike in a certain way and one is different. Select the odd one. </span></p>",
                    question_hi: " <p>22. निम्नलिखित चार अक्षर-समूहों में से तीन एक निश्चित तरीके से समान हैं और एक अलग है। विषम का चयन करें। </span></p>",
                    options_en: [" <p> LPT </span></p>", " <p> GKO </span></p>", 
                                " <p> QUY </span></p>", " <p> HLQ </span></p>"],
                    options_hi: [" <p> LPT </span></p>", " <p> GKO </span></p>",
                                " <p> QUY </span></p>", " <p> HLQ </span></p>"],
                    solution_en: " <p>22.</span><span style=\"font-family:Times New Roman\">(d) Except option (d), All others are following +4 pattern.</span></p>",
                    solution_hi: " <p><span style=\"font-family:Roboto\">22.</span><span style=\"font-family:Baloo\">(d) विकल्प (d) को छोड़कर, अन्य सभी +4 पैटर्न का अनुसरण कर रहे हैं।</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "23",
                    section: "6",
                    question_en: " <p>23. Four words have been given, out of which three are alike in some manner and one is different. Select the word that is different. </span></p>",
                    question_hi: " <p>23. चार शब्द दिए गए हैं, जिनमें से तीन किसी तरह से एक जैसे हैं और एक अलग है। उस शब्द का चयन करें जो भिन्न है।</span></p>",
                    options_en: [" <p> Equivocal </span><span style=\"font-family:Times New Roman\">    </span></p>", " <p> Hesitant </span></p>", 
                                " <p> Ambivalent </span><span style=\"font-family:Times New Roman\">    </span></p>", " <p> Certain </span></p>"],
                    options_hi: [" <p> Equivocal </span><span style=\"font-family:Times New Roman\">    </span></p>", " <p> Hesitant </span></p>",
                                " <p> Ambivalent </span><span style=\"font-family:Times New Roman\">   </span></p>", " <p> Certain </span></p>"],
                    solution_en: " <p>23.</span><span style=\"font-family:Times New Roman\">(d) Except Certain, All others are synonymous words. </span></p>",
                    solution_hi: " <p><span style=\"font-family:Roboto\">23.</span><span style=\"font-family:Baloo\">(d) Certain को छोड़कर, अन्य सभी पर्यायवाची शब्द हैं।</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "24",
                    section: "6",
                    question_en: " <p><span style=\"font-family:Times New Roman\">24. Select the option that is related to the third word in the same way as the second word is related to the first word. </span></p> <p><span style=\"font-family:Times New Roman\">Ohmmeter : Resistance : : Zymometer :? </span></p>",
                    question_hi: " <p>24. उस विकल्प का चयन करें जो तीसरे शब्द से उसी प्रकार संबंधित है जैसे दूसरा शब्द पहले शब्द से संबंधित है। </span></p> <p><span style=\"font-family:Baloo\">ओह्ममीटर : प्रतिरोध : : ज़ाइमोमीटर : ? </span></p>",
                    options_en: [" <p> Viscosity </span></p>", " <p> Density </span></p>", 
                                " <p> Fermentation </span></p>", " <p> Radiation </span></p>"],
                    options_hi: [" <p> चिपचिपापन</span></p>", " <p> घनत्व</span></p>",
                                " <p> किण्वन</span></p>", " <p> विकिरण</span></p>"],
                    solution_en: " <p>24.</span><span style=\"font-family:Times New Roman\">(c) As, Ohmmeter is used to measure the Resistance similarly </span><span style=\"font-family:Times New Roman\">Zymometer</span><span style=\"font-family:Times New Roman\"> is used to measure the degree of Fermentation.</span></p>",
                    solution_hi: " <p><span style=\"font-family:Roboto\">24.</span><span style=\"font-family:Baloo\">(c) जिस प्रकार ओममीटर का उपयोग प्रतिरोध को मापने के लिए किया जाता है उसी प्रकार जयमोमेटेर का उपयोग किण्वन की डिग्री को मापने के लिए किया जाता है।</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "25",
                    section: "misc",
                    question_en: " <p>25. Select the correct combination of mathematical signs that can sequentially replace the * signs and balance the given equation. </span></p> <p><span style=\"font-family:Times New Roman\">14 * 11 * 552 * 23 * 29 = 159 </span></p>",
                    question_hi: " <p>25. गणितीय चिह्नों के उस सही संयोजन का चयन करें जो * चिह्नों को क्रमिक रूप से बदल सके और दिए गए समीकरण को संतुलित कर सके।</span></p> <p><span style=\"font-family:Times New Roman\">14 * 11 * 552 * 23 * 29 = 159  </span></p>",
                    options_en: [" <p> ×, +, ÷, −  </span><span style=\"font-family:Gungsuh\">      </span></p>", " <p> ×, −, ÷, + </span></p>", 
                                " <p> ×, ÷, −, + </span><span style=\"font-family:Gungsuh\">      </span></p>", " <p> +, −, ÷, × </span></p>"],
                    options_hi: [" <p> ×, +, ÷, −  </span><span style=\"font-family:Gungsuh\">      </span></p>", " <p> ×, −, ÷, + </span></p>",
                                " <p> ×, ÷, −, + </span><span style=\"font-family:Gungsuh\">      </span></p>", " <p> +, −, ÷, × </span></p>"],
                    solution_en: " <p>25.</span><span style=\"font-family:Times New Roman\">(b) Applying hit and trial putting the symbols of option a </span></p> <p><span style=\"font-family:Times New Roman\">14 * 11 * 552 * 23 * 29 = 159 </span></p> <p><span style=\"font-family:Times New Roman\">14 × 11 -552 ÷ 23 + 29 = 159</span></p> <p><span style=\"font-family:Times New Roman\">154 - 24 +29 = 159</span></p> <p><span style=\"font-family:Times New Roman\">154 +5 = 159</span></p> <p><span style=\"font-family:Times New Roman\">Hence Verified</span></p>",
                    solution_hi: " <p><span style=\"font-family:Roboto\">25.</span><span style=\"font-family:Baloo\">(b) विकल्प (a) के चिह्न लगाकर हिट और ट्रायल लागू करने पर, </span></p> <p><span style=\"font-family:Times New Roman\">14 * 11 * 552 * 23 * 29 = 159 </span></p> <p><span style=\"font-family:Times New Roman\">14 × 11 -552 ÷ 23 + 29 = 159</span></p> <p><span style=\"font-family:Times New Roman\">154 - 24 +29 = 159</span></p> <p><span style=\"font-family:Times New Roman\">154 +5 = 159</span></p> <p><span style=\"font-family:Times New Roman\">159 = 159</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML - more compact
            let html = `
                <div class="card mb-3">
                    <div class="card-body">
                        <div class="question-text mb-3">
                            <div class="en">${question.question_en}</div>
                            <div class="hi" style="display:none">${question.question_hi}</div>
                        </div>
                        <div class="options">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = 'option-label';
                // When reviewing, highlight user's choice and correct answer
                if (submitted) {
                    if (opt === question.correct) {
                        optionClass += ' border-success bg-success bg-opacity-10';
                    }
                    if (answers[question.id] === opt && opt !== question.correct) {
                        optionClass += ' border-danger bg-danger bg-opacity-10';
                    }
                }
                html += `
                    <div class="form-check mb-2">
                        <input class="form-check-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${answers[question.id] === opt ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                        <label class="form-check-label w-100 ${optionClass}" for="opt-${index}-${opt}">
                            ${submitted && opt === question.correct ? '<i class="fas fa-check-circle text-success me-1"></i>' : ''}
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const alertClass = isCorrect ? 'alert-success' : 'alert-danger';
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="alert ${alertClass} mt-3 py-2">
                        <strong>${resultText}</strong>
                        <div class="mt-1 small">
                            <strong>Solution:</strong>
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                document.getElementById(`box-${index}`).classList.add('attempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { if (currentQuestion > 0) showQuestion(currentQuestion - 1); }
        function nextQuestion() { if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
            // Update button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const content = document.getElementById('main-content');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                if (window.innerWidth >= 992) {
                    content.classList.add('with-nav');
                }
            } else {
                nav.classList.remove('show');
                if (window.innerWidth >= 992) {
                    content.classList.remove('with-nav');
                }
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer - compact
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            if (confirm(`You have attempted ${attempted} of ${questions.length} questions.
${remaining} questions are unattempted.
Are you sure you want to submit?`)) {
                submitTest();
            }
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('bg-secondary', 'text-white');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            resultsModal.show();
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.classList.replace('btn-light', 'btn-success');
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            resultsModal.hide();
            showQuestion(0);
        }
        // Add event listener for window resize
        window.addEventListener('resize', function() {
            if (window.innerWidth >= 992) {
                if (document.getElementById('question-nav').classList.contains('show')) {
                    document.getElementById('main-content').classList.add('with-nav');
                }
            } else {
                document.getElementById('main-content').classList.remove('with-nav');
            }
        });
    </script>
</body>
</html>