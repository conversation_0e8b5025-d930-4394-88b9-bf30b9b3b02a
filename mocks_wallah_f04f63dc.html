<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">5:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 15</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">15</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 5 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["18"] = {
                name: "General Awareness",
                start: 0,
                end: 13
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="18">General Awareness</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 14,
                end: 14
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "18",
                    question_en: "<p>1. Ernest Rutherford used about ________ thick gold foil for alpha scattering experiments.</p>",
                    question_hi: "<p>1. अर्नेस्ट रदरफोर्ड ने अल्फा प्रकीर्णन प्रयोगों के लिए लगभग _______मोटी सोने की पन्नी का इस्तेमाल किया था।</p>",
                    options_en: ["<p>500 atoms</p>", "<p>1000 atoms</p>", 
                                "<p>900 atoms</p>", "<p>700 atoms</p>"],
                    options_hi: ["<p>500 परमाणु</p>", "<p>1000 परमाणु</p>",
                                "<p>900 परमाणु</p>", "<p>700 परमाणु</p>"],
                    solution_en: "<p>1.(b) <strong>1000 atoms.</strong> Ernest Rutherford used gold for his scattering experiment because gold is the most malleable metal and he wanted the thinnest layer of metal. He conducted an experiment to study the arrangement of electrons in an atom and designed an experiment for this. In this experiment, fast moving alpha (&alpha;)-particles were made to fall on a thin gold foil. This experiment showed that the atom is mostly empty space with a tiny, dense, positively-charged nucleus.</p>",
                    solution_hi: "<p>1.(b) <strong>1000 परमाणु। </strong>अर्नेस्ट रदरफोर्ड ने अपने प्रकीर्णन प्रयोग के लिए सोने का उपयोग किया क्योंकि सोना सबसे अधिक आघातवर्ध्यनीय धातु है और इससे आसानी से बहुत पतली शीट बनाई जा सकती है। उन्होंने एक परमाणु में इलेक्ट्रॉनों की स्थिति का अध्ययन करने के लिए एक प्रयोग किया और इसके लिए एक प्रयोग तैयार किया। इस प्रयोग में, तेज़ गति से चलने वाले अल्फा (&alpha;)-कणों को एक पतली सोने की पन्नी में प्रक्षेपित किया। इस प्रयोग से पता चला कि परमाणु में अधिकतर खाली स्थान होता है जिसमें एक छोटा, घना, धनात्मक रूप से आवेशित नाभिक होता है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "18",
                    question_en: "<p>2. Calculate the oxidation number of &lsquo;S&rsquo; in H<sub>2</sub>S<sub>2</sub>O<sub>7</sub>.</p>",
                    question_hi: "<p>2. H<sub>2</sub>S<sub>2</sub>O<sub>7</sub> में \'S\' की ऑक्सीकरण संख्या की गणना कीजिए।</p>",
                    options_en: ["<p>3</p>", "<p>7</p>", 
                                "<p>6</p>", "<p>2</p>"],
                    options_hi: ["<p>3</p>", "<p>7</p>",
                                "<p>6</p>", "<p>2</p>"],
                    solution_en: "<p>2.(c) <strong>6. </strong>To calculate the oxidation number of S in H<sub>2</sub>S<sub>2</sub>O<sub>7</sub>:<br>Assign oxidation numbers to known elements: H= +1 and O = -2<br>Write the equation: 2(+1) + 2x + 7(-2) = 0<br>&rArr; 2 + 2x - 14 = 0<br>&rArr; 2x = 12<br>&rArr; x = 6.</p>",
                    solution_hi: "<p>2.(c) <strong>6. </strong>H<sub>2</sub>S<sub>2</sub>O<sub>7</sub> में S की ऑक्सीकरण संख्या की गणना करने के लिए:<br>ज्ञात तत्वों को ऑक्सीकरण संख्याएँ निर्दिष्ट करें: H= +1 और O = -2<br>समीकरण लिखें: 2(+1) + 2x + 7(-2) = 0<br>&rArr; 2 + 2x - 14 = 0<br>&rArr; 2x = 12<br>&rArr; x = 6.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "18",
                    question_en: "<p>3. Which of the following gases emits red light when electricity is passed through it?</p>",
                    question_hi: "<p>3. निम्नलिखित में से किस गैस से विद्युत प्रवाहित करने पर लाल प्रकाश उत्सर्जित होता है?</p>",
                    options_en: ["<p>Neon</p>", "<p>Hydrogen</p>", 
                                "<p>Argon</p>", "<p>Helium</p>"],
                    options_hi: ["<p>नियॉन</p>", "<p>हाइड्रोजन</p>",
                                "<p>आर्गन</p>", "<p>हीलियम</p>"],
                    solution_en: "<p>3.(a) <strong>Neon.</strong> Electric discharge in gases happens when electric current passes through a gaseous medium, leading to the ionization of the gas. This process can produce visible light, with different elements emitting various wavelengths and colors as they return to their ground states. Gas and Colour : Hydrogen - Blue-violet, Helium - Pink-orange, Argon - Violet and Krypton - Lavender.</p>",
                    solution_hi: "<p>3.(a) <strong>नियॉन।</strong> गैसों में विद्युत निर्वहन तब होता है जब विद्युत धारा गैसीय माध्यम से होकर गुजरती है, जिससे गैस का आयनीकरण होता है। यह प्रक्रिया दृश्य प्रकाश उत्पन्न कर सकती है, जिसमें विभिन्न तत्व विभिन्न तरंगदैर्ध्य और रंगों का उत्सर्जन करते हैं क्योंकि वे अपनी मूल अवस्था में लौटते हैं। गैस और उनके रंग: हाइड्रोजन - नीला-बैंगनी, हीलियम - गुलाबी-नारंगी, आर्गन - बैंगनी और क्रिप्टॉन - लैवेंडर।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "18",
                    question_en: "<p>4. What is the product formed when zinc and sulphuric acid react?</p>",
                    question_hi: "<p>4. जिंक और सल्फ्यूरिक अम्ल की अभिक्रिया से कौन-सा उत्पाद निर्मित होता है?</p>",
                    options_en: ["<p>Zinc sulphate</p>", "<p>Zinc hydroxide</p>", 
                                "<p>Zinc sulphide</p>", "<p>Zinc oxide</p>"],
                    options_hi: ["<p>जिंक सल्फेट</p>", "<p>जिंक हाइड्रॉक्साइड</p>",
                                "<p>जिंक सल्फाइड</p>", "<p>जिंक ऑक्साइड</p>"],
                    solution_en: "<p>4.(a) <strong>Zinc sulphate.</strong> Zinc reacts with dilute Sulphuric acid to form Zinc sulphate and Hydrogen gas is evolved from the reaction. This is a single displacement reaction of a non-metal by a metal. Both metals and nonmetals take part in displacement reactions. Zn (s) + H<sub>2</sub>SO<sub>4</sub>(aq) &rarr; ZnSO<sub>4</sub>(aq) + H<sub>2</sub>(g).</p>",
                    solution_hi: "<p>4.(a) <strong>जिंक सल्फेट।</strong> जिंक तनु सल्फ्यूरिक अम्ल के साथ अभिक्रिया करके जिंक सल्फेट बनाता है और अभिक्रिया से हाइड्रोजन गैस निकलती है। यह एक धातु द्वारा अधातु की एकल विस्थापन अभिक्रिया है। धातु और अधातु दोनों ही विस्थापन अभिक्रियाओं में भाग लेते हैं।<br>Zn (s) + H<sub>2</sub>SO<sub>4</sub>(aq) &rarr; ZnSO<sub>4</sub>(aq) + H<sub>2</sub>(g).</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "18",
                    question_en: "<p>5. What was Antoine-Laurent Lavoisier\'s most important contribution to chemistry in 1789?</p>",
                    question_hi: "<p>5. 1789 में, एंटोनी-लॉरेंट लेवोज़ियर (Antoine-Laurent Lavoisier) का रसायन विज्ञान में सर्वाधिक महत्वपूर्ण योगदान क्या था?</p>",
                    options_en: ["<p>Law of Conservation of Mass</p>", "<p>Law of Multiple Proportions</p>", 
                                "<p>Law of Definite Proportions</p>", "<p>Law of Conservation of Energy</p>"],
                    options_hi: ["<p>द्रव्यमान संरक्षण का नियम</p>", "<p>गुणित अनुपात का नियम</p>",
                                "<p>स्थिर अनुपात का नियम</p>", "<p>ऊर्जा संरक्षण का नियम</p>"],
                    solution_en: "<p>5.(a) <strong>Law of Conservation of Mass</strong> states that mass can neither be created nor destroyed in a chemical reaction. The Law of Multiple Proportions (John Dalton) states that the masses of one element which combine with a fixed mass of the second element are in a ratio of whole numbers. Law of Definite Proportions (Joseph Proust) states that in a given chemical substance the elements are always present in definite proportions by mass. The law of conservation of energy (Julius Robert Mayer) states that energy can neither be created nor destroyed, only converted from one form to another.</p>",
                    solution_hi: "<p>5.(a) <strong>द्रव्यमान संरक्षण का नियम</strong> कहता है कि रासायनिक अभिक्रिया में द्रव्यमान को न तो निर्माण किया जा सकता है और न ही नष्ट किया जा सकता है। गुणित अनुपात का नियम (जॉन डाल्टन) कहता है कि एक तत्व के द्रव्यमान, दूसरे तत्व के निश्चित द्रव्यमान के साथ संयोजित होकर, पूर्ण संख्याओं के अनुपात में होते हैं। स्थिर अनुपात का नियम (जोसेफ प्राउस्ट) कहता है कि किसी दिए गए रासायनिक पदार्थ में तत्व हमेशा द्रव्यमान के अनुसार स्थिर अनुपात में मौजूद होते हैं। ऊर्जा संरक्षण का नियम (जूलियस रॉबर्ट मेयर) कहता है कि ऊर्जा को न तो निर्माण किया जा सकता है और न ही नष्ट किया जा सकता है, इसे केवल एक रूप से दूसरे रूप में परिवर्तित किया जा सकता है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "18",
                    question_en: "<p>6. Which polyatomic ionic compound is a white, crystalline powder used in fire extinguishers and to neutralise acids and bases?</p>",
                    question_hi: "<p>6. कौन-सा बहुपरमाणुक आयनी यौगिक ऐसा सफेद, क्रिस्टलीय पाउडर होता है जिसका उपयोग अग्निशामक यंत्रों में और अम्ल तथा क्षार को उदासीन करने में किया जाता है?</p>",
                    options_en: ["<p>Sodium Bisulphite</p>", "<p>Sodium Thiosulphate</p>", 
                                "<p>Sodium Chromate</p>", "<p>Sodium Bicarbonate</p>"],
                    options_hi: ["<p>सोडियम बाइसल्फाइट (Sodium Bisulphite)</p>", "<p>सोडियम थायोसल्फेट (Sodium Thiosulphate)</p>",
                                "<p>सोडियम क्रोमेट (Sodium Chromate)</p>", "<p>सोडियम बाईकारबोनेट (Sodium Bicarbonate)</p>"],
                    solution_en: "<p>6.(d) <strong>Sodium Bicarbonate</strong> also known as Baking soda. Its chemical formula is NaHCO<sub>3</sub>. It is used as a pH buffering agent, an electrolyte replenisher, systemic alkalizer and in topical cleansing solutions. Sodium Bisulphite (NaHSO<sub>3</sub>) appears as white crystals or crystalline powder used in food processing, photography. Sodium Chromate (Na<sub>2</sub>CrO<sub>4</sub>) appears as a yellow crystalline solid used to make pigments for paints and inks. Sodium thiosulfate (Na<sub>2</sub>O<sub>3</sub>S<sub>2</sub>) is an inorganic sodium salt, It has a role as an antidote to cyanide poisoning, a nephroprotective agent and an antifungal drug.</p>",
                    solution_hi: "<p>6.(d) <strong>सोडियम बाईकारबोनेट</strong> (Sodium Bicarbonate) को बेकिंग सोडा के नाम से भी जाना जाता है। इसका रासायनिक सूत्र NaHCO<sub>3</sub> है। इसका उपयोग pH बफरिंग एजेंट, इलेक्ट्रोलाइट पुनःपूर्तिकर्ता, प्रणालीगत क्षारीयकर्ता और सामयिक सफाई समाधान के रूप में किया जाता है। सोडियम बाइसल्फाइट (NaHSO<sub>3</sub>) सफेद क्रिस्टल या क्रिस्टलीय पाउडर के रूप में दिखाई देता है जिसका उपयोग खाद्य प्रसंस्करण, फोटोग्राफी में किया जाता है। सोडियम क्रोमेट (Na<sub>2</sub>CrO<sub>4</sub>) एक पीले क्रिस्टलीय ठोस के रूप में दिखाई देता है जिसका उपयोग पेंट और स्याही के लिए पिगमेंट बनाने के लिए किया जाता है। सोडियम थायोसल्फेट (Na<sub>2</sub>O<sub>3</sub>S<sub>2</sub>) एक अकार्बनिक सोडियम नमक है, यह साइनाइड विषाक्तता के लिए एक मारक, एक नेफ्रोप्रोटेक्टिव घटक और एक एंटीफंगल दवा के रूप में मुख्य भूमिका निभाता है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "18",
                    question_en: "<p>7. Transition elements are the elements that are found in Groups 3-12 of the modern periodic table, that constitute the _____</p>",
                    question_hi: "<p>7. संक्रमण तत्व वे तत्व हैं जो आधुनिक आवर्त सारणी के समूह 3 - 12 में पाए जाते हैं, जो _____ का निर्माण करते हैं।</p>",
                    options_en: ["<p>d-block</p>", "<p>s-block</p>", 
                                "<p>p-block</p>", "<p>f-block</p>"],
                    options_hi: ["<p>d-ब्लॉक</p>", "<p>s-ब्लॉक</p>",
                                "<p>p-ब्लॉक</p>", "<p>f-ब्लॉक</p>"],
                    solution_en: "<p>7.(a) <strong>d-block.</strong> Transition metals are defined as metals that have an incomplete d subshell in either their neutral atom or in their ions. p-block elements are located in groups 13&ndash;18 of the periodic table. S-block elements are in groups 1 and 2 of the periodic table. The f-block consists of elements in which 4 f and 5 f orbitals are progressively filled. They are placed in a separate panel at the bottom of the periodic table.</p>",
                    solution_hi: "<p>7.(a) <strong>d-ब्लॉक। </strong>संक्रमण तत्व को उन तत्व के रूप में परिभाषित किया जाता है जिनके उदासीन परमाणु या आयनों में एक अपूर्ण d उपकोश होता है। p-ब्लॉक तत्व आवर्त सारणी के समूह 13-18 में स्थित हैं। s-ब्लॉक तत्व आवर्त सारणी के समूह 1 और 2 में हैं। f-ब्लॉक में ऐसे तत्व होते हैं जिनमें 4 f और 5 f ऑर्बिटल्स क्रमिक रूप से भरे जाते हैं। उन्हें आवर्त सारणी के निचले भाग में एक अलग पैनल में रखा गया है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "18",
                    question_en: "<p>8. Identify the structural formula for magnesium hydroxide.</p>",
                    question_hi: "<p>8. मैग्नीशियम हाइड्रॉक्साइड के संरचनात्मक सूत्र की पहचान करें।</p>",
                    options_en: ["<p>MgOH</p>", "<p>MgO<sub>2</sub></p>", 
                                "<p>Mg<sub>2</sub>H<sub>2</sub></p>", "<p>Mg(OH)<sub>2</sub></p>"],
                    options_hi: ["<p>MgOH</p>", "<p>MgO<sub>2</sub></p>",
                                "<p>Mg<sub>2</sub>H<sub>2</sub></p>", "<p>Mg(OH)<sub>2</sub></p>"],
                    solution_en: "<p>8.(d) <strong>Mg(OH)<sub>2</sub>. </strong>Magnesium hydroxide is an inorganic compound. It is naturally found as the mineral brucite. Magnesium hydroxide can be used as an antacid or a laxative in either an oral liquid suspension or chewable tablet form. Additionally, magnesium hydroxide has smoke suppressing and flame retardant properties and is thus used commercially as a fire retardant.</p>",
                    solution_hi: "<p>8.(d) <strong>Mg(OH)<sub>2</sub>.</strong> मैग्नीशियम हाइड्रॉक्साइड एक अकार्बनिक यौगिक है। यह प्राकृतिक रूप से खनिज ब्रुसाइट के रूप में पाया जाता है। मैग्नीशियम हाइड्रॉक्साइड को एंटासिड या रेचक के रूप में मौखिक तरल निलंबन (oral liquid suspension) या चबाने योग्य टैबलेट के रूप में इस्तेमाल किया जा सकता है। इसके अतिरिक्त, मैग्नीशियम हाइड्रॉक्साइड में धुंध शमन और ज्वाला मंदक गुण होते हैं और इस प्रकार इसे अग्निरोधी के रूप में व्यावसायिक रूप से उपयोग किया जाता है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "18",
                    question_en: "<p>9. Grignard reagent is represented as:</p>",
                    question_hi: "<p>9. ग्रिग्नार्ड अभिकर्मक को निम्न रूप में दर्शाया जाता है:</p>",
                    options_en: ["<p>CH<sub>3</sub>-Ca-F</p>", "<p>CH<sub>3</sub>-Be-F</p>", 
                                "<p>CH<sub>3</sub>-Mg-Cl</p>", "<p>H-Mg-H</p>"],
                    options_hi: ["<p>CH<sub>3</sub>-Ca-F</p>", "<p>CH<sub>3</sub>-Be-F</p>",
                                "<p>CH<sub>3</sub>-Mg-Cl</p>", "<p>H-Mg-H</p>"],
                    solution_en: "<p>9.(c) <strong>CH<sub>3</sub>-Mg-Cl.</strong> Grignard reagent : It is an organometallic compound with the general formula RMgX, where R is an alkyl or aryl group, Mg represents magnesium, and X is a halogen (Cl, Br, I). Grignard reagents are used in organic synthesis (addition, substitution); React with carbonyl compounds, acids, and esters; Useful for forming carbon-carbon bonds.</p>",
                    solution_hi: "<p>9.(c) <strong>CH<sub>3</sub>-Mg-Cl.</strong> ग्रिग्नार्ड अभिकर्मक: यह एक कार्बनिक धातु यौगिक है जिसका सामान्य सूत्र RMgX है, जहाँ R एक एल्काइल या एरिल समूह है, Mg मैग्नीशियम को दर्शाता है, और X एक हैलोजन (Cl, Br, I) है। ग्रिग्नार्ड अभिकर्मकों का उपयोग कार्बनिक संश्लेषण (योग, प्रतिस्थापन) में किया जाता है; कार्बोनिल यौगिकों, अम्लों तथा एस्टर के साथ अभिक्रिया करते हैं; कार्बन-कार्बन बंध बनाने के लिए उपयोगी होते हैं।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "18",
                    question_en: "<p>10. Which of the following gases get released when dilute sulphuric acid (H<sub>2</sub>SO<sub>4</sub>) reacts with magnesium (Mg)?</p>",
                    question_hi: "<p>10. जब तनु सल्फ़्यूरिक अम्ल (H<sub>2</sub>SO<sub>4</sub>), मैग्नीशियम (Mg) के साथ अभिक्रिया करता है, तो निम्नलिखित में से कौन-सी गैस निकलती है?</p>",
                    options_en: ["<p>SO<sub>3</sub></p>", "<p>SO<sub>2</sub></p>", 
                                "<p>O<sub>2</sub></p>", "<p>H<sub>2</sub></p>"],
                    options_hi: ["<p>SO<sub>3</sub></p>", "<p>SO<sub>2</sub></p>",
                                "<p>O<sub>2</sub></p>", "<p>H<sub>2</sub></p>"],
                    solution_en: "<p>10.(d) <strong>H<sub>2</sub>.</strong> The chemical equation for this reaction is : Mg (s) + H<sub>2</sub>SO<sub>4</sub> (aq) <math display=\"inline\"><mo>&#8594;</mo></math> MgSO<sub>4</sub> (aq) + H<sub>2 </sub>(g). This is a displacement reaction that occurs at normal temperatures. The gas produced (H<sub>2</sub>) by a metal reacting with a dilute acid burn with a popping sound.</p>",
                    solution_hi: "<p>10.(d) <strong>H<sub>2</sub>.</strong> इस अभिक्रिया का रासायनिक समीकरण है: Mg (s) + H<sub>2</sub>SO<sub>4</sub> (aq) <math display=\"inline\"><mo>&#8594;</mo></math> MgSO<sub>4</sub> (aq) + H<sub>2 </sub>(g)। यह एक विस्थापन अभिक्रिया है जो सामान्य तापमान पर होती है। धातु द्वारा तनु अम्ल के साथ अभिक्रिया करने पर उत्पन्न गैस (H<sub>2</sub>) चटकने की ध्वनि के साथ जलती है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "18",
                    question_en: "11. Which of the following is NOT a halogen gas?",
                    question_hi: "11. निम्नलिखित में से कौन-सी हैलोजन गैस नहीं है?",
                    options_en: [" F ", " He ", 
                                " Cl", " Br"],
                    options_hi: [" F ", " He ",
                                " Cl", " Br"],
                    solution_en: "11.(b) Helium (He) is a chemical element with atomic number 2. It is a colorless, odorless, tasteless, non-toxic, inert gas and the first in the Noble gas group in the periodic table. The Halogens are non-Metallic elements found in group 17 (second from the right) of the Modern periodic table. The halogen elements are Fluorine (F), Chlorine (Cl), Bromine (Br), Iodine (I), Astatine (At), and Tennessine (Ts). ",
                    solution_hi: "11.(b) हीलियम (He) एक रासायनिक तत्व है, जिसका परमाणु क्रमांक 2 है। यह एक रंगहीन, गंधहीन, स्वादहीन, गैर विषैली, निष्क्रिय गैस है तथा आवर्त सारणी में नोबल गैस समूह में प्रथम है। हैलोजन आधुनिक आवर्त सारणी के समूह 17 (दाएं से दूसरे) में पाया जाने वाला गैर-धात्विक तत्व है। हैलोजन तत्व फ्लोरीन (F), क्लोरीन (Cl), ब्रोमीन (Br), आयोडीन (I), एस्टेटिन (At), और टेनेसीन (Ts) हैं।",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "18",
                    question_en: "<p>12. The chemical formula of glucose is C<sub>6</sub>H<sub>12</sub>O<sub>6</sub>. The weight % of carbon in glucose is:</p>",
                    question_hi: "<p>12. ग्लूकोज का रासायनिक सूत्र C<sub>6</sub>H<sub>12</sub>O<sub>6</sub>है। ग्लूकोज में कार्बन का भार प्रतिशत (%) कितना होता है।</p>",
                    options_en: ["<p>40</p>", "<p>72</p>", 
                                "<p>53</p>", "<p>25</p>"],
                    options_hi: ["<p>40</p>", "<p>72</p>",
                                "<p>53</p>", "<p>25</p>"],
                    solution_en: "<p>12.(a)<strong> 40.</strong> Calculate the molar mass of glucose (C<sub>6</sub>H<sub>12</sub>O<sub>6</sub>) = (6 x 12) + (12 x 1) + (6 x 16) = 72 + 12 + 96 = 180 g/mol.&nbsp;<br>Total mass of carbon (C) = 6 x 12 = 72 g.<br>We Know that,<br>Percentage by Weight = <math display=\"inline\"><mfrac><mrow><mi>W</mi><mi>e</mi><mi>i</mi><mi>g</mi><mi>h</mi><mi>t</mi><mi>&#160;</mi><mi>o</mi><mi>f</mi><mi>&#160;</mi><mi>c</mi><mi>a</mi><mi>r</mi><mi>b</mi><mi>o</mi><mi>n</mi></mrow><mrow><mi>W</mi><mi>e</mi><mi>i</mi><mi>g</mi><mi>h</mi><mi>t</mi><mi>&#160;</mi><mi>o</mi><mi>f</mi><mi>&#160;</mi><mi>G</mi><mi>l</mi><mi>u</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>e</mi></mrow></mfrac></math> &times; 100 <br>&rArr; <math display=\"inline\"><mfrac><mrow><mn>72</mn></mrow><mrow><mn>180</mn></mrow></mfrac></math> &times; 100 = 40 %.</p>",
                    solution_hi: "<p>12.(a)&nbsp;<strong>40.</strong> ग्लूकोज़ (C<sub>6</sub>H<sub>12</sub>O<sub>6</sub>) का मोलर द्रव्यमान की गणना = (6 &times; 12) + (12 &times; 1) + (6 &times; 16) = 72 + 12 + 96 = 180 ग्राम/मोल&nbsp;<br>कार्बन का कुल द्रव्यमान (C) = 6 &times; 12 = 72 g.<br>हम जानते हैं कि,<br>भार प्रतिशत = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2325;&#2366;&#2352;&#2348;&#2344;</mi><mi>&#160;</mi><mi>&#2325;&#2366;</mi><mi>&#160;</mi><mi>&#2349;&#2366;&#2352;</mi></mrow><mrow><mi>&#2327;&#2381;&#2354;&#2370;&#2325;&#2379;&#2332;</mi><mi>&#160;</mi><mi>&#2325;&#2366;</mi><mi>&#160;</mi><mi>&#2349;&#2366;&#2352;</mi></mrow></mfrac></math> &times; 100 <br>&rArr; <math display=\"inline\"><mfrac><mrow><mn>72</mn></mrow><mrow><mn>180</mn></mrow></mfrac></math> &times; 100 = 40 %.</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "18",
                    question_en: "<p>13. Which of the following is the most acidic?</p>",
                    question_hi: "<p>13. निम्नलिखित में से कौन-सा सबसे अधिक अम्लीय है?</p>",
                    options_en: ["<p>HCOOH</p>", "<p>C<sub>2</sub>H<sub>5</sub>COOH</p>", 
                                "<p>C<sub>3</sub>H<sub>7</sub>COOH</p>", "<p>CH<sub>3</sub>COOH</p>"],
                    options_hi: ["<p>HCOOH</p>", "<p>C<sub>2</sub>H<sub>5</sub>COOH</p>",
                                "<p>C<sub>3</sub>H<sub>7</sub>COOH</p>", "<p>CH<sub>3</sub>COOH</p>"],
                    solution_en: "<p>13.(a) <strong>HCOOH.</strong> An acidic substance has a pH level below 7 on the pH scale, which ranges from 0 to 14, with 7 being neutral. The closer the pH is to 0, the stronger the acid. According to the Arrhenius theory, acids increase the concentration of hydrogen ions (H⁺) in water. Fluoroantimonic acid is recognized as the strongest acid ever discovered.</p>",
                    solution_hi: "<p>13.(a) <strong>HCOOH.</strong> pH का रेंज 0 से 14 तक होता है, अम्लीय पदार्थ का pH मान pH स्केल पर 7 से कम होता है, जिसमें 7 उदासीन को दर्शाता है। pH मान जितना 0 के नजदीक होता है, अम्ल उतना ही प्रबल होता है। अरहेनियस सिद्धांत के अनुसार, अम्ल जल में हाइड्रोजन आयनों (H⁺) की सांद्रता को बढ़ाते हैं। फ्लोरोएंटिमोनिक अम्ल को अब तक खोजे गए सबसे प्रबल अम्ल के रूप में जाना जाता है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "18",
                    question_en: "<p>14. Which of the following is NOT a greenhouse gas?</p>",
                    question_hi: "<p>14. निम्नलिखित में से कौन-सी एक ग्रीन हाउस गैस नहीं है?</p>",
                    options_en: ["<p>Methane</p>", "<p>Carbon dioxide</p>", 
                                "<p>Nitrous oxide</p>", "<p>Nitric oxide</p>"],
                    options_hi: ["<p>मीथेन</p>", "<p>कार्बन डाइऑक्साइड</p>",
                                "<p>----empty----</p>", "<p>नाइट्रस ऑक्साइड<br>(dh) नाइट्रिक ऑक्साइड</p>"],
                    solution_en: "<p>14.(d)<strong> Nitric oxide.</strong> The greenhouse effect is a natural process that warms the Earth\'s surface by trapping heat from the sun. Greenhouse gases, such as methane, carbon dioxide, nitrogen dioxide, ozone, and water vapor, absorb and emit infrared radiation, functioning similarly to a greenhouse. This process is essential for maintaining the Earth\'s temperature and climate.</p>",
                    solution_hi: "<p>14.(d)<strong> नाइट्रिक ऑक्साइड।</strong> ग्रीनहाउस प्रभाव एक प्राकृतिक प्रक्रिया है जो सूर्य से आने वाली ऊष्मा को रोककर पृथ्वी की सतह को गर्म करती है। ग्रीनहाउस गैसें, जैसे मीथेन, कार्बन डाइऑक्साइड, नाइट्रोजन डाइऑक्साइड, ओजोन और जल वाष्प, अवरक्त विकिरण आदि को अवशोषित और उत्सर्जित करती हैं, जो ग्रीनहाउस के समान कार्य करती हैं। यह प्रक्रिया पृथ्वी के ताप और जलवायु को बनाए रखने के लिए आवश्यक है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "misc",
                    question_en: "<p>15. Which allotrope of carbon was discovered by Robert F Curl, Harold W Kroto and Richard E Smalley in 1985?</p>",
                    question_hi: "<p>15. 1985 में रॉबर्ट एफ कर्ल (Robert F Curl), हेरोल्ड डब्ल्यू क्रोटो (Harold W Kroto) और रिचर्ड ई स्माली (Richard E Smalley) ने कार्बन के किस अपररूप की खोज की थी?</p>",
                    options_en: ["<p>Graphene</p>", "<p>Lonsdaleite</p>", 
                                "<p>Carbophene</p>", "<p>Fullerene</p>"],
                    options_hi: ["<p>ग्राफीन (Graphene)</p>", "<p>लौन्सडेलाइट (Lonsdaleite)</p>",
                                "<p>कार्बोफीन (Carbophene)</p>", "<p>फुलेरीन (Fullerene)</p>"],
                    solution_en: "<p>15.(d) <strong>Fullerene.</strong> A fullerene is a carbon molecule in the shape of a hollow sphere, ellipsoid, or tube, with the general formula C<sub>2n</sub> (n &ge; 30). Created by evaporating graphite with a laser, fullerenes are unique among carbon allotropes, which also include diamond, graphite, and amorphous carbon&mdash;found in substances like coal and soot.</p>",
                    solution_hi: "<p>15.(d) <strong>फुलेरीन</strong> (Fullerene)। फुलेरीन कार्बन का एक अणु है जो एक खोखले गोले, दीर्घवृत्ताकार या ट्यूब के आकार का होता है, जिसका सामान्य सूत्र C<sub>2n </sub>(n &ge; 30) होता है। लेजर से ग्रेफाइट को वाष्पित करके बनाया गया फुलेरीन कार्बन एलोट्रोप्स में अद्वितीय है, जिसमें हीरा, ग्रेफाइट और कार्बन के अन्य रूप भी शामिल हैं - जो कोयले और कालिख जैसे पदार्थों में पाया जाता है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>