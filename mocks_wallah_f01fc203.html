<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">15:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 15</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">15</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 15 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 0,
                end: 13
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 14,
                end: 14
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "17",
                    question_en: "<p>1. The following table shows the marks (out of 100) obtained by four students in five different subjects.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1732689513595.png\" alt=\"rId4\" width=\"424\" height=\"136\"> <br>How many marks did Preeti score in Science?</p>",
                    question_hi: "<p>1. निम्नलिखित तालिका पांच अलग-अलग विषयों में चार छात्रों द्वारा प्राप्त अंक (100 में से) दर्शाती है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1732689513708.png\" alt=\"rId5\" width=\"353\" height=\"136\"></p>",
                    options_en: ["<p>95</p>", "<p>92</p>", 
                                "<p>97</p>", "<p>96</p>"],
                    options_hi: ["<p>95</p>", "<p>92</p>",
                                "<p>97</p>", "<p>96</p>"],
                    solution_en: "<p>1.(c)<br>From the above table, it is clearly shown that Preeti scored 97 marks in Science.</p>",
                    solution_hi: "<p>1.(c)<br>उपरोक्त तालिका से यह स्पष्ट रूप से पता चलता है कि प्रीति ने विज्ञान में 97 अंक प्राप्त किये।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "17",
                    question_en: "<p>2. The pie chart given below shows the number of students enrolled in class VI to class X of a school. If 1250 students are enrolled in VI to X, then find the sum of students enrolled in class VIII and IX.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1732689513854.png\" alt=\"rId6\" width=\"215\" height=\"200\"></p>",
                    question_hi: "<p>2. नीचे दिया गया पाई चार्ट एक स्कूल की कक्षा VI से कक्षा X तक नामांकित विद्यार्थियों की संख्या को दर्शाता है। यदि VI से X तक 1250 विद्यार्थी नामांकित हैं, तो कक्षा VIII और IX में नामांकित विद्यार्थियों का योगफल ज्ञात कीजिए।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1732689513854.png\" alt=\"rId6\" width=\"215\" height=\"200\"></p>",
                    options_en: ["<p>616</p>", "<p>516</p>", 
                                "<p>661</p>", "<p>575</p>"],
                    options_hi: ["<p>616</p>", "<p>516</p>",
                                "<p>661</p>", "<p>575</p>"],
                    solution_en: "<p>2.(d)<br>Let the total enrollments of students be 100%<br>% of students enrolled in class VIII and IX = 20+26 = 46%<br>Required no of students = <math display=\"inline\"><mfrac><mrow><mn>46</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math>&times;1250 = 575</p>",
                    solution_hi: "<p>2.(d)<br>माना कि छात्रों का कुल नामांकन 100% है<br>आठवीं और नौवीं कक्षा में नामांकित छात्रों का प्रतिशत = 20 + 26 = 46%<br>छात्रों की आवश्यक संख्या = <math display=\"inline\"><mfrac><mrow><mn>46</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math>&times;1250 = 575</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "17",
                    question_en: "<p>3. The following pie charts show the data of the number of appeared and passed students of class 12 in sections A,B,C,D and E.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1732689513965.png\" alt=\"rId7\" width=\"204\" height=\"193\"> <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1732689514070.png\" alt=\"rId8\" width=\"199\" height=\"187\"> <br>What is the percentage of students who appeared for the exam in section E (correct to one decimal place) ?</p>",
                    question_hi: "<p>3. निम्न पाई चार्ट कक्षा 12 के सेक्शन A, B, C, D और E में उपस्थित और उत्तीर्ण छात्रों की संख्या का डेटा दर्शाते हैं।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1732689514336.png\" alt=\"rId9\" width=\"206\" height=\"200\"> <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1732689514477.png\" alt=\"rId10\" width=\"203\" height=\"200\"> <br>सेक्शन E में परीक्षा में उपस्थित छात्रों का प्रतिशत क्या है (दशमलव के बाद एक अंक तक) ?</p>",
                    options_en: [" 18.2%", " 16.8%", 
                                " 29.1%", " 16.1%"],
                    options_hi: [" 18.2%", " 16.8%",
                                " 29.1%", " 16.1%"],
                    solution_en: "3.(d)<br />% of students who appeared for the exam in section E = <math display=\"inline\"><mfrac><mrow><mn>58</mn><mo>°</mo></mrow><mrow><mn>360</mn><mo>°</mo></mrow></mfrac></math>×100 =  16.1%",
                    solution_hi: "3.(d)<br />सेक्शन E में परीक्षा देने वाले छात्रों का % = <math display=\"inline\"><mfrac><mrow><mn>58</mn><mo>°</mo></mrow><mrow><mn>360</mn><mo>°</mo></mrow></mfrac></math>×100 =  16.1%",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "17",
                    question_en: "<p>4. The pie chart given below shows the monthly expenditure of a family (in rupees) on various items.If the total earning is ₹70,560, then find the difference between the amount spent on Education and Rent.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1732689514584.png\" alt=\"rId11\" width=\"211\" height=\"203\"></p>",
                    question_hi: "<p>4. नीचे दिया गया पाई चार्ट विभिन्न मदों पर एक परिवार के मासिक खर्च (रुपये में) को दर्शाता है। यदि कुल आय ₹70,560 है, तो शिक्षा और किराए पर खर्च की गई राशि के बीच अंतर ज्ञात करें।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1732689514778.png\" alt=\"rId12\" width=\"211\" height=\"204\"></p>",
                    options_en: ["<p>₹7,804</p>", "<p>₹8,407</p>", 
                                "<p>₹7,056</p>", "<p>₹8,047</p>"],
                    options_hi: ["<p>₹7,804</p>", "<p>₹8,407</p>",
                                "<p>₹7,056</p>", "<p>₹8,047</p>"],
                    solution_en: "<p>4.(c)<br>Difference between the amount spent on Education and Rent <br>= 70,560&times;(30-20)% = 70560&times;10% = ₹7056</p>",
                    solution_hi: "<p>4.(c)<br>शिक्षा और किराये पर खर्च की गई राशि के बीच अंतर <br>= 70,560&times;(30-20)% = 70560&times;10% = ₹7056</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "17",
                    question_en: "<p>5. The following table shows the production of some products. What is the value of \'X\'?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1732689514893.png\" alt=\"rId13\" width=\"419\" height=\"100\"></p>",
                    question_hi: "<p>5. नीचे दी गई तालिका में कुछ उत्पादों के उत्पादन को दिखाया गया है। &lsquo;X\' का मान ज्ञात करें।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1732689515011.png\" alt=\"rId14\" width=\"322\" height=\"102\"></p>",
                    options_en: ["<p>15</p>", "<p>16</p>", 
                                "<p>17</p>", "<p>18</p>"],
                    options_hi: ["<p>15</p>", "<p>16</p>",
                                "<p>17</p>", "<p>18</p>"],
                    solution_en: "<p>5.(c)<br>According to the question,<br>15 + <math display=\"inline\"><mi>x</mi></math> + 13 + 16 = 61<br>44 + <math display=\"inline\"><mi>x</mi></math> = 61<br><math display=\"inline\"><mi>x</mi></math> = 61 - 44 = 17</p>",
                    solution_hi: "<p>5.(c)<br>प्रश्न के अनुसार,<br>15 + <math display=\"inline\"><mi>x</mi></math> + 13 + 16 = 61<br>44 + <math display=\"inline\"><mi>x</mi></math> = 61<br><math display=\"inline\"><mi>x</mi></math> = 61 - 44 = 17</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "17",
                    question_en: "<p>6. The bar graph given below shows sales of table fans (in thousand numbers) from five&nbsp;showrooms during two consecutive years 2021 and 2022. The total sales of showroom B for both years is what per cent of the total sales of showroom E for both years?<br>(Rounded off to 2 decimal places)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1732689515186.png\" alt=\"rId15\" width=\"318\" height=\"230\"></p>",
                    question_hi: "<p>6. नीचे दिया गया बार ग्राफ दो क्रमागत वर्षों 2021 और 2022 के दौरान पांच शोरूम से टेबल फैन (हजार संख्या में) की बिक्री को दर्शाता है। दोनों वर्षों में शोरूम B की कुल बिक्री, दोनों वर्षों में शोरूम E की कुल बिक्री का कितना प्रतिशत है? (दो दशमलव स्थानों तक पूर्णांकित)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1732689515186.png\" alt=\"rId15\" width=\"318\" height=\"230\"></p>",
                    options_en: ["<p>0.7499</p>", "<p>0.7694</p>", 
                                "<p>0.7869</p>", "<p>0.7949</p>"],
                    options_hi: ["<p>0.7499</p>", "<p>0.7694</p>",
                                "<p>0.7869</p>", "<p>0.7949</p>"],
                    solution_en: "<p>6.(d)<br>The total sales of showroom B for both years = 70 + 85 = 155<br>The total sales of showroom E for both years = 90 + 105 = 195<br>Ratio = 155 : 195 = 31 : 39<br>Required % = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>31</mn><mn>39</mn></mfrac></math>&times; 100 =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>3100</mn><mn>39</mn></mfrac></mstyle></math> = 79.49% = 0.7949</p>",
                    solution_hi: "<p>6.(d)<br>दोनों वर्षों में शोरूम B की कुल बिक्री = 70 + 85 = 155<br>दोनों वर्षों के लिए शोरूम E की कुल बिक्री = 90 + 105 = 195<br>अनुपात = 155 : 195 = 31 : 39<br>आवश्यक % = <math display=\"inline\"><mfrac><mrow><mn>31</mn></mrow><mrow><mn>39</mn></mrow></mfrac></math> &times; 100 =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>3100</mn><mn>39</mn></mfrac></mstyle></math> = 79.49% = 0.7949</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "17",
                    question_en: "<p>7. The following pie charts show the data of the number of appeared and passed students of class 12 in sections A,B,C,D and E.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1732689513965.png\" alt=\"rId7\" width=\"213\" height=\"201\"><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1732689514070.png\" alt=\"rId8\" width=\"208\" height=\"195\"> <br>Find the difference between the number of students who appeared for the exam in sections A and B</p>",
                    question_hi: "<p>7. निम्न पाई चार्ट कक्षा 12 के सेक्शन A, B, C, D और E में उपस्थित और उत्तीर्ण छात्रों की संख्या का डेटा दर्शाते हैं।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1732689514336.png\" alt=\"rId9\" width=\"207\" height=\"201\"><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1732689514477.png\" alt=\"rId10\" width=\"205\" height=\"201\"> <br>सेक्शन A और B में परीक्षा में उपस्थित छात्रों की संख्या के बीच अंतर ज्ञात कीजिए।</p>",
                    options_en: ["<p>30</p>", "<p>60</p>", 
                                "<p>50</p>", "<p>40</p>"],
                    options_hi: ["<p>30</p>", "<p>60</p>",
                                "<p>50</p>", "<p>40</p>"],
                    solution_en: "<p>7.(d)<br>Required difference = <math display=\"inline\"><mfrac><mrow><mn>1800</mn></mrow><mrow><mn>360</mn><mo>&#176;</mo></mrow></mfrac></math> &times; ( 50 - 42 )&deg; =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>1800</mn><mrow><mn>360</mn><mo>&#176;</mo></mrow></mfrac></mstyle></math>&times; 8&deg; = 40</p>",
                    solution_hi: "<p>7.(d)<br>आवश्यक अंतर = <math display=\"inline\"><mfrac><mrow><mn>1800</mn></mrow><mrow><mn>360</mn><mo>&#176;</mo></mrow></mfrac></math> &times; ( 50 - 42 )&deg; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>1800</mn><mrow><mn>360</mn><mo>&#176;</mo></mrow></mfrac></mstyle></math>&times; 8&deg; = 40</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "17",
                    question_en: "<p>8. The pie chart given below shows the percentage of 8000 students in terms of the subject of specialisation in MBA.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1732689515327.png\" alt=\"rId16\" width=\"212\" height=\"210\"> <br>What is the total number of students specialising in IB?</p>",
                    question_hi: "<p>8. नीचे दिया गया पाई चार्ट MBA में विशेषज्ञता के विषय के संदर्भ में 8000 छात्रों का प्रतिशत दर्शाता है। <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1732689515453.png\" alt=\"rId17\" width=\"226\" height=\"213\"> <br>IB = आईबी IR = आईआर Finance = वित्त Marketing = विपणन HR = एचआर IT = आईटी <br>आईबी में विशेषज्ञता प्राप्त छात्रों की कुल संख्या कितनी है?</p>",
                    options_en: ["<p>1520</p>", "<p>1450</p>", 
                                "<p>1625</p>", "<p>1850</p>"],
                    options_hi: ["<p>1520</p>", "<p>1450</p>",
                                "<p>1625</p>", "<p>1850</p>"],
                    solution_en: "<p>8.(a)<br>Total number of students specialising in IB = 8000&times;<math display=\"inline\"><mfrac><mrow><mn>19</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 1520</p>",
                    solution_hi: "<p>8.(a)<br>आईबी में विशेषज्ञता प्राप्त छात्रों की कुल संख्या = 8000 &times; <math display=\"inline\"><mfrac><mrow><mn>19</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 1520</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "17",
                    question_en: "<p>9. Who among the following scored the maximum overall percentage?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1732689515647.png\" alt=\"rId18\" width=\"313\" height=\"165\"></p>",
                    question_hi: "<p>9. निम्नलिखित में से किसने अधिकतम कुल प्रतिशत प्राप्त किया?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1732689515788.png\" alt=\"rId19\" width=\"273\" height=\"206\"></p>",
                    options_en: ["<p>Simran</p>", "<p>Jyoti</p>", 
                                "<p>Sanjana</p>", "<p>Bhumika</p>"],
                    options_hi: ["<p>सिमरन</p>", "<p>ज्योति</p>",
                                "<p>संजना</p>", "<p>भूमिका</p>"],
                    solution_en: "<p>9.(b)<br>Marks scored by Simran = 78+65+89+78+66 = 376<br>Overall% = <math display=\"inline\"><mfrac><mrow><mn>376</mn></mrow><mrow><mn>500</mn></mrow></mfrac></math>&times;100 = 75.2%<br>Marks scored by Jyoti = 69+90+93+88+92 = 432<br>Overall % = <math display=\"inline\"><mfrac><mrow><mn>432</mn></mrow><mrow><mn>500</mn></mrow></mfrac></math>&times;100 = 86.4%<br>Marks scored by Sanjana = 87+70+95+84+84 = 420<br>Overall % = <math display=\"inline\"><mfrac><mrow><mn>420</mn></mrow><mrow><mn>500</mn></mrow></mfrac></math>&times;100 = 84%<br>Marks scored by Bhumika = 74+84+88+87+79 = 412<br>Overall % = <math display=\"inline\"><mfrac><mrow><mn>412</mn></mrow><mrow><mn>500</mn></mrow></mfrac></math>&times;100 = 82.4%<br>Clearly, we can see that Jyoti scored the maximum overall percentage.</p>",
                    solution_hi: "<p>9.(b)<br>सिमरन द्वारा प्राप्त अंक = 78+65+89+78+66 = 376<br>कुल मिलाकर% = <math display=\"inline\"><mfrac><mrow><mn>376</mn></mrow><mrow><mn>500</mn></mrow></mfrac></math>&times;100 = 75.2%<br>ज्योति द्वारा प्राप्त अंक= 69+90+93+88+92 = 432<br>कुल मिलाकर % = <math display=\"inline\"><mfrac><mrow><mn>432</mn></mrow><mrow><mn>500</mn></mrow></mfrac></math>&times;100 = 86.4%<br>संजना द्वारा प्राप्त अंक= 87+70+95+84+84 = 420<br>कुल मिलाकर % = <math display=\"inline\"><mfrac><mrow><mn>420</mn></mrow><mrow><mn>500</mn></mrow></mfrac></math>&times;100 = 84%<br>भूमिका द्वारा प्राप्त अंक = 74+84+88+87+79 = 412<br>कुल मिलाकर % = <math display=\"inline\"><mfrac><mrow><mn>412</mn></mrow><mrow><mn>500</mn></mrow></mfrac></math>&times;100 = 82.4%<br>स्पष्ट रूप से, हम देख सकते हैं कि ज्योति ने अधिकतम कुल प्रतिशत प्राप्त किया।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "17",
                    question_en: "<p>10. A tractor manufacturing company sells each tractor for ₹5,00,000. Assuming that KISAN firm buys 50 tractors from them as a part of an annual contract, the company offers a trade discount of 10% to KISAN and an additional 2% discount if the payment is made within 30 days. What will be the amount payable by KISAN within 30 days for the consignment?</p>",
                    question_hi: "<p>10. एक ट्रैक्टर निर्माता कंपनी प्रत्येक ट्रैक्टर को ₹5,00,000 में बेचती है। यह मानते हुए कि किसान फर्म&nbsp;वार्षिक अनुबंध के हिस्से के रूप में उनसे 50 ट्रैक्टर खरीदती है, कंपनी किसान को 10% की व्&zwj;यापारिक छूट और 30 दिन के भीतर भुगतान किए जाने पर अतिरिक्त 2% छूट प्रदान करती है। प्रेषित माल के लिए 30 दिन के भीतर किसान फर्म द्वारा देय राशि कितनी होगी?</p>",
                    options_en: ["<p>₹22,080,100</p>", "<p>₹24,050,020</p>", 
                                "<p>₹20,051,000</p>", "<p>₹22,050,000</p>"],
                    options_hi: ["<p>₹22,080,100</p>", "<p>₹24,050,020</p>",
                                "<p>₹20,051,000</p>", "<p>₹22,050,000</p>"],
                    solution_en: "<p>10.(d)<br>Price of 50 tractors = 5,00,000&times;50 = ₹2,50,00,000<br>Required amount payable by Kisan = 2,50,00,000&times;<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>90</mn><mn>100</mn></mfrac></math>&times;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>98</mn><mn>100</mn></mfrac></mstyle></math>= ₹22,050,000</p>",
                    solution_hi: "<p>10.(d)<br>50 ट्रैक्टरों की कीमत = 5,00,000&times;50 = ₹2,50,00,000<br>किसान द्वारा देय राशि = = 2,50,00,000&times;<math display=\"inline\"><mfrac><mrow><mn>90</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math>&times;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>98</mn><mn>100</mn></mfrac></mstyle></math>= ₹22,050,000</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "17",
                    question_en: "<p>11. From the following table, find is the average weight (in kg) of the given persons?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1732689515943.png\" alt=\"rId20\" width=\"407\" height=\"50\"></p>",
                    question_hi: "<p>11. नीचे दी गई तालिका के आधार पर, उसमें दिए गए व्यक्तियों का औसत वजन (kg में) ज्ञात करें। <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1732689516078.png\" alt=\"rId21\" width=\"357\" height=\"64\"></p>",
                    options_en: ["<p>54.33</p>", "<p>55.5</p>", 
                                "<p>56.0</p>", "<p>56.33</p>"],
                    options_hi: ["<p>54.33</p>", "<p>55.5</p>",
                                "<p>56.0</p>", "<p>56.33</p>"],
                    solution_en: "<p>11.(c)<br>Let average weight of given person be 55 kg<br>Sum of the Deviation in the weight of the given persons = (-2.3)+(-1)+0.2+2.4+3.7+3 = 6<br>So, average weight = 55+<math display=\"inline\"><mfrac><mrow><mn>6</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math> = 55+1 = 56 kg</p>",
                    solution_hi: "<p>11.(c)<br>माना दिए गए व्यक्ति का औसत वजन 55 किलोग्राम है<br>दिए गए व्यक्तियों के वजन में विचलन का योग= (-2.3)+(-1)+0.2+2.4+3.7+3 = 6<br>तो, औसत वजन = 55+<math display=\"inline\"><mfrac><mrow><mn>6</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math> = 55+1 = 56 kg</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "17",
                    question_en: "<p>12. The marks (out of 150) of five students in different subjects are given in the following table.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1732689516205.png\" alt=\"rId22\" width=\"395\" height=\"138\"> <br>The percentage of marks scored by Ravi in Mathematics is:</p>",
                    question_hi: "<p>12. निम्न तालिका में एक परीक्षा में विभिन्न विषयों में पांच विद्यार्थियों के अंक (150 में से) दिए गए हैं।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1732689516339.png\" alt=\"rId23\" width=\"373\" height=\"199\"> <br>रवि द्वारा गणित में प्राप्त अंकों का प्रतिशत कितना है?</p>",
                    options_en: ["<p>91</p>", "<p>92</p>", 
                                "<p>94</p>", "<p>97</p>"],
                    options_hi: ["<p>91</p>", "<p>92</p>",
                                "<p>94</p>", "<p>97</p>"],
                    solution_en: "<p>12.(c)<br>% of marks scored by Ravi in Mathematics = <math display=\"inline\"><mfrac><mrow><mn>141</mn></mrow><mrow><mn>150</mn></mrow></mfrac></math> &times; 100 = 94%</p>",
                    solution_hi: "<p>12.(c)<br>गणित में रवि द्वारा प्राप्त अंकों का % = <math display=\"inline\"><mfrac><mrow><mn>141</mn></mrow><mrow><mn>150</mn></mrow></mfrac></math> &times; 100 = 94%</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "17",
                    question_en: "<p>13. The pie-chart below illustrates the different fruit yields from a farmer\'s land that were sold to a fruit shop during a specific week. study the chart and answer the question that follows.<br><img src=\"data:image/png;base64,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\" width=\"261\" height=\"232\"><br>If a total of 900 kg of fruits were sold that week, find the difference in sales (in kg) between oranges and grapes.</p>",
                    question_hi: "<p>13. नीचे दिया गया पाई-चार्ट एक किसान के खेत से विभिन्न फलों की पैदावार को दर्शाता है, जो एक विशिष्ट सप्ताह के दौरान एक फल की दुकान को बेची गई थीं। चार्ट का अध्ययन करें और निम्नलिखित प्रश्न का उत्तर दें।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1732689516559.png\" alt=\"rId25\" width=\"237\" height=\"223\"> <br>यदि उस सप्ताह कुल 900 KG फल बेचे गए, तो संतरे और अंगूर के बीच बिक्री में अंतर (KG में) ज्ञात कीजिए।</p>",
                    options_en: ["<p>60</p>", "<p>75</p>", 
                                "<p>70</p>", "<p>45</p>"],
                    options_hi: ["<p>60</p>", "<p>75</p>",
                                "<p>70</p>", "<p>45</p>"],
                    solution_en: "<p>13.(d)<br>Required difference = 900&times; <math display=\"inline\"><mfrac><mrow><mo>(</mo><mn>15</mn><mo>-</mo><mn>10</mn><mo>)</mo><mi>%</mi></mrow><mrow><mn>100</mn><mi>%</mi></mrow></mfrac></math> = 45</p>",
                    solution_hi: "<p>13.(d)<br>आवश्यक अंतर = 900&times; <math display=\"inline\"><mfrac><mrow><mo>(</mo><mn>15</mn><mo>-</mo><mn>10</mn><mo>)</mo><mi>%</mi></mrow><mrow><mn>100</mn><mi>%</mi></mrow></mfrac></math> = 45</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "17",
                    question_en: "<p>14. The pie chart below shows the distribution of female employees working in a company in five different districts (A, B, C, D and E).<br>Total number of female employees in five districts-136000<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1732689516643.png\" alt=\"rId26\" width=\"412\" height=\"248\"> <br>What is the total number of female employees working in a company in district C and district D?</p>",
                    question_hi: "<p>14. नीचे दिया गया पाई चार्ट पांच अलग-अलग जिलों (A, B, C, D और E) में एक कंपनी में कार्यरत महिला कर्मचारियों के बंटन को दर्शाता है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1732689516773.png\" alt=\"rId27\" width=\"362\" height=\"248\"> <br>पांच जिलों में महिला कर्मचारियों की कुल संख्या = 136000<br>जिला C और जिला D में कंपनी में कार्यरत महिला कर्मचारियों की कुल संख्या क्या है?</p>",
                    options_en: ["<p>61000</p>", "<p>61200</p>", 
                                "<p>62290</p>", "<p>62000</p>"],
                    options_hi: ["<p>61000</p>", "<p>61200</p>",
                                "<p>62290</p>", "<p>62000</p>"],
                    solution_en: "<p>14.(b)<br>Required no of people = 1,36,000&times;(30+15)% = 1,36,000&times;<math display=\"inline\"><mfrac><mrow><mn>45</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 61,200</p>",
                    solution_hi: "<p>14.(b)<br>आवश्यक व्यक्तियों की संख्या = 1,36,000&times;(30+15)% = 1,36,000&times;<math display=\"inline\"><mfrac><mrow><mn>45</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 61,200</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "misc",
                    question_en: "<p>15. The following table gives the number of post - graduate students enrolled in 4 different colleges A, B, C, and D in a city over the years 2015-2018 and also the number of students who passed the final examination during these years. Study the table carefully and answer the question.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1732689516891.png\" alt=\"rId28\" width=\"467\" height=\"99\"> <br>Find the percentage of students who passed from college C over all the years put together to the number of students enrolled in college A over all the years (rounded off to 2 decimal places).</p>",
                    question_hi: "<p>15. निम्नलिखित तालिका वर्ष 2015-2018 के दौरान एक शहर के 4 अलग-अलग कॉलेजों A, B, C और D में नामांकित स्नातकोत्तर विद्यार्थियों की संख्या और इन वर्षों के दौरान अंतिम परीक्षा उत्तीर्ण करने वाले विद्यार्थियों की संख्या भी दर्शाती है। तालिका का ध्यानपूर्वक अध्ययन कीजिए और प्रश्न का उत्तर दीजिए।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1732689516991.png\" alt=\"rId29\" width=\"497\" height=\"129\"> <br>सभी वर्षों में एक साथ मिलाकर कॉलेज A में नामांकित विद्यार्थियों की संख्या की तुलना में सभी वर्षों में एक साथ मिलाकर कॉलेज C से उत्तीर्ण होने वाले विद्यार्थियों का प्रतिशत (2 दशमलव स्थान तक पूर्णांकित) ज्ञात कीजिए।</p>",
                    options_en: ["<p>1.0321</p>", "<p>1.0112</p>", 
                                "<p>1.0212</p>", "<p>1.0021</p>"],
                    options_hi: ["<p>1.0321</p>", "<p>1.0112</p>",
                                "<p>1.0212</p>", "<p>1.0021</p>"],
                    solution_en: "<p>15.(a)<br>Total no of students who passed from college C over all the years = 720+780+850+870 = 3220<br>Total no of students enrolled in college A over all the years = 720+650+850+900 = 3120<br>Required % = <math display=\"inline\"><mfrac><mrow><mn>3220</mn></mrow><mrow><mn>3120</mn></mrow></mfrac></math>&times;100 &asymp; 103.21% = 1.0321</p>",
                    solution_hi: "<p>15.(a)<br>सभी वर्षों में कॉलेज C से उत्तीर्ण छात्रों की कुल संख्या = 720+780+850+870 = 3220<br>सभी वर्षों में कॉलेज A में नामांकित छात्रों की कुल संख्या = 720+650+850+900 = 3120<br>आवश्यक % = <math display=\"inline\"><mfrac><mrow><mn>3220</mn></mrow><mrow><mn>3120</mn></mrow></mfrac></math>&times;100 &asymp; 103.21% = 1.0321</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>