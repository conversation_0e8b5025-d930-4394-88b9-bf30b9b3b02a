<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">8:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 15</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">15</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 8 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["18"] = {
                name: "General Awareness",
                start: 0,
                end: 13
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="18">General Awareness</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 14,
                end: 14
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "18",
                    question_en: "<p>1. In which of the following locations was &lsquo;Nataraja&rsquo;, a stone male dancing figure discovered?</p>",
                    question_hi: "<p>1. निम्नलिखित में से किस स्थान पर \'नटराज\', एक पाषाण पुरुष नृत्य करने वाली आकृति की खोज की गई थी?</p>",
                    options_en: ["<p>Harappa</p>", "<p>Lothal</p>", 
                                "<p>Rangpur</p>", "<p>Mohenjo-daro</p>"],
                    options_hi: ["<p>हड़प्पा</p>", "<p>लोथल</p>",
                                "<p>रंगपुर</p>", "<p>मोहन जोदड़ो</p>"],
                    solution_en: "<p>1.(a) <strong>Harappa</strong>. The first extensive excavation at Harappa was started by Daya Ram Sahni in 1920. <strong>Lothal</strong>: Location - Gujarat. Discovered in 1954 by archaeologist S.R. Rao. Found: Dockyard, Granary etc. <strong>Mohenjo-daro: </strong>Located in the Sindh province of Pakistan. It was discovered in 1922 by R. D. Banerji. <strong>Excavations at Mohenjo-daro:</strong> Urban planning: Mohenjo-daro was a well-planned city, with streets laid out in a grid pattern and buildings constructed using standardized brick sizes. The city was divided into two main sections: a lower town and a citadel. <strong>Great Bath:</strong> One of the most impressive structures at Mohenjo-daro.</p>",
                    solution_hi: "<p>1.(a) <strong>हड़प्पा</strong>। हड़प्पा की पहली व्यापक खुदाई दया राम साहनी द्वारा 1920 में शुरू की गई थी। <strong>लोथल</strong>: स्थान - गुजरात। पुरातत्वविद् एस.आर.राव द्वारा 1954 में खोजा गया। प्राप्त हुआ : गोदी, अन्न भंडार आदि।) मोहनजोदड़ो पाकिस्तान के सिंध प्रांत में स्थित है। इसकी खोज 1922 में आर. डी. बनर्जी ने की थी।) <strong>मोहनजोदड़ो का उत्खनन: शहरी नियोजन:</strong>) मोहनजोदड़ो एक सुनियोजित शहर था, जिसमें ग्रिड पद्धति से सड़कें बनाई गई थीं और इमारतों का निर्माण मानव द्वारा निर्मित ईंट का उपयोग करके किया गया था। शहर को दो मुख्य भागों में बांटा गया था: एक निचला शहर और दूसरा गढ़।<strong> महान स्नानागार: </strong>मोहन जोदड़ो की सबसे प्रभावशाली संरचना है ।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "2",
                    section: "18",
                    question_en: "<p>2. Which of the following sites is <strong>NOT</strong> a part of the Indus Valley Civilization?</p>",
                    question_hi: "<p>2. निम्नलिखित में से कौन सा स्थल सिंधु घाटी सभ्यता का हिस्सा <strong>नहीं </strong>है?</p>",
                    options_en: ["<p>Harappa</p>", "<p>Uruk</p>", 
                                "<p>Lothal</p>", "<p>Mohenjo-Daro</p>"],
                    options_hi: ["<p>हड़प्पा</p>", "<p>उरुक</p>",
                                "<p>लोथल</p>", "<p>मोहनजोदड़ो</p>"],
                    solution_en: "<p>2.(b) <strong>Uruk </strong>(Warka) is an ancient mesopotamia site located in Iraq .<strong> Important Indus Valley Civilisation Sites: </strong>Pakistan (Harappa, Ganeriwala, Allahdino, Mehrgarh, and Mohenjo-daro), India (Dholavira, Kalibangan, Rakhigarhi, Ropar, Lothal and Babar Kot), and <strong>Afghanistan </strong>(Shortugai).</p>",
                    solution_hi: "<p>2.(b) <strong>उरुक </strong>(वार्का) इराक में स्थित एक प्राचीन मेसोपोटामिया स्थल है।<strong> महत्वपूर्ण सिंधु घाटी सभ्यता के स्थल: पाकिस्तान</strong> (हड़प्पा, गनेरीवाला, अल्लाहदीनो, मेहरगढ़, और मोहनजोदड़ो), भारत (धोलावीरा, कालीबंगन, राखीगढ़ी, रोपड़, लोथल और बाबर कोट), और <strong>अफगानिस्तान </strong>(शॉर्टुगई)।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "3",
                    section: "18",
                    question_en: "<p>3. The famous Indus Valley site Mohenjo - daro was first excavated by the eminent Indian archaeologist.</p>",
                    question_hi: "<p>3. प्रसिद्ध सिंधु घाटी स्थल मोहनजोदड़ो की खुदाई सबसे पहले किस प्रख्यात भारतीय पुरातत्वविद् ने की थी।</p>",
                    options_en: ["<p>B B Lal</p>", "<p>R D Banerji</p>", 
                                "<p>Daya Ram Sahni</p>", "<p>SR Rao </p>"],
                    options_hi: ["<p>बी बी लाल</p>", "<p>आर.डी. बनर्जी</p>",
                                "<p>दया राम साहनी</p>", "<p>एस.आर. राव</p>"],
                    solution_en: "<p>3.(b) <strong>R. D. Banerji. Mohenjodaro -</strong> Situated on the Bank of river Indus in Larkana district in Punjab. Discovered in - 1922. <strong>Indus Valley Civilization:</strong> Discovered - John Marshall. <strong>Majar Sites - Harappa: </strong>Situated on the bank of river Ravi in Punjab (Pakistan) extensively Excavated by - Daya Ram Sahni. Kalibanga (Rajasthan) - Amlanand Ghose. Lothal (Gujarat) - S.R. Rao. Dholavira (Gujarat) - R.S Bisht.</p>",
                    solution_hi: "<p>3.(b) <strong>आर. डी. बनर्जी। मोहनजोदड़ो - </strong>पंजाब प्रांत में लरकाना जिले में सिंधु नदी के तट पर स्थित है। खोजा गया - 1922 में। सिंधु घाटी सभ्यता: <strong>खोजकर्ता </strong>- जॉन मार्शल। <strong>प्रमुख स्थल - हड़प्पा: </strong>पंजाब (पाकिस्तान) में रावी नदी के तट पर स्थित, बड़े पैमाने पर उत्खनन कराया - दया राम साहनी के द्वारा। कालीबंगा (राजस्थान) - अमलानंद घोष। लोथल (गुजरात) - एस.आर. राव. धोलावीरा (गुजरात) - आर.एस.बिष्ट।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "4",
                    section: "18",
                    question_en: "<p>4. Which of the following Harappan site is <strong>NOT</strong> associated with craft production?</p>",
                    question_hi: "<p>4. निम्नलिखित में से कौन सा हड़प्पा स्थल शिल्प उत्पादन से जुड़ा <strong>नहीं</strong> है?</p>",
                    options_en: ["<p>Chanhudaro</p>", "<p>Balakot</p>", 
                                "<p>Nageshwar</p>", "<p>Manda</p>"],
                    options_hi: ["<p>चन्हुदड़ो</p>", "<p>बालाकोट</p>",
                                "<p>नागेश्वर</p>", "<p>मांडा</p>"],
                    solution_en: "<p>4.(d) <strong>Manda (Jammu &amp; Kashmir): </strong>Excavated by <strong>J. P. Joshi. Chanhudaro</strong> (Sindh, Pakistan) - Excavated by N.G Majumdar in 1931. Finding - Bead makers shop, Footprint of Dog chasing a Cat. <strong>Balakot (Pakistan):</strong> Excavated by Robert Raikes. <strong>Nageshwar (Gujarat) -</strong> Specialised centre for making shell objects (Bangles, ladles and inlay).</p>",
                    solution_hi: "<p>4.(d) <strong>माँडा (जम्मू और कश्मीर)।</strong>जे.पी. जोशी द्वारा उत्खनन कार्य करवाया गया । चन्हुदड़ो (सिंध, पाकिस्तान) - 1931 में एन.जी. मजूमदार द्वारा उत्खनन। खोज - मनका बनाने वाली कारखाने , बिल्ली का पीछा करते कुत्ते के पदचिह्न। <strong>बालाकोट (पाकिस्तान): </strong>रॉबर्ट राईक्स द्वारा उत्खनन। <strong>नागेश्वर (गुजरात) </strong>- शंख की वस्तुएं (चूड़ियाँ, करछुल और जड़ाऊ) बनाने का विशेष केन्द्र।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "5",
                    section: "18",
                    question_en: "<p>5. In 1994 who took over as the Director General of the Archaeological Survey of India and undertook the excavations of Harappa?</p>",
                    question_hi: "<p>5. 1994 में किसने भारतीय पुरातत्व सर्वेक्षण के महानिदेशक के रूप में पदभार ग्रहण किया और हड़प्पा की खुदाई का कार्य किया?</p>",
                    options_en: ["<p>REM Wheeler</p>", "<p>Rakhal Das Banerji</p>", 
                                "<p>John Marshall</p>", "<p>Daya Ram Sahni</p>"],
                    options_hi: ["<p>REM व्हीलर</p>", "<p>राखल दास बनर्जी</p>",
                                "<p>जॉन मार्शल</p>", "<p>दया राम साहनी</p>"],
                    solution_en: "<p>5.(a) <strong>REM Wheeler. Sir John Hubert Marshall:</strong> Director General of the Archaeological Survey of India (1902 - 1928). Discovered - Indus valley civilisation. <strong>Rakhal Das Banerji:</strong> Discovered Mohenjodaro in 1922. <strong>Dayaram Sahni:</strong> Discovered harappa in 1921-22.</p>",
                    solution_hi: "<p>5.(a) <strong>REM व्हीलर। सर जॉन ह्यूबर्ट मार्शल: </strong>भारतीय पुरातत्व सर्वेक्षण के महानिदेशक (1902 - 1928)। खोज की गई - सिन्धु घाटी सभ्यता। <strong>राखाल दास बनर्जी:</strong> 1922 में मोहनजोदड़ो की खोज की। <strong>दयाराम साहनी: </strong>1921-22 में हड़प्पा की खोज की।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "6",
                    section: "18",
                    question_en: "<p>6. Which one of the following animals was frequently seen on the seals of the Harappan Civilization?</p>",
                    question_hi: "<p>6. निम्नलिखित में से कौन सा जानवर हड़प्पा सभ्यता की मुहरों पर अक्सर देखा जाता था?</p>",
                    options_en: ["<p>Fox</p>", "<p>Deer</p>", 
                                "<p>Bull</p>", "<p>Lion</p>"],
                    options_hi: ["<p>लोमड़ी</p>", "<p>हिरन</p>",
                                "<p>साँड़</p>", "<p>शेर</p>"],
                    solution_en: "<p>6.(c) <strong>Bull</strong>. The seals of the Harappan Civilization (Indus valley civilization and Bronze age civilisation) - were also found outside India, \'Umma\' and \'Ur\' (Mesopotamian cities), Central Asia and on the coast of Arabian Peninsula. <strong>Shape </strong>- Rectangular, Circular or Cylindrical and Square. Other Animals depicted on Harappan seals - Rhinoceros, Elephants, Tiger, Unicorns and Bulls. <strong>Pashupati Seal - </strong>Discovered at Mohenjo Daro, Chief male deity Pasupati (proto-Siva) represented as sitting in a yogic posture with three faces and two horns.</p>",
                    solution_hi: "<p>6.(c) <strong>साँड़। </strong>हड़प्पा सभ्यता (सिंधु घाटी सभ्यता और कांस्य युगीन सभ्यता) की मुहरें - भारत के बाहर, उम्मा\' और \'उर\' (मेसोपोटामिया के शहर)\', मध्य एशिया और अरब प्रायद्वीप के तट पर भी पाई गईं। <strong>आकार </strong>- आयताकार, गोलाकार या बेलनाकार और वर्गाकार। हड़प्पा की मुहरों पर चित्रित अन्य जानवर - गैंडा, हाथी, बाघ, गेंडा और बैल।<strong> पशुपति मुहर - </strong>मोहनजोदड़ो में खोजी गई, मुख्य पुरुष देवता पशुपति (आद्य-शिव) को तीन चेहरों और दो सींगों के साथ योग मुद्रा में बैठे हुए दर्शाया गया है।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "7",
                    section: "18",
                    question_en: "<p>7. The first site discovered at the Indus Valley Civilization is.</p>",
                    question_hi: "<p>7. सिंधु सभ्यता में खोजा गया पहला स्थान कौन सा था ?</p>",
                    options_en: ["<p>Lothal</p>", "<p>Harappa</p>", 
                                "<p>Kalibangan</p>", "<p>Mohenjo - Daro</p>"],
                    options_hi: ["<p>लोथल</p>", "<p>हड़प्पा</p>",
                                "<p>कालीबंगा</p>", "<p>मोहनजोदड़ो</p>"],
                    solution_en: "<p>7.(b) <strong>Harappa </strong>(Ravi River): Located - Sahiwal District, Punjab (Pakistan). Excavated by Sir Alexander Cunningham in 1872-73. First extensively Excavated - <strong>1921 </strong>by Daya Ram Sahni. <strong>Indus Valley Civilization (2500 BC - 1750 BC):</strong> Discovered - John Marshall. <strong>Majar Sites: </strong>Mohenjodaro (Pakistan) - R.D Banerjee in 1922. Lothal (Gujarat) - R.Rao in 1953.</p>",
                    solution_hi: "<p>7.(b) <strong>हड़प्पा </strong>(रावी नदी): स्थित - साहीवाल जिला, पंजाब (पाकिस्तान)। 1872-73 में सर अलेक्जेंडर कनिंघम द्वारा उत्खनन किया गया। प्रथम व्यापक उत्खनन - <strong>1921</strong>, दया राम साहनी द्वारा। <strong>सिंधु घाटी सभ्यता (2500 ई.पू.- 1750 ई.पू.):</strong> खोज - जॉन मार्शल। <strong>प्रमुख स्थल: </strong>मोहनजोदड़ो (पाकिस्तान) - 1922 में आर.डी. बनर्जी। लोथल (गुजरात) - 1953 में आर.राव द्वारा ।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "8",
                    section: "18",
                    question_en: "<p>8. Which city from the Harappan Civilization was almost exclusively devoted to craft production including bead making, shell cutting, metal working, seal making and weight making?</p>",
                    question_hi: "<p>8. हड़प्पा सभ्यता से कौन सा शहर लगभग विशेष रूप से शिल्प उत्पादन के लिए समर्पित था जिसमें मनका बनाना, खोल काटना, धातु का काम करना, मुहर बनाना और वजन बढ़ानाशामिल था?</p>",
                    options_en: ["<p>Harappa</p>", "<p>Mohenjo Daro</p>", 
                                "<p>Nageshwar</p>", "<p>Chanhudaro</p>"],
                    options_hi: ["<p>हड़प्पा</p>", "<p>मोहनजोदड़ो</p>",
                                "<p>नागेश्वर</p>", "<p>चन्हुदड़ो</p>"],
                    solution_en: "<p>8.(d) <strong>Chanhudaro </strong>(in 1931 by N.G Majumdar). <strong>Other findings of Chanhudaro -</strong> Copper knives, spears, razors, tools, axes, vessels and dishes. <strong>Harappa </strong>(in 1921 by Dayaram Sahni) - Granaries, bullock-cart. <strong>Mohenjodaro </strong>(in 1922 by R.D. Banerjee) - Great bath, Seal of Pashupati. <strong>Other sites: Lothal </strong>(in 1953 by R.Rao) - Dockyard, fire altars). <strong>Kalibangan</strong> (in 1953 by Ghose) - Wooden plough, camel bones. <strong>Dholavira </strong>(in 1985 by R.S. Bisht) - Water harnessing and reservoir.</p>",
                    solution_hi: "<p>8.(d) <strong>चन्हुदड़ो </strong>(1931 में एन.जी. मजूमदार द्वारा खोजा गया )।<strong> चन्हुदड़ो की अन्य खोजें -</strong> तांबे के चाकू, भाले, छुरी, औजार, कुल्हाड़ी, बर्तन इत्यादि। <strong>हड़प्पा </strong>(1921 में दयाराम साहनी द्वारा खोजा गया) - अन्न भंडार, बैलगाड़ी। <strong>मोहनजोदड़ो </strong>(1922 में आर.डी. बनर्जी द्वारा खोजा गया ) - बड़ा स्नानघर, पशुपति की मुहर। <strong>अन्य स्थल: लोथल </strong>(1953 में आर.राव द्वारा) - गोदीघर, अग्निवेदियाँ)। <strong>कालीबंगन </strong>(अमलानन्द घोष द्वारा 1953 में में खोजा गया) - लकड़ी का हल, ऊँट की हड्डियाँ। <strong>धौलावीरा</strong>(1985 में R.S. बिष्ट द्वारा खोजा गया ) - जल संचयन एवं स्नानागार ।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "9",
                    section: "18",
                    question_en: "<p>9. Which out of these is the Harappan site discovered in Gujarat?</p>",
                    question_hi: "<p>9. इनमें से गुजरात में खोजा गया हड़प्पा स्थल कौन सा है?</p>",
                    options_en: ["<p>Dholavira</p>", "<p>Manda</p>", 
                                "<p>Balathal</p>", "<p>Khandia</p>"],
                    options_hi: ["<p>धौलावीरा</p>", "<p>मंडा</p>",
                                "<p>बालथल</p>", "<p>खंडिया</p>"],
                    solution_en: "<p>9.(a) <strong>Dholavira </strong>(discovered in 1968 by archaeologist Jagat Pati Joshi) is a UNESCO World Heritage Site. The Indus Valley Civilisation (<strong>Harappan Civilization</strong>) extended from modern-day northeast Afghanistan to Pakistan and northwest India. <strong>Major sites -</strong> Manda (Jammu and Kashmir), Harappa (Pakistan), Mohenjo-Daro (Pakistan), Kalibangan (Rajasthan), Rakhigarhi (Haryana).</p>",
                    solution_hi: "<p>9.(a) <strong>धौलावीरा </strong>(पुरातत्वविद् जगत पति जोशी द्वारा 1968 में खोजा गया) एक यूनेस्को विश्व धरोहर स्थल है। सिंधु घाटी सभ्यता (<strong>हड़प्पा सभ्यता)</strong> आधुनिक उत्तर-पूर्व अफगानिस्तान से लेकर पाकिस्तान और उत्तर-पश्चिम भारत तक फैली हुई थी। <strong>प्रमुख स्थल -</strong> मांडा (जम्मू और कश्मीर), हड़प्पा (पाकिस्तान), मोहनजोदड़ो (पाकिस्तान), कालीबंगा (राजस्थान), राखीगढ़ी (हरियाणा)।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "10",
                    section: "18",
                    question_en: "<p>10. Which of the following is the correct chronological sequence of pre-historic period of human activities and civilization?</p>",
                    question_hi: "<p>10. निम्नलिखित में से मानव गतिविधियों और सभ्यता के प्रागैतिहासिक काल का सही कालानुक्रमिक क्रम कौन सा है?</p>",
                    options_en: ["<p>Neolithic Period, Mesolithic Period, Palaeolithic Period</p>", "<p>Palaeolithic Period, Mesolithic Period, Neolithic Period</p>", 
                                "<p>Mesolithic Period, Neolithic Period, Palaeolithic Period</p>", "<p>Metal Age Period, Mesolithic Period, Palaeolithic Period</p>"],
                    options_hi: ["<p>नवपाषाण काल, मध्यपाषाण काल, पुरापाषाण काल</p>", "<p>पुरापाषाण काल, मध्यपाषाण काल, नवपाषाण काल</p>",
                                "<p>मध्य पाषाण काल, नवपाषाण काल, पुरापाषाण काल</p>", "<p>धातु युग, मध्य पाषाण काल, पुरापाषाण काल</p>"],
                    solution_en: "<p>10.(b) <strong>Palaeolithic Period, Mesolithic Period, Neolithic Period. </strong>The Palaeolithic Period - was an age of purely hunting and gathering. The Mesolithic Period - The development of agriculture contributed to the rise of permanent settlements. The Neolithic Period (New Stone Age) is the final stage of cultural evolution or technological development among prehistoric humans.</p>",
                    solution_hi: "<p>10.(b) <strong>पुरापाषाण काल, मध्यपाषाण काल, नवपाषाण काल। </strong>पुरापाषाण काल -<strong> </strong>विशुद्ध रूप से शिकार और संग्रहण का युग था। मध्यपाषाण काल - कृषि के विकास ने स्थायी बस्तियों के उदय में योगदान दिया। नवपाषाण काल (नव पाषाण युग) प्रागैतिहासिक मानवों के बीच सांस्कृतिक विकास या तकनीकी विकास का अंतिम चरण है।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "11",
                    section: "18",
                    question_en: "<p>11. In which year did a team of German and Italian archaeologists begin surface exploration at Mohenjodaro?</p>",
                    question_hi: "<p>11. जर्मन और इतालवी पुरातत्वविदों की एक टीम ने मोहनजोदड़ो में सतह की खोज किस वर्ष शुरू की थी?</p>",
                    options_en: ["<p>1970</p>", "<p>1990</p>", 
                                "<p>1980</p>", "<p>1955</p>"],
                    options_hi: ["<p>1970</p>", "<p>1990</p>",
                                "<p>1980</p>", "<p>1955</p>"],
                    solution_en: "<p>11.(c)<strong> 1980. Mohenjo-daro</strong> (The mound of the dead): Excavated by RD Banerjee in 1922. Located - Larkana District (Pakistan) on the bank of Indus. <strong>Major findings:</strong> Great bath, Granary, Unicorn Seals, Bronze dancing girl statue, Pashupati Seal etc. Important sites of Indus Valley Civilization - Harappa (Pakistan), Kalibangan (Rajasthan), Lothal (Gujarat), Chanhudaro (Pakistan), Dholavira (Gujarat), Banawali (Haryana), etc.</p>",
                    solution_hi: "<p>11.(c) <strong>1980। मोहनजोदड़ो </strong>(मृतकों का टीला): 1922 में RD बनर्जी द्वारा खुदाई की गई। सिंधु के तट पर लरकाना जिले (पाकिस्तान) में स्थित है। <strong>प्रमुख प्राप्तियां: </strong>महान स्नानागार, अन्नागार, यूनिकॉर्न मुहरें, कांस्य नृत्य करती लड़की की मूर्ति, पशुपति मूर्ति आदि। सिंधु घाटी सभ्यता के महत्वपूर्ण स्थल - हड़प्पा (पाकिस्तान), कालीबंगा (राजस्थान), लोथल (गुजरात), चन्हुदड़ो (पाकिस्तान), धोलावीरा (गुजरात), बनावली (हरियाणा), आदि।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "12",
                    section: "18",
                    question_en: "<p>12. Which material was used to make beads in the Harappan Civilisation?</p>",
                    question_hi: "<p>12. हड़प्पा सभ्यता में मोतियों को बनाने के लिए किस सामग्री का उपयोग किया जाता था?</p>",
                    options_en: ["<p>Wood</p>", "<p>Plastic</p>", 
                                "<p>Limestone</p>", "<p>Carnelian stone</p>"],
                    options_hi: ["<p>लकड़ी</p>", "<p>प्लास्टिक</p>",
                                "<p>चूना पत्थर</p>", "<p>कारेलियन पत्थर</p>"],
                    solution_en: "<p>12.(d) <strong>Carnelian stone. </strong>Harappan Sites discovered by - Dayaram Sahni (1921). <strong>Major findings from the sites:-</strong> Mohenjo Daro - Pashupati seal, Bronze dancing girl, Post cremation burial. Lothal - Rice husk, Double burial (male female together). Surkotada - Horse skeleton. Mohenjo Daro - Discovered by R D Banerjee (1922).</p>",
                    solution_hi: "<p>12.(d) <strong>कारेलियन पत्थर।</strong> हड़प्पा स्थलों की खोज - दयाराम साहनी (1921) द्वारा की गई। <strong>स्थलों से प्रमुख निष्कर्ष:- </strong>मोहनजोदड़ो <strong>-</strong> पशुपति मुहर, कांस्य की नर्तकी की प्रतिमा, अंतिम संस्कार के बाद दफन। लोथल - चावल की भूसी, युगल शवाधान(नर मादा एक साथ)। सुरकोटदा - घोड़े की अस्थियाँ। मोहनजोदड़ो - आर डी बनर्जी (1922) द्वारा खोजा गया ।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "13",
                    section: "18",
                    question_en: "<p>13. Mohenjo Daro is situated in:</p>",
                    question_hi: "<p>13. मोहनजोदड़ो _____ स्थित है?</p>",
                    options_en: ["<p>Punjab</p>", "<p>Khyber Pakhtunkhwa</p>", 
                                "<p>Sindh</p>", "<p>Balochistan</p>"],
                    options_hi: ["<p>पंजाब</p>", "<p>खैबर पख्तूनख्वा</p>",
                                "<p>सिंध</p>", "<p>बलूचिस्तान</p>"],
                    solution_en: "<p>13.(c) <strong>Sindh</strong>. Mohenjodaro (&lsquo;mound of the dead&rsquo;)is situated on the right bank of the Indus River, northern Sindh province, Pakistan. Mohenjo-daro was discovered in 1922 by R. D. Banerji. Harappa was the first site to be discovered in Indus valley civilization.</p>",
                    solution_hi: "<p>13.(c) <strong>सिंध</strong>। मोहनजोदड़ो (मृतकों का टीला\') पाकिस्तान के उत्तरी सिंध प्रांत में सिंधु नदी के दाहिने तट पर स्थित है। मोहनजोदड़ो की खोज 1922 में आर. डी. बनर्जी ने की थी। हड़प्पा सिंधु घाटी सभ्यता में खोजा जाने वाला प्रथम स्थल था।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "14",
                    section: "18",
                    question_en: "<p>14. In which state is the archaeological site of Surkotada situated?</p>",
                    question_hi: "<p>14. सुरकोतड़ा का पुरातात्विक स्थल किस राज्य में स्थित है?</p>",
                    options_en: ["<p>Punjab</p>", "<p>Gujarat</p>", 
                                "<p>Rajasthan</p>", "<p>Bihar</p>"],
                    options_hi: ["<p>पंजाब</p>", "<p>गुजरात</p>",
                                "<p>राजस्थान</p>", "<p>बिहार</p>"],
                    solution_en: "<p>14.(b) <strong>Gujarat</strong>. Indus Valley Civilisation was a Bronze Age Civilisation in the northwestern regions of South Asia. It was discovered in 1921 by Dayaram Sahni (The excavations were done under the guidance of Sir John Marshall and Colonel Meke). Archaeological Survey of India (ASI): Founder - Alexander Cunningham (1861), Headquarters - New Delhi, Official language - English, Hindi, Parent organization - Ministry of Culture.</p>",
                    solution_hi: "<p>14.(b) <strong>गुजरात </strong>। सिंधु घाटी सभ्यता दक्षिण एशिया के उत्तर-पश्चिमी क्षेत्रों में कांस्य युग की सभ्यता थी। इसकी खोज 1921 में दयाराम साहनी ने की थी (खुदाई सर जॉन मार्शल और कर्नल मेके के मार्गदर्शन में की गई थी)। भारतीय पुरातत्व सर्वेक्षण (ASI): संस्थापक - अलेक्जेंडर कनिंघम ( मु1861),ख्यालय - नई दिल्ली, आधिकारिक भाषा - अंग्रेजी, हिंदी, मूल संगठन - संस्कृति मंत्रालय।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "15",
                    section: "misc",
                    question_en: "<p>15. Which town of the Indus Valley Civilisation literally means &lsquo;mound of the dead&rsquo;?</p>",
                    question_hi: "<p>15. सिंधु घाटी सभ्यता के किस शहर का शाब्दिक अर्थ है \'मृतकों का टीला\'?</p>",
                    options_en: ["<p>Mohenjo-daro</p>", "<p>Mesopotamia</p>", 
                                "<p>Harappa</p>", "<p>Balakot </p>"],
                    options_hi: ["<p>मोहनजोदड़ो</p>", "<p>मेसोपोटामिया</p>",
                                "<p>हड़प्पा</p>", "<p>बालाकोट</p>"],
                    solution_en: "<p>15.(a) <strong>Mohenjo-Daro (discovered in 1922 by R. D. Banerji) - </strong>The largest city built in the Indus Valley civilization around 2500 BC, located west of the bank of the Indus River, Sindh (Pakistan). It was the largest settlement of the ancient Indus Valley Civilisation and one of the world\'s earliest major cities. <strong>Features </strong>: The town was divided into two parts, the world&rsquo;s best-channeled drainage system, multi-story buildings, The Great Bath, excellent craftsmen, People worshipped a figure which was similar to Shiva.</p>",
                    solution_hi: "<p>15.(a) <strong>मोहनजोदड़ो (आर. डी. बनर्जी द्वारा 1922 में खोज की गई थी ) -</strong> 2500 ईसा पूर्व के आसपास सिंधु घाटी सभ्यता में निर्मित सबसे बड़ा शहर, सिंधु नदी के तट के पश्चिम में, सिंध (पाकिस्तान) में स्थित है। यह प्राचीन सिंधु घाटी सभ्यता की सबसे बड़ी बस्ती थी और दुनिया के शुरुआती प्रमुख शहरों में से एक थी। <strong>विशेषताएं :&nbsp;</strong>शहर को दो भागों में विभाजित किया गया था, दुनिया की सबसे सुद्रण जल निकासी प्रणाली, बहुमंजिला इमारतें, विशाल स्नानागार , उत्कृष्ट कारीगर, लोग एक आकृति की पूजा करते थे जो शिव के समान थी।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>