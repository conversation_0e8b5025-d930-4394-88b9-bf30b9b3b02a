<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">8:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 15</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">15</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 8 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["18"] = {
                name: "General Awareness",
                start: 0,
                end: 13
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="18">General Awareness</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 14,
                end: 14
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "18",
                    question_en: "<p>1. Which of the following statements is CORRECT about EI Nino?</p>",
                    question_hi: "<p>1. निम्नलिखित में से कौन सा कथन अल नीनो के बारे में सही है?</p>",
                    options_en: ["<p>It is unusual warming of surface waters in the Pacific Ocean.</p>", "<p>India is not affected by EI Nino.</p>", 
                                "<p>It originates from the Mediterranean Sea.</p>", "<p>It is unusual cooling of surface waters in the Pacific Ocean.</p>"],
                    options_hi: ["<p>यह प्रशांत महासागर में सतही जल का असामान्य रूप से गर्म होना है</p>", "<p>भारत ईआई नीनो से प्रभावित नहीं है</p>",
                                "<p>यह भूमध्य सागर से निकलती है।</p>", "<p>यह प्रशांत महासागर में सतही जल का असामान्य रूप से ठंडा होना है।</p>"],
                    solution_en: "<p>1.(a) <strong>Option (a) is correct. El Nino and La Nina: </strong>These are climate patterns in the Pacific Ocean that can affect weather worldwide.<strong> El Nino (Warm phase)</strong> - It is the warming of surface waters in the eastern tropical Pacific Ocean. It is associated with weak monsoons and lower than average rainfall in India<strong>. La Nina (Cool phase) </strong>- It creates heavy monsoons throughout India and Southeast Asia.</p>",
                    solution_hi: "<p>1.(a) <strong>विकल्प (a) सही है। अल नीनो और ला नीना: </strong>ये प्रशांत महासागर में जलवायु पैटर्न हैं जो दुनिया भर के मौसम को प्रभावित कर सकते हैं।<strong> अल नीनो (उष्ण अवस्था) -</strong> यह पूर्वी उष्णकटिबंधीय प्रशांत महासागर में सतही जल का गर्म होना है। यह भारत में कमजोर मानसून और औसत से कम वर्षा से जुड़ा है। <strong>ला नीना</strong> (शीत अवस्था) - यह पूरे भारत और दक्षिण पूर्व एशिया में भारी मानसून पैदा करता है।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "2",
                    section: "18",
                    question_en: "<p>2. Which of the following statements about the Coriolis Force is INCORRECT?</p>",
                    question_hi: "<p>2. कोरिओलिस बल के बारे में निम्नलिखित में से कौन सा कथन गलत है?</p>",
                    options_en: ["<p>It is caused by difference in atmospheric pressure</p>", "<p>It is absent at the equator</p>", 
                                "<p>It is directly proportional to the angle of latitude</p>", "<p>It is maximum at the poles</p>"],
                    options_hi: ["<p>यह वायुमंडलीय दाब में अंतर के कारण होता है</p>", "<p>यह भूमध्य रेखा पर अनुपस्थित है</p>",
                                "<p>यह अक्षांश के कोण के अनुक्रमानुपाती होता है</p>", "<p>यह ध्रुवों पर अधिकतम होता है</p>"],
                    solution_en: "<p>2.(a) <strong>Coriolis force</strong> is an apparent force created due to the rotation of the earth about its axis. It is named after the French mathematician Gaspard Gustave de Coriolis. <strong>Examples:</strong> Cyclones and trade winds.</p>",
                    solution_hi: "<p>2.(a) <strong>कोरिओलिस बल </strong>पृथ्वी के अपनी धुरी पर घूमने के कारण उत्पन्न एक स्पष्ट बल है। इसका नाम फ्रांसीसी गणितज्ञ गैसपार्ड गुस्ताव डी कोरिओलिस के नाम पर रखा गया है। <strong>उदाहरण:</strong> चक्रवात और व्यापारिक पवने (trade wind)।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "3",
                    section: "18",
                    question_en: "<p>3. The pre-monsoon showers which are a common phenomenon in Kerala and the coastal areas of Karnataka are locally known as____.</p>",
                    question_hi: "<p>3. प्री-मानसून वर्षा जो केरल और कर्नाटक के तटीय क्षेत्रों में एक सामान्य घटना है, को स्थानीय रूप से किस नाम से जाना जाता है?</p>",
                    options_en: ["<p>mango showers</p>", "<p>winter showers</p>", 
                                "<p>Nor Westers</p>", "<p>blossom showers</p>"],
                    options_hi: ["<p>आम्र वर्षा</p>", "<p>शीतकालीन वर्षा</p>",
                                "<p>उत्तर पश्चिमी हवा</p>", "<p>ब्लॉसम वर्षा</p>"],
                    solution_en: "<p>3.(a) <strong>Mango showers: </strong>Towards the end of summer, in some coastal areas, the rain helps in ripening of mangoes.<strong> Blossom Shower </strong>- It makes coffee flowers blossom in Kerala and nearby areas. <strong>Norwester </strong>- These are thunderstorms in Bengal and Assam, which are useful for tea, jute, and rice cultivation.</p>",
                    solution_hi: "<p>3.(a) <strong>आम्र वर्षा :</strong> कुछ तटीय क्षेत्रों में, गर्मियों के अंत में, यह वर्षा आम को पकने में मदद करती है।<strong> ब्लॉसम वर्षा-</strong> इसमें केरल और आस-पास के क्षेत्रों में कॉफी के फूल खिलते है।<strong> उत्तर पश्चिमी हवा </strong>- ये बंगाल और असम में तूफान हैं, जो चाय, जूट और चावल की खेती के लिए उपयोगी हैं।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "4",
                    section: "18",
                    question_en: "4. Which among the following is the tallest tree species in the world?",
                    question_hi: "4. निम्नलिखित में से कौन विश्व की सबसे ऊँची वृक्ष प्रजाति है?",
                    options_en: [" Oak ", " Deodar ", 
                                " Redwood ", " Eucalyptus  "],
                    options_hi: ["  ओक", "  देवदार",
                                "  रेडवुड", "  यूकेलिप्टस"],
                    solution_en: "<p>4.(c) <strong>Redwood</strong> - It belongs to the cypress, or Cupressaceae family. <strong>Oak</strong> is a tree or shrub in the genus Quercus of the beech family, Fagaceae. Deodar trees belong to the Pinaceae (Pine) family. <strong>Eucalyptus</strong> globulus (southern blue gum) is a species of flowering plant in the family Myrtaceae.</p>",
                    solution_hi: "<p>4.(c) <strong>रेडवुड</strong> - यह साइप्रस या कूप्रेसाएसिए (Cupressaceae) परिवार से संबंधित है। <strong>ओक,</strong> बीच परिवार, फागेसी के जीनस क्वेरकस में एक पेड़ या झाड़ी है। <strong>देवदार</strong> के पेड़ पिनेसी (पाइन) परिवार के हैं। <strong>यूकेलिप्टस</strong> ग्लोब्युलस (दक्षिणी ब्लू गम) मायराटेसी परिवार में फूल पौधे की एक प्रजाति है।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "5",
                    section: "18",
                    question_en: "<p>5. What do we call the force which affects the direction of wind and is caused by the rotation of the Earth on its axis?</p>",
                    question_hi: "<p>5. हम उस बल को क्या कहते हैं जो हवा की दिशा को प्रभावित करता है और पृथ्वी के अपनी धुरी पर घूमने के कारण होता है?</p>",
                    options_en: ["<p>Frictional force</p>", "<p>Pressure gradient force</p>", 
                                "<p>Gravitational force</p>", "<p>Coriolis force</p>"],
                    options_hi: ["<p>घर्षण बल</p>", "<p>दाब प्रवणता बल</p>",
                                "<p>गुरुत्वाकर्षण बल</p>", "<p>कोरिओलिस बल</p>"],
                    solution_en: "<p>5.(d) <strong>Coriolis force</strong> - It is responsible for deflecting winds towards the right in the northern hemisphere and towards the left in the southern hemisphere. <strong>Pressure </strong>gradient force - Force that moves air from an area of high pressure to an area of low pressure. <strong>Frictional Force</strong> - An opposing force that opposes motion when the surface of a body comes into contact with that of another body. <strong>Gravitational force - </strong>A force that attracts any two objects in the universe, whether they have equal masses or not.</p>",
                    solution_hi: "<p>5.(d)<strong> कोरिओलिस बल </strong>- यह उत्तरी गोलार्ध में हवाओं को दाईं ओर और दक्षिणी गोलार्ध में बाईं ओर विक्षेपित करने के लिए उत्तरदायी है।<strong> दाब प्रवणता बल - </strong>वह बल जो हवा को उच्च दबाव वाले क्षेत्र से कम दबाव वाले क्षेत्र की ओर ले जाता है। <strong>घर्षण बल </strong>- एक विरोधी बल जो किसी पिंड की सतह को दूसरे पिंड की सतह के संपर्क में आने पर गति का विरोध करता है। <strong>गुरुत्वाकर्षण बल</strong> - वह बल जो ब्रह्मांड में किन्हीं दो वस्तुओं को आकर्षित करता है, चाहे उनका द्रव्यमान समान हो या नहीं।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "6",
                    section: "18",
                    question_en: "<p>6.The downslope winds flowing from high elevations of mountains, plateaus, and hills down their slopes to the valleys or plains below are known as:</p>",
                    question_hi: "<p>6. पर्वतों, पठारों और पहाड़ियों की ऊँचाई से नीचे की ओर ढलान वाली घाटियों या मैदानों की ओर बहने वाली ढलान वाली हवाओं को कहा जाता है:</p>",
                    options_en: ["<p>Samoon winds</p>", "<p>Anabatic winds</p>", 
                                "<p>Katabatic winds</p>", "<p>shamal winds</p>"],
                    options_hi: ["<p>समून पवन</p>", "<p>एनाबैटिक पवन</p>",
                                "<p>कैटाबेटिक पवन</p>", "<p>शामल पवन</p>"],
                    solution_en: "<p>6.(c) <strong>Katabatic winds. Samoon winds </strong>- A violent hot sand-laden wind on the deserts of Arabia and North Africa. Anabatic winds - upslope winds that occur during the daytime as the sun heats the surface of the land, causing the air near the surface to rise and flow up the slopes of mountains or hills. <strong>Shamal winds</strong> - hot and dry, dusty wind from the north or northwest in Iraq, Iran, and the Arabian Peninsula.</p>",
                    solution_hi: "<p>6.(c) <strong>कैटाबेटिक पवन।</strong> समून पवन - अरब और उत्तरी अफ्रीका के रेगिस्तानों पर चलने वाली प्रचंड गर्म रेत से भरी पवन है। <strong>एनाबैटिक पवन</strong> - दिन के समय उठने वाली तेज़ हवाएं जब सूर्य भूमि की सतह को गर्म करता है, जिससे सतह के पास की हवा ऊपर उठती है और पहाड़ों अथवा पहाड़ियों की ढलानों की ओर बहती है।<strong> शामल पवन</strong> - इराक, ईरान और अरब प्रायद्वीप में उत्तर या उत्तर पश्चिम से गर्म और शुष्क, धूल भरी हवा है ।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "7",
                    section: "18",
                    question_en: "<p>7. Which of the following options has the correct types of Planetary winds?</p>",
                    question_hi: "<p>7. निम्नलिखित में से किस विकल्प में ग्रहीय पवनों के सही प्रकार हैं?</p>",
                    options_en: ["<p>Local Winds and Periodic Winds</p>", "<p>Trade Winds and Periodic Winds</p>", 
                                "<p>Trade Winds, Westerlies and Polar Easterlies</p>", "<p>Polar Winds and Westerlies</p>"],
                    options_hi: ["<p>स्थानीय हवाएं और आवधिक हवाएं</p>", "<p>व्यापारिक हवाएं और आवधिक हवाएं</p>",
                                "<p>व्यापारिक हवाएँ, पछुआ हवाएँ और ध्रुवीय पूर्वी हवाएँ</p>", "<p>ध्रुवीय हवाएं और पछुआ हवाएं</p>"],
                    solution_en: "<p>7.(c)<strong> Trade Winds, Westerlies and Polar Easterlies. Planetary or permanent winds </strong>blow from high pressure belts to low pressure belts in the same direction throughout the year. <strong>Trade winds</strong> - Found between approximately 30 degrees latitude and the equator. <strong>Westerlies </strong>- Between 40&deg; and 65&deg; South latitudes. For sailors, these latitudes are known as Roaring Forties and Shrieking Sixties.<strong> Polar Easterlies</strong> are dry, cold winds that blow from the north-east to the south-west in the Northern Hemisphere and from the south-east to the north-west in the Southern Hemisphere.</p>",
                    solution_hi: "<p>7.(c) <strong>व्यापारिक हवाएँ, पछुआ हवाएँ और ध्रुवीय पूर्वी हवाएँ है। ग्रहीय या स्थायी हवाएँ </strong>पूरे वर्ष एक ही दिशा में उच्च दबाव बेल्ट से निम्न दबाव बेल्ट की ओर बहती हैं। <strong>व्यापारिक हवाएँ </strong>- लगभग 30 डिग्री अक्षांश और भूमध्य रेखा के बीच पाई जाती हैं। <strong>पछुआ हवाएँ -</strong> 40&deg; और 65&deg; दक्षिणी अक्षांशों के बीच पाई जाती है। नाविकों के लिए इन अक्षांशों को रोअरिंग फोर्टीज़ और श्रीकिंग सिक्सटीज़ के नाम से जाना जाता है। <strong>ध्रुवीय पूर्वी हवाएँ शुष्क,</strong> ठंडी हवाएँ हैं जो उत्तरी गोलार्ध में उत्तर-पूर्व से दक्षिण-पश्चिम की ओर और दक्षिणी गोलार्ध में दक्षिण-पूर्व से उत्तर-पश्चिम की ओर बहती हैं।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "8",
                    section: "18",
                    question_en: "<p>8. From which language is the word &lsquo;El-Nino&rsquo; derived?</p>",
                    question_hi: "<p>8. &lsquo;El-Nino&rsquo; शब्द किस भाषा से लिया गया है?</p>",
                    options_en: ["<p>French</p>", "<p>Italian</p>", 
                                "<p>Spanish</p>", "<p>Dutch</p>"],
                    options_hi: ["<p>फ्रेंच</p>", "<p>इटालियन</p>",
                                "<p>स्पेनिश</p>", "<p>डच</p>"],
                    solution_en: "<p>8.(c)<strong> Spanish. El Nino </strong>- It means<strong> \'little boy\' or \'Christ child</strong>\' in Spanish. It is a climate pattern that describes the unusual warming of surface waters in the eastern tropical Pacific Ocean. It is the &ldquo;warm phase&rdquo; of a larger phenomenon called the El Nino-Southern Oscillation (ENSO).</p>",
                    solution_hi: "<p>8.(c) <strong>स्पेनिश । EL नीनो </strong>- इसका स्पेनिश में अर्थ है <strong>\'छोटा लड़का\' या \'ईसा मसीह का बच्चा</strong>\'। यह एक जलवायु पैटर्न है जो पूर्वी उष्णकटिबंधीय प्रशांत महासागर में सतही जल के असामान्य रूप से गर्म होने का वर्णन करता है। यह EL नीनो-दक्षिणी दोलन (ENSO) नामक एक बड़ी घटना का \"गर्म चरण\" है।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "9",
                    section: "18",
                    question_en: "<p>9. The word &lsquo;Mausim&rsquo; (referring to &lsquo;monsoon&rsquo;) comes from which of the following languages?</p>",
                    question_hi: "<p>9. \'मौसिम\' शब्द (\'मानसून\' के संदर्भ में) निम्नलिखित में से किस भाषा से आया है?</p>",
                    options_en: ["<p>Arabic</p>", "<p>Hindi</p>", 
                                "<p>Sanskrit</p>", "<p>Persian</p>"],
                    options_hi: ["<p>अरबी</p>", "<p>हिंदी</p>",
                                "<p>संस्कृत</p>", "<p>फ़ारसी</p>"],
                    solution_en: "<p>9.(a) <strong>Arabic</strong>. &lsquo;Monsoon&rsquo; means <strong>seasons.</strong> The term \'Monsoon\' was <strong>coined</strong> by The famous Arab scholar, a world-traveller and a prolific writer, Al- Masudi. <strong>Monsoon</strong> - It is a seasonal change in the direction of the prevailing, or strongest, winds of a region.<strong> Types of Monsoon in India</strong> - The southwest monsoon and The northeast monsoon.</p>",
                    solution_hi: "<p>9.(a) <strong>अरबी</strong>। \'मानसून\' का अर्थ है ऋतुएँ । \'मानसून\' शब्द प्रसिद्ध अरब विद्वान, विश्व-यात्री और विपुल लेखक, अल-मसूदी द्वारा दिया (गढ़ा गया) गया था। <strong>मानसून </strong>- यह किसी क्षेत्र की प्रचलित या सबसे तेज़ हवाओं की दिशा में एक मौसमी परिवर्तन है। <strong>भारत में मानसून के प्रकार </strong>- दक्षिणपश्चिमी मानसून और उत्तरपूर्वी मानसून।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "10",
                    section: "18",
                    question_en: "<p>10. The word &lsquo;monsoon&rsquo; is derived from the _________ word &lsquo;mausim&rsquo; which means &lsquo;season&rsquo;.</p>",
                    question_hi: "<p>10. \'मानसून\' शब्द को _________ शब्द \'मौसिम\' से लिया गया है जिसका अर्थ \'मौसम\' है।</p>",
                    options_en: ["<p>Latin</p>", "<p>Sanskrit</p>", 
                                "<p>French</p>", "<p>Arabic</p>"],
                    options_hi: ["<p>लैटिन</p>", "<p>संस्कृत</p>",
                                "<p>फ्रेंच</p>", "<p>अरबी</p>"],
                    solution_en: "<p>10.(d) <strong>Arabic</strong>. The term monsoon has been derived from the Arabic word &lsquo;Mausim&rsquo;. Monsoons are seasonal winds, which inroads into south Asia as <strong>Southwest Monsoon season </strong>(Rainfall occurs between June and September). North-East Monsoon (Retreating Monsoon season) - The months of October and November (Bring rainfall in Tamilnadu).</p>",
                    solution_hi: "<p>10.(d) <strong>अरबी।</strong> मानसून शब्द अरबी शब्द \'मौसिम\' से लिया गया है। मानसून मौसमी हवाएँ हैं, जो <strong>दक्षिण पश्चिम मानसून</strong> के मौसम (जून और सितंबर के बीच वर्षा होती है) के रूप में दक्षिण एशिया में प्रवेश करती हैं। उत्तर-पूर्वी मानसून (मानसून की वापसी का मौसम) - अक्टूबर और नवंबर के महीने (तमिलनाडु में वर्षा लाते हैं)।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "11",
                    section: "18",
                    question_en: "<p>11. Towards the close of the summer season, the pre-monsoon showers are common, especially, in Kerala and Karnataka, which are often referred to as:</p>",
                    question_hi: "<p>11. ग्रीष्म ऋतु के अंत में, विशेष रूप से केरल और कर्नाटक में प्री-मानसून बौछारें आम हैं, जिन्हें अक्सर कहा जाता है:</p>",
                    options_en: ["<p>Kaal Baisakhi</p>", "<p>Mango showers</p>", 
                                "<p>Jet streams</p>", "<p>Western depressions</p>"],
                    options_hi: ["<p>काल बैसाखी</p>", "<p>आम्र वर्षा</p>",
                                "<p>जेट स्ट्रीम</p>", "<p>पश्चिमी अवसाद</p>"],
                    solution_en: "<p>11.(b) <strong>Mango showers. Kalbaisakhi </strong>is a localized rainfall and thunderstorm event which occurs in Bangladesh as well as the Indian states of Bihar, Jharkhand, Odisha, Tripura, Assam and West Bengal. <strong>Jet streams </strong>are narrow bands of strong wind that generally blow from west to east all across the globe. <strong>Western Disturbances</strong> are the cause of the most winter and pre-monsoon season rainfall across North-West India.</p>",
                    solution_hi: "<p>11.(b) <strong>आम्र वर्षा। कालबैसाखी </strong>एक स्थानीय वर्षा और तूफान का मौसम है जो बांग्लादेश के साथ-साथ भारतीय राज्यों बिहार, झारखंड, ओडिशा, त्रिपुरा, असम और पश्चिम बंगाल में होती है।<strong> जेट स्ट्रीम </strong>तेज़ हवा के संकीर्ण बैंड हैं जो सामान्यतः दुनिया भर में पश्चिम से पूर्व की ओर चलते हैं। <strong>पश्चिमी विक्षोभ</strong> पूरे उत्तर-पश्चिम भारत में सबसे अधिक सर्दी एवं प्रथम -मॉनसून वर्षा ऋतु का कारण है।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "12",
                    section: "18",
                    question_en: "<p>12. What is the direction of the Inter Tropical Convergence Zone in winter?</p>",
                    question_hi: "<p>12. शीतऋतु में अन्तः उष्ण कटिबंधीय अभिसरण क्षेत्र (Inter Tropical Convergence Zone) की दिशा क्या होती है</p>",
                    options_en: ["<p>Westward</p>", "<p>Southward</p>", 
                                "<p>Northward</p>", "<p>Eastward</p>"],
                    options_hi: ["<p>पश्चिम की ओर</p>", "<p>दक्षिण की ओर</p>",
                                "<p>उत्तर की ओर</p>", "<p>पूर्व की ओर</p>"],
                    solution_en: "<p>12.(b) <strong>Southward</strong>. The Inter Tropical Convergence Zone (ITCZ) follows the position of the sun and varies seasonally. In winter, due to the sun&rsquo;s apparent movement toward the Tropic of Capricorn, the ITCZ moves <strong>southward</strong>, and so the reversal of wind direction takes place in the Indian Subcontinent. ITCZ is responsible for the wet and dry seasons in the tropics.</p>",
                    solution_hi: "<p>12.(b) <strong>दक्षिण की ओर</strong> I अंतर (अन्तः) उष्णकटिबंधीय अभिसरण क्षेत्र (ITCZ) सूर्य की स्थिति का अनुसरण करता है और मौसम के अनुसार बदलता रहता है। सर्दियों में, सूर्य के मकर रेखा की ओर स्पष्ट गति के कारण, ITCZ ​​<strong>दक्षिण की ओर बढ़ता है,</strong> और इसलिए भारतीय उपमहाद्वीप में हवा की दिशा उलट जाती है। ITCZ उष्ण कटिबंध में गीले और सूखे मौसम के लिए उत्तरदायी है।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "13",
                    section: "18",
                    question_en: "<p>13. Which of the following regions is known to receive maximum rainfall from the south-west monsoon winds in India?</p>",
                    question_hi: "<p>13. निम्नलिखित में से कौन सा क्षेत्र भारत में दक्षिण-पश्चिमी मानसून पवनों से अधिकतम वर्षा प्राप्त करने के लिए जाना जाता है?</p>",
                    options_en: ["<p>Western Ghats</p>", "<p>Eastern Ghats</p>", 
                                "<p>Central plateau region</p>", "<p>Ganga plains</p>"],
                    options_hi: ["<p>पश्चिमी घाट</p>", "<p>पूर्वी घाट</p>",
                                "<p>मध्य पठारी क्षेत्र</p>", "<p>गंगा के मैदान</p>"],
                    solution_en: "<p>13.(a)<strong> Western Ghats</strong>. India has two monsoons - the southwest monsoon and the northeast monsoon.The southwest monsoon first hits coastal state of Kerala,making it the first state in India to receive rain from the Southwest Monsoon. The northeast monsoon winds bring rainfall to the southeast coast of the country (Tamil Nadu coast and Seemandhra&rsquo;s south coast).</p>",
                    solution_hi: "<p>13.(a) <strong>पश्चिमी घाट</strong>। भारत में वास्तव में दो प्रकार के मानसून हैं - दक्षिण-पश्चिम मानसून और उत्तर-पूर्व मानसून। दक्षिण-पश्चिम मानसून सबसे पहले तटीय राज्य केरल से टकराता है, जिससे यह दक्षिण-पश्चिम मानसून से बारिश प्राप्त करने वाला भारत का पहला राज्य बन जाता है। उत्तर-पूर्व मानसूनी हवाएँ देश के दक्षिण-पूर्वी तट (तमिलनाडु तट और सीमांध्र के दक्षिणी तट) पर वर्षा लाती हैं।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "14",
                    section: "18",
                    question_en: "<p>14. Bordoisila storm originates in which of the following Indian states?</p>",
                    question_hi: "<p>14. बोरदोईसिला (Bordoisila) तूफान, इनमें से किस भारतीय राज्य में उठता है?</p>",
                    options_en: ["<p>Assam</p>", "<p>Maharashtra</p>", 
                                "<p>Madhya Pradesh</p>", "<p>Kerala</p>"],
                    options_hi: ["<p>असम</p>", "<p>महाराष्ट्र</p>",
                                "<p>मध्य प्रदेश</p>", "<p>केरल</p>"],
                    solution_en: "<p>14.(a) <strong>Assam</strong>. Norwesters / Bordoisila (Assam) / Kalbaishakhi (Bengal) - localized rainfall and thunderstorm event which occurs in Bangladesh and Indian states of Bihar, Jharkhand, Odisha, Tripura, West Bengal and Assam from April till the monsoon establishes itself over North-East India. It also marks the beginning of the Assamese month Bohag or Baishagu. Other local winds in India - Elephanta (Malabar coast), Kali Andhi (northwestern parts of the Indo-Gangetic Plain), Loo (hot wind in India and Pakistan), Mango showers (Karnataka, Kerala and Tamil Nadu).</p>",
                    solution_hi: "<p>14.(a) <strong>असम।</strong> नॉरवेस्टर/बोरदोईसिला&nbsp;(असम)/कालबैशाखी (बंगाल) - अप्रैल से बांग्लादेश और भारतीय राज्यों बिहार, झारखंड, ओडिशा, त्रिपुरा, पश्चिम बंगाल और असम में होने वाली स्थानीय बारिश और आंधी की घटना जब तक कि मानसून उत्तर-पूर्व भारत में खुद को स्थापित नहीं कर लेता। यह असमिया महीने बोहाग या बैशागू की शुरुआत का भी प्रतीक है। भारत में अन्य स्थानीय हवाएँ - एलिफेंटा (मालाबार तट), काली आँधी (सिंधु-गंगा के मैदान के उत्तर-पश्चिमी भाग), लू (भारत और पाकिस्तान में गर्म हवा), आम्र वर्षा (कर्नाटक, केरल और तमिलनाडु)।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "15",
                    section: "misc",
                    question_en: "<p>15. The doldrums are an area of_________pressure around the equator, where prevailing winds are calm.</p>",
                    question_hi: "<p>15. डोलड्रम (doldrums ), भूमध्य रेखा के इर्द-गिर्द________ दाब वाला क्षेत्र है, जहां चलने वाली हवाएं शांत होती हैं।</p>",
                    options_en: ["<p>Constant</p>", "<p>Low</p>", 
                                "<p>High</p>", "<p>Fluctuating</p>"],
                    options_hi: ["<p>स्थिर</p>", "<p>निम्न</p>",
                                "<p>उच्च</p>", "<p>उच्चावच</p>"],
                    solution_en: "<p>15.(b) <strong>Low. Doldrums </strong>(Equatorial Low Pressure Belt) - also known as Inter Tropical Convergence Zone <strong>(ITCZ).</strong> Area of low pressure, high temperatures and high humidity around the equator (10&ordm; N and 10&ordm; S latitudes). It is the region of weak winds within the intertropical convergence zone (ITCZ). Due to excessive heating horizontal movement of air is absent here and only conventional currents are there. <strong>Sailing ships </strong>sometimes <strong>get stuck</strong> on windless waters.</p>",
                    solution_hi: "<p>15.(b) <strong>निम्न । डोलड्रम </strong>(भूमध्यरेखीय निम्न दाब पेटी) - इसे अंतर-उष्णकटिबंधीय अभिसरण क्षेत्र (ITCZ) के रूप में भी जाना जाता है। भूमध्य रेखा (10&ordm; उत्तर और 10&ordm; दक्षिण अक्षांश) के आसपास कम दबाव, उच्च तापमान और उच्च आर्द्रता का क्षेत्र। यह अंतर-उष्णकटिबंधीय अभिसरण क्षेत्र (ITCZ) के भीतर कमजोर हवाओं का क्षेत्र है। अत्यधिक ताप के कारण यहाँ वायु की क्षैतिज गति अनुपस्थित होती है और केवल परम्परागत धाराएँ होती हैं। <strong>नौकायन जहाज</strong> कभी-कभी हवा रहित पानी में फंस जाते हैं।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>