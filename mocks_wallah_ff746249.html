<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">10:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 14</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">14</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 10 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 0,
                end: 12
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 13,
                end: 13
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "17",
                    question_en: "<p>1. When a positive integer n is divided by 12, the remainder is 5. What will be the remainder if <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>8</mn><msup><mi>n</mi><mn>2</mn></msup><mo>+</mo><mn>7</mn><mo>&#160;</mo></math><span style=\"font-family: Times New Roman;\"> is divided by 12?</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">SSC CHSL 6/8/2021 , Evening</span></p>",
                    question_hi: "<p>1. <span style=\"font-family: Baloo;\">जब एक धनात्मक पूर्णांक</span><span style=\"font-family: Times New Roman;\"> n </span><span style=\"font-family: Baloo;\">को</span><span style=\"font-family: Times New Roman;\"> 12</span><span style=\"font-family: Baloo;\"> से विभाजित किया जाता है, तो शेषफल 5 प्राप्त होता है। यदि</span><span style=\"font-family: Times New Roman;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>8</mn><msup><mi>n</mi><mn>2</mn></msup><mo>+</mo><mn>7</mn></math></span><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Baloo;\">को</span><span style=\"font-family: Times New Roman;\"> 12</span><span style=\"font-family: Baloo;\"> से विभाजित किया जाए तो शेषफल क्या होगा</span><span style=\"font-family: Times New Roman;\">?</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">CHSL 6/8/2021 , Evening</span></p>",
                    options_en: ["<p>2</p>", "<p>5</p>", 
                                "<p>3</p>", "<p>4</p>"],
                    options_hi: ["<p>2</p>", "<p>5</p>",
                                "<p>3</p>", "<p>4</p>"],
                    solution_en: "<p>1.(c) <span style=\"font-family: Times New Roman;\">Short-trick:</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">When a positive integer n is divided by 12 remainder is 5</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">N = 17</span></p>\r\n<p><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>8</mn><msup><mi>n</mi><mn>2</mn></msup><mo>+</mo><mo>&#160;</mo><mn>7</mn><mo>&#160;</mo></math>= 2319</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">When 2319 is divided by 12, gives remainder 3</span></p>",
                    solution_hi: "<p>1.(c) <span style=\"font-family: Baloo;\">जब एक धनात्मक पूर्णांक</span><span style=\"font-family: Times New Roman;\"> n </span><span style=\"font-family: Baloo;\">को</span><span style=\"font-family: Times New Roman;\"> 12 </span><span style=\"font-family: Baloo;\">से विभाजित किया जाता है तो शेषफल</span><span style=\"font-family: Times New Roman;\"> 5 </span><span style=\"font-family: Baloo;\">प्राप्त होता है</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">n = 17</span></p>\r\n<p><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>8</mn><msup><mi>n</mi><mn>2</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>7</mn><mo>&#160;</mo></math>= 2319</span></p>\r\n<p><span style=\"font-family: Baloo;\">जब </span><span style=\"font-family: Times New Roman;\">2319</span><span style=\"font-family: Baloo;\"> को</span><span style=\"font-family: Times New Roman;\"> 12 </span><span style=\"font-family: Baloo;\">से विभाजित किया जाता है, तो शेषफल </span><span style=\"font-family: Times New Roman;\">3 </span><span style=\"font-family: Baloo;\">मिलता है</span><span style=\"font-family: Times New Roman;\">|</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "17",
                    question_en: "<p>2. If <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>71</mn><mn>83</mn></msup><mo>&#160;</mo><mo>+</mo><msup><mn>73</mn><mn>83</mn></msup></math><span style=\"font-family: Times New Roman;\">&nbsp;</span><span style=\"font-family: Times New Roman;\">is divisible by 36, the remainder is:</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">SSC CHSL 6/8/2021 , Evening</span></p>",
                    question_hi: "<p>2. <span style=\"font-family: Baloo;\">यदि</span><span style=\"font-family: Times New Roman;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>71</mn><mn>83</mn></msup><mo>&#160;</mo><mo>+</mo><msup><mn>73</mn><mn>83</mn></msup></math></span><span style=\"font-family: Times New Roman;\">&nbsp;, 36 </span><span style=\"font-family: Baloo;\">से विभाज्य है, तो शेषफल है:</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">SSC CHSL 6/8/2021 , Evening</span></p>",
                    options_en: ["<p>0</p>", "<p>8</p>", 
                                "<p>9</p>", "<p>13</p>"],
                    options_hi: ["<p>0</p>", "<p>8</p>",
                                "<p>9</p>", "<p>13</p>"],
                    solution_en: "<p>2.(a)&nbsp; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#160;</mo><msup><mi>a</mi><mi>n</mi></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mi>b</mi><mi>n</mi></msup></math><span style=\"font-family: Times New Roman;\">is always divisible by (a+b) when n is odd</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">When <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>71</mn><mn>83</mn></msup><mo>&#160;</mo><mo>+</mo><msup><mn>73</mn><mn>83</mn></msup></math></span><span style=\"font-family: Times New Roman;\">&nbsp;is divided by 36 then the remainder is 0.</span></p>",
                    solution_hi: "<p>2.(a) <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>a</mi><mrow><mi>n</mi><mo>&#160;</mo></mrow></msup><mo>+</mo><mo>&#160;</mo><msup><mi>b</mi><mi>n</mi></msup></math><span style=\"font-family: Times New Roman;\">&nbsp;</span><span style=\"font-family: Baloo;\">हमेशा</span><span style=\"font-family: Times New Roman;\"> (a + b) </span><span style=\"font-family: Baloo;\">से विभाजित होता है जब</span><span style=\"font-family: Times New Roman;\"> n </span><span style=\"font-family: Baloo;\">विषम होता है</span></p>\r\n<p><span style=\"font-family: Baloo;\">जब</span><span style=\"font-family: Times New Roman;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#160;</mo><msup><mn>71</mn><mn>83</mn></msup><mo>&#160;</mo><mo>+</mo><msup><mn>73</mn><mn>83</mn></msup><mo>&#160;</mo></math></span><span style=\"font-family: Baloo;\">को</span><span style=\"font-family: Times New Roman;\"> 36 </span><span style=\"font-family: Baloo;\">से विभाजित किया जाता है तो शेषफल</span><span style=\"font-family: Times New Roman;\"> 0 </span><span style=\"font-family: Baloo;\">होता है।</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "17",
                    question_en: " <p>3. If the number 645A2879B8 is divisible by both 8 and 9, then the smallest possible value of A and B will be:</span></p> <p><span style=\"font-family:Times New Roman\">SSC CHSL 9/8/2021 , Afternoon</span></p>",
                    question_hi: "<p>3. <span style=\"font-family: Baloo;\">यदि संख्या</span><span style=\"font-family: Times New Roman;\"> 645A2879B8, 8 </span><span style=\"font-family: Baloo;\">और</span><span style=\"font-family: Times New Roman;\"> 9 </span><span style=\"font-family: Baloo;\">दोनों से विभाज्य है, तो</span><span style=\"font-family: Times New Roman;\"> A</span><span style=\"font-family: Baloo;\"> और</span><span style=\"font-family: Times New Roman;\"> B </span><span style=\"font-family: Baloo;\">का न्यूनतम संभव मान होगा</span><span style=\"font-family: Times New Roman;\">:</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">CHSL 9/8/2021 , Afternoon</span></p>",
                    options_en: [" <p> A=3, B=4 </span><span style=\"font-family:Times New Roman\"> </span></p>", " <p> A=4, B=3   </span></p>", 
                                " <p> A=2, B=3  </span></p>", " <p> A=3, B=2</span></p>"],
                    options_hi: ["<p>A=3, B=4</p>", "<p>A=4, B=3</p>",
                                "<p>A=2, B=3</p>", "<p>A=3, B=2</p>"],
                    solution_en: " <p>3.(d) Divisibility rule of 8 :</span></p> <p><span style=\"font-family:Times New Roman\">If last 3 digits of a number is divisible by 8 then number is divisible by 8</span></p> <p><span style=\"font-family:Times New Roman\">Check option : only B = 2 satisfies the condition.</span></p>",
                    solution_hi: "<p>3.(d) 8 <span style=\"font-family: Baloo;\">से विभाज्यता नियम:-</span></p>\r\n<p><span style=\"font-family: Baloo;\">यदि किसी संख्या के अंतिम</span><span style=\"font-family: Times New Roman;\"> 3 </span><span style=\"font-family: Baloo;\">अंक</span><span style=\"font-family: Times New Roman;\"> 8 </span><span style=\"font-family: Baloo;\">से विभाज्य हैं तो संख्या</span><span style=\"font-family: Times New Roman;\"> 8 </span><span style=\"font-family: Baloo;\">से विभाज्य होगी।</span><span style=\"font-family: Times New Roman;\"> </span></p>\r\n<p><span style=\"font-family: Baloo;\">विकल्प की जाँच करें,</span></p>\r\n<p><span style=\"font-family: Baloo;\">केवल </span><span style=\"font-family: Times New Roman;\">B = 2 </span><span style=\"font-family: Baloo;\">शर्त को पूरा करता है।</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "17",
                    question_en: "<p>4. If&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>31</mn><mn>47</mn></msup><mo>+</mo><msup><mn>43</mn><mn>47</mn></msup></math> is <span style=\"font-family: Times New Roman;\">divided by 37, the remainder is:</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">SSC CHSL 9/8/2021 , Evening</span></p>",
                    question_hi: "<p>4. <span style=\"font-family: Baloo;\">यदि </span><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Baloo;\">को</span><span style=\"font-family: Times New Roman;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>31</mn><mn>47</mn></msup><mo>+</mo><msup><mn>43</mn><mn>47</mn></msup></math>&nbsp;</span><span style=\"font-family: Baloo;\">से विभाजित किया जाता है, तो शेषफल क्या होगा </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">CHSL 9/8/2021 , Evening</span></p>",
                    options_en: ["<p>2</p>", "<p>3</p>", 
                                "<p>0</p>", "<p>1</p>"],
                    options_hi: ["<p>2</p>", "<p>3</p>",
                                "<p>0</p>", "<p>1</p>"],
                    solution_en: "<p>4.(c)&nbsp; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>31</mn><mn>47</mn></msup><mo>+</mo><msup><mn>43</mn><mn>47</mn></msup></math><span style=\"font-family: Times New Roman;\">is divisible by the factor of (31+43) or 74</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">37 is the factor of 74</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">So, on dividing <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>31</mn><mn>47</mn></msup><mo>+</mo><msup><mn>43</mn><mn>47</mn></msup></math></span><span style=\"font-family: Times New Roman;\"> by 37, remainder = 0</span></p>",
                    solution_hi: "<p>4.(c) <span style=\"font-family: Times New Roman;\">&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><msup><mn>31</mn><mn>47</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mn>43</mn><mn>47</mn></msup><mo>)</mo></math>, <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><mn>31</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>43</mn><mo>)</mo><mo>&#160;</mo></math></span><span style=\"font-family: Baloo;\">या</span><span style=\"font-family: Baloo;\"> 74 के</span><span style=\"font-family: Baloo;\"> गुणनखंड से विभाज्य है |</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">37, 74 </span><span style=\"font-family: Baloo;\">का गुणनखंड है</span></p>\r\n<p><span style=\"font-family: Baloo;\">अत:</span><span style=\"font-family: Times New Roman;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>31</mn><mn>47</mn></msup><mo>+</mo><msup><mn>43</mn><mn>47</mn></msup></math></span><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Baloo;\">को </span><span style=\"font-family: Times New Roman;\">37</span><span style=\"font-family: Baloo;\"> से भाग देने पर शेषफल</span><span style=\"font-family: Times New Roman;\"> = 0</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "17",
                    question_en: "<p>5. What is the sum of all the positive values of k for which a seven-digit number 23k567k is divisible by 3?</p>\r\n<p><span style=\"font-family: Times New Roman;\">SSC CHSL 10/8/2021 , Morning</span></p>",
                    question_hi: "<p>5. k<span style=\"font-family: Baloo;\"> के सभी मानों का योग क्या है जिसके लिए सात अंकों की संख्या 23k567k, 3 से विभाज्य है? </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">CHSL 10/8/2021 , Morning</span></p>",
                    options_en: ["<p>15</p>", "<p>3</p>", 
                                "<p>109</p>", "<p>16</p>"],
                    options_hi: ["<p>15</p>", "<p>3</p>",
                                "<p>109</p>", "<p>16</p>"],
                    solution_en: "<p>5.(a) For divisibility by 3 : sum of digits of the number should be divisible by 3</p>\r\n<p><span style=\"font-family: Times New Roman;\">Sum of digits = 23 + 2k </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Possible value of k = 2, 5, 8, </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Sum = 2 + 5 + 8 = 15</span></p>",
                    solution_hi: "<p>5.(a) 3 <span style=\"font-family: Baloo;\">से विभाज्यता के लिए संख्या के अंकों का योग</span><span style=\"font-family: Times New Roman;\"> 3 </span><span style=\"font-family: Baloo;\">से विभाज्य होना चाहिए |</span></p>\r\n<p><span style=\"font-family: Baloo;\">अंकों का योग</span><span style=\"font-family: Times New Roman;\"> = 23 + 2k</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">k </span><span style=\"font-family: Baloo;\">का संभावित मान </span><span style=\"font-family: Times New Roman;\">= 2, 5, 8</span></p>\r\n<p><span style=\"font-family: Baloo;\">योग =</span><span style=\"font-family: Times New Roman;\"> 2 + 5 + 8 = 15</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "17",
                    question_en: "<p>6. If the number 4A306768B2 is divisible by both 8 and 11, then the smallest possible value of A and B will be:</p>\r\n<p><span style=\"font-family: Times New Roman;\">SSC CHSL 10/8/2021 , Afternoon</span></p>",
                    question_hi: "<p>6. <span style=\"font-family: Baloo;\">यदि संख्या</span><span style=\"font-family: Times New Roman;\"> 4A306768B2, 8 </span><span style=\"font-family: Baloo;\">और</span><span style=\"font-family: Times New Roman;\"> 11 </span><span style=\"font-family: Baloo;\">दोनों से विभाज्य है, तो</span><span style=\"font-family: Times New Roman;\"> A </span><span style=\"font-family: Baloo;\">और </span><span style=\"font-family: Times New Roman;\">B </span><span style=\"font-family: Baloo;\">का न्यूनतम संभव मान होगा:</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">CHSL 10/8/2021 , Afternoon</span></p>",
                    options_en: ["<p>A=5, B=3</p>", "<p>A=3, B=5</p>", 
                                "<p>A=5, B=2</p>", "<p>A=5, B=4</p>"],
                    options_hi: ["<p>A=5, B=3</p>", "<p>A=3, B=5</p>",
                                "<p>A=5, B=2</p>", "<p>A=5, B=4</p>"],
                    solution_en: "<p>6.(a) For divisibility by 8: last 3 digits should be divisible by 8</p>\r\n<p><span style=\"font-family: Times New Roman;\">Possible value of B = 3,7</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Smallest possible value of B = 3</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">B = 3 is only in option (a).</span></p>",
                    solution_hi: "<p>6.(a) 8 <span style=\"font-family: Baloo;\">से विभाज्यता के लिए:- अंतिम</span><span style=\"font-family: Times New Roman;\"> 3 </span><span style=\"font-family: Baloo;\">अंक</span><span style=\"font-family: Times New Roman;\"> 8 </span><span style=\"font-family: Baloo;\">से विभाज्य होना चाहिए।</span><span style=\"font-family: Times New Roman;\"> </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">B </span><span style=\"font-family: Baloo;\">का संभावित मान =</span><span style=\"font-family: Times New Roman;\"> 3,7</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">B </span><span style=\"font-family: Baloo;\">का न्यूनतम संभव मान </span><span style=\"font-family: Times New Roman;\">= 3</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">B = 3 </span><span style=\"font-family: Baloo;\">केवल विकल्प</span><span style=\"font-family: Times New Roman;\"> (a) </span><span style=\"font-family: Baloo;\">में है।</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "17",
                    question_en: "<p>7. If the 11-digit number 4y6884805x6 is divisible by 72, and x&ne;<span style=\"font-family: Times New Roman;\">y, then the value of <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mi>x</mi><mi>y</mi></msqrt></math></span><span style=\"font-family: Times New Roman;\"> is:</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">SSC CHSL 10/8/2021 , Evening</span></p>",
                    question_hi: "<p>7. <span style=\"font-family: Baloo;\">यदि</span><span style=\"font-family: Times New Roman;\"> 11 </span><span style=\"font-family: Baloo;\">अंकों की संख्या</span><span style=\"font-family: Times New Roman;\"> 4y6884805x6, 72 </span><span style=\"font-family: Baloo;\">से विभाज्य है और</span><span style=\"font-family: Times New Roman;\"> x&ne;</span><span style=\"font-family: Times New Roman;\"> y , </span><span style=\"font-family: Baloo;\">तो</span><span style=\"font-family: Times New Roman;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mi>x</mi><mi>y</mi></msqrt></math></span><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Baloo;\">का मान है |</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">CHSL 10/8/2021 , Evening</span></p>",
                    options_en: ["<p><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>12</mn></msqrt></math> </span></p>", "<p><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>6</mn></msqrt></math> </span></p>", 
                                "<p><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>5</mn></msqrt></math> </span></p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>8</mn></msqrt></math></p>"],
                    options_hi: ["<p><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>12</mn></msqrt></math> </span></p>", "<p><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>6</mn></msqrt></math> </span></p>",
                                "<p><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>5</mn></msqrt></math> </span></p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>8</mn></msqrt></math></p>"],
                    solution_en: "<p>7.(b) The given number is 4y6884805x6.</p>\r\n<p><span style=\"font-family: Times New Roman;\">Co-prime factor of 72 = (8,9)</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">For divisibility by 8: the last 3 digits should be divisible by 8</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Possible value of x = 7, 3</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">For divisibility by 9 : Sum of digits should be divisible by 9 </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">If x = 7, sum of digits = 56 + Y</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Possible value of Y = 7</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">If x = 3, Sum of digits = 52 + y</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Possible value of y = 2</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Possible pair of x and y = (3, 2)</span></p>\r\n<p dir=\"rtl\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mi>x</mi><mi>y</mi></msqrt><mo>&#160;</mo><mo>=</mo><msqrt><mn>6</mn></msqrt></math></p>",
                    solution_hi: "<p>7.(b) <span style=\"font-family: Baloo;\">दी गई संख्या</span><span style=\"font-family: Times New Roman;\"> 4y6884805x6 </span><span style=\"font-family: Baloo;\">है</span><span style=\"font-family: Baloo;\">।</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">72 </span><span style=\"font-family: Baloo;\">का सह-अभाज्य गुणनखंड =</span><span style=\"font-family: Times New Roman;\"> (8,9)</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">8 </span><span style=\"font-family: Baloo;\">से विभाज्यता के लिए: अंतिम</span><span style=\"font-family: Times New Roman;\"> 3 </span><span style=\"font-family: Baloo;\">अंक</span><span style=\"font-family: Times New Roman;\"> 8 </span><span style=\"font-family: Baloo;\">से विभाज्य होने चाहिए</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">x </span><span style=\"font-family: Baloo;\">का संभावित मान </span><span style=\"font-family: Times New Roman;\">= 7, 3</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">9 </span><span style=\"font-family: Baloo;\">से विभाज्यता के लिए: - अंकों का योग</span><span style=\"font-family: Times New Roman;\"> 9 </span><span style=\"font-family: Baloo;\">से विभाज्य होना चाहिए </span></p>\r\n<p><span style=\"font-family: Baloo;\">यदि</span><span style=\"font-family: Times New Roman;\"> x = 7, </span><span style=\"font-family: Baloo;\">अंकों का योग</span><span style=\"font-family: Times New Roman;\"> = 56 + Y</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Y </span><span style=\"font-family: Baloo;\">का संभावित मान </span><span style=\"font-family: Times New Roman;\">= 7 . </span></p>\r\n<p><span style=\"font-family: Baloo;\">यदि</span><span style=\"font-family: Times New Roman;\"> x = 3, </span><span style=\"font-family: Baloo;\">अंकों का योग </span><span style=\"font-family: Times New Roman;\">= 52 + y</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">y </span><span style=\"font-family: Baloo;\">का संभावित मान=</span><span style=\"font-family: Times New Roman;\"> 2 </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">x </span><span style=\"font-family: Baloo;\">और</span><span style=\"font-family: Times New Roman;\"> y </span><span style=\"font-family: Baloo;\">का संभावित युग्म</span><span style=\"font-family: Times New Roman;\"> = (3, 2)</span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mi>x</mi><mi>y</mi></msqrt><mo>&#160;</mo><mo>=</mo><msqrt><mn>6</mn></msqrt></math></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "17",
                    question_en: "<p>8. What is the ratio of the mean proportion between 1.2 and 10.8 to the third proportional to 0.2 and 1.2?</p>\r\n<p><span style=\"font-family: Times New Roman;\">SSC CHSL 10/8/2021 , Evening</span></p>",
                    question_hi: "<p>8. 1.2 <span style=\"font-family: Baloo;\">और </span><span style=\"font-family: Times New Roman;\">10.8</span><span style=\"font-family: Baloo;\"> के माध्य अनुपात तथा</span><span style=\"font-family: Times New Roman;\"> 0.2 </span><span style=\"font-family: Baloo;\">और </span><span style=\"font-family: Times New Roman;\">1.2 </span><span style=\"font-family: Baloo;\">के तृतीयानुपात का अनुपात क्या है?</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">CHSL 10/8/2021 , Evening</span></p>",
                    options_en: ["<p>1:3</p>", "<p>1:2</p>", 
                                "<p>2:1</p>", "<p>3:1</p>"],
                    options_hi: ["<p>1:3</p>", "<p>1:2</p>",
                                "<p>2:1</p>", "<p>3:1</p>"],
                    solution_en: "<p>8.(b) Mean proportion of 1.2 and 10.8 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>1.2</mn><mi>&#160;</mi><mo>&#215;</mo><mn>10.8</mn></msqrt></math><span style=\"font-family: Times New Roman;\"> = 3.6</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Third proportion of 0.2 and 1.2 =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1.2</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>1.2</mn></mrow><mn>0.2</mn></mfrac></math></span><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Times New Roman;\"> = 7.2</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Ratio = 3.6 : 7.2</span></p>\r\n<p><span style=\"font-family: Times New Roman;\"> = 1 : 2</span></p>",
                    solution_hi: "<p>8.(b) 1.2 <span style=\"font-family: Baloo;\">और</span><span style=\"font-family: Times New Roman;\"> 10.8</span><span style=\"font-family: Baloo;\"> का माध्य अनुपात =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>1.2</mn><mi>&#160;</mi><mo>&#215;</mo><mn>10.8</mn></msqrt></math> </span><span style=\"font-family: Times New Roman;\"> = 3.6</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">0.2 </span><span style=\"font-family: Baloo;\">और</span><span style=\"font-family: Times New Roman;\"> 1.2 </span><span style=\"font-family: Baloo;\">का तीसरा अनुपात</span><span style=\"font-family: Times New Roman;\"> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1.2</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>1.2</mn></mrow><mn>0.2</mn></mfrac></math></span><span style=\"font-family: Times New Roman;\"> = 7.2</span></p>\r\n<p><span style=\"font-family: Baloo;\">अनुपात</span><span style=\"font-family: Times New Roman;\"> = 3.6 : 7.2 = 1 : 2</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "17",
                    question_en: " <p>9. When a number is divided by 3, the remainder is 2. Again, when the quotient is divided by 7, the remainder is 5. What will be the remainder when the original number is divided by 21?</span></p> <p><span style=\"font-family:Times New Roman\">SSC CHSL 11/8/2021 , Morning</span></p>",
                    question_hi: " <p>9.</span><span style=\"font-family:Baloo\"> जब एक संख्या को 3 से विभाजित किया जाता है, तो शेषफल 2 प्राप्त होता है, जब भागफल को 7 से विभाजित किया जाता है, तो शेषफल 5 प्राप्त होता है, जब मूल संख्या को 21 से विभाजित किया जाता है, तो शेषफल क्या होगा?</span></p> <p><span style=\"font-family:Times New Roman\">CHSL 11/8/2021 (Morning)</span></p>",
                    options_en: [" <p> 14</span></p>", " <p> 13</span></p>", 
                                " <p> 17</span></p>", " <p> 16</span></p>"],
                    options_hi: [" <p> 14</span></p>", " <p> 13</span></p>",
                                " <p> 17</span></p>", " <p> 16</span></p>"],
                    solution_en: " <p>9.(c) Q is the quotient when number is divided by 3</span></p> <p><span style=\"font-family:Times New Roman\">Number = 3Q + 2</span></p> <p><span style=\"font-family:Times New Roman\">Let, x is the remainder when Q is divided by 7</span></p> <p><span style=\"font-family:Times New Roman\">Q = 7x + 5</span></p> <p><span style=\"font-family:Times New Roman\">Number = 21x +1 7</span></p> <p><span style=\"font-family:Times New Roman\">When number is divided by 21, Remainder = 17</span></p>",
                    solution_hi: " <p>9.(c) Q </span><span style=\"font-family:Baloo\">वह भागफल है जब संख्या को </span><span style=\"font-family:Times New Roman\">3</span><span style=\"font-family:Baloo\"> से विभाजित किया जाता है</span></p> <p><span style=\"font-family:Baloo\">संख्या</span><span style=\"font-family:Times New Roman\"> =  3Q + 2</span></p> <p><span style=\"font-family:Baloo\">माना</span><span style=\"font-family:Times New Roman\">, x </span><span style=\"font-family:Baloo\">शेषफल है जब</span><span style=\"font-family:Times New Roman\"> Q </span><span style=\"font-family:Baloo\">को</span><span style=\"font-family:Times New Roman\"> 7 </span><span style=\"font-family:Baloo\">से विभाजित किया जाता है</span></p> <p><span style=\"font-family:Times New Roman\">Q = 7x + 5</span></p> <p><span style=\"font-family:Baloo\">संख्या =</span><span style=\"font-family:Times New Roman\"> 21x+ 17</span></p> <p><span style=\"font-family:Baloo\">जब संख्या को</span><span style=\"font-family:Times New Roman\"> 21 </span><span style=\"font-family:Baloo\">से विभाजित किया जाता है, तो शेषफल =</span><span style=\"font-family:Times New Roman\"> 17</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "17",
                    question_en: " <p>10. The smallest six-digit number that is exactly divisible by 53 is:</span></p> <p><span style=\"font-family:Times New Roman\">SSC CHSL 11/8/2021 , Afternoon</span></p>",
                    question_hi: " <p>10.</span><span style=\"font-family:Baloo\"> छ: अंकों की सबसे छोटी संख्या, जो 53 से पूर्णतः विभाज्य है, है |</span></p> <p><span style=\"font-family:Times New Roman\">CHSL 11/8/2021 , Afternoon</span></p>",
                    options_en: [" <p>100011</span><span style=\"font-family:Times New Roman\">   </span></p>", " <p>100042  </span></p>", 
                                " <p>100008</span><span style=\"font-family:Times New Roman\">  </span></p>", " <p>100064</span></p>"],
                    options_hi: [" <p>100011</span></p>", " <p>100042  </span></p>",
                                " <p>100008      </span></p>", " <p>100064</span></p>"],
                    solution_en: " <p>10.(a) </span></p> <p><span style=\"font-family:Times New Roman\">Smallest six-digit number = 100000</span></p> <p><span style=\"font-family:Times New Roman\">When 100000 is divided by 53, Remainder is 42</span></p> <p><span style=\"font-family:Times New Roman\">53 - 42 = 11</span></p> <p><span style=\"font-family:Times New Roman\">Required number = 100000 + 11 = 100011</span></p>",
                    solution_hi: " <p>10.(a) </span><span style=\"font-family:Baloo\">छह अंकों की सबसे छोटी संख्या =</span><span style=\"font-family:Times New Roman\"> 100000</span></p> <p><span style=\"font-family:Baloo\">जब</span><span style=\"font-family:Times New Roman\"> 100000 </span><span style=\"font-family:Baloo\">को</span><span style=\"font-family:Times New Roman\"> 53 </span><span style=\"font-family:Baloo\">से विभाजित किया जाता है, तो शेषफल</span><span style=\"font-family:Times New Roman\"> 42 </span><span style=\"font-family:Baloo\">होता है। </span></p> <p><span style=\"font-family:Times New Roman\">53 - 42 = 11</span></p> <p><span style=\"font-family:Baloo\">आवश्यक संख्या</span><span style=\"font-family:Times New Roman\"> = 100000 + 11 = 100011</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "17",
                    question_en: " <p>11. The largest six-digit number exactly divisible by 243 is:</span></p> <p><span style=\"font-family:Times New Roman\">SSC CHSL 11/8/2021 , Evening</span></p>",
                    question_hi: " <p>11. 243</span><span style=\"font-family:Baloo\"> से पूर्णतः विभाजित होने वाली छह अंकों की सबसे बड़ी संख्या है:</span></p> <p><span style=\"font-family:Times New Roman\">CHSL 11/8/2021 , Evening</span></p>",
                    options_en: [" <p> 999943</span></p>", " <p> 999945</span></p>", 
                                " <p> 999947   </span></p>", " <p> 999949</span></p>"],
                    options_hi: [" <p> 999943</span></p>", " <p> 999945      </span></p>",
                                " <p> 999947     </span></p>", " <p> 999949</span></p>"],
                    solution_en: " <p>11.(b)</span></p> <p><span style=\"font-family:Times New Roman\">Six digit largest number = 999999</span></p> <p><span style=\"font-family:Times New Roman\">When divided by 243 remainder is 55</span></p> <p><span style=\"font-family:Times New Roman\">Number = 999999-55 = 999944</span></p>",
                    solution_hi: " <p>11.(b) </span><span style=\"font-family:Baloo\">छह अंकों की सबसे बड़ी संख्या =</span><span style=\"font-family:Times New Roman\"> 999999</span></p> <p><span style=\"font-family:Baloo\">जब</span><span style=\"font-family:Times New Roman\"> 243 </span><span style=\"font-family:Baloo\">से विभाजित किया जाता है तो शेषफल</span><span style=\"font-family:Times New Roman\"> 55</span><span style=\"font-family:Baloo\"> होता है|</span></p> <p><span style=\"font-family:Baloo\">संख्या</span><span style=\"font-family:Times New Roman\"> = 999999-55 = 999944</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "17",
                    question_en: "<p>12. If the five-digit number 457ab is divisible by 3, 7 and 11, then what is the value of <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>a</mi><mn>2</mn></msup><mo>+</mo><msup><mi>b</mi><mn>2</mn></msup><mo>-</mo><mi>a</mi><mi>b</mi></math><span style=\"font-family: Times New Roman;\"> ?</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">SSC CHSL 12/8/2021 , Morning</span></p>\n",
                    question_hi: "<p>12. &#2351;<span style=\"font-family: Baloo;\">&#2342;&#2367; &#2346;&#2366;&#2305;&#2330; &#2309;&#2306;&#2325;&#2379;&#2306; &#2325;&#2368; &#2360;&#2306;&#2326;&#2381;&#2351;&#2366; 457ab, 3, 7 &#2324;&#2352; 11 &#2360;&#2375; &#2357;&#2367;&#2349;&#2366;&#2332;&#2381;&#2351; &#2361;&#2376;, &#2340;&#2379;</span><span style=\"font-family: Times New Roman;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>a</mi><mn>2</mn></msup><mo>+</mo><msup><mi>b</mi><mn>2</mn></msup><mo>-</mo><mi>a</mi><mi>b</mi><mo>&nbsp;</mo></math></span><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Baloo;\">&#2325;&#2366; &#2350;&#2366;&#2344; &#2325;&#2381;&#2351;&#2366; &#2361;&#2376;?</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">CHSL 12/8/2021 , Morning </span></p>\n",
                    options_en: ["<p>49</p>\n", "<p>36</p>\n", 
                                "<p>24</p>\n", "<p>33</p>\n"],
                    options_hi: ["<p>49</p>\n", "<p>36</p>\n",
                                "<p>24</p>\n", "<p>33</p>\n"],
                    solution_en: "<p>12.(a) <span style=\"font-family: Times New Roman;\">Short-trick:</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">3 &times; 7 &times; 11 = 231</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Take the largest value of the given number = 45799</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">When 45799 divided by 231, remainder is 61</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">number = 457ab = 45738</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">a = 3, b = 8</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">a&sup2; + b&sup2; - ab = 3&sup2; + 8&sup2; - 3 &times; 8 = 9 + 64 - 24 = 49 </span></p>\n",
                    solution_hi: "<p>12.(a) 3 &times; 7 &times; 11 = 231</p>\r\n<p><span style=\"font-family: Baloo;\">&#2342;&#2368; &#2327;&#2312; &#2360;&#2306;&#2326;&#2381;&#2351;&#2366; &#2325;&#2366; &#2360;&#2348;&#2360;&#2375; &#2348;&#2337;&#2364;&#2366; &#2350;&#2366;&#2344; &#2354;&#2368;&#2332;&#2367;&#2319;</span><span style=\"font-family: Times New Roman;\"> = 45799</span></p>\r\n<p><span style=\"font-family: Baloo;\">&#2332;&#2348;</span><span style=\"font-family: Times New Roman;\"> 45799 </span><span style=\"font-family: Baloo;\">&#2325;&#2379; </span><span style=\"font-family: Times New Roman;\">231 </span><span style=\"font-family: Baloo;\">&#2360;&#2375; &#2357;&#2367;&#2349;&#2366;&#2332;&#2367;&#2340; &#2325;&#2367;&#2351;&#2366; &#2332;&#2366;&#2340;&#2366; &#2361;&#2376;, &#2340;&#2379; &#2358;&#2375;&#2359;&#2347;&#2354;</span><span style=\"font-family: Times New Roman;\"> 61 </span><span style=\"font-family: Baloo;\">&#2361;&#2379;&#2340;&#2366; &#2361;&#2376;</span></p>\r\n<p><span style=\"font-family: Baloo;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366; =</span><span style=\"font-family: Times New Roman;\"> 457ab = 45738</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">a = 3, b = 8</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">a&sup2; + b&sup2; - ab = 3&sup2; + 8&sup2; - 3 &times; 8 = 9 + 64 - 24 = 49 </span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "17",
                    question_en: "<p>13. If the nine-digit number 259876p05 is completely divisible by 11, then what is the value of (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>p</mi><mn>2</mn></msup><mo>+</mo><mn>5</mn></math><span style=\"font-family: Times New Roman;\">)? </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">SSC CHSL 12/8/2021 , Afternoon</span></p>",
                    question_hi: "<p>13. <span style=\"font-family: Baloo;\">यदि नौ अंकों की संख्या</span><span style=\"font-family: Times New Roman;\"> 259876p05, 11 </span><span style=\"font-family: Baloo;\">से पूर्णतः विभाज्य है, तो</span><span style=\"font-family: Times New Roman;\"> (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>p</mi><mn>2</mn></msup><mo>+</mo><mn>5</mn></math></span><span style=\"font-family: Times New Roman;\">) </span><span style=\"font-family: Baloo;\">का मान क्या होगा?</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">CHSL 12/8/2021 , Afternoon</span></p>",
                    options_en: ["<p>48</p>", "<p>45</p>", 
                                "<p>54</p>", "<p>50</p>"],
                    options_hi: ["<p>48</p>", "<p>45</p>",
                                "<p>54</p>", "<p>50</p>"],
                    solution_en: "<p>13.(c) Sum of digits at odd places = 2 + 9 + 7 + p + 5 = 23 + p</p>\r\n<p><span style=\"font-family: Times New Roman;\">Sum of digits at even places = 5 + 8 + 6 + 0 = 19</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">23 + p - 19 = 0 or 11</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">p + 4 = 11 (as 0 is not possible)</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">p = 7 and <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#160;</mo><msup><mi>p</mi><mn>2</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>5</mn><mo>&#160;</mo></math></span><span style=\"font-family: Times New Roman;\">= 49 + 5 = 54</span></p>",
                    solution_hi: "<p>13.(c) <span style=\"font-family: Baloo;\">विषम स्थानों पर अंकों का योग </span><span style=\"font-family: Times New Roman;\">= 2 + 9 + 7 + p + 5 = 23 + p</span></p>\r\n<p><span style=\"font-family: Baloo;\">सम स्थानों पर अंकों का योग </span><span style=\"font-family: Times New Roman;\">= 5+8+6+0 = 19</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">23 + p - 19 = 0 or 11</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">p + 4 = 11 </span><span style=\"font-family: Baloo;\">(जैसा कि 0 संभव नहीं है)</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">p = 7 </span><span style=\"font-family: Baloo;\">और</span><span style=\"font-family: Times New Roman;\"> p</span><span style=\"font-family: Times New Roman;\">2</span><span style=\"font-family: Times New Roman;\"> + 5 = 49 + 5 = 54</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "misc",
                    question_en: "<p>14. If the nine-digit number 8475639AB is divisible by 99, then what is the value of A and B?</p>\r\n<p><span style=\"font-family: Times New Roman;\">SSC CHSL 12/8/2021 , Evening</span></p>",
                    question_hi: "<p>14. <span style=\"font-family: Baloo;\">यदि नौ अंकों की संख्या </span><span style=\"font-family: Times New Roman;\">8475639AB, 99 </span><span style=\"font-family: Baloo;\">से विभाज्य है, तो</span><span style=\"font-family: Times New Roman;\"> A</span><span style=\"font-family: Baloo;\"> और</span><span style=\"font-family: Times New Roman;\"> B </span><span style=\"font-family: Baloo;\">का मान क्या है?</span></p>\r\n<p><span style=\"font-family: Times New Roman;\"> SSC CHSL 12/8/2021 , Evening</span></p>",
                    options_en: ["<p>A=4, B=6</p>", "<p>A=5, B=3</p>", 
                                "<p>A=3, B=9</p>", "<p>A=4, B=8</p>"],
                    options_hi: ["<p>A=4, B=6</p>", "<p>A=5, B=3</p>",
                                "<p>A=3, B=9</p>", "<p>A=4, B=8</p>"],
                    solution_en: "<p>14.(d) For divisibility of 99, number should be divisible by 9 and 11</p>\r\n<p><span style=\"font-family: Times New Roman;\">For divisibility of 9, check digit sum</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Go through option</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">a. 8+4+7+5+6+3+9+4+6 = 49 (not divisible by 9)</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">b. 8+4+7+5+6+3+9+5+3 = 50 (not divisible by 9)</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">c. 8+4+7+5+6+3+9+3+9 = 54 (divisible by 9)</span></p>\r\n<p>(8+7+6+9+9) -(4+5+2+2)&nbsp; = 26 (not divisible by 11)</p>\r\n<p><span style=\"font-family: Times New Roman;\">d. 8+4+7+5+6+3+9+4+8 = 54 ( divisible by 9)</span></p>\r\n<p>(8+7+6+9+8) - (4+5+3+4) = 22 (divisible by 11)</p>",
                    solution_hi: "<p>14.(d) 99 <span style=\"font-family: Baloo;\">की विभाज्यता के लिए, संख्या</span><span style=\"font-family: Times New Roman;\"> 9 </span><span style=\"font-family: Baloo;\">और</span><span style=\"font-family: Times New Roman;\"> 11 </span><span style=\"font-family: Baloo;\">से विभाज्य होनी चाहिए</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">9 </span><span style=\"font-family: Baloo;\">की विभाज्यता के लिए, अंकों का योग जांचें</span></p>\r\n<p><span style=\"font-family: Baloo;\">विकल्प के माध्यम से जाओ</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">a. 8+4+7+5+6+3+9+4+6 = 49 (9 </span><span style=\"font-family: Baloo;\">से विभाज्य नहीं)</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">b. 8+4+7+5+6+3+9+5+3 = 50 (9 </span><span style=\"font-family: Baloo;\">से विभाज्य नहीं)</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">c. 8+4+7+5+6+3+9+3+9 = 54 (9 </span><span style=\"font-family: Baloo;\">से विभाज्य)</span></p>\r\n<p>(8+7+6+9+9) -(4+5+2+2)&nbsp; = 26 (11 से विभाज्य नहीं)</p>\r\n<p><span style=\"font-family: Times New Roman;\">d. 8+4+7+5+6+3+9+4+8 = 54 (9 </span><span style=\"font-family: Baloo;\">से विभाज्य)</span></p>\r\n<p>(8+7+6+9+8) - (4+5+3+4) = 22(11 से विभाज्य)</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>