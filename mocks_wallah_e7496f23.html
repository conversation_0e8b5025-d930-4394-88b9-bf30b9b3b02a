<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">15:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 15</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">15</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 15 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 0,
                end: 13
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 14,
                end: 14
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "17",
                    question_en: "<p>1. <span style=\"font-family: Cambria Math;\">A wholesaler has 1000 kg sugar, part of which he sells at 6% profit and the rest at 12% profit. His gain is 10% on the whole. The quantity sold at 6% profit is: (consider up to two decimals)</span></p>\n",
                    question_hi: "<p>1. <span style=\"font-family: Kohinoor Devanagari;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2341;&#2379;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2357;&#2381;&#2351;&#2366;&#2346;&#2366;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2346;&#2366;&#2360;</span><span style=\"font-family: Cambria Math;\"> 1000 kg </span><span style=\"font-family: Kohinoor Devanagari;\">&#2330;&#2368;&#2344;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Kohinoor Devanagari;\">&#2332;&#2367;&#2360;&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2361;&#2367;&#2360;&#2381;&#2360;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2357;&#2361;</span><span style=\"font-family: Cambria Math;\"> 6% </span><span style=\"font-family: Kohinoor Devanagari;\">&#2354;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2348;&#2375;&#2330;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2358;&#2375;&#2359;</span><span style=\"font-family: Cambria Math;\"> 12% </span><span style=\"font-family: Kohinoor Devanagari;\">&#2354;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2348;&#2375;&#2330;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2313;&#2360;&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2369;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2354;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\"> 10% </span><span style=\"font-family: Kohinoor Devanagari;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> 6% </span><span style=\"font-family: Kohinoor Devanagari;\">&#2354;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2348;&#2375;&#2330;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2327;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2350;&#2366;&#2340;&#2381;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">? (</span><span style=\"font-family: Kohinoor Devanagari;\">&#2342;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2342;&#2358;&#2350;&#2354;&#2357;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2340;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2354;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\">)</span></p>\n",
                    options_en: ["<p>336.33 kg</p>\n", "<p>663.66 kg</p>\n", 
                                "<p>333.33 kg</p>\n", "<p>666.66 kg</p>\n"],
                    options_hi: ["<p>336.33 kg</p>\n", "<p>663.66 kg</p>\n",
                                "<p>333.33 kg</p>\n", "<p>666.66 kg</p>\n"],
                    solution_en: "<p>1.(c)</p>\r\n<p>&nbsp;<img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1700549814/word/media/image2.png\" width=\"204\" height=\"140\"></p>\r\n<p><span style=\"font-family: Cambria Math;\">3 unit = 1000 kg </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">So, 1 unit (6%) = 333.33kg</span></p>\n",
                    solution_hi: "<p>1.(c)</p>\r\n<p>&nbsp;<img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1700549814/word/media/image4.png\" width=\"176\" height=\"127\"></p>\r\n<p><span style=\"font-family: Cambria Math;\">3 </span><span style=\"font-family: Kohinoor Devanagari;\">&#2311;&#2325;&#2366;&#2312;</span><span style=\"font-family: Cambria Math;\"> = 1000 kg </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">So, 1 </span><span style=\"font-family: Kohinoor Devanagari;\">&#2311;&#2325;&#2366;&#2312;</span><span style=\"font-family: Cambria Math;\"> (6%) = 333.33 kg</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "17",
                    question_en: "<p>2. <span style=\"font-family: Cambria Math;\">The profit percentage made on an item sold for Rs.1,920 is equal to the loss percentage made on selling the same item for Rs.1,280. What should the item be sold for in order to make a 25% profit?</span></p>\n",
                    question_hi: "<p>2. <span style=\"font-family: Cambria Math;\">1,920 </span><span style=\"font-family: Kohinoor Devanagari;\">&#2352;&#2369;&#2346;&#2351;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2348;&#2375;&#2330;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2327;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2357;&#2360;&#2381;&#2340;&#2369;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2309;&#2352;&#2381;&#2332;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2354;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2313;&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2357;&#2360;&#2381;&#2340;&#2369;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> 1,280 </span><span style=\"font-family: Kohinoor Devanagari;\">&#2352;&#2369;&#2346;&#2351;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2348;&#2375;&#2330;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2361;&#2369;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2361;&#2366;&#2344;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2348;&#2352;&#2366;&#2348;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> 25% </span><span style=\"font-family: Kohinoor Devanagari;\">&#2354;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2350;&#2366;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2357;&#2360;&#2381;&#2340;&#2369;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2367;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2368;&#2350;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2348;&#2375;&#2330;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2332;&#2366;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2330;&#2366;&#2361;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>Rs.1,960</p>\n", "<p>Rs.2,020</p>\n", 
                                "<p>Rs.2,000</p>\n", "<p>Rs.1,940</p>\n"],
                    options_hi: ["<p>Rs.1,960</p>\n", "<p>Rs.2,020</p>\n",
                                "<p>Rs.2,000</p>\n", "<p>Rs.1,940</p>\n"],
                    solution_en: "<p>2.(c)&nbsp;<span style=\"font-weight: 400;\">Let the price be Rs. </span><span style=\"font-weight: 400;\">x</span></p>\r\n<p><span style=\"font-weight: 400;\">1920 </span><span style=\"font-weight: 400;\">-</span><span style=\"font-weight: 400;\"> </span><span style=\"font-weight: 400;\">x</span><span style=\"font-weight: 400;\"> = </span><span style=\"font-weight: 400;\">x</span><span style=\"font-weight: 400;\"> </span><span style=\"font-weight: 400;\">-</span><span style=\"font-weight: 400;\"> 1280</span></p>\r\n<p><span style=\"font-weight: 400;\">2</span><span style=\"font-weight: 400;\">x</span><span style=\"font-weight: 400;\"> = 3200 &rArr;</span><span style=\"font-weight: 400;\"> </span><span style=\"font-weight: 400;\">x</span><span style=\"font-weight: 400;\"> = 1600</span></p>\r\n<p><span style=\"font-weight: 400;\">Profit = 25 %</span></p>\r\n<p><span style=\"font-weight: 400;\">Required price = 1600 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>125</mn><mn>100</mn></mfrac></math></span><span style=\"font-weight: 400;\">&nbsp;= Rs. 2000</span></p>\n",
                    solution_hi: "<p>2.(c) <span style=\"font-family: Kohinoor Devanagari;\">&#2350;&#2366;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2368;&#2350;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2352;&#2369;</span><span style=\"font-family: Cambria Math;\">. x </span><span style=\"font-family: Kohinoor Devanagari;\">&#2361;&#2376;</span></p>\r\n<p><span style=\"font-weight: 400;\">&rArr; 1920 </span><span style=\"font-weight: 400;\">-</span><span style=\"font-weight: 400;\"> </span><span style=\"font-weight: 400;\">x</span><span style=\"font-weight: 400;\"> = </span><span style=\"font-weight: 400;\">x</span><span style=\"font-weight: 400;\"> </span><span style=\"font-weight: 400;\">-</span><span style=\"font-weight: 400;\"> 1280</span></p>\r\n<p><span style=\"font-weight: 400;\">&rArr; 2</span><span style=\"font-weight: 400;\">x</span><span style=\"font-weight: 400;\"> = 3200 &rArr;</span><span style=\"font-weight: 400;\"> </span><span style=\"font-weight: 400;\">x</span><span style=\"font-weight: 400;\"> = 1600</span></p>\r\n<p><span style=\"font-weight: 400;\">&#2354;&#2366;&#2349; = 25 %</span></p>\r\n<p><span style=\"font-weight: 400;\">&#2310;&#2357;&#2358;&#2381;&#2351;&#2325; &#2350;&#2370;&#2354;&#2381;&#2351; = 1600 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>125</mn><mn>100</mn></mfrac></math></span><span style=\"font-weight: 400;\"> = &#2352;&#2369;. 2000</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "17",
                    question_en: "<p>3. <span style=\"font-family: Cambria Math;\">An almirah was bought for &#8377;15,500 and &#8377;260 was spent on its transportation. At what price should it be sold to gain 20%?</span></p>\n",
                    question_hi: "<p>3. <span style=\"font-family: Kohinoor Devanagari;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2309;&#2354;&#2350;&#2366;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> &#8377; 15,500 </span><span style=\"font-family: Kohinoor Devanagari;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2326;&#2352;&#2368;&#2342;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2327;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> &#8377; 260 </span><span style=\"font-family: Kohinoor Devanagari;\">&#2311;&#2360;&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2346;&#2352;&#2367;&#2357;&#2361;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2326;&#2352;&#2381;&#2330;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2327;&#2351;&#2366;&#2404;</span><span style=\"font-family: Cambria Math;\"> 20% </span><span style=\"font-family: Kohinoor Devanagari;\">&#2354;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2346;&#2381;&#2352;&#2366;&#2346;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2352;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2311;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2367;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2348;&#2375;&#2330;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2332;&#2366;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2330;&#2366;&#2361;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>&#8377;18,912</p>\n", "<p>&#8377;18,910</p>\n", 
                                "<p>&#8377;18,914</p>\n", "<p>&#8377;18,916</p>\n"],
                    options_hi: ["<p>&#8377;18,912</p>\n", "<p>&#8377;18,910</p>\n",
                                "<p>&#8377;18,914</p>\n", "<p>&#8377;18,916</p>\n"],
                    solution_en: "<p>3.(a) <span style=\"font-family: Cambria Math;\">C.P = &#8377;(15500 + 260) = &#8377;15760</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Profit = 20%</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">S.P = 15760 &times;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>120</mn><mn>100</mn></mfrac></math> </span><span style=\"font-family: Cambria Math;\"> = &#8377; 18912</span></p>\n",
                    solution_hi: "<p>3.(a) <span style=\"font-weight: 400;\">C.P = &#8377;(15500 + 260) =&nbsp; &#8377;15760</span></p>\r\n<p><span style=\"font-weight: 400;\">&#2354;&#2366;&#2349; = 20%</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">S.P = 15760 &times;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>120</mn><mn>100</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> = &#8377; 18912</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "17",
                    question_en: "<p>4. <span style=\"font-family: Cambria Math;\">Samir purchased 20 dozens of toys at the rate of Rs.372 per dozen. He sold each one of them at the rate of Rs.32. What was his percentage profit?</span></p>\n",
                    question_hi: "<p>4. <span style=\"font-family: Kohinoor Devanagari;\">&#2360;&#2350;&#2368;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> 372 </span><span style=\"font-family: Kohinoor Devanagari;\">&#2352;&#2369;&#2346;&#2351;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2346;&#2381;&#2352;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2342;&#2352;&#2381;&#2332;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2342;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">20 </span><span style=\"font-family: Kohinoor Devanagari;\">&#2342;&#2352;&#2381;&#2332;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2326;&#2367;&#2354;&#2380;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2326;&#2352;&#2368;&#2342;&#2375;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2313;&#2360;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2313;&#2344;&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2346;&#2381;&#2352;&#2340;&#2381;&#2351;&#2375;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> 32 </span><span style=\"font-family: Kohinoor Devanagari;\">&#2352;&#2369;&#2346;&#2351;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2342;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2348;&#2375;&#2330;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2342;&#2367;&#2351;&#2366;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2313;&#2360;&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2354;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2341;&#2366;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>4.2%</p>\n", "<p>3.6%</p>\n", 
                                "<p>3.2%</p>\n", "<p>2.2%</p>\n"],
                    options_hi: ["<p>4.2%</p>\n", "<p>3.6%</p>\n",
                                "<p>3.2%</p>\n", "<p>2.2%</p>\n"],
                    solution_en: "<p>4.(c)&nbsp;<span style=\"font-weight: 400;\">C.P of 1 dozen toys = 372</span></p>\r\n<p><span style=\"font-weight: 400;\">C.P of 1 toys = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>372</mn><mn>12</mn></mfrac></math></span><span style=\"font-weight: 400;\">&nbsp;= &#8377;31</span></p>\r\n<p><span style=\"font-weight: 400;\">S.P of 1 toys = &#8377;32</span></p>\r\n<p><span style=\"font-weight: 400;\">Profit percentage = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>32</mn><mo>&nbsp;</mo><mo>-</mo><mn>31</mn></mrow><mn>31</mn></mfrac></math></span><span style=\"font-weight: 400;\">&times; 100 = 3.2%</span></p>\n",
                    solution_hi: "<p>4.(c) <span style=\"font-weight: 400;\">1 &#2342;&#2352;&#2381;&#2332;&#2344; &#2326;&#2367;&#2354;&#2380;&#2344;&#2379;&#2306; &#2325;&#2366; &#2325;&#2381;&#2352;&#2351; &#2350;&#2370;&#2354;&#2381;&#2351; </span><span style=\"font-weight: 400;\">= 372</span></p>\r\n<p><span style=\"font-weight: 400;\">1 &#2326;&#2367;&#2354;&#2380;&#2344;&#2375; &#2325;&#2366; &#2325;&#2381;&#2352;&#2351; &#2350;&#2370;&#2354;&#2381;&#2351; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>372</mn><mn>12</mn></mfrac></math></span><span style=\"font-weight: 400;\"> = 31&#8377;</span></p>\r\n<p><span style=\"font-weight: 400;\">1 &#2326;&#2367;&#2354;&#2380;&#2344;&#2375; &#2325;&#2366; &#2357;&#2367;&#2325;&#2381;&#2352;&#2351; &#2350;&#2370;&#2354;&#2381;&#2351; = 32&#8377;</span></p>\r\n<p><span style=\"font-weight: 400;\">&#2354;&#2366;&#2349; &#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>32</mn><mo>&nbsp;</mo><mo>-</mo><mn>31</mn></mrow><mn>31</mn></mfrac></math></span><span style=\"font-weight: 400;\">&times; 100 = 3.2%</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "17",
                    question_en: "<p>5. <span style=\"font-family: Cambria Math;\">Suresh sold his mobile phone to Mohan for &#8377;29,700 at a gain of 10%. What was the cost price of the phone for Suresh?</span></p>\n",
                    question_hi: "<p>5. <span style=\"font-family: Kohinoor Devanagari;\">&#2360;&#2369;&#2352;&#2375;&#2358;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2309;&#2346;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2350;&#2379;&#2348;&#2366;&#2311;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2347;&#2379;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2350;&#2379;&#2361;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> &#8377;29,700 </span><span style=\"font-family: Kohinoor Devanagari;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> 10% </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2354;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2348;&#2375;&#2330;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2342;&#2367;&#2351;&#2366;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2360;&#2369;&#2352;&#2375;&#2358;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2347;&#2364;&#2379;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2381;&#2352;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2341;&#2366;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>&#8377;25,000</p>\n", "<p>&#8377;28,000</p>\n", 
                                "<p>&#8377;26,000</p>\n", "<p>&#8377;27,000</p>\n"],
                    options_hi: ["<p>&#8377;25,000</p>\n", "<p>&#8377;28,000</p>\n",
                                "<p>&#8377;26,000</p>\n", "<p>&#8377;27,000</p>\n"],
                    solution_en: "<p>5.(d) <span style=\"font-weight: 400;\">C.P of the phone &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>11</mn><mn>10</mn></mfrac></math></span><span style=\"font-weight: 400;\">&nbsp;= 29700&nbsp;</span></p>\r\n<p><span style=\"font-weight: 400;\">C.P = 27000 &#8377;</span></p>\n",
                    solution_hi: "<p>5.(d) <span style=\"font-weight: 400;\">&#2347;&#2364;&#2379;&#2344; &#2325;&#2366; &#2325;&#2381;&#2352;&#2351; &#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-weight: 400;\">&nbsp;&times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>11</mn><mn>10</mn></mfrac></math></span><span style=\"font-weight: 400;\">&nbsp;= 29700&nbsp;</span></p>\r\n<p><span style=\"font-weight: 400;\">&#2325;&#2381;&#2352;&#2351; &#2350;&#2370;&#2354;&#2381;&#2351; &nbsp;= 27000 &#8377;</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "17",
                    question_en: "<p>6. <span style=\"font-family: Cambria Math;\">A merchant bought 1200 items for &#8377;1,800 and he sold them at the rate of 1000 for &#8377;1,600. Calculate gain or loss percentage.</span></p>\n",
                    question_hi: "<p>6. <span style=\"font-family: Kohinoor Devanagari;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2357;&#2381;&#2351;&#2366;&#2346;&#2366;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> &#8377;1,800 </span><span style=\"font-family: Kohinoor Devanagari;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> 1200 </span><span style=\"font-family: Kohinoor Devanagari;\">&#2357;&#2360;&#2381;&#2340;&#2369;&#2319;&#2305;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2326;&#2352;&#2368;&#2342;&#2368;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2313;&#2360;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2313;&#2344;&#2381;&#2361;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> &#8377;1,600 </span><span style=\"font-family: Kohinoor Devanagari;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> 1000 </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2342;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2348;&#2375;&#2330;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2342;&#2367;&#2351;&#2366;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2354;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2361;&#2366;&#2344;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2327;&#2339;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2352;&#2375;&#2306;&#2404;</span></p>\n",
                    options_en: ["<p><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>20</mn><mn>3</mn></mfrac></math>%loss</span></p>\n", "<p><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>30</mn><mn>7</mn></mfrac></math>% loss</span></p>\n", 
                                "<p><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>30</mn><mn>7</mn></mfrac></math>% gain</span></p>\n", "<p><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>20</mn><mn>3</mn></mfrac></math>%gain</span></p>\n"],
                    options_hi: ["<p><span style=\"font-family: Kohinoor Devanagari;\"><span style=\"font-weight: 400;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>20</mn><mn>3</mn></mfrac></math>%</span>&#2361;&#2366;&#2344;&#2367;</span></p>\n", "<p><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>30</mn><mn>7</mn></mfrac></math>% </span><span style=\"font-family: Kohinoor Devanagari;\">&#2361;&#2366;&#2344;&#2367;</span></p>\n",
                                "<p><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>30</mn><mn>7</mn></mfrac></math>% </span><span style=\"font-family: Kohinoor Devanagari;\">&#2354;&#2366;&#2349;</span></p>\n", "<p><span style=\"font-family: Kohinoor Devanagari;\"><span style=\"font-weight: 400;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>20</mn><mn>3</mn></mfrac></math>%</span>&#2354;&#2366;&#2349;</span></p>\n"],
                    solution_en: "<p>6.(d) <span style=\"font-weight: 400;\">C.P of the 1000&nbsp; articles&nbsp;</span></p>\r\n<p><span style=\"font-weight: 400;\">=</span><span style=\"font-weight: 400;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1800</mn><mn>1200</mn></mfrac></math></span><span style=\"font-weight: 400;\">&nbsp;</span><span style=\"font-weight: 400;\">&times; 1000 = 1500&#8377;</span></p>\r\n<p><span style=\"font-weight: 400;\">S.P of 1000 articles = 1600&#8377;</span></p>\r\n<p><span style=\"font-weight: 400;\">Profit % = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1600</mn><mo>-</mo><mn>1500</mn></mrow><mn>1500</mn></mfrac></math></span><span style=\"font-weight: 400;\">&nbsp;&times; 100&nbsp;</span></p>\r\n<p><span style=\"font-weight: 400;\">= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>100</mn><mn>15</mn></mfrac></math></span><span style=\"font-weight: 400;\"> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>20</mn><mn>3</mn></mfrac></math></span><span style=\"font-weight: 400;\">%&nbsp;</span></p>\n",
                    solution_hi: "<p>6.(d) <span style=\"font-weight: 400;\">1000 &#2357;&#2360;&#2381;&#2340;&#2369;&#2323;&#2306; &#2325;&#2366; &#2325;&#2381;&#2352;&#2351; &#2350;&#2370;&#2354;&#2381;&#2351; </span></p>\r\n<p><span style=\"font-weight: 400;\">=</span><span style=\"font-weight: 400;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1800</mn><mn>1200</mn></mfrac></math></span><span style=\"font-weight: 400;\">&nbsp;</span><span style=\"font-weight: 400;\">&times; 1000 = 1500&#8377;</span></p>\r\n<p><span style=\"font-weight: 400;\">1000 &#2357;&#2360;&#2381;&#2340;&#2369;&#2323;&#2306; &#2325;&#2366; &#2357;&#2367;&#2325;&#2381;&#2352;&#2351; &#2350;&#2370;&#2354;&#2381;&#2351; = 1600&#8377;</span></p>\r\n<p><span style=\"font-weight: 400;\">&#2354;&#2366;&#2349; &#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340; % = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1600</mn><mo>-</mo><mn>1500</mn></mrow><mn>1500</mn></mfrac></math></span><span style=\"font-weight: 400;\">&nbsp;&times; 100&nbsp;</span></p>\r\n<p><span style=\"font-weight: 400;\">= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>100</mn><mn>15</mn></mfrac></math></span><span style=\"font-weight: 400;\"> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>20</mn><mn>3</mn></mfrac></math></span><span style=\"font-weight: 400;\">%&nbsp;</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "17",
                    question_en: "<p>7. <span style=\"font-family: Cambria Math;\">A dealer sells wheat at 25% profit </span><span style=\"font-family: Cambria Math;\">and uses a weight 5% less than the actual measure. His gain percentage is:</span></p>\n",
                    question_hi: "<p>7. <span style=\"font-family: Kohinoor Devanagari;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2337;&#2368;&#2354;&#2352;</span><span style=\"font-family: Cambria Math;\"> 25% </span><span style=\"font-family: Kohinoor Devanagari;\">&#2354;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2327;&#2375;&#2361;&#2370;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2348;&#2375;&#2330;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2357;&#2366;&#2360;&#2381;&#2340;&#2357;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2350;&#2366;&#2346;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> 5% </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2357;&#2332;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2313;&#2346;&#2351;&#2379;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2352;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2313;&#2360;&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2354;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">:</span></p>\n",
                    options_en: ["<p>3<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>11</mn><mn>19</mn></mfrac></math><span style=\"font-family: Cambria Math;\">% </span></p>\n", "<p>3<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>19</mn></mfrac></math><span style=\"font-family: Cambria Math;\">%</span></p>\n", 
                                "<p>23<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>19</mn></mfrac></math><span style=\"font-family: Cambria Math;\">% </span></p>\n", "<p>31<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>11</mn><mn>19</mn></mfrac></math><span style=\"font-family: Cambria Math;\">%</span></p>\n"],
                    options_hi: ["<p>3<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>11</mn><mn>19</mn></mfrac></math><span style=\"font-family: Cambria Math;\">% </span></p>\n", "<p>3<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>19</mn></mfrac></math><span style=\"font-family: Cambria Math;\">%</span></p>\n",
                                "<p>23<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>19</mn></mfrac></math><span style=\"font-family: Cambria Math;\">% </span></p>\n", "<p>31<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>11</mn><mn>19</mn></mfrac></math><span style=\"font-family: Cambria Math;\">%</span></p>\n"],
                    solution_en: "<p>7.(d)&nbsp;</p>\r\n<p><span style=\"font-weight: 400;\">Ratio&rarr; &nbsp;</span><span style=\"font-weight: 400;\">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; C.P &nbsp; :&nbsp; S.P</span></p>\r\n<p><span style=\"font-weight: 400;\">Profit&rarr;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;</span><span style=\"font-weight: 400;\">4&nbsp; &nbsp; : &nbsp; 5</span></p>\r\n<p><span style=\"text-decoration: underline;\"><span style=\"font-weight: 400;\">Faulty weight&rarr; </span><span style=\"font-weight: 400;\">&nbsp; &nbsp; 950 &nbsp; :&nbsp; 1000&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp;</span></span></p>\r\n<p><span style=\"font-weight: 400;\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Final&rarr;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; </span><span style=\"font-weight: 400;\">19&nbsp; &nbsp; : &nbsp; 25&nbsp;&nbsp;</span></p>\r\n<p><span style=\"font-weight: 400;\">Profit % = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>25</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>19</mn></mrow><mn>19</mn></mfrac></math></span><span style=\"font-weight: 400;\">&nbsp;&times; 100&nbsp;</span></p>\r\n<p><span style=\"font-weight: 400;\">= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>600</mn><mn>19</mn></mfrac></math></span><span style=\"font-weight: 400;\"> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>31</mn><mfrac><mn>11</mn><mn>19</mn></mfrac></math></span><span style=\"font-weight: 400;\">%</span></p>\n",
                    solution_hi: "<p>7.(d)&nbsp;</p>\r\n<p><span style=\"font-weight: 400;\">&#2309;&#2344;&#2369;&#2346;&#2366;&#2340;&rarr; &nbsp;</span><span style=\"font-weight: 400;\">&nbsp; &nbsp; &nbsp; &#2325;&#2381;&#2352;&#2351; &#2350;&#2370;&#2354;&#2381;&#2351; &nbsp; : &nbsp;&#2357;&#2367;&#2325;&#2381;&#2352;&#2351; &#2350;&#2370;&#2354;&#2381;&#2351;</span></p>\r\n<p><span style=\"font-weight: 400;\">&#2354;&#2366;&#2349;&rarr;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;</span><span style=\"font-weight: 400;\">4&nbsp; &nbsp; : &nbsp; 5</span></p>\r\n<p><span style=\"text-decoration: underline;\"><span style=\"font-weight: 400;\">&#2342;&#2379;&#2359;&#2346;&#2370;&#2352;&#2381;&#2339; &#2357;&#2332;&#2344;&rarr; </span><span style=\"font-weight: 400;\">&nbsp; &nbsp; 950&nbsp; &nbsp; :&nbsp; 1000&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp;</span></span></p>\r\n<p><span style=\"font-weight: 400;\">&nbsp;&#2309;&#2306;&#2340;&#2367;&#2350;&rarr;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; </span><span style=\"font-weight: 400;\">19&nbsp; &nbsp;: &nbsp; 25&nbsp;&nbsp;</span></p>\r\n<p><span style=\"font-weight: 400;\">&#2354;&#2366;&#2349; &#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340; % = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>25</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>19</mn></mrow><mn>19</mn></mfrac></math></span><span style=\"font-weight: 400;\">&nbsp;&times; 100&nbsp;</span></p>\r\n<p><span style=\"font-weight: 400;\">= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>600</mn><mn>19</mn></mfrac></math></span><span style=\"font-weight: 400;\"> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>31</mn><mfrac><mn>11</mn><mn>19</mn></mfrac></math></span><span style=\"font-weight: 400;\">%</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "17",
                    question_en: "<p>8.<span style=\"font-family: Cambria Math;\"> Rakhi sold a TV to Sujata at a profit of 10%. Urmila sold that TV to Rakhi at a profit of 20%. How much did Sujata pay to buy the TV if the cost price for Urmila was Rs.50,000?</span></p>\n",
                    question_hi: "<p>8.<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2352;&#2366;&#2326;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> 10% </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2354;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2360;&#2369;&#2332;&#2366;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> T.V. </span><span style=\"font-family: Kohinoor Devanagari;\">&#2348;&#2375;&#2330;&#2366;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2313;&#2352;&#2381;&#2350;&#2367;&#2354;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2313;&#2360;</span><span style=\"font-family: Cambria Math;\"> T.V. </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2352;&#2366;&#2326;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> 20% </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2354;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2348;&#2375;&#2330;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2342;&#2367;&#2351;&#2366;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2313;&#2352;&#2381;&#2350;&#2367;&#2354;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2354;&#2366;&#2327;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> 50,000 </span><span style=\"font-family: Kohinoor Devanagari;\">&#2352;&#2369;&#2346;&#2351;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2341;&#2366;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Kohinoor Devanagari;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2360;&#2369;&#2332;&#2366;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2335;&#2368;&#2357;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2326;&#2352;&#2368;&#2342;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2367;&#2340;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2349;&#2369;&#2327;&#2340;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> ? </span></p>\n",
                    options_en: ["<p>Rs.66,000</p>\n", "<p>Rs.72,000</p>\n", 
                                "<p>Rs.78,000</p>\n", "<p>Rs.65,000</p>\n"],
                    options_hi: ["<p>Rs.66,000</p>\n", "<p>Rs.72,000</p>\n",
                                "<p>Rs.78,000</p>\n", "<p>Rs.65,000</p>\n"],
                    solution_en: "<p>8.(a) <span style=\"font-weight: 400;\">C.P of rakhi = 50000 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>6</mn><mn>5</mn></mfrac></math></span><span style=\"font-weight: 400;\">&nbsp; = &#8377;60000</span></p>\r\n<p><span style=\"font-weight: 400;\">C.P of Sujata = 60000 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>11</mn><mn>10</mn></mfrac></math></span><span style=\"font-weight: 400;\">&nbsp;=&nbsp; &#8377;66000</span></p>\n",
                    solution_hi: "<p>8.(a) <span style=\"font-weight: 400;\">&#2352;&#2366;&#2326;&#2368; &#2325;&#2366; &#2325;&#2381;&#2352;&#2351; &#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-weight: 400;\">&nbsp;= 50000 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>6</mn><mn>5</mn></mfrac></math></span><span style=\"font-weight: 400;\">&nbsp; = &#8377;60000</span></p>\r\n<p><span style=\"font-weight: 400;\">&#2360;&#2369;&#2332;&#2366;&#2340;&#2366; &#2325;&#2366; &#2325;&#2381;&#2352;&#2351; &#2350;&#2370;&#2354;&#2381;&#2351; = 60000 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>11</mn><mn>10</mn></mfrac></math></span><span style=\"font-weight: 400;\">&nbsp;=&nbsp; &#8377;66000</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "17",
                    question_en: "<p>9.<span style=\"font-family: Cambria Math;\"> A seller marked the price 25% more than its cost price. If he allows a discount of 42%, then find his loss percent.</span></p>\n",
                    question_hi: "<p>9.<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2357;&#2367;&#2325;&#2381;&#2352;&#2375;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2309;&#2346;&#2344;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2354;&#2366;&#2327;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> 25% </span><span style=\"font-family: Kohinoor Devanagari;\">&#2309;&#2343;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2368;&#2350;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2309;&#2306;&#2325;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2368;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2357;&#2361;</span><span style=\"font-family: Cambria Math;\"> 42% </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2331;&#2370;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2342;&#2375;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Kohinoor Devanagari;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2313;&#2360;&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2361;&#2366;&#2344;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2332;&#2381;&#2334;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2368;&#2332;&#2367;&#2319;&#2404;</span></p>\n",
                    options_en: ["<p>33.5%</p>\n", "<p>27.5%</p>\n", 
                                "<p>30%</p>\n", "<p>8.5%</p>\n"],
                    options_hi: ["<p>33.5%</p>\n", "<p>27.5%</p>\n",
                                "<p>30%</p>\n", "<p>8.5%</p>\n"],
                    solution_en: "<p>9.(b) <span style=\"font-weight: 400;\">Let C.P</span><span style=\"font-weight: 400;\">&nbsp; = 100 &#8377;</span></p>\r\n<p><span style=\"font-weight: 400;\">M.P &nbsp;= 100 &times;</span><span style=\"font-weight: 400;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>4</mn></mfrac></math></span><span style=\"font-weight: 400;\">&nbsp;= 125 &#8377;</span></p>\r\n<p><span style=\"font-weight: 400;\">S.P &nbsp;= 125 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>29</mn><mn>50</mn></mfrac></math></span><span style=\"font-weight: 400;\">&nbsp;</span><span style=\"font-weight: 400;\">=&nbsp; 72.5</span></p>\r\n<p><span style=\"font-weight: 400;\">Percentage loss &nbsp;= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>100</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>72</mn><mo>.</mo><mn>5</mn></mrow><mn>100</mn></mfrac></math></span><span style=\"font-weight: 400;\">&times; 100&nbsp;</span></p>\r\n<p><span style=\"font-weight: 400;\">= 27.5 % (loss)</span></p>\r\n<p><strong>Short trick :-</strong></p>\r\n<p><span style=\"font-weight: 400;\">25 </span><span style=\"font-weight: 400;\">- </span><span style=\"font-weight: 400;\">42 </span><span style=\"font-weight: 400;\">+ <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>25</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mo>(</mo><mo>-</mo><mn>42</mn><mo>)</mo></mrow><mn>100</mn></mfrac></math></span><span style=\"font-weight: 400;\">&nbsp;= </span><span style=\"font-weight: 400;\">-</span><span style=\"font-weight: 400;\">27.5% (loss)</span></p>\n",
                    solution_hi: "<p>9.(b)&nbsp;<span style=\"font-weight: 400;\">&#2350;&#2366;&#2344;&#2366; &#2354;&#2366;&#2327;&#2340; &#2350;&#2370;&#2354;&#2381;&#2351;&nbsp; = 100 &#8377;</span></p>\r\n<p><span style=\"font-weight: 400;\">&#2309;&#2306;&#2325;&#2367;&#2340; &#2350;&#2370;&#2354;&#2381;&#2351; &nbsp; = 100 &times;</span><span style=\"font-weight: 400;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>4</mn></mfrac></math></span><span style=\"font-weight: 400;\">&nbsp;= 125 &#8377;</span></p>\r\n<p><span style=\"font-weight: 400;\">&#2357;&#2367;&#2325;&#2381;&#2352;&#2351; &#2350;&#2370;&#2354;&#2381;&#2351;&nbsp; = 125 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>29</mn><mn>50</mn></mfrac></math></span><span style=\"font-weight: 400;\">&nbsp;</span><span style=\"font-weight: 400;\">=&nbsp; 72.5</span></p>\r\n<p><span style=\"font-weight: 400;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340; &#2361;&#2366;&#2344;&#2367; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>100</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>72</mn><mo>.</mo><mn>5</mn></mrow><mn>100</mn></mfrac></math></span><span style=\"font-weight: 400;\">&times; 100&nbsp;</span></p>\r\n<p><span style=\"font-weight: 400;\">= 27.5 % (&#2361;&#2366;&#2344;&#2367;)</span></p>\r\n<p><strong>&#2358;&#2366;&#2352;&#2381;&#2335; &#2335;&#2381;&#2352;&#2367;&#2325; :-</strong></p>\r\n<p><span style=\"font-weight: 400;\">25 </span><span style=\"font-weight: 400;\">- </span><span style=\"font-weight: 400;\">42 </span><span style=\"font-weight: 400;\">+ <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>25</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mo>(</mo><mo>-</mo><mo>&nbsp;</mo><mn>42</mn><mo>)</mo></mrow><mn>100</mn></mfrac></math></span><span style=\"font-weight: 400;\">&nbsp;= </span><span style=\"font-weight: 400;\">-</span><span style=\"font-weight: 400;\">27.5% (&#2361;&#2366;&#2344;&#2367;)</span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "17",
                    question_en: "<p>10.<span style=\"font-family: Cambria Math;\"> A shopkeeper earns a profit of 8% after offering a 15% discount on the marked price. To clear the stock, the shopkeeper offers a successive discount of x% and earns no profit or loss. What is the value (approximate) of \'x\' ?</span></p>\n",
                    question_hi: "<p>10.<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2309;&#2306;&#2325;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> 15% </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2331;&#2370;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2342;&#2375;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2348;&#2366;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2342;&#2369;&#2325;&#2366;&#2344;&#2342;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> 8% </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2354;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2309;&#2352;&#2381;&#2332;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2352;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2360;&#2381;&#2335;&#2377;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2326;&#2366;&#2354;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2352;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Kohinoor Devanagari;\">&#2342;&#2369;&#2325;&#2366;&#2344;&#2342;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> x% </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2381;&#2352;&#2350;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2331;&#2370;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2323;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2346;&#2381;&#2352;&#2342;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2352;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2379;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2354;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2361;&#2366;&#2344;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2344;&#2361;&#2368;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2350;&#2366;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> \'x\' </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> (</span><span style=\"font-family: Kohinoor Devanagari;\">&#2309;&#2344;&#2369;&#2350;&#2366;&#2344;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\">) </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>7.8</p>\n", "<p>7.4</p>\n", 
                                "<p>7.6</p>\n", "<p>8.0</p>\n"],
                    options_hi: ["<p>7.8</p>\n", "<p>7.4</p>\n",
                                "<p>7.6</p>\n", "<p>8.0</p>\n"],
                    solution_en: "<p>10.(b)&nbsp;<span style=\"font-weight: 400;\">According to the question,</span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>C</mi><mi>P</mi></mrow><mrow><mo>&nbsp;</mo><mi>M</mi><mi>P</mi></mrow></mfrac><mo>=</mo><mfrac><mn>85</mn><mn>108</mn></mfrac></math></p>\r\n<p><span style=\"font-weight: 400;\">After the discount of 15 % , selling price&nbsp;</span></p>\r\n<p><span style=\"font-weight: 400;\"> </span><span style=\"font-weight: 400;\">108 &times;</span><span style=\"font-weight: 400;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>85</mn><mn>100</mn></mfrac></math></span><span style=\"font-weight: 400;\">&nbsp;= 91.80 Rs.</span></p>\r\n<p><span style=\"font-weight: 400;\">For the no profit no loss&nbsp;</span></p>\r\n<p><span style=\"font-weight: 400;\">Additional discount %&nbsp;</span></p>\r\n<p><span style=\"font-weight: 400;\">= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>91</mn><mo>.</mo><mn>80</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>85</mn></mrow><mrow><mn>91</mn><mo>.</mo><mn>80</mn></mrow></mfrac></math></span><span style=\"font-weight: 400;\">&nbsp;</span><span style=\"font-weight: 400;\">&times; 100</span><span style=\"font-weight: 400;\"> = 7.4%</span></p>\n",
                    solution_hi: "<p>10.(b)&nbsp;<span style=\"font-weight: 400;\">&#2346;&#2381;&#2352;&#2358;&#2381;&#2344; &#2325;&#2375; &#2309;&#2344;&#2369;&#2360;&#2366;&#2352;,</span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>C</mi><mi>P</mi></mrow><mrow><mo>&nbsp;</mo><mi>M</mi><mi>P</mi></mrow></mfrac><mo>=</mo><mfrac><mn>85</mn><mn>108</mn></mfrac></math></p>\r\n<p><span style=\"font-weight: 400;\">15% &#2325;&#2368; &#2331;&#2370;&#2335; &#2325;&#2375; &#2348;&#2366;&#2342;, &#2357;&#2367;&#2325;&#2381;&#2352;&#2351; &#2350;&#2370;&#2354;&#2381;&#2351;</span></p>\r\n<p><span style=\"font-weight: 400;\"> </span><span style=\"font-weight: 400;\">108 &times;</span><span style=\"font-weight: 400;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>85</mn><mn>100</mn></mfrac></math></span><span style=\"font-weight: 400;\"> = 91.80 </span><span style=\"font-weight: 400;\">&#2352;&#2369;.</span></p>\r\n<p><span style=\"font-weight: 400;\">&#2344;&#2366; &#2354;&#2366;&#2349; &#2344;&#2366; &#2361;&#2366;&#2344;&#2367; &#2325;&#2375; &#2354;&#2367;&#2319; &#2309;&#2340;&#2367;&#2352;&#2367;&#2325;&#2381;&#2340; &#2331;&#2370;&#2335;% </span></p>\r\n<p><span style=\"font-weight: 400;\">= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>91</mn><mo>.</mo><mn>80</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>85</mn></mrow><mrow><mn>91</mn><mo>.</mo><mn>80</mn></mrow></mfrac></math></span><span style=\"font-weight: 400;\">&nbsp;</span><span style=\"font-weight: 400;\">&times; 100</span><span style=\"font-weight: 400;\"> = 7.4%</span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "17",
                    question_en: "<p>11.<span style=\"font-family: Cambria Math;\"> A merchant marks up his goods by 20% and gives a discount of 10%. He uses a faulty balance which measures 800 grams instead of 1 kg. What is his net percentage profit?</span></p>\n",
                    question_hi: "<p>11.<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2357;&#2381;&#2351;&#2366;&#2346;&#2366;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2309;&#2346;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2350;&#2366;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> 20% </span><span style=\"font-family: Kohinoor Devanagari;\">&#2309;&#2343;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2309;&#2306;&#2325;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2352;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> 10% </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2331;&#2370;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2342;&#2375;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2357;&#2361;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2326;&#2352;&#2366;&#2348;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2340;&#2369;&#2354;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2346;&#2381;&#2352;&#2351;&#2379;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2352;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2332;&#2367;&#2360;&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2350;&#2366;&#2346;</span><span style=\"font-family: Cambria Math;\"> 1 kg </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2360;&#2381;&#2341;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> 800 </span><span style=\"font-family: Kohinoor Devanagari;\">&#2327;&#2381;&#2352;&#2366;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2361;&#2379;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2313;&#2360;&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2358;&#2369;&#2342;&#2381;&#2343;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2354;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>15%</p>\n", "<p>25%</p>\n", 
                                "<p>20%</p>\n", "<p>35%</p>\n"],
                    options_hi: ["<p>15%</p>\n", "<p>25%</p>\n",
                                "<p>20%</p>\n", "<p>35%</p>\n"],
                    solution_en: "<p>11.(d)&nbsp;</p>\r\n<p><span style=\"font-weight: 400;\">Ratio&rarr;&nbsp;</span><span style=\"font-weight: 400;\">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; C.P&nbsp; &nbsp; :&nbsp; &nbsp; S.P</span></p>\r\n<p><span style=\"font-weight: 400;\">Markup % &rarr;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; </span><span style=\"font-weight: 400;\">5&nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; 6</span></p>\r\n<p><span style=\"font-weight: 400;\">Discount &rarr;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; </span><span style=\"font-weight: 400;\">10&nbsp; &nbsp; :&nbsp; &nbsp; &nbsp;9</span></p>\r\n<p><span style=\"text-decoration: underline;\"><span style=\"font-weight: 400;\">Faulty weight &rarr;&nbsp; &nbsp; &nbsp; </span><span style=\"font-weight: 400;\">800&nbsp; &nbsp;:&nbsp; 1000&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp;</span></span></p>\r\n<p><span style=\"font-weight: 400;\">Final&rarr; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;</span><span style=\"font-weight: 400;\"> 20&nbsp; &nbsp; :&nbsp; &nbsp; 27&nbsp;</span></p>\r\n<p><span style=\"font-weight: 400;\">Profit percentage = </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>27</mn><mo>-</mo><mn>20</mn></mrow><mn>20</mn></mfrac></math> <span style=\"font-weight: 400;\">&times; 100 = 35%</span></p>\n",
                    solution_hi: "<p>11.(d)&nbsp;</p>\r\n<p><span style=\"font-weight: 400;\">&#2309;&#2344;&#2369;&#2346;&#2366;&#2340;&rarr; </span><span style=\"font-weight: 400;\">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;C.P&nbsp; &nbsp; :&nbsp; &nbsp; S.P</span></p>\r\n<p><span style=\"font-weight: 400;\">&#2350;&#2366;&#2352;&#2381;&#2325;&#2309;&#2346;% &rarr; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;</span><span style=\"font-weight: 400;\">5&nbsp; &nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; 6</span></p>\r\n<p><span style=\"font-weight: 400;\">&#2331;&#2370;&#2335; &rarr;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; </span><span style=\"font-weight: 400;\">10&nbsp; &nbsp; :&nbsp; &nbsp; &nbsp;9</span></p>\r\n<p><span style=\"text-decoration: underline;\"><span style=\"font-weight: 400;\">&#2342;&#2379;&#2359;&#2346;&#2370;&#2352;&#2381;&#2339; &#2357;&#2332;&#2344; &rarr; &nbsp; &nbsp;</span><span style=\"font-weight: 400;\">800&nbsp; &nbsp; &nbsp;:&nbsp; 1000&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp;</span></span></p>\r\n<p><span style=\"font-weight: 400;\">&#2309;&#2306;&#2340;&#2367;&#2350;&rarr; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;</span><span style=\"font-weight: 400;\">20&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; 27&nbsp;</span></p>\r\n<p><span style=\"font-weight: 400;\">&#2354;&#2366;&#2349; &#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340; = </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>27</mn><mo>-</mo><mn>20</mn></mrow><mn>20</mn></mfrac></math> <span style=\"font-weight: 400;\">&times; 100 = 35%</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "17",
                    question_en: "<p>12.<span style=\"font-family: Cambria Math;\"> A boy buys a toy car for Rs.650 and sells it for Rs.750. Find the gain percentage.</span></p>\n",
                    question_hi: "<p>12.<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2354;&#2337;&#2364;&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2335;&#2377;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> 650 </span><span style=\"font-family: Kohinoor Devanagari;\">&#2352;&#2369;&#2346;&#2351;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2326;&#2352;&#2368;&#2342;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2313;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> 750 </span><span style=\"font-family: Kohinoor Devanagari;\">&#2352;&#2369;&#2346;&#2351;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2348;&#2375;&#2330;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2354;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2332;&#2381;&#2334;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2368;&#2332;&#2367;&#2319;&#2404;</span></p>\n",
                    options_en: ["<p>16.38%</p>\n", "<p>14.38%</p>\n", 
                                "<p>15.38%</p>\n", "<p>13.33%</p>\n"],
                    options_hi: ["<p>16.38%</p>\n", "<p>14.38%</p>\n",
                                "<p>15.38%</p>\n", "<p>13.33%</p>\n"],
                    solution_en: "<p>12.(c) <span style=\"font-family: Cambria Math;\">gain percentage </span><span style=\"font-family: Cambria Math;\">= </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&nbsp;</mo><mn>750</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>650</mn></mrow><mn>650</mn></mfrac><mo>&times;</mo><mn>100</mn><mo>=</mo><mn>15</mn><mo>.</mo><mn>38</mn><mo>%</mo></math></p>\n",
                    solution_hi: "<p>12.(c) <span style=\"font-weight: 400;\">&#2354;&#2366;&#2349; &#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340; </span><span style=\"font-family: Cambria Math;\">= </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&nbsp;</mo><mn>750</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>650</mn></mrow><mn>650</mn></mfrac><mo>&times;</mo><mn>100</mn><mo>=</mo><mn>15</mn><mo>.</mo><mn>38</mn><mo>%</mo></math></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "17",
                    question_en: "<p>13.<span style=\"font-family: Cambria Math;\"> The marked price of an article is Rs.1000 and shopkeeper gave 40 percent discount. What will be the selling price ?</span></p>\n",
                    question_hi: "<p>13.<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2357;&#2360;&#2381;&#2340;&#2369;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2309;&#2306;&#2325;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> 1000 </span><span style=\"font-family: Kohinoor Devanagari;\">&#2352;&#2369;&#2346;&#2351;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2342;&#2369;&#2325;&#2366;&#2344;&#2342;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> 40 </span><span style=\"font-family: Kohinoor Devanagari;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2331;&#2370;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2342;&#2375;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Kohinoor Devanagari;\">&#2357;&#2367;&#2325;&#2381;&#2352;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2361;&#2379;&#2327;&#2366;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>Rs. 400</p>\n", "<p>Rs. 700</p>\n", 
                                "<p>Rs. 500</p>\n", "<p>Rs. 600</p>\n"],
                    options_hi: ["<p>Rs. 400</p>\n", "<p>Rs. 700</p>\n",
                                "<p>Rs 500</p>\n", "<p>Rs. 600</p>\n"],
                    solution_en: "<p>13.(d) <span style=\"font-weight: 400;\">S.P of the book </span><span style=\"font-family: Cambria Math;\">= 1000 &times;</span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>60</mn><mn>100</mn></mfrac></math> = 600&#8377;</span></p>\n",
                    solution_hi: "<p>13.(d) <span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2367;&#2340;&#2366;&#2348;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2357;&#2367;&#2325;&#2381;&#2352;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> = 1000 &times;</span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>60</mn><mn>100</mn></mfrac></math> = 600&#8377;</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "17",
                    question_en: "<p>14.<span style=\"font-family: Cambria Math;\"> A watch is sold for Rs.15,000 at a loss of 25%. What is the cost price of the watch?</span></p>\n",
                    question_hi: "<p>14.<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2328;&#2337;&#2364;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> 25% </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2361;&#2366;&#2344;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> 15,000 </span><span style=\"font-family: Kohinoor Devanagari;\">&#2352;&#2369;&#2346;&#2351;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2348;&#2375;&#2330;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2332;&#2366;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2328;&#2337;&#2364;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2381;&#2352;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>Rs.16,000</p>\n", "<p>Rs.18,500</p>\n", 
                                "<p>Rs.20,000</p>\n", "<p>Rs.18,000</p>\n"],
                    options_hi: ["<p>Rs.16,000</p>\n", "<p>Rs.18,500</p>\n",
                                "<p>Rs.20,000</p>\n", "<p>Rs.18,000</p>\n"],
                    solution_en: "<p>14.(c) <span style=\"font-weight: 400;\">C.P of the watch &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>4</mn></mfrac></math></span><span style=\"font-weight: 400;\">&nbsp;= 15000 &#8377;</span></p>\r\n<p><span style=\"font-weight: 400;\">&nbsp;&rArr; C.P = 20000 &#8377;</span></p>\n",
                    solution_hi: "<p>14.(c) <span style=\"font-family: Kohinoor Devanagari;\">&#2328;&#2337;&#2364;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2381;&#2352;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> &times;</span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>4</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> = 15000 &#8377;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&nbsp;<span style=\"font-weight: 400;\">&rArr; </span></span><span style=\"font-family: Kohinoor Devanagari;\">&#2328;&#2337;&#2364;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2381;&#2352;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> = 20000 &#8377;</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "misc",
                    question_en: "<p>15.<span style=\"font-family: Cambria Math;\"> A faulty machine weighs 900 g as 1000 g. A shopkeeper uses this weighing machine to sell 45 kg of rice at Rs.65 per kg which he bought at Rs.58 per kg. His overall profit percentage is approximately equal to:</span></p>\n",
                    question_hi: "<p>15.<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2342;&#2379;&#2359;&#2346;&#2370;&#2352;&#2381;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2350;&#2358;&#2368;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2357;&#2332;&#2344;</span><span style=\"font-family: Cambria Math;\"> 1000 </span><span style=\"font-family: Kohinoor Devanagari;\">&#2327;&#2381;&#2352;&#2366;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2352;&#2370;&#2346;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> 900 </span><span style=\"font-family: Kohinoor Devanagari;\">&#2327;&#2381;&#2352;&#2366;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2361;&#2379;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2342;&#2369;&#2325;&#2366;&#2344;&#2342;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2311;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2340;&#2380;&#2354;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2357;&#2366;&#2354;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2350;&#2358;&#2368;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2313;&#2346;&#2351;&#2379;&#2327;</span><span style=\"font-family: Cambria Math;\"> 45 </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2367;&#2354;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2330;&#2366;&#2357;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> 65 </span><span style=\"font-family: Kohinoor Devanagari;\">&#2352;&#2369;&#2346;&#2351;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2346;&#2381;&#2352;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2367;&#2354;&#2379;&#2327;&#2381;&#2352;&#2366;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2342;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2348;&#2375;&#2330;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2352;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2332;&#2367;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2313;&#2360;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> 58 </span><span style=\"font-family: Kohinoor Devanagari;\">&#2352;&#2369;&#2346;&#2351;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2346;&#2381;&#2352;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2367;&#2354;&#2379;&#2327;&#2381;&#2352;&#2366;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2326;&#2352;&#2368;&#2342;&#2366;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2313;&#2360;&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2369;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2354;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2354;&#2327;&#2349;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2348;&#2352;&#2366;&#2348;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">:</span></p>\n",
                    options_en: ["<p>24.5%</p>\n", "<p>22.6%</p>\n", 
                                "<p>18.9%</p>\n", "<p>23.8%</p>\n"],
                    options_hi: ["<p>24.5%</p>\n", "<p>22.6%</p>\n",
                                "<p>18.9%</p>\n", "<p>23.8%</p>\n"],
                    solution_en: "<p>15.(a)&nbsp;</p>\r\n<p><span style=\"font-weight: 400;\">Ratio&rarr; </span><span style=\"font-weight: 400;\">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; C.P &nbsp; : &nbsp; S.P</span></p>\r\n<p><span style=\"font-weight: 400;\">Faulty weight &rarr;&nbsp; &nbsp; &nbsp; &nbsp; </span><span style=\"font-weight: 400;\">9&nbsp; &nbsp; : &nbsp; 10</span></p>\r\n<p><span style=\"text-decoration: underline;\"><span style=\"font-weight: 400;\">Price&rarr;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;</span><span style=\"font-weight: 400;\">58 &nbsp; :&nbsp; &nbsp;65&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp;</span></span></p>\r\n<p><span style=\"font-weight: 400;\">Final &rarr;&nbsp;</span><span style=\"font-weight: 400;\">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;522 &nbsp; :&nbsp; &nbsp; 650</span></p>\r\n<p><span style=\"font-weight: 400;\">Profit percentage =</span><span style=\"font-weight: 400;\">&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>650</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>522</mn></mrow><mrow><mn>522</mn><mo>&nbsp;</mo></mrow></mfrac></math></span></p>\r\n<p><span style=\"font-weight: 400;\">= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>128</mn><mn>522</mn></mfrac></math></span><span style=\"font-weight: 400;\">&nbsp;&times; 100 = 24.5%</span></p>\n",
                    solution_hi: "<p>15.(a)&nbsp;</p>\r\n<p><span style=\"font-weight: 400;\">&#2309;&#2344;&#2369;&#2346;&#2366;&#2340;&rarr; </span><span style=\"font-weight: 400;\">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;C.P &nbsp; : &nbsp; S.P</span></p>\r\n<p><span style=\"font-weight: 400;\">&#2342;&#2379;&#2359;&#2346;&#2370;&#2352;&#2381;&#2339; &#2357;&#2332;&#2344;t &rarr;&nbsp; &nbsp; </span><span style=\"font-weight: 400;\">9&nbsp; &nbsp; &nbsp;: &nbsp; 10</span></p>\r\n<p><span style=\"text-decoration: underline;\"><span style=\"font-weight: 400;\">&#2325;&#2368;&#2350;&#2340; &rarr;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; </span><span style=\"font-weight: 400;\">58 &nbsp; :&nbsp; &nbsp;65&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp;</span></span></p>\r\n<p><span style=\"font-weight: 400;\">&#2309;&#2306;&#2340;&#2367;&#2350; &rarr; </span><span style=\"font-weight: 400;\">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;522 &nbsp; :&nbsp; &nbsp; 650</span></p>\r\n<p><span style=\"font-weight: 400;\">&#2354;&#2366;&#2349; &#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340; =</span><span style=\"font-weight: 400;\">&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>650</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>522</mn></mrow><mrow><mn>522</mn><mo>&nbsp;</mo></mrow></mfrac></math></span></p>\r\n<p><span style=\"font-weight: 400;\">= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>128</mn><mn>522</mn></mfrac></math></span><span style=\"font-weight: 400;\">&nbsp;&times; 100 = 24.5%</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>