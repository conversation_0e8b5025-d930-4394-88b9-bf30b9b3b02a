<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #7209b7;
            --secondary: #4361ee;
            --accent: #f72585;
            --success: #06d6a0;
            --warning: #ffd166;
            --danger: #ef476f;
            --light: #f8f9fa;
            --dark: #212529;
        }
        body {
            background-color: #f0f2f5;
            font-size: 16px;
            padding-bottom: 60px; /* Space for fixed navigation */
        }
        .navbar {
            background: linear-gradient(135deg, var(--primary), var(--secondary)) !important;
            box-shadow: 0 2px 10px rgba(0,0,0,.1);
            padding: 0.5rem 1rem;
        }
        .navbar-brand {
            font-size: 1.1rem;
            margin-right: 0;
        }
        .navbar .container-fluid {
            justify-content: space-between;
        }
        .controls-group {
            display: flex;
            align-items: center;
            flex-wrap: nowrap;
        }
        .timer-display {
            background: linear-gradient(135deg, #4361ee, #3a0ca3);
            color: white;
            border-radius: 50px;
            box-shadow: 0 3px 8px rgba(0,0,0,.1);
            padding: 0.25rem 0.75rem;
            font-size: 0.875rem;
            white-space: nowrap;
        }
        .nav-btn {
            padding: 0.25rem 0.75rem;
            font-size: 0.875rem;
            margin-left: 0.25rem;
        }
        .question-nav {
            position: fixed;
            top: 56px;
            right: 0;
            height: calc(100vh - 56px);
            width: 280px;
            transform: translateX(280px);
            transition: transform .3s;
            z-index: 100;
            background: #fff;
            box-shadow: -5px 0 15px rgba(0,0,0,.1);
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .main-content {
            transition: margin .3s;
            min-height: calc(100vh - 116px);
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.5;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(32px, 1fr));
            gap: 6px;
        }
        .q-box {
            width: 35px;
            height: 35px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 5px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid #dee2e6;
            background: #fff;
            transition: all .2s;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: 0 3px 5px rgba(0,0,0,.1);
        }
        .q-box.attempted {
            background-color: var(--secondary);
            color: white;
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,.15);
        }
        .card {
            border-radius: 12px;
            overflow: hidden;
            border: none;
            box-shadow: 0 4px 12px rgba(0,0,0,.08);
        }
        .option-label {
            border-radius: 8px;
            transition: all .2s;
            border: 1px solid #dee2e6;
            padding: 0.75rem 1rem;
            font-size: 1rem;
        }
        .option-label:hover {
            background-color: #f8f9fa;
            transform: translateY(-2px);
            box-shadow: 0 3px 8px rgba(0,0,0,.05);
        }
        .form-check-input:checked + .option-label {
            border-color: var(--primary);
            background-color: rgba(114,9,183,.05);
        }
        .btn-primary {
            background-color: var(--primary);
            border-color: var(--primary);
        }
        .btn-outline-primary {
            color: var(--primary);
            border-color: var(--primary);
        }
        .btn-outline-primary:hover {
            background-color: var(--primary);
            color: white;
        }
        /* Bottom fixed navigation for mobile */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: white;
            box-shadow: 0 -2px 10px rgba(0,0,0,.1);
            padding: 10px 15px;
            display: flex;
            justify-content: space-between;
            z-index: 90;
        }
        @media (min-width: 992px) {
            .main-content.with-nav {
                margin-right: 280px;
            }
            .question-nav {
                transform: translateX(0);
            }
            .nav-toggle-btn {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(30px, 1fr));
            }
            .option-label {
                padding: 0.625rem 0.875rem;
                font-size: 0.95rem;
            }
            .question-text {
                font-size: 1rem;
            }
            .navbar-brand {
                font-size: 1rem;
            }
            .timer-display {
                font-size: 0.8125rem;
                padding: 0.2rem 0.6rem;
            }
            .nav-btn {
                padding: 0.2rem 0.6rem;
                font-size: 0.8125rem;
            }
            .container {
                padding-left: 10px;
                padding-right: 10px;
            }
        }
    </style>
</head>
<body>
    <!-- Header navbar -->
    <nav class="navbar navbar-dark sticky-top">
        <div class="container-fluid px-2">
            <a class="navbar-brand fw-bold" href="#"><i class="fas fa-graduation-cap me-1"></i></a>
            <div class="controls-group">
                <!-- Timer -->
                <div class="timer-display me-1 fw-bold" id="timer">
                    <i class="far fa-clock me-1"></i><span id="timer-display">8:00</span>
                </div>
                <!-- Submit button -->
                <button class="btn btn-light btn-sm nav-btn me-1" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Language toggle -->
                <button class="btn btn-light btn-sm nav-btn me-1" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="btn btn-light btn-sm nav-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </nav>
    <!-- Main content -->
    <div class="main-content" id="main-content">
        <div class="container py-3">
            <!-- Question counter -->
            <div class="fw-bold text-center mb-3" id="question-counter">Question 1 of 25</div>
            <!-- Questions will be displayed here -->
            <div id="questions-container"></div>
        </div>
    </div>
    <!-- Bottom navigation for mobile -->
    <div class="bottom-nav d-lg-none">
        <button class="btn btn-outline-primary px-3" onclick="prevQuestion()">
            <i class="fas fa-chevron-left me-1"></i> Previous
        </button>
        <button class="btn btn-outline-primary px-3" onclick="nextQuestion()">
            Next <i class="fas fa-chevron-right ms-1"></i>
        </button>
    </div>
    <!-- Question navigation sidebar -->
    <div class="question-nav" id="question-nav">
        <div class="p-3">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h5 class="m-0">Questions</h5>
                <div>
                    <span class="badge bg-secondary me-1" id="attempted-count">0</span>
                    <span class="badge bg-secondary" id="total-count">25</span>
                    <button class="btn btn-sm btn-outline-secondary ms-2 d-lg-none" onclick="toggleNav()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
            <!-- Section selector moved here -->
            <div class="mb-3">
                <label for="section-selector" class="form-label small text-muted">Jump to Section</label>
                <select class="form-select form-select-sm" id="section-selector" onchange="switchSection(this.value)">
                    <option value="all">All Sections</option>
                </select>
            </div>
            <div class="question-grid" id="question-boxes"></div>
        </div>
    </div>
    <!-- Results modal -->
    <div class="modal fade" id="results-modal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content border-0">
                <div class="modal-header border-0" style="background:linear-gradient(135deg,var(--primary),var(--secondary));color:white">
                    <h5 class="modal-title">Test Results</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body p-4">
                    <div class="row text-center g-2">
                        <div class="col-6">
                            <div class="p-3 rounded shadow-sm" style="background:linear-gradient(135deg,#4361ee,#3a0ca3);color:white">
                                <h3 id="score-value" class="mb-0 fw-bold">0</h3>
                                <div class="small">Score</div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="p-3 rounded shadow-sm" style="background:linear-gradient(135deg,#06d6a0,#1b9aaa);color:white">
                                <h3 id="correct-value" class="mb-0 fw-bold">0</h3>
                                <div class="small">Correct</div>
                            </div>
                        </div>
                        <div class="col-6 mt-2">
                            <div class="p-3 rounded shadow-sm" style="background:linear-gradient(135deg,#ef476f,#f72585);color:white">
                                <h3 id="incorrect-value" class="mb-0 fw-bold">0</h3>
                                <div class="small">Incorrect</div>
                            </div>
                        </div>
                        <div class="col-6 mt-2">
                            <div class="p-3 rounded shadow-sm" style="background:linear-gradient(135deg,#6c757d,#495057);color:white">
                                <h3 id="unattempted-value" class="mb-0 fw-bold">0</h3>
                                <div class="small">Unattempted</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer border-0">
                    <button type="button" class="btn btn-primary" onclick="reviewTest()">Review Test</button>
                </div>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 8 * 60;
        let resultsModal;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            resultsModal = new bootstrap.Modal(document.getElementById('results-modal'));
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["18"] = {
                name: "General Awareness",
                start: 0,
                end: 23
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="18">General Awareness</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 24,
                end: 24
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "18",
                    question_en: " <p>1. Who has been  appointed as chief of Asset Reconstruction Co India Ltd (ARCIL)?   ",
                    question_hi: "<p><span style=\"font-family: Baloo;\">1. एसेट रिकंस्ट्रक्शन कंपनी (इंडिया) लिमिटेड (ARCIL) के प्रमुख के रूप में किसे नियुक्त किया गया है?</span></p>",
                    options_en: [" <p> Aswini Kumar</span></p>", " <p> Pallav Mohapatra</span></p>", 
                                " <p> Swaminathan Janakiraman</span></p>", " <p> Siddhartha Mohanty</span></p>"],
                    options_hi: ["<p>अश्विनी कुमार</p>", "<p>पल्लव मोहापात्रा&nbsp;</p>",
                                "<p>स्वामीनाथन जानकीरमन</p>", "<p>सिद्धार्थ मोहंती</p>"],
                    solution_en: "<p><span style=\"font-family: Times New Roman;\">(b) Former Central Bank of India CEO Pallav Mohapatra has been appointed as chief of Asset Reconstruction Co India Ltd (ARCIL) with effect from March 8. </span></p>",
                    solution_hi: "<p><span style=\"font-family: Times New Roman;\">(b)सेंट्रल बैंक ऑफ इंडिया के पूर्व सीईओ पल्लव महापात्रा को 8 मार्च से एसेट रिकंस्ट्रक्शन कंपनी लिमिटेड (एआरसीआईएल) का प्रमुख नियुक्त किया गया है।</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "18",
                    question_en: " <p>2. The symbol Sb stands for stibnum or stibnite. What is the modern name of this element?   ",
                    question_hi: "<p><span style=\"font-family: Baloo;\">2. संकेत Sb स्टिबनम या स्टिबनाइट का अर्थ देता है | इस तत्व का आधुनिक नाम क्या है ? </span></p>",
                    options_en: [" <p> Arsenic  </span></p>", " <p> Antimony  </span></p>", 
                                " <p> Tin  </span></p>", " <p> Samarium </span></p>"],
                    options_hi: ["<p>आर्सेनिक</p>", "<p>एंटीमनी</p>",
                                "<p>टिन</p>", "<p>समैरियम</p>"],
                    solution_en: " <p><span style=\"font-family:Times New Roman\">(b) Stibnum is the old name for antimony.</span></p>",
                    solution_hi: "<p><span style=\"font-family: Times New Roman;\">(b) स्टिबनम सुरमा का पुराना नाम है।</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "18",
                    question_en: " <p>3. Which one of the following Articles of the Constitution prohibits human trafficking and forced labor?   ",
                    question_hi: "<p><span style=\"font-family: Baloo;\">3. संविधान का निम्न में से कौन सा अनुच्छेद मानव तस्करी एवं जबरन श्रम को प्रतिबंधित करता है ? </span></p>",
                    options_en: [" <p> Article 21 </span></p>", " <p> Article 22 </span></p>", 
                                " <p> Article 23 </span></p>", " <p> Article 24</span></p>"],
                    options_hi: ["<p>अनुच्छेद 21</p>", "<p>अनुच्छेद 22</p>",
                                "<p>अनुच्छेद 23</p>", "<p>अनुच्छेद 24</p>"],
                    solution_en: " <p><span style=\"font-family:Times New Roman\">(c) Article 23 & 24 of Indian Constitution deal with the Right against Exploitation. Article 23 prohibits the traffic in human beings and forced labor such as beggars.</span></p>",
                    solution_hi: "<p><span style=\"font-family: Times New Roman;\">(c) भारतीय संविधान का अनुच्छेद २३ और &nbsp;24 शोषण के खिलाफ अधिकार से संबंधित है। अनुच्छेद 23 मानव के अवैध व्यापार और भिखारियों जैसे बलात् श्रम को प्रतिबंधित करता है।</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "18",
                    question_en: " <p>4. Which government body has launched the first-of-its-kind Online Dispute Resolution (ODR) handbook in India?   ",
                    question_hi: "<p><span style=\"font-family: Baloo;\">4. किस सरकारी संस्था ने भारत में अपनी तरह का पहला ऑनलाइन विवाद समाधान (ODR) हैंडबुक लॉन्च किया है?</span></p>",
                    options_en: [" <p> National Company Law Tribunal </span></p>", " <p> Supreme Court of India  </span></p>", 
                                " <p> NITI Aayog  </span></p>", " <p>Insolvency and Bankruptcy Board of India  </span></p>"],
                    options_hi: ["<p>नेशनल कंपनी लॉ ट्रिब्यूनल</p>", "<p>सुप्रीम कोर्ट ऑफ़ इंडिया</p>",
                                "<p>नीति आयोग</p>", "<p>इन्सॉल्वेंसी एंड बैंकरप्सी बोर्ड ऑफ इंडिया</p>"],
                    solution_en: " <p><span style=\"font-family:Times New Roman\">(c) NITI Aayog has launched the first-of-its-kind Online Dispute Resolution (ODR) handbook in India. ODR is the resolution of disputes outside courts, particularly of small and medium-value cases, using digital technology and techniques of alternate dispute resolution such as negotiation, mediation, and arbitration.</span></p>",
                    solution_hi: "<p><span style=\"font-family: Times New Roman;\">(c) नीति आयोग ने भारत में अपनी तरह की पहली ऑनलाइन विवाद समाधान (ओडीआर) पुस्तिका लॉन्च की है। ओडीआर अदालतों के बाहर विवादों का समाधान है, विशेष रूप से छोटे और मध्यम मूल्य के मामलों में, डिजिटल तकनीक और वैकल्पिक विवाद समाधान की तकनीकों जैसे बातचीत, मध्यस्थता और मध्यस्थता का उपयोग करना।</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "18",
                    question_en: " <p>5.Who has won the Formula One Spanish Grand Prix?  ",
                    question_hi: "<p><span style=\"font-family: Baloo;\">5. फॉर्मूला वन स्पेनिश ग्रां प्री किसने जीती है?</span></p>",
                    options_en: [" <p> Valtteri Bottas  </span></p>", " <p> Max Verstappen  </span></p>", 
                                " <p> Lewis Hamilton </span></p>", " <p> Sebastian Vettel </span></p>"],
                    options_hi: ["<p>वाल्टेरी बोटास</p>", "<p>मैक्स वेरस्टैपेन</p>",
                                "<p>लुईस हैमिल्टन</p>", "<p>सेबस्टियन वेटेलन</p>"],
                    solution_en: " <p><span style=\"font-family:Times New Roman\">(c) Mercedes driver Lewis Hamilton has won Formula One Spanish Grand Prix at Circuit de Barcelona-Catalunya.</span></p>",
                    solution_hi: "<p><span style=\"font-family: Times New Roman;\">(c) मर्सिडीज के ड्राइवर लुईस हैमिल्टन ने सर्किट डे बार्सिलोना-कैटालुन्या में फॉर्मूला वन स्पैनिश ग्रां प्री जीत लिया है।</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "18",
                    question_en: " <p>6. Famous artist Astad Deboo who passed away recently was  a _________.  ",
                    question_hi: "<p><span style=\"font-family: Baloo;\">6. प्रसिद्ध कलाकार अस्ताद देबू का हाल ही में निधन हो गया|वे क्या थे?</span></p>",
                    options_en: [" <p> Dancer  </span></p>", " <p> Singer </span></p>", 
                                " <p> Painter </span></p>", " <p> Tabla Player </span></p>"],
                    options_hi: ["<p>नृतक</p>", "<p>गायक</p>",
                                "<p>पेंटर</p>", "<p>तबला वादक</p>"],
                    solution_en: " <p>(a)</span><span style=\"font-family:Times New Roman\"> Contemporary Indian dancer Astad Deboo, known for amalgamating Indian and western dance techniques passed away.</span></p>",
                    solution_hi: "<p>(a)भारतीय और पश्चिमी नृत्य तकनीकों को मिलाने के लिए जाने जाने वाले समकालीन भारतीय नर्तक अष्टद देबू का निधन हो गया।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "18",
                    question_en: " <p>7. Who has taken up the charge as the new Managing Director and CEO of the India Post Payments Bank? ",
                    question_hi: "<p><span style=\"font-family: Baloo;\">7. इंडिया पोस्ट पेमेंट्स बैंक के नए प्रबंध निदेशक और सीईओ के रूप में कार्यभार किसने संभाला है?</span></p>",
                    options_en: [" <p> Partha Pratim Sengupta</span></p>", " <p> J. </span><span style=\"font-family:Times New Roman\">Venkatramu</span><span style=\"font-family:Times New Roman\"> </span></p>", 
                                " <p> Shri Sanjiv Chadha </span></p>", " <p> Pradeep Kumar Bisoi</span></p>"],
                    options_hi: ["<p>पार्थ प्रतिम सेनगुप्ता</p>", "<p>जे वेंकटरमू</p>",
                                "<p>श्री संजीव चड्ढा</p>", "<p>प्रदीप कुमार बिसोई</p>"],
                    solution_en: " <p><span style=\"font-family:Times New Roman\">(b) J. </span><span style=\"font-family:Times New Roman\">Venkatramu</span><span style=\"font-family:Times New Roman\"> has taken up charge as the new Managing Director and CEO of the India Post Payments Bank (IPPB). IPPB HQs- New Delhi</span></p>",
                    solution_hi: "<p><span style=\"font-family: Times New Roman;\">(b) जे. वेंकटरामु ने इंडिया पोस्ट पेमेंट्स बैंक (आईपीपीबी) के नए प्रबंध निदेशक और सीईओ के रूप में कार्यभार संभाला है। आईपीपीबी मुख्यालय- नई दिल्ली</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "18",
                    question_en: "<p>8. ISRO has developed Radar for Joint Earth Observation Satellite Mission with which agency?</p>",
                    question_hi: "<p><span style=\"font-family: Baloo;\">8. ISRO ने किस एजेंसी के साथ संयुक्त पृथ्वी अवलोकन उपग्रह मिशन के लिए रडार विकसित किया है?</span></p>",
                    options_en: ["<p>JAXA</p>", "<p>NASA</p>", 
                                "<p>ROSCOSMOS</p>", "<p>ESA</p>"],
                    options_hi: ["<p>JAXA</p>", "<p>NASA</p>",
                                "<p>ROSCOSMOS<span style=\"font-family: Times New Roman;\"> </span></p>", "<p>ESA</p>"],
                    solution_en: "<p><span style=\"font-family: Times New Roman;\">(b) Indian Space Research Organisation (ISRO) in collaboration with the US space agency, NASA has completed the development of Synthetic Aperture Radar (SAR) which has the capability of producing extremely high-resolution images for joint earth observation satellite missions.</span></p>",
                    solution_hi: "<p><span style=\"font-family: Times New Roman;\">(b) भारतीय अंतरिक्ष अनुसंधान संगठन (इसरो) ने अमेरिकी अंतरिक्ष एजेंसी, नासा के सहयोग से सिंथेटिक एपर्चर रडार (एसएआर) का विकास पूरा कर लिया है, जिसमें संयुक्त पृथ्वी अवलोकन उपग्रह मिशन के लिए अत्यंत उच्च-रिज़ॉल्यूशन छवियों का उत्पादन करने की क्षमता है।</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "18",
                    question_en: " <p>9. The Uprising of 1857 was described as the first Indian war of Independence by-   ",
                    question_hi: "<p><span style=\"font-family: Baloo;\">9. 1857 के विद्रोह का वर्णन किसके द्वारा भारतीय स्वतंत्रता की पहली लड़ाई के रूप में किया गया था ? </span></p>",
                    options_en: [" <p> S.N. Sen        </span></p>", " <p> R.C. Mazumdar   </span></p>", 
                                " <p> B.G. Tilak  </span></p>", " <p> V.D. Savarkar  </span></p>"],
                    options_hi: ["<p>एस.एन सेन</p>", "<p>आर.सी तेंदुलकर</p>",
                                "<p>बी. जी तिलक</p>", "<p>वीडी सावरकर</p>"],
                    solution_en: " <p><span style=\"font-family:Times New Roman\">(d) The Indian War of Independence is an Indian nationalist history of the 1857 revolt by Vinayak Damodar Savarkar that was first published in 1909.</span></p>",
                    solution_hi: "<p><span style=\"font-family: Times New Roman;\">(d)भारतीय स्वतंत्रता संग्राम विनायक दामोदर सावरकर द्वारा 1857 के विद्रोह का एक भारतीय राष्ट्रवादी इतिहास है जो पहली बार 1909 में प्रकाशित हुआ था।</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "18",
                    question_en: " <p>10. In which year, the Maratha Empire ceased to exist with the surrender of the Marathas to the British, ending the Third Anglo-Maratha War.   ",
                    question_hi: "<p><span style=\"font-family: Baloo;\">10. किस वर्ष तीसरे आंग्ल-मराठा युद्ध में मराठों के द्वारा अंग्रेजों के सामने समर्पण करने के साथ ही मराठा साम्राज्य का अंत हो गया था ? </span></p>",
                    options_en: [" <p> 1792</span></p>", " <p> 1818</span></p>", 
                                " <p> 1811</span></p>", " <p> 1809</span></p>"],
                    options_hi: ["<p>1792</p>", "<p>1818</p>",
                                "<p>1811<span style=\"font-family: Times New Roman;\"> </span></p>", "<p>1809</p>"],
                    solution_en: " <p><span style=\"font-family:Times New Roman\">(b) In the Third Anglo-Maratha War, the last Peshwa, Baji Rao II, was defeated by the British in 1818 and the empire ceased to exist.</span></p>",
                    solution_hi: "<p><span style=\"font-family: Times New Roman;\">(b) तीसरे आंग्ल-मराठा युद्ध में, अंतिम पेशवा, बाजी राव द्वितीय, 1818 में अंग्रेजों से हार गए और साम्राज्य का अस्तित्व समाप्त हो गया।</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "18",
                    question_en: "<p>11. <span style=\"font-family: \'times new roman\', times, serif;\">Which is the first Tibetan </span><span style=\"font-family: Times New Roman;\"><span style=\"font-family: \'times new roman\', times, serif;\">Vil</span>lage, in</span><span style=\"font-family: Times New Roman;\"> Ladakh that has been electrified by the Global Himalayan Expedition? </span></p>",
                    question_hi: "<p><span style=\"font-family: Baloo;\">11. लद्दाख में पहला तिब्बती ग्राम कौन सा है, जिसे ग्लोबल हिमालयन एक्सपेडिशन द्वारा विद्युतीकृत किया गया है?</span></p>",
                    options_en: ["<p>Dungti</p>", "<p>Demchok</p>", 
                                "<p>Charding</p>", "<p>Subansiri</p>"],
                    options_hi: ["<p>डुंगती</p>", "<p>डेमचोक</p>",
                                "<p>चारडिंग</p>", "<p>सुबनसिरी</p>"],
                    solution_en: "<p>(a)<span style=\"font-family: Times New Roman;\"> The Global Himalayan Expedition has electrified the first Tibetan Village, &lsquo;</span><span style=\"font-family: Times New Roman;\">Dungti</span><span style=\"font-family: Times New Roman;\">&rsquo; near Line of Actual Control in Ladakh. The team of Engineers has electrified the 51 households of this village by providing Solar based DC electricity.</span></p>",
                    solution_hi: "<p>(a)ग्लोबल हिमालयन अभियान ने लद्दाख में वास्तविक नियंत्रण रेखा के पास पहले तिब्बती गांव \'दुंगती\' का विद्युतीकरण किया है। इंजीनियरों की टीम ने इस गांव के 51 घरों में सोलर आधारित डीसी बिजली उपलब्ध कराकर बिजली पहुंचाई है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "18",
                    question_en: " <p>12. Which of the following alloys is used to make magnets ?   ",
                    question_hi: "<p><span style=\"font-family: Baloo;\">12. निम्न में से किस मिश्रधातु का प्रयोग चुंबक बनाने में किया जाता है ? </span></p>",
                    options_en: [" <p> Duralumin</span></p>", " <p> Stainless steel</span></p>", 
                                " <p> Alnico </span></p>", " <p> Magnalium  </span></p>"],
                    options_hi: ["<p>ड्यूरालुमिन</p>", "<p>स्टेनलेस स्टील</p>",
                                "<p>अल्निको</p>", "<p>मैग्नेलियम</p>"],
                    solution_en: " <p><span style=\"font-family:Times New Roman\">(c) Alnico is a family of iron alloys which in addition to iron are composed primarily of aluminium (Al), nickel (Ni) and cobalt (Co), hence acronym al-ni-co. They also include copper, and sometimes titanium. Alnico alloys are ferromagnetic, and are used to make permanent magnets.</span></p>",
                    solution_hi: "<p><span style=\"font-family: Times New Roman;\">(c) Alnico लौह मिश्र धातुओं का एक परिवार है जो लोहे के अलावा मुख्य रूप से एल्यूमीनियम (Al), निकल (Ni) और कोबाल्ट (Co) से बना है, इसलिए इसका संक्षिप्त नाम al-ni-co है। इनमें तांबा और कभी-कभी टाइटेनियम भी शामिल है। Alnico मिश्र धातु लौहचुम्बकीय हैं, और इनका उपयोग स्थायी चुम्बक बनाने के लिए किया जाता है।</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "18",
                    question_en: " <p>13. Who was the first Sultan of Delhi to issue regular currency and to declare Delhi as the capital of his empire?   ",
                    question_hi: "<p><span style=\"font-family: Baloo;\">13. नियमित मुद्रा जारी करने वाला तथा दिल्ली को अपने साम्राज्य की राजधानी घोषित करने वाला दिल्ली का पहला सुलतान कौन था ? </span></p>",
                    options_en: [" <p> Balban    </span><span style=\"font-family:Times New Roman\">                </span></p>", " <p> Aram Shah  </span></p>", 
                                " <p> Nasiruddin Mahmud </span></p>", " <p> Iltutmish </span></p>"],
                    options_hi: ["<p>बलबन</p>", "<p>आराम शाह</p>",
                                "<p>नसीरुद्दीन महमूद</p>", "<p>इल्तुमिश</p>"],
                    solution_en: " <p><span style=\"font-family:Times New Roman\">(d) Iltutmish was the first Sultan of Delhi to issue regular currency and declare Delhi as the capital of his empire. He was the third ruler of the Delhi Sultanate (1211 – 1236), belonging to the Mamluk dynasty.</span></p>",
                    solution_hi: "<p><span style=\"font-family: Times New Roman;\">(d)इल्तुतमिश दिल्ली का पहला सुल्तान था जिसने नियमित मुद्रा जारी की और दिल्ली को अपने साम्राज्य की राजधानी घोषित किया। वह मामलुक वंश से संबंधित दिल्ली सल्तनत (1211-1236) के तीसरे शासक थे।</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "18",
                    question_en: " <p>14. Which high court has the largest jurisdiction in the terms of states?   ",
                    question_hi: "<p><span style=\"font-family: Baloo;\">14. राज्यों की दृष्टि से किस उच्च न्यायालय का कार्य क्षेत्र सबसे बड़ा है ? </span></p>",
                    options_en: [" <p> Gauhati high court </span></p>", " <p> Delhi high court  </span></p>", 
                                " <p> Madras high court</span></p>", " <p> Patna high court  </span></p>"],
                    options_hi: ["<p>गुवाहाटी उच्च न्यायालय</p>", "<p>दिल्ली उच्च न्यायालय</p>",
                                "<p>मद्रास उच्च न्यायालय</p>", "<p>पटना उच्च न्यायालय</p>"],
                    solution_en: " <p>(a)</span><span style=\"font-family:Times New Roman\">  Gauhati High Court was originally known as the High Court of Assam and Nagaland, but renamed as Gauhati High Court in 1971 by the North East Areas (Reorganisation) Act, 1971. It has the largest jurisdiction in terms of states, with its area covering the states of Assam, Arunachal Pradesh, Nagaland, and Mizoram.</span></p>",
                    solution_hi: "<p>(a)<span style=\"font-family: Times New Roman;\"> गुवाहाटी उच्च न्यायालय को मूल रूप से असम और नागालैंड के उच्च न्यायालय के रूप में जाना जाता था, लेकिन 1971 में उत्तर पूर्व क्षेत्र (पुनर्गठन) अधिनियम, 1971 द्वारा इसका नाम बदलकर गौहाटी उच्च न्यायालय कर दिया गया। राज्यों के संदर्भ में इसका सबसे बड़ा क्षेत्राधिकार है, इसके क्षेत्र में इसका क्षेत्र शामिल है। असम, अरुणाचल प्रदेश, नागालैंड और मिजोरम राज्य।</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "18",
                    question_en: " <p>15. Mesopotamian civilization thrived in the area between which two rivers?  ",
                    question_hi: "<p><span style=\"font-family: Baloo;\">15. मेसोपोटामिया सभ्यता किन दो नदियों के बीच के क्षेत्र में फली-फूली थी ? </span></p>",
                    options_en: [" <p> Euphrates and Nile </span></p>", " <p> Tigris and Euphrates  </span></p>", 
                                " <p> Nile and Tigris</span></p>", " <p> Euphrates and Diyala</span></p>"],
                    options_hi: ["<p>युफ्रेट्स और नील</p>", "<p>टिगरिस और युफ्रेट्स</p>",
                                "<p>नील और टिगरिस</p>", "<p>युफ्रेट्स और डियाला</p>"],
                    solution_en: " <p><span style=\"font-family:Times New Roman\">(b) Mesopotamia is an ancient, historical region that lies between the Tigris and Euphrates rivers.</span></p>",
                    solution_hi: "<p><span style=\"font-family: Times New Roman;\">(b) मेसोपोटामिया एक प्राचीन, ऐतिहासिक क्षेत्र है जो टाइग्रिस और यूफ्रेट्स नदियों के बीच स्थित है।</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "18",
                    question_en: "<p>16. The Dampier-Hodges line is related to which of the following?</p>",
                    question_hi: "<p><span style=\"font-family: Baloo;\">16. डैम्पियर-</span><span style=\"font-family: Baloo;\">हॉजेस</span><span style=\"font-family: Baloo;\"> लाइन का संबंध निम्न में से किस से है ? </span></p>",
                    options_en: ["<p>Bay of Cambay</p>", "<p>Palk Strait</p>", 
                                "<p>Andaman and Nicobar Islands</p>", "<p>Sundarbans</p>"],
                    options_hi: ["<p>काम्बे की खाड़ी</p>", "<p>पाक जलडमरूमध्य</p>",
                                "<p>अंडमान और निकोबार द्वीप</p>", "<p>सुंदरवन</p>"],
                    solution_en: "<p><span style=\"font-family: Times New Roman;\">(d) Dampier-Hodges line is an imaginary line drawn in 1829-1830 to mark the northern boundary of Sundarbans delta.</span></p>",
                    solution_hi: "<p><span style=\"font-family: Times New Roman;\">(d) डैम्पियर-होजेस रेखा सुंदरबन डेल्टा की उत्तरी सीमा को चिह्नित करने के लिए 1829-1830 में खींची गई एक काल्पनिक रेखा है।</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "18",
                    question_en: "<p>17. Which is the only state in India producing muga silk ?</p>",
                    question_hi: "<p><span style=\"font-family: Baloo;\">17. मुगा रेशम का उत्पादन करने वाला भारत का एकमात्र राज्य कौन सा है ? </span></p>",
                    options_en: ["<p>Assam</p>", "<p>Bihar</p>", 
                                "<p>Odisha</p>", "<p>West Bengal</p>"],
                    options_hi: ["<p>असम</p>", "<p>बिहार</p>",
                                "<p>ओडिशा</p>", "<p>पश्चिम बंगाल</p>"],
                    solution_en: "<p>(a)<span style=\"font-family: Times New Roman;\"> Muga silk is a variety of wild silk geographically tagged to the state of Assam in India.</span></p>",
                    solution_hi: "<p>(a)<span style=\"font-family: Times New Roman;\"> मुगा रेशम भारत में असम राज्य के लिए भौगोलिक रूप से टैग की गई जंगली रेशम की एक किस्म है।</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "18",
                    question_en: " <p>18. When was Indian Air Force founded?",
                    question_hi: "<p><span style=\"font-family: Baloo;\">18. भारतीय वायुसेना की स्थापना कब की गयी थी ? </span></p>",
                    options_en: [" <p> 1932</span><span style=\"font-family:Times New Roman\">          </span></p>", " <p> 1933              </span></p>", 
                                " <p> 1934</span><span style=\"font-family:Times New Roman\">     </span></p>", " <p> 1935</span></p>"],
                    options_hi: ["<p>1932</p>", "<p>1933</p>",
                                "<p>1934</p>", "<p>1935</p>"],
                    solution_en: " <p>(a)</span><span style=\"font-family:Times New Roman\"> The Indian Air Force was officially established on 8 October 1932. Its first ac flight came into being on 01 Apr 1933.</span></p>",
                    solution_hi: "<p>(a)<span style=\"font-family: Times New Roman;\"> भारतीय वायु सेना की आधिकारिक रूप से स्थापना 8 अक्टूबर 1932 को हुई थी। इसकी पहली एसी उड़ान 01 अप्रैल 1933 को अस्तित्व में आई थी।</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "18",
                    question_en: "<p>19.Recently a rare species of bird has been added to India\'s bird list. In which state was it discovered?</p>",
                    question_hi: "<p><span style=\"font-family: Baloo;\">19. हाल ही में पक्षी की एक दुर्लभ प्रजाति को भारत की पक्षी सूची में जोड़ा गया है। यह किस राज्य में खोजी गयी थी?</span></p>",
                    options_en: ["<p>Assam</p>", "<p>Arunachal Pradesh</p>", 
                                "<p>Sikkim</p>", "<p>Himachal Pradesh</p>"],
                    options_hi: ["<p>असम</p>", "<p>अरुणाचल प्रदेश</p>",
                                "<p>सिक्किम</p>", "<p>हिमाचल प्रदेश</p>"],
                    solution_en: "<p><span style=\"font-family: Times New Roman;\">(b) A team of scientists from Bombay Natural History Society (BNHS), Mumbai, while exploring the high-altitude coniferous forests of Arunachal Pradesh, has recorded a rare species of bird which has been identified as the Three-banded Rosefinch after detailed observations.</span></p>",
                    solution_hi: "<p><span style=\"font-family: Times New Roman;\">(b)बॉम्बे नेचुरल हिस्ट्री सोसाइटी (बीएनएचएस), मुंबई के वैज्ञानिकों की एक टीम ने अरुणाचल प्रदेश के उच्च ऊंचाई वाले शंकुधारी जंगलों की खोज करते हुए पक्षी की एक दुर्लभ प्रजाति दर्ज की है जिसे विस्तृत अवलोकन के बाद थ्री-बैंडेड रोज़फिंच के रूप में पहचाना गया है।</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "18",
                    question_en: "<p>20. Total interacting animals and plants in any well defined area is known as</p>",
                    question_hi: "<p><span style=\"font-family: Baloo;\">20. किसी भी सुपरिभाषित क्षेत्र में अन्योन्याश्रित कुल पशुओं एवं पौधों को ____ नाम से जाना जाता है | </span></p>",
                    options_en: ["<p>population</p>", "<p>biome</p>", 
                                "<p>community</p>", "<p>species</p>"],
                    options_hi: ["<p>आबादी</p>", "<p>बायोम</p>",
                                "<p>समुदाय</p>", "<p>प्रजाति</p>"],
                    solution_en: "<p><span style=\"font-family: Times New Roman;\">(b) Biome, the largest geographic biotic unit, a major community of plants and animals with similar life forms and environmental conditions.</span></p>",
                    solution_hi: "<p><span style=\"font-family: Times New Roman;\">(b)बायोम, सबसे बड़ी भौगोलिक जैविक इकाई, समान जीवन रूपों और पर्यावरणीय परिस्थितियों वाले पौधों और जानवरों का एक प्रमुख समुदाय।</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "18",
                    question_en: "<p>21. Name the branch of zoology that deals with the scientific study of animal behaviour.</p>",
                    question_hi: "<p><span style=\"font-family: Baloo;\">21. प्राणी विज्ञान की उस शाखा का नाम बताएं जिसका संबंध जीवों के व्यवहार के अध्ययन से है |</span></p>",
                    options_en: ["<p>Ecology</p>", "<p>Physiology</p>", 
                                "<p>Ethology</p>", "<p>Anatomy</p>"],
                    options_hi: ["<p>इकोलॉजी</p>", "<p>फिजियोलॉजी</p>",
                                "<p>इथोलॉजी</p>", "<p>एनाटोमी</p>"],
                    solution_en: "<p><span style=\"font-family: Times New Roman;\">(c) Ethology is the scientific and objective study of animal behaviour, usually with a focus on behaviour under natural conditions, and viewing behaviour as an evolutionarily adaptive trait.</span></p>",
                    solution_hi: "<p><span style=\"font-family: Times New Roman;\">(c) एथोलॉजी पशु व्यवहार का वैज्ञानिक और उद्देश्य अध्ययन है, आमतौर पर प्राकृतिक परिस्थितियों में व्यवहार पर ध्यान देने के साथ, और व्यवहार को एक क्रमिक रूप से अनुकूली विशेषता के रूप में देखना।</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "18",
                    question_en: "<p>22. Which was the first private sector bank in India?</p>",
                    question_hi: "<p><span style=\"font-family: Baloo;\">22. भारत में निजी क्षेत्र का पहला बैंक कौन सा था ? </span></p>",
                    options_en: ["<p>Catholic Syrian Bank</p>", "<p>City Union Bank</p>", 
                                "<p>Nedungadi Bank</p>", "<p>Nainital Bank</p>"],
                    options_hi: ["<p>कैथोलिक सीरियन बैंक</p>", "<p>सिटी यूनियन बैंक</p>",
                                "<p>नेदुंगड़ी बैंक</p>", "<p>नैनीताल बैंक</p>"],
                    solution_en: "<p><span style=\"font-family: Times New Roman;\">(c) Nedungadi Bank was established in 1899 in Kerala. It was taken over by Punjab National Bank in 2003.</span></p>",
                    solution_hi: "<p><span style=\"font-family: Times New Roman;\">(c) नेदुंगडी बैंक की स्थापना 1899 में केरल में हुई थी। इसे 2003 में पंजाब नेशनल बैंक ने अधिग्रहण कर लिया था।</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "23",
                    section: "18",
                    question_en: "<p>23. The Kuril Island is being disputed <span style=\"font-family: Times New Roman;\">between which two</span><span style=\"font-family: Times New Roman;\"> countries?</span></p>",
                    question_hi: "<p><span style=\"font-family: Times New Roman;\">23. </span><span style=\"font-family: Baloo;\">कुरिल</span><span style=\"font-family: Baloo;\"> द्वीप को लेकर किन दो देशों के बीच विवाद है ? </span></p>",
                    options_en: ["<p>Japan and South Korea</p>", "<p>Japan and Russia</p>", 
                                "<p>Russia and Ukraine</p>", "<p>Russia and USA</p>"],
                    options_hi: ["<p>जापान और दक्षिण कोरिया</p>", "<p>जापान और रूस</p>",
                                "<p>रूस और यूक्रेन</p>", "<p>रूस और अमेरिका</p>"],
                    solution_en: "<p><span style=\"font-family: Times New Roman;\">(b) The Kuril Islands dispute, also known in Japan as the Northern Territories dispute, is a disagreement between Japan and Russia and also some individuals of the Ainu people over sovereignty of the four southernmost Kuril Islands.</span></p>",
                    solution_hi: "<p><span style=\"font-family: Times New Roman;\">(b)कुरील द्वीप विवाद, जिसे जापान में उत्तरी क्षेत्र विवाद के रूप में भी जाना जाता है, जापान और रूस के बीच और चार दक्षिणी कुरील द्वीपों की संप्रभुता पर ऐनू लोगों के कुछ व्यक्तियों के बीच एक असहमति है।</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "24",
                    section: "18",
                    question_en: "<p>24. The author of &lsquo;On the Origin of Species&rsquo; is _____.</p>",
                    question_hi: "<p><span style=\"font-family: Baloo;\">24. &lsquo;ऑन द ओरिजिन ऑफ़ स्पीशीज&rsquo; के लेखक _____ हैं | </span></p>",
                    options_en: ["<p><span style=\"font-family: Times New Roman;\">Charles Lyell</span></p>", "<p>Charles Darwin</p>", 
                                "<p>Charles Baudelaire</p>", "<p>Charles Dickens</p>"],
                    options_hi: ["<p><span style=\"font-family: Baloo;\">चार्ल्स ल्येल</span></p>", "<p>चार्ल्स डार्विन</p>",
                                "<p>चार्ल्स बॉडलेयर</p>", "<p>चार्ल्स डिकेंस</p>"],
                    solution_en: "<p><span style=\"font-family: Times New Roman;\">(b) On the Origin of Species published on 24 November 1859, is a work of scientific literature by Charles Darwin which is considered to be the foundation of evolutionary biology.</span></p>",
                    solution_hi: "<p><span style=\"font-family: Times New Roman;\">(b)24 नवंबर 1859 को प्रकाशित ऑन द ओरिजिन ऑफ स्पीशीज़, चार्ल्स डार्विन द्वारा वैज्ञानिक साहित्य का एक काम है जिसे विकासवादी जीव विज्ञान की नींव माना जाता है।</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "25",
                    section: "misc",
                    question_en: "<p>25. In which session of Indian National Congress the tricolour flag was unfurled for the first time?</p>",
                    question_hi: "<p><span style=\"font-family: Baloo;\">25. भारतीय राष्ट्रीय कांग्रेस के किस सत्र में तिरंगा झंडा पहली बार फहराया गया था? </span></p>",
                    options_en: ["<p>Lahore session, 1929</p>", "<p>Lahore session, 1909</p>", 
                                "<p>Kanpur session, 1925</p>", "<p><span style=\"font-family: Times New Roman;\">Belgaon</span><span style=\"font-family: Times New Roman;\"> session, 1924</span></p>"],
                    options_hi: ["<p>लाहौर सत्र, 1929</p>", "<p>लाहौर सत्र, 1909</p>",
                                "<p>कानपुर सत्र, 1925</p>", "<p>बेलगाँव सत्र, 1924</p>"],
                    solution_en: "<p>(a)<span style=\"font-family: Times New Roman;\"> The flag of India was hoisted by Jawaharlal Nehru on 31 December 1929 on the banks of Ravi river, in Lahore, modern-day Pakistan. The Congress asked the people of India to observe 26th of January as Independence Day.</span></p>",
                    solution_hi: "<p>(a)<span style=\"font-family: Times New Roman;\"> भारत का झंडा जवाहरलाल नेहरू द्वारा 31 दिसंबर 1929 को रावी नदी के तट पर लाहौर, आधुनिक पाकिस्तान में फहराया गया था। कांग्रेस ने भारत के लोगों से 26 जनवरी को स्वतंत्रता दिवस के रूप में मनाने के लिए कहा।</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML - more compact
            let html = `
                <div class="card mb-3">
                    <div class="card-body">
                        <div class="question-text mb-3">
                            <div class="en">${question.question_en}</div>
                            <div class="hi" style="display:none">${question.question_hi}</div>
                        </div>
                        <div class="options">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = 'option-label';
                // When reviewing, highlight user's choice and correct answer
                if (submitted) {
                    if (opt === question.correct) {
                        optionClass += ' border-success bg-success bg-opacity-10';
                    }
                    if (answers[question.id] === opt && opt !== question.correct) {
                        optionClass += ' border-danger bg-danger bg-opacity-10';
                    }
                }
                html += `
                    <div class="form-check mb-2">
                        <input class="form-check-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${answers[question.id] === opt ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                        <label class="form-check-label w-100 ${optionClass}" for="opt-${index}-${opt}">
                            ${submitted && opt === question.correct ? '<i class="fas fa-check-circle text-success me-1"></i>' : ''}
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const alertClass = isCorrect ? 'alert-success' : 'alert-danger';
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="alert ${alertClass} mt-3 py-2">
                        <strong>${resultText}</strong>
                        <div class="mt-1 small">
                            <strong>Solution:</strong>
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                document.getElementById(`box-${index}`).classList.add('attempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { if (currentQuestion > 0) showQuestion(currentQuestion - 1); }
        function nextQuestion() { if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
            // Update button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const content = document.getElementById('main-content');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                if (window.innerWidth >= 992) {
                    content.classList.add('with-nav');
                }
            } else {
                nav.classList.remove('show');
                if (window.innerWidth >= 992) {
                    content.classList.remove('with-nav');
                }
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer - compact
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            if (confirm(`You have attempted ${attempted} of ${questions.length} questions.
${remaining} questions are unattempted.
Are you sure you want to submit?`)) {
                submitTest();
            }
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('bg-secondary', 'text-white');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            resultsModal.show();
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.classList.replace('btn-light', 'btn-success');
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            resultsModal.hide();
            showQuestion(0);
        }
        // Add event listener for window resize
        window.addEventListener('resize', function() {
            if (window.innerWidth >= 992) {
                if (document.getElementById('question-nav').classList.contains('show')) {
                    document.getElementById('main-content').classList.add('with-nav');
                }
            } else {
                document.getElementById('main-content').classList.remove('with-nav');
            }
        });
    </script>
</body>
</html>