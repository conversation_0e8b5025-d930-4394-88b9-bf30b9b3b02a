<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">15:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 20</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">20</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 15 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["31"] = {
                name: "Computer Knowledge",
                start: 0,
                end: 18
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="31">Computer Knowledge</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 19,
                end: 19
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "31",
                    question_en: " <p>1. </span><span style=\"font-family:Cambria Math\">Instant Messaging (IM) services come under which of the following categories of services?</span></p>",
                    question_hi: " <p>1. </span><span style=\"font-family:Kokila\">इंस्टेंट</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">मैसेजिंग</span><span style=\"font-family:Cambria Math\"> (IM) </span><span style=\"font-family:Kokila\">सेवाएं</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">निम्नलिखित</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">से</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">किस</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">श्रेणी</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">की</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">सेवाओं</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">अंतर्गत</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">आती</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">हैं</span><span style=\"font-family:Cambria Math\">?</span></p>",
                    options_en: [" <p> Web services </span></p>", " <p> Communication   </span></p>", 
                                " <p> World Wide Web   </span></p>", " <p> Information retrieval  </span></p>"],
                    options_hi: [" <p> </span><span style=\"font-family:Kokila\">वेब</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">सेवाएं</span><span style=\"font-family:Cambria Math\"> [Web services]</span></p>", " <p> </span><span style=\"font-family:Kokila\">संचार</span><span style=\"font-family:Cambria Math\"> [Communication]</span></p>",
                                " <p> </span><span style=\"font-family:Kokila\">वर्ल्ड</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">वाइड</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">वेब</span><span style=\"font-family:Cambria Math\"> [World Wide Web]</span></p>", " <p> </span><span style=\"font-family:Kokila\">सूचना</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">पुनर्प्राप्ति</span><span style=\"font-family:Cambria Math\"> [Information retrieval]</span></p>"],
                    solution_en: " <p>1.(b) Instant messaging (IM)</span><span style=\"font-family:Cambria Math\">, a form of text-based communication in which two persons participate in a single conversation over their computers or mobile devices within an Internet-based chatroom. Examples of major IM services are Google Talk, Facebook Messenger, Snapchat and Skype. </span></p>",
                    solution_hi: " <p>1.(b) </span><span style=\"font-family:Kokila\">इंस्टेंट</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">मैसेजिंग</span><span style=\"font-family:Cambria Math\"> (IM),</span><span style=\"font-family:Cambria Math\">  text-based communication  </span><span style=\"font-family:Kokila\">का</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">एक</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">रूप</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">होता</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">हे</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">जिसमें</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">दो</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">व्यक्ति</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">इंटरनेट</span><span style=\"font-family:Cambria Math\">-</span><span style=\"font-family:Kokila\">आधारित</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">चैट</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">रूम</span><span style=\"font-family:Cambria Math\"> (Internet-based chatroom) </span><span style=\"font-family:Kokila\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">भीतर</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">अपने</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">कंप्यूटर</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">या</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">मोबाइल</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">उपकरणों</span><span style=\"font-family:Cambria Math\"> ( devices ) </span><span style=\"font-family:Kokila\">पर</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">एक</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">ही</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">वार्तालाप</span><span style=\"font-family:Cambria Math\"> (conversation)</span><span style=\"font-family:Kokila\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">भाग</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">लेते</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">हैं।</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">प्रमुख</span><span style=\"font-family:Cambria Math\"> IM </span><span style=\"font-family:Kokila\">सेवाओं</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">उदाहरण</span><span style=\"font-family:Cambria Math\"> Google Talk, Facebook Messenger, Snapchat </span><span style=\"font-family:Kokila\">और</span><span style=\"font-family:Cambria Math\"> Skype </span><span style=\"font-family:Kokila\">हैं।</span></p>",
                    correct: "b",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "2",
                    section: "31",
                    question_en: " <p>2. </span><span style=\"font-family:Cambria Math\">Which of these symbols separates the two parts of an email address?</span></p>",
                    question_hi: " <p>2. </span><span style=\"font-family:Kokila\">इनमें</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">से</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">कौन</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">सा</span><span style=\"font-family:Cambria Math\"> symbol E mail address </span><span style=\"font-family:Kokila\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">दो</span><span style=\"font-family:Cambria Math\"> parts </span><span style=\"font-family:Kokila\">को</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">अलग</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">करता</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">है</span><span style=\"font-family:Cambria Math\">?</span></p>",
                    options_en: [" <p> $ </span></p>", " <p> @</span></p>", 
                                " <p> &</span></p>", " <p> *</span></p>"],
                    options_hi: [" <p> $ </span></p>", " <p> @</span></p>",
                                " <p> &</span></p>", " <p> *</span></p>"],
                    solution_en: " <p>2.(b)</span><span style=\"font-family:Cambria Math\"> The</span><span style=\"font-family:Cambria Math\"> e-mail address</span><span style=\"font-family:Cambria Math\"> generally has two parts, the user id, and the domain name. The @ (at) symbol is used to separate the user id from the domain name in the e-mail address. </span><span style=\"font-family:Cambria Math\">Example:</span><span style=\"font-family:Cambria Math\"> <EMAIL>.</span></p>",
                    solution_hi: " <p>2.(b) e-mail address</span><span style=\"font-family:Cambria Math\">  </span><span style=\"font-family:Kokila\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">आम</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">तौर</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">पर</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">दो</span><span style=\"font-family:Cambria Math\"> parts  </span><span style=\"font-family:Kokila\">होते</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">हैं</span><span style=\"font-family:Cambria Math\">, User Id  </span><span style=\"font-family:Kokila\">और</span><span style=\"font-family:Cambria Math\"> Domain name </span><span style=\"font-family:Kokila\">।</span><span style=\"font-family:Cambria Math\"> E-mail address  </span><span style=\"font-family:Kokila\">में</span><span style=\"font-family:Cambria Math\"> Domain name </span><span style=\"font-family:Kokila\">से</span><span style=\"font-family:Cambria Math\"> User Id  </span><span style=\"font-family:Kokila\">को</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">अलग</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">करने</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">लिए</span><span style=\"font-family:Cambria Math\"> @ (at ) symbol  </span><span style=\"font-family:Kokila\">का</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">उपयोग</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">किया</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">जाता</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">है।</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">उदाहरण</span><span style=\"font-family:Cambria Math\">: <EMAIL></span></p>",
                    correct: "b",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "3",
                    section: "31",
                    question_en: " <p>3. </span><span style=\"font-family:Cambria Math\">Which of the following is an example of a mixed cell reference in MS Excel?</span></p>",
                    question_hi: " <p>3. </span><span style=\"font-family:Kokila\">निम्नलिखित</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">से</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">कौन</span><span style=\"font-family:Cambria Math\"> MS Excel </span><span style=\"font-family:Kokila\">में</span><span style=\"font-family:Cambria Math\"> mixed cell reference </span><span style=\"font-family:Kokila\">का</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">एक</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">उदाहरण</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">है</span><span style=\"font-family:Cambria Math\">?</span></p>",
                    options_en: [" <p> $A$5 </span></p>", " <p> $A5 </span></p>", 
                                " <p> A5</span></p>", " <p> A$5$</span></p>"],
                    options_hi: [" <p> $A$5 </span></p>", " <p> $A5 </span></p>",
                                " <p> A5</span></p>", " <p> A$5$</span></p>"],
                    solution_en: " <p>3.(b) Mixed Reference </span><span style=\"font-family:Cambria Math\">is a type of Absolute reference in which either the column is made constant or the row is made constant. It refers to a specific row or column. For example, $A1 or A$1. Here the example is $A5. </span></p>",
                    solution_hi: " <p>3.(b) Mixed Reference </span><span style=\"font-family:Kokila\">एक</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">प्रकार</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">का</span><span style=\"font-family:Cambria Math\">  Absolute reference  </span><span style=\"font-family:Kokila\">है</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">जिसमें</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">या</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">तो</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">कॉलम</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">को</span><span style=\"font-family:Cambria Math\"> constant  </span><span style=\"font-family:Kokila\">बनाया</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">जाता</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">है</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">या</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">पंक्ति</span><span style=\"font-family:Cambria Math\"> (row)  </span><span style=\"font-family:Kokila\">को</span><span style=\"font-family:Cambria Math\"> constant  </span><span style=\"font-family:Kokila\">बनाया</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">जाता</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">है।</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">यह</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">एक</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">विशिष्ट</span><span style=\"font-family:Cambria Math\">( specific) </span><span style=\"font-family:Kokila\">पंक्ति</span><span style=\"font-family:Cambria Math\"> (row) </span><span style=\"font-family:Kokila\">या</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">कॉलम</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">को</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">संदर्भित</span><span style=\"font-family:Cambria Math\"> ( represent) </span><span style=\"font-family:Kokila\">करता</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">है।</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">उदाहरण</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">लिए</span><span style=\"font-family:Cambria Math\">, $A1 </span><span style=\"font-family:Kokila\">या</span><span style=\"font-family:Cambria Math\"> A$1</span><span style=\"font-family:Kokila\">।</span></p> <p><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">यहाँ</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">उदाहरण</span><span style=\"font-family:Cambria Math\"> $A5 </span><span style=\"font-family:Kokila\">है।</span></p>",
                    correct: "b",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "4",
                    section: "31",
                    question_en: " <p>4. </span><span style=\"font-family:Cambria Math\">Which of the following refers to a workbook with reference to MS Excel?</span></p>",
                    question_hi: " <p>4. </span><span style=\"font-family:Kokila\">निम्नलिखित</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">से</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">कौन</span><span style=\"font-family:Cambria Math\"> MS Excel </span><span style=\"font-family:Kokila\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">संदर्भ</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">एक</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">वर्कबुक</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">को</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">संदर्भित</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">करता</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">है</span><span style=\"font-family:Cambria Math\">?</span></p>",
                    options_en: [" <p> Collection of columns </span></p>", " <p> Collection of rows </span></p>", 
                                " <p> Collection of cells</span></p>", " <p> Collection of worksheets </span></p>"],
                    options_hi: [" <p> </span><span style=\"font-family:Kokila\">कॉलम</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">का</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">संग्रह</span><span style=\"font-family:Cambria Math\"> [Collection of columns]</span></p>", " <p> </span><span style=\"font-family:Kokila\">पंक्तियों</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">का</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">संग्रह</span><span style=\"font-family:Cambria Math\"> [Collection of rows]</span></p>",
                                " <p> </span><span style=\"font-family:Kokila\">सेल्स</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">का</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">संग्रह</span><span style=\"font-family:Cambria Math\"> [Collection of cells]</span></p>", " <p> </span><span style=\"font-family:Kokila\">वर्कशीट्स</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">का</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">संग्रह</span><span style=\"font-family:Cambria Math\"> [Collection of worksheets]</span></p>"],
                    solution_en: " <p>4.(d)</span><span style=\"font-family:Cambria Math\"> Each page is called a </span><span style=\"font-family:Cambria Math\">worksheet</span><span style=\"font-family:Cambria Math\">, and a collection of one or more worksheets is called a</span><span style=\"font-family:Cambria Math\"> workbook </span><span style=\"font-family:Cambria Math\">(spreadsheet file). The term </span><span style=\"font-family:Cambria Math\">Worksheet</span><span style=\"font-family:Cambria Math\"> used in Excel documents is a collection of cells organized in rows and columns.</span></p>",
                    solution_hi: " <p>4.(d) </span><span style=\"font-family:Kokila\">प्रत्येक</span><span style=\"font-family:Cambria Math\"> page </span><span style=\"font-family:Kokila\">को</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">Worksheet  </span><span style=\"font-family:Kokila\">कहा</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">जाता</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">है</span><span style=\"font-family:Cambria Math\">, </span><span style=\"font-family:Kokila\">और</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">एक</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">या</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">अधिक</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">वर्कशीट</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">संग्रह</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">को</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">वर्कबुक</span><span style=\"font-family:Cambria Math\"> (</span><span style=\"font-family:Kokila\">स्प्रेडशीट</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">फ़ाइल</span><span style=\"font-family:Cambria Math\">) </span><span style=\"font-family:Kokila\">कहा</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">जाता</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">है।</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">एक्सेल</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">वर्कशीट</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">प्रयुक्त</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">वर्कशीट</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">शब्द</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">पंक्तियों</span><span style=\"font-family:Cambria Math\"> (row) </span><span style=\"font-family:Kokila\">और</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">स्तंभों</span><span style=\"font-family:Cambria Math\"> (column) </span><span style=\"font-family:Kokila\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">व्यवस्थित</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">सेल</span><span style=\"font-family:Cambria Math\"> ( organized cells ) </span><span style=\"font-family:Kokila\">का</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">एक</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">संग्रह</span><span style=\"font-family:Cambria Math\">(combination) </span><span style=\"font-family:Kokila\">है।</span></p>",
                    correct: "d",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "5",
                    section: "31",
                    question_en: " <p>5. </span><span style=\"font-family:Cambria Math\">Which of the following functions in MS Excel 2007 counts the number of cells in a range that is not empty?</span></p>",
                    question_hi: " <p>5. </span><span style=\"font-family:Cambria Math\">MS Excel 2007 </span><span style=\"font-family:Kokila\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">निम्नलिखित</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">से</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">कौन</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">सा</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">फ़ंक्शन</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">एक</span><span style=\"font-family:Cambria Math\"> Range </span><span style=\"font-family:Kokila\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">सेल्स</span><span style=\"font-family:Cambria Math\"> ( cells ) </span><span style=\"font-family:Kokila\">की</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">संख्या</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">की</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">गणना</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">करता</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">है</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">जो</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">खाली</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">नहीं</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">है</span><span style=\"font-family:Cambria Math\">?</span></p>",
                    options_en: [" <p> COUNTBLANK </span></p>", " <p> COUNTA  </span></p>", 
                                " <p> COUNTIF </span></p>", " <p> COUNT </span></p>"],
                    options_hi: [" <p> COUNTBLANK </span></p>", " <p> COUNTA  </span></p>",
                                " <p> COUNTIF </span></p>", " <p> COUNT </span></p>"],
                    solution_en: " <p>5.(b) COUNTA </span><span style=\"font-family:Cambria Math\">counts the number of cells in a range that is not empty in MS Excel 2007. </span></p>",
                    solution_hi: " <p>5.(b) </span><span style=\"font-family:Cambria Math\">COUNTA </span><span style=\"font-family:Kokila\">उस</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">श्रेणी</span><span style=\"font-family:Cambria Math\"> ( range ) </span><span style=\"font-family:Kokila\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">सेल</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">की</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">संख्या</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">की</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">गणना</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">करता</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">है</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">जो</span><span style=\"font-family:Cambria Math\"> MS </span><span style=\"font-family:Kokila\">एक्सेल</span><span style=\"font-family:Cambria Math\"> 2007 </span><span style=\"font-family:Kokila\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">खाली</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">नहीं</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">है।</span></p>",
                    correct: "b",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "6",
                    section: "31",
                    question_en: " <p>6. </span><span style=\"font-family:Cambria Math\">Which of the following is not an option within Print Preview in MS Word 2007?</span></p>",
                    question_hi: " <p>6. </span><span style=\"font-family:Cambria Math\">MS Word 2007 </span><span style=\"font-family:Kokila\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">प्रिंट</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">प्रीव्यू</span><span style=\"font-family:Cambria Math\"> ( print preview ) </span><span style=\"font-family:Kokila\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">निम्नलिखित</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">से</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">कौन</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">सा</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">विकल्प</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">नहीं</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">है</span><span style=\"font-family:Cambria Math\">?</span></p>",
                    options_en: [" <p> Print  </span></p>", " <p> Zoom </span></p>", 
                                " <p> Quick Print  </span></p>", " <p> Page Setup</span></p>"],
                    options_hi: [" <p> Print  </span></p>", " <p>  Zoom </span></p>",
                                " <p> Quick Print   </span></p>", " <p> Page Setup</span></p> <p><span style=\"font-family:Cambria Math\"> </span></p>"],
                    solution_en: " <p>6.(c) </span><span style=\"font-family:Cambria Math\">The print preview toolbar offers the following options: Print,  Next Page, Prev Page, One Page / Two Page, Zoom In, and Zoom Out. </span></p>",
                    solution_hi: " <p>6.(c) </span><span style=\"font-family:Kokila\">प्रिंट</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">प्रीव्यू</span><span style=\"font-family:Cambria Math\"> ( print preview ) </span><span style=\"font-family:Kokila\">टूलबार</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">निम्नलिखित</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">विकल्प</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">प्रदान</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">करता</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">है</span><span style=\"font-family:Cambria Math\">:Print,  Next Page, Prev Page, One Page / Two Page, Zoom In,</span><span style=\"font-family:Kokila\">और</span><span style=\"font-family:Cambria Math\"> Zoom Out. </span></p>",
                    correct: "c",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "7",
                    section: "31",
                    question_en: "<p>7. <span style=\"font-family: Cambria Math;\">Which of the following features of MS Word is used to assign a name to a specific point in a document?</span></p>",
                    question_hi: "<p>7. <span style=\"font-family: Cambria Math;\">MS Word </span><span style=\"font-family: Kokila;\">की</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">निम्नलिखित</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">में</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">से</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">कौन</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">सी</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">विशेषता</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">किसी</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">दस्तावेज़</span><span style=\"font-family: Cambria Math;\"> (document) </span><span style=\"font-family: Kokila;\">में</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">किसी</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">विशिष्ट</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">बिंदु</span><span style=\"font-family: Cambria Math;\"> (specific point ) </span><span style=\"font-family: Kokila;\">को</span><span style=\"font-family: Cambria Math;\"> Name assign </span><span style=\"font-family: Kokila;\">करने</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">के</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">लिए</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">उपयोग</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">की</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">जाती</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">है</span><span style=\"font-family: Cambria Math;\">?</span></p>",
                    options_en: ["<p>Bookmark</p>", "<p>AutoFormat</p>", 
                                "<p>Hyperlink</p>", "<p>Cross-reference</p>"],
                    options_hi: ["<p><span style=\"font-family: Kokila;\">बुकमार्क</span><span style=\"font-family: Cambria Math;\">[Bookmark]</span></p>", "<p><span style=\"font-family: Kokila;\">ऑटोफॉर्मेट</span><span style=\"font-family: Cambria Math;\">[Autoformat]</span></p>",
                                "<p><span style=\"font-family: Kokila;\">हाइपरलिंक</span><span style=\"font-family: Cambria Math;\"> [Hyperlink]</span></p>", "<p><span style=\"font-family: Kokila;\">क्रॉस</span><span style=\"font-family: Cambria Math;\">-</span><span style=\"font-family: Kokila;\">रेफरेंस</span><span style=\"font-family: Cambria Math;\"> [cross reference]</span></p>"],
                    solution_en: "<p>7.(a) Bookmark <span style=\"font-family: Cambria Math;\">is used to assign a name to a specific point in a document.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">A </span><span style=\"font-family: Cambria Math;\">Hyperlink</span><span style=\"font-family: Cambria Math;\"> is a document element used to jump to a Bookmark in the same document or to an external resource. </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">The </span><span style=\"font-family: Cambria Math;\">AutoFormat</span><span style=\"font-family: Cambria Math;\"> is one type tab that provides options for formatting that occurs automatically based on what one type. </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">A cross-reference allows you to link to other parts of the same document. For example, you might use a cross-reference to link to a chart or graphic that appears elsewhere in the document. The cross-reference appears as a link that takes the reader to the referenced item.</span></p>",
                    solution_hi: "<p>7.(a) <span style=\"font-family: Kokila;\">बुकमार्क</span><span style=\"font-family: Cambria Math;\"> (Bookmark )</span><span style=\"font-family: Kokila;\">एक</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">ऐसा</span><span style=\"font-family: Cambria Math;\"> Object </span><span style=\"font-family: Kokila;\">है</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">जिसका</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">उपयोग</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">किसी</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">दस्तावेज़</span><span style=\"font-family: Cambria Math;\"> (document) </span><span style=\"font-family: Kokila;\">में</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">किसी</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">विशिष्ट</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">बिंदु</span><span style=\"font-family: Cambria Math;\"> (specific point ) </span><span style=\"font-family: Kokila;\">को</span><span style=\"font-family: Cambria Math;\"> Name assign </span><span style=\"font-family: Kokila;\">करने</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">के</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">लिए</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">उपयोग</span><span style=\"font-family: Cambria Math;\"> <span style=\"font-family: Kokila;\">किया</span> <span style=\"font-family: Kokila;\">जाता</span> <span style=\"font-family: Kokila;\">है। |</span></span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">हाइपरलिंक</span><span style=\"font-family: Cambria Math;\"> [Hyperlink]</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">एक</span><span style=\"font-family: Cambria Math;\"> document element </span><span style=\"font-family: Kokila;\">है</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">जिसका</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">उपयोग</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">उसी</span><span style=\"font-family: Cambria Math;\"> document </span><span style=\"font-family: Kokila;\">में</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">या</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">किसी</span><span style=\"font-family: Cambria Math;\"> external resource. </span><span style=\"font-family: Kokila;\">में</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">बुकमार्क</span><span style=\"font-family: Cambria Math;\"> ( Bookmark ) </span><span style=\"font-family: Kokila;\">पर</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">जाने</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">के</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">लिए&nbsp;<span style=\"font-family: Cambria Math;\"> <span style=\"font-family: Kokila;\">किया</span> <span style=\"font-family: Kokila;\">जाता</span> <span style=\"font-family: Kokila;\">है। </span></span></span></p>\r\n<p><span style=\"font-family: Kokila;\">ऑटो</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">फॉर्मेट</span><span style=\"font-family: Cambria Math;\">(AutoFormat)</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">एक</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">प्रकार</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">का</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">टैब</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">है</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">जो</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">फ़ॉर्मेटिंग</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">के</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">लिए</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">विकल्प</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">प्रदान</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">करता</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">है</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">जो</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">कि</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">एक</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">प्रकार</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">से</span><span style=\"font-family: Cambria Math;\"> Automatic </span><span style=\"font-family: Kokila;\">होता</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">है।</span><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><span style=\"font-family: Kokila;\">क्रॉस</span><span style=\"font-family: Cambria Math;\">-</span><span style=\"font-family: Kokila;\">रेफरेंस</span><span style=\"font-family: Cambria Math;\"> (Cross-references)</span><span style=\"font-family: Cambria Math;\"> document element </span><span style=\"font-family: Kokila;\">हैं</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">जो</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">एक</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">ही</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">डॉक्यूमेंट</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">में</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">एक</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">अलग</span><span style=\"font-family: Cambria Math;\"> Element </span><span style=\"font-family: Kokila;\">को</span><span style=\"font-family: Cambria Math;\"> point </span><span style=\"font-family: Kokila;\">करते</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">हैं। क्रॉस-रेफरेंस <span style=\"font-family: Cambria Math;\">(Cross-references)</span><span style=\"font-family: Cambria Math;\"> </span>आपको उसी दस्तावेज़ के अन्य भागों से लिंक करने की अनुमति देता है। उदाहरण के लिए, आप किसी चार्ट ( chart ) या ग्राफ़िक ( graphics)&nbsp; से लिंक करने के लिए क्रॉस-रेफरेंस का उपयोग कर सकते हैं जो दस्तावेज़ में कहीं और दिखाई देता है। क्रॉस-रेफरेंस एक लिंक के रूप में प्रकट होता है जो पाठक ( reader )&nbsp; को संदर्भित आइटम ( referenced item )&nbsp; पर ले जाता है।</span></p>",
                    correct: "a",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "8",
                    section: "31",
                    question_en: " <p>8. </span><span style=\"font-family:Cambria Math\">Which of the following with reference to email is incorrect? </span></p>",
                    question_hi: " <p>8. </span><span style=\"font-family:Kokila\">ईमेल</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">संदर्भ</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">निम्नलिखित</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">से</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">कौन</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">गलत</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">है</span><span style=\"font-family:Cambria Math\">?</span></p>",
                    options_en: [" <p> Each email address in the world is unique  </span></p>", " <p> the first part of an email address consists of the domain name  </span></p>", 
                                " <p> An email is an online message delivery system </span></p>", " <p> Gmail is a free web-based email service</span></p>"],
                    options_hi: [" <p> </span><span style=\"font-family:Kokila\">दुनिया</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">प्रत्येक</span><span style=\"font-family:Cambria Math\"> email address unique </span><span style=\"font-family:Kokila\">होता</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">है</span><span style=\"font-family:Cambria Math\"> |</span></p>", " <p> Email address </span><span style=\"font-family:Kokila\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">पहले</span><span style=\"font-family:Cambria Math\"> part </span><span style=\"font-family:Kokila\">में</span><span style=\"font-family:Cambria Math\"> domain name </span><span style=\"font-family:Kokila\">होता</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">है</span><span style=\"font-family:Cambria Math\"> |</span></p>",
                                " <p> </span><span style=\"font-family:Kokila\">एक</span><span style=\"font-family:Cambria Math\"> Email </span><span style=\"font-family:Kokila\">एक</span><span style=\"font-family:Cambria Math\"> message delivery system </span><span style=\"font-family:Kokila\">होता</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">है</span><span style=\"font-family:Cambria Math\"> |</span></p>", " <p> Gmail  </span><span style=\"font-family:Kokila\">एक</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">मुफ्त</span><span style=\"font-family:Cambria Math\"> web-based  </span><span style=\"font-family:Kokila\">ईमेल</span><span style=\"font-family:Cambria Math\"> service </span><span style=\"font-family:Kokila\">होता</span><span style=\"font-family:Cambria Math\">  </span><span style=\"font-family:Kokila\">है</span><span style=\"font-family:Cambria Math\"> |</span></p>"],
                    solution_en: " <p>8.(b) </span><span style=\"font-family:Cambria Math\">Every </span><span style=\"font-family:Cambria Math\">email address</span><span style=\"font-family:Cambria Math\"> has two main parts: a username and a domain name. The username comes first, followed by an at (@) symbol, followed by the domain name.</span></p>",
                    solution_hi: " <p>8.(b) </span><span style=\"font-family:Kokila\">प्रत्येक</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">email address </span><span style=\"font-family:Kokila\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">दो</span><span style=\"font-family:Cambria Math\"> main part </span><span style=\"font-family:Kokila\">होते</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">हैं</span><span style=\"font-family:Cambria Math\">: </span><span style=\"font-family:Kokila\">एक</span><span style=\"font-family:Cambria Math\"> username   </span><span style=\"font-family:Kokila\">और</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">एक</span><span style=\"font-family:Cambria Math\"> domain name </span><span style=\"font-family:Kokila\">।</span><span style=\"font-family:Cambria Math\"> username  </span><span style=\"font-family:Kokila\">पहले</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">आता</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">है</span><span style=\"font-family:Cambria Math\">, </span><span style=\"font-family:Kokila\">उसके</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">बाद</span><span style=\"font-family:Cambria Math\"> at (@) symbol, </span><span style=\"font-family:Kokila\">और</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">उसके</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">बाद</span><span style=\"font-family:Cambria Math\"> Domain name </span><span style=\"font-family:Kokila\">आता</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">है।</span></p>",
                    correct: "b",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "9",
                    section: "31",
                    question_en: "<p>9. <span style=\"font-family: Cambria Math;\">Which of the following is the maximum font size that can be set up manually in MS Word 2007?</span></p>",
                    question_hi: "<p>9. <span style=\"font-family: Kokila;\">निम्नलिखित</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">में</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">से</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">कौन</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">सा</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">अधिकतम</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">फ़ॉन्ट</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">आकार</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">है</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">जिसे</span><span style=\"font-family: Cambria Math;\"> MS Word 2007 </span><span style=\"font-family: Kokila;\">में</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">मैन्युअल</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">रूप</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">से</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">सेट</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">किया</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">जा</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">सकता</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">है</span><span style=\"font-family: Cambria Math;\">?</span></p>",
                    options_en: ["<p>72</p>", "<p>1638</p>", 
                                "<p>1683</p>", "<p>78</p>"],
                    options_hi: ["<p>72</p>", "<p>1638</p>",
                                "<p>1683</p>", "<p>78</p>"],
                    solution_en: "<p>9.(b) Word supports font size between 1 and 1638 if you access it through Fonts dialog box. So the largest font size in Word 2007 is 1638 points, but all these numbers are not available in default Font Size tool in the ribbon.</p>",
                    solution_hi: "<p>9.(b) <span style=\"font-family: Cambria Math;\">Word 1 </span><span style=\"font-family: Kokila;\">और</span><span style=\"font-family: Cambria Math;\"> 1638 </span><span style=\"font-family: Kokila;\">के</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">बीच</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">फ़ॉन्ट</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">आकार</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">का</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">समर्थन</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">करता</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">है।</span><span style=\"font-family: Cambria Math;\"> Word 2007 </span><span style=\"font-family: Kokila;\">में</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">सबसे</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">बड़ा</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">फ़ॉन्ट</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">आकार</span><span style=\"font-family: Cambria Math;\"> 1638 point </span><span style=\"font-family: Kokila;\">है</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Kokila;\">लेकिन</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">ये</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">सभी</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">नंबर</span><span style=\"font-family: Cambria Math;\"> ribbon </span><span style=\"font-family: Kokila;\">में</span><span style=\"font-family: Cambria Math;\"> default Font Size tool </span><span style=\"font-family: Kokila;\">में</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">उपलब्ध</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">नहीं</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">होते</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">हैं।</span></p>",
                    correct: "b",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "10",
                    section: "31",
                    question_en: " <p>10. </span><span style=\"font-family:Cambria Math\">A document saved in MS Word 2013 has a _______extension.</span></p>",
                    question_hi: " <p>10. </span><span style=\"font-family:Cambria Math\">MS Word 2013 </span><span style=\"font-family:Kokila\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">सहेजे</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">गए</span><span style=\"font-family:Cambria Math\"> ( saved ) </span><span style=\"font-family:Kokila\">दस्तावेज़</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">में</span><span style=\"font-family:Cambria Math\"> _______ </span><span style=\"font-family:Kokila\">एक्सटेंशन</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">होता</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">है।</span></p>",
                    options_en: [" <p> .ocx  </span></p>", " <p> .dokx </span></p>", 
                                " <p> .rtf  </span></p>", " <p> .docx</span></p>"],
                    options_hi: [" <p> .ocx  </span></p>", " <p> .dokx </span></p>",
                                " <p> .rtf  </span></p>", " <p> .docx</span></p>"],
                    solution_en: " <p>10.(d) .</span><span style=\"font-family:Cambria Math\">docx is an extension in Microsoft Word document 2013 for saved documents. </span></p>",
                    solution_hi: " <p>10.(d) .docx </span><span style=\"font-family:Kokila\">माइक्रोसॉफ्ट</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">वर्ड</span><span style=\"font-family:Cambria Math\">  </span><span style=\"font-family:Kokila\">दस्तावेज़</span><span style=\"font-family:Cambria Math\"> 2013 </span><span style=\"font-family:Kokila\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">सेव</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">किये</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">गए</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">डॉक्यूमेंट</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">लिए</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">एक</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">एक्सटेंशन</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">है।</span></p>",
                    correct: "d",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "11",
                    section: "31",
                    question_en: " <p>11</span><span style=\"font-family:Cambria Math\">. Which of the following devices is used in applications such as Computer Aided Design (CAD)?</span></p>",
                    question_hi: " <p>11. Computer Aided Design (CAD) </span><span style=\"font-family:Kokila\">जैसी</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">एप्लीकेशन</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">निम्नलिखित</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">से</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">किस</span><span style=\"font-family:Cambria Math\"> device  </span><span style=\"font-family:Kokila\">का</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">उपयोग</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">किया</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">जाता</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">है</span><span style=\"font-family:Cambria Math\">?</span></p>",
                    options_en: [" <p> Scanners </span></p>", " <p> Plotters </span></p>", 
                                " <p> Speaker </span></p>", " <p> Pantographs </span></p>"],
                    options_hi: [" <p> Scanners </span></p>", " <p> Plotters </span></p>",
                                " <p> Speaker </span></p>", " <p> Pantographs </span></p>"],
                    solution_en: " <p>11.(b) Plotters  are used in applications such as Computer Aided Design (CAD). Plotters were used in applications such as computer-aided design, as they were able to produce line drawings much faster and of a higher quality than contemporary conventional printers. They are most frequently used for CAE (computer-aided engineering) applications, such as CAD (computer-aided design) and CAM (computer-aided manufacturing).</span></p>",
                    solution_hi: " <p>11.(b)  Computer Aided Design(CAD) </span><span style=\"font-family:Kokila\">जैसे</span><span style=\"font-family:Cambria Math\"> Application </span><span style=\"font-family:Kokila\">में</span><span style=\"font-family:Cambria Math\">  Plotters </span><span style=\"font-family:Kokila\">का</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">उपयोग</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">किया</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">जाता</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">है।</span><span style=\"font-family:Cambria Math\">  Computer Aided Design(CAD) </span><span style=\"font-family:Kokila\">जैसे</span><span style=\"font-family:Cambria Math\"> Application </span><span style=\"font-family:Kokila\">में</span><span style=\"font-family:Cambria Math\"> Plotter </span><span style=\"font-family:Kokila\">का</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">उपयोग</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">किया</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">गया</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">था</span><span style=\"font-family:Cambria Math\">, </span><span style=\"font-family:Kokila\">क्योंकि</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">वे</span><span style=\"font-family:Cambria Math\"> conventional printers </span><span style=\"font-family:Kokila\">की</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">तुलना</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">बहुत</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">तेजी</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">से</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">और</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">उच्च</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">गुणवत्ता</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">वाले</span><span style=\"font-family:Cambria Math\"> drawing lines  </span><span style=\"font-family:Kokila\">बनाने</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">सक्षम</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">थे।</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">ये</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">अक्सर</span><span style=\"font-family:Cambria Math\"> CAE (computer-aided engineering) Applications </span><span style=\"font-family:Kokila\">जैसे</span><span style=\"font-family:Cambria Math\"> CAD  (computer aided design) </span><span style=\"font-family:Kokila\">और</span><span style=\"font-family:Cambria Math\"> CAM (computer aided manufacturing ) </span><span style=\"font-family:Kokila\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">लिए</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">उपयोग</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">किए</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">जाते</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">हैं।</span></p>",
                    correct: "b",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "12",
                    section: "31",
                    question_en: " <p>12</span><span style=\"font-family:Cambria Math\">. Which of the following options defines Operating System?</span></p>",
                    question_hi: " <p>12. </span><span style=\"font-family:Kokila\">निम्नलिखित</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">से</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">कौन</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">सा</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">विकल्प</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">ऑपरेटिंग</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">सिस्टम</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">को</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">परिभाषित</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">करता</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">है</span><span style=\"font-family:Cambria Math\">?</span></p>",
                    options_en: [" <p> It is a software that is used to convert source program instructions to set programs. </span></p>", " <p> It is a set of programs that control the way a computer works and runs other programs </span></p>", 
                                " <p> It is a set of programs used to convert high level language to low level language. </span></p>", " <p> It is the actual way of working on computers. </span></p>"],
                    options_hi: [" <p> </span><span style=\"font-family:Kokila\">यह</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">एक</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">सॉफ्टवेयर</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">है</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">जिसका</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">उपयोग</span><span style=\"font-family:Cambria Math\"> source program instructions </span><span style=\"font-family:Kokila\">को</span><span style=\"font-family:Cambria Math\"> set program </span><span style=\"font-family:Kokila\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">बदलने</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">लिए</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">किया</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">जाता</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">है।</span><span style=\"font-family:Cambria Math\"> </span></p>", " <p> </span><span style=\"font-family:Kokila\">यह</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">प्रोग्राम</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">का</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">एक</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">सेट</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">है</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">जो</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">कंप्यूटर</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">काम</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">करने</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">तरीके</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">को</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">नियंत्रित</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">करता</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">है</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">और</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">अन्य</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">प्रोग्राम</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">चलाता</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">है</span></p>",
                                " <p> </span><span style=\"font-family:Kokila\">यह</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">उच्च</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">स्तरीय</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">भाषा</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">को</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">निम्न</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">स्तर</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">की</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">भाषा</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">बदलने</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">लिए</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">उपयोग</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">किए</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">जाने</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">वाले</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">प्रोग्राम</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">का</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">एक</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">समूह</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">है।</span><span style=\"font-family:Cambria Math\"> </span></p>", " <p> </span><span style=\"font-family:Kokila\">यह</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">कंप्यूटर</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">पर</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">काम</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">करने</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">का</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">वास्तविक</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">तरीका</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">है।</span></p>"],
                    solution_en: " <p>12.(b)  An operating system (OS) is system software that manages computer hardware, software resources, and provides common services for computer programs.It is a set of programs that control the way a computer works and runs other programs.</span></p>",
                    solution_hi: " <p>12.(b) </span><span style=\"font-family:Kokila\">ऑपरेटिंग</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">सिस्टम</span><span style=\"font-family:Cambria Math\"> (OS) </span><span style=\"font-family:Kokila\">एक</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">सिस्टम</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">सॉफ्टवेयर</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">है</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">जो</span><span style=\"font-family:Cambria Math\"> computer hardware  </span><span style=\"font-family:Kokila\">और</span><span style=\"font-family:Cambria Math\"> software resources </span><span style=\"font-family:Kokila\">का</span><span style=\"font-family:Cambria Math\"> management </span><span style=\"font-family:Kokila\">करता</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">है</span><span style=\"font-family:Cambria Math\">, </span><span style=\"font-family:Kokila\">और</span><span style=\"font-family:Cambria Math\"> computer program  </span><span style=\"font-family:Kokila\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">लिए</span><span style=\"font-family:Cambria Math\">  common services  </span><span style=\"font-family:Kokila\">प्रदान</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">करता</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">है।</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">यह</span><span style=\"font-family:Cambria Math\"> set of programs  </span><span style=\"font-family:Kokila\">है</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">जो</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">कंप्यूटर</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">काम</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">करने</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">तरीके</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">को</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">नियंत्रित</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">करता</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">है</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">और</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">अन्य</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">प्रोग्राम</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">चलाता</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">है।</span></p>",
                    correct: "b",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "13",
                    section: "31",
                    question_en: " <p>13</span><span style=\"font-family:Cambria Math\">. Cupcake, Donut, Eclair, Froyo and Gingerbread are codenames for different _________ versions ?</span></p>",
                    question_hi: " <p>13. Cupcake, Donut, Eclair, Froyo </span><span style=\"font-family:Kokila\">और</span><span style=\"font-family:Cambria Math\"> Gingerbread  </span><span style=\"font-family:Kokila\">विभिन्न</span><span style=\"font-family:Cambria Math\"> _________ versions </span><span style=\"font-family:Kokila\">के</span><span style=\"font-family:Cambria Math\"> codename </span><span style=\"font-family:Kokila\">हैं।</span></p>",
                    options_en: [" <p>  Piano  </span></p>", " <p>  Android  </span></p>", 
                                " <p>  Computer  </span></p>", " <p>  Ca  </span></p>"],
                    options_hi: [" <p>  Piano  </span></p>", " <p>  Android  </span></p>",
                                " <p>  Computer  </span></p>", " <p>  Ca  </span></p>"],
                    solution_en: " <p>13.(b) Cupcake, Donut, Eclair, Froyo and Gingerbread are code names for different Android versions.</span></p>",
                    solution_hi: " <p>13.(b) Cupcake, Donut, Eclair, Froyo </span><span style=\"font-family:Kokila\">और</span><span style=\"font-family:Cambria Math\"> Gingerbread  </span><span style=\"font-family:Kokila\">विभिन्न</span><span style=\"font-family:Cambria Math\">  Android  versions </span><span style=\"font-family:Kokila\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">लिए</span><span style=\"font-family:Cambria Math\"> codename </span><span style=\"font-family:Kokila\">हैं।</span></p>",
                    correct: "b",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "14",
                    section: "31",
                    question_en: " <p>14</span><span style=\"font-family:Cambria Math\">. Which of the following is the time taken by the CPU to access a location in memory ?</span></p>",
                    question_hi: " <p>14. CPU </span><span style=\"font-family:Kokila\">द्वारा</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">मेमोरी</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">किसी</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">स्थान</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">तक</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">पहुंचने</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">लगने</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">वाला</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">समय</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">निम्न</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">से</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">कौन</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">सा</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">है</span><span style=\"font-family:Cambria Math\">?</span></p>",
                    options_en: [" <p> Instruction cycle</span></p>", " <p> Memory formatting time</span></p>", 
                                " <p> Memory access time</span></p>", " <p> CPU frequency</span></p>"],
                    options_hi: [" <p> Instruction cycle</span></p>", " <p> Memory formatting time</span></p>",
                                " <p> Memory access time</span></p>", " <p> CPU frequency</span></p>"],
                    solution_en: " <p>14.(c)Memory access time is the time taken by the CPU to access a location in memory . </span></p>",
                    solution_hi: " <p>14.(c)  Memory access time , CPU </span><span style=\"font-family:Kokila\">द्वारा</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">मेमोरी</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">किसी</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">स्थान</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">तक</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">पहुंचने</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">लगने</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">वाला</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">समय</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">है</span><span style=\"font-family:Cambria Math\"> |</span></p>",
                    correct: "c",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "15",
                    section: "31",
                    question_en: "<p>15<span style=\"font-family: Cambria Math;\">. Find the odd statement out in relation to firewall. </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">A. Firewall can be software.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">B. Firewall can be hardware.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">C. Firewall can be a combination of software and hardware</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">D. Firewall protects computers from fire.</span></p>",
                    question_hi: "<p>15. <span style=\"font-family: Kokila;\">फ़ायरवॉल</span><span style=\"font-family: Cambria Math;\">( firewall ) </span><span style=\"font-family: Kokila;\">के</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">संबंध</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">में</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">विषम</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">कथन</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">ज्ञात</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">कीजिए</span><span style=\"font-family: Cambria Math;\"> ? </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">A. </span><span style=\"font-family: Kokila;\">फ़ायरवॉल</span><span style=\"font-family: Cambria Math;\"> (firewall )</span><span style=\"font-family: Kokila;\">सॉफ्टवेयर</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">हो</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">सकता</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">है।</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">B. </span><span style=\"font-family: Kokila;\">फ़ायरवॉल</span><span style=\"font-family: Cambria Math;\"> (firewall )</span><span style=\"font-family: Kokila;\">हार्डवेयर</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">हो</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">सकता</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">है।</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">C. </span><span style=\"font-family: Kokila;\">फ़ायरवॉल</span><span style=\"font-family: Cambria Math;\"> (firewall )</span><span style=\"font-family: Kokila;\">सॉफ्टवेयर</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">और</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">हार्डवेयर</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">का</span><span style=\"font-family: Cambria Math;\"> combination </span><span style=\"font-family: Kokila;\">हो</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">सकता</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">है</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">D. </span><span style=\"font-family: Kokila;\">फायरवॉल</span><span style=\"font-family: Cambria Math;\"> (firewall ) </span><span style=\"font-family: Kokila;\">कंप्यूटर</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">को</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">आग</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">से</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">बचाता</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">है।</span></p>",
                    options_en: ["<p>A</p>", "<p>C</p>", 
                                "<p>B</p>", "<p>D</p>"],
                    options_hi: ["<p>A</p>", "<p>C</p>",
                                "<p>B</p>", "<p>D</p>"],
                    solution_en: "<p>15.(d) <span style=\"font-family: Cambria Math;\">Firewall protects computers from fire is odd statement .</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Firewall can be a combination of software and hardware . The main difference between a hardware firewall and a software firewall is that the hardware firewall runs on its own physical device, while a software firewall is installed on another machine. A common example of a software firewall is the firewall built into most operating systems like Windows and macOS.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">A firewall is a network security device that monitors traffic to or from your network. It allows or blocks traffic based on a defined set of security rules.</span></p>",
                    solution_hi: "<p>15.(d ) <span style=\"font-family: Kokila;\">फायरवॉल</span><span style=\"font-family: Cambria Math;\"> (firewall ) </span><span style=\"font-family: Kokila;\">कंप्यूटर</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">को</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">आग</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">से</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">बचाता</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">है यह एक विषम कथन हे&nbsp; |</span></p>\r\n<p><span style=\"font-family: Kokila;\">फ़ायरवॉल<span style=\"font-family: Cambria Math;\"> (firewall )</span>सॉफ्टवेयर<span style=\"font-family: Cambria Math;\"> </span>और<span style=\"font-family: Cambria Math;\"> </span>हार्डवेयर<span style=\"font-family: Cambria Math;\"> </span>का<span style=\"font-family: Cambria Math;\"> combination </span>हो<span style=\"font-family: Cambria Math;\"> </span>सकता<span style=\"font-family: Cambria Math;\"> </span>है | </span><span style=\"font-family: Kokila;\">हार्डवेयर फ़ायरवॉल ( hardware firewall&nbsp; और सॉफ़्टवेयर फ़ायरवॉल ( software firewall ) के बीच मुख्य अंतर यह है कि हार्डवेयर फ़ायरवॉल अपने भौतिक डिवाइस ( physical device ) पर चलता है, जबकि सॉफ़्टवेयर फ़ायरवॉल ( software firewall ) किसी अन्य मशीन पर स्थापित ( install )होता है। सॉफ़्टवेयर फ़ायरवॉल का एक सामान्य उदाहरण Windows और macOS जैसे अधिकांश ऑपरेटिंग सिस्टम में निर्मित फ़ायरवॉल है।</span></p>\r\n<p><span style=\"font-family: Kokila;\">फ़ायरवॉल</span><span style=\"font-family: Cambria Math;\">(Firewall) </span><span style=\"font-family: Kokila;\">एक</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">नेटवर्क</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">सुरक्षा</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">उपकरण</span><span style=\"font-family: Cambria Math;\"> ( network security device ) </span><span style=\"font-family: Kokila;\">है</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">जो</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">आपके</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">नेटवर्क</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">पर</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">या</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">उससे</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">आने</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">वाले</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">ट्रैफ़िक</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">पर</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">नज़र</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">रखता</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">है।</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">यह</span><span style=\"font-family: Cambria Math;\"> security rules </span><span style=\"font-family: Kokila;\">के</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">एक</span><span style=\"font-family: Cambria Math;\"> defined set </span><span style=\"font-family: Kokila;\">के</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">आधार</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">पर</span><span style=\"font-family: Cambria Math;\"> traffic </span><span style=\"font-family: Kokila;\">की</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">अनुमति</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">देता</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">है</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">या</span><span style=\"font-family: Cambria Math;\"> block </span><span style=\"font-family: Kokila;\">करता</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">है।</span><span style=\"font-family: Cambria Math;\"> </span></p>",
                    correct: "d",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "16",
                    section: "31",
                    question_en: " <p>16</span><span style=\"font-family:Cambria Math\">. When a computer virus attached itself to another computer program it is known as a :</span></p>",
                    question_hi: " <p>16. </span><span style=\"font-family:Kokila\">जब</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">एक</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">कंप्यूटर</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">वायरस</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">दूसरे</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">कंप्यूटर</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">प्रोग्राम</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">से</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">जुड़</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">जाता</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">है</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">तो</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">इसे</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">कहते</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">हैं</span><span style=\"font-family:Cambria Math\"> :</span></p>",
                    options_en: [" <p> host program</span></p>", " <p> backward program</span></p>", 
                                " <p> risky program</span></p>", " <p> Trojan horse </span></p>"],
                    options_hi: [" <p> host program</span><span style=\"font-family:Cambria Math\">  </span></p>", " <p> backward program</span></p>",
                                " <p> risky program</span><span style=\"font-family:Cambria Math\">  </span></p>", " <p> Trojan horse </span></p>"],
                    solution_en: " <p>16.(d) When a computer virus attaches itself to another computer program it is known as a Trojan Horse. A Trojan horse is a type of malicious code or software that looks legitimate but can take control of your computer.</span></p>",
                    solution_hi: " <p>16.(d)</span><span style=\"font-family:Kokila\">जब</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">एक</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">कंप्यूटर</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">वायरस</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">दूसरे</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">कंप्यूटर</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">प्रोग्राम</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">से</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">जुड़</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">जाता</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">है</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">तो</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">इसे</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">ट्रोजन</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">हॉर्स</span><span style=\"font-family:Cambria Math\"> ( Trojan horse ) </span><span style=\"font-family:Kokila\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">रूप</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">जाना</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">जाता</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">है।</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">ट्रोजन</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">हॉर्स</span><span style=\"font-family:Cambria Math\"> (Trojan horse )</span><span style=\"font-family:Kokila\">एक</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">प्रकार</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">का</span><span style=\"font-family:Cambria Math\"> Malicious </span><span style=\"font-family:Kokila\">सॉफ़्टवेयर</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">है</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">जो</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">देखने</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">तो</span><span style=\"font-family:Cambria Math\"> Valid </span><span style=\"font-family:Kokila\">लगता</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">है</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">लेकिन</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">आपके</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">कंप्यूटर</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">को</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">नियंत्रित</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">कर</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">सकता</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">है।</span></p>",
                    correct: "d",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "17",
                    section: "31",
                    question_en: " <p>17</span><span style=\"font-family:Cambria Math\">. Which of the following is a small piece of software that uses computer networks and security holes to replicate itself? </span></p>",
                    question_hi: " <p>17. </span><span style=\"font-family:Kokila\">निम्नलिखित</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">से</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">कौन</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">सा</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">सॉफ्टवेयर</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">का</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">एक</span><span style=\"font-family:Cambria Math\"> small piece </span><span style=\"font-family:Kokila\">है</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">जो</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">खुद</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">को</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">दोहराने</span><span style=\"font-family:Cambria Math\"> ( replicate )  </span><span style=\"font-family:Kokila\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">लिए</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">कंप्यूटर</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">नेटवर्क</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">और</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">सुरक्षा</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">होल</span><span style=\"font-family:Cambria Math\"> ( security holes )  </span><span style=\"font-family:Kokila\">का</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">उपयोग</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">करता</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">है</span><span style=\"font-family:Cambria Math\">? </span></p>",
                    options_en: [" <p> Trojan horse </span></p>", " <p> Application </span></p>", 
                                " <p> Worm</span></p>", " <p> Operating System </span></p>"],
                    options_hi: [" <p> Trojan horse </span></p>", " <p> Application </span></p>",
                                " <p> Worm</span></p>", " <p> Operating System </span></p>"],
                    solution_en: " <p>17.(c) A computer worm is a type of malware that spreads copies of itself from computer to computer.</span></p>",
                    solution_hi: " <p>17.(c) computer worm  </span><span style=\"font-family:Kokila\">एक</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">प्रकार</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">का</span><span style=\"font-family:Cambria Math\"> malware </span><span style=\"font-family:Kokila\">है</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">जो</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">कंप्यूटर</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">से</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">कंप्यूटर</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">स्वयं</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">की</span><span style=\"font-family:Cambria Math\"> copy  </span><span style=\"font-family:Kokila\">फैलाता</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">है।</span></p>",
                    correct: "c",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "18",
                    section: "31",
                    question_en: "<p>18<span style=\"font-family: Cambria Math;\">. Which one of the following is a computer virus? </span></p>",
                    question_hi: "<p>18. <span style=\"font-family: Kokila;\">निम्नलिखित</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">में</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">से</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">कौन</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">एक</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">कंप्यूटर</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">वायरस</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">है</span><span style=\"font-family: Cambria Math;\">? </span></p>",
                    options_en: ["<p>Penguin</p>", "<p>Panda</p>", 
                                "<p>Spider</p>", "<p>Creeper</p>"],
                    options_hi: ["<p>पेंगुइन(Penguin)<span style=\"font-family: Cambria Math;\"> </span></p>", "<p>पांडा (Panda)</p>",
                                "<p>स्पाइडर (Spider)</p>", "<p>क्रीपर(Creeper )</p>"],
                    solution_en: "<p>18.(d) The Creeper virus is the first computer virus ever developed by Bob in 1971. while penguin , panda and spider are the search algorithm of google.</p>",
                    solution_hi: "<p>18.(d) <span style=\"font-family: Kokila;\">क्रीपर</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">वायरस</span><span style=\"font-family: Cambria Math;\">( Creeper virus) </span><span style=\"font-family: Kokila;\">बॉब</span><span style=\"font-family: Cambria Math;\">( Bob) </span><span style=\"font-family: Kokila;\">द्वारा</span><span style=\"font-family: Cambria Math;\"> 1971 </span><span style=\"font-family: Kokila;\">में</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">विकसित</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">किया</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">गया</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">पहला</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">कंप्यूटर</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">वायरस</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">है।</span></p>\r\n<p><span style=\"font-family: Kokila;\">जबकि penguin , panda और spider , google&nbsp; के algorithm&nbsp; हे |</span></p>",
                    correct: "d",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "19",
                    section: "31",
                    question_en: " <p>19</span><span style=\"font-family:Cambria Math\">. In a web address, the domain indicator </span><span style=\"font-family:Cambria Math\">‘.com‘</span><span style=\"font-family:Cambria Math\"> stands for:</span></p>",
                    question_hi: " <p>19. </span><span style=\"font-family:Kokila\">एक</span><span style=\"font-family:Cambria Math\"> web address, </span><span style=\"font-family:Kokila\">में</span><span style=\"font-family:Cambria Math\">, domain indicator </span><span style=\"font-family:Cambria Math\">\'.com\' </span><span style=\"font-family:Kokila\">का</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">अर्थ</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">है</span><span style=\"font-family:Cambria Math\">:</span></p>",
                    options_en: [" <p> computer </span></p>", " <p> communication </span></p>", 
                                " <p> common </span></p>", " <p> commercial </span></p>"],
                    options_hi: [" <p> computer            </span></p>", " <p> communication </span></p>",
                                " <p> common             </span></p>", " <p> commercial </span></p>"],
                    solution_en: " <p>19.(d) In a web address, the domain indicator </span><span style=\"font-family:Cambria Math\">‘.com‘ </span><span style=\"font-family:Cambria Math\">stands for commercial.</span></p>",
                    solution_hi: " <p>19.(d) </span><span style=\"font-family:Kokila\">एक</span><span style=\"font-family:Cambria Math\">  web address  </span><span style=\"font-family:Kokila\">में</span><span style=\"font-family:Cambria Math\">,  domain indicator</span><span style=\"font-family:Cambria Math\"> \'.com\' ,  </span><span style=\"font-family:Cambria Math\">commercial </span><span style=\"font-family:Kokila\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">लिए</span><span style=\"font-family:Cambria Math\"> use </span><span style=\"font-family:Kokila\">है।</span><span style=\"font-family:Cambria Math\"> </span></p>",
                    correct: "d",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "20",
                    section: "misc",
                    question_en: " <p>20</span><span style=\"font-family:Cambria Math\">. The second generation computers were based on:</span></p>",
                    question_hi: " <p>20. </span><span style=\"font-family:Kokila\">दूसरी</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">पीढ़ी</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">कंप्यूटर</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">किस</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">पर</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">आधारित</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">थे</span></p>",
                    options_en: [" <p> Transistors </span></p>", " <p> Silicon chips </span></p>", 
                                " <p> Bio optics </span></p>", " <p> Vacuum tubes </span></p>"],
                    options_hi: [" <p> </span><span style=\"font-family:Kokila\">ट्रांजिस्टर</span><span style=\"font-family:Cambria Math\">[Transistors]</span></p>", " <p> </span><span style=\"font-family:Kokila\">सिलिकॉन</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">चिप्स</span><span style=\"font-family:Cambria Math\"> [Silicon chips ]</span></p>",
                                " <p> </span><span style=\"font-family:Kokila\">प्रकाशिकी</span><span style=\"font-family:Cambria Math\"> [ Bio optics ]</span></p>", " <p> </span><span style=\"font-family:Kokila\">वैक्यूम</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">ट्यूब</span><span style=\"font-family:Cambria Math\"> [Vacuum tubes ]</span></p>"],
                    solution_en: " <p>20.(a) The second generation computers were based on transistors.</span></p> <p><span style=\"font-family:Cambria Math\"> A </span><span style=\"font-family:Cambria Math\">silicon chip</span><span style=\"font-family:Cambria Math\"> is an integrated circuit made primarily of silicon. </span></p> <p><span style=\"font-family:Cambria Math\">Bio optics </span><span style=\"font-family:Cambria Math\">is a branch of biology that studies the aggregate of phenomena associated with the use by living organisms of light for orientation. </span></p>",
                    solution_hi: " <p>20.(a)  </span><span style=\"font-family:Kokila\">दूसरी</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">पीढ़ी</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">कंप्यूटर</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">ट्रांजिस्टर</span><span style=\"font-family:Cambria Math\"> ( Transistor )  </span><span style=\"font-family:Kokila\">पर</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">आधारित</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">थे।</span></p> <p><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">एक</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">सिलिकॉन</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">चिप</span><span style=\"font-family:Cambria Math\"> [Silicon chips ] </span><span style=\"font-family:Kokila\">मुख्य</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">रूप</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">से</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">सिलिकॉन</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">से</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">बना</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">एक</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">एकीकृत</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">सर्किट</span><span style=\"font-family:Cambria Math\"> [ integrated circuit ] </span><span style=\"font-family:Kokila\">है।</span><span style=\"font-family:Cambria Math\"> </span></p> <p><span style=\"font-family:Cambria Math\">Bio optics ,  </span><span style=\"font-family:Kokila\">जीव</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">विज्ञान</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">की</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">एक</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">शाखा</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">है</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">जो</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">प्रकाश</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">के</span><span style=\"font-family:Cambria Math\"> living organisms </span><span style=\"font-family:Kokila\">द्वारा</span><span style=\"font-family:Cambria Math\"> orientation.  </span><span style=\"font-family:Kokila\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">लिए</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">उपयोग</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">से</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">जुड़ी</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">घटनाओं</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">समुच्चय</span><span style=\"font-family:Cambria Math\"> ( aggregate of phenomena ) </span><span style=\"font-family:Kokila\">का</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">अध्ययन</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">करती</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">है।</span><span style=\"font-family:Cambria Math\"> </span></p>",
                    correct: "a",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>