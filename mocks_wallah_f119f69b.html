<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">8:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 25</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">25</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 8 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["18"] = {
                name: "General Awareness",
                start: 0,
                end: 23
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="18">General Awareness</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 24,
                end: 24
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "18",
                    question_en: "<p>1. According to Census 2011, India&rsquo;s population accounts _________ of the world&rsquo;s&nbsp;population.</p>",
                    question_hi: "<p>1. 2011 की जनगणना के अनुसार, भारत की जनसंख्या विश्व की जनसंख्या का _________ है।</p>",
                    options_en: ["<p>10%</p>", "<p>15%</p>", 
                                "<p>20%</p>", "<p>17.5%</p>"],
                    options_hi: ["<p>10%</p>", "<p>15%</p>",
                                "<p>20%</p>", "<p>17.5%</p>"],
                    solution_en: "<p>1.(d) <strong>17.5%</strong>. Census 2011: Five Most Populated states: Uttar Pradesh (19.98 Crores), Maharashtra (11.24 Crores), Bihar (10.41 Crores), West Bengal (9.13 Crores), Andhra Pradesh (8.46 Crores). Least Populated States/UT: Lakshadweep (64 Thousands), Daman and Diu (2.43 Lakhs), Dadra and Nagar Haveli (3.44 Lakhs), Andaman and Nicobar Islands (3.81 Lakhs), Sikkim (6.11 Lakhs).</p>",
                    solution_hi: "<p>1.(d) <strong>17.5%.</strong> जनगणना 2011: पांच सबसे अधिक आबादी वाले राज्य: उत्तर प्रदेश (19.98 करोड़), महाराष्ट्र (11.24 करोड़), बिहार (10.41 करोड़), पश्चिम बंगाल (9.13 करोड़), आंध्र प्रदेश (8.46 करोड़)। सबसे कम आबादी वाले राज्य/केंद्रशासित प्रदेश: लक्षद्वीप (64 हज़ार), दमन और दीव (2.43 लाख), दादरा और नगर हवेली (3.44 लाख), अंडमान और निकोबार द्वीप समूह (3.81 लाख), सिक्किम (6.11 लाख)।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "18",
                    question_en: "<p>2. Rouf is the traditional dance of __________, which is practiced on the festive occasions of Eid and during Ramzan days.</p>",
                    question_hi: "<p>2. राउफ _______ का पारंपरिक नृत्य है, जो ईद के त्योहारों और रमजान के दिनों में किया जाता है।</p>",
                    options_en: ["<p>Chandigarh</p>", "<p>Leh</p>", 
                                "<p>Jammu &amp; Kashmir</p>", "<p>Puducherry</p>"],
                    options_hi: ["<p>चंडीगढ़</p>", "<p>लेह</p>",
                                "<p>जम्मू और कश्मीर</p>", "<p>पुदुचेरी</p>"],
                    solution_en: "<p>2.(c)<strong> Jammu &amp; Kashmir. </strong>Rouf dance is primarily performed to celebrate the harvesting season of spring. Other important festivals: Jammu &amp; Kashmir - Tulip festival, Navreh. Leh - Ladakh - Matho Nagrang, Dosmoche festival, Hemis festival, Sindhu Darshan. Punjab - Lohri, Hola Mohalla, Teeyan, Babe-da-viah, Kila Raipur festival. Puducherry - Mangani festival, Mandalam Vilakku, Putha Lanthira, Kandoori festival.</p>",
                    solution_hi: "<p>2.(c) <strong>जम्मू और कश्मीर। </strong>राउफ नृत्य मुख्य रूप से वसंत की कटाई के मौसम का जश्न मनाने के लिए किया जाता है। अन्य महत्वपूर्ण त्यौहार: जम्मू और कश्मीर - ट्यूलिप उत्सव, नवरेह। लेह - लद्दाख - माथो नागरांग, दोस्मोचे उत्सव, हेमिस उत्सव, सिंधु दर्शन। पंजाब - लोहड़ी, होला मोहल्ला, तीयां, बाबे-दा-विया, किला रायपुर त्योहार। पुडुचेरी - मंगनी उत्सव, मंडलम विलाक्कू, पुथा लंथिरा, कंदूरी उत्सव।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "18",
                    question_en: "<p>3. The cell wall of bacteria is made up of:</p>",
                    question_hi: "<p>3. जीवाणुओं की कोशिका भित्ति किससे बनी होती है?</p>",
                    options_en: ["<p>peptidoglycan</p>", "<p>glycogen</p>", 
                                "<p>cellulose</p>", "<p>peptone</p>"],
                    options_hi: ["<p>पेप्टिडोग्लाइकन (peptidoglycan)</p>", "<p>ग्लाइकोजन (glycogen)</p>",
                                "<p>सेल्यूलोज (cellulose)</p>", "<p>पेप्टॉन (peptone)</p>"],
                    solution_en: "<p>3.(a) <strong>Peptidoglycan </strong>is a complex molecule made of sugars and amino acids. It provides structural support and shape to bacterial cells and protects them from environmental stress. Glycogen is a storage polysaccharide found in animals, not related to cell walls. Cellulose is a polysaccharide that makes up the cell walls of plants, not bacteria. Peptones are products of protein digestion used as nutrient sources in microbial culture media, not structural components of cell walls.</p>",
                    solution_hi: "<p>3.(a) <strong>पेप्टिडोग्लाइकन </strong>शर्करा और अमीनो अम्ल से बना एक जटिल अणु है। यह जीवाणु कोशिकाओं को संरचनात्मक सहायता और आकार प्रदान करता है और उन्हें पर्यावरणीय तनाव से बचाता है। ग्लाइकोजन जानवरों में पाया जाने वाला एक संचयन पॉलीसैकेराइड है, जो कोशिका भित्ति से संबंधित नहीं है। सेल्यूलोज़ एक पॉलीसैकेराइड है जो बैक्टीरिया की नहीं बल्कि पौधों की कोशिका भित्ति में पाया जाता है। पेप्टोन्स, प्रोटीन पाचन के उत्पाद हैं, जिनका उपयोग कोशिका भित्ति के संरचनात्मक घटक के रूप में नहीं बल्कि सूक्ष्मजीव संवर्धन माध्यम में पोषक स्रोत के रूप में किया जाता है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "18",
                    question_en: "<p>4. How does the CPU distinguish between each memory cell ?</p>",
                    question_hi: "<p>4. CPU प्रत्येक मेमोरी सेल के बीच अंतर कैसे करता है?</p>",
                    options_en: ["<p>By its byte size</p>", "<p>By its capacity</p>", 
                                "<p>By its address</p>", "<p>By its data item</p>"],
                    options_hi: ["<p>इसके बाइट आकार द्वारा</p>", "<p>इसकी क्षमता द्वारा</p>",
                                "<p>इसके पते द्वारा</p>", "<p>इसके डेटा आइटम द्वारा</p>"],
                    solution_en: "<p>4.(c) <strong>By its address. </strong>The Central Processing Unit (CPU) is the primary component of a computer that acts as its &ldquo;control center.&rdquo; It has three main parts: Arithmetic logic unit (ALU), control unit (CU), and memory unit.</p>",
                    solution_hi: "<p>4.(c) <strong>इसके पते द्वारा। </strong>सेंट्रल प्रोसेसिंग यूनिट (CPU) कंप्यूटर का प्राथमिक घटक है जो इसके \"कंट्रोल सेंटर\" के रूप में कार्य करता है। इसके तीन मुख्य भाग हैं: अर्थमेटिक लॉजिक यूनिट (ALU), कंट्रोल यूनिट (CU), और मेमोरी यूनिट।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "18",
                    question_en: "<p>5. Which of the following dates is observed as a national festival?</p>",
                    question_hi: "<p>5. निम्नलिखित में से किस तिथि को राष्ट्रीय पर्व के रूप में मनाया जाता है?</p>",
                    options_en: ["<p>24 October</p>", "<p>2 October</p>", 
                                "<p>25 October</p>", "<p>10 October</p>"],
                    options_hi: ["<p>24 अक्टूबर</p>", "<p>2 अक्टूबर</p>",
                                "<p>25 अक्टूबर</p>", "<p>10 अक्टूबर</p>"],
                    solution_en: "<p>5.(b) <strong>2 October.</strong> This day is celebrated as Gandhi Jayanti in India, to honor the birth of Mahatma Gandhi ji. Pravasi Bharatiya Divas (PBD) - 9th January. Martyrs\' Day - 30th January. United Nations Day - 24 October. International Artist\'s Day - 25 October. World Mental Health Day - 10 October. Other national festivals include Independence Day (August 15) and Republic Day (January 26).</p>",
                    solution_hi: "<p>5.(b) <strong>2 अक्टूबर। </strong>इस दिन को महात्मा गांधी जी के जन्म के सम्मान में गांधी जयंती के रूप में मनाया जाता है। प्रवासी भारतीय दिवस (PBD) - 9 जनवरी। शहीद दिवस - 30 जनवरी। संयुक्त राष्ट्र दिवस - 24 अक्टूबर। अंतर्राष्ट्रीय कलाकार दिवस - 25 अक्टूबर। विश्व मानसिक स्वास्थ्य दिवस - 10 अक्टूबर। अन्य राष्ट्रीय त्योहारों में स्वतंत्रता दिवस (15 अगस्त) और गणतंत्र दिवस (26 जनवरी) शामिल हैं।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "18",
                    question_en: "<p>6. The Union Cabinet, in Aug 2022, approved the enhancement in the limit of Emergency Credit Line Guarantee Scheme (ECLGS) by ________ , with the additional amount being earmarked exclusively for enterprises in hospitality and related sectors.</p>",
                    question_hi: "<p>6. केंद्रीय मंत्रिमंडल ने अगस्त 2022 में आपातकालीन क्रेडिट लाइन गारंटी योजना (ECLGS) की सीमा को ________ तक बढ़ाने को मंजूरी दे दी, जिसमें अतिरिक्त राशि विशेष रूप से अतिरिक्त और संबंधित क्षेत्रों के उद्यमों के लिए निर्धारित की गई है।</p>",
                    options_en: ["<p>₹10,000 crore</p>", "<p>₹50,000 crore</p>", 
                                "<p>₹60,000 crore</p>", "<p>₹30,000 crore</p>"],
                    options_hi: ["<p>₹10,000 करोड़</p>", "<p>₹50,000 करोड़</p>",
                                "<p>₹60,000 करोड़</p>", "<p>₹30,000 करोड़</p>"],
                    solution_en: "<p>6.(b) <strong>₹50,000 crore.</strong> ECLGS was rolled out in 2020 as part of the Centre&rsquo;s Aatmanirbhar package in response to the Covid-19 crisis. The objective was to support small businesses struggling to meet their operational liabilities due to the imposition of a nationwide lockdown.</p>",
                    solution_hi: "<p>6.(b) <strong>₹50,000 करोड़।</strong> ECLGS (आपातकालीन क्रेडिट लाइन गारंटी योजना) को 2020 में केंद्र सरकार के आत्मनिर्भर पैकेज के तहत कोविड-19 संकट में लागू किया गया था। इसका उद्देश्य छोटे व्यवसायों को सहायता प्रदान करना था, जो देशव्यापी लॉकडाउन के कारण अपनी संचालन संबंधी देनदारियों को पूरा करने में संघर्ष कर रहे थे।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "18",
                    question_en: "<p>7. Warren Hastings became the Governor-General of Bengal from Governor of Bengal after the passing of which of the following Acts ?</p>",
                    question_hi: "<p>7. निम्नलिखित में से किस अधिनियम के पारित होने के बाद वॉरेन हेस्टिंग्स (Warren Hastings) बंगाल के गवर्नर से बंगाल के गवर्नर-जनरल बन गए थे?</p>",
                    options_en: ["<p>Charter Act, 1813</p>", "<p>Pitt&rsquo;s India Act, 1784</p>", 
                                "<p>Indian Council Act of 1861</p>", "<p>Regulating Act, 1773</p>"],
                    options_hi: ["<p>चार्टर अधिनियम, 1813 (Charter Act, 1813)</p>", "<p>पिट्स इंडिया एक्ट, 1784 (Pitt&rsquo;s India Act, 1784)</p>",
                                "<p>1861 का भारतीय परिषद अधिनियम (Indian Council Act of 1861)</p>", "<p>विनियमन अधिनियम, 1773 (Regulating Act, 1773)</p>"],
                    solution_en: "<p>7.(d) <strong>Regulating Act, 1773. </strong>The act was the first step taken by the British government to control and regulate the affairs of East India company in India. Pitt&rsquo;s India Act, 1784 was passed to rectify the defects of the Regulating Act 1773. The Charter Act of 1813 ended the East India Company\'s monopoly over trade in India, except for tea, opium, and trade with China. The Indian Councils Act of 1861 introduced the portfolio system and empowered the Viceroy to issue ordinances.</p>",
                    solution_hi: "<p>7.(d) <strong>विनियमन अधिनियम,1773 । </strong>यह अधिनियम भारत में ईस्ट इंडिया कंपनी के मामलों को नियंत्रित और विनियमित करने के लिए ब्रिटिश सरकार द्वारा उठाया गया पहला कदम था। विनियमन अधिनियम 1773 के कमियों को दूर करने के लिए पिट्स इंडिया अधिनियम, 1784 पारित किया गया था। 1813 के चार्टर अधिनियम ने चाय, अफीम और चीन के साथ व्यापार को छोड़कर भारत में व्यापार पर ईस्ट इंडिया कंपनी के एकाधिकार को समाप्त कर दिया। 1861 के भारतीय परिषद अधिनियम ने पोर्टफोलियो प्रणाली की शुरुआत की और वायसराय को अध्यादेश जारी करने का अधिकार दिया।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "18",
                    question_en: "<p>8. Who amongst the following Mughal emperors got classical Hindu mythological texts Ramayana and Mahabharata translated into Persian ?</p>",
                    question_hi: "<p>8. निम्नलिखित में से किस मुगल सम्राट ने प्राचीन हिंदू पौराणिक ग्रंथों रामायण और महाभारत का फारसी में अनुवाद करवाया था?</p>",
                    options_en: ["<p>Jahangir</p>", "<p>Aurangzeb</p>", 
                                "<p>Akbar</p>", "<p>Shahjahan</p>"],
                    options_hi: ["<p>जहाँगीर</p>", "<p>औरंगजेब</p>",
                                "<p>अकबर</p>", "<p>शाहजहां</p>"],
                    solution_en: "<p>8.(c) <strong>Akbar</strong> was the 3rd Mughal emperor (1556 to 1605). The Mahabharata was translated by Faizi and `Abd al-Qadir Bada\'uni and named Razmnama. Ramayana translated by Mulla Abdul Qadir Badayuni. Literature under the rule of Akbar: Akbarnama (Autobiography of Akbar) and Ain-i-Akbari written by Abu\'l-Fazl.</p>",
                    solution_hi: "<p>8.(c) <strong>अकबर </strong>तीसरा मुगल बादशाह (1556 से 1605 तक) था। महाभारत का अनुवाद फैजी और अब्दुल कादिर बदायुनी ने किया था और इसका नाम रज्मनामा रखा गया। रामायण का अनुवाद मुल्ला अब्दुल कादिर बदायुनी ने किया। अकबर के शासन के दौरान साहित्य: अबुल-फ़ज़ल द्वारा लिखित अकबरनामा (अकबर की आत्मकथा) और आइन-ए-अकबरी।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "18",
                    question_en: "<p>9. The Big Dipper is an asterism formed by the seven brightest stars in the constellation:</p>",
                    question_hi: "<p>9. बिग डिपर (Big Dipper) एक नक्षत्र (asterism) है जो _________ तारामंडल के सात सबसे चमकीले तारों द्वारा निर्मित होता है।</p>",
                    options_en: ["<p>Ursa Major</p>", "<p>Cassiopeia</p>", 
                                "<p>Leo Major</p>", "<p>Orion</p>"],
                    options_hi: ["<p>उर्सा मेजर (Ursa Major)</p>", "<p>कैसिओपेआ (Cassiopeia)</p>",
                                "<p>लियो मेजर (Leo Major)</p>", "<p>ओरियन (Orion)</p>"],
                    solution_en: "<p>9.(a) <strong>Ursa Major</strong> (Great Bear) is the third-largest constellation in the sky and the largest constellation in the Northern Hemisphere. Orion is three bright stars close together in an almost-straight line. Cassiopeia is a large constellation located in the northern sky. The Leo constellation lies in the northern sky.</p>",
                    solution_hi: "<p>9.(a) <strong>उर्सा मेजर (</strong>ग्रेट बीयर) आकाश में तीसरा सबसे बड़ा तारामंडल है और उत्तरी गोलार्ध में सबसे बड़ा तारामंडल है। ओरियन तीन चमकीले तारे हैं जो एक साथ लगभग सीधी रेखा में स्थित हैं। कैसिओपिया उत्तरी आकाश में स्थित एक बड़ा तारामंडल है। लियो तारामंडल उत्तरी आकाश में स्थित है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "18",
                    question_en: "<p>10. Milk of magnesia is a suspension of ____________ in water.</p>",
                    question_hi: "<p>10. मिल्क ऑफ मैग्नीशिया जल में _________ का निलंबन होता है।</p>",
                    options_en: ["<p>magnesium chlorate</p>", "<p>magnesium bromide</p>", 
                                "<p>magnesium hydroxide</p>", "<p>magnesium oxalate</p>"],
                    options_hi: ["<p>मैग्नीशियम क्लोरेट</p>", "<p>मैग्नीशियम ब्रोमाइड</p>",
                                "<p>मैग्नीशियम हाइड्रॉक्साइड</p>", "<p>मैग्नीशियम ऑक्सालेट</p>"],
                    solution_en: "<p>10.(c) <strong>Magnesium hydroxide</strong>. It is an inorganic compound. As a suspension in water, it is often called milk of magnesia because of its milk-like appearance. Name of base and Found in: Calcium hydroxide - Lime water, Ammonium hydroxide - Window cleaner, Sodium hydroxide/Potassium hydroxide - Soap.</p>",
                    solution_hi: "<p>10.(c) <strong>मैग्नीशियम हाइड्रॉक्साइड।</strong> यह एक अकार्बनिक यौगिक है। जल में निलंबन के रूप में इसे प्रायः \"मिल्क ऑफ मैग्नेशिया\" कहा जाता है, क्योंकि इसका रूप दूध जैसा होता है। क्षार का नाम और उपयोग: कैल्शियम हाइड्रॉक्साइड - चूने का पानी, अमोनियम हाइड्रॉक्साइड - विंडो क्लीनर, सोडियम हाइड्रॉक्साइड/पोटैशियम हाइड्रॉक्साइड - साबुन।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "18",
                    question_en: "<p>11. Intervention by the monetary authority of a country in the money market to keep money supply stable against exogenous or sometimes external shocks is called _____.</p>",
                    question_hi: "<p>11. बहिर्जात (exogenous) या बाहरी अस्थिरता (external shocks) के विरुद्ध मुद्रा बाजार में मुद्रा आपूर्ति को स्थिर रखने के लिए किसी देश की मुद्रा प्राधिकारी (monetary authority) द्वारा हस्तक्षेप ________ कहलाता है।</p>",
                    options_en: ["<p>neutralisation</p>", "<p>capitalisation</p>", 
                                "<p>conservation</p>", "<p>sterilisation</p>"],
                    options_hi: ["<p>निराकरण (neutralisation)</p>", "<p>पूंजीवादीकरण (capitalisation)</p>",
                                "<p>संरक्षरण (conservation)</p>", "<p>बांध्यकरण (sterilisation)</p>"],
                    solution_en: "<p>11.(d) <strong>Sterilisation</strong>. It refers to the actions taken by a monetary authority, such as a central bank, to offset the effects of foreign exchange operations on the domestic money supply. For instance, if a central bank experiences an inflow of foreign exchange, it might conduct an open market sale of government securities to absorb the excess money and maintain the stability of the money supply. This process helps to protect the economy from external shocks and prevent inflationary or deflationary pressures.</p>",
                    solution_hi: "<p>11.(d) <strong>बांध्यकरण</strong>। यह किसी मौद्रिक प्राधिकरण, जैसे कि केंद्रीय बैंक, द्वारा घरेलू मुद्रा आपूर्ति पर विदेशी मुद्रा संचालन के प्रभावों को संतुलित करने के लिए की गई कार्रवाइयों को संदर्भित करता है। उदाहरण के लिए, यदि किसी केंद्रीय बैंक को विदेशी मुद्रा का प्रवाह अनुभव होता है, तो वह अतिरिक्त धन को अवशोषित करने और मुद्रा आपूर्ति की स्थिरता बनाए रखने के लिए सरकारी प्रतिभूतियों की खुले बाजार में बिक्री कर सकता है। यह प्रक्रिया अर्थव्यवस्था को अप्रत्याशित घटनाओं से बचाने और मुद्रास्फीति या अपस्फीति के दबावों को रोकने में मदद करती है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "18",
                    question_en: "<p>12. Which of the following is NOT an Olympics Sport ? (As of March, 2023)</p>",
                    question_hi: "<p>12. निम्नलिखित में से कौन-सा एक ओलंपिक खेल नहीं है? (मार्च 2023 तक)</p>",
                    options_en: ["<p>Sailing</p>", "<p>3&times;3 Basketball</p>", 
                                "<p>Cricket</p>", "<p>Rowing</p>"],
                    options_hi: ["<p>नौकायन (Sailing)</p>", "<p>3&times;3 बास्केटबॉल (3&times;3 Basketball)</p>",
                                "<p>क्रिकेट (Cricket)</p>", "<p>रोइंग (Rowing)</p>"],
                    solution_en: "<p>12.(c) <strong>Cricket</strong>. It was part of the 1900 Paris Olympics with only two teams, but it has not been included in subsequent Olympics. However, cricket is set to return to the Olympics at the Los Angeles 2028 Games, following its formal inclusion by the International Olympic Committee (IOC) at the 141st IOC Session. This will mark cricket\'s return to the Olympic stage after 128 years.</p>",
                    solution_hi: "<p>12.(c) <strong>क्रिकेट</strong>। यह 1900 के पेरिस ओलंपिक का हिस्सा था जिसमें केवल दो टीमें शामिल थीं, लेकिन इसे बाद के ओलंपिक में शामिल नहीं किया गया। हालांकि, क्रिकेट को लॉस एंजिल्स 2028 खेलों में ओलंपिक में वापस लाया जाएगा, जिसे अंतर्राष्ट्रीय ओलंपिक समिति (IOC) द्वारा 141वें IOC सत्र में औपचारिक रूप से शामिल किया जाएगा। इससे 128 वर्षों के बाद क्रिकेट की ओलंपिक मंच पर वापसी होगी।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "18",
                    question_en: "<p>13. Baiga is a folk music of Baiga Tribe from which of the following states?</p>",
                    question_hi: "<p>13. बैगा (Baiga) निम्नलिखित में से किस राज्य की बैगा जनजाति का लोक संगीत है?</p>",
                    options_en: ["<p>Assam</p>", "<p>Bihar</p>", 
                                "<p>Uttarakhand</p>", "<p>Madhya Pradesh</p>"],
                    options_hi: ["<p>असम</p>", "<p>बिहार</p>",
                                "<p>उत्तराखंड</p>", "<p>मध्य प्रदेश</p>"],
                    solution_en: "<p>13.(d) <strong>Madhya Pradesh.</strong> Other Baiga tribe dances include Faag, Saila, Karma, and Bilma. The Folk Dances of Madhya Pradesh: Gaur Dance - Gaur tribe, Pandavani Dance - Pardhi Community, Karma Dance - Kharwar tribe.</p>",
                    solution_hi: "<p>13.(d) <strong>मध्य प्रदेश।</strong> बैगा जनजाति के अन्य नृत्यों में फाग, सैला, कर्मा और बिल्मा शामिल हैं। मध्य प्रदेश के लोक नृत्य: गौर नृत्य - गौर जनजाति, पंडवानी नृत्य - पारधी समुदाय, करमा नृत्य - खरवार जनजाति।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "18",
                    question_en: "<p>14. In 1955, _____ noted the possibility of using small-scale industries for promoting rural development.</p>",
                    question_hi: "<p>14. 1955 में, ______ ने ग्रामीण विकास को बढ़ावा देने के लिए लघुस्तर उद्योगों (small-scale industries) का इस्तेमाल करने की संभावनाओं का उल्लेख किया।</p>",
                    options_en: ["<p>Kelkar Committee</p>", "<p>Sarkaria Committee</p>", 
                                "<p>Karve Committee</p>", "<p>Chakravarty Committee</p>"],
                    options_hi: ["<p>केलकर समिति (Kelkar Committee)</p>", "<p>सरकारिया समिति (Sarkaria Committee)</p>",
                                "<p>कर्वे समिति (Karve Committee)</p>", "<p>चक्रवर्ती समिति (Chakravarty Committee)</p>"],
                    solution_en: "<p>14.(c) <strong>Karve Committee. </strong>Other committees are: Kelkar Committee - It was formed in 2002 under the chairmanship of Vijay Kelkar. The Kelkar Committee was re-constituted in 2015 to study and evaluate the Public Private Partnership (PPP) model in India. Sarkaria Committee (1983) formed under the chairmanship of Justice R.S. Sarkaria to review the question of center-state relations. The S. Chakravarty Committee (1982) was set up to review the working of the monetary system.</p>",
                    solution_hi: "<p>14.(c) <strong>कर्वे समिति।</strong> अन्य समितियाँ: केलकर समिति - इसका गठन 2002 में विजय केलकर की अध्यक्षता में किया गया था। सार्वजनिक निजी साझेदारी (PPP) मॉडल का अध्ययन और मूल्यांकन करने के लिए भारत में केलकर समिति का पुनः गठन 2015 में किया गया था। सरकारिया समिति (1983) का गठन न्यायमूर्ति आर.एस. सरकारिया की अध्यक्षता में केंद्र-राज्य संबंधों के प्रश्न की समीक्षा करने के लिए किया गया था। एस. चक्रवर्ती समिति (1982) की स्थापना मौद्रिक प्रणाली के कामकाज की समीक्षा करने के लिए की गई थी।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "18",
                    question_en: "<p>15. In a test match, the captain of the fielding side may demand a new ball after _____.</p>",
                    question_hi: "<p>15. एक टेस्ट मैच में, फ़िल्डिंग करने वाली टीम का कप्तान ________के बाद नई गेंद की मांग कर सकता है।</p>",
                    options_en: ["<p>85 overs</p>", "<p>70 overs</p>", 
                                "<p>80 overs</p>", "<p>75 overs</p>"],
                    options_hi: ["<p>85 ओवरों</p>", "<p>70 ओवरों</p>",
                                "<p>80 ओवरों</p>", "<p>75 ओवरों</p>"],
                    solution_en: "<p>15.(c) <strong>80 overs. </strong>Test cricket is the traditional form of the game, which has been played since 1877 and now settled in a five-day format which comprises two innings each. It is considered the pinnacle form because it tests teams over a longer period of time. Test cricket consists of three sessions of two hours each, the break between sessions being 40 minutes for lunch and 20 minutes. The International Cricket Council (ICC) governs Test cricket.</p>",
                    solution_hi: "<p>15.(c) <strong>80 ओवरों।</strong> टेस्ट क्रिकेट खेल की पारंपरिक रूप है, जो 1877 से खेली जा रही है और अब पांच-दिवसीय प्रारूप में निर्धारित है जिसमें प्रत्येक टीम को दो पारियां मिलती हैं। इसे सर्वोच्च प्रारूप माना जाता है क्योंकि यह लम्बे समय तक टीमों का परीक्षण करता है। टेस्ट क्रिकेट में दो-दो घंटे के तीन सेशन होते हैं, प्रत्येक सेशन के बीच 40 मिनट का लंच ब्रेक और 20 मिनट का ब्रेक होता है। टेस्ट क्रिकेट का संचालन अंतर्राष्ट्रीय क्रिकेट परिषद (ICC) करता है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "18",
                    question_en: "<p>16. Jharkhand and Chhattisgarh states are rich with ___________ material deposits.</p>",
                    question_hi: "<p>16. झारखंड और छत्तीसगढ़ राज्य ___________ के भंडार से समृद्ध हैं।</p>",
                    options_en: ["<p>gold</p>", "<p>petroleum</p>", 
                                "<p>silicon</p>", "<p>coal</p>"],
                    options_hi: ["<p>सोना</p>", "<p>पेट्रोलियम</p>",
                                "<p>सिलिकॉन</p>", "<p>कोयला</p>"],
                    solution_en: "<p>16.(d) <strong>Coal</strong>. Jharkhand - 40% of the country\'s total mineral resources are available here. It produces coking coal, uranium, and pyrite. Jharkhand ranks 1st in the production of coal, mica, kyanite, and copper in India. Major coal fields include Jharia, Bokaro, Karanpura, Hutar, Auranga, Daltonganj, Deoghar, and Rajmahal. Chhattisgarh: A leading producer of coal, dolomite, bauxite, and iron ore. Major coal-producing areas include Korea, Korba, Raigarh, and Sarguja districts.</p>",
                    solution_hi: "<p>16.(d) <strong>कोयला</strong>। झारखंड - देश के कुल खनिज संसाधनों का 40% यहाँ उपलब्ध है। यह कोकिंग कोल, यूरेनियम और पाइराइट का उत्पादन करता है। झारखंड भारत में कोयला, अभ्रक, कायनाइट और तांबे के उत्पादन में पहले स्थान पर है। प्रमुख कोयला क्षेत्रों में झरिया, बोकारो, करणपुरा, हुटार, औरंगा, डाल्टनगंज, देवघर और राजमहल शामिल हैं। छत्तीसगढ़: कोयला, डोलोमाइट, बॉक्साइट और लौह अयस्क का अग्रणी उत्पादक। प्रमुख कोयला उत्पादक क्षेत्रों में कोरिया, कोरबा, रायगढ़ और सरगुजा जिले शामिल हैं।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "18",
                    question_en: "<p>17. In PPT What method can be used to move among slides if your hands are already on&nbsp;the keyboard ?</p>",
                    question_hi: "<p>17. PPT में, यदि आपके हाथ पहले से ही कीबोर्ड पर हैं तो स्लाइडों के बीच आने-जाने के लिए किस विधि&nbsp;का उपयोग किया जा सकता है?</p>",
                    options_en: ["<p>Scrolling with the mouse wheel</p>", "<p>Using the keyboard directional keys</p>", 
                                "<p>Clicking on the slide thumbnail in the Slides tab</p>", "<p>Pressing the Esc key to exit the presentation</p>"],
                    options_hi: ["<p>माउस व्हील से स्क्रॉल करना</p>", "<p>कीबोर्ड दिशात्मक कुंजियों (keyboard directional keys) का उपयोग करना</p>",
                                "<p>स्लाइड्स टैब (Slides tab) में स्लाइड थंबनेल पर क्लिक करना</p>", "<p>प्रेजेंटेजेंशन से बाहर निकलने के लिए Esc कुंजी दबाना</p>"],
                    solution_en: "<p>17.(b)<strong> Using the keyboard directional keys.</strong> PowerPoint (PPT) is a presentation program that allows users to create, edit, and share multimedia presentations on their computer or mobile device.</p>",
                    solution_hi: "<p>17.(b) <strong>कीबोर्ड दिशात्मक कुंजियों का उपयोग करना। </strong>पावरपॉइंट (PPT) एक प्रेजेंटेशन प्रोग्राम है जो उपयोगकर्ताओं को अपने कंप्यूटर या मोबाइल डिवाइस पर मल्टीमीडिया प्रेजेंटेशन बनाने, संपादित करने (edit) और साझा करने (share) की अनुमति देता है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "18",
                    question_en: "<p>18. Which of the following Articles of the Indian Constitution are related to Fundamental&nbsp;Rights ?</p>",
                    question_hi: "<p>18. भारतीय संविधान के निम्नलिखित में से कौन-से अनुच्छेद मौलिक अधिकारों से संबंधित हैं?</p>",
                    options_en: ["<p>12-36</p>", "<p>32-51</p>", 
                                "<p>12-35</p>", "<p>14-32</p>"],
                    options_hi: ["<p>12-36</p>", "<p>32-51</p>",
                                "<p>12-35</p>", "<p>14-32</p>"],
                    solution_en: "<p>18.(c)<strong> 12-35. </strong>Fundamental Rights: Right to equality (Articles 14&ndash;18), Right to freedom (Articles 19&ndash;22), Right against exploitation (Articles 23&ndash;24), Right to freedom of religion (Articles 25&ndash;28), Cultural and educational rights (Articles 29&ndash;30), Right to constitutional remedies (Article 32). The Right to property was removed from the list of fundamental rights by the 44th Amendment Act, 1978 and it was made a legal right under Article 300-A in Part XII of the Constitution.</p>",
                    solution_hi: "<p>18.(c) <strong>12-35. </strong>मौलिक अधिकार: समानता का अधिकार (अनुच्छेद 14-18), स्वतंत्रता का अधिकार (अनुच्छेद 19-22), शोषण के विरुद्ध अधिकार (अनुच्छेद 23-24), धर्म की स्वतंत्रता का अधिकार (अनुच्छेद 25-28), सांस्कृतिक और शैक्षिक अधिकार (अनुच्छेद 29-30), संवैधानिक उपचारों का अधिकार (अनुच्छेद 32)। 44वें संशोधन अधिनियम, 1978 द्वारा संपत्ति के अधिकार को मौलिक अधिकारों की सूची से हटा दिया गया तथा इसे संविधान के भाग XII में अनुच्छेद 300-A के तहत कानूनी अधिकार बना दिया गया है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "18",
                    question_en: "<p>19. Aajeevika - National Rural Livelihoods Mission (NRLM) - was launched by the Ministry of Rural Development (MoRD), Government of India, in the year ________.</p>",
                    question_hi: "<p>19. आजीविका - राष्ट्रीय ग्रामीण आजीविका मिशन (NRLM) - ग्रामीण विकास मंत्रालय (MoRD), भारत सरकार द्वारा वर्ष ________ में शुरू किया गया था।</p>",
                    options_en: ["<p>2012</p>", "<p>2013</p>", 
                                "<p>2011</p>", "<p>2010</p>"],
                    options_hi: ["<p>2012</p>", "<p>2013</p>",
                                "<p>2011</p>", "<p>2010</p>"],
                    solution_en: "<p>19.(c) <strong>2011</strong>. Programmes operated by the Ministry of Rural Development: (MGNREGA - 2005) for providing wage employment. Pradhan Mantri Awaas Yojana - Grameen (PMAY-G - 2016) for providing housing to BPL households. Pradhan Mantri Gram Sadak Yojana (PMGSY - 2000) for construction of quality roads. National Social Assistance Programme (1995) for social pension. Integrated Watershed Management Programme (2009-10) for improving the productivity of the land.</p>",
                    solution_hi: "<p>19.(c) <strong>2011</strong>. ग्रामीण विकास मंत्रालय द्वारा संचालित कार्यक्रम: (मनरेगा - 2005) मजदूरी रोजगार प्रदान करने के लिए। प्रधानमंत्री आवास योजना - ग्रामीण (PMAY-G - 2016) BPL परिवारों को आवास प्रदान करने के लिए। प्रधानमंत्री ग्राम सड़क योजना (PMGSY - 2000) गुणवत्ता वाली सड़कों के निर्माण के लिए। सामाजिक पेंशन के लिए राष्ट्रीय सामाजिक सहायता कार्यक्रम (1995)। भूमि की उत्पादकता में सुधार के लिए एकीकृत वाटरशेड प्रबंधन कार्यक्रम (2009-10)।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "18",
                    question_en: "<p>20. Which party won the Jalandhar (a city in Punjab) Lok Sabha bypoll election held in May 2023 ?</p>",
                    question_hi: "<p>20. मई 2023 में हुए जालंधर (पंजाब का एक शहर) लोकसभा उपचुनाव में किस पार्टी ने जीत हासिल की?</p>",
                    options_en: ["<p>Bharatiya Janata Party</p>", "<p>Akali Dal</p>", 
                                "<p>Indian National Congress</p>", "<p>Aam Aadmi Party</p>"],
                    options_hi: ["<p>भारतीय जनता पार्टी</p>", "<p>अकाली दल</p>",
                                "<p>भारतीय राष्ट्रीय कांग्रेस</p>", "<p>आम आदमी पार्टी</p>"],
                    solution_en: "<p>20.(d) <strong>Aam Aadmi Party (AAP) :</strong> Formed on 26 November 2012, following the 2011 anti-corruption movement. It is a national party. A party is recognized as a national party if it secures at least 6% of the total votes in Lok Sabha or Assembly elections in four states and wins at least four seats in the Lok Sabha.</p>",
                    solution_hi: "<p>20.(d)<strong> आम आदमी पार्टी (AAP) :&nbsp;</strong>2011 के भ्रष्टाचार विरोधी आंदोलन के बाद 26 नवंबर 2012 को गठित हुआ। यह एक राष्ट्रीय पार्टी है। किसी पार्टी को राष्ट्रीय पार्टी के रूप में मान्यता तब मिलती है जब वह चार राज्यों में लोकसभा या विधानसभा चुनावों में कुल वोटों का कम से कम 6% वोट हासिल करती है और लोकसभा में कम से कम चार सीटें जीतती है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "18",
                    question_en: "<p>21. Fundamental duties were introduced in Part IVA of the Constitution by the:</p>",
                    question_hi: "<p>21. संविधान के भाग IVA में मौलिक कर्तव्यों को _________ के द्वारा पेश किया गया था।</p>",
                    options_en: ["<p>Forty-second Amendment Act, 1976</p>", "<p>Forty-first Amendment Act, 1976</p>", 
                                "<p>Forty-third Amendment Act, 1977</p>", "<p>Forty-fourth Amendment Act, 1978</p>"],
                    options_hi: ["<p>बयालीसवें संशोधन अधिनियम, 1976</p>", "<p>इकतालीसवें संशोधन अधिनियम, 1976</p>",
                                "<p>तैंतालीसवें संशोधन अधिनियम, 1977</p>", "<p>चौवालीसवें संशोधन अधिनियम, 1978</p>"],
                    solution_en: "<p>21.(a) <strong>Forty-second Amendment Act, 1976.</strong> Fundamental Duties (Part IVA, Article 51A) incorporated on the recommendations of the Swaran Singh Committee. Article 51A originally listed 10 duties. An additional duty was later added by the 86th Constitutional Amendment Act of 2002, which mandates that parents or guardians provide opportunities for education to their children or wards between the ages of six and fourteen years.</p>",
                    solution_hi: "<p>21.(a) <strong>बयालीसवें संशोधन अधिनियम, 1976. </strong>स्वर्ण सिंह समिति की सिफारिशों पर मौलिक कर्तव्य (भाग IVA, अनुच्छेद 51A) शामिल किए गए। अनुच्छेद 51A में मूल रूप से 10 कर्तव्य सूचीबद्ध थे। बाद में 2002 के 86वें संविधान संशोधन अधिनियम द्वारा एक अतिरिक्त कर्तव्य जोड़ा गया, जो यह अनिवार्य करता है कि माता-पिता या अभिभावक अपने बच्चों या छह से चौदह वर्ष की आयु के बच्चों को शिक्षा के अवसर प्रदान करें।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "18",
                    question_en: "<p>22. What was the name of the person who has been elected as the Chair of the 62nd session of the United Nations Commission for Social Development ?</p>",
                    question_hi: "<p>22. निम्नलिखित में से किसे संयुक्त राष्ट्र सामाजिक विकास आयोग के 62वें सत्र के अध्यक्ष के रूप में चुना गया?</p>",
                    options_en: ["<p>BM Vinod Kumar</p>", "<p>Satinder Kumar Lambah</p>", 
                                "<p>Ruchira Kamboj</p>", "<p>Nirupama Menon Rao</p>"],
                    options_hi: ["<p>बी. एम. विनोद कुमार</p>", "<p>सतिंदर कुमार लांबा</p>",
                                "<p>रुचिरा कंबोज (Ruchira Kamboj)</p>", "<p>निरुपमा मेनन राव (Nirupama Menon Rao)</p>"],
                    solution_en: "<p>22.(c)<strong> Ruchira Kamboj. </strong>United Nations Commission for Social Development: Headquarters in New York (USA), Its primary purpose is to advance social development and formulate policies and recommendations to address global social issues.</p>",
                    solution_hi: "<p>22.(c) <strong>रुचिरा कंबोज। </strong>संयुक्त राष्ट्र सामाजिक विकास आयोग: इसका मुख्यालय न्यूयॉर्क (USA) में है। इसका प्राथमिक उद्देश्य सामाजिक विकास को आगे बढ़ाना और वैश्विक सामाजिक मुद्दों के समाधान के लिए नीतियां और सिफारिशें तैयार करना है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "23",
                    section: "18",
                    question_en: "<p>23. What kind of a satellite is CMS-01,that was launched by ISRO in December 2020?</p>",
                    question_hi: "<p>23. दिसंबर 2020 में इसरो (ISRO) द्वारा लॉन्च किया गया CMS-01 किस तरह का उपग्रह है?</p>",
                    options_en: ["<p>Tsunami observation</p>", "<p>Resource management</p>", 
                                "<p>Navigation</p>", "<p>Communication</p>"],
                    options_hi: ["<p>सुनामी निगरानी</p>", "<p>संसाधन प्रबंधन</p>",
                                "<p>नौपरिवहन</p>", "<p>संचार</p>"],
                    solution_en: "<p>23.(d) <strong>Communication</strong>. CMS-01 launched by PSLV-C50 from the Satish Dhawan Space Centre (SDSC) SHAR, Sriharikota (Andhra Pradesh). SHAR the lead centres of Indian Space Research Organisation (ISRO established- 1969, headquarter - Bengaluru).</p>",
                    solution_hi: "<p>23.(d) <strong>संचार</strong>। CMS-01 को PSLV-C50 द्वारा सतीश धवन अंतरिक्ष केंद्र (SDSC) SHAR, श्रीहरिकोटा (आंध्र प्रदेश) से प्रक्षेपित किया गया। SHAR , भारतीय अंतरिक्ष अनुसंधान संगठन (ISRO की स्थापना- 1969, मुख्यालय - बेंगलुरु) का प्रमुख केंद्र है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "24",
                    section: "18",
                    question_en: "<p>24. Shattantri Veena, meaning a Veena with hundred strings, is the ancient Sanskrit name of which instrument ?</p>",
                    question_hi: "<p>24. शतांत्री वीणा (Shattantri Veena), जिसका अर्थ है सौ तारों वाली वीणा, किस वाद्य यंत्र का प्राचीन संस्कृत नाम है?</p>",
                    options_en: ["<p>Santoor</p>", "<p>Sarod</p>", 
                                "<p>Sarangi</p>", "<p>Tambura/Tanpura</p>"],
                    options_hi: ["<p>संतूर</p>", "<p>सरोद</p>",
                                "<p>सारंगी</p>", "<p>तंबूरा/तानपुरा</p>"],
                    solution_en: "<p>24.(a) <strong>Santoor</strong>. It gets its name from Shat Trantri Veena. &ldquo;Shat&rdquo; means hundred and &ldquo;Tantra&rdquo; means &ldquo;Tar&rdquo;. So, this is a one hundred string musical instrument and the name came from Shat-tantri to Shat-tar to San-tar to Santoor. Santoor maestro: Pandit Shiv Kumar Sharma, Varsha Agarwal, Rahul Sharma.</p>",
                    solution_hi: "<p>24.(a) <strong>संतूर</strong>। इसका नाम शतांत्री वीणा से लिया गया है। \"शत\" का अर्थ है &lsquo;सौ&rsquo; और \"तंत्र\" का अर्थ है &lsquo;तार&rsquo;। इसलिए , यह एक सौ तार वाला संगीत वाद्ययंत्र है और इसका नाम शततंत्री से शत-तार, शत-तार से सन-तार फिर सन-तार से संतूर पड़ा। संतूर वादक: पंडित शिव कुमार शर्मा, वर्षा अग्रवाल, राहुल शर्मा।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "25",
                    section: "misc",
                    question_en: "<p>25. According to rules laid down by the International Football Association Board (IFAB),&nbsp;what can be the maximum length of a goal line in football ?</p>",
                    question_hi: "<p>25. अंतर्राष्ट्रीय फुटबॉल एसोसिएशन बोर्ड (International Football Association Board - IFAB) द्वारा&nbsp;निर्धारित किए गए नियमों के अनुसार, फुटबॉल में लक्ष्य रेखा (goal line) की अधिकतम लंबाई कितनी हो सकती है?</p>",
                    options_en: ["<p>40 m</p>", "<p>100 m</p>", 
                                "<p>90 m</p>", "<p>50 m</p>"],
                    options_hi: ["<p>40 m</p>", "<p>100 m</p>",
                                "<p>90 m</p>", "<p>50 m</p>"],
                    solution_en: "<p>25.(c) <strong>90 m.</strong> International Football Association Board established in 1886. FIFA recommendations for field dimensions in professional football are 105 metres in length and 68 metres in width. Number of Players - 11 players.</p>",
                    solution_hi: "<p>25.(c)<strong> 90 m. </strong>अंतर्राष्ट्रीय फुटबॉल एसोसिएशन बोर्ड (IFAB) की स्थापना 1886 में हुई थी। प्रोफेशनल फुटबॉल में फील्ड के आयामों के लिए FIFA की सिफारिशें, लंबाई में 105 मीटर और चौड़ाई में 68 मीटर हैं।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>